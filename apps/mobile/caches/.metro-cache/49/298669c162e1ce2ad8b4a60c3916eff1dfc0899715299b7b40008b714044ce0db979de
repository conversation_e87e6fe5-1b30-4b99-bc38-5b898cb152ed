{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Recycle = exports.default = (0, _createLucideIcon.default)(\"Recycle\", [[\"path\", {\n    d: \"M7 19H4.815a1.83 1.83 0 0 1-1.57-.881 1.785 1.785 0 0 1-.004-1.784L7.196 9.5\",\n    key: \"x6z5xu\"\n  }], [\"path\", {\n    d: \"M11 19h8.203a1.83 1.83 0 0 0 1.556-.89 1.784 1.784 0 0 0 0-1.775l-1.226-2.12\",\n    key: \"1x4zh5\"\n  }], [\"path\", {\n    d: \"m14 16-3 3 3 3\",\n    key: \"f6jyew\"\n  }], [\"path\", {\n    d: \"M8.293 13.596 7.196 9.5 3.1 10.598\",\n    key: \"wf1obh\"\n  }], [\"path\", {\n    d: \"m9.344 5.811 1.093-1.892A1.83 1.83 0 0 1 11.985 3a1.784 1.784 0 0 1 1.546.888l3.943 6.843\",\n    key: \"9tzpgr\"\n  }], [\"path\", {\n    d: \"m13.378 9.633 4.096 1.098 1.097-4.096\",\n    key: \"1oe83g\"\n  }]]);\n});", "lineCount": 34, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Recycle"], [15, 15, 10, 13], [15, 18, 10, 13, "exports"], [15, 25, 10, 13], [15, 26, 10, 13, "default"], [15, 33, 10, 13], [15, 36, 10, 16], [15, 40, 10, 16, "createLucideIcon"], [15, 65, 10, 32], [15, 67, 10, 33], [15, 76, 10, 42], [15, 78, 10, 44], [15, 79, 11, 2], [15, 80, 12, 4], [15, 86, 12, 10], [15, 88, 13, 4], [16, 4, 14, 6, "d"], [16, 5, 14, 7], [16, 7, 14, 9], [16, 85, 14, 87], [17, 4, 15, 6, "key"], [17, 7, 15, 9], [17, 9, 15, 11], [18, 2, 16, 4], [18, 3, 16, 5], [18, 4, 17, 3], [18, 6, 18, 2], [18, 7, 19, 4], [18, 13, 19, 10], [18, 15, 20, 4], [19, 4, 21, 6, "d"], [19, 5, 21, 7], [19, 7, 21, 9], [19, 85, 21, 87], [20, 4, 22, 6, "key"], [20, 7, 22, 9], [20, 9, 22, 11], [21, 2, 23, 4], [21, 3, 23, 5], [21, 4, 24, 3], [21, 6, 25, 2], [21, 7, 25, 3], [21, 13, 25, 9], [21, 15, 25, 11], [22, 4, 25, 13, "d"], [22, 5, 25, 14], [22, 7, 25, 16], [22, 23, 25, 32], [23, 4, 25, 34, "key"], [23, 7, 25, 37], [23, 9, 25, 39], [24, 2, 25, 48], [24, 3, 25, 49], [24, 4, 25, 50], [24, 6, 26, 2], [24, 7, 26, 3], [24, 13, 26, 9], [24, 15, 26, 11], [25, 4, 26, 13, "d"], [25, 5, 26, 14], [25, 7, 26, 16], [25, 43, 26, 52], [26, 4, 26, 54, "key"], [26, 7, 26, 57], [26, 9, 26, 59], [27, 2, 26, 68], [27, 3, 26, 69], [27, 4, 26, 70], [27, 6, 27, 2], [27, 7, 28, 4], [27, 13, 28, 10], [27, 15, 29, 4], [28, 4, 30, 6, "d"], [28, 5, 30, 7], [28, 7, 30, 9], [28, 98, 30, 100], [29, 4, 31, 6, "key"], [29, 7, 31, 9], [29, 9, 31, 11], [30, 2, 32, 4], [30, 3, 32, 5], [30, 4, 33, 3], [30, 6, 34, 2], [30, 7, 34, 3], [30, 13, 34, 9], [30, 15, 34, 11], [31, 4, 34, 13, "d"], [31, 5, 34, 14], [31, 7, 34, 16], [31, 46, 34, 55], [32, 4, 34, 57, "key"], [32, 7, 34, 60], [32, 9, 34, 62], [33, 2, 34, 71], [33, 3, 34, 72], [33, 4, 34, 73], [33, 5, 35, 1], [33, 6, 35, 2], [34, 0, 35, 3], [34, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}