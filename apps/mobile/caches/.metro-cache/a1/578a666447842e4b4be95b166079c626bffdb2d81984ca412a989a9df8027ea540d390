{"dependencies": [{"name": "../animation/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 58, "index": 73}}], "key": "6GAoNhzQ7+ZSX+WBszeRuj9gSFc=", "exportNames": ["*"]}}, {"name": "../isSharedValue.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 74}, "end": {"line": 4, "column": 52, "index": 126}}], "key": "5U54DlUmC1sHQzl1QFdsXXrazfk=", "exportNames": ["*"]}}, {"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 127}, "end": {"line": 5, "column": 57, "index": 184}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "../WorkletEventHandler.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 185}, "end": {"line": 6, "column": 64, "index": 249}}], "key": "e5fG6a6nTf5/kChbWzO7k8c/97s=", "exportNames": ["*"]}}, {"name": "./InlinePropManager.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 250}, "end": {"line": 7, "column": 73, "index": 323}}], "key": "YHZ4YfR3/SR4hav+p3EqwWcxN0E=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 324}, "end": {"line": 8, "column": 47, "index": 371}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PropsFilter = void 0;\n  var _index = require(_dependencyMap[0], \"../animation/index.js\");\n  var _isSharedValue = require(_dependencyMap[1], \"../isSharedValue.js\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../PlatformChecker.js\");\n  var _WorkletEventHandler = require(_dependencyMap[3], \"../WorkletEventHandler.js\");\n  var _InlinePropManager = require(_dependencyMap[4], \"./InlinePropManager.js\");\n  var _utils = require(_dependencyMap[5], \"./utils.js\");\n  function dummyListener() {\n    // empty listener we use to assign to listener properties for which animated\n    // event is used.\n  }\n  class PropsFilter {\n    _initialPropsMap = new Map();\n    filterNonAnimatedProps(component) {\n      const inputProps = component.props;\n      const props = {};\n      for (const key in inputProps) {\n        const value = inputProps[key];\n        if (key === 'style') {\n          const styleProp = inputProps.style;\n          const styles = (0, _utils.flattenArray)(styleProp ?? []);\n          const processedStyle = styles.map(style => {\n            if (style && style.viewDescriptors) {\n              const handle = style;\n              if (component._isFirstRender) {\n                this._initialPropsMap.set(handle, {\n                  ...handle.initial.value,\n                  ...(0, _index.initialUpdaterRun)(handle.initial.updater)\n                });\n              }\n              return this._initialPropsMap.get(handle) ?? {};\n            } else if ((0, _InlinePropManager.hasInlineStyles)(style)) {\n              return (0, _InlinePropManager.getInlineStyle)(style, component._isFirstRender);\n            } else {\n              return style;\n            }\n          });\n          // keep styles as they were passed by the user\n          // it will help other libs to interpret styles correctly\n          props[key] = processedStyle;\n        } else if (key === 'animatedProps') {\n          const animatedProp = inputProps.animatedProps;\n          if (animatedProp.initial !== undefined) {\n            Object.keys(animatedProp.initial.value).forEach(initialValueKey => {\n              props[initialValueKey] = animatedProp.initial?.value[initialValueKey];\n            });\n          }\n        } else if ((0, _utils.has)('workletEventHandler', value) && value.workletEventHandler instanceof _WorkletEventHandler.WorkletEventHandler) {\n          if (value.workletEventHandler.eventNames.length > 0) {\n            value.workletEventHandler.eventNames.forEach(eventName => {\n              props[eventName] = (0, _utils.has)('listeners', value.workletEventHandler) ? value.workletEventHandler.listeners[eventName] : dummyListener;\n            });\n          } else {\n            props[key] = dummyListener;\n          }\n        } else if ((0, _isSharedValue.isSharedValue)(value)) {\n          if (component._isFirstRender) {\n            props[key] = value.value;\n          }\n        } else if (key !== 'onGestureHandlerStateChange' || !(0, _PlatformChecker.isChromeDebugger)()) {\n          props[key] = value;\n        }\n      }\n      return props;\n    }\n  }\n  exports.PropsFilter = PropsFilter;\n});", "lineCount": 74, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 21, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_index"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_isSharedValue"], [9, 20, 4, 0], [9, 23, 4, 0, "require"], [9, 30, 4, 0], [9, 31, 4, 0, "_dependencyMap"], [9, 45, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_PlatformChecker"], [10, 22, 5, 0], [10, 25, 5, 0, "require"], [10, 32, 5, 0], [10, 33, 5, 0, "_dependencyMap"], [10, 47, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_WorkletEventHandler"], [11, 26, 6, 0], [11, 29, 6, 0, "require"], [11, 36, 6, 0], [11, 37, 6, 0, "_dependencyMap"], [11, 51, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_InlinePropManager"], [12, 24, 7, 0], [12, 27, 7, 0, "require"], [12, 34, 7, 0], [12, 35, 7, 0, "_dependencyMap"], [12, 49, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_utils"], [13, 12, 8, 0], [13, 15, 8, 0, "require"], [13, 22, 8, 0], [13, 23, 8, 0, "_dependencyMap"], [13, 37, 8, 0], [14, 2, 9, 0], [14, 11, 9, 9, "dummyListener"], [14, 24, 9, 22, "dummyListener"], [14, 25, 9, 22], [14, 27, 9, 25], [15, 4, 10, 2], [16, 4, 11, 2], [17, 2, 11, 2], [18, 2, 13, 7], [18, 8, 13, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 19, 13, 24], [18, 20, 13, 25], [19, 4, 14, 2, "_initialPropsMap"], [19, 20, 14, 18], [19, 23, 14, 21], [19, 27, 14, 25, "Map"], [19, 30, 14, 28], [19, 31, 14, 29], [19, 32, 14, 30], [20, 4, 15, 2, "filterNonAnimatedProps"], [20, 26, 15, 24, "filterNonAnimatedProps"], [20, 27, 15, 25, "component"], [20, 36, 15, 34], [20, 38, 15, 36], [21, 6, 16, 4], [21, 12, 16, 10, "inputProps"], [21, 22, 16, 20], [21, 25, 16, 23, "component"], [21, 34, 16, 32], [21, 35, 16, 33, "props"], [21, 40, 16, 38], [22, 6, 17, 4], [22, 12, 17, 10, "props"], [22, 17, 17, 15], [22, 20, 17, 18], [22, 21, 17, 19], [22, 22, 17, 20], [23, 6, 18, 4], [23, 11, 18, 9], [23, 17, 18, 15, "key"], [23, 20, 18, 18], [23, 24, 18, 22, "inputProps"], [23, 34, 18, 32], [23, 36, 18, 34], [24, 8, 19, 6], [24, 14, 19, 12, "value"], [24, 19, 19, 17], [24, 22, 19, 20, "inputProps"], [24, 32, 19, 30], [24, 33, 19, 31, "key"], [24, 36, 19, 34], [24, 37, 19, 35], [25, 8, 20, 6], [25, 12, 20, 10, "key"], [25, 15, 20, 13], [25, 20, 20, 18], [25, 27, 20, 25], [25, 29, 20, 27], [26, 10, 21, 8], [26, 16, 21, 14, "styleProp"], [26, 25, 21, 23], [26, 28, 21, 26, "inputProps"], [26, 38, 21, 36], [26, 39, 21, 37, "style"], [26, 44, 21, 42], [27, 10, 22, 8], [27, 16, 22, 14, "styles"], [27, 22, 22, 20], [27, 25, 22, 23], [27, 29, 22, 23, "flattenArray"], [27, 48, 22, 35], [27, 50, 22, 36, "styleProp"], [27, 59, 22, 45], [27, 63, 22, 49], [27, 65, 22, 51], [27, 66, 22, 52], [28, 10, 23, 8], [28, 16, 23, 14, "processedStyle"], [28, 30, 23, 28], [28, 33, 23, 31, "styles"], [28, 39, 23, 37], [28, 40, 23, 38, "map"], [28, 43, 23, 41], [28, 44, 23, 42, "style"], [28, 49, 23, 47], [28, 53, 23, 51], [29, 12, 24, 10], [29, 16, 24, 14, "style"], [29, 21, 24, 19], [29, 25, 24, 23, "style"], [29, 30, 24, 28], [29, 31, 24, 29, "viewDescriptors"], [29, 46, 24, 44], [29, 48, 24, 46], [30, 14, 25, 12], [30, 20, 25, 18, "handle"], [30, 26, 25, 24], [30, 29, 25, 27, "style"], [30, 34, 25, 32], [31, 14, 26, 12], [31, 18, 26, 16, "component"], [31, 27, 26, 25], [31, 28, 26, 26, "_isFirstRender"], [31, 42, 26, 40], [31, 44, 26, 42], [32, 16, 27, 14], [32, 20, 27, 18], [32, 21, 27, 19, "_initialPropsMap"], [32, 37, 27, 35], [32, 38, 27, 36, "set"], [32, 41, 27, 39], [32, 42, 27, 40, "handle"], [32, 48, 27, 46], [32, 50, 27, 48], [33, 18, 28, 16], [33, 21, 28, 19, "handle"], [33, 27, 28, 25], [33, 28, 28, 26, "initial"], [33, 35, 28, 33], [33, 36, 28, 34, "value"], [33, 41, 28, 39], [34, 18, 29, 16], [34, 21, 29, 19], [34, 25, 29, 19, "initialUpdaterRun"], [34, 49, 29, 36], [34, 51, 29, 37, "handle"], [34, 57, 29, 43], [34, 58, 29, 44, "initial"], [34, 65, 29, 51], [34, 66, 29, 52, "updater"], [34, 73, 29, 59], [35, 16, 30, 14], [35, 17, 30, 15], [35, 18, 30, 16], [36, 14, 31, 12], [37, 14, 32, 12], [37, 21, 32, 19], [37, 25, 32, 23], [37, 26, 32, 24, "_initialPropsMap"], [37, 42, 32, 40], [37, 43, 32, 41, "get"], [37, 46, 32, 44], [37, 47, 32, 45, "handle"], [37, 53, 32, 51], [37, 54, 32, 52], [37, 58, 32, 56], [37, 59, 32, 57], [37, 60, 32, 58], [38, 12, 33, 10], [38, 13, 33, 11], [38, 19, 33, 17], [38, 23, 33, 21], [38, 27, 33, 21, "hasInlineStyles"], [38, 61, 33, 36], [38, 63, 33, 37, "style"], [38, 68, 33, 42], [38, 69, 33, 43], [38, 71, 33, 45], [39, 14, 34, 12], [39, 21, 34, 19], [39, 25, 34, 19, "getInlineStyle"], [39, 58, 34, 33], [39, 60, 34, 34, "style"], [39, 65, 34, 39], [39, 67, 34, 41, "component"], [39, 76, 34, 50], [39, 77, 34, 51, "_isFirstRender"], [39, 91, 34, 65], [39, 92, 34, 66], [40, 12, 35, 10], [40, 13, 35, 11], [40, 19, 35, 17], [41, 14, 36, 12], [41, 21, 36, 19, "style"], [41, 26, 36, 24], [42, 12, 37, 10], [43, 10, 38, 8], [43, 11, 38, 9], [43, 12, 38, 10], [44, 10, 39, 8], [45, 10, 40, 8], [46, 10, 41, 8, "props"], [46, 15, 41, 13], [46, 16, 41, 14, "key"], [46, 19, 41, 17], [46, 20, 41, 18], [46, 23, 41, 21, "processedStyle"], [46, 37, 41, 35], [47, 8, 42, 6], [47, 9, 42, 7], [47, 15, 42, 13], [47, 19, 42, 17, "key"], [47, 22, 42, 20], [47, 27, 42, 25], [47, 42, 42, 40], [47, 44, 42, 42], [48, 10, 43, 8], [48, 16, 43, 14, "animatedProp"], [48, 28, 43, 26], [48, 31, 43, 29, "inputProps"], [48, 41, 43, 39], [48, 42, 43, 40, "animatedProps"], [48, 55, 43, 53], [49, 10, 44, 8], [49, 14, 44, 12, "animatedProp"], [49, 26, 44, 24], [49, 27, 44, 25, "initial"], [49, 34, 44, 32], [49, 39, 44, 37, "undefined"], [49, 48, 44, 46], [49, 50, 44, 48], [50, 12, 45, 10, "Object"], [50, 18, 45, 16], [50, 19, 45, 17, "keys"], [50, 23, 45, 21], [50, 24, 45, 22, "animatedProp"], [50, 36, 45, 34], [50, 37, 45, 35, "initial"], [50, 44, 45, 42], [50, 45, 45, 43, "value"], [50, 50, 45, 48], [50, 51, 45, 49], [50, 52, 45, 50, "for<PERSON>ach"], [50, 59, 45, 57], [50, 60, 45, 58, "initialValueKey"], [50, 75, 45, 73], [50, 79, 45, 77], [51, 14, 46, 12, "props"], [51, 19, 46, 17], [51, 20, 46, 18, "initialValueKey"], [51, 35, 46, 33], [51, 36, 46, 34], [51, 39, 46, 37, "animatedProp"], [51, 51, 46, 49], [51, 52, 46, 50, "initial"], [51, 59, 46, 57], [51, 61, 46, 59, "value"], [51, 66, 46, 64], [51, 67, 46, 65, "initialValueKey"], [51, 82, 46, 80], [51, 83, 46, 81], [52, 12, 47, 10], [52, 13, 47, 11], [52, 14, 47, 12], [53, 10, 48, 8], [54, 8, 49, 6], [54, 9, 49, 7], [54, 15, 49, 13], [54, 19, 49, 17], [54, 23, 49, 17, "has"], [54, 33, 49, 20], [54, 35, 49, 21], [54, 56, 49, 42], [54, 58, 49, 44, "value"], [54, 63, 49, 49], [54, 64, 49, 50], [54, 68, 49, 54, "value"], [54, 73, 49, 59], [54, 74, 49, 60, "workletEventHandler"], [54, 93, 49, 79], [54, 105, 49, 91, "WorkletEventHandler"], [54, 145, 49, 110], [54, 147, 49, 112], [55, 10, 50, 8], [55, 14, 50, 12, "value"], [55, 19, 50, 17], [55, 20, 50, 18, "workletEventHandler"], [55, 39, 50, 37], [55, 40, 50, 38, "eventNames"], [55, 50, 50, 48], [55, 51, 50, 49, "length"], [55, 57, 50, 55], [55, 60, 50, 58], [55, 61, 50, 59], [55, 63, 50, 61], [56, 12, 51, 10, "value"], [56, 17, 51, 15], [56, 18, 51, 16, "workletEventHandler"], [56, 37, 51, 35], [56, 38, 51, 36, "eventNames"], [56, 48, 51, 46], [56, 49, 51, 47, "for<PERSON>ach"], [56, 56, 51, 54], [56, 57, 51, 55, "eventName"], [56, 66, 51, 64], [56, 70, 51, 68], [57, 14, 52, 12, "props"], [57, 19, 52, 17], [57, 20, 52, 18, "eventName"], [57, 29, 52, 27], [57, 30, 52, 28], [57, 33, 52, 31], [57, 37, 52, 31, "has"], [57, 47, 52, 34], [57, 49, 52, 35], [57, 60, 52, 46], [57, 62, 52, 48, "value"], [57, 67, 52, 53], [57, 68, 52, 54, "workletEventHandler"], [57, 87, 52, 73], [57, 88, 52, 74], [57, 91, 52, 77, "value"], [57, 96, 52, 82], [57, 97, 52, 83, "workletEventHandler"], [57, 116, 52, 102], [57, 117, 52, 103, "listeners"], [57, 126, 52, 112], [57, 127, 52, 113, "eventName"], [57, 136, 52, 122], [57, 137, 52, 123], [57, 140, 52, 126, "dummyListener"], [57, 153, 52, 139], [58, 12, 53, 10], [58, 13, 53, 11], [58, 14, 53, 12], [59, 10, 54, 8], [59, 11, 54, 9], [59, 17, 54, 15], [60, 12, 55, 10, "props"], [60, 17, 55, 15], [60, 18, 55, 16, "key"], [60, 21, 55, 19], [60, 22, 55, 20], [60, 25, 55, 23, "dummyListener"], [60, 38, 55, 36], [61, 10, 56, 8], [62, 8, 57, 6], [62, 9, 57, 7], [62, 15, 57, 13], [62, 19, 57, 17], [62, 23, 57, 17, "isSharedValue"], [62, 51, 57, 30], [62, 53, 57, 31, "value"], [62, 58, 57, 36], [62, 59, 57, 37], [62, 61, 57, 39], [63, 10, 58, 8], [63, 14, 58, 12, "component"], [63, 23, 58, 21], [63, 24, 58, 22, "_isFirstRender"], [63, 38, 58, 36], [63, 40, 58, 38], [64, 12, 59, 10, "props"], [64, 17, 59, 15], [64, 18, 59, 16, "key"], [64, 21, 59, 19], [64, 22, 59, 20], [64, 25, 59, 23, "value"], [64, 30, 59, 28], [64, 31, 59, 29, "value"], [64, 36, 59, 34], [65, 10, 60, 8], [66, 8, 61, 6], [66, 9, 61, 7], [66, 15, 61, 13], [66, 19, 61, 17, "key"], [66, 22, 61, 20], [66, 27, 61, 25], [66, 56, 61, 54], [66, 60, 61, 58], [66, 61, 61, 59], [66, 65, 61, 59, "isChromeDebugger"], [66, 98, 61, 75], [66, 100, 61, 76], [66, 101, 61, 77], [66, 103, 61, 79], [67, 10, 62, 8, "props"], [67, 15, 62, 13], [67, 16, 62, 14, "key"], [67, 19, 62, 17], [67, 20, 62, 18], [67, 23, 62, 21, "value"], [67, 28, 62, 26], [68, 8, 63, 6], [69, 6, 64, 4], [70, 6, 65, 4], [70, 13, 65, 11, "props"], [70, 18, 65, 16], [71, 4, 66, 2], [72, 2, 67, 0], [73, 2, 67, 1, "exports"], [73, 9, 67, 1], [73, 10, 67, 1, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [73, 21, 67, 1], [73, 24, 67, 1, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [73, 35, 67, 1], [74, 0, 67, 1], [74, 3]], "functionMap": {"names": ["<global>", "dummyListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterNonAnimatedProps", "styles.map$argument_0", "Object.keys.forEach$argument_0", "value.workletEventHandler.eventNames.forEach$argument_0"], "mappings": "AAA;ACQ;CDG;OEC;ECE;0CCQ;SDe;0DEO;WFE;uDGI;WHE;GDa;CFC"}}, "type": "js/module"}]}