{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 52, "index": 99}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../PlatformPressable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 5, "column": 60, "index": 160}}], "key": "auJZ4k92W56l1sd57k0rcJrkXw0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 161}, "end": {"line": 6, "column": 48, "index": 209}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderButton = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _PlatformPressable = require(_dependencyMap[2], \"../PlatformPressable.js\");\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function HeaderButtonInternal(_ref, ref) {\n    var disabled = _ref.disabled,\n      onPress = _ref.onPress,\n      pressColor = _ref.pressColor,\n      pressOpacity = _ref.pressOpacity,\n      accessibilityLabel = _ref.accessibilityLabel,\n      testID = _ref.testID,\n      style = _ref.style,\n      href = _ref.href,\n      children = _ref.children;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_PlatformPressable.PlatformPressable, {\n      ref: ref,\n      disabled: disabled,\n      href: href,\n      \"aria-label\": accessibilityLabel,\n      testID: testID,\n      onPress: onPress,\n      pressColor: pressColor,\n      pressOpacity: pressOpacity,\n      android_ripple: androidRipple,\n      style: [styles.container, disabled && styles.disabled, style],\n      hitSlop: _reactNative.Platform.select({\n        ios: undefined,\n        default: {\n          top: 16,\n          right: 16,\n          bottom: 16,\n          left: 16\n        }\n      }),\n      children: children\n    });\n  }\n  var HeaderButton = exports.HeaderButton = /*#__PURE__*/React.forwardRef(HeaderButtonInternal);\n  HeaderButton.displayName = 'HeaderButton';\n  var androidRipple = {\n    borderless: true,\n    foreground: _reactNative.Platform.OS === 'android' && _reactNative.Platform.Version >= 23,\n    radius: 20\n  };\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      paddingHorizontal: 8,\n      // Roundness for iPad hover effect\n      borderRadius: 10\n    },\n    disabled: {\n      opacity: 0.5\n    }\n  });\n});", "lineCount": 65, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 22, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_reactNative"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_PlatformPressable"], [10, 24, 5, 0], [10, 27, 5, 0, "require"], [10, 34, 5, 0], [10, 35, 5, 0, "_dependencyMap"], [10, 49, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_jsxRuntime"], [11, 17, 6, 0], [11, 20, 6, 0, "require"], [11, 27, 6, 0], [11, 28, 6, 0, "_dependencyMap"], [11, 42, 6, 0], [12, 2, 6, 48], [12, 11, 6, 48, "_interopRequireWildcard"], [12, 35, 6, 48, "e"], [12, 36, 6, 48], [12, 38, 6, 48, "t"], [12, 39, 6, 48], [12, 68, 6, 48, "WeakMap"], [12, 75, 6, 48], [12, 81, 6, 48, "r"], [12, 82, 6, 48], [12, 89, 6, 48, "WeakMap"], [12, 96, 6, 48], [12, 100, 6, 48, "n"], [12, 101, 6, 48], [12, 108, 6, 48, "WeakMap"], [12, 115, 6, 48], [12, 127, 6, 48, "_interopRequireWildcard"], [12, 150, 6, 48], [12, 162, 6, 48, "_interopRequireWildcard"], [12, 163, 6, 48, "e"], [12, 164, 6, 48], [12, 166, 6, 48, "t"], [12, 167, 6, 48], [12, 176, 6, 48, "t"], [12, 177, 6, 48], [12, 181, 6, 48, "e"], [12, 182, 6, 48], [12, 186, 6, 48, "e"], [12, 187, 6, 48], [12, 188, 6, 48, "__esModule"], [12, 198, 6, 48], [12, 207, 6, 48, "e"], [12, 208, 6, 48], [12, 214, 6, 48, "o"], [12, 215, 6, 48], [12, 217, 6, 48, "i"], [12, 218, 6, 48], [12, 220, 6, 48, "f"], [12, 221, 6, 48], [12, 226, 6, 48, "__proto__"], [12, 235, 6, 48], [12, 243, 6, 48, "default"], [12, 250, 6, 48], [12, 252, 6, 48, "e"], [12, 253, 6, 48], [12, 270, 6, 48, "e"], [12, 271, 6, 48], [12, 294, 6, 48, "e"], [12, 295, 6, 48], [12, 320, 6, 48, "e"], [12, 321, 6, 48], [12, 330, 6, 48, "f"], [12, 331, 6, 48], [12, 337, 6, 48, "o"], [12, 338, 6, 48], [12, 341, 6, 48, "t"], [12, 342, 6, 48], [12, 345, 6, 48, "n"], [12, 346, 6, 48], [12, 349, 6, 48, "r"], [12, 350, 6, 48], [12, 358, 6, 48, "o"], [12, 359, 6, 48], [12, 360, 6, 48, "has"], [12, 363, 6, 48], [12, 364, 6, 48, "e"], [12, 365, 6, 48], [12, 375, 6, 48, "o"], [12, 376, 6, 48], [12, 377, 6, 48, "get"], [12, 380, 6, 48], [12, 381, 6, 48, "e"], [12, 382, 6, 48], [12, 385, 6, 48, "o"], [12, 386, 6, 48], [12, 387, 6, 48, "set"], [12, 390, 6, 48], [12, 391, 6, 48, "e"], [12, 392, 6, 48], [12, 394, 6, 48, "f"], [12, 395, 6, 48], [12, 409, 6, 48, "_t"], [12, 411, 6, 48], [12, 415, 6, 48, "e"], [12, 416, 6, 48], [12, 432, 6, 48, "_t"], [12, 434, 6, 48], [12, 441, 6, 48, "hasOwnProperty"], [12, 455, 6, 48], [12, 456, 6, 48, "call"], [12, 460, 6, 48], [12, 461, 6, 48, "e"], [12, 462, 6, 48], [12, 464, 6, 48, "_t"], [12, 466, 6, 48], [12, 473, 6, 48, "i"], [12, 474, 6, 48], [12, 478, 6, 48, "o"], [12, 479, 6, 48], [12, 482, 6, 48, "Object"], [12, 488, 6, 48], [12, 489, 6, 48, "defineProperty"], [12, 503, 6, 48], [12, 508, 6, 48, "Object"], [12, 514, 6, 48], [12, 515, 6, 48, "getOwnPropertyDescriptor"], [12, 539, 6, 48], [12, 540, 6, 48, "e"], [12, 541, 6, 48], [12, 543, 6, 48, "_t"], [12, 545, 6, 48], [12, 552, 6, 48, "i"], [12, 553, 6, 48], [12, 554, 6, 48, "get"], [12, 557, 6, 48], [12, 561, 6, 48, "i"], [12, 562, 6, 48], [12, 563, 6, 48, "set"], [12, 566, 6, 48], [12, 570, 6, 48, "o"], [12, 571, 6, 48], [12, 572, 6, 48, "f"], [12, 573, 6, 48], [12, 575, 6, 48, "_t"], [12, 577, 6, 48], [12, 579, 6, 48, "i"], [12, 580, 6, 48], [12, 584, 6, 48, "f"], [12, 585, 6, 48], [12, 586, 6, 48, "_t"], [12, 588, 6, 48], [12, 592, 6, 48, "e"], [12, 593, 6, 48], [12, 594, 6, 48, "_t"], [12, 596, 6, 48], [12, 607, 6, 48, "f"], [12, 608, 6, 48], [12, 613, 6, 48, "e"], [12, 614, 6, 48], [12, 616, 6, 48, "t"], [12, 617, 6, 48], [13, 2, 7, 0], [13, 11, 7, 9, "HeaderButtonInternal"], [13, 31, 7, 29, "HeaderButtonInternal"], [13, 32, 7, 29, "_ref"], [13, 36, 7, 29], [13, 38, 17, 3, "ref"], [13, 41, 17, 6], [13, 43, 17, 8], [14, 4, 17, 8], [14, 8, 8, 2, "disabled"], [14, 16, 8, 10], [14, 19, 8, 10, "_ref"], [14, 23, 8, 10], [14, 24, 8, 2, "disabled"], [14, 32, 8, 10], [15, 6, 9, 2, "onPress"], [15, 13, 9, 9], [15, 16, 9, 9, "_ref"], [15, 20, 9, 9], [15, 21, 9, 2, "onPress"], [15, 28, 9, 9], [16, 6, 10, 2, "pressColor"], [16, 16, 10, 12], [16, 19, 10, 12, "_ref"], [16, 23, 10, 12], [16, 24, 10, 2, "pressColor"], [16, 34, 10, 12], [17, 6, 11, 2, "pressOpacity"], [17, 18, 11, 14], [17, 21, 11, 14, "_ref"], [17, 25, 11, 14], [17, 26, 11, 2, "pressOpacity"], [17, 38, 11, 14], [18, 6, 12, 2, "accessibilityLabel"], [18, 24, 12, 20], [18, 27, 12, 20, "_ref"], [18, 31, 12, 20], [18, 32, 12, 2, "accessibilityLabel"], [18, 50, 12, 20], [19, 6, 13, 2, "testID"], [19, 12, 13, 8], [19, 15, 13, 8, "_ref"], [19, 19, 13, 8], [19, 20, 13, 2, "testID"], [19, 26, 13, 8], [20, 6, 14, 2, "style"], [20, 11, 14, 7], [20, 14, 14, 7, "_ref"], [20, 18, 14, 7], [20, 19, 14, 2, "style"], [20, 24, 14, 7], [21, 6, 15, 2, "href"], [21, 10, 15, 6], [21, 13, 15, 6, "_ref"], [21, 17, 15, 6], [21, 18, 15, 2, "href"], [21, 22, 15, 6], [22, 6, 16, 2, "children"], [22, 14, 16, 10], [22, 17, 16, 10, "_ref"], [22, 21, 16, 10], [22, 22, 16, 2, "children"], [22, 30, 16, 10], [23, 4, 18, 2], [23, 11, 18, 9], [23, 24, 18, 22], [23, 28, 18, 22, "_jsx"], [23, 43, 18, 26], [23, 45, 18, 27, "PlatformPressable"], [23, 81, 18, 44], [23, 83, 18, 46], [24, 6, 19, 4, "ref"], [24, 9, 19, 7], [24, 11, 19, 9, "ref"], [24, 14, 19, 12], [25, 6, 20, 4, "disabled"], [25, 14, 20, 12], [25, 16, 20, 14, "disabled"], [25, 24, 20, 22], [26, 6, 21, 4, "href"], [26, 10, 21, 8], [26, 12, 21, 10, "href"], [26, 16, 21, 14], [27, 6, 22, 4], [27, 18, 22, 16], [27, 20, 22, 18, "accessibilityLabel"], [27, 38, 22, 36], [28, 6, 23, 4, "testID"], [28, 12, 23, 10], [28, 14, 23, 12, "testID"], [28, 20, 23, 18], [29, 6, 24, 4, "onPress"], [29, 13, 24, 11], [29, 15, 24, 13, "onPress"], [29, 22, 24, 20], [30, 6, 25, 4, "pressColor"], [30, 16, 25, 14], [30, 18, 25, 16, "pressColor"], [30, 28, 25, 26], [31, 6, 26, 4, "pressOpacity"], [31, 18, 26, 16], [31, 20, 26, 18, "pressOpacity"], [31, 32, 26, 30], [32, 6, 27, 4, "android_ripple"], [32, 20, 27, 18], [32, 22, 27, 20, "androidRipple"], [32, 35, 27, 33], [33, 6, 28, 4, "style"], [33, 11, 28, 9], [33, 13, 28, 11], [33, 14, 28, 12, "styles"], [33, 20, 28, 18], [33, 21, 28, 19, "container"], [33, 30, 28, 28], [33, 32, 28, 30, "disabled"], [33, 40, 28, 38], [33, 44, 28, 42, "styles"], [33, 50, 28, 48], [33, 51, 28, 49, "disabled"], [33, 59, 28, 57], [33, 61, 28, 59, "style"], [33, 66, 28, 64], [33, 67, 28, 65], [34, 6, 29, 4, "hitSlop"], [34, 13, 29, 11], [34, 15, 29, 13, "Platform"], [34, 36, 29, 21], [34, 37, 29, 22, "select"], [34, 43, 29, 28], [34, 44, 29, 29], [35, 8, 30, 6, "ios"], [35, 11, 30, 9], [35, 13, 30, 11, "undefined"], [35, 22, 30, 20], [36, 8, 31, 6, "default"], [36, 15, 31, 13], [36, 17, 31, 15], [37, 10, 32, 8, "top"], [37, 13, 32, 11], [37, 15, 32, 13], [37, 17, 32, 15], [38, 10, 33, 8, "right"], [38, 15, 33, 13], [38, 17, 33, 15], [38, 19, 33, 17], [39, 10, 34, 8, "bottom"], [39, 16, 34, 14], [39, 18, 34, 16], [39, 20, 34, 18], [40, 10, 35, 8, "left"], [40, 14, 35, 12], [40, 16, 35, 14], [41, 8, 36, 6], [42, 6, 37, 4], [42, 7, 37, 5], [42, 8, 37, 6], [43, 6, 38, 4, "children"], [43, 14, 38, 12], [43, 16, 38, 14, "children"], [44, 4, 39, 2], [44, 5, 39, 3], [44, 6, 39, 4], [45, 2, 40, 0], [46, 2, 41, 7], [46, 6, 41, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [46, 18, 41, 25], [46, 21, 41, 25, "exports"], [46, 28, 41, 25], [46, 29, 41, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [46, 41, 41, 25], [46, 44, 41, 28], [46, 57, 41, 41, "React"], [46, 62, 41, 46], [46, 63, 41, 47, "forwardRef"], [46, 73, 41, 57], [46, 74, 41, 58, "HeaderButtonInternal"], [46, 94, 41, 78], [46, 95, 41, 79], [47, 2, 42, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [47, 14, 42, 12], [47, 15, 42, 13, "displayName"], [47, 26, 42, 24], [47, 29, 42, 27], [47, 43, 42, 41], [48, 2, 43, 0], [48, 6, 43, 6, "androidRipple"], [48, 19, 43, 19], [48, 22, 43, 22], [49, 4, 44, 2, "borderless"], [49, 14, 44, 12], [49, 16, 44, 14], [49, 20, 44, 18], [50, 4, 45, 2, "foreground"], [50, 14, 45, 12], [50, 16, 45, 14, "Platform"], [50, 37, 45, 22], [50, 38, 45, 23, "OS"], [50, 40, 45, 25], [50, 45, 45, 30], [50, 54, 45, 39], [50, 58, 45, 43, "Platform"], [50, 79, 45, 51], [50, 80, 45, 52, "Version"], [50, 87, 45, 59], [50, 91, 45, 63], [50, 93, 45, 65], [51, 4, 46, 2, "radius"], [51, 10, 46, 8], [51, 12, 46, 10], [52, 2, 47, 0], [52, 3, 47, 1], [53, 2, 48, 0], [53, 6, 48, 6, "styles"], [53, 12, 48, 12], [53, 15, 48, 15, "StyleSheet"], [53, 38, 48, 25], [53, 39, 48, 26, "create"], [53, 45, 48, 32], [53, 46, 48, 33], [54, 4, 49, 2, "container"], [54, 13, 49, 11], [54, 15, 49, 13], [55, 6, 50, 4, "flexDirection"], [55, 19, 50, 17], [55, 21, 50, 19], [55, 26, 50, 24], [56, 6, 51, 4, "alignItems"], [56, 16, 51, 14], [56, 18, 51, 16], [56, 26, 51, 24], [57, 6, 52, 4, "paddingHorizontal"], [57, 23, 52, 21], [57, 25, 52, 23], [57, 26, 52, 24], [58, 6, 53, 4], [59, 6, 54, 4, "borderRadius"], [59, 18, 54, 16], [59, 20, 54, 18], [60, 4, 55, 2], [60, 5, 55, 3], [61, 4, 56, 2, "disabled"], [61, 12, 56, 10], [61, 14, 56, 12], [62, 6, 57, 4, "opacity"], [62, 13, 57, 11], [62, 15, 57, 13], [63, 4, 58, 2], [64, 2, 59, 0], [64, 3, 59, 1], [64, 4, 59, 2], [65, 0, 59, 3], [65, 3]], "functionMap": {"names": ["<global>", "HeaderButtonInternal"], "mappings": "AAA;ACM;CDiC"}}, "type": "js/module"}]}