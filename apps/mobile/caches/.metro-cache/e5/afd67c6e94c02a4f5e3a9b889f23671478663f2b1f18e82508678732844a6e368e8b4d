{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./HeaderHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 63, "index": 110}}], "key": "D2EUjQkDLcdKKEMlJIHvq8Uo0gc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useHeaderHeight = useHeaderHeight;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _HeaderHeightContext = require(_dependencyMap[1], \"./HeaderHeightContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function useHeaderHeight() {\n    const height = React.useContext(_HeaderHeightContext.HeaderHeightContext);\n    if (height === undefined) {\n      throw new Error(\"Couldn't find the header height. Are you inside a screen in a navigator with a header?\");\n    }\n    return height;\n  }\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useHeaderHeight"], [7, 25, 1, 13], [7, 28, 1, 13, "useHeaderHeight"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_HeaderHeightContext"], [9, 26, 4, 0], [9, 29, 4, 0, "require"], [9, 36, 4, 0], [9, 37, 4, 0, "_dependencyMap"], [9, 51, 4, 0], [10, 2, 4, 63], [10, 11, 4, 63, "_interopRequireWildcard"], [10, 35, 4, 63, "e"], [10, 36, 4, 63], [10, 38, 4, 63, "t"], [10, 39, 4, 63], [10, 68, 4, 63, "WeakMap"], [10, 75, 4, 63], [10, 81, 4, 63, "r"], [10, 82, 4, 63], [10, 89, 4, 63, "WeakMap"], [10, 96, 4, 63], [10, 100, 4, 63, "n"], [10, 101, 4, 63], [10, 108, 4, 63, "WeakMap"], [10, 115, 4, 63], [10, 127, 4, 63, "_interopRequireWildcard"], [10, 150, 4, 63], [10, 162, 4, 63, "_interopRequireWildcard"], [10, 163, 4, 63, "e"], [10, 164, 4, 63], [10, 166, 4, 63, "t"], [10, 167, 4, 63], [10, 176, 4, 63, "t"], [10, 177, 4, 63], [10, 181, 4, 63, "e"], [10, 182, 4, 63], [10, 186, 4, 63, "e"], [10, 187, 4, 63], [10, 188, 4, 63, "__esModule"], [10, 198, 4, 63], [10, 207, 4, 63, "e"], [10, 208, 4, 63], [10, 214, 4, 63, "o"], [10, 215, 4, 63], [10, 217, 4, 63, "i"], [10, 218, 4, 63], [10, 220, 4, 63, "f"], [10, 221, 4, 63], [10, 226, 4, 63, "__proto__"], [10, 235, 4, 63], [10, 243, 4, 63, "default"], [10, 250, 4, 63], [10, 252, 4, 63, "e"], [10, 253, 4, 63], [10, 270, 4, 63, "e"], [10, 271, 4, 63], [10, 294, 4, 63, "e"], [10, 295, 4, 63], [10, 320, 4, 63, "e"], [10, 321, 4, 63], [10, 330, 4, 63, "f"], [10, 331, 4, 63], [10, 337, 4, 63, "o"], [10, 338, 4, 63], [10, 341, 4, 63, "t"], [10, 342, 4, 63], [10, 345, 4, 63, "n"], [10, 346, 4, 63], [10, 349, 4, 63, "r"], [10, 350, 4, 63], [10, 358, 4, 63, "o"], [10, 359, 4, 63], [10, 360, 4, 63, "has"], [10, 363, 4, 63], [10, 364, 4, 63, "e"], [10, 365, 4, 63], [10, 375, 4, 63, "o"], [10, 376, 4, 63], [10, 377, 4, 63, "get"], [10, 380, 4, 63], [10, 381, 4, 63, "e"], [10, 382, 4, 63], [10, 385, 4, 63, "o"], [10, 386, 4, 63], [10, 387, 4, 63, "set"], [10, 390, 4, 63], [10, 391, 4, 63, "e"], [10, 392, 4, 63], [10, 394, 4, 63, "f"], [10, 395, 4, 63], [10, 411, 4, 63, "t"], [10, 412, 4, 63], [10, 416, 4, 63, "e"], [10, 417, 4, 63], [10, 433, 4, 63, "t"], [10, 434, 4, 63], [10, 441, 4, 63, "hasOwnProperty"], [10, 455, 4, 63], [10, 456, 4, 63, "call"], [10, 460, 4, 63], [10, 461, 4, 63, "e"], [10, 462, 4, 63], [10, 464, 4, 63, "t"], [10, 465, 4, 63], [10, 472, 4, 63, "i"], [10, 473, 4, 63], [10, 477, 4, 63, "o"], [10, 478, 4, 63], [10, 481, 4, 63, "Object"], [10, 487, 4, 63], [10, 488, 4, 63, "defineProperty"], [10, 502, 4, 63], [10, 507, 4, 63, "Object"], [10, 513, 4, 63], [10, 514, 4, 63, "getOwnPropertyDescriptor"], [10, 538, 4, 63], [10, 539, 4, 63, "e"], [10, 540, 4, 63], [10, 542, 4, 63, "t"], [10, 543, 4, 63], [10, 550, 4, 63, "i"], [10, 551, 4, 63], [10, 552, 4, 63, "get"], [10, 555, 4, 63], [10, 559, 4, 63, "i"], [10, 560, 4, 63], [10, 561, 4, 63, "set"], [10, 564, 4, 63], [10, 568, 4, 63, "o"], [10, 569, 4, 63], [10, 570, 4, 63, "f"], [10, 571, 4, 63], [10, 573, 4, 63, "t"], [10, 574, 4, 63], [10, 576, 4, 63, "i"], [10, 577, 4, 63], [10, 581, 4, 63, "f"], [10, 582, 4, 63], [10, 583, 4, 63, "t"], [10, 584, 4, 63], [10, 588, 4, 63, "e"], [10, 589, 4, 63], [10, 590, 4, 63, "t"], [10, 591, 4, 63], [10, 602, 4, 63, "f"], [10, 603, 4, 63], [10, 608, 4, 63, "e"], [10, 609, 4, 63], [10, 611, 4, 63, "t"], [10, 612, 4, 63], [11, 2, 5, 7], [11, 11, 5, 16, "useHeaderHeight"], [11, 26, 5, 31, "useHeaderHeight"], [11, 27, 5, 31], [11, 29, 5, 34], [12, 4, 6, 2], [12, 10, 6, 8, "height"], [12, 16, 6, 14], [12, 19, 6, 17, "React"], [12, 24, 6, 22], [12, 25, 6, 23, "useContext"], [12, 35, 6, 33], [12, 36, 6, 34, "HeaderHeightContext"], [12, 76, 6, 53], [12, 77, 6, 54], [13, 4, 7, 2], [13, 8, 7, 6, "height"], [13, 14, 7, 12], [13, 19, 7, 17, "undefined"], [13, 28, 7, 26], [13, 30, 7, 28], [14, 6, 8, 4], [14, 12, 8, 10], [14, 16, 8, 14, "Error"], [14, 21, 8, 19], [14, 22, 8, 20], [14, 110, 8, 108], [14, 111, 8, 109], [15, 4, 9, 2], [16, 4, 10, 2], [16, 11, 10, 9, "height"], [16, 17, 10, 15], [17, 2, 11, 0], [18, 0, 11, 1], [18, 3]], "functionMap": {"names": ["<global>", "useHeaderHeight"], "mappings": "AAA;OCI;CDM"}}, "type": "js/module"}]}