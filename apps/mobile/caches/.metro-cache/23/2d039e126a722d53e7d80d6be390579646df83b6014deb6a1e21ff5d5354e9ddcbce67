{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 118}, "end": {"line": 5, "column": 50, "index": 168}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 169}, "end": {"line": 6, "column": 48, "index": 217}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Text = Text;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  var _excluded = [\"style\"]; // eslint-disable-next-line no-restricted-imports\n  function Text(_ref) {\n    var style = _ref.style,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Text, {\n      ...rest,\n      style: [{\n        color: colors.text\n      }, fonts.regular, style]\n    });\n  }\n});", "lineCount": 27, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Text"], [8, 14, 1, 13], [8, 17, 1, 13, "Text"], [8, 21, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_reactNative"], [11, 18, 5, 0], [11, 21, 5, 0, "require"], [11, 28, 5, 0], [11, 29, 5, 0, "_dependencyMap"], [11, 43, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_jsxRuntime"], [12, 17, 6, 0], [12, 20, 6, 0, "require"], [12, 27, 6, 0], [12, 28, 6, 0, "_dependencyMap"], [12, 42, 6, 0], [13, 2, 6, 48], [13, 6, 6, 48, "_excluded"], [13, 15, 6, 48], [13, 29, 4, 0], [14, 2, 7, 7], [14, 11, 7, 16, "Text"], [14, 15, 7, 20, "Text"], [14, 16, 7, 20, "_ref"], [14, 20, 7, 20], [14, 22, 10, 3], [15, 4, 10, 3], [15, 8, 8, 2, "style"], [15, 13, 8, 7], [15, 16, 8, 7, "_ref"], [15, 20, 8, 7], [15, 21, 8, 2, "style"], [15, 26, 8, 7], [16, 6, 9, 5, "rest"], [16, 10, 9, 9], [16, 17, 9, 9, "_objectWithoutProperties2"], [16, 42, 9, 9], [16, 43, 9, 9, "default"], [16, 50, 9, 9], [16, 52, 9, 9, "_ref"], [16, 56, 9, 9], [16, 58, 9, 9, "_excluded"], [16, 67, 9, 9], [17, 4, 11, 2], [17, 8, 11, 2, "_useTheme"], [17, 17, 11, 2], [17, 20, 14, 6], [17, 24, 14, 6, "useTheme"], [17, 40, 14, 14], [17, 42, 14, 15], [17, 43, 14, 16], [18, 6, 12, 4, "colors"], [18, 12, 12, 10], [18, 15, 12, 10, "_useTheme"], [18, 24, 12, 10], [18, 25, 12, 4, "colors"], [18, 31, 12, 10], [19, 6, 13, 4, "fonts"], [19, 11, 13, 9], [19, 14, 13, 9, "_useTheme"], [19, 23, 13, 9], [19, 24, 13, 4, "fonts"], [19, 29, 13, 9], [20, 4, 15, 2], [20, 11, 15, 9], [20, 24, 15, 22], [20, 28, 15, 22, "_jsx"], [20, 43, 15, 26], [20, 45, 15, 27, "NativeText"], [20, 62, 15, 37], [20, 64, 15, 39], [21, 6, 16, 4], [21, 9, 16, 7, "rest"], [21, 13, 16, 11], [22, 6, 17, 4, "style"], [22, 11, 17, 9], [22, 13, 17, 11], [22, 14, 17, 12], [23, 8, 18, 6, "color"], [23, 13, 18, 11], [23, 15, 18, 13, "colors"], [23, 21, 18, 19], [23, 22, 18, 20, "text"], [24, 6, 19, 4], [24, 7, 19, 5], [24, 9, 19, 7, "fonts"], [24, 14, 19, 12], [24, 15, 19, 13, "regular"], [24, 22, 19, 20], [24, 24, 19, 22, "style"], [24, 29, 19, 27], [25, 4, 20, 2], [25, 5, 20, 3], [25, 6, 20, 4], [26, 2, 21, 0], [27, 0, 21, 1], [27, 3]], "functionMap": {"names": ["<global>", "Text"], "mappings": "AAA;OCM;CDc"}}, "type": "js/module"}]}