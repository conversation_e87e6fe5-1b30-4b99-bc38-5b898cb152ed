{"dependencies": [{"name": "../../shared", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 17, "index": 131}, "end": {"line": 4, "column": 40, "index": 154}}], "key": "nGuj9FEtRBU67xZ2eTMaA3OncKU=", "exportNames": ["*"]}}, {"name": "../observable", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 21, "index": 177}, "end": {"line": 5, "column": 45, "index": 201}}], "key": "Wp4whP3mc8t6X+fLKju5VuvTTrc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rem = exports._rem = void 0;\n  const shared_1 = require(_dependencyMap[0], \"../../shared\");\n  const observable_1 = require(_dependencyMap[1], \"../observable\");\n  const isSSR = globalThis.window === undefined;\n  exports._rem = (0, observable_1.observable)(isSSR ? 16 : Number.parseFloat(globalThis.window.getComputedStyle(globalThis.window.document.documentElement).fontSize) || 16);\n  exports.rem = {\n    get(effect) {\n      return exports._rem.get(effect);\n    },\n    set(value) {\n      exports._rem.set(value);\n      if (!isSSR) {\n        globalThis.window.document.documentElement.style.fontSize = `${value}px`;\n      }\n    },\n    [shared_1.INTERNAL_RESET](value = 16) {\n      exports._rem.set(value);\n    }\n  };\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "rem"], [7, 13, 3, 11], [7, 16, 3, 14, "exports"], [7, 23, 3, 21], [7, 24, 3, 22, "_rem"], [7, 28, 3, 26], [7, 31, 3, 29], [7, 36, 3, 34], [7, 37, 3, 35], [8, 2, 4, 0], [8, 8, 4, 6, "shared_1"], [8, 16, 4, 14], [8, 19, 4, 17, "require"], [8, 26, 4, 24], [8, 27, 4, 24, "_dependencyMap"], [8, 41, 4, 24], [8, 60, 4, 39], [8, 61, 4, 40], [9, 2, 5, 0], [9, 8, 5, 6, "observable_1"], [9, 20, 5, 18], [9, 23, 5, 21, "require"], [9, 30, 5, 28], [9, 31, 5, 28, "_dependencyMap"], [9, 45, 5, 28], [9, 65, 5, 44], [9, 66, 5, 45], [10, 2, 6, 0], [10, 8, 6, 6, "isSSR"], [10, 13, 6, 11], [10, 16, 6, 14, "globalThis"], [10, 26, 6, 24], [10, 27, 6, 25, "window"], [10, 33, 6, 31], [10, 38, 6, 36, "undefined"], [10, 47, 6, 45], [11, 2, 7, 0, "exports"], [11, 9, 7, 7], [11, 10, 7, 8, "_rem"], [11, 14, 7, 12], [11, 17, 7, 15], [11, 18, 7, 16], [11, 19, 7, 17], [11, 21, 7, 19, "observable_1"], [11, 33, 7, 31], [11, 34, 7, 32, "observable"], [11, 44, 7, 42], [11, 46, 7, 44, "isSSR"], [11, 51, 7, 49], [11, 54, 8, 6], [11, 56, 8, 8], [11, 59, 9, 6, "Number"], [11, 65, 9, 12], [11, 66, 9, 13, "parseFloat"], [11, 76, 9, 23], [11, 77, 9, 24, "globalThis"], [11, 87, 9, 34], [11, 88, 9, 35, "window"], [11, 94, 9, 41], [11, 95, 9, 42, "getComputedStyle"], [11, 111, 9, 58], [11, 112, 9, 59, "globalThis"], [11, 122, 9, 69], [11, 123, 9, 70, "window"], [11, 129, 9, 76], [11, 130, 9, 77, "document"], [11, 138, 9, 85], [11, 139, 9, 86, "documentElement"], [11, 154, 9, 101], [11, 155, 9, 102], [11, 156, 9, 103, "fontSize"], [11, 164, 9, 111], [11, 165, 9, 112], [11, 169, 9, 116], [11, 171, 9, 118], [11, 172, 9, 119], [12, 2, 10, 0, "exports"], [12, 9, 10, 7], [12, 10, 10, 8, "rem"], [12, 13, 10, 11], [12, 16, 10, 14], [13, 4, 11, 4, "get"], [13, 7, 11, 7, "get"], [13, 8, 11, 8, "effect"], [13, 14, 11, 14], [13, 16, 11, 16], [14, 6, 12, 8], [14, 13, 12, 15, "exports"], [14, 20, 12, 22], [14, 21, 12, 23, "_rem"], [14, 25, 12, 27], [14, 26, 12, 28, "get"], [14, 29, 12, 31], [14, 30, 12, 32, "effect"], [14, 36, 12, 38], [14, 37, 12, 39], [15, 4, 13, 4], [15, 5, 13, 5], [16, 4, 14, 4, "set"], [16, 7, 14, 7, "set"], [16, 8, 14, 8, "value"], [16, 13, 14, 13], [16, 15, 14, 15], [17, 6, 15, 8, "exports"], [17, 13, 15, 15], [17, 14, 15, 16, "_rem"], [17, 18, 15, 20], [17, 19, 15, 21, "set"], [17, 22, 15, 24], [17, 23, 15, 25, "value"], [17, 28, 15, 30], [17, 29, 15, 31], [18, 6, 16, 8], [18, 10, 16, 12], [18, 11, 16, 13, "isSSR"], [18, 16, 16, 18], [18, 18, 16, 20], [19, 8, 17, 12, "globalThis"], [19, 18, 17, 22], [19, 19, 17, 23, "window"], [19, 25, 17, 29], [19, 26, 17, 30, "document"], [19, 34, 17, 38], [19, 35, 17, 39, "documentElement"], [19, 50, 17, 54], [19, 51, 17, 55, "style"], [19, 56, 17, 60], [19, 57, 17, 61, "fontSize"], [19, 65, 17, 69], [19, 68, 17, 72], [19, 71, 17, 75, "value"], [19, 76, 17, 80], [19, 80, 17, 84], [20, 6, 18, 8], [21, 4, 19, 4], [21, 5, 19, 5], [22, 4, 20, 4], [22, 5, 20, 5, "shared_1"], [22, 13, 20, 13], [22, 14, 20, 14, "INTERNAL_RESET"], [22, 28, 20, 28], [22, 30, 20, 30, "value"], [22, 35, 20, 35], [22, 38, 20, 38], [22, 40, 20, 40], [22, 42, 20, 42], [23, 6, 21, 8, "exports"], [23, 13, 21, 15], [23, 14, 21, 16, "_rem"], [23, 18, 21, 20], [23, 19, 21, 21, "set"], [23, 22, 21, 24], [23, 23, 21, 25, "value"], [23, 28, 21, 30], [23, 29, 21, 31], [24, 4, 22, 4], [25, 2, 23, 0], [25, 3, 23, 1], [26, 0, 23, 2], [26, 3]], "functionMap": {"names": ["<global>", "exports.rem.get", "exports.rem.set", "exports.rem.shared_1.INTERNAL_RESET"], "mappings": "AAA;ICU;KDE;IEC;KFK;IGC;KHE"}}, "type": "js/module"}]}