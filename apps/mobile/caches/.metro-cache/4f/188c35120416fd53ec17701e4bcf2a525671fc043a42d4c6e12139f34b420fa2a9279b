{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 50, "index": 298}}], "key": "eTOOXVNPpMK2U8dOAmBWjbEJ4yE=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 299}, "end": {"line": 5, "column": 46, "index": 345}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}, {"name": "../detectors/ScaleGestureDetector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 346}, "end": {"line": 6, "column": 69, "index": 415}}], "key": "tFOd3eEKqx8JoU1k9XAnYKO/ik8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _constants = require(_dependencyMap[2], \"../constants\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./GestureHandler\"));\n  var _ScaleGestureDetector = _interopRequireDefault(require(_dependencyMap[4], \"../detectors/ScaleGestureDetector\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class PinchGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"scale\", 1);\n      _defineProperty(this, \"velocity\", 0);\n      _defineProperty(this, \"startingSpan\", 0);\n      _defineProperty(this, \"spanSlop\", _constants.DEFAULT_TOUCH_SLOP);\n      _defineProperty(this, \"scaleDetectorListener\", {\n        onScaleBegin: detector => {\n          this.startingSpan = detector.currentSpan;\n          return true;\n        },\n        onScale: detector => {\n          const prevScaleFactor = this.scale;\n          this.scale *= detector.calculateScaleFactor(this.tracker.trackedPointersCount);\n          const delta = detector.timeDelta;\n          if (delta > 0) {\n            this.velocity = (this.scale - prevScaleFactor) / delta;\n          }\n          if (Math.abs(this.startingSpan - detector.currentSpan) >= this.spanSlop && this.state === _State.State.BEGAN) {\n            this.activate();\n          }\n          return true;\n        },\n        onScaleEnd: _detector => {}\n      });\n      _defineProperty(this, \"scaleGestureDetector\", new _ScaleGestureDetector.default(this.scaleDetectorListener));\n    }\n    init(ref, propsRef) {\n      super.init(ref, propsRef);\n      this.shouldCancelWhenOutside = false;\n    }\n    transformNativeEvent() {\n      return {\n        focalX: this.scaleGestureDetector.focusX,\n        focalY: this.scaleGestureDetector.focusY,\n        velocity: this.velocity,\n        scale: this.scale\n      };\n    }\n    onPointerDown(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerDown(event);\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerAdd(event);\n      this.tryBegin();\n      this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      this.tracker.removeFromTracker(event.pointerId);\n      if (this.state !== _State.State.ACTIVE) {\n        return;\n      }\n      this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n      if (this.state === _State.State.ACTIVE) {\n        this.end();\n      } else {\n        this.fail();\n      }\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n      this.tracker.removeFromTracker(event.pointerId);\n      if (this.state === _State.State.ACTIVE && this.tracker.trackedPointersCount < 2) {\n        this.end();\n      }\n    }\n    onPointerMove(event) {\n      if (this.tracker.trackedPointersCount < 2) {\n        return;\n      }\n      this.tracker.track(event);\n      this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n      super.onPointerMove(event);\n    }\n    onPointerOutOfBounds(event) {\n      if (this.tracker.trackedPointersCount < 2) {\n        return;\n      }\n      this.tracker.track(event);\n      this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n      super.onPointerOutOfBounds(event);\n    }\n    tryBegin() {\n      if (this.state !== _State.State.UNDETERMINED) {\n        return;\n      }\n      this.resetProgress();\n      this.begin();\n    }\n    activate(force) {\n      if (this.state !== _State.State.ACTIVE) {\n        this.resetProgress();\n      }\n      super.activate(force);\n    }\n    onReset() {\n      this.resetProgress();\n    }\n    resetProgress() {\n      if (this.state === _State.State.ACTIVE) {\n        return;\n      }\n      this.velocity = 0;\n      this.scale = 1;\n    }\n  }\n  exports.default = PinchGestureHandler;\n});", "lineCount": 137, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_constants"], [8, 16, 4, 0], [8, 19, 4, 0, "require"], [8, 26, 4, 0], [8, 27, 4, 0, "_dependencyMap"], [8, 41, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_Gesture<PERSON><PERSON>ler"], [9, 21, 5, 0], [9, 24, 5, 0, "_interopRequireDefault"], [9, 46, 5, 0], [9, 47, 5, 0, "require"], [9, 54, 5, 0], [9, 55, 5, 0, "_dependencyMap"], [9, 69, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_ScaleGestureDetector"], [10, 27, 6, 0], [10, 30, 6, 0, "_interopRequireDefault"], [10, 52, 6, 0], [10, 53, 6, 0, "require"], [10, 60, 6, 0], [10, 61, 6, 0, "_dependencyMap"], [10, 75, 6, 0], [11, 2, 1, 0], [11, 11, 1, 9, "_defineProperty"], [11, 26, 1, 24, "_defineProperty"], [11, 27, 1, 25, "obj"], [11, 30, 1, 28], [11, 32, 1, 30, "key"], [11, 35, 1, 33], [11, 37, 1, 35, "value"], [11, 42, 1, 40], [11, 44, 1, 42], [12, 4, 1, 44], [12, 8, 1, 48, "key"], [12, 11, 1, 51], [12, 15, 1, 55, "obj"], [12, 18, 1, 58], [12, 20, 1, 60], [13, 6, 1, 62, "Object"], [13, 12, 1, 68], [13, 13, 1, 69, "defineProperty"], [13, 27, 1, 83], [13, 28, 1, 84, "obj"], [13, 31, 1, 87], [13, 33, 1, 89, "key"], [13, 36, 1, 92], [13, 38, 1, 94], [14, 8, 1, 96, "value"], [14, 13, 1, 101], [14, 15, 1, 103, "value"], [14, 20, 1, 108], [15, 8, 1, 110, "enumerable"], [15, 18, 1, 120], [15, 20, 1, 122], [15, 24, 1, 126], [16, 8, 1, 128, "configurable"], [16, 20, 1, 140], [16, 22, 1, 142], [16, 26, 1, 146], [17, 8, 1, 148, "writable"], [17, 16, 1, 156], [17, 18, 1, 158], [18, 6, 1, 163], [18, 7, 1, 164], [18, 8, 1, 165], [19, 4, 1, 167], [19, 5, 1, 168], [19, 11, 1, 174], [20, 6, 1, 176, "obj"], [20, 9, 1, 179], [20, 10, 1, 180, "key"], [20, 13, 1, 183], [20, 14, 1, 184], [20, 17, 1, 187, "value"], [20, 22, 1, 192], [21, 4, 1, 194], [22, 4, 1, 196], [22, 11, 1, 203, "obj"], [22, 14, 1, 206], [23, 2, 1, 208], [24, 2, 7, 15], [24, 8, 7, 21, "PinchGestureHandler"], [24, 27, 7, 40], [24, 36, 7, 49, "Gesture<PERSON>andler"], [24, 59, 7, 63], [24, 60, 7, 64], [25, 4, 8, 2, "constructor"], [25, 15, 8, 13, "constructor"], [25, 16, 8, 14], [25, 19, 8, 17, "args"], [25, 23, 8, 21], [25, 25, 8, 23], [26, 6, 9, 4], [26, 11, 9, 9], [26, 12, 9, 10], [26, 15, 9, 13, "args"], [26, 19, 9, 17], [26, 20, 9, 18], [27, 6, 11, 4, "_defineProperty"], [27, 21, 11, 19], [27, 22, 11, 20], [27, 26, 11, 24], [27, 28, 11, 26], [27, 35, 11, 33], [27, 37, 11, 35], [27, 38, 11, 36], [27, 39, 11, 37], [28, 6, 13, 4, "_defineProperty"], [28, 21, 13, 19], [28, 22, 13, 20], [28, 26, 13, 24], [28, 28, 13, 26], [28, 38, 13, 36], [28, 40, 13, 38], [28, 41, 13, 39], [28, 42, 13, 40], [29, 6, 15, 4, "_defineProperty"], [29, 21, 15, 19], [29, 22, 15, 20], [29, 26, 15, 24], [29, 28, 15, 26], [29, 42, 15, 40], [29, 44, 15, 42], [29, 45, 15, 43], [29, 46, 15, 44], [30, 6, 17, 4, "_defineProperty"], [30, 21, 17, 19], [30, 22, 17, 20], [30, 26, 17, 24], [30, 28, 17, 26], [30, 38, 17, 36], [30, 40, 17, 38, "DEFAULT_TOUCH_SLOP"], [30, 69, 17, 56], [30, 70, 17, 57], [31, 6, 19, 4, "_defineProperty"], [31, 21, 19, 19], [31, 22, 19, 20], [31, 26, 19, 24], [31, 28, 19, 26], [31, 51, 19, 49], [31, 53, 19, 51], [32, 8, 20, 6, "onScaleBegin"], [32, 20, 20, 18], [32, 22, 20, 20, "detector"], [32, 30, 20, 28], [32, 34, 20, 32], [33, 10, 21, 8], [33, 14, 21, 12], [33, 15, 21, 13, "startingSpan"], [33, 27, 21, 25], [33, 30, 21, 28, "detector"], [33, 38, 21, 36], [33, 39, 21, 37, "currentSpan"], [33, 50, 21, 48], [34, 10, 22, 8], [34, 17, 22, 15], [34, 21, 22, 19], [35, 8, 23, 6], [35, 9, 23, 7], [36, 8, 24, 6, "onScale"], [36, 15, 24, 13], [36, 17, 24, 15, "detector"], [36, 25, 24, 23], [36, 29, 24, 27], [37, 10, 25, 8], [37, 16, 25, 14, "prevScaleFactor"], [37, 31, 25, 29], [37, 34, 25, 32], [37, 38, 25, 36], [37, 39, 25, 37, "scale"], [37, 44, 25, 42], [38, 10, 26, 8], [38, 14, 26, 12], [38, 15, 26, 13, "scale"], [38, 20, 26, 18], [38, 24, 26, 22, "detector"], [38, 32, 26, 30], [38, 33, 26, 31, "calculateScaleFactor"], [38, 53, 26, 51], [38, 54, 26, 52], [38, 58, 26, 56], [38, 59, 26, 57, "tracker"], [38, 66, 26, 64], [38, 67, 26, 65, "trackedPointersCount"], [38, 87, 26, 85], [38, 88, 26, 86], [39, 10, 27, 8], [39, 16, 27, 14, "delta"], [39, 21, 27, 19], [39, 24, 27, 22, "detector"], [39, 32, 27, 30], [39, 33, 27, 31, "<PERSON><PERSON><PERSON><PERSON>"], [39, 42, 27, 40], [40, 10, 29, 8], [40, 14, 29, 12, "delta"], [40, 19, 29, 17], [40, 22, 29, 20], [40, 23, 29, 21], [40, 25, 29, 23], [41, 12, 30, 10], [41, 16, 30, 14], [41, 17, 30, 15, "velocity"], [41, 25, 30, 23], [41, 28, 30, 26], [41, 29, 30, 27], [41, 33, 30, 31], [41, 34, 30, 32, "scale"], [41, 39, 30, 37], [41, 42, 30, 40, "prevScaleFactor"], [41, 57, 30, 55], [41, 61, 30, 59, "delta"], [41, 66, 30, 64], [42, 10, 31, 8], [43, 10, 33, 8], [43, 14, 33, 12, "Math"], [43, 18, 33, 16], [43, 19, 33, 17, "abs"], [43, 22, 33, 20], [43, 23, 33, 21], [43, 27, 33, 25], [43, 28, 33, 26, "startingSpan"], [43, 40, 33, 38], [43, 43, 33, 41, "detector"], [43, 51, 33, 49], [43, 52, 33, 50, "currentSpan"], [43, 63, 33, 61], [43, 64, 33, 62], [43, 68, 33, 66], [43, 72, 33, 70], [43, 73, 33, 71, "spanSlop"], [43, 81, 33, 79], [43, 85, 33, 83], [43, 89, 33, 87], [43, 90, 33, 88, "state"], [43, 95, 33, 93], [43, 100, 33, 98, "State"], [43, 112, 33, 103], [43, 113, 33, 104, "BEGAN"], [43, 118, 33, 109], [43, 120, 33, 111], [44, 12, 34, 10], [44, 16, 34, 14], [44, 17, 34, 15, "activate"], [44, 25, 34, 23], [44, 26, 34, 24], [44, 27, 34, 25], [45, 10, 35, 8], [46, 10, 37, 8], [46, 17, 37, 15], [46, 21, 37, 19], [47, 8, 38, 6], [47, 9, 38, 7], [48, 8, 39, 6, "onScaleEnd"], [48, 18, 39, 16], [48, 20, 39, 18, "_detector"], [48, 29, 39, 27], [48, 33, 39, 31], [48, 34, 39, 32], [49, 6, 40, 4], [49, 7, 40, 5], [49, 8, 40, 6], [50, 6, 42, 4, "_defineProperty"], [50, 21, 42, 19], [50, 22, 42, 20], [50, 26, 42, 24], [50, 28, 42, 26], [50, 50, 42, 48], [50, 52, 42, 50], [50, 56, 42, 54, "ScaleGestureDetector"], [50, 85, 42, 74], [50, 86, 42, 75], [50, 90, 42, 79], [50, 91, 42, 80, "scaleDetectorListener"], [50, 112, 42, 101], [50, 113, 42, 102], [50, 114, 42, 103], [51, 4, 43, 2], [52, 4, 45, 2, "init"], [52, 8, 45, 6, "init"], [52, 9, 45, 7, "ref"], [52, 12, 45, 10], [52, 14, 45, 12, "propsRef"], [52, 22, 45, 20], [52, 24, 45, 22], [53, 6, 46, 4], [53, 11, 46, 9], [53, 12, 46, 10, "init"], [53, 16, 46, 14], [53, 17, 46, 15, "ref"], [53, 20, 46, 18], [53, 22, 46, 20, "propsRef"], [53, 30, 46, 28], [53, 31, 46, 29], [54, 6, 47, 4], [54, 10, 47, 8], [54, 11, 47, 9, "shouldCancelWhenOutside"], [54, 34, 47, 32], [54, 37, 47, 35], [54, 42, 47, 40], [55, 4, 48, 2], [56, 4, 50, 2, "transformNativeEvent"], [56, 24, 50, 22, "transformNativeEvent"], [56, 25, 50, 22], [56, 27, 50, 25], [57, 6, 51, 4], [57, 13, 51, 11], [58, 8, 52, 6, "focalX"], [58, 14, 52, 12], [58, 16, 52, 14], [58, 20, 52, 18], [58, 21, 52, 19, "scaleGestureDetector"], [58, 41, 52, 39], [58, 42, 52, 40, "focusX"], [58, 48, 52, 46], [59, 8, 53, 6, "focalY"], [59, 14, 53, 12], [59, 16, 53, 14], [59, 20, 53, 18], [59, 21, 53, 19, "scaleGestureDetector"], [59, 41, 53, 39], [59, 42, 53, 40, "focusY"], [59, 48, 53, 46], [60, 8, 54, 6, "velocity"], [60, 16, 54, 14], [60, 18, 54, 16], [60, 22, 54, 20], [60, 23, 54, 21, "velocity"], [60, 31, 54, 29], [61, 8, 55, 6, "scale"], [61, 13, 55, 11], [61, 15, 55, 13], [61, 19, 55, 17], [61, 20, 55, 18, "scale"], [62, 6, 56, 4], [62, 7, 56, 5], [63, 4, 57, 2], [64, 4, 59, 2, "onPointerDown"], [64, 17, 59, 15, "onPointerDown"], [64, 18, 59, 16, "event"], [64, 23, 59, 21], [64, 25, 59, 23], [65, 6, 60, 4], [65, 10, 60, 8], [65, 11, 60, 9, "tracker"], [65, 18, 60, 16], [65, 19, 60, 17, "addToTracker"], [65, 31, 60, 29], [65, 32, 60, 30, "event"], [65, 37, 60, 35], [65, 38, 60, 36], [66, 6, 61, 4], [66, 11, 61, 9], [66, 12, 61, 10, "onPointerDown"], [66, 25, 61, 23], [66, 26, 61, 24, "event"], [66, 31, 61, 29], [66, 32, 61, 30], [67, 6, 62, 4], [67, 10, 62, 8], [67, 11, 62, 9, "tryToSendTouchEvent"], [67, 30, 62, 28], [67, 31, 62, 29, "event"], [67, 36, 62, 34], [67, 37, 62, 35], [68, 4, 63, 2], [69, 4, 65, 2, "onPointerAdd"], [69, 16, 65, 14, "onPointerAdd"], [69, 17, 65, 15, "event"], [69, 22, 65, 20], [69, 24, 65, 22], [70, 6, 66, 4], [70, 10, 66, 8], [70, 11, 66, 9, "tracker"], [70, 18, 66, 16], [70, 19, 66, 17, "addToTracker"], [70, 31, 66, 29], [70, 32, 66, 30, "event"], [70, 37, 66, 35], [70, 38, 66, 36], [71, 6, 67, 4], [71, 11, 67, 9], [71, 12, 67, 10, "onPointerAdd"], [71, 24, 67, 22], [71, 25, 67, 23, "event"], [71, 30, 67, 28], [71, 31, 67, 29], [72, 6, 68, 4], [72, 10, 68, 8], [72, 11, 68, 9, "tryBegin"], [72, 19, 68, 17], [72, 20, 68, 18], [72, 21, 68, 19], [73, 6, 69, 4], [73, 10, 69, 8], [73, 11, 69, 9, "scaleGestureDetector"], [73, 31, 69, 29], [73, 32, 69, 30, "onTouchEvent"], [73, 44, 69, 42], [73, 45, 69, 43, "event"], [73, 50, 69, 48], [73, 52, 69, 50], [73, 56, 69, 54], [73, 57, 69, 55, "tracker"], [73, 64, 69, 62], [73, 65, 69, 63], [74, 4, 70, 2], [75, 4, 72, 2, "onPointerUp"], [75, 15, 72, 13, "onPointerUp"], [75, 16, 72, 14, "event"], [75, 21, 72, 19], [75, 23, 72, 21], [76, 6, 73, 4], [76, 11, 73, 9], [76, 12, 73, 10, "onPointerUp"], [76, 23, 73, 21], [76, 24, 73, 22, "event"], [76, 29, 73, 27], [76, 30, 73, 28], [77, 6, 74, 4], [77, 10, 74, 8], [77, 11, 74, 9, "tracker"], [77, 18, 74, 16], [77, 19, 74, 17, "removeFromTracker"], [77, 36, 74, 34], [77, 37, 74, 35, "event"], [77, 42, 74, 40], [77, 43, 74, 41, "pointerId"], [77, 52, 74, 50], [77, 53, 74, 51], [78, 6, 76, 4], [78, 10, 76, 8], [78, 14, 76, 12], [78, 15, 76, 13, "state"], [78, 20, 76, 18], [78, 25, 76, 23, "State"], [78, 37, 76, 28], [78, 38, 76, 29, "ACTIVE"], [78, 44, 76, 35], [78, 46, 76, 37], [79, 8, 77, 6], [80, 6, 78, 4], [81, 6, 80, 4], [81, 10, 80, 8], [81, 11, 80, 9, "scaleGestureDetector"], [81, 31, 80, 29], [81, 32, 80, 30, "onTouchEvent"], [81, 44, 80, 42], [81, 45, 80, 43, "event"], [81, 50, 80, 48], [81, 52, 80, 50], [81, 56, 80, 54], [81, 57, 80, 55, "tracker"], [81, 64, 80, 62], [81, 65, 80, 63], [82, 6, 82, 4], [82, 10, 82, 8], [82, 14, 82, 12], [82, 15, 82, 13, "state"], [82, 20, 82, 18], [82, 25, 82, 23, "State"], [82, 37, 82, 28], [82, 38, 82, 29, "ACTIVE"], [82, 44, 82, 35], [82, 46, 82, 37], [83, 8, 83, 6], [83, 12, 83, 10], [83, 13, 83, 11, "end"], [83, 16, 83, 14], [83, 17, 83, 15], [83, 18, 83, 16], [84, 6, 84, 4], [84, 7, 84, 5], [84, 13, 84, 11], [85, 8, 85, 6], [85, 12, 85, 10], [85, 13, 85, 11, "fail"], [85, 17, 85, 15], [85, 18, 85, 16], [85, 19, 85, 17], [86, 6, 86, 4], [87, 4, 87, 2], [88, 4, 89, 2, "onPointerRemove"], [88, 19, 89, 17, "onPointerRemove"], [88, 20, 89, 18, "event"], [88, 25, 89, 23], [88, 27, 89, 25], [89, 6, 90, 4], [89, 11, 90, 9], [89, 12, 90, 10, "onPointerRemove"], [89, 27, 90, 25], [89, 28, 90, 26, "event"], [89, 33, 90, 31], [89, 34, 90, 32], [90, 6, 91, 4], [90, 10, 91, 8], [90, 11, 91, 9, "scaleGestureDetector"], [90, 31, 91, 29], [90, 32, 91, 30, "onTouchEvent"], [90, 44, 91, 42], [90, 45, 91, 43, "event"], [90, 50, 91, 48], [90, 52, 91, 50], [90, 56, 91, 54], [90, 57, 91, 55, "tracker"], [90, 64, 91, 62], [90, 65, 91, 63], [91, 6, 92, 4], [91, 10, 92, 8], [91, 11, 92, 9, "tracker"], [91, 18, 92, 16], [91, 19, 92, 17, "removeFromTracker"], [91, 36, 92, 34], [91, 37, 92, 35, "event"], [91, 42, 92, 40], [91, 43, 92, 41, "pointerId"], [91, 52, 92, 50], [91, 53, 92, 51], [92, 6, 94, 4], [92, 10, 94, 8], [92, 14, 94, 12], [92, 15, 94, 13, "state"], [92, 20, 94, 18], [92, 25, 94, 23, "State"], [92, 37, 94, 28], [92, 38, 94, 29, "ACTIVE"], [92, 44, 94, 35], [92, 48, 94, 39], [92, 52, 94, 43], [92, 53, 94, 44, "tracker"], [92, 60, 94, 51], [92, 61, 94, 52, "trackedPointersCount"], [92, 81, 94, 72], [92, 84, 94, 75], [92, 85, 94, 76], [92, 87, 94, 78], [93, 8, 95, 6], [93, 12, 95, 10], [93, 13, 95, 11, "end"], [93, 16, 95, 14], [93, 17, 95, 15], [93, 18, 95, 16], [94, 6, 96, 4], [95, 4, 97, 2], [96, 4, 99, 2, "onPointerMove"], [96, 17, 99, 15, "onPointerMove"], [96, 18, 99, 16, "event"], [96, 23, 99, 21], [96, 25, 99, 23], [97, 6, 100, 4], [97, 10, 100, 8], [97, 14, 100, 12], [97, 15, 100, 13, "tracker"], [97, 22, 100, 20], [97, 23, 100, 21, "trackedPointersCount"], [97, 43, 100, 41], [97, 46, 100, 44], [97, 47, 100, 45], [97, 49, 100, 47], [98, 8, 101, 6], [99, 6, 102, 4], [100, 6, 104, 4], [100, 10, 104, 8], [100, 11, 104, 9, "tracker"], [100, 18, 104, 16], [100, 19, 104, 17, "track"], [100, 24, 104, 22], [100, 25, 104, 23, "event"], [100, 30, 104, 28], [100, 31, 104, 29], [101, 6, 105, 4], [101, 10, 105, 8], [101, 11, 105, 9, "scaleGestureDetector"], [101, 31, 105, 29], [101, 32, 105, 30, "onTouchEvent"], [101, 44, 105, 42], [101, 45, 105, 43, "event"], [101, 50, 105, 48], [101, 52, 105, 50], [101, 56, 105, 54], [101, 57, 105, 55, "tracker"], [101, 64, 105, 62], [101, 65, 105, 63], [102, 6, 106, 4], [102, 11, 106, 9], [102, 12, 106, 10, "onPointerMove"], [102, 25, 106, 23], [102, 26, 106, 24, "event"], [102, 31, 106, 29], [102, 32, 106, 30], [103, 4, 107, 2], [104, 4, 109, 2, "onPointerOutOfBounds"], [104, 24, 109, 22, "onPointerOutOfBounds"], [104, 25, 109, 23, "event"], [104, 30, 109, 28], [104, 32, 109, 30], [105, 6, 110, 4], [105, 10, 110, 8], [105, 14, 110, 12], [105, 15, 110, 13, "tracker"], [105, 22, 110, 20], [105, 23, 110, 21, "trackedPointersCount"], [105, 43, 110, 41], [105, 46, 110, 44], [105, 47, 110, 45], [105, 49, 110, 47], [106, 8, 111, 6], [107, 6, 112, 4], [108, 6, 114, 4], [108, 10, 114, 8], [108, 11, 114, 9, "tracker"], [108, 18, 114, 16], [108, 19, 114, 17, "track"], [108, 24, 114, 22], [108, 25, 114, 23, "event"], [108, 30, 114, 28], [108, 31, 114, 29], [109, 6, 115, 4], [109, 10, 115, 8], [109, 11, 115, 9, "scaleGestureDetector"], [109, 31, 115, 29], [109, 32, 115, 30, "onTouchEvent"], [109, 44, 115, 42], [109, 45, 115, 43, "event"], [109, 50, 115, 48], [109, 52, 115, 50], [109, 56, 115, 54], [109, 57, 115, 55, "tracker"], [109, 64, 115, 62], [109, 65, 115, 63], [110, 6, 116, 4], [110, 11, 116, 9], [110, 12, 116, 10, "onPointerOutOfBounds"], [110, 32, 116, 30], [110, 33, 116, 31, "event"], [110, 38, 116, 36], [110, 39, 116, 37], [111, 4, 117, 2], [112, 4, 119, 2, "tryBegin"], [112, 12, 119, 10, "tryBegin"], [112, 13, 119, 10], [112, 15, 119, 13], [113, 6, 120, 4], [113, 10, 120, 8], [113, 14, 120, 12], [113, 15, 120, 13, "state"], [113, 20, 120, 18], [113, 25, 120, 23, "State"], [113, 37, 120, 28], [113, 38, 120, 29, "UNDETERMINED"], [113, 50, 120, 41], [113, 52, 120, 43], [114, 8, 121, 6], [115, 6, 122, 4], [116, 6, 124, 4], [116, 10, 124, 8], [116, 11, 124, 9, "resetProgress"], [116, 24, 124, 22], [116, 25, 124, 23], [116, 26, 124, 24], [117, 6, 125, 4], [117, 10, 125, 8], [117, 11, 125, 9, "begin"], [117, 16, 125, 14], [117, 17, 125, 15], [117, 18, 125, 16], [118, 4, 126, 2], [119, 4, 128, 2, "activate"], [119, 12, 128, 10, "activate"], [119, 13, 128, 11, "force"], [119, 18, 128, 16], [119, 20, 128, 18], [120, 6, 129, 4], [120, 10, 129, 8], [120, 14, 129, 12], [120, 15, 129, 13, "state"], [120, 20, 129, 18], [120, 25, 129, 23, "State"], [120, 37, 129, 28], [120, 38, 129, 29, "ACTIVE"], [120, 44, 129, 35], [120, 46, 129, 37], [121, 8, 130, 6], [121, 12, 130, 10], [121, 13, 130, 11, "resetProgress"], [121, 26, 130, 24], [121, 27, 130, 25], [121, 28, 130, 26], [122, 6, 131, 4], [123, 6, 133, 4], [123, 11, 133, 9], [123, 12, 133, 10, "activate"], [123, 20, 133, 18], [123, 21, 133, 19, "force"], [123, 26, 133, 24], [123, 27, 133, 25], [124, 4, 134, 2], [125, 4, 136, 2, "onReset"], [125, 11, 136, 9, "onReset"], [125, 12, 136, 9], [125, 14, 136, 12], [126, 6, 137, 4], [126, 10, 137, 8], [126, 11, 137, 9, "resetProgress"], [126, 24, 137, 22], [126, 25, 137, 23], [126, 26, 137, 24], [127, 4, 138, 2], [128, 4, 140, 2, "resetProgress"], [128, 17, 140, 15, "resetProgress"], [128, 18, 140, 15], [128, 20, 140, 18], [129, 6, 141, 4], [129, 10, 141, 8], [129, 14, 141, 12], [129, 15, 141, 13, "state"], [129, 20, 141, 18], [129, 25, 141, 23, "State"], [129, 37, 141, 28], [129, 38, 141, 29, "ACTIVE"], [129, 44, 141, 35], [129, 46, 141, 37], [130, 8, 142, 6], [131, 6, 143, 4], [132, 6, 145, 4], [132, 10, 145, 8], [132, 11, 145, 9, "velocity"], [132, 19, 145, 17], [132, 22, 145, 20], [132, 23, 145, 21], [133, 6, 146, 4], [133, 10, 146, 8], [133, 11, 146, 9, "scale"], [133, 16, 146, 14], [133, 19, 146, 17], [133, 20, 146, 18], [134, 4, 147, 2], [135, 2, 149, 0], [136, 2, 149, 1, "exports"], [136, 9, 149, 1], [136, 10, 149, 1, "default"], [136, 17, 149, 1], [136, 20, 149, 1, "PinchGestureHandler"], [136, 39, 149, 1], [137, 0, 149, 1], [137, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "PinchGestureHandler", "constructor", "_defineProperty$argument_2.onScaleBegin", "_defineProperty$argument_2.onScale", "_defineProperty$argument_2.onScaleEnd", "init", "transformNativeEvent", "onPointerDown", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerOutOfBounds", "tryBegin", "activate", "onReset", "resetProgress"], "mappings": "AAA,iNC;eCM;ECC;oBCY;ODG;eEC;OFc;kBGC,eH;GDI;EKE;GLG;EME;GNO;EOE;GPI;EQE;GRK;ESE;GTe;EUE;GVQ;EWE;GXQ;EYE;GZQ;EaE;GbO;EcE;GdM;EeE;GfE;EgBE;GhBO;CDE"}}, "type": "js/module"}]}