{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../UIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 37, "index": 218}}], "key": "QEvI6Qp5yj0uKHcpJuhn6T7mPD8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _UIManager = _interopRequireDefault(require(_dependencyMap[1], \"../UIManager\"));\n  /**\n   * Copyright (c) <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  // NativeModules shim\n  var NativeModules = {\n    UIManager: _UIManager.default\n  };\n  var _default = exports.default = NativeModules;\n});", "lineCount": 22, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_UIManager"], [7, 16, 10, 0], [7, 19, 10, 0, "_interopRequireDefault"], [7, 41, 10, 0], [7, 42, 10, 0, "require"], [7, 49, 10, 0], [7, 50, 10, 0, "_dependencyMap"], [7, 64, 10, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [17, 2, 12, 0], [18, 2, 13, 0], [18, 6, 13, 4, "NativeModules"], [18, 19, 13, 17], [18, 22, 13, 20], [19, 4, 14, 2, "UIManager"], [19, 13, 14, 11], [19, 15, 14, 2, "UIManager"], [20, 2, 15, 0], [20, 3, 15, 1], [21, 2, 15, 2], [21, 6, 15, 2, "_default"], [21, 14, 15, 2], [21, 17, 15, 2, "exports"], [21, 24, 15, 2], [21, 25, 15, 2, "default"], [21, 32, 15, 2], [21, 35, 16, 15, "NativeModules"], [21, 48, 16, 28], [22, 0, 16, 28], [22, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}