{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 67, "index": 82}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 83}, "end": {"line": 4, "column": 26, "index": 109}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 110}, "end": {"line": 5, "column": 31, "index": 141}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 211}, "end": {"line": 7, "column": 67, "index": 278}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "../assets/search-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 279}, "end": {"line": 8, "column": 51, "index": 330}}], "key": "ai40rVaAzolPoKDrCo7kH+CIoHU=", "exportNames": ["*"]}}, {"name": "../useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 331}, "end": {"line": 9, "column": 50, "index": 381}}], "key": "7taeXB3nt1BpdUmcfwNb365E1ko=", "exportNames": ["*"]}}, {"name": "./getDefaultHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 382}, "end": {"line": 10, "column": 69, "index": 451}}], "key": "XS+pdXfrnHFs4TugUxV9atNlm5I=", "exportNames": ["*"]}}, {"name": "./HeaderBackButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 452}, "end": {"line": 11, "column": 57, "index": 509}}], "key": "4bKq+NNcBdtVVEx2kV4uO9wAEtk=", "exportNames": ["*"]}}, {"name": "./HeaderBackground.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 510}, "end": {"line": 12, "column": 57, "index": 567}}], "key": "ye9cJnmaS+9XPtNY5wHFS+fPFuI=", "exportNames": ["*"]}}, {"name": "./HeaderButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 568}, "end": {"line": 13, "column": 49, "index": 617}}], "key": "5Mfp2bWqztZ2HFy80uJBbvbN6HA=", "exportNames": ["*"]}}, {"name": "./HeaderIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 618}, "end": {"line": 14, "column": 45, "index": 663}}], "key": "0JPASIZzwd0DulPaj/kDrorllj8=", "exportNames": ["*"]}}, {"name": "./HeaderSearchBar.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 664}, "end": {"line": 15, "column": 55, "index": 719}}], "key": "vzM0SWN0jKogpTdmzlNqPMFwJYo=", "exportNames": ["*"]}}, {"name": "./HeaderShownContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 720}, "end": {"line": 16, "column": 61, "index": 781}}], "key": "dJPbJxjIRPcLd4c1Az+chGzNyds=", "exportNames": ["*"]}}, {"name": "./HeaderTitle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 782}, "end": {"line": 17, "column": 47, "index": 829}}], "key": "EVm/qUR3ximiZhC6CgHKasWhi6k=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 900}, "end": {"line": 20, "column": 86, "index": 986}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Header = Header;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[2], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Animated\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/View\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[8], \"react-native-safe-area-context\");\n  var _searchIcon = _interopRequireDefault(require(_dependencyMap[9], \"../assets/search-icon.png\"));\n  var _useFrameSize = require(_dependencyMap[10], \"../useFrameSize.js\");\n  var _getDefaultHeaderHeight = require(_dependencyMap[11], \"./getDefaultHeaderHeight.js\");\n  var _HeaderBackButton = require(_dependencyMap[12], \"./HeaderBackButton.js\");\n  var _HeaderBackground = require(_dependencyMap[13], \"./HeaderBackground.js\");\n  var _HeaderButton = require(_dependencyMap[14], \"./HeaderButton.js\");\n  var _HeaderIcon = require(_dependencyMap[15], \"./HeaderIcon.js\");\n  var _HeaderSearchBar = require(_dependencyMap[16], \"./HeaderSearchBar.js\");\n  var _HeaderShownContext = require(_dependencyMap[17], \"./HeaderShownContext.js\");\n  var _HeaderTitle = require(_dependencyMap[18], \"./HeaderTitle.js\");\n  var _jsxRuntime = require(_dependencyMap[19], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  // Width of the screen in split layout on portrait mode on iPad Mini\n\n  const IPAD_MINI_MEDIUM_WIDTH = 414;\n  const warnIfHeaderStylesDefined = styles => {\n    Object.keys(styles).forEach(styleProp => {\n      const value = styles[styleProp];\n      if (styleProp === 'position' && value === 'absolute') {\n        console.warn(\"position: 'absolute' is not supported on headerStyle. If you would like to render content under the header, use the 'headerTransparent' option.\");\n      } else if (value !== undefined) {\n        console.warn(`${styleProp} was given a value of ${value}, this has no effect on headerStyle.`);\n      }\n    });\n  };\n  function Header(props) {\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const frame = (0, _useFrameSize.useFrameSize)(size => size, true);\n    const {\n      colors\n    } = (0, _native.useTheme)();\n    const navigation = (0, _native.useNavigation)();\n    const isParentHeaderShown = React.useContext(_HeaderShownContext.HeaderShownContext);\n    const [searchBarVisible, setSearchBarVisible] = React.useState(false);\n    const [titleLayout, setTitleLayout] = React.useState(undefined);\n    const onTitleLayout = e => {\n      const {\n        height,\n        width\n      } = e.nativeEvent.layout;\n      setTitleLayout(titleLayout => {\n        if (titleLayout && height === titleLayout.height && width === titleLayout.width) {\n          return titleLayout;\n        }\n        return {\n          height,\n          width\n        };\n      });\n    };\n    const {\n      layout = frame,\n      modal = false,\n      back,\n      title,\n      headerTitle: customTitle,\n      headerTitleAlign = _Platform.default.OS === 'ios' ? 'center' : 'left',\n      headerLeft = back ? props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderBackButton.HeaderBackButton, {\n        ...props\n      }) : undefined,\n      headerSearchBarOptions,\n      headerTransparent,\n      headerTintColor,\n      headerBackground,\n      headerRight,\n      headerTitleAllowFontScaling: titleAllowFontScaling,\n      headerTitleStyle: titleStyle,\n      headerLeftContainerStyle: leftContainerStyle,\n      headerRightContainerStyle: rightContainerStyle,\n      headerTitleContainerStyle: titleContainerStyle,\n      headerBackButtonDisplayMode = _Platform.default.OS === 'ios' ? 'default' : 'minimal',\n      headerBackTitleStyle,\n      headerBackgroundContainerStyle: backgroundContainerStyle,\n      headerStyle: customHeaderStyle,\n      headerShadowVisible,\n      headerPressColor,\n      headerPressOpacity,\n      headerStatusBarHeight = isParentHeaderShown ? 0 : insets.top\n    } = props;\n    const defaultHeight = (0, _getDefaultHeaderHeight.getDefaultHeaderHeight)(layout, modal, headerStatusBarHeight);\n    const {\n      height = defaultHeight,\n      minHeight,\n      maxHeight,\n      backgroundColor,\n      borderBottomColor,\n      borderBottomEndRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius,\n      borderBottomStartRadius,\n      borderBottomWidth,\n      borderColor,\n      borderEndColor,\n      borderEndWidth,\n      borderLeftColor,\n      borderLeftWidth,\n      borderRadius,\n      borderRightColor,\n      borderRightWidth,\n      borderStartColor,\n      borderStartWidth,\n      borderStyle,\n      borderTopColor,\n      borderTopEndRadius,\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderTopStartRadius,\n      borderTopWidth,\n      borderWidth,\n      boxShadow,\n      elevation,\n      shadowColor,\n      shadowOffset,\n      shadowOpacity,\n      shadowRadius,\n      opacity,\n      transform,\n      ...unsafeStyles\n    } = _StyleSheet.default.flatten(customHeaderStyle || {});\n    if (process.env.NODE_ENV !== 'production') {\n      warnIfHeaderStylesDefined(unsafeStyles);\n    }\n    const safeStyles = {\n      backgroundColor,\n      borderBottomColor,\n      borderBottomEndRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius,\n      borderBottomStartRadius,\n      borderBottomWidth,\n      borderColor,\n      borderEndColor,\n      borderEndWidth,\n      borderLeftColor,\n      borderLeftWidth,\n      borderRadius,\n      borderRightColor,\n      borderRightWidth,\n      borderStartColor,\n      borderStartWidth,\n      borderStyle,\n      borderTopColor,\n      borderTopEndRadius,\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderTopStartRadius,\n      borderTopWidth,\n      borderWidth,\n      boxShadow,\n      elevation,\n      shadowColor,\n      shadowOffset,\n      shadowOpacity,\n      shadowRadius,\n      opacity,\n      transform\n    };\n\n    // Setting a property to undefined triggers default style\n    // So we need to filter them out\n    // Users can use `null` instead\n    for (const styleProp in safeStyles) {\n      // @ts-expect-error: typescript wrongly complains that styleProp cannot be used to index safeStyles\n      if (safeStyles[styleProp] === undefined) {\n        // @ts-expect-error don't need to care about index signature for deletion\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete safeStyles[styleProp];\n      }\n    }\n    const backgroundStyle = {\n      ...(headerTransparent && {\n        backgroundColor: 'transparent'\n      }),\n      ...((headerTransparent || headerShadowVisible === false) && {\n        borderBottomWidth: 0,\n        ..._Platform.default.select({\n          android: {\n            elevation: 0\n          },\n          web: {\n            boxShadow: 'none'\n          },\n          default: {\n            shadowOpacity: 0\n          }\n        })\n      }),\n      ...safeStyles\n    };\n    const iconTintColor = headerTintColor ?? _Platform.default.select({\n      ios: colors.primary,\n      default: colors.text\n    });\n    const leftButton = headerLeft ? headerLeft({\n      tintColor: iconTintColor,\n      pressColor: headerPressColor,\n      pressOpacity: headerPressOpacity,\n      displayMode: headerBackButtonDisplayMode,\n      titleLayout,\n      screenLayout: layout,\n      canGoBack: Boolean(back),\n      onPress: back ? navigation.goBack : undefined,\n      label: back?.title,\n      labelStyle: headerBackTitleStyle,\n      href: back?.href\n    }) : null;\n    const rightButton = headerRight ? headerRight({\n      tintColor: iconTintColor,\n      pressColor: headerPressColor,\n      pressOpacity: headerPressOpacity,\n      canGoBack: Boolean(back)\n    }) : null;\n    const headerTitle = typeof customTitle !== 'function' ? props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderTitle.HeaderTitle, {\n      ...props\n    }) : customTitle;\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_Animated.default.View, {\n      pointerEvents: \"box-none\",\n      style: [{\n        height,\n        minHeight,\n        maxHeight,\n        opacity,\n        transform\n      }],\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.View, {\n        pointerEvents: \"box-none\",\n        style: [_StyleSheet.default.absoluteFill, backgroundContainerStyle],\n        children: headerBackground ? headerBackground({\n          style: backgroundStyle\n        }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderBackground.HeaderBackground, {\n          pointerEvents:\n          // Allow touch through the header when background color is transparent\n          headerTransparent && (backgroundStyle.backgroundColor === 'transparent' || (0, _color.default)(backgroundStyle.backgroundColor).alpha() === 0) ? 'none' : 'auto',\n          style: backgroundStyle\n        })\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n        pointerEvents: \"none\",\n        style: {\n          height: headerStatusBarHeight\n        }\n      }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_View.default, {\n        pointerEvents: \"box-none\",\n        style: [styles.content, _Platform.default.OS === 'ios' && frame.width >= IPAD_MINI_MEDIUM_WIDTH ? styles.large : null],\n        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.View, {\n          pointerEvents: \"box-none\",\n          style: [styles.start, !searchBarVisible && headerTitleAlign === 'center' && styles.expand, {\n            marginStart: insets.left\n          }, leftContainerStyle],\n          children: leftButton\n        }), _Platform.default.OS === 'ios' || !searchBarVisible ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.View, {\n            pointerEvents: \"box-none\",\n            style: [styles.title, {\n              // Avoid the title from going offscreen or overlapping buttons\n              maxWidth: headerTitleAlign === 'center' ? layout.width - ((leftButton ? headerBackButtonDisplayMode !== 'minimal' ? 80 : 32 : 16) + (rightButton || headerSearchBarOptions ? 16 : 0) + Math.max(insets.left, insets.right)) * 2 : layout.width - ((leftButton ? 52 : 16) + (rightButton || headerSearchBarOptions ? 52 : 16) + insets.left - insets.right)\n            }, headerTitleAlign === 'left' && leftButton ? {\n              marginStart: 4\n            } : {\n              marginHorizontal: 16\n            }, titleContainerStyle],\n            children: headerTitle({\n              children: title,\n              allowFontScaling: titleAllowFontScaling,\n              tintColor: headerTintColor,\n              onLayout: onTitleLayout,\n              style: titleStyle\n            })\n          }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_Animated.default.View, {\n            pointerEvents: \"box-none\",\n            style: [styles.end, styles.expand, {\n              marginEnd: insets.right\n            }, rightContainerStyle],\n            children: [rightButton, headerSearchBarOptions ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderButton.HeaderButton, {\n              tintColor: iconTintColor,\n              pressColor: headerPressColor,\n              pressOpacity: headerPressOpacity,\n              onPress: () => {\n                setSearchBarVisible(true);\n                headerSearchBarOptions?.onOpen?.();\n              },\n              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {\n                source: _searchIcon.default,\n                tintColor: iconTintColor\n              })\n            }) : null]\n          })]\n        }) : null, _Platform.default.OS === 'ios' || searchBarVisible ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderSearchBar.HeaderSearchBar, {\n          ...headerSearchBarOptions,\n          visible: searchBarVisible,\n          onClose: () => {\n            setSearchBarVisible(false);\n            headerSearchBarOptions?.onClose?.();\n          },\n          tintColor: headerTintColor,\n          style: [_Platform.default.OS === 'ios' ? [_StyleSheet.default.absoluteFill, {\n            paddingTop: headerStatusBarHeight ? 0 : 4\n          }, {\n            backgroundColor: backgroundColor ?? colors.card\n          }] : !leftButton && {\n            marginStart: 8\n          }]\n        }) : null]\n      })]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    content: {\n      flex: 1,\n      flexDirection: 'row',\n      alignItems: 'stretch'\n    },\n    large: {\n      marginHorizontal: 5\n    },\n    title: {\n      justifyContent: 'center'\n    },\n    start: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'flex-start'\n    },\n    end: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      justifyContent: 'flex-end'\n    },\n    expand: {\n      flexGrow: 1,\n      flexBasis: 0\n    }\n  });\n});", "lineCount": 349, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Header"], [8, 16, 1, 13], [8, 19, 1, 13, "Header"], [8, 25, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_color"], [10, 12, 4, 0], [10, 15, 4, 0, "_interopRequireDefault"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "React"], [11, 11, 5, 0], [11, 14, 5, 0, "_interopRequireWildcard"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 5, 31], [12, 6, 5, 31, "_Animated"], [12, 15, 5, 31], [12, 18, 5, 31, "_interopRequireDefault"], [12, 40, 5, 31], [12, 41, 5, 31, "require"], [12, 48, 5, 31], [12, 49, 5, 31, "_dependencyMap"], [12, 63, 5, 31], [13, 2, 5, 31], [13, 6, 5, 31, "_Platform"], [13, 15, 5, 31], [13, 18, 5, 31, "_interopRequireDefault"], [13, 40, 5, 31], [13, 41, 5, 31, "require"], [13, 48, 5, 31], [13, 49, 5, 31, "_dependencyMap"], [13, 63, 5, 31], [14, 2, 5, 31], [14, 6, 5, 31, "_StyleSheet"], [14, 17, 5, 31], [14, 20, 5, 31, "_interopRequireDefault"], [14, 42, 5, 31], [14, 43, 5, 31, "require"], [14, 50, 5, 31], [14, 51, 5, 31, "_dependencyMap"], [14, 65, 5, 31], [15, 2, 5, 31], [15, 6, 5, 31, "_View"], [15, 11, 5, 31], [15, 14, 5, 31, "_interopRequireDefault"], [15, 36, 5, 31], [15, 37, 5, 31, "require"], [15, 44, 5, 31], [15, 45, 5, 31, "_dependencyMap"], [15, 59, 5, 31], [16, 2, 7, 0], [16, 6, 7, 0, "_reactNativeSafeAreaContext"], [16, 33, 7, 0], [16, 36, 7, 0, "require"], [16, 43, 7, 0], [16, 44, 7, 0, "_dependencyMap"], [16, 58, 7, 0], [17, 2, 8, 0], [17, 6, 8, 0, "_searchIcon"], [17, 17, 8, 0], [17, 20, 8, 0, "_interopRequireDefault"], [17, 42, 8, 0], [17, 43, 8, 0, "require"], [17, 50, 8, 0], [17, 51, 8, 0, "_dependencyMap"], [17, 65, 8, 0], [18, 2, 9, 0], [18, 6, 9, 0, "_useFrameSize"], [18, 19, 9, 0], [18, 22, 9, 0, "require"], [18, 29, 9, 0], [18, 30, 9, 0, "_dependencyMap"], [18, 44, 9, 0], [19, 2, 10, 0], [19, 6, 10, 0, "_getDefaultHeaderHeight"], [19, 29, 10, 0], [19, 32, 10, 0, "require"], [19, 39, 10, 0], [19, 40, 10, 0, "_dependencyMap"], [19, 54, 10, 0], [20, 2, 11, 0], [20, 6, 11, 0, "_HeaderBackButton"], [20, 23, 11, 0], [20, 26, 11, 0, "require"], [20, 33, 11, 0], [20, 34, 11, 0, "_dependencyMap"], [20, 48, 11, 0], [21, 2, 12, 0], [21, 6, 12, 0, "_HeaderBackground"], [21, 23, 12, 0], [21, 26, 12, 0, "require"], [21, 33, 12, 0], [21, 34, 12, 0, "_dependencyMap"], [21, 48, 12, 0], [22, 2, 13, 0], [22, 6, 13, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [22, 19, 13, 0], [22, 22, 13, 0, "require"], [22, 29, 13, 0], [22, 30, 13, 0, "_dependencyMap"], [22, 44, 13, 0], [23, 2, 14, 0], [23, 6, 14, 0, "_HeaderIcon"], [23, 17, 14, 0], [23, 20, 14, 0, "require"], [23, 27, 14, 0], [23, 28, 14, 0, "_dependencyMap"], [23, 42, 14, 0], [24, 2, 15, 0], [24, 6, 15, 0, "_HeaderSear<PERSON>B<PERSON>"], [24, 22, 15, 0], [24, 25, 15, 0, "require"], [24, 32, 15, 0], [24, 33, 15, 0, "_dependencyMap"], [24, 47, 15, 0], [25, 2, 16, 0], [25, 6, 16, 0, "_HeaderShownContext"], [25, 25, 16, 0], [25, 28, 16, 0, "require"], [25, 35, 16, 0], [25, 36, 16, 0, "_dependencyMap"], [25, 50, 16, 0], [26, 2, 17, 0], [26, 6, 17, 0, "_Header<PERSON>itle"], [26, 18, 17, 0], [26, 21, 17, 0, "require"], [26, 28, 17, 0], [26, 29, 17, 0, "_dependencyMap"], [26, 43, 17, 0], [27, 2, 20, 0], [27, 6, 20, 0, "_jsxRuntime"], [27, 17, 20, 0], [27, 20, 20, 0, "require"], [27, 27, 20, 0], [27, 28, 20, 0, "_dependencyMap"], [27, 42, 20, 0], [28, 2, 20, 86], [28, 11, 20, 86, "_interopRequireWildcard"], [28, 35, 20, 86, "e"], [28, 36, 20, 86], [28, 38, 20, 86, "t"], [28, 39, 20, 86], [28, 68, 20, 86, "WeakMap"], [28, 75, 20, 86], [28, 81, 20, 86, "r"], [28, 82, 20, 86], [28, 89, 20, 86, "WeakMap"], [28, 96, 20, 86], [28, 100, 20, 86, "n"], [28, 101, 20, 86], [28, 108, 20, 86, "WeakMap"], [28, 115, 20, 86], [28, 127, 20, 86, "_interopRequireWildcard"], [28, 150, 20, 86], [28, 162, 20, 86, "_interopRequireWildcard"], [28, 163, 20, 86, "e"], [28, 164, 20, 86], [28, 166, 20, 86, "t"], [28, 167, 20, 86], [28, 176, 20, 86, "t"], [28, 177, 20, 86], [28, 181, 20, 86, "e"], [28, 182, 20, 86], [28, 186, 20, 86, "e"], [28, 187, 20, 86], [28, 188, 20, 86, "__esModule"], [28, 198, 20, 86], [28, 207, 20, 86, "e"], [28, 208, 20, 86], [28, 214, 20, 86, "o"], [28, 215, 20, 86], [28, 217, 20, 86, "i"], [28, 218, 20, 86], [28, 220, 20, 86, "f"], [28, 221, 20, 86], [28, 226, 20, 86, "__proto__"], [28, 235, 20, 86], [28, 243, 20, 86, "default"], [28, 250, 20, 86], [28, 252, 20, 86, "e"], [28, 253, 20, 86], [28, 270, 20, 86, "e"], [28, 271, 20, 86], [28, 294, 20, 86, "e"], [28, 295, 20, 86], [28, 320, 20, 86, "e"], [28, 321, 20, 86], [28, 330, 20, 86, "f"], [28, 331, 20, 86], [28, 337, 20, 86, "o"], [28, 338, 20, 86], [28, 341, 20, 86, "t"], [28, 342, 20, 86], [28, 345, 20, 86, "n"], [28, 346, 20, 86], [28, 349, 20, 86, "r"], [28, 350, 20, 86], [28, 358, 20, 86, "o"], [28, 359, 20, 86], [28, 360, 20, 86, "has"], [28, 363, 20, 86], [28, 364, 20, 86, "e"], [28, 365, 20, 86], [28, 375, 20, 86, "o"], [28, 376, 20, 86], [28, 377, 20, 86, "get"], [28, 380, 20, 86], [28, 381, 20, 86, "e"], [28, 382, 20, 86], [28, 385, 20, 86, "o"], [28, 386, 20, 86], [28, 387, 20, 86, "set"], [28, 390, 20, 86], [28, 391, 20, 86, "e"], [28, 392, 20, 86], [28, 394, 20, 86, "f"], [28, 395, 20, 86], [28, 411, 20, 86, "t"], [28, 412, 20, 86], [28, 416, 20, 86, "e"], [28, 417, 20, 86], [28, 433, 20, 86, "t"], [28, 434, 20, 86], [28, 441, 20, 86, "hasOwnProperty"], [28, 455, 20, 86], [28, 456, 20, 86, "call"], [28, 460, 20, 86], [28, 461, 20, 86, "e"], [28, 462, 20, 86], [28, 464, 20, 86, "t"], [28, 465, 20, 86], [28, 472, 20, 86, "i"], [28, 473, 20, 86], [28, 477, 20, 86, "o"], [28, 478, 20, 86], [28, 481, 20, 86, "Object"], [28, 487, 20, 86], [28, 488, 20, 86, "defineProperty"], [28, 502, 20, 86], [28, 507, 20, 86, "Object"], [28, 513, 20, 86], [28, 514, 20, 86, "getOwnPropertyDescriptor"], [28, 538, 20, 86], [28, 539, 20, 86, "e"], [28, 540, 20, 86], [28, 542, 20, 86, "t"], [28, 543, 20, 86], [28, 550, 20, 86, "i"], [28, 551, 20, 86], [28, 552, 20, 86, "get"], [28, 555, 20, 86], [28, 559, 20, 86, "i"], [28, 560, 20, 86], [28, 561, 20, 86, "set"], [28, 564, 20, 86], [28, 568, 20, 86, "o"], [28, 569, 20, 86], [28, 570, 20, 86, "f"], [28, 571, 20, 86], [28, 573, 20, 86, "t"], [28, 574, 20, 86], [28, 576, 20, 86, "i"], [28, 577, 20, 86], [28, 581, 20, 86, "f"], [28, 582, 20, 86], [28, 583, 20, 86, "t"], [28, 584, 20, 86], [28, 588, 20, 86, "e"], [28, 589, 20, 86], [28, 590, 20, 86, "t"], [28, 591, 20, 86], [28, 602, 20, 86, "f"], [28, 603, 20, 86], [28, 608, 20, 86, "e"], [28, 609, 20, 86], [28, 611, 20, 86, "t"], [28, 612, 20, 86], [29, 2, 19, 0], [31, 2, 21, 0], [31, 8, 21, 6, "IPAD_MINI_MEDIUM_WIDTH"], [31, 30, 21, 28], [31, 33, 21, 31], [31, 36, 21, 34], [32, 2, 22, 0], [32, 8, 22, 6, "warnIfHeaderStylesDefined"], [32, 33, 22, 31], [32, 36, 22, 34, "styles"], [32, 42, 22, 40], [32, 46, 22, 44], [33, 4, 23, 2, "Object"], [33, 10, 23, 8], [33, 11, 23, 9, "keys"], [33, 15, 23, 13], [33, 16, 23, 14, "styles"], [33, 22, 23, 20], [33, 23, 23, 21], [33, 24, 23, 22, "for<PERSON>ach"], [33, 31, 23, 29], [33, 32, 23, 30, "styleProp"], [33, 41, 23, 39], [33, 45, 23, 43], [34, 6, 24, 4], [34, 12, 24, 10, "value"], [34, 17, 24, 15], [34, 20, 24, 18, "styles"], [34, 26, 24, 24], [34, 27, 24, 25, "styleProp"], [34, 36, 24, 34], [34, 37, 24, 35], [35, 6, 25, 4], [35, 10, 25, 8, "styleProp"], [35, 19, 25, 17], [35, 24, 25, 22], [35, 34, 25, 32], [35, 38, 25, 36, "value"], [35, 43, 25, 41], [35, 48, 25, 46], [35, 58, 25, 56], [35, 60, 25, 58], [36, 8, 26, 6, "console"], [36, 15, 26, 13], [36, 16, 26, 14, "warn"], [36, 20, 26, 18], [36, 21, 26, 19], [36, 166, 26, 164], [36, 167, 26, 165], [37, 6, 27, 4], [37, 7, 27, 5], [37, 13, 27, 11], [37, 17, 27, 15, "value"], [37, 22, 27, 20], [37, 27, 27, 25, "undefined"], [37, 36, 27, 34], [37, 38, 27, 36], [38, 8, 28, 6, "console"], [38, 15, 28, 13], [38, 16, 28, 14, "warn"], [38, 20, 28, 18], [38, 21, 28, 19], [38, 24, 28, 22, "styleProp"], [38, 33, 28, 31], [38, 58, 28, 56, "value"], [38, 63, 28, 61], [38, 101, 28, 99], [38, 102, 28, 100], [39, 6, 29, 4], [40, 4, 30, 2], [40, 5, 30, 3], [40, 6, 30, 4], [41, 2, 31, 0], [41, 3, 31, 1], [42, 2, 32, 7], [42, 11, 32, 16, "Header"], [42, 17, 32, 22, "Header"], [42, 18, 32, 23, "props"], [42, 23, 32, 28], [42, 25, 32, 30], [43, 4, 33, 2], [43, 10, 33, 8, "insets"], [43, 16, 33, 14], [43, 19, 33, 17], [43, 23, 33, 17, "useSafeAreaInsets"], [43, 68, 33, 34], [43, 70, 33, 35], [43, 71, 33, 36], [44, 4, 34, 2], [44, 10, 34, 8, "frame"], [44, 15, 34, 13], [44, 18, 34, 16], [44, 22, 34, 16, "useFrameSize"], [44, 48, 34, 28], [44, 50, 34, 29, "size"], [44, 54, 34, 33], [44, 58, 34, 37, "size"], [44, 62, 34, 41], [44, 64, 34, 43], [44, 68, 34, 47], [44, 69, 34, 48], [45, 4, 35, 2], [45, 10, 35, 8], [46, 6, 36, 4, "colors"], [47, 4, 37, 2], [47, 5, 37, 3], [47, 8, 37, 6], [47, 12, 37, 6, "useTheme"], [47, 28, 37, 14], [47, 30, 37, 15], [47, 31, 37, 16], [48, 4, 38, 2], [48, 10, 38, 8, "navigation"], [48, 20, 38, 18], [48, 23, 38, 21], [48, 27, 38, 21, "useNavigation"], [48, 48, 38, 34], [48, 50, 38, 35], [48, 51, 38, 36], [49, 4, 39, 2], [49, 10, 39, 8, "isParentHeaderShown"], [49, 29, 39, 27], [49, 32, 39, 30, "React"], [49, 37, 39, 35], [49, 38, 39, 36, "useContext"], [49, 48, 39, 46], [49, 49, 39, 47, "HeaderShownContext"], [49, 87, 39, 65], [49, 88, 39, 66], [50, 4, 40, 2], [50, 10, 40, 8], [50, 11, 40, 9, "searchBarVisible"], [50, 27, 40, 25], [50, 29, 40, 27, "setSearchBarVisible"], [50, 48, 40, 46], [50, 49, 40, 47], [50, 52, 40, 50, "React"], [50, 57, 40, 55], [50, 58, 40, 56, "useState"], [50, 66, 40, 64], [50, 67, 40, 65], [50, 72, 40, 70], [50, 73, 40, 71], [51, 4, 41, 2], [51, 10, 41, 8], [51, 11, 41, 9, "titleLayout"], [51, 22, 41, 20], [51, 24, 41, 22, "setTitleLayout"], [51, 38, 41, 36], [51, 39, 41, 37], [51, 42, 41, 40, "React"], [51, 47, 41, 45], [51, 48, 41, 46, "useState"], [51, 56, 41, 54], [51, 57, 41, 55, "undefined"], [51, 66, 41, 64], [51, 67, 41, 65], [52, 4, 42, 2], [52, 10, 42, 8, "onTitleLayout"], [52, 23, 42, 21], [52, 26, 42, 24, "e"], [52, 27, 42, 25], [52, 31, 42, 29], [53, 6, 43, 4], [53, 12, 43, 10], [54, 8, 44, 6, "height"], [54, 14, 44, 12], [55, 8, 45, 6, "width"], [56, 6, 46, 4], [56, 7, 46, 5], [56, 10, 46, 8, "e"], [56, 11, 46, 9], [56, 12, 46, 10, "nativeEvent"], [56, 23, 46, 21], [56, 24, 46, 22, "layout"], [56, 30, 46, 28], [57, 6, 47, 4, "setTitleLayout"], [57, 20, 47, 18], [57, 21, 47, 19, "titleLayout"], [57, 32, 47, 30], [57, 36, 47, 34], [58, 8, 48, 6], [58, 12, 48, 10, "titleLayout"], [58, 23, 48, 21], [58, 27, 48, 25, "height"], [58, 33, 48, 31], [58, 38, 48, 36, "titleLayout"], [58, 49, 48, 47], [58, 50, 48, 48, "height"], [58, 56, 48, 54], [58, 60, 48, 58, "width"], [58, 65, 48, 63], [58, 70, 48, 68, "titleLayout"], [58, 81, 48, 79], [58, 82, 48, 80, "width"], [58, 87, 48, 85], [58, 89, 48, 87], [59, 10, 49, 8], [59, 17, 49, 15, "titleLayout"], [59, 28, 49, 26], [60, 8, 50, 6], [61, 8, 51, 6], [61, 15, 51, 13], [62, 10, 52, 8, "height"], [62, 16, 52, 14], [63, 10, 53, 8, "width"], [64, 8, 54, 6], [64, 9, 54, 7], [65, 6, 55, 4], [65, 7, 55, 5], [65, 8, 55, 6], [66, 4, 56, 2], [66, 5, 56, 3], [67, 4, 57, 2], [67, 10, 57, 8], [68, 6, 58, 4, "layout"], [68, 12, 58, 10], [68, 15, 58, 13, "frame"], [68, 20, 58, 18], [69, 6, 59, 4, "modal"], [69, 11, 59, 9], [69, 14, 59, 12], [69, 19, 59, 17], [70, 6, 60, 4, "back"], [70, 10, 60, 8], [71, 6, 61, 4, "title"], [71, 11, 61, 9], [72, 6, 62, 4, "headerTitle"], [72, 17, 62, 15], [72, 19, 62, 17, "customTitle"], [72, 30, 62, 28], [73, 6, 63, 4, "headerTitleAlign"], [73, 22, 63, 20], [73, 25, 63, 23, "Platform"], [73, 42, 63, 31], [73, 43, 63, 32, "OS"], [73, 45, 63, 34], [73, 50, 63, 39], [73, 55, 63, 44], [73, 58, 63, 47], [73, 66, 63, 55], [73, 69, 63, 58], [73, 75, 63, 64], [74, 6, 64, 4, "headerLeft"], [74, 16, 64, 14], [74, 19, 64, 17, "back"], [74, 23, 64, 21], [74, 26, 64, 24, "props"], [74, 31, 64, 29], [74, 35, 64, 33], [74, 48, 64, 46], [74, 52, 64, 46, "_jsx"], [74, 67, 64, 50], [74, 69, 64, 51, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [74, 103, 64, 67], [74, 105, 64, 69], [75, 8, 65, 6], [75, 11, 65, 9, "props"], [76, 6, 66, 4], [76, 7, 66, 5], [76, 8, 66, 6], [76, 11, 66, 9, "undefined"], [76, 20, 66, 18], [77, 6, 67, 4, "headerSearchBarOptions"], [77, 28, 67, 26], [78, 6, 68, 4, "headerTransparent"], [78, 23, 68, 21], [79, 6, 69, 4, "headerTintColor"], [79, 21, 69, 19], [80, 6, 70, 4, "headerBackground"], [80, 22, 70, 20], [81, 6, 71, 4, "headerRight"], [81, 17, 71, 15], [82, 6, 72, 4, "headerTitleAllowFontScaling"], [82, 33, 72, 31], [82, 35, 72, 33, "titleAllowFontScaling"], [82, 56, 72, 54], [83, 6, 73, 4, "headerTitleStyle"], [83, 22, 73, 20], [83, 24, 73, 22, "titleStyle"], [83, 34, 73, 32], [84, 6, 74, 4, "headerLeftContainerStyle"], [84, 30, 74, 28], [84, 32, 74, 30, "leftContainerStyle"], [84, 50, 74, 48], [85, 6, 75, 4, "headerRightContainerStyle"], [85, 31, 75, 29], [85, 33, 75, 31, "rightContainerStyle"], [85, 52, 75, 50], [86, 6, 76, 4, "headerTitleContainerStyle"], [86, 31, 76, 29], [86, 33, 76, 31, "titleContainerStyle"], [86, 52, 76, 50], [87, 6, 77, 4, "headerBackButtonDisplayMode"], [87, 33, 77, 31], [87, 36, 77, 34, "Platform"], [87, 53, 77, 42], [87, 54, 77, 43, "OS"], [87, 56, 77, 45], [87, 61, 77, 50], [87, 66, 77, 55], [87, 69, 77, 58], [87, 78, 77, 67], [87, 81, 77, 70], [87, 90, 77, 79], [88, 6, 78, 4, "headerBackTitleStyle"], [88, 26, 78, 24], [89, 6, 79, 4, "headerBackgroundContainerStyle"], [89, 36, 79, 34], [89, 38, 79, 36, "backgroundContainerStyle"], [89, 62, 79, 60], [90, 6, 80, 4, "headerStyle"], [90, 17, 80, 15], [90, 19, 80, 17, "customHeaderStyle"], [90, 36, 80, 34], [91, 6, 81, 4, "headerShadowVisible"], [91, 25, 81, 23], [92, 6, 82, 4, "headerPressColor"], [92, 22, 82, 20], [93, 6, 83, 4, "headerPressOpacity"], [93, 24, 83, 22], [94, 6, 84, 4, "headerStatusBarHeight"], [94, 27, 84, 25], [94, 30, 84, 28, "isParentHeaderShown"], [94, 49, 84, 47], [94, 52, 84, 50], [94, 53, 84, 51], [94, 56, 84, 54, "insets"], [94, 62, 84, 60], [94, 63, 84, 61, "top"], [95, 4, 85, 2], [95, 5, 85, 3], [95, 8, 85, 6, "props"], [95, 13, 85, 11], [96, 4, 86, 2], [96, 10, 86, 8, "defaultHeight"], [96, 23, 86, 21], [96, 26, 86, 24], [96, 30, 86, 24, "getDefaultHeaderHeight"], [96, 76, 86, 46], [96, 78, 86, 47, "layout"], [96, 84, 86, 53], [96, 86, 86, 55, "modal"], [96, 91, 86, 60], [96, 93, 86, 62, "headerStatusBarHeight"], [96, 114, 86, 83], [96, 115, 86, 84], [97, 4, 87, 2], [97, 10, 87, 8], [98, 6, 88, 4, "height"], [98, 12, 88, 10], [98, 15, 88, 13, "defaultHeight"], [98, 28, 88, 26], [99, 6, 89, 4, "minHeight"], [99, 15, 89, 13], [100, 6, 90, 4, "maxHeight"], [100, 15, 90, 13], [101, 6, 91, 4, "backgroundColor"], [101, 21, 91, 19], [102, 6, 92, 4, "borderBottomColor"], [102, 23, 92, 21], [103, 6, 93, 4, "borderBottomEndRadius"], [103, 27, 93, 25], [104, 6, 94, 4, "borderBottomLeftRadius"], [104, 28, 94, 26], [105, 6, 95, 4, "borderBottomRightRadius"], [105, 29, 95, 27], [106, 6, 96, 4, "borderBottomStartRadius"], [106, 29, 96, 27], [107, 6, 97, 4, "borderBottomWidth"], [107, 23, 97, 21], [108, 6, 98, 4, "borderColor"], [108, 17, 98, 15], [109, 6, 99, 4, "borderEndColor"], [109, 20, 99, 18], [110, 6, 100, 4, "borderEndWidth"], [110, 20, 100, 18], [111, 6, 101, 4, "borderLeftColor"], [111, 21, 101, 19], [112, 6, 102, 4, "borderLeftWidth"], [112, 21, 102, 19], [113, 6, 103, 4, "borderRadius"], [113, 18, 103, 16], [114, 6, 104, 4, "borderRightColor"], [114, 22, 104, 20], [115, 6, 105, 4, "borderRightWidth"], [115, 22, 105, 20], [116, 6, 106, 4, "borderStartColor"], [116, 22, 106, 20], [117, 6, 107, 4, "borderStartWidth"], [117, 22, 107, 20], [118, 6, 108, 4, "borderStyle"], [118, 17, 108, 15], [119, 6, 109, 4, "borderTopColor"], [119, 20, 109, 18], [120, 6, 110, 4, "borderTopEndRadius"], [120, 24, 110, 22], [121, 6, 111, 4, "borderTopLeftRadius"], [121, 25, 111, 23], [122, 6, 112, 4, "borderTopRightRadius"], [122, 26, 112, 24], [123, 6, 113, 4, "borderTopStartRadius"], [123, 26, 113, 24], [124, 6, 114, 4, "borderTopWidth"], [124, 20, 114, 18], [125, 6, 115, 4, "borderWidth"], [125, 17, 115, 15], [126, 6, 116, 4, "boxShadow"], [126, 15, 116, 13], [127, 6, 117, 4, "elevation"], [127, 15, 117, 13], [128, 6, 118, 4, "shadowColor"], [128, 17, 118, 15], [129, 6, 119, 4, "shadowOffset"], [129, 18, 119, 16], [130, 6, 120, 4, "shadowOpacity"], [130, 19, 120, 17], [131, 6, 121, 4, "shadowRadius"], [131, 18, 121, 16], [132, 6, 122, 4, "opacity"], [132, 13, 122, 11], [133, 6, 123, 4, "transform"], [133, 15, 123, 13], [134, 6, 124, 4], [134, 9, 124, 7, "unsafeStyles"], [135, 4, 125, 2], [135, 5, 125, 3], [135, 8, 125, 6, "StyleSheet"], [135, 27, 125, 16], [135, 28, 125, 17, "flatten"], [135, 35, 125, 24], [135, 36, 125, 25, "customHeaderStyle"], [135, 53, 125, 42], [135, 57, 125, 46], [135, 58, 125, 47], [135, 59, 125, 48], [135, 60, 125, 49], [136, 4, 126, 2], [136, 8, 126, 6, "process"], [136, 15, 126, 13], [136, 16, 126, 14, "env"], [136, 19, 126, 17], [136, 20, 126, 18, "NODE_ENV"], [136, 28, 126, 26], [136, 33, 126, 31], [136, 45, 126, 43], [136, 47, 126, 45], [137, 6, 127, 4, "warnIfHeaderStylesDefined"], [137, 31, 127, 29], [137, 32, 127, 30, "unsafeStyles"], [137, 44, 127, 42], [137, 45, 127, 43], [138, 4, 128, 2], [139, 4, 129, 2], [139, 10, 129, 8, "safeStyles"], [139, 20, 129, 18], [139, 23, 129, 21], [140, 6, 130, 4, "backgroundColor"], [140, 21, 130, 19], [141, 6, 131, 4, "borderBottomColor"], [141, 23, 131, 21], [142, 6, 132, 4, "borderBottomEndRadius"], [142, 27, 132, 25], [143, 6, 133, 4, "borderBottomLeftRadius"], [143, 28, 133, 26], [144, 6, 134, 4, "borderBottomRightRadius"], [144, 29, 134, 27], [145, 6, 135, 4, "borderBottomStartRadius"], [145, 29, 135, 27], [146, 6, 136, 4, "borderBottomWidth"], [146, 23, 136, 21], [147, 6, 137, 4, "borderColor"], [147, 17, 137, 15], [148, 6, 138, 4, "borderEndColor"], [148, 20, 138, 18], [149, 6, 139, 4, "borderEndWidth"], [149, 20, 139, 18], [150, 6, 140, 4, "borderLeftColor"], [150, 21, 140, 19], [151, 6, 141, 4, "borderLeftWidth"], [151, 21, 141, 19], [152, 6, 142, 4, "borderRadius"], [152, 18, 142, 16], [153, 6, 143, 4, "borderRightColor"], [153, 22, 143, 20], [154, 6, 144, 4, "borderRightWidth"], [154, 22, 144, 20], [155, 6, 145, 4, "borderStartColor"], [155, 22, 145, 20], [156, 6, 146, 4, "borderStartWidth"], [156, 22, 146, 20], [157, 6, 147, 4, "borderStyle"], [157, 17, 147, 15], [158, 6, 148, 4, "borderTopColor"], [158, 20, 148, 18], [159, 6, 149, 4, "borderTopEndRadius"], [159, 24, 149, 22], [160, 6, 150, 4, "borderTopLeftRadius"], [160, 25, 150, 23], [161, 6, 151, 4, "borderTopRightRadius"], [161, 26, 151, 24], [162, 6, 152, 4, "borderTopStartRadius"], [162, 26, 152, 24], [163, 6, 153, 4, "borderTopWidth"], [163, 20, 153, 18], [164, 6, 154, 4, "borderWidth"], [164, 17, 154, 15], [165, 6, 155, 4, "boxShadow"], [165, 15, 155, 13], [166, 6, 156, 4, "elevation"], [166, 15, 156, 13], [167, 6, 157, 4, "shadowColor"], [167, 17, 157, 15], [168, 6, 158, 4, "shadowOffset"], [168, 18, 158, 16], [169, 6, 159, 4, "shadowOpacity"], [169, 19, 159, 17], [170, 6, 160, 4, "shadowRadius"], [170, 18, 160, 16], [171, 6, 161, 4, "opacity"], [171, 13, 161, 11], [172, 6, 162, 4, "transform"], [173, 4, 163, 2], [173, 5, 163, 3], [175, 4, 165, 2], [176, 4, 166, 2], [177, 4, 167, 2], [178, 4, 168, 2], [178, 9, 168, 7], [178, 15, 168, 13, "styleProp"], [178, 24, 168, 22], [178, 28, 168, 26, "safeStyles"], [178, 38, 168, 36], [178, 40, 168, 38], [179, 6, 169, 4], [180, 6, 170, 4], [180, 10, 170, 8, "safeStyles"], [180, 20, 170, 18], [180, 21, 170, 19, "styleProp"], [180, 30, 170, 28], [180, 31, 170, 29], [180, 36, 170, 34, "undefined"], [180, 45, 170, 43], [180, 47, 170, 45], [181, 8, 171, 6], [182, 8, 172, 6], [183, 8, 173, 6], [183, 15, 173, 13, "safeStyles"], [183, 25, 173, 23], [183, 26, 173, 24, "styleProp"], [183, 35, 173, 33], [183, 36, 173, 34], [184, 6, 174, 4], [185, 4, 175, 2], [186, 4, 176, 2], [186, 10, 176, 8, "backgroundStyle"], [186, 25, 176, 23], [186, 28, 176, 26], [187, 6, 177, 4], [187, 10, 177, 8, "headerTransparent"], [187, 27, 177, 25], [187, 31, 177, 29], [188, 8, 178, 6, "backgroundColor"], [188, 23, 178, 21], [188, 25, 178, 23], [189, 6, 179, 4], [189, 7, 179, 5], [189, 8, 179, 6], [190, 6, 180, 4], [190, 10, 180, 8], [190, 11, 180, 9, "headerTransparent"], [190, 28, 180, 26], [190, 32, 180, 30, "headerShadowVisible"], [190, 51, 180, 49], [190, 56, 180, 54], [190, 61, 180, 59], [190, 66, 180, 64], [191, 8, 181, 6, "borderBottomWidth"], [191, 25, 181, 23], [191, 27, 181, 25], [191, 28, 181, 26], [192, 8, 182, 6], [192, 11, 182, 9, "Platform"], [192, 28, 182, 17], [192, 29, 182, 18, "select"], [192, 35, 182, 24], [192, 36, 182, 25], [193, 10, 183, 8, "android"], [193, 17, 183, 15], [193, 19, 183, 17], [194, 12, 184, 10, "elevation"], [194, 21, 184, 19], [194, 23, 184, 21], [195, 10, 185, 8], [195, 11, 185, 9], [196, 10, 186, 8, "web"], [196, 13, 186, 11], [196, 15, 186, 13], [197, 12, 187, 10, "boxShadow"], [197, 21, 187, 19], [197, 23, 187, 21], [198, 10, 188, 8], [198, 11, 188, 9], [199, 10, 189, 8, "default"], [199, 17, 189, 15], [199, 19, 189, 17], [200, 12, 190, 10, "shadowOpacity"], [200, 25, 190, 23], [200, 27, 190, 25], [201, 10, 191, 8], [202, 8, 192, 6], [202, 9, 192, 7], [203, 6, 193, 4], [203, 7, 193, 5], [203, 8, 193, 6], [204, 6, 194, 4], [204, 9, 194, 7, "safeStyles"], [205, 4, 195, 2], [205, 5, 195, 3], [206, 4, 196, 2], [206, 10, 196, 8, "iconTintColor"], [206, 23, 196, 21], [206, 26, 196, 24, "headerTintColor"], [206, 41, 196, 39], [206, 45, 196, 43, "Platform"], [206, 62, 196, 51], [206, 63, 196, 52, "select"], [206, 69, 196, 58], [206, 70, 196, 59], [207, 6, 197, 4, "ios"], [207, 9, 197, 7], [207, 11, 197, 9, "colors"], [207, 17, 197, 15], [207, 18, 197, 16, "primary"], [207, 25, 197, 23], [208, 6, 198, 4, "default"], [208, 13, 198, 11], [208, 15, 198, 13, "colors"], [208, 21, 198, 19], [208, 22, 198, 20, "text"], [209, 4, 199, 2], [209, 5, 199, 3], [209, 6, 199, 4], [210, 4, 200, 2], [210, 10, 200, 8, "leftButton"], [210, 20, 200, 18], [210, 23, 200, 21, "headerLeft"], [210, 33, 200, 31], [210, 36, 200, 34, "headerLeft"], [210, 46, 200, 44], [210, 47, 200, 45], [211, 6, 201, 4, "tintColor"], [211, 15, 201, 13], [211, 17, 201, 15, "iconTintColor"], [211, 30, 201, 28], [212, 6, 202, 4, "pressColor"], [212, 16, 202, 14], [212, 18, 202, 16, "headerPressColor"], [212, 34, 202, 32], [213, 6, 203, 4, "pressOpacity"], [213, 18, 203, 16], [213, 20, 203, 18, "headerPressOpacity"], [213, 38, 203, 36], [214, 6, 204, 4, "displayMode"], [214, 17, 204, 15], [214, 19, 204, 17, "headerBackButtonDisplayMode"], [214, 46, 204, 44], [215, 6, 205, 4, "titleLayout"], [215, 17, 205, 15], [216, 6, 206, 4, "screenLayout"], [216, 18, 206, 16], [216, 20, 206, 18, "layout"], [216, 26, 206, 24], [217, 6, 207, 4, "canGoBack"], [217, 15, 207, 13], [217, 17, 207, 15, "Boolean"], [217, 24, 207, 22], [217, 25, 207, 23, "back"], [217, 29, 207, 27], [217, 30, 207, 28], [218, 6, 208, 4, "onPress"], [218, 13, 208, 11], [218, 15, 208, 13, "back"], [218, 19, 208, 17], [218, 22, 208, 20, "navigation"], [218, 32, 208, 30], [218, 33, 208, 31, "goBack"], [218, 39, 208, 37], [218, 42, 208, 40, "undefined"], [218, 51, 208, 49], [219, 6, 209, 4, "label"], [219, 11, 209, 9], [219, 13, 209, 11, "back"], [219, 17, 209, 15], [219, 19, 209, 17, "title"], [219, 24, 209, 22], [220, 6, 210, 4, "labelStyle"], [220, 16, 210, 14], [220, 18, 210, 16, "headerBackTitleStyle"], [220, 38, 210, 36], [221, 6, 211, 4, "href"], [221, 10, 211, 8], [221, 12, 211, 10, "back"], [221, 16, 211, 14], [221, 18, 211, 16, "href"], [222, 4, 212, 2], [222, 5, 212, 3], [222, 6, 212, 4], [222, 9, 212, 7], [222, 13, 212, 11], [223, 4, 213, 2], [223, 10, 213, 8, "rightB<PERSON>on"], [223, 21, 213, 19], [223, 24, 213, 22, "headerRight"], [223, 35, 213, 33], [223, 38, 213, 36, "headerRight"], [223, 49, 213, 47], [223, 50, 213, 48], [224, 6, 214, 4, "tintColor"], [224, 15, 214, 13], [224, 17, 214, 15, "iconTintColor"], [224, 30, 214, 28], [225, 6, 215, 4, "pressColor"], [225, 16, 215, 14], [225, 18, 215, 16, "headerPressColor"], [225, 34, 215, 32], [226, 6, 216, 4, "pressOpacity"], [226, 18, 216, 16], [226, 20, 216, 18, "headerPressOpacity"], [226, 38, 216, 36], [227, 6, 217, 4, "canGoBack"], [227, 15, 217, 13], [227, 17, 217, 15, "Boolean"], [227, 24, 217, 22], [227, 25, 217, 23, "back"], [227, 29, 217, 27], [228, 4, 218, 2], [228, 5, 218, 3], [228, 6, 218, 4], [228, 9, 218, 7], [228, 13, 218, 11], [229, 4, 219, 2], [229, 10, 219, 8, "headerTitle"], [229, 21, 219, 19], [229, 24, 219, 22], [229, 31, 219, 29, "customTitle"], [229, 42, 219, 40], [229, 47, 219, 45], [229, 57, 219, 55], [229, 60, 219, 58, "props"], [229, 65, 219, 63], [229, 69, 219, 67], [229, 82, 219, 80], [229, 86, 219, 80, "_jsx"], [229, 101, 219, 84], [229, 103, 219, 85, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [229, 127, 219, 96], [229, 129, 219, 98], [230, 6, 220, 4], [230, 9, 220, 7, "props"], [231, 4, 221, 2], [231, 5, 221, 3], [231, 6, 221, 4], [231, 9, 221, 7, "customTitle"], [231, 20, 221, 18], [232, 4, 222, 2], [232, 11, 222, 9], [232, 24, 222, 22], [232, 28, 222, 22, "_jsxs"], [232, 44, 222, 27], [232, 46, 222, 28, "Animated"], [232, 63, 222, 36], [232, 64, 222, 37, "View"], [232, 68, 222, 41], [232, 70, 222, 43], [233, 6, 223, 4, "pointerEvents"], [233, 19, 223, 17], [233, 21, 223, 19], [233, 31, 223, 29], [234, 6, 224, 4, "style"], [234, 11, 224, 9], [234, 13, 224, 11], [234, 14, 224, 12], [235, 8, 225, 6, "height"], [235, 14, 225, 12], [236, 8, 226, 6, "minHeight"], [236, 17, 226, 15], [237, 8, 227, 6, "maxHeight"], [237, 17, 227, 15], [238, 8, 228, 6, "opacity"], [238, 15, 228, 13], [239, 8, 229, 6, "transform"], [240, 6, 230, 4], [240, 7, 230, 5], [240, 8, 230, 6], [241, 6, 231, 4, "children"], [241, 14, 231, 12], [241, 16, 231, 14], [241, 17, 231, 15], [241, 30, 231, 28], [241, 34, 231, 28, "_jsx"], [241, 49, 231, 32], [241, 51, 231, 33, "Animated"], [241, 68, 231, 41], [241, 69, 231, 42, "View"], [241, 73, 231, 46], [241, 75, 231, 48], [242, 8, 232, 6, "pointerEvents"], [242, 21, 232, 19], [242, 23, 232, 21], [242, 33, 232, 31], [243, 8, 233, 6, "style"], [243, 13, 233, 11], [243, 15, 233, 13], [243, 16, 233, 14, "StyleSheet"], [243, 35, 233, 24], [243, 36, 233, 25, "absoluteFill"], [243, 48, 233, 37], [243, 50, 233, 39, "backgroundContainerStyle"], [243, 74, 233, 63], [243, 75, 233, 64], [244, 8, 234, 6, "children"], [244, 16, 234, 14], [244, 18, 234, 16, "headerBackground"], [244, 34, 234, 32], [244, 37, 234, 35, "headerBackground"], [244, 53, 234, 51], [244, 54, 234, 52], [245, 10, 235, 8, "style"], [245, 15, 235, 13], [245, 17, 235, 15, "backgroundStyle"], [246, 8, 236, 6], [246, 9, 236, 7], [246, 10, 236, 8], [246, 13, 236, 11], [246, 26, 236, 24], [246, 30, 236, 24, "_jsx"], [246, 45, 236, 28], [246, 47, 236, 29, "HeaderBackground"], [246, 81, 236, 45], [246, 83, 236, 47], [247, 10, 237, 8, "pointerEvents"], [247, 23, 237, 21], [248, 10, 238, 8], [249, 10, 239, 8, "headerTransparent"], [249, 27, 239, 25], [249, 32, 239, 30, "backgroundStyle"], [249, 47, 239, 45], [249, 48, 239, 46, "backgroundColor"], [249, 63, 239, 61], [249, 68, 239, 66], [249, 81, 239, 79], [249, 85, 239, 83], [249, 89, 239, 83, "Color"], [249, 103, 239, 88], [249, 105, 239, 89, "backgroundStyle"], [249, 120, 239, 104], [249, 121, 239, 105, "backgroundColor"], [249, 136, 239, 120], [249, 137, 239, 121], [249, 138, 239, 122, "alpha"], [249, 143, 239, 127], [249, 144, 239, 128], [249, 145, 239, 129], [249, 150, 239, 134], [249, 151, 239, 135], [249, 152, 239, 136], [249, 155, 239, 139], [249, 161, 239, 145], [249, 164, 239, 148], [249, 170, 239, 154], [250, 10, 240, 8, "style"], [250, 15, 240, 13], [250, 17, 240, 15, "backgroundStyle"], [251, 8, 241, 6], [251, 9, 241, 7], [252, 6, 242, 4], [252, 7, 242, 5], [252, 8, 242, 6], [252, 10, 242, 8], [252, 23, 242, 21], [252, 27, 242, 21, "_jsx"], [252, 42, 242, 25], [252, 44, 242, 26, "View"], [252, 57, 242, 30], [252, 59, 242, 32], [253, 8, 243, 6, "pointerEvents"], [253, 21, 243, 19], [253, 23, 243, 21], [253, 29, 243, 27], [254, 8, 244, 6, "style"], [254, 13, 244, 11], [254, 15, 244, 13], [255, 10, 245, 8, "height"], [255, 16, 245, 14], [255, 18, 245, 16, "headerStatusBarHeight"], [256, 8, 246, 6], [257, 6, 247, 4], [257, 7, 247, 5], [257, 8, 247, 6], [257, 10, 247, 8], [257, 23, 247, 21], [257, 27, 247, 21, "_jsxs"], [257, 43, 247, 26], [257, 45, 247, 27, "View"], [257, 58, 247, 31], [257, 60, 247, 33], [258, 8, 248, 6, "pointerEvents"], [258, 21, 248, 19], [258, 23, 248, 21], [258, 33, 248, 31], [259, 8, 249, 6, "style"], [259, 13, 249, 11], [259, 15, 249, 13], [259, 16, 249, 14, "styles"], [259, 22, 249, 20], [259, 23, 249, 21, "content"], [259, 30, 249, 28], [259, 32, 249, 30, "Platform"], [259, 49, 249, 38], [259, 50, 249, 39, "OS"], [259, 52, 249, 41], [259, 57, 249, 46], [259, 62, 249, 51], [259, 66, 249, 55, "frame"], [259, 71, 249, 60], [259, 72, 249, 61, "width"], [259, 77, 249, 66], [259, 81, 249, 70, "IPAD_MINI_MEDIUM_WIDTH"], [259, 103, 249, 92], [259, 106, 249, 95, "styles"], [259, 112, 249, 101], [259, 113, 249, 102, "large"], [259, 118, 249, 107], [259, 121, 249, 110], [259, 125, 249, 114], [259, 126, 249, 115], [260, 8, 250, 6, "children"], [260, 16, 250, 14], [260, 18, 250, 16], [260, 19, 250, 17], [260, 32, 250, 30], [260, 36, 250, 30, "_jsx"], [260, 51, 250, 34], [260, 53, 250, 35, "Animated"], [260, 70, 250, 43], [260, 71, 250, 44, "View"], [260, 75, 250, 48], [260, 77, 250, 50], [261, 10, 251, 8, "pointerEvents"], [261, 23, 251, 21], [261, 25, 251, 23], [261, 35, 251, 33], [262, 10, 252, 8, "style"], [262, 15, 252, 13], [262, 17, 252, 15], [262, 18, 252, 16, "styles"], [262, 24, 252, 22], [262, 25, 252, 23, "start"], [262, 30, 252, 28], [262, 32, 252, 30], [262, 33, 252, 31, "searchBarVisible"], [262, 49, 252, 47], [262, 53, 252, 51, "headerTitleAlign"], [262, 69, 252, 67], [262, 74, 252, 72], [262, 82, 252, 80], [262, 86, 252, 84, "styles"], [262, 92, 252, 90], [262, 93, 252, 91, "expand"], [262, 99, 252, 97], [262, 101, 252, 99], [263, 12, 253, 10, "marginStart"], [263, 23, 253, 21], [263, 25, 253, 23, "insets"], [263, 31, 253, 29], [263, 32, 253, 30, "left"], [264, 10, 254, 8], [264, 11, 254, 9], [264, 13, 254, 11, "leftContainerStyle"], [264, 31, 254, 29], [264, 32, 254, 30], [265, 10, 255, 8, "children"], [265, 18, 255, 16], [265, 20, 255, 18, "leftButton"], [266, 8, 256, 6], [266, 9, 256, 7], [266, 10, 256, 8], [266, 12, 256, 10, "Platform"], [266, 29, 256, 18], [266, 30, 256, 19, "OS"], [266, 32, 256, 21], [266, 37, 256, 26], [266, 42, 256, 31], [266, 46, 256, 35], [266, 47, 256, 36, "searchBarVisible"], [266, 63, 256, 52], [266, 66, 256, 55], [266, 79, 256, 68], [266, 83, 256, 68, "_jsxs"], [266, 99, 256, 73], [266, 101, 256, 74, "_Fragment"], [266, 121, 256, 83], [266, 123, 256, 85], [267, 10, 257, 8, "children"], [267, 18, 257, 16], [267, 20, 257, 18], [267, 21, 257, 19], [267, 34, 257, 32], [267, 38, 257, 32, "_jsx"], [267, 53, 257, 36], [267, 55, 257, 37, "Animated"], [267, 72, 257, 45], [267, 73, 257, 46, "View"], [267, 77, 257, 50], [267, 79, 257, 52], [268, 12, 258, 10, "pointerEvents"], [268, 25, 258, 23], [268, 27, 258, 25], [268, 37, 258, 35], [269, 12, 259, 10, "style"], [269, 17, 259, 15], [269, 19, 259, 17], [269, 20, 259, 18, "styles"], [269, 26, 259, 24], [269, 27, 259, 25, "title"], [269, 32, 259, 30], [269, 34, 259, 32], [270, 14, 260, 12], [271, 14, 261, 12, "max<PERSON><PERSON><PERSON>"], [271, 22, 261, 20], [271, 24, 261, 22, "headerTitleAlign"], [271, 40, 261, 38], [271, 45, 261, 43], [271, 53, 261, 51], [271, 56, 261, 54, "layout"], [271, 62, 261, 60], [271, 63, 261, 61, "width"], [271, 68, 261, 66], [271, 71, 261, 69], [271, 72, 261, 70], [271, 73, 261, 71, "leftButton"], [271, 83, 261, 81], [271, 86, 261, 84, "headerBackButtonDisplayMode"], [271, 113, 261, 111], [271, 118, 261, 116], [271, 127, 261, 125], [271, 130, 261, 128], [271, 132, 261, 130], [271, 135, 261, 133], [271, 137, 261, 135], [271, 140, 261, 138], [271, 142, 261, 140], [271, 147, 261, 145, "rightB<PERSON>on"], [271, 158, 261, 156], [271, 162, 261, 160, "headerSearchBarOptions"], [271, 184, 261, 182], [271, 187, 261, 185], [271, 189, 261, 187], [271, 192, 261, 190], [271, 193, 261, 191], [271, 194, 261, 192], [271, 197, 261, 195, "Math"], [271, 201, 261, 199], [271, 202, 261, 200, "max"], [271, 205, 261, 203], [271, 206, 261, 204, "insets"], [271, 212, 261, 210], [271, 213, 261, 211, "left"], [271, 217, 261, 215], [271, 219, 261, 217, "insets"], [271, 225, 261, 223], [271, 226, 261, 224, "right"], [271, 231, 261, 229], [271, 232, 261, 230], [271, 236, 261, 234], [271, 237, 261, 235], [271, 240, 261, 238, "layout"], [271, 246, 261, 244], [271, 247, 261, 245, "width"], [271, 252, 261, 250], [271, 256, 261, 254], [271, 257, 261, 255, "leftButton"], [271, 267, 261, 265], [271, 270, 261, 268], [271, 272, 261, 270], [271, 275, 261, 273], [271, 277, 261, 275], [271, 282, 261, 280, "rightB<PERSON>on"], [271, 293, 261, 291], [271, 297, 261, 295, "headerSearchBarOptions"], [271, 319, 261, 317], [271, 322, 261, 320], [271, 324, 261, 322], [271, 327, 261, 325], [271, 329, 261, 327], [271, 330, 261, 328], [271, 333, 261, 331, "insets"], [271, 339, 261, 337], [271, 340, 261, 338, "left"], [271, 344, 261, 342], [271, 347, 261, 345, "insets"], [271, 353, 261, 351], [271, 354, 261, 352, "right"], [271, 359, 261, 357], [272, 12, 262, 10], [272, 13, 262, 11], [272, 15, 262, 13, "headerTitleAlign"], [272, 31, 262, 29], [272, 36, 262, 34], [272, 42, 262, 40], [272, 46, 262, 44, "leftButton"], [272, 56, 262, 54], [272, 59, 262, 57], [273, 14, 263, 12, "marginStart"], [273, 25, 263, 23], [273, 27, 263, 25], [274, 12, 264, 10], [274, 13, 264, 11], [274, 16, 264, 14], [275, 14, 265, 12, "marginHorizontal"], [275, 30, 265, 28], [275, 32, 265, 30], [276, 12, 266, 10], [276, 13, 266, 11], [276, 15, 266, 13, "titleContainerStyle"], [276, 34, 266, 32], [276, 35, 266, 33], [277, 12, 267, 10, "children"], [277, 20, 267, 18], [277, 22, 267, 20, "headerTitle"], [277, 33, 267, 31], [277, 34, 267, 32], [278, 14, 268, 12, "children"], [278, 22, 268, 20], [278, 24, 268, 22, "title"], [278, 29, 268, 27], [279, 14, 269, 12, "allowFontScaling"], [279, 30, 269, 28], [279, 32, 269, 30, "titleAllowFontScaling"], [279, 53, 269, 51], [280, 14, 270, 12, "tintColor"], [280, 23, 270, 21], [280, 25, 270, 23, "headerTintColor"], [280, 40, 270, 38], [281, 14, 271, 12, "onLayout"], [281, 22, 271, 20], [281, 24, 271, 22, "onTitleLayout"], [281, 37, 271, 35], [282, 14, 272, 12, "style"], [282, 19, 272, 17], [282, 21, 272, 19, "titleStyle"], [283, 12, 273, 10], [283, 13, 273, 11], [284, 10, 274, 8], [284, 11, 274, 9], [284, 12, 274, 10], [284, 14, 274, 12], [284, 27, 274, 25], [284, 31, 274, 25, "_jsxs"], [284, 47, 274, 30], [284, 49, 274, 31, "Animated"], [284, 66, 274, 39], [284, 67, 274, 40, "View"], [284, 71, 274, 44], [284, 73, 274, 46], [285, 12, 275, 10, "pointerEvents"], [285, 25, 275, 23], [285, 27, 275, 25], [285, 37, 275, 35], [286, 12, 276, 10, "style"], [286, 17, 276, 15], [286, 19, 276, 17], [286, 20, 276, 18, "styles"], [286, 26, 276, 24], [286, 27, 276, 25, "end"], [286, 30, 276, 28], [286, 32, 276, 30, "styles"], [286, 38, 276, 36], [286, 39, 276, 37, "expand"], [286, 45, 276, 43], [286, 47, 276, 45], [287, 14, 277, 12, "marginEnd"], [287, 23, 277, 21], [287, 25, 277, 23, "insets"], [287, 31, 277, 29], [287, 32, 277, 30, "right"], [288, 12, 278, 10], [288, 13, 278, 11], [288, 15, 278, 13, "rightContainerStyle"], [288, 34, 278, 32], [288, 35, 278, 33], [289, 12, 279, 10, "children"], [289, 20, 279, 18], [289, 22, 279, 20], [289, 23, 279, 21, "rightB<PERSON>on"], [289, 34, 279, 32], [289, 36, 279, 34, "headerSearchBarOptions"], [289, 58, 279, 56], [289, 61, 279, 59], [289, 74, 279, 72], [289, 78, 279, 72, "_jsx"], [289, 93, 279, 76], [289, 95, 279, 77, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [289, 121, 279, 89], [289, 123, 279, 91], [290, 14, 280, 12, "tintColor"], [290, 23, 280, 21], [290, 25, 280, 23, "iconTintColor"], [290, 38, 280, 36], [291, 14, 281, 12, "pressColor"], [291, 24, 281, 22], [291, 26, 281, 24, "headerPressColor"], [291, 42, 281, 40], [292, 14, 282, 12, "pressOpacity"], [292, 26, 282, 24], [292, 28, 282, 26, "headerPressOpacity"], [292, 46, 282, 44], [293, 14, 283, 12, "onPress"], [293, 21, 283, 19], [293, 23, 283, 21, "onPress"], [293, 24, 283, 21], [293, 29, 283, 27], [294, 16, 284, 14, "setSearchBarVisible"], [294, 35, 284, 33], [294, 36, 284, 34], [294, 40, 284, 38], [294, 41, 284, 39], [295, 16, 285, 14, "headerSearchBarOptions"], [295, 38, 285, 36], [295, 40, 285, 38, "onOpen"], [295, 46, 285, 44], [295, 49, 285, 47], [295, 50, 285, 48], [296, 14, 286, 12], [296, 15, 286, 13], [297, 14, 287, 12, "children"], [297, 22, 287, 20], [297, 24, 287, 22], [297, 37, 287, 35], [297, 41, 287, 35, "_jsx"], [297, 56, 287, 39], [297, 58, 287, 40, "HeaderIcon"], [297, 80, 287, 50], [297, 82, 287, 52], [298, 16, 288, 14, "source"], [298, 22, 288, 20], [298, 24, 288, 22, "searchIcon"], [298, 43, 288, 32], [299, 16, 289, 14, "tintColor"], [299, 25, 289, 23], [299, 27, 289, 25, "iconTintColor"], [300, 14, 290, 12], [300, 15, 290, 13], [301, 12, 291, 10], [301, 13, 291, 11], [301, 14, 291, 12], [301, 17, 291, 15], [301, 21, 291, 19], [302, 10, 292, 8], [302, 11, 292, 9], [302, 12, 292, 10], [303, 8, 293, 6], [303, 9, 293, 7], [303, 10, 293, 8], [303, 13, 293, 11], [303, 17, 293, 15], [303, 19, 293, 17, "Platform"], [303, 36, 293, 25], [303, 37, 293, 26, "OS"], [303, 39, 293, 28], [303, 44, 293, 33], [303, 49, 293, 38], [303, 53, 293, 42, "searchBarVisible"], [303, 69, 293, 58], [303, 72, 293, 61], [303, 85, 293, 74], [303, 89, 293, 74, "_jsx"], [303, 104, 293, 78], [303, 106, 293, 79, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [303, 138, 293, 94], [303, 140, 293, 96], [304, 10, 294, 8], [304, 13, 294, 11, "headerSearchBarOptions"], [304, 35, 294, 33], [305, 10, 295, 8, "visible"], [305, 17, 295, 15], [305, 19, 295, 17, "searchBarVisible"], [305, 35, 295, 33], [306, 10, 296, 8, "onClose"], [306, 17, 296, 15], [306, 19, 296, 17, "onClose"], [306, 20, 296, 17], [306, 25, 296, 23], [307, 12, 297, 10, "setSearchBarVisible"], [307, 31, 297, 29], [307, 32, 297, 30], [307, 37, 297, 35], [307, 38, 297, 36], [308, 12, 298, 10, "headerSearchBarOptions"], [308, 34, 298, 32], [308, 36, 298, 34, "onClose"], [308, 43, 298, 41], [308, 46, 298, 44], [308, 47, 298, 45], [309, 10, 299, 8], [309, 11, 299, 9], [310, 10, 300, 8, "tintColor"], [310, 19, 300, 17], [310, 21, 300, 19, "headerTintColor"], [310, 36, 300, 34], [311, 10, 301, 8, "style"], [311, 15, 301, 13], [311, 17, 301, 15], [311, 18, 301, 16, "Platform"], [311, 35, 301, 24], [311, 36, 301, 25, "OS"], [311, 38, 301, 27], [311, 43, 301, 32], [311, 48, 301, 37], [311, 51, 301, 40], [311, 52, 301, 41, "StyleSheet"], [311, 71, 301, 51], [311, 72, 301, 52, "absoluteFill"], [311, 84, 301, 64], [311, 86, 301, 66], [312, 12, 302, 10, "paddingTop"], [312, 22, 302, 20], [312, 24, 302, 22, "headerStatusBarHeight"], [312, 45, 302, 43], [312, 48, 302, 46], [312, 49, 302, 47], [312, 52, 302, 50], [313, 10, 303, 8], [313, 11, 303, 9], [313, 13, 303, 11], [314, 12, 304, 10, "backgroundColor"], [314, 27, 304, 25], [314, 29, 304, 27, "backgroundColor"], [314, 44, 304, 42], [314, 48, 304, 46, "colors"], [314, 54, 304, 52], [314, 55, 304, 53, "card"], [315, 10, 305, 8], [315, 11, 305, 9], [315, 12, 305, 10], [315, 15, 305, 13], [315, 16, 305, 14, "leftButton"], [315, 26, 305, 24], [315, 30, 305, 28], [316, 12, 306, 10, "marginStart"], [316, 23, 306, 21], [316, 25, 306, 23], [317, 10, 307, 8], [317, 11, 307, 9], [318, 8, 308, 6], [318, 9, 308, 7], [318, 10, 308, 8], [318, 13, 308, 11], [318, 17, 308, 15], [319, 6, 309, 4], [319, 7, 309, 5], [319, 8, 309, 6], [320, 4, 310, 2], [320, 5, 310, 3], [320, 6, 310, 4], [321, 2, 311, 0], [322, 2, 312, 0], [322, 8, 312, 6, "styles"], [322, 14, 312, 12], [322, 17, 312, 15, "StyleSheet"], [322, 36, 312, 25], [322, 37, 312, 26, "create"], [322, 43, 312, 32], [322, 44, 312, 33], [323, 4, 313, 2, "content"], [323, 11, 313, 9], [323, 13, 313, 11], [324, 6, 314, 4, "flex"], [324, 10, 314, 8], [324, 12, 314, 10], [324, 13, 314, 11], [325, 6, 315, 4, "flexDirection"], [325, 19, 315, 17], [325, 21, 315, 19], [325, 26, 315, 24], [326, 6, 316, 4, "alignItems"], [326, 16, 316, 14], [326, 18, 316, 16], [327, 4, 317, 2], [327, 5, 317, 3], [328, 4, 318, 2, "large"], [328, 9, 318, 7], [328, 11, 318, 9], [329, 6, 319, 4, "marginHorizontal"], [329, 22, 319, 20], [329, 24, 319, 22], [330, 4, 320, 2], [330, 5, 320, 3], [331, 4, 321, 2, "title"], [331, 9, 321, 7], [331, 11, 321, 9], [332, 6, 322, 4, "justifyContent"], [332, 20, 322, 18], [332, 22, 322, 20], [333, 4, 323, 2], [333, 5, 323, 3], [334, 4, 324, 2, "start"], [334, 9, 324, 7], [334, 11, 324, 9], [335, 6, 325, 4, "flexDirection"], [335, 19, 325, 17], [335, 21, 325, 19], [335, 26, 325, 24], [336, 6, 326, 4, "alignItems"], [336, 16, 326, 14], [336, 18, 326, 16], [336, 26, 326, 24], [337, 6, 327, 4, "justifyContent"], [337, 20, 327, 18], [337, 22, 327, 20], [338, 4, 328, 2], [338, 5, 328, 3], [339, 4, 329, 2, "end"], [339, 7, 329, 5], [339, 9, 329, 7], [340, 6, 330, 4, "flexDirection"], [340, 19, 330, 17], [340, 21, 330, 19], [340, 26, 330, 24], [341, 6, 331, 4, "alignItems"], [341, 16, 331, 14], [341, 18, 331, 16], [341, 26, 331, 24], [342, 6, 332, 4, "justifyContent"], [342, 20, 332, 18], [342, 22, 332, 20], [343, 4, 333, 2], [343, 5, 333, 3], [344, 4, 334, 2, "expand"], [344, 10, 334, 8], [344, 12, 334, 10], [345, 6, 335, 4, "flexGrow"], [345, 14, 335, 12], [345, 16, 335, 14], [345, 17, 335, 15], [346, 6, 336, 4, "flexBasis"], [346, 15, 336, 13], [346, 17, 336, 15], [347, 4, 337, 2], [348, 2, 338, 0], [348, 3, 338, 1], [348, 4, 338, 2], [349, 0, 338, 3], [349, 3]], "functionMap": {"names": ["<global>", "warnIfHeaderStylesDefined", "Object.keys.forEach$argument_0", "Header", "useFrameSize$argument_0", "onTitleLayout", "setTitleLayout$argument_0", "<anonymous>", "_jsx$argument_1.onPress", "_jsx$argument_1.onClose"], "mappings": "AAA;kCCqB;8BCC;GDO;CDC;OGC;6BCE,YD;wBEQ;mBCK;KDQ;GFC;wBIQ;MJE;0DIyJ;IJE;qBK8D;aLG;iBMU;SNG;CHY"}}, "type": "js/module"}]}