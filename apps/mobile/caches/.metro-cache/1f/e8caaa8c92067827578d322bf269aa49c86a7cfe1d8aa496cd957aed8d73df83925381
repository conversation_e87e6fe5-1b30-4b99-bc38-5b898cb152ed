{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getDefaultHeaderHeight = getDefaultHeaderHeight;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  function getDefaultHeaderHeight(layout, modalPresentation, topInset) {\n    var headerHeight;\n\n    // On models with Dynamic Island the status bar height is smaller than the safe area top inset.\n    var hasDynamicIsland = _reactNative.Platform.OS === 'ios' && topInset > 50;\n    var statusBarHeight = hasDynamicIsland ? topInset - (5 + 1 / _reactNative.PixelRatio.get()) : topInset;\n    var isLandscape = layout.width > layout.height;\n    if (_reactNative.Platform.OS === 'ios') {\n      if (_reactNative.Platform.isPad || _reactNative.Platform.isTV) {\n        if (modalPresentation) {\n          headerHeight = 56;\n        } else {\n          headerHeight = 50;\n        }\n      } else {\n        if (isLandscape) {\n          headerHeight = 32;\n        } else {\n          if (modalPresentation) {\n            headerHeight = 56;\n          } else {\n            headerHeight = 44;\n          }\n        }\n      }\n    } else {\n      headerHeight = 64;\n    }\n    return headerHeight + statusBarHeight;\n  }\n});", "lineCount": 39, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getDefaultHeaderHeight"], [7, 32, 1, 13], [7, 35, 1, 13, "getDefaultHeaderHeight"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_reactNative"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "getDefaultHeaderHeight"], [9, 33, 4, 38, "getDefaultHeaderHeight"], [9, 34, 4, 39, "layout"], [9, 40, 4, 45], [9, 42, 4, 47, "modalPresentation"], [9, 59, 4, 64], [9, 61, 4, 66, "topInset"], [9, 69, 4, 74], [9, 71, 4, 76], [10, 4, 5, 2], [10, 8, 5, 6, "headerHeight"], [10, 20, 5, 18], [12, 4, 7, 2], [13, 4, 8, 2], [13, 8, 8, 8, "hasDynamicIsland"], [13, 24, 8, 24], [13, 27, 8, 27, "Platform"], [13, 48, 8, 35], [13, 49, 8, 36, "OS"], [13, 51, 8, 38], [13, 56, 8, 43], [13, 61, 8, 48], [13, 65, 8, 52, "topInset"], [13, 73, 8, 60], [13, 76, 8, 63], [13, 78, 8, 65], [14, 4, 9, 2], [14, 8, 9, 8, "statusBarHeight"], [14, 23, 9, 23], [14, 26, 9, 26, "hasDynamicIsland"], [14, 42, 9, 42], [14, 45, 9, 45, "topInset"], [14, 53, 9, 53], [14, 57, 9, 57], [14, 58, 9, 58], [14, 61, 9, 61], [14, 62, 9, 62], [14, 65, 9, 65, "PixelRatio"], [14, 88, 9, 75], [14, 89, 9, 76, "get"], [14, 92, 9, 79], [14, 93, 9, 80], [14, 94, 9, 81], [14, 95, 9, 82], [14, 98, 9, 85, "topInset"], [14, 106, 9, 93], [15, 4, 10, 2], [15, 8, 10, 8, "isLandscape"], [15, 19, 10, 19], [15, 22, 10, 22, "layout"], [15, 28, 10, 28], [15, 29, 10, 29, "width"], [15, 34, 10, 34], [15, 37, 10, 37, "layout"], [15, 43, 10, 43], [15, 44, 10, 44, "height"], [15, 50, 10, 50], [16, 4, 11, 2], [16, 8, 11, 6, "Platform"], [16, 29, 11, 14], [16, 30, 11, 15, "OS"], [16, 32, 11, 17], [16, 37, 11, 22], [16, 42, 11, 27], [16, 44, 11, 29], [17, 6, 12, 4], [17, 10, 12, 8, "Platform"], [17, 31, 12, 16], [17, 32, 12, 17, "isPad"], [17, 37, 12, 22], [17, 41, 12, 26, "Platform"], [17, 62, 12, 34], [17, 63, 12, 35, "isTV"], [17, 67, 12, 39], [17, 69, 12, 41], [18, 8, 13, 6], [18, 12, 13, 10, "modalPresentation"], [18, 29, 13, 27], [18, 31, 13, 29], [19, 10, 14, 8, "headerHeight"], [19, 22, 14, 20], [19, 25, 14, 23], [19, 27, 14, 25], [20, 8, 15, 6], [20, 9, 15, 7], [20, 15, 15, 13], [21, 10, 16, 8, "headerHeight"], [21, 22, 16, 20], [21, 25, 16, 23], [21, 27, 16, 25], [22, 8, 17, 6], [23, 6, 18, 4], [23, 7, 18, 5], [23, 13, 18, 11], [24, 8, 19, 6], [24, 12, 19, 10, "isLandscape"], [24, 23, 19, 21], [24, 25, 19, 23], [25, 10, 20, 8, "headerHeight"], [25, 22, 20, 20], [25, 25, 20, 23], [25, 27, 20, 25], [26, 8, 21, 6], [26, 9, 21, 7], [26, 15, 21, 13], [27, 10, 22, 8], [27, 14, 22, 12, "modalPresentation"], [27, 31, 22, 29], [27, 33, 22, 31], [28, 12, 23, 10, "headerHeight"], [28, 24, 23, 22], [28, 27, 23, 25], [28, 29, 23, 27], [29, 10, 24, 8], [29, 11, 24, 9], [29, 17, 24, 15], [30, 12, 25, 10, "headerHeight"], [30, 24, 25, 22], [30, 27, 25, 25], [30, 29, 25, 27], [31, 10, 26, 8], [32, 8, 27, 6], [33, 6, 28, 4], [34, 4, 29, 2], [34, 5, 29, 3], [34, 11, 29, 9], [35, 6, 30, 4, "headerHeight"], [35, 18, 30, 16], [35, 21, 30, 19], [35, 23, 30, 21], [36, 4, 31, 2], [37, 4, 32, 2], [37, 11, 32, 9, "headerHeight"], [37, 23, 32, 21], [37, 26, 32, 24, "statusBarHeight"], [37, 41, 32, 39], [38, 2, 33, 0], [39, 0, 33, 1], [39, 3]], "functionMap": {"names": ["<global>", "getDefaultHeaderHeight"], "mappings": "AAA;OCG;CD6B"}}, "type": "js/module"}]}