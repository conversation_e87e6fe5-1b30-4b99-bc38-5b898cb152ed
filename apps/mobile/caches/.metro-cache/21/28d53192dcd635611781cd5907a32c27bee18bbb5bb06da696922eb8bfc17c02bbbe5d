{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context/lib/commonjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 80}, "end": {"line": 3, "column": 97, "index": 177}}], "key": "rTYCe/OVA9H2u9+IUaGIhNpGQOQ=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"SafeAreaFrameContext\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.SafeAreaFrameContext;\n    }\n  });\n  Object.defineProperty(exports, \"SafeAreaInsetsContext\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.SafeAreaInsetsContext;\n    }\n  });\n  Object.defineProperty(exports, \"SafeAreaProvider\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.SafeAreaProvider;\n    }\n  });\n  exports.default = exports.SafeAreaView = void 0;\n  Object.defineProperty(exports, \"initialWindowMetrics\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.initialWindowMetrics;\n    }\n  });\n  Object.defineProperty(exports, \"useSafeAreaFrame\", {\n    enumerable: true,\n    get: function () {\n      return _commonjs.useSafeAreaFrame;\n    }\n  });\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _commonjs = require(_dependencyMap[3], \"react-native-safe-area-context/lib/commonjs\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/polyfills/SafeAreaView.web.jsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const SafeAreaView = exports.SafeAreaView = /*#__PURE__*/(0, _react.forwardRef)(_c = ({\n    children,\n    edges = ['top', 'right', 'bottom', 'left'],\n    ...rest\n  }, forwardedRef) => {\n    const isTabletAndAbove = typeof window !== 'undefined' ? window.self !== window.top : true;\n    return (0, _jsxRuntime.jsxs)(_commonjs.SafeAreaView, {\n      ...rest,\n      edges: edges,\n      ref: forwardedRef,\n      children: [isTabletAndAbove && edges.includes('top') && (0, _jsxRuntime.jsx)(_View.default, {\n        style: {\n          height: 64\n        }\n      }), children, isTabletAndAbove && edges.includes('bottom') && (0, _jsxRuntime.jsx)(_View.default, {\n        style: {\n          height: 34\n        }\n      })]\n    });\n  });\n  _c2 = SafeAreaView;\n  var _default = exports.default = SafeAreaView;\n  var _c, _c2;\n  $RefreshReg$(_c, \"SafeAreaView$forwardRef\");\n  $RefreshReg$(_c2, \"SafeAreaView\");\n});", "lineCount": 69, "map": [[37, 2, 1, 0], [37, 6, 1, 0, "_react"], [37, 12, 1, 0], [37, 15, 1, 0, "_interopRequireWildcard"], [37, 38, 1, 0], [37, 39, 1, 0, "require"], [37, 46, 1, 0], [37, 47, 1, 0, "_dependencyMap"], [37, 61, 1, 0], [38, 2, 1, 42], [38, 6, 1, 42, "_View"], [38, 11, 1, 42], [38, 14, 1, 42, "_interopRequireDefault"], [38, 36, 1, 42], [38, 37, 1, 42, "require"], [38, 44, 1, 42], [38, 45, 1, 42, "_dependencyMap"], [38, 59, 1, 42], [39, 2, 3, 0], [39, 6, 3, 0, "_commonjs"], [39, 15, 3, 0], [39, 18, 3, 0, "require"], [39, 25, 3, 0], [39, 26, 3, 0, "_dependencyMap"], [39, 40, 3, 0], [40, 2, 3, 97], [40, 6, 3, 97, "_jsxRuntime"], [40, 17, 3, 97], [40, 20, 3, 97, "require"], [40, 27, 3, 97], [40, 28, 3, 97, "_dependencyMap"], [40, 42, 3, 97], [41, 2, 3, 97], [41, 6, 3, 97, "_jsxFileName"], [41, 18, 3, 97], [42, 2, 3, 97], [42, 11, 3, 97, "_interopRequireWildcard"], [42, 35, 3, 97, "e"], [42, 36, 3, 97], [42, 38, 3, 97, "t"], [42, 39, 3, 97], [42, 68, 3, 97, "WeakMap"], [42, 75, 3, 97], [42, 81, 3, 97, "r"], [42, 82, 3, 97], [42, 89, 3, 97, "WeakMap"], [42, 96, 3, 97], [42, 100, 3, 97, "n"], [42, 101, 3, 97], [42, 108, 3, 97, "WeakMap"], [42, 115, 3, 97], [42, 127, 3, 97, "_interopRequireWildcard"], [42, 150, 3, 97], [42, 162, 3, 97, "_interopRequireWildcard"], [42, 163, 3, 97, "e"], [42, 164, 3, 97], [42, 166, 3, 97, "t"], [42, 167, 3, 97], [42, 176, 3, 97, "t"], [42, 177, 3, 97], [42, 181, 3, 97, "e"], [42, 182, 3, 97], [42, 186, 3, 97, "e"], [42, 187, 3, 97], [42, 188, 3, 97, "__esModule"], [42, 198, 3, 97], [42, 207, 3, 97, "e"], [42, 208, 3, 97], [42, 214, 3, 97, "o"], [42, 215, 3, 97], [42, 217, 3, 97, "i"], [42, 218, 3, 97], [42, 220, 3, 97, "f"], [42, 221, 3, 97], [42, 226, 3, 97, "__proto__"], [42, 235, 3, 97], [42, 243, 3, 97, "default"], [42, 250, 3, 97], [42, 252, 3, 97, "e"], [42, 253, 3, 97], [42, 270, 3, 97, "e"], [42, 271, 3, 97], [42, 294, 3, 97, "e"], [42, 295, 3, 97], [42, 320, 3, 97, "e"], [42, 321, 3, 97], [42, 330, 3, 97, "f"], [42, 331, 3, 97], [42, 337, 3, 97, "o"], [42, 338, 3, 97], [42, 341, 3, 97, "t"], [42, 342, 3, 97], [42, 345, 3, 97, "n"], [42, 346, 3, 97], [42, 349, 3, 97, "r"], [42, 350, 3, 97], [42, 358, 3, 97, "o"], [42, 359, 3, 97], [42, 360, 3, 97, "has"], [42, 363, 3, 97], [42, 364, 3, 97, "e"], [42, 365, 3, 97], [42, 375, 3, 97, "o"], [42, 376, 3, 97], [42, 377, 3, 97, "get"], [42, 380, 3, 97], [42, 381, 3, 97, "e"], [42, 382, 3, 97], [42, 385, 3, 97, "o"], [42, 386, 3, 97], [42, 387, 3, 97, "set"], [42, 390, 3, 97], [42, 391, 3, 97, "e"], [42, 392, 3, 97], [42, 394, 3, 97, "f"], [42, 395, 3, 97], [42, 411, 3, 97, "t"], [42, 412, 3, 97], [42, 416, 3, 97, "e"], [42, 417, 3, 97], [42, 433, 3, 97, "t"], [42, 434, 3, 97], [42, 441, 3, 97, "hasOwnProperty"], [42, 455, 3, 97], [42, 456, 3, 97, "call"], [42, 460, 3, 97], [42, 461, 3, 97, "e"], [42, 462, 3, 97], [42, 464, 3, 97, "t"], [42, 465, 3, 97], [42, 472, 3, 97, "i"], [42, 473, 3, 97], [42, 477, 3, 97, "o"], [42, 478, 3, 97], [42, 481, 3, 97, "Object"], [42, 487, 3, 97], [42, 488, 3, 97, "defineProperty"], [42, 502, 3, 97], [42, 507, 3, 97, "Object"], [42, 513, 3, 97], [42, 514, 3, 97, "getOwnPropertyDescriptor"], [42, 538, 3, 97], [42, 539, 3, 97, "e"], [42, 540, 3, 97], [42, 542, 3, 97, "t"], [42, 543, 3, 97], [42, 550, 3, 97, "i"], [42, 551, 3, 97], [42, 552, 3, 97, "get"], [42, 555, 3, 97], [42, 559, 3, 97, "i"], [42, 560, 3, 97], [42, 561, 3, 97, "set"], [42, 564, 3, 97], [42, 568, 3, 97, "o"], [42, 569, 3, 97], [42, 570, 3, 97, "f"], [42, 571, 3, 97], [42, 573, 3, 97, "t"], [42, 574, 3, 97], [42, 576, 3, 97, "i"], [42, 577, 3, 97], [42, 581, 3, 97, "f"], [42, 582, 3, 97], [42, 583, 3, 97, "t"], [42, 584, 3, 97], [42, 588, 3, 97, "e"], [42, 589, 3, 97], [42, 590, 3, 97, "t"], [42, 591, 3, 97], [42, 602, 3, 97, "f"], [42, 603, 3, 97], [42, 608, 3, 97, "e"], [42, 609, 3, 97], [42, 611, 3, 97, "t"], [42, 612, 3, 97], [43, 2, 12, 7], [43, 8, 12, 13, "SafeAreaView"], [43, 20, 12, 25], [43, 23, 12, 25, "exports"], [43, 30, 12, 25], [43, 31, 12, 25, "SafeAreaView"], [43, 43, 12, 25], [43, 59, 12, 28], [43, 63, 12, 28, "forwardRef"], [43, 80, 12, 38], [43, 82, 12, 38, "_c"], [43, 84, 12, 38], [43, 87, 13, 1, "_c"], [43, 88, 14, 2], [44, 4, 14, 4, "children"], [44, 12, 14, 12], [45, 4, 14, 14, "edges"], [45, 9, 14, 19], [45, 12, 14, 22], [45, 13, 14, 23], [45, 18, 14, 28], [45, 20, 14, 30], [45, 27, 14, 37], [45, 29, 14, 39], [45, 37, 14, 47], [45, 39, 14, 49], [45, 45, 14, 55], [45, 46, 14, 56], [46, 4, 14, 58], [46, 7, 14, 61, "rest"], [47, 2, 14, 66], [47, 3, 14, 67], [47, 5, 15, 2, "forwardedRef"], [47, 17, 15, 14], [47, 22, 16, 6], [48, 4, 17, 2], [48, 10, 17, 8, "isTabletAndAbove"], [48, 26, 17, 24], [48, 29, 18, 3], [48, 36, 18, 10, "window"], [48, 42, 18, 16], [48, 47, 18, 21], [48, 58, 18, 32], [48, 61, 18, 35, "window"], [48, 67, 18, 41], [48, 68, 18, 42, "self"], [48, 72, 18, 46], [48, 77, 18, 51, "window"], [48, 83, 18, 57], [48, 84, 18, 58, "top"], [48, 87, 18, 61], [48, 90, 18, 64], [48, 94, 18, 68], [49, 4, 19, 2], [49, 11, 20, 3], [49, 15, 20, 3, "_jsxRuntime"], [49, 26, 20, 3], [49, 27, 20, 3, "jsxs"], [49, 31, 20, 3], [49, 33, 20, 4, "_commonjs"], [49, 42, 20, 4], [49, 43, 20, 4, "SafeAreaView"], [49, 55, 20, 22], [50, 6, 20, 22], [50, 9, 20, 27, "rest"], [50, 13, 20, 31], [51, 6, 20, 33, "edges"], [51, 11, 20, 38], [51, 13, 20, 40, "edges"], [51, 18, 20, 46], [52, 6, 20, 47, "ref"], [52, 9, 20, 50], [52, 11, 20, 52, "forwardedRef"], [52, 23, 20, 65], [53, 6, 20, 65, "children"], [53, 14, 20, 65], [53, 17, 21, 5, "isTabletAndAbove"], [53, 33, 21, 21], [53, 37, 21, 25, "edges"], [53, 42, 21, 30], [53, 43, 21, 31, "includes"], [53, 51, 21, 39], [53, 52, 21, 40], [53, 57, 21, 45], [53, 58, 21, 46], [53, 62, 22, 5], [53, 66, 22, 5, "_jsxRuntime"], [53, 77, 22, 5], [53, 78, 22, 5, "jsx"], [53, 81, 22, 5], [53, 83, 22, 6, "_View"], [53, 88, 22, 6], [53, 89, 22, 6, "default"], [53, 96, 22, 10], [54, 8, 22, 11, "style"], [54, 13, 22, 16], [54, 15, 22, 18], [55, 10, 22, 20, "height"], [55, 16, 22, 26], [55, 18, 22, 28], [56, 8, 22, 31], [57, 6, 22, 33], [57, 7, 22, 35], [57, 8, 23, 5], [57, 10, 24, 5, "children"], [57, 18, 24, 13], [57, 20, 25, 5, "isTabletAndAbove"], [57, 36, 25, 21], [57, 40, 25, 25, "edges"], [57, 45, 25, 30], [57, 46, 25, 31, "includes"], [57, 54, 25, 39], [57, 55, 25, 40], [57, 63, 25, 48], [57, 64, 25, 49], [57, 68, 26, 5], [57, 72, 26, 5, "_jsxRuntime"], [57, 83, 26, 5], [57, 84, 26, 5, "jsx"], [57, 87, 26, 5], [57, 89, 26, 6, "_View"], [57, 94, 26, 6], [57, 95, 26, 6, "default"], [57, 102, 26, 10], [58, 8, 26, 11, "style"], [58, 13, 26, 16], [58, 15, 26, 18], [59, 10, 26, 20, "height"], [59, 16, 26, 26], [59, 18, 26, 28], [60, 8, 26, 31], [61, 6, 26, 33], [61, 7, 26, 35], [61, 8, 27, 5], [62, 4, 27, 5], [62, 5, 28, 23], [62, 6, 28, 24], [63, 2, 30, 1], [63, 3, 31, 0], [63, 4, 31, 1], [64, 2, 31, 2, "_c2"], [64, 5, 31, 2], [64, 8, 12, 13, "SafeAreaView"], [64, 20, 12, 25], [65, 2, 12, 25], [65, 6, 12, 25, "_default"], [65, 14, 12, 25], [65, 17, 12, 25, "exports"], [65, 24, 12, 25], [65, 25, 12, 25, "default"], [65, 32, 12, 25], [65, 35, 32, 15, "SafeAreaView"], [65, 47, 32, 27], [66, 2, 32, 27], [66, 6, 32, 27, "_c"], [66, 8, 32, 27], [66, 10, 32, 27, "_c2"], [66, 13, 32, 27], [67, 2, 32, 27, "$RefreshReg$"], [67, 14, 32, 27], [67, 15, 32, 27, "_c"], [67, 17, 32, 27], [68, 2, 32, 27, "$RefreshReg$"], [68, 14, 32, 27], [68, 15, 32, 27, "_c2"], [68, 18, 32, 27], [69, 0, 32, 27], [69, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0"], "mappings": "AAA;CCY;EDiB"}}, "type": "js/module"}]}