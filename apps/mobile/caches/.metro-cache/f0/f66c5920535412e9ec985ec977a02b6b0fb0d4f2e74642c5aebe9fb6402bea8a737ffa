{"dependencies": [{"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[0], \"../../State\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class InteractionManager {\n    // Private becaues of singleton\n    // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n    constructor() {\n      _defineProperty(this, \"waitForRelations\", new Map());\n      _defineProperty(this, \"simultaneousRelations\", new Map());\n      _defineProperty(this, \"blocksHandlersRelations\", new Map());\n    }\n    configureInteractions(handler, config) {\n      this.dropRelationsForHandlerWithTag(handler.handlerTag);\n      if (config.waitFor) {\n        const waitFor = [];\n        config.waitFor.forEach(otherHandler => {\n          // New API reference\n          if (typeof otherHandler === 'number') {\n            waitFor.push(otherHandler);\n          } else {\n            // Old API reference\n            waitFor.push(otherHandler.handlerTag);\n          }\n        });\n        this.waitForRelations.set(handler.handlerTag, waitFor);\n      }\n      if (config.simultaneousHandlers) {\n        const simultaneousHandlers = [];\n        config.simultaneousHandlers.forEach(otherHandler => {\n          if (typeof otherHandler === 'number') {\n            simultaneousHandlers.push(otherHandler);\n          } else {\n            simultaneousHandlers.push(otherHandler.handlerTag);\n          }\n        });\n        this.simultaneousRelations.set(handler.handlerTag, simultaneousHandlers);\n      }\n      if (config.blocksHandlers) {\n        const blocksHandlers = [];\n        config.blocksHandlers.forEach(otherHandler => {\n          if (typeof otherHandler === 'number') {\n            blocksHandlers.push(otherHandler);\n          } else {\n            blocksHandlers.push(otherHandler.handlerTag);\n          }\n        });\n        this.blocksHandlersRelations.set(handler.handlerTag, blocksHandlers);\n      }\n    }\n    shouldWaitForHandlerFailure(handler, otherHandler) {\n      const waitFor = this.waitForRelations.get(handler.handlerTag);\n      return (waitFor === null || waitFor === void 0 ? void 0 : waitFor.find(tag => {\n        return tag === otherHandler.handlerTag;\n      })) !== undefined;\n    }\n    shouldRecognizeSimultaneously(handler, otherHandler) {\n      const simultaneousHandlers = this.simultaneousRelations.get(handler.handlerTag);\n      return (simultaneousHandlers === null || simultaneousHandlers === void 0 ? void 0 : simultaneousHandlers.find(tag => {\n        return tag === otherHandler.handlerTag;\n      })) !== undefined;\n    }\n    shouldRequireHandlerToWaitForFailure(handler, otherHandler) {\n      const waitFor = this.blocksHandlersRelations.get(handler.handlerTag);\n      return (waitFor === null || waitFor === void 0 ? void 0 : waitFor.find(tag => {\n        return tag === otherHandler.handlerTag;\n      })) !== undefined;\n    }\n    shouldHandlerBeCancelledBy(_handler, otherHandler) {\n      var _otherHandler$isButto;\n\n      // We check constructor name instead of using `instanceof` in order do avoid circular dependencies\n      const isNativeHandler = otherHandler.constructor.name === 'NativeViewGestureHandler';\n      const isActive = otherHandler.state === _State.State.ACTIVE;\n      const isButton = ((_otherHandler$isButto = otherHandler.isButton) === null || _otherHandler$isButto === void 0 ? void 0 : _otherHandler$isButto.call(otherHandler)) === true;\n      return isNativeHandler && isActive && !isButton;\n    }\n    dropRelationsForHandlerWithTag(handlerTag) {\n      this.waitForRelations.delete(handlerTag);\n      this.simultaneousRelations.delete(handlerTag);\n      this.blocksHandlersRelations.delete(handlerTag);\n    }\n    reset() {\n      this.waitForRelations.clear();\n      this.simultaneousRelations.clear();\n      this.blocksHandlersRelations.clear();\n    }\n    static get instance() {\n      if (!this._instance) {\n        this._instance = new InteractionManager();\n      }\n      return this._instance;\n    }\n  }\n  exports.default = InteractionManager;\n  _defineProperty(InteractionManager, \"_instance\", void 0);\n});", "lineCount": 112, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_State"], [6, 12, 3, 0], [6, 15, 3, 0, "require"], [6, 22, 3, 0], [6, 23, 3, 0, "_dependencyMap"], [6, 37, 3, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "obj"], [7, 30, 1, 28], [7, 32, 1, 30, "key"], [7, 35, 1, 33], [7, 37, 1, 35, "value"], [7, 42, 1, 40], [7, 44, 1, 42], [8, 4, 1, 44], [8, 8, 1, 48, "key"], [8, 11, 1, 51], [8, 15, 1, 55, "obj"], [8, 18, 1, 58], [8, 20, 1, 60], [9, 6, 1, 62, "Object"], [9, 12, 1, 68], [9, 13, 1, 69, "defineProperty"], [9, 27, 1, 83], [9, 28, 1, 84, "obj"], [9, 31, 1, 87], [9, 33, 1, 89, "key"], [9, 36, 1, 92], [9, 38, 1, 94], [10, 8, 1, 96, "value"], [10, 13, 1, 101], [10, 15, 1, 103, "value"], [10, 20, 1, 108], [11, 8, 1, 110, "enumerable"], [11, 18, 1, 120], [11, 20, 1, 122], [11, 24, 1, 126], [12, 8, 1, 128, "configurable"], [12, 20, 1, 140], [12, 22, 1, 142], [12, 26, 1, 146], [13, 8, 1, 148, "writable"], [13, 16, 1, 156], [13, 18, 1, 158], [14, 6, 1, 163], [14, 7, 1, 164], [14, 8, 1, 165], [15, 4, 1, 167], [15, 5, 1, 168], [15, 11, 1, 174], [16, 6, 1, 176, "obj"], [16, 9, 1, 179], [16, 10, 1, 180, "key"], [16, 13, 1, 183], [16, 14, 1, 184], [16, 17, 1, 187, "value"], [16, 22, 1, 192], [17, 4, 1, 194], [18, 4, 1, 196], [18, 11, 1, 203, "obj"], [18, 14, 1, 206], [19, 2, 1, 208], [20, 2, 4, 15], [20, 8, 4, 21, "InteractionManager"], [20, 26, 4, 39], [20, 27, 4, 40], [21, 4, 5, 2], [22, 4, 6, 2], [23, 4, 7, 2, "constructor"], [23, 15, 7, 13, "constructor"], [23, 16, 7, 13], [23, 18, 7, 16], [24, 6, 8, 4, "_defineProperty"], [24, 21, 8, 19], [24, 22, 8, 20], [24, 26, 8, 24], [24, 28, 8, 26], [24, 46, 8, 44], [24, 48, 8, 46], [24, 52, 8, 50, "Map"], [24, 55, 8, 53], [24, 56, 8, 54], [24, 57, 8, 55], [24, 58, 8, 56], [25, 6, 10, 4, "_defineProperty"], [25, 21, 10, 19], [25, 22, 10, 20], [25, 26, 10, 24], [25, 28, 10, 26], [25, 51, 10, 49], [25, 53, 10, 51], [25, 57, 10, 55, "Map"], [25, 60, 10, 58], [25, 61, 10, 59], [25, 62, 10, 60], [25, 63, 10, 61], [26, 6, 12, 4, "_defineProperty"], [26, 21, 12, 19], [26, 22, 12, 20], [26, 26, 12, 24], [26, 28, 12, 26], [26, 53, 12, 51], [26, 55, 12, 53], [26, 59, 12, 57, "Map"], [26, 62, 12, 60], [26, 63, 12, 61], [26, 64, 12, 62], [26, 65, 12, 63], [27, 4, 13, 2], [28, 4, 15, 2, "configureInteractions"], [28, 25, 15, 23, "configureInteractions"], [28, 26, 15, 24, "handler"], [28, 33, 15, 31], [28, 35, 15, 33, "config"], [28, 41, 15, 39], [28, 43, 15, 41], [29, 6, 16, 4], [29, 10, 16, 8], [29, 11, 16, 9, "dropRelationsForHandlerWithTag"], [29, 41, 16, 39], [29, 42, 16, 40, "handler"], [29, 49, 16, 47], [29, 50, 16, 48, "handlerTag"], [29, 60, 16, 58], [29, 61, 16, 59], [30, 6, 18, 4], [30, 10, 18, 8, "config"], [30, 16, 18, 14], [30, 17, 18, 15, "waitFor"], [30, 24, 18, 22], [30, 26, 18, 24], [31, 8, 19, 6], [31, 14, 19, 12, "waitFor"], [31, 21, 19, 19], [31, 24, 19, 22], [31, 26, 19, 24], [32, 8, 20, 6, "config"], [32, 14, 20, 12], [32, 15, 20, 13, "waitFor"], [32, 22, 20, 20], [32, 23, 20, 21, "for<PERSON>ach"], [32, 30, 20, 28], [32, 31, 20, 29, "<PERSON><PERSON><PERSON><PERSON>"], [32, 43, 20, 41], [32, 47, 20, 45], [33, 10, 21, 8], [34, 10, 22, 8], [34, 14, 22, 12], [34, 21, 22, 19, "<PERSON><PERSON><PERSON><PERSON>"], [34, 33, 22, 31], [34, 38, 22, 36], [34, 46, 22, 44], [34, 48, 22, 46], [35, 12, 23, 10, "waitFor"], [35, 19, 23, 17], [35, 20, 23, 18, "push"], [35, 24, 23, 22], [35, 25, 23, 23, "<PERSON><PERSON><PERSON><PERSON>"], [35, 37, 23, 35], [35, 38, 23, 36], [36, 10, 24, 8], [36, 11, 24, 9], [36, 17, 24, 15], [37, 12, 25, 10], [38, 12, 26, 10, "waitFor"], [38, 19, 26, 17], [38, 20, 26, 18, "push"], [38, 24, 26, 22], [38, 25, 26, 23, "<PERSON><PERSON><PERSON><PERSON>"], [38, 37, 26, 35], [38, 38, 26, 36, "handlerTag"], [38, 48, 26, 46], [38, 49, 26, 47], [39, 10, 27, 8], [40, 8, 28, 6], [40, 9, 28, 7], [40, 10, 28, 8], [41, 8, 29, 6], [41, 12, 29, 10], [41, 13, 29, 11, "waitForRelations"], [41, 29, 29, 27], [41, 30, 29, 28, "set"], [41, 33, 29, 31], [41, 34, 29, 32, "handler"], [41, 41, 29, 39], [41, 42, 29, 40, "handlerTag"], [41, 52, 29, 50], [41, 54, 29, 52, "waitFor"], [41, 61, 29, 59], [41, 62, 29, 60], [42, 6, 30, 4], [43, 6, 32, 4], [43, 10, 32, 8, "config"], [43, 16, 32, 14], [43, 17, 32, 15, "simultaneousHandlers"], [43, 37, 32, 35], [43, 39, 32, 37], [44, 8, 33, 6], [44, 14, 33, 12, "simultaneousHandlers"], [44, 34, 33, 32], [44, 37, 33, 35], [44, 39, 33, 37], [45, 8, 34, 6, "config"], [45, 14, 34, 12], [45, 15, 34, 13, "simultaneousHandlers"], [45, 35, 34, 33], [45, 36, 34, 34, "for<PERSON>ach"], [45, 43, 34, 41], [45, 44, 34, 42, "<PERSON><PERSON><PERSON><PERSON>"], [45, 56, 34, 54], [45, 60, 34, 58], [46, 10, 35, 8], [46, 14, 35, 12], [46, 21, 35, 19, "<PERSON><PERSON><PERSON><PERSON>"], [46, 33, 35, 31], [46, 38, 35, 36], [46, 46, 35, 44], [46, 48, 35, 46], [47, 12, 36, 10, "simultaneousHandlers"], [47, 32, 36, 30], [47, 33, 36, 31, "push"], [47, 37, 36, 35], [47, 38, 36, 36, "<PERSON><PERSON><PERSON><PERSON>"], [47, 50, 36, 48], [47, 51, 36, 49], [48, 10, 37, 8], [48, 11, 37, 9], [48, 17, 37, 15], [49, 12, 38, 10, "simultaneousHandlers"], [49, 32, 38, 30], [49, 33, 38, 31, "push"], [49, 37, 38, 35], [49, 38, 38, 36, "<PERSON><PERSON><PERSON><PERSON>"], [49, 50, 38, 48], [49, 51, 38, 49, "handlerTag"], [49, 61, 38, 59], [49, 62, 38, 60], [50, 10, 39, 8], [51, 8, 40, 6], [51, 9, 40, 7], [51, 10, 40, 8], [52, 8, 41, 6], [52, 12, 41, 10], [52, 13, 41, 11, "simultaneousRelations"], [52, 34, 41, 32], [52, 35, 41, 33, "set"], [52, 38, 41, 36], [52, 39, 41, 37, "handler"], [52, 46, 41, 44], [52, 47, 41, 45, "handlerTag"], [52, 57, 41, 55], [52, 59, 41, 57, "simultaneousHandlers"], [52, 79, 41, 77], [52, 80, 41, 78], [53, 6, 42, 4], [54, 6, 44, 4], [54, 10, 44, 8, "config"], [54, 16, 44, 14], [54, 17, 44, 15, "blocksHandlers"], [54, 31, 44, 29], [54, 33, 44, 31], [55, 8, 45, 6], [55, 14, 45, 12, "blocksHandlers"], [55, 28, 45, 26], [55, 31, 45, 29], [55, 33, 45, 31], [56, 8, 46, 6, "config"], [56, 14, 46, 12], [56, 15, 46, 13, "blocksHandlers"], [56, 29, 46, 27], [56, 30, 46, 28, "for<PERSON>ach"], [56, 37, 46, 35], [56, 38, 46, 36, "<PERSON><PERSON><PERSON><PERSON>"], [56, 50, 46, 48], [56, 54, 46, 52], [57, 10, 47, 8], [57, 14, 47, 12], [57, 21, 47, 19, "<PERSON><PERSON><PERSON><PERSON>"], [57, 33, 47, 31], [57, 38, 47, 36], [57, 46, 47, 44], [57, 48, 47, 46], [58, 12, 48, 10, "blocksHandlers"], [58, 26, 48, 24], [58, 27, 48, 25, "push"], [58, 31, 48, 29], [58, 32, 48, 30, "<PERSON><PERSON><PERSON><PERSON>"], [58, 44, 48, 42], [58, 45, 48, 43], [59, 10, 49, 8], [59, 11, 49, 9], [59, 17, 49, 15], [60, 12, 50, 10, "blocksHandlers"], [60, 26, 50, 24], [60, 27, 50, 25, "push"], [60, 31, 50, 29], [60, 32, 50, 30, "<PERSON><PERSON><PERSON><PERSON>"], [60, 44, 50, 42], [60, 45, 50, 43, "handlerTag"], [60, 55, 50, 53], [60, 56, 50, 54], [61, 10, 51, 8], [62, 8, 52, 6], [62, 9, 52, 7], [62, 10, 52, 8], [63, 8, 53, 6], [63, 12, 53, 10], [63, 13, 53, 11, "blocksHandlersRelations"], [63, 36, 53, 34], [63, 37, 53, 35, "set"], [63, 40, 53, 38], [63, 41, 53, 39, "handler"], [63, 48, 53, 46], [63, 49, 53, 47, "handlerTag"], [63, 59, 53, 57], [63, 61, 53, 59, "blocksHandlers"], [63, 75, 53, 73], [63, 76, 53, 74], [64, 6, 54, 4], [65, 4, 55, 2], [66, 4, 57, 2, "shouldWaitForHandlerFailure"], [66, 31, 57, 29, "shouldWaitForHandlerFailure"], [66, 32, 57, 30, "handler"], [66, 39, 57, 37], [66, 41, 57, 39, "<PERSON><PERSON><PERSON><PERSON>"], [66, 53, 57, 51], [66, 55, 57, 53], [67, 6, 58, 4], [67, 12, 58, 10, "waitFor"], [67, 19, 58, 17], [67, 22, 58, 20], [67, 26, 58, 24], [67, 27, 58, 25, "waitForRelations"], [67, 43, 58, 41], [67, 44, 58, 42, "get"], [67, 47, 58, 45], [67, 48, 58, 46, "handler"], [67, 55, 58, 53], [67, 56, 58, 54, "handlerTag"], [67, 66, 58, 64], [67, 67, 58, 65], [68, 6, 59, 4], [68, 13, 59, 11], [68, 14, 59, 12, "waitFor"], [68, 21, 59, 19], [68, 26, 59, 24], [68, 30, 59, 28], [68, 34, 59, 32, "waitFor"], [68, 41, 59, 39], [68, 46, 59, 44], [68, 51, 59, 49], [68, 52, 59, 50], [68, 55, 59, 53], [68, 60, 59, 58], [68, 61, 59, 59], [68, 64, 59, 62, "waitFor"], [68, 71, 59, 69], [68, 72, 59, 70, "find"], [68, 76, 59, 74], [68, 77, 59, 75, "tag"], [68, 80, 59, 78], [68, 84, 59, 82], [69, 8, 60, 6], [69, 15, 60, 13, "tag"], [69, 18, 60, 16], [69, 23, 60, 21, "<PERSON><PERSON><PERSON><PERSON>"], [69, 35, 60, 33], [69, 36, 60, 34, "handlerTag"], [69, 46, 60, 44], [70, 6, 61, 4], [70, 7, 61, 5], [70, 8, 61, 6], [70, 14, 61, 12, "undefined"], [70, 23, 61, 21], [71, 4, 62, 2], [72, 4, 64, 2, "shouldRecognizeSimultaneously"], [72, 33, 64, 31, "shouldRecognizeSimultaneously"], [72, 34, 64, 32, "handler"], [72, 41, 64, 39], [72, 43, 64, 41, "<PERSON><PERSON><PERSON><PERSON>"], [72, 55, 64, 53], [72, 57, 64, 55], [73, 6, 65, 4], [73, 12, 65, 10, "simultaneousHandlers"], [73, 32, 65, 30], [73, 35, 65, 33], [73, 39, 65, 37], [73, 40, 65, 38, "simultaneousRelations"], [73, 61, 65, 59], [73, 62, 65, 60, "get"], [73, 65, 65, 63], [73, 66, 65, 64, "handler"], [73, 73, 65, 71], [73, 74, 65, 72, "handlerTag"], [73, 84, 65, 82], [73, 85, 65, 83], [74, 6, 66, 4], [74, 13, 66, 11], [74, 14, 66, 12, "simultaneousHandlers"], [74, 34, 66, 32], [74, 39, 66, 37], [74, 43, 66, 41], [74, 47, 66, 45, "simultaneousHandlers"], [74, 67, 66, 65], [74, 72, 66, 70], [74, 77, 66, 75], [74, 78, 66, 76], [74, 81, 66, 79], [74, 86, 66, 84], [74, 87, 66, 85], [74, 90, 66, 88, "simultaneousHandlers"], [74, 110, 66, 108], [74, 111, 66, 109, "find"], [74, 115, 66, 113], [74, 116, 66, 114, "tag"], [74, 119, 66, 117], [74, 123, 66, 121], [75, 8, 67, 6], [75, 15, 67, 13, "tag"], [75, 18, 67, 16], [75, 23, 67, 21, "<PERSON><PERSON><PERSON><PERSON>"], [75, 35, 67, 33], [75, 36, 67, 34, "handlerTag"], [75, 46, 67, 44], [76, 6, 68, 4], [76, 7, 68, 5], [76, 8, 68, 6], [76, 14, 68, 12, "undefined"], [76, 23, 68, 21], [77, 4, 69, 2], [78, 4, 71, 2, "shouldRequireHandlerToWaitForFailure"], [78, 40, 71, 38, "shouldRequireHandlerToWaitForFailure"], [78, 41, 71, 39, "handler"], [78, 48, 71, 46], [78, 50, 71, 48, "<PERSON><PERSON><PERSON><PERSON>"], [78, 62, 71, 60], [78, 64, 71, 62], [79, 6, 72, 4], [79, 12, 72, 10, "waitFor"], [79, 19, 72, 17], [79, 22, 72, 20], [79, 26, 72, 24], [79, 27, 72, 25, "blocksHandlersRelations"], [79, 50, 72, 48], [79, 51, 72, 49, "get"], [79, 54, 72, 52], [79, 55, 72, 53, "handler"], [79, 62, 72, 60], [79, 63, 72, 61, "handlerTag"], [79, 73, 72, 71], [79, 74, 72, 72], [80, 6, 73, 4], [80, 13, 73, 11], [80, 14, 73, 12, "waitFor"], [80, 21, 73, 19], [80, 26, 73, 24], [80, 30, 73, 28], [80, 34, 73, 32, "waitFor"], [80, 41, 73, 39], [80, 46, 73, 44], [80, 51, 73, 49], [80, 52, 73, 50], [80, 55, 73, 53], [80, 60, 73, 58], [80, 61, 73, 59], [80, 64, 73, 62, "waitFor"], [80, 71, 73, 69], [80, 72, 73, 70, "find"], [80, 76, 73, 74], [80, 77, 73, 75, "tag"], [80, 80, 73, 78], [80, 84, 73, 82], [81, 8, 74, 6], [81, 15, 74, 13, "tag"], [81, 18, 74, 16], [81, 23, 74, 21, "<PERSON><PERSON><PERSON><PERSON>"], [81, 35, 74, 33], [81, 36, 74, 34, "handlerTag"], [81, 46, 74, 44], [82, 6, 75, 4], [82, 7, 75, 5], [82, 8, 75, 6], [82, 14, 75, 12, "undefined"], [82, 23, 75, 21], [83, 4, 76, 2], [84, 4, 78, 2, "shouldHandlerBeCancelledBy"], [84, 30, 78, 28, "shouldHandlerBeCancelledBy"], [84, 31, 78, 29, "_handler"], [84, 39, 78, 37], [84, 41, 78, 39, "<PERSON><PERSON><PERSON><PERSON>"], [84, 53, 78, 51], [84, 55, 78, 53], [85, 6, 79, 4], [85, 10, 79, 8, "_otherHandler$isButto"], [85, 31, 79, 29], [87, 6, 81, 4], [88, 6, 82, 4], [88, 12, 82, 10, "isNativeHandler"], [88, 27, 82, 25], [88, 30, 82, 28, "<PERSON><PERSON><PERSON><PERSON>"], [88, 42, 82, 40], [88, 43, 82, 41, "constructor"], [88, 54, 82, 52], [88, 55, 82, 53, "name"], [88, 59, 82, 57], [88, 64, 82, 62], [88, 90, 82, 88], [89, 6, 83, 4], [89, 12, 83, 10, "isActive"], [89, 20, 83, 18], [89, 23, 83, 21, "<PERSON><PERSON><PERSON><PERSON>"], [89, 35, 83, 33], [89, 36, 83, 34, "state"], [89, 41, 83, 39], [89, 46, 83, 44, "State"], [89, 58, 83, 49], [89, 59, 83, 50, "ACTIVE"], [89, 65, 83, 56], [90, 6, 84, 4], [90, 12, 84, 10, "isButton"], [90, 20, 84, 18], [90, 23, 84, 21], [90, 24, 84, 22], [90, 25, 84, 23, "_otherHandler$isButto"], [90, 46, 84, 44], [90, 49, 84, 47, "<PERSON><PERSON><PERSON><PERSON>"], [90, 61, 84, 59], [90, 62, 84, 60, "isButton"], [90, 70, 84, 68], [90, 76, 84, 74], [90, 80, 84, 78], [90, 84, 84, 82, "_otherHandler$isButto"], [90, 105, 84, 103], [90, 110, 84, 108], [90, 115, 84, 113], [90, 116, 84, 114], [90, 119, 84, 117], [90, 124, 84, 122], [90, 125, 84, 123], [90, 128, 84, 126, "_otherHandler$isButto"], [90, 149, 84, 147], [90, 150, 84, 148, "call"], [90, 154, 84, 152], [90, 155, 84, 153, "<PERSON><PERSON><PERSON><PERSON>"], [90, 167, 84, 165], [90, 168, 84, 166], [90, 174, 84, 172], [90, 178, 84, 176], [91, 6, 85, 4], [91, 13, 85, 11, "isNativeHandler"], [91, 28, 85, 26], [91, 32, 85, 30, "isActive"], [91, 40, 85, 38], [91, 44, 85, 42], [91, 45, 85, 43, "isButton"], [91, 53, 85, 51], [92, 4, 86, 2], [93, 4, 88, 2, "dropRelationsForHandlerWithTag"], [93, 34, 88, 32, "dropRelationsForHandlerWithTag"], [93, 35, 88, 33, "handlerTag"], [93, 45, 88, 43], [93, 47, 88, 45], [94, 6, 89, 4], [94, 10, 89, 8], [94, 11, 89, 9, "waitForRelations"], [94, 27, 89, 25], [94, 28, 89, 26, "delete"], [94, 34, 89, 32], [94, 35, 89, 33, "handlerTag"], [94, 45, 89, 43], [94, 46, 89, 44], [95, 6, 90, 4], [95, 10, 90, 8], [95, 11, 90, 9, "simultaneousRelations"], [95, 32, 90, 30], [95, 33, 90, 31, "delete"], [95, 39, 90, 37], [95, 40, 90, 38, "handlerTag"], [95, 50, 90, 48], [95, 51, 90, 49], [96, 6, 91, 4], [96, 10, 91, 8], [96, 11, 91, 9, "blocksHandlersRelations"], [96, 34, 91, 32], [96, 35, 91, 33, "delete"], [96, 41, 91, 39], [96, 42, 91, 40, "handlerTag"], [96, 52, 91, 50], [96, 53, 91, 51], [97, 4, 92, 2], [98, 4, 94, 2, "reset"], [98, 9, 94, 7, "reset"], [98, 10, 94, 7], [98, 12, 94, 10], [99, 6, 95, 4], [99, 10, 95, 8], [99, 11, 95, 9, "waitForRelations"], [99, 27, 95, 25], [99, 28, 95, 26, "clear"], [99, 33, 95, 31], [99, 34, 95, 32], [99, 35, 95, 33], [100, 6, 96, 4], [100, 10, 96, 8], [100, 11, 96, 9, "simultaneousRelations"], [100, 32, 96, 30], [100, 33, 96, 31, "clear"], [100, 38, 96, 36], [100, 39, 96, 37], [100, 40, 96, 38], [101, 6, 97, 4], [101, 10, 97, 8], [101, 11, 97, 9, "blocksHandlersRelations"], [101, 34, 97, 32], [101, 35, 97, 33, "clear"], [101, 40, 97, 38], [101, 41, 97, 39], [101, 42, 97, 40], [102, 4, 98, 2], [103, 4, 100, 2], [103, 15, 100, 13, "instance"], [103, 23, 100, 21, "instance"], [103, 24, 100, 21], [103, 26, 100, 24], [104, 6, 101, 4], [104, 10, 101, 8], [104, 11, 101, 9], [104, 15, 101, 13], [104, 16, 101, 14, "_instance"], [104, 25, 101, 23], [104, 27, 101, 25], [105, 8, 102, 6], [105, 12, 102, 10], [105, 13, 102, 11, "_instance"], [105, 22, 102, 20], [105, 25, 102, 23], [105, 29, 102, 27, "InteractionManager"], [105, 47, 102, 45], [105, 48, 102, 46], [105, 49, 102, 47], [106, 6, 103, 4], [107, 6, 105, 4], [107, 13, 105, 11], [107, 17, 105, 15], [107, 18, 105, 16, "_instance"], [107, 27, 105, 25], [108, 4, 106, 2], [109, 2, 108, 0], [110, 2, 108, 1, "exports"], [110, 9, 108, 1], [110, 10, 108, 1, "default"], [110, 17, 108, 1], [110, 20, 108, 1, "InteractionManager"], [110, 38, 108, 1], [111, 2, 110, 0, "_defineProperty"], [111, 17, 110, 15], [111, 18, 110, 16, "InteractionManager"], [111, 36, 110, 34], [111, 38, 110, 36], [111, 49, 110, 47], [111, 51, 110, 49], [111, 56, 110, 54], [111, 57, 110, 55], [111, 58, 110, 56], [112, 0, 110, 57], [112, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "InteractionManager", "constructor", "configureInteractions", "config.waitFor.forEach$argument_0", "config.simultaneousHandlers.forEach$argument_0", "config.blocksHandlers.forEach$argument_0", "shouldWaitForHandlerFailure", "waitFor.find$argument_0", "shouldRecognizeSimultaneously", "simultaneousHandlers.find$argument_0", "shouldRequireHandlerToWaitForFailure", "shouldHandlerBeCancelledBy", "dropRelationsForHandlerWithTag", "reset", "get__instance"], "mappings": "AAA,iNC;eCG;ECG;GDM;EEE;6BCK;ODQ;0CEM;OFM;oCGM;OHM;GFG;EME;2ECE;KDE;GNC;EQE;kHCE;KDE;GRC;EUE;2EHE;KGE;GVC;EWE;GXQ;EYE;GZI;EaE;GbI;EcE;GdM;CDE"}}, "type": "js/module"}]}