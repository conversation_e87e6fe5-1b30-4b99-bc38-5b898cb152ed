{"dependencies": [{"name": "./matchers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 19, "index": 184}, "end": {"line": 5, "column": 40, "index": 205}}], "key": "89ylKT57ef0l7ma8+p1HhPaMj94=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.sortRoutes = sortRoutes;\n  exports.sortRoutesWithInitial = sortRoutesWithInitial;\n  const matchers_1 = require(_dependencyMap[0], \"./matchers\");\n  function sortDynamicConvention(a, b) {\n    if (a.deep && !b.deep) {\n      return 1;\n    }\n    if (!a.deep && b.deep) {\n      return -1;\n    }\n    return 0;\n  }\n  function sortRoutes(a, b) {\n    if (a.dynamic && !b.dynamic) {\n      return 1;\n    }\n    if (!a.dynamic && b.dynamic) {\n      return -1;\n    }\n    if (a.dynamic && b.dynamic) {\n      if (a.dynamic.length !== b.dynamic.length) {\n        return b.dynamic.length - a.dynamic.length;\n      }\n      for (let i = 0; i < a.dynamic.length; i++) {\n        const aDynamic = a.dynamic[i];\n        const bDynamic = b.dynamic[i];\n        if (aDynamic.notFound && bDynamic.notFound) {\n          const s = sortDynamicConvention(aDynamic, bDynamic);\n          if (s) {\n            return s;\n          }\n        }\n        if (aDynamic.notFound && !bDynamic.notFound) {\n          return 1;\n        }\n        if (!aDynamic.notFound && bDynamic.notFound) {\n          return -1;\n        }\n        const s = sortDynamicConvention(aDynamic, bDynamic);\n        if (s) {\n          return s;\n        }\n      }\n      return 0;\n    }\n    const aIndex = a.route === 'index' || (0, matchers_1.matchGroupName)(a.route) != null;\n    const bIndex = b.route === 'index' || (0, matchers_1.matchGroupName)(b.route) != null;\n    if (aIndex && !bIndex) {\n      return -1;\n    }\n    if (!aIndex && bIndex) {\n      return 1;\n    }\n    return a.route.length - b.route.length;\n  }\n  function sortRoutesWithInitial(initialRouteName) {\n    return (a, b) => {\n      if (initialRouteName) {\n        if (a.route === initialRouteName) {\n          return -1;\n        }\n        if (b.route === initialRouteName) {\n          return 1;\n        }\n      }\n      return sortRoutes(a, b);\n    };\n  }\n});", "lineCount": 75, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "sortRoutes"], [7, 20, 3, 18], [7, 23, 3, 21, "sortRoutes"], [7, 33, 3, 31], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "sortRoutesWithInitial"], [8, 31, 4, 29], [8, 34, 4, 32, "sortRoutesWithInitial"], [8, 55, 4, 53], [9, 2, 5, 0], [9, 8, 5, 6, "matchers_1"], [9, 18, 5, 16], [9, 21, 5, 19, "require"], [9, 28, 5, 26], [9, 29, 5, 26, "_dependencyMap"], [9, 43, 5, 26], [9, 60, 5, 39], [9, 61, 5, 40], [10, 2, 6, 0], [10, 11, 6, 9, "sortDynamicConvention"], [10, 32, 6, 30, "sortDynamicConvention"], [10, 33, 6, 31, "a"], [10, 34, 6, 32], [10, 36, 6, 34, "b"], [10, 37, 6, 35], [10, 39, 6, 37], [11, 4, 7, 4], [11, 8, 7, 8, "a"], [11, 9, 7, 9], [11, 10, 7, 10, "deep"], [11, 14, 7, 14], [11, 18, 7, 18], [11, 19, 7, 19, "b"], [11, 20, 7, 20], [11, 21, 7, 21, "deep"], [11, 25, 7, 25], [11, 27, 7, 27], [12, 6, 8, 8], [12, 13, 8, 15], [12, 14, 8, 16], [13, 4, 9, 4], [14, 4, 10, 4], [14, 8, 10, 8], [14, 9, 10, 9, "a"], [14, 10, 10, 10], [14, 11, 10, 11, "deep"], [14, 15, 10, 15], [14, 19, 10, 19, "b"], [14, 20, 10, 20], [14, 21, 10, 21, "deep"], [14, 25, 10, 25], [14, 27, 10, 27], [15, 6, 11, 8], [15, 13, 11, 15], [15, 14, 11, 16], [15, 15, 11, 17], [16, 4, 12, 4], [17, 4, 13, 4], [17, 11, 13, 11], [17, 12, 13, 12], [18, 2, 14, 0], [19, 2, 15, 0], [19, 11, 15, 9, "sortRoutes"], [19, 21, 15, 19, "sortRoutes"], [19, 22, 15, 20, "a"], [19, 23, 15, 21], [19, 25, 15, 23, "b"], [19, 26, 15, 24], [19, 28, 15, 26], [20, 4, 16, 4], [20, 8, 16, 8, "a"], [20, 9, 16, 9], [20, 10, 16, 10, "dynamic"], [20, 17, 16, 17], [20, 21, 16, 21], [20, 22, 16, 22, "b"], [20, 23, 16, 23], [20, 24, 16, 24, "dynamic"], [20, 31, 16, 31], [20, 33, 16, 33], [21, 6, 17, 8], [21, 13, 17, 15], [21, 14, 17, 16], [22, 4, 18, 4], [23, 4, 19, 4], [23, 8, 19, 8], [23, 9, 19, 9, "a"], [23, 10, 19, 10], [23, 11, 19, 11, "dynamic"], [23, 18, 19, 18], [23, 22, 19, 22, "b"], [23, 23, 19, 23], [23, 24, 19, 24, "dynamic"], [23, 31, 19, 31], [23, 33, 19, 33], [24, 6, 20, 8], [24, 13, 20, 15], [24, 14, 20, 16], [24, 15, 20, 17], [25, 4, 21, 4], [26, 4, 22, 4], [26, 8, 22, 8, "a"], [26, 9, 22, 9], [26, 10, 22, 10, "dynamic"], [26, 17, 22, 17], [26, 21, 22, 21, "b"], [26, 22, 22, 22], [26, 23, 22, 23, "dynamic"], [26, 30, 22, 30], [26, 32, 22, 32], [27, 6, 23, 8], [27, 10, 23, 12, "a"], [27, 11, 23, 13], [27, 12, 23, 14, "dynamic"], [27, 19, 23, 21], [27, 20, 23, 22, "length"], [27, 26, 23, 28], [27, 31, 23, 33, "b"], [27, 32, 23, 34], [27, 33, 23, 35, "dynamic"], [27, 40, 23, 42], [27, 41, 23, 43, "length"], [27, 47, 23, 49], [27, 49, 23, 51], [28, 8, 24, 12], [28, 15, 24, 19, "b"], [28, 16, 24, 20], [28, 17, 24, 21, "dynamic"], [28, 24, 24, 28], [28, 25, 24, 29, "length"], [28, 31, 24, 35], [28, 34, 24, 38, "a"], [28, 35, 24, 39], [28, 36, 24, 40, "dynamic"], [28, 43, 24, 47], [28, 44, 24, 48, "length"], [28, 50, 24, 54], [29, 6, 25, 8], [30, 6, 26, 8], [30, 11, 26, 13], [30, 15, 26, 17, "i"], [30, 16, 26, 18], [30, 19, 26, 21], [30, 20, 26, 22], [30, 22, 26, 24, "i"], [30, 23, 26, 25], [30, 26, 26, 28, "a"], [30, 27, 26, 29], [30, 28, 26, 30, "dynamic"], [30, 35, 26, 37], [30, 36, 26, 38, "length"], [30, 42, 26, 44], [30, 44, 26, 46, "i"], [30, 45, 26, 47], [30, 47, 26, 49], [30, 49, 26, 51], [31, 8, 27, 12], [31, 14, 27, 18, "aDynamic"], [31, 22, 27, 26], [31, 25, 27, 29, "a"], [31, 26, 27, 30], [31, 27, 27, 31, "dynamic"], [31, 34, 27, 38], [31, 35, 27, 39, "i"], [31, 36, 27, 40], [31, 37, 27, 41], [32, 8, 28, 12], [32, 14, 28, 18, "bDynamic"], [32, 22, 28, 26], [32, 25, 28, 29, "b"], [32, 26, 28, 30], [32, 27, 28, 31, "dynamic"], [32, 34, 28, 38], [32, 35, 28, 39, "i"], [32, 36, 28, 40], [32, 37, 28, 41], [33, 8, 29, 12], [33, 12, 29, 16, "aDynamic"], [33, 20, 29, 24], [33, 21, 29, 25, "notFound"], [33, 29, 29, 33], [33, 33, 29, 37, "bDynamic"], [33, 41, 29, 45], [33, 42, 29, 46, "notFound"], [33, 50, 29, 54], [33, 52, 29, 56], [34, 10, 30, 16], [34, 16, 30, 22, "s"], [34, 17, 30, 23], [34, 20, 30, 26, "sortDynamicConvention"], [34, 41, 30, 47], [34, 42, 30, 48, "aDynamic"], [34, 50, 30, 56], [34, 52, 30, 58, "bDynamic"], [34, 60, 30, 66], [34, 61, 30, 67], [35, 10, 31, 16], [35, 14, 31, 20, "s"], [35, 15, 31, 21], [35, 17, 31, 23], [36, 12, 32, 20], [36, 19, 32, 27, "s"], [36, 20, 32, 28], [37, 10, 33, 16], [38, 8, 34, 12], [39, 8, 35, 12], [39, 12, 35, 16, "aDynamic"], [39, 20, 35, 24], [39, 21, 35, 25, "notFound"], [39, 29, 35, 33], [39, 33, 35, 37], [39, 34, 35, 38, "bDynamic"], [39, 42, 35, 46], [39, 43, 35, 47, "notFound"], [39, 51, 35, 55], [39, 53, 35, 57], [40, 10, 36, 16], [40, 17, 36, 23], [40, 18, 36, 24], [41, 8, 37, 12], [42, 8, 38, 12], [42, 12, 38, 16], [42, 13, 38, 17, "aDynamic"], [42, 21, 38, 25], [42, 22, 38, 26, "notFound"], [42, 30, 38, 34], [42, 34, 38, 38, "bDynamic"], [42, 42, 38, 46], [42, 43, 38, 47, "notFound"], [42, 51, 38, 55], [42, 53, 38, 57], [43, 10, 39, 16], [43, 17, 39, 23], [43, 18, 39, 24], [43, 19, 39, 25], [44, 8, 40, 12], [45, 8, 41, 12], [45, 14, 41, 18, "s"], [45, 15, 41, 19], [45, 18, 41, 22, "sortDynamicConvention"], [45, 39, 41, 43], [45, 40, 41, 44, "aDynamic"], [45, 48, 41, 52], [45, 50, 41, 54, "bDynamic"], [45, 58, 41, 62], [45, 59, 41, 63], [46, 8, 42, 12], [46, 12, 42, 16, "s"], [46, 13, 42, 17], [46, 15, 42, 19], [47, 10, 43, 16], [47, 17, 43, 23, "s"], [47, 18, 43, 24], [48, 8, 44, 12], [49, 6, 45, 8], [50, 6, 46, 8], [50, 13, 46, 15], [50, 14, 46, 16], [51, 4, 47, 4], [52, 4, 48, 4], [52, 10, 48, 10, "aIndex"], [52, 16, 48, 16], [52, 19, 48, 19, "a"], [52, 20, 48, 20], [52, 21, 48, 21, "route"], [52, 26, 48, 26], [52, 31, 48, 31], [52, 38, 48, 38], [52, 42, 48, 42], [52, 43, 48, 43], [52, 44, 48, 44], [52, 46, 48, 46, "matchers_1"], [52, 56, 48, 56], [52, 57, 48, 57, "matchGroupName"], [52, 71, 48, 71], [52, 73, 48, 73, "a"], [52, 74, 48, 74], [52, 75, 48, 75, "route"], [52, 80, 48, 80], [52, 81, 48, 81], [52, 85, 48, 85], [52, 89, 48, 89], [53, 4, 49, 4], [53, 10, 49, 10, "bIndex"], [53, 16, 49, 16], [53, 19, 49, 19, "b"], [53, 20, 49, 20], [53, 21, 49, 21, "route"], [53, 26, 49, 26], [53, 31, 49, 31], [53, 38, 49, 38], [53, 42, 49, 42], [53, 43, 49, 43], [53, 44, 49, 44], [53, 46, 49, 46, "matchers_1"], [53, 56, 49, 56], [53, 57, 49, 57, "matchGroupName"], [53, 71, 49, 71], [53, 73, 49, 73, "b"], [53, 74, 49, 74], [53, 75, 49, 75, "route"], [53, 80, 49, 80], [53, 81, 49, 81], [53, 85, 49, 85], [53, 89, 49, 89], [54, 4, 50, 4], [54, 8, 50, 8, "aIndex"], [54, 14, 50, 14], [54, 18, 50, 18], [54, 19, 50, 19, "bIndex"], [54, 25, 50, 25], [54, 27, 50, 27], [55, 6, 51, 8], [55, 13, 51, 15], [55, 14, 51, 16], [55, 15, 51, 17], [56, 4, 52, 4], [57, 4, 53, 4], [57, 8, 53, 8], [57, 9, 53, 9, "aIndex"], [57, 15, 53, 15], [57, 19, 53, 19, "bIndex"], [57, 25, 53, 25], [57, 27, 53, 27], [58, 6, 54, 8], [58, 13, 54, 15], [58, 14, 54, 16], [59, 4, 55, 4], [60, 4, 56, 4], [60, 11, 56, 11, "a"], [60, 12, 56, 12], [60, 13, 56, 13, "route"], [60, 18, 56, 18], [60, 19, 56, 19, "length"], [60, 25, 56, 25], [60, 28, 56, 28, "b"], [60, 29, 56, 29], [60, 30, 56, 30, "route"], [60, 35, 56, 35], [60, 36, 56, 36, "length"], [60, 42, 56, 42], [61, 2, 57, 0], [62, 2, 58, 0], [62, 11, 58, 9, "sortRoutesWithInitial"], [62, 32, 58, 30, "sortRoutesWithInitial"], [62, 33, 58, 31, "initialRouteName"], [62, 49, 58, 47], [62, 51, 58, 49], [63, 4, 59, 4], [63, 11, 59, 11], [63, 12, 59, 12, "a"], [63, 13, 59, 13], [63, 15, 59, 15, "b"], [63, 16, 59, 16], [63, 21, 59, 21], [64, 6, 60, 8], [64, 10, 60, 12, "initialRouteName"], [64, 26, 60, 28], [64, 28, 60, 30], [65, 8, 61, 12], [65, 12, 61, 16, "a"], [65, 13, 61, 17], [65, 14, 61, 18, "route"], [65, 19, 61, 23], [65, 24, 61, 28, "initialRouteName"], [65, 40, 61, 44], [65, 42, 61, 46], [66, 10, 62, 16], [66, 17, 62, 23], [66, 18, 62, 24], [66, 19, 62, 25], [67, 8, 63, 12], [68, 8, 64, 12], [68, 12, 64, 16, "b"], [68, 13, 64, 17], [68, 14, 64, 18, "route"], [68, 19, 64, 23], [68, 24, 64, 28, "initialRouteName"], [68, 40, 64, 44], [68, 42, 64, 46], [69, 10, 65, 16], [69, 17, 65, 23], [69, 18, 65, 24], [70, 8, 66, 12], [71, 6, 67, 8], [72, 6, 68, 8], [72, 13, 68, 15, "sortRoutes"], [72, 23, 68, 25], [72, 24, 68, 26, "a"], [72, 25, 68, 27], [72, 27, 68, 29, "b"], [72, 28, 68, 30], [72, 29, 68, 31], [73, 4, 69, 4], [73, 5, 69, 5], [74, 2, 70, 0], [75, 0, 70, 1], [75, 3]], "functionMap": {"names": ["<global>", "sortDynamicConvention", "sortRoutes", "sortRoutesWithInitial", "<anonymous>"], "mappings": "AAA;ACK;CDQ;AEC;CF0C;AGC;WCC;KDU;CHC"}}, "type": "js/module"}]}