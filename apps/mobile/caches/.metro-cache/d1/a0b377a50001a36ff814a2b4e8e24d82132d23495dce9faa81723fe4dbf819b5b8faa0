{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 7, "column": 57, "index": 216}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 217}, "end": {"line": 8, "column": 52, "index": 269}}], "key": "2ER/r3Agt+5SFwaFR8HXg24Rpu4=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/with-selector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 270}, "end": {"line": 9, "column": 89, "index": 359}}], "key": "eWOvQ07XtQMBjXiY0qREKFi+uR8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 499}, "end": {"line": 13, "column": 86, "index": 585}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 25, "index": 611}, "end": {"line": 14, "column": 66, "index": 652}}], "key": "6pHRDUl9j7DHzZ/OfZoTArvVaDg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FrameSizeProvider = FrameSizeProvider;\n  exports.useFrameSize = useFrameSize;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  var _useLatestCallback = _interopRequireDefault(require(_dependencyMap[5], \"use-latest-callback\"));\n  var _withSelector = require(_dependencyMap[6], \"use-sync-external-store/with-selector\");\n  var _jsxRuntime = require(_dependencyMap[7], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  // Load with require to avoid error from webpack due to missing export in older versions\n  // eslint-disable-next-line import-x/no-commonjs\n\n  const SafeAreaListener = require(_dependencyMap[8], \"react-native-safe-area-context\").SafeAreaListener;\n  const FrameContext = /*#__PURE__*/React.createContext(undefined);\n  function useFrameSize(selector, throttle) {\n    const context = React.useContext(FrameContext);\n    if (context == null) {\n      throw new Error('useFrameSize must be used within a FrameSizeProvider');\n    }\n    const value = (0, _withSelector.useSyncExternalStoreWithSelector)(throttle ? context.subscribeThrottled : context.subscribe, context.getCurrent, context.getCurrent, selector);\n    return value;\n  }\n  function FrameSizeProvider({\n    initialFrame,\n    children\n  }) {\n    const context = React.useContext(FrameContext);\n    if (context != null) {\n      // If the context is already present, don't wrap again\n      return children;\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(FrameSizeProviderInner, {\n      initialFrame: initialFrame,\n      children: children\n    });\n  }\n  function FrameSizeProviderInner({\n    initialFrame,\n    children\n  }) {\n    const frameRef = React.useRef({\n      width: initialFrame.width,\n      height: initialFrame.height\n    });\n    const listeners = React.useRef(new Set());\n    const getCurrent = (0, _useLatestCallback.default)(() => frameRef.current);\n    const subscribe = (0, _useLatestCallback.default)(listener => {\n      listeners.current.add(listener);\n      return () => {\n        listeners.current.delete(listener);\n      };\n    });\n    const subscribeThrottled = (0, _useLatestCallback.default)(listener => {\n      const delay = 100; // Throttle delay in milliseconds\n\n      let timer;\n      let updated = false;\n      let waiting = false;\n      const throttledListener = () => {\n        clearTimeout(timer);\n        updated = true;\n        if (waiting) {\n          // Schedule a timer to call the listener at the end\n          timer = setTimeout(() => {\n            if (updated) {\n              updated = false;\n              listener();\n            }\n          }, delay);\n        } else {\n          waiting = true;\n          setTimeout(function () {\n            waiting = false;\n          }, delay);\n\n          // Call the listener immediately at start\n          updated = false;\n          listener();\n        }\n      };\n      const unsubscribe = subscribe(throttledListener);\n      return () => {\n        unsubscribe();\n        clearTimeout(timer);\n      };\n    });\n    const context = React.useMemo(() => ({\n      getCurrent,\n      subscribe,\n      subscribeThrottled\n    }), [subscribe, subscribeThrottled, getCurrent]);\n    const onChange = (0, _useLatestCallback.default)(frame => {\n      if (frameRef.current.height === frame.height && frameRef.current.width === frame.width) {\n        return;\n      }\n      frameRef.current = {\n        width: frame.width,\n        height: frame.height\n      };\n      listeners.current.forEach(listener => listener());\n    });\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n      children: [_Platform.default.OS === 'web' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(FrameSizeListenerWeb, {\n        onChange: onChange\n      }) : typeof SafeAreaListener === 'undefined' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(FrameSizeListenerNativeFallback, {\n        onChange: onChange\n      }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(SafeAreaListener, {\n        onChange: ({\n          frame\n        }) => onChange(frame),\n        style: _StyleSheet.default.absoluteFill\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(FrameContext.Provider, {\n        value: context,\n        children: children\n      })]\n    });\n  }\n\n  // SafeAreaListener is available only on newer versions\n  // Fallback to an effect-based shim for older versions\n  function FrameSizeListenerNativeFallback({\n    onChange\n  }) {\n    const frame = (0, _reactNativeSafeAreaContext.useSafeAreaFrame)();\n    React.useLayoutEffect(() => {\n      onChange(frame);\n    }, [frame, onChange]);\n    return null;\n  }\n\n  // FIXME: On the Web, the safe area frame value doesn't update on resize\n  // So we workaround this by measuring the frame on resize\n  function FrameSizeListenerWeb({\n    onChange\n  }) {\n    const elementRef = React.useRef(null);\n    React.useEffect(() => {\n      if (elementRef.current == null) {\n        return;\n      }\n      const rect = elementRef.current.getBoundingClientRect();\n      onChange({\n        width: rect.width,\n        height: rect.height\n      });\n      const observer = new ResizeObserver(entries => {\n        const entry = entries[0];\n        if (entry) {\n          const {\n            width,\n            height\n          } = entry.contentRect;\n          onChange({\n            width,\n            height\n          });\n        }\n      });\n      observer.observe(elementRef.current);\n      return () => {\n        observer.disconnect();\n      };\n    }, [onChange]);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(\"div\", {\n      ref: elementRef,\n      style: {\n        ..._StyleSheet.default.absoluteFillObject,\n        pointerEvents: 'none',\n        visibility: 'hidden'\n      }\n    });\n  }\n});", "lineCount": 181, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "FrameSizeProvider"], [8, 27, 1, 13], [8, 30, 1, 13, "FrameSizeProvider"], [8, 47, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "useFrameSize"], [9, 22, 1, 13], [9, 25, 1, 13, "useFrameSize"], [9, 37, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 3, 31], [11, 6, 3, 31, "_Platform"], [11, 15, 3, 31], [11, 18, 3, 31, "_interopRequireDefault"], [11, 40, 3, 31], [11, 41, 3, 31, "require"], [11, 48, 3, 31], [11, 49, 3, 31, "_dependencyMap"], [11, 63, 3, 31], [12, 2, 3, 31], [12, 6, 3, 31, "_StyleSheet"], [12, 17, 3, 31], [12, 20, 3, 31, "_interopRequireDefault"], [12, 42, 3, 31], [12, 43, 3, 31, "require"], [12, 50, 3, 31], [12, 51, 3, 31, "_dependencyMap"], [12, 65, 3, 31], [13, 2, 5, 0], [13, 6, 5, 0, "_reactNativeSafeAreaContext"], [13, 33, 5, 0], [13, 36, 5, 0, "require"], [13, 43, 5, 0], [13, 44, 5, 0, "_dependencyMap"], [13, 58, 5, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_useLatestCallback"], [14, 24, 8, 0], [14, 27, 8, 0, "_interopRequireDefault"], [14, 49, 8, 0], [14, 50, 8, 0, "require"], [14, 57, 8, 0], [14, 58, 8, 0, "_dependencyMap"], [14, 72, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_withSelector"], [15, 19, 9, 0], [15, 22, 9, 0, "require"], [15, 29, 9, 0], [15, 30, 9, 0, "_dependencyMap"], [15, 44, 9, 0], [16, 2, 13, 0], [16, 6, 13, 0, "_jsxRuntime"], [16, 17, 13, 0], [16, 20, 13, 0, "require"], [16, 27, 13, 0], [16, 28, 13, 0, "_dependencyMap"], [16, 42, 13, 0], [17, 2, 13, 86], [17, 11, 13, 86, "_interopRequireWildcard"], [17, 35, 13, 86, "e"], [17, 36, 13, 86], [17, 38, 13, 86, "t"], [17, 39, 13, 86], [17, 68, 13, 86, "WeakMap"], [17, 75, 13, 86], [17, 81, 13, 86, "r"], [17, 82, 13, 86], [17, 89, 13, 86, "WeakMap"], [17, 96, 13, 86], [17, 100, 13, 86, "n"], [17, 101, 13, 86], [17, 108, 13, 86, "WeakMap"], [17, 115, 13, 86], [17, 127, 13, 86, "_interopRequireWildcard"], [17, 150, 13, 86], [17, 162, 13, 86, "_interopRequireWildcard"], [17, 163, 13, 86, "e"], [17, 164, 13, 86], [17, 166, 13, 86, "t"], [17, 167, 13, 86], [17, 176, 13, 86, "t"], [17, 177, 13, 86], [17, 181, 13, 86, "e"], [17, 182, 13, 86], [17, 186, 13, 86, "e"], [17, 187, 13, 86], [17, 188, 13, 86, "__esModule"], [17, 198, 13, 86], [17, 207, 13, 86, "e"], [17, 208, 13, 86], [17, 214, 13, 86, "o"], [17, 215, 13, 86], [17, 217, 13, 86, "i"], [17, 218, 13, 86], [17, 220, 13, 86, "f"], [17, 221, 13, 86], [17, 226, 13, 86, "__proto__"], [17, 235, 13, 86], [17, 243, 13, 86, "default"], [17, 250, 13, 86], [17, 252, 13, 86, "e"], [17, 253, 13, 86], [17, 270, 13, 86, "e"], [17, 271, 13, 86], [17, 294, 13, 86, "e"], [17, 295, 13, 86], [17, 320, 13, 86, "e"], [17, 321, 13, 86], [17, 330, 13, 86, "f"], [17, 331, 13, 86], [17, 337, 13, 86, "o"], [17, 338, 13, 86], [17, 341, 13, 86, "t"], [17, 342, 13, 86], [17, 345, 13, 86, "n"], [17, 346, 13, 86], [17, 349, 13, 86, "r"], [17, 350, 13, 86], [17, 358, 13, 86, "o"], [17, 359, 13, 86], [17, 360, 13, 86, "has"], [17, 363, 13, 86], [17, 364, 13, 86, "e"], [17, 365, 13, 86], [17, 375, 13, 86, "o"], [17, 376, 13, 86], [17, 377, 13, 86, "get"], [17, 380, 13, 86], [17, 381, 13, 86, "e"], [17, 382, 13, 86], [17, 385, 13, 86, "o"], [17, 386, 13, 86], [17, 387, 13, 86, "set"], [17, 390, 13, 86], [17, 391, 13, 86, "e"], [17, 392, 13, 86], [17, 394, 13, 86, "f"], [17, 395, 13, 86], [17, 411, 13, 86, "t"], [17, 412, 13, 86], [17, 416, 13, 86, "e"], [17, 417, 13, 86], [17, 433, 13, 86, "t"], [17, 434, 13, 86], [17, 441, 13, 86, "hasOwnProperty"], [17, 455, 13, 86], [17, 456, 13, 86, "call"], [17, 460, 13, 86], [17, 461, 13, 86, "e"], [17, 462, 13, 86], [17, 464, 13, 86, "t"], [17, 465, 13, 86], [17, 472, 13, 86, "i"], [17, 473, 13, 86], [17, 477, 13, 86, "o"], [17, 478, 13, 86], [17, 481, 13, 86, "Object"], [17, 487, 13, 86], [17, 488, 13, 86, "defineProperty"], [17, 502, 13, 86], [17, 507, 13, 86, "Object"], [17, 513, 13, 86], [17, 514, 13, 86, "getOwnPropertyDescriptor"], [17, 538, 13, 86], [17, 539, 13, 86, "e"], [17, 540, 13, 86], [17, 542, 13, 86, "t"], [17, 543, 13, 86], [17, 550, 13, 86, "i"], [17, 551, 13, 86], [17, 552, 13, 86, "get"], [17, 555, 13, 86], [17, 559, 13, 86, "i"], [17, 560, 13, 86], [17, 561, 13, 86, "set"], [17, 564, 13, 86], [17, 568, 13, 86, "o"], [17, 569, 13, 86], [17, 570, 13, 86, "f"], [17, 571, 13, 86], [17, 573, 13, 86, "t"], [17, 574, 13, 86], [17, 576, 13, 86, "i"], [17, 577, 13, 86], [17, 581, 13, 86, "f"], [17, 582, 13, 86], [17, 583, 13, 86, "t"], [17, 584, 13, 86], [17, 588, 13, 86, "e"], [17, 589, 13, 86], [17, 590, 13, 86, "t"], [17, 591, 13, 86], [17, 602, 13, 86, "f"], [17, 603, 13, 86], [17, 608, 13, 86, "e"], [17, 609, 13, 86], [17, 611, 13, 86, "t"], [17, 612, 13, 86], [18, 2, 11, 0], [19, 2, 12, 0], [21, 2, 14, 0], [21, 8, 14, 6, "SafeAreaListener"], [21, 24, 14, 22], [21, 27, 14, 25, "require"], [21, 34, 14, 32], [21, 35, 14, 32, "_dependencyMap"], [21, 49, 14, 32], [21, 86, 14, 65], [21, 87, 14, 66], [21, 88, 14, 67, "SafeAreaListener"], [21, 104, 14, 83], [22, 2, 15, 0], [22, 8, 15, 6, "FrameContext"], [22, 20, 15, 18], [22, 23, 15, 21], [22, 36, 15, 34, "React"], [22, 41, 15, 39], [22, 42, 15, 40, "createContext"], [22, 55, 15, 53], [22, 56, 15, 54, "undefined"], [22, 65, 15, 63], [22, 66, 15, 64], [23, 2, 16, 7], [23, 11, 16, 16, "useFrameSize"], [23, 23, 16, 28, "useFrameSize"], [23, 24, 16, 29, "selector"], [23, 32, 16, 37], [23, 34, 16, 39, "throttle"], [23, 42, 16, 47], [23, 44, 16, 49], [24, 4, 17, 2], [24, 10, 17, 8, "context"], [24, 17, 17, 15], [24, 20, 17, 18, "React"], [24, 25, 17, 23], [24, 26, 17, 24, "useContext"], [24, 36, 17, 34], [24, 37, 17, 35, "FrameContext"], [24, 49, 17, 47], [24, 50, 17, 48], [25, 4, 18, 2], [25, 8, 18, 6, "context"], [25, 15, 18, 13], [25, 19, 18, 17], [25, 23, 18, 21], [25, 25, 18, 23], [26, 6, 19, 4], [26, 12, 19, 10], [26, 16, 19, 14, "Error"], [26, 21, 19, 19], [26, 22, 19, 20], [26, 76, 19, 74], [26, 77, 19, 75], [27, 4, 20, 2], [28, 4, 21, 2], [28, 10, 21, 8, "value"], [28, 15, 21, 13], [28, 18, 21, 16], [28, 22, 21, 16, "useSyncExternalStoreWithSelector"], [28, 68, 21, 48], [28, 70, 21, 49, "throttle"], [28, 78, 21, 57], [28, 81, 21, 60, "context"], [28, 88, 21, 67], [28, 89, 21, 68, "subscribeThrottled"], [28, 107, 21, 86], [28, 110, 21, 89, "context"], [28, 117, 21, 96], [28, 118, 21, 97, "subscribe"], [28, 127, 21, 106], [28, 129, 21, 108, "context"], [28, 136, 21, 115], [28, 137, 21, 116, "get<PERSON>urrent"], [28, 147, 21, 126], [28, 149, 21, 128, "context"], [28, 156, 21, 135], [28, 157, 21, 136, "get<PERSON>urrent"], [28, 167, 21, 146], [28, 169, 21, 148, "selector"], [28, 177, 21, 156], [28, 178, 21, 157], [29, 4, 22, 2], [29, 11, 22, 9, "value"], [29, 16, 22, 14], [30, 2, 23, 0], [31, 2, 24, 7], [31, 11, 24, 16, "FrameSizeProvider"], [31, 28, 24, 33, "FrameSizeProvider"], [31, 29, 24, 34], [32, 4, 25, 2, "initialFrame"], [32, 16, 25, 14], [33, 4, 26, 2, "children"], [34, 2, 27, 0], [34, 3, 27, 1], [34, 5, 27, 3], [35, 4, 28, 2], [35, 10, 28, 8, "context"], [35, 17, 28, 15], [35, 20, 28, 18, "React"], [35, 25, 28, 23], [35, 26, 28, 24, "useContext"], [35, 36, 28, 34], [35, 37, 28, 35, "FrameContext"], [35, 49, 28, 47], [35, 50, 28, 48], [36, 4, 29, 2], [36, 8, 29, 6, "context"], [36, 15, 29, 13], [36, 19, 29, 17], [36, 23, 29, 21], [36, 25, 29, 23], [37, 6, 30, 4], [38, 6, 31, 4], [38, 13, 31, 11, "children"], [38, 21, 31, 19], [39, 4, 32, 2], [40, 4, 33, 2], [40, 11, 33, 9], [40, 24, 33, 22], [40, 28, 33, 22, "_jsx"], [40, 43, 33, 26], [40, 45, 33, 27, "FrameSizeProviderInner"], [40, 67, 33, 49], [40, 69, 33, 51], [41, 6, 34, 4, "initialFrame"], [41, 18, 34, 16], [41, 20, 34, 18, "initialFrame"], [41, 32, 34, 30], [42, 6, 35, 4, "children"], [42, 14, 35, 12], [42, 16, 35, 14, "children"], [43, 4, 36, 2], [43, 5, 36, 3], [43, 6, 36, 4], [44, 2, 37, 0], [45, 2, 38, 0], [45, 11, 38, 9, "FrameSizeProviderInner"], [45, 33, 38, 31, "FrameSizeProviderInner"], [45, 34, 38, 32], [46, 4, 39, 2, "initialFrame"], [46, 16, 39, 14], [47, 4, 40, 2, "children"], [48, 2, 41, 0], [48, 3, 41, 1], [48, 5, 41, 3], [49, 4, 42, 2], [49, 10, 42, 8, "frameRef"], [49, 18, 42, 16], [49, 21, 42, 19, "React"], [49, 26, 42, 24], [49, 27, 42, 25, "useRef"], [49, 33, 42, 31], [49, 34, 42, 32], [50, 6, 43, 4, "width"], [50, 11, 43, 9], [50, 13, 43, 11, "initialFrame"], [50, 25, 43, 23], [50, 26, 43, 24, "width"], [50, 31, 43, 29], [51, 6, 44, 4, "height"], [51, 12, 44, 10], [51, 14, 44, 12, "initialFrame"], [51, 26, 44, 24], [51, 27, 44, 25, "height"], [52, 4, 45, 2], [52, 5, 45, 3], [52, 6, 45, 4], [53, 4, 46, 2], [53, 10, 46, 8, "listeners"], [53, 19, 46, 17], [53, 22, 46, 20, "React"], [53, 27, 46, 25], [53, 28, 46, 26, "useRef"], [53, 34, 46, 32], [53, 35, 46, 33], [53, 39, 46, 37, "Set"], [53, 42, 46, 40], [53, 43, 46, 41], [53, 44, 46, 42], [53, 45, 46, 43], [54, 4, 47, 2], [54, 10, 47, 8, "get<PERSON>urrent"], [54, 20, 47, 18], [54, 23, 47, 21], [54, 27, 47, 21, "useLatestCallback"], [54, 53, 47, 38], [54, 55, 47, 39], [54, 61, 47, 45, "frameRef"], [54, 69, 47, 53], [54, 70, 47, 54, "current"], [54, 77, 47, 61], [54, 78, 47, 62], [55, 4, 48, 2], [55, 10, 48, 8, "subscribe"], [55, 19, 48, 17], [55, 22, 48, 20], [55, 26, 48, 20, "useLatestCallback"], [55, 52, 48, 37], [55, 54, 48, 38, "listener"], [55, 62, 48, 46], [55, 66, 48, 50], [56, 6, 49, 4, "listeners"], [56, 15, 49, 13], [56, 16, 49, 14, "current"], [56, 23, 49, 21], [56, 24, 49, 22, "add"], [56, 27, 49, 25], [56, 28, 49, 26, "listener"], [56, 36, 49, 34], [56, 37, 49, 35], [57, 6, 50, 4], [57, 13, 50, 11], [57, 19, 50, 17], [58, 8, 51, 6, "listeners"], [58, 17, 51, 15], [58, 18, 51, 16, "current"], [58, 25, 51, 23], [58, 26, 51, 24, "delete"], [58, 32, 51, 30], [58, 33, 51, 31, "listener"], [58, 41, 51, 39], [58, 42, 51, 40], [59, 6, 52, 4], [59, 7, 52, 5], [60, 4, 53, 2], [60, 5, 53, 3], [60, 6, 53, 4], [61, 4, 54, 2], [61, 10, 54, 8, "subscribeThrottled"], [61, 28, 54, 26], [61, 31, 54, 29], [61, 35, 54, 29, "useLatestCallback"], [61, 61, 54, 46], [61, 63, 54, 47, "listener"], [61, 71, 54, 55], [61, 75, 54, 59], [62, 6, 55, 4], [62, 12, 55, 10, "delay"], [62, 17, 55, 15], [62, 20, 55, 18], [62, 23, 55, 21], [62, 24, 55, 22], [62, 25, 55, 23], [64, 6, 57, 4], [64, 10, 57, 8, "timer"], [64, 15, 57, 13], [65, 6, 58, 4], [65, 10, 58, 8, "updated"], [65, 17, 58, 15], [65, 20, 58, 18], [65, 25, 58, 23], [66, 6, 59, 4], [66, 10, 59, 8, "waiting"], [66, 17, 59, 15], [66, 20, 59, 18], [66, 25, 59, 23], [67, 6, 60, 4], [67, 12, 60, 10, "throttledListener"], [67, 29, 60, 27], [67, 32, 60, 30, "throttledListener"], [67, 33, 60, 30], [67, 38, 60, 36], [68, 8, 61, 6, "clearTimeout"], [68, 20, 61, 18], [68, 21, 61, 19, "timer"], [68, 26, 61, 24], [68, 27, 61, 25], [69, 8, 62, 6, "updated"], [69, 15, 62, 13], [69, 18, 62, 16], [69, 22, 62, 20], [70, 8, 63, 6], [70, 12, 63, 10, "waiting"], [70, 19, 63, 17], [70, 21, 63, 19], [71, 10, 64, 8], [72, 10, 65, 8, "timer"], [72, 15, 65, 13], [72, 18, 65, 16, "setTimeout"], [72, 28, 65, 26], [72, 29, 65, 27], [72, 35, 65, 33], [73, 12, 66, 10], [73, 16, 66, 14, "updated"], [73, 23, 66, 21], [73, 25, 66, 23], [74, 14, 67, 12, "updated"], [74, 21, 67, 19], [74, 24, 67, 22], [74, 29, 67, 27], [75, 14, 68, 12, "listener"], [75, 22, 68, 20], [75, 23, 68, 21], [75, 24, 68, 22], [76, 12, 69, 10], [77, 10, 70, 8], [77, 11, 70, 9], [77, 13, 70, 11, "delay"], [77, 18, 70, 16], [77, 19, 70, 17], [78, 8, 71, 6], [78, 9, 71, 7], [78, 15, 71, 13], [79, 10, 72, 8, "waiting"], [79, 17, 72, 15], [79, 20, 72, 18], [79, 24, 72, 22], [80, 10, 73, 8, "setTimeout"], [80, 20, 73, 18], [80, 21, 73, 19], [80, 33, 73, 31], [81, 12, 74, 10, "waiting"], [81, 19, 74, 17], [81, 22, 74, 20], [81, 27, 74, 25], [82, 10, 75, 8], [82, 11, 75, 9], [82, 13, 75, 11, "delay"], [82, 18, 75, 16], [82, 19, 75, 17], [84, 10, 77, 8], [85, 10, 78, 8, "updated"], [85, 17, 78, 15], [85, 20, 78, 18], [85, 25, 78, 23], [86, 10, 79, 8, "listener"], [86, 18, 79, 16], [86, 19, 79, 17], [86, 20, 79, 18], [87, 8, 80, 6], [88, 6, 81, 4], [88, 7, 81, 5], [89, 6, 82, 4], [89, 12, 82, 10, "unsubscribe"], [89, 23, 82, 21], [89, 26, 82, 24, "subscribe"], [89, 35, 82, 33], [89, 36, 82, 34, "throttledListener"], [89, 53, 82, 51], [89, 54, 82, 52], [90, 6, 83, 4], [90, 13, 83, 11], [90, 19, 83, 17], [91, 8, 84, 6, "unsubscribe"], [91, 19, 84, 17], [91, 20, 84, 18], [91, 21, 84, 19], [92, 8, 85, 6, "clearTimeout"], [92, 20, 85, 18], [92, 21, 85, 19, "timer"], [92, 26, 85, 24], [92, 27, 85, 25], [93, 6, 86, 4], [93, 7, 86, 5], [94, 4, 87, 2], [94, 5, 87, 3], [94, 6, 87, 4], [95, 4, 88, 2], [95, 10, 88, 8, "context"], [95, 17, 88, 15], [95, 20, 88, 18, "React"], [95, 25, 88, 23], [95, 26, 88, 24, "useMemo"], [95, 33, 88, 31], [95, 34, 88, 32], [95, 41, 88, 39], [96, 6, 89, 4, "get<PERSON>urrent"], [96, 16, 89, 14], [97, 6, 90, 4, "subscribe"], [97, 15, 90, 13], [98, 6, 91, 4, "subscribeThrottled"], [99, 4, 92, 2], [99, 5, 92, 3], [99, 6, 92, 4], [99, 8, 92, 6], [99, 9, 92, 7, "subscribe"], [99, 18, 92, 16], [99, 20, 92, 18, "subscribeThrottled"], [99, 38, 92, 36], [99, 40, 92, 38, "get<PERSON>urrent"], [99, 50, 92, 48], [99, 51, 92, 49], [99, 52, 92, 50], [100, 4, 93, 2], [100, 10, 93, 8, "onChange"], [100, 18, 93, 16], [100, 21, 93, 19], [100, 25, 93, 19, "useLatestCallback"], [100, 51, 93, 36], [100, 53, 93, 37, "frame"], [100, 58, 93, 42], [100, 62, 93, 46], [101, 6, 94, 4], [101, 10, 94, 8, "frameRef"], [101, 18, 94, 16], [101, 19, 94, 17, "current"], [101, 26, 94, 24], [101, 27, 94, 25, "height"], [101, 33, 94, 31], [101, 38, 94, 36, "frame"], [101, 43, 94, 41], [101, 44, 94, 42, "height"], [101, 50, 94, 48], [101, 54, 94, 52, "frameRef"], [101, 62, 94, 60], [101, 63, 94, 61, "current"], [101, 70, 94, 68], [101, 71, 94, 69, "width"], [101, 76, 94, 74], [101, 81, 94, 79, "frame"], [101, 86, 94, 84], [101, 87, 94, 85, "width"], [101, 92, 94, 90], [101, 94, 94, 92], [102, 8, 95, 6], [103, 6, 96, 4], [104, 6, 97, 4, "frameRef"], [104, 14, 97, 12], [104, 15, 97, 13, "current"], [104, 22, 97, 20], [104, 25, 97, 23], [105, 8, 98, 6, "width"], [105, 13, 98, 11], [105, 15, 98, 13, "frame"], [105, 20, 98, 18], [105, 21, 98, 19, "width"], [105, 26, 98, 24], [106, 8, 99, 6, "height"], [106, 14, 99, 12], [106, 16, 99, 14, "frame"], [106, 21, 99, 19], [106, 22, 99, 20, "height"], [107, 6, 100, 4], [107, 7, 100, 5], [108, 6, 101, 4, "listeners"], [108, 15, 101, 13], [108, 16, 101, 14, "current"], [108, 23, 101, 21], [108, 24, 101, 22, "for<PERSON>ach"], [108, 31, 101, 29], [108, 32, 101, 30, "listener"], [108, 40, 101, 38], [108, 44, 101, 42, "listener"], [108, 52, 101, 50], [108, 53, 101, 51], [108, 54, 101, 52], [108, 55, 101, 53], [109, 4, 102, 2], [109, 5, 102, 3], [109, 6, 102, 4], [110, 4, 103, 2], [110, 11, 103, 9], [110, 24, 103, 22], [110, 28, 103, 22, "_jsxs"], [110, 44, 103, 27], [110, 46, 103, 28, "_Fragment"], [110, 66, 103, 37], [110, 68, 103, 39], [111, 6, 104, 4, "children"], [111, 14, 104, 12], [111, 16, 104, 14], [111, 17, 104, 15, "Platform"], [111, 34, 104, 23], [111, 35, 104, 24, "OS"], [111, 37, 104, 26], [111, 42, 104, 31], [111, 47, 104, 36], [111, 50, 104, 39], [111, 63, 104, 52], [111, 67, 104, 52, "_jsx"], [111, 82, 104, 56], [111, 84, 104, 57, "FrameSizeListenerWeb"], [111, 104, 104, 77], [111, 106, 104, 79], [112, 8, 105, 6, "onChange"], [112, 16, 105, 14], [112, 18, 105, 16, "onChange"], [113, 6, 106, 4], [113, 7, 106, 5], [113, 8, 106, 6], [113, 11, 106, 9], [113, 18, 106, 16, "SafeAreaListener"], [113, 34, 106, 32], [113, 39, 106, 37], [113, 50, 106, 48], [113, 53, 106, 51], [113, 66, 106, 64], [113, 70, 106, 64, "_jsx"], [113, 85, 106, 68], [113, 87, 106, 69, "FrameSizeListenerNativeFallback"], [113, 118, 106, 100], [113, 120, 106, 102], [114, 8, 107, 6, "onChange"], [114, 16, 107, 14], [114, 18, 107, 16, "onChange"], [115, 6, 108, 4], [115, 7, 108, 5], [115, 8, 108, 6], [115, 11, 108, 9], [115, 24, 108, 22], [115, 28, 108, 22, "_jsx"], [115, 43, 108, 26], [115, 45, 108, 27, "SafeAreaListener"], [115, 61, 108, 43], [115, 63, 108, 45], [116, 8, 109, 6, "onChange"], [116, 16, 109, 14], [116, 18, 109, 16, "onChange"], [116, 19, 109, 17], [117, 10, 110, 8, "frame"], [118, 8, 111, 6], [118, 9, 111, 7], [118, 14, 111, 12, "onChange"], [118, 22, 111, 20], [118, 23, 111, 21, "frame"], [118, 28, 111, 26], [118, 29, 111, 27], [119, 8, 112, 6, "style"], [119, 13, 112, 11], [119, 15, 112, 13, "StyleSheet"], [119, 34, 112, 23], [119, 35, 112, 24, "absoluteFill"], [120, 6, 113, 4], [120, 7, 113, 5], [120, 8, 113, 6], [120, 10, 113, 8], [120, 23, 113, 21], [120, 27, 113, 21, "_jsx"], [120, 42, 113, 25], [120, 44, 113, 26, "FrameContext"], [120, 56, 113, 38], [120, 57, 113, 39, "Provider"], [120, 65, 113, 47], [120, 67, 113, 49], [121, 8, 114, 6, "value"], [121, 13, 114, 11], [121, 15, 114, 13, "context"], [121, 22, 114, 20], [122, 8, 115, 6, "children"], [122, 16, 115, 14], [122, 18, 115, 16, "children"], [123, 6, 116, 4], [123, 7, 116, 5], [123, 8, 116, 6], [124, 4, 117, 2], [124, 5, 117, 3], [124, 6, 117, 4], [125, 2, 118, 0], [127, 2, 120, 0], [128, 2, 121, 0], [129, 2, 122, 0], [129, 11, 122, 9, "FrameSizeListenerNativeFallback"], [129, 42, 122, 40, "FrameSizeListenerNativeFallback"], [129, 43, 122, 41], [130, 4, 123, 2, "onChange"], [131, 2, 124, 0], [131, 3, 124, 1], [131, 5, 124, 3], [132, 4, 125, 2], [132, 10, 125, 8, "frame"], [132, 15, 125, 13], [132, 18, 125, 16], [132, 22, 125, 16, "useSafeAreaFrame"], [132, 66, 125, 32], [132, 68, 125, 33], [132, 69, 125, 34], [133, 4, 126, 2, "React"], [133, 9, 126, 7], [133, 10, 126, 8, "useLayoutEffect"], [133, 25, 126, 23], [133, 26, 126, 24], [133, 32, 126, 30], [134, 6, 127, 4, "onChange"], [134, 14, 127, 12], [134, 15, 127, 13, "frame"], [134, 20, 127, 18], [134, 21, 127, 19], [135, 4, 128, 2], [135, 5, 128, 3], [135, 7, 128, 5], [135, 8, 128, 6, "frame"], [135, 13, 128, 11], [135, 15, 128, 13, "onChange"], [135, 23, 128, 21], [135, 24, 128, 22], [135, 25, 128, 23], [136, 4, 129, 2], [136, 11, 129, 9], [136, 15, 129, 13], [137, 2, 130, 0], [139, 2, 132, 0], [140, 2, 133, 0], [141, 2, 134, 0], [141, 11, 134, 9, "FrameSizeListenerWeb"], [141, 31, 134, 29, "FrameSizeListenerWeb"], [141, 32, 134, 30], [142, 4, 135, 2, "onChange"], [143, 2, 136, 0], [143, 3, 136, 1], [143, 5, 136, 3], [144, 4, 137, 2], [144, 10, 137, 8, "elementRef"], [144, 20, 137, 18], [144, 23, 137, 21, "React"], [144, 28, 137, 26], [144, 29, 137, 27, "useRef"], [144, 35, 137, 33], [144, 36, 137, 34], [144, 40, 137, 38], [144, 41, 137, 39], [145, 4, 138, 2, "React"], [145, 9, 138, 7], [145, 10, 138, 8, "useEffect"], [145, 19, 138, 17], [145, 20, 138, 18], [145, 26, 138, 24], [146, 6, 139, 4], [146, 10, 139, 8, "elementRef"], [146, 20, 139, 18], [146, 21, 139, 19, "current"], [146, 28, 139, 26], [146, 32, 139, 30], [146, 36, 139, 34], [146, 38, 139, 36], [147, 8, 140, 6], [148, 6, 141, 4], [149, 6, 142, 4], [149, 12, 142, 10, "rect"], [149, 16, 142, 14], [149, 19, 142, 17, "elementRef"], [149, 29, 142, 27], [149, 30, 142, 28, "current"], [149, 37, 142, 35], [149, 38, 142, 36, "getBoundingClientRect"], [149, 59, 142, 57], [149, 60, 142, 58], [149, 61, 142, 59], [150, 6, 143, 4, "onChange"], [150, 14, 143, 12], [150, 15, 143, 13], [151, 8, 144, 6, "width"], [151, 13, 144, 11], [151, 15, 144, 13, "rect"], [151, 19, 144, 17], [151, 20, 144, 18, "width"], [151, 25, 144, 23], [152, 8, 145, 6, "height"], [152, 14, 145, 12], [152, 16, 145, 14, "rect"], [152, 20, 145, 18], [152, 21, 145, 19, "height"], [153, 6, 146, 4], [153, 7, 146, 5], [153, 8, 146, 6], [154, 6, 147, 4], [154, 12, 147, 10, "observer"], [154, 20, 147, 18], [154, 23, 147, 21], [154, 27, 147, 25, "ResizeObserver"], [154, 41, 147, 39], [154, 42, 147, 40, "entries"], [154, 49, 147, 47], [154, 53, 147, 51], [155, 8, 148, 6], [155, 14, 148, 12, "entry"], [155, 19, 148, 17], [155, 22, 148, 20, "entries"], [155, 29, 148, 27], [155, 30, 148, 28], [155, 31, 148, 29], [155, 32, 148, 30], [156, 8, 149, 6], [156, 12, 149, 10, "entry"], [156, 17, 149, 15], [156, 19, 149, 17], [157, 10, 150, 8], [157, 16, 150, 14], [158, 12, 151, 10, "width"], [158, 17, 151, 15], [159, 12, 152, 10, "height"], [160, 10, 153, 8], [160, 11, 153, 9], [160, 14, 153, 12, "entry"], [160, 19, 153, 17], [160, 20, 153, 18, "contentRect"], [160, 31, 153, 29], [161, 10, 154, 8, "onChange"], [161, 18, 154, 16], [161, 19, 154, 17], [162, 12, 155, 10, "width"], [162, 17, 155, 15], [163, 12, 156, 10, "height"], [164, 10, 157, 8], [164, 11, 157, 9], [164, 12, 157, 10], [165, 8, 158, 6], [166, 6, 159, 4], [166, 7, 159, 5], [166, 8, 159, 6], [167, 6, 160, 4, "observer"], [167, 14, 160, 12], [167, 15, 160, 13, "observe"], [167, 22, 160, 20], [167, 23, 160, 21, "elementRef"], [167, 33, 160, 31], [167, 34, 160, 32, "current"], [167, 41, 160, 39], [167, 42, 160, 40], [168, 6, 161, 4], [168, 13, 161, 11], [168, 19, 161, 17], [169, 8, 162, 6, "observer"], [169, 16, 162, 14], [169, 17, 162, 15, "disconnect"], [169, 27, 162, 25], [169, 28, 162, 26], [169, 29, 162, 27], [170, 6, 163, 4], [170, 7, 163, 5], [171, 4, 164, 2], [171, 5, 164, 3], [171, 7, 164, 5], [171, 8, 164, 6, "onChange"], [171, 16, 164, 14], [171, 17, 164, 15], [171, 18, 164, 16], [172, 4, 165, 2], [172, 11, 165, 9], [172, 24, 165, 22], [172, 28, 165, 22, "_jsx"], [172, 43, 165, 26], [172, 45, 165, 27], [172, 50, 165, 32], [172, 52, 165, 34], [173, 6, 166, 4, "ref"], [173, 9, 166, 7], [173, 11, 166, 9, "elementRef"], [173, 21, 166, 19], [174, 6, 167, 4, "style"], [174, 11, 167, 9], [174, 13, 167, 11], [175, 8, 168, 6], [175, 11, 168, 9, "StyleSheet"], [175, 30, 168, 19], [175, 31, 168, 20, "absoluteFillObject"], [175, 49, 168, 38], [176, 8, 169, 6, "pointerEvents"], [176, 21, 169, 19], [176, 23, 169, 21], [176, 29, 169, 27], [177, 8, 170, 6, "visibility"], [177, 18, 170, 16], [177, 20, 170, 18], [178, 6, 171, 4], [179, 4, 172, 2], [179, 5, 172, 3], [179, 6, 172, 4], [180, 2, 173, 0], [181, 0, 173, 1], [181, 3]], "functionMap": {"names": ["<global>", "useFrameSize", "FrameSizeProvider", "FrameSizeProviderInner", "useLatestCallback$argument_0", "<anonymous>", "throttledListener", "setTimeout$argument_0", "React.useMemo$argument_0", "listeners.current.forEach$argument_0", "_jsx$argument_1.onChange", "FrameSizeListenerNativeFallback", "React.useLayoutEffect$argument_0", "FrameSizeListenerWeb", "React.useEffect$argument_0", "ResizeObserver$argument_0"], "mappings": "AAA;OCe;CDO;OEC;CFa;AGC;uCCS,sBD;sCCC;WCE;KDE;GDC;+CCC;8BEM;2BCK;SDK;mBCG;SDE;KFM;WCE;KDG;GDC;gCKC;ILI;qCCC;8BKQ,sBL;GDC;gBOO;2BPE;CHO;AWI;wBCI;GDE;CXE;AaI;kBCI;wCCS;KDY;WTE;KSE;GDC;CbS"}}, "type": "js/module"}]}