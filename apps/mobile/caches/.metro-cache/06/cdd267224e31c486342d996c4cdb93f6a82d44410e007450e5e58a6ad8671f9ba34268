{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var GitCommitVertical = exports.default = (0, _createLucideIcon.default)(\"GitCommitVertical\", [[\"path\", {\n    d: \"M12 3v6\",\n    key: \"1holv5\"\n  }], [\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"3\",\n    key: \"1v7zrd\"\n  }], [\"path\", {\n    d: \"M12 15v6\",\n    key: \"a9ows0\"\n  }]]);\n});", "lineCount": 27, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "GitCommitVertical"], [15, 23, 10, 23], [15, 26, 10, 23, "exports"], [15, 33, 10, 23], [15, 34, 10, 23, "default"], [15, 41, 10, 23], [15, 44, 10, 26], [15, 48, 10, 26, "createLucideIcon"], [15, 73, 10, 42], [15, 75, 10, 43], [15, 94, 10, 62], [15, 96, 10, 64], [15, 97, 11, 2], [15, 98, 11, 3], [15, 104, 11, 9], [15, 106, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 16, 11, 25], [17, 4, 11, 27, "key"], [17, 7, 11, 30], [17, 9, 11, 32], [18, 2, 11, 41], [18, 3, 11, 42], [18, 4, 11, 43], [18, 6, 12, 2], [18, 7, 12, 3], [18, 15, 12, 11], [18, 17, 12, 13], [19, 4, 12, 15, "cx"], [19, 6, 12, 17], [19, 8, 12, 19], [19, 12, 12, 23], [20, 4, 12, 25, "cy"], [20, 6, 12, 27], [20, 8, 12, 29], [20, 12, 12, 33], [21, 4, 12, 35, "r"], [21, 5, 12, 36], [21, 7, 12, 38], [21, 10, 12, 41], [22, 4, 12, 43, "key"], [22, 7, 12, 46], [22, 9, 12, 48], [23, 2, 12, 57], [23, 3, 12, 58], [23, 4, 12, 59], [23, 6, 13, 2], [23, 7, 13, 3], [23, 13, 13, 9], [23, 15, 13, 11], [24, 4, 13, 13, "d"], [24, 5, 13, 14], [24, 7, 13, 16], [24, 17, 13, 26], [25, 4, 13, 28, "key"], [25, 7, 13, 31], [25, 9, 13, 33], [26, 2, 13, 42], [26, 3, 13, 43], [26, 4, 13, 44], [26, 5, 14, 1], [26, 6, 14, 2], [27, 0, 14, 3], [27, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}