{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Lectern = exports.default = (0, _createLucideIcon.default)(\"Lectern\", [[\"path\", {\n    d: \"M16 12h3a2 2 0 0 0 1.902-1.38l1.056-3.333A1 1 0 0 0 21 6H3a1 1 0 0 0-.958 1.287l1.056 3.334A2 2 0 0 0 5 12h3\",\n    key: \"13jjxg\"\n  }], [\"path\", {\n    d: \"M18 6V3a1 1 0 0 0-1-1h-3\",\n    key: \"1550fe\"\n  }], [\"rect\", {\n    width: \"8\",\n    height: \"12\",\n    x: \"8\",\n    y: \"10\",\n    rx: \"1\",\n    key: \"qmu8b6\"\n  }]]);\n});", "lineCount": 29, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Lectern"], [15, 15, 10, 13], [15, 18, 10, 13, "exports"], [15, 25, 10, 13], [15, 26, 10, 13, "default"], [15, 33, 10, 13], [15, 36, 10, 16], [15, 40, 10, 16, "createLucideIcon"], [15, 65, 10, 32], [15, 67, 10, 33], [15, 76, 10, 42], [15, 78, 10, 44], [15, 79, 11, 2], [15, 80, 12, 4], [15, 86, 12, 10], [15, 88, 13, 4], [16, 4, 14, 6, "d"], [16, 5, 14, 7], [16, 7, 14, 9], [16, 117, 14, 119], [17, 4, 15, 6, "key"], [17, 7, 15, 9], [17, 9, 15, 11], [18, 2, 16, 4], [18, 3, 16, 5], [18, 4, 17, 3], [18, 6, 18, 2], [18, 7, 18, 3], [18, 13, 18, 9], [18, 15, 18, 11], [19, 4, 18, 13, "d"], [19, 5, 18, 14], [19, 7, 18, 16], [19, 33, 18, 42], [20, 4, 18, 44, "key"], [20, 7, 18, 47], [20, 9, 18, 49], [21, 2, 18, 58], [21, 3, 18, 59], [21, 4, 18, 60], [21, 6, 19, 2], [21, 7, 19, 3], [21, 13, 19, 9], [21, 15, 19, 11], [22, 4, 19, 13, "width"], [22, 9, 19, 18], [22, 11, 19, 20], [22, 14, 19, 23], [23, 4, 19, 25, "height"], [23, 10, 19, 31], [23, 12, 19, 33], [23, 16, 19, 37], [24, 4, 19, 39, "x"], [24, 5, 19, 40], [24, 7, 19, 42], [24, 10, 19, 45], [25, 4, 19, 47, "y"], [25, 5, 19, 48], [25, 7, 19, 50], [25, 11, 19, 54], [26, 4, 19, 56, "rx"], [26, 6, 19, 58], [26, 8, 19, 60], [26, 11, 19, 63], [27, 4, 19, 65, "key"], [27, 7, 19, 68], [27, 9, 19, 70], [28, 2, 19, 79], [28, 3, 19, 80], [28, 4, 19, 81], [28, 5, 20, 1], [28, 6, 20, 2], [29, 0, 20, 3], [29, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}