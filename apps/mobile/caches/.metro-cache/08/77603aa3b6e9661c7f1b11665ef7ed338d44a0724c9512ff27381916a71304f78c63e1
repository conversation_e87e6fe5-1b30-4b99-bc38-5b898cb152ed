{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.reloadAppAsync = reloadAppAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  /**\n   * Reloads the app. This method works for both release and debug builds.\n   *\n   * Unlike [`Updates.reloadAsync()`](/versions/latest/sdk/updates/#updatesreloadasync),\n   * this function does not use a new update even if one is available. It only reloads the app using the same JavaScript bundle that is currently running.\n   *\n   * @param reason The reason for reloading the app. This is used only for some platforms.\n   */\n  function reloadAppAsync() {\n    return _reloadAppAsync.apply(this, arguments);\n  }\n  function _reloadAppAsync() {\n    _reloadAppAsync = (0, _asyncToGenerator2.default)(function* () {\n      var reason = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Reloaded from JS call';\n      yield globalThis.expo?.reloadAppAsync(reason);\n    });\n    return _reloadAppAsync.apply(this, arguments);\n  }\n});", "lineCount": 26, "map": [[8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 2, 1, 0], [16, 11, 9, 22, "reloadAppAsync"], [16, 25, 9, 36, "reloadAppAsync"], [16, 26, 9, 36], [17, 4, 9, 36], [17, 11, 9, 36, "_reloadAppAsync"], [17, 26, 9, 36], [17, 27, 9, 36, "apply"], [17, 32, 9, 36], [17, 39, 9, 36, "arguments"], [17, 48, 9, 36], [18, 2, 9, 36], [19, 2, 9, 36], [19, 11, 9, 36, "_reloadAppAsync"], [19, 27, 9, 36], [20, 4, 9, 36, "_reloadAppAsync"], [20, 19, 9, 36], [20, 26, 9, 36, "_asyncToGenerator2"], [20, 44, 9, 36], [20, 45, 9, 36, "default"], [20, 52, 9, 36], [20, 54, 9, 7], [20, 67, 9, 94], [21, 6, 9, 94], [21, 10, 9, 37, "reason"], [21, 16, 9, 51], [21, 19, 9, 51, "arguments"], [21, 28, 9, 51], [21, 29, 9, 51, "length"], [21, 35, 9, 51], [21, 43, 9, 51, "arguments"], [21, 52, 9, 51], [21, 60, 9, 51, "undefined"], [21, 69, 9, 51], [21, 72, 9, 51, "arguments"], [21, 81, 9, 51], [21, 87, 9, 54], [21, 110, 9, 77], [22, 6, 10, 2], [22, 12, 10, 8, "globalThis"], [22, 22, 10, 18], [22, 23, 10, 19, "expo"], [22, 27, 10, 23], [22, 29, 10, 25, "reloadAppAsync"], [22, 43, 10, 39], [22, 44, 10, 40, "reason"], [22, 50, 10, 46], [22, 51, 10, 47], [23, 4, 11, 0], [23, 5, 11, 1], [24, 4, 11, 1], [24, 11, 11, 1, "_reloadAppAsync"], [24, 26, 11, 1], [24, 27, 11, 1, "apply"], [24, 32, 11, 1], [24, 39, 11, 1, "arguments"], [24, 48, 11, 1], [25, 2, 11, 1], [26, 0, 11, 1], [26, 3]], "functionMap": {"names": ["<global>", "reloadAppAsync"], "mappings": "AAA;OCQ;CDE"}}, "type": "js/module"}]}