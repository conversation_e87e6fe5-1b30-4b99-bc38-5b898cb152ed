{"dependencies": [{"name": "../ReducedMotion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 66, "index": 80}}], "key": "KOT/pgeLpi4gIRD7QyPlcDltLOw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useReducedMotion = useReducedMotion;\n  var _ReducedMotion = require(_dependencyMap[0], \"../ReducedMotion\");\n  var IS_REDUCED_MOTION_ENABLED_IN_SYSTEM = (0, _ReducedMotion.isReducedMotionEnabledInSystem)();\n\n  /**\n   * Lets you query the reduced motion system setting.\n   *\n   * Changing the reduced motion system setting doesn't cause your components to\n   * rerender.\n   *\n   * @returns A boolean indicating whether the reduced motion setting was enabled\n   *   when the app started.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useReducedMotion\n   */\n  function useReducedMotion() {\n    return IS_REDUCED_MOTION_ENABLED_IN_SYSTEM;\n  }\n});", "lineCount": 24, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useReducedMotion"], [7, 26, 1, 13], [7, 29, 1, 13, "useReducedMotion"], [7, 45, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_ReducedMotion"], [8, 20, 2, 0], [8, 23, 2, 0, "require"], [8, 30, 2, 0], [8, 31, 2, 0, "_dependencyMap"], [8, 45, 2, 0], [9, 2, 4, 0], [9, 6, 4, 6, "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM"], [9, 41, 4, 41], [9, 44, 4, 44], [9, 48, 4, 44, "isReducedMotionEnabledInSystem"], [9, 93, 4, 74], [9, 95, 4, 75], [9, 96, 4, 76], [11, 2, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 2, 16, 7], [21, 11, 16, 16, "useReducedMotion"], [21, 27, 16, 32, "useReducedMotion"], [21, 28, 16, 32], [21, 30, 16, 35], [22, 4, 17, 2], [22, 11, 17, 9, "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM"], [22, 46, 17, 44], [23, 2, 18, 0], [24, 0, 18, 1], [24, 3]], "functionMap": {"names": ["<global>", "useReducedMotion"], "mappings": "AAA;OCe;CDE"}}, "type": "js/module"}]}