{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  class JSPropsUpdaterWeb {\n    addOnJSPropsChangeListener(_animatedComponent) {\n      // noop\n    }\n    removeOnJSPropsChangeListener(_animatedComponent) {\n      // noop\n    }\n  }\n  exports.default = JSPropsUpdaterWeb;\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 3, 15], [8, 8, 3, 21, "JSPropsUpdaterWeb"], [8, 25, 3, 38], [8, 26, 3, 39], [9, 4, 4, 2, "addOnJSPropsChangeListener"], [9, 30, 4, 28, "addOnJSPropsChangeListener"], [9, 31, 4, 29, "_animatedComponent"], [9, 49, 4, 47], [9, 51, 4, 49], [10, 6, 5, 4], [11, 4, 5, 4], [12, 4, 7, 2, "removeOnJSPropsChangeListener"], [12, 33, 7, 31, "removeOnJSPropsChangeListener"], [12, 34, 7, 32, "_animatedComponent"], [12, 52, 7, 50], [12, 54, 7, 52], [13, 6, 8, 4], [14, 4, 8, 4], [15, 2, 10, 0], [16, 2, 10, 1, "exports"], [16, 9, 10, 1], [16, 10, 10, 1, "default"], [16, 17, 10, 1], [16, 20, 10, 1, "JSPropsUpdaterWeb"], [16, 37, 10, 1], [17, 0, 10, 1], [17, 3]], "functionMap": {"names": ["<global>", "JSPropsUpdaterWeb", "JSPropsUpdaterWeb#addOnJSPropsChangeListener", "JSPropsUpdaterWeb#removeOnJSPropsChangeListener"], "mappings": "AAA;eCE;ECC;GDE;EEC;GFE;CDC"}}, "type": "js/module"}]}