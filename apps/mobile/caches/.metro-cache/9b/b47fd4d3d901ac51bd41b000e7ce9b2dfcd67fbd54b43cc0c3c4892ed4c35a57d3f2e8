{"dependencies": [{"name": "../../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 59, "index": 74}}], "key": "vhY7QX3yty1rmiaRlwcQa5g4v48=", "exportNames": ["*"]}}, {"name": "../../Easing.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 75}, "end": {"line": 4, "column": 51, "index": 126}}], "key": "Xeo9ubSIyCQFVRA0bDYEznsxmBA=", "exportNames": ["*"]}}, {"name": "../../logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 127}, "end": {"line": 5, "column": 47, "index": 174}}], "key": "6SNP0qYc6Dvb4y6pRCC6IV2Z4aU=", "exportNames": ["*"]}}, {"name": "../animationBuilder/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 175}, "end": {"line": 6, "column": 56, "index": 231}}], "key": "Wj0fdHDocwf0cswRWN7z1KC5KSk=", "exportNames": ["*"]}}, {"name": "./componentStyle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 232}, "end": {"line": 7, "column": 57, "index": 289}}], "key": "JxDOem4+XEd8jLROs+J4XKes0Sc=", "exportNames": ["*"]}}, {"name": "./componentUtils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 290}, "end": {"line": 8, "column": 155, "index": 445}}], "key": "mrV0T8LQhyHOo28DWdY5CBvO0CM=", "exportNames": ["*"]}}, {"name": "./config.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 446}, "end": {"line": 9, "column": 41, "index": 487}}], "key": "bUPXFgGH+XQHosI76NH2QbmMaAI=", "exportNames": ["*"]}}, {"name": "./createAnimation.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 488}, "end": {"line": 10, "column": 103, "index": 591}}], "key": "i+Y8VKKfFzS1sfsEyLLjIgMA9IM=", "exportNames": ["*"]}}, {"name": "./domUtils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 592}, "end": {"line": 11, "column": 49, "index": 641}}], "key": "0duQh0EQU3LifQ3CyaK4iQBtJH0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.startWebLayoutAnimation = startWebLayoutAnimation;\n  exports.tryActivateLayoutTransition = tryActivateLayoutTransition;\n  var _commonTypes = require(_dependencyMap[0], \"../../commonTypes.js\");\n  var _Easing = require(_dependencyMap[1], \"../../Easing.js\");\n  var _index = require(_dependencyMap[2], \"../../logger/index.js\");\n  var _index2 = require(_dependencyMap[3], \"../animationBuilder/index.js\");\n  var _componentStyle = require(_dependencyMap[4], \"./componentStyle.js\");\n  var _componentUtils = require(_dependencyMap[5], \"./componentUtils.js\");\n  var _config = require(_dependencyMap[6], \"./config.js\");\n  var _createAnimation = require(_dependencyMap[7], \"./createAnimation.js\");\n  var _domUtils = require(_dependencyMap[8], \"./domUtils.js\");\n  function chooseConfig(animationType, props) {\n    const config = animationType === _commonTypes.LayoutAnimationType.ENTERING ? props.entering : animationType === _commonTypes.LayoutAnimationType.EXITING ? props.exiting : animationType === _commonTypes.LayoutAnimationType.LAYOUT ? props.layout : null;\n    return config;\n  }\n  function checkUndefinedAnimationFail(initialAnimationName, needsCustomization) {\n    // This prevents crashes if we try to set animations that are not defined.\n    // We don't care about layout transitions or custom keyframes since they're created dynamically\n    if (initialAnimationName in _config.Animations || needsCustomization) {\n      return false;\n    }\n    _index.logger.warn(\"Couldn't load entering/exiting animation. Current version supports only predefined animations with modifiers: duration, delay, easing, randomizeDelay, withCallback, reducedMotion.\");\n    return true;\n  }\n  function maybeReportOverwrittenProperties(keyframe, styles) {\n    const propertyRegex = /([a-zA-Z-]+)(?=:)/g;\n    const animationProperties = new Set();\n    for (const match of keyframe.matchAll(propertyRegex)) {\n      animationProperties.add(match[1]);\n    }\n    const commonProperties = Array.from(styles).filter(style => animationProperties.has(style));\n    if (commonProperties.length === 0) {\n      return;\n    }\n    _index.logger.warn(`${commonProperties.length === 1 ? 'Property' : 'Properties'} [${commonProperties.join(', ')}] may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`);\n  }\n  function chooseAction(animationType, animationConfig, element, transitionData) {\n    switch (animationType) {\n      case _commonTypes.LayoutAnimationType.ENTERING:\n        (0, _componentUtils.setElementAnimation)(element, animationConfig, true);\n        break;\n      case _commonTypes.LayoutAnimationType.LAYOUT:\n        transitionData.reversed = animationConfig.reversed;\n        (0, _componentUtils.handleLayoutTransition)(element, animationConfig, transitionData);\n        break;\n      case _commonTypes.LayoutAnimationType.EXITING:\n        (0, _componentUtils.handleExitingAnimation)(element, animationConfig);\n        break;\n    }\n  }\n  function tryGetAnimationConfig(props, animationType) {\n    const config = chooseConfig(animationType, props);\n    if (!config) {\n      return null;\n    }\n    const isLayoutTransition = animationType === _commonTypes.LayoutAnimationType.LAYOUT;\n    const isCustomKeyframe = config instanceof _index2.Keyframe;\n    const hasInitialValues = config.initialValues !== undefined;\n    let animationName;\n    if (isCustomKeyframe) {\n      animationName = (0, _createAnimation.createCustomKeyFrameAnimation)(config.definitions);\n    } else if (typeof config === 'function') {\n      animationName = config.presetName;\n    } else {\n      animationName = config.constructor.presetName;\n    }\n    if (hasInitialValues) {\n      animationName = (0, _createAnimation.createAnimationWithInitialValues)(animationName, config.initialValues);\n    }\n    const shouldFail = checkUndefinedAnimationFail(animationName, isLayoutTransition || isCustomKeyframe || hasInitialValues);\n    if (shouldFail) {\n      return null;\n    }\n    if (isCustomKeyframe) {\n      const keyframeTimestamps = Object.keys(config.definitions);\n      if (!(keyframeTimestamps.includes('100') || keyframeTimestamps.includes('to'))) {\n        _index.logger.warn(`Neither '100' nor 'to' was specified in Keyframe definition. This may result in wrong final position of your component. One possible solution is to duplicate last timestamp in definition as '100' (or 'to')`);\n      }\n    }\n    const animationConfig = (0, _componentUtils.getProcessedConfig)(animationName, animationType, config);\n    return animationConfig;\n  }\n  function startWebLayoutAnimation(props, element, animationType, transitionData) {\n    const animationConfig = tryGetAnimationConfig(props, animationType);\n    (0, _componentUtils.maybeModifyStyleForKeyframe)(element, props.entering);\n    if (animationConfig?.animationName in _config.Animations) {\n      maybeReportOverwrittenProperties(_config.Animations[animationConfig?.animationName].style, element.style);\n    }\n    if (animationConfig) {\n      chooseAction(animationType, animationConfig, element, transitionData);\n    } else {\n      (0, _componentStyle.makeElementVisible)(element, 0);\n    }\n  }\n  function tryActivateLayoutTransition(props, element, snapshot) {\n    if (!props.layout) {\n      return;\n    }\n    const rect = element.getBoundingClientRect();\n    if ((0, _domUtils.areDOMRectsEqual)(rect, snapshot)) {\n      return;\n    }\n    const enteringAnimation = props.layout.enteringV?.presetName;\n    const exitingAnimation = props.layout.exitingV?.presetName;\n    const deltaX = (snapshot.width - rect.width) / 2;\n    const deltaY = (snapshot.height - rect.height) / 2;\n    const transitionData = {\n      translateX: snapshot.x - rect.x + deltaX,\n      translateY: snapshot.y - rect.y + deltaY,\n      scaleX: snapshot.width / rect.width,\n      scaleY: snapshot.height / rect.height,\n      reversed: false,\n      // This field is used only in `SequencedTransition`, so by default it will be false\n      easingX: props.layout.easingXV?.[_Easing.EasingNameSymbol] ?? 'ease',\n      easingY: props.layout.easingYV?.[_Easing.EasingNameSymbol] ?? 'ease',\n      entering: enteringAnimation,\n      exiting: exitingAnimation\n    };\n    startWebLayoutAnimation(props, element, _commonTypes.LayoutAnimationType.LAYOUT, transitionData);\n  }\n});", "lineCount": 127, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "startWebLayoutAnimation"], [7, 33, 1, 13], [7, 36, 1, 13, "startWebLayoutAnimation"], [7, 59, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "tryActivateLayoutTransition"], [8, 37, 1, 13], [8, 40, 1, 13, "tryActivateLayoutTransition"], [8, 67, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_commonTypes"], [9, 18, 3, 0], [9, 21, 3, 0, "require"], [9, 28, 3, 0], [9, 29, 3, 0, "_dependencyMap"], [9, 43, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_Easing"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_index"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_index2"], [12, 13, 6, 0], [12, 16, 6, 0, "require"], [12, 23, 6, 0], [12, 24, 6, 0, "_dependencyMap"], [12, 38, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_componentStyle"], [13, 21, 7, 0], [13, 24, 7, 0, "require"], [13, 31, 7, 0], [13, 32, 7, 0, "_dependencyMap"], [13, 46, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_componentUtils"], [14, 21, 8, 0], [14, 24, 8, 0, "require"], [14, 31, 8, 0], [14, 32, 8, 0, "_dependencyMap"], [14, 46, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_config"], [15, 13, 9, 0], [15, 16, 9, 0, "require"], [15, 23, 9, 0], [15, 24, 9, 0, "_dependencyMap"], [15, 38, 9, 0], [16, 2, 10, 0], [16, 6, 10, 0, "_createAnimation"], [16, 22, 10, 0], [16, 25, 10, 0, "require"], [16, 32, 10, 0], [16, 33, 10, 0, "_dependencyMap"], [16, 47, 10, 0], [17, 2, 11, 0], [17, 6, 11, 0, "_domUtils"], [17, 15, 11, 0], [17, 18, 11, 0, "require"], [17, 25, 11, 0], [17, 26, 11, 0, "_dependencyMap"], [17, 40, 11, 0], [18, 2, 12, 0], [18, 11, 12, 9, "chooseConfig"], [18, 23, 12, 21, "chooseConfig"], [18, 24, 12, 22, "animationType"], [18, 37, 12, 35], [18, 39, 12, 37, "props"], [18, 44, 12, 42], [18, 46, 12, 44], [19, 4, 13, 2], [19, 10, 13, 8, "config"], [19, 16, 13, 14], [19, 19, 13, 17, "animationType"], [19, 32, 13, 30], [19, 37, 13, 35, "LayoutAnimationType"], [19, 69, 13, 54], [19, 70, 13, 55, "ENTERING"], [19, 78, 13, 63], [19, 81, 13, 66, "props"], [19, 86, 13, 71], [19, 87, 13, 72, "entering"], [19, 95, 13, 80], [19, 98, 13, 83, "animationType"], [19, 111, 13, 96], [19, 116, 13, 101, "LayoutAnimationType"], [19, 148, 13, 120], [19, 149, 13, 121, "EXITING"], [19, 156, 13, 128], [19, 159, 13, 131, "props"], [19, 164, 13, 136], [19, 165, 13, 137, "exiting"], [19, 172, 13, 144], [19, 175, 13, 147, "animationType"], [19, 188, 13, 160], [19, 193, 13, 165, "LayoutAnimationType"], [19, 225, 13, 184], [19, 226, 13, 185, "LAYOUT"], [19, 232, 13, 191], [19, 235, 13, 194, "props"], [19, 240, 13, 199], [19, 241, 13, 200, "layout"], [19, 247, 13, 206], [19, 250, 13, 209], [19, 254, 13, 213], [20, 4, 14, 2], [20, 11, 14, 9, "config"], [20, 17, 14, 15], [21, 2, 15, 0], [22, 2, 16, 0], [22, 11, 16, 9, "checkUndefinedAnimationFail"], [22, 38, 16, 36, "checkUndefinedAnimationFail"], [22, 39, 16, 37, "initialAnimationName"], [22, 59, 16, 57], [22, 61, 16, 59, "needsCustomization"], [22, 79, 16, 77], [22, 81, 16, 79], [23, 4, 17, 2], [24, 4, 18, 2], [25, 4, 19, 2], [25, 8, 19, 6, "initialAnimationName"], [25, 28, 19, 26], [25, 32, 19, 30, "Animations"], [25, 50, 19, 40], [25, 54, 19, 44, "needsCustomization"], [25, 72, 19, 62], [25, 74, 19, 64], [26, 6, 20, 4], [26, 13, 20, 11], [26, 18, 20, 16], [27, 4, 21, 2], [28, 4, 22, 2, "logger"], [28, 17, 22, 8], [28, 18, 22, 9, "warn"], [28, 22, 22, 13], [28, 23, 22, 14], [28, 204, 22, 195], [28, 205, 22, 196], [29, 4, 23, 2], [29, 11, 23, 9], [29, 15, 23, 13], [30, 2, 24, 0], [31, 2, 25, 0], [31, 11, 25, 9, "maybeReportOverwrittenProperties"], [31, 43, 25, 41, "maybeReportOverwrittenProperties"], [31, 44, 25, 42, "keyframe"], [31, 52, 25, 50], [31, 54, 25, 52, "styles"], [31, 60, 25, 58], [31, 62, 25, 60], [32, 4, 26, 2], [32, 10, 26, 8, "propertyRegex"], [32, 23, 26, 21], [32, 26, 26, 24], [32, 46, 26, 44], [33, 4, 27, 2], [33, 10, 27, 8, "animationProperties"], [33, 29, 27, 27], [33, 32, 27, 30], [33, 36, 27, 34, "Set"], [33, 39, 27, 37], [33, 40, 27, 38], [33, 41, 27, 39], [34, 4, 28, 2], [34, 9, 28, 7], [34, 15, 28, 13, "match"], [34, 20, 28, 18], [34, 24, 28, 22, "keyframe"], [34, 32, 28, 30], [34, 33, 28, 31, "matchAll"], [34, 41, 28, 39], [34, 42, 28, 40, "propertyRegex"], [34, 55, 28, 53], [34, 56, 28, 54], [34, 58, 28, 56], [35, 6, 29, 4, "animationProperties"], [35, 25, 29, 23], [35, 26, 29, 24, "add"], [35, 29, 29, 27], [35, 30, 29, 28, "match"], [35, 35, 29, 33], [35, 36, 29, 34], [35, 37, 29, 35], [35, 38, 29, 36], [35, 39, 29, 37], [36, 4, 30, 2], [37, 4, 31, 2], [37, 10, 31, 8, "commonProperties"], [37, 26, 31, 24], [37, 29, 31, 27, "Array"], [37, 34, 31, 32], [37, 35, 31, 33, "from"], [37, 39, 31, 37], [37, 40, 31, 38, "styles"], [37, 46, 31, 44], [37, 47, 31, 45], [37, 48, 31, 46, "filter"], [37, 54, 31, 52], [37, 55, 31, 53, "style"], [37, 60, 31, 58], [37, 64, 31, 62, "animationProperties"], [37, 83, 31, 81], [37, 84, 31, 82, "has"], [37, 87, 31, 85], [37, 88, 31, 86, "style"], [37, 93, 31, 91], [37, 94, 31, 92], [37, 95, 31, 93], [38, 4, 32, 2], [38, 8, 32, 6, "commonProperties"], [38, 24, 32, 22], [38, 25, 32, 23, "length"], [38, 31, 32, 29], [38, 36, 32, 34], [38, 37, 32, 35], [38, 39, 32, 37], [39, 6, 33, 4], [40, 4, 34, 2], [41, 4, 35, 2, "logger"], [41, 17, 35, 8], [41, 18, 35, 9, "warn"], [41, 22, 35, 13], [41, 23, 35, 14], [41, 26, 35, 17, "commonProperties"], [41, 42, 35, 33], [41, 43, 35, 34, "length"], [41, 49, 35, 40], [41, 54, 35, 45], [41, 55, 35, 46], [41, 58, 35, 49], [41, 68, 35, 59], [41, 71, 35, 62], [41, 83, 35, 74], [41, 88, 35, 79, "commonProperties"], [41, 104, 35, 95], [41, 105, 35, 96, "join"], [41, 109, 35, 100], [41, 110, 35, 101], [41, 114, 35, 105], [41, 115, 35, 106], [41, 256, 35, 247], [41, 257, 35, 248], [42, 2, 36, 0], [43, 2, 37, 0], [43, 11, 37, 9, "chooseAction"], [43, 23, 37, 21, "chooseAction"], [43, 24, 37, 22, "animationType"], [43, 37, 37, 35], [43, 39, 37, 37, "animationConfig"], [43, 54, 37, 52], [43, 56, 37, 54, "element"], [43, 63, 37, 61], [43, 65, 37, 63, "transitionData"], [43, 79, 37, 77], [43, 81, 37, 79], [44, 4, 38, 2], [44, 12, 38, 10, "animationType"], [44, 25, 38, 23], [45, 6, 39, 4], [45, 11, 39, 9, "LayoutAnimationType"], [45, 43, 39, 28], [45, 44, 39, 29, "ENTERING"], [45, 52, 39, 37], [46, 8, 40, 6], [46, 12, 40, 6, "setElementAnimation"], [46, 47, 40, 25], [46, 49, 40, 26, "element"], [46, 56, 40, 33], [46, 58, 40, 35, "animationConfig"], [46, 73, 40, 50], [46, 75, 40, 52], [46, 79, 40, 56], [46, 80, 40, 57], [47, 8, 41, 6], [48, 6, 42, 4], [48, 11, 42, 9, "LayoutAnimationType"], [48, 43, 42, 28], [48, 44, 42, 29, "LAYOUT"], [48, 50, 42, 35], [49, 8, 43, 6, "transitionData"], [49, 22, 43, 20], [49, 23, 43, 21, "reversed"], [49, 31, 43, 29], [49, 34, 43, 32, "animationConfig"], [49, 49, 43, 47], [49, 50, 43, 48, "reversed"], [49, 58, 43, 56], [50, 8, 44, 6], [50, 12, 44, 6, "handleLayoutTransition"], [50, 50, 44, 28], [50, 52, 44, 29, "element"], [50, 59, 44, 36], [50, 61, 44, 38, "animationConfig"], [50, 76, 44, 53], [50, 78, 44, 55, "transitionData"], [50, 92, 44, 69], [50, 93, 44, 70], [51, 8, 45, 6], [52, 6, 46, 4], [52, 11, 46, 9, "LayoutAnimationType"], [52, 43, 46, 28], [52, 44, 46, 29, "EXITING"], [52, 51, 46, 36], [53, 8, 47, 6], [53, 12, 47, 6, "handleExitingAnimation"], [53, 50, 47, 28], [53, 52, 47, 29, "element"], [53, 59, 47, 36], [53, 61, 47, 38, "animationConfig"], [53, 76, 47, 53], [53, 77, 47, 54], [54, 8, 48, 6], [55, 4, 49, 2], [56, 2, 50, 0], [57, 2, 51, 0], [57, 11, 51, 9, "tryGetAnimationConfig"], [57, 32, 51, 30, "tryGetAnimationConfig"], [57, 33, 51, 31, "props"], [57, 38, 51, 36], [57, 40, 51, 38, "animationType"], [57, 53, 51, 51], [57, 55, 51, 53], [58, 4, 52, 2], [58, 10, 52, 8, "config"], [58, 16, 52, 14], [58, 19, 52, 17, "chooseConfig"], [58, 31, 52, 29], [58, 32, 52, 30, "animationType"], [58, 45, 52, 43], [58, 47, 52, 45, "props"], [58, 52, 52, 50], [58, 53, 52, 51], [59, 4, 53, 2], [59, 8, 53, 6], [59, 9, 53, 7, "config"], [59, 15, 53, 13], [59, 17, 53, 15], [60, 6, 54, 4], [60, 13, 54, 11], [60, 17, 54, 15], [61, 4, 55, 2], [62, 4, 56, 2], [62, 10, 56, 8, "isLayoutTransition"], [62, 28, 56, 26], [62, 31, 56, 29, "animationType"], [62, 44, 56, 42], [62, 49, 56, 47, "LayoutAnimationType"], [62, 81, 56, 66], [62, 82, 56, 67, "LAYOUT"], [62, 88, 56, 73], [63, 4, 57, 2], [63, 10, 57, 8, "isCustomKeyframe"], [63, 26, 57, 24], [63, 29, 57, 27, "config"], [63, 35, 57, 33], [63, 47, 57, 45, "Keyframe"], [63, 63, 57, 53], [64, 4, 58, 2], [64, 10, 58, 8, "hasInitialValues"], [64, 26, 58, 24], [64, 29, 58, 27, "config"], [64, 35, 58, 33], [64, 36, 58, 34, "initialValues"], [64, 49, 58, 47], [64, 54, 58, 52, "undefined"], [64, 63, 58, 61], [65, 4, 59, 2], [65, 8, 59, 6, "animationName"], [65, 21, 59, 19], [66, 4, 60, 2], [66, 8, 60, 6, "isCustomKeyframe"], [66, 24, 60, 22], [66, 26, 60, 24], [67, 6, 61, 4, "animationName"], [67, 19, 61, 17], [67, 22, 61, 20], [67, 26, 61, 20, "createCustomKeyFrameAnimation"], [67, 72, 61, 49], [67, 74, 61, 50, "config"], [67, 80, 61, 56], [67, 81, 61, 57, "definitions"], [67, 92, 61, 68], [67, 93, 61, 69], [68, 4, 62, 2], [68, 5, 62, 3], [68, 11, 62, 9], [68, 15, 62, 13], [68, 22, 62, 20, "config"], [68, 28, 62, 26], [68, 33, 62, 31], [68, 43, 62, 41], [68, 45, 62, 43], [69, 6, 63, 4, "animationName"], [69, 19, 63, 17], [69, 22, 63, 20, "config"], [69, 28, 63, 26], [69, 29, 63, 27, "presetName"], [69, 39, 63, 37], [70, 4, 64, 2], [70, 5, 64, 3], [70, 11, 64, 9], [71, 6, 65, 4, "animationName"], [71, 19, 65, 17], [71, 22, 65, 20, "config"], [71, 28, 65, 26], [71, 29, 65, 27, "constructor"], [71, 40, 65, 38], [71, 41, 65, 39, "presetName"], [71, 51, 65, 49], [72, 4, 66, 2], [73, 4, 67, 2], [73, 8, 67, 6, "hasInitialValues"], [73, 24, 67, 22], [73, 26, 67, 24], [74, 6, 68, 4, "animationName"], [74, 19, 68, 17], [74, 22, 68, 20], [74, 26, 68, 20, "createAnimationWithInitialValues"], [74, 75, 68, 52], [74, 77, 68, 53, "animationName"], [74, 90, 68, 66], [74, 92, 68, 68, "config"], [74, 98, 68, 74], [74, 99, 68, 75, "initialValues"], [74, 112, 68, 88], [74, 113, 68, 89], [75, 4, 69, 2], [76, 4, 70, 2], [76, 10, 70, 8, "shouldFail"], [76, 20, 70, 18], [76, 23, 70, 21, "checkUndefinedAnimationFail"], [76, 50, 70, 48], [76, 51, 70, 49, "animationName"], [76, 64, 70, 62], [76, 66, 70, 64, "isLayoutTransition"], [76, 84, 70, 82], [76, 88, 70, 86, "isCustomKeyframe"], [76, 104, 70, 102], [76, 108, 70, 106, "hasInitialValues"], [76, 124, 70, 122], [76, 125, 70, 123], [77, 4, 71, 2], [77, 8, 71, 6, "shouldFail"], [77, 18, 71, 16], [77, 20, 71, 18], [78, 6, 72, 4], [78, 13, 72, 11], [78, 17, 72, 15], [79, 4, 73, 2], [80, 4, 74, 2], [80, 8, 74, 6, "isCustomKeyframe"], [80, 24, 74, 22], [80, 26, 74, 24], [81, 6, 75, 4], [81, 12, 75, 10, "keyframeTimestamps"], [81, 30, 75, 28], [81, 33, 75, 31, "Object"], [81, 39, 75, 37], [81, 40, 75, 38, "keys"], [81, 44, 75, 42], [81, 45, 75, 43, "config"], [81, 51, 75, 49], [81, 52, 75, 50, "definitions"], [81, 63, 75, 61], [81, 64, 75, 62], [82, 6, 76, 4], [82, 10, 76, 8], [82, 12, 76, 10, "keyframeTimestamps"], [82, 30, 76, 28], [82, 31, 76, 29, "includes"], [82, 39, 76, 37], [82, 40, 76, 38], [82, 45, 76, 43], [82, 46, 76, 44], [82, 50, 76, 48, "keyframeTimestamps"], [82, 68, 76, 66], [82, 69, 76, 67, "includes"], [82, 77, 76, 75], [82, 78, 76, 76], [82, 82, 76, 80], [82, 83, 76, 81], [82, 84, 76, 82], [82, 86, 76, 84], [83, 8, 77, 6, "logger"], [83, 21, 77, 12], [83, 22, 77, 13, "warn"], [83, 26, 77, 17], [83, 27, 77, 18], [83, 234, 77, 225], [83, 235, 77, 226], [84, 6, 78, 4], [85, 4, 79, 2], [86, 4, 80, 2], [86, 10, 80, 8, "animationConfig"], [86, 25, 80, 23], [86, 28, 80, 26], [86, 32, 80, 26, "getProcessedConfig"], [86, 66, 80, 44], [86, 68, 80, 45, "animationName"], [86, 81, 80, 58], [86, 83, 80, 60, "animationType"], [86, 96, 80, 73], [86, 98, 80, 75, "config"], [86, 104, 80, 81], [86, 105, 80, 82], [87, 4, 81, 2], [87, 11, 81, 9, "animationConfig"], [87, 26, 81, 24], [88, 2, 82, 0], [89, 2, 83, 7], [89, 11, 83, 16, "startWebLayoutAnimation"], [89, 34, 83, 39, "startWebLayoutAnimation"], [89, 35, 83, 40, "props"], [89, 40, 83, 45], [89, 42, 83, 47, "element"], [89, 49, 83, 54], [89, 51, 83, 56, "animationType"], [89, 64, 83, 69], [89, 66, 83, 71, "transitionData"], [89, 80, 83, 85], [89, 82, 83, 87], [90, 4, 84, 2], [90, 10, 84, 8, "animationConfig"], [90, 25, 84, 23], [90, 28, 84, 26, "tryGetAnimationConfig"], [90, 49, 84, 47], [90, 50, 84, 48, "props"], [90, 55, 84, 53], [90, 57, 84, 55, "animationType"], [90, 70, 84, 68], [90, 71, 84, 69], [91, 4, 85, 2], [91, 8, 85, 2, "maybeModifyStyleForKeyframe"], [91, 51, 85, 29], [91, 53, 85, 30, "element"], [91, 60, 85, 37], [91, 62, 85, 39, "props"], [91, 67, 85, 44], [91, 68, 85, 45, "entering"], [91, 76, 85, 53], [91, 77, 85, 54], [92, 4, 86, 2], [92, 8, 86, 6, "animationConfig"], [92, 23, 86, 21], [92, 25, 86, 23, "animationName"], [92, 38, 86, 36], [92, 42, 86, 40, "Animations"], [92, 60, 86, 50], [92, 62, 86, 52], [93, 6, 87, 4, "maybeReportOverwrittenProperties"], [93, 38, 87, 36], [93, 39, 87, 37, "Animations"], [93, 57, 87, 47], [93, 58, 87, 48, "animationConfig"], [93, 73, 87, 63], [93, 75, 87, 65, "animationName"], [93, 88, 87, 78], [93, 89, 87, 79], [93, 90, 87, 80, "style"], [93, 95, 87, 85], [93, 97, 87, 87, "element"], [93, 104, 87, 94], [93, 105, 87, 95, "style"], [93, 110, 87, 100], [93, 111, 87, 101], [94, 4, 88, 2], [95, 4, 89, 2], [95, 8, 89, 6, "animationConfig"], [95, 23, 89, 21], [95, 25, 89, 23], [96, 6, 90, 4, "chooseAction"], [96, 18, 90, 16], [96, 19, 90, 17, "animationType"], [96, 32, 90, 30], [96, 34, 90, 32, "animationConfig"], [96, 49, 90, 47], [96, 51, 90, 49, "element"], [96, 58, 90, 56], [96, 60, 90, 58, "transitionData"], [96, 74, 90, 72], [96, 75, 90, 73], [97, 4, 91, 2], [97, 5, 91, 3], [97, 11, 91, 9], [98, 6, 92, 4], [98, 10, 92, 4, "makeElementVisible"], [98, 44, 92, 22], [98, 46, 92, 23, "element"], [98, 53, 92, 30], [98, 55, 92, 32], [98, 56, 92, 33], [98, 57, 92, 34], [99, 4, 93, 2], [100, 2, 94, 0], [101, 2, 95, 7], [101, 11, 95, 16, "tryActivateLayoutTransition"], [101, 38, 95, 43, "tryActivateLayoutTransition"], [101, 39, 95, 44, "props"], [101, 44, 95, 49], [101, 46, 95, 51, "element"], [101, 53, 95, 58], [101, 55, 95, 60, "snapshot"], [101, 63, 95, 68], [101, 65, 95, 70], [102, 4, 96, 2], [102, 8, 96, 6], [102, 9, 96, 7, "props"], [102, 14, 96, 12], [102, 15, 96, 13, "layout"], [102, 21, 96, 19], [102, 23, 96, 21], [103, 6, 97, 4], [104, 4, 98, 2], [105, 4, 99, 2], [105, 10, 99, 8, "rect"], [105, 14, 99, 12], [105, 17, 99, 15, "element"], [105, 24, 99, 22], [105, 25, 99, 23, "getBoundingClientRect"], [105, 46, 99, 44], [105, 47, 99, 45], [105, 48, 99, 46], [106, 4, 100, 2], [106, 8, 100, 6], [106, 12, 100, 6, "areDOMRectsEqual"], [106, 38, 100, 22], [106, 40, 100, 23, "rect"], [106, 44, 100, 27], [106, 46, 100, 29, "snapshot"], [106, 54, 100, 37], [106, 55, 100, 38], [106, 57, 100, 40], [107, 6, 101, 4], [108, 4, 102, 2], [109, 4, 103, 2], [109, 10, 103, 8, "enteringAnimation"], [109, 27, 103, 25], [109, 30, 103, 28, "props"], [109, 35, 103, 33], [109, 36, 103, 34, "layout"], [109, 42, 103, 40], [109, 43, 103, 41, "enteringV"], [109, 52, 103, 50], [109, 54, 103, 52, "presetName"], [109, 64, 103, 62], [110, 4, 104, 2], [110, 10, 104, 8, "exitingAnimation"], [110, 26, 104, 24], [110, 29, 104, 27, "props"], [110, 34, 104, 32], [110, 35, 104, 33, "layout"], [110, 41, 104, 39], [110, 42, 104, 40, "exitingV"], [110, 50, 104, 48], [110, 52, 104, 50, "presetName"], [110, 62, 104, 60], [111, 4, 105, 2], [111, 10, 105, 8, "deltaX"], [111, 16, 105, 14], [111, 19, 105, 17], [111, 20, 105, 18, "snapshot"], [111, 28, 105, 26], [111, 29, 105, 27, "width"], [111, 34, 105, 32], [111, 37, 105, 35, "rect"], [111, 41, 105, 39], [111, 42, 105, 40, "width"], [111, 47, 105, 45], [111, 51, 105, 49], [111, 52, 105, 50], [112, 4, 106, 2], [112, 10, 106, 8, "deltaY"], [112, 16, 106, 14], [112, 19, 106, 17], [112, 20, 106, 18, "snapshot"], [112, 28, 106, 26], [112, 29, 106, 27, "height"], [112, 35, 106, 33], [112, 38, 106, 36, "rect"], [112, 42, 106, 40], [112, 43, 106, 41, "height"], [112, 49, 106, 47], [112, 53, 106, 51], [112, 54, 106, 52], [113, 4, 107, 2], [113, 10, 107, 8, "transitionData"], [113, 24, 107, 22], [113, 27, 107, 25], [114, 6, 108, 4, "translateX"], [114, 16, 108, 14], [114, 18, 108, 16, "snapshot"], [114, 26, 108, 24], [114, 27, 108, 25, "x"], [114, 28, 108, 26], [114, 31, 108, 29, "rect"], [114, 35, 108, 33], [114, 36, 108, 34, "x"], [114, 37, 108, 35], [114, 40, 108, 38, "deltaX"], [114, 46, 108, 44], [115, 6, 109, 4, "translateY"], [115, 16, 109, 14], [115, 18, 109, 16, "snapshot"], [115, 26, 109, 24], [115, 27, 109, 25, "y"], [115, 28, 109, 26], [115, 31, 109, 29, "rect"], [115, 35, 109, 33], [115, 36, 109, 34, "y"], [115, 37, 109, 35], [115, 40, 109, 38, "deltaY"], [115, 46, 109, 44], [116, 6, 110, 4, "scaleX"], [116, 12, 110, 10], [116, 14, 110, 12, "snapshot"], [116, 22, 110, 20], [116, 23, 110, 21, "width"], [116, 28, 110, 26], [116, 31, 110, 29, "rect"], [116, 35, 110, 33], [116, 36, 110, 34, "width"], [116, 41, 110, 39], [117, 6, 111, 4, "scaleY"], [117, 12, 111, 10], [117, 14, 111, 12, "snapshot"], [117, 22, 111, 20], [117, 23, 111, 21, "height"], [117, 29, 111, 27], [117, 32, 111, 30, "rect"], [117, 36, 111, 34], [117, 37, 111, 35, "height"], [117, 43, 111, 41], [118, 6, 112, 4, "reversed"], [118, 14, 112, 12], [118, 16, 112, 14], [118, 21, 112, 19], [119, 6, 113, 4], [120, 6, 114, 4, "easingX"], [120, 13, 114, 11], [120, 15, 114, 13, "props"], [120, 20, 114, 18], [120, 21, 114, 19, "layout"], [120, 27, 114, 25], [120, 28, 114, 26, "easingXV"], [120, 36, 114, 34], [120, 39, 114, 37, "EasingNameSymbol"], [120, 63, 114, 53], [120, 64, 114, 54], [120, 68, 114, 58], [120, 74, 114, 64], [121, 6, 115, 4, "easingY"], [121, 13, 115, 11], [121, 15, 115, 13, "props"], [121, 20, 115, 18], [121, 21, 115, 19, "layout"], [121, 27, 115, 25], [121, 28, 115, 26, "easingYV"], [121, 36, 115, 34], [121, 39, 115, 37, "EasingNameSymbol"], [121, 63, 115, 53], [121, 64, 115, 54], [121, 68, 115, 58], [121, 74, 115, 64], [122, 6, 116, 4, "entering"], [122, 14, 116, 12], [122, 16, 116, 14, "enteringAnimation"], [122, 33, 116, 31], [123, 6, 117, 4, "exiting"], [123, 13, 117, 11], [123, 15, 117, 13, "exitingAnimation"], [124, 4, 118, 2], [124, 5, 118, 3], [125, 4, 119, 2, "startWebLayoutAnimation"], [125, 27, 119, 25], [125, 28, 119, 26, "props"], [125, 33, 119, 31], [125, 35, 119, 33, "element"], [125, 42, 119, 40], [125, 44, 119, 42, "LayoutAnimationType"], [125, 76, 119, 61], [125, 77, 119, 62, "LAYOUT"], [125, 83, 119, 68], [125, 85, 119, 70, "transitionData"], [125, 99, 119, 84], [125, 100, 119, 85], [126, 2, 120, 0], [127, 0, 120, 1], [127, 3]], "functionMap": {"names": ["<global>", "chooseConfig", "checkUndefinedAnimationFail", "maybeReportOverwrittenProperties", "Array.from.filter$argument_0", "chooseAction", "tryGetAnimationConfig", "startWebLayoutAnimation", "tryActivateLayoutTransition"], "mappings": "AAA;ACW;CDG;AEC;CFQ;AGC;qDCM,uCD;CHK;AKC;CLa;AMC;CN+B;OOC;CPW;OQC;CRyB"}}, "type": "js/module"}]}