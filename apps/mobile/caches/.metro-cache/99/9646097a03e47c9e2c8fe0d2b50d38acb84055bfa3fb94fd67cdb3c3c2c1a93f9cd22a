{"dependencies": [{"name": "react-native-web/dist/exports/StyleSheet/compiler/createReactDOMStyle", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 2, "index": 400}, "end": {"line": 12, "column": 82, "index": 480}}], "key": "tFeH++xEHV9tfVFgvcIpCzXhecE=", "exportNames": ["*"], "isOptional": true}}, {"name": "react-native-web/dist/exports/StyleSheet/preprocess", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 2, "index": 631}, "end": {"line": 18, "column": 64, "index": 693}}, {"start": {"line": 23, "column": 2, "index": 830}, "end": {"line": 23, "column": 64, "index": 892}}], "key": "Dg03p7ByEvblHScexMoQmR63fmo=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createTransformValue = exports.createTextShadowValue = exports.createReactDOMStyle = void 0;\n  let createReactDOMStyle = exports.createReactDOMStyle = void 0;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let createTransformValue = exports.createTransformValue = void 0;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let createTextShadowValue = exports.createTextShadowValue = void 0;\n  try {\n    exports.createReactDOMStyle = createReactDOMStyle =\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    require(_dependencyMap[0], \"react-native-web/dist/exports/StyleSheet/compiler/createReactDOMStyle\").default;\n  } catch (e) {}\n  try {\n    // React Native Web 0.19+\n    exports.createTransformValue = createTransformValue =\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    require(_dependencyMap[1], \"react-native-web/dist/exports/StyleSheet/preprocess\").createTransformValue;\n  } catch (e) {}\n  try {\n    exports.createTextShadowValue = createTextShadowValue =\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    require(_dependencyMap[1], \"react-native-web/dist/exports/StyleSheet/preprocess\").createTextShadowValue;\n  } catch (e) {}\n});", "lineCount": 30, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 0], [5, 9, 3, 0, "defineProperty"], [5, 23, 3, 0], [5, 24, 3, 0, "exports"], [5, 31, 3, 0], [6, 4, 3, 0, "value"], [6, 9, 3, 0], [7, 2, 3, 0], [8, 2, 3, 0, "exports"], [8, 9, 3, 0], [8, 10, 3, 0, "createTransformValue"], [8, 30, 3, 0], [8, 33, 3, 0, "exports"], [8, 40, 3, 0], [8, 41, 3, 0, "createTextShadowValue"], [8, 62, 3, 0], [8, 65, 3, 0, "exports"], [8, 72, 3, 0], [8, 73, 3, 0, "createReactDOMStyle"], [8, 92, 3, 0], [9, 2, 4, 7], [9, 6, 4, 11, "createReactDOMStyle"], [9, 25, 4, 30], [9, 28, 4, 30, "exports"], [9, 35, 4, 30], [9, 36, 4, 30, "createReactDOMStyle"], [9, 55, 4, 30], [10, 2, 5, 0], [11, 2, 6, 7], [11, 6, 6, 11, "createTransformValue"], [11, 26, 6, 31], [11, 29, 6, 31, "exports"], [11, 36, 6, 31], [11, 37, 6, 31, "createTransformValue"], [11, 57, 6, 31], [12, 2, 7, 0], [13, 2, 8, 7], [13, 6, 8, 11, "createTextShadowValue"], [13, 27, 8, 32], [13, 30, 8, 32, "exports"], [13, 37, 8, 32], [13, 38, 8, 32, "createTextShadowValue"], [13, 59, 8, 32], [14, 2, 9, 0], [14, 6, 9, 4], [15, 4, 10, 2, "exports"], [15, 11, 10, 2], [15, 12, 10, 2, "createReactDOMStyle"], [15, 31, 10, 2], [15, 34, 10, 2, "createReactDOMStyle"], [15, 53, 10, 21], [16, 4, 11, 2], [17, 4, 12, 2, "require"], [17, 11, 12, 9], [17, 12, 12, 9, "_dependencyMap"], [17, 26, 12, 9], [17, 102, 12, 81], [17, 103, 12, 82], [17, 104, 12, 83, "default"], [17, 111, 12, 90], [18, 2, 13, 0], [18, 3, 13, 1], [18, 4, 13, 2], [18, 11, 13, 9, "e"], [18, 12, 13, 10], [18, 14, 13, 12], [18, 15, 13, 13], [19, 2, 14, 0], [19, 6, 14, 4], [20, 4, 15, 2], [21, 4, 16, 2, "exports"], [21, 11, 16, 2], [21, 12, 16, 2, "createTransformValue"], [21, 32, 16, 2], [21, 35, 16, 2, "createTransformValue"], [21, 55, 16, 22], [22, 4, 17, 2], [23, 4, 18, 2, "require"], [23, 11, 18, 9], [23, 12, 18, 9, "_dependencyMap"], [23, 26, 18, 9], [23, 84, 18, 63], [23, 85, 18, 64], [23, 86, 18, 65, "createTransformValue"], [23, 106, 18, 85], [24, 2, 19, 0], [24, 3, 19, 1], [24, 4, 19, 2], [24, 11, 19, 9, "e"], [24, 12, 19, 10], [24, 14, 19, 12], [24, 15, 19, 13], [25, 2, 20, 0], [25, 6, 20, 4], [26, 4, 21, 2, "exports"], [26, 11, 21, 2], [26, 12, 21, 2, "createTextShadowValue"], [26, 33, 21, 2], [26, 36, 21, 2, "createTextShadowValue"], [26, 57, 21, 23], [27, 4, 22, 2], [28, 4, 23, 2, "require"], [28, 11, 23, 9], [28, 12, 23, 9, "_dependencyMap"], [28, 26, 23, 9], [28, 84, 23, 63], [28, 85, 23, 64], [28, 86, 23, 65, "createTextShadowValue"], [28, 107, 23, 86], [29, 2, 24, 0], [29, 3, 24, 1], [29, 4, 24, 2], [29, 11, 24, 9, "e"], [29, 12, 24, 10], [29, 14, 24, 12], [29, 15, 24, 13], [30, 0, 24, 14], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}