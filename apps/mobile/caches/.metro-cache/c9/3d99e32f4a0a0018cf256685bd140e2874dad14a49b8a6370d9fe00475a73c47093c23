{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VEC_LEN_SQ = exports.TEST_MIN_IF_NOT_NAN = exports.TEST_MAX_IF_NOT_NAN = void 0;\n  exports.fireAfterInterval = fireAfterInterval;\n  exports.isnan = exports.isValidNumber = void 0;\n  // TODO(TS) remove if not necessary after rewrite\n  const isnan = v => Number.isNaN(v); // TODO(TS) remove if not necessary after rewrite\n  exports.isnan = isnan;\n  const isValidNumber = v => typeof v === 'number' && !Number.isNaN(v);\n  exports.isValidNumber = isValidNumber;\n  const TEST_MIN_IF_NOT_NAN = (value, limit) => !isnan(limit) && (limit < 0 && value <= limit || limit >= 0 && value >= limit);\n  exports.TEST_MIN_IF_NOT_NAN = TEST_MIN_IF_NOT_NAN;\n  const VEC_LEN_SQ = ({\n    x = 0,\n    y = 0\n  } = {}) => x * x + y * y;\n  exports.VEC_LEN_SQ = VEC_LEN_SQ;\n  const TEST_MAX_IF_NOT_NAN = (value, max) => !isnan(max) && (max < 0 && value < max || max >= 0 && value > max);\n  exports.TEST_MAX_IF_NOT_NAN = TEST_MAX_IF_NOT_NAN;\n  function fireAfterInterval(method, interval) {\n    if (!interval) {\n      method();\n      return null;\n    }\n    return setTimeout(() => method(), interval);\n  }\n});", "lineCount": 29, "map": [[8, 2, 1, 0], [9, 2, 2, 7], [9, 8, 2, 13, "isnan"], [9, 13, 2, 18], [9, 16, 2, 21, "v"], [9, 17, 2, 22], [9, 21, 2, 26, "Number"], [9, 27, 2, 32], [9, 28, 2, 33, "isNaN"], [9, 33, 2, 38], [9, 34, 2, 39, "v"], [9, 35, 2, 40], [9, 36, 2, 41], [9, 37, 2, 42], [9, 38, 2, 43], [10, 2, 2, 43, "exports"], [10, 9, 2, 43], [10, 10, 2, 43, "isnan"], [10, 15, 2, 43], [10, 18, 2, 43, "isnan"], [10, 23, 2, 43], [11, 2, 4, 7], [11, 8, 4, 13, "isValidNumber"], [11, 21, 4, 26], [11, 24, 4, 29, "v"], [11, 25, 4, 30], [11, 29, 4, 34], [11, 36, 4, 41, "v"], [11, 37, 4, 42], [11, 42, 4, 47], [11, 50, 4, 55], [11, 54, 4, 59], [11, 55, 4, 60, "Number"], [11, 61, 4, 66], [11, 62, 4, 67, "isNaN"], [11, 67, 4, 72], [11, 68, 4, 73, "v"], [11, 69, 4, 74], [11, 70, 4, 75], [12, 2, 4, 76, "exports"], [12, 9, 4, 76], [12, 10, 4, 76, "isValidNumber"], [12, 23, 4, 76], [12, 26, 4, 76, "isValidNumber"], [12, 39, 4, 76], [13, 2, 5, 7], [13, 8, 5, 13, "TEST_MIN_IF_NOT_NAN"], [13, 27, 5, 32], [13, 30, 5, 35, "TEST_MIN_IF_NOT_NAN"], [13, 31, 5, 36, "value"], [13, 36, 5, 41], [13, 38, 5, 43, "limit"], [13, 43, 5, 48], [13, 48, 5, 53], [13, 49, 5, 54, "isnan"], [13, 54, 5, 59], [13, 55, 5, 60, "limit"], [13, 60, 5, 65], [13, 61, 5, 66], [13, 66, 5, 71, "limit"], [13, 71, 5, 76], [13, 74, 5, 79], [13, 75, 5, 80], [13, 79, 5, 84, "value"], [13, 84, 5, 89], [13, 88, 5, 93, "limit"], [13, 93, 5, 98], [13, 97, 5, 102, "limit"], [13, 102, 5, 107], [13, 106, 5, 111], [13, 107, 5, 112], [13, 111, 5, 116, "value"], [13, 116, 5, 121], [13, 120, 5, 125, "limit"], [13, 125, 5, 130], [13, 126, 5, 131], [14, 2, 5, 132, "exports"], [14, 9, 5, 132], [14, 10, 5, 132, "TEST_MIN_IF_NOT_NAN"], [14, 29, 5, 132], [14, 32, 5, 132, "TEST_MIN_IF_NOT_NAN"], [14, 51, 5, 132], [15, 2, 6, 7], [15, 8, 6, 13, "VEC_LEN_SQ"], [15, 18, 6, 23], [15, 21, 6, 26, "VEC_LEN_SQ"], [15, 22, 6, 27], [16, 4, 7, 2, "x"], [16, 5, 7, 3], [16, 8, 7, 6], [16, 9, 7, 7], [17, 4, 8, 2, "y"], [17, 5, 8, 3], [17, 8, 8, 6], [18, 2, 9, 0], [18, 3, 9, 1], [18, 6, 9, 4], [18, 7, 9, 5], [18, 8, 9, 6], [18, 13, 9, 11, "x"], [18, 14, 9, 12], [18, 17, 9, 15, "x"], [18, 18, 9, 16], [18, 21, 9, 19, "y"], [18, 22, 9, 20], [18, 25, 9, 23, "y"], [18, 26, 9, 24], [19, 2, 9, 25, "exports"], [19, 9, 9, 25], [19, 10, 9, 25, "VEC_LEN_SQ"], [19, 20, 9, 25], [19, 23, 9, 25, "VEC_LEN_SQ"], [19, 33, 9, 25], [20, 2, 10, 7], [20, 8, 10, 13, "TEST_MAX_IF_NOT_NAN"], [20, 27, 10, 32], [20, 30, 10, 35, "TEST_MAX_IF_NOT_NAN"], [20, 31, 10, 36, "value"], [20, 36, 10, 41], [20, 38, 10, 43, "max"], [20, 41, 10, 46], [20, 46, 10, 51], [20, 47, 10, 52, "isnan"], [20, 52, 10, 57], [20, 53, 10, 58, "max"], [20, 56, 10, 61], [20, 57, 10, 62], [20, 62, 10, 67, "max"], [20, 65, 10, 70], [20, 68, 10, 73], [20, 69, 10, 74], [20, 73, 10, 78, "value"], [20, 78, 10, 83], [20, 81, 10, 86, "max"], [20, 84, 10, 89], [20, 88, 10, 93, "max"], [20, 91, 10, 96], [20, 95, 10, 100], [20, 96, 10, 101], [20, 100, 10, 105, "value"], [20, 105, 10, 110], [20, 108, 10, 113, "max"], [20, 111, 10, 116], [20, 112, 10, 117], [21, 2, 10, 118, "exports"], [21, 9, 10, 118], [21, 10, 10, 118, "TEST_MAX_IF_NOT_NAN"], [21, 29, 10, 118], [21, 32, 10, 118, "TEST_MAX_IF_NOT_NAN"], [21, 51, 10, 118], [22, 2, 11, 7], [22, 11, 11, 16, "fireAfterInterval"], [22, 28, 11, 33, "fireAfterInterval"], [22, 29, 11, 34, "method"], [22, 35, 11, 40], [22, 37, 11, 42, "interval"], [22, 45, 11, 50], [22, 47, 11, 52], [23, 4, 12, 2], [23, 8, 12, 6], [23, 9, 12, 7, "interval"], [23, 17, 12, 15], [23, 19, 12, 17], [24, 6, 13, 4, "method"], [24, 12, 13, 10], [24, 13, 13, 11], [24, 14, 13, 12], [25, 6, 14, 4], [25, 13, 14, 11], [25, 17, 14, 15], [26, 4, 15, 2], [27, 4, 17, 2], [27, 11, 17, 9, "setTimeout"], [27, 21, 17, 19], [27, 22, 17, 20], [27, 28, 17, 26, "method"], [27, 34, 17, 32], [27, 35, 17, 33], [27, 36, 17, 34], [27, 38, 17, 36, "interval"], [27, 46, 17, 44], [27, 47, 17, 45], [28, 2, 18, 0], [29, 0, 18, 1], [29, 3]], "functionMap": {"names": ["<global>", "isnan", "isValidNumber", "TEST_MIN_IF_NOT_NAN", "VEC_LEN_SQ", "TEST_MAX_IF_NOT_NAN", "fireAfterInterval", "setTimeout$argument_0"], "mappings": "AAA;qBCC,oBD;6BEE,8CF;mCGC,gGH;0BIC;wBJG;mCKC,kFL;OMC;oBCM,cD;CNC"}}, "type": "js/module"}]}