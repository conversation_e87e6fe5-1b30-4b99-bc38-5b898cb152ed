{"dependencies": [{"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 67, "index": 82}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 83}, "end": {"line": 4, "column": 31, "index": 114}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useInvalidPreventRemoveError = useInvalidPreventRemoveError;\n  var _native = require(_dependencyMap[0], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useInvalidPreventRemoveError(descriptors) {\n    var _usePreventRemoveCont = (0, _native.usePreventRemoveContext)(),\n      preventedRoutes = _usePreventRemoveCont.preventedRoutes;\n    var preventedRouteKey = Object.keys(preventedRoutes)[0];\n    var preventedDescriptor = descriptors[preventedRouteKey];\n    var isHeaderBackButtonMenuEnabledOnPreventedScreen = preventedDescriptor?.options?.headerBackButtonMenuEnabled;\n    var preventedRouteName = preventedDescriptor?.route?.name;\n    React.useEffect(() => {\n      if (preventedRouteKey != null && isHeaderBackButtonMenuEnabledOnPreventedScreen) {\n        var message = `The screen ${preventedRouteName} uses 'usePreventRemove' hook alongside 'headerBackButtonMenuEnabled: true', which is not supported. \\n\\n` + `Consider removing 'headerBackButtonMenuEnabled: true' from ${preventedRouteName} screen to get rid of this error.`;\n        console.error(message);\n      }\n    }, [preventedRouteKey, isHeaderBackButtonMenuEnabledOnPreventedScreen, preventedRouteName]);\n  }\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useInvalidPreventRemoveError"], [7, 38, 1, 13], [7, 41, 1, 13, "useInvalidPreventRemoveError"], [7, 69, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_native"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 4, 31], [10, 11, 4, 31, "_interopRequireWildcard"], [10, 35, 4, 31, "e"], [10, 36, 4, 31], [10, 38, 4, 31, "t"], [10, 39, 4, 31], [10, 68, 4, 31, "WeakMap"], [10, 75, 4, 31], [10, 81, 4, 31, "r"], [10, 82, 4, 31], [10, 89, 4, 31, "WeakMap"], [10, 96, 4, 31], [10, 100, 4, 31, "n"], [10, 101, 4, 31], [10, 108, 4, 31, "WeakMap"], [10, 115, 4, 31], [10, 127, 4, 31, "_interopRequireWildcard"], [10, 150, 4, 31], [10, 162, 4, 31, "_interopRequireWildcard"], [10, 163, 4, 31, "e"], [10, 164, 4, 31], [10, 166, 4, 31, "t"], [10, 167, 4, 31], [10, 176, 4, 31, "t"], [10, 177, 4, 31], [10, 181, 4, 31, "e"], [10, 182, 4, 31], [10, 186, 4, 31, "e"], [10, 187, 4, 31], [10, 188, 4, 31, "__esModule"], [10, 198, 4, 31], [10, 207, 4, 31, "e"], [10, 208, 4, 31], [10, 214, 4, 31, "o"], [10, 215, 4, 31], [10, 217, 4, 31, "i"], [10, 218, 4, 31], [10, 220, 4, 31, "f"], [10, 221, 4, 31], [10, 226, 4, 31, "__proto__"], [10, 235, 4, 31], [10, 243, 4, 31, "default"], [10, 250, 4, 31], [10, 252, 4, 31, "e"], [10, 253, 4, 31], [10, 270, 4, 31, "e"], [10, 271, 4, 31], [10, 294, 4, 31, "e"], [10, 295, 4, 31], [10, 320, 4, 31, "e"], [10, 321, 4, 31], [10, 330, 4, 31, "f"], [10, 331, 4, 31], [10, 337, 4, 31, "o"], [10, 338, 4, 31], [10, 341, 4, 31, "t"], [10, 342, 4, 31], [10, 345, 4, 31, "n"], [10, 346, 4, 31], [10, 349, 4, 31, "r"], [10, 350, 4, 31], [10, 358, 4, 31, "o"], [10, 359, 4, 31], [10, 360, 4, 31, "has"], [10, 363, 4, 31], [10, 364, 4, 31, "e"], [10, 365, 4, 31], [10, 375, 4, 31, "o"], [10, 376, 4, 31], [10, 377, 4, 31, "get"], [10, 380, 4, 31], [10, 381, 4, 31, "e"], [10, 382, 4, 31], [10, 385, 4, 31, "o"], [10, 386, 4, 31], [10, 387, 4, 31, "set"], [10, 390, 4, 31], [10, 391, 4, 31, "e"], [10, 392, 4, 31], [10, 394, 4, 31, "f"], [10, 395, 4, 31], [10, 409, 4, 31, "_t"], [10, 411, 4, 31], [10, 415, 4, 31, "e"], [10, 416, 4, 31], [10, 432, 4, 31, "_t"], [10, 434, 4, 31], [10, 441, 4, 31, "hasOwnProperty"], [10, 455, 4, 31], [10, 456, 4, 31, "call"], [10, 460, 4, 31], [10, 461, 4, 31, "e"], [10, 462, 4, 31], [10, 464, 4, 31, "_t"], [10, 466, 4, 31], [10, 473, 4, 31, "i"], [10, 474, 4, 31], [10, 478, 4, 31, "o"], [10, 479, 4, 31], [10, 482, 4, 31, "Object"], [10, 488, 4, 31], [10, 489, 4, 31, "defineProperty"], [10, 503, 4, 31], [10, 508, 4, 31, "Object"], [10, 514, 4, 31], [10, 515, 4, 31, "getOwnPropertyDescriptor"], [10, 539, 4, 31], [10, 540, 4, 31, "e"], [10, 541, 4, 31], [10, 543, 4, 31, "_t"], [10, 545, 4, 31], [10, 552, 4, 31, "i"], [10, 553, 4, 31], [10, 554, 4, 31, "get"], [10, 557, 4, 31], [10, 561, 4, 31, "i"], [10, 562, 4, 31], [10, 563, 4, 31, "set"], [10, 566, 4, 31], [10, 570, 4, 31, "o"], [10, 571, 4, 31], [10, 572, 4, 31, "f"], [10, 573, 4, 31], [10, 575, 4, 31, "_t"], [10, 577, 4, 31], [10, 579, 4, 31, "i"], [10, 580, 4, 31], [10, 584, 4, 31, "f"], [10, 585, 4, 31], [10, 586, 4, 31, "_t"], [10, 588, 4, 31], [10, 592, 4, 31, "e"], [10, 593, 4, 31], [10, 594, 4, 31, "_t"], [10, 596, 4, 31], [10, 607, 4, 31, "f"], [10, 608, 4, 31], [10, 613, 4, 31, "e"], [10, 614, 4, 31], [10, 616, 4, 31, "t"], [10, 617, 4, 31], [11, 2, 5, 7], [11, 11, 5, 16, "useInvalidPreventRemoveError"], [11, 39, 5, 44, "useInvalidPreventRemoveError"], [11, 40, 5, 45, "descriptors"], [11, 51, 5, 56], [11, 53, 5, 58], [12, 4, 6, 2], [12, 8, 6, 2, "_usePreventRemoveCont"], [12, 29, 6, 2], [12, 32, 8, 6], [12, 36, 8, 6, "usePreventRemoveContext"], [12, 67, 8, 29], [12, 69, 8, 30], [12, 70, 8, 31], [13, 6, 7, 4, "preventedRoutes"], [13, 21, 7, 19], [13, 24, 7, 19, "_usePreventRemoveCont"], [13, 45, 7, 19], [13, 46, 7, 4, "preventedRoutes"], [13, 61, 7, 19], [14, 4, 9, 2], [14, 8, 9, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 25, 9, 25], [14, 28, 9, 28, "Object"], [14, 34, 9, 34], [14, 35, 9, 35, "keys"], [14, 39, 9, 39], [14, 40, 9, 40, "preventedRoutes"], [14, 55, 9, 55], [14, 56, 9, 56], [14, 57, 9, 57], [14, 58, 9, 58], [14, 59, 9, 59], [15, 4, 10, 2], [15, 8, 10, 8, "preventedDescriptor"], [15, 27, 10, 27], [15, 30, 10, 30, "descriptors"], [15, 41, 10, 41], [15, 42, 10, 42, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 59, 10, 59], [15, 60, 10, 60], [16, 4, 11, 2], [16, 8, 11, 8, "isHeaderBackButtonMenuEnabledOnPreventedScreen"], [16, 54, 11, 54], [16, 57, 11, 57, "preventedDescriptor"], [16, 76, 11, 76], [16, 78, 11, 78, "options"], [16, 85, 11, 85], [16, 87, 11, 87, "headerBackButtonMenuEnabled"], [16, 114, 11, 114], [17, 4, 12, 2], [17, 8, 12, 8, "preventedRouteName"], [17, 26, 12, 26], [17, 29, 12, 29, "preventedDescriptor"], [17, 48, 12, 48], [17, 50, 12, 50, "route"], [17, 55, 12, 55], [17, 57, 12, 57, "name"], [17, 61, 12, 61], [18, 4, 13, 2, "React"], [18, 9, 13, 7], [18, 10, 13, 8, "useEffect"], [18, 19, 13, 17], [18, 20, 13, 18], [18, 26, 13, 24], [19, 6, 14, 4], [19, 10, 14, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 27, 14, 25], [19, 31, 14, 29], [19, 35, 14, 33], [19, 39, 14, 37, "isHeaderBackButtonMenuEnabledOnPreventedScreen"], [19, 85, 14, 83], [19, 87, 14, 85], [20, 8, 15, 6], [20, 12, 15, 12, "message"], [20, 19, 15, 19], [20, 22, 15, 22], [20, 36, 15, 36, "preventedRouteName"], [20, 54, 15, 54], [20, 161, 15, 161], [20, 164, 15, 164], [20, 226, 15, 226, "preventedRouteName"], [20, 244, 15, 244], [20, 279, 15, 279], [21, 8, 16, 6, "console"], [21, 15, 16, 13], [21, 16, 16, 14, "error"], [21, 21, 16, 19], [21, 22, 16, 20, "message"], [21, 29, 16, 27], [21, 30, 16, 28], [22, 6, 17, 4], [23, 4, 18, 2], [23, 5, 18, 3], [23, 7, 18, 5], [23, 8, 18, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 25, 18, 23], [23, 27, 18, 25, "isHeaderBackButtonMenuEnabledOnPreventedScreen"], [23, 73, 18, 71], [23, 75, 18, 73, "preventedRouteName"], [23, 93, 18, 91], [23, 94, 18, 92], [23, 95, 18, 93], [24, 2, 19, 0], [25, 0, 19, 1], [25, 3]], "functionMap": {"names": ["<global>", "useInvalidPreventRemoveError", "React.useEffect$argument_0"], "mappings": "AAA;OCI;kBCQ;GDK;CDC"}}, "type": "js/module"}]}