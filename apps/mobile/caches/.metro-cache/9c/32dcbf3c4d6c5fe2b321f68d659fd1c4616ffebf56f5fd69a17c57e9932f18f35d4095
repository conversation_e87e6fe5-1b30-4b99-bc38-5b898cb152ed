{"dependencies": [{"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 20}, "end": {"line": 2, "column": 54, "index": 74}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Removable = void 0;\n  var _utils = require(_dependencyMap[0], \"./utils.js\");\n  // src/removable.ts\n\n  var Removable = class {\n    #gcTimeout;\n    destroy() {\n      this.clearGcTimeout();\n    }\n    scheduleGc() {\n      this.clearGcTimeout();\n      if ((0, _utils.isValidTimeout)(this.gcTime)) {\n        this.#gcTimeout = setTimeout(() => {\n          this.optionalRemove();\n        }, this.gcTime);\n      }\n    }\n    updateGcTime(newGcTime) {\n      this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (_utils.isServer ? Infinity : 5 * 60 * 1e3));\n    }\n    clearGcTimeout() {\n      if (this.#gcTimeout) {\n        clearTimeout(this.#gcTimeout);\n        this.#gcTimeout = void 0;\n      }\n    }\n  };\n  exports.Removable = Removable;\n});", "lineCount": 33, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_utils"], [6, 12, 2, 0], [6, 15, 2, 0, "require"], [6, 22, 2, 0], [6, 23, 2, 0, "_dependencyMap"], [6, 37, 2, 0], [7, 2, 1, 0], [9, 2, 3, 0], [9, 6, 3, 4, "Removable"], [9, 15, 3, 13], [9, 18, 3, 16], [9, 24, 3, 22], [10, 4, 4, 2], [10, 5, 4, 3, "gcTimeout"], [10, 14, 4, 12], [11, 4, 5, 2, "destroy"], [11, 11, 5, 9, "destroy"], [11, 12, 5, 9], [11, 14, 5, 12], [12, 6, 6, 4], [12, 10, 6, 8], [12, 11, 6, 9, "clearGcTimeout"], [12, 25, 6, 23], [12, 26, 6, 24], [12, 27, 6, 25], [13, 4, 7, 2], [14, 4, 8, 2, "scheduleGc"], [14, 14, 8, 12, "scheduleGc"], [14, 15, 8, 12], [14, 17, 8, 15], [15, 6, 9, 4], [15, 10, 9, 8], [15, 11, 9, 9, "clearGcTimeout"], [15, 25, 9, 23], [15, 26, 9, 24], [15, 27, 9, 25], [16, 6, 10, 4], [16, 10, 10, 8], [16, 14, 10, 8, "isValidTimeout"], [16, 35, 10, 22], [16, 37, 10, 23], [16, 41, 10, 27], [16, 42, 10, 28, "gcTime"], [16, 48, 10, 34], [16, 49, 10, 35], [16, 51, 10, 37], [17, 8, 11, 6], [17, 12, 11, 10], [17, 13, 11, 11], [17, 14, 11, 12, "gcTimeout"], [17, 23, 11, 21], [17, 26, 11, 24, "setTimeout"], [17, 36, 11, 34], [17, 37, 11, 35], [17, 43, 11, 41], [18, 10, 12, 8], [18, 14, 12, 12], [18, 15, 12, 13, "optionalRemove"], [18, 29, 12, 27], [18, 30, 12, 28], [18, 31, 12, 29], [19, 8, 13, 6], [19, 9, 13, 7], [19, 11, 13, 9], [19, 15, 13, 13], [19, 16, 13, 14, "gcTime"], [19, 22, 13, 20], [19, 23, 13, 21], [20, 6, 14, 4], [21, 4, 15, 2], [22, 4, 16, 2, "updateGcTime"], [22, 16, 16, 14, "updateGcTime"], [22, 17, 16, 15, "newGcTime"], [22, 26, 16, 24], [22, 28, 16, 26], [23, 6, 17, 4], [23, 10, 17, 8], [23, 11, 17, 9, "gcTime"], [23, 17, 17, 15], [23, 20, 17, 18, "Math"], [23, 24, 17, 22], [23, 25, 17, 23, "max"], [23, 28, 17, 26], [23, 29, 18, 6], [23, 33, 18, 10], [23, 34, 18, 11, "gcTime"], [23, 40, 18, 17], [23, 44, 18, 21], [23, 45, 18, 22], [23, 47, 19, 6, "newGcTime"], [23, 56, 19, 15], [23, 61, 19, 20, "isServer"], [23, 76, 19, 28], [23, 79, 19, 31, "Infinity"], [23, 87, 19, 39], [23, 90, 19, 42], [23, 91, 19, 43], [23, 94, 19, 46], [23, 96, 19, 48], [23, 99, 19, 51], [23, 102, 19, 54], [23, 103, 20, 4], [23, 104, 20, 5], [24, 4, 21, 2], [25, 4, 22, 2, "clearGcTimeout"], [25, 18, 22, 16, "clearGcTimeout"], [25, 19, 22, 16], [25, 21, 22, 19], [26, 6, 23, 4], [26, 10, 23, 8], [26, 14, 23, 12], [26, 15, 23, 13], [26, 16, 23, 14, "gcTimeout"], [26, 25, 23, 23], [26, 27, 23, 25], [27, 8, 24, 6, "clearTimeout"], [27, 20, 24, 18], [27, 21, 24, 19], [27, 25, 24, 23], [27, 26, 24, 24], [27, 27, 24, 25, "gcTimeout"], [27, 36, 24, 34], [27, 37, 24, 35], [28, 8, 25, 6], [28, 12, 25, 10], [28, 13, 25, 11], [28, 14, 25, 12, "gcTimeout"], [28, 23, 25, 21], [28, 26, 25, 24], [28, 31, 25, 29], [28, 32, 25, 30], [29, 6, 26, 4], [30, 4, 27, 2], [31, 2, 28, 0], [31, 3, 28, 1], [32, 2, 28, 2, "exports"], [32, 9, 28, 2], [32, 10, 28, 2, "Removable"], [32, 19, 28, 2], [32, 22, 28, 2, "Removable"], [32, 31, 28, 2], [33, 0, 28, 2], [33, 3]], "functionMap": {"names": ["<global>", "Removable", "Removable#destroy", "Removable#scheduleGc", "setTimeout$argument_0", "Removable#updateGcTime", "Removable#clearGcTimeout"], "mappings": "AAA;gBCE;ECE;GDE;EEC;mCCG;ODE;GFE;EIC;GJK;EKC;GLK;CDC"}}, "type": "js/module"}]}