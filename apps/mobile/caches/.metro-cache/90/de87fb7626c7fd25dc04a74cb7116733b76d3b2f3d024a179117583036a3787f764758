{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.numberAsInset = exports.isTouchWithinInset = exports.gestureTouchToPressableEvent = exports.gestureToPressableEvent = exports.addInsets = void 0;\n  const numberAsInset = value => ({\n    left: value,\n    right: value,\n    top: value,\n    bottom: value\n  });\n  exports.numberAsInset = numberAsInset;\n  const addInsets = (a, b) => {\n    var _a$left, _b$left, _a$right, _b$right, _a$top, _b$top, _a$bottom, _b$bottom;\n    return {\n      left: ((_a$left = a.left) !== null && _a$left !== void 0 ? _a$left : 0) + ((_b$left = b.left) !== null && _b$left !== void 0 ? _b$left : 0),\n      right: ((_a$right = a.right) !== null && _a$right !== void 0 ? _a$right : 0) + ((_b$right = b.right) !== null && _b$right !== void 0 ? _b$right : 0),\n      top: ((_a$top = a.top) !== null && _a$top !== void 0 ? _a$top : 0) + ((_b$top = b.top) !== null && _b$top !== void 0 ? _b$top : 0),\n      bottom: ((_a$bottom = a.bottom) !== null && _a$bottom !== void 0 ? _a$bottom : 0) + ((_b$bottom = b.bottom) !== null && _b$bottom !== void 0 ? _b$bottom : 0)\n    };\n  };\n  exports.addInsets = addInsets;\n  const touchDataToPressEvent = (data, timestamp, targetId) => ({\n    identifier: data.id,\n    locationX: data.x,\n    locationY: data.y,\n    pageX: data.absoluteX,\n    pageY: data.absoluteY,\n    target: targetId,\n    timestamp: timestamp,\n    touches: [],\n    // Always empty - legacy compatibility\n    changedTouches: [] // Always empty - legacy compatibility\n  });\n  const gestureToPressEvent = (event, timestamp, targetId) => ({\n    identifier: event.handlerTag,\n    locationX: event.x,\n    locationY: event.y,\n    pageX: event.absoluteX,\n    pageY: event.absoluteY,\n    target: targetId,\n    timestamp: timestamp,\n    touches: [],\n    // Always empty - legacy compatibility\n    changedTouches: [] // Always empty - legacy compatibility\n  });\n  const isTouchWithinInset = (dimensions, inset, touch) => {\n    var _touch$x, _inset$right, _touch$y, _inset$bottom, _touch$x2, _inset$left, _touch$y2, _inset$top;\n    return ((_touch$x = touch === null || touch === void 0 ? void 0 : touch.x) !== null && _touch$x !== void 0 ? _touch$x : 0) < ((_inset$right = inset.right) !== null && _inset$right !== void 0 ? _inset$right : 0) + dimensions.width && ((_touch$y = touch === null || touch === void 0 ? void 0 : touch.y) !== null && _touch$y !== void 0 ? _touch$y : 0) < ((_inset$bottom = inset.bottom) !== null && _inset$bottom !== void 0 ? _inset$bottom : 0) + dimensions.height && ((_touch$x2 = touch === null || touch === void 0 ? void 0 : touch.x) !== null && _touch$x2 !== void 0 ? _touch$x2 : 0) > -((_inset$left = inset.left) !== null && _inset$left !== void 0 ? _inset$left : 0) && ((_touch$y2 = touch === null || touch === void 0 ? void 0 : touch.y) !== null && _touch$y2 !== void 0 ? _touch$y2 : 0) > -((_inset$top = inset.top) !== null && _inset$top !== void 0 ? _inset$top : 0);\n  };\n  exports.isTouchWithinInset = isTouchWithinInset;\n  const gestureToPressableEvent = event => {\n    const timestamp = Date.now(); // As far as I can see, there isn't a conventional way of getting targetId with the data we get\n\n    const targetId = 0;\n    const pressEvent = gestureToPressEvent(event, timestamp, targetId);\n    return {\n      nativeEvent: {\n        touches: [pressEvent],\n        changedTouches: [pressEvent],\n        identifier: pressEvent.identifier,\n        locationX: event.x,\n        locationY: event.y,\n        pageX: event.absoluteX,\n        pageY: event.absoluteY,\n        target: targetId,\n        timestamp: timestamp,\n        force: undefined\n      }\n    };\n  };\n  exports.gestureToPressableEvent = gestureToPressableEvent;\n  const gestureTouchToPressableEvent = event => {\n    var _event$allTouches$at$, _event$allTouches$at, _event$allTouches$at$2, _event$allTouches$at2, _event$allTouches$at$3, _event$allTouches$at3, _event$allTouches$at$4, _event$allTouches$at4;\n    const timestamp = Date.now(); // As far as I can see, there isn't a conventional way of getting targetId with the data we get\n\n    const targetId = 0;\n    const touchesList = event.allTouches.map(touch => touchDataToPressEvent(touch, timestamp, targetId));\n    const changedTouchesList = event.changedTouches.map(touch => touchDataToPressEvent(touch, timestamp, targetId));\n    return {\n      nativeEvent: {\n        touches: touchesList,\n        changedTouches: changedTouchesList,\n        identifier: event.handlerTag,\n        locationX: (_event$allTouches$at$ = (_event$allTouches$at = event.allTouches.at(0)) === null || _event$allTouches$at === void 0 ? void 0 : _event$allTouches$at.x) !== null && _event$allTouches$at$ !== void 0 ? _event$allTouches$at$ : -1,\n        locationY: (_event$allTouches$at$2 = (_event$allTouches$at2 = event.allTouches.at(0)) === null || _event$allTouches$at2 === void 0 ? void 0 : _event$allTouches$at2.y) !== null && _event$allTouches$at$2 !== void 0 ? _event$allTouches$at$2 : -1,\n        pageX: (_event$allTouches$at$3 = (_event$allTouches$at3 = event.allTouches.at(0)) === null || _event$allTouches$at3 === void 0 ? void 0 : _event$allTouches$at3.absoluteX) !== null && _event$allTouches$at$3 !== void 0 ? _event$allTouches$at$3 : -1,\n        pageY: (_event$allTouches$at$4 = (_event$allTouches$at4 = event.allTouches.at(0)) === null || _event$allTouches$at4 === void 0 ? void 0 : _event$allTouches$at4.absoluteY) !== null && _event$allTouches$at$4 !== void 0 ? _event$allTouches$at$4 : -1,\n        target: targetId,\n        timestamp: timestamp,\n        force: undefined\n      }\n    };\n  };\n  exports.gestureTouchToPressableEvent = gestureTouchToPressableEvent;\n});", "lineCount": 96, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "numberAsInset"], [6, 21, 1, 19], [6, 24, 1, 22, "value"], [6, 29, 1, 27], [6, 34, 1, 32], [7, 4, 2, 2, "left"], [7, 8, 2, 6], [7, 10, 2, 8, "value"], [7, 15, 2, 13], [8, 4, 3, 2, "right"], [8, 9, 3, 7], [8, 11, 3, 9, "value"], [8, 16, 3, 14], [9, 4, 4, 2, "top"], [9, 7, 4, 5], [9, 9, 4, 7, "value"], [9, 14, 4, 12], [10, 4, 5, 2, "bottom"], [10, 10, 5, 8], [10, 12, 5, 10, "value"], [11, 2, 6, 0], [11, 3, 6, 1], [11, 4, 6, 2], [12, 2, 6, 3, "exports"], [12, 9, 6, 3], [12, 10, 6, 3, "numberAsInset"], [12, 23, 6, 3], [12, 26, 6, 3, "numberAsInset"], [12, 39, 6, 3], [13, 2, 8, 0], [13, 8, 8, 6, "addInsets"], [13, 17, 8, 15], [13, 20, 8, 18, "addInsets"], [13, 21, 8, 19, "a"], [13, 22, 8, 20], [13, 24, 8, 22, "b"], [13, 25, 8, 23], [13, 30, 8, 28], [14, 4, 9, 2], [14, 8, 9, 6, "_a$left"], [14, 15, 9, 13], [14, 17, 9, 15, "_b$left"], [14, 24, 9, 22], [14, 26, 9, 24, "_a$right"], [14, 34, 9, 32], [14, 36, 9, 34, "_b$right"], [14, 44, 9, 42], [14, 46, 9, 44, "_a$top"], [14, 52, 9, 50], [14, 54, 9, 52, "_b$top"], [14, 60, 9, 58], [14, 62, 9, 60, "_a$bottom"], [14, 71, 9, 69], [14, 73, 9, 71, "_b$bottom"], [14, 82, 9, 80], [15, 4, 11, 2], [15, 11, 11, 9], [16, 6, 12, 4, "left"], [16, 10, 12, 8], [16, 12, 12, 10], [16, 13, 12, 11], [16, 14, 12, 12, "_a$left"], [16, 21, 12, 19], [16, 24, 12, 22, "a"], [16, 25, 12, 23], [16, 26, 12, 24, "left"], [16, 30, 12, 28], [16, 36, 12, 34], [16, 40, 12, 38], [16, 44, 12, 42, "_a$left"], [16, 51, 12, 49], [16, 56, 12, 54], [16, 61, 12, 59], [16, 62, 12, 60], [16, 65, 12, 63, "_a$left"], [16, 72, 12, 70], [16, 75, 12, 73], [16, 76, 12, 74], [16, 81, 12, 79], [16, 82, 12, 80, "_b$left"], [16, 89, 12, 87], [16, 92, 12, 90, "b"], [16, 93, 12, 91], [16, 94, 12, 92, "left"], [16, 98, 12, 96], [16, 104, 12, 102], [16, 108, 12, 106], [16, 112, 12, 110, "_b$left"], [16, 119, 12, 117], [16, 124, 12, 122], [16, 129, 12, 127], [16, 130, 12, 128], [16, 133, 12, 131, "_b$left"], [16, 140, 12, 138], [16, 143, 12, 141], [16, 144, 12, 142], [16, 145, 12, 143], [17, 6, 13, 4, "right"], [17, 11, 13, 9], [17, 13, 13, 11], [17, 14, 13, 12], [17, 15, 13, 13, "_a$right"], [17, 23, 13, 21], [17, 26, 13, 24, "a"], [17, 27, 13, 25], [17, 28, 13, 26, "right"], [17, 33, 13, 31], [17, 39, 13, 37], [17, 43, 13, 41], [17, 47, 13, 45, "_a$right"], [17, 55, 13, 53], [17, 60, 13, 58], [17, 65, 13, 63], [17, 66, 13, 64], [17, 69, 13, 67, "_a$right"], [17, 77, 13, 75], [17, 80, 13, 78], [17, 81, 13, 79], [17, 86, 13, 84], [17, 87, 13, 85, "_b$right"], [17, 95, 13, 93], [17, 98, 13, 96, "b"], [17, 99, 13, 97], [17, 100, 13, 98, "right"], [17, 105, 13, 103], [17, 111, 13, 109], [17, 115, 13, 113], [17, 119, 13, 117, "_b$right"], [17, 127, 13, 125], [17, 132, 13, 130], [17, 137, 13, 135], [17, 138, 13, 136], [17, 141, 13, 139, "_b$right"], [17, 149, 13, 147], [17, 152, 13, 150], [17, 153, 13, 151], [17, 154, 13, 152], [18, 6, 14, 4, "top"], [18, 9, 14, 7], [18, 11, 14, 9], [18, 12, 14, 10], [18, 13, 14, 11, "_a$top"], [18, 19, 14, 17], [18, 22, 14, 20, "a"], [18, 23, 14, 21], [18, 24, 14, 22, "top"], [18, 27, 14, 25], [18, 33, 14, 31], [18, 37, 14, 35], [18, 41, 14, 39, "_a$top"], [18, 47, 14, 45], [18, 52, 14, 50], [18, 57, 14, 55], [18, 58, 14, 56], [18, 61, 14, 59, "_a$top"], [18, 67, 14, 65], [18, 70, 14, 68], [18, 71, 14, 69], [18, 76, 14, 74], [18, 77, 14, 75, "_b$top"], [18, 83, 14, 81], [18, 86, 14, 84, "b"], [18, 87, 14, 85], [18, 88, 14, 86, "top"], [18, 91, 14, 89], [18, 97, 14, 95], [18, 101, 14, 99], [18, 105, 14, 103, "_b$top"], [18, 111, 14, 109], [18, 116, 14, 114], [18, 121, 14, 119], [18, 122, 14, 120], [18, 125, 14, 123, "_b$top"], [18, 131, 14, 129], [18, 134, 14, 132], [18, 135, 14, 133], [18, 136, 14, 134], [19, 6, 15, 4, "bottom"], [19, 12, 15, 10], [19, 14, 15, 12], [19, 15, 15, 13], [19, 16, 15, 14, "_a$bottom"], [19, 25, 15, 23], [19, 28, 15, 26, "a"], [19, 29, 15, 27], [19, 30, 15, 28, "bottom"], [19, 36, 15, 34], [19, 42, 15, 40], [19, 46, 15, 44], [19, 50, 15, 48, "_a$bottom"], [19, 59, 15, 57], [19, 64, 15, 62], [19, 69, 15, 67], [19, 70, 15, 68], [19, 73, 15, 71, "_a$bottom"], [19, 82, 15, 80], [19, 85, 15, 83], [19, 86, 15, 84], [19, 91, 15, 89], [19, 92, 15, 90, "_b$bottom"], [19, 101, 15, 99], [19, 104, 15, 102, "b"], [19, 105, 15, 103], [19, 106, 15, 104, "bottom"], [19, 112, 15, 110], [19, 118, 15, 116], [19, 122, 15, 120], [19, 126, 15, 124, "_b$bottom"], [19, 135, 15, 133], [19, 140, 15, 138], [19, 145, 15, 143], [19, 146, 15, 144], [19, 149, 15, 147, "_b$bottom"], [19, 158, 15, 156], [19, 161, 15, 159], [19, 162, 15, 160], [20, 4, 16, 2], [20, 5, 16, 3], [21, 2, 17, 0], [21, 3, 17, 1], [22, 2, 17, 2, "exports"], [22, 9, 17, 2], [22, 10, 17, 2, "addInsets"], [22, 19, 17, 2], [22, 22, 17, 2, "addInsets"], [22, 31, 17, 2], [23, 2, 19, 0], [23, 8, 19, 6, "touchDataToPressEvent"], [23, 29, 19, 27], [23, 32, 19, 30, "touchDataToPressEvent"], [23, 33, 19, 31, "data"], [23, 37, 19, 35], [23, 39, 19, 37, "timestamp"], [23, 48, 19, 46], [23, 50, 19, 48, "targetId"], [23, 58, 19, 56], [23, 64, 19, 62], [24, 4, 20, 2, "identifier"], [24, 14, 20, 12], [24, 16, 20, 14, "data"], [24, 20, 20, 18], [24, 21, 20, 19, "id"], [24, 23, 20, 21], [25, 4, 21, 2, "locationX"], [25, 13, 21, 11], [25, 15, 21, 13, "data"], [25, 19, 21, 17], [25, 20, 21, 18, "x"], [25, 21, 21, 19], [26, 4, 22, 2, "locationY"], [26, 13, 22, 11], [26, 15, 22, 13, "data"], [26, 19, 22, 17], [26, 20, 22, 18, "y"], [26, 21, 22, 19], [27, 4, 23, 2, "pageX"], [27, 9, 23, 7], [27, 11, 23, 9, "data"], [27, 15, 23, 13], [27, 16, 23, 14, "absoluteX"], [27, 25, 23, 23], [28, 4, 24, 2, "pageY"], [28, 9, 24, 7], [28, 11, 24, 9, "data"], [28, 15, 24, 13], [28, 16, 24, 14, "absoluteY"], [28, 25, 24, 23], [29, 4, 25, 2, "target"], [29, 10, 25, 8], [29, 12, 25, 10, "targetId"], [29, 20, 25, 18], [30, 4, 26, 2, "timestamp"], [30, 13, 26, 11], [30, 15, 26, 13, "timestamp"], [30, 24, 26, 22], [31, 4, 27, 2, "touches"], [31, 11, 27, 9], [31, 13, 27, 11], [31, 15, 27, 13], [32, 4, 28, 2], [33, 4, 29, 2, "changedTouches"], [33, 18, 29, 16], [33, 20, 29, 18], [33, 22, 29, 20], [33, 23, 29, 21], [34, 2, 31, 0], [34, 3, 31, 1], [34, 4, 31, 2], [35, 2, 33, 0], [35, 8, 33, 6, "gestureToPressEvent"], [35, 27, 33, 25], [35, 30, 33, 28, "gestureToPressEvent"], [35, 31, 33, 29, "event"], [35, 36, 33, 34], [35, 38, 33, 36, "timestamp"], [35, 47, 33, 45], [35, 49, 33, 47, "targetId"], [35, 57, 33, 55], [35, 63, 33, 61], [36, 4, 34, 2, "identifier"], [36, 14, 34, 12], [36, 16, 34, 14, "event"], [36, 21, 34, 19], [36, 22, 34, 20, "handlerTag"], [36, 32, 34, 30], [37, 4, 35, 2, "locationX"], [37, 13, 35, 11], [37, 15, 35, 13, "event"], [37, 20, 35, 18], [37, 21, 35, 19, "x"], [37, 22, 35, 20], [38, 4, 36, 2, "locationY"], [38, 13, 36, 11], [38, 15, 36, 13, "event"], [38, 20, 36, 18], [38, 21, 36, 19, "y"], [38, 22, 36, 20], [39, 4, 37, 2, "pageX"], [39, 9, 37, 7], [39, 11, 37, 9, "event"], [39, 16, 37, 14], [39, 17, 37, 15, "absoluteX"], [39, 26, 37, 24], [40, 4, 38, 2, "pageY"], [40, 9, 38, 7], [40, 11, 38, 9, "event"], [40, 16, 38, 14], [40, 17, 38, 15, "absoluteY"], [40, 26, 38, 24], [41, 4, 39, 2, "target"], [41, 10, 39, 8], [41, 12, 39, 10, "targetId"], [41, 20, 39, 18], [42, 4, 40, 2, "timestamp"], [42, 13, 40, 11], [42, 15, 40, 13, "timestamp"], [42, 24, 40, 22], [43, 4, 41, 2, "touches"], [43, 11, 41, 9], [43, 13, 41, 11], [43, 15, 41, 13], [44, 4, 42, 2], [45, 4, 43, 2, "changedTouches"], [45, 18, 43, 16], [45, 20, 43, 18], [45, 22, 43, 20], [45, 23, 43, 21], [46, 2, 45, 0], [46, 3, 45, 1], [46, 4, 45, 2], [47, 2, 47, 0], [47, 8, 47, 6, "isTouchWithinInset"], [47, 26, 47, 24], [47, 29, 47, 27, "isTouchWithinInset"], [47, 30, 47, 28, "dimensions"], [47, 40, 47, 38], [47, 42, 47, 40, "inset"], [47, 47, 47, 45], [47, 49, 47, 47, "touch"], [47, 54, 47, 52], [47, 59, 47, 57], [48, 4, 48, 2], [48, 8, 48, 6, "_touch$x"], [48, 16, 48, 14], [48, 18, 48, 16, "_inset$right"], [48, 30, 48, 28], [48, 32, 48, 30, "_touch$y"], [48, 40, 48, 38], [48, 42, 48, 40, "_inset$bottom"], [48, 55, 48, 53], [48, 57, 48, 55, "_touch$x2"], [48, 66, 48, 64], [48, 68, 48, 66, "_inset$left"], [48, 79, 48, 77], [48, 81, 48, 79, "_touch$y2"], [48, 90, 48, 88], [48, 92, 48, 90, "_inset$top"], [48, 102, 48, 100], [49, 4, 50, 2], [49, 11, 50, 9], [49, 12, 50, 10], [49, 13, 50, 11, "_touch$x"], [49, 21, 50, 19], [49, 24, 50, 22, "touch"], [49, 29, 50, 27], [49, 34, 50, 32], [49, 38, 50, 36], [49, 42, 50, 40, "touch"], [49, 47, 50, 45], [49, 52, 50, 50], [49, 57, 50, 55], [49, 58, 50, 56], [49, 61, 50, 59], [49, 66, 50, 64], [49, 67, 50, 65], [49, 70, 50, 68, "touch"], [49, 75, 50, 73], [49, 76, 50, 74, "x"], [49, 77, 50, 75], [49, 83, 50, 81], [49, 87, 50, 85], [49, 91, 50, 89, "_touch$x"], [49, 99, 50, 97], [49, 104, 50, 102], [49, 109, 50, 107], [49, 110, 50, 108], [49, 113, 50, 111, "_touch$x"], [49, 121, 50, 119], [49, 124, 50, 122], [49, 125, 50, 123], [49, 129, 50, 127], [49, 130, 50, 128], [49, 131, 50, 129, "_inset$right"], [49, 143, 50, 141], [49, 146, 50, 144, "inset"], [49, 151, 50, 149], [49, 152, 50, 150, "right"], [49, 157, 50, 155], [49, 163, 50, 161], [49, 167, 50, 165], [49, 171, 50, 169, "_inset$right"], [49, 183, 50, 181], [49, 188, 50, 186], [49, 193, 50, 191], [49, 194, 50, 192], [49, 197, 50, 195, "_inset$right"], [49, 209, 50, 207], [49, 212, 50, 210], [49, 213, 50, 211], [49, 217, 50, 215, "dimensions"], [49, 227, 50, 225], [49, 228, 50, 226, "width"], [49, 233, 50, 231], [49, 237, 50, 235], [49, 238, 50, 236], [49, 239, 50, 237, "_touch$y"], [49, 247, 50, 245], [49, 250, 50, 248, "touch"], [49, 255, 50, 253], [49, 260, 50, 258], [49, 264, 50, 262], [49, 268, 50, 266, "touch"], [49, 273, 50, 271], [49, 278, 50, 276], [49, 283, 50, 281], [49, 284, 50, 282], [49, 287, 50, 285], [49, 292, 50, 290], [49, 293, 50, 291], [49, 296, 50, 294, "touch"], [49, 301, 50, 299], [49, 302, 50, 300, "y"], [49, 303, 50, 301], [49, 309, 50, 307], [49, 313, 50, 311], [49, 317, 50, 315, "_touch$y"], [49, 325, 50, 323], [49, 330, 50, 328], [49, 335, 50, 333], [49, 336, 50, 334], [49, 339, 50, 337, "_touch$y"], [49, 347, 50, 345], [49, 350, 50, 348], [49, 351, 50, 349], [49, 355, 50, 353], [49, 356, 50, 354], [49, 357, 50, 355, "_inset$bottom"], [49, 370, 50, 368], [49, 373, 50, 371, "inset"], [49, 378, 50, 376], [49, 379, 50, 377, "bottom"], [49, 385, 50, 383], [49, 391, 50, 389], [49, 395, 50, 393], [49, 399, 50, 397, "_inset$bottom"], [49, 412, 50, 410], [49, 417, 50, 415], [49, 422, 50, 420], [49, 423, 50, 421], [49, 426, 50, 424, "_inset$bottom"], [49, 439, 50, 437], [49, 442, 50, 440], [49, 443, 50, 441], [49, 447, 50, 445, "dimensions"], [49, 457, 50, 455], [49, 458, 50, 456, "height"], [49, 464, 50, 462], [49, 468, 50, 466], [49, 469, 50, 467], [49, 470, 50, 468, "_touch$x2"], [49, 479, 50, 477], [49, 482, 50, 480, "touch"], [49, 487, 50, 485], [49, 492, 50, 490], [49, 496, 50, 494], [49, 500, 50, 498, "touch"], [49, 505, 50, 503], [49, 510, 50, 508], [49, 515, 50, 513], [49, 516, 50, 514], [49, 519, 50, 517], [49, 524, 50, 522], [49, 525, 50, 523], [49, 528, 50, 526, "touch"], [49, 533, 50, 531], [49, 534, 50, 532, "x"], [49, 535, 50, 533], [49, 541, 50, 539], [49, 545, 50, 543], [49, 549, 50, 547, "_touch$x2"], [49, 558, 50, 556], [49, 563, 50, 561], [49, 568, 50, 566], [49, 569, 50, 567], [49, 572, 50, 570, "_touch$x2"], [49, 581, 50, 579], [49, 584, 50, 582], [49, 585, 50, 583], [49, 589, 50, 587], [49, 591, 50, 589], [49, 592, 50, 590, "_inset$left"], [49, 603, 50, 601], [49, 606, 50, 604, "inset"], [49, 611, 50, 609], [49, 612, 50, 610, "left"], [49, 616, 50, 614], [49, 622, 50, 620], [49, 626, 50, 624], [49, 630, 50, 628, "_inset$left"], [49, 641, 50, 639], [49, 646, 50, 644], [49, 651, 50, 649], [49, 652, 50, 650], [49, 655, 50, 653, "_inset$left"], [49, 666, 50, 664], [49, 669, 50, 667], [49, 670, 50, 668], [49, 671, 50, 669], [49, 675, 50, 673], [49, 676, 50, 674], [49, 677, 50, 675, "_touch$y2"], [49, 686, 50, 684], [49, 689, 50, 687, "touch"], [49, 694, 50, 692], [49, 699, 50, 697], [49, 703, 50, 701], [49, 707, 50, 705, "touch"], [49, 712, 50, 710], [49, 717, 50, 715], [49, 722, 50, 720], [49, 723, 50, 721], [49, 726, 50, 724], [49, 731, 50, 729], [49, 732, 50, 730], [49, 735, 50, 733, "touch"], [49, 740, 50, 738], [49, 741, 50, 739, "y"], [49, 742, 50, 740], [49, 748, 50, 746], [49, 752, 50, 750], [49, 756, 50, 754, "_touch$y2"], [49, 765, 50, 763], [49, 770, 50, 768], [49, 775, 50, 773], [49, 776, 50, 774], [49, 779, 50, 777, "_touch$y2"], [49, 788, 50, 786], [49, 791, 50, 789], [49, 792, 50, 790], [49, 796, 50, 794], [49, 798, 50, 796], [49, 799, 50, 797, "_inset$top"], [49, 809, 50, 807], [49, 812, 50, 810, "inset"], [49, 817, 50, 815], [49, 818, 50, 816, "top"], [49, 821, 50, 819], [49, 827, 50, 825], [49, 831, 50, 829], [49, 835, 50, 833, "_inset$top"], [49, 845, 50, 843], [49, 850, 50, 848], [49, 855, 50, 853], [49, 856, 50, 854], [49, 859, 50, 857, "_inset$top"], [49, 869, 50, 867], [49, 872, 50, 870], [49, 873, 50, 871], [49, 874, 50, 872], [50, 2, 51, 0], [50, 3, 51, 1], [51, 2, 51, 2, "exports"], [51, 9, 51, 2], [51, 10, 51, 2, "isTouchWithinInset"], [51, 28, 51, 2], [51, 31, 51, 2, "isTouchWithinInset"], [51, 49, 51, 2], [52, 2, 53, 0], [52, 8, 53, 6, "gestureToPressableEvent"], [52, 31, 53, 29], [52, 34, 53, 32, "event"], [52, 39, 53, 37], [52, 43, 53, 41], [53, 4, 54, 2], [53, 10, 54, 8, "timestamp"], [53, 19, 54, 17], [53, 22, 54, 20, "Date"], [53, 26, 54, 24], [53, 27, 54, 25, "now"], [53, 30, 54, 28], [53, 31, 54, 29], [53, 32, 54, 30], [53, 33, 54, 31], [53, 34, 54, 32], [55, 4, 56, 2], [55, 10, 56, 8, "targetId"], [55, 18, 56, 16], [55, 21, 56, 19], [55, 22, 56, 20], [56, 4, 57, 2], [56, 10, 57, 8, "pressEvent"], [56, 20, 57, 18], [56, 23, 57, 21, "gestureToPressEvent"], [56, 42, 57, 40], [56, 43, 57, 41, "event"], [56, 48, 57, 46], [56, 50, 57, 48, "timestamp"], [56, 59, 57, 57], [56, 61, 57, 59, "targetId"], [56, 69, 57, 67], [56, 70, 57, 68], [57, 4, 58, 2], [57, 11, 58, 9], [58, 6, 59, 4, "nativeEvent"], [58, 17, 59, 15], [58, 19, 59, 17], [59, 8, 60, 6, "touches"], [59, 15, 60, 13], [59, 17, 60, 15], [59, 18, 60, 16, "pressEvent"], [59, 28, 60, 26], [59, 29, 60, 27], [60, 8, 61, 6, "changedTouches"], [60, 22, 61, 20], [60, 24, 61, 22], [60, 25, 61, 23, "pressEvent"], [60, 35, 61, 33], [60, 36, 61, 34], [61, 8, 62, 6, "identifier"], [61, 18, 62, 16], [61, 20, 62, 18, "pressEvent"], [61, 30, 62, 28], [61, 31, 62, 29, "identifier"], [61, 41, 62, 39], [62, 8, 63, 6, "locationX"], [62, 17, 63, 15], [62, 19, 63, 17, "event"], [62, 24, 63, 22], [62, 25, 63, 23, "x"], [62, 26, 63, 24], [63, 8, 64, 6, "locationY"], [63, 17, 64, 15], [63, 19, 64, 17, "event"], [63, 24, 64, 22], [63, 25, 64, 23, "y"], [63, 26, 64, 24], [64, 8, 65, 6, "pageX"], [64, 13, 65, 11], [64, 15, 65, 13, "event"], [64, 20, 65, 18], [64, 21, 65, 19, "absoluteX"], [64, 30, 65, 28], [65, 8, 66, 6, "pageY"], [65, 13, 66, 11], [65, 15, 66, 13, "event"], [65, 20, 66, 18], [65, 21, 66, 19, "absoluteY"], [65, 30, 66, 28], [66, 8, 67, 6, "target"], [66, 14, 67, 12], [66, 16, 67, 14, "targetId"], [66, 24, 67, 22], [67, 8, 68, 6, "timestamp"], [67, 17, 68, 15], [67, 19, 68, 17, "timestamp"], [67, 28, 68, 26], [68, 8, 69, 6, "force"], [68, 13, 69, 11], [68, 15, 69, 13, "undefined"], [69, 6, 70, 4], [70, 4, 71, 2], [70, 5, 71, 3], [71, 2, 72, 0], [71, 3, 72, 1], [72, 2, 72, 2, "exports"], [72, 9, 72, 2], [72, 10, 72, 2, "gestureToPressableEvent"], [72, 33, 72, 2], [72, 36, 72, 2, "gestureToPressableEvent"], [72, 59, 72, 2], [73, 2, 74, 0], [73, 8, 74, 6, "gestureTouchToPressableEvent"], [73, 36, 74, 34], [73, 39, 74, 37, "event"], [73, 44, 74, 42], [73, 48, 74, 46], [74, 4, 75, 2], [74, 8, 75, 6, "_event$allTouches$at$"], [74, 29, 75, 27], [74, 31, 75, 29, "_event$allTouches$at"], [74, 51, 75, 49], [74, 53, 75, 51, "_event$allTouches$at$2"], [74, 75, 75, 73], [74, 77, 75, 75, "_event$allTouches$at2"], [74, 98, 75, 96], [74, 100, 75, 98, "_event$allTouches$at$3"], [74, 122, 75, 120], [74, 124, 75, 122, "_event$allTouches$at3"], [74, 145, 75, 143], [74, 147, 75, 145, "_event$allTouches$at$4"], [74, 169, 75, 167], [74, 171, 75, 169, "_event$allTouches$at4"], [74, 192, 75, 190], [75, 4, 77, 2], [75, 10, 77, 8, "timestamp"], [75, 19, 77, 17], [75, 22, 77, 20, "Date"], [75, 26, 77, 24], [75, 27, 77, 25, "now"], [75, 30, 77, 28], [75, 31, 77, 29], [75, 32, 77, 30], [75, 33, 77, 31], [75, 34, 77, 32], [77, 4, 79, 2], [77, 10, 79, 8, "targetId"], [77, 18, 79, 16], [77, 21, 79, 19], [77, 22, 79, 20], [78, 4, 80, 2], [78, 10, 80, 8, "touchesList"], [78, 21, 80, 19], [78, 24, 80, 22, "event"], [78, 29, 80, 27], [78, 30, 80, 28, "allTouches"], [78, 40, 80, 38], [78, 41, 80, 39, "map"], [78, 44, 80, 42], [78, 45, 80, 43, "touch"], [78, 50, 80, 48], [78, 54, 80, 52, "touchDataToPressEvent"], [78, 75, 80, 73], [78, 76, 80, 74, "touch"], [78, 81, 80, 79], [78, 83, 80, 81, "timestamp"], [78, 92, 80, 90], [78, 94, 80, 92, "targetId"], [78, 102, 80, 100], [78, 103, 80, 101], [78, 104, 80, 102], [79, 4, 81, 2], [79, 10, 81, 8, "changedTouchesList"], [79, 28, 81, 26], [79, 31, 81, 29, "event"], [79, 36, 81, 34], [79, 37, 81, 35, "changedTouches"], [79, 51, 81, 49], [79, 52, 81, 50, "map"], [79, 55, 81, 53], [79, 56, 81, 54, "touch"], [79, 61, 81, 59], [79, 65, 81, 63, "touchDataToPressEvent"], [79, 86, 81, 84], [79, 87, 81, 85, "touch"], [79, 92, 81, 90], [79, 94, 81, 92, "timestamp"], [79, 103, 81, 101], [79, 105, 81, 103, "targetId"], [79, 113, 81, 111], [79, 114, 81, 112], [79, 115, 81, 113], [80, 4, 82, 2], [80, 11, 82, 9], [81, 6, 83, 4, "nativeEvent"], [81, 17, 83, 15], [81, 19, 83, 17], [82, 8, 84, 6, "touches"], [82, 15, 84, 13], [82, 17, 84, 15, "touchesList"], [82, 28, 84, 26], [83, 8, 85, 6, "changedTouches"], [83, 22, 85, 20], [83, 24, 85, 22, "changedTouchesList"], [83, 42, 85, 40], [84, 8, 86, 6, "identifier"], [84, 18, 86, 16], [84, 20, 86, 18, "event"], [84, 25, 86, 23], [84, 26, 86, 24, "handlerTag"], [84, 36, 86, 34], [85, 8, 87, 6, "locationX"], [85, 17, 87, 15], [85, 19, 87, 17], [85, 20, 87, 18, "_event$allTouches$at$"], [85, 41, 87, 39], [85, 44, 87, 42], [85, 45, 87, 43, "_event$allTouches$at"], [85, 65, 87, 63], [85, 68, 87, 66, "event"], [85, 73, 87, 71], [85, 74, 87, 72, "allTouches"], [85, 84, 87, 82], [85, 85, 87, 83, "at"], [85, 87, 87, 85], [85, 88, 87, 86], [85, 89, 87, 87], [85, 90, 87, 88], [85, 96, 87, 94], [85, 100, 87, 98], [85, 104, 87, 102, "_event$allTouches$at"], [85, 124, 87, 122], [85, 129, 87, 127], [85, 134, 87, 132], [85, 135, 87, 133], [85, 138, 87, 136], [85, 143, 87, 141], [85, 144, 87, 142], [85, 147, 87, 145, "_event$allTouches$at"], [85, 167, 87, 165], [85, 168, 87, 166, "x"], [85, 169, 87, 167], [85, 175, 87, 173], [85, 179, 87, 177], [85, 183, 87, 181, "_event$allTouches$at$"], [85, 204, 87, 202], [85, 209, 87, 207], [85, 214, 87, 212], [85, 215, 87, 213], [85, 218, 87, 216, "_event$allTouches$at$"], [85, 239, 87, 237], [85, 242, 87, 240], [85, 243, 87, 241], [85, 244, 87, 242], [86, 8, 88, 6, "locationY"], [86, 17, 88, 15], [86, 19, 88, 17], [86, 20, 88, 18, "_event$allTouches$at$2"], [86, 42, 88, 40], [86, 45, 88, 43], [86, 46, 88, 44, "_event$allTouches$at2"], [86, 67, 88, 65], [86, 70, 88, 68, "event"], [86, 75, 88, 73], [86, 76, 88, 74, "allTouches"], [86, 86, 88, 84], [86, 87, 88, 85, "at"], [86, 89, 88, 87], [86, 90, 88, 88], [86, 91, 88, 89], [86, 92, 88, 90], [86, 98, 88, 96], [86, 102, 88, 100], [86, 106, 88, 104, "_event$allTouches$at2"], [86, 127, 88, 125], [86, 132, 88, 130], [86, 137, 88, 135], [86, 138, 88, 136], [86, 141, 88, 139], [86, 146, 88, 144], [86, 147, 88, 145], [86, 150, 88, 148, "_event$allTouches$at2"], [86, 171, 88, 169], [86, 172, 88, 170, "y"], [86, 173, 88, 171], [86, 179, 88, 177], [86, 183, 88, 181], [86, 187, 88, 185, "_event$allTouches$at$2"], [86, 209, 88, 207], [86, 214, 88, 212], [86, 219, 88, 217], [86, 220, 88, 218], [86, 223, 88, 221, "_event$allTouches$at$2"], [86, 245, 88, 243], [86, 248, 88, 246], [86, 249, 88, 247], [86, 250, 88, 248], [87, 8, 89, 6, "pageX"], [87, 13, 89, 11], [87, 15, 89, 13], [87, 16, 89, 14, "_event$allTouches$at$3"], [87, 38, 89, 36], [87, 41, 89, 39], [87, 42, 89, 40, "_event$allTouches$at3"], [87, 63, 89, 61], [87, 66, 89, 64, "event"], [87, 71, 89, 69], [87, 72, 89, 70, "allTouches"], [87, 82, 89, 80], [87, 83, 89, 81, "at"], [87, 85, 89, 83], [87, 86, 89, 84], [87, 87, 89, 85], [87, 88, 89, 86], [87, 94, 89, 92], [87, 98, 89, 96], [87, 102, 89, 100, "_event$allTouches$at3"], [87, 123, 89, 121], [87, 128, 89, 126], [87, 133, 89, 131], [87, 134, 89, 132], [87, 137, 89, 135], [87, 142, 89, 140], [87, 143, 89, 141], [87, 146, 89, 144, "_event$allTouches$at3"], [87, 167, 89, 165], [87, 168, 89, 166, "absoluteX"], [87, 177, 89, 175], [87, 183, 89, 181], [87, 187, 89, 185], [87, 191, 89, 189, "_event$allTouches$at$3"], [87, 213, 89, 211], [87, 218, 89, 216], [87, 223, 89, 221], [87, 224, 89, 222], [87, 227, 89, 225, "_event$allTouches$at$3"], [87, 249, 89, 247], [87, 252, 89, 250], [87, 253, 89, 251], [87, 254, 89, 252], [88, 8, 90, 6, "pageY"], [88, 13, 90, 11], [88, 15, 90, 13], [88, 16, 90, 14, "_event$allTouches$at$4"], [88, 38, 90, 36], [88, 41, 90, 39], [88, 42, 90, 40, "_event$allTouches$at4"], [88, 63, 90, 61], [88, 66, 90, 64, "event"], [88, 71, 90, 69], [88, 72, 90, 70, "allTouches"], [88, 82, 90, 80], [88, 83, 90, 81, "at"], [88, 85, 90, 83], [88, 86, 90, 84], [88, 87, 90, 85], [88, 88, 90, 86], [88, 94, 90, 92], [88, 98, 90, 96], [88, 102, 90, 100, "_event$allTouches$at4"], [88, 123, 90, 121], [88, 128, 90, 126], [88, 133, 90, 131], [88, 134, 90, 132], [88, 137, 90, 135], [88, 142, 90, 140], [88, 143, 90, 141], [88, 146, 90, 144, "_event$allTouches$at4"], [88, 167, 90, 165], [88, 168, 90, 166, "absoluteY"], [88, 177, 90, 175], [88, 183, 90, 181], [88, 187, 90, 185], [88, 191, 90, 189, "_event$allTouches$at$4"], [88, 213, 90, 211], [88, 218, 90, 216], [88, 223, 90, 221], [88, 224, 90, 222], [88, 227, 90, 225, "_event$allTouches$at$4"], [88, 249, 90, 247], [88, 252, 90, 250], [88, 253, 90, 251], [88, 254, 90, 252], [89, 8, 91, 6, "target"], [89, 14, 91, 12], [89, 16, 91, 14, "targetId"], [89, 24, 91, 22], [90, 8, 92, 6, "timestamp"], [90, 17, 92, 15], [90, 19, 92, 17, "timestamp"], [90, 28, 92, 26], [91, 8, 93, 6, "force"], [91, 13, 93, 11], [91, 15, 93, 13, "undefined"], [92, 6, 94, 4], [93, 4, 95, 2], [93, 5, 95, 3], [94, 2, 96, 0], [94, 3, 96, 1], [95, 2, 96, 2, "exports"], [95, 9, 96, 2], [95, 10, 96, 2, "gestureTouchToPressableEvent"], [95, 38, 96, 2], [95, 41, 96, 2, "gestureTouchToPressableEvent"], [95, 69, 96, 2], [96, 0, 96, 2], [96, 3]], "functionMap": {"names": ["<global>", "numberAsInset", "addInsets", "touchDataToPressEvent", "gestureToPressEvent", "isTouchWithinInset", "gestureToPressableEvent", "gestureTouchToPressableEvent", "event.allTouches.map$argument_0", "event.changedTouches.map$argument_0"], "mappings": "AAA,sBC;EDK;kBEE;CFS;8BGE;EHY;4BIE;EJY;2BKE;CLI;gCME;CNmB;qCOE;2CCM,0DD;sDEC,0DF;CPe"}}, "type": "js/module"}]}