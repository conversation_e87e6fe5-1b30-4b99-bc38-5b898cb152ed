{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "../PlatformPressable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 5, "column": 60, "index": 160}}], "key": "auJZ4k92W56l1sd57k0rcJrkXw0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 161}, "end": {"line": 6, "column": 48, "index": 209}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderButton = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _PlatformPressable = require(_dependencyMap[4], \"../PlatformPressable.js\");\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function HeaderButtonInternal({\n    disabled,\n    onPress,\n    pressColor,\n    pressOpacity,\n    accessibilityLabel,\n    testID,\n    style,\n    href,\n    children\n  }, ref) {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_PlatformPressable.PlatformPressable, {\n      ref: ref,\n      disabled: disabled,\n      href: href,\n      \"aria-label\": accessibilityLabel,\n      testID: testID,\n      onPress: onPress,\n      pressColor: pressColor,\n      pressOpacity: pressOpacity,\n      android_ripple: androidRipple,\n      style: [styles.container, disabled && styles.disabled, style],\n      hitSlop: _Platform.default.select({\n        ios: undefined,\n        default: {\n          top: 16,\n          right: 16,\n          bottom: 16,\n          left: 16\n        }\n      }),\n      children: children\n    });\n  }\n  const HeaderButton = exports.HeaderButton = /*#__PURE__*/React.forwardRef(HeaderButtonInternal);\n  HeaderButton.displayName = 'HeaderButton';\n  const androidRipple = {\n    borderless: true,\n    foreground: _Platform.default.OS === 'android' && _Platform.default.Version >= 23,\n    radius: 20\n  };\n  const styles = _StyleSheet.default.create({\n    container: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      paddingHorizontal: 8,\n      // Roundness for iPad hover effect\n      borderRadius: 10\n    },\n    disabled: {\n      opacity: 0.5\n    }\n  });\n});", "lineCount": 68, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 22, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 3, 31], [10, 6, 3, 31, "_Platform"], [10, 15, 3, 31], [10, 18, 3, 31, "_interopRequireDefault"], [10, 40, 3, 31], [10, 41, 3, 31, "require"], [10, 48, 3, 31], [10, 49, 3, 31, "_dependencyMap"], [10, 63, 3, 31], [11, 2, 3, 31], [11, 6, 3, 31, "_StyleSheet"], [11, 17, 3, 31], [11, 20, 3, 31, "_interopRequireDefault"], [11, 42, 3, 31], [11, 43, 3, 31, "require"], [11, 50, 3, 31], [11, 51, 3, 31, "_dependencyMap"], [11, 65, 3, 31], [12, 2, 5, 0], [12, 6, 5, 0, "_PlatformPressable"], [12, 24, 5, 0], [12, 27, 5, 0, "require"], [12, 34, 5, 0], [12, 35, 5, 0, "_dependencyMap"], [12, 49, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_jsxRuntime"], [13, 17, 6, 0], [13, 20, 6, 0, "require"], [13, 27, 6, 0], [13, 28, 6, 0, "_dependencyMap"], [13, 42, 6, 0], [14, 2, 6, 48], [14, 11, 6, 48, "_interopRequireWildcard"], [14, 35, 6, 48, "e"], [14, 36, 6, 48], [14, 38, 6, 48, "t"], [14, 39, 6, 48], [14, 68, 6, 48, "WeakMap"], [14, 75, 6, 48], [14, 81, 6, 48, "r"], [14, 82, 6, 48], [14, 89, 6, 48, "WeakMap"], [14, 96, 6, 48], [14, 100, 6, 48, "n"], [14, 101, 6, 48], [14, 108, 6, 48, "WeakMap"], [14, 115, 6, 48], [14, 127, 6, 48, "_interopRequireWildcard"], [14, 150, 6, 48], [14, 162, 6, 48, "_interopRequireWildcard"], [14, 163, 6, 48, "e"], [14, 164, 6, 48], [14, 166, 6, 48, "t"], [14, 167, 6, 48], [14, 176, 6, 48, "t"], [14, 177, 6, 48], [14, 181, 6, 48, "e"], [14, 182, 6, 48], [14, 186, 6, 48, "e"], [14, 187, 6, 48], [14, 188, 6, 48, "__esModule"], [14, 198, 6, 48], [14, 207, 6, 48, "e"], [14, 208, 6, 48], [14, 214, 6, 48, "o"], [14, 215, 6, 48], [14, 217, 6, 48, "i"], [14, 218, 6, 48], [14, 220, 6, 48, "f"], [14, 221, 6, 48], [14, 226, 6, 48, "__proto__"], [14, 235, 6, 48], [14, 243, 6, 48, "default"], [14, 250, 6, 48], [14, 252, 6, 48, "e"], [14, 253, 6, 48], [14, 270, 6, 48, "e"], [14, 271, 6, 48], [14, 294, 6, 48, "e"], [14, 295, 6, 48], [14, 320, 6, 48, "e"], [14, 321, 6, 48], [14, 330, 6, 48, "f"], [14, 331, 6, 48], [14, 337, 6, 48, "o"], [14, 338, 6, 48], [14, 341, 6, 48, "t"], [14, 342, 6, 48], [14, 345, 6, 48, "n"], [14, 346, 6, 48], [14, 349, 6, 48, "r"], [14, 350, 6, 48], [14, 358, 6, 48, "o"], [14, 359, 6, 48], [14, 360, 6, 48, "has"], [14, 363, 6, 48], [14, 364, 6, 48, "e"], [14, 365, 6, 48], [14, 375, 6, 48, "o"], [14, 376, 6, 48], [14, 377, 6, 48, "get"], [14, 380, 6, 48], [14, 381, 6, 48, "e"], [14, 382, 6, 48], [14, 385, 6, 48, "o"], [14, 386, 6, 48], [14, 387, 6, 48, "set"], [14, 390, 6, 48], [14, 391, 6, 48, "e"], [14, 392, 6, 48], [14, 394, 6, 48, "f"], [14, 395, 6, 48], [14, 411, 6, 48, "t"], [14, 412, 6, 48], [14, 416, 6, 48, "e"], [14, 417, 6, 48], [14, 433, 6, 48, "t"], [14, 434, 6, 48], [14, 441, 6, 48, "hasOwnProperty"], [14, 455, 6, 48], [14, 456, 6, 48, "call"], [14, 460, 6, 48], [14, 461, 6, 48, "e"], [14, 462, 6, 48], [14, 464, 6, 48, "t"], [14, 465, 6, 48], [14, 472, 6, 48, "i"], [14, 473, 6, 48], [14, 477, 6, 48, "o"], [14, 478, 6, 48], [14, 481, 6, 48, "Object"], [14, 487, 6, 48], [14, 488, 6, 48, "defineProperty"], [14, 502, 6, 48], [14, 507, 6, 48, "Object"], [14, 513, 6, 48], [14, 514, 6, 48, "getOwnPropertyDescriptor"], [14, 538, 6, 48], [14, 539, 6, 48, "e"], [14, 540, 6, 48], [14, 542, 6, 48, "t"], [14, 543, 6, 48], [14, 550, 6, 48, "i"], [14, 551, 6, 48], [14, 552, 6, 48, "get"], [14, 555, 6, 48], [14, 559, 6, 48, "i"], [14, 560, 6, 48], [14, 561, 6, 48, "set"], [14, 564, 6, 48], [14, 568, 6, 48, "o"], [14, 569, 6, 48], [14, 570, 6, 48, "f"], [14, 571, 6, 48], [14, 573, 6, 48, "t"], [14, 574, 6, 48], [14, 576, 6, 48, "i"], [14, 577, 6, 48], [14, 581, 6, 48, "f"], [14, 582, 6, 48], [14, 583, 6, 48, "t"], [14, 584, 6, 48], [14, 588, 6, 48, "e"], [14, 589, 6, 48], [14, 590, 6, 48, "t"], [14, 591, 6, 48], [14, 602, 6, 48, "f"], [14, 603, 6, 48], [14, 608, 6, 48, "e"], [14, 609, 6, 48], [14, 611, 6, 48, "t"], [14, 612, 6, 48], [15, 2, 7, 0], [15, 11, 7, 9, "HeaderButtonInternal"], [15, 31, 7, 29, "HeaderButtonInternal"], [15, 32, 7, 30], [16, 4, 8, 2, "disabled"], [16, 12, 8, 10], [17, 4, 9, 2, "onPress"], [17, 11, 9, 9], [18, 4, 10, 2, "pressColor"], [18, 14, 10, 12], [19, 4, 11, 2, "pressOpacity"], [19, 16, 11, 14], [20, 4, 12, 2, "accessibilityLabel"], [20, 22, 12, 20], [21, 4, 13, 2, "testID"], [21, 10, 13, 8], [22, 4, 14, 2, "style"], [22, 9, 14, 7], [23, 4, 15, 2, "href"], [23, 8, 15, 6], [24, 4, 16, 2, "children"], [25, 2, 17, 0], [25, 3, 17, 1], [25, 5, 17, 3, "ref"], [25, 8, 17, 6], [25, 10, 17, 8], [26, 4, 18, 2], [26, 11, 18, 9], [26, 24, 18, 22], [26, 28, 18, 22, "_jsx"], [26, 43, 18, 26], [26, 45, 18, 27, "PlatformPressable"], [26, 81, 18, 44], [26, 83, 18, 46], [27, 6, 19, 4, "ref"], [27, 9, 19, 7], [27, 11, 19, 9, "ref"], [27, 14, 19, 12], [28, 6, 20, 4, "disabled"], [28, 14, 20, 12], [28, 16, 20, 14, "disabled"], [28, 24, 20, 22], [29, 6, 21, 4, "href"], [29, 10, 21, 8], [29, 12, 21, 10, "href"], [29, 16, 21, 14], [30, 6, 22, 4], [30, 18, 22, 16], [30, 20, 22, 18, "accessibilityLabel"], [30, 38, 22, 36], [31, 6, 23, 4, "testID"], [31, 12, 23, 10], [31, 14, 23, 12, "testID"], [31, 20, 23, 18], [32, 6, 24, 4, "onPress"], [32, 13, 24, 11], [32, 15, 24, 13, "onPress"], [32, 22, 24, 20], [33, 6, 25, 4, "pressColor"], [33, 16, 25, 14], [33, 18, 25, 16, "pressColor"], [33, 28, 25, 26], [34, 6, 26, 4, "pressOpacity"], [34, 18, 26, 16], [34, 20, 26, 18, "pressOpacity"], [34, 32, 26, 30], [35, 6, 27, 4, "android_ripple"], [35, 20, 27, 18], [35, 22, 27, 20, "androidRipple"], [35, 35, 27, 33], [36, 6, 28, 4, "style"], [36, 11, 28, 9], [36, 13, 28, 11], [36, 14, 28, 12, "styles"], [36, 20, 28, 18], [36, 21, 28, 19, "container"], [36, 30, 28, 28], [36, 32, 28, 30, "disabled"], [36, 40, 28, 38], [36, 44, 28, 42, "styles"], [36, 50, 28, 48], [36, 51, 28, 49, "disabled"], [36, 59, 28, 57], [36, 61, 28, 59, "style"], [36, 66, 28, 64], [36, 67, 28, 65], [37, 6, 29, 4, "hitSlop"], [37, 13, 29, 11], [37, 15, 29, 13, "Platform"], [37, 32, 29, 21], [37, 33, 29, 22, "select"], [37, 39, 29, 28], [37, 40, 29, 29], [38, 8, 30, 6, "ios"], [38, 11, 30, 9], [38, 13, 30, 11, "undefined"], [38, 22, 30, 20], [39, 8, 31, 6, "default"], [39, 15, 31, 13], [39, 17, 31, 15], [40, 10, 32, 8, "top"], [40, 13, 32, 11], [40, 15, 32, 13], [40, 17, 32, 15], [41, 10, 33, 8, "right"], [41, 15, 33, 13], [41, 17, 33, 15], [41, 19, 33, 17], [42, 10, 34, 8, "bottom"], [42, 16, 34, 14], [42, 18, 34, 16], [42, 20, 34, 18], [43, 10, 35, 8, "left"], [43, 14, 35, 12], [43, 16, 35, 14], [44, 8, 36, 6], [45, 6, 37, 4], [45, 7, 37, 5], [45, 8, 37, 6], [46, 6, 38, 4, "children"], [46, 14, 38, 12], [46, 16, 38, 14, "children"], [47, 4, 39, 2], [47, 5, 39, 3], [47, 6, 39, 4], [48, 2, 40, 0], [49, 2, 41, 7], [49, 8, 41, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [49, 20, 41, 25], [49, 23, 41, 25, "exports"], [49, 30, 41, 25], [49, 31, 41, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [49, 43, 41, 25], [49, 46, 41, 28], [49, 59, 41, 41, "React"], [49, 64, 41, 46], [49, 65, 41, 47, "forwardRef"], [49, 75, 41, 57], [49, 76, 41, 58, "HeaderButtonInternal"], [49, 96, 41, 78], [49, 97, 41, 79], [50, 2, 42, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [50, 14, 42, 12], [50, 15, 42, 13, "displayName"], [50, 26, 42, 24], [50, 29, 42, 27], [50, 43, 42, 41], [51, 2, 43, 0], [51, 8, 43, 6, "androidRipple"], [51, 21, 43, 19], [51, 24, 43, 22], [52, 4, 44, 2, "borderless"], [52, 14, 44, 12], [52, 16, 44, 14], [52, 20, 44, 18], [53, 4, 45, 2, "foreground"], [53, 14, 45, 12], [53, 16, 45, 14, "Platform"], [53, 33, 45, 22], [53, 34, 45, 23, "OS"], [53, 36, 45, 25], [53, 41, 45, 30], [53, 50, 45, 39], [53, 54, 45, 43, "Platform"], [53, 71, 45, 51], [53, 72, 45, 52, "Version"], [53, 79, 45, 59], [53, 83, 45, 63], [53, 85, 45, 65], [54, 4, 46, 2, "radius"], [54, 10, 46, 8], [54, 12, 46, 10], [55, 2, 47, 0], [55, 3, 47, 1], [56, 2, 48, 0], [56, 8, 48, 6, "styles"], [56, 14, 48, 12], [56, 17, 48, 15, "StyleSheet"], [56, 36, 48, 25], [56, 37, 48, 26, "create"], [56, 43, 48, 32], [56, 44, 48, 33], [57, 4, 49, 2, "container"], [57, 13, 49, 11], [57, 15, 49, 13], [58, 6, 50, 4, "flexDirection"], [58, 19, 50, 17], [58, 21, 50, 19], [58, 26, 50, 24], [59, 6, 51, 4, "alignItems"], [59, 16, 51, 14], [59, 18, 51, 16], [59, 26, 51, 24], [60, 6, 52, 4, "paddingHorizontal"], [60, 23, 52, 21], [60, 25, 52, 23], [60, 26, 52, 24], [61, 6, 53, 4], [62, 6, 54, 4, "borderRadius"], [62, 18, 54, 16], [62, 20, 54, 18], [63, 4, 55, 2], [63, 5, 55, 3], [64, 4, 56, 2, "disabled"], [64, 12, 56, 10], [64, 14, 56, 12], [65, 6, 57, 4, "opacity"], [65, 13, 57, 11], [65, 15, 57, 13], [66, 4, 58, 2], [67, 2, 59, 0], [67, 3, 59, 1], [67, 4, 59, 2], [68, 0, 59, 3], [68, 3]], "functionMap": {"names": ["<global>", "HeaderButtonInternal"], "mappings": "AAA;ACM;CDiC"}}, "type": "js/module"}]}