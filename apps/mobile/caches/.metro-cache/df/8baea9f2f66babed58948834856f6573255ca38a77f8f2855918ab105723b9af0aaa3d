{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Trash = exports.default = (0, _createLucideIcon.default)(\"Trash\", [[\"path\", {\n    d: \"M3 6h18\",\n    key: \"d0wm0j\"\n  }], [\"path\", {\n    d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\",\n    key: \"4alrt4\"\n  }], [\"path\", {\n    d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\",\n    key: \"v07s0e\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Trash"], [15, 13, 10, 11], [15, 16, 10, 11, "exports"], [15, 23, 10, 11], [15, 24, 10, 11, "default"], [15, 31, 10, 11], [15, 34, 10, 14], [15, 38, 10, 14, "createLucideIcon"], [15, 63, 10, 30], [15, 65, 10, 31], [15, 72, 10, 38], [15, 74, 10, 40], [15, 75, 11, 2], [15, 76, 11, 3], [15, 82, 11, 9], [15, 84, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 16, 11, 25], [17, 4, 11, 27, "key"], [17, 7, 11, 30], [17, 9, 11, 32], [18, 2, 11, 41], [18, 3, 11, 42], [18, 4, 11, 43], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 46, 12, 55], [20, 4, 12, 57, "key"], [20, 7, 12, 60], [20, 9, 12, 62], [21, 2, 12, 71], [21, 3, 12, 72], [21, 4, 12, 73], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 43, 13, 52], [23, 4, 13, 54, "key"], [23, 7, 13, 57], [23, 9, 13, 59], [24, 2, 13, 68], [24, 3, 13, 69], [24, 4, 13, 70], [24, 5, 14, 1], [24, 6, 14, 2], [25, 0, 14, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}