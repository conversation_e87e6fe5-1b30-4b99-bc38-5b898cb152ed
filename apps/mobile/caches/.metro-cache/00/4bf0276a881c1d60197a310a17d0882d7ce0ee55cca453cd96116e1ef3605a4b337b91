{"dependencies": [{"name": "../animationParser", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 71, "index": 85}}], "key": "NS2upIa4aHN1XdKmQKcusYkE9o0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StretchOutData = exports.StretchOut = exports.StretchInData = exports.StretchIn = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser\");\n  var DEFAULT_STRETCH_TIME = 0.3;\n  var StretchInData = exports.StretchInData = {\n    StretchInX: {\n      name: 'StretchInX',\n      style: {\n        0: {\n          transform: [{\n            scaleX: 0\n          }]\n        },\n        100: {\n          transform: [{\n            scaleX: 1\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    },\n    StretchInY: {\n      name: 'StretchInY',\n      style: {\n        0: {\n          transform: [{\n            scaleY: 0\n          }]\n        },\n        100: {\n          transform: [{\n            scaleY: 1\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    }\n  };\n  var StretchOutData = exports.StretchOutData = {\n    StretchOutX: {\n      name: 'StretchOutX',\n      style: {\n        0: {\n          transform: [{\n            scaleX: 1\n          }]\n        },\n        100: {\n          transform: [{\n            scaleX: 0\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    },\n    StretchOutY: {\n      name: 'StretchOutY',\n      style: {\n        0: {\n          transform: [{\n            scaleY: 1\n          }]\n        },\n        100: {\n          transform: [{\n            scaleY: 0\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    }\n  };\n  var StretchIn = exports.StretchIn = {\n    StretchInX: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchInData.StretchInX),\n      duration: StretchInData.StretchInX.duration\n    },\n    StretchInY: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchInData.StretchInY),\n      duration: StretchInData.StretchInY.duration\n    }\n  };\n  var StretchOut = exports.StretchOut = {\n    StretchOutX: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchOutData.StretchOutX),\n      duration: StretchOutData.StretchOutX.duration\n    },\n    StretchOutY: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchOutData.StretchOutY),\n      duration: StretchOutData.StretchOutY.duration\n    }\n  };\n});", "lineCount": 98, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "StretchOutData"], [7, 24, 1, 13], [7, 27, 1, 13, "exports"], [7, 34, 1, 13], [7, 35, 1, 13, "StretchOut"], [7, 45, 1, 13], [7, 48, 1, 13, "exports"], [7, 55, 1, 13], [7, 56, 1, 13, "StretchInData"], [7, 69, 1, 13], [7, 72, 1, 13, "exports"], [7, 79, 1, 13], [7, 80, 1, 13, "StretchIn"], [7, 89, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_animation<PERSON><PERSON>er"], [8, 22, 2, 0], [8, 25, 2, 0, "require"], [8, 32, 2, 0], [8, 33, 2, 0, "_dependencyMap"], [8, 47, 2, 0], [9, 2, 4, 0], [9, 6, 4, 6, "DEFAULT_STRETCH_TIME"], [9, 26, 4, 26], [9, 29, 4, 29], [9, 32, 4, 32], [10, 2, 6, 7], [10, 6, 6, 13, "StretchInData"], [10, 19, 6, 26], [10, 22, 6, 26, "exports"], [10, 29, 6, 26], [10, 30, 6, 26, "StretchInData"], [10, 43, 6, 26], [10, 46, 6, 29], [11, 4, 7, 2, "StretchInX"], [11, 14, 7, 12], [11, 16, 7, 14], [12, 6, 8, 4, "name"], [12, 10, 8, 8], [12, 12, 8, 10], [12, 24, 8, 22], [13, 6, 9, 4, "style"], [13, 11, 9, 9], [13, 13, 9, 11], [14, 8, 10, 6], [14, 9, 10, 7], [14, 11, 10, 9], [15, 10, 10, 11, "transform"], [15, 19, 10, 20], [15, 21, 10, 22], [15, 22, 10, 23], [16, 12, 10, 25, "scaleX"], [16, 18, 10, 31], [16, 20, 10, 33], [17, 10, 10, 35], [17, 11, 10, 36], [18, 8, 10, 38], [18, 9, 10, 39], [19, 8, 11, 6], [19, 11, 11, 9], [19, 13, 11, 11], [20, 10, 11, 13, "transform"], [20, 19, 11, 22], [20, 21, 11, 24], [20, 22, 11, 25], [21, 12, 11, 27, "scaleX"], [21, 18, 11, 33], [21, 20, 11, 35], [22, 10, 11, 37], [22, 11, 11, 38], [23, 8, 11, 40], [24, 6, 12, 4], [24, 7, 12, 5], [25, 6, 13, 4, "duration"], [25, 14, 13, 12], [25, 16, 13, 14, "DEFAULT_STRETCH_TIME"], [26, 4, 14, 2], [26, 5, 14, 3], [27, 4, 16, 2, "StretchInY"], [27, 14, 16, 12], [27, 16, 16, 14], [28, 6, 17, 4, "name"], [28, 10, 17, 8], [28, 12, 17, 10], [28, 24, 17, 22], [29, 6, 18, 4, "style"], [29, 11, 18, 9], [29, 13, 18, 11], [30, 8, 19, 6], [30, 9, 19, 7], [30, 11, 19, 9], [31, 10, 19, 11, "transform"], [31, 19, 19, 20], [31, 21, 19, 22], [31, 22, 19, 23], [32, 12, 19, 25, "scaleY"], [32, 18, 19, 31], [32, 20, 19, 33], [33, 10, 19, 35], [33, 11, 19, 36], [34, 8, 19, 38], [34, 9, 19, 39], [35, 8, 20, 6], [35, 11, 20, 9], [35, 13, 20, 11], [36, 10, 20, 13, "transform"], [36, 19, 20, 22], [36, 21, 20, 24], [36, 22, 20, 25], [37, 12, 20, 27, "scaleY"], [37, 18, 20, 33], [37, 20, 20, 35], [38, 10, 20, 37], [38, 11, 20, 38], [39, 8, 20, 40], [40, 6, 21, 4], [40, 7, 21, 5], [41, 6, 22, 4, "duration"], [41, 14, 22, 12], [41, 16, 22, 14, "DEFAULT_STRETCH_TIME"], [42, 4, 23, 2], [43, 2, 24, 0], [43, 3, 24, 1], [44, 2, 26, 7], [44, 6, 26, 13, "StretchOutData"], [44, 20, 26, 27], [44, 23, 26, 27, "exports"], [44, 30, 26, 27], [44, 31, 26, 27, "StretchOutData"], [44, 45, 26, 27], [44, 48, 26, 30], [45, 4, 27, 2, "StretchOutX"], [45, 15, 27, 13], [45, 17, 27, 15], [46, 6, 28, 4, "name"], [46, 10, 28, 8], [46, 12, 28, 10], [46, 25, 28, 23], [47, 6, 29, 4, "style"], [47, 11, 29, 9], [47, 13, 29, 11], [48, 8, 30, 6], [48, 9, 30, 7], [48, 11, 30, 9], [49, 10, 30, 11, "transform"], [49, 19, 30, 20], [49, 21, 30, 22], [49, 22, 30, 23], [50, 12, 30, 25, "scaleX"], [50, 18, 30, 31], [50, 20, 30, 33], [51, 10, 30, 35], [51, 11, 30, 36], [52, 8, 30, 38], [52, 9, 30, 39], [53, 8, 31, 6], [53, 11, 31, 9], [53, 13, 31, 11], [54, 10, 31, 13, "transform"], [54, 19, 31, 22], [54, 21, 31, 24], [54, 22, 31, 25], [55, 12, 31, 27, "scaleX"], [55, 18, 31, 33], [55, 20, 31, 35], [56, 10, 31, 37], [56, 11, 31, 38], [57, 8, 31, 40], [58, 6, 32, 4], [58, 7, 32, 5], [59, 6, 33, 4, "duration"], [59, 14, 33, 12], [59, 16, 33, 14, "DEFAULT_STRETCH_TIME"], [60, 4, 34, 2], [60, 5, 34, 3], [61, 4, 36, 2, "StretchOutY"], [61, 15, 36, 13], [61, 17, 36, 15], [62, 6, 37, 4, "name"], [62, 10, 37, 8], [62, 12, 37, 10], [62, 25, 37, 23], [63, 6, 38, 4, "style"], [63, 11, 38, 9], [63, 13, 38, 11], [64, 8, 39, 6], [64, 9, 39, 7], [64, 11, 39, 9], [65, 10, 39, 11, "transform"], [65, 19, 39, 20], [65, 21, 39, 22], [65, 22, 39, 23], [66, 12, 39, 25, "scaleY"], [66, 18, 39, 31], [66, 20, 39, 33], [67, 10, 39, 35], [67, 11, 39, 36], [68, 8, 39, 38], [68, 9, 39, 39], [69, 8, 40, 6], [69, 11, 40, 9], [69, 13, 40, 11], [70, 10, 40, 13, "transform"], [70, 19, 40, 22], [70, 21, 40, 24], [70, 22, 40, 25], [71, 12, 40, 27, "scaleY"], [71, 18, 40, 33], [71, 20, 40, 35], [72, 10, 40, 37], [72, 11, 40, 38], [73, 8, 40, 40], [74, 6, 41, 4], [74, 7, 41, 5], [75, 6, 42, 4, "duration"], [75, 14, 42, 12], [75, 16, 42, 14, "DEFAULT_STRETCH_TIME"], [76, 4, 43, 2], [77, 2, 44, 0], [77, 3, 44, 1], [78, 2, 46, 7], [78, 6, 46, 13, "StretchIn"], [78, 15, 46, 22], [78, 18, 46, 22, "exports"], [78, 25, 46, 22], [78, 26, 46, 22, "StretchIn"], [78, 35, 46, 22], [78, 38, 46, 25], [79, 4, 47, 2, "StretchInX"], [79, 14, 47, 12], [79, 16, 47, 14], [80, 6, 48, 4, "style"], [80, 11, 48, 9], [80, 13, 48, 11], [80, 17, 48, 11, "convertAnimationObjectToKeyframes"], [80, 67, 48, 44], [80, 69, 48, 45, "StretchInData"], [80, 82, 48, 58], [80, 83, 48, 59, "StretchInX"], [80, 93, 48, 69], [80, 94, 48, 70], [81, 6, 49, 4, "duration"], [81, 14, 49, 12], [81, 16, 49, 14, "StretchInData"], [81, 29, 49, 27], [81, 30, 49, 28, "StretchInX"], [81, 40, 49, 38], [81, 41, 49, 39, "duration"], [82, 4, 50, 2], [82, 5, 50, 3], [83, 4, 51, 2, "StretchInY"], [83, 14, 51, 12], [83, 16, 51, 14], [84, 6, 52, 4, "style"], [84, 11, 52, 9], [84, 13, 52, 11], [84, 17, 52, 11, "convertAnimationObjectToKeyframes"], [84, 67, 52, 44], [84, 69, 52, 45, "StretchInData"], [84, 82, 52, 58], [84, 83, 52, 59, "StretchInY"], [84, 93, 52, 69], [84, 94, 52, 70], [85, 6, 53, 4, "duration"], [85, 14, 53, 12], [85, 16, 53, 14, "StretchInData"], [85, 29, 53, 27], [85, 30, 53, 28, "StretchInY"], [85, 40, 53, 38], [85, 41, 53, 39, "duration"], [86, 4, 54, 2], [87, 2, 55, 0], [87, 3, 55, 1], [88, 2, 57, 7], [88, 6, 57, 13, "StretchOut"], [88, 16, 57, 23], [88, 19, 57, 23, "exports"], [88, 26, 57, 23], [88, 27, 57, 23, "StretchOut"], [88, 37, 57, 23], [88, 40, 57, 26], [89, 4, 58, 2, "StretchOutX"], [89, 15, 58, 13], [89, 17, 58, 15], [90, 6, 59, 4, "style"], [90, 11, 59, 9], [90, 13, 59, 11], [90, 17, 59, 11, "convertAnimationObjectToKeyframes"], [90, 67, 59, 44], [90, 69, 59, 45, "StretchOutData"], [90, 83, 59, 59], [90, 84, 59, 60, "StretchOutX"], [90, 95, 59, 71], [90, 96, 59, 72], [91, 6, 60, 4, "duration"], [91, 14, 60, 12], [91, 16, 60, 14, "StretchOutData"], [91, 30, 60, 28], [91, 31, 60, 29, "StretchOutX"], [91, 42, 60, 40], [91, 43, 60, 41, "duration"], [92, 4, 61, 2], [92, 5, 61, 3], [93, 4, 62, 2, "StretchOutY"], [93, 15, 62, 13], [93, 17, 62, 15], [94, 6, 63, 4, "style"], [94, 11, 63, 9], [94, 13, 63, 11], [94, 17, 63, 11, "convertAnimationObjectToKeyframes"], [94, 67, 63, 44], [94, 69, 63, 45, "StretchOutData"], [94, 83, 63, 59], [94, 84, 63, 60, "StretchOutY"], [94, 95, 63, 71], [94, 96, 63, 72], [95, 6, 64, 4, "duration"], [95, 14, 64, 12], [95, 16, 64, 14, "StretchOutData"], [95, 30, 64, 28], [95, 31, 64, 29, "StretchOutY"], [95, 42, 64, 40], [95, 43, 64, 41, "duration"], [96, 4, 65, 2], [97, 2, 66, 0], [97, 3, 66, 1], [98, 0, 66, 2], [98, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}