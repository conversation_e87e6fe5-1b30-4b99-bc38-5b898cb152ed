{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BrushProperties = void 0;\n  // https://www.w3.org/TR/SVG11/color.html\n  var BrushProperties = exports.BrushProperties = ['fill', 'stroke', 'stopColor', 'floodColor', 'lightingColor'];\n});", "lineCount": 8, "map": [[6, 2, 1, 0], [7, 2, 2, 7], [7, 6, 2, 13, "BrushProperties"], [7, 21, 2, 28], [7, 24, 2, 28, "exports"], [7, 31, 2, 28], [7, 32, 2, 28, "BrushProperties"], [7, 47, 2, 28], [7, 50, 2, 31], [7, 51, 3, 2], [7, 57, 3, 8], [7, 59, 4, 2], [7, 67, 4, 10], [7, 69, 5, 2], [7, 80, 5, 13], [7, 82, 6, 2], [7, 94, 6, 14], [7, 96, 7, 2], [7, 111, 7, 17], [7, 112, 8, 1], [8, 0, 8, 2], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}