{"dependencies": [{"name": "../../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 50, "index": 65}}], "key": "vhY7QX3yty1rmiaRlwcQa5g4v48=", "exportNames": ["*"]}}, {"name": "../../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 66}, "end": {"line": 4, "column": 50, "index": 116}}], "key": "Jq1DcLPs1AjY3ygtzPUe4D8IdoQ=", "exportNames": ["*"]}}, {"name": "../../logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 117}, "end": {"line": 5, "column": 47, "index": 164}}], "key": "6SNP0qYc6Dvb4y6pRCC6IV2Z4aU=", "exportNames": ["*"]}}, {"name": "../../mockedRequestAnimationFrame.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 165}, "end": {"line": 6, "column": 83, "index": 248}}], "key": "dagNF1WtnvrbWFSurbmBySD1R/Y=", "exportNames": ["*"]}}, {"name": "../../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 249}, "end": {"line": 7, "column": 94, "index": 343}}], "key": "gUqK/inccYjUWjFbxEcDL+F40JA=", "exportNames": ["*"]}}, {"name": "../../worklets/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 344}, "end": {"line": 8, "column": 57, "index": 401}}], "key": "AhyzgbcWFzlDu7FRLeLfnqRRbpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Platform = void 0;\n  exports.createJSReanimatedModule = createJSReanimatedModule;\n  var _commonTypes = require(_dependencyMap[0], \"../../commonTypes.js\");\n  var _errors = require(_dependencyMap[1], \"../../errors.js\");\n  var _index = require(_dependencyMap[2], \"../../logger/index.js\");\n  var _mockedRequestAnimationFrame = require(_dependencyMap[3], \"../../mockedRequestAnimationFrame.js\");\n  var _PlatformChecker = require(_dependencyMap[4], \"../../PlatformChecker.js\");\n  var _index2 = require(_dependencyMap[5], \"../../worklets/index.js\");\n  function createJSReanimatedModule() {\n    return new JSReanimated();\n  }\n\n  // In Node.js environments (like when static rendering with Expo Router)\n  // requestAnimationFrame is unavailable, so we use our mock.\n  // It also has to be mocked for Jest purposes (see `initializeUIRuntime`).\n  const requestAnimationFrameImpl = (0, _PlatformChecker.isJest)() || !globalThis.requestAnimationFrame ? _mockedRequestAnimationFrame.mockedRequestAnimationFrame : globalThis.requestAnimationFrame;\n  class JSReanimated {\n    /**\n     * We keep the instance of `WorkletsModule` here to keep correct coupling of\n     * the modules and initialization order.\n     */\n    #workletsModule = _index2.WorkletsModule;\n    nextSensorId = 0;\n    sensors = new Map();\n    platform = undefined;\n    scheduleOnUI(worklet) {\n      // @ts-ignore web implementation has still not been updated after the rewrite, this will be addressed once the web implementation updates are ready\n      requestAnimationFrameImpl(worklet);\n    }\n    createWorkletRuntime(_name, _initializer) {\n      throw new _errors.ReanimatedError('createWorkletRuntime is not available in JSReanimated.');\n    }\n    scheduleOnRuntime() {\n      throw new _errors.ReanimatedError('scheduleOnRuntime is not available in JSReanimated.');\n    }\n    registerEventHandler(_eventHandler, _eventName, _emitterReactTag) {\n      throw new _errors.ReanimatedError('registerEventHandler is not available in JSReanimated.');\n    }\n    unregisterEventHandler(_) {\n      throw new _errors.ReanimatedError('unregisterEventHandler is not available in JSReanimated.');\n    }\n    enableLayoutAnimations() {\n      if ((0, _PlatformChecker.isWeb)()) {\n        _index.logger.warn('Layout Animations are not supported on web yet.');\n      } else if ((0, _PlatformChecker.isJest)()) {\n        _index.logger.warn('Layout Animations are no-ops when using Jest.');\n      } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n        _index.logger.warn('Layout Animations are no-ops when using Chrome Debugger.');\n      } else {\n        _index.logger.warn('Layout Animations are not supported on this configuration.');\n      }\n    }\n    configureLayoutAnimationBatch() {\n      // no-op\n    }\n    setShouldAnimateExitingForTag() {\n      // no-op\n    }\n    registerSensor(sensorType, interval, _iosReferenceFrame, eventHandler) {\n      if (!(0, _PlatformChecker.isWindowAvailable)()) {\n        // the window object is unavailable when building the server portion of a site that uses SSG\n        // this check is here to ensure that the server build won't fail\n        return -1;\n      }\n      if (this.platform === undefined) {\n        this.detectPlatform();\n      }\n      if (!(this.getSensorName(sensorType) in window)) {\n        // https://w3c.github.io/sensors/#secure-context\n        _index.logger.warn('Sensor is not available.' + ((0, _PlatformChecker.isWeb)() && location.protocol !== 'https:' ? ' Make sure you use secure origin with `npx expo start --web --https`.' : '') + (this.platform === Platform.WEB_IOS ? ' For iOS web, you will also have to also grant permission in the browser: https://dev.to/li/how-to-requestpermission-for-devicemotion-and-deviceorientation-events-in-ios-13-46g2.' : ''));\n        return -1;\n      }\n      if (this.platform === undefined) {\n        this.detectPlatform();\n      }\n      const sensor = this.initializeSensor(sensorType, interval);\n      sensor.addEventListener('reading', this.getSensorCallback(sensor, sensorType, eventHandler));\n      sensor.start();\n      this.sensors.set(this.nextSensorId, sensor);\n      return this.nextSensorId++;\n    }\n    getSensorCallback = (sensor, sensorType, eventHandler) => {\n      switch (sensorType) {\n        case _commonTypes.SensorType.ACCELEROMETER:\n        case _commonTypes.SensorType.GRAVITY:\n          return () => {\n            let {\n              x,\n              y,\n              z\n            } = sensor;\n\n            // Web Android sensors have a different coordinate system than iOS\n            if (this.platform === Platform.WEB_ANDROID) {\n              [x, y, z] = [-x, -y, -z];\n            }\n            // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n            eventHandler({\n              x,\n              y,\n              z,\n              interfaceOrientation: 0\n            });\n          };\n        case _commonTypes.SensorType.GYROSCOPE:\n        case _commonTypes.SensorType.MAGNETIC_FIELD:\n          return () => {\n            const {\n              x,\n              y,\n              z\n            } = sensor;\n            // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n            eventHandler({\n              x,\n              y,\n              z,\n              interfaceOrientation: 0\n            });\n          };\n        case _commonTypes.SensorType.ROTATION:\n          return () => {\n            let [qw, qx, qy, qz] = sensor.quaternion;\n\n            // Android sensors have a different coordinate system than iOS\n            if (this.platform === Platform.WEB_ANDROID) {\n              [qy, qz] = [qz, -qy];\n            }\n\n            // reference: https://stackoverflow.com/questions/5782658/extracting-yaw-from-a-quaternion\n            const yaw = -Math.atan2(2.0 * (qy * qz + qw * qx), qw * qw - qx * qx - qy * qy + qz * qz);\n            const pitch = Math.sin(-2.0 * (qx * qz - qw * qy));\n            const roll = -Math.atan2(2.0 * (qx * qy + qw * qz), qw * qw + qx * qx - qy * qy - qz * qz);\n            // TODO TYPESCRIPT on web ShareableRef is the value itself so we call it directly\n            eventHandler({\n              qw,\n              qx,\n              qy,\n              qz,\n              yaw,\n              pitch,\n              roll,\n              interfaceOrientation: 0\n            });\n          };\n      }\n    };\n    unregisterSensor(id) {\n      const sensor = this.sensors.get(id);\n      if (sensor !== undefined) {\n        sensor.stop();\n        this.sensors.delete(id);\n      }\n    }\n    subscribeForKeyboardEvents(_) {\n      if ((0, _PlatformChecker.isWeb)()) {\n        _index.logger.warn('useAnimatedKeyboard is not available on web yet.');\n      } else if ((0, _PlatformChecker.isJest)()) {\n        _index.logger.warn('useAnimatedKeyboard is not available when using Jest.');\n      } else if ((0, _PlatformChecker.isChromeDebugger)()) {\n        _index.logger.warn('useAnimatedKeyboard is not available when using Chrome Debugger.');\n      } else {\n        _index.logger.warn('useAnimatedKeyboard is not available on this configuration.');\n      }\n      return -1;\n    }\n    unsubscribeFromKeyboardEvents(_) {\n      // noop\n    }\n    initializeSensor(sensorType, interval) {\n      const config = interval <= 0 ? {\n        referenceFrame: 'device'\n      } : {\n        frequency: 1000 / interval\n      };\n      switch (sensorType) {\n        case _commonTypes.SensorType.ACCELEROMETER:\n          return new window.Accelerometer(config);\n        case _commonTypes.SensorType.GYROSCOPE:\n          return new window.Gyroscope(config);\n        case _commonTypes.SensorType.GRAVITY:\n          return new window.GravitySensor(config);\n        case _commonTypes.SensorType.MAGNETIC_FIELD:\n          return new window.Magnetometer(config);\n        case _commonTypes.SensorType.ROTATION:\n          return new window.AbsoluteOrientationSensor(config);\n      }\n    }\n    getSensorName(sensorType) {\n      switch (sensorType) {\n        case _commonTypes.SensorType.ACCELEROMETER:\n          return 'Accelerometer';\n        case _commonTypes.SensorType.GRAVITY:\n          return 'GravitySensor';\n        case _commonTypes.SensorType.GYROSCOPE:\n          return 'Gyroscope';\n        case _commonTypes.SensorType.MAGNETIC_FIELD:\n          return 'Magnetometer';\n        case _commonTypes.SensorType.ROTATION:\n          return 'AbsoluteOrientationSensor';\n      }\n    }\n    detectPlatform() {\n      const userAgent = navigator.userAgent || navigator.vendor || window.opera;\n      if (userAgent === undefined) {\n        this.platform = Platform.UNKNOWN;\n      } else if (/iPad|iPhone|iPod/.test(userAgent)) {\n        this.platform = Platform.WEB_IOS;\n      } else if (/android/i.test(userAgent)) {\n        this.platform = Platform.WEB_ANDROID;\n      } else {\n        this.platform = Platform.WEB;\n      }\n    }\n    getViewProp(_viewTag, _propName, _component, _callback) {\n      throw new _errors.ReanimatedError('getViewProp is not available in JSReanimated.');\n    }\n    configureProps() {\n      throw new _errors.ReanimatedError('configureProps is not available in JSReanimated.');\n    }\n    executeOnUIRuntimeSync(_shareable) {\n      throw new _errors.ReanimatedError('`executeOnUIRuntimeSync` is not available in JSReanimated.');\n    }\n    markNodeAsRemovable(_shadowNodeWrapper) {\n      throw new _errors.ReanimatedError('markNodeAsRemovable is not available in JSReanimated.');\n    }\n    unmarkNodeAsRemovable(_viewTag) {\n      throw new _errors.ReanimatedError('unmarkNodeAsRemovable is not available in JSReanimated.');\n    }\n  }\n\n  // Lack of this export breaks TypeScript generation since\n  // an enum transpiles into JavaScript code.\n  // ts-prune-ignore-next\n  let Platform = exports.Platform = /*#__PURE__*/function (Platform) {\n    Platform[\"WEB_IOS\"] = \"web iOS\";\n    Platform[\"WEB_ANDROID\"] = \"web Android\";\n    Platform[\"WEB\"] = \"web\";\n    Platform[\"UNKNOWN\"] = \"unknown\";\n    return Platform;\n  }({});\n});", "lineCount": 248, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "Platform"], [7, 18, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createJSReanimatedModule"], [8, 34, 1, 13], [8, 37, 1, 13, "createJSReanimatedModule"], [8, 61, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_commonTypes"], [9, 18, 3, 0], [9, 21, 3, 0, "require"], [9, 28, 3, 0], [9, 29, 3, 0, "_dependencyMap"], [9, 43, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_errors"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_index"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_mockedRequestAnimationFrame"], [12, 34, 6, 0], [12, 37, 6, 0, "require"], [12, 44, 6, 0], [12, 45, 6, 0, "_dependencyMap"], [12, 59, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_PlatformChecker"], [13, 22, 7, 0], [13, 25, 7, 0, "require"], [13, 32, 7, 0], [13, 33, 7, 0, "_dependencyMap"], [13, 47, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_index2"], [14, 13, 8, 0], [14, 16, 8, 0, "require"], [14, 23, 8, 0], [14, 24, 8, 0, "_dependencyMap"], [14, 38, 8, 0], [15, 2, 9, 7], [15, 11, 9, 16, "createJSReanimatedModule"], [15, 35, 9, 40, "createJSReanimatedModule"], [15, 36, 9, 40], [15, 38, 9, 43], [16, 4, 10, 2], [16, 11, 10, 9], [16, 15, 10, 13, "JSReanimated"], [16, 27, 10, 25], [16, 28, 10, 26], [16, 29, 10, 27], [17, 2, 11, 0], [19, 2, 13, 0], [20, 2, 14, 0], [21, 2, 15, 0], [22, 2, 16, 0], [22, 8, 16, 6, "requestAnimationFrameImpl"], [22, 33, 16, 31], [22, 36, 16, 34], [22, 40, 16, 34, "isJest"], [22, 63, 16, 40], [22, 65, 16, 41], [22, 66, 16, 42], [22, 70, 16, 46], [22, 71, 16, 47, "globalThis"], [22, 81, 16, 57], [22, 82, 16, 58, "requestAnimationFrame"], [22, 103, 16, 79], [22, 106, 16, 82, "mockedRequestAnimationFrame"], [22, 162, 16, 109], [22, 165, 16, 112, "globalThis"], [22, 175, 16, 122], [22, 176, 16, 123, "requestAnimationFrame"], [22, 197, 16, 144], [23, 2, 17, 0], [23, 8, 17, 6, "JSReanimated"], [23, 20, 17, 18], [23, 21, 17, 19], [24, 4, 18, 2], [25, 0, 19, 0], [26, 0, 20, 0], [27, 0, 21, 0], [28, 4, 22, 2], [28, 5, 22, 3, "workletsModule"], [28, 19, 22, 17], [28, 22, 22, 20, "WorkletsModule"], [28, 44, 22, 34], [29, 4, 23, 2, "nextSensorId"], [29, 16, 23, 14], [29, 19, 23, 17], [29, 20, 23, 18], [30, 4, 24, 2, "sensors"], [30, 11, 24, 9], [30, 14, 24, 12], [30, 18, 24, 16, "Map"], [30, 21, 24, 19], [30, 22, 24, 20], [30, 23, 24, 21], [31, 4, 25, 2, "platform"], [31, 12, 25, 10], [31, 15, 25, 13, "undefined"], [31, 24, 25, 22], [32, 4, 26, 2, "scheduleOnUI"], [32, 16, 26, 14, "scheduleOnUI"], [32, 17, 26, 15, "worklet"], [32, 24, 26, 22], [32, 26, 26, 24], [33, 6, 27, 4], [34, 6, 28, 4, "requestAnimationFrameImpl"], [34, 31, 28, 29], [34, 32, 28, 30, "worklet"], [34, 39, 28, 37], [34, 40, 28, 38], [35, 4, 29, 2], [36, 4, 30, 2, "createWorkletRuntime"], [36, 24, 30, 22, "createWorkletRuntime"], [36, 25, 30, 23, "_name"], [36, 30, 30, 28], [36, 32, 30, 30, "_initializer"], [36, 44, 30, 42], [36, 46, 30, 44], [37, 6, 31, 4], [37, 12, 31, 10], [37, 16, 31, 14, "ReanimatedError"], [37, 39, 31, 29], [37, 40, 31, 30], [37, 96, 31, 86], [37, 97, 31, 87], [38, 4, 32, 2], [39, 4, 33, 2, "scheduleOnRuntime"], [39, 21, 33, 19, "scheduleOnRuntime"], [39, 22, 33, 19], [39, 24, 33, 22], [40, 6, 34, 4], [40, 12, 34, 10], [40, 16, 34, 14, "ReanimatedError"], [40, 39, 34, 29], [40, 40, 34, 30], [40, 93, 34, 83], [40, 94, 34, 84], [41, 4, 35, 2], [42, 4, 36, 2, "registerEventHandler"], [42, 24, 36, 22, "registerEventHandler"], [42, 25, 36, 23, "_event<PERSON><PERSON><PERSON>"], [42, 38, 36, 36], [42, 40, 36, 38, "_eventName"], [42, 50, 36, 48], [42, 52, 36, 50, "_emitterReactTag"], [42, 68, 36, 66], [42, 70, 36, 68], [43, 6, 37, 4], [43, 12, 37, 10], [43, 16, 37, 14, "ReanimatedError"], [43, 39, 37, 29], [43, 40, 37, 30], [43, 96, 37, 86], [43, 97, 37, 87], [44, 4, 38, 2], [45, 4, 39, 2, "unregisterEventHandler"], [45, 26, 39, 24, "unregisterEventHandler"], [45, 27, 39, 25, "_"], [45, 28, 39, 26], [45, 30, 39, 28], [46, 6, 40, 4], [46, 12, 40, 10], [46, 16, 40, 14, "ReanimatedError"], [46, 39, 40, 29], [46, 40, 40, 30], [46, 98, 40, 88], [46, 99, 40, 89], [47, 4, 41, 2], [48, 4, 42, 2, "enableLayoutAnimations"], [48, 26, 42, 24, "enableLayoutAnimations"], [48, 27, 42, 24], [48, 29, 42, 27], [49, 6, 43, 4], [49, 10, 43, 8], [49, 14, 43, 8, "isWeb"], [49, 36, 43, 13], [49, 38, 43, 14], [49, 39, 43, 15], [49, 41, 43, 17], [50, 8, 44, 6, "logger"], [50, 21, 44, 12], [50, 22, 44, 13, "warn"], [50, 26, 44, 17], [50, 27, 44, 18], [50, 76, 44, 67], [50, 77, 44, 68], [51, 6, 45, 4], [51, 7, 45, 5], [51, 13, 45, 11], [51, 17, 45, 15], [51, 21, 45, 15, "isJest"], [51, 44, 45, 21], [51, 46, 45, 22], [51, 47, 45, 23], [51, 49, 45, 25], [52, 8, 46, 6, "logger"], [52, 21, 46, 12], [52, 22, 46, 13, "warn"], [52, 26, 46, 17], [52, 27, 46, 18], [52, 74, 46, 65], [52, 75, 46, 66], [53, 6, 47, 4], [53, 7, 47, 5], [53, 13, 47, 11], [53, 17, 47, 15], [53, 21, 47, 15, "isChromeDebugger"], [53, 54, 47, 31], [53, 56, 47, 32], [53, 57, 47, 33], [53, 59, 47, 35], [54, 8, 48, 6, "logger"], [54, 21, 48, 12], [54, 22, 48, 13, "warn"], [54, 26, 48, 17], [54, 27, 48, 18], [54, 85, 48, 76], [54, 86, 48, 77], [55, 6, 49, 4], [55, 7, 49, 5], [55, 13, 49, 11], [56, 8, 50, 6, "logger"], [56, 21, 50, 12], [56, 22, 50, 13, "warn"], [56, 26, 50, 17], [56, 27, 50, 18], [56, 87, 50, 78], [56, 88, 50, 79], [57, 6, 51, 4], [58, 4, 52, 2], [59, 4, 53, 2, "configureLayoutAnimationBatch"], [59, 33, 53, 31, "configureLayoutAnimationBatch"], [59, 34, 53, 31], [59, 36, 53, 34], [60, 6, 54, 4], [61, 4, 54, 4], [62, 4, 56, 2, "setShouldAnimateExitingForTag"], [62, 33, 56, 31, "setShouldAnimateExitingForTag"], [62, 34, 56, 31], [62, 36, 56, 34], [63, 6, 57, 4], [64, 4, 57, 4], [65, 4, 59, 2, "registerSensor"], [65, 18, 59, 16, "registerSensor"], [65, 19, 59, 17, "sensorType"], [65, 29, 59, 27], [65, 31, 59, 29, "interval"], [65, 39, 59, 37], [65, 41, 59, 39, "_iosReferenceFrame"], [65, 59, 59, 57], [65, 61, 59, 59, "<PERSON><PERSON><PERSON><PERSON>"], [65, 73, 59, 71], [65, 75, 59, 73], [66, 6, 60, 4], [66, 10, 60, 8], [66, 11, 60, 9], [66, 15, 60, 9, "isWindowAvailable"], [66, 49, 60, 26], [66, 51, 60, 27], [66, 52, 60, 28], [66, 54, 60, 30], [67, 8, 61, 6], [68, 8, 62, 6], [69, 8, 63, 6], [69, 15, 63, 13], [69, 16, 63, 14], [69, 17, 63, 15], [70, 6, 64, 4], [71, 6, 65, 4], [71, 10, 65, 8], [71, 14, 65, 12], [71, 15, 65, 13, "platform"], [71, 23, 65, 21], [71, 28, 65, 26, "undefined"], [71, 37, 65, 35], [71, 39, 65, 37], [72, 8, 66, 6], [72, 12, 66, 10], [72, 13, 66, 11, "detectPlatform"], [72, 27, 66, 25], [72, 28, 66, 26], [72, 29, 66, 27], [73, 6, 67, 4], [74, 6, 68, 4], [74, 10, 68, 8], [74, 12, 68, 10], [74, 16, 68, 14], [74, 17, 68, 15, "getSensorName"], [74, 30, 68, 28], [74, 31, 68, 29, "sensorType"], [74, 41, 68, 39], [74, 42, 68, 40], [74, 46, 68, 44, "window"], [74, 52, 68, 50], [74, 53, 68, 51], [74, 55, 68, 53], [75, 8, 69, 6], [76, 8, 70, 6, "logger"], [76, 21, 70, 12], [76, 22, 70, 13, "warn"], [76, 26, 70, 17], [76, 27, 70, 18], [76, 53, 70, 44], [76, 57, 70, 48], [76, 61, 70, 48, "isWeb"], [76, 83, 70, 53], [76, 85, 70, 54], [76, 86, 70, 55], [76, 90, 70, 59, "location"], [76, 98, 70, 67], [76, 99, 70, 68, "protocol"], [76, 107, 70, 76], [76, 112, 70, 81], [76, 120, 70, 89], [76, 123, 70, 92], [76, 194, 70, 163], [76, 197, 70, 166], [76, 199, 70, 168], [76, 200, 70, 169], [76, 204, 70, 173], [76, 208, 70, 177], [76, 209, 70, 178, "platform"], [76, 217, 70, 186], [76, 222, 70, 191, "Platform"], [76, 230, 70, 199], [76, 231, 70, 200, "WEB_IOS"], [76, 238, 70, 207], [76, 241, 70, 210], [76, 421, 70, 390], [76, 424, 70, 393], [76, 426, 70, 395], [76, 427, 70, 396], [76, 428, 70, 397], [77, 8, 71, 6], [77, 15, 71, 13], [77, 16, 71, 14], [77, 17, 71, 15], [78, 6, 72, 4], [79, 6, 73, 4], [79, 10, 73, 8], [79, 14, 73, 12], [79, 15, 73, 13, "platform"], [79, 23, 73, 21], [79, 28, 73, 26, "undefined"], [79, 37, 73, 35], [79, 39, 73, 37], [80, 8, 74, 6], [80, 12, 74, 10], [80, 13, 74, 11, "detectPlatform"], [80, 27, 74, 25], [80, 28, 74, 26], [80, 29, 74, 27], [81, 6, 75, 4], [82, 6, 76, 4], [82, 12, 76, 10, "sensor"], [82, 18, 76, 16], [82, 21, 76, 19], [82, 25, 76, 23], [82, 26, 76, 24, "initializeSensor"], [82, 42, 76, 40], [82, 43, 76, 41, "sensorType"], [82, 53, 76, 51], [82, 55, 76, 53, "interval"], [82, 63, 76, 61], [82, 64, 76, 62], [83, 6, 77, 4, "sensor"], [83, 12, 77, 10], [83, 13, 77, 11, "addEventListener"], [83, 29, 77, 27], [83, 30, 77, 28], [83, 39, 77, 37], [83, 41, 77, 39], [83, 45, 77, 43], [83, 46, 77, 44, "getSensorCallback"], [83, 63, 77, 61], [83, 64, 77, 62, "sensor"], [83, 70, 77, 68], [83, 72, 77, 70, "sensorType"], [83, 82, 77, 80], [83, 84, 77, 82, "<PERSON><PERSON><PERSON><PERSON>"], [83, 96, 77, 94], [83, 97, 77, 95], [83, 98, 77, 96], [84, 6, 78, 4, "sensor"], [84, 12, 78, 10], [84, 13, 78, 11, "start"], [84, 18, 78, 16], [84, 19, 78, 17], [84, 20, 78, 18], [85, 6, 79, 4], [85, 10, 79, 8], [85, 11, 79, 9, "sensors"], [85, 18, 79, 16], [85, 19, 79, 17, "set"], [85, 22, 79, 20], [85, 23, 79, 21], [85, 27, 79, 25], [85, 28, 79, 26, "nextSensorId"], [85, 40, 79, 38], [85, 42, 79, 40, "sensor"], [85, 48, 79, 46], [85, 49, 79, 47], [86, 6, 80, 4], [86, 13, 80, 11], [86, 17, 80, 15], [86, 18, 80, 16, "nextSensorId"], [86, 30, 80, 28], [86, 32, 80, 30], [87, 4, 81, 2], [88, 4, 82, 2, "getSensorCallback"], [88, 21, 82, 19], [88, 24, 82, 22, "getSensorCallback"], [88, 25, 82, 23, "sensor"], [88, 31, 82, 29], [88, 33, 82, 31, "sensorType"], [88, 43, 82, 41], [88, 45, 82, 43, "<PERSON><PERSON><PERSON><PERSON>"], [88, 57, 82, 55], [88, 62, 82, 60], [89, 6, 83, 4], [89, 14, 83, 12, "sensorType"], [89, 24, 83, 22], [90, 8, 84, 6], [90, 13, 84, 11, "SensorType"], [90, 36, 84, 21], [90, 37, 84, 22, "ACCELEROMETER"], [90, 50, 84, 35], [91, 8, 85, 6], [91, 13, 85, 11, "SensorType"], [91, 36, 85, 21], [91, 37, 85, 22, "GRAVITY"], [91, 44, 85, 29], [92, 10, 86, 8], [92, 17, 86, 15], [92, 23, 86, 21], [93, 12, 87, 10], [93, 16, 87, 14], [94, 14, 88, 12, "x"], [94, 15, 88, 13], [95, 14, 89, 12, "y"], [95, 15, 89, 13], [96, 14, 90, 12, "z"], [97, 12, 91, 10], [97, 13, 91, 11], [97, 16, 91, 14, "sensor"], [97, 22, 91, 20], [99, 12, 93, 10], [100, 12, 94, 10], [100, 16, 94, 14], [100, 20, 94, 18], [100, 21, 94, 19, "platform"], [100, 29, 94, 27], [100, 34, 94, 32, "Platform"], [100, 42, 94, 40], [100, 43, 94, 41, "WEB_ANDROID"], [100, 54, 94, 52], [100, 56, 94, 54], [101, 14, 95, 12], [101, 15, 95, 13, "x"], [101, 16, 95, 14], [101, 18, 95, 16, "y"], [101, 19, 95, 17], [101, 21, 95, 19, "z"], [101, 22, 95, 20], [101, 23, 95, 21], [101, 26, 95, 24], [101, 27, 95, 25], [101, 28, 95, 26, "x"], [101, 29, 95, 27], [101, 31, 95, 29], [101, 32, 95, 30, "y"], [101, 33, 95, 31], [101, 35, 95, 33], [101, 36, 95, 34, "z"], [101, 37, 95, 35], [101, 38, 95, 36], [102, 12, 96, 10], [103, 12, 97, 10], [104, 12, 98, 10, "<PERSON><PERSON><PERSON><PERSON>"], [104, 24, 98, 22], [104, 25, 98, 23], [105, 14, 99, 12, "x"], [105, 15, 99, 13], [106, 14, 100, 12, "y"], [106, 15, 100, 13], [107, 14, 101, 12, "z"], [107, 15, 101, 13], [108, 14, 102, 12, "interfaceOrientation"], [108, 34, 102, 32], [108, 36, 102, 34], [109, 12, 103, 10], [109, 13, 103, 11], [109, 14, 103, 12], [110, 10, 104, 8], [110, 11, 104, 9], [111, 8, 105, 6], [111, 13, 105, 11, "SensorType"], [111, 36, 105, 21], [111, 37, 105, 22, "GYROSCOPE"], [111, 46, 105, 31], [112, 8, 106, 6], [112, 13, 106, 11, "SensorType"], [112, 36, 106, 21], [112, 37, 106, 22, "MAGNETIC_FIELD"], [112, 51, 106, 36], [113, 10, 107, 8], [113, 17, 107, 15], [113, 23, 107, 21], [114, 12, 108, 10], [114, 18, 108, 16], [115, 14, 109, 12, "x"], [115, 15, 109, 13], [116, 14, 110, 12, "y"], [116, 15, 110, 13], [117, 14, 111, 12, "z"], [118, 12, 112, 10], [118, 13, 112, 11], [118, 16, 112, 14, "sensor"], [118, 22, 112, 20], [119, 12, 113, 10], [120, 12, 114, 10, "<PERSON><PERSON><PERSON><PERSON>"], [120, 24, 114, 22], [120, 25, 114, 23], [121, 14, 115, 12, "x"], [121, 15, 115, 13], [122, 14, 116, 12, "y"], [122, 15, 116, 13], [123, 14, 117, 12, "z"], [123, 15, 117, 13], [124, 14, 118, 12, "interfaceOrientation"], [124, 34, 118, 32], [124, 36, 118, 34], [125, 12, 119, 10], [125, 13, 119, 11], [125, 14, 119, 12], [126, 10, 120, 8], [126, 11, 120, 9], [127, 8, 121, 6], [127, 13, 121, 11, "SensorType"], [127, 36, 121, 21], [127, 37, 121, 22, "ROTATION"], [127, 45, 121, 30], [128, 10, 122, 8], [128, 17, 122, 15], [128, 23, 122, 21], [129, 12, 123, 10], [129, 16, 123, 14], [129, 17, 123, 15, "qw"], [129, 19, 123, 17], [129, 21, 123, 19, "qx"], [129, 23, 123, 21], [129, 25, 123, 23, "qy"], [129, 27, 123, 25], [129, 29, 123, 27, "qz"], [129, 31, 123, 29], [129, 32, 123, 30], [129, 35, 123, 33, "sensor"], [129, 41, 123, 39], [129, 42, 123, 40, "quaternion"], [129, 52, 123, 50], [131, 12, 125, 10], [132, 12, 126, 10], [132, 16, 126, 14], [132, 20, 126, 18], [132, 21, 126, 19, "platform"], [132, 29, 126, 27], [132, 34, 126, 32, "Platform"], [132, 42, 126, 40], [132, 43, 126, 41, "WEB_ANDROID"], [132, 54, 126, 52], [132, 56, 126, 54], [133, 14, 127, 12], [133, 15, 127, 13, "qy"], [133, 17, 127, 15], [133, 19, 127, 17, "qz"], [133, 21, 127, 19], [133, 22, 127, 20], [133, 25, 127, 23], [133, 26, 127, 24, "qz"], [133, 28, 127, 26], [133, 30, 127, 28], [133, 31, 127, 29, "qy"], [133, 33, 127, 31], [133, 34, 127, 32], [134, 12, 128, 10], [136, 12, 130, 10], [137, 12, 131, 10], [137, 18, 131, 16, "yaw"], [137, 21, 131, 19], [137, 24, 131, 22], [137, 25, 131, 23, "Math"], [137, 29, 131, 27], [137, 30, 131, 28, "atan2"], [137, 35, 131, 33], [137, 36, 131, 34], [137, 39, 131, 37], [137, 43, 131, 41, "qy"], [137, 45, 131, 43], [137, 48, 131, 46, "qz"], [137, 50, 131, 48], [137, 53, 131, 51, "qw"], [137, 55, 131, 53], [137, 58, 131, 56, "qx"], [137, 60, 131, 58], [137, 61, 131, 59], [137, 63, 131, 61, "qw"], [137, 65, 131, 63], [137, 68, 131, 66, "qw"], [137, 70, 131, 68], [137, 73, 131, 71, "qx"], [137, 75, 131, 73], [137, 78, 131, 76, "qx"], [137, 80, 131, 78], [137, 83, 131, 81, "qy"], [137, 85, 131, 83], [137, 88, 131, 86, "qy"], [137, 90, 131, 88], [137, 93, 131, 91, "qz"], [137, 95, 131, 93], [137, 98, 131, 96, "qz"], [137, 100, 131, 98], [137, 101, 131, 99], [138, 12, 132, 10], [138, 18, 132, 16, "pitch"], [138, 23, 132, 21], [138, 26, 132, 24, "Math"], [138, 30, 132, 28], [138, 31, 132, 29, "sin"], [138, 34, 132, 32], [138, 35, 132, 33], [138, 36, 132, 34], [138, 39, 132, 37], [138, 43, 132, 41, "qx"], [138, 45, 132, 43], [138, 48, 132, 46, "qz"], [138, 50, 132, 48], [138, 53, 132, 51, "qw"], [138, 55, 132, 53], [138, 58, 132, 56, "qy"], [138, 60, 132, 58], [138, 61, 132, 59], [138, 62, 132, 60], [139, 12, 133, 10], [139, 18, 133, 16, "roll"], [139, 22, 133, 20], [139, 25, 133, 23], [139, 26, 133, 24, "Math"], [139, 30, 133, 28], [139, 31, 133, 29, "atan2"], [139, 36, 133, 34], [139, 37, 133, 35], [139, 40, 133, 38], [139, 44, 133, 42, "qx"], [139, 46, 133, 44], [139, 49, 133, 47, "qy"], [139, 51, 133, 49], [139, 54, 133, 52, "qw"], [139, 56, 133, 54], [139, 59, 133, 57, "qz"], [139, 61, 133, 59], [139, 62, 133, 60], [139, 64, 133, 62, "qw"], [139, 66, 133, 64], [139, 69, 133, 67, "qw"], [139, 71, 133, 69], [139, 74, 133, 72, "qx"], [139, 76, 133, 74], [139, 79, 133, 77, "qx"], [139, 81, 133, 79], [139, 84, 133, 82, "qy"], [139, 86, 133, 84], [139, 89, 133, 87, "qy"], [139, 91, 133, 89], [139, 94, 133, 92, "qz"], [139, 96, 133, 94], [139, 99, 133, 97, "qz"], [139, 101, 133, 99], [139, 102, 133, 100], [140, 12, 134, 10], [141, 12, 135, 10, "<PERSON><PERSON><PERSON><PERSON>"], [141, 24, 135, 22], [141, 25, 135, 23], [142, 14, 136, 12, "qw"], [142, 16, 136, 14], [143, 14, 137, 12, "qx"], [143, 16, 137, 14], [144, 14, 138, 12, "qy"], [144, 16, 138, 14], [145, 14, 139, 12, "qz"], [145, 16, 139, 14], [146, 14, 140, 12, "yaw"], [146, 17, 140, 15], [147, 14, 141, 12, "pitch"], [147, 19, 141, 17], [148, 14, 142, 12, "roll"], [148, 18, 142, 16], [149, 14, 143, 12, "interfaceOrientation"], [149, 34, 143, 32], [149, 36, 143, 34], [150, 12, 144, 10], [150, 13, 144, 11], [150, 14, 144, 12], [151, 10, 145, 8], [151, 11, 145, 9], [152, 6, 146, 4], [153, 4, 147, 2], [153, 5, 147, 3], [154, 4, 148, 2, "unregisterSensor"], [154, 20, 148, 18, "unregisterSensor"], [154, 21, 148, 19, "id"], [154, 23, 148, 21], [154, 25, 148, 23], [155, 6, 149, 4], [155, 12, 149, 10, "sensor"], [155, 18, 149, 16], [155, 21, 149, 19], [155, 25, 149, 23], [155, 26, 149, 24, "sensors"], [155, 33, 149, 31], [155, 34, 149, 32, "get"], [155, 37, 149, 35], [155, 38, 149, 36, "id"], [155, 40, 149, 38], [155, 41, 149, 39], [156, 6, 150, 4], [156, 10, 150, 8, "sensor"], [156, 16, 150, 14], [156, 21, 150, 19, "undefined"], [156, 30, 150, 28], [156, 32, 150, 30], [157, 8, 151, 6, "sensor"], [157, 14, 151, 12], [157, 15, 151, 13, "stop"], [157, 19, 151, 17], [157, 20, 151, 18], [157, 21, 151, 19], [158, 8, 152, 6], [158, 12, 152, 10], [158, 13, 152, 11, "sensors"], [158, 20, 152, 18], [158, 21, 152, 19, "delete"], [158, 27, 152, 25], [158, 28, 152, 26, "id"], [158, 30, 152, 28], [158, 31, 152, 29], [159, 6, 153, 4], [160, 4, 154, 2], [161, 4, 155, 2, "subscribeForKeyboardEvents"], [161, 30, 155, 28, "subscribeForKeyboardEvents"], [161, 31, 155, 29, "_"], [161, 32, 155, 30], [161, 34, 155, 32], [162, 6, 156, 4], [162, 10, 156, 8], [162, 14, 156, 8, "isWeb"], [162, 36, 156, 13], [162, 38, 156, 14], [162, 39, 156, 15], [162, 41, 156, 17], [163, 8, 157, 6, "logger"], [163, 21, 157, 12], [163, 22, 157, 13, "warn"], [163, 26, 157, 17], [163, 27, 157, 18], [163, 77, 157, 68], [163, 78, 157, 69], [164, 6, 158, 4], [164, 7, 158, 5], [164, 13, 158, 11], [164, 17, 158, 15], [164, 21, 158, 15, "isJest"], [164, 44, 158, 21], [164, 46, 158, 22], [164, 47, 158, 23], [164, 49, 158, 25], [165, 8, 159, 6, "logger"], [165, 21, 159, 12], [165, 22, 159, 13, "warn"], [165, 26, 159, 17], [165, 27, 159, 18], [165, 82, 159, 73], [165, 83, 159, 74], [166, 6, 160, 4], [166, 7, 160, 5], [166, 13, 160, 11], [166, 17, 160, 15], [166, 21, 160, 15, "isChromeDebugger"], [166, 54, 160, 31], [166, 56, 160, 32], [166, 57, 160, 33], [166, 59, 160, 35], [167, 8, 161, 6, "logger"], [167, 21, 161, 12], [167, 22, 161, 13, "warn"], [167, 26, 161, 17], [167, 27, 161, 18], [167, 93, 161, 84], [167, 94, 161, 85], [168, 6, 162, 4], [168, 7, 162, 5], [168, 13, 162, 11], [169, 8, 163, 6, "logger"], [169, 21, 163, 12], [169, 22, 163, 13, "warn"], [169, 26, 163, 17], [169, 27, 163, 18], [169, 88, 163, 79], [169, 89, 163, 80], [170, 6, 164, 4], [171, 6, 165, 4], [171, 13, 165, 11], [171, 14, 165, 12], [171, 15, 165, 13], [172, 4, 166, 2], [173, 4, 167, 2, "unsubscribeFromKeyboardEvents"], [173, 33, 167, 31, "unsubscribeFromKeyboardEvents"], [173, 34, 167, 32, "_"], [173, 35, 167, 33], [173, 37, 167, 35], [174, 6, 168, 4], [175, 4, 168, 4], [176, 4, 170, 2, "initializeSensor"], [176, 20, 170, 18, "initializeSensor"], [176, 21, 170, 19, "sensorType"], [176, 31, 170, 29], [176, 33, 170, 31, "interval"], [176, 41, 170, 39], [176, 43, 170, 41], [177, 6, 171, 4], [177, 12, 171, 10, "config"], [177, 18, 171, 16], [177, 21, 171, 19, "interval"], [177, 29, 171, 27], [177, 33, 171, 31], [177, 34, 171, 32], [177, 37, 171, 35], [178, 8, 172, 6, "referenceFrame"], [178, 22, 172, 20], [178, 24, 172, 22], [179, 6, 173, 4], [179, 7, 173, 5], [179, 10, 173, 8], [180, 8, 174, 6, "frequency"], [180, 17, 174, 15], [180, 19, 174, 17], [180, 23, 174, 21], [180, 26, 174, 24, "interval"], [181, 6, 175, 4], [181, 7, 175, 5], [182, 6, 176, 4], [182, 14, 176, 12, "sensorType"], [182, 24, 176, 22], [183, 8, 177, 6], [183, 13, 177, 11, "SensorType"], [183, 36, 177, 21], [183, 37, 177, 22, "ACCELEROMETER"], [183, 50, 177, 35], [184, 10, 178, 8], [184, 17, 178, 15], [184, 21, 178, 19, "window"], [184, 27, 178, 25], [184, 28, 178, 26, "Accelerometer"], [184, 41, 178, 39], [184, 42, 178, 40, "config"], [184, 48, 178, 46], [184, 49, 178, 47], [185, 8, 179, 6], [185, 13, 179, 11, "SensorType"], [185, 36, 179, 21], [185, 37, 179, 22, "GYROSCOPE"], [185, 46, 179, 31], [186, 10, 180, 8], [186, 17, 180, 15], [186, 21, 180, 19, "window"], [186, 27, 180, 25], [186, 28, 180, 26, "Gyroscope"], [186, 37, 180, 35], [186, 38, 180, 36, "config"], [186, 44, 180, 42], [186, 45, 180, 43], [187, 8, 181, 6], [187, 13, 181, 11, "SensorType"], [187, 36, 181, 21], [187, 37, 181, 22, "GRAVITY"], [187, 44, 181, 29], [188, 10, 182, 8], [188, 17, 182, 15], [188, 21, 182, 19, "window"], [188, 27, 182, 25], [188, 28, 182, 26, "GravitySensor"], [188, 41, 182, 39], [188, 42, 182, 40, "config"], [188, 48, 182, 46], [188, 49, 182, 47], [189, 8, 183, 6], [189, 13, 183, 11, "SensorType"], [189, 36, 183, 21], [189, 37, 183, 22, "MAGNETIC_FIELD"], [189, 51, 183, 36], [190, 10, 184, 8], [190, 17, 184, 15], [190, 21, 184, 19, "window"], [190, 27, 184, 25], [190, 28, 184, 26, "Magnetometer"], [190, 40, 184, 38], [190, 41, 184, 39, "config"], [190, 47, 184, 45], [190, 48, 184, 46], [191, 8, 185, 6], [191, 13, 185, 11, "SensorType"], [191, 36, 185, 21], [191, 37, 185, 22, "ROTATION"], [191, 45, 185, 30], [192, 10, 186, 8], [192, 17, 186, 15], [192, 21, 186, 19, "window"], [192, 27, 186, 25], [192, 28, 186, 26, "AbsoluteOrientationSensor"], [192, 53, 186, 51], [192, 54, 186, 52, "config"], [192, 60, 186, 58], [192, 61, 186, 59], [193, 6, 187, 4], [194, 4, 188, 2], [195, 4, 189, 2, "getSensorName"], [195, 17, 189, 15, "getSensorName"], [195, 18, 189, 16, "sensorType"], [195, 28, 189, 26], [195, 30, 189, 28], [196, 6, 190, 4], [196, 14, 190, 12, "sensorType"], [196, 24, 190, 22], [197, 8, 191, 6], [197, 13, 191, 11, "SensorType"], [197, 36, 191, 21], [197, 37, 191, 22, "ACCELEROMETER"], [197, 50, 191, 35], [198, 10, 192, 8], [198, 17, 192, 15], [198, 32, 192, 30], [199, 8, 193, 6], [199, 13, 193, 11, "SensorType"], [199, 36, 193, 21], [199, 37, 193, 22, "GRAVITY"], [199, 44, 193, 29], [200, 10, 194, 8], [200, 17, 194, 15], [200, 32, 194, 30], [201, 8, 195, 6], [201, 13, 195, 11, "SensorType"], [201, 36, 195, 21], [201, 37, 195, 22, "GYROSCOPE"], [201, 46, 195, 31], [202, 10, 196, 8], [202, 17, 196, 15], [202, 28, 196, 26], [203, 8, 197, 6], [203, 13, 197, 11, "SensorType"], [203, 36, 197, 21], [203, 37, 197, 22, "MAGNETIC_FIELD"], [203, 51, 197, 36], [204, 10, 198, 8], [204, 17, 198, 15], [204, 31, 198, 29], [205, 8, 199, 6], [205, 13, 199, 11, "SensorType"], [205, 36, 199, 21], [205, 37, 199, 22, "ROTATION"], [205, 45, 199, 30], [206, 10, 200, 8], [206, 17, 200, 15], [206, 44, 200, 42], [207, 6, 201, 4], [208, 4, 202, 2], [209, 4, 203, 2, "detectPlatform"], [209, 18, 203, 16, "detectPlatform"], [209, 19, 203, 16], [209, 21, 203, 19], [210, 6, 204, 4], [210, 12, 204, 10, "userAgent"], [210, 21, 204, 19], [210, 24, 204, 22, "navigator"], [210, 33, 204, 31], [210, 34, 204, 32, "userAgent"], [210, 43, 204, 41], [210, 47, 204, 45, "navigator"], [210, 56, 204, 54], [210, 57, 204, 55, "vendor"], [210, 63, 204, 61], [210, 67, 204, 65, "window"], [210, 73, 204, 71], [210, 74, 204, 72, "opera"], [210, 79, 204, 77], [211, 6, 205, 4], [211, 10, 205, 8, "userAgent"], [211, 19, 205, 17], [211, 24, 205, 22, "undefined"], [211, 33, 205, 31], [211, 35, 205, 33], [212, 8, 206, 6], [212, 12, 206, 10], [212, 13, 206, 11, "platform"], [212, 21, 206, 19], [212, 24, 206, 22, "Platform"], [212, 32, 206, 30], [212, 33, 206, 31, "UNKNOWN"], [212, 40, 206, 38], [213, 6, 207, 4], [213, 7, 207, 5], [213, 13, 207, 11], [213, 17, 207, 15], [213, 35, 207, 33], [213, 36, 207, 34, "test"], [213, 40, 207, 38], [213, 41, 207, 39, "userAgent"], [213, 50, 207, 48], [213, 51, 207, 49], [213, 53, 207, 51], [214, 8, 208, 6], [214, 12, 208, 10], [214, 13, 208, 11, "platform"], [214, 21, 208, 19], [214, 24, 208, 22, "Platform"], [214, 32, 208, 30], [214, 33, 208, 31, "WEB_IOS"], [214, 40, 208, 38], [215, 6, 209, 4], [215, 7, 209, 5], [215, 13, 209, 11], [215, 17, 209, 15], [215, 27, 209, 25], [215, 28, 209, 26, "test"], [215, 32, 209, 30], [215, 33, 209, 31, "userAgent"], [215, 42, 209, 40], [215, 43, 209, 41], [215, 45, 209, 43], [216, 8, 210, 6], [216, 12, 210, 10], [216, 13, 210, 11, "platform"], [216, 21, 210, 19], [216, 24, 210, 22, "Platform"], [216, 32, 210, 30], [216, 33, 210, 31, "WEB_ANDROID"], [216, 44, 210, 42], [217, 6, 211, 4], [217, 7, 211, 5], [217, 13, 211, 11], [218, 8, 212, 6], [218, 12, 212, 10], [218, 13, 212, 11, "platform"], [218, 21, 212, 19], [218, 24, 212, 22, "Platform"], [218, 32, 212, 30], [218, 33, 212, 31, "WEB"], [218, 36, 212, 34], [219, 6, 213, 4], [220, 4, 214, 2], [221, 4, 215, 2, "getViewProp"], [221, 15, 215, 13, "getViewProp"], [221, 16, 215, 14, "_viewTag"], [221, 24, 215, 22], [221, 26, 215, 24, "_propName"], [221, 35, 215, 33], [221, 37, 215, 35, "_component"], [221, 47, 215, 45], [221, 49, 215, 47, "_callback"], [221, 58, 215, 56], [221, 60, 215, 58], [222, 6, 216, 4], [222, 12, 216, 10], [222, 16, 216, 14, "ReanimatedError"], [222, 39, 216, 29], [222, 40, 216, 30], [222, 87, 216, 77], [222, 88, 216, 78], [223, 4, 217, 2], [224, 4, 218, 2, "configureProps"], [224, 18, 218, 16, "configureProps"], [224, 19, 218, 16], [224, 21, 218, 19], [225, 6, 219, 4], [225, 12, 219, 10], [225, 16, 219, 14, "ReanimatedError"], [225, 39, 219, 29], [225, 40, 219, 30], [225, 90, 219, 80], [225, 91, 219, 81], [226, 4, 220, 2], [227, 4, 221, 2, "executeOnUIRuntimeSync"], [227, 26, 221, 24, "executeOnUIRuntimeSync"], [227, 27, 221, 25, "_shareable"], [227, 37, 221, 35], [227, 39, 221, 37], [228, 6, 222, 4], [228, 12, 222, 10], [228, 16, 222, 14, "ReanimatedError"], [228, 39, 222, 29], [228, 40, 222, 30], [228, 100, 222, 90], [228, 101, 222, 91], [229, 4, 223, 2], [230, 4, 224, 2, "markNodeAsRemovable"], [230, 23, 224, 21, "markNodeAsRemovable"], [230, 24, 224, 22, "_shadowNodeWrapper"], [230, 42, 224, 40], [230, 44, 224, 42], [231, 6, 225, 4], [231, 12, 225, 10], [231, 16, 225, 14, "ReanimatedError"], [231, 39, 225, 29], [231, 40, 225, 30], [231, 95, 225, 85], [231, 96, 225, 86], [232, 4, 226, 2], [233, 4, 227, 2, "unmarkNodeAsRemovable"], [233, 25, 227, 23, "unmarkNodeAsRemovable"], [233, 26, 227, 24, "_viewTag"], [233, 34, 227, 32], [233, 36, 227, 34], [234, 6, 228, 4], [234, 12, 228, 10], [234, 16, 228, 14, "ReanimatedError"], [234, 39, 228, 29], [234, 40, 228, 30], [234, 97, 228, 87], [234, 98, 228, 88], [235, 4, 229, 2], [236, 2, 230, 0], [238, 2, 232, 0], [239, 2, 233, 0], [240, 2, 234, 0], [241, 2, 235, 7], [241, 6, 235, 11, "Platform"], [241, 14, 235, 19], [241, 17, 235, 19, "exports"], [241, 24, 235, 19], [241, 25, 235, 19, "Platform"], [241, 33, 235, 19], [241, 36, 235, 22], [241, 49, 235, 35], [241, 59, 235, 45, "Platform"], [241, 67, 235, 53], [241, 69, 235, 55], [242, 4, 236, 2, "Platform"], [242, 12, 236, 10], [242, 13, 236, 11], [242, 22, 236, 20], [242, 23, 236, 21], [242, 26, 236, 24], [242, 35, 236, 33], [243, 4, 237, 2, "Platform"], [243, 12, 237, 10], [243, 13, 237, 11], [243, 26, 237, 24], [243, 27, 237, 25], [243, 30, 237, 28], [243, 43, 237, 41], [244, 4, 238, 2, "Platform"], [244, 12, 238, 10], [244, 13, 238, 11], [244, 18, 238, 16], [244, 19, 238, 17], [244, 22, 238, 20], [244, 27, 238, 25], [245, 4, 239, 2, "Platform"], [245, 12, 239, 10], [245, 13, 239, 11], [245, 22, 239, 20], [245, 23, 239, 21], [245, 26, 239, 24], [245, 35, 239, 33], [246, 4, 240, 2], [246, 11, 240, 9, "Platform"], [246, 19, 240, 17], [247, 2, 241, 0], [247, 3, 241, 1], [247, 4, 241, 2], [247, 5, 241, 3], [247, 6, 241, 4], [247, 7, 241, 5], [248, 0, 241, 6], [248, 3]], "functionMap": {"names": ["<global>", "createJSReanimatedModule", "JSReanimated", "scheduleOnUI", "createWorkletRuntime", "scheduleOnRuntime", "registerEventHandler", "unregisterEventHandler", "enableLayoutAnimations", "configureLayoutAnimationBatch", "setShouldAnimateExitingForTag", "registerSensor", "getSensorCallback", "<anonymous>", "unregisterSensor", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "initializeSensor", "getSensorName", "detectPlatform", "getViewProp", "configureProps", "executeOnUIRuntimeSync", "markNodeAsRemovable", "unmarkNodeAsRemovable"], "mappings": "AAA;OCQ;CDE;AEM;ECS;GDG;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNU;EOC;GPE;EQC;GRE;ESC;GTsB;sBUC;eCI;SDkB;eCG;SDa;eCE;SDuB;GVE;EYC;GZM;EaC;GbW;EcC;GdE;EeC;GfkB;EgBC;GhBa;EiBC;GjBW;EkBC;GlBE;EmBC;GnBE;EoBC;GpBE;EqBC;GrBE;EsBC;GtBE;CFC;mCaK;CbM"}}, "type": "js/module"}]}