{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-native/normalize-colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 60, "index": 295}}], "key": "r6xNj+hfHNSiyr0OqQ2Fc9JYEeE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _normalizeColors = _interopRequireDefault(require(_dependencyMap[1], \"@react-native/normalize-colors\"));\n  /**\n   * Copyright (c) <PERSON> Gallagher.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var processColor = color => {\n    if (color === undefined || color === null) {\n      return color;\n    }\n\n    // convert number and hex\n    var int32Color = (0, _normalizeColors.default)(color);\n    if (int32Color === undefined || int32Color === null) {\n      return undefined;\n    }\n    int32Color = (int32Color << 24 | int32Color >>> 8) >>> 0;\n    return int32Color;\n  };\n  var _default = exports.default = processColor;\n});", "lineCount": 32, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_normalizeColors"], [7, 22, 11, 0], [7, 25, 11, 0, "_interopRequireDefault"], [7, 47, 11, 0], [7, 48, 11, 0, "require"], [7, 55, 11, 0], [7, 56, 11, 0, "_dependencyMap"], [7, 70, 11, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [18, 2, 12, 0], [18, 6, 12, 4, "processColor"], [18, 18, 12, 16], [18, 21, 12, 19, "color"], [18, 26, 12, 24], [18, 30, 12, 28], [19, 4, 13, 2], [19, 8, 13, 6, "color"], [19, 13, 13, 11], [19, 18, 13, 16, "undefined"], [19, 27, 13, 25], [19, 31, 13, 29, "color"], [19, 36, 13, 34], [19, 41, 13, 39], [19, 45, 13, 43], [19, 47, 13, 45], [20, 6, 14, 4], [20, 13, 14, 11, "color"], [20, 18, 14, 16], [21, 4, 15, 2], [23, 4, 17, 2], [24, 4, 18, 2], [24, 8, 18, 6, "int32Color"], [24, 18, 18, 16], [24, 21, 18, 19], [24, 25, 18, 19, "normalizeColor"], [24, 49, 18, 33], [24, 51, 18, 34, "color"], [24, 56, 18, 39], [24, 57, 18, 40], [25, 4, 19, 2], [25, 8, 19, 6, "int32Color"], [25, 18, 19, 16], [25, 23, 19, 21, "undefined"], [25, 32, 19, 30], [25, 36, 19, 34, "int32Color"], [25, 46, 19, 44], [25, 51, 19, 49], [25, 55, 19, 53], [25, 57, 19, 55], [26, 6, 20, 4], [26, 13, 20, 11, "undefined"], [26, 22, 20, 20], [27, 4, 21, 2], [28, 4, 22, 2, "int32Color"], [28, 14, 22, 12], [28, 17, 22, 15], [28, 18, 22, 16, "int32Color"], [28, 28, 22, 26], [28, 32, 22, 30], [28, 34, 22, 32], [28, 37, 22, 35, "int32Color"], [28, 47, 22, 45], [28, 52, 22, 50], [28, 53, 22, 51], [28, 59, 22, 57], [28, 60, 22, 58], [29, 4, 23, 2], [29, 11, 23, 9, "int32Color"], [29, 21, 23, 19], [30, 2, 24, 0], [30, 3, 24, 1], [31, 2, 24, 2], [31, 6, 24, 2, "_default"], [31, 14, 24, 2], [31, 17, 24, 2, "exports"], [31, 24, 24, 2], [31, 25, 24, 2, "default"], [31, 32, 24, 2], [31, 35, 25, 15, "processColor"], [31, 47, 25, 27], [32, 0, 25, 27], [32, 3]], "functionMap": {"names": ["<global>", "processColor"], "mappings": "AAA;mBCW;CDY"}}, "type": "js/module"}]}