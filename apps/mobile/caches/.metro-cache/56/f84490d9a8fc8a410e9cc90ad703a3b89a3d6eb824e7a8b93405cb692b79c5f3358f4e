{"dependencies": [{"name": "react-is", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 16, "index": 245}, "end": {"line": 9, "column": 35, "index": 264}}], "key": "hoZupclAije+HbYquo78nDVN6Z8=", "exportNames": ["*"]}}, {"name": "./factoryWithTypeCheckers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 19, "index": 441}, "end": {"line": 14, "column": 55, "index": 477}}], "key": "AJ8PEIUIsT7wY8zRwZLv+TYY0Oo=", "exportNames": ["*"]}}, {"name": "./factoryWithThrowingShims", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 19, "index": 667}, "end": {"line": 18, "column": 56, "index": 704}}], "key": "WO13krhvdyQ9tzMUYg8wZV+JNRg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  if (process.env.NODE_ENV !== 'production') {\n    var ReactIs = require(_dependencyMap[0], \"react-is\");\n\n    // By explicitly using `prop-types` you are opting into new development behavior.\n    // http://fb.me/prop-types-in-prod\n    var throwOnDirectAccess = true;\n    module.exports = require(_dependencyMap[1], \"./factoryWithTypeCheckers\")(ReactIs.isElement, throwOnDirectAccess);\n  } else {\n    // By explicitly using `prop-types` you are opting into new production behavior.\n    // http://fb.me/prop-types-in-prod\n    module.exports = require(_dependencyMap[2], \"./factoryWithThrowingShims\")();\n  }\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [9, 2, 8, 0], [9, 6, 8, 4, "process"], [9, 13, 8, 11], [9, 14, 8, 12, "env"], [9, 17, 8, 15], [9, 18, 8, 16, "NODE_ENV"], [9, 26, 8, 24], [9, 31, 8, 29], [9, 43, 8, 41], [9, 45, 8, 43], [10, 4, 9, 2], [10, 8, 9, 6, "ReactIs"], [10, 15, 9, 13], [10, 18, 9, 16, "require"], [10, 25, 9, 23], [10, 26, 9, 23, "_dependencyMap"], [10, 40, 9, 23], [10, 55, 9, 34], [10, 56, 9, 35], [12, 4, 11, 2], [13, 4, 12, 2], [14, 4, 13, 2], [14, 8, 13, 6, "throwOnDirectAccess"], [14, 27, 13, 25], [14, 30, 13, 28], [14, 34, 13, 32], [15, 4, 14, 2, "module"], [15, 10, 14, 8], [15, 11, 14, 9, "exports"], [15, 18, 14, 16], [15, 21, 14, 19, "require"], [15, 28, 14, 26], [15, 29, 14, 26, "_dependencyMap"], [15, 43, 14, 26], [15, 75, 14, 54], [15, 76, 14, 55], [15, 77, 14, 56, "ReactIs"], [15, 84, 14, 63], [15, 85, 14, 64, "isElement"], [15, 94, 14, 73], [15, 96, 14, 75, "throwOnDirectAccess"], [15, 115, 14, 94], [15, 116, 14, 95], [16, 2, 15, 0], [16, 3, 15, 1], [16, 9, 15, 7], [17, 4, 16, 2], [18, 4, 17, 2], [19, 4, 18, 2, "module"], [19, 10, 18, 8], [19, 11, 18, 9, "exports"], [19, 18, 18, 16], [19, 21, 18, 19, "require"], [19, 28, 18, 26], [19, 29, 18, 26, "_dependencyMap"], [19, 43, 18, 26], [19, 76, 18, 55], [19, 77, 18, 56], [19, 78, 18, 57], [19, 79, 18, 58], [20, 2, 19, 0], [21, 0, 19, 1], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}