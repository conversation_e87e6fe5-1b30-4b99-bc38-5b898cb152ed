{"dependencies": [{"name": "../../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 49, "index": 64}}], "key": "gUqK/inccYjUWjFbxEcDL+F40JA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isValidRubberBandConfig = exports.VELOCITY_EPS = exports.SLOPE_FACTOR = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../../PlatformChecker.js\");\n  const IS_WEB = (0, _PlatformChecker.isWeb)();\n  const VELOCITY_EPS = exports.VELOCITY_EPS = IS_WEB ? 1 / 20 : 1;\n  const SLOPE_FACTOR = exports.SLOPE_FACTOR = 0.1;\n\n  /**\n   * The decay animation configuration.\n   *\n   * @param velocity - Initial velocity of the animation. Defaults to 0.\n   * @param deceleration - The rate at which the velocity decreases over time.\n   *   Defaults to 0.998.\n   * @param clamp - Array of two numbers which restricts animation's range.\n   *   Defaults to [].\n   * @param velocityFactor - Velocity multiplier. Defaults to 1.\n   * @param rubberBandEffect - Makes the animation bounce over the limit specified\n   *   in `clamp`. Defaults to `false`.\n   * @param rubberBandFactor - Strength of the rubber band effect. Defaults to\n   *   0.6.\n   * @param reduceMotion - Determines how the animation responds to the device's\n   *   reduced motion accessibility setting. Default to `ReduceMotion.System` -\n   *   {@link ReduceMotion}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDecay#config\n   */\n\n  // If user wants to use rubber band decay animation we have to make sure he has provided clamp\n  const _worklet_17410890019123_init_data = {\n    code: \"function isValidRubberBandConfig_reactNativeReanimated_utilsJs1(config){return!!config.rubberBandEffect&&Array.isArray(config.clamp)&&config.clamp.length===2;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/utils.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isValidRubberBandConfig_reactNativeReanimated_utilsJs1\\\",\\\"config\\\",\\\"rubberBandEffect\\\",\\\"Array\\\",\\\"isArray\\\",\\\"clamp\\\",\\\"length\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/utils.js\\\"],\\\"mappings\\\":\\\"AA4BO,SAAAA,sDAAyCA,CAAAC,MAAA,EAG9C,MAAO,CAAC,CAACA,MAAM,CAACC,gBAAgB,EAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,KAAK,CAAC,EAAIJ,MAAM,CAACI,KAAK,CAACC,MAAM,GAAK,CAAC,CAC9F\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const isValidRubberBandConfig = exports.isValidRubberBandConfig = function () {\n    const _e = [new global.Error(), 1, -27];\n    const isValidRubberBandConfig = function (config) {\n      return !!config.rubberBandEffect && Array.isArray(config.clamp) && config.clamp.length === 2;\n    };\n    isValidRubberBandConfig.__closure = {};\n    isValidRubberBandConfig.__workletHash = 17410890019123;\n    isValidRubberBandConfig.__initData = _worklet_17410890019123_init_data;\n    isValidRubberBandConfig.__stackDetails = _e;\n    return isValidRubberBandConfig;\n  }();\n});", "lineCount": 50, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "isValidRubberBandConfig"], [7, 33, 1, 13], [7, 36, 1, 13, "exports"], [7, 43, 1, 13], [7, 44, 1, 13, "VELOCITY_EPS"], [7, 56, 1, 13], [7, 59, 1, 13, "exports"], [7, 66, 1, 13], [7, 67, 1, 13, "SLOPE_FACTOR"], [7, 79, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "IS_WEB"], [9, 14, 4, 12], [9, 17, 4, 15], [9, 21, 4, 15, "isWeb"], [9, 43, 4, 20], [9, 45, 4, 21], [9, 46, 4, 22], [10, 2, 5, 7], [10, 8, 5, 13, "VELOCITY_EPS"], [10, 20, 5, 25], [10, 23, 5, 25, "exports"], [10, 30, 5, 25], [10, 31, 5, 25, "VELOCITY_EPS"], [10, 43, 5, 25], [10, 46, 5, 28, "IS_WEB"], [10, 52, 5, 34], [10, 55, 5, 37], [10, 56, 5, 38], [10, 59, 5, 41], [10, 61, 5, 43], [10, 64, 5, 46], [10, 65, 5, 47], [11, 2, 6, 7], [11, 8, 6, 13, "SLOPE_FACTOR"], [11, 20, 6, 25], [11, 23, 6, 25, "exports"], [11, 30, 6, 25], [11, 31, 6, 25, "SLOPE_FACTOR"], [11, 43, 6, 25], [11, 46, 6, 28], [11, 49, 6, 31], [13, 2, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 0, 24, 0], [30, 0, 25, 0], [32, 2, 27, 0], [33, 2, 27, 0], [33, 8, 27, 0, "_worklet_17410890019123_init_data"], [33, 41, 27, 0], [34, 4, 27, 0, "code"], [34, 8, 27, 0], [35, 4, 27, 0, "location"], [35, 12, 27, 0], [36, 4, 27, 0, "sourceMap"], [36, 13, 27, 0], [37, 4, 27, 0, "version"], [37, 11, 27, 0], [38, 2, 27, 0], [39, 2, 27, 0], [39, 8, 27, 0, "isValidRubberBandConfig"], [39, 31, 27, 0], [39, 34, 27, 0, "exports"], [39, 41, 27, 0], [39, 42, 27, 0, "isValidRubberBandConfig"], [39, 65, 27, 0], [39, 68, 29, 7], [40, 4, 29, 7], [40, 10, 29, 7, "_e"], [40, 12, 29, 7], [40, 20, 29, 7, "global"], [40, 26, 29, 7], [40, 27, 29, 7, "Error"], [40, 32, 29, 7], [41, 4, 29, 7], [41, 10, 29, 7, "isValidRubberBandConfig"], [41, 33, 29, 7], [41, 45, 29, 7, "isValidRubberBandConfig"], [41, 46, 29, 40, "config"], [41, 52, 29, 46], [41, 54, 29, 48], [42, 6, 32, 2], [42, 13, 32, 9], [42, 14, 32, 10], [42, 15, 32, 11, "config"], [42, 21, 32, 17], [42, 22, 32, 18, "rubberBandEffect"], [42, 38, 32, 34], [42, 42, 32, 38, "Array"], [42, 47, 32, 43], [42, 48, 32, 44, "isArray"], [42, 55, 32, 51], [42, 56, 32, 52, "config"], [42, 62, 32, 58], [42, 63, 32, 59, "clamp"], [42, 68, 32, 64], [42, 69, 32, 65], [42, 73, 32, 69, "config"], [42, 79, 32, 75], [42, 80, 32, 76, "clamp"], [42, 85, 32, 81], [42, 86, 32, 82, "length"], [42, 92, 32, 88], [42, 97, 32, 93], [42, 98, 32, 94], [43, 4, 33, 0], [43, 5, 33, 1], [44, 4, 33, 1, "isValidRubberBandConfig"], [44, 27, 33, 1], [44, 28, 33, 1, "__closure"], [44, 37, 33, 1], [45, 4, 33, 1, "isValidRubberBandConfig"], [45, 27, 33, 1], [45, 28, 33, 1, "__workletHash"], [45, 41, 33, 1], [46, 4, 33, 1, "isValidRubberBandConfig"], [46, 27, 33, 1], [46, 28, 33, 1, "__initData"], [46, 38, 33, 1], [46, 41, 33, 1, "_worklet_17410890019123_init_data"], [46, 74, 33, 1], [47, 4, 33, 1, "isValidRubberBandConfig"], [47, 27, 33, 1], [47, 28, 33, 1, "__stackDetails"], [47, 42, 33, 1], [47, 45, 33, 1, "_e"], [47, 47, 33, 1], [48, 4, 33, 1], [48, 11, 33, 1, "isValidRubberBandConfig"], [48, 34, 33, 1], [49, 2, 33, 1], [49, 3, 29, 7], [50, 0, 29, 7], [50, 3]], "functionMap": {"names": ["<global>", "isValidRubberBandConfig"], "mappings": "AAA;OC4B;CDI"}}, "type": "js/module"}]}