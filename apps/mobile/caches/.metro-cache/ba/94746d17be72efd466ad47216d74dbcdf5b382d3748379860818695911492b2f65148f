{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.customDirectEventTypes = void 0;\n  // customDirectEventTypes doesn't exist in react-native-web, therefore importing it\n  // directly in createHandler.tsx would end in crash.\n  const customDirectEventTypes = exports.customDirectEventTypes = {};\n});", "lineCount": 9, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [8, 8, 3, 6, "customDirectEventTypes"], [8, 30, 3, 28], [8, 33, 3, 28, "exports"], [8, 40, 3, 28], [8, 41, 3, 28, "customDirectEventTypes"], [8, 63, 3, 28], [8, 66, 3, 31], [8, 67, 3, 32], [8, 68, 3, 33], [9, 0, 3, 34], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}