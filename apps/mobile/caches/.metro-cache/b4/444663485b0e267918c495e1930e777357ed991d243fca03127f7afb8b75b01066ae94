{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  // Implementation taken from Flutter's LeastSquareSolver\n  // https://github.com/flutter/flutter/blob/master/packages/flutter/lib/src/gestures/lsq_solver.dart\n  class Vector {\n    constructor(length) {\n      _defineProperty(this, \"offset\", void 0);\n      _defineProperty(this, \"length\", void 0);\n      _defineProperty(this, \"elements\", void 0);\n      this.offset = 0;\n      this.length = length;\n      this.elements = new Array(length);\n    }\n    static fromVOL(values, offset, length) {\n      const result = new Vector(0);\n      result.offset = offset;\n      result.length = length;\n      result.elements = values;\n      return result;\n    }\n    get(index) {\n      return this.elements[this.offset + index];\n    }\n    set(index, value) {\n      this.elements[this.offset + index] = value;\n    }\n    dot(other) {\n      let result = 0;\n      for (let i = 0; i < this.length; i++) {\n        result += this.get(i) * other.get(i);\n      }\n      return result;\n    }\n    norm() {\n      return Math.sqrt(this.dot(this));\n    }\n  }\n  class Matrix {\n    constructor(rows, columns) {\n      _defineProperty(this, \"columns\", void 0);\n      _defineProperty(this, \"elements\", void 0);\n      this.columns = columns;\n      this.elements = new Array(rows * columns);\n    }\n    get(row, column) {\n      return this.elements[row * this.columns + column];\n    }\n    set(row, column, value) {\n      this.elements[row * this.columns + column] = value;\n    }\n    getRow(row) {\n      return Vector.fromVOL(this.elements, row * this.columns, this.columns);\n    }\n  } // An nth degree polynomial fit to a dataset.\n\n  class PolynomialFit {\n    // The polynomial coefficients of the fit.\n    //\n    // For each `i`, the element `coefficients[i]` is the coefficient of\n    // the `i`-th power of the variable.\n    // Creates a polynomial fit of the given degree.\n    //\n    // There are n + 1 coefficients in a fit of degree n.\n    constructor(degree) {\n      _defineProperty(this, \"coefficients\", void 0);\n      this.coefficients = new Array(degree + 1);\n    }\n  }\n  const precisionErrorTolerance = 1e-10; // Uses the least-squares algorithm to fit a polynomial to a set of data.\n\n  class LeastSquareSolver {\n    // The x-coordinates of each data point.\n    // The y-coordinates of each data point.\n    // The weight to use for each data point.\n    // Creates a least-squares solver.\n    //\n    // The [x], [y], and [w] arguments must not be null.\n    constructor(x, y, w) {\n      _defineProperty(this, \"x\", void 0);\n      _defineProperty(this, \"y\", void 0);\n      _defineProperty(this, \"w\", void 0);\n      this.x = x;\n      this.y = y;\n      this.w = w;\n    } // Fits a polynomial of the given degree to the data points.\n    //\n    // When there is not enough data to fit a curve null is returned.\n\n    solve(degree) {\n      if (degree > this.x.length) {\n        // Not enough data to fit a curve.\n        return null;\n      }\n      const result = new PolynomialFit(degree); // Shorthands for the purpose of notation equivalence to original C++ code.\n\n      const m = this.x.length;\n      const n = degree + 1; // Expand the X vector to a matrix A, pre-multiplied by the weights.\n\n      const a = new Matrix(n, m);\n      for (let h = 0; h < m; h++) {\n        a.set(0, h, this.w[h]);\n        for (let i = 1; i < n; i++) {\n          a.set(i, h, a.get(i - 1, h) * this.x[h]);\n        }\n      } // Apply the Gram-Schmidt process to A to obtain its QR decomposition.\n      // Orthonormal basis, column-major ordVectorer.\n\n      const q = new Matrix(n, m); // Upper triangular matrix, row-major order.\n\n      const r = new Matrix(n, m);\n      for (let j = 0; j < n; j += 1) {\n        for (let h = 0; h < m; h += 1) {\n          q.set(j, h, a.get(j, h));\n        }\n        for (let i = 0; i < j; i += 1) {\n          const dot = q.getRow(j).dot(q.getRow(i));\n          for (let h = 0; h < m; h += 1) {\n            q.set(j, h, q.get(j, h) - dot * q.get(i, h));\n          }\n        }\n        const norm = q.getRow(j).norm();\n        if (norm < precisionErrorTolerance) {\n          // Vectors are linearly dependent or zero so no solution.\n          return null;\n        }\n        const inverseNorm = 1.0 / norm;\n        for (let h = 0; h < m; h += 1) {\n          q.set(j, h, q.get(j, h) * inverseNorm);\n        }\n        for (let i = 0; i < n; i += 1) {\n          r.set(j, i, i < j ? 0.0 : q.getRow(j).dot(a.getRow(i)));\n        }\n      } // Solve R B = Qt W Y to find B. This is easy because R is upper triangular.\n      // We just work from bottom-right to top-left calculating B's coefficients.\n\n      const wy = new Vector(m);\n      for (let h = 0; h < m; h += 1) {\n        wy.set(h, this.y[h] * this.w[h]);\n      }\n      for (let i = n - 1; i >= 0; i -= 1) {\n        result.coefficients[i] = q.getRow(i).dot(wy);\n        for (let j = n - 1; j > i; j -= 1) {\n          result.coefficients[i] -= r.get(i, j) * result.coefficients[j];\n        }\n        result.coefficients[i] /= r.get(i, i);\n      }\n      return result;\n    }\n  }\n  exports.default = LeastSquareSolver;\n});", "lineCount": 168, "map": [[6, 2, 1, 0], [6, 11, 1, 9, "_defineProperty"], [6, 26, 1, 24, "_defineProperty"], [6, 27, 1, 25, "obj"], [6, 30, 1, 28], [6, 32, 1, 30, "key"], [6, 35, 1, 33], [6, 37, 1, 35, "value"], [6, 42, 1, 40], [6, 44, 1, 42], [7, 4, 1, 44], [7, 8, 1, 48, "key"], [7, 11, 1, 51], [7, 15, 1, 55, "obj"], [7, 18, 1, 58], [7, 20, 1, 60], [8, 6, 1, 62, "Object"], [8, 12, 1, 68], [8, 13, 1, 69, "defineProperty"], [8, 27, 1, 83], [8, 28, 1, 84, "obj"], [8, 31, 1, 87], [8, 33, 1, 89, "key"], [8, 36, 1, 92], [8, 38, 1, 94], [9, 8, 1, 96, "value"], [9, 13, 1, 101], [9, 15, 1, 103, "value"], [9, 20, 1, 108], [10, 8, 1, 110, "enumerable"], [10, 18, 1, 120], [10, 20, 1, 122], [10, 24, 1, 126], [11, 8, 1, 128, "configurable"], [11, 20, 1, 140], [11, 22, 1, 142], [11, 26, 1, 146], [12, 8, 1, 148, "writable"], [12, 16, 1, 156], [12, 18, 1, 158], [13, 6, 1, 163], [13, 7, 1, 164], [13, 8, 1, 165], [14, 4, 1, 167], [14, 5, 1, 168], [14, 11, 1, 174], [15, 6, 1, 176, "obj"], [15, 9, 1, 179], [15, 10, 1, 180, "key"], [15, 13, 1, 183], [15, 14, 1, 184], [15, 17, 1, 187, "value"], [15, 22, 1, 192], [16, 4, 1, 194], [17, 4, 1, 196], [17, 11, 1, 203, "obj"], [17, 14, 1, 206], [18, 2, 1, 208], [20, 2, 3, 0], [21, 2, 4, 0], [22, 2, 5, 0], [22, 8, 5, 6, "Vector"], [22, 14, 5, 12], [22, 15, 5, 13], [23, 4, 6, 2, "constructor"], [23, 15, 6, 13, "constructor"], [23, 16, 6, 14, "length"], [23, 22, 6, 20], [23, 24, 6, 22], [24, 6, 7, 4, "_defineProperty"], [24, 21, 7, 19], [24, 22, 7, 20], [24, 26, 7, 24], [24, 28, 7, 26], [24, 36, 7, 34], [24, 38, 7, 36], [24, 43, 7, 41], [24, 44, 7, 42], [24, 45, 7, 43], [25, 6, 9, 4, "_defineProperty"], [25, 21, 9, 19], [25, 22, 9, 20], [25, 26, 9, 24], [25, 28, 9, 26], [25, 36, 9, 34], [25, 38, 9, 36], [25, 43, 9, 41], [25, 44, 9, 42], [25, 45, 9, 43], [26, 6, 11, 4, "_defineProperty"], [26, 21, 11, 19], [26, 22, 11, 20], [26, 26, 11, 24], [26, 28, 11, 26], [26, 38, 11, 36], [26, 40, 11, 38], [26, 45, 11, 43], [26, 46, 11, 44], [26, 47, 11, 45], [27, 6, 13, 4], [27, 10, 13, 8], [27, 11, 13, 9, "offset"], [27, 17, 13, 15], [27, 20, 13, 18], [27, 21, 13, 19], [28, 6, 14, 4], [28, 10, 14, 8], [28, 11, 14, 9, "length"], [28, 17, 14, 15], [28, 20, 14, 18, "length"], [28, 26, 14, 24], [29, 6, 15, 4], [29, 10, 15, 8], [29, 11, 15, 9, "elements"], [29, 19, 15, 17], [29, 22, 15, 20], [29, 26, 15, 24, "Array"], [29, 31, 15, 29], [29, 32, 15, 30, "length"], [29, 38, 15, 36], [29, 39, 15, 37], [30, 4, 16, 2], [31, 4, 18, 2], [31, 11, 18, 9, "fromVOL"], [31, 18, 18, 16, "fromVOL"], [31, 19, 18, 17, "values"], [31, 25, 18, 23], [31, 27, 18, 25, "offset"], [31, 33, 18, 31], [31, 35, 18, 33, "length"], [31, 41, 18, 39], [31, 43, 18, 41], [32, 6, 19, 4], [32, 12, 19, 10, "result"], [32, 18, 19, 16], [32, 21, 19, 19], [32, 25, 19, 23, "Vector"], [32, 31, 19, 29], [32, 32, 19, 30], [32, 33, 19, 31], [32, 34, 19, 32], [33, 6, 20, 4, "result"], [33, 12, 20, 10], [33, 13, 20, 11, "offset"], [33, 19, 20, 17], [33, 22, 20, 20, "offset"], [33, 28, 20, 26], [34, 6, 21, 4, "result"], [34, 12, 21, 10], [34, 13, 21, 11, "length"], [34, 19, 21, 17], [34, 22, 21, 20, "length"], [34, 28, 21, 26], [35, 6, 22, 4, "result"], [35, 12, 22, 10], [35, 13, 22, 11, "elements"], [35, 21, 22, 19], [35, 24, 22, 22, "values"], [35, 30, 22, 28], [36, 6, 23, 4], [36, 13, 23, 11, "result"], [36, 19, 23, 17], [37, 4, 24, 2], [38, 4, 26, 2, "get"], [38, 7, 26, 5, "get"], [38, 8, 26, 6, "index"], [38, 13, 26, 11], [38, 15, 26, 13], [39, 6, 27, 4], [39, 13, 27, 11], [39, 17, 27, 15], [39, 18, 27, 16, "elements"], [39, 26, 27, 24], [39, 27, 27, 25], [39, 31, 27, 29], [39, 32, 27, 30, "offset"], [39, 38, 27, 36], [39, 41, 27, 39, "index"], [39, 46, 27, 44], [39, 47, 27, 45], [40, 4, 28, 2], [41, 4, 30, 2, "set"], [41, 7, 30, 5, "set"], [41, 8, 30, 6, "index"], [41, 13, 30, 11], [41, 15, 30, 13, "value"], [41, 20, 30, 18], [41, 22, 30, 20], [42, 6, 31, 4], [42, 10, 31, 8], [42, 11, 31, 9, "elements"], [42, 19, 31, 17], [42, 20, 31, 18], [42, 24, 31, 22], [42, 25, 31, 23, "offset"], [42, 31, 31, 29], [42, 34, 31, 32, "index"], [42, 39, 31, 37], [42, 40, 31, 38], [42, 43, 31, 41, "value"], [42, 48, 31, 46], [43, 4, 32, 2], [44, 4, 34, 2, "dot"], [44, 7, 34, 5, "dot"], [44, 8, 34, 6, "other"], [44, 13, 34, 11], [44, 15, 34, 13], [45, 6, 35, 4], [45, 10, 35, 8, "result"], [45, 16, 35, 14], [45, 19, 35, 17], [45, 20, 35, 18], [46, 6, 37, 4], [46, 11, 37, 9], [46, 15, 37, 13, "i"], [46, 16, 37, 14], [46, 19, 37, 17], [46, 20, 37, 18], [46, 22, 37, 20, "i"], [46, 23, 37, 21], [46, 26, 37, 24], [46, 30, 37, 28], [46, 31, 37, 29, "length"], [46, 37, 37, 35], [46, 39, 37, 37, "i"], [46, 40, 37, 38], [46, 42, 37, 40], [46, 44, 37, 42], [47, 8, 38, 6, "result"], [47, 14, 38, 12], [47, 18, 38, 16], [47, 22, 38, 20], [47, 23, 38, 21, "get"], [47, 26, 38, 24], [47, 27, 38, 25, "i"], [47, 28, 38, 26], [47, 29, 38, 27], [47, 32, 38, 30, "other"], [47, 37, 38, 35], [47, 38, 38, 36, "get"], [47, 41, 38, 39], [47, 42, 38, 40, "i"], [47, 43, 38, 41], [47, 44, 38, 42], [48, 6, 39, 4], [49, 6, 41, 4], [49, 13, 41, 11, "result"], [49, 19, 41, 17], [50, 4, 42, 2], [51, 4, 44, 2, "norm"], [51, 8, 44, 6, "norm"], [51, 9, 44, 6], [51, 11, 44, 9], [52, 6, 45, 4], [52, 13, 45, 11, "Math"], [52, 17, 45, 15], [52, 18, 45, 16, "sqrt"], [52, 22, 45, 20], [52, 23, 45, 21], [52, 27, 45, 25], [52, 28, 45, 26, "dot"], [52, 31, 45, 29], [52, 32, 45, 30], [52, 36, 45, 34], [52, 37, 45, 35], [52, 38, 45, 36], [53, 4, 46, 2], [54, 2, 48, 0], [55, 2, 50, 0], [55, 8, 50, 6, "Matrix"], [55, 14, 50, 12], [55, 15, 50, 13], [56, 4, 51, 2, "constructor"], [56, 15, 51, 13, "constructor"], [56, 16, 51, 14, "rows"], [56, 20, 51, 18], [56, 22, 51, 20, "columns"], [56, 29, 51, 27], [56, 31, 51, 29], [57, 6, 52, 4, "_defineProperty"], [57, 21, 52, 19], [57, 22, 52, 20], [57, 26, 52, 24], [57, 28, 52, 26], [57, 37, 52, 35], [57, 39, 52, 37], [57, 44, 52, 42], [57, 45, 52, 43], [57, 46, 52, 44], [58, 6, 54, 4, "_defineProperty"], [58, 21, 54, 19], [58, 22, 54, 20], [58, 26, 54, 24], [58, 28, 54, 26], [58, 38, 54, 36], [58, 40, 54, 38], [58, 45, 54, 43], [58, 46, 54, 44], [58, 47, 54, 45], [59, 6, 56, 4], [59, 10, 56, 8], [59, 11, 56, 9, "columns"], [59, 18, 56, 16], [59, 21, 56, 19, "columns"], [59, 28, 56, 26], [60, 6, 57, 4], [60, 10, 57, 8], [60, 11, 57, 9, "elements"], [60, 19, 57, 17], [60, 22, 57, 20], [60, 26, 57, 24, "Array"], [60, 31, 57, 29], [60, 32, 57, 30, "rows"], [60, 36, 57, 34], [60, 39, 57, 37, "columns"], [60, 46, 57, 44], [60, 47, 57, 45], [61, 4, 58, 2], [62, 4, 60, 2, "get"], [62, 7, 60, 5, "get"], [62, 8, 60, 6, "row"], [62, 11, 60, 9], [62, 13, 60, 11, "column"], [62, 19, 60, 17], [62, 21, 60, 19], [63, 6, 61, 4], [63, 13, 61, 11], [63, 17, 61, 15], [63, 18, 61, 16, "elements"], [63, 26, 61, 24], [63, 27, 61, 25, "row"], [63, 30, 61, 28], [63, 33, 61, 31], [63, 37, 61, 35], [63, 38, 61, 36, "columns"], [63, 45, 61, 43], [63, 48, 61, 46, "column"], [63, 54, 61, 52], [63, 55, 61, 53], [64, 4, 62, 2], [65, 4, 64, 2, "set"], [65, 7, 64, 5, "set"], [65, 8, 64, 6, "row"], [65, 11, 64, 9], [65, 13, 64, 11, "column"], [65, 19, 64, 17], [65, 21, 64, 19, "value"], [65, 26, 64, 24], [65, 28, 64, 26], [66, 6, 65, 4], [66, 10, 65, 8], [66, 11, 65, 9, "elements"], [66, 19, 65, 17], [66, 20, 65, 18, "row"], [66, 23, 65, 21], [66, 26, 65, 24], [66, 30, 65, 28], [66, 31, 65, 29, "columns"], [66, 38, 65, 36], [66, 41, 65, 39, "column"], [66, 47, 65, 45], [66, 48, 65, 46], [66, 51, 65, 49, "value"], [66, 56, 65, 54], [67, 4, 66, 2], [68, 4, 68, 2, "getRow"], [68, 10, 68, 8, "getRow"], [68, 11, 68, 9, "row"], [68, 14, 68, 12], [68, 16, 68, 14], [69, 6, 69, 4], [69, 13, 69, 11, "Vector"], [69, 19, 69, 17], [69, 20, 69, 18, "fromVOL"], [69, 27, 69, 25], [69, 28, 69, 26], [69, 32, 69, 30], [69, 33, 69, 31, "elements"], [69, 41, 69, 39], [69, 43, 69, 41, "row"], [69, 46, 69, 44], [69, 49, 69, 47], [69, 53, 69, 51], [69, 54, 69, 52, "columns"], [69, 61, 69, 59], [69, 63, 69, 61], [69, 67, 69, 65], [69, 68, 69, 66, "columns"], [69, 75, 69, 73], [69, 76, 69, 74], [70, 4, 70, 2], [71, 2, 72, 0], [71, 3, 72, 1], [71, 4, 72, 2], [73, 2, 75, 0], [73, 8, 75, 6, "PolynomialFit"], [73, 21, 75, 19], [73, 22, 75, 20], [74, 4, 76, 2], [75, 4, 77, 2], [76, 4, 78, 2], [77, 4, 79, 2], [78, 4, 80, 2], [79, 4, 81, 2], [80, 4, 82, 2], [81, 4, 83, 2, "constructor"], [81, 15, 83, 13, "constructor"], [81, 16, 83, 14, "degree"], [81, 22, 83, 20], [81, 24, 83, 22], [82, 6, 84, 4, "_defineProperty"], [82, 21, 84, 19], [82, 22, 84, 20], [82, 26, 84, 24], [82, 28, 84, 26], [82, 42, 84, 40], [82, 44, 84, 42], [82, 49, 84, 47], [82, 50, 84, 48], [82, 51, 84, 49], [83, 6, 86, 4], [83, 10, 86, 8], [83, 11, 86, 9, "coefficients"], [83, 23, 86, 21], [83, 26, 86, 24], [83, 30, 86, 28, "Array"], [83, 35, 86, 33], [83, 36, 86, 34, "degree"], [83, 42, 86, 40], [83, 45, 86, 43], [83, 46, 86, 44], [83, 47, 86, 45], [84, 4, 87, 2], [85, 2, 89, 0], [86, 2, 91, 0], [86, 8, 91, 6, "precisionErrorTolerance"], [86, 31, 91, 29], [86, 34, 91, 32], [86, 39, 91, 37], [86, 40, 91, 38], [86, 41, 91, 39], [88, 2, 93, 15], [88, 8, 93, 21, "LeastSquareSolver"], [88, 25, 93, 38], [88, 26, 93, 39], [89, 4, 94, 2], [90, 4, 95, 2], [91, 4, 96, 2], [92, 4, 97, 2], [93, 4, 98, 2], [94, 4, 99, 2], [95, 4, 100, 2, "constructor"], [95, 15, 100, 13, "constructor"], [95, 16, 100, 14, "x"], [95, 17, 100, 15], [95, 19, 100, 17, "y"], [95, 20, 100, 18], [95, 22, 100, 20, "w"], [95, 23, 100, 21], [95, 25, 100, 23], [96, 6, 101, 4, "_defineProperty"], [96, 21, 101, 19], [96, 22, 101, 20], [96, 26, 101, 24], [96, 28, 101, 26], [96, 31, 101, 29], [96, 33, 101, 31], [96, 38, 101, 36], [96, 39, 101, 37], [96, 40, 101, 38], [97, 6, 103, 4, "_defineProperty"], [97, 21, 103, 19], [97, 22, 103, 20], [97, 26, 103, 24], [97, 28, 103, 26], [97, 31, 103, 29], [97, 33, 103, 31], [97, 38, 103, 36], [97, 39, 103, 37], [97, 40, 103, 38], [98, 6, 105, 4, "_defineProperty"], [98, 21, 105, 19], [98, 22, 105, 20], [98, 26, 105, 24], [98, 28, 105, 26], [98, 31, 105, 29], [98, 33, 105, 31], [98, 38, 105, 36], [98, 39, 105, 37], [98, 40, 105, 38], [99, 6, 107, 4], [99, 10, 107, 8], [99, 11, 107, 9, "x"], [99, 12, 107, 10], [99, 15, 107, 13, "x"], [99, 16, 107, 14], [100, 6, 108, 4], [100, 10, 108, 8], [100, 11, 108, 9, "y"], [100, 12, 108, 10], [100, 15, 108, 13, "y"], [100, 16, 108, 14], [101, 6, 109, 4], [101, 10, 109, 8], [101, 11, 109, 9, "w"], [101, 12, 109, 10], [101, 15, 109, 13, "w"], [101, 16, 109, 14], [102, 4, 110, 2], [102, 5, 110, 3], [102, 6, 110, 4], [103, 4, 111, 2], [104, 4, 112, 2], [106, 4, 115, 2, "solve"], [106, 9, 115, 7, "solve"], [106, 10, 115, 8, "degree"], [106, 16, 115, 14], [106, 18, 115, 16], [107, 6, 116, 4], [107, 10, 116, 8, "degree"], [107, 16, 116, 14], [107, 19, 116, 17], [107, 23, 116, 21], [107, 24, 116, 22, "x"], [107, 25, 116, 23], [107, 26, 116, 24, "length"], [107, 32, 116, 30], [107, 34, 116, 32], [108, 8, 117, 6], [109, 8, 118, 6], [109, 15, 118, 13], [109, 19, 118, 17], [110, 6, 119, 4], [111, 6, 121, 4], [111, 12, 121, 10, "result"], [111, 18, 121, 16], [111, 21, 121, 19], [111, 25, 121, 23, "PolynomialFit"], [111, 38, 121, 36], [111, 39, 121, 37, "degree"], [111, 45, 121, 43], [111, 46, 121, 44], [111, 47, 121, 45], [111, 48, 121, 46], [113, 6, 123, 4], [113, 12, 123, 10, "m"], [113, 13, 123, 11], [113, 16, 123, 14], [113, 20, 123, 18], [113, 21, 123, 19, "x"], [113, 22, 123, 20], [113, 23, 123, 21, "length"], [113, 29, 123, 27], [114, 6, 124, 4], [114, 12, 124, 10, "n"], [114, 13, 124, 11], [114, 16, 124, 14, "degree"], [114, 22, 124, 20], [114, 25, 124, 23], [114, 26, 124, 24], [114, 27, 124, 25], [114, 28, 124, 26], [116, 6, 126, 4], [116, 12, 126, 10, "a"], [116, 13, 126, 11], [116, 16, 126, 14], [116, 20, 126, 18, "Matrix"], [116, 26, 126, 24], [116, 27, 126, 25, "n"], [116, 28, 126, 26], [116, 30, 126, 28, "m"], [116, 31, 126, 29], [116, 32, 126, 30], [117, 6, 128, 4], [117, 11, 128, 9], [117, 15, 128, 13, "h"], [117, 16, 128, 14], [117, 19, 128, 17], [117, 20, 128, 18], [117, 22, 128, 20, "h"], [117, 23, 128, 21], [117, 26, 128, 24, "m"], [117, 27, 128, 25], [117, 29, 128, 27, "h"], [117, 30, 128, 28], [117, 32, 128, 30], [117, 34, 128, 32], [118, 8, 129, 6, "a"], [118, 9, 129, 7], [118, 10, 129, 8, "set"], [118, 13, 129, 11], [118, 14, 129, 12], [118, 15, 129, 13], [118, 17, 129, 15, "h"], [118, 18, 129, 16], [118, 20, 129, 18], [118, 24, 129, 22], [118, 25, 129, 23, "w"], [118, 26, 129, 24], [118, 27, 129, 25, "h"], [118, 28, 129, 26], [118, 29, 129, 27], [118, 30, 129, 28], [119, 8, 131, 6], [119, 13, 131, 11], [119, 17, 131, 15, "i"], [119, 18, 131, 16], [119, 21, 131, 19], [119, 22, 131, 20], [119, 24, 131, 22, "i"], [119, 25, 131, 23], [119, 28, 131, 26, "n"], [119, 29, 131, 27], [119, 31, 131, 29, "i"], [119, 32, 131, 30], [119, 34, 131, 32], [119, 36, 131, 34], [120, 10, 132, 8, "a"], [120, 11, 132, 9], [120, 12, 132, 10, "set"], [120, 15, 132, 13], [120, 16, 132, 14, "i"], [120, 17, 132, 15], [120, 19, 132, 17, "h"], [120, 20, 132, 18], [120, 22, 132, 20, "a"], [120, 23, 132, 21], [120, 24, 132, 22, "get"], [120, 27, 132, 25], [120, 28, 132, 26, "i"], [120, 29, 132, 27], [120, 32, 132, 30], [120, 33, 132, 31], [120, 35, 132, 33, "h"], [120, 36, 132, 34], [120, 37, 132, 35], [120, 40, 132, 38], [120, 44, 132, 42], [120, 45, 132, 43, "x"], [120, 46, 132, 44], [120, 47, 132, 45, "h"], [120, 48, 132, 46], [120, 49, 132, 47], [120, 50, 132, 48], [121, 8, 133, 6], [122, 6, 134, 4], [122, 7, 134, 5], [122, 8, 134, 6], [123, 6, 135, 4], [125, 6, 138, 4], [125, 12, 138, 10, "q"], [125, 13, 138, 11], [125, 16, 138, 14], [125, 20, 138, 18, "Matrix"], [125, 26, 138, 24], [125, 27, 138, 25, "n"], [125, 28, 138, 26], [125, 30, 138, 28, "m"], [125, 31, 138, 29], [125, 32, 138, 30], [125, 33, 138, 31], [125, 34, 138, 32], [127, 6, 140, 4], [127, 12, 140, 10, "r"], [127, 13, 140, 11], [127, 16, 140, 14], [127, 20, 140, 18, "Matrix"], [127, 26, 140, 24], [127, 27, 140, 25, "n"], [127, 28, 140, 26], [127, 30, 140, 28, "m"], [127, 31, 140, 29], [127, 32, 140, 30], [128, 6, 142, 4], [128, 11, 142, 9], [128, 15, 142, 13, "j"], [128, 16, 142, 14], [128, 19, 142, 17], [128, 20, 142, 18], [128, 22, 142, 20, "j"], [128, 23, 142, 21], [128, 26, 142, 24, "n"], [128, 27, 142, 25], [128, 29, 142, 27, "j"], [128, 30, 142, 28], [128, 34, 142, 32], [128, 35, 142, 33], [128, 37, 142, 35], [129, 8, 143, 6], [129, 13, 143, 11], [129, 17, 143, 15, "h"], [129, 18, 143, 16], [129, 21, 143, 19], [129, 22, 143, 20], [129, 24, 143, 22, "h"], [129, 25, 143, 23], [129, 28, 143, 26, "m"], [129, 29, 143, 27], [129, 31, 143, 29, "h"], [129, 32, 143, 30], [129, 36, 143, 34], [129, 37, 143, 35], [129, 39, 143, 37], [130, 10, 144, 8, "q"], [130, 11, 144, 9], [130, 12, 144, 10, "set"], [130, 15, 144, 13], [130, 16, 144, 14, "j"], [130, 17, 144, 15], [130, 19, 144, 17, "h"], [130, 20, 144, 18], [130, 22, 144, 20, "a"], [130, 23, 144, 21], [130, 24, 144, 22, "get"], [130, 27, 144, 25], [130, 28, 144, 26, "j"], [130, 29, 144, 27], [130, 31, 144, 29, "h"], [130, 32, 144, 30], [130, 33, 144, 31], [130, 34, 144, 32], [131, 8, 145, 6], [132, 8, 147, 6], [132, 13, 147, 11], [132, 17, 147, 15, "i"], [132, 18, 147, 16], [132, 21, 147, 19], [132, 22, 147, 20], [132, 24, 147, 22, "i"], [132, 25, 147, 23], [132, 28, 147, 26, "j"], [132, 29, 147, 27], [132, 31, 147, 29, "i"], [132, 32, 147, 30], [132, 36, 147, 34], [132, 37, 147, 35], [132, 39, 147, 37], [133, 10, 148, 8], [133, 16, 148, 14, "dot"], [133, 19, 148, 17], [133, 22, 148, 20, "q"], [133, 23, 148, 21], [133, 24, 148, 22, "getRow"], [133, 30, 148, 28], [133, 31, 148, 29, "j"], [133, 32, 148, 30], [133, 33, 148, 31], [133, 34, 148, 32, "dot"], [133, 37, 148, 35], [133, 38, 148, 36, "q"], [133, 39, 148, 37], [133, 40, 148, 38, "getRow"], [133, 46, 148, 44], [133, 47, 148, 45, "i"], [133, 48, 148, 46], [133, 49, 148, 47], [133, 50, 148, 48], [134, 10, 150, 8], [134, 15, 150, 13], [134, 19, 150, 17, "h"], [134, 20, 150, 18], [134, 23, 150, 21], [134, 24, 150, 22], [134, 26, 150, 24, "h"], [134, 27, 150, 25], [134, 30, 150, 28, "m"], [134, 31, 150, 29], [134, 33, 150, 31, "h"], [134, 34, 150, 32], [134, 38, 150, 36], [134, 39, 150, 37], [134, 41, 150, 39], [135, 12, 151, 10, "q"], [135, 13, 151, 11], [135, 14, 151, 12, "set"], [135, 17, 151, 15], [135, 18, 151, 16, "j"], [135, 19, 151, 17], [135, 21, 151, 19, "h"], [135, 22, 151, 20], [135, 24, 151, 22, "q"], [135, 25, 151, 23], [135, 26, 151, 24, "get"], [135, 29, 151, 27], [135, 30, 151, 28, "j"], [135, 31, 151, 29], [135, 33, 151, 31, "h"], [135, 34, 151, 32], [135, 35, 151, 33], [135, 38, 151, 36, "dot"], [135, 41, 151, 39], [135, 44, 151, 42, "q"], [135, 45, 151, 43], [135, 46, 151, 44, "get"], [135, 49, 151, 47], [135, 50, 151, 48, "i"], [135, 51, 151, 49], [135, 53, 151, 51, "h"], [135, 54, 151, 52], [135, 55, 151, 53], [135, 56, 151, 54], [136, 10, 152, 8], [137, 8, 153, 6], [138, 8, 155, 6], [138, 14, 155, 12, "norm"], [138, 18, 155, 16], [138, 21, 155, 19, "q"], [138, 22, 155, 20], [138, 23, 155, 21, "getRow"], [138, 29, 155, 27], [138, 30, 155, 28, "j"], [138, 31, 155, 29], [138, 32, 155, 30], [138, 33, 155, 31, "norm"], [138, 37, 155, 35], [138, 38, 155, 36], [138, 39, 155, 37], [139, 8, 157, 6], [139, 12, 157, 10, "norm"], [139, 16, 157, 14], [139, 19, 157, 17, "precisionErrorTolerance"], [139, 42, 157, 40], [139, 44, 157, 42], [140, 10, 158, 8], [141, 10, 159, 8], [141, 17, 159, 15], [141, 21, 159, 19], [142, 8, 160, 6], [143, 8, 162, 6], [143, 14, 162, 12, "inverseNorm"], [143, 25, 162, 23], [143, 28, 162, 26], [143, 31, 162, 29], [143, 34, 162, 32, "norm"], [143, 38, 162, 36], [144, 8, 164, 6], [144, 13, 164, 11], [144, 17, 164, 15, "h"], [144, 18, 164, 16], [144, 21, 164, 19], [144, 22, 164, 20], [144, 24, 164, 22, "h"], [144, 25, 164, 23], [144, 28, 164, 26, "m"], [144, 29, 164, 27], [144, 31, 164, 29, "h"], [144, 32, 164, 30], [144, 36, 164, 34], [144, 37, 164, 35], [144, 39, 164, 37], [145, 10, 165, 8, "q"], [145, 11, 165, 9], [145, 12, 165, 10, "set"], [145, 15, 165, 13], [145, 16, 165, 14, "j"], [145, 17, 165, 15], [145, 19, 165, 17, "h"], [145, 20, 165, 18], [145, 22, 165, 20, "q"], [145, 23, 165, 21], [145, 24, 165, 22, "get"], [145, 27, 165, 25], [145, 28, 165, 26, "j"], [145, 29, 165, 27], [145, 31, 165, 29, "h"], [145, 32, 165, 30], [145, 33, 165, 31], [145, 36, 165, 34, "inverseNorm"], [145, 47, 165, 45], [145, 48, 165, 46], [146, 8, 166, 6], [147, 8, 168, 6], [147, 13, 168, 11], [147, 17, 168, 15, "i"], [147, 18, 168, 16], [147, 21, 168, 19], [147, 22, 168, 20], [147, 24, 168, 22, "i"], [147, 25, 168, 23], [147, 28, 168, 26, "n"], [147, 29, 168, 27], [147, 31, 168, 29, "i"], [147, 32, 168, 30], [147, 36, 168, 34], [147, 37, 168, 35], [147, 39, 168, 37], [148, 10, 169, 8, "r"], [148, 11, 169, 9], [148, 12, 169, 10, "set"], [148, 15, 169, 13], [148, 16, 169, 14, "j"], [148, 17, 169, 15], [148, 19, 169, 17, "i"], [148, 20, 169, 18], [148, 22, 169, 20, "i"], [148, 23, 169, 21], [148, 26, 169, 24, "j"], [148, 27, 169, 25], [148, 30, 169, 28], [148, 33, 169, 31], [148, 36, 169, 34, "q"], [148, 37, 169, 35], [148, 38, 169, 36, "getRow"], [148, 44, 169, 42], [148, 45, 169, 43, "j"], [148, 46, 169, 44], [148, 47, 169, 45], [148, 48, 169, 46, "dot"], [148, 51, 169, 49], [148, 52, 169, 50, "a"], [148, 53, 169, 51], [148, 54, 169, 52, "getRow"], [148, 60, 169, 58], [148, 61, 169, 59, "i"], [148, 62, 169, 60], [148, 63, 169, 61], [148, 64, 169, 62], [148, 65, 169, 63], [149, 8, 170, 6], [150, 6, 171, 4], [150, 7, 171, 5], [150, 8, 171, 6], [151, 6, 172, 4], [153, 6, 175, 4], [153, 12, 175, 10, "wy"], [153, 14, 175, 12], [153, 17, 175, 15], [153, 21, 175, 19, "Vector"], [153, 27, 175, 25], [153, 28, 175, 26, "m"], [153, 29, 175, 27], [153, 30, 175, 28], [154, 6, 177, 4], [154, 11, 177, 9], [154, 15, 177, 13, "h"], [154, 16, 177, 14], [154, 19, 177, 17], [154, 20, 177, 18], [154, 22, 177, 20, "h"], [154, 23, 177, 21], [154, 26, 177, 24, "m"], [154, 27, 177, 25], [154, 29, 177, 27, "h"], [154, 30, 177, 28], [154, 34, 177, 32], [154, 35, 177, 33], [154, 37, 177, 35], [155, 8, 178, 6, "wy"], [155, 10, 178, 8], [155, 11, 178, 9, "set"], [155, 14, 178, 12], [155, 15, 178, 13, "h"], [155, 16, 178, 14], [155, 18, 178, 16], [155, 22, 178, 20], [155, 23, 178, 21, "y"], [155, 24, 178, 22], [155, 25, 178, 23, "h"], [155, 26, 178, 24], [155, 27, 178, 25], [155, 30, 178, 28], [155, 34, 178, 32], [155, 35, 178, 33, "w"], [155, 36, 178, 34], [155, 37, 178, 35, "h"], [155, 38, 178, 36], [155, 39, 178, 37], [155, 40, 178, 38], [156, 6, 179, 4], [157, 6, 181, 4], [157, 11, 181, 9], [157, 15, 181, 13, "i"], [157, 16, 181, 14], [157, 19, 181, 17, "n"], [157, 20, 181, 18], [157, 23, 181, 21], [157, 24, 181, 22], [157, 26, 181, 24, "i"], [157, 27, 181, 25], [157, 31, 181, 29], [157, 32, 181, 30], [157, 34, 181, 32, "i"], [157, 35, 181, 33], [157, 39, 181, 37], [157, 40, 181, 38], [157, 42, 181, 40], [158, 8, 182, 6, "result"], [158, 14, 182, 12], [158, 15, 182, 13, "coefficients"], [158, 27, 182, 25], [158, 28, 182, 26, "i"], [158, 29, 182, 27], [158, 30, 182, 28], [158, 33, 182, 31, "q"], [158, 34, 182, 32], [158, 35, 182, 33, "getRow"], [158, 41, 182, 39], [158, 42, 182, 40, "i"], [158, 43, 182, 41], [158, 44, 182, 42], [158, 45, 182, 43, "dot"], [158, 48, 182, 46], [158, 49, 182, 47, "wy"], [158, 51, 182, 49], [158, 52, 182, 50], [159, 8, 184, 6], [159, 13, 184, 11], [159, 17, 184, 15, "j"], [159, 18, 184, 16], [159, 21, 184, 19, "n"], [159, 22, 184, 20], [159, 25, 184, 23], [159, 26, 184, 24], [159, 28, 184, 26, "j"], [159, 29, 184, 27], [159, 32, 184, 30, "i"], [159, 33, 184, 31], [159, 35, 184, 33, "j"], [159, 36, 184, 34], [159, 40, 184, 38], [159, 41, 184, 39], [159, 43, 184, 41], [160, 10, 185, 8, "result"], [160, 16, 185, 14], [160, 17, 185, 15, "coefficients"], [160, 29, 185, 27], [160, 30, 185, 28, "i"], [160, 31, 185, 29], [160, 32, 185, 30], [160, 36, 185, 34, "r"], [160, 37, 185, 35], [160, 38, 185, 36, "get"], [160, 41, 185, 39], [160, 42, 185, 40, "i"], [160, 43, 185, 41], [160, 45, 185, 43, "j"], [160, 46, 185, 44], [160, 47, 185, 45], [160, 50, 185, 48, "result"], [160, 56, 185, 54], [160, 57, 185, 55, "coefficients"], [160, 69, 185, 67], [160, 70, 185, 68, "j"], [160, 71, 185, 69], [160, 72, 185, 70], [161, 8, 186, 6], [162, 8, 188, 6, "result"], [162, 14, 188, 12], [162, 15, 188, 13, "coefficients"], [162, 27, 188, 25], [162, 28, 188, 26, "i"], [162, 29, 188, 27], [162, 30, 188, 28], [162, 34, 188, 32, "r"], [162, 35, 188, 33], [162, 36, 188, 34, "get"], [162, 39, 188, 37], [162, 40, 188, 38, "i"], [162, 41, 188, 39], [162, 43, 188, 41, "i"], [162, 44, 188, 42], [162, 45, 188, 43], [163, 6, 189, 4], [164, 6, 191, 4], [164, 13, 191, 11, "result"], [164, 19, 191, 17], [165, 4, 192, 2], [166, 2, 194, 0], [167, 2, 194, 1, "exports"], [167, 9, 194, 1], [167, 10, 194, 1, "default"], [167, 17, 194, 1], [167, 20, 194, 1, "LeastSquareSolver"], [167, 37, 194, 1], [168, 0, 194, 1], [168, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "Vector", "Vector#constructor", "Vector.fromVOL", "Vector#get", "Vector#set", "Vector#dot", "Vector#norm", "Matrix", "Matrix#constructor", "Matrix#get", "Matrix#set", "Matrix#getRow", "PolynomialFit", "PolynomialFit#constructor", "LeastSquareSolver", "constructor", "solve"], "mappings": "AAA,iNC;ACI;ECC;GDU;EEE;GFM;EGE;GHE;EIE;GJE;EKE;GLQ;EME;GNE;CDE;AQE;ECC;GDO;EEE;GFE;EGE;GHE;EIE;GJE;CRE;AaG;ECQ;GDI;CbE;eeI;ECO;GDU;EEK;GF6E;CfE"}}, "type": "js/module"}]}