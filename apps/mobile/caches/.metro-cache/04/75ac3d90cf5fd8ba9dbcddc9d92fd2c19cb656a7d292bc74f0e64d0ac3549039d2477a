{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 26, "index": 94}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 95}, "end": {"line": 5, "column": 31, "index": 126}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 127}, "end": {"line": 6, "column": 62, "index": 189}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 190}, "end": {"line": 7, "column": 48, "index": 238}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Badge = Badge;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _native = require(_dependencyMap[3], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[4], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[5], \"react\"));\n  var _reactNative = require(_dependencyMap[6], \"react-native\");\n  var _jsxRuntime = require(_dependencyMap[7], \"react/jsx-runtime\");\n  var _excluded = [\"children\", \"style\", \"visible\", \"size\"],\n    _excluded2 = [\"backgroundColor\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var useNativeDriver = _reactNative.Platform.OS !== 'web';\n  function Badge(_ref) {\n    var children = _ref.children,\n      style = _ref.style,\n      _ref$visible = _ref.visible,\n      visible = _ref$visible === void 0 ? true : _ref$visible,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 18 : _ref$size,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var _React$useState = React.useState(() => new _reactNative.Animated.Value(visible ? 1 : 0)),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 1),\n      opacity = _React$useState2[0];\n    var _React$useState3 = React.useState(visible),\n      _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n      rendered = _React$useState4[0],\n      setRendered = _React$useState4[1];\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    React.useEffect(() => {\n      if (!rendered) {\n        return;\n      }\n      _reactNative.Animated.timing(opacity, {\n        toValue: visible ? 1 : 0,\n        duration: 150,\n        useNativeDriver\n      }).start(_ref2 => {\n        var finished = _ref2.finished;\n        if (finished && !visible) {\n          setRendered(false);\n        }\n      });\n      return () => opacity.stopAnimation();\n    }, [opacity, rendered, visible]);\n    if (!rendered) {\n      if (visible) {\n        setRendered(true);\n      } else {\n        return null;\n      }\n    }\n\n    // @ts-expect-error: backgroundColor definitely exists\n    var _ref3 = _reactNative.StyleSheet.flatten(style) || {},\n      _ref3$backgroundColor = _ref3.backgroundColor,\n      backgroundColor = _ref3$backgroundColor === void 0 ? colors.notification : _ref3$backgroundColor,\n      restStyle = (0, _objectWithoutProperties2.default)(_ref3, _excluded2);\n    var textColor = (0, _color.default)(backgroundColor).isLight() ? 'black' : 'white';\n    var borderRadius = size / 2;\n    var fontSize = Math.floor(size * 3 / 4);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.Text, {\n      numberOfLines: 1,\n      style: [{\n        transform: [{\n          scale: opacity.interpolate({\n            inputRange: [0, 1],\n            outputRange: [0.5, 1]\n          })\n        }],\n        color: textColor,\n        lineHeight: size - 1,\n        height: size,\n        minWidth: size,\n        opacity,\n        backgroundColor,\n        fontSize,\n        borderRadius\n      }, fonts.regular, styles.container, restStyle],\n      ...rest,\n      children: children\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      alignSelf: 'flex-end',\n      textAlign: 'center',\n      paddingHorizontal: 4,\n      overflow: 'hidden'\n    }\n  });\n});", "lineCount": 100, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Badge"], [8, 15, 1, 13], [8, 18, 1, 13, "Badge"], [8, 23, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_objectWithoutProperties2"], [10, 31, 1, 13], [10, 34, 1, 13, "_interopRequireDefault"], [10, 56, 1, 13], [10, 57, 1, 13, "require"], [10, 64, 1, 13], [10, 65, 1, 13, "_dependencyMap"], [10, 79, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_native"], [11, 13, 3, 0], [11, 16, 3, 0, "require"], [11, 23, 3, 0], [11, 24, 3, 0, "_dependencyMap"], [11, 38, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_color"], [12, 12, 4, 0], [12, 15, 4, 0, "_interopRequireDefault"], [12, 37, 4, 0], [12, 38, 4, 0, "require"], [12, 45, 4, 0], [12, 46, 4, 0, "_dependencyMap"], [12, 60, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "React"], [13, 11, 5, 0], [13, 14, 5, 0, "_interopRequireWildcard"], [13, 37, 5, 0], [13, 38, 5, 0, "require"], [13, 45, 5, 0], [13, 46, 5, 0, "_dependencyMap"], [13, 60, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_reactNative"], [14, 18, 6, 0], [14, 21, 6, 0, "require"], [14, 28, 6, 0], [14, 29, 6, 0, "_dependencyMap"], [14, 43, 6, 0], [15, 2, 7, 0], [15, 6, 7, 0, "_jsxRuntime"], [15, 17, 7, 0], [15, 20, 7, 0, "require"], [15, 27, 7, 0], [15, 28, 7, 0, "_dependencyMap"], [15, 42, 7, 0], [16, 2, 7, 48], [16, 6, 7, 48, "_excluded"], [16, 15, 7, 48], [17, 4, 7, 48, "_excluded2"], [17, 14, 7, 48], [18, 2, 7, 48], [18, 11, 7, 48, "_interopRequireWildcard"], [18, 35, 7, 48, "e"], [18, 36, 7, 48], [18, 38, 7, 48, "t"], [18, 39, 7, 48], [18, 68, 7, 48, "WeakMap"], [18, 75, 7, 48], [18, 81, 7, 48, "r"], [18, 82, 7, 48], [18, 89, 7, 48, "WeakMap"], [18, 96, 7, 48], [18, 100, 7, 48, "n"], [18, 101, 7, 48], [18, 108, 7, 48, "WeakMap"], [18, 115, 7, 48], [18, 127, 7, 48, "_interopRequireWildcard"], [18, 150, 7, 48], [18, 162, 7, 48, "_interopRequireWildcard"], [18, 163, 7, 48, "e"], [18, 164, 7, 48], [18, 166, 7, 48, "t"], [18, 167, 7, 48], [18, 176, 7, 48, "t"], [18, 177, 7, 48], [18, 181, 7, 48, "e"], [18, 182, 7, 48], [18, 186, 7, 48, "e"], [18, 187, 7, 48], [18, 188, 7, 48, "__esModule"], [18, 198, 7, 48], [18, 207, 7, 48, "e"], [18, 208, 7, 48], [18, 214, 7, 48, "o"], [18, 215, 7, 48], [18, 217, 7, 48, "i"], [18, 218, 7, 48], [18, 220, 7, 48, "f"], [18, 221, 7, 48], [18, 226, 7, 48, "__proto__"], [18, 235, 7, 48], [18, 243, 7, 48, "default"], [18, 250, 7, 48], [18, 252, 7, 48, "e"], [18, 253, 7, 48], [18, 270, 7, 48, "e"], [18, 271, 7, 48], [18, 294, 7, 48, "e"], [18, 295, 7, 48], [18, 320, 7, 48, "e"], [18, 321, 7, 48], [18, 330, 7, 48, "f"], [18, 331, 7, 48], [18, 337, 7, 48, "o"], [18, 338, 7, 48], [18, 341, 7, 48, "t"], [18, 342, 7, 48], [18, 345, 7, 48, "n"], [18, 346, 7, 48], [18, 349, 7, 48, "r"], [18, 350, 7, 48], [18, 358, 7, 48, "o"], [18, 359, 7, 48], [18, 360, 7, 48, "has"], [18, 363, 7, 48], [18, 364, 7, 48, "e"], [18, 365, 7, 48], [18, 375, 7, 48, "o"], [18, 376, 7, 48], [18, 377, 7, 48, "get"], [18, 380, 7, 48], [18, 381, 7, 48, "e"], [18, 382, 7, 48], [18, 385, 7, 48, "o"], [18, 386, 7, 48], [18, 387, 7, 48, "set"], [18, 390, 7, 48], [18, 391, 7, 48, "e"], [18, 392, 7, 48], [18, 394, 7, 48, "f"], [18, 395, 7, 48], [18, 409, 7, 48, "_t"], [18, 411, 7, 48], [18, 415, 7, 48, "e"], [18, 416, 7, 48], [18, 432, 7, 48, "_t"], [18, 434, 7, 48], [18, 441, 7, 48, "hasOwnProperty"], [18, 455, 7, 48], [18, 456, 7, 48, "call"], [18, 460, 7, 48], [18, 461, 7, 48, "e"], [18, 462, 7, 48], [18, 464, 7, 48, "_t"], [18, 466, 7, 48], [18, 473, 7, 48, "i"], [18, 474, 7, 48], [18, 478, 7, 48, "o"], [18, 479, 7, 48], [18, 482, 7, 48, "Object"], [18, 488, 7, 48], [18, 489, 7, 48, "defineProperty"], [18, 503, 7, 48], [18, 508, 7, 48, "Object"], [18, 514, 7, 48], [18, 515, 7, 48, "getOwnPropertyDescriptor"], [18, 539, 7, 48], [18, 540, 7, 48, "e"], [18, 541, 7, 48], [18, 543, 7, 48, "_t"], [18, 545, 7, 48], [18, 552, 7, 48, "i"], [18, 553, 7, 48], [18, 554, 7, 48, "get"], [18, 557, 7, 48], [18, 561, 7, 48, "i"], [18, 562, 7, 48], [18, 563, 7, 48, "set"], [18, 566, 7, 48], [18, 570, 7, 48, "o"], [18, 571, 7, 48], [18, 572, 7, 48, "f"], [18, 573, 7, 48], [18, 575, 7, 48, "_t"], [18, 577, 7, 48], [18, 579, 7, 48, "i"], [18, 580, 7, 48], [18, 584, 7, 48, "f"], [18, 585, 7, 48], [18, 586, 7, 48, "_t"], [18, 588, 7, 48], [18, 592, 7, 48, "e"], [18, 593, 7, 48], [18, 594, 7, 48, "_t"], [18, 596, 7, 48], [18, 607, 7, 48, "f"], [18, 608, 7, 48], [18, 613, 7, 48, "e"], [18, 614, 7, 48], [18, 616, 7, 48, "t"], [18, 617, 7, 48], [19, 2, 8, 0], [19, 6, 8, 6, "useNativeDriver"], [19, 21, 8, 21], [19, 24, 8, 24, "Platform"], [19, 45, 8, 32], [19, 46, 8, 33, "OS"], [19, 48, 8, 35], [19, 53, 8, 40], [19, 58, 8, 45], [20, 2, 9, 7], [20, 11, 9, 16, "Badge"], [20, 16, 9, 21, "Badge"], [20, 17, 9, 21, "_ref"], [20, 21, 9, 21], [20, 23, 15, 3], [21, 4, 15, 3], [21, 8, 10, 2, "children"], [21, 16, 10, 10], [21, 19, 10, 10, "_ref"], [21, 23, 10, 10], [21, 24, 10, 2, "children"], [21, 32, 10, 10], [22, 6, 11, 2, "style"], [22, 11, 11, 7], [22, 14, 11, 7, "_ref"], [22, 18, 11, 7], [22, 19, 11, 2, "style"], [22, 24, 11, 7], [23, 6, 11, 7, "_ref$visible"], [23, 18, 11, 7], [23, 21, 11, 7, "_ref"], [23, 25, 11, 7], [23, 26, 12, 2, "visible"], [23, 33, 12, 9], [24, 6, 12, 2, "visible"], [24, 13, 12, 9], [24, 16, 12, 9, "_ref$visible"], [24, 28, 12, 9], [24, 42, 12, 12], [24, 46, 12, 16], [24, 49, 12, 16, "_ref$visible"], [24, 61, 12, 16], [25, 6, 12, 16, "_ref$size"], [25, 15, 12, 16], [25, 18, 12, 16, "_ref"], [25, 22, 12, 16], [25, 23, 13, 2, "size"], [25, 27, 13, 6], [26, 6, 13, 2, "size"], [26, 10, 13, 6], [26, 13, 13, 6, "_ref$size"], [26, 22, 13, 6], [26, 36, 13, 9], [26, 38, 13, 11], [26, 41, 13, 11, "_ref$size"], [26, 50, 13, 11], [27, 6, 14, 5, "rest"], [27, 10, 14, 9], [27, 17, 14, 9, "_objectWithoutProperties2"], [27, 42, 14, 9], [27, 43, 14, 9, "default"], [27, 50, 14, 9], [27, 52, 14, 9, "_ref"], [27, 56, 14, 9], [27, 58, 14, 9, "_excluded"], [27, 67, 14, 9], [28, 4, 16, 2], [28, 8, 16, 2, "_React$useState"], [28, 23, 16, 2], [28, 26, 16, 20, "React"], [28, 31, 16, 25], [28, 32, 16, 26, "useState"], [28, 40, 16, 34], [28, 41, 16, 35], [28, 47, 16, 41], [28, 51, 16, 45, "Animated"], [28, 72, 16, 53], [28, 73, 16, 54, "Value"], [28, 78, 16, 59], [28, 79, 16, 60, "visible"], [28, 86, 16, 67], [28, 89, 16, 70], [28, 90, 16, 71], [28, 93, 16, 74], [28, 94, 16, 75], [28, 95, 16, 76], [28, 96, 16, 77], [29, 6, 16, 77, "_React$useState2"], [29, 22, 16, 77], [29, 29, 16, 77, "_slicedToArray2"], [29, 44, 16, 77], [29, 45, 16, 77, "default"], [29, 52, 16, 77], [29, 54, 16, 77, "_React$useState"], [29, 69, 16, 77], [30, 6, 16, 9, "opacity"], [30, 13, 16, 16], [30, 16, 16, 16, "_React$useState2"], [30, 32, 16, 16], [31, 4, 17, 2], [31, 8, 17, 2, "_React$useState3"], [31, 24, 17, 2], [31, 27, 17, 34, "React"], [31, 32, 17, 39], [31, 33, 17, 40, "useState"], [31, 41, 17, 48], [31, 42, 17, 49, "visible"], [31, 49, 17, 56], [31, 50, 17, 57], [32, 6, 17, 57, "_React$useState4"], [32, 22, 17, 57], [32, 29, 17, 57, "_slicedToArray2"], [32, 44, 17, 57], [32, 45, 17, 57, "default"], [32, 52, 17, 57], [32, 54, 17, 57, "_React$useState3"], [32, 70, 17, 57], [33, 6, 17, 9, "rendered"], [33, 14, 17, 17], [33, 17, 17, 17, "_React$useState4"], [33, 33, 17, 17], [34, 6, 17, 19, "setRendered"], [34, 17, 17, 30], [34, 20, 17, 30, "_React$useState4"], [34, 36, 17, 30], [35, 4, 18, 2], [35, 8, 18, 2, "_useTheme"], [35, 17, 18, 2], [35, 20, 21, 6], [35, 24, 21, 6, "useTheme"], [35, 40, 21, 14], [35, 42, 21, 15], [35, 43, 21, 16], [36, 6, 19, 4, "colors"], [36, 12, 19, 10], [36, 15, 19, 10, "_useTheme"], [36, 24, 19, 10], [36, 25, 19, 4, "colors"], [36, 31, 19, 10], [37, 6, 20, 4, "fonts"], [37, 11, 20, 9], [37, 14, 20, 9, "_useTheme"], [37, 23, 20, 9], [37, 24, 20, 4, "fonts"], [37, 29, 20, 9], [38, 4, 22, 2, "React"], [38, 9, 22, 7], [38, 10, 22, 8, "useEffect"], [38, 19, 22, 17], [38, 20, 22, 18], [38, 26, 22, 24], [39, 6, 23, 4], [39, 10, 23, 8], [39, 11, 23, 9, "rendered"], [39, 19, 23, 17], [39, 21, 23, 19], [40, 8, 24, 6], [41, 6, 25, 4], [42, 6, 26, 4, "Animated"], [42, 27, 26, 12], [42, 28, 26, 13, "timing"], [42, 34, 26, 19], [42, 35, 26, 20, "opacity"], [42, 42, 26, 27], [42, 44, 26, 29], [43, 8, 27, 6, "toValue"], [43, 15, 27, 13], [43, 17, 27, 15, "visible"], [43, 24, 27, 22], [43, 27, 27, 25], [43, 28, 27, 26], [43, 31, 27, 29], [43, 32, 27, 30], [44, 8, 28, 6, "duration"], [44, 16, 28, 14], [44, 18, 28, 16], [44, 21, 28, 19], [45, 8, 29, 6, "useNativeDriver"], [46, 6, 30, 4], [46, 7, 30, 5], [46, 8, 30, 6], [46, 9, 30, 7, "start"], [46, 14, 30, 12], [46, 15, 30, 13, "_ref2"], [46, 20, 30, 13], [46, 24, 32, 10], [47, 8, 32, 10], [47, 12, 31, 6, "finished"], [47, 20, 31, 14], [47, 23, 31, 14, "_ref2"], [47, 28, 31, 14], [47, 29, 31, 6, "finished"], [47, 37, 31, 14], [48, 8, 33, 6], [48, 12, 33, 10, "finished"], [48, 20, 33, 18], [48, 24, 33, 22], [48, 25, 33, 23, "visible"], [48, 32, 33, 30], [48, 34, 33, 32], [49, 10, 34, 8, "setRendered"], [49, 21, 34, 19], [49, 22, 34, 20], [49, 27, 34, 25], [49, 28, 34, 26], [50, 8, 35, 6], [51, 6, 36, 4], [51, 7, 36, 5], [51, 8, 36, 6], [52, 6, 37, 4], [52, 13, 37, 11], [52, 19, 37, 17, "opacity"], [52, 26, 37, 24], [52, 27, 37, 25, "stopAnimation"], [52, 40, 37, 38], [52, 41, 37, 39], [52, 42, 37, 40], [53, 4, 38, 2], [53, 5, 38, 3], [53, 7, 38, 5], [53, 8, 38, 6, "opacity"], [53, 15, 38, 13], [53, 17, 38, 15, "rendered"], [53, 25, 38, 23], [53, 27, 38, 25, "visible"], [53, 34, 38, 32], [53, 35, 38, 33], [53, 36, 38, 34], [54, 4, 39, 2], [54, 8, 39, 6], [54, 9, 39, 7, "rendered"], [54, 17, 39, 15], [54, 19, 39, 17], [55, 6, 40, 4], [55, 10, 40, 8, "visible"], [55, 17, 40, 15], [55, 19, 40, 17], [56, 8, 41, 6, "setRendered"], [56, 19, 41, 17], [56, 20, 41, 18], [56, 24, 41, 22], [56, 25, 41, 23], [57, 6, 42, 4], [57, 7, 42, 5], [57, 13, 42, 11], [58, 8, 43, 6], [58, 15, 43, 13], [58, 19, 43, 17], [59, 6, 44, 4], [60, 4, 45, 2], [62, 4, 47, 2], [63, 4, 48, 2], [63, 8, 48, 2, "_ref3"], [63, 13, 48, 2], [63, 16, 51, 6, "StyleSheet"], [63, 39, 51, 16], [63, 40, 51, 17, "flatten"], [63, 47, 51, 24], [63, 48, 51, 25, "style"], [63, 53, 51, 30], [63, 54, 51, 31], [63, 58, 51, 35], [63, 59, 51, 36], [63, 60, 51, 37], [64, 6, 51, 37, "_ref3$backgroundColor"], [64, 27, 51, 37], [64, 30, 51, 37, "_ref3"], [64, 35, 51, 37], [64, 36, 49, 4, "backgroundColor"], [64, 51, 49, 19], [65, 6, 49, 4, "backgroundColor"], [65, 21, 49, 19], [65, 24, 49, 19, "_ref3$backgroundColor"], [65, 45, 49, 19], [65, 59, 49, 22, "colors"], [65, 65, 49, 28], [65, 66, 49, 29, "notification"], [65, 78, 49, 41], [65, 81, 49, 41, "_ref3$backgroundColor"], [65, 102, 49, 41], [66, 6, 50, 7, "restStyle"], [66, 15, 50, 16], [66, 22, 50, 16, "_objectWithoutProperties2"], [66, 47, 50, 16], [66, 48, 50, 16, "default"], [66, 55, 50, 16], [66, 57, 50, 16, "_ref3"], [66, 62, 50, 16], [66, 64, 50, 16, "_excluded2"], [66, 74, 50, 16], [67, 4, 52, 2], [67, 8, 52, 8, "textColor"], [67, 17, 52, 17], [67, 20, 52, 20], [67, 24, 52, 20, "color"], [67, 38, 52, 25], [67, 40, 52, 26, "backgroundColor"], [67, 55, 52, 41], [67, 56, 52, 42], [67, 57, 52, 43, "isLight"], [67, 64, 52, 50], [67, 65, 52, 51], [67, 66, 52, 52], [67, 69, 52, 55], [67, 76, 52, 62], [67, 79, 52, 65], [67, 86, 52, 72], [68, 4, 53, 2], [68, 8, 53, 8, "borderRadius"], [68, 20, 53, 20], [68, 23, 53, 23, "size"], [68, 27, 53, 27], [68, 30, 53, 30], [68, 31, 53, 31], [69, 4, 54, 2], [69, 8, 54, 8, "fontSize"], [69, 16, 54, 16], [69, 19, 54, 19, "Math"], [69, 23, 54, 23], [69, 24, 54, 24, "floor"], [69, 29, 54, 29], [69, 30, 54, 30, "size"], [69, 34, 54, 34], [69, 37, 54, 37], [69, 38, 54, 38], [69, 41, 54, 41], [69, 42, 54, 42], [69, 43, 54, 43], [70, 4, 55, 2], [70, 11, 55, 9], [70, 24, 55, 22], [70, 28, 55, 22, "_jsx"], [70, 43, 55, 26], [70, 45, 55, 27, "Animated"], [70, 66, 55, 35], [70, 67, 55, 36, "Text"], [70, 71, 55, 40], [70, 73, 55, 42], [71, 6, 56, 4, "numberOfLines"], [71, 19, 56, 17], [71, 21, 56, 19], [71, 22, 56, 20], [72, 6, 57, 4, "style"], [72, 11, 57, 9], [72, 13, 57, 11], [72, 14, 57, 12], [73, 8, 58, 6, "transform"], [73, 17, 58, 15], [73, 19, 58, 17], [73, 20, 58, 18], [74, 10, 59, 8, "scale"], [74, 15, 59, 13], [74, 17, 59, 15, "opacity"], [74, 24, 59, 22], [74, 25, 59, 23, "interpolate"], [74, 36, 59, 34], [74, 37, 59, 35], [75, 12, 60, 10, "inputRange"], [75, 22, 60, 20], [75, 24, 60, 22], [75, 25, 60, 23], [75, 26, 60, 24], [75, 28, 60, 26], [75, 29, 60, 27], [75, 30, 60, 28], [76, 12, 61, 10, "outputRange"], [76, 23, 61, 21], [76, 25, 61, 23], [76, 26, 61, 24], [76, 29, 61, 27], [76, 31, 61, 29], [76, 32, 61, 30], [77, 10, 62, 8], [77, 11, 62, 9], [78, 8, 63, 6], [78, 9, 63, 7], [78, 10, 63, 8], [79, 8, 64, 6, "color"], [79, 13, 64, 11], [79, 15, 64, 13, "textColor"], [79, 24, 64, 22], [80, 8, 65, 6, "lineHeight"], [80, 18, 65, 16], [80, 20, 65, 18, "size"], [80, 24, 65, 22], [80, 27, 65, 25], [80, 28, 65, 26], [81, 8, 66, 6, "height"], [81, 14, 66, 12], [81, 16, 66, 14, "size"], [81, 20, 66, 18], [82, 8, 67, 6, "min<PERSON><PERSON><PERSON>"], [82, 16, 67, 14], [82, 18, 67, 16, "size"], [82, 22, 67, 20], [83, 8, 68, 6, "opacity"], [83, 15, 68, 13], [84, 8, 69, 6, "backgroundColor"], [84, 23, 69, 21], [85, 8, 70, 6, "fontSize"], [85, 16, 70, 14], [86, 8, 71, 6, "borderRadius"], [87, 6, 72, 4], [87, 7, 72, 5], [87, 9, 72, 7, "fonts"], [87, 14, 72, 12], [87, 15, 72, 13, "regular"], [87, 22, 72, 20], [87, 24, 72, 22, "styles"], [87, 30, 72, 28], [87, 31, 72, 29, "container"], [87, 40, 72, 38], [87, 42, 72, 40, "restStyle"], [87, 51, 72, 49], [87, 52, 72, 50], [88, 6, 73, 4], [88, 9, 73, 7, "rest"], [88, 13, 73, 11], [89, 6, 74, 4, "children"], [89, 14, 74, 12], [89, 16, 74, 14, "children"], [90, 4, 75, 2], [90, 5, 75, 3], [90, 6, 75, 4], [91, 2, 76, 0], [92, 2, 77, 0], [92, 6, 77, 6, "styles"], [92, 12, 77, 12], [92, 15, 77, 15, "StyleSheet"], [92, 38, 77, 25], [92, 39, 77, 26, "create"], [92, 45, 77, 32], [92, 46, 77, 33], [93, 4, 78, 2, "container"], [93, 13, 78, 11], [93, 15, 78, 13], [94, 6, 79, 4, "alignSelf"], [94, 15, 79, 13], [94, 17, 79, 15], [94, 27, 79, 25], [95, 6, 80, 4, "textAlign"], [95, 15, 80, 13], [95, 17, 80, 15], [95, 25, 80, 23], [96, 6, 81, 4, "paddingHorizontal"], [96, 23, 81, 21], [96, 25, 81, 23], [96, 26, 81, 24], [97, 6, 82, 4, "overflow"], [97, 14, 82, 12], [97, 16, 82, 14], [98, 4, 83, 2], [99, 2, 84, 0], [99, 3, 84, 1], [99, 4, 84, 2], [100, 0, 84, 3], [100, 3]], "functionMap": {"names": ["<global>", "Badge", "React.useState$argument_0", "React.useEffect$argument_0", "Animated.timing.start$argument_0", "<anonymous>"], "mappings": "AAA;OCQ;mCCO,yCD;kBEM;aCQ;KDM;WEC,6BF;GFC;CDsC"}}, "type": "js/module"}]}