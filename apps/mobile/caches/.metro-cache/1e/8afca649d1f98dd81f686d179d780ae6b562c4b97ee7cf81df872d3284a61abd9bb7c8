{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 42}, "end": {"line": 4, "column": 48, "index": 90}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./Badge.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 91}, "end": {"line": 5, "column": 35, "index": 126}}], "key": "MZFkiMuDDsiAZkLPfZB0JBL51Ng=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 127}, "end": {"line": 6, "column": 63, "index": 190}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TabBarIcon = TabBarIcon;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _Badge = require(_dependencyMap[3], \"./Badge.js\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  /**\n   * Icon sizes taken from Apple HIG\n   * https://developer.apple.com/design/human-interface-guidelines/tab-bars\n   */\n  var ICON_SIZE_WIDE = 31;\n  var ICON_SIZE_WIDE_COMPACT = 23;\n  var ICON_SIZE_TALL = 28;\n  var ICON_SIZE_TALL_COMPACT = 20;\n  var ICON_SIZE_ROUND = 25;\n  var ICON_SIZE_ROUND_COMPACT = 18;\n  var ICON_SIZE_MATERIAL = 24;\n  function TabBarIcon(_ref) {\n    var _ = _ref.route,\n      variant = _ref.variant,\n      size = _ref.size,\n      badge = _ref.badge,\n      badgeStyle = _ref.badgeStyle,\n      activeOpacity = _ref.activeOpacity,\n      inactiveOpacity = _ref.inactiveOpacity,\n      activeTintColor = _ref.activeTintColor,\n      inactiveTintColor = _ref.inactiveTintColor,\n      renderIcon = _ref.renderIcon,\n      allowFontScaling = _ref.allowFontScaling,\n      style = _ref.style;\n    var iconSize = variant === 'material' ? ICON_SIZE_MATERIAL : size === 'compact' ? ICON_SIZE_ROUND_COMPACT : ICON_SIZE_ROUND;\n\n    // We render the icon twice at the same position on top of each other:\n    // active and inactive one, so we can fade between them.\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {\n      style: [variant === 'material' ? styles.wrapperMaterial : size === 'compact' ? styles.wrapperUikitCompact : styles.wrapperUikit, style],\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        style: [styles.icon, {\n          opacity: activeOpacity,\n          // Workaround for react-native >= 0.54 layout bug\n          minWidth: iconSize\n        }],\n        children: renderIcon({\n          focused: true,\n          size: iconSize,\n          color: activeTintColor\n        })\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n        style: [styles.icon, {\n          opacity: inactiveOpacity\n        }],\n        children: renderIcon({\n          focused: false,\n          size: iconSize,\n          color: inactiveTintColor\n        })\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_Badge.Badge, {\n        visible: badge != null,\n        size: iconSize * 0.75,\n        allowFontScaling: allowFontScaling,\n        style: [styles.badge, badgeStyle],\n        children: badge\n      })]\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    icon: {\n      // We render the icon twice at the same position on top of each other:\n      // active and inactive one, so we can fade between them:\n      // Cover the whole iconContainer:\n      position: 'absolute',\n      alignSelf: 'center',\n      alignItems: 'center',\n      justifyContent: 'center',\n      height: '100%',\n      width: '100%'\n    },\n    wrapperUikit: {\n      width: ICON_SIZE_WIDE,\n      height: ICON_SIZE_TALL\n    },\n    wrapperUikitCompact: {\n      width: ICON_SIZE_WIDE_COMPACT,\n      height: ICON_SIZE_TALL_COMPACT\n    },\n    wrapperMaterial: {\n      width: ICON_SIZE_MATERIAL,\n      height: ICON_SIZE_MATERIAL\n    },\n    badge: {\n      position: 'absolute',\n      end: -3,\n      top: -3\n    }\n  });\n});", "lineCount": 102, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "TabBarIcon"], [8, 20, 1, 13], [8, 23, 1, 13, "TabBarIcon"], [8, 33, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_react"], [9, 12, 3, 0], [9, 15, 3, 0, "_interopRequireDefault"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_reactNative"], [10, 18, 4, 0], [10, 21, 4, 0, "require"], [10, 28, 4, 0], [10, 29, 4, 0, "_dependencyMap"], [10, 43, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_Badge"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_jsxRuntime"], [12, 17, 6, 0], [12, 20, 6, 0, "require"], [12, 27, 6, 0], [12, 28, 6, 0, "_dependencyMap"], [12, 42, 6, 0], [13, 2, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 2, 11, 0], [17, 6, 11, 6, "ICON_SIZE_WIDE"], [17, 20, 11, 20], [17, 23, 11, 23], [17, 25, 11, 25], [18, 2, 12, 0], [18, 6, 12, 6, "ICON_SIZE_WIDE_COMPACT"], [18, 28, 12, 28], [18, 31, 12, 31], [18, 33, 12, 33], [19, 2, 13, 0], [19, 6, 13, 6, "ICON_SIZE_TALL"], [19, 20, 13, 20], [19, 23, 13, 23], [19, 25, 13, 25], [20, 2, 14, 0], [20, 6, 14, 6, "ICON_SIZE_TALL_COMPACT"], [20, 28, 14, 28], [20, 31, 14, 31], [20, 33, 14, 33], [21, 2, 15, 0], [21, 6, 15, 6, "ICON_SIZE_ROUND"], [21, 21, 15, 21], [21, 24, 15, 24], [21, 26, 15, 26], [22, 2, 16, 0], [22, 6, 16, 6, "ICON_SIZE_ROUND_COMPACT"], [22, 29, 16, 29], [22, 32, 16, 32], [22, 34, 16, 34], [23, 2, 17, 0], [23, 6, 17, 6, "ICON_SIZE_MATERIAL"], [23, 24, 17, 24], [23, 27, 17, 27], [23, 29, 17, 29], [24, 2, 18, 7], [24, 11, 18, 16, "TabBarIcon"], [24, 21, 18, 26, "TabBarIcon"], [24, 22, 18, 26, "_ref"], [24, 26, 18, 26], [24, 28, 31, 3], [25, 4, 31, 3], [25, 8, 19, 9, "_"], [25, 9, 19, 10], [25, 12, 19, 10, "_ref"], [25, 16, 19, 10], [25, 17, 19, 2, "route"], [25, 22, 19, 7], [26, 6, 20, 2, "variant"], [26, 13, 20, 9], [26, 16, 20, 9, "_ref"], [26, 20, 20, 9], [26, 21, 20, 2, "variant"], [26, 28, 20, 9], [27, 6, 21, 2, "size"], [27, 10, 21, 6], [27, 13, 21, 6, "_ref"], [27, 17, 21, 6], [27, 18, 21, 2, "size"], [27, 22, 21, 6], [28, 6, 22, 2, "badge"], [28, 11, 22, 7], [28, 14, 22, 7, "_ref"], [28, 18, 22, 7], [28, 19, 22, 2, "badge"], [28, 24, 22, 7], [29, 6, 23, 2, "badgeStyle"], [29, 16, 23, 12], [29, 19, 23, 12, "_ref"], [29, 23, 23, 12], [29, 24, 23, 2, "badgeStyle"], [29, 34, 23, 12], [30, 6, 24, 2, "activeOpacity"], [30, 19, 24, 15], [30, 22, 24, 15, "_ref"], [30, 26, 24, 15], [30, 27, 24, 2, "activeOpacity"], [30, 40, 24, 15], [31, 6, 25, 2, "inactiveOpacity"], [31, 21, 25, 17], [31, 24, 25, 17, "_ref"], [31, 28, 25, 17], [31, 29, 25, 2, "inactiveOpacity"], [31, 44, 25, 17], [32, 6, 26, 2, "activeTintColor"], [32, 21, 26, 17], [32, 24, 26, 17, "_ref"], [32, 28, 26, 17], [32, 29, 26, 2, "activeTintColor"], [32, 44, 26, 17], [33, 6, 27, 2, "inactiveTintColor"], [33, 23, 27, 19], [33, 26, 27, 19, "_ref"], [33, 30, 27, 19], [33, 31, 27, 2, "inactiveTintColor"], [33, 48, 27, 19], [34, 6, 28, 2, "renderIcon"], [34, 16, 28, 12], [34, 19, 28, 12, "_ref"], [34, 23, 28, 12], [34, 24, 28, 2, "renderIcon"], [34, 34, 28, 12], [35, 6, 29, 2, "allowFontScaling"], [35, 22, 29, 18], [35, 25, 29, 18, "_ref"], [35, 29, 29, 18], [35, 30, 29, 2, "allowFontScaling"], [35, 46, 29, 18], [36, 6, 30, 2, "style"], [36, 11, 30, 7], [36, 14, 30, 7, "_ref"], [36, 18, 30, 7], [36, 19, 30, 2, "style"], [36, 24, 30, 7], [37, 4, 32, 2], [37, 8, 32, 8, "iconSize"], [37, 16, 32, 16], [37, 19, 32, 19, "variant"], [37, 26, 32, 26], [37, 31, 32, 31], [37, 41, 32, 41], [37, 44, 32, 44, "ICON_SIZE_MATERIAL"], [37, 62, 32, 62], [37, 65, 32, 65, "size"], [37, 69, 32, 69], [37, 74, 32, 74], [37, 83, 32, 83], [37, 86, 32, 86, "ICON_SIZE_ROUND_COMPACT"], [37, 109, 32, 109], [37, 112, 32, 112, "ICON_SIZE_ROUND"], [37, 127, 32, 127], [39, 4, 34, 2], [40, 4, 35, 2], [41, 4, 36, 2], [41, 11, 36, 9], [41, 24, 36, 22], [41, 28, 36, 22, "_jsxs"], [41, 44, 36, 27], [41, 46, 36, 28, "View"], [41, 63, 36, 32], [41, 65, 36, 34], [42, 6, 37, 4, "style"], [42, 11, 37, 9], [42, 13, 37, 11], [42, 14, 37, 12, "variant"], [42, 21, 37, 19], [42, 26, 37, 24], [42, 36, 37, 34], [42, 39, 37, 37, "styles"], [42, 45, 37, 43], [42, 46, 37, 44, "wrapperMaterial"], [42, 61, 37, 59], [42, 64, 37, 62, "size"], [42, 68, 37, 66], [42, 73, 37, 71], [42, 82, 37, 80], [42, 85, 37, 83, "styles"], [42, 91, 37, 89], [42, 92, 37, 90, "wrapperUikitCompact"], [42, 111, 37, 109], [42, 114, 37, 112, "styles"], [42, 120, 37, 118], [42, 121, 37, 119, "wrapperUikit"], [42, 133, 37, 131], [42, 135, 37, 133, "style"], [42, 140, 37, 138], [42, 141, 37, 139], [43, 6, 38, 4, "children"], [43, 14, 38, 12], [43, 16, 38, 14], [43, 17, 38, 15], [43, 30, 38, 28], [43, 34, 38, 28, "_jsx"], [43, 49, 38, 32], [43, 51, 38, 33, "View"], [43, 68, 38, 37], [43, 70, 38, 39], [44, 8, 39, 6, "style"], [44, 13, 39, 11], [44, 15, 39, 13], [44, 16, 39, 14, "styles"], [44, 22, 39, 20], [44, 23, 39, 21, "icon"], [44, 27, 39, 25], [44, 29, 39, 27], [45, 10, 40, 8, "opacity"], [45, 17, 40, 15], [45, 19, 40, 17, "activeOpacity"], [45, 32, 40, 30], [46, 10, 41, 8], [47, 10, 42, 8, "min<PERSON><PERSON><PERSON>"], [47, 18, 42, 16], [47, 20, 42, 18, "iconSize"], [48, 8, 43, 6], [48, 9, 43, 7], [48, 10, 43, 8], [49, 8, 44, 6, "children"], [49, 16, 44, 14], [49, 18, 44, 16, "renderIcon"], [49, 28, 44, 26], [49, 29, 44, 27], [50, 10, 45, 8, "focused"], [50, 17, 45, 15], [50, 19, 45, 17], [50, 23, 45, 21], [51, 10, 46, 8, "size"], [51, 14, 46, 12], [51, 16, 46, 14, "iconSize"], [51, 24, 46, 22], [52, 10, 47, 8, "color"], [52, 15, 47, 13], [52, 17, 47, 15, "activeTintColor"], [53, 8, 48, 6], [53, 9, 48, 7], [54, 6, 49, 4], [54, 7, 49, 5], [54, 8, 49, 6], [54, 10, 49, 8], [54, 23, 49, 21], [54, 27, 49, 21, "_jsx"], [54, 42, 49, 25], [54, 44, 49, 26, "View"], [54, 61, 49, 30], [54, 63, 49, 32], [55, 8, 50, 6, "style"], [55, 13, 50, 11], [55, 15, 50, 13], [55, 16, 50, 14, "styles"], [55, 22, 50, 20], [55, 23, 50, 21, "icon"], [55, 27, 50, 25], [55, 29, 50, 27], [56, 10, 51, 8, "opacity"], [56, 17, 51, 15], [56, 19, 51, 17, "inactiveOpacity"], [57, 8, 52, 6], [57, 9, 52, 7], [57, 10, 52, 8], [58, 8, 53, 6, "children"], [58, 16, 53, 14], [58, 18, 53, 16, "renderIcon"], [58, 28, 53, 26], [58, 29, 53, 27], [59, 10, 54, 8, "focused"], [59, 17, 54, 15], [59, 19, 54, 17], [59, 24, 54, 22], [60, 10, 55, 8, "size"], [60, 14, 55, 12], [60, 16, 55, 14, "iconSize"], [60, 24, 55, 22], [61, 10, 56, 8, "color"], [61, 15, 56, 13], [61, 17, 56, 15, "inactiveTintColor"], [62, 8, 57, 6], [62, 9, 57, 7], [63, 6, 58, 4], [63, 7, 58, 5], [63, 8, 58, 6], [63, 10, 58, 8], [63, 23, 58, 21], [63, 27, 58, 21, "_jsx"], [63, 42, 58, 25], [63, 44, 58, 26, "Badge"], [63, 56, 58, 31], [63, 58, 58, 33], [64, 8, 59, 6, "visible"], [64, 15, 59, 13], [64, 17, 59, 15, "badge"], [64, 22, 59, 20], [64, 26, 59, 24], [64, 30, 59, 28], [65, 8, 60, 6, "size"], [65, 12, 60, 10], [65, 14, 60, 12, "iconSize"], [65, 22, 60, 20], [65, 25, 60, 23], [65, 29, 60, 27], [66, 8, 61, 6, "allowFontScaling"], [66, 24, 61, 22], [66, 26, 61, 24, "allowFontScaling"], [66, 42, 61, 40], [67, 8, 62, 6, "style"], [67, 13, 62, 11], [67, 15, 62, 13], [67, 16, 62, 14, "styles"], [67, 22, 62, 20], [67, 23, 62, 21, "badge"], [67, 28, 62, 26], [67, 30, 62, 28, "badgeStyle"], [67, 40, 62, 38], [67, 41, 62, 39], [68, 8, 63, 6, "children"], [68, 16, 63, 14], [68, 18, 63, 16, "badge"], [69, 6, 64, 4], [69, 7, 64, 5], [69, 8, 64, 6], [70, 4, 65, 2], [70, 5, 65, 3], [70, 6, 65, 4], [71, 2, 66, 0], [72, 2, 67, 0], [72, 6, 67, 6, "styles"], [72, 12, 67, 12], [72, 15, 67, 15, "StyleSheet"], [72, 38, 67, 25], [72, 39, 67, 26, "create"], [72, 45, 67, 32], [72, 46, 67, 33], [73, 4, 68, 2, "icon"], [73, 8, 68, 6], [73, 10, 68, 8], [74, 6, 69, 4], [75, 6, 70, 4], [76, 6, 71, 4], [77, 6, 72, 4, "position"], [77, 14, 72, 12], [77, 16, 72, 14], [77, 26, 72, 24], [78, 6, 73, 4, "alignSelf"], [78, 15, 73, 13], [78, 17, 73, 15], [78, 25, 73, 23], [79, 6, 74, 4, "alignItems"], [79, 16, 74, 14], [79, 18, 74, 16], [79, 26, 74, 24], [80, 6, 75, 4, "justifyContent"], [80, 20, 75, 18], [80, 22, 75, 20], [80, 30, 75, 28], [81, 6, 76, 4, "height"], [81, 12, 76, 10], [81, 14, 76, 12], [81, 20, 76, 18], [82, 6, 77, 4, "width"], [82, 11, 77, 9], [82, 13, 77, 11], [83, 4, 78, 2], [83, 5, 78, 3], [84, 4, 79, 2, "wrapperUikit"], [84, 16, 79, 14], [84, 18, 79, 16], [85, 6, 80, 4, "width"], [85, 11, 80, 9], [85, 13, 80, 11, "ICON_SIZE_WIDE"], [85, 27, 80, 25], [86, 6, 81, 4, "height"], [86, 12, 81, 10], [86, 14, 81, 12, "ICON_SIZE_TALL"], [87, 4, 82, 2], [87, 5, 82, 3], [88, 4, 83, 2, "wrapperUikitCompact"], [88, 23, 83, 21], [88, 25, 83, 23], [89, 6, 84, 4, "width"], [89, 11, 84, 9], [89, 13, 84, 11, "ICON_SIZE_WIDE_COMPACT"], [89, 35, 84, 33], [90, 6, 85, 4, "height"], [90, 12, 85, 10], [90, 14, 85, 12, "ICON_SIZE_TALL_COMPACT"], [91, 4, 86, 2], [91, 5, 86, 3], [92, 4, 87, 2, "wrapperMaterial"], [92, 19, 87, 17], [92, 21, 87, 19], [93, 6, 88, 4, "width"], [93, 11, 88, 9], [93, 13, 88, 11, "ICON_SIZE_MATERIAL"], [93, 31, 88, 29], [94, 6, 89, 4, "height"], [94, 12, 89, 10], [94, 14, 89, 12, "ICON_SIZE_MATERIAL"], [95, 4, 90, 2], [95, 5, 90, 3], [96, 4, 91, 2, "badge"], [96, 9, 91, 7], [96, 11, 91, 9], [97, 6, 92, 4, "position"], [97, 14, 92, 12], [97, 16, 92, 14], [97, 26, 92, 24], [98, 6, 93, 4, "end"], [98, 9, 93, 7], [98, 11, 93, 9], [98, 12, 93, 10], [98, 13, 93, 11], [99, 6, 94, 4, "top"], [99, 9, 94, 7], [99, 11, 94, 9], [99, 12, 94, 10], [100, 4, 95, 2], [101, 2, 96, 0], [101, 3, 96, 1], [101, 4, 96, 2], [102, 0, 96, 3], [102, 3]], "functionMap": {"names": ["<global>", "TabBarIcon"], "mappings": "AAA;OCiB;CDgD"}}, "type": "js/module"}]}