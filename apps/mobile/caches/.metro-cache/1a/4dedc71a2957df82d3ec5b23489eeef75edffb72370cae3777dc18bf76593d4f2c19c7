{"dependencies": [{"name": "expo-router/entry-classic", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 189}, "end": {"line": 3, "column": 35, "index": 224}}], "key": "gCS4JS38TXu+VFH51aDUIJZco2k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  require(_dependencyMap[0], \"expo-router/entry-classic\");\n});", "lineCount": 3, "map": [[2, 2, 3, 0, "require"], [2, 9, 3, 0], [2, 10, 3, 0, "_dependencyMap"], [2, 24, 3, 0], [3, 0, 3, 35], [3, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}