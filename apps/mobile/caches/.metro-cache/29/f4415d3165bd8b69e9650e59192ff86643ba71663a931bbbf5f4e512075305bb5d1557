{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var CalendarCheck = exports.default = (0, _createLucideIcon.default)(\"CalendarCheck\", [[\"path\", {\n    d: \"M8 2v4\",\n    key: \"1cmpym\"\n  }], [\"path\", {\n    d: \"M16 2v4\",\n    key: \"4m81vk\"\n  }], [\"rect\", {\n    width: \"18\",\n    height: \"18\",\n    x: \"3\",\n    y: \"4\",\n    rx: \"2\",\n    key: \"1hopcy\"\n  }], [\"path\", {\n    d: \"M3 10h18\",\n    key: \"8toen8\"\n  }], [\"path\", {\n    d: \"m9 16 2 2 4-4\",\n    key: \"19s6y9\"\n  }]]);\n});", "lineCount": 35, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "CalendarCheck"], [15, 19, 10, 19], [15, 22, 10, 19, "exports"], [15, 29, 10, 19], [15, 30, 10, 19, "default"], [15, 37, 10, 19], [15, 40, 10, 22], [15, 44, 10, 22, "createLucideIcon"], [15, 69, 10, 38], [15, 71, 10, 39], [15, 86, 10, 54], [15, 88, 10, 56], [15, 89, 11, 2], [15, 90, 11, 3], [15, 96, 11, 9], [15, 98, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 15, 11, 24], [17, 4, 11, 26, "key"], [17, 7, 11, 29], [17, 9, 11, 31], [18, 2, 11, 40], [18, 3, 11, 41], [18, 4, 11, 42], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "width"], [22, 9, 13, 18], [22, 11, 13, 20], [22, 15, 13, 24], [23, 4, 13, 26, "height"], [23, 10, 13, 32], [23, 12, 13, 34], [23, 16, 13, 38], [24, 4, 13, 40, "x"], [24, 5, 13, 41], [24, 7, 13, 43], [24, 10, 13, 46], [25, 4, 13, 48, "y"], [25, 5, 13, 49], [25, 7, 13, 51], [25, 10, 13, 54], [26, 4, 13, 56, "rx"], [26, 6, 13, 58], [26, 8, 13, 60], [26, 11, 13, 63], [27, 4, 13, 65, "key"], [27, 7, 13, 68], [27, 9, 13, 70], [28, 2, 13, 79], [28, 3, 13, 80], [28, 4, 13, 81], [28, 6, 14, 2], [28, 7, 14, 3], [28, 13, 14, 9], [28, 15, 14, 11], [29, 4, 14, 13, "d"], [29, 5, 14, 14], [29, 7, 14, 16], [29, 17, 14, 26], [30, 4, 14, 28, "key"], [30, 7, 14, 31], [30, 9, 14, 33], [31, 2, 14, 42], [31, 3, 14, 43], [31, 4, 14, 44], [31, 6, 15, 2], [31, 7, 15, 3], [31, 13, 15, 9], [31, 15, 15, 11], [32, 4, 15, 13, "d"], [32, 5, 15, 14], [32, 7, 15, 16], [32, 22, 15, 31], [33, 4, 15, 33, "key"], [33, 7, 15, 36], [33, 9, 15, 38], [34, 2, 15, 47], [34, 3, 15, 48], [34, 4, 15, 49], [34, 5, 16, 1], [34, 6, 16, 2], [35, 0, 16, 3], [35, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}