{"dependencies": [{"name": "../platformFunctions/findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 69, "index": 84}}], "key": "1isdGYORv8bBV0ZCFH0po00eajE=", "exportNames": ["*"]}}, {"name": "../WorkletEventHandler.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 85}, "end": {"line": 4, "column": 64, "index": 149}}], "key": "e5fG6a6nTf5/kChbWzO7k8c/97s=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 150}, "end": {"line": 5, "column": 33, "index": 183}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NativeEventsManager = void 0;\n  var _findNodeHandle = require(_dependencyMap[0], \"../platformFunctions/findNodeHandle\");\n  var _WorkletEventHandler = require(_dependencyMap[1], \"../WorkletEventHandler.js\");\n  var _utils = require(_dependencyMap[2], \"./utils.js\");\n  class NativeEventsManager {\n    #managedComponent;\n    #componentOptions;\n    #eventViewTag = -1;\n    constructor(component, options) {\n      this.#managedComponent = component;\n      this.#componentOptions = options;\n      this.#eventViewTag = this.getEventViewTag();\n    }\n    attachEvents() {\n      executeForEachEventHandler(this.#managedComponent.props, (key, handler) => {\n        handler.registerForEvents(this.#eventViewTag, key);\n      });\n    }\n    detachEvents() {\n      executeForEachEventHandler(this.#managedComponent.props, (_key, handler) => {\n        handler.unregisterFromEvents(this.#eventViewTag);\n      });\n    }\n    updateEvents(prevProps) {\n      const computedEventTag = this.getEventViewTag(true);\n      // If the event view tag changes, we need to completely re-mount all events\n      if (this.#eventViewTag !== computedEventTag) {\n        // Remove all bindings from previous props that ran on the old viewTag\n        executeForEachEventHandler(prevProps, (_key, handler) => {\n          handler.unregisterFromEvents(this.#eventViewTag);\n        });\n        // We don't need to unregister from current (new) props, because their events weren't registered yet\n        // Replace the view tag\n        this.#eventViewTag = computedEventTag;\n        // Attach the events with a new viewTag\n        this.attachEvents();\n        return;\n      }\n      executeForEachEventHandler(prevProps, (key, prevHandler) => {\n        const newProp = this.#managedComponent.props[key];\n        if (!newProp) {\n          // Prop got deleted\n          prevHandler.unregisterFromEvents(this.#eventViewTag);\n        } else if (isWorkletEventHandler(newProp) && newProp.workletEventHandler !== prevHandler) {\n          // Prop got changed\n          prevHandler.unregisterFromEvents(this.#eventViewTag);\n          newProp.workletEventHandler.registerForEvents(this.#eventViewTag);\n        }\n      });\n      executeForEachEventHandler(this.#managedComponent.props, (key, handler) => {\n        if (!prevProps[key]) {\n          // Prop got added\n          handler.registerForEvents(this.#eventViewTag);\n        }\n      });\n    }\n    getEventViewTag(componentUpdate = false) {\n      // Get the tag for registering events - since the event emitting view can be nested inside the main component\n      const componentAnimatedRef = this.#managedComponent._componentRef;\n      if (componentAnimatedRef.getScrollableNode) {\n        /*\n          In most cases, getScrollableNode() returns a view tag, and findNodeHandle is not required. \n          However, to cover more exotic list cases, we will continue to use findNodeHandle \n          for consistency. For numerical values, findNodeHandle should return the value immediately, \n          as documented here: https://github.com/facebook/react/blob/91061073d57783c061889ac6720ef1ab7f0c2149/packages/react-native-renderer/src/ReactNativePublicCompat.js#L113\n        */\n        const scrollableNode = componentAnimatedRef.getScrollableNode();\n        if (typeof scrollableNode === 'number') {\n          return scrollableNode;\n        }\n        return (0, _findNodeHandle.findNodeHandle)(scrollableNode) ?? -1;\n      }\n      if (this.#componentOptions?.setNativeProps) {\n        // This case ensures backward compatibility with components that\n        // have their own setNativeProps method passed as an option.\n        return (0, _findNodeHandle.findNodeHandle)(this.#managedComponent) ?? -1;\n      }\n      if (!componentUpdate) {\n        // On the first render of a component, we may already receive a resolved view tag.\n        return this.#managedComponent.getComponentViewTag();\n      }\n      if (componentAnimatedRef.__nativeTag || componentAnimatedRef._nativeTag) {\n        /*\n          Fast path for native refs,\n          _nativeTag is used by Paper components,\n          __nativeTag is used by Fabric components.\n        */\n        return componentAnimatedRef.__nativeTag ?? componentAnimatedRef._nativeTag ?? -1;\n      }\n      /*\n        When a component is updated, a child could potentially change and have a different \n        view tag. This can occur with a GestureDetector component.\n      */\n      return (0, _findNodeHandle.findNodeHandle)(componentAnimatedRef) ?? -1;\n    }\n  }\n  exports.NativeEventsManager = NativeEventsManager;\n  function isWorkletEventHandler(prop) {\n    return (0, _utils.has)('workletEventHandler', prop) && prop.workletEventHandler instanceof _WorkletEventHandler.WorkletEventHandler;\n  }\n  function executeForEachEventHandler(props, callback) {\n    for (const key in props) {\n      const prop = props[key];\n      if (isWorkletEventHandler(prop)) {\n        callback(key, prop.workletEventHandler);\n      }\n    }\n  }\n});", "lineCount": 115, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "NativeEventsManager"], [7, 29, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_findNodeHandle"], [8, 21, 3, 0], [8, 24, 3, 0, "require"], [8, 31, 3, 0], [8, 32, 3, 0, "_dependencyMap"], [8, 46, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_WorkletEventHandler"], [9, 26, 4, 0], [9, 29, 4, 0, "require"], [9, 36, 4, 0], [9, 37, 4, 0, "_dependencyMap"], [9, 51, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_utils"], [10, 12, 5, 0], [10, 15, 5, 0, "require"], [10, 22, 5, 0], [10, 23, 5, 0, "_dependencyMap"], [10, 37, 5, 0], [11, 2, 6, 7], [11, 8, 6, 13, "NativeEventsManager"], [11, 27, 6, 32], [11, 28, 6, 33], [12, 4, 7, 2], [12, 5, 7, 3, "managedComponent"], [12, 21, 7, 19], [13, 4, 8, 2], [13, 5, 8, 3, "componentOptions"], [13, 21, 8, 19], [14, 4, 9, 2], [14, 5, 9, 3, "eventViewTag"], [14, 17, 9, 15], [14, 20, 9, 18], [14, 21, 9, 19], [14, 22, 9, 20], [15, 4, 10, 2, "constructor"], [15, 15, 10, 13, "constructor"], [15, 16, 10, 14, "component"], [15, 25, 10, 23], [15, 27, 10, 25, "options"], [15, 34, 10, 32], [15, 36, 10, 34], [16, 6, 11, 4], [16, 10, 11, 8], [16, 11, 11, 9], [16, 12, 11, 10, "managedComponent"], [16, 28, 11, 26], [16, 31, 11, 29, "component"], [16, 40, 11, 38], [17, 6, 12, 4], [17, 10, 12, 8], [17, 11, 12, 9], [17, 12, 12, 10, "componentOptions"], [17, 28, 12, 26], [17, 31, 12, 29, "options"], [17, 38, 12, 36], [18, 6, 13, 4], [18, 10, 13, 8], [18, 11, 13, 9], [18, 12, 13, 10, "eventViewTag"], [18, 24, 13, 22], [18, 27, 13, 25], [18, 31, 13, 29], [18, 32, 13, 30, "getEventViewTag"], [18, 47, 13, 45], [18, 48, 13, 46], [18, 49, 13, 47], [19, 4, 14, 2], [20, 4, 15, 2, "attachEvents"], [20, 16, 15, 14, "attachEvents"], [20, 17, 15, 14], [20, 19, 15, 17], [21, 6, 16, 4, "executeForEachEventHandler"], [21, 32, 16, 30], [21, 33, 16, 31], [21, 37, 16, 35], [21, 38, 16, 36], [21, 39, 16, 37, "managedComponent"], [21, 55, 16, 53], [21, 56, 16, 54, "props"], [21, 61, 16, 59], [21, 63, 16, 61], [21, 64, 16, 62, "key"], [21, 67, 16, 65], [21, 69, 16, 67, "handler"], [21, 76, 16, 74], [21, 81, 16, 79], [22, 8, 17, 6, "handler"], [22, 15, 17, 13], [22, 16, 17, 14, "registerForEvents"], [22, 33, 17, 31], [22, 34, 17, 32], [22, 38, 17, 36], [22, 39, 17, 37], [22, 40, 17, 38, "eventViewTag"], [22, 52, 17, 50], [22, 54, 17, 52, "key"], [22, 57, 17, 55], [22, 58, 17, 56], [23, 6, 18, 4], [23, 7, 18, 5], [23, 8, 18, 6], [24, 4, 19, 2], [25, 4, 20, 2, "detachEvents"], [25, 16, 20, 14, "detachEvents"], [25, 17, 20, 14], [25, 19, 20, 17], [26, 6, 21, 4, "executeForEachEventHandler"], [26, 32, 21, 30], [26, 33, 21, 31], [26, 37, 21, 35], [26, 38, 21, 36], [26, 39, 21, 37, "managedComponent"], [26, 55, 21, 53], [26, 56, 21, 54, "props"], [26, 61, 21, 59], [26, 63, 21, 61], [26, 64, 21, 62, "_key"], [26, 68, 21, 66], [26, 70, 21, 68, "handler"], [26, 77, 21, 75], [26, 82, 21, 80], [27, 8, 22, 6, "handler"], [27, 15, 22, 13], [27, 16, 22, 14, "unregisterFromEvents"], [27, 36, 22, 34], [27, 37, 22, 35], [27, 41, 22, 39], [27, 42, 22, 40], [27, 43, 22, 41, "eventViewTag"], [27, 55, 22, 53], [27, 56, 22, 54], [28, 6, 23, 4], [28, 7, 23, 5], [28, 8, 23, 6], [29, 4, 24, 2], [30, 4, 25, 2, "updateEvents"], [30, 16, 25, 14, "updateEvents"], [30, 17, 25, 15, "prevProps"], [30, 26, 25, 24], [30, 28, 25, 26], [31, 6, 26, 4], [31, 12, 26, 10, "computedEventTag"], [31, 28, 26, 26], [31, 31, 26, 29], [31, 35, 26, 33], [31, 36, 26, 34, "getEventViewTag"], [31, 51, 26, 49], [31, 52, 26, 50], [31, 56, 26, 54], [31, 57, 26, 55], [32, 6, 27, 4], [33, 6, 28, 4], [33, 10, 28, 8], [33, 14, 28, 12], [33, 15, 28, 13], [33, 16, 28, 14, "eventViewTag"], [33, 28, 28, 26], [33, 33, 28, 31, "computedEventTag"], [33, 49, 28, 47], [33, 51, 28, 49], [34, 8, 29, 6], [35, 8, 30, 6, "executeForEachEventHandler"], [35, 34, 30, 32], [35, 35, 30, 33, "prevProps"], [35, 44, 30, 42], [35, 46, 30, 44], [35, 47, 30, 45, "_key"], [35, 51, 30, 49], [35, 53, 30, 51, "handler"], [35, 60, 30, 58], [35, 65, 30, 63], [36, 10, 31, 8, "handler"], [36, 17, 31, 15], [36, 18, 31, 16, "unregisterFromEvents"], [36, 38, 31, 36], [36, 39, 31, 37], [36, 43, 31, 41], [36, 44, 31, 42], [36, 45, 31, 43, "eventViewTag"], [36, 57, 31, 55], [36, 58, 31, 56], [37, 8, 32, 6], [37, 9, 32, 7], [37, 10, 32, 8], [38, 8, 33, 6], [39, 8, 34, 6], [40, 8, 35, 6], [40, 12, 35, 10], [40, 13, 35, 11], [40, 14, 35, 12, "eventViewTag"], [40, 26, 35, 24], [40, 29, 35, 27, "computedEventTag"], [40, 45, 35, 43], [41, 8, 36, 6], [42, 8, 37, 6], [42, 12, 37, 10], [42, 13, 37, 11, "attachEvents"], [42, 25, 37, 23], [42, 26, 37, 24], [42, 27, 37, 25], [43, 8, 38, 6], [44, 6, 39, 4], [45, 6, 40, 4, "executeForEachEventHandler"], [45, 32, 40, 30], [45, 33, 40, 31, "prevProps"], [45, 42, 40, 40], [45, 44, 40, 42], [45, 45, 40, 43, "key"], [45, 48, 40, 46], [45, 50, 40, 48, "prev<PERSON><PERSON><PERSON>"], [45, 61, 40, 59], [45, 66, 40, 64], [46, 8, 41, 6], [46, 14, 41, 12, "newProp"], [46, 21, 41, 19], [46, 24, 41, 22], [46, 28, 41, 26], [46, 29, 41, 27], [46, 30, 41, 28, "managedComponent"], [46, 46, 41, 44], [46, 47, 41, 45, "props"], [46, 52, 41, 50], [46, 53, 41, 51, "key"], [46, 56, 41, 54], [46, 57, 41, 55], [47, 8, 42, 6], [47, 12, 42, 10], [47, 13, 42, 11, "newProp"], [47, 20, 42, 18], [47, 22, 42, 20], [48, 10, 43, 8], [49, 10, 44, 8, "prev<PERSON><PERSON><PERSON>"], [49, 21, 44, 19], [49, 22, 44, 20, "unregisterFromEvents"], [49, 42, 44, 40], [49, 43, 44, 41], [49, 47, 44, 45], [49, 48, 44, 46], [49, 49, 44, 47, "eventViewTag"], [49, 61, 44, 59], [49, 62, 44, 60], [50, 8, 45, 6], [50, 9, 45, 7], [50, 15, 45, 13], [50, 19, 45, 17, "isWorkletEventHandler"], [50, 40, 45, 38], [50, 41, 45, 39, "newProp"], [50, 48, 45, 46], [50, 49, 45, 47], [50, 53, 45, 51, "newProp"], [50, 60, 45, 58], [50, 61, 45, 59, "workletEventHandler"], [50, 80, 45, 78], [50, 85, 45, 83, "prev<PERSON><PERSON><PERSON>"], [50, 96, 45, 94], [50, 98, 45, 96], [51, 10, 46, 8], [52, 10, 47, 8, "prev<PERSON><PERSON><PERSON>"], [52, 21, 47, 19], [52, 22, 47, 20, "unregisterFromEvents"], [52, 42, 47, 40], [52, 43, 47, 41], [52, 47, 47, 45], [52, 48, 47, 46], [52, 49, 47, 47, "eventViewTag"], [52, 61, 47, 59], [52, 62, 47, 60], [53, 10, 48, 8, "newProp"], [53, 17, 48, 15], [53, 18, 48, 16, "workletEventHandler"], [53, 37, 48, 35], [53, 38, 48, 36, "registerForEvents"], [53, 55, 48, 53], [53, 56, 48, 54], [53, 60, 48, 58], [53, 61, 48, 59], [53, 62, 48, 60, "eventViewTag"], [53, 74, 48, 72], [53, 75, 48, 73], [54, 8, 49, 6], [55, 6, 50, 4], [55, 7, 50, 5], [55, 8, 50, 6], [56, 6, 51, 4, "executeForEachEventHandler"], [56, 32, 51, 30], [56, 33, 51, 31], [56, 37, 51, 35], [56, 38, 51, 36], [56, 39, 51, 37, "managedComponent"], [56, 55, 51, 53], [56, 56, 51, 54, "props"], [56, 61, 51, 59], [56, 63, 51, 61], [56, 64, 51, 62, "key"], [56, 67, 51, 65], [56, 69, 51, 67, "handler"], [56, 76, 51, 74], [56, 81, 51, 79], [57, 8, 52, 6], [57, 12, 52, 10], [57, 13, 52, 11, "prevProps"], [57, 22, 52, 20], [57, 23, 52, 21, "key"], [57, 26, 52, 24], [57, 27, 52, 25], [57, 29, 52, 27], [58, 10, 53, 8], [59, 10, 54, 8, "handler"], [59, 17, 54, 15], [59, 18, 54, 16, "registerForEvents"], [59, 35, 54, 33], [59, 36, 54, 34], [59, 40, 54, 38], [59, 41, 54, 39], [59, 42, 54, 40, "eventViewTag"], [59, 54, 54, 52], [59, 55, 54, 53], [60, 8, 55, 6], [61, 6, 56, 4], [61, 7, 56, 5], [61, 8, 56, 6], [62, 4, 57, 2], [63, 4, 58, 2, "getEventViewTag"], [63, 19, 58, 17, "getEventViewTag"], [63, 20, 58, 18, "componentUpdate"], [63, 35, 58, 33], [63, 38, 58, 36], [63, 43, 58, 41], [63, 45, 58, 43], [64, 6, 59, 4], [65, 6, 60, 4], [65, 12, 60, 10, "componentAnimatedRef"], [65, 32, 60, 30], [65, 35, 60, 33], [65, 39, 60, 37], [65, 40, 60, 38], [65, 41, 60, 39, "managedComponent"], [65, 57, 60, 55], [65, 58, 60, 56, "_componentRef"], [65, 71, 60, 69], [66, 6, 61, 4], [66, 10, 61, 8, "componentAnimatedRef"], [66, 30, 61, 28], [66, 31, 61, 29, "getScrollableNode"], [66, 48, 61, 46], [66, 50, 61, 48], [67, 8, 62, 6], [68, 0, 63, 0], [69, 0, 64, 0], [70, 0, 65, 0], [71, 0, 66, 0], [72, 0, 67, 0], [73, 8, 68, 6], [73, 14, 68, 12, "scrollableNode"], [73, 28, 68, 26], [73, 31, 68, 29, "componentAnimatedRef"], [73, 51, 68, 49], [73, 52, 68, 50, "getScrollableNode"], [73, 69, 68, 67], [73, 70, 68, 68], [73, 71, 68, 69], [74, 8, 69, 6], [74, 12, 69, 10], [74, 19, 69, 17, "scrollableNode"], [74, 33, 69, 31], [74, 38, 69, 36], [74, 46, 69, 44], [74, 48, 69, 46], [75, 10, 70, 8], [75, 17, 70, 15, "scrollableNode"], [75, 31, 70, 29], [76, 8, 71, 6], [77, 8, 72, 6], [77, 15, 72, 13], [77, 19, 72, 13, "findNodeHandle"], [77, 49, 72, 27], [77, 51, 72, 28, "scrollableNode"], [77, 65, 72, 42], [77, 66, 72, 43], [77, 70, 72, 47], [77, 71, 72, 48], [77, 72, 72, 49], [78, 6, 73, 4], [79, 6, 74, 4], [79, 10, 74, 8], [79, 14, 74, 12], [79, 15, 74, 13], [79, 16, 74, 14, "componentOptions"], [79, 32, 74, 30], [79, 34, 74, 32, "setNativeProps"], [79, 48, 74, 46], [79, 50, 74, 48], [80, 8, 75, 6], [81, 8, 76, 6], [82, 8, 77, 6], [82, 15, 77, 13], [82, 19, 77, 13, "findNodeHandle"], [82, 49, 77, 27], [82, 51, 77, 28], [82, 55, 77, 32], [82, 56, 77, 33], [82, 57, 77, 34, "managedComponent"], [82, 73, 77, 50], [82, 74, 77, 51], [82, 78, 77, 55], [82, 79, 77, 56], [82, 80, 77, 57], [83, 6, 78, 4], [84, 6, 79, 4], [84, 10, 79, 8], [84, 11, 79, 9, "componentUpdate"], [84, 26, 79, 24], [84, 28, 79, 26], [85, 8, 80, 6], [86, 8, 81, 6], [86, 15, 81, 13], [86, 19, 81, 17], [86, 20, 81, 18], [86, 21, 81, 19, "managedComponent"], [86, 37, 81, 35], [86, 38, 81, 36, "getComponentViewTag"], [86, 57, 81, 55], [86, 58, 81, 56], [86, 59, 81, 57], [87, 6, 82, 4], [88, 6, 83, 4], [88, 10, 83, 8, "componentAnimatedRef"], [88, 30, 83, 28], [88, 31, 83, 29, "__nativeTag"], [88, 42, 83, 40], [88, 46, 83, 44, "componentAnimatedRef"], [88, 66, 83, 64], [88, 67, 83, 65, "_nativeTag"], [88, 77, 83, 75], [88, 79, 83, 77], [89, 8, 84, 6], [90, 0, 85, 0], [91, 0, 86, 0], [92, 0, 87, 0], [93, 0, 88, 0], [94, 8, 89, 6], [94, 15, 89, 13, "componentAnimatedRef"], [94, 35, 89, 33], [94, 36, 89, 34, "__nativeTag"], [94, 47, 89, 45], [94, 51, 89, 49, "componentAnimatedRef"], [94, 71, 89, 69], [94, 72, 89, 70, "_nativeTag"], [94, 82, 89, 80], [94, 86, 89, 84], [94, 87, 89, 85], [94, 88, 89, 86], [95, 6, 90, 4], [96, 6, 91, 4], [97, 0, 92, 0], [98, 0, 93, 0], [99, 0, 94, 0], [100, 6, 95, 4], [100, 13, 95, 11], [100, 17, 95, 11, "findNodeHandle"], [100, 47, 95, 25], [100, 49, 95, 26, "componentAnimatedRef"], [100, 69, 95, 46], [100, 70, 95, 47], [100, 74, 95, 51], [100, 75, 95, 52], [100, 76, 95, 53], [101, 4, 96, 2], [102, 2, 97, 0], [103, 2, 97, 1, "exports"], [103, 9, 97, 1], [103, 10, 97, 1, "NativeEventsManager"], [103, 29, 97, 1], [103, 32, 97, 1, "NativeEventsManager"], [103, 51, 97, 1], [104, 2, 98, 0], [104, 11, 98, 9, "isWorkletEventHandler"], [104, 32, 98, 30, "isWorkletEventHandler"], [104, 33, 98, 31, "prop"], [104, 37, 98, 35], [104, 39, 98, 37], [105, 4, 99, 2], [105, 11, 99, 9], [105, 15, 99, 9, "has"], [105, 25, 99, 12], [105, 27, 99, 13], [105, 48, 99, 34], [105, 50, 99, 36, "prop"], [105, 54, 99, 40], [105, 55, 99, 41], [105, 59, 99, 45, "prop"], [105, 63, 99, 49], [105, 64, 99, 50, "workletEventHandler"], [105, 83, 99, 69], [105, 95, 99, 81, "WorkletEventHandler"], [105, 135, 99, 100], [106, 2, 100, 0], [107, 2, 101, 0], [107, 11, 101, 9, "executeForEachEventHandler"], [107, 37, 101, 35, "executeForEachEventHandler"], [107, 38, 101, 36, "props"], [107, 43, 101, 41], [107, 45, 101, 43, "callback"], [107, 53, 101, 51], [107, 55, 101, 53], [108, 4, 102, 2], [108, 9, 102, 7], [108, 15, 102, 13, "key"], [108, 18, 102, 16], [108, 22, 102, 20, "props"], [108, 27, 102, 25], [108, 29, 102, 27], [109, 6, 103, 4], [109, 12, 103, 10, "prop"], [109, 16, 103, 14], [109, 19, 103, 17, "props"], [109, 24, 103, 22], [109, 25, 103, 23, "key"], [109, 28, 103, 26], [109, 29, 103, 27], [110, 6, 104, 4], [110, 10, 104, 8, "isWorkletEventHandler"], [110, 31, 104, 29], [110, 32, 104, 30, "prop"], [110, 36, 104, 34], [110, 37, 104, 35], [110, 39, 104, 37], [111, 8, 105, 6, "callback"], [111, 16, 105, 14], [111, 17, 105, 15, "key"], [111, 20, 105, 18], [111, 22, 105, 20, "prop"], [111, 26, 105, 24], [111, 27, 105, 25, "workletEventHandler"], [111, 46, 105, 44], [111, 47, 105, 45], [112, 6, 106, 4], [113, 4, 107, 2], [114, 2, 108, 0], [115, 0, 108, 1], [115, 3]], "functionMap": {"names": ["<global>", "NativeEventsManager", "constructor", "attachEvents", "executeForEachEventHandler$argument_1", "detachEvents", "updateEvents", "getEventViewTag", "isWorkletEventHandler", "executeForEachEventHandler"], "mappings": "AAA;OCK;ECI;GDI;EEC;6DCC;KDE;GFC;EIC;6DDC;KCE;GJC;EKC;4CFK;OEE;0CFQ;KEU;6DFC;KEK;GLC;EMC;GNsC;CDC;AQC;CRE;ASC;CTO"}}, "type": "js/module"}]}