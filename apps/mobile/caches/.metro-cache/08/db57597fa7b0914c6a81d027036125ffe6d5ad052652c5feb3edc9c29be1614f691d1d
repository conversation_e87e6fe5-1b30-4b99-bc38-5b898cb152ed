{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useEventEmitter = useEventEmitter;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to manage the event system used by the navigator to notify screens of various events.\n   */\n  function useEventEmitter(listen) {\n    var listenRef = React.useRef(listen);\n    React.useEffect(() => {\n      listenRef.current = listen;\n    });\n    var listeners = React.useRef(Object.create(null));\n    var create = React.useCallback(target => {\n      var removeListener = (type, callback) => {\n        var callbacks = listeners.current[type] ? listeners.current[type][target] : undefined;\n        if (!callbacks) {\n          return;\n        }\n        var index = callbacks.indexOf(callback);\n        if (index > -1) {\n          callbacks.splice(index, 1);\n        }\n      };\n      var addListener = (type, callback) => {\n        listeners.current[type] = listeners.current[type] || {};\n        listeners.current[type][target] = listeners.current[type][target] || [];\n        listeners.current[type][target].push(callback);\n        var removed = false;\n        return () => {\n          // Prevent removing other listeners when unsubscribing same listener multiple times\n          if (!removed) {\n            removed = true;\n            removeListener(type, callback);\n          }\n        };\n      };\n      return {\n        addListener,\n        removeListener\n      };\n    }, []);\n    var emit = React.useCallback(_ref => {\n      var type = _ref.type,\n        data = _ref.data,\n        target = _ref.target,\n        canPreventDefault = _ref.canPreventDefault;\n      var items = listeners.current[type] || {};\n\n      // Copy the current list of callbacks in case they are mutated during execution\n      var callbacks = target !== undefined ? items[target]?.slice() : [].concat(...Object.keys(items).map(t => items[t])).filter((cb, i, self) => self.lastIndexOf(cb) === i);\n      var event = {\n        get type() {\n          return type;\n        }\n      };\n      if (target !== undefined) {\n        Object.defineProperty(event, 'target', {\n          enumerable: true,\n          get() {\n            return target;\n          }\n        });\n      }\n      if (data !== undefined) {\n        Object.defineProperty(event, 'data', {\n          enumerable: true,\n          get() {\n            return data;\n          }\n        });\n      }\n      if (canPreventDefault) {\n        var defaultPrevented = false;\n        Object.defineProperties(event, {\n          defaultPrevented: {\n            enumerable: true,\n            get() {\n              return defaultPrevented;\n            }\n          },\n          preventDefault: {\n            enumerable: true,\n            value() {\n              defaultPrevented = true;\n            }\n          }\n        });\n      }\n      listenRef.current?.(event);\n      callbacks?.forEach(cb => cb(event));\n      return event;\n    }, []);\n    return React.useMemo(() => ({\n      create,\n      emit\n    }), [create, emit]);\n  }\n});", "lineCount": 104, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useEventEmitter"], [7, 25, 1, 13], [7, 28, 1, 13, "useEventEmitter"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 11, 3, 31, "_interopRequireWildcard"], [9, 35, 3, 31, "e"], [9, 36, 3, 31], [9, 38, 3, 31, "t"], [9, 39, 3, 31], [9, 68, 3, 31, "WeakMap"], [9, 75, 3, 31], [9, 81, 3, 31, "r"], [9, 82, 3, 31], [9, 89, 3, 31, "WeakMap"], [9, 96, 3, 31], [9, 100, 3, 31, "n"], [9, 101, 3, 31], [9, 108, 3, 31, "WeakMap"], [9, 115, 3, 31], [9, 127, 3, 31, "_interopRequireWildcard"], [9, 150, 3, 31], [9, 162, 3, 31, "_interopRequireWildcard"], [9, 163, 3, 31, "e"], [9, 164, 3, 31], [9, 166, 3, 31, "t"], [9, 167, 3, 31], [9, 176, 3, 31, "t"], [9, 177, 3, 31], [9, 181, 3, 31, "e"], [9, 182, 3, 31], [9, 186, 3, 31, "e"], [9, 187, 3, 31], [9, 188, 3, 31, "__esModule"], [9, 198, 3, 31], [9, 207, 3, 31, "e"], [9, 208, 3, 31], [9, 214, 3, 31, "o"], [9, 215, 3, 31], [9, 217, 3, 31, "i"], [9, 218, 3, 31], [9, 220, 3, 31, "f"], [9, 221, 3, 31], [9, 226, 3, 31, "__proto__"], [9, 235, 3, 31], [9, 243, 3, 31, "default"], [9, 250, 3, 31], [9, 252, 3, 31, "e"], [9, 253, 3, 31], [9, 270, 3, 31, "e"], [9, 271, 3, 31], [9, 294, 3, 31, "e"], [9, 295, 3, 31], [9, 320, 3, 31, "e"], [9, 321, 3, 31], [9, 330, 3, 31, "f"], [9, 331, 3, 31], [9, 337, 3, 31, "o"], [9, 338, 3, 31], [9, 341, 3, 31, "t"], [9, 342, 3, 31], [9, 345, 3, 31, "n"], [9, 346, 3, 31], [9, 349, 3, 31, "r"], [9, 350, 3, 31], [9, 358, 3, 31, "o"], [9, 359, 3, 31], [9, 360, 3, 31, "has"], [9, 363, 3, 31], [9, 364, 3, 31, "e"], [9, 365, 3, 31], [9, 375, 3, 31, "o"], [9, 376, 3, 31], [9, 377, 3, 31, "get"], [9, 380, 3, 31], [9, 381, 3, 31, "e"], [9, 382, 3, 31], [9, 385, 3, 31, "o"], [9, 386, 3, 31], [9, 387, 3, 31, "set"], [9, 390, 3, 31], [9, 391, 3, 31, "e"], [9, 392, 3, 31], [9, 394, 3, 31, "f"], [9, 395, 3, 31], [9, 409, 3, 31, "_t"], [9, 411, 3, 31], [9, 415, 3, 31, "e"], [9, 416, 3, 31], [9, 432, 3, 31, "_t"], [9, 434, 3, 31], [9, 441, 3, 31, "hasOwnProperty"], [9, 455, 3, 31], [9, 456, 3, 31, "call"], [9, 460, 3, 31], [9, 461, 3, 31, "e"], [9, 462, 3, 31], [9, 464, 3, 31, "_t"], [9, 466, 3, 31], [9, 473, 3, 31, "i"], [9, 474, 3, 31], [9, 478, 3, 31, "o"], [9, 479, 3, 31], [9, 482, 3, 31, "Object"], [9, 488, 3, 31], [9, 489, 3, 31, "defineProperty"], [9, 503, 3, 31], [9, 508, 3, 31, "Object"], [9, 514, 3, 31], [9, 515, 3, 31, "getOwnPropertyDescriptor"], [9, 539, 3, 31], [9, 540, 3, 31, "e"], [9, 541, 3, 31], [9, 543, 3, 31, "_t"], [9, 545, 3, 31], [9, 552, 3, 31, "i"], [9, 553, 3, 31], [9, 554, 3, 31, "get"], [9, 557, 3, 31], [9, 561, 3, 31, "i"], [9, 562, 3, 31], [9, 563, 3, 31, "set"], [9, 566, 3, 31], [9, 570, 3, 31, "o"], [9, 571, 3, 31], [9, 572, 3, 31, "f"], [9, 573, 3, 31], [9, 575, 3, 31, "_t"], [9, 577, 3, 31], [9, 579, 3, 31, "i"], [9, 580, 3, 31], [9, 584, 3, 31, "f"], [9, 585, 3, 31], [9, 586, 3, 31, "_t"], [9, 588, 3, 31], [9, 592, 3, 31, "e"], [9, 593, 3, 31], [9, 594, 3, 31, "_t"], [9, 596, 3, 31], [9, 607, 3, 31, "f"], [9, 608, 3, 31], [9, 613, 3, 31, "e"], [9, 614, 3, 31], [9, 616, 3, 31, "t"], [9, 617, 3, 31], [10, 2, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 2, 7, 7], [13, 11, 7, 16, "useEventEmitter"], [13, 26, 7, 31, "useEventEmitter"], [13, 27, 7, 32, "listen"], [13, 33, 7, 38], [13, 35, 7, 40], [14, 4, 8, 2], [14, 8, 8, 8, "listenRef"], [14, 17, 8, 17], [14, 20, 8, 20, "React"], [14, 25, 8, 25], [14, 26, 8, 26, "useRef"], [14, 32, 8, 32], [14, 33, 8, 33, "listen"], [14, 39, 8, 39], [14, 40, 8, 40], [15, 4, 9, 2, "React"], [15, 9, 9, 7], [15, 10, 9, 8, "useEffect"], [15, 19, 9, 17], [15, 20, 9, 18], [15, 26, 9, 24], [16, 6, 10, 4, "listenRef"], [16, 15, 10, 13], [16, 16, 10, 14, "current"], [16, 23, 10, 21], [16, 26, 10, 24, "listen"], [16, 32, 10, 30], [17, 4, 11, 2], [17, 5, 11, 3], [17, 6, 11, 4], [18, 4, 12, 2], [18, 8, 12, 8, "listeners"], [18, 17, 12, 17], [18, 20, 12, 20, "React"], [18, 25, 12, 25], [18, 26, 12, 26, "useRef"], [18, 32, 12, 32], [18, 33, 12, 33, "Object"], [18, 39, 12, 39], [18, 40, 12, 40, "create"], [18, 46, 12, 46], [18, 47, 12, 47], [18, 51, 12, 51], [18, 52, 12, 52], [18, 53, 12, 53], [19, 4, 13, 2], [19, 8, 13, 8, "create"], [19, 14, 13, 14], [19, 17, 13, 17, "React"], [19, 22, 13, 22], [19, 23, 13, 23, "useCallback"], [19, 34, 13, 34], [19, 35, 13, 35, "target"], [19, 41, 13, 41], [19, 45, 13, 45], [20, 6, 14, 4], [20, 10, 14, 10, "removeListener"], [20, 24, 14, 24], [20, 27, 14, 27, "removeListener"], [20, 28, 14, 28, "type"], [20, 32, 14, 32], [20, 34, 14, 34, "callback"], [20, 42, 14, 42], [20, 47, 14, 47], [21, 8, 15, 6], [21, 12, 15, 12, "callbacks"], [21, 21, 15, 21], [21, 24, 15, 24, "listeners"], [21, 33, 15, 33], [21, 34, 15, 34, "current"], [21, 41, 15, 41], [21, 42, 15, 42, "type"], [21, 46, 15, 46], [21, 47, 15, 47], [21, 50, 15, 50, "listeners"], [21, 59, 15, 59], [21, 60, 15, 60, "current"], [21, 67, 15, 67], [21, 68, 15, 68, "type"], [21, 72, 15, 72], [21, 73, 15, 73], [21, 74, 15, 74, "target"], [21, 80, 15, 80], [21, 81, 15, 81], [21, 84, 15, 84, "undefined"], [21, 93, 15, 93], [22, 8, 16, 6], [22, 12, 16, 10], [22, 13, 16, 11, "callbacks"], [22, 22, 16, 20], [22, 24, 16, 22], [23, 10, 17, 8], [24, 8, 18, 6], [25, 8, 19, 6], [25, 12, 19, 12, "index"], [25, 17, 19, 17], [25, 20, 19, 20, "callbacks"], [25, 29, 19, 29], [25, 30, 19, 30, "indexOf"], [25, 37, 19, 37], [25, 38, 19, 38, "callback"], [25, 46, 19, 46], [25, 47, 19, 47], [26, 8, 20, 6], [26, 12, 20, 10, "index"], [26, 17, 20, 15], [26, 20, 20, 18], [26, 21, 20, 19], [26, 22, 20, 20], [26, 24, 20, 22], [27, 10, 21, 8, "callbacks"], [27, 19, 21, 17], [27, 20, 21, 18, "splice"], [27, 26, 21, 24], [27, 27, 21, 25, "index"], [27, 32, 21, 30], [27, 34, 21, 32], [27, 35, 21, 33], [27, 36, 21, 34], [28, 8, 22, 6], [29, 6, 23, 4], [29, 7, 23, 5], [30, 6, 24, 4], [30, 10, 24, 10, "addListener"], [30, 21, 24, 21], [30, 24, 24, 24, "addListener"], [30, 25, 24, 25, "type"], [30, 29, 24, 29], [30, 31, 24, 31, "callback"], [30, 39, 24, 39], [30, 44, 24, 44], [31, 8, 25, 6, "listeners"], [31, 17, 25, 15], [31, 18, 25, 16, "current"], [31, 25, 25, 23], [31, 26, 25, 24, "type"], [31, 30, 25, 28], [31, 31, 25, 29], [31, 34, 25, 32, "listeners"], [31, 43, 25, 41], [31, 44, 25, 42, "current"], [31, 51, 25, 49], [31, 52, 25, 50, "type"], [31, 56, 25, 54], [31, 57, 25, 55], [31, 61, 25, 59], [31, 62, 25, 60], [31, 63, 25, 61], [32, 8, 26, 6, "listeners"], [32, 17, 26, 15], [32, 18, 26, 16, "current"], [32, 25, 26, 23], [32, 26, 26, 24, "type"], [32, 30, 26, 28], [32, 31, 26, 29], [32, 32, 26, 30, "target"], [32, 38, 26, 36], [32, 39, 26, 37], [32, 42, 26, 40, "listeners"], [32, 51, 26, 49], [32, 52, 26, 50, "current"], [32, 59, 26, 57], [32, 60, 26, 58, "type"], [32, 64, 26, 62], [32, 65, 26, 63], [32, 66, 26, 64, "target"], [32, 72, 26, 70], [32, 73, 26, 71], [32, 77, 26, 75], [32, 79, 26, 77], [33, 8, 27, 6, "listeners"], [33, 17, 27, 15], [33, 18, 27, 16, "current"], [33, 25, 27, 23], [33, 26, 27, 24, "type"], [33, 30, 27, 28], [33, 31, 27, 29], [33, 32, 27, 30, "target"], [33, 38, 27, 36], [33, 39, 27, 37], [33, 40, 27, 38, "push"], [33, 44, 27, 42], [33, 45, 27, 43, "callback"], [33, 53, 27, 51], [33, 54, 27, 52], [34, 8, 28, 6], [34, 12, 28, 10, "removed"], [34, 19, 28, 17], [34, 22, 28, 20], [34, 27, 28, 25], [35, 8, 29, 6], [35, 15, 29, 13], [35, 21, 29, 19], [36, 10, 30, 8], [37, 10, 31, 8], [37, 14, 31, 12], [37, 15, 31, 13, "removed"], [37, 22, 31, 20], [37, 24, 31, 22], [38, 12, 32, 10, "removed"], [38, 19, 32, 17], [38, 22, 32, 20], [38, 26, 32, 24], [39, 12, 33, 10, "removeListener"], [39, 26, 33, 24], [39, 27, 33, 25, "type"], [39, 31, 33, 29], [39, 33, 33, 31, "callback"], [39, 41, 33, 39], [39, 42, 33, 40], [40, 10, 34, 8], [41, 8, 35, 6], [41, 9, 35, 7], [42, 6, 36, 4], [42, 7, 36, 5], [43, 6, 37, 4], [43, 13, 37, 11], [44, 8, 38, 6, "addListener"], [44, 19, 38, 17], [45, 8, 39, 6, "removeListener"], [46, 6, 40, 4], [46, 7, 40, 5], [47, 4, 41, 2], [47, 5, 41, 3], [47, 7, 41, 5], [47, 9, 41, 7], [47, 10, 41, 8], [48, 4, 42, 2], [48, 8, 42, 8, "emit"], [48, 12, 42, 12], [48, 15, 42, 15, "React"], [48, 20, 42, 20], [48, 21, 42, 21, "useCallback"], [48, 32, 42, 32], [48, 33, 42, 33, "_ref"], [48, 37, 42, 33], [48, 41, 47, 8], [49, 6, 47, 8], [49, 10, 43, 4, "type"], [49, 14, 43, 8], [49, 17, 43, 8, "_ref"], [49, 21, 43, 8], [49, 22, 43, 4, "type"], [49, 26, 43, 8], [50, 8, 44, 4, "data"], [50, 12, 44, 8], [50, 15, 44, 8, "_ref"], [50, 19, 44, 8], [50, 20, 44, 4, "data"], [50, 24, 44, 8], [51, 8, 45, 4, "target"], [51, 14, 45, 10], [51, 17, 45, 10, "_ref"], [51, 21, 45, 10], [51, 22, 45, 4, "target"], [51, 28, 45, 10], [52, 8, 46, 4, "canPreventDefault"], [52, 25, 46, 21], [52, 28, 46, 21, "_ref"], [52, 32, 46, 21], [52, 33, 46, 4, "canPreventDefault"], [52, 50, 46, 21], [53, 6, 48, 4], [53, 10, 48, 10, "items"], [53, 15, 48, 15], [53, 18, 48, 18, "listeners"], [53, 27, 48, 27], [53, 28, 48, 28, "current"], [53, 35, 48, 35], [53, 36, 48, 36, "type"], [53, 40, 48, 40], [53, 41, 48, 41], [53, 45, 48, 45], [53, 46, 48, 46], [53, 47, 48, 47], [55, 6, 50, 4], [56, 6, 51, 4], [56, 10, 51, 10, "callbacks"], [56, 19, 51, 19], [56, 22, 51, 22, "target"], [56, 28, 51, 28], [56, 33, 51, 33, "undefined"], [56, 42, 51, 42], [56, 45, 51, 45, "items"], [56, 50, 51, 50], [56, 51, 51, 51, "target"], [56, 57, 51, 57], [56, 58, 51, 58], [56, 60, 51, 60, "slice"], [56, 65, 51, 65], [56, 66, 51, 66], [56, 67, 51, 67], [56, 70, 51, 70], [56, 72, 51, 72], [56, 73, 51, 73, "concat"], [56, 79, 51, 79], [56, 80, 51, 80], [56, 83, 51, 83, "Object"], [56, 89, 51, 89], [56, 90, 51, 90, "keys"], [56, 94, 51, 94], [56, 95, 51, 95, "items"], [56, 100, 51, 100], [56, 101, 51, 101], [56, 102, 51, 102, "map"], [56, 105, 51, 105], [56, 106, 51, 106, "t"], [56, 107, 51, 107], [56, 111, 51, 111, "items"], [56, 116, 51, 116], [56, 117, 51, 117, "t"], [56, 118, 51, 118], [56, 119, 51, 119], [56, 120, 51, 120], [56, 121, 51, 121], [56, 122, 51, 122, "filter"], [56, 128, 51, 128], [56, 129, 51, 129], [56, 130, 51, 130, "cb"], [56, 132, 51, 132], [56, 134, 51, 134, "i"], [56, 135, 51, 135], [56, 137, 51, 137, "self"], [56, 141, 51, 141], [56, 146, 51, 146, "self"], [56, 150, 51, 150], [56, 151, 51, 151, "lastIndexOf"], [56, 162, 51, 162], [56, 163, 51, 163, "cb"], [56, 165, 51, 165], [56, 166, 51, 166], [56, 171, 51, 171, "i"], [56, 172, 51, 172], [56, 173, 51, 173], [57, 6, 52, 4], [57, 10, 52, 10, "event"], [57, 15, 52, 15], [57, 18, 52, 18], [58, 8, 53, 6], [58, 12, 53, 10, "type"], [58, 16, 53, 14, "type"], [58, 17, 53, 14], [58, 19, 53, 17], [59, 10, 54, 8], [59, 17, 54, 15, "type"], [59, 21, 54, 19], [60, 8, 55, 6], [61, 6, 56, 4], [61, 7, 56, 5], [62, 6, 57, 4], [62, 10, 57, 8, "target"], [62, 16, 57, 14], [62, 21, 57, 19, "undefined"], [62, 30, 57, 28], [62, 32, 57, 30], [63, 8, 58, 6, "Object"], [63, 14, 58, 12], [63, 15, 58, 13, "defineProperty"], [63, 29, 58, 27], [63, 30, 58, 28, "event"], [63, 35, 58, 33], [63, 37, 58, 35], [63, 45, 58, 43], [63, 47, 58, 45], [64, 10, 59, 8, "enumerable"], [64, 20, 59, 18], [64, 22, 59, 20], [64, 26, 59, 24], [65, 10, 60, 8, "get"], [65, 13, 60, 11, "get"], [65, 14, 60, 11], [65, 16, 60, 14], [66, 12, 61, 10], [66, 19, 61, 17, "target"], [66, 25, 61, 23], [67, 10, 62, 8], [68, 8, 63, 6], [68, 9, 63, 7], [68, 10, 63, 8], [69, 6, 64, 4], [70, 6, 65, 4], [70, 10, 65, 8, "data"], [70, 14, 65, 12], [70, 19, 65, 17, "undefined"], [70, 28, 65, 26], [70, 30, 65, 28], [71, 8, 66, 6, "Object"], [71, 14, 66, 12], [71, 15, 66, 13, "defineProperty"], [71, 29, 66, 27], [71, 30, 66, 28, "event"], [71, 35, 66, 33], [71, 37, 66, 35], [71, 43, 66, 41], [71, 45, 66, 43], [72, 10, 67, 8, "enumerable"], [72, 20, 67, 18], [72, 22, 67, 20], [72, 26, 67, 24], [73, 10, 68, 8, "get"], [73, 13, 68, 11, "get"], [73, 14, 68, 11], [73, 16, 68, 14], [74, 12, 69, 10], [74, 19, 69, 17, "data"], [74, 23, 69, 21], [75, 10, 70, 8], [76, 8, 71, 6], [76, 9, 71, 7], [76, 10, 71, 8], [77, 6, 72, 4], [78, 6, 73, 4], [78, 10, 73, 8, "canPreventDefault"], [78, 27, 73, 25], [78, 29, 73, 27], [79, 8, 74, 6], [79, 12, 74, 10, "defaultPrevented"], [79, 28, 74, 26], [79, 31, 74, 29], [79, 36, 74, 34], [80, 8, 75, 6, "Object"], [80, 14, 75, 12], [80, 15, 75, 13, "defineProperties"], [80, 31, 75, 29], [80, 32, 75, 30, "event"], [80, 37, 75, 35], [80, 39, 75, 37], [81, 10, 76, 8, "defaultPrevented"], [81, 26, 76, 24], [81, 28, 76, 26], [82, 12, 77, 10, "enumerable"], [82, 22, 77, 20], [82, 24, 77, 22], [82, 28, 77, 26], [83, 12, 78, 10, "get"], [83, 15, 78, 13, "get"], [83, 16, 78, 13], [83, 18, 78, 16], [84, 14, 79, 12], [84, 21, 79, 19, "defaultPrevented"], [84, 37, 79, 35], [85, 12, 80, 10], [86, 10, 81, 8], [86, 11, 81, 9], [87, 10, 82, 8, "preventDefault"], [87, 24, 82, 22], [87, 26, 82, 24], [88, 12, 83, 10, "enumerable"], [88, 22, 83, 20], [88, 24, 83, 22], [88, 28, 83, 26], [89, 12, 84, 10, "value"], [89, 17, 84, 15, "value"], [89, 18, 84, 15], [89, 20, 84, 18], [90, 14, 85, 12, "defaultPrevented"], [90, 30, 85, 28], [90, 33, 85, 31], [90, 37, 85, 35], [91, 12, 86, 10], [92, 10, 87, 8], [93, 8, 88, 6], [93, 9, 88, 7], [93, 10, 88, 8], [94, 6, 89, 4], [95, 6, 90, 4, "listenRef"], [95, 15, 90, 13], [95, 16, 90, 14, "current"], [95, 23, 90, 21], [95, 26, 90, 24, "event"], [95, 31, 90, 29], [95, 32, 90, 30], [96, 6, 91, 4, "callbacks"], [96, 15, 91, 13], [96, 17, 91, 15, "for<PERSON>ach"], [96, 24, 91, 22], [96, 25, 91, 23, "cb"], [96, 27, 91, 25], [96, 31, 91, 29, "cb"], [96, 33, 91, 31], [96, 34, 91, 32, "event"], [96, 39, 91, 37], [96, 40, 91, 38], [96, 41, 91, 39], [97, 6, 92, 4], [97, 13, 92, 11, "event"], [97, 18, 92, 16], [98, 4, 93, 2], [98, 5, 93, 3], [98, 7, 93, 5], [98, 9, 93, 7], [98, 10, 93, 8], [99, 4, 94, 2], [99, 11, 94, 9, "React"], [99, 16, 94, 14], [99, 17, 94, 15, "useMemo"], [99, 24, 94, 22], [99, 25, 94, 23], [99, 32, 94, 30], [100, 6, 95, 4, "create"], [100, 12, 95, 10], [101, 6, 96, 4, "emit"], [102, 4, 97, 2], [102, 5, 97, 3], [102, 6, 97, 4], [102, 8, 97, 6], [102, 9, 97, 7, "create"], [102, 15, 97, 13], [102, 17, 97, 15, "emit"], [102, 21, 97, 19], [102, 22, 97, 20], [102, 23, 97, 21], [103, 2, 98, 0], [104, 0, 98, 1], [104, 3]], "functionMap": {"names": ["<global>", "useEventEmitter", "React.useEffect$argument_0", "create", "removeListener", "addListener", "<anonymous>", "emit", "Object.keys.map$argument_0", "concat.filter$argument_0", "event.get__type", "Object.defineProperty$argument_2.get", "Object.defineProperties$argument_1.defaultPrevented.get", "Object.defineProperties$argument_1.preventDefault.value", "callbacks.forEach$argument_0", "React.useMemo$argument_0"], "mappings": "AAA;OCM;kBCE;GDE;mCEE;2BCC;KDS;wBEC;aCK;ODM;KFC;GFK;iCMC;0GCS,aD,UE,2CF;MGE;OHE;QIK;SJE;QIM;SJE;UKQ;WLE;UMI;WNE;uBOK,eP;GNE;uBcC;IdG;CDC"}}, "type": "js/module"}]}