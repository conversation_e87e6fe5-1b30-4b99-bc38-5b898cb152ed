{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Sandwich = exports.default = (0, _createLucideIcon.default)(\"Sandwich\", [[\"path\", {\n    d: \"m2.37 11.223 8.372-6.777a2 2 0 0 1 2.516 0l8.371 6.777\",\n    key: \"f1wd0e\"\n  }], [\"path\", {\n    d: \"M21 15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5.25\",\n    key: \"1pfu07\"\n  }], [\"path\", {\n    d: \"M3 15a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9\",\n    key: \"1oq9qw\"\n  }], [\"path\", {\n    d: \"m6.67 15 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2\",\n    key: \"1fnwu5\"\n  }], [\"rect\", {\n    width: \"20\",\n    height: \"4\",\n    x: \"2\",\n    y: \"11\",\n    rx: \"1\",\n    key: \"itshg\"\n  }]]);\n});", "lineCount": 35, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Sandwich"], [15, 16, 10, 14], [15, 19, 10, 14, "exports"], [15, 26, 10, 14], [15, 27, 10, 14, "default"], [15, 34, 10, 14], [15, 37, 10, 17], [15, 41, 10, 17, "createLucideIcon"], [15, 66, 10, 33], [15, 68, 10, 34], [15, 78, 10, 44], [15, 80, 10, 46], [15, 81, 11, 2], [15, 82, 11, 3], [15, 88, 11, 9], [15, 90, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 63, 11, 72], [17, 4, 11, 74, "key"], [17, 7, 11, 77], [17, 9, 11, 79], [18, 2, 11, 88], [18, 3, 11, 89], [18, 4, 11, 90], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 51, 12, 60], [20, 4, 12, 62, "key"], [20, 7, 12, 65], [20, 9, 12, 67], [21, 2, 12, 76], [21, 3, 12, 77], [21, 4, 12, 78], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 46, 13, 55], [23, 4, 13, 57, "key"], [23, 7, 13, 60], [23, 9, 13, 62], [24, 2, 13, 71], [24, 3, 13, 72], [24, 4, 13, 73], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 52, 14, 61], [26, 4, 14, 63, "key"], [26, 7, 14, 66], [26, 9, 14, 68], [27, 2, 14, 77], [27, 3, 14, 78], [27, 4, 14, 79], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "width"], [28, 9, 15, 18], [28, 11, 15, 20], [28, 15, 15, 24], [29, 4, 15, 26, "height"], [29, 10, 15, 32], [29, 12, 15, 34], [29, 15, 15, 37], [30, 4, 15, 39, "x"], [30, 5, 15, 40], [30, 7, 15, 42], [30, 10, 15, 45], [31, 4, 15, 47, "y"], [31, 5, 15, 48], [31, 7, 15, 50], [31, 11, 15, 54], [32, 4, 15, 56, "rx"], [32, 6, 15, 58], [32, 8, 15, 60], [32, 11, 15, 63], [33, 4, 15, 65, "key"], [33, 7, 15, 68], [33, 9, 15, 70], [34, 2, 15, 78], [34, 3, 15, 79], [34, 4, 15, 80], [34, 5, 16, 1], [34, 6, 16, 2], [35, 0, 16, 3], [35, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}