{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 99, "index": 99}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./injection", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 158}, "end": {"line": 4, "column": 67, "index": 225}}], "key": "Ri12V5UvvLPFQrEealibULxq4mg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDOMImperativeHandle = useDOMImperativeHandle;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _injection = require(_dependencyMap[1], \"./injection\");\n  /**\n   * A React `useImperativeHandle` like hook for DOM components.\n   *\n   */\n  function useDOMImperativeHandle(ref, init, deps) {\n    // @ts-expect-error: Added via react-native-webview\n    const isTargetWeb = typeof window.ReactNativeWebView === 'undefined';\n    const stubHandlerFactory = (0, _react.useCallback)(() => ({}), deps ?? []);\n\n    // This standard useImperativeHandle hook is serving for web\n    (0, _react.useImperativeHandle)(ref, isTargetWeb ? init : stubHandlerFactory, deps);\n\n    // This `globalThis._domRefProxy` is serving for native\n    (0, _react.useEffect)(() => {\n      if (!isTargetWeb) {\n        globalThis._domRefProxy = init();\n        // TODO(@kitten): Type `ReactNativeWebView` and the message data\n        // @ts-expect-error: Added via react-native-webview\n        window.ReactNativeWebView.postMessage(JSON.stringify({\n          type: _injection.REGISTER_DOM_IMPERATIVE_HANDLE_PROPS,\n          data: Object.keys(globalThis._domRefProxy)\n        }));\n      }\n      return () => {\n        if (!isTargetWeb) {\n          globalThis._domRefProxy = undefined;\n        }\n      };\n    }, deps);\n  }\n});", "lineCount": 38, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_injection"], [7, 16, 4, 0], [7, 19, 4, 0, "require"], [7, 26, 4, 0], [7, 27, 4, 0, "_dependencyMap"], [7, 41, 4, 0], [8, 2, 10, 0], [9, 0, 11, 0], [10, 0, 12, 0], [11, 0, 13, 0], [12, 2, 14, 7], [12, 11, 14, 16, "useDOMImperativeHandle"], [12, 33, 14, 38, "useDOMImperativeHandle"], [12, 34, 15, 2, "ref"], [12, 37, 15, 13], [12, 39, 16, 2, "init"], [12, 43, 16, 15], [12, 45, 17, 2, "deps"], [12, 49, 17, 23], [12, 51, 18, 2], [13, 4, 19, 2], [14, 4, 20, 2], [14, 10, 20, 8, "isTargetWeb"], [14, 21, 20, 19], [14, 24, 20, 22], [14, 31, 20, 29, "window"], [14, 37, 20, 35], [14, 38, 20, 36, "ReactNativeWebView"], [14, 56, 20, 54], [14, 61, 20, 59], [14, 72, 20, 70], [15, 4, 22, 2], [15, 10, 22, 8, "stubHandlerFactory"], [15, 28, 22, 26], [15, 31, 22, 29], [15, 35, 22, 29, "useCallback"], [15, 53, 22, 40], [15, 55, 22, 41], [15, 62, 22, 48], [15, 63, 22, 49], [15, 64, 22, 50], [15, 65, 22, 56], [15, 67, 22, 58, "deps"], [15, 71, 22, 62], [15, 75, 22, 66], [15, 77, 22, 68], [15, 78, 22, 69], [17, 4, 24, 2], [18, 4, 25, 2], [18, 8, 25, 2, "useImperativeHandle"], [18, 34, 25, 21], [18, 36, 25, 22, "ref"], [18, 39, 25, 25], [18, 41, 25, 27, "isTargetWeb"], [18, 52, 25, 38], [18, 55, 25, 41, "init"], [18, 59, 25, 45], [18, 62, 25, 48, "stubHandlerFactory"], [18, 80, 25, 66], [18, 82, 25, 68, "deps"], [18, 86, 25, 72], [18, 87, 25, 73], [20, 4, 27, 2], [21, 4, 28, 2], [21, 8, 28, 2, "useEffect"], [21, 24, 28, 11], [21, 26, 28, 12], [21, 32, 28, 18], [22, 6, 29, 4], [22, 10, 29, 8], [22, 11, 29, 9, "isTargetWeb"], [22, 22, 29, 20], [22, 24, 29, 22], [23, 8, 30, 6, "globalThis"], [23, 18, 30, 16], [23, 19, 30, 17, "_domRefProxy"], [23, 31, 30, 29], [23, 34, 30, 32, "init"], [23, 38, 30, 36], [23, 39, 30, 37], [23, 40, 30, 38], [24, 8, 31, 6], [25, 8, 32, 6], [26, 8, 33, 6, "window"], [26, 14, 33, 12], [26, 15, 33, 13, "ReactNativeWebView"], [26, 33, 33, 31], [26, 34, 33, 32, "postMessage"], [26, 45, 33, 43], [26, 46, 34, 8, "JSON"], [26, 50, 34, 12], [26, 51, 34, 13, "stringify"], [26, 60, 34, 22], [26, 61, 34, 23], [27, 10, 35, 10, "type"], [27, 14, 35, 14], [27, 16, 35, 16, "REGISTER_DOM_IMPERATIVE_HANDLE_PROPS"], [27, 63, 35, 52], [28, 10, 36, 10, "data"], [28, 14, 36, 14], [28, 16, 36, 16, "Object"], [28, 22, 36, 22], [28, 23, 36, 23, "keys"], [28, 27, 36, 27], [28, 28, 36, 28, "globalThis"], [28, 38, 36, 38], [28, 39, 36, 39, "_domRefProxy"], [28, 51, 36, 58], [29, 8, 37, 8], [29, 9, 37, 9], [29, 10, 38, 6], [29, 11, 38, 7], [30, 6, 39, 4], [31, 6, 40, 4], [31, 13, 40, 11], [31, 19, 40, 17], [32, 8, 41, 6], [32, 12, 41, 10], [32, 13, 41, 11, "isTargetWeb"], [32, 24, 41, 22], [32, 26, 41, 24], [33, 10, 42, 8, "globalThis"], [33, 20, 42, 18], [33, 21, 42, 19, "_domRefProxy"], [33, 33, 42, 31], [33, 36, 42, 34, "undefined"], [33, 45, 42, 43], [34, 8, 43, 6], [35, 6, 44, 4], [35, 7, 44, 5], [36, 4, 45, 2], [36, 5, 45, 3], [36, 7, 45, 5, "deps"], [36, 11, 45, 9], [36, 12, 45, 10], [37, 2, 46, 0], [38, 0, 46, 1], [38, 3]], "functionMap": {"names": ["<global>", "useDOMImperativeHandle", "stubHandlerFactory", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCa;yCCQ,eD;YEM;WCY;KDI;GFC;CDC"}}, "type": "js/module"}]}