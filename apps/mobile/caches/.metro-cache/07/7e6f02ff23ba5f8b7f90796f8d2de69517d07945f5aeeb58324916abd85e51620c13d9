{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 57}}], "key": "zeoV4QTz/loUWg7IhOU/wEvU+mg=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "./NativeStatusBarManagerAndroid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 76}}], "key": "EsMYqtmWexscM05IS6niX2hY5bQ=", "exportNames": ["*"]}}, {"name": "./NativeStatusBarManagerIOS", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}], "key": "/ZlWPFU6h/wJrhTgPcsv9pp11WE=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _processColor = _interopRequireDefault(require(_dependencyMap[6], \"../../StyleSheet/processColor\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[7], \"../../Utilities/Platform\"));\n  var _NativeStatusBarManagerAndroid = _interopRequireDefault(require(_dependencyMap[8], \"./NativeStatusBarManagerAndroid\"));\n  var _NativeStatusBarManagerIOS = _interopRequireDefault(require(_dependencyMap[9], \"./NativeStatusBarManagerIOS\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[10], \"invariant\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[11], \"react\"));\n  var _StatusBar;\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function mergePropsStack(propsStack, defaultValues) {\n    return propsStack.reduce((prev, cur) => {\n      for (var prop in cur) {\n        if (cur[prop] != null) {\n          prev[prop] = cur[prop];\n        }\n      }\n      return prev;\n    }, {\n      ...defaultValues\n    });\n  }\n  function createStackEntry(props) {\n    var animated = props.animated ?? false;\n    var showHideTransition = props.showHideTransition ?? 'fade';\n    return {\n      backgroundColor: props.backgroundColor != null ? {\n        value: props.backgroundColor,\n        animated\n      } : null,\n      barStyle: props.barStyle != null ? {\n        value: props.barStyle,\n        animated\n      } : null,\n      translucent: props.translucent,\n      hidden: props.hidden != null ? {\n        value: props.hidden,\n        animated,\n        transition: showHideTransition\n      } : null,\n      networkActivityIndicatorVisible: props.networkActivityIndicatorVisible\n    };\n  }\n  var StatusBar = /*#__PURE__*/function (_React$Component) {\n    function StatusBar() {\n      var _this;\n      (0, _classCallCheck2.default)(this, StatusBar);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, StatusBar, [...args]);\n      _this._stackEntry = null;\n      return _this;\n    }\n    (0, _inherits2.default)(StatusBar, _React$Component);\n    return (0, _createClass2.default)(StatusBar, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this._stackEntry = StatusBar.pushStackEntry(this.props);\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        if (this._stackEntry != null) {\n          StatusBar.popStackEntry(this._stackEntry);\n        }\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate() {\n        if (this._stackEntry != null) {\n          this._stackEntry = StatusBar.replaceStackEntry(this._stackEntry, this.props);\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return null;\n      }\n    }], [{\n      key: \"setHidden\",\n      value: function setHidden(hidden, animation) {\n        animation = animation || 'none';\n        StatusBar._defaultProps.hidden.value = hidden;\n        if (_Platform.default.OS === 'ios') {\n          _NativeStatusBarManagerIOS.default.setHidden(hidden, animation);\n        } else if (_Platform.default.OS === 'android') {\n          _NativeStatusBarManagerAndroid.default.setHidden(hidden);\n        }\n      }\n    }, {\n      key: \"setBarStyle\",\n      value: function setBarStyle(style, animated) {\n        animated = animated || false;\n        StatusBar._defaultProps.barStyle.value = style;\n        if (_Platform.default.OS === 'ios') {\n          _NativeStatusBarManagerIOS.default.setStyle(style, animated);\n        } else if (_Platform.default.OS === 'android') {\n          _NativeStatusBarManagerAndroid.default.setStyle(style);\n        }\n      }\n    }, {\n      key: \"setNetworkActivityIndicatorVisible\",\n      value: function setNetworkActivityIndicatorVisible(visible) {\n        if (_Platform.default.OS !== 'ios') {\n          console.warn('`setNetworkActivityIndicatorVisible` is only available on iOS');\n          return;\n        }\n        StatusBar._defaultProps.networkActivityIndicatorVisible = visible;\n        _NativeStatusBarManagerIOS.default.setNetworkActivityIndicatorVisible(visible);\n      }\n    }, {\n      key: \"setBackgroundColor\",\n      value: function setBackgroundColor(color, animated) {\n        if (_Platform.default.OS !== 'android') {\n          console.warn('`setBackgroundColor` is only available on Android');\n          return;\n        }\n        animated = animated || false;\n        StatusBar._defaultProps.backgroundColor.value = color;\n        var processedColor = (0, _processColor.default)(color);\n        if (processedColor == null) {\n          console.warn(`\\`StatusBar.setBackgroundColor\\`: Color ${String(color)} parsed to null or undefined`);\n          return;\n        }\n        (0, _invariant.default)(typeof processedColor === 'number', 'Unexpected color given for StatusBar.setBackgroundColor');\n        _NativeStatusBarManagerAndroid.default.setColor(processedColor, animated);\n      }\n    }, {\n      key: \"setTranslucent\",\n      value: function setTranslucent(translucent) {\n        if (_Platform.default.OS !== 'android') {\n          console.warn('`setTranslucent` is only available on Android');\n          return;\n        }\n        StatusBar._defaultProps.translucent = translucent;\n        _NativeStatusBarManagerAndroid.default.setTranslucent(translucent);\n      }\n    }, {\n      key: \"pushStackEntry\",\n      value: function pushStackEntry(props) {\n        var entry = createStackEntry(props);\n        StatusBar._propsStack.push(entry);\n        StatusBar._updatePropsStack();\n        return entry;\n      }\n    }, {\n      key: \"popStackEntry\",\n      value: function popStackEntry(entry) {\n        var index = StatusBar._propsStack.indexOf(entry);\n        if (index !== -1) {\n          StatusBar._propsStack.splice(index, 1);\n        }\n        StatusBar._updatePropsStack();\n      }\n    }, {\n      key: \"replaceStackEntry\",\n      value: function replaceStackEntry(entry, props) {\n        var newEntry = createStackEntry(props);\n        var index = StatusBar._propsStack.indexOf(entry);\n        if (index !== -1) {\n          StatusBar._propsStack[index] = newEntry;\n        }\n        StatusBar._updatePropsStack();\n        return newEntry;\n      }\n    }]);\n  }(React.Component);\n  _StatusBar = StatusBar;\n  StatusBar._propsStack = [];\n  StatusBar._defaultProps = createStackEntry({\n    backgroundColor: _Platform.default.OS === 'android' ? _NativeStatusBarManagerAndroid.default.getConstants().DEFAULT_BACKGROUND_COLOR ?? 'black' : 'black',\n    barStyle: 'default',\n    translucent: false,\n    hidden: false,\n    networkActivityIndicatorVisible: false\n  });\n  StatusBar._updateImmediate = null;\n  StatusBar._currentValues = null;\n  StatusBar.currentHeight = _Platform.default.OS === 'android' ? _NativeStatusBarManagerAndroid.default.getConstants().HEIGHT : null;\n  StatusBar._updatePropsStack = () => {\n    clearImmediate(_StatusBar._updateImmediate);\n    _StatusBar._updateImmediate = setImmediate(() => {\n      var oldProps = _StatusBar._currentValues;\n      var mergedProps = mergePropsStack(_StatusBar._propsStack, _StatusBar._defaultProps);\n      if (_Platform.default.OS === 'ios') {\n        if (!oldProps || oldProps.barStyle?.value !== mergedProps.barStyle.value) {\n          _NativeStatusBarManagerIOS.default.setStyle(mergedProps.barStyle.value, mergedProps.barStyle.animated || false);\n        }\n        if (!oldProps || oldProps.hidden?.value !== mergedProps.hidden.value) {\n          _NativeStatusBarManagerIOS.default.setHidden(mergedProps.hidden.value, mergedProps.hidden.animated ? mergedProps.hidden.transition : 'none');\n        }\n        if (!oldProps || oldProps.networkActivityIndicatorVisible !== mergedProps.networkActivityIndicatorVisible) {\n          _NativeStatusBarManagerIOS.default.setNetworkActivityIndicatorVisible(mergedProps.networkActivityIndicatorVisible);\n        }\n      } else if (_Platform.default.OS === 'android') {\n        _NativeStatusBarManagerAndroid.default.setStyle(mergedProps.barStyle.value);\n        var processedColor = (0, _processColor.default)(mergedProps.backgroundColor.value);\n        if (processedColor == null) {\n          console.warn(`\\`StatusBar._updatePropsStack\\`: Color ${mergedProps.backgroundColor.value} parsed to null or undefined`);\n        } else {\n          (0, _invariant.default)(typeof processedColor === 'number', 'Unexpected color given in StatusBar._updatePropsStack');\n          _NativeStatusBarManagerAndroid.default.setColor(processedColor, mergedProps.backgroundColor.animated);\n        }\n        if (!oldProps || oldProps.hidden?.value !== mergedProps.hidden.value) {\n          _NativeStatusBarManagerAndroid.default.setHidden(mergedProps.hidden.value);\n        }\n        if (!oldProps || oldProps.translucent !== mergedProps.translucent || mergedProps.translucent) {\n          _NativeStatusBarManagerAndroid.default.setTranslucent(mergedProps.translucent);\n        }\n      }\n      _StatusBar._currentValues = mergedProps;\n    });\n  };\n  var _default = exports.default = StatusBar;\n});", "lineCount": 227, "map": [[12, 2, 13, 0], [12, 6, 13, 0, "_processColor"], [12, 19, 13, 0], [12, 22, 13, 0, "_interopRequireDefault"], [12, 44, 13, 0], [12, 45, 13, 0, "require"], [12, 52, 13, 0], [12, 53, 13, 0, "_dependencyMap"], [12, 67, 13, 0], [13, 2, 14, 0], [13, 6, 14, 0, "_Platform"], [13, 15, 14, 0], [13, 18, 14, 0, "_interopRequireDefault"], [13, 40, 14, 0], [13, 41, 14, 0, "require"], [13, 48, 14, 0], [13, 49, 14, 0, "_dependencyMap"], [13, 63, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "_NativeStatusBarManagerAndroid"], [14, 36, 15, 0], [14, 39, 15, 0, "_interopRequireDefault"], [14, 61, 15, 0], [14, 62, 15, 0, "require"], [14, 69, 15, 0], [14, 70, 15, 0, "_dependencyMap"], [14, 84, 15, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_NativeStatusBarManagerIOS"], [15, 32, 16, 0], [15, 35, 16, 0, "_interopRequireDefault"], [15, 57, 16, 0], [15, 58, 16, 0, "require"], [15, 65, 16, 0], [15, 66, 16, 0, "_dependencyMap"], [15, 80, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_invariant"], [16, 16, 17, 0], [16, 19, 17, 0, "_interopRequireDefault"], [16, 41, 17, 0], [16, 42, 17, 0, "require"], [16, 49, 17, 0], [16, 50, 17, 0, "_dependencyMap"], [16, 64, 17, 0], [17, 2, 18, 0], [17, 6, 18, 0, "React"], [17, 11, 18, 0], [17, 14, 18, 0, "_interopRequireWildcard"], [17, 37, 18, 0], [17, 38, 18, 0, "require"], [17, 45, 18, 0], [17, 46, 18, 0, "_dependencyMap"], [17, 60, 18, 0], [18, 2, 18, 31], [18, 6, 18, 31, "_StatusBar"], [18, 16, 18, 31], [19, 2, 18, 31], [19, 11, 18, 31, "_interopRequireWildcard"], [19, 35, 18, 31, "e"], [19, 36, 18, 31], [19, 38, 18, 31, "t"], [19, 39, 18, 31], [19, 68, 18, 31, "WeakMap"], [19, 75, 18, 31], [19, 81, 18, 31, "r"], [19, 82, 18, 31], [19, 89, 18, 31, "WeakMap"], [19, 96, 18, 31], [19, 100, 18, 31, "n"], [19, 101, 18, 31], [19, 108, 18, 31, "WeakMap"], [19, 115, 18, 31], [19, 127, 18, 31, "_interopRequireWildcard"], [19, 150, 18, 31], [19, 162, 18, 31, "_interopRequireWildcard"], [19, 163, 18, 31, "e"], [19, 164, 18, 31], [19, 166, 18, 31, "t"], [19, 167, 18, 31], [19, 176, 18, 31, "t"], [19, 177, 18, 31], [19, 181, 18, 31, "e"], [19, 182, 18, 31], [19, 186, 18, 31, "e"], [19, 187, 18, 31], [19, 188, 18, 31, "__esModule"], [19, 198, 18, 31], [19, 207, 18, 31, "e"], [19, 208, 18, 31], [19, 214, 18, 31, "o"], [19, 215, 18, 31], [19, 217, 18, 31, "i"], [19, 218, 18, 31], [19, 220, 18, 31, "f"], [19, 221, 18, 31], [19, 226, 18, 31, "__proto__"], [19, 235, 18, 31], [19, 243, 18, 31, "default"], [19, 250, 18, 31], [19, 252, 18, 31, "e"], [19, 253, 18, 31], [19, 270, 18, 31, "e"], [19, 271, 18, 31], [19, 294, 18, 31, "e"], [19, 295, 18, 31], [19, 320, 18, 31, "e"], [19, 321, 18, 31], [19, 330, 18, 31, "f"], [19, 331, 18, 31], [19, 337, 18, 31, "o"], [19, 338, 18, 31], [19, 341, 18, 31, "t"], [19, 342, 18, 31], [19, 345, 18, 31, "n"], [19, 346, 18, 31], [19, 349, 18, 31, "r"], [19, 350, 18, 31], [19, 358, 18, 31, "o"], [19, 359, 18, 31], [19, 360, 18, 31, "has"], [19, 363, 18, 31], [19, 364, 18, 31, "e"], [19, 365, 18, 31], [19, 375, 18, 31, "o"], [19, 376, 18, 31], [19, 377, 18, 31, "get"], [19, 380, 18, 31], [19, 381, 18, 31, "e"], [19, 382, 18, 31], [19, 385, 18, 31, "o"], [19, 386, 18, 31], [19, 387, 18, 31, "set"], [19, 390, 18, 31], [19, 391, 18, 31, "e"], [19, 392, 18, 31], [19, 394, 18, 31, "f"], [19, 395, 18, 31], [19, 409, 18, 31, "_t"], [19, 411, 18, 31], [19, 415, 18, 31, "e"], [19, 416, 18, 31], [19, 432, 18, 31, "_t"], [19, 434, 18, 31], [19, 441, 18, 31, "hasOwnProperty"], [19, 455, 18, 31], [19, 456, 18, 31, "call"], [19, 460, 18, 31], [19, 461, 18, 31, "e"], [19, 462, 18, 31], [19, 464, 18, 31, "_t"], [19, 466, 18, 31], [19, 473, 18, 31, "i"], [19, 474, 18, 31], [19, 478, 18, 31, "o"], [19, 479, 18, 31], [19, 482, 18, 31, "Object"], [19, 488, 18, 31], [19, 489, 18, 31, "defineProperty"], [19, 503, 18, 31], [19, 508, 18, 31, "Object"], [19, 514, 18, 31], [19, 515, 18, 31, "getOwnPropertyDescriptor"], [19, 539, 18, 31], [19, 540, 18, 31, "e"], [19, 541, 18, 31], [19, 543, 18, 31, "_t"], [19, 545, 18, 31], [19, 552, 18, 31, "i"], [19, 553, 18, 31], [19, 554, 18, 31, "get"], [19, 557, 18, 31], [19, 561, 18, 31, "i"], [19, 562, 18, 31], [19, 563, 18, 31, "set"], [19, 566, 18, 31], [19, 570, 18, 31, "o"], [19, 571, 18, 31], [19, 572, 18, 31, "f"], [19, 573, 18, 31], [19, 575, 18, 31, "_t"], [19, 577, 18, 31], [19, 579, 18, 31, "i"], [19, 580, 18, 31], [19, 584, 18, 31, "f"], [19, 585, 18, 31], [19, 586, 18, 31, "_t"], [19, 588, 18, 31], [19, 592, 18, 31, "e"], [19, 593, 18, 31], [19, 594, 18, 31, "_t"], [19, 596, 18, 31], [19, 607, 18, 31, "f"], [19, 608, 18, 31], [19, 613, 18, 31, "e"], [19, 614, 18, 31], [19, 616, 18, 31, "t"], [19, 617, 18, 31], [20, 2, 18, 31], [20, 11, 18, 31, "_callSuper"], [20, 22, 18, 31, "t"], [20, 23, 18, 31], [20, 25, 18, 31, "o"], [20, 26, 18, 31], [20, 28, 18, 31, "e"], [20, 29, 18, 31], [20, 40, 18, 31, "o"], [20, 41, 18, 31], [20, 48, 18, 31, "_getPrototypeOf2"], [20, 64, 18, 31], [20, 65, 18, 31, "default"], [20, 72, 18, 31], [20, 74, 18, 31, "o"], [20, 75, 18, 31], [20, 82, 18, 31, "_possibleConstructorReturn2"], [20, 109, 18, 31], [20, 110, 18, 31, "default"], [20, 117, 18, 31], [20, 119, 18, 31, "t"], [20, 120, 18, 31], [20, 122, 18, 31, "_isNativeReflectConstruct"], [20, 147, 18, 31], [20, 152, 18, 31, "Reflect"], [20, 159, 18, 31], [20, 160, 18, 31, "construct"], [20, 169, 18, 31], [20, 170, 18, 31, "o"], [20, 171, 18, 31], [20, 173, 18, 31, "e"], [20, 174, 18, 31], [20, 186, 18, 31, "_getPrototypeOf2"], [20, 202, 18, 31], [20, 203, 18, 31, "default"], [20, 210, 18, 31], [20, 212, 18, 31, "t"], [20, 213, 18, 31], [20, 215, 18, 31, "constructor"], [20, 226, 18, 31], [20, 230, 18, 31, "o"], [20, 231, 18, 31], [20, 232, 18, 31, "apply"], [20, 237, 18, 31], [20, 238, 18, 31, "t"], [20, 239, 18, 31], [20, 241, 18, 31, "e"], [20, 242, 18, 31], [21, 2, 18, 31], [21, 11, 18, 31, "_isNativeReflectConstruct"], [21, 37, 18, 31], [21, 51, 18, 31, "t"], [21, 52, 18, 31], [21, 56, 18, 31, "Boolean"], [21, 63, 18, 31], [21, 64, 18, 31, "prototype"], [21, 73, 18, 31], [21, 74, 18, 31, "valueOf"], [21, 81, 18, 31], [21, 82, 18, 31, "call"], [21, 86, 18, 31], [21, 87, 18, 31, "Reflect"], [21, 94, 18, 31], [21, 95, 18, 31, "construct"], [21, 104, 18, 31], [21, 105, 18, 31, "Boolean"], [21, 112, 18, 31], [21, 145, 18, 31, "t"], [21, 146, 18, 31], [21, 159, 18, 31, "_isNativeReflectConstruct"], [21, 184, 18, 31], [21, 196, 18, 31, "_isNativeReflectConstruct"], [21, 197, 18, 31], [21, 210, 18, 31, "t"], [21, 211, 18, 31], [22, 2, 133, 0], [22, 11, 133, 9, "mergePropsStack"], [22, 26, 133, 24, "mergePropsStack"], [22, 27, 134, 2, "propsStack"], [22, 37, 134, 27], [22, 39, 135, 2, "defaultValues"], [22, 52, 135, 23], [22, 54, 136, 10], [23, 4, 137, 2], [23, 11, 137, 9, "propsStack"], [23, 21, 137, 19], [23, 22, 137, 20, "reduce"], [23, 28, 137, 26], [23, 29, 138, 4], [23, 30, 138, 5, "prev"], [23, 34, 138, 9], [23, 36, 138, 11, "cur"], [23, 39, 138, 14], [23, 44, 138, 19], [24, 6, 139, 6], [24, 11, 139, 11], [24, 15, 139, 17, "prop"], [24, 19, 139, 21], [24, 23, 139, 25, "cur"], [24, 26, 139, 28], [24, 28, 139, 30], [25, 8, 140, 8], [25, 12, 140, 12, "cur"], [25, 15, 140, 15], [25, 16, 140, 16, "prop"], [25, 20, 140, 20], [25, 21, 140, 21], [25, 25, 140, 25], [25, 29, 140, 29], [25, 31, 140, 31], [26, 10, 141, 10, "prev"], [26, 14, 141, 14], [26, 15, 141, 15, "prop"], [26, 19, 141, 19], [26, 20, 141, 20], [26, 23, 141, 23, "cur"], [26, 26, 141, 26], [26, 27, 141, 27, "prop"], [26, 31, 141, 31], [26, 32, 141, 32], [27, 8, 142, 8], [28, 6, 143, 6], [29, 6, 144, 6], [29, 13, 144, 13, "prev"], [29, 17, 144, 17], [30, 4, 145, 4], [30, 5, 145, 5], [30, 7, 146, 4], [31, 6, 146, 5], [31, 9, 146, 8, "defaultValues"], [32, 4, 146, 21], [32, 5, 147, 2], [32, 6, 147, 3], [33, 2, 148, 0], [34, 2, 154, 0], [34, 11, 154, 9, "createStackEntry"], [34, 27, 154, 25, "createStackEntry"], [34, 28, 154, 26, "props"], [34, 33, 154, 47], [34, 35, 154, 61], [35, 4, 155, 2], [35, 8, 155, 8, "animated"], [35, 16, 155, 16], [35, 19, 155, 19, "props"], [35, 24, 155, 24], [35, 25, 155, 25, "animated"], [35, 33, 155, 33], [35, 37, 155, 37], [35, 42, 155, 42], [36, 4, 156, 2], [36, 8, 156, 8, "showHideTransition"], [36, 26, 156, 26], [36, 29, 156, 29, "props"], [36, 34, 156, 34], [36, 35, 156, 35, "showHideTransition"], [36, 53, 156, 53], [36, 57, 156, 57], [36, 63, 156, 63], [37, 4, 157, 2], [37, 11, 157, 9], [38, 6, 158, 4, "backgroundColor"], [38, 21, 158, 19], [38, 23, 159, 6, "props"], [38, 28, 159, 11], [38, 29, 159, 12, "backgroundColor"], [38, 44, 159, 27], [38, 48, 159, 31], [38, 52, 159, 35], [38, 55, 160, 10], [39, 8, 161, 12, "value"], [39, 13, 161, 17], [39, 15, 161, 19, "props"], [39, 20, 161, 24], [39, 21, 161, 25, "backgroundColor"], [39, 36, 161, 40], [40, 8, 162, 12, "animated"], [41, 6, 163, 10], [41, 7, 163, 11], [41, 10, 164, 10], [41, 14, 164, 14], [42, 6, 165, 4, "barStyle"], [42, 14, 165, 12], [42, 16, 166, 6, "props"], [42, 21, 166, 11], [42, 22, 166, 12, "barStyle"], [42, 30, 166, 20], [42, 34, 166, 24], [42, 38, 166, 28], [42, 41, 167, 10], [43, 8, 168, 12, "value"], [43, 13, 168, 17], [43, 15, 168, 19, "props"], [43, 20, 168, 24], [43, 21, 168, 25, "barStyle"], [43, 29, 168, 33], [44, 8, 169, 12, "animated"], [45, 6, 170, 10], [45, 7, 170, 11], [45, 10, 171, 10], [45, 14, 171, 14], [46, 6, 172, 4, "translucent"], [46, 17, 172, 15], [46, 19, 172, 17, "props"], [46, 24, 172, 22], [46, 25, 172, 23, "translucent"], [46, 36, 172, 34], [47, 6, 173, 4, "hidden"], [47, 12, 173, 10], [47, 14, 174, 6, "props"], [47, 19, 174, 11], [47, 20, 174, 12, "hidden"], [47, 26, 174, 18], [47, 30, 174, 22], [47, 34, 174, 26], [47, 37, 175, 10], [48, 8, 176, 12, "value"], [48, 13, 176, 17], [48, 15, 176, 19, "props"], [48, 20, 176, 24], [48, 21, 176, 25, "hidden"], [48, 27, 176, 31], [49, 8, 177, 12, "animated"], [49, 16, 177, 20], [50, 8, 178, 12, "transition"], [50, 18, 178, 22], [50, 20, 178, 24, "showHideTransition"], [51, 6, 179, 10], [51, 7, 179, 11], [51, 10, 180, 10], [51, 14, 180, 14], [52, 6, 181, 4, "networkActivityIndicatorVisible"], [52, 37, 181, 35], [52, 39, 181, 37, "props"], [52, 44, 181, 42], [52, 45, 181, 43, "networkActivityIndicatorVisible"], [53, 4, 182, 2], [53, 5, 182, 3], [54, 2, 183, 0], [55, 2, 183, 1], [55, 6, 227, 6, "StatusBar"], [55, 15, 227, 15], [55, 41, 227, 15, "_React$Component"], [55, 57, 227, 15], [56, 4, 227, 15], [56, 13, 227, 15, "StatusBar"], [56, 23, 227, 15], [57, 6, 227, 15], [57, 10, 227, 15, "_this"], [57, 15, 227, 15], [58, 6, 227, 15], [58, 10, 227, 15, "_classCallCheck2"], [58, 26, 227, 15], [58, 27, 227, 15, "default"], [58, 34, 227, 15], [58, 42, 227, 15, "StatusBar"], [58, 51, 227, 15], [59, 6, 227, 15], [59, 15, 227, 15, "_len"], [59, 19, 227, 15], [59, 22, 227, 15, "arguments"], [59, 31, 227, 15], [59, 32, 227, 15, "length"], [59, 38, 227, 15], [59, 40, 227, 15, "args"], [59, 44, 227, 15], [59, 51, 227, 15, "Array"], [59, 56, 227, 15], [59, 57, 227, 15, "_len"], [59, 61, 227, 15], [59, 64, 227, 15, "_key"], [59, 68, 227, 15], [59, 74, 227, 15, "_key"], [59, 78, 227, 15], [59, 81, 227, 15, "_len"], [59, 85, 227, 15], [59, 87, 227, 15, "_key"], [59, 91, 227, 15], [60, 8, 227, 15, "args"], [60, 12, 227, 15], [60, 13, 227, 15, "_key"], [60, 17, 227, 15], [60, 21, 227, 15, "arguments"], [60, 30, 227, 15], [60, 31, 227, 15, "_key"], [60, 35, 227, 15], [61, 6, 227, 15], [62, 6, 227, 15, "_this"], [62, 11, 227, 15], [62, 14, 227, 15, "_callSuper"], [62, 24, 227, 15], [62, 31, 227, 15, "StatusBar"], [62, 40, 227, 15], [62, 46, 227, 15, "args"], [62, 50, 227, 15], [63, 6, 227, 15, "_this"], [63, 11, 227, 15], [63, 12, 397, 2, "_stackEntry"], [63, 23, 397, 13], [63, 26, 397, 29], [63, 30, 397, 33], [64, 6, 397, 33], [64, 13, 397, 33, "_this"], [64, 18, 397, 33], [65, 4, 397, 33], [66, 4, 397, 33], [66, 8, 397, 33, "_inherits2"], [66, 18, 397, 33], [66, 19, 397, 33, "default"], [66, 26, 397, 33], [66, 28, 397, 33, "StatusBar"], [66, 37, 397, 33], [66, 39, 397, 33, "_React$Component"], [66, 55, 397, 33], [67, 4, 397, 33], [67, 15, 397, 33, "_createClass2"], [67, 28, 397, 33], [67, 29, 397, 33, "default"], [67, 36, 397, 33], [67, 38, 397, 33, "StatusBar"], [67, 47, 397, 33], [68, 6, 397, 33, "key"], [68, 9, 397, 33], [69, 6, 397, 33, "value"], [69, 11, 397, 33], [69, 13, 399, 2], [69, 22, 399, 2, "componentDidMount"], [69, 39, 399, 19, "componentDidMount"], [69, 40, 399, 19], [69, 42, 399, 22], [70, 8, 404, 4], [70, 12, 404, 8], [70, 13, 404, 9, "_stackEntry"], [70, 24, 404, 20], [70, 27, 404, 23, "StatusBar"], [70, 36, 404, 32], [70, 37, 404, 33, "pushStackEntry"], [70, 51, 404, 47], [70, 52, 404, 48], [70, 56, 404, 52], [70, 57, 404, 53, "props"], [70, 62, 404, 58], [70, 63, 404, 59], [71, 6, 405, 2], [72, 4, 405, 3], [73, 6, 405, 3, "key"], [73, 9, 405, 3], [74, 6, 405, 3, "value"], [74, 11, 405, 3], [74, 13, 407, 2], [74, 22, 407, 2, "componentWillUnmount"], [74, 42, 407, 22, "componentWillUnmount"], [74, 43, 407, 22], [74, 45, 407, 25], [75, 8, 410, 4], [75, 12, 410, 8], [75, 16, 410, 12], [75, 17, 410, 13, "_stackEntry"], [75, 28, 410, 24], [75, 32, 410, 28], [75, 36, 410, 32], [75, 38, 410, 34], [76, 10, 411, 6, "StatusBar"], [76, 19, 411, 15], [76, 20, 411, 16, "popStackEntry"], [76, 33, 411, 29], [76, 34, 411, 30], [76, 38, 411, 34], [76, 39, 411, 35, "_stackEntry"], [76, 50, 411, 46], [76, 51, 411, 47], [77, 8, 412, 4], [78, 6, 413, 2], [79, 4, 413, 3], [80, 6, 413, 3, "key"], [80, 9, 413, 3], [81, 6, 413, 3, "value"], [81, 11, 413, 3], [81, 13, 415, 2], [81, 22, 415, 2, "componentDidUpdate"], [81, 40, 415, 20, "componentDidUpdate"], [81, 41, 415, 20], [81, 43, 415, 23], [82, 8, 416, 4], [82, 12, 416, 8], [82, 16, 416, 12], [82, 17, 416, 13, "_stackEntry"], [82, 28, 416, 24], [82, 32, 416, 28], [82, 36, 416, 32], [82, 38, 416, 34], [83, 10, 417, 6], [83, 14, 417, 10], [83, 15, 417, 11, "_stackEntry"], [83, 26, 417, 22], [83, 29, 417, 25, "StatusBar"], [83, 38, 417, 34], [83, 39, 417, 35, "replaceStackEntry"], [83, 56, 417, 52], [83, 57, 418, 8], [83, 61, 418, 12], [83, 62, 418, 13, "_stackEntry"], [83, 73, 418, 24], [83, 75, 419, 8], [83, 79, 419, 12], [83, 80, 419, 13, "props"], [83, 85, 420, 6], [83, 86, 420, 7], [84, 8, 421, 4], [85, 6, 422, 2], [86, 4, 422, 3], [87, 6, 422, 3, "key"], [87, 9, 422, 3], [88, 6, 422, 3, "value"], [88, 11, 422, 3], [88, 13, 502, 2], [88, 22, 502, 2, "render"], [88, 28, 502, 8, "render"], [88, 29, 502, 8], [88, 31, 502, 23], [89, 8, 503, 4], [89, 15, 503, 11], [89, 19, 503, 15], [90, 6, 504, 2], [91, 4, 504, 3], [92, 6, 504, 3, "key"], [92, 9, 504, 3], [93, 6, 504, 3, "value"], [93, 11, 504, 3], [93, 13, 269, 2], [93, 22, 269, 9, "setHidden"], [93, 31, 269, 18, "setHidden"], [93, 32, 269, 19, "hidden"], [93, 38, 269, 34], [93, 40, 269, 36, "animation"], [93, 49, 269, 66], [93, 51, 269, 68], [94, 8, 270, 4, "animation"], [94, 17, 270, 13], [94, 20, 270, 16, "animation"], [94, 29, 270, 25], [94, 33, 270, 29], [94, 39, 270, 35], [95, 8, 271, 4, "StatusBar"], [95, 17, 271, 13], [95, 18, 271, 14, "_defaultProps"], [95, 31, 271, 27], [95, 32, 271, 28, "hidden"], [95, 38, 271, 34], [95, 39, 271, 35, "value"], [95, 44, 271, 40], [95, 47, 271, 43, "hidden"], [95, 53, 271, 49], [96, 8, 272, 4], [96, 12, 272, 8, "Platform"], [96, 29, 272, 16], [96, 30, 272, 17, "OS"], [96, 32, 272, 19], [96, 37, 272, 24], [96, 42, 272, 29], [96, 44, 272, 31], [97, 10, 273, 6, "NativeStatusBarManagerIOS"], [97, 44, 273, 31], [97, 45, 273, 32, "setHidden"], [97, 54, 273, 41], [97, 55, 273, 42, "hidden"], [97, 61, 273, 48], [97, 63, 273, 50, "animation"], [97, 72, 273, 59], [97, 73, 273, 60], [98, 8, 274, 4], [98, 9, 274, 5], [98, 15, 274, 11], [98, 19, 274, 15, "Platform"], [98, 36, 274, 23], [98, 37, 274, 24, "OS"], [98, 39, 274, 26], [98, 44, 274, 31], [98, 53, 274, 40], [98, 55, 274, 42], [99, 10, 275, 6, "NativeStatusBarManagerAndroid"], [99, 48, 275, 35], [99, 49, 275, 36, "setHidden"], [99, 58, 275, 45], [99, 59, 275, 46, "hidden"], [99, 65, 275, 52], [99, 66, 275, 53], [100, 8, 276, 4], [101, 6, 277, 2], [102, 4, 277, 3], [103, 6, 277, 3, "key"], [103, 9, 277, 3], [104, 6, 277, 3, "value"], [104, 11, 277, 3], [104, 13, 284, 2], [104, 22, 284, 9, "setBarStyle"], [104, 33, 284, 20, "setBarStyle"], [104, 34, 284, 21, "style"], [104, 39, 284, 42], [104, 41, 284, 44, "animated"], [104, 49, 284, 62], [104, 51, 284, 64], [105, 8, 285, 4, "animated"], [105, 16, 285, 12], [105, 19, 285, 15, "animated"], [105, 27, 285, 23], [105, 31, 285, 27], [105, 36, 285, 32], [106, 8, 286, 4, "StatusBar"], [106, 17, 286, 13], [106, 18, 286, 14, "_defaultProps"], [106, 31, 286, 27], [106, 32, 286, 28, "barStyle"], [106, 40, 286, 36], [106, 41, 286, 37, "value"], [106, 46, 286, 42], [106, 49, 286, 45, "style"], [106, 54, 286, 50], [107, 8, 287, 4], [107, 12, 287, 8, "Platform"], [107, 29, 287, 16], [107, 30, 287, 17, "OS"], [107, 32, 287, 19], [107, 37, 287, 24], [107, 42, 287, 29], [107, 44, 287, 31], [108, 10, 288, 6, "NativeStatusBarManagerIOS"], [108, 44, 288, 31], [108, 45, 288, 32, "setStyle"], [108, 53, 288, 40], [108, 54, 288, 41, "style"], [108, 59, 288, 46], [108, 61, 288, 48, "animated"], [108, 69, 288, 56], [108, 70, 288, 57], [109, 8, 289, 4], [109, 9, 289, 5], [109, 15, 289, 11], [109, 19, 289, 15, "Platform"], [109, 36, 289, 23], [109, 37, 289, 24, "OS"], [109, 39, 289, 26], [109, 44, 289, 31], [109, 53, 289, 40], [109, 55, 289, 42], [110, 10, 290, 6, "NativeStatusBarManagerAndroid"], [110, 48, 290, 35], [110, 49, 290, 36, "setStyle"], [110, 57, 290, 44], [110, 58, 290, 45, "style"], [110, 63, 290, 50], [110, 64, 290, 51], [111, 8, 291, 4], [112, 6, 292, 2], [113, 4, 292, 3], [114, 6, 292, 3, "key"], [114, 9, 292, 3], [115, 6, 292, 3, "value"], [115, 11, 292, 3], [115, 13, 300, 2], [115, 22, 300, 9, "setNetworkActivityIndicatorVisible"], [115, 56, 300, 43, "setNetworkActivityIndicatorVisible"], [115, 57, 300, 44, "visible"], [115, 64, 300, 60], [115, 66, 300, 62], [116, 8, 301, 4], [116, 12, 301, 8, "Platform"], [116, 29, 301, 16], [116, 30, 301, 17, "OS"], [116, 32, 301, 19], [116, 37, 301, 24], [116, 42, 301, 29], [116, 44, 301, 31], [117, 10, 302, 6, "console"], [117, 17, 302, 13], [117, 18, 302, 14, "warn"], [117, 22, 302, 18], [117, 23, 303, 8], [117, 86, 304, 6], [117, 87, 304, 7], [118, 10, 305, 6], [119, 8, 306, 4], [120, 8, 307, 4, "StatusBar"], [120, 17, 307, 13], [120, 18, 307, 14, "_defaultProps"], [120, 31, 307, 27], [120, 32, 307, 28, "networkActivityIndicatorVisible"], [120, 63, 307, 59], [120, 66, 307, 62, "visible"], [120, 73, 307, 69], [121, 8, 308, 4, "NativeStatusBarManagerIOS"], [121, 42, 308, 29], [121, 43, 308, 30, "setNetworkActivityIndicatorVisible"], [121, 77, 308, 64], [121, 78, 308, 65, "visible"], [121, 85, 308, 72], [121, 86, 308, 73], [122, 6, 309, 2], [123, 4, 309, 3], [124, 6, 309, 3, "key"], [124, 9, 309, 3], [125, 6, 309, 3, "value"], [125, 11, 309, 3], [125, 13, 316, 2], [125, 22, 316, 9, "setBackgroundColor"], [125, 40, 316, 27, "setBackgroundColor"], [125, 41, 316, 28, "color"], [125, 46, 316, 45], [125, 48, 316, 47, "animated"], [125, 56, 316, 65], [125, 58, 316, 73], [126, 8, 317, 4], [126, 12, 317, 8, "Platform"], [126, 29, 317, 16], [126, 30, 317, 17, "OS"], [126, 32, 317, 19], [126, 37, 317, 24], [126, 46, 317, 33], [126, 48, 317, 35], [127, 10, 318, 6, "console"], [127, 17, 318, 13], [127, 18, 318, 14, "warn"], [127, 22, 318, 18], [127, 23, 318, 19], [127, 74, 318, 70], [127, 75, 318, 71], [128, 10, 319, 6], [129, 8, 320, 4], [130, 8, 321, 4, "animated"], [130, 16, 321, 12], [130, 19, 321, 15, "animated"], [130, 27, 321, 23], [130, 31, 321, 27], [130, 36, 321, 32], [131, 8, 322, 4, "StatusBar"], [131, 17, 322, 13], [131, 18, 322, 14, "_defaultProps"], [131, 31, 322, 27], [131, 32, 322, 28, "backgroundColor"], [131, 47, 322, 43], [131, 48, 322, 44, "value"], [131, 53, 322, 49], [131, 56, 322, 52, "color"], [131, 61, 322, 57], [132, 8, 324, 4], [132, 12, 324, 10, "processedColor"], [132, 26, 324, 24], [132, 29, 324, 27], [132, 33, 324, 27, "processColor"], [132, 54, 324, 39], [132, 56, 324, 40, "color"], [132, 61, 324, 45], [132, 62, 324, 46], [133, 8, 325, 4], [133, 12, 325, 8, "processedColor"], [133, 26, 325, 22], [133, 30, 325, 26], [133, 34, 325, 30], [133, 36, 325, 32], [134, 10, 326, 6, "console"], [134, 17, 326, 13], [134, 18, 326, 14, "warn"], [134, 22, 326, 18], [134, 23, 327, 8], [134, 66, 327, 51, "String"], [134, 72, 327, 57], [134, 73, 327, 58, "color"], [134, 78, 327, 63], [134, 79, 327, 64], [134, 109, 328, 6], [134, 110, 328, 7], [135, 10, 329, 6], [136, 8, 330, 4], [137, 8, 331, 4], [137, 12, 331, 4, "invariant"], [137, 30, 331, 13], [137, 32, 332, 6], [137, 39, 332, 13, "processedColor"], [137, 53, 332, 27], [137, 58, 332, 32], [137, 66, 332, 40], [137, 68, 333, 6], [137, 125, 334, 4], [137, 126, 334, 5], [138, 8, 336, 4, "NativeStatusBarManagerAndroid"], [138, 46, 336, 33], [138, 47, 336, 34, "setColor"], [138, 55, 336, 42], [138, 56, 336, 43, "processedColor"], [138, 70, 336, 57], [138, 72, 336, 59, "animated"], [138, 80, 336, 67], [138, 81, 336, 68], [139, 6, 337, 2], [140, 4, 337, 3], [141, 6, 337, 3, "key"], [141, 9, 337, 3], [142, 6, 337, 3, "value"], [142, 11, 337, 3], [142, 13, 343, 2], [142, 22, 343, 9, "setTranslucent"], [142, 36, 343, 23, "setTranslucent"], [142, 37, 343, 24, "translucent"], [142, 48, 343, 44], [142, 50, 343, 46], [143, 8, 344, 4], [143, 12, 344, 8, "Platform"], [143, 29, 344, 16], [143, 30, 344, 17, "OS"], [143, 32, 344, 19], [143, 37, 344, 24], [143, 46, 344, 33], [143, 48, 344, 35], [144, 10, 345, 6, "console"], [144, 17, 345, 13], [144, 18, 345, 14, "warn"], [144, 22, 345, 18], [144, 23, 345, 19], [144, 70, 345, 66], [144, 71, 345, 67], [145, 10, 346, 6], [146, 8, 347, 4], [147, 8, 348, 4, "StatusBar"], [147, 17, 348, 13], [147, 18, 348, 14, "_defaultProps"], [147, 31, 348, 27], [147, 32, 348, 28, "translucent"], [147, 43, 348, 39], [147, 46, 348, 42, "translucent"], [147, 57, 348, 53], [148, 8, 349, 4, "NativeStatusBarManagerAndroid"], [148, 46, 349, 33], [148, 47, 349, 34, "setTranslucent"], [148, 61, 349, 48], [148, 62, 349, 49, "translucent"], [148, 73, 349, 60], [148, 74, 349, 61], [149, 6, 350, 2], [150, 4, 350, 3], [151, 6, 350, 3, "key"], [151, 9, 350, 3], [152, 6, 350, 3, "value"], [152, 11, 350, 3], [152, 13, 358, 2], [152, 22, 358, 9, "pushStackEntry"], [152, 36, 358, 23, "pushStackEntry"], [152, 37, 358, 24, "props"], [152, 42, 358, 45], [152, 44, 358, 59], [153, 8, 359, 4], [153, 12, 359, 10, "entry"], [153, 17, 359, 15], [153, 20, 359, 18, "createStackEntry"], [153, 36, 359, 34], [153, 37, 359, 35, "props"], [153, 42, 359, 40], [153, 43, 359, 41], [154, 8, 360, 4, "StatusBar"], [154, 17, 360, 13], [154, 18, 360, 14, "_propsStack"], [154, 29, 360, 25], [154, 30, 360, 26, "push"], [154, 34, 360, 30], [154, 35, 360, 31, "entry"], [154, 40, 360, 36], [154, 41, 360, 37], [155, 8, 361, 4, "StatusBar"], [155, 17, 361, 13], [155, 18, 361, 14, "_updatePropsStack"], [155, 35, 361, 31], [155, 36, 361, 32], [155, 37, 361, 33], [156, 8, 362, 4], [156, 15, 362, 11, "entry"], [156, 20, 362, 16], [157, 6, 363, 2], [158, 4, 363, 3], [159, 6, 363, 3, "key"], [159, 9, 363, 3], [160, 6, 363, 3, "value"], [160, 11, 363, 3], [160, 13, 370, 2], [160, 22, 370, 9, "popStackEntry"], [160, 35, 370, 22, "popStackEntry"], [160, 36, 370, 23, "entry"], [160, 41, 370, 40], [160, 43, 370, 42], [161, 8, 371, 4], [161, 12, 371, 10, "index"], [161, 17, 371, 15], [161, 20, 371, 18, "StatusBar"], [161, 29, 371, 27], [161, 30, 371, 28, "_propsStack"], [161, 41, 371, 39], [161, 42, 371, 40, "indexOf"], [161, 49, 371, 47], [161, 50, 371, 48, "entry"], [161, 55, 371, 53], [161, 56, 371, 54], [162, 8, 372, 4], [162, 12, 372, 8, "index"], [162, 17, 372, 13], [162, 22, 372, 18], [162, 23, 372, 19], [162, 24, 372, 20], [162, 26, 372, 22], [163, 10, 373, 6, "StatusBar"], [163, 19, 373, 15], [163, 20, 373, 16, "_propsStack"], [163, 31, 373, 27], [163, 32, 373, 28, "splice"], [163, 38, 373, 34], [163, 39, 373, 35, "index"], [163, 44, 373, 40], [163, 46, 373, 42], [163, 47, 373, 43], [163, 48, 373, 44], [164, 8, 374, 4], [165, 8, 375, 4, "StatusBar"], [165, 17, 375, 13], [165, 18, 375, 14, "_updatePropsStack"], [165, 35, 375, 31], [165, 36, 375, 32], [165, 37, 375, 33], [166, 6, 376, 2], [167, 4, 376, 3], [168, 6, 376, 3, "key"], [168, 9, 376, 3], [169, 6, 376, 3, "value"], [169, 11, 376, 3], [169, 13, 384, 2], [169, 22, 384, 9, "replaceStackEntry"], [169, 39, 384, 26, "replaceStackEntry"], [169, 40, 385, 4, "entry"], [169, 45, 385, 21], [169, 47, 386, 4, "props"], [169, 52, 386, 25], [169, 54, 387, 16], [170, 8, 388, 4], [170, 12, 388, 10, "newEntry"], [170, 20, 388, 18], [170, 23, 388, 21, "createStackEntry"], [170, 39, 388, 37], [170, 40, 388, 38, "props"], [170, 45, 388, 43], [170, 46, 388, 44], [171, 8, 389, 4], [171, 12, 389, 10, "index"], [171, 17, 389, 15], [171, 20, 389, 18, "StatusBar"], [171, 29, 389, 27], [171, 30, 389, 28, "_propsStack"], [171, 41, 389, 39], [171, 42, 389, 40, "indexOf"], [171, 49, 389, 47], [171, 50, 389, 48, "entry"], [171, 55, 389, 53], [171, 56, 389, 54], [172, 8, 390, 4], [172, 12, 390, 8, "index"], [172, 17, 390, 13], [172, 22, 390, 18], [172, 23, 390, 19], [172, 24, 390, 20], [172, 26, 390, 22], [173, 10, 391, 6, "StatusBar"], [173, 19, 391, 15], [173, 20, 391, 16, "_propsStack"], [173, 31, 391, 27], [173, 32, 391, 28, "index"], [173, 37, 391, 33], [173, 38, 391, 34], [173, 41, 391, 37, "newEntry"], [173, 49, 391, 45], [174, 8, 392, 4], [175, 8, 393, 4, "StatusBar"], [175, 17, 393, 13], [175, 18, 393, 14, "_updatePropsStack"], [175, 35, 393, 31], [175, 36, 393, 32], [175, 37, 393, 33], [176, 8, 394, 4], [176, 15, 394, 11, "newEntry"], [176, 23, 394, 19], [177, 6, 395, 2], [178, 4, 395, 3], [179, 2, 395, 3], [179, 4, 227, 24, "React"], [179, 9, 227, 29], [179, 10, 227, 30, "Component"], [179, 19, 227, 39], [180, 2, 227, 39, "_StatusBar"], [180, 12, 227, 39], [180, 15, 227, 6, "StatusBar"], [180, 24, 227, 15], [181, 2, 227, 6, "StatusBar"], [181, 11, 227, 15], [181, 12, 228, 9, "_propsStack"], [181, 23, 228, 20], [181, 26, 228, 42], [181, 28, 228, 44], [182, 2, 227, 6, "StatusBar"], [182, 11, 227, 15], [182, 12, 230, 9, "_defaultProps"], [182, 25, 230, 22], [182, 28, 230, 30, "createStackEntry"], [182, 44, 230, 46], [182, 45, 230, 47], [183, 4, 231, 4, "backgroundColor"], [183, 19, 231, 19], [183, 21, 232, 6, "Platform"], [183, 38, 232, 14], [183, 39, 232, 15, "OS"], [183, 41, 232, 17], [183, 46, 232, 22], [183, 55, 232, 31], [183, 58, 233, 10, "NativeStatusBarManagerAndroid"], [183, 96, 233, 39], [183, 97, 233, 40, "getConstants"], [183, 109, 233, 52], [183, 110, 233, 53], [183, 111, 233, 54], [183, 112, 234, 13, "DEFAULT_BACKGROUND_COLOR"], [183, 136, 234, 37], [183, 140, 234, 41], [183, 147, 234, 48], [183, 150, 235, 10], [183, 157, 235, 17], [184, 4, 236, 4, "barStyle"], [184, 12, 236, 12], [184, 14, 236, 14], [184, 23, 236, 23], [185, 4, 237, 4, "translucent"], [185, 15, 237, 15], [185, 17, 237, 17], [185, 22, 237, 22], [186, 4, 238, 4, "hidden"], [186, 10, 238, 10], [186, 12, 238, 12], [186, 17, 238, 17], [187, 4, 239, 4, "networkActivityIndicatorVisible"], [187, 35, 239, 35], [187, 37, 239, 37], [188, 2, 240, 2], [188, 3, 240, 3], [188, 4, 240, 4], [189, 2, 227, 6, "StatusBar"], [189, 11, 227, 15], [189, 12, 243, 9, "_updateImmediate"], [189, 28, 243, 25], [189, 31, 243, 37], [189, 35, 243, 41], [190, 2, 227, 6, "StatusBar"], [190, 11, 227, 15], [190, 12, 246, 9, "_currentValues"], [190, 26, 246, 23], [190, 29, 246, 39], [190, 33, 246, 43], [191, 2, 227, 6, "StatusBar"], [191, 11, 227, 15], [191, 12, 255, 9, "currentHeight"], [191, 25, 255, 22], [191, 28, 256, 4, "Platform"], [191, 45, 256, 12], [191, 46, 256, 13, "OS"], [191, 48, 256, 15], [191, 53, 256, 20], [191, 62, 256, 29], [191, 65, 257, 8, "NativeStatusBarManagerAndroid"], [191, 103, 257, 37], [191, 104, 257, 38, "getConstants"], [191, 116, 257, 50], [191, 117, 257, 51], [191, 118, 257, 52], [191, 119, 257, 53, "HEIGHT"], [191, 125, 257, 59], [191, 128, 258, 8], [191, 132, 258, 12], [192, 2, 227, 6, "StatusBar"], [192, 11, 227, 15], [192, 12, 427, 9, "_updatePropsStack"], [192, 29, 427, 26], [192, 32, 427, 29], [192, 38, 427, 35], [193, 4, 429, 4, "clearImmediate"], [193, 18, 429, 18], [193, 19, 429, 19, "StatusBar"], [193, 29, 429, 28], [193, 30, 429, 29, "_updateImmediate"], [193, 46, 429, 45], [193, 47, 429, 46], [194, 4, 430, 4, "StatusBar"], [194, 14, 430, 13], [194, 15, 430, 14, "_updateImmediate"], [194, 31, 430, 30], [194, 34, 430, 33, "setImmediate"], [194, 46, 430, 45], [194, 47, 430, 46], [194, 53, 430, 52], [195, 6, 431, 6], [195, 10, 431, 12, "oldProps"], [195, 18, 431, 20], [195, 21, 431, 23, "StatusBar"], [195, 31, 431, 32], [195, 32, 431, 33, "_currentValues"], [195, 46, 431, 47], [196, 6, 432, 6], [196, 10, 432, 12, "mergedProps"], [196, 21, 432, 23], [196, 24, 432, 26, "mergePropsStack"], [196, 39, 432, 41], [196, 40, 433, 8, "StatusBar"], [196, 50, 433, 17], [196, 51, 433, 18, "_propsStack"], [196, 62, 433, 29], [196, 64, 434, 8, "StatusBar"], [196, 74, 434, 17], [196, 75, 434, 18, "_defaultProps"], [196, 88, 435, 6], [196, 89, 435, 7], [197, 6, 438, 6], [197, 10, 438, 10, "Platform"], [197, 27, 438, 18], [197, 28, 438, 19, "OS"], [197, 30, 438, 21], [197, 35, 438, 26], [197, 40, 438, 31], [197, 42, 438, 33], [198, 8, 439, 8], [198, 12, 440, 10], [198, 13, 440, 11, "oldProps"], [198, 21, 440, 19], [198, 25, 441, 10, "oldProps"], [198, 33, 441, 18], [198, 34, 441, 19, "barStyle"], [198, 42, 441, 27], [198, 44, 441, 29, "value"], [198, 49, 441, 34], [198, 54, 441, 39, "mergedProps"], [198, 65, 441, 50], [198, 66, 441, 51, "barStyle"], [198, 74, 441, 59], [198, 75, 441, 60, "value"], [198, 80, 441, 65], [198, 82, 442, 10], [199, 10, 443, 10, "NativeStatusBarManagerIOS"], [199, 44, 443, 35], [199, 45, 443, 36, "setStyle"], [199, 53, 443, 44], [199, 54, 444, 12, "mergedProps"], [199, 65, 444, 23], [199, 66, 444, 24, "barStyle"], [199, 74, 444, 32], [199, 75, 444, 33, "value"], [199, 80, 444, 38], [199, 82, 445, 12, "mergedProps"], [199, 93, 445, 23], [199, 94, 445, 24, "barStyle"], [199, 102, 445, 32], [199, 103, 445, 33, "animated"], [199, 111, 445, 41], [199, 115, 445, 45], [199, 120, 446, 10], [199, 121, 446, 11], [200, 8, 447, 8], [201, 8, 448, 8], [201, 12, 448, 12], [201, 13, 448, 13, "oldProps"], [201, 21, 448, 21], [201, 25, 448, 25, "oldProps"], [201, 33, 448, 33], [201, 34, 448, 34, "hidden"], [201, 40, 448, 40], [201, 42, 448, 42, "value"], [201, 47, 448, 47], [201, 52, 448, 52, "mergedProps"], [201, 63, 448, 63], [201, 64, 448, 64, "hidden"], [201, 70, 448, 70], [201, 71, 448, 71, "value"], [201, 76, 448, 76], [201, 78, 448, 78], [202, 10, 449, 10, "NativeStatusBarManagerIOS"], [202, 44, 449, 35], [202, 45, 449, 36, "setHidden"], [202, 54, 449, 45], [202, 55, 450, 12, "mergedProps"], [202, 66, 450, 23], [202, 67, 450, 24, "hidden"], [202, 73, 450, 30], [202, 74, 450, 31, "value"], [202, 79, 450, 36], [202, 81, 451, 12, "mergedProps"], [202, 92, 451, 23], [202, 93, 451, 24, "hidden"], [202, 99, 451, 30], [202, 100, 451, 31, "animated"], [202, 108, 451, 39], [202, 111, 452, 16, "mergedProps"], [202, 122, 452, 27], [202, 123, 452, 28, "hidden"], [202, 129, 452, 34], [202, 130, 452, 35, "transition"], [202, 140, 452, 45], [202, 143, 453, 16], [202, 149, 454, 10], [202, 150, 454, 11], [203, 8, 455, 8], [204, 8, 457, 8], [204, 12, 458, 10], [204, 13, 458, 11, "oldProps"], [204, 21, 458, 19], [204, 25, 459, 10, "oldProps"], [204, 33, 459, 18], [204, 34, 459, 19, "networkActivityIndicatorVisible"], [204, 65, 459, 50], [204, 70, 460, 12, "mergedProps"], [204, 81, 460, 23], [204, 82, 460, 24, "networkActivityIndicatorVisible"], [204, 113, 460, 55], [204, 115, 461, 10], [205, 10, 462, 10, "NativeStatusBarManagerIOS"], [205, 44, 462, 35], [205, 45, 462, 36, "setNetworkActivityIndicatorVisible"], [205, 79, 462, 70], [205, 80, 463, 12, "mergedProps"], [205, 91, 463, 23], [205, 92, 463, 24, "networkActivityIndicatorVisible"], [205, 123, 464, 10], [205, 124, 464, 11], [206, 8, 465, 8], [207, 6, 466, 6], [207, 7, 466, 7], [207, 13, 466, 13], [207, 17, 466, 17, "Platform"], [207, 34, 466, 25], [207, 35, 466, 26, "OS"], [207, 37, 466, 28], [207, 42, 466, 33], [207, 51, 466, 42], [207, 53, 466, 44], [208, 8, 469, 8, "NativeStatusBarManagerAndroid"], [208, 46, 469, 37], [208, 47, 469, 38, "setStyle"], [208, 55, 469, 46], [208, 56, 469, 47, "mergedProps"], [208, 67, 469, 58], [208, 68, 469, 59, "barStyle"], [208, 76, 469, 67], [208, 77, 469, 68, "value"], [208, 82, 469, 73], [208, 83, 469, 74], [209, 8, 470, 8], [209, 12, 470, 14, "processedColor"], [209, 26, 470, 28], [209, 29, 470, 31], [209, 33, 470, 31, "processColor"], [209, 54, 470, 43], [209, 56, 470, 44, "mergedProps"], [209, 67, 470, 55], [209, 68, 470, 56, "backgroundColor"], [209, 83, 470, 71], [209, 84, 470, 72, "value"], [209, 89, 470, 77], [209, 90, 470, 78], [210, 8, 471, 8], [210, 12, 471, 12, "processedColor"], [210, 26, 471, 26], [210, 30, 471, 30], [210, 34, 471, 34], [210, 36, 471, 36], [211, 10, 472, 10, "console"], [211, 17, 472, 17], [211, 18, 472, 18, "warn"], [211, 22, 472, 22], [211, 23, 473, 12], [211, 65, 473, 54, "mergedProps"], [211, 76, 473, 65], [211, 77, 473, 66, "backgroundColor"], [211, 92, 473, 81], [211, 93, 473, 82, "value"], [211, 98, 473, 87], [211, 128, 474, 10], [211, 129, 474, 11], [212, 8, 475, 8], [212, 9, 475, 9], [212, 15, 475, 15], [213, 10, 476, 10], [213, 14, 476, 10, "invariant"], [213, 32, 476, 19], [213, 34, 477, 12], [213, 41, 477, 19, "processedColor"], [213, 55, 477, 33], [213, 60, 477, 38], [213, 68, 477, 46], [213, 70, 478, 12], [213, 125, 479, 10], [213, 126, 479, 11], [214, 10, 480, 10, "NativeStatusBarManagerAndroid"], [214, 48, 480, 39], [214, 49, 480, 40, "setColor"], [214, 57, 480, 48], [214, 58, 481, 12, "processedColor"], [214, 72, 481, 26], [214, 74, 482, 12, "mergedProps"], [214, 85, 482, 23], [214, 86, 482, 24, "backgroundColor"], [214, 101, 482, 39], [214, 102, 482, 40, "animated"], [214, 110, 483, 10], [214, 111, 483, 11], [215, 8, 484, 8], [216, 8, 485, 8], [216, 12, 485, 12], [216, 13, 485, 13, "oldProps"], [216, 21, 485, 21], [216, 25, 485, 25, "oldProps"], [216, 33, 485, 33], [216, 34, 485, 34, "hidden"], [216, 40, 485, 40], [216, 42, 485, 42, "value"], [216, 47, 485, 47], [216, 52, 485, 52, "mergedProps"], [216, 63, 485, 63], [216, 64, 485, 64, "hidden"], [216, 70, 485, 70], [216, 71, 485, 71, "value"], [216, 76, 485, 76], [216, 78, 485, 78], [217, 10, 486, 10, "NativeStatusBarManagerAndroid"], [217, 48, 486, 39], [217, 49, 486, 40, "setHidden"], [217, 58, 486, 49], [217, 59, 486, 50, "mergedProps"], [217, 70, 486, 61], [217, 71, 486, 62, "hidden"], [217, 77, 486, 68], [217, 78, 486, 69, "value"], [217, 83, 486, 74], [217, 84, 486, 75], [218, 8, 487, 8], [219, 8, 489, 8], [219, 12, 490, 10], [219, 13, 490, 11, "oldProps"], [219, 21, 490, 19], [219, 25, 491, 10, "oldProps"], [219, 33, 491, 18], [219, 34, 491, 19, "translucent"], [219, 45, 491, 30], [219, 50, 491, 35, "mergedProps"], [219, 61, 491, 46], [219, 62, 491, 47, "translucent"], [219, 73, 491, 58], [219, 77, 492, 10, "mergedProps"], [219, 88, 492, 21], [219, 89, 492, 22, "translucent"], [219, 100, 492, 33], [219, 102, 493, 10], [220, 10, 494, 10, "NativeStatusBarManagerAndroid"], [220, 48, 494, 39], [220, 49, 494, 40, "setTranslucent"], [220, 63, 494, 54], [220, 64, 494, 55, "mergedProps"], [220, 75, 494, 66], [220, 76, 494, 67, "translucent"], [220, 87, 494, 78], [220, 88, 494, 79], [221, 8, 495, 8], [222, 6, 496, 6], [223, 6, 498, 6, "StatusBar"], [223, 16, 498, 15], [223, 17, 498, 16, "_currentValues"], [223, 31, 498, 30], [223, 34, 498, 33, "mergedProps"], [223, 45, 498, 44], [224, 4, 499, 4], [224, 5, 499, 5], [224, 6, 499, 6], [225, 2, 500, 2], [225, 3, 500, 3], [226, 2, 500, 3], [226, 6, 500, 3, "_default"], [226, 14, 500, 3], [226, 17, 500, 3, "exports"], [226, 24, 500, 3], [226, 25, 500, 3, "default"], [226, 32, 500, 3], [226, 35, 507, 15, "StatusBar"], [226, 44, 507, 24], [227, 0, 507, 24], [227, 3]], "functionMap": {"names": ["<global>", "mergePropsStack", "propsStack.reduce$argument_0", "createStackEntry", "StatusBar", "setHidden", "setBarStyle", "setNetworkActivityIndicatorVisible", "setBackgroundColor", "setTranslucent", "pushStackEntry", "popStackEntry", "replaceStackEntry", "componentDidMount", "componentWillUnmount", "componentDidUpdate", "_updatePropsStack", "setImmediate$argument_0", "render"], "mappings": "AAA;ACoI;ICK;KDO;CDG;AGM;CH6B;AI4C;EC0C;GDQ;EEO;GFQ;EGQ;GHS;EIO;GJqB;EKM;GLO;EMQ;GNK;EOO;GPM;EQQ;GRW;ESI;GTM;EUE;GVM;EWE;GXO;6BYK;8CCG;KDqE;GZC;EcE;GdE;CJC"}}, "type": "js/module"}]}