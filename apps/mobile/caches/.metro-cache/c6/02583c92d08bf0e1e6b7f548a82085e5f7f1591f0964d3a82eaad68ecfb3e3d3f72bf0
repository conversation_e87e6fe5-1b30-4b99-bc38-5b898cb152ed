{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Router = exports.default = (0, _createLucideIcon.default)(\"Router\", [[\"rect\", {\n    width: \"20\",\n    height: \"8\",\n    x: \"2\",\n    y: \"14\",\n    rx: \"2\",\n    key: \"w68u3i\"\n  }], [\"path\", {\n    d: \"M6.01 18H6\",\n    key: \"19vcac\"\n  }], [\"path\", {\n    d: \"M10.01 18H10\",\n    key: \"uamcmx\"\n  }], [\"path\", {\n    d: \"M15 10v4\",\n    key: \"qjz1xs\"\n  }], [\"path\", {\n    d: \"M17.84 7.17a4 4 0 0 0-5.66 0\",\n    key: \"1rif40\"\n  }], [\"path\", {\n    d: \"M20.66 4.34a8 8 0 0 0-11.31 0\",\n    key: \"6a5xfq\"\n  }]]);\n});", "lineCount": 38, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Router"], [15, 14, 10, 12], [15, 17, 10, 12, "exports"], [15, 24, 10, 12], [15, 25, 10, 12, "default"], [15, 32, 10, 12], [15, 35, 10, 15], [15, 39, 10, 15, "createLucideIcon"], [15, 64, 10, 31], [15, 66, 10, 32], [15, 74, 10, 40], [15, 76, 10, 42], [15, 77, 11, 2], [15, 78, 11, 3], [15, 84, 11, 9], [15, 86, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 15, 11, 24], [17, 4, 11, 26, "height"], [17, 10, 11, 32], [17, 12, 11, 34], [17, 15, 11, 37], [18, 4, 11, 39, "x"], [18, 5, 11, 40], [18, 7, 11, 42], [18, 10, 11, 45], [19, 4, 11, 47, "y"], [19, 5, 11, 48], [19, 7, 11, 50], [19, 11, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "key"], [21, 7, 11, 68], [21, 9, 11, 70], [22, 2, 11, 79], [22, 3, 11, 80], [22, 4, 11, 81], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [22, 15, 12, 11], [23, 4, 12, 13, "d"], [23, 5, 12, 14], [23, 7, 12, 16], [23, 19, 12, 28], [24, 4, 12, 30, "key"], [24, 7, 12, 33], [24, 9, 12, 35], [25, 2, 12, 44], [25, 3, 12, 45], [25, 4, 12, 46], [25, 6, 13, 2], [25, 7, 13, 3], [25, 13, 13, 9], [25, 15, 13, 11], [26, 4, 13, 13, "d"], [26, 5, 13, 14], [26, 7, 13, 16], [26, 21, 13, 30], [27, 4, 13, 32, "key"], [27, 7, 13, 35], [27, 9, 13, 37], [28, 2, 13, 46], [28, 3, 13, 47], [28, 4, 13, 48], [28, 6, 14, 2], [28, 7, 14, 3], [28, 13, 14, 9], [28, 15, 14, 11], [29, 4, 14, 13, "d"], [29, 5, 14, 14], [29, 7, 14, 16], [29, 17, 14, 26], [30, 4, 14, 28, "key"], [30, 7, 14, 31], [30, 9, 14, 33], [31, 2, 14, 42], [31, 3, 14, 43], [31, 4, 14, 44], [31, 6, 15, 2], [31, 7, 15, 3], [31, 13, 15, 9], [31, 15, 15, 11], [32, 4, 15, 13, "d"], [32, 5, 15, 14], [32, 7, 15, 16], [32, 37, 15, 46], [33, 4, 15, 48, "key"], [33, 7, 15, 51], [33, 9, 15, 53], [34, 2, 15, 62], [34, 3, 15, 63], [34, 4, 15, 64], [34, 6, 16, 2], [34, 7, 16, 3], [34, 13, 16, 9], [34, 15, 16, 11], [35, 4, 16, 13, "d"], [35, 5, 16, 14], [35, 7, 16, 16], [35, 38, 16, 47], [36, 4, 16, 49, "key"], [36, 7, 16, 52], [36, 9, 16, 54], [37, 2, 16, 63], [37, 3, 16, 64], [37, 4, 16, 65], [37, 5, 17, 1], [37, 6, 17, 2], [38, 0, 17, 3], [38, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}