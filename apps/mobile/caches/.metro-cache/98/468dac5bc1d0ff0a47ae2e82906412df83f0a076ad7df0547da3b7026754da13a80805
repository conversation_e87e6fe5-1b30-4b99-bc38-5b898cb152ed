{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 106}, "end": {"line": 5, "column": 48, "index": 154}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ResourceSavingView = ResourceSavingView;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/View\"));\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const FAR_FAR_AWAY = 30000; // this should be big enough to move the whole view out of its container\n\n  function ResourceSavingView({\n    visible,\n    children,\n    style,\n    ...rest\n  }) {\n    if (_Platform.default.OS === 'web') {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default\n      // @ts-expect-error: hidden exists on web, but not in React Native\n      , {\n        hidden: !visible,\n        style: [{\n          display: visible ? 'flex' : 'none'\n        }, styles.container, style],\n        pointerEvents: visible ? 'auto' : 'none',\n        ...rest,\n        children: children\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n      style: [styles.container, style]\n      // box-none doesn't seem to work properly on Android\n      ,\n\n      pointerEvents: visible ? 'auto' : 'none',\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n        collapsable: false,\n        removeClippedSubviews:\n        // On iOS & macOS, set removeClippedSubviews to true only when not focused\n        // This is an workaround for a bug where the clipped view never re-appears\n        _Platform.default.OS === 'ios' || _Platform.default.OS === 'macos' ? !visible : true,\n        pointerEvents: visible ? 'auto' : 'none',\n        style: visible ? styles.attached : styles.detached,\n        children: children\n      })\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      overflow: 'hidden'\n    },\n    attached: {\n      flex: 1\n    },\n    detached: {\n      flex: 1,\n      top: FAR_FAR_AWAY\n    }\n  });\n});", "lineCount": 67, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "ResourceSavingView"], [8, 28, 1, 13], [8, 31, 1, 13, "ResourceSavingView"], [8, 49, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "React"], [9, 11, 3, 0], [9, 14, 3, 0, "_interopRequireWildcard"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 3, 31], [10, 6, 3, 31, "_Platform"], [10, 15, 3, 31], [10, 18, 3, 31, "_interopRequireDefault"], [10, 40, 3, 31], [10, 41, 3, 31, "require"], [10, 48, 3, 31], [10, 49, 3, 31, "_dependencyMap"], [10, 63, 3, 31], [11, 2, 3, 31], [11, 6, 3, 31, "_StyleSheet"], [11, 17, 3, 31], [11, 20, 3, 31, "_interopRequireDefault"], [11, 42, 3, 31], [11, 43, 3, 31, "require"], [11, 50, 3, 31], [11, 51, 3, 31, "_dependencyMap"], [11, 65, 3, 31], [12, 2, 3, 31], [12, 6, 3, 31, "_View"], [12, 11, 3, 31], [12, 14, 3, 31, "_interopRequireDefault"], [12, 36, 3, 31], [12, 37, 3, 31, "require"], [12, 44, 3, 31], [12, 45, 3, 31, "_dependencyMap"], [12, 59, 3, 31], [13, 2, 5, 0], [13, 6, 5, 0, "_jsxRuntime"], [13, 17, 5, 0], [13, 20, 5, 0, "require"], [13, 27, 5, 0], [13, 28, 5, 0, "_dependencyMap"], [13, 42, 5, 0], [14, 2, 5, 48], [14, 11, 5, 48, "_interopRequireWildcard"], [14, 35, 5, 48, "e"], [14, 36, 5, 48], [14, 38, 5, 48, "t"], [14, 39, 5, 48], [14, 68, 5, 48, "WeakMap"], [14, 75, 5, 48], [14, 81, 5, 48, "r"], [14, 82, 5, 48], [14, 89, 5, 48, "WeakMap"], [14, 96, 5, 48], [14, 100, 5, 48, "n"], [14, 101, 5, 48], [14, 108, 5, 48, "WeakMap"], [14, 115, 5, 48], [14, 127, 5, 48, "_interopRequireWildcard"], [14, 150, 5, 48], [14, 162, 5, 48, "_interopRequireWildcard"], [14, 163, 5, 48, "e"], [14, 164, 5, 48], [14, 166, 5, 48, "t"], [14, 167, 5, 48], [14, 176, 5, 48, "t"], [14, 177, 5, 48], [14, 181, 5, 48, "e"], [14, 182, 5, 48], [14, 186, 5, 48, "e"], [14, 187, 5, 48], [14, 188, 5, 48, "__esModule"], [14, 198, 5, 48], [14, 207, 5, 48, "e"], [14, 208, 5, 48], [14, 214, 5, 48, "o"], [14, 215, 5, 48], [14, 217, 5, 48, "i"], [14, 218, 5, 48], [14, 220, 5, 48, "f"], [14, 221, 5, 48], [14, 226, 5, 48, "__proto__"], [14, 235, 5, 48], [14, 243, 5, 48, "default"], [14, 250, 5, 48], [14, 252, 5, 48, "e"], [14, 253, 5, 48], [14, 270, 5, 48, "e"], [14, 271, 5, 48], [14, 294, 5, 48, "e"], [14, 295, 5, 48], [14, 320, 5, 48, "e"], [14, 321, 5, 48], [14, 330, 5, 48, "f"], [14, 331, 5, 48], [14, 337, 5, 48, "o"], [14, 338, 5, 48], [14, 341, 5, 48, "t"], [14, 342, 5, 48], [14, 345, 5, 48, "n"], [14, 346, 5, 48], [14, 349, 5, 48, "r"], [14, 350, 5, 48], [14, 358, 5, 48, "o"], [14, 359, 5, 48], [14, 360, 5, 48, "has"], [14, 363, 5, 48], [14, 364, 5, 48, "e"], [14, 365, 5, 48], [14, 375, 5, 48, "o"], [14, 376, 5, 48], [14, 377, 5, 48, "get"], [14, 380, 5, 48], [14, 381, 5, 48, "e"], [14, 382, 5, 48], [14, 385, 5, 48, "o"], [14, 386, 5, 48], [14, 387, 5, 48, "set"], [14, 390, 5, 48], [14, 391, 5, 48, "e"], [14, 392, 5, 48], [14, 394, 5, 48, "f"], [14, 395, 5, 48], [14, 411, 5, 48, "t"], [14, 412, 5, 48], [14, 416, 5, 48, "e"], [14, 417, 5, 48], [14, 433, 5, 48, "t"], [14, 434, 5, 48], [14, 441, 5, 48, "hasOwnProperty"], [14, 455, 5, 48], [14, 456, 5, 48, "call"], [14, 460, 5, 48], [14, 461, 5, 48, "e"], [14, 462, 5, 48], [14, 464, 5, 48, "t"], [14, 465, 5, 48], [14, 472, 5, 48, "i"], [14, 473, 5, 48], [14, 477, 5, 48, "o"], [14, 478, 5, 48], [14, 481, 5, 48, "Object"], [14, 487, 5, 48], [14, 488, 5, 48, "defineProperty"], [14, 502, 5, 48], [14, 507, 5, 48, "Object"], [14, 513, 5, 48], [14, 514, 5, 48, "getOwnPropertyDescriptor"], [14, 538, 5, 48], [14, 539, 5, 48, "e"], [14, 540, 5, 48], [14, 542, 5, 48, "t"], [14, 543, 5, 48], [14, 550, 5, 48, "i"], [14, 551, 5, 48], [14, 552, 5, 48, "get"], [14, 555, 5, 48], [14, 559, 5, 48, "i"], [14, 560, 5, 48], [14, 561, 5, 48, "set"], [14, 564, 5, 48], [14, 568, 5, 48, "o"], [14, 569, 5, 48], [14, 570, 5, 48, "f"], [14, 571, 5, 48], [14, 573, 5, 48, "t"], [14, 574, 5, 48], [14, 576, 5, 48, "i"], [14, 577, 5, 48], [14, 581, 5, 48, "f"], [14, 582, 5, 48], [14, 583, 5, 48, "t"], [14, 584, 5, 48], [14, 588, 5, 48, "e"], [14, 589, 5, 48], [14, 590, 5, 48, "t"], [14, 591, 5, 48], [14, 602, 5, 48, "f"], [14, 603, 5, 48], [14, 608, 5, 48, "e"], [14, 609, 5, 48], [14, 611, 5, 48, "t"], [14, 612, 5, 48], [15, 2, 6, 0], [15, 8, 6, 6, "FAR_FAR_AWAY"], [15, 20, 6, 18], [15, 23, 6, 21], [15, 28, 6, 26], [15, 29, 6, 27], [15, 30, 6, 28], [17, 2, 8, 7], [17, 11, 8, 16, "ResourceSavingView"], [17, 29, 8, 34, "ResourceSavingView"], [17, 30, 8, 35], [18, 4, 9, 2, "visible"], [18, 11, 9, 9], [19, 4, 10, 2, "children"], [19, 12, 10, 10], [20, 4, 11, 2, "style"], [20, 9, 11, 7], [21, 4, 12, 2], [21, 7, 12, 5, "rest"], [22, 2, 13, 0], [22, 3, 13, 1], [22, 5, 13, 3], [23, 4, 14, 2], [23, 8, 14, 6, "Platform"], [23, 25, 14, 14], [23, 26, 14, 15, "OS"], [23, 28, 14, 17], [23, 33, 14, 22], [23, 38, 14, 27], [23, 40, 14, 29], [24, 6, 15, 4], [24, 13, 15, 11], [24, 26, 15, 24], [24, 30, 15, 24, "_jsx"], [24, 45, 15, 28], [24, 47, 15, 29, "View"], [25, 6, 16, 4], [26, 6, 16, 4], [26, 8, 17, 6], [27, 8, 18, 6, "hidden"], [27, 14, 18, 12], [27, 16, 18, 14], [27, 17, 18, 15, "visible"], [27, 24, 18, 22], [28, 8, 19, 6, "style"], [28, 13, 19, 11], [28, 15, 19, 13], [28, 16, 19, 14], [29, 10, 20, 8, "display"], [29, 17, 20, 15], [29, 19, 20, 17, "visible"], [29, 26, 20, 24], [29, 29, 20, 27], [29, 35, 20, 33], [29, 38, 20, 36], [30, 8, 21, 6], [30, 9, 21, 7], [30, 11, 21, 9, "styles"], [30, 17, 21, 15], [30, 18, 21, 16, "container"], [30, 27, 21, 25], [30, 29, 21, 27, "style"], [30, 34, 21, 32], [30, 35, 21, 33], [31, 8, 22, 6, "pointerEvents"], [31, 21, 22, 19], [31, 23, 22, 21, "visible"], [31, 30, 22, 28], [31, 33, 22, 31], [31, 39, 22, 37], [31, 42, 22, 40], [31, 48, 22, 46], [32, 8, 23, 6], [32, 11, 23, 9, "rest"], [32, 15, 23, 13], [33, 8, 24, 6, "children"], [33, 16, 24, 14], [33, 18, 24, 16, "children"], [34, 6, 25, 4], [34, 7, 25, 5], [34, 8, 25, 6], [35, 4, 26, 2], [36, 4, 27, 2], [36, 11, 27, 9], [36, 24, 27, 22], [36, 28, 27, 22, "_jsx"], [36, 43, 27, 26], [36, 45, 27, 27, "View"], [36, 58, 27, 31], [36, 60, 27, 33], [37, 6, 28, 4, "style"], [37, 11, 28, 9], [37, 13, 28, 11], [37, 14, 28, 12, "styles"], [37, 20, 28, 18], [37, 21, 28, 19, "container"], [37, 30, 28, 28], [37, 32, 28, 30, "style"], [37, 37, 28, 35], [38, 6, 29, 4], [39, 6, 29, 4], [41, 6, 31, 4, "pointerEvents"], [41, 19, 31, 17], [41, 21, 31, 19, "visible"], [41, 28, 31, 26], [41, 31, 31, 29], [41, 37, 31, 35], [41, 40, 31, 38], [41, 46, 31, 44], [42, 6, 32, 4, "children"], [42, 14, 32, 12], [42, 16, 32, 14], [42, 29, 32, 27], [42, 33, 32, 27, "_jsx"], [42, 48, 32, 31], [42, 50, 32, 32, "View"], [42, 63, 32, 36], [42, 65, 32, 38], [43, 8, 33, 6, "collapsable"], [43, 19, 33, 17], [43, 21, 33, 19], [43, 26, 33, 24], [44, 8, 34, 6, "removeClippedSubviews"], [44, 29, 34, 27], [45, 8, 35, 6], [46, 8, 36, 6], [47, 8, 37, 6, "Platform"], [47, 25, 37, 14], [47, 26, 37, 15, "OS"], [47, 28, 37, 17], [47, 33, 37, 22], [47, 38, 37, 27], [47, 42, 37, 31, "Platform"], [47, 59, 37, 39], [47, 60, 37, 40, "OS"], [47, 62, 37, 42], [47, 67, 37, 47], [47, 74, 37, 54], [47, 77, 37, 57], [47, 78, 37, 58, "visible"], [47, 85, 37, 65], [47, 88, 37, 68], [47, 92, 37, 72], [48, 8, 38, 6, "pointerEvents"], [48, 21, 38, 19], [48, 23, 38, 21, "visible"], [48, 30, 38, 28], [48, 33, 38, 31], [48, 39, 38, 37], [48, 42, 38, 40], [48, 48, 38, 46], [49, 8, 39, 6, "style"], [49, 13, 39, 11], [49, 15, 39, 13, "visible"], [49, 22, 39, 20], [49, 25, 39, 23, "styles"], [49, 31, 39, 29], [49, 32, 39, 30, "attached"], [49, 40, 39, 38], [49, 43, 39, 41, "styles"], [49, 49, 39, 47], [49, 50, 39, 48, "detached"], [49, 58, 39, 56], [50, 8, 40, 6, "children"], [50, 16, 40, 14], [50, 18, 40, 16, "children"], [51, 6, 41, 4], [51, 7, 41, 5], [52, 4, 42, 2], [52, 5, 42, 3], [52, 6, 42, 4], [53, 2, 43, 0], [54, 2, 44, 0], [54, 8, 44, 6, "styles"], [54, 14, 44, 12], [54, 17, 44, 15, "StyleSheet"], [54, 36, 44, 25], [54, 37, 44, 26, "create"], [54, 43, 44, 32], [54, 44, 44, 33], [55, 4, 45, 2, "container"], [55, 13, 45, 11], [55, 15, 45, 13], [56, 6, 46, 4, "flex"], [56, 10, 46, 8], [56, 12, 46, 10], [56, 13, 46, 11], [57, 6, 47, 4, "overflow"], [57, 14, 47, 12], [57, 16, 47, 14], [58, 4, 48, 2], [58, 5, 48, 3], [59, 4, 49, 2, "attached"], [59, 12, 49, 10], [59, 14, 49, 12], [60, 6, 50, 4, "flex"], [60, 10, 50, 8], [60, 12, 50, 10], [61, 4, 51, 2], [61, 5, 51, 3], [62, 4, 52, 2, "detached"], [62, 12, 52, 10], [62, 14, 52, 12], [63, 6, 53, 4, "flex"], [63, 10, 53, 8], [63, 12, 53, 10], [63, 13, 53, 11], [64, 6, 54, 4, "top"], [64, 9, 54, 7], [64, 11, 54, 9, "FAR_FAR_AWAY"], [65, 4, 55, 2], [66, 2, 56, 0], [66, 3, 56, 1], [66, 4, 56, 2], [67, 0, 56, 3], [67, 3]], "functionMap": {"names": ["<global>", "ResourceSavingView"], "mappings": "AAA;OCO;CDmC"}}, "type": "js/module"}]}