{"dependencies": [{"name": "../getNamedContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 56, "index": 71}}], "key": "KRgSZGDSx7/6c0jPDTEf7wsaS4k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderShownContext = void 0;\n  var _getNamedContext = require(_dependencyMap[0], \"../getNamedContext.js\");\n  const HeaderShownContext = exports.HeaderShownContext = (0, _getNamedContext.getNamedContext)('HeaderShownContext', false);\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "HeaderShownContext"], [7, 28, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_getNamedContext"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "HeaderShownContext"], [9, 26, 4, 31], [9, 29, 4, 31, "exports"], [9, 36, 4, 31], [9, 37, 4, 31, "HeaderShownContext"], [9, 55, 4, 31], [9, 58, 4, 34], [9, 62, 4, 34, "getNamedContext"], [9, 94, 4, 49], [9, 96, 4, 50], [9, 116, 4, 70], [9, 118, 4, 72], [9, 123, 4, 77], [9, 124, 4, 78], [10, 0, 4, 79], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}