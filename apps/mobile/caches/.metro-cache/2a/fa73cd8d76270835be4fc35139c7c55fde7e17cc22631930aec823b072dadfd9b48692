{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getShadowNodeFromRef = getShadowNodeFromRef;\n  // Used by GestureDetector (unsupported on web at the moment) to check whether the\n  // attached view may get flattened on Fabric. Original implementation causes errors\n  // on web due to the static resolution of `require` statements by webpack breaking\n  // the conditional importing.\n  function getShadowNodeFromRef(_ref) {\n    return null;\n  }\n});", "lineCount": 13, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [9, 2, 4, 0], [10, 2, 5, 7], [10, 11, 5, 16, "getShadowNodeFromRef"], [10, 31, 5, 36, "getShadowNodeFromRef"], [10, 32, 5, 37, "_ref"], [10, 36, 5, 41], [10, 38, 5, 43], [11, 4, 6, 2], [11, 11, 6, 9], [11, 15, 6, 13], [12, 2, 7, 0], [13, 0, 7, 1], [13, 3]], "functionMap": {"names": ["<global>", "getShadowNodeFromRef"], "mappings": "AAA;OCI;CDE"}}, "type": "js/module"}]}