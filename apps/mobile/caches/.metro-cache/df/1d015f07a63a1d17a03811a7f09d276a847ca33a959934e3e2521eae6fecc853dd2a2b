{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TouchEventType = void 0;\n  const TouchEventType = exports.TouchEventType = {\n    UNDETERMINED: 0,\n    TOUCHES_DOWN: 1,\n    TOUCHES_MOVE: 2,\n    TOUCHES_UP: 3,\n    TOUCHES_CANCELLED: 4\n  }; // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\n});", "lineCount": 13, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "TouchEventType"], [6, 22, 1, 27], [6, 25, 1, 27, "exports"], [6, 32, 1, 27], [6, 33, 1, 27, "TouchEventType"], [6, 47, 1, 27], [6, 50, 1, 30], [7, 4, 2, 2, "UNDETERMINED"], [7, 16, 2, 14], [7, 18, 2, 16], [7, 19, 2, 17], [8, 4, 3, 2, "TOUCHES_DOWN"], [8, 16, 3, 14], [8, 18, 3, 16], [8, 19, 3, 17], [9, 4, 4, 2, "TOUCHES_MOVE"], [9, 16, 4, 14], [9, 18, 4, 16], [9, 19, 4, 17], [10, 4, 5, 2, "TOUCHES_UP"], [10, 14, 5, 12], [10, 16, 5, 14], [10, 17, 5, 15], [11, 4, 6, 2, "TOUCHES_CANCELLED"], [11, 21, 6, 19], [11, 23, 6, 21], [12, 2, 7, 0], [12, 3, 7, 1], [12, 4, 7, 2], [12, 5, 7, 3], [13, 0, 7, 3], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}