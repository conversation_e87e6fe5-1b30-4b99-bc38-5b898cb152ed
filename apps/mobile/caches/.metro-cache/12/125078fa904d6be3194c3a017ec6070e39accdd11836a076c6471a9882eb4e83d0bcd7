{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./AnimatedInterpolation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 60}}], "key": "rc+0kZbcFDfUhy6xWENBgDldync=", "exportNames": ["*"]}}, {"name": "./AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 44}}], "key": "MXjn1CQaLNtMiiooxlb5qObVfR0=", "exportNames": ["*"]}}, {"name": "./AnimatedWithChildren", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 58}}], "key": "IUkIH5MYbr+OqFsp9MMa/cV/D0g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _AnimatedInterpolation = _interopRequireDefault(require(_dependencyMap[7], \"./AnimatedInterpolation\"));\n  var _AnimatedValue = _interopRequireDefault(require(_dependencyMap[8], \"./AnimatedValue\"));\n  var _AnimatedWithChildren2 = _interopRequireDefault(require(_dependencyMap[9], \"./AnimatedWithChildren\"));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var AnimatedAddition = exports.default = /*#__PURE__*/function (_AnimatedWithChildren) {\n    function AnimatedAddition(a, b, config) {\n      var _this;\n      (0, _classCallCheck2.default)(this, AnimatedAddition);\n      _this = _callSuper(this, AnimatedAddition, [config]);\n      _this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;\n      _this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;\n      return _this;\n    }\n    (0, _inherits2.default)(AnimatedAddition, _AnimatedWithChildren);\n    return (0, _createClass2.default)(AnimatedAddition, [{\n      key: \"__makeNative\",\n      value: function __makeNative(platformConfig) {\n        this._a.__makeNative(platformConfig);\n        this._b.__makeNative(platformConfig);\n        _superPropGet(AnimatedAddition, \"__makeNative\", this, 3)([platformConfig]);\n      }\n    }, {\n      key: \"__getValue\",\n      value: function __getValue() {\n        return this._a.__getValue() + this._b.__getValue();\n      }\n    }, {\n      key: \"interpolate\",\n      value: function interpolate(config) {\n        return new _AnimatedInterpolation.default(this, config);\n      }\n    }, {\n      key: \"__attach\",\n      value: function __attach() {\n        this._a.__addChild(this);\n        this._b.__addChild(this);\n        _superPropGet(AnimatedAddition, \"__attach\", this, 3)([]);\n      }\n    }, {\n      key: \"__detach\",\n      value: function __detach() {\n        this._a.__removeChild(this);\n        this._b.__removeChild(this);\n        _superPropGet(AnimatedAddition, \"__detach\", this, 3)([]);\n      }\n    }, {\n      key: \"__getNativeConfig\",\n      value: function __getNativeConfig() {\n        return {\n          type: 'addition',\n          input: [this._a.__getNativeTag(), this._b.__getNativeTag()],\n          debugID: this.__getDebugID()\n        };\n      }\n    }]);\n  }(_AnimatedWithChildren2.default);\n});", "lineCount": 73, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_possibleConstructorReturn2"], [11, 33, 11, 13], [11, 36, 11, 13, "_interopRequireDefault"], [11, 58, 11, 13], [11, 59, 11, 13, "require"], [11, 66, 11, 13], [11, 67, 11, 13, "_dependencyMap"], [11, 81, 11, 13], [12, 2, 11, 13], [12, 6, 11, 13, "_getPrototypeOf2"], [12, 22, 11, 13], [12, 25, 11, 13, "_interopRequireDefault"], [12, 47, 11, 13], [12, 48, 11, 13, "require"], [12, 55, 11, 13], [12, 56, 11, 13, "_dependencyMap"], [12, 70, 11, 13], [13, 2, 11, 13], [13, 6, 11, 13, "_get2"], [13, 11, 11, 13], [13, 14, 11, 13, "_interopRequireDefault"], [13, 36, 11, 13], [13, 37, 11, 13, "require"], [13, 44, 11, 13], [13, 45, 11, 13, "_dependencyMap"], [13, 59, 11, 13], [14, 2, 11, 13], [14, 6, 11, 13, "_inherits2"], [14, 16, 11, 13], [14, 19, 11, 13, "_interopRequireDefault"], [14, 41, 11, 13], [14, 42, 11, 13, "require"], [14, 49, 11, 13], [14, 50, 11, 13, "_dependencyMap"], [14, 64, 11, 13], [15, 2, 18, 0], [15, 6, 18, 0, "_AnimatedInterpolation"], [15, 28, 18, 0], [15, 31, 18, 0, "_interopRequireDefault"], [15, 53, 18, 0], [15, 54, 18, 0, "require"], [15, 61, 18, 0], [15, 62, 18, 0, "_dependencyMap"], [15, 76, 18, 0], [16, 2, 19, 0], [16, 6, 19, 0, "_AnimatedValue"], [16, 20, 19, 0], [16, 23, 19, 0, "_interopRequireDefault"], [16, 45, 19, 0], [16, 46, 19, 0, "require"], [16, 53, 19, 0], [16, 54, 19, 0, "_dependencyMap"], [16, 68, 19, 0], [17, 2, 20, 0], [17, 6, 20, 0, "_AnimatedWithChildren2"], [17, 28, 20, 0], [17, 31, 20, 0, "_interopRequireDefault"], [17, 53, 20, 0], [17, 54, 20, 0, "require"], [17, 61, 20, 0], [17, 62, 20, 0, "_dependencyMap"], [17, 76, 20, 0], [18, 2, 20, 58], [18, 11, 20, 58, "_callSuper"], [18, 22, 20, 58, "t"], [18, 23, 20, 58], [18, 25, 20, 58, "o"], [18, 26, 20, 58], [18, 28, 20, 58, "e"], [18, 29, 20, 58], [18, 40, 20, 58, "o"], [18, 41, 20, 58], [18, 48, 20, 58, "_getPrototypeOf2"], [18, 64, 20, 58], [18, 65, 20, 58, "default"], [18, 72, 20, 58], [18, 74, 20, 58, "o"], [18, 75, 20, 58], [18, 82, 20, 58, "_possibleConstructorReturn2"], [18, 109, 20, 58], [18, 110, 20, 58, "default"], [18, 117, 20, 58], [18, 119, 20, 58, "t"], [18, 120, 20, 58], [18, 122, 20, 58, "_isNativeReflectConstruct"], [18, 147, 20, 58], [18, 152, 20, 58, "Reflect"], [18, 159, 20, 58], [18, 160, 20, 58, "construct"], [18, 169, 20, 58], [18, 170, 20, 58, "o"], [18, 171, 20, 58], [18, 173, 20, 58, "e"], [18, 174, 20, 58], [18, 186, 20, 58, "_getPrototypeOf2"], [18, 202, 20, 58], [18, 203, 20, 58, "default"], [18, 210, 20, 58], [18, 212, 20, 58, "t"], [18, 213, 20, 58], [18, 215, 20, 58, "constructor"], [18, 226, 20, 58], [18, 230, 20, 58, "o"], [18, 231, 20, 58], [18, 232, 20, 58, "apply"], [18, 237, 20, 58], [18, 238, 20, 58, "t"], [18, 239, 20, 58], [18, 241, 20, 58, "e"], [18, 242, 20, 58], [19, 2, 20, 58], [19, 11, 20, 58, "_isNativeReflectConstruct"], [19, 37, 20, 58], [19, 51, 20, 58, "t"], [19, 52, 20, 58], [19, 56, 20, 58, "Boolean"], [19, 63, 20, 58], [19, 64, 20, 58, "prototype"], [19, 73, 20, 58], [19, 74, 20, 58, "valueOf"], [19, 81, 20, 58], [19, 82, 20, 58, "call"], [19, 86, 20, 58], [19, 87, 20, 58, "Reflect"], [19, 94, 20, 58], [19, 95, 20, 58, "construct"], [19, 104, 20, 58], [19, 105, 20, 58, "Boolean"], [19, 112, 20, 58], [19, 145, 20, 58, "t"], [19, 146, 20, 58], [19, 159, 20, 58, "_isNativeReflectConstruct"], [19, 184, 20, 58], [19, 196, 20, 58, "_isNativeReflectConstruct"], [19, 197, 20, 58], [19, 210, 20, 58, "t"], [19, 211, 20, 58], [20, 2, 20, 58], [20, 11, 20, 58, "_superPropGet"], [20, 25, 20, 58, "t"], [20, 26, 20, 58], [20, 28, 20, 58, "o"], [20, 29, 20, 58], [20, 31, 20, 58, "e"], [20, 32, 20, 58], [20, 34, 20, 58, "r"], [20, 35, 20, 58], [20, 43, 20, 58, "p"], [20, 44, 20, 58], [20, 51, 20, 58, "_get2"], [20, 56, 20, 58], [20, 57, 20, 58, "default"], [20, 64, 20, 58], [20, 70, 20, 58, "_getPrototypeOf2"], [20, 86, 20, 58], [20, 87, 20, 58, "default"], [20, 94, 20, 58], [20, 100, 20, 58, "r"], [20, 101, 20, 58], [20, 104, 20, 58, "t"], [20, 105, 20, 58], [20, 106, 20, 58, "prototype"], [20, 115, 20, 58], [20, 118, 20, 58, "t"], [20, 119, 20, 58], [20, 122, 20, 58, "o"], [20, 123, 20, 58], [20, 125, 20, 58, "e"], [20, 126, 20, 58], [20, 140, 20, 58, "r"], [20, 141, 20, 58], [20, 166, 20, 58, "p"], [20, 167, 20, 58], [20, 180, 20, 58, "t"], [20, 181, 20, 58], [20, 192, 20, 58, "p"], [20, 193, 20, 58], [20, 194, 20, 58, "apply"], [20, 199, 20, 58], [20, 200, 20, 58, "e"], [20, 201, 20, 58], [20, 203, 20, 58, "t"], [20, 204, 20, 58], [20, 211, 20, 58, "p"], [20, 212, 20, 58], [21, 2, 20, 58], [21, 6, 22, 21, "AnimatedAddition"], [21, 22, 22, 37], [21, 25, 22, 37, "exports"], [21, 32, 22, 37], [21, 33, 22, 37, "default"], [21, 40, 22, 37], [21, 66, 22, 37, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [21, 87, 22, 37], [22, 4, 26, 2], [22, 13, 26, 2, "AnimatedAddition"], [22, 30, 27, 4, "a"], [22, 31, 27, 28], [22, 33, 28, 4, "b"], [22, 34, 28, 28], [22, 36, 29, 4, "config"], [22, 42, 29, 32], [22, 44, 30, 4], [23, 6, 30, 4], [23, 10, 30, 4, "_this"], [23, 15, 30, 4], [24, 6, 30, 4], [24, 10, 30, 4, "_classCallCheck2"], [24, 26, 30, 4], [24, 27, 30, 4, "default"], [24, 34, 30, 4], [24, 42, 30, 4, "AnimatedAddition"], [24, 58, 30, 4], [25, 6, 31, 4, "_this"], [25, 11, 31, 4], [25, 14, 31, 4, "_callSuper"], [25, 24, 31, 4], [25, 31, 31, 4, "AnimatedAddition"], [25, 47, 31, 4], [25, 50, 31, 10, "config"], [25, 56, 31, 16], [26, 6, 32, 4, "_this"], [26, 11, 32, 4], [26, 12, 32, 9, "_a"], [26, 14, 32, 11], [26, 17, 32, 14], [26, 24, 32, 21, "a"], [26, 25, 32, 22], [26, 30, 32, 27], [26, 38, 32, 35], [26, 41, 32, 38], [26, 45, 32, 42, "AnimatedValue"], [26, 67, 32, 55], [26, 68, 32, 56, "a"], [26, 69, 32, 57], [26, 70, 32, 58], [26, 73, 32, 61, "a"], [26, 74, 32, 62], [27, 6, 33, 4, "_this"], [27, 11, 33, 4], [27, 12, 33, 9, "_b"], [27, 14, 33, 11], [27, 17, 33, 14], [27, 24, 33, 21, "b"], [27, 25, 33, 22], [27, 30, 33, 27], [27, 38, 33, 35], [27, 41, 33, 38], [27, 45, 33, 42, "AnimatedValue"], [27, 67, 33, 55], [27, 68, 33, 56, "b"], [27, 69, 33, 57], [27, 70, 33, 58], [27, 73, 33, 61, "b"], [27, 74, 33, 62], [28, 6, 33, 63], [28, 13, 33, 63, "_this"], [28, 18, 33, 63], [29, 4, 34, 2], [30, 4, 34, 3], [30, 8, 34, 3, "_inherits2"], [30, 18, 34, 3], [30, 19, 34, 3, "default"], [30, 26, 34, 3], [30, 28, 34, 3, "AnimatedAddition"], [30, 44, 34, 3], [30, 46, 34, 3, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [30, 67, 34, 3], [31, 4, 34, 3], [31, 15, 34, 3, "_createClass2"], [31, 28, 34, 3], [31, 29, 34, 3, "default"], [31, 36, 34, 3], [31, 38, 34, 3, "AnimatedAddition"], [31, 54, 34, 3], [32, 6, 34, 3, "key"], [32, 9, 34, 3], [33, 6, 34, 3, "value"], [33, 11, 34, 3], [33, 13, 36, 2], [33, 22, 36, 2, "__makeNative"], [33, 34, 36, 14, "__makeNative"], [33, 35, 36, 15, "platformConfig"], [33, 49, 36, 46], [33, 51, 36, 48], [34, 8, 37, 4], [34, 12, 37, 8], [34, 13, 37, 9, "_a"], [34, 15, 37, 11], [34, 16, 37, 12, "__makeNative"], [34, 28, 37, 24], [34, 29, 37, 25, "platformConfig"], [34, 43, 37, 39], [34, 44, 37, 40], [35, 8, 38, 4], [35, 12, 38, 8], [35, 13, 38, 9, "_b"], [35, 15, 38, 11], [35, 16, 38, 12, "__makeNative"], [35, 28, 38, 24], [35, 29, 38, 25, "platformConfig"], [35, 43, 38, 39], [35, 44, 38, 40], [36, 8, 39, 4, "_superPropGet"], [36, 21, 39, 4], [36, 22, 39, 4, "AnimatedAddition"], [36, 38, 39, 4], [36, 66, 39, 23, "platformConfig"], [36, 80, 39, 37], [37, 6, 40, 2], [38, 4, 40, 3], [39, 6, 40, 3, "key"], [39, 9, 40, 3], [40, 6, 40, 3, "value"], [40, 11, 40, 3], [40, 13, 42, 2], [40, 22, 42, 2, "__getValue"], [40, 32, 42, 12, "__getValue"], [40, 33, 42, 12], [40, 35, 42, 23], [41, 8, 43, 4], [41, 15, 43, 11], [41, 19, 43, 15], [41, 20, 43, 16, "_a"], [41, 22, 43, 18], [41, 23, 43, 19, "__getValue"], [41, 33, 43, 29], [41, 34, 43, 30], [41, 35, 43, 31], [41, 38, 43, 34], [41, 42, 43, 38], [41, 43, 43, 39, "_b"], [41, 45, 43, 41], [41, 46, 43, 42, "__getValue"], [41, 56, 43, 52], [41, 57, 43, 53], [41, 58, 43, 54], [42, 6, 44, 2], [43, 4, 44, 3], [44, 6, 44, 3, "key"], [44, 9, 44, 3], [45, 6, 44, 3, "value"], [45, 11, 44, 3], [45, 13, 46, 2], [45, 22, 46, 2, "interpolate"], [45, 33, 46, 13, "interpolate"], [45, 34, 47, 4, "config"], [45, 40, 47, 44], [45, 42, 48, 36], [46, 8, 49, 4], [46, 15, 49, 11], [46, 19, 49, 15, "AnimatedInterpolation"], [46, 49, 49, 36], [46, 50, 49, 37], [46, 54, 49, 41], [46, 56, 49, 43, "config"], [46, 62, 49, 49], [46, 63, 49, 50], [47, 6, 50, 2], [48, 4, 50, 3], [49, 6, 50, 3, "key"], [49, 9, 50, 3], [50, 6, 50, 3, "value"], [50, 11, 50, 3], [50, 13, 52, 2], [50, 22, 52, 2, "__attach"], [50, 30, 52, 10, "__attach"], [50, 31, 52, 10], [50, 33, 52, 19], [51, 8, 53, 4], [51, 12, 53, 8], [51, 13, 53, 9, "_a"], [51, 15, 53, 11], [51, 16, 53, 12, "__add<PERSON><PERSON>d"], [51, 26, 53, 22], [51, 27, 53, 23], [51, 31, 53, 27], [51, 32, 53, 28], [52, 8, 54, 4], [52, 12, 54, 8], [52, 13, 54, 9, "_b"], [52, 15, 54, 11], [52, 16, 54, 12, "__add<PERSON><PERSON>d"], [52, 26, 54, 22], [52, 27, 54, 23], [52, 31, 54, 27], [52, 32, 54, 28], [53, 8, 55, 4, "_superPropGet"], [53, 21, 55, 4], [53, 22, 55, 4, "AnimatedAddition"], [53, 38, 55, 4], [54, 6, 56, 2], [55, 4, 56, 3], [56, 6, 56, 3, "key"], [56, 9, 56, 3], [57, 6, 56, 3, "value"], [57, 11, 56, 3], [57, 13, 58, 2], [57, 22, 58, 2, "__detach"], [57, 30, 58, 10, "__detach"], [57, 31, 58, 10], [57, 33, 58, 19], [58, 8, 59, 4], [58, 12, 59, 8], [58, 13, 59, 9, "_a"], [58, 15, 59, 11], [58, 16, 59, 12, "__remove<PERSON><PERSON>d"], [58, 29, 59, 25], [58, 30, 59, 26], [58, 34, 59, 30], [58, 35, 59, 31], [59, 8, 60, 4], [59, 12, 60, 8], [59, 13, 60, 9, "_b"], [59, 15, 60, 11], [59, 16, 60, 12, "__remove<PERSON><PERSON>d"], [59, 29, 60, 25], [59, 30, 60, 26], [59, 34, 60, 30], [59, 35, 60, 31], [60, 8, 61, 4, "_superPropGet"], [60, 21, 61, 4], [60, 22, 61, 4, "AnimatedAddition"], [60, 38, 61, 4], [61, 6, 62, 2], [62, 4, 62, 3], [63, 6, 62, 3, "key"], [63, 9, 62, 3], [64, 6, 62, 3, "value"], [64, 11, 62, 3], [64, 13, 64, 2], [64, 22, 64, 2, "__getNativeConfig"], [64, 39, 64, 19, "__getNativeConfig"], [64, 40, 64, 19], [64, 42, 64, 27], [65, 8, 65, 4], [65, 15, 65, 11], [66, 10, 66, 6, "type"], [66, 14, 66, 10], [66, 16, 66, 12], [66, 26, 66, 22], [67, 10, 67, 6, "input"], [67, 15, 67, 11], [67, 17, 67, 13], [67, 18, 67, 14], [67, 22, 67, 18], [67, 23, 67, 19, "_a"], [67, 25, 67, 21], [67, 26, 67, 22, "__getNativeTag"], [67, 40, 67, 36], [67, 41, 67, 37], [67, 42, 67, 38], [67, 44, 67, 40], [67, 48, 67, 44], [67, 49, 67, 45, "_b"], [67, 51, 67, 47], [67, 52, 67, 48, "__getNativeTag"], [67, 66, 67, 62], [67, 67, 67, 63], [67, 68, 67, 64], [67, 69, 67, 65], [68, 10, 68, 6, "debugID"], [68, 17, 68, 13], [68, 19, 68, 15], [68, 23, 68, 19], [68, 24, 68, 20, "__getDebugID"], [68, 36, 68, 32], [68, 37, 68, 33], [69, 8, 69, 4], [69, 9, 69, 5], [70, 6, 70, 2], [71, 4, 70, 3], [72, 2, 70, 3], [72, 4, 22, 46, "AnimatedWithChildren"], [72, 34, 22, 66], [73, 0, 22, 66], [73, 3]], "functionMap": {"names": ["<global>", "AnimatedAddition", "constructor", "__makeNative", "__getValue", "interpolate", "__attach", "__detach", "__getNativeConfig"], "mappings": "AAA;eCqB;ECI;GDQ;EEE;GFI;EGE;GHE;EIE;GJI;EKE;GLI;EME;GNI;EOE;GPM"}}, "type": "js/module"}]}