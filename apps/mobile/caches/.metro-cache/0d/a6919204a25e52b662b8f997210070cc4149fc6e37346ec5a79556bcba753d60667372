{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 58, "index": 58}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 59}, "end": {"line": 2, "column": 44, "index": 103}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/BackHandler", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JJ8ZeP6TwsodxtZCeal/l/p3LP0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.usePreventBack = exports.default = void 0;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _expoRouter = require(_dependencyMap[2], \"expo-router\");\n  var _BackHandler = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/BackHandler\"));\n  var _s = $RefreshSig$();\n  const usePreventBack = () => {\n    _s();\n    const navigation = (0, _expoRouter.useNavigation)();\n    (0, _native.useFocusEffect)(() => {\n      navigation.setOptions({\n        headerLeft: () => null,\n        gestureEnabled: false\n      });\n      navigation.getParent()?.setOptions({\n        gestureEnabled: false\n      });\n\n      // Android back button handler\n      const hardwareBackPressHandler = _BackHandler.default.addEventListener('hardwareBackPress', () => {\n        // Prevent default behavior of leaving the screen\n        return true;\n      });\n      return () => {\n        navigation.getParent()?.setOptions({\n          gestureEnabled: true\n        });\n        navigation.setOptions({\n          gestureEnabled: true\n        });\n        hardwareBackPressHandler.remove();\n      };\n    });\n  };\n  exports.usePreventBack = usePreventBack;\n  _s(usePreventBack, \"SjYmkRpRmIk6Vfxkhl5pSWDUAXk=\", false, function () {\n    return [_expoRouter.useNavigation, _native.useFocusEffect];\n  });\n  var _default = exports.default = usePreventBack;\n});", "lineCount": 44, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_native"], [7, 13, 1, 0], [7, 16, 1, 0, "require"], [7, 23, 1, 0], [7, 24, 1, 0, "_dependencyMap"], [7, 38, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_expoRouter"], [8, 17, 2, 0], [8, 20, 2, 0, "require"], [8, 27, 2, 0], [8, 28, 2, 0, "_dependencyMap"], [8, 42, 2, 0], [9, 2, 2, 44], [9, 6, 2, 44, "_<PERSON><PERSON><PERSON><PERSON>"], [9, 18, 2, 44], [9, 21, 2, 44, "_interopRequireDefault"], [9, 43, 2, 44], [9, 44, 2, 44, "require"], [9, 51, 2, 44], [9, 52, 2, 44, "_dependencyMap"], [9, 66, 2, 44], [10, 2, 2, 44], [10, 6, 2, 44, "_s"], [10, 8, 2, 44], [10, 11, 2, 44, "$RefreshSig$"], [10, 23, 2, 44], [11, 2, 5, 7], [11, 8, 5, 13, "usePreventBack"], [11, 22, 5, 27], [11, 25, 5, 30, "usePreventBack"], [11, 26, 5, 30], [11, 31, 5, 36], [12, 4, 5, 36, "_s"], [12, 6, 5, 36], [13, 4, 6, 1], [13, 10, 6, 7, "navigation"], [13, 20, 6, 17], [13, 23, 6, 20], [13, 27, 6, 20, "useNavigation"], [13, 52, 6, 33], [13, 54, 6, 34], [13, 55, 6, 35], [14, 4, 8, 1], [14, 8, 8, 1, "useFocusEffect"], [14, 30, 8, 15], [14, 32, 8, 16], [14, 38, 8, 22], [15, 6, 9, 2, "navigation"], [15, 16, 9, 12], [15, 17, 9, 13, "setOptions"], [15, 27, 9, 23], [15, 28, 9, 24], [16, 8, 10, 3, "headerLeft"], [16, 18, 10, 13], [16, 20, 10, 15, "headerLeft"], [16, 21, 10, 15], [16, 26, 10, 21], [16, 30, 10, 25], [17, 8, 11, 3, "gestureEnabled"], [17, 22, 11, 17], [17, 24, 11, 19], [18, 6, 12, 2], [18, 7, 12, 3], [18, 8, 12, 4], [19, 6, 14, 2, "navigation"], [19, 16, 14, 12], [19, 17, 14, 13, "getParent"], [19, 26, 14, 22], [19, 27, 14, 23], [19, 28, 14, 24], [19, 30, 14, 26, "setOptions"], [19, 40, 14, 36], [19, 41, 14, 37], [20, 8, 14, 39, "gestureEnabled"], [20, 22, 14, 53], [20, 24, 14, 55], [21, 6, 14, 61], [21, 7, 14, 62], [21, 8, 14, 63], [23, 6, 16, 2], [24, 6, 17, 2], [24, 12, 17, 8, "hardwareBackPressHandler"], [24, 36, 17, 32], [24, 39, 17, 35, "<PERSON><PERSON><PERSON><PERSON>"], [24, 59, 17, 46], [24, 60, 17, 47, "addEventListener"], [24, 76, 17, 63], [24, 77, 18, 3], [24, 96, 18, 22], [24, 98, 19, 3], [24, 104, 19, 9], [25, 8, 20, 4], [26, 8, 21, 4], [26, 15, 21, 11], [26, 19, 21, 15], [27, 6, 22, 3], [27, 7, 23, 2], [27, 8, 23, 3], [28, 6, 25, 2], [28, 13, 25, 9], [28, 19, 25, 15], [29, 8, 26, 3, "navigation"], [29, 18, 26, 13], [29, 19, 26, 14, "getParent"], [29, 28, 26, 23], [29, 29, 26, 24], [29, 30, 26, 25], [29, 32, 26, 27, "setOptions"], [29, 42, 26, 37], [29, 43, 26, 38], [30, 10, 26, 40, "gestureEnabled"], [30, 24, 26, 54], [30, 26, 26, 56], [31, 8, 26, 61], [31, 9, 26, 62], [31, 10, 26, 63], [32, 8, 27, 3, "navigation"], [32, 18, 27, 13], [32, 19, 27, 14, "setOptions"], [32, 29, 27, 24], [32, 30, 27, 25], [33, 10, 28, 4, "gestureEnabled"], [33, 24, 28, 18], [33, 26, 28, 20], [34, 8, 29, 3], [34, 9, 29, 4], [34, 10, 29, 5], [35, 8, 30, 3, "hardwareBackPressHandler"], [35, 32, 30, 27], [35, 33, 30, 28, "remove"], [35, 39, 30, 34], [35, 40, 30, 35], [35, 41, 30, 36], [36, 6, 31, 2], [36, 7, 31, 3], [37, 4, 32, 1], [37, 5, 32, 2], [37, 6, 32, 3], [38, 2, 33, 0], [38, 3, 33, 1], [39, 2, 33, 2, "exports"], [39, 9, 33, 2], [39, 10, 33, 2, "usePreventBack"], [39, 24, 33, 2], [39, 27, 33, 2, "usePreventBack"], [39, 41, 33, 2], [40, 2, 33, 2, "_s"], [40, 4, 33, 2], [40, 5, 5, 13, "usePreventBack"], [40, 19, 5, 27], [41, 4, 5, 27], [41, 12, 6, 20, "useNavigation"], [41, 37, 6, 33], [41, 39, 8, 1, "useFocusEffect"], [41, 61, 8, 15], [42, 2, 8, 15], [43, 2, 8, 15], [43, 6, 8, 15, "_default"], [43, 14, 8, 15], [43, 17, 8, 15, "exports"], [43, 24, 8, 15], [43, 25, 8, 15, "default"], [43, 32, 8, 15], [43, 35, 35, 15, "usePreventBack"], [43, 49, 35, 29], [44, 0, 35, 29], [44, 3]], "functionMap": {"names": ["<global>", "usePreventBack", "useFocusEffect$argument_0", "navigation.setOptions$argument_0.headerLeft", "BackHandler.addEventListener$argument_1", "<anonymous>"], "mappings": "AAA;8BCI;gBCG;eCE,UD;GES;IFG;SGG;GHM;EDC;CDC"}}, "type": "js/module"}]}