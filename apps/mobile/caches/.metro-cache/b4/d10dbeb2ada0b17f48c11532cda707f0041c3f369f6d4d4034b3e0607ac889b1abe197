{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 46, "index": 294}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}, {"name": "../detectors/RotationGestureDetector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 295}, "end": {"line": 5, "column": 75, "index": 370}}], "key": "daz0h2jVVKKbGU+XUZZYS7vasjE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[2], \"./GestureHandler\"));\n  var _RotationGestureDetector = _interopRequireDefault(require(_dependencyMap[3], \"../detectors/RotationGestureDetector\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const ROTATION_RECOGNITION_THRESHOLD = Math.PI / 36;\n  class RotationGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"rotation\", 0);\n      _defineProperty(this, \"velocity\", 0);\n      _defineProperty(this, \"cachedAnchorX\", 0);\n      _defineProperty(this, \"cachedAnchorY\", 0);\n      _defineProperty(this, \"rotationGestureListener\", {\n        onRotationBegin: _detector => true,\n        onRotation: detector => {\n          const previousRotation = this.rotation;\n          this.rotation += detector.rotation;\n          const delta = detector.timeDelta;\n          if (delta > 0) {\n            this.velocity = (this.rotation - previousRotation) / delta;\n          }\n          if (Math.abs(this.rotation) >= ROTATION_RECOGNITION_THRESHOLD && this.state === _State.State.BEGAN) {\n            this.activate();\n          }\n          return true;\n        },\n        onRotationEnd: _detector => {\n          this.end();\n        }\n      });\n      _defineProperty(this, \"rotationGestureDetector\", new _RotationGestureDetector.default(this.rotationGestureListener));\n    }\n    init(ref, propsRef) {\n      super.init(ref, propsRef);\n      this.shouldCancelWhenOutside = false;\n    }\n    transformNativeEvent() {\n      return {\n        rotation: this.rotation ? this.rotation : 0,\n        anchorX: this.getAnchorX(),\n        anchorY: this.getAnchorY(),\n        velocity: this.velocity ? this.velocity : 0\n      };\n    }\n    getAnchorX() {\n      const anchorX = this.rotationGestureDetector.anchorX;\n      return anchorX ? anchorX : this.cachedAnchorX;\n    }\n    getAnchorY() {\n      const anchorY = this.rotationGestureDetector.anchorY;\n      return anchorY ? anchorY : this.cachedAnchorY;\n    }\n    onPointerDown(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerDown(event);\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerAdd(event);\n      this.tryBegin();\n      this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n    }\n    onPointerMove(event) {\n      if (this.tracker.trackedPointersCount < 2) {\n        return;\n      }\n      if (this.getAnchorX()) {\n        this.cachedAnchorX = this.getAnchorX();\n      }\n      if (this.getAnchorY()) {\n        this.cachedAnchorY = this.getAnchorY();\n      }\n      this.tracker.track(event);\n      this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n      super.onPointerMove(event);\n    }\n    onPointerOutOfBounds(event) {\n      if (this.tracker.trackedPointersCount < 2) {\n        return;\n      }\n      if (this.getAnchorX()) {\n        this.cachedAnchorX = this.getAnchorX();\n      }\n      if (this.getAnchorY()) {\n        this.cachedAnchorY = this.getAnchorY();\n      }\n      this.tracker.track(event);\n      this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n      super.onPointerOutOfBounds(event);\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      this.tracker.removeFromTracker(event.pointerId);\n      this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n      if (this.state !== _State.State.ACTIVE) {\n        return;\n      }\n      if (this.state === _State.State.ACTIVE) {\n        this.end();\n      } else {\n        this.fail();\n      }\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n      this.tracker.removeFromTracker(event.pointerId);\n    }\n    tryBegin() {\n      if (this.state !== _State.State.UNDETERMINED) {\n        return;\n      }\n      this.begin();\n    }\n    onReset() {\n      if (this.state === _State.State.ACTIVE) {\n        return;\n      }\n      this.rotation = 0;\n      this.velocity = 0;\n      this.rotationGestureDetector.reset();\n    }\n  }\n  exports.default = RotationGestureHandler;\n});", "lineCount": 144, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_Gesture<PERSON><PERSON>ler"], [8, 21, 4, 0], [8, 24, 4, 0, "_interopRequireDefault"], [8, 46, 4, 0], [8, 47, 4, 0, "require"], [8, 54, 4, 0], [8, 55, 4, 0, "_dependencyMap"], [8, 69, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_RotationGestureDetector"], [9, 30, 5, 0], [9, 33, 5, 0, "_interopRequireDefault"], [9, 55, 5, 0], [9, 56, 5, 0, "require"], [9, 63, 5, 0], [9, 64, 5, 0, "_dependencyMap"], [9, 78, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 6, 0], [23, 8, 6, 6, "ROTATION_RECOGNITION_THRESHOLD"], [23, 38, 6, 36], [23, 41, 6, 39, "Math"], [23, 45, 6, 43], [23, 46, 6, 44, "PI"], [23, 48, 6, 46], [23, 51, 6, 49], [23, 53, 6, 51], [24, 2, 7, 15], [24, 8, 7, 21, "RotationGestureHandler"], [24, 30, 7, 43], [24, 39, 7, 52, "Gesture<PERSON>andler"], [24, 62, 7, 66], [24, 63, 7, 67], [25, 4, 8, 2, "constructor"], [25, 15, 8, 13, "constructor"], [25, 16, 8, 14], [25, 19, 8, 17, "args"], [25, 23, 8, 21], [25, 25, 8, 23], [26, 6, 9, 4], [26, 11, 9, 9], [26, 12, 9, 10], [26, 15, 9, 13, "args"], [26, 19, 9, 17], [26, 20, 9, 18], [27, 6, 11, 4, "_defineProperty"], [27, 21, 11, 19], [27, 22, 11, 20], [27, 26, 11, 24], [27, 28, 11, 26], [27, 38, 11, 36], [27, 40, 11, 38], [27, 41, 11, 39], [27, 42, 11, 40], [28, 6, 13, 4, "_defineProperty"], [28, 21, 13, 19], [28, 22, 13, 20], [28, 26, 13, 24], [28, 28, 13, 26], [28, 38, 13, 36], [28, 40, 13, 38], [28, 41, 13, 39], [28, 42, 13, 40], [29, 6, 15, 4, "_defineProperty"], [29, 21, 15, 19], [29, 22, 15, 20], [29, 26, 15, 24], [29, 28, 15, 26], [29, 43, 15, 41], [29, 45, 15, 43], [29, 46, 15, 44], [29, 47, 15, 45], [30, 6, 17, 4, "_defineProperty"], [30, 21, 17, 19], [30, 22, 17, 20], [30, 26, 17, 24], [30, 28, 17, 26], [30, 43, 17, 41], [30, 45, 17, 43], [30, 46, 17, 44], [30, 47, 17, 45], [31, 6, 19, 4, "_defineProperty"], [31, 21, 19, 19], [31, 22, 19, 20], [31, 26, 19, 24], [31, 28, 19, 26], [31, 53, 19, 51], [31, 55, 19, 53], [32, 8, 20, 6, "onRotationBegin"], [32, 23, 20, 21], [32, 25, 20, 23, "_detector"], [32, 34, 20, 32], [32, 38, 20, 36], [32, 42, 20, 40], [33, 8, 21, 6, "onRotation"], [33, 18, 21, 16], [33, 20, 21, 18, "detector"], [33, 28, 21, 26], [33, 32, 21, 30], [34, 10, 22, 8], [34, 16, 22, 14, "previousRotation"], [34, 32, 22, 30], [34, 35, 22, 33], [34, 39, 22, 37], [34, 40, 22, 38, "rotation"], [34, 48, 22, 46], [35, 10, 23, 8], [35, 14, 23, 12], [35, 15, 23, 13, "rotation"], [35, 23, 23, 21], [35, 27, 23, 25, "detector"], [35, 35, 23, 33], [35, 36, 23, 34, "rotation"], [35, 44, 23, 42], [36, 10, 24, 8], [36, 16, 24, 14, "delta"], [36, 21, 24, 19], [36, 24, 24, 22, "detector"], [36, 32, 24, 30], [36, 33, 24, 31, "<PERSON><PERSON><PERSON><PERSON>"], [36, 42, 24, 40], [37, 10, 26, 8], [37, 14, 26, 12, "delta"], [37, 19, 26, 17], [37, 22, 26, 20], [37, 23, 26, 21], [37, 25, 26, 23], [38, 12, 27, 10], [38, 16, 27, 14], [38, 17, 27, 15, "velocity"], [38, 25, 27, 23], [38, 28, 27, 26], [38, 29, 27, 27], [38, 33, 27, 31], [38, 34, 27, 32, "rotation"], [38, 42, 27, 40], [38, 45, 27, 43, "previousRotation"], [38, 61, 27, 59], [38, 65, 27, 63, "delta"], [38, 70, 27, 68], [39, 10, 28, 8], [40, 10, 30, 8], [40, 14, 30, 12, "Math"], [40, 18, 30, 16], [40, 19, 30, 17, "abs"], [40, 22, 30, 20], [40, 23, 30, 21], [40, 27, 30, 25], [40, 28, 30, 26, "rotation"], [40, 36, 30, 34], [40, 37, 30, 35], [40, 41, 30, 39, "ROTATION_RECOGNITION_THRESHOLD"], [40, 71, 30, 69], [40, 75, 30, 73], [40, 79, 30, 77], [40, 80, 30, 78, "state"], [40, 85, 30, 83], [40, 90, 30, 88, "State"], [40, 102, 30, 93], [40, 103, 30, 94, "BEGAN"], [40, 108, 30, 99], [40, 110, 30, 101], [41, 12, 31, 10], [41, 16, 31, 14], [41, 17, 31, 15, "activate"], [41, 25, 31, 23], [41, 26, 31, 24], [41, 27, 31, 25], [42, 10, 32, 8], [43, 10, 34, 8], [43, 17, 34, 15], [43, 21, 34, 19], [44, 8, 35, 6], [44, 9, 35, 7], [45, 8, 36, 6, "onRotationEnd"], [45, 21, 36, 19], [45, 23, 36, 21, "_detector"], [45, 32, 36, 30], [45, 36, 36, 34], [46, 10, 37, 8], [46, 14, 37, 12], [46, 15, 37, 13, "end"], [46, 18, 37, 16], [46, 19, 37, 17], [46, 20, 37, 18], [47, 8, 38, 6], [48, 6, 39, 4], [48, 7, 39, 5], [48, 8, 39, 6], [49, 6, 41, 4, "_defineProperty"], [49, 21, 41, 19], [49, 22, 41, 20], [49, 26, 41, 24], [49, 28, 41, 26], [49, 53, 41, 51], [49, 55, 41, 53], [49, 59, 41, 57, "RotationGestureDetector"], [49, 91, 41, 80], [49, 92, 41, 81], [49, 96, 41, 85], [49, 97, 41, 86, "rotationGestureListener"], [49, 120, 41, 109], [49, 121, 41, 110], [49, 122, 41, 111], [50, 4, 42, 2], [51, 4, 44, 2, "init"], [51, 8, 44, 6, "init"], [51, 9, 44, 7, "ref"], [51, 12, 44, 10], [51, 14, 44, 12, "propsRef"], [51, 22, 44, 20], [51, 24, 44, 22], [52, 6, 45, 4], [52, 11, 45, 9], [52, 12, 45, 10, "init"], [52, 16, 45, 14], [52, 17, 45, 15, "ref"], [52, 20, 45, 18], [52, 22, 45, 20, "propsRef"], [52, 30, 45, 28], [52, 31, 45, 29], [53, 6, 46, 4], [53, 10, 46, 8], [53, 11, 46, 9, "shouldCancelWhenOutside"], [53, 34, 46, 32], [53, 37, 46, 35], [53, 42, 46, 40], [54, 4, 47, 2], [55, 4, 49, 2, "transformNativeEvent"], [55, 24, 49, 22, "transformNativeEvent"], [55, 25, 49, 22], [55, 27, 49, 25], [56, 6, 50, 4], [56, 13, 50, 11], [57, 8, 51, 6, "rotation"], [57, 16, 51, 14], [57, 18, 51, 16], [57, 22, 51, 20], [57, 23, 51, 21, "rotation"], [57, 31, 51, 29], [57, 34, 51, 32], [57, 38, 51, 36], [57, 39, 51, 37, "rotation"], [57, 47, 51, 45], [57, 50, 51, 48], [57, 51, 51, 49], [58, 8, 52, 6, "anchorX"], [58, 15, 52, 13], [58, 17, 52, 15], [58, 21, 52, 19], [58, 22, 52, 20, "getAnchorX"], [58, 32, 52, 30], [58, 33, 52, 31], [58, 34, 52, 32], [59, 8, 53, 6, "anchorY"], [59, 15, 53, 13], [59, 17, 53, 15], [59, 21, 53, 19], [59, 22, 53, 20, "getAnchorY"], [59, 32, 53, 30], [59, 33, 53, 31], [59, 34, 53, 32], [60, 8, 54, 6, "velocity"], [60, 16, 54, 14], [60, 18, 54, 16], [60, 22, 54, 20], [60, 23, 54, 21, "velocity"], [60, 31, 54, 29], [60, 34, 54, 32], [60, 38, 54, 36], [60, 39, 54, 37, "velocity"], [60, 47, 54, 45], [60, 50, 54, 48], [61, 6, 55, 4], [61, 7, 55, 5], [62, 4, 56, 2], [63, 4, 58, 2, "getAnchorX"], [63, 14, 58, 12, "getAnchorX"], [63, 15, 58, 12], [63, 17, 58, 15], [64, 6, 59, 4], [64, 12, 59, 10, "anchorX"], [64, 19, 59, 17], [64, 22, 59, 20], [64, 26, 59, 24], [64, 27, 59, 25, "rotationGestureDetector"], [64, 50, 59, 48], [64, 51, 59, 49, "anchorX"], [64, 58, 59, 56], [65, 6, 60, 4], [65, 13, 60, 11, "anchorX"], [65, 20, 60, 18], [65, 23, 60, 21, "anchorX"], [65, 30, 60, 28], [65, 33, 60, 31], [65, 37, 60, 35], [65, 38, 60, 36, "cachedAnchorX"], [65, 51, 60, 49], [66, 4, 61, 2], [67, 4, 63, 2, "getAnchorY"], [67, 14, 63, 12, "getAnchorY"], [67, 15, 63, 12], [67, 17, 63, 15], [68, 6, 64, 4], [68, 12, 64, 10, "anchorY"], [68, 19, 64, 17], [68, 22, 64, 20], [68, 26, 64, 24], [68, 27, 64, 25, "rotationGestureDetector"], [68, 50, 64, 48], [68, 51, 64, 49, "anchorY"], [68, 58, 64, 56], [69, 6, 65, 4], [69, 13, 65, 11, "anchorY"], [69, 20, 65, 18], [69, 23, 65, 21, "anchorY"], [69, 30, 65, 28], [69, 33, 65, 31], [69, 37, 65, 35], [69, 38, 65, 36, "cachedAnchorY"], [69, 51, 65, 49], [70, 4, 66, 2], [71, 4, 68, 2, "onPointerDown"], [71, 17, 68, 15, "onPointerDown"], [71, 18, 68, 16, "event"], [71, 23, 68, 21], [71, 25, 68, 23], [72, 6, 69, 4], [72, 10, 69, 8], [72, 11, 69, 9, "tracker"], [72, 18, 69, 16], [72, 19, 69, 17, "addToTracker"], [72, 31, 69, 29], [72, 32, 69, 30, "event"], [72, 37, 69, 35], [72, 38, 69, 36], [73, 6, 70, 4], [73, 11, 70, 9], [73, 12, 70, 10, "onPointerDown"], [73, 25, 70, 23], [73, 26, 70, 24, "event"], [73, 31, 70, 29], [73, 32, 70, 30], [74, 6, 71, 4], [74, 10, 71, 8], [74, 11, 71, 9, "tryToSendTouchEvent"], [74, 30, 71, 28], [74, 31, 71, 29, "event"], [74, 36, 71, 34], [74, 37, 71, 35], [75, 4, 72, 2], [76, 4, 74, 2, "onPointerAdd"], [76, 16, 74, 14, "onPointerAdd"], [76, 17, 74, 15, "event"], [76, 22, 74, 20], [76, 24, 74, 22], [77, 6, 75, 4], [77, 10, 75, 8], [77, 11, 75, 9, "tracker"], [77, 18, 75, 16], [77, 19, 75, 17, "addToTracker"], [77, 31, 75, 29], [77, 32, 75, 30, "event"], [77, 37, 75, 35], [77, 38, 75, 36], [78, 6, 76, 4], [78, 11, 76, 9], [78, 12, 76, 10, "onPointerAdd"], [78, 24, 76, 22], [78, 25, 76, 23, "event"], [78, 30, 76, 28], [78, 31, 76, 29], [79, 6, 77, 4], [79, 10, 77, 8], [79, 11, 77, 9, "tryBegin"], [79, 19, 77, 17], [79, 20, 77, 18], [79, 21, 77, 19], [80, 6, 78, 4], [80, 10, 78, 8], [80, 11, 78, 9, "rotationGestureDetector"], [80, 34, 78, 32], [80, 35, 78, 33, "onTouchEvent"], [80, 47, 78, 45], [80, 48, 78, 46, "event"], [80, 53, 78, 51], [80, 55, 78, 53], [80, 59, 78, 57], [80, 60, 78, 58, "tracker"], [80, 67, 78, 65], [80, 68, 78, 66], [81, 4, 79, 2], [82, 4, 81, 2, "onPointerMove"], [82, 17, 81, 15, "onPointerMove"], [82, 18, 81, 16, "event"], [82, 23, 81, 21], [82, 25, 81, 23], [83, 6, 82, 4], [83, 10, 82, 8], [83, 14, 82, 12], [83, 15, 82, 13, "tracker"], [83, 22, 82, 20], [83, 23, 82, 21, "trackedPointersCount"], [83, 43, 82, 41], [83, 46, 82, 44], [83, 47, 82, 45], [83, 49, 82, 47], [84, 8, 83, 6], [85, 6, 84, 4], [86, 6, 86, 4], [86, 10, 86, 8], [86, 14, 86, 12], [86, 15, 86, 13, "getAnchorX"], [86, 25, 86, 23], [86, 26, 86, 24], [86, 27, 86, 25], [86, 29, 86, 27], [87, 8, 87, 6], [87, 12, 87, 10], [87, 13, 87, 11, "cachedAnchorX"], [87, 26, 87, 24], [87, 29, 87, 27], [87, 33, 87, 31], [87, 34, 87, 32, "getAnchorX"], [87, 44, 87, 42], [87, 45, 87, 43], [87, 46, 87, 44], [88, 6, 88, 4], [89, 6, 90, 4], [89, 10, 90, 8], [89, 14, 90, 12], [89, 15, 90, 13, "getAnchorY"], [89, 25, 90, 23], [89, 26, 90, 24], [89, 27, 90, 25], [89, 29, 90, 27], [90, 8, 91, 6], [90, 12, 91, 10], [90, 13, 91, 11, "cachedAnchorY"], [90, 26, 91, 24], [90, 29, 91, 27], [90, 33, 91, 31], [90, 34, 91, 32, "getAnchorY"], [90, 44, 91, 42], [90, 45, 91, 43], [90, 46, 91, 44], [91, 6, 92, 4], [92, 6, 94, 4], [92, 10, 94, 8], [92, 11, 94, 9, "tracker"], [92, 18, 94, 16], [92, 19, 94, 17, "track"], [92, 24, 94, 22], [92, 25, 94, 23, "event"], [92, 30, 94, 28], [92, 31, 94, 29], [93, 6, 95, 4], [93, 10, 95, 8], [93, 11, 95, 9, "rotationGestureDetector"], [93, 34, 95, 32], [93, 35, 95, 33, "onTouchEvent"], [93, 47, 95, 45], [93, 48, 95, 46, "event"], [93, 53, 95, 51], [93, 55, 95, 53], [93, 59, 95, 57], [93, 60, 95, 58, "tracker"], [93, 67, 95, 65], [93, 68, 95, 66], [94, 6, 96, 4], [94, 11, 96, 9], [94, 12, 96, 10, "onPointerMove"], [94, 25, 96, 23], [94, 26, 96, 24, "event"], [94, 31, 96, 29], [94, 32, 96, 30], [95, 4, 97, 2], [96, 4, 99, 2, "onPointerOutOfBounds"], [96, 24, 99, 22, "onPointerOutOfBounds"], [96, 25, 99, 23, "event"], [96, 30, 99, 28], [96, 32, 99, 30], [97, 6, 100, 4], [97, 10, 100, 8], [97, 14, 100, 12], [97, 15, 100, 13, "tracker"], [97, 22, 100, 20], [97, 23, 100, 21, "trackedPointersCount"], [97, 43, 100, 41], [97, 46, 100, 44], [97, 47, 100, 45], [97, 49, 100, 47], [98, 8, 101, 6], [99, 6, 102, 4], [100, 6, 104, 4], [100, 10, 104, 8], [100, 14, 104, 12], [100, 15, 104, 13, "getAnchorX"], [100, 25, 104, 23], [100, 26, 104, 24], [100, 27, 104, 25], [100, 29, 104, 27], [101, 8, 105, 6], [101, 12, 105, 10], [101, 13, 105, 11, "cachedAnchorX"], [101, 26, 105, 24], [101, 29, 105, 27], [101, 33, 105, 31], [101, 34, 105, 32, "getAnchorX"], [101, 44, 105, 42], [101, 45, 105, 43], [101, 46, 105, 44], [102, 6, 106, 4], [103, 6, 108, 4], [103, 10, 108, 8], [103, 14, 108, 12], [103, 15, 108, 13, "getAnchorY"], [103, 25, 108, 23], [103, 26, 108, 24], [103, 27, 108, 25], [103, 29, 108, 27], [104, 8, 109, 6], [104, 12, 109, 10], [104, 13, 109, 11, "cachedAnchorY"], [104, 26, 109, 24], [104, 29, 109, 27], [104, 33, 109, 31], [104, 34, 109, 32, "getAnchorY"], [104, 44, 109, 42], [104, 45, 109, 43], [104, 46, 109, 44], [105, 6, 110, 4], [106, 6, 112, 4], [106, 10, 112, 8], [106, 11, 112, 9, "tracker"], [106, 18, 112, 16], [106, 19, 112, 17, "track"], [106, 24, 112, 22], [106, 25, 112, 23, "event"], [106, 30, 112, 28], [106, 31, 112, 29], [107, 6, 113, 4], [107, 10, 113, 8], [107, 11, 113, 9, "rotationGestureDetector"], [107, 34, 113, 32], [107, 35, 113, 33, "onTouchEvent"], [107, 47, 113, 45], [107, 48, 113, 46, "event"], [107, 53, 113, 51], [107, 55, 113, 53], [107, 59, 113, 57], [107, 60, 113, 58, "tracker"], [107, 67, 113, 65], [107, 68, 113, 66], [108, 6, 114, 4], [108, 11, 114, 9], [108, 12, 114, 10, "onPointerOutOfBounds"], [108, 32, 114, 30], [108, 33, 114, 31, "event"], [108, 38, 114, 36], [108, 39, 114, 37], [109, 4, 115, 2], [110, 4, 117, 2, "onPointerUp"], [110, 15, 117, 13, "onPointerUp"], [110, 16, 117, 14, "event"], [110, 21, 117, 19], [110, 23, 117, 21], [111, 6, 118, 4], [111, 11, 118, 9], [111, 12, 118, 10, "onPointerUp"], [111, 23, 118, 21], [111, 24, 118, 22, "event"], [111, 29, 118, 27], [111, 30, 118, 28], [112, 6, 119, 4], [112, 10, 119, 8], [112, 11, 119, 9, "tracker"], [112, 18, 119, 16], [112, 19, 119, 17, "removeFromTracker"], [112, 36, 119, 34], [112, 37, 119, 35, "event"], [112, 42, 119, 40], [112, 43, 119, 41, "pointerId"], [112, 52, 119, 50], [112, 53, 119, 51], [113, 6, 120, 4], [113, 10, 120, 8], [113, 11, 120, 9, "rotationGestureDetector"], [113, 34, 120, 32], [113, 35, 120, 33, "onTouchEvent"], [113, 47, 120, 45], [113, 48, 120, 46, "event"], [113, 53, 120, 51], [113, 55, 120, 53], [113, 59, 120, 57], [113, 60, 120, 58, "tracker"], [113, 67, 120, 65], [113, 68, 120, 66], [114, 6, 122, 4], [114, 10, 122, 8], [114, 14, 122, 12], [114, 15, 122, 13, "state"], [114, 20, 122, 18], [114, 25, 122, 23, "State"], [114, 37, 122, 28], [114, 38, 122, 29, "ACTIVE"], [114, 44, 122, 35], [114, 46, 122, 37], [115, 8, 123, 6], [116, 6, 124, 4], [117, 6, 126, 4], [117, 10, 126, 8], [117, 14, 126, 12], [117, 15, 126, 13, "state"], [117, 20, 126, 18], [117, 25, 126, 23, "State"], [117, 37, 126, 28], [117, 38, 126, 29, "ACTIVE"], [117, 44, 126, 35], [117, 46, 126, 37], [118, 8, 127, 6], [118, 12, 127, 10], [118, 13, 127, 11, "end"], [118, 16, 127, 14], [118, 17, 127, 15], [118, 18, 127, 16], [119, 6, 128, 4], [119, 7, 128, 5], [119, 13, 128, 11], [120, 8, 129, 6], [120, 12, 129, 10], [120, 13, 129, 11, "fail"], [120, 17, 129, 15], [120, 18, 129, 16], [120, 19, 129, 17], [121, 6, 130, 4], [122, 4, 131, 2], [123, 4, 133, 2, "onPointerRemove"], [123, 19, 133, 17, "onPointerRemove"], [123, 20, 133, 18, "event"], [123, 25, 133, 23], [123, 27, 133, 25], [124, 6, 134, 4], [124, 11, 134, 9], [124, 12, 134, 10, "onPointerRemove"], [124, 27, 134, 25], [124, 28, 134, 26, "event"], [124, 33, 134, 31], [124, 34, 134, 32], [125, 6, 135, 4], [125, 10, 135, 8], [125, 11, 135, 9, "rotationGestureDetector"], [125, 34, 135, 32], [125, 35, 135, 33, "onTouchEvent"], [125, 47, 135, 45], [125, 48, 135, 46, "event"], [125, 53, 135, 51], [125, 55, 135, 53], [125, 59, 135, 57], [125, 60, 135, 58, "tracker"], [125, 67, 135, 65], [125, 68, 135, 66], [126, 6, 136, 4], [126, 10, 136, 8], [126, 11, 136, 9, "tracker"], [126, 18, 136, 16], [126, 19, 136, 17, "removeFromTracker"], [126, 36, 136, 34], [126, 37, 136, 35, "event"], [126, 42, 136, 40], [126, 43, 136, 41, "pointerId"], [126, 52, 136, 50], [126, 53, 136, 51], [127, 4, 137, 2], [128, 4, 139, 2, "tryBegin"], [128, 12, 139, 10, "tryBegin"], [128, 13, 139, 10], [128, 15, 139, 13], [129, 6, 140, 4], [129, 10, 140, 8], [129, 14, 140, 12], [129, 15, 140, 13, "state"], [129, 20, 140, 18], [129, 25, 140, 23, "State"], [129, 37, 140, 28], [129, 38, 140, 29, "UNDETERMINED"], [129, 50, 140, 41], [129, 52, 140, 43], [130, 8, 141, 6], [131, 6, 142, 4], [132, 6, 144, 4], [132, 10, 144, 8], [132, 11, 144, 9, "begin"], [132, 16, 144, 14], [132, 17, 144, 15], [132, 18, 144, 16], [133, 4, 145, 2], [134, 4, 147, 2, "onReset"], [134, 11, 147, 9, "onReset"], [134, 12, 147, 9], [134, 14, 147, 12], [135, 6, 148, 4], [135, 10, 148, 8], [135, 14, 148, 12], [135, 15, 148, 13, "state"], [135, 20, 148, 18], [135, 25, 148, 23, "State"], [135, 37, 148, 28], [135, 38, 148, 29, "ACTIVE"], [135, 44, 148, 35], [135, 46, 148, 37], [136, 8, 149, 6], [137, 6, 150, 4], [138, 6, 152, 4], [138, 10, 152, 8], [138, 11, 152, 9, "rotation"], [138, 19, 152, 17], [138, 22, 152, 20], [138, 23, 152, 21], [139, 6, 153, 4], [139, 10, 153, 8], [139, 11, 153, 9, "velocity"], [139, 19, 153, 17], [139, 22, 153, 20], [139, 23, 153, 21], [140, 6, 154, 4], [140, 10, 154, 8], [140, 11, 154, 9, "rotationGestureDetector"], [140, 34, 154, 32], [140, 35, 154, 33, "reset"], [140, 40, 154, 38], [140, 41, 154, 39], [140, 42, 154, 40], [141, 4, 155, 2], [142, 2, 157, 0], [143, 2, 157, 1, "exports"], [143, 9, 157, 1], [143, 10, 157, 1, "default"], [143, 17, 157, 1], [143, 20, 157, 1, "RotationGestureHandler"], [143, 42, 157, 1], [144, 0, 157, 1], [144, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "RotationGestureHandler", "constructor", "_defineProperty$argument_2.onRotationBegin", "_defineProperty$argument_2.onRotation", "_defineProperty$argument_2.onRotationEnd", "init", "transformNativeEvent", "getAnchorX", "getAnchorY", "onPointerDown", "onPointerAdd", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onPointerRemove", "tryBegin", "onReset"], "mappings": "AAA,iNC;eCM;ECC;uBCY,iBD;kBEC;OFc;qBGC;OHE;GDI;EKE;GLG;EME;GNO;EOE;GPG;EQE;GRG;ESE;GTI;EUE;GVK;EWE;GXgB;EYE;GZgB;EaE;Gbc;EcE;GdI;EeE;GfM;EgBE;GhBQ;CDE"}}, "type": "js/module"}]}