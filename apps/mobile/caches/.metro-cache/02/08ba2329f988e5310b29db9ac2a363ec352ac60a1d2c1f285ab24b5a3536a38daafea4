{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 987}, "end": {"line": 33, "column": 40, "index": 1027}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "@tanstack/react-query", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1028}, "end": {"line": 34, "column": 68, "index": 1096}}], "key": "Pzwu/0TIyhnZOrC9PAkpZx92hFo=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _expoRouter = require(_dependencyMap[21], \"expo-router\");\n  var _reactQuery = require(_dependencyMap[22], \"@tanstack/react-query\");\n  var _jsxDevRuntime = require(_dependencyMap[23], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const router = (0, _expoRouter.useRouter)();\n    const queryClient = (0, _reactQuery.useQueryClient)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0, _react.useState)(null);\n    const [sessionStartTime, setSessionStartTime] = (0, _react.useState)(null);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = async () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n      setSessionStartTime(new Date());\n\n      // Create a new session in the database\n      try {\n        const response = await fetch('/api/sessions', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            title: 'New Brainstorming Session',\n            agent_type: 'general',\n            user_id: null // For now, anonymous sessions\n          })\n        });\n        if (response.ok) {\n          const {\n            session\n          } = await response.json();\n          setCurrentSessionId(session.id);\n\n          // Add initial messages to the session\n          for (const message of _fakeData.fakeMessages) {\n            await fetch('/api/messages', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                session_id: session.id,\n                role: message.role,\n                content: message.content\n              })\n            });\n          }\n        }\n      } catch (error) {\n        console.error('Error creating session:', error);\n      }\n    };\n    const handleSendMessage = async (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Save user message to database if we have a session\n      if (currentSessionId) {\n        try {\n          await fetch('/api/messages', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              session_id: currentSessionId,\n              role: 'user',\n              content: message\n            })\n          });\n        } catch (error) {\n          console.error('Error saving user message:', error);\n        }\n      }\n\n      // Simulate AI response\n      setTimeout(async () => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n\n        // Save AI response to database if we have a session\n        if (currentSessionId) {\n          try {\n            await fetch('/api/messages', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                session_id: currentSessionId,\n                role: 'assistant',\n                content: aiResponse.content,\n                quick_actions: _fakeData.fakeQuickActions\n              })\n            });\n          } catch (error) {\n            console.error('Error saving AI message:', error);\n          }\n        }\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"902VswXJGTYh6FyaM254sBNxdF8=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _expoRouter.useRouter, _reactQuery.useQueryClient, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 683, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 33, 0], [27, 6, 33, 0, "_expoRouter"], [27, 17, 33, 0], [27, 20, 33, 0, "require"], [27, 27, 33, 0], [27, 28, 33, 0, "_dependencyMap"], [27, 42, 33, 0], [28, 2, 34, 0], [28, 6, 34, 0, "_reactQuery"], [28, 17, 34, 0], [28, 20, 34, 0, "require"], [28, 27, 34, 0], [28, 28, 34, 0, "_dependencyMap"], [28, 42, 34, 0], [29, 2, 34, 68], [29, 6, 34, 68, "_jsxDevRuntime"], [29, 20, 34, 68], [29, 23, 34, 68, "require"], [29, 30, 34, 68], [29, 31, 34, 68, "_dependencyMap"], [29, 45, 34, 68], [30, 2, 34, 68], [30, 6, 34, 68, "_jsxFileName"], [30, 18, 34, 68], [31, 4, 34, 68, "_s"], [31, 6, 34, 68], [31, 9, 34, 68, "$RefreshSig$"], [31, 21, 34, 68], [32, 2, 34, 68], [32, 11, 34, 68, "_interopRequireWildcard"], [32, 35, 34, 68, "e"], [32, 36, 34, 68], [32, 38, 34, 68, "t"], [32, 39, 34, 68], [32, 68, 34, 68, "WeakMap"], [32, 75, 34, 68], [32, 81, 34, 68, "r"], [32, 82, 34, 68], [32, 89, 34, 68, "WeakMap"], [32, 96, 34, 68], [32, 100, 34, 68, "n"], [32, 101, 34, 68], [32, 108, 34, 68, "WeakMap"], [32, 115, 34, 68], [32, 127, 34, 68, "_interopRequireWildcard"], [32, 150, 34, 68], [32, 162, 34, 68, "_interopRequireWildcard"], [32, 163, 34, 68, "e"], [32, 164, 34, 68], [32, 166, 34, 68, "t"], [32, 167, 34, 68], [32, 176, 34, 68, "t"], [32, 177, 34, 68], [32, 181, 34, 68, "e"], [32, 182, 34, 68], [32, 186, 34, 68, "e"], [32, 187, 34, 68], [32, 188, 34, 68, "__esModule"], [32, 198, 34, 68], [32, 207, 34, 68, "e"], [32, 208, 34, 68], [32, 214, 34, 68, "o"], [32, 215, 34, 68], [32, 217, 34, 68, "i"], [32, 218, 34, 68], [32, 220, 34, 68, "f"], [32, 221, 34, 68], [32, 226, 34, 68, "__proto__"], [32, 235, 34, 68], [32, 243, 34, 68, "default"], [32, 250, 34, 68], [32, 252, 34, 68, "e"], [32, 253, 34, 68], [32, 270, 34, 68, "e"], [32, 271, 34, 68], [32, 294, 34, 68, "e"], [32, 295, 34, 68], [32, 320, 34, 68, "e"], [32, 321, 34, 68], [32, 330, 34, 68, "f"], [32, 331, 34, 68], [32, 337, 34, 68, "o"], [32, 338, 34, 68], [32, 341, 34, 68, "t"], [32, 342, 34, 68], [32, 345, 34, 68, "n"], [32, 346, 34, 68], [32, 349, 34, 68, "r"], [32, 350, 34, 68], [32, 358, 34, 68, "o"], [32, 359, 34, 68], [32, 360, 34, 68, "has"], [32, 363, 34, 68], [32, 364, 34, 68, "e"], [32, 365, 34, 68], [32, 375, 34, 68, "o"], [32, 376, 34, 68], [32, 377, 34, 68, "get"], [32, 380, 34, 68], [32, 381, 34, 68, "e"], [32, 382, 34, 68], [32, 385, 34, 68, "o"], [32, 386, 34, 68], [32, 387, 34, 68, "set"], [32, 390, 34, 68], [32, 391, 34, 68, "e"], [32, 392, 34, 68], [32, 394, 34, 68, "f"], [32, 395, 34, 68], [32, 411, 34, 68, "t"], [32, 412, 34, 68], [32, 416, 34, 68, "e"], [32, 417, 34, 68], [32, 433, 34, 68, "t"], [32, 434, 34, 68], [32, 441, 34, 68, "hasOwnProperty"], [32, 455, 34, 68], [32, 456, 34, 68, "call"], [32, 460, 34, 68], [32, 461, 34, 68, "e"], [32, 462, 34, 68], [32, 464, 34, 68, "t"], [32, 465, 34, 68], [32, 472, 34, 68, "i"], [32, 473, 34, 68], [32, 477, 34, 68, "o"], [32, 478, 34, 68], [32, 481, 34, 68, "Object"], [32, 487, 34, 68], [32, 488, 34, 68, "defineProperty"], [32, 502, 34, 68], [32, 507, 34, 68, "Object"], [32, 513, 34, 68], [32, 514, 34, 68, "getOwnPropertyDescriptor"], [32, 538, 34, 68], [32, 539, 34, 68, "e"], [32, 540, 34, 68], [32, 542, 34, 68, "t"], [32, 543, 34, 68], [32, 550, 34, 68, "i"], [32, 551, 34, 68], [32, 552, 34, 68, "get"], [32, 555, 34, 68], [32, 559, 34, 68, "i"], [32, 560, 34, 68], [32, 561, 34, 68, "set"], [32, 564, 34, 68], [32, 568, 34, 68, "o"], [32, 569, 34, 68], [32, 570, 34, 68, "f"], [32, 571, 34, 68], [32, 573, 34, 68, "t"], [32, 574, 34, 68], [32, 576, 34, 68, "i"], [32, 577, 34, 68], [32, 581, 34, 68, "f"], [32, 582, 34, 68], [32, 583, 34, 68, "t"], [32, 584, 34, 68], [32, 588, 34, 68, "e"], [32, 589, 34, 68], [32, 590, 34, 68, "t"], [32, 591, 34, 68], [32, 602, 34, 68, "f"], [32, 603, 34, 68], [32, 608, 34, 68, "e"], [32, 609, 34, 68], [32, 611, 34, 68, "t"], [32, 612, 34, 68], [33, 2, 36, 15], [33, 11, 36, 24, "BrainstormScreen"], [33, 27, 36, 40, "BrainstormScreen"], [33, 28, 36, 40], [33, 30, 36, 43], [34, 4, 36, 43, "_s"], [34, 6, 36, 43], [35, 4, 37, 2], [35, 10, 37, 8, "insets"], [35, 16, 37, 14], [35, 19, 37, 17], [35, 23, 37, 17, "useSafeAreaInsets"], [35, 68, 37, 34], [35, 70, 37, 35], [35, 71, 37, 36], [36, 4, 38, 2], [36, 10, 38, 8, "colors"], [36, 16, 38, 14], [36, 19, 38, 17], [36, 23, 38, 17, "useColors"], [36, 43, 38, 26], [36, 45, 38, 27], [36, 46, 38, 28], [37, 4, 39, 2], [37, 10, 39, 8, "router"], [37, 16, 39, 14], [37, 19, 39, 17], [37, 23, 39, 17, "useRouter"], [37, 44, 39, 26], [37, 46, 39, 27], [37, 47, 39, 28], [38, 4, 40, 2], [38, 10, 40, 8, "queryClient"], [38, 21, 40, 19], [38, 24, 40, 22], [38, 28, 40, 22, "useQueryClient"], [38, 54, 40, 36], [38, 56, 40, 37], [38, 57, 40, 38], [39, 4, 41, 2], [39, 10, 41, 8], [39, 11, 41, 9, "fontsLoaded"], [39, 22, 41, 20], [39, 23, 41, 21], [39, 26, 41, 24], [39, 30, 41, 24, "useFonts"], [39, 47, 41, 32], [39, 49, 41, 33], [40, 6, 42, 4, "Poppins_400Regular"], [40, 24, 42, 22], [40, 26, 42, 4, "Poppins_400Regular"], [40, 53, 42, 22], [41, 6, 43, 4, "Poppins_500Medium"], [41, 23, 43, 21], [41, 25, 43, 4, "Poppins_500Medium"], [41, 51, 43, 21], [42, 6, 44, 4, "Poppins_600SemiBold"], [42, 25, 44, 23], [42, 27, 44, 4, "Poppins_600SemiBold"], [43, 4, 45, 2], [43, 5, 45, 3], [43, 6, 45, 4], [44, 4, 47, 2], [44, 10, 47, 8, "recorder"], [44, 18, 47, 16], [44, 21, 47, 19], [44, 25, 47, 19, "useAudioRecorder"], [44, 52, 47, 35], [44, 54, 47, 36, "RecordingPresets"], [44, 81, 47, 52], [44, 82, 47, 53, "HIGH_QUALITY"], [44, 94, 47, 65], [44, 95, 47, 66], [45, 4, 48, 2], [45, 10, 48, 8, "recorderState"], [45, 23, 48, 21], [45, 26, 48, 24], [45, 30, 48, 24, "useAudioRecorderState"], [45, 62, 48, 45], [45, 64, 48, 46, "recorder"], [45, 72, 48, 54], [45, 73, 48, 55], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "messages"], [46, 19, 50, 17], [46, 21, 50, 19, "setMessages"], [46, 32, 50, 30], [46, 33, 50, 31], [46, 36, 50, 34], [46, 40, 50, 34, "useState"], [46, 55, 50, 42], [46, 57, 50, 43], [46, 59, 50, 45], [46, 60, 50, 46], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "inputText"], [47, 20, 51, 18], [47, 22, 51, 20, "setInputText"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 61, 51, 47], [47, 62, 51, 48], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "isFirstTime"], [48, 22, 52, 20], [48, 24, 52, 22, "setIsFirstTime"], [48, 38, 52, 36], [48, 39, 52, 37], [48, 42, 52, 40], [48, 46, 52, 40, "useState"], [48, 61, 52, 48], [48, 63, 52, 49], [48, 67, 52, 53], [48, 68, 52, 54], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isLoading"], [49, 20, 53, 18], [49, 22, 53, 20, "setIsLoading"], [49, 34, 53, 32], [49, 35, 53, 33], [49, 38, 53, 36], [49, 42, 53, 36, "useState"], [49, 57, 53, 44], [49, 59, 53, 45], [49, 64, 53, 50], [49, 65, 53, 51], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "quickActions"], [50, 23, 54, 21], [50, 25, 54, 23, "setQuickActions"], [50, 40, 54, 38], [50, 41, 54, 39], [50, 44, 54, 42], [50, 48, 54, 42, "useState"], [50, 63, 54, 50], [50, 65, 54, 51], [50, 67, 54, 53], [50, 68, 54, 54], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "voiceMode"], [51, 20, 55, 18], [51, 22, 55, 20, "setVoiceMode"], [51, 34, 55, 32], [51, 35, 55, 33], [51, 38, 55, 36], [51, 42, 55, 36, "useState"], [51, 57, 55, 44], [51, 59, 55, 45], [51, 63, 55, 49], [51, 64, 55, 50], [51, 65, 55, 51], [51, 66, 55, 52], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "hasPermission"], [52, 24, 56, 22], [52, 26, 56, 24, "setHasPermission"], [52, 42, 56, 40], [52, 43, 56, 41], [52, 46, 56, 44], [52, 50, 56, 44, "useState"], [52, 65, 56, 52], [52, 67, 56, 53], [52, 72, 56, 58], [52, 73, 56, 59], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isDictating"], [53, 22, 57, 20], [53, 24, 57, 22, "setIsDictating"], [53, 38, 57, 36], [53, 39, 57, 37], [53, 42, 57, 40], [53, 46, 57, 40, "useState"], [53, 61, 57, 48], [53, 63, 57, 49], [53, 68, 57, 54], [53, 69, 57, 55], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "longPressedMessage"], [54, 29, 58, 27], [54, 31, 58, 29, "setLongPressedMessage"], [54, 52, 58, 50], [54, 53, 58, 51], [54, 56, 58, 54], [54, 60, 58, 54, "useState"], [54, 75, 58, 62], [54, 77, 58, 63], [54, 81, 58, 67], [54, 82, 58, 68], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "isContextMenuVisible"], [55, 31, 59, 29], [55, 33, 59, 31, "setIsContextMenuVisible"], [55, 56, 59, 54], [55, 57, 59, 55], [55, 60, 59, 58], [55, 64, 59, 58, "useState"], [55, 79, 59, 66], [55, 81, 59, 67], [55, 86, 59, 72], [55, 87, 59, 73], [56, 4, 60, 2], [56, 10, 60, 8], [56, 11, 60, 9, "transcript"], [56, 21, 60, 19], [56, 23, 60, 21, "setTranscript"], [56, 36, 60, 34], [56, 37, 60, 35], [56, 40, 60, 38], [56, 44, 60, 38, "useState"], [56, 59, 60, 46], [56, 61, 60, 47], [56, 63, 60, 49], [56, 64, 60, 50], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "isMuted"], [57, 18, 61, 16], [57, 20, 61, 18, "setIsMuted"], [57, 30, 61, 28], [57, 31, 61, 29], [57, 34, 61, 32], [57, 38, 61, 32, "useState"], [57, 53, 61, 40], [57, 55, 61, 41], [57, 60, 61, 46], [57, 61, 61, 47], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "currentSessionId"], [58, 27, 62, 25], [58, 29, 62, 27, "setCurrentSessionId"], [58, 48, 62, 46], [58, 49, 62, 47], [58, 52, 62, 50], [58, 56, 62, 50, "useState"], [58, 71, 62, 58], [58, 73, 62, 59], [58, 77, 62, 63], [58, 78, 62, 64], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "sessionStartTime"], [59, 27, 63, 25], [59, 29, 63, 27, "setSessionStartTime"], [59, 48, 63, 46], [59, 49, 63, 47], [59, 52, 63, 50], [59, 56, 63, 50, "useState"], [59, 71, 63, 58], [59, 73, 63, 59], [59, 77, 63, 63], [59, 78, 63, 64], [60, 4, 65, 2], [60, 10, 65, 8, "scrollViewRef"], [60, 23, 65, 21], [60, 26, 65, 24], [60, 30, 65, 24, "useRef"], [60, 43, 65, 30], [60, 45, 65, 31], [60, 49, 65, 35], [60, 50, 65, 36], [62, 4, 67, 2], [63, 4, 68, 2], [63, 8, 68, 2, "useEffect"], [63, 24, 68, 11], [63, 26, 68, 12], [63, 32, 68, 18], [64, 6, 69, 4], [64, 7, 69, 5], [64, 19, 69, 17], [65, 8, 70, 6], [65, 14, 70, 12], [66, 10, 70, 14, "granted"], [67, 8, 70, 22], [67, 9, 70, 23], [67, 12, 70, 26], [67, 18, 70, 32], [67, 22, 70, 32, "requestRecordingPermissionsAsync"], [67, 65, 70, 64], [67, 67, 70, 65], [67, 68, 70, 66], [68, 8, 71, 6, "setHasPermission"], [68, 24, 71, 22], [68, 25, 71, 23, "granted"], [68, 32, 71, 30], [68, 33, 71, 31], [69, 8, 72, 6], [69, 12, 72, 10], [69, 13, 72, 11, "granted"], [69, 20, 72, 18], [69, 24, 72, 22, "voiceMode"], [69, 33, 72, 31], [69, 35, 72, 33], [70, 10, 73, 8, "<PERSON><PERSON>"], [70, 24, 73, 13], [70, 25, 73, 14, "alert"], [70, 30, 73, 19], [70, 31, 74, 10], [70, 52, 74, 31], [70, 54, 75, 10], [70, 133, 75, 89], [70, 135, 76, 10], [70, 136, 77, 12], [71, 12, 77, 14, "text"], [71, 16, 77, 18], [71, 18, 77, 20], [71, 33, 77, 35], [72, 12, 77, 37, "onPress"], [72, 19, 77, 44], [72, 21, 77, 46, "onPress"], [72, 22, 77, 46], [72, 27, 77, 52, "setVoiceMode"], [72, 39, 77, 64], [72, 40, 77, 65], [72, 45, 77, 70], [73, 10, 77, 72], [73, 11, 77, 73], [73, 13, 78, 12], [74, 12, 78, 14, "text"], [74, 16, 78, 18], [74, 18, 78, 20], [75, 10, 78, 25], [75, 11, 78, 26], [75, 12, 80, 8], [75, 13, 80, 9], [76, 8, 81, 6], [77, 6, 82, 4], [77, 7, 82, 5], [77, 9, 82, 7], [77, 10, 82, 8], [78, 4, 83, 2], [78, 5, 83, 3], [78, 7, 83, 5], [78, 8, 83, 6, "voiceMode"], [78, 17, 83, 15], [78, 18, 83, 16], [78, 19, 83, 17], [79, 4, 85, 2], [79, 10, 85, 8, "handleStartBrainstorming"], [79, 34, 85, 32], [79, 37, 85, 35], [79, 43, 85, 35, "handleStartBrainstorming"], [79, 44, 85, 35], [79, 49, 85, 47], [80, 6, 86, 4, "Haptics"], [80, 13, 86, 11], [80, 14, 86, 12, "impactAsync"], [80, 25, 86, 23], [80, 26, 86, 24, "Haptics"], [80, 33, 86, 31], [80, 34, 86, 32, "ImpactFeedbackStyle"], [80, 53, 86, 51], [80, 54, 86, 52, "Medium"], [80, 60, 86, 58], [80, 61, 86, 59], [81, 6, 87, 4, "setIsFirstTime"], [81, 20, 87, 18], [81, 21, 87, 19], [81, 26, 87, 24], [81, 27, 87, 25], [82, 6, 88, 4, "setMessages"], [82, 17, 88, 15], [82, 18, 88, 16, "fakeMessages"], [82, 40, 88, 28], [82, 41, 88, 29], [83, 6, 89, 4, "setQuickActions"], [83, 21, 89, 19], [83, 22, 89, 20, "fakeQuickActions"], [83, 48, 89, 36], [83, 49, 89, 37], [84, 6, 90, 4, "setSessionStartTime"], [84, 25, 90, 23], [84, 26, 90, 24], [84, 30, 90, 28, "Date"], [84, 34, 90, 32], [84, 35, 90, 33], [84, 36, 90, 34], [84, 37, 90, 35], [86, 6, 92, 4], [87, 6, 93, 4], [87, 10, 93, 8], [88, 8, 94, 6], [88, 14, 94, 12, "response"], [88, 22, 94, 20], [88, 25, 94, 23], [88, 31, 94, 29, "fetch"], [88, 36, 94, 34], [88, 37, 94, 35], [88, 52, 94, 50], [88, 54, 94, 52], [89, 10, 95, 8, "method"], [89, 16, 95, 14], [89, 18, 95, 16], [89, 24, 95, 22], [90, 10, 96, 8, "headers"], [90, 17, 96, 15], [90, 19, 96, 17], [91, 12, 96, 19], [91, 26, 96, 33], [91, 28, 96, 35], [92, 10, 96, 54], [92, 11, 96, 55], [93, 10, 97, 8, "body"], [93, 14, 97, 12], [93, 16, 97, 14, "JSON"], [93, 20, 97, 18], [93, 21, 97, 19, "stringify"], [93, 30, 97, 28], [93, 31, 97, 29], [94, 12, 98, 10, "title"], [94, 17, 98, 15], [94, 19, 98, 17], [94, 46, 98, 44], [95, 12, 99, 10, "agent_type"], [95, 22, 99, 20], [95, 24, 99, 22], [95, 33, 99, 31], [96, 12, 100, 10, "user_id"], [96, 19, 100, 17], [96, 21, 100, 19], [96, 25, 100, 23], [96, 26, 100, 25], [97, 10, 101, 8], [97, 11, 101, 9], [98, 8, 102, 6], [98, 9, 102, 7], [98, 10, 102, 8], [99, 8, 104, 6], [99, 12, 104, 10, "response"], [99, 20, 104, 18], [99, 21, 104, 19, "ok"], [99, 23, 104, 21], [99, 25, 104, 23], [100, 10, 105, 8], [100, 16, 105, 14], [101, 12, 105, 16, "session"], [102, 10, 105, 24], [102, 11, 105, 25], [102, 14, 105, 28], [102, 20, 105, 34, "response"], [102, 28, 105, 42], [102, 29, 105, 43, "json"], [102, 33, 105, 47], [102, 34, 105, 48], [102, 35, 105, 49], [103, 10, 106, 8, "setCurrentSessionId"], [103, 29, 106, 27], [103, 30, 106, 28, "session"], [103, 37, 106, 35], [103, 38, 106, 36, "id"], [103, 40, 106, 38], [103, 41, 106, 39], [105, 10, 108, 8], [106, 10, 109, 8], [106, 15, 109, 13], [106, 21, 109, 19, "message"], [106, 28, 109, 26], [106, 32, 109, 30, "fakeMessages"], [106, 54, 109, 42], [106, 56, 109, 44], [107, 12, 110, 10], [107, 18, 110, 16, "fetch"], [107, 23, 110, 21], [107, 24, 110, 22], [107, 39, 110, 37], [107, 41, 110, 39], [108, 14, 111, 12, "method"], [108, 20, 111, 18], [108, 22, 111, 20], [108, 28, 111, 26], [109, 14, 112, 12, "headers"], [109, 21, 112, 19], [109, 23, 112, 21], [110, 16, 112, 23], [110, 30, 112, 37], [110, 32, 112, 39], [111, 14, 112, 58], [111, 15, 112, 59], [112, 14, 113, 12, "body"], [112, 18, 113, 16], [112, 20, 113, 18, "JSON"], [112, 24, 113, 22], [112, 25, 113, 23, "stringify"], [112, 34, 113, 32], [112, 35, 113, 33], [113, 16, 114, 14, "session_id"], [113, 26, 114, 24], [113, 28, 114, 26, "session"], [113, 35, 114, 33], [113, 36, 114, 34, "id"], [113, 38, 114, 36], [114, 16, 115, 14, "role"], [114, 20, 115, 18], [114, 22, 115, 20, "message"], [114, 29, 115, 27], [114, 30, 115, 28, "role"], [114, 34, 115, 32], [115, 16, 116, 14, "content"], [115, 23, 116, 21], [115, 25, 116, 23, "message"], [115, 32, 116, 30], [115, 33, 116, 31, "content"], [116, 14, 117, 12], [116, 15, 117, 13], [117, 12, 118, 10], [117, 13, 118, 11], [117, 14, 118, 12], [118, 10, 119, 8], [119, 8, 120, 6], [120, 6, 121, 4], [120, 7, 121, 5], [120, 8, 121, 6], [120, 15, 121, 13, "error"], [120, 20, 121, 18], [120, 22, 121, 20], [121, 8, 122, 6, "console"], [121, 15, 122, 13], [121, 16, 122, 14, "error"], [121, 21, 122, 19], [121, 22, 122, 20], [121, 47, 122, 45], [121, 49, 122, 47, "error"], [121, 54, 122, 52], [121, 55, 122, 53], [122, 6, 123, 4], [123, 4, 124, 2], [123, 5, 124, 3], [124, 4, 126, 2], [124, 10, 126, 8, "handleSendMessage"], [124, 27, 126, 25], [124, 30, 126, 28], [124, 36, 126, 28, "handleSendMessage"], [124, 37, 126, 35, "message"], [124, 44, 126, 42], [124, 47, 126, 45, "inputText"], [124, 56, 126, 54], [124, 61, 126, 59], [125, 6, 127, 4], [125, 10, 127, 8], [125, 11, 127, 9, "message"], [125, 18, 127, 16], [125, 19, 127, 17, "trim"], [125, 23, 127, 21], [125, 24, 127, 22], [125, 25, 127, 23], [125, 27, 127, 25], [126, 6, 129, 4], [126, 12, 129, 10, "newUserMessage"], [126, 26, 129, 24], [126, 29, 129, 27], [127, 8, 129, 29, "role"], [127, 12, 129, 33], [127, 14, 129, 35], [127, 20, 129, 41], [128, 8, 129, 43, "content"], [128, 15, 129, 50], [128, 17, 129, 52, "message"], [129, 6, 129, 60], [129, 7, 129, 61], [130, 6, 130, 4, "setMessages"], [130, 17, 130, 15], [130, 18, 130, 17, "prev"], [130, 22, 130, 21], [130, 26, 130, 26], [130, 27, 130, 27], [130, 30, 130, 30, "prev"], [130, 34, 130, 34], [130, 36, 130, 36, "newUserMessage"], [130, 50, 130, 50], [130, 51, 130, 51], [130, 52, 130, 52], [131, 6, 131, 4, "setInputText"], [131, 18, 131, 16], [131, 19, 131, 17], [131, 21, 131, 19], [131, 22, 131, 20], [132, 6, 132, 4, "setQuickActions"], [132, 21, 132, 19], [132, 22, 132, 20], [132, 24, 132, 22], [132, 25, 132, 23], [133, 6, 133, 4, "setIsLoading"], [133, 18, 133, 16], [133, 19, 133, 17], [133, 23, 133, 21], [133, 24, 133, 22], [135, 6, 135, 4], [136, 6, 136, 4], [136, 10, 136, 8, "currentSessionId"], [136, 26, 136, 24], [136, 28, 136, 26], [137, 8, 137, 6], [137, 12, 137, 10], [138, 10, 138, 8], [138, 16, 138, 14, "fetch"], [138, 21, 138, 19], [138, 22, 138, 20], [138, 37, 138, 35], [138, 39, 138, 37], [139, 12, 139, 10, "method"], [139, 18, 139, 16], [139, 20, 139, 18], [139, 26, 139, 24], [140, 12, 140, 10, "headers"], [140, 19, 140, 17], [140, 21, 140, 19], [141, 14, 140, 21], [141, 28, 140, 35], [141, 30, 140, 37], [142, 12, 140, 56], [142, 13, 140, 57], [143, 12, 141, 10, "body"], [143, 16, 141, 14], [143, 18, 141, 16, "JSON"], [143, 22, 141, 20], [143, 23, 141, 21, "stringify"], [143, 32, 141, 30], [143, 33, 141, 31], [144, 14, 142, 12, "session_id"], [144, 24, 142, 22], [144, 26, 142, 24, "currentSessionId"], [144, 42, 142, 40], [145, 14, 143, 12, "role"], [145, 18, 143, 16], [145, 20, 143, 18], [145, 26, 143, 24], [146, 14, 144, 12, "content"], [146, 21, 144, 19], [146, 23, 144, 21, "message"], [147, 12, 145, 10], [147, 13, 145, 11], [148, 10, 146, 8], [148, 11, 146, 9], [148, 12, 146, 10], [149, 8, 147, 6], [149, 9, 147, 7], [149, 10, 147, 8], [149, 17, 147, 15, "error"], [149, 22, 147, 20], [149, 24, 147, 22], [150, 10, 148, 8, "console"], [150, 17, 148, 15], [150, 18, 148, 16, "error"], [150, 23, 148, 21], [150, 24, 148, 22], [150, 52, 148, 50], [150, 54, 148, 52, "error"], [150, 59, 148, 57], [150, 60, 148, 58], [151, 8, 149, 6], [152, 6, 150, 4], [154, 6, 152, 4], [155, 6, 153, 4, "setTimeout"], [155, 16, 153, 14], [155, 17, 153, 15], [155, 29, 153, 27], [156, 8, 154, 6], [156, 14, 154, 12, "aiResponse"], [156, 24, 154, 22], [156, 27, 154, 25], [157, 10, 155, 8, "role"], [157, 14, 155, 12], [157, 16, 155, 14], [157, 27, 155, 25], [158, 10, 156, 8, "content"], [158, 17, 156, 15], [158, 19, 156, 17], [159, 8, 157, 6], [159, 9, 157, 7], [160, 8, 158, 6, "setMessages"], [160, 19, 158, 17], [160, 20, 158, 19, "prev"], [160, 24, 158, 23], [160, 28, 158, 28], [160, 29, 158, 29], [160, 32, 158, 32, "prev"], [160, 36, 158, 36], [160, 38, 158, 38, "aiResponse"], [160, 48, 158, 48], [160, 49, 158, 49], [160, 50, 158, 50], [161, 8, 159, 6, "setQuickActions"], [161, 23, 159, 21], [161, 24, 159, 22, "fakeQuickActions"], [161, 50, 159, 38], [161, 51, 159, 39], [162, 8, 160, 6, "setIsLoading"], [162, 20, 160, 18], [162, 21, 160, 19], [162, 26, 160, 24], [162, 27, 160, 25], [164, 8, 162, 6], [165, 8, 163, 6], [165, 12, 163, 10, "currentSessionId"], [165, 28, 163, 26], [165, 30, 163, 28], [166, 10, 164, 8], [166, 14, 164, 12], [167, 12, 165, 10], [167, 18, 165, 16, "fetch"], [167, 23, 165, 21], [167, 24, 165, 22], [167, 39, 165, 37], [167, 41, 165, 39], [168, 14, 166, 12, "method"], [168, 20, 166, 18], [168, 22, 166, 20], [168, 28, 166, 26], [169, 14, 167, 12, "headers"], [169, 21, 167, 19], [169, 23, 167, 21], [170, 16, 167, 23], [170, 30, 167, 37], [170, 32, 167, 39], [171, 14, 167, 58], [171, 15, 167, 59], [172, 14, 168, 12, "body"], [172, 18, 168, 16], [172, 20, 168, 18, "JSON"], [172, 24, 168, 22], [172, 25, 168, 23, "stringify"], [172, 34, 168, 32], [172, 35, 168, 33], [173, 16, 169, 14, "session_id"], [173, 26, 169, 24], [173, 28, 169, 26, "currentSessionId"], [173, 44, 169, 42], [174, 16, 170, 14, "role"], [174, 20, 170, 18], [174, 22, 170, 20], [174, 33, 170, 31], [175, 16, 171, 14, "content"], [175, 23, 171, 21], [175, 25, 171, 23, "aiResponse"], [175, 35, 171, 33], [175, 36, 171, 34, "content"], [175, 43, 171, 41], [176, 16, 172, 14, "quick_actions"], [176, 29, 172, 27], [176, 31, 172, 29, "fakeQuickActions"], [177, 14, 173, 12], [177, 15, 173, 13], [178, 12, 174, 10], [178, 13, 174, 11], [178, 14, 174, 12], [179, 10, 175, 8], [179, 11, 175, 9], [179, 12, 175, 10], [179, 19, 175, 17, "error"], [179, 24, 175, 22], [179, 26, 175, 24], [180, 12, 176, 10, "console"], [180, 19, 176, 17], [180, 20, 176, 18, "error"], [180, 25, 176, 23], [180, 26, 176, 24], [180, 52, 176, 50], [180, 54, 176, 52, "error"], [180, 59, 176, 57], [180, 60, 176, 58], [181, 10, 177, 8], [182, 8, 178, 6], [183, 6, 179, 4], [183, 7, 179, 5], [183, 9, 179, 7], [183, 13, 179, 11], [183, 14, 179, 12], [184, 4, 180, 2], [184, 5, 180, 3], [185, 4, 182, 2], [185, 10, 182, 8, "handleQuickAction"], [185, 27, 182, 25], [185, 30, 182, 29, "action"], [185, 36, 182, 35], [185, 40, 182, 40], [186, 6, 183, 4, "Haptics"], [186, 13, 183, 11], [186, 14, 183, 12, "impactAsync"], [186, 25, 183, 23], [186, 26, 183, 24, "Haptics"], [186, 33, 183, 31], [186, 34, 183, 32, "ImpactFeedbackStyle"], [186, 53, 183, 51], [186, 54, 183, 52, "Light"], [186, 59, 183, 57], [186, 60, 183, 58], [187, 6, 184, 4, "handleSendMessage"], [187, 23, 184, 21], [187, 24, 184, 22, "action"], [187, 30, 184, 28], [187, 31, 184, 29], [188, 4, 185, 2], [188, 5, 185, 3], [189, 4, 187, 2], [189, 10, 187, 8, "toggleVoiceMode"], [189, 25, 187, 23], [189, 28, 187, 26], [189, 34, 187, 26, "toggleVoiceMode"], [189, 35, 187, 26], [189, 40, 187, 38], [190, 6, 188, 4], [190, 10, 188, 8], [190, 11, 188, 9, "voiceMode"], [190, 20, 188, 18], [190, 24, 188, 22], [190, 25, 188, 23, "hasPermission"], [190, 38, 188, 36], [190, 40, 188, 38], [191, 8, 189, 6, "<PERSON><PERSON>"], [191, 22, 189, 11], [191, 23, 189, 12, "alert"], [191, 28, 189, 17], [191, 29, 190, 8], [191, 50, 190, 29], [191, 52, 191, 8], [191, 102, 191, 58], [191, 104, 192, 8], [191, 105, 193, 10], [192, 10, 193, 12, "text"], [192, 14, 193, 16], [192, 16, 193, 18], [193, 8, 193, 27], [193, 9, 193, 28], [193, 11, 194, 10], [194, 10, 195, 12, "text"], [194, 14, 195, 16], [194, 16, 195, 18], [194, 34, 195, 36], [195, 10, 196, 12, "onPress"], [195, 17, 196, 19], [195, 19, 196, 21], [195, 25, 196, 21, "onPress"], [195, 26, 196, 21], [195, 31, 196, 33], [196, 12, 197, 14], [196, 18, 197, 20], [197, 14, 197, 22, "granted"], [198, 12, 197, 30], [198, 13, 197, 31], [198, 16, 197, 34], [198, 22, 197, 40], [198, 26, 197, 40, "requestRecordingPermissionsAsync"], [198, 69, 197, 72], [198, 71, 197, 73], [198, 72, 197, 74], [199, 12, 198, 14], [199, 16, 198, 18, "granted"], [199, 23, 198, 25], [199, 25, 198, 27], [200, 14, 199, 16, "setHasPermission"], [200, 30, 199, 32], [200, 31, 199, 33], [200, 35, 199, 37], [200, 36, 199, 38], [201, 14, 200, 16, "setVoiceMode"], [201, 26, 200, 28], [201, 27, 200, 29], [201, 31, 200, 33], [201, 32, 200, 34], [202, 14, 201, 16], [203, 14, 202, 16], [203, 20, 202, 22, "startVoiceSession"], [203, 37, 202, 39], [203, 38, 202, 40], [203, 39, 202, 41], [204, 12, 203, 14], [205, 10, 204, 12], [206, 8, 205, 10], [206, 9, 205, 11], [206, 10, 207, 6], [206, 11, 207, 7], [207, 8, 208, 6], [208, 6, 209, 4], [209, 6, 211, 4, "Haptics"], [209, 13, 211, 11], [209, 14, 211, 12, "impactAsync"], [209, 25, 211, 23], [209, 26, 211, 24, "Haptics"], [209, 33, 211, 31], [209, 34, 211, 32, "ImpactFeedbackStyle"], [209, 53, 211, 51], [209, 54, 211, 52, "Light"], [209, 59, 211, 57], [209, 60, 211, 58], [210, 6, 213, 4], [210, 10, 213, 8], [210, 11, 213, 9, "voiceMode"], [210, 20, 213, 18], [210, 22, 213, 20], [211, 8, 214, 6], [212, 8, 215, 6, "setVoiceMode"], [212, 20, 215, 18], [212, 21, 215, 19], [212, 25, 215, 23], [212, 26, 215, 24], [213, 8, 216, 6], [213, 12, 216, 10, "hasPermission"], [213, 25, 216, 23], [213, 27, 216, 25], [214, 10, 217, 8], [214, 16, 217, 14, "startVoiceSession"], [214, 33, 217, 31], [214, 34, 217, 32], [214, 35, 217, 33], [215, 8, 218, 6], [216, 6, 219, 4], [216, 7, 219, 5], [216, 13, 219, 11], [217, 8, 220, 6], [218, 8, 221, 6, "setVoiceMode"], [218, 20, 221, 18], [218, 21, 221, 19], [218, 26, 221, 24], [218, 27, 221, 25], [219, 8, 222, 6], [219, 12, 222, 10, "recorderState"], [219, 25, 222, 23], [219, 26, 222, 24, "isRecording"], [219, 37, 222, 35], [219, 39, 222, 37], [220, 10, 223, 8], [220, 16, 223, 14, "recorder"], [220, 24, 223, 22], [220, 25, 223, 23, "stop"], [220, 29, 223, 27], [220, 30, 223, 28], [220, 31, 223, 29], [221, 8, 224, 6], [222, 8, 225, 6, "setIsMuted"], [222, 18, 225, 16], [222, 19, 225, 17], [222, 24, 225, 22], [222, 25, 225, 23], [222, 26, 225, 24], [222, 27, 225, 25], [223, 6, 226, 4], [224, 4, 227, 2], [224, 5, 227, 3], [225, 4, 229, 2], [225, 10, 229, 8, "startVoiceSession"], [225, 27, 229, 25], [225, 30, 229, 28], [225, 36, 229, 28, "startVoiceSession"], [225, 37, 229, 28], [225, 42, 229, 40], [226, 6, 230, 4], [226, 10, 230, 8], [226, 11, 230, 9, "hasPermission"], [226, 24, 230, 22], [226, 26, 230, 24], [227, 6, 232, 4], [227, 10, 232, 8], [228, 8, 233, 6], [228, 14, 233, 12, "recorder"], [228, 22, 233, 20], [228, 23, 233, 21, "prepareToRecordAsync"], [228, 43, 233, 41], [228, 44, 233, 42], [228, 45, 233, 43], [229, 8, 234, 6, "recorder"], [229, 16, 234, 14], [229, 17, 234, 15, "record"], [229, 23, 234, 21], [229, 24, 234, 22], [229, 25, 234, 23], [230, 8, 235, 6, "setIsMuted"], [230, 18, 235, 16], [230, 19, 235, 17], [230, 24, 235, 22], [230, 25, 235, 23], [230, 26, 235, 24], [230, 27, 235, 25], [231, 8, 236, 6, "Haptics"], [231, 15, 236, 13], [231, 16, 236, 14, "impactAsync"], [231, 27, 236, 25], [231, 28, 236, 26, "Haptics"], [231, 35, 236, 33], [231, 36, 236, 34, "ImpactFeedbackStyle"], [231, 55, 236, 53], [231, 56, 236, 54, "Medium"], [231, 62, 236, 60], [231, 63, 236, 61], [232, 6, 237, 4], [232, 7, 237, 5], [232, 8, 237, 6], [232, 15, 237, 13, "error"], [232, 20, 237, 18], [232, 22, 237, 20], [233, 8, 238, 6, "console"], [233, 15, 238, 13], [233, 16, 238, 14, "error"], [233, 21, 238, 19], [233, 22, 238, 20], [233, 53, 238, 51], [233, 55, 238, 53, "error"], [233, 60, 238, 58], [233, 61, 238, 59], [234, 8, 239, 6, "<PERSON><PERSON>"], [234, 22, 239, 11], [234, 23, 239, 12, "alert"], [234, 28, 239, 17], [234, 29, 239, 18], [234, 36, 239, 25], [234, 38, 239, 27], [234, 69, 239, 58], [234, 70, 239, 59], [235, 6, 240, 4], [236, 4, 241, 2], [236, 5, 241, 3], [237, 4, 243, 2], [237, 10, 243, 8, "stopVoiceSession"], [237, 26, 243, 24], [237, 29, 243, 27], [237, 35, 243, 27, "stopVoiceSession"], [237, 36, 243, 27], [237, 41, 243, 39], [238, 6, 244, 4], [238, 10, 244, 8], [239, 8, 245, 6], [239, 12, 245, 10, "recorderState"], [239, 25, 245, 23], [239, 26, 245, 24, "isRecording"], [239, 37, 245, 35], [239, 39, 245, 37], [240, 10, 246, 8], [240, 16, 246, 14, "recorder"], [240, 24, 246, 22], [240, 25, 246, 23, "stop"], [240, 29, 246, 27], [240, 30, 246, 28], [240, 31, 246, 29], [241, 10, 247, 8, "Haptics"], [241, 17, 247, 15], [241, 18, 247, 16, "impactAsync"], [241, 29, 247, 27], [241, 30, 247, 28, "Haptics"], [241, 37, 247, 35], [241, 38, 247, 36, "ImpactFeedbackStyle"], [241, 57, 247, 55], [241, 58, 247, 56, "Medium"], [241, 64, 247, 62], [241, 65, 247, 63], [242, 8, 248, 6], [243, 6, 249, 4], [243, 7, 249, 5], [243, 8, 249, 6], [243, 15, 249, 13, "error"], [243, 20, 249, 18], [243, 22, 249, 20], [244, 8, 250, 6, "console"], [244, 15, 250, 13], [244, 16, 250, 14, "error"], [244, 21, 250, 19], [244, 22, 250, 20], [244, 53, 250, 51], [244, 55, 250, 53, "error"], [244, 60, 250, 58], [244, 61, 250, 59], [245, 6, 251, 4], [246, 4, 252, 2], [246, 5, 252, 3], [247, 4, 254, 2], [247, 10, 254, 8, "handleDictation"], [247, 25, 254, 23], [247, 28, 254, 26], [247, 34, 254, 26, "handleDictation"], [247, 35, 254, 26], [247, 40, 254, 38], [248, 6, 255, 4], [248, 10, 255, 8], [248, 11, 255, 9, "hasPermission"], [248, 24, 255, 22], [248, 26, 255, 24], [249, 8, 256, 6, "<PERSON><PERSON>"], [249, 22, 256, 11], [249, 23, 256, 12, "alert"], [249, 28, 256, 17], [249, 29, 257, 8], [249, 50, 257, 29], [249, 52, 258, 8], [249, 102, 259, 6], [249, 103, 259, 7], [250, 8, 260, 6], [251, 6, 261, 4], [252, 6, 263, 4], [252, 10, 263, 8], [253, 8, 264, 6], [253, 12, 264, 10, "isDictating"], [253, 23, 264, 21], [253, 25, 264, 23], [254, 10, 265, 8], [254, 16, 265, 14, "recorder"], [254, 24, 265, 22], [254, 25, 265, 23, "stop"], [254, 29, 265, 27], [254, 30, 265, 28], [254, 31, 265, 29], [255, 10, 266, 8], [255, 16, 266, 14, "mockTranscript"], [255, 30, 266, 28], [255, 33, 266, 31], [255, 62, 266, 60], [255, 63, 266, 61], [255, 64, 266, 62], [256, 10, 267, 8, "setInputText"], [256, 22, 267, 20], [256, 23, 267, 21, "inputText"], [256, 32, 267, 30], [256, 35, 267, 33, "mockTranscript"], [256, 49, 267, 47], [256, 50, 267, 48], [257, 10, 268, 8, "setIsDictating"], [257, 24, 268, 22], [257, 25, 268, 23], [257, 30, 268, 28], [257, 31, 268, 29], [258, 8, 269, 6], [258, 9, 269, 7], [258, 15, 269, 13], [259, 10, 270, 8], [259, 16, 270, 14, "recorder"], [259, 24, 270, 22], [259, 25, 270, 23, "prepareToRecordAsync"], [259, 45, 270, 43], [259, 46, 270, 44], [259, 47, 270, 45], [260, 10, 271, 8, "recorder"], [260, 18, 271, 16], [260, 19, 271, 17, "record"], [260, 25, 271, 23], [260, 26, 271, 24], [260, 27, 271, 25], [261, 10, 272, 8, "setIsDictating"], [261, 24, 272, 22], [261, 25, 272, 23], [261, 29, 272, 27], [261, 30, 272, 28], [262, 10, 273, 8, "Haptics"], [262, 17, 273, 15], [262, 18, 273, 16, "impactAsync"], [262, 29, 273, 27], [262, 30, 273, 28, "Haptics"], [262, 37, 273, 35], [262, 38, 273, 36, "ImpactFeedbackStyle"], [262, 57, 273, 55], [262, 58, 273, 56, "Medium"], [262, 64, 273, 62], [262, 65, 273, 63], [263, 8, 274, 6], [264, 6, 275, 4], [264, 7, 275, 5], [264, 8, 275, 6], [264, 15, 275, 13, "error"], [264, 20, 275, 18], [264, 22, 275, 20], [265, 8, 276, 6, "console"], [265, 15, 276, 13], [265, 16, 276, 14, "error"], [265, 21, 276, 19], [265, 22, 276, 20], [265, 45, 276, 43], [265, 47, 276, 45, "error"], [265, 52, 276, 50], [265, 53, 276, 51], [266, 8, 277, 6, "<PERSON><PERSON>"], [266, 22, 277, 11], [266, 23, 277, 12, "alert"], [266, 28, 277, 17], [266, 29, 277, 18], [266, 36, 277, 25], [266, 38, 277, 27], [266, 65, 277, 54], [266, 66, 277, 55], [267, 8, 278, 6, "setIsDictating"], [267, 22, 278, 20], [267, 23, 278, 21], [267, 28, 278, 26], [267, 29, 278, 27], [268, 6, 279, 4], [269, 4, 280, 2], [269, 5, 280, 3], [270, 4, 282, 2], [270, 10, 282, 8, "handleLongPress"], [270, 25, 282, 23], [270, 28, 282, 27, "message"], [270, 35, 282, 34], [270, 39, 282, 39], [271, 6, 283, 4, "setLongPressedMessage"], [271, 27, 283, 25], [271, 28, 283, 26, "message"], [271, 35, 283, 33], [271, 36, 283, 34], [272, 6, 284, 4, "setIsContextMenuVisible"], [272, 29, 284, 27], [272, 30, 284, 28], [272, 34, 284, 32], [272, 35, 284, 33], [273, 4, 285, 2], [273, 5, 285, 3], [274, 4, 287, 2], [274, 10, 287, 8, "handleCopyMessage"], [274, 27, 287, 25], [274, 30, 287, 28, "handleCopyMessage"], [274, 31, 287, 28], [274, 36, 287, 34], [275, 6, 288, 4], [275, 10, 288, 8, "longPressedMessage"], [275, 28, 288, 26], [275, 30, 288, 28], [276, 8, 289, 6, "Clipboard"], [276, 26, 289, 15], [276, 27, 289, 16, "setString"], [276, 36, 289, 25], [276, 37, 289, 26, "longPressedMessage"], [276, 55, 289, 44], [276, 56, 289, 45, "content"], [276, 63, 289, 52], [276, 64, 289, 53], [277, 8, 290, 6, "setIsContextMenuVisible"], [277, 31, 290, 29], [277, 32, 290, 30], [277, 37, 290, 35], [277, 38, 290, 36], [278, 8, 291, 6, "setLongPressedMessage"], [278, 29, 291, 27], [278, 30, 291, 28], [278, 34, 291, 32], [278, 35, 291, 33], [279, 6, 292, 4], [280, 4, 293, 2], [280, 5, 293, 3], [281, 4, 295, 2], [281, 10, 295, 8, "handleListenToMessage"], [281, 31, 295, 29], [281, 34, 295, 32, "handleListenToMessage"], [281, 35, 295, 32], [281, 40, 295, 38], [282, 6, 296, 4], [282, 10, 296, 8, "longPressedMessage"], [282, 28, 296, 26], [282, 30, 296, 28], [283, 8, 297, 6], [284, 8, 298, 6, "<PERSON><PERSON>"], [284, 22, 298, 11], [284, 23, 298, 12, "alert"], [284, 28, 298, 17], [284, 29, 298, 18], [284, 51, 298, 40], [284, 53, 298, 42, "longPressedMessage"], [284, 71, 298, 60], [284, 72, 298, 61, "content"], [284, 79, 298, 68], [284, 80, 298, 69], [285, 8, 299, 6, "setIsContextMenuVisible"], [285, 31, 299, 29], [285, 32, 299, 30], [285, 37, 299, 35], [285, 38, 299, 36], [286, 8, 300, 6, "setLongPressedMessage"], [286, 29, 300, 27], [286, 30, 300, 28], [286, 34, 300, 32], [286, 35, 300, 33], [287, 6, 301, 4], [288, 4, 302, 2], [288, 5, 302, 3], [289, 4, 304, 2], [289, 10, 304, 8, "handleMute"], [289, 20, 304, 18], [289, 23, 304, 21], [289, 29, 304, 21, "handleMute"], [289, 30, 304, 21], [289, 35, 304, 33], [290, 6, 305, 4], [290, 10, 305, 8], [290, 11, 305, 9, "hasPermission"], [290, 24, 305, 22], [290, 26, 305, 24], [291, 6, 307, 4], [291, 10, 307, 8], [292, 8, 308, 6], [292, 12, 308, 10, "isMuted"], [292, 19, 308, 17], [292, 21, 308, 19], [293, 10, 309, 8], [294, 10, 310, 8], [294, 14, 310, 12], [294, 15, 310, 13, "recorderState"], [294, 28, 310, 26], [294, 29, 310, 27, "isRecording"], [294, 40, 310, 38], [294, 42, 310, 40], [295, 12, 311, 10], [295, 18, 311, 16, "recorder"], [295, 26, 311, 24], [295, 27, 311, 25, "prepareToRecordAsync"], [295, 47, 311, 45], [295, 48, 311, 46], [295, 49, 311, 47], [296, 12, 312, 10, "recorder"], [296, 20, 312, 18], [296, 21, 312, 19, "record"], [296, 27, 312, 25], [296, 28, 312, 26], [296, 29, 312, 27], [297, 10, 313, 8], [298, 10, 314, 8, "setIsMuted"], [298, 20, 314, 18], [298, 21, 314, 19], [298, 26, 314, 24], [298, 27, 314, 25], [299, 10, 315, 8, "Haptics"], [299, 17, 315, 15], [299, 18, 315, 16, "impactAsync"], [299, 29, 315, 27], [299, 30, 315, 28, "Haptics"], [299, 37, 315, 35], [299, 38, 315, 36, "ImpactFeedbackStyle"], [299, 57, 315, 55], [299, 58, 315, 56, "Light"], [299, 63, 315, 61], [299, 64, 315, 62], [300, 8, 316, 6], [300, 9, 316, 7], [300, 15, 316, 13], [301, 10, 317, 8], [302, 10, 318, 8], [302, 14, 318, 12, "recorderState"], [302, 27, 318, 25], [302, 28, 318, 26, "isRecording"], [302, 39, 318, 37], [302, 41, 318, 39], [303, 12, 319, 10], [303, 18, 319, 16, "recorder"], [303, 26, 319, 24], [303, 27, 319, 25, "stop"], [303, 31, 319, 29], [303, 32, 319, 30], [303, 33, 319, 31], [304, 10, 320, 8], [305, 10, 321, 8, "setIsMuted"], [305, 20, 321, 18], [305, 21, 321, 19], [305, 25, 321, 23], [305, 26, 321, 24], [306, 10, 322, 8, "Haptics"], [306, 17, 322, 15], [306, 18, 322, 16, "impactAsync"], [306, 29, 322, 27], [306, 30, 322, 28, "Haptics"], [306, 37, 322, 35], [306, 38, 322, 36, "ImpactFeedbackStyle"], [306, 57, 322, 55], [306, 58, 322, 56, "Light"], [306, 63, 322, 61], [306, 64, 322, 62], [307, 8, 323, 6], [308, 6, 324, 4], [308, 7, 324, 5], [308, 8, 324, 6], [308, 15, 324, 13, "error"], [308, 20, 324, 18], [308, 22, 324, 20], [309, 8, 325, 6, "console"], [309, 15, 325, 13], [309, 16, 325, 14, "error"], [309, 21, 325, 19], [309, 22, 325, 20], [309, 47, 325, 45], [309, 49, 325, 47, "error"], [309, 54, 325, 52], [309, 55, 325, 53], [310, 6, 326, 4], [311, 4, 327, 2], [311, 5, 327, 3], [313, 4, 330, 2], [314, 4, 331, 2], [314, 8, 331, 2, "useEffect"], [314, 24, 331, 11], [314, 26, 331, 12], [314, 32, 331, 18], [315, 6, 332, 4], [315, 10, 332, 8, "scrollViewRef"], [315, 23, 332, 21], [315, 24, 332, 22, "current"], [315, 31, 332, 29], [315, 33, 332, 31], [316, 8, 333, 6, "scrollViewRef"], [316, 21, 333, 19], [316, 22, 333, 20, "current"], [316, 29, 333, 27], [316, 30, 333, 28, "scrollToEnd"], [316, 41, 333, 39], [316, 42, 333, 40], [317, 10, 333, 42, "animated"], [317, 18, 333, 50], [317, 20, 333, 52], [318, 8, 333, 57], [318, 9, 333, 58], [318, 10, 333, 59], [319, 6, 334, 4], [320, 4, 335, 2], [320, 5, 335, 3], [320, 7, 335, 5], [320, 8, 335, 6, "messages"], [320, 16, 335, 14], [320, 17, 335, 15], [320, 18, 335, 16], [321, 4, 337, 2], [321, 8, 337, 6], [321, 9, 337, 7, "fontsLoaded"], [321, 20, 337, 18], [321, 22, 337, 20], [322, 6, 338, 4], [322, 13, 338, 11], [322, 17, 338, 15], [323, 4, 339, 2], [325, 4, 341, 2], [326, 4, 342, 2], [326, 8, 342, 6, "isFirstTime"], [326, 19, 342, 17], [326, 21, 342, 19], [327, 6, 343, 4], [327, 26, 344, 6], [327, 30, 344, 6, "_jsxDevRuntime"], [327, 44, 344, 6], [327, 45, 344, 6, "jsxDEV"], [327, 51, 344, 6], [327, 53, 344, 7, "_View"], [327, 58, 344, 7], [327, 59, 344, 7, "default"], [327, 66, 344, 11], [328, 8, 344, 12, "style"], [328, 13, 344, 17], [328, 15, 344, 19], [329, 10, 344, 21, "flex"], [329, 14, 344, 25], [329, 16, 344, 27], [329, 17, 344, 28], [330, 10, 344, 30, "backgroundColor"], [330, 25, 344, 45], [330, 27, 344, 47, "colors"], [330, 33, 344, 53], [330, 34, 344, 54, "background"], [331, 8, 344, 65], [331, 9, 344, 67], [332, 8, 344, 67, "children"], [332, 16, 344, 67], [332, 31, 345, 8], [332, 35, 345, 8, "_jsxDevRuntime"], [332, 49, 345, 8], [332, 50, 345, 8, "jsxDEV"], [332, 56, 345, 8], [332, 58, 345, 9, "_View"], [332, 63, 345, 9], [332, 64, 345, 9, "default"], [332, 71, 345, 13], [333, 10, 346, 10, "style"], [333, 15, 346, 15], [333, 17, 346, 17], [334, 12, 347, 12, "flex"], [334, 16, 347, 16], [334, 18, 347, 18], [334, 19, 347, 19], [335, 12, 348, 12, "paddingTop"], [335, 22, 348, 22], [335, 24, 348, 24, "insets"], [335, 30, 348, 30], [335, 31, 348, 31, "top"], [335, 34, 348, 34], [335, 37, 348, 37], [335, 39, 348, 39], [336, 12, 349, 12, "paddingHorizontal"], [336, 29, 349, 29], [336, 31, 349, 31], [336, 33, 349, 33], [337, 12, 350, 12, "paddingBottom"], [337, 25, 350, 25], [337, 27, 350, 27, "insets"], [337, 33, 350, 33], [337, 34, 350, 34, "bottom"], [337, 40, 350, 40], [337, 43, 350, 43], [337, 45, 350, 45], [338, 12, 351, 12, "alignItems"], [338, 22, 351, 22], [338, 24, 351, 24], [338, 32, 351, 32], [339, 12, 352, 12, "justifyContent"], [339, 26, 352, 26], [339, 28, 352, 28], [340, 10, 353, 10], [340, 11, 353, 12], [341, 10, 353, 12, "children"], [341, 18, 353, 12], [341, 34, 355, 10], [341, 38, 355, 10, "_jsxDevRuntime"], [341, 52, 355, 10], [341, 53, 355, 10, "jsxDEV"], [341, 59, 355, 10], [341, 61, 355, 11, "_View"], [341, 66, 355, 11], [341, 67, 355, 11, "default"], [341, 74, 355, 15], [342, 12, 356, 12, "style"], [342, 17, 356, 17], [342, 19, 356, 19], [343, 14, 357, 14, "width"], [343, 19, 357, 19], [343, 21, 357, 21], [343, 24, 357, 24], [344, 14, 358, 14, "height"], [344, 20, 358, 20], [344, 22, 358, 22], [344, 25, 358, 25], [345, 14, 359, 14, "borderRadius"], [345, 26, 359, 26], [345, 28, 359, 28], [345, 30, 359, 30], [346, 14, 360, 14, "backgroundColor"], [346, 29, 360, 29], [346, 31, 360, 31, "colors"], [346, 37, 360, 37], [346, 38, 360, 38, "primaryUltraLight"], [346, 55, 360, 55], [347, 14, 361, 14, "alignItems"], [347, 24, 361, 24], [347, 26, 361, 26], [347, 34, 361, 34], [348, 14, 362, 14, "justifyContent"], [348, 28, 362, 28], [348, 30, 362, 30], [348, 38, 362, 38], [349, 14, 363, 14, "marginBottom"], [349, 26, 363, 26], [349, 28, 363, 28], [350, 12, 364, 12], [350, 13, 364, 14], [351, 12, 364, 14, "children"], [351, 20, 364, 14], [351, 35, 365, 12], [351, 39, 365, 12, "_jsxDevRuntime"], [351, 53, 365, 12], [351, 54, 365, 12, "jsxDEV"], [351, 60, 365, 12], [351, 62, 365, 13, "_lucideReactNative"], [351, 80, 365, 13], [351, 81, 365, 13, "MessageSquare"], [351, 94, 365, 26], [352, 14, 365, 27, "size"], [352, 18, 365, 31], [352, 20, 365, 33], [352, 22, 365, 36], [353, 14, 365, 37, "color"], [353, 19, 365, 42], [353, 21, 365, 44, "colors"], [353, 27, 365, 50], [353, 28, 365, 51, "primary"], [354, 12, 365, 59], [355, 14, 365, 59, "fileName"], [355, 22, 365, 59], [355, 24, 365, 59, "_jsxFileName"], [355, 36, 365, 59], [356, 14, 365, 59, "lineNumber"], [356, 24, 365, 59], [357, 14, 365, 59, "columnNumber"], [357, 26, 365, 59], [358, 12, 365, 59], [358, 19, 365, 61], [359, 10, 365, 62], [360, 12, 365, 62, "fileName"], [360, 20, 365, 62], [360, 22, 365, 62, "_jsxFileName"], [360, 34, 365, 62], [361, 12, 365, 62, "lineNumber"], [361, 22, 365, 62], [362, 12, 365, 62, "columnNumber"], [362, 24, 365, 62], [363, 10, 365, 62], [363, 17, 366, 16], [363, 18, 366, 17], [363, 33, 369, 10], [363, 37, 369, 10, "_jsxDevRuntime"], [363, 51, 369, 10], [363, 52, 369, 10, "jsxDEV"], [363, 58, 369, 10], [363, 60, 369, 11, "_Text"], [363, 65, 369, 11], [363, 66, 369, 11, "default"], [363, 73, 369, 15], [364, 12, 370, 12, "style"], [364, 17, 370, 17], [364, 19, 370, 19], [365, 14, 371, 14, "fontSize"], [365, 22, 371, 22], [365, 24, 371, 24], [365, 26, 371, 26], [366, 14, 372, 14, "fontFamily"], [366, 24, 372, 24], [366, 26, 372, 26], [366, 47, 372, 47], [367, 14, 373, 14, "color"], [367, 19, 373, 19], [367, 21, 373, 21, "colors"], [367, 27, 373, 27], [367, 28, 373, 28, "text"], [367, 32, 373, 32], [368, 14, 374, 14, "textAlign"], [368, 23, 374, 23], [368, 25, 374, 25], [368, 33, 374, 33], [369, 14, 375, 14, "marginBottom"], [369, 26, 375, 26], [369, 28, 375, 28], [370, 12, 376, 12], [370, 13, 376, 14], [371, 12, 376, 14, "children"], [371, 20, 376, 14], [371, 22, 376, 15], [372, 10, 378, 10], [373, 12, 378, 10, "fileName"], [373, 20, 378, 10], [373, 22, 378, 10, "_jsxFileName"], [373, 34, 378, 10], [374, 12, 378, 10, "lineNumber"], [374, 22, 378, 10], [375, 12, 378, 10, "columnNumber"], [375, 24, 378, 10], [376, 10, 378, 10], [376, 17, 378, 16], [376, 18, 378, 17], [376, 33, 381, 10], [376, 37, 381, 10, "_jsxDevRuntime"], [376, 51, 381, 10], [376, 52, 381, 10, "jsxDEV"], [376, 58, 381, 10], [376, 60, 381, 11, "_Text"], [376, 65, 381, 11], [376, 66, 381, 11, "default"], [376, 73, 381, 15], [377, 12, 382, 12, "style"], [377, 17, 382, 17], [377, 19, 382, 19], [378, 14, 383, 14, "fontSize"], [378, 22, 383, 22], [378, 24, 383, 24], [378, 26, 383, 26], [379, 14, 384, 14, "fontFamily"], [379, 24, 384, 24], [379, 26, 384, 26], [379, 46, 384, 46], [380, 14, 385, 14, "color"], [380, 19, 385, 19], [380, 21, 385, 21, "colors"], [380, 27, 385, 27], [380, 28, 385, 28, "textSecondary"], [380, 41, 385, 41], [381, 14, 386, 14, "textAlign"], [381, 23, 386, 23], [381, 25, 386, 25], [381, 33, 386, 33], [382, 14, 387, 14, "lineHeight"], [382, 24, 387, 24], [382, 26, 387, 26], [382, 28, 387, 28], [383, 14, 388, 14, "marginBottom"], [383, 26, 388, 26], [383, 28, 388, 28], [384, 12, 389, 12], [384, 13, 389, 14], [385, 12, 389, 14, "children"], [385, 20, 389, 14], [385, 22, 389, 15], [386, 10, 392, 10], [387, 12, 392, 10, "fileName"], [387, 20, 392, 10], [387, 22, 392, 10, "_jsxFileName"], [387, 34, 392, 10], [388, 12, 392, 10, "lineNumber"], [388, 22, 392, 10], [389, 12, 392, 10, "columnNumber"], [389, 24, 392, 10], [390, 10, 392, 10], [390, 17, 392, 16], [390, 18, 392, 17], [390, 33, 395, 10], [390, 37, 395, 10, "_jsxDevRuntime"], [390, 51, 395, 10], [390, 52, 395, 10, "jsxDEV"], [390, 58, 395, 10], [390, 60, 395, 11, "_TouchableOpacity"], [390, 77, 395, 11], [390, 78, 395, 11, "default"], [390, 85, 395, 27], [391, 12, 396, 12, "style"], [391, 17, 396, 17], [391, 19, 396, 19], [392, 14, 397, 14, "backgroundColor"], [392, 29, 397, 29], [392, 31, 397, 31, "colors"], [392, 37, 397, 37], [392, 38, 397, 38, "primary"], [392, 45, 397, 45], [393, 14, 398, 14, "borderRadius"], [393, 26, 398, 26], [393, 28, 398, 28], [393, 30, 398, 30], [394, 14, 399, 14, "paddingHorizontal"], [394, 31, 399, 31], [394, 33, 399, 33], [394, 35, 399, 35], [395, 14, 400, 14, "paddingVertical"], [395, 29, 400, 29], [395, 31, 400, 31], [395, 33, 400, 33], [396, 14, 401, 14, "min<PERSON><PERSON><PERSON>"], [396, 22, 401, 22], [396, 24, 401, 24], [396, 27, 401, 27], [397, 14, 402, 14, "alignItems"], [397, 24, 402, 24], [397, 26, 402, 26], [398, 12, 403, 12], [398, 13, 403, 14], [399, 12, 404, 12, "onPress"], [399, 19, 404, 19], [399, 21, 404, 21, "handleStartBrainstorming"], [399, 45, 404, 46], [400, 12, 404, 46, "children"], [400, 20, 404, 46], [400, 35, 405, 12], [400, 39, 405, 12, "_jsxDevRuntime"], [400, 53, 405, 12], [400, 54, 405, 12, "jsxDEV"], [400, 60, 405, 12], [400, 62, 405, 13, "_Text"], [400, 67, 405, 13], [400, 68, 405, 13, "default"], [400, 75, 405, 17], [401, 14, 406, 14, "style"], [401, 19, 406, 19], [401, 21, 406, 21], [402, 16, 407, 16, "fontSize"], [402, 24, 407, 24], [402, 26, 407, 26], [402, 28, 407, 28], [403, 16, 408, 16, "fontFamily"], [403, 26, 408, 26], [403, 28, 408, 28], [403, 49, 408, 49], [404, 16, 409, 16, "color"], [404, 21, 409, 21], [404, 23, 409, 23, "colors"], [404, 29, 409, 29], [404, 30, 409, 30, "background"], [405, 14, 410, 14], [405, 15, 410, 16], [406, 14, 410, 16, "children"], [406, 22, 410, 16], [406, 24, 410, 17], [407, 12, 412, 12], [408, 14, 412, 12, "fileName"], [408, 22, 412, 12], [408, 24, 412, 12, "_jsxFileName"], [408, 36, 412, 12], [409, 14, 412, 12, "lineNumber"], [409, 24, 412, 12], [410, 14, 412, 12, "columnNumber"], [410, 26, 412, 12], [411, 12, 412, 12], [411, 19, 412, 18], [412, 10, 412, 19], [413, 12, 412, 19, "fileName"], [413, 20, 412, 19], [413, 22, 412, 19, "_jsxFileName"], [413, 34, 412, 19], [414, 12, 412, 19, "lineNumber"], [414, 22, 412, 19], [415, 12, 412, 19, "columnNumber"], [415, 24, 412, 19], [416, 10, 412, 19], [416, 17, 413, 28], [416, 18, 413, 29], [417, 8, 413, 29], [418, 10, 413, 29, "fileName"], [418, 18, 413, 29], [418, 20, 413, 29, "_jsxFileName"], [418, 32, 413, 29], [419, 10, 413, 29, "lineNumber"], [419, 20, 413, 29], [420, 10, 413, 29, "columnNumber"], [420, 22, 413, 29], [421, 8, 413, 29], [421, 15, 414, 14], [422, 6, 414, 15], [423, 8, 414, 15, "fileName"], [423, 16, 414, 15], [423, 18, 414, 15, "_jsxFileName"], [423, 30, 414, 15], [424, 8, 414, 15, "lineNumber"], [424, 18, 414, 15], [425, 8, 414, 15, "columnNumber"], [425, 20, 414, 15], [426, 6, 414, 15], [426, 13, 415, 12], [426, 14, 415, 13], [427, 4, 417, 2], [429, 4, 419, 2], [430, 4, 420, 2], [430, 24, 421, 4], [430, 28, 421, 4, "_jsxDevRuntime"], [430, 42, 421, 4], [430, 43, 421, 4, "jsxDEV"], [430, 49, 421, 4], [430, 51, 421, 5, "_View"], [430, 56, 421, 5], [430, 57, 421, 5, "default"], [430, 64, 421, 9], [431, 6, 421, 10, "style"], [431, 11, 421, 15], [431, 13, 421, 17], [432, 8, 421, 19, "flex"], [432, 12, 421, 23], [432, 14, 421, 25], [432, 15, 421, 26], [433, 8, 421, 28, "backgroundColor"], [433, 23, 421, 43], [433, 25, 421, 45, "colors"], [433, 31, 421, 51], [433, 32, 421, 52, "background"], [434, 6, 421, 63], [434, 7, 421, 65], [435, 6, 421, 65, "children"], [435, 14, 421, 65], [435, 30, 422, 6], [435, 34, 422, 6, "_jsxDevRuntime"], [435, 48, 422, 6], [435, 49, 422, 6, "jsxDEV"], [435, 55, 422, 6], [435, 57, 422, 7, "_Header"], [435, 64, 422, 7], [435, 65, 422, 7, "default"], [435, 72, 422, 13], [436, 8, 423, 8, "voiceMode"], [436, 17, 423, 17], [436, 19, 423, 19, "voiceMode"], [436, 28, 423, 29], [437, 8, 424, 8, "onToggleVoiceMode"], [437, 25, 424, 25], [437, 27, 424, 27, "toggleVoiceMode"], [437, 42, 424, 43], [438, 8, 425, 8, "onDone"], [438, 14, 425, 14], [438, 16, 425, 16, "onDone"], [438, 17, 425, 16], [438, 22, 425, 22, "<PERSON><PERSON>"], [438, 36, 425, 27], [438, 37, 425, 28, "alert"], [438, 42, 425, 33], [438, 43, 425, 34], [438, 57, 425, 48], [439, 6, 425, 50], [440, 8, 425, 50, "fileName"], [440, 16, 425, 50], [440, 18, 425, 50, "_jsxFileName"], [440, 30, 425, 50], [441, 8, 425, 50, "lineNumber"], [441, 18, 425, 50], [442, 8, 425, 50, "columnNumber"], [442, 20, 425, 50], [443, 6, 425, 50], [443, 13, 426, 7], [443, 14, 426, 8], [443, 16, 427, 7, "voiceMode"], [443, 25, 427, 16], [443, 41, 428, 8], [443, 45, 428, 8, "_jsxDevRuntime"], [443, 59, 428, 8], [443, 60, 428, 8, "jsxDEV"], [443, 66, 428, 8], [443, 68, 428, 9, "_VoiceMode"], [443, 78, 428, 9], [443, 79, 428, 9, "default"], [443, 86, 428, 18], [444, 8, 429, 10, "isRecording"], [444, 19, 429, 21], [444, 21, 429, 23, "recorderState"], [444, 34, 429, 36], [444, 35, 429, 37, "isRecording"], [444, 46, 429, 49], [445, 8, 430, 10, "hasPermission"], [445, 21, 430, 23], [445, 23, 430, 25, "hasPermission"], [445, 36, 430, 39], [446, 8, 431, 10, "isLoading"], [446, 17, 431, 19], [446, 19, 431, 21, "isLoading"], [446, 28, 431, 31], [447, 8, 432, 10, "transcript"], [447, 18, 432, 20], [447, 20, 432, 22, "transcript"], [447, 30, 432, 33], [448, 8, 433, 10, "isMuted"], [448, 15, 433, 17], [448, 17, 433, 19, "isMuted"], [448, 24, 433, 27], [449, 8, 434, 10, "onMute"], [449, 14, 434, 16], [449, 16, 434, 18, "handleMute"], [450, 6, 434, 29], [451, 8, 434, 29, "fileName"], [451, 16, 434, 29], [451, 18, 434, 29, "_jsxFileName"], [451, 30, 434, 29], [452, 8, 434, 29, "lineNumber"], [452, 18, 434, 29], [453, 8, 434, 29, "columnNumber"], [453, 20, 434, 29], [454, 6, 434, 29], [454, 13, 435, 9], [454, 14, 435, 10], [454, 30, 437, 8], [454, 34, 437, 8, "_jsxDevRuntime"], [454, 48, 437, 8], [454, 49, 437, 8, "jsxDEV"], [454, 55, 437, 8], [454, 57, 437, 9, "_KeyboardAvoidingAnimatedView"], [454, 86, 437, 9], [454, 87, 437, 9, "default"], [454, 94, 437, 37], [455, 8, 437, 38, "style"], [455, 13, 437, 43], [455, 15, 437, 45], [456, 10, 437, 47, "flex"], [456, 14, 437, 51], [456, 16, 437, 53], [457, 8, 437, 55], [457, 9, 437, 57], [458, 8, 437, 57, "children"], [458, 16, 437, 57], [458, 32, 439, 10], [458, 36, 439, 10, "_jsxDevRuntime"], [458, 50, 439, 10], [458, 51, 439, 10, "jsxDEV"], [458, 57, 439, 10], [458, 59, 439, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [458, 70, 439, 11], [458, 71, 439, 11, "default"], [458, 78, 439, 21], [459, 10, 440, 12, "ref"], [459, 13, 440, 15], [459, 15, 440, 17, "scrollViewRef"], [459, 28, 440, 31], [460, 10, 441, 12, "style"], [460, 15, 441, 17], [460, 17, 441, 19], [461, 12, 441, 21, "flex"], [461, 16, 441, 25], [461, 18, 441, 27], [462, 10, 441, 29], [462, 11, 441, 31], [463, 10, 442, 12, "contentContainerStyle"], [463, 31, 442, 33], [463, 33, 442, 35], [464, 12, 443, 14, "paddingHorizontal"], [464, 29, 443, 31], [464, 31, 443, 33], [464, 33, 443, 35], [465, 12, 444, 14, "paddingVertical"], [465, 27, 444, 29], [465, 29, 444, 31], [465, 31, 444, 33], [466, 12, 445, 14, "paddingBottom"], [466, 25, 445, 27], [466, 27, 445, 29], [466, 30, 445, 32], [466, 31, 445, 34], [467, 10, 446, 12], [467, 11, 446, 14], [468, 10, 447, 12, "showsVerticalScrollIndicator"], [468, 38, 447, 40], [468, 40, 447, 42], [468, 45, 447, 48], [469, 10, 447, 48, "children"], [469, 18, 447, 48], [469, 21, 448, 13, "messages"], [469, 29, 448, 21], [469, 30, 448, 22, "map"], [469, 33, 448, 25], [469, 34, 448, 26], [469, 35, 448, 27, "message"], [469, 42, 448, 34], [469, 44, 448, 36, "index"], [469, 49, 448, 41], [469, 67, 449, 14], [469, 71, 449, 14, "_jsxDevRuntime"], [469, 85, 449, 14], [469, 86, 449, 14, "jsxDEV"], [469, 92, 449, 14], [469, 94, 449, 15, "_MessageBubble"], [469, 108, 449, 15], [469, 109, 449, 15, "default"], [469, 116, 449, 28], [470, 12, 449, 41, "message"], [470, 19, 449, 48], [470, 21, 449, 50, "message"], [470, 28, 449, 58], [471, 12, 449, 59, "onLongPress"], [471, 23, 449, 70], [471, 25, 449, 72, "handleLongPress"], [472, 10, 449, 88], [472, 13, 449, 34, "index"], [472, 18, 449, 39], [473, 12, 449, 39, "fileName"], [473, 20, 449, 39], [473, 22, 449, 39, "_jsxFileName"], [473, 34, 449, 39], [474, 12, 449, 39, "lineNumber"], [474, 22, 449, 39], [475, 12, 449, 39, "columnNumber"], [475, 24, 449, 39], [476, 10, 449, 39], [476, 17, 449, 90], [476, 18, 450, 13], [476, 19, 450, 14], [476, 21, 453, 13, "isLoading"], [476, 30, 453, 22], [476, 47, 454, 14], [476, 51, 454, 14, "_jsxDevRuntime"], [476, 65, 454, 14], [476, 66, 454, 14, "jsxDEV"], [476, 72, 454, 14], [476, 74, 454, 15, "_View"], [476, 79, 454, 15], [476, 80, 454, 15, "default"], [476, 87, 454, 19], [477, 12, 455, 16, "style"], [477, 17, 455, 21], [477, 19, 455, 23], [478, 14, 456, 18, "marginBottom"], [478, 26, 456, 30], [478, 28, 456, 32], [478, 30, 456, 34], [479, 14, 457, 18, "alignSelf"], [479, 23, 457, 27], [479, 25, 457, 29], [479, 37, 457, 41], [480, 14, 458, 18, "max<PERSON><PERSON><PERSON>"], [480, 22, 458, 26], [480, 24, 458, 28], [481, 12, 459, 16], [481, 13, 459, 18], [482, 12, 459, 18, "children"], [482, 20, 459, 18], [482, 35, 460, 16], [482, 39, 460, 16, "_jsxDevRuntime"], [482, 53, 460, 16], [482, 54, 460, 16, "jsxDEV"], [482, 60, 460, 16], [482, 62, 460, 17, "_View"], [482, 67, 460, 17], [482, 68, 460, 17, "default"], [482, 75, 460, 21], [483, 14, 461, 18, "style"], [483, 19, 461, 23], [483, 21, 461, 25], [484, 16, 462, 20, "backgroundColor"], [484, 31, 462, 35], [484, 33, 462, 37, "colors"], [484, 39, 462, 43], [484, 40, 462, 44, "cardBackground"], [484, 54, 462, 58], [485, 16, 463, 20, "borderRadius"], [485, 28, 463, 32], [485, 30, 463, 34], [485, 32, 463, 36], [486, 16, 464, 20, "paddingHorizontal"], [486, 33, 464, 37], [486, 35, 464, 39], [486, 37, 464, 41], [487, 16, 465, 20, "paddingVertical"], [487, 31, 465, 35], [487, 33, 465, 37], [487, 35, 465, 39], [488, 16, 466, 20, "borderWidth"], [488, 27, 466, 31], [488, 29, 466, 33], [488, 30, 466, 34], [489, 16, 467, 20, "borderColor"], [489, 27, 467, 31], [489, 29, 467, 33, "colors"], [489, 35, 467, 39], [489, 36, 467, 40, "outline"], [490, 14, 468, 18], [490, 15, 468, 20], [491, 14, 468, 20, "children"], [491, 22, 468, 20], [491, 37, 469, 18], [491, 41, 469, 18, "_jsxDevRuntime"], [491, 55, 469, 18], [491, 56, 469, 18, "jsxDEV"], [491, 62, 469, 18], [491, 64, 469, 19, "_Text"], [491, 69, 469, 19], [491, 70, 469, 19, "default"], [491, 77, 469, 23], [492, 16, 470, 20, "style"], [492, 21, 470, 25], [492, 23, 470, 27], [493, 18, 471, 22, "fontSize"], [493, 26, 471, 30], [493, 28, 471, 32], [493, 30, 471, 34], [494, 18, 472, 22, "fontFamily"], [494, 28, 472, 32], [494, 30, 472, 34], [494, 50, 472, 54], [495, 18, 473, 22, "color"], [495, 23, 473, 27], [495, 25, 473, 29, "colors"], [495, 31, 473, 35], [495, 32, 473, 36, "textSecondary"], [495, 45, 473, 49], [496, 18, 474, 22, "lineHeight"], [496, 28, 474, 32], [496, 30, 474, 34], [497, 16, 475, 20], [497, 17, 475, 22], [498, 16, 475, 22, "children"], [498, 24, 475, 22], [498, 26, 475, 23], [499, 14, 477, 18], [500, 16, 477, 18, "fileName"], [500, 24, 477, 18], [500, 26, 477, 18, "_jsxFileName"], [500, 38, 477, 18], [501, 16, 477, 18, "lineNumber"], [501, 26, 477, 18], [502, 16, 477, 18, "columnNumber"], [502, 28, 477, 18], [503, 14, 477, 18], [503, 21, 477, 24], [504, 12, 477, 25], [505, 14, 477, 25, "fileName"], [505, 22, 477, 25], [505, 24, 477, 25, "_jsxFileName"], [505, 36, 477, 25], [506, 14, 477, 25, "lineNumber"], [506, 24, 477, 25], [507, 14, 477, 25, "columnNumber"], [507, 26, 477, 25], [508, 12, 477, 25], [508, 19, 478, 22], [509, 10, 478, 23], [510, 12, 478, 23, "fileName"], [510, 20, 478, 23], [510, 22, 478, 23, "_jsxFileName"], [510, 34, 478, 23], [511, 12, 478, 23, "lineNumber"], [511, 22, 478, 23], [512, 12, 478, 23, "columnNumber"], [512, 24, 478, 23], [513, 10, 478, 23], [513, 17, 479, 20], [513, 18, 480, 13], [514, 8, 480, 13], [515, 10, 480, 13, "fileName"], [515, 18, 480, 13], [515, 20, 480, 13, "_jsxFileName"], [515, 32, 480, 13], [516, 10, 480, 13, "lineNumber"], [516, 20, 480, 13], [517, 10, 480, 13, "columnNumber"], [517, 22, 480, 13], [518, 8, 480, 13], [518, 15, 481, 22], [518, 16, 481, 23], [518, 18, 484, 11, "quickActions"], [518, 30, 484, 23], [518, 31, 484, 24, "length"], [518, 37, 484, 30], [518, 40, 484, 33], [518, 41, 484, 34], [518, 58, 485, 12], [518, 62, 485, 12, "_jsxDevRuntime"], [518, 76, 485, 12], [518, 77, 485, 12, "jsxDEV"], [518, 83, 485, 12], [518, 85, 485, 13, "_View"], [518, 90, 485, 13], [518, 91, 485, 13, "default"], [518, 98, 485, 17], [519, 10, 486, 14, "style"], [519, 15, 486, 19], [519, 17, 486, 21], [520, 12, 487, 16, "position"], [520, 20, 487, 24], [520, 22, 487, 26], [520, 32, 487, 36], [521, 12, 488, 16, "bottom"], [521, 18, 488, 22], [521, 20, 488, 24, "insets"], [521, 26, 488, 30], [521, 27, 488, 31, "bottom"], [521, 33, 488, 37], [521, 36, 488, 40], [521, 39, 488, 43], [522, 12, 489, 16, "left"], [522, 16, 489, 20], [522, 18, 489, 22], [522, 20, 489, 24], [523, 12, 490, 16, "right"], [523, 17, 490, 21], [523, 19, 490, 23], [524, 10, 491, 14], [524, 11, 491, 16], [525, 10, 491, 16, "children"], [525, 18, 491, 16], [525, 33, 492, 14], [525, 37, 492, 14, "_jsxDevRuntime"], [525, 51, 492, 14], [525, 52, 492, 14, "jsxDEV"], [525, 58, 492, 14], [525, 60, 492, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [525, 71, 492, 15], [525, 72, 492, 15, "default"], [525, 79, 492, 25], [526, 12, 493, 16, "horizontal"], [526, 22, 493, 26], [527, 12, 494, 16, "showsHorizontalScrollIndicator"], [527, 42, 494, 46], [527, 44, 494, 48], [527, 49, 494, 54], [528, 12, 495, 16, "contentContainerStyle"], [528, 33, 495, 37], [528, 35, 495, 39], [529, 14, 495, 41, "gap"], [529, 17, 495, 44], [529, 19, 495, 46], [530, 12, 495, 48], [530, 13, 495, 50], [531, 12, 495, 50, "children"], [531, 20, 495, 50], [531, 22, 496, 17, "quickActions"], [531, 34, 496, 29], [531, 35, 496, 30, "map"], [531, 38, 496, 33], [531, 39, 496, 34], [531, 40, 496, 35, "action"], [531, 46, 496, 41], [531, 48, 496, 43, "index"], [531, 53, 496, 48], [531, 71, 497, 18], [531, 75, 497, 18, "_jsxDevRuntime"], [531, 89, 497, 18], [531, 90, 497, 18, "jsxDEV"], [531, 96, 497, 18], [531, 98, 497, 19, "_TouchableOpacity"], [531, 115, 497, 19], [531, 116, 497, 19, "default"], [531, 123, 497, 35], [532, 14, 499, 20, "style"], [532, 19, 499, 25], [532, 21, 499, 27], [533, 16, 500, 22, "backgroundColor"], [533, 31, 500, 37], [533, 33, 500, 39, "colors"], [533, 39, 500, 45], [533, 40, 500, 46, "primaryUltraLight"], [533, 57, 500, 63], [534, 16, 501, 22, "borderRadius"], [534, 28, 501, 34], [534, 30, 501, 36], [534, 32, 501, 38], [535, 16, 502, 22, "paddingHorizontal"], [535, 33, 502, 39], [535, 35, 502, 41], [535, 37, 502, 43], [536, 16, 503, 22, "paddingVertical"], [536, 31, 503, 37], [536, 33, 503, 39], [536, 34, 503, 40], [537, 16, 504, 22, "borderWidth"], [537, 27, 504, 33], [537, 29, 504, 35], [537, 30, 504, 36], [538, 16, 505, 22, "borderColor"], [538, 27, 505, 33], [538, 29, 505, 35, "colors"], [538, 35, 505, 41], [538, 36, 505, 42, "primary"], [539, 14, 506, 20], [539, 15, 506, 22], [540, 14, 507, 20, "onPress"], [540, 21, 507, 27], [540, 23, 507, 29, "onPress"], [540, 24, 507, 29], [540, 29, 507, 35, "handleQuickAction"], [540, 46, 507, 52], [540, 47, 507, 53, "action"], [540, 53, 507, 59], [540, 54, 507, 61], [541, 14, 507, 61, "children"], [541, 22, 507, 61], [541, 37, 508, 20], [541, 41, 508, 20, "_jsxDevRuntime"], [541, 55, 508, 20], [541, 56, 508, 20, "jsxDEV"], [541, 62, 508, 20], [541, 64, 508, 21, "_Text"], [541, 69, 508, 21], [541, 70, 508, 21, "default"], [541, 77, 508, 25], [542, 16, 509, 22, "style"], [542, 21, 509, 27], [542, 23, 509, 29], [543, 18, 510, 24, "fontSize"], [543, 26, 510, 32], [543, 28, 510, 34], [543, 30, 510, 36], [544, 18, 511, 24, "fontFamily"], [544, 28, 511, 34], [544, 30, 511, 36], [544, 49, 511, 55], [545, 18, 512, 24, "color"], [545, 23, 512, 29], [545, 25, 512, 31, "colors"], [545, 31, 512, 37], [545, 32, 512, 38, "primary"], [546, 16, 513, 22], [546, 17, 513, 24], [547, 16, 513, 24, "children"], [547, 24, 513, 24], [547, 26, 514, 23, "action"], [548, 14, 514, 29], [549, 16, 514, 29, "fileName"], [549, 24, 514, 29], [549, 26, 514, 29, "_jsxFileName"], [549, 38, 514, 29], [550, 16, 514, 29, "lineNumber"], [550, 26, 514, 29], [551, 16, 514, 29, "columnNumber"], [551, 28, 514, 29], [552, 14, 514, 29], [552, 21, 515, 26], [553, 12, 515, 27], [553, 15, 498, 25, "index"], [553, 20, 498, 30], [554, 14, 498, 30, "fileName"], [554, 22, 498, 30], [554, 24, 498, 30, "_jsxFileName"], [554, 36, 498, 30], [555, 14, 498, 30, "lineNumber"], [555, 24, 498, 30], [556, 14, 498, 30, "columnNumber"], [556, 26, 498, 30], [557, 12, 498, 30], [557, 19, 516, 36], [557, 20, 517, 17], [558, 10, 517, 18], [559, 12, 517, 18, "fileName"], [559, 20, 517, 18], [559, 22, 517, 18, "_jsxFileName"], [559, 34, 517, 18], [560, 12, 517, 18, "lineNumber"], [560, 22, 517, 18], [561, 12, 517, 18, "columnNumber"], [561, 24, 517, 18], [562, 10, 517, 18], [562, 17, 518, 26], [563, 8, 518, 27], [564, 10, 518, 27, "fileName"], [564, 18, 518, 27], [564, 20, 518, 27, "_jsxFileName"], [564, 32, 518, 27], [565, 10, 518, 27, "lineNumber"], [565, 20, 518, 27], [566, 10, 518, 27, "columnNumber"], [566, 22, 518, 27], [567, 8, 518, 27], [567, 15, 519, 18], [567, 16, 520, 11], [567, 31, 523, 10], [567, 35, 523, 10, "_jsxDevRuntime"], [567, 49, 523, 10], [567, 50, 523, 10, "jsxDEV"], [567, 56, 523, 10], [567, 58, 523, 11, "_View"], [567, 63, 523, 11], [567, 64, 523, 11, "default"], [567, 71, 523, 15], [568, 10, 524, 12, "style"], [568, 15, 524, 17], [568, 17, 524, 19], [569, 12, 525, 14, "position"], [569, 20, 525, 22], [569, 22, 525, 24], [569, 32, 525, 34], [570, 12, 526, 14, "bottom"], [570, 18, 526, 20], [570, 20, 526, 22], [570, 21, 526, 23], [571, 12, 527, 14, "left"], [571, 16, 527, 18], [571, 18, 527, 20], [571, 19, 527, 21], [572, 12, 528, 14, "right"], [572, 17, 528, 19], [572, 19, 528, 21], [573, 10, 529, 12], [573, 11, 529, 14], [574, 10, 529, 14, "children"], [574, 18, 529, 14], [574, 33, 530, 12], [574, 37, 530, 12, "_jsxDevRuntime"], [574, 51, 530, 12], [574, 52, 530, 12, "jsxDEV"], [574, 58, 530, 12], [574, 60, 530, 13, "_TextMode"], [574, 69, 530, 13], [574, 70, 530, 13, "default"], [574, 77, 530, 21], [575, 12, 531, 14, "inputText"], [575, 21, 531, 23], [575, 23, 531, 25, "inputText"], [575, 32, 531, 35], [576, 12, 532, 14, "onInputChange"], [576, 25, 532, 27], [576, 27, 532, 29, "setInputText"], [576, 39, 532, 42], [577, 12, 533, 14, "onSendMessage"], [577, 25, 533, 27], [577, 27, 533, 29, "onSendMessage"], [577, 28, 533, 29], [577, 33, 533, 35, "handleSendMessage"], [577, 50, 533, 52], [577, 51, 533, 53], [577, 52, 533, 55], [578, 12, 534, 14, "onStartDictation"], [578, 28, 534, 30], [578, 30, 534, 32, "handleDictation"], [579, 10, 534, 48], [580, 12, 534, 48, "fileName"], [580, 20, 534, 48], [580, 22, 534, 48, "_jsxFileName"], [580, 34, 534, 48], [581, 12, 534, 48, "lineNumber"], [581, 22, 534, 48], [582, 12, 534, 48, "columnNumber"], [582, 24, 534, 48], [583, 10, 534, 48], [583, 17, 535, 13], [584, 8, 535, 14], [585, 10, 535, 14, "fileName"], [585, 18, 535, 14], [585, 20, 535, 14, "_jsxFileName"], [585, 32, 535, 14], [586, 10, 535, 14, "lineNumber"], [586, 20, 535, 14], [587, 10, 535, 14, "columnNumber"], [587, 22, 535, 14], [588, 8, 535, 14], [588, 15, 536, 16], [588, 16, 536, 17], [589, 6, 536, 17], [590, 8, 536, 17, "fileName"], [590, 16, 536, 17], [590, 18, 536, 17, "_jsxFileName"], [590, 30, 536, 17], [591, 8, 536, 17, "lineNumber"], [591, 18, 536, 17], [592, 8, 536, 17, "columnNumber"], [592, 20, 536, 17], [593, 6, 536, 17], [593, 13, 537, 38], [593, 14, 538, 7], [593, 29, 541, 6], [593, 33, 541, 6, "_jsxDevRuntime"], [593, 47, 541, 6], [593, 48, 541, 6, "jsxDEV"], [593, 54, 541, 6], [593, 56, 541, 7, "_Modal"], [593, 62, 541, 7], [593, 63, 541, 7, "default"], [593, 70, 541, 12], [594, 8, 542, 8, "transparent"], [594, 19, 542, 19], [595, 8, 543, 8, "visible"], [595, 15, 543, 15], [595, 17, 543, 17, "isContextMenuVisible"], [595, 37, 543, 38], [596, 8, 544, 8, "onRequestClose"], [596, 22, 544, 22], [596, 24, 544, 24, "onRequestClose"], [596, 25, 544, 24], [596, 30, 544, 30, "setIsContextMenuVisible"], [596, 53, 544, 53], [596, 54, 544, 54], [596, 59, 544, 59], [596, 60, 544, 61], [597, 8, 544, 61, "children"], [597, 16, 544, 61], [597, 31, 546, 8], [597, 35, 546, 8, "_jsxDevRuntime"], [597, 49, 546, 8], [597, 50, 546, 8, "jsxDEV"], [597, 56, 546, 8], [597, 58, 546, 9, "_TouchableOpacity"], [597, 75, 546, 9], [597, 76, 546, 9, "default"], [597, 83, 546, 25], [598, 10, 547, 10, "style"], [598, 15, 547, 15], [598, 17, 547, 17], [599, 12, 547, 19, "flex"], [599, 16, 547, 23], [599, 18, 547, 25], [599, 19, 547, 26], [600, 12, 547, 28, "backgroundColor"], [600, 27, 547, 43], [600, 29, 547, 45], [600, 46, 547, 62], [601, 12, 547, 64, "justifyContent"], [601, 26, 547, 78], [601, 28, 547, 80], [601, 36, 547, 88], [602, 12, 547, 90, "alignItems"], [602, 22, 547, 100], [602, 24, 547, 102], [603, 10, 547, 111], [603, 11, 547, 113], [604, 10, 548, 10, "activeOpacity"], [604, 23, 548, 23], [604, 25, 548, 25], [604, 26, 548, 27], [605, 10, 549, 10, "onPressOut"], [605, 20, 549, 20], [605, 22, 549, 22, "onPressOut"], [605, 23, 549, 22], [605, 28, 549, 28, "setIsContextMenuVisible"], [605, 51, 549, 51], [605, 52, 549, 52], [605, 57, 549, 57], [605, 58, 549, 59], [606, 10, 549, 59, "children"], [606, 18, 549, 59], [606, 33, 551, 10], [606, 37, 551, 10, "_jsxDevRuntime"], [606, 51, 551, 10], [606, 52, 551, 10, "jsxDEV"], [606, 58, 551, 10], [606, 60, 551, 11, "_View"], [606, 65, 551, 11], [606, 66, 551, 11, "default"], [606, 73, 551, 15], [607, 12, 551, 16, "style"], [607, 17, 551, 21], [607, 19, 551, 23], [608, 14, 551, 25, "backgroundColor"], [608, 29, 551, 40], [608, 31, 551, 42, "colors"], [608, 37, 551, 48], [608, 38, 551, 49, "background"], [608, 48, 551, 59], [609, 14, 551, 61, "borderRadius"], [609, 26, 551, 73], [609, 28, 551, 75], [609, 30, 551, 77], [610, 14, 551, 79, "padding"], [610, 21, 551, 86], [610, 23, 551, 88], [610, 25, 551, 90], [611, 14, 551, 92, "width"], [611, 19, 551, 97], [611, 21, 551, 99], [612, 12, 551, 105], [612, 13, 551, 107], [613, 12, 551, 107, "children"], [613, 20, 551, 107], [613, 36, 552, 12], [613, 40, 552, 12, "_jsxDevRuntime"], [613, 54, 552, 12], [613, 55, 552, 12, "jsxDEV"], [613, 61, 552, 12], [613, 63, 552, 13, "_TouchableOpacity"], [613, 80, 552, 13], [613, 81, 552, 13, "default"], [613, 88, 552, 29], [614, 14, 552, 30, "onPress"], [614, 21, 552, 37], [614, 23, 552, 39, "handleCopyMessage"], [614, 40, 552, 57], [615, 14, 552, 58, "style"], [615, 19, 552, 63], [615, 21, 552, 65], [616, 16, 552, 67, "paddingVertical"], [616, 31, 552, 82], [616, 33, 552, 84], [617, 14, 552, 87], [617, 15, 552, 89], [618, 14, 552, 89, "children"], [618, 22, 552, 89], [618, 37, 553, 14], [618, 41, 553, 14, "_jsxDevRuntime"], [618, 55, 553, 14], [618, 56, 553, 14, "jsxDEV"], [618, 62, 553, 14], [618, 64, 553, 15, "_Text"], [618, 69, 553, 15], [618, 70, 553, 15, "default"], [618, 77, 553, 19], [619, 16, 553, 20, "style"], [619, 21, 553, 25], [619, 23, 553, 27], [620, 18, 553, 29, "fontSize"], [620, 26, 553, 37], [620, 28, 553, 39], [620, 30, 553, 41], [621, 18, 553, 43, "fontFamily"], [621, 28, 553, 53], [621, 30, 553, 55], [621, 49, 553, 74], [622, 18, 553, 76, "color"], [622, 23, 553, 81], [622, 25, 553, 83, "colors"], [622, 31, 553, 89], [622, 32, 553, 90, "text"], [623, 16, 553, 95], [623, 17, 553, 97], [624, 16, 553, 97, "children"], [624, 24, 553, 97], [624, 26, 553, 98], [625, 14, 553, 110], [626, 16, 553, 110, "fileName"], [626, 24, 553, 110], [626, 26, 553, 110, "_jsxFileName"], [626, 38, 553, 110], [627, 16, 553, 110, "lineNumber"], [627, 26, 553, 110], [628, 16, 553, 110, "columnNumber"], [628, 28, 553, 110], [629, 14, 553, 110], [629, 21, 553, 116], [630, 12, 553, 117], [631, 14, 553, 117, "fileName"], [631, 22, 553, 117], [631, 24, 553, 117, "_jsxFileName"], [631, 36, 553, 117], [632, 14, 553, 117, "lineNumber"], [632, 24, 553, 117], [633, 14, 553, 117, "columnNumber"], [633, 26, 553, 117], [634, 12, 553, 117], [634, 19, 554, 30], [634, 20, 554, 31], [634, 22, 555, 13, "longPressedMessage"], [634, 40, 555, 31], [634, 42, 555, 33, "role"], [634, 46, 555, 37], [634, 51, 555, 42], [634, 62, 555, 53], [634, 79, 556, 14], [634, 83, 556, 14, "_jsxDevRuntime"], [634, 97, 556, 14], [634, 98, 556, 14, "jsxDEV"], [634, 104, 556, 14], [634, 106, 556, 15, "_TouchableOpacity"], [634, 123, 556, 15], [634, 124, 556, 15, "default"], [634, 131, 556, 31], [635, 14, 556, 32, "onPress"], [635, 21, 556, 39], [635, 23, 556, 41, "handleListenToMessage"], [635, 44, 556, 63], [636, 14, 556, 64, "style"], [636, 19, 556, 69], [636, 21, 556, 71], [637, 16, 556, 73, "paddingVertical"], [637, 31, 556, 88], [637, 33, 556, 90], [638, 14, 556, 93], [638, 15, 556, 95], [639, 14, 556, 95, "children"], [639, 22, 556, 95], [639, 37, 557, 16], [639, 41, 557, 16, "_jsxDevRuntime"], [639, 55, 557, 16], [639, 56, 557, 16, "jsxDEV"], [639, 62, 557, 16], [639, 64, 557, 17, "_Text"], [639, 69, 557, 17], [639, 70, 557, 17, "default"], [639, 77, 557, 21], [640, 16, 557, 22, "style"], [640, 21, 557, 27], [640, 23, 557, 29], [641, 18, 557, 31, "fontSize"], [641, 26, 557, 39], [641, 28, 557, 41], [641, 30, 557, 43], [642, 18, 557, 45, "fontFamily"], [642, 28, 557, 55], [642, 30, 557, 57], [642, 49, 557, 76], [643, 18, 557, 78, "color"], [643, 23, 557, 83], [643, 25, 557, 85, "colors"], [643, 31, 557, 91], [643, 32, 557, 92, "text"], [644, 16, 557, 97], [644, 17, 557, 99], [645, 16, 557, 99, "children"], [645, 24, 557, 99], [645, 26, 557, 100], [646, 14, 557, 117], [647, 16, 557, 117, "fileName"], [647, 24, 557, 117], [647, 26, 557, 117, "_jsxFileName"], [647, 38, 557, 117], [648, 16, 557, 117, "lineNumber"], [648, 26, 557, 117], [649, 16, 557, 117, "columnNumber"], [649, 28, 557, 117], [650, 14, 557, 117], [650, 21, 557, 123], [651, 12, 557, 124], [652, 14, 557, 124, "fileName"], [652, 22, 557, 124], [652, 24, 557, 124, "_jsxFileName"], [652, 36, 557, 124], [653, 14, 557, 124, "lineNumber"], [653, 24, 557, 124], [654, 14, 557, 124, "columnNumber"], [654, 26, 557, 124], [655, 12, 557, 124], [655, 19, 558, 32], [655, 20, 559, 13], [656, 10, 559, 13], [657, 12, 559, 13, "fileName"], [657, 20, 559, 13], [657, 22, 559, 13, "_jsxFileName"], [657, 34, 559, 13], [658, 12, 559, 13, "lineNumber"], [658, 22, 559, 13], [659, 12, 559, 13, "columnNumber"], [659, 24, 559, 13], [660, 10, 559, 13], [660, 17, 560, 16], [661, 8, 560, 17], [662, 10, 560, 17, "fileName"], [662, 18, 560, 17], [662, 20, 560, 17, "_jsxFileName"], [662, 32, 560, 17], [663, 10, 560, 17, "lineNumber"], [663, 20, 560, 17], [664, 10, 560, 17, "columnNumber"], [664, 22, 560, 17], [665, 8, 560, 17], [665, 15, 561, 26], [666, 6, 561, 27], [667, 8, 561, 27, "fileName"], [667, 16, 561, 27], [667, 18, 561, 27, "_jsxFileName"], [667, 30, 561, 27], [668, 8, 561, 27, "lineNumber"], [668, 18, 561, 27], [669, 8, 561, 27, "columnNumber"], [669, 20, 561, 27], [670, 6, 561, 27], [670, 13, 562, 13], [670, 14, 562, 14], [671, 4, 562, 14], [672, 6, 562, 14, "fileName"], [672, 14, 562, 14], [672, 16, 562, 14, "_jsxFileName"], [672, 28, 562, 14], [673, 6, 562, 14, "lineNumber"], [673, 16, 562, 14], [674, 6, 562, 14, "columnNumber"], [674, 18, 562, 14], [675, 4, 562, 14], [675, 11, 563, 10], [675, 12, 563, 11], [676, 2, 565, 0], [677, 2, 565, 1, "_s"], [677, 4, 565, 1], [677, 5, 36, 24, "BrainstormScreen"], [677, 21, 36, 40], [678, 4, 36, 40], [678, 12, 37, 17, "useSafeAreaInsets"], [678, 57, 37, 34], [678, 59, 38, 17, "useColors"], [678, 79, 38, 26], [678, 81, 39, 17, "useRouter"], [678, 102, 39, 26], [678, 104, 40, 22, "useQueryClient"], [678, 130, 40, 36], [678, 132, 41, 24, "useFonts"], [678, 149, 41, 32], [678, 151, 47, 19, "useAudioRecorder"], [678, 178, 47, 35], [678, 180, 48, 24, "useAudioRecorderState"], [678, 212, 48, 45], [679, 2, 48, 45], [680, 2, 48, 45, "_c"], [680, 4, 48, 45], [680, 7, 36, 24, "BrainstormScreen"], [680, 23, 36, 40], [681, 2, 36, 40], [681, 6, 36, 40, "_c"], [681, 8, 36, 40], [682, 2, 36, 40, "$RefreshReg$"], [682, 14, 36, 40], [682, 15, 36, 40, "_c"], [682, 17, 36, 40], [683, 0, 36, 40], [683, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCmC;YCgC;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJuC;4BKE;gBCI,mCD;eEuB;kBDK,+BC;KFqB;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;YCI;GDI;gBiB0F,iCjB;0BkBuB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}