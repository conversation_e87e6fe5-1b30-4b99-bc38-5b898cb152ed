{"dependencies": [{"name": "../../../src/private/specs_DEPRECATED/modules/NativeTiming", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 75}}], "key": "3kQU0sVihJrjYuM8HC1IhwO1cGM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _NativeTiming = _interopRequireWildcard(require(_dependencyMap[0], \"../../../src/private/specs_DEPRECATED/modules/NativeTiming\"));\n  Object.keys(_NativeTiming).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _NativeTiming[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _NativeTiming[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _NativeTiming.default;\n});", "lineCount": 21, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeTiming"], [7, 19, 11, 0], [7, 22, 11, 0, "_interopRequireWildcard"], [7, 45, 11, 0], [7, 46, 11, 0, "require"], [7, 53, 11, 0], [7, 54, 11, 0, "_dependencyMap"], [7, 68, 11, 0], [8, 2, 11, 0, "Object"], [8, 8, 11, 0], [8, 9, 11, 0, "keys"], [8, 13, 11, 0], [8, 14, 11, 0, "_NativeTiming"], [8, 27, 11, 0], [8, 29, 11, 0, "for<PERSON>ach"], [8, 36, 11, 0], [8, 47, 11, 0, "key"], [8, 50, 11, 0], [9, 4, 11, 0], [9, 8, 11, 0, "key"], [9, 11, 11, 0], [9, 29, 11, 0, "key"], [9, 32, 11, 0], [10, 4, 11, 0], [10, 8, 11, 0, "Object"], [10, 14, 11, 0], [10, 15, 11, 0, "prototype"], [10, 24, 11, 0], [10, 25, 11, 0, "hasOwnProperty"], [10, 39, 11, 0], [10, 40, 11, 0, "call"], [10, 44, 11, 0], [10, 45, 11, 0, "_exportNames"], [10, 57, 11, 0], [10, 59, 11, 0, "key"], [10, 62, 11, 0], [11, 4, 11, 0], [11, 8, 11, 0, "key"], [11, 11, 11, 0], [11, 15, 11, 0, "exports"], [11, 22, 11, 0], [11, 26, 11, 0, "exports"], [11, 33, 11, 0], [11, 34, 11, 0, "key"], [11, 37, 11, 0], [11, 43, 11, 0, "_NativeTiming"], [11, 56, 11, 0], [11, 57, 11, 0, "key"], [11, 60, 11, 0], [12, 4, 11, 0, "Object"], [12, 10, 11, 0], [12, 11, 11, 0, "defineProperty"], [12, 25, 11, 0], [12, 26, 11, 0, "exports"], [12, 33, 11, 0], [12, 35, 11, 0, "key"], [12, 38, 11, 0], [13, 6, 11, 0, "enumerable"], [13, 16, 11, 0], [14, 6, 11, 0, "get"], [14, 9, 11, 0], [14, 20, 11, 0, "get"], [14, 21, 11, 0], [15, 8, 11, 0], [15, 15, 11, 0, "_NativeTiming"], [15, 28, 11, 0], [15, 29, 11, 0, "key"], [15, 32, 11, 0], [16, 6, 11, 0], [17, 4, 11, 0], [18, 2, 11, 0], [19, 2, 11, 75], [19, 11, 11, 75, "_interopRequireWildcard"], [19, 35, 11, 75, "e"], [19, 36, 11, 75], [19, 38, 11, 75, "t"], [19, 39, 11, 75], [19, 68, 11, 75, "WeakMap"], [19, 75, 11, 75], [19, 81, 11, 75, "r"], [19, 82, 11, 75], [19, 89, 11, 75, "WeakMap"], [19, 96, 11, 75], [19, 100, 11, 75, "n"], [19, 101, 11, 75], [19, 108, 11, 75, "WeakMap"], [19, 115, 11, 75], [19, 127, 11, 75, "_interopRequireWildcard"], [19, 150, 11, 75], [19, 162, 11, 75, "_interopRequireWildcard"], [19, 163, 11, 75, "e"], [19, 164, 11, 75], [19, 166, 11, 75, "t"], [19, 167, 11, 75], [19, 176, 11, 75, "t"], [19, 177, 11, 75], [19, 181, 11, 75, "e"], [19, 182, 11, 75], [19, 186, 11, 75, "e"], [19, 187, 11, 75], [19, 188, 11, 75, "__esModule"], [19, 198, 11, 75], [19, 207, 11, 75, "e"], [19, 208, 11, 75], [19, 214, 11, 75, "o"], [19, 215, 11, 75], [19, 217, 11, 75, "i"], [19, 218, 11, 75], [19, 220, 11, 75, "f"], [19, 221, 11, 75], [19, 226, 11, 75, "__proto__"], [19, 235, 11, 75], [19, 243, 11, 75, "default"], [19, 250, 11, 75], [19, 252, 11, 75, "e"], [19, 253, 11, 75], [19, 270, 11, 75, "e"], [19, 271, 11, 75], [19, 294, 11, 75, "e"], [19, 295, 11, 75], [19, 320, 11, 75, "e"], [19, 321, 11, 75], [19, 330, 11, 75, "f"], [19, 331, 11, 75], [19, 337, 11, 75, "o"], [19, 338, 11, 75], [19, 341, 11, 75, "t"], [19, 342, 11, 75], [19, 345, 11, 75, "n"], [19, 346, 11, 75], [19, 349, 11, 75, "r"], [19, 350, 11, 75], [19, 358, 11, 75, "o"], [19, 359, 11, 75], [19, 360, 11, 75, "has"], [19, 363, 11, 75], [19, 364, 11, 75, "e"], [19, 365, 11, 75], [19, 375, 11, 75, "o"], [19, 376, 11, 75], [19, 377, 11, 75, "get"], [19, 380, 11, 75], [19, 381, 11, 75, "e"], [19, 382, 11, 75], [19, 385, 11, 75, "o"], [19, 386, 11, 75], [19, 387, 11, 75, "set"], [19, 390, 11, 75], [19, 391, 11, 75, "e"], [19, 392, 11, 75], [19, 394, 11, 75, "f"], [19, 395, 11, 75], [19, 409, 11, 75, "_t"], [19, 411, 11, 75], [19, 415, 11, 75, "e"], [19, 416, 11, 75], [19, 432, 11, 75, "_t"], [19, 434, 11, 75], [19, 441, 11, 75, "hasOwnProperty"], [19, 455, 11, 75], [19, 456, 11, 75, "call"], [19, 460, 11, 75], [19, 461, 11, 75, "e"], [19, 462, 11, 75], [19, 464, 11, 75, "_t"], [19, 466, 11, 75], [19, 473, 11, 75, "i"], [19, 474, 11, 75], [19, 478, 11, 75, "o"], [19, 479, 11, 75], [19, 482, 11, 75, "Object"], [19, 488, 11, 75], [19, 489, 11, 75, "defineProperty"], [19, 503, 11, 75], [19, 508, 11, 75, "Object"], [19, 514, 11, 75], [19, 515, 11, 75, "getOwnPropertyDescriptor"], [19, 539, 11, 75], [19, 540, 11, 75, "e"], [19, 541, 11, 75], [19, 543, 11, 75, "_t"], [19, 545, 11, 75], [19, 552, 11, 75, "i"], [19, 553, 11, 75], [19, 554, 11, 75, "get"], [19, 557, 11, 75], [19, 561, 11, 75, "i"], [19, 562, 11, 75], [19, 563, 11, 75, "set"], [19, 566, 11, 75], [19, 570, 11, 75, "o"], [19, 571, 11, 75], [19, 572, 11, 75, "f"], [19, 573, 11, 75], [19, 575, 11, 75, "_t"], [19, 577, 11, 75], [19, 579, 11, 75, "i"], [19, 580, 11, 75], [19, 584, 11, 75, "f"], [19, 585, 11, 75], [19, 586, 11, 75, "_t"], [19, 588, 11, 75], [19, 592, 11, 75, "e"], [19, 593, 11, 75], [19, 594, 11, 75, "_t"], [19, 596, 11, 75], [19, 607, 11, 75, "f"], [19, 608, 11, 75], [19, 613, 11, 75, "e"], [19, 614, 11, 75], [19, 616, 11, 75, "t"], [19, 617, 11, 75], [20, 2, 11, 75], [20, 6, 11, 75, "_default"], [20, 14, 11, 75], [20, 17, 11, 75, "exports"], [20, 24, 11, 75], [20, 25, 11, 75, "default"], [20, 32, 11, 75], [20, 35, 13, 15, "NativeTiming"], [20, 56, 13, 27], [21, 0, 13, 27], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}