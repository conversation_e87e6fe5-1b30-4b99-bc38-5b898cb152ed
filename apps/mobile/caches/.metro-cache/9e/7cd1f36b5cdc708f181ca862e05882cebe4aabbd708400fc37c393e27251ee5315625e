{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Truck = exports.default = (0, _createLucideIcon.default)(\"Truck\", [[\"path\", {\n    d: \"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\",\n    key: \"wrbu53\"\n  }], [\"path\", {\n    d: \"M15 18H9\",\n    key: \"1lyqi6\"\n  }], [\"path\", {\n    d: \"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14\",\n    key: \"lysw3i\"\n  }], [\"circle\", {\n    cx: \"17\",\n    cy: \"18\",\n    r: \"2\",\n    key: \"332jqn\"\n  }], [\"circle\", {\n    cx: \"7\",\n    cy: \"18\",\n    r: \"2\",\n    key: \"19iecd\"\n  }]]);\n});", "lineCount": 35, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Truck"], [15, 13, 10, 11], [15, 16, 10, 11, "exports"], [15, 23, 10, 11], [15, 24, 10, 11, "default"], [15, 31, 10, 11], [15, 34, 10, 14], [15, 38, 10, 14, "createLucideIcon"], [15, 63, 10, 30], [15, 65, 10, 31], [15, 72, 10, 38], [15, 74, 10, 40], [15, 75, 11, 2], [15, 76, 11, 3], [15, 82, 11, 9], [15, 84, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 66, 11, 75], [17, 4, 11, 77, "key"], [17, 7, 11, 80], [17, 9, 11, 82], [18, 2, 11, 91], [18, 3, 11, 92], [18, 4, 11, 93], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 17, 12, 26], [20, 4, 12, 28, "key"], [20, 7, 12, 31], [20, 9, 12, 33], [21, 2, 12, 42], [21, 3, 12, 43], [21, 4, 12, 44], [21, 6, 13, 2], [21, 7, 14, 4], [21, 13, 14, 10], [21, 15, 15, 4], [22, 4, 16, 6, "d"], [22, 5, 16, 7], [22, 7, 16, 9], [22, 88, 16, 90], [23, 4, 17, 6, "key"], [23, 7, 17, 9], [23, 9, 17, 11], [24, 2, 18, 4], [24, 3, 18, 5], [24, 4, 19, 3], [24, 6, 20, 2], [24, 7, 20, 3], [24, 15, 20, 11], [24, 17, 20, 13], [25, 4, 20, 15, "cx"], [25, 6, 20, 17], [25, 8, 20, 19], [25, 12, 20, 23], [26, 4, 20, 25, "cy"], [26, 6, 20, 27], [26, 8, 20, 29], [26, 12, 20, 33], [27, 4, 20, 35, "r"], [27, 5, 20, 36], [27, 7, 20, 38], [27, 10, 20, 41], [28, 4, 20, 43, "key"], [28, 7, 20, 46], [28, 9, 20, 48], [29, 2, 20, 57], [29, 3, 20, 58], [29, 4, 20, 59], [29, 6, 21, 2], [29, 7, 21, 3], [29, 15, 21, 11], [29, 17, 21, 13], [30, 4, 21, 15, "cx"], [30, 6, 21, 17], [30, 8, 21, 19], [30, 11, 21, 22], [31, 4, 21, 24, "cy"], [31, 6, 21, 26], [31, 8, 21, 28], [31, 12, 21, 32], [32, 4, 21, 34, "r"], [32, 5, 21, 35], [32, 7, 21, 37], [32, 10, 21, 40], [33, 4, 21, 42, "key"], [33, 7, 21, 45], [33, 9, 21, 47], [34, 2, 21, 56], [34, 3, 21, 57], [34, 4, 21, 58], [34, 5, 22, 1], [34, 6, 22, 2], [35, 0, 22, 3], [35, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}