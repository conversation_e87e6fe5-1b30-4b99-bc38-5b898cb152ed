{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var ReactNativeFeatureFlags = {\n    shouldEmitW3CPointerEvents: () => false,\n    shouldPressibilityUseW3CPointerEventsForHover: () => false\n  };\n  var _default = exports.default = ReactNativeFeatureFlags;\n});", "lineCount": 11, "map": [[6, 2, 26, 0], [6, 6, 26, 6, "ReactNativeFeatureFlags"], [6, 29, 26, 43], [6, 32, 26, 46], [7, 4, 27, 2, "shouldEmitW3CPointerEvents"], [7, 30, 27, 28], [7, 32, 27, 30, "shouldEmitW3CPointerEvents"], [7, 33, 27, 30], [7, 38, 27, 36], [7, 43, 27, 41], [8, 4, 28, 2, "shouldPressibilityUseW3CPointerEventsForHover"], [8, 49, 28, 47], [8, 51, 28, 49, "shouldPressibilityUseW3CPointerEventsForHover"], [8, 52, 28, 49], [8, 57, 28, 55], [9, 2, 29, 0], [9, 3, 29, 1], [10, 2, 29, 2], [10, 6, 29, 2, "_default"], [10, 14, 29, 2], [10, 17, 29, 2, "exports"], [10, 24, 29, 2], [10, 25, 29, 2, "default"], [10, 32, 29, 2], [10, 35, 31, 15, "ReactNativeFeatureFlags"], [10, 58, 31, 38], [11, 0, 31, 38], [11, 3]], "functionMap": {"names": ["<global>", "shouldEmitW3CPointerEvents", "shouldPressibilityUseW3CPointerEventsForHover"], "mappings": "AAA;8BC0B,WD;iDEC,WF"}}, "type": "js/module"}]}