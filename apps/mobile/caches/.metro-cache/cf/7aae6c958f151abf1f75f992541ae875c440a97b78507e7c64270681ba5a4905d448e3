{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const NotepadText = exports.default = (0, _createLucideIcon.default)(\"NotepadText\", [[\"path\", {\n    d: \"M8 2v4\",\n    key: \"1cmpym\"\n  }], [\"path\", {\n    d: \"M12 2v4\",\n    key: \"3427ic\"\n  }], [\"path\", {\n    d: \"M16 2v4\",\n    key: \"4m81vk\"\n  }], [\"rect\", {\n    width: \"16\",\n    height: \"18\",\n    x: \"4\",\n    y: \"4\",\n    rx: \"2\",\n    key: \"1u9h20\"\n  }], [\"path\", {\n    d: \"M8 10h6\",\n    key: \"3oa6kw\"\n  }], [\"path\", {\n    d: \"M8 14h8\",\n    key: \"1fgep2\"\n  }], [\"path\", {\n    d: \"M8 18h5\",\n    key: \"17enja\"\n  }]]);\n});", "lineCount": 41, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "NotepadText"], [15, 19, 10, 17], [15, 22, 10, 17, "exports"], [15, 29, 10, 17], [15, 30, 10, 17, "default"], [15, 37, 10, 17], [15, 40, 10, 20], [15, 44, 10, 20, "createLucideIcon"], [15, 69, 10, 36], [15, 71, 10, 37], [15, 84, 10, 50], [15, 86, 10, 52], [15, 87, 11, 2], [15, 88, 11, 3], [15, 94, 11, 9], [15, 96, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 15, 11, 24], [17, 4, 11, 26, "key"], [17, 7, 11, 29], [17, 9, 11, 31], [18, 2, 11, 40], [18, 3, 11, 41], [18, 4, 11, 42], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 16, 13, 25], [23, 4, 13, 27, "key"], [23, 7, 13, 30], [23, 9, 13, 32], [24, 2, 13, 41], [24, 3, 13, 42], [24, 4, 13, 43], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "width"], [25, 9, 14, 18], [25, 11, 14, 20], [25, 15, 14, 24], [26, 4, 14, 26, "height"], [26, 10, 14, 32], [26, 12, 14, 34], [26, 16, 14, 38], [27, 4, 14, 40, "x"], [27, 5, 14, 41], [27, 7, 14, 43], [27, 10, 14, 46], [28, 4, 14, 48, "y"], [28, 5, 14, 49], [28, 7, 14, 51], [28, 10, 14, 54], [29, 4, 14, 56, "rx"], [29, 6, 14, 58], [29, 8, 14, 60], [29, 11, 14, 63], [30, 4, 14, 65, "key"], [30, 7, 14, 68], [30, 9, 14, 70], [31, 2, 14, 79], [31, 3, 14, 80], [31, 4, 14, 81], [31, 6, 15, 2], [31, 7, 15, 3], [31, 13, 15, 9], [31, 15, 15, 11], [32, 4, 15, 13, "d"], [32, 5, 15, 14], [32, 7, 15, 16], [32, 16, 15, 25], [33, 4, 15, 27, "key"], [33, 7, 15, 30], [33, 9, 15, 32], [34, 2, 15, 41], [34, 3, 15, 42], [34, 4, 15, 43], [34, 6, 16, 2], [34, 7, 16, 3], [34, 13, 16, 9], [34, 15, 16, 11], [35, 4, 16, 13, "d"], [35, 5, 16, 14], [35, 7, 16, 16], [35, 16, 16, 25], [36, 4, 16, 27, "key"], [36, 7, 16, 30], [36, 9, 16, 32], [37, 2, 16, 41], [37, 3, 16, 42], [37, 4, 16, 43], [37, 6, 17, 2], [37, 7, 17, 3], [37, 13, 17, 9], [37, 15, 17, 11], [38, 4, 17, 13, "d"], [38, 5, 17, 14], [38, 7, 17, 16], [38, 16, 17, 25], [39, 4, 17, 27, "key"], [39, 7, 17, 30], [39, 9, 17, 32], [40, 2, 17, 41], [40, 3, 17, 42], [40, 4, 17, 43], [40, 5, 18, 1], [40, 6, 18, 2], [41, 0, 18, 3], [41, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}