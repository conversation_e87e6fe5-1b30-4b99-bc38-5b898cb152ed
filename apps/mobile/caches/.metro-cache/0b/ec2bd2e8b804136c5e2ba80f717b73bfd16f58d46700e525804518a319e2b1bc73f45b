{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fromBouncinessAndSpeed = fromBouncinessAndSpeed;\n  exports.fromOrigamiTensionAndFriction = fromOrigamiTensionAndFriction;\n  function stiffnessFromOrigamiValue(oValue) {\n    return (oValue - 30) * 3.62 + 194;\n  }\n  function dampingFromOrigamiValue(oValue) {\n    return (oValue - 8) * 3 + 25;\n  }\n  function fromOrigamiTensionAndFriction(tension, friction) {\n    return {\n      stiffness: stiffnessFromOrigamiValue(tension),\n      damping: dampingFromOrigamiValue(friction)\n    };\n  }\n  function fromBouncinessAndSpeed(bounciness, speed) {\n    function normalize(value, startValue, endValue) {\n      return (value - startValue) / (endValue - startValue);\n    }\n    function projectNormal(n, start, end) {\n      return start + n * (end - start);\n    }\n    function linearInterpolation(t, start, end) {\n      return t * end + (1 - t) * start;\n    }\n    function quadraticOutInterpolation(t, start, end) {\n      return linearInterpolation(2 * t - t * t, start, end);\n    }\n    function b3Friction1(x) {\n      return 0.0007 * Math.pow(x, 3) - 0.031 * Math.pow(x, 2) + 0.64 * x + 1.28;\n    }\n    function b3Friction2(x) {\n      return 0.000044 * Math.pow(x, 3) - 0.006 * Math.pow(x, 2) + 0.36 * x + 2;\n    }\n    function b3Friction3(x) {\n      return 0.00000045 * Math.pow(x, 3) - 0.000332 * Math.pow(x, 2) + 0.1078 * x + 5.84;\n    }\n    function b3Nobounce(tension) {\n      if (tension <= 18) {\n        return b3Friction1(tension);\n      } else if (tension > 18 && tension <= 44) {\n        return b3Friction2(tension);\n      } else {\n        return b3Friction3(tension);\n      }\n    }\n    var b = normalize(bounciness / 1.7, 0, 20);\n    b = projectNormal(b, 0, 0.8);\n    var s = normalize(speed / 1.7, 0, 20);\n    var bouncyTension = projectNormal(s, 0.5, 200);\n    var bouncyFriction = quadraticOutInterpolation(b, b3Nobounce(bouncyTension), 0.01);\n    return {\n      stiffness: stiffnessFromOrigamiValue(bouncyTension),\n      damping: dampingFromOrigamiValue(bouncyFriction)\n    };\n  }\n});", "lineCount": 62, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "fromBouncinessAndSpeed"], [7, 32, 11, 13], [7, 35, 11, 13, "fromBouncinessAndSpeed"], [7, 57, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "fromOrigamiTensionAndFriction"], [8, 39, 11, 13], [8, 42, 11, 13, "fromOrigamiTensionAndFriction"], [8, 71, 11, 13], [9, 2, 19, 0], [9, 11, 19, 9, "stiffnessFromOrigamiValue"], [9, 36, 19, 34, "stiffnessFromOrigamiValue"], [9, 37, 19, 35, "oValue"], [9, 43, 19, 49], [9, 45, 19, 51], [10, 4, 20, 2], [10, 11, 20, 9], [10, 12, 20, 10, "oValue"], [10, 18, 20, 16], [10, 21, 20, 19], [10, 23, 20, 21], [10, 27, 20, 25], [10, 31, 20, 29], [10, 34, 20, 32], [10, 37, 20, 35], [11, 2, 21, 0], [12, 2, 23, 0], [12, 11, 23, 9, "dampingFromOrigamiValue"], [12, 34, 23, 32, "dampingFromOrigamiValue"], [12, 35, 23, 33, "oValue"], [12, 41, 23, 47], [12, 43, 23, 49], [13, 4, 24, 2], [13, 11, 24, 9], [13, 12, 24, 10, "oValue"], [13, 18, 24, 16], [13, 21, 24, 19], [13, 22, 24, 20], [13, 26, 24, 24], [13, 27, 24, 25], [13, 30, 24, 28], [13, 32, 24, 30], [14, 2, 25, 0], [15, 2, 27, 7], [15, 11, 27, 16, "fromOrigamiTensionAndFriction"], [15, 40, 27, 45, "fromOrigamiTensionAndFriction"], [15, 41, 28, 2, "tension"], [15, 48, 28, 17], [15, 50, 29, 2, "friction"], [15, 58, 29, 18], [15, 60, 30, 20], [16, 4, 31, 2], [16, 11, 31, 9], [17, 6, 32, 4, "stiffness"], [17, 15, 32, 13], [17, 17, 32, 15, "stiffnessFromOrigamiValue"], [17, 42, 32, 40], [17, 43, 32, 41, "tension"], [17, 50, 32, 48], [17, 51, 32, 49], [18, 6, 33, 4, "damping"], [18, 13, 33, 11], [18, 15, 33, 13, "dampingFromOrigamiValue"], [18, 38, 33, 36], [18, 39, 33, 37, "friction"], [18, 47, 33, 45], [19, 4, 34, 2], [19, 5, 34, 3], [20, 2, 35, 0], [21, 2, 37, 7], [21, 11, 37, 16, "fromBouncinessAndSpeed"], [21, 33, 37, 38, "fromBouncinessAndSpeed"], [21, 34, 38, 2, "bounciness"], [21, 44, 38, 20], [21, 46, 39, 2, "speed"], [21, 51, 39, 15], [21, 53, 40, 20], [22, 4, 41, 2], [22, 13, 41, 11, "normalize"], [22, 22, 41, 20, "normalize"], [22, 23, 41, 21, "value"], [22, 28, 41, 34], [22, 30, 41, 36, "startValue"], [22, 40, 41, 54], [22, 42, 41, 56, "endValue"], [22, 50, 41, 72], [22, 52, 41, 74], [23, 6, 42, 4], [23, 13, 42, 11], [23, 14, 42, 12, "value"], [23, 19, 42, 17], [23, 22, 42, 20, "startValue"], [23, 32, 42, 30], [23, 37, 42, 35, "endValue"], [23, 45, 42, 43], [23, 48, 42, 46, "startValue"], [23, 58, 42, 56], [23, 59, 42, 57], [24, 4, 43, 2], [25, 4, 45, 2], [25, 13, 45, 11, "projectNormal"], [25, 26, 45, 24, "projectNormal"], [25, 27, 45, 25, "n"], [25, 28, 45, 34], [25, 30, 45, 36, "start"], [25, 35, 45, 49], [25, 37, 45, 51, "end"], [25, 40, 45, 62], [25, 42, 45, 64], [26, 6, 46, 4], [26, 13, 46, 11, "start"], [26, 18, 46, 16], [26, 21, 46, 19, "n"], [26, 22, 46, 20], [26, 26, 46, 24, "end"], [26, 29, 46, 27], [26, 32, 46, 30, "start"], [26, 37, 46, 35], [26, 38, 46, 36], [27, 4, 47, 2], [28, 4, 49, 2], [28, 13, 49, 11, "linearInterpolation"], [28, 32, 49, 30, "linearInterpolation"], [28, 33, 49, 31, "t"], [28, 34, 49, 40], [28, 36, 49, 42, "start"], [28, 41, 49, 55], [28, 43, 49, 57, "end"], [28, 46, 49, 68], [28, 48, 49, 70], [29, 6, 50, 4], [29, 13, 50, 11, "t"], [29, 14, 50, 12], [29, 17, 50, 15, "end"], [29, 20, 50, 18], [29, 23, 50, 21], [29, 24, 50, 22], [29, 25, 50, 23], [29, 28, 50, 26, "t"], [29, 29, 50, 27], [29, 33, 50, 31, "start"], [29, 38, 50, 36], [30, 4, 51, 2], [31, 4, 53, 2], [31, 13, 53, 11, "quadraticOutInterpolation"], [31, 38, 53, 36, "quadraticOutInterpolation"], [31, 39, 53, 37, "t"], [31, 40, 53, 46], [31, 42, 53, 48, "start"], [31, 47, 53, 61], [31, 49, 53, 63, "end"], [31, 52, 53, 74], [31, 54, 53, 76], [32, 6, 54, 4], [32, 13, 54, 11, "linearInterpolation"], [32, 32, 54, 30], [32, 33, 54, 31], [32, 34, 54, 32], [32, 37, 54, 35, "t"], [32, 38, 54, 36], [32, 41, 54, 39, "t"], [32, 42, 54, 40], [32, 45, 54, 43, "t"], [32, 46, 54, 44], [32, 48, 54, 46, "start"], [32, 53, 54, 51], [32, 55, 54, 53, "end"], [32, 58, 54, 56], [32, 59, 54, 57], [33, 4, 55, 2], [34, 4, 57, 2], [34, 13, 57, 11, "b3Friction1"], [34, 24, 57, 22, "b3Friction1"], [34, 25, 57, 23, "x"], [34, 26, 57, 32], [34, 28, 57, 34], [35, 6, 58, 4], [35, 13, 58, 11], [35, 19, 58, 17], [35, 22, 58, 20, "Math"], [35, 26, 58, 24], [35, 27, 58, 25, "pow"], [35, 30, 58, 28], [35, 31, 58, 29, "x"], [35, 32, 58, 30], [35, 34, 58, 32], [35, 35, 58, 33], [35, 36, 58, 34], [35, 39, 58, 37], [35, 44, 58, 42], [35, 47, 58, 45, "Math"], [35, 51, 58, 49], [35, 52, 58, 50, "pow"], [35, 55, 58, 53], [35, 56, 58, 54, "x"], [35, 57, 58, 55], [35, 59, 58, 57], [35, 60, 58, 58], [35, 61, 58, 59], [35, 64, 58, 62], [35, 68, 58, 66], [35, 71, 58, 69, "x"], [35, 72, 58, 70], [35, 75, 58, 73], [35, 79, 58, 77], [36, 4, 59, 2], [37, 4, 61, 2], [37, 13, 61, 11, "b3Friction2"], [37, 24, 61, 22, "b3Friction2"], [37, 25, 61, 23, "x"], [37, 26, 61, 32], [37, 28, 61, 34], [38, 6, 62, 4], [38, 13, 62, 11], [38, 21, 62, 19], [38, 24, 62, 22, "Math"], [38, 28, 62, 26], [38, 29, 62, 27, "pow"], [38, 32, 62, 30], [38, 33, 62, 31, "x"], [38, 34, 62, 32], [38, 36, 62, 34], [38, 37, 62, 35], [38, 38, 62, 36], [38, 41, 62, 39], [38, 46, 62, 44], [38, 49, 62, 47, "Math"], [38, 53, 62, 51], [38, 54, 62, 52, "pow"], [38, 57, 62, 55], [38, 58, 62, 56, "x"], [38, 59, 62, 57], [38, 61, 62, 59], [38, 62, 62, 60], [38, 63, 62, 61], [38, 66, 62, 64], [38, 70, 62, 68], [38, 73, 62, 71, "x"], [38, 74, 62, 72], [38, 77, 62, 75], [38, 78, 62, 76], [39, 4, 63, 2], [40, 4, 65, 2], [40, 13, 65, 11, "b3Friction3"], [40, 24, 65, 22, "b3Friction3"], [40, 25, 65, 23, "x"], [40, 26, 65, 32], [40, 28, 65, 34], [41, 6, 66, 4], [41, 13, 67, 6], [41, 23, 67, 16], [41, 26, 67, 19, "Math"], [41, 30, 67, 23], [41, 31, 67, 24, "pow"], [41, 34, 67, 27], [41, 35, 67, 28, "x"], [41, 36, 67, 29], [41, 38, 67, 31], [41, 39, 67, 32], [41, 40, 67, 33], [41, 43, 68, 6], [41, 51, 68, 14], [41, 54, 68, 17, "Math"], [41, 58, 68, 21], [41, 59, 68, 22, "pow"], [41, 62, 68, 25], [41, 63, 68, 26, "x"], [41, 64, 68, 27], [41, 66, 68, 29], [41, 67, 68, 30], [41, 68, 68, 31], [41, 71, 69, 6], [41, 77, 69, 12], [41, 80, 69, 15, "x"], [41, 81, 69, 16], [41, 84, 70, 6], [41, 88, 70, 10], [42, 4, 72, 2], [43, 4, 74, 2], [43, 13, 74, 11, "b3Nobounce"], [43, 23, 74, 21, "b3Nobounce"], [43, 24, 74, 22, "tension"], [43, 31, 74, 37], [43, 33, 74, 39], [44, 6, 75, 4], [44, 10, 75, 8, "tension"], [44, 17, 75, 15], [44, 21, 75, 19], [44, 23, 75, 21], [44, 25, 75, 23], [45, 8, 76, 6], [45, 15, 76, 13, "b3Friction1"], [45, 26, 76, 24], [45, 27, 76, 25, "tension"], [45, 34, 76, 32], [45, 35, 76, 33], [46, 6, 77, 4], [46, 7, 77, 5], [46, 13, 77, 11], [46, 17, 77, 15, "tension"], [46, 24, 77, 22], [46, 27, 77, 25], [46, 29, 77, 27], [46, 33, 77, 31, "tension"], [46, 40, 77, 38], [46, 44, 77, 42], [46, 46, 77, 44], [46, 48, 77, 46], [47, 8, 78, 6], [47, 15, 78, 13, "b3Friction2"], [47, 26, 78, 24], [47, 27, 78, 25, "tension"], [47, 34, 78, 32], [47, 35, 78, 33], [48, 6, 79, 4], [48, 7, 79, 5], [48, 13, 79, 11], [49, 8, 80, 6], [49, 15, 80, 13, "b3Friction3"], [49, 26, 80, 24], [49, 27, 80, 25, "tension"], [49, 34, 80, 32], [49, 35, 80, 33], [50, 6, 81, 4], [51, 4, 82, 2], [52, 4, 84, 2], [52, 8, 84, 6, "b"], [52, 9, 84, 7], [52, 12, 84, 10, "normalize"], [52, 21, 84, 19], [52, 22, 84, 20, "bounciness"], [52, 32, 84, 30], [52, 35, 84, 33], [52, 38, 84, 36], [52, 40, 84, 38], [52, 41, 84, 39], [52, 43, 84, 41], [52, 45, 84, 43], [52, 46, 84, 44], [53, 4, 85, 2, "b"], [53, 5, 85, 3], [53, 8, 85, 6, "projectNormal"], [53, 21, 85, 19], [53, 22, 85, 20, "b"], [53, 23, 85, 21], [53, 25, 85, 23], [53, 26, 85, 24], [53, 28, 85, 26], [53, 31, 85, 29], [53, 32, 85, 30], [54, 4, 86, 2], [54, 8, 86, 8, "s"], [54, 9, 86, 9], [54, 12, 86, 12, "normalize"], [54, 21, 86, 21], [54, 22, 86, 22, "speed"], [54, 27, 86, 27], [54, 30, 86, 30], [54, 33, 86, 33], [54, 35, 86, 35], [54, 36, 86, 36], [54, 38, 86, 38], [54, 40, 86, 40], [54, 41, 86, 41], [55, 4, 87, 2], [55, 8, 87, 8, "bouncyTension"], [55, 21, 87, 21], [55, 24, 87, 24, "projectNormal"], [55, 37, 87, 37], [55, 38, 87, 38, "s"], [55, 39, 87, 39], [55, 41, 87, 41], [55, 44, 87, 44], [55, 46, 87, 46], [55, 49, 87, 49], [55, 50, 87, 50], [56, 4, 88, 2], [56, 8, 88, 8, "bouncyFriction"], [56, 22, 88, 22], [56, 25, 88, 25, "quadraticOutInterpolation"], [56, 50, 88, 50], [56, 51, 89, 4, "b"], [56, 52, 89, 5], [56, 54, 90, 4, "b3Nobounce"], [56, 64, 90, 14], [56, 65, 90, 15, "bouncyTension"], [56, 78, 90, 28], [56, 79, 90, 29], [56, 81, 91, 4], [56, 85, 92, 2], [56, 86, 92, 3], [57, 4, 94, 2], [57, 11, 94, 9], [58, 6, 95, 4, "stiffness"], [58, 15, 95, 13], [58, 17, 95, 15, "stiffnessFromOrigamiValue"], [58, 42, 95, 40], [58, 43, 95, 41, "bouncyTension"], [58, 56, 95, 54], [58, 57, 95, 55], [59, 6, 96, 4, "damping"], [59, 13, 96, 11], [59, 15, 96, 13, "dampingFromOrigamiValue"], [59, 38, 96, 36], [59, 39, 96, 37, "bouncyFriction"], [59, 53, 96, 51], [60, 4, 97, 2], [60, 5, 97, 3], [61, 2, 98, 0], [62, 0, 98, 1], [62, 3]], "functionMap": {"names": ["<global>", "stiffnessFromOrigamiValue", "dampingFromOrigamiValue", "fromOrigamiTensionAndFriction", "fromBouncinessAndSpeed", "normalize", "projectNormal", "linearInterpolation", "quadraticOutInterpolation", "b3Friction1", "b3Friction2", "b3Friction3", "b3Nobounce"], "mappings": "AAA;ACkB;CDE;AEE;CFE;OGE;CHQ;OIE;ECI;GDE;EEE;GFE;EGE;GHE;EIE;GJE;EKE;GLE;EME;GNE;EOE;GPO;EQE;GRQ"}}, "type": "js/module"}]}