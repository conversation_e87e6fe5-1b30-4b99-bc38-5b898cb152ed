{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 80, "index": 95}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 96}, "end": {"line": 4, "column": 52, "index": 148}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 149}, "end": {"line": 5, "column": 26, "index": 175}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 176}, "end": {"line": 6, "column": 26, "index": 202}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 203}, "end": {"line": 7, "column": 58, "index": 261}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./TabBarIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 262}, "end": {"line": 8, "column": 45, "index": 307}}], "key": "FMB8MytshydkrDDOSQ85sBeOr4Y=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 308}, "end": {"line": 9, "column": 63, "index": 371}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BottomTabItem = BottomTabItem;\n  var _elements = require(_dependencyMap[1], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[3], \"color\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[4], \"react\"));\n  var _reactNative = require(_dependencyMap[5], \"react-native\");\n  var _TabBarIcon = require(_dependencyMap[6], \"./TabBarIcon.js\");\n  var _jsxRuntime = require(_dependencyMap[7], \"react/jsx-runtime\");\n  var renderButtonDefault = props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.PlatformPressable, {\n    ...props\n  });\n  var SUPPORTS_LARGE_CONTENT_VIEWER = _reactNative.Platform.OS === 'ios' && parseInt(_reactNative.Platform.Version, 10) >= 13;\n  function BottomTabItem(_ref) {\n    var route = _ref.route,\n      href = _ref.href,\n      focused = _ref.focused,\n      descriptor = _ref.descriptor,\n      label = _ref.label,\n      icon = _ref.icon,\n      badge = _ref.badge,\n      badgeStyle = _ref.badgeStyle,\n      _ref$button = _ref.button,\n      button = _ref$button === void 0 ? renderButtonDefault : _ref$button,\n      accessibilityLabel = _ref.accessibilityLabel,\n      testID = _ref.testID,\n      onPress = _ref.onPress,\n      onLongPress = _ref.onLongPress,\n      horizontal = _ref.horizontal,\n      compact = _ref.compact,\n      sidebar = _ref.sidebar,\n      variant = _ref.variant,\n      customActiveTintColor = _ref.activeTintColor,\n      customInactiveTintColor = _ref.inactiveTintColor,\n      customActiveBackgroundColor = _ref.activeBackgroundColor,\n      _ref$inactiveBackgrou = _ref.inactiveBackgroundColor,\n      inactiveBackgroundColor = _ref$inactiveBackgrou === void 0 ? 'transparent' : _ref$inactiveBackgrou,\n      _ref$showLabel = _ref.showLabel,\n      showLabel = _ref$showLabel === void 0 ? true : _ref$showLabel,\n      _ref$allowFontScaling = _ref.allowFontScaling,\n      allowFontScaling = _ref$allowFontScaling === void 0 ? SUPPORTS_LARGE_CONTENT_VIEWER ? false : undefined : _ref$allowFontScaling,\n      labelStyle = _ref.labelStyle,\n      iconStyle = _ref.iconStyle,\n      style = _ref.style;\n    var _useTheme = (0, _native.useTheme)(),\n      colors = _useTheme.colors,\n      fonts = _useTheme.fonts;\n    var activeTintColor = customActiveTintColor ?? (variant === 'uikit' && sidebar && horizontal ? (0, _color.default)(colors.primary).isDark() ? 'white' : (0, _color.default)(colors.primary).darken(0.71).string() : colors.primary);\n    var inactiveTintColor = customInactiveTintColor === undefined ? variant === 'material' ? (0, _color.default)(colors.text).alpha(0.68).rgb().string() : (0, _color.default)(colors.text).mix((0, _color.default)(colors.card), 0.5).hex() : customInactiveTintColor;\n    var activeBackgroundColor = customActiveBackgroundColor ?? (variant === 'material' ? (0, _color.default)(activeTintColor).alpha(0.12).rgb().string() : sidebar && horizontal ? colors.primary : 'transparent');\n    var options = descriptor.options;\n    var labelString = (0, _elements.getLabel)({\n      label: typeof options.tabBarLabel === 'string' ? options.tabBarLabel : undefined,\n      title: options.title\n    }, route.name);\n    var labelInactiveTintColor = inactiveTintColor;\n    var iconInactiveTintColor = inactiveTintColor;\n    if (variant === 'uikit' && sidebar && horizontal && customInactiveTintColor === undefined) {\n      iconInactiveTintColor = colors.primary;\n      labelInactiveTintColor = colors.text;\n    }\n    var renderLabel = _ref2 => {\n      var focused = _ref2.focused;\n      if (showLabel === false) {\n        return null;\n      }\n      var color = focused ? activeTintColor : labelInactiveTintColor;\n      if (typeof label !== 'string') {\n        return label({\n          focused,\n          color,\n          position: horizontal ? 'beside-icon' : 'below-icon',\n          children: labelString\n        });\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.Label, {\n        style: [horizontal ? [styles.labelBeside, variant === 'material' ? styles.labelSidebarMaterial : sidebar ? styles.labelSidebarUiKit : compact ? styles.labelBesideUikitCompact : styles.labelBesideUikit, icon == null && {\n          marginStart: 0\n        }] : styles.labelBeneath, compact || variant === 'uikit' && sidebar && horizontal ? fonts.regular : fonts.medium, labelStyle],\n        allowFontScaling: allowFontScaling,\n        tintColor: color,\n        children: label\n      });\n    };\n    var renderIcon = _ref3 => {\n      var focused = _ref3.focused;\n      if (icon === undefined) {\n        return null;\n      }\n      var activeOpacity = focused ? 1 : 0;\n      var inactiveOpacity = focused ? 0 : 1;\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_TabBarIcon.TabBarIcon, {\n        route: route,\n        variant: variant,\n        size: compact ? 'compact' : 'regular',\n        badge: badge,\n        badgeStyle: badgeStyle,\n        activeOpacity: activeOpacity,\n        allowFontScaling: allowFontScaling,\n        inactiveOpacity: inactiveOpacity,\n        activeTintColor: activeTintColor,\n        inactiveTintColor: iconInactiveTintColor,\n        renderIcon: icon,\n        style: iconStyle\n      });\n    };\n    var scene = {\n      route,\n      focused\n    };\n    var backgroundColor = focused ? activeBackgroundColor : inactiveBackgroundColor;\n    var _StyleSheet$flatten = _reactNative.StyleSheet.flatten(style || {}),\n      flex = _StyleSheet$flatten.flex;\n    var borderRadius = variant === 'material' ? horizontal ? 56 : 16 : sidebar && horizontal ? 10 : 0;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {\n      style: [\n      // Clip ripple effect on Android\n      {\n        borderRadius,\n        overflow: variant === 'material' ? 'hidden' : 'visible'\n      }, style],\n      children: button({\n        href,\n        onPress,\n        onLongPress,\n        testID,\n        'aria-label': accessibilityLabel,\n        'accessibilityLargeContentTitle': labelString,\n        'accessibilityShowsLargeContentViewer': true,\n        // FIXME: role: 'tab' doesn't seem to work as expected on iOS\n        'role': _reactNative.Platform.select({\n          ios: 'button',\n          default: 'tab'\n        }),\n        'aria-selected': focused,\n        'android_ripple': {\n          borderless: true\n        },\n        'hoverEffect': variant === 'material' || sidebar && horizontal ? {\n          color: colors.text\n        } : undefined,\n        'pressOpacity': 1,\n        'style': [styles.tab, {\n          flex,\n          backgroundColor,\n          borderRadius\n        }, sidebar ? variant === 'material' ? horizontal ? styles.tabBarSidebarMaterial : styles.tabVerticalMaterial : horizontal ? styles.tabBarSidebarUiKit : styles.tabVerticalUiKit : variant === 'material' ? styles.tabVerticalMaterial : horizontal ? styles.tabHorizontalUiKit : styles.tabVerticalUiKit],\n        'children': /*#__PURE__*/(0, _jsxRuntime.jsxs)(_react.default.Fragment, {\n          children: [renderIcon(scene), renderLabel(scene)]\n        })\n      })\n    });\n  }\n  var styles = _reactNative.StyleSheet.create({\n    tab: {\n      alignItems: 'center',\n      // Roundness for iPad hover effect\n      borderRadius: 10\n    },\n    tabVerticalUiKit: {\n      justifyContent: 'flex-start',\n      flexDirection: 'column',\n      padding: 5\n    },\n    tabVerticalMaterial: {\n      padding: 10\n    },\n    tabHorizontalUiKit: {\n      justifyContent: 'center',\n      alignItems: 'center',\n      flexDirection: 'row',\n      padding: 5\n    },\n    tabBarSidebarUiKit: {\n      justifyContent: 'flex-start',\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingVertical: 7,\n      paddingHorizontal: 5\n    },\n    tabBarSidebarMaterial: {\n      justifyContent: 'flex-start',\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingVertical: 15,\n      paddingStart: 16,\n      paddingEnd: 24\n    },\n    labelSidebarMaterial: {\n      marginStart: 12\n    },\n    labelSidebarUiKit: {\n      fontSize: 17,\n      marginStart: 10\n    },\n    labelBeneath: {\n      fontSize: 10\n    },\n    labelBeside: {\n      marginEnd: 12,\n      lineHeight: 24\n    },\n    labelBesideUikit: {\n      fontSize: 13,\n      marginStart: 5\n    },\n    labelBesideUikitCompact: {\n      fontSize: 12,\n      marginStart: 5\n    }\n  });\n});", "lineCount": 218, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "BottomTabItem"], [8, 23, 1, 13], [8, 26, 1, 13, "BottomTabItem"], [8, 39, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_elements"], [9, 15, 3, 0], [9, 18, 3, 0, "require"], [9, 25, 3, 0], [9, 26, 3, 0, "_dependencyMap"], [9, 40, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_native"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_color"], [11, 12, 5, 0], [11, 15, 5, 0, "_interopRequireDefault"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_react"], [12, 12, 6, 0], [12, 15, 6, 0, "_interopRequireDefault"], [12, 37, 6, 0], [12, 38, 6, 0, "require"], [12, 45, 6, 0], [12, 46, 6, 0, "_dependencyMap"], [12, 60, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_reactNative"], [13, 18, 7, 0], [13, 21, 7, 0, "require"], [13, 28, 7, 0], [13, 29, 7, 0, "_dependencyMap"], [13, 43, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_TabBarIcon"], [14, 17, 8, 0], [14, 20, 8, 0, "require"], [14, 27, 8, 0], [14, 28, 8, 0, "_dependencyMap"], [14, 42, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_jsxRuntime"], [15, 17, 9, 0], [15, 20, 9, 0, "require"], [15, 27, 9, 0], [15, 28, 9, 0, "_dependencyMap"], [15, 42, 9, 0], [16, 2, 10, 0], [16, 6, 10, 6, "renderButtonDefault"], [16, 25, 10, 25], [16, 28, 10, 28, "props"], [16, 33, 10, 33], [16, 37, 10, 37], [16, 50, 10, 50], [16, 54, 10, 50, "_jsx"], [16, 69, 10, 54], [16, 71, 10, 55, "PlatformPressable"], [16, 98, 10, 72], [16, 100, 10, 74], [17, 4, 11, 2], [17, 7, 11, 5, "props"], [18, 2, 12, 0], [18, 3, 12, 1], [18, 4, 12, 2], [19, 2, 13, 0], [19, 6, 13, 6, "SUPPORTS_LARGE_CONTENT_VIEWER"], [19, 35, 13, 35], [19, 38, 13, 38, "Platform"], [19, 59, 13, 46], [19, 60, 13, 47, "OS"], [19, 62, 13, 49], [19, 67, 13, 54], [19, 72, 13, 59], [19, 76, 13, 63, "parseInt"], [19, 84, 13, 71], [19, 85, 13, 72, "Platform"], [19, 106, 13, 80], [19, 107, 13, 81, "Version"], [19, 114, 13, 88], [19, 116, 13, 90], [19, 118, 13, 92], [19, 119, 13, 93], [19, 123, 13, 97], [19, 125, 13, 99], [20, 2, 14, 7], [20, 11, 14, 16, "BottomTabItem"], [20, 24, 14, 29, "BottomTabItem"], [20, 25, 14, 29, "_ref"], [20, 29, 14, 29], [20, 31, 44, 3], [21, 4, 44, 3], [21, 8, 15, 2, "route"], [21, 13, 15, 7], [21, 16, 15, 7, "_ref"], [21, 20, 15, 7], [21, 21, 15, 2, "route"], [21, 26, 15, 7], [22, 6, 16, 2, "href"], [22, 10, 16, 6], [22, 13, 16, 6, "_ref"], [22, 17, 16, 6], [22, 18, 16, 2, "href"], [22, 22, 16, 6], [23, 6, 17, 2, "focused"], [23, 13, 17, 9], [23, 16, 17, 9, "_ref"], [23, 20, 17, 9], [23, 21, 17, 2, "focused"], [23, 28, 17, 9], [24, 6, 18, 2, "descriptor"], [24, 16, 18, 12], [24, 19, 18, 12, "_ref"], [24, 23, 18, 12], [24, 24, 18, 2, "descriptor"], [24, 34, 18, 12], [25, 6, 19, 2, "label"], [25, 11, 19, 7], [25, 14, 19, 7, "_ref"], [25, 18, 19, 7], [25, 19, 19, 2, "label"], [25, 24, 19, 7], [26, 6, 20, 2, "icon"], [26, 10, 20, 6], [26, 13, 20, 6, "_ref"], [26, 17, 20, 6], [26, 18, 20, 2, "icon"], [26, 22, 20, 6], [27, 6, 21, 2, "badge"], [27, 11, 21, 7], [27, 14, 21, 7, "_ref"], [27, 18, 21, 7], [27, 19, 21, 2, "badge"], [27, 24, 21, 7], [28, 6, 22, 2, "badgeStyle"], [28, 16, 22, 12], [28, 19, 22, 12, "_ref"], [28, 23, 22, 12], [28, 24, 22, 2, "badgeStyle"], [28, 34, 22, 12], [29, 6, 22, 12, "_ref$button"], [29, 17, 22, 12], [29, 20, 22, 12, "_ref"], [29, 24, 22, 12], [29, 25, 23, 2, "button"], [29, 31, 23, 8], [30, 6, 23, 2, "button"], [30, 12, 23, 8], [30, 15, 23, 8, "_ref$button"], [30, 26, 23, 8], [30, 40, 23, 11, "renderButtonDefault"], [30, 59, 23, 30], [30, 62, 23, 30, "_ref$button"], [30, 73, 23, 30], [31, 6, 24, 2, "accessibilityLabel"], [31, 24, 24, 20], [31, 27, 24, 20, "_ref"], [31, 31, 24, 20], [31, 32, 24, 2, "accessibilityLabel"], [31, 50, 24, 20], [32, 6, 25, 2, "testID"], [32, 12, 25, 8], [32, 15, 25, 8, "_ref"], [32, 19, 25, 8], [32, 20, 25, 2, "testID"], [32, 26, 25, 8], [33, 6, 26, 2, "onPress"], [33, 13, 26, 9], [33, 16, 26, 9, "_ref"], [33, 20, 26, 9], [33, 21, 26, 2, "onPress"], [33, 28, 26, 9], [34, 6, 27, 2, "onLongPress"], [34, 17, 27, 13], [34, 20, 27, 13, "_ref"], [34, 24, 27, 13], [34, 25, 27, 2, "onLongPress"], [34, 36, 27, 13], [35, 6, 28, 2, "horizontal"], [35, 16, 28, 12], [35, 19, 28, 12, "_ref"], [35, 23, 28, 12], [35, 24, 28, 2, "horizontal"], [35, 34, 28, 12], [36, 6, 29, 2, "compact"], [36, 13, 29, 9], [36, 16, 29, 9, "_ref"], [36, 20, 29, 9], [36, 21, 29, 2, "compact"], [36, 28, 29, 9], [37, 6, 30, 2, "sidebar"], [37, 13, 30, 9], [37, 16, 30, 9, "_ref"], [37, 20, 30, 9], [37, 21, 30, 2, "sidebar"], [37, 28, 30, 9], [38, 6, 31, 2, "variant"], [38, 13, 31, 9], [38, 16, 31, 9, "_ref"], [38, 20, 31, 9], [38, 21, 31, 2, "variant"], [38, 28, 31, 9], [39, 6, 32, 19, "customActiveTintColor"], [39, 27, 32, 40], [39, 30, 32, 40, "_ref"], [39, 34, 32, 40], [39, 35, 32, 2, "activeTintColor"], [39, 50, 32, 17], [40, 6, 33, 21, "customInactiveTintColor"], [40, 29, 33, 44], [40, 32, 33, 44, "_ref"], [40, 36, 33, 44], [40, 37, 33, 2, "inactiveTintColor"], [40, 54, 33, 19], [41, 6, 34, 25, "customActiveBackgroundColor"], [41, 33, 34, 52], [41, 36, 34, 52, "_ref"], [41, 40, 34, 52], [41, 41, 34, 2, "activeBackgroundColor"], [41, 62, 34, 23], [42, 6, 34, 23, "_ref$inactiveBackgrou"], [42, 27, 34, 23], [42, 30, 34, 23, "_ref"], [42, 34, 34, 23], [42, 35, 35, 2, "inactiveBackgroundColor"], [42, 58, 35, 25], [43, 6, 35, 2, "inactiveBackgroundColor"], [43, 29, 35, 25], [43, 32, 35, 25, "_ref$inactiveBackgrou"], [43, 53, 35, 25], [43, 67, 35, 28], [43, 80, 35, 41], [43, 83, 35, 41, "_ref$inactiveBackgrou"], [43, 104, 35, 41], [44, 6, 35, 41, "_ref$showLabel"], [44, 20, 35, 41], [44, 23, 35, 41, "_ref"], [44, 27, 35, 41], [44, 28, 36, 2, "showLabel"], [44, 37, 36, 11], [45, 6, 36, 2, "showLabel"], [45, 15, 36, 11], [45, 18, 36, 11, "_ref$showLabel"], [45, 32, 36, 11], [45, 46, 36, 14], [45, 50, 36, 18], [45, 53, 36, 18, "_ref$showLabel"], [45, 67, 36, 18], [46, 6, 36, 18, "_ref$allowFontScaling"], [46, 27, 36, 18], [46, 30, 36, 18, "_ref"], [46, 34, 36, 18], [46, 35, 40, 2, "allowFontScaling"], [46, 51, 40, 18], [47, 6, 40, 2, "allowFontScaling"], [47, 22, 40, 18], [47, 25, 40, 18, "_ref$allowFontScaling"], [47, 46, 40, 18], [47, 60, 40, 21, "SUPPORTS_LARGE_CONTENT_VIEWER"], [47, 89, 40, 50], [47, 92, 40, 53], [47, 97, 40, 58], [47, 100, 40, 61, "undefined"], [47, 109, 40, 70], [47, 112, 40, 70, "_ref$allowFontScaling"], [47, 133, 40, 70], [48, 6, 41, 2, "labelStyle"], [48, 16, 41, 12], [48, 19, 41, 12, "_ref"], [48, 23, 41, 12], [48, 24, 41, 2, "labelStyle"], [48, 34, 41, 12], [49, 6, 42, 2, "iconStyle"], [49, 15, 42, 11], [49, 18, 42, 11, "_ref"], [49, 22, 42, 11], [49, 23, 42, 2, "iconStyle"], [49, 32, 42, 11], [50, 6, 43, 2, "style"], [50, 11, 43, 7], [50, 14, 43, 7, "_ref"], [50, 18, 43, 7], [50, 19, 43, 2, "style"], [50, 24, 43, 7], [51, 4, 45, 2], [51, 8, 45, 2, "_useTheme"], [51, 17, 45, 2], [51, 20, 48, 6], [51, 24, 48, 6, "useTheme"], [51, 40, 48, 14], [51, 42, 48, 15], [51, 43, 48, 16], [52, 6, 46, 4, "colors"], [52, 12, 46, 10], [52, 15, 46, 10, "_useTheme"], [52, 24, 46, 10], [52, 25, 46, 4, "colors"], [52, 31, 46, 10], [53, 6, 47, 4, "fonts"], [53, 11, 47, 9], [53, 14, 47, 9, "_useTheme"], [53, 23, 47, 9], [53, 24, 47, 4, "fonts"], [53, 29, 47, 9], [54, 4, 49, 2], [54, 8, 49, 8, "activeTintColor"], [54, 23, 49, 23], [54, 26, 49, 26, "customActiveTintColor"], [54, 47, 49, 47], [54, 52, 49, 52, "variant"], [54, 59, 49, 59], [54, 64, 49, 64], [54, 71, 49, 71], [54, 75, 49, 75, "sidebar"], [54, 82, 49, 82], [54, 86, 49, 86, "horizontal"], [54, 96, 49, 96], [54, 99, 49, 99], [54, 103, 49, 99, "Color"], [54, 117, 49, 104], [54, 119, 49, 105, "colors"], [54, 125, 49, 111], [54, 126, 49, 112, "primary"], [54, 133, 49, 119], [54, 134, 49, 120], [54, 135, 49, 121, "isDark"], [54, 141, 49, 127], [54, 142, 49, 128], [54, 143, 49, 129], [54, 146, 49, 132], [54, 153, 49, 139], [54, 156, 49, 142], [54, 160, 49, 142, "Color"], [54, 174, 49, 147], [54, 176, 49, 148, "colors"], [54, 182, 49, 154], [54, 183, 49, 155, "primary"], [54, 190, 49, 162], [54, 191, 49, 163], [54, 192, 49, 164, "darken"], [54, 198, 49, 170], [54, 199, 49, 171], [54, 203, 49, 175], [54, 204, 49, 176], [54, 205, 49, 177, "string"], [54, 211, 49, 183], [54, 212, 49, 184], [54, 213, 49, 185], [54, 216, 49, 188, "colors"], [54, 222, 49, 194], [54, 223, 49, 195, "primary"], [54, 230, 49, 202], [54, 231, 49, 203], [55, 4, 50, 2], [55, 8, 50, 8, "inactiveTintColor"], [55, 25, 50, 25], [55, 28, 50, 28, "customInactiveTintColor"], [55, 51, 50, 51], [55, 56, 50, 56, "undefined"], [55, 65, 50, 65], [55, 68, 50, 68, "variant"], [55, 75, 50, 75], [55, 80, 50, 80], [55, 90, 50, 90], [55, 93, 50, 93], [55, 97, 50, 93, "Color"], [55, 111, 50, 98], [55, 113, 50, 99, "colors"], [55, 119, 50, 105], [55, 120, 50, 106, "text"], [55, 124, 50, 110], [55, 125, 50, 111], [55, 126, 50, 112, "alpha"], [55, 131, 50, 117], [55, 132, 50, 118], [55, 136, 50, 122], [55, 137, 50, 123], [55, 138, 50, 124, "rgb"], [55, 141, 50, 127], [55, 142, 50, 128], [55, 143, 50, 129], [55, 144, 50, 130, "string"], [55, 150, 50, 136], [55, 151, 50, 137], [55, 152, 50, 138], [55, 155, 50, 141], [55, 159, 50, 141, "Color"], [55, 173, 50, 146], [55, 175, 50, 147, "colors"], [55, 181, 50, 153], [55, 182, 50, 154, "text"], [55, 186, 50, 158], [55, 187, 50, 159], [55, 188, 50, 160, "mix"], [55, 191, 50, 163], [55, 192, 50, 164], [55, 196, 50, 164, "Color"], [55, 210, 50, 169], [55, 212, 50, 170, "colors"], [55, 218, 50, 176], [55, 219, 50, 177, "card"], [55, 223, 50, 181], [55, 224, 50, 182], [55, 226, 50, 184], [55, 229, 50, 187], [55, 230, 50, 188], [55, 231, 50, 189, "hex"], [55, 234, 50, 192], [55, 235, 50, 193], [55, 236, 50, 194], [55, 239, 50, 197, "customInactiveTintColor"], [55, 262, 50, 220], [56, 4, 51, 2], [56, 8, 51, 8, "activeBackgroundColor"], [56, 29, 51, 29], [56, 32, 51, 32, "customActiveBackgroundColor"], [56, 59, 51, 59], [56, 64, 51, 64, "variant"], [56, 71, 51, 71], [56, 76, 51, 76], [56, 86, 51, 86], [56, 89, 51, 89], [56, 93, 51, 89, "Color"], [56, 107, 51, 94], [56, 109, 51, 95, "activeTintColor"], [56, 124, 51, 110], [56, 125, 51, 111], [56, 126, 51, 112, "alpha"], [56, 131, 51, 117], [56, 132, 51, 118], [56, 136, 51, 122], [56, 137, 51, 123], [56, 138, 51, 124, "rgb"], [56, 141, 51, 127], [56, 142, 51, 128], [56, 143, 51, 129], [56, 144, 51, 130, "string"], [56, 150, 51, 136], [56, 151, 51, 137], [56, 152, 51, 138], [56, 155, 51, 141, "sidebar"], [56, 162, 51, 148], [56, 166, 51, 152, "horizontal"], [56, 176, 51, 162], [56, 179, 51, 165, "colors"], [56, 185, 51, 171], [56, 186, 51, 172, "primary"], [56, 193, 51, 179], [56, 196, 51, 182], [56, 209, 51, 195], [56, 210, 51, 196], [57, 4, 52, 2], [57, 8, 53, 4, "options"], [57, 15, 53, 11], [57, 18, 54, 6, "descriptor"], [57, 28, 54, 16], [57, 29, 53, 4, "options"], [57, 36, 53, 11], [58, 4, 55, 2], [58, 8, 55, 8, "labelString"], [58, 19, 55, 19], [58, 22, 55, 22], [58, 26, 55, 22, "get<PERSON><PERSON><PERSON>"], [58, 44, 55, 30], [58, 46, 55, 31], [59, 6, 56, 4, "label"], [59, 11, 56, 9], [59, 13, 56, 11], [59, 20, 56, 18, "options"], [59, 27, 56, 25], [59, 28, 56, 26, "tabBarLabel"], [59, 39, 56, 37], [59, 44, 56, 42], [59, 52, 56, 50], [59, 55, 56, 53, "options"], [59, 62, 56, 60], [59, 63, 56, 61, "tabBarLabel"], [59, 74, 56, 72], [59, 77, 56, 75, "undefined"], [59, 86, 56, 84], [60, 6, 57, 4, "title"], [60, 11, 57, 9], [60, 13, 57, 11, "options"], [60, 20, 57, 18], [60, 21, 57, 19, "title"], [61, 4, 58, 2], [61, 5, 58, 3], [61, 7, 58, 5, "route"], [61, 12, 58, 10], [61, 13, 58, 11, "name"], [61, 17, 58, 15], [61, 18, 58, 16], [62, 4, 59, 2], [62, 8, 59, 6, "labelInactiveTintColor"], [62, 30, 59, 28], [62, 33, 59, 31, "inactiveTintColor"], [62, 50, 59, 48], [63, 4, 60, 2], [63, 8, 60, 6, "iconInactiveTintColor"], [63, 29, 60, 27], [63, 32, 60, 30, "inactiveTintColor"], [63, 49, 60, 47], [64, 4, 61, 2], [64, 8, 61, 6, "variant"], [64, 15, 61, 13], [64, 20, 61, 18], [64, 27, 61, 25], [64, 31, 61, 29, "sidebar"], [64, 38, 61, 36], [64, 42, 61, 40, "horizontal"], [64, 52, 61, 50], [64, 56, 61, 54, "customInactiveTintColor"], [64, 79, 61, 77], [64, 84, 61, 82, "undefined"], [64, 93, 61, 91], [64, 95, 61, 93], [65, 6, 62, 4, "iconInactiveTintColor"], [65, 27, 62, 25], [65, 30, 62, 28, "colors"], [65, 36, 62, 34], [65, 37, 62, 35, "primary"], [65, 44, 62, 42], [66, 6, 63, 4, "labelInactiveTintColor"], [66, 28, 63, 26], [66, 31, 63, 29, "colors"], [66, 37, 63, 35], [66, 38, 63, 36, "text"], [66, 42, 63, 40], [67, 4, 64, 2], [68, 4, 65, 2], [68, 8, 65, 8, "renderLabel"], [68, 19, 65, 19], [68, 22, 65, 22, "_ref2"], [68, 27, 65, 22], [68, 31, 67, 8], [69, 6, 67, 8], [69, 10, 66, 4, "focused"], [69, 17, 66, 11], [69, 20, 66, 11, "_ref2"], [69, 25, 66, 11], [69, 26, 66, 4, "focused"], [69, 33, 66, 11], [70, 6, 68, 4], [70, 10, 68, 8, "showLabel"], [70, 19, 68, 17], [70, 24, 68, 22], [70, 29, 68, 27], [70, 31, 68, 29], [71, 8, 69, 6], [71, 15, 69, 13], [71, 19, 69, 17], [72, 6, 70, 4], [73, 6, 71, 4], [73, 10, 71, 10, "color"], [73, 15, 71, 15], [73, 18, 71, 18, "focused"], [73, 25, 71, 25], [73, 28, 71, 28, "activeTintColor"], [73, 43, 71, 43], [73, 46, 71, 46, "labelInactiveTintColor"], [73, 68, 71, 68], [74, 6, 72, 4], [74, 10, 72, 8], [74, 17, 72, 15, "label"], [74, 22, 72, 20], [74, 27, 72, 25], [74, 35, 72, 33], [74, 37, 72, 35], [75, 8, 73, 6], [75, 15, 73, 13, "label"], [75, 20, 73, 18], [75, 21, 73, 19], [76, 10, 74, 8, "focused"], [76, 17, 74, 15], [77, 10, 75, 8, "color"], [77, 15, 75, 13], [78, 10, 76, 8, "position"], [78, 18, 76, 16], [78, 20, 76, 18, "horizontal"], [78, 30, 76, 28], [78, 33, 76, 31], [78, 46, 76, 44], [78, 49, 76, 47], [78, 61, 76, 59], [79, 10, 77, 8, "children"], [79, 18, 77, 16], [79, 20, 77, 18, "labelString"], [80, 8, 78, 6], [80, 9, 78, 7], [80, 10, 78, 8], [81, 6, 79, 4], [82, 6, 80, 4], [82, 13, 80, 11], [82, 26, 80, 24], [82, 30, 80, 24, "_jsx"], [82, 45, 80, 28], [82, 47, 80, 29, "Label"], [82, 62, 80, 34], [82, 64, 80, 36], [83, 8, 81, 6, "style"], [83, 13, 81, 11], [83, 15, 81, 13], [83, 16, 81, 14, "horizontal"], [83, 26, 81, 24], [83, 29, 81, 27], [83, 30, 81, 28, "styles"], [83, 36, 81, 34], [83, 37, 81, 35, "labelBeside"], [83, 48, 81, 46], [83, 50, 81, 48, "variant"], [83, 57, 81, 55], [83, 62, 81, 60], [83, 72, 81, 70], [83, 75, 81, 73, "styles"], [83, 81, 81, 79], [83, 82, 81, 80, "labelSidebarMaterial"], [83, 102, 81, 100], [83, 105, 81, 103, "sidebar"], [83, 112, 81, 110], [83, 115, 81, 113, "styles"], [83, 121, 81, 119], [83, 122, 81, 120, "labelSidebarUiKit"], [83, 139, 81, 137], [83, 142, 81, 140, "compact"], [83, 149, 81, 147], [83, 152, 81, 150, "styles"], [83, 158, 81, 156], [83, 159, 81, 157, "labelBesideUikitCompact"], [83, 182, 81, 180], [83, 185, 81, 183, "styles"], [83, 191, 81, 189], [83, 192, 81, 190, "labelBesideUikit"], [83, 208, 81, 206], [83, 210, 81, 208, "icon"], [83, 214, 81, 212], [83, 218, 81, 216], [83, 222, 81, 220], [83, 226, 81, 224], [84, 10, 82, 8, "marginStart"], [84, 21, 82, 19], [84, 23, 82, 21], [85, 8, 83, 6], [85, 9, 83, 7], [85, 10, 83, 8], [85, 13, 83, 11, "styles"], [85, 19, 83, 17], [85, 20, 83, 18, "labelBeneath"], [85, 32, 83, 30], [85, 34, 83, 32, "compact"], [85, 41, 83, 39], [85, 45, 83, 43, "variant"], [85, 52, 83, 50], [85, 57, 83, 55], [85, 64, 83, 62], [85, 68, 83, 66, "sidebar"], [85, 75, 83, 73], [85, 79, 83, 77, "horizontal"], [85, 89, 83, 87], [85, 92, 83, 90, "fonts"], [85, 97, 83, 95], [85, 98, 83, 96, "regular"], [85, 105, 83, 103], [85, 108, 83, 106, "fonts"], [85, 113, 83, 111], [85, 114, 83, 112, "medium"], [85, 120, 83, 118], [85, 122, 83, 120, "labelStyle"], [85, 132, 83, 130], [85, 133, 83, 131], [86, 8, 84, 6, "allowFontScaling"], [86, 24, 84, 22], [86, 26, 84, 24, "allowFontScaling"], [86, 42, 84, 40], [87, 8, 85, 6, "tintColor"], [87, 17, 85, 15], [87, 19, 85, 17, "color"], [87, 24, 85, 22], [88, 8, 86, 6, "children"], [88, 16, 86, 14], [88, 18, 86, 16, "label"], [89, 6, 87, 4], [89, 7, 87, 5], [89, 8, 87, 6], [90, 4, 88, 2], [90, 5, 88, 3], [91, 4, 89, 2], [91, 8, 89, 8, "renderIcon"], [91, 18, 89, 18], [91, 21, 89, 21, "_ref3"], [91, 26, 89, 21], [91, 30, 91, 8], [92, 6, 91, 8], [92, 10, 90, 4, "focused"], [92, 17, 90, 11], [92, 20, 90, 11, "_ref3"], [92, 25, 90, 11], [92, 26, 90, 4, "focused"], [92, 33, 90, 11], [93, 6, 92, 4], [93, 10, 92, 8, "icon"], [93, 14, 92, 12], [93, 19, 92, 17, "undefined"], [93, 28, 92, 26], [93, 30, 92, 28], [94, 8, 93, 6], [94, 15, 93, 13], [94, 19, 93, 17], [95, 6, 94, 4], [96, 6, 95, 4], [96, 10, 95, 10, "activeOpacity"], [96, 23, 95, 23], [96, 26, 95, 26, "focused"], [96, 33, 95, 33], [96, 36, 95, 36], [96, 37, 95, 37], [96, 40, 95, 40], [96, 41, 95, 41], [97, 6, 96, 4], [97, 10, 96, 10, "inactiveOpacity"], [97, 25, 96, 25], [97, 28, 96, 28, "focused"], [97, 35, 96, 35], [97, 38, 96, 38], [97, 39, 96, 39], [97, 42, 96, 42], [97, 43, 96, 43], [98, 6, 97, 4], [98, 13, 97, 11], [98, 26, 97, 24], [98, 30, 97, 24, "_jsx"], [98, 45, 97, 28], [98, 47, 97, 29, "TabBarIcon"], [98, 69, 97, 39], [98, 71, 97, 41], [99, 8, 98, 6, "route"], [99, 13, 98, 11], [99, 15, 98, 13, "route"], [99, 20, 98, 18], [100, 8, 99, 6, "variant"], [100, 15, 99, 13], [100, 17, 99, 15, "variant"], [100, 24, 99, 22], [101, 8, 100, 6, "size"], [101, 12, 100, 10], [101, 14, 100, 12, "compact"], [101, 21, 100, 19], [101, 24, 100, 22], [101, 33, 100, 31], [101, 36, 100, 34], [101, 45, 100, 43], [102, 8, 101, 6, "badge"], [102, 13, 101, 11], [102, 15, 101, 13, "badge"], [102, 20, 101, 18], [103, 8, 102, 6, "badgeStyle"], [103, 18, 102, 16], [103, 20, 102, 18, "badgeStyle"], [103, 30, 102, 28], [104, 8, 103, 6, "activeOpacity"], [104, 21, 103, 19], [104, 23, 103, 21, "activeOpacity"], [104, 36, 103, 34], [105, 8, 104, 6, "allowFontScaling"], [105, 24, 104, 22], [105, 26, 104, 24, "allowFontScaling"], [105, 42, 104, 40], [106, 8, 105, 6, "inactiveOpacity"], [106, 23, 105, 21], [106, 25, 105, 23, "inactiveOpacity"], [106, 40, 105, 38], [107, 8, 106, 6, "activeTintColor"], [107, 23, 106, 21], [107, 25, 106, 23, "activeTintColor"], [107, 40, 106, 38], [108, 8, 107, 6, "inactiveTintColor"], [108, 25, 107, 23], [108, 27, 107, 25, "iconInactiveTintColor"], [108, 48, 107, 46], [109, 8, 108, 6, "renderIcon"], [109, 18, 108, 16], [109, 20, 108, 18, "icon"], [109, 24, 108, 22], [110, 8, 109, 6, "style"], [110, 13, 109, 11], [110, 15, 109, 13, "iconStyle"], [111, 6, 110, 4], [111, 7, 110, 5], [111, 8, 110, 6], [112, 4, 111, 2], [112, 5, 111, 3], [113, 4, 112, 2], [113, 8, 112, 8, "scene"], [113, 13, 112, 13], [113, 16, 112, 16], [114, 6, 113, 4, "route"], [114, 11, 113, 9], [115, 6, 114, 4, "focused"], [116, 4, 115, 2], [116, 5, 115, 3], [117, 4, 116, 2], [117, 8, 116, 8, "backgroundColor"], [117, 23, 116, 23], [117, 26, 116, 26, "focused"], [117, 33, 116, 33], [117, 36, 116, 36, "activeBackgroundColor"], [117, 57, 116, 57], [117, 60, 116, 60, "inactiveBackgroundColor"], [117, 83, 116, 83], [118, 4, 117, 2], [118, 8, 117, 2, "_StyleSheet$flatten"], [118, 27, 117, 2], [118, 30, 119, 6, "StyleSheet"], [118, 53, 119, 16], [118, 54, 119, 17, "flatten"], [118, 61, 119, 24], [118, 62, 119, 25, "style"], [118, 67, 119, 30], [118, 71, 119, 34], [118, 72, 119, 35], [118, 73, 119, 36], [118, 74, 119, 37], [119, 6, 118, 4, "flex"], [119, 10, 118, 8], [119, 13, 118, 8, "_StyleSheet$flatten"], [119, 32, 118, 8], [119, 33, 118, 4, "flex"], [119, 37, 118, 8], [120, 4, 120, 2], [120, 8, 120, 8, "borderRadius"], [120, 20, 120, 20], [120, 23, 120, 23, "variant"], [120, 30, 120, 30], [120, 35, 120, 35], [120, 45, 120, 45], [120, 48, 120, 48, "horizontal"], [120, 58, 120, 58], [120, 61, 120, 61], [120, 63, 120, 63], [120, 66, 120, 66], [120, 68, 120, 68], [120, 71, 120, 71, "sidebar"], [120, 78, 120, 78], [120, 82, 120, 82, "horizontal"], [120, 92, 120, 92], [120, 95, 120, 95], [120, 97, 120, 97], [120, 100, 120, 100], [120, 101, 120, 101], [121, 4, 121, 2], [121, 11, 121, 9], [121, 24, 121, 22], [121, 28, 121, 22, "_jsx"], [121, 43, 121, 26], [121, 45, 121, 27, "View"], [121, 62, 121, 31], [121, 64, 121, 33], [122, 6, 122, 4, "style"], [122, 11, 122, 9], [122, 13, 122, 11], [123, 6, 123, 4], [124, 6, 124, 4], [125, 8, 125, 6, "borderRadius"], [125, 20, 125, 18], [126, 8, 126, 6, "overflow"], [126, 16, 126, 14], [126, 18, 126, 16, "variant"], [126, 25, 126, 23], [126, 30, 126, 28], [126, 40, 126, 38], [126, 43, 126, 41], [126, 51, 126, 49], [126, 54, 126, 52], [127, 6, 127, 4], [127, 7, 127, 5], [127, 9, 127, 7, "style"], [127, 14, 127, 12], [127, 15, 127, 13], [128, 6, 128, 4, "children"], [128, 14, 128, 12], [128, 16, 128, 14, "button"], [128, 22, 128, 20], [128, 23, 128, 21], [129, 8, 129, 6, "href"], [129, 12, 129, 10], [130, 8, 130, 6, "onPress"], [130, 15, 130, 13], [131, 8, 131, 6, "onLongPress"], [131, 19, 131, 17], [132, 8, 132, 6, "testID"], [132, 14, 132, 12], [133, 8, 133, 6], [133, 20, 133, 18], [133, 22, 133, 20, "accessibilityLabel"], [133, 40, 133, 38], [134, 8, 134, 6], [134, 40, 134, 38], [134, 42, 134, 40, "labelString"], [134, 53, 134, 51], [135, 8, 135, 6], [135, 46, 135, 44], [135, 48, 135, 46], [135, 52, 135, 50], [136, 8, 136, 6], [137, 8, 137, 6], [137, 14, 137, 12], [137, 16, 137, 14, "Platform"], [137, 37, 137, 22], [137, 38, 137, 23, "select"], [137, 44, 137, 29], [137, 45, 137, 30], [138, 10, 138, 8, "ios"], [138, 13, 138, 11], [138, 15, 138, 13], [138, 23, 138, 21], [139, 10, 139, 8, "default"], [139, 17, 139, 15], [139, 19, 139, 17], [140, 8, 140, 6], [140, 9, 140, 7], [140, 10, 140, 8], [141, 8, 141, 6], [141, 23, 141, 21], [141, 25, 141, 23, "focused"], [141, 32, 141, 30], [142, 8, 142, 6], [142, 24, 142, 22], [142, 26, 142, 24], [143, 10, 143, 8, "borderless"], [143, 20, 143, 18], [143, 22, 143, 20], [144, 8, 144, 6], [144, 9, 144, 7], [145, 8, 145, 6], [145, 21, 145, 19], [145, 23, 145, 21, "variant"], [145, 30, 145, 28], [145, 35, 145, 33], [145, 45, 145, 43], [145, 49, 145, 47, "sidebar"], [145, 56, 145, 54], [145, 60, 145, 58, "horizontal"], [145, 70, 145, 68], [145, 73, 145, 71], [146, 10, 146, 8, "color"], [146, 15, 146, 13], [146, 17, 146, 15, "colors"], [146, 23, 146, 21], [146, 24, 146, 22, "text"], [147, 8, 147, 6], [147, 9, 147, 7], [147, 12, 147, 10, "undefined"], [147, 21, 147, 19], [148, 8, 148, 6], [148, 22, 148, 20], [148, 24, 148, 22], [148, 25, 148, 23], [149, 8, 149, 6], [149, 15, 149, 13], [149, 17, 149, 15], [149, 18, 149, 16, "styles"], [149, 24, 149, 22], [149, 25, 149, 23, "tab"], [149, 28, 149, 26], [149, 30, 149, 28], [150, 10, 150, 8, "flex"], [150, 14, 150, 12], [151, 10, 151, 8, "backgroundColor"], [151, 25, 151, 23], [152, 10, 152, 8, "borderRadius"], [153, 8, 153, 6], [153, 9, 153, 7], [153, 11, 153, 9, "sidebar"], [153, 18, 153, 16], [153, 21, 153, 19, "variant"], [153, 28, 153, 26], [153, 33, 153, 31], [153, 43, 153, 41], [153, 46, 153, 44, "horizontal"], [153, 56, 153, 54], [153, 59, 153, 57, "styles"], [153, 65, 153, 63], [153, 66, 153, 64, "tabBarSidebarMaterial"], [153, 87, 153, 85], [153, 90, 153, 88, "styles"], [153, 96, 153, 94], [153, 97, 153, 95, "tabVerticalMaterial"], [153, 116, 153, 114], [153, 119, 153, 117, "horizontal"], [153, 129, 153, 127], [153, 132, 153, 130, "styles"], [153, 138, 153, 136], [153, 139, 153, 137, "tabBarSidebarUiKit"], [153, 157, 153, 155], [153, 160, 153, 158, "styles"], [153, 166, 153, 164], [153, 167, 153, 165, "tabVerticalUiKit"], [153, 183, 153, 181], [153, 186, 153, 184, "variant"], [153, 193, 153, 191], [153, 198, 153, 196], [153, 208, 153, 206], [153, 211, 153, 209, "styles"], [153, 217, 153, 215], [153, 218, 153, 216, "tabVerticalMaterial"], [153, 237, 153, 235], [153, 240, 153, 238, "horizontal"], [153, 250, 153, 248], [153, 253, 153, 251, "styles"], [153, 259, 153, 257], [153, 260, 153, 258, "tabHorizontalUiKit"], [153, 278, 153, 276], [153, 281, 153, 279, "styles"], [153, 287, 153, 285], [153, 288, 153, 286, "tabVerticalUiKit"], [153, 304, 153, 302], [153, 305, 153, 303], [154, 8, 154, 6], [154, 18, 154, 16], [154, 20, 154, 18], [154, 33, 154, 31], [154, 37, 154, 31, "_jsxs"], [154, 53, 154, 36], [154, 55, 154, 37, "React"], [154, 69, 154, 42], [154, 70, 154, 43, "Fragment"], [154, 78, 154, 51], [154, 80, 154, 53], [155, 10, 155, 8, "children"], [155, 18, 155, 16], [155, 20, 155, 18], [155, 21, 155, 19, "renderIcon"], [155, 31, 155, 29], [155, 32, 155, 30, "scene"], [155, 37, 155, 35], [155, 38, 155, 36], [155, 40, 155, 38, "renderLabel"], [155, 51, 155, 49], [155, 52, 155, 50, "scene"], [155, 57, 155, 55], [155, 58, 155, 56], [156, 8, 156, 6], [156, 9, 156, 7], [157, 6, 157, 4], [157, 7, 157, 5], [158, 4, 158, 2], [158, 5, 158, 3], [158, 6, 158, 4], [159, 2, 159, 0], [160, 2, 160, 0], [160, 6, 160, 6, "styles"], [160, 12, 160, 12], [160, 15, 160, 15, "StyleSheet"], [160, 38, 160, 25], [160, 39, 160, 26, "create"], [160, 45, 160, 32], [160, 46, 160, 33], [161, 4, 161, 2, "tab"], [161, 7, 161, 5], [161, 9, 161, 7], [162, 6, 162, 4, "alignItems"], [162, 16, 162, 14], [162, 18, 162, 16], [162, 26, 162, 24], [163, 6, 163, 4], [164, 6, 164, 4, "borderRadius"], [164, 18, 164, 16], [164, 20, 164, 18], [165, 4, 165, 2], [165, 5, 165, 3], [166, 4, 166, 2, "tabVerticalUiKit"], [166, 20, 166, 18], [166, 22, 166, 20], [167, 6, 167, 4, "justifyContent"], [167, 20, 167, 18], [167, 22, 167, 20], [167, 34, 167, 32], [168, 6, 168, 4, "flexDirection"], [168, 19, 168, 17], [168, 21, 168, 19], [168, 29, 168, 27], [169, 6, 169, 4, "padding"], [169, 13, 169, 11], [169, 15, 169, 13], [170, 4, 170, 2], [170, 5, 170, 3], [171, 4, 171, 2, "tabVerticalMaterial"], [171, 23, 171, 21], [171, 25, 171, 23], [172, 6, 172, 4, "padding"], [172, 13, 172, 11], [172, 15, 172, 13], [173, 4, 173, 2], [173, 5, 173, 3], [174, 4, 174, 2, "tabHorizontalUiKit"], [174, 22, 174, 20], [174, 24, 174, 22], [175, 6, 175, 4, "justifyContent"], [175, 20, 175, 18], [175, 22, 175, 20], [175, 30, 175, 28], [176, 6, 176, 4, "alignItems"], [176, 16, 176, 14], [176, 18, 176, 16], [176, 26, 176, 24], [177, 6, 177, 4, "flexDirection"], [177, 19, 177, 17], [177, 21, 177, 19], [177, 26, 177, 24], [178, 6, 178, 4, "padding"], [178, 13, 178, 11], [178, 15, 178, 13], [179, 4, 179, 2], [179, 5, 179, 3], [180, 4, 180, 2, "tabBarSidebarUiKit"], [180, 22, 180, 20], [180, 24, 180, 22], [181, 6, 181, 4, "justifyContent"], [181, 20, 181, 18], [181, 22, 181, 20], [181, 34, 181, 32], [182, 6, 182, 4, "alignItems"], [182, 16, 182, 14], [182, 18, 182, 16], [182, 26, 182, 24], [183, 6, 183, 4, "flexDirection"], [183, 19, 183, 17], [183, 21, 183, 19], [183, 26, 183, 24], [184, 6, 184, 4, "paddingVertical"], [184, 21, 184, 19], [184, 23, 184, 21], [184, 24, 184, 22], [185, 6, 185, 4, "paddingHorizontal"], [185, 23, 185, 21], [185, 25, 185, 23], [186, 4, 186, 2], [186, 5, 186, 3], [187, 4, 187, 2, "tabBarSidebarMaterial"], [187, 25, 187, 23], [187, 27, 187, 25], [188, 6, 188, 4, "justifyContent"], [188, 20, 188, 18], [188, 22, 188, 20], [188, 34, 188, 32], [189, 6, 189, 4, "alignItems"], [189, 16, 189, 14], [189, 18, 189, 16], [189, 26, 189, 24], [190, 6, 190, 4, "flexDirection"], [190, 19, 190, 17], [190, 21, 190, 19], [190, 26, 190, 24], [191, 6, 191, 4, "paddingVertical"], [191, 21, 191, 19], [191, 23, 191, 21], [191, 25, 191, 23], [192, 6, 192, 4, "paddingStart"], [192, 18, 192, 16], [192, 20, 192, 18], [192, 22, 192, 20], [193, 6, 193, 4, "paddingEnd"], [193, 16, 193, 14], [193, 18, 193, 16], [194, 4, 194, 2], [194, 5, 194, 3], [195, 4, 195, 2, "labelSidebarMaterial"], [195, 24, 195, 22], [195, 26, 195, 24], [196, 6, 196, 4, "marginStart"], [196, 17, 196, 15], [196, 19, 196, 17], [197, 4, 197, 2], [197, 5, 197, 3], [198, 4, 198, 2, "labelSidebarUiKit"], [198, 21, 198, 19], [198, 23, 198, 21], [199, 6, 199, 4, "fontSize"], [199, 14, 199, 12], [199, 16, 199, 14], [199, 18, 199, 16], [200, 6, 200, 4, "marginStart"], [200, 17, 200, 15], [200, 19, 200, 17], [201, 4, 201, 2], [201, 5, 201, 3], [202, 4, 202, 2, "labelBeneath"], [202, 16, 202, 14], [202, 18, 202, 16], [203, 6, 203, 4, "fontSize"], [203, 14, 203, 12], [203, 16, 203, 14], [204, 4, 204, 2], [204, 5, 204, 3], [205, 4, 205, 2, "labelBeside"], [205, 15, 205, 13], [205, 17, 205, 15], [206, 6, 206, 4, "marginEnd"], [206, 15, 206, 13], [206, 17, 206, 15], [206, 19, 206, 17], [207, 6, 207, 4, "lineHeight"], [207, 16, 207, 14], [207, 18, 207, 16], [208, 4, 208, 2], [208, 5, 208, 3], [209, 4, 209, 2, "labelBesideUikit"], [209, 20, 209, 18], [209, 22, 209, 20], [210, 6, 210, 4, "fontSize"], [210, 14, 210, 12], [210, 16, 210, 14], [210, 18, 210, 16], [211, 6, 211, 4, "marginStart"], [211, 17, 211, 15], [211, 19, 211, 17], [212, 4, 212, 2], [212, 5, 212, 3], [213, 4, 213, 2, "labelBesideUikitCompact"], [213, 27, 213, 25], [213, 29, 213, 27], [214, 6, 214, 4, "fontSize"], [214, 14, 214, 12], [214, 16, 214, 14], [214, 18, 214, 16], [215, 6, 215, 4, "marginStart"], [215, 17, 215, 15], [215, 19, 215, 17], [216, 4, 216, 2], [217, 2, 217, 0], [217, 3, 217, 1], [217, 4, 217, 2], [218, 0, 217, 3], [218, 3]], "functionMap": {"names": ["<global>", "renderButtonDefault", "BottomTabItem", "renderLabel", "renderIcon"], "mappings": "AAA;4BCS;EDE;OEE;sBCmD;GDuB;qBEC;GFsB;CFgD"}}, "type": "js/module"}]}