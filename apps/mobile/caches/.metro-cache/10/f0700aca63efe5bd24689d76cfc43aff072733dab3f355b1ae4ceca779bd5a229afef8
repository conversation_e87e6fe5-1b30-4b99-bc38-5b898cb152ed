{"dependencies": [{"name": "./setUpGlobals", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 25}}], "key": "SQCsVIwDIXKOX+4sfYBj1GfIQdU=", "exportNames": ["*"]}}, {"name": "../../src/private/setup/setUpDOM", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 43}}], "key": "QNKP08YQHVQCIIIRokh8pJ9YcHQ=", "exportNames": ["*"]}}, {"name": "./setUpPerformance", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 29}}], "key": "ASkYadjOSbuPNDriKF4HxezvGuc=", "exportNames": ["*"]}}, {"name": "./polyfillPromise", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 28}}], "key": "fsKXXBRFk34xUdP/u5ZXjMPjKmw=", "exportNames": ["*"]}}, {"name": "./setUpTimers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 24}}], "key": "hTnJQn3dpfTQ+3R/wSQ/UsCi3JQ=", "exportNames": ["*"]}}, {"name": "./setUpReactDevTools", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 33}}], "key": "7j5phEkronIreG4llfRCeTJK1Yk=", "exportNames": ["*"]}}, {"name": "./setUpErrorHandling", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 31}}], "key": "uzKX1ZGtdJQ3BrmP45Yq/VhTOGo=", "exportNames": ["*"]}}, {"name": "./setUpRegeneratorRuntime", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 36}}], "key": "OUy6q/sDbSbNKeInrZSN/NJaIY0=", "exportNames": ["*"]}}, {"name": "./setUpXHR", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 21}}], "key": "ZvpUn+yUO9l0X2eJe5z9zBl9blM=", "exportNames": ["*"]}}, {"name": "./setUpAlert", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 23}}], "key": "7hSD23yiCfHTmtIygdlkFUW7CtE=", "exportNames": ["*"]}}, {"name": "./setUpNavigator", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 27}}], "key": "9Z2fkH9t8zv5JVmggYKx9lbviyg=", "exportNames": ["*"]}}, {"name": "./setUpBatchedBridge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 31}}], "key": "4cbEYj9pMpUPAIHN68rQqRrL6Ds=", "exportNames": ["*"]}}, {"name": "./setUpSegmentFetcher", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 32}}], "key": "5xiZqvu7JsofrxS1e8Xw+HBPju0=", "exportNames": ["*"]}}, {"name": "./checkNativeVersion", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 33}}], "key": "gBV4UXzbOWCks2ytdG6rk9L1WZ4=", "exportNames": ["*"]}}, {"name": "./setUpDeveloperTools", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 34}}], "key": "uknUB/XbPci+wEFgKQw5HpvM3jc=", "exportNames": ["*"]}}, {"name": "../LogBox/LogBox", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 29}}], "key": "zTwdk7dC9GxByr2WsxrMuO9pgw4=", "exportNames": ["*"]}}, {"name": "../ReactNative/AppRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 37}}], "key": "nTzxDvkYcLQ5Rl1pC4ndln5vLxc=", "exportNames": ["*"]}}, {"name": "../Utilities/GlobalPerformanceLogger", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 49}}], "key": "JKbg23XAxLNXfrvEguzquaLJPJQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n  'use strict';\n\n  var start = Date.now();\n  require(_dependencyMap[0], \"./setUpGlobals\");\n  require(_dependencyMap[1], \"../../src/private/setup/setUpDOM\").default();\n  require(_dependencyMap[2], \"./setUpPerformance\");\n  require(_dependencyMap[3], \"./polyfillPromise\");\n  require(_dependencyMap[4], \"./setUpTimers\");\n  if (__DEV__) {\n    require(_dependencyMap[5], \"./setUpReactDevTools\");\n  }\n  require(_dependencyMap[6], \"./setUpErrorHandling\");\n  require(_dependencyMap[7], \"./setUpRegeneratorRuntime\");\n  require(_dependencyMap[8], \"./setUpXHR\");\n  require(_dependencyMap[9], \"./setUpAlert\");\n  require(_dependencyMap[10], \"./setUpNavigator\");\n  require(_dependencyMap[11], \"./setUpBatchedBridge\");\n  require(_dependencyMap[12], \"./setUpSegmentFetcher\");\n  if (__DEV__) {\n    require(_dependencyMap[13], \"./checkNativeVersion\");\n    require(_dependencyMap[14], \"./setUpDeveloperTools\");\n    require(_dependencyMap[15], \"../LogBox/LogBox\").default.install();\n  }\n  require(_dependencyMap[16], \"../ReactNative/AppRegistry\");\n  var GlobalPerformanceLogger = require(_dependencyMap[17], \"../Utilities/GlobalPerformanceLogger\").default;\n  GlobalPerformanceLogger.markPoint('initializeCore_start', GlobalPerformanceLogger.currentTimestamp() - (Date.now() - start));\n  GlobalPerformanceLogger.markPoint('initializeCore_end');\n});", "lineCount": 30, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [3, 2, 27, 0], [3, 14, 27, 12], [5, 2, 29, 0], [5, 6, 29, 6, "start"], [5, 11, 29, 11], [5, 14, 29, 14, "Date"], [5, 18, 29, 18], [5, 19, 29, 19, "now"], [5, 22, 29, 22], [5, 23, 29, 23], [5, 24, 29, 24], [6, 2, 31, 0, "require"], [6, 9, 31, 7], [6, 10, 31, 7, "_dependencyMap"], [6, 24, 31, 7], [6, 45, 31, 24], [6, 46, 31, 25], [7, 2, 32, 0, "require"], [7, 9, 32, 7], [7, 10, 32, 7, "_dependencyMap"], [7, 24, 32, 7], [7, 63, 32, 42], [7, 64, 32, 43], [7, 65, 32, 44, "default"], [7, 72, 32, 51], [7, 73, 32, 52], [7, 74, 32, 53], [8, 2, 33, 0, "require"], [8, 9, 33, 7], [8, 10, 33, 7, "_dependencyMap"], [8, 24, 33, 7], [8, 49, 33, 28], [8, 50, 33, 29], [9, 2, 34, 0, "require"], [9, 9, 34, 7], [9, 10, 34, 7, "_dependencyMap"], [9, 24, 34, 7], [9, 48, 34, 27], [9, 49, 34, 28], [10, 2, 35, 0, "require"], [10, 9, 35, 7], [10, 10, 35, 7, "_dependencyMap"], [10, 24, 35, 7], [10, 44, 35, 23], [10, 45, 35, 24], [11, 2, 36, 0], [11, 6, 36, 4, "__DEV__"], [11, 13, 36, 11], [11, 15, 36, 13], [12, 4, 37, 2, "require"], [12, 11, 37, 9], [12, 12, 37, 9, "_dependencyMap"], [12, 26, 37, 9], [12, 53, 37, 32], [12, 54, 37, 33], [13, 2, 38, 0], [14, 2, 39, 0, "require"], [14, 9, 39, 7], [14, 10, 39, 7, "_dependencyMap"], [14, 24, 39, 7], [14, 51, 39, 30], [14, 52, 39, 31], [15, 2, 40, 0, "require"], [15, 9, 40, 7], [15, 10, 40, 7, "_dependencyMap"], [15, 24, 40, 7], [15, 56, 40, 35], [15, 57, 40, 36], [16, 2, 41, 0, "require"], [16, 9, 41, 7], [16, 10, 41, 7, "_dependencyMap"], [16, 24, 41, 7], [16, 41, 41, 20], [16, 42, 41, 21], [17, 2, 42, 0, "require"], [17, 9, 42, 7], [17, 10, 42, 7, "_dependencyMap"], [17, 24, 42, 7], [17, 43, 42, 22], [17, 44, 42, 23], [18, 2, 43, 0, "require"], [18, 9, 43, 7], [18, 10, 43, 7, "_dependencyMap"], [18, 24, 43, 7], [18, 48, 43, 26], [18, 49, 43, 27], [19, 2, 44, 0, "require"], [19, 9, 44, 7], [19, 10, 44, 7, "_dependencyMap"], [19, 24, 44, 7], [19, 52, 44, 30], [19, 53, 44, 31], [20, 2, 45, 0, "require"], [20, 9, 45, 7], [20, 10, 45, 7, "_dependencyMap"], [20, 24, 45, 7], [20, 53, 45, 31], [20, 54, 45, 32], [21, 2, 46, 0], [21, 6, 46, 4, "__DEV__"], [21, 13, 46, 11], [21, 15, 46, 13], [22, 4, 47, 2, "require"], [22, 11, 47, 9], [22, 12, 47, 9, "_dependencyMap"], [22, 26, 47, 9], [22, 54, 47, 32], [22, 55, 47, 33], [23, 4, 48, 2, "require"], [23, 11, 48, 9], [23, 12, 48, 9, "_dependencyMap"], [23, 26, 48, 9], [23, 55, 48, 33], [23, 56, 48, 34], [24, 4, 49, 2, "require"], [24, 11, 49, 9], [24, 12, 49, 9, "_dependencyMap"], [24, 26, 49, 9], [24, 50, 49, 28], [24, 51, 49, 29], [24, 52, 49, 30, "default"], [24, 59, 49, 37], [24, 60, 49, 38, "install"], [24, 67, 49, 45], [24, 68, 49, 46], [24, 69, 49, 47], [25, 2, 50, 0], [26, 2, 52, 0, "require"], [26, 9, 52, 7], [26, 10, 52, 7, "_dependencyMap"], [26, 24, 52, 7], [26, 58, 52, 36], [26, 59, 52, 37], [27, 2, 54, 0], [27, 6, 54, 6, "GlobalPerformanceLogger"], [27, 29, 54, 29], [27, 32, 55, 2, "require"], [27, 39, 55, 9], [27, 40, 55, 9, "_dependencyMap"], [27, 54, 55, 9], [27, 98, 55, 48], [27, 99, 55, 49], [27, 100, 55, 50, "default"], [27, 107, 55, 57], [28, 2, 59, 0, "GlobalPerformanceLogger"], [28, 25, 59, 23], [28, 26, 59, 24, "markPoint"], [28, 35, 59, 33], [28, 36, 60, 2], [28, 58, 60, 24], [28, 60, 61, 2, "GlobalPerformanceLogger"], [28, 83, 61, 25], [28, 84, 61, 26, "currentTimestamp"], [28, 100, 61, 42], [28, 101, 61, 43], [28, 102, 61, 44], [28, 106, 61, 48, "Date"], [28, 110, 61, 52], [28, 111, 61, 53, "now"], [28, 114, 61, 56], [28, 115, 61, 57], [28, 116, 61, 58], [28, 119, 61, 61, "start"], [28, 124, 61, 66], [28, 125, 62, 0], [28, 126, 62, 1], [29, 2, 63, 0, "GlobalPerformanceLogger"], [29, 25, 63, 23], [29, 26, 63, 24, "markPoint"], [29, 35, 63, 33], [29, 36, 63, 34], [29, 56, 63, 54], [29, 57, 63, 55], [30, 0, 63, 56], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}