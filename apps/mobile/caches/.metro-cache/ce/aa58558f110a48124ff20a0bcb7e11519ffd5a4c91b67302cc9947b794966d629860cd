{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /*\n  object-assign\n  (c) <PERSON><PERSON> Sorhus\n  @license MIT\n  */\n\n  'use strict';\n\n  /* eslint-disable no-unused-vars */\n  var getOwnPropertySymbols = Object.getOwnPropertySymbols;\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  var propIsEnumerable = Object.prototype.propertyIsEnumerable;\n  function toObject(val) {\n    if (val === null || val === undefined) {\n      throw new TypeError('Object.assign cannot be called with null or undefined');\n    }\n    return Object(val);\n  }\n  function shouldUseNative() {\n    try {\n      if (!Object.assign) {\n        return false;\n      }\n\n      // Detect buggy property enumeration order in older V8 versions.\n\n      // https://bugs.chromium.org/p/v8/issues/detail?id=4118\n      var test1 = new String('abc'); // eslint-disable-line no-new-wrappers\n      test1[5] = 'de';\n      if (Object.getOwnPropertyNames(test1)[0] === '5') {\n        return false;\n      }\n\n      // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n      var test2 = {};\n      for (var i = 0; i < 10; i++) {\n        test2['_' + String.fromCharCode(i)] = i;\n      }\n      var order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n        return test2[n];\n      });\n      if (order2.join('') !== '0123456789') {\n        return false;\n      }\n\n      // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n      var test3 = {};\n      'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n        test3[letter] = letter;\n      });\n      if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {\n        return false;\n      }\n      return true;\n    } catch (err) {\n      // We don't expect any of the above to throw, but better to be safe.\n      return false;\n    }\n  }\n  module.exports = shouldUseNative() ? Object.assign : function (target, source) {\n    var from;\n    var to = toObject(target);\n    var symbols;\n    for (var s = 1; s < arguments.length; s++) {\n      from = Object(arguments[s]);\n      for (var key in from) {\n        if (hasOwnProperty.call(from, key)) {\n          to[key] = from[key];\n        }\n      }\n      if (getOwnPropertySymbols) {\n        symbols = getOwnPropertySymbols(from);\n        for (var i = 0; i < symbols.length; i++) {\n          if (propIsEnumerable.call(from, symbols[i])) {\n            to[symbols[i]] = from[symbols[i]];\n          }\n        }\n      }\n    }\n    return to;\n  };\n});", "lineCount": 83, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [8, 2, 7, 0], [8, 14, 7, 12], [10, 2, 8, 0], [11, 2, 9, 0], [11, 6, 9, 4, "getOwnPropertySymbols"], [11, 27, 9, 25], [11, 30, 9, 28, "Object"], [11, 36, 9, 34], [11, 37, 9, 35, "getOwnPropertySymbols"], [11, 58, 9, 56], [12, 2, 10, 0], [12, 6, 10, 4, "hasOwnProperty"], [12, 20, 10, 18], [12, 23, 10, 21, "Object"], [12, 29, 10, 27], [12, 30, 10, 28, "prototype"], [12, 39, 10, 37], [12, 40, 10, 38, "hasOwnProperty"], [12, 54, 10, 52], [13, 2, 11, 0], [13, 6, 11, 4, "propIsEnumerable"], [13, 22, 11, 20], [13, 25, 11, 23, "Object"], [13, 31, 11, 29], [13, 32, 11, 30, "prototype"], [13, 41, 11, 39], [13, 42, 11, 40, "propertyIsEnumerable"], [13, 62, 11, 60], [14, 2, 13, 0], [14, 11, 13, 9, "toObject"], [14, 19, 13, 17, "toObject"], [14, 20, 13, 18, "val"], [14, 23, 13, 21], [14, 25, 13, 23], [15, 4, 14, 1], [15, 8, 14, 5, "val"], [15, 11, 14, 8], [15, 16, 14, 13], [15, 20, 14, 17], [15, 24, 14, 21, "val"], [15, 27, 14, 24], [15, 32, 14, 29, "undefined"], [15, 41, 14, 38], [15, 43, 14, 40], [16, 6, 15, 2], [16, 12, 15, 8], [16, 16, 15, 12, "TypeError"], [16, 25, 15, 21], [16, 26, 15, 22], [16, 81, 15, 77], [16, 82, 15, 78], [17, 4, 16, 1], [18, 4, 18, 1], [18, 11, 18, 8, "Object"], [18, 17, 18, 14], [18, 18, 18, 15, "val"], [18, 21, 18, 18], [18, 22, 18, 19], [19, 2, 19, 0], [20, 2, 21, 0], [20, 11, 21, 9, "shouldUseNative"], [20, 26, 21, 24, "shouldUseNative"], [20, 27, 21, 24], [20, 29, 21, 27], [21, 4, 22, 1], [21, 8, 22, 5], [22, 6, 23, 2], [22, 10, 23, 6], [22, 11, 23, 7, "Object"], [22, 17, 23, 13], [22, 18, 23, 14, "assign"], [22, 24, 23, 20], [22, 26, 23, 22], [23, 8, 24, 3], [23, 15, 24, 10], [23, 20, 24, 15], [24, 6, 25, 2], [26, 6, 27, 2], [28, 6, 29, 2], [29, 6, 30, 2], [29, 10, 30, 6, "test1"], [29, 15, 30, 11], [29, 18, 30, 14], [29, 22, 30, 18, "String"], [29, 28, 30, 24], [29, 29, 30, 25], [29, 34, 30, 30], [29, 35, 30, 31], [29, 36, 30, 32], [29, 37, 30, 34], [30, 6, 31, 2, "test1"], [30, 11, 31, 7], [30, 12, 31, 8], [30, 13, 31, 9], [30, 14, 31, 10], [30, 17, 31, 13], [30, 21, 31, 17], [31, 6, 32, 2], [31, 10, 32, 6, "Object"], [31, 16, 32, 12], [31, 17, 32, 13, "getOwnPropertyNames"], [31, 36, 32, 32], [31, 37, 32, 33, "test1"], [31, 42, 32, 38], [31, 43, 32, 39], [31, 44, 32, 40], [31, 45, 32, 41], [31, 46, 32, 42], [31, 51, 32, 47], [31, 54, 32, 50], [31, 56, 32, 52], [32, 8, 33, 3], [32, 15, 33, 10], [32, 20, 33, 15], [33, 6, 34, 2], [35, 6, 36, 2], [36, 6, 37, 2], [36, 10, 37, 6, "test2"], [36, 15, 37, 11], [36, 18, 37, 14], [36, 19, 37, 15], [36, 20, 37, 16], [37, 6, 38, 2], [37, 11, 38, 7], [37, 15, 38, 11, "i"], [37, 16, 38, 12], [37, 19, 38, 15], [37, 20, 38, 16], [37, 22, 38, 18, "i"], [37, 23, 38, 19], [37, 26, 38, 22], [37, 28, 38, 24], [37, 30, 38, 26, "i"], [37, 31, 38, 27], [37, 33, 38, 29], [37, 35, 38, 31], [38, 8, 39, 3, "test2"], [38, 13, 39, 8], [38, 14, 39, 9], [38, 17, 39, 12], [38, 20, 39, 15, "String"], [38, 26, 39, 21], [38, 27, 39, 22, "fromCharCode"], [38, 39, 39, 34], [38, 40, 39, 35, "i"], [38, 41, 39, 36], [38, 42, 39, 37], [38, 43, 39, 38], [38, 46, 39, 41, "i"], [38, 47, 39, 42], [39, 6, 40, 2], [40, 6, 41, 2], [40, 10, 41, 6, "order2"], [40, 16, 41, 12], [40, 19, 41, 15, "Object"], [40, 25, 41, 21], [40, 26, 41, 22, "getOwnPropertyNames"], [40, 45, 41, 41], [40, 46, 41, 42, "test2"], [40, 51, 41, 47], [40, 52, 41, 48], [40, 53, 41, 49, "map"], [40, 56, 41, 52], [40, 57, 41, 53], [40, 67, 41, 63, "n"], [40, 68, 41, 64], [40, 70, 41, 66], [41, 8, 42, 3], [41, 15, 42, 10, "test2"], [41, 20, 42, 15], [41, 21, 42, 16, "n"], [41, 22, 42, 17], [41, 23, 42, 18], [42, 6, 43, 2], [42, 7, 43, 3], [42, 8, 43, 4], [43, 6, 44, 2], [43, 10, 44, 6, "order2"], [43, 16, 44, 12], [43, 17, 44, 13, "join"], [43, 21, 44, 17], [43, 22, 44, 18], [43, 24, 44, 20], [43, 25, 44, 21], [43, 30, 44, 26], [43, 42, 44, 38], [43, 44, 44, 40], [44, 8, 45, 3], [44, 15, 45, 10], [44, 20, 45, 15], [45, 6, 46, 2], [47, 6, 48, 2], [48, 6, 49, 2], [48, 10, 49, 6, "test3"], [48, 15, 49, 11], [48, 18, 49, 14], [48, 19, 49, 15], [48, 20, 49, 16], [49, 6, 50, 2], [49, 28, 50, 24], [49, 29, 50, 25, "split"], [49, 34, 50, 30], [49, 35, 50, 31], [49, 37, 50, 33], [49, 38, 50, 34], [49, 39, 50, 35, "for<PERSON>ach"], [49, 46, 50, 42], [49, 47, 50, 43], [49, 57, 50, 53, "letter"], [49, 63, 50, 59], [49, 65, 50, 61], [50, 8, 51, 3, "test3"], [50, 13, 51, 8], [50, 14, 51, 9, "letter"], [50, 20, 51, 15], [50, 21, 51, 16], [50, 24, 51, 19, "letter"], [50, 30, 51, 25], [51, 6, 52, 2], [51, 7, 52, 3], [51, 8, 52, 4], [52, 6, 53, 2], [52, 10, 53, 6, "Object"], [52, 16, 53, 12], [52, 17, 53, 13, "keys"], [52, 21, 53, 17], [52, 22, 53, 18, "Object"], [52, 28, 53, 24], [52, 29, 53, 25, "assign"], [52, 35, 53, 31], [52, 36, 53, 32], [52, 37, 53, 33], [52, 38, 53, 34], [52, 40, 53, 36, "test3"], [52, 45, 53, 41], [52, 46, 53, 42], [52, 47, 53, 43], [52, 48, 53, 44, "join"], [52, 52, 53, 48], [52, 53, 53, 49], [52, 55, 53, 51], [52, 56, 53, 52], [52, 61, 54, 4], [52, 83, 54, 26], [52, 85, 54, 28], [53, 8, 55, 3], [53, 15, 55, 10], [53, 20, 55, 15], [54, 6, 56, 2], [55, 6, 58, 2], [55, 13, 58, 9], [55, 17, 58, 13], [56, 4, 59, 1], [56, 5, 59, 2], [56, 6, 59, 3], [56, 13, 59, 10, "err"], [56, 16, 59, 13], [56, 18, 59, 15], [57, 6, 60, 2], [58, 6, 61, 2], [58, 13, 61, 9], [58, 18, 61, 14], [59, 4, 62, 1], [60, 2, 63, 0], [61, 2, 65, 0, "module"], [61, 8, 65, 6], [61, 9, 65, 7, "exports"], [61, 16, 65, 14], [61, 19, 65, 17, "shouldUseNative"], [61, 34, 65, 32], [61, 35, 65, 33], [61, 36, 65, 34], [61, 39, 65, 37, "Object"], [61, 45, 65, 43], [61, 46, 65, 44, "assign"], [61, 52, 65, 50], [61, 55, 65, 53], [61, 65, 65, 63, "target"], [61, 71, 65, 69], [61, 73, 65, 71, "source"], [61, 79, 65, 77], [61, 81, 65, 79], [62, 4, 66, 1], [62, 8, 66, 5, "from"], [62, 12, 66, 9], [63, 4, 67, 1], [63, 8, 67, 5, "to"], [63, 10, 67, 7], [63, 13, 67, 10, "toObject"], [63, 21, 67, 18], [63, 22, 67, 19, "target"], [63, 28, 67, 25], [63, 29, 67, 26], [64, 4, 68, 1], [64, 8, 68, 5, "symbols"], [64, 15, 68, 12], [65, 4, 70, 1], [65, 9, 70, 6], [65, 13, 70, 10, "s"], [65, 14, 70, 11], [65, 17, 70, 14], [65, 18, 70, 15], [65, 20, 70, 17, "s"], [65, 21, 70, 18], [65, 24, 70, 21, "arguments"], [65, 33, 70, 30], [65, 34, 70, 31, "length"], [65, 40, 70, 37], [65, 42, 70, 39, "s"], [65, 43, 70, 40], [65, 45, 70, 42], [65, 47, 70, 44], [66, 6, 71, 2, "from"], [66, 10, 71, 6], [66, 13, 71, 9, "Object"], [66, 19, 71, 15], [66, 20, 71, 16, "arguments"], [66, 29, 71, 25], [66, 30, 71, 26, "s"], [66, 31, 71, 27], [66, 32, 71, 28], [66, 33, 71, 29], [67, 6, 73, 2], [67, 11, 73, 7], [67, 15, 73, 11, "key"], [67, 18, 73, 14], [67, 22, 73, 18, "from"], [67, 26, 73, 22], [67, 28, 73, 24], [68, 8, 74, 3], [68, 12, 74, 7, "hasOwnProperty"], [68, 26, 74, 21], [68, 27, 74, 22, "call"], [68, 31, 74, 26], [68, 32, 74, 27, "from"], [68, 36, 74, 31], [68, 38, 74, 33, "key"], [68, 41, 74, 36], [68, 42, 74, 37], [68, 44, 74, 39], [69, 10, 75, 4, "to"], [69, 12, 75, 6], [69, 13, 75, 7, "key"], [69, 16, 75, 10], [69, 17, 75, 11], [69, 20, 75, 14, "from"], [69, 24, 75, 18], [69, 25, 75, 19, "key"], [69, 28, 75, 22], [69, 29, 75, 23], [70, 8, 76, 3], [71, 6, 77, 2], [72, 6, 79, 2], [72, 10, 79, 6, "getOwnPropertySymbols"], [72, 31, 79, 27], [72, 33, 79, 29], [73, 8, 80, 3, "symbols"], [73, 15, 80, 10], [73, 18, 80, 13, "getOwnPropertySymbols"], [73, 39, 80, 34], [73, 40, 80, 35, "from"], [73, 44, 80, 39], [73, 45, 80, 40], [74, 8, 81, 3], [74, 13, 81, 8], [74, 17, 81, 12, "i"], [74, 18, 81, 13], [74, 21, 81, 16], [74, 22, 81, 17], [74, 24, 81, 19, "i"], [74, 25, 81, 20], [74, 28, 81, 23, "symbols"], [74, 35, 81, 30], [74, 36, 81, 31, "length"], [74, 42, 81, 37], [74, 44, 81, 39, "i"], [74, 45, 81, 40], [74, 47, 81, 42], [74, 49, 81, 44], [75, 10, 82, 4], [75, 14, 82, 8, "propIsEnumerable"], [75, 30, 82, 24], [75, 31, 82, 25, "call"], [75, 35, 82, 29], [75, 36, 82, 30, "from"], [75, 40, 82, 34], [75, 42, 82, 36, "symbols"], [75, 49, 82, 43], [75, 50, 82, 44, "i"], [75, 51, 82, 45], [75, 52, 82, 46], [75, 53, 82, 47], [75, 55, 82, 49], [76, 12, 83, 5, "to"], [76, 14, 83, 7], [76, 15, 83, 8, "symbols"], [76, 22, 83, 15], [76, 23, 83, 16, "i"], [76, 24, 83, 17], [76, 25, 83, 18], [76, 26, 83, 19], [76, 29, 83, 22, "from"], [76, 33, 83, 26], [76, 34, 83, 27, "symbols"], [76, 41, 83, 34], [76, 42, 83, 35, "i"], [76, 43, 83, 36], [76, 44, 83, 37], [76, 45, 83, 38], [77, 10, 84, 4], [78, 8, 85, 3], [79, 6, 86, 2], [80, 4, 87, 1], [81, 4, 89, 1], [81, 11, 89, 8, "to"], [81, 13, 89, 10], [82, 2, 90, 0], [82, 3, 90, 1], [83, 0, 90, 2], [83, 3]], "functionMap": {"names": ["<global>", "toObject", "shouldUseNative", "Object.getOwnPropertyNames.map$argument_0", "abcdefghijklmnopqrst.split.forEach$argument_0", "<anonymous>"], "mappings": "AAA;ACY;CDM;AEE;qDCoB;GDE;2CEO;GFE;CFW;qDKE;CLyB"}}, "type": "js/module"}]}