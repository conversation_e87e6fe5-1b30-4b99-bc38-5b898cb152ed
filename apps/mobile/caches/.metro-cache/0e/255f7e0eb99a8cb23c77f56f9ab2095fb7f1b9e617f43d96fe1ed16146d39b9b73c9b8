{"dependencies": [{"name": "expo-constants", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 41, "index": 304}, "end": {"line": 7, "column": 66, "index": 329}}], "key": "MLjvisfgn5XkSYgDpD4nfivY4nE=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/index", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "55efhPHw3gz2FoQtoN2yI1VuhbM=", "exportNames": ["*"]}}, {"name": "react-native-is-edge-to-edge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 39, "index": 419}, "end": {"line": 9, "column": 78, "index": 458}}], "key": "/DF8pvIK7hVUN1ny60pi3/9Ia+A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.canOverrideStatusBarBehavior = void 0;\n  const expo_constants_1 = __importDefault(require(_dependencyMap[0], \"expo-constants\"));\n  const react_native_1 = require(_dependencyMap[1], \"react-native-web/dist/index\");\n  const react_native_is_edge_to_edge_1 = require(_dependencyMap[2], \"react-native-is-edge-to-edge\");\n  const hasViewControllerBasedStatusBarAppearance = react_native_1.Platform.OS === 'ios' && !!expo_constants_1.default.expoConfig?.ios?.infoPlist?.UIViewControllerBasedStatusBarAppearance;\n  exports.canOverrideStatusBarBehavior = !(0, react_native_is_edge_to_edge_1.isEdgeToEdge)() && !hasViewControllerBasedStatusBarAppearance;\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__importDefault"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__importDefault"], [4, 52, 2, 51], [4, 56, 2, 56], [4, 66, 2, 66, "mod"], [4, 69, 2, 69], [4, 71, 2, 71], [5, 4, 3, 4], [5, 11, 3, 12, "mod"], [5, 14, 3, 15], [5, 18, 3, 19, "mod"], [5, 21, 3, 22], [5, 22, 3, 23, "__esModule"], [5, 32, 3, 33], [5, 35, 3, 37, "mod"], [5, 38, 3, 40], [5, 41, 3, 43], [6, 6, 3, 45], [6, 15, 3, 54], [6, 17, 3, 56, "mod"], [7, 4, 3, 60], [7, 5, 3, 61], [8, 2, 4, 0], [8, 3, 4, 1], [9, 2, 5, 0, "Object"], [9, 8, 5, 6], [9, 9, 5, 7, "defineProperty"], [9, 23, 5, 21], [9, 24, 5, 22, "exports"], [9, 31, 5, 29], [9, 33, 5, 31], [9, 45, 5, 43], [9, 47, 5, 45], [10, 4, 5, 47, "value"], [10, 9, 5, 52], [10, 11, 5, 54], [11, 2, 5, 59], [11, 3, 5, 60], [11, 4, 5, 61], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "canOverrideStatusBarBehavior"], [12, 38, 6, 36], [12, 41, 6, 39], [12, 46, 6, 44], [12, 47, 6, 45], [13, 2, 7, 0], [13, 8, 7, 6, "expo_constants_1"], [13, 24, 7, 22], [13, 27, 7, 25, "__importDefault"], [13, 42, 7, 40], [13, 43, 7, 41, "require"], [13, 50, 7, 48], [13, 51, 7, 48, "_dependencyMap"], [13, 65, 7, 48], [13, 86, 7, 65], [13, 87, 7, 66], [13, 88, 7, 67], [14, 2, 7, 68], [14, 8, 7, 68, "react_native_1"], [14, 22, 7, 68], [14, 25, 7, 68, "require"], [14, 32, 7, 68], [14, 33, 7, 68, "_dependencyMap"], [14, 47, 7, 68], [15, 2, 9, 0], [15, 8, 9, 6, "react_native_is_edge_to_edge_1"], [15, 38, 9, 36], [15, 41, 9, 39, "require"], [15, 48, 9, 46], [15, 49, 9, 46, "_dependencyMap"], [15, 63, 9, 46], [15, 98, 9, 77], [15, 99, 9, 78], [16, 2, 10, 0], [16, 8, 10, 6, "hasViewControllerBasedStatusBarAppearance"], [16, 49, 10, 47], [16, 52, 10, 50, "react_native_1"], [16, 66, 10, 64], [16, 67, 10, 65, "Platform"], [16, 75, 10, 73], [16, 76, 10, 74, "OS"], [16, 78, 10, 76], [16, 83, 10, 81], [16, 88, 10, 86], [16, 92, 11, 4], [16, 93, 11, 5], [16, 94, 11, 6, "expo_constants_1"], [16, 110, 11, 22], [16, 111, 11, 23, "default"], [16, 118, 11, 30], [16, 119, 11, 31, "expoConfig"], [16, 129, 11, 41], [16, 131, 11, 43, "ios"], [16, 134, 11, 46], [16, 136, 11, 48, "infoPlist"], [16, 145, 11, 57], [16, 147, 11, 59, "UIViewControllerBasedStatusBarAppearance"], [16, 187, 11, 99], [17, 2, 12, 0, "exports"], [17, 9, 12, 7], [17, 10, 12, 8, "canOverrideStatusBarBehavior"], [17, 38, 12, 36], [17, 41, 12, 39], [17, 42, 12, 40], [17, 43, 12, 41], [17, 44, 12, 42], [17, 46, 12, 44, "react_native_is_edge_to_edge_1"], [17, 76, 12, 74], [17, 77, 12, 75, "isEdgeToEdge"], [17, 89, 12, 87], [17, 91, 12, 89], [17, 92, 12, 90], [17, 96, 12, 94], [17, 97, 12, 95, "hasViewControllerBasedStatusBarAppearance"], [17, 138, 12, 136], [18, 0, 12, 137], [18, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;wDCC;CDE"}}, "type": "js/module"}]}