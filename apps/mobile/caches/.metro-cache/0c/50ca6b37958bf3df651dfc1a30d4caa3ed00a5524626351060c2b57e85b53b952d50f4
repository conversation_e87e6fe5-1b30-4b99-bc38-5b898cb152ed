{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var GalleryHorizontalEnd = exports.default = (0, _createLucideIcon.default)(\"GalleryHorizontalEnd\", [[\"path\", {\n    d: \"M2 7v10\",\n    key: \"a2pl2d\"\n  }], [\"path\", {\n    d: \"M6 5v14\",\n    key: \"1kq3d7\"\n  }], [\"rect\", {\n    width: \"12\",\n    height: \"18\",\n    x: \"10\",\n    y: \"3\",\n    rx: \"2\",\n    key: \"13i7bc\"\n  }]]);\n});", "lineCount": 29, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "GalleryHorizontalEnd"], [15, 26, 10, 26], [15, 29, 10, 26, "exports"], [15, 36, 10, 26], [15, 37, 10, 26, "default"], [15, 44, 10, 26], [15, 47, 10, 29], [15, 51, 10, 29, "createLucideIcon"], [15, 76, 10, 45], [15, 78, 10, 46], [15, 100, 10, 68], [15, 102, 10, 70], [15, 103, 11, 2], [15, 104, 11, 3], [15, 110, 11, 9], [15, 112, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 16, 11, 25], [17, 4, 11, 27, "key"], [17, 7, 11, 30], [17, 9, 11, 32], [18, 2, 11, 41], [18, 3, 11, 42], [18, 4, 11, 43], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "width"], [22, 9, 13, 18], [22, 11, 13, 20], [22, 15, 13, 24], [23, 4, 13, 26, "height"], [23, 10, 13, 32], [23, 12, 13, 34], [23, 16, 13, 38], [24, 4, 13, 40, "x"], [24, 5, 13, 41], [24, 7, 13, 43], [24, 11, 13, 47], [25, 4, 13, 49, "y"], [25, 5, 13, 50], [25, 7, 13, 52], [25, 10, 13, 55], [26, 4, 13, 57, "rx"], [26, 6, 13, 59], [26, 8, 13, 61], [26, 11, 13, 64], [27, 4, 13, 66, "key"], [27, 7, 13, 69], [27, 9, 13, 71], [28, 2, 13, 80], [28, 3, 13, 81], [28, 4, 13, 82], [28, 5, 14, 1], [28, 6, 14, 2], [29, 0, 14, 3], [29, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}