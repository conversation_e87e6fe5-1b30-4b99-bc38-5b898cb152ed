{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 243}, "end": {"line": 7, "column": 38, "index": 281}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  /**\n   * There's a bug happening on iOS 10 where `ArrayBuffer.prototype.byteLength`\n   * is not defined, but present on the object returned by the function/constructor\n   * See https://github.com/charpeni/react-native-url-polyfill/issues/190\n   * */\n\n  var majorVersionIOS = parseInt(_reactNative.Platform.Version, 10);\n  if (_reactNative.Platform.OS === 'ios' && majorVersionIOS === 10) {\n    if (Object.getOwnPropertyDescriptor(ArrayBuffer.prototype, 'byteLength') == null) {\n      // eslint-disable-next-line no-extend-native\n      Object.defineProperty(ArrayBuffer.prototype, 'byteLength', {\n        configurable: true,\n        enumerable: false,\n        get() {\n          return null;\n        }\n      });\n    }\n  }\n});", "lineCount": 22, "map": [[2, 2, 7, 0], [2, 6, 7, 0, "_reactNative"], [2, 18, 7, 0], [2, 21, 7, 0, "require"], [2, 28, 7, 0], [2, 29, 7, 0, "_dependencyMap"], [2, 43, 7, 0], [3, 2, 1, 0], [4, 0, 2, 0], [5, 0, 3, 0], [6, 0, 4, 0], [7, 0, 5, 0], [9, 2, 9, 0], [9, 6, 9, 6, "majorVersionIOS"], [9, 21, 9, 21], [9, 24, 9, 24, "parseInt"], [9, 32, 9, 32], [9, 33, 9, 33, "Platform"], [9, 54, 9, 41], [9, 55, 9, 42, "Version"], [9, 62, 9, 49], [9, 64, 9, 51], [9, 66, 9, 53], [9, 67, 9, 54], [10, 2, 11, 0], [10, 6, 11, 4, "Platform"], [10, 27, 11, 12], [10, 28, 11, 13, "OS"], [10, 30, 11, 15], [10, 35, 11, 20], [10, 40, 11, 25], [10, 44, 11, 29, "majorVersionIOS"], [10, 59, 11, 44], [10, 64, 11, 49], [10, 66, 11, 51], [10, 68, 11, 53], [11, 4, 12, 2], [11, 8, 13, 4, "Object"], [11, 14, 13, 10], [11, 15, 13, 11, "getOwnPropertyDescriptor"], [11, 39, 13, 35], [11, 40, 13, 36, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 51, 13, 47], [11, 52, 13, 48, "prototype"], [11, 61, 13, 57], [11, 63, 13, 59], [11, 75, 13, 71], [11, 76, 13, 72], [11, 80, 13, 76], [11, 84, 13, 80], [11, 86, 14, 4], [12, 6, 15, 4], [13, 6, 16, 4, "Object"], [13, 12, 16, 10], [13, 13, 16, 11, "defineProperty"], [13, 27, 16, 25], [13, 28, 16, 26, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [13, 39, 16, 37], [13, 40, 16, 38, "prototype"], [13, 49, 16, 47], [13, 51, 16, 49], [13, 63, 16, 61], [13, 65, 16, 63], [14, 8, 17, 6, "configurable"], [14, 20, 17, 18], [14, 22, 17, 20], [14, 26, 17, 24], [15, 8, 18, 6, "enumerable"], [15, 18, 18, 16], [15, 20, 18, 18], [15, 25, 18, 23], [16, 8, 19, 6, "get"], [16, 11, 19, 9, "get"], [16, 12, 19, 9], [16, 14, 19, 12], [17, 10, 20, 8], [17, 17, 20, 15], [17, 21, 20, 19], [18, 8, 21, 6], [19, 6, 22, 4], [19, 7, 22, 5], [19, 8, 22, 6], [20, 4, 23, 2], [21, 2, 24, 0], [22, 0, 24, 1], [22, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get"], "mappings": "AAA;MCkB;ODE"}}, "type": "js/module"}]}