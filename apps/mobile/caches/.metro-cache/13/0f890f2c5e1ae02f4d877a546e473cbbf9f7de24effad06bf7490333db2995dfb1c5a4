{"dependencies": [{"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 17, "index": 147}, "end": {"line": 5, "column": 52, "index": 182}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "./constants", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 20, "index": 204}, "end": {"line": 6, "column": 42, "index": 226}}], "key": "3z43bJyk/UB4EKjDCOXTFak09do=", "exportNames": ["*"]}}, {"name": "./link/href", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 15, "index": 243}, "end": {"line": 7, "column": 37, "index": 265}}], "key": "bbOjCna2na3KLYIiMI/X7Z9HiTo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useNavigation = useNavigation;\n  var native_1 = require(_dependencyMap[0], \"@react-navigation/native\");\n  var constants_1 = require(_dependencyMap[1], \"./constants\");\n  var href_1 = require(_dependencyMap[2], \"./link/href\");\n  /**\n   * Returns the underlying React Navigation [`navigation` object](https://reactnavigation.org/docs/navigation-object)\n   * to imperatively access layout-specific functionality like `navigation.openDrawer()` in a\n   * [Drawer](/router/advanced/drawer/) layout.\n   *\n   * @example\n   * ```tsx app/index.tsx\n   * import { useNavigation } from 'expo-router';\n   *\n   * export default function Route() {\n   *   // Access the current navigation object for the current route.\n   *   const navigation = useNavigation();\n   *\n   *   return (\n   *     <View>\n   *       <Text onPress={() => {\n   *         // Open the drawer view.\n   *         navigation.openDrawer();\n   *       }}>\n   *         Open Drawer\n   *       </Text>\n   *     </View>\n   *   );\n   * }\n   * ```\n   *\n   * When using nested layouts, you can access higher-order layouts by passing a secondary argument denoting the layout route.\n   * For example, `/menu/_layout.tsx` is nested inside `/app/orders/`, you can use `useNavigation('/orders/menu/')`.\n   *\n   * @example\n   * ```tsx app/orders/menu/index.tsx\n   * import { useNavigation } from 'expo-router';\n   *\n   * export default function MenuRoute() {\n   *   const rootLayout = useNavigation('/');\n   *   const ordersLayout = useNavigation('/orders');\n   *\n   *   // Same as the default results of `useNavigation()` when invoked in this route.\n   *   const parentLayout = useNavigation('/orders/menu');\n   * }\n   * ```\n   *\n   * If you attempt to access a layout that doesn't exist, an error such as\n   * `Could not find parent navigation with route \"/non-existent\"` is thrown.\n   *\n   *\n   * @param parent Provide an absolute path such as `/(root)` to the parent route or a relative path like `../../` to the parent route.\n   * @returns The navigation object for the current route.\n   *\n   * @see React Navigation documentation on [navigation dependent functions](https://reactnavigation.org/docs/navigation-object/#navigator-dependent-functions)\n   * for more information.\n   */\n  function useNavigation(parent) {\n    var navigation = (0, native_1.useNavigation)();\n    var state = (0, native_1.useStateForPath)();\n    if (parent === undefined) {\n      // If no parent is provided, return the current navigation object\n      return navigation;\n    }\n    // Check for the top-level navigator - we cannot fetch anything higher!\n    var currentId = navigation.getId();\n    if (currentId === '' || currentId === `/expo-router/build/views/Navigator`) {\n      return navigation;\n    }\n    if (typeof parent === 'object') {\n      parent = (0, href_1.resolveHref)(parent);\n    }\n    if (parent === '/') {\n      // This is the root navigator\n      return navigation.getParent(`/expo-router/build/views/Navigator`) ?? navigation.getParent(``);\n    } else if (parent?.startsWith('../')) {\n      var names = [];\n      while (state) {\n        var route = state.routes[0];\n        state = route.state;\n        // Don't include the last router, as thats the current route\n        if (state) {\n          names.push(route.name);\n        }\n      }\n      // Removing the trailing slash to make splitting easier\n      var originalParent = parent;\n      if (parent.endsWith('/')) {\n        parent = parent.slice(0, -1);\n      }\n      var segments = parent.split('/');\n      if (!segments.every(segment => segment === '..')) {\n        throw new Error(`Invalid parent path \"${originalParent}\". Only \"../\" segments are allowed when using relative paths.`);\n      }\n      var levels = segments.length;\n      var index = names.length - 1 - levels;\n      if (index < 0) {\n        throw new Error(`Invalid parent path \"${originalParent}\". Cannot go up ${levels} levels from the current route.`);\n      }\n      parent = names[index];\n      // Expo Router navigators use the context key as the name which has a leading `/`\n      // The exception to this is the INTERNAL_SLOT_NAME, and the root navigator which uses ''\n      if (parent && parent !== constants_1.INTERNAL_SLOT_NAME) {\n        parent = `/${parent}`;\n      }\n    }\n    navigation = navigation.getParent(parent);\n    if (process.env.NODE_ENV !== 'production') {\n      if (!navigation) {\n        var ids = [];\n        while (navigation) {\n          ids.push(navigation.getId() || '/');\n          navigation = navigation.getParent();\n        }\n        throw new Error(`Could not find parent navigation with route \"${parent}\". Available routes are: '${ids.join(\"', '\")}'`);\n      }\n    }\n    return navigation;\n  }\n});", "lineCount": 126, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 3, 0, "Object"], [5, 8, 3, 6], [5, 9, 3, 7, "defineProperty"], [5, 23, 3, 21], [5, 24, 3, 22, "exports"], [5, 31, 3, 29], [5, 33, 3, 31], [5, 45, 3, 43], [5, 47, 3, 45], [6, 4, 3, 47, "value"], [6, 9, 3, 52], [6, 11, 3, 54], [7, 2, 3, 59], [7, 3, 3, 60], [7, 4, 3, 61], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "useNavigation"], [8, 23, 4, 21], [8, 26, 4, 24, "useNavigation"], [8, 39, 4, 37], [9, 2, 5, 0], [9, 6, 5, 6, "native_1"], [9, 14, 5, 14], [9, 17, 5, 17, "require"], [9, 24, 5, 24], [9, 25, 5, 24, "_dependencyMap"], [9, 39, 5, 24], [9, 70, 5, 51], [9, 71, 5, 52], [10, 2, 6, 0], [10, 6, 6, 6, "constants_1"], [10, 17, 6, 17], [10, 20, 6, 20, "require"], [10, 27, 6, 27], [10, 28, 6, 27, "_dependencyMap"], [10, 42, 6, 27], [10, 60, 6, 41], [10, 61, 6, 42], [11, 2, 7, 0], [11, 6, 7, 6, "href_1"], [11, 12, 7, 12], [11, 15, 7, 15, "require"], [11, 22, 7, 22], [11, 23, 7, 22, "_dependencyMap"], [11, 37, 7, 22], [11, 55, 7, 36], [11, 56, 7, 37], [12, 2, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 0, 20, 0], [25, 0, 21, 0], [26, 0, 22, 0], [27, 0, 23, 0], [28, 0, 24, 0], [29, 0, 25, 0], [30, 0, 26, 0], [31, 0, 27, 0], [32, 0, 28, 0], [33, 0, 29, 0], [34, 0, 30, 0], [35, 0, 31, 0], [36, 0, 32, 0], [37, 0, 33, 0], [38, 0, 34, 0], [39, 0, 35, 0], [40, 0, 36, 0], [41, 0, 37, 0], [42, 0, 38, 0], [43, 0, 39, 0], [44, 0, 40, 0], [45, 0, 41, 0], [46, 0, 42, 0], [47, 0, 43, 0], [48, 0, 44, 0], [49, 0, 45, 0], [50, 0, 46, 0], [51, 0, 47, 0], [52, 0, 48, 0], [53, 0, 49, 0], [54, 0, 50, 0], [55, 0, 51, 0], [56, 0, 52, 0], [57, 0, 53, 0], [58, 0, 54, 0], [59, 0, 55, 0], [60, 0, 56, 0], [61, 0, 57, 0], [62, 0, 58, 0], [63, 0, 59, 0], [64, 2, 60, 0], [64, 11, 60, 9, "useNavigation"], [64, 24, 60, 22, "useNavigation"], [64, 25, 60, 23, "parent"], [64, 31, 60, 29], [64, 33, 60, 31], [65, 4, 61, 4], [65, 8, 61, 8, "navigation"], [65, 18, 61, 18], [65, 21, 61, 21], [65, 22, 61, 22], [65, 23, 61, 23], [65, 25, 61, 25, "native_1"], [65, 33, 61, 33], [65, 34, 61, 34, "useNavigation"], [65, 47, 61, 47], [65, 49, 61, 49], [65, 50, 61, 50], [66, 4, 62, 4], [66, 8, 62, 8, "state"], [66, 13, 62, 13], [66, 16, 62, 16], [66, 17, 62, 17], [66, 18, 62, 18], [66, 20, 62, 20, "native_1"], [66, 28, 62, 28], [66, 29, 62, 29, "useStateForPath"], [66, 44, 62, 44], [66, 46, 62, 46], [66, 47, 62, 47], [67, 4, 63, 4], [67, 8, 63, 8, "parent"], [67, 14, 63, 14], [67, 19, 63, 19, "undefined"], [67, 28, 63, 28], [67, 30, 63, 30], [68, 6, 64, 8], [69, 6, 65, 8], [69, 13, 65, 15, "navigation"], [69, 23, 65, 25], [70, 4, 66, 4], [71, 4, 67, 4], [72, 4, 68, 4], [72, 8, 68, 10, "currentId"], [72, 17, 68, 19], [72, 20, 68, 22, "navigation"], [72, 30, 68, 32], [72, 31, 68, 33, "getId"], [72, 36, 68, 38], [72, 37, 68, 39], [72, 38, 68, 40], [73, 4, 69, 4], [73, 8, 69, 8, "currentId"], [73, 17, 69, 17], [73, 22, 69, 22], [73, 24, 69, 24], [73, 28, 69, 28, "currentId"], [73, 37, 69, 37], [73, 42, 69, 42], [73, 78, 69, 78], [73, 80, 69, 80], [74, 6, 70, 8], [74, 13, 70, 15, "navigation"], [74, 23, 70, 25], [75, 4, 71, 4], [76, 4, 72, 4], [76, 8, 72, 8], [76, 15, 72, 15, "parent"], [76, 21, 72, 21], [76, 26, 72, 26], [76, 34, 72, 34], [76, 36, 72, 36], [77, 6, 73, 8, "parent"], [77, 12, 73, 14], [77, 15, 73, 17], [77, 16, 73, 18], [77, 17, 73, 19], [77, 19, 73, 21, "href_1"], [77, 25, 73, 27], [77, 26, 73, 28, "resolveHref"], [77, 37, 73, 39], [77, 39, 73, 41, "parent"], [77, 45, 73, 47], [77, 46, 73, 48], [78, 4, 74, 4], [79, 4, 75, 4], [79, 8, 75, 8, "parent"], [79, 14, 75, 14], [79, 19, 75, 19], [79, 22, 75, 22], [79, 24, 75, 24], [80, 6, 76, 8], [81, 6, 77, 8], [81, 13, 77, 15, "navigation"], [81, 23, 77, 25], [81, 24, 77, 26, "getParent"], [81, 33, 77, 35], [81, 34, 77, 36], [81, 70, 77, 72], [81, 71, 77, 73], [81, 75, 77, 77, "navigation"], [81, 85, 77, 87], [81, 86, 77, 88, "getParent"], [81, 95, 77, 97], [81, 96, 77, 98], [81, 98, 77, 100], [81, 99, 77, 101], [82, 4, 78, 4], [82, 5, 78, 5], [82, 11, 79, 9], [82, 15, 79, 13, "parent"], [82, 21, 79, 19], [82, 23, 79, 21, "startsWith"], [82, 33, 79, 31], [82, 34, 79, 32], [82, 39, 79, 37], [82, 40, 79, 38], [82, 42, 79, 40], [83, 6, 80, 8], [83, 10, 80, 14, "names"], [83, 15, 80, 19], [83, 18, 80, 22], [83, 20, 80, 24], [84, 6, 81, 8], [84, 13, 81, 15, "state"], [84, 18, 81, 20], [84, 20, 81, 22], [85, 8, 82, 12], [85, 12, 82, 18, "route"], [85, 17, 82, 23], [85, 20, 82, 26, "state"], [85, 25, 82, 31], [85, 26, 82, 32, "routes"], [85, 32, 82, 38], [85, 33, 82, 39], [85, 34, 82, 40], [85, 35, 82, 41], [86, 8, 83, 12, "state"], [86, 13, 83, 17], [86, 16, 83, 20, "route"], [86, 21, 83, 25], [86, 22, 83, 26, "state"], [86, 27, 83, 31], [87, 8, 84, 12], [88, 8, 85, 12], [88, 12, 85, 16, "state"], [88, 17, 85, 21], [88, 19, 85, 23], [89, 10, 86, 16, "names"], [89, 15, 86, 21], [89, 16, 86, 22, "push"], [89, 20, 86, 26], [89, 21, 86, 27, "route"], [89, 26, 86, 32], [89, 27, 86, 33, "name"], [89, 31, 86, 37], [89, 32, 86, 38], [90, 8, 87, 12], [91, 6, 88, 8], [92, 6, 89, 8], [93, 6, 90, 8], [93, 10, 90, 14, "originalParent"], [93, 24, 90, 28], [93, 27, 90, 31, "parent"], [93, 33, 90, 37], [94, 6, 91, 8], [94, 10, 91, 12, "parent"], [94, 16, 91, 18], [94, 17, 91, 19, "endsWith"], [94, 25, 91, 27], [94, 26, 91, 28], [94, 29, 91, 31], [94, 30, 91, 32], [94, 32, 91, 34], [95, 8, 92, 12, "parent"], [95, 14, 92, 18], [95, 17, 92, 21, "parent"], [95, 23, 92, 27], [95, 24, 92, 28, "slice"], [95, 29, 92, 33], [95, 30, 92, 34], [95, 31, 92, 35], [95, 33, 92, 37], [95, 34, 92, 38], [95, 35, 92, 39], [95, 36, 92, 40], [96, 6, 93, 8], [97, 6, 94, 8], [97, 10, 94, 14, "segments"], [97, 18, 94, 22], [97, 21, 94, 25, "parent"], [97, 27, 94, 31], [97, 28, 94, 32, "split"], [97, 33, 94, 37], [97, 34, 94, 38], [97, 37, 94, 41], [97, 38, 94, 42], [98, 6, 95, 8], [98, 10, 95, 12], [98, 11, 95, 13, "segments"], [98, 19, 95, 21], [98, 20, 95, 22, "every"], [98, 25, 95, 27], [98, 26, 95, 29, "segment"], [98, 33, 95, 36], [98, 37, 95, 41, "segment"], [98, 44, 95, 48], [98, 49, 95, 53], [98, 53, 95, 57], [98, 54, 95, 58], [98, 56, 95, 60], [99, 8, 96, 12], [99, 14, 96, 18], [99, 18, 96, 22, "Error"], [99, 23, 96, 27], [99, 24, 96, 28], [99, 48, 96, 52, "originalParent"], [99, 62, 96, 66], [99, 125, 96, 129], [99, 126, 96, 130], [100, 6, 97, 8], [101, 6, 98, 8], [101, 10, 98, 14, "levels"], [101, 16, 98, 20], [101, 19, 98, 23, "segments"], [101, 27, 98, 31], [101, 28, 98, 32, "length"], [101, 34, 98, 38], [102, 6, 99, 8], [102, 10, 99, 14, "index"], [102, 15, 99, 19], [102, 18, 99, 22, "names"], [102, 23, 99, 27], [102, 24, 99, 28, "length"], [102, 30, 99, 34], [102, 33, 99, 37], [102, 34, 99, 38], [102, 37, 99, 41, "levels"], [102, 43, 99, 47], [103, 6, 100, 8], [103, 10, 100, 12, "index"], [103, 15, 100, 17], [103, 18, 100, 20], [103, 19, 100, 21], [103, 21, 100, 23], [104, 8, 101, 12], [104, 14, 101, 18], [104, 18, 101, 22, "Error"], [104, 23, 101, 27], [104, 24, 101, 28], [104, 48, 101, 52, "originalParent"], [104, 62, 101, 66], [104, 81, 101, 85, "levels"], [104, 87, 101, 91], [104, 120, 101, 124], [104, 121, 101, 125], [105, 6, 102, 8], [106, 6, 103, 8, "parent"], [106, 12, 103, 14], [106, 15, 103, 17, "names"], [106, 20, 103, 22], [106, 21, 103, 23, "index"], [106, 26, 103, 28], [106, 27, 103, 29], [107, 6, 104, 8], [108, 6, 105, 8], [109, 6, 106, 8], [109, 10, 106, 12, "parent"], [109, 16, 106, 18], [109, 20, 106, 22, "parent"], [109, 26, 106, 28], [109, 31, 106, 33, "constants_1"], [109, 42, 106, 44], [109, 43, 106, 45, "INTERNAL_SLOT_NAME"], [109, 61, 106, 63], [109, 63, 106, 65], [110, 8, 107, 12, "parent"], [110, 14, 107, 18], [110, 17, 107, 21], [110, 21, 107, 25, "parent"], [110, 27, 107, 31], [110, 29, 107, 33], [111, 6, 108, 8], [112, 4, 109, 4], [113, 4, 110, 4, "navigation"], [113, 14, 110, 14], [113, 17, 110, 17, "navigation"], [113, 27, 110, 27], [113, 28, 110, 28, "getParent"], [113, 37, 110, 37], [113, 38, 110, 38, "parent"], [113, 44, 110, 44], [113, 45, 110, 45], [114, 4, 111, 4], [114, 8, 111, 8, "process"], [114, 15, 111, 15], [114, 16, 111, 16, "env"], [114, 19, 111, 19], [114, 20, 111, 20, "NODE_ENV"], [114, 28, 111, 28], [114, 33, 111, 33], [114, 45, 111, 45], [114, 47, 111, 47], [115, 6, 112, 8], [115, 10, 112, 12], [115, 11, 112, 13, "navigation"], [115, 21, 112, 23], [115, 23, 112, 25], [116, 8, 113, 12], [116, 12, 113, 18, "ids"], [116, 15, 113, 21], [116, 18, 113, 24], [116, 20, 113, 26], [117, 8, 114, 12], [117, 15, 114, 19, "navigation"], [117, 25, 114, 29], [117, 27, 114, 31], [118, 10, 115, 16, "ids"], [118, 13, 115, 19], [118, 14, 115, 20, "push"], [118, 18, 115, 24], [118, 19, 115, 25, "navigation"], [118, 29, 115, 35], [118, 30, 115, 36, "getId"], [118, 35, 115, 41], [118, 36, 115, 42], [118, 37, 115, 43], [118, 41, 115, 47], [118, 44, 115, 50], [118, 45, 115, 51], [119, 10, 116, 16, "navigation"], [119, 20, 116, 26], [119, 23, 116, 29, "navigation"], [119, 33, 116, 39], [119, 34, 116, 40, "getParent"], [119, 43, 116, 49], [119, 44, 116, 50], [119, 45, 116, 51], [120, 8, 117, 12], [121, 8, 118, 12], [121, 14, 118, 18], [121, 18, 118, 22, "Error"], [121, 23, 118, 27], [121, 24, 118, 28], [121, 72, 118, 76, "parent"], [121, 78, 118, 82], [121, 107, 118, 111, "ids"], [121, 110, 118, 114], [121, 111, 118, 115, "join"], [121, 115, 118, 119], [121, 116, 118, 120], [121, 122, 118, 126], [121, 123, 118, 127], [121, 126, 118, 130], [121, 127, 118, 131], [122, 6, 119, 8], [123, 4, 120, 4], [124, 4, 121, 4], [124, 11, 121, 11, "navigation"], [124, 21, 121, 21], [125, 2, 122, 0], [126, 0, 122, 1], [126, 3]], "functionMap": {"names": ["<global>", "useNavigation", "segments.every$argument_0"], "mappings": "AAA;AC2D;4BCmC,6BD;CD2B"}}, "type": "js/module"}]}