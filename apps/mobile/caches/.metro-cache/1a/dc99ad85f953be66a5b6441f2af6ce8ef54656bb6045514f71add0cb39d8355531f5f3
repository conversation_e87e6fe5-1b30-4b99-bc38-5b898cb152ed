{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 62, "column": 0, "index": 1691}, "end": {"line": 64, "column": 3, "index": 1786}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 62, "column": 0, "index": 1691}, "end": {"line": 64, "column": 3, "index": 1786}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNSVGEllipse';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGEllipse\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      cx: true,\n      cy: true,\n      rx: true,\n      ry: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 49, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 62, 0], [8, 6, 62, 0, "NativeComponentRegistry"], [8, 29, 64, 3], [8, 32, 62, 0, "require"], [8, 39, 64, 3], [8, 40, 64, 3, "_dependencyMap"], [8, 54, 64, 3], [8, 123, 64, 2], [8, 124, 64, 3], [9, 2, 62, 0], [9, 6, 62, 0, "nativeComponentName"], [9, 25, 64, 3], [9, 28, 62, 0], [9, 42, 64, 3], [10, 2, 62, 0], [10, 6, 62, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 64, 3], [10, 31, 64, 3, "exports"], [10, 38, 64, 3], [10, 39, 64, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 64, 3], [10, 64, 62, 0], [11, 4, 62, 0, "uiViewClassName"], [11, 19, 64, 3], [11, 21, 62, 0], [11, 35, 64, 3], [12, 4, 62, 0, "validAttributes"], [12, 19, 64, 3], [12, 21, 62, 0], [13, 6, 62, 0, "name"], [13, 10, 64, 3], [13, 12, 62, 0], [13, 16, 64, 3], [14, 6, 62, 0, "opacity"], [14, 13, 64, 3], [14, 15, 62, 0], [14, 19, 64, 3], [15, 6, 62, 0, "matrix"], [15, 12, 64, 3], [15, 14, 62, 0], [15, 18, 64, 3], [16, 6, 62, 0, "mask"], [16, 10, 64, 3], [16, 12, 62, 0], [16, 16, 64, 3], [17, 6, 62, 0, "markerStart"], [17, 17, 64, 3], [17, 19, 62, 0], [17, 23, 64, 3], [18, 6, 62, 0, "markerMid"], [18, 15, 64, 3], [18, 17, 62, 0], [18, 21, 64, 3], [19, 6, 62, 0, "markerEnd"], [19, 15, 64, 3], [19, 17, 62, 0], [19, 21, 64, 3], [20, 6, 62, 0, "clipPath"], [20, 14, 64, 3], [20, 16, 62, 0], [20, 20, 64, 3], [21, 6, 62, 0, "clipRule"], [21, 14, 64, 3], [21, 16, 62, 0], [21, 20, 64, 3], [22, 6, 62, 0, "responsible"], [22, 17, 64, 3], [22, 19, 62, 0], [22, 23, 64, 3], [23, 6, 62, 0, "display"], [23, 13, 64, 3], [23, 15, 62, 0], [23, 19, 64, 3], [24, 6, 62, 0, "pointerEvents"], [24, 19, 64, 3], [24, 21, 62, 0], [24, 25, 64, 3], [25, 6, 62, 0, "color"], [25, 11, 64, 3], [25, 13, 62, 0], [26, 8, 62, 0, "process"], [26, 15, 64, 3], [26, 17, 62, 0, "require"], [26, 24, 64, 3], [26, 25, 64, 3, "_dependencyMap"], [26, 39, 64, 3], [26, 92, 64, 2], [26, 93, 64, 3], [26, 94, 62, 0, "default"], [27, 6, 64, 2], [27, 7, 64, 3], [28, 6, 62, 0, "fill"], [28, 10, 64, 3], [28, 12, 62, 0], [28, 16, 64, 3], [29, 6, 62, 0, "fillOpacity"], [29, 17, 64, 3], [29, 19, 62, 0], [29, 23, 64, 3], [30, 6, 62, 0, "fillRule"], [30, 14, 64, 3], [30, 16, 62, 0], [30, 20, 64, 3], [31, 6, 62, 0, "stroke"], [31, 12, 64, 3], [31, 14, 62, 0], [31, 18, 64, 3], [32, 6, 62, 0, "strokeOpacity"], [32, 19, 64, 3], [32, 21, 62, 0], [32, 25, 64, 3], [33, 6, 62, 0, "strokeWidth"], [33, 17, 64, 3], [33, 19, 62, 0], [33, 23, 64, 3], [34, 6, 62, 0, "strokeLinecap"], [34, 19, 64, 3], [34, 21, 62, 0], [34, 25, 64, 3], [35, 6, 62, 0, "strokeLinejoin"], [35, 20, 64, 3], [35, 22, 62, 0], [35, 26, 64, 3], [36, 6, 62, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 64, 3], [36, 23, 62, 0], [36, 27, 64, 3], [37, 6, 62, 0, "strokeDashoffset"], [37, 22, 64, 3], [37, 24, 62, 0], [37, 28, 64, 3], [38, 6, 62, 0, "strokeMiterlimit"], [38, 22, 64, 3], [38, 24, 62, 0], [38, 28, 64, 3], [39, 6, 62, 0, "vectorEffect"], [39, 18, 64, 3], [39, 20, 62, 0], [39, 24, 64, 3], [40, 6, 62, 0, "propList"], [40, 14, 64, 3], [40, 16, 62, 0], [40, 20, 64, 3], [41, 6, 62, 0, "filter"], [41, 12, 64, 3], [41, 14, 62, 0], [41, 18, 64, 3], [42, 6, 62, 0, "cx"], [42, 8, 64, 3], [42, 10, 62, 0], [42, 14, 64, 3], [43, 6, 62, 0, "cy"], [43, 8, 64, 3], [43, 10, 62, 0], [43, 14, 64, 3], [44, 6, 62, 0, "rx"], [44, 8, 64, 3], [44, 10, 62, 0], [44, 14, 64, 3], [45, 6, 62, 0, "ry"], [45, 8, 64, 3], [45, 10, 62, 0], [46, 4, 64, 2], [47, 2, 64, 2], [47, 3, 64, 3], [48, 2, 64, 3], [48, 6, 64, 3, "_default"], [48, 14, 64, 3], [48, 17, 64, 3, "exports"], [48, 24, 64, 3], [48, 25, 64, 3, "default"], [48, 32, 64, 3], [48, 35, 62, 0, "NativeComponentRegistry"], [48, 58, 64, 3], [48, 59, 62, 0, "get"], [48, 62, 64, 3], [48, 63, 62, 0, "nativeComponentName"], [48, 82, 64, 3], [48, 84, 62, 0], [48, 90, 62, 0, "__INTERNAL_VIEW_CONFIG"], [48, 112, 64, 2], [48, 113, 64, 3], [49, 0, 64, 3], [49, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}