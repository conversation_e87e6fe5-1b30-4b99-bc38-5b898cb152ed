{"dependencies": [{"name": "./AssetUris", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 25}, "end": {"line": 2, "column": 42, "index": 67}}], "key": "s5UQlFlCKXftpyl/zrvZYZ0/A8E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getImageInfoAsync = getImageInfoAsync;\n  exports.isImageType = isImageType;\n  var _AssetUris = require(_dependencyMap[0], \"./AssetUris\");\n  /* eslint-env browser */\n\n  function isImageType(type) {\n    return /^(jpeg|jpg|gif|png|bmp|webp|heic)$/i.test(type);\n  }\n  function getImageInfoAsync(url) {\n    if (typeof window === 'undefined') {\n      return Promise.resolve({\n        name: (0, _AssetUris.getFilename)(url),\n        width: 0,\n        height: 0\n      });\n    }\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      img.onerror = reject;\n      img.onload = () => {\n        resolve({\n          name: (0, _AssetUris.getFilename)(url),\n          width: img.naturalWidth,\n          height: img.naturalHeight\n        });\n      };\n      img.src = url;\n    });\n  }\n});", "lineCount": 34, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_<PERSON><PERSON><PERSON><PERSON>"], [7, 16, 2, 0], [7, 19, 2, 0, "require"], [7, 26, 2, 0], [7, 27, 2, 0, "_dependencyMap"], [7, 41, 2, 0], [8, 2, 1, 0], [10, 2, 3, 7], [10, 11, 3, 16, "isImageType"], [10, 22, 3, 27, "isImageType"], [10, 23, 3, 28, "type"], [10, 27, 3, 32], [10, 29, 3, 34], [11, 4, 4, 4], [11, 11, 4, 11], [11, 48, 4, 48], [11, 49, 4, 49, "test"], [11, 53, 4, 53], [11, 54, 4, 54, "type"], [11, 58, 4, 58], [11, 59, 4, 59], [12, 2, 5, 0], [13, 2, 6, 7], [13, 11, 6, 16, "getImageInfoAsync"], [13, 28, 6, 33, "getImageInfoAsync"], [13, 29, 6, 34, "url"], [13, 32, 6, 37], [13, 34, 6, 39], [14, 4, 7, 4], [14, 8, 7, 8], [14, 15, 7, 15, "window"], [14, 21, 7, 21], [14, 26, 7, 26], [14, 37, 7, 37], [14, 39, 7, 39], [15, 6, 8, 8], [15, 13, 8, 15, "Promise"], [15, 20, 8, 22], [15, 21, 8, 23, "resolve"], [15, 28, 8, 30], [15, 29, 8, 31], [16, 8, 8, 33, "name"], [16, 12, 8, 37], [16, 14, 8, 39], [16, 18, 8, 39, "getFilename"], [16, 40, 8, 50], [16, 42, 8, 51, "url"], [16, 45, 8, 54], [16, 46, 8, 55], [17, 8, 8, 57, "width"], [17, 13, 8, 62], [17, 15, 8, 64], [17, 16, 8, 65], [18, 8, 8, 67, "height"], [18, 14, 8, 73], [18, 16, 8, 75], [19, 6, 8, 77], [19, 7, 8, 78], [19, 8, 8, 79], [20, 4, 9, 4], [21, 4, 10, 4], [21, 11, 10, 11], [21, 15, 10, 15, "Promise"], [21, 22, 10, 22], [21, 23, 10, 23], [21, 24, 10, 24, "resolve"], [21, 31, 10, 31], [21, 33, 10, 33, "reject"], [21, 39, 10, 39], [21, 44, 10, 44], [22, 6, 11, 8], [22, 12, 11, 14, "img"], [22, 15, 11, 17], [22, 18, 11, 20], [22, 22, 11, 24, "Image"], [22, 27, 11, 29], [22, 28, 11, 30], [22, 29, 11, 31], [23, 6, 12, 8, "img"], [23, 9, 12, 11], [23, 10, 12, 12, "onerror"], [23, 17, 12, 19], [23, 20, 12, 22, "reject"], [23, 26, 12, 28], [24, 6, 13, 8, "img"], [24, 9, 13, 11], [24, 10, 13, 12, "onload"], [24, 16, 13, 18], [24, 19, 13, 21], [24, 25, 13, 27], [25, 8, 14, 12, "resolve"], [25, 15, 14, 19], [25, 16, 14, 20], [26, 10, 15, 16, "name"], [26, 14, 15, 20], [26, 16, 15, 22], [26, 20, 15, 22, "getFilename"], [26, 42, 15, 33], [26, 44, 15, 34, "url"], [26, 47, 15, 37], [26, 48, 15, 38], [27, 10, 16, 16, "width"], [27, 15, 16, 21], [27, 17, 16, 23, "img"], [27, 20, 16, 26], [27, 21, 16, 27, "naturalWidth"], [27, 33, 16, 39], [28, 10, 17, 16, "height"], [28, 16, 17, 22], [28, 18, 17, 24, "img"], [28, 21, 17, 27], [28, 22, 17, 28, "naturalHeight"], [29, 8, 18, 12], [29, 9, 18, 13], [29, 10, 18, 14], [30, 6, 19, 8], [30, 7, 19, 9], [31, 6, 20, 8, "img"], [31, 9, 20, 11], [31, 10, 20, 12, "src"], [31, 13, 20, 15], [31, 16, 20, 18, "url"], [31, 19, 20, 21], [32, 4, 21, 4], [32, 5, 21, 5], [32, 6, 21, 6], [33, 2, 22, 0], [34, 0, 22, 1], [34, 3]], "functionMap": {"names": ["<global>", "isImageType", "getImageInfoAsync", "Promise$argument_0", "img.onload"], "mappings": "AAA;OCE;CDE;OEC;uBCI;qBCG;SDM;KDE;CFC"}}, "type": "js/module"}]}