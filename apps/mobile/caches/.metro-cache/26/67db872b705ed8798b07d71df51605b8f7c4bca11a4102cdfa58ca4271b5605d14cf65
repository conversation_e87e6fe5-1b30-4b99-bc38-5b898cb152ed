{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RootTagContext = void 0;\n  exports.createRootTag = createRootTag;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var RootTagContext = exports.RootTagContext = /*#__PURE__*/React.createContext(0);\n  if (__DEV__) {\n    RootTagContext.displayName = 'RootTagContext';\n  }\n  function createRootTag(rootTag) {\n    return rootTag;\n  }\n});", "lineCount": 16, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "React"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireWildcard"], [7, 37, 11, 0], [7, 38, 11, 0, "require"], [7, 45, 11, 0], [7, 46, 11, 0, "_dependencyMap"], [7, 60, 11, 0], [8, 2, 11, 31], [8, 11, 11, 31, "_interopRequireWildcard"], [8, 35, 11, 31, "e"], [8, 36, 11, 31], [8, 38, 11, 31, "t"], [8, 39, 11, 31], [8, 68, 11, 31, "WeakMap"], [8, 75, 11, 31], [8, 81, 11, 31, "r"], [8, 82, 11, 31], [8, 89, 11, 31, "WeakMap"], [8, 96, 11, 31], [8, 100, 11, 31, "n"], [8, 101, 11, 31], [8, 108, 11, 31, "WeakMap"], [8, 115, 11, 31], [8, 127, 11, 31, "_interopRequireWildcard"], [8, 150, 11, 31], [8, 162, 11, 31, "_interopRequireWildcard"], [8, 163, 11, 31, "e"], [8, 164, 11, 31], [8, 166, 11, 31, "t"], [8, 167, 11, 31], [8, 176, 11, 31, "t"], [8, 177, 11, 31], [8, 181, 11, 31, "e"], [8, 182, 11, 31], [8, 186, 11, 31, "e"], [8, 187, 11, 31], [8, 188, 11, 31, "__esModule"], [8, 198, 11, 31], [8, 207, 11, 31, "e"], [8, 208, 11, 31], [8, 214, 11, 31, "o"], [8, 215, 11, 31], [8, 217, 11, 31, "i"], [8, 218, 11, 31], [8, 220, 11, 31, "f"], [8, 221, 11, 31], [8, 226, 11, 31, "__proto__"], [8, 235, 11, 31], [8, 243, 11, 31, "default"], [8, 250, 11, 31], [8, 252, 11, 31, "e"], [8, 253, 11, 31], [8, 270, 11, 31, "e"], [8, 271, 11, 31], [8, 294, 11, 31, "e"], [8, 295, 11, 31], [8, 320, 11, 31, "e"], [8, 321, 11, 31], [8, 330, 11, 31, "f"], [8, 331, 11, 31], [8, 337, 11, 31, "o"], [8, 338, 11, 31], [8, 341, 11, 31, "t"], [8, 342, 11, 31], [8, 345, 11, 31, "n"], [8, 346, 11, 31], [8, 349, 11, 31, "r"], [8, 350, 11, 31], [8, 358, 11, 31, "o"], [8, 359, 11, 31], [8, 360, 11, 31, "has"], [8, 363, 11, 31], [8, 364, 11, 31, "e"], [8, 365, 11, 31], [8, 375, 11, 31, "o"], [8, 376, 11, 31], [8, 377, 11, 31, "get"], [8, 380, 11, 31], [8, 381, 11, 31, "e"], [8, 382, 11, 31], [8, 385, 11, 31, "o"], [8, 386, 11, 31], [8, 387, 11, 31, "set"], [8, 390, 11, 31], [8, 391, 11, 31, "e"], [8, 392, 11, 31], [8, 394, 11, 31, "f"], [8, 395, 11, 31], [8, 409, 11, 31, "_t"], [8, 411, 11, 31], [8, 415, 11, 31, "e"], [8, 416, 11, 31], [8, 432, 11, 31, "_t"], [8, 434, 11, 31], [8, 441, 11, 31, "hasOwnProperty"], [8, 455, 11, 31], [8, 456, 11, 31, "call"], [8, 460, 11, 31], [8, 461, 11, 31, "e"], [8, 462, 11, 31], [8, 464, 11, 31, "_t"], [8, 466, 11, 31], [8, 473, 11, 31, "i"], [8, 474, 11, 31], [8, 478, 11, 31, "o"], [8, 479, 11, 31], [8, 482, 11, 31, "Object"], [8, 488, 11, 31], [8, 489, 11, 31, "defineProperty"], [8, 503, 11, 31], [8, 508, 11, 31, "Object"], [8, 514, 11, 31], [8, 515, 11, 31, "getOwnPropertyDescriptor"], [8, 539, 11, 31], [8, 540, 11, 31, "e"], [8, 541, 11, 31], [8, 543, 11, 31, "_t"], [8, 545, 11, 31], [8, 552, 11, 31, "i"], [8, 553, 11, 31], [8, 554, 11, 31, "get"], [8, 557, 11, 31], [8, 561, 11, 31, "i"], [8, 562, 11, 31], [8, 563, 11, 31, "set"], [8, 566, 11, 31], [8, 570, 11, 31, "o"], [8, 571, 11, 31], [8, 572, 11, 31, "f"], [8, 573, 11, 31], [8, 575, 11, 31, "_t"], [8, 577, 11, 31], [8, 579, 11, 31, "i"], [8, 580, 11, 31], [8, 584, 11, 31, "f"], [8, 585, 11, 31], [8, 586, 11, 31, "_t"], [8, 588, 11, 31], [8, 592, 11, 31, "e"], [8, 593, 11, 31], [8, 594, 11, 31, "_t"], [8, 596, 11, 31], [8, 607, 11, 31, "f"], [8, 608, 11, 31], [8, 613, 11, 31, "e"], [8, 614, 11, 31], [8, 616, 11, 31, "t"], [8, 617, 11, 31], [9, 2, 15, 7], [9, 6, 15, 13, "RootTagContext"], [9, 20, 15, 51], [9, 23, 15, 51, "exports"], [9, 30, 15, 51], [9, 31, 15, 51, "RootTagContext"], [9, 45, 15, 51], [9, 61, 16, 2, "React"], [9, 66, 16, 7], [9, 67, 16, 8, "createContext"], [9, 80, 16, 21], [9, 81, 16, 31], [9, 82, 16, 32], [9, 83, 16, 33], [10, 2, 18, 0], [10, 6, 18, 4, "__DEV__"], [10, 13, 18, 11], [10, 15, 18, 13], [11, 4, 19, 2, "RootTagContext"], [11, 18, 19, 16], [11, 19, 19, 17, "displayName"], [11, 30, 19, 28], [11, 33, 19, 31], [11, 49, 19, 47], [12, 2, 20, 0], [13, 2, 25, 7], [13, 11, 25, 16, "createRootTag"], [13, 24, 25, 29, "createRootTag"], [13, 25, 25, 30, "rootTag"], [13, 32, 25, 55], [13, 34, 25, 66], [14, 4, 26, 2], [14, 11, 26, 9, "rootTag"], [14, 18, 26, 16], [15, 2, 27, 0], [16, 0, 27, 1], [16, 3]], "functionMap": {"names": ["<global>", "createRootTag"], "mappings": "AAA;OCwB"}}, "type": "js/module"}]}