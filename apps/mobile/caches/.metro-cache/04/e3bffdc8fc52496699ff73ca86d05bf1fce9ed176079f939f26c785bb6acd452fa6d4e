{"dependencies": [{"name": "./ReactNativeVersionCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 32}, "end": {"line": 17, "column": 68}}], "key": "gLWyn5oS/5PP5TuJ7IWTN/EXLls=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var ReactNativeVersionCheck = require(_dependencyMap[0], \"./ReactNativeVersionCheck\");\n  ReactNativeVersionCheck.checkVersions();\n});", "lineCount": 6, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 17, 0], [4, 6, 17, 6, "ReactNativeVersionCheck"], [4, 29, 17, 29], [4, 32, 17, 32, "require"], [4, 39, 17, 39], [4, 40, 17, 39, "_dependencyMap"], [4, 54, 17, 39], [4, 86, 17, 67], [4, 87, 17, 68], [5, 2, 18, 0, "ReactNativeVersionCheck"], [5, 25, 18, 23], [5, 26, 18, 24, "checkVersions"], [5, 39, 18, 37], [5, 40, 18, 38], [5, 41, 18, 39], [6, 0, 18, 40], [6, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}