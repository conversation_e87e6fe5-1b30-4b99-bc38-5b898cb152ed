{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/lib/create-icon-set-from-fontello", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 109, "index": 109}}], "key": "S+EGmVQnaEp/1GTD58WbeoAEw94=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = _default;\n  var _createIconSetFromFontello = _interopRequireDefault(require(_dependencyMap[1], \"./vendor/react-native-vector-icons/lib/create-icon-set-from-fontello\"));\n  function _default(config, expoFontName, expoAssetId) {\n    return (0, _createIconSetFromFontello.default)(config, expoFontName, expoAssetId);\n  }\n});", "lineCount": 11, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_createIconSetFrom<PERSON><PERSON><PERSON>"], [7, 32, 1, 0], [7, 35, 1, 0, "_interopRequireDefault"], [7, 57, 1, 0], [7, 58, 1, 0, "require"], [7, 65, 1, 0], [7, 66, 1, 0, "_dependencyMap"], [7, 80, 1, 0], [8, 2, 2, 15], [8, 11, 2, 15, "_default"], [8, 20, 2, 25, "config"], [8, 26, 2, 31], [8, 28, 2, 33, "expoFontName"], [8, 40, 2, 45], [8, 42, 2, 47, "expoAssetId"], [8, 53, 2, 58], [8, 55, 2, 60], [9, 4, 3, 4], [9, 11, 3, 11], [9, 15, 3, 11, "createIconSetFromFontello"], [9, 49, 3, 36], [9, 51, 3, 37, "config"], [9, 57, 3, 43], [9, 59, 3, 45, "expoFontName"], [9, 71, 3, 57], [9, 73, 3, 59, "expoAssetId"], [9, 84, 3, 70], [9, 85, 3, 71], [10, 2, 4, 0], [11, 0, 4, 1], [11, 3]], "functionMap": {"names": ["<global>", "default"], "mappings": "AAA;eCC;CDE"}}, "type": "js/module"}]}