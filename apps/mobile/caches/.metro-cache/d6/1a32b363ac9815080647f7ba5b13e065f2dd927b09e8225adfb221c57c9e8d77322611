{"dependencies": [{"name": "../config.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 46, "index": 61}}], "key": "KC/6O4o0+wWAJaE/vbdfH2v9syg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.EntryExitTransition = EntryExitTransition;\n  var _config = require(_dependencyMap[0], \"../config.js\");\n  const ExitingFinalStep = 49;\n  const EnteringStartStep = 50;\n  // Layout transitions on web work in \"reverse order\". It means that the element is rendered at its destination and then, at the beginning of the animation,\n  // we move it back to its starting point.\n  // This function is responsible for adding transition data into beginning of each keyframe step.\n  // Doing so will ensure that the element will perform animation from correct position.\n  function addTransformToKeepPosition(keyframeStyleData, animationStyle, transformData, isExiting) {\n    for (const [timestamp, styles] of Object.entries(animationStyle)) {\n      if (styles.transform !== undefined) {\n        // If transform was defined, we want to put transform from transition at the beginning, hence we use `unshift`\n        styles.transform.unshift(transformData);\n      } else {\n        // If transform was undefined, we simply add transform from transition\n        styles.transform = [transformData];\n      }\n      const newTimestamp = parseInt(timestamp) / 2;\n      const index = isExiting ? Math.min(newTimestamp, ExitingFinalStep) // We want to squeeze exiting animation from range 0-100 into range 0-49\n      : newTimestamp + EnteringStartStep; // Entering animation will start from 50 and go up to 100\n\n      keyframeStyleData[`${index}`] = styles;\n    }\n  }\n\n  // EntryExit transition consists of two animations - exiting and entering.\n  // In Keyframes one cannot simply specify animation for given frame. Switching from one animation\n  // to the other one between steps 49 and 50 may lead to flickers, since browser tries to interpolate\n  // one step into the other. To avoid that, we set components' `opacity` to 0 right before switching animation\n  // and set it again to 1 when component is in right position. Hiding component between animations\n  // prevents flickers.\n  function hideComponentBetweenAnimations(keyframeStyleData) {\n    // We have to take into account that some animations have already defined `opacity`.\n    // In that case, we don't want to override it.\n    const opacityInStep = new Map();\n    if (keyframeStyleData[0].opacity === undefined) {\n      opacityInStep.set(48, 1);\n      opacityInStep.set(49, 0);\n    }\n    if (keyframeStyleData[50].opacity === undefined) {\n      opacityInStep.set(50, 0);\n      opacityInStep.set(51, 1);\n    }\n    for (const [step, opacity] of opacityInStep) {\n      keyframeStyleData[step] = {\n        ...keyframeStyleData[step],\n        opacity\n      };\n    }\n  }\n  function EntryExitTransition(name, transitionData) {\n    const exitingAnimationData = structuredClone(_config.AnimationsData[transitionData.exiting]);\n    const enteringAnimationData = structuredClone(_config.AnimationsData[transitionData.entering]);\n    const additionalExitingData = {\n      translateX: `${transitionData.translateX}px`,\n      translateY: `${transitionData.translateY}px`,\n      scale: `${transitionData.scaleX},${transitionData.scaleY}`\n    };\n    const additionalEnteringData = {\n      translateX: `0px`,\n      translateY: `0px`,\n      scale: `1,1`\n    };\n    const keyframeData = {\n      name,\n      style: {},\n      duration: 300\n    };\n    addTransformToKeepPosition(keyframeData.style, exitingAnimationData.style, additionalExitingData, true);\n    addTransformToKeepPosition(keyframeData.style, enteringAnimationData.style, additionalEnteringData, false);\n    hideComponentBetweenAnimations(keyframeData.style);\n    return keyframeData;\n  }\n});", "lineCount": 80, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "EntryExitTransition"], [7, 29, 1, 13], [7, 32, 1, 13, "EntryExitTransition"], [7, 51, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_config"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "ExitingFinalStep"], [9, 24, 4, 22], [9, 27, 4, 25], [9, 29, 4, 27], [10, 2, 5, 0], [10, 8, 5, 6, "EnteringStartStep"], [10, 25, 5, 23], [10, 28, 5, 26], [10, 30, 5, 28], [11, 2, 6, 0], [12, 2, 7, 0], [13, 2, 8, 0], [14, 2, 9, 0], [15, 2, 10, 0], [15, 11, 10, 9, "addTransformToKeepPosition"], [15, 37, 10, 35, "addTransformToKeepPosition"], [15, 38, 10, 36, "keyframeStyleData"], [15, 55, 10, 53], [15, 57, 10, 55, "animationStyle"], [15, 71, 10, 69], [15, 73, 10, 71, "transformData"], [15, 86, 10, 84], [15, 88, 10, 86, "isExiting"], [15, 97, 10, 95], [15, 99, 10, 97], [16, 4, 11, 2], [16, 9, 11, 7], [16, 15, 11, 13], [16, 16, 11, 14, "timestamp"], [16, 25, 11, 23], [16, 27, 11, 25, "styles"], [16, 33, 11, 31], [16, 34, 11, 32], [16, 38, 11, 36, "Object"], [16, 44, 11, 42], [16, 45, 11, 43, "entries"], [16, 52, 11, 50], [16, 53, 11, 51, "animationStyle"], [16, 67, 11, 65], [16, 68, 11, 66], [16, 70, 11, 68], [17, 6, 12, 4], [17, 10, 12, 8, "styles"], [17, 16, 12, 14], [17, 17, 12, 15, "transform"], [17, 26, 12, 24], [17, 31, 12, 29, "undefined"], [17, 40, 12, 38], [17, 42, 12, 40], [18, 8, 13, 6], [19, 8, 14, 6, "styles"], [19, 14, 14, 12], [19, 15, 14, 13, "transform"], [19, 24, 14, 22], [19, 25, 14, 23, "unshift"], [19, 32, 14, 30], [19, 33, 14, 31, "transformData"], [19, 46, 14, 44], [19, 47, 14, 45], [20, 6, 15, 4], [20, 7, 15, 5], [20, 13, 15, 11], [21, 8, 16, 6], [22, 8, 17, 6, "styles"], [22, 14, 17, 12], [22, 15, 17, 13, "transform"], [22, 24, 17, 22], [22, 27, 17, 25], [22, 28, 17, 26, "transformData"], [22, 41, 17, 39], [22, 42, 17, 40], [23, 6, 18, 4], [24, 6, 19, 4], [24, 12, 19, 10, "newTimestamp"], [24, 24, 19, 22], [24, 27, 19, 25, "parseInt"], [24, 35, 19, 33], [24, 36, 19, 34, "timestamp"], [24, 45, 19, 43], [24, 46, 19, 44], [24, 49, 19, 47], [24, 50, 19, 48], [25, 6, 20, 4], [25, 12, 20, 10, "index"], [25, 17, 20, 15], [25, 20, 20, 18, "isExiting"], [25, 29, 20, 27], [25, 32, 20, 30, "Math"], [25, 36, 20, 34], [25, 37, 20, 35, "min"], [25, 40, 20, 38], [25, 41, 20, 39, "newTimestamp"], [25, 53, 20, 51], [25, 55, 20, 53, "ExitingFinalStep"], [25, 71, 20, 69], [25, 72, 20, 70], [25, 73, 20, 71], [26, 6, 20, 71], [26, 8, 21, 6, "newTimestamp"], [26, 20, 21, 18], [26, 23, 21, 21, "EnteringStartStep"], [26, 40, 21, 38], [26, 41, 21, 39], [26, 42, 21, 40], [28, 6, 23, 4, "keyframeStyleData"], [28, 23, 23, 21], [28, 24, 23, 22], [28, 27, 23, 25, "index"], [28, 32, 23, 30], [28, 34, 23, 32], [28, 35, 23, 33], [28, 38, 23, 36, "styles"], [28, 44, 23, 42], [29, 4, 24, 2], [30, 2, 25, 0], [32, 2, 27, 0], [33, 2, 28, 0], [34, 2, 29, 0], [35, 2, 30, 0], [36, 2, 31, 0], [37, 2, 32, 0], [38, 2, 33, 0], [38, 11, 33, 9, "hideComponentBetweenAnimations"], [38, 41, 33, 39, "hideComponentBetweenAnimations"], [38, 42, 33, 40, "keyframeStyleData"], [38, 59, 33, 57], [38, 61, 33, 59], [39, 4, 34, 2], [40, 4, 35, 2], [41, 4, 36, 2], [41, 10, 36, 8, "opacityInStep"], [41, 23, 36, 21], [41, 26, 36, 24], [41, 30, 36, 28, "Map"], [41, 33, 36, 31], [41, 34, 36, 32], [41, 35, 36, 33], [42, 4, 37, 2], [42, 8, 37, 6, "keyframeStyleData"], [42, 25, 37, 23], [42, 26, 37, 24], [42, 27, 37, 25], [42, 28, 37, 26], [42, 29, 37, 27, "opacity"], [42, 36, 37, 34], [42, 41, 37, 39, "undefined"], [42, 50, 37, 48], [42, 52, 37, 50], [43, 6, 38, 4, "opacityInStep"], [43, 19, 38, 17], [43, 20, 38, 18, "set"], [43, 23, 38, 21], [43, 24, 38, 22], [43, 26, 38, 24], [43, 28, 38, 26], [43, 29, 38, 27], [43, 30, 38, 28], [44, 6, 39, 4, "opacityInStep"], [44, 19, 39, 17], [44, 20, 39, 18, "set"], [44, 23, 39, 21], [44, 24, 39, 22], [44, 26, 39, 24], [44, 28, 39, 26], [44, 29, 39, 27], [44, 30, 39, 28], [45, 4, 40, 2], [46, 4, 41, 2], [46, 8, 41, 6, "keyframeStyleData"], [46, 25, 41, 23], [46, 26, 41, 24], [46, 28, 41, 26], [46, 29, 41, 27], [46, 30, 41, 28, "opacity"], [46, 37, 41, 35], [46, 42, 41, 40, "undefined"], [46, 51, 41, 49], [46, 53, 41, 51], [47, 6, 42, 4, "opacityInStep"], [47, 19, 42, 17], [47, 20, 42, 18, "set"], [47, 23, 42, 21], [47, 24, 42, 22], [47, 26, 42, 24], [47, 28, 42, 26], [47, 29, 42, 27], [47, 30, 42, 28], [48, 6, 43, 4, "opacityInStep"], [48, 19, 43, 17], [48, 20, 43, 18, "set"], [48, 23, 43, 21], [48, 24, 43, 22], [48, 26, 43, 24], [48, 28, 43, 26], [48, 29, 43, 27], [48, 30, 43, 28], [49, 4, 44, 2], [50, 4, 45, 2], [50, 9, 45, 7], [50, 15, 45, 13], [50, 16, 45, 14, "step"], [50, 20, 45, 18], [50, 22, 45, 20, "opacity"], [50, 29, 45, 27], [50, 30, 45, 28], [50, 34, 45, 32, "opacityInStep"], [50, 47, 45, 45], [50, 49, 45, 47], [51, 6, 46, 4, "keyframeStyleData"], [51, 23, 46, 21], [51, 24, 46, 22, "step"], [51, 28, 46, 26], [51, 29, 46, 27], [51, 32, 46, 30], [52, 8, 47, 6], [52, 11, 47, 9, "keyframeStyleData"], [52, 28, 47, 26], [52, 29, 47, 27, "step"], [52, 33, 47, 31], [52, 34, 47, 32], [53, 8, 48, 6, "opacity"], [54, 6, 49, 4], [54, 7, 49, 5], [55, 4, 50, 2], [56, 2, 51, 0], [57, 2, 52, 7], [57, 11, 52, 16, "EntryExitTransition"], [57, 30, 52, 35, "EntryExitTransition"], [57, 31, 52, 36, "name"], [57, 35, 52, 40], [57, 37, 52, 42, "transitionData"], [57, 51, 52, 56], [57, 53, 52, 58], [58, 4, 53, 2], [58, 10, 53, 8, "exitingAnimationData"], [58, 30, 53, 28], [58, 33, 53, 31, "structuredClone"], [58, 48, 53, 46], [58, 49, 53, 47, "AnimationsData"], [58, 71, 53, 61], [58, 72, 53, 62, "transitionData"], [58, 86, 53, 76], [58, 87, 53, 77, "exiting"], [58, 94, 53, 84], [58, 95, 53, 85], [58, 96, 53, 86], [59, 4, 54, 2], [59, 10, 54, 8, "enteringAnimationData"], [59, 31, 54, 29], [59, 34, 54, 32, "structuredClone"], [59, 49, 54, 47], [59, 50, 54, 48, "AnimationsData"], [59, 72, 54, 62], [59, 73, 54, 63, "transitionData"], [59, 87, 54, 77], [59, 88, 54, 78, "entering"], [59, 96, 54, 86], [59, 97, 54, 87], [59, 98, 54, 88], [60, 4, 55, 2], [60, 10, 55, 8, "additionalExitingData"], [60, 31, 55, 29], [60, 34, 55, 32], [61, 6, 56, 4, "translateX"], [61, 16, 56, 14], [61, 18, 56, 16], [61, 21, 56, 19, "transitionData"], [61, 35, 56, 33], [61, 36, 56, 34, "translateX"], [61, 46, 56, 44], [61, 50, 56, 48], [62, 6, 57, 4, "translateY"], [62, 16, 57, 14], [62, 18, 57, 16], [62, 21, 57, 19, "transitionData"], [62, 35, 57, 33], [62, 36, 57, 34, "translateY"], [62, 46, 57, 44], [62, 50, 57, 48], [63, 6, 58, 4, "scale"], [63, 11, 58, 9], [63, 13, 58, 11], [63, 16, 58, 14, "transitionData"], [63, 30, 58, 28], [63, 31, 58, 29, "scaleX"], [63, 37, 58, 35], [63, 41, 58, 39, "transitionData"], [63, 55, 58, 53], [63, 56, 58, 54, "scaleY"], [63, 62, 58, 60], [64, 4, 59, 2], [64, 5, 59, 3], [65, 4, 60, 2], [65, 10, 60, 8, "additionalEnteringData"], [65, 32, 60, 30], [65, 35, 60, 33], [66, 6, 61, 4, "translateX"], [66, 16, 61, 14], [66, 18, 61, 16], [66, 23, 61, 21], [67, 6, 62, 4, "translateY"], [67, 16, 62, 14], [67, 18, 62, 16], [67, 23, 62, 21], [68, 6, 63, 4, "scale"], [68, 11, 63, 9], [68, 13, 63, 11], [69, 4, 64, 2], [69, 5, 64, 3], [70, 4, 65, 2], [70, 10, 65, 8, "keyframeData"], [70, 22, 65, 20], [70, 25, 65, 23], [71, 6, 66, 4, "name"], [71, 10, 66, 8], [72, 6, 67, 4, "style"], [72, 11, 67, 9], [72, 13, 67, 11], [72, 14, 67, 12], [72, 15, 67, 13], [73, 6, 68, 4, "duration"], [73, 14, 68, 12], [73, 16, 68, 14], [74, 4, 69, 2], [74, 5, 69, 3], [75, 4, 70, 2, "addTransformToKeepPosition"], [75, 30, 70, 28], [75, 31, 70, 29, "keyframeData"], [75, 43, 70, 41], [75, 44, 70, 42, "style"], [75, 49, 70, 47], [75, 51, 70, 49, "exitingAnimationData"], [75, 71, 70, 69], [75, 72, 70, 70, "style"], [75, 77, 70, 75], [75, 79, 70, 77, "additionalExitingData"], [75, 100, 70, 98], [75, 102, 70, 100], [75, 106, 70, 104], [75, 107, 70, 105], [76, 4, 71, 2, "addTransformToKeepPosition"], [76, 30, 71, 28], [76, 31, 71, 29, "keyframeData"], [76, 43, 71, 41], [76, 44, 71, 42, "style"], [76, 49, 71, 47], [76, 51, 71, 49, "enteringAnimationData"], [76, 72, 71, 70], [76, 73, 71, 71, "style"], [76, 78, 71, 76], [76, 80, 71, 78, "additionalEnteringData"], [76, 102, 71, 100], [76, 104, 71, 102], [76, 109, 71, 107], [76, 110, 71, 108], [77, 4, 72, 2, "hideComponentBetweenAnimations"], [77, 34, 72, 32], [77, 35, 72, 33, "keyframeData"], [77, 47, 72, 45], [77, 48, 72, 46, "style"], [77, 53, 72, 51], [77, 54, 72, 52], [78, 4, 73, 2], [78, 11, 73, 9, "keyframeData"], [78, 23, 73, 21], [79, 2, 74, 0], [80, 0, 74, 1], [80, 3]], "functionMap": {"names": ["<global>", "addTransformToKeepPosition", "hideComponentBetweenAnimations", "EntryExitTransition"], "mappings": "AAA;ACS;CDe;AEQ;CFkB;OGC;CHsB"}}, "type": "js/module"}]}