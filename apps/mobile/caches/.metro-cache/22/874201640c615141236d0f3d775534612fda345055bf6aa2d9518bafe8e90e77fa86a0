{"dependencies": [{"name": "../../src/private/debugging/setUpFuseboxReactDevToolsDispatcher", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 76}}], "key": "a5v05BpsW7lPUcrgvkBTYxAbqRw=", "exportNames": ["*"]}}, {"name": "react-devtools-core", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 36}}], "key": "0NblxLGJ4gX9z2QqKvuKBWVXaB0=", "exportNames": ["*"]}}, {"name": "../../src/private/debugging/ReactDevToolsSettingsManager", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 41, "column": 39}, "end": {"line": 41, "column": 106}}], "key": "ofcM4CLbTUc9R0WC2GkI1YCZHTI=", "exportNames": ["*"]}}, {"name": "../../src/private/fusebox/specs/NativeReactDevToolsRuntimeSettingsModule", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 87}}], "key": "u9nUQOKhDt5vsAWy+YreVAIggIc=", "exportNames": ["*"]}}, {"name": "../Components/View/ReactNativeStyleAttributes", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 60}}], "key": "fcEq0iITM54okTlx5mwDkutMSME=", "exportNames": ["*"]}}, {"name": "../StyleSheet/flattenStyle", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 75, "column": 25}, "end": {"line": 75, "column": 62}}], "key": "BIktiA0QMs32UXb0pxb877akUBk=", "exportNames": ["*"]}}, {"name": "../AppState/AppState", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 134, "column": 23}, "end": {"line": 134, "column": 54}}], "key": "/p/svsTloqXRObwXEeUlVpX3V/s=", "exportNames": ["*"]}}, {"name": "./Devtools/getDevServer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 135, "column": 27}, "end": {"line": 135, "column": 61}}], "key": "iAecA4BaBjYLGuc17d4egTGlY5E=", "exportNames": ["*"]}}, {"name": "../WebSocket/WebSocket", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 161, "column": 24}, "end": {"line": 161, "column": 57}}], "key": "YzL+ui6xotWaSVWZqsgWhTyGQQQ=", "exportNames": ["*"]}}, {"name": "../EventEmitter/RCTNativeAppEventEmitter", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": 55}}], "key": "KwWhNA7a6bWlBGrh7YAT4ygtIbE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  if (__DEV__) {\n    if (typeof global.queueMicrotask !== 'function') {\n      console.error('queueMicrotask should exist before setting up React DevTools.');\n    }\n    if (console._errorOriginal != null) {\n      console.error('ExceptionsManager should be set up after React DevTools to avoid console.error arguments mutation');\n    }\n  }\n  if (__DEV__) {\n    require(_dependencyMap[0], \"../../src/private/debugging/setUpFuseboxReactDevToolsDispatcher\");\n    var _require = require(_dependencyMap[1], \"react-devtools-core\"),\n      initialize = _require.initialize,\n      connectToDevTools = _require.connectToDevTools,\n      connectWithCustomMessagingProtocol = _require.connectWithCustomMessagingProtocol;\n    var reactDevToolsSettingsManager = require(_dependencyMap[2], \"../../src/private/debugging/ReactDevToolsSettingsManager\");\n    var serializedHookSettings = reactDevToolsSettingsManager.getGlobalHookSettings();\n    var maybeReactDevToolsRuntimeSettingsModuleModule = require(_dependencyMap[3], \"../../src/private/fusebox/specs/NativeReactDevToolsRuntimeSettingsModule\").default;\n    var hookSettings = null;\n    if (serializedHookSettings != null) {\n      try {\n        var parsedSettings = JSON.parse(serializedHookSettings);\n        hookSettings = parsedSettings;\n      } catch {\n        console.error('Failed to parse persisted React DevTools hook settings. React DevTools will be initialized with default settings.');\n      }\n    }\n    var _readReloadAndProfile = readReloadAndProfileConfig(maybeReactDevToolsRuntimeSettingsModuleModule),\n      shouldStartProfilingNow = _readReloadAndProfile.isProfiling,\n      initialProfilingSettings = _readReloadAndProfile.profilingSettings;\n    initialize(hookSettings, shouldStartProfilingNow, initialProfilingSettings);\n    var fuseboxReactDevToolsDispatcher = global.__FUSEBOX_REACT_DEVTOOLS_DISPATCHER__;\n    var reactDevToolsFuseboxGlobalBindingName = fuseboxReactDevToolsDispatcher.BINDING_NAME;\n    var ReactNativeStyleAttributes = require(_dependencyMap[4], \"../Components/View/ReactNativeStyleAttributes\").default;\n    var resolveRNStyle = require(_dependencyMap[5], \"../StyleSheet/flattenStyle\").default;\n    function handleReactDevToolsSettingsUpdate(settings) {\n      reactDevToolsSettingsManager.setGlobalHookSettings(JSON.stringify(settings));\n    }\n    var disconnect = null;\n    function disconnectBackendFromReactDevToolsInFuseboxIfNeeded() {\n      if (disconnect != null) {\n        disconnect();\n        disconnect = null;\n      }\n    }\n    function connectToReactDevToolsInFusebox(domain) {\n      var _readReloadAndProfile2 = readReloadAndProfileConfig(maybeReactDevToolsRuntimeSettingsModuleModule),\n        isReloadAndProfileSupported = _readReloadAndProfile2.isReloadAndProfileSupported,\n        isProfiling = _readReloadAndProfile2.isProfiling,\n        onReloadAndProfile = _readReloadAndProfile2.onReloadAndProfile,\n        onReloadAndProfileFlagsReset = _readReloadAndProfile2.onReloadAndProfileFlagsReset;\n      disconnect = connectWithCustomMessagingProtocol({\n        onSubscribe: listener => {\n          domain.onMessage.addEventListener(listener);\n        },\n        onUnsubscribe: listener => {\n          domain.onMessage.removeEventListener(listener);\n        },\n        onMessage: (event, payload) => {\n          domain.sendMessage({\n            event,\n            payload\n          });\n        },\n        nativeStyleEditorValidAttributes: Object.keys(ReactNativeStyleAttributes),\n        resolveRNStyle,\n        onSettingsUpdated: handleReactDevToolsSettingsUpdate,\n        isReloadAndProfileSupported,\n        isProfiling,\n        onReloadAndProfile,\n        onReloadAndProfileFlagsReset\n      });\n    }\n    var isWebSocketOpen = false;\n    var ws = null;\n    function connectToWSBasedReactDevToolsFrontend() {\n      if (ws !== null && isWebSocketOpen) {\n        return;\n      }\n      if (!window.document) {\n        var AppState = require(_dependencyMap[6], \"../AppState/AppState\").default;\n        var getDevServer = require(_dependencyMap[7], \"./Devtools/getDevServer\").default;\n        var isAppActive = () => AppState.currentState !== 'background';\n        var devServer = getDevServer();\n        var host = devServer.bundleLoadedFromServer ? devServer.url.replace(/https?:\\/\\//, '').replace(/\\/$/, '').split(':')[0] : 'localhost';\n        var port = window.__REACT_DEVTOOLS_PORT__ != null ? window.__REACT_DEVTOOLS_PORT__ : 8097;\n        var WebSocket = require(_dependencyMap[8], \"../WebSocket/WebSocket\").default;\n        ws = new WebSocket('ws://' + host + ':' + port);\n        ws.addEventListener('close', event => {\n          isWebSocketOpen = false;\n        });\n        ws.addEventListener('open', event => {\n          isWebSocketOpen = true;\n        });\n        var _readReloadAndProfile3 = readReloadAndProfileConfig(maybeReactDevToolsRuntimeSettingsModuleModule),\n          isReloadAndProfileSupported = _readReloadAndProfile3.isReloadAndProfileSupported,\n          isProfiling = _readReloadAndProfile3.isProfiling,\n          onReloadAndProfile = _readReloadAndProfile3.onReloadAndProfile,\n          onReloadAndProfileFlagsReset = _readReloadAndProfile3.onReloadAndProfileFlagsReset;\n        connectToDevTools({\n          isAppActive,\n          resolveRNStyle,\n          nativeStyleEditorValidAttributes: Object.keys(ReactNativeStyleAttributes),\n          websocket: ws,\n          onSettingsUpdated: handleReactDevToolsSettingsUpdate,\n          isReloadAndProfileSupported,\n          isProfiling,\n          onReloadAndProfile,\n          onReloadAndProfileFlagsReset\n        });\n      }\n    }\n    if (global[reactDevToolsFuseboxGlobalBindingName] != null) {\n      disconnectBackendFromReactDevToolsInFuseboxIfNeeded();\n      var domain = fuseboxReactDevToolsDispatcher.initializeDomain('react-devtools');\n      connectToReactDevToolsInFusebox(domain);\n    }\n    global.__FUSEBOX_REACT_DEVTOOLS_DISPATCHER__.onDomainInitialization.addEventListener(domain => {\n      if (domain.name === 'react-devtools') {\n        disconnectBackendFromReactDevToolsInFuseboxIfNeeded();\n        connectToReactDevToolsInFusebox(domain);\n      }\n    });\n    var RCTNativeAppEventEmitter = require(_dependencyMap[9], \"../EventEmitter/RCTNativeAppEventEmitter\").default;\n    RCTNativeAppEventEmitter.addListener('RCTDevMenuShown', connectToWSBasedReactDevToolsFrontend);\n    connectToWSBasedReactDevToolsFrontend();\n  }\n  function readReloadAndProfileConfig(maybeModule) {\n    var isReloadAndProfileSupported = maybeModule != null;\n    var config = maybeModule?.getReloadAndProfileConfig();\n    var isProfiling = config?.shouldReloadAndProfile === true;\n    var profilingSettings = {\n      recordChangeDescriptions: config?.recordChangeDescriptions === true,\n      recordTimeline: false\n    };\n    var onReloadAndProfile = recordChangeDescriptions => {\n      if (maybeModule == null) {\n        return;\n      }\n      maybeModule.setReloadAndProfileConfig({\n        shouldReloadAndProfile: true,\n        recordChangeDescriptions\n      });\n    };\n    var onReloadAndProfileFlagsReset = () => {\n      if (maybeModule == null) {\n        return;\n      }\n      maybeModule.setReloadAndProfileConfig({\n        shouldReloadAndProfile: false,\n        recordChangeDescriptions: false\n      });\n    };\n    return {\n      isReloadAndProfileSupported,\n      isProfiling,\n      profilingSettings,\n      onReloadAndProfile,\n      onReloadAndProfileFlagsReset\n    };\n  }\n});", "lineCount": 164, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 16, 0], [4, 6, 16, 4, "__DEV__"], [4, 13, 16, 11], [4, 15, 16, 13], [5, 4, 17, 2], [5, 8, 17, 6], [5, 15, 17, 13, "global"], [5, 21, 17, 19], [5, 22, 17, 20, "queueMicrotask"], [5, 36, 17, 34], [5, 41, 17, 39], [5, 51, 17, 49], [5, 53, 17, 51], [6, 6, 18, 4, "console"], [6, 13, 18, 11], [6, 14, 18, 12, "error"], [6, 19, 18, 17], [6, 20, 19, 6], [6, 83, 20, 4], [6, 84, 20, 5], [7, 4, 21, 2], [8, 4, 25, 2], [8, 8, 25, 6, "console"], [8, 15, 25, 13], [8, 16, 25, 14, "_errorOriginal"], [8, 30, 25, 28], [8, 34, 25, 32], [8, 38, 25, 36], [8, 40, 25, 38], [9, 6, 26, 4, "console"], [9, 13, 26, 11], [9, 14, 26, 12, "error"], [9, 19, 26, 17], [9, 20, 27, 6], [9, 119, 28, 4], [9, 120, 28, 5], [10, 4, 29, 2], [11, 2, 30, 0], [12, 2, 32, 0], [12, 6, 32, 4, "__DEV__"], [12, 13, 32, 11], [12, 15, 32, 13], [13, 4, 34, 2, "require"], [13, 11, 34, 9], [13, 12, 34, 9, "_dependencyMap"], [13, 26, 34, 9], [13, 96, 34, 75], [13, 97, 34, 76], [14, 4, 35, 2], [14, 8, 35, 2, "_require"], [14, 16, 35, 2], [14, 19, 39, 6, "require"], [14, 26, 39, 13], [14, 27, 39, 13, "_dependencyMap"], [14, 41, 39, 13], [14, 67, 39, 35], [14, 68, 39, 36], [15, 6, 36, 4, "initialize"], [15, 16, 36, 14], [15, 19, 36, 14, "_require"], [15, 27, 36, 14], [15, 28, 36, 4, "initialize"], [15, 38, 36, 14], [16, 6, 37, 4, "connectToDevTools"], [16, 23, 37, 21], [16, 26, 37, 21, "_require"], [16, 34, 37, 21], [16, 35, 37, 4, "connectToDevTools"], [16, 52, 37, 21], [17, 6, 38, 4, "connectWithCustomMessagingProtocol"], [17, 40, 38, 38], [17, 43, 38, 38, "_require"], [17, 51, 38, 38], [17, 52, 38, 4, "connectWithCustomMessagingProtocol"], [17, 86, 38, 38], [18, 4, 41, 2], [18, 8, 41, 8, "reactDevToolsSettingsManager"], [18, 36, 41, 36], [18, 39, 41, 39, "require"], [18, 46, 41, 46], [18, 47, 41, 46, "_dependencyMap"], [18, 61, 41, 46], [18, 124, 41, 105], [18, 125, 41, 106], [19, 4, 42, 2], [19, 8, 42, 8, "serializedHookSettings"], [19, 30, 42, 30], [19, 33, 43, 4, "reactDevToolsSettingsManager"], [19, 61, 43, 32], [19, 62, 43, 33, "getGlobalHookSettings"], [19, 83, 43, 54], [19, 84, 43, 55], [19, 85, 43, 56], [20, 4, 44, 2], [20, 8, 44, 8, "maybeReactDevToolsRuntimeSettingsModuleModule"], [20, 53, 44, 53], [20, 56, 45, 4, "require"], [20, 63, 45, 11], [20, 64, 45, 11, "_dependencyMap"], [20, 78, 45, 11], [20, 157, 45, 86], [20, 158, 45, 87], [20, 159, 45, 88, "default"], [20, 166, 45, 95], [21, 4, 47, 2], [21, 8, 47, 6, "hookSettings"], [21, 20, 47, 18], [21, 23, 47, 21], [21, 27, 47, 25], [22, 4, 48, 2], [22, 8, 48, 6, "serializedHookSettings"], [22, 30, 48, 28], [22, 34, 48, 32], [22, 38, 48, 36], [22, 40, 48, 38], [23, 6, 49, 4], [23, 10, 49, 8], [24, 8, 50, 6], [24, 12, 50, 12, "parsedSettings"], [24, 26, 50, 26], [24, 29, 50, 29, "JSON"], [24, 33, 50, 33], [24, 34, 50, 34, "parse"], [24, 39, 50, 39], [24, 40, 50, 40, "serializedHookSettings"], [24, 62, 50, 62], [24, 63, 50, 63], [25, 8, 51, 6, "hookSettings"], [25, 20, 51, 18], [25, 23, 51, 21, "parsedSettings"], [25, 37, 51, 35], [26, 6, 52, 4], [26, 7, 52, 5], [26, 8, 52, 6], [26, 14, 52, 12], [27, 8, 53, 6, "console"], [27, 15, 53, 13], [27, 16, 53, 14, "error"], [27, 21, 53, 19], [27, 22, 54, 8], [27, 137, 55, 6], [27, 138, 55, 7], [28, 6, 56, 4], [29, 4, 57, 2], [30, 4, 59, 2], [30, 8, 59, 2, "_readReloadAndProfile"], [30, 29, 59, 2], [30, 32, 62, 6, "readReloadAndProfileConfig"], [30, 58, 62, 32], [30, 59, 62, 33, "maybeReactDevToolsRuntimeSettingsModuleModule"], [30, 104, 62, 78], [30, 105, 62, 79], [31, 6, 60, 17, "shouldStartProfilingNow"], [31, 29, 60, 40], [31, 32, 60, 40, "_readReloadAndProfile"], [31, 53, 60, 40], [31, 54, 60, 4, "isProfiling"], [31, 65, 60, 15], [32, 6, 61, 23, "initialProfilingSettings"], [32, 30, 61, 47], [32, 33, 61, 47, "_readReloadAndProfile"], [32, 54, 61, 47], [32, 55, 61, 4, "profilingSettings"], [32, 72, 61, 21], [33, 4, 65, 2, "initialize"], [33, 14, 65, 12], [33, 15, 65, 13, "hookSettings"], [33, 27, 65, 25], [33, 29, 65, 27, "shouldStartProfilingNow"], [33, 52, 65, 50], [33, 54, 65, 52, "initialProfilingSettings"], [33, 78, 65, 76], [33, 79, 65, 77], [34, 4, 68, 2], [34, 8, 68, 8, "fuseboxReactDevToolsDispatcher"], [34, 38, 68, 38], [34, 41, 69, 4, "global"], [34, 47, 69, 10], [34, 48, 69, 11, "__FUSEBOX_REACT_DEVTOOLS_DISPATCHER__"], [34, 85, 69, 48], [35, 4, 70, 2], [35, 8, 70, 8, "reactDevToolsFuseboxGlobalBindingName"], [35, 45, 70, 45], [35, 48, 71, 4, "fuseboxReactDevToolsDispatcher"], [35, 78, 71, 34], [35, 79, 71, 35, "BINDING_NAME"], [35, 91, 71, 47], [36, 4, 73, 2], [36, 8, 73, 8, "ReactNativeStyleAttributes"], [36, 34, 73, 34], [36, 37, 74, 4, "require"], [36, 44, 74, 11], [36, 45, 74, 11, "_dependencyMap"], [36, 59, 74, 11], [36, 111, 74, 59], [36, 112, 74, 60], [36, 113, 74, 61, "default"], [36, 120, 74, 68], [37, 4, 75, 2], [37, 8, 75, 8, "resolveRNStyle"], [37, 22, 75, 22], [37, 25, 75, 25, "require"], [37, 32, 75, 32], [37, 33, 75, 32, "_dependencyMap"], [37, 47, 75, 32], [37, 80, 75, 61], [37, 81, 75, 62], [37, 82, 75, 63, "default"], [37, 89, 75, 70], [38, 4, 77, 2], [38, 13, 77, 11, "handleReactDevToolsSettingsUpdate"], [38, 46, 77, 44, "handleReactDevToolsSettingsUpdate"], [38, 47, 77, 45, "settings"], [38, 55, 77, 61], [38, 57, 77, 63], [39, 6, 78, 4, "reactDevToolsSettingsManager"], [39, 34, 78, 32], [39, 35, 78, 33, "setGlobalHookSettings"], [39, 56, 78, 54], [39, 57, 79, 6, "JSON"], [39, 61, 79, 10], [39, 62, 79, 11, "stringify"], [39, 71, 79, 20], [39, 72, 79, 21, "settings"], [39, 80, 79, 29], [39, 81, 80, 4], [39, 82, 80, 5], [40, 4, 81, 2], [41, 4, 83, 2], [41, 8, 83, 6, "disconnect"], [41, 18, 83, 16], [41, 21, 83, 19], [41, 25, 83, 23], [42, 4, 84, 2], [42, 13, 84, 11, "disconnectBackendFromReactDevToolsInFuseboxIfNeeded"], [42, 64, 84, 62, "disconnectBackendFromReactDevToolsInFuseboxIfNeeded"], [42, 65, 84, 62], [42, 67, 84, 65], [43, 6, 85, 4], [43, 10, 85, 8, "disconnect"], [43, 20, 85, 18], [43, 24, 85, 22], [43, 28, 85, 26], [43, 30, 85, 28], [44, 8, 86, 6, "disconnect"], [44, 18, 86, 16], [44, 19, 86, 17], [44, 20, 86, 18], [45, 8, 87, 6, "disconnect"], [45, 18, 87, 16], [45, 21, 87, 19], [45, 25, 87, 23], [46, 6, 88, 4], [47, 4, 89, 2], [48, 4, 91, 2], [48, 13, 91, 11, "connectToReactDevToolsInFusebox"], [48, 44, 91, 42, "connectToReactDevToolsInFusebox"], [48, 45, 91, 43, "domain"], [48, 51, 91, 57], [48, 53, 91, 59], [49, 6, 92, 4], [49, 10, 92, 4, "_readReloadAndProfile2"], [49, 32, 92, 4], [49, 35, 97, 8, "readReloadAndProfileConfig"], [49, 61, 97, 34], [49, 62, 98, 6, "maybeReactDevToolsRuntimeSettingsModuleModule"], [49, 107, 99, 4], [49, 108, 99, 5], [50, 8, 93, 6, "isReloadAndProfileSupported"], [50, 35, 93, 33], [50, 38, 93, 33, "_readReloadAndProfile2"], [50, 60, 93, 33], [50, 61, 93, 6, "isReloadAndProfileSupported"], [50, 88, 93, 33], [51, 8, 94, 6, "isProfiling"], [51, 19, 94, 17], [51, 22, 94, 17, "_readReloadAndProfile2"], [51, 44, 94, 17], [51, 45, 94, 6, "isProfiling"], [51, 56, 94, 17], [52, 8, 95, 6, "onReloadAndProfile"], [52, 26, 95, 24], [52, 29, 95, 24, "_readReloadAndProfile2"], [52, 51, 95, 24], [52, 52, 95, 6, "onReloadAndProfile"], [52, 70, 95, 24], [53, 8, 96, 6, "onReloadAndProfileFlagsReset"], [53, 36, 96, 34], [53, 39, 96, 34, "_readReloadAndProfile2"], [53, 61, 96, 34], [53, 62, 96, 6, "onReloadAndProfileFlagsReset"], [53, 90, 96, 34], [54, 6, 100, 4, "disconnect"], [54, 16, 100, 14], [54, 19, 100, 17, "connectWithCustomMessagingProtocol"], [54, 53, 100, 51], [54, 54, 100, 52], [55, 8, 101, 6, "onSubscribe"], [55, 19, 101, 17], [55, 21, 101, 19, "listener"], [55, 29, 101, 27], [55, 33, 101, 31], [56, 10, 102, 8, "domain"], [56, 16, 102, 14], [56, 17, 102, 15, "onMessage"], [56, 26, 102, 24], [56, 27, 102, 25, "addEventListener"], [56, 43, 102, 41], [56, 44, 102, 42, "listener"], [56, 52, 102, 50], [56, 53, 102, 51], [57, 8, 103, 6], [57, 9, 103, 7], [58, 8, 104, 6, "onUnsubscribe"], [58, 21, 104, 19], [58, 23, 104, 21, "listener"], [58, 31, 104, 29], [58, 35, 104, 33], [59, 10, 105, 8, "domain"], [59, 16, 105, 14], [59, 17, 105, 15, "onMessage"], [59, 26, 105, 24], [59, 27, 105, 25, "removeEventListener"], [59, 46, 105, 44], [59, 47, 105, 45, "listener"], [59, 55, 105, 53], [59, 56, 105, 54], [60, 8, 106, 6], [60, 9, 106, 7], [61, 8, 107, 6, "onMessage"], [61, 17, 107, 15], [61, 19, 107, 17, "onMessage"], [61, 20, 107, 18, "event"], [61, 25, 107, 23], [61, 27, 107, 25, "payload"], [61, 34, 107, 32], [61, 39, 107, 37], [62, 10, 108, 8, "domain"], [62, 16, 108, 14], [62, 17, 108, 15, "sendMessage"], [62, 28, 108, 26], [62, 29, 108, 27], [63, 12, 108, 28, "event"], [63, 17, 108, 33], [64, 12, 108, 35, "payload"], [65, 10, 108, 42], [65, 11, 108, 43], [65, 12, 108, 44], [66, 8, 109, 6], [66, 9, 109, 7], [67, 8, 110, 6, "nativeStyleEditorValidAttributes"], [67, 40, 110, 38], [67, 42, 110, 40, "Object"], [67, 48, 110, 46], [67, 49, 110, 47, "keys"], [67, 53, 110, 51], [67, 54, 110, 52, "ReactNativeStyleAttributes"], [67, 80, 110, 78], [67, 81, 110, 79], [68, 8, 111, 6, "resolveRNStyle"], [68, 22, 111, 20], [69, 8, 112, 6, "onSettingsUpdated"], [69, 25, 112, 23], [69, 27, 112, 25, "handleReactDevToolsSettingsUpdate"], [69, 60, 112, 58], [70, 8, 113, 6, "isReloadAndProfileSupported"], [70, 35, 113, 33], [71, 8, 114, 6, "isProfiling"], [71, 19, 114, 17], [72, 8, 115, 6, "onReloadAndProfile"], [72, 26, 115, 24], [73, 8, 116, 6, "onReloadAndProfileFlagsReset"], [74, 6, 117, 4], [74, 7, 117, 5], [74, 8, 117, 6], [75, 4, 118, 2], [76, 4, 120, 2], [76, 8, 120, 6, "isWebSocketOpen"], [76, 23, 120, 21], [76, 26, 120, 24], [76, 31, 120, 29], [77, 4, 121, 2], [77, 8, 121, 6, "ws"], [77, 10, 121, 8], [77, 13, 121, 11], [77, 17, 121, 15], [78, 4, 122, 2], [78, 13, 122, 11, "connectToWSBasedReactDevToolsFrontend"], [78, 50, 122, 48, "connectToWSBasedReactDevToolsFrontend"], [78, 51, 122, 48], [78, 53, 122, 51], [79, 6, 123, 4], [79, 10, 123, 8, "ws"], [79, 12, 123, 10], [79, 17, 123, 15], [79, 21, 123, 19], [79, 25, 123, 23, "isWebSocketOpen"], [79, 40, 123, 38], [79, 42, 123, 40], [80, 8, 128, 6], [81, 6, 129, 4], [82, 6, 133, 4], [82, 10, 133, 8], [82, 11, 133, 9, "window"], [82, 17, 133, 15], [82, 18, 133, 16, "document"], [82, 26, 133, 24], [82, 28, 133, 26], [83, 8, 134, 6], [83, 12, 134, 12, "AppState"], [83, 20, 134, 20], [83, 23, 134, 23, "require"], [83, 30, 134, 30], [83, 31, 134, 30, "_dependencyMap"], [83, 45, 134, 30], [83, 72, 134, 53], [83, 73, 134, 54], [83, 74, 134, 55, "default"], [83, 81, 134, 62], [84, 8, 135, 6], [84, 12, 135, 12, "getDevServer"], [84, 24, 135, 24], [84, 27, 135, 27, "require"], [84, 34, 135, 34], [84, 35, 135, 34, "_dependencyMap"], [84, 49, 135, 34], [84, 79, 135, 60], [84, 80, 135, 61], [84, 81, 135, 62, "default"], [84, 88, 135, 69], [85, 8, 141, 6], [85, 12, 141, 12, "isAppActive"], [85, 23, 141, 23], [85, 26, 141, 26, "isAppActive"], [85, 27, 141, 26], [85, 32, 141, 32, "AppState"], [85, 40, 141, 40], [85, 41, 141, 41, "currentState"], [85, 53, 141, 53], [85, 58, 141, 58], [85, 70, 141, 70], [86, 8, 144, 6], [86, 12, 144, 12, "devServer"], [86, 21, 144, 21], [86, 24, 144, 24, "getDevServer"], [86, 36, 144, 36], [86, 37, 144, 37], [86, 38, 144, 38], [87, 8, 145, 6], [87, 12, 145, 12, "host"], [87, 16, 145, 16], [87, 19, 145, 19, "devServer"], [87, 28, 145, 28], [87, 29, 145, 29, "bundleLoadedFromServer"], [87, 51, 145, 51], [87, 54, 146, 10, "devServer"], [87, 63, 146, 19], [87, 64, 146, 20, "url"], [87, 67, 146, 23], [87, 68, 147, 13, "replace"], [87, 75, 147, 20], [87, 76, 147, 21], [87, 89, 147, 34], [87, 91, 147, 36], [87, 93, 147, 38], [87, 94, 147, 39], [87, 95, 148, 13, "replace"], [87, 102, 148, 20], [87, 103, 148, 21], [87, 108, 148, 26], [87, 110, 148, 28], [87, 112, 148, 30], [87, 113, 148, 31], [87, 114, 149, 13, "split"], [87, 119, 149, 18], [87, 120, 149, 19], [87, 123, 149, 22], [87, 124, 149, 23], [87, 125, 149, 24], [87, 126, 149, 25], [87, 127, 149, 26], [87, 130, 150, 10], [87, 141, 150, 21], [88, 8, 154, 6], [88, 12, 154, 12, "port"], [88, 16, 154, 16], [88, 19, 157, 8, "window"], [88, 25, 157, 14], [88, 26, 157, 15, "__REACT_DEVTOOLS_PORT__"], [88, 49, 157, 38], [88, 53, 157, 42], [88, 57, 157, 46], [88, 60, 158, 12, "window"], [88, 66, 158, 18], [88, 67, 158, 19, "__REACT_DEVTOOLS_PORT__"], [88, 90, 158, 42], [88, 93, 159, 12], [88, 97, 159, 16], [89, 8, 161, 6], [89, 12, 161, 12, "WebSocket"], [89, 21, 161, 21], [89, 24, 161, 24, "require"], [89, 31, 161, 31], [89, 32, 161, 31, "_dependencyMap"], [89, 46, 161, 31], [89, 75, 161, 56], [89, 76, 161, 57], [89, 77, 161, 58, "default"], [89, 84, 161, 65], [90, 8, 162, 6, "ws"], [90, 10, 162, 8], [90, 13, 162, 11], [90, 17, 162, 15, "WebSocket"], [90, 26, 162, 24], [90, 27, 162, 25], [90, 34, 162, 32], [90, 37, 162, 35, "host"], [90, 41, 162, 39], [90, 44, 162, 42], [90, 47, 162, 45], [90, 50, 162, 48, "port"], [90, 54, 162, 52], [90, 55, 162, 53], [91, 8, 163, 6, "ws"], [91, 10, 163, 8], [91, 11, 163, 9, "addEventListener"], [91, 27, 163, 25], [91, 28, 163, 26], [91, 35, 163, 33], [91, 37, 163, 35, "event"], [91, 42, 163, 40], [91, 46, 163, 44], [92, 10, 164, 8, "isWebSocketOpen"], [92, 25, 164, 23], [92, 28, 164, 26], [92, 33, 164, 31], [93, 8, 165, 6], [93, 9, 165, 7], [93, 10, 165, 8], [94, 8, 166, 6, "ws"], [94, 10, 166, 8], [94, 11, 166, 9, "addEventListener"], [94, 27, 166, 25], [94, 28, 166, 26], [94, 34, 166, 32], [94, 36, 166, 34, "event"], [94, 41, 166, 39], [94, 45, 166, 43], [95, 10, 167, 8, "isWebSocketOpen"], [95, 25, 167, 23], [95, 28, 167, 26], [95, 32, 167, 30], [96, 8, 168, 6], [96, 9, 168, 7], [96, 10, 168, 8], [97, 8, 170, 6], [97, 12, 170, 6, "_readReloadAndProfile3"], [97, 34, 170, 6], [97, 37, 175, 10, "readReloadAndProfileConfig"], [97, 63, 175, 36], [97, 64, 176, 8, "maybeReactDevToolsRuntimeSettingsModuleModule"], [97, 109, 177, 6], [97, 110, 177, 7], [98, 10, 171, 8, "isReloadAndProfileSupported"], [98, 37, 171, 35], [98, 40, 171, 35, "_readReloadAndProfile3"], [98, 62, 171, 35], [98, 63, 171, 8, "isReloadAndProfileSupported"], [98, 90, 171, 35], [99, 10, 172, 8, "isProfiling"], [99, 21, 172, 19], [99, 24, 172, 19, "_readReloadAndProfile3"], [99, 46, 172, 19], [99, 47, 172, 8, "isProfiling"], [99, 58, 172, 19], [100, 10, 173, 8, "onReloadAndProfile"], [100, 28, 173, 26], [100, 31, 173, 26, "_readReloadAndProfile3"], [100, 53, 173, 26], [100, 54, 173, 8, "onReloadAndProfile"], [100, 72, 173, 26], [101, 10, 174, 8, "onReloadAndProfileFlagsReset"], [101, 38, 174, 36], [101, 41, 174, 36, "_readReloadAndProfile3"], [101, 63, 174, 36], [101, 64, 174, 8, "onReloadAndProfileFlagsReset"], [101, 92, 174, 36], [102, 8, 178, 6, "connectToDevTools"], [102, 25, 178, 23], [102, 26, 178, 24], [103, 10, 179, 8, "isAppActive"], [103, 21, 179, 19], [104, 10, 180, 8, "resolveRNStyle"], [104, 24, 180, 22], [105, 10, 181, 8, "nativeStyleEditorValidAttributes"], [105, 42, 181, 40], [105, 44, 181, 42, "Object"], [105, 50, 181, 48], [105, 51, 181, 49, "keys"], [105, 55, 181, 53], [105, 56, 182, 10, "ReactNativeStyleAttributes"], [105, 82, 183, 8], [105, 83, 183, 9], [106, 10, 184, 8, "websocket"], [106, 19, 184, 17], [106, 21, 184, 19, "ws"], [106, 23, 184, 21], [107, 10, 185, 8, "onSettingsUpdated"], [107, 27, 185, 25], [107, 29, 185, 27, "handleReactDevToolsSettingsUpdate"], [107, 62, 185, 60], [108, 10, 186, 8, "isReloadAndProfileSupported"], [108, 37, 186, 35], [109, 10, 187, 8, "isProfiling"], [109, 21, 187, 19], [110, 10, 188, 8, "onReloadAndProfile"], [110, 28, 188, 26], [111, 10, 189, 8, "onReloadAndProfileFlagsReset"], [112, 8, 190, 6], [112, 9, 190, 7], [112, 10, 190, 8], [113, 6, 191, 4], [114, 4, 192, 2], [115, 4, 195, 2], [115, 8, 195, 6, "global"], [115, 14, 195, 12], [115, 15, 195, 13, "reactDevToolsFuseboxGlobalBindingName"], [115, 52, 195, 50], [115, 53, 195, 51], [115, 57, 195, 55], [115, 61, 195, 59], [115, 63, 195, 61], [116, 6, 196, 4, "disconnectBackendFromReactDevToolsInFuseboxIfNeeded"], [116, 57, 196, 55], [116, 58, 196, 56], [116, 59, 196, 57], [117, 6, 197, 4], [117, 10, 197, 10, "domain"], [117, 16, 197, 16], [117, 19, 198, 6, "fuseboxReactDevToolsDispatcher"], [117, 49, 198, 36], [117, 50, 198, 37, "initializeDomain"], [117, 66, 198, 53], [117, 67, 198, 54], [117, 83, 198, 70], [117, 84, 198, 71], [118, 6, 199, 4, "connectToReactDevToolsInFusebox"], [118, 37, 199, 35], [118, 38, 199, 36, "domain"], [118, 44, 199, 42], [118, 45, 199, 43], [119, 4, 200, 2], [120, 4, 204, 2, "global"], [120, 10, 204, 8], [120, 11, 204, 9, "__FUSEBOX_REACT_DEVTOOLS_DISPATCHER__"], [120, 48, 204, 46], [120, 49, 204, 47, "onDomainInitialization"], [120, 71, 204, 69], [120, 72, 204, 70, "addEventListener"], [120, 88, 204, 86], [120, 89, 205, 5, "domain"], [120, 95, 205, 19], [120, 99, 205, 24], [121, 6, 206, 6], [121, 10, 206, 10, "domain"], [121, 16, 206, 16], [121, 17, 206, 17, "name"], [121, 21, 206, 21], [121, 26, 206, 26], [121, 42, 206, 42], [121, 44, 206, 44], [122, 8, 207, 8, "disconnectBackendFromReactDevToolsInFuseboxIfNeeded"], [122, 59, 207, 59], [122, 60, 207, 60], [122, 61, 207, 61], [123, 8, 208, 8, "connectToReactDevToolsInFusebox"], [123, 39, 208, 39], [123, 40, 208, 40, "domain"], [123, 46, 208, 46], [123, 47, 208, 47], [124, 6, 209, 6], [125, 4, 210, 4], [125, 5, 211, 2], [125, 6, 211, 3], [126, 4, 214, 2], [126, 8, 214, 8, "RCTNativeAppEventEmitter"], [126, 32, 214, 32], [126, 35, 215, 4, "require"], [126, 42, 215, 11], [126, 43, 215, 11, "_dependencyMap"], [126, 57, 215, 11], [126, 104, 215, 54], [126, 105, 215, 55], [126, 106, 215, 56, "default"], [126, 113, 215, 63], [127, 4, 216, 2, "RCTNativeAppEventEmitter"], [127, 28, 216, 26], [127, 29, 216, 27, "addListener"], [127, 40, 216, 38], [127, 41, 217, 4], [127, 58, 217, 21], [127, 60, 218, 4, "connectToWSBasedReactDevToolsFrontend"], [127, 97, 219, 2], [127, 98, 219, 3], [128, 4, 220, 2, "connectToWSBasedReactDevToolsFrontend"], [128, 41, 220, 39], [128, 42, 220, 40], [128, 43, 220, 41], [129, 2, 221, 0], [130, 2, 223, 0], [130, 11, 223, 9, "readReloadAndProfileConfig"], [130, 37, 223, 35, "readReloadAndProfileConfig"], [130, 38, 224, 2, "maybeModule"], [130, 49, 224, 60], [130, 51, 225, 2], [131, 4, 226, 2], [131, 8, 226, 8, "isReloadAndProfileSupported"], [131, 35, 226, 35], [131, 38, 226, 38, "maybeModule"], [131, 49, 226, 49], [131, 53, 226, 53], [131, 57, 226, 57], [132, 4, 227, 2], [132, 8, 227, 8, "config"], [132, 14, 227, 14], [132, 17, 227, 17, "maybeModule"], [132, 28, 227, 28], [132, 30, 227, 30, "getReloadAndProfileConfig"], [132, 55, 227, 55], [132, 56, 227, 56], [132, 57, 227, 57], [133, 4, 228, 2], [133, 8, 228, 8, "isProfiling"], [133, 19, 228, 19], [133, 22, 228, 22, "config"], [133, 28, 228, 28], [133, 30, 228, 30, "shouldReloadAndProfile"], [133, 52, 228, 52], [133, 57, 228, 57], [133, 61, 228, 61], [134, 4, 229, 2], [134, 8, 229, 8, "profilingSettings"], [134, 25, 229, 25], [134, 28, 229, 28], [135, 6, 230, 4, "recordChangeDescriptions"], [135, 30, 230, 28], [135, 32, 230, 30, "config"], [135, 38, 230, 36], [135, 40, 230, 38, "recordChangeDescriptions"], [135, 64, 230, 62], [135, 69, 230, 67], [135, 73, 230, 71], [136, 6, 231, 4, "recordTimeline"], [136, 20, 231, 18], [136, 22, 231, 20], [137, 4, 232, 2], [137, 5, 232, 3], [138, 4, 233, 2], [138, 8, 233, 8, "onReloadAndProfile"], [138, 26, 233, 26], [138, 29, 233, 30, "recordChangeDescriptions"], [138, 53, 233, 63], [138, 57, 233, 68], [139, 6, 234, 4], [139, 10, 234, 8, "maybeModule"], [139, 21, 234, 19], [139, 25, 234, 23], [139, 29, 234, 27], [139, 31, 234, 29], [140, 8, 235, 6], [141, 6, 236, 4], [142, 6, 238, 4, "maybeModule"], [142, 17, 238, 15], [142, 18, 238, 16, "setReloadAndProfileConfig"], [142, 43, 238, 41], [142, 44, 238, 42], [143, 8, 239, 6, "shouldReloadAndProfile"], [143, 30, 239, 28], [143, 32, 239, 30], [143, 36, 239, 34], [144, 8, 240, 6, "recordChangeDescriptions"], [145, 6, 241, 4], [145, 7, 241, 5], [145, 8, 241, 6], [146, 4, 242, 2], [146, 5, 242, 3], [147, 4, 243, 2], [147, 8, 243, 8, "onReloadAndProfileFlagsReset"], [147, 36, 243, 36], [147, 39, 243, 39, "onReloadAndProfileFlagsReset"], [147, 40, 243, 39], [147, 45, 243, 45], [148, 6, 244, 4], [148, 10, 244, 8, "maybeModule"], [148, 21, 244, 19], [148, 25, 244, 23], [148, 29, 244, 27], [148, 31, 244, 29], [149, 8, 245, 6], [150, 6, 246, 4], [151, 6, 248, 4, "maybeModule"], [151, 17, 248, 15], [151, 18, 248, 16, "setReloadAndProfileConfig"], [151, 43, 248, 41], [151, 44, 248, 42], [152, 8, 249, 6, "shouldReloadAndProfile"], [152, 30, 249, 28], [152, 32, 249, 30], [152, 37, 249, 35], [153, 8, 250, 6, "recordChangeDescriptions"], [153, 32, 250, 30], [153, 34, 250, 32], [154, 6, 251, 4], [154, 7, 251, 5], [154, 8, 251, 6], [155, 4, 252, 2], [155, 5, 252, 3], [156, 4, 254, 2], [156, 11, 254, 9], [157, 6, 255, 4, "isReloadAndProfileSupported"], [157, 33, 255, 31], [158, 6, 256, 4, "isProfiling"], [158, 17, 256, 15], [159, 6, 257, 4, "profilingSettings"], [159, 23, 257, 21], [160, 6, 258, 4, "onReloadAndProfile"], [160, 24, 258, 22], [161, 6, 259, 4, "onReloadAndProfileFlagsReset"], [162, 4, 260, 2], [162, 5, 260, 3], [163, 2, 261, 0], [164, 0, 261, 1], [164, 3]], "functionMap": {"names": ["<global>", "handleReactDevToolsSettingsUpdate", "disconnectBackendFromReactDevToolsInFuseboxIfNeeded", "connectToReactDevToolsInFusebox", "connectWithCustomMessagingProtocol$argument_0.onSubscribe", "connectWithCustomMessagingProtocol$argument_0.onUnsubscribe", "connectWithCustomMessagingProtocol$argument_0.onMessage", "connectToWSBasedReactDevToolsFrontend", "isAppActive", "ws.addEventListener$argument_1", "global.__FUSEBOX_REACT_DEVTOOLS_DISPATCHER__.onDomainInitialization.addEventListener$argument_0", "readReloadAndProfileConfig", "onReloadAndProfile", "onReloadAndProfileFlagsReset"], "mappings": "AAA;EC4E;GDI;EEG;GFK;EGE;mBCU;ODE;qBEC;OFE;iBGC;OHE;GHS;EOI;0BCmB,4CD;mCEsB;OFE;kCEC;OFE;GPwB;IUa;KVK;AWa;6BCU;GDS;uCEC;GFS"}}, "type": "js/module"}]}