{"dependencies": [{"name": "./elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 48, "column": 20, "index": 619}}], "key": "Jzo/Yd6BZZ7INdCboGTp5X+67rE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.tags = void 0;\n  var _elements = require(_dependencyMap[0], \"./elements\");\n  var tags = exports.tags = {\n    circle: _elements.Circle,\n    clipPath: _elements.ClipPath,\n    defs: _elements.Defs,\n    ellipse: _elements.Ellipse,\n    filter: _elements.Filter,\n    feBlend: _elements.FeBlend,\n    feColorMatrix: _elements.FeColorMatrix,\n    feComponentTransfer: _elements.FeComponentTransfer,\n    feComposite: _elements.FeComposite,\n    feConvolveMatrix: _elements.FeConvolveMatrix,\n    feDiffuseLighting: _elements.FeDiffuseLighting,\n    feDisplacementMap: _elements.FeDisplacementMap,\n    feDistantLight: _elements.FeDistantLight,\n    feDropShadow: _elements.FeDropShadow,\n    feFlood: _elements.FeFlood,\n    feGaussianBlur: _elements.FeGaussianBlur,\n    feImage: _elements.FeImage,\n    feMerge: _elements.FeMerge,\n    feMergeNode: _elements.FeMergeNode,\n    feMorphology: _elements.FeMorphology,\n    feOffset: _elements.FeOffset,\n    fePointLight: _elements.FePointLight,\n    feSpecularLighting: _elements.FeSpecularLighting,\n    feSpotLight: _elements.FeSpotLight,\n    feTile: _elements.FeTile,\n    feTurbulence: _elements.FeTurbulence,\n    foreignObject: _elements.ForeignObject,\n    g: _elements.G,\n    image: _elements.Image,\n    line: _elements.Line,\n    linearGradient: _elements.LinearGradient,\n    marker: _elements.Marker,\n    mask: _elements.Mask,\n    path: _elements.Path,\n    pattern: _elements.Pattern,\n    polygon: _elements.Polygon,\n    polyline: _elements.Polyline,\n    radialGradient: _elements.RadialGradient,\n    rect: _elements.Rect,\n    stop: _elements.Stop,\n    svg: _elements.Svg,\n    symbol: _elements.Symbol,\n    text: _elements.Text,\n    textPath: _elements.TextPath,\n    tspan: _elements.TSpan,\n    use: _elements.Use\n  };\n});", "lineCount": 55, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_elements"], [6, 15, 1, 0], [6, 18, 1, 0, "require"], [6, 25, 1, 0], [6, 26, 1, 0, "_dependencyMap"], [6, 40, 1, 0], [7, 2, 50, 7], [7, 6, 50, 13, "tags"], [7, 10, 50, 17], [7, 13, 50, 17, "exports"], [7, 20, 50, 17], [7, 21, 50, 17, "tags"], [7, 25, 50, 17], [7, 28, 50, 20], [8, 4, 51, 2, "circle"], [8, 10, 51, 8], [8, 12, 51, 10, "Circle"], [8, 28, 51, 16], [9, 4, 52, 2, "clipPath"], [9, 12, 52, 10], [9, 14, 52, 12, "<PERSON><PERSON><PERSON><PERSON>"], [9, 32, 52, 20], [10, 4, 53, 2, "defs"], [10, 8, 53, 6], [10, 10, 53, 8, "Defs"], [10, 24, 53, 12], [11, 4, 54, 2, "ellipse"], [11, 11, 54, 9], [11, 13, 54, 11, "Ellipse"], [11, 30, 54, 18], [12, 4, 55, 2, "filter"], [12, 10, 55, 8], [12, 12, 55, 10, "Filter"], [12, 28, 55, 16], [13, 4, 56, 2, "feBlend"], [13, 11, 56, 9], [13, 13, 56, 11, "FeBlend"], [13, 30, 56, 18], [14, 4, 57, 2, "feColorMatrix"], [14, 17, 57, 15], [14, 19, 57, 17, "FeColorMatrix"], [14, 42, 57, 30], [15, 4, 58, 2, "feComponentTransfer"], [15, 23, 58, 21], [15, 25, 58, 23, "FeComponentTransfer"], [15, 54, 58, 42], [16, 4, 59, 2, "feComposite"], [16, 15, 59, 13], [16, 17, 59, 15, "FeComposite"], [16, 38, 59, 26], [17, 4, 60, 2, "feConvolveMatrix"], [17, 20, 60, 18], [17, 22, 60, 20, "FeConvolveMatrix"], [17, 48, 60, 36], [18, 4, 61, 2, "feDiffuseLighting"], [18, 21, 61, 19], [18, 23, 61, 21, "FeDiffuseLighting"], [18, 50, 61, 38], [19, 4, 62, 2, "feDisplacementMap"], [19, 21, 62, 19], [19, 23, 62, 21, "FeDisplacementMap"], [19, 50, 62, 38], [20, 4, 63, 2, "feDistantLight"], [20, 18, 63, 16], [20, 20, 63, 18, "FeDistantLight"], [20, 44, 63, 32], [21, 4, 64, 2, "feDropShadow"], [21, 16, 64, 14], [21, 18, 64, 16, "FeDropShadow"], [21, 40, 64, 28], [22, 4, 65, 2, "feFlood"], [22, 11, 65, 9], [22, 13, 65, 11, "FeFlood"], [22, 30, 65, 18], [23, 4, 66, 2, "feG<PERSON><PERSON><PERSON>lur"], [23, 18, 66, 16], [23, 20, 66, 18, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 44, 66, 32], [24, 4, 67, 2, "feImage"], [24, 11, 67, 9], [24, 13, 67, 11, "FeImage"], [24, 30, 67, 18], [25, 4, 68, 2, "feMerge"], [25, 11, 68, 9], [25, 13, 68, 11, "FeMerge"], [25, 30, 68, 18], [26, 4, 69, 2, "feMergeNode"], [26, 15, 69, 13], [26, 17, 69, 15, "FeMergeNode"], [26, 38, 69, 26], [27, 4, 70, 2, "feMorphology"], [27, 16, 70, 14], [27, 18, 70, 16, "FeMorphology"], [27, 40, 70, 28], [28, 4, 71, 2, "feOffset"], [28, 12, 71, 10], [28, 14, 71, 12, "FeOffset"], [28, 32, 71, 20], [29, 4, 72, 2, "fePointLight"], [29, 16, 72, 14], [29, 18, 72, 16, "FePointLight"], [29, 40, 72, 28], [30, 4, 73, 2, "feSpecularLighting"], [30, 22, 73, 20], [30, 24, 73, 22, "FeSpecularLighting"], [30, 52, 73, 40], [31, 4, 74, 2, "feSpotLight"], [31, 15, 74, 13], [31, 17, 74, 15, "FeSpotLight"], [31, 38, 74, 26], [32, 4, 75, 2, "feTile"], [32, 10, 75, 8], [32, 12, 75, 10, "FeTile"], [32, 28, 75, 16], [33, 4, 76, 2, "feTurbulence"], [33, 16, 76, 14], [33, 18, 76, 16, "FeTurbulence"], [33, 40, 76, 28], [34, 4, 77, 2, "foreignObject"], [34, 17, 77, 15], [34, 19, 77, 17, "ForeignObject"], [34, 42, 77, 30], [35, 4, 78, 2, "g"], [35, 5, 78, 3], [35, 7, 78, 5, "G"], [35, 18, 78, 6], [36, 4, 79, 2, "image"], [36, 9, 79, 7], [36, 11, 79, 9, "Image"], [36, 26, 79, 14], [37, 4, 80, 2, "line"], [37, 8, 80, 6], [37, 10, 80, 8, "Line"], [37, 24, 80, 12], [38, 4, 81, 2, "linearGradient"], [38, 18, 81, 16], [38, 20, 81, 18, "LinearGradient"], [38, 44, 81, 32], [39, 4, 82, 2, "marker"], [39, 10, 82, 8], [39, 12, 82, 10, "<PERSON><PERSON>"], [39, 28, 82, 16], [40, 4, 83, 2, "mask"], [40, 8, 83, 6], [40, 10, 83, 8, "Mask"], [40, 24, 83, 12], [41, 4, 84, 2, "path"], [41, 8, 84, 6], [41, 10, 84, 8, "Path"], [41, 24, 84, 12], [42, 4, 85, 2, "pattern"], [42, 11, 85, 9], [42, 13, 85, 11, "Pattern"], [42, 30, 85, 18], [43, 4, 86, 2, "polygon"], [43, 11, 86, 9], [43, 13, 86, 11, "Polygon"], [43, 30, 86, 18], [44, 4, 87, 2, "polyline"], [44, 12, 87, 10], [44, 14, 87, 12, "Polyline"], [44, 32, 87, 20], [45, 4, 88, 2, "radialGradient"], [45, 18, 88, 16], [45, 20, 88, 18, "RadialGrad<PERSON>"], [45, 44, 88, 32], [46, 4, 89, 2, "rect"], [46, 8, 89, 6], [46, 10, 89, 8, "Rect"], [46, 24, 89, 12], [47, 4, 90, 2, "stop"], [47, 8, 90, 6], [47, 10, 90, 8, "Stop"], [47, 24, 90, 12], [48, 4, 91, 2, "svg"], [48, 7, 91, 5], [48, 9, 91, 7, "Svg"], [48, 22, 91, 10], [49, 4, 92, 2, "symbol"], [49, 10, 92, 8], [49, 12, 92, 10, "Symbol"], [49, 28, 92, 16], [50, 4, 93, 2, "text"], [50, 8, 93, 6], [50, 10, 93, 8, "Text"], [50, 24, 93, 12], [51, 4, 94, 2, "textPath"], [51, 12, 94, 10], [51, 14, 94, 12, "TextPath"], [51, 32, 94, 20], [52, 4, 95, 2, "tspan"], [52, 9, 95, 7], [52, 11, 95, 9, "TSpan"], [52, 26, 95, 14], [53, 4, 96, 2, "use"], [53, 7, 96, 5], [53, 9, 96, 7, "Use"], [54, 2, 97, 0], [54, 3, 97, 10], [55, 0, 97, 11], [55, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}