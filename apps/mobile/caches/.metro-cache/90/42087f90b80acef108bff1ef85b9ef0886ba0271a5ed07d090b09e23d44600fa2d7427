{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const TableProperties = exports.default = (0, _createLucideIcon.default)(\"TableProperties\", [[\"path\", {\n    d: \"M15 3v18\",\n    key: \"14nvp0\"\n  }], [\"rect\", {\n    width: \"18\",\n    height: \"18\",\n    x: \"3\",\n    y: \"3\",\n    rx: \"2\",\n    key: \"afitv7\"\n  }], [\"path\", {\n    d: \"M21 9H3\",\n    key: \"1338ky\"\n  }], [\"path\", {\n    d: \"M21 15H3\",\n    key: \"9uk58r\"\n  }]]);\n});", "lineCount": 32, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "TableProperties"], [15, 23, 10, 21], [15, 26, 10, 21, "exports"], [15, 33, 10, 21], [15, 34, 10, 21, "default"], [15, 41, 10, 21], [15, 44, 10, 24], [15, 48, 10, 24, "createLucideIcon"], [15, 73, 10, 40], [15, 75, 10, 41], [15, 92, 10, 58], [15, 94, 10, 60], [15, 95, 11, 2], [15, 96, 11, 3], [15, 102, 11, 9], [15, 104, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 17, 11, 26], [17, 4, 11, 28, "key"], [17, 7, 11, 31], [17, 9, 11, 33], [18, 2, 11, 42], [18, 3, 11, 43], [18, 4, 11, 44], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "width"], [19, 9, 12, 18], [19, 11, 12, 20], [19, 15, 12, 24], [20, 4, 12, 26, "height"], [20, 10, 12, 32], [20, 12, 12, 34], [20, 16, 12, 38], [21, 4, 12, 40, "x"], [21, 5, 12, 41], [21, 7, 12, 43], [21, 10, 12, 46], [22, 4, 12, 48, "y"], [22, 5, 12, 49], [22, 7, 12, 51], [22, 10, 12, 54], [23, 4, 12, 56, "rx"], [23, 6, 12, 58], [23, 8, 12, 60], [23, 11, 12, 63], [24, 4, 12, 65, "key"], [24, 7, 12, 68], [24, 9, 12, 70], [25, 2, 12, 79], [25, 3, 12, 80], [25, 4, 12, 81], [25, 6, 13, 2], [25, 7, 13, 3], [25, 13, 13, 9], [25, 15, 13, 11], [26, 4, 13, 13, "d"], [26, 5, 13, 14], [26, 7, 13, 16], [26, 16, 13, 25], [27, 4, 13, 27, "key"], [27, 7, 13, 30], [27, 9, 13, 32], [28, 2, 13, 41], [28, 3, 13, 42], [28, 4, 13, 43], [28, 6, 14, 2], [28, 7, 14, 3], [28, 13, 14, 9], [28, 15, 14, 11], [29, 4, 14, 13, "d"], [29, 5, 14, 14], [29, 7, 14, 16], [29, 17, 14, 26], [30, 4, 14, 28, "key"], [30, 7, 14, 31], [30, 9, 14, 33], [31, 2, 14, 42], [31, 3, 14, 43], [31, 4, 14, 44], [31, 5, 15, 1], [31, 6, 15, 2], [32, 0, 15, 3], [32, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}