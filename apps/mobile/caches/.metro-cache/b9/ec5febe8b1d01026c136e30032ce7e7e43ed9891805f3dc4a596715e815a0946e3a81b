{"dependencies": [{"name": "../logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "pBiviTeVoxyQBwxnAV5OZFjetV0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RNScreensTurboModule = void 0;\n  var _index = require(_dependencyMap[0], \"../logger/index.js\");\n  const _worklet_16972810443201_init_data = {\n    code: \"function reactNativeReanimated_RNScreensTurboModuleJs1(){const{logger,defaultReturnValue}=this.__closure;logger.warn('RNScreensTurboModule has not been found. Check that you have installed `react-native-screens@3.30.0` or newer in your project and rebuilt your app.');return defaultReturnValue;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/RNScreensTurboModule.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_RNScreensTurboModuleJs1\\\",\\\"logger\\\",\\\"defaultReturnValue\\\",\\\"__closure\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/RNScreensTurboModule.js\\\"],\\\"mappings\\\":\\\"AAIS,SAAAA,6CAAMA,CAAA,QAAAC,MAAA,CAAAC,kBAAA,OAAAC,SAAA,CAGXF,MAAM,CAACG,IAAI,CAAC,qJAAqJ,CAAC,CAClK,MAAO,CAAAF,kBAAkB,CAC3B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function noopFactory(defaultReturnValue) {\n    return function () {\n      const _e = [new global.Error(), -3, -27];\n      const reactNativeReanimated_RNScreensTurboModuleJs1 = function () {\n        _index.logger.warn('RNScreensTurboModule has not been found. Check that you have installed `react-native-screens@3.30.0` or newer in your project and rebuilt your app.');\n        return defaultReturnValue;\n      };\n      reactNativeReanimated_RNScreensTurboModuleJs1.__closure = {\n        logger: _index.logger,\n        defaultReturnValue\n      };\n      reactNativeReanimated_RNScreensTurboModuleJs1.__workletHash = 16972810443201;\n      reactNativeReanimated_RNScreensTurboModuleJs1.__initData = _worklet_16972810443201_init_data;\n      reactNativeReanimated_RNScreensTurboModuleJs1.__stackDetails = _e;\n      return reactNativeReanimated_RNScreensTurboModuleJs1;\n    }();\n  }\n  const RNScreensTurboModule = exports.RNScreensTurboModule = global.RNScreensTurboModule || {\n    startTransition: noopFactory({\n      topScreenId: -1,\n      belowTopScreenId: -1,\n      canStartTransition: false\n    }),\n    updateTransition: noopFactory(),\n    finishTransition: noopFactory()\n  };\n});", "lineCount": 41, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "RNScreensTurboModule"], [7, 30, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_index"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 3, 44], [9, 8, 3, 44, "_worklet_16972810443201_init_data"], [9, 41, 3, 44], [10, 4, 3, 44, "code"], [10, 8, 3, 44], [11, 4, 3, 44, "location"], [11, 12, 3, 44], [12, 4, 3, 44, "sourceMap"], [12, 13, 3, 44], [13, 4, 3, 44, "version"], [13, 11, 3, 44], [14, 2, 3, 44], [15, 2, 4, 0], [15, 11, 4, 9, "noopFactory"], [15, 22, 4, 20, "noopFactory"], [15, 23, 4, 21, "defaultReturnValue"], [15, 41, 4, 39], [15, 43, 4, 41], [16, 4, 5, 2], [16, 11, 5, 9], [17, 6, 5, 9], [17, 12, 5, 9, "_e"], [17, 14, 5, 9], [17, 22, 5, 9, "global"], [17, 28, 5, 9], [17, 29, 5, 9, "Error"], [17, 34, 5, 9], [18, 6, 5, 9], [18, 12, 5, 9, "reactNativeReanimated_RNScreensTurboModuleJs1"], [18, 57, 5, 9], [18, 69, 5, 9, "reactNativeReanimated_RNScreensTurboModuleJs1"], [18, 70, 5, 9], [18, 72, 5, 15], [19, 8, 8, 4, "logger"], [19, 21, 8, 10], [19, 22, 8, 11, "warn"], [19, 26, 8, 15], [19, 27, 8, 16], [19, 176, 8, 165], [19, 177, 8, 166], [20, 8, 9, 4], [20, 15, 9, 11, "defaultReturnValue"], [20, 33, 9, 29], [21, 6, 10, 2], [21, 7, 10, 3], [22, 6, 10, 3, "reactNativeReanimated_RNScreensTurboModuleJs1"], [22, 51, 10, 3], [22, 52, 10, 3, "__closure"], [22, 61, 10, 3], [23, 8, 10, 3, "logger"], [23, 14, 10, 3], [23, 16, 8, 4, "logger"], [23, 29, 8, 10], [24, 8, 8, 10, "defaultReturnValue"], [25, 6, 8, 10], [26, 6, 8, 10, "reactNativeReanimated_RNScreensTurboModuleJs1"], [26, 51, 8, 10], [26, 52, 8, 10, "__workletHash"], [26, 65, 8, 10], [27, 6, 8, 10, "reactNativeReanimated_RNScreensTurboModuleJs1"], [27, 51, 8, 10], [27, 52, 8, 10, "__initData"], [27, 62, 8, 10], [27, 65, 8, 10, "_worklet_16972810443201_init_data"], [27, 98, 8, 10], [28, 6, 8, 10, "reactNativeReanimated_RNScreensTurboModuleJs1"], [28, 51, 8, 10], [28, 52, 8, 10, "__stackDetails"], [28, 66, 8, 10], [28, 69, 8, 10, "_e"], [28, 71, 8, 10], [29, 6, 8, 10], [29, 13, 8, 10, "reactNativeReanimated_RNScreensTurboModuleJs1"], [29, 58, 8, 10], [30, 4, 8, 10], [30, 5, 5, 9], [31, 2, 11, 0], [32, 2, 12, 7], [32, 8, 12, 13, "RNScreensTurboModule"], [32, 28, 12, 33], [32, 31, 12, 33, "exports"], [32, 38, 12, 33], [32, 39, 12, 33, "RNScreensTurboModule"], [32, 59, 12, 33], [32, 62, 12, 36, "global"], [32, 68, 12, 42], [32, 69, 12, 43, "RNScreensTurboModule"], [32, 89, 12, 63], [32, 93, 12, 67], [33, 4, 13, 2, "startTransition"], [33, 19, 13, 17], [33, 21, 13, 19, "noopFactory"], [33, 32, 13, 30], [33, 33, 13, 31], [34, 6, 14, 4, "topScreenId"], [34, 17, 14, 15], [34, 19, 14, 17], [34, 20, 14, 18], [34, 21, 14, 19], [35, 6, 15, 4, "belowTopScreenId"], [35, 22, 15, 20], [35, 24, 15, 22], [35, 25, 15, 23], [35, 26, 15, 24], [36, 6, 16, 4, "canStartTransition"], [36, 24, 16, 22], [36, 26, 16, 24], [37, 4, 17, 2], [37, 5, 17, 3], [37, 6, 17, 4], [38, 4, 18, 2, "updateTransition"], [38, 20, 18, 18], [38, 22, 18, 20, "noopFactory"], [38, 33, 18, 31], [38, 34, 18, 32], [38, 35, 18, 33], [39, 4, 19, 2, "finishTransition"], [39, 20, 19, 18], [39, 22, 19, 20, "noopFactory"], [39, 33, 19, 31], [39, 34, 19, 32], [40, 2, 20, 0], [40, 3, 20, 1], [41, 0, 20, 2], [41, 3]], "functionMap": {"names": ["<global>", "noopFactory", "<anonymous>"], "mappings": "AAA;ACG;SCC;GDK;CDC"}}, "type": "js/module"}]}