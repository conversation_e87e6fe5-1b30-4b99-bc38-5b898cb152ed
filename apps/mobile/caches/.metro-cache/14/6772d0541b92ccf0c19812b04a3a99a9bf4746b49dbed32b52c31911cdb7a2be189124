{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const PointerOff = exports.default = (0, _createLucideIcon.default)(\"PointerOff\", [[\"path\", {\n    d: \"M10 4.5V4a2 2 0 0 0-2.41-1.957\",\n    key: \"jsi14n\"\n  }], [\"path\", {\n    d: \"M13.9 8.4a2 2 0 0 0-1.26-1.295\",\n    key: \"hirc7f\"\n  }], [\"path\", {\n    d: \"M21.7 16.2A8 8 0 0 0 22 14v-3a2 2 0 1 0-4 0v-1a2 2 0 0 0-3.63-1.158\",\n    key: \"1jxb2e\"\n  }], [\"path\", {\n    d: \"m7 15-1.8-1.8a2 2 0 0 0-2.79 2.86L6 19.7a7.74 7.74 0 0 0 6 2.3h2a8 8 0 0 0 5.657-2.343\",\n    key: \"10r7hm\"\n  }], [\"path\", {\n    d: \"M6 6v8\",\n    key: \"tv5xkp\"\n  }], [\"path\", {\n    d: \"m2 2 20 20\",\n    key: \"1ooewy\"\n  }]]);\n});", "lineCount": 34, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "<PERSON><PERSON><PERSON><PERSON>"], [15, 18, 10, 16], [15, 21, 10, 16, "exports"], [15, 28, 10, 16], [15, 29, 10, 16, "default"], [15, 36, 10, 16], [15, 39, 10, 19], [15, 43, 10, 19, "createLucideIcon"], [15, 68, 10, 35], [15, 70, 10, 36], [15, 82, 10, 48], [15, 84, 10, 50], [15, 85, 11, 2], [15, 86, 11, 3], [15, 92, 11, 9], [15, 94, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 39, 11, 48], [17, 4, 11, 50, "key"], [17, 7, 11, 53], [17, 9, 11, 55], [18, 2, 11, 64], [18, 3, 11, 65], [18, 4, 11, 66], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 39, 12, 48], [20, 4, 12, 50, "key"], [20, 7, 12, 53], [20, 9, 12, 55], [21, 2, 12, 64], [21, 3, 12, 65], [21, 4, 12, 66], [21, 6, 13, 2], [21, 7, 14, 4], [21, 13, 14, 10], [21, 15, 15, 4], [22, 4, 15, 6, "d"], [22, 5, 15, 7], [22, 7, 15, 9], [22, 76, 15, 78], [23, 4, 15, 80, "key"], [23, 7, 15, 83], [23, 9, 15, 85], [24, 2, 15, 94], [24, 3, 15, 95], [24, 4, 16, 3], [24, 6, 17, 2], [24, 7, 18, 4], [24, 13, 18, 10], [24, 15, 19, 4], [25, 4, 20, 6, "d"], [25, 5, 20, 7], [25, 7, 20, 9], [25, 95, 20, 97], [26, 4, 21, 6, "key"], [26, 7, 21, 9], [26, 9, 21, 11], [27, 2, 22, 4], [27, 3, 22, 5], [27, 4, 23, 3], [27, 6, 24, 2], [27, 7, 24, 3], [27, 13, 24, 9], [27, 15, 24, 11], [28, 4, 24, 13, "d"], [28, 5, 24, 14], [28, 7, 24, 16], [28, 15, 24, 24], [29, 4, 24, 26, "key"], [29, 7, 24, 29], [29, 9, 24, 31], [30, 2, 24, 40], [30, 3, 24, 41], [30, 4, 24, 42], [30, 6, 25, 2], [30, 7, 25, 3], [30, 13, 25, 9], [30, 15, 25, 11], [31, 4, 25, 13, "d"], [31, 5, 25, 14], [31, 7, 25, 16], [31, 19, 25, 28], [32, 4, 25, 30, "key"], [32, 7, 25, 33], [32, 9, 25, 35], [33, 2, 25, 44], [33, 3, 25, 45], [33, 4, 25, 46], [33, 5, 26, 1], [33, 6, 26, 2], [34, 0, 26, 3], [34, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}