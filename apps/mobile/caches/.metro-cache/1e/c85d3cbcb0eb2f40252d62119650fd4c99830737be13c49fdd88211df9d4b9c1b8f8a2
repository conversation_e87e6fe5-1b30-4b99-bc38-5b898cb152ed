{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  'use strict';\n\n  /**\n   * Used to find the indices of the frames that overlap the given offsets. Useful for finding the\n   * items that bound different windows of content, such as the visible area or the buffered overscan\n   * area.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.computeWindowedRenderLimits = computeWindowedRenderLimits;\n  exports.elementsThatOverlapOffsets = elementsThatOverlapOffsets;\n  exports.keyExtractor = keyExtractor;\n  exports.newRangeCount = newRangeCount;\n  function elementsThatOverlapOffsets(offsets, props, getFrameMetrics, zoomScale) {\n    if (zoomScale === void 0) {\n      zoomScale = 1;\n    }\n    var itemCount = props.getItemCount(props.data);\n    var result = [];\n    for (var offsetIndex = 0; offsetIndex < offsets.length; offsetIndex++) {\n      var currentOffset = offsets[offsetIndex];\n      var left = 0;\n      var right = itemCount - 1;\n      while (left <= right) {\n        // eslint-disable-next-line no-bitwise\n        var mid = left + (right - left >>> 1);\n        var frame = getFrameMetrics(mid, props);\n        var scaledOffsetStart = frame.offset * zoomScale;\n        var scaledOffsetEnd = (frame.offset + frame.length) * zoomScale;\n\n        // We want the first frame that contains the offset, with inclusive bounds. Thus, for the\n        // first frame the scaledOffsetStart is inclusive, while for other frames it is exclusive.\n        if (mid === 0 && currentOffset < scaledOffsetStart || mid !== 0 && currentOffset <= scaledOffsetStart) {\n          right = mid - 1;\n        } else if (currentOffset > scaledOffsetEnd) {\n          left = mid + 1;\n        } else {\n          result[offsetIndex] = mid;\n          break;\n        }\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Computes the number of elements in the `next` range that are new compared to the `prev` range.\n   * Handy for calculating how many new items will be rendered when the render window changes so we\n   * can restrict the number of new items render at once so that content can appear on the screen\n   * faster.\n   */\n  function newRangeCount(prev, next) {\n    return next.last - next.first + 1 - Math.max(0, 1 + Math.min(next.last, prev.last) - Math.max(next.first, prev.first));\n  }\n\n  /**\n   * Custom logic for determining which items should be rendered given the current frame and scroll\n   * metrics, as well as the previous render state. The algorithm may evolve over time, but generally\n   * prioritizes the visible area first, then expands that with overscan regions ahead and behind,\n   * biased in the direction of scroll.\n   */\n  function computeWindowedRenderLimits(props, maxToRenderPerBatch, windowSize, prev, getFrameMetricsApprox, scrollMetrics) {\n    var itemCount = props.getItemCount(props.data);\n    if (itemCount === 0) {\n      return {\n        first: 0,\n        last: -1\n      };\n    }\n    var offset = scrollMetrics.offset,\n      velocity = scrollMetrics.velocity,\n      visibleLength = scrollMetrics.visibleLength,\n      _scrollMetrics$zoomSc = scrollMetrics.zoomScale,\n      zoomScale = _scrollMetrics$zoomSc === void 0 ? 1 : _scrollMetrics$zoomSc;\n\n    // Start with visible area, then compute maximum overscan region by expanding from there, biased\n    // in the direction of scroll. Total overscan area is capped, which should cap memory consumption\n    // too.\n    var visibleBegin = Math.max(0, offset);\n    var visibleEnd = visibleBegin + visibleLength;\n    var overscanLength = (windowSize - 1) * visibleLength;\n\n    // Considering velocity seems to introduce more churn than it's worth.\n    var leadFactor = 0.5; // Math.max(0, Math.min(1, velocity / 25 + 0.5));\n\n    var fillPreference = velocity > 1 ? 'after' : velocity < -1 ? 'before' : 'none';\n    var overscanBegin = Math.max(0, visibleBegin - (1 - leadFactor) * overscanLength);\n    var overscanEnd = Math.max(0, visibleEnd + leadFactor * overscanLength);\n    var lastItemOffset = getFrameMetricsApprox(itemCount - 1, props).offset * zoomScale;\n    if (lastItemOffset < overscanBegin) {\n      // Entire list is before our overscan window\n      return {\n        first: Math.max(0, itemCount - 1 - maxToRenderPerBatch),\n        last: itemCount - 1\n      };\n    }\n\n    // Find the indices that correspond to the items at the render boundaries we're targeting.\n    var _elementsThatOverlapO = elementsThatOverlapOffsets([overscanBegin, visibleBegin, visibleEnd, overscanEnd], props, getFrameMetricsApprox, zoomScale),\n      overscanFirst = _elementsThatOverlapO[0],\n      first = _elementsThatOverlapO[1],\n      last = _elementsThatOverlapO[2],\n      overscanLast = _elementsThatOverlapO[3];\n    overscanFirst = overscanFirst == null ? 0 : overscanFirst;\n    first = first == null ? Math.max(0, overscanFirst) : first;\n    overscanLast = overscanLast == null ? itemCount - 1 : overscanLast;\n    last = last == null ? Math.min(overscanLast, first + maxToRenderPerBatch - 1) : last;\n    var visible = {\n      first,\n      last\n    };\n\n    // We want to limit the number of new cells we're rendering per batch so that we can fill the\n    // content on the screen quickly. If we rendered the entire overscan window at once, the user\n    // could be staring at white space for a long time waiting for a bunch of offscreen content to\n    // render.\n    var newCellCount = newRangeCount(prev, visible);\n    while (true) {\n      if (first <= overscanFirst && last >= overscanLast) {\n        // If we fill the entire overscan range, we're done.\n        break;\n      }\n      var maxNewCells = newCellCount >= maxToRenderPerBatch;\n      var firstWillAddMore = first <= prev.first || first > prev.last;\n      var firstShouldIncrement = first > overscanFirst && (!maxNewCells || !firstWillAddMore);\n      var lastWillAddMore = last >= prev.last || last < prev.first;\n      var lastShouldIncrement = last < overscanLast && (!maxNewCells || !lastWillAddMore);\n      if (maxNewCells && !firstShouldIncrement && !lastShouldIncrement) {\n        // We only want to stop if we've hit maxNewCells AND we cannot increment first or last\n        // without rendering new items. This let's us preserve as many already rendered items as\n        // possible, reducing render churn and keeping the rendered overscan range as large as\n        // possible.\n        break;\n      }\n      if (firstShouldIncrement && !(fillPreference === 'after' && lastShouldIncrement && lastWillAddMore)) {\n        if (firstWillAddMore) {\n          newCellCount++;\n        }\n        first--;\n      }\n      if (lastShouldIncrement && !(fillPreference === 'before' && firstShouldIncrement && firstWillAddMore)) {\n        if (lastWillAddMore) {\n          newCellCount++;\n        }\n        last++;\n      }\n    }\n    if (!(last >= first && first >= 0 && last < itemCount && first >= overscanFirst && last <= overscanLast && first <= visible.first && last >= visible.last)) {\n      throw new Error('Bad window calculation ' + JSON.stringify({\n        first,\n        last,\n        itemCount,\n        overscanFirst,\n        overscanLast,\n        visible\n      }));\n    }\n    return {\n      first,\n      last\n    };\n  }\n  function keyExtractor(item, index) {\n    if (typeof item === 'object' && (item == null ? void 0 : item.key) != null) {\n      return item.key;\n    }\n    if (typeof item === 'object' && (item == null ? void 0 : item.id) != null) {\n      return item.id;\n    }\n    return String(index);\n  }\n});", "lineCount": 184, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 0, 17, 0], [19, 2, 13, 0, "Object"], [19, 8, 13, 0], [19, 9, 13, 0, "defineProperty"], [19, 23, 13, 0], [19, 24, 13, 0, "exports"], [19, 31, 13, 0], [20, 4, 13, 0, "value"], [20, 9, 13, 0], [21, 2, 13, 0], [22, 2, 13, 0, "exports"], [22, 9, 13, 0], [22, 10, 13, 0, "computeWindowedRenderLimits"], [22, 37, 13, 0], [22, 40, 13, 0, "computeWindowedRenderLimits"], [22, 67, 13, 0], [23, 2, 13, 0, "exports"], [23, 9, 13, 0], [23, 10, 13, 0, "elementsThatOverlapOffsets"], [23, 36, 13, 0], [23, 39, 13, 0, "elementsThatOverlapOffsets"], [23, 65, 13, 0], [24, 2, 13, 0, "exports"], [24, 9, 13, 0], [24, 10, 13, 0, "keyExtractor"], [24, 22, 13, 0], [24, 25, 13, 0, "keyExtractor"], [24, 37, 13, 0], [25, 2, 13, 0, "exports"], [25, 9, 13, 0], [25, 10, 13, 0, "newRangeCount"], [25, 23, 13, 0], [25, 26, 13, 0, "newRangeCount"], [25, 39, 13, 0], [26, 2, 18, 7], [26, 11, 18, 16, "elementsThatOverlapOffsets"], [26, 37, 18, 42, "elementsThatOverlapOffsets"], [26, 38, 18, 43, "offsets"], [26, 45, 18, 50], [26, 47, 18, 52, "props"], [26, 52, 18, 57], [26, 54, 18, 59, "getFrameMetrics"], [26, 69, 18, 74], [26, 71, 18, 76, "zoomScale"], [26, 80, 18, 85], [26, 82, 18, 87], [27, 4, 19, 2], [27, 8, 19, 6, "zoomScale"], [27, 17, 19, 15], [27, 22, 19, 20], [27, 27, 19, 25], [27, 28, 19, 26], [27, 30, 19, 28], [28, 6, 20, 4, "zoomScale"], [28, 15, 20, 13], [28, 18, 20, 16], [28, 19, 20, 17], [29, 4, 21, 2], [30, 4, 22, 2], [30, 8, 22, 6, "itemCount"], [30, 17, 22, 15], [30, 20, 22, 18, "props"], [30, 25, 22, 23], [30, 26, 22, 24, "getItemCount"], [30, 38, 22, 36], [30, 39, 22, 37, "props"], [30, 44, 22, 42], [30, 45, 22, 43, "data"], [30, 49, 22, 47], [30, 50, 22, 48], [31, 4, 23, 2], [31, 8, 23, 6, "result"], [31, 14, 23, 12], [31, 17, 23, 15], [31, 19, 23, 17], [32, 4, 24, 2], [32, 9, 24, 7], [32, 13, 24, 11, "offsetIndex"], [32, 24, 24, 22], [32, 27, 24, 25], [32, 28, 24, 26], [32, 30, 24, 28, "offsetIndex"], [32, 41, 24, 39], [32, 44, 24, 42, "offsets"], [32, 51, 24, 49], [32, 52, 24, 50, "length"], [32, 58, 24, 56], [32, 60, 24, 58, "offsetIndex"], [32, 71, 24, 69], [32, 73, 24, 71], [32, 75, 24, 73], [33, 6, 25, 4], [33, 10, 25, 8, "currentOffset"], [33, 23, 25, 21], [33, 26, 25, 24, "offsets"], [33, 33, 25, 31], [33, 34, 25, 32, "offsetIndex"], [33, 45, 25, 43], [33, 46, 25, 44], [34, 6, 26, 4], [34, 10, 26, 8, "left"], [34, 14, 26, 12], [34, 17, 26, 15], [34, 18, 26, 16], [35, 6, 27, 4], [35, 10, 27, 8, "right"], [35, 15, 27, 13], [35, 18, 27, 16, "itemCount"], [35, 27, 27, 25], [35, 30, 27, 28], [35, 31, 27, 29], [36, 6, 28, 4], [36, 13, 28, 11, "left"], [36, 17, 28, 15], [36, 21, 28, 19, "right"], [36, 26, 28, 24], [36, 28, 28, 26], [37, 8, 29, 6], [38, 8, 30, 6], [38, 12, 30, 10, "mid"], [38, 15, 30, 13], [38, 18, 30, 16, "left"], [38, 22, 30, 20], [38, 26, 30, 24, "right"], [38, 31, 30, 29], [38, 34, 30, 32, "left"], [38, 38, 30, 36], [38, 43, 30, 41], [38, 44, 30, 42], [38, 45, 30, 43], [39, 8, 31, 6], [39, 12, 31, 10, "frame"], [39, 17, 31, 15], [39, 20, 31, 18, "getFrameMetrics"], [39, 35, 31, 33], [39, 36, 31, 34, "mid"], [39, 39, 31, 37], [39, 41, 31, 39, "props"], [39, 46, 31, 44], [39, 47, 31, 45], [40, 8, 32, 6], [40, 12, 32, 10, "scaledOffsetStart"], [40, 29, 32, 27], [40, 32, 32, 30, "frame"], [40, 37, 32, 35], [40, 38, 32, 36, "offset"], [40, 44, 32, 42], [40, 47, 32, 45, "zoomScale"], [40, 56, 32, 54], [41, 8, 33, 6], [41, 12, 33, 10, "scaledOffsetEnd"], [41, 27, 33, 25], [41, 30, 33, 28], [41, 31, 33, 29, "frame"], [41, 36, 33, 34], [41, 37, 33, 35, "offset"], [41, 43, 33, 41], [41, 46, 33, 44, "frame"], [41, 51, 33, 49], [41, 52, 33, 50, "length"], [41, 58, 33, 56], [41, 62, 33, 60, "zoomScale"], [41, 71, 33, 69], [43, 8, 35, 6], [44, 8, 36, 6], [45, 8, 37, 6], [45, 12, 37, 10, "mid"], [45, 15, 37, 13], [45, 20, 37, 18], [45, 21, 37, 19], [45, 25, 37, 23, "currentOffset"], [45, 38, 37, 36], [45, 41, 37, 39, "scaledOffsetStart"], [45, 58, 37, 56], [45, 62, 37, 60, "mid"], [45, 65, 37, 63], [45, 70, 37, 68], [45, 71, 37, 69], [45, 75, 37, 73, "currentOffset"], [45, 88, 37, 86], [45, 92, 37, 90, "scaledOffsetStart"], [45, 109, 37, 107], [45, 111, 37, 109], [46, 10, 38, 8, "right"], [46, 15, 38, 13], [46, 18, 38, 16, "mid"], [46, 21, 38, 19], [46, 24, 38, 22], [46, 25, 38, 23], [47, 8, 39, 6], [47, 9, 39, 7], [47, 15, 39, 13], [47, 19, 39, 17, "currentOffset"], [47, 32, 39, 30], [47, 35, 39, 33, "scaledOffsetEnd"], [47, 50, 39, 48], [47, 52, 39, 50], [48, 10, 40, 8, "left"], [48, 14, 40, 12], [48, 17, 40, 15, "mid"], [48, 20, 40, 18], [48, 23, 40, 21], [48, 24, 40, 22], [49, 8, 41, 6], [49, 9, 41, 7], [49, 15, 41, 13], [50, 10, 42, 8, "result"], [50, 16, 42, 14], [50, 17, 42, 15, "offsetIndex"], [50, 28, 42, 26], [50, 29, 42, 27], [50, 32, 42, 30, "mid"], [50, 35, 42, 33], [51, 10, 43, 8], [52, 8, 44, 6], [53, 6, 45, 4], [54, 4, 46, 2], [55, 4, 47, 2], [55, 11, 47, 9, "result"], [55, 17, 47, 15], [56, 2, 48, 0], [58, 2, 50, 0], [59, 0, 51, 0], [60, 0, 52, 0], [61, 0, 53, 0], [62, 0, 54, 0], [63, 0, 55, 0], [64, 2, 56, 7], [64, 11, 56, 16, "newRangeCount"], [64, 24, 56, 29, "newRangeCount"], [64, 25, 56, 30, "prev"], [64, 29, 56, 34], [64, 31, 56, 36, "next"], [64, 35, 56, 40], [64, 37, 56, 42], [65, 4, 57, 2], [65, 11, 57, 9, "next"], [65, 15, 57, 13], [65, 16, 57, 14, "last"], [65, 20, 57, 18], [65, 23, 57, 21, "next"], [65, 27, 57, 25], [65, 28, 57, 26, "first"], [65, 33, 57, 31], [65, 36, 57, 34], [65, 37, 57, 35], [65, 40, 57, 38, "Math"], [65, 44, 57, 42], [65, 45, 57, 43, "max"], [65, 48, 57, 46], [65, 49, 57, 47], [65, 50, 57, 48], [65, 52, 57, 50], [65, 53, 57, 51], [65, 56, 57, 54, "Math"], [65, 60, 57, 58], [65, 61, 57, 59, "min"], [65, 64, 57, 62], [65, 65, 57, 63, "next"], [65, 69, 57, 67], [65, 70, 57, 68, "last"], [65, 74, 57, 72], [65, 76, 57, 74, "prev"], [65, 80, 57, 78], [65, 81, 57, 79, "last"], [65, 85, 57, 83], [65, 86, 57, 84], [65, 89, 57, 87, "Math"], [65, 93, 57, 91], [65, 94, 57, 92, "max"], [65, 97, 57, 95], [65, 98, 57, 96, "next"], [65, 102, 57, 100], [65, 103, 57, 101, "first"], [65, 108, 57, 106], [65, 110, 57, 108, "prev"], [65, 114, 57, 112], [65, 115, 57, 113, "first"], [65, 120, 57, 118], [65, 121, 57, 119], [65, 122, 57, 120], [66, 2, 58, 0], [68, 2, 60, 0], [69, 0, 61, 0], [70, 0, 62, 0], [71, 0, 63, 0], [72, 0, 64, 0], [73, 0, 65, 0], [74, 2, 66, 7], [74, 11, 66, 16, "computeWindowedRenderLimits"], [74, 38, 66, 43, "computeWindowedRenderLimits"], [74, 39, 66, 44, "props"], [74, 44, 66, 49], [74, 46, 66, 51, "maxToRenderPerBatch"], [74, 65, 66, 70], [74, 67, 66, 72, "windowSize"], [74, 77, 66, 82], [74, 79, 66, 84, "prev"], [74, 83, 66, 88], [74, 85, 66, 90, "getFrameMetricsApprox"], [74, 106, 66, 111], [74, 108, 66, 113, "scrollMetrics"], [74, 121, 66, 126], [74, 123, 66, 128], [75, 4, 67, 2], [75, 8, 67, 6, "itemCount"], [75, 17, 67, 15], [75, 20, 67, 18, "props"], [75, 25, 67, 23], [75, 26, 67, 24, "getItemCount"], [75, 38, 67, 36], [75, 39, 67, 37, "props"], [75, 44, 67, 42], [75, 45, 67, 43, "data"], [75, 49, 67, 47], [75, 50, 67, 48], [76, 4, 68, 2], [76, 8, 68, 6, "itemCount"], [76, 17, 68, 15], [76, 22, 68, 20], [76, 23, 68, 21], [76, 25, 68, 23], [77, 6, 69, 4], [77, 13, 69, 11], [78, 8, 70, 6, "first"], [78, 13, 70, 11], [78, 15, 70, 13], [78, 16, 70, 14], [79, 8, 71, 6, "last"], [79, 12, 71, 10], [79, 14, 71, 12], [79, 15, 71, 13], [80, 6, 72, 4], [80, 7, 72, 5], [81, 4, 73, 2], [82, 4, 74, 2], [82, 8, 74, 6, "offset"], [82, 14, 74, 12], [82, 17, 74, 15, "scrollMetrics"], [82, 30, 74, 28], [82, 31, 74, 29, "offset"], [82, 37, 74, 35], [83, 6, 75, 4, "velocity"], [83, 14, 75, 12], [83, 17, 75, 15, "scrollMetrics"], [83, 30, 75, 28], [83, 31, 75, 29, "velocity"], [83, 39, 75, 37], [84, 6, 76, 4, "<PERSON><PERSON><PERSON><PERSON>"], [84, 19, 76, 17], [84, 22, 76, 20, "scrollMetrics"], [84, 35, 76, 33], [84, 36, 76, 34, "<PERSON><PERSON><PERSON><PERSON>"], [84, 49, 76, 47], [85, 6, 77, 4, "_scrollMetrics$zoomSc"], [85, 27, 77, 25], [85, 30, 77, 28, "scrollMetrics"], [85, 43, 77, 41], [85, 44, 77, 42, "zoomScale"], [85, 53, 77, 51], [86, 6, 78, 4, "zoomScale"], [86, 15, 78, 13], [86, 18, 78, 16, "_scrollMetrics$zoomSc"], [86, 39, 78, 37], [86, 44, 78, 42], [86, 49, 78, 47], [86, 50, 78, 48], [86, 53, 78, 51], [86, 54, 78, 52], [86, 57, 78, 55, "_scrollMetrics$zoomSc"], [86, 78, 78, 76], [88, 4, 80, 2], [89, 4, 81, 2], [90, 4, 82, 2], [91, 4, 83, 2], [91, 8, 83, 6, "visibleBegin"], [91, 20, 83, 18], [91, 23, 83, 21, "Math"], [91, 27, 83, 25], [91, 28, 83, 26, "max"], [91, 31, 83, 29], [91, 32, 83, 30], [91, 33, 83, 31], [91, 35, 83, 33, "offset"], [91, 41, 83, 39], [91, 42, 83, 40], [92, 4, 84, 2], [92, 8, 84, 6, "visibleEnd"], [92, 18, 84, 16], [92, 21, 84, 19, "visibleBegin"], [92, 33, 84, 31], [92, 36, 84, 34, "<PERSON><PERSON><PERSON><PERSON>"], [92, 49, 84, 47], [93, 4, 85, 2], [93, 8, 85, 6, "overscanLength"], [93, 22, 85, 20], [93, 25, 85, 23], [93, 26, 85, 24, "windowSize"], [93, 36, 85, 34], [93, 39, 85, 37], [93, 40, 85, 38], [93, 44, 85, 42, "<PERSON><PERSON><PERSON><PERSON>"], [93, 57, 85, 55], [95, 4, 87, 2], [96, 4, 88, 2], [96, 8, 88, 6, "leadFactor"], [96, 18, 88, 16], [96, 21, 88, 19], [96, 24, 88, 22], [96, 25, 88, 23], [96, 26, 88, 24], [98, 4, 90, 2], [98, 8, 90, 6, "fillPreference"], [98, 22, 90, 20], [98, 25, 90, 23, "velocity"], [98, 33, 90, 31], [98, 36, 90, 34], [98, 37, 90, 35], [98, 40, 90, 38], [98, 47, 90, 45], [98, 50, 90, 48, "velocity"], [98, 58, 90, 56], [98, 61, 90, 59], [98, 62, 90, 60], [98, 63, 90, 61], [98, 66, 90, 64], [98, 74, 90, 72], [98, 77, 90, 75], [98, 83, 90, 81], [99, 4, 91, 2], [99, 8, 91, 6, "overscanBegin"], [99, 21, 91, 19], [99, 24, 91, 22, "Math"], [99, 28, 91, 26], [99, 29, 91, 27, "max"], [99, 32, 91, 30], [99, 33, 91, 31], [99, 34, 91, 32], [99, 36, 91, 34, "visibleBegin"], [99, 48, 91, 46], [99, 51, 91, 49], [99, 52, 91, 50], [99, 53, 91, 51], [99, 56, 91, 54, "leadFactor"], [99, 66, 91, 64], [99, 70, 91, 68, "overscanLength"], [99, 84, 91, 82], [99, 85, 91, 83], [100, 4, 92, 2], [100, 8, 92, 6, "overscanEnd"], [100, 19, 92, 17], [100, 22, 92, 20, "Math"], [100, 26, 92, 24], [100, 27, 92, 25, "max"], [100, 30, 92, 28], [100, 31, 92, 29], [100, 32, 92, 30], [100, 34, 92, 32, "visibleEnd"], [100, 44, 92, 42], [100, 47, 92, 45, "leadFactor"], [100, 57, 92, 55], [100, 60, 92, 58, "overscanLength"], [100, 74, 92, 72], [100, 75, 92, 73], [101, 4, 93, 2], [101, 8, 93, 6, "lastItemOffset"], [101, 22, 93, 20], [101, 25, 93, 23, "getFrameMetricsApprox"], [101, 46, 93, 44], [101, 47, 93, 45, "itemCount"], [101, 56, 93, 54], [101, 59, 93, 57], [101, 60, 93, 58], [101, 62, 93, 60, "props"], [101, 67, 93, 65], [101, 68, 93, 66], [101, 69, 93, 67, "offset"], [101, 75, 93, 73], [101, 78, 93, 76, "zoomScale"], [101, 87, 93, 85], [102, 4, 94, 2], [102, 8, 94, 6, "lastItemOffset"], [102, 22, 94, 20], [102, 25, 94, 23, "overscanBegin"], [102, 38, 94, 36], [102, 40, 94, 38], [103, 6, 95, 4], [104, 6, 96, 4], [104, 13, 96, 11], [105, 8, 97, 6, "first"], [105, 13, 97, 11], [105, 15, 97, 13, "Math"], [105, 19, 97, 17], [105, 20, 97, 18, "max"], [105, 23, 97, 21], [105, 24, 97, 22], [105, 25, 97, 23], [105, 27, 97, 25, "itemCount"], [105, 36, 97, 34], [105, 39, 97, 37], [105, 40, 97, 38], [105, 43, 97, 41, "maxToRenderPerBatch"], [105, 62, 97, 60], [105, 63, 97, 61], [106, 8, 98, 6, "last"], [106, 12, 98, 10], [106, 14, 98, 12, "itemCount"], [106, 23, 98, 21], [106, 26, 98, 24], [107, 6, 99, 4], [107, 7, 99, 5], [108, 4, 100, 2], [110, 4, 102, 2], [111, 4, 103, 2], [111, 8, 103, 6, "_elementsThatOverlapO"], [111, 29, 103, 27], [111, 32, 103, 30, "elementsThatOverlapOffsets"], [111, 58, 103, 56], [111, 59, 103, 57], [111, 60, 103, 58, "overscanBegin"], [111, 73, 103, 71], [111, 75, 103, 73, "visibleBegin"], [111, 87, 103, 85], [111, 89, 103, 87, "visibleEnd"], [111, 99, 103, 97], [111, 101, 103, 99, "overscanEnd"], [111, 112, 103, 110], [111, 113, 103, 111], [111, 115, 103, 113, "props"], [111, 120, 103, 118], [111, 122, 103, 120, "getFrameMetricsApprox"], [111, 143, 103, 141], [111, 145, 103, 143, "zoomScale"], [111, 154, 103, 152], [111, 155, 103, 153], [112, 6, 104, 4, "overscanFirst"], [112, 19, 104, 17], [112, 22, 104, 20, "_elementsThatOverlapO"], [112, 43, 104, 41], [112, 44, 104, 42], [112, 45, 104, 43], [112, 46, 104, 44], [113, 6, 105, 4, "first"], [113, 11, 105, 9], [113, 14, 105, 12, "_elementsThatOverlapO"], [113, 35, 105, 33], [113, 36, 105, 34], [113, 37, 105, 35], [113, 38, 105, 36], [114, 6, 106, 4, "last"], [114, 10, 106, 8], [114, 13, 106, 11, "_elementsThatOverlapO"], [114, 34, 106, 32], [114, 35, 106, 33], [114, 36, 106, 34], [114, 37, 106, 35], [115, 6, 107, 4, "overscanLast"], [115, 18, 107, 16], [115, 21, 107, 19, "_elementsThatOverlapO"], [115, 42, 107, 40], [115, 43, 107, 41], [115, 44, 107, 42], [115, 45, 107, 43], [116, 4, 108, 2, "overscanFirst"], [116, 17, 108, 15], [116, 20, 108, 18, "overscanFirst"], [116, 33, 108, 31], [116, 37, 108, 35], [116, 41, 108, 39], [116, 44, 108, 42], [116, 45, 108, 43], [116, 48, 108, 46, "overscanFirst"], [116, 61, 108, 59], [117, 4, 109, 2, "first"], [117, 9, 109, 7], [117, 12, 109, 10, "first"], [117, 17, 109, 15], [117, 21, 109, 19], [117, 25, 109, 23], [117, 28, 109, 26, "Math"], [117, 32, 109, 30], [117, 33, 109, 31, "max"], [117, 36, 109, 34], [117, 37, 109, 35], [117, 38, 109, 36], [117, 40, 109, 38, "overscanFirst"], [117, 53, 109, 51], [117, 54, 109, 52], [117, 57, 109, 55, "first"], [117, 62, 109, 60], [118, 4, 110, 2, "overscanLast"], [118, 16, 110, 14], [118, 19, 110, 17, "overscanLast"], [118, 31, 110, 29], [118, 35, 110, 33], [118, 39, 110, 37], [118, 42, 110, 40, "itemCount"], [118, 51, 110, 49], [118, 54, 110, 52], [118, 55, 110, 53], [118, 58, 110, 56, "overscanLast"], [118, 70, 110, 68], [119, 4, 111, 2, "last"], [119, 8, 111, 6], [119, 11, 111, 9, "last"], [119, 15, 111, 13], [119, 19, 111, 17], [119, 23, 111, 21], [119, 26, 111, 24, "Math"], [119, 30, 111, 28], [119, 31, 111, 29, "min"], [119, 34, 111, 32], [119, 35, 111, 33, "overscanLast"], [119, 47, 111, 45], [119, 49, 111, 47, "first"], [119, 54, 111, 52], [119, 57, 111, 55, "maxToRenderPerBatch"], [119, 76, 111, 74], [119, 79, 111, 77], [119, 80, 111, 78], [119, 81, 111, 79], [119, 84, 111, 82, "last"], [119, 88, 111, 86], [120, 4, 112, 2], [120, 8, 112, 6, "visible"], [120, 15, 112, 13], [120, 18, 112, 16], [121, 6, 113, 4, "first"], [121, 11, 113, 9], [122, 6, 114, 4, "last"], [123, 4, 115, 2], [123, 5, 115, 3], [125, 4, 117, 2], [126, 4, 118, 2], [127, 4, 119, 2], [128, 4, 120, 2], [129, 4, 121, 2], [129, 8, 121, 6, "newCellCount"], [129, 20, 121, 18], [129, 23, 121, 21, "newRangeCount"], [129, 36, 121, 34], [129, 37, 121, 35, "prev"], [129, 41, 121, 39], [129, 43, 121, 41, "visible"], [129, 50, 121, 48], [129, 51, 121, 49], [130, 4, 122, 2], [130, 11, 122, 9], [130, 15, 122, 13], [130, 17, 122, 15], [131, 6, 123, 4], [131, 10, 123, 8, "first"], [131, 15, 123, 13], [131, 19, 123, 17, "overscanFirst"], [131, 32, 123, 30], [131, 36, 123, 34, "last"], [131, 40, 123, 38], [131, 44, 123, 42, "overscanLast"], [131, 56, 123, 54], [131, 58, 123, 56], [132, 8, 124, 6], [133, 8, 125, 6], [134, 6, 126, 4], [135, 6, 127, 4], [135, 10, 127, 8, "max<PERSON><PERSON><PERSON><PERSON><PERSON>"], [135, 21, 127, 19], [135, 24, 127, 22, "newCellCount"], [135, 36, 127, 34], [135, 40, 127, 38, "maxToRenderPerBatch"], [135, 59, 127, 57], [136, 6, 128, 4], [136, 10, 128, 8, "firstWillAddMore"], [136, 26, 128, 24], [136, 29, 128, 27, "first"], [136, 34, 128, 32], [136, 38, 128, 36, "prev"], [136, 42, 128, 40], [136, 43, 128, 41, "first"], [136, 48, 128, 46], [136, 52, 128, 50, "first"], [136, 57, 128, 55], [136, 60, 128, 58, "prev"], [136, 64, 128, 62], [136, 65, 128, 63, "last"], [136, 69, 128, 67], [137, 6, 129, 4], [137, 10, 129, 8, "firstShouldIncrement"], [137, 30, 129, 28], [137, 33, 129, 31, "first"], [137, 38, 129, 36], [137, 41, 129, 39, "overscanFirst"], [137, 54, 129, 52], [137, 59, 129, 57], [137, 60, 129, 58, "max<PERSON><PERSON><PERSON><PERSON><PERSON>"], [137, 71, 129, 69], [137, 75, 129, 73], [137, 76, 129, 74, "firstWillAddMore"], [137, 92, 129, 90], [137, 93, 129, 91], [138, 6, 130, 4], [138, 10, 130, 8, "lastWillAddMore"], [138, 25, 130, 23], [138, 28, 130, 26, "last"], [138, 32, 130, 30], [138, 36, 130, 34, "prev"], [138, 40, 130, 38], [138, 41, 130, 39, "last"], [138, 45, 130, 43], [138, 49, 130, 47, "last"], [138, 53, 130, 51], [138, 56, 130, 54, "prev"], [138, 60, 130, 58], [138, 61, 130, 59, "first"], [138, 66, 130, 64], [139, 6, 131, 4], [139, 10, 131, 8, "lastShouldIncrement"], [139, 29, 131, 27], [139, 32, 131, 30, "last"], [139, 36, 131, 34], [139, 39, 131, 37, "overscanLast"], [139, 51, 131, 49], [139, 56, 131, 54], [139, 57, 131, 55, "max<PERSON><PERSON><PERSON><PERSON><PERSON>"], [139, 68, 131, 66], [139, 72, 131, 70], [139, 73, 131, 71, "lastWillAddMore"], [139, 88, 131, 86], [139, 89, 131, 87], [140, 6, 132, 4], [140, 10, 132, 8, "max<PERSON><PERSON><PERSON><PERSON><PERSON>"], [140, 21, 132, 19], [140, 25, 132, 23], [140, 26, 132, 24, "firstShouldIncrement"], [140, 46, 132, 44], [140, 50, 132, 48], [140, 51, 132, 49, "lastShouldIncrement"], [140, 70, 132, 68], [140, 72, 132, 70], [141, 8, 133, 6], [142, 8, 134, 6], [143, 8, 135, 6], [144, 8, 136, 6], [145, 8, 137, 6], [146, 6, 138, 4], [147, 6, 139, 4], [147, 10, 139, 8, "firstShouldIncrement"], [147, 30, 139, 28], [147, 34, 139, 32], [147, 36, 139, 34, "fillPreference"], [147, 50, 139, 48], [147, 55, 139, 53], [147, 62, 139, 60], [147, 66, 139, 64, "lastShouldIncrement"], [147, 85, 139, 83], [147, 89, 139, 87, "lastWillAddMore"], [147, 104, 139, 102], [147, 105, 139, 103], [147, 107, 139, 105], [148, 8, 140, 6], [148, 12, 140, 10, "firstWillAddMore"], [148, 28, 140, 26], [148, 30, 140, 28], [149, 10, 141, 8, "newCellCount"], [149, 22, 141, 20], [149, 24, 141, 22], [150, 8, 142, 6], [151, 8, 143, 6, "first"], [151, 13, 143, 11], [151, 15, 143, 13], [152, 6, 144, 4], [153, 6, 145, 4], [153, 10, 145, 8, "lastShouldIncrement"], [153, 29, 145, 27], [153, 33, 145, 31], [153, 35, 145, 33, "fillPreference"], [153, 49, 145, 47], [153, 54, 145, 52], [153, 62, 145, 60], [153, 66, 145, 64, "firstShouldIncrement"], [153, 86, 145, 84], [153, 90, 145, 88, "firstWillAddMore"], [153, 106, 145, 104], [153, 107, 145, 105], [153, 109, 145, 107], [154, 8, 146, 6], [154, 12, 146, 10, "lastWillAddMore"], [154, 27, 146, 25], [154, 29, 146, 27], [155, 10, 147, 8, "newCellCount"], [155, 22, 147, 20], [155, 24, 147, 22], [156, 8, 148, 6], [157, 8, 149, 6, "last"], [157, 12, 149, 10], [157, 14, 149, 12], [158, 6, 150, 4], [159, 4, 151, 2], [160, 4, 152, 2], [160, 8, 152, 6], [160, 10, 152, 8, "last"], [160, 14, 152, 12], [160, 18, 152, 16, "first"], [160, 23, 152, 21], [160, 27, 152, 25, "first"], [160, 32, 152, 30], [160, 36, 152, 34], [160, 37, 152, 35], [160, 41, 152, 39, "last"], [160, 45, 152, 43], [160, 48, 152, 46, "itemCount"], [160, 57, 152, 55], [160, 61, 152, 59, "first"], [160, 66, 152, 64], [160, 70, 152, 68, "overscanFirst"], [160, 83, 152, 81], [160, 87, 152, 85, "last"], [160, 91, 152, 89], [160, 95, 152, 93, "overscanLast"], [160, 107, 152, 105], [160, 111, 152, 109, "first"], [160, 116, 152, 114], [160, 120, 152, 118, "visible"], [160, 127, 152, 125], [160, 128, 152, 126, "first"], [160, 133, 152, 131], [160, 137, 152, 135, "last"], [160, 141, 152, 139], [160, 145, 152, 143, "visible"], [160, 152, 152, 150], [160, 153, 152, 151, "last"], [160, 157, 152, 155], [160, 158, 152, 156], [160, 160, 152, 158], [161, 6, 153, 4], [161, 12, 153, 10], [161, 16, 153, 14, "Error"], [161, 21, 153, 19], [161, 22, 153, 20], [161, 47, 153, 45], [161, 50, 153, 48, "JSON"], [161, 54, 153, 52], [161, 55, 153, 53, "stringify"], [161, 64, 153, 62], [161, 65, 153, 63], [162, 8, 154, 6, "first"], [162, 13, 154, 11], [163, 8, 155, 6, "last"], [163, 12, 155, 10], [164, 8, 156, 6, "itemCount"], [164, 17, 156, 15], [165, 8, 157, 6, "overscanFirst"], [165, 21, 157, 19], [166, 8, 158, 6, "overscanLast"], [166, 20, 158, 18], [167, 8, 159, 6, "visible"], [168, 6, 160, 4], [168, 7, 160, 5], [168, 8, 160, 6], [168, 9, 160, 7], [169, 4, 161, 2], [170, 4, 162, 2], [170, 11, 162, 9], [171, 6, 163, 4, "first"], [171, 11, 163, 9], [172, 6, 164, 4, "last"], [173, 4, 165, 2], [173, 5, 165, 3], [174, 2, 166, 0], [175, 2, 167, 7], [175, 11, 167, 16, "keyExtractor"], [175, 23, 167, 28, "keyExtractor"], [175, 24, 167, 29, "item"], [175, 28, 167, 33], [175, 30, 167, 35, "index"], [175, 35, 167, 40], [175, 37, 167, 42], [176, 4, 168, 2], [176, 8, 168, 6], [176, 15, 168, 13, "item"], [176, 19, 168, 17], [176, 24, 168, 22], [176, 32, 168, 30], [176, 36, 168, 34], [176, 37, 168, 35, "item"], [176, 41, 168, 39], [176, 45, 168, 43], [176, 49, 168, 47], [176, 52, 168, 50], [176, 57, 168, 55], [176, 58, 168, 56], [176, 61, 168, 59, "item"], [176, 65, 168, 63], [176, 66, 168, 64, "key"], [176, 69, 168, 67], [176, 74, 168, 72], [176, 78, 168, 76], [176, 80, 168, 78], [177, 6, 169, 4], [177, 13, 169, 11, "item"], [177, 17, 169, 15], [177, 18, 169, 16, "key"], [177, 21, 169, 19], [178, 4, 170, 2], [179, 4, 171, 2], [179, 8, 171, 6], [179, 15, 171, 13, "item"], [179, 19, 171, 17], [179, 24, 171, 22], [179, 32, 171, 30], [179, 36, 171, 34], [179, 37, 171, 35, "item"], [179, 41, 171, 39], [179, 45, 171, 43], [179, 49, 171, 47], [179, 52, 171, 50], [179, 57, 171, 55], [179, 58, 171, 56], [179, 61, 171, 59, "item"], [179, 65, 171, 63], [179, 66, 171, 64, "id"], [179, 68, 171, 66], [179, 73, 171, 71], [179, 77, 171, 75], [179, 79, 171, 77], [180, 6, 172, 4], [180, 13, 172, 11, "item"], [180, 17, 172, 15], [180, 18, 172, 16, "id"], [180, 20, 172, 18], [181, 4, 173, 2], [182, 4, 174, 2], [182, 11, 174, 9, "String"], [182, 17, 174, 15], [182, 18, 174, 16, "index"], [182, 23, 174, 21], [182, 24, 174, 22], [183, 2, 175, 0], [184, 0, 175, 1], [184, 3]], "functionMap": {"names": ["<global>", "elementsThatOverlapOffsets", "newRangeCount", "computeWindowedRenderLimits", "keyExtractor"], "mappings": "AAA;OCiB;CD8B;OEQ;CFE;OGQ;CHoG;OIC"}}, "type": "js/module"}]}