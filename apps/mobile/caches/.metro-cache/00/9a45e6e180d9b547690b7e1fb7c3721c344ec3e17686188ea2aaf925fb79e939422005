{"dependencies": [{"name": "../Utilities/FeatureDetection", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 31}, "end": {"line": 13, "column": 71}}], "key": "KNMivunt2HbOWsLAszP6XBfyx8A=", "exportNames": ["*"]}}, {"name": "../Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 66}}], "key": "HYclPKQCLeyfRj4pG+IgrzgyEZ8=", "exportNames": ["*"]}}, {"name": "regenerator-runtime/runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 44, "column": 11}, "end": {"line": 44, "column": 49}}], "key": "YE24CprUhZeKNgUyjy5/LE+aVF4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _require = require(_dependencyMap[0], \"../Utilities/FeatureDetection\"),\n    hasNativeConstructor = _require.hasNativeConstructor;\n  var _require2 = require(_dependencyMap[1], \"../Utilities/PolyfillFunctions\"),\n    polyfillGlobal = _require2.polyfillGlobal;\n  var hasNativeGenerator;\n  try {\n    hasNativeGenerator = hasNativeConstructor(function* () {}, 'GeneratorFunction');\n  } catch {\n    hasNativeGenerator = false;\n  }\n  if (!hasNativeGenerator) {\n    polyfillGlobal('regeneratorRuntime', () => {\n      delete global.regeneratorRuntime;\n      return require(_dependencyMap[2], \"regenerator-runtime/runtime\");\n    });\n  }\n});", "lineCount": 20, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 13, 0], [4, 6, 13, 0, "_require"], [4, 14, 13, 0], [4, 17, 13, 31, "require"], [4, 24, 13, 38], [4, 25, 13, 38, "_dependencyMap"], [4, 39, 13, 38], [4, 75, 13, 70], [4, 76, 13, 71], [5, 4, 13, 7, "hasNativeConstructor"], [5, 24, 13, 27], [5, 27, 13, 27, "_require"], [5, 35, 13, 27], [5, 36, 13, 7, "hasNativeConstructor"], [5, 56, 13, 27], [6, 2, 14, 0], [6, 6, 14, 0, "_require2"], [6, 15, 14, 0], [6, 18, 14, 25, "require"], [6, 25, 14, 32], [6, 26, 14, 32, "_dependencyMap"], [6, 40, 14, 32], [6, 77, 14, 65], [6, 78, 14, 66], [7, 4, 14, 7, "polyfillGlobal"], [7, 18, 14, 21], [7, 21, 14, 21, "_require2"], [7, 30, 14, 21], [7, 31, 14, 7, "polyfillGlobal"], [7, 45, 14, 21], [8, 2, 21, 0], [8, 6, 21, 4, "hasNativeGenerator"], [8, 24, 21, 22], [9, 2, 22, 0], [9, 6, 22, 4], [10, 4, 25, 2, "hasNativeGenerator"], [10, 22, 25, 20], [10, 25, 25, 23, "hasNativeConstructor"], [10, 45, 25, 43], [10, 46, 26, 4], [10, 59, 26, 17], [10, 60, 26, 18], [10, 61, 26, 19], [10, 63, 27, 4], [10, 82, 28, 2], [10, 83, 28, 3], [11, 2, 29, 0], [11, 3, 29, 1], [11, 4, 29, 2], [11, 10, 29, 8], [12, 4, 31, 2, "hasNativeGenerator"], [12, 22, 31, 20], [12, 25, 31, 23], [12, 30, 31, 28], [13, 2, 32, 0], [14, 2, 36, 0], [14, 6, 36, 4], [14, 7, 36, 5, "hasNativeGenerator"], [14, 25, 36, 23], [14, 27, 36, 25], [15, 4, 37, 2, "polyfillGlobal"], [15, 18, 37, 16], [15, 19, 37, 17], [15, 39, 37, 37], [15, 41, 37, 39], [15, 47, 37, 45], [16, 6, 40, 4], [16, 13, 40, 11, "global"], [16, 19, 40, 17], [16, 20, 40, 18, "regeneratorRuntime"], [16, 38, 40, 36], [17, 6, 44, 4], [17, 13, 44, 11, "require"], [17, 20, 44, 18], [17, 21, 44, 18, "_dependencyMap"], [17, 35, 44, 18], [17, 69, 44, 48], [17, 70, 44, 49], [18, 4, 45, 2], [18, 5, 45, 3], [18, 6, 45, 4], [19, 2, 46, 0], [20, 0, 46, 1], [20, 3]], "functionMap": {"names": ["<global>", "hasNativeConstructor$argument_0", "polyfillGlobal$argument_1"], "mappings": "AAA;ICyB,eD;uCEW;GFQ"}}, "type": "js/module"}]}