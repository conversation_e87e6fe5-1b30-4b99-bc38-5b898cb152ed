{"dependencies": [{"name": "../../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 50, "index": 65}}], "key": "Jq1DcLPs1AjY3ygtzPUe4D8IdoQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createJSWorkletsModule = createJSWorkletsModule;\n  var _errors = require(_dependencyMap[0], \"../../errors.js\");\n  function createJSWorkletsModule() {\n    return new JSWorklets();\n  }\n  class JSWorklets {\n    makeShareableClone() {\n      throw new _errors.ReanimatedError('makeShareableClone should never be called in JSWorklets.');\n    }\n  }\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createJSWorkletsModule"], [7, 32, 1, 13], [7, 35, 1, 13, "createJSWorkletsModule"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_errors"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "createJSWorkletsModule"], [9, 33, 4, 38, "createJSWorkletsModule"], [9, 34, 4, 38], [9, 36, 4, 41], [10, 4, 5, 2], [10, 11, 5, 9], [10, 15, 5, 13, "JSWorklets"], [10, 25, 5, 23], [10, 26, 5, 24], [10, 27, 5, 25], [11, 2, 6, 0], [12, 2, 7, 0], [12, 8, 7, 6, "JSWorklets"], [12, 18, 7, 16], [12, 19, 7, 17], [13, 4, 8, 2, "makeShareableClone"], [13, 22, 8, 20, "makeShareableClone"], [13, 23, 8, 20], [13, 25, 8, 23], [14, 6, 9, 4], [14, 12, 9, 10], [14, 16, 9, 14, "ReanimatedError"], [14, 39, 9, 29], [14, 40, 9, 30], [14, 98, 9, 88], [14, 99, 9, 89], [15, 4, 10, 2], [16, 2, 11, 0], [17, 0, 11, 1], [17, 3]], "functionMap": {"names": ["<global>", "createJSWorkletsModule", "JSWorklets", "makeShareableClone"], "mappings": "AAA;OCG;CDE;AEC;ECC;GDE;CFC"}}, "type": "js/module"}]}