{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 51, "index": 65}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 168}, "end": {"line": 14, "column": 24, "index": 260}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 261}, "end": {"line": 15, "column": 77, "index": 338}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}, {"name": "../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 339}, "end": {"line": 16, "column": 44, "index": 383}}], "key": "K1yKq+VUoHdgwBY7Fz9TrE1h5uU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedSensor = useAnimatedSensor;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes\");\n  var _core = require(_dependencyMap[2], \"../core\");\n  var _threads = require(_dependencyMap[3], \"../threads\");\n  // euler angles are in order ZXY, z = yaw, x = pitch, y = roll\n  // https://github.com/mrdoob/three.js/blob/dev/src/math/Quaternion.js#L237\n  var _worklet_763515177202_init_data = {\n    code: \"function eulerToQuaternion_reactNativeReanimated_useAnimatedSensorTs1(pitch,roll,yaw){const c1=Math.cos(pitch/2);const s1=Math.sin(pitch/2);const c2=Math.cos(roll/2);const s2=Math.sin(roll/2);const c3=Math.cos(yaw/2);const s3=Math.sin(yaw/2);return[s1*c2*c3-c1*s2*s3,c1*s2*c3+s1*c2*s3,c1*c2*s3+s1*s2*c3,c1*c2*c3-s1*s2*s3];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"eulerToQuaternion_reactNativeReanimated_useAnimatedSensorTs1\\\",\\\"pitch\\\",\\\"roll\\\",\\\"yaw\\\",\\\"c1\\\",\\\"Math\\\",\\\"cos\\\",\\\"s1\\\",\\\"sin\\\",\\\"c2\\\",\\\"s2\\\",\\\"c3\\\",\\\"s3\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\\\"],\\\"mappings\\\":\\\"AAiBA,SAAAA,6DAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA,QAAAC,EAAA,CAAAC,IAAA,CAAAC,GAAA,CAAAL,KAAA,IACA,MAAAM,EAAA,CAAAF,IAAA,CAAAG,GAAA,CAAAP,KAAA,IACA,MAAAQ,EAAS,CAAAJ,IAAA,CAAAC,GAAA,CAAAJ,IAAiB,CAAC,GAEzB,KAAM,CAAAQ,EAAE,CAAGL,IAAI,CAACG,GAAG,CAACN,IAAA,CAAK,CAAG,CAAC,CAC7B,KAAM,CAAAS,EAAE,CAAGN,IAAI,CAACC,GAAG,CAACH,GAAA,CAAK,EAAG,CAC5B,KAAM,CAAAS,EAAE,CAAGP,IAAI,CAACG,GAAG,CAACL,GAAA,CAAI,CAAG,CAAC,CAC5B,MAAM,CAAEI,EAAA,CAAGE,EAAI,CAACE,EAAI,CAAAP,EAAI,CAAIM,EAAC,CAAAE,EAAA,CAAAR,EAAA,CAAAM,EAAA,CAAAC,EAAA,CAAAJ,EAAA,CAAAE,EAAA,CAAAG,EAAA,CAAAR,EAAA,CAAAK,EAAA,CAAAG,EAAA,CAAAL,EAAA,CAAAG,EAAA,CAAAC,EAAA,CAAAP,EAAA,CAAAK,EAAA,CAAAE,EAAA,CAAAJ,EAAA,CAAAG,EAAA,CAAAE,EAAA,E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var eulerToQuaternion = function () {\n    var _e = [new global.Error(), 1, -27];\n    var eulerToQuaternion = function (pitch, roll, yaw) {\n      var c1 = Math.cos(pitch / 2);\n      var s1 = Math.sin(pitch / 2);\n      var c2 = Math.cos(roll / 2);\n      var s2 = Math.sin(roll / 2);\n      var c3 = Math.cos(yaw / 2);\n      var s3 = Math.sin(yaw / 2);\n      return [s1 * c2 * c3 - c1 * s2 * s3, c1 * s2 * c3 + s1 * c2 * s3, c1 * c2 * s3 + s1 * s2 * c3, c1 * c2 * c3 - s1 * s2 * s3];\n    };\n    eulerToQuaternion.__closure = {};\n    eulerToQuaternion.__workletHash = 763515177202;\n    eulerToQuaternion.__initData = _worklet_763515177202_init_data;\n    eulerToQuaternion.__stackDetails = _e;\n    return eulerToQuaternion;\n  }();\n  var _worklet_17236990978851_init_data = {\n    code: \"function adjustRotationToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorTs2(data){const{InterfaceOrientation,eulerToQuaternion}=this.__closure;const{interfaceOrientation:interfaceOrientation,pitch:pitch,roll:roll,yaw:yaw}=data;if(interfaceOrientation===InterfaceOrientation.ROTATION_90){data.pitch=roll;data.roll=-pitch;data.yaw=yaw-Math.PI/2;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_270){data.pitch=-roll;data.roll=pitch;data.yaw=yaw+Math.PI/2;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_180){data.pitch*=-1;data.roll*=-1;data.yaw*=-1;}const q=eulerToQuaternion(data.pitch,data.roll,data.yaw);data.qx=q[0];data.qy=q[1];data.qz=q[2];data.qw=q[3];return data;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"adjustRotationToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorTs2\\\",\\\"data\\\",\\\"InterfaceOrientation\\\",\\\"eulerToQuaternion\\\",\\\"__closure\\\",\\\"interfaceOrientation\\\",\\\"pitch\\\",\\\"roll\\\",\\\"yaw\\\",\\\"ROTATION_90\\\",\\\"Math\\\",\\\"PI\\\",\\\"ROTATION_270\\\",\\\"ROTATION_180\\\",\\\"q\\\",\\\"qx\\\",\\\"qy\\\",\\\"qz\\\",\\\"qw\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\\\"],\\\"mappings\\\":\\\"AAoCA,SAAAA,+EAAmEA,CAAAC,IAAA,QAAAC,oBAAA,CAAAC,iBAAA,OAAAC,SAAA,CAEjE,KAAM,CAAEC,oBAAoB,CAApBA,oBAAoB,CAAEC,KAAK,CAALA,KAAK,CAAEC,IAAI,CAAJA,IAAI,CAAEC,GAAA,CAAAA,GAAI,CAAC,CAAGP,IAAI,CACvD,GAAII,oBAAoB,GAAKH,oBAAoB,CAACO,WAAW,CAAE,CAC7DR,IAAI,CAACK,KAAK,CAAGC,IAAI,CACjBN,IAAI,CAACM,IAAI,CAAG,CAACD,KAAK,CAClBL,IAAI,CAACO,GAAG,CAAGA,GAAG,CAAGE,IAAI,CAACC,EAAE,CAAG,CAAC,CAC9B,CAAC,IAAM,IAAIN,oBAAoB,GAAKH,oBAAoB,CAACU,YAAY,CAAE,CACrEX,IAAI,CAACK,KAAK,CAAG,CAACC,IAAI,CAClBN,IAAI,CAACM,IAAI,CAAGD,KAAK,CACjBL,IAAI,CAACO,GAAG,CAAGA,GAAG,CAAGE,IAAI,CAACC,EAAE,CAAG,CAAC,CAC9B,CAAC,IAAM,IAAIN,oBAAoB,GAAKH,oBAAoB,CAACW,YAAY,CAAE,CACrEZ,IAAI,CAACK,KAAK,EAAI,CAAC,CAAC,CAChBL,IAAI,CAACM,IAAI,EAAI,CAAC,CAAC,CACfN,IAAI,CAACO,GAAG,EAAI,CAAC,CAAC,CAChB,CAEA,KAAM,CAAAM,CAAC,CAAGX,iBAAiB,CAACF,IAAI,CAACK,KAAK,CAAEL,IAAI,CAACM,IAAI,CAAEN,IAAI,CAACO,GAAG,CAAC,CAC5DP,IAAI,CAACc,EAAE,CAAGD,CAAC,CAAC,CAAC,CAAC,CACdb,IAAI,CAACe,EAAE,CAAGF,CAAC,CAAC,CAAC,CAAC,CACdb,IAAI,CAACgB,EAAE,CAAGH,CAAC,CAAC,CAAC,CAAC,CACdb,IAAI,CAACiB,EAAE,CAAGJ,CAAC,CAAC,CAAC,CAAC,CACd,MAAO,CAAAb,IAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var adjustRotationToInterfaceOrientation = function () {\n    var _e = [new global.Error(), -3, -27];\n    var adjustRotationToInterfaceOrientation = function (data) {\n      var interfaceOrientation = data.interfaceOrientation,\n        pitch = data.pitch,\n        roll = data.roll,\n        yaw = data.yaw;\n      if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_90) {\n        data.pitch = roll;\n        data.roll = -pitch;\n        data.yaw = yaw - Math.PI / 2;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_270) {\n        data.pitch = -roll;\n        data.roll = pitch;\n        data.yaw = yaw + Math.PI / 2;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_180) {\n        data.pitch *= -1;\n        data.roll *= -1;\n        data.yaw *= -1;\n      }\n      var q = eulerToQuaternion(data.pitch, data.roll, data.yaw);\n      data.qx = q[0];\n      data.qy = q[1];\n      data.qz = q[2];\n      data.qw = q[3];\n      return data;\n    };\n    adjustRotationToInterfaceOrientation.__closure = {\n      InterfaceOrientation: _commonTypes.InterfaceOrientation,\n      eulerToQuaternion\n    };\n    adjustRotationToInterfaceOrientation.__workletHash = 17236990978851;\n    adjustRotationToInterfaceOrientation.__initData = _worklet_17236990978851_init_data;\n    adjustRotationToInterfaceOrientation.__stackDetails = _e;\n    return adjustRotationToInterfaceOrientation;\n  }();\n  var _worklet_16590673686333_init_data = {\n    code: \"function adjustVectorToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorTs3(data){const{InterfaceOrientation}=this.__closure;const{interfaceOrientation:interfaceOrientation,x:x,y:y}=data;if(interfaceOrientation===InterfaceOrientation.ROTATION_90){data.x=-y;data.y=x;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_270){data.x=y;data.y=-x;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_180){data.x*=-1;data.y*=-1;}return data;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"adjustVectorToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorTs3\\\",\\\"data\\\",\\\"InterfaceOrientation\\\",\\\"__closure\\\",\\\"interfaceOrientation\\\",\\\"x\\\",\\\"y\\\",\\\"ROTATION_90\\\",\\\"ROTATION_270\\\",\\\"ROTATION_180\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\\\"],\\\"mappings\\\":\\\"AA6DA,SAAAA,6EAA2DA,CAAAC,IAAA,QAAAC,oBAAA,OAAAC,SAAA,CAEzD,KAAM,CAAEC,oBAAoB,CAApBA,oBAAoB,CAAEC,CAAC,CAADA,CAAC,CAAEC,CAAA,CAAAA,CAAE,CAAC,CAAGL,IAAI,CAC3C,GAAIG,oBAAoB,GAAKF,oBAAoB,CAACK,WAAW,CAAE,CAC7DN,IAAI,CAACI,CAAC,CAAG,CAACC,CAAC,CACXL,IAAI,CAACK,CAAC,CAAGD,CAAC,CACZ,CAAC,IAAM,IAAID,oBAAoB,GAAKF,oBAAoB,CAACM,YAAY,CAAE,CACrEP,IAAI,CAACI,CAAC,CAAGC,CAAC,CACVL,IAAI,CAACK,CAAC,CAAG,CAACD,CAAC,CACb,CAAC,IAAM,IAAID,oBAAoB,GAAKF,oBAAoB,CAACO,YAAY,CAAE,CACrER,IAAI,CAACI,CAAC,EAAI,CAAC,CAAC,CACZJ,IAAI,CAACK,CAAC,EAAI,CAAC,CAAC,CACd,CACA,MAAO,CAAAL,IAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var adjustVectorToInterfaceOrientation = function () {\n    var _e = [new global.Error(), -2, -27];\n    var adjustVectorToInterfaceOrientation = function (data) {\n      var interfaceOrientation = data.interfaceOrientation,\n        x = data.x,\n        y = data.y;\n      if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_90) {\n        data.x = -y;\n        data.y = x;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_270) {\n        data.x = y;\n        data.y = -x;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_180) {\n        data.x *= -1;\n        data.y *= -1;\n      }\n      return data;\n    };\n    adjustVectorToInterfaceOrientation.__closure = {\n      InterfaceOrientation: _commonTypes.InterfaceOrientation\n    };\n    adjustVectorToInterfaceOrientation.__workletHash = 16590673686333;\n    adjustVectorToInterfaceOrientation.__initData = _worklet_16590673686333_init_data;\n    adjustVectorToInterfaceOrientation.__stackDetails = _e;\n    return adjustVectorToInterfaceOrientation;\n  }();\n  /**\n   * Lets you create animations based on data from the device's sensors.\n   *\n   * @param sensorType - Type of the sensor to use. Configured with\n   *   {@link SensorType} enum.\n   * @param config - The sensor configuration - {@link SensorConfig}.\n   * @returns An object containing the sensor measurements [shared\n   *   value](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value)\n   *   and a function to unregister the sensor\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useAnimatedSensor\n   */\n  var _worklet_2154297770743_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedSensorTs4(data){const{adjustToInterfaceOrientation,sensorType,SensorType,adjustRotationToInterfaceOrientation,adjustVectorToInterfaceOrientation,sensorData,callMicrotasks}=this.__closure;if(adjustToInterfaceOrientation){if(sensorType===SensorType.ROTATION){data=adjustRotationToInterfaceOrientation(data);}else{data=adjustVectorToInterfaceOrientation(data);}}sensorData.value=data;callMicrotasks();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedSensorTs4\\\",\\\"data\\\",\\\"adjustToInterfaceOrientation\\\",\\\"sensorType\\\",\\\"SensorType\\\",\\\"adjustRotationToInterfaceOrientation\\\",\\\"adjustVectorToInterfaceOrientation\\\",\\\"sensorData\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"ROTATION\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedSensor.ts\\\"],\\\"mappings\\\":\\\"AAiJmD,SAAAA,0CAASA,CAAAC,IAAA,QAAAC,4BAAA,CAAAC,UAAA,CAAAC,UAAA,CAAAC,oCAAA,CAAAC,kCAAA,CAAAC,UAAA,CAAAC,cAAA,OAAAC,SAAA,CAEtD,GAAIP,4BAA4B,CAAE,CAChC,GAAIC,UAAU,GAAKC,UAAU,CAACM,QAAQ,CAAE,CACtCT,IAAI,CAAGI,oCAAoC,CAACJ,IAAqB,CAAC,CACpE,CAAC,IAAM,CACLA,IAAI,CAAGK,kCAAkC,CAACL,IAAe,CAAC,CAC5D,CACF,CACAM,UAAU,CAACI,KAAK,CAAGV,IAAI,CACvBO,cAAc,CAAC,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedSensor(sensorType, userConfig) {\n    var userConfigRef = (0, _react.useRef)(userConfig);\n    var hasConfigChanged = userConfigRef.current?.adjustToInterfaceOrientation !== userConfig?.adjustToInterfaceOrientation || userConfigRef.current?.interval !== userConfig?.interval || userConfigRef.current?.iosReferenceFrame !== userConfig?.iosReferenceFrame;\n    if (hasConfigChanged) {\n      userConfigRef.current = {\n        ...userConfig\n      };\n    }\n    var config = (0, _react.useMemo)(() => ({\n      interval: 'auto',\n      adjustToInterfaceOrientation: true,\n      iosReferenceFrame: _commonTypes.IOSReferenceFrame.Auto,\n      ...userConfigRef.current\n    }), [userConfigRef.current]);\n    var ref = (0, _react.useRef)({\n      sensor: (0, _core.initializeSensor)(sensorType, config),\n      unregister: () => {\n        // NOOP\n      },\n      isAvailable: false,\n      config\n    });\n    (0, _react.useEffect)(() => {\n      ref.current = {\n        sensor: (0, _core.initializeSensor)(sensorType, config),\n        unregister: () => {\n          // NOOP\n        },\n        isAvailable: false,\n        config\n      };\n      var sensorData = ref.current.sensor;\n      var adjustToInterfaceOrientation = ref.current.config.adjustToInterfaceOrientation;\n      var id = (0, _core.registerSensor)(sensorType, config, function () {\n        var _e = [new global.Error(), -8, -27];\n        var reactNativeReanimated_useAnimatedSensorTs4 = function (data) {\n          if (adjustToInterfaceOrientation) {\n            if (sensorType === _commonTypes.SensorType.ROTATION) {\n              data = adjustRotationToInterfaceOrientation(data);\n            } else {\n              data = adjustVectorToInterfaceOrientation(data);\n            }\n          }\n          sensorData.value = data;\n          (0, _threads.callMicrotasks)();\n        };\n        reactNativeReanimated_useAnimatedSensorTs4.__closure = {\n          adjustToInterfaceOrientation,\n          sensorType,\n          SensorType: _commonTypes.SensorType,\n          adjustRotationToInterfaceOrientation,\n          adjustVectorToInterfaceOrientation,\n          sensorData,\n          callMicrotasks: _threads.callMicrotasks\n        };\n        reactNativeReanimated_useAnimatedSensorTs4.__workletHash = 2154297770743;\n        reactNativeReanimated_useAnimatedSensorTs4.__initData = _worklet_2154297770743_init_data;\n        reactNativeReanimated_useAnimatedSensorTs4.__stackDetails = _e;\n        return reactNativeReanimated_useAnimatedSensorTs4;\n      }());\n      if (id !== -1) {\n        // if sensor is available\n        ref.current.unregister = () => (0, _core.unregisterSensor)(id);\n        ref.current.isAvailable = true;\n      } else {\n        // if sensor is unavailable\n        ref.current.unregister = () => {\n          // NOOP\n        };\n        ref.current.isAvailable = false;\n      }\n      return () => {\n        ref.current.unregister();\n      };\n    }, [sensorType, config]);\n    return ref.current;\n  }\n});", "lineCount": 205, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedSensor"], [7, 27, 1, 13], [7, 30, 1, 13, "useAnimatedSensor"], [7, 47, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 10, 0], [9, 6, 10, 0, "_commonTypes"], [9, 18, 10, 0], [9, 21, 10, 0, "require"], [9, 28, 10, 0], [9, 29, 10, 0, "_dependencyMap"], [9, 43, 10, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_core"], [10, 11, 15, 0], [10, 14, 15, 0, "require"], [10, 21, 15, 0], [10, 22, 15, 0, "_dependencyMap"], [10, 36, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_threads"], [11, 14, 16, 0], [11, 17, 16, 0, "require"], [11, 24, 16, 0], [11, 25, 16, 0, "_dependencyMap"], [11, 39, 16, 0], [12, 2, 18, 0], [13, 2, 19, 0], [14, 2, 19, 0], [14, 6, 19, 0, "_worklet_763515177202_init_data"], [14, 37, 19, 0], [15, 4, 19, 0, "code"], [15, 8, 19, 0], [16, 4, 19, 0, "location"], [16, 12, 19, 0], [17, 4, 19, 0, "sourceMap"], [17, 13, 19, 0], [18, 4, 19, 0, "version"], [18, 11, 19, 0], [19, 2, 19, 0], [20, 2, 19, 0], [20, 6, 19, 0, "eulerToQuaternion"], [20, 23, 19, 0], [20, 26, 20, 0], [21, 4, 20, 0], [21, 8, 20, 0, "_e"], [21, 10, 20, 0], [21, 18, 20, 0, "global"], [21, 24, 20, 0], [21, 25, 20, 0, "Error"], [21, 30, 20, 0], [22, 4, 20, 0], [22, 8, 20, 0, "eulerToQuaternion"], [22, 25, 20, 0], [22, 37, 20, 0, "eulerToQuaternion"], [22, 38, 20, 27, "pitch"], [22, 43, 20, 40], [22, 45, 20, 42, "roll"], [22, 49, 20, 54], [22, 51, 20, 56, "yaw"], [22, 54, 20, 67], [22, 56, 20, 69], [23, 6, 22, 2], [23, 10, 22, 8, "c1"], [23, 12, 22, 10], [23, 15, 22, 13, "Math"], [23, 19, 22, 17], [23, 20, 22, 18, "cos"], [23, 23, 22, 21], [23, 24, 22, 22, "pitch"], [23, 29, 22, 27], [23, 32, 22, 30], [23, 33, 22, 31], [23, 34, 22, 32], [24, 6, 23, 2], [24, 10, 23, 8, "s1"], [24, 12, 23, 10], [24, 15, 23, 13, "Math"], [24, 19, 23, 17], [24, 20, 23, 18, "sin"], [24, 23, 23, 21], [24, 24, 23, 22, "pitch"], [24, 29, 23, 27], [24, 32, 23, 30], [24, 33, 23, 31], [24, 34, 23, 32], [25, 6, 24, 2], [25, 10, 24, 8, "c2"], [25, 12, 24, 10], [25, 15, 24, 13, "Math"], [25, 19, 24, 17], [25, 20, 24, 18, "cos"], [25, 23, 24, 21], [25, 24, 24, 22, "roll"], [25, 28, 24, 26], [25, 31, 24, 29], [25, 32, 24, 30], [25, 33, 24, 31], [26, 6, 25, 2], [26, 10, 25, 8, "s2"], [26, 12, 25, 10], [26, 15, 25, 13, "Math"], [26, 19, 25, 17], [26, 20, 25, 18, "sin"], [26, 23, 25, 21], [26, 24, 25, 22, "roll"], [26, 28, 25, 26], [26, 31, 25, 29], [26, 32, 25, 30], [26, 33, 25, 31], [27, 6, 26, 2], [27, 10, 26, 8, "c3"], [27, 12, 26, 10], [27, 15, 26, 13, "Math"], [27, 19, 26, 17], [27, 20, 26, 18, "cos"], [27, 23, 26, 21], [27, 24, 26, 22, "yaw"], [27, 27, 26, 25], [27, 30, 26, 28], [27, 31, 26, 29], [27, 32, 26, 30], [28, 6, 27, 2], [28, 10, 27, 8, "s3"], [28, 12, 27, 10], [28, 15, 27, 13, "Math"], [28, 19, 27, 17], [28, 20, 27, 18, "sin"], [28, 23, 27, 21], [28, 24, 27, 22, "yaw"], [28, 27, 27, 25], [28, 30, 27, 28], [28, 31, 27, 29], [28, 32, 27, 30], [29, 6, 29, 2], [29, 13, 29, 9], [29, 14, 30, 4, "s1"], [29, 16, 30, 6], [29, 19, 30, 9, "c2"], [29, 21, 30, 11], [29, 24, 30, 14, "c3"], [29, 26, 30, 16], [29, 29, 30, 19, "c1"], [29, 31, 30, 21], [29, 34, 30, 24, "s2"], [29, 36, 30, 26], [29, 39, 30, 29, "s3"], [29, 41, 30, 31], [29, 43, 31, 4, "c1"], [29, 45, 31, 6], [29, 48, 31, 9, "s2"], [29, 50, 31, 11], [29, 53, 31, 14, "c3"], [29, 55, 31, 16], [29, 58, 31, 19, "s1"], [29, 60, 31, 21], [29, 63, 31, 24, "c2"], [29, 65, 31, 26], [29, 68, 31, 29, "s3"], [29, 70, 31, 31], [29, 72, 32, 4, "c1"], [29, 74, 32, 6], [29, 77, 32, 9, "c2"], [29, 79, 32, 11], [29, 82, 32, 14, "s3"], [29, 84, 32, 16], [29, 87, 32, 19, "s1"], [29, 89, 32, 21], [29, 92, 32, 24, "s2"], [29, 94, 32, 26], [29, 97, 32, 29, "c3"], [29, 99, 32, 31], [29, 101, 33, 4, "c1"], [29, 103, 33, 6], [29, 106, 33, 9, "c2"], [29, 108, 33, 11], [29, 111, 33, 14, "c3"], [29, 113, 33, 16], [29, 116, 33, 19, "s1"], [29, 118, 33, 21], [29, 121, 33, 24, "s2"], [29, 123, 33, 26], [29, 126, 33, 29, "s3"], [29, 128, 33, 31], [29, 129, 34, 3], [30, 4, 35, 0], [30, 5, 35, 1], [31, 4, 35, 1, "eulerToQuaternion"], [31, 21, 35, 1], [31, 22, 35, 1, "__closure"], [31, 31, 35, 1], [32, 4, 35, 1, "eulerToQuaternion"], [32, 21, 35, 1], [32, 22, 35, 1, "__workletHash"], [32, 35, 35, 1], [33, 4, 35, 1, "eulerToQuaternion"], [33, 21, 35, 1], [33, 22, 35, 1, "__initData"], [33, 32, 35, 1], [33, 35, 35, 1, "_worklet_763515177202_init_data"], [33, 66, 35, 1], [34, 4, 35, 1, "eulerToQuaternion"], [34, 21, 35, 1], [34, 22, 35, 1, "__stackDetails"], [34, 36, 35, 1], [34, 39, 35, 1, "_e"], [34, 41, 35, 1], [35, 4, 35, 1], [35, 11, 35, 1, "eulerToQuaternion"], [35, 28, 35, 1], [36, 2, 35, 1], [36, 3, 20, 0], [37, 2, 20, 0], [37, 6, 20, 0, "_worklet_17236990978851_init_data"], [37, 39, 20, 0], [38, 4, 20, 0, "code"], [38, 8, 20, 0], [39, 4, 20, 0, "location"], [39, 12, 20, 0], [40, 4, 20, 0, "sourceMap"], [40, 13, 20, 0], [41, 4, 20, 0, "version"], [41, 11, 20, 0], [42, 2, 20, 0], [43, 2, 20, 0], [43, 6, 20, 0, "adjustRotationToInterfaceOrientation"], [43, 42, 20, 0], [43, 45, 37, 0], [44, 4, 37, 0], [44, 8, 37, 0, "_e"], [44, 10, 37, 0], [44, 18, 37, 0, "global"], [44, 24, 37, 0], [44, 25, 37, 0, "Error"], [44, 30, 37, 0], [45, 4, 37, 0], [45, 8, 37, 0, "adjustRotationToInterfaceOrientation"], [45, 44, 37, 0], [45, 56, 37, 0, "adjustRotationToInterfaceOrientation"], [45, 57, 37, 46, "data"], [45, 61, 37, 65], [45, 63, 37, 67], [46, 6, 39, 2], [46, 10, 39, 10, "interfaceOrientation"], [46, 30, 39, 30], [46, 33, 39, 53, "data"], [46, 37, 39, 57], [46, 38, 39, 10, "interfaceOrientation"], [46, 58, 39, 30], [47, 8, 39, 32, "pitch"], [47, 13, 39, 37], [47, 16, 39, 53, "data"], [47, 20, 39, 57], [47, 21, 39, 32, "pitch"], [47, 26, 39, 37], [48, 8, 39, 39, "roll"], [48, 12, 39, 43], [48, 15, 39, 53, "data"], [48, 19, 39, 57], [48, 20, 39, 39, "roll"], [48, 24, 39, 43], [49, 8, 39, 45, "yaw"], [49, 11, 39, 48], [49, 14, 39, 53, "data"], [49, 18, 39, 57], [49, 19, 39, 45, "yaw"], [49, 22, 39, 48], [50, 6, 40, 2], [50, 10, 40, 6, "interfaceOrientation"], [50, 30, 40, 26], [50, 35, 40, 31, "InterfaceOrientation"], [50, 68, 40, 51], [50, 69, 40, 52, "ROTATION_90"], [50, 80, 40, 63], [50, 82, 40, 65], [51, 8, 41, 4, "data"], [51, 12, 41, 8], [51, 13, 41, 9, "pitch"], [51, 18, 41, 14], [51, 21, 41, 17, "roll"], [51, 25, 41, 21], [52, 8, 42, 4, "data"], [52, 12, 42, 8], [52, 13, 42, 9, "roll"], [52, 17, 42, 13], [52, 20, 42, 16], [52, 21, 42, 17, "pitch"], [52, 26, 42, 22], [53, 8, 43, 4, "data"], [53, 12, 43, 8], [53, 13, 43, 9, "yaw"], [53, 16, 43, 12], [53, 19, 43, 15, "yaw"], [53, 22, 43, 18], [53, 25, 43, 21, "Math"], [53, 29, 43, 25], [53, 30, 43, 26, "PI"], [53, 32, 43, 28], [53, 35, 43, 31], [53, 36, 43, 32], [54, 6, 44, 2], [54, 7, 44, 3], [54, 13, 44, 9], [54, 17, 44, 13, "interfaceOrientation"], [54, 37, 44, 33], [54, 42, 44, 38, "InterfaceOrientation"], [54, 75, 44, 58], [54, 76, 44, 59, "ROTATION_270"], [54, 88, 44, 71], [54, 90, 44, 73], [55, 8, 45, 4, "data"], [55, 12, 45, 8], [55, 13, 45, 9, "pitch"], [55, 18, 45, 14], [55, 21, 45, 17], [55, 22, 45, 18, "roll"], [55, 26, 45, 22], [56, 8, 46, 4, "data"], [56, 12, 46, 8], [56, 13, 46, 9, "roll"], [56, 17, 46, 13], [56, 20, 46, 16, "pitch"], [56, 25, 46, 21], [57, 8, 47, 4, "data"], [57, 12, 47, 8], [57, 13, 47, 9, "yaw"], [57, 16, 47, 12], [57, 19, 47, 15, "yaw"], [57, 22, 47, 18], [57, 25, 47, 21, "Math"], [57, 29, 47, 25], [57, 30, 47, 26, "PI"], [57, 32, 47, 28], [57, 35, 47, 31], [57, 36, 47, 32], [58, 6, 48, 2], [58, 7, 48, 3], [58, 13, 48, 9], [58, 17, 48, 13, "interfaceOrientation"], [58, 37, 48, 33], [58, 42, 48, 38, "InterfaceOrientation"], [58, 75, 48, 58], [58, 76, 48, 59, "ROTATION_180"], [58, 88, 48, 71], [58, 90, 48, 73], [59, 8, 49, 4, "data"], [59, 12, 49, 8], [59, 13, 49, 9, "pitch"], [59, 18, 49, 14], [59, 22, 49, 18], [59, 23, 49, 19], [59, 24, 49, 20], [60, 8, 50, 4, "data"], [60, 12, 50, 8], [60, 13, 50, 9, "roll"], [60, 17, 50, 13], [60, 21, 50, 17], [60, 22, 50, 18], [60, 23, 50, 19], [61, 8, 51, 4, "data"], [61, 12, 51, 8], [61, 13, 51, 9, "yaw"], [61, 16, 51, 12], [61, 20, 51, 16], [61, 21, 51, 17], [61, 22, 51, 18], [62, 6, 52, 2], [63, 6, 54, 2], [63, 10, 54, 8, "q"], [63, 11, 54, 9], [63, 14, 54, 12, "eulerToQuaternion"], [63, 31, 54, 29], [63, 32, 54, 30, "data"], [63, 36, 54, 34], [63, 37, 54, 35, "pitch"], [63, 42, 54, 40], [63, 44, 54, 42, "data"], [63, 48, 54, 46], [63, 49, 54, 47, "roll"], [63, 53, 54, 51], [63, 55, 54, 53, "data"], [63, 59, 54, 57], [63, 60, 54, 58, "yaw"], [63, 63, 54, 61], [63, 64, 54, 62], [64, 6, 55, 2, "data"], [64, 10, 55, 6], [64, 11, 55, 7, "qx"], [64, 13, 55, 9], [64, 16, 55, 12, "q"], [64, 17, 55, 13], [64, 18, 55, 14], [64, 19, 55, 15], [64, 20, 55, 16], [65, 6, 56, 2, "data"], [65, 10, 56, 6], [65, 11, 56, 7, "qy"], [65, 13, 56, 9], [65, 16, 56, 12, "q"], [65, 17, 56, 13], [65, 18, 56, 14], [65, 19, 56, 15], [65, 20, 56, 16], [66, 6, 57, 2, "data"], [66, 10, 57, 6], [66, 11, 57, 7, "qz"], [66, 13, 57, 9], [66, 16, 57, 12, "q"], [66, 17, 57, 13], [66, 18, 57, 14], [66, 19, 57, 15], [66, 20, 57, 16], [67, 6, 58, 2, "data"], [67, 10, 58, 6], [67, 11, 58, 7, "qw"], [67, 13, 58, 9], [67, 16, 58, 12, "q"], [67, 17, 58, 13], [67, 18, 58, 14], [67, 19, 58, 15], [67, 20, 58, 16], [68, 6, 59, 2], [68, 13, 59, 9, "data"], [68, 17, 59, 13], [69, 4, 60, 0], [69, 5, 60, 1], [70, 4, 60, 1, "adjustRotationToInterfaceOrientation"], [70, 40, 60, 1], [70, 41, 60, 1, "__closure"], [70, 50, 60, 1], [71, 6, 60, 1, "InterfaceOrientation"], [71, 26, 60, 1], [71, 28, 40, 31, "InterfaceOrientation"], [71, 61, 40, 51], [72, 6, 40, 51, "eulerToQuaternion"], [73, 4, 40, 51], [74, 4, 40, 51, "adjustRotationToInterfaceOrientation"], [74, 40, 40, 51], [74, 41, 40, 51, "__workletHash"], [74, 54, 40, 51], [75, 4, 40, 51, "adjustRotationToInterfaceOrientation"], [75, 40, 40, 51], [75, 41, 40, 51, "__initData"], [75, 51, 40, 51], [75, 54, 40, 51, "_worklet_17236990978851_init_data"], [75, 87, 40, 51], [76, 4, 40, 51, "adjustRotationToInterfaceOrientation"], [76, 40, 40, 51], [76, 41, 40, 51, "__stackDetails"], [76, 55, 40, 51], [76, 58, 40, 51, "_e"], [76, 60, 40, 51], [77, 4, 40, 51], [77, 11, 40, 51, "adjustRotationToInterfaceOrientation"], [77, 47, 40, 51], [78, 2, 40, 51], [78, 3, 37, 0], [79, 2, 37, 0], [79, 6, 37, 0, "_worklet_16590673686333_init_data"], [79, 39, 37, 0], [80, 4, 37, 0, "code"], [80, 8, 37, 0], [81, 4, 37, 0, "location"], [81, 12, 37, 0], [82, 4, 37, 0, "sourceMap"], [82, 13, 37, 0], [83, 4, 37, 0, "version"], [83, 11, 37, 0], [84, 2, 37, 0], [85, 2, 37, 0], [85, 6, 37, 0, "adjustVectorToInterfaceOrientation"], [85, 40, 37, 0], [85, 43, 62, 0], [86, 4, 62, 0], [86, 8, 62, 0, "_e"], [86, 10, 62, 0], [86, 18, 62, 0, "global"], [86, 24, 62, 0], [86, 25, 62, 0, "Error"], [86, 30, 62, 0], [87, 4, 62, 0], [87, 8, 62, 0, "adjustVectorToInterfaceOrientation"], [87, 42, 62, 0], [87, 54, 62, 0, "adjustVectorToInterfaceOrientation"], [87, 55, 62, 44, "data"], [87, 59, 62, 57], [87, 61, 62, 59], [88, 6, 64, 2], [88, 10, 64, 10, "interfaceOrientation"], [88, 30, 64, 30], [88, 33, 64, 41, "data"], [88, 37, 64, 45], [88, 38, 64, 10, "interfaceOrientation"], [88, 58, 64, 30], [89, 8, 64, 32, "x"], [89, 9, 64, 33], [89, 12, 64, 41, "data"], [89, 16, 64, 45], [89, 17, 64, 32, "x"], [89, 18, 64, 33], [90, 8, 64, 35, "y"], [90, 9, 64, 36], [90, 12, 64, 41, "data"], [90, 16, 64, 45], [90, 17, 64, 35, "y"], [90, 18, 64, 36], [91, 6, 65, 2], [91, 10, 65, 6, "interfaceOrientation"], [91, 30, 65, 26], [91, 35, 65, 31, "InterfaceOrientation"], [91, 68, 65, 51], [91, 69, 65, 52, "ROTATION_90"], [91, 80, 65, 63], [91, 82, 65, 65], [92, 8, 66, 4, "data"], [92, 12, 66, 8], [92, 13, 66, 9, "x"], [92, 14, 66, 10], [92, 17, 66, 13], [92, 18, 66, 14, "y"], [92, 19, 66, 15], [93, 8, 67, 4, "data"], [93, 12, 67, 8], [93, 13, 67, 9, "y"], [93, 14, 67, 10], [93, 17, 67, 13, "x"], [93, 18, 67, 14], [94, 6, 68, 2], [94, 7, 68, 3], [94, 13, 68, 9], [94, 17, 68, 13, "interfaceOrientation"], [94, 37, 68, 33], [94, 42, 68, 38, "InterfaceOrientation"], [94, 75, 68, 58], [94, 76, 68, 59, "ROTATION_270"], [94, 88, 68, 71], [94, 90, 68, 73], [95, 8, 69, 4, "data"], [95, 12, 69, 8], [95, 13, 69, 9, "x"], [95, 14, 69, 10], [95, 17, 69, 13, "y"], [95, 18, 69, 14], [96, 8, 70, 4, "data"], [96, 12, 70, 8], [96, 13, 70, 9, "y"], [96, 14, 70, 10], [96, 17, 70, 13], [96, 18, 70, 14, "x"], [96, 19, 70, 15], [97, 6, 71, 2], [97, 7, 71, 3], [97, 13, 71, 9], [97, 17, 71, 13, "interfaceOrientation"], [97, 37, 71, 33], [97, 42, 71, 38, "InterfaceOrientation"], [97, 75, 71, 58], [97, 76, 71, 59, "ROTATION_180"], [97, 88, 71, 71], [97, 90, 71, 73], [98, 8, 72, 4, "data"], [98, 12, 72, 8], [98, 13, 72, 9, "x"], [98, 14, 72, 10], [98, 18, 72, 14], [98, 19, 72, 15], [98, 20, 72, 16], [99, 8, 73, 4, "data"], [99, 12, 73, 8], [99, 13, 73, 9, "y"], [99, 14, 73, 10], [99, 18, 73, 14], [99, 19, 73, 15], [99, 20, 73, 16], [100, 6, 74, 2], [101, 6, 75, 2], [101, 13, 75, 9, "data"], [101, 17, 75, 13], [102, 4, 76, 0], [102, 5, 76, 1], [103, 4, 76, 1, "adjustVectorToInterfaceOrientation"], [103, 38, 76, 1], [103, 39, 76, 1, "__closure"], [103, 48, 76, 1], [104, 6, 76, 1, "InterfaceOrientation"], [104, 26, 76, 1], [104, 28, 65, 31, "InterfaceOrientation"], [105, 4, 65, 51], [106, 4, 65, 51, "adjustVectorToInterfaceOrientation"], [106, 38, 65, 51], [106, 39, 65, 51, "__workletHash"], [106, 52, 65, 51], [107, 4, 65, 51, "adjustVectorToInterfaceOrientation"], [107, 38, 65, 51], [107, 39, 65, 51, "__initData"], [107, 49, 65, 51], [107, 52, 65, 51, "_worklet_16590673686333_init_data"], [107, 85, 65, 51], [108, 4, 65, 51, "adjustVectorToInterfaceOrientation"], [108, 38, 65, 51], [108, 39, 65, 51, "__stackDetails"], [108, 53, 65, 51], [108, 56, 65, 51, "_e"], [108, 58, 65, 51], [109, 4, 65, 51], [109, 11, 65, 51, "adjustVectorToInterfaceOrientation"], [109, 45, 65, 51], [110, 2, 65, 51], [110, 3, 62, 0], [111, 2, 78, 0], [112, 0, 79, 0], [113, 0, 80, 0], [114, 0, 81, 0], [115, 0, 82, 0], [116, 0, 83, 0], [117, 0, 84, 0], [118, 0, 85, 0], [119, 0, 86, 0], [120, 0, 87, 0], [121, 0, 88, 0], [122, 2, 78, 0], [122, 6, 78, 0, "_worklet_2154297770743_init_data"], [122, 38, 78, 0], [123, 4, 78, 0, "code"], [123, 8, 78, 0], [124, 4, 78, 0, "location"], [124, 12, 78, 0], [125, 4, 78, 0, "sourceMap"], [125, 13, 78, 0], [126, 4, 78, 0, "version"], [126, 11, 78, 0], [127, 2, 78, 0], [128, 2, 97, 7], [128, 11, 97, 16, "useAnimatedSensor"], [128, 28, 97, 33, "useAnimatedSensor"], [128, 29, 98, 2, "sensorType"], [128, 39, 98, 24], [128, 41, 99, 2, "userConfig"], [128, 51, 99, 36], [128, 53, 100, 59], [129, 4, 101, 2], [129, 8, 101, 8, "userConfigRef"], [129, 21, 101, 21], [129, 24, 101, 24], [129, 28, 101, 24, "useRef"], [129, 41, 101, 30], [129, 43, 101, 31, "userConfig"], [129, 53, 101, 41], [129, 54, 101, 42], [130, 4, 103, 2], [130, 8, 103, 8, "hasConfigChanged"], [130, 24, 103, 24], [130, 27, 104, 4, "userConfigRef"], [130, 40, 104, 17], [130, 41, 104, 18, "current"], [130, 48, 104, 25], [130, 50, 104, 27, "adjustToInterfaceOrientation"], [130, 78, 104, 55], [130, 83, 105, 6, "userConfig"], [130, 93, 105, 16], [130, 95, 105, 18, "adjustToInterfaceOrientation"], [130, 123, 105, 46], [130, 127, 106, 4, "userConfigRef"], [130, 140, 106, 17], [130, 141, 106, 18, "current"], [130, 148, 106, 25], [130, 150, 106, 27, "interval"], [130, 158, 106, 35], [130, 163, 106, 40, "userConfig"], [130, 173, 106, 50], [130, 175, 106, 52, "interval"], [130, 183, 106, 60], [130, 187, 107, 4, "userConfigRef"], [130, 200, 107, 17], [130, 201, 107, 18, "current"], [130, 208, 107, 25], [130, 210, 107, 27, "iosReferenceFrame"], [130, 227, 107, 44], [130, 232, 107, 49, "userConfig"], [130, 242, 107, 59], [130, 244, 107, 61, "iosReferenceFrame"], [130, 261, 107, 78], [131, 4, 109, 2], [131, 8, 109, 6, "hasConfigChanged"], [131, 24, 109, 22], [131, 26, 109, 24], [132, 6, 110, 4, "userConfigRef"], [132, 19, 110, 17], [132, 20, 110, 18, "current"], [132, 27, 110, 25], [132, 30, 110, 28], [133, 8, 110, 30], [133, 11, 110, 33, "userConfig"], [134, 6, 110, 44], [134, 7, 110, 45], [135, 4, 111, 2], [136, 4, 113, 2], [136, 8, 113, 8, "config"], [136, 14, 113, 28], [136, 17, 113, 31], [136, 21, 113, 31, "useMemo"], [136, 35, 113, 38], [136, 37, 114, 4], [136, 44, 114, 11], [137, 6, 115, 6, "interval"], [137, 14, 115, 14], [137, 16, 115, 16], [137, 22, 115, 22], [138, 6, 116, 6, "adjustToInterfaceOrientation"], [138, 34, 116, 34], [138, 36, 116, 36], [138, 40, 116, 40], [139, 6, 117, 6, "iosReferenceFrame"], [139, 23, 117, 23], [139, 25, 117, 25, "IOSReferenceFrame"], [139, 55, 117, 42], [139, 56, 117, 43, "Auto"], [139, 60, 117, 47], [140, 6, 118, 6], [140, 9, 118, 9, "userConfigRef"], [140, 22, 118, 22], [140, 23, 118, 23, "current"], [141, 4, 119, 4], [141, 5, 119, 5], [141, 6, 119, 6], [141, 8, 120, 4], [141, 9, 120, 5, "userConfigRef"], [141, 22, 120, 18], [141, 23, 120, 19, "current"], [141, 30, 120, 26], [141, 31, 121, 2], [141, 32, 121, 3], [142, 4, 123, 2], [142, 8, 123, 8, "ref"], [142, 11, 123, 11], [142, 14, 123, 14], [142, 18, 123, 14, "useRef"], [142, 31, 123, 20], [142, 33, 123, 62], [143, 6, 124, 4, "sensor"], [143, 12, 124, 10], [143, 14, 124, 12], [143, 18, 124, 12, "initializeSensor"], [143, 40, 124, 28], [143, 42, 124, 29, "sensorType"], [143, 52, 124, 39], [143, 54, 124, 41, "config"], [143, 60, 124, 47], [143, 61, 124, 48], [144, 6, 125, 4, "unregister"], [144, 16, 125, 14], [144, 18, 125, 16, "unregister"], [144, 19, 125, 16], [144, 24, 125, 22], [145, 8, 126, 6], [146, 6, 126, 6], [146, 7, 127, 5], [147, 6, 128, 4, "isAvailable"], [147, 17, 128, 15], [147, 19, 128, 17], [147, 24, 128, 22], [148, 6, 129, 4, "config"], [149, 4, 130, 2], [149, 5, 130, 3], [149, 6, 130, 4], [150, 4, 132, 2], [150, 8, 132, 2, "useEffect"], [150, 24, 132, 11], [150, 26, 132, 12], [150, 32, 132, 18], [151, 6, 133, 4, "ref"], [151, 9, 133, 7], [151, 10, 133, 8, "current"], [151, 17, 133, 15], [151, 20, 133, 18], [152, 8, 134, 6, "sensor"], [152, 14, 134, 12], [152, 16, 134, 14], [152, 20, 134, 14, "initializeSensor"], [152, 42, 134, 30], [152, 44, 134, 31, "sensorType"], [152, 54, 134, 41], [152, 56, 134, 43, "config"], [152, 62, 134, 49], [152, 63, 134, 50], [153, 8, 135, 6, "unregister"], [153, 18, 135, 16], [153, 20, 135, 18, "unregister"], [153, 21, 135, 18], [153, 26, 135, 24], [154, 10, 136, 8], [155, 8, 136, 8], [155, 9, 137, 7], [156, 8, 138, 6, "isAvailable"], [156, 19, 138, 17], [156, 21, 138, 19], [156, 26, 138, 24], [157, 8, 139, 6, "config"], [158, 6, 140, 4], [158, 7, 140, 5], [159, 6, 142, 4], [159, 10, 142, 10, "sensorData"], [159, 20, 142, 20], [159, 23, 142, 23, "ref"], [159, 26, 142, 26], [159, 27, 142, 27, "current"], [159, 34, 142, 34], [159, 35, 142, 35, "sensor"], [159, 41, 142, 41], [160, 6, 143, 4], [160, 10, 143, 10, "adjustToInterfaceOrientation"], [160, 38, 143, 38], [160, 41, 144, 6, "ref"], [160, 44, 144, 9], [160, 45, 144, 10, "current"], [160, 52, 144, 17], [160, 53, 144, 18, "config"], [160, 59, 144, 24], [160, 60, 144, 25, "adjustToInterfaceOrientation"], [160, 88, 144, 53], [161, 6, 146, 4], [161, 10, 146, 10, "id"], [161, 12, 146, 12], [161, 15, 146, 15], [161, 19, 146, 15, "registerSensor"], [161, 39, 146, 29], [161, 41, 146, 30, "sensorType"], [161, 51, 146, 40], [161, 53, 146, 42, "config"], [161, 59, 146, 48], [161, 61, 146, 50], [162, 8, 146, 50], [162, 12, 146, 50, "_e"], [162, 14, 146, 50], [162, 22, 146, 50, "global"], [162, 28, 146, 50], [162, 29, 146, 50, "Error"], [162, 34, 146, 50], [163, 8, 146, 50], [163, 12, 146, 50, "reactNativeReanimated_useAnimatedSensorTs4"], [163, 54, 146, 50], [163, 66, 146, 50, "reactNativeReanimated_useAnimatedSensorTs4"], [163, 67, 146, 51, "data"], [163, 71, 146, 55], [163, 73, 146, 60], [164, 10, 148, 6], [164, 14, 148, 10, "adjustToInterfaceOrientation"], [164, 42, 148, 38], [164, 44, 148, 40], [165, 12, 149, 8], [165, 16, 149, 12, "sensorType"], [165, 26, 149, 22], [165, 31, 149, 27, "SensorType"], [165, 54, 149, 37], [165, 55, 149, 38, "ROTATION"], [165, 63, 149, 46], [165, 65, 149, 48], [166, 14, 150, 10, "data"], [166, 18, 150, 14], [166, 21, 150, 17, "adjustRotationToInterfaceOrientation"], [166, 57, 150, 53], [166, 58, 150, 54, "data"], [166, 62, 150, 75], [166, 63, 150, 76], [167, 12, 151, 8], [167, 13, 151, 9], [167, 19, 151, 15], [168, 14, 152, 10, "data"], [168, 18, 152, 14], [168, 21, 152, 17, "adjustVectorToInterfaceOrientation"], [168, 55, 152, 51], [168, 56, 152, 52, "data"], [168, 60, 152, 67], [168, 61, 152, 68], [169, 12, 153, 8], [170, 10, 154, 6], [171, 10, 155, 6, "sensorData"], [171, 20, 155, 16], [171, 21, 155, 17, "value"], [171, 26, 155, 22], [171, 29, 155, 25, "data"], [171, 33, 155, 29], [172, 10, 156, 6], [172, 14, 156, 6, "callMicrotasks"], [172, 37, 156, 20], [172, 39, 156, 21], [172, 40, 156, 22], [173, 8, 157, 4], [173, 9, 157, 5], [174, 8, 157, 5, "reactNativeReanimated_useAnimatedSensorTs4"], [174, 50, 157, 5], [174, 51, 157, 5, "__closure"], [174, 60, 157, 5], [175, 10, 157, 5, "adjustToInterfaceOrientation"], [175, 38, 157, 5], [176, 10, 157, 5, "sensorType"], [176, 20, 157, 5], [177, 10, 157, 5, "SensorType"], [177, 20, 157, 5], [177, 22, 149, 27, "SensorType"], [177, 45, 149, 37], [178, 10, 149, 37, "adjustRotationToInterfaceOrientation"], [178, 46, 149, 37], [179, 10, 149, 37, "adjustVectorToInterfaceOrientation"], [179, 44, 149, 37], [180, 10, 149, 37, "sensorData"], [180, 20, 149, 37], [181, 10, 149, 37, "callMicrotasks"], [181, 24, 149, 37], [181, 26, 156, 6, "callMicrotasks"], [182, 8, 156, 20], [183, 8, 156, 20, "reactNativeReanimated_useAnimatedSensorTs4"], [183, 50, 156, 20], [183, 51, 156, 20, "__workletHash"], [183, 64, 156, 20], [184, 8, 156, 20, "reactNativeReanimated_useAnimatedSensorTs4"], [184, 50, 156, 20], [184, 51, 156, 20, "__initData"], [184, 61, 156, 20], [184, 64, 156, 20, "_worklet_2154297770743_init_data"], [184, 96, 156, 20], [185, 8, 156, 20, "reactNativeReanimated_useAnimatedSensorTs4"], [185, 50, 156, 20], [185, 51, 156, 20, "__stackDetails"], [185, 65, 156, 20], [185, 68, 156, 20, "_e"], [185, 70, 156, 20], [186, 8, 156, 20], [186, 15, 156, 20, "reactNativeReanimated_useAnimatedSensorTs4"], [186, 57, 156, 20], [187, 6, 156, 20], [187, 7, 146, 50], [187, 9, 157, 5], [187, 10, 157, 6], [188, 6, 159, 4], [188, 10, 159, 8, "id"], [188, 12, 159, 10], [188, 17, 159, 15], [188, 18, 159, 16], [188, 19, 159, 17], [188, 21, 159, 19], [189, 8, 160, 6], [190, 8, 161, 6, "ref"], [190, 11, 161, 9], [190, 12, 161, 10, "current"], [190, 19, 161, 17], [190, 20, 161, 18, "unregister"], [190, 30, 161, 28], [190, 33, 161, 31], [190, 39, 161, 37], [190, 43, 161, 37, "unregisterSensor"], [190, 65, 161, 53], [190, 67, 161, 54, "id"], [190, 69, 161, 56], [190, 70, 161, 57], [191, 8, 162, 6, "ref"], [191, 11, 162, 9], [191, 12, 162, 10, "current"], [191, 19, 162, 17], [191, 20, 162, 18, "isAvailable"], [191, 31, 162, 29], [191, 34, 162, 32], [191, 38, 162, 36], [192, 6, 163, 4], [192, 7, 163, 5], [192, 13, 163, 11], [193, 8, 164, 6], [194, 8, 165, 6, "ref"], [194, 11, 165, 9], [194, 12, 165, 10, "current"], [194, 19, 165, 17], [194, 20, 165, 18, "unregister"], [194, 30, 165, 28], [194, 33, 165, 31], [194, 39, 165, 37], [195, 10, 166, 8], [196, 8, 166, 8], [196, 9, 167, 7], [197, 8, 168, 6, "ref"], [197, 11, 168, 9], [197, 12, 168, 10, "current"], [197, 19, 168, 17], [197, 20, 168, 18, "isAvailable"], [197, 31, 168, 29], [197, 34, 168, 32], [197, 39, 168, 37], [198, 6, 169, 4], [199, 6, 171, 4], [199, 13, 171, 11], [199, 19, 171, 17], [200, 8, 172, 6, "ref"], [200, 11, 172, 9], [200, 12, 172, 10, "current"], [200, 19, 172, 17], [200, 20, 172, 18, "unregister"], [200, 30, 172, 28], [200, 31, 172, 29], [200, 32, 172, 30], [201, 6, 173, 4], [201, 7, 173, 5], [202, 4, 174, 2], [202, 5, 174, 3], [202, 7, 174, 5], [202, 8, 174, 6, "sensorType"], [202, 18, 174, 16], [202, 20, 174, 18, "config"], [202, 26, 174, 24], [202, 27, 174, 25], [202, 28, 174, 26], [203, 4, 176, 2], [203, 11, 176, 9, "ref"], [203, 14, 176, 12], [203, 15, 176, 13, "current"], [203, 22, 176, 20], [204, 2, 177, 0], [205, 0, 177, 1], [205, 3]], "functionMap": {"names": ["<global>", "eulerToQuaternion", "adjustRotationToInterfaceOrientation", "adjustVectorToInterfaceOrientation", "useAnimatedSensor", "useMemo$argument_0", "useRef$argument_0.unregister", "useEffect$argument_0", "ref.current.unregister", "registerSensor$argument_2", "<anonymous>"], "mappings": "AAA;ACmB;CDe;AEE;CFuB;AGE;CHc;OIqB;ICiB;MDK;gBEM;KFE;YGK;kBCG;ODE;kDES;KFW;+BCI,0BD;+BCI;ODE;WGI;KHE;GHC;CJG"}}, "type": "js/module"}]}