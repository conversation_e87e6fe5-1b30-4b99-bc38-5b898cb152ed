{"dependencies": [{"name": "./PlatformColorValueTypes.ios", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 69}}], "key": "c8tPF+AGVDz8bdfTtY3LxtKJ+J4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.DynamicColorIOS = void 0;\n  var _PlatformColorValueTypes = require(_dependencyMap[0], \"./PlatformColorValueTypes.ios\");\n  var DynamicColorIOS = tuple => {\n    return (0, _PlatformColorValueTypes.DynamicColorIOSPrivate)({\n      light: tuple.light,\n      dark: tuple.dark,\n      highContrastLight: tuple.highContrastLight,\n      highContrastDark: tuple.highContrastDark\n    });\n  };\n  exports.DynamicColorIOS = DynamicColorIOS;\n});", "lineCount": 16, "map": [[6, 2, 13, 0], [6, 6, 13, 0, "_PlatformColorValueTypes"], [6, 30, 13, 0], [6, 33, 13, 0, "require"], [6, 40, 13, 0], [6, 41, 13, 0, "_dependencyMap"], [6, 55, 13, 0], [7, 2, 22, 7], [7, 6, 22, 13, "DynamicColorIOS"], [7, 21, 22, 28], [7, 24, 22, 32, "tuple"], [7, 29, 22, 59], [7, 33, 22, 76], [8, 4, 23, 2], [8, 11, 23, 9], [8, 15, 23, 9, "DynamicColorIOSPrivate"], [8, 62, 23, 31], [8, 64, 23, 32], [9, 6, 24, 4, "light"], [9, 11, 24, 9], [9, 13, 24, 11, "tuple"], [9, 18, 24, 16], [9, 19, 24, 17, "light"], [9, 24, 24, 22], [10, 6, 25, 4, "dark"], [10, 10, 25, 8], [10, 12, 25, 10, "tuple"], [10, 17, 25, 15], [10, 18, 25, 16, "dark"], [10, 22, 25, 20], [11, 6, 26, 4, "highContrastLight"], [11, 23, 26, 21], [11, 25, 26, 23, "tuple"], [11, 30, 26, 28], [11, 31, 26, 29, "highContrastLight"], [11, 48, 26, 46], [12, 6, 27, 4, "highContrastDark"], [12, 22, 27, 20], [12, 24, 27, 22, "tuple"], [12, 29, 27, 27], [12, 30, 27, 28, "highContrastDark"], [13, 4, 28, 2], [13, 5, 28, 3], [13, 6, 28, 4], [14, 2, 29, 0], [14, 3, 29, 1], [15, 2, 29, 2, "exports"], [15, 9, 29, 2], [15, 10, 29, 2, "DynamicColorIOS"], [15, 25, 29, 2], [15, 28, 29, 2, "DynamicColorIOS"], [15, 43, 29, 2], [16, 0, 29, 2], [16, 3]], "functionMap": {"names": ["<global>", "DynamicColorIOS"], "mappings": "AAA;+BCqB;CDO"}}, "type": "js/module"}]}