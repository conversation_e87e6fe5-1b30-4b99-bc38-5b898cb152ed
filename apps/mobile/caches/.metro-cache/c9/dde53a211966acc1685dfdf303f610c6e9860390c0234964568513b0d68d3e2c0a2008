{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 37}, "end": {"line": 2, "column": 33, "index": 70}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD = exports.MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD = exports.HammerInputNames = exports.HammerDirectionNames = exports.EventMap = exports.DirectionMap = exports.Direction = exports.DEG_RAD = exports.CONTENT_TOUCHES_QUICK_TAP_END_DELAY = exports.CONTENT_TOUCHES_DELAY = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _State = require(_dependencyMap[2], \"../State\");\n  const CONTENT_TOUCHES_DELAY = exports.CONTENT_TOUCHES_DELAY = 240;\n  const CONTENT_TOUCHES_QUICK_TAP_END_DELAY = exports.CONTENT_TOUCHES_QUICK_TAP_END_DELAY = 50;\n  const MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD = exports.MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD = 0.1;\n  const MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD = exports.MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD = 7;\n  const DEG_RAD = exports.DEG_RAD = Math.PI / 180; // Map Hammer values to RNGH\n\n  const EventMap = exports.EventMap = {\n    [_hammerjs.default.INPUT_START]: _State.State.BEGAN,\n    [_hammerjs.default.INPUT_MOVE]: _State.State.ACTIVE,\n    [_hammerjs.default.INPUT_END]: _State.State.END,\n    [_hammerjs.default.INPUT_CANCEL]: _State.State.FAILED\n  };\n  const Direction = exports.Direction = {\n    RIGHT: 1,\n    LEFT: 2,\n    UP: 4,\n    DOWN: 8\n  };\n  const DirectionMap = exports.DirectionMap = {\n    [_hammerjs.default.DIRECTION_RIGHT]: Direction.RIGHT,\n    [_hammerjs.default.DIRECTION_LEFT]: Direction.LEFT,\n    [_hammerjs.default.DIRECTION_UP]: Direction.UP,\n    [_hammerjs.default.DIRECTION_DOWN]: Direction.DOWN\n  };\n  const HammerInputNames = exports.HammerInputNames = {\n    [_hammerjs.default.INPUT_START]: 'START',\n    [_hammerjs.default.INPUT_MOVE]: 'MOVE',\n    [_hammerjs.default.INPUT_END]: 'END',\n    [_hammerjs.default.INPUT_CANCEL]: 'CANCEL'\n  };\n  const HammerDirectionNames = exports.HammerDirectionNames = {\n    [_hammerjs.default.DIRECTION_HORIZONTAL]: 'HORIZONTAL',\n    [_hammerjs.default.DIRECTION_UP]: 'UP',\n    [_hammerjs.default.DIRECTION_DOWN]: 'DOWN',\n    [_hammerjs.default.DIRECTION_VERTICAL]: 'VERTICAL',\n    [_hammerjs.default.DIRECTION_NONE]: 'NONE',\n    [_hammerjs.default.DIRECTION_ALL]: 'ALL',\n    [_hammerjs.default.DIRECTION_RIGHT]: 'RIGHT',\n    [_hammerjs.default.DIRECTION_LEFT]: 'LEFT'\n  };\n});", "lineCount": 49, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_hammerjs"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_State"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 3, 7], [9, 8, 3, 13, "CONTENT_TOUCHES_DELAY"], [9, 29, 3, 34], [9, 32, 3, 34, "exports"], [9, 39, 3, 34], [9, 40, 3, 34, "CONTENT_TOUCHES_DELAY"], [9, 61, 3, 34], [9, 64, 3, 37], [9, 67, 3, 40], [10, 2, 4, 7], [10, 8, 4, 13, "CONTENT_TOUCHES_QUICK_TAP_END_DELAY"], [10, 43, 4, 48], [10, 46, 4, 48, "exports"], [10, 53, 4, 48], [10, 54, 4, 48, "CONTENT_TOUCHES_QUICK_TAP_END_DELAY"], [10, 89, 4, 48], [10, 92, 4, 51], [10, 94, 4, 53], [11, 2, 5, 7], [11, 8, 5, 13, "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD"], [11, 44, 5, 49], [11, 47, 5, 49, "exports"], [11, 54, 5, 49], [11, 55, 5, 49, "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD"], [11, 91, 5, 49], [11, 94, 5, 52], [11, 97, 5, 55], [12, 2, 6, 7], [12, 8, 6, 13, "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD"], [12, 47, 6, 52], [12, 50, 6, 52, "exports"], [12, 57, 6, 52], [12, 58, 6, 52, "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD"], [12, 97, 6, 52], [12, 100, 6, 55], [12, 101, 6, 56], [13, 2, 7, 7], [13, 8, 7, 13, "DEG_RAD"], [13, 15, 7, 20], [13, 18, 7, 20, "exports"], [13, 25, 7, 20], [13, 26, 7, 20, "DEG_RAD"], [13, 33, 7, 20], [13, 36, 7, 23, "Math"], [13, 40, 7, 27], [13, 41, 7, 28, "PI"], [13, 43, 7, 30], [13, 46, 7, 33], [13, 49, 7, 36], [13, 50, 7, 37], [13, 51, 7, 38], [15, 2, 9, 7], [15, 8, 9, 13, "EventMap"], [15, 16, 9, 21], [15, 19, 9, 21, "exports"], [15, 26, 9, 21], [15, 27, 9, 21, "EventMap"], [15, 35, 9, 21], [15, 38, 9, 24], [16, 4, 10, 2], [16, 5, 10, 3, "Hammer"], [16, 22, 10, 9], [16, 23, 10, 10, "INPUT_START"], [16, 34, 10, 21], [16, 37, 10, 24, "State"], [16, 49, 10, 29], [16, 50, 10, 30, "BEGAN"], [16, 55, 10, 35], [17, 4, 11, 2], [17, 5, 11, 3, "Hammer"], [17, 22, 11, 9], [17, 23, 11, 10, "INPUT_MOVE"], [17, 33, 11, 20], [17, 36, 11, 23, "State"], [17, 48, 11, 28], [17, 49, 11, 29, "ACTIVE"], [17, 55, 11, 35], [18, 4, 12, 2], [18, 5, 12, 3, "Hammer"], [18, 22, 12, 9], [18, 23, 12, 10, "INPUT_END"], [18, 32, 12, 19], [18, 35, 12, 22, "State"], [18, 47, 12, 27], [18, 48, 12, 28, "END"], [18, 51, 12, 31], [19, 4, 13, 2], [19, 5, 13, 3, "Hammer"], [19, 22, 13, 9], [19, 23, 13, 10, "INPUT_CANCEL"], [19, 35, 13, 22], [19, 38, 13, 25, "State"], [19, 50, 13, 30], [19, 51, 13, 31, "FAILED"], [20, 2, 14, 0], [20, 3, 14, 1], [21, 2, 15, 7], [21, 8, 15, 13, "Direction"], [21, 17, 15, 22], [21, 20, 15, 22, "exports"], [21, 27, 15, 22], [21, 28, 15, 22, "Direction"], [21, 37, 15, 22], [21, 40, 15, 25], [22, 4, 16, 2, "RIGHT"], [22, 9, 16, 7], [22, 11, 16, 9], [22, 12, 16, 10], [23, 4, 17, 2, "LEFT"], [23, 8, 17, 6], [23, 10, 17, 8], [23, 11, 17, 9], [24, 4, 18, 2, "UP"], [24, 6, 18, 4], [24, 8, 18, 6], [24, 9, 18, 7], [25, 4, 19, 2, "DOWN"], [25, 8, 19, 6], [25, 10, 19, 8], [26, 2, 20, 0], [26, 3, 20, 1], [27, 2, 21, 7], [27, 8, 21, 13, "DirectionMap"], [27, 20, 21, 25], [27, 23, 21, 25, "exports"], [27, 30, 21, 25], [27, 31, 21, 25, "DirectionMap"], [27, 43, 21, 25], [27, 46, 21, 28], [28, 4, 22, 2], [28, 5, 22, 3, "Hammer"], [28, 22, 22, 9], [28, 23, 22, 10, "DIRECTION_RIGHT"], [28, 38, 22, 25], [28, 41, 22, 28, "Direction"], [28, 50, 22, 37], [28, 51, 22, 38, "RIGHT"], [28, 56, 22, 43], [29, 4, 23, 2], [29, 5, 23, 3, "Hammer"], [29, 22, 23, 9], [29, 23, 23, 10, "DIRECTION_LEFT"], [29, 37, 23, 24], [29, 40, 23, 27, "Direction"], [29, 49, 23, 36], [29, 50, 23, 37, "LEFT"], [29, 54, 23, 41], [30, 4, 24, 2], [30, 5, 24, 3, "Hammer"], [30, 22, 24, 9], [30, 23, 24, 10, "DIRECTION_UP"], [30, 35, 24, 22], [30, 38, 24, 25, "Direction"], [30, 47, 24, 34], [30, 48, 24, 35, "UP"], [30, 50, 24, 37], [31, 4, 25, 2], [31, 5, 25, 3, "Hammer"], [31, 22, 25, 9], [31, 23, 25, 10, "DIRECTION_DOWN"], [31, 37, 25, 24], [31, 40, 25, 27, "Direction"], [31, 49, 25, 36], [31, 50, 25, 37, "DOWN"], [32, 2, 26, 0], [32, 3, 26, 1], [33, 2, 27, 7], [33, 8, 27, 13, "HammerInputNames"], [33, 24, 27, 29], [33, 27, 27, 29, "exports"], [33, 34, 27, 29], [33, 35, 27, 29, "HammerInputNames"], [33, 51, 27, 29], [33, 54, 27, 32], [34, 4, 28, 2], [34, 5, 28, 3, "Hammer"], [34, 22, 28, 9], [34, 23, 28, 10, "INPUT_START"], [34, 34, 28, 21], [34, 37, 28, 24], [34, 44, 28, 31], [35, 4, 29, 2], [35, 5, 29, 3, "Hammer"], [35, 22, 29, 9], [35, 23, 29, 10, "INPUT_MOVE"], [35, 33, 29, 20], [35, 36, 29, 23], [35, 42, 29, 29], [36, 4, 30, 2], [36, 5, 30, 3, "Hammer"], [36, 22, 30, 9], [36, 23, 30, 10, "INPUT_END"], [36, 32, 30, 19], [36, 35, 30, 22], [36, 40, 30, 27], [37, 4, 31, 2], [37, 5, 31, 3, "Hammer"], [37, 22, 31, 9], [37, 23, 31, 10, "INPUT_CANCEL"], [37, 35, 31, 22], [37, 38, 31, 25], [38, 2, 32, 0], [38, 3, 32, 1], [39, 2, 33, 7], [39, 8, 33, 13, "HammerDirectionNames"], [39, 28, 33, 33], [39, 31, 33, 33, "exports"], [39, 38, 33, 33], [39, 39, 33, 33, "HammerDirectionNames"], [39, 59, 33, 33], [39, 62, 33, 36], [40, 4, 34, 2], [40, 5, 34, 3, "Hammer"], [40, 22, 34, 9], [40, 23, 34, 10, "DIRECTION_HORIZONTAL"], [40, 43, 34, 30], [40, 46, 34, 33], [40, 58, 34, 45], [41, 4, 35, 2], [41, 5, 35, 3, "Hammer"], [41, 22, 35, 9], [41, 23, 35, 10, "DIRECTION_UP"], [41, 35, 35, 22], [41, 38, 35, 25], [41, 42, 35, 29], [42, 4, 36, 2], [42, 5, 36, 3, "Hammer"], [42, 22, 36, 9], [42, 23, 36, 10, "DIRECTION_DOWN"], [42, 37, 36, 24], [42, 40, 36, 27], [42, 46, 36, 33], [43, 4, 37, 2], [43, 5, 37, 3, "Hammer"], [43, 22, 37, 9], [43, 23, 37, 10, "DIRECTION_VERTICAL"], [43, 41, 37, 28], [43, 44, 37, 31], [43, 54, 37, 41], [44, 4, 38, 2], [44, 5, 38, 3, "Hammer"], [44, 22, 38, 9], [44, 23, 38, 10, "DIRECTION_NONE"], [44, 37, 38, 24], [44, 40, 38, 27], [44, 46, 38, 33], [45, 4, 39, 2], [45, 5, 39, 3, "Hammer"], [45, 22, 39, 9], [45, 23, 39, 10, "DIRECTION_ALL"], [45, 36, 39, 23], [45, 39, 39, 26], [45, 44, 39, 31], [46, 4, 40, 2], [46, 5, 40, 3, "Hammer"], [46, 22, 40, 9], [46, 23, 40, 10, "DIRECTION_RIGHT"], [46, 38, 40, 25], [46, 41, 40, 28], [46, 48, 40, 35], [47, 4, 41, 2], [47, 5, 41, 3, "Hammer"], [47, 22, 41, 9], [47, 23, 41, 10, "DIRECTION_LEFT"], [47, 37, 41, 24], [47, 40, 41, 27], [48, 2, 42, 0], [48, 3, 42, 1], [49, 0, 42, 2], [49, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}