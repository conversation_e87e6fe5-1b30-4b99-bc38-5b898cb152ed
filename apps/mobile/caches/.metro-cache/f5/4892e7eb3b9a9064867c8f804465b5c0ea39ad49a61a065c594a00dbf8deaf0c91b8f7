{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Ribbon = exports.default = (0, _createLucideIcon.default)(\"Ribbon\", [[\"path\", {\n    d: \"M12 11.22C11 9.997 10 9 10 8a2 2 0 0 1 4 0c0 1-.998 2.002-2.01 3.22\",\n    key: \"1rnhq3\"\n  }], [\"path\", {\n    d: \"m12 18 2.57-3.5\",\n    key: \"116vt7\"\n  }], [\"path\", {\n    d: \"M6.243 9.016a7 7 0 0 1 11.507-.009\",\n    key: \"10dq0b\"\n  }], [\"path\", {\n    d: \"M9.35 14.53 12 11.22\",\n    key: \"tdsyp2\"\n  }], [\"path\", {\n    d: \"M9.35 14.53C7.728 12.246 6 10.221 6 7a6 5 0 0 1 12 0c-.005 3.22-1.778 5.235-3.43 7.5l3.557 4.527a1 1 0 0 1-.203 1.43l-1.894 1.36a1 1 0 0 1-1.384-.215L12 18l-2.679 3.593a1 1 0 0 1-1.39.213l-1.865-1.353a1 1 0 0 1-.203-1.422z\",\n    key: \"nmifey\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Ribbon"], [15, 14, 10, 12], [15, 17, 10, 12, "exports"], [15, 24, 10, 12], [15, 25, 10, 12, "default"], [15, 32, 10, 12], [15, 35, 10, 15], [15, 39, 10, 15, "createLucideIcon"], [15, 64, 10, 31], [15, 66, 10, 32], [15, 74, 10, 40], [15, 76, 10, 42], [15, 77, 11, 2], [15, 78, 12, 4], [15, 84, 12, 10], [15, 86, 13, 4], [16, 4, 13, 6, "d"], [16, 5, 13, 7], [16, 7, 13, 9], [16, 76, 13, 78], [17, 4, 13, 80, "key"], [17, 7, 13, 83], [17, 9, 13, 85], [18, 2, 13, 94], [18, 3, 13, 95], [18, 4, 14, 3], [18, 6, 15, 2], [18, 7, 15, 3], [18, 13, 15, 9], [18, 15, 15, 11], [19, 4, 15, 13, "d"], [19, 5, 15, 14], [19, 7, 15, 16], [19, 24, 15, 33], [20, 4, 15, 35, "key"], [20, 7, 15, 38], [20, 9, 15, 40], [21, 2, 15, 49], [21, 3, 15, 50], [21, 4, 15, 51], [21, 6, 16, 2], [21, 7, 16, 3], [21, 13, 16, 9], [21, 15, 16, 11], [22, 4, 16, 13, "d"], [22, 5, 16, 14], [22, 7, 16, 16], [22, 43, 16, 52], [23, 4, 16, 54, "key"], [23, 7, 16, 57], [23, 9, 16, 59], [24, 2, 16, 68], [24, 3, 16, 69], [24, 4, 16, 70], [24, 6, 17, 2], [24, 7, 17, 3], [24, 13, 17, 9], [24, 15, 17, 11], [25, 4, 17, 13, "d"], [25, 5, 17, 14], [25, 7, 17, 16], [25, 29, 17, 38], [26, 4, 17, 40, "key"], [26, 7, 17, 43], [26, 9, 17, 45], [27, 2, 17, 54], [27, 3, 17, 55], [27, 4, 17, 56], [27, 6, 18, 2], [27, 7, 19, 4], [27, 13, 19, 10], [27, 15, 20, 4], [28, 4, 21, 6, "d"], [28, 5, 21, 7], [28, 7, 21, 9], [28, 231, 21, 233], [29, 4, 22, 6, "key"], [29, 7, 22, 9], [29, 9, 22, 11], [30, 2, 23, 4], [30, 3, 23, 5], [30, 4, 24, 3], [30, 5, 25, 1], [30, 6, 25, 2], [31, 0, 25, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}