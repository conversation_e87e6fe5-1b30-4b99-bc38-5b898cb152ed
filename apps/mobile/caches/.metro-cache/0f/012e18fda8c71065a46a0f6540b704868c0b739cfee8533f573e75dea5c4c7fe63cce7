{"dependencies": [{"name": "react-native/Libraries/Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 177}, "end": {"line": 5, "column": 101, "index": 278}}], "key": "k6njkimG04wbVRRJfO1eozehR4g=", "exportNames": ["*"]}}, {"name": "./FormData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 280}, "end": {"line": 7, "column": 50, "index": 330}}], "key": "v9DanDXOiZnYdyyFv1kGRqeraMg=", "exportNames": ["*"]}}, {"name": "./TextDecoder", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 27, "column": 29, "index": 949}, "end": {"line": 27, "column": 53, "index": 973}}], "key": "ELeQkJ7iwbCdb4Zwtj3/5FG5hyo=", "exportNames": ["*"]}}, {"name": "./url", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 29, "column": 21, "index": 1045}, "end": {"line": 29, "column": 37, "index": 1061}}, {"start": {"line": 31, "column": 33, "index": 1149}, "end": {"line": 31, "column": 49, "index": 1165}}], "key": "uPuCk1oMCGEQGPes4SyVoRoPRJo=", "exportNames": ["*"]}}, {"name": "./ImportMetaRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 35, "column": 42, "index": 1317}, "end": {"line": 35, "column": 73, "index": 1348}}], "key": "E9M+TcyDJ9PkgtIqN+FAyloPpsM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _PolyfillFunctions = require(_dependencyMap[0], \"react-native/Libraries/Utilities/PolyfillFunctions\");\n  var _FormData = require(_dependencyMap[1], \"./FormData\");\n  // This file configures the runtime environment to increase compatibility with WinterCG.\n  // https://wintercg.org/\n\n  // @ts-ignore: PolyfillFunctions does not have types exported\n\n  // Add a well-known shared symbol that doesn't show up in iteration or inspection\n  // this can be used to detect if the global object abides by the Expo team's documented\n  // built-in requirements.\n  var BUILTIN_SYMBOL = Symbol.for('expo.builtin');\n  function addBuiltinSymbol(obj) {\n    Object.defineProperty(obj, BUILTIN_SYMBOL, {\n      value: true,\n      enumerable: false,\n      configurable: false\n    });\n    return obj;\n  }\n  function install(name, getValue) {\n    (0, _PolyfillFunctions.polyfillGlobal)(name, () => addBuiltinSymbol(getValue()));\n  }\n\n  // https://encoding.spec.whatwg.org/#textdecoder\n  install('TextDecoder', () => require(_dependencyMap[2], \"./TextDecoder\").TextDecoder);\n  // https://url.spec.whatwg.org/#url\n  install('URL', () => require(_dependencyMap[3], \"./url\").URL);\n  // https://url.spec.whatwg.org/#urlsearchparams\n  install('URLSearchParams', () => require(_dependencyMap[3], \"./url\").URLSearchParams);\n  // https://streams.spec.whatwg.org/#rs\n  // ReadableStream is injected by Metro as a global\n\n  install('__ExpoImportMetaRegistry', () => require(_dependencyMap[4], \"./ImportMetaRegistry\").ImportMetaRegistry);\n  (0, _FormData.installFormDataPatch)(FormData);\n\n  // Polyfill async iterator symbol for Hermes.\n  // @ts-expect-error: readonly property only applies when the engine supports it\n  Symbol.asyncIterator ??= Symbol.for('Symbol.asyncIterator');\n});", "lineCount": 40, "map": [[2, 2, 5, 0], [2, 6, 5, 0, "_PolyfillFunctions"], [2, 24, 5, 0], [2, 27, 5, 0, "require"], [2, 34, 5, 0], [2, 35, 5, 0, "_dependencyMap"], [2, 49, 5, 0], [3, 2, 7, 0], [3, 6, 7, 0, "_FormData"], [3, 15, 7, 0], [3, 18, 7, 0, "require"], [3, 25, 7, 0], [3, 26, 7, 0, "_dependencyMap"], [3, 40, 7, 0], [4, 2, 1, 0], [5, 2, 2, 0], [7, 2, 4, 0], [9, 2, 8, 0], [10, 2, 9, 0], [11, 2, 10, 0], [12, 2, 11, 0], [12, 6, 11, 6, "BUILTIN_SYMBOL"], [12, 20, 11, 20], [12, 23, 11, 23, "Symbol"], [12, 29, 11, 29], [12, 30, 11, 30, "for"], [12, 33, 11, 33], [12, 34, 11, 34], [12, 48, 11, 48], [12, 49, 11, 49], [13, 2, 13, 0], [13, 11, 13, 9, "addBuiltinSymbol"], [13, 27, 13, 25, "addBuiltinSymbol"], [13, 28, 13, 26, "obj"], [13, 31, 13, 37], [13, 33, 13, 39], [14, 4, 14, 2, "Object"], [14, 10, 14, 8], [14, 11, 14, 9, "defineProperty"], [14, 25, 14, 23], [14, 26, 14, 24, "obj"], [14, 29, 14, 27], [14, 31, 14, 29, "BUILTIN_SYMBOL"], [14, 45, 14, 43], [14, 47, 14, 45], [15, 6, 15, 4, "value"], [15, 11, 15, 9], [15, 13, 15, 11], [15, 17, 15, 15], [16, 6, 16, 4, "enumerable"], [16, 16, 16, 14], [16, 18, 16, 16], [16, 23, 16, 21], [17, 6, 17, 4, "configurable"], [17, 18, 17, 16], [17, 20, 17, 18], [18, 4, 18, 2], [18, 5, 18, 3], [18, 6, 18, 4], [19, 4, 19, 2], [19, 11, 19, 9, "obj"], [19, 14, 19, 12], [20, 2, 20, 0], [21, 2, 22, 0], [21, 11, 22, 9, "install"], [21, 18, 22, 16, "install"], [21, 19, 22, 17, "name"], [21, 23, 22, 29], [21, 25, 22, 31, "getValue"], [21, 33, 22, 50], [21, 35, 22, 52], [22, 4, 23, 2], [22, 8, 23, 2, "installGlobal"], [22, 41, 23, 15], [22, 43, 23, 16, "name"], [22, 47, 23, 20], [22, 49, 23, 22], [22, 55, 23, 28, "addBuiltinSymbol"], [22, 71, 23, 44], [22, 72, 23, 45, "getValue"], [22, 80, 23, 53], [22, 81, 23, 54], [22, 82, 23, 55], [22, 83, 23, 56], [22, 84, 23, 57], [23, 2, 24, 0], [25, 2, 26, 0], [26, 2, 27, 0, "install"], [26, 9, 27, 7], [26, 10, 27, 8], [26, 23, 27, 21], [26, 25, 27, 23], [26, 31, 27, 29, "require"], [26, 38, 27, 36], [26, 39, 27, 36, "_dependencyMap"], [26, 53, 27, 36], [26, 73, 27, 52], [26, 74, 27, 53], [26, 75, 27, 54, "TextDecoder"], [26, 86, 27, 65], [26, 87, 27, 66], [27, 2, 28, 0], [28, 2, 29, 0, "install"], [28, 9, 29, 7], [28, 10, 29, 8], [28, 15, 29, 13], [28, 17, 29, 15], [28, 23, 29, 21, "require"], [28, 30, 29, 28], [28, 31, 29, 28, "_dependencyMap"], [28, 45, 29, 28], [28, 57, 29, 36], [28, 58, 29, 37], [28, 59, 29, 38, "URL"], [28, 62, 29, 41], [28, 63, 29, 42], [29, 2, 30, 0], [30, 2, 31, 0, "install"], [30, 9, 31, 7], [30, 10, 31, 8], [30, 27, 31, 25], [30, 29, 31, 27], [30, 35, 31, 33, "require"], [30, 42, 31, 40], [30, 43, 31, 40, "_dependencyMap"], [30, 57, 31, 40], [30, 69, 31, 48], [30, 70, 31, 49], [30, 71, 31, 50, "URLSearchParams"], [30, 86, 31, 65], [30, 87, 31, 66], [31, 2, 32, 0], [32, 2, 33, 0], [34, 2, 35, 0, "install"], [34, 9, 35, 7], [34, 10, 35, 8], [34, 36, 35, 34], [34, 38, 35, 36], [34, 44, 35, 42, "require"], [34, 51, 35, 49], [34, 52, 35, 49, "_dependencyMap"], [34, 66, 35, 49], [34, 93, 35, 72], [34, 94, 35, 73], [34, 95, 35, 74, "ImportMetaRegistry"], [34, 113, 35, 92], [34, 114, 35, 93], [35, 2, 37, 0], [35, 6, 37, 0, "installFormDataPatch"], [35, 36, 37, 20], [35, 38, 37, 21, "FormData"], [35, 46, 37, 29], [35, 47, 37, 30], [37, 2, 39, 0], [38, 2, 40, 0], [39, 2, 41, 0, "Symbol"], [39, 8, 41, 6], [39, 9, 41, 7, "asyncIterator"], [39, 22, 41, 20], [39, 27, 41, 25, "Symbol"], [39, 33, 41, 31], [39, 34, 41, 32, "for"], [39, 37, 41, 35], [39, 38, 41, 36], [39, 60, 41, 58], [39, 61, 41, 59], [40, 0, 41, 60], [40, 3]], "functionMap": {"names": ["<global>", "addBuiltinSymbol", "install", "installGlobal$argument_1", "install$argument_1"], "mappings": "AAA;ACY;CDO;AEE;sBCC,kCD;CFC;uBIG,0CJ;eIE,0BJ;2BIE,sCJ;oCII,wDJ"}}, "type": "js/module"}]}