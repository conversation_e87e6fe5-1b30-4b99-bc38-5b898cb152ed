{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../../src/private/animated/NativeAnimatedHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 86}}], "key": "nPwQvxMCRdjC57J8sIprqhf4lHM=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/normalizeColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 61}}], "key": "7RfthyJNM7vnzMKnQG9aDSjEopk=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/PlatformColorValueTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 76}}], "key": "G5v7LsH/vb1su+NUwequgArLna0=", "exportNames": ["*"]}}, {"name": "./AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 58}}], "key": "MXjn1CQaLNtMiiooxlb5qObVfR0=", "exportNames": ["*"]}}, {"name": "./AnimatedWithChildren", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 58}}], "key": "IUkIH5MYbr+OqFsp9MMa/cV/D0g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _NativeAnimatedHelper = _interopRequireDefault(require(_dependencyMap[7], \"../../../src/private/animated/NativeAnimatedHelper\"));\n  var _normalizeColor = _interopRequireDefault(require(_dependencyMap[8], \"../../StyleSheet/normalizeColor\"));\n  var _PlatformColorValueTypes = require(_dependencyMap[9], \"../../StyleSheet/PlatformColorValueTypes\");\n  var _AnimatedValue = _interopRequireWildcard(require(_dependencyMap[10], \"./AnimatedValue\"));\n  var _AnimatedWithChildren2 = _interopRequireDefault(require(_dependencyMap[11], \"./AnimatedWithChildren\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;\n  var defaultColor = {\n    r: 0,\n    g: 0,\n    b: 0,\n    a: 1.0\n  };\n  function processColor(color) {\n    if (color === undefined || color === null) {\n      return null;\n    }\n    if (isRgbaValue(color)) {\n      return color;\n    }\n    var normalizedColor = (0, _normalizeColor.default)(color);\n    if (normalizedColor === undefined || normalizedColor === null) {\n      return null;\n    }\n    if (typeof normalizedColor === 'object') {\n      var processedColorObj = (0, _PlatformColorValueTypes.processColorObject)(normalizedColor);\n      if (processedColorObj != null) {\n        return processedColorObj;\n      }\n    } else if (typeof normalizedColor === 'number') {\n      var r = (normalizedColor & 0xff000000) >>> 24;\n      var g = (normalizedColor & 0x00ff0000) >>> 16;\n      var b = (normalizedColor & 0x0000ff00) >>> 8;\n      var a = (normalizedColor & 0x000000ff) / 255;\n      return {\n        r,\n        g,\n        b,\n        a\n      };\n    }\n    return null;\n  }\n  function isRgbaValue(value) {\n    return value && typeof value.r === 'number' && typeof value.g === 'number' && typeof value.b === 'number' && typeof value.a === 'number';\n  }\n  function isRgbaAnimatedValue(value) {\n    return value && value.r instanceof _AnimatedValue.default && value.g instanceof _AnimatedValue.default && value.b instanceof _AnimatedValue.default && value.a instanceof _AnimatedValue.default;\n  }\n  var AnimatedColor = exports.default = /*#__PURE__*/function (_AnimatedWithChildren) {\n    function AnimatedColor(valueIn, config) {\n      var _this;\n      (0, _classCallCheck2.default)(this, AnimatedColor);\n      _this = _callSuper(this, AnimatedColor, [config]);\n      _this._suspendCallbacks = 0;\n      var value = valueIn ?? defaultColor;\n      if (isRgbaAnimatedValue(value)) {\n        var rgbaAnimatedValue = value;\n        _this.r = rgbaAnimatedValue.r;\n        _this.g = rgbaAnimatedValue.g;\n        _this.b = rgbaAnimatedValue.b;\n        _this.a = rgbaAnimatedValue.a;\n      } else {\n        var processedColor = processColor(value) ?? defaultColor;\n        var initColor = defaultColor;\n        if (isRgbaValue(processedColor)) {\n          initColor = processedColor;\n        } else {\n          _this.nativeColor = processedColor;\n        }\n        _this.r = new _AnimatedValue.default(initColor.r);\n        _this.g = new _AnimatedValue.default(initColor.g);\n        _this.b = new _AnimatedValue.default(initColor.b);\n        _this.a = new _AnimatedValue.default(initColor.a);\n      }\n      if (config?.useNativeDriver) {\n        _this.__makeNative();\n      }\n      return _this;\n    }\n    (0, _inherits2.default)(AnimatedColor, _AnimatedWithChildren);\n    return (0, _createClass2.default)(AnimatedColor, [{\n      key: \"setValue\",\n      value: function setValue(value) {\n        var shouldUpdateNodeConfig = false;\n        if (this.__isNative) {\n          var nativeTag = this.__getNativeTag();\n          NativeAnimatedAPI.setWaitingForIdentifier(nativeTag.toString());\n        }\n        var processedColor = processColor(value) ?? defaultColor;\n        this._withSuspendedCallbacks(() => {\n          if (isRgbaValue(processedColor)) {\n            var rgbaValue = processedColor;\n            this.r.setValue(rgbaValue.r);\n            this.g.setValue(rgbaValue.g);\n            this.b.setValue(rgbaValue.b);\n            this.a.setValue(rgbaValue.a);\n            if (this.nativeColor != null) {\n              this.nativeColor = null;\n              shouldUpdateNodeConfig = true;\n            }\n          } else {\n            var nativeColor = processedColor;\n            if (this.nativeColor !== nativeColor) {\n              this.nativeColor = nativeColor;\n              shouldUpdateNodeConfig = true;\n            }\n          }\n        });\n        if (this.__isNative) {\n          var _nativeTag = this.__getNativeTag();\n          if (shouldUpdateNodeConfig) {\n            NativeAnimatedAPI.updateAnimatedNodeConfig(_nativeTag, this.__getNativeConfig());\n          }\n          NativeAnimatedAPI.unsetWaitingForIdentifier(_nativeTag.toString());\n        } else {\n          (0, _AnimatedValue.flushValue)(this);\n        }\n        this.__callListeners(this.__getValue());\n      }\n    }, {\n      key: \"setOffset\",\n      value: function setOffset(offset) {\n        this.r.setOffset(offset.r);\n        this.g.setOffset(offset.g);\n        this.b.setOffset(offset.b);\n        this.a.setOffset(offset.a);\n      }\n    }, {\n      key: \"flattenOffset\",\n      value: function flattenOffset() {\n        this.r.flattenOffset();\n        this.g.flattenOffset();\n        this.b.flattenOffset();\n        this.a.flattenOffset();\n      }\n    }, {\n      key: \"extractOffset\",\n      value: function extractOffset() {\n        this.r.extractOffset();\n        this.g.extractOffset();\n        this.b.extractOffset();\n        this.a.extractOffset();\n      }\n    }, {\n      key: \"stopAnimation\",\n      value: function stopAnimation(callback) {\n        this.r.stopAnimation();\n        this.g.stopAnimation();\n        this.b.stopAnimation();\n        this.a.stopAnimation();\n        callback && callback(this.__getValue());\n      }\n    }, {\n      key: \"resetAnimation\",\n      value: function resetAnimation(callback) {\n        this.r.resetAnimation();\n        this.g.resetAnimation();\n        this.b.resetAnimation();\n        this.a.resetAnimation();\n        callback && callback(this.__getValue());\n      }\n    }, {\n      key: \"__getValue\",\n      value: function __getValue() {\n        if (this.nativeColor != null) {\n          return this.nativeColor;\n        } else {\n          return `rgba(${this.r.__getValue()}, ${this.g.__getValue()}, ${this.b.__getValue()}, ${this.a.__getValue()})`;\n        }\n      }\n    }, {\n      key: \"__attach\",\n      value: function __attach() {\n        this.r.__addChild(this);\n        this.g.__addChild(this);\n        this.b.__addChild(this);\n        this.a.__addChild(this);\n        _superPropGet(AnimatedColor, \"__attach\", this, 3)([]);\n      }\n    }, {\n      key: \"__detach\",\n      value: function __detach() {\n        this.r.__removeChild(this);\n        this.g.__removeChild(this);\n        this.b.__removeChild(this);\n        this.a.__removeChild(this);\n        _superPropGet(AnimatedColor, \"__detach\", this, 3)([]);\n      }\n    }, {\n      key: \"_withSuspendedCallbacks\",\n      value: function _withSuspendedCallbacks(callback) {\n        this._suspendCallbacks++;\n        callback();\n        this._suspendCallbacks--;\n      }\n    }, {\n      key: \"__callListeners\",\n      value: function __callListeners(value) {\n        if (this._suspendCallbacks === 0) {\n          _superPropGet(AnimatedColor, \"__callListeners\", this, 3)([value]);\n        }\n      }\n    }, {\n      key: \"__makeNative\",\n      value: function __makeNative(platformConfig) {\n        this.r.__makeNative(platformConfig);\n        this.g.__makeNative(platformConfig);\n        this.b.__makeNative(platformConfig);\n        this.a.__makeNative(platformConfig);\n        _superPropGet(AnimatedColor, \"__makeNative\", this, 3)([platformConfig]);\n      }\n    }, {\n      key: \"__getNativeConfig\",\n      value: function __getNativeConfig() {\n        return {\n          type: 'color',\n          r: this.r.__getNativeTag(),\n          g: this.g.__getNativeTag(),\n          b: this.b.__getNativeTag(),\n          a: this.a.__getNativeTag(),\n          nativeColor: this.nativeColor,\n          debugID: this.__getDebugID()\n        };\n      }\n    }]);\n  }(_AnimatedWithChildren2.default);\n});", "lineCount": 245, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 11, 13], [11, 6, 11, 13, "_possibleConstructorReturn2"], [11, 33, 11, 13], [11, 36, 11, 13, "_interopRequireDefault"], [11, 58, 11, 13], [11, 59, 11, 13, "require"], [11, 66, 11, 13], [11, 67, 11, 13, "_dependencyMap"], [11, 81, 11, 13], [12, 2, 11, 13], [12, 6, 11, 13, "_getPrototypeOf2"], [12, 22, 11, 13], [12, 25, 11, 13, "_interopRequireDefault"], [12, 47, 11, 13], [12, 48, 11, 13, "require"], [12, 55, 11, 13], [12, 56, 11, 13, "_dependencyMap"], [12, 70, 11, 13], [13, 2, 11, 13], [13, 6, 11, 13, "_get2"], [13, 11, 11, 13], [13, 14, 11, 13, "_interopRequireDefault"], [13, 36, 11, 13], [13, 37, 11, 13, "require"], [13, 44, 11, 13], [13, 45, 11, 13, "_dependencyMap"], [13, 59, 11, 13], [14, 2, 11, 13], [14, 6, 11, 13, "_inherits2"], [14, 16, 11, 13], [14, 19, 11, 13, "_interopRequireDefault"], [14, 41, 11, 13], [14, 42, 11, 13, "require"], [14, 49, 11, 13], [14, 50, 11, 13, "_dependencyMap"], [14, 64, 11, 13], [15, 2, 19, 0], [15, 6, 19, 0, "_NativeAnimatedHelper"], [15, 27, 19, 0], [15, 30, 19, 0, "_interopRequireDefault"], [15, 52, 19, 0], [15, 53, 19, 0, "require"], [15, 60, 19, 0], [15, 61, 19, 0, "_dependencyMap"], [15, 75, 19, 0], [16, 2, 20, 0], [16, 6, 20, 0, "_normalizeColor"], [16, 21, 20, 0], [16, 24, 20, 0, "_interopRequireDefault"], [16, 46, 20, 0], [16, 47, 20, 0, "require"], [16, 54, 20, 0], [16, 55, 20, 0, "_dependencyMap"], [16, 69, 20, 0], [17, 2, 21, 0], [17, 6, 21, 0, "_PlatformColorValueTypes"], [17, 30, 21, 0], [17, 33, 21, 0, "require"], [17, 40, 21, 0], [17, 41, 21, 0, "_dependencyMap"], [17, 55, 21, 0], [18, 2, 22, 0], [18, 6, 22, 0, "_AnimatedValue"], [18, 20, 22, 0], [18, 23, 22, 0, "_interopRequireWildcard"], [18, 46, 22, 0], [18, 47, 22, 0, "require"], [18, 54, 22, 0], [18, 55, 22, 0, "_dependencyMap"], [18, 69, 22, 0], [19, 2, 23, 0], [19, 6, 23, 0, "_AnimatedWithChildren2"], [19, 28, 23, 0], [19, 31, 23, 0, "_interopRequireDefault"], [19, 53, 23, 0], [19, 54, 23, 0, "require"], [19, 61, 23, 0], [19, 62, 23, 0, "_dependencyMap"], [19, 76, 23, 0], [20, 2, 23, 58], [20, 11, 23, 58, "_interopRequireWildcard"], [20, 35, 23, 58, "e"], [20, 36, 23, 58], [20, 38, 23, 58, "t"], [20, 39, 23, 58], [20, 68, 23, 58, "WeakMap"], [20, 75, 23, 58], [20, 81, 23, 58, "r"], [20, 82, 23, 58], [20, 89, 23, 58, "WeakMap"], [20, 96, 23, 58], [20, 100, 23, 58, "n"], [20, 101, 23, 58], [20, 108, 23, 58, "WeakMap"], [20, 115, 23, 58], [20, 127, 23, 58, "_interopRequireWildcard"], [20, 150, 23, 58], [20, 162, 23, 58, "_interopRequireWildcard"], [20, 163, 23, 58, "e"], [20, 164, 23, 58], [20, 166, 23, 58, "t"], [20, 167, 23, 58], [20, 176, 23, 58, "t"], [20, 177, 23, 58], [20, 181, 23, 58, "e"], [20, 182, 23, 58], [20, 186, 23, 58, "e"], [20, 187, 23, 58], [20, 188, 23, 58, "__esModule"], [20, 198, 23, 58], [20, 207, 23, 58, "e"], [20, 208, 23, 58], [20, 214, 23, 58, "o"], [20, 215, 23, 58], [20, 217, 23, 58, "i"], [20, 218, 23, 58], [20, 220, 23, 58, "f"], [20, 221, 23, 58], [20, 226, 23, 58, "__proto__"], [20, 235, 23, 58], [20, 243, 23, 58, "default"], [20, 250, 23, 58], [20, 252, 23, 58, "e"], [20, 253, 23, 58], [20, 270, 23, 58, "e"], [20, 271, 23, 58], [20, 294, 23, 58, "e"], [20, 295, 23, 58], [20, 320, 23, 58, "e"], [20, 321, 23, 58], [20, 330, 23, 58, "f"], [20, 331, 23, 58], [20, 337, 23, 58, "o"], [20, 338, 23, 58], [20, 341, 23, 58, "t"], [20, 342, 23, 58], [20, 345, 23, 58, "n"], [20, 346, 23, 58], [20, 349, 23, 58, "r"], [20, 350, 23, 58], [20, 358, 23, 58, "o"], [20, 359, 23, 58], [20, 360, 23, 58, "has"], [20, 363, 23, 58], [20, 364, 23, 58, "e"], [20, 365, 23, 58], [20, 375, 23, 58, "o"], [20, 376, 23, 58], [20, 377, 23, 58, "get"], [20, 380, 23, 58], [20, 381, 23, 58, "e"], [20, 382, 23, 58], [20, 385, 23, 58, "o"], [20, 386, 23, 58], [20, 387, 23, 58, "set"], [20, 390, 23, 58], [20, 391, 23, 58, "e"], [20, 392, 23, 58], [20, 394, 23, 58, "f"], [20, 395, 23, 58], [20, 409, 23, 58, "_t"], [20, 411, 23, 58], [20, 415, 23, 58, "e"], [20, 416, 23, 58], [20, 432, 23, 58, "_t"], [20, 434, 23, 58], [20, 441, 23, 58, "hasOwnProperty"], [20, 455, 23, 58], [20, 456, 23, 58, "call"], [20, 460, 23, 58], [20, 461, 23, 58, "e"], [20, 462, 23, 58], [20, 464, 23, 58, "_t"], [20, 466, 23, 58], [20, 473, 23, 58, "i"], [20, 474, 23, 58], [20, 478, 23, 58, "o"], [20, 479, 23, 58], [20, 482, 23, 58, "Object"], [20, 488, 23, 58], [20, 489, 23, 58, "defineProperty"], [20, 503, 23, 58], [20, 508, 23, 58, "Object"], [20, 514, 23, 58], [20, 515, 23, 58, "getOwnPropertyDescriptor"], [20, 539, 23, 58], [20, 540, 23, 58, "e"], [20, 541, 23, 58], [20, 543, 23, 58, "_t"], [20, 545, 23, 58], [20, 552, 23, 58, "i"], [20, 553, 23, 58], [20, 554, 23, 58, "get"], [20, 557, 23, 58], [20, 561, 23, 58, "i"], [20, 562, 23, 58], [20, 563, 23, 58, "set"], [20, 566, 23, 58], [20, 570, 23, 58, "o"], [20, 571, 23, 58], [20, 572, 23, 58, "f"], [20, 573, 23, 58], [20, 575, 23, 58, "_t"], [20, 577, 23, 58], [20, 579, 23, 58, "i"], [20, 580, 23, 58], [20, 584, 23, 58, "f"], [20, 585, 23, 58], [20, 586, 23, 58, "_t"], [20, 588, 23, 58], [20, 592, 23, 58, "e"], [20, 593, 23, 58], [20, 594, 23, 58, "_t"], [20, 596, 23, 58], [20, 607, 23, 58, "f"], [20, 608, 23, 58], [20, 613, 23, 58, "e"], [20, 614, 23, 58], [20, 616, 23, 58, "t"], [20, 617, 23, 58], [21, 2, 23, 58], [21, 11, 23, 58, "_callSuper"], [21, 22, 23, 58, "t"], [21, 23, 23, 58], [21, 25, 23, 58, "o"], [21, 26, 23, 58], [21, 28, 23, 58, "e"], [21, 29, 23, 58], [21, 40, 23, 58, "o"], [21, 41, 23, 58], [21, 48, 23, 58, "_getPrototypeOf2"], [21, 64, 23, 58], [21, 65, 23, 58, "default"], [21, 72, 23, 58], [21, 74, 23, 58, "o"], [21, 75, 23, 58], [21, 82, 23, 58, "_possibleConstructorReturn2"], [21, 109, 23, 58], [21, 110, 23, 58, "default"], [21, 117, 23, 58], [21, 119, 23, 58, "t"], [21, 120, 23, 58], [21, 122, 23, 58, "_isNativeReflectConstruct"], [21, 147, 23, 58], [21, 152, 23, 58, "Reflect"], [21, 159, 23, 58], [21, 160, 23, 58, "construct"], [21, 169, 23, 58], [21, 170, 23, 58, "o"], [21, 171, 23, 58], [21, 173, 23, 58, "e"], [21, 174, 23, 58], [21, 186, 23, 58, "_getPrototypeOf2"], [21, 202, 23, 58], [21, 203, 23, 58, "default"], [21, 210, 23, 58], [21, 212, 23, 58, "t"], [21, 213, 23, 58], [21, 215, 23, 58, "constructor"], [21, 226, 23, 58], [21, 230, 23, 58, "o"], [21, 231, 23, 58], [21, 232, 23, 58, "apply"], [21, 237, 23, 58], [21, 238, 23, 58, "t"], [21, 239, 23, 58], [21, 241, 23, 58, "e"], [21, 242, 23, 58], [22, 2, 23, 58], [22, 11, 23, 58, "_isNativeReflectConstruct"], [22, 37, 23, 58], [22, 51, 23, 58, "t"], [22, 52, 23, 58], [22, 56, 23, 58, "Boolean"], [22, 63, 23, 58], [22, 64, 23, 58, "prototype"], [22, 73, 23, 58], [22, 74, 23, 58, "valueOf"], [22, 81, 23, 58], [22, 82, 23, 58, "call"], [22, 86, 23, 58], [22, 87, 23, 58, "Reflect"], [22, 94, 23, 58], [22, 95, 23, 58, "construct"], [22, 104, 23, 58], [22, 105, 23, 58, "Boolean"], [22, 112, 23, 58], [22, 145, 23, 58, "t"], [22, 146, 23, 58], [22, 159, 23, 58, "_isNativeReflectConstruct"], [22, 184, 23, 58], [22, 196, 23, 58, "_isNativeReflectConstruct"], [22, 197, 23, 58], [22, 210, 23, 58, "t"], [22, 211, 23, 58], [23, 2, 23, 58], [23, 11, 23, 58, "_superPropGet"], [23, 25, 23, 58, "t"], [23, 26, 23, 58], [23, 28, 23, 58, "o"], [23, 29, 23, 58], [23, 31, 23, 58, "e"], [23, 32, 23, 58], [23, 34, 23, 58, "r"], [23, 35, 23, 58], [23, 43, 23, 58, "p"], [23, 44, 23, 58], [23, 51, 23, 58, "_get2"], [23, 56, 23, 58], [23, 57, 23, 58, "default"], [23, 64, 23, 58], [23, 70, 23, 58, "_getPrototypeOf2"], [23, 86, 23, 58], [23, 87, 23, 58, "default"], [23, 94, 23, 58], [23, 100, 23, 58, "r"], [23, 101, 23, 58], [23, 104, 23, 58, "t"], [23, 105, 23, 58], [23, 106, 23, 58, "prototype"], [23, 115, 23, 58], [23, 118, 23, 58, "t"], [23, 119, 23, 58], [23, 122, 23, 58, "o"], [23, 123, 23, 58], [23, 125, 23, 58, "e"], [23, 126, 23, 58], [23, 140, 23, 58, "r"], [23, 141, 23, 58], [23, 166, 23, 58, "p"], [23, 167, 23, 58], [23, 180, 23, 58, "t"], [23, 181, 23, 58], [23, 192, 23, 58, "p"], [23, 193, 23, 58], [23, 194, 23, 58, "apply"], [23, 199, 23, 58], [23, 200, 23, 58, "e"], [23, 201, 23, 58], [23, 203, 23, 58, "t"], [23, 204, 23, 58], [23, 211, 23, 58, "p"], [23, 212, 23, 58], [24, 2, 50, 0], [24, 6, 50, 6, "NativeAnimatedAPI"], [24, 23, 50, 23], [24, 26, 50, 26, "NativeAnimatedHelper"], [24, 55, 50, 46], [24, 56, 50, 47, "API"], [24, 59, 50, 50], [25, 2, 52, 0], [25, 6, 52, 6, "defaultColor"], [25, 18, 52, 29], [25, 21, 52, 32], [26, 4, 52, 33, "r"], [26, 5, 52, 34], [26, 7, 52, 36], [26, 8, 52, 37], [27, 4, 52, 39, "g"], [27, 5, 52, 40], [27, 7, 52, 42], [27, 8, 52, 43], [28, 4, 52, 45, "b"], [28, 5, 52, 46], [28, 7, 52, 48], [28, 8, 52, 49], [29, 4, 52, 51, "a"], [29, 5, 52, 52], [29, 7, 52, 54], [30, 2, 52, 57], [30, 3, 52, 58], [31, 2, 55, 0], [31, 11, 55, 9, "processColor"], [31, 23, 55, 21, "processColor"], [31, 24, 56, 2, "color"], [31, 29, 56, 35], [31, 31, 57, 35], [32, 4, 58, 2], [32, 8, 58, 6, "color"], [32, 13, 58, 11], [32, 18, 58, 16, "undefined"], [32, 27, 58, 25], [32, 31, 58, 29, "color"], [32, 36, 58, 34], [32, 41, 58, 39], [32, 45, 58, 43], [32, 47, 58, 45], [33, 6, 59, 4], [33, 13, 59, 11], [33, 17, 59, 15], [34, 4, 60, 2], [35, 4, 62, 2], [35, 8, 62, 6, "isRgbaValue"], [35, 19, 62, 17], [35, 20, 62, 18, "color"], [35, 25, 62, 23], [35, 26, 62, 24], [35, 28, 62, 26], [36, 6, 64, 4], [36, 13, 64, 12, "color"], [36, 18, 64, 17], [37, 4, 65, 2], [38, 4, 67, 2], [38, 8, 67, 6, "normalizedColor"], [38, 23, 67, 43], [38, 26, 67, 46], [38, 30, 67, 46, "normalizeColor"], [38, 53, 67, 60], [38, 55, 69, 5, "color"], [38, 60, 70, 2], [38, 61, 70, 3], [39, 4, 71, 2], [39, 8, 71, 6, "normalizedColor"], [39, 23, 71, 21], [39, 28, 71, 26, "undefined"], [39, 37, 71, 35], [39, 41, 71, 39, "normalizedColor"], [39, 56, 71, 54], [39, 61, 71, 59], [39, 65, 71, 63], [39, 67, 71, 65], [40, 6, 72, 4], [40, 13, 72, 11], [40, 17, 72, 15], [41, 4, 73, 2], [42, 4, 75, 2], [42, 8, 75, 6], [42, 15, 75, 13, "normalizedColor"], [42, 30, 75, 28], [42, 35, 75, 33], [42, 43, 75, 41], [42, 45, 75, 43], [43, 6, 76, 4], [43, 10, 76, 10, "processedColorObj"], [43, 27, 76, 46], [43, 30, 77, 6], [43, 34, 77, 6, "processColorObject"], [43, 77, 77, 24], [43, 79, 77, 25, "normalizedColor"], [43, 94, 77, 40], [43, 95, 77, 41], [44, 6, 78, 4], [44, 10, 78, 8, "processedColorObj"], [44, 27, 78, 25], [44, 31, 78, 29], [44, 35, 78, 33], [44, 37, 78, 35], [45, 8, 79, 6], [45, 15, 79, 13, "processedColorObj"], [45, 32, 79, 30], [46, 6, 80, 4], [47, 4, 81, 2], [47, 5, 81, 3], [47, 11, 81, 9], [47, 15, 81, 13], [47, 22, 81, 20, "normalizedColor"], [47, 37, 81, 35], [47, 42, 81, 40], [47, 50, 81, 48], [47, 52, 81, 50], [48, 6, 82, 4], [48, 10, 82, 10, "r"], [48, 11, 82, 19], [48, 14, 82, 22], [48, 15, 82, 23, "normalizedColor"], [48, 30, 82, 38], [48, 33, 82, 41], [48, 43, 82, 51], [48, 49, 82, 57], [48, 51, 82, 59], [49, 6, 83, 4], [49, 10, 83, 10, "g"], [49, 11, 83, 19], [49, 14, 83, 22], [49, 15, 83, 23, "normalizedColor"], [49, 30, 83, 38], [49, 33, 83, 41], [49, 43, 83, 51], [49, 49, 83, 57], [49, 51, 83, 59], [50, 6, 84, 4], [50, 10, 84, 10, "b"], [50, 11, 84, 19], [50, 14, 84, 22], [50, 15, 84, 23, "normalizedColor"], [50, 30, 84, 38], [50, 33, 84, 41], [50, 43, 84, 51], [50, 49, 84, 57], [50, 50, 84, 58], [51, 6, 85, 4], [51, 10, 85, 10, "a"], [51, 11, 85, 19], [51, 14, 85, 22], [51, 15, 85, 23, "normalizedColor"], [51, 30, 85, 38], [51, 33, 85, 41], [51, 43, 85, 51], [51, 47, 85, 55], [51, 50, 85, 58], [52, 6, 87, 4], [52, 13, 87, 11], [53, 8, 87, 12, "r"], [53, 9, 87, 13], [54, 8, 87, 15, "g"], [54, 9, 87, 16], [55, 8, 87, 18, "b"], [55, 9, 87, 19], [56, 8, 87, 21, "a"], [57, 6, 87, 22], [57, 7, 87, 23], [58, 4, 88, 2], [59, 4, 90, 2], [59, 11, 90, 9], [59, 15, 90, 13], [60, 2, 91, 0], [61, 2, 93, 0], [61, 11, 93, 9, "isRgbaValue"], [61, 22, 93, 20, "isRgbaValue"], [61, 23, 93, 21, "value"], [61, 28, 93, 31], [61, 30, 93, 42], [62, 4, 94, 2], [62, 11, 95, 4, "value"], [62, 16, 95, 9], [62, 20, 96, 4], [62, 27, 96, 11, "value"], [62, 32, 96, 16], [62, 33, 96, 17, "r"], [62, 34, 96, 18], [62, 39, 96, 23], [62, 47, 96, 31], [62, 51, 97, 4], [62, 58, 97, 11, "value"], [62, 63, 97, 16], [62, 64, 97, 17, "g"], [62, 65, 97, 18], [62, 70, 97, 23], [62, 78, 97, 31], [62, 82, 98, 4], [62, 89, 98, 11, "value"], [62, 94, 98, 16], [62, 95, 98, 17, "b"], [62, 96, 98, 18], [62, 101, 98, 23], [62, 109, 98, 31], [62, 113, 99, 4], [62, 120, 99, 11, "value"], [62, 125, 99, 16], [62, 126, 99, 17, "a"], [62, 127, 99, 18], [62, 132, 99, 23], [62, 140, 99, 31], [63, 2, 101, 0], [64, 2, 103, 0], [64, 11, 103, 9, "isRgbaAnimatedValue"], [64, 30, 103, 28, "isRgbaAnimatedValue"], [64, 31, 103, 29, "value"], [64, 36, 103, 39], [64, 38, 103, 50], [65, 4, 104, 2], [65, 11, 105, 4, "value"], [65, 16, 105, 9], [65, 20, 106, 4, "value"], [65, 25, 106, 9], [65, 26, 106, 10, "r"], [65, 27, 106, 11], [65, 39, 106, 23, "AnimatedValue"], [65, 61, 106, 36], [65, 65, 107, 4, "value"], [65, 70, 107, 9], [65, 71, 107, 10, "g"], [65, 72, 107, 11], [65, 84, 107, 23, "AnimatedValue"], [65, 106, 107, 36], [65, 110, 108, 4, "value"], [65, 115, 108, 9], [65, 116, 108, 10, "b"], [65, 117, 108, 11], [65, 129, 108, 23, "AnimatedValue"], [65, 151, 108, 36], [65, 155, 109, 4, "value"], [65, 160, 109, 9], [65, 161, 109, 10, "a"], [65, 162, 109, 11], [65, 174, 109, 23, "AnimatedValue"], [65, 196, 109, 36], [66, 2, 111, 0], [67, 2, 111, 1], [67, 6, 113, 21, "AnimatedColor"], [67, 19, 113, 34], [67, 22, 113, 34, "exports"], [67, 29, 113, 34], [67, 30, 113, 34, "default"], [67, 37, 113, 34], [67, 63, 113, 34, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [67, 84, 113, 34], [68, 4, 122, 2], [68, 13, 122, 2, "AnimatedColor"], [68, 27, 122, 14, "valueIn"], [68, 34, 122, 34], [68, 36, 122, 36, "config"], [68, 42, 122, 65], [68, 44, 122, 67], [69, 6, 122, 67], [69, 10, 122, 67, "_this"], [69, 15, 122, 67], [70, 6, 122, 67], [70, 10, 122, 67, "_classCallCheck2"], [70, 26, 122, 67], [70, 27, 122, 67, "default"], [70, 34, 122, 67], [70, 42, 122, 67, "AnimatedColor"], [70, 55, 122, 67], [71, 6, 123, 4, "_this"], [71, 11, 123, 4], [71, 14, 123, 4, "_callSuper"], [71, 24, 123, 4], [71, 31, 123, 4, "AnimatedColor"], [71, 44, 123, 4], [71, 47, 123, 10, "config"], [71, 53, 123, 16], [72, 6, 123, 18, "_this"], [72, 11, 123, 18], [72, 12, 120, 2, "_suspendCallbacks"], [72, 29, 120, 19], [72, 32, 120, 30], [72, 33, 120, 31], [73, 6, 125, 4], [73, 10, 125, 8, "value"], [73, 15, 125, 57], [73, 18, 126, 6, "valueIn"], [73, 25, 126, 13], [73, 29, 126, 17, "defaultColor"], [73, 41, 126, 29], [74, 6, 127, 4], [74, 10, 127, 8, "isRgbaAnimatedValue"], [74, 29, 127, 27], [74, 30, 127, 28, "value"], [74, 35, 127, 33], [74, 36, 127, 34], [74, 38, 127, 36], [75, 8, 129, 6], [75, 12, 129, 12, "rgbaAnimatedValue"], [75, 29, 129, 48], [75, 32, 129, 52, "value"], [75, 37, 129, 77], [76, 8, 130, 6, "_this"], [76, 13, 130, 6], [76, 14, 130, 11, "r"], [76, 15, 130, 12], [76, 18, 130, 15, "rgbaAnimatedValue"], [76, 35, 130, 32], [76, 36, 130, 33, "r"], [76, 37, 130, 34], [77, 8, 131, 6, "_this"], [77, 13, 131, 6], [77, 14, 131, 11, "g"], [77, 15, 131, 12], [77, 18, 131, 15, "rgbaAnimatedValue"], [77, 35, 131, 32], [77, 36, 131, 33, "g"], [77, 37, 131, 34], [78, 8, 132, 6, "_this"], [78, 13, 132, 6], [78, 14, 132, 11, "b"], [78, 15, 132, 12], [78, 18, 132, 15, "rgbaAnimatedValue"], [78, 35, 132, 32], [78, 36, 132, 33, "b"], [78, 37, 132, 34], [79, 8, 133, 6, "_this"], [79, 13, 133, 6], [79, 14, 133, 11, "a"], [79, 15, 133, 12], [79, 18, 133, 15, "rgbaAnimatedValue"], [79, 35, 133, 32], [79, 36, 133, 33, "a"], [79, 37, 133, 34], [80, 6, 134, 4], [80, 7, 134, 5], [80, 13, 134, 11], [81, 8, 135, 6], [81, 12, 135, 12, "processedColor"], [81, 26, 135, 56], [81, 29, 137, 8, "processColor"], [81, 41, 137, 20], [81, 42, 137, 22, "value"], [81, 47, 137, 52], [81, 48, 137, 53], [81, 52, 137, 57, "defaultColor"], [81, 64, 137, 69], [82, 8, 138, 6], [82, 12, 138, 10, "initColor"], [82, 21, 138, 30], [82, 24, 138, 33, "defaultColor"], [82, 36, 138, 45], [83, 8, 139, 6], [83, 12, 139, 10, "isRgbaValue"], [83, 23, 139, 21], [83, 24, 139, 22, "processedColor"], [83, 38, 139, 36], [83, 39, 139, 37], [83, 41, 139, 39], [84, 10, 141, 8, "initColor"], [84, 19, 141, 17], [84, 22, 141, 21, "processedColor"], [84, 36, 141, 47], [85, 8, 142, 6], [85, 9, 142, 7], [85, 15, 142, 13], [86, 10, 144, 8, "_this"], [86, 15, 144, 8], [86, 16, 144, 13, "nativeColor"], [86, 27, 144, 24], [86, 30, 144, 28, "processedColor"], [86, 44, 144, 61], [87, 8, 145, 6], [88, 8, 147, 6, "_this"], [88, 13, 147, 6], [88, 14, 147, 11, "r"], [88, 15, 147, 12], [88, 18, 147, 15], [88, 22, 147, 19, "AnimatedValue"], [88, 44, 147, 32], [88, 45, 147, 33, "initColor"], [88, 54, 147, 42], [88, 55, 147, 43, "r"], [88, 56, 147, 44], [88, 57, 147, 45], [89, 8, 148, 6, "_this"], [89, 13, 148, 6], [89, 14, 148, 11, "g"], [89, 15, 148, 12], [89, 18, 148, 15], [89, 22, 148, 19, "AnimatedValue"], [89, 44, 148, 32], [89, 45, 148, 33, "initColor"], [89, 54, 148, 42], [89, 55, 148, 43, "g"], [89, 56, 148, 44], [89, 57, 148, 45], [90, 8, 149, 6, "_this"], [90, 13, 149, 6], [90, 14, 149, 11, "b"], [90, 15, 149, 12], [90, 18, 149, 15], [90, 22, 149, 19, "AnimatedValue"], [90, 44, 149, 32], [90, 45, 149, 33, "initColor"], [90, 54, 149, 42], [90, 55, 149, 43, "b"], [90, 56, 149, 44], [90, 57, 149, 45], [91, 8, 150, 6, "_this"], [91, 13, 150, 6], [91, 14, 150, 11, "a"], [91, 15, 150, 12], [91, 18, 150, 15], [91, 22, 150, 19, "AnimatedValue"], [91, 44, 150, 32], [91, 45, 150, 33, "initColor"], [91, 54, 150, 42], [91, 55, 150, 43, "a"], [91, 56, 150, 44], [91, 57, 150, 45], [92, 6, 151, 4], [93, 6, 153, 4], [93, 10, 153, 8, "config"], [93, 16, 153, 14], [93, 18, 153, 16, "useNativeDriver"], [93, 33, 153, 31], [93, 35, 153, 33], [94, 8, 154, 6, "_this"], [94, 13, 154, 6], [94, 14, 154, 11, "__makeNative"], [94, 26, 154, 23], [94, 27, 154, 24], [94, 28, 154, 25], [95, 6, 155, 4], [96, 6, 155, 5], [96, 13, 155, 5, "_this"], [96, 18, 155, 5], [97, 4, 156, 2], [98, 4, 156, 3], [98, 8, 156, 3, "_inherits2"], [98, 18, 156, 3], [98, 19, 156, 3, "default"], [98, 26, 156, 3], [98, 28, 156, 3, "AnimatedColor"], [98, 41, 156, 3], [98, 43, 156, 3, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [98, 64, 156, 3], [99, 4, 156, 3], [99, 15, 156, 3, "_createClass2"], [99, 28, 156, 3], [99, 29, 156, 3, "default"], [99, 36, 156, 3], [99, 38, 156, 3, "AnimatedColor"], [99, 51, 156, 3], [100, 6, 156, 3, "key"], [100, 9, 156, 3], [101, 6, 156, 3, "value"], [101, 11, 156, 3], [101, 13, 162, 2], [101, 22, 162, 2, "setValue"], [101, 30, 162, 10, "setValue"], [101, 31, 162, 11, "value"], [101, 36, 162, 40], [101, 38, 162, 48], [102, 8, 163, 4], [102, 12, 163, 8, "shouldUpdateNodeConfig"], [102, 34, 163, 30], [102, 37, 163, 33], [102, 42, 163, 38], [103, 8, 164, 4], [103, 12, 164, 8], [103, 16, 164, 12], [103, 17, 164, 13, "__isNative"], [103, 27, 164, 23], [103, 29, 164, 25], [104, 10, 165, 6], [104, 14, 165, 12, "nativeTag"], [104, 23, 165, 21], [104, 26, 165, 24], [104, 30, 165, 28], [104, 31, 165, 29, "__getNativeTag"], [104, 45, 165, 43], [104, 46, 165, 44], [104, 47, 165, 45], [105, 10, 166, 6, "NativeAnimatedAPI"], [105, 27, 166, 23], [105, 28, 166, 24, "setWaitingForIdentifier"], [105, 51, 166, 47], [105, 52, 166, 48, "nativeTag"], [105, 61, 166, 57], [105, 62, 166, 58, "toString"], [105, 70, 166, 66], [105, 71, 166, 67], [105, 72, 166, 68], [105, 73, 166, 69], [106, 8, 167, 4], [107, 8, 169, 4], [107, 12, 169, 10, "processedColor"], [107, 26, 169, 54], [107, 29, 170, 6, "processColor"], [107, 41, 170, 18], [107, 42, 170, 19, "value"], [107, 47, 170, 24], [107, 48, 170, 25], [107, 52, 170, 29, "defaultColor"], [107, 64, 170, 41], [108, 8, 171, 4], [108, 12, 171, 8], [108, 13, 171, 9, "_withSuspendedCallbacks"], [108, 36, 171, 32], [108, 37, 171, 33], [108, 43, 171, 39], [109, 10, 172, 6], [109, 14, 172, 10, "isRgbaValue"], [109, 25, 172, 21], [109, 26, 172, 22, "processedColor"], [109, 40, 172, 36], [109, 41, 172, 37], [109, 43, 172, 39], [110, 12, 174, 8], [110, 16, 174, 14, "rgbaValue"], [110, 25, 174, 34], [110, 28, 174, 37, "processedColor"], [110, 42, 174, 51], [111, 12, 175, 8], [111, 16, 175, 12], [111, 17, 175, 13, "r"], [111, 18, 175, 14], [111, 19, 175, 15, "setValue"], [111, 27, 175, 23], [111, 28, 175, 24, "rgbaValue"], [111, 37, 175, 33], [111, 38, 175, 34, "r"], [111, 39, 175, 35], [111, 40, 175, 36], [112, 12, 176, 8], [112, 16, 176, 12], [112, 17, 176, 13, "g"], [112, 18, 176, 14], [112, 19, 176, 15, "setValue"], [112, 27, 176, 23], [112, 28, 176, 24, "rgbaValue"], [112, 37, 176, 33], [112, 38, 176, 34, "g"], [112, 39, 176, 35], [112, 40, 176, 36], [113, 12, 177, 8], [113, 16, 177, 12], [113, 17, 177, 13, "b"], [113, 18, 177, 14], [113, 19, 177, 15, "setValue"], [113, 27, 177, 23], [113, 28, 177, 24, "rgbaValue"], [113, 37, 177, 33], [113, 38, 177, 34, "b"], [113, 39, 177, 35], [113, 40, 177, 36], [114, 12, 178, 8], [114, 16, 178, 12], [114, 17, 178, 13, "a"], [114, 18, 178, 14], [114, 19, 178, 15, "setValue"], [114, 27, 178, 23], [114, 28, 178, 24, "rgbaValue"], [114, 37, 178, 33], [114, 38, 178, 34, "a"], [114, 39, 178, 35], [114, 40, 178, 36], [115, 12, 179, 8], [115, 16, 179, 12], [115, 20, 179, 16], [115, 21, 179, 17, "nativeColor"], [115, 32, 179, 28], [115, 36, 179, 32], [115, 40, 179, 36], [115, 42, 179, 38], [116, 14, 180, 10], [116, 18, 180, 14], [116, 19, 180, 15, "nativeColor"], [116, 30, 180, 26], [116, 33, 180, 29], [116, 37, 180, 33], [117, 14, 181, 10, "shouldUpdateNodeConfig"], [117, 36, 181, 32], [117, 39, 181, 35], [117, 43, 181, 39], [118, 12, 182, 8], [119, 10, 183, 6], [119, 11, 183, 7], [119, 17, 183, 13], [120, 12, 185, 8], [120, 16, 185, 14, "nativeColor"], [120, 27, 185, 43], [120, 30, 185, 46, "processedColor"], [120, 44, 185, 60], [121, 12, 186, 8], [121, 16, 186, 12], [121, 20, 186, 16], [121, 21, 186, 17, "nativeColor"], [121, 32, 186, 28], [121, 37, 186, 33, "nativeColor"], [121, 48, 186, 44], [121, 50, 186, 46], [122, 14, 187, 10], [122, 18, 187, 14], [122, 19, 187, 15, "nativeColor"], [122, 30, 187, 26], [122, 33, 187, 29, "nativeColor"], [122, 44, 187, 40], [123, 14, 188, 10, "shouldUpdateNodeConfig"], [123, 36, 188, 32], [123, 39, 188, 35], [123, 43, 188, 39], [124, 12, 189, 8], [125, 10, 190, 6], [126, 8, 191, 4], [126, 9, 191, 5], [126, 10, 191, 6], [127, 8, 193, 4], [127, 12, 193, 8], [127, 16, 193, 12], [127, 17, 193, 13, "__isNative"], [127, 27, 193, 23], [127, 29, 193, 25], [128, 10, 194, 6], [128, 14, 194, 12, "nativeTag"], [128, 24, 194, 21], [128, 27, 194, 24], [128, 31, 194, 28], [128, 32, 194, 29, "__getNativeTag"], [128, 46, 194, 43], [128, 47, 194, 44], [128, 48, 194, 45], [129, 10, 195, 6], [129, 14, 195, 10, "shouldUpdateNodeConfig"], [129, 36, 195, 32], [129, 38, 195, 34], [130, 12, 196, 8, "NativeAnimatedAPI"], [130, 29, 196, 25], [130, 30, 196, 26, "updateAnimatedNodeConfig"], [130, 54, 196, 50], [130, 55, 197, 10, "nativeTag"], [130, 65, 197, 19], [130, 67, 198, 10], [130, 71, 198, 14], [130, 72, 198, 15, "__getNativeConfig"], [130, 89, 198, 32], [130, 90, 198, 33], [130, 91, 199, 8], [130, 92, 199, 9], [131, 10, 200, 6], [132, 10, 201, 6, "NativeAnimatedAPI"], [132, 27, 201, 23], [132, 28, 201, 24, "unsetWaitingForIdentifier"], [132, 53, 201, 49], [132, 54, 201, 50, "nativeTag"], [132, 64, 201, 59], [132, 65, 201, 60, "toString"], [132, 73, 201, 68], [132, 74, 201, 69], [132, 75, 201, 70], [132, 76, 201, 71], [133, 8, 202, 4], [133, 9, 202, 5], [133, 15, 202, 11], [134, 10, 203, 6], [134, 14, 203, 6, "flushValue"], [134, 39, 203, 16], [134, 41, 203, 17], [134, 45, 203, 21], [134, 46, 203, 22], [135, 8, 204, 4], [136, 8, 207, 4], [136, 12, 207, 8], [136, 13, 207, 9, "__callListeners"], [136, 28, 207, 24], [136, 29, 207, 25], [136, 33, 207, 29], [136, 34, 207, 30, "__getValue"], [136, 44, 207, 40], [136, 45, 207, 41], [136, 46, 207, 42], [136, 47, 207, 43], [137, 6, 208, 2], [138, 4, 208, 3], [139, 6, 208, 3, "key"], [139, 9, 208, 3], [140, 6, 208, 3, "value"], [140, 11, 208, 3], [140, 13, 215, 2], [140, 22, 215, 2, "setOffset"], [140, 31, 215, 11, "setOffset"], [140, 32, 215, 12, "offset"], [140, 38, 215, 29], [140, 40, 215, 37], [141, 8, 216, 4], [141, 12, 216, 8], [141, 13, 216, 9, "r"], [141, 14, 216, 10], [141, 15, 216, 11, "setOffset"], [141, 24, 216, 20], [141, 25, 216, 21, "offset"], [141, 31, 216, 27], [141, 32, 216, 28, "r"], [141, 33, 216, 29], [141, 34, 216, 30], [142, 8, 217, 4], [142, 12, 217, 8], [142, 13, 217, 9, "g"], [142, 14, 217, 10], [142, 15, 217, 11, "setOffset"], [142, 24, 217, 20], [142, 25, 217, 21, "offset"], [142, 31, 217, 27], [142, 32, 217, 28, "g"], [142, 33, 217, 29], [142, 34, 217, 30], [143, 8, 218, 4], [143, 12, 218, 8], [143, 13, 218, 9, "b"], [143, 14, 218, 10], [143, 15, 218, 11, "setOffset"], [143, 24, 218, 20], [143, 25, 218, 21, "offset"], [143, 31, 218, 27], [143, 32, 218, 28, "b"], [143, 33, 218, 29], [143, 34, 218, 30], [144, 8, 219, 4], [144, 12, 219, 8], [144, 13, 219, 9, "a"], [144, 14, 219, 10], [144, 15, 219, 11, "setOffset"], [144, 24, 219, 20], [144, 25, 219, 21, "offset"], [144, 31, 219, 27], [144, 32, 219, 28, "a"], [144, 33, 219, 29], [144, 34, 219, 30], [145, 6, 220, 2], [146, 4, 220, 3], [147, 6, 220, 3, "key"], [147, 9, 220, 3], [148, 6, 220, 3, "value"], [148, 11, 220, 3], [148, 13, 226, 2], [148, 22, 226, 2, "flattenOffset"], [148, 35, 226, 15, "flattenOffset"], [148, 36, 226, 15], [148, 38, 226, 24], [149, 8, 227, 4], [149, 12, 227, 8], [149, 13, 227, 9, "r"], [149, 14, 227, 10], [149, 15, 227, 11, "flattenOffset"], [149, 28, 227, 24], [149, 29, 227, 25], [149, 30, 227, 26], [150, 8, 228, 4], [150, 12, 228, 8], [150, 13, 228, 9, "g"], [150, 14, 228, 10], [150, 15, 228, 11, "flattenOffset"], [150, 28, 228, 24], [150, 29, 228, 25], [150, 30, 228, 26], [151, 8, 229, 4], [151, 12, 229, 8], [151, 13, 229, 9, "b"], [151, 14, 229, 10], [151, 15, 229, 11, "flattenOffset"], [151, 28, 229, 24], [151, 29, 229, 25], [151, 30, 229, 26], [152, 8, 230, 4], [152, 12, 230, 8], [152, 13, 230, 9, "a"], [152, 14, 230, 10], [152, 15, 230, 11, "flattenOffset"], [152, 28, 230, 24], [152, 29, 230, 25], [152, 30, 230, 26], [153, 6, 231, 2], [154, 4, 231, 3], [155, 6, 231, 3, "key"], [155, 9, 231, 3], [156, 6, 231, 3, "value"], [156, 11, 231, 3], [156, 13, 237, 2], [156, 22, 237, 2, "extractOffset"], [156, 35, 237, 15, "extractOffset"], [156, 36, 237, 15], [156, 38, 237, 24], [157, 8, 238, 4], [157, 12, 238, 8], [157, 13, 238, 9, "r"], [157, 14, 238, 10], [157, 15, 238, 11, "extractOffset"], [157, 28, 238, 24], [157, 29, 238, 25], [157, 30, 238, 26], [158, 8, 239, 4], [158, 12, 239, 8], [158, 13, 239, 9, "g"], [158, 14, 239, 10], [158, 15, 239, 11, "extractOffset"], [158, 28, 239, 24], [158, 29, 239, 25], [158, 30, 239, 26], [159, 8, 240, 4], [159, 12, 240, 8], [159, 13, 240, 9, "b"], [159, 14, 240, 10], [159, 15, 240, 11, "extractOffset"], [159, 28, 240, 24], [159, 29, 240, 25], [159, 30, 240, 26], [160, 8, 241, 4], [160, 12, 241, 8], [160, 13, 241, 9, "a"], [160, 14, 241, 10], [160, 15, 241, 11, "extractOffset"], [160, 28, 241, 24], [160, 29, 241, 25], [160, 30, 241, 26], [161, 6, 242, 2], [162, 4, 242, 3], [163, 6, 242, 3, "key"], [163, 9, 242, 3], [164, 6, 242, 3, "value"], [164, 11, 242, 3], [164, 13, 249, 2], [164, 22, 249, 2, "stopAnimation"], [164, 35, 249, 15, "stopAnimation"], [164, 36, 249, 16, "callback"], [164, 44, 249, 48], [164, 46, 249, 56], [165, 8, 250, 4], [165, 12, 250, 8], [165, 13, 250, 9, "r"], [165, 14, 250, 10], [165, 15, 250, 11, "stopAnimation"], [165, 28, 250, 24], [165, 29, 250, 25], [165, 30, 250, 26], [166, 8, 251, 4], [166, 12, 251, 8], [166, 13, 251, 9, "g"], [166, 14, 251, 10], [166, 15, 251, 11, "stopAnimation"], [166, 28, 251, 24], [166, 29, 251, 25], [166, 30, 251, 26], [167, 8, 252, 4], [167, 12, 252, 8], [167, 13, 252, 9, "b"], [167, 14, 252, 10], [167, 15, 252, 11, "stopAnimation"], [167, 28, 252, 24], [167, 29, 252, 25], [167, 30, 252, 26], [168, 8, 253, 4], [168, 12, 253, 8], [168, 13, 253, 9, "a"], [168, 14, 253, 10], [168, 15, 253, 11, "stopAnimation"], [168, 28, 253, 24], [168, 29, 253, 25], [168, 30, 253, 26], [169, 8, 254, 4, "callback"], [169, 16, 254, 12], [169, 20, 254, 16, "callback"], [169, 28, 254, 24], [169, 29, 254, 25], [169, 33, 254, 29], [169, 34, 254, 30, "__getValue"], [169, 44, 254, 40], [169, 45, 254, 41], [169, 46, 254, 42], [169, 47, 254, 43], [170, 6, 255, 2], [171, 4, 255, 3], [172, 6, 255, 3, "key"], [172, 9, 255, 3], [173, 6, 255, 3, "value"], [173, 11, 255, 3], [173, 13, 260, 2], [173, 22, 260, 2, "resetAnimation"], [173, 36, 260, 16, "resetAnimation"], [173, 37, 260, 17, "callback"], [173, 45, 260, 49], [173, 47, 260, 57], [174, 8, 261, 4], [174, 12, 261, 8], [174, 13, 261, 9, "r"], [174, 14, 261, 10], [174, 15, 261, 11, "resetAnimation"], [174, 29, 261, 25], [174, 30, 261, 26], [174, 31, 261, 27], [175, 8, 262, 4], [175, 12, 262, 8], [175, 13, 262, 9, "g"], [175, 14, 262, 10], [175, 15, 262, 11, "resetAnimation"], [175, 29, 262, 25], [175, 30, 262, 26], [175, 31, 262, 27], [176, 8, 263, 4], [176, 12, 263, 8], [176, 13, 263, 9, "b"], [176, 14, 263, 10], [176, 15, 263, 11, "resetAnimation"], [176, 29, 263, 25], [176, 30, 263, 26], [176, 31, 263, 27], [177, 8, 264, 4], [177, 12, 264, 8], [177, 13, 264, 9, "a"], [177, 14, 264, 10], [177, 15, 264, 11, "resetAnimation"], [177, 29, 264, 25], [177, 30, 264, 26], [177, 31, 264, 27], [178, 8, 265, 4, "callback"], [178, 16, 265, 12], [178, 20, 265, 16, "callback"], [178, 28, 265, 24], [178, 29, 265, 25], [178, 33, 265, 29], [178, 34, 265, 30, "__getValue"], [178, 44, 265, 40], [178, 45, 265, 41], [178, 46, 265, 42], [178, 47, 265, 43], [179, 6, 266, 2], [180, 4, 266, 3], [181, 6, 266, 3, "key"], [181, 9, 266, 3], [182, 6, 266, 3, "value"], [182, 11, 266, 3], [182, 13, 268, 2], [182, 22, 268, 2, "__getValue"], [182, 32, 268, 12, "__getValue"], [182, 33, 268, 12], [182, 35, 268, 27], [183, 8, 269, 4], [183, 12, 269, 8], [183, 16, 269, 12], [183, 17, 269, 13, "nativeColor"], [183, 28, 269, 24], [183, 32, 269, 28], [183, 36, 269, 32], [183, 38, 269, 34], [184, 10, 270, 6], [184, 17, 270, 13], [184, 21, 270, 17], [184, 22, 270, 18, "nativeColor"], [184, 33, 270, 29], [185, 8, 271, 4], [185, 9, 271, 5], [185, 15, 271, 11], [186, 10, 272, 6], [186, 17, 272, 13], [186, 25, 272, 21], [186, 29, 272, 25], [186, 30, 272, 26, "r"], [186, 31, 272, 27], [186, 32, 272, 28, "__getValue"], [186, 42, 272, 38], [186, 43, 272, 39], [186, 44, 272, 40], [186, 49, 272, 45], [186, 53, 272, 49], [186, 54, 272, 50, "g"], [186, 55, 272, 51], [186, 56, 272, 52, "__getValue"], [186, 66, 272, 62], [186, 67, 272, 63], [186, 68, 272, 64], [186, 73, 272, 69], [186, 77, 272, 73], [186, 78, 272, 74, "b"], [186, 79, 272, 75], [186, 80, 272, 76, "__getValue"], [186, 90, 272, 86], [186, 91, 272, 87], [186, 92, 272, 88], [186, 97, 272, 93], [186, 101, 272, 97], [186, 102, 272, 98, "a"], [186, 103, 272, 99], [186, 104, 272, 100, "__getValue"], [186, 114, 272, 110], [186, 115, 272, 111], [186, 116, 272, 112], [186, 119, 272, 115], [187, 8, 273, 4], [188, 6, 274, 2], [189, 4, 274, 3], [190, 6, 274, 3, "key"], [190, 9, 274, 3], [191, 6, 274, 3, "value"], [191, 11, 274, 3], [191, 13, 276, 2], [191, 22, 276, 2, "__attach"], [191, 30, 276, 10, "__attach"], [191, 31, 276, 10], [191, 33, 276, 19], [192, 8, 277, 4], [192, 12, 277, 8], [192, 13, 277, 9, "r"], [192, 14, 277, 10], [192, 15, 277, 11, "__add<PERSON><PERSON>d"], [192, 25, 277, 21], [192, 26, 277, 22], [192, 30, 277, 26], [192, 31, 277, 27], [193, 8, 278, 4], [193, 12, 278, 8], [193, 13, 278, 9, "g"], [193, 14, 278, 10], [193, 15, 278, 11, "__add<PERSON><PERSON>d"], [193, 25, 278, 21], [193, 26, 278, 22], [193, 30, 278, 26], [193, 31, 278, 27], [194, 8, 279, 4], [194, 12, 279, 8], [194, 13, 279, 9, "b"], [194, 14, 279, 10], [194, 15, 279, 11, "__add<PERSON><PERSON>d"], [194, 25, 279, 21], [194, 26, 279, 22], [194, 30, 279, 26], [194, 31, 279, 27], [195, 8, 280, 4], [195, 12, 280, 8], [195, 13, 280, 9, "a"], [195, 14, 280, 10], [195, 15, 280, 11, "__add<PERSON><PERSON>d"], [195, 25, 280, 21], [195, 26, 280, 22], [195, 30, 280, 26], [195, 31, 280, 27], [196, 8, 281, 4, "_superPropGet"], [196, 21, 281, 4], [196, 22, 281, 4, "AnimatedColor"], [196, 35, 281, 4], [197, 6, 282, 2], [198, 4, 282, 3], [199, 6, 282, 3, "key"], [199, 9, 282, 3], [200, 6, 282, 3, "value"], [200, 11, 282, 3], [200, 13, 284, 2], [200, 22, 284, 2, "__detach"], [200, 30, 284, 10, "__detach"], [200, 31, 284, 10], [200, 33, 284, 19], [201, 8, 285, 4], [201, 12, 285, 8], [201, 13, 285, 9, "r"], [201, 14, 285, 10], [201, 15, 285, 11, "__remove<PERSON><PERSON>d"], [201, 28, 285, 24], [201, 29, 285, 25], [201, 33, 285, 29], [201, 34, 285, 30], [202, 8, 286, 4], [202, 12, 286, 8], [202, 13, 286, 9, "g"], [202, 14, 286, 10], [202, 15, 286, 11, "__remove<PERSON><PERSON>d"], [202, 28, 286, 24], [202, 29, 286, 25], [202, 33, 286, 29], [202, 34, 286, 30], [203, 8, 287, 4], [203, 12, 287, 8], [203, 13, 287, 9, "b"], [203, 14, 287, 10], [203, 15, 287, 11, "__remove<PERSON><PERSON>d"], [203, 28, 287, 24], [203, 29, 287, 25], [203, 33, 287, 29], [203, 34, 287, 30], [204, 8, 288, 4], [204, 12, 288, 8], [204, 13, 288, 9, "a"], [204, 14, 288, 10], [204, 15, 288, 11, "__remove<PERSON><PERSON>d"], [204, 28, 288, 24], [204, 29, 288, 25], [204, 33, 288, 29], [204, 34, 288, 30], [205, 8, 289, 4, "_superPropGet"], [205, 21, 289, 4], [205, 22, 289, 4, "AnimatedColor"], [205, 35, 289, 4], [206, 6, 290, 2], [207, 4, 290, 3], [208, 6, 290, 3, "key"], [208, 9, 290, 3], [209, 6, 290, 3, "value"], [209, 11, 290, 3], [209, 13, 292, 2], [209, 22, 292, 2, "_withSuspendedCallbacks"], [209, 45, 292, 25, "_withSuspendedCallbacks"], [209, 46, 292, 26, "callback"], [209, 54, 292, 46], [209, 56, 292, 48], [210, 8, 293, 4], [210, 12, 293, 8], [210, 13, 293, 9, "_suspendCallbacks"], [210, 30, 293, 26], [210, 32, 293, 28], [211, 8, 294, 4, "callback"], [211, 16, 294, 12], [211, 17, 294, 13], [211, 18, 294, 14], [212, 8, 295, 4], [212, 12, 295, 8], [212, 13, 295, 9, "_suspendCallbacks"], [212, 30, 295, 26], [212, 32, 295, 28], [213, 6, 296, 2], [214, 4, 296, 3], [215, 6, 296, 3, "key"], [215, 9, 296, 3], [216, 6, 296, 3, "value"], [216, 11, 296, 3], [216, 13, 298, 2], [216, 22, 298, 2, "__callListeners"], [216, 37, 298, 17, "__callListeners"], [216, 38, 298, 18, "value"], [216, 43, 298, 31], [216, 45, 298, 39], [217, 8, 299, 4], [217, 12, 299, 8], [217, 16, 299, 12], [217, 17, 299, 13, "_suspendCallbacks"], [217, 34, 299, 30], [217, 39, 299, 35], [217, 40, 299, 36], [217, 42, 299, 38], [218, 10, 300, 6, "_superPropGet"], [218, 23, 300, 6], [218, 24, 300, 6, "AnimatedColor"], [218, 37, 300, 6], [218, 68, 300, 28, "value"], [218, 73, 300, 33], [219, 8, 301, 4], [220, 6, 302, 2], [221, 4, 302, 3], [222, 6, 302, 3, "key"], [222, 9, 302, 3], [223, 6, 302, 3, "value"], [223, 11, 302, 3], [223, 13, 304, 2], [223, 22, 304, 2, "__makeNative"], [223, 34, 304, 14, "__makeNative"], [223, 35, 304, 15, "platformConfig"], [223, 49, 304, 46], [223, 51, 304, 48], [224, 8, 305, 4], [224, 12, 305, 8], [224, 13, 305, 9, "r"], [224, 14, 305, 10], [224, 15, 305, 11, "__makeNative"], [224, 27, 305, 23], [224, 28, 305, 24, "platformConfig"], [224, 42, 305, 38], [224, 43, 305, 39], [225, 8, 306, 4], [225, 12, 306, 8], [225, 13, 306, 9, "g"], [225, 14, 306, 10], [225, 15, 306, 11, "__makeNative"], [225, 27, 306, 23], [225, 28, 306, 24, "platformConfig"], [225, 42, 306, 38], [225, 43, 306, 39], [226, 8, 307, 4], [226, 12, 307, 8], [226, 13, 307, 9, "b"], [226, 14, 307, 10], [226, 15, 307, 11, "__makeNative"], [226, 27, 307, 23], [226, 28, 307, 24, "platformConfig"], [226, 42, 307, 38], [226, 43, 307, 39], [227, 8, 308, 4], [227, 12, 308, 8], [227, 13, 308, 9, "a"], [227, 14, 308, 10], [227, 15, 308, 11, "__makeNative"], [227, 27, 308, 23], [227, 28, 308, 24, "platformConfig"], [227, 42, 308, 38], [227, 43, 308, 39], [228, 8, 309, 4, "_superPropGet"], [228, 21, 309, 4], [228, 22, 309, 4, "AnimatedColor"], [228, 35, 309, 4], [228, 63, 309, 23, "platformConfig"], [228, 77, 309, 37], [229, 6, 310, 2], [230, 4, 310, 3], [231, 6, 310, 3, "key"], [231, 9, 310, 3], [232, 6, 310, 3, "value"], [232, 11, 310, 3], [232, 13, 312, 2], [232, 22, 312, 2, "__getNativeConfig"], [232, 39, 312, 19, "__getNativeConfig"], [232, 40, 312, 19], [232, 42, 312, 29], [233, 8, 313, 4], [233, 15, 313, 11], [234, 10, 314, 6, "type"], [234, 14, 314, 10], [234, 16, 314, 12], [234, 23, 314, 19], [235, 10, 315, 6, "r"], [235, 11, 315, 7], [235, 13, 315, 9], [235, 17, 315, 13], [235, 18, 315, 14, "r"], [235, 19, 315, 15], [235, 20, 315, 16, "__getNativeTag"], [235, 34, 315, 30], [235, 35, 315, 31], [235, 36, 315, 32], [236, 10, 316, 6, "g"], [236, 11, 316, 7], [236, 13, 316, 9], [236, 17, 316, 13], [236, 18, 316, 14, "g"], [236, 19, 316, 15], [236, 20, 316, 16, "__getNativeTag"], [236, 34, 316, 30], [236, 35, 316, 31], [236, 36, 316, 32], [237, 10, 317, 6, "b"], [237, 11, 317, 7], [237, 13, 317, 9], [237, 17, 317, 13], [237, 18, 317, 14, "b"], [237, 19, 317, 15], [237, 20, 317, 16, "__getNativeTag"], [237, 34, 317, 30], [237, 35, 317, 31], [237, 36, 317, 32], [238, 10, 318, 6, "a"], [238, 11, 318, 7], [238, 13, 318, 9], [238, 17, 318, 13], [238, 18, 318, 14, "a"], [238, 19, 318, 15], [238, 20, 318, 16, "__getNativeTag"], [238, 34, 318, 30], [238, 35, 318, 31], [238, 36, 318, 32], [239, 10, 319, 6, "nativeColor"], [239, 21, 319, 17], [239, 23, 319, 19], [239, 27, 319, 23], [239, 28, 319, 24, "nativeColor"], [239, 39, 319, 35], [240, 10, 320, 6, "debugID"], [240, 17, 320, 13], [240, 19, 320, 15], [240, 23, 320, 19], [240, 24, 320, 20, "__getDebugID"], [240, 36, 320, 32], [240, 37, 320, 33], [241, 8, 321, 4], [241, 9, 321, 5], [242, 6, 322, 2], [243, 4, 322, 3], [244, 2, 322, 3], [244, 4, 113, 43, "AnimatedWithChildren"], [244, 34, 113, 63], [245, 0, 113, 63], [245, 3]], "functionMap": {"names": ["<global>", "processColor", "isRgbaValue", "isRgbaAnimatedValue", "AnimatedColor", "constructor", "setValue", "_withSuspendedCallbacks$argument_0", "setOffset", "flattenOffset", "extractOffset", "stopAnimation", "resetAnimation", "__getValue", "__attach", "__detach", "_withSuspendedCallbacks", "__callListeners", "__makeNative", "__getNativeConfig"], "mappings": "AAA;ACsD;CDoC;AEE;CFQ;AGE;CHQ;eIE;ECS;GDkC;EEM;iCCS;KDoB;GFiB;EIO;GJK;EKM;GLK;EMM;GNK;EOO;GPM;EQK;GRM;ESE;GTM;EUE;GVM;EWE;GXM;EYE;GZI;EaE;GbI;EcE;GdM;EeE;GfU"}}, "type": "js/module"}]}