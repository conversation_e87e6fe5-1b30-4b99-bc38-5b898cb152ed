{"dependencies": [{"name": "../../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 56, "index": 71}}], "key": "vhHMm+PKBSj2e9y550uvksCLTMU=", "exportNames": ["*"]}}, {"name": "../../Easing", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 196}, "end": {"line": 8, "column": 48, "index": 244}}], "key": "Pdfn5mePF9NOG++CTOCTw0Eb7Vw=", "exportNames": ["*"]}}, {"name": "../../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 245}, "end": {"line": 9, "column": 38, "index": 283}}], "key": "BAUnomHCaPEo8SwbXzlKtt9pd/8=", "exportNames": ["*"]}}, {"name": "../animationBuilder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 367}, "end": {"line": 11, "column": 47, "index": 414}}], "key": "R5JQTdOMlkYPuFuFEBj/+tNyNyA=", "exportNames": ["*"]}}, {"name": "./componentStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 472}, "end": {"line": 13, "column": 54, "index": 526}}], "key": "fOjZFTm/VXjk+R+fwki3Uvzs2Zo=", "exportNames": ["*"]}}, {"name": "./componentUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 527}, "end": {"line": 20, "column": 26, "index": 690}}], "key": "NLy4Dc7AYx9ZjQEInbEC8R21Z3c=", "exportNames": ["*"]}}, {"name": "./config", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 827}, "end": {"line": 28, "column": 38, "index": 865}}], "key": "apL7GyCxHQJfXSypmIMW0g+q+wo=", "exportNames": ["*"]}}, {"name": "./createAnimation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 866}, "end": {"line": 32, "column": 27, "index": 971}}], "key": "qDHUSoNc54hZyrUabQtWleQvNyU=", "exportNames": ["*"]}}, {"name": "./domUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 972}, "end": {"line": 33, "column": 46, "index": 1018}}], "key": "0d4bIOSgNZHGMgw8FnUojmKgfKI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.startWebLayoutAnimation = startWebLayoutAnimation;\n  exports.tryActivateLayoutTransition = tryActivateLayoutTransition;\n  var _commonTypes = require(_dependencyMap[0], \"../../commonTypes\");\n  var _Easing = require(_dependencyMap[1], \"../../Easing\");\n  var _logger = require(_dependencyMap[2], \"../../logger\");\n  var _animationBuilder = require(_dependencyMap[3], \"../animationBuilder\");\n  var _componentStyle = require(_dependencyMap[4], \"./componentStyle\");\n  var _componentUtils = require(_dependencyMap[5], \"./componentUtils\");\n  var _config = require(_dependencyMap[6], \"./config\");\n  var _createAnimation = require(_dependencyMap[7], \"./createAnimation\");\n  var _domUtils = require(_dependencyMap[8], \"./domUtils\");\n  function chooseConfig(animationType, props) {\n    var config = animationType === _commonTypes.LayoutAnimationType.ENTERING ? props.entering : animationType === _commonTypes.LayoutAnimationType.EXITING ? props.exiting : animationType === _commonTypes.LayoutAnimationType.LAYOUT ? props.layout : null;\n    return config;\n  }\n  function checkUndefinedAnimationFail(initialAnimationName, needsCustomization) {\n    // This prevents crashes if we try to set animations that are not defined.\n    // We don't care about layout transitions or custom keyframes since they're created dynamically\n    if (initialAnimationName in _config.Animations || needsCustomization) {\n      return false;\n    }\n    _logger.logger.warn(\"Couldn't load entering/exiting animation. Current version supports only predefined animations with modifiers: duration, delay, easing, randomizeDelay, withCallback, reducedMotion.\");\n    return true;\n  }\n  function maybeReportOverwrittenProperties(keyframe, styles) {\n    var propertyRegex = /([a-zA-Z-]+)(?=:)/g;\n    var animationProperties = new Set();\n    for (var match of keyframe.matchAll(propertyRegex)) {\n      animationProperties.add(match[1]);\n    }\n    var commonProperties = Array.from(styles).filter(style => animationProperties.has(style));\n    if (commonProperties.length === 0) {\n      return;\n    }\n    _logger.logger.warn(`${commonProperties.length === 1 ? 'Property' : 'Properties'} [${commonProperties.join(', ')}] may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`);\n  }\n  function chooseAction(animationType, animationConfig, element, transitionData) {\n    switch (animationType) {\n      case _commonTypes.LayoutAnimationType.ENTERING:\n        (0, _componentUtils.setElementAnimation)(element, animationConfig, true);\n        break;\n      case _commonTypes.LayoutAnimationType.LAYOUT:\n        transitionData.reversed = animationConfig.reversed;\n        (0, _componentUtils.handleLayoutTransition)(element, animationConfig, transitionData);\n        break;\n      case _commonTypes.LayoutAnimationType.EXITING:\n        (0, _componentUtils.handleExitingAnimation)(element, animationConfig);\n        break;\n    }\n  }\n  function tryGetAnimationConfig(props, animationType) {\n    var config = chooseConfig(animationType, props);\n    if (!config) {\n      return null;\n    }\n    var isLayoutTransition = animationType === _commonTypes.LayoutAnimationType.LAYOUT;\n    var isCustomKeyframe = config instanceof _animationBuilder.Keyframe;\n    var hasInitialValues = config.initialValues !== undefined;\n    var animationName;\n    if (isCustomKeyframe) {\n      animationName = (0, _createAnimation.createCustomKeyFrameAnimation)(config.definitions);\n    } else if (typeof config === 'function') {\n      animationName = config.presetName;\n    } else {\n      animationName = config.constructor.presetName;\n    }\n    if (hasInitialValues) {\n      animationName = (0, _createAnimation.createAnimationWithInitialValues)(animationName, config.initialValues);\n    }\n    var shouldFail = checkUndefinedAnimationFail(animationName, isLayoutTransition || isCustomKeyframe || hasInitialValues);\n    if (shouldFail) {\n      return null;\n    }\n    if (isCustomKeyframe) {\n      var keyframeTimestamps = Object.keys(config.definitions);\n      if (!(keyframeTimestamps.includes('100') || keyframeTimestamps.includes('to'))) {\n        _logger.logger.warn(`Neither '100' nor 'to' was specified in Keyframe definition. This may result in wrong final position of your component. One possible solution is to duplicate last timestamp in definition as '100' (or 'to')`);\n      }\n    }\n    var animationConfig = (0, _componentUtils.getProcessedConfig)(animationName, animationType, config);\n    return animationConfig;\n  }\n  function startWebLayoutAnimation(props, element, animationType, transitionData) {\n    var animationConfig = tryGetAnimationConfig(props, animationType);\n    (0, _componentUtils.maybeModifyStyleForKeyframe)(element, props.entering);\n    if (animationConfig?.animationName in _config.Animations) {\n      maybeReportOverwrittenProperties(_config.Animations[animationConfig?.animationName].style, element.style);\n    }\n    if (animationConfig) {\n      chooseAction(animationType, animationConfig, element, transitionData);\n    } else {\n      (0, _componentStyle.makeElementVisible)(element, 0);\n    }\n  }\n  function tryActivateLayoutTransition(props, element, snapshot) {\n    if (!props.layout) {\n      return;\n    }\n    var rect = element.getBoundingClientRect();\n    if ((0, _domUtils.areDOMRectsEqual)(rect, snapshot)) {\n      return;\n    }\n    var enteringAnimation = props.layout.enteringV?.presetName;\n    var exitingAnimation = props.layout.exitingV?.presetName;\n    var deltaX = (snapshot.width - rect.width) / 2;\n    var deltaY = (snapshot.height - rect.height) / 2;\n    var transitionData = {\n      translateX: snapshot.x - rect.x + deltaX,\n      translateY: snapshot.y - rect.y + deltaY,\n      scaleX: snapshot.width / rect.width,\n      scaleY: snapshot.height / rect.height,\n      reversed: false,\n      // This field is used only in `SequencedTransition`, so by default it will be false\n      easingX: props.layout.easingXV?.[_Easing.EasingNameSymbol] ?? 'ease',\n      easingY: props.layout.easingYV?.[_Easing.EasingNameSymbol] ?? 'ease',\n      entering: enteringAnimation,\n      exiting: exitingAnimation\n    };\n    startWebLayoutAnimation(props, element, _commonTypes.LayoutAnimationType.LAYOUT, transitionData);\n  }\n});", "lineCount": 127, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "startWebLayoutAnimation"], [7, 33, 1, 13], [7, 36, 1, 13, "startWebLayoutAnimation"], [7, 59, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "tryActivateLayoutTransition"], [8, 37, 1, 13], [8, 40, 1, 13, "tryActivateLayoutTransition"], [8, 67, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_commonTypes"], [9, 18, 3, 0], [9, 21, 3, 0, "require"], [9, 28, 3, 0], [9, 29, 3, 0, "_dependencyMap"], [9, 43, 3, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_Easing"], [10, 13, 8, 0], [10, 16, 8, 0, "require"], [10, 23, 8, 0], [10, 24, 8, 0, "_dependencyMap"], [10, 38, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_logger"], [11, 13, 9, 0], [11, 16, 9, 0, "require"], [11, 23, 9, 0], [11, 24, 9, 0, "_dependencyMap"], [11, 38, 9, 0], [12, 2, 11, 0], [12, 6, 11, 0, "_animationBuilder"], [12, 23, 11, 0], [12, 26, 11, 0, "require"], [12, 33, 11, 0], [12, 34, 11, 0, "_dependencyMap"], [12, 48, 11, 0], [13, 2, 13, 0], [13, 6, 13, 0, "_componentStyle"], [13, 21, 13, 0], [13, 24, 13, 0, "require"], [13, 31, 13, 0], [13, 32, 13, 0, "_dependencyMap"], [13, 46, 13, 0], [14, 2, 14, 0], [14, 6, 14, 0, "_componentUtils"], [14, 21, 14, 0], [14, 24, 14, 0, "require"], [14, 31, 14, 0], [14, 32, 14, 0, "_dependencyMap"], [14, 46, 14, 0], [15, 2, 28, 0], [15, 6, 28, 0, "_config"], [15, 13, 28, 0], [15, 16, 28, 0, "require"], [15, 23, 28, 0], [15, 24, 28, 0, "_dependencyMap"], [15, 38, 28, 0], [16, 2, 29, 0], [16, 6, 29, 0, "_createAnimation"], [16, 22, 29, 0], [16, 25, 29, 0, "require"], [16, 32, 29, 0], [16, 33, 29, 0, "_dependencyMap"], [16, 47, 29, 0], [17, 2, 33, 0], [17, 6, 33, 0, "_domUtils"], [17, 15, 33, 0], [17, 18, 33, 0, "require"], [17, 25, 33, 0], [17, 26, 33, 0, "_dependencyMap"], [17, 40, 33, 0], [18, 2, 35, 0], [18, 11, 35, 9, "chooseConfig"], [18, 23, 35, 21, "chooseConfig"], [18, 24, 36, 2, "animationType"], [18, 37, 36, 36], [18, 39, 37, 2, "props"], [18, 44, 37, 57], [18, 46, 38, 2], [19, 4, 39, 2], [19, 8, 39, 8, "config"], [19, 14, 39, 14], [19, 17, 40, 4, "animationType"], [19, 30, 40, 17], [19, 35, 40, 22, "LayoutAnimationType"], [19, 67, 40, 41], [19, 68, 40, 42, "ENTERING"], [19, 76, 40, 50], [19, 79, 41, 8, "props"], [19, 84, 41, 13], [19, 85, 41, 14, "entering"], [19, 93, 41, 22], [19, 96, 42, 8, "animationType"], [19, 109, 42, 21], [19, 114, 42, 26, "LayoutAnimationType"], [19, 146, 42, 45], [19, 147, 42, 46, "EXITING"], [19, 154, 42, 53], [19, 157, 43, 10, "props"], [19, 162, 43, 15], [19, 163, 43, 16, "exiting"], [19, 170, 43, 23], [19, 173, 44, 10, "animationType"], [19, 186, 44, 23], [19, 191, 44, 28, "LayoutAnimationType"], [19, 223, 44, 47], [19, 224, 44, 48, "LAYOUT"], [19, 230, 44, 54], [19, 233, 45, 12, "props"], [19, 238, 45, 17], [19, 239, 45, 18, "layout"], [19, 245, 45, 24], [19, 248, 46, 12], [19, 252, 46, 16], [20, 4, 48, 2], [20, 11, 48, 9, "config"], [20, 17, 48, 15], [21, 2, 49, 0], [22, 2, 51, 0], [22, 11, 51, 9, "checkUndefinedAnimationFail"], [22, 38, 51, 36, "checkUndefinedAnimationFail"], [22, 39, 52, 2, "initialAnimationName"], [22, 59, 52, 30], [22, 61, 53, 2, "needsCustomization"], [22, 79, 53, 29], [22, 81, 54, 2], [23, 4, 55, 2], [24, 4, 56, 2], [25, 4, 57, 2], [25, 8, 57, 6, "initialAnimationName"], [25, 28, 57, 26], [25, 32, 57, 30, "Animations"], [25, 50, 57, 40], [25, 54, 57, 44, "needsCustomization"], [25, 72, 57, 62], [25, 74, 57, 64], [26, 6, 58, 4], [26, 13, 58, 11], [26, 18, 58, 16], [27, 4, 59, 2], [28, 4, 61, 2, "logger"], [28, 18, 61, 8], [28, 19, 61, 9, "warn"], [28, 23, 61, 13], [28, 24, 62, 4], [28, 205, 63, 2], [28, 206, 63, 3], [29, 4, 65, 2], [29, 11, 65, 9], [29, 15, 65, 13], [30, 2, 66, 0], [31, 2, 68, 0], [31, 11, 68, 9, "maybeReportOverwrittenProperties"], [31, 43, 68, 41, "maybeReportOverwrittenProperties"], [31, 44, 69, 2, "keyframe"], [31, 52, 69, 18], [31, 54, 70, 2, "styles"], [31, 60, 70, 29], [31, 62, 71, 2], [32, 4, 72, 2], [32, 8, 72, 8, "propertyRegex"], [32, 21, 72, 21], [32, 24, 72, 24], [32, 44, 72, 44], [33, 4, 73, 2], [33, 8, 73, 8, "animationProperties"], [33, 27, 73, 27], [33, 30, 73, 30], [33, 34, 73, 34, "Set"], [33, 37, 73, 37], [33, 38, 73, 38], [33, 39, 73, 39], [34, 4, 75, 2], [34, 9, 75, 7], [34, 13, 75, 13, "match"], [34, 18, 75, 18], [34, 22, 75, 22, "keyframe"], [34, 30, 75, 30], [34, 31, 75, 31, "matchAll"], [34, 39, 75, 39], [34, 40, 75, 40, "propertyRegex"], [34, 53, 75, 53], [34, 54, 75, 54], [34, 56, 75, 56], [35, 6, 76, 4, "animationProperties"], [35, 25, 76, 23], [35, 26, 76, 24, "add"], [35, 29, 76, 27], [35, 30, 76, 28, "match"], [35, 35, 76, 33], [35, 36, 76, 34], [35, 37, 76, 35], [35, 38, 76, 36], [35, 39, 76, 37], [36, 4, 77, 2], [37, 4, 79, 2], [37, 8, 79, 8, "commonProperties"], [37, 24, 79, 24], [37, 27, 79, 27, "Array"], [37, 32, 79, 32], [37, 33, 79, 33, "from"], [37, 37, 79, 37], [37, 38, 79, 38, "styles"], [37, 44, 79, 44], [37, 45, 79, 45], [37, 46, 79, 46, "filter"], [37, 52, 79, 52], [37, 53, 79, 54, "style"], [37, 58, 79, 59], [37, 62, 80, 4, "animationProperties"], [37, 81, 80, 23], [37, 82, 80, 24, "has"], [37, 85, 80, 27], [37, 86, 80, 28, "style"], [37, 91, 80, 33], [37, 92, 81, 2], [37, 93, 81, 3], [38, 4, 83, 2], [38, 8, 83, 6, "commonProperties"], [38, 24, 83, 22], [38, 25, 83, 23, "length"], [38, 31, 83, 29], [38, 36, 83, 34], [38, 37, 83, 35], [38, 39, 83, 37], [39, 6, 84, 4], [40, 4, 85, 2], [41, 4, 87, 2, "logger"], [41, 18, 87, 8], [41, 19, 87, 9, "warn"], [41, 23, 87, 13], [41, 24, 88, 4], [41, 27, 89, 6, "commonProperties"], [41, 43, 89, 22], [41, 44, 89, 23, "length"], [41, 50, 89, 29], [41, 55, 89, 34], [41, 56, 89, 35], [41, 59, 89, 38], [41, 69, 89, 48], [41, 72, 89, 51], [41, 84, 89, 63], [41, 89, 90, 9, "commonProperties"], [41, 105, 90, 25], [41, 106, 90, 26, "join"], [41, 110, 90, 30], [41, 111, 91, 6], [41, 115, 92, 4], [41, 116, 92, 5], [41, 257, 93, 2], [41, 258, 93, 3], [42, 2, 94, 0], [43, 2, 96, 0], [43, 11, 96, 9, "chooseAction"], [43, 23, 96, 21, "chooseAction"], [43, 24, 97, 2, "animationType"], [43, 37, 97, 36], [43, 39, 98, 2, "animationConfig"], [43, 54, 98, 34], [43, 56, 99, 2, "element"], [43, 63, 99, 32], [43, 65, 100, 2, "transitionData"], [43, 79, 100, 32], [43, 81, 101, 2], [44, 4, 102, 2], [44, 12, 102, 10, "animationType"], [44, 25, 102, 23], [45, 6, 103, 4], [45, 11, 103, 9, "LayoutAnimationType"], [45, 43, 103, 28], [45, 44, 103, 29, "ENTERING"], [45, 52, 103, 37], [46, 8, 104, 6], [46, 12, 104, 6, "setElementAnimation"], [46, 47, 104, 25], [46, 49, 104, 26, "element"], [46, 56, 104, 33], [46, 58, 104, 35, "animationConfig"], [46, 73, 104, 50], [46, 75, 104, 52], [46, 79, 104, 56], [46, 80, 104, 57], [47, 8, 105, 6], [48, 6, 106, 4], [48, 11, 106, 9, "LayoutAnimationType"], [48, 43, 106, 28], [48, 44, 106, 29, "LAYOUT"], [48, 50, 106, 35], [49, 8, 107, 6, "transitionData"], [49, 22, 107, 20], [49, 23, 107, 21, "reversed"], [49, 31, 107, 29], [49, 34, 107, 32, "animationConfig"], [49, 49, 107, 47], [49, 50, 107, 48, "reversed"], [49, 58, 107, 56], [50, 8, 108, 6], [50, 12, 108, 6, "handleLayoutTransition"], [50, 50, 108, 28], [50, 52, 108, 29, "element"], [50, 59, 108, 36], [50, 61, 108, 38, "animationConfig"], [50, 76, 108, 53], [50, 78, 108, 55, "transitionData"], [50, 92, 108, 69], [50, 93, 108, 70], [51, 8, 109, 6], [52, 6, 110, 4], [52, 11, 110, 9, "LayoutAnimationType"], [52, 43, 110, 28], [52, 44, 110, 29, "EXITING"], [52, 51, 110, 36], [53, 8, 111, 6], [53, 12, 111, 6, "handleExitingAnimation"], [53, 50, 111, 28], [53, 52, 111, 29, "element"], [53, 59, 111, 36], [53, 61, 111, 38, "animationConfig"], [53, 76, 111, 53], [53, 77, 111, 54], [54, 8, 112, 6], [55, 4, 113, 2], [56, 2, 114, 0], [57, 2, 116, 0], [57, 11, 116, 9, "tryGetAnimationConfig"], [57, 32, 116, 30, "tryGetAnimationConfig"], [57, 33, 117, 2, "props"], [57, 38, 117, 57], [57, 40, 118, 2, "animationType"], [57, 53, 118, 36], [57, 55, 119, 2], [58, 4, 120, 2], [58, 8, 120, 8, "config"], [58, 14, 120, 14], [58, 17, 120, 17, "chooseConfig"], [58, 29, 120, 29], [58, 30, 120, 30, "animationType"], [58, 43, 120, 43], [58, 45, 120, 45, "props"], [58, 50, 120, 50], [58, 51, 120, 51], [59, 4, 121, 2], [59, 8, 121, 6], [59, 9, 121, 7, "config"], [59, 15, 121, 13], [59, 17, 121, 15], [60, 6, 122, 4], [60, 13, 122, 11], [60, 17, 122, 15], [61, 4, 123, 2], [62, 4, 128, 2], [62, 8, 128, 8, "isLayoutTransition"], [62, 26, 128, 26], [62, 29, 128, 29, "animationType"], [62, 42, 128, 42], [62, 47, 128, 47, "LayoutAnimationType"], [62, 79, 128, 66], [62, 80, 128, 67, "LAYOUT"], [62, 86, 128, 73], [63, 4, 129, 2], [63, 8, 129, 8, "isCustomKeyframe"], [63, 24, 129, 24], [63, 27, 129, 27, "config"], [63, 33, 129, 33], [63, 45, 129, 45, "Keyframe"], [63, 71, 129, 53], [64, 4, 130, 2], [64, 8, 130, 8, "hasInitialValues"], [64, 24, 130, 24], [64, 27, 130, 28, "config"], [64, 33, 130, 34], [64, 34, 130, 52, "initialValues"], [64, 47, 130, 65], [64, 52, 130, 70, "undefined"], [64, 61, 130, 79], [65, 4, 132, 2], [65, 8, 132, 6, "animationName"], [65, 21, 132, 19], [66, 4, 134, 2], [66, 8, 134, 6, "isCustomKeyframe"], [66, 24, 134, 22], [66, 26, 134, 24], [67, 6, 135, 4, "animationName"], [67, 19, 135, 17], [67, 22, 135, 20], [67, 26, 135, 20, "createCustomKeyFrameAnimation"], [67, 72, 135, 49], [67, 74, 136, 7, "config"], [67, 80, 136, 13], [67, 81, 136, 31, "definitions"], [67, 92, 137, 4], [67, 93, 137, 5], [68, 4, 138, 2], [68, 5, 138, 3], [68, 11, 138, 9], [68, 15, 138, 13], [68, 22, 138, 20, "config"], [68, 28, 138, 26], [68, 33, 138, 31], [68, 43, 138, 41], [68, 45, 138, 43], [69, 6, 139, 4, "animationName"], [69, 19, 139, 17], [69, 22, 139, 20, "config"], [69, 28, 139, 26], [69, 29, 139, 27, "presetName"], [69, 39, 139, 37], [70, 4, 140, 2], [70, 5, 140, 3], [70, 11, 140, 9], [71, 6, 141, 4, "animationName"], [71, 19, 141, 17], [71, 22, 141, 21, "config"], [71, 28, 141, 27], [71, 29, 141, 28, "constructor"], [71, 40, 141, 39], [71, 41, 142, 7, "presetName"], [71, 51, 142, 17], [72, 4, 143, 2], [73, 4, 145, 2], [73, 8, 145, 6, "hasInitialValues"], [73, 24, 145, 22], [73, 26, 145, 24], [74, 6, 146, 4, "animationName"], [74, 19, 146, 17], [74, 22, 146, 20], [74, 26, 146, 20, "createAnimationWithInitialValues"], [74, 75, 146, 52], [74, 77, 147, 6, "animationName"], [74, 90, 147, 19], [74, 92, 148, 7, "config"], [74, 98, 148, 13], [74, 99, 148, 31, "initialValues"], [74, 112, 149, 4], [74, 113, 149, 5], [75, 4, 150, 2], [76, 4, 152, 2], [76, 8, 152, 8, "shouldFail"], [76, 18, 152, 18], [76, 21, 152, 21, "checkUndefinedAnimationFail"], [76, 48, 152, 48], [76, 49, 153, 4, "animationName"], [76, 62, 153, 17], [76, 64, 154, 4, "isLayoutTransition"], [76, 82, 154, 22], [76, 86, 154, 26, "isCustomKeyframe"], [76, 102, 154, 42], [76, 106, 154, 46, "hasInitialValues"], [76, 122, 155, 2], [76, 123, 155, 3], [77, 4, 157, 2], [77, 8, 157, 6, "shouldFail"], [77, 18, 157, 16], [77, 20, 157, 18], [78, 6, 158, 4], [78, 13, 158, 11], [78, 17, 158, 15], [79, 4, 159, 2], [80, 4, 161, 2], [80, 8, 161, 6, "isCustomKeyframe"], [80, 24, 161, 22], [80, 26, 161, 24], [81, 6, 162, 4], [81, 10, 162, 10, "keyframeTimestamps"], [81, 28, 162, 28], [81, 31, 162, 31, "Object"], [81, 37, 162, 37], [81, 38, 162, 38, "keys"], [81, 42, 162, 42], [81, 43, 163, 7, "config"], [81, 49, 163, 13], [81, 50, 163, 31, "definitions"], [81, 61, 164, 4], [81, 62, 164, 5], [82, 6, 166, 4], [82, 10, 167, 6], [82, 12, 167, 8, "keyframeTimestamps"], [82, 30, 167, 26], [82, 31, 167, 27, "includes"], [82, 39, 167, 35], [82, 40, 167, 36], [82, 45, 167, 41], [82, 46, 167, 42], [82, 50, 167, 46, "keyframeTimestamps"], [82, 68, 167, 64], [82, 69, 167, 65, "includes"], [82, 77, 167, 73], [82, 78, 167, 74], [82, 82, 167, 78], [82, 83, 167, 79], [82, 84, 167, 80], [82, 86, 168, 6], [83, 8, 169, 6, "logger"], [83, 22, 169, 12], [83, 23, 169, 13, "warn"], [83, 27, 169, 17], [83, 28, 170, 8], [83, 235, 171, 6], [83, 236, 171, 7], [84, 6, 172, 4], [85, 4, 173, 2], [86, 4, 175, 2], [86, 8, 175, 8, "animationConfig"], [86, 23, 175, 23], [86, 26, 175, 26], [86, 30, 175, 26, "getProcessedConfig"], [86, 64, 175, 44], [86, 66, 176, 4, "animationName"], [86, 79, 176, 17], [86, 81, 177, 4, "animationType"], [86, 94, 177, 17], [86, 96, 178, 4, "config"], [86, 102, 179, 2], [86, 103, 179, 3], [87, 4, 181, 2], [87, 11, 181, 9, "animationConfig"], [87, 26, 181, 24], [88, 2, 182, 0], [89, 2, 184, 7], [89, 11, 184, 16, "startWebLayoutAnimation"], [89, 34, 184, 39, "startWebLayoutAnimation"], [89, 35, 187, 2, "props"], [89, 40, 187, 57], [89, 42, 188, 2, "element"], [89, 49, 188, 32], [89, 51, 189, 2, "animationType"], [89, 64, 189, 36], [89, 66, 190, 2, "transitionData"], [89, 80, 190, 33], [89, 82, 191, 2], [90, 4, 192, 2], [90, 8, 192, 8, "animationConfig"], [90, 23, 192, 23], [90, 26, 192, 26, "tryGetAnimationConfig"], [90, 47, 192, 47], [90, 48, 192, 48, "props"], [90, 53, 192, 53], [90, 55, 192, 55, "animationType"], [90, 68, 192, 68], [90, 69, 192, 69], [91, 4, 194, 2], [91, 8, 194, 2, "maybeModifyStyleForKeyframe"], [91, 51, 194, 29], [91, 53, 194, 30, "element"], [91, 60, 194, 37], [91, 62, 194, 39, "props"], [91, 67, 194, 44], [91, 68, 194, 45, "entering"], [91, 76, 194, 69], [91, 77, 194, 70], [92, 4, 196, 2], [92, 8, 196, 7, "animationConfig"], [92, 23, 196, 22], [92, 25, 196, 24, "animationName"], [92, 38, 196, 37], [92, 42, 196, 60, "Animations"], [92, 60, 196, 70], [92, 62, 196, 72], [93, 6, 197, 4, "maybeReportOverwrittenProperties"], [93, 38, 197, 36], [93, 39, 198, 6, "Animations"], [93, 57, 198, 16], [93, 58, 198, 17, "animationConfig"], [93, 73, 198, 32], [93, 75, 198, 34, "animationName"], [93, 88, 198, 47], [93, 89, 198, 66], [93, 90, 198, 67, "style"], [93, 95, 198, 72], [93, 97, 199, 6, "element"], [93, 104, 199, 13], [93, 105, 199, 14, "style"], [93, 110, 200, 4], [93, 111, 200, 5], [94, 4, 201, 2], [95, 4, 203, 2], [95, 8, 203, 6, "animationConfig"], [95, 23, 203, 21], [95, 25, 203, 23], [96, 6, 204, 4, "chooseAction"], [96, 18, 204, 16], [96, 19, 205, 6, "animationType"], [96, 32, 205, 19], [96, 34, 206, 6, "animationConfig"], [96, 49, 206, 21], [96, 51, 207, 6, "element"], [96, 58, 207, 13], [96, 60, 208, 6, "transitionData"], [96, 74, 209, 4], [96, 75, 209, 5], [97, 4, 210, 2], [97, 5, 210, 3], [97, 11, 210, 9], [98, 6, 211, 4], [98, 10, 211, 4, "makeElementVisible"], [98, 44, 211, 22], [98, 46, 211, 23, "element"], [98, 53, 211, 30], [98, 55, 211, 32], [98, 56, 211, 33], [98, 57, 211, 34], [99, 4, 212, 2], [100, 2, 213, 0], [101, 2, 215, 7], [101, 11, 215, 16, "tryActivateLayoutTransition"], [101, 38, 215, 43, "tryActivateLayoutTransition"], [101, 39, 218, 2, "props"], [101, 44, 218, 57], [101, 46, 219, 2, "element"], [101, 53, 219, 32], [101, 55, 220, 2, "snapshot"], [101, 63, 220, 19], [101, 65, 221, 2], [102, 4, 222, 2], [102, 8, 222, 6], [102, 9, 222, 7, "props"], [102, 14, 222, 12], [102, 15, 222, 13, "layout"], [102, 21, 222, 19], [102, 23, 222, 21], [103, 6, 223, 4], [104, 4, 224, 2], [105, 4, 226, 2], [105, 8, 226, 8, "rect"], [105, 12, 226, 12], [105, 15, 226, 15, "element"], [105, 22, 226, 22], [105, 23, 226, 23, "getBoundingClientRect"], [105, 44, 226, 44], [105, 45, 226, 45], [105, 46, 226, 46], [106, 4, 228, 2], [106, 8, 228, 6], [106, 12, 228, 6, "areDOMRectsEqual"], [106, 38, 228, 22], [106, 40, 228, 23, "rect"], [106, 44, 228, 27], [106, 46, 228, 29, "snapshot"], [106, 54, 228, 37], [106, 55, 228, 38], [106, 57, 228, 40], [107, 6, 229, 4], [108, 4, 230, 2], [109, 4, 232, 2], [109, 8, 232, 8, "enteringAnimation"], [109, 25, 232, 25], [109, 28, 232, 29, "props"], [109, 33, 232, 34], [109, 34, 232, 35, "layout"], [109, 40, 232, 41], [109, 41, 232, 59, "enteringV"], [109, 50, 232, 68], [109, 52, 233, 6, "presetName"], [109, 62, 233, 16], [110, 4, 234, 2], [110, 8, 234, 8, "exitingAnimation"], [110, 24, 234, 24], [110, 27, 234, 28, "props"], [110, 32, 234, 33], [110, 33, 234, 34, "layout"], [110, 39, 234, 40], [110, 40, 234, 58, "exitingV"], [110, 48, 234, 66], [110, 50, 234, 68, "presetName"], [110, 60, 234, 78], [111, 4, 236, 2], [111, 8, 236, 8, "deltaX"], [111, 14, 236, 14], [111, 17, 236, 17], [111, 18, 236, 18, "snapshot"], [111, 26, 236, 26], [111, 27, 236, 27, "width"], [111, 32, 236, 32], [111, 35, 236, 35, "rect"], [111, 39, 236, 39], [111, 40, 236, 40, "width"], [111, 45, 236, 45], [111, 49, 236, 49], [111, 50, 236, 50], [112, 4, 237, 2], [112, 8, 237, 8, "deltaY"], [112, 14, 237, 14], [112, 17, 237, 17], [112, 18, 237, 18, "snapshot"], [112, 26, 237, 26], [112, 27, 237, 27, "height"], [112, 33, 237, 33], [112, 36, 237, 36, "rect"], [112, 40, 237, 40], [112, 41, 237, 41, "height"], [112, 47, 237, 47], [112, 51, 237, 51], [112, 52, 237, 52], [113, 4, 238, 2], [113, 8, 238, 8, "transitionData"], [113, 22, 238, 38], [113, 25, 238, 41], [114, 6, 239, 4, "translateX"], [114, 16, 239, 14], [114, 18, 239, 16, "snapshot"], [114, 26, 239, 24], [114, 27, 239, 25, "x"], [114, 28, 239, 26], [114, 31, 239, 29, "rect"], [114, 35, 239, 33], [114, 36, 239, 34, "x"], [114, 37, 239, 35], [114, 40, 239, 38, "deltaX"], [114, 46, 239, 44], [115, 6, 240, 4, "translateY"], [115, 16, 240, 14], [115, 18, 240, 16, "snapshot"], [115, 26, 240, 24], [115, 27, 240, 25, "y"], [115, 28, 240, 26], [115, 31, 240, 29, "rect"], [115, 35, 240, 33], [115, 36, 240, 34, "y"], [115, 37, 240, 35], [115, 40, 240, 38, "deltaY"], [115, 46, 240, 44], [116, 6, 241, 4, "scaleX"], [116, 12, 241, 10], [116, 14, 241, 12, "snapshot"], [116, 22, 241, 20], [116, 23, 241, 21, "width"], [116, 28, 241, 26], [116, 31, 241, 29, "rect"], [116, 35, 241, 33], [116, 36, 241, 34, "width"], [116, 41, 241, 39], [117, 6, 242, 4, "scaleY"], [117, 12, 242, 10], [117, 14, 242, 12, "snapshot"], [117, 22, 242, 20], [117, 23, 242, 21, "height"], [117, 29, 242, 27], [117, 32, 242, 30, "rect"], [117, 36, 242, 34], [117, 37, 242, 35, "height"], [117, 43, 242, 41], [118, 6, 243, 4, "reversed"], [118, 14, 243, 12], [118, 16, 243, 14], [118, 21, 243, 19], [119, 6, 243, 21], [120, 6, 244, 4, "easingX"], [120, 13, 244, 11], [120, 15, 245, 7, "props"], [120, 20, 245, 12], [120, 21, 245, 13, "layout"], [120, 27, 245, 19], [120, 28, 245, 37, "easingXV"], [120, 36, 245, 45], [120, 39, 245, 48, "EasingNameSymbol"], [120, 63, 245, 64], [120, 64, 245, 65], [120, 68, 245, 69], [120, 74, 245, 75], [121, 6, 246, 4, "easingY"], [121, 13, 246, 11], [121, 15, 247, 7, "props"], [121, 20, 247, 12], [121, 21, 247, 13, "layout"], [121, 27, 247, 19], [121, 28, 247, 37, "easingYV"], [121, 36, 247, 45], [121, 39, 247, 48, "EasingNameSymbol"], [121, 63, 247, 64], [121, 64, 247, 65], [121, 68, 247, 69], [121, 74, 247, 75], [122, 6, 248, 4, "entering"], [122, 14, 248, 12], [122, 16, 248, 14, "enteringAnimation"], [122, 33, 248, 31], [123, 6, 249, 4, "exiting"], [123, 13, 249, 11], [123, 15, 249, 13, "exitingAnimation"], [124, 4, 250, 2], [124, 5, 250, 3], [125, 4, 252, 2, "startWebLayoutAnimation"], [125, 27, 252, 25], [125, 28, 253, 4, "props"], [125, 33, 253, 9], [125, 35, 254, 4, "element"], [125, 42, 254, 11], [125, 44, 255, 4, "LayoutAnimationType"], [125, 76, 255, 23], [125, 77, 255, 24, "LAYOUT"], [125, 83, 255, 30], [125, 85, 256, 4, "transitionData"], [125, 99, 257, 2], [125, 100, 257, 3], [126, 2, 258, 0], [127, 0, 258, 1], [127, 3]], "functionMap": {"names": ["<global>", "chooseConfig", "checkUndefinedAnimationFail", "maybeReportOverwrittenProperties", "Array.from.filter$argument_0", "chooseAction", "tryGetAnimationConfig", "startWebLayoutAnimation", "tryActivateLayoutTransition"], "mappings": "AAA;ACkC;CDc;AEE;CFe;AGE;qDCW;kCDC;CHc;AKE;CLkB;AME;CNkE;OOE;CP6B;OQE;CR2C"}}, "type": "js/module"}]}