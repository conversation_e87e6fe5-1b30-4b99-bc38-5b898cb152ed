{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 87}, "end": {"line": 4, "column": 47, "index": 134}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createJSWorkletsModule = createJSWorkletsModule;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _errors = require(_dependencyMap[3], \"../../errors\");\n  function createJSWorkletsModule() {\n    return new JSWorklets();\n  }\n  var JSWorklets = /*#__PURE__*/function () {\n    function JSWorklets() {\n      (0, _classCallCheck2.default)(this, JSWorklets);\n    }\n    return (0, _createClass2.default)(JSWorklets, [{\n      key: \"makeShareableClone\",\n      value: function makeShareableClone() {\n        throw new _errors.ReanimatedError('makeShareableClone should never be called in JSWorklets.');\n      }\n    }]);\n  }();\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createJSWorkletsModule"], [8, 32, 1, 13], [8, 35, 1, 13, "createJSWorkletsModule"], [8, 57, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 4, 0], [11, 6, 4, 0, "_errors"], [11, 13, 4, 0], [11, 16, 4, 0, "require"], [11, 23, 4, 0], [11, 24, 4, 0, "_dependencyMap"], [11, 38, 4, 0], [12, 2, 6, 7], [12, 11, 6, 16, "createJSWorkletsModule"], [12, 33, 6, 38, "createJSWorkletsModule"], [12, 34, 6, 38], [12, 36, 6, 58], [13, 4, 7, 2], [13, 11, 7, 9], [13, 15, 7, 13, "JSWorklets"], [13, 25, 7, 23], [13, 26, 7, 24], [13, 27, 7, 25], [14, 2, 8, 0], [15, 2, 8, 1], [15, 6, 10, 6, "JSWorklets"], [15, 16, 10, 16], [16, 4, 10, 16], [16, 13, 10, 16, "JSWorklets"], [16, 24, 10, 16], [17, 6, 10, 16], [17, 10, 10, 16, "_classCallCheck2"], [17, 26, 10, 16], [17, 27, 10, 16, "default"], [17, 34, 10, 16], [17, 42, 10, 16, "JSWorklets"], [17, 52, 10, 16], [18, 4, 10, 16], [19, 4, 10, 16], [19, 15, 10, 16, "_createClass2"], [19, 28, 10, 16], [19, 29, 10, 16, "default"], [19, 36, 10, 16], [19, 38, 10, 16, "JSWorklets"], [19, 48, 10, 16], [20, 6, 10, 16, "key"], [20, 9, 10, 16], [21, 6, 10, 16, "value"], [21, 11, 10, 16], [21, 13, 11, 2], [21, 22, 11, 2, "makeShareableClone"], [21, 40, 11, 20, "makeShareableClone"], [21, 41, 11, 20], [21, 43, 11, 43], [22, 8, 12, 4], [22, 14, 12, 10], [22, 18, 12, 14, "ReanimatedError"], [22, 41, 12, 29], [22, 42, 13, 6], [22, 100, 14, 4], [22, 101, 14, 5], [23, 6, 15, 2], [24, 4, 15, 3], [25, 2, 15, 3], [26, 0, 15, 3], [26, 3]], "functionMap": {"names": ["<global>", "createJSWorkletsModule", "JSWorklets", "makeShareableClone"], "mappings": "AAA;OCK;CDE;AEE;ECC;GDI;CFC"}}, "type": "js/module"}]}