{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /* eslint-disable no-var */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n});", "lineCount": 8, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 2, 2, 0, "Object"], [5, 8, 2, 0], [5, 9, 2, 0, "defineProperty"], [5, 23, 2, 0], [5, 24, 2, 0, "exports"], [5, 31, 2, 0], [6, 4, 2, 0, "value"], [6, 9, 2, 0], [7, 2, 2, 0], [8, 0, 2, 0], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}