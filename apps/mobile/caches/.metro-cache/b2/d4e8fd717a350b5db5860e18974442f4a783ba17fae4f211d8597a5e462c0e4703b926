{"dependencies": [{"name": "../Matrix2D", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 80, "index": 80}}], "key": "G8Mr0+BXo0ZsNarr0bCVxm3CyOk=", "exportNames": ["*"]}}, {"name": "./transform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 81}, "end": {"line": 2, "column": 36, "index": 117}}], "key": "R53IbS00pxjfjWrxlPaZF2IlW4E=", "exportNames": ["*"]}}, {"name": "./transformToRn", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 118}, "end": {"line": 3, "column": 70, "index": 188}}], "key": "Snmf2O7jv7Ta5OtYNqgSaq3VVvM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = extractTransform;\n  exports.extractTransformSvgView = extractTransformSvgView;\n  exports.props2transform = props2transform;\n  exports.transformToMatrix = transformToMatrix;\n  exports.transformsArrayToProps = transformsArrayToProps;\n  var _Matrix2D = require(_dependencyMap[0], \"../Matrix2D\");\n  var _transform = require(_dependencyMap[1], \"./transform\");\n  var _transformToRn = require(_dependencyMap[2], \"./transformToRn\");\n  function appendTransformProps(props) {\n    const {\n      x,\n      y,\n      originX,\n      originY,\n      scaleX,\n      scaleY,\n      rotation,\n      skewX,\n      skewY\n    } = props;\n    (0, _Matrix2D.appendTransform)(x + originX, y + originY, scaleX, scaleY, rotation, skewX, skewY, originX, originY);\n  }\n  function universal2axis(universal, axisX, axisY, defaultValue) {\n    let x;\n    let y;\n    if (typeof universal === 'number') {\n      x = y = universal;\n    } else if (typeof universal === 'string') {\n      const coords = universal.split(/\\s*,\\s*/);\n      if (coords.length === 2) {\n        x = +coords[0];\n        y = +coords[1];\n      } else if (coords.length === 1) {\n        x = y = +coords[0];\n      }\n    } else if (Array.isArray(universal)) {\n      if (universal.length === 2) {\n        x = +universal[0];\n        y = +universal[1];\n      } else if (universal.length === 1) {\n        x = y = +universal[0];\n      }\n    }\n    axisX = +axisX;\n    if (!isNaN(axisX)) {\n      x = axisX;\n    }\n    axisY = +axisY;\n    if (!isNaN(axisY)) {\n      y = axisY;\n    }\n    return [x || defaultValue || 0, y || defaultValue || 0];\n  }\n  function transformsArrayToProps(transformObjectsArray) {\n    const props = {};\n    transformObjectsArray === null || transformObjectsArray === void 0 || transformObjectsArray.forEach(transformObject => {\n      const keys = Object.keys(transformObject);\n      if (keys.length !== 1) {\n        console.error('You must specify exactly one property per transform object.');\n      }\n      const key = keys[0];\n      const value = transformObject[key];\n      // @ts-expect-error FIXME\n      props[key] = value;\n    });\n    return props;\n  }\n  function props2transform(props) {\n    if (!props) {\n      return null;\n    }\n    const {\n      rotation,\n      translate,\n      translateX,\n      translateY,\n      origin,\n      originX,\n      originY,\n      scale,\n      scaleX,\n      scaleY,\n      skew,\n      skewX,\n      skewY,\n      x,\n      y\n    } = props;\n    if (rotation == null && translate == null && translateX == null && translateY == null && origin == null && originX == null && originY == null && scale == null && scaleX == null && scaleY == null && skew == null && skewX == null && skewY == null && x == null && y == null) {\n      return null;\n    }\n    if (Array.isArray(x) || Array.isArray(y)) {\n      console.warn('Passing SvgLengthList to x or y attribute where SvgLength expected');\n    }\n    const tr = universal2axis(translate, translateX || (Array.isArray(x) ? x[0] : x), translateY || (Array.isArray(y) ? y[0] : y));\n    const or = universal2axis(origin, originX, originY);\n    const sc = universal2axis(scale, scaleX, scaleY, 1);\n    const sk = universal2axis(skew, skewX, skewY);\n    return {\n      rotation: rotation == null ? 0 : +rotation || 0,\n      originX: or[0],\n      originY: or[1],\n      scaleX: sc[0],\n      scaleY: sc[1],\n      skewX: sk[0],\n      skewY: sk[1],\n      x: tr[0],\n      y: tr[1]\n    };\n  }\n  function transformToMatrix(props, transform) {\n    if (!props && !transform) {\n      return null;\n    }\n    (0, _Matrix2D.reset)();\n    props && appendTransformProps(props);\n    if (transform) {\n      if (Array.isArray(transform)) {\n        if (typeof transform[0] === 'number') {\n          const columnMatrix = transform;\n          (0, _Matrix2D.append)(columnMatrix[0], columnMatrix[1], columnMatrix[2], columnMatrix[3], columnMatrix[4], columnMatrix[5]);\n        } else {\n          const transformProps = props2transform(\n          // @ts-expect-error FIXME\n          transformsArrayToProps(transform));\n          transformProps && appendTransformProps(transformProps);\n        }\n      } else if (typeof transform === 'string') {\n        try {\n          const t = (0, _transform.parse)(transform);\n          (0, _Matrix2D.append)(t[0], t[3], t[1], t[4], t[2], t[5]);\n        } catch (e) {\n          console.error(e);\n        }\n      } else {\n        // @ts-expect-error FIXME\n        const transformProps = props2transform(transform);\n        transformProps && appendTransformProps(transformProps);\n      }\n    }\n    return (0, _Matrix2D.toArray)();\n  }\n  function extractTransform(props) {\n    if (Array.isArray(props) && typeof props[0] === 'number') {\n      return props;\n    }\n    if (typeof props === 'string') {\n      try {\n        const t = (0, _transform.parse)(props);\n        return [t[0], t[3], t[1], t[4], t[2], t[5]];\n      } catch (e) {\n        console.error(e);\n        return _Matrix2D.identity;\n      }\n    }\n    // this type is not correct since props can be of type TransformsStyle['transform'] too\n    // but it satisfies TS and should not produce any type errors\n    const transformProps = props;\n    return transformToMatrix(props2transform(transformProps), transformProps === null || transformProps === void 0 ? void 0 : transformProps.transform);\n  }\n  function extractTransformSvgView(props) {\n    if (typeof props.transform === 'string') {\n      return (0, _transformToRn.parse)(props.transform);\n    }\n    return props.transform;\n  }\n});", "lineCount": 171, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_Matrix2D"], [10, 15, 1, 0], [10, 18, 1, 0, "require"], [10, 25, 1, 0], [10, 26, 1, 0, "_dependencyMap"], [10, 40, 1, 0], [11, 2, 2, 0], [11, 6, 2, 0, "_transform"], [11, 16, 2, 0], [11, 19, 2, 0, "require"], [11, 26, 2, 0], [11, 27, 2, 0, "_dependencyMap"], [11, 41, 2, 0], [12, 2, 3, 0], [12, 6, 3, 0, "_transformToRn"], [12, 20, 3, 0], [12, 23, 3, 0, "require"], [12, 30, 3, 0], [12, 31, 3, 0, "_dependencyMap"], [12, 45, 3, 0], [13, 2, 4, 0], [13, 11, 4, 9, "appendTransformProps"], [13, 31, 4, 29, "appendTransformProps"], [13, 32, 4, 30, "props"], [13, 37, 4, 35], [13, 39, 4, 37], [14, 4, 5, 2], [14, 10, 5, 8], [15, 6, 6, 4, "x"], [15, 7, 6, 5], [16, 6, 7, 4, "y"], [16, 7, 7, 5], [17, 6, 8, 4, "originX"], [17, 13, 8, 11], [18, 6, 9, 4, "originY"], [18, 13, 9, 11], [19, 6, 10, 4, "scaleX"], [19, 12, 10, 10], [20, 6, 11, 4, "scaleY"], [20, 12, 11, 10], [21, 6, 12, 4, "rotation"], [21, 14, 12, 12], [22, 6, 13, 4, "skewX"], [22, 11, 13, 9], [23, 6, 14, 4, "skewY"], [24, 4, 15, 2], [24, 5, 15, 3], [24, 8, 15, 6, "props"], [24, 13, 15, 11], [25, 4, 16, 2], [25, 8, 16, 2, "appendTransform"], [25, 33, 16, 17], [25, 35, 16, 18, "x"], [25, 36, 16, 19], [25, 39, 16, 22, "originX"], [25, 46, 16, 29], [25, 48, 16, 31, "y"], [25, 49, 16, 32], [25, 52, 16, 35, "originY"], [25, 59, 16, 42], [25, 61, 16, 44, "scaleX"], [25, 67, 16, 50], [25, 69, 16, 52, "scaleY"], [25, 75, 16, 58], [25, 77, 16, 60, "rotation"], [25, 85, 16, 68], [25, 87, 16, 70, "skewX"], [25, 92, 16, 75], [25, 94, 16, 77, "skewY"], [25, 99, 16, 82], [25, 101, 16, 84, "originX"], [25, 108, 16, 91], [25, 110, 16, 93, "originY"], [25, 117, 16, 100], [25, 118, 16, 101], [26, 2, 17, 0], [27, 2, 18, 0], [27, 11, 18, 9, "universal2axis"], [27, 25, 18, 23, "universal2axis"], [27, 26, 18, 24, "universal"], [27, 35, 18, 33], [27, 37, 18, 35, "axisX"], [27, 42, 18, 40], [27, 44, 18, 42, "axisY"], [27, 49, 18, 47], [27, 51, 18, 49, "defaultValue"], [27, 63, 18, 61], [27, 65, 18, 63], [28, 4, 19, 2], [28, 8, 19, 6, "x"], [28, 9, 19, 7], [29, 4, 20, 2], [29, 8, 20, 6, "y"], [29, 9, 20, 7], [30, 4, 21, 2], [30, 8, 21, 6], [30, 15, 21, 13, "universal"], [30, 24, 21, 22], [30, 29, 21, 27], [30, 37, 21, 35], [30, 39, 21, 37], [31, 6, 22, 4, "x"], [31, 7, 22, 5], [31, 10, 22, 8, "y"], [31, 11, 22, 9], [31, 14, 22, 12, "universal"], [31, 23, 22, 21], [32, 4, 23, 2], [32, 5, 23, 3], [32, 11, 23, 9], [32, 15, 23, 13], [32, 22, 23, 20, "universal"], [32, 31, 23, 29], [32, 36, 23, 34], [32, 44, 23, 42], [32, 46, 23, 44], [33, 6, 24, 4], [33, 12, 24, 10, "coords"], [33, 18, 24, 16], [33, 21, 24, 19, "universal"], [33, 30, 24, 28], [33, 31, 24, 29, "split"], [33, 36, 24, 34], [33, 37, 24, 35], [33, 46, 24, 44], [33, 47, 24, 45], [34, 6, 25, 4], [34, 10, 25, 8, "coords"], [34, 16, 25, 14], [34, 17, 25, 15, "length"], [34, 23, 25, 21], [34, 28, 25, 26], [34, 29, 25, 27], [34, 31, 25, 29], [35, 8, 26, 6, "x"], [35, 9, 26, 7], [35, 12, 26, 10], [35, 13, 26, 11, "coords"], [35, 19, 26, 17], [35, 20, 26, 18], [35, 21, 26, 19], [35, 22, 26, 20], [36, 8, 27, 6, "y"], [36, 9, 27, 7], [36, 12, 27, 10], [36, 13, 27, 11, "coords"], [36, 19, 27, 17], [36, 20, 27, 18], [36, 21, 27, 19], [36, 22, 27, 20], [37, 6, 28, 4], [37, 7, 28, 5], [37, 13, 28, 11], [37, 17, 28, 15, "coords"], [37, 23, 28, 21], [37, 24, 28, 22, "length"], [37, 30, 28, 28], [37, 35, 28, 33], [37, 36, 28, 34], [37, 38, 28, 36], [38, 8, 29, 6, "x"], [38, 9, 29, 7], [38, 12, 29, 10, "y"], [38, 13, 29, 11], [38, 16, 29, 14], [38, 17, 29, 15, "coords"], [38, 23, 29, 21], [38, 24, 29, 22], [38, 25, 29, 23], [38, 26, 29, 24], [39, 6, 30, 4], [40, 4, 31, 2], [40, 5, 31, 3], [40, 11, 31, 9], [40, 15, 31, 13, "Array"], [40, 20, 31, 18], [40, 21, 31, 19, "isArray"], [40, 28, 31, 26], [40, 29, 31, 27, "universal"], [40, 38, 31, 36], [40, 39, 31, 37], [40, 41, 31, 39], [41, 6, 32, 4], [41, 10, 32, 8, "universal"], [41, 19, 32, 17], [41, 20, 32, 18, "length"], [41, 26, 32, 24], [41, 31, 32, 29], [41, 32, 32, 30], [41, 34, 32, 32], [42, 8, 33, 6, "x"], [42, 9, 33, 7], [42, 12, 33, 10], [42, 13, 33, 11, "universal"], [42, 22, 33, 20], [42, 23, 33, 21], [42, 24, 33, 22], [42, 25, 33, 23], [43, 8, 34, 6, "y"], [43, 9, 34, 7], [43, 12, 34, 10], [43, 13, 34, 11, "universal"], [43, 22, 34, 20], [43, 23, 34, 21], [43, 24, 34, 22], [43, 25, 34, 23], [44, 6, 35, 4], [44, 7, 35, 5], [44, 13, 35, 11], [44, 17, 35, 15, "universal"], [44, 26, 35, 24], [44, 27, 35, 25, "length"], [44, 33, 35, 31], [44, 38, 35, 36], [44, 39, 35, 37], [44, 41, 35, 39], [45, 8, 36, 6, "x"], [45, 9, 36, 7], [45, 12, 36, 10, "y"], [45, 13, 36, 11], [45, 16, 36, 14], [45, 17, 36, 15, "universal"], [45, 26, 36, 24], [45, 27, 36, 25], [45, 28, 36, 26], [45, 29, 36, 27], [46, 6, 37, 4], [47, 4, 38, 2], [48, 4, 39, 2, "axisX"], [48, 9, 39, 7], [48, 12, 39, 10], [48, 13, 39, 11, "axisX"], [48, 18, 39, 16], [49, 4, 40, 2], [49, 8, 40, 6], [49, 9, 40, 7, "isNaN"], [49, 14, 40, 12], [49, 15, 40, 13, "axisX"], [49, 20, 40, 18], [49, 21, 40, 19], [49, 23, 40, 21], [50, 6, 41, 4, "x"], [50, 7, 41, 5], [50, 10, 41, 8, "axisX"], [50, 15, 41, 13], [51, 4, 42, 2], [52, 4, 43, 2, "axisY"], [52, 9, 43, 7], [52, 12, 43, 10], [52, 13, 43, 11, "axisY"], [52, 18, 43, 16], [53, 4, 44, 2], [53, 8, 44, 6], [53, 9, 44, 7, "isNaN"], [53, 14, 44, 12], [53, 15, 44, 13, "axisY"], [53, 20, 44, 18], [53, 21, 44, 19], [53, 23, 44, 21], [54, 6, 45, 4, "y"], [54, 7, 45, 5], [54, 10, 45, 8, "axisY"], [54, 15, 45, 13], [55, 4, 46, 2], [56, 4, 47, 2], [56, 11, 47, 9], [56, 12, 47, 10, "x"], [56, 13, 47, 11], [56, 17, 47, 15, "defaultValue"], [56, 29, 47, 27], [56, 33, 47, 31], [56, 34, 47, 32], [56, 36, 47, 34, "y"], [56, 37, 47, 35], [56, 41, 47, 39, "defaultValue"], [56, 53, 47, 51], [56, 57, 47, 55], [56, 58, 47, 56], [56, 59, 47, 57], [57, 2, 48, 0], [58, 2, 49, 7], [58, 11, 49, 16, "transformsArrayToProps"], [58, 33, 49, 38, "transformsArrayToProps"], [58, 34, 49, 39, "transformObjectsArray"], [58, 55, 49, 60], [58, 57, 49, 62], [59, 4, 50, 2], [59, 10, 50, 8, "props"], [59, 15, 50, 13], [59, 18, 50, 16], [59, 19, 50, 17], [59, 20, 50, 18], [60, 4, 51, 2, "transformObjectsArray"], [60, 25, 51, 23], [60, 30, 51, 28], [60, 34, 51, 32], [60, 38, 51, 36, "transformObjectsArray"], [60, 59, 51, 57], [60, 64, 51, 62], [60, 69, 51, 67], [60, 70, 51, 68], [60, 74, 51, 72, "transformObjectsArray"], [60, 95, 51, 93], [60, 96, 51, 94, "for<PERSON>ach"], [60, 103, 51, 101], [60, 104, 51, 102, "transformObject"], [60, 119, 51, 117], [60, 123, 51, 121], [61, 6, 52, 4], [61, 12, 52, 10, "keys"], [61, 16, 52, 14], [61, 19, 52, 17, "Object"], [61, 25, 52, 23], [61, 26, 52, 24, "keys"], [61, 30, 52, 28], [61, 31, 52, 29, "transformObject"], [61, 46, 52, 44], [61, 47, 52, 45], [62, 6, 53, 4], [62, 10, 53, 8, "keys"], [62, 14, 53, 12], [62, 15, 53, 13, "length"], [62, 21, 53, 19], [62, 26, 53, 24], [62, 27, 53, 25], [62, 29, 53, 27], [63, 8, 54, 6, "console"], [63, 15, 54, 13], [63, 16, 54, 14, "error"], [63, 21, 54, 19], [63, 22, 54, 20], [63, 83, 54, 81], [63, 84, 54, 82], [64, 6, 55, 4], [65, 6, 56, 4], [65, 12, 56, 10, "key"], [65, 15, 56, 13], [65, 18, 56, 16, "keys"], [65, 22, 56, 20], [65, 23, 56, 21], [65, 24, 56, 22], [65, 25, 56, 23], [66, 6, 57, 4], [66, 12, 57, 10, "value"], [66, 17, 57, 15], [66, 20, 57, 18, "transformObject"], [66, 35, 57, 33], [66, 36, 57, 34, "key"], [66, 39, 57, 37], [66, 40, 57, 38], [67, 6, 58, 4], [68, 6, 59, 4, "props"], [68, 11, 59, 9], [68, 12, 59, 10, "key"], [68, 15, 59, 13], [68, 16, 59, 14], [68, 19, 59, 17, "value"], [68, 24, 59, 22], [69, 4, 60, 2], [69, 5, 60, 3], [69, 6, 60, 4], [70, 4, 61, 2], [70, 11, 61, 9, "props"], [70, 16, 61, 14], [71, 2, 62, 0], [72, 2, 63, 7], [72, 11, 63, 16, "props2transform"], [72, 26, 63, 31, "props2transform"], [72, 27, 63, 32, "props"], [72, 32, 63, 37], [72, 34, 63, 39], [73, 4, 64, 2], [73, 8, 64, 6], [73, 9, 64, 7, "props"], [73, 14, 64, 12], [73, 16, 64, 14], [74, 6, 65, 4], [74, 13, 65, 11], [74, 17, 65, 15], [75, 4, 66, 2], [76, 4, 67, 2], [76, 10, 67, 8], [77, 6, 68, 4, "rotation"], [77, 14, 68, 12], [78, 6, 69, 4, "translate"], [78, 15, 69, 13], [79, 6, 70, 4, "translateX"], [79, 16, 70, 14], [80, 6, 71, 4, "translateY"], [80, 16, 71, 14], [81, 6, 72, 4, "origin"], [81, 12, 72, 10], [82, 6, 73, 4, "originX"], [82, 13, 73, 11], [83, 6, 74, 4, "originY"], [83, 13, 74, 11], [84, 6, 75, 4, "scale"], [84, 11, 75, 9], [85, 6, 76, 4, "scaleX"], [85, 12, 76, 10], [86, 6, 77, 4, "scaleY"], [86, 12, 77, 10], [87, 6, 78, 4, "skew"], [87, 10, 78, 8], [88, 6, 79, 4, "skewX"], [88, 11, 79, 9], [89, 6, 80, 4, "skewY"], [89, 11, 80, 9], [90, 6, 81, 4, "x"], [90, 7, 81, 5], [91, 6, 82, 4, "y"], [92, 4, 83, 2], [92, 5, 83, 3], [92, 8, 83, 6, "props"], [92, 13, 83, 11], [93, 4, 84, 2], [93, 8, 84, 6, "rotation"], [93, 16, 84, 14], [93, 20, 84, 18], [93, 24, 84, 22], [93, 28, 84, 26, "translate"], [93, 37, 84, 35], [93, 41, 84, 39], [93, 45, 84, 43], [93, 49, 84, 47, "translateX"], [93, 59, 84, 57], [93, 63, 84, 61], [93, 67, 84, 65], [93, 71, 84, 69, "translateY"], [93, 81, 84, 79], [93, 85, 84, 83], [93, 89, 84, 87], [93, 93, 84, 91, "origin"], [93, 99, 84, 97], [93, 103, 84, 101], [93, 107, 84, 105], [93, 111, 84, 109, "originX"], [93, 118, 84, 116], [93, 122, 84, 120], [93, 126, 84, 124], [93, 130, 84, 128, "originY"], [93, 137, 84, 135], [93, 141, 84, 139], [93, 145, 84, 143], [93, 149, 84, 147, "scale"], [93, 154, 84, 152], [93, 158, 84, 156], [93, 162, 84, 160], [93, 166, 84, 164, "scaleX"], [93, 172, 84, 170], [93, 176, 84, 174], [93, 180, 84, 178], [93, 184, 84, 182, "scaleY"], [93, 190, 84, 188], [93, 194, 84, 192], [93, 198, 84, 196], [93, 202, 84, 200, "skew"], [93, 206, 84, 204], [93, 210, 84, 208], [93, 214, 84, 212], [93, 218, 84, 216, "skewX"], [93, 223, 84, 221], [93, 227, 84, 225], [93, 231, 84, 229], [93, 235, 84, 233, "skewY"], [93, 240, 84, 238], [93, 244, 84, 242], [93, 248, 84, 246], [93, 252, 84, 250, "x"], [93, 253, 84, 251], [93, 257, 84, 255], [93, 261, 84, 259], [93, 265, 84, 263, "y"], [93, 266, 84, 264], [93, 270, 84, 268], [93, 274, 84, 272], [93, 276, 84, 274], [94, 6, 85, 4], [94, 13, 85, 11], [94, 17, 85, 15], [95, 4, 86, 2], [96, 4, 87, 2], [96, 8, 87, 6, "Array"], [96, 13, 87, 11], [96, 14, 87, 12, "isArray"], [96, 21, 87, 19], [96, 22, 87, 20, "x"], [96, 23, 87, 21], [96, 24, 87, 22], [96, 28, 87, 26, "Array"], [96, 33, 87, 31], [96, 34, 87, 32, "isArray"], [96, 41, 87, 39], [96, 42, 87, 40, "y"], [96, 43, 87, 41], [96, 44, 87, 42], [96, 46, 87, 44], [97, 6, 88, 4, "console"], [97, 13, 88, 11], [97, 14, 88, 12, "warn"], [97, 18, 88, 16], [97, 19, 88, 17], [97, 87, 88, 85], [97, 88, 88, 86], [98, 4, 89, 2], [99, 4, 90, 2], [99, 10, 90, 8, "tr"], [99, 12, 90, 10], [99, 15, 90, 13, "universal2axis"], [99, 29, 90, 27], [99, 30, 90, 28, "translate"], [99, 39, 90, 37], [99, 41, 90, 39, "translateX"], [99, 51, 90, 49], [99, 56, 90, 54, "Array"], [99, 61, 90, 59], [99, 62, 90, 60, "isArray"], [99, 69, 90, 67], [99, 70, 90, 68, "x"], [99, 71, 90, 69], [99, 72, 90, 70], [99, 75, 90, 73, "x"], [99, 76, 90, 74], [99, 77, 90, 75], [99, 78, 90, 76], [99, 79, 90, 77], [99, 82, 90, 80, "x"], [99, 83, 90, 81], [99, 84, 90, 82], [99, 86, 90, 84, "translateY"], [99, 96, 90, 94], [99, 101, 90, 99, "Array"], [99, 106, 90, 104], [99, 107, 90, 105, "isArray"], [99, 114, 90, 112], [99, 115, 90, 113, "y"], [99, 116, 90, 114], [99, 117, 90, 115], [99, 120, 90, 118, "y"], [99, 121, 90, 119], [99, 122, 90, 120], [99, 123, 90, 121], [99, 124, 90, 122], [99, 127, 90, 125, "y"], [99, 128, 90, 126], [99, 129, 90, 127], [99, 130, 90, 128], [100, 4, 91, 2], [100, 10, 91, 8, "or"], [100, 12, 91, 10], [100, 15, 91, 13, "universal2axis"], [100, 29, 91, 27], [100, 30, 91, 28, "origin"], [100, 36, 91, 34], [100, 38, 91, 36, "originX"], [100, 45, 91, 43], [100, 47, 91, 45, "originY"], [100, 54, 91, 52], [100, 55, 91, 53], [101, 4, 92, 2], [101, 10, 92, 8, "sc"], [101, 12, 92, 10], [101, 15, 92, 13, "universal2axis"], [101, 29, 92, 27], [101, 30, 92, 28, "scale"], [101, 35, 92, 33], [101, 37, 92, 35, "scaleX"], [101, 43, 92, 41], [101, 45, 92, 43, "scaleY"], [101, 51, 92, 49], [101, 53, 92, 51], [101, 54, 92, 52], [101, 55, 92, 53], [102, 4, 93, 2], [102, 10, 93, 8, "sk"], [102, 12, 93, 10], [102, 15, 93, 13, "universal2axis"], [102, 29, 93, 27], [102, 30, 93, 28, "skew"], [102, 34, 93, 32], [102, 36, 93, 34, "skewX"], [102, 41, 93, 39], [102, 43, 93, 41, "skewY"], [102, 48, 93, 46], [102, 49, 93, 47], [103, 4, 94, 2], [103, 11, 94, 9], [104, 6, 95, 4, "rotation"], [104, 14, 95, 12], [104, 16, 95, 14, "rotation"], [104, 24, 95, 22], [104, 28, 95, 26], [104, 32, 95, 30], [104, 35, 95, 33], [104, 36, 95, 34], [104, 39, 95, 37], [104, 40, 95, 38, "rotation"], [104, 48, 95, 46], [104, 52, 95, 50], [104, 53, 95, 51], [105, 6, 96, 4, "originX"], [105, 13, 96, 11], [105, 15, 96, 13, "or"], [105, 17, 96, 15], [105, 18, 96, 16], [105, 19, 96, 17], [105, 20, 96, 18], [106, 6, 97, 4, "originY"], [106, 13, 97, 11], [106, 15, 97, 13, "or"], [106, 17, 97, 15], [106, 18, 97, 16], [106, 19, 97, 17], [106, 20, 97, 18], [107, 6, 98, 4, "scaleX"], [107, 12, 98, 10], [107, 14, 98, 12, "sc"], [107, 16, 98, 14], [107, 17, 98, 15], [107, 18, 98, 16], [107, 19, 98, 17], [108, 6, 99, 4, "scaleY"], [108, 12, 99, 10], [108, 14, 99, 12, "sc"], [108, 16, 99, 14], [108, 17, 99, 15], [108, 18, 99, 16], [108, 19, 99, 17], [109, 6, 100, 4, "skewX"], [109, 11, 100, 9], [109, 13, 100, 11, "sk"], [109, 15, 100, 13], [109, 16, 100, 14], [109, 17, 100, 15], [109, 18, 100, 16], [110, 6, 101, 4, "skewY"], [110, 11, 101, 9], [110, 13, 101, 11, "sk"], [110, 15, 101, 13], [110, 16, 101, 14], [110, 17, 101, 15], [110, 18, 101, 16], [111, 6, 102, 4, "x"], [111, 7, 102, 5], [111, 9, 102, 7, "tr"], [111, 11, 102, 9], [111, 12, 102, 10], [111, 13, 102, 11], [111, 14, 102, 12], [112, 6, 103, 4, "y"], [112, 7, 103, 5], [112, 9, 103, 7, "tr"], [112, 11, 103, 9], [112, 12, 103, 10], [112, 13, 103, 11], [113, 4, 104, 2], [113, 5, 104, 3], [114, 2, 105, 0], [115, 2, 106, 7], [115, 11, 106, 16, "transformToMatrix"], [115, 28, 106, 33, "transformToMatrix"], [115, 29, 106, 34, "props"], [115, 34, 106, 39], [115, 36, 106, 41, "transform"], [115, 45, 106, 50], [115, 47, 106, 52], [116, 4, 107, 2], [116, 8, 107, 6], [116, 9, 107, 7, "props"], [116, 14, 107, 12], [116, 18, 107, 16], [116, 19, 107, 17, "transform"], [116, 28, 107, 26], [116, 30, 107, 28], [117, 6, 108, 4], [117, 13, 108, 11], [117, 17, 108, 15], [118, 4, 109, 2], [119, 4, 110, 2], [119, 8, 110, 2, "reset"], [119, 23, 110, 7], [119, 25, 110, 8], [119, 26, 110, 9], [120, 4, 111, 2, "props"], [120, 9, 111, 7], [120, 13, 111, 11, "appendTransformProps"], [120, 33, 111, 31], [120, 34, 111, 32, "props"], [120, 39, 111, 37], [120, 40, 111, 38], [121, 4, 112, 2], [121, 8, 112, 6, "transform"], [121, 17, 112, 15], [121, 19, 112, 17], [122, 6, 113, 4], [122, 10, 113, 8, "Array"], [122, 15, 113, 13], [122, 16, 113, 14, "isArray"], [122, 23, 113, 21], [122, 24, 113, 22, "transform"], [122, 33, 113, 31], [122, 34, 113, 32], [122, 36, 113, 34], [123, 8, 114, 6], [123, 12, 114, 10], [123, 19, 114, 17, "transform"], [123, 28, 114, 26], [123, 29, 114, 27], [123, 30, 114, 28], [123, 31, 114, 29], [123, 36, 114, 34], [123, 44, 114, 42], [123, 46, 114, 44], [124, 10, 115, 8], [124, 16, 115, 14, "columnMatrix"], [124, 28, 115, 26], [124, 31, 115, 29, "transform"], [124, 40, 115, 38], [125, 10, 116, 8], [125, 14, 116, 8, "append"], [125, 30, 116, 14], [125, 32, 116, 15, "columnMatrix"], [125, 44, 116, 27], [125, 45, 116, 28], [125, 46, 116, 29], [125, 47, 116, 30], [125, 49, 116, 32, "columnMatrix"], [125, 61, 116, 44], [125, 62, 116, 45], [125, 63, 116, 46], [125, 64, 116, 47], [125, 66, 116, 49, "columnMatrix"], [125, 78, 116, 61], [125, 79, 116, 62], [125, 80, 116, 63], [125, 81, 116, 64], [125, 83, 116, 66, "columnMatrix"], [125, 95, 116, 78], [125, 96, 116, 79], [125, 97, 116, 80], [125, 98, 116, 81], [125, 100, 116, 83, "columnMatrix"], [125, 112, 116, 95], [125, 113, 116, 96], [125, 114, 116, 97], [125, 115, 116, 98], [125, 117, 116, 100, "columnMatrix"], [125, 129, 116, 112], [125, 130, 116, 113], [125, 131, 116, 114], [125, 132, 116, 115], [125, 133, 116, 116], [126, 8, 117, 6], [126, 9, 117, 7], [126, 15, 117, 13], [127, 10, 118, 8], [127, 16, 118, 14, "transformProps"], [127, 30, 118, 28], [127, 33, 118, 31, "props2transform"], [127, 48, 118, 46], [128, 10, 119, 8], [129, 10, 120, 8, "transformsArrayToProps"], [129, 32, 120, 30], [129, 33, 120, 31, "transform"], [129, 42, 120, 40], [129, 43, 120, 41], [129, 44, 120, 42], [130, 10, 121, 8, "transformProps"], [130, 24, 121, 22], [130, 28, 121, 26, "appendTransformProps"], [130, 48, 121, 46], [130, 49, 121, 47, "transformProps"], [130, 63, 121, 61], [130, 64, 121, 62], [131, 8, 122, 6], [132, 6, 123, 4], [132, 7, 123, 5], [132, 13, 123, 11], [132, 17, 123, 15], [132, 24, 123, 22, "transform"], [132, 33, 123, 31], [132, 38, 123, 36], [132, 46, 123, 44], [132, 48, 123, 46], [133, 8, 124, 6], [133, 12, 124, 10], [134, 10, 125, 8], [134, 16, 125, 14, "t"], [134, 17, 125, 15], [134, 20, 125, 18], [134, 24, 125, 18, "parse"], [134, 40, 125, 23], [134, 42, 125, 24, "transform"], [134, 51, 125, 33], [134, 52, 125, 34], [135, 10, 126, 8], [135, 14, 126, 8, "append"], [135, 30, 126, 14], [135, 32, 126, 15, "t"], [135, 33, 126, 16], [135, 34, 126, 17], [135, 35, 126, 18], [135, 36, 126, 19], [135, 38, 126, 21, "t"], [135, 39, 126, 22], [135, 40, 126, 23], [135, 41, 126, 24], [135, 42, 126, 25], [135, 44, 126, 27, "t"], [135, 45, 126, 28], [135, 46, 126, 29], [135, 47, 126, 30], [135, 48, 126, 31], [135, 50, 126, 33, "t"], [135, 51, 126, 34], [135, 52, 126, 35], [135, 53, 126, 36], [135, 54, 126, 37], [135, 56, 126, 39, "t"], [135, 57, 126, 40], [135, 58, 126, 41], [135, 59, 126, 42], [135, 60, 126, 43], [135, 62, 126, 45, "t"], [135, 63, 126, 46], [135, 64, 126, 47], [135, 65, 126, 48], [135, 66, 126, 49], [135, 67, 126, 50], [136, 8, 127, 6], [136, 9, 127, 7], [136, 10, 127, 8], [136, 17, 127, 15, "e"], [136, 18, 127, 16], [136, 20, 127, 18], [137, 10, 128, 8, "console"], [137, 17, 128, 15], [137, 18, 128, 16, "error"], [137, 23, 128, 21], [137, 24, 128, 22, "e"], [137, 25, 128, 23], [137, 26, 128, 24], [138, 8, 129, 6], [139, 6, 130, 4], [139, 7, 130, 5], [139, 13, 130, 11], [140, 8, 131, 6], [141, 8, 132, 6], [141, 14, 132, 12, "transformProps"], [141, 28, 132, 26], [141, 31, 132, 29, "props2transform"], [141, 46, 132, 44], [141, 47, 132, 45, "transform"], [141, 56, 132, 54], [141, 57, 132, 55], [142, 8, 133, 6, "transformProps"], [142, 22, 133, 20], [142, 26, 133, 24, "appendTransformProps"], [142, 46, 133, 44], [142, 47, 133, 45, "transformProps"], [142, 61, 133, 59], [142, 62, 133, 60], [143, 6, 134, 4], [144, 4, 135, 2], [145, 4, 136, 2], [145, 11, 136, 9], [145, 15, 136, 9, "toArray"], [145, 32, 136, 16], [145, 34, 136, 17], [145, 35, 136, 18], [146, 2, 137, 0], [147, 2, 138, 15], [147, 11, 138, 24, "extractTransform"], [147, 27, 138, 40, "extractTransform"], [147, 28, 138, 41, "props"], [147, 33, 138, 46], [147, 35, 138, 48], [148, 4, 139, 2], [148, 8, 139, 6, "Array"], [148, 13, 139, 11], [148, 14, 139, 12, "isArray"], [148, 21, 139, 19], [148, 22, 139, 20, "props"], [148, 27, 139, 25], [148, 28, 139, 26], [148, 32, 139, 30], [148, 39, 139, 37, "props"], [148, 44, 139, 42], [148, 45, 139, 43], [148, 46, 139, 44], [148, 47, 139, 45], [148, 52, 139, 50], [148, 60, 139, 58], [148, 62, 139, 60], [149, 6, 140, 4], [149, 13, 140, 11, "props"], [149, 18, 140, 16], [150, 4, 141, 2], [151, 4, 142, 2], [151, 8, 142, 6], [151, 15, 142, 13, "props"], [151, 20, 142, 18], [151, 25, 142, 23], [151, 33, 142, 31], [151, 35, 142, 33], [152, 6, 143, 4], [152, 10, 143, 8], [153, 8, 144, 6], [153, 14, 144, 12, "t"], [153, 15, 144, 13], [153, 18, 144, 16], [153, 22, 144, 16, "parse"], [153, 38, 144, 21], [153, 40, 144, 22, "props"], [153, 45, 144, 27], [153, 46, 144, 28], [154, 8, 145, 6], [154, 15, 145, 13], [154, 16, 145, 14, "t"], [154, 17, 145, 15], [154, 18, 145, 16], [154, 19, 145, 17], [154, 20, 145, 18], [154, 22, 145, 20, "t"], [154, 23, 145, 21], [154, 24, 145, 22], [154, 25, 145, 23], [154, 26, 145, 24], [154, 28, 145, 26, "t"], [154, 29, 145, 27], [154, 30, 145, 28], [154, 31, 145, 29], [154, 32, 145, 30], [154, 34, 145, 32, "t"], [154, 35, 145, 33], [154, 36, 145, 34], [154, 37, 145, 35], [154, 38, 145, 36], [154, 40, 145, 38, "t"], [154, 41, 145, 39], [154, 42, 145, 40], [154, 43, 145, 41], [154, 44, 145, 42], [154, 46, 145, 44, "t"], [154, 47, 145, 45], [154, 48, 145, 46], [154, 49, 145, 47], [154, 50, 145, 48], [154, 51, 145, 49], [155, 6, 146, 4], [155, 7, 146, 5], [155, 8, 146, 6], [155, 15, 146, 13, "e"], [155, 16, 146, 14], [155, 18, 146, 16], [156, 8, 147, 6, "console"], [156, 15, 147, 13], [156, 16, 147, 14, "error"], [156, 21, 147, 19], [156, 22, 147, 20, "e"], [156, 23, 147, 21], [156, 24, 147, 22], [157, 8, 148, 6], [157, 15, 148, 13, "identity"], [157, 33, 148, 21], [158, 6, 149, 4], [159, 4, 150, 2], [160, 4, 151, 2], [161, 4, 152, 2], [162, 4, 153, 2], [162, 10, 153, 8, "transformProps"], [162, 24, 153, 22], [162, 27, 153, 25, "props"], [162, 32, 153, 30], [163, 4, 154, 2], [163, 11, 154, 9, "transformToMatrix"], [163, 28, 154, 26], [163, 29, 154, 27, "props2transform"], [163, 44, 154, 42], [163, 45, 154, 43, "transformProps"], [163, 59, 154, 57], [163, 60, 154, 58], [163, 62, 154, 60, "transformProps"], [163, 76, 154, 74], [163, 81, 154, 79], [163, 85, 154, 83], [163, 89, 154, 87, "transformProps"], [163, 103, 154, 101], [163, 108, 154, 106], [163, 113, 154, 111], [163, 114, 154, 112], [163, 117, 154, 115], [163, 122, 154, 120], [163, 123, 154, 121], [163, 126, 154, 124, "transformProps"], [163, 140, 154, 138], [163, 141, 154, 139, "transform"], [163, 150, 154, 148], [163, 151, 154, 149], [164, 2, 155, 0], [165, 2, 156, 7], [165, 11, 156, 16, "extractTransformSvgView"], [165, 34, 156, 39, "extractTransformSvgView"], [165, 35, 156, 40, "props"], [165, 40, 156, 45], [165, 42, 156, 47], [166, 4, 157, 2], [166, 8, 157, 6], [166, 15, 157, 13, "props"], [166, 20, 157, 18], [166, 21, 157, 19, "transform"], [166, 30, 157, 28], [166, 35, 157, 33], [166, 43, 157, 41], [166, 45, 157, 43], [167, 6, 158, 4], [167, 13, 158, 11], [167, 17, 158, 11, "parseTransformSvgToRnStyle"], [167, 37, 158, 37], [167, 39, 158, 38, "props"], [167, 44, 158, 43], [167, 45, 158, 44, "transform"], [167, 54, 158, 53], [167, 55, 158, 54], [168, 4, 159, 2], [169, 4, 160, 2], [169, 11, 160, 9, "props"], [169, 16, 160, 14], [169, 17, 160, 15, "transform"], [169, 26, 160, 24], [170, 2, 161, 0], [171, 0, 161, 1], [171, 3]], "functionMap": {"names": ["<global>", "appendTransformProps", "universal2axis", "transformsArrayToProps", "transformObjectsArray.forEach$argument_0", "props2transform", "transformToMatrix", "extractTransform", "extractTransformSvgView"], "mappings": "AAA;ACG;CDa;AEC;CF8B;OGC;sGCE;GDS;CHE;OKC;CL0C;OMC;CN+B;eOC;CPiB;OQC;CRK"}}, "type": "js/module"}]}