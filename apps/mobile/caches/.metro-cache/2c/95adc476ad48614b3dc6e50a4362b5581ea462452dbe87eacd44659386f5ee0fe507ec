{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const TruckElectric = exports.default = (0, _createLucideIcon.default)(\"TruckElectric\", [[\"path\", {\n    d: \"M14 19V7a2 2 0 0 0-2-2H9\",\n    key: \"15peso\"\n  }], [\"path\", {\n    d: \"M15 19H9\",\n    key: \"18q6dt\"\n  }], [\"path\", {\n    d: \"M19 19h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.62L18.3 9.38a1 1 0 0 0-.78-.38H14\",\n    key: \"1dkp3j\"\n  }], [\"path\", {\n    d: \"M2 13v5a1 1 0 0 0 1 1h2\",\n    key: \"pkmmzz\"\n  }], [\"path\", {\n    d: \"M4 3 2.15 5.15a.495.495 0 0 0 .35.86h2.15a.47.47 0 0 1 .35.86L3 9.02\",\n    key: \"1n26pd\"\n  }], [\"circle\", {\n    cx: \"17\",\n    cy: \"19\",\n    r: \"2\",\n    key: \"1nxcgd\"\n  }], [\"circle\", {\n    cx: \"7\",\n    cy: \"19\",\n    r: \"2\",\n    key: \"gzo7y7\"\n  }]]);\n});", "lineCount": 41, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "TruckElectric"], [15, 21, 10, 19], [15, 24, 10, 19, "exports"], [15, 31, 10, 19], [15, 32, 10, 19, "default"], [15, 39, 10, 19], [15, 42, 10, 22], [15, 46, 10, 22, "createLucideIcon"], [15, 71, 10, 38], [15, 73, 10, 39], [15, 88, 10, 54], [15, 90, 10, 56], [15, 91, 11, 2], [15, 92, 11, 3], [15, 98, 11, 9], [15, 100, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 33, 11, 42], [17, 4, 11, 44, "key"], [17, 7, 11, 47], [17, 9, 11, 49], [18, 2, 11, 58], [18, 3, 11, 59], [18, 4, 11, 60], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 17, 12, 26], [20, 4, 12, 28, "key"], [20, 7, 12, 31], [20, 9, 12, 33], [21, 2, 12, 42], [21, 3, 12, 43], [21, 4, 12, 44], [21, 6, 13, 2], [21, 7, 14, 4], [21, 13, 14, 10], [21, 15, 15, 4], [22, 4, 16, 6, "d"], [22, 5, 16, 7], [22, 7, 16, 9], [22, 86, 16, 88], [23, 4, 17, 6, "key"], [23, 7, 17, 9], [23, 9, 17, 11], [24, 2, 18, 4], [24, 3, 18, 5], [24, 4, 19, 3], [24, 6, 20, 2], [24, 7, 20, 3], [24, 13, 20, 9], [24, 15, 20, 11], [25, 4, 20, 13, "d"], [25, 5, 20, 14], [25, 7, 20, 16], [25, 32, 20, 41], [26, 4, 20, 43, "key"], [26, 7, 20, 46], [26, 9, 20, 48], [27, 2, 20, 57], [27, 3, 20, 58], [27, 4, 20, 59], [27, 6, 21, 2], [27, 7, 22, 4], [27, 13, 22, 10], [27, 15, 23, 4], [28, 4, 23, 6, "d"], [28, 5, 23, 7], [28, 7, 23, 9], [28, 77, 23, 79], [29, 4, 23, 81, "key"], [29, 7, 23, 84], [29, 9, 23, 86], [30, 2, 23, 95], [30, 3, 23, 96], [30, 4, 24, 3], [30, 6, 25, 2], [30, 7, 25, 3], [30, 15, 25, 11], [30, 17, 25, 13], [31, 4, 25, 15, "cx"], [31, 6, 25, 17], [31, 8, 25, 19], [31, 12, 25, 23], [32, 4, 25, 25, "cy"], [32, 6, 25, 27], [32, 8, 25, 29], [32, 12, 25, 33], [33, 4, 25, 35, "r"], [33, 5, 25, 36], [33, 7, 25, 38], [33, 10, 25, 41], [34, 4, 25, 43, "key"], [34, 7, 25, 46], [34, 9, 25, 48], [35, 2, 25, 57], [35, 3, 25, 58], [35, 4, 25, 59], [35, 6, 26, 2], [35, 7, 26, 3], [35, 15, 26, 11], [35, 17, 26, 13], [36, 4, 26, 15, "cx"], [36, 6, 26, 17], [36, 8, 26, 19], [36, 11, 26, 22], [37, 4, 26, 24, "cy"], [37, 6, 26, 26], [37, 8, 26, 28], [37, 12, 26, 32], [38, 4, 26, 34, "r"], [38, 5, 26, 35], [38, 7, 26, 37], [38, 10, 26, 40], [39, 4, 26, 42, "key"], [39, 7, 26, 45], [39, 9, 26, 47], [40, 2, 26, 56], [40, 3, 26, 57], [40, 4, 26, 58], [40, 5, 27, 1], [40, 6, 27, 2], [41, 0, 27, 3], [41, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}