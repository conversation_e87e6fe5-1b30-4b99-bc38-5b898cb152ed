{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 80}, "end": {"line": 4, "column": 46, "index": 126}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 127}, "end": {"line": 5, "column": 46, "index": 173}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[1], \"./GestureHandler\"));\n  var _utils = require(_dependencyMap[2], \"./utils\");\n  /* eslint-disable eslint-comments/no-unlimited-disable */\n\n  /* eslint-disable */\n\n  class DiscreteGestureHandler extends _GestureHandler.default {\n    get isDiscrete() {\n      return true;\n    }\n    get shouldEnableGestureOnSetup() {\n      return true;\n    }\n    shouldFailUnderCustomCriteria({\n      x,\n      y,\n      deltaX,\n      deltaY\n    }, {\n      maxDeltaX,\n      maxDeltaY,\n      maxDistSq,\n      shouldCancelWhenOutside\n    }) {\n      if (shouldCancelWhenOutside) {\n        if (!this.isPointInView({\n          x,\n          y\n        })) {\n          return true;\n        }\n      }\n      return (0, _utils.TEST_MAX_IF_NOT_NAN)(Math.abs(deltaX), maxDeltaX) || (0, _utils.TEST_MAX_IF_NOT_NAN)(Math.abs(deltaY), maxDeltaY) || (0, _utils.TEST_MAX_IF_NOT_NAN)(Math.abs(deltaY * deltaY + deltaX * deltaX), maxDistSq);\n    }\n    transformNativeEvent({\n      center: {\n        x,\n        y\n      }\n    }) {\n      // @ts-ignore FIXME(TS)\n      const rect = this.view.getBoundingClientRect();\n      return {\n        absoluteX: x,\n        absoluteY: y,\n        x: x - rect.left,\n        y: y - rect.top\n      };\n    }\n    isGestureEnabledForEvent({\n      minPointers,\n      maxPointers,\n      maxDeltaX,\n      maxDeltaY,\n      maxDistSq,\n      shouldCancelWhenOutside\n    }, _recognizer, {\n      maxPointers: pointerLength,\n      center,\n      deltaX,\n      deltaY\n    }) {\n      const validPointerCount = pointerLength >= minPointers && pointerLength <= maxPointers;\n      if (this.shouldFailUnderCustomCriteria({\n        ...center,\n        deltaX,\n        deltaY\n      }, {\n        maxDeltaX,\n        maxDeltaY,\n        maxDistSq,\n        shouldCancelWhenOutside\n      }) ||\n      // A user probably won't land a multi-pointer tap on the first tick (so we cannot just cancel each time)\n      // but if the gesture is running and the user adds or subtracts another pointer then it should fail.\n      !validPointerCount && this.isGestureRunning) {\n        return {\n          failed: true\n        };\n      }\n      return {\n        success: validPointerCount\n      };\n    }\n  }\n  var _default = exports.default = DiscreteGestureHandler;\n});", "lineCount": 93, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_Gesture<PERSON><PERSON>ler"], [7, 21, 4, 0], [7, 24, 4, 0, "_interopRequireDefault"], [7, 46, 4, 0], [7, 47, 4, 0, "require"], [7, 54, 4, 0], [7, 55, 4, 0, "_dependencyMap"], [7, 69, 4, 0], [8, 2, 5, 0], [8, 6, 5, 0, "_utils"], [8, 12, 5, 0], [8, 15, 5, 0, "require"], [8, 22, 5, 0], [8, 23, 5, 0, "_dependencyMap"], [8, 37, 5, 0], [9, 2, 1, 0], [11, 2, 3, 0], [13, 2, 7, 0], [13, 8, 7, 6, "DiscreteGestureHandler"], [13, 30, 7, 28], [13, 39, 7, 37, "Gesture<PERSON>andler"], [13, 62, 7, 51], [13, 63, 7, 52], [14, 4, 8, 2], [14, 8, 8, 6, "isDiscrete"], [14, 18, 8, 16, "isDiscrete"], [14, 19, 8, 16], [14, 21, 8, 19], [15, 6, 9, 4], [15, 13, 9, 11], [15, 17, 9, 15], [16, 4, 10, 2], [17, 4, 12, 2], [17, 8, 12, 6, "shouldEnableGestureOnSetup"], [17, 34, 12, 32, "shouldEnableGestureOnSetup"], [17, 35, 12, 32], [17, 37, 12, 35], [18, 6, 13, 4], [18, 13, 13, 11], [18, 17, 13, 15], [19, 4, 14, 2], [20, 4, 16, 2, "shouldFailUnderCustomCriteria"], [20, 33, 16, 31, "shouldFailUnderCustomCriteria"], [20, 34, 16, 32], [21, 6, 17, 4, "x"], [21, 7, 17, 5], [22, 6, 18, 4, "y"], [22, 7, 18, 5], [23, 6, 19, 4, "deltaX"], [23, 12, 19, 10], [24, 6, 20, 4, "deltaY"], [25, 4, 21, 2], [25, 5, 21, 3], [25, 7, 21, 5], [26, 6, 22, 4, "maxDeltaX"], [26, 15, 22, 13], [27, 6, 23, 4, "maxDeltaY"], [27, 15, 23, 13], [28, 6, 24, 4, "maxDistSq"], [28, 15, 24, 13], [29, 6, 25, 4, "shouldCancelWhenOutside"], [30, 4, 26, 2], [30, 5, 26, 3], [30, 7, 26, 5], [31, 6, 27, 4], [31, 10, 27, 8, "shouldCancelWhenOutside"], [31, 33, 27, 31], [31, 35, 27, 33], [32, 8, 28, 6], [32, 12, 28, 10], [32, 13, 28, 11], [32, 17, 28, 15], [32, 18, 28, 16, "isPointInView"], [32, 31, 28, 29], [32, 32, 28, 30], [33, 10, 29, 8, "x"], [33, 11, 29, 9], [34, 10, 30, 8, "y"], [35, 8, 31, 6], [35, 9, 31, 7], [35, 10, 31, 8], [35, 12, 31, 10], [36, 10, 32, 8], [36, 17, 32, 15], [36, 21, 32, 19], [37, 8, 33, 6], [38, 6, 34, 4], [39, 6, 36, 4], [39, 13, 36, 11], [39, 17, 36, 11, "TEST_MAX_IF_NOT_NAN"], [39, 43, 36, 30], [39, 45, 36, 31, "Math"], [39, 49, 36, 35], [39, 50, 36, 36, "abs"], [39, 53, 36, 39], [39, 54, 36, 40, "deltaX"], [39, 60, 36, 46], [39, 61, 36, 47], [39, 63, 36, 49, "maxDeltaX"], [39, 72, 36, 58], [39, 73, 36, 59], [39, 77, 36, 63], [39, 81, 36, 63, "TEST_MAX_IF_NOT_NAN"], [39, 107, 36, 82], [39, 109, 36, 83, "Math"], [39, 113, 36, 87], [39, 114, 36, 88, "abs"], [39, 117, 36, 91], [39, 118, 36, 92, "deltaY"], [39, 124, 36, 98], [39, 125, 36, 99], [39, 127, 36, 101, "maxDeltaY"], [39, 136, 36, 110], [39, 137, 36, 111], [39, 141, 36, 115], [39, 145, 36, 115, "TEST_MAX_IF_NOT_NAN"], [39, 171, 36, 134], [39, 173, 36, 135, "Math"], [39, 177, 36, 139], [39, 178, 36, 140, "abs"], [39, 181, 36, 143], [39, 182, 36, 144, "deltaY"], [39, 188, 36, 150], [39, 191, 36, 153, "deltaY"], [39, 197, 36, 159], [39, 200, 36, 162, "deltaX"], [39, 206, 36, 168], [39, 209, 36, 171, "deltaX"], [39, 215, 36, 177], [39, 216, 36, 178], [39, 218, 36, 180, "maxDistSq"], [39, 227, 36, 189], [39, 228, 36, 190], [40, 4, 37, 2], [41, 4, 39, 2, "transformNativeEvent"], [41, 24, 39, 22, "transformNativeEvent"], [41, 25, 39, 23], [42, 6, 40, 4, "center"], [42, 12, 40, 10], [42, 14, 40, 12], [43, 8, 41, 6, "x"], [43, 9, 41, 7], [44, 8, 42, 6, "y"], [45, 6, 43, 4], [46, 4, 44, 2], [46, 5, 44, 3], [46, 7, 44, 5], [47, 6, 45, 4], [48, 6, 46, 4], [48, 12, 46, 10, "rect"], [48, 16, 46, 14], [48, 19, 46, 17], [48, 23, 46, 21], [48, 24, 46, 22, "view"], [48, 28, 46, 26], [48, 29, 46, 27, "getBoundingClientRect"], [48, 50, 46, 48], [48, 51, 46, 49], [48, 52, 46, 50], [49, 6, 47, 4], [49, 13, 47, 11], [50, 8, 48, 6, "absoluteX"], [50, 17, 48, 15], [50, 19, 48, 17, "x"], [50, 20, 48, 18], [51, 8, 49, 6, "absoluteY"], [51, 17, 49, 15], [51, 19, 49, 17, "y"], [51, 20, 49, 18], [52, 8, 50, 6, "x"], [52, 9, 50, 7], [52, 11, 50, 9, "x"], [52, 12, 50, 10], [52, 15, 50, 13, "rect"], [52, 19, 50, 17], [52, 20, 50, 18, "left"], [52, 24, 50, 22], [53, 8, 51, 6, "y"], [53, 9, 51, 7], [53, 11, 51, 9, "y"], [53, 12, 51, 10], [53, 15, 51, 13, "rect"], [53, 19, 51, 17], [53, 20, 51, 18, "top"], [54, 6, 52, 4], [54, 7, 52, 5], [55, 4, 53, 2], [56, 4, 55, 2, "isGestureEnabledForEvent"], [56, 28, 55, 26, "isGestureEnabledForEvent"], [56, 29, 55, 27], [57, 6, 56, 4, "minPointers"], [57, 17, 56, 15], [58, 6, 57, 4, "maxPointers"], [58, 17, 57, 15], [59, 6, 58, 4, "maxDeltaX"], [59, 15, 58, 13], [60, 6, 59, 4, "maxDeltaY"], [60, 15, 59, 13], [61, 6, 60, 4, "maxDistSq"], [61, 15, 60, 13], [62, 6, 61, 4, "shouldCancelWhenOutside"], [63, 4, 62, 2], [63, 5, 62, 3], [63, 7, 62, 5, "_recognizer"], [63, 18, 62, 16], [63, 20, 62, 18], [64, 6, 63, 4, "maxPointers"], [64, 17, 63, 15], [64, 19, 63, 17, "pointer<PERSON><PERSON><PERSON>"], [64, 32, 63, 30], [65, 6, 64, 4, "center"], [65, 12, 64, 10], [66, 6, 65, 4, "deltaX"], [66, 12, 65, 10], [67, 6, 66, 4, "deltaY"], [68, 4, 67, 2], [68, 5, 67, 3], [68, 7, 67, 5], [69, 6, 68, 4], [69, 12, 68, 10, "validPointerCount"], [69, 29, 68, 27], [69, 32, 68, 30, "pointer<PERSON><PERSON><PERSON>"], [69, 45, 68, 43], [69, 49, 68, 47, "minPointers"], [69, 60, 68, 58], [69, 64, 68, 62, "pointer<PERSON><PERSON><PERSON>"], [69, 77, 68, 75], [69, 81, 68, 79, "maxPointers"], [69, 92, 68, 90], [70, 6, 70, 4], [70, 10, 70, 8], [70, 14, 70, 12], [70, 15, 70, 13, "shouldFailUnderCustomCriteria"], [70, 44, 70, 42], [70, 45, 70, 43], [71, 8, 70, 45], [71, 11, 70, 48, "center"], [71, 17, 70, 54], [72, 8, 71, 6, "deltaX"], [72, 14, 71, 12], [73, 8, 72, 6, "deltaY"], [74, 6, 73, 4], [74, 7, 73, 5], [74, 9, 73, 7], [75, 8, 74, 6, "maxDeltaX"], [75, 17, 74, 15], [76, 8, 75, 6, "maxDeltaY"], [76, 17, 75, 15], [77, 8, 76, 6, "maxDistSq"], [77, 17, 76, 15], [78, 8, 77, 6, "shouldCancelWhenOutside"], [79, 6, 78, 4], [79, 7, 78, 5], [79, 8, 78, 6], [80, 6, 78, 10], [81, 6, 79, 4], [82, 6, 80, 4], [82, 7, 80, 5, "validPointerCount"], [82, 24, 80, 22], [82, 28, 80, 26], [82, 32, 80, 30], [82, 33, 80, 31, "isGestureRunning"], [82, 49, 80, 47], [82, 51, 80, 49], [83, 8, 81, 6], [83, 15, 81, 13], [84, 10, 82, 8, "failed"], [84, 16, 82, 14], [84, 18, 82, 16], [85, 8, 83, 6], [85, 9, 83, 7], [86, 6, 84, 4], [87, 6, 86, 4], [87, 13, 86, 11], [88, 8, 87, 6, "success"], [88, 15, 87, 13], [88, 17, 87, 15, "validPointerCount"], [89, 6, 88, 4], [89, 7, 88, 5], [90, 4, 89, 2], [91, 2, 91, 0], [92, 2, 91, 1], [92, 6, 91, 1, "_default"], [92, 14, 91, 1], [92, 17, 91, 1, "exports"], [92, 24, 91, 1], [92, 25, 91, 1, "default"], [92, 32, 91, 1], [92, 35, 93, 15, "DiscreteGestureHandler"], [92, 57, 93, 37], [93, 0, 93, 37], [93, 3]], "functionMap": {"names": ["<global>", "DiscreteGestureHandler", "get__isDiscrete", "get__shouldEnableGestureOnSetup", "shouldFailUnderCustomCriteria", "transformNativeEvent", "isGestureEnabledForEvent"], "mappings": "AAA;ACM;ECC;GDE;EEE;GFE;EGE;GHqB;EIE;GJc;EKE;GLkC;CDE"}}, "type": "js/module"}]}