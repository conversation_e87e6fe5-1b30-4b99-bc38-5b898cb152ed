{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.convertInt32ColorToRGBA = convertInt32ColorToRGBA;\n  function convertInt32ColorToRGBA(color) {\n    const r = color >> 16 & 255;\n    const g = color >> 8 & 255;\n    const b = color & 255;\n    const a = (color >> 24 & 255) / 255;\n    const alpha = a.toFixed(2);\n    return `rgba(${r},${g},${b},${alpha})`;\n  }\n});", "lineCount": 14, "map": [[6, 2, 1, 7], [6, 11, 1, 16, "convertInt32ColorToRGBA"], [6, 34, 1, 39, "convertInt32ColorToRGBA"], [6, 35, 1, 40, "color"], [6, 40, 1, 45], [6, 42, 1, 47], [7, 4, 2, 2], [7, 10, 2, 8, "r"], [7, 11, 2, 9], [7, 14, 2, 12, "color"], [7, 19, 2, 17], [7, 23, 2, 21], [7, 25, 2, 23], [7, 28, 2, 26], [7, 31, 2, 29], [8, 4, 3, 2], [8, 10, 3, 8, "g"], [8, 11, 3, 9], [8, 14, 3, 12, "color"], [8, 19, 3, 17], [8, 23, 3, 21], [8, 24, 3, 22], [8, 27, 3, 25], [8, 30, 3, 28], [9, 4, 4, 2], [9, 10, 4, 8, "b"], [9, 11, 4, 9], [9, 14, 4, 12, "color"], [9, 19, 4, 17], [9, 22, 4, 20], [9, 25, 4, 23], [10, 4, 5, 2], [10, 10, 5, 8, "a"], [10, 11, 5, 9], [10, 14, 5, 12], [10, 15, 5, 13, "color"], [10, 20, 5, 18], [10, 24, 5, 22], [10, 26, 5, 24], [10, 29, 5, 27], [10, 32, 5, 30], [10, 36, 5, 34], [10, 39, 5, 37], [11, 4, 6, 2], [11, 10, 6, 8, "alpha"], [11, 15, 6, 13], [11, 18, 6, 16, "a"], [11, 19, 6, 17], [11, 20, 6, 18, "toFixed"], [11, 27, 6, 25], [11, 28, 6, 26], [11, 29, 6, 27], [11, 30, 6, 28], [12, 4, 7, 2], [12, 11, 7, 9], [12, 19, 7, 17, "r"], [12, 20, 7, 18], [12, 24, 7, 22, "g"], [12, 25, 7, 23], [12, 29, 7, 27, "b"], [12, 30, 7, 28], [12, 34, 7, 32, "alpha"], [12, 39, 7, 37], [12, 42, 7, 40], [13, 2, 8, 0], [14, 0, 8, 1], [14, 3]], "functionMap": {"names": ["<global>", "convertInt32ColorToRGBA"], "mappings": "AAA,OC;CDO"}}, "type": "js/module"}]}