{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  const pick = (obj, ...keys) => keys.flat().filter(key => Object.prototype.hasOwnProperty.call(obj, key)).reduce((acc, key) => {\n    acc[key] = obj[key];\n    return acc;\n  }, {});\n  const omit = (obj, ...keysToOmit) => {\n    const keysToOmitSet = new Set(keysToOmit.flat());\n    return Object.getOwnPropertyNames(obj).filter(key => !keysToOmitSet.has(key)).reduce((acc, key) => {\n      acc[key] = obj[key];\n      return acc;\n    }, {});\n  };\n  module.exports = {\n    pick,\n    omit\n  };\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 8, 1, 6, "pick"], [2, 12, 1, 10], [2, 15, 1, 13, "pick"], [2, 16, 1, 14, "obj"], [2, 19, 1, 17], [2, 21, 1, 19], [2, 24, 1, 22, "keys"], [2, 28, 1, 26], [2, 33, 2, 2, "keys"], [2, 37, 2, 6], [2, 38, 3, 5, "flat"], [2, 42, 3, 9], [2, 43, 3, 10], [2, 44, 3, 11], [2, 45, 4, 5, "filter"], [2, 51, 4, 11], [2, 52, 4, 12, "key"], [2, 55, 4, 15], [2, 59, 4, 19, "Object"], [2, 65, 4, 25], [2, 66, 4, 26, "prototype"], [2, 75, 4, 35], [2, 76, 4, 36, "hasOwnProperty"], [2, 90, 4, 50], [2, 91, 4, 51, "call"], [2, 95, 4, 55], [2, 96, 4, 56, "obj"], [2, 99, 4, 59], [2, 101, 4, 61, "key"], [2, 104, 4, 64], [2, 105, 4, 65], [2, 106, 4, 66], [2, 107, 5, 5, "reduce"], [2, 113, 5, 11], [2, 114, 5, 12], [2, 115, 5, 13, "acc"], [2, 118, 5, 16], [2, 120, 5, 18, "key"], [2, 123, 5, 21], [2, 128, 5, 26], [3, 4, 6, 6, "acc"], [3, 7, 6, 9], [3, 8, 6, 10, "key"], [3, 11, 6, 13], [3, 12, 6, 14], [3, 15, 6, 17, "obj"], [3, 18, 6, 20], [3, 19, 6, 21, "key"], [3, 22, 6, 24], [3, 23, 6, 25], [4, 4, 7, 6], [4, 11, 7, 13, "acc"], [4, 14, 7, 16], [5, 2, 8, 4], [5, 3, 8, 5], [5, 5, 8, 7], [5, 6, 8, 8], [5, 7, 8, 9], [5, 8, 8, 10], [6, 2, 10, 0], [6, 8, 10, 6, "omit"], [6, 12, 10, 10], [6, 15, 10, 13, "omit"], [6, 16, 10, 14, "obj"], [6, 19, 10, 17], [6, 21, 10, 19], [6, 24, 10, 22, "keysToOmit"], [6, 34, 10, 32], [6, 39, 10, 37], [7, 4, 11, 2], [7, 10, 11, 8, "keysToOmitSet"], [7, 23, 11, 21], [7, 26, 11, 24], [7, 30, 11, 28, "Set"], [7, 33, 11, 31], [7, 34, 11, 32, "keysToOmit"], [7, 44, 11, 42], [7, 45, 11, 43, "flat"], [7, 49, 11, 47], [7, 50, 11, 48], [7, 51, 11, 49], [7, 52, 11, 50], [8, 4, 12, 2], [8, 11, 12, 9, "Object"], [8, 17, 12, 15], [8, 18, 12, 16, "getOwnPropertyNames"], [8, 37, 12, 35], [8, 38, 12, 36, "obj"], [8, 41, 12, 39], [8, 42, 12, 40], [8, 43, 13, 5, "filter"], [8, 49, 13, 11], [8, 50, 13, 12, "key"], [8, 53, 13, 15], [8, 57, 13, 19], [8, 58, 13, 20, "keysToOmitSet"], [8, 71, 13, 33], [8, 72, 13, 34, "has"], [8, 75, 13, 37], [8, 76, 13, 38, "key"], [8, 79, 13, 41], [8, 80, 13, 42], [8, 81, 13, 43], [8, 82, 14, 5, "reduce"], [8, 88, 14, 11], [8, 89, 14, 12], [8, 90, 14, 13, "acc"], [8, 93, 14, 16], [8, 95, 14, 18, "key"], [8, 98, 14, 21], [8, 103, 14, 26], [9, 6, 15, 6, "acc"], [9, 9, 15, 9], [9, 10, 15, 10, "key"], [9, 13, 15, 13], [9, 14, 15, 14], [9, 17, 15, 17, "obj"], [9, 20, 15, 20], [9, 21, 15, 21, "key"], [9, 24, 15, 24], [9, 25, 15, 25], [10, 6, 16, 6], [10, 13, 16, 13, "acc"], [10, 16, 16, 16], [11, 4, 17, 4], [11, 5, 17, 5], [11, 7, 17, 7], [11, 8, 17, 8], [11, 9, 17, 9], [11, 10, 17, 10], [12, 2, 18, 0], [12, 3, 18, 1], [13, 2, 20, 0, "module"], [13, 8, 20, 6], [13, 9, 20, 7, "exports"], [13, 16, 20, 14], [13, 19, 20, 17], [14, 4, 20, 19, "pick"], [14, 8, 20, 23], [15, 4, 20, 25, "omit"], [16, 2, 20, 30], [16, 3, 20, 31], [17, 0, 20, 32], [17, 3]], "functionMap": {"names": ["<global>", "pick", "keys.flat.filter$argument_0", "keys.flat.filter.reduce$argument_0", "omit", "Object.getOwnPropertyNames.filter$argument_0", "Object.getOwnPropertyNames.filter.reduce$argument_0"], "mappings": "AAA,aC;YCG,qDD;YEC;KFG,KD;aIE;YCG,8BD;YEC;KFG;CJC"}}, "type": "js/module"}]}