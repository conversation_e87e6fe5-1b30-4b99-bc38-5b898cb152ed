{"dependencies": [{"name": "../Utilities/PolyfillFunctions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 66}}], "key": "HYclPKQCLeyfRj4pG+IgrzgyEZ8=", "exportNames": ["*"]}}, {"name": "../promiseRejectionTrackingOptions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 51}}], "key": "fq4OXTCoEpSJiVilIFLLePAgHCY=", "exportNames": ["*"]}}, {"name": "../Promise", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 37, "column": 34}, "end": {"line": 37, "column": 55}}], "key": "byI1ycN8bXMgqXIHoBO//bFMcD4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _require = require(_dependencyMap[0], \"../Utilities/PolyfillFunctions\"),\n    polyfillGlobal = _require.polyfillGlobal;\n  if (global?.HermesInternal?.hasPromise?.()) {\n    var HermesPromise = global.Promise;\n    if (__DEV__) {\n      if (typeof HermesPromise !== 'function') {\n        console.error('HermesPromise does not exist');\n      }\n      global.HermesInternal?.enablePromiseRejectionTracker?.(require(_dependencyMap[1], \"../promiseRejectionTrackingOptions\").default);\n    }\n  } else {\n    polyfillGlobal('Promise', () => require(_dependencyMap[2], \"../Promise\").default);\n  }\n});", "lineCount": 17, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 13, 0], [4, 6, 13, 0, "_require"], [4, 14, 13, 0], [4, 17, 13, 25, "require"], [4, 24, 13, 32], [4, 25, 13, 32, "_dependencyMap"], [4, 39, 13, 32], [4, 76, 13, 65], [4, 77, 13, 66], [5, 4, 13, 7, "polyfillGlobal"], [5, 18, 13, 21], [5, 21, 13, 21, "_require"], [5, 29, 13, 21], [5, 30, 13, 7, "polyfillGlobal"], [5, 44, 13, 21], [6, 2, 25, 0], [6, 6, 25, 4, "global"], [6, 12, 25, 10], [6, 14, 25, 12, "HermesInternal"], [6, 28, 25, 26], [6, 30, 25, 28, "<PERSON><PERSON><PERSON><PERSON>"], [6, 40, 25, 38], [6, 43, 25, 41], [6, 44, 25, 42], [6, 46, 25, 44], [7, 4, 26, 2], [7, 8, 26, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [7, 21, 26, 21], [7, 24, 26, 24, "global"], [7, 30, 26, 30], [7, 31, 26, 31, "Promise"], [7, 38, 26, 38], [8, 4, 28, 2], [8, 8, 28, 6, "__DEV__"], [8, 15, 28, 13], [8, 17, 28, 15], [9, 6, 29, 4], [9, 10, 29, 8], [9, 17, 29, 15, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [9, 30, 29, 28], [9, 35, 29, 33], [9, 45, 29, 43], [9, 47, 29, 45], [10, 8, 30, 6, "console"], [10, 15, 30, 13], [10, 16, 30, 14, "error"], [10, 21, 30, 19], [10, 22, 30, 20], [10, 52, 30, 50], [10, 53, 30, 51], [11, 6, 31, 4], [12, 6, 32, 4, "global"], [12, 12, 32, 10], [12, 13, 32, 11, "HermesInternal"], [12, 27, 32, 25], [12, 29, 32, 27, "enablePromiseRejectionTracker"], [12, 58, 32, 56], [12, 61, 33, 6, "require"], [12, 68, 33, 13], [12, 69, 33, 13, "_dependencyMap"], [12, 83, 33, 13], [12, 124, 33, 50], [12, 125, 33, 51], [12, 126, 33, 52, "default"], [12, 133, 34, 4], [12, 134, 34, 5], [13, 4, 35, 2], [14, 2, 36, 0], [14, 3, 36, 1], [14, 9, 36, 7], [15, 4, 37, 2, "polyfillGlobal"], [15, 18, 37, 16], [15, 19, 37, 17], [15, 28, 37, 26], [15, 30, 37, 28], [15, 36, 37, 34, "require"], [15, 43, 37, 41], [15, 44, 37, 41, "_dependencyMap"], [15, 58, 37, 41], [15, 75, 37, 54], [15, 76, 37, 55], [15, 77, 37, 56, "default"], [15, 84, 37, 63], [15, 85, 37, 64], [16, 2, 38, 0], [17, 0, 38, 1], [17, 3]], "functionMap": {"names": ["<global>", "polyfillGlobal$argument_1"], "mappings": "AAA;4BCoC,mCD"}}, "type": "js/module"}]}