{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const UnfoldVertical = exports.default = (0, _createLucideIcon.default)(\"UnfoldVertical\", [[\"path\", {\n    d: \"M12 22v-6\",\n    key: \"6o8u61\"\n  }], [\"path\", {\n    d: \"M12 8V2\",\n    key: \"1wkif3\"\n  }], [\"path\", {\n    d: \"M4 12H2\",\n    key: \"rhcxmi\"\n  }], [\"path\", {\n    d: \"M10 12H8\",\n    key: \"s88cx1\"\n  }], [\"path\", {\n    d: \"M16 12h-2\",\n    key: \"10asgb\"\n  }], [\"path\", {\n    d: \"M22 12h-2\",\n    key: \"14jgyd\"\n  }], [\"path\", {\n    d: \"m15 19-3 3-3-3\",\n    key: \"11eu04\"\n  }], [\"path\", {\n    d: \"m15 5-3-3-3 3\",\n    key: \"itvq4r\"\n  }]]);\n});", "lineCount": 40, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "UnfoldVertical"], [15, 22, 10, 20], [15, 25, 10, 20, "exports"], [15, 32, 10, 20], [15, 33, 10, 20, "default"], [15, 40, 10, 20], [15, 43, 10, 23], [15, 47, 10, 23, "createLucideIcon"], [15, 72, 10, 39], [15, 74, 10, 40], [15, 90, 10, 56], [15, 92, 10, 58], [15, 93, 11, 2], [15, 94, 11, 3], [15, 100, 11, 9], [15, 102, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 18, 11, 27], [17, 4, 11, 29, "key"], [17, 7, 11, 32], [17, 9, 11, 34], [18, 2, 11, 43], [18, 3, 11, 44], [18, 4, 11, 45], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 16, 13, 25], [23, 4, 13, 27, "key"], [23, 7, 13, 30], [23, 9, 13, 32], [24, 2, 13, 41], [24, 3, 13, 42], [24, 4, 13, 43], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 17, 14, 26], [26, 4, 14, 28, "key"], [26, 7, 14, 31], [26, 9, 14, 33], [27, 2, 14, 42], [27, 3, 14, 43], [27, 4, 14, 44], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 18, 15, 27], [29, 4, 15, 29, "key"], [29, 7, 15, 32], [29, 9, 15, 34], [30, 2, 15, 43], [30, 3, 15, 44], [30, 4, 15, 45], [30, 6, 16, 2], [30, 7, 16, 3], [30, 13, 16, 9], [30, 15, 16, 11], [31, 4, 16, 13, "d"], [31, 5, 16, 14], [31, 7, 16, 16], [31, 18, 16, 27], [32, 4, 16, 29, "key"], [32, 7, 16, 32], [32, 9, 16, 34], [33, 2, 16, 43], [33, 3, 16, 44], [33, 4, 16, 45], [33, 6, 17, 2], [33, 7, 17, 3], [33, 13, 17, 9], [33, 15, 17, 11], [34, 4, 17, 13, "d"], [34, 5, 17, 14], [34, 7, 17, 16], [34, 23, 17, 32], [35, 4, 17, 34, "key"], [35, 7, 17, 37], [35, 9, 17, 39], [36, 2, 17, 48], [36, 3, 17, 49], [36, 4, 17, 50], [36, 6, 18, 2], [36, 7, 18, 3], [36, 13, 18, 9], [36, 15, 18, 11], [37, 4, 18, 13, "d"], [37, 5, 18, 14], [37, 7, 18, 16], [37, 22, 18, 31], [38, 4, 18, 33, "key"], [38, 7, 18, 36], [38, 9, 18, 38], [39, 2, 18, 47], [39, 3, 18, 48], [39, 4, 18, 49], [39, 5, 19, 1], [39, 6, 19, 2], [40, 0, 19, 3], [40, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}