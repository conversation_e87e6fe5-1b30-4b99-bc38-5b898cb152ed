{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function flattenStyle(style) {\n    if (style === null || typeof style !== 'object') {\n      return undefined;\n    }\n    if (!Array.isArray(style)) {\n      return style;\n    }\n    var result = {};\n    for (var i = 0, styleLength = style.length; i < styleLength; ++i) {\n      var computedStyle = flattenStyle(style[i]);\n      if (computedStyle) {\n        for (var key in computedStyle) {\n          result[key] = computedStyle[key];\n        }\n      }\n    }\n    return result;\n  }\n  var _default = exports.default = flattenStyle;\n});", "lineCount": 27, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 16, 0], [8, 11, 16, 9, "flattenStyle"], [8, 23, 16, 21, "flattenStyle"], [8, 24, 17, 2, "style"], [8, 29, 17, 20], [8, 31, 19, 46], [9, 4, 20, 2], [9, 8, 20, 6, "style"], [9, 13, 20, 11], [9, 18, 20, 16], [9, 22, 20, 20], [9, 26, 20, 24], [9, 33, 20, 31, "style"], [9, 38, 20, 36], [9, 43, 20, 41], [9, 51, 20, 49], [9, 53, 20, 51], [10, 6, 21, 4], [10, 13, 21, 11, "undefined"], [10, 22, 21, 20], [11, 4, 22, 2], [12, 4, 24, 2], [12, 8, 24, 6], [12, 9, 24, 7, "Array"], [12, 14, 24, 12], [12, 15, 24, 13, "isArray"], [12, 22, 24, 20], [12, 23, 24, 21, "style"], [12, 28, 24, 26], [12, 29, 24, 27], [12, 31, 24, 29], [13, 6, 26, 4], [13, 13, 26, 11, "style"], [13, 18, 26, 16], [14, 4, 27, 2], [15, 4, 29, 2], [15, 8, 29, 8, "result"], [15, 14, 29, 38], [15, 17, 29, 41], [15, 18, 29, 42], [15, 19, 29, 43], [16, 4, 30, 2], [16, 9, 30, 7], [16, 13, 30, 11, "i"], [16, 14, 30, 12], [16, 17, 30, 15], [16, 18, 30, 16], [16, 20, 30, 18, "styleLength"], [16, 31, 30, 29], [16, 34, 30, 32, "style"], [16, 39, 30, 37], [16, 40, 30, 38, "length"], [16, 46, 30, 44], [16, 48, 30, 46, "i"], [16, 49, 30, 47], [16, 52, 30, 50, "styleLength"], [16, 63, 30, 61], [16, 65, 30, 63], [16, 67, 30, 65, "i"], [16, 68, 30, 66], [16, 70, 30, 68], [17, 6, 32, 4], [17, 10, 32, 10, "computedStyle"], [17, 23, 32, 23], [17, 26, 32, 26, "flattenStyle"], [17, 38, 32, 38], [17, 39, 32, 39, "style"], [17, 44, 32, 44], [17, 45, 32, 45, "i"], [17, 46, 32, 46], [17, 47, 32, 47], [17, 48, 32, 48], [18, 6, 33, 4], [18, 10, 33, 8, "computedStyle"], [18, 23, 33, 21], [18, 25, 33, 23], [19, 8, 35, 6], [19, 13, 35, 11], [19, 17, 35, 17, "key"], [19, 20, 35, 20], [19, 24, 35, 24, "computedStyle"], [19, 37, 35, 37], [19, 39, 35, 39], [20, 10, 38, 8, "result"], [20, 16, 38, 14], [20, 17, 38, 15, "key"], [20, 20, 38, 18], [20, 21, 38, 19], [20, 24, 38, 22, "computedStyle"], [20, 37, 38, 35], [20, 38, 38, 36, "key"], [20, 41, 38, 39], [20, 42, 38, 40], [21, 8, 39, 6], [22, 6, 40, 4], [23, 4, 41, 2], [24, 4, 43, 2], [24, 11, 43, 9, "result"], [24, 17, 43, 15], [25, 2, 44, 0], [26, 2, 44, 1], [26, 6, 44, 1, "_default"], [26, 14, 44, 1], [26, 17, 44, 1, "exports"], [26, 24, 44, 1], [26, 25, 44, 1, "default"], [26, 32, 44, 1], [26, 35, 46, 15, "flattenStyle"], [26, 47, 46, 27], [27, 0, 46, 27], [27, 3]], "functionMap": {"names": ["<global>", "flattenStyle"], "mappings": "AAA;ACe;CD4B"}}, "type": "js/module"}]}