{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const UnfoldHorizontal = exports.default = (0, _createLucideIcon.default)(\"UnfoldHorizontal\", [[\"path\", {\n    d: \"M16 12h6\",\n    key: \"15xry1\"\n  }], [\"path\", {\n    d: \"M8 12H2\",\n    key: \"1jqql6\"\n  }], [\"path\", {\n    d: \"M12 2v2\",\n    key: \"tus03m\"\n  }], [\"path\", {\n    d: \"M12 8v2\",\n    key: \"1woqiv\"\n  }], [\"path\", {\n    d: \"M12 14v2\",\n    key: \"8jcxud\"\n  }], [\"path\", {\n    d: \"M12 20v2\",\n    key: \"1lh1kg\"\n  }], [\"path\", {\n    d: \"m19 15 3-3-3-3\",\n    key: \"wjy7rq\"\n  }], [\"path\", {\n    d: \"m5 9-3 3 3 3\",\n    key: \"j64kie\"\n  }]]);\n});", "lineCount": 40, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "UnfoldHorizontal"], [15, 24, 10, 22], [15, 27, 10, 22, "exports"], [15, 34, 10, 22], [15, 35, 10, 22, "default"], [15, 42, 10, 22], [15, 45, 10, 25], [15, 49, 10, 25, "createLucideIcon"], [15, 74, 10, 41], [15, 76, 10, 42], [15, 94, 10, 60], [15, 96, 10, 62], [15, 97, 11, 2], [15, 98, 11, 3], [15, 104, 11, 9], [15, 106, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 17, 11, 26], [17, 4, 11, 28, "key"], [17, 7, 11, 31], [17, 9, 11, 33], [18, 2, 11, 42], [18, 3, 11, 43], [18, 4, 11, 44], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 16, 13, 25], [23, 4, 13, 27, "key"], [23, 7, 13, 30], [23, 9, 13, 32], [24, 2, 13, 41], [24, 3, 13, 42], [24, 4, 13, 43], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 16, 14, 25], [26, 4, 14, 27, "key"], [26, 7, 14, 30], [26, 9, 14, 32], [27, 2, 14, 41], [27, 3, 14, 42], [27, 4, 14, 43], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 17, 15, 26], [29, 4, 15, 28, "key"], [29, 7, 15, 31], [29, 9, 15, 33], [30, 2, 15, 42], [30, 3, 15, 43], [30, 4, 15, 44], [30, 6, 16, 2], [30, 7, 16, 3], [30, 13, 16, 9], [30, 15, 16, 11], [31, 4, 16, 13, "d"], [31, 5, 16, 14], [31, 7, 16, 16], [31, 17, 16, 26], [32, 4, 16, 28, "key"], [32, 7, 16, 31], [32, 9, 16, 33], [33, 2, 16, 42], [33, 3, 16, 43], [33, 4, 16, 44], [33, 6, 17, 2], [33, 7, 17, 3], [33, 13, 17, 9], [33, 15, 17, 11], [34, 4, 17, 13, "d"], [34, 5, 17, 14], [34, 7, 17, 16], [34, 23, 17, 32], [35, 4, 17, 34, "key"], [35, 7, 17, 37], [35, 9, 17, 39], [36, 2, 17, 48], [36, 3, 17, 49], [36, 4, 17, 50], [36, 6, 18, 2], [36, 7, 18, 3], [36, 13, 18, 9], [36, 15, 18, 11], [37, 4, 18, 13, "d"], [37, 5, 18, 14], [37, 7, 18, 16], [37, 21, 18, 30], [38, 4, 18, 32, "key"], [38, 7, 18, 35], [38, 9, 18, 37], [39, 2, 18, 46], [39, 3, 18, 47], [39, 4, 18, 48], [39, 5, 19, 1], [39, 6, 19, 2], [40, 0, 19, 3], [40, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}