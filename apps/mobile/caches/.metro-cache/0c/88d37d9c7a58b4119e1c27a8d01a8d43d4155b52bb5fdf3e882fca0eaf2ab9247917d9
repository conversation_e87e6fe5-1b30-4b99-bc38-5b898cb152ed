{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 16, "index": 301}, "end": {"line": 8, "column": 32, "index": 317}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./matchers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 19, "index": 338}, "end": {"line": 9, "column": 40, "index": 359}}], "key": "89ylKT57ef0l7ma8+p1HhPaMj94=", "exportNames": ["*"]}}, {"name": "./sortRoutes", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 21, "index": 382}, "end": {"line": 10, "column": 44, "index": 405}}], "key": "AGgdHPlpLdteTt8GoJKs7TyzmuQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/Route.js\";\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.sortRoutes = exports.sortRoutesWithInitial = exports.LocalRouteParamsContext = void 0;\n  exports.useRouteNode = useRouteNode;\n  exports.useContextKey = useContextKey;\n  exports.Route = Route;\n  const react_1 = require(_dependencyMap[1], \"react\");\n  const matchers_1 = require(_dependencyMap[2], \"./matchers\");\n  const sortRoutes_1 = require(_dependencyMap[3], \"./sortRoutes\");\n  Object.defineProperty(exports, \"sortRoutesWithInitial\", {\n    enumerable: true,\n    get: function () {\n      return sortRoutes_1.sortRoutesWithInitial;\n    }\n  });\n  Object.defineProperty(exports, \"sortRoutes\", {\n    enumerable: true,\n    get: function () {\n      return sortRoutes_1.sortRoutes;\n    }\n  });\n  const CurrentRouteContext = (0, react_1.createContext)(null);\n  exports.LocalRouteParamsContext = (0, react_1.createContext)({});\n  if (process.env.NODE_ENV !== 'production') {\n    CurrentRouteContext.displayName = 'RouteNode';\n  }\n  /** Return the RouteNode at the current contextual boundary. */\n  function useRouteNode() {\n    return (0, react_1.use)(CurrentRouteContext);\n  }\n  function useContextKey() {\n    const node = useRouteNode();\n    if (node == null) {\n      throw new Error('No filename found. This is likely a bug in expo-router.');\n    }\n    return (0, matchers_1.getContextKey)(node.contextKey);\n  }\n  /** Provides the matching routes and filename to the children. */\n  function Route({\n    children,\n    node,\n    route\n  }) {\n    return _reactNativeCssInteropJsxRuntime.jsx(exports.LocalRouteParamsContext.Provider, {\n      value: route?.params,\n      children: _reactNativeCssInteropJsxRuntime.jsx(CurrentRouteContext.Provider, {\n        value: node,\n        children: children\n      })\n    });\n  }\n});", "lineCount": 59, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_reactNativeCssInteropJsxRuntime"], [5, 38, 2, 13], [5, 41, 2, 13, "require"], [5, 48, 2, 13], [5, 49, 2, 13, "_dependencyMap"], [5, 63, 2, 13], [6, 2, 2, 13], [6, 6, 2, 13, "_jsxFileName"], [6, 18, 2, 13], [7, 2, 3, 0, "Object"], [7, 8, 3, 6], [7, 9, 3, 7, "defineProperty"], [7, 23, 3, 21], [7, 24, 3, 22, "exports"], [7, 31, 3, 29], [7, 33, 3, 31], [7, 45, 3, 43], [7, 47, 3, 45], [8, 4, 3, 47, "value"], [8, 9, 3, 52], [8, 11, 3, 54], [9, 2, 3, 59], [9, 3, 3, 60], [9, 4, 3, 61], [10, 2, 4, 0, "exports"], [10, 9, 4, 7], [10, 10, 4, 8, "sortRoutes"], [10, 20, 4, 18], [10, 23, 4, 21, "exports"], [10, 30, 4, 28], [10, 31, 4, 29, "sortRoutesWithInitial"], [10, 52, 4, 50], [10, 55, 4, 53, "exports"], [10, 62, 4, 60], [10, 63, 4, 61, "LocalRouteParamsContext"], [10, 86, 4, 84], [10, 89, 4, 87], [10, 94, 4, 92], [10, 95, 4, 93], [11, 2, 5, 0, "exports"], [11, 9, 5, 7], [11, 10, 5, 8, "useRouteNode"], [11, 22, 5, 20], [11, 25, 5, 23, "useRouteNode"], [11, 37, 5, 35], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "useContextKey"], [12, 23, 6, 21], [12, 26, 6, 24, "useContextKey"], [12, 39, 6, 37], [13, 2, 7, 0, "exports"], [13, 9, 7, 7], [13, 10, 7, 8, "Route"], [13, 15, 7, 13], [13, 18, 7, 16, "Route"], [13, 23, 7, 21], [14, 2, 8, 0], [14, 8, 8, 6, "react_1"], [14, 15, 8, 13], [14, 18, 8, 16, "require"], [14, 25, 8, 23], [14, 26, 8, 23, "_dependencyMap"], [14, 40, 8, 23], [14, 52, 8, 31], [14, 53, 8, 32], [15, 2, 9, 0], [15, 8, 9, 6, "matchers_1"], [15, 18, 9, 16], [15, 21, 9, 19, "require"], [15, 28, 9, 26], [15, 29, 9, 26, "_dependencyMap"], [15, 43, 9, 26], [15, 60, 9, 39], [15, 61, 9, 40], [16, 2, 10, 0], [16, 8, 10, 6, "sortRoutes_1"], [16, 20, 10, 18], [16, 23, 10, 21, "require"], [16, 30, 10, 28], [16, 31, 10, 28, "_dependencyMap"], [16, 45, 10, 28], [16, 64, 10, 43], [16, 65, 10, 44], [17, 2, 11, 0, "Object"], [17, 8, 11, 6], [17, 9, 11, 7, "defineProperty"], [17, 23, 11, 21], [17, 24, 11, 22, "exports"], [17, 31, 11, 29], [17, 33, 11, 31], [17, 56, 11, 54], [17, 58, 11, 56], [18, 4, 11, 58, "enumerable"], [18, 14, 11, 68], [18, 16, 11, 70], [18, 20, 11, 74], [19, 4, 11, 76, "get"], [19, 7, 11, 79], [19, 9, 11, 81], [19, 18, 11, 81, "get"], [19, 19, 11, 81], [19, 21, 11, 93], [20, 6, 11, 95], [20, 13, 11, 102, "sortRoutes_1"], [20, 25, 11, 114], [20, 26, 11, 115, "sortRoutesWithInitial"], [20, 47, 11, 136], [21, 4, 11, 138], [22, 2, 11, 140], [22, 3, 11, 141], [22, 4, 11, 142], [23, 2, 12, 0, "Object"], [23, 8, 12, 6], [23, 9, 12, 7, "defineProperty"], [23, 23, 12, 21], [23, 24, 12, 22, "exports"], [23, 31, 12, 29], [23, 33, 12, 31], [23, 45, 12, 43], [23, 47, 12, 45], [24, 4, 12, 47, "enumerable"], [24, 14, 12, 57], [24, 16, 12, 59], [24, 20, 12, 63], [25, 4, 12, 65, "get"], [25, 7, 12, 68], [25, 9, 12, 70], [25, 18, 12, 70, "get"], [25, 19, 12, 70], [25, 21, 12, 82], [26, 6, 12, 84], [26, 13, 12, 91, "sortRoutes_1"], [26, 25, 12, 103], [26, 26, 12, 104, "sortRoutes"], [26, 36, 12, 114], [27, 4, 12, 116], [28, 2, 12, 118], [28, 3, 12, 119], [28, 4, 12, 120], [29, 2, 13, 0], [29, 8, 13, 6, "CurrentRouteContext"], [29, 27, 13, 25], [29, 30, 13, 28], [29, 31, 13, 29], [29, 32, 13, 30], [29, 34, 13, 32, "react_1"], [29, 41, 13, 39], [29, 42, 13, 40, "createContext"], [29, 55, 13, 53], [29, 57, 13, 55], [29, 61, 13, 59], [29, 62, 13, 60], [30, 2, 14, 0, "exports"], [30, 9, 14, 7], [30, 10, 14, 8, "LocalRouteParamsContext"], [30, 33, 14, 31], [30, 36, 14, 34], [30, 37, 14, 35], [30, 38, 14, 36], [30, 40, 14, 38, "react_1"], [30, 47, 14, 45], [30, 48, 14, 46, "createContext"], [30, 61, 14, 59], [30, 63, 14, 61], [30, 64, 14, 62], [30, 65, 14, 63], [30, 66, 14, 64], [31, 2, 15, 0], [31, 6, 15, 4, "process"], [31, 13, 15, 11], [31, 14, 15, 12, "env"], [31, 17, 15, 15], [31, 18, 15, 16, "NODE_ENV"], [31, 26, 15, 24], [31, 31, 15, 29], [31, 43, 15, 41], [31, 45, 15, 43], [32, 4, 16, 4, "CurrentRouteContext"], [32, 23, 16, 23], [32, 24, 16, 24, "displayName"], [32, 35, 16, 35], [32, 38, 16, 38], [32, 49, 16, 49], [33, 2, 17, 0], [34, 2, 18, 0], [35, 2, 19, 0], [35, 11, 19, 9, "useRouteNode"], [35, 23, 19, 21, "useRouteNode"], [35, 24, 19, 21], [35, 26, 19, 24], [36, 4, 20, 4], [36, 11, 20, 11], [36, 12, 20, 12], [36, 13, 20, 13], [36, 15, 20, 15, "react_1"], [36, 22, 20, 22], [36, 23, 20, 23, "use"], [36, 26, 20, 26], [36, 28, 20, 28, "CurrentRouteContext"], [36, 47, 20, 47], [36, 48, 20, 48], [37, 2, 21, 0], [38, 2, 22, 0], [38, 11, 22, 9, "useContextKey"], [38, 24, 22, 22, "useContextKey"], [38, 25, 22, 22], [38, 27, 22, 25], [39, 4, 23, 4], [39, 10, 23, 10, "node"], [39, 14, 23, 14], [39, 17, 23, 17, "useRouteNode"], [39, 29, 23, 29], [39, 30, 23, 30], [39, 31, 23, 31], [40, 4, 24, 4], [40, 8, 24, 8, "node"], [40, 12, 24, 12], [40, 16, 24, 16], [40, 20, 24, 20], [40, 22, 24, 22], [41, 6, 25, 8], [41, 12, 25, 14], [41, 16, 25, 18, "Error"], [41, 21, 25, 23], [41, 22, 25, 24], [41, 79, 25, 81], [41, 80, 25, 82], [42, 4, 26, 4], [43, 4, 27, 4], [43, 11, 27, 11], [43, 12, 27, 12], [43, 13, 27, 13], [43, 15, 27, 15, "matchers_1"], [43, 25, 27, 25], [43, 26, 27, 26, "getContextKey"], [43, 39, 27, 39], [43, 41, 27, 41, "node"], [43, 45, 27, 45], [43, 46, 27, 46, "<PERSON><PERSON>ey"], [43, 56, 27, 56], [43, 57, 27, 57], [44, 2, 28, 0], [45, 2, 29, 0], [46, 2, 30, 0], [46, 11, 30, 9, "Route"], [46, 16, 30, 14, "Route"], [46, 17, 30, 15], [47, 4, 30, 17, "children"], [47, 12, 30, 25], [48, 4, 30, 27, "node"], [48, 8, 30, 31], [49, 4, 30, 33, "route"], [50, 2, 30, 39], [50, 3, 30, 40], [50, 5, 30, 42], [51, 4, 31, 4], [51, 11, 31, 12, "_reactNativeCssInteropJsxRuntime"], [51, 43, 31, 12], [51, 44, 31, 12, "jsx"], [51, 47, 31, 12], [51, 48, 31, 13, "exports"], [51, 55, 31, 20], [51, 56, 31, 21, "LocalRouteParamsContext"], [51, 79, 31, 44], [51, 80, 31, 45, "Provider"], [51, 88, 31, 53], [52, 6, 31, 54, "value"], [52, 11, 31, 59], [52, 13, 31, 61, "route"], [52, 18, 31, 66], [52, 20, 31, 68, "params"], [52, 26, 31, 75], [53, 6, 31, 75, "children"], [53, 14, 31, 75], [53, 16, 32, 6, "_reactNativeCssInteropJsxRuntime"], [53, 48, 32, 6], [53, 49, 32, 6, "jsx"], [53, 52, 32, 6], [53, 53, 32, 7, "CurrentRouteContext"], [53, 72, 32, 26], [53, 73, 32, 27, "Provider"], [53, 81, 32, 35], [54, 8, 32, 36, "value"], [54, 13, 32, 41], [54, 15, 32, 43, "node"], [54, 19, 32, 48], [55, 8, 32, 48, "children"], [55, 16, 32, 48], [55, 18, 32, 50, "children"], [56, 6, 32, 58], [56, 7, 32, 89], [57, 4, 32, 90], [57, 5, 33, 46], [57, 6, 33, 47], [58, 2, 34, 0], [59, 0, 34, 1], [59, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get", "useRouteNode", "useContextKey", "Route"], "mappings": "AAA;iFCU,0DD;sECC,+CD;AEO;CFE;AGC;CHM;AIE;CJI"}}, "type": "js/module"}]}