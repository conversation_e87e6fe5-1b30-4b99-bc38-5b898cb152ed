{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 50, "index": 298}}], "key": "eTOOXVNPpMK2U8dOAmBWjbEJ4yE=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 299}, "end": {"line": 5, "column": 44, "index": 343}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 344}, "end": {"line": 6, "column": 46, "index": 390}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _constants = require(_dependencyMap[2], \"../constants\");\n  var _interfaces = require(_dependencyMap[3], \"../interfaces\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[4], \"./GestureHandler\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const DEFAULT_MIN_POINTERS = 1;\n  const DEFAULT_MAX_POINTERS = 10;\n  const DEFAULT_MIN_DIST_SQ = _constants.DEFAULT_TOUCH_SLOP * _constants.DEFAULT_TOUCH_SLOP;\n  class PanGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"customActivationProperties\", ['activeOffsetXStart', 'activeOffsetXEnd', 'failOffsetXStart', 'failOffsetXEnd', 'activeOffsetYStart', 'activeOffsetYEnd', 'failOffsetYStart', 'failOffsetYEnd', 'minVelocityX', 'minVelocityY', 'minVelocity']);\n      _defineProperty(this, \"velocityX\", 0);\n      _defineProperty(this, \"velocityY\", 0);\n      _defineProperty(this, \"minDistSq\", DEFAULT_MIN_DIST_SQ);\n      _defineProperty(this, \"activeOffsetXStart\", -Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"activeOffsetXEnd\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"failOffsetXStart\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"failOffsetXEnd\", Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"activeOffsetYStart\", Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"activeOffsetYEnd\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"failOffsetYStart\", Number.MIN_SAFE_INTEGER);\n      _defineProperty(this, \"failOffsetYEnd\", Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"minVelocityX\", Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"minVelocityY\", Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"minVelocitySq\", Number.MAX_SAFE_INTEGER);\n      _defineProperty(this, \"minPointers\", DEFAULT_MIN_POINTERS);\n      _defineProperty(this, \"maxPointers\", DEFAULT_MAX_POINTERS);\n      _defineProperty(this, \"startX\", 0);\n      _defineProperty(this, \"startY\", 0);\n      _defineProperty(this, \"offsetX\", 0);\n      _defineProperty(this, \"offsetY\", 0);\n      _defineProperty(this, \"lastX\", 0);\n      _defineProperty(this, \"lastY\", 0);\n      _defineProperty(this, \"stylusData\", void 0);\n      _defineProperty(this, \"activateAfterLongPress\", 0);\n      _defineProperty(this, \"activationTimeout\", 0);\n      _defineProperty(this, \"enableTrackpadTwoFingerGesture\", false);\n      _defineProperty(this, \"endWheelTimeout\", 0);\n      _defineProperty(this, \"wheelDevice\", _interfaces.WheelDevice.UNDETERMINED);\n    }\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      this.resetConfig();\n      super.updateGestureConfig({\n        enabled: enabled,\n        ...props\n      });\n      this.checkCustomActivationCriteria(this.customActivationProperties);\n      if (this.config.minDist !== undefined) {\n        this.minDistSq = this.config.minDist * this.config.minDist;\n      } else if (this.hasCustomActivationCriteria) {\n        this.minDistSq = Number.MAX_SAFE_INTEGER;\n      }\n      if (this.config.minPointers !== undefined) {\n        this.minPointers = this.config.minPointers;\n      }\n      if (this.config.maxPointers !== undefined) {\n        this.maxPointers = this.config.maxPointers;\n      }\n      if (this.config.minVelocity !== undefined) {\n        this.minVelocityX = this.config.minVelocity;\n        this.minVelocityY = this.config.minVelocity;\n      }\n      if (this.config.minVelocityX !== undefined) {\n        this.minVelocityX = this.config.minVelocityX;\n      }\n      if (this.config.minVelocityY !== undefined) {\n        this.minVelocityY = this.config.minVelocityY;\n      }\n      if (this.config.activateAfterLongPress !== undefined) {\n        this.activateAfterLongPress = this.config.activateAfterLongPress;\n      }\n      if (this.config.activeOffsetXStart !== undefined) {\n        this.activeOffsetXStart = this.config.activeOffsetXStart;\n        if (this.config.activeOffsetXEnd === undefined) {\n          this.activeOffsetXEnd = Number.MAX_SAFE_INTEGER;\n        }\n      }\n      if (this.config.activeOffsetXEnd !== undefined) {\n        this.activeOffsetXEnd = this.config.activeOffsetXEnd;\n        if (this.config.activeOffsetXStart === undefined) {\n          this.activeOffsetXStart = Number.MIN_SAFE_INTEGER;\n        }\n      }\n      if (this.config.failOffsetXStart !== undefined) {\n        this.failOffsetXStart = this.config.failOffsetXStart;\n        if (this.config.failOffsetXEnd === undefined) {\n          this.failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n        }\n      }\n      if (this.config.failOffsetXEnd !== undefined) {\n        this.failOffsetXEnd = this.config.failOffsetXEnd;\n        if (this.config.failOffsetXStart === undefined) {\n          this.failOffsetXStart = Number.MIN_SAFE_INTEGER;\n        }\n      }\n      if (this.config.activeOffsetYStart !== undefined) {\n        this.activeOffsetYStart = this.config.activeOffsetYStart;\n        if (this.config.activeOffsetYEnd === undefined) {\n          this.activeOffsetYEnd = Number.MAX_SAFE_INTEGER;\n        }\n      }\n      if (this.config.activeOffsetYEnd !== undefined) {\n        this.activeOffsetYEnd = this.config.activeOffsetYEnd;\n        if (this.config.activeOffsetYStart === undefined) {\n          this.activeOffsetYStart = Number.MIN_SAFE_INTEGER;\n        }\n      }\n      if (this.config.failOffsetYStart !== undefined) {\n        this.failOffsetYStart = this.config.failOffsetYStart;\n        if (this.config.failOffsetYEnd === undefined) {\n          this.failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n        }\n      }\n      if (this.config.failOffsetYEnd !== undefined) {\n        this.failOffsetYEnd = this.config.failOffsetYEnd;\n        if (this.config.failOffsetYStart === undefined) {\n          this.failOffsetYStart = Number.MIN_SAFE_INTEGER;\n        }\n      }\n      if (this.config.enableTrackpadTwoFingerGesture !== undefined) {\n        this.enableTrackpadTwoFingerGesture = this.config.enableTrackpadTwoFingerGesture;\n      }\n    }\n    resetConfig() {\n      super.resetConfig();\n      this.activeOffsetXStart = -Number.MAX_SAFE_INTEGER;\n      this.activeOffsetXEnd = Number.MIN_SAFE_INTEGER;\n      this.failOffsetXStart = Number.MIN_SAFE_INTEGER;\n      this.failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n      this.activeOffsetYStart = Number.MAX_SAFE_INTEGER;\n      this.activeOffsetYEnd = Number.MIN_SAFE_INTEGER;\n      this.failOffsetYStart = Number.MIN_SAFE_INTEGER;\n      this.failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n      this.minVelocityX = Number.MAX_SAFE_INTEGER;\n      this.minVelocityY = Number.MAX_SAFE_INTEGER;\n      this.minVelocitySq = Number.MAX_SAFE_INTEGER;\n      this.minDistSq = DEFAULT_MIN_DIST_SQ;\n      this.minPointers = DEFAULT_MIN_POINTERS;\n      this.maxPointers = DEFAULT_MAX_POINTERS;\n      this.activateAfterLongPress = 0;\n    }\n    transformNativeEvent() {\n      const translationX = this.getTranslationX();\n      const translationY = this.getTranslationY();\n      return {\n        ...super.transformNativeEvent(),\n        translationX: isNaN(translationX) ? 0 : translationX,\n        translationY: isNaN(translationY) ? 0 : translationY,\n        velocityX: this.velocityX,\n        velocityY: this.velocityY,\n        stylusData: this.stylusData\n      };\n    }\n    getTranslationX() {\n      return this.lastX - this.startX + this.offsetX;\n    }\n    getTranslationY() {\n      return this.lastY - this.startY + this.offsetY;\n    }\n    clearActivationTimeout() {\n      clearTimeout(this.activationTimeout);\n    } // Events Handling\n\n    onPointerDown(event) {\n      if (!this.isButtonInConfig(event.button)) {\n        return;\n      }\n      this.tracker.addToTracker(event);\n      this.stylusData = event.stylusData;\n      super.onPointerDown(event);\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.startX = this.lastX;\n      this.startY = this.lastY;\n      this.tryBegin(event);\n      this.checkBegan();\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerAdd(event);\n      this.tryBegin(event);\n      this.offsetX += this.lastX - this.startX;\n      this.offsetY += this.lastY - this.startY;\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.startX = this.lastX;\n      this.startY = this.lastY;\n      if (this.tracker.trackedPointersCount > this.maxPointers) {\n        if (this.state === _State.State.ACTIVE) {\n          this.cancel();\n        } else {\n          this.fail();\n        }\n      } else {\n        this.checkBegan();\n      }\n    }\n    onPointerUp(event) {\n      this.stylusData = event.stylusData;\n      super.onPointerUp(event);\n      if (this.state === _State.State.ACTIVE) {\n        const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n        this.lastX = lastCoords.x;\n        this.lastY = lastCoords.y;\n      }\n      this.tracker.removeFromTracker(event.pointerId);\n      if (this.tracker.trackedPointersCount === 0) {\n        this.clearActivationTimeout();\n      }\n      if (this.state === _State.State.ACTIVE) {\n        this.end();\n      } else {\n        this.resetProgress();\n        this.fail();\n      }\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.tracker.removeFromTracker(event.pointerId);\n      this.offsetX += this.lastX - this.startX;\n      this.offsetY += this.lastY - this.startY;\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      this.startX = this.lastX;\n      this.startY = this.lastY;\n      if (!(this.state === _State.State.ACTIVE && this.tracker.trackedPointersCount < this.minPointers)) {\n        this.checkBegan();\n      }\n    }\n    onPointerMove(event) {\n      this.tracker.track(event);\n      this.stylusData = event.stylusData;\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      const velocity = this.tracker.getVelocity(event.pointerId);\n      this.velocityX = velocity.x;\n      this.velocityY = velocity.y;\n      this.checkBegan();\n      super.onPointerMove(event);\n    }\n    onPointerOutOfBounds(event) {\n      if (this.shouldCancelWhenOutside) {\n        return;\n      }\n      this.tracker.track(event);\n      this.stylusData = event.stylusData;\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      const velocity = this.tracker.getVelocity(event.pointerId);\n      this.velocityX = velocity.x;\n      this.velocityY = velocity.y;\n      this.checkBegan();\n      if (this.state === _State.State.ACTIVE) {\n        super.onPointerOutOfBounds(event);\n      }\n    }\n    scheduleWheelEnd(event) {\n      clearTimeout(this.endWheelTimeout);\n      this.endWheelTimeout = setTimeout(() => {\n        if (this.state === _State.State.ACTIVE) {\n          this.end();\n          this.tracker.removeFromTracker(event.pointerId);\n          this.state = _State.State.UNDETERMINED;\n        }\n        this.wheelDevice = _interfaces.WheelDevice.UNDETERMINED;\n      }, 30);\n    }\n    onWheel(event) {\n      if (this.wheelDevice === _interfaces.WheelDevice.MOUSE || !this.enableTrackpadTwoFingerGesture) {\n        return;\n      }\n      if (this.state === _State.State.UNDETERMINED) {\n        this.wheelDevice = event.wheelDeltaY % 120 !== 0 ? _interfaces.WheelDevice.TOUCHPAD : _interfaces.WheelDevice.MOUSE;\n        if (this.wheelDevice === _interfaces.WheelDevice.MOUSE) {\n          this.scheduleWheelEnd(event);\n          return;\n        }\n        this.tracker.addToTracker(event);\n        const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n        this.lastX = lastCoords.x;\n        this.lastY = lastCoords.y;\n        this.startX = this.lastX;\n        this.startY = this.lastY;\n        this.begin();\n        this.activate();\n      }\n      this.tracker.track(event);\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.lastX = lastCoords.x;\n      this.lastY = lastCoords.y;\n      const velocity = this.tracker.getVelocity(event.pointerId);\n      this.velocityX = velocity.x;\n      this.velocityY = velocity.y;\n      this.tryToSendMoveEvent(false, event);\n      this.scheduleWheelEnd(event);\n    }\n    shouldActivate() {\n      const dx = this.getTranslationX();\n      if (this.activeOffsetXStart !== Number.MAX_SAFE_INTEGER && dx < this.activeOffsetXStart) {\n        return true;\n      }\n      if (this.activeOffsetXEnd !== Number.MIN_SAFE_INTEGER && dx > this.activeOffsetXEnd) {\n        return true;\n      }\n      const dy = this.getTranslationY();\n      if (this.activeOffsetYStart !== Number.MAX_SAFE_INTEGER && dy < this.activeOffsetYStart) {\n        return true;\n      }\n      if (this.activeOffsetYEnd !== Number.MIN_SAFE_INTEGER && dy > this.activeOffsetYEnd) {\n        return true;\n      }\n      const distanceSq = dx * dx + dy * dy;\n      if (this.minDistSq !== Number.MAX_SAFE_INTEGER && distanceSq >= this.minDistSq) {\n        return true;\n      }\n      const vx = this.velocityX;\n      if (this.minVelocityX !== Number.MAX_SAFE_INTEGER && (this.minVelocityX < 0 && vx <= this.minVelocityX || this.minVelocityX >= 0 && this.minVelocityX <= vx)) {\n        return true;\n      }\n      const vy = this.velocityY;\n      if (this.minVelocityY !== Number.MAX_SAFE_INTEGER && (this.minVelocityY < 0 && vy <= this.minVelocityY || this.minVelocityY >= 0 && this.minVelocityY <= vy)) {\n        return true;\n      }\n      const velocitySq = vx * vx + vy * vy;\n      return this.minVelocitySq !== Number.MAX_SAFE_INTEGER && velocitySq >= this.minVelocitySq;\n    }\n    shouldFail() {\n      const dx = this.getTranslationX();\n      const dy = this.getTranslationY();\n      const distanceSq = dx * dx + dy * dy;\n      if (this.activateAfterLongPress > 0 && distanceSq > DEFAULT_MIN_DIST_SQ) {\n        this.clearActivationTimeout();\n        return true;\n      }\n      if (this.failOffsetXStart !== Number.MIN_SAFE_INTEGER && dx < this.failOffsetXStart) {\n        return true;\n      }\n      if (this.failOffsetXEnd !== Number.MAX_SAFE_INTEGER && dx > this.failOffsetXEnd) {\n        return true;\n      }\n      if (this.failOffsetYStart !== Number.MIN_SAFE_INTEGER && dy < this.failOffsetYStart) {\n        return true;\n      }\n      return this.failOffsetYEnd !== Number.MAX_SAFE_INTEGER && dy > this.failOffsetYEnd;\n    }\n    tryBegin(event) {\n      if (this.state === _State.State.UNDETERMINED && this.tracker.trackedPointersCount >= this.minPointers) {\n        this.resetProgress();\n        this.offsetX = 0;\n        this.offsetY = 0;\n        this.velocityX = 0;\n        this.velocityY = 0;\n        this.begin();\n        if (this.activateAfterLongPress > 0) {\n          this.activationTimeout = setTimeout(() => {\n            this.activate();\n          }, this.activateAfterLongPress);\n        }\n      } else {\n        const velocity = this.tracker.getVelocity(event.pointerId);\n        this.velocityX = velocity.x;\n        this.velocityY = velocity.y;\n      }\n    }\n    checkBegan() {\n      if (this.state === _State.State.BEGAN) {\n        if (this.shouldFail()) {\n          this.fail();\n        } else if (this.shouldActivate()) {\n          this.activate();\n        }\n      }\n    }\n    activate(force = false) {\n      if (this.state !== _State.State.ACTIVE) {\n        this.resetProgress();\n      }\n      super.activate(force);\n    }\n    onCancel() {\n      this.clearActivationTimeout();\n    }\n    onReset() {\n      this.clearActivationTimeout();\n    }\n    resetProgress() {\n      if (this.state === _State.State.ACTIVE) {\n        return;\n      }\n      this.startX = this.lastX;\n      this.startY = this.lastY;\n    }\n  }\n  exports.default = PanGestureHandler;\n});", "lineCount": 423, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_constants"], [8, 16, 4, 0], [8, 19, 4, 0, "require"], [8, 26, 4, 0], [8, 27, 4, 0, "_dependencyMap"], [8, 41, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_interfaces"], [9, 17, 5, 0], [9, 20, 5, 0, "require"], [9, 27, 5, 0], [9, 28, 5, 0, "_dependencyMap"], [9, 42, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_Gesture<PERSON><PERSON>ler"], [10, 21, 6, 0], [10, 24, 6, 0, "_interopRequireDefault"], [10, 46, 6, 0], [10, 47, 6, 0, "require"], [10, 54, 6, 0], [10, 55, 6, 0, "_dependencyMap"], [10, 69, 6, 0], [11, 2, 1, 0], [11, 11, 1, 9, "_defineProperty"], [11, 26, 1, 24, "_defineProperty"], [11, 27, 1, 25, "obj"], [11, 30, 1, 28], [11, 32, 1, 30, "key"], [11, 35, 1, 33], [11, 37, 1, 35, "value"], [11, 42, 1, 40], [11, 44, 1, 42], [12, 4, 1, 44], [12, 8, 1, 48, "key"], [12, 11, 1, 51], [12, 15, 1, 55, "obj"], [12, 18, 1, 58], [12, 20, 1, 60], [13, 6, 1, 62, "Object"], [13, 12, 1, 68], [13, 13, 1, 69, "defineProperty"], [13, 27, 1, 83], [13, 28, 1, 84, "obj"], [13, 31, 1, 87], [13, 33, 1, 89, "key"], [13, 36, 1, 92], [13, 38, 1, 94], [14, 8, 1, 96, "value"], [14, 13, 1, 101], [14, 15, 1, 103, "value"], [14, 20, 1, 108], [15, 8, 1, 110, "enumerable"], [15, 18, 1, 120], [15, 20, 1, 122], [15, 24, 1, 126], [16, 8, 1, 128, "configurable"], [16, 20, 1, 140], [16, 22, 1, 142], [16, 26, 1, 146], [17, 8, 1, 148, "writable"], [17, 16, 1, 156], [17, 18, 1, 158], [18, 6, 1, 163], [18, 7, 1, 164], [18, 8, 1, 165], [19, 4, 1, 167], [19, 5, 1, 168], [19, 11, 1, 174], [20, 6, 1, 176, "obj"], [20, 9, 1, 179], [20, 10, 1, 180, "key"], [20, 13, 1, 183], [20, 14, 1, 184], [20, 17, 1, 187, "value"], [20, 22, 1, 192], [21, 4, 1, 194], [22, 4, 1, 196], [22, 11, 1, 203, "obj"], [22, 14, 1, 206], [23, 2, 1, 208], [24, 2, 7, 0], [24, 8, 7, 6, "DEFAULT_MIN_POINTERS"], [24, 28, 7, 26], [24, 31, 7, 29], [24, 32, 7, 30], [25, 2, 8, 0], [25, 8, 8, 6, "DEFAULT_MAX_POINTERS"], [25, 28, 8, 26], [25, 31, 8, 29], [25, 33, 8, 31], [26, 2, 9, 0], [26, 8, 9, 6, "DEFAULT_MIN_DIST_SQ"], [26, 27, 9, 25], [26, 30, 9, 28, "DEFAULT_TOUCH_SLOP"], [26, 59, 9, 46], [26, 62, 9, 49, "DEFAULT_TOUCH_SLOP"], [26, 91, 9, 67], [27, 2, 10, 15], [27, 8, 10, 21, "PanGestureHandler"], [27, 25, 10, 38], [27, 34, 10, 47, "Gesture<PERSON>andler"], [27, 57, 10, 61], [27, 58, 10, 62], [28, 4, 11, 2, "constructor"], [28, 15, 11, 13, "constructor"], [28, 16, 11, 14], [28, 19, 11, 17, "args"], [28, 23, 11, 21], [28, 25, 11, 23], [29, 6, 12, 4], [29, 11, 12, 9], [29, 12, 12, 10], [29, 15, 12, 13, "args"], [29, 19, 12, 17], [29, 20, 12, 18], [30, 6, 14, 4, "_defineProperty"], [30, 21, 14, 19], [30, 22, 14, 20], [30, 26, 14, 24], [30, 28, 14, 26], [30, 56, 14, 54], [30, 58, 14, 56], [30, 59, 14, 57], [30, 79, 14, 77], [30, 81, 14, 79], [30, 99, 14, 97], [30, 101, 14, 99], [30, 119, 14, 117], [30, 121, 14, 119], [30, 137, 14, 135], [30, 139, 14, 137], [30, 159, 14, 157], [30, 161, 14, 159], [30, 179, 14, 177], [30, 181, 14, 179], [30, 199, 14, 197], [30, 201, 14, 199], [30, 217, 14, 215], [30, 219, 14, 217], [30, 233, 14, 231], [30, 235, 14, 233], [30, 249, 14, 247], [30, 251, 14, 249], [30, 264, 14, 262], [30, 265, 14, 263], [30, 266, 14, 264], [31, 6, 16, 4, "_defineProperty"], [31, 21, 16, 19], [31, 22, 16, 20], [31, 26, 16, 24], [31, 28, 16, 26], [31, 39, 16, 37], [31, 41, 16, 39], [31, 42, 16, 40], [31, 43, 16, 41], [32, 6, 18, 4, "_defineProperty"], [32, 21, 18, 19], [32, 22, 18, 20], [32, 26, 18, 24], [32, 28, 18, 26], [32, 39, 18, 37], [32, 41, 18, 39], [32, 42, 18, 40], [32, 43, 18, 41], [33, 6, 20, 4, "_defineProperty"], [33, 21, 20, 19], [33, 22, 20, 20], [33, 26, 20, 24], [33, 28, 20, 26], [33, 39, 20, 37], [33, 41, 20, 39, "DEFAULT_MIN_DIST_SQ"], [33, 60, 20, 58], [33, 61, 20, 59], [34, 6, 22, 4, "_defineProperty"], [34, 21, 22, 19], [34, 22, 22, 20], [34, 26, 22, 24], [34, 28, 22, 26], [34, 48, 22, 46], [34, 50, 22, 48], [34, 51, 22, 49, "Number"], [34, 57, 22, 55], [34, 58, 22, 56, "MAX_SAFE_INTEGER"], [34, 74, 22, 72], [34, 75, 22, 73], [35, 6, 24, 4, "_defineProperty"], [35, 21, 24, 19], [35, 22, 24, 20], [35, 26, 24, 24], [35, 28, 24, 26], [35, 46, 24, 44], [35, 48, 24, 46, "Number"], [35, 54, 24, 52], [35, 55, 24, 53, "MIN_SAFE_INTEGER"], [35, 71, 24, 69], [35, 72, 24, 70], [36, 6, 26, 4, "_defineProperty"], [36, 21, 26, 19], [36, 22, 26, 20], [36, 26, 26, 24], [36, 28, 26, 26], [36, 46, 26, 44], [36, 48, 26, 46, "Number"], [36, 54, 26, 52], [36, 55, 26, 53, "MIN_SAFE_INTEGER"], [36, 71, 26, 69], [36, 72, 26, 70], [37, 6, 28, 4, "_defineProperty"], [37, 21, 28, 19], [37, 22, 28, 20], [37, 26, 28, 24], [37, 28, 28, 26], [37, 44, 28, 42], [37, 46, 28, 44, "Number"], [37, 52, 28, 50], [37, 53, 28, 51, "MAX_SAFE_INTEGER"], [37, 69, 28, 67], [37, 70, 28, 68], [38, 6, 30, 4, "_defineProperty"], [38, 21, 30, 19], [38, 22, 30, 20], [38, 26, 30, 24], [38, 28, 30, 26], [38, 48, 30, 46], [38, 50, 30, 48, "Number"], [38, 56, 30, 54], [38, 57, 30, 55, "MAX_SAFE_INTEGER"], [38, 73, 30, 71], [38, 74, 30, 72], [39, 6, 32, 4, "_defineProperty"], [39, 21, 32, 19], [39, 22, 32, 20], [39, 26, 32, 24], [39, 28, 32, 26], [39, 46, 32, 44], [39, 48, 32, 46, "Number"], [39, 54, 32, 52], [39, 55, 32, 53, "MIN_SAFE_INTEGER"], [39, 71, 32, 69], [39, 72, 32, 70], [40, 6, 34, 4, "_defineProperty"], [40, 21, 34, 19], [40, 22, 34, 20], [40, 26, 34, 24], [40, 28, 34, 26], [40, 46, 34, 44], [40, 48, 34, 46, "Number"], [40, 54, 34, 52], [40, 55, 34, 53, "MIN_SAFE_INTEGER"], [40, 71, 34, 69], [40, 72, 34, 70], [41, 6, 36, 4, "_defineProperty"], [41, 21, 36, 19], [41, 22, 36, 20], [41, 26, 36, 24], [41, 28, 36, 26], [41, 44, 36, 42], [41, 46, 36, 44, "Number"], [41, 52, 36, 50], [41, 53, 36, 51, "MAX_SAFE_INTEGER"], [41, 69, 36, 67], [41, 70, 36, 68], [42, 6, 38, 4, "_defineProperty"], [42, 21, 38, 19], [42, 22, 38, 20], [42, 26, 38, 24], [42, 28, 38, 26], [42, 42, 38, 40], [42, 44, 38, 42, "Number"], [42, 50, 38, 48], [42, 51, 38, 49, "MAX_SAFE_INTEGER"], [42, 67, 38, 65], [42, 68, 38, 66], [43, 6, 40, 4, "_defineProperty"], [43, 21, 40, 19], [43, 22, 40, 20], [43, 26, 40, 24], [43, 28, 40, 26], [43, 42, 40, 40], [43, 44, 40, 42, "Number"], [43, 50, 40, 48], [43, 51, 40, 49, "MAX_SAFE_INTEGER"], [43, 67, 40, 65], [43, 68, 40, 66], [44, 6, 42, 4, "_defineProperty"], [44, 21, 42, 19], [44, 22, 42, 20], [44, 26, 42, 24], [44, 28, 42, 26], [44, 43, 42, 41], [44, 45, 42, 43, "Number"], [44, 51, 42, 49], [44, 52, 42, 50, "MAX_SAFE_INTEGER"], [44, 68, 42, 66], [44, 69, 42, 67], [45, 6, 44, 4, "_defineProperty"], [45, 21, 44, 19], [45, 22, 44, 20], [45, 26, 44, 24], [45, 28, 44, 26], [45, 41, 44, 39], [45, 43, 44, 41, "DEFAULT_MIN_POINTERS"], [45, 63, 44, 61], [45, 64, 44, 62], [46, 6, 46, 4, "_defineProperty"], [46, 21, 46, 19], [46, 22, 46, 20], [46, 26, 46, 24], [46, 28, 46, 26], [46, 41, 46, 39], [46, 43, 46, 41, "DEFAULT_MAX_POINTERS"], [46, 63, 46, 61], [46, 64, 46, 62], [47, 6, 48, 4, "_defineProperty"], [47, 21, 48, 19], [47, 22, 48, 20], [47, 26, 48, 24], [47, 28, 48, 26], [47, 36, 48, 34], [47, 38, 48, 36], [47, 39, 48, 37], [47, 40, 48, 38], [48, 6, 50, 4, "_defineProperty"], [48, 21, 50, 19], [48, 22, 50, 20], [48, 26, 50, 24], [48, 28, 50, 26], [48, 36, 50, 34], [48, 38, 50, 36], [48, 39, 50, 37], [48, 40, 50, 38], [49, 6, 52, 4, "_defineProperty"], [49, 21, 52, 19], [49, 22, 52, 20], [49, 26, 52, 24], [49, 28, 52, 26], [49, 37, 52, 35], [49, 39, 52, 37], [49, 40, 52, 38], [49, 41, 52, 39], [50, 6, 54, 4, "_defineProperty"], [50, 21, 54, 19], [50, 22, 54, 20], [50, 26, 54, 24], [50, 28, 54, 26], [50, 37, 54, 35], [50, 39, 54, 37], [50, 40, 54, 38], [50, 41, 54, 39], [51, 6, 56, 4, "_defineProperty"], [51, 21, 56, 19], [51, 22, 56, 20], [51, 26, 56, 24], [51, 28, 56, 26], [51, 35, 56, 33], [51, 37, 56, 35], [51, 38, 56, 36], [51, 39, 56, 37], [52, 6, 58, 4, "_defineProperty"], [52, 21, 58, 19], [52, 22, 58, 20], [52, 26, 58, 24], [52, 28, 58, 26], [52, 35, 58, 33], [52, 37, 58, 35], [52, 38, 58, 36], [52, 39, 58, 37], [53, 6, 60, 4, "_defineProperty"], [53, 21, 60, 19], [53, 22, 60, 20], [53, 26, 60, 24], [53, 28, 60, 26], [53, 40, 60, 38], [53, 42, 60, 40], [53, 47, 60, 45], [53, 48, 60, 46], [53, 49, 60, 47], [54, 6, 62, 4, "_defineProperty"], [54, 21, 62, 19], [54, 22, 62, 20], [54, 26, 62, 24], [54, 28, 62, 26], [54, 52, 62, 50], [54, 54, 62, 52], [54, 55, 62, 53], [54, 56, 62, 54], [55, 6, 64, 4, "_defineProperty"], [55, 21, 64, 19], [55, 22, 64, 20], [55, 26, 64, 24], [55, 28, 64, 26], [55, 47, 64, 45], [55, 49, 64, 47], [55, 50, 64, 48], [55, 51, 64, 49], [56, 6, 66, 4, "_defineProperty"], [56, 21, 66, 19], [56, 22, 66, 20], [56, 26, 66, 24], [56, 28, 66, 26], [56, 60, 66, 58], [56, 62, 66, 60], [56, 67, 66, 65], [56, 68, 66, 66], [57, 6, 68, 4, "_defineProperty"], [57, 21, 68, 19], [57, 22, 68, 20], [57, 26, 68, 24], [57, 28, 68, 26], [57, 45, 68, 43], [57, 47, 68, 45], [57, 48, 68, 46], [57, 49, 68, 47], [58, 6, 70, 4, "_defineProperty"], [58, 21, 70, 19], [58, 22, 70, 20], [58, 26, 70, 24], [58, 28, 70, 26], [58, 41, 70, 39], [58, 43, 70, 41, "WheelDevice"], [58, 66, 70, 52], [58, 67, 70, 53, "UNDETERMINED"], [58, 79, 70, 65], [58, 80, 70, 66], [59, 4, 71, 2], [60, 4, 73, 2, "updateGestureConfig"], [60, 23, 73, 21, "updateGestureConfig"], [60, 24, 73, 22], [61, 6, 74, 4, "enabled"], [61, 13, 74, 11], [61, 16, 74, 14], [61, 20, 74, 18], [62, 6, 75, 4], [62, 9, 75, 7, "props"], [63, 4, 76, 2], [63, 5, 76, 3], [63, 7, 76, 5], [64, 6, 77, 4], [64, 10, 77, 8], [64, 11, 77, 9, "resetConfig"], [64, 22, 77, 20], [64, 23, 77, 21], [64, 24, 77, 22], [65, 6, 78, 4], [65, 11, 78, 9], [65, 12, 78, 10, "updateGestureConfig"], [65, 31, 78, 29], [65, 32, 78, 30], [66, 8, 79, 6, "enabled"], [66, 15, 79, 13], [66, 17, 79, 15, "enabled"], [66, 24, 79, 22], [67, 8, 80, 6], [67, 11, 80, 9, "props"], [68, 6, 81, 4], [68, 7, 81, 5], [68, 8, 81, 6], [69, 6, 82, 4], [69, 10, 82, 8], [69, 11, 82, 9, "checkCustomActivationCriteria"], [69, 40, 82, 38], [69, 41, 82, 39], [69, 45, 82, 43], [69, 46, 82, 44, "customActivationProperties"], [69, 72, 82, 70], [69, 73, 82, 71], [70, 6, 84, 4], [70, 10, 84, 8], [70, 14, 84, 12], [70, 15, 84, 13, "config"], [70, 21, 84, 19], [70, 22, 84, 20, "minDist"], [70, 29, 84, 27], [70, 34, 84, 32, "undefined"], [70, 43, 84, 41], [70, 45, 84, 43], [71, 8, 85, 6], [71, 12, 85, 10], [71, 13, 85, 11, "minDistSq"], [71, 22, 85, 20], [71, 25, 85, 23], [71, 29, 85, 27], [71, 30, 85, 28, "config"], [71, 36, 85, 34], [71, 37, 85, 35, "minDist"], [71, 44, 85, 42], [71, 47, 85, 45], [71, 51, 85, 49], [71, 52, 85, 50, "config"], [71, 58, 85, 56], [71, 59, 85, 57, "minDist"], [71, 66, 85, 64], [72, 6, 86, 4], [72, 7, 86, 5], [72, 13, 86, 11], [72, 17, 86, 15], [72, 21, 86, 19], [72, 22, 86, 20, "hasCustomActivationCriteria"], [72, 49, 86, 47], [72, 51, 86, 49], [73, 8, 87, 6], [73, 12, 87, 10], [73, 13, 87, 11, "minDistSq"], [73, 22, 87, 20], [73, 25, 87, 23, "Number"], [73, 31, 87, 29], [73, 32, 87, 30, "MAX_SAFE_INTEGER"], [73, 48, 87, 46], [74, 6, 88, 4], [75, 6, 90, 4], [75, 10, 90, 8], [75, 14, 90, 12], [75, 15, 90, 13, "config"], [75, 21, 90, 19], [75, 22, 90, 20, "minPointers"], [75, 33, 90, 31], [75, 38, 90, 36, "undefined"], [75, 47, 90, 45], [75, 49, 90, 47], [76, 8, 91, 6], [76, 12, 91, 10], [76, 13, 91, 11, "minPointers"], [76, 24, 91, 22], [76, 27, 91, 25], [76, 31, 91, 29], [76, 32, 91, 30, "config"], [76, 38, 91, 36], [76, 39, 91, 37, "minPointers"], [76, 50, 91, 48], [77, 6, 92, 4], [78, 6, 94, 4], [78, 10, 94, 8], [78, 14, 94, 12], [78, 15, 94, 13, "config"], [78, 21, 94, 19], [78, 22, 94, 20, "maxPointers"], [78, 33, 94, 31], [78, 38, 94, 36, "undefined"], [78, 47, 94, 45], [78, 49, 94, 47], [79, 8, 95, 6], [79, 12, 95, 10], [79, 13, 95, 11, "maxPointers"], [79, 24, 95, 22], [79, 27, 95, 25], [79, 31, 95, 29], [79, 32, 95, 30, "config"], [79, 38, 95, 36], [79, 39, 95, 37, "maxPointers"], [79, 50, 95, 48], [80, 6, 96, 4], [81, 6, 98, 4], [81, 10, 98, 8], [81, 14, 98, 12], [81, 15, 98, 13, "config"], [81, 21, 98, 19], [81, 22, 98, 20, "minVelocity"], [81, 33, 98, 31], [81, 38, 98, 36, "undefined"], [81, 47, 98, 45], [81, 49, 98, 47], [82, 8, 99, 6], [82, 12, 99, 10], [82, 13, 99, 11, "minVelocityX"], [82, 25, 99, 23], [82, 28, 99, 26], [82, 32, 99, 30], [82, 33, 99, 31, "config"], [82, 39, 99, 37], [82, 40, 99, 38, "minVelocity"], [82, 51, 99, 49], [83, 8, 100, 6], [83, 12, 100, 10], [83, 13, 100, 11, "minVelocityY"], [83, 25, 100, 23], [83, 28, 100, 26], [83, 32, 100, 30], [83, 33, 100, 31, "config"], [83, 39, 100, 37], [83, 40, 100, 38, "minVelocity"], [83, 51, 100, 49], [84, 6, 101, 4], [85, 6, 103, 4], [85, 10, 103, 8], [85, 14, 103, 12], [85, 15, 103, 13, "config"], [85, 21, 103, 19], [85, 22, 103, 20, "minVelocityX"], [85, 34, 103, 32], [85, 39, 103, 37, "undefined"], [85, 48, 103, 46], [85, 50, 103, 48], [86, 8, 104, 6], [86, 12, 104, 10], [86, 13, 104, 11, "minVelocityX"], [86, 25, 104, 23], [86, 28, 104, 26], [86, 32, 104, 30], [86, 33, 104, 31, "config"], [86, 39, 104, 37], [86, 40, 104, 38, "minVelocityX"], [86, 52, 104, 50], [87, 6, 105, 4], [88, 6, 107, 4], [88, 10, 107, 8], [88, 14, 107, 12], [88, 15, 107, 13, "config"], [88, 21, 107, 19], [88, 22, 107, 20, "minVelocityY"], [88, 34, 107, 32], [88, 39, 107, 37, "undefined"], [88, 48, 107, 46], [88, 50, 107, 48], [89, 8, 108, 6], [89, 12, 108, 10], [89, 13, 108, 11, "minVelocityY"], [89, 25, 108, 23], [89, 28, 108, 26], [89, 32, 108, 30], [89, 33, 108, 31, "config"], [89, 39, 108, 37], [89, 40, 108, 38, "minVelocityY"], [89, 52, 108, 50], [90, 6, 109, 4], [91, 6, 111, 4], [91, 10, 111, 8], [91, 14, 111, 12], [91, 15, 111, 13, "config"], [91, 21, 111, 19], [91, 22, 111, 20, "activateAfterLongPress"], [91, 44, 111, 42], [91, 49, 111, 47, "undefined"], [91, 58, 111, 56], [91, 60, 111, 58], [92, 8, 112, 6], [92, 12, 112, 10], [92, 13, 112, 11, "activateAfterLongPress"], [92, 35, 112, 33], [92, 38, 112, 36], [92, 42, 112, 40], [92, 43, 112, 41, "config"], [92, 49, 112, 47], [92, 50, 112, 48, "activateAfterLongPress"], [92, 72, 112, 70], [93, 6, 113, 4], [94, 6, 115, 4], [94, 10, 115, 8], [94, 14, 115, 12], [94, 15, 115, 13, "config"], [94, 21, 115, 19], [94, 22, 115, 20, "activeOffsetXStart"], [94, 40, 115, 38], [94, 45, 115, 43, "undefined"], [94, 54, 115, 52], [94, 56, 115, 54], [95, 8, 116, 6], [95, 12, 116, 10], [95, 13, 116, 11, "activeOffsetXStart"], [95, 31, 116, 29], [95, 34, 116, 32], [95, 38, 116, 36], [95, 39, 116, 37, "config"], [95, 45, 116, 43], [95, 46, 116, 44, "activeOffsetXStart"], [95, 64, 116, 62], [96, 8, 118, 6], [96, 12, 118, 10], [96, 16, 118, 14], [96, 17, 118, 15, "config"], [96, 23, 118, 21], [96, 24, 118, 22, "activeOffsetXEnd"], [96, 40, 118, 38], [96, 45, 118, 43, "undefined"], [96, 54, 118, 52], [96, 56, 118, 54], [97, 10, 119, 8], [97, 14, 119, 12], [97, 15, 119, 13, "activeOffsetXEnd"], [97, 31, 119, 29], [97, 34, 119, 32, "Number"], [97, 40, 119, 38], [97, 41, 119, 39, "MAX_SAFE_INTEGER"], [97, 57, 119, 55], [98, 8, 120, 6], [99, 6, 121, 4], [100, 6, 123, 4], [100, 10, 123, 8], [100, 14, 123, 12], [100, 15, 123, 13, "config"], [100, 21, 123, 19], [100, 22, 123, 20, "activeOffsetXEnd"], [100, 38, 123, 36], [100, 43, 123, 41, "undefined"], [100, 52, 123, 50], [100, 54, 123, 52], [101, 8, 124, 6], [101, 12, 124, 10], [101, 13, 124, 11, "activeOffsetXEnd"], [101, 29, 124, 27], [101, 32, 124, 30], [101, 36, 124, 34], [101, 37, 124, 35, "config"], [101, 43, 124, 41], [101, 44, 124, 42, "activeOffsetXEnd"], [101, 60, 124, 58], [102, 8, 126, 6], [102, 12, 126, 10], [102, 16, 126, 14], [102, 17, 126, 15, "config"], [102, 23, 126, 21], [102, 24, 126, 22, "activeOffsetXStart"], [102, 42, 126, 40], [102, 47, 126, 45, "undefined"], [102, 56, 126, 54], [102, 58, 126, 56], [103, 10, 127, 8], [103, 14, 127, 12], [103, 15, 127, 13, "activeOffsetXStart"], [103, 33, 127, 31], [103, 36, 127, 34, "Number"], [103, 42, 127, 40], [103, 43, 127, 41, "MIN_SAFE_INTEGER"], [103, 59, 127, 57], [104, 8, 128, 6], [105, 6, 129, 4], [106, 6, 131, 4], [106, 10, 131, 8], [106, 14, 131, 12], [106, 15, 131, 13, "config"], [106, 21, 131, 19], [106, 22, 131, 20, "failOffsetXStart"], [106, 38, 131, 36], [106, 43, 131, 41, "undefined"], [106, 52, 131, 50], [106, 54, 131, 52], [107, 8, 132, 6], [107, 12, 132, 10], [107, 13, 132, 11, "failOffsetXStart"], [107, 29, 132, 27], [107, 32, 132, 30], [107, 36, 132, 34], [107, 37, 132, 35, "config"], [107, 43, 132, 41], [107, 44, 132, 42, "failOffsetXStart"], [107, 60, 132, 58], [108, 8, 134, 6], [108, 12, 134, 10], [108, 16, 134, 14], [108, 17, 134, 15, "config"], [108, 23, 134, 21], [108, 24, 134, 22, "failOffsetXEnd"], [108, 38, 134, 36], [108, 43, 134, 41, "undefined"], [108, 52, 134, 50], [108, 54, 134, 52], [109, 10, 135, 8], [109, 14, 135, 12], [109, 15, 135, 13, "failOffsetXEnd"], [109, 29, 135, 27], [109, 32, 135, 30, "Number"], [109, 38, 135, 36], [109, 39, 135, 37, "MAX_SAFE_INTEGER"], [109, 55, 135, 53], [110, 8, 136, 6], [111, 6, 137, 4], [112, 6, 139, 4], [112, 10, 139, 8], [112, 14, 139, 12], [112, 15, 139, 13, "config"], [112, 21, 139, 19], [112, 22, 139, 20, "failOffsetXEnd"], [112, 36, 139, 34], [112, 41, 139, 39, "undefined"], [112, 50, 139, 48], [112, 52, 139, 50], [113, 8, 140, 6], [113, 12, 140, 10], [113, 13, 140, 11, "failOffsetXEnd"], [113, 27, 140, 25], [113, 30, 140, 28], [113, 34, 140, 32], [113, 35, 140, 33, "config"], [113, 41, 140, 39], [113, 42, 140, 40, "failOffsetXEnd"], [113, 56, 140, 54], [114, 8, 142, 6], [114, 12, 142, 10], [114, 16, 142, 14], [114, 17, 142, 15, "config"], [114, 23, 142, 21], [114, 24, 142, 22, "failOffsetXStart"], [114, 40, 142, 38], [114, 45, 142, 43, "undefined"], [114, 54, 142, 52], [114, 56, 142, 54], [115, 10, 143, 8], [115, 14, 143, 12], [115, 15, 143, 13, "failOffsetXStart"], [115, 31, 143, 29], [115, 34, 143, 32, "Number"], [115, 40, 143, 38], [115, 41, 143, 39, "MIN_SAFE_INTEGER"], [115, 57, 143, 55], [116, 8, 144, 6], [117, 6, 145, 4], [118, 6, 147, 4], [118, 10, 147, 8], [118, 14, 147, 12], [118, 15, 147, 13, "config"], [118, 21, 147, 19], [118, 22, 147, 20, "activeOffsetYStart"], [118, 40, 147, 38], [118, 45, 147, 43, "undefined"], [118, 54, 147, 52], [118, 56, 147, 54], [119, 8, 148, 6], [119, 12, 148, 10], [119, 13, 148, 11, "activeOffsetYStart"], [119, 31, 148, 29], [119, 34, 148, 32], [119, 38, 148, 36], [119, 39, 148, 37, "config"], [119, 45, 148, 43], [119, 46, 148, 44, "activeOffsetYStart"], [119, 64, 148, 62], [120, 8, 150, 6], [120, 12, 150, 10], [120, 16, 150, 14], [120, 17, 150, 15, "config"], [120, 23, 150, 21], [120, 24, 150, 22, "activeOffsetYEnd"], [120, 40, 150, 38], [120, 45, 150, 43, "undefined"], [120, 54, 150, 52], [120, 56, 150, 54], [121, 10, 151, 8], [121, 14, 151, 12], [121, 15, 151, 13, "activeOffsetYEnd"], [121, 31, 151, 29], [121, 34, 151, 32, "Number"], [121, 40, 151, 38], [121, 41, 151, 39, "MAX_SAFE_INTEGER"], [121, 57, 151, 55], [122, 8, 152, 6], [123, 6, 153, 4], [124, 6, 155, 4], [124, 10, 155, 8], [124, 14, 155, 12], [124, 15, 155, 13, "config"], [124, 21, 155, 19], [124, 22, 155, 20, "activeOffsetYEnd"], [124, 38, 155, 36], [124, 43, 155, 41, "undefined"], [124, 52, 155, 50], [124, 54, 155, 52], [125, 8, 156, 6], [125, 12, 156, 10], [125, 13, 156, 11, "activeOffsetYEnd"], [125, 29, 156, 27], [125, 32, 156, 30], [125, 36, 156, 34], [125, 37, 156, 35, "config"], [125, 43, 156, 41], [125, 44, 156, 42, "activeOffsetYEnd"], [125, 60, 156, 58], [126, 8, 158, 6], [126, 12, 158, 10], [126, 16, 158, 14], [126, 17, 158, 15, "config"], [126, 23, 158, 21], [126, 24, 158, 22, "activeOffsetYStart"], [126, 42, 158, 40], [126, 47, 158, 45, "undefined"], [126, 56, 158, 54], [126, 58, 158, 56], [127, 10, 159, 8], [127, 14, 159, 12], [127, 15, 159, 13, "activeOffsetYStart"], [127, 33, 159, 31], [127, 36, 159, 34, "Number"], [127, 42, 159, 40], [127, 43, 159, 41, "MIN_SAFE_INTEGER"], [127, 59, 159, 57], [128, 8, 160, 6], [129, 6, 161, 4], [130, 6, 163, 4], [130, 10, 163, 8], [130, 14, 163, 12], [130, 15, 163, 13, "config"], [130, 21, 163, 19], [130, 22, 163, 20, "failOffsetYStart"], [130, 38, 163, 36], [130, 43, 163, 41, "undefined"], [130, 52, 163, 50], [130, 54, 163, 52], [131, 8, 164, 6], [131, 12, 164, 10], [131, 13, 164, 11, "failOffsetYStart"], [131, 29, 164, 27], [131, 32, 164, 30], [131, 36, 164, 34], [131, 37, 164, 35, "config"], [131, 43, 164, 41], [131, 44, 164, 42, "failOffsetYStart"], [131, 60, 164, 58], [132, 8, 166, 6], [132, 12, 166, 10], [132, 16, 166, 14], [132, 17, 166, 15, "config"], [132, 23, 166, 21], [132, 24, 166, 22, "failOffsetYEnd"], [132, 38, 166, 36], [132, 43, 166, 41, "undefined"], [132, 52, 166, 50], [132, 54, 166, 52], [133, 10, 167, 8], [133, 14, 167, 12], [133, 15, 167, 13, "failOffsetYEnd"], [133, 29, 167, 27], [133, 32, 167, 30, "Number"], [133, 38, 167, 36], [133, 39, 167, 37, "MAX_SAFE_INTEGER"], [133, 55, 167, 53], [134, 8, 168, 6], [135, 6, 169, 4], [136, 6, 171, 4], [136, 10, 171, 8], [136, 14, 171, 12], [136, 15, 171, 13, "config"], [136, 21, 171, 19], [136, 22, 171, 20, "failOffsetYEnd"], [136, 36, 171, 34], [136, 41, 171, 39, "undefined"], [136, 50, 171, 48], [136, 52, 171, 50], [137, 8, 172, 6], [137, 12, 172, 10], [137, 13, 172, 11, "failOffsetYEnd"], [137, 27, 172, 25], [137, 30, 172, 28], [137, 34, 172, 32], [137, 35, 172, 33, "config"], [137, 41, 172, 39], [137, 42, 172, 40, "failOffsetYEnd"], [137, 56, 172, 54], [138, 8, 174, 6], [138, 12, 174, 10], [138, 16, 174, 14], [138, 17, 174, 15, "config"], [138, 23, 174, 21], [138, 24, 174, 22, "failOffsetYStart"], [138, 40, 174, 38], [138, 45, 174, 43, "undefined"], [138, 54, 174, 52], [138, 56, 174, 54], [139, 10, 175, 8], [139, 14, 175, 12], [139, 15, 175, 13, "failOffsetYStart"], [139, 31, 175, 29], [139, 34, 175, 32, "Number"], [139, 40, 175, 38], [139, 41, 175, 39, "MIN_SAFE_INTEGER"], [139, 57, 175, 55], [140, 8, 176, 6], [141, 6, 177, 4], [142, 6, 179, 4], [142, 10, 179, 8], [142, 14, 179, 12], [142, 15, 179, 13, "config"], [142, 21, 179, 19], [142, 22, 179, 20, "enableTrackpadTwoFingerGesture"], [142, 52, 179, 50], [142, 57, 179, 55, "undefined"], [142, 66, 179, 64], [142, 68, 179, 66], [143, 8, 180, 6], [143, 12, 180, 10], [143, 13, 180, 11, "enableTrackpadTwoFingerGesture"], [143, 43, 180, 41], [143, 46, 180, 44], [143, 50, 180, 48], [143, 51, 180, 49, "config"], [143, 57, 180, 55], [143, 58, 180, 56, "enableTrackpadTwoFingerGesture"], [143, 88, 180, 86], [144, 6, 181, 4], [145, 4, 182, 2], [146, 4, 184, 2, "resetConfig"], [146, 15, 184, 13, "resetConfig"], [146, 16, 184, 13], [146, 18, 184, 16], [147, 6, 185, 4], [147, 11, 185, 9], [147, 12, 185, 10, "resetConfig"], [147, 23, 185, 21], [147, 24, 185, 22], [147, 25, 185, 23], [148, 6, 186, 4], [148, 10, 186, 8], [148, 11, 186, 9, "activeOffsetXStart"], [148, 29, 186, 27], [148, 32, 186, 30], [148, 33, 186, 31, "Number"], [148, 39, 186, 37], [148, 40, 186, 38, "MAX_SAFE_INTEGER"], [148, 56, 186, 54], [149, 6, 187, 4], [149, 10, 187, 8], [149, 11, 187, 9, "activeOffsetXEnd"], [149, 27, 187, 25], [149, 30, 187, 28, "Number"], [149, 36, 187, 34], [149, 37, 187, 35, "MIN_SAFE_INTEGER"], [149, 53, 187, 51], [150, 6, 188, 4], [150, 10, 188, 8], [150, 11, 188, 9, "failOffsetXStart"], [150, 27, 188, 25], [150, 30, 188, 28, "Number"], [150, 36, 188, 34], [150, 37, 188, 35, "MIN_SAFE_INTEGER"], [150, 53, 188, 51], [151, 6, 189, 4], [151, 10, 189, 8], [151, 11, 189, 9, "failOffsetXEnd"], [151, 25, 189, 23], [151, 28, 189, 26, "Number"], [151, 34, 189, 32], [151, 35, 189, 33, "MAX_SAFE_INTEGER"], [151, 51, 189, 49], [152, 6, 190, 4], [152, 10, 190, 8], [152, 11, 190, 9, "activeOffsetYStart"], [152, 29, 190, 27], [152, 32, 190, 30, "Number"], [152, 38, 190, 36], [152, 39, 190, 37, "MAX_SAFE_INTEGER"], [152, 55, 190, 53], [153, 6, 191, 4], [153, 10, 191, 8], [153, 11, 191, 9, "activeOffsetYEnd"], [153, 27, 191, 25], [153, 30, 191, 28, "Number"], [153, 36, 191, 34], [153, 37, 191, 35, "MIN_SAFE_INTEGER"], [153, 53, 191, 51], [154, 6, 192, 4], [154, 10, 192, 8], [154, 11, 192, 9, "failOffsetYStart"], [154, 27, 192, 25], [154, 30, 192, 28, "Number"], [154, 36, 192, 34], [154, 37, 192, 35, "MIN_SAFE_INTEGER"], [154, 53, 192, 51], [155, 6, 193, 4], [155, 10, 193, 8], [155, 11, 193, 9, "failOffsetYEnd"], [155, 25, 193, 23], [155, 28, 193, 26, "Number"], [155, 34, 193, 32], [155, 35, 193, 33, "MAX_SAFE_INTEGER"], [155, 51, 193, 49], [156, 6, 194, 4], [156, 10, 194, 8], [156, 11, 194, 9, "minVelocityX"], [156, 23, 194, 21], [156, 26, 194, 24, "Number"], [156, 32, 194, 30], [156, 33, 194, 31, "MAX_SAFE_INTEGER"], [156, 49, 194, 47], [157, 6, 195, 4], [157, 10, 195, 8], [157, 11, 195, 9, "minVelocityY"], [157, 23, 195, 21], [157, 26, 195, 24, "Number"], [157, 32, 195, 30], [157, 33, 195, 31, "MAX_SAFE_INTEGER"], [157, 49, 195, 47], [158, 6, 196, 4], [158, 10, 196, 8], [158, 11, 196, 9, "minVelocitySq"], [158, 24, 196, 22], [158, 27, 196, 25, "Number"], [158, 33, 196, 31], [158, 34, 196, 32, "MAX_SAFE_INTEGER"], [158, 50, 196, 48], [159, 6, 197, 4], [159, 10, 197, 8], [159, 11, 197, 9, "minDistSq"], [159, 20, 197, 18], [159, 23, 197, 21, "DEFAULT_MIN_DIST_SQ"], [159, 42, 197, 40], [160, 6, 198, 4], [160, 10, 198, 8], [160, 11, 198, 9, "minPointers"], [160, 22, 198, 20], [160, 25, 198, 23, "DEFAULT_MIN_POINTERS"], [160, 45, 198, 43], [161, 6, 199, 4], [161, 10, 199, 8], [161, 11, 199, 9, "maxPointers"], [161, 22, 199, 20], [161, 25, 199, 23, "DEFAULT_MAX_POINTERS"], [161, 45, 199, 43], [162, 6, 200, 4], [162, 10, 200, 8], [162, 11, 200, 9, "activateAfterLongPress"], [162, 33, 200, 31], [162, 36, 200, 34], [162, 37, 200, 35], [163, 4, 201, 2], [164, 4, 203, 2, "transformNativeEvent"], [164, 24, 203, 22, "transformNativeEvent"], [164, 25, 203, 22], [164, 27, 203, 25], [165, 6, 204, 4], [165, 12, 204, 10, "translationX"], [165, 24, 204, 22], [165, 27, 204, 25], [165, 31, 204, 29], [165, 32, 204, 30, "getTranslationX"], [165, 47, 204, 45], [165, 48, 204, 46], [165, 49, 204, 47], [166, 6, 205, 4], [166, 12, 205, 10, "translationY"], [166, 24, 205, 22], [166, 27, 205, 25], [166, 31, 205, 29], [166, 32, 205, 30, "getTranslationY"], [166, 47, 205, 45], [166, 48, 205, 46], [166, 49, 205, 47], [167, 6, 206, 4], [167, 13, 206, 11], [168, 8, 206, 13], [168, 11, 206, 16], [168, 16, 206, 21], [168, 17, 206, 22, "transformNativeEvent"], [168, 37, 206, 42], [168, 38, 206, 43], [168, 39, 206, 44], [169, 8, 207, 6, "translationX"], [169, 20, 207, 18], [169, 22, 207, 20, "isNaN"], [169, 27, 207, 25], [169, 28, 207, 26, "translationX"], [169, 40, 207, 38], [169, 41, 207, 39], [169, 44, 207, 42], [169, 45, 207, 43], [169, 48, 207, 46, "translationX"], [169, 60, 207, 58], [170, 8, 208, 6, "translationY"], [170, 20, 208, 18], [170, 22, 208, 20, "isNaN"], [170, 27, 208, 25], [170, 28, 208, 26, "translationY"], [170, 40, 208, 38], [170, 41, 208, 39], [170, 44, 208, 42], [170, 45, 208, 43], [170, 48, 208, 46, "translationY"], [170, 60, 208, 58], [171, 8, 209, 6, "velocityX"], [171, 17, 209, 15], [171, 19, 209, 17], [171, 23, 209, 21], [171, 24, 209, 22, "velocityX"], [171, 33, 209, 31], [172, 8, 210, 6, "velocityY"], [172, 17, 210, 15], [172, 19, 210, 17], [172, 23, 210, 21], [172, 24, 210, 22, "velocityY"], [172, 33, 210, 31], [173, 8, 211, 6, "stylusData"], [173, 18, 211, 16], [173, 20, 211, 18], [173, 24, 211, 22], [173, 25, 211, 23, "stylusData"], [174, 6, 212, 4], [174, 7, 212, 5], [175, 4, 213, 2], [176, 4, 215, 2, "getTranslationX"], [176, 19, 215, 17, "getTranslationX"], [176, 20, 215, 17], [176, 22, 215, 20], [177, 6, 216, 4], [177, 13, 216, 11], [177, 17, 216, 15], [177, 18, 216, 16, "lastX"], [177, 23, 216, 21], [177, 26, 216, 24], [177, 30, 216, 28], [177, 31, 216, 29, "startX"], [177, 37, 216, 35], [177, 40, 216, 38], [177, 44, 216, 42], [177, 45, 216, 43, "offsetX"], [177, 52, 216, 50], [178, 4, 217, 2], [179, 4, 219, 2, "getTranslationY"], [179, 19, 219, 17, "getTranslationY"], [179, 20, 219, 17], [179, 22, 219, 20], [180, 6, 220, 4], [180, 13, 220, 11], [180, 17, 220, 15], [180, 18, 220, 16, "lastY"], [180, 23, 220, 21], [180, 26, 220, 24], [180, 30, 220, 28], [180, 31, 220, 29, "startY"], [180, 37, 220, 35], [180, 40, 220, 38], [180, 44, 220, 42], [180, 45, 220, 43, "offsetY"], [180, 52, 220, 50], [181, 4, 221, 2], [182, 4, 223, 2, "clearActivationTimeout"], [182, 26, 223, 24, "clearActivationTimeout"], [182, 27, 223, 24], [182, 29, 223, 27], [183, 6, 224, 4, "clearTimeout"], [183, 18, 224, 16], [183, 19, 224, 17], [183, 23, 224, 21], [183, 24, 224, 22, "activationTimeout"], [183, 41, 224, 39], [183, 42, 224, 40], [184, 4, 225, 2], [184, 5, 225, 3], [184, 6, 225, 4], [186, 4, 228, 2, "onPointerDown"], [186, 17, 228, 15, "onPointerDown"], [186, 18, 228, 16, "event"], [186, 23, 228, 21], [186, 25, 228, 23], [187, 6, 229, 4], [187, 10, 229, 8], [187, 11, 229, 9], [187, 15, 229, 13], [187, 16, 229, 14, "isButtonInConfig"], [187, 32, 229, 30], [187, 33, 229, 31, "event"], [187, 38, 229, 36], [187, 39, 229, 37, "button"], [187, 45, 229, 43], [187, 46, 229, 44], [187, 48, 229, 46], [188, 8, 230, 6], [189, 6, 231, 4], [190, 6, 233, 4], [190, 10, 233, 8], [190, 11, 233, 9, "tracker"], [190, 18, 233, 16], [190, 19, 233, 17, "addToTracker"], [190, 31, 233, 29], [190, 32, 233, 30, "event"], [190, 37, 233, 35], [190, 38, 233, 36], [191, 6, 234, 4], [191, 10, 234, 8], [191, 11, 234, 9, "stylusData"], [191, 21, 234, 19], [191, 24, 234, 22, "event"], [191, 29, 234, 27], [191, 30, 234, 28, "stylusData"], [191, 40, 234, 38], [192, 6, 235, 4], [192, 11, 235, 9], [192, 12, 235, 10, "onPointerDown"], [192, 25, 235, 23], [192, 26, 235, 24, "event"], [192, 31, 235, 29], [192, 32, 235, 30], [193, 6, 236, 4], [193, 12, 236, 10, "lastCoords"], [193, 22, 236, 20], [193, 25, 236, 23], [193, 29, 236, 27], [193, 30, 236, 28, "tracker"], [193, 37, 236, 35], [193, 38, 236, 36, "getAbsoluteCoordsAverage"], [193, 62, 236, 60], [193, 63, 236, 61], [193, 64, 236, 62], [194, 6, 237, 4], [194, 10, 237, 8], [194, 11, 237, 9, "lastX"], [194, 16, 237, 14], [194, 19, 237, 17, "lastCoords"], [194, 29, 237, 27], [194, 30, 237, 28, "x"], [194, 31, 237, 29], [195, 6, 238, 4], [195, 10, 238, 8], [195, 11, 238, 9, "lastY"], [195, 16, 238, 14], [195, 19, 238, 17, "lastCoords"], [195, 29, 238, 27], [195, 30, 238, 28, "y"], [195, 31, 238, 29], [196, 6, 239, 4], [196, 10, 239, 8], [196, 11, 239, 9, "startX"], [196, 17, 239, 15], [196, 20, 239, 18], [196, 24, 239, 22], [196, 25, 239, 23, "lastX"], [196, 30, 239, 28], [197, 6, 240, 4], [197, 10, 240, 8], [197, 11, 240, 9, "startY"], [197, 17, 240, 15], [197, 20, 240, 18], [197, 24, 240, 22], [197, 25, 240, 23, "lastY"], [197, 30, 240, 28], [198, 6, 241, 4], [198, 10, 241, 8], [198, 11, 241, 9, "tryBegin"], [198, 19, 241, 17], [198, 20, 241, 18, "event"], [198, 25, 241, 23], [198, 26, 241, 24], [199, 6, 242, 4], [199, 10, 242, 8], [199, 11, 242, 9, "<PERSON><PERSON><PERSON>"], [199, 21, 242, 19], [199, 22, 242, 20], [199, 23, 242, 21], [200, 6, 243, 4], [200, 10, 243, 8], [200, 11, 243, 9, "tryToSendTouchEvent"], [200, 30, 243, 28], [200, 31, 243, 29, "event"], [200, 36, 243, 34], [200, 37, 243, 35], [201, 4, 244, 2], [202, 4, 246, 2, "onPointerAdd"], [202, 16, 246, 14, "onPointerAdd"], [202, 17, 246, 15, "event"], [202, 22, 246, 20], [202, 24, 246, 22], [203, 6, 247, 4], [203, 10, 247, 8], [203, 11, 247, 9, "tracker"], [203, 18, 247, 16], [203, 19, 247, 17, "addToTracker"], [203, 31, 247, 29], [203, 32, 247, 30, "event"], [203, 37, 247, 35], [203, 38, 247, 36], [204, 6, 248, 4], [204, 11, 248, 9], [204, 12, 248, 10, "onPointerAdd"], [204, 24, 248, 22], [204, 25, 248, 23, "event"], [204, 30, 248, 28], [204, 31, 248, 29], [205, 6, 249, 4], [205, 10, 249, 8], [205, 11, 249, 9, "tryBegin"], [205, 19, 249, 17], [205, 20, 249, 18, "event"], [205, 25, 249, 23], [205, 26, 249, 24], [206, 6, 250, 4], [206, 10, 250, 8], [206, 11, 250, 9, "offsetX"], [206, 18, 250, 16], [206, 22, 250, 20], [206, 26, 250, 24], [206, 27, 250, 25, "lastX"], [206, 32, 250, 30], [206, 35, 250, 33], [206, 39, 250, 37], [206, 40, 250, 38, "startX"], [206, 46, 250, 44], [207, 6, 251, 4], [207, 10, 251, 8], [207, 11, 251, 9, "offsetY"], [207, 18, 251, 16], [207, 22, 251, 20], [207, 26, 251, 24], [207, 27, 251, 25, "lastY"], [207, 32, 251, 30], [207, 35, 251, 33], [207, 39, 251, 37], [207, 40, 251, 38, "startY"], [207, 46, 251, 44], [208, 6, 252, 4], [208, 12, 252, 10, "lastCoords"], [208, 22, 252, 20], [208, 25, 252, 23], [208, 29, 252, 27], [208, 30, 252, 28, "tracker"], [208, 37, 252, 35], [208, 38, 252, 36, "getAbsoluteCoordsAverage"], [208, 62, 252, 60], [208, 63, 252, 61], [208, 64, 252, 62], [209, 6, 253, 4], [209, 10, 253, 8], [209, 11, 253, 9, "lastX"], [209, 16, 253, 14], [209, 19, 253, 17, "lastCoords"], [209, 29, 253, 27], [209, 30, 253, 28, "x"], [209, 31, 253, 29], [210, 6, 254, 4], [210, 10, 254, 8], [210, 11, 254, 9, "lastY"], [210, 16, 254, 14], [210, 19, 254, 17, "lastCoords"], [210, 29, 254, 27], [210, 30, 254, 28, "y"], [210, 31, 254, 29], [211, 6, 255, 4], [211, 10, 255, 8], [211, 11, 255, 9, "startX"], [211, 17, 255, 15], [211, 20, 255, 18], [211, 24, 255, 22], [211, 25, 255, 23, "lastX"], [211, 30, 255, 28], [212, 6, 256, 4], [212, 10, 256, 8], [212, 11, 256, 9, "startY"], [212, 17, 256, 15], [212, 20, 256, 18], [212, 24, 256, 22], [212, 25, 256, 23, "lastY"], [212, 30, 256, 28], [213, 6, 258, 4], [213, 10, 258, 8], [213, 14, 258, 12], [213, 15, 258, 13, "tracker"], [213, 22, 258, 20], [213, 23, 258, 21, "trackedPointersCount"], [213, 43, 258, 41], [213, 46, 258, 44], [213, 50, 258, 48], [213, 51, 258, 49, "maxPointers"], [213, 62, 258, 60], [213, 64, 258, 62], [214, 8, 259, 6], [214, 12, 259, 10], [214, 16, 259, 14], [214, 17, 259, 15, "state"], [214, 22, 259, 20], [214, 27, 259, 25, "State"], [214, 39, 259, 30], [214, 40, 259, 31, "ACTIVE"], [214, 46, 259, 37], [214, 48, 259, 39], [215, 10, 260, 8], [215, 14, 260, 12], [215, 15, 260, 13, "cancel"], [215, 21, 260, 19], [215, 22, 260, 20], [215, 23, 260, 21], [216, 8, 261, 6], [216, 9, 261, 7], [216, 15, 261, 13], [217, 10, 262, 8], [217, 14, 262, 12], [217, 15, 262, 13, "fail"], [217, 19, 262, 17], [217, 20, 262, 18], [217, 21, 262, 19], [218, 8, 263, 6], [219, 6, 264, 4], [219, 7, 264, 5], [219, 13, 264, 11], [220, 8, 265, 6], [220, 12, 265, 10], [220, 13, 265, 11, "<PERSON><PERSON><PERSON>"], [220, 23, 265, 21], [220, 24, 265, 22], [220, 25, 265, 23], [221, 6, 266, 4], [222, 4, 267, 2], [223, 4, 269, 2, "onPointerUp"], [223, 15, 269, 13, "onPointerUp"], [223, 16, 269, 14, "event"], [223, 21, 269, 19], [223, 23, 269, 21], [224, 6, 270, 4], [224, 10, 270, 8], [224, 11, 270, 9, "stylusData"], [224, 21, 270, 19], [224, 24, 270, 22, "event"], [224, 29, 270, 27], [224, 30, 270, 28, "stylusData"], [224, 40, 270, 38], [225, 6, 271, 4], [225, 11, 271, 9], [225, 12, 271, 10, "onPointerUp"], [225, 23, 271, 21], [225, 24, 271, 22, "event"], [225, 29, 271, 27], [225, 30, 271, 28], [226, 6, 273, 4], [226, 10, 273, 8], [226, 14, 273, 12], [226, 15, 273, 13, "state"], [226, 20, 273, 18], [226, 25, 273, 23, "State"], [226, 37, 273, 28], [226, 38, 273, 29, "ACTIVE"], [226, 44, 273, 35], [226, 46, 273, 37], [227, 8, 274, 6], [227, 14, 274, 12, "lastCoords"], [227, 24, 274, 22], [227, 27, 274, 25], [227, 31, 274, 29], [227, 32, 274, 30, "tracker"], [227, 39, 274, 37], [227, 40, 274, 38, "getAbsoluteCoordsAverage"], [227, 64, 274, 62], [227, 65, 274, 63], [227, 66, 274, 64], [228, 8, 275, 6], [228, 12, 275, 10], [228, 13, 275, 11, "lastX"], [228, 18, 275, 16], [228, 21, 275, 19, "lastCoords"], [228, 31, 275, 29], [228, 32, 275, 30, "x"], [228, 33, 275, 31], [229, 8, 276, 6], [229, 12, 276, 10], [229, 13, 276, 11, "lastY"], [229, 18, 276, 16], [229, 21, 276, 19, "lastCoords"], [229, 31, 276, 29], [229, 32, 276, 30, "y"], [229, 33, 276, 31], [230, 6, 277, 4], [231, 6, 279, 4], [231, 10, 279, 8], [231, 11, 279, 9, "tracker"], [231, 18, 279, 16], [231, 19, 279, 17, "removeFromTracker"], [231, 36, 279, 34], [231, 37, 279, 35, "event"], [231, 42, 279, 40], [231, 43, 279, 41, "pointerId"], [231, 52, 279, 50], [231, 53, 279, 51], [232, 6, 281, 4], [232, 10, 281, 8], [232, 14, 281, 12], [232, 15, 281, 13, "tracker"], [232, 22, 281, 20], [232, 23, 281, 21, "trackedPointersCount"], [232, 43, 281, 41], [232, 48, 281, 46], [232, 49, 281, 47], [232, 51, 281, 49], [233, 8, 282, 6], [233, 12, 282, 10], [233, 13, 282, 11, "clearActivationTimeout"], [233, 35, 282, 33], [233, 36, 282, 34], [233, 37, 282, 35], [234, 6, 283, 4], [235, 6, 285, 4], [235, 10, 285, 8], [235, 14, 285, 12], [235, 15, 285, 13, "state"], [235, 20, 285, 18], [235, 25, 285, 23, "State"], [235, 37, 285, 28], [235, 38, 285, 29, "ACTIVE"], [235, 44, 285, 35], [235, 46, 285, 37], [236, 8, 286, 6], [236, 12, 286, 10], [236, 13, 286, 11, "end"], [236, 16, 286, 14], [236, 17, 286, 15], [236, 18, 286, 16], [237, 6, 287, 4], [237, 7, 287, 5], [237, 13, 287, 11], [238, 8, 288, 6], [238, 12, 288, 10], [238, 13, 288, 11, "resetProgress"], [238, 26, 288, 24], [238, 27, 288, 25], [238, 28, 288, 26], [239, 8, 289, 6], [239, 12, 289, 10], [239, 13, 289, 11, "fail"], [239, 17, 289, 15], [239, 18, 289, 16], [239, 19, 289, 17], [240, 6, 290, 4], [241, 4, 291, 2], [242, 4, 293, 2, "onPointerRemove"], [242, 19, 293, 17, "onPointerRemove"], [242, 20, 293, 18, "event"], [242, 25, 293, 23], [242, 27, 293, 25], [243, 6, 294, 4], [243, 11, 294, 9], [243, 12, 294, 10, "onPointerRemove"], [243, 27, 294, 25], [243, 28, 294, 26, "event"], [243, 33, 294, 31], [243, 34, 294, 32], [244, 6, 295, 4], [244, 10, 295, 8], [244, 11, 295, 9, "tracker"], [244, 18, 295, 16], [244, 19, 295, 17, "removeFromTracker"], [244, 36, 295, 34], [244, 37, 295, 35, "event"], [244, 42, 295, 40], [244, 43, 295, 41, "pointerId"], [244, 52, 295, 50], [244, 53, 295, 51], [245, 6, 296, 4], [245, 10, 296, 8], [245, 11, 296, 9, "offsetX"], [245, 18, 296, 16], [245, 22, 296, 20], [245, 26, 296, 24], [245, 27, 296, 25, "lastX"], [245, 32, 296, 30], [245, 35, 296, 33], [245, 39, 296, 37], [245, 40, 296, 38, "startX"], [245, 46, 296, 44], [246, 6, 297, 4], [246, 10, 297, 8], [246, 11, 297, 9, "offsetY"], [246, 18, 297, 16], [246, 22, 297, 20], [246, 26, 297, 24], [246, 27, 297, 25, "lastY"], [246, 32, 297, 30], [246, 35, 297, 33], [246, 39, 297, 37], [246, 40, 297, 38, "startY"], [246, 46, 297, 44], [247, 6, 298, 4], [247, 12, 298, 10, "lastCoords"], [247, 22, 298, 20], [247, 25, 298, 23], [247, 29, 298, 27], [247, 30, 298, 28, "tracker"], [247, 37, 298, 35], [247, 38, 298, 36, "getAbsoluteCoordsAverage"], [247, 62, 298, 60], [247, 63, 298, 61], [247, 64, 298, 62], [248, 6, 299, 4], [248, 10, 299, 8], [248, 11, 299, 9, "lastX"], [248, 16, 299, 14], [248, 19, 299, 17, "lastCoords"], [248, 29, 299, 27], [248, 30, 299, 28, "x"], [248, 31, 299, 29], [249, 6, 300, 4], [249, 10, 300, 8], [249, 11, 300, 9, "lastY"], [249, 16, 300, 14], [249, 19, 300, 17, "lastCoords"], [249, 29, 300, 27], [249, 30, 300, 28, "y"], [249, 31, 300, 29], [250, 6, 301, 4], [250, 10, 301, 8], [250, 11, 301, 9, "startX"], [250, 17, 301, 15], [250, 20, 301, 18], [250, 24, 301, 22], [250, 25, 301, 23, "lastX"], [250, 30, 301, 28], [251, 6, 302, 4], [251, 10, 302, 8], [251, 11, 302, 9, "startY"], [251, 17, 302, 15], [251, 20, 302, 18], [251, 24, 302, 22], [251, 25, 302, 23, "lastY"], [251, 30, 302, 28], [252, 6, 304, 4], [252, 10, 304, 8], [252, 12, 304, 10], [252, 16, 304, 14], [252, 17, 304, 15, "state"], [252, 22, 304, 20], [252, 27, 304, 25, "State"], [252, 39, 304, 30], [252, 40, 304, 31, "ACTIVE"], [252, 46, 304, 37], [252, 50, 304, 41], [252, 54, 304, 45], [252, 55, 304, 46, "tracker"], [252, 62, 304, 53], [252, 63, 304, 54, "trackedPointersCount"], [252, 83, 304, 74], [252, 86, 304, 77], [252, 90, 304, 81], [252, 91, 304, 82, "minPointers"], [252, 102, 304, 93], [252, 103, 304, 94], [252, 105, 304, 96], [253, 8, 305, 6], [253, 12, 305, 10], [253, 13, 305, 11, "<PERSON><PERSON><PERSON>"], [253, 23, 305, 21], [253, 24, 305, 22], [253, 25, 305, 23], [254, 6, 306, 4], [255, 4, 307, 2], [256, 4, 309, 2, "onPointerMove"], [256, 17, 309, 15, "onPointerMove"], [256, 18, 309, 16, "event"], [256, 23, 309, 21], [256, 25, 309, 23], [257, 6, 310, 4], [257, 10, 310, 8], [257, 11, 310, 9, "tracker"], [257, 18, 310, 16], [257, 19, 310, 17, "track"], [257, 24, 310, 22], [257, 25, 310, 23, "event"], [257, 30, 310, 28], [257, 31, 310, 29], [258, 6, 311, 4], [258, 10, 311, 8], [258, 11, 311, 9, "stylusData"], [258, 21, 311, 19], [258, 24, 311, 22, "event"], [258, 29, 311, 27], [258, 30, 311, 28, "stylusData"], [258, 40, 311, 38], [259, 6, 312, 4], [259, 12, 312, 10, "lastCoords"], [259, 22, 312, 20], [259, 25, 312, 23], [259, 29, 312, 27], [259, 30, 312, 28, "tracker"], [259, 37, 312, 35], [259, 38, 312, 36, "getAbsoluteCoordsAverage"], [259, 62, 312, 60], [259, 63, 312, 61], [259, 64, 312, 62], [260, 6, 313, 4], [260, 10, 313, 8], [260, 11, 313, 9, "lastX"], [260, 16, 313, 14], [260, 19, 313, 17, "lastCoords"], [260, 29, 313, 27], [260, 30, 313, 28, "x"], [260, 31, 313, 29], [261, 6, 314, 4], [261, 10, 314, 8], [261, 11, 314, 9, "lastY"], [261, 16, 314, 14], [261, 19, 314, 17, "lastCoords"], [261, 29, 314, 27], [261, 30, 314, 28, "y"], [261, 31, 314, 29], [262, 6, 315, 4], [262, 12, 315, 10, "velocity"], [262, 20, 315, 18], [262, 23, 315, 21], [262, 27, 315, 25], [262, 28, 315, 26, "tracker"], [262, 35, 315, 33], [262, 36, 315, 34, "getVelocity"], [262, 47, 315, 45], [262, 48, 315, 46, "event"], [262, 53, 315, 51], [262, 54, 315, 52, "pointerId"], [262, 63, 315, 61], [262, 64, 315, 62], [263, 6, 316, 4], [263, 10, 316, 8], [263, 11, 316, 9, "velocityX"], [263, 20, 316, 18], [263, 23, 316, 21, "velocity"], [263, 31, 316, 29], [263, 32, 316, 30, "x"], [263, 33, 316, 31], [264, 6, 317, 4], [264, 10, 317, 8], [264, 11, 317, 9, "velocityY"], [264, 20, 317, 18], [264, 23, 317, 21, "velocity"], [264, 31, 317, 29], [264, 32, 317, 30, "y"], [264, 33, 317, 31], [265, 6, 318, 4], [265, 10, 318, 8], [265, 11, 318, 9, "<PERSON><PERSON><PERSON>"], [265, 21, 318, 19], [265, 22, 318, 20], [265, 23, 318, 21], [266, 6, 319, 4], [266, 11, 319, 9], [266, 12, 319, 10, "onPointerMove"], [266, 25, 319, 23], [266, 26, 319, 24, "event"], [266, 31, 319, 29], [266, 32, 319, 30], [267, 4, 320, 2], [268, 4, 322, 2, "onPointerOutOfBounds"], [268, 24, 322, 22, "onPointerOutOfBounds"], [268, 25, 322, 23, "event"], [268, 30, 322, 28], [268, 32, 322, 30], [269, 6, 323, 4], [269, 10, 323, 8], [269, 14, 323, 12], [269, 15, 323, 13, "shouldCancelWhenOutside"], [269, 38, 323, 36], [269, 40, 323, 38], [270, 8, 324, 6], [271, 6, 325, 4], [272, 6, 327, 4], [272, 10, 327, 8], [272, 11, 327, 9, "tracker"], [272, 18, 327, 16], [272, 19, 327, 17, "track"], [272, 24, 327, 22], [272, 25, 327, 23, "event"], [272, 30, 327, 28], [272, 31, 327, 29], [273, 6, 328, 4], [273, 10, 328, 8], [273, 11, 328, 9, "stylusData"], [273, 21, 328, 19], [273, 24, 328, 22, "event"], [273, 29, 328, 27], [273, 30, 328, 28, "stylusData"], [273, 40, 328, 38], [274, 6, 329, 4], [274, 12, 329, 10, "lastCoords"], [274, 22, 329, 20], [274, 25, 329, 23], [274, 29, 329, 27], [274, 30, 329, 28, "tracker"], [274, 37, 329, 35], [274, 38, 329, 36, "getAbsoluteCoordsAverage"], [274, 62, 329, 60], [274, 63, 329, 61], [274, 64, 329, 62], [275, 6, 330, 4], [275, 10, 330, 8], [275, 11, 330, 9, "lastX"], [275, 16, 330, 14], [275, 19, 330, 17, "lastCoords"], [275, 29, 330, 27], [275, 30, 330, 28, "x"], [275, 31, 330, 29], [276, 6, 331, 4], [276, 10, 331, 8], [276, 11, 331, 9, "lastY"], [276, 16, 331, 14], [276, 19, 331, 17, "lastCoords"], [276, 29, 331, 27], [276, 30, 331, 28, "y"], [276, 31, 331, 29], [277, 6, 332, 4], [277, 12, 332, 10, "velocity"], [277, 20, 332, 18], [277, 23, 332, 21], [277, 27, 332, 25], [277, 28, 332, 26, "tracker"], [277, 35, 332, 33], [277, 36, 332, 34, "getVelocity"], [277, 47, 332, 45], [277, 48, 332, 46, "event"], [277, 53, 332, 51], [277, 54, 332, 52, "pointerId"], [277, 63, 332, 61], [277, 64, 332, 62], [278, 6, 333, 4], [278, 10, 333, 8], [278, 11, 333, 9, "velocityX"], [278, 20, 333, 18], [278, 23, 333, 21, "velocity"], [278, 31, 333, 29], [278, 32, 333, 30, "x"], [278, 33, 333, 31], [279, 6, 334, 4], [279, 10, 334, 8], [279, 11, 334, 9, "velocityY"], [279, 20, 334, 18], [279, 23, 334, 21, "velocity"], [279, 31, 334, 29], [279, 32, 334, 30, "y"], [279, 33, 334, 31], [280, 6, 335, 4], [280, 10, 335, 8], [280, 11, 335, 9, "<PERSON><PERSON><PERSON>"], [280, 21, 335, 19], [280, 22, 335, 20], [280, 23, 335, 21], [281, 6, 337, 4], [281, 10, 337, 8], [281, 14, 337, 12], [281, 15, 337, 13, "state"], [281, 20, 337, 18], [281, 25, 337, 23, "State"], [281, 37, 337, 28], [281, 38, 337, 29, "ACTIVE"], [281, 44, 337, 35], [281, 46, 337, 37], [282, 8, 338, 6], [282, 13, 338, 11], [282, 14, 338, 12, "onPointerOutOfBounds"], [282, 34, 338, 32], [282, 35, 338, 33, "event"], [282, 40, 338, 38], [282, 41, 338, 39], [283, 6, 339, 4], [284, 4, 340, 2], [285, 4, 342, 2, "scheduleWheelEnd"], [285, 20, 342, 18, "scheduleWheelEnd"], [285, 21, 342, 19, "event"], [285, 26, 342, 24], [285, 28, 342, 26], [286, 6, 343, 4, "clearTimeout"], [286, 18, 343, 16], [286, 19, 343, 17], [286, 23, 343, 21], [286, 24, 343, 22, "endWheelTimeout"], [286, 39, 343, 37], [286, 40, 343, 38], [287, 6, 344, 4], [287, 10, 344, 8], [287, 11, 344, 9, "endWheelTimeout"], [287, 26, 344, 24], [287, 29, 344, 27, "setTimeout"], [287, 39, 344, 37], [287, 40, 344, 38], [287, 46, 344, 44], [288, 8, 345, 6], [288, 12, 345, 10], [288, 16, 345, 14], [288, 17, 345, 15, "state"], [288, 22, 345, 20], [288, 27, 345, 25, "State"], [288, 39, 345, 30], [288, 40, 345, 31, "ACTIVE"], [288, 46, 345, 37], [288, 48, 345, 39], [289, 10, 346, 8], [289, 14, 346, 12], [289, 15, 346, 13, "end"], [289, 18, 346, 16], [289, 19, 346, 17], [289, 20, 346, 18], [290, 10, 347, 8], [290, 14, 347, 12], [290, 15, 347, 13, "tracker"], [290, 22, 347, 20], [290, 23, 347, 21, "removeFromTracker"], [290, 40, 347, 38], [290, 41, 347, 39, "event"], [290, 46, 347, 44], [290, 47, 347, 45, "pointerId"], [290, 56, 347, 54], [290, 57, 347, 55], [291, 10, 348, 8], [291, 14, 348, 12], [291, 15, 348, 13, "state"], [291, 20, 348, 18], [291, 23, 348, 21, "State"], [291, 35, 348, 26], [291, 36, 348, 27, "UNDETERMINED"], [291, 48, 348, 39], [292, 8, 349, 6], [293, 8, 351, 6], [293, 12, 351, 10], [293, 13, 351, 11, "wheelDevice"], [293, 24, 351, 22], [293, 27, 351, 25, "WheelDevice"], [293, 50, 351, 36], [293, 51, 351, 37, "UNDETERMINED"], [293, 63, 351, 49], [294, 6, 352, 4], [294, 7, 352, 5], [294, 9, 352, 7], [294, 11, 352, 9], [294, 12, 352, 10], [295, 4, 353, 2], [296, 4, 355, 2, "onWheel"], [296, 11, 355, 9, "onWheel"], [296, 12, 355, 10, "event"], [296, 17, 355, 15], [296, 19, 355, 17], [297, 6, 356, 4], [297, 10, 356, 8], [297, 14, 356, 12], [297, 15, 356, 13, "wheelDevice"], [297, 26, 356, 24], [297, 31, 356, 29, "WheelDevice"], [297, 54, 356, 40], [297, 55, 356, 41, "MOUSE"], [297, 60, 356, 46], [297, 64, 356, 50], [297, 65, 356, 51], [297, 69, 356, 55], [297, 70, 356, 56, "enableTrackpadTwoFingerGesture"], [297, 100, 356, 86], [297, 102, 356, 88], [298, 8, 357, 6], [299, 6, 358, 4], [300, 6, 360, 4], [300, 10, 360, 8], [300, 14, 360, 12], [300, 15, 360, 13, "state"], [300, 20, 360, 18], [300, 25, 360, 23, "State"], [300, 37, 360, 28], [300, 38, 360, 29, "UNDETERMINED"], [300, 50, 360, 41], [300, 52, 360, 43], [301, 8, 361, 6], [301, 12, 361, 10], [301, 13, 361, 11, "wheelDevice"], [301, 24, 361, 22], [301, 27, 361, 25, "event"], [301, 32, 361, 30], [301, 33, 361, 31, "wheelDeltaY"], [301, 44, 361, 42], [301, 47, 361, 45], [301, 50, 361, 48], [301, 55, 361, 53], [301, 56, 361, 54], [301, 59, 361, 57, "WheelDevice"], [301, 82, 361, 68], [301, 83, 361, 69, "TOUCHPAD"], [301, 91, 361, 77], [301, 94, 361, 80, "WheelDevice"], [301, 117, 361, 91], [301, 118, 361, 92, "MOUSE"], [301, 123, 361, 97], [302, 8, 363, 6], [302, 12, 363, 10], [302, 16, 363, 14], [302, 17, 363, 15, "wheelDevice"], [302, 28, 363, 26], [302, 33, 363, 31, "WheelDevice"], [302, 56, 363, 42], [302, 57, 363, 43, "MOUSE"], [302, 62, 363, 48], [302, 64, 363, 50], [303, 10, 364, 8], [303, 14, 364, 12], [303, 15, 364, 13, "scheduleWheelEnd"], [303, 31, 364, 29], [303, 32, 364, 30, "event"], [303, 37, 364, 35], [303, 38, 364, 36], [304, 10, 365, 8], [305, 8, 366, 6], [306, 8, 368, 6], [306, 12, 368, 10], [306, 13, 368, 11, "tracker"], [306, 20, 368, 18], [306, 21, 368, 19, "addToTracker"], [306, 33, 368, 31], [306, 34, 368, 32, "event"], [306, 39, 368, 37], [306, 40, 368, 38], [307, 8, 369, 6], [307, 14, 369, 12, "lastCoords"], [307, 24, 369, 22], [307, 27, 369, 25], [307, 31, 369, 29], [307, 32, 369, 30, "tracker"], [307, 39, 369, 37], [307, 40, 369, 38, "getAbsoluteCoordsAverage"], [307, 64, 369, 62], [307, 65, 369, 63], [307, 66, 369, 64], [308, 8, 370, 6], [308, 12, 370, 10], [308, 13, 370, 11, "lastX"], [308, 18, 370, 16], [308, 21, 370, 19, "lastCoords"], [308, 31, 370, 29], [308, 32, 370, 30, "x"], [308, 33, 370, 31], [309, 8, 371, 6], [309, 12, 371, 10], [309, 13, 371, 11, "lastY"], [309, 18, 371, 16], [309, 21, 371, 19, "lastCoords"], [309, 31, 371, 29], [309, 32, 371, 30, "y"], [309, 33, 371, 31], [310, 8, 372, 6], [310, 12, 372, 10], [310, 13, 372, 11, "startX"], [310, 19, 372, 17], [310, 22, 372, 20], [310, 26, 372, 24], [310, 27, 372, 25, "lastX"], [310, 32, 372, 30], [311, 8, 373, 6], [311, 12, 373, 10], [311, 13, 373, 11, "startY"], [311, 19, 373, 17], [311, 22, 373, 20], [311, 26, 373, 24], [311, 27, 373, 25, "lastY"], [311, 32, 373, 30], [312, 8, 374, 6], [312, 12, 374, 10], [312, 13, 374, 11, "begin"], [312, 18, 374, 16], [312, 19, 374, 17], [312, 20, 374, 18], [313, 8, 375, 6], [313, 12, 375, 10], [313, 13, 375, 11, "activate"], [313, 21, 375, 19], [313, 22, 375, 20], [313, 23, 375, 21], [314, 6, 376, 4], [315, 6, 378, 4], [315, 10, 378, 8], [315, 11, 378, 9, "tracker"], [315, 18, 378, 16], [315, 19, 378, 17, "track"], [315, 24, 378, 22], [315, 25, 378, 23, "event"], [315, 30, 378, 28], [315, 31, 378, 29], [316, 6, 379, 4], [316, 12, 379, 10, "lastCoords"], [316, 22, 379, 20], [316, 25, 379, 23], [316, 29, 379, 27], [316, 30, 379, 28, "tracker"], [316, 37, 379, 35], [316, 38, 379, 36, "getAbsoluteCoordsAverage"], [316, 62, 379, 60], [316, 63, 379, 61], [316, 64, 379, 62], [317, 6, 380, 4], [317, 10, 380, 8], [317, 11, 380, 9, "lastX"], [317, 16, 380, 14], [317, 19, 380, 17, "lastCoords"], [317, 29, 380, 27], [317, 30, 380, 28, "x"], [317, 31, 380, 29], [318, 6, 381, 4], [318, 10, 381, 8], [318, 11, 381, 9, "lastY"], [318, 16, 381, 14], [318, 19, 381, 17, "lastCoords"], [318, 29, 381, 27], [318, 30, 381, 28, "y"], [318, 31, 381, 29], [319, 6, 382, 4], [319, 12, 382, 10, "velocity"], [319, 20, 382, 18], [319, 23, 382, 21], [319, 27, 382, 25], [319, 28, 382, 26, "tracker"], [319, 35, 382, 33], [319, 36, 382, 34, "getVelocity"], [319, 47, 382, 45], [319, 48, 382, 46, "event"], [319, 53, 382, 51], [319, 54, 382, 52, "pointerId"], [319, 63, 382, 61], [319, 64, 382, 62], [320, 6, 383, 4], [320, 10, 383, 8], [320, 11, 383, 9, "velocityX"], [320, 20, 383, 18], [320, 23, 383, 21, "velocity"], [320, 31, 383, 29], [320, 32, 383, 30, "x"], [320, 33, 383, 31], [321, 6, 384, 4], [321, 10, 384, 8], [321, 11, 384, 9, "velocityY"], [321, 20, 384, 18], [321, 23, 384, 21, "velocity"], [321, 31, 384, 29], [321, 32, 384, 30, "y"], [321, 33, 384, 31], [322, 6, 385, 4], [322, 10, 385, 8], [322, 11, 385, 9, "tryToSendMoveEvent"], [322, 29, 385, 27], [322, 30, 385, 28], [322, 35, 385, 33], [322, 37, 385, 35, "event"], [322, 42, 385, 40], [322, 43, 385, 41], [323, 6, 386, 4], [323, 10, 386, 8], [323, 11, 386, 9, "scheduleWheelEnd"], [323, 27, 386, 25], [323, 28, 386, 26, "event"], [323, 33, 386, 31], [323, 34, 386, 32], [324, 4, 387, 2], [325, 4, 389, 2, "shouldActivate"], [325, 18, 389, 16, "shouldActivate"], [325, 19, 389, 16], [325, 21, 389, 19], [326, 6, 390, 4], [326, 12, 390, 10, "dx"], [326, 14, 390, 12], [326, 17, 390, 15], [326, 21, 390, 19], [326, 22, 390, 20, "getTranslationX"], [326, 37, 390, 35], [326, 38, 390, 36], [326, 39, 390, 37], [327, 6, 392, 4], [327, 10, 392, 8], [327, 14, 392, 12], [327, 15, 392, 13, "activeOffsetXStart"], [327, 33, 392, 31], [327, 38, 392, 36, "Number"], [327, 44, 392, 42], [327, 45, 392, 43, "MAX_SAFE_INTEGER"], [327, 61, 392, 59], [327, 65, 392, 63, "dx"], [327, 67, 392, 65], [327, 70, 392, 68], [327, 74, 392, 72], [327, 75, 392, 73, "activeOffsetXStart"], [327, 93, 392, 91], [327, 95, 392, 93], [328, 8, 393, 6], [328, 15, 393, 13], [328, 19, 393, 17], [329, 6, 394, 4], [330, 6, 396, 4], [330, 10, 396, 8], [330, 14, 396, 12], [330, 15, 396, 13, "activeOffsetXEnd"], [330, 31, 396, 29], [330, 36, 396, 34, "Number"], [330, 42, 396, 40], [330, 43, 396, 41, "MIN_SAFE_INTEGER"], [330, 59, 396, 57], [330, 63, 396, 61, "dx"], [330, 65, 396, 63], [330, 68, 396, 66], [330, 72, 396, 70], [330, 73, 396, 71, "activeOffsetXEnd"], [330, 89, 396, 87], [330, 91, 396, 89], [331, 8, 397, 6], [331, 15, 397, 13], [331, 19, 397, 17], [332, 6, 398, 4], [333, 6, 400, 4], [333, 12, 400, 10, "dy"], [333, 14, 400, 12], [333, 17, 400, 15], [333, 21, 400, 19], [333, 22, 400, 20, "getTranslationY"], [333, 37, 400, 35], [333, 38, 400, 36], [333, 39, 400, 37], [334, 6, 402, 4], [334, 10, 402, 8], [334, 14, 402, 12], [334, 15, 402, 13, "activeOffsetYStart"], [334, 33, 402, 31], [334, 38, 402, 36, "Number"], [334, 44, 402, 42], [334, 45, 402, 43, "MAX_SAFE_INTEGER"], [334, 61, 402, 59], [334, 65, 402, 63, "dy"], [334, 67, 402, 65], [334, 70, 402, 68], [334, 74, 402, 72], [334, 75, 402, 73, "activeOffsetYStart"], [334, 93, 402, 91], [334, 95, 402, 93], [335, 8, 403, 6], [335, 15, 403, 13], [335, 19, 403, 17], [336, 6, 404, 4], [337, 6, 406, 4], [337, 10, 406, 8], [337, 14, 406, 12], [337, 15, 406, 13, "activeOffsetYEnd"], [337, 31, 406, 29], [337, 36, 406, 34, "Number"], [337, 42, 406, 40], [337, 43, 406, 41, "MIN_SAFE_INTEGER"], [337, 59, 406, 57], [337, 63, 406, 61, "dy"], [337, 65, 406, 63], [337, 68, 406, 66], [337, 72, 406, 70], [337, 73, 406, 71, "activeOffsetYEnd"], [337, 89, 406, 87], [337, 91, 406, 89], [338, 8, 407, 6], [338, 15, 407, 13], [338, 19, 407, 17], [339, 6, 408, 4], [340, 6, 410, 4], [340, 12, 410, 10, "distanceSq"], [340, 22, 410, 20], [340, 25, 410, 23, "dx"], [340, 27, 410, 25], [340, 30, 410, 28, "dx"], [340, 32, 410, 30], [340, 35, 410, 33, "dy"], [340, 37, 410, 35], [340, 40, 410, 38, "dy"], [340, 42, 410, 40], [341, 6, 412, 4], [341, 10, 412, 8], [341, 14, 412, 12], [341, 15, 412, 13, "minDistSq"], [341, 24, 412, 22], [341, 29, 412, 27, "Number"], [341, 35, 412, 33], [341, 36, 412, 34, "MAX_SAFE_INTEGER"], [341, 52, 412, 50], [341, 56, 412, 54, "distanceSq"], [341, 66, 412, 64], [341, 70, 412, 68], [341, 74, 412, 72], [341, 75, 412, 73, "minDistSq"], [341, 84, 412, 82], [341, 86, 412, 84], [342, 8, 413, 6], [342, 15, 413, 13], [342, 19, 413, 17], [343, 6, 414, 4], [344, 6, 416, 4], [344, 12, 416, 10, "vx"], [344, 14, 416, 12], [344, 17, 416, 15], [344, 21, 416, 19], [344, 22, 416, 20, "velocityX"], [344, 31, 416, 29], [345, 6, 418, 4], [345, 10, 418, 8], [345, 14, 418, 12], [345, 15, 418, 13, "minVelocityX"], [345, 27, 418, 25], [345, 32, 418, 30, "Number"], [345, 38, 418, 36], [345, 39, 418, 37, "MAX_SAFE_INTEGER"], [345, 55, 418, 53], [345, 60, 418, 58], [345, 64, 418, 62], [345, 65, 418, 63, "minVelocityX"], [345, 77, 418, 75], [345, 80, 418, 78], [345, 81, 418, 79], [345, 85, 418, 83, "vx"], [345, 87, 418, 85], [345, 91, 418, 89], [345, 95, 418, 93], [345, 96, 418, 94, "minVelocityX"], [345, 108, 418, 106], [345, 112, 418, 110], [345, 116, 418, 114], [345, 117, 418, 115, "minVelocityX"], [345, 129, 418, 127], [345, 133, 418, 131], [345, 134, 418, 132], [345, 138, 418, 136], [345, 142, 418, 140], [345, 143, 418, 141, "minVelocityX"], [345, 155, 418, 153], [345, 159, 418, 157, "vx"], [345, 161, 418, 159], [345, 162, 418, 160], [345, 164, 418, 162], [346, 8, 419, 6], [346, 15, 419, 13], [346, 19, 419, 17], [347, 6, 420, 4], [348, 6, 422, 4], [348, 12, 422, 10, "vy"], [348, 14, 422, 12], [348, 17, 422, 15], [348, 21, 422, 19], [348, 22, 422, 20, "velocityY"], [348, 31, 422, 29], [349, 6, 424, 4], [349, 10, 424, 8], [349, 14, 424, 12], [349, 15, 424, 13, "minVelocityY"], [349, 27, 424, 25], [349, 32, 424, 30, "Number"], [349, 38, 424, 36], [349, 39, 424, 37, "MAX_SAFE_INTEGER"], [349, 55, 424, 53], [349, 60, 424, 58], [349, 64, 424, 62], [349, 65, 424, 63, "minVelocityY"], [349, 77, 424, 75], [349, 80, 424, 78], [349, 81, 424, 79], [349, 85, 424, 83, "vy"], [349, 87, 424, 85], [349, 91, 424, 89], [349, 95, 424, 93], [349, 96, 424, 94, "minVelocityY"], [349, 108, 424, 106], [349, 112, 424, 110], [349, 116, 424, 114], [349, 117, 424, 115, "minVelocityY"], [349, 129, 424, 127], [349, 133, 424, 131], [349, 134, 424, 132], [349, 138, 424, 136], [349, 142, 424, 140], [349, 143, 424, 141, "minVelocityY"], [349, 155, 424, 153], [349, 159, 424, 157, "vy"], [349, 161, 424, 159], [349, 162, 424, 160], [349, 164, 424, 162], [350, 8, 425, 6], [350, 15, 425, 13], [350, 19, 425, 17], [351, 6, 426, 4], [352, 6, 428, 4], [352, 12, 428, 10, "velocitySq"], [352, 22, 428, 20], [352, 25, 428, 23, "vx"], [352, 27, 428, 25], [352, 30, 428, 28, "vx"], [352, 32, 428, 30], [352, 35, 428, 33, "vy"], [352, 37, 428, 35], [352, 40, 428, 38, "vy"], [352, 42, 428, 40], [353, 6, 429, 4], [353, 13, 429, 11], [353, 17, 429, 15], [353, 18, 429, 16, "minVelocitySq"], [353, 31, 429, 29], [353, 36, 429, 34, "Number"], [353, 42, 429, 40], [353, 43, 429, 41, "MAX_SAFE_INTEGER"], [353, 59, 429, 57], [353, 63, 429, 61, "velocitySq"], [353, 73, 429, 71], [353, 77, 429, 75], [353, 81, 429, 79], [353, 82, 429, 80, "minVelocitySq"], [353, 95, 429, 93], [354, 4, 430, 2], [355, 4, 432, 2, "shouldFail"], [355, 14, 432, 12, "shouldFail"], [355, 15, 432, 12], [355, 17, 432, 15], [356, 6, 433, 4], [356, 12, 433, 10, "dx"], [356, 14, 433, 12], [356, 17, 433, 15], [356, 21, 433, 19], [356, 22, 433, 20, "getTranslationX"], [356, 37, 433, 35], [356, 38, 433, 36], [356, 39, 433, 37], [357, 6, 434, 4], [357, 12, 434, 10, "dy"], [357, 14, 434, 12], [357, 17, 434, 15], [357, 21, 434, 19], [357, 22, 434, 20, "getTranslationY"], [357, 37, 434, 35], [357, 38, 434, 36], [357, 39, 434, 37], [358, 6, 435, 4], [358, 12, 435, 10, "distanceSq"], [358, 22, 435, 20], [358, 25, 435, 23, "dx"], [358, 27, 435, 25], [358, 30, 435, 28, "dx"], [358, 32, 435, 30], [358, 35, 435, 33, "dy"], [358, 37, 435, 35], [358, 40, 435, 38, "dy"], [358, 42, 435, 40], [359, 6, 437, 4], [359, 10, 437, 8], [359, 14, 437, 12], [359, 15, 437, 13, "activateAfterLongPress"], [359, 37, 437, 35], [359, 40, 437, 38], [359, 41, 437, 39], [359, 45, 437, 43, "distanceSq"], [359, 55, 437, 53], [359, 58, 437, 56, "DEFAULT_MIN_DIST_SQ"], [359, 77, 437, 75], [359, 79, 437, 77], [360, 8, 438, 6], [360, 12, 438, 10], [360, 13, 438, 11, "clearActivationTimeout"], [360, 35, 438, 33], [360, 36, 438, 34], [360, 37, 438, 35], [361, 8, 439, 6], [361, 15, 439, 13], [361, 19, 439, 17], [362, 6, 440, 4], [363, 6, 442, 4], [363, 10, 442, 8], [363, 14, 442, 12], [363, 15, 442, 13, "failOffsetXStart"], [363, 31, 442, 29], [363, 36, 442, 34, "Number"], [363, 42, 442, 40], [363, 43, 442, 41, "MIN_SAFE_INTEGER"], [363, 59, 442, 57], [363, 63, 442, 61, "dx"], [363, 65, 442, 63], [363, 68, 442, 66], [363, 72, 442, 70], [363, 73, 442, 71, "failOffsetXStart"], [363, 89, 442, 87], [363, 91, 442, 89], [364, 8, 443, 6], [364, 15, 443, 13], [364, 19, 443, 17], [365, 6, 444, 4], [366, 6, 446, 4], [366, 10, 446, 8], [366, 14, 446, 12], [366, 15, 446, 13, "failOffsetXEnd"], [366, 29, 446, 27], [366, 34, 446, 32, "Number"], [366, 40, 446, 38], [366, 41, 446, 39, "MAX_SAFE_INTEGER"], [366, 57, 446, 55], [366, 61, 446, 59, "dx"], [366, 63, 446, 61], [366, 66, 446, 64], [366, 70, 446, 68], [366, 71, 446, 69, "failOffsetXEnd"], [366, 85, 446, 83], [366, 87, 446, 85], [367, 8, 447, 6], [367, 15, 447, 13], [367, 19, 447, 17], [368, 6, 448, 4], [369, 6, 450, 4], [369, 10, 450, 8], [369, 14, 450, 12], [369, 15, 450, 13, "failOffsetYStart"], [369, 31, 450, 29], [369, 36, 450, 34, "Number"], [369, 42, 450, 40], [369, 43, 450, 41, "MIN_SAFE_INTEGER"], [369, 59, 450, 57], [369, 63, 450, 61, "dy"], [369, 65, 450, 63], [369, 68, 450, 66], [369, 72, 450, 70], [369, 73, 450, 71, "failOffsetYStart"], [369, 89, 450, 87], [369, 91, 450, 89], [370, 8, 451, 6], [370, 15, 451, 13], [370, 19, 451, 17], [371, 6, 452, 4], [372, 6, 454, 4], [372, 13, 454, 11], [372, 17, 454, 15], [372, 18, 454, 16, "failOffsetYEnd"], [372, 32, 454, 30], [372, 37, 454, 35, "Number"], [372, 43, 454, 41], [372, 44, 454, 42, "MAX_SAFE_INTEGER"], [372, 60, 454, 58], [372, 64, 454, 62, "dy"], [372, 66, 454, 64], [372, 69, 454, 67], [372, 73, 454, 71], [372, 74, 454, 72, "failOffsetYEnd"], [372, 88, 454, 86], [373, 4, 455, 2], [374, 4, 457, 2, "tryBegin"], [374, 12, 457, 10, "tryBegin"], [374, 13, 457, 11, "event"], [374, 18, 457, 16], [374, 20, 457, 18], [375, 6, 458, 4], [375, 10, 458, 8], [375, 14, 458, 12], [375, 15, 458, 13, "state"], [375, 20, 458, 18], [375, 25, 458, 23, "State"], [375, 37, 458, 28], [375, 38, 458, 29, "UNDETERMINED"], [375, 50, 458, 41], [375, 54, 458, 45], [375, 58, 458, 49], [375, 59, 458, 50, "tracker"], [375, 66, 458, 57], [375, 67, 458, 58, "trackedPointersCount"], [375, 87, 458, 78], [375, 91, 458, 82], [375, 95, 458, 86], [375, 96, 458, 87, "minPointers"], [375, 107, 458, 98], [375, 109, 458, 100], [376, 8, 459, 6], [376, 12, 459, 10], [376, 13, 459, 11, "resetProgress"], [376, 26, 459, 24], [376, 27, 459, 25], [376, 28, 459, 26], [377, 8, 460, 6], [377, 12, 460, 10], [377, 13, 460, 11, "offsetX"], [377, 20, 460, 18], [377, 23, 460, 21], [377, 24, 460, 22], [378, 8, 461, 6], [378, 12, 461, 10], [378, 13, 461, 11, "offsetY"], [378, 20, 461, 18], [378, 23, 461, 21], [378, 24, 461, 22], [379, 8, 462, 6], [379, 12, 462, 10], [379, 13, 462, 11, "velocityX"], [379, 22, 462, 20], [379, 25, 462, 23], [379, 26, 462, 24], [380, 8, 463, 6], [380, 12, 463, 10], [380, 13, 463, 11, "velocityY"], [380, 22, 463, 20], [380, 25, 463, 23], [380, 26, 463, 24], [381, 8, 464, 6], [381, 12, 464, 10], [381, 13, 464, 11, "begin"], [381, 18, 464, 16], [381, 19, 464, 17], [381, 20, 464, 18], [382, 8, 466, 6], [382, 12, 466, 10], [382, 16, 466, 14], [382, 17, 466, 15, "activateAfterLongPress"], [382, 39, 466, 37], [382, 42, 466, 40], [382, 43, 466, 41], [382, 45, 466, 43], [383, 10, 467, 8], [383, 14, 467, 12], [383, 15, 467, 13, "activationTimeout"], [383, 32, 467, 30], [383, 35, 467, 33, "setTimeout"], [383, 45, 467, 43], [383, 46, 467, 44], [383, 52, 467, 50], [384, 12, 468, 10], [384, 16, 468, 14], [384, 17, 468, 15, "activate"], [384, 25, 468, 23], [384, 26, 468, 24], [384, 27, 468, 25], [385, 10, 469, 8], [385, 11, 469, 9], [385, 13, 469, 11], [385, 17, 469, 15], [385, 18, 469, 16, "activateAfterLongPress"], [385, 40, 469, 38], [385, 41, 469, 39], [386, 8, 470, 6], [387, 6, 471, 4], [387, 7, 471, 5], [387, 13, 471, 11], [388, 8, 472, 6], [388, 14, 472, 12, "velocity"], [388, 22, 472, 20], [388, 25, 472, 23], [388, 29, 472, 27], [388, 30, 472, 28, "tracker"], [388, 37, 472, 35], [388, 38, 472, 36, "getVelocity"], [388, 49, 472, 47], [388, 50, 472, 48, "event"], [388, 55, 472, 53], [388, 56, 472, 54, "pointerId"], [388, 65, 472, 63], [388, 66, 472, 64], [389, 8, 473, 6], [389, 12, 473, 10], [389, 13, 473, 11, "velocityX"], [389, 22, 473, 20], [389, 25, 473, 23, "velocity"], [389, 33, 473, 31], [389, 34, 473, 32, "x"], [389, 35, 473, 33], [390, 8, 474, 6], [390, 12, 474, 10], [390, 13, 474, 11, "velocityY"], [390, 22, 474, 20], [390, 25, 474, 23, "velocity"], [390, 33, 474, 31], [390, 34, 474, 32, "y"], [390, 35, 474, 33], [391, 6, 475, 4], [392, 4, 476, 2], [393, 4, 478, 2, "<PERSON><PERSON><PERSON>"], [393, 14, 478, 12, "<PERSON><PERSON><PERSON>"], [393, 15, 478, 12], [393, 17, 478, 15], [394, 6, 479, 4], [394, 10, 479, 8], [394, 14, 479, 12], [394, 15, 479, 13, "state"], [394, 20, 479, 18], [394, 25, 479, 23, "State"], [394, 37, 479, 28], [394, 38, 479, 29, "BEGAN"], [394, 43, 479, 34], [394, 45, 479, 36], [395, 8, 480, 6], [395, 12, 480, 10], [395, 16, 480, 14], [395, 17, 480, 15, "shouldFail"], [395, 27, 480, 25], [395, 28, 480, 26], [395, 29, 480, 27], [395, 31, 480, 29], [396, 10, 481, 8], [396, 14, 481, 12], [396, 15, 481, 13, "fail"], [396, 19, 481, 17], [396, 20, 481, 18], [396, 21, 481, 19], [397, 8, 482, 6], [397, 9, 482, 7], [397, 15, 482, 13], [397, 19, 482, 17], [397, 23, 482, 21], [397, 24, 482, 22, "shouldActivate"], [397, 38, 482, 36], [397, 39, 482, 37], [397, 40, 482, 38], [397, 42, 482, 40], [398, 10, 483, 8], [398, 14, 483, 12], [398, 15, 483, 13, "activate"], [398, 23, 483, 21], [398, 24, 483, 22], [398, 25, 483, 23], [399, 8, 484, 6], [400, 6, 485, 4], [401, 4, 486, 2], [402, 4, 488, 2, "activate"], [402, 12, 488, 10, "activate"], [402, 13, 488, 11, "force"], [402, 18, 488, 16], [402, 21, 488, 19], [402, 26, 488, 24], [402, 28, 488, 26], [403, 6, 489, 4], [403, 10, 489, 8], [403, 14, 489, 12], [403, 15, 489, 13, "state"], [403, 20, 489, 18], [403, 25, 489, 23, "State"], [403, 37, 489, 28], [403, 38, 489, 29, "ACTIVE"], [403, 44, 489, 35], [403, 46, 489, 37], [404, 8, 490, 6], [404, 12, 490, 10], [404, 13, 490, 11, "resetProgress"], [404, 26, 490, 24], [404, 27, 490, 25], [404, 28, 490, 26], [405, 6, 491, 4], [406, 6, 493, 4], [406, 11, 493, 9], [406, 12, 493, 10, "activate"], [406, 20, 493, 18], [406, 21, 493, 19, "force"], [406, 26, 493, 24], [406, 27, 493, 25], [407, 4, 494, 2], [408, 4, 496, 2, "onCancel"], [408, 12, 496, 10, "onCancel"], [408, 13, 496, 10], [408, 15, 496, 13], [409, 6, 497, 4], [409, 10, 497, 8], [409, 11, 497, 9, "clearActivationTimeout"], [409, 33, 497, 31], [409, 34, 497, 32], [409, 35, 497, 33], [410, 4, 498, 2], [411, 4, 500, 2, "onReset"], [411, 11, 500, 9, "onReset"], [411, 12, 500, 9], [411, 14, 500, 12], [412, 6, 501, 4], [412, 10, 501, 8], [412, 11, 501, 9, "clearActivationTimeout"], [412, 33, 501, 31], [412, 34, 501, 32], [412, 35, 501, 33], [413, 4, 502, 2], [414, 4, 504, 2, "resetProgress"], [414, 17, 504, 15, "resetProgress"], [414, 18, 504, 15], [414, 20, 504, 18], [415, 6, 505, 4], [415, 10, 505, 8], [415, 14, 505, 12], [415, 15, 505, 13, "state"], [415, 20, 505, 18], [415, 25, 505, 23, "State"], [415, 37, 505, 28], [415, 38, 505, 29, "ACTIVE"], [415, 44, 505, 35], [415, 46, 505, 37], [416, 8, 506, 6], [417, 6, 507, 4], [418, 6, 509, 4], [418, 10, 509, 8], [418, 11, 509, 9, "startX"], [418, 17, 509, 15], [418, 20, 509, 18], [418, 24, 509, 22], [418, 25, 509, 23, "lastX"], [418, 30, 509, 28], [419, 6, 510, 4], [419, 10, 510, 8], [419, 11, 510, 9, "startY"], [419, 17, 510, 15], [419, 20, 510, 18], [419, 24, 510, 22], [419, 25, 510, 23, "lastY"], [419, 30, 510, 28], [420, 4, 511, 2], [421, 2, 513, 0], [422, 2, 513, 1, "exports"], [422, 9, 513, 1], [422, 10, 513, 1, "default"], [422, 17, 513, 1], [422, 20, 513, 1, "PanGestureHandler"], [422, 37, 513, 1], [423, 0, 513, 1], [423, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "PanGestureHandler", "constructor", "updateGestureConfig", "resetConfig", "transformNativeEvent", "getTranslationX", "getTranslationY", "clearActivationTimeout", "onPointerDown", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerOutOfBounds", "scheduleWheelEnd", "setTimeout$argument_0", "onWheel", "shouldActivate", "shouldFail", "tryBegin", "<PERSON><PERSON><PERSON>", "activate", "onCancel", "onReset", "resetProgress"], "mappings": "AAA,iNC;eCS;ECC;GD4D;EEE;GF6G;EGE;GHiB;EIE;GJU;EKE;GLE;EME;GNE;EOE;GPE;EQG;GRgB;ESE;GTqB;EUE;GVsB;EWE;GXc;EYE;GZW;EaE;GbkB;EcE;sCCE;KDQ;GdC;EgBE;GhBgC;EiBE;GjByC;EkBE;GlBuB;EmBE;4CJU;SIE;GnBO;EoBE;GpBQ;EqBE;GrBM;EsBE;GtBE;EuBE;GvBE;EwBE;GxBO;CDE"}}, "type": "js/module"}]}