{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 51}, "end": {"line": 2, "column": 31, "index": 82}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 83}, "end": {"line": 3, "column": 53, "index": 136}}], "key": "5viveWF5O/AXsjQDU5X7yyaGrUk=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 137}, "end": {"line": 4, "column": 75, "index": 212}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 213}, "end": {"line": 5, "column": 63, "index": 276}}], "key": "uyrN8K1sxp8o5ztOrm1s7TT4qf8=", "exportNames": ["*"]}}, {"name": "../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 402}, "end": {"line": 12, "column": 41, "index": 443}}], "key": "80gGlYetrHmVdqDZvFU6/ojATpA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 444}, "end": {"line": 13, "column": 28, "index": 472}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "./TSpan", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 473}, "end": {"line": 14, "column": 17, "index": 490}}], "key": "M8orbKyGJ5/skPPvrLCJlDjfIGQ=", "exportNames": ["*"]}}, {"name": "../fabric/TextNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 491}, "end": {"line": 15, "column": 54, "index": 545}}], "key": "jifW4dNrxz9xD2DSWZ/rF06n414=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _extractText = _interopRequireDefault(require(_dependencyMap[7], \"../lib/extract/extractText\"));\n  var _extractProps = _interopRequireWildcard(require(_dependencyMap[8], \"../lib/extract/extractProps\"));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[9], \"../lib/extract/extractTransform\"));\n  var _util = require(_dependencyMap[10], \"../lib/util\");\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[11], \"./Shape\"));\n  require(_dependencyMap[12], \"./TSpan\");\n  var _TextNativeComponent = _interopRequireDefault(require(_dependencyMap[13], \"../fabric/TextNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[14], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-svg/src/elements/Text.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Text = exports.default = /*#__PURE__*/function (_Shape) {\n    function Text() {\n      var _this;\n      (0, _classCallCheck2.default)(this, Text);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, Text, [...args]);\n      _this.setNativeProps = props => {\n        var matrix = props && !props.matrix && (0, _extractTransform.default)(props);\n        if (matrix) {\n          props.matrix = matrix;\n        }\n        var prop = (0, _extractProps.propsAndStyles)(props);\n        Object.assign(prop, (0, _util.pickNotNil)((0, _extractText.default)(prop, true)));\n        _this.root && _this.root.setNativeProps(prop);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(Text, _Shape);\n    return (0, _createClass2.default)(Text, [{\n      key: \"render\",\n      value: function render() {\n        var prop = (0, _extractProps.propsAndStyles)(this.props);\n        var props = (0, _extractProps.default)({\n          ...prop,\n          x: null,\n          y: null\n        }, this);\n        Object.assign(props, (0, _extractText.default)(prop, true));\n        props.ref = this.refMethod;\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextNativeComponent.default, {\n          ...props\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 12\n        }, this);\n      }\n    }]);\n  }(_Shape2.default);\n  Text.displayName = 'Text';\n});", "lineCount": 67, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractText"], [13, 18, 3, 0], [13, 21, 3, 0, "_interopRequireDefault"], [13, 43, 3, 0], [13, 44, 3, 0, "require"], [13, 51, 3, 0], [13, 52, 3, 0, "_dependencyMap"], [13, 66, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_extractProps"], [14, 19, 4, 0], [14, 22, 4, 0, "_interopRequireWildcard"], [14, 45, 4, 0], [14, 46, 4, 0, "require"], [14, 53, 4, 0], [14, 54, 4, 0, "_dependencyMap"], [14, 68, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_extractTransform"], [15, 23, 5, 0], [15, 26, 5, 0, "_interopRequireDefault"], [15, 48, 5, 0], [15, 49, 5, 0, "require"], [15, 56, 5, 0], [15, 57, 5, 0, "_dependencyMap"], [15, 71, 5, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_util"], [16, 11, 12, 0], [16, 14, 12, 0, "require"], [16, 21, 12, 0], [16, 22, 12, 0, "_dependencyMap"], [16, 36, 12, 0], [17, 2, 13, 0], [17, 6, 13, 0, "_Shape2"], [17, 13, 13, 0], [17, 16, 13, 0, "_interopRequireDefault"], [17, 38, 13, 0], [17, 39, 13, 0, "require"], [17, 46, 13, 0], [17, 47, 13, 0, "_dependencyMap"], [17, 61, 13, 0], [18, 2, 14, 0, "require"], [18, 9, 14, 0], [18, 10, 14, 0, "_dependencyMap"], [18, 24, 14, 0], [19, 2, 15, 0], [19, 6, 15, 0, "_TextNativeComponent"], [19, 26, 15, 0], [19, 29, 15, 0, "_interopRequireDefault"], [19, 51, 15, 0], [19, 52, 15, 0, "require"], [19, 59, 15, 0], [19, 60, 15, 0, "_dependencyMap"], [19, 74, 15, 0], [20, 2, 15, 54], [20, 6, 15, 54, "_jsxDevRuntime"], [20, 20, 15, 54], [20, 23, 15, 54, "require"], [20, 30, 15, 54], [20, 31, 15, 54, "_dependencyMap"], [20, 45, 15, 54], [21, 2, 15, 54], [21, 6, 15, 54, "_jsxFileName"], [21, 18, 15, 54], [22, 2, 15, 54], [22, 11, 15, 54, "_interopRequireWildcard"], [22, 35, 15, 54, "e"], [22, 36, 15, 54], [22, 38, 15, 54, "t"], [22, 39, 15, 54], [22, 68, 15, 54, "WeakMap"], [22, 75, 15, 54], [22, 81, 15, 54, "r"], [22, 82, 15, 54], [22, 89, 15, 54, "WeakMap"], [22, 96, 15, 54], [22, 100, 15, 54, "n"], [22, 101, 15, 54], [22, 108, 15, 54, "WeakMap"], [22, 115, 15, 54], [22, 127, 15, 54, "_interopRequireWildcard"], [22, 150, 15, 54], [22, 162, 15, 54, "_interopRequireWildcard"], [22, 163, 15, 54, "e"], [22, 164, 15, 54], [22, 166, 15, 54, "t"], [22, 167, 15, 54], [22, 176, 15, 54, "t"], [22, 177, 15, 54], [22, 181, 15, 54, "e"], [22, 182, 15, 54], [22, 186, 15, 54, "e"], [22, 187, 15, 54], [22, 188, 15, 54, "__esModule"], [22, 198, 15, 54], [22, 207, 15, 54, "e"], [22, 208, 15, 54], [22, 214, 15, 54, "o"], [22, 215, 15, 54], [22, 217, 15, 54, "i"], [22, 218, 15, 54], [22, 220, 15, 54, "f"], [22, 221, 15, 54], [22, 226, 15, 54, "__proto__"], [22, 235, 15, 54], [22, 243, 15, 54, "default"], [22, 250, 15, 54], [22, 252, 15, 54, "e"], [22, 253, 15, 54], [22, 270, 15, 54, "e"], [22, 271, 15, 54], [22, 294, 15, 54, "e"], [22, 295, 15, 54], [22, 320, 15, 54, "e"], [22, 321, 15, 54], [22, 330, 15, 54, "f"], [22, 331, 15, 54], [22, 337, 15, 54, "o"], [22, 338, 15, 54], [22, 341, 15, 54, "t"], [22, 342, 15, 54], [22, 345, 15, 54, "n"], [22, 346, 15, 54], [22, 349, 15, 54, "r"], [22, 350, 15, 54], [22, 358, 15, 54, "o"], [22, 359, 15, 54], [22, 360, 15, 54, "has"], [22, 363, 15, 54], [22, 364, 15, 54, "e"], [22, 365, 15, 54], [22, 375, 15, 54, "o"], [22, 376, 15, 54], [22, 377, 15, 54, "get"], [22, 380, 15, 54], [22, 381, 15, 54, "e"], [22, 382, 15, 54], [22, 385, 15, 54, "o"], [22, 386, 15, 54], [22, 387, 15, 54, "set"], [22, 390, 15, 54], [22, 391, 15, 54, "e"], [22, 392, 15, 54], [22, 394, 15, 54, "f"], [22, 395, 15, 54], [22, 409, 15, 54, "_t"], [22, 411, 15, 54], [22, 415, 15, 54, "e"], [22, 416, 15, 54], [22, 432, 15, 54, "_t"], [22, 434, 15, 54], [22, 441, 15, 54, "hasOwnProperty"], [22, 455, 15, 54], [22, 456, 15, 54, "call"], [22, 460, 15, 54], [22, 461, 15, 54, "e"], [22, 462, 15, 54], [22, 464, 15, 54, "_t"], [22, 466, 15, 54], [22, 473, 15, 54, "i"], [22, 474, 15, 54], [22, 478, 15, 54, "o"], [22, 479, 15, 54], [22, 482, 15, 54, "Object"], [22, 488, 15, 54], [22, 489, 15, 54, "defineProperty"], [22, 503, 15, 54], [22, 508, 15, 54, "Object"], [22, 514, 15, 54], [22, 515, 15, 54, "getOwnPropertyDescriptor"], [22, 539, 15, 54], [22, 540, 15, 54, "e"], [22, 541, 15, 54], [22, 543, 15, 54, "_t"], [22, 545, 15, 54], [22, 552, 15, 54, "i"], [22, 553, 15, 54], [22, 554, 15, 54, "get"], [22, 557, 15, 54], [22, 561, 15, 54, "i"], [22, 562, 15, 54], [22, 563, 15, 54, "set"], [22, 566, 15, 54], [22, 570, 15, 54, "o"], [22, 571, 15, 54], [22, 572, 15, 54, "f"], [22, 573, 15, 54], [22, 575, 15, 54, "_t"], [22, 577, 15, 54], [22, 579, 15, 54, "i"], [22, 580, 15, 54], [22, 584, 15, 54, "f"], [22, 585, 15, 54], [22, 586, 15, 54, "_t"], [22, 588, 15, 54], [22, 592, 15, 54, "e"], [22, 593, 15, 54], [22, 594, 15, 54, "_t"], [22, 596, 15, 54], [22, 607, 15, 54, "f"], [22, 608, 15, 54], [22, 613, 15, 54, "e"], [22, 614, 15, 54], [22, 616, 15, 54, "t"], [22, 617, 15, 54], [23, 2, 15, 54], [23, 11, 15, 54, "_callSuper"], [23, 22, 15, 54, "t"], [23, 23, 15, 54], [23, 25, 15, 54, "o"], [23, 26, 15, 54], [23, 28, 15, 54, "e"], [23, 29, 15, 54], [23, 40, 15, 54, "o"], [23, 41, 15, 54], [23, 48, 15, 54, "_getPrototypeOf2"], [23, 64, 15, 54], [23, 65, 15, 54, "default"], [23, 72, 15, 54], [23, 74, 15, 54, "o"], [23, 75, 15, 54], [23, 82, 15, 54, "_possibleConstructorReturn2"], [23, 109, 15, 54], [23, 110, 15, 54, "default"], [23, 117, 15, 54], [23, 119, 15, 54, "t"], [23, 120, 15, 54], [23, 122, 15, 54, "_isNativeReflectConstruct"], [23, 147, 15, 54], [23, 152, 15, 54, "Reflect"], [23, 159, 15, 54], [23, 160, 15, 54, "construct"], [23, 169, 15, 54], [23, 170, 15, 54, "o"], [23, 171, 15, 54], [23, 173, 15, 54, "e"], [23, 174, 15, 54], [23, 186, 15, 54, "_getPrototypeOf2"], [23, 202, 15, 54], [23, 203, 15, 54, "default"], [23, 210, 15, 54], [23, 212, 15, 54, "t"], [23, 213, 15, 54], [23, 215, 15, 54, "constructor"], [23, 226, 15, 54], [23, 230, 15, 54, "o"], [23, 231, 15, 54], [23, 232, 15, 54, "apply"], [23, 237, 15, 54], [23, 238, 15, 54, "t"], [23, 239, 15, 54], [23, 241, 15, 54, "e"], [23, 242, 15, 54], [24, 2, 15, 54], [24, 11, 15, 54, "_isNativeReflectConstruct"], [24, 37, 15, 54], [24, 51, 15, 54, "t"], [24, 52, 15, 54], [24, 56, 15, 54, "Boolean"], [24, 63, 15, 54], [24, 64, 15, 54, "prototype"], [24, 73, 15, 54], [24, 74, 15, 54, "valueOf"], [24, 81, 15, 54], [24, 82, 15, 54, "call"], [24, 86, 15, 54], [24, 87, 15, 54, "Reflect"], [24, 94, 15, 54], [24, 95, 15, 54, "construct"], [24, 104, 15, 54], [24, 105, 15, 54, "Boolean"], [24, 112, 15, 54], [24, 145, 15, 54, "t"], [24, 146, 15, 54], [24, 159, 15, 54, "_isNativeReflectConstruct"], [24, 184, 15, 54], [24, 196, 15, 54, "_isNativeReflectConstruct"], [24, 197, 15, 54], [24, 210, 15, 54, "t"], [24, 211, 15, 54], [25, 2, 15, 54], [25, 6, 28, 21, "Text"], [25, 10, 28, 25], [25, 13, 28, 25, "exports"], [25, 20, 28, 25], [25, 21, 28, 25, "default"], [25, 28, 28, 25], [25, 54, 28, 25, "_Shape"], [25, 60, 28, 25], [26, 4, 28, 25], [26, 13, 28, 25, "Text"], [26, 18, 28, 25], [27, 6, 28, 25], [27, 10, 28, 25, "_this"], [27, 15, 28, 25], [28, 6, 28, 25], [28, 10, 28, 25, "_classCallCheck2"], [28, 26, 28, 25], [28, 27, 28, 25, "default"], [28, 34, 28, 25], [28, 42, 28, 25, "Text"], [28, 46, 28, 25], [29, 6, 28, 25], [29, 15, 28, 25, "_len"], [29, 19, 28, 25], [29, 22, 28, 25, "arguments"], [29, 31, 28, 25], [29, 32, 28, 25, "length"], [29, 38, 28, 25], [29, 40, 28, 25, "args"], [29, 44, 28, 25], [29, 51, 28, 25, "Array"], [29, 56, 28, 25], [29, 57, 28, 25, "_len"], [29, 61, 28, 25], [29, 64, 28, 25, "_key"], [29, 68, 28, 25], [29, 74, 28, 25, "_key"], [29, 78, 28, 25], [29, 81, 28, 25, "_len"], [29, 85, 28, 25], [29, 87, 28, 25, "_key"], [29, 91, 28, 25], [30, 8, 28, 25, "args"], [30, 12, 28, 25], [30, 13, 28, 25, "_key"], [30, 17, 28, 25], [30, 21, 28, 25, "arguments"], [30, 30, 28, 25], [30, 31, 28, 25, "_key"], [30, 35, 28, 25], [31, 6, 28, 25], [32, 6, 28, 25, "_this"], [32, 11, 28, 25], [32, 14, 28, 25, "_callSuper"], [32, 24, 28, 25], [32, 31, 28, 25, "Text"], [32, 35, 28, 25], [32, 41, 28, 25, "args"], [32, 45, 28, 25], [33, 6, 28, 25, "_this"], [33, 11, 28, 25], [33, 12, 31, 2, "setNativeProps"], [33, 26, 31, 16], [33, 29, 32, 4, "props"], [33, 34, 35, 5], [33, 38, 36, 7], [34, 8, 37, 4], [34, 12, 37, 10, "matrix"], [34, 18, 37, 16], [34, 21, 37, 19, "props"], [34, 26, 37, 24], [34, 30, 37, 28], [34, 31, 37, 29, "props"], [34, 36, 37, 34], [34, 37, 37, 35, "matrix"], [34, 43, 37, 41], [34, 47, 37, 45], [34, 51, 37, 45, "extractTransform"], [34, 76, 37, 61], [34, 78, 37, 62, "props"], [34, 83, 37, 67], [34, 84, 37, 68], [35, 8, 38, 4], [35, 12, 38, 8, "matrix"], [35, 18, 38, 14], [35, 20, 38, 16], [36, 10, 39, 6, "props"], [36, 15, 39, 11], [36, 16, 39, 12, "matrix"], [36, 22, 39, 18], [36, 25, 39, 21, "matrix"], [36, 31, 39, 27], [37, 8, 40, 4], [38, 8, 41, 4], [38, 12, 41, 10, "prop"], [38, 16, 41, 14], [38, 19, 41, 17], [38, 23, 41, 17, "propsAndStyles"], [38, 51, 41, 31], [38, 53, 41, 32, "props"], [38, 58, 41, 37], [38, 59, 41, 38], [39, 8, 42, 4, "Object"], [39, 14, 42, 10], [39, 15, 42, 11, "assign"], [39, 21, 42, 17], [39, 22, 42, 18, "prop"], [39, 26, 42, 22], [39, 28, 42, 24], [39, 32, 42, 24, "pickNotNil"], [39, 48, 42, 34], [39, 50, 42, 35], [39, 54, 42, 35, "extractText"], [39, 74, 42, 46], [39, 76, 42, 47, "prop"], [39, 80, 42, 51], [39, 82, 42, 53], [39, 86, 42, 57], [39, 87, 42, 58], [39, 88, 42, 59], [39, 89, 42, 60], [40, 8, 43, 4, "_this"], [40, 13, 43, 4], [40, 14, 43, 9, "root"], [40, 18, 43, 13], [40, 22, 43, 17, "_this"], [40, 27, 43, 17], [40, 28, 43, 22, "root"], [40, 32, 43, 26], [40, 33, 43, 27, "setNativeProps"], [40, 47, 43, 41], [40, 48, 43, 42, "prop"], [40, 52, 43, 46], [40, 53, 43, 47], [41, 6, 44, 2], [41, 7, 44, 3], [42, 6, 44, 3], [42, 13, 44, 3, "_this"], [42, 18, 44, 3], [43, 4, 44, 3], [44, 4, 44, 3], [44, 8, 44, 3, "_inherits2"], [44, 18, 44, 3], [44, 19, 44, 3, "default"], [44, 26, 44, 3], [44, 28, 44, 3, "Text"], [44, 32, 44, 3], [44, 34, 44, 3, "_Shape"], [44, 40, 44, 3], [45, 4, 44, 3], [45, 15, 44, 3, "_createClass2"], [45, 28, 44, 3], [45, 29, 44, 3, "default"], [45, 36, 44, 3], [45, 38, 44, 3, "Text"], [45, 42, 44, 3], [46, 6, 44, 3, "key"], [46, 9, 44, 3], [47, 6, 44, 3, "value"], [47, 11, 44, 3], [47, 13, 46, 2], [47, 22, 46, 2, "render"], [47, 28, 46, 8, "render"], [47, 29, 46, 8], [47, 31, 46, 11], [48, 8, 47, 4], [48, 12, 47, 10, "prop"], [48, 16, 47, 14], [48, 19, 47, 17], [48, 23, 47, 17, "propsAndStyles"], [48, 51, 47, 31], [48, 53, 47, 32], [48, 57, 47, 36], [48, 58, 47, 37, "props"], [48, 63, 47, 42], [48, 64, 47, 43], [49, 8, 48, 4], [49, 12, 48, 10, "props"], [49, 17, 48, 15], [49, 20, 48, 18], [49, 24, 48, 18, "extractProps"], [49, 45, 48, 30], [49, 47, 49, 6], [50, 10, 50, 8], [50, 13, 50, 11, "prop"], [50, 17, 50, 15], [51, 10, 51, 8, "x"], [51, 11, 51, 9], [51, 13, 51, 11], [51, 17, 51, 15], [52, 10, 52, 8, "y"], [52, 11, 52, 9], [52, 13, 52, 11], [53, 8, 53, 6], [53, 9, 53, 7], [53, 11, 54, 6], [53, 15, 55, 4], [53, 16, 55, 5], [54, 8, 56, 4, "Object"], [54, 14, 56, 10], [54, 15, 56, 11, "assign"], [54, 21, 56, 17], [54, 22, 56, 18, "props"], [54, 27, 56, 23], [54, 29, 56, 25], [54, 33, 56, 25, "extractText"], [54, 53, 56, 36], [54, 55, 56, 37, "prop"], [54, 59, 56, 41], [54, 61, 56, 43], [54, 65, 56, 47], [54, 66, 56, 48], [54, 67, 56, 49], [55, 8, 57, 4, "props"], [55, 13, 57, 9], [55, 14, 57, 10, "ref"], [55, 17, 57, 13], [55, 20, 57, 16], [55, 24, 57, 20], [55, 25, 57, 21, "refMethod"], [55, 34, 57, 70], [56, 8, 58, 4], [56, 28, 58, 11], [56, 32, 58, 11, "_jsxDevRuntime"], [56, 46, 58, 11], [56, 47, 58, 11, "jsxDEV"], [56, 53, 58, 11], [56, 55, 58, 12, "_TextNativeComponent"], [56, 75, 58, 12], [56, 76, 58, 12, "default"], [56, 83, 58, 21], [57, 10, 58, 21], [57, 13, 58, 26, "props"], [58, 8, 58, 31], [59, 10, 58, 31, "fileName"], [59, 18, 58, 31], [59, 20, 58, 31, "_jsxFileName"], [59, 32, 58, 31], [60, 10, 58, 31, "lineNumber"], [60, 20, 58, 31], [61, 10, 58, 31, "columnNumber"], [61, 22, 58, 31], [62, 8, 58, 31], [62, 15, 58, 34], [62, 16, 58, 35], [63, 6, 59, 2], [64, 4, 59, 3], [65, 2, 59, 3], [65, 4, 28, 34, "<PERSON><PERSON><PERSON>"], [65, 19, 28, 39], [66, 2, 28, 21, "Text"], [66, 6, 28, 25], [66, 7, 29, 9, "displayName"], [66, 18, 29, 20], [66, 21, 29, 23], [66, 27, 29, 29], [67, 0, 29, 29], [67, 3]], "functionMap": {"names": ["<global>", "Text", "setNativeProps", "render"], "mappings": "AAA;eC2B;mBCG;GDa;EEE;GFa;CDC"}}, "type": "js/module"}]}