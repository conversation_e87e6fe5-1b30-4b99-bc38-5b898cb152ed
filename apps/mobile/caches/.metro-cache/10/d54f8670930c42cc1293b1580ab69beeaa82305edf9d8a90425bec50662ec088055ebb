{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDocumentTitle = useDocumentTitle;\n  function useDocumentTitle() {\n    // Noop for native platforms\n  }\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useDocumentTitle"], [7, 26, 1, 13], [7, 29, 1, 13, "useDocumentTitle"], [7, 45, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "useDocumentTitle"], [8, 27, 3, 32, "useDocumentTitle"], [8, 28, 3, 32], [8, 30, 3, 35], [9, 4, 4, 2], [10, 2, 4, 2], [11, 0, 5, 1], [11, 3]], "functionMap": {"names": ["<global>", "useDocumentTitle"], "mappings": "AAA;OCE;CDE"}}, "type": "js/module"}]}