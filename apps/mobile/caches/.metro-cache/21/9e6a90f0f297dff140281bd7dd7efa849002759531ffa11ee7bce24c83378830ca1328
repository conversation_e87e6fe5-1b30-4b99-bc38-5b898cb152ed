{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _default = exports.default = _reactNative.NativeModules.DevLoadingView;\n});", "lineCount": 8, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 1, 45], [7, 6, 1, 45, "_default"], [7, 14, 1, 45], [7, 17, 1, 45, "exports"], [7, 24, 1, 45], [7, 25, 1, 45, "default"], [7, 32, 1, 45], [7, 35, 3, 15, "NativeModules"], [7, 61, 3, 28], [7, 62, 3, 29, "DevLoadingView"], [7, 76, 3, 43], [8, 0, 3, 43], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}