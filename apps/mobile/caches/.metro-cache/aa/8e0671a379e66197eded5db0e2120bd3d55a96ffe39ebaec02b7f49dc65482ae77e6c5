{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 89, "index": 89}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  class ExpoFontUtils extends _expoModulesCore.NativeModule {\n    async renderToImageAsync(glyphs, options) {\n      throw new _expoModulesCore.UnavailabilityError('expo-font', 'renderToImageAsync');\n    }\n  }\n  var _default = exports.default = (0, _expoModulesCore.registerWebModule)(ExpoFontUtils, 'ExpoFontUtils');\n});", "lineCount": 13, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 2, 0], [7, 8, 2, 6, "ExpoFontUtils"], [7, 21, 2, 19], [7, 30, 2, 28, "NativeModule"], [7, 59, 2, 40], [7, 60, 2, 41], [8, 4, 3, 4], [8, 10, 3, 10, "renderToImageAsync"], [8, 28, 3, 28, "renderToImageAsync"], [8, 29, 3, 29, "glyphs"], [8, 35, 3, 35], [8, 37, 3, 37, "options"], [8, 44, 3, 44], [8, 46, 3, 46], [9, 6, 4, 8], [9, 12, 4, 14], [9, 16, 4, 18, "UnavailabilityError"], [9, 52, 4, 37], [9, 53, 4, 38], [9, 64, 4, 49], [9, 66, 4, 51], [9, 86, 4, 71], [9, 87, 4, 72], [10, 4, 5, 4], [11, 2, 6, 0], [12, 2, 6, 1], [12, 6, 6, 1, "_default"], [12, 14, 6, 1], [12, 17, 6, 1, "exports"], [12, 24, 6, 1], [12, 25, 6, 1, "default"], [12, 32, 6, 1], [12, 35, 7, 15], [12, 39, 7, 15, "registerWebModule"], [12, 73, 7, 32], [12, 75, 7, 33, "ExpoFontUtils"], [12, 88, 7, 46], [12, 90, 7, 48], [12, 105, 7, 63], [12, 106, 7, 64], [13, 0, 7, 64], [13, 3]], "functionMap": {"names": ["<global>", "ExpoFontUtils", "renderToImageAsync"], "mappings": "AAA;ACC;ICC;KDE;CDC"}}, "type": "js/module"}]}