{"dependencies": [{"name": "./ensureNativeModulesAreInstalled", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 84, "index": 99}}], "key": "A4316oxUZ5JztskIqVu3iyhr9Sk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ensureNativeModulesAreInstalled = require(_dependencyMap[0], \"./ensureNativeModulesAreInstalled\");\n  (0, _ensureNativeModulesAreInstalled.ensureNativeModulesAreInstalled)();\n  var SharedObject = globalThis.expo.SharedObject;\n  var _default = exports.default = SharedObject;\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_ensureNativeModulesAreInstalled"], [8, 38, 3, 0], [8, 41, 3, 0, "require"], [8, 48, 3, 0], [8, 49, 3, 0, "_dependencyMap"], [8, 63, 3, 0], [9, 2, 6, 0], [9, 6, 6, 0, "ensureNativeModulesAreInstalled"], [9, 70, 6, 31], [9, 72, 6, 32], [9, 73, 6, 33], [10, 2, 8, 0], [10, 6, 8, 6, "SharedObject"], [10, 18, 8, 18], [10, 21, 8, 21, "globalThis"], [10, 31, 8, 31], [10, 32, 8, 32, "expo"], [10, 36, 8, 36], [10, 37, 8, 37, "SharedObject"], [10, 49, 8, 76], [11, 2, 8, 77], [11, 6, 8, 77, "_default"], [11, 14, 8, 77], [11, 17, 8, 77, "exports"], [11, 24, 8, 77], [11, 25, 8, 77, "default"], [11, 32, 8, 77], [11, 35, 10, 15, "SharedObject"], [11, 47, 10, 27], [12, 0, 10, 27], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}