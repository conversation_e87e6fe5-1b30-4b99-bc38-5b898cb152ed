{"dependencies": [{"name": "../../Components/TextInput/AndroidTextInputNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 112}}], "key": "4yrHr4L69dNsIl/HgbOnUDBvxCo=", "exportNames": ["*"]}}, {"name": "../../Components/TextInput/RCTSingelineTextInputNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 113}}], "key": "z1vh9H2XSTCCxM85yq/A4Qgr/fY=", "exportNames": ["*"]}}, {"name": "../../ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 20, "column": 25}, "end": {"line": 20, "column": 67}}], "key": "T5tpzkUltdJeyXwu85s3KOfgZyI=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 17}, "end": {"line": 21, "column": 52}}], "key": "rq8uaKZ6EGoAq6r0NBzCUcqwzHs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _AndroidTextInputNativeComponent = require(_dependencyMap[0], \"../../Components/TextInput/AndroidTextInputNativeComponent\");\n  var _RCTSingelineTextInputNativeComponent = require(_dependencyMap[1], \"../../Components/TextInput/RCTSingelineTextInputNativeComponent\");\n  var _require = require(_dependencyMap[2], \"../../ReactNative/RendererProxy\"),\n    findNodeHandle = _require.findNodeHandle;\n  var Platform = require(_dependencyMap[3], \"../../Utilities/Platform\").default;\n  var currentlyFocusedInputRef = null;\n  var inputs = new Set();\n  function currentlyFocusedInput() {\n    return currentlyFocusedInputRef;\n  }\n  function currentlyFocusedField() {\n    if (__DEV__) {\n      console.error('currentlyFocusedField is deprecated and will be removed in a future release. Use currentlyFocusedInput');\n    }\n    return findNodeHandle(currentlyFocusedInputRef);\n  }\n  function focusInput(textField) {\n    if (currentlyFocusedInputRef !== textField && textField != null) {\n      currentlyFocusedInputRef = textField;\n    }\n  }\n  function blurInput(textField) {\n    if (currentlyFocusedInputRef === textField && textField != null) {\n      currentlyFocusedInputRef = null;\n    }\n  }\n  function focusField(textFieldID) {\n    if (__DEV__) {\n      console.error('focusField no longer works. Use focusInput');\n    }\n    return;\n  }\n  function blurField(textFieldID) {\n    if (__DEV__) {\n      console.error('blurField no longer works. Use blurInput');\n    }\n    return;\n  }\n  function focusTextInput(textField) {\n    if (typeof textField === 'number') {\n      if (__DEV__) {\n        console.error('focusTextInput must be called with a host component. Passing a react tag is deprecated.');\n      }\n      return;\n    }\n    if (textField != null) {\n      var fieldCanBeFocused = currentlyFocusedInputRef !== textField && textField.currentProps?.editable !== false;\n      if (!fieldCanBeFocused) {\n        return;\n      }\n      focusInput(textField);\n      if (Platform.OS === 'ios') {\n        _RCTSingelineTextInputNativeComponent.Commands.focus(textField);\n      } else if (Platform.OS === 'android') {\n        _AndroidTextInputNativeComponent.Commands.focus(textField);\n      }\n    }\n  }\n  function blurTextInput(textField) {\n    if (typeof textField === 'number') {\n      if (__DEV__) {\n        console.error('blurTextInput must be called with a host component. Passing a react tag is deprecated.');\n      }\n      return;\n    }\n    if (currentlyFocusedInputRef === textField && textField != null) {\n      blurInput(textField);\n      if (Platform.OS === 'ios') {\n        _RCTSingelineTextInputNativeComponent.Commands.blur(textField);\n      } else if (Platform.OS === 'android') {\n        _AndroidTextInputNativeComponent.Commands.blur(textField);\n      }\n    }\n  }\n  function registerInput(textField) {\n    if (typeof textField === 'number') {\n      if (__DEV__) {\n        console.error('registerInput must be called with a host component. Passing a react tag is deprecated.');\n      }\n      return;\n    }\n    inputs.add(textField);\n  }\n  function unregisterInput(textField) {\n    if (typeof textField === 'number') {\n      if (__DEV__) {\n        console.error('unregisterInput must be called with a host component. Passing a react tag is deprecated.');\n      }\n      return;\n    }\n    inputs.delete(textField);\n  }\n  function isTextInput(textField) {\n    if (typeof textField === 'number') {\n      if (__DEV__) {\n        console.error('isTextInput must be called with a host component. Passing a react tag is deprecated.');\n      }\n      return false;\n    }\n    return inputs.has(textField);\n  }\n  var TextInputState = {\n    currentlyFocusedInput,\n    focusInput,\n    blurInput,\n    currentlyFocusedField,\n    focusField,\n    blurField,\n    focusTextInput,\n    blurTextInput,\n    registerInput,\n    unregisterInput,\n    isTextInput\n  };\n  var _default = exports.default = TextInputState;\n});", "lineCount": 121, "map": [[6, 2, 17, 0], [6, 6, 17, 0, "_AndroidTextInputNativeComponent"], [6, 38, 17, 0], [6, 41, 17, 0, "require"], [6, 48, 17, 0], [6, 49, 17, 0, "_dependencyMap"], [6, 63, 17, 0], [7, 2, 18, 0], [7, 6, 18, 0, "_RCTSingelineTextInputNativeComponent"], [7, 43, 18, 0], [7, 46, 18, 0, "require"], [7, 53, 18, 0], [7, 54, 18, 0, "_dependencyMap"], [7, 68, 18, 0], [8, 2, 20, 0], [8, 6, 20, 0, "_require"], [8, 14, 20, 0], [8, 17, 20, 25, "require"], [8, 24, 20, 32], [8, 25, 20, 32, "_dependencyMap"], [8, 39, 20, 32], [8, 77, 20, 66], [8, 78, 20, 67], [9, 4, 20, 7, "findNodeHandle"], [9, 18, 20, 21], [9, 21, 20, 21, "_require"], [9, 29, 20, 21], [9, 30, 20, 7, "findNodeHandle"], [9, 44, 20, 21], [10, 2, 21, 0], [10, 6, 21, 6, "Platform"], [10, 14, 21, 14], [10, 17, 21, 17, "require"], [10, 24, 21, 24], [10, 25, 21, 24, "_dependencyMap"], [10, 39, 21, 24], [10, 70, 21, 51], [10, 71, 21, 52], [10, 72, 21, 53, "default"], [10, 79, 21, 60], [11, 2, 23, 0], [11, 6, 23, 4, "currentlyFocusedInputRef"], [11, 30, 23, 43], [11, 33, 23, 46], [11, 37, 23, 50], [12, 2, 24, 0], [12, 6, 24, 6, "inputs"], [12, 12, 24, 12], [12, 15, 24, 15], [12, 19, 24, 19, "Set"], [12, 22, 24, 22], [12, 23, 24, 37], [12, 24, 24, 38], [13, 2, 26, 0], [13, 11, 26, 9, "currentlyFocusedInput"], [13, 32, 26, 30, "currentlyFocusedInput"], [13, 33, 26, 30], [13, 35, 26, 48], [14, 4, 27, 2], [14, 11, 27, 9, "currentlyFocusedInputRef"], [14, 35, 27, 33], [15, 2, 28, 0], [16, 2, 34, 0], [16, 11, 34, 9, "currentlyFocusedField"], [16, 32, 34, 30, "currentlyFocusedField"], [16, 33, 34, 30], [16, 35, 34, 42], [17, 4, 35, 2], [17, 8, 35, 6, "__DEV__"], [17, 15, 35, 13], [17, 17, 35, 15], [18, 6, 36, 4, "console"], [18, 13, 36, 11], [18, 14, 36, 12, "error"], [18, 19, 36, 17], [18, 20, 37, 6], [18, 124, 38, 4], [18, 125, 38, 5], [19, 4, 39, 2], [20, 4, 41, 2], [20, 11, 41, 9, "findNodeHandle"], [20, 25, 41, 23], [20, 26, 41, 24, "currentlyFocusedInputRef"], [20, 50, 41, 48], [20, 51, 41, 49], [21, 2, 42, 0], [22, 2, 44, 0], [22, 11, 44, 9, "focusInput"], [22, 21, 44, 19, "focusInput"], [22, 22, 44, 20, "textField"], [22, 31, 44, 44], [22, 33, 44, 52], [23, 4, 45, 2], [23, 8, 45, 6, "currentlyFocusedInputRef"], [23, 32, 45, 30], [23, 37, 45, 35, "textField"], [23, 46, 45, 44], [23, 50, 45, 48, "textField"], [23, 59, 45, 57], [23, 63, 45, 61], [23, 67, 45, 65], [23, 69, 45, 67], [24, 6, 46, 4, "currentlyFocusedInputRef"], [24, 30, 46, 28], [24, 33, 46, 31, "textField"], [24, 42, 46, 40], [25, 4, 47, 2], [26, 2, 48, 0], [27, 2, 50, 0], [27, 11, 50, 9, "blurInput"], [27, 20, 50, 18, "blurInput"], [27, 21, 50, 19, "textField"], [27, 30, 50, 43], [27, 32, 50, 51], [28, 4, 51, 2], [28, 8, 51, 6, "currentlyFocusedInputRef"], [28, 32, 51, 30], [28, 37, 51, 35, "textField"], [28, 46, 51, 44], [28, 50, 51, 48, "textField"], [28, 59, 51, 57], [28, 63, 51, 61], [28, 67, 51, 65], [28, 69, 51, 67], [29, 6, 52, 4, "currentlyFocusedInputRef"], [29, 30, 52, 28], [29, 33, 52, 31], [29, 37, 52, 35], [30, 4, 53, 2], [31, 2, 54, 0], [32, 2, 56, 0], [32, 11, 56, 9, "focusField"], [32, 21, 56, 19, "focusField"], [32, 22, 56, 20, "textFieldID"], [32, 33, 56, 40], [32, 35, 56, 48], [33, 4, 57, 2], [33, 8, 57, 6, "__DEV__"], [33, 15, 57, 13], [33, 17, 57, 15], [34, 6, 58, 4, "console"], [34, 13, 58, 11], [34, 14, 58, 12, "error"], [34, 19, 58, 17], [34, 20, 58, 18], [34, 64, 58, 62], [34, 65, 58, 63], [35, 4, 59, 2], [36, 4, 61, 2], [37, 2, 62, 0], [38, 2, 64, 0], [38, 11, 64, 9, "blurField"], [38, 20, 64, 18, "blurField"], [38, 21, 64, 19, "textFieldID"], [38, 32, 64, 39], [38, 34, 64, 41], [39, 4, 65, 2], [39, 8, 65, 6, "__DEV__"], [39, 15, 65, 13], [39, 17, 65, 15], [40, 6, 66, 4, "console"], [40, 13, 66, 11], [40, 14, 66, 12, "error"], [40, 19, 66, 17], [40, 20, 66, 18], [40, 62, 66, 60], [40, 63, 66, 61], [41, 4, 67, 2], [42, 4, 69, 2], [43, 2, 70, 0], [44, 2, 77, 0], [44, 11, 77, 9, "focusTextInput"], [44, 25, 77, 23, "focusTextInput"], [44, 26, 77, 24, "textField"], [44, 35, 77, 48], [44, 37, 77, 50], [45, 4, 78, 2], [45, 8, 78, 6], [45, 15, 78, 13, "textField"], [45, 24, 78, 22], [45, 29, 78, 27], [45, 37, 78, 35], [45, 39, 78, 37], [46, 6, 79, 4], [46, 10, 79, 8, "__DEV__"], [46, 17, 79, 15], [46, 19, 79, 17], [47, 8, 80, 6, "console"], [47, 15, 80, 13], [47, 16, 80, 14, "error"], [47, 21, 80, 19], [47, 22, 81, 8], [47, 111, 82, 6], [47, 112, 82, 7], [48, 6, 83, 4], [49, 6, 85, 4], [50, 4, 86, 2], [51, 4, 88, 2], [51, 8, 88, 6, "textField"], [51, 17, 88, 15], [51, 21, 88, 19], [51, 25, 88, 23], [51, 27, 88, 25], [52, 6, 89, 4], [52, 10, 89, 10, "fieldCanBeFocused"], [52, 27, 89, 27], [52, 30, 90, 6, "currentlyFocusedInputRef"], [52, 54, 90, 30], [52, 59, 90, 35, "textField"], [52, 68, 90, 44], [52, 72, 92, 6, "textField"], [52, 81, 92, 15], [52, 82, 92, 16, "currentProps"], [52, 94, 92, 28], [52, 96, 92, 30, "editable"], [52, 104, 92, 38], [52, 109, 92, 43], [52, 114, 92, 48], [53, 6, 94, 4], [53, 10, 94, 8], [53, 11, 94, 9, "fieldCanBeFocused"], [53, 28, 94, 26], [53, 30, 94, 28], [54, 8, 95, 6], [55, 6, 96, 4], [56, 6, 97, 4, "focusInput"], [56, 16, 97, 14], [56, 17, 97, 15, "textField"], [56, 26, 97, 24], [56, 27, 97, 25], [57, 6, 98, 4], [57, 10, 98, 8, "Platform"], [57, 18, 98, 16], [57, 19, 98, 17, "OS"], [57, 21, 98, 19], [57, 26, 98, 24], [57, 31, 98, 29], [57, 33, 98, 31], [58, 8, 104, 6, "iOSTextInputCommands"], [58, 54, 104, 26], [58, 55, 104, 27, "focus"], [58, 60, 104, 32], [58, 61, 104, 33, "textField"], [58, 70, 104, 42], [58, 71, 104, 43], [59, 6, 105, 4], [59, 7, 105, 5], [59, 13, 105, 11], [59, 17, 105, 15, "Platform"], [59, 25, 105, 23], [59, 26, 105, 24, "OS"], [59, 28, 105, 26], [59, 33, 105, 31], [59, 42, 105, 40], [59, 44, 105, 42], [60, 8, 106, 6, "AndroidTextInputCommands"], [60, 49, 106, 30], [60, 50, 106, 31, "focus"], [60, 55, 106, 36], [60, 56, 106, 37, "textField"], [60, 65, 106, 46], [60, 66, 106, 47], [61, 6, 107, 4], [62, 4, 108, 2], [63, 2, 109, 0], [64, 2, 116, 0], [64, 11, 116, 9, "blurTextInput"], [64, 24, 116, 22, "blurTextInput"], [64, 25, 116, 23, "textField"], [64, 34, 116, 47], [64, 36, 116, 49], [65, 4, 117, 2], [65, 8, 117, 6], [65, 15, 117, 13, "textField"], [65, 24, 117, 22], [65, 29, 117, 27], [65, 37, 117, 35], [65, 39, 117, 37], [66, 6, 118, 4], [66, 10, 118, 8, "__DEV__"], [66, 17, 118, 15], [66, 19, 118, 17], [67, 8, 119, 6, "console"], [67, 15, 119, 13], [67, 16, 119, 14, "error"], [67, 21, 119, 19], [67, 22, 120, 8], [67, 110, 121, 6], [67, 111, 121, 7], [68, 6, 122, 4], [69, 6, 124, 4], [70, 4, 125, 2], [71, 4, 127, 2], [71, 8, 127, 6, "currentlyFocusedInputRef"], [71, 32, 127, 30], [71, 37, 127, 35, "textField"], [71, 46, 127, 44], [71, 50, 127, 48, "textField"], [71, 59, 127, 57], [71, 63, 127, 61], [71, 67, 127, 65], [71, 69, 127, 67], [72, 6, 128, 4, "blurInput"], [72, 15, 128, 13], [72, 16, 128, 14, "textField"], [72, 25, 128, 23], [72, 26, 128, 24], [73, 6, 129, 4], [73, 10, 129, 8, "Platform"], [73, 18, 129, 16], [73, 19, 129, 17, "OS"], [73, 21, 129, 19], [73, 26, 129, 24], [73, 31, 129, 29], [73, 33, 129, 31], [74, 8, 135, 6, "iOSTextInputCommands"], [74, 54, 135, 26], [74, 55, 135, 27, "blur"], [74, 59, 135, 31], [74, 60, 135, 32, "textField"], [74, 69, 135, 41], [74, 70, 135, 42], [75, 6, 136, 4], [75, 7, 136, 5], [75, 13, 136, 11], [75, 17, 136, 15, "Platform"], [75, 25, 136, 23], [75, 26, 136, 24, "OS"], [75, 28, 136, 26], [75, 33, 136, 31], [75, 42, 136, 40], [75, 44, 136, 42], [76, 8, 137, 6, "AndroidTextInputCommands"], [76, 49, 137, 30], [76, 50, 137, 31, "blur"], [76, 54, 137, 35], [76, 55, 137, 36, "textField"], [76, 64, 137, 45], [76, 65, 137, 46], [77, 6, 138, 4], [78, 4, 139, 2], [79, 2, 140, 0], [80, 2, 142, 0], [80, 11, 142, 9, "registerInput"], [80, 24, 142, 22, "registerInput"], [80, 25, 142, 23, "textField"], [80, 34, 142, 46], [80, 36, 142, 48], [81, 4, 143, 2], [81, 8, 143, 6], [81, 15, 143, 13, "textField"], [81, 24, 143, 22], [81, 29, 143, 27], [81, 37, 143, 35], [81, 39, 143, 37], [82, 6, 144, 4], [82, 10, 144, 8, "__DEV__"], [82, 17, 144, 15], [82, 19, 144, 17], [83, 8, 145, 6, "console"], [83, 15, 145, 13], [83, 16, 145, 14, "error"], [83, 21, 145, 19], [83, 22, 146, 8], [83, 110, 147, 6], [83, 111, 147, 7], [84, 6, 148, 4], [85, 6, 150, 4], [86, 4, 151, 2], [87, 4, 153, 2, "inputs"], [87, 10, 153, 8], [87, 11, 153, 9, "add"], [87, 14, 153, 12], [87, 15, 153, 13, "textField"], [87, 24, 153, 22], [87, 25, 153, 23], [88, 2, 154, 0], [89, 2, 156, 0], [89, 11, 156, 9, "unregisterInput"], [89, 26, 156, 24, "unregisterInput"], [89, 27, 156, 25, "textField"], [89, 36, 156, 48], [89, 38, 156, 50], [90, 4, 157, 2], [90, 8, 157, 6], [90, 15, 157, 13, "textField"], [90, 24, 157, 22], [90, 29, 157, 27], [90, 37, 157, 35], [90, 39, 157, 37], [91, 6, 158, 4], [91, 10, 158, 8, "__DEV__"], [91, 17, 158, 15], [91, 19, 158, 17], [92, 8, 159, 6, "console"], [92, 15, 159, 13], [92, 16, 159, 14, "error"], [92, 21, 159, 19], [92, 22, 160, 8], [92, 112, 161, 6], [92, 113, 161, 7], [93, 6, 162, 4], [94, 6, 164, 4], [95, 4, 165, 2], [96, 4, 166, 2, "inputs"], [96, 10, 166, 8], [96, 11, 166, 9, "delete"], [96, 17, 166, 15], [96, 18, 166, 16, "textField"], [96, 27, 166, 25], [96, 28, 166, 26], [97, 2, 167, 0], [98, 2, 169, 0], [98, 11, 169, 9, "isTextInput"], [98, 22, 169, 20, "isTextInput"], [98, 23, 169, 21, "textField"], [98, 32, 169, 44], [98, 34, 169, 55], [99, 4, 170, 2], [99, 8, 170, 6], [99, 15, 170, 13, "textField"], [99, 24, 170, 22], [99, 29, 170, 27], [99, 37, 170, 35], [99, 39, 170, 37], [100, 6, 171, 4], [100, 10, 171, 8, "__DEV__"], [100, 17, 171, 15], [100, 19, 171, 17], [101, 8, 172, 6, "console"], [101, 15, 172, 13], [101, 16, 172, 14, "error"], [101, 21, 172, 19], [101, 22, 173, 8], [101, 108, 174, 6], [101, 109, 174, 7], [102, 6, 175, 4], [103, 6, 177, 4], [103, 13, 177, 11], [103, 18, 177, 16], [104, 4, 178, 2], [105, 4, 180, 2], [105, 11, 180, 9, "inputs"], [105, 17, 180, 15], [105, 18, 180, 16, "has"], [105, 21, 180, 19], [105, 22, 180, 20, "textField"], [105, 31, 180, 29], [105, 32, 180, 30], [106, 2, 181, 0], [107, 2, 183, 0], [107, 6, 183, 6, "TextInputState"], [107, 20, 183, 20], [107, 23, 183, 23], [108, 4, 184, 2, "currentlyFocusedInput"], [108, 25, 184, 23], [109, 4, 185, 2, "focusInput"], [109, 14, 185, 12], [110, 4, 186, 2, "blurInput"], [110, 13, 186, 11], [111, 4, 188, 2, "currentlyFocusedField"], [111, 25, 188, 23], [112, 4, 189, 2, "focusField"], [112, 14, 189, 12], [113, 4, 190, 2, "blurField"], [113, 13, 190, 11], [114, 4, 191, 2, "focusTextInput"], [114, 18, 191, 16], [115, 4, 192, 2, "blurTextInput"], [115, 17, 192, 15], [116, 4, 193, 2, "registerInput"], [116, 17, 193, 15], [117, 4, 194, 2, "unregisterInput"], [117, 19, 194, 17], [118, 4, 195, 2, "isTextInput"], [119, 2, 196, 0], [119, 3, 196, 1], [120, 2, 196, 2], [120, 6, 196, 2, "_default"], [120, 14, 196, 2], [120, 17, 196, 2, "exports"], [120, 24, 196, 2], [120, 25, 196, 2, "default"], [120, 32, 196, 2], [120, 35, 198, 15, "TextInputState"], [120, 49, 198, 29], [121, 0, 198, 29], [121, 3]], "functionMap": {"names": ["<global>", "currentlyFocusedInput", "currentlyFocusedField", "focusInput", "blurInput", "focusField", "blurField", "focusTextInput", "blurTextInput", "registerInput", "unregisterInput", "isTextInput"], "mappings": "AAA;ACyB;CDE;AEM;CFQ;AGE;CHI;AIE;CJI;AKE;CLM;AME;CNM;AOO;CPgC;AQO;CRwB;ASE;CTY;AUE;CVW;AWE;CXY"}}, "type": "js/module"}]}