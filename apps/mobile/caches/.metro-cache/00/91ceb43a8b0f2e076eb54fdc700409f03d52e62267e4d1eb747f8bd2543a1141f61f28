{"dependencies": [{"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "expo", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 37, "column": 15, "index": 1502}, "end": {"line": 37, "column": 30, "index": 1517}}], "key": "XXQ6vUbPkRbq1KG0TEcCvw3p4ao=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 38, "column": 27, "index": 1546}, "end": {"line": 38, "column": 43, "index": 1562}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 23, "index": 1588}, "end": {"line": 39, "column": 46, "index": 1611}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "./utils/splash", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 40, "column": 34, "index": 1647}, "end": {"line": 40, "column": 59, "index": 1672}}], "key": "CV95aXfpimwNZaPpwefcW677KSY=", "exportNames": ["*"]}}, {"name": "@expo/metro-runtime/error-overlay", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 87, "column": 45, "index": 3201}, "end": {"line": 87, "column": 89, "index": 3245}}], "key": "dk/Ivg07VB9TlnAAKVonXGLVYx8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _reactJsxDevRuntime = require(_dependencyMap[0], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/expo-router/build/renderRootComponent.js\";\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.renderRootComponent = renderRootComponent;\n  var expo_1 = require(_dependencyMap[1], \"expo\");\n  var React = __importStar(require(_dependencyMap[2], \"react\"));\n  var react_native_1 = require(_dependencyMap[3], \"react-native\");\n  var SplashScreen = __importStar(require(_dependencyMap[4], \"./utils/splash\"));\n  function isBaseObject(obj) {\n    if (Object.prototype.toString.call(obj) !== '[object Object]') {\n      return false;\n    }\n    var proto = Object.getPrototypeOf(obj);\n    if (proto === null) {\n      return true;\n    }\n    return proto === Object.prototype;\n  }\n  function isErrorShaped(error) {\n    return error && typeof error === 'object' && typeof error.name === 'string' && typeof error.message === 'string';\n  }\n  /**\n   * After we throw this error, any number of tools could handle it.\n   * This check ensures the error is always in a reason state before surfacing it to the runtime.\n   */\n  function convertError(error) {\n    if (isErrorShaped(error)) {\n      return error;\n    }\n    if (process.env.NODE_ENV === 'development') {\n      if (error == null) {\n        return new Error('A null/undefined error was thrown.');\n      }\n    }\n    if (isBaseObject(error)) {\n      return new Error(JSON.stringify(error));\n    }\n    return new Error(String(error));\n  }\n  /**\n   * Register and mount the root component using the predefined rendering\n   * method. This function ensures the Splash Screen and errors are handled correctly.\n   */\n  function renderRootComponent(Component) {\n    try {\n      // This must be delayed so the user has a chance to call it first.\n      setTimeout(() => {\n        SplashScreen._internal_preventAutoHideAsync?.();\n      });\n      React.startTransition(() => {\n        if (process.env.NODE_ENV !== 'production') {\n          var _require = require(_dependencyMap[5], \"@expo/metro-runtime/error-overlay\"),\n            withErrorOverlay = _require.withErrorOverlay;\n          (0, expo_1.registerRootComponent)(withErrorOverlay(Component));\n        } else {\n          (0, expo_1.registerRootComponent)(Component);\n        }\n      });\n    } catch (e) {\n      // Hide the splash screen if there was an error so the user can see it.\n      SplashScreen.hideAsync();\n      var error = convertError(e);\n      // Prevent the app from throwing confusing:\n      //  ERROR  Invariant Violation: \"main\" has not been registered. This can happen if:\n      // * Metro (the local dev server) is run from the wrong folder. Check if Metro is running, stop it and restart it in the current project.\n      // * A module failed to load due to an error and `AppRegistry.registerComponent` wasn't called.\n      (0, expo_1.registerRootComponent)(() => /*#__PURE__*/_reactJsxDevRuntime.jsxDEV(react_native_1.View, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 49\n      }, this));\n      // Console is pretty useless on native, on web you get interactive stack traces.\n      if (false) {\n        console.error(error);\n        console.error(`A runtime error has occurred while rendering the root component.`);\n      }\n      // Give React a tick to render before throwing.\n      setTimeout(() => {\n        throw error;\n      });\n      // TODO: Render a production-only error screen.\n    }\n  }\n});", "lineCount": 130, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_reactJsxDevRuntime"], [4, 25, 1, 13], [4, 28, 1, 13, "require"], [4, 35, 1, 13], [4, 36, 1, 13, "_dependencyMap"], [4, 50, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_jsxFileName"], [5, 18, 1, 13], [6, 2, 2, 0], [6, 6, 2, 4, "__createBinding"], [6, 21, 2, 19], [6, 24, 2, 23], [6, 28, 2, 27], [6, 32, 2, 31], [6, 36, 2, 35], [6, 37, 2, 36, "__createBinding"], [6, 52, 2, 51], [6, 57, 2, 57, "Object"], [6, 63, 2, 63], [6, 64, 2, 64, "create"], [6, 70, 2, 70], [6, 73, 2, 74], [6, 83, 2, 83, "o"], [6, 84, 2, 84], [6, 86, 2, 86, "m"], [6, 87, 2, 87], [6, 89, 2, 89, "k"], [6, 90, 2, 90], [6, 92, 2, 92, "k2"], [6, 94, 2, 94], [6, 96, 2, 96], [7, 4, 3, 4], [7, 8, 3, 8, "k2"], [7, 10, 3, 10], [7, 15, 3, 15, "undefined"], [7, 24, 3, 24], [7, 26, 3, 26, "k2"], [7, 28, 3, 28], [7, 31, 3, 31, "k"], [7, 32, 3, 32], [8, 4, 4, 4], [8, 8, 4, 8, "desc"], [8, 12, 4, 12], [8, 15, 4, 15, "Object"], [8, 21, 4, 21], [8, 22, 4, 22, "getOwnPropertyDescriptor"], [8, 46, 4, 46], [8, 47, 4, 47, "m"], [8, 48, 4, 48], [8, 50, 4, 50, "k"], [8, 51, 4, 51], [8, 52, 4, 52], [9, 4, 5, 4], [9, 8, 5, 8], [9, 9, 5, 9, "desc"], [9, 13, 5, 13], [9, 18, 5, 18], [9, 23, 5, 23], [9, 27, 5, 27, "desc"], [9, 31, 5, 31], [9, 34, 5, 34], [9, 35, 5, 35, "m"], [9, 36, 5, 36], [9, 37, 5, 37, "__esModule"], [9, 47, 5, 47], [9, 50, 5, 50, "desc"], [9, 54, 5, 54], [9, 55, 5, 55, "writable"], [9, 63, 5, 63], [9, 67, 5, 67, "desc"], [9, 71, 5, 71], [9, 72, 5, 72, "configurable"], [9, 84, 5, 84], [9, 85, 5, 85], [9, 87, 5, 87], [10, 6, 6, 6, "desc"], [10, 10, 6, 10], [10, 13, 6, 13], [11, 8, 6, 15, "enumerable"], [11, 18, 6, 25], [11, 20, 6, 27], [11, 24, 6, 31], [12, 8, 6, 33, "get"], [12, 11, 6, 36], [12, 13, 6, 38], [12, 22, 6, 38, "get"], [12, 23, 6, 38], [12, 25, 6, 49], [13, 10, 6, 51], [13, 17, 6, 58, "m"], [13, 18, 6, 59], [13, 19, 6, 60, "k"], [13, 20, 6, 61], [13, 21, 6, 62], [14, 8, 6, 64], [15, 6, 6, 66], [15, 7, 6, 67], [16, 4, 7, 4], [17, 4, 8, 4, "Object"], [17, 10, 8, 10], [17, 11, 8, 11, "defineProperty"], [17, 25, 8, 25], [17, 26, 8, 26, "o"], [17, 27, 8, 27], [17, 29, 8, 29, "k2"], [17, 31, 8, 31], [17, 33, 8, 33, "desc"], [17, 37, 8, 37], [17, 38, 8, 38], [18, 2, 9, 0], [18, 3, 9, 1], [18, 6, 9, 6], [18, 16, 9, 15, "o"], [18, 17, 9, 16], [18, 19, 9, 18, "m"], [18, 20, 9, 19], [18, 22, 9, 21, "k"], [18, 23, 9, 22], [18, 25, 9, 24, "k2"], [18, 27, 9, 26], [18, 29, 9, 28], [19, 4, 10, 4], [19, 8, 10, 8, "k2"], [19, 10, 10, 10], [19, 15, 10, 15, "undefined"], [19, 24, 10, 24], [19, 26, 10, 26, "k2"], [19, 28, 10, 28], [19, 31, 10, 31, "k"], [19, 32, 10, 32], [20, 4, 11, 4, "o"], [20, 5, 11, 5], [20, 6, 11, 6, "k2"], [20, 8, 11, 8], [20, 9, 11, 9], [20, 12, 11, 12, "m"], [20, 13, 11, 13], [20, 14, 11, 14, "k"], [20, 15, 11, 15], [20, 16, 11, 16], [21, 2, 12, 0], [21, 3, 12, 2], [21, 4, 12, 3], [22, 2, 13, 0], [22, 6, 13, 4, "__setModuleDefault"], [22, 24, 13, 22], [22, 27, 13, 26], [22, 31, 13, 30], [22, 35, 13, 34], [22, 39, 13, 38], [22, 40, 13, 39, "__setModuleDefault"], [22, 58, 13, 57], [22, 63, 13, 63, "Object"], [22, 69, 13, 69], [22, 70, 13, 70, "create"], [22, 76, 13, 76], [22, 79, 13, 80], [22, 89, 13, 89, "o"], [22, 90, 13, 90], [22, 92, 13, 92, "v"], [22, 93, 13, 93], [22, 95, 13, 95], [23, 4, 14, 4, "Object"], [23, 10, 14, 10], [23, 11, 14, 11, "defineProperty"], [23, 25, 14, 25], [23, 26, 14, 26, "o"], [23, 27, 14, 27], [23, 29, 14, 29], [23, 38, 14, 38], [23, 40, 14, 40], [24, 6, 14, 42, "enumerable"], [24, 16, 14, 52], [24, 18, 14, 54], [24, 22, 14, 58], [25, 6, 14, 60, "value"], [25, 11, 14, 65], [25, 13, 14, 67, "v"], [26, 4, 14, 69], [26, 5, 14, 70], [26, 6, 14, 71], [27, 2, 15, 0], [27, 3, 15, 1], [27, 6, 15, 5], [27, 16, 15, 14, "o"], [27, 17, 15, 15], [27, 19, 15, 17, "v"], [27, 20, 15, 18], [27, 22, 15, 20], [28, 4, 16, 4, "o"], [28, 5, 16, 5], [28, 6, 16, 6], [28, 15, 16, 15], [28, 16, 16, 16], [28, 19, 16, 19, "v"], [28, 20, 16, 20], [29, 2, 17, 0], [29, 3, 17, 1], [29, 4, 17, 2], [30, 2, 18, 0], [30, 6, 18, 4, "__importStar"], [30, 18, 18, 16], [30, 21, 18, 20], [30, 25, 18, 24], [30, 29, 18, 28], [30, 33, 18, 32], [30, 34, 18, 33, "__importStar"], [30, 46, 18, 45], [30, 50, 18, 51], [30, 62, 18, 63], [31, 4, 19, 4], [31, 8, 19, 8, "ownKeys"], [31, 15, 19, 15], [31, 18, 19, 18], [31, 27, 19, 18, "ownKeys"], [31, 28, 19, 27, "o"], [31, 29, 19, 28], [31, 31, 19, 30], [32, 6, 20, 8, "ownKeys"], [32, 13, 20, 15], [32, 16, 20, 18, "Object"], [32, 22, 20, 24], [32, 23, 20, 25, "getOwnPropertyNames"], [32, 42, 20, 44], [32, 46, 20, 48], [32, 56, 20, 58, "o"], [32, 57, 20, 59], [32, 59, 20, 61], [33, 8, 21, 12], [33, 12, 21, 16, "ar"], [33, 14, 21, 18], [33, 17, 21, 21], [33, 19, 21, 23], [34, 8, 22, 12], [34, 13, 22, 17], [34, 17, 22, 21, "k"], [34, 18, 22, 22], [34, 22, 22, 26, "o"], [34, 23, 22, 27], [34, 25, 22, 29], [34, 29, 22, 33, "Object"], [34, 35, 22, 39], [34, 36, 22, 40, "prototype"], [34, 45, 22, 49], [34, 46, 22, 50, "hasOwnProperty"], [34, 60, 22, 64], [34, 61, 22, 65, "call"], [34, 65, 22, 69], [34, 66, 22, 70, "o"], [34, 67, 22, 71], [34, 69, 22, 73, "k"], [34, 70, 22, 74], [34, 71, 22, 75], [34, 73, 22, 77, "ar"], [34, 75, 22, 79], [34, 76, 22, 80, "ar"], [34, 78, 22, 82], [34, 79, 22, 83, "length"], [34, 85, 22, 89], [34, 86, 22, 90], [34, 89, 22, 93, "k"], [34, 90, 22, 94], [35, 8, 23, 12], [35, 15, 23, 19, "ar"], [35, 17, 23, 21], [36, 6, 24, 8], [36, 7, 24, 9], [37, 6, 25, 8], [37, 13, 25, 15, "ownKeys"], [37, 20, 25, 22], [37, 21, 25, 23, "o"], [37, 22, 25, 24], [37, 23, 25, 25], [38, 4, 26, 4], [38, 5, 26, 5], [39, 4, 27, 4], [39, 11, 27, 11], [39, 21, 27, 21, "mod"], [39, 24, 27, 24], [39, 26, 27, 26], [40, 6, 28, 8], [40, 10, 28, 12, "mod"], [40, 13, 28, 15], [40, 17, 28, 19, "mod"], [40, 20, 28, 22], [40, 21, 28, 23, "__esModule"], [40, 31, 28, 33], [40, 33, 28, 35], [40, 40, 28, 42, "mod"], [40, 43, 28, 45], [41, 6, 29, 8], [41, 10, 29, 12, "result"], [41, 16, 29, 18], [41, 19, 29, 21], [41, 20, 29, 22], [41, 21, 29, 23], [42, 6, 30, 8], [42, 10, 30, 12, "mod"], [42, 13, 30, 15], [42, 17, 30, 19], [42, 21, 30, 23], [42, 23, 30, 25], [42, 28, 30, 30], [42, 32, 30, 34, "k"], [42, 33, 30, 35], [42, 36, 30, 38, "ownKeys"], [42, 43, 30, 45], [42, 44, 30, 46, "mod"], [42, 47, 30, 49], [42, 48, 30, 50], [42, 50, 30, 52, "i"], [42, 51, 30, 53], [42, 54, 30, 56], [42, 55, 30, 57], [42, 57, 30, 59, "i"], [42, 58, 30, 60], [42, 61, 30, 63, "k"], [42, 62, 30, 64], [42, 63, 30, 65, "length"], [42, 69, 30, 71], [42, 71, 30, 73, "i"], [42, 72, 30, 74], [42, 74, 30, 76], [42, 76, 30, 78], [42, 80, 30, 82, "k"], [42, 81, 30, 83], [42, 82, 30, 84, "i"], [42, 83, 30, 85], [42, 84, 30, 86], [42, 89, 30, 91], [42, 98, 30, 100], [42, 100, 30, 102, "__createBinding"], [42, 115, 30, 117], [42, 116, 30, 118, "result"], [42, 122, 30, 124], [42, 124, 30, 126, "mod"], [42, 127, 30, 129], [42, 129, 30, 131, "k"], [42, 130, 30, 132], [42, 131, 30, 133, "i"], [42, 132, 30, 134], [42, 133, 30, 135], [42, 134, 30, 136], [43, 6, 31, 8, "__setModuleDefault"], [43, 24, 31, 26], [43, 25, 31, 27, "result"], [43, 31, 31, 33], [43, 33, 31, 35, "mod"], [43, 36, 31, 38], [43, 37, 31, 39], [44, 6, 32, 8], [44, 13, 32, 15, "result"], [44, 19, 32, 21], [45, 4, 33, 4], [45, 5, 33, 5], [46, 2, 34, 0], [46, 3, 34, 1], [46, 4, 34, 3], [46, 5, 34, 4], [47, 2, 35, 0, "Object"], [47, 8, 35, 6], [47, 9, 35, 7, "defineProperty"], [47, 23, 35, 21], [47, 24, 35, 22, "exports"], [47, 31, 35, 29], [47, 33, 35, 31], [47, 45, 35, 43], [47, 47, 35, 45], [48, 4, 35, 47, "value"], [48, 9, 35, 52], [48, 11, 35, 54], [49, 2, 35, 59], [49, 3, 35, 60], [49, 4, 35, 61], [50, 2, 36, 0, "exports"], [50, 9, 36, 7], [50, 10, 36, 8, "renderRootComponent"], [50, 29, 36, 27], [50, 32, 36, 30, "renderRootComponent"], [50, 51, 36, 49], [51, 2, 37, 0], [51, 6, 37, 6, "expo_1"], [51, 12, 37, 12], [51, 15, 37, 15, "require"], [51, 22, 37, 22], [51, 23, 37, 22, "_dependencyMap"], [51, 37, 37, 22], [51, 48, 37, 29], [51, 49, 37, 30], [52, 2, 38, 0], [52, 6, 38, 6, "React"], [52, 11, 38, 11], [52, 14, 38, 14, "__importStar"], [52, 26, 38, 26], [52, 27, 38, 27, "require"], [52, 34, 38, 34], [52, 35, 38, 34, "_dependencyMap"], [52, 49, 38, 34], [52, 61, 38, 42], [52, 62, 38, 43], [52, 63, 38, 44], [53, 2, 39, 0], [53, 6, 39, 6, "react_native_1"], [53, 20, 39, 20], [53, 23, 39, 23, "require"], [53, 30, 39, 30], [53, 31, 39, 30, "_dependencyMap"], [53, 45, 39, 30], [53, 64, 39, 45], [53, 65, 39, 46], [54, 2, 40, 0], [54, 6, 40, 6, "SplashScreen"], [54, 18, 40, 18], [54, 21, 40, 21, "__importStar"], [54, 33, 40, 33], [54, 34, 40, 34, "require"], [54, 41, 40, 41], [54, 42, 40, 41, "_dependencyMap"], [54, 56, 40, 41], [54, 77, 40, 58], [54, 78, 40, 59], [54, 79, 40, 60], [55, 2, 41, 0], [55, 11, 41, 9, "isBaseObject"], [55, 23, 41, 21, "isBaseObject"], [55, 24, 41, 22, "obj"], [55, 27, 41, 25], [55, 29, 41, 27], [56, 4, 42, 4], [56, 8, 42, 8, "Object"], [56, 14, 42, 14], [56, 15, 42, 15, "prototype"], [56, 24, 42, 24], [56, 25, 42, 25, "toString"], [56, 33, 42, 33], [56, 34, 42, 34, "call"], [56, 38, 42, 38], [56, 39, 42, 39, "obj"], [56, 42, 42, 42], [56, 43, 42, 43], [56, 48, 42, 48], [56, 65, 42, 65], [56, 67, 42, 67], [57, 6, 43, 8], [57, 13, 43, 15], [57, 18, 43, 20], [58, 4, 44, 4], [59, 4, 45, 4], [59, 8, 45, 10, "proto"], [59, 13, 45, 15], [59, 16, 45, 18, "Object"], [59, 22, 45, 24], [59, 23, 45, 25, "getPrototypeOf"], [59, 37, 45, 39], [59, 38, 45, 40, "obj"], [59, 41, 45, 43], [59, 42, 45, 44], [60, 4, 46, 4], [60, 8, 46, 8, "proto"], [60, 13, 46, 13], [60, 18, 46, 18], [60, 22, 46, 22], [60, 24, 46, 24], [61, 6, 47, 8], [61, 13, 47, 15], [61, 17, 47, 19], [62, 4, 48, 4], [63, 4, 49, 4], [63, 11, 49, 11, "proto"], [63, 16, 49, 16], [63, 21, 49, 21, "Object"], [63, 27, 49, 27], [63, 28, 49, 28, "prototype"], [63, 37, 49, 37], [64, 2, 50, 0], [65, 2, 51, 0], [65, 11, 51, 9, "isErrorShaped"], [65, 24, 51, 22, "isErrorShaped"], [65, 25, 51, 23, "error"], [65, 30, 51, 28], [65, 32, 51, 30], [66, 4, 52, 4], [66, 11, 52, 12, "error"], [66, 16, 52, 17], [66, 20, 53, 8], [66, 27, 53, 15, "error"], [66, 32, 53, 20], [66, 37, 53, 25], [66, 45, 53, 33], [66, 49, 54, 8], [66, 56, 54, 15, "error"], [66, 61, 54, 20], [66, 62, 54, 21, "name"], [66, 66, 54, 25], [66, 71, 54, 30], [66, 79, 54, 38], [66, 83, 55, 8], [66, 90, 55, 15, "error"], [66, 95, 55, 20], [66, 96, 55, 21, "message"], [66, 103, 55, 28], [66, 108, 55, 33], [66, 116, 55, 41], [67, 2, 56, 0], [68, 2, 57, 0], [69, 0, 58, 0], [70, 0, 59, 0], [71, 0, 60, 0], [72, 2, 61, 0], [72, 11, 61, 9, "convertError"], [72, 23, 61, 21, "convertError"], [72, 24, 61, 22, "error"], [72, 29, 61, 27], [72, 31, 61, 29], [73, 4, 62, 4], [73, 8, 62, 8, "isErrorShaped"], [73, 21, 62, 21], [73, 22, 62, 22, "error"], [73, 27, 62, 27], [73, 28, 62, 28], [73, 30, 62, 30], [74, 6, 63, 8], [74, 13, 63, 15, "error"], [74, 18, 63, 20], [75, 4, 64, 4], [76, 4, 65, 4], [76, 8, 65, 8, "process"], [76, 15, 65, 15], [76, 16, 65, 16, "env"], [76, 19, 65, 19], [76, 20, 65, 20, "NODE_ENV"], [76, 28, 65, 28], [76, 33, 65, 33], [76, 46, 65, 46], [76, 48, 65, 48], [77, 6, 66, 8], [77, 10, 66, 12, "error"], [77, 15, 66, 17], [77, 19, 66, 21], [77, 23, 66, 25], [77, 25, 66, 27], [78, 8, 67, 12], [78, 15, 67, 19], [78, 19, 67, 23, "Error"], [78, 24, 67, 28], [78, 25, 67, 29], [78, 61, 67, 65], [78, 62, 67, 66], [79, 6, 68, 8], [80, 4, 69, 4], [81, 4, 70, 4], [81, 8, 70, 8, "isBaseObject"], [81, 20, 70, 20], [81, 21, 70, 21, "error"], [81, 26, 70, 26], [81, 27, 70, 27], [81, 29, 70, 29], [82, 6, 71, 8], [82, 13, 71, 15], [82, 17, 71, 19, "Error"], [82, 22, 71, 24], [82, 23, 71, 25, "JSON"], [82, 27, 71, 29], [82, 28, 71, 30, "stringify"], [82, 37, 71, 39], [82, 38, 71, 40, "error"], [82, 43, 71, 45], [82, 44, 71, 46], [82, 45, 71, 47], [83, 4, 72, 4], [84, 4, 73, 4], [84, 11, 73, 11], [84, 15, 73, 15, "Error"], [84, 20, 73, 20], [84, 21, 73, 21, "String"], [84, 27, 73, 27], [84, 28, 73, 28, "error"], [84, 33, 73, 33], [84, 34, 73, 34], [84, 35, 73, 35], [85, 2, 74, 0], [86, 2, 75, 0], [87, 0, 76, 0], [88, 0, 77, 0], [89, 0, 78, 0], [90, 2, 79, 0], [90, 11, 79, 9, "renderRootComponent"], [90, 30, 79, 28, "renderRootComponent"], [90, 31, 79, 29, "Component"], [90, 40, 79, 38], [90, 42, 79, 40], [91, 4, 80, 4], [91, 8, 80, 8], [92, 6, 81, 8], [93, 6, 82, 8, "setTimeout"], [93, 16, 82, 18], [93, 17, 82, 19], [93, 23, 82, 25], [94, 8, 83, 12, "SplashScreen"], [94, 20, 83, 24], [94, 21, 83, 25, "_internal_preventAutoHideAsync"], [94, 51, 83, 55], [94, 54, 83, 58], [94, 55, 83, 59], [95, 6, 84, 8], [95, 7, 84, 9], [95, 8, 84, 10], [96, 6, 85, 8, "React"], [96, 11, 85, 13], [96, 12, 85, 14, "startTransition"], [96, 27, 85, 29], [96, 28, 85, 30], [96, 34, 85, 36], [97, 8, 86, 12], [97, 12, 86, 16, "process"], [97, 19, 86, 23], [97, 20, 86, 24, "env"], [97, 23, 86, 27], [97, 24, 86, 28, "NODE_ENV"], [97, 32, 86, 36], [97, 37, 86, 41], [97, 49, 86, 53], [97, 51, 86, 55], [98, 10, 87, 16], [98, 14, 87, 16, "_require"], [98, 22, 87, 16], [98, 25, 87, 45, "require"], [98, 32, 87, 52], [98, 33, 87, 52, "_dependencyMap"], [98, 47, 87, 52], [98, 87, 87, 88], [98, 88, 87, 89], [99, 12, 87, 24, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [99, 28, 87, 40], [99, 31, 87, 40, "_require"], [99, 39, 87, 40], [99, 40, 87, 24, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [99, 56, 87, 40], [100, 10, 88, 16], [100, 11, 88, 17], [100, 12, 88, 18], [100, 14, 88, 20, "expo_1"], [100, 20, 88, 26], [100, 21, 88, 27, "registerRootComponent"], [100, 42, 88, 48], [100, 44, 88, 50, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [100, 60, 88, 66], [100, 61, 88, 67, "Component"], [100, 70, 88, 76], [100, 71, 88, 77], [100, 72, 88, 78], [101, 8, 89, 12], [101, 9, 89, 13], [101, 15, 90, 17], [102, 10, 91, 16], [102, 11, 91, 17], [102, 12, 91, 18], [102, 14, 91, 20, "expo_1"], [102, 20, 91, 26], [102, 21, 91, 27, "registerRootComponent"], [102, 42, 91, 48], [102, 44, 91, 50, "Component"], [102, 53, 91, 59], [102, 54, 91, 60], [103, 8, 92, 12], [104, 6, 93, 8], [104, 7, 93, 9], [104, 8, 93, 10], [105, 4, 94, 4], [105, 5, 94, 5], [105, 6, 95, 4], [105, 13, 95, 11, "e"], [105, 14, 95, 12], [105, 16, 95, 14], [106, 6, 96, 8], [107, 6, 97, 8, "SplashScreen"], [107, 18, 97, 20], [107, 19, 97, 21, "<PERSON><PERSON><PERSON>"], [107, 28, 97, 30], [107, 29, 97, 31], [107, 30, 97, 32], [108, 6, 98, 8], [108, 10, 98, 14, "error"], [108, 15, 98, 19], [108, 18, 98, 22, "convertError"], [108, 30, 98, 34], [108, 31, 98, 35, "e"], [108, 32, 98, 36], [108, 33, 98, 37], [109, 6, 99, 8], [110, 6, 100, 8], [111, 6, 101, 8], [112, 6, 102, 8], [113, 6, 103, 8], [113, 7, 103, 9], [113, 8, 103, 10], [113, 10, 103, 12, "expo_1"], [113, 16, 103, 18], [113, 17, 103, 19, "registerRootComponent"], [113, 38, 103, 40], [113, 40, 103, 42], [113, 59, 103, 48, "_reactJsxDevRuntime"], [113, 78, 103, 48], [113, 79, 103, 48, "jsxDEV"], [113, 85, 103, 48], [113, 86, 103, 49, "react_native_1"], [113, 100, 103, 63], [113, 101, 103, 64, "View"], [113, 105, 103, 68], [114, 8, 103, 68, "fileName"], [114, 16, 103, 68], [114, 18, 103, 68, "_jsxFileName"], [114, 30, 103, 68], [115, 8, 103, 68, "lineNumber"], [115, 18, 103, 68], [116, 8, 103, 68, "columnNumber"], [116, 20, 103, 68], [117, 6, 103, 68], [117, 13, 103, 70], [117, 14, 103, 71], [117, 15, 103, 72], [118, 6, 104, 8], [119, 6, 105, 8], [119, 17, 105, 43], [120, 8, 106, 12, "console"], [120, 15, 106, 19], [120, 16, 106, 20, "error"], [120, 21, 106, 25], [120, 22, 106, 26, "error"], [120, 27, 106, 31], [120, 28, 106, 32], [121, 8, 107, 12, "console"], [121, 15, 107, 19], [121, 16, 107, 20, "error"], [121, 21, 107, 25], [121, 22, 107, 26], [121, 88, 107, 92], [121, 89, 107, 93], [122, 6, 108, 8], [123, 6, 109, 8], [124, 6, 110, 8, "setTimeout"], [124, 16, 110, 18], [124, 17, 110, 19], [124, 23, 110, 25], [125, 8, 111, 12], [125, 14, 111, 18, "error"], [125, 19, 111, 23], [126, 6, 112, 8], [126, 7, 112, 9], [126, 8, 112, 10], [127, 6, 113, 8], [128, 4, 114, 4], [129, 2, 115, 0], [130, 0, 115, 1], [130, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "isBaseObject", "isErrorShaped", "convertError", "renderRootComponent", "setTimeout$argument_0", "React.startTransition$argument_0"], "mappings": "AAA;0ECC;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;AIO;CJS;AKC;CLK;AMK;CNa;AOK;mBCG;SDE;8BEC;SFQ;0CNU,6BM;mBCO;SDE;CPG"}}, "type": "js/module"}]}