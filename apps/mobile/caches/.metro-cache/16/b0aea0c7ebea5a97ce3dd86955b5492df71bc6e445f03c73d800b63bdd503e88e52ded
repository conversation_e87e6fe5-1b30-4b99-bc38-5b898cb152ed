{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./NativeBlobModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 50}}], "key": "U6TvaDH8SXQ+L3uxoXKyiOMXH+8=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "./Blob", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 37}}], "key": "mgvDEIvsyYQQJCSt18UmaoHoX6k=", "exportNames": ["*"]}}, {"name": "./BlobRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 46}}], "key": "JHbMLLFwEH95R1ixWPXDXrku130=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _NativeBlobModule = _interopRequireDefault(require(_dependencyMap[3], \"./NativeBlobModule\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[4], \"invariant\"));\n  var Blob = require(_dependencyMap[5], \"./Blob\").default;\n  var BlobRegistry = require(_dependencyMap[6], \"./BlobRegistry\");\n  function uuidv4() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n      var r = Math.random() * 16 | 0,\n        v = c == 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  function createBlobCollector(blobId) {\n    if (global.__blobCollectorProvider == null) {\n      return null;\n    } else {\n      return global.__blobCollectorProvider(blobId);\n    }\n  }\n  var BlobManager = /*#__PURE__*/function () {\n    function BlobManager() {\n      (0, _classCallCheck2.default)(this, BlobManager);\n    }\n    return (0, _createClass2.default)(BlobManager, null, [{\n      key: \"createFromParts\",\n      value: function createFromParts(parts, options) {\n        (0, _invariant.default)(_NativeBlobModule.default, 'NativeBlobModule is available.');\n        var blobId = uuidv4();\n        var items = parts.map(part => {\n          if (part instanceof ArrayBuffer || ArrayBuffer.isView(part)) {\n            throw new Error(\"Creating blobs from 'ArrayBuffer' and 'ArrayBufferView' are not supported\");\n          }\n          if (part instanceof Blob) {\n            return {\n              data: part.data,\n              type: 'blob'\n            };\n          } else {\n            return {\n              data: String(part),\n              type: 'string'\n            };\n          }\n        });\n        var size = items.reduce((acc, curr) => {\n          if (curr.type === 'string') {\n            return acc + global.unescape(encodeURI(curr.data)).length;\n          } else {\n            return acc + curr.data.size;\n          }\n        }, 0);\n        _NativeBlobModule.default.createFromParts(items, blobId);\n        return BlobManager.createFromOptions({\n          blobId,\n          offset: 0,\n          size,\n          type: options ? options.type : '',\n          lastModified: options ? options.lastModified : Date.now()\n        });\n      }\n    }, {\n      key: \"createFromOptions\",\n      value: function createFromOptions(options) {\n        BlobRegistry.register(options.blobId);\n        return Object.assign(Object.create(Blob.prototype), {\n          data: options.__collector == null ? {\n            ...options,\n            __collector: createBlobCollector(options.blobId)\n          } : options\n        });\n      }\n    }, {\n      key: \"release\",\n      value: function release(blobId) {\n        (0, _invariant.default)(_NativeBlobModule.default, 'NativeBlobModule is available.');\n        BlobRegistry.unregister(blobId);\n        if (BlobRegistry.has(blobId)) {\n          return;\n        }\n        _NativeBlobModule.default.release(blobId);\n      }\n    }, {\n      key: \"addNetworkingHandler\",\n      value: function addNetworkingHandler() {\n        (0, _invariant.default)(_NativeBlobModule.default, 'NativeBlobModule is available.');\n        _NativeBlobModule.default.addNetworkingHandler();\n      }\n    }, {\n      key: \"addWebSocketHandler\",\n      value: function addWebSocketHandler(socketId) {\n        (0, _invariant.default)(_NativeBlobModule.default, 'NativeBlobModule is available.');\n        _NativeBlobModule.default.addWebSocketHandler(socketId);\n      }\n    }, {\n      key: \"removeWebSocketHandler\",\n      value: function removeWebSocketHandler(socketId) {\n        (0, _invariant.default)(_NativeBlobModule.default, 'NativeBlobModule is available.');\n        _NativeBlobModule.default.removeWebSocketHandler(socketId);\n      }\n    }, {\n      key: \"sendOverSocket\",\n      value: function sendOverSocket(blob, socketId) {\n        (0, _invariant.default)(_NativeBlobModule.default, 'NativeBlobModule is available.');\n        _NativeBlobModule.default.sendOverSocket(blob.data, socketId);\n      }\n    }]);\n  }();\n  BlobManager.isAvailable = !!_NativeBlobModule.default;\n  var _default = exports.default = BlobManager;\n});", "lineCount": 117, "map": [[9, 2, 14, 0], [9, 6, 14, 0, "_NativeBlobModule"], [9, 23, 14, 0], [9, 26, 14, 0, "_interopRequireDefault"], [9, 48, 14, 0], [9, 49, 14, 0, "require"], [9, 56, 14, 0], [9, 57, 14, 0, "_dependencyMap"], [9, 71, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_invariant"], [10, 16, 15, 0], [10, 19, 15, 0, "_interopRequireDefault"], [10, 41, 15, 0], [10, 42, 15, 0, "require"], [10, 49, 15, 0], [10, 50, 15, 0, "_dependencyMap"], [10, 64, 15, 0], [11, 2, 17, 0], [11, 6, 17, 6, "Blob"], [11, 10, 17, 17], [11, 13, 17, 20, "require"], [11, 20, 17, 27], [11, 21, 17, 27, "_dependencyMap"], [11, 35, 17, 27], [11, 48, 17, 36], [11, 49, 17, 37], [11, 50, 17, 38, "default"], [11, 57, 17, 45], [12, 2, 18, 0], [12, 6, 18, 6, "BlobRegistry"], [12, 18, 18, 18], [12, 21, 18, 21, "require"], [12, 28, 18, 28], [12, 29, 18, 28, "_dependencyMap"], [12, 43, 18, 28], [12, 64, 18, 45], [12, 65, 18, 46], [13, 2, 27, 0], [13, 11, 27, 9, "uuidv4"], [13, 17, 27, 15, "uuidv4"], [13, 18, 27, 15], [13, 20, 27, 26], [14, 4, 28, 2], [14, 11, 28, 9], [14, 49, 28, 47], [14, 50, 28, 48, "replace"], [14, 57, 28, 55], [14, 58, 28, 56], [14, 65, 28, 63], [14, 67, 28, 65, "c"], [14, 68, 28, 66], [14, 72, 28, 70], [15, 6, 29, 4], [15, 10, 29, 10, "r"], [15, 11, 29, 11], [15, 14, 29, 15, "Math"], [15, 18, 29, 19], [15, 19, 29, 20, "random"], [15, 25, 29, 26], [15, 26, 29, 27], [15, 27, 29, 28], [15, 30, 29, 31], [15, 32, 29, 33], [15, 35, 29, 37], [15, 36, 29, 38], [16, 8, 30, 6, "v"], [16, 9, 30, 7], [16, 12, 30, 10, "c"], [16, 13, 30, 11], [16, 17, 30, 15], [16, 20, 30, 18], [16, 23, 30, 21, "r"], [16, 24, 30, 22], [16, 27, 30, 26, "r"], [16, 28, 30, 27], [16, 31, 30, 30], [16, 34, 30, 33], [16, 37, 30, 37], [16, 40, 30, 40], [17, 6, 31, 4], [17, 13, 31, 11, "v"], [17, 14, 31, 12], [17, 15, 31, 13, "toString"], [17, 23, 31, 21], [17, 24, 31, 22], [17, 26, 31, 24], [17, 27, 31, 25], [18, 4, 32, 2], [18, 5, 32, 3], [18, 6, 32, 4], [19, 2, 33, 0], [20, 2, 42, 0], [20, 11, 42, 9, "createBlobCollector"], [20, 30, 42, 28, "createBlobCollector"], [20, 31, 42, 29, "blobId"], [20, 37, 42, 43], [20, 39, 42, 67], [21, 4, 43, 2], [21, 8, 43, 6, "global"], [21, 14, 43, 12], [21, 15, 43, 13, "__blobCollectorProvider"], [21, 38, 43, 36], [21, 42, 43, 40], [21, 46, 43, 44], [21, 48, 43, 46], [22, 6, 44, 4], [22, 13, 44, 11], [22, 17, 44, 15], [23, 4, 45, 2], [23, 5, 45, 3], [23, 11, 45, 9], [24, 6, 46, 4], [24, 13, 46, 11, "global"], [24, 19, 46, 17], [24, 20, 46, 18, "__blobCollectorProvider"], [24, 43, 46, 41], [24, 44, 46, 42, "blobId"], [24, 50, 46, 48], [24, 51, 46, 49], [25, 4, 47, 2], [26, 2, 48, 0], [27, 2, 48, 1], [27, 6, 53, 6, "BlobManager"], [27, 17, 53, 17], [28, 4, 53, 17], [28, 13, 53, 17, "BlobManager"], [28, 25, 53, 17], [29, 6, 53, 17], [29, 10, 53, 17, "_classCallCheck2"], [29, 26, 53, 17], [29, 27, 53, 17, "default"], [29, 34, 53, 17], [29, 42, 53, 17, "BlobManager"], [29, 53, 53, 17], [30, 4, 53, 17], [31, 4, 53, 17], [31, 15, 53, 17, "_createClass2"], [31, 28, 53, 17], [31, 29, 53, 17, "default"], [31, 36, 53, 17], [31, 38, 53, 17, "BlobManager"], [31, 49, 53, 17], [32, 6, 53, 17, "key"], [32, 9, 53, 17], [33, 6, 53, 17, "value"], [33, 11, 53, 17], [33, 13, 62, 2], [33, 22, 62, 9, "createFromParts"], [33, 37, 62, 24, "createFromParts"], [33, 38, 63, 4, "parts"], [33, 43, 63, 31], [33, 45, 64, 4, "options"], [33, 52, 64, 25], [33, 54, 65, 10], [34, 8, 66, 4], [34, 12, 66, 4, "invariant"], [34, 30, 66, 13], [34, 32, 66, 14, "NativeBlobModule"], [34, 57, 66, 30], [34, 59, 66, 32], [34, 91, 66, 64], [34, 92, 66, 65], [35, 8, 68, 4], [35, 12, 68, 10, "blobId"], [35, 18, 68, 16], [35, 21, 68, 19, "uuidv4"], [35, 27, 68, 25], [35, 28, 68, 26], [35, 29, 68, 27], [36, 8, 69, 4], [36, 12, 69, 10, "items"], [36, 17, 69, 15], [36, 20, 69, 18, "parts"], [36, 25, 69, 23], [36, 26, 69, 24, "map"], [36, 29, 69, 27], [36, 30, 69, 28, "part"], [36, 34, 69, 32], [36, 38, 69, 36], [37, 10, 70, 6], [37, 14, 70, 10, "part"], [37, 18, 70, 14], [37, 30, 70, 26, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [37, 41, 70, 37], [37, 45, 70, 41, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [37, 56, 70, 52], [37, 57, 70, 53, "<PERSON><PERSON><PERSON><PERSON>"], [37, 63, 70, 59], [37, 64, 70, 60, "part"], [37, 68, 70, 64], [37, 69, 70, 65], [37, 71, 70, 67], [38, 12, 71, 8], [38, 18, 71, 14], [38, 22, 71, 18, "Error"], [38, 27, 71, 23], [38, 28, 72, 10], [38, 103, 73, 8], [38, 104, 73, 9], [39, 10, 74, 6], [40, 10, 75, 6], [40, 14, 75, 10, "part"], [40, 18, 75, 14], [40, 30, 75, 26, "Blob"], [40, 34, 75, 30], [40, 36, 75, 32], [41, 12, 76, 8], [41, 19, 76, 15], [42, 14, 77, 10, "data"], [42, 18, 77, 14], [42, 20, 77, 16, "part"], [42, 24, 77, 20], [42, 25, 77, 21, "data"], [42, 29, 77, 25], [43, 14, 78, 10, "type"], [43, 18, 78, 14], [43, 20, 78, 16], [44, 12, 79, 8], [44, 13, 79, 9], [45, 10, 80, 6], [45, 11, 80, 7], [45, 17, 80, 13], [46, 12, 81, 8], [46, 19, 81, 15], [47, 14, 82, 10, "data"], [47, 18, 82, 14], [47, 20, 82, 16, "String"], [47, 26, 82, 22], [47, 27, 82, 23, "part"], [47, 31, 82, 27], [47, 32, 82, 28], [48, 14, 83, 10, "type"], [48, 18, 83, 14], [48, 20, 83, 16], [49, 12, 84, 8], [49, 13, 84, 9], [50, 10, 85, 6], [51, 8, 86, 4], [51, 9, 86, 5], [51, 10, 86, 6], [52, 8, 87, 4], [52, 12, 87, 10, "size"], [52, 16, 87, 14], [52, 19, 87, 17, "items"], [52, 24, 87, 22], [52, 25, 87, 23, "reduce"], [52, 31, 87, 29], [52, 32, 87, 30], [52, 33, 87, 31, "acc"], [52, 36, 87, 34], [52, 38, 87, 36, "curr"], [52, 42, 87, 40], [52, 47, 87, 45], [53, 10, 88, 6], [53, 14, 88, 10, "curr"], [53, 18, 88, 14], [53, 19, 88, 15, "type"], [53, 23, 88, 19], [53, 28, 88, 24], [53, 36, 88, 32], [53, 38, 88, 34], [54, 12, 89, 8], [54, 19, 89, 15, "acc"], [54, 22, 89, 18], [54, 25, 89, 21, "global"], [54, 31, 89, 27], [54, 32, 89, 28, "unescape"], [54, 40, 89, 36], [54, 41, 89, 37, "encodeURI"], [54, 50, 89, 46], [54, 51, 89, 47, "curr"], [54, 55, 89, 51], [54, 56, 89, 52, "data"], [54, 60, 89, 56], [54, 61, 89, 57], [54, 62, 89, 58], [54, 63, 89, 59, "length"], [54, 69, 89, 65], [55, 10, 90, 6], [55, 11, 90, 7], [55, 17, 90, 13], [56, 12, 91, 8], [56, 19, 91, 15, "acc"], [56, 22, 91, 18], [56, 25, 91, 21, "curr"], [56, 29, 91, 25], [56, 30, 91, 26, "data"], [56, 34, 91, 30], [56, 35, 91, 31, "size"], [56, 39, 91, 35], [57, 10, 92, 6], [58, 8, 93, 4], [58, 9, 93, 5], [58, 11, 93, 7], [58, 12, 93, 8], [58, 13, 93, 9], [59, 8, 95, 4, "NativeBlobModule"], [59, 33, 95, 20], [59, 34, 95, 21, "createFromParts"], [59, 49, 95, 36], [59, 50, 95, 37, "items"], [59, 55, 95, 42], [59, 57, 95, 44, "blobId"], [59, 63, 95, 50], [59, 64, 95, 51], [60, 8, 97, 4], [60, 15, 97, 11, "BlobManager"], [60, 26, 97, 22], [60, 27, 97, 23, "createFromOptions"], [60, 44, 97, 40], [60, 45, 97, 41], [61, 10, 98, 6, "blobId"], [61, 16, 98, 12], [62, 10, 99, 6, "offset"], [62, 16, 99, 12], [62, 18, 99, 14], [62, 19, 99, 15], [63, 10, 100, 6, "size"], [63, 14, 100, 10], [64, 10, 101, 6, "type"], [64, 14, 101, 10], [64, 16, 101, 12, "options"], [64, 23, 101, 19], [64, 26, 101, 22, "options"], [64, 33, 101, 29], [64, 34, 101, 30, "type"], [64, 38, 101, 34], [64, 41, 101, 37], [64, 43, 101, 39], [65, 10, 102, 6, "lastModified"], [65, 22, 102, 18], [65, 24, 102, 20, "options"], [65, 31, 102, 27], [65, 34, 102, 30, "options"], [65, 41, 102, 37], [65, 42, 102, 38, "lastModified"], [65, 54, 102, 50], [65, 57, 102, 53, "Date"], [65, 61, 102, 57], [65, 62, 102, 58, "now"], [65, 65, 102, 61], [65, 66, 102, 62], [66, 8, 103, 4], [66, 9, 103, 5], [66, 10, 103, 6], [67, 6, 104, 2], [68, 4, 104, 3], [69, 6, 104, 3, "key"], [69, 9, 104, 3], [70, 6, 104, 3, "value"], [70, 11, 104, 3], [70, 13, 110, 2], [70, 22, 110, 9, "createFromOptions"], [70, 39, 110, 26, "createFromOptions"], [70, 40, 110, 27, "options"], [70, 47, 110, 44], [70, 49, 110, 52], [71, 8, 111, 4, "BlobRegistry"], [71, 20, 111, 16], [71, 21, 111, 17, "register"], [71, 29, 111, 25], [71, 30, 111, 26, "options"], [71, 37, 111, 33], [71, 38, 111, 34, "blobId"], [71, 44, 111, 40], [71, 45, 111, 41], [72, 8, 113, 4], [72, 15, 113, 11, "Object"], [72, 21, 113, 17], [72, 22, 113, 18, "assign"], [72, 28, 113, 24], [72, 29, 113, 25, "Object"], [72, 35, 113, 31], [72, 36, 113, 32, "create"], [72, 42, 113, 38], [72, 43, 113, 39, "Blob"], [72, 47, 113, 43], [72, 48, 113, 44, "prototype"], [72, 57, 113, 53], [72, 58, 113, 54], [72, 60, 113, 56], [73, 10, 114, 6, "data"], [73, 14, 114, 10], [73, 16, 118, 8, "options"], [73, 23, 118, 15], [73, 24, 118, 16, "__collector"], [73, 35, 118, 27], [73, 39, 118, 31], [73, 43, 118, 35], [73, 46, 119, 12], [74, 12, 120, 14], [74, 15, 120, 17, "options"], [74, 22, 120, 24], [75, 12, 121, 14, "__collector"], [75, 23, 121, 25], [75, 25, 121, 27, "createBlobCollector"], [75, 44, 121, 46], [75, 45, 121, 47, "options"], [75, 52, 121, 54], [75, 53, 121, 55, "blobId"], [75, 59, 121, 61], [76, 10, 122, 12], [76, 11, 122, 13], [76, 14, 123, 12, "options"], [77, 8, 124, 4], [77, 9, 124, 5], [77, 10, 124, 6], [78, 6, 125, 2], [79, 4, 125, 3], [80, 6, 125, 3, "key"], [80, 9, 125, 3], [81, 6, 125, 3, "value"], [81, 11, 125, 3], [81, 13, 130, 2], [81, 22, 130, 9, "release"], [81, 29, 130, 16, "release"], [81, 30, 130, 17, "blobId"], [81, 36, 130, 31], [81, 38, 130, 39], [82, 8, 131, 4], [82, 12, 131, 4, "invariant"], [82, 30, 131, 13], [82, 32, 131, 14, "NativeBlobModule"], [82, 57, 131, 30], [82, 59, 131, 32], [82, 91, 131, 64], [82, 92, 131, 65], [83, 8, 133, 4, "BlobRegistry"], [83, 20, 133, 16], [83, 21, 133, 17, "unregister"], [83, 31, 133, 27], [83, 32, 133, 28, "blobId"], [83, 38, 133, 34], [83, 39, 133, 35], [84, 8, 134, 4], [84, 12, 134, 8, "BlobRegistry"], [84, 24, 134, 20], [84, 25, 134, 21, "has"], [84, 28, 134, 24], [84, 29, 134, 25, "blobId"], [84, 35, 134, 31], [84, 36, 134, 32], [84, 38, 134, 34], [85, 10, 135, 6], [86, 8, 136, 4], [87, 8, 137, 4, "NativeBlobModule"], [87, 33, 137, 20], [87, 34, 137, 21, "release"], [87, 41, 137, 28], [87, 42, 137, 29, "blobId"], [87, 48, 137, 35], [87, 49, 137, 36], [88, 6, 138, 2], [89, 4, 138, 3], [90, 6, 138, 3, "key"], [90, 9, 138, 3], [91, 6, 138, 3, "value"], [91, 11, 138, 3], [91, 13, 144, 2], [91, 22, 144, 9, "addNetworkingHandler"], [91, 42, 144, 29, "addNetworkingHandler"], [91, 43, 144, 29], [91, 45, 144, 38], [92, 8, 145, 4], [92, 12, 145, 4, "invariant"], [92, 30, 145, 13], [92, 32, 145, 14, "NativeBlobModule"], [92, 57, 145, 30], [92, 59, 145, 32], [92, 91, 145, 64], [92, 92, 145, 65], [93, 8, 147, 4, "NativeBlobModule"], [93, 33, 147, 20], [93, 34, 147, 21, "addNetworkingHandler"], [93, 54, 147, 41], [93, 55, 147, 42], [93, 56, 147, 43], [94, 6, 148, 2], [95, 4, 148, 3], [96, 6, 148, 3, "key"], [96, 9, 148, 3], [97, 6, 148, 3, "value"], [97, 11, 148, 3], [97, 13, 154, 2], [97, 22, 154, 9, "addWebSocketHandler"], [97, 41, 154, 28, "addWebSocketHandler"], [97, 42, 154, 29, "socketId"], [97, 50, 154, 45], [97, 52, 154, 53], [98, 8, 155, 4], [98, 12, 155, 4, "invariant"], [98, 30, 155, 13], [98, 32, 155, 14, "NativeBlobModule"], [98, 57, 155, 30], [98, 59, 155, 32], [98, 91, 155, 64], [98, 92, 155, 65], [99, 8, 157, 4, "NativeBlobModule"], [99, 33, 157, 20], [99, 34, 157, 21, "addWebSocketHandler"], [99, 53, 157, 40], [99, 54, 157, 41, "socketId"], [99, 62, 157, 49], [99, 63, 157, 50], [100, 6, 158, 2], [101, 4, 158, 3], [102, 6, 158, 3, "key"], [102, 9, 158, 3], [103, 6, 158, 3, "value"], [103, 11, 158, 3], [103, 13, 164, 2], [103, 22, 164, 9, "removeWebSocketHandler"], [103, 44, 164, 31, "removeWebSocketHandler"], [103, 45, 164, 32, "socketId"], [103, 53, 164, 48], [103, 55, 164, 56], [104, 8, 165, 4], [104, 12, 165, 4, "invariant"], [104, 30, 165, 13], [104, 32, 165, 14, "NativeBlobModule"], [104, 57, 165, 30], [104, 59, 165, 32], [104, 91, 165, 64], [104, 92, 165, 65], [105, 8, 167, 4, "NativeBlobModule"], [105, 33, 167, 20], [105, 34, 167, 21, "removeWebSocketHandler"], [105, 56, 167, 43], [105, 57, 167, 44, "socketId"], [105, 65, 167, 52], [105, 66, 167, 53], [106, 6, 168, 2], [107, 4, 168, 3], [108, 6, 168, 3, "key"], [108, 9, 168, 3], [109, 6, 168, 3, "value"], [109, 11, 168, 3], [109, 13, 173, 2], [109, 22, 173, 9, "sendOverSocket"], [109, 36, 173, 23, "sendOverSocket"], [109, 37, 173, 24, "blob"], [109, 41, 173, 34], [109, 43, 173, 36, "socketId"], [109, 51, 173, 52], [109, 53, 173, 60], [110, 8, 174, 4], [110, 12, 174, 4, "invariant"], [110, 30, 174, 13], [110, 32, 174, 14, "NativeBlobModule"], [110, 57, 174, 30], [110, 59, 174, 32], [110, 91, 174, 64], [110, 92, 174, 65], [111, 8, 176, 4, "NativeBlobModule"], [111, 33, 176, 20], [111, 34, 176, 21, "sendOverSocket"], [111, 48, 176, 35], [111, 49, 176, 36, "blob"], [111, 53, 176, 40], [111, 54, 176, 41, "data"], [111, 58, 176, 45], [111, 60, 176, 47, "socketId"], [111, 68, 176, 55], [111, 69, 176, 56], [112, 6, 177, 2], [113, 4, 177, 3], [114, 2, 177, 3], [115, 2, 53, 6, "BlobManager"], [115, 13, 53, 17], [115, 14, 57, 9, "isAvailable"], [115, 25, 57, 20], [115, 28, 57, 32], [115, 29, 57, 33], [115, 30, 57, 34, "NativeBlobModule"], [115, 55, 57, 50], [116, 2, 57, 50], [116, 6, 57, 50, "_default"], [116, 14, 57, 50], [116, 17, 57, 50, "exports"], [116, 24, 57, 50], [116, 25, 57, 50, "default"], [116, 32, 57, 50], [116, 35, 180, 15, "BlobManager"], [116, 46, 180, 26], [117, 0, 180, 26], [117, 3]], "functionMap": {"names": ["<global>", "uuidv4", "xxxxxxxxXxxx4xxxYxxxXxxxxxxxxxxx.replace$argument_1", "createBlobCollector", "BlobManager", "createFromParts", "parts.map$argument_0", "items.reduce$argument_0", "createFromOptions", "release", "addNetworkingHandler", "addWebSocketHandler", "removeWebSocketHandler", "sendOverSocket"], "mappings": "AAA;AC0B;iECC;GDI;CDC;AGS;CHM;AIK;ECS;4BCO;KDiB;8BEC;KFM;GDW;EIM;GJe;EKK;GLQ;EMM;GNI;EOM;GPI;EQM;GRI;ESK;GTI;CJC"}}, "type": "js/module"}]}