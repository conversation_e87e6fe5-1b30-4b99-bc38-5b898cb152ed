{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var PressabilityPerformanceEventEmitter = /*#__PURE__*/function () {\n    function PressabilityPerformanceEventEmitter() {\n      (0, _classCallCheck2.default)(this, PressabilityPerformanceEventEmitter);\n      this._listeners = [];\n    }\n    return (0, _createClass2.default)(PressabilityPerformanceEventEmitter, [{\n      key: \"addListener\",\n      value: function addListener(listener) {\n        this._listeners.push(listener);\n      }\n    }, {\n      key: \"removeListener\",\n      value: function removeListener(listener) {\n        var index = this._listeners.indexOf(listener);\n        if (index > -1) {\n          this._listeners.splice(index, 1);\n        }\n      }\n    }, {\n      key: \"emitEvent\",\n      value: function emitEvent(constructEvent) {\n        if (this._listeners.length === 0) {\n          return;\n        }\n        var event = constructEvent();\n        this._listeners.forEach(listener => listener(event));\n      }\n    }]);\n  }();\n  var PressabilityPerformanceEventEmitterSingleton = new PressabilityPerformanceEventEmitter();\n  var _default = exports.default = PressabilityPerformanceEventEmitterSingleton;\n});", "lineCount": 40, "map": [[9, 6, 20, 6, "PressabilityPerformanceEventEmitter"], [9, 41, 20, 41], [10, 4, 23, 2], [10, 13, 23, 2, "PressabilityPerformanceEventEmitter"], [10, 49, 23, 2], [10, 51, 23, 16], [11, 6, 23, 16], [11, 10, 23, 16, "_classCallCheck2"], [11, 26, 23, 16], [11, 27, 23, 16, "default"], [11, 34, 23, 16], [11, 42, 23, 16, "PressabilityPerformanceEventEmitter"], [11, 77, 23, 16], [12, 6, 23, 16], [12, 11, 21, 2, "_listeners"], [12, 21, 21, 12], [12, 24, 21, 60], [12, 26, 21, 62], [13, 4, 23, 17], [14, 4, 23, 18], [14, 15, 23, 18, "_createClass2"], [14, 28, 23, 18], [14, 29, 23, 18, "default"], [14, 36, 23, 18], [14, 38, 23, 18, "PressabilityPerformanceEventEmitter"], [14, 73, 23, 18], [15, 6, 23, 18, "key"], [15, 9, 23, 18], [16, 6, 23, 18, "value"], [16, 11, 23, 18], [16, 13, 25, 2], [16, 22, 25, 2, "addListener"], [16, 33, 25, 13, "addListener"], [16, 34, 25, 14, "listener"], [16, 42, 25, 60], [16, 44, 25, 68], [17, 8, 26, 4], [17, 12, 26, 8], [17, 13, 26, 9, "_listeners"], [17, 23, 26, 19], [17, 24, 26, 20, "push"], [17, 28, 26, 24], [17, 29, 26, 25, "listener"], [17, 37, 26, 33], [17, 38, 26, 34], [18, 6, 27, 2], [19, 4, 27, 3], [20, 6, 27, 3, "key"], [20, 9, 27, 3], [21, 6, 27, 3, "value"], [21, 11, 27, 3], [21, 13, 29, 2], [21, 22, 29, 2, "removeListener"], [21, 36, 29, 16, "removeListener"], [21, 37, 29, 17, "listener"], [21, 45, 29, 63], [21, 47, 29, 71], [22, 8, 30, 4], [22, 12, 30, 10, "index"], [22, 17, 30, 15], [22, 20, 30, 18], [22, 24, 30, 22], [22, 25, 30, 23, "_listeners"], [22, 35, 30, 33], [22, 36, 30, 34, "indexOf"], [22, 43, 30, 41], [22, 44, 30, 42, "listener"], [22, 52, 30, 50], [22, 53, 30, 51], [23, 8, 31, 4], [23, 12, 31, 8, "index"], [23, 17, 31, 13], [23, 20, 31, 16], [23, 21, 31, 17], [23, 22, 31, 18], [23, 24, 31, 20], [24, 10, 32, 6], [24, 14, 32, 10], [24, 15, 32, 11, "_listeners"], [24, 25, 32, 21], [24, 26, 32, 22, "splice"], [24, 32, 32, 28], [24, 33, 32, 29, "index"], [24, 38, 32, 34], [24, 40, 32, 36], [24, 41, 32, 37], [24, 42, 32, 38], [25, 8, 33, 4], [26, 6, 34, 2], [27, 4, 34, 3], [28, 6, 34, 3, "key"], [28, 9, 34, 3], [29, 6, 34, 3, "value"], [29, 11, 34, 3], [29, 13, 36, 2], [29, 22, 36, 2, "emitEvent"], [29, 31, 36, 11, "emitEvent"], [29, 32, 36, 12, "constructEvent"], [29, 46, 36, 62], [29, 48, 36, 70], [30, 8, 37, 4], [30, 12, 37, 8], [30, 16, 37, 12], [30, 17, 37, 13, "_listeners"], [30, 27, 37, 23], [30, 28, 37, 24, "length"], [30, 34, 37, 30], [30, 39, 37, 35], [30, 40, 37, 36], [30, 42, 37, 38], [31, 10, 38, 6], [32, 8, 39, 4], [33, 8, 41, 4], [33, 12, 41, 10, "event"], [33, 17, 41, 15], [33, 20, 41, 18, "constructEvent"], [33, 34, 41, 32], [33, 35, 41, 33], [33, 36, 41, 34], [34, 8, 42, 4], [34, 12, 42, 8], [34, 13, 42, 9, "_listeners"], [34, 23, 42, 19], [34, 24, 42, 20, "for<PERSON>ach"], [34, 31, 42, 27], [34, 32, 42, 28, "listener"], [34, 40, 42, 36], [34, 44, 42, 40, "listener"], [34, 52, 42, 48], [34, 53, 42, 49, "event"], [34, 58, 42, 54], [34, 59, 42, 55], [34, 60, 42, 56], [35, 6, 43, 2], [36, 4, 43, 3], [37, 2, 43, 3], [38, 2, 46, 0], [38, 6, 46, 6, "PressabilityPerformanceEventEmitterSingleton"], [38, 50, 46, 87], [38, 53, 47, 2], [38, 57, 47, 6, "PressabilityPerformanceEventEmitter"], [38, 92, 47, 41], [38, 93, 47, 42], [38, 94, 47, 43], [39, 2, 47, 44], [39, 6, 47, 44, "_default"], [39, 14, 47, 44], [39, 17, 47, 44, "exports"], [39, 24, 47, 44], [39, 25, 47, 44, "default"], [39, 32, 47, 44], [39, 35, 49, 15, "PressabilityPerformanceEventEmitterSingleton"], [39, 79, 49, 59], [40, 0, 49, 59], [40, 3]], "functionMap": {"names": ["<global>", "PressabilityPerformanceEventEmitter", "constructor", "addListener", "removeListener", "emitEvent", "_listeners.forEach$argument_0"], "mappings": "AAA;ACmB;ECG,gBD;EEE;GFE;EGE;GHK;EIE;4BCM,2BD;GJC;CDC"}}, "type": "js/module"}]}