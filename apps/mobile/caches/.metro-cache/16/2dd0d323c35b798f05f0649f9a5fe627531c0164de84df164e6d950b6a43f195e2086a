{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  module.exports = string => {\n    if (typeof string !== 'string') {\n      throw new TypeError('Expected a string');\n    }\n\n    // Escape characters with special meaning either inside or outside character sets.\n    // Use a simple backslash escape when it’s always valid, and a \\unnnn escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n    return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d');\n  };\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "module"], [4, 8, 3, 6], [4, 9, 3, 7, "exports"], [4, 16, 3, 14], [4, 19, 3, 17, "string"], [4, 25, 3, 23], [4, 29, 3, 27], [5, 4, 4, 1], [5, 8, 4, 5], [5, 15, 4, 12, "string"], [5, 21, 4, 18], [5, 26, 4, 23], [5, 34, 4, 31], [5, 36, 4, 33], [6, 6, 5, 2], [6, 12, 5, 8], [6, 16, 5, 12, "TypeError"], [6, 25, 5, 21], [6, 26, 5, 22], [6, 45, 5, 41], [6, 46, 5, 42], [7, 4, 6, 1], [9, 4, 8, 1], [10, 4, 9, 1], [11, 4, 10, 1], [11, 11, 10, 8, "string"], [11, 17, 10, 14], [11, 18, 11, 3, "replace"], [11, 25, 11, 10], [11, 26, 11, 11], [11, 47, 11, 32], [11, 49, 11, 34], [11, 55, 11, 40], [11, 56, 11, 41], [11, 57, 12, 3, "replace"], [11, 64, 12, 10], [11, 65, 12, 11], [11, 69, 12, 15], [11, 71, 12, 17], [11, 78, 12, 24], [11, 79, 12, 25], [12, 2, 13, 0], [12, 3, 13, 1], [13, 0, 13, 2], [13, 3]], "functionMap": {"names": ["<global>", "module.exports"], "mappings": "AAA;iBCE;CDU"}}, "type": "js/module"}]}