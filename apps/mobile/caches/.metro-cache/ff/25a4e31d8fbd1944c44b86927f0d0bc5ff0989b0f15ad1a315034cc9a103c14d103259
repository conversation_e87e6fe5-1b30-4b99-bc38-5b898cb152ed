{"dependencies": [{"name": "../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 47, "index": 62}}], "key": "hqwpWRawU/ruYp+nBkn/8IqEHoU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processTransformOrigin = void 0;\n  var _errors = require(_dependencyMap[0], \"../errors.js\");\n  const INDEX_X = 0;\n  const INDEX_Y = 1;\n  const INDEX_Z = 2;\n\n  // Implementation based on https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/StyleSheet/processTransformOrigin.js\n  const _worklet_7298181072720_init_data = {\n    code: \"function validateTransformOrigin_reactNativeReanimated_processTransformOriginJs1(transformOrigin){if(transformOrigin.length!==3){throw new ReanimatedError('Transform origin must have exactly 3 values.');}const[x,y,z]=transformOrigin;if(!(typeof x==='number'||typeof x==='string'&&x.endsWith('%'))){throw new ReanimatedError(\\\"Transform origin x-position must be a number or a percentage string. Passed value: \\\"+x+\\\".\\\");}if(!(typeof y==='number'||typeof y==='string'&&y.endsWith('%'))){throw new ReanimatedError(\\\"Transform origin y-position must be a number or a percentage string. Passed value: \\\"+y+\\\".\\\");}if(typeof z!=='number'){throw new ReanimatedError(\\\"Transform origin z-position must be a number. Passed value: \\\"+z+\\\".\\\");}}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/processTransformOrigin.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"validateTransformOrigin_reactNativeReanimated_processTransformOriginJs1\\\",\\\"transformOrigin\\\",\\\"length\\\",\\\"ReanimatedError\\\",\\\"x\\\",\\\"y\\\",\\\"z\\\",\\\"endsWith\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/processTransformOrigin.js\\\"],\\\"mappings\\\":\\\"AAOA,SAAAA,wEAAAC,eAAA,KAAAA,eAAA,CAAAC,MAAA,MACA,KAAS,KAAAC,eAAA,+CAAyC,EAGhD,C,KACE,CAAAC,CAAM,CAAAC,CAAA,CAAIC,CAAA,EAAAL,eAAgB,CAC5B,YAAAG,CAAA,oBAAAA,CAAA,aAAAA,CAAA,CAAAG,QAAA,QACA,KAAQ,IAAG,CAAAJ,eAAO,sFAAe,CAAAC,CAAA,MACjC,C,GACE,QAAU,CAAAC,CAAA,WAAe,SAAAA,CAAA,aAAAA,CAAA,CAAAE,QAAA,QAC3B,UAAAJ,eAAA,uFAAAE,CAAA,MACA,C,GACE,MAAM,CAAAC,CAAI,YAAe,CAC3B,UAAAH,eAAA,gEAAAG,CAAA,MACA,C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const validateTransformOrigin = function () {\n    const _e = [new global.Error(), 1, -27];\n    const validateTransformOrigin = function (transformOrigin) {\n      if (transformOrigin.length !== 3) {\n        throw new _errors.ReanimatedError('Transform origin must have exactly 3 values.');\n      }\n      const [x, y, z] = transformOrigin;\n      if (!(typeof x === 'number' || typeof x === 'string' && x.endsWith('%'))) {\n        throw new _errors.ReanimatedError(`Transform origin x-position must be a number or a percentage string. Passed value: ${x}.`);\n      }\n      if (!(typeof y === 'number' || typeof y === 'string' && y.endsWith('%'))) {\n        throw new _errors.ReanimatedError(`Transform origin y-position must be a number or a percentage string. Passed value: ${y}.`);\n      }\n      if (typeof z !== 'number') {\n        throw new _errors.ReanimatedError(`Transform origin z-position must be a number. Passed value: ${z}.`);\n      }\n    };\n    validateTransformOrigin.__closure = {};\n    validateTransformOrigin.__workletHash = 7298181072720;\n    validateTransformOrigin.__initData = _worklet_7298181072720_init_data;\n    validateTransformOrigin.__stackDetails = _e;\n    return validateTransformOrigin;\n  }();\n  const _worklet_4007711361503_init_data = {\n    code: \"function processTransformOrigin_reactNativeReanimated_processTransformOriginJs2(transformOriginIn){const{INDEX_X,INDEX_Z,INDEX_Y,__DEV__,validateTransformOrigin}=this.__closure;let transformOrigin=Array.isArray(transformOriginIn)?transformOriginIn:['50%','50%',0];if(typeof transformOriginIn==='string'){const transformOriginString=transformOriginIn;const regex=/(top|bottom|left|right|center|\\\\d+(?:%|px)|0)/gi;const transformOriginArray=['50%','50%',0];let index=INDEX_X;let matches;while(matches=regex.exec(transformOriginString)){let nextIndex=index+1;const value=matches[0];const valueLower=value.toLowerCase();switch(valueLower){case'left':case'right':{if(index!==INDEX_X){throw new ReanimatedError(\\\"Transform-origin \\\"+value+\\\" can only be used for x-position\\\");}transformOriginArray[INDEX_X]=valueLower==='left'?0:'100%';break;}case'top':case'bottom':{if(index===INDEX_Z){throw new ReanimatedError(\\\"Transform-origin \\\"+value+\\\" can only be used for y-position\\\");}transformOriginArray[INDEX_Y]=valueLower==='top'?0:'100%';if(index===INDEX_X){const horizontal=regex.exec(transformOriginString);if(horizontal==null){break;}switch(horizontal===null||horizontal===void 0?void 0:horizontal[0].toLowerCase()){case'left':transformOriginArray[INDEX_X]=0;break;case'right':transformOriginArray[INDEX_X]='100%';break;case'center':transformOriginArray[INDEX_X]='50%';break;default:throw new ReanimatedError(\\\"Could not parse transform-origin: \\\"+transformOriginString);}nextIndex=INDEX_Z;}break;}case'center':{if(index===INDEX_Z){throw new ReanimatedError(\\\"Transform-origin value \\\"+value+\\\" cannot be used for z-position\\\");}transformOriginArray[index]='50%';break;}default:{if(value.endsWith('%')){transformOriginArray[index]=value;}else{const numericValue=parseFloat(value);if(isNaN(numericValue)){throw new ReanimatedError(\\\"Invalid numeric value in transform-origin: \\\"+value);}transformOriginArray[index]=numericValue;}break;}}index=nextIndex;}transformOrigin=transformOriginArray;}if(typeof transformOriginIn!=='string'&&!Array.isArray(transformOriginIn)){throw new ReanimatedError(\\\"Invalid transformOrigin type: \\\"+typeof transformOriginIn);}if(__DEV__){validateTransformOrigin(transformOrigin);}return transformOrigin;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/processTransformOrigin.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"processTransformOrigin_reactNativeReanimated_processTransformOriginJs2\\\",\\\"transformOriginIn\\\",\\\"INDEX_X\\\",\\\"INDEX_Z\\\",\\\"INDEX_Y\\\",\\\"__DEV__\\\",\\\"validateTransformOrigin\\\",\\\"__closure\\\",\\\"transformOrigin\\\",\\\"Array\\\",\\\"isArray\\\",\\\"transformOriginString\\\",\\\"regex\\\",\\\"transformOriginArray\\\",\\\"index\\\",\\\"matches\\\",\\\"exec\\\",\\\"nextIndex\\\",\\\"value\\\",\\\"valueLower\\\",\\\"toLowerCase\\\",\\\"ReanimatedError\\\",\\\"horizontal\\\",\\\"endsWith\\\",\\\"numericValue\\\",\\\"parseFloat\\\",\\\"isNaN\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/processTransformOrigin.js\\\"],\\\"mappings\\\":\\\"AAyBO,SAAAA,sEAAmDA,CAAAC,iBAAA,QAAAC,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,uBAAA,OAAAC,SAAA,CAGxD,GAAI,CAAAC,eAAe,CAAGC,KAAK,CAACC,OAAO,CAACT,iBAAiB,CAAC,CAAGA,iBAAiB,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,CAAC,CAAC,CAC9F,GAAI,MAAO,CAAAA,iBAAiB,GAAK,QAAQ,CAAE,CACzC,KAAM,CAAAU,qBAAqB,CAAGV,iBAAiB,CAC/C,KAAM,CAAAW,KAAK,CAAG,gDAAgD,CAC9D,KAAM,CAAAC,oBAAoB,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,CAAC,CAAC,CAC9C,GAAI,CAAAC,KAAK,CAAGZ,OAAO,CACnB,GAAI,CAAAa,OAAO,CACX,MAAOA,OAAO,CAAGH,KAAK,CAACI,IAAI,CAACL,qBAAqB,CAAC,CAAE,CAClD,GAAI,CAAAM,SAAS,CAAGH,KAAK,CAAG,CAAC,CACzB,KAAM,CAAAI,KAAK,CAAGH,OAAO,CAAC,CAAC,CAAC,CACxB,KAAM,CAAAI,UAAU,CAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,CACtC,OAAQD,UAAU,EAChB,IAAK,MAAM,CACX,IAAK,OAAO,CACV,CACE,GAAIL,KAAK,GAAKZ,OAAO,CAAE,CACrB,KAAM,IAAI,CAAAmB,eAAe,qBAAqBH,KAAK,mCAAkC,CAAC,CACxF,CACAL,oBAAoB,CAACX,OAAO,CAAC,CAAGiB,UAAU,GAAK,MAAM,CAAG,CAAC,CAAG,MAAM,CAClE,MACF,CACF,IAAK,KAAK,CACV,IAAK,QAAQ,CACX,CACE,GAAIL,KAAK,GAAKX,OAAO,CAAE,CACrB,KAAM,IAAI,CAAAkB,eAAe,qBAAqBH,KAAK,mCAAkC,CAAC,CACxF,CACAL,oBAAoB,CAACT,OAAO,CAAC,CAAGe,UAAU,GAAK,KAAK,CAAG,CAAC,CAAG,MAAM,CAGjE,GAAIL,KAAK,GAAKZ,OAAO,CAAE,CACrB,KAAM,CAAAoB,UAAU,CAAGV,KAAK,CAACI,IAAI,CAACL,qBAAqB,CAAC,CACpD,GAAIW,UAAU,EAAI,IAAI,CAAE,CACtB,MACF,CACA,OAAQA,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAG,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,EACnC,IAAK,MAAM,CACTP,oBAAoB,CAACX,OAAO,CAAC,CAAG,CAAC,CACjC,MACF,IAAK,OAAO,CACVW,oBAAoB,CAACX,OAAO,CAAC,CAAG,MAAM,CACtC,MACF,IAAK,QAAQ,CACXW,oBAAoB,CAACX,OAAO,CAAC,CAAG,KAAK,CACrC,MACF,QACE,KAAM,IAAI,CAAAmB,eAAe,sCAAsCV,qBAAuB,CAAC,CAC3F,CACAM,SAAS,CAAGd,OAAO,CACrB,CACA,MACF,CACF,IAAK,QAAQ,CACX,CACE,GAAIW,KAAK,GAAKX,OAAO,CAAE,CACrB,KAAM,IAAI,CAAAkB,eAAe,2BAA2BH,KAAK,iCAAgC,CAAC,CAC5F,CACAL,oBAAoB,CAACC,KAAK,CAAC,CAAG,KAAK,CACnC,MACF,CACF,QACE,CACE,GAAII,KAAK,CAACK,QAAQ,CAAC,GAAG,CAAC,CAAE,CACvBV,oBAAoB,CAACC,KAAK,CAAC,CAAGI,KAAK,CACrC,CAAC,IAAM,CACL,KAAM,CAAAM,YAAY,CAAGC,UAAU,CAACP,KAAK,CAAC,CACtC,GAAIQ,KAAK,CAACF,YAAY,CAAC,CAAE,CACvB,KAAM,IAAI,CAAAH,eAAe,+CAA+CH,KAAO,CAAC,CAClF,CACAL,oBAAoB,CAACC,KAAK,CAAC,CAAGU,YAAY,CAC5C,CACA,MACF,CACJ,CACAV,KAAK,CAAGG,SAAS,CACnB,CACAT,eAAe,CAAGK,oBAAoB,CACxC,CACA,GAAI,MAAO,CAAAZ,iBAAiB,GAAK,QAAQ,EAAI,CAACQ,KAAK,CAACC,OAAO,CAACT,iBAAiB,CAAC,CAAE,CAC9E,KAAM,IAAI,CAAAoB,eAAe,kCAAkC,MAAO,CAAApB,iBAAmB,CAAC,CACxF,CACA,GAAII,OAAO,CAAE,CACXC,uBAAuB,CAACE,eAAe,CAAC,CAC1C,CACA,MAAO,CAAAA,eAAe,CACxB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processTransformOrigin = exports.processTransformOrigin = function () {\n    const _e = [new global.Error(), -6, -27];\n    const processTransformOrigin = function (transformOriginIn) {\n      let transformOrigin = Array.isArray(transformOriginIn) ? transformOriginIn : ['50%', '50%', 0];\n      if (typeof transformOriginIn === 'string') {\n        const transformOriginString = transformOriginIn;\n        const regex = /(top|bottom|left|right|center|\\d+(?:%|px)|0)/gi;\n        const transformOriginArray = ['50%', '50%', 0];\n        let index = INDEX_X;\n        let matches;\n        while (matches = regex.exec(transformOriginString)) {\n          let nextIndex = index + 1;\n          const value = matches[0];\n          const valueLower = value.toLowerCase();\n          switch (valueLower) {\n            case 'left':\n            case 'right':\n              {\n                if (index !== INDEX_X) {\n                  throw new _errors.ReanimatedError(`Transform-origin ${value} can only be used for x-position`);\n                }\n                transformOriginArray[INDEX_X] = valueLower === 'left' ? 0 : '100%';\n                break;\n              }\n            case 'top':\n            case 'bottom':\n              {\n                if (index === INDEX_Z) {\n                  throw new _errors.ReanimatedError(`Transform-origin ${value} can only be used for y-position`);\n                }\n                transformOriginArray[INDEX_Y] = valueLower === 'top' ? 0 : '100%';\n\n                // Handle [[ center | left | right ] && [ center | top | bottom ]] <length>?\n                if (index === INDEX_X) {\n                  const horizontal = regex.exec(transformOriginString);\n                  if (horizontal == null) {\n                    break;\n                  }\n                  switch (horizontal?.[0].toLowerCase()) {\n                    case 'left':\n                      transformOriginArray[INDEX_X] = 0;\n                      break;\n                    case 'right':\n                      transformOriginArray[INDEX_X] = '100%';\n                      break;\n                    case 'center':\n                      transformOriginArray[INDEX_X] = '50%';\n                      break;\n                    default:\n                      throw new _errors.ReanimatedError(`Could not parse transform-origin: ${transformOriginString}`);\n                  }\n                  nextIndex = INDEX_Z;\n                }\n                break;\n              }\n            case 'center':\n              {\n                if (index === INDEX_Z) {\n                  throw new _errors.ReanimatedError(`Transform-origin value ${value} cannot be used for z-position`);\n                }\n                transformOriginArray[index] = '50%';\n                break;\n              }\n            default:\n              {\n                if (value.endsWith('%')) {\n                  transformOriginArray[index] = value;\n                } else {\n                  const numericValue = parseFloat(value);\n                  if (isNaN(numericValue)) {\n                    throw new _errors.ReanimatedError(`Invalid numeric value in transform-origin: ${value}`);\n                  }\n                  transformOriginArray[index] = numericValue;\n                }\n                break;\n              }\n          }\n          index = nextIndex;\n        }\n        transformOrigin = transformOriginArray;\n      }\n      if (typeof transformOriginIn !== 'string' && !Array.isArray(transformOriginIn)) {\n        throw new _errors.ReanimatedError(`Invalid transformOrigin type: ${typeof transformOriginIn}`);\n      }\n      if (__DEV__) {\n        validateTransformOrigin(transformOrigin);\n      }\n      return transformOrigin;\n    };\n    processTransformOrigin.__closure = {\n      INDEX_X,\n      INDEX_Z,\n      INDEX_Y,\n      __DEV__,\n      validateTransformOrigin\n    };\n    processTransformOrigin.__workletHash = 4007711361503;\n    processTransformOrigin.__initData = _worklet_4007711361503_init_data;\n    processTransformOrigin.__stackDetails = _e;\n    return processTransformOrigin;\n  }();\n});", "lineCount": 150, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "processTransformOrigin"], [7, 32, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_errors"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "INDEX_X"], [9, 15, 4, 13], [9, 18, 4, 16], [9, 19, 4, 17], [10, 2, 5, 0], [10, 8, 5, 6, "INDEX_Y"], [10, 15, 5, 13], [10, 18, 5, 16], [10, 19, 5, 17], [11, 2, 6, 0], [11, 8, 6, 6, "INDEX_Z"], [11, 15, 6, 13], [11, 18, 6, 16], [11, 19, 6, 17], [13, 2, 8, 0], [14, 2, 8, 0], [14, 8, 8, 0, "_worklet_7298181072720_init_data"], [14, 40, 8, 0], [15, 4, 8, 0, "code"], [15, 8, 8, 0], [16, 4, 8, 0, "location"], [16, 12, 8, 0], [17, 4, 8, 0, "sourceMap"], [17, 13, 8, 0], [18, 4, 8, 0, "version"], [18, 11, 8, 0], [19, 2, 8, 0], [20, 2, 8, 0], [20, 8, 8, 0, "validateTransformOrigin"], [20, 31, 8, 0], [20, 34, 9, 0], [21, 4, 9, 0], [21, 10, 9, 0, "_e"], [21, 12, 9, 0], [21, 20, 9, 0, "global"], [21, 26, 9, 0], [21, 27, 9, 0, "Error"], [21, 32, 9, 0], [22, 4, 9, 0], [22, 10, 9, 0, "validateTransformOrigin"], [22, 33, 9, 0], [22, 45, 9, 0, "validateTransformOrigin"], [22, 46, 9, 33, "transform<PERSON><PERSON>in"], [22, 61, 9, 48], [22, 63, 9, 50], [23, 6, 12, 2], [23, 10, 12, 6, "transform<PERSON><PERSON>in"], [23, 25, 12, 21], [23, 26, 12, 22, "length"], [23, 32, 12, 28], [23, 37, 12, 33], [23, 38, 12, 34], [23, 40, 12, 36], [24, 8, 13, 4], [24, 14, 13, 10], [24, 18, 13, 14, "ReanimatedError"], [24, 41, 13, 29], [24, 42, 13, 30], [24, 88, 13, 76], [24, 89, 13, 77], [25, 6, 14, 2], [26, 6, 15, 2], [26, 12, 15, 8], [26, 13, 15, 9, "x"], [26, 14, 15, 10], [26, 16, 15, 12, "y"], [26, 17, 15, 13], [26, 19, 15, 15, "z"], [26, 20, 15, 16], [26, 21, 15, 17], [26, 24, 15, 20, "transform<PERSON><PERSON>in"], [26, 39, 15, 35], [27, 6, 16, 2], [27, 10, 16, 6], [27, 12, 16, 8], [27, 19, 16, 15, "x"], [27, 20, 16, 16], [27, 25, 16, 21], [27, 33, 16, 29], [27, 37, 16, 33], [27, 44, 16, 40, "x"], [27, 45, 16, 41], [27, 50, 16, 46], [27, 58, 16, 54], [27, 62, 16, 58, "x"], [27, 63, 16, 59], [27, 64, 16, 60, "endsWith"], [27, 72, 16, 68], [27, 73, 16, 69], [27, 76, 16, 72], [27, 77, 16, 73], [27, 78, 16, 74], [27, 80, 16, 76], [28, 8, 17, 4], [28, 14, 17, 10], [28, 18, 17, 14, "ReanimatedError"], [28, 41, 17, 29], [28, 42, 17, 30], [28, 128, 17, 116, "x"], [28, 129, 17, 117], [28, 132, 17, 120], [28, 133, 17, 121], [29, 6, 18, 2], [30, 6, 19, 2], [30, 10, 19, 6], [30, 12, 19, 8], [30, 19, 19, 15, "y"], [30, 20, 19, 16], [30, 25, 19, 21], [30, 33, 19, 29], [30, 37, 19, 33], [30, 44, 19, 40, "y"], [30, 45, 19, 41], [30, 50, 19, 46], [30, 58, 19, 54], [30, 62, 19, 58, "y"], [30, 63, 19, 59], [30, 64, 19, 60, "endsWith"], [30, 72, 19, 68], [30, 73, 19, 69], [30, 76, 19, 72], [30, 77, 19, 73], [30, 78, 19, 74], [30, 80, 19, 76], [31, 8, 20, 4], [31, 14, 20, 10], [31, 18, 20, 14, "ReanimatedError"], [31, 41, 20, 29], [31, 42, 20, 30], [31, 128, 20, 116, "y"], [31, 129, 20, 117], [31, 132, 20, 120], [31, 133, 20, 121], [32, 6, 21, 2], [33, 6, 22, 2], [33, 10, 22, 6], [33, 17, 22, 13, "z"], [33, 18, 22, 14], [33, 23, 22, 19], [33, 31, 22, 27], [33, 33, 22, 29], [34, 8, 23, 4], [34, 14, 23, 10], [34, 18, 23, 14, "ReanimatedError"], [34, 41, 23, 29], [34, 42, 23, 30], [34, 105, 23, 93, "z"], [34, 106, 23, 94], [34, 109, 23, 97], [34, 110, 23, 98], [35, 6, 24, 2], [36, 4, 25, 0], [36, 5, 25, 1], [37, 4, 25, 1, "validateTransformOrigin"], [37, 27, 25, 1], [37, 28, 25, 1, "__closure"], [37, 37, 25, 1], [38, 4, 25, 1, "validateTransformOrigin"], [38, 27, 25, 1], [38, 28, 25, 1, "__workletHash"], [38, 41, 25, 1], [39, 4, 25, 1, "validateTransformOrigin"], [39, 27, 25, 1], [39, 28, 25, 1, "__initData"], [39, 38, 25, 1], [39, 41, 25, 1, "_worklet_7298181072720_init_data"], [39, 73, 25, 1], [40, 4, 25, 1, "validateTransformOrigin"], [40, 27, 25, 1], [40, 28, 25, 1, "__stackDetails"], [40, 42, 25, 1], [40, 45, 25, 1, "_e"], [40, 47, 25, 1], [41, 4, 25, 1], [41, 11, 25, 1, "validateTransformOrigin"], [41, 34, 25, 1], [42, 2, 25, 1], [42, 3, 9, 0], [43, 2, 9, 0], [43, 8, 9, 0, "_worklet_4007711361503_init_data"], [43, 40, 9, 0], [44, 4, 9, 0, "code"], [44, 8, 9, 0], [45, 4, 9, 0, "location"], [45, 12, 9, 0], [46, 4, 9, 0, "sourceMap"], [46, 13, 9, 0], [47, 4, 9, 0, "version"], [47, 11, 9, 0], [48, 2, 9, 0], [49, 2, 9, 0], [49, 8, 9, 0, "processTransformOrigin"], [49, 30, 9, 0], [49, 33, 9, 0, "exports"], [49, 40, 9, 0], [49, 41, 9, 0, "processTransformOrigin"], [49, 63, 9, 0], [49, 66, 26, 7], [50, 4, 26, 7], [50, 10, 26, 7, "_e"], [50, 12, 26, 7], [50, 20, 26, 7, "global"], [50, 26, 26, 7], [50, 27, 26, 7, "Error"], [50, 32, 26, 7], [51, 4, 26, 7], [51, 10, 26, 7, "processTransformOrigin"], [51, 32, 26, 7], [51, 44, 26, 7, "processTransformOrigin"], [51, 45, 26, 39, "transformOriginIn"], [51, 62, 26, 56], [51, 64, 26, 58], [52, 6, 29, 2], [52, 10, 29, 6, "transform<PERSON><PERSON>in"], [52, 25, 29, 21], [52, 28, 29, 24, "Array"], [52, 33, 29, 29], [52, 34, 29, 30, "isArray"], [52, 41, 29, 37], [52, 42, 29, 38, "transformOriginIn"], [52, 59, 29, 55], [52, 60, 29, 56], [52, 63, 29, 59, "transformOriginIn"], [52, 80, 29, 76], [52, 83, 29, 79], [52, 84, 29, 80], [52, 89, 29, 85], [52, 91, 29, 87], [52, 96, 29, 92], [52, 98, 29, 94], [52, 99, 29, 95], [52, 100, 29, 96], [53, 6, 30, 2], [53, 10, 30, 6], [53, 17, 30, 13, "transformOriginIn"], [53, 34, 30, 30], [53, 39, 30, 35], [53, 47, 30, 43], [53, 49, 30, 45], [54, 8, 31, 4], [54, 14, 31, 10, "transformOriginString"], [54, 35, 31, 31], [54, 38, 31, 34, "transformOriginIn"], [54, 55, 31, 51], [55, 8, 32, 4], [55, 14, 32, 10, "regex"], [55, 19, 32, 15], [55, 22, 32, 18], [55, 70, 32, 66], [56, 8, 33, 4], [56, 14, 33, 10, "transformOriginArray"], [56, 34, 33, 30], [56, 37, 33, 33], [56, 38, 33, 34], [56, 43, 33, 39], [56, 45, 33, 41], [56, 50, 33, 46], [56, 52, 33, 48], [56, 53, 33, 49], [56, 54, 33, 50], [57, 8, 34, 4], [57, 12, 34, 8, "index"], [57, 17, 34, 13], [57, 20, 34, 16, "INDEX_X"], [57, 27, 34, 23], [58, 8, 35, 4], [58, 12, 35, 8, "matches"], [58, 19, 35, 15], [59, 8, 36, 4], [59, 15, 36, 11, "matches"], [59, 22, 36, 18], [59, 25, 36, 21, "regex"], [59, 30, 36, 26], [59, 31, 36, 27, "exec"], [59, 35, 36, 31], [59, 36, 36, 32, "transformOriginString"], [59, 57, 36, 53], [59, 58, 36, 54], [59, 60, 36, 56], [60, 10, 37, 6], [60, 14, 37, 10, "nextIndex"], [60, 23, 37, 19], [60, 26, 37, 22, "index"], [60, 31, 37, 27], [60, 34, 37, 30], [60, 35, 37, 31], [61, 10, 38, 6], [61, 16, 38, 12, "value"], [61, 21, 38, 17], [61, 24, 38, 20, "matches"], [61, 31, 38, 27], [61, 32, 38, 28], [61, 33, 38, 29], [61, 34, 38, 30], [62, 10, 39, 6], [62, 16, 39, 12, "valueLower"], [62, 26, 39, 22], [62, 29, 39, 25, "value"], [62, 34, 39, 30], [62, 35, 39, 31, "toLowerCase"], [62, 46, 39, 42], [62, 47, 39, 43], [62, 48, 39, 44], [63, 10, 40, 6], [63, 18, 40, 14, "valueLower"], [63, 28, 40, 24], [64, 12, 41, 8], [64, 17, 41, 13], [64, 23, 41, 19], [65, 12, 42, 8], [65, 17, 42, 13], [65, 24, 42, 20], [66, 14, 43, 10], [67, 16, 44, 12], [67, 20, 44, 16, "index"], [67, 25, 44, 21], [67, 30, 44, 26, "INDEX_X"], [67, 37, 44, 33], [67, 39, 44, 35], [68, 18, 45, 14], [68, 24, 45, 20], [68, 28, 45, 24, "ReanimatedError"], [68, 51, 45, 39], [68, 52, 45, 40], [68, 72, 45, 60, "value"], [68, 77, 45, 65], [68, 111, 45, 99], [68, 112, 45, 100], [69, 16, 46, 12], [70, 16, 47, 12, "transformOriginArray"], [70, 36, 47, 32], [70, 37, 47, 33, "INDEX_X"], [70, 44, 47, 40], [70, 45, 47, 41], [70, 48, 47, 44, "valueLower"], [70, 58, 47, 54], [70, 63, 47, 59], [70, 69, 47, 65], [70, 72, 47, 68], [70, 73, 47, 69], [70, 76, 47, 72], [70, 82, 47, 78], [71, 16, 48, 12], [72, 14, 49, 10], [73, 12, 50, 8], [73, 17, 50, 13], [73, 22, 50, 18], [74, 12, 51, 8], [74, 17, 51, 13], [74, 25, 51, 21], [75, 14, 52, 10], [76, 16, 53, 12], [76, 20, 53, 16, "index"], [76, 25, 53, 21], [76, 30, 53, 26, "INDEX_Z"], [76, 37, 53, 33], [76, 39, 53, 35], [77, 18, 54, 14], [77, 24, 54, 20], [77, 28, 54, 24, "ReanimatedError"], [77, 51, 54, 39], [77, 52, 54, 40], [77, 72, 54, 60, "value"], [77, 77, 54, 65], [77, 111, 54, 99], [77, 112, 54, 100], [78, 16, 55, 12], [79, 16, 56, 12, "transformOriginArray"], [79, 36, 56, 32], [79, 37, 56, 33, "INDEX_Y"], [79, 44, 56, 40], [79, 45, 56, 41], [79, 48, 56, 44, "valueLower"], [79, 58, 56, 54], [79, 63, 56, 59], [79, 68, 56, 64], [79, 71, 56, 67], [79, 72, 56, 68], [79, 75, 56, 71], [79, 81, 56, 77], [81, 16, 58, 12], [82, 16, 59, 12], [82, 20, 59, 16, "index"], [82, 25, 59, 21], [82, 30, 59, 26, "INDEX_X"], [82, 37, 59, 33], [82, 39, 59, 35], [83, 18, 60, 14], [83, 24, 60, 20, "horizontal"], [83, 34, 60, 30], [83, 37, 60, 33, "regex"], [83, 42, 60, 38], [83, 43, 60, 39, "exec"], [83, 47, 60, 43], [83, 48, 60, 44, "transformOriginString"], [83, 69, 60, 65], [83, 70, 60, 66], [84, 18, 61, 14], [84, 22, 61, 18, "horizontal"], [84, 32, 61, 28], [84, 36, 61, 32], [84, 40, 61, 36], [84, 42, 61, 38], [85, 20, 62, 16], [86, 18, 63, 14], [87, 18, 64, 14], [87, 26, 64, 22, "horizontal"], [87, 36, 64, 32], [87, 39, 64, 35], [87, 40, 64, 36], [87, 41, 64, 37], [87, 42, 64, 38, "toLowerCase"], [87, 53, 64, 49], [87, 54, 64, 50], [87, 55, 64, 51], [88, 20, 65, 16], [88, 25, 65, 21], [88, 31, 65, 27], [89, 22, 66, 18, "transformOriginArray"], [89, 42, 66, 38], [89, 43, 66, 39, "INDEX_X"], [89, 50, 66, 46], [89, 51, 66, 47], [89, 54, 66, 50], [89, 55, 66, 51], [90, 22, 67, 18], [91, 20, 68, 16], [91, 25, 68, 21], [91, 32, 68, 28], [92, 22, 69, 18, "transformOriginArray"], [92, 42, 69, 38], [92, 43, 69, 39, "INDEX_X"], [92, 50, 69, 46], [92, 51, 69, 47], [92, 54, 69, 50], [92, 60, 69, 56], [93, 22, 70, 18], [94, 20, 71, 16], [94, 25, 71, 21], [94, 33, 71, 29], [95, 22, 72, 18, "transformOriginArray"], [95, 42, 72, 38], [95, 43, 72, 39, "INDEX_X"], [95, 50, 72, 46], [95, 51, 72, 47], [95, 54, 72, 50], [95, 59, 72, 55], [96, 22, 73, 18], [97, 20, 74, 16], [98, 22, 75, 18], [98, 28, 75, 24], [98, 32, 75, 28, "ReanimatedError"], [98, 55, 75, 43], [98, 56, 75, 44], [98, 93, 75, 81, "transformOriginString"], [98, 114, 75, 102], [98, 116, 75, 104], [98, 117, 75, 105], [99, 18, 76, 14], [100, 18, 77, 14, "nextIndex"], [100, 27, 77, 23], [100, 30, 77, 26, "INDEX_Z"], [100, 37, 77, 33], [101, 16, 78, 12], [102, 16, 79, 12], [103, 14, 80, 10], [104, 12, 81, 8], [104, 17, 81, 13], [104, 25, 81, 21], [105, 14, 82, 10], [106, 16, 83, 12], [106, 20, 83, 16, "index"], [106, 25, 83, 21], [106, 30, 83, 26, "INDEX_Z"], [106, 37, 83, 33], [106, 39, 83, 35], [107, 18, 84, 14], [107, 24, 84, 20], [107, 28, 84, 24, "ReanimatedError"], [107, 51, 84, 39], [107, 52, 84, 40], [107, 78, 84, 66, "value"], [107, 83, 84, 71], [107, 115, 84, 103], [107, 116, 84, 104], [108, 16, 85, 12], [109, 16, 86, 12, "transformOriginArray"], [109, 36, 86, 32], [109, 37, 86, 33, "index"], [109, 42, 86, 38], [109, 43, 86, 39], [109, 46, 86, 42], [109, 51, 86, 47], [110, 16, 87, 12], [111, 14, 88, 10], [112, 12, 89, 8], [113, 14, 90, 10], [114, 16, 91, 12], [114, 20, 91, 16, "value"], [114, 25, 91, 21], [114, 26, 91, 22, "endsWith"], [114, 34, 91, 30], [114, 35, 91, 31], [114, 38, 91, 34], [114, 39, 91, 35], [114, 41, 91, 37], [115, 18, 92, 14, "transformOriginArray"], [115, 38, 92, 34], [115, 39, 92, 35, "index"], [115, 44, 92, 40], [115, 45, 92, 41], [115, 48, 92, 44, "value"], [115, 53, 92, 49], [116, 16, 93, 12], [116, 17, 93, 13], [116, 23, 93, 19], [117, 18, 94, 14], [117, 24, 94, 20, "numericValue"], [117, 36, 94, 32], [117, 39, 94, 35, "parseFloat"], [117, 49, 94, 45], [117, 50, 94, 46, "value"], [117, 55, 94, 51], [117, 56, 94, 52], [118, 18, 95, 14], [118, 22, 95, 18, "isNaN"], [118, 27, 95, 23], [118, 28, 95, 24, "numericValue"], [118, 40, 95, 36], [118, 41, 95, 37], [118, 43, 95, 39], [119, 20, 96, 16], [119, 26, 96, 22], [119, 30, 96, 26, "ReanimatedError"], [119, 53, 96, 41], [119, 54, 96, 42], [119, 100, 96, 88, "value"], [119, 105, 96, 93], [119, 107, 96, 95], [119, 108, 96, 96], [120, 18, 97, 14], [121, 18, 98, 14, "transformOriginArray"], [121, 38, 98, 34], [121, 39, 98, 35, "index"], [121, 44, 98, 40], [121, 45, 98, 41], [121, 48, 98, 44, "numericValue"], [121, 60, 98, 56], [122, 16, 99, 12], [123, 16, 100, 12], [124, 14, 101, 10], [125, 10, 102, 6], [126, 10, 103, 6, "index"], [126, 15, 103, 11], [126, 18, 103, 14, "nextIndex"], [126, 27, 103, 23], [127, 8, 104, 4], [128, 8, 105, 4, "transform<PERSON><PERSON>in"], [128, 23, 105, 19], [128, 26, 105, 22, "transformOriginArray"], [128, 46, 105, 42], [129, 6, 106, 2], [130, 6, 107, 2], [130, 10, 107, 6], [130, 17, 107, 13, "transformOriginIn"], [130, 34, 107, 30], [130, 39, 107, 35], [130, 47, 107, 43], [130, 51, 107, 47], [130, 52, 107, 48, "Array"], [130, 57, 107, 53], [130, 58, 107, 54, "isArray"], [130, 65, 107, 61], [130, 66, 107, 62, "transformOriginIn"], [130, 83, 107, 79], [130, 84, 107, 80], [130, 86, 107, 82], [131, 8, 108, 4], [131, 14, 108, 10], [131, 18, 108, 14, "ReanimatedError"], [131, 41, 108, 29], [131, 42, 108, 30], [131, 75, 108, 63], [131, 82, 108, 70, "transformOriginIn"], [131, 99, 108, 87], [131, 101, 108, 89], [131, 102, 108, 90], [132, 6, 109, 2], [133, 6, 110, 2], [133, 10, 110, 6, "__DEV__"], [133, 17, 110, 13], [133, 19, 110, 15], [134, 8, 111, 4, "validateTransformOrigin"], [134, 31, 111, 27], [134, 32, 111, 28, "transform<PERSON><PERSON>in"], [134, 47, 111, 43], [134, 48, 111, 44], [135, 6, 112, 2], [136, 6, 113, 2], [136, 13, 113, 9, "transform<PERSON><PERSON>in"], [136, 28, 113, 24], [137, 4, 114, 0], [137, 5, 114, 1], [138, 4, 114, 1, "processTransformOrigin"], [138, 26, 114, 1], [138, 27, 114, 1, "__closure"], [138, 36, 114, 1], [139, 6, 114, 1, "INDEX_X"], [139, 13, 114, 1], [140, 6, 114, 1, "INDEX_Z"], [140, 13, 114, 1], [141, 6, 114, 1, "INDEX_Y"], [141, 13, 114, 1], [142, 6, 114, 1, "__DEV__"], [142, 13, 114, 1], [143, 6, 114, 1, "validateTransformOrigin"], [144, 4, 114, 1], [145, 4, 114, 1, "processTransformOrigin"], [145, 26, 114, 1], [145, 27, 114, 1, "__workletHash"], [145, 40, 114, 1], [146, 4, 114, 1, "processTransformOrigin"], [146, 26, 114, 1], [146, 27, 114, 1, "__initData"], [146, 37, 114, 1], [146, 40, 114, 1, "_worklet_4007711361503_init_data"], [146, 72, 114, 1], [147, 4, 114, 1, "processTransformOrigin"], [147, 26, 114, 1], [147, 27, 114, 1, "__stackDetails"], [147, 41, 114, 1], [147, 44, 114, 1, "_e"], [147, 46, 114, 1], [148, 4, 114, 1], [148, 11, 114, 1, "processTransformOrigin"], [148, 33, 114, 1], [149, 2, 114, 1], [149, 3, 26, 7], [150, 0, 26, 7], [150, 3]], "functionMap": {"names": ["<global>", "validateTransformOrigin", "processTransformOrigin"], "mappings": "AAA;ACQ;CDgB;OEC;CFwF"}}, "type": "js/module"}]}