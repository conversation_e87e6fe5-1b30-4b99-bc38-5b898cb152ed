{"dependencies": [{"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 37, "index": 37}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "expo-secure-store", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 38}, "end": {"line": 2, "column": 49, "index": 87}}], "key": "BU2XtfznZ4PiVldqd/oueHCCaLo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 88}, "end": {"line": 3, "column": 56, "index": 144}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "zustand", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 145}, "end": {"line": 4, "column": 33, "index": 178}}], "key": "POPvx7yS3Y3wz+S/9OAefiXNs0Q=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 179}, "end": {"line": 5, "column": 43, "index": 222}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./store", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 223}, "end": {"line": 6, "column": 62, "index": 285}}], "key": "5APpJpQn7z9vMHIr4H9Bv6aaMsY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useRequireAuth = exports.useAuth = exports.default = void 0;\n  var _expoRouter = require(_dependencyMap[0], \"expo-router\");\n  var SecureStore = _interopRequireWildcard(require(_dependencyMap[1], \"expo-secure-store\"));\n  var _react = require(_dependencyMap[2], \"react\");\n  var _zustand = require(_dependencyMap[3], \"zustand\");\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _store = require(_dependencyMap[5], \"./store\");\n  var _s = $RefreshSig$(),\n    _s2 = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * This hook provides authentication functionality.\n   * It may be easier to use the `useAuthModal` or `useRequireAuth` hooks\n   * instead as those will also handle showing authentication to the user\n   * directly.\n   */\n  var useAuth = () => {\n    _s();\n    var _useAuthStore = (0, _store.useAuthStore)(),\n      isReady = _useAuthStore.isReady,\n      auth = _useAuthStore.auth,\n      setAuth = _useAuthStore.setAuth;\n    var _useAuthModal = (0, _store.useAuthModal)(),\n      isOpen = _useAuthModal.isOpen,\n      close = _useAuthModal.close,\n      open = _useAuthModal.open;\n    var initiate = (0, _react.useCallback)(() => {\n      SecureStore.getItemAsync(_store.authKey).then(auth => {\n        _store.useAuthStore.setState({\n          auth: auth ? JSON.parse(auth) : null,\n          isReady: true\n        });\n      });\n    }, []);\n    (0, _react.useEffect)(() => {}, []);\n    var signIn = (0, _react.useCallback)(() => {\n      open({\n        mode: 'signin'\n      });\n    }, [open]);\n    var signUp = (0, _react.useCallback)(() => {\n      open({\n        mode: 'signup'\n      });\n    }, [open]);\n    var signOut = (0, _react.useCallback)(() => {\n      setAuth(null);\n      close();\n    }, [close]);\n    return {\n      isReady,\n      isAuthenticated: isReady ? !!auth : null,\n      signIn,\n      signOut,\n      signUp,\n      auth,\n      setAuth,\n      initiate\n    };\n  };\n\n  /**\n   * This hook will automatically open the authentication modal if the user is not authenticated.\n   */\n  exports.useAuth = useAuth;\n  _s(useAuth, \"i35Wyh9BbTF734PTV6WNDYAMJ70=\", false, function () {\n    return [_store.useAuthStore, _store.useAuthModal];\n  });\n  var useRequireAuth = options => {\n    _s2();\n    var _useAuth = useAuth(),\n      isAuthenticated = _useAuth.isAuthenticated,\n      isReady = _useAuth.isReady;\n    var _useAuthModal2 = (0, _store.useAuthModal)(),\n      open = _useAuthModal2.open;\n    (0, _react.useEffect)(() => {\n      if (!isAuthenticated && isReady) {\n        open({\n          mode: options?.mode\n        });\n      }\n    }, [isAuthenticated, open, options?.mode, isReady]);\n  };\n  exports.useRequireAuth = useRequireAuth;\n  _s2(useRequireAuth, \"Ut2Gv5Cru0XgoBi/l6NJFDPOPtQ=\", false, function () {\n    return [useAuth, _store.useAuthModal];\n  });\n  var _default = exports.default = useAuth;\n});", "lineCount": 93, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoRouter"], [6, 17, 1, 0], [6, 20, 1, 0, "require"], [6, 27, 1, 0], [6, 28, 1, 0, "_dependencyMap"], [6, 42, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "SecureStore"], [7, 17, 2, 0], [7, 20, 2, 0, "_interopRequireWildcard"], [7, 43, 2, 0], [7, 44, 2, 0, "require"], [7, 51, 2, 0], [7, 52, 2, 0, "_dependencyMap"], [7, 66, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_zustand"], [9, 14, 4, 0], [9, 17, 4, 0, "require"], [9, 24, 4, 0], [9, 25, 4, 0, "_dependencyMap"], [9, 39, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_reactNative"], [10, 18, 5, 0], [10, 21, 5, 0, "require"], [10, 28, 5, 0], [10, 29, 5, 0, "_dependencyMap"], [10, 43, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_store"], [11, 12, 6, 0], [11, 15, 6, 0, "require"], [11, 22, 6, 0], [11, 23, 6, 0, "_dependencyMap"], [11, 37, 6, 0], [12, 2, 6, 62], [12, 6, 6, 62, "_s"], [12, 8, 6, 62], [12, 11, 6, 62, "$RefreshSig$"], [12, 23, 6, 62], [13, 4, 6, 62, "_s2"], [13, 7, 6, 62], [13, 10, 6, 62, "$RefreshSig$"], [13, 22, 6, 62], [14, 2, 6, 62], [14, 11, 6, 62, "_interopRequireWildcard"], [14, 35, 6, 62, "e"], [14, 36, 6, 62], [14, 38, 6, 62, "t"], [14, 39, 6, 62], [14, 68, 6, 62, "WeakMap"], [14, 75, 6, 62], [14, 81, 6, 62, "r"], [14, 82, 6, 62], [14, 89, 6, 62, "WeakMap"], [14, 96, 6, 62], [14, 100, 6, 62, "n"], [14, 101, 6, 62], [14, 108, 6, 62, "WeakMap"], [14, 115, 6, 62], [14, 127, 6, 62, "_interopRequireWildcard"], [14, 150, 6, 62], [14, 162, 6, 62, "_interopRequireWildcard"], [14, 163, 6, 62, "e"], [14, 164, 6, 62], [14, 166, 6, 62, "t"], [14, 167, 6, 62], [14, 176, 6, 62, "t"], [14, 177, 6, 62], [14, 181, 6, 62, "e"], [14, 182, 6, 62], [14, 186, 6, 62, "e"], [14, 187, 6, 62], [14, 188, 6, 62, "__esModule"], [14, 198, 6, 62], [14, 207, 6, 62, "e"], [14, 208, 6, 62], [14, 214, 6, 62, "o"], [14, 215, 6, 62], [14, 217, 6, 62, "i"], [14, 218, 6, 62], [14, 220, 6, 62, "f"], [14, 221, 6, 62], [14, 226, 6, 62, "__proto__"], [14, 235, 6, 62], [14, 243, 6, 62, "default"], [14, 250, 6, 62], [14, 252, 6, 62, "e"], [14, 253, 6, 62], [14, 270, 6, 62, "e"], [14, 271, 6, 62], [14, 294, 6, 62, "e"], [14, 295, 6, 62], [14, 320, 6, 62, "e"], [14, 321, 6, 62], [14, 330, 6, 62, "f"], [14, 331, 6, 62], [14, 337, 6, 62, "o"], [14, 338, 6, 62], [14, 341, 6, 62, "t"], [14, 342, 6, 62], [14, 345, 6, 62, "n"], [14, 346, 6, 62], [14, 349, 6, 62, "r"], [14, 350, 6, 62], [14, 358, 6, 62, "o"], [14, 359, 6, 62], [14, 360, 6, 62, "has"], [14, 363, 6, 62], [14, 364, 6, 62, "e"], [14, 365, 6, 62], [14, 375, 6, 62, "o"], [14, 376, 6, 62], [14, 377, 6, 62, "get"], [14, 380, 6, 62], [14, 381, 6, 62, "e"], [14, 382, 6, 62], [14, 385, 6, 62, "o"], [14, 386, 6, 62], [14, 387, 6, 62, "set"], [14, 390, 6, 62], [14, 391, 6, 62, "e"], [14, 392, 6, 62], [14, 394, 6, 62, "f"], [14, 395, 6, 62], [14, 409, 6, 62, "_t"], [14, 411, 6, 62], [14, 415, 6, 62, "e"], [14, 416, 6, 62], [14, 432, 6, 62, "_t"], [14, 434, 6, 62], [14, 441, 6, 62, "hasOwnProperty"], [14, 455, 6, 62], [14, 456, 6, 62, "call"], [14, 460, 6, 62], [14, 461, 6, 62, "e"], [14, 462, 6, 62], [14, 464, 6, 62, "_t"], [14, 466, 6, 62], [14, 473, 6, 62, "i"], [14, 474, 6, 62], [14, 478, 6, 62, "o"], [14, 479, 6, 62], [14, 482, 6, 62, "Object"], [14, 488, 6, 62], [14, 489, 6, 62, "defineProperty"], [14, 503, 6, 62], [14, 508, 6, 62, "Object"], [14, 514, 6, 62], [14, 515, 6, 62, "getOwnPropertyDescriptor"], [14, 539, 6, 62], [14, 540, 6, 62, "e"], [14, 541, 6, 62], [14, 543, 6, 62, "_t"], [14, 545, 6, 62], [14, 552, 6, 62, "i"], [14, 553, 6, 62], [14, 554, 6, 62, "get"], [14, 557, 6, 62], [14, 561, 6, 62, "i"], [14, 562, 6, 62], [14, 563, 6, 62, "set"], [14, 566, 6, 62], [14, 570, 6, 62, "o"], [14, 571, 6, 62], [14, 572, 6, 62, "f"], [14, 573, 6, 62], [14, 575, 6, 62, "_t"], [14, 577, 6, 62], [14, 579, 6, 62, "i"], [14, 580, 6, 62], [14, 584, 6, 62, "f"], [14, 585, 6, 62], [14, 586, 6, 62, "_t"], [14, 588, 6, 62], [14, 592, 6, 62, "e"], [14, 593, 6, 62], [14, 594, 6, 62, "_t"], [14, 596, 6, 62], [14, 607, 6, 62, "f"], [14, 608, 6, 62], [14, 613, 6, 62, "e"], [14, 614, 6, 62], [14, 616, 6, 62, "t"], [14, 617, 6, 62], [15, 2, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 0, 14, 0], [21, 2, 15, 7], [21, 6, 15, 13, "useAuth"], [21, 13, 15, 20], [21, 16, 15, 23, "useAuth"], [21, 17, 15, 23], [21, 22, 15, 29], [22, 4, 15, 29, "_s"], [22, 6, 15, 29], [23, 4, 16, 2], [23, 8, 16, 2, "_useAuthStore"], [23, 21, 16, 2], [23, 24, 16, 37], [23, 28, 16, 37, "useAuthStore"], [23, 47, 16, 49], [23, 49, 16, 50], [23, 50, 16, 51], [24, 6, 16, 10, "isReady"], [24, 13, 16, 17], [24, 16, 16, 17, "_useAuthStore"], [24, 29, 16, 17], [24, 30, 16, 10, "isReady"], [24, 37, 16, 17], [25, 6, 16, 19, "auth"], [25, 10, 16, 23], [25, 13, 16, 23, "_useAuthStore"], [25, 26, 16, 23], [25, 27, 16, 19, "auth"], [25, 31, 16, 23], [26, 6, 16, 25, "setAuth"], [26, 13, 16, 32], [26, 16, 16, 32, "_useAuthStore"], [26, 29, 16, 32], [26, 30, 16, 25, "setAuth"], [26, 37, 16, 32], [27, 4, 17, 2], [27, 8, 17, 2, "_useAuthModal"], [27, 21, 17, 2], [27, 24, 17, 34], [27, 28, 17, 34, "useAuthModal"], [27, 47, 17, 46], [27, 49, 17, 47], [27, 50, 17, 48], [28, 6, 17, 10, "isOpen"], [28, 12, 17, 16], [28, 15, 17, 16, "_useAuthModal"], [28, 28, 17, 16], [28, 29, 17, 10, "isOpen"], [28, 35, 17, 16], [29, 6, 17, 18, "close"], [29, 11, 17, 23], [29, 14, 17, 23, "_useAuthModal"], [29, 27, 17, 23], [29, 28, 17, 18, "close"], [29, 33, 17, 23], [30, 6, 17, 25, "open"], [30, 10, 17, 29], [30, 13, 17, 29, "_useAuthModal"], [30, 26, 17, 29], [30, 27, 17, 25, "open"], [30, 31, 17, 29], [31, 4, 19, 2], [31, 8, 19, 8, "initiate"], [31, 16, 19, 16], [31, 19, 19, 19], [31, 23, 19, 19, "useCallback"], [31, 41, 19, 30], [31, 43, 19, 31], [31, 49, 19, 37], [32, 6, 20, 4, "SecureStore"], [32, 17, 20, 15], [32, 18, 20, 16, "getItemAsync"], [32, 30, 20, 28], [32, 31, 20, 29, "auth<PERSON><PERSON>"], [32, 45, 20, 36], [32, 46, 20, 37], [32, 47, 20, 38, "then"], [32, 51, 20, 42], [32, 52, 20, 44, "auth"], [32, 56, 20, 48], [32, 60, 20, 53], [33, 8, 21, 6, "useAuthStore"], [33, 27, 21, 18], [33, 28, 21, 19, "setState"], [33, 36, 21, 27], [33, 37, 21, 28], [34, 10, 22, 8, "auth"], [34, 14, 22, 12], [34, 16, 22, 14, "auth"], [34, 20, 22, 18], [34, 23, 22, 21, "JSON"], [34, 27, 22, 25], [34, 28, 22, 26, "parse"], [34, 33, 22, 31], [34, 34, 22, 32, "auth"], [34, 38, 22, 36], [34, 39, 22, 37], [34, 42, 22, 40], [34, 46, 22, 44], [35, 10, 23, 8, "isReady"], [35, 17, 23, 15], [35, 19, 23, 17], [36, 8, 24, 6], [36, 9, 24, 7], [36, 10, 24, 8], [37, 6, 25, 4], [37, 7, 25, 5], [37, 8, 25, 6], [38, 4, 26, 2], [38, 5, 26, 3], [38, 7, 26, 5], [38, 9, 26, 7], [38, 10, 26, 8], [39, 4, 28, 2], [39, 8, 28, 2, "useEffect"], [39, 24, 28, 11], [39, 26, 28, 12], [39, 32, 28, 18], [39, 33, 28, 19], [39, 34, 28, 20], [39, 36, 28, 22], [39, 38, 28, 24], [39, 39, 28, 25], [40, 4, 30, 2], [40, 8, 30, 8, "signIn"], [40, 14, 30, 14], [40, 17, 30, 17], [40, 21, 30, 17, "useCallback"], [40, 39, 30, 28], [40, 41, 30, 29], [40, 47, 30, 35], [41, 6, 31, 4, "open"], [41, 10, 31, 8], [41, 11, 31, 9], [42, 8, 31, 11, "mode"], [42, 12, 31, 15], [42, 14, 31, 17], [43, 6, 31, 26], [43, 7, 31, 27], [43, 8, 31, 28], [44, 4, 32, 2], [44, 5, 32, 3], [44, 7, 32, 5], [44, 8, 32, 6, "open"], [44, 12, 32, 10], [44, 13, 32, 11], [44, 14, 32, 12], [45, 4, 33, 2], [45, 8, 33, 8, "signUp"], [45, 14, 33, 14], [45, 17, 33, 17], [45, 21, 33, 17, "useCallback"], [45, 39, 33, 28], [45, 41, 33, 29], [45, 47, 33, 35], [46, 6, 34, 4, "open"], [46, 10, 34, 8], [46, 11, 34, 9], [47, 8, 34, 11, "mode"], [47, 12, 34, 15], [47, 14, 34, 17], [48, 6, 34, 26], [48, 7, 34, 27], [48, 8, 34, 28], [49, 4, 35, 2], [49, 5, 35, 3], [49, 7, 35, 5], [49, 8, 35, 6, "open"], [49, 12, 35, 10], [49, 13, 35, 11], [49, 14, 35, 12], [50, 4, 37, 2], [50, 8, 37, 8, "signOut"], [50, 15, 37, 15], [50, 18, 37, 18], [50, 22, 37, 18, "useCallback"], [50, 40, 37, 29], [50, 42, 37, 30], [50, 48, 37, 36], [51, 6, 38, 4, "setAuth"], [51, 13, 38, 11], [51, 14, 38, 12], [51, 18, 38, 16], [51, 19, 38, 17], [52, 6, 39, 4, "close"], [52, 11, 39, 9], [52, 12, 39, 10], [52, 13, 39, 11], [53, 4, 40, 2], [53, 5, 40, 3], [53, 7, 40, 5], [53, 8, 40, 6, "close"], [53, 13, 40, 11], [53, 14, 40, 12], [53, 15, 40, 13], [54, 4, 42, 2], [54, 11, 42, 9], [55, 6, 43, 4, "isReady"], [55, 13, 43, 11], [56, 6, 44, 4, "isAuthenticated"], [56, 21, 44, 19], [56, 23, 44, 21, "isReady"], [56, 30, 44, 28], [56, 33, 44, 31], [56, 34, 44, 32], [56, 35, 44, 33, "auth"], [56, 39, 44, 37], [56, 42, 44, 40], [56, 46, 44, 44], [57, 6, 45, 4, "signIn"], [57, 12, 45, 10], [58, 6, 46, 4, "signOut"], [58, 13, 46, 11], [59, 6, 47, 4, "signUp"], [59, 12, 47, 10], [60, 6, 48, 4, "auth"], [60, 10, 48, 8], [61, 6, 49, 4, "setAuth"], [61, 13, 49, 11], [62, 6, 50, 4, "initiate"], [63, 4, 51, 2], [63, 5, 51, 3], [64, 2, 52, 0], [64, 3, 52, 1], [66, 2, 54, 0], [67, 0, 55, 0], [68, 0, 56, 0], [69, 2, 54, 0, "exports"], [69, 9, 54, 0], [69, 10, 54, 0, "useAuth"], [69, 17, 54, 0], [69, 20, 54, 0, "useAuth"], [69, 27, 54, 0], [70, 2, 54, 0, "_s"], [70, 4, 54, 0], [70, 5, 15, 13, "useAuth"], [70, 12, 15, 20], [71, 4, 15, 20], [71, 12, 16, 37, "useAuthStore"], [71, 31, 16, 49], [71, 33, 17, 34, "useAuthModal"], [71, 52, 17, 46], [72, 2, 17, 46], [73, 2, 57, 7], [73, 6, 57, 13, "useRequireAuth"], [73, 20, 57, 27], [73, 23, 57, 31, "options"], [73, 30, 57, 38], [73, 34, 57, 43], [74, 4, 57, 43, "_s2"], [74, 7, 57, 43], [75, 4, 58, 2], [75, 8, 58, 2, "_useAuth"], [75, 16, 58, 2], [75, 19, 58, 39, "useAuth"], [75, 26, 58, 46], [75, 27, 58, 47], [75, 28, 58, 48], [76, 6, 58, 10, "isAuthenticated"], [76, 21, 58, 25], [76, 24, 58, 25, "_useAuth"], [76, 32, 58, 25], [76, 33, 58, 10, "isAuthenticated"], [76, 48, 58, 25], [77, 6, 58, 27, "isReady"], [77, 13, 58, 34], [77, 16, 58, 34, "_useAuth"], [77, 24, 58, 34], [77, 25, 58, 27, "isReady"], [77, 32, 58, 34], [78, 4, 59, 2], [78, 8, 59, 2, "_useAuthModal2"], [78, 22, 59, 2], [78, 25, 59, 19], [78, 29, 59, 19, "useAuthModal"], [78, 48, 59, 31], [78, 50, 59, 32], [78, 51, 59, 33], [79, 6, 59, 10, "open"], [79, 10, 59, 14], [79, 13, 59, 14, "_useAuthModal2"], [79, 27, 59, 14], [79, 28, 59, 10, "open"], [79, 32, 59, 14], [80, 4, 61, 2], [80, 8, 61, 2, "useEffect"], [80, 24, 61, 11], [80, 26, 61, 12], [80, 32, 61, 18], [81, 6, 62, 4], [81, 10, 62, 8], [81, 11, 62, 9, "isAuthenticated"], [81, 26, 62, 24], [81, 30, 62, 28, "isReady"], [81, 37, 62, 35], [81, 39, 62, 37], [82, 8, 63, 6, "open"], [82, 12, 63, 10], [82, 13, 63, 11], [83, 10, 63, 13, "mode"], [83, 14, 63, 17], [83, 16, 63, 19, "options"], [83, 23, 63, 26], [83, 25, 63, 28, "mode"], [84, 8, 63, 33], [84, 9, 63, 34], [84, 10, 63, 35], [85, 6, 64, 4], [86, 4, 65, 2], [86, 5, 65, 3], [86, 7, 65, 5], [86, 8, 65, 6, "isAuthenticated"], [86, 23, 65, 21], [86, 25, 65, 23, "open"], [86, 29, 65, 27], [86, 31, 65, 29, "options"], [86, 38, 65, 36], [86, 40, 65, 38, "mode"], [86, 44, 65, 42], [86, 46, 65, 44, "isReady"], [86, 53, 65, 51], [86, 54, 65, 52], [86, 55, 65, 53], [87, 2, 66, 0], [87, 3, 66, 1], [88, 2, 66, 2, "exports"], [88, 9, 66, 2], [88, 10, 66, 2, "useRequireAuth"], [88, 24, 66, 2], [88, 27, 66, 2, "useRequireAuth"], [88, 41, 66, 2], [89, 2, 66, 2, "_s2"], [89, 5, 66, 2], [89, 6, 57, 13, "useRequireAuth"], [89, 20, 57, 27], [90, 4, 57, 27], [90, 12, 58, 39, "useAuth"], [90, 19, 58, 46], [90, 21, 59, 19, "useAuthModal"], [90, 40, 59, 31], [91, 2, 59, 31], [92, 2, 59, 31], [92, 6, 59, 31, "_default"], [92, 14, 59, 31], [92, 17, 59, 31, "exports"], [92, 24, 59, 31], [92, 25, 59, 31, "default"], [92, 32, 59, 31], [92, 35, 68, 15, "useAuth"], [92, 42, 68, 22], [93, 0, 68, 22], [93, 3]], "functionMap": {"names": ["<global>", "useAuth", "initiate", "SecureStore.getItemAsync.then$argument_0", "useEffect$argument_0", "signIn", "signUp", "signOut", "useRequireAuth"], "mappings": "AAA;uBCc;+BCI;2CCC;KDK;GDC;YGE,QH;6BIE;GJE;6BKC;GLE;8BME;GNG;CDY;8BQK;YJI;GII;CRC"}}, "type": "js/module"}]}