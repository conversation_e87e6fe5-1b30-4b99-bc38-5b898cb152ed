{"dependencies": [{"name": "./Asset", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 68, "index": 68}}], "key": "TwnMoPBJu+ST6a0NSE4l343cBbk=", "exportNames": ["*"]}}, {"name": "./PlatformUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 69}, "end": {"line": 2, "column": 59, "index": 128}}], "key": "vk5TSZJTws6vRpll7frKCvmMWgw=", "exportNames": ["*"]}}, {"name": "./resolveAssetSource", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 129}, "end": {"line": 3, "column": 86, "index": 215}}], "key": "qiXdfzfF08Pne6HL41q9H4FDwsA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _Asset = require(_dependencyMap[0], \"./Asset\");\n  var _PlatformUtils = require(_dependencyMap[1], \"./PlatformUtils\");\n  var _resolveAssetSource = _interopRequireWildcard(require(_dependencyMap[2], \"./resolveAssetSource\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  // Override React Native's asset resolution for `Image` components in contexts where it matters\n  if (_PlatformUtils.IS_ENV_WITH_LOCAL_ASSETS) {\n    var setTransformer = _resolveAssetSource.default.setCustomSourceTransformer || _resolveAssetSource.setCustomSourceTransformer;\n    setTransformer(resolver => {\n      try {\n        // Bundler is using the hashAssetFiles plugin if and only if the fileHashes property exists\n        if ('fileHashes' in resolver.asset && resolver.asset.fileHashes) {\n          var asset = _Asset.Asset.fromMetadata(resolver.asset);\n          if (asset.uri.startsWith(_Asset.ANDROID_EMBEDDED_URL_BASE_RESOURCE)) {\n            // TODO(@kitten): See https://github.com/expo/expo/commit/ec940b57a87d99ab4f1d06d87126e662c3f04f04#r155340943\n            // It's unclear whether this is sound since this may be our own AssetSourceResolver, which doesn't have this method\n            // Please compare `AssetSourceResolver` type from `react-native/Libraries/Image/AssetSourceResolver` against `./AssetSourceResolver`\n            return resolver.resourceIdentifierWithoutScale();\n          }\n          return resolver.fromSource(asset.downloaded ? asset.localUri : asset.uri);\n        } else {\n          return resolver.defaultAsset();\n        }\n      } catch {\n        return resolver.defaultAsset();\n      }\n    });\n  }\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 6, 1, 0, "_Asset"], [2, 12, 1, 0], [2, 15, 1, 0, "require"], [2, 22, 1, 0], [2, 23, 1, 0, "_dependencyMap"], [2, 37, 1, 0], [3, 2, 2, 0], [3, 6, 2, 0, "_PlatformUtils"], [3, 20, 2, 0], [3, 23, 2, 0, "require"], [3, 30, 2, 0], [3, 31, 2, 0, "_dependencyMap"], [3, 45, 2, 0], [4, 2, 3, 0], [4, 6, 3, 0, "_resolveAssetSource"], [4, 25, 3, 0], [4, 28, 3, 0, "_interopRequireWildcard"], [4, 51, 3, 0], [4, 52, 3, 0, "require"], [4, 59, 3, 0], [4, 60, 3, 0, "_dependencyMap"], [4, 74, 3, 0], [5, 2, 3, 86], [5, 11, 3, 86, "_interopRequireWildcard"], [5, 35, 3, 86, "e"], [5, 36, 3, 86], [5, 38, 3, 86, "t"], [5, 39, 3, 86], [5, 68, 3, 86, "WeakMap"], [5, 75, 3, 86], [5, 81, 3, 86, "r"], [5, 82, 3, 86], [5, 89, 3, 86, "WeakMap"], [5, 96, 3, 86], [5, 100, 3, 86, "n"], [5, 101, 3, 86], [5, 108, 3, 86, "WeakMap"], [5, 115, 3, 86], [5, 127, 3, 86, "_interopRequireWildcard"], [5, 150, 3, 86], [5, 162, 3, 86, "_interopRequireWildcard"], [5, 163, 3, 86, "e"], [5, 164, 3, 86], [5, 166, 3, 86, "t"], [5, 167, 3, 86], [5, 176, 3, 86, "t"], [5, 177, 3, 86], [5, 181, 3, 86, "e"], [5, 182, 3, 86], [5, 186, 3, 86, "e"], [5, 187, 3, 86], [5, 188, 3, 86, "__esModule"], [5, 198, 3, 86], [5, 207, 3, 86, "e"], [5, 208, 3, 86], [5, 214, 3, 86, "o"], [5, 215, 3, 86], [5, 217, 3, 86, "i"], [5, 218, 3, 86], [5, 220, 3, 86, "f"], [5, 221, 3, 86], [5, 226, 3, 86, "__proto__"], [5, 235, 3, 86], [5, 243, 3, 86, "default"], [5, 250, 3, 86], [5, 252, 3, 86, "e"], [5, 253, 3, 86], [5, 270, 3, 86, "e"], [5, 271, 3, 86], [5, 294, 3, 86, "e"], [5, 295, 3, 86], [5, 320, 3, 86, "e"], [5, 321, 3, 86], [5, 330, 3, 86, "f"], [5, 331, 3, 86], [5, 337, 3, 86, "o"], [5, 338, 3, 86], [5, 341, 3, 86, "t"], [5, 342, 3, 86], [5, 345, 3, 86, "n"], [5, 346, 3, 86], [5, 349, 3, 86, "r"], [5, 350, 3, 86], [5, 358, 3, 86, "o"], [5, 359, 3, 86], [5, 360, 3, 86, "has"], [5, 363, 3, 86], [5, 364, 3, 86, "e"], [5, 365, 3, 86], [5, 375, 3, 86, "o"], [5, 376, 3, 86], [5, 377, 3, 86, "get"], [5, 380, 3, 86], [5, 381, 3, 86, "e"], [5, 382, 3, 86], [5, 385, 3, 86, "o"], [5, 386, 3, 86], [5, 387, 3, 86, "set"], [5, 390, 3, 86], [5, 391, 3, 86, "e"], [5, 392, 3, 86], [5, 394, 3, 86, "f"], [5, 395, 3, 86], [5, 409, 3, 86, "_t"], [5, 411, 3, 86], [5, 415, 3, 86, "e"], [5, 416, 3, 86], [5, 432, 3, 86, "_t"], [5, 434, 3, 86], [5, 441, 3, 86, "hasOwnProperty"], [5, 455, 3, 86], [5, 456, 3, 86, "call"], [5, 460, 3, 86], [5, 461, 3, 86, "e"], [5, 462, 3, 86], [5, 464, 3, 86, "_t"], [5, 466, 3, 86], [5, 473, 3, 86, "i"], [5, 474, 3, 86], [5, 478, 3, 86, "o"], [5, 479, 3, 86], [5, 482, 3, 86, "Object"], [5, 488, 3, 86], [5, 489, 3, 86, "defineProperty"], [5, 503, 3, 86], [5, 508, 3, 86, "Object"], [5, 514, 3, 86], [5, 515, 3, 86, "getOwnPropertyDescriptor"], [5, 539, 3, 86], [5, 540, 3, 86, "e"], [5, 541, 3, 86], [5, 543, 3, 86, "_t"], [5, 545, 3, 86], [5, 552, 3, 86, "i"], [5, 553, 3, 86], [5, 554, 3, 86, "get"], [5, 557, 3, 86], [5, 561, 3, 86, "i"], [5, 562, 3, 86], [5, 563, 3, 86, "set"], [5, 566, 3, 86], [5, 570, 3, 86, "o"], [5, 571, 3, 86], [5, 572, 3, 86, "f"], [5, 573, 3, 86], [5, 575, 3, 86, "_t"], [5, 577, 3, 86], [5, 579, 3, 86, "i"], [5, 580, 3, 86], [5, 584, 3, 86, "f"], [5, 585, 3, 86], [5, 586, 3, 86, "_t"], [5, 588, 3, 86], [5, 592, 3, 86, "e"], [5, 593, 3, 86], [5, 594, 3, 86, "_t"], [5, 596, 3, 86], [5, 607, 3, 86, "f"], [5, 608, 3, 86], [5, 613, 3, 86, "e"], [5, 614, 3, 86], [5, 616, 3, 86, "t"], [5, 617, 3, 86], [6, 2, 4, 0], [7, 2, 5, 0], [7, 6, 5, 4, "IS_ENV_WITH_LOCAL_ASSETS"], [7, 45, 5, 28], [7, 47, 5, 30], [8, 4, 6, 4], [8, 8, 6, 10, "setTransformer"], [8, 22, 6, 24], [8, 25, 6, 27, "resolveAssetSource"], [8, 52, 6, 45], [8, 53, 6, 46, "setCustomSourceTransformer"], [8, 79, 6, 72], [8, 83, 6, 76, "setCustomSourceTransformer"], [8, 129, 6, 102], [9, 4, 7, 4, "setTransformer"], [9, 18, 7, 18], [9, 19, 7, 20, "resolver"], [9, 27, 7, 28], [9, 31, 7, 33], [10, 6, 8, 8], [10, 10, 8, 12], [11, 8, 9, 12], [12, 8, 10, 12], [12, 12, 10, 16], [12, 24, 10, 28], [12, 28, 10, 32, "resolver"], [12, 36, 10, 40], [12, 37, 10, 41, "asset"], [12, 42, 10, 46], [12, 46, 10, 50, "resolver"], [12, 54, 10, 58], [12, 55, 10, 59, "asset"], [12, 60, 10, 64], [12, 61, 10, 65, "fileHashes"], [12, 71, 10, 75], [12, 73, 10, 77], [13, 10, 11, 16], [13, 14, 11, 22, "asset"], [13, 19, 11, 27], [13, 22, 11, 30, "<PERSON><PERSON>"], [13, 34, 11, 35], [13, 35, 11, 36, "fromMetadata"], [13, 47, 11, 48], [13, 48, 11, 49, "resolver"], [13, 56, 11, 57], [13, 57, 11, 58, "asset"], [13, 62, 11, 63], [13, 63, 11, 64], [14, 10, 12, 16], [14, 14, 12, 20, "asset"], [14, 19, 12, 25], [14, 20, 12, 26, "uri"], [14, 23, 12, 29], [14, 24, 12, 30, "startsWith"], [14, 34, 12, 40], [14, 35, 12, 41, "ANDROID_EMBEDDED_URL_BASE_RESOURCE"], [14, 76, 12, 75], [14, 77, 12, 76], [14, 79, 12, 78], [15, 12, 13, 20], [16, 12, 14, 20], [17, 12, 15, 20], [18, 12, 16, 20], [18, 19, 16, 27, "resolver"], [18, 27, 16, 35], [18, 28, 16, 36, "resourceIdentifierWithoutScale"], [18, 58, 16, 66], [18, 59, 16, 67], [18, 60, 16, 68], [19, 10, 17, 16], [20, 10, 18, 16], [20, 17, 18, 23, "resolver"], [20, 25, 18, 31], [20, 26, 18, 32, "fromSource"], [20, 36, 18, 42], [20, 37, 18, 43, "asset"], [20, 42, 18, 48], [20, 43, 18, 49, "downloaded"], [20, 53, 18, 59], [20, 56, 18, 62, "asset"], [20, 61, 18, 67], [20, 62, 18, 68, "localUri"], [20, 70, 18, 76], [20, 73, 18, 79, "asset"], [20, 78, 18, 84], [20, 79, 18, 85, "uri"], [20, 82, 18, 88], [20, 83, 18, 89], [21, 8, 19, 12], [21, 9, 19, 13], [21, 15, 20, 17], [22, 10, 21, 16], [22, 17, 21, 23, "resolver"], [22, 25, 21, 31], [22, 26, 21, 32, "defaultAsset"], [22, 38, 21, 44], [22, 39, 21, 45], [22, 40, 21, 46], [23, 8, 22, 12], [24, 6, 23, 8], [24, 7, 23, 9], [24, 8, 24, 8], [24, 14, 24, 14], [25, 8, 25, 12], [25, 15, 25, 19, "resolver"], [25, 23, 25, 27], [25, 24, 25, 28, "defaultAsset"], [25, 36, 25, 40], [25, 37, 25, 41], [25, 38, 25, 42], [26, 6, 26, 8], [27, 4, 27, 4], [27, 5, 27, 5], [27, 6, 27, 6], [28, 2, 28, 0], [29, 0, 28, 1], [29, 3]], "functionMap": {"names": ["<global>", "setTransformer$argument_0"], "mappings": "AAA;mBCM;KDoB"}}, "type": "js/module"}]}