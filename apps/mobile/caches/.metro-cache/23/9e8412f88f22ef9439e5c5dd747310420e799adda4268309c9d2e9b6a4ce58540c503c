{"dependencies": [{"name": "./hasProperty", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 53, "index": 53}}], "key": "FXAswYs01w6oUruTCP9pv71/ioY=", "exportNames": ["*"]}}, {"name": "./parseTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 54}, "end": {"line": 2, "column": 54, "index": 108}}], "key": "TlPozx8q9h9SpmC3ZtUujVV7+3g=", "exportNames": ["*"]}}, {"name": "../../lib/resolve", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 109}, "end": {"line": 3, "column": 44, "index": 153}}], "key": "MJBV6chS7nLUCOw5eGZbJRediM0=", "exportNames": ["*"]}}, {"name": "../../lib/resolveAssetUri", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 154}, "end": {"line": 4, "column": 60, "index": 214}}], "key": "vf+dIfbd3/p0qZY5FeVvoC4wKAY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.prepare = void 0;\n  var _hasProperty = require(_dependencyMap[0], \"./hasProperty\");\n  var _parseTransform = require(_dependencyMap[1], \"./parseTransform\");\n  var _resolve = require(_dependencyMap[2], \"../../lib/resolve\");\n  var _resolveAssetUri2 = require(_dependencyMap[3], \"../../lib/resolveAssetUri\");\n  /**\n   * `react-native-svg` supports additional props that aren't defined in the spec.\n   * This function replaces them in a spec conforming manner.\n   *\n   * @param {WebShape} self Instance given to us.\n   * @param {Object?} props Optional overridden props given to us.\n   * @returns {Object} Cleaned props object.\n   * @private\n   */\n  const prepare = (self, props = self.props) => {\n    const {\n      transform,\n      origin,\n      originX,\n      originY,\n      fontFamily,\n      fontSize,\n      fontWeight,\n      fontStyle,\n      style,\n      forwardedRef,\n      gradientTransform,\n      patternTransform,\n      onPress,\n      ...rest\n    } = props;\n    const clean = {\n      ...((0, _hasProperty.hasTouchableProperty)(props) ? {\n        onStartShouldSetResponder: self.touchableHandleStartShouldSetResponder,\n        onResponderTerminationRequest: self.touchableHandleResponderTerminationRequest,\n        onResponderGrant: self.touchableHandleResponderGrant,\n        onResponderMove: self.touchableHandleResponderMove,\n        onResponderRelease: self.touchableHandleResponderRelease,\n        onResponderTerminate: self.touchableHandleResponderTerminate\n      } : null),\n      ...rest\n    };\n    if (origin != null) {\n      clean['transform-origin'] = origin.toString().replace(',', ' ');\n    } else if (originX != null || originY != null) {\n      clean['transform-origin'] = `${originX || 0} ${originY || 0}`;\n    }\n\n    // we do it like this because setting transform as undefined causes error in web\n    const parsedTransform = (0, _parseTransform.parseTransformProp)(transform, props);\n    if (parsedTransform) {\n      clean.transform = parsedTransform;\n    }\n    const parsedGradientTransform = (0, _parseTransform.parseTransformProp)(gradientTransform);\n    if (parsedGradientTransform) {\n      clean.gradientTransform = parsedGradientTransform;\n    }\n    const parsedPatternTransform = (0, _parseTransform.parseTransformProp)(patternTransform);\n    if (parsedPatternTransform) {\n      clean.patternTransform = parsedPatternTransform;\n    }\n    clean.ref = el => {\n      self.elementRef.current = el;\n      if (typeof forwardedRef === 'function') {\n        forwardedRef(el);\n      } else if (forwardedRef) {\n        forwardedRef.current = el;\n      }\n    };\n    const styles = {};\n    if (fontFamily != null) {\n      styles.fontFamily = fontFamily;\n    }\n    if (fontSize != null) {\n      styles.fontSize = fontSize;\n    }\n    if (fontWeight != null) {\n      styles.fontWeight = fontWeight;\n    }\n    if (fontStyle != null) {\n      styles.fontStyle = fontStyle;\n    }\n    clean.style = (0, _resolve.resolve)(style, styles);\n    if (onPress !== null) {\n      clean.onClick = props.onPress;\n    }\n    if (props.href !== null && props.href !== undefined) {\n      var _resolveAssetUri;\n      clean.href = (_resolveAssetUri = (0, _resolveAssetUri2.resolveAssetUri)(props.href)) === null || _resolveAssetUri === void 0 ? void 0 : _resolveAssetUri.uri;\n    }\n    return clean;\n  };\n  exports.prepare = prepare;\n});", "lineCount": 98, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_hasProperty"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_parseTransform"], [7, 21, 2, 0], [7, 24, 2, 0, "require"], [7, 31, 2, 0], [7, 32, 2, 0, "_dependencyMap"], [7, 46, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_resolve"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_resolveAssetUri2"], [9, 23, 4, 0], [9, 26, 4, 0, "require"], [9, 33, 4, 0], [9, 34, 4, 0, "_dependencyMap"], [9, 48, 4, 0], [10, 2, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 2, 14, 7], [19, 8, 14, 13, "prepare"], [19, 15, 14, 20], [19, 18, 14, 23, "prepare"], [19, 19, 14, 24, "self"], [19, 23, 14, 28], [19, 25, 14, 30, "props"], [19, 30, 14, 35], [19, 33, 14, 38, "self"], [19, 37, 14, 42], [19, 38, 14, 43, "props"], [19, 43, 14, 48], [19, 48, 14, 53], [20, 4, 15, 2], [20, 10, 15, 8], [21, 6, 16, 4, "transform"], [21, 15, 16, 13], [22, 6, 17, 4, "origin"], [22, 12, 17, 10], [23, 6, 18, 4, "originX"], [23, 13, 18, 11], [24, 6, 19, 4, "originY"], [24, 13, 19, 11], [25, 6, 20, 4, "fontFamily"], [25, 16, 20, 14], [26, 6, 21, 4, "fontSize"], [26, 14, 21, 12], [27, 6, 22, 4, "fontWeight"], [27, 16, 22, 14], [28, 6, 23, 4, "fontStyle"], [28, 15, 23, 13], [29, 6, 24, 4, "style"], [29, 11, 24, 9], [30, 6, 25, 4, "forwardedRef"], [30, 18, 25, 16], [31, 6, 26, 4, "gradientTransform"], [31, 23, 26, 21], [32, 6, 27, 4, "patternTransform"], [32, 22, 27, 20], [33, 6, 28, 4, "onPress"], [33, 13, 28, 11], [34, 6, 29, 4], [34, 9, 29, 7, "rest"], [35, 4, 30, 2], [35, 5, 30, 3], [35, 8, 30, 6, "props"], [35, 13, 30, 11], [36, 4, 31, 2], [36, 10, 31, 8, "clean"], [36, 15, 31, 13], [36, 18, 31, 16], [37, 6, 32, 4], [37, 10, 32, 8], [37, 14, 32, 8, "hasTouchableProperty"], [37, 47, 32, 28], [37, 49, 32, 29, "props"], [37, 54, 32, 34], [37, 55, 32, 35], [37, 58, 32, 38], [38, 8, 33, 6, "onStartShouldSetResponder"], [38, 33, 33, 31], [38, 35, 33, 33, "self"], [38, 39, 33, 37], [38, 40, 33, 38, "touchableHandleStartShouldSetResponder"], [38, 78, 33, 76], [39, 8, 34, 6, "onResponderTerminationRequest"], [39, 37, 34, 35], [39, 39, 34, 37, "self"], [39, 43, 34, 41], [39, 44, 34, 42, "touchableHandleResponderTerminationRequest"], [39, 86, 34, 84], [40, 8, 35, 6, "onResponderGrant"], [40, 24, 35, 22], [40, 26, 35, 24, "self"], [40, 30, 35, 28], [40, 31, 35, 29, "touchableHandleResponderGrant"], [40, 60, 35, 58], [41, 8, 36, 6, "onResponderMove"], [41, 23, 36, 21], [41, 25, 36, 23, "self"], [41, 29, 36, 27], [41, 30, 36, 28, "touchableHandleResponderMove"], [41, 58, 36, 56], [42, 8, 37, 6, "onResponderRelease"], [42, 26, 37, 24], [42, 28, 37, 26, "self"], [42, 32, 37, 30], [42, 33, 37, 31, "touchableHandleResponderRelease"], [42, 64, 37, 62], [43, 8, 38, 6, "onResponderTerminate"], [43, 28, 38, 26], [43, 30, 38, 28, "self"], [43, 34, 38, 32], [43, 35, 38, 33, "touchableHandleResponderTerminate"], [44, 6, 39, 4], [44, 7, 39, 5], [44, 10, 39, 8], [44, 14, 39, 12], [44, 15, 39, 13], [45, 6, 40, 4], [45, 9, 40, 7, "rest"], [46, 4, 41, 2], [46, 5, 41, 3], [47, 4, 42, 2], [47, 8, 42, 6, "origin"], [47, 14, 42, 12], [47, 18, 42, 16], [47, 22, 42, 20], [47, 24, 42, 22], [48, 6, 43, 4, "clean"], [48, 11, 43, 9], [48, 12, 43, 10], [48, 30, 43, 28], [48, 31, 43, 29], [48, 34, 43, 32, "origin"], [48, 40, 43, 38], [48, 41, 43, 39, "toString"], [48, 49, 43, 47], [48, 50, 43, 48], [48, 51, 43, 49], [48, 52, 43, 50, "replace"], [48, 59, 43, 57], [48, 60, 43, 58], [48, 63, 43, 61], [48, 65, 43, 63], [48, 68, 43, 66], [48, 69, 43, 67], [49, 4, 44, 2], [49, 5, 44, 3], [49, 11, 44, 9], [49, 15, 44, 13, "originX"], [49, 22, 44, 20], [49, 26, 44, 24], [49, 30, 44, 28], [49, 34, 44, 32, "originY"], [49, 41, 44, 39], [49, 45, 44, 43], [49, 49, 44, 47], [49, 51, 44, 49], [50, 6, 45, 4, "clean"], [50, 11, 45, 9], [50, 12, 45, 10], [50, 30, 45, 28], [50, 31, 45, 29], [50, 34, 45, 32], [50, 37, 45, 35, "originX"], [50, 44, 45, 42], [50, 48, 45, 46], [50, 49, 45, 47], [50, 53, 45, 51, "originY"], [50, 60, 45, 58], [50, 64, 45, 62], [50, 65, 45, 63], [50, 67, 45, 65], [51, 4, 46, 2], [53, 4, 48, 2], [54, 4, 49, 2], [54, 10, 49, 8, "parsedTransform"], [54, 25, 49, 23], [54, 28, 49, 26], [54, 32, 49, 26, "parseTransformProp"], [54, 66, 49, 44], [54, 68, 49, 45, "transform"], [54, 77, 49, 54], [54, 79, 49, 56, "props"], [54, 84, 49, 61], [54, 85, 49, 62], [55, 4, 50, 2], [55, 8, 50, 6, "parsedTransform"], [55, 23, 50, 21], [55, 25, 50, 23], [56, 6, 51, 4, "clean"], [56, 11, 51, 9], [56, 12, 51, 10, "transform"], [56, 21, 51, 19], [56, 24, 51, 22, "parsedTransform"], [56, 39, 51, 37], [57, 4, 52, 2], [58, 4, 53, 2], [58, 10, 53, 8, "parsedGradientTransform"], [58, 33, 53, 31], [58, 36, 53, 34], [58, 40, 53, 34, "parseTransformProp"], [58, 74, 53, 52], [58, 76, 53, 53, "gradientTransform"], [58, 93, 53, 70], [58, 94, 53, 71], [59, 4, 54, 2], [59, 8, 54, 6, "parsedGradientTransform"], [59, 31, 54, 29], [59, 33, 54, 31], [60, 6, 55, 4, "clean"], [60, 11, 55, 9], [60, 12, 55, 10, "gradientTransform"], [60, 29, 55, 27], [60, 32, 55, 30, "parsedGradientTransform"], [60, 55, 55, 53], [61, 4, 56, 2], [62, 4, 57, 2], [62, 10, 57, 8, "parsedPatternTransform"], [62, 32, 57, 30], [62, 35, 57, 33], [62, 39, 57, 33, "parseTransformProp"], [62, 73, 57, 51], [62, 75, 57, 52, "patternTransform"], [62, 91, 57, 68], [62, 92, 57, 69], [63, 4, 58, 2], [63, 8, 58, 6, "parsedPatternTransform"], [63, 30, 58, 28], [63, 32, 58, 30], [64, 6, 59, 4, "clean"], [64, 11, 59, 9], [64, 12, 59, 10, "patternTransform"], [64, 28, 59, 26], [64, 31, 59, 29, "parsedPatternTransform"], [64, 53, 59, 51], [65, 4, 60, 2], [66, 4, 61, 2, "clean"], [66, 9, 61, 7], [66, 10, 61, 8, "ref"], [66, 13, 61, 11], [66, 16, 61, 14, "el"], [66, 18, 61, 16], [66, 22, 61, 20], [67, 6, 62, 4, "self"], [67, 10, 62, 8], [67, 11, 62, 9, "elementRef"], [67, 21, 62, 19], [67, 22, 62, 20, "current"], [67, 29, 62, 27], [67, 32, 62, 30, "el"], [67, 34, 62, 32], [68, 6, 63, 4], [68, 10, 63, 8], [68, 17, 63, 15, "forwardedRef"], [68, 29, 63, 27], [68, 34, 63, 32], [68, 44, 63, 42], [68, 46, 63, 44], [69, 8, 64, 6, "forwardedRef"], [69, 20, 64, 18], [69, 21, 64, 19, "el"], [69, 23, 64, 21], [69, 24, 64, 22], [70, 6, 65, 4], [70, 7, 65, 5], [70, 13, 65, 11], [70, 17, 65, 15, "forwardedRef"], [70, 29, 65, 27], [70, 31, 65, 29], [71, 8, 66, 6, "forwardedRef"], [71, 20, 66, 18], [71, 21, 66, 19, "current"], [71, 28, 66, 26], [71, 31, 66, 29, "el"], [71, 33, 66, 31], [72, 6, 67, 4], [73, 4, 68, 2], [73, 5, 68, 3], [74, 4, 69, 2], [74, 10, 69, 8, "styles"], [74, 16, 69, 14], [74, 19, 69, 17], [74, 20, 69, 18], [74, 21, 69, 19], [75, 4, 70, 2], [75, 8, 70, 6, "fontFamily"], [75, 18, 70, 16], [75, 22, 70, 20], [75, 26, 70, 24], [75, 28, 70, 26], [76, 6, 71, 4, "styles"], [76, 12, 71, 10], [76, 13, 71, 11, "fontFamily"], [76, 23, 71, 21], [76, 26, 71, 24, "fontFamily"], [76, 36, 71, 34], [77, 4, 72, 2], [78, 4, 73, 2], [78, 8, 73, 6, "fontSize"], [78, 16, 73, 14], [78, 20, 73, 18], [78, 24, 73, 22], [78, 26, 73, 24], [79, 6, 74, 4, "styles"], [79, 12, 74, 10], [79, 13, 74, 11, "fontSize"], [79, 21, 74, 19], [79, 24, 74, 22, "fontSize"], [79, 32, 74, 30], [80, 4, 75, 2], [81, 4, 76, 2], [81, 8, 76, 6, "fontWeight"], [81, 18, 76, 16], [81, 22, 76, 20], [81, 26, 76, 24], [81, 28, 76, 26], [82, 6, 77, 4, "styles"], [82, 12, 77, 10], [82, 13, 77, 11, "fontWeight"], [82, 23, 77, 21], [82, 26, 77, 24, "fontWeight"], [82, 36, 77, 34], [83, 4, 78, 2], [84, 4, 79, 2], [84, 8, 79, 6, "fontStyle"], [84, 17, 79, 15], [84, 21, 79, 19], [84, 25, 79, 23], [84, 27, 79, 25], [85, 6, 80, 4, "styles"], [85, 12, 80, 10], [85, 13, 80, 11, "fontStyle"], [85, 22, 80, 20], [85, 25, 80, 23, "fontStyle"], [85, 34, 80, 32], [86, 4, 81, 2], [87, 4, 82, 2, "clean"], [87, 9, 82, 7], [87, 10, 82, 8, "style"], [87, 15, 82, 13], [87, 18, 82, 16], [87, 22, 82, 16, "resolve"], [87, 38, 82, 23], [87, 40, 82, 24, "style"], [87, 45, 82, 29], [87, 47, 82, 31, "styles"], [87, 53, 82, 37], [87, 54, 82, 38], [88, 4, 83, 2], [88, 8, 83, 6, "onPress"], [88, 15, 83, 13], [88, 20, 83, 18], [88, 24, 83, 22], [88, 26, 83, 24], [89, 6, 84, 4, "clean"], [89, 11, 84, 9], [89, 12, 84, 10, "onClick"], [89, 19, 84, 17], [89, 22, 84, 20, "props"], [89, 27, 84, 25], [89, 28, 84, 26, "onPress"], [89, 35, 84, 33], [90, 4, 85, 2], [91, 4, 86, 2], [91, 8, 86, 6, "props"], [91, 13, 86, 11], [91, 14, 86, 12, "href"], [91, 18, 86, 16], [91, 23, 86, 21], [91, 27, 86, 25], [91, 31, 86, 29, "props"], [91, 36, 86, 34], [91, 37, 86, 35, "href"], [91, 41, 86, 39], [91, 46, 86, 44, "undefined"], [91, 55, 86, 53], [91, 57, 86, 55], [92, 6, 87, 4], [92, 10, 87, 8, "_resolveAssetUri"], [92, 26, 87, 24], [93, 6, 88, 4, "clean"], [93, 11, 88, 9], [93, 12, 88, 10, "href"], [93, 16, 88, 14], [93, 19, 88, 17], [93, 20, 88, 18, "_resolveAssetUri"], [93, 36, 88, 34], [93, 39, 88, 37], [93, 43, 88, 37, "resolveAssetUri"], [93, 76, 88, 52], [93, 78, 88, 53, "props"], [93, 83, 88, 58], [93, 84, 88, 59, "href"], [93, 88, 88, 63], [93, 89, 88, 64], [93, 95, 88, 70], [93, 99, 88, 74], [93, 103, 88, 78, "_resolveAssetUri"], [93, 119, 88, 94], [93, 124, 88, 99], [93, 129, 88, 104], [93, 130, 88, 105], [93, 133, 88, 108], [93, 138, 88, 113], [93, 139, 88, 114], [93, 142, 88, 117, "_resolveAssetUri"], [93, 158, 88, 133], [93, 159, 88, 134, "uri"], [93, 162, 88, 137], [94, 4, 89, 2], [95, 4, 90, 2], [95, 11, 90, 9, "clean"], [95, 16, 90, 14], [96, 2, 91, 0], [96, 3, 91, 1], [97, 2, 91, 2, "exports"], [97, 9, 91, 2], [97, 10, 91, 2, "prepare"], [97, 17, 91, 2], [97, 20, 91, 2, "prepare"], [97, 27, 91, 2], [98, 0, 91, 2], [98, 3]], "functionMap": {"names": ["<global>", "prepare", "clean.ref"], "mappings": "AAA;uBCa;cC+C;GDO;CDuB"}}, "type": "js/module"}]}