{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Pointer = exports.default = (0, _createLucideIcon.default)(\"Pointer\", [[\"path\", {\n    d: \"M22 14a8 8 0 0 1-8 8\",\n    key: \"56vcr3\"\n  }], [\"path\", {\n    d: \"M18 11v-1a2 2 0 0 0-2-2a2 2 0 0 0-2 2\",\n    key: \"1agjmk\"\n  }], [\"path\", {\n    d: \"M14 10V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v1\",\n    key: \"wdbh2u\"\n  }], [\"path\", {\n    d: \"M10 9.5V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v10\",\n    key: \"1ibuk9\"\n  }], [\"path\", {\n    d: \"M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15\",\n    key: \"g6ys72\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Pointer"], [15, 13, 10, 13], [15, 16, 10, 13, "exports"], [15, 23, 10, 13], [15, 24, 10, 13, "default"], [15, 31, 10, 13], [15, 34, 10, 16], [15, 38, 10, 16, "createLucideIcon"], [15, 63, 10, 32], [15, 65, 10, 33], [15, 74, 10, 42], [15, 76, 10, 44], [15, 77, 11, 2], [15, 78, 11, 3], [15, 84, 11, 9], [15, 86, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 29, 11, 38], [17, 4, 11, 40, "key"], [17, 7, 11, 43], [17, 9, 11, 45], [18, 2, 11, 54], [18, 3, 11, 55], [18, 4, 11, 56], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 46, 12, 55], [20, 4, 12, 57, "key"], [20, 7, 12, 60], [20, 9, 12, 62], [21, 2, 12, 71], [21, 3, 12, 72], [21, 4, 12, 73], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 47, 13, 56], [23, 4, 13, 58, "key"], [23, 7, 13, 61], [23, 9, 13, 63], [24, 2, 13, 72], [24, 3, 13, 73], [24, 4, 13, 74], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 49, 14, 58], [26, 4, 14, 60, "key"], [26, 7, 14, 63], [26, 9, 14, 65], [27, 2, 14, 74], [27, 3, 14, 75], [27, 4, 14, 76], [27, 6, 15, 2], [27, 7, 16, 4], [27, 13, 16, 10], [27, 15, 17, 4], [28, 4, 18, 6, "d"], [28, 5, 18, 7], [28, 7, 18, 9], [28, 107, 18, 109], [29, 4, 19, 6, "key"], [29, 7, 19, 9], [29, 9, 19, 11], [30, 2, 20, 4], [30, 3, 20, 5], [30, 4, 21, 3], [30, 5, 22, 1], [30, 6, 22, 2], [31, 0, 22, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}