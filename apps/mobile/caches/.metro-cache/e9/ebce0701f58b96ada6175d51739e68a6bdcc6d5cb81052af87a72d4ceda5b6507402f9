{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Presentation = exports.default = (0, _createLucideIcon.default)(\"Presentation\", [[\"path\", {\n    d: \"M2 3h20\",\n    key: \"91anmk\"\n  }], [\"path\", {\n    d: \"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3\",\n    key: \"2k9sn8\"\n  }], [\"path\", {\n    d: \"m7 21 5-5 5 5\",\n    key: \"bip4we\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Presentation"], [15, 20, 10, 18], [15, 23, 10, 18, "exports"], [15, 30, 10, 18], [15, 31, 10, 18, "default"], [15, 38, 10, 18], [15, 41, 10, 21], [15, 45, 10, 21, "createLucideIcon"], [15, 70, 10, 37], [15, 72, 10, 38], [15, 86, 10, 52], [15, 88, 10, 54], [15, 89, 11, 2], [15, 90, 11, 3], [15, 96, 11, 9], [15, 98, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 16, 11, 25], [17, 4, 11, 27, "key"], [17, 7, 11, 30], [17, 9, 11, 32], [18, 2, 11, 41], [18, 3, 11, 42], [18, 4, 11, 43], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 49, 12, 58], [20, 4, 12, 60, "key"], [20, 7, 12, 63], [20, 9, 12, 65], [21, 2, 12, 74], [21, 3, 12, 75], [21, 4, 12, 76], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 22, 13, 31], [23, 4, 13, 33, "key"], [23, 7, 13, 36], [23, 9, 13, 38], [24, 2, 13, 47], [24, 3, 13, 48], [24, 4, 13, 49], [24, 5, 14, 1], [24, 6, 14, 2], [25, 0, 14, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}