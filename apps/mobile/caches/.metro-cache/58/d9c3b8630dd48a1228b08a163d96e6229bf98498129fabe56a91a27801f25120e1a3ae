{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MountRegistry = void 0;\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-extraneous-class\n  class MountRegistry {\n    static addMountListener(listener) {\n      this.mountListeners.add(listener);\n      return () => {\n        this.mountListeners.delete(listener);\n      };\n    }\n    static addUnmountListener(listener) {\n      this.unmountListeners.add(listener);\n      return () => {\n        this.unmountListeners.delete(listener);\n      };\n    }\n    static gestureHandlerWillMount(handler) {\n      this.mountListeners.forEach(listener => listener(handler));\n    }\n    static gestureHandlerWillUnmount(handler) {\n      this.unmountListeners.forEach(listener => listener(handler));\n    }\n    static gestureWillMount(gesture) {\n      this.mountListeners.forEach(listener => listener(gesture));\n    }\n    static gestureWillUnmount(gesture) {\n      this.unmountListeners.forEach(listener => listener(gesture));\n    }\n  }\n  exports.MountRegistry = MountRegistry;\n  _defineProperty(MountRegistry, \"mountListeners\", new Set());\n  _defineProperty(MountRegistry, \"unmountListeners\", new Set());\n});", "lineCount": 50, "map": [[6, 2, 1, 0], [6, 11, 1, 9, "_defineProperty"], [6, 26, 1, 24, "_defineProperty"], [6, 27, 1, 25, "obj"], [6, 30, 1, 28], [6, 32, 1, 30, "key"], [6, 35, 1, 33], [6, 37, 1, 35, "value"], [6, 42, 1, 40], [6, 44, 1, 42], [7, 4, 1, 44], [7, 8, 1, 48, "key"], [7, 11, 1, 51], [7, 15, 1, 55, "obj"], [7, 18, 1, 58], [7, 20, 1, 60], [8, 6, 1, 62, "Object"], [8, 12, 1, 68], [8, 13, 1, 69, "defineProperty"], [8, 27, 1, 83], [8, 28, 1, 84, "obj"], [8, 31, 1, 87], [8, 33, 1, 89, "key"], [8, 36, 1, 92], [8, 38, 1, 94], [9, 8, 1, 96, "value"], [9, 13, 1, 101], [9, 15, 1, 103, "value"], [9, 20, 1, 108], [10, 8, 1, 110, "enumerable"], [10, 18, 1, 120], [10, 20, 1, 122], [10, 24, 1, 126], [11, 8, 1, 128, "configurable"], [11, 20, 1, 140], [11, 22, 1, 142], [11, 26, 1, 146], [12, 8, 1, 148, "writable"], [12, 16, 1, 156], [12, 18, 1, 158], [13, 6, 1, 163], [13, 7, 1, 164], [13, 8, 1, 165], [14, 4, 1, 167], [14, 5, 1, 168], [14, 11, 1, 174], [15, 6, 1, 176, "obj"], [15, 9, 1, 179], [15, 10, 1, 180, "key"], [15, 13, 1, 183], [15, 14, 1, 184], [15, 17, 1, 187, "value"], [15, 22, 1, 192], [16, 4, 1, 194], [17, 4, 1, 196], [17, 11, 1, 203, "obj"], [17, 14, 1, 206], [18, 2, 1, 208], [20, 2, 3, 0], [21, 2, 4, 7], [21, 8, 4, 13, "MountRegistry"], [21, 21, 4, 26], [21, 22, 4, 27], [22, 4, 5, 2], [22, 11, 5, 9, "addMountListener"], [22, 27, 5, 25, "addMountListener"], [22, 28, 5, 26, "listener"], [22, 36, 5, 34], [22, 38, 5, 36], [23, 6, 6, 4], [23, 10, 6, 8], [23, 11, 6, 9, "mountListeners"], [23, 25, 6, 23], [23, 26, 6, 24, "add"], [23, 29, 6, 27], [23, 30, 6, 28, "listener"], [23, 38, 6, 36], [23, 39, 6, 37], [24, 6, 7, 4], [24, 13, 7, 11], [24, 19, 7, 17], [25, 8, 8, 6], [25, 12, 8, 10], [25, 13, 8, 11, "mountListeners"], [25, 27, 8, 25], [25, 28, 8, 26, "delete"], [25, 34, 8, 32], [25, 35, 8, 33, "listener"], [25, 43, 8, 41], [25, 44, 8, 42], [26, 6, 9, 4], [26, 7, 9, 5], [27, 4, 10, 2], [28, 4, 12, 2], [28, 11, 12, 9, "addUnmountListener"], [28, 29, 12, 27, "addUnmountListener"], [28, 30, 12, 28, "listener"], [28, 38, 12, 36], [28, 40, 12, 38], [29, 6, 13, 4], [29, 10, 13, 8], [29, 11, 13, 9, "unmountListeners"], [29, 27, 13, 25], [29, 28, 13, 26, "add"], [29, 31, 13, 29], [29, 32, 13, 30, "listener"], [29, 40, 13, 38], [29, 41, 13, 39], [30, 6, 14, 4], [30, 13, 14, 11], [30, 19, 14, 17], [31, 8, 15, 6], [31, 12, 15, 10], [31, 13, 15, 11, "unmountListeners"], [31, 29, 15, 27], [31, 30, 15, 28, "delete"], [31, 36, 15, 34], [31, 37, 15, 35, "listener"], [31, 45, 15, 43], [31, 46, 15, 44], [32, 6, 16, 4], [32, 7, 16, 5], [33, 4, 17, 2], [34, 4, 19, 2], [34, 11, 19, 9, "gestureHandlerWillMount"], [34, 34, 19, 32, "gestureHandlerWillMount"], [34, 35, 19, 33, "handler"], [34, 42, 19, 40], [34, 44, 19, 42], [35, 6, 20, 4], [35, 10, 20, 8], [35, 11, 20, 9, "mountListeners"], [35, 25, 20, 23], [35, 26, 20, 24, "for<PERSON>ach"], [35, 33, 20, 31], [35, 34, 20, 32, "listener"], [35, 42, 20, 40], [35, 46, 20, 44, "listener"], [35, 54, 20, 52], [35, 55, 20, 53, "handler"], [35, 62, 20, 60], [35, 63, 20, 61], [35, 64, 20, 62], [36, 4, 21, 2], [37, 4, 23, 2], [37, 11, 23, 9, "gestureHandlerWillUnmount"], [37, 36, 23, 34, "gestureHandlerWillUnmount"], [37, 37, 23, 35, "handler"], [37, 44, 23, 42], [37, 46, 23, 44], [38, 6, 24, 4], [38, 10, 24, 8], [38, 11, 24, 9, "unmountListeners"], [38, 27, 24, 25], [38, 28, 24, 26, "for<PERSON>ach"], [38, 35, 24, 33], [38, 36, 24, 34, "listener"], [38, 44, 24, 42], [38, 48, 24, 46, "listener"], [38, 56, 24, 54], [38, 57, 24, 55, "handler"], [38, 64, 24, 62], [38, 65, 24, 63], [38, 66, 24, 64], [39, 4, 25, 2], [40, 4, 27, 2], [40, 11, 27, 9, "gestureWillMount"], [40, 27, 27, 25, "gestureWillMount"], [40, 28, 27, 26, "gesture"], [40, 35, 27, 33], [40, 37, 27, 35], [41, 6, 28, 4], [41, 10, 28, 8], [41, 11, 28, 9, "mountListeners"], [41, 25, 28, 23], [41, 26, 28, 24, "for<PERSON>ach"], [41, 33, 28, 31], [41, 34, 28, 32, "listener"], [41, 42, 28, 40], [41, 46, 28, 44, "listener"], [41, 54, 28, 52], [41, 55, 28, 53, "gesture"], [41, 62, 28, 60], [41, 63, 28, 61], [41, 64, 28, 62], [42, 4, 29, 2], [43, 4, 31, 2], [43, 11, 31, 9, "gestureWillUnmount"], [43, 29, 31, 27, "gestureWillUnmount"], [43, 30, 31, 28, "gesture"], [43, 37, 31, 35], [43, 39, 31, 37], [44, 6, 32, 4], [44, 10, 32, 8], [44, 11, 32, 9, "unmountListeners"], [44, 27, 32, 25], [44, 28, 32, 26, "for<PERSON>ach"], [44, 35, 32, 33], [44, 36, 32, 34, "listener"], [44, 44, 32, 42], [44, 48, 32, 46, "listener"], [44, 56, 32, 54], [44, 57, 32, 55, "gesture"], [44, 64, 32, 62], [44, 65, 32, 63], [44, 66, 32, 64], [45, 4, 33, 2], [46, 2, 35, 0], [47, 2, 35, 1, "exports"], [47, 9, 35, 1], [47, 10, 35, 1, "MountRegistry"], [47, 23, 35, 1], [47, 26, 35, 1, "MountRegistry"], [47, 39, 35, 1], [48, 2, 37, 0, "_defineProperty"], [48, 17, 37, 15], [48, 18, 37, 16, "MountRegistry"], [48, 31, 37, 29], [48, 33, 37, 31], [48, 49, 37, 47], [48, 51, 37, 49], [48, 55, 37, 53, "Set"], [48, 58, 37, 56], [48, 59, 37, 57], [48, 60, 37, 58], [48, 61, 37, 59], [49, 2, 39, 0, "_defineProperty"], [49, 17, 39, 15], [49, 18, 39, 16, "MountRegistry"], [49, 31, 39, 29], [49, 33, 39, 31], [49, 51, 39, 49], [49, 53, 39, 51], [49, 57, 39, 55, "Set"], [49, 60, 39, 58], [49, 61, 39, 59], [49, 62, 39, 60], [49, 63, 39, 61], [50, 0, 39, 62], [50, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "MountRegistry", "MountRegistry.addMountListener", "<anonymous>", "MountRegistry.addUnmountListener", "MountRegistry.gestureHandlerWillMount", "mountListeners.forEach$argument_0", "MountRegistry.gestureHandlerWillUnmount", "unmountListeners.forEach$argument_0", "MountRegistry.gestureWillMount", "MountRegistry.gestureWillUnmount"], "mappings": "AAA,iNC;OCG;ECC;WCE;KDE;GDC;EGE;WDE;KCE;GHC;EIE;gCCC,6BD;GJC;EME;kCCC,6BD;GNC;EQE;gCHC,6BG;GRC;ESE;kCFC,6BE;GTC;CDE"}}, "type": "js/module"}]}