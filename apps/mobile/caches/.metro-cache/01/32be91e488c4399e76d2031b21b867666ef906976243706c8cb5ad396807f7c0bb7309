{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CellRenderMask = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[4], \"invariant\"));\n  var CellRenderMask = exports.CellRenderMask = /*#__PURE__*/function () {\n    function CellRenderMask(numCells) {\n      (0, _classCallCheck2.default)(this, CellRenderMask);\n      (0, _invariant.default)(numCells >= 0, 'CellRenderMask must contain a non-negative number os cells');\n      this._numCells = numCells;\n      if (numCells === 0) {\n        this._regions = [];\n      } else {\n        this._regions = [{\n          first: 0,\n          last: numCells - 1,\n          isSpacer: true\n        }];\n      }\n    }\n    return (0, _createClass2.default)(CellRenderMask, [{\n      key: \"enumerateRegions\",\n      value: function enumerateRegions() {\n        return this._regions;\n      }\n    }, {\n      key: \"addCells\",\n      value: function addCells(cells) {\n        (0, _invariant.default)(cells.first >= 0 && cells.first < this._numCells && cells.last >= -1 && cells.last < this._numCells && cells.last >= cells.first - 1, 'CellRenderMask.addCells called with invalid cell range');\n        if (cells.last < cells.first) {\n          return;\n        }\n        var _this$_findRegion = this._findRegion(cells.first),\n          _this$_findRegion2 = (0, _slicedToArray2.default)(_this$_findRegion, 2),\n          firstIntersect = _this$_findRegion2[0],\n          firstIntersectIdx = _this$_findRegion2[1];\n        var _this$_findRegion3 = this._findRegion(cells.last),\n          _this$_findRegion4 = (0, _slicedToArray2.default)(_this$_findRegion3, 2),\n          lastIntersect = _this$_findRegion4[0],\n          lastIntersectIdx = _this$_findRegion4[1];\n        if (firstIntersectIdx === lastIntersectIdx && !firstIntersect.isSpacer) {\n          return;\n        }\n        var newLeadRegion = [];\n        var newTailRegion = [];\n        var newMainRegion = {\n          ...cells,\n          isSpacer: false\n        };\n        if (firstIntersect.first < newMainRegion.first) {\n          if (firstIntersect.isSpacer) {\n            newLeadRegion.push({\n              first: firstIntersect.first,\n              last: newMainRegion.first - 1,\n              isSpacer: true\n            });\n          } else {\n            newMainRegion.first = firstIntersect.first;\n          }\n        }\n        if (lastIntersect.last > newMainRegion.last) {\n          if (lastIntersect.isSpacer) {\n            newTailRegion.push({\n              first: newMainRegion.last + 1,\n              last: lastIntersect.last,\n              isSpacer: true\n            });\n          } else {\n            newMainRegion.last = lastIntersect.last;\n          }\n        }\n        var replacementRegions = [...newLeadRegion, newMainRegion, ...newTailRegion];\n        var numRegionsToDelete = lastIntersectIdx - firstIntersectIdx + 1;\n        this._regions.splice(firstIntersectIdx, numRegionsToDelete, ...replacementRegions);\n      }\n    }, {\n      key: \"numCells\",\n      value: function numCells() {\n        return this._numCells;\n      }\n    }, {\n      key: \"equals\",\n      value: function equals(other) {\n        return this._numCells === other._numCells && this._regions.length === other._regions.length && this._regions.every((region, i) => region.first === other._regions[i].first && region.last === other._regions[i].last && region.isSpacer === other._regions[i].isSpacer);\n      }\n    }, {\n      key: \"_findRegion\",\n      value: function _findRegion(cellIdx) {\n        var firstIdx = 0;\n        var lastIdx = this._regions.length - 1;\n        while (firstIdx <= lastIdx) {\n          var middleIdx = Math.floor((firstIdx + lastIdx) / 2);\n          var middleRegion = this._regions[middleIdx];\n          if (cellIdx >= middleRegion.first && cellIdx <= middleRegion.last) {\n            return [middleRegion, middleIdx];\n          } else if (cellIdx < middleRegion.first) {\n            lastIdx = middleIdx - 1;\n          } else if (cellIdx > middleRegion.last) {\n            firstIdx = middleIdx + 1;\n          }\n        }\n        (0, _invariant.default)(false, `A region was not found containing cellIdx ${cellIdx}`);\n      }\n    }]);\n  }();\n});", "lineCount": 111, "map": [[10, 2, 11, 0], [10, 6, 11, 0, "_invariant"], [10, 16, 11, 0], [10, 19, 11, 0, "_interopRequireDefault"], [10, 41, 11, 0], [10, 42, 11, 0, "require"], [10, 49, 11, 0], [10, 50, 11, 0, "_dependencyMap"], [10, 64, 11, 0], [11, 2, 11, 34], [11, 6, 19, 13, "CellRenderMask"], [11, 20, 19, 27], [11, 23, 19, 27, "exports"], [11, 30, 19, 27], [11, 31, 19, 27, "CellRenderMask"], [11, 45, 19, 27], [12, 4, 23, 2], [12, 13, 23, 2, "CellRenderMask"], [12, 28, 23, 14, "num<PERSON>ells"], [12, 36, 23, 30], [12, 38, 23, 32], [13, 6, 23, 32], [13, 10, 23, 32, "_classCallCheck2"], [13, 26, 23, 32], [13, 27, 23, 32, "default"], [13, 34, 23, 32], [13, 42, 23, 32, "CellRenderMask"], [13, 56, 23, 32], [14, 6, 24, 4], [14, 10, 24, 4, "invariant"], [14, 28, 24, 13], [14, 30, 25, 6, "num<PERSON>ells"], [14, 38, 25, 14], [14, 42, 25, 18], [14, 43, 25, 19], [14, 45, 26, 6], [14, 105, 27, 4], [14, 106, 27, 5], [15, 6, 29, 4], [15, 10, 29, 8], [15, 11, 29, 9, "_numCells"], [15, 20, 29, 18], [15, 23, 29, 21, "num<PERSON>ells"], [15, 31, 29, 29], [16, 6, 31, 4], [16, 10, 31, 8, "num<PERSON>ells"], [16, 18, 31, 16], [16, 23, 31, 21], [16, 24, 31, 22], [16, 26, 31, 24], [17, 8, 32, 6], [17, 12, 32, 10], [17, 13, 32, 11, "_regions"], [17, 21, 32, 19], [17, 24, 32, 22], [17, 26, 32, 24], [18, 6, 33, 4], [18, 7, 33, 5], [18, 13, 33, 11], [19, 8, 34, 6], [19, 12, 34, 10], [19, 13, 34, 11, "_regions"], [19, 21, 34, 19], [19, 24, 34, 22], [19, 25, 35, 8], [20, 10, 36, 10, "first"], [20, 15, 36, 15], [20, 17, 36, 17], [20, 18, 36, 18], [21, 10, 37, 10, "last"], [21, 14, 37, 14], [21, 16, 37, 16, "num<PERSON>ells"], [21, 24, 37, 24], [21, 27, 37, 27], [21, 28, 37, 28], [22, 10, 38, 10, "isSpacer"], [22, 18, 38, 18], [22, 20, 38, 20], [23, 8, 39, 8], [23, 9, 39, 9], [23, 10, 40, 7], [24, 6, 41, 4], [25, 4, 42, 2], [26, 4, 42, 3], [26, 15, 42, 3, "_createClass2"], [26, 28, 42, 3], [26, 29, 42, 3, "default"], [26, 36, 42, 3], [26, 38, 42, 3, "CellRenderMask"], [26, 52, 42, 3], [27, 6, 42, 3, "key"], [27, 9, 42, 3], [28, 6, 42, 3, "value"], [28, 11, 42, 3], [28, 13, 44, 2], [28, 22, 44, 2, "enumerateRegions"], [28, 38, 44, 18, "enumerateRegions"], [28, 39, 44, 18], [28, 41, 44, 49], [29, 8, 45, 4], [29, 15, 45, 11], [29, 19, 45, 15], [29, 20, 45, 16, "_regions"], [29, 28, 45, 24], [30, 6, 46, 2], [31, 4, 46, 3], [32, 6, 46, 3, "key"], [32, 9, 46, 3], [33, 6, 46, 3, "value"], [33, 11, 46, 3], [33, 13, 48, 2], [33, 22, 48, 2, "add<PERSON>ells"], [33, 30, 48, 10, "add<PERSON>ells"], [33, 31, 48, 11, "cells"], [33, 36, 48, 47], [33, 38, 48, 55], [34, 8, 49, 4], [34, 12, 49, 4, "invariant"], [34, 30, 49, 13], [34, 32, 50, 6, "cells"], [34, 37, 50, 11], [34, 38, 50, 12, "first"], [34, 43, 50, 17], [34, 47, 50, 21], [34, 48, 50, 22], [34, 52, 51, 8, "cells"], [34, 57, 51, 13], [34, 58, 51, 14, "first"], [34, 63, 51, 19], [34, 66, 51, 22], [34, 70, 51, 26], [34, 71, 51, 27, "_numCells"], [34, 80, 51, 36], [34, 84, 52, 8, "cells"], [34, 89, 52, 13], [34, 90, 52, 14, "last"], [34, 94, 52, 18], [34, 98, 52, 22], [34, 99, 52, 23], [34, 100, 52, 24], [34, 104, 53, 8, "cells"], [34, 109, 53, 13], [34, 110, 53, 14, "last"], [34, 114, 53, 18], [34, 117, 53, 21], [34, 121, 53, 25], [34, 122, 53, 26, "_numCells"], [34, 131, 53, 35], [34, 135, 54, 8, "cells"], [34, 140, 54, 13], [34, 141, 54, 14, "last"], [34, 145, 54, 18], [34, 149, 54, 22, "cells"], [34, 154, 54, 27], [34, 155, 54, 28, "first"], [34, 160, 54, 33], [34, 163, 54, 36], [34, 164, 54, 37], [34, 166, 55, 6], [34, 222, 56, 4], [34, 223, 56, 5], [35, 8, 60, 4], [35, 12, 60, 8, "cells"], [35, 17, 60, 13], [35, 18, 60, 14, "last"], [35, 22, 60, 18], [35, 25, 60, 21, "cells"], [35, 30, 60, 26], [35, 31, 60, 27, "first"], [35, 36, 60, 32], [35, 38, 60, 34], [36, 10, 61, 6], [37, 8, 62, 4], [38, 8, 64, 4], [38, 12, 64, 4, "_this$_findRegion"], [38, 29, 64, 4], [38, 32, 64, 48], [38, 36, 64, 52], [38, 37, 64, 53, "_findRegion"], [38, 48, 64, 64], [38, 49, 64, 65, "cells"], [38, 54, 64, 70], [38, 55, 64, 71, "first"], [38, 60, 64, 76], [38, 61, 64, 77], [39, 10, 64, 77, "_this$_findRegion2"], [39, 28, 64, 77], [39, 35, 64, 77, "_slicedToArray2"], [39, 50, 64, 77], [39, 51, 64, 77, "default"], [39, 58, 64, 77], [39, 60, 64, 77, "_this$_findRegion"], [39, 77, 64, 77], [40, 10, 64, 11, "firstIntersect"], [40, 24, 64, 25], [40, 27, 64, 25, "_this$_findRegion2"], [40, 45, 64, 25], [41, 10, 64, 27, "firstIntersectIdx"], [41, 27, 64, 44], [41, 30, 64, 44, "_this$_findRegion2"], [41, 48, 64, 44], [42, 8, 65, 4], [42, 12, 65, 4, "_this$_findRegion3"], [42, 30, 65, 4], [42, 33, 65, 46], [42, 37, 65, 50], [42, 38, 65, 51, "_findRegion"], [42, 49, 65, 62], [42, 50, 65, 63, "cells"], [42, 55, 65, 68], [42, 56, 65, 69, "last"], [42, 60, 65, 73], [42, 61, 65, 74], [43, 10, 65, 74, "_this$_findRegion4"], [43, 28, 65, 74], [43, 35, 65, 74, "_slicedToArray2"], [43, 50, 65, 74], [43, 51, 65, 74, "default"], [43, 58, 65, 74], [43, 60, 65, 74, "_this$_findRegion3"], [43, 78, 65, 74], [44, 10, 65, 11, "lastIntersect"], [44, 23, 65, 24], [44, 26, 65, 24, "_this$_findRegion4"], [44, 44, 65, 24], [45, 10, 65, 26, "lastIntersectIdx"], [45, 26, 65, 42], [45, 29, 65, 42, "_this$_findRegion4"], [45, 47, 65, 42], [46, 8, 69, 4], [46, 12, 69, 8, "firstIntersectIdx"], [46, 29, 69, 25], [46, 34, 69, 30, "lastIntersectIdx"], [46, 50, 69, 46], [46, 54, 69, 50], [46, 55, 69, 51, "firstIntersect"], [46, 69, 69, 65], [46, 70, 69, 66, "isSpacer"], [46, 78, 69, 74], [46, 80, 69, 76], [47, 10, 70, 6], [48, 8, 71, 4], [49, 8, 75, 4], [49, 12, 75, 10, "newLeadRegion"], [49, 25, 75, 42], [49, 28, 75, 45], [49, 30, 75, 47], [50, 8, 76, 4], [50, 12, 76, 10, "newTailRegion"], [50, 25, 76, 42], [50, 28, 76, 45], [50, 30, 76, 47], [51, 8, 77, 4], [51, 12, 77, 10, "newMainRegion"], [51, 25, 77, 35], [51, 28, 77, 38], [52, 10, 78, 6], [52, 13, 78, 9, "cells"], [52, 18, 78, 14], [53, 10, 79, 6, "isSpacer"], [53, 18, 79, 14], [53, 20, 79, 16], [54, 8, 80, 4], [54, 9, 80, 5], [55, 8, 82, 4], [55, 12, 82, 8, "firstIntersect"], [55, 26, 82, 22], [55, 27, 82, 23, "first"], [55, 32, 82, 28], [55, 35, 82, 31, "newMainRegion"], [55, 48, 82, 44], [55, 49, 82, 45, "first"], [55, 54, 82, 50], [55, 56, 82, 52], [56, 10, 83, 6], [56, 14, 83, 10, "firstIntersect"], [56, 28, 83, 24], [56, 29, 83, 25, "isSpacer"], [56, 37, 83, 33], [56, 39, 83, 35], [57, 12, 84, 8, "newLeadRegion"], [57, 25, 84, 21], [57, 26, 84, 22, "push"], [57, 30, 84, 26], [57, 31, 84, 27], [58, 14, 85, 10, "first"], [58, 19, 85, 15], [58, 21, 85, 17, "firstIntersect"], [58, 35, 85, 31], [58, 36, 85, 32, "first"], [58, 41, 85, 37], [59, 14, 86, 10, "last"], [59, 18, 86, 14], [59, 20, 86, 16, "newMainRegion"], [59, 33, 86, 29], [59, 34, 86, 30, "first"], [59, 39, 86, 35], [59, 42, 86, 38], [59, 43, 86, 39], [60, 14, 87, 10, "isSpacer"], [60, 22, 87, 18], [60, 24, 87, 20], [61, 12, 88, 8], [61, 13, 88, 9], [61, 14, 88, 10], [62, 10, 89, 6], [62, 11, 89, 7], [62, 17, 89, 13], [63, 12, 90, 8, "newMainRegion"], [63, 25, 90, 21], [63, 26, 90, 22, "first"], [63, 31, 90, 27], [63, 34, 90, 30, "firstIntersect"], [63, 48, 90, 44], [63, 49, 90, 45, "first"], [63, 54, 90, 50], [64, 10, 91, 6], [65, 8, 92, 4], [66, 8, 94, 4], [66, 12, 94, 8, "lastIntersect"], [66, 25, 94, 21], [66, 26, 94, 22, "last"], [66, 30, 94, 26], [66, 33, 94, 29, "newMainRegion"], [66, 46, 94, 42], [66, 47, 94, 43, "last"], [66, 51, 94, 47], [66, 53, 94, 49], [67, 10, 95, 6], [67, 14, 95, 10, "lastIntersect"], [67, 27, 95, 23], [67, 28, 95, 24, "isSpacer"], [67, 36, 95, 32], [67, 38, 95, 34], [68, 12, 96, 8, "newTailRegion"], [68, 25, 96, 21], [68, 26, 96, 22, "push"], [68, 30, 96, 26], [68, 31, 96, 27], [69, 14, 97, 10, "first"], [69, 19, 97, 15], [69, 21, 97, 17, "newMainRegion"], [69, 34, 97, 30], [69, 35, 97, 31, "last"], [69, 39, 97, 35], [69, 42, 97, 38], [69, 43, 97, 39], [70, 14, 98, 10, "last"], [70, 18, 98, 14], [70, 20, 98, 16, "lastIntersect"], [70, 33, 98, 29], [70, 34, 98, 30, "last"], [70, 38, 98, 34], [71, 14, 99, 10, "isSpacer"], [71, 22, 99, 18], [71, 24, 99, 20], [72, 12, 100, 8], [72, 13, 100, 9], [72, 14, 100, 10], [73, 10, 101, 6], [73, 11, 101, 7], [73, 17, 101, 13], [74, 12, 102, 8, "newMainRegion"], [74, 25, 102, 21], [74, 26, 102, 22, "last"], [74, 30, 102, 26], [74, 33, 102, 29, "lastIntersect"], [74, 46, 102, 42], [74, 47, 102, 43, "last"], [74, 51, 102, 47], [75, 10, 103, 6], [76, 8, 104, 4], [77, 8, 106, 4], [77, 12, 106, 10, "replacementRegions"], [77, 30, 106, 47], [77, 33, 106, 50], [77, 34, 107, 6], [77, 37, 107, 9, "newLeadRegion"], [77, 50, 107, 22], [77, 52, 108, 6, "newMainRegion"], [77, 65, 108, 19], [77, 67, 109, 6], [77, 70, 109, 9, "newTailRegion"], [77, 83, 109, 22], [77, 84, 110, 5], [78, 8, 111, 4], [78, 12, 111, 10, "numRegionsToDelete"], [78, 30, 111, 28], [78, 33, 111, 31, "lastIntersectIdx"], [78, 49, 111, 47], [78, 52, 111, 50, "firstIntersectIdx"], [78, 69, 111, 67], [78, 72, 111, 70], [78, 73, 111, 71], [79, 8, 112, 4], [79, 12, 112, 8], [79, 13, 112, 9, "_regions"], [79, 21, 112, 17], [79, 22, 112, 18, "splice"], [79, 28, 112, 24], [79, 29, 113, 6, "firstIntersectIdx"], [79, 46, 113, 23], [79, 48, 114, 6, "numRegionsToDelete"], [79, 66, 114, 24], [79, 68, 115, 6], [79, 71, 115, 9, "replacementRegions"], [79, 89, 116, 4], [79, 90, 116, 5], [80, 6, 117, 2], [81, 4, 117, 3], [82, 6, 117, 3, "key"], [82, 9, 117, 3], [83, 6, 117, 3, "value"], [83, 11, 117, 3], [83, 13, 119, 2], [83, 22, 119, 2, "num<PERSON>ells"], [83, 30, 119, 10, "num<PERSON>ells"], [83, 31, 119, 10], [83, 33, 119, 21], [84, 8, 120, 4], [84, 15, 120, 11], [84, 19, 120, 15], [84, 20, 120, 16, "_numCells"], [84, 29, 120, 25], [85, 6, 121, 2], [86, 4, 121, 3], [87, 6, 121, 3, "key"], [87, 9, 121, 3], [88, 6, 121, 3, "value"], [88, 11, 121, 3], [88, 13, 123, 2], [88, 22, 123, 2, "equals"], [88, 28, 123, 8, "equals"], [88, 29, 123, 9, "other"], [88, 34, 123, 30], [88, 36, 123, 41], [89, 8, 124, 4], [89, 15, 125, 6], [89, 19, 125, 10], [89, 20, 125, 11, "_numCells"], [89, 29, 125, 20], [89, 34, 125, 25, "other"], [89, 39, 125, 30], [89, 40, 125, 31, "_numCells"], [89, 49, 125, 40], [89, 53, 126, 6], [89, 57, 126, 10], [89, 58, 126, 11, "_regions"], [89, 66, 126, 19], [89, 67, 126, 20, "length"], [89, 73, 126, 26], [89, 78, 126, 31, "other"], [89, 83, 126, 36], [89, 84, 126, 37, "_regions"], [89, 92, 126, 45], [89, 93, 126, 46, "length"], [89, 99, 126, 52], [89, 103, 127, 6], [89, 107, 127, 10], [89, 108, 127, 11, "_regions"], [89, 116, 127, 19], [89, 117, 127, 20, "every"], [89, 122, 127, 25], [89, 123, 128, 8], [89, 124, 128, 9, "region"], [89, 130, 128, 15], [89, 132, 128, 17, "i"], [89, 133, 128, 18], [89, 138, 129, 10, "region"], [89, 144, 129, 16], [89, 145, 129, 17, "first"], [89, 150, 129, 22], [89, 155, 129, 27, "other"], [89, 160, 129, 32], [89, 161, 129, 33, "_regions"], [89, 169, 129, 41], [89, 170, 129, 42, "i"], [89, 171, 129, 43], [89, 172, 129, 44], [89, 173, 129, 45, "first"], [89, 178, 129, 50], [89, 182, 130, 10, "region"], [89, 188, 130, 16], [89, 189, 130, 17, "last"], [89, 193, 130, 21], [89, 198, 130, 26, "other"], [89, 203, 130, 31], [89, 204, 130, 32, "_regions"], [89, 212, 130, 40], [89, 213, 130, 41, "i"], [89, 214, 130, 42], [89, 215, 130, 43], [89, 216, 130, 44, "last"], [89, 220, 130, 48], [89, 224, 131, 10, "region"], [89, 230, 131, 16], [89, 231, 131, 17, "isSpacer"], [89, 239, 131, 25], [89, 244, 131, 30, "other"], [89, 249, 131, 35], [89, 250, 131, 36, "_regions"], [89, 258, 131, 44], [89, 259, 131, 45, "i"], [89, 260, 131, 46], [89, 261, 131, 47], [89, 262, 131, 48, "isSpacer"], [89, 270, 132, 6], [89, 271, 132, 7], [90, 6, 134, 2], [91, 4, 134, 3], [92, 6, 134, 3, "key"], [92, 9, 134, 3], [93, 6, 134, 3, "value"], [93, 11, 134, 3], [93, 13, 136, 2], [93, 22, 136, 2, "_findRegion"], [93, 33, 136, 13, "_findRegion"], [93, 34, 136, 14, "cellIdx"], [93, 41, 136, 29], [93, 43, 136, 53], [94, 8, 137, 4], [94, 12, 137, 8, "firstIdx"], [94, 20, 137, 16], [94, 23, 137, 19], [94, 24, 137, 20], [95, 8, 138, 4], [95, 12, 138, 8, "lastIdx"], [95, 19, 138, 15], [95, 22, 138, 18], [95, 26, 138, 22], [95, 27, 138, 23, "_regions"], [95, 35, 138, 31], [95, 36, 138, 32, "length"], [95, 42, 138, 38], [95, 45, 138, 41], [95, 46, 138, 42], [96, 8, 140, 4], [96, 15, 140, 11, "firstIdx"], [96, 23, 140, 19], [96, 27, 140, 23, "lastIdx"], [96, 34, 140, 30], [96, 36, 140, 32], [97, 10, 141, 6], [97, 14, 141, 12, "middleIdx"], [97, 23, 141, 21], [97, 26, 141, 24, "Math"], [97, 30, 141, 28], [97, 31, 141, 29, "floor"], [97, 36, 141, 34], [97, 37, 141, 35], [97, 38, 141, 36, "firstIdx"], [97, 46, 141, 44], [97, 49, 141, 47, "lastIdx"], [97, 56, 141, 54], [97, 60, 141, 58], [97, 61, 141, 59], [97, 62, 141, 60], [98, 10, 142, 6], [98, 14, 142, 12, "middleRegion"], [98, 26, 142, 24], [98, 29, 142, 27], [98, 33, 142, 31], [98, 34, 142, 32, "_regions"], [98, 42, 142, 40], [98, 43, 142, 41, "middleIdx"], [98, 52, 142, 50], [98, 53, 142, 51], [99, 10, 144, 6], [99, 14, 144, 10, "cellIdx"], [99, 21, 144, 17], [99, 25, 144, 21, "middleRegion"], [99, 37, 144, 33], [99, 38, 144, 34, "first"], [99, 43, 144, 39], [99, 47, 144, 43, "cellIdx"], [99, 54, 144, 50], [99, 58, 144, 54, "middleRegion"], [99, 70, 144, 66], [99, 71, 144, 67, "last"], [99, 75, 144, 71], [99, 77, 144, 73], [100, 12, 145, 8], [100, 19, 145, 15], [100, 20, 145, 16, "middleRegion"], [100, 32, 145, 28], [100, 34, 145, 30, "middleIdx"], [100, 43, 145, 39], [100, 44, 145, 40], [101, 10, 146, 6], [101, 11, 146, 7], [101, 17, 146, 13], [101, 21, 146, 17, "cellIdx"], [101, 28, 146, 24], [101, 31, 146, 27, "middleRegion"], [101, 43, 146, 39], [101, 44, 146, 40, "first"], [101, 49, 146, 45], [101, 51, 146, 47], [102, 12, 147, 8, "lastIdx"], [102, 19, 147, 15], [102, 22, 147, 18, "middleIdx"], [102, 31, 147, 27], [102, 34, 147, 30], [102, 35, 147, 31], [103, 10, 148, 6], [103, 11, 148, 7], [103, 17, 148, 13], [103, 21, 148, 17, "cellIdx"], [103, 28, 148, 24], [103, 31, 148, 27, "middleRegion"], [103, 43, 148, 39], [103, 44, 148, 40, "last"], [103, 48, 148, 44], [103, 50, 148, 46], [104, 12, 149, 8, "firstIdx"], [104, 20, 149, 16], [104, 23, 149, 19, "middleIdx"], [104, 32, 149, 28], [104, 35, 149, 31], [104, 36, 149, 32], [105, 10, 150, 6], [106, 8, 151, 4], [107, 8, 153, 4], [107, 12, 153, 4, "invariant"], [107, 30, 153, 13], [107, 32, 153, 14], [107, 37, 153, 19], [107, 39, 153, 21], [107, 84, 153, 66, "cellIdx"], [107, 91, 153, 73], [107, 93, 153, 75], [107, 94, 153, 76], [108, 6, 154, 2], [109, 4, 154, 3], [110, 2, 154, 3], [111, 0, 154, 3], [111, 3]], "functionMap": {"names": ["<global>", "CellRenderMask", "constructor", "enumerateRegions", "add<PERSON>ells", "num<PERSON>ells", "equals", "_regions.every$argument_0", "_findRegion"], "mappings": "AAA;OCkB;ECI;GDmB;EEE;GFE;EGE;GHqE;EIE;GJE;EKE;QCK;wDDG;GLG;EOE;GPkB"}}, "type": "js/module"}]}