{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectDestructuringEmpty", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JWgwp1AYM3rN4k6Vft1eP0TCU4I=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 16, "index": 351}, "end": {"line": 6, "column": 32, "index": 367}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "../../shared", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 17, "index": 386}, "end": {"line": 7, "column": 40, "index": 409}}], "key": "nGuj9FEtRBU67xZ2eTMaA3OncKU=", "exportNames": ["*"]}}, {"name": "../config", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 17, "index": 428}, "end": {"line": 8, "column": 37, "index": 448}}], "key": "ZCEQutz0TJPAIQVhAEtbqc1Jeaw=", "exportNames": ["*"]}}, {"name": "../observable", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 21, "index": 471}, "end": {"line": 9, "column": 45, "index": 495}}], "key": "Wp4whP3mc8t6X+fLKju5VuvTTrc=", "exportNames": ["*"]}}, {"name": "./appearance-observables", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 33, "index": 530}, "end": {"line": 10, "column": 68, "index": 565}}, {"start": {"line": 16, "column": 31, "index": 916}, "end": {"line": 16, "column": 66, "index": 951}}], "key": "s1t6Lv1gdu7frQGd56my8mMjrlc=", "exportNames": ["*"]}}, {"name": "./native-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 25, "index": 592}, "end": {"line": 11, "column": 52, "index": 619}}], "key": "ucGcEuaY0zOUZSv/AaUwIKFft4M=", "exportNames": ["*"]}}, {"name": "./styles", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 17, "index": 638}, "end": {"line": 12, "column": 36, "index": 657}}], "key": "TIj/WdIC6e9M3OcCVCdkL3+mheQ=", "exportNames": ["*"]}}, {"name": "./unwrap-components", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 28, "index": 687}, "end": {"line": 13, "column": 58, "index": 717}}], "key": "bdq+fRK7IQ5pNEoeZHbIIjiQTjE=", "exportNames": ["*"]}}, {"name": "./stylesheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 19, "index": 738}, "end": {"line": 14, "column": 42, "index": 761}}], "key": "+988CTNoUB9Xq0hySmlSzwRp6IU=", "exportNames": ["*"]}}, {"name": "./unit-observables", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 25, "index": 1114}, "end": {"line": 18, "column": 54, "index": 1143}}], "key": "osbgIlV1a8LBVQYH1R+DJV+8OhU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  var _objectDestructuringEmpty = require(_dependencyMap[1], \"@babel/runtime/helpers/objectDestructuringEmpty\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useSafeAreaEnv = exports.useUnstableNativeVariable = exports.remapProps = exports.cssInterop = exports.interopComponents = exports.rem = exports.colorScheme = exports.StyleSheet = void 0;\n  exports.useColorScheme = useColorScheme;\n  exports.vars = vars;\n  var react_1 = require(_dependencyMap[2], \"react\");\n  var shared_1 = require(_dependencyMap[3], \"../../shared\");\n  var config_1 = require(_dependencyMap[4], \"../config\");\n  var observable_1 = require(_dependencyMap[5], \"../observable\");\n  var appearance_observables_1 = require(_dependencyMap[6], \"./appearance-observables\");\n  var native_interop_1 = require(_dependencyMap[7], \"./native-interop\");\n  var styles_1 = require(_dependencyMap[8], \"./styles\");\n  var unwrap_components_1 = require(_dependencyMap[9], \"./unwrap-components\");\n  var stylesheet_1 = require(_dependencyMap[10], \"./stylesheet\");\n  Object.defineProperty(exports, \"StyleSheet\", {\n    enumerable: true,\n    get: function () {\n      return stylesheet_1.StyleSheet;\n    }\n  });\n  var appearance_observables_2 = require(_dependencyMap[6], \"./appearance-observables\");\n  Object.defineProperty(exports, \"colorScheme\", {\n    enumerable: true,\n    get: function () {\n      return appearance_observables_2.colorScheme;\n    }\n  });\n  var unit_observables_1 = require(_dependencyMap[11], \"./unit-observables\");\n  Object.defineProperty(exports, \"rem\", {\n    enumerable: true,\n    get: function () {\n      return unit_observables_1.rem;\n    }\n  });\n  exports.interopComponents = new Map();\n  var cssInterop = (baseComponent, mapping) => {\n    var configs = (0, config_1.getNormalizeConfig)(mapping);\n    var component;\n    var type = (0, unwrap_components_1.getComponentType)(baseComponent);\n    if (type === \"function\") {\n      component = props => {\n        return (0, native_interop_1.interop)(baseComponent, configs, props, undefined);\n      };\n    } else {\n      component = (0, react_1.forwardRef)((props, ref) => {\n        return (0, native_interop_1.interop)(baseComponent, configs, props, ref);\n      });\n    }\n    var name = baseComponent.displayName ?? baseComponent.name ?? \"unknown\";\n    component.displayName = `CssInterop.${name}`;\n    exports.interopComponents.set(baseComponent, component);\n    return component;\n  };\n  exports.cssInterop = cssInterop;\n  var remapProps = (component, mapping) => {\n    var configs = (0, config_1.getNormalizeConfig)(mapping);\n    var interopComponent = (0, react_1.forwardRef)(function RemapPropsComponent(_ref, ref) {\n      var props = Object.assign({}, (_objectDestructuringEmpty(_ref), _ref));\n      for (var config of configs) {\n        var source = props?.[config.source];\n        if (typeof source !== \"string\" || !source) continue;\n        var placeholder = {\n          [shared_1.PLACEHOLDER_SYMBOL]: true\n        };\n        styles_1.opaqueStyles.set(placeholder, {\n          [shared_1.StyleRuleSetSymbol]: \"RemappedClassName\",\n          classNames: source.split(/\\s+/)\n        });\n        delete props[config.source];\n        (0, shared_1.assignToTarget)(props, placeholder, config, {\n          objectMergeStyle: \"toArray\"\n        });\n      }\n      props.ref = ref;\n      return (0, react_1.createElement)(component, props, props.children);\n    });\n    exports.interopComponents.set(component, interopComponent);\n    return interopComponent;\n  };\n  exports.remapProps = remapProps;\n  function useColorScheme() {\n    var _ref2 = (0, react_1.useState)(() => ({\n        run: () => setEffect(s => ({\n          ...s\n        })),\n        dependencies: new Set()\n      })),\n      _ref3 = _slicedToArray(_ref2, 2),\n      effect = _ref3[0],\n      setEffect = _ref3[1];\n    (0, observable_1.cleanupEffect)(effect);\n    return {\n      colorScheme: appearance_observables_1.colorScheme.get(effect),\n      setColorScheme: appearance_observables_1.colorScheme.set,\n      toggleColorScheme: appearance_observables_1.colorScheme.toggle\n    };\n  }\n  function vars(variables) {\n    var style = {};\n    styles_1.opaqueStyles.set(style, {\n      [shared_1.StyleRuleSetSymbol]: true,\n      variables: true,\n      n: [{\n        [shared_1.StyleRuleSymbol]: true,\n        s: shared_1.inlineSpecificity,\n        variables: Object.entries(variables).map(_ref4 => {\n          var _ref5 = _slicedToArray(_ref4, 2),\n            name = _ref5[0],\n            value = _ref5[1];\n          return [name.startsWith(\"--\") ? name : `--${name}`, value];\n        })\n      }]\n    });\n    return style;\n  }\n  var useUnstableNativeVariable = name => {\n    var context = (0, react_1.useContext)(styles_1.VariableContext);\n    var _ref6 = (0, react_1.useState)(() => ({\n        run: () => setState(s => ({\n          ...s\n        })),\n        dependencies: new Set()\n      })),\n      _ref7 = _slicedToArray(_ref6, 2),\n      effect = _ref7[0],\n      setState = _ref7[1];\n    var value = (0, styles_1.getVariable)(name, context, effect);\n    if (typeof value === \"object\" && \"get\" in value) {\n      (0, observable_1.cleanupEffect)(effect);\n      value = value.get(effect);\n    }\n    return value;\n  };\n  exports.useUnstableNativeVariable = useUnstableNativeVariable;\n  var useSafeAreaEnv = () => {\n    console.warn(\"useSafeAreaEnv() is deprecated. Please use <SafeAreaProvider /> directly\");\n  };\n  exports.useSafeAreaEnv = useSafeAreaEnv;\n});", "lineCount": 145, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_slicedToArray"], [4, 20, 1, 13], [4, 23, 1, 13, "require"], [4, 30, 1, 13], [4, 31, 1, 13, "_dependencyMap"], [4, 45, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_objectDestructuringEmpty"], [5, 31, 1, 13], [5, 34, 1, 13, "require"], [5, 41, 1, 13], [5, 42, 1, 13, "_dependencyMap"], [5, 56, 1, 13], [6, 2, 2, 0, "Object"], [6, 8, 2, 6], [6, 9, 2, 7, "defineProperty"], [6, 23, 2, 21], [6, 24, 2, 22, "exports"], [6, 31, 2, 29], [6, 33, 2, 31], [6, 45, 2, 43], [6, 47, 2, 45], [7, 4, 2, 47, "value"], [7, 9, 2, 52], [7, 11, 2, 54], [8, 2, 2, 59], [8, 3, 2, 60], [8, 4, 2, 61], [9, 2, 3, 0, "exports"], [9, 9, 3, 7], [9, 10, 3, 8, "useSafeAreaEnv"], [9, 24, 3, 22], [9, 27, 3, 25, "exports"], [9, 34, 3, 32], [9, 35, 3, 33, "useUnstableNativeVariable"], [9, 60, 3, 58], [9, 63, 3, 61, "exports"], [9, 70, 3, 68], [9, 71, 3, 69, "remapProps"], [9, 81, 3, 79], [9, 84, 3, 82, "exports"], [9, 91, 3, 89], [9, 92, 3, 90, "cssInterop"], [9, 102, 3, 100], [9, 105, 3, 103, "exports"], [9, 112, 3, 110], [9, 113, 3, 111, "interopComponents"], [9, 130, 3, 128], [9, 133, 3, 131, "exports"], [9, 140, 3, 138], [9, 141, 3, 139, "rem"], [9, 144, 3, 142], [9, 147, 3, 145, "exports"], [9, 154, 3, 152], [9, 155, 3, 153, "colorScheme"], [9, 166, 3, 164], [9, 169, 3, 167, "exports"], [9, 176, 3, 174], [9, 177, 3, 175, "StyleSheet"], [9, 187, 3, 185], [9, 190, 3, 188], [9, 195, 3, 193], [9, 196, 3, 194], [10, 2, 4, 0, "exports"], [10, 9, 4, 7], [10, 10, 4, 8, "useColorScheme"], [10, 24, 4, 22], [10, 27, 4, 25, "useColorScheme"], [10, 41, 4, 39], [11, 2, 5, 0, "exports"], [11, 9, 5, 7], [11, 10, 5, 8, "vars"], [11, 14, 5, 12], [11, 17, 5, 15, "vars"], [11, 21, 5, 19], [12, 2, 6, 0], [12, 6, 6, 6, "react_1"], [12, 13, 6, 13], [12, 16, 6, 16, "require"], [12, 23, 6, 23], [12, 24, 6, 23, "_dependencyMap"], [12, 38, 6, 23], [12, 50, 6, 31], [12, 51, 6, 32], [13, 2, 7, 0], [13, 6, 7, 6, "shared_1"], [13, 14, 7, 14], [13, 17, 7, 17, "require"], [13, 24, 7, 24], [13, 25, 7, 24, "_dependencyMap"], [13, 39, 7, 24], [13, 58, 7, 39], [13, 59, 7, 40], [14, 2, 8, 0], [14, 6, 8, 6, "config_1"], [14, 14, 8, 14], [14, 17, 8, 17, "require"], [14, 24, 8, 24], [14, 25, 8, 24, "_dependencyMap"], [14, 39, 8, 24], [14, 55, 8, 36], [14, 56, 8, 37], [15, 2, 9, 0], [15, 6, 9, 6, "observable_1"], [15, 18, 9, 18], [15, 21, 9, 21, "require"], [15, 28, 9, 28], [15, 29, 9, 28, "_dependencyMap"], [15, 43, 9, 28], [15, 63, 9, 44], [15, 64, 9, 45], [16, 2, 10, 0], [16, 6, 10, 6, "appearance_observables_1"], [16, 30, 10, 30], [16, 33, 10, 33, "require"], [16, 40, 10, 40], [16, 41, 10, 40, "_dependencyMap"], [16, 55, 10, 40], [16, 86, 10, 67], [16, 87, 10, 68], [17, 2, 11, 0], [17, 6, 11, 6, "native_interop_1"], [17, 22, 11, 22], [17, 25, 11, 25, "require"], [17, 32, 11, 32], [17, 33, 11, 32, "_dependencyMap"], [17, 47, 11, 32], [17, 70, 11, 51], [17, 71, 11, 52], [18, 2, 12, 0], [18, 6, 12, 6, "styles_1"], [18, 14, 12, 14], [18, 17, 12, 17, "require"], [18, 24, 12, 24], [18, 25, 12, 24, "_dependencyMap"], [18, 39, 12, 24], [18, 54, 12, 35], [18, 55, 12, 36], [19, 2, 13, 0], [19, 6, 13, 6, "unwrap_components_1"], [19, 25, 13, 25], [19, 28, 13, 28, "require"], [19, 35, 13, 35], [19, 36, 13, 35, "_dependencyMap"], [19, 50, 13, 35], [19, 76, 13, 57], [19, 77, 13, 58], [20, 2, 14, 0], [20, 6, 14, 4, "stylesheet_1"], [20, 18, 14, 16], [20, 21, 14, 19, "require"], [20, 28, 14, 26], [20, 29, 14, 26, "_dependencyMap"], [20, 43, 14, 26], [20, 63, 14, 41], [20, 64, 14, 42], [21, 2, 15, 0, "Object"], [21, 8, 15, 6], [21, 9, 15, 7, "defineProperty"], [21, 23, 15, 21], [21, 24, 15, 22, "exports"], [21, 31, 15, 29], [21, 33, 15, 31], [21, 45, 15, 43], [21, 47, 15, 45], [22, 4, 15, 47, "enumerable"], [22, 14, 15, 57], [22, 16, 15, 59], [22, 20, 15, 63], [23, 4, 15, 65, "get"], [23, 7, 15, 68], [23, 9, 15, 70], [23, 18, 15, 70, "get"], [23, 19, 15, 70], [23, 21, 15, 82], [24, 6, 15, 84], [24, 13, 15, 91, "stylesheet_1"], [24, 25, 15, 103], [24, 26, 15, 104, "StyleSheet"], [24, 36, 15, 114], [25, 4, 15, 116], [26, 2, 15, 118], [26, 3, 15, 119], [26, 4, 15, 120], [27, 2, 16, 0], [27, 6, 16, 4, "appearance_observables_2"], [27, 30, 16, 28], [27, 33, 16, 31, "require"], [27, 40, 16, 38], [27, 41, 16, 38, "_dependencyMap"], [27, 55, 16, 38], [27, 86, 16, 65], [27, 87, 16, 66], [28, 2, 17, 0, "Object"], [28, 8, 17, 6], [28, 9, 17, 7, "defineProperty"], [28, 23, 17, 21], [28, 24, 17, 22, "exports"], [28, 31, 17, 29], [28, 33, 17, 31], [28, 46, 17, 44], [28, 48, 17, 46], [29, 4, 17, 48, "enumerable"], [29, 14, 17, 58], [29, 16, 17, 60], [29, 20, 17, 64], [30, 4, 17, 66, "get"], [30, 7, 17, 69], [30, 9, 17, 71], [30, 18, 17, 71, "get"], [30, 19, 17, 71], [30, 21, 17, 83], [31, 6, 17, 85], [31, 13, 17, 92, "appearance_observables_2"], [31, 37, 17, 116], [31, 38, 17, 117, "colorScheme"], [31, 49, 17, 128], [32, 4, 17, 130], [33, 2, 17, 132], [33, 3, 17, 133], [33, 4, 17, 134], [34, 2, 18, 0], [34, 6, 18, 4, "unit_observables_1"], [34, 24, 18, 22], [34, 27, 18, 25, "require"], [34, 34, 18, 32], [34, 35, 18, 32, "_dependencyMap"], [34, 49, 18, 32], [34, 75, 18, 53], [34, 76, 18, 54], [35, 2, 19, 0, "Object"], [35, 8, 19, 6], [35, 9, 19, 7, "defineProperty"], [35, 23, 19, 21], [35, 24, 19, 22, "exports"], [35, 31, 19, 29], [35, 33, 19, 31], [35, 38, 19, 36], [35, 40, 19, 38], [36, 4, 19, 40, "enumerable"], [36, 14, 19, 50], [36, 16, 19, 52], [36, 20, 19, 56], [37, 4, 19, 58, "get"], [37, 7, 19, 61], [37, 9, 19, 63], [37, 18, 19, 63, "get"], [37, 19, 19, 63], [37, 21, 19, 75], [38, 6, 19, 77], [38, 13, 19, 84, "unit_observables_1"], [38, 31, 19, 102], [38, 32, 19, 103, "rem"], [38, 35, 19, 106], [39, 4, 19, 108], [40, 2, 19, 110], [40, 3, 19, 111], [40, 4, 19, 112], [41, 2, 20, 0, "exports"], [41, 9, 20, 7], [41, 10, 20, 8, "interopComponents"], [41, 27, 20, 25], [41, 30, 20, 28], [41, 34, 20, 32, "Map"], [41, 37, 20, 35], [41, 38, 20, 36], [41, 39, 20, 37], [42, 2, 21, 0], [42, 6, 21, 6, "cssInterop"], [42, 16, 21, 16], [42, 19, 21, 19, "cssInterop"], [42, 20, 21, 20, "baseComponent"], [42, 33, 21, 33], [42, 35, 21, 35, "mapping"], [42, 42, 21, 42], [42, 47, 21, 47], [43, 4, 22, 4], [43, 8, 22, 10, "configs"], [43, 15, 22, 17], [43, 18, 22, 20], [43, 19, 22, 21], [43, 20, 22, 22], [43, 22, 22, 24, "config_1"], [43, 30, 22, 32], [43, 31, 22, 33, "getNormalizeConfig"], [43, 49, 22, 51], [43, 51, 22, 53, "mapping"], [43, 58, 22, 60], [43, 59, 22, 61], [44, 4, 23, 4], [44, 8, 23, 8, "component"], [44, 17, 23, 17], [45, 4, 24, 4], [45, 8, 24, 10, "type"], [45, 12, 24, 14], [45, 15, 24, 17], [45, 16, 24, 18], [45, 17, 24, 19], [45, 19, 24, 21, "unwrap_components_1"], [45, 38, 24, 40], [45, 39, 24, 41, "getComponentType"], [45, 55, 24, 57], [45, 57, 24, 59, "baseComponent"], [45, 70, 24, 72], [45, 71, 24, 73], [46, 4, 25, 4], [46, 8, 25, 8, "type"], [46, 12, 25, 12], [46, 17, 25, 17], [46, 27, 25, 27], [46, 29, 25, 29], [47, 6, 26, 8, "component"], [47, 15, 26, 17], [47, 18, 26, 21, "props"], [47, 23, 26, 26], [47, 27, 26, 31], [48, 8, 27, 12], [48, 15, 27, 19], [48, 16, 27, 20], [48, 17, 27, 21], [48, 19, 27, 23, "native_interop_1"], [48, 35, 27, 39], [48, 36, 27, 40, "interop"], [48, 43, 27, 47], [48, 45, 27, 49, "baseComponent"], [48, 58, 27, 62], [48, 60, 27, 64, "configs"], [48, 67, 27, 71], [48, 69, 27, 73, "props"], [48, 74, 27, 78], [48, 76, 27, 80, "undefined"], [48, 85, 27, 89], [48, 86, 27, 90], [49, 6, 28, 8], [49, 7, 28, 9], [50, 4, 29, 4], [50, 5, 29, 5], [50, 11, 30, 9], [51, 6, 31, 8, "component"], [51, 15, 31, 17], [51, 18, 31, 20], [51, 19, 31, 21], [51, 20, 31, 22], [51, 22, 31, 24, "react_1"], [51, 29, 31, 31], [51, 30, 31, 32, "forwardRef"], [51, 40, 31, 42], [51, 42, 31, 44], [51, 43, 31, 45, "props"], [51, 48, 31, 50], [51, 50, 31, 52, "ref"], [51, 53, 31, 55], [51, 58, 31, 60], [52, 8, 32, 12], [52, 15, 32, 19], [52, 16, 32, 20], [52, 17, 32, 21], [52, 19, 32, 23, "native_interop_1"], [52, 35, 32, 39], [52, 36, 32, 40, "interop"], [52, 43, 32, 47], [52, 45, 32, 49, "baseComponent"], [52, 58, 32, 62], [52, 60, 32, 64, "configs"], [52, 67, 32, 71], [52, 69, 32, 73, "props"], [52, 74, 32, 78], [52, 76, 32, 80, "ref"], [52, 79, 32, 83], [52, 80, 32, 84], [53, 6, 33, 8], [53, 7, 33, 9], [53, 8, 33, 10], [54, 4, 34, 4], [55, 4, 35, 4], [55, 8, 35, 10, "name"], [55, 12, 35, 14], [55, 15, 35, 17, "baseComponent"], [55, 28, 35, 30], [55, 29, 35, 31, "displayName"], [55, 40, 35, 42], [55, 44, 35, 46, "baseComponent"], [55, 57, 35, 59], [55, 58, 35, 60, "name"], [55, 62, 35, 64], [55, 66, 35, 68], [55, 75, 35, 77], [56, 4, 36, 4, "component"], [56, 13, 36, 13], [56, 14, 36, 14, "displayName"], [56, 25, 36, 25], [56, 28, 36, 28], [56, 42, 36, 42, "name"], [56, 46, 36, 46], [56, 48, 36, 48], [57, 4, 37, 4, "exports"], [57, 11, 37, 11], [57, 12, 37, 12, "interopComponents"], [57, 29, 37, 29], [57, 30, 37, 30, "set"], [57, 33, 37, 33], [57, 34, 37, 34, "baseComponent"], [57, 47, 37, 47], [57, 49, 37, 49, "component"], [57, 58, 37, 58], [57, 59, 37, 59], [58, 4, 38, 4], [58, 11, 38, 11, "component"], [58, 20, 38, 20], [59, 2, 39, 0], [59, 3, 39, 1], [60, 2, 40, 0, "exports"], [60, 9, 40, 7], [60, 10, 40, 8, "cssInterop"], [60, 20, 40, 18], [60, 23, 40, 21, "cssInterop"], [60, 33, 40, 31], [61, 2, 41, 0], [61, 6, 41, 6, "remapProps"], [61, 16, 41, 16], [61, 19, 41, 19, "remapProps"], [61, 20, 41, 20, "component"], [61, 29, 41, 29], [61, 31, 41, 31, "mapping"], [61, 38, 41, 38], [61, 43, 41, 43], [62, 4, 42, 4], [62, 8, 42, 10, "configs"], [62, 15, 42, 17], [62, 18, 42, 20], [62, 19, 42, 21], [62, 20, 42, 22], [62, 22, 42, 24, "config_1"], [62, 30, 42, 32], [62, 31, 42, 33, "getNormalizeConfig"], [62, 49, 42, 51], [62, 51, 42, 53, "mapping"], [62, 58, 42, 60], [62, 59, 42, 61], [63, 4, 43, 4], [63, 8, 43, 10, "interopComponent"], [63, 24, 43, 26], [63, 27, 43, 29], [63, 28, 43, 30], [63, 29, 43, 31], [63, 31, 43, 33, "react_1"], [63, 38, 43, 40], [63, 39, 43, 41, "forwardRef"], [63, 49, 43, 51], [63, 51, 43, 53], [63, 60, 43, 62, "RemapPropsComponent"], [63, 79, 43, 81, "RemapPropsComponent"], [63, 80, 43, 81, "_ref"], [63, 84, 43, 81], [63, 86, 43, 96, "ref"], [63, 89, 43, 99], [63, 91, 43, 101], [64, 6, 43, 101], [64, 10, 43, 87, "props"], [64, 15, 43, 92], [64, 18, 43, 92, "Object"], [64, 24, 43, 92], [64, 25, 43, 92, "assign"], [64, 31, 43, 92], [64, 37, 43, 92, "_objectDestructuringEmpty"], [64, 62, 43, 92], [64, 63, 43, 92, "_ref"], [64, 67, 43, 92], [64, 70, 43, 92, "_ref"], [64, 74, 43, 92], [65, 6, 44, 8], [65, 11, 44, 13], [65, 15, 44, 19, "config"], [65, 21, 44, 25], [65, 25, 44, 29, "configs"], [65, 32, 44, 36], [65, 34, 44, 38], [66, 8, 45, 12], [66, 12, 45, 18, "source"], [66, 18, 45, 24], [66, 21, 45, 27, "props"], [66, 26, 45, 32], [66, 29, 45, 35, "config"], [66, 35, 45, 41], [66, 36, 45, 42, "source"], [66, 42, 45, 48], [66, 43, 45, 49], [67, 8, 46, 12], [67, 12, 46, 16], [67, 19, 46, 23, "source"], [67, 25, 46, 29], [67, 30, 46, 34], [67, 38, 46, 42], [67, 42, 46, 46], [67, 43, 46, 47, "source"], [67, 49, 46, 53], [67, 51, 47, 16], [68, 8, 48, 12], [68, 12, 48, 18, "placeholder"], [68, 23, 48, 29], [68, 26, 48, 32], [69, 10, 49, 16], [69, 11, 49, 17, "shared_1"], [69, 19, 49, 25], [69, 20, 49, 26, "PLACEHOLDER_SYMBOL"], [69, 38, 49, 44], [69, 41, 49, 47], [70, 8, 50, 12], [70, 9, 50, 13], [71, 8, 51, 12, "styles_1"], [71, 16, 51, 20], [71, 17, 51, 21, "opaqueStyles"], [71, 29, 51, 33], [71, 30, 51, 34, "set"], [71, 33, 51, 37], [71, 34, 51, 38, "placeholder"], [71, 45, 51, 49], [71, 47, 51, 51], [72, 10, 52, 16], [72, 11, 52, 17, "shared_1"], [72, 19, 52, 25], [72, 20, 52, 26, "StyleRuleSetSymbol"], [72, 38, 52, 44], [72, 41, 52, 47], [72, 60, 52, 66], [73, 10, 53, 16, "classNames"], [73, 20, 53, 26], [73, 22, 53, 28, "source"], [73, 28, 53, 34], [73, 29, 53, 35, "split"], [73, 34, 53, 40], [73, 35, 53, 41], [73, 40, 53, 46], [74, 8, 54, 12], [74, 9, 54, 13], [74, 10, 54, 14], [75, 8, 55, 12], [75, 15, 55, 19, "props"], [75, 20, 55, 24], [75, 21, 55, 25, "config"], [75, 27, 55, 31], [75, 28, 55, 32, "source"], [75, 34, 55, 38], [75, 35, 55, 39], [76, 8, 56, 12], [76, 9, 56, 13], [76, 10, 56, 14], [76, 12, 56, 16, "shared_1"], [76, 20, 56, 24], [76, 21, 56, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [76, 35, 56, 39], [76, 37, 56, 41, "props"], [76, 42, 56, 46], [76, 44, 56, 48, "placeholder"], [76, 55, 56, 59], [76, 57, 56, 61, "config"], [76, 63, 56, 67], [76, 65, 56, 69], [77, 10, 57, 16, "objectMergeStyle"], [77, 26, 57, 32], [77, 28, 57, 34], [78, 8, 58, 12], [78, 9, 58, 13], [78, 10, 58, 14], [79, 6, 59, 8], [80, 6, 60, 8, "props"], [80, 11, 60, 13], [80, 12, 60, 14, "ref"], [80, 15, 60, 17], [80, 18, 60, 20, "ref"], [80, 21, 60, 23], [81, 6, 61, 8], [81, 13, 61, 15], [81, 14, 61, 16], [81, 15, 61, 17], [81, 17, 61, 19, "react_1"], [81, 24, 61, 26], [81, 25, 61, 27, "createElement"], [81, 38, 61, 40], [81, 40, 61, 42, "component"], [81, 49, 61, 51], [81, 51, 61, 53, "props"], [81, 56, 61, 58], [81, 58, 61, 60, "props"], [81, 63, 61, 65], [81, 64, 61, 66, "children"], [81, 72, 61, 74], [81, 73, 61, 75], [82, 4, 62, 4], [82, 5, 62, 5], [82, 6, 62, 6], [83, 4, 63, 4, "exports"], [83, 11, 63, 11], [83, 12, 63, 12, "interopComponents"], [83, 29, 63, 29], [83, 30, 63, 30, "set"], [83, 33, 63, 33], [83, 34, 63, 34, "component"], [83, 43, 63, 43], [83, 45, 63, 45, "interopComponent"], [83, 61, 63, 61], [83, 62, 63, 62], [84, 4, 64, 4], [84, 11, 64, 11, "interopComponent"], [84, 27, 64, 27], [85, 2, 65, 0], [85, 3, 65, 1], [86, 2, 66, 0, "exports"], [86, 9, 66, 7], [86, 10, 66, 8, "remapProps"], [86, 20, 66, 18], [86, 23, 66, 21, "remapProps"], [86, 33, 66, 31], [87, 2, 67, 0], [87, 11, 67, 9, "useColorScheme"], [87, 25, 67, 23, "useColorScheme"], [87, 26, 67, 23], [87, 28, 67, 26], [88, 4, 68, 4], [88, 8, 68, 4, "_ref2"], [88, 13, 68, 4], [88, 16, 68, 32], [88, 17, 68, 33], [88, 18, 68, 34], [88, 20, 68, 36, "react_1"], [88, 27, 68, 43], [88, 28, 68, 44, "useState"], [88, 36, 68, 52], [88, 38, 68, 54], [88, 45, 68, 61], [89, 8, 69, 8, "run"], [89, 11, 69, 11], [89, 13, 69, 13, "run"], [89, 14, 69, 13], [89, 19, 69, 19, "setEffect"], [89, 28, 69, 28], [89, 29, 69, 30, "s"], [89, 30, 69, 31], [89, 35, 69, 37], [90, 10, 69, 39], [90, 13, 69, 42, "s"], [91, 8, 69, 44], [91, 9, 69, 45], [91, 10, 69, 46], [91, 11, 69, 47], [92, 8, 70, 8, "dependencies"], [92, 20, 70, 20], [92, 22, 70, 22], [92, 26, 70, 26, "Set"], [92, 29, 70, 29], [92, 30, 70, 30], [93, 6, 71, 4], [93, 7, 71, 5], [93, 8, 71, 6], [93, 9, 71, 7], [94, 6, 71, 7, "_ref3"], [94, 11, 71, 7], [94, 14, 71, 7, "_slicedToArray"], [94, 28, 71, 7], [94, 29, 71, 7, "_ref2"], [94, 34, 71, 7], [95, 6, 68, 11, "effect"], [95, 12, 68, 17], [95, 15, 68, 17, "_ref3"], [95, 20, 68, 17], [96, 6, 68, 19, "setEffect"], [96, 15, 68, 28], [96, 18, 68, 28, "_ref3"], [96, 23, 68, 28], [97, 4, 72, 4], [97, 5, 72, 5], [97, 6, 72, 6], [97, 8, 72, 8, "observable_1"], [97, 20, 72, 20], [97, 21, 72, 21, "cleanupEffect"], [97, 34, 72, 34], [97, 36, 72, 36, "effect"], [97, 42, 72, 42], [97, 43, 72, 43], [98, 4, 73, 4], [98, 11, 73, 11], [99, 6, 74, 8, "colorScheme"], [99, 17, 74, 19], [99, 19, 74, 21, "appearance_observables_1"], [99, 43, 74, 45], [99, 44, 74, 46, "colorScheme"], [99, 55, 74, 57], [99, 56, 74, 58, "get"], [99, 59, 74, 61], [99, 60, 74, 62, "effect"], [99, 66, 74, 68], [99, 67, 74, 69], [100, 6, 75, 8, "setColorScheme"], [100, 20, 75, 22], [100, 22, 75, 24, "appearance_observables_1"], [100, 46, 75, 48], [100, 47, 75, 49, "colorScheme"], [100, 58, 75, 60], [100, 59, 75, 61, "set"], [100, 62, 75, 64], [101, 6, 76, 8, "toggleColorScheme"], [101, 23, 76, 25], [101, 25, 76, 27, "appearance_observables_1"], [101, 49, 76, 51], [101, 50, 76, 52, "colorScheme"], [101, 61, 76, 63], [101, 62, 76, 64, "toggle"], [102, 4, 77, 4], [102, 5, 77, 5], [103, 2, 78, 0], [104, 2, 79, 0], [104, 11, 79, 9, "vars"], [104, 15, 79, 13, "vars"], [104, 16, 79, 14, "variables"], [104, 25, 79, 23], [104, 27, 79, 25], [105, 4, 80, 4], [105, 8, 80, 10, "style"], [105, 13, 80, 15], [105, 16, 80, 18], [105, 17, 80, 19], [105, 18, 80, 20], [106, 4, 81, 4, "styles_1"], [106, 12, 81, 12], [106, 13, 81, 13, "opaqueStyles"], [106, 25, 81, 25], [106, 26, 81, 26, "set"], [106, 29, 81, 29], [106, 30, 81, 30, "style"], [106, 35, 81, 35], [106, 37, 81, 37], [107, 6, 82, 8], [107, 7, 82, 9, "shared_1"], [107, 15, 82, 17], [107, 16, 82, 18, "StyleRuleSetSymbol"], [107, 34, 82, 36], [107, 37, 82, 39], [107, 41, 82, 43], [108, 6, 83, 8, "variables"], [108, 15, 83, 17], [108, 17, 83, 19], [108, 21, 83, 23], [109, 6, 84, 8, "n"], [109, 7, 84, 9], [109, 9, 84, 11], [109, 10, 85, 12], [110, 8, 86, 16], [110, 9, 86, 17, "shared_1"], [110, 17, 86, 25], [110, 18, 86, 26, "StyleRuleSymbol"], [110, 33, 86, 41], [110, 36, 86, 44], [110, 40, 86, 48], [111, 8, 87, 16, "s"], [111, 9, 87, 17], [111, 11, 87, 19, "shared_1"], [111, 19, 87, 27], [111, 20, 87, 28, "inlineSpecificity"], [111, 37, 87, 45], [112, 8, 88, 16, "variables"], [112, 17, 88, 25], [112, 19, 88, 27, "Object"], [112, 25, 88, 33], [112, 26, 88, 34, "entries"], [112, 33, 88, 41], [112, 34, 88, 42, "variables"], [112, 43, 88, 51], [112, 44, 88, 52], [112, 45, 88, 53, "map"], [112, 48, 88, 56], [112, 49, 88, 57, "_ref4"], [112, 54, 88, 57], [112, 58, 88, 76], [113, 10, 88, 76], [113, 14, 88, 76, "_ref5"], [113, 19, 88, 76], [113, 22, 88, 76, "_slicedToArray"], [113, 36, 88, 76], [113, 37, 88, 76, "_ref4"], [113, 42, 88, 76], [114, 12, 88, 59, "name"], [114, 16, 88, 63], [114, 19, 88, 63, "_ref5"], [114, 24, 88, 63], [115, 12, 88, 65, "value"], [115, 17, 88, 70], [115, 20, 88, 70, "_ref5"], [115, 25, 88, 70], [116, 10, 89, 20], [116, 17, 89, 27], [116, 18, 89, 28, "name"], [116, 22, 89, 32], [116, 23, 89, 33, "startsWith"], [116, 33, 89, 43], [116, 34, 89, 44], [116, 38, 89, 48], [116, 39, 89, 49], [116, 42, 89, 52, "name"], [116, 46, 89, 56], [116, 49, 89, 59], [116, 54, 89, 64, "name"], [116, 58, 89, 68], [116, 60, 89, 70], [116, 62, 89, 72, "value"], [116, 67, 89, 77], [116, 68, 89, 78], [117, 8, 90, 16], [117, 9, 90, 17], [118, 6, 91, 12], [118, 7, 91, 13], [119, 4, 93, 4], [119, 5, 93, 5], [119, 6, 93, 6], [120, 4, 94, 4], [120, 11, 94, 11, "style"], [120, 16, 94, 16], [121, 2, 95, 0], [122, 2, 96, 0], [122, 6, 96, 6, "useUnstableNativeVariable"], [122, 31, 96, 31], [122, 34, 96, 35, "name"], [122, 38, 96, 39], [122, 42, 96, 44], [123, 4, 97, 4], [123, 8, 97, 10, "context"], [123, 15, 97, 17], [123, 18, 97, 20], [123, 19, 97, 21], [123, 20, 97, 22], [123, 22, 97, 24, "react_1"], [123, 29, 97, 31], [123, 30, 97, 32, "useContext"], [123, 40, 97, 42], [123, 42, 97, 44, "styles_1"], [123, 50, 97, 52], [123, 51, 97, 53, "VariableContext"], [123, 66, 97, 68], [123, 67, 97, 69], [124, 4, 98, 4], [124, 8, 98, 4, "_ref6"], [124, 13, 98, 4], [124, 16, 98, 31], [124, 17, 98, 32], [124, 18, 98, 33], [124, 20, 98, 35, "react_1"], [124, 27, 98, 42], [124, 28, 98, 43, "useState"], [124, 36, 98, 51], [124, 38, 98, 53], [124, 45, 98, 60], [125, 8, 99, 8, "run"], [125, 11, 99, 11], [125, 13, 99, 13, "run"], [125, 14, 99, 13], [125, 19, 99, 19, "setState"], [125, 27, 99, 27], [125, 28, 99, 29, "s"], [125, 29, 99, 30], [125, 34, 99, 36], [126, 10, 99, 38], [126, 13, 99, 41, "s"], [127, 8, 99, 43], [127, 9, 99, 44], [127, 10, 99, 45], [127, 11, 99, 46], [128, 8, 100, 8, "dependencies"], [128, 20, 100, 20], [128, 22, 100, 22], [128, 26, 100, 26, "Set"], [128, 29, 100, 29], [128, 30, 100, 30], [129, 6, 101, 4], [129, 7, 101, 5], [129, 8, 101, 6], [129, 9, 101, 7], [130, 6, 101, 7, "_ref7"], [130, 11, 101, 7], [130, 14, 101, 7, "_slicedToArray"], [130, 28, 101, 7], [130, 29, 101, 7, "_ref6"], [130, 34, 101, 7], [131, 6, 98, 11, "effect"], [131, 12, 98, 17], [131, 15, 98, 17, "_ref7"], [131, 20, 98, 17], [132, 6, 98, 19, "setState"], [132, 14, 98, 27], [132, 17, 98, 27, "_ref7"], [132, 22, 98, 27], [133, 4, 102, 4], [133, 8, 102, 8, "value"], [133, 13, 102, 13], [133, 16, 102, 16], [133, 17, 102, 17], [133, 18, 102, 18], [133, 20, 102, 20, "styles_1"], [133, 28, 102, 28], [133, 29, 102, 29, "getVariable"], [133, 40, 102, 40], [133, 42, 102, 42, "name"], [133, 46, 102, 46], [133, 48, 102, 48, "context"], [133, 55, 102, 55], [133, 57, 102, 57, "effect"], [133, 63, 102, 63], [133, 64, 102, 64], [134, 4, 103, 4], [134, 8, 103, 8], [134, 15, 103, 15, "value"], [134, 20, 103, 20], [134, 25, 103, 25], [134, 33, 103, 33], [134, 37, 103, 37], [134, 42, 103, 42], [134, 46, 103, 46, "value"], [134, 51, 103, 51], [134, 53, 103, 53], [135, 6, 104, 8], [135, 7, 104, 9], [135, 8, 104, 10], [135, 10, 104, 12, "observable_1"], [135, 22, 104, 24], [135, 23, 104, 25, "cleanupEffect"], [135, 36, 104, 38], [135, 38, 104, 40, "effect"], [135, 44, 104, 46], [135, 45, 104, 47], [136, 6, 105, 8, "value"], [136, 11, 105, 13], [136, 14, 105, 16, "value"], [136, 19, 105, 21], [136, 20, 105, 22, "get"], [136, 23, 105, 25], [136, 24, 105, 26, "effect"], [136, 30, 105, 32], [136, 31, 105, 33], [137, 4, 106, 4], [138, 4, 107, 4], [138, 11, 107, 11, "value"], [138, 16, 107, 16], [139, 2, 108, 0], [139, 3, 108, 1], [140, 2, 109, 0, "exports"], [140, 9, 109, 7], [140, 10, 109, 8, "useUnstableNativeVariable"], [140, 35, 109, 33], [140, 38, 109, 36, "useUnstableNativeVariable"], [140, 63, 109, 61], [141, 2, 110, 0], [141, 6, 110, 6, "useSafeAreaEnv"], [141, 20, 110, 20], [141, 23, 110, 23, "useSafeAreaEnv"], [141, 24, 110, 23], [141, 29, 110, 29], [142, 4, 111, 4, "console"], [142, 11, 111, 11], [142, 12, 111, 12, "warn"], [142, 16, 111, 16], [142, 17, 111, 17], [142, 91, 111, 91], [142, 92, 111, 92], [143, 2, 112, 0], [143, 3, 112, 1], [144, 2, 113, 0, "exports"], [144, 9, 113, 7], [144, 10, 113, 8, "useSafeAreaEnv"], [144, 24, 113, 22], [144, 27, 113, 25, "useSafeAreaEnv"], [144, 41, 113, 39], [145, 0, 113, 40], [145, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get", "cssInterop", "component", "<anonymous>", "remapProps", "RemapPropsComponent", "useColorScheme", "run", "setEffect$argument_0", "vars", "Object.entries.map$argument_0", "useUnstableNativeVariable", "setState$argument_0", "useSafeAreaEnv"], "mappings": "AAA;sECc,+CD;uECE,4DD;+DCE,8CD;mBEE;oBCK;SDE;4CEG;SFE;CFM;mBKE;qDCE;KDmB;CLG;AOE;sDHC;aIC,gBC,iBD,CJ;MGE;CPO;AUC;yDCS;iBDE;CVK;kCYC;qDRE;aIC,eK,iBL,CJ;MQE;CZO;uBcE;CdE"}}, "type": "js/module"}]}