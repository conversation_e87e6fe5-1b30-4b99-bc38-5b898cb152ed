{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var SquareScissors = exports.default = (0, _createLucideIcon.default)(\"SquareScissors\", [[\"rect\", {\n    width: \"20\",\n    height: \"20\",\n    x: \"2\",\n    y: \"2\",\n    rx: \"2\",\n    key: \"1btzen\"\n  }], [\"circle\", {\n    cx: \"8\",\n    cy: \"8\",\n    r: \"2\",\n    key: \"14cg06\"\n  }], [\"path\", {\n    d: \"M9.414 9.414 12 12\",\n    key: \"qz4lzr\"\n  }], [\"path\", {\n    d: \"M14.8 14.8 18 18\",\n    key: \"11flf1\"\n  }], [\"circle\", {\n    cx: \"8\",\n    cy: \"16\",\n    r: \"2\",\n    key: \"1acxsx\"\n  }], [\"path\", {\n    d: \"m18 6-8.586 8.586\",\n    key: \"11kzk1\"\n  }]]);\n});", "lineCount": 42, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "SquareScissors"], [15, 20, 10, 20], [15, 23, 10, 20, "exports"], [15, 30, 10, 20], [15, 31, 10, 20, "default"], [15, 38, 10, 20], [15, 41, 10, 23], [15, 45, 10, 23, "createLucideIcon"], [15, 70, 10, 39], [15, 72, 10, 40], [15, 88, 10, 56], [15, 90, 10, 58], [15, 91, 11, 2], [15, 92, 11, 3], [15, 98, 11, 9], [15, 100, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 15, 11, 24], [17, 4, 11, 26, "height"], [17, 10, 11, 32], [17, 12, 11, 34], [17, 16, 11, 38], [18, 4, 11, 40, "x"], [18, 5, 11, 41], [18, 7, 11, 43], [18, 10, 11, 46], [19, 4, 11, 48, "y"], [19, 5, 11, 49], [19, 7, 11, 51], [19, 10, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "key"], [21, 7, 11, 68], [21, 9, 11, 70], [22, 2, 11, 79], [22, 3, 11, 80], [22, 4, 11, 81], [22, 6, 12, 2], [22, 7, 12, 3], [22, 15, 12, 11], [22, 17, 12, 13], [23, 4, 12, 15, "cx"], [23, 6, 12, 17], [23, 8, 12, 19], [23, 11, 12, 22], [24, 4, 12, 24, "cy"], [24, 6, 12, 26], [24, 8, 12, 28], [24, 11, 12, 31], [25, 4, 12, 33, "r"], [25, 5, 12, 34], [25, 7, 12, 36], [25, 10, 12, 39], [26, 4, 12, 41, "key"], [26, 7, 12, 44], [26, 9, 12, 46], [27, 2, 12, 55], [27, 3, 12, 56], [27, 4, 12, 57], [27, 6, 13, 2], [27, 7, 13, 3], [27, 13, 13, 9], [27, 15, 13, 11], [28, 4, 13, 13, "d"], [28, 5, 13, 14], [28, 7, 13, 16], [28, 27, 13, 36], [29, 4, 13, 38, "key"], [29, 7, 13, 41], [29, 9, 13, 43], [30, 2, 13, 52], [30, 3, 13, 53], [30, 4, 13, 54], [30, 6, 14, 2], [30, 7, 14, 3], [30, 13, 14, 9], [30, 15, 14, 11], [31, 4, 14, 13, "d"], [31, 5, 14, 14], [31, 7, 14, 16], [31, 25, 14, 34], [32, 4, 14, 36, "key"], [32, 7, 14, 39], [32, 9, 14, 41], [33, 2, 14, 50], [33, 3, 14, 51], [33, 4, 14, 52], [33, 6, 15, 2], [33, 7, 15, 3], [33, 15, 15, 11], [33, 17, 15, 13], [34, 4, 15, 15, "cx"], [34, 6, 15, 17], [34, 8, 15, 19], [34, 11, 15, 22], [35, 4, 15, 24, "cy"], [35, 6, 15, 26], [35, 8, 15, 28], [35, 12, 15, 32], [36, 4, 15, 34, "r"], [36, 5, 15, 35], [36, 7, 15, 37], [36, 10, 15, 40], [37, 4, 15, 42, "key"], [37, 7, 15, 45], [37, 9, 15, 47], [38, 2, 15, 56], [38, 3, 15, 57], [38, 4, 15, 58], [38, 6, 16, 2], [38, 7, 16, 3], [38, 13, 16, 9], [38, 15, 16, 11], [39, 4, 16, 13, "d"], [39, 5, 16, 14], [39, 7, 16, 16], [39, 26, 16, 35], [40, 4, 16, 37, "key"], [40, 7, 16, 40], [40, 9, 16, 42], [41, 2, 16, 51], [41, 3, 16, 52], [41, 4, 16, 53], [41, 5, 17, 1], [41, 6, 17, 2], [42, 0, 17, 3], [42, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}