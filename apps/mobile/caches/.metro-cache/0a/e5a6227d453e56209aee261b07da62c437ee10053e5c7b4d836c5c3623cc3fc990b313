{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const PictureInPicture2 = exports.default = (0, _createLucideIcon.default)(\"PictureInPicture2\", [[\"path\", {\n    d: \"M21 9V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h4\",\n    key: \"daa4of\"\n  }], [\"rect\", {\n    width: \"10\",\n    height: \"7\",\n    x: \"12\",\n    y: \"13\",\n    rx: \"2\",\n    key: \"1nb8gs\"\n  }]]);\n});", "lineCount": 26, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "PictureInPicture2"], [15, 25, 10, 23], [15, 28, 10, 23, "exports"], [15, 35, 10, 23], [15, 36, 10, 23, "default"], [15, 43, 10, 23], [15, 46, 10, 26], [15, 50, 10, 26, "createLucideIcon"], [15, 75, 10, 42], [15, 77, 10, 43], [15, 96, 10, 62], [15, 98, 10, 64], [15, 99, 11, 2], [15, 100, 11, 3], [15, 106, 11, 9], [15, 108, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 65, 11, 74], [17, 4, 11, 76, "key"], [17, 7, 11, 79], [17, 9, 11, 81], [18, 2, 11, 90], [18, 3, 11, 91], [18, 4, 11, 92], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "width"], [19, 9, 12, 18], [19, 11, 12, 20], [19, 15, 12, 24], [20, 4, 12, 26, "height"], [20, 10, 12, 32], [20, 12, 12, 34], [20, 15, 12, 37], [21, 4, 12, 39, "x"], [21, 5, 12, 40], [21, 7, 12, 42], [21, 11, 12, 46], [22, 4, 12, 48, "y"], [22, 5, 12, 49], [22, 7, 12, 51], [22, 11, 12, 55], [23, 4, 12, 57, "rx"], [23, 6, 12, 59], [23, 8, 12, 61], [23, 11, 12, 64], [24, 4, 12, 66, "key"], [24, 7, 12, 69], [24, 9, 12, 71], [25, 2, 12, 80], [25, 3, 12, 81], [25, 4, 12, 82], [25, 5, 13, 1], [25, 6, 13, 2], [26, 0, 13, 3], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}