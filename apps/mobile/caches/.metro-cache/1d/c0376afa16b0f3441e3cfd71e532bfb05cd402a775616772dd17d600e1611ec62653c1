{"dependencies": [{"name": "expo/dom/global", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 17, "index": 296}, "end": {"line": 7, "column": 43, "index": 322}}], "key": "5bO3ucGHWMBCsJYlm6TwO/aW1HE=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 356}, "end": {"line": 8, "column": 48, "index": 372}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./events", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 17, "index": 392}, "end": {"line": 9, "column": 36, "index": 411}}], "key": "RQEAXnrIpl4a6RHsE1TF/7TG44E=", "exportNames": ["*"]}}, {"name": "../global-state/routing", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 18, "index": 431}, "end": {"line": 10, "column": 52, "index": 465}}], "key": "Cqbl9MOLPE8L7igowwxv8ngZpbk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDomComponentNavigation = useDomComponentNavigation;\n  var global_1 = require(_dependencyMap[0], \"expo/dom/global\");\n  var react_1 = __importDefault(require(_dependencyMap[1], \"react\"));\n  var events_1 = require(_dependencyMap[2], \"./events\");\n  var routing_1 = require(_dependencyMap[3], \"../global-state/routing\");\n  function useDomComponentNavigation() {\n    react_1.default.useEffect(() => {\n      if (false) {\n        return () => {};\n      }\n      return (0, global_1.addGlobalDomEventListener)(_ref => {\n        var type = _ref.type,\n          data = _ref.data;\n        switch (type) {\n          case events_1.ROUTER_LINK_TYPE:\n            (0, routing_1.linkTo)(data.href, data.options);\n            break;\n          case events_1.ROUTER_DISMISS_ALL_TYPE:\n            (0, routing_1.dismissAll)();\n            break;\n          case events_1.ROUTER_DISMISS_TYPE:\n            (0, routing_1.dismiss)(data.count);\n            break;\n          case events_1.ROUTER_BACK_TYPE:\n            (0, routing_1.goBack)();\n            break;\n          case events_1.ROUTER_SET_PARAMS_TYPE:\n            (0, routing_1.setParams)(data.params);\n            break;\n        }\n      });\n    }, []);\n  }\n});", "lineCount": 45, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__importDefault"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__importDefault"], [4, 52, 2, 51], [4, 56, 2, 56], [4, 66, 2, 66, "mod"], [4, 69, 2, 69], [4, 71, 2, 71], [5, 4, 3, 4], [5, 11, 3, 12, "mod"], [5, 14, 3, 15], [5, 18, 3, 19, "mod"], [5, 21, 3, 22], [5, 22, 3, 23, "__esModule"], [5, 32, 3, 33], [5, 35, 3, 37, "mod"], [5, 38, 3, 40], [5, 41, 3, 43], [6, 6, 3, 45], [6, 15, 3, 54], [6, 17, 3, 56, "mod"], [7, 4, 3, 60], [7, 5, 3, 61], [8, 2, 4, 0], [8, 3, 4, 1], [9, 2, 5, 0, "Object"], [9, 8, 5, 6], [9, 9, 5, 7, "defineProperty"], [9, 23, 5, 21], [9, 24, 5, 22, "exports"], [9, 31, 5, 29], [9, 33, 5, 31], [9, 45, 5, 43], [9, 47, 5, 45], [10, 4, 5, 47, "value"], [10, 9, 5, 52], [10, 11, 5, 54], [11, 2, 5, 59], [11, 3, 5, 60], [11, 4, 5, 61], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "useDomComponentNavigation"], [12, 35, 6, 33], [12, 38, 6, 36, "useDomComponentNavigation"], [12, 63, 6, 61], [13, 2, 7, 0], [13, 6, 7, 6, "global_1"], [13, 14, 7, 14], [13, 17, 7, 17, "require"], [13, 24, 7, 24], [13, 25, 7, 24, "_dependencyMap"], [13, 39, 7, 24], [13, 61, 7, 42], [13, 62, 7, 43], [14, 2, 8, 0], [14, 6, 8, 6, "react_1"], [14, 13, 8, 13], [14, 16, 8, 16, "__importDefault"], [14, 31, 8, 31], [14, 32, 8, 32, "require"], [14, 39, 8, 39], [14, 40, 8, 39, "_dependencyMap"], [14, 54, 8, 39], [14, 66, 8, 47], [14, 67, 8, 48], [14, 68, 8, 49], [15, 2, 9, 0], [15, 6, 9, 6, "events_1"], [15, 14, 9, 14], [15, 17, 9, 17, "require"], [15, 24, 9, 24], [15, 25, 9, 24, "_dependencyMap"], [15, 39, 9, 24], [15, 54, 9, 35], [15, 55, 9, 36], [16, 2, 10, 0], [16, 6, 10, 6, "routing_1"], [16, 15, 10, 15], [16, 18, 10, 18, "require"], [16, 25, 10, 25], [16, 26, 10, 25, "_dependencyMap"], [16, 40, 10, 25], [16, 70, 10, 51], [16, 71, 10, 52], [17, 2, 11, 0], [17, 11, 11, 9, "useDomComponentNavigation"], [17, 36, 11, 34, "useDomComponentNavigation"], [17, 37, 11, 34], [17, 39, 11, 37], [18, 4, 12, 4, "react_1"], [18, 11, 12, 11], [18, 12, 12, 12, "default"], [18, 19, 12, 19], [18, 20, 12, 20, "useEffect"], [18, 29, 12, 29], [18, 30, 12, 30], [18, 36, 12, 36], [19, 6, 13, 8], [19, 17, 13, 43], [20, 8, 14, 12], [20, 15, 14, 19], [20, 21, 14, 25], [20, 22, 14, 27], [20, 23, 14, 28], [21, 6, 15, 8], [22, 6, 16, 8], [22, 13, 16, 15], [22, 14, 16, 16], [22, 15, 16, 17], [22, 17, 16, 19, "global_1"], [22, 25, 16, 27], [22, 26, 16, 28, "addGlobalDomEventListener"], [22, 51, 16, 53], [22, 53, 16, 55, "_ref"], [22, 57, 16, 55], [22, 61, 16, 75], [23, 8, 16, 75], [23, 12, 16, 58, "type"], [23, 16, 16, 62], [23, 19, 16, 62, "_ref"], [23, 23, 16, 62], [23, 24, 16, 58, "type"], [23, 28, 16, 62], [24, 10, 16, 64, "data"], [24, 14, 16, 68], [24, 17, 16, 68, "_ref"], [24, 21, 16, 68], [24, 22, 16, 64, "data"], [24, 26, 16, 68], [25, 8, 17, 12], [25, 16, 17, 20, "type"], [25, 20, 17, 24], [26, 10, 18, 16], [26, 15, 18, 21, "events_1"], [26, 23, 18, 29], [26, 24, 18, 30, "ROUTER_LINK_TYPE"], [26, 40, 18, 46], [27, 12, 19, 20], [27, 13, 19, 21], [27, 14, 19, 22], [27, 16, 19, 24, "routing_1"], [27, 25, 19, 33], [27, 26, 19, 34, "linkTo"], [27, 32, 19, 40], [27, 34, 19, 42, "data"], [27, 38, 19, 46], [27, 39, 19, 47, "href"], [27, 43, 19, 51], [27, 45, 19, 53, "data"], [27, 49, 19, 57], [27, 50, 19, 58, "options"], [27, 57, 19, 65], [27, 58, 19, 66], [28, 12, 20, 20], [29, 10, 21, 16], [29, 15, 21, 21, "events_1"], [29, 23, 21, 29], [29, 24, 21, 30, "ROUTER_DISMISS_ALL_TYPE"], [29, 47, 21, 53], [30, 12, 22, 20], [30, 13, 22, 21], [30, 14, 22, 22], [30, 16, 22, 24, "routing_1"], [30, 25, 22, 33], [30, 26, 22, 34, "dismissAll"], [30, 36, 22, 44], [30, 38, 22, 46], [30, 39, 22, 47], [31, 12, 23, 20], [32, 10, 24, 16], [32, 15, 24, 21, "events_1"], [32, 23, 24, 29], [32, 24, 24, 30, "ROUTER_DISMISS_TYPE"], [32, 43, 24, 49], [33, 12, 25, 20], [33, 13, 25, 21], [33, 14, 25, 22], [33, 16, 25, 24, "routing_1"], [33, 25, 25, 33], [33, 26, 25, 34, "dismiss"], [33, 33, 25, 41], [33, 35, 25, 43, "data"], [33, 39, 25, 47], [33, 40, 25, 48, "count"], [33, 45, 25, 53], [33, 46, 25, 54], [34, 12, 26, 20], [35, 10, 27, 16], [35, 15, 27, 21, "events_1"], [35, 23, 27, 29], [35, 24, 27, 30, "ROUTER_BACK_TYPE"], [35, 40, 27, 46], [36, 12, 28, 20], [36, 13, 28, 21], [36, 14, 28, 22], [36, 16, 28, 24, "routing_1"], [36, 25, 28, 33], [36, 26, 28, 34, "goBack"], [36, 32, 28, 40], [36, 34, 28, 42], [36, 35, 28, 43], [37, 12, 29, 20], [38, 10, 30, 16], [38, 15, 30, 21, "events_1"], [38, 23, 30, 29], [38, 24, 30, 30, "ROUTER_SET_PARAMS_TYPE"], [38, 46, 30, 52], [39, 12, 31, 20], [39, 13, 31, 21], [39, 14, 31, 22], [39, 16, 31, 24, "routing_1"], [39, 25, 31, 33], [39, 26, 31, 34, "setParams"], [39, 35, 31, 43], [39, 37, 31, 45, "data"], [39, 41, 31, 49], [39, 42, 31, 50, "params"], [39, 48, 31, 56], [39, 49, 31, 57], [40, 12, 32, 20], [41, 8, 33, 12], [42, 6, 34, 8], [42, 7, 34, 9], [42, 8, 34, 10], [43, 4, 35, 4], [43, 5, 35, 5], [43, 7, 35, 7], [43, 9, 35, 9], [43, 10, 35, 10], [44, 2, 36, 0], [45, 0, 36, 1], [45, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "useDomComponentNavigation", "react_1._default.useEffect$argument_0"], "mappings": "AAA;wDCC;CDE;AEO;8BCC;mBFE,SE;uDFE;SEkB;KDC;CFC"}}, "type": "js/module"}]}