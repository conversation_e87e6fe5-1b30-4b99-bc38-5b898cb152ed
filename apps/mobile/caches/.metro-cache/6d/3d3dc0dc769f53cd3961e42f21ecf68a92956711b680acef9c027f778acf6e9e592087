{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-extraneous-class\n  class NodeManager {\n    static getHandler(tag) {\n      if (tag in this.gestures) {\n        return this.gestures[tag];\n      }\n      throw new Error(`No handler for tag ${tag}`);\n    }\n    static createGestureHandler(handlerTag, handler) {\n      if (handlerTag in this.gestures) {\n        throw new Error(`Handler with tag ${handlerTag} already exists. Please ensure that no Gesture instance is used across multiple GestureDetectors.`);\n      }\n      this.gestures[handlerTag] = handler;\n      this.gestures[handlerTag].handlerTag = handlerTag;\n    }\n    static dropGestureHandler(handlerTag) {\n      if (!(handlerTag in this.gestures)) {\n        return;\n      }\n      this.gestures[handlerTag].onDestroy(); // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n\n      delete this.gestures[handlerTag];\n    }\n    static get nodes() {\n      return {\n        ...this.gestures\n      };\n    }\n  }\n  exports.default = NodeManager;\n  _defineProperty(NodeManager, \"gestures\", {});\n});", "lineCount": 51, "map": [[6, 2, 1, 0], [6, 11, 1, 9, "_defineProperty"], [6, 26, 1, 24, "_defineProperty"], [6, 27, 1, 25, "obj"], [6, 30, 1, 28], [6, 32, 1, 30, "key"], [6, 35, 1, 33], [6, 37, 1, 35, "value"], [6, 42, 1, 40], [6, 44, 1, 42], [7, 4, 1, 44], [7, 8, 1, 48, "key"], [7, 11, 1, 51], [7, 15, 1, 55, "obj"], [7, 18, 1, 58], [7, 20, 1, 60], [8, 6, 1, 62, "Object"], [8, 12, 1, 68], [8, 13, 1, 69, "defineProperty"], [8, 27, 1, 83], [8, 28, 1, 84, "obj"], [8, 31, 1, 87], [8, 33, 1, 89, "key"], [8, 36, 1, 92], [8, 38, 1, 94], [9, 8, 1, 96, "value"], [9, 13, 1, 101], [9, 15, 1, 103, "value"], [9, 20, 1, 108], [10, 8, 1, 110, "enumerable"], [10, 18, 1, 120], [10, 20, 1, 122], [10, 24, 1, 126], [11, 8, 1, 128, "configurable"], [11, 20, 1, 140], [11, 22, 1, 142], [11, 26, 1, 146], [12, 8, 1, 148, "writable"], [12, 16, 1, 156], [12, 18, 1, 158], [13, 6, 1, 163], [13, 7, 1, 164], [13, 8, 1, 165], [14, 4, 1, 167], [14, 5, 1, 168], [14, 11, 1, 174], [15, 6, 1, 176, "obj"], [15, 9, 1, 179], [15, 10, 1, 180, "key"], [15, 13, 1, 183], [15, 14, 1, 184], [15, 17, 1, 187, "value"], [15, 22, 1, 192], [16, 4, 1, 194], [17, 4, 1, 196], [17, 11, 1, 203, "obj"], [17, 14, 1, 206], [18, 2, 1, 208], [20, 2, 3, 0], [21, 2, 4, 15], [21, 8, 4, 21, "NodeManager"], [21, 19, 4, 32], [21, 20, 4, 33], [22, 4, 5, 2], [22, 11, 5, 9, "<PERSON><PERSON><PERSON><PERSON>"], [22, 21, 5, 19, "<PERSON><PERSON><PERSON><PERSON>"], [22, 22, 5, 20, "tag"], [22, 25, 5, 23], [22, 27, 5, 25], [23, 6, 6, 4], [23, 10, 6, 8, "tag"], [23, 13, 6, 11], [23, 17, 6, 15], [23, 21, 6, 19], [23, 22, 6, 20, "gestures"], [23, 30, 6, 28], [23, 32, 6, 30], [24, 8, 7, 6], [24, 15, 7, 13], [24, 19, 7, 17], [24, 20, 7, 18, "gestures"], [24, 28, 7, 26], [24, 29, 7, 27, "tag"], [24, 32, 7, 30], [24, 33, 7, 31], [25, 6, 8, 4], [26, 6, 10, 4], [26, 12, 10, 10], [26, 16, 10, 14, "Error"], [26, 21, 10, 19], [26, 22, 10, 20], [26, 44, 10, 42, "tag"], [26, 47, 10, 45], [26, 49, 10, 47], [26, 50, 10, 48], [27, 4, 11, 2], [28, 4, 13, 2], [28, 11, 13, 9, "createGestureHandler"], [28, 31, 13, 29, "createGestureHandler"], [28, 32, 13, 30, "handlerTag"], [28, 42, 13, 40], [28, 44, 13, 42, "handler"], [28, 51, 13, 49], [28, 53, 13, 51], [29, 6, 14, 4], [29, 10, 14, 8, "handlerTag"], [29, 20, 14, 18], [29, 24, 14, 22], [29, 28, 14, 26], [29, 29, 14, 27, "gestures"], [29, 37, 14, 35], [29, 39, 14, 37], [30, 8, 15, 6], [30, 14, 15, 12], [30, 18, 15, 16, "Error"], [30, 23, 15, 21], [30, 24, 15, 22], [30, 44, 15, 42, "handlerTag"], [30, 54, 15, 52], [30, 153, 15, 151], [30, 154, 15, 152], [31, 6, 16, 4], [32, 6, 18, 4], [32, 10, 18, 8], [32, 11, 18, 9, "gestures"], [32, 19, 18, 17], [32, 20, 18, 18, "handlerTag"], [32, 30, 18, 28], [32, 31, 18, 29], [32, 34, 18, 32, "handler"], [32, 41, 18, 39], [33, 6, 19, 4], [33, 10, 19, 8], [33, 11, 19, 9, "gestures"], [33, 19, 19, 17], [33, 20, 19, 18, "handlerTag"], [33, 30, 19, 28], [33, 31, 19, 29], [33, 32, 19, 30, "handlerTag"], [33, 42, 19, 40], [33, 45, 19, 43, "handlerTag"], [33, 55, 19, 53], [34, 4, 20, 2], [35, 4, 22, 2], [35, 11, 22, 9, "dropGestureHandler"], [35, 29, 22, 27, "dropGestureHandler"], [35, 30, 22, 28, "handlerTag"], [35, 40, 22, 38], [35, 42, 22, 40], [36, 6, 23, 4], [36, 10, 23, 8], [36, 12, 23, 10, "handlerTag"], [36, 22, 23, 20], [36, 26, 23, 24], [36, 30, 23, 28], [36, 31, 23, 29, "gestures"], [36, 39, 23, 37], [36, 40, 23, 38], [36, 42, 23, 40], [37, 8, 24, 6], [38, 6, 25, 4], [39, 6, 27, 4], [39, 10, 27, 8], [39, 11, 27, 9, "gestures"], [39, 19, 27, 17], [39, 20, 27, 18, "handlerTag"], [39, 30, 27, 28], [39, 31, 27, 29], [39, 32, 27, 30, "onDestroy"], [39, 41, 27, 39], [39, 42, 27, 40], [39, 43, 27, 41], [39, 44, 27, 42], [39, 45, 27, 43], [41, 6, 29, 4], [41, 13, 29, 11], [41, 17, 29, 15], [41, 18, 29, 16, "gestures"], [41, 26, 29, 24], [41, 27, 29, 25, "handlerTag"], [41, 37, 29, 35], [41, 38, 29, 36], [42, 4, 30, 2], [43, 4, 32, 2], [43, 15, 32, 13, "nodes"], [43, 20, 32, 18, "nodes"], [43, 21, 32, 18], [43, 23, 32, 21], [44, 6, 33, 4], [44, 13, 33, 11], [45, 8, 33, 13], [45, 11, 33, 16], [45, 15, 33, 20], [45, 16, 33, 21, "gestures"], [46, 6, 34, 4], [46, 7, 34, 5], [47, 4, 35, 2], [48, 2, 37, 0], [49, 2, 37, 1, "exports"], [49, 9, 37, 1], [49, 10, 37, 1, "default"], [49, 17, 37, 1], [49, 20, 37, 1, "NodeManager"], [49, 31, 37, 1], [50, 2, 39, 0, "_defineProperty"], [50, 17, 39, 15], [50, 18, 39, 16, "NodeManager"], [50, 29, 39, 27], [50, 31, 39, 29], [50, 41, 39, 39], [50, 43, 39, 41], [50, 44, 39, 42], [50, 45, 39, 43], [50, 46, 39, 44], [51, 0, 39, 45], [51, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "NodeManager", "<PERSON><PERSON><PERSON><PERSON>", "createGestureHandler", "dropGestureHandler", "get__nodes"], "mappings": "AAA,iNC;eCG;ECC;GDM;EEE;GFO;EGE;GHQ;EIE;GJG;CDE"}}, "type": "js/module"}]}