{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/createElement", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "hwcMDuX6ahNKBi9GfK6FsxxQxmE=", "exportNames": ["*"]}}, {"name": "./utils/prepare", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 139}, "end": {"line": 5, "column": 42, "index": 181}}], "key": "GqKeXJqqPYJpnsdyEb9nkUiDDgY=", "exportNames": ["*"]}}, {"name": "./utils/convertInt32Color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 182}, "end": {"line": 6, "column": 68, "index": 250}}], "key": "BBCuFwBQZ9vIYDW2b41WTu9wn0A=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 251}, "end": {"line": 7, "column": 55, "index": 306}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "./utils/hasProperty", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 307}, "end": {"line": 8, "column": 59, "index": 366}}], "key": "KpyUYQVIDZMQ5fFxv+Uik6Q4PnU=", "exportNames": ["*"]}}, {"name": "../lib/SvgTouchableMixin", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 367}, "end": {"line": 9, "column": 57, "index": 424}}], "key": "G7na4RkwGgEaBeNbn5G/gGt/5KE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WebShape = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _createElement = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/createElement\"));\n  var _prepare = require(_dependencyMap[3], \"./utils/prepare\");\n  var _convertInt32Color = require(_dependencyMap[4], \"./utils/convertInt32Color\");\n  var _utils = require(_dependencyMap[5], \"./utils\");\n  var _hasProperty = require(_dependencyMap[6], \"./utils/hasProperty\");\n  var _SvgTouchableMixin = _interopRequireDefault(require(_dependencyMap[7], \"../lib/SvgTouchableMixin\"));\n  class WebShape extends _react.default.Component {\n    prepareProps(props) {\n      return props;\n    }\n    elementRef = /*#__PURE__*/_react.default.createRef();\n    lastMergedProps = {};\n\n    /**\n     * disclaimer: I am not sure why the props are wrapped in a `style` attribute here, but that's how reanimated calls it\n     */\n    setNativeProps(props) {\n      const merged = Object.assign({}, this.props, this.lastMergedProps, props.style);\n      this.lastMergedProps = merged;\n      const clean = (0, _prepare.prepare)(this, this.prepareProps(merged));\n      const current = this.elementRef.current;\n      if (current) {\n        for (const cleanAttribute of Object.keys(clean)) {\n          const cleanValue = clean[cleanAttribute];\n          switch (cleanAttribute) {\n            case 'ref':\n            case 'children':\n              break;\n            case 'style':\n              // style can be an object here or an array, so we convert it to an array and assign each element\n              for (const partialStyle of [].concat(clean.style ?? [])) {\n                Object.assign(current.style, partialStyle);\n              }\n              break;\n            case 'fill':\n              if (cleanValue && typeof cleanValue === 'object') {\n                const value = cleanValue;\n                current.setAttribute('fill', (0, _convertInt32Color.convertInt32ColorToRGBA)(value.payload));\n              }\n              break;\n            case 'stroke':\n              if (cleanValue && typeof cleanValue === 'object') {\n                const value = cleanValue;\n                current.setAttribute('stroke', (0, _convertInt32Color.convertInt32ColorToRGBA)(value.payload));\n              }\n              break;\n            default:\n              // apply all other incoming prop updates as attributes on the node\n              // same logic as in https://github.com/software-mansion/react-native-reanimated/blob/d04720c82f5941532991b235787285d36d717247/src/reanimated2/js-reanimated/index.ts#L38-L39\n              // @ts-expect-error TODO: fix this\n              current.setAttribute((0, _utils.camelCaseToDashed)(cleanAttribute), cleanValue);\n              break;\n          }\n        }\n      }\n    }\n    constructor(props) {\n      super(props);\n\n      // Do not attach touchable mixin handlers if SVG element doesn't have a touchable prop\n      if ((0, _hasProperty.hasTouchableProperty)(props)) {\n        (0, _SvgTouchableMixin.default)(this);\n      }\n      this._remeasureMetricsOnActivation = _utils.remeasure.bind(this);\n    }\n    render() {\n      if (!this.tag) {\n        throw new Error('When extending `WebShape` you need to overwrite either `tag` or `render`!');\n      }\n      this.lastMergedProps = {};\n      return (0, _createElement.default)(this.tag, (0, _prepare.prepare)(this, this.prepareProps(this.props)));\n    }\n  }\n  exports.WebShape = WebShape;\n});", "lineCount": 82, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 1, 26], [8, 6, 1, 26, "_createElement"], [8, 20, 1, 26], [8, 23, 1, 26, "_interopRequireDefault"], [8, 45, 1, 26], [8, 46, 1, 26, "require"], [8, 53, 1, 26], [8, 54, 1, 26, "_dependencyMap"], [8, 68, 1, 26], [9, 2, 5, 0], [9, 6, 5, 0, "_prepare"], [9, 14, 5, 0], [9, 17, 5, 0, "require"], [9, 24, 5, 0], [9, 25, 5, 0, "_dependencyMap"], [9, 39, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_convertInt32Color"], [10, 24, 6, 0], [10, 27, 6, 0, "require"], [10, 34, 6, 0], [10, 35, 6, 0, "_dependencyMap"], [10, 49, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_utils"], [11, 12, 7, 0], [11, 15, 7, 0, "require"], [11, 22, 7, 0], [11, 23, 7, 0, "_dependencyMap"], [11, 37, 7, 0], [12, 2, 8, 0], [12, 6, 8, 0, "_hasProperty"], [12, 18, 8, 0], [12, 21, 8, 0, "require"], [12, 28, 8, 0], [12, 29, 8, 0, "_dependencyMap"], [12, 43, 8, 0], [13, 2, 9, 0], [13, 6, 9, 0, "_SvgTouchableMixin"], [13, 24, 9, 0], [13, 27, 9, 0, "_interopRequireDefault"], [13, 49, 9, 0], [13, 50, 9, 0, "require"], [13, 57, 9, 0], [13, 58, 9, 0, "_dependencyMap"], [13, 72, 9, 0], [14, 2, 10, 7], [14, 8, 10, 13, "WebShape"], [14, 16, 10, 21], [14, 25, 10, 30, "React"], [14, 39, 10, 35], [14, 40, 10, 36, "Component"], [14, 49, 10, 45], [14, 50, 10, 46], [15, 4, 11, 2, "prepareProps"], [15, 16, 11, 14, "prepareProps"], [15, 17, 11, 15, "props"], [15, 22, 11, 20], [15, 24, 11, 22], [16, 6, 12, 4], [16, 13, 12, 11, "props"], [16, 18, 12, 16], [17, 4, 13, 2], [18, 4, 14, 2, "elementRef"], [18, 14, 14, 12], [18, 17, 14, 15], [18, 30, 14, 28, "React"], [18, 44, 14, 33], [18, 45, 14, 34, "createRef"], [18, 54, 14, 43], [18, 55, 14, 44], [18, 56, 14, 45], [19, 4, 15, 2, "lastMergedProps"], [19, 19, 15, 17], [19, 22, 15, 20], [19, 23, 15, 21], [19, 24, 15, 22], [21, 4, 17, 2], [22, 0, 18, 0], [23, 0, 19, 0], [24, 4, 20, 2, "setNativeProps"], [24, 18, 20, 16, "setNativeProps"], [24, 19, 20, 17, "props"], [24, 24, 20, 22], [24, 26, 20, 24], [25, 6, 21, 4], [25, 12, 21, 10, "merged"], [25, 18, 21, 16], [25, 21, 21, 19, "Object"], [25, 27, 21, 25], [25, 28, 21, 26, "assign"], [25, 34, 21, 32], [25, 35, 21, 33], [25, 36, 21, 34], [25, 37, 21, 35], [25, 39, 21, 37], [25, 43, 21, 41], [25, 44, 21, 42, "props"], [25, 49, 21, 47], [25, 51, 21, 49], [25, 55, 21, 53], [25, 56, 21, 54, "lastMergedProps"], [25, 71, 21, 69], [25, 73, 21, 71, "props"], [25, 78, 21, 76], [25, 79, 21, 77, "style"], [25, 84, 21, 82], [25, 85, 21, 83], [26, 6, 22, 4], [26, 10, 22, 8], [26, 11, 22, 9, "lastMergedProps"], [26, 26, 22, 24], [26, 29, 22, 27, "merged"], [26, 35, 22, 33], [27, 6, 23, 4], [27, 12, 23, 10, "clean"], [27, 17, 23, 15], [27, 20, 23, 18], [27, 24, 23, 18, "prepare"], [27, 40, 23, 25], [27, 42, 23, 26], [27, 46, 23, 30], [27, 48, 23, 32], [27, 52, 23, 36], [27, 53, 23, 37, "prepareProps"], [27, 65, 23, 49], [27, 66, 23, 50, "merged"], [27, 72, 23, 56], [27, 73, 23, 57], [27, 74, 23, 58], [28, 6, 24, 4], [28, 12, 24, 10, "current"], [28, 19, 24, 17], [28, 22, 24, 20], [28, 26, 24, 24], [28, 27, 24, 25, "elementRef"], [28, 37, 24, 35], [28, 38, 24, 36, "current"], [28, 45, 24, 43], [29, 6, 25, 4], [29, 10, 25, 8, "current"], [29, 17, 25, 15], [29, 19, 25, 17], [30, 8, 26, 6], [30, 13, 26, 11], [30, 19, 26, 17, "cleanAttribute"], [30, 33, 26, 31], [30, 37, 26, 35, "Object"], [30, 43, 26, 41], [30, 44, 26, 42, "keys"], [30, 48, 26, 46], [30, 49, 26, 47, "clean"], [30, 54, 26, 52], [30, 55, 26, 53], [30, 57, 26, 55], [31, 10, 27, 8], [31, 16, 27, 14, "cleanValue"], [31, 26, 27, 24], [31, 29, 27, 27, "clean"], [31, 34, 27, 32], [31, 35, 27, 33, "cleanAttribute"], [31, 49, 27, 47], [31, 50, 27, 48], [32, 10, 28, 8], [32, 18, 28, 16, "cleanAttribute"], [32, 32, 28, 30], [33, 12, 29, 10], [33, 17, 29, 15], [33, 22, 29, 20], [34, 12, 30, 10], [34, 17, 30, 15], [34, 27, 30, 25], [35, 14, 31, 12], [36, 12, 32, 10], [36, 17, 32, 15], [36, 24, 32, 22], [37, 14, 33, 12], [38, 14, 34, 12], [38, 19, 34, 17], [38, 25, 34, 23, "partialStyle"], [38, 37, 34, 35], [38, 41, 34, 39], [38, 43, 34, 41], [38, 44, 34, 42, "concat"], [38, 50, 34, 48], [38, 51, 34, 49, "clean"], [38, 56, 34, 54], [38, 57, 34, 55, "style"], [38, 62, 34, 60], [38, 66, 34, 64], [38, 68, 34, 66], [38, 69, 34, 67], [38, 71, 34, 69], [39, 16, 35, 14, "Object"], [39, 22, 35, 20], [39, 23, 35, 21, "assign"], [39, 29, 35, 27], [39, 30, 35, 28, "current"], [39, 37, 35, 35], [39, 38, 35, 36, "style"], [39, 43, 35, 41], [39, 45, 35, 43, "partialStyle"], [39, 57, 35, 55], [39, 58, 35, 56], [40, 14, 36, 12], [41, 14, 37, 12], [42, 12, 38, 10], [42, 17, 38, 15], [42, 23, 38, 21], [43, 14, 39, 12], [43, 18, 39, 16, "cleanValue"], [43, 28, 39, 26], [43, 32, 39, 30], [43, 39, 39, 37, "cleanValue"], [43, 49, 39, 47], [43, 54, 39, 52], [43, 62, 39, 60], [43, 64, 39, 62], [44, 16, 40, 14], [44, 22, 40, 20, "value"], [44, 27, 40, 25], [44, 30, 40, 28, "cleanValue"], [44, 40, 40, 38], [45, 16, 41, 14, "current"], [45, 23, 41, 21], [45, 24, 41, 22, "setAttribute"], [45, 36, 41, 34], [45, 37, 41, 35], [45, 43, 41, 41], [45, 45, 41, 43], [45, 49, 41, 43, "convertInt32ColorToRGBA"], [45, 91, 41, 66], [45, 93, 41, 67, "value"], [45, 98, 41, 72], [45, 99, 41, 73, "payload"], [45, 106, 41, 80], [45, 107, 41, 81], [45, 108, 41, 82], [46, 14, 42, 12], [47, 14, 43, 12], [48, 12, 44, 10], [48, 17, 44, 15], [48, 25, 44, 23], [49, 14, 45, 12], [49, 18, 45, 16, "cleanValue"], [49, 28, 45, 26], [49, 32, 45, 30], [49, 39, 45, 37, "cleanValue"], [49, 49, 45, 47], [49, 54, 45, 52], [49, 62, 45, 60], [49, 64, 45, 62], [50, 16, 46, 14], [50, 22, 46, 20, "value"], [50, 27, 46, 25], [50, 30, 46, 28, "cleanValue"], [50, 40, 46, 38], [51, 16, 47, 14, "current"], [51, 23, 47, 21], [51, 24, 47, 22, "setAttribute"], [51, 36, 47, 34], [51, 37, 47, 35], [51, 45, 47, 43], [51, 47, 47, 45], [51, 51, 47, 45, "convertInt32ColorToRGBA"], [51, 93, 47, 68], [51, 95, 47, 69, "value"], [51, 100, 47, 74], [51, 101, 47, 75, "payload"], [51, 108, 47, 82], [51, 109, 47, 83], [51, 110, 47, 84], [52, 14, 48, 12], [53, 14, 49, 12], [54, 12, 50, 10], [55, 14, 51, 12], [56, 14, 52, 12], [57, 14, 53, 12], [58, 14, 54, 12, "current"], [58, 21, 54, 19], [58, 22, 54, 20, "setAttribute"], [58, 34, 54, 32], [58, 35, 54, 33], [58, 39, 54, 33, "camelCaseToDashed"], [58, 63, 54, 50], [58, 65, 54, 51, "cleanAttribute"], [58, 79, 54, 65], [58, 80, 54, 66], [58, 82, 54, 68, "cleanValue"], [58, 92, 54, 78], [58, 93, 54, 79], [59, 14, 55, 12], [60, 10, 56, 8], [61, 8, 57, 6], [62, 6, 58, 4], [63, 4, 59, 2], [64, 4, 60, 2, "constructor"], [64, 15, 60, 13, "constructor"], [64, 16, 60, 14, "props"], [64, 21, 60, 19], [64, 23, 60, 21], [65, 6, 61, 4], [65, 11, 61, 9], [65, 12, 61, 10, "props"], [65, 17, 61, 15], [65, 18, 61, 16], [67, 6, 63, 4], [68, 6, 64, 4], [68, 10, 64, 8], [68, 14, 64, 8, "hasTouchableProperty"], [68, 47, 64, 28], [68, 49, 64, 29, "props"], [68, 54, 64, 34], [68, 55, 64, 35], [68, 57, 64, 37], [69, 8, 65, 6], [69, 12, 65, 6, "SvgTouchableMixin"], [69, 38, 65, 23], [69, 40, 65, 24], [69, 44, 65, 28], [69, 45, 65, 29], [70, 6, 66, 4], [71, 6, 67, 4], [71, 10, 67, 8], [71, 11, 67, 9, "_remeasureMetricsOnActivation"], [71, 40, 67, 38], [71, 43, 67, 41, "remeasure"], [71, 59, 67, 50], [71, 60, 67, 51, "bind"], [71, 64, 67, 55], [71, 65, 67, 56], [71, 69, 67, 60], [71, 70, 67, 61], [72, 4, 68, 2], [73, 4, 69, 2, "render"], [73, 10, 69, 8, "render"], [73, 11, 69, 8], [73, 13, 69, 11], [74, 6, 70, 4], [74, 10, 70, 8], [74, 11, 70, 9], [74, 15, 70, 13], [74, 16, 70, 14, "tag"], [74, 19, 70, 17], [74, 21, 70, 19], [75, 8, 71, 6], [75, 14, 71, 12], [75, 18, 71, 16, "Error"], [75, 23, 71, 21], [75, 24, 71, 22], [75, 99, 71, 97], [75, 100, 71, 98], [76, 6, 72, 4], [77, 6, 73, 4], [77, 10, 73, 8], [77, 11, 73, 9, "lastMergedProps"], [77, 26, 73, 24], [77, 29, 73, 27], [77, 30, 73, 28], [77, 31, 73, 29], [78, 6, 74, 4], [78, 13, 74, 11], [78, 17, 74, 11, "createElement"], [78, 39, 74, 24], [78, 41, 74, 25], [78, 45, 74, 29], [78, 46, 74, 30, "tag"], [78, 49, 74, 33], [78, 51, 74, 35], [78, 55, 74, 35, "prepare"], [78, 71, 74, 42], [78, 73, 74, 43], [78, 77, 74, 47], [78, 79, 74, 49], [78, 83, 74, 53], [78, 84, 74, 54, "prepareProps"], [78, 96, 74, 66], [78, 97, 74, 67], [78, 101, 74, 71], [78, 102, 74, 72, "props"], [78, 107, 74, 77], [78, 108, 74, 78], [78, 109, 74, 79], [78, 110, 74, 80], [79, 4, 75, 2], [80, 2, 76, 0], [81, 2, 76, 1, "exports"], [81, 9, 76, 1], [81, 10, 76, 1, "WebShape"], [81, 18, 76, 1], [81, 21, 76, 1, "WebShape"], [81, 29, 76, 1], [82, 0, 76, 1], [82, 3]], "functionMap": {"names": ["<global>", "WebShape", "prepareProps", "setNativeProps", "constructor", "render"], "mappings": "AAA;OCS;ECC;GDE;EEO;GFuC;EGC;GHQ;EIC;GJM;CDC"}}, "type": "js/module"}]}