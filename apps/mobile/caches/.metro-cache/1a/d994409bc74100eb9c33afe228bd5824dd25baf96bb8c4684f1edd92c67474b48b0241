{"dependencies": [{"name": "../../../Libraries/Network/XMLHttpRequest", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 54}}], "key": "qNDGQJ0hVRex3vOJLGddOAHrNJY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var XMLHttpRequest = require(_dependencyMap[0], \"../../../Libraries/Network/XMLHttpRequest\").default;\n  var originalXHROpen = XMLHttpRequest.prototype.open;\n  var originalXHRSend = XMLHttpRequest.prototype.send;\n  var originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;\n  var openCallback;\n  var sendCallback;\n  var requestHeaderCallback;\n  var headerReceivedCallback;\n  var responseCallback;\n  var isInterceptorEnabled = false;\n  var XHRInterceptor = {\n    setOpenCallback(callback) {\n      openCallback = callback;\n    },\n    setSendCallback(callback) {\n      sendCallback = callback;\n    },\n    setHeaderReceivedCallback(callback) {\n      headerReceivedCallback = callback;\n    },\n    setResponseCallback(callback) {\n      responseCallback = callback;\n    },\n    setRequestHeaderCallback(callback) {\n      requestHeaderCallback = callback;\n    },\n    isInterceptorEnabled() {\n      return isInterceptorEnabled;\n    },\n    enableInterception() {\n      if (isInterceptorEnabled) {\n        return;\n      }\n      XMLHttpRequest.prototype.open = function (method, url) {\n        if (openCallback) {\n          openCallback(method, url, this);\n        }\n        originalXHROpen.apply(this, arguments);\n      };\n      XMLHttpRequest.prototype.setRequestHeader = function (header, value) {\n        if (requestHeaderCallback) {\n          requestHeaderCallback(header, value, this);\n        }\n        originalXHRSetRequestHeader.apply(this, arguments);\n      };\n      XMLHttpRequest.prototype.send = function (data) {\n        if (sendCallback) {\n          sendCallback(data, this);\n        }\n        if (this.addEventListener) {\n          this.addEventListener('readystatechange', () => {\n            if (!isInterceptorEnabled) {\n              return;\n            }\n            if (this.readyState === this.HEADERS_RECEIVED) {\n              var contentTypeString = this.getResponseHeader('Content-Type');\n              var contentLengthString = this.getResponseHeader('Content-Length');\n              var _responseContentType, _responseSize;\n              if (contentTypeString) {\n                _responseContentType = contentTypeString.split(';')[0];\n              }\n              if (contentLengthString) {\n                _responseSize = parseInt(contentLengthString, 10);\n              }\n              if (headerReceivedCallback) {\n                headerReceivedCallback(_responseContentType, _responseSize, this.getAllResponseHeaders(), this);\n              }\n            }\n            if (this.readyState === this.DONE) {\n              if (responseCallback) {\n                responseCallback(this.status, this.timeout, this.response, this.responseURL, this.responseType, this);\n              }\n            }\n          }, false);\n        }\n        originalXHRSend.apply(this, arguments);\n      };\n      isInterceptorEnabled = true;\n    },\n    disableInterception() {\n      if (!isInterceptorEnabled) {\n        return;\n      }\n      isInterceptorEnabled = false;\n      XMLHttpRequest.prototype.send = originalXHRSend;\n      XMLHttpRequest.prototype.open = originalXHROpen;\n      XMLHttpRequest.prototype.setRequestHeader = originalXHRSetRequestHeader;\n      responseCallback = null;\n      openCallback = null;\n      sendCallback = null;\n      headerReceivedCallback = null;\n      requestHeaderCallback = null;\n    }\n  };\n  var _default = exports.default = XHRInterceptor;\n});", "lineCount": 103, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 15, 0], [8, 6, 15, 6, "XMLHttpRequest"], [8, 20, 15, 37], [8, 23, 16, 2, "require"], [8, 30, 16, 9], [8, 31, 16, 9, "_dependencyMap"], [8, 45, 16, 9], [8, 93, 16, 53], [8, 94, 16, 54], [8, 95, 16, 55, "default"], [8, 102, 16, 62], [9, 2, 18, 0], [9, 6, 18, 6, "originalXHROpen"], [9, 21, 18, 21], [9, 24, 18, 24, "XMLHttpRequest"], [9, 38, 18, 38], [9, 39, 18, 39, "prototype"], [9, 48, 18, 48], [9, 49, 18, 49, "open"], [9, 53, 18, 53], [10, 2, 20, 0], [10, 6, 20, 6, "originalXHRSend"], [10, 21, 20, 21], [10, 24, 20, 24, "XMLHttpRequest"], [10, 38, 20, 38], [10, 39, 20, 39, "prototype"], [10, 48, 20, 48], [10, 49, 20, 49, "send"], [10, 53, 20, 53], [11, 2, 22, 0], [11, 6, 22, 6, "originalXHRSetRequestHeader"], [11, 33, 22, 33], [11, 36, 22, 36, "XMLHttpRequest"], [11, 50, 22, 50], [11, 51, 22, 51, "prototype"], [11, 60, 22, 60], [11, 61, 22, 61, "setRequestHeader"], [11, 77, 22, 77], [12, 2, 57, 0], [12, 6, 57, 4, "openCallback"], [12, 18, 57, 51], [13, 2, 58, 0], [13, 6, 58, 4, "send<PERSON><PERSON>back"], [13, 18, 58, 51], [14, 2, 59, 0], [14, 6, 59, 4, "requestHeaderCallback"], [14, 27, 59, 69], [15, 2, 60, 0], [15, 6, 60, 4, "headerReceivedCallback"], [15, 28, 60, 71], [16, 2, 61, 0], [16, 6, 61, 4, "responseCallback"], [16, 22, 61, 59], [17, 2, 63, 0], [17, 6, 63, 4, "isInterceptorEnabled"], [17, 26, 63, 24], [17, 29, 63, 27], [17, 34, 63, 32], [18, 2, 72, 0], [18, 6, 72, 6, "XHRInterceptor"], [18, 20, 72, 20], [18, 23, 72, 23], [19, 4, 76, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 19, 76, 17, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 20, 76, 18, "callback"], [19, 28, 76, 54], [19, 30, 76, 56], [20, 6, 77, 4, "openCallback"], [20, 18, 77, 16], [20, 21, 77, 19, "callback"], [20, 29, 77, 27], [21, 4, 78, 2], [21, 5, 78, 3], [22, 4, 83, 2, "setSendCallback"], [22, 19, 83, 17, "setSendCallback"], [22, 20, 83, 18, "callback"], [22, 28, 83, 54], [22, 30, 83, 56], [23, 6, 84, 4, "send<PERSON><PERSON>back"], [23, 18, 84, 16], [23, 21, 84, 19, "callback"], [23, 29, 84, 27], [24, 4, 85, 2], [24, 5, 85, 3], [25, 4, 90, 2, "setHeaderReceivedCallback"], [25, 29, 90, 27, "setHeaderReceivedCallback"], [25, 30, 90, 28, "callback"], [25, 38, 90, 74], [25, 40, 90, 76], [26, 6, 91, 4, "headerReceivedCallback"], [26, 28, 91, 26], [26, 31, 91, 29, "callback"], [26, 39, 91, 37], [27, 4, 92, 2], [27, 5, 92, 3], [28, 4, 97, 2, "setResponseCallback"], [28, 23, 97, 21, "setResponseCallback"], [28, 24, 97, 22, "callback"], [28, 32, 97, 62], [28, 34, 97, 64], [29, 6, 98, 4, "responseCallback"], [29, 22, 98, 20], [29, 25, 98, 23, "callback"], [29, 33, 98, 31], [30, 4, 99, 2], [30, 5, 99, 3], [31, 4, 104, 2, "setRequestHeaderCallback"], [31, 28, 104, 26, "setRequestHeaderCallback"], [31, 29, 104, 27, "callback"], [31, 37, 104, 72], [31, 39, 104, 74], [32, 6, 105, 4, "requestHeaderCallback"], [32, 27, 105, 25], [32, 30, 105, 28, "callback"], [32, 38, 105, 36], [33, 4, 106, 2], [33, 5, 106, 3], [34, 4, 108, 2, "isInterceptorEnabled"], [34, 24, 108, 22, "isInterceptorEnabled"], [34, 25, 108, 22], [34, 27, 108, 34], [35, 6, 109, 4], [35, 13, 109, 11, "isInterceptorEnabled"], [35, 33, 109, 31], [36, 4, 110, 2], [36, 5, 110, 3], [37, 4, 112, 2, "enableInterception"], [37, 22, 112, 20, "enableInterception"], [37, 23, 112, 20], [37, 25, 112, 23], [38, 6, 113, 4], [38, 10, 113, 8, "isInterceptorEnabled"], [38, 30, 113, 28], [38, 32, 113, 30], [39, 8, 114, 6], [40, 6, 115, 4], [41, 6, 120, 4, "XMLHttpRequest"], [41, 20, 120, 18], [41, 21, 120, 19, "prototype"], [41, 30, 120, 28], [41, 31, 120, 29, "open"], [41, 35, 120, 33], [41, 38, 120, 36], [41, 48, 120, 46, "method"], [41, 54, 120, 60], [41, 56, 120, 62, "url"], [41, 59, 120, 73], [41, 61, 120, 75], [42, 8, 121, 6], [42, 12, 121, 10, "openCallback"], [42, 24, 121, 22], [42, 26, 121, 24], [43, 10, 122, 8, "openCallback"], [43, 22, 122, 20], [43, 23, 122, 21, "method"], [43, 29, 122, 27], [43, 31, 122, 29, "url"], [43, 34, 122, 32], [43, 36, 122, 34], [43, 40, 122, 38], [43, 41, 122, 39], [44, 8, 123, 6], [45, 8, 124, 6, "originalXHROpen"], [45, 23, 124, 21], [45, 24, 124, 22, "apply"], [45, 29, 124, 27], [45, 30, 124, 28], [45, 34, 124, 32], [45, 36, 124, 34, "arguments"], [45, 45, 124, 43], [45, 46, 124, 44], [46, 6, 125, 4], [46, 7, 125, 5], [47, 6, 131, 4, "XMLHttpRequest"], [47, 20, 131, 18], [47, 21, 131, 19, "prototype"], [47, 30, 131, 28], [47, 31, 131, 29, "setRequestHeader"], [47, 47, 131, 45], [47, 50, 131, 48], [47, 60, 132, 6, "header"], [47, 66, 132, 20], [47, 68, 133, 6, "value"], [47, 73, 133, 19], [47, 75, 134, 6], [48, 8, 135, 6], [48, 12, 135, 10, "requestHeaderCallback"], [48, 33, 135, 31], [48, 35, 135, 33], [49, 10, 136, 8, "requestHeaderCallback"], [49, 31, 136, 29], [49, 32, 136, 30, "header"], [49, 38, 136, 36], [49, 40, 136, 38, "value"], [49, 45, 136, 43], [49, 47, 136, 45], [49, 51, 136, 49], [49, 52, 136, 50], [50, 8, 137, 6], [51, 8, 138, 6, "originalXHRSetRequestHeader"], [51, 35, 138, 33], [51, 36, 138, 34, "apply"], [51, 41, 138, 39], [51, 42, 138, 40], [51, 46, 138, 44], [51, 48, 138, 46, "arguments"], [51, 57, 138, 55], [51, 58, 138, 56], [52, 6, 139, 4], [52, 7, 139, 5], [53, 6, 145, 4, "XMLHttpRequest"], [53, 20, 145, 18], [53, 21, 145, 19, "prototype"], [53, 30, 145, 28], [53, 31, 145, 29, "send"], [53, 35, 145, 33], [53, 38, 145, 36], [53, 48, 145, 46, "data"], [53, 52, 145, 58], [53, 54, 145, 60], [54, 8, 146, 6], [54, 12, 146, 10, "send<PERSON><PERSON>back"], [54, 24, 146, 22], [54, 26, 146, 24], [55, 10, 147, 8, "send<PERSON><PERSON>back"], [55, 22, 147, 20], [55, 23, 147, 21, "data"], [55, 27, 147, 25], [55, 29, 147, 27], [55, 33, 147, 31], [55, 34, 147, 32], [56, 8, 148, 6], [57, 8, 149, 6], [57, 12, 149, 10], [57, 16, 149, 14], [57, 17, 149, 15, "addEventListener"], [57, 33, 149, 31], [57, 35, 149, 33], [58, 10, 150, 8], [58, 14, 150, 12], [58, 15, 150, 13, "addEventListener"], [58, 31, 150, 29], [58, 32, 151, 10], [58, 50, 151, 28], [58, 52, 152, 10], [58, 58, 152, 16], [59, 12, 153, 12], [59, 16, 153, 16], [59, 17, 153, 17, "isInterceptorEnabled"], [59, 37, 153, 37], [59, 39, 153, 39], [60, 14, 154, 14], [61, 12, 155, 12], [62, 12, 156, 12], [62, 16, 156, 16], [62, 20, 156, 20], [62, 21, 156, 21, "readyState"], [62, 31, 156, 31], [62, 36, 156, 36], [62, 40, 156, 40], [62, 41, 156, 41, "HEADERS_RECEIVED"], [62, 57, 156, 57], [62, 59, 156, 59], [63, 14, 157, 14], [63, 18, 157, 20, "contentTypeString"], [63, 35, 157, 37], [63, 38, 157, 40], [63, 42, 157, 44], [63, 43, 157, 45, "getResponseHeader"], [63, 60, 157, 62], [63, 61, 157, 63], [63, 75, 157, 77], [63, 76, 157, 78], [64, 14, 158, 14], [64, 18, 158, 20, "contentLengthString"], [64, 37, 158, 39], [64, 40, 159, 16], [64, 44, 159, 20], [64, 45, 159, 21, "getResponseHeader"], [64, 62, 159, 38], [64, 63, 159, 39], [64, 79, 159, 55], [64, 80, 159, 56], [65, 14, 160, 14], [65, 18, 160, 18, "responseContentType"], [65, 38, 160, 37], [65, 40, 160, 39, "responseSize"], [65, 53, 160, 51], [66, 14, 161, 14], [66, 18, 161, 18, "contentTypeString"], [66, 35, 161, 35], [66, 37, 161, 37], [67, 16, 162, 16, "responseContentType"], [67, 36, 162, 35], [67, 39, 162, 38, "contentTypeString"], [67, 56, 162, 55], [67, 57, 162, 56, "split"], [67, 62, 162, 61], [67, 63, 162, 62], [67, 66, 162, 65], [67, 67, 162, 66], [67, 68, 162, 67], [67, 69, 162, 68], [67, 70, 162, 69], [68, 14, 163, 14], [69, 14, 164, 14], [69, 18, 164, 18, "contentLengthString"], [69, 37, 164, 37], [69, 39, 164, 39], [70, 16, 165, 16, "responseSize"], [70, 29, 165, 28], [70, 32, 165, 31, "parseInt"], [70, 40, 165, 39], [70, 41, 165, 40, "contentLengthString"], [70, 60, 165, 59], [70, 62, 165, 61], [70, 64, 165, 63], [70, 65, 165, 64], [71, 14, 166, 14], [72, 14, 167, 14], [72, 18, 167, 18, "headerReceivedCallback"], [72, 40, 167, 40], [72, 42, 167, 42], [73, 16, 168, 16, "headerReceivedCallback"], [73, 38, 168, 38], [73, 39, 169, 18, "responseContentType"], [73, 59, 169, 37], [73, 61, 170, 18, "responseSize"], [73, 74, 170, 30], [73, 76, 171, 18], [73, 80, 171, 22], [73, 81, 171, 23, "getAllResponseHeaders"], [73, 102, 171, 44], [73, 103, 171, 45], [73, 104, 171, 46], [73, 106, 172, 18], [73, 110, 173, 16], [73, 111, 173, 17], [74, 14, 174, 14], [75, 12, 175, 12], [76, 12, 176, 12], [76, 16, 176, 16], [76, 20, 176, 20], [76, 21, 176, 21, "readyState"], [76, 31, 176, 31], [76, 36, 176, 36], [76, 40, 176, 40], [76, 41, 176, 41, "DONE"], [76, 45, 176, 45], [76, 47, 176, 47], [77, 14, 177, 14], [77, 18, 177, 18, "responseCallback"], [77, 34, 177, 34], [77, 36, 177, 36], [78, 16, 178, 16, "responseCallback"], [78, 32, 178, 32], [78, 33, 179, 18], [78, 37, 179, 22], [78, 38, 179, 23, "status"], [78, 44, 179, 29], [78, 46, 180, 18], [78, 50, 180, 22], [78, 51, 180, 23, "timeout"], [78, 58, 180, 30], [78, 60, 181, 18], [78, 64, 181, 22], [78, 65, 181, 23, "response"], [78, 73, 181, 31], [78, 75, 182, 18], [78, 79, 182, 22], [78, 80, 182, 23, "responseURL"], [78, 91, 182, 34], [78, 93, 183, 18], [78, 97, 183, 22], [78, 98, 183, 23, "responseType"], [78, 110, 183, 35], [78, 112, 184, 18], [78, 116, 185, 16], [78, 117, 185, 17], [79, 14, 186, 14], [80, 12, 187, 12], [81, 10, 188, 10], [81, 11, 188, 11], [81, 13, 189, 10], [81, 18, 190, 8], [81, 19, 190, 9], [82, 8, 191, 6], [83, 8, 192, 6, "originalXHRSend"], [83, 23, 192, 21], [83, 24, 192, 22, "apply"], [83, 29, 192, 27], [83, 30, 192, 28], [83, 34, 192, 32], [83, 36, 192, 34, "arguments"], [83, 45, 192, 43], [83, 46, 192, 44], [84, 6, 193, 4], [84, 7, 193, 5], [85, 6, 194, 4, "isInterceptorEnabled"], [85, 26, 194, 24], [85, 29, 194, 27], [85, 33, 194, 31], [86, 4, 195, 2], [86, 5, 195, 3], [87, 4, 198, 2, "disableInterception"], [87, 23, 198, 21, "disableInterception"], [87, 24, 198, 21], [87, 26, 198, 24], [88, 6, 199, 4], [88, 10, 199, 8], [88, 11, 199, 9, "isInterceptorEnabled"], [88, 31, 199, 29], [88, 33, 199, 31], [89, 8, 200, 6], [90, 6, 201, 4], [91, 6, 202, 4, "isInterceptorEnabled"], [91, 26, 202, 24], [91, 29, 202, 27], [91, 34, 202, 32], [92, 6, 204, 4, "XMLHttpRequest"], [92, 20, 204, 18], [92, 21, 204, 19, "prototype"], [92, 30, 204, 28], [92, 31, 204, 29, "send"], [92, 35, 204, 33], [92, 38, 204, 36, "originalXHRSend"], [92, 53, 204, 51], [93, 6, 206, 4, "XMLHttpRequest"], [93, 20, 206, 18], [93, 21, 206, 19, "prototype"], [93, 30, 206, 28], [93, 31, 206, 29, "open"], [93, 35, 206, 33], [93, 38, 206, 36, "originalXHROpen"], [93, 53, 206, 51], [94, 6, 208, 4, "XMLHttpRequest"], [94, 20, 208, 18], [94, 21, 208, 19, "prototype"], [94, 30, 208, 28], [94, 31, 208, 29, "setRequestHeader"], [94, 47, 208, 45], [94, 50, 208, 48, "originalXHRSetRequestHeader"], [94, 77, 208, 75], [95, 6, 209, 4, "responseCallback"], [95, 22, 209, 20], [95, 25, 209, 23], [95, 29, 209, 27], [96, 6, 210, 4, "openCallback"], [96, 18, 210, 16], [96, 21, 210, 19], [96, 25, 210, 23], [97, 6, 211, 4, "send<PERSON><PERSON>back"], [97, 18, 211, 16], [97, 21, 211, 19], [97, 25, 211, 23], [98, 6, 212, 4, "headerReceivedCallback"], [98, 28, 212, 26], [98, 31, 212, 29], [98, 35, 212, 33], [99, 6, 213, 4, "requestHeaderCallback"], [99, 27, 213, 25], [99, 30, 213, 28], [99, 34, 213, 32], [100, 4, 214, 2], [101, 2, 215, 0], [101, 3, 215, 1], [102, 2, 215, 2], [102, 6, 215, 2, "_default"], [102, 14, 215, 2], [102, 17, 215, 2, "exports"], [102, 24, 215, 2], [102, 25, 215, 2, "default"], [102, 32, 215, 2], [102, 35, 217, 15, "XHRInterceptor"], [102, 49, 217, 29], [103, 0, 217, 29], [103, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSendCallback", "setHeaderReceivedCallback", "setResponseCallback", "setRequestHeaderCallback", "isInterceptorEnabled", "enableInterception", "XMLHttpRequest.prototype.open", "XMLHttpRequest.prototype.setRequestHeader", "XMLHttpRequest.prototype.send", "addEventListener$argument_1", "disableInterception"], "mappings": "AAA;EC2E;GDE;EEK;GFE;EGK;GHE;EIK;GJE;EKK;GLE;EME;GNE;EOE;oCCQ;KDK;gDEM;KFQ;oCGM;UCO;WDoC;KHK;GPE;EYG;GZgB"}}, "type": "js/module"}]}