{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var FireExtinguisher = exports.default = (0, _createLucideIcon.default)(\"FireExtinguisher\", [[\"path\", {\n    d: \"M15 6.5V3a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v3.5\",\n    key: \"sqyvz\"\n  }], [\"path\", {\n    d: \"M9 18h8\",\n    key: \"i7pszb\"\n  }], [\"path\", {\n    d: \"M18 3h-3\",\n    key: \"7idoqj\"\n  }], [\"path\", {\n    d: \"M11 3a6 6 0 0 0-6 6v11\",\n    key: \"1v5je3\"\n  }], [\"path\", {\n    d: \"M5 13h4\",\n    key: \"svpcxo\"\n  }], [\"path\", {\n    d: \"M17 10a4 4 0 0 0-8 0v10a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2Z\",\n    key: \"vsjego\"\n  }]]);\n});", "lineCount": 34, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "FireExtinguisher"], [15, 22, 10, 22], [15, 25, 10, 22, "exports"], [15, 32, 10, 22], [15, 33, 10, 22, "default"], [15, 40, 10, 22], [15, 43, 10, 25], [15, 47, 10, 25, "createLucideIcon"], [15, 72, 10, 41], [15, 74, 10, 42], [15, 92, 10, 60], [15, 94, 10, 62], [15, 95, 11, 2], [15, 96, 11, 3], [15, 102, 11, 9], [15, 104, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 53, 11, 62], [17, 4, 11, 64, "key"], [17, 7, 11, 67], [17, 9, 11, 69], [18, 2, 11, 77], [18, 3, 11, 78], [18, 4, 11, 79], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 17, 13, 26], [23, 4, 13, 28, "key"], [23, 7, 13, 31], [23, 9, 13, 33], [24, 2, 13, 42], [24, 3, 13, 43], [24, 4, 13, 44], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 31, 14, 40], [26, 4, 14, 42, "key"], [26, 7, 14, 45], [26, 9, 14, 47], [27, 2, 14, 56], [27, 3, 14, 57], [27, 4, 14, 58], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 16, 15, 25], [29, 4, 15, 27, "key"], [29, 7, 15, 30], [29, 9, 15, 32], [30, 2, 15, 41], [30, 3, 15, 42], [30, 4, 15, 43], [30, 6, 16, 2], [30, 7, 16, 3], [30, 13, 16, 9], [30, 15, 16, 11], [31, 4, 16, 13, "d"], [31, 5, 16, 14], [31, 7, 16, 16], [31, 63, 16, 72], [32, 4, 16, 74, "key"], [32, 7, 16, 77], [32, 9, 16, 79], [33, 2, 16, 88], [33, 3, 16, 89], [33, 4, 16, 90], [33, 5, 17, 1], [33, 6, 17, 2], [34, 0, 17, 3], [34, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}