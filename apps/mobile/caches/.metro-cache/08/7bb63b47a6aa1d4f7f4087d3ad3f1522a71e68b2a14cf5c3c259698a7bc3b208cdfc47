{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 38, "index": 53}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ShiftSpec = exports.FadeSpec = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var FadeSpec = exports.FadeSpec = {\n    animation: 'timing',\n    config: {\n      duration: 150,\n      easing: _reactNative.Easing.in(_reactNative.Easing.linear)\n    }\n  };\n  var ShiftSpec = exports.ShiftSpec = {\n    animation: 'timing',\n    config: {\n      duration: 150,\n      easing: _reactNative.Easing.inOut(_reactNative.Easing.ease)\n    }\n  };\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ShiftSpec"], [7, 19, 1, 13], [7, 22, 1, 13, "exports"], [7, 29, 1, 13], [7, 30, 1, 13, "FadeSpec"], [7, 38, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_reactNative"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 4, 7], [9, 6, 4, 13, "FadeSpec"], [9, 14, 4, 21], [9, 17, 4, 21, "exports"], [9, 24, 4, 21], [9, 25, 4, 21, "FadeSpec"], [9, 33, 4, 21], [9, 36, 4, 24], [10, 4, 5, 2, "animation"], [10, 13, 5, 11], [10, 15, 5, 13], [10, 23, 5, 21], [11, 4, 6, 2, "config"], [11, 10, 6, 8], [11, 12, 6, 10], [12, 6, 7, 4, "duration"], [12, 14, 7, 12], [12, 16, 7, 14], [12, 19, 7, 17], [13, 6, 8, 4, "easing"], [13, 12, 8, 10], [13, 14, 8, 12, "Easing"], [13, 33, 8, 18], [13, 34, 8, 19, "in"], [13, 36, 8, 21], [13, 37, 8, 22, "Easing"], [13, 56, 8, 28], [13, 57, 8, 29, "linear"], [13, 63, 8, 35], [14, 4, 9, 2], [15, 2, 10, 0], [15, 3, 10, 1], [16, 2, 11, 7], [16, 6, 11, 13, "ShiftSpec"], [16, 15, 11, 22], [16, 18, 11, 22, "exports"], [16, 25, 11, 22], [16, 26, 11, 22, "ShiftSpec"], [16, 35, 11, 22], [16, 38, 11, 25], [17, 4, 12, 2, "animation"], [17, 13, 12, 11], [17, 15, 12, 13], [17, 23, 12, 21], [18, 4, 13, 2, "config"], [18, 10, 13, 8], [18, 12, 13, 10], [19, 6, 14, 4, "duration"], [19, 14, 14, 12], [19, 16, 14, 14], [19, 19, 14, 17], [20, 6, 15, 4, "easing"], [20, 12, 15, 10], [20, 14, 15, 12, "Easing"], [20, 33, 15, 18], [20, 34, 15, 19, "inOut"], [20, 39, 15, 24], [20, 40, 15, 25, "Easing"], [20, 59, 15, 31], [20, 60, 15, 32, "ease"], [20, 64, 15, 36], [21, 4, 16, 2], [22, 2, 17, 0], [22, 3, 17, 1], [23, 0, 17, 2], [23, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}