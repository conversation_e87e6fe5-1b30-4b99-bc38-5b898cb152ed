{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 80}, "end": {"line": 4, "column": 36, "index": 116}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 117}, "end": {"line": 5, "column": 33, "index": 150}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}, {"name": "./PressGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 151}, "end": {"line": 6, "column": 56, "index": 207}}], "key": "K7JCCzt/0tQ/4D2w8oVjath5+4I=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 208}, "end": {"line": 7, "column": 47, "index": 255}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _State = require(_dependencyMap[2], \"../State\");\n  var _PressGestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./PressGestureHandler\"));\n  var _utils = require(_dependencyMap[4], \"./utils\");\n  /* eslint-disable eslint-comments/no-unlimited-disable */\n\n  /* eslint-disable */\n\n  class LongPressGestureHandler extends _PressGestureHandler.default {\n    get minDurationMs() {\n      // @ts-ignore FIXNE(TS)\n      return (0, _utils.isnan)(this.config.minDurationMs) ? 251 : this.config.minDurationMs;\n    }\n    get maxDist() {\n      // @ts-ignore FIXNE(TS)\n      return (0, _utils.isnan)(this.config.maxDist) ? 9 : this.config.maxDist;\n    }\n    updateHasCustomActivationCriteria({\n      maxDistSq\n    }) {\n      return !(0, _utils.isValidNumber)(maxDistSq);\n    }\n    getConfig() {\n      if (!this.hasCustomActivationCriteria) {\n        // Default config\n        // If no params have been defined then this config should emulate the native gesture as closely as possible.\n        return {\n          shouldCancelWhenOutside: true,\n          maxDistSq: 10\n        };\n      }\n      return this.config;\n    }\n    getHammerConfig() {\n      return {\n        ...super.getHammerConfig(),\n        // threshold: this.maxDist,\n        time: this.minDurationMs\n      };\n    }\n    getState(type) {\n      return {\n        [_hammerjs.default.INPUT_START]: _State.State.ACTIVE,\n        [_hammerjs.default.INPUT_MOVE]: _State.State.ACTIVE,\n        [_hammerjs.default.INPUT_END]: _State.State.END,\n        [_hammerjs.default.INPUT_CANCEL]: _State.State.FAILED\n      }[type];\n    }\n  }\n  var _default = exports.default = LongPressGestureHandler;\n});", "lineCount": 57, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_hammerjs"], [7, 15, 4, 0], [7, 18, 4, 0, "_interopRequireDefault"], [7, 40, 4, 0], [7, 41, 4, 0, "require"], [7, 48, 4, 0], [7, 49, 4, 0, "_dependencyMap"], [7, 63, 4, 0], [8, 2, 5, 0], [8, 6, 5, 0, "_State"], [8, 12, 5, 0], [8, 15, 5, 0, "require"], [8, 22, 5, 0], [8, 23, 5, 0, "_dependencyMap"], [8, 37, 5, 0], [9, 2, 6, 0], [9, 6, 6, 0, "_PressGestureHandler"], [9, 26, 6, 0], [9, 29, 6, 0, "_interopRequireDefault"], [9, 51, 6, 0], [9, 52, 6, 0, "require"], [9, 59, 6, 0], [9, 60, 6, 0, "_dependencyMap"], [9, 74, 6, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_utils"], [10, 12, 7, 0], [10, 15, 7, 0, "require"], [10, 22, 7, 0], [10, 23, 7, 0, "_dependencyMap"], [10, 37, 7, 0], [11, 2, 1, 0], [13, 2, 3, 0], [15, 2, 9, 0], [15, 8, 9, 6, "LongPressGestureHandler"], [15, 31, 9, 29], [15, 40, 9, 38, "PressGestureHandler"], [15, 68, 9, 57], [15, 69, 9, 58], [16, 4, 10, 2], [16, 8, 10, 6, "minDurationMs"], [16, 21, 10, 19, "minDurationMs"], [16, 22, 10, 19], [16, 24, 10, 22], [17, 6, 11, 4], [18, 6, 12, 4], [18, 13, 12, 11], [18, 17, 12, 11, "isnan"], [18, 29, 12, 16], [18, 31, 12, 17], [18, 35, 12, 21], [18, 36, 12, 22, "config"], [18, 42, 12, 28], [18, 43, 12, 29, "minDurationMs"], [18, 56, 12, 42], [18, 57, 12, 43], [18, 60, 12, 46], [18, 63, 12, 49], [18, 66, 12, 52], [18, 70, 12, 56], [18, 71, 12, 57, "config"], [18, 77, 12, 63], [18, 78, 12, 64, "minDurationMs"], [18, 91, 12, 77], [19, 4, 13, 2], [20, 4, 15, 2], [20, 8, 15, 6, "maxDist"], [20, 15, 15, 13, "maxDist"], [20, 16, 15, 13], [20, 18, 15, 16], [21, 6, 16, 4], [22, 6, 17, 4], [22, 13, 17, 11], [22, 17, 17, 11, "isnan"], [22, 29, 17, 16], [22, 31, 17, 17], [22, 35, 17, 21], [22, 36, 17, 22, "config"], [22, 42, 17, 28], [22, 43, 17, 29, "maxDist"], [22, 50, 17, 36], [22, 51, 17, 37], [22, 54, 17, 40], [22, 55, 17, 41], [22, 58, 17, 44], [22, 62, 17, 48], [22, 63, 17, 49, "config"], [22, 69, 17, 55], [22, 70, 17, 56, "maxDist"], [22, 77, 17, 63], [23, 4, 18, 2], [24, 4, 20, 2, "updateHasCustomActivationCriteria"], [24, 37, 20, 35, "updateHasCustomActivationCriteria"], [24, 38, 20, 36], [25, 6, 21, 4, "maxDistSq"], [26, 4, 22, 2], [26, 5, 22, 3], [26, 7, 22, 5], [27, 6, 23, 4], [27, 13, 23, 11], [27, 14, 23, 12], [27, 18, 23, 12, "isValidNumber"], [27, 38, 23, 25], [27, 40, 23, 26, "maxDistSq"], [27, 49, 23, 35], [27, 50, 23, 36], [28, 4, 24, 2], [29, 4, 26, 2, "getConfig"], [29, 13, 26, 11, "getConfig"], [29, 14, 26, 11], [29, 16, 26, 14], [30, 6, 27, 4], [30, 10, 27, 8], [30, 11, 27, 9], [30, 15, 27, 13], [30, 16, 27, 14, "hasCustomActivationCriteria"], [30, 43, 27, 41], [30, 45, 27, 43], [31, 8, 28, 6], [32, 8, 29, 6], [33, 8, 30, 6], [33, 15, 30, 13], [34, 10, 31, 8, "shouldCancelWhenOutside"], [34, 33, 31, 31], [34, 35, 31, 33], [34, 39, 31, 37], [35, 10, 32, 8, "maxDistSq"], [35, 19, 32, 17], [35, 21, 32, 19], [36, 8, 33, 6], [36, 9, 33, 7], [37, 6, 34, 4], [38, 6, 36, 4], [38, 13, 36, 11], [38, 17, 36, 15], [38, 18, 36, 16, "config"], [38, 24, 36, 22], [39, 4, 37, 2], [40, 4, 39, 2, "getHammerConfig"], [40, 19, 39, 17, "getHammerConfig"], [40, 20, 39, 17], [40, 22, 39, 20], [41, 6, 40, 4], [41, 13, 40, 11], [42, 8, 40, 13], [42, 11, 40, 16], [42, 16, 40, 21], [42, 17, 40, 22, "getHammerConfig"], [42, 32, 40, 37], [42, 33, 40, 38], [42, 34, 40, 39], [43, 8, 41, 6], [44, 8, 42, 6, "time"], [44, 12, 42, 10], [44, 14, 42, 12], [44, 18, 42, 16], [44, 19, 42, 17, "minDurationMs"], [45, 6, 43, 4], [45, 7, 43, 5], [46, 4, 44, 2], [47, 4, 46, 2, "getState"], [47, 12, 46, 10, "getState"], [47, 13, 46, 11, "type"], [47, 17, 46, 15], [47, 19, 46, 17], [48, 6, 47, 4], [48, 13, 47, 11], [49, 8, 48, 6], [49, 9, 48, 7, "Hammer"], [49, 26, 48, 13], [49, 27, 48, 14, "INPUT_START"], [49, 38, 48, 25], [49, 41, 48, 28, "State"], [49, 53, 48, 33], [49, 54, 48, 34, "ACTIVE"], [49, 60, 48, 40], [50, 8, 49, 6], [50, 9, 49, 7, "Hammer"], [50, 26, 49, 13], [50, 27, 49, 14, "INPUT_MOVE"], [50, 37, 49, 24], [50, 40, 49, 27, "State"], [50, 52, 49, 32], [50, 53, 49, 33, "ACTIVE"], [50, 59, 49, 39], [51, 8, 50, 6], [51, 9, 50, 7, "Hammer"], [51, 26, 50, 13], [51, 27, 50, 14, "INPUT_END"], [51, 36, 50, 23], [51, 39, 50, 26, "State"], [51, 51, 50, 31], [51, 52, 50, 32, "END"], [51, 55, 50, 35], [52, 8, 51, 6], [52, 9, 51, 7, "Hammer"], [52, 26, 51, 13], [52, 27, 51, 14, "INPUT_CANCEL"], [52, 39, 51, 26], [52, 42, 51, 29, "State"], [52, 54, 51, 34], [52, 55, 51, 35, "FAILED"], [53, 6, 52, 4], [53, 7, 52, 5], [53, 8, 52, 6, "type"], [53, 12, 52, 10], [53, 13, 52, 11], [54, 4, 53, 2], [55, 2, 55, 0], [56, 2, 55, 1], [56, 6, 55, 1, "_default"], [56, 14, 55, 1], [56, 17, 55, 1, "exports"], [56, 24, 55, 1], [56, 25, 55, 1, "default"], [56, 32, 55, 1], [56, 35, 57, 15, "LongPressGestureHandler"], [56, 58, 57, 38], [57, 0, 57, 38], [57, 3]], "functionMap": {"names": ["<global>", "LongPressGestureHandler", "get__minDurationMs", "get__maxDist", "updateHasCustomActivationCriteria", "getConfig", "getHammerConfig", "getState"], "mappings": "AAA;ACQ;ECC;GDG;EEE;GFG;EGE;GHI;EIE;GJW;EKE;GLK;EME;GNO;CDE"}}, "type": "js/module"}]}