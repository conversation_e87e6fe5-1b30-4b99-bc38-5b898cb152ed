{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeModulesProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "W+feuhrG2kjxGmfPOxPmPEHY8Ac=", "exportNames": ["*"]}}, {"name": "./ensureNativeModulesAreInstalled", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 84, "index": 139}}], "key": "A4316oxUZ5JztskIqVu3iyhr9Sk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.requireNativeModule = requireNativeModule;\n  exports.requireOptionalNativeModule = requireOptionalNativeModule;\n  var _NativeModulesProxy = _interopRequireDefault(require(_dependencyMap[1], \"./NativeModulesProxy\"));\n  var _ensureNativeModulesAreInstalled = require(_dependencyMap[2], \"./ensureNativeModulesAreInstalled\");\n  /**\n   * Imports the native module registered with given name. In the first place it tries to load\n   * the module installed through the JSI host object and then falls back to the bridge proxy module.\n   * Notice that the modules loaded from the proxy may not support some features like synchronous functions.\n   *\n   * @param moduleName Name of the requested native module.\n   * @returns Object representing the native module.\n   * @throws Error when there is no native module with given name.\n   */\n  function requireNativeModule(moduleName) {\n    var nativeModule = requireOptionalNativeModule(moduleName);\n    if (!nativeModule) {\n      throw new Error(`Cannot find native module '${moduleName}'`);\n    }\n    return nativeModule;\n  }\n\n  /**\n   * Imports the native module registered with the given name. The same as `requireNativeModule`,\n   * but returns `null` when the module cannot be found instead of throwing an error.\n   *\n   * @param moduleName Name of the requested native module.\n   * @returns Object representing the native module or `null` when it cannot be found.\n   */\n  function requireOptionalNativeModule(moduleName) {\n    (0, _ensureNativeModulesAreInstalled.ensureNativeModulesAreInstalled)();\n    return globalThis.expo?.modules?.[moduleName] ?? _NativeModulesProxy.default[moduleName] ?? null;\n  }\n});", "lineCount": 38, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_NativeModulesProxy"], [8, 25, 1, 0], [8, 28, 1, 0, "_interopRequireDefault"], [8, 50, 1, 0], [8, 51, 1, 0, "require"], [8, 58, 1, 0], [8, 59, 1, 0, "_dependencyMap"], [8, 73, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_ensureNativeModulesAreInstalled"], [9, 38, 2, 0], [9, 41, 2, 0, "require"], [9, 48, 2, 0], [9, 49, 2, 0, "_dependencyMap"], [9, 63, 2, 0], [10, 2, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 2, 13, 7], [19, 11, 13, 16, "requireNativeModule"], [19, 30, 13, 35, "requireNativeModule"], [19, 31, 13, 54, "moduleName"], [19, 41, 13, 72], [19, 43, 13, 86], [20, 4, 14, 2], [20, 8, 14, 8, "nativeModule"], [20, 20, 14, 20], [20, 23, 14, 23, "requireOptionalNativeModule"], [20, 50, 14, 50], [20, 51, 14, 63, "moduleName"], [20, 61, 14, 73], [20, 62, 14, 74], [21, 4, 16, 2], [21, 8, 16, 6], [21, 9, 16, 7, "nativeModule"], [21, 21, 16, 19], [21, 23, 16, 21], [22, 6, 17, 4], [22, 12, 17, 10], [22, 16, 17, 14, "Error"], [22, 21, 17, 19], [22, 22, 17, 20], [22, 52, 17, 50, "moduleName"], [22, 62, 17, 60], [22, 65, 17, 63], [22, 66, 17, 64], [23, 4, 18, 2], [24, 4, 19, 2], [24, 11, 19, 9, "nativeModule"], [24, 23, 19, 21], [25, 2, 20, 0], [27, 2, 22, 0], [28, 0, 23, 0], [29, 0, 24, 0], [30, 0, 25, 0], [31, 0, 26, 0], [32, 0, 27, 0], [33, 0, 28, 0], [34, 2, 29, 7], [34, 11, 29, 16, "requireOptionalNativeModule"], [34, 38, 29, 43, "requireOptionalNativeModule"], [34, 39, 30, 2, "moduleName"], [34, 49, 30, 20], [34, 51, 31, 21], [35, 4, 32, 2], [35, 8, 32, 2, "ensureNativeModulesAreInstalled"], [35, 72, 32, 33], [35, 74, 32, 34], [35, 75, 32, 35], [36, 4, 34, 2], [36, 11, 34, 9, "globalThis"], [36, 21, 34, 19], [36, 22, 34, 20, "expo"], [36, 26, 34, 24], [36, 28, 34, 26, "modules"], [36, 35, 34, 33], [36, 38, 34, 36, "moduleName"], [36, 48, 34, 46], [36, 49, 34, 47], [36, 53, 34, 51, "NativeModulesProxy"], [36, 80, 34, 69], [36, 81, 34, 70, "moduleName"], [36, 91, 34, 80], [36, 92, 34, 81], [36, 96, 34, 85], [36, 100, 34, 89], [37, 2, 35, 0], [38, 0, 35, 1], [38, 3]], "functionMap": {"names": ["<global>", "requireNativeModule", "requireOptionalNativeModule"], "mappings": "AAA;OCY;CDO;OES;CFM"}}, "type": "js/module"}]}