{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../PointerType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 48, "index": 259}}], "key": "PNpP2j+zRZwLQ3w6ZmXPMJNakiU=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 260}, "end": {"line": 4, "column": 36, "index": 296}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "./PointerTracker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 297}, "end": {"line": 5, "column": 46, "index": 343}}], "key": "1iEqYtl129GsycZ0U5VpXvfk4T0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _PointerType = require(_dependencyMap[1], \"../../PointerType\");\n  var _State = require(_dependencyMap[2], \"../../State\");\n  var _PointerTracker = _interopRequireDefault(require(_dependencyMap[3], \"./PointerTracker\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class GestureHandlerOrchestrator {\n    // Private beacuse of Singleton\n    // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n    constructor() {\n      _defineProperty(this, \"gestureHandlers\", []);\n      _defineProperty(this, \"awaitingHandlers\", []);\n      _defineProperty(this, \"awaitingHandlersTags\", new Set());\n      _defineProperty(this, \"handlingChangeSemaphore\", 0);\n      _defineProperty(this, \"activationIndex\", 0);\n    }\n    scheduleFinishedHandlersCleanup() {\n      if (this.handlingChangeSemaphore === 0) {\n        this.cleanupFinishedHandlers();\n      }\n    }\n    cleanHandler(handler) {\n      handler.reset();\n      handler.active = false;\n      handler.awaiting = false;\n      handler.activationIndex = Number.MAX_VALUE;\n    }\n    removeHandlerFromOrchestrator(handler) {\n      const indexInGestureHandlers = this.gestureHandlers.indexOf(handler);\n      const indexInAwaitingHandlers = this.awaitingHandlers.indexOf(handler);\n      if (indexInGestureHandlers >= 0) {\n        this.gestureHandlers.splice(indexInGestureHandlers, 1);\n      }\n      if (indexInAwaitingHandlers >= 0) {\n        this.awaitingHandlers.splice(indexInAwaitingHandlers, 1);\n        this.awaitingHandlersTags.delete(handler.handlerTag);\n      }\n    }\n    cleanupFinishedHandlers() {\n      const handlersToRemove = new Set();\n      for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n        const handler = this.gestureHandlers[i];\n        if (this.isFinished(handler.state) && !handler.awaiting) {\n          this.cleanHandler(handler);\n          handlersToRemove.add(handler);\n        }\n      }\n      this.gestureHandlers = this.gestureHandlers.filter(handler => !handlersToRemove.has(handler));\n    }\n    hasOtherHandlerToWaitFor(handler) {\n      const hasToWaitFor = otherHandler => {\n        return !this.isFinished(otherHandler.state) && this.shouldHandlerWaitForOther(handler, otherHandler);\n      };\n      return this.gestureHandlers.some(hasToWaitFor);\n    }\n    shouldBeCancelledByFinishedHandler(handler) {\n      const shouldBeCancelled = otherHandler => {\n        return this.shouldHandlerWaitForOther(handler, otherHandler) && otherHandler.state === _State.State.END;\n      };\n      return this.gestureHandlers.some(shouldBeCancelled);\n    }\n    tryActivate(handler) {\n      if (this.shouldBeCancelledByFinishedHandler(handler)) {\n        handler.cancel();\n        return;\n      }\n      if (this.hasOtherHandlerToWaitFor(handler)) {\n        this.addAwaitingHandler(handler);\n        return;\n      }\n      const handlerState = handler.state;\n      if (handlerState === _State.State.CANCELLED || handlerState === _State.State.FAILED) {\n        return;\n      }\n      if (this.shouldActivate(handler)) {\n        this.makeActive(handler);\n        return;\n      }\n      if (handlerState === _State.State.ACTIVE) {\n        handler.fail();\n        return;\n      }\n      if (handlerState === _State.State.BEGAN) {\n        handler.cancel();\n      }\n    }\n    shouldActivate(handler) {\n      const shouldBeCancelledBy = otherHandler => {\n        return this.shouldHandlerBeCancelledBy(handler, otherHandler);\n      };\n      return !this.gestureHandlers.some(shouldBeCancelledBy);\n    }\n    cleanupAwaitingHandlers(handler) {\n      const shouldWait = otherHandler => {\n        return !otherHandler.awaiting && this.shouldHandlerWaitForOther(otherHandler, handler);\n      };\n      for (const otherHandler of this.awaitingHandlers) {\n        if (shouldWait(otherHandler)) {\n          this.cleanHandler(otherHandler);\n          this.awaitingHandlersTags.delete(otherHandler.handlerTag);\n        }\n      }\n      this.awaitingHandlers = this.awaitingHandlers.filter(otherHandler => this.awaitingHandlersTags.has(otherHandler.handlerTag));\n    }\n    onHandlerStateChange(handler, newState, oldState, sendIfDisabled) {\n      if (!handler.enabled && !sendIfDisabled) {\n        return;\n      }\n      this.handlingChangeSemaphore += 1;\n      if (this.isFinished(newState)) {\n        for (const otherHandler of this.awaitingHandlers) {\n          if (!this.shouldHandlerWaitForOther(otherHandler, handler) || !this.awaitingHandlersTags.has(otherHandler.handlerTag)) {\n            continue;\n          }\n          if (newState !== _State.State.END) {\n            this.tryActivate(otherHandler);\n            continue;\n          }\n          otherHandler.cancel();\n          if (otherHandler.state === _State.State.END) {\n            // Handle edge case, where discrete gestures end immediately after activation thus\n            // their state is set to END and when the gesture they are waiting for activates they\n            // should be cancelled, however `cancel` was never sent as gestures were already in the END state.\n            // Send synthetic BEGAN -> CANCELLED to properly handle JS logic\n            otherHandler.sendEvent(_State.State.CANCELLED, _State.State.BEGAN);\n          }\n          otherHandler.awaiting = false;\n        }\n      }\n      if (newState === _State.State.ACTIVE) {\n        this.tryActivate(handler);\n      } else if (oldState === _State.State.ACTIVE || oldState === _State.State.END) {\n        if (handler.active) {\n          handler.sendEvent(newState, oldState);\n        } else if (oldState === _State.State.ACTIVE && (newState === _State.State.CANCELLED || newState === _State.State.FAILED)) {\n          handler.sendEvent(newState, _State.State.BEGAN);\n        }\n      } else if (oldState !== _State.State.UNDETERMINED || newState !== _State.State.CANCELLED) {\n        handler.sendEvent(newState, oldState);\n      }\n      this.handlingChangeSemaphore -= 1;\n      this.scheduleFinishedHandlersCleanup();\n      if (!this.awaitingHandlers.includes(handler)) {\n        this.cleanupAwaitingHandlers(handler);\n      }\n    }\n    makeActive(handler) {\n      const currentState = handler.state;\n      handler.active = true;\n      handler.shouldResetProgress = true;\n      handler.activationIndex = this.activationIndex++;\n      for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n        if (this.shouldHandlerBeCancelledBy(this.gestureHandlers[i], handler)) {\n          this.gestureHandlers[i].cancel();\n        }\n      }\n      for (const otherHandler of this.awaitingHandlers) {\n        if (this.shouldHandlerBeCancelledBy(otherHandler, handler)) {\n          otherHandler.awaiting = false;\n        }\n      }\n      handler.sendEvent(_State.State.ACTIVE, _State.State.BEGAN);\n      if (currentState !== _State.State.ACTIVE) {\n        handler.sendEvent(_State.State.END, _State.State.ACTIVE);\n        if (currentState !== _State.State.END) {\n          handler.sendEvent(_State.State.UNDETERMINED, _State.State.END);\n        }\n      }\n      if (!handler.awaiting) {\n        return;\n      }\n      handler.awaiting = false;\n      this.awaitingHandlers = this.awaitingHandlers.filter(otherHandler => otherHandler !== handler);\n    }\n    addAwaitingHandler(handler) {\n      if (this.awaitingHandlers.includes(handler)) {\n        return;\n      }\n      this.awaitingHandlers.push(handler);\n      this.awaitingHandlersTags.add(handler.handlerTag);\n      handler.awaiting = true;\n      handler.activationIndex = this.activationIndex++;\n    }\n    recordHandlerIfNotPresent(handler) {\n      if (this.gestureHandlers.includes(handler)) {\n        return;\n      }\n      this.gestureHandlers.push(handler);\n      handler.active = false;\n      handler.awaiting = false;\n      handler.activationIndex = Number.MAX_SAFE_INTEGER;\n    }\n    shouldHandlerWaitForOther(handler, otherHandler) {\n      return handler !== otherHandler && (handler.shouldWaitForHandlerFailure(otherHandler) || otherHandler.shouldRequireToWaitForFailure(handler));\n    }\n    canRunSimultaneously(gh1, gh2) {\n      return gh1 === gh2 || gh1.shouldRecognizeSimultaneously(gh2) || gh2.shouldRecognizeSimultaneously(gh1);\n    }\n    shouldHandlerBeCancelledBy(handler, otherHandler) {\n      if (this.canRunSimultaneously(handler, otherHandler)) {\n        return false;\n      }\n      if (handler.awaiting || handler.state === _State.State.ACTIVE) {\n        // For now it always returns false\n        return handler.shouldBeCancelledByOther(otherHandler);\n      }\n      const handlerPointers = handler.getTrackedPointersID();\n      const otherPointers = otherHandler.getTrackedPointersID();\n      if (!_PointerTracker.default.shareCommonPointers(handlerPointers, otherPointers) && handler.delegate.view !== otherHandler.delegate.view) {\n        return this.checkOverlap(handler, otherHandler);\n      }\n      return true;\n    }\n    checkOverlap(handler, otherHandler) {\n      // If handlers don't have common pointers, default return value is false.\n      // However, if at least on pointer overlaps with both handlers, we return true\n      // This solves issue in overlapping parents example\n      // TODO: Find better way to handle that issue, for example by activation order and handler cancelling\n      const isPointerWithinBothBounds = pointer => {\n        const point = handler.tracker.getLastAbsoluteCoords(pointer);\n        return handler.delegate.isPointerInBounds(point) && otherHandler.delegate.isPointerInBounds(point);\n      };\n      return handler.getTrackedPointersID().some(isPointerWithinBothBounds);\n    }\n    isFinished(state) {\n      return state === _State.State.END || state === _State.State.FAILED || state === _State.State.CANCELLED;\n    } // This function is called when handler receives touchdown event\n    // If handler is using mouse or pen as a pointer and any handler receives touch event,\n    // mouse/pen event dissappears - it doesn't send onPointerCancel nor onPointerUp (and others)\n    // This became a problem because handler was left at active state without any signal to end or fail\n    // To handle this, when new touch event is received, we loop through active handlers and check which type of\n    // pointer they're using. If there are any handler with mouse/pen as a pointer, we cancel them\n\n    cancelMouseAndPenGestures(currentHandler) {\n      this.gestureHandlers.forEach(handler => {\n        if (handler.pointerType !== _PointerType.PointerType.MOUSE && handler.pointerType !== _PointerType.PointerType.STYLUS) {\n          return;\n        }\n        if (handler !== currentHandler) {\n          handler.cancel();\n        } else {\n          // Handler that received touch event should have its pointer tracker reset\n          // This allows handler to smoothly change from mouse/pen to touch\n          // The drawback is, that when we try to use mouse/pen one more time, it doesn't send onPointerDown at the first time\n          // so it is required to click two times to get handler to work\n          //\n          // However, handler will receive manually created onPointerEnter that is triggered in EventManager in onPointerMove method.\n          // There may be possibility to use that fact to make handler respond properly to first mouse click\n          handler.tracker.resetTracker();\n        }\n      });\n    }\n    static get instance() {\n      if (!GestureHandlerOrchestrator._instance) {\n        GestureHandlerOrchestrator._instance = new GestureHandlerOrchestrator();\n      }\n      return GestureHandlerOrchestrator._instance;\n    }\n  }\n  exports.default = GestureHandlerOrchestrator;\n  _defineProperty(GestureHandlerOrchestrator, \"_instance\", void 0);\n});", "lineCount": 278, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_PointerType"], [7, 18, 3, 0], [7, 21, 3, 0, "require"], [7, 28, 3, 0], [7, 29, 3, 0, "_dependencyMap"], [7, 43, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_State"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_PointerTracker"], [9, 21, 5, 0], [9, 24, 5, 0, "_interopRequireDefault"], [9, 46, 5, 0], [9, 47, 5, 0, "require"], [9, 54, 5, 0], [9, 55, 5, 0, "_dependencyMap"], [9, 69, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 6, 15], [23, 8, 6, 21, "GestureHandlerOrchestrator"], [23, 34, 6, 47], [23, 35, 6, 48], [24, 4, 7, 2], [25, 4, 8, 2], [26, 4, 9, 2, "constructor"], [26, 15, 9, 13, "constructor"], [26, 16, 9, 13], [26, 18, 9, 16], [27, 6, 10, 4, "_defineProperty"], [27, 21, 10, 19], [27, 22, 10, 20], [27, 26, 10, 24], [27, 28, 10, 26], [27, 45, 10, 43], [27, 47, 10, 45], [27, 49, 10, 47], [27, 50, 10, 48], [28, 6, 12, 4, "_defineProperty"], [28, 21, 12, 19], [28, 22, 12, 20], [28, 26, 12, 24], [28, 28, 12, 26], [28, 46, 12, 44], [28, 48, 12, 46], [28, 50, 12, 48], [28, 51, 12, 49], [29, 6, 14, 4, "_defineProperty"], [29, 21, 14, 19], [29, 22, 14, 20], [29, 26, 14, 24], [29, 28, 14, 26], [29, 50, 14, 48], [29, 52, 14, 50], [29, 56, 14, 54, "Set"], [29, 59, 14, 57], [29, 60, 14, 58], [29, 61, 14, 59], [29, 62, 14, 60], [30, 6, 16, 4, "_defineProperty"], [30, 21, 16, 19], [30, 22, 16, 20], [30, 26, 16, 24], [30, 28, 16, 26], [30, 53, 16, 51], [30, 55, 16, 53], [30, 56, 16, 54], [30, 57, 16, 55], [31, 6, 18, 4, "_defineProperty"], [31, 21, 18, 19], [31, 22, 18, 20], [31, 26, 18, 24], [31, 28, 18, 26], [31, 45, 18, 43], [31, 47, 18, 45], [31, 48, 18, 46], [31, 49, 18, 47], [32, 4, 19, 2], [33, 4, 21, 2, "scheduleFinishedHandlersCleanup"], [33, 35, 21, 33, "scheduleFinishedHandlersCleanup"], [33, 36, 21, 33], [33, 38, 21, 36], [34, 6, 22, 4], [34, 10, 22, 8], [34, 14, 22, 12], [34, 15, 22, 13, "handlingChangeSemaphore"], [34, 38, 22, 36], [34, 43, 22, 41], [34, 44, 22, 42], [34, 46, 22, 44], [35, 8, 23, 6], [35, 12, 23, 10], [35, 13, 23, 11, "cleanupFinishedHandlers"], [35, 36, 23, 34], [35, 37, 23, 35], [35, 38, 23, 36], [36, 6, 24, 4], [37, 4, 25, 2], [38, 4, 27, 2, "<PERSON><PERSON><PERSON><PERSON>"], [38, 16, 27, 14, "<PERSON><PERSON><PERSON><PERSON>"], [38, 17, 27, 15, "handler"], [38, 24, 27, 22], [38, 26, 27, 24], [39, 6, 28, 4, "handler"], [39, 13, 28, 11], [39, 14, 28, 12, "reset"], [39, 19, 28, 17], [39, 20, 28, 18], [39, 21, 28, 19], [40, 6, 29, 4, "handler"], [40, 13, 29, 11], [40, 14, 29, 12, "active"], [40, 20, 29, 18], [40, 23, 29, 21], [40, 28, 29, 26], [41, 6, 30, 4, "handler"], [41, 13, 30, 11], [41, 14, 30, 12, "awaiting"], [41, 22, 30, 20], [41, 25, 30, 23], [41, 30, 30, 28], [42, 6, 31, 4, "handler"], [42, 13, 31, 11], [42, 14, 31, 12, "activationIndex"], [42, 29, 31, 27], [42, 32, 31, 30, "Number"], [42, 38, 31, 36], [42, 39, 31, 37, "MAX_VALUE"], [42, 48, 31, 46], [43, 4, 32, 2], [44, 4, 34, 2, "removeHandlerFromOrchestrator"], [44, 33, 34, 31, "removeHandlerFromOrchestrator"], [44, 34, 34, 32, "handler"], [44, 41, 34, 39], [44, 43, 34, 41], [45, 6, 35, 4], [45, 12, 35, 10, "indexInGestureHandlers"], [45, 34, 35, 32], [45, 37, 35, 35], [45, 41, 35, 39], [45, 42, 35, 40, "gestureHandlers"], [45, 57, 35, 55], [45, 58, 35, 56, "indexOf"], [45, 65, 35, 63], [45, 66, 35, 64, "handler"], [45, 73, 35, 71], [45, 74, 35, 72], [46, 6, 36, 4], [46, 12, 36, 10, "indexInAwaitingHandlers"], [46, 35, 36, 33], [46, 38, 36, 36], [46, 42, 36, 40], [46, 43, 36, 41, "awaitingHandlers"], [46, 59, 36, 57], [46, 60, 36, 58, "indexOf"], [46, 67, 36, 65], [46, 68, 36, 66, "handler"], [46, 75, 36, 73], [46, 76, 36, 74], [47, 6, 38, 4], [47, 10, 38, 8, "indexInGestureHandlers"], [47, 32, 38, 30], [47, 36, 38, 34], [47, 37, 38, 35], [47, 39, 38, 37], [48, 8, 39, 6], [48, 12, 39, 10], [48, 13, 39, 11, "gestureHandlers"], [48, 28, 39, 26], [48, 29, 39, 27, "splice"], [48, 35, 39, 33], [48, 36, 39, 34, "indexInGestureHandlers"], [48, 58, 39, 56], [48, 60, 39, 58], [48, 61, 39, 59], [48, 62, 39, 60], [49, 6, 40, 4], [50, 6, 42, 4], [50, 10, 42, 8, "indexInAwaitingHandlers"], [50, 33, 42, 31], [50, 37, 42, 35], [50, 38, 42, 36], [50, 40, 42, 38], [51, 8, 43, 6], [51, 12, 43, 10], [51, 13, 43, 11, "awaitingHandlers"], [51, 29, 43, 27], [51, 30, 43, 28, "splice"], [51, 36, 43, 34], [51, 37, 43, 35, "indexInAwaitingHandlers"], [51, 60, 43, 58], [51, 62, 43, 60], [51, 63, 43, 61], [51, 64, 43, 62], [52, 8, 44, 6], [52, 12, 44, 10], [52, 13, 44, 11, "awaitingHandlersTags"], [52, 33, 44, 31], [52, 34, 44, 32, "delete"], [52, 40, 44, 38], [52, 41, 44, 39, "handler"], [52, 48, 44, 46], [52, 49, 44, 47, "handlerTag"], [52, 59, 44, 57], [52, 60, 44, 58], [53, 6, 45, 4], [54, 4, 46, 2], [55, 4, 48, 2, "cleanupFinishedHandlers"], [55, 27, 48, 25, "cleanupFinishedHandlers"], [55, 28, 48, 25], [55, 30, 48, 28], [56, 6, 49, 4], [56, 12, 49, 10, "handlersToRemove"], [56, 28, 49, 26], [56, 31, 49, 29], [56, 35, 49, 33, "Set"], [56, 38, 49, 36], [56, 39, 49, 37], [56, 40, 49, 38], [57, 6, 51, 4], [57, 11, 51, 9], [57, 15, 51, 13, "i"], [57, 16, 51, 14], [57, 19, 51, 17], [57, 23, 51, 21], [57, 24, 51, 22, "gestureHandlers"], [57, 39, 51, 37], [57, 40, 51, 38, "length"], [57, 46, 51, 44], [57, 49, 51, 47], [57, 50, 51, 48], [57, 52, 51, 50, "i"], [57, 53, 51, 51], [57, 57, 51, 55], [57, 58, 51, 56], [57, 60, 51, 58], [57, 62, 51, 60, "i"], [57, 63, 51, 61], [57, 65, 51, 63], [58, 8, 52, 6], [58, 14, 52, 12, "handler"], [58, 21, 52, 19], [58, 24, 52, 22], [58, 28, 52, 26], [58, 29, 52, 27, "gestureHandlers"], [58, 44, 52, 42], [58, 45, 52, 43, "i"], [58, 46, 52, 44], [58, 47, 52, 45], [59, 8, 54, 6], [59, 12, 54, 10], [59, 16, 54, 14], [59, 17, 54, 15, "isFinished"], [59, 27, 54, 25], [59, 28, 54, 26, "handler"], [59, 35, 54, 33], [59, 36, 54, 34, "state"], [59, 41, 54, 39], [59, 42, 54, 40], [59, 46, 54, 44], [59, 47, 54, 45, "handler"], [59, 54, 54, 52], [59, 55, 54, 53, "awaiting"], [59, 63, 54, 61], [59, 65, 54, 63], [60, 10, 55, 8], [60, 14, 55, 12], [60, 15, 55, 13, "<PERSON><PERSON><PERSON><PERSON>"], [60, 27, 55, 25], [60, 28, 55, 26, "handler"], [60, 35, 55, 33], [60, 36, 55, 34], [61, 10, 56, 8, "handlersToRemove"], [61, 26, 56, 24], [61, 27, 56, 25, "add"], [61, 30, 56, 28], [61, 31, 56, 29, "handler"], [61, 38, 56, 36], [61, 39, 56, 37], [62, 8, 57, 6], [63, 6, 58, 4], [64, 6, 60, 4], [64, 10, 60, 8], [64, 11, 60, 9, "gestureHandlers"], [64, 26, 60, 24], [64, 29, 60, 27], [64, 33, 60, 31], [64, 34, 60, 32, "gestureHandlers"], [64, 49, 60, 47], [64, 50, 60, 48, "filter"], [64, 56, 60, 54], [64, 57, 60, 55, "handler"], [64, 64, 60, 62], [64, 68, 60, 66], [64, 69, 60, 67, "handlersToRemove"], [64, 85, 60, 83], [64, 86, 60, 84, "has"], [64, 89, 60, 87], [64, 90, 60, 88, "handler"], [64, 97, 60, 95], [64, 98, 60, 96], [64, 99, 60, 97], [65, 4, 61, 2], [66, 4, 63, 2, "hasOtherHandlerToWaitFor"], [66, 28, 63, 26, "hasOtherHandlerToWaitFor"], [66, 29, 63, 27, "handler"], [66, 36, 63, 34], [66, 38, 63, 36], [67, 6, 64, 4], [67, 12, 64, 10, "hasToWaitFor"], [67, 24, 64, 22], [67, 27, 64, 25, "<PERSON><PERSON><PERSON><PERSON>"], [67, 39, 64, 37], [67, 43, 64, 41], [68, 8, 65, 6], [68, 15, 65, 13], [68, 16, 65, 14], [68, 20, 65, 18], [68, 21, 65, 19, "isFinished"], [68, 31, 65, 29], [68, 32, 65, 30, "<PERSON><PERSON><PERSON><PERSON>"], [68, 44, 65, 42], [68, 45, 65, 43, "state"], [68, 50, 65, 48], [68, 51, 65, 49], [68, 55, 65, 53], [68, 59, 65, 57], [68, 60, 65, 58, "shouldHandlerWaitForOther"], [68, 85, 65, 83], [68, 86, 65, 84, "handler"], [68, 93, 65, 91], [68, 95, 65, 93, "<PERSON><PERSON><PERSON><PERSON>"], [68, 107, 65, 105], [68, 108, 65, 106], [69, 6, 66, 4], [69, 7, 66, 5], [70, 6, 68, 4], [70, 13, 68, 11], [70, 17, 68, 15], [70, 18, 68, 16, "gestureHandlers"], [70, 33, 68, 31], [70, 34, 68, 32, "some"], [70, 38, 68, 36], [70, 39, 68, 37, "hasToWaitFor"], [70, 51, 68, 49], [70, 52, 68, 50], [71, 4, 69, 2], [72, 4, 71, 2, "shouldBeCancelledByFinishedHandler"], [72, 38, 71, 36, "shouldBeCancelledByFinishedHandler"], [72, 39, 71, 37, "handler"], [72, 46, 71, 44], [72, 48, 71, 46], [73, 6, 72, 4], [73, 12, 72, 10, "shouldBeCancelled"], [73, 29, 72, 27], [73, 32, 72, 30, "<PERSON><PERSON><PERSON><PERSON>"], [73, 44, 72, 42], [73, 48, 72, 46], [74, 8, 73, 6], [74, 15, 73, 13], [74, 19, 73, 17], [74, 20, 73, 18, "shouldHandlerWaitForOther"], [74, 45, 73, 43], [74, 46, 73, 44, "handler"], [74, 53, 73, 51], [74, 55, 73, 53, "<PERSON><PERSON><PERSON><PERSON>"], [74, 67, 73, 65], [74, 68, 73, 66], [74, 72, 73, 70, "<PERSON><PERSON><PERSON><PERSON>"], [74, 84, 73, 82], [74, 85, 73, 83, "state"], [74, 90, 73, 88], [74, 95, 73, 93, "State"], [74, 107, 73, 98], [74, 108, 73, 99, "END"], [74, 111, 73, 102], [75, 6, 74, 4], [75, 7, 74, 5], [76, 6, 76, 4], [76, 13, 76, 11], [76, 17, 76, 15], [76, 18, 76, 16, "gestureHandlers"], [76, 33, 76, 31], [76, 34, 76, 32, "some"], [76, 38, 76, 36], [76, 39, 76, 37, "shouldBeCancelled"], [76, 56, 76, 54], [76, 57, 76, 55], [77, 4, 77, 2], [78, 4, 79, 2, "tryActivate"], [78, 15, 79, 13, "tryActivate"], [78, 16, 79, 14, "handler"], [78, 23, 79, 21], [78, 25, 79, 23], [79, 6, 80, 4], [79, 10, 80, 8], [79, 14, 80, 12], [79, 15, 80, 13, "shouldBeCancelledByFinishedHandler"], [79, 49, 80, 47], [79, 50, 80, 48, "handler"], [79, 57, 80, 55], [79, 58, 80, 56], [79, 60, 80, 58], [80, 8, 81, 6, "handler"], [80, 15, 81, 13], [80, 16, 81, 14, "cancel"], [80, 22, 81, 20], [80, 23, 81, 21], [80, 24, 81, 22], [81, 8, 82, 6], [82, 6, 83, 4], [83, 6, 85, 4], [83, 10, 85, 8], [83, 14, 85, 12], [83, 15, 85, 13, "hasOtherHandlerToWaitFor"], [83, 39, 85, 37], [83, 40, 85, 38, "handler"], [83, 47, 85, 45], [83, 48, 85, 46], [83, 50, 85, 48], [84, 8, 86, 6], [84, 12, 86, 10], [84, 13, 86, 11, "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [84, 31, 86, 29], [84, 32, 86, 30, "handler"], [84, 39, 86, 37], [84, 40, 86, 38], [85, 8, 87, 6], [86, 6, 88, 4], [87, 6, 90, 4], [87, 12, 90, 10, "handlerState"], [87, 24, 90, 22], [87, 27, 90, 25, "handler"], [87, 34, 90, 32], [87, 35, 90, 33, "state"], [87, 40, 90, 38], [88, 6, 92, 4], [88, 10, 92, 8, "handlerState"], [88, 22, 92, 20], [88, 27, 92, 25, "State"], [88, 39, 92, 30], [88, 40, 92, 31, "CANCELLED"], [88, 49, 92, 40], [88, 53, 92, 44, "handlerState"], [88, 65, 92, 56], [88, 70, 92, 61, "State"], [88, 82, 92, 66], [88, 83, 92, 67, "FAILED"], [88, 89, 92, 73], [88, 91, 92, 75], [89, 8, 93, 6], [90, 6, 94, 4], [91, 6, 96, 4], [91, 10, 96, 8], [91, 14, 96, 12], [91, 15, 96, 13, "shouldActivate"], [91, 29, 96, 27], [91, 30, 96, 28, "handler"], [91, 37, 96, 35], [91, 38, 96, 36], [91, 40, 96, 38], [92, 8, 97, 6], [92, 12, 97, 10], [92, 13, 97, 11, "makeActive"], [92, 23, 97, 21], [92, 24, 97, 22, "handler"], [92, 31, 97, 29], [92, 32, 97, 30], [93, 8, 98, 6], [94, 6, 99, 4], [95, 6, 101, 4], [95, 10, 101, 8, "handlerState"], [95, 22, 101, 20], [95, 27, 101, 25, "State"], [95, 39, 101, 30], [95, 40, 101, 31, "ACTIVE"], [95, 46, 101, 37], [95, 48, 101, 39], [96, 8, 102, 6, "handler"], [96, 15, 102, 13], [96, 16, 102, 14, "fail"], [96, 20, 102, 18], [96, 21, 102, 19], [96, 22, 102, 20], [97, 8, 103, 6], [98, 6, 104, 4], [99, 6, 106, 4], [99, 10, 106, 8, "handlerState"], [99, 22, 106, 20], [99, 27, 106, 25, "State"], [99, 39, 106, 30], [99, 40, 106, 31, "BEGAN"], [99, 45, 106, 36], [99, 47, 106, 38], [100, 8, 107, 6, "handler"], [100, 15, 107, 13], [100, 16, 107, 14, "cancel"], [100, 22, 107, 20], [100, 23, 107, 21], [100, 24, 107, 22], [101, 6, 108, 4], [102, 4, 109, 2], [103, 4, 111, 2, "shouldActivate"], [103, 18, 111, 16, "shouldActivate"], [103, 19, 111, 17, "handler"], [103, 26, 111, 24], [103, 28, 111, 26], [104, 6, 112, 4], [104, 12, 112, 10, "shouldBeCancelledBy"], [104, 31, 112, 29], [104, 34, 112, 32, "<PERSON><PERSON><PERSON><PERSON>"], [104, 46, 112, 44], [104, 50, 112, 48], [105, 8, 113, 6], [105, 15, 113, 13], [105, 19, 113, 17], [105, 20, 113, 18, "shouldHandlerBeCancelledBy"], [105, 46, 113, 44], [105, 47, 113, 45, "handler"], [105, 54, 113, 52], [105, 56, 113, 54, "<PERSON><PERSON><PERSON><PERSON>"], [105, 68, 113, 66], [105, 69, 113, 67], [106, 6, 114, 4], [106, 7, 114, 5], [107, 6, 116, 4], [107, 13, 116, 11], [107, 14, 116, 12], [107, 18, 116, 16], [107, 19, 116, 17, "gestureHandlers"], [107, 34, 116, 32], [107, 35, 116, 33, "some"], [107, 39, 116, 37], [107, 40, 116, 38, "shouldBeCancelledBy"], [107, 59, 116, 57], [107, 60, 116, 58], [108, 4, 117, 2], [109, 4, 119, 2, "cleanupAwaitingHandlers"], [109, 27, 119, 25, "cleanupAwaitingHandlers"], [109, 28, 119, 26, "handler"], [109, 35, 119, 33], [109, 37, 119, 35], [110, 6, 120, 4], [110, 12, 120, 10, "should<PERSON>ait"], [110, 22, 120, 20], [110, 25, 120, 23, "<PERSON><PERSON><PERSON><PERSON>"], [110, 37, 120, 35], [110, 41, 120, 39], [111, 8, 121, 6], [111, 15, 121, 13], [111, 16, 121, 14, "<PERSON><PERSON><PERSON><PERSON>"], [111, 28, 121, 26], [111, 29, 121, 27, "awaiting"], [111, 37, 121, 35], [111, 41, 121, 39], [111, 45, 121, 43], [111, 46, 121, 44, "shouldHandlerWaitForOther"], [111, 71, 121, 69], [111, 72, 121, 70, "<PERSON><PERSON><PERSON><PERSON>"], [111, 84, 121, 82], [111, 86, 121, 84, "handler"], [111, 93, 121, 91], [111, 94, 121, 92], [112, 6, 122, 4], [112, 7, 122, 5], [113, 6, 124, 4], [113, 11, 124, 9], [113, 17, 124, 15, "<PERSON><PERSON><PERSON><PERSON>"], [113, 29, 124, 27], [113, 33, 124, 31], [113, 37, 124, 35], [113, 38, 124, 36, "awaitingHandlers"], [113, 54, 124, 52], [113, 56, 124, 54], [114, 8, 125, 6], [114, 12, 125, 10, "should<PERSON>ait"], [114, 22, 125, 20], [114, 23, 125, 21, "<PERSON><PERSON><PERSON><PERSON>"], [114, 35, 125, 33], [114, 36, 125, 34], [114, 38, 125, 36], [115, 10, 126, 8], [115, 14, 126, 12], [115, 15, 126, 13, "<PERSON><PERSON><PERSON><PERSON>"], [115, 27, 126, 25], [115, 28, 126, 26, "<PERSON><PERSON><PERSON><PERSON>"], [115, 40, 126, 38], [115, 41, 126, 39], [116, 10, 127, 8], [116, 14, 127, 12], [116, 15, 127, 13, "awaitingHandlersTags"], [116, 35, 127, 33], [116, 36, 127, 34, "delete"], [116, 42, 127, 40], [116, 43, 127, 41, "<PERSON><PERSON><PERSON><PERSON>"], [116, 55, 127, 53], [116, 56, 127, 54, "handlerTag"], [116, 66, 127, 64], [116, 67, 127, 65], [117, 8, 128, 6], [118, 6, 129, 4], [119, 6, 131, 4], [119, 10, 131, 8], [119, 11, 131, 9, "awaitingHandlers"], [119, 27, 131, 25], [119, 30, 131, 28], [119, 34, 131, 32], [119, 35, 131, 33, "awaitingHandlers"], [119, 51, 131, 49], [119, 52, 131, 50, "filter"], [119, 58, 131, 56], [119, 59, 131, 57, "<PERSON><PERSON><PERSON><PERSON>"], [119, 71, 131, 69], [119, 75, 131, 73], [119, 79, 131, 77], [119, 80, 131, 78, "awaitingHandlersTags"], [119, 100, 131, 98], [119, 101, 131, 99, "has"], [119, 104, 131, 102], [119, 105, 131, 103, "<PERSON><PERSON><PERSON><PERSON>"], [119, 117, 131, 115], [119, 118, 131, 116, "handlerTag"], [119, 128, 131, 126], [119, 129, 131, 127], [119, 130, 131, 128], [120, 4, 132, 2], [121, 4, 134, 2, "onHandlerStateChange"], [121, 24, 134, 22, "onHandlerStateChange"], [121, 25, 134, 23, "handler"], [121, 32, 134, 30], [121, 34, 134, 32, "newState"], [121, 42, 134, 40], [121, 44, 134, 42, "oldState"], [121, 52, 134, 50], [121, 54, 134, 52, "sendIfDisabled"], [121, 68, 134, 66], [121, 70, 134, 68], [122, 6, 135, 4], [122, 10, 135, 8], [122, 11, 135, 9, "handler"], [122, 18, 135, 16], [122, 19, 135, 17, "enabled"], [122, 26, 135, 24], [122, 30, 135, 28], [122, 31, 135, 29, "sendIfDisabled"], [122, 45, 135, 43], [122, 47, 135, 45], [123, 8, 136, 6], [124, 6, 137, 4], [125, 6, 139, 4], [125, 10, 139, 8], [125, 11, 139, 9, "handlingChangeSemaphore"], [125, 34, 139, 32], [125, 38, 139, 36], [125, 39, 139, 37], [126, 6, 141, 4], [126, 10, 141, 8], [126, 14, 141, 12], [126, 15, 141, 13, "isFinished"], [126, 25, 141, 23], [126, 26, 141, 24, "newState"], [126, 34, 141, 32], [126, 35, 141, 33], [126, 37, 141, 35], [127, 8, 142, 6], [127, 13, 142, 11], [127, 19, 142, 17, "<PERSON><PERSON><PERSON><PERSON>"], [127, 31, 142, 29], [127, 35, 142, 33], [127, 39, 142, 37], [127, 40, 142, 38, "awaitingHandlers"], [127, 56, 142, 54], [127, 58, 142, 56], [128, 10, 143, 8], [128, 14, 143, 12], [128, 15, 143, 13], [128, 19, 143, 17], [128, 20, 143, 18, "shouldHandlerWaitForOther"], [128, 45, 143, 43], [128, 46, 143, 44, "<PERSON><PERSON><PERSON><PERSON>"], [128, 58, 143, 56], [128, 60, 143, 58, "handler"], [128, 67, 143, 65], [128, 68, 143, 66], [128, 72, 143, 70], [128, 73, 143, 71], [128, 77, 143, 75], [128, 78, 143, 76, "awaitingHandlersTags"], [128, 98, 143, 96], [128, 99, 143, 97, "has"], [128, 102, 143, 100], [128, 103, 143, 101, "<PERSON><PERSON><PERSON><PERSON>"], [128, 115, 143, 113], [128, 116, 143, 114, "handlerTag"], [128, 126, 143, 124], [128, 127, 143, 125], [128, 129, 143, 127], [129, 12, 144, 10], [130, 10, 145, 8], [131, 10, 147, 8], [131, 14, 147, 12, "newState"], [131, 22, 147, 20], [131, 27, 147, 25, "State"], [131, 39, 147, 30], [131, 40, 147, 31, "END"], [131, 43, 147, 34], [131, 45, 147, 36], [132, 12, 148, 10], [132, 16, 148, 14], [132, 17, 148, 15, "tryActivate"], [132, 28, 148, 26], [132, 29, 148, 27, "<PERSON><PERSON><PERSON><PERSON>"], [132, 41, 148, 39], [132, 42, 148, 40], [133, 12, 149, 10], [134, 10, 150, 8], [135, 10, 152, 8, "<PERSON><PERSON><PERSON><PERSON>"], [135, 22, 152, 20], [135, 23, 152, 21, "cancel"], [135, 29, 152, 27], [135, 30, 152, 28], [135, 31, 152, 29], [136, 10, 154, 8], [136, 14, 154, 12, "<PERSON><PERSON><PERSON><PERSON>"], [136, 26, 154, 24], [136, 27, 154, 25, "state"], [136, 32, 154, 30], [136, 37, 154, 35, "State"], [136, 49, 154, 40], [136, 50, 154, 41, "END"], [136, 53, 154, 44], [136, 55, 154, 46], [137, 12, 155, 10], [138, 12, 156, 10], [139, 12, 157, 10], [140, 12, 158, 10], [141, 12, 159, 10, "<PERSON><PERSON><PERSON><PERSON>"], [141, 24, 159, 22], [141, 25, 159, 23, "sendEvent"], [141, 34, 159, 32], [141, 35, 159, 33, "State"], [141, 47, 159, 38], [141, 48, 159, 39, "CANCELLED"], [141, 57, 159, 48], [141, 59, 159, 50, "State"], [141, 71, 159, 55], [141, 72, 159, 56, "BEGAN"], [141, 77, 159, 61], [141, 78, 159, 62], [142, 10, 160, 8], [143, 10, 162, 8, "<PERSON><PERSON><PERSON><PERSON>"], [143, 22, 162, 20], [143, 23, 162, 21, "awaiting"], [143, 31, 162, 29], [143, 34, 162, 32], [143, 39, 162, 37], [144, 8, 163, 6], [145, 6, 164, 4], [146, 6, 166, 4], [146, 10, 166, 8, "newState"], [146, 18, 166, 16], [146, 23, 166, 21, "State"], [146, 35, 166, 26], [146, 36, 166, 27, "ACTIVE"], [146, 42, 166, 33], [146, 44, 166, 35], [147, 8, 167, 6], [147, 12, 167, 10], [147, 13, 167, 11, "tryActivate"], [147, 24, 167, 22], [147, 25, 167, 23, "handler"], [147, 32, 167, 30], [147, 33, 167, 31], [148, 6, 168, 4], [148, 7, 168, 5], [148, 13, 168, 11], [148, 17, 168, 15, "oldState"], [148, 25, 168, 23], [148, 30, 168, 28, "State"], [148, 42, 168, 33], [148, 43, 168, 34, "ACTIVE"], [148, 49, 168, 40], [148, 53, 168, 44, "oldState"], [148, 61, 168, 52], [148, 66, 168, 57, "State"], [148, 78, 168, 62], [148, 79, 168, 63, "END"], [148, 82, 168, 66], [148, 84, 168, 68], [149, 8, 169, 6], [149, 12, 169, 10, "handler"], [149, 19, 169, 17], [149, 20, 169, 18, "active"], [149, 26, 169, 24], [149, 28, 169, 26], [150, 10, 170, 8, "handler"], [150, 17, 170, 15], [150, 18, 170, 16, "sendEvent"], [150, 27, 170, 25], [150, 28, 170, 26, "newState"], [150, 36, 170, 34], [150, 38, 170, 36, "oldState"], [150, 46, 170, 44], [150, 47, 170, 45], [151, 8, 171, 6], [151, 9, 171, 7], [151, 15, 171, 13], [151, 19, 171, 17, "oldState"], [151, 27, 171, 25], [151, 32, 171, 30, "State"], [151, 44, 171, 35], [151, 45, 171, 36, "ACTIVE"], [151, 51, 171, 42], [151, 56, 171, 47, "newState"], [151, 64, 171, 55], [151, 69, 171, 60, "State"], [151, 81, 171, 65], [151, 82, 171, 66, "CANCELLED"], [151, 91, 171, 75], [151, 95, 171, 79, "newState"], [151, 103, 171, 87], [151, 108, 171, 92, "State"], [151, 120, 171, 97], [151, 121, 171, 98, "FAILED"], [151, 127, 171, 104], [151, 128, 171, 105], [151, 130, 171, 107], [152, 10, 172, 8, "handler"], [152, 17, 172, 15], [152, 18, 172, 16, "sendEvent"], [152, 27, 172, 25], [152, 28, 172, 26, "newState"], [152, 36, 172, 34], [152, 38, 172, 36, "State"], [152, 50, 172, 41], [152, 51, 172, 42, "BEGAN"], [152, 56, 172, 47], [152, 57, 172, 48], [153, 8, 173, 6], [154, 6, 174, 4], [154, 7, 174, 5], [154, 13, 174, 11], [154, 17, 174, 15, "oldState"], [154, 25, 174, 23], [154, 30, 174, 28, "State"], [154, 42, 174, 33], [154, 43, 174, 34, "UNDETERMINED"], [154, 55, 174, 46], [154, 59, 174, 50, "newState"], [154, 67, 174, 58], [154, 72, 174, 63, "State"], [154, 84, 174, 68], [154, 85, 174, 69, "CANCELLED"], [154, 94, 174, 78], [154, 96, 174, 80], [155, 8, 175, 6, "handler"], [155, 15, 175, 13], [155, 16, 175, 14, "sendEvent"], [155, 25, 175, 23], [155, 26, 175, 24, "newState"], [155, 34, 175, 32], [155, 36, 175, 34, "oldState"], [155, 44, 175, 42], [155, 45, 175, 43], [156, 6, 176, 4], [157, 6, 178, 4], [157, 10, 178, 8], [157, 11, 178, 9, "handlingChangeSemaphore"], [157, 34, 178, 32], [157, 38, 178, 36], [157, 39, 178, 37], [158, 6, 179, 4], [158, 10, 179, 8], [158, 11, 179, 9, "scheduleFinishedHandlersCleanup"], [158, 42, 179, 40], [158, 43, 179, 41], [158, 44, 179, 42], [159, 6, 181, 4], [159, 10, 181, 8], [159, 11, 181, 9], [159, 15, 181, 13], [159, 16, 181, 14, "awaitingHandlers"], [159, 32, 181, 30], [159, 33, 181, 31, "includes"], [159, 41, 181, 39], [159, 42, 181, 40, "handler"], [159, 49, 181, 47], [159, 50, 181, 48], [159, 52, 181, 50], [160, 8, 182, 6], [160, 12, 182, 10], [160, 13, 182, 11, "cleanupAwaitingHandlers"], [160, 36, 182, 34], [160, 37, 182, 35, "handler"], [160, 44, 182, 42], [160, 45, 182, 43], [161, 6, 183, 4], [162, 4, 184, 2], [163, 4, 186, 2, "makeActive"], [163, 14, 186, 12, "makeActive"], [163, 15, 186, 13, "handler"], [163, 22, 186, 20], [163, 24, 186, 22], [164, 6, 187, 4], [164, 12, 187, 10, "currentState"], [164, 24, 187, 22], [164, 27, 187, 25, "handler"], [164, 34, 187, 32], [164, 35, 187, 33, "state"], [164, 40, 187, 38], [165, 6, 188, 4, "handler"], [165, 13, 188, 11], [165, 14, 188, 12, "active"], [165, 20, 188, 18], [165, 23, 188, 21], [165, 27, 188, 25], [166, 6, 189, 4, "handler"], [166, 13, 189, 11], [166, 14, 189, 12, "shouldResetProgress"], [166, 33, 189, 31], [166, 36, 189, 34], [166, 40, 189, 38], [167, 6, 190, 4, "handler"], [167, 13, 190, 11], [167, 14, 190, 12, "activationIndex"], [167, 29, 190, 27], [167, 32, 190, 30], [167, 36, 190, 34], [167, 37, 190, 35, "activationIndex"], [167, 52, 190, 50], [167, 54, 190, 52], [168, 6, 192, 4], [168, 11, 192, 9], [168, 15, 192, 13, "i"], [168, 16, 192, 14], [168, 19, 192, 17], [168, 23, 192, 21], [168, 24, 192, 22, "gestureHandlers"], [168, 39, 192, 37], [168, 40, 192, 38, "length"], [168, 46, 192, 44], [168, 49, 192, 47], [168, 50, 192, 48], [168, 52, 192, 50, "i"], [168, 53, 192, 51], [168, 57, 192, 55], [168, 58, 192, 56], [168, 60, 192, 58], [168, 62, 192, 60, "i"], [168, 63, 192, 61], [168, 65, 192, 63], [169, 8, 193, 6], [169, 12, 193, 10], [169, 16, 193, 14], [169, 17, 193, 15, "shouldHandlerBeCancelledBy"], [169, 43, 193, 41], [169, 44, 193, 42], [169, 48, 193, 46], [169, 49, 193, 47, "gestureHandlers"], [169, 64, 193, 62], [169, 65, 193, 63, "i"], [169, 66, 193, 64], [169, 67, 193, 65], [169, 69, 193, 67, "handler"], [169, 76, 193, 74], [169, 77, 193, 75], [169, 79, 193, 77], [170, 10, 194, 8], [170, 14, 194, 12], [170, 15, 194, 13, "gestureHandlers"], [170, 30, 194, 28], [170, 31, 194, 29, "i"], [170, 32, 194, 30], [170, 33, 194, 31], [170, 34, 194, 32, "cancel"], [170, 40, 194, 38], [170, 41, 194, 39], [170, 42, 194, 40], [171, 8, 195, 6], [172, 6, 196, 4], [173, 6, 198, 4], [173, 11, 198, 9], [173, 17, 198, 15, "<PERSON><PERSON><PERSON><PERSON>"], [173, 29, 198, 27], [173, 33, 198, 31], [173, 37, 198, 35], [173, 38, 198, 36, "awaitingHandlers"], [173, 54, 198, 52], [173, 56, 198, 54], [174, 8, 199, 6], [174, 12, 199, 10], [174, 16, 199, 14], [174, 17, 199, 15, "shouldHandlerBeCancelledBy"], [174, 43, 199, 41], [174, 44, 199, 42, "<PERSON><PERSON><PERSON><PERSON>"], [174, 56, 199, 54], [174, 58, 199, 56, "handler"], [174, 65, 199, 63], [174, 66, 199, 64], [174, 68, 199, 66], [175, 10, 200, 8, "<PERSON><PERSON><PERSON><PERSON>"], [175, 22, 200, 20], [175, 23, 200, 21, "awaiting"], [175, 31, 200, 29], [175, 34, 200, 32], [175, 39, 200, 37], [176, 8, 201, 6], [177, 6, 202, 4], [178, 6, 204, 4, "handler"], [178, 13, 204, 11], [178, 14, 204, 12, "sendEvent"], [178, 23, 204, 21], [178, 24, 204, 22, "State"], [178, 36, 204, 27], [178, 37, 204, 28, "ACTIVE"], [178, 43, 204, 34], [178, 45, 204, 36, "State"], [178, 57, 204, 41], [178, 58, 204, 42, "BEGAN"], [178, 63, 204, 47], [178, 64, 204, 48], [179, 6, 206, 4], [179, 10, 206, 8, "currentState"], [179, 22, 206, 20], [179, 27, 206, 25, "State"], [179, 39, 206, 30], [179, 40, 206, 31, "ACTIVE"], [179, 46, 206, 37], [179, 48, 206, 39], [180, 8, 207, 6, "handler"], [180, 15, 207, 13], [180, 16, 207, 14, "sendEvent"], [180, 25, 207, 23], [180, 26, 207, 24, "State"], [180, 38, 207, 29], [180, 39, 207, 30, "END"], [180, 42, 207, 33], [180, 44, 207, 35, "State"], [180, 56, 207, 40], [180, 57, 207, 41, "ACTIVE"], [180, 63, 207, 47], [180, 64, 207, 48], [181, 8, 209, 6], [181, 12, 209, 10, "currentState"], [181, 24, 209, 22], [181, 29, 209, 27, "State"], [181, 41, 209, 32], [181, 42, 209, 33, "END"], [181, 45, 209, 36], [181, 47, 209, 38], [182, 10, 210, 8, "handler"], [182, 17, 210, 15], [182, 18, 210, 16, "sendEvent"], [182, 27, 210, 25], [182, 28, 210, 26, "State"], [182, 40, 210, 31], [182, 41, 210, 32, "UNDETERMINED"], [182, 53, 210, 44], [182, 55, 210, 46, "State"], [182, 67, 210, 51], [182, 68, 210, 52, "END"], [182, 71, 210, 55], [182, 72, 210, 56], [183, 8, 211, 6], [184, 6, 212, 4], [185, 6, 214, 4], [185, 10, 214, 8], [185, 11, 214, 9, "handler"], [185, 18, 214, 16], [185, 19, 214, 17, "awaiting"], [185, 27, 214, 25], [185, 29, 214, 27], [186, 8, 215, 6], [187, 6, 216, 4], [188, 6, 218, 4, "handler"], [188, 13, 218, 11], [188, 14, 218, 12, "awaiting"], [188, 22, 218, 20], [188, 25, 218, 23], [188, 30, 218, 28], [189, 6, 219, 4], [189, 10, 219, 8], [189, 11, 219, 9, "awaitingHandlers"], [189, 27, 219, 25], [189, 30, 219, 28], [189, 34, 219, 32], [189, 35, 219, 33, "awaitingHandlers"], [189, 51, 219, 49], [189, 52, 219, 50, "filter"], [189, 58, 219, 56], [189, 59, 219, 57, "<PERSON><PERSON><PERSON><PERSON>"], [189, 71, 219, 69], [189, 75, 219, 73, "<PERSON><PERSON><PERSON><PERSON>"], [189, 87, 219, 85], [189, 92, 219, 90, "handler"], [189, 99, 219, 97], [189, 100, 219, 98], [190, 4, 220, 2], [191, 4, 222, 2, "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [191, 22, 222, 20, "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [191, 23, 222, 21, "handler"], [191, 30, 222, 28], [191, 32, 222, 30], [192, 6, 223, 4], [192, 10, 223, 8], [192, 14, 223, 12], [192, 15, 223, 13, "awaitingHandlers"], [192, 31, 223, 29], [192, 32, 223, 30, "includes"], [192, 40, 223, 38], [192, 41, 223, 39, "handler"], [192, 48, 223, 46], [192, 49, 223, 47], [192, 51, 223, 49], [193, 8, 224, 6], [194, 6, 225, 4], [195, 6, 227, 4], [195, 10, 227, 8], [195, 11, 227, 9, "awaitingHandlers"], [195, 27, 227, 25], [195, 28, 227, 26, "push"], [195, 32, 227, 30], [195, 33, 227, 31, "handler"], [195, 40, 227, 38], [195, 41, 227, 39], [196, 6, 228, 4], [196, 10, 228, 8], [196, 11, 228, 9, "awaitingHandlersTags"], [196, 31, 228, 29], [196, 32, 228, 30, "add"], [196, 35, 228, 33], [196, 36, 228, 34, "handler"], [196, 43, 228, 41], [196, 44, 228, 42, "handlerTag"], [196, 54, 228, 52], [196, 55, 228, 53], [197, 6, 229, 4, "handler"], [197, 13, 229, 11], [197, 14, 229, 12, "awaiting"], [197, 22, 229, 20], [197, 25, 229, 23], [197, 29, 229, 27], [198, 6, 230, 4, "handler"], [198, 13, 230, 11], [198, 14, 230, 12, "activationIndex"], [198, 29, 230, 27], [198, 32, 230, 30], [198, 36, 230, 34], [198, 37, 230, 35, "activationIndex"], [198, 52, 230, 50], [198, 54, 230, 52], [199, 4, 231, 2], [200, 4, 233, 2, "recordHandlerIfNotPresent"], [200, 29, 233, 27, "recordHandlerIfNotPresent"], [200, 30, 233, 28, "handler"], [200, 37, 233, 35], [200, 39, 233, 37], [201, 6, 234, 4], [201, 10, 234, 8], [201, 14, 234, 12], [201, 15, 234, 13, "gestureHandlers"], [201, 30, 234, 28], [201, 31, 234, 29, "includes"], [201, 39, 234, 37], [201, 40, 234, 38, "handler"], [201, 47, 234, 45], [201, 48, 234, 46], [201, 50, 234, 48], [202, 8, 235, 6], [203, 6, 236, 4], [204, 6, 238, 4], [204, 10, 238, 8], [204, 11, 238, 9, "gestureHandlers"], [204, 26, 238, 24], [204, 27, 238, 25, "push"], [204, 31, 238, 29], [204, 32, 238, 30, "handler"], [204, 39, 238, 37], [204, 40, 238, 38], [205, 6, 239, 4, "handler"], [205, 13, 239, 11], [205, 14, 239, 12, "active"], [205, 20, 239, 18], [205, 23, 239, 21], [205, 28, 239, 26], [206, 6, 240, 4, "handler"], [206, 13, 240, 11], [206, 14, 240, 12, "awaiting"], [206, 22, 240, 20], [206, 25, 240, 23], [206, 30, 240, 28], [207, 6, 241, 4, "handler"], [207, 13, 241, 11], [207, 14, 241, 12, "activationIndex"], [207, 29, 241, 27], [207, 32, 241, 30, "Number"], [207, 38, 241, 36], [207, 39, 241, 37, "MAX_SAFE_INTEGER"], [207, 55, 241, 53], [208, 4, 242, 2], [209, 4, 244, 2, "shouldHandlerWaitForOther"], [209, 29, 244, 27, "shouldHandlerWaitForOther"], [209, 30, 244, 28, "handler"], [209, 37, 244, 35], [209, 39, 244, 37, "<PERSON><PERSON><PERSON><PERSON>"], [209, 51, 244, 49], [209, 53, 244, 51], [210, 6, 245, 4], [210, 13, 245, 11, "handler"], [210, 20, 245, 18], [210, 25, 245, 23, "<PERSON><PERSON><PERSON><PERSON>"], [210, 37, 245, 35], [210, 42, 245, 40, "handler"], [210, 49, 245, 47], [210, 50, 245, 48, "shouldWaitForHandlerFailure"], [210, 77, 245, 75], [210, 78, 245, 76, "<PERSON><PERSON><PERSON><PERSON>"], [210, 90, 245, 88], [210, 91, 245, 89], [210, 95, 245, 93, "<PERSON><PERSON><PERSON><PERSON>"], [210, 107, 245, 105], [210, 108, 245, 106, "shouldRequireToWaitForFailure"], [210, 137, 245, 135], [210, 138, 245, 136, "handler"], [210, 145, 245, 143], [210, 146, 245, 144], [210, 147, 245, 145], [211, 4, 246, 2], [212, 4, 248, 2, "canRunSimultaneously"], [212, 24, 248, 22, "canRunSimultaneously"], [212, 25, 248, 23, "gh1"], [212, 28, 248, 26], [212, 30, 248, 28, "gh2"], [212, 33, 248, 31], [212, 35, 248, 33], [213, 6, 249, 4], [213, 13, 249, 11, "gh1"], [213, 16, 249, 14], [213, 21, 249, 19, "gh2"], [213, 24, 249, 22], [213, 28, 249, 26, "gh1"], [213, 31, 249, 29], [213, 32, 249, 30, "shouldRecognizeSimultaneously"], [213, 61, 249, 59], [213, 62, 249, 60, "gh2"], [213, 65, 249, 63], [213, 66, 249, 64], [213, 70, 249, 68, "gh2"], [213, 73, 249, 71], [213, 74, 249, 72, "shouldRecognizeSimultaneously"], [213, 103, 249, 101], [213, 104, 249, 102, "gh1"], [213, 107, 249, 105], [213, 108, 249, 106], [214, 4, 250, 2], [215, 4, 252, 2, "shouldHandlerBeCancelledBy"], [215, 30, 252, 28, "shouldHandlerBeCancelledBy"], [215, 31, 252, 29, "handler"], [215, 38, 252, 36], [215, 40, 252, 38, "<PERSON><PERSON><PERSON><PERSON>"], [215, 52, 252, 50], [215, 54, 252, 52], [216, 6, 253, 4], [216, 10, 253, 8], [216, 14, 253, 12], [216, 15, 253, 13, "canRunSimultaneously"], [216, 35, 253, 33], [216, 36, 253, 34, "handler"], [216, 43, 253, 41], [216, 45, 253, 43, "<PERSON><PERSON><PERSON><PERSON>"], [216, 57, 253, 55], [216, 58, 253, 56], [216, 60, 253, 58], [217, 8, 254, 6], [217, 15, 254, 13], [217, 20, 254, 18], [218, 6, 255, 4], [219, 6, 257, 4], [219, 10, 257, 8, "handler"], [219, 17, 257, 15], [219, 18, 257, 16, "awaiting"], [219, 26, 257, 24], [219, 30, 257, 28, "handler"], [219, 37, 257, 35], [219, 38, 257, 36, "state"], [219, 43, 257, 41], [219, 48, 257, 46, "State"], [219, 60, 257, 51], [219, 61, 257, 52, "ACTIVE"], [219, 67, 257, 58], [219, 69, 257, 60], [220, 8, 258, 6], [221, 8, 259, 6], [221, 15, 259, 13, "handler"], [221, 22, 259, 20], [221, 23, 259, 21, "shouldBeCancelledByOther"], [221, 47, 259, 45], [221, 48, 259, 46, "<PERSON><PERSON><PERSON><PERSON>"], [221, 60, 259, 58], [221, 61, 259, 59], [222, 6, 260, 4], [223, 6, 262, 4], [223, 12, 262, 10, "handlerPointers"], [223, 27, 262, 25], [223, 30, 262, 28, "handler"], [223, 37, 262, 35], [223, 38, 262, 36, "getTrackedPointersID"], [223, 58, 262, 56], [223, 59, 262, 57], [223, 60, 262, 58], [224, 6, 263, 4], [224, 12, 263, 10, "otherPointers"], [224, 25, 263, 23], [224, 28, 263, 26, "<PERSON><PERSON><PERSON><PERSON>"], [224, 40, 263, 38], [224, 41, 263, 39, "getTrackedPointersID"], [224, 61, 263, 59], [224, 62, 263, 60], [224, 63, 263, 61], [225, 6, 265, 4], [225, 10, 265, 8], [225, 11, 265, 9, "PointerTracker"], [225, 34, 265, 23], [225, 35, 265, 24, "shareCommonPointers"], [225, 54, 265, 43], [225, 55, 265, 44, "handlerPointers"], [225, 70, 265, 59], [225, 72, 265, 61, "otherPointers"], [225, 85, 265, 74], [225, 86, 265, 75], [225, 90, 265, 79, "handler"], [225, 97, 265, 86], [225, 98, 265, 87, "delegate"], [225, 106, 265, 95], [225, 107, 265, 96, "view"], [225, 111, 265, 100], [225, 116, 265, 105, "<PERSON><PERSON><PERSON><PERSON>"], [225, 128, 265, 117], [225, 129, 265, 118, "delegate"], [225, 137, 265, 126], [225, 138, 265, 127, "view"], [225, 142, 265, 131], [225, 144, 265, 133], [226, 8, 266, 6], [226, 15, 266, 13], [226, 19, 266, 17], [226, 20, 266, 18, "checkOverlap"], [226, 32, 266, 30], [226, 33, 266, 31, "handler"], [226, 40, 266, 38], [226, 42, 266, 40, "<PERSON><PERSON><PERSON><PERSON>"], [226, 54, 266, 52], [226, 55, 266, 53], [227, 6, 267, 4], [228, 6, 269, 4], [228, 13, 269, 11], [228, 17, 269, 15], [229, 4, 270, 2], [230, 4, 272, 2, "checkOverlap"], [230, 16, 272, 14, "checkOverlap"], [230, 17, 272, 15, "handler"], [230, 24, 272, 22], [230, 26, 272, 24, "<PERSON><PERSON><PERSON><PERSON>"], [230, 38, 272, 36], [230, 40, 272, 38], [231, 6, 273, 4], [232, 6, 274, 4], [233, 6, 275, 4], [234, 6, 276, 4], [235, 6, 277, 4], [235, 12, 277, 10, "isPointerWithinBothBounds"], [235, 37, 277, 35], [235, 40, 277, 38, "pointer"], [235, 47, 277, 45], [235, 51, 277, 49], [236, 8, 278, 6], [236, 14, 278, 12, "point"], [236, 19, 278, 17], [236, 22, 278, 20, "handler"], [236, 29, 278, 27], [236, 30, 278, 28, "tracker"], [236, 37, 278, 35], [236, 38, 278, 36, "getLastAbsoluteCoords"], [236, 59, 278, 57], [236, 60, 278, 58, "pointer"], [236, 67, 278, 65], [236, 68, 278, 66], [237, 8, 279, 6], [237, 15, 279, 13, "handler"], [237, 22, 279, 20], [237, 23, 279, 21, "delegate"], [237, 31, 279, 29], [237, 32, 279, 30, "isPointerInBounds"], [237, 49, 279, 47], [237, 50, 279, 48, "point"], [237, 55, 279, 53], [237, 56, 279, 54], [237, 60, 279, 58, "<PERSON><PERSON><PERSON><PERSON>"], [237, 72, 279, 70], [237, 73, 279, 71, "delegate"], [237, 81, 279, 79], [237, 82, 279, 80, "isPointerInBounds"], [237, 99, 279, 97], [237, 100, 279, 98, "point"], [237, 105, 279, 103], [237, 106, 279, 104], [238, 6, 280, 4], [238, 7, 280, 5], [239, 6, 282, 4], [239, 13, 282, 11, "handler"], [239, 20, 282, 18], [239, 21, 282, 19, "getTrackedPointersID"], [239, 41, 282, 39], [239, 42, 282, 40], [239, 43, 282, 41], [239, 44, 282, 42, "some"], [239, 48, 282, 46], [239, 49, 282, 47, "isPointerWithinBothBounds"], [239, 74, 282, 72], [239, 75, 282, 73], [240, 4, 283, 2], [241, 4, 285, 2, "isFinished"], [241, 14, 285, 12, "isFinished"], [241, 15, 285, 13, "state"], [241, 20, 285, 18], [241, 22, 285, 20], [242, 6, 286, 4], [242, 13, 286, 11, "state"], [242, 18, 286, 16], [242, 23, 286, 21, "State"], [242, 35, 286, 26], [242, 36, 286, 27, "END"], [242, 39, 286, 30], [242, 43, 286, 34, "state"], [242, 48, 286, 39], [242, 53, 286, 44, "State"], [242, 65, 286, 49], [242, 66, 286, 50, "FAILED"], [242, 72, 286, 56], [242, 76, 286, 60, "state"], [242, 81, 286, 65], [242, 86, 286, 70, "State"], [242, 98, 286, 75], [242, 99, 286, 76, "CANCELLED"], [242, 108, 286, 85], [243, 4, 287, 2], [243, 5, 287, 3], [243, 6, 287, 4], [244, 4, 288, 2], [245, 4, 289, 2], [246, 4, 290, 2], [247, 4, 291, 2], [248, 4, 292, 2], [250, 4, 295, 2, "cancelMouseAndPenGestures"], [250, 29, 295, 27, "cancelMouseAndPenGestures"], [250, 30, 295, 28, "<PERSON><PERSON><PERSON><PERSON>"], [250, 44, 295, 42], [250, 46, 295, 44], [251, 6, 296, 4], [251, 10, 296, 8], [251, 11, 296, 9, "gestureHandlers"], [251, 26, 296, 24], [251, 27, 296, 25, "for<PERSON>ach"], [251, 34, 296, 32], [251, 35, 296, 33, "handler"], [251, 42, 296, 40], [251, 46, 296, 44], [252, 8, 297, 6], [252, 12, 297, 10, "handler"], [252, 19, 297, 17], [252, 20, 297, 18, "pointerType"], [252, 31, 297, 29], [252, 36, 297, 34, "PointerType"], [252, 60, 297, 45], [252, 61, 297, 46, "MOUSE"], [252, 66, 297, 51], [252, 70, 297, 55, "handler"], [252, 77, 297, 62], [252, 78, 297, 63, "pointerType"], [252, 89, 297, 74], [252, 94, 297, 79, "PointerType"], [252, 118, 297, 90], [252, 119, 297, 91, "STYLUS"], [252, 125, 297, 97], [252, 127, 297, 99], [253, 10, 298, 8], [254, 8, 299, 6], [255, 8, 301, 6], [255, 12, 301, 10, "handler"], [255, 19, 301, 17], [255, 24, 301, 22, "<PERSON><PERSON><PERSON><PERSON>"], [255, 38, 301, 36], [255, 40, 301, 38], [256, 10, 302, 8, "handler"], [256, 17, 302, 15], [256, 18, 302, 16, "cancel"], [256, 24, 302, 22], [256, 25, 302, 23], [256, 26, 302, 24], [257, 8, 303, 6], [257, 9, 303, 7], [257, 15, 303, 13], [258, 10, 304, 8], [259, 10, 305, 8], [260, 10, 306, 8], [261, 10, 307, 8], [262, 10, 308, 8], [263, 10, 309, 8], [264, 10, 310, 8], [265, 10, 311, 8, "handler"], [265, 17, 311, 15], [265, 18, 311, 16, "tracker"], [265, 25, 311, 23], [265, 26, 311, 24, "resetTracker"], [265, 38, 311, 36], [265, 39, 311, 37], [265, 40, 311, 38], [266, 8, 312, 6], [267, 6, 313, 4], [267, 7, 313, 5], [267, 8, 313, 6], [268, 4, 314, 2], [269, 4, 316, 2], [269, 15, 316, 13, "instance"], [269, 23, 316, 21, "instance"], [269, 24, 316, 21], [269, 26, 316, 24], [270, 6, 317, 4], [270, 10, 317, 8], [270, 11, 317, 9, "GestureHandlerOrchestrator"], [270, 37, 317, 35], [270, 38, 317, 36, "_instance"], [270, 47, 317, 45], [270, 49, 317, 47], [271, 8, 318, 6, "GestureHandlerOrchestrator"], [271, 34, 318, 32], [271, 35, 318, 33, "_instance"], [271, 44, 318, 42], [271, 47, 318, 45], [271, 51, 318, 49, "GestureHandlerOrchestrator"], [271, 77, 318, 75], [271, 78, 318, 76], [271, 79, 318, 77], [272, 6, 319, 4], [273, 6, 321, 4], [273, 13, 321, 11, "GestureHandlerOrchestrator"], [273, 39, 321, 37], [273, 40, 321, 38, "_instance"], [273, 49, 321, 47], [274, 4, 322, 2], [275, 2, 324, 0], [276, 2, 324, 1, "exports"], [276, 9, 324, 1], [276, 10, 324, 1, "default"], [276, 17, 324, 1], [276, 20, 324, 1, "GestureHandlerOrchestrator"], [276, 46, 324, 1], [277, 2, 326, 0, "_defineProperty"], [277, 17, 326, 15], [277, 18, 326, 16, "GestureHandlerOrchestrator"], [277, 44, 326, 42], [277, 46, 326, 44], [277, 57, 326, 55], [277, 59, 326, 57], [277, 64, 326, 62], [277, 65, 326, 63], [277, 66, 326, 64], [278, 0, 326, 65], [278, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "GestureHandlerOrchestrator", "constructor", "scheduleFinishedHandlersCleanup", "<PERSON><PERSON><PERSON><PERSON>", "removeHandlerFromOrchestrator", "cleanupFinishedHandlers", "gestureHandlers.filter$argument_0", "hasOtherHandlerToWaitFor", "hasToWaitFor", "shouldBeCancelledByFinishedHandler", "shouldBeCancelled", "tryActivate", "shouldActivate", "shouldBeCancelledBy", "cleanupAwaitingHandlers", "should<PERSON>ait", "awaitingHandlers.filter$argument_0", "onHandlerStateChange", "makeActive", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordHandlerIfNotPresent", "shouldHandlerWaitForOther", "canRunSimultaneously", "shouldHandlerBeCancelledBy", "checkOverlap", "isPointerWithinBothBounds", "isFinished", "cancelMouseAndPenGestures", "gestureHandlers.forEach$argument_0", "get__instance"], "mappings": "AAA,iNC;eCK;ECG;GDU;EEE;GFI;EGE;GHK;EIE;GJY;EKE;uDCY,yCD;GLC;EOE;yBCC;KDE;GPG;ESE;8BCC;KDE;GTG;EWE;GX8B;EYE;gCCC;KDE;GZG;EcE;uBCC;KDE;yDES,sEF;GdC;EiBE;GjBkD;EkBE;yDFiC,wCE;GlBC;EmBE;GnBS;EoBE;GpBS;EqBE;GrBE;EsBE;GtBE;EuBE;GvBkB;EwBE;sCCK;KDG;GxBG;E0BE;G1BE;E2BQ;iCCC;KDiB;G3BC;E6BE;G7BM;CDE"}}, "type": "js/module"}]}