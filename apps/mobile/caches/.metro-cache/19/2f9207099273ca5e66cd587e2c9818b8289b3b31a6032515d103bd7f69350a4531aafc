{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getActionFromState = getActionFromState;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  function getActionFromState(state, options) {\n    // Create a normalized configs object which will be easier to use\n    var normalizedConfig = options ? createNormalizedConfigItem(options) : {};\n    var routes = state.index != null ? state.routes.slice(0, state.index + 1) : state.routes;\n    if (routes.length === 0) {\n      return undefined;\n    }\n    if (!(routes.length === 1 && routes[0].key === undefined || routes.length === 2 && routes[0].key === undefined && routes[0].name === normalizedConfig?.initialRouteName && routes[1].key === undefined)) {\n      return {\n        type: 'RESET',\n        payload: state\n      };\n    }\n    var route = state.routes[state.index ?? state.routes.length - 1];\n    var current = route?.state;\n    var config = normalizedConfig?.screens?.[route?.name];\n    var params = {\n      ...route.params\n    };\n    var payload = route ? {\n      name: route.name,\n      path: route.path,\n      params\n    } : undefined;\n\n    // If the screen contains a navigator, pop other screens to navigate to it\n    // This avoid pushing multiple instances of navigators onto a stack\n    //\n    // For example:\n    // - RootStack\n    //   - BottomTabs\n    //   - SomeScreen\n    //\n    // In this case, if deep linking to `BottomTabs`, we should pop `SomeScreen`\n    // Otherwise, we'll end up with 2 instances of `BottomTabs` in the stack\n    //\n    // There are 2 ways we can detect if a screen contains a navigator:\n    // - The route contains nested state in `route.state`\n    // - Nested screens are defined in the config\n    if (payload && config?.screens && Object.keys(config.screens).length) {\n      payload.pop = true;\n    }\n    while (current) {\n      if (current.routes.length === 0) {\n        return undefined;\n      }\n      var _routes = current.index != null ? current.routes.slice(0, current.index + 1) : current.routes;\n      var _route = _routes[_routes.length - 1];\n\n      // Explicitly set to override existing value when merging params\n      Object.assign(params, {\n        initial: undefined,\n        screen: undefined,\n        params: undefined,\n        state: undefined\n      });\n      if (_routes.length === 1 && _routes[0].key === undefined) {\n        params.initial = true;\n        params.screen = _route.name;\n      } else if (_routes.length === 2 && _routes[0].key === undefined && _routes[0].name === config?.initialRouteName && _routes[1].key === undefined) {\n        params.initial = false;\n        params.screen = _route.name;\n      } else {\n        params.state = current;\n        break;\n      }\n      if (_route.state) {\n        params.params = {\n          ..._route.params\n        };\n        params.pop = true;\n        params = params.params;\n      } else {\n        params.path = _route.path;\n        params.params = _route.params;\n      }\n      current = _route.state;\n      config = config?.screens?.[_route.name];\n      if (config?.screens && Object.keys(config.screens).length) {\n        params.pop = true;\n      }\n    }\n    if (payload?.params.screen || payload?.params.state) {\n      payload.pop = true;\n    }\n    if (!payload) {\n      return;\n    }\n\n    // Try to construct payload for a `NAVIGATE` action from the state\n    // This lets us preserve the navigation state and not lose it\n    return {\n      type: 'NAVIGATE',\n      payload\n    };\n  }\n  var createNormalizedConfigItem = config => typeof config === 'object' && config != null ? {\n    initialRouteName: config.initialRouteName,\n    screens: config.screens != null ? createNormalizedConfigs(config.screens) : undefined\n  } : {};\n  var createNormalizedConfigs = options => Object.entries(options).reduce((acc, _ref) => {\n    var _ref2 = (0, _slicedToArray2.default)(_ref, 2),\n      k = _ref2[0],\n      v = _ref2[1];\n    acc[k] = createNormalizedConfigItem(v);\n    return acc;\n  }, {});\n});", "lineCount": 117, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "getActionFromState"], [8, 28, 1, 13], [8, 31, 1, 13, "getActionFromState"], [8, 49, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 7], [10, 11, 3, 16, "getActionFromState"], [10, 29, 3, 34, "getActionFromState"], [10, 30, 3, 35, "state"], [10, 35, 3, 40], [10, 37, 3, 42, "options"], [10, 44, 3, 49], [10, 46, 3, 51], [11, 4, 4, 2], [12, 4, 5, 2], [12, 8, 5, 8, "normalizedConfig"], [12, 24, 5, 24], [12, 27, 5, 27, "options"], [12, 34, 5, 34], [12, 37, 5, 37, "createNormalizedConfigItem"], [12, 63, 5, 63], [12, 64, 5, 64, "options"], [12, 71, 5, 71], [12, 72, 5, 72], [12, 75, 5, 75], [12, 76, 5, 76], [12, 77, 5, 77], [13, 4, 6, 2], [13, 8, 6, 8, "routes"], [13, 14, 6, 14], [13, 17, 6, 17, "state"], [13, 22, 6, 22], [13, 23, 6, 23, "index"], [13, 28, 6, 28], [13, 32, 6, 32], [13, 36, 6, 36], [13, 39, 6, 39, "state"], [13, 44, 6, 44], [13, 45, 6, 45, "routes"], [13, 51, 6, 51], [13, 52, 6, 52, "slice"], [13, 57, 6, 57], [13, 58, 6, 58], [13, 59, 6, 59], [13, 61, 6, 61, "state"], [13, 66, 6, 66], [13, 67, 6, 67, "index"], [13, 72, 6, 72], [13, 75, 6, 75], [13, 76, 6, 76], [13, 77, 6, 77], [13, 80, 6, 80, "state"], [13, 85, 6, 85], [13, 86, 6, 86, "routes"], [13, 92, 6, 92], [14, 4, 7, 2], [14, 8, 7, 6, "routes"], [14, 14, 7, 12], [14, 15, 7, 13, "length"], [14, 21, 7, 19], [14, 26, 7, 24], [14, 27, 7, 25], [14, 29, 7, 27], [15, 6, 8, 4], [15, 13, 8, 11, "undefined"], [15, 22, 8, 20], [16, 4, 9, 2], [17, 4, 10, 2], [17, 8, 10, 6], [17, 10, 10, 8, "routes"], [17, 16, 10, 14], [17, 17, 10, 15, "length"], [17, 23, 10, 21], [17, 28, 10, 26], [17, 29, 10, 27], [17, 33, 10, 31, "routes"], [17, 39, 10, 37], [17, 40, 10, 38], [17, 41, 10, 39], [17, 42, 10, 40], [17, 43, 10, 41, "key"], [17, 46, 10, 44], [17, 51, 10, 49, "undefined"], [17, 60, 10, 58], [17, 64, 10, 62, "routes"], [17, 70, 10, 68], [17, 71, 10, 69, "length"], [17, 77, 10, 75], [17, 82, 10, 80], [17, 83, 10, 81], [17, 87, 10, 85, "routes"], [17, 93, 10, 91], [17, 94, 10, 92], [17, 95, 10, 93], [17, 96, 10, 94], [17, 97, 10, 95, "key"], [17, 100, 10, 98], [17, 105, 10, 103, "undefined"], [17, 114, 10, 112], [17, 118, 10, 116, "routes"], [17, 124, 10, 122], [17, 125, 10, 123], [17, 126, 10, 124], [17, 127, 10, 125], [17, 128, 10, 126, "name"], [17, 132, 10, 130], [17, 137, 10, 135, "normalizedConfig"], [17, 153, 10, 151], [17, 155, 10, 153, "initialRouteName"], [17, 171, 10, 169], [17, 175, 10, 173, "routes"], [17, 181, 10, 179], [17, 182, 10, 180], [17, 183, 10, 181], [17, 184, 10, 182], [17, 185, 10, 183, "key"], [17, 188, 10, 186], [17, 193, 10, 191, "undefined"], [17, 202, 10, 200], [17, 203, 10, 201], [17, 205, 10, 203], [18, 6, 11, 4], [18, 13, 11, 11], [19, 8, 12, 6, "type"], [19, 12, 12, 10], [19, 14, 12, 12], [19, 21, 12, 19], [20, 8, 13, 6, "payload"], [20, 15, 13, 13], [20, 17, 13, 15, "state"], [21, 6, 14, 4], [21, 7, 14, 5], [22, 4, 15, 2], [23, 4, 16, 2], [23, 8, 16, 8, "route"], [23, 13, 16, 13], [23, 16, 16, 16, "state"], [23, 21, 16, 21], [23, 22, 16, 22, "routes"], [23, 28, 16, 28], [23, 29, 16, 29, "state"], [23, 34, 16, 34], [23, 35, 16, 35, "index"], [23, 40, 16, 40], [23, 44, 16, 44, "state"], [23, 49, 16, 49], [23, 50, 16, 50, "routes"], [23, 56, 16, 56], [23, 57, 16, 57, "length"], [23, 63, 16, 63], [23, 66, 16, 66], [23, 67, 16, 67], [23, 68, 16, 68], [24, 4, 17, 2], [24, 8, 17, 6, "current"], [24, 15, 17, 13], [24, 18, 17, 16, "route"], [24, 23, 17, 21], [24, 25, 17, 23, "state"], [24, 30, 17, 28], [25, 4, 18, 2], [25, 8, 18, 6, "config"], [25, 14, 18, 12], [25, 17, 18, 15, "normalizedConfig"], [25, 33, 18, 31], [25, 35, 18, 33, "screens"], [25, 42, 18, 40], [25, 45, 18, 43, "route"], [25, 50, 18, 48], [25, 52, 18, 50, "name"], [25, 56, 18, 54], [25, 57, 18, 55], [26, 4, 19, 2], [26, 8, 19, 6, "params"], [26, 14, 19, 12], [26, 17, 19, 15], [27, 6, 20, 4], [27, 9, 20, 7, "route"], [27, 14, 20, 12], [27, 15, 20, 13, "params"], [28, 4, 21, 2], [28, 5, 21, 3], [29, 4, 22, 2], [29, 8, 22, 8, "payload"], [29, 15, 22, 15], [29, 18, 22, 18, "route"], [29, 23, 22, 23], [29, 26, 22, 26], [30, 6, 23, 4, "name"], [30, 10, 23, 8], [30, 12, 23, 10, "route"], [30, 17, 23, 15], [30, 18, 23, 16, "name"], [30, 22, 23, 20], [31, 6, 24, 4, "path"], [31, 10, 24, 8], [31, 12, 24, 10, "route"], [31, 17, 24, 15], [31, 18, 24, 16, "path"], [31, 22, 24, 20], [32, 6, 25, 4, "params"], [33, 4, 26, 2], [33, 5, 26, 3], [33, 8, 26, 6, "undefined"], [33, 17, 26, 15], [35, 4, 28, 2], [36, 4, 29, 2], [37, 4, 30, 2], [38, 4, 31, 2], [39, 4, 32, 2], [40, 4, 33, 2], [41, 4, 34, 2], [42, 4, 35, 2], [43, 4, 36, 2], [44, 4, 37, 2], [45, 4, 38, 2], [46, 4, 39, 2], [47, 4, 40, 2], [48, 4, 41, 2], [49, 4, 42, 2], [49, 8, 42, 6, "payload"], [49, 15, 42, 13], [49, 19, 42, 17, "config"], [49, 25, 42, 23], [49, 27, 42, 25, "screens"], [49, 34, 42, 32], [49, 38, 42, 36, "Object"], [49, 44, 42, 42], [49, 45, 42, 43, "keys"], [49, 49, 42, 47], [49, 50, 42, 48, "config"], [49, 56, 42, 54], [49, 57, 42, 55, "screens"], [49, 64, 42, 62], [49, 65, 42, 63], [49, 66, 42, 64, "length"], [49, 72, 42, 70], [49, 74, 42, 72], [50, 6, 43, 4, "payload"], [50, 13, 43, 11], [50, 14, 43, 12, "pop"], [50, 17, 43, 15], [50, 20, 43, 18], [50, 24, 43, 22], [51, 4, 44, 2], [52, 4, 45, 2], [52, 11, 45, 9, "current"], [52, 18, 45, 16], [52, 20, 45, 18], [53, 6, 46, 4], [53, 10, 46, 8, "current"], [53, 17, 46, 15], [53, 18, 46, 16, "routes"], [53, 24, 46, 22], [53, 25, 46, 23, "length"], [53, 31, 46, 29], [53, 36, 46, 34], [53, 37, 46, 35], [53, 39, 46, 37], [54, 8, 47, 6], [54, 15, 47, 13, "undefined"], [54, 24, 47, 22], [55, 6, 48, 4], [56, 6, 49, 4], [56, 10, 49, 10, "routes"], [56, 17, 49, 16], [56, 20, 49, 19, "current"], [56, 27, 49, 26], [56, 28, 49, 27, "index"], [56, 33, 49, 32], [56, 37, 49, 36], [56, 41, 49, 40], [56, 44, 49, 43, "current"], [56, 51, 49, 50], [56, 52, 49, 51, "routes"], [56, 58, 49, 57], [56, 59, 49, 58, "slice"], [56, 64, 49, 63], [56, 65, 49, 64], [56, 66, 49, 65], [56, 68, 49, 67, "current"], [56, 75, 49, 74], [56, 76, 49, 75, "index"], [56, 81, 49, 80], [56, 84, 49, 83], [56, 85, 49, 84], [56, 86, 49, 85], [56, 89, 49, 88, "current"], [56, 96, 49, 95], [56, 97, 49, 96, "routes"], [56, 103, 49, 102], [57, 6, 50, 4], [57, 10, 50, 10, "route"], [57, 16, 50, 15], [57, 19, 50, 18, "routes"], [57, 26, 50, 24], [57, 27, 50, 25, "routes"], [57, 34, 50, 31], [57, 35, 50, 32, "length"], [57, 41, 50, 38], [57, 44, 50, 41], [57, 45, 50, 42], [57, 46, 50, 43], [59, 6, 52, 4], [60, 6, 53, 4, "Object"], [60, 12, 53, 10], [60, 13, 53, 11, "assign"], [60, 19, 53, 17], [60, 20, 53, 18, "params"], [60, 26, 53, 24], [60, 28, 53, 26], [61, 8, 54, 6, "initial"], [61, 15, 54, 13], [61, 17, 54, 15, "undefined"], [61, 26, 54, 24], [62, 8, 55, 6, "screen"], [62, 14, 55, 12], [62, 16, 55, 14, "undefined"], [62, 25, 55, 23], [63, 8, 56, 6, "params"], [63, 14, 56, 12], [63, 16, 56, 14, "undefined"], [63, 25, 56, 23], [64, 8, 57, 6, "state"], [64, 13, 57, 11], [64, 15, 57, 13, "undefined"], [65, 6, 58, 4], [65, 7, 58, 5], [65, 8, 58, 6], [66, 6, 59, 4], [66, 10, 59, 8, "routes"], [66, 17, 59, 14], [66, 18, 59, 15, "length"], [66, 24, 59, 21], [66, 29, 59, 26], [66, 30, 59, 27], [66, 34, 59, 31, "routes"], [66, 41, 59, 37], [66, 42, 59, 38], [66, 43, 59, 39], [66, 44, 59, 40], [66, 45, 59, 41, "key"], [66, 48, 59, 44], [66, 53, 59, 49, "undefined"], [66, 62, 59, 58], [66, 64, 59, 60], [67, 8, 60, 6, "params"], [67, 14, 60, 12], [67, 15, 60, 13, "initial"], [67, 22, 60, 20], [67, 25, 60, 23], [67, 29, 60, 27], [68, 8, 61, 6, "params"], [68, 14, 61, 12], [68, 15, 61, 13, "screen"], [68, 21, 61, 19], [68, 24, 61, 22, "route"], [68, 30, 61, 27], [68, 31, 61, 28, "name"], [68, 35, 61, 32], [69, 6, 62, 4], [69, 7, 62, 5], [69, 13, 62, 11], [69, 17, 62, 15, "routes"], [69, 24, 62, 21], [69, 25, 62, 22, "length"], [69, 31, 62, 28], [69, 36, 62, 33], [69, 37, 62, 34], [69, 41, 62, 38, "routes"], [69, 48, 62, 44], [69, 49, 62, 45], [69, 50, 62, 46], [69, 51, 62, 47], [69, 52, 62, 48, "key"], [69, 55, 62, 51], [69, 60, 62, 56, "undefined"], [69, 69, 62, 65], [69, 73, 62, 69, "routes"], [69, 80, 62, 75], [69, 81, 62, 76], [69, 82, 62, 77], [69, 83, 62, 78], [69, 84, 62, 79, "name"], [69, 88, 62, 83], [69, 93, 62, 88, "config"], [69, 99, 62, 94], [69, 101, 62, 96, "initialRouteName"], [69, 117, 62, 112], [69, 121, 62, 116, "routes"], [69, 128, 62, 122], [69, 129, 62, 123], [69, 130, 62, 124], [69, 131, 62, 125], [69, 132, 62, 126, "key"], [69, 135, 62, 129], [69, 140, 62, 134, "undefined"], [69, 149, 62, 143], [69, 151, 62, 145], [70, 8, 63, 6, "params"], [70, 14, 63, 12], [70, 15, 63, 13, "initial"], [70, 22, 63, 20], [70, 25, 63, 23], [70, 30, 63, 28], [71, 8, 64, 6, "params"], [71, 14, 64, 12], [71, 15, 64, 13, "screen"], [71, 21, 64, 19], [71, 24, 64, 22, "route"], [71, 30, 64, 27], [71, 31, 64, 28, "name"], [71, 35, 64, 32], [72, 6, 65, 4], [72, 7, 65, 5], [72, 13, 65, 11], [73, 8, 66, 6, "params"], [73, 14, 66, 12], [73, 15, 66, 13, "state"], [73, 20, 66, 18], [73, 23, 66, 21, "current"], [73, 30, 66, 28], [74, 8, 67, 6], [75, 6, 68, 4], [76, 6, 69, 4], [76, 10, 69, 8, "route"], [76, 16, 69, 13], [76, 17, 69, 14, "state"], [76, 22, 69, 19], [76, 24, 69, 21], [77, 8, 70, 6, "params"], [77, 14, 70, 12], [77, 15, 70, 13, "params"], [77, 21, 70, 19], [77, 24, 70, 22], [78, 10, 71, 8], [78, 13, 71, 11, "route"], [78, 19, 71, 16], [78, 20, 71, 17, "params"], [79, 8, 72, 6], [79, 9, 72, 7], [80, 8, 73, 6, "params"], [80, 14, 73, 12], [80, 15, 73, 13, "pop"], [80, 18, 73, 16], [80, 21, 73, 19], [80, 25, 73, 23], [81, 8, 74, 6, "params"], [81, 14, 74, 12], [81, 17, 74, 15, "params"], [81, 23, 74, 21], [81, 24, 74, 22, "params"], [81, 30, 74, 28], [82, 6, 75, 4], [82, 7, 75, 5], [82, 13, 75, 11], [83, 8, 76, 6, "params"], [83, 14, 76, 12], [83, 15, 76, 13, "path"], [83, 19, 76, 17], [83, 22, 76, 20, "route"], [83, 28, 76, 25], [83, 29, 76, 26, "path"], [83, 33, 76, 30], [84, 8, 77, 6, "params"], [84, 14, 77, 12], [84, 15, 77, 13, "params"], [84, 21, 77, 19], [84, 24, 77, 22, "route"], [84, 30, 77, 27], [84, 31, 77, 28, "params"], [84, 37, 77, 34], [85, 6, 78, 4], [86, 6, 79, 4, "current"], [86, 13, 79, 11], [86, 16, 79, 14, "route"], [86, 22, 79, 19], [86, 23, 79, 20, "state"], [86, 28, 79, 25], [87, 6, 80, 4, "config"], [87, 12, 80, 10], [87, 15, 80, 13, "config"], [87, 21, 80, 19], [87, 23, 80, 21, "screens"], [87, 30, 80, 28], [87, 33, 80, 31, "route"], [87, 39, 80, 36], [87, 40, 80, 37, "name"], [87, 44, 80, 41], [87, 45, 80, 42], [88, 6, 81, 4], [88, 10, 81, 8, "config"], [88, 16, 81, 14], [88, 18, 81, 16, "screens"], [88, 25, 81, 23], [88, 29, 81, 27, "Object"], [88, 35, 81, 33], [88, 36, 81, 34, "keys"], [88, 40, 81, 38], [88, 41, 81, 39, "config"], [88, 47, 81, 45], [88, 48, 81, 46, "screens"], [88, 55, 81, 53], [88, 56, 81, 54], [88, 57, 81, 55, "length"], [88, 63, 81, 61], [88, 65, 81, 63], [89, 8, 82, 6, "params"], [89, 14, 82, 12], [89, 15, 82, 13, "pop"], [89, 18, 82, 16], [89, 21, 82, 19], [89, 25, 82, 23], [90, 6, 83, 4], [91, 4, 84, 2], [92, 4, 85, 2], [92, 8, 85, 6, "payload"], [92, 15, 85, 13], [92, 17, 85, 15, "params"], [92, 23, 85, 21], [92, 24, 85, 22, "screen"], [92, 30, 85, 28], [92, 34, 85, 32, "payload"], [92, 41, 85, 39], [92, 43, 85, 41, "params"], [92, 49, 85, 47], [92, 50, 85, 48, "state"], [92, 55, 85, 53], [92, 57, 85, 55], [93, 6, 86, 4, "payload"], [93, 13, 86, 11], [93, 14, 86, 12, "pop"], [93, 17, 86, 15], [93, 20, 86, 18], [93, 24, 86, 22], [94, 4, 87, 2], [95, 4, 88, 2], [95, 8, 88, 6], [95, 9, 88, 7, "payload"], [95, 16, 88, 14], [95, 18, 88, 16], [96, 6, 89, 4], [97, 4, 90, 2], [99, 4, 92, 2], [100, 4, 93, 2], [101, 4, 94, 2], [101, 11, 94, 9], [102, 6, 95, 4, "type"], [102, 10, 95, 8], [102, 12, 95, 10], [102, 22, 95, 20], [103, 6, 96, 4, "payload"], [104, 4, 97, 2], [104, 5, 97, 3], [105, 2, 98, 0], [106, 2, 99, 0], [106, 6, 99, 6, "createNormalizedConfigItem"], [106, 32, 99, 32], [106, 35, 99, 35, "config"], [106, 41, 99, 41], [106, 45, 99, 45], [106, 52, 99, 52, "config"], [106, 58, 99, 58], [106, 63, 99, 63], [106, 71, 99, 71], [106, 75, 99, 75, "config"], [106, 81, 99, 81], [106, 85, 99, 85], [106, 89, 99, 89], [106, 92, 99, 92], [107, 4, 100, 2, "initialRouteName"], [107, 20, 100, 18], [107, 22, 100, 20, "config"], [107, 28, 100, 26], [107, 29, 100, 27, "initialRouteName"], [107, 45, 100, 43], [108, 4, 101, 2, "screens"], [108, 11, 101, 9], [108, 13, 101, 11, "config"], [108, 19, 101, 17], [108, 20, 101, 18, "screens"], [108, 27, 101, 25], [108, 31, 101, 29], [108, 35, 101, 33], [108, 38, 101, 36, "createNormalizedConfigs"], [108, 61, 101, 59], [108, 62, 101, 60, "config"], [108, 68, 101, 66], [108, 69, 101, 67, "screens"], [108, 76, 101, 74], [108, 77, 101, 75], [108, 80, 101, 78, "undefined"], [109, 2, 102, 0], [109, 3, 102, 1], [109, 6, 102, 4], [109, 7, 102, 5], [109, 8, 102, 6], [110, 2, 103, 0], [110, 6, 103, 6, "createNormalizedConfigs"], [110, 29, 103, 29], [110, 32, 103, 32, "options"], [110, 39, 103, 39], [110, 43, 103, 43, "Object"], [110, 49, 103, 49], [110, 50, 103, 50, "entries"], [110, 57, 103, 57], [110, 58, 103, 58, "options"], [110, 65, 103, 65], [110, 66, 103, 66], [110, 67, 103, 67, "reduce"], [110, 73, 103, 73], [110, 74, 103, 74], [110, 75, 103, 75, "acc"], [110, 78, 103, 78], [110, 80, 103, 78, "_ref"], [110, 84, 103, 78], [110, 89, 103, 91], [111, 4, 103, 91], [111, 8, 103, 91, "_ref2"], [111, 13, 103, 91], [111, 20, 103, 91, "_slicedToArray2"], [111, 35, 103, 91], [111, 36, 103, 91, "default"], [111, 43, 103, 91], [111, 45, 103, 91, "_ref"], [111, 49, 103, 91], [112, 6, 103, 81, "k"], [112, 7, 103, 82], [112, 10, 103, 82, "_ref2"], [112, 15, 103, 82], [113, 6, 103, 84, "v"], [113, 7, 103, 85], [113, 10, 103, 85, "_ref2"], [113, 15, 103, 85], [114, 4, 104, 2, "acc"], [114, 7, 104, 5], [114, 8, 104, 6, "k"], [114, 9, 104, 7], [114, 10, 104, 8], [114, 13, 104, 11, "createNormalizedConfigItem"], [114, 39, 104, 37], [114, 40, 104, 38, "v"], [114, 41, 104, 39], [114, 42, 104, 40], [115, 4, 105, 2], [115, 11, 105, 9, "acc"], [115, 14, 105, 12], [116, 2, 106, 0], [116, 3, 106, 1], [116, 5, 106, 3], [116, 6, 106, 4], [116, 7, 106, 5], [116, 8, 106, 6], [117, 0, 106, 7], [117, 3]], "functionMap": {"names": ["<global>", "getActionFromState", "createNormalizedConfigItem", "createNormalizedConfigs", "Object.entries.reduce$argument_0"], "mappings": "AAA;OCE;CD+F;mCEC;MFG;gCGC,0CC;CDG,KH"}}, "type": "js/module"}]}