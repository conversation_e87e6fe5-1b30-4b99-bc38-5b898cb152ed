{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 44}, "end": {"line": 4, "column": 31, "index": 75}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "@tanstack/query-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 76}, "end": {"line": 5, "column": 47, "index": 123}}], "key": "GAsr4MDTe9ve1mRxgvML4iY2BZg=", "exportNames": ["*"]}}, {"name": "./QueryClientProvider.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 124}, "end": {"line": 6, "column": 58, "index": 182}}], "key": "R2Cn87b3k6V9pZdY+hWd8Ft8C58=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use client\";\n\n  // src/HydrationBoundary.tsx\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HydrationBoundary = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _queryCore = require(_dependencyMap[1], \"@tanstack/query-core\");\n  var _QueryClientProvider = require(_dependencyMap[2], \"./QueryClientProvider.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var HydrationBoundary = _ref => {\n    var children = _ref.children,\n      _ref$options = _ref.options,\n      options = _ref$options === void 0 ? {} : _ref$options,\n      state = _ref.state,\n      queryClient = _ref.queryClient;\n    var client = (0, _QueryClientProvider.useQueryClient)(queryClient);\n    var optionsRef = React.useRef(options);\n    optionsRef.current = options;\n    var hydrationQueue = React.useMemo(() => {\n      if (state) {\n        if (typeof state !== \"object\") {\n          return;\n        }\n        var queryCache = client.getQueryCache();\n        var queries = state.queries || [];\n        var newQueries = [];\n        var existingQueries = [];\n        for (var dehydratedQuery of queries) {\n          var existingQuery = queryCache.get(dehydratedQuery.queryHash);\n          if (!existingQuery) {\n            newQueries.push(dehydratedQuery);\n          } else {\n            var hydrationIsNewer = dehydratedQuery.state.dataUpdatedAt > existingQuery.state.dataUpdatedAt || dehydratedQuery.promise && existingQuery.state.status !== \"pending\" && existingQuery.state.fetchStatus !== \"fetching\" && dehydratedQuery.dehydratedAt !== void 0 && dehydratedQuery.dehydratedAt > existingQuery.state.dataUpdatedAt;\n            if (hydrationIsNewer) {\n              existingQueries.push(dehydratedQuery);\n            }\n          }\n        }\n        if (newQueries.length > 0) {\n          (0, _queryCore.hydrate)(client, {\n            queries: newQueries\n          }, optionsRef.current);\n        }\n        if (existingQueries.length > 0) {\n          return existingQueries;\n        }\n      }\n      return void 0;\n    }, [client, state]);\n    React.useEffect(() => {\n      if (hydrationQueue) {\n        (0, _queryCore.hydrate)(client, {\n          queries: hydrationQueue\n        }, optionsRef.current);\n      }\n    }, [client, hydrationQueue]);\n    return children;\n  };\n  exports.HydrationBoundary = HydrationBoundary;\n});", "lineCount": 63, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 0], [5, 9, 3, 0, "defineProperty"], [5, 23, 3, 0], [5, 24, 3, 0, "exports"], [5, 31, 3, 0], [6, 4, 3, 0, "value"], [6, 9, 3, 0], [7, 2, 3, 0], [8, 2, 3, 0, "exports"], [8, 9, 3, 0], [8, 10, 3, 0, "HydrationBoundary"], [8, 27, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_queryCore"], [10, 16, 5, 0], [10, 19, 5, 0, "require"], [10, 26, 5, 0], [10, 27, 5, 0, "_dependencyMap"], [10, 41, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_QueryClientProvider"], [11, 26, 6, 0], [11, 29, 6, 0, "require"], [11, 36, 6, 0], [11, 37, 6, 0, "_dependencyMap"], [11, 51, 6, 0], [12, 2, 6, 58], [12, 11, 6, 58, "_interopRequireWildcard"], [12, 35, 6, 58, "e"], [12, 36, 6, 58], [12, 38, 6, 58, "t"], [12, 39, 6, 58], [12, 68, 6, 58, "WeakMap"], [12, 75, 6, 58], [12, 81, 6, 58, "r"], [12, 82, 6, 58], [12, 89, 6, 58, "WeakMap"], [12, 96, 6, 58], [12, 100, 6, 58, "n"], [12, 101, 6, 58], [12, 108, 6, 58, "WeakMap"], [12, 115, 6, 58], [12, 127, 6, 58, "_interopRequireWildcard"], [12, 150, 6, 58], [12, 162, 6, 58, "_interopRequireWildcard"], [12, 163, 6, 58, "e"], [12, 164, 6, 58], [12, 166, 6, 58, "t"], [12, 167, 6, 58], [12, 176, 6, 58, "t"], [12, 177, 6, 58], [12, 181, 6, 58, "e"], [12, 182, 6, 58], [12, 186, 6, 58, "e"], [12, 187, 6, 58], [12, 188, 6, 58, "__esModule"], [12, 198, 6, 58], [12, 207, 6, 58, "e"], [12, 208, 6, 58], [12, 214, 6, 58, "o"], [12, 215, 6, 58], [12, 217, 6, 58, "i"], [12, 218, 6, 58], [12, 220, 6, 58, "f"], [12, 221, 6, 58], [12, 226, 6, 58, "__proto__"], [12, 235, 6, 58], [12, 243, 6, 58, "default"], [12, 250, 6, 58], [12, 252, 6, 58, "e"], [12, 253, 6, 58], [12, 270, 6, 58, "e"], [12, 271, 6, 58], [12, 294, 6, 58, "e"], [12, 295, 6, 58], [12, 320, 6, 58, "e"], [12, 321, 6, 58], [12, 330, 6, 58, "f"], [12, 331, 6, 58], [12, 337, 6, 58, "o"], [12, 338, 6, 58], [12, 341, 6, 58, "t"], [12, 342, 6, 58], [12, 345, 6, 58, "n"], [12, 346, 6, 58], [12, 349, 6, 58, "r"], [12, 350, 6, 58], [12, 358, 6, 58, "o"], [12, 359, 6, 58], [12, 360, 6, 58, "has"], [12, 363, 6, 58], [12, 364, 6, 58, "e"], [12, 365, 6, 58], [12, 375, 6, 58, "o"], [12, 376, 6, 58], [12, 377, 6, 58, "get"], [12, 380, 6, 58], [12, 381, 6, 58, "e"], [12, 382, 6, 58], [12, 385, 6, 58, "o"], [12, 386, 6, 58], [12, 387, 6, 58, "set"], [12, 390, 6, 58], [12, 391, 6, 58, "e"], [12, 392, 6, 58], [12, 394, 6, 58, "f"], [12, 395, 6, 58], [12, 409, 6, 58, "_t"], [12, 411, 6, 58], [12, 415, 6, 58, "e"], [12, 416, 6, 58], [12, 432, 6, 58, "_t"], [12, 434, 6, 58], [12, 441, 6, 58, "hasOwnProperty"], [12, 455, 6, 58], [12, 456, 6, 58, "call"], [12, 460, 6, 58], [12, 461, 6, 58, "e"], [12, 462, 6, 58], [12, 464, 6, 58, "_t"], [12, 466, 6, 58], [12, 473, 6, 58, "i"], [12, 474, 6, 58], [12, 478, 6, 58, "o"], [12, 479, 6, 58], [12, 482, 6, 58, "Object"], [12, 488, 6, 58], [12, 489, 6, 58, "defineProperty"], [12, 503, 6, 58], [12, 508, 6, 58, "Object"], [12, 514, 6, 58], [12, 515, 6, 58, "getOwnPropertyDescriptor"], [12, 539, 6, 58], [12, 540, 6, 58, "e"], [12, 541, 6, 58], [12, 543, 6, 58, "_t"], [12, 545, 6, 58], [12, 552, 6, 58, "i"], [12, 553, 6, 58], [12, 554, 6, 58, "get"], [12, 557, 6, 58], [12, 561, 6, 58, "i"], [12, 562, 6, 58], [12, 563, 6, 58, "set"], [12, 566, 6, 58], [12, 570, 6, 58, "o"], [12, 571, 6, 58], [12, 572, 6, 58, "f"], [12, 573, 6, 58], [12, 575, 6, 58, "_t"], [12, 577, 6, 58], [12, 579, 6, 58, "i"], [12, 580, 6, 58], [12, 584, 6, 58, "f"], [12, 585, 6, 58], [12, 586, 6, 58, "_t"], [12, 588, 6, 58], [12, 592, 6, 58, "e"], [12, 593, 6, 58], [12, 594, 6, 58, "_t"], [12, 596, 6, 58], [12, 607, 6, 58, "f"], [12, 608, 6, 58], [12, 613, 6, 58, "e"], [12, 614, 6, 58], [12, 616, 6, 58, "t"], [12, 617, 6, 58], [13, 2, 7, 0], [13, 6, 7, 4, "HydrationBoundary"], [13, 23, 7, 21], [13, 26, 7, 24, "_ref"], [13, 30, 7, 24], [13, 34, 12, 6], [14, 4, 12, 6], [14, 8, 8, 2, "children"], [14, 16, 8, 10], [14, 19, 8, 10, "_ref"], [14, 23, 8, 10], [14, 24, 8, 2, "children"], [14, 32, 8, 10], [15, 6, 8, 10, "_ref$options"], [15, 18, 8, 10], [15, 21, 8, 10, "_ref"], [15, 25, 8, 10], [15, 26, 9, 2, "options"], [15, 33, 9, 9], [16, 6, 9, 2, "options"], [16, 13, 9, 9], [16, 16, 9, 9, "_ref$options"], [16, 28, 9, 9], [16, 42, 9, 12], [16, 43, 9, 13], [16, 44, 9, 14], [16, 47, 9, 14, "_ref$options"], [16, 59, 9, 14], [17, 6, 10, 2, "state"], [17, 11, 10, 7], [17, 14, 10, 7, "_ref"], [17, 18, 10, 7], [17, 19, 10, 2, "state"], [17, 24, 10, 7], [18, 6, 11, 2, "queryClient"], [18, 17, 11, 13], [18, 20, 11, 13, "_ref"], [18, 24, 11, 13], [18, 25, 11, 2, "queryClient"], [18, 36, 11, 13], [19, 4, 13, 2], [19, 8, 13, 8, "client"], [19, 14, 13, 14], [19, 17, 13, 17], [19, 21, 13, 17, "useQueryClient"], [19, 56, 13, 31], [19, 58, 13, 32, "queryClient"], [19, 69, 13, 43], [19, 70, 13, 44], [20, 4, 14, 2], [20, 8, 14, 8, "optionsRef"], [20, 18, 14, 18], [20, 21, 14, 21, "React"], [20, 26, 14, 26], [20, 27, 14, 27, "useRef"], [20, 33, 14, 33], [20, 34, 14, 34, "options"], [20, 41, 14, 41], [20, 42, 14, 42], [21, 4, 15, 2, "optionsRef"], [21, 14, 15, 12], [21, 15, 15, 13, "current"], [21, 22, 15, 20], [21, 25, 15, 23, "options"], [21, 32, 15, 30], [22, 4, 16, 2], [22, 8, 16, 8, "hydrationQueue"], [22, 22, 16, 22], [22, 25, 16, 25, "React"], [22, 30, 16, 30], [22, 31, 16, 31, "useMemo"], [22, 38, 16, 38], [22, 39, 16, 39], [22, 45, 16, 45], [23, 6, 17, 4], [23, 10, 17, 8, "state"], [23, 15, 17, 13], [23, 17, 17, 15], [24, 8, 18, 6], [24, 12, 18, 10], [24, 19, 18, 17, "state"], [24, 24, 18, 22], [24, 29, 18, 27], [24, 37, 18, 35], [24, 39, 18, 37], [25, 10, 19, 8], [26, 8, 20, 6], [27, 8, 21, 6], [27, 12, 21, 12, "queryCache"], [27, 22, 21, 22], [27, 25, 21, 25, "client"], [27, 31, 21, 31], [27, 32, 21, 32, "get<PERSON><PERSON><PERSON><PERSON>ache"], [27, 45, 21, 45], [27, 46, 21, 46], [27, 47, 21, 47], [28, 8, 22, 6], [28, 12, 22, 12, "queries"], [28, 19, 22, 19], [28, 22, 22, 22, "state"], [28, 27, 22, 27], [28, 28, 22, 28, "queries"], [28, 35, 22, 35], [28, 39, 22, 39], [28, 41, 22, 41], [29, 8, 23, 6], [29, 12, 23, 12, "newQueries"], [29, 22, 23, 22], [29, 25, 23, 25], [29, 27, 23, 27], [30, 8, 24, 6], [30, 12, 24, 12, "existingQueries"], [30, 27, 24, 27], [30, 30, 24, 30], [30, 32, 24, 32], [31, 8, 25, 6], [31, 13, 25, 11], [31, 17, 25, 17, "dehydrated<PERSON><PERSON>y"], [31, 32, 25, 32], [31, 36, 25, 36, "queries"], [31, 43, 25, 43], [31, 45, 25, 45], [32, 10, 26, 8], [32, 14, 26, 14, "existingQuery"], [32, 27, 26, 27], [32, 30, 26, 30, "queryCache"], [32, 40, 26, 40], [32, 41, 26, 41, "get"], [32, 44, 26, 44], [32, 45, 26, 45, "dehydrated<PERSON><PERSON>y"], [32, 60, 26, 60], [32, 61, 26, 61, "queryHash"], [32, 70, 26, 70], [32, 71, 26, 71], [33, 10, 27, 8], [33, 14, 27, 12], [33, 15, 27, 13, "existingQuery"], [33, 28, 27, 26], [33, 30, 27, 28], [34, 12, 28, 10, "newQueries"], [34, 22, 28, 20], [34, 23, 28, 21, "push"], [34, 27, 28, 25], [34, 28, 28, 26, "dehydrated<PERSON><PERSON>y"], [34, 43, 28, 41], [34, 44, 28, 42], [35, 10, 29, 8], [35, 11, 29, 9], [35, 17, 29, 15], [36, 12, 30, 10], [36, 16, 30, 16, "hydrationIsNewer"], [36, 32, 30, 32], [36, 35, 30, 35, "dehydrated<PERSON><PERSON>y"], [36, 50, 30, 50], [36, 51, 30, 51, "state"], [36, 56, 30, 56], [36, 57, 30, 57, "dataUpdatedAt"], [36, 70, 30, 70], [36, 73, 30, 73, "existingQuery"], [36, 86, 30, 86], [36, 87, 30, 87, "state"], [36, 92, 30, 92], [36, 93, 30, 93, "dataUpdatedAt"], [36, 106, 30, 106], [36, 110, 30, 110, "dehydrated<PERSON><PERSON>y"], [36, 125, 30, 125], [36, 126, 30, 126, "promise"], [36, 133, 30, 133], [36, 137, 30, 137, "existingQuery"], [36, 150, 30, 150], [36, 151, 30, 151, "state"], [36, 156, 30, 156], [36, 157, 30, 157, "status"], [36, 163, 30, 163], [36, 168, 30, 168], [36, 177, 30, 177], [36, 181, 30, 181, "existingQuery"], [36, 194, 30, 194], [36, 195, 30, 195, "state"], [36, 200, 30, 200], [36, 201, 30, 201, "fetchStatus"], [36, 212, 30, 212], [36, 217, 30, 217], [36, 227, 30, 227], [36, 231, 30, 231, "dehydrated<PERSON><PERSON>y"], [36, 246, 30, 246], [36, 247, 30, 247, "dehydratedAt"], [36, 259, 30, 259], [36, 264, 30, 264], [36, 269, 30, 269], [36, 270, 30, 270], [36, 274, 30, 274, "dehydrated<PERSON><PERSON>y"], [36, 289, 30, 289], [36, 290, 30, 290, "dehydratedAt"], [36, 302, 30, 302], [36, 305, 30, 305, "existingQuery"], [36, 318, 30, 318], [36, 319, 30, 319, "state"], [36, 324, 30, 324], [36, 325, 30, 325, "dataUpdatedAt"], [36, 338, 30, 338], [37, 12, 31, 10], [37, 16, 31, 14, "hydrationIsNewer"], [37, 32, 31, 30], [37, 34, 31, 32], [38, 14, 32, 12, "existingQueries"], [38, 29, 32, 27], [38, 30, 32, 28, "push"], [38, 34, 32, 32], [38, 35, 32, 33, "dehydrated<PERSON><PERSON>y"], [38, 50, 32, 48], [38, 51, 32, 49], [39, 12, 33, 10], [40, 10, 34, 8], [41, 8, 35, 6], [42, 8, 36, 6], [42, 12, 36, 10, "newQueries"], [42, 22, 36, 20], [42, 23, 36, 21, "length"], [42, 29, 36, 27], [42, 32, 36, 30], [42, 33, 36, 31], [42, 35, 36, 33], [43, 10, 37, 8], [43, 14, 37, 8, "hydrate"], [43, 32, 37, 15], [43, 34, 37, 16, "client"], [43, 40, 37, 22], [43, 42, 37, 24], [44, 12, 37, 26, "queries"], [44, 19, 37, 33], [44, 21, 37, 35, "newQueries"], [45, 10, 37, 46], [45, 11, 37, 47], [45, 13, 37, 49, "optionsRef"], [45, 23, 37, 59], [45, 24, 37, 60, "current"], [45, 31, 37, 67], [45, 32, 37, 68], [46, 8, 38, 6], [47, 8, 39, 6], [47, 12, 39, 10, "existingQueries"], [47, 27, 39, 25], [47, 28, 39, 26, "length"], [47, 34, 39, 32], [47, 37, 39, 35], [47, 38, 39, 36], [47, 40, 39, 38], [48, 10, 40, 8], [48, 17, 40, 15, "existingQueries"], [48, 32, 40, 30], [49, 8, 41, 6], [50, 6, 42, 4], [51, 6, 43, 4], [51, 13, 43, 11], [51, 18, 43, 16], [51, 19, 43, 17], [52, 4, 44, 2], [52, 5, 44, 3], [52, 7, 44, 5], [52, 8, 44, 6, "client"], [52, 14, 44, 12], [52, 16, 44, 14, "state"], [52, 21, 44, 19], [52, 22, 44, 20], [52, 23, 44, 21], [53, 4, 45, 2, "React"], [53, 9, 45, 7], [53, 10, 45, 8, "useEffect"], [53, 19, 45, 17], [53, 20, 45, 18], [53, 26, 45, 24], [54, 6, 46, 4], [54, 10, 46, 8, "hydrationQueue"], [54, 24, 46, 22], [54, 26, 46, 24], [55, 8, 47, 6], [55, 12, 47, 6, "hydrate"], [55, 30, 47, 13], [55, 32, 47, 14, "client"], [55, 38, 47, 20], [55, 40, 47, 22], [56, 10, 47, 24, "queries"], [56, 17, 47, 31], [56, 19, 47, 33, "hydrationQueue"], [57, 8, 47, 48], [57, 9, 47, 49], [57, 11, 47, 51, "optionsRef"], [57, 21, 47, 61], [57, 22, 47, 62, "current"], [57, 29, 47, 69], [57, 30, 47, 70], [58, 6, 48, 4], [59, 4, 49, 2], [59, 5, 49, 3], [59, 7, 49, 5], [59, 8, 49, 6, "client"], [59, 14, 49, 12], [59, 16, 49, 14, "hydrationQueue"], [59, 30, 49, 28], [59, 31, 49, 29], [59, 32, 49, 30], [60, 4, 50, 2], [60, 11, 50, 9, "children"], [60, 19, 50, 17], [61, 2, 51, 0], [61, 3, 51, 1], [62, 2, 51, 2, "exports"], [62, 9, 51, 2], [62, 10, 51, 2, "HydrationBoundary"], [62, 27, 51, 2], [62, 30, 51, 2, "HydrationBoundary"], [62, 47, 51, 2], [63, 0, 51, 2], [63, 3]], "functionMap": {"names": ["<global>", "HydrationBoundary", "React.useMemo$argument_0", "React.useEffect$argument_0"], "mappings": "AAA;wBCM;uCCS;GD4B;kBEC;GFI;CDE"}}, "type": "js/module"}]}