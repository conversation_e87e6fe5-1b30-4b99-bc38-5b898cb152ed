import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Mic, Keyboard } from 'lucide-react-native';
import { useColors } from './useColors';

export default function Header({ voiceMode, onToggleVoiceMode, onDone }) {
  const insets = useSafeAreaInsets();
  const colors = useColors();

  return (
    <View
      style={{
        paddingTop: insets.top + 16,
        paddingHorizontal: 20,
        paddingBottom: 16,
        backgroundColor: colors.background,
        borderBottomWidth: 1,
        borderBottomColor: colors.outline,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <TouchableOpacity
        onPress={onToggleVoiceMode}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: colors.primary,
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 20,
          gap: 6,
        }}
      >
        {voiceMode ? (
          <>
            <Mic size={18} color={colors.background} />
            <Text style={{
              fontSize: 14,
              fontFamily: 'Poppins_500Medium',
              color: colors.background,
            }}>
              Voice
            </Text>
          </>
        ) : (
          <>
            <Keyboard size={18} color={colors.background} />
            <Text style={{
              fontSize: 14,
              fontFamily: 'Poppins_500Medium',
              color: colors.background,
            }}>
              Text
            </Text>
          </>
        )}
      </TouchableOpacity>
      <Text
        style={{
          fontSize: 20,
          fontFamily: 'Poppins_600SemiBold',
          color: colors.text,
        }}
      >
        Session
      </Text>
      <TouchableOpacity onPress={onDone}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: 'Poppins_500Medium',
            color: colors.primary,
          }}
        >
          Done
        </Text>
      </TouchableOpacity>
    </View>
  );
}
