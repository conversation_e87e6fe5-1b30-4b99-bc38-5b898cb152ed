{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "./subscribable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 23}, "end": {"line": 2, "column": 49, "index": 72}}], "key": "f0fxTGZggQRtb//cHMvH9AHIWOw=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 73}, "end": {"line": 3, "column": 38, "index": 111}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.focusManager = exports.FocusManager = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _subscribable = require(_dependencyMap[8], \"./subscribable.js\");\n  var _utils = require(_dependencyMap[9], \"./utils.js\");\n  var _focused, _cleanup, _setup; // src/focusManager.ts\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var FocusManager = exports.FocusManager = (_focused = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"focused\"), _cleanup = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"cleanup\"), _setup = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"setup\"), /*#__PURE__*/function (_Subscribable) {\n    function FocusManager() {\n      var _this;\n      (0, _classCallCheck2.default)(this, FocusManager);\n      _this = _callSuper(this, FocusManager);\n      Object.defineProperty(_this, _focused, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _cleanup, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _setup, {\n        writable: true,\n        value: void 0\n      });\n      (0, _classPrivateFieldLooseBase2.default)(_this, _setup)[_setup] = onFocus => {\n        if (!_utils.isServer && window.addEventListener) {\n          var listener = () => onFocus();\n          window.addEventListener(\"visibilitychange\", listener, false);\n          return () => {\n            window.removeEventListener(\"visibilitychange\", listener);\n          };\n        }\n        return;\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(FocusManager, _Subscribable);\n    return (0, _createClass2.default)(FocusManager, [{\n      key: \"onSubscribe\",\n      value: function onSubscribe() {\n        if (!(0, _classPrivateFieldLooseBase2.default)(this, _cleanup)[_cleanup]) {\n          this.setEventListener((0, _classPrivateFieldLooseBase2.default)(this, _setup)[_setup]);\n        }\n      }\n    }, {\n      key: \"onUnsubscribe\",\n      value: function onUnsubscribe() {\n        if (!this.hasListeners()) {\n          (0, _classPrivateFieldLooseBase2.default)(this, _cleanup)[_cleanup]?.();\n          (0, _classPrivateFieldLooseBase2.default)(this, _cleanup)[_cleanup] = void 0;\n        }\n      }\n    }, {\n      key: \"setEventListener\",\n      value: function setEventListener(setup) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _setup)[_setup] = setup;\n        (0, _classPrivateFieldLooseBase2.default)(this, _cleanup)[_cleanup]?.();\n        (0, _classPrivateFieldLooseBase2.default)(this, _cleanup)[_cleanup] = setup(focused => {\n          if (typeof focused === \"boolean\") {\n            this.setFocused(focused);\n          } else {\n            this.onFocus();\n          }\n        });\n      }\n    }, {\n      key: \"setFocused\",\n      value: function setFocused(focused) {\n        var changed = (0, _classPrivateFieldLooseBase2.default)(this, _focused)[_focused] !== focused;\n        if (changed) {\n          (0, _classPrivateFieldLooseBase2.default)(this, _focused)[_focused] = focused;\n          this.onFocus();\n        }\n      }\n    }, {\n      key: \"onFocus\",\n      value: function onFocus() {\n        var isFocused = this.isFocused();\n        this.listeners.forEach(listener => {\n          listener(isFocused);\n        });\n      }\n    }, {\n      key: \"isFocused\",\n      value: function isFocused() {\n        if (typeof (0, _classPrivateFieldLooseBase2.default)(this, _focused)[_focused] === \"boolean\") {\n          return (0, _classPrivateFieldLooseBase2.default)(this, _focused)[_focused];\n        }\n        return globalThis.document?.visibilityState !== \"hidden\";\n      }\n    }]);\n  }(_subscribable.Subscribable));\n  var focusManager = exports.focusManager = new FocusManager();\n});", "lineCount": 105, "map": [[14, 2, 2, 0], [14, 6, 2, 0, "_subscribable"], [14, 19, 2, 0], [14, 22, 2, 0, "require"], [14, 29, 2, 0], [14, 30, 2, 0, "_dependencyMap"], [14, 44, 2, 0], [15, 2, 3, 0], [15, 6, 3, 0, "_utils"], [15, 12, 3, 0], [15, 15, 3, 0, "require"], [15, 22, 3, 0], [15, 23, 3, 0, "_dependencyMap"], [15, 37, 3, 0], [16, 2, 3, 38], [16, 6, 3, 38, "_focused"], [16, 14, 3, 38], [16, 16, 3, 38, "_cleanup"], [16, 24, 3, 38], [16, 26, 3, 38, "_setup"], [16, 32, 3, 38], [16, 34, 1, 0], [17, 2, 1, 0], [17, 11, 1, 0, "_callSuper"], [17, 22, 1, 0, "t"], [17, 23, 1, 0], [17, 25, 1, 0, "o"], [17, 26, 1, 0], [17, 28, 1, 0, "e"], [17, 29, 1, 0], [17, 40, 1, 0, "o"], [17, 41, 1, 0], [17, 48, 1, 0, "_getPrototypeOf2"], [17, 64, 1, 0], [17, 65, 1, 0, "default"], [17, 72, 1, 0], [17, 74, 1, 0, "o"], [17, 75, 1, 0], [17, 82, 1, 0, "_possibleConstructorReturn2"], [17, 109, 1, 0], [17, 110, 1, 0, "default"], [17, 117, 1, 0], [17, 119, 1, 0, "t"], [17, 120, 1, 0], [17, 122, 1, 0, "_isNativeReflectConstruct"], [17, 147, 1, 0], [17, 152, 1, 0, "Reflect"], [17, 159, 1, 0], [17, 160, 1, 0, "construct"], [17, 169, 1, 0], [17, 170, 1, 0, "o"], [17, 171, 1, 0], [17, 173, 1, 0, "e"], [17, 174, 1, 0], [17, 186, 1, 0, "_getPrototypeOf2"], [17, 202, 1, 0], [17, 203, 1, 0, "default"], [17, 210, 1, 0], [17, 212, 1, 0, "t"], [17, 213, 1, 0], [17, 215, 1, 0, "constructor"], [17, 226, 1, 0], [17, 230, 1, 0, "o"], [17, 231, 1, 0], [17, 232, 1, 0, "apply"], [17, 237, 1, 0], [17, 238, 1, 0, "t"], [17, 239, 1, 0], [17, 241, 1, 0, "e"], [17, 242, 1, 0], [18, 2, 1, 0], [18, 11, 1, 0, "_isNativeReflectConstruct"], [18, 37, 1, 0], [18, 51, 1, 0, "t"], [18, 52, 1, 0], [18, 56, 1, 0, "Boolean"], [18, 63, 1, 0], [18, 64, 1, 0, "prototype"], [18, 73, 1, 0], [18, 74, 1, 0, "valueOf"], [18, 81, 1, 0], [18, 82, 1, 0, "call"], [18, 86, 1, 0], [18, 87, 1, 0, "Reflect"], [18, 94, 1, 0], [18, 95, 1, 0, "construct"], [18, 104, 1, 0], [18, 105, 1, 0, "Boolean"], [18, 112, 1, 0], [18, 145, 1, 0, "t"], [18, 146, 1, 0], [18, 159, 1, 0, "_isNativeReflectConstruct"], [18, 184, 1, 0], [18, 196, 1, 0, "_isNativeReflectConstruct"], [18, 197, 1, 0], [18, 210, 1, 0, "t"], [18, 211, 1, 0], [19, 2, 4, 0], [19, 6, 4, 4, "FocusManager"], [19, 18, 4, 16], [19, 21, 4, 16, "exports"], [19, 28, 4, 16], [19, 29, 4, 16, "FocusManager"], [19, 41, 4, 16], [19, 45, 4, 16, "_focused"], [19, 53, 4, 16], [19, 73, 4, 16, "_classPrivateFieldLooseKey2"], [19, 100, 4, 16], [19, 101, 4, 16, "default"], [19, 108, 4, 16], [19, 122, 4, 16, "_cleanup"], [19, 130, 4, 16], [19, 150, 4, 16, "_classPrivateFieldLooseKey2"], [19, 177, 4, 16], [19, 178, 4, 16, "default"], [19, 185, 4, 16], [19, 199, 4, 16, "_setup"], [19, 205, 4, 16], [19, 225, 4, 16, "_classPrivateFieldLooseKey2"], [19, 252, 4, 16], [19, 253, 4, 16, "default"], [19, 260, 4, 16], [19, 295, 4, 16, "_Subscribable"], [19, 308, 4, 16], [20, 4, 8, 2], [20, 13, 8, 2, "FocusManager"], [20, 26, 8, 2], [20, 28, 8, 16], [21, 6, 8, 16], [21, 10, 8, 16, "_this"], [21, 15, 8, 16], [22, 6, 8, 16], [22, 10, 8, 16, "_classCallCheck2"], [22, 26, 8, 16], [22, 27, 8, 16, "default"], [22, 34, 8, 16], [22, 42, 8, 16, "FocusManager"], [22, 54, 8, 16], [23, 6, 9, 4, "_this"], [23, 11, 9, 4], [23, 14, 9, 4, "_callSuper"], [23, 24, 9, 4], [23, 31, 9, 4, "FocusManager"], [23, 43, 9, 4], [24, 6, 9, 12, "Object"], [24, 12, 9, 12], [24, 13, 9, 12, "defineProperty"], [24, 27, 9, 12], [24, 28, 9, 12, "_this"], [24, 33, 9, 12], [24, 35, 9, 12, "_focused"], [24, 43, 9, 12], [25, 8, 9, 12, "writable"], [25, 16, 9, 12], [26, 8, 9, 12, "value"], [26, 13, 9, 12], [27, 6, 9, 12], [28, 6, 9, 12, "Object"], [28, 12, 9, 12], [28, 13, 9, 12, "defineProperty"], [28, 27, 9, 12], [28, 28, 9, 12, "_this"], [28, 33, 9, 12], [28, 35, 9, 12, "_cleanup"], [28, 43, 9, 12], [29, 8, 9, 12, "writable"], [29, 16, 9, 12], [30, 8, 9, 12, "value"], [30, 13, 9, 12], [31, 6, 9, 12], [32, 6, 9, 12, "Object"], [32, 12, 9, 12], [32, 13, 9, 12, "defineProperty"], [32, 27, 9, 12], [32, 28, 9, 12, "_this"], [32, 33, 9, 12], [32, 35, 9, 12, "_setup"], [32, 41, 9, 12], [33, 8, 9, 12, "writable"], [33, 16, 9, 12], [34, 8, 9, 12, "value"], [34, 13, 9, 12], [35, 6, 9, 12], [36, 6, 10, 4], [36, 10, 10, 4, "_classPrivateFieldLooseBase2"], [36, 38, 10, 4], [36, 39, 10, 4, "default"], [36, 46, 10, 4], [36, 48, 10, 4, "_this"], [36, 53, 10, 4], [36, 55, 10, 4, "_setup"], [36, 61, 10, 4], [36, 63, 10, 4, "_setup"], [36, 69, 10, 4], [36, 73, 10, 19, "onFocus"], [36, 80, 10, 26], [36, 84, 10, 31], [37, 8, 11, 6], [37, 12, 11, 10], [37, 13, 11, 11, "isServer"], [37, 28, 11, 19], [37, 32, 11, 23, "window"], [37, 38, 11, 29], [37, 39, 11, 30, "addEventListener"], [37, 55, 11, 46], [37, 57, 11, 48], [38, 10, 12, 8], [38, 14, 12, 14, "listener"], [38, 22, 12, 22], [38, 25, 12, 25, "listener"], [38, 26, 12, 25], [38, 31, 12, 31, "onFocus"], [38, 38, 12, 38], [38, 39, 12, 39], [38, 40, 12, 40], [39, 10, 13, 8, "window"], [39, 16, 13, 14], [39, 17, 13, 15, "addEventListener"], [39, 33, 13, 31], [39, 34, 13, 32], [39, 52, 13, 50], [39, 54, 13, 52, "listener"], [39, 62, 13, 60], [39, 64, 13, 62], [39, 69, 13, 67], [39, 70, 13, 68], [40, 10, 14, 8], [40, 17, 14, 15], [40, 23, 14, 21], [41, 12, 15, 10, "window"], [41, 18, 15, 16], [41, 19, 15, 17, "removeEventListener"], [41, 38, 15, 36], [41, 39, 15, 37], [41, 57, 15, 55], [41, 59, 15, 57, "listener"], [41, 67, 15, 65], [41, 68, 15, 66], [42, 10, 16, 8], [42, 11, 16, 9], [43, 8, 17, 6], [44, 8, 18, 6], [45, 6, 19, 4], [45, 7, 19, 5], [46, 6, 19, 6], [46, 13, 19, 6, "_this"], [46, 18, 19, 6], [47, 4, 20, 2], [48, 4, 20, 3], [48, 8, 20, 3, "_inherits2"], [48, 18, 20, 3], [48, 19, 20, 3, "default"], [48, 26, 20, 3], [48, 28, 20, 3, "FocusManager"], [48, 40, 20, 3], [48, 42, 20, 3, "_Subscribable"], [48, 55, 20, 3], [49, 4, 20, 3], [49, 15, 20, 3, "_createClass2"], [49, 28, 20, 3], [49, 29, 20, 3, "default"], [49, 36, 20, 3], [49, 38, 20, 3, "FocusManager"], [49, 50, 20, 3], [50, 6, 20, 3, "key"], [50, 9, 20, 3], [51, 6, 20, 3, "value"], [51, 11, 20, 3], [51, 13, 21, 2], [51, 22, 21, 2, "onSubscribe"], [51, 33, 21, 13, "onSubscribe"], [51, 34, 21, 13], [51, 36, 21, 16], [52, 8, 22, 4], [52, 12, 22, 8], [52, 17, 22, 8, "_classPrivateFieldLooseBase2"], [52, 45, 22, 8], [52, 46, 22, 8, "default"], [52, 53, 22, 8], [52, 55, 22, 9], [52, 59, 22, 13], [52, 61, 22, 13, "_cleanup"], [52, 69, 22, 13], [52, 71, 22, 13, "_cleanup"], [52, 79, 22, 13], [52, 80, 22, 22], [52, 82, 22, 24], [53, 10, 23, 6], [53, 14, 23, 10], [53, 15, 23, 11, "setEventListener"], [53, 31, 23, 27], [53, 36, 23, 27, "_classPrivateFieldLooseBase2"], [53, 64, 23, 27], [53, 65, 23, 27, "default"], [53, 72, 23, 27], [53, 74, 23, 28], [53, 78, 23, 32], [53, 80, 23, 32, "_setup"], [53, 86, 23, 32], [53, 88, 23, 32, "_setup"], [53, 94, 23, 32], [53, 95, 23, 39], [53, 96, 23, 40], [54, 8, 24, 4], [55, 6, 25, 2], [56, 4, 25, 3], [57, 6, 25, 3, "key"], [57, 9, 25, 3], [58, 6, 25, 3, "value"], [58, 11, 25, 3], [58, 13, 26, 2], [58, 22, 26, 2, "onUnsubscribe"], [58, 35, 26, 15, "onUnsubscribe"], [58, 36, 26, 15], [58, 38, 26, 18], [59, 8, 27, 4], [59, 12, 27, 8], [59, 13, 27, 9], [59, 17, 27, 13], [59, 18, 27, 14, "hasListeners"], [59, 30, 27, 26], [59, 31, 27, 27], [59, 32, 27, 28], [59, 34, 27, 30], [60, 10, 28, 6], [60, 14, 28, 6, "_classPrivateFieldLooseBase2"], [60, 42, 28, 6], [60, 43, 28, 6, "default"], [60, 50, 28, 6], [60, 56, 28, 10], [60, 58, 28, 10, "_cleanup"], [60, 66, 28, 10], [60, 68, 28, 10, "_cleanup"], [60, 76, 28, 10], [61, 10, 29, 6], [61, 14, 29, 6, "_classPrivateFieldLooseBase2"], [61, 42, 29, 6], [61, 43, 29, 6, "default"], [61, 50, 29, 6], [61, 56, 29, 10], [61, 58, 29, 10, "_cleanup"], [61, 66, 29, 10], [61, 68, 29, 10, "_cleanup"], [61, 76, 29, 10], [61, 80, 29, 22], [61, 85, 29, 27], [61, 86, 29, 28], [62, 8, 30, 4], [63, 6, 31, 2], [64, 4, 31, 3], [65, 6, 31, 3, "key"], [65, 9, 31, 3], [66, 6, 31, 3, "value"], [66, 11, 31, 3], [66, 13, 32, 2], [66, 22, 32, 2, "setEventListener"], [66, 38, 32, 18, "setEventListener"], [66, 39, 32, 19, "setup"], [66, 44, 32, 24], [66, 46, 32, 26], [67, 8, 33, 4], [67, 12, 33, 4, "_classPrivateFieldLooseBase2"], [67, 40, 33, 4], [67, 41, 33, 4, "default"], [67, 48, 33, 4], [67, 54, 33, 8], [67, 56, 33, 8, "_setup"], [67, 62, 33, 8], [67, 64, 33, 8, "_setup"], [67, 70, 33, 8], [67, 74, 33, 18, "setup"], [67, 79, 33, 23], [68, 8, 34, 4], [68, 12, 34, 4, "_classPrivateFieldLooseBase2"], [68, 40, 34, 4], [68, 41, 34, 4, "default"], [68, 48, 34, 4], [68, 54, 34, 8], [68, 56, 34, 8, "_cleanup"], [68, 64, 34, 8], [68, 66, 34, 8, "_cleanup"], [68, 74, 34, 8], [69, 8, 35, 4], [69, 12, 35, 4, "_classPrivateFieldLooseBase2"], [69, 40, 35, 4], [69, 41, 35, 4, "default"], [69, 48, 35, 4], [69, 54, 35, 8], [69, 56, 35, 8, "_cleanup"], [69, 64, 35, 8], [69, 66, 35, 8, "_cleanup"], [69, 74, 35, 8], [69, 78, 35, 20, "setup"], [69, 83, 35, 25], [69, 84, 35, 27, "focused"], [69, 91, 35, 34], [69, 95, 35, 39], [70, 10, 36, 6], [70, 14, 36, 10], [70, 21, 36, 17, "focused"], [70, 28, 36, 24], [70, 33, 36, 29], [70, 42, 36, 38], [70, 44, 36, 40], [71, 12, 37, 8], [71, 16, 37, 12], [71, 17, 37, 13, "setFocused"], [71, 27, 37, 23], [71, 28, 37, 24, "focused"], [71, 35, 37, 31], [71, 36, 37, 32], [72, 10, 38, 6], [72, 11, 38, 7], [72, 17, 38, 13], [73, 12, 39, 8], [73, 16, 39, 12], [73, 17, 39, 13, "onFocus"], [73, 24, 39, 20], [73, 25, 39, 21], [73, 26, 39, 22], [74, 10, 40, 6], [75, 8, 41, 4], [75, 9, 41, 5], [75, 10, 41, 6], [76, 6, 42, 2], [77, 4, 42, 3], [78, 6, 42, 3, "key"], [78, 9, 42, 3], [79, 6, 42, 3, "value"], [79, 11, 42, 3], [79, 13, 43, 2], [79, 22, 43, 2, "setFocused"], [79, 32, 43, 12, "setFocused"], [79, 33, 43, 13, "focused"], [79, 40, 43, 20], [79, 42, 43, 22], [80, 8, 44, 4], [80, 12, 44, 10, "changed"], [80, 19, 44, 17], [80, 22, 44, 20], [80, 26, 44, 20, "_classPrivateFieldLooseBase2"], [80, 54, 44, 20], [80, 55, 44, 20, "default"], [80, 62, 44, 20], [80, 68, 44, 24], [80, 70, 44, 24, "_focused"], [80, 78, 44, 24], [80, 80, 44, 24, "_focused"], [80, 88, 44, 24], [80, 94, 44, 38, "focused"], [80, 101, 44, 45], [81, 8, 45, 4], [81, 12, 45, 8, "changed"], [81, 19, 45, 15], [81, 21, 45, 17], [82, 10, 46, 6], [82, 14, 46, 6, "_classPrivateFieldLooseBase2"], [82, 42, 46, 6], [82, 43, 46, 6, "default"], [82, 50, 46, 6], [82, 56, 46, 10], [82, 58, 46, 10, "_focused"], [82, 66, 46, 10], [82, 68, 46, 10, "_focused"], [82, 76, 46, 10], [82, 80, 46, 22, "focused"], [82, 87, 46, 29], [83, 10, 47, 6], [83, 14, 47, 10], [83, 15, 47, 11, "onFocus"], [83, 22, 47, 18], [83, 23, 47, 19], [83, 24, 47, 20], [84, 8, 48, 4], [85, 6, 49, 2], [86, 4, 49, 3], [87, 6, 49, 3, "key"], [87, 9, 49, 3], [88, 6, 49, 3, "value"], [88, 11, 49, 3], [88, 13, 50, 2], [88, 22, 50, 2, "onFocus"], [88, 29, 50, 9, "onFocus"], [88, 30, 50, 9], [88, 32, 50, 12], [89, 8, 51, 4], [89, 12, 51, 10, "isFocused"], [89, 21, 51, 19], [89, 24, 51, 22], [89, 28, 51, 26], [89, 29, 51, 27, "isFocused"], [89, 38, 51, 36], [89, 39, 51, 37], [89, 40, 51, 38], [90, 8, 52, 4], [90, 12, 52, 8], [90, 13, 52, 9, "listeners"], [90, 22, 52, 18], [90, 23, 52, 19, "for<PERSON>ach"], [90, 30, 52, 26], [90, 31, 52, 28, "listener"], [90, 39, 52, 36], [90, 43, 52, 41], [91, 10, 53, 6, "listener"], [91, 18, 53, 14], [91, 19, 53, 15, "isFocused"], [91, 28, 53, 24], [91, 29, 53, 25], [92, 8, 54, 4], [92, 9, 54, 5], [92, 10, 54, 6], [93, 6, 55, 2], [94, 4, 55, 3], [95, 6, 55, 3, "key"], [95, 9, 55, 3], [96, 6, 55, 3, "value"], [96, 11, 55, 3], [96, 13, 56, 2], [96, 22, 56, 2, "isFocused"], [96, 31, 56, 11, "isFocused"], [96, 32, 56, 11], [96, 34, 56, 14], [97, 8, 57, 4], [97, 12, 57, 8], [97, 23, 57, 8, "_classPrivateFieldLooseBase2"], [97, 51, 57, 8], [97, 52, 57, 8, "default"], [97, 59, 57, 8], [97, 61, 57, 15], [97, 65, 57, 19], [97, 67, 57, 19, "_focused"], [97, 75, 57, 19], [97, 77, 57, 19, "_focused"], [97, 85, 57, 19], [97, 86, 57, 28], [97, 91, 57, 33], [97, 100, 57, 42], [97, 102, 57, 44], [98, 10, 58, 6], [98, 21, 58, 6, "_classPrivateFieldLooseBase2"], [98, 49, 58, 6], [98, 50, 58, 6, "default"], [98, 57, 58, 6], [98, 59, 58, 13], [98, 63, 58, 17], [98, 65, 58, 17, "_focused"], [98, 73, 58, 17], [98, 75, 58, 17, "_focused"], [98, 83, 58, 17], [99, 8, 59, 4], [100, 8, 60, 4], [100, 15, 60, 11, "globalThis"], [100, 25, 60, 21], [100, 26, 60, 22, "document"], [100, 34, 60, 30], [100, 36, 60, 32, "visibilityState"], [100, 51, 60, 47], [100, 56, 60, 52], [100, 64, 60, 60], [101, 6, 61, 2], [102, 4, 61, 3], [103, 2, 61, 3], [103, 4, 4, 33, "Subscribable"], [103, 30, 4, 45], [103, 32, 62, 1], [104, 2, 63, 0], [104, 6, 63, 4, "focusManager"], [104, 18, 63, 16], [104, 21, 63, 16, "exports"], [104, 28, 63, 16], [104, 29, 63, 16, "focusManager"], [104, 41, 63, 16], [104, 44, 63, 19], [104, 48, 63, 23, "FocusManager"], [104, 60, 63, 35], [104, 61, 63, 36], [104, 62, 63, 37], [105, 0, 63, 38], [105, 3]], "functionMap": {"names": ["<global>", "FocusManager", "FocusManager#constructor", "<anonymous>", "listener", "FocusManager#onSubscribe", "FocusManager#onUnsubscribe", "FocusManager#setEventListener", "setup$argument_0", "FocusManager#setFocused", "FocusManager#onFocus", "listeners.forEach$argument_0", "FocusManager#isFocused"], "mappings": "AAA;mBCG;ECI;kBCE;yBCE,eD;KDO;GDC;EIC;GJI;EKC;GLK;EMC;0BCG;KDM;GNC;EQC;GRM;ESC;2BCE;KDE;GTC;EWC;GXK;CDC"}}, "type": "js/module"}]}