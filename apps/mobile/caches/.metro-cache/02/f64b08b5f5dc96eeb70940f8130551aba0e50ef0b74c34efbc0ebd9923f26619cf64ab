{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./Path", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 26, "index": 58}}], "key": "NGtBzyQh0z9iu/F5/LY5JJ2knZA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 59}, "end": {"line": 3, "column": 28, "index": 87}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractPolyPoints", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 161}, "end": {"line": 5, "column": 65, "index": 226}}], "key": "PTc5jlMXx8+cE16xlGs/tDiy0Us=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _Path = _interopRequireDefault(require(_dependencyMap[7], \"./Path\"));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8], \"./Shape\"));\n  var _extractPolyPoints = _interopRequireDefault(require(_dependencyMap[9], \"../lib/extract/extractPolyPoints\"));\n  var _jsxDevRuntime = require(_dependencyMap[10], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-svg/src/elements/Polygon.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Polygon = exports.default = /*#__PURE__*/function (_Shape) {\n    function Polygon() {\n      var _this;\n      (0, _classCallCheck2.default)(this, Polygon);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, Polygon, [...args]);\n      _this.setNativeProps = props => {\n        var points = props.points;\n        if (points) {\n          props.d = `M${(0, _extractPolyPoints.default)(points)}z`;\n        }\n        _this.root && _this.root.setNativeProps(props);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(Polygon, _Shape);\n    return (0, _createClass2.default)(Polygon, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var points = props.points;\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Path.default, {\n          ref: this.refMethod,\n          d: points && `M${(0, _extractPolyPoints.default)(points)}z`,\n          ...props\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_Shape2.default);\n  Polygon.displayName = 'Polygon';\n  Polygon.defaultProps = {\n    points: ''\n  };\n});", "lineCount": 60, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "React"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireWildcard"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_Path"], [13, 11, 2, 0], [13, 14, 2, 0, "_interopRequireDefault"], [13, 36, 2, 0], [13, 37, 2, 0, "require"], [13, 44, 2, 0], [13, 45, 2, 0, "_dependencyMap"], [13, 59, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_Shape2"], [14, 13, 3, 0], [14, 16, 3, 0, "_interopRequireDefault"], [14, 38, 3, 0], [14, 39, 3, 0, "require"], [14, 46, 3, 0], [14, 47, 3, 0, "_dependencyMap"], [14, 61, 3, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_extractPolyPoints"], [15, 24, 5, 0], [15, 27, 5, 0, "_interopRequireDefault"], [15, 49, 5, 0], [15, 50, 5, 0, "require"], [15, 57, 5, 0], [15, 58, 5, 0, "_dependencyMap"], [15, 72, 5, 0], [16, 2, 5, 65], [16, 6, 5, 65, "_jsxDevRuntime"], [16, 20, 5, 65], [16, 23, 5, 65, "require"], [16, 30, 5, 65], [16, 31, 5, 65, "_dependencyMap"], [16, 45, 5, 65], [17, 2, 5, 65], [17, 6, 5, 65, "_jsxFileName"], [17, 18, 5, 65], [18, 2, 5, 65], [18, 11, 5, 65, "_interopRequireWildcard"], [18, 35, 5, 65, "e"], [18, 36, 5, 65], [18, 38, 5, 65, "t"], [18, 39, 5, 65], [18, 68, 5, 65, "WeakMap"], [18, 75, 5, 65], [18, 81, 5, 65, "r"], [18, 82, 5, 65], [18, 89, 5, 65, "WeakMap"], [18, 96, 5, 65], [18, 100, 5, 65, "n"], [18, 101, 5, 65], [18, 108, 5, 65, "WeakMap"], [18, 115, 5, 65], [18, 127, 5, 65, "_interopRequireWildcard"], [18, 150, 5, 65], [18, 162, 5, 65, "_interopRequireWildcard"], [18, 163, 5, 65, "e"], [18, 164, 5, 65], [18, 166, 5, 65, "t"], [18, 167, 5, 65], [18, 176, 5, 65, "t"], [18, 177, 5, 65], [18, 181, 5, 65, "e"], [18, 182, 5, 65], [18, 186, 5, 65, "e"], [18, 187, 5, 65], [18, 188, 5, 65, "__esModule"], [18, 198, 5, 65], [18, 207, 5, 65, "e"], [18, 208, 5, 65], [18, 214, 5, 65, "o"], [18, 215, 5, 65], [18, 217, 5, 65, "i"], [18, 218, 5, 65], [18, 220, 5, 65, "f"], [18, 221, 5, 65], [18, 226, 5, 65, "__proto__"], [18, 235, 5, 65], [18, 243, 5, 65, "default"], [18, 250, 5, 65], [18, 252, 5, 65, "e"], [18, 253, 5, 65], [18, 270, 5, 65, "e"], [18, 271, 5, 65], [18, 294, 5, 65, "e"], [18, 295, 5, 65], [18, 320, 5, 65, "e"], [18, 321, 5, 65], [18, 330, 5, 65, "f"], [18, 331, 5, 65], [18, 337, 5, 65, "o"], [18, 338, 5, 65], [18, 341, 5, 65, "t"], [18, 342, 5, 65], [18, 345, 5, 65, "n"], [18, 346, 5, 65], [18, 349, 5, 65, "r"], [18, 350, 5, 65], [18, 358, 5, 65, "o"], [18, 359, 5, 65], [18, 360, 5, 65, "has"], [18, 363, 5, 65], [18, 364, 5, 65, "e"], [18, 365, 5, 65], [18, 375, 5, 65, "o"], [18, 376, 5, 65], [18, 377, 5, 65, "get"], [18, 380, 5, 65], [18, 381, 5, 65, "e"], [18, 382, 5, 65], [18, 385, 5, 65, "o"], [18, 386, 5, 65], [18, 387, 5, 65, "set"], [18, 390, 5, 65], [18, 391, 5, 65, "e"], [18, 392, 5, 65], [18, 394, 5, 65, "f"], [18, 395, 5, 65], [18, 409, 5, 65, "_t"], [18, 411, 5, 65], [18, 415, 5, 65, "e"], [18, 416, 5, 65], [18, 432, 5, 65, "_t"], [18, 434, 5, 65], [18, 441, 5, 65, "hasOwnProperty"], [18, 455, 5, 65], [18, 456, 5, 65, "call"], [18, 460, 5, 65], [18, 461, 5, 65, "e"], [18, 462, 5, 65], [18, 464, 5, 65, "_t"], [18, 466, 5, 65], [18, 473, 5, 65, "i"], [18, 474, 5, 65], [18, 478, 5, 65, "o"], [18, 479, 5, 65], [18, 482, 5, 65, "Object"], [18, 488, 5, 65], [18, 489, 5, 65, "defineProperty"], [18, 503, 5, 65], [18, 508, 5, 65, "Object"], [18, 514, 5, 65], [18, 515, 5, 65, "getOwnPropertyDescriptor"], [18, 539, 5, 65], [18, 540, 5, 65, "e"], [18, 541, 5, 65], [18, 543, 5, 65, "_t"], [18, 545, 5, 65], [18, 552, 5, 65, "i"], [18, 553, 5, 65], [18, 554, 5, 65, "get"], [18, 557, 5, 65], [18, 561, 5, 65, "i"], [18, 562, 5, 65], [18, 563, 5, 65, "set"], [18, 566, 5, 65], [18, 570, 5, 65, "o"], [18, 571, 5, 65], [18, 572, 5, 65, "f"], [18, 573, 5, 65], [18, 575, 5, 65, "_t"], [18, 577, 5, 65], [18, 579, 5, 65, "i"], [18, 580, 5, 65], [18, 584, 5, 65, "f"], [18, 585, 5, 65], [18, 586, 5, 65, "_t"], [18, 588, 5, 65], [18, 592, 5, 65, "e"], [18, 593, 5, 65], [18, 594, 5, 65, "_t"], [18, 596, 5, 65], [18, 607, 5, 65, "f"], [18, 608, 5, 65], [18, 613, 5, 65, "e"], [18, 614, 5, 65], [18, 616, 5, 65, "t"], [18, 617, 5, 65], [19, 2, 5, 65], [19, 11, 5, 65, "_callSuper"], [19, 22, 5, 65, "t"], [19, 23, 5, 65], [19, 25, 5, 65, "o"], [19, 26, 5, 65], [19, 28, 5, 65, "e"], [19, 29, 5, 65], [19, 40, 5, 65, "o"], [19, 41, 5, 65], [19, 48, 5, 65, "_getPrototypeOf2"], [19, 64, 5, 65], [19, 65, 5, 65, "default"], [19, 72, 5, 65], [19, 74, 5, 65, "o"], [19, 75, 5, 65], [19, 82, 5, 65, "_possibleConstructorReturn2"], [19, 109, 5, 65], [19, 110, 5, 65, "default"], [19, 117, 5, 65], [19, 119, 5, 65, "t"], [19, 120, 5, 65], [19, 122, 5, 65, "_isNativeReflectConstruct"], [19, 147, 5, 65], [19, 152, 5, 65, "Reflect"], [19, 159, 5, 65], [19, 160, 5, 65, "construct"], [19, 169, 5, 65], [19, 170, 5, 65, "o"], [19, 171, 5, 65], [19, 173, 5, 65, "e"], [19, 174, 5, 65], [19, 186, 5, 65, "_getPrototypeOf2"], [19, 202, 5, 65], [19, 203, 5, 65, "default"], [19, 210, 5, 65], [19, 212, 5, 65, "t"], [19, 213, 5, 65], [19, 215, 5, 65, "constructor"], [19, 226, 5, 65], [19, 230, 5, 65, "o"], [19, 231, 5, 65], [19, 232, 5, 65, "apply"], [19, 237, 5, 65], [19, 238, 5, 65, "t"], [19, 239, 5, 65], [19, 241, 5, 65, "e"], [19, 242, 5, 65], [20, 2, 5, 65], [20, 11, 5, 65, "_isNativeReflectConstruct"], [20, 37, 5, 65], [20, 51, 5, 65, "t"], [20, 52, 5, 65], [20, 56, 5, 65, "Boolean"], [20, 63, 5, 65], [20, 64, 5, 65, "prototype"], [20, 73, 5, 65], [20, 74, 5, 65, "valueOf"], [20, 81, 5, 65], [20, 82, 5, 65, "call"], [20, 86, 5, 65], [20, 87, 5, 65, "Reflect"], [20, 94, 5, 65], [20, 95, 5, 65, "construct"], [20, 104, 5, 65], [20, 105, 5, 65, "Boolean"], [20, 112, 5, 65], [20, 145, 5, 65, "t"], [20, 146, 5, 65], [20, 159, 5, 65, "_isNativeReflectConstruct"], [20, 184, 5, 65], [20, 196, 5, 65, "_isNativeReflectConstruct"], [20, 197, 5, 65], [20, 210, 5, 65, "t"], [20, 211, 5, 65], [21, 2, 5, 65], [21, 6, 12, 21, "Polygon"], [21, 13, 12, 28], [21, 16, 12, 28, "exports"], [21, 23, 12, 28], [21, 24, 12, 28, "default"], [21, 31, 12, 28], [21, 57, 12, 28, "_Shape"], [21, 63, 12, 28], [22, 4, 12, 28], [22, 13, 12, 28, "Polygon"], [22, 21, 12, 28], [23, 6, 12, 28], [23, 10, 12, 28, "_this"], [23, 15, 12, 28], [24, 6, 12, 28], [24, 10, 12, 28, "_classCallCheck2"], [24, 26, 12, 28], [24, 27, 12, 28, "default"], [24, 34, 12, 28], [24, 42, 12, 28, "Polygon"], [24, 49, 12, 28], [25, 6, 12, 28], [25, 15, 12, 28, "_len"], [25, 19, 12, 28], [25, 22, 12, 28, "arguments"], [25, 31, 12, 28], [25, 32, 12, 28, "length"], [25, 38, 12, 28], [25, 40, 12, 28, "args"], [25, 44, 12, 28], [25, 51, 12, 28, "Array"], [25, 56, 12, 28], [25, 57, 12, 28, "_len"], [25, 61, 12, 28], [25, 64, 12, 28, "_key"], [25, 68, 12, 28], [25, 74, 12, 28, "_key"], [25, 78, 12, 28], [25, 81, 12, 28, "_len"], [25, 85, 12, 28], [25, 87, 12, 28, "_key"], [25, 91, 12, 28], [26, 8, 12, 28, "args"], [26, 12, 12, 28], [26, 13, 12, 28, "_key"], [26, 17, 12, 28], [26, 21, 12, 28, "arguments"], [26, 30, 12, 28], [26, 31, 12, 28, "_key"], [26, 35, 12, 28], [27, 6, 12, 28], [28, 6, 12, 28, "_this"], [28, 11, 12, 28], [28, 14, 12, 28, "_callSuper"], [28, 24, 12, 28], [28, 31, 12, 28, "Polygon"], [28, 38, 12, 28], [28, 44, 12, 28, "args"], [28, 48, 12, 28], [29, 6, 12, 28, "_this"], [29, 11, 12, 28], [29, 12, 19, 2, "setNativeProps"], [29, 26, 19, 16], [29, 29, 20, 4, "props"], [29, 34, 22, 5], [29, 38, 23, 7], [30, 8, 24, 4], [30, 12, 24, 12, "points"], [30, 18, 24, 18], [30, 21, 24, 23, "props"], [30, 26, 24, 28], [30, 27, 24, 12, "points"], [30, 33, 24, 18], [31, 8, 25, 4], [31, 12, 25, 8, "points"], [31, 18, 25, 14], [31, 20, 25, 16], [32, 10, 26, 6, "props"], [32, 15, 26, 11], [32, 16, 26, 12, "d"], [32, 17, 26, 13], [32, 20, 26, 16], [32, 24, 26, 20], [32, 28, 26, 20, "extractPolyPoints"], [32, 54, 26, 37], [32, 56, 26, 38, "points"], [32, 62, 26, 44], [32, 63, 26, 45], [32, 66, 26, 48], [33, 8, 27, 4], [34, 8, 28, 4, "_this"], [34, 13, 28, 4], [34, 14, 28, 9, "root"], [34, 18, 28, 13], [34, 22, 28, 17, "_this"], [34, 27, 28, 17], [34, 28, 28, 22, "root"], [34, 32, 28, 26], [34, 33, 28, 27, "setNativeProps"], [34, 47, 28, 41], [34, 48, 28, 42, "props"], [34, 53, 28, 47], [34, 54, 28, 48], [35, 6, 29, 2], [35, 7, 29, 3], [36, 6, 29, 3], [36, 13, 29, 3, "_this"], [36, 18, 29, 3], [37, 4, 29, 3], [38, 4, 29, 3], [38, 8, 29, 3, "_inherits2"], [38, 18, 29, 3], [38, 19, 29, 3, "default"], [38, 26, 29, 3], [38, 28, 29, 3, "Polygon"], [38, 35, 29, 3], [38, 37, 29, 3, "_Shape"], [38, 43, 29, 3], [39, 4, 29, 3], [39, 15, 29, 3, "_createClass2"], [39, 28, 29, 3], [39, 29, 29, 3, "default"], [39, 36, 29, 3], [39, 38, 29, 3, "Polygon"], [39, 45, 29, 3], [40, 6, 29, 3, "key"], [40, 9, 29, 3], [41, 6, 29, 3, "value"], [41, 11, 29, 3], [41, 13, 31, 2], [41, 22, 31, 2, "render"], [41, 28, 31, 8, "render"], [41, 29, 31, 8], [41, 31, 31, 11], [42, 8, 32, 4], [42, 12, 32, 12, "props"], [42, 17, 32, 17], [42, 20, 32, 22], [42, 24, 32, 26], [42, 25, 32, 12, "props"], [42, 30, 32, 17], [43, 8, 33, 4], [43, 12, 33, 12, "points"], [43, 18, 33, 18], [43, 21, 33, 23, "props"], [43, 26, 33, 28], [43, 27, 33, 12, "points"], [43, 33, 33, 18], [44, 8, 34, 4], [44, 28, 35, 6], [44, 32, 35, 6, "_jsxDevRuntime"], [44, 46, 35, 6], [44, 47, 35, 6, "jsxDEV"], [44, 53, 35, 6], [44, 55, 35, 7, "_Path"], [44, 60, 35, 7], [44, 61, 35, 7, "default"], [44, 68, 35, 11], [45, 10, 36, 8, "ref"], [45, 13, 36, 11], [45, 15, 36, 13], [45, 19, 36, 17], [45, 20, 36, 18, "refMethod"], [45, 29, 36, 63], [46, 10, 37, 8, "d"], [46, 11, 37, 9], [46, 13, 37, 11, "points"], [46, 19, 37, 17], [46, 23, 37, 21], [46, 27, 37, 25], [46, 31, 37, 25, "extractPolyPoints"], [46, 57, 37, 42], [46, 59, 37, 43, "points"], [46, 65, 37, 49], [46, 66, 37, 50], [46, 69, 37, 54], [47, 10, 37, 54], [47, 13, 38, 12, "props"], [48, 8, 38, 17], [49, 10, 38, 17, "fileName"], [49, 18, 38, 17], [49, 20, 38, 17, "_jsxFileName"], [49, 32, 38, 17], [50, 10, 38, 17, "lineNumber"], [50, 20, 38, 17], [51, 10, 38, 17, "columnNumber"], [51, 22, 38, 17], [52, 8, 38, 17], [52, 15, 39, 7], [52, 16, 39, 8], [53, 6, 41, 2], [54, 4, 41, 3], [55, 2, 41, 3], [55, 4, 12, 37, "<PERSON><PERSON><PERSON>"], [55, 19, 12, 42], [56, 2, 12, 21, "Polygon"], [56, 9, 12, 28], [56, 10, 13, 9, "displayName"], [56, 21, 13, 20], [56, 24, 13, 23], [56, 33, 13, 32], [57, 2, 12, 21, "Polygon"], [57, 9, 12, 28], [57, 10, 15, 9, "defaultProps"], [57, 22, 15, 21], [57, 25, 15, 24], [58, 4, 16, 4, "points"], [58, 10, 16, 10], [58, 12, 16, 12], [59, 2, 17, 2], [59, 3, 17, 3], [60, 0, 17, 3], [60, 3]], "functionMap": {"names": ["<global>", "Polygon", "setNativeProps", "render"], "mappings": "AAA;eCW;mBCO;GDU;EEE;GFU;CDC"}}, "type": "js/module"}]}