{"dependencies": [{"name": "../PointerType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "hYk7sjMJT+Y27KGuBYSPOC2Jih8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RNSVGElements = exports.PointerTypeMapping = void 0;\n  exports.calculateViewScale = calculateViewScale;\n  exports.degToRad = exports.coneToDeviation = void 0;\n  exports.isPointerInBounds = isPointerInBounds;\n  exports.isRNSVGElement = isRNSVGElement;\n  exports.isRNSVGNode = isRNSVGNode;\n  exports.tryExtractStylusData = tryExtractStylusData;\n  var _PointerType = require(_dependencyMap[0], \"../PointerType\");\n  function isPointerInBounds(view, {\n    x,\n    y\n  }) {\n    const rect = view.getBoundingClientRect();\n    return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;\n  }\n  const PointerTypeMapping = exports.PointerTypeMapping = new Map([['mouse', _PointerType.PointerType.MOUSE], ['touch', _PointerType.PointerType.TOUCH], ['pen', _PointerType.PointerType.STYLUS], ['none', _PointerType.PointerType.OTHER]]);\n  const degToRad = degrees => degrees * Math.PI / 180;\n  exports.degToRad = degToRad;\n  const coneToDeviation = degrees => Math.cos(degToRad(degrees / 2));\n  exports.coneToDeviation = coneToDeviation;\n  function calculateViewScale(view) {\n    var _RegExp$exec;\n    const styles = getComputedStyle(view);\n    const resultScales = {\n      scaleX: 1,\n      scaleY: 1\n    }; // Get scales from scale property\n\n    if (styles.scale !== undefined && styles.scale !== 'none') {\n      const scales = styles.scale.split(' ');\n      if (scales[0]) {\n        resultScales.scaleX = parseFloat(scales[0]);\n      }\n      resultScales.scaleY = scales[1] ? parseFloat(scales[1]) : parseFloat(scales[0]);\n    } // Get scales from transform property\n\n    const matrixElements = (_RegExp$exec = new RegExp(/matrix\\((.+)\\)/).exec(styles.transform)) === null || _RegExp$exec === void 0 ? void 0 : _RegExp$exec[1];\n    if (matrixElements) {\n      const matrixElementsArray = matrixElements.split(', ');\n      resultScales.scaleX *= parseFloat(matrixElementsArray[0]);\n      resultScales.scaleY *= parseFloat(matrixElementsArray[3]);\n    }\n    return resultScales;\n  }\n  function tryExtractStylusData(event) {\n    const pointerType = PointerTypeMapping.get(event.pointerType);\n    if (pointerType !== _PointerType.PointerType.STYLUS) {\n      return;\n    } // @ts-ignore This property exists (https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent#instance_properties)\n\n    const eventAzimuthAngle = event.azimuthAngle; // @ts-ignore This property exists (https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent#instance_properties)\n\n    const eventAltitudeAngle = event.altitudeAngle;\n    if (event.tiltX === 0 && event.tiltY === 0) {\n      // If we are in this branch, it means that either tilt properties are not supported and we have to calculate them from altitude and azimuth angles,\n      // or stylus is perpendicular to the screen and we can use altitude / azimuth instead of tilt\n      // If azimuth and altitude are undefined in this branch, it means that we are either perpendicular to the screen,\n      // or that none of the position sets is supported. In that case, we can treat stylus as perpendicular\n      if (eventAzimuthAngle === undefined || eventAltitudeAngle === undefined) {\n        return {\n          tiltX: 0,\n          tiltY: 0,\n          azimuthAngle: Math.PI / 2,\n          altitudeAngle: Math.PI / 2,\n          pressure: event.pressure\n        };\n      }\n      const {\n        tiltX,\n        tiltY\n      } = spherical2tilt(eventAltitudeAngle, eventAzimuthAngle);\n      return {\n        tiltX,\n        tiltY,\n        azimuthAngle: eventAzimuthAngle,\n        altitudeAngle: eventAltitudeAngle,\n        pressure: event.pressure\n      };\n    }\n    const {\n      altitudeAngle,\n      azimuthAngle\n    } = tilt2spherical(event.tiltX, event.tiltY);\n    return {\n      tiltX: event.tiltX,\n      tiltY: event.tiltY,\n      azimuthAngle,\n      altitudeAngle,\n      pressure: event.pressure\n    };\n  } // `altitudeAngle` and `azimuthAngle` are experimental properties, which are not supported on Firefox and Safari.\n  // Given that, we use `tilt` properties and algorithm that converts one value to another.\n  //\n  // Source: https://w3c.github.io/pointerevents/#converting-between-tiltx-tilty-and-altitudeangle-azimuthangle\n\n  function tilt2spherical(tiltX, tiltY) {\n    const tiltXrad = tiltX * Math.PI / 180;\n    const tiltYrad = tiltY * Math.PI / 180; // calculate azimuth angle\n\n    let azimuthAngle = 0;\n    if (tiltX === 0) {\n      if (tiltY > 0) {\n        azimuthAngle = Math.PI / 2;\n      } else if (tiltY < 0) {\n        azimuthAngle = 3 * Math.PI / 2;\n      }\n    } else if (tiltY === 0) {\n      if (tiltX < 0) {\n        azimuthAngle = Math.PI;\n      }\n    } else if (Math.abs(tiltX) === 90 || Math.abs(tiltY) === 90) {\n      // not enough information to calculate azimuth\n      azimuthAngle = 0;\n    } else {\n      // Non-boundary case: neither tiltX nor tiltY is equal to 0 or +-90\n      const tanX = Math.tan(tiltXrad);\n      const tanY = Math.tan(tiltYrad);\n      azimuthAngle = Math.atan2(tanY, tanX);\n      if (azimuthAngle < 0) {\n        azimuthAngle += 2 * Math.PI;\n      }\n    } // calculate altitude angle\n\n    let altitudeAngle = 0;\n    if (Math.abs(tiltX) === 90 || Math.abs(tiltY) === 90) {\n      altitudeAngle = 0;\n    } else if (tiltX === 0) {\n      altitudeAngle = Math.PI / 2 - Math.abs(tiltYrad);\n    } else if (tiltY === 0) {\n      altitudeAngle = Math.PI / 2 - Math.abs(tiltXrad);\n    } else {\n      // Non-boundary case: neither tiltX nor tiltY is equal to 0 or +-90\n      altitudeAngle = Math.atan(1.0 / Math.sqrt(Math.pow(Math.tan(tiltXrad), 2) + Math.pow(Math.tan(tiltYrad), 2)));\n    }\n    return {\n      altitudeAngle: altitudeAngle,\n      azimuthAngle: azimuthAngle\n    };\n  } // If we are on a platform that doesn't support `tiltX` and `tiltY`, we have to calculate them from `altitude` and `azimuth` angles.\n  //\n  // Source: https://w3c.github.io/pointerevents/#converting-between-tiltx-tilty-and-altitudeangle-azimuthangle\n\n  function spherical2tilt(altitudeAngle, azimuthAngle) {\n    const radToDeg = 180 / Math.PI;\n    let tiltXrad = 0;\n    let tiltYrad = 0;\n    if (altitudeAngle === 0) {\n      // the pen is in the X-Y plane\n      if (azimuthAngle === 0 || azimuthAngle === 2 * Math.PI) {\n        // pen is on positive X axis\n        tiltXrad = Math.PI / 2;\n      }\n      if (azimuthAngle === Math.PI / 2) {\n        // pen is on positive Y axis\n        tiltYrad = Math.PI / 2;\n      }\n      if (azimuthAngle === Math.PI) {\n        // pen is on negative X axis\n        tiltXrad = -Math.PI / 2;\n      }\n      if (azimuthAngle === 3 * Math.PI / 2) {\n        // pen is on negative Y axis\n        tiltYrad = -Math.PI / 2;\n      }\n      if (azimuthAngle > 0 && azimuthAngle < Math.PI / 2) {\n        tiltXrad = Math.PI / 2;\n        tiltYrad = Math.PI / 2;\n      }\n      if (azimuthAngle > Math.PI / 2 && azimuthAngle < Math.PI) {\n        tiltXrad = -Math.PI / 2;\n        tiltYrad = Math.PI / 2;\n      }\n      if (azimuthAngle > Math.PI && azimuthAngle < 3 * Math.PI / 2) {\n        tiltXrad = -Math.PI / 2;\n        tiltYrad = -Math.PI / 2;\n      }\n      if (azimuthAngle > 3 * Math.PI / 2 && azimuthAngle < 2 * Math.PI) {\n        tiltXrad = Math.PI / 2;\n        tiltYrad = -Math.PI / 2;\n      }\n    }\n    if (altitudeAngle !== 0) {\n      const tanAlt = Math.tan(altitudeAngle);\n      tiltXrad = Math.atan(Math.cos(azimuthAngle) / tanAlt);\n      tiltYrad = Math.atan(Math.sin(azimuthAngle) / tanAlt);\n    }\n    const tiltX = Math.round(tiltXrad * radToDeg);\n    const tiltY = Math.round(tiltYrad * radToDeg);\n    return {\n      tiltX,\n      tiltY\n    };\n  }\n  const RNSVGElements = exports.RNSVGElements = new Set(['Circle', 'ClipPath', 'Ellipse', 'ForeignObject', 'G', 'Image', 'Line', 'Marker', 'Mask', 'Path', 'Pattern', 'Polygon', 'Polyline', 'Rect', 'Svg', 'Symbol', 'TSpan', 'Text', 'TextPath', 'Use']); // This function helps us determine whether given node is SVGElement or not. In our implementation of\n  // findNodeHandle, we can encounter such element in 2 forms - SVG tag or ref to SVG Element. Since Gesture Handler\n  // does not depend on SVG, we use our simplified SVGRef type that has `elementRef` field. This is something that is present\n  // in actual SVG ref object.\n  //\n  // In order to make sure that node passed into this function is in fact SVG element, first we check if its constructor name\n  // corresponds to one of the possible SVG elements. Then we also check if `elementRef` field exists.\n  // By doing both steps we decrease probability of detecting situations where, for example, user makes custom `Circle` and\n  // we treat it as SVG.\n\n  function isRNSVGElement(viewRef) {\n    const componentClassName = Object.getPrototypeOf(viewRef).constructor.name;\n    return RNSVGElements.has(componentClassName) && Object.hasOwn(viewRef, 'elementRef');\n  } // This function checks if given node is SVGElement. Unlike the function above, this one\n  // operates on React Nodes, not DOM nodes.\n  //\n  // Second condition was introduced to handle case where SVG element was wrapped with\n  // `createAnimatedComponent` from Reanimated.\n\n  function isRNSVGNode(node) {\n    var _node$ref, _Object$getPrototypeO, _node$type;\n\n    // If `ref` has `rngh` field, it means that component comes from Gesture Handler. This is a special case for\n    // `Text` component, which is present in `RNSVGElements` set, yet we don't want to treat it as SVG.\n    if ((_node$ref = node.ref) !== null && _node$ref !== void 0 && _node$ref.rngh) {\n      return false;\n    }\n    return ((_Object$getPrototypeO = Object.getPrototypeOf(node === null || node === void 0 ? void 0 : node.type)) === null || _Object$getPrototypeO === void 0 ? void 0 : _Object$getPrototypeO.name) === 'WebShape' || RNSVGElements.has(node === null || node === void 0 ? void 0 : (_node$type = node.type) === null || _node$type === void 0 ? void 0 : _node$type.displayName);\n  }\n});", "lineCount": 227, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_PointerType"], [12, 18, 1, 0], [12, 21, 1, 0, "require"], [12, 28, 1, 0], [12, 29, 1, 0, "_dependencyMap"], [12, 43, 1, 0], [13, 2, 2, 7], [13, 11, 2, 16, "isPointerInBounds"], [13, 28, 2, 33, "isPointerInBounds"], [13, 29, 2, 34, "view"], [13, 33, 2, 38], [13, 35, 2, 40], [14, 4, 3, 2, "x"], [14, 5, 3, 3], [15, 4, 4, 2, "y"], [16, 2, 5, 0], [16, 3, 5, 1], [16, 5, 5, 3], [17, 4, 6, 2], [17, 10, 6, 8, "rect"], [17, 14, 6, 12], [17, 17, 6, 15, "view"], [17, 21, 6, 19], [17, 22, 6, 20, "getBoundingClientRect"], [17, 43, 6, 41], [17, 44, 6, 42], [17, 45, 6, 43], [18, 4, 7, 2], [18, 11, 7, 9, "x"], [18, 12, 7, 10], [18, 16, 7, 14, "rect"], [18, 20, 7, 18], [18, 21, 7, 19, "left"], [18, 25, 7, 23], [18, 29, 7, 27, "x"], [18, 30, 7, 28], [18, 34, 7, 32, "rect"], [18, 38, 7, 36], [18, 39, 7, 37, "right"], [18, 44, 7, 42], [18, 48, 7, 46, "y"], [18, 49, 7, 47], [18, 53, 7, 51, "rect"], [18, 57, 7, 55], [18, 58, 7, 56, "top"], [18, 61, 7, 59], [18, 65, 7, 63, "y"], [18, 66, 7, 64], [18, 70, 7, 68, "rect"], [18, 74, 7, 72], [18, 75, 7, 73, "bottom"], [18, 81, 7, 79], [19, 2, 8, 0], [20, 2, 9, 7], [20, 8, 9, 13, "PointerTypeMapping"], [20, 26, 9, 31], [20, 29, 9, 31, "exports"], [20, 36, 9, 31], [20, 37, 9, 31, "PointerTypeMapping"], [20, 55, 9, 31], [20, 58, 9, 34], [20, 62, 9, 38, "Map"], [20, 65, 9, 41], [20, 66, 9, 42], [20, 67, 9, 43], [20, 68, 9, 44], [20, 75, 9, 51], [20, 77, 9, 53, "PointerType"], [20, 101, 9, 64], [20, 102, 9, 65, "MOUSE"], [20, 107, 9, 70], [20, 108, 9, 71], [20, 110, 9, 73], [20, 111, 9, 74], [20, 118, 9, 81], [20, 120, 9, 83, "PointerType"], [20, 144, 9, 94], [20, 145, 9, 95, "TOUCH"], [20, 150, 9, 100], [20, 151, 9, 101], [20, 153, 9, 103], [20, 154, 9, 104], [20, 159, 9, 109], [20, 161, 9, 111, "PointerType"], [20, 185, 9, 122], [20, 186, 9, 123, "STYLUS"], [20, 192, 9, 129], [20, 193, 9, 130], [20, 195, 9, 132], [20, 196, 9, 133], [20, 202, 9, 139], [20, 204, 9, 141, "PointerType"], [20, 228, 9, 152], [20, 229, 9, 153, "OTHER"], [20, 234, 9, 158], [20, 235, 9, 159], [20, 236, 9, 160], [20, 237, 9, 161], [21, 2, 10, 7], [21, 8, 10, 13, "degToRad"], [21, 16, 10, 21], [21, 19, 10, 24, "degrees"], [21, 26, 10, 31], [21, 30, 10, 35, "degrees"], [21, 37, 10, 42], [21, 40, 10, 45, "Math"], [21, 44, 10, 49], [21, 45, 10, 50, "PI"], [21, 47, 10, 52], [21, 50, 10, 55], [21, 53, 10, 58], [22, 2, 10, 59, "exports"], [22, 9, 10, 59], [22, 10, 10, 59, "degToRad"], [22, 18, 10, 59], [22, 21, 10, 59, "degToRad"], [22, 29, 10, 59], [23, 2, 11, 7], [23, 8, 11, 13, "coneToDeviation"], [23, 23, 11, 28], [23, 26, 11, 31, "degrees"], [23, 33, 11, 38], [23, 37, 11, 42, "Math"], [23, 41, 11, 46], [23, 42, 11, 47, "cos"], [23, 45, 11, 50], [23, 46, 11, 51, "degToRad"], [23, 54, 11, 59], [23, 55, 11, 60, "degrees"], [23, 62, 11, 67], [23, 65, 11, 70], [23, 66, 11, 71], [23, 67, 11, 72], [23, 68, 11, 73], [24, 2, 11, 74, "exports"], [24, 9, 11, 74], [24, 10, 11, 74, "coneToDeviation"], [24, 25, 11, 74], [24, 28, 11, 74, "coneToDeviation"], [24, 43, 11, 74], [25, 2, 12, 7], [25, 11, 12, 16, "calculateViewScale"], [25, 29, 12, 34, "calculateViewScale"], [25, 30, 12, 35, "view"], [25, 34, 12, 39], [25, 36, 12, 41], [26, 4, 13, 2], [26, 8, 13, 6, "_RegExp$exec"], [26, 20, 13, 18], [27, 4, 15, 2], [27, 10, 15, 8, "styles"], [27, 16, 15, 14], [27, 19, 15, 17, "getComputedStyle"], [27, 35, 15, 33], [27, 36, 15, 34, "view"], [27, 40, 15, 38], [27, 41, 15, 39], [28, 4, 16, 2], [28, 10, 16, 8, "resultScales"], [28, 22, 16, 20], [28, 25, 16, 23], [29, 6, 17, 4, "scaleX"], [29, 12, 17, 10], [29, 14, 17, 12], [29, 15, 17, 13], [30, 6, 18, 4, "scaleY"], [30, 12, 18, 10], [30, 14, 18, 12], [31, 4, 19, 2], [31, 5, 19, 3], [31, 6, 19, 4], [31, 7, 19, 5], [33, 4, 21, 2], [33, 8, 21, 6, "styles"], [33, 14, 21, 12], [33, 15, 21, 13, "scale"], [33, 20, 21, 18], [33, 25, 21, 23, "undefined"], [33, 34, 21, 32], [33, 38, 21, 36, "styles"], [33, 44, 21, 42], [33, 45, 21, 43, "scale"], [33, 50, 21, 48], [33, 55, 21, 53], [33, 61, 21, 59], [33, 63, 21, 61], [34, 6, 22, 4], [34, 12, 22, 10, "scales"], [34, 18, 22, 16], [34, 21, 22, 19, "styles"], [34, 27, 22, 25], [34, 28, 22, 26, "scale"], [34, 33, 22, 31], [34, 34, 22, 32, "split"], [34, 39, 22, 37], [34, 40, 22, 38], [34, 43, 22, 41], [34, 44, 22, 42], [35, 6, 24, 4], [35, 10, 24, 8, "scales"], [35, 16, 24, 14], [35, 17, 24, 15], [35, 18, 24, 16], [35, 19, 24, 17], [35, 21, 24, 19], [36, 8, 25, 6, "resultScales"], [36, 20, 25, 18], [36, 21, 25, 19, "scaleX"], [36, 27, 25, 25], [36, 30, 25, 28, "parseFloat"], [36, 40, 25, 38], [36, 41, 25, 39, "scales"], [36, 47, 25, 45], [36, 48, 25, 46], [36, 49, 25, 47], [36, 50, 25, 48], [36, 51, 25, 49], [37, 6, 26, 4], [38, 6, 28, 4, "resultScales"], [38, 18, 28, 16], [38, 19, 28, 17, "scaleY"], [38, 25, 28, 23], [38, 28, 28, 26, "scales"], [38, 34, 28, 32], [38, 35, 28, 33], [38, 36, 28, 34], [38, 37, 28, 35], [38, 40, 28, 38, "parseFloat"], [38, 50, 28, 48], [38, 51, 28, 49, "scales"], [38, 57, 28, 55], [38, 58, 28, 56], [38, 59, 28, 57], [38, 60, 28, 58], [38, 61, 28, 59], [38, 64, 28, 62, "parseFloat"], [38, 74, 28, 72], [38, 75, 28, 73, "scales"], [38, 81, 28, 79], [38, 82, 28, 80], [38, 83, 28, 81], [38, 84, 28, 82], [38, 85, 28, 83], [39, 4, 29, 2], [39, 5, 29, 3], [39, 6, 29, 4], [41, 4, 32, 2], [41, 10, 32, 8, "matrixElements"], [41, 24, 32, 22], [41, 27, 32, 25], [41, 28, 32, 26, "_RegExp$exec"], [41, 40, 32, 38], [41, 43, 32, 41], [41, 47, 32, 45, "RegExp"], [41, 53, 32, 51], [41, 54, 32, 52], [41, 70, 32, 68], [41, 71, 32, 69], [41, 72, 32, 70, "exec"], [41, 76, 32, 74], [41, 77, 32, 75, "styles"], [41, 83, 32, 81], [41, 84, 32, 82, "transform"], [41, 93, 32, 91], [41, 94, 32, 92], [41, 100, 32, 98], [41, 104, 32, 102], [41, 108, 32, 106, "_RegExp$exec"], [41, 120, 32, 118], [41, 125, 32, 123], [41, 130, 32, 128], [41, 131, 32, 129], [41, 134, 32, 132], [41, 139, 32, 137], [41, 140, 32, 138], [41, 143, 32, 141, "_RegExp$exec"], [41, 155, 32, 153], [41, 156, 32, 154], [41, 157, 32, 155], [41, 158, 32, 156], [42, 4, 34, 2], [42, 8, 34, 6, "matrixElements"], [42, 22, 34, 20], [42, 24, 34, 22], [43, 6, 35, 4], [43, 12, 35, 10, "matrixElementsArray"], [43, 31, 35, 29], [43, 34, 35, 32, "matrixElements"], [43, 48, 35, 46], [43, 49, 35, 47, "split"], [43, 54, 35, 52], [43, 55, 35, 53], [43, 59, 35, 57], [43, 60, 35, 58], [44, 6, 36, 4, "resultScales"], [44, 18, 36, 16], [44, 19, 36, 17, "scaleX"], [44, 25, 36, 23], [44, 29, 36, 27, "parseFloat"], [44, 39, 36, 37], [44, 40, 36, 38, "matrixElementsArray"], [44, 59, 36, 57], [44, 60, 36, 58], [44, 61, 36, 59], [44, 62, 36, 60], [44, 63, 36, 61], [45, 6, 37, 4, "resultScales"], [45, 18, 37, 16], [45, 19, 37, 17, "scaleY"], [45, 25, 37, 23], [45, 29, 37, 27, "parseFloat"], [45, 39, 37, 37], [45, 40, 37, 38, "matrixElementsArray"], [45, 59, 37, 57], [45, 60, 37, 58], [45, 61, 37, 59], [45, 62, 37, 60], [45, 63, 37, 61], [46, 4, 38, 2], [47, 4, 40, 2], [47, 11, 40, 9, "resultScales"], [47, 23, 40, 21], [48, 2, 41, 0], [49, 2, 42, 7], [49, 11, 42, 16, "tryExtractStylusData"], [49, 31, 42, 36, "tryExtractStylusData"], [49, 32, 42, 37, "event"], [49, 37, 42, 42], [49, 39, 42, 44], [50, 4, 43, 2], [50, 10, 43, 8, "pointerType"], [50, 21, 43, 19], [50, 24, 43, 22, "PointerTypeMapping"], [50, 42, 43, 40], [50, 43, 43, 41, "get"], [50, 46, 43, 44], [50, 47, 43, 45, "event"], [50, 52, 43, 50], [50, 53, 43, 51, "pointerType"], [50, 64, 43, 62], [50, 65, 43, 63], [51, 4, 45, 2], [51, 8, 45, 6, "pointerType"], [51, 19, 45, 17], [51, 24, 45, 22, "PointerType"], [51, 48, 45, 33], [51, 49, 45, 34, "STYLUS"], [51, 55, 45, 40], [51, 57, 45, 42], [52, 6, 46, 4], [53, 4, 47, 2], [53, 5, 47, 3], [53, 6, 47, 4], [55, 4, 50, 2], [55, 10, 50, 8, "eventAzimuthAngle"], [55, 27, 50, 25], [55, 30, 50, 28, "event"], [55, 35, 50, 33], [55, 36, 50, 34, "azimuthAngle"], [55, 48, 50, 46], [55, 49, 50, 47], [55, 50, 50, 48], [57, 4, 52, 2], [57, 10, 52, 8, "eventAltitudeAngle"], [57, 28, 52, 26], [57, 31, 52, 29, "event"], [57, 36, 52, 34], [57, 37, 52, 35, "altitudeAngle"], [57, 50, 52, 48], [58, 4, 54, 2], [58, 8, 54, 6, "event"], [58, 13, 54, 11], [58, 14, 54, 12, "tiltX"], [58, 19, 54, 17], [58, 24, 54, 22], [58, 25, 54, 23], [58, 29, 54, 27, "event"], [58, 34, 54, 32], [58, 35, 54, 33, "tiltY"], [58, 40, 54, 38], [58, 45, 54, 43], [58, 46, 54, 44], [58, 48, 54, 46], [59, 6, 55, 4], [60, 6, 56, 4], [61, 6, 57, 4], [62, 6, 58, 4], [63, 6, 59, 4], [63, 10, 59, 8, "eventAzimuthAngle"], [63, 27, 59, 25], [63, 32, 59, 30, "undefined"], [63, 41, 59, 39], [63, 45, 59, 43, "eventAltitudeAngle"], [63, 63, 59, 61], [63, 68, 59, 66, "undefined"], [63, 77, 59, 75], [63, 79, 59, 77], [64, 8, 60, 6], [64, 15, 60, 13], [65, 10, 61, 8, "tiltX"], [65, 15, 61, 13], [65, 17, 61, 15], [65, 18, 61, 16], [66, 10, 62, 8, "tiltY"], [66, 15, 62, 13], [66, 17, 62, 15], [66, 18, 62, 16], [67, 10, 63, 8, "azimuthAngle"], [67, 22, 63, 20], [67, 24, 63, 22, "Math"], [67, 28, 63, 26], [67, 29, 63, 27, "PI"], [67, 31, 63, 29], [67, 34, 63, 32], [67, 35, 63, 33], [68, 10, 64, 8, "altitudeAngle"], [68, 23, 64, 21], [68, 25, 64, 23, "Math"], [68, 29, 64, 27], [68, 30, 64, 28, "PI"], [68, 32, 64, 30], [68, 35, 64, 33], [68, 36, 64, 34], [69, 10, 65, 8, "pressure"], [69, 18, 65, 16], [69, 20, 65, 18, "event"], [69, 25, 65, 23], [69, 26, 65, 24, "pressure"], [70, 8, 66, 6], [70, 9, 66, 7], [71, 6, 67, 4], [72, 6, 69, 4], [72, 12, 69, 10], [73, 8, 70, 6, "tiltX"], [73, 13, 70, 11], [74, 8, 71, 6, "tiltY"], [75, 6, 72, 4], [75, 7, 72, 5], [75, 10, 72, 8, "spherical2tilt"], [75, 24, 72, 22], [75, 25, 72, 23, "eventAltitudeAngle"], [75, 43, 72, 41], [75, 45, 72, 43, "eventAzimuthAngle"], [75, 62, 72, 60], [75, 63, 72, 61], [76, 6, 73, 4], [76, 13, 73, 11], [77, 8, 74, 6, "tiltX"], [77, 13, 74, 11], [78, 8, 75, 6, "tiltY"], [78, 13, 75, 11], [79, 8, 76, 6, "azimuthAngle"], [79, 20, 76, 18], [79, 22, 76, 20, "eventAzimuthAngle"], [79, 39, 76, 37], [80, 8, 77, 6, "altitudeAngle"], [80, 21, 77, 19], [80, 23, 77, 21, "eventAltitudeAngle"], [80, 41, 77, 39], [81, 8, 78, 6, "pressure"], [81, 16, 78, 14], [81, 18, 78, 16, "event"], [81, 23, 78, 21], [81, 24, 78, 22, "pressure"], [82, 6, 79, 4], [82, 7, 79, 5], [83, 4, 80, 2], [84, 4, 82, 2], [84, 10, 82, 8], [85, 6, 83, 4, "altitudeAngle"], [85, 19, 83, 17], [86, 6, 84, 4, "azimuthAngle"], [87, 4, 85, 2], [87, 5, 85, 3], [87, 8, 85, 6, "tilt2spherical"], [87, 22, 85, 20], [87, 23, 85, 21, "event"], [87, 28, 85, 26], [87, 29, 85, 27, "tiltX"], [87, 34, 85, 32], [87, 36, 85, 34, "event"], [87, 41, 85, 39], [87, 42, 85, 40, "tiltY"], [87, 47, 85, 45], [87, 48, 85, 46], [88, 4, 86, 2], [88, 11, 86, 9], [89, 6, 87, 4, "tiltX"], [89, 11, 87, 9], [89, 13, 87, 11, "event"], [89, 18, 87, 16], [89, 19, 87, 17, "tiltX"], [89, 24, 87, 22], [90, 6, 88, 4, "tiltY"], [90, 11, 88, 9], [90, 13, 88, 11, "event"], [90, 18, 88, 16], [90, 19, 88, 17, "tiltY"], [90, 24, 88, 22], [91, 6, 89, 4, "azimuthAngle"], [91, 18, 89, 16], [92, 6, 90, 4, "altitudeAngle"], [92, 19, 90, 17], [93, 6, 91, 4, "pressure"], [93, 14, 91, 12], [93, 16, 91, 14, "event"], [93, 21, 91, 19], [93, 22, 91, 20, "pressure"], [94, 4, 92, 2], [94, 5, 92, 3], [95, 2, 93, 0], [95, 3, 93, 1], [95, 4, 93, 2], [96, 2, 94, 0], [97, 2, 95, 0], [98, 2, 96, 0], [100, 2, 98, 0], [100, 11, 98, 9, "tilt2spherical"], [100, 25, 98, 23, "tilt2spherical"], [100, 26, 98, 24, "tiltX"], [100, 31, 98, 29], [100, 33, 98, 31, "tiltY"], [100, 38, 98, 36], [100, 40, 98, 38], [101, 4, 99, 2], [101, 10, 99, 8, "tiltXrad"], [101, 18, 99, 16], [101, 21, 99, 19, "tiltX"], [101, 26, 99, 24], [101, 29, 99, 27, "Math"], [101, 33, 99, 31], [101, 34, 99, 32, "PI"], [101, 36, 99, 34], [101, 39, 99, 37], [101, 42, 99, 40], [102, 4, 100, 2], [102, 10, 100, 8, "tiltYrad"], [102, 18, 100, 16], [102, 21, 100, 19, "tiltY"], [102, 26, 100, 24], [102, 29, 100, 27, "Math"], [102, 33, 100, 31], [102, 34, 100, 32, "PI"], [102, 36, 100, 34], [102, 39, 100, 37], [102, 42, 100, 40], [102, 43, 100, 41], [102, 44, 100, 42], [104, 4, 102, 2], [104, 8, 102, 6, "azimuthAngle"], [104, 20, 102, 18], [104, 23, 102, 21], [104, 24, 102, 22], [105, 4, 104, 2], [105, 8, 104, 6, "tiltX"], [105, 13, 104, 11], [105, 18, 104, 16], [105, 19, 104, 17], [105, 21, 104, 19], [106, 6, 105, 4], [106, 10, 105, 8, "tiltY"], [106, 15, 105, 13], [106, 18, 105, 16], [106, 19, 105, 17], [106, 21, 105, 19], [107, 8, 106, 6, "azimuthAngle"], [107, 20, 106, 18], [107, 23, 106, 21, "Math"], [107, 27, 106, 25], [107, 28, 106, 26, "PI"], [107, 30, 106, 28], [107, 33, 106, 31], [107, 34, 106, 32], [108, 6, 107, 4], [108, 7, 107, 5], [108, 13, 107, 11], [108, 17, 107, 15, "tiltY"], [108, 22, 107, 20], [108, 25, 107, 23], [108, 26, 107, 24], [108, 28, 107, 26], [109, 8, 108, 6, "azimuthAngle"], [109, 20, 108, 18], [109, 23, 108, 21], [109, 24, 108, 22], [109, 27, 108, 25, "Math"], [109, 31, 108, 29], [109, 32, 108, 30, "PI"], [109, 34, 108, 32], [109, 37, 108, 35], [109, 38, 108, 36], [110, 6, 109, 4], [111, 4, 110, 2], [111, 5, 110, 3], [111, 11, 110, 9], [111, 15, 110, 13, "tiltY"], [111, 20, 110, 18], [111, 25, 110, 23], [111, 26, 110, 24], [111, 28, 110, 26], [112, 6, 111, 4], [112, 10, 111, 8, "tiltX"], [112, 15, 111, 13], [112, 18, 111, 16], [112, 19, 111, 17], [112, 21, 111, 19], [113, 8, 112, 6, "azimuthAngle"], [113, 20, 112, 18], [113, 23, 112, 21, "Math"], [113, 27, 112, 25], [113, 28, 112, 26, "PI"], [113, 30, 112, 28], [114, 6, 113, 4], [115, 4, 114, 2], [115, 5, 114, 3], [115, 11, 114, 9], [115, 15, 114, 13, "Math"], [115, 19, 114, 17], [115, 20, 114, 18, "abs"], [115, 23, 114, 21], [115, 24, 114, 22, "tiltX"], [115, 29, 114, 27], [115, 30, 114, 28], [115, 35, 114, 33], [115, 37, 114, 35], [115, 41, 114, 39, "Math"], [115, 45, 114, 43], [115, 46, 114, 44, "abs"], [115, 49, 114, 47], [115, 50, 114, 48, "tiltY"], [115, 55, 114, 53], [115, 56, 114, 54], [115, 61, 114, 59], [115, 63, 114, 61], [115, 65, 114, 63], [116, 6, 115, 4], [117, 6, 116, 4, "azimuthAngle"], [117, 18, 116, 16], [117, 21, 116, 19], [117, 22, 116, 20], [118, 4, 117, 2], [118, 5, 117, 3], [118, 11, 117, 9], [119, 6, 118, 4], [120, 6, 119, 4], [120, 12, 119, 10, "tanX"], [120, 16, 119, 14], [120, 19, 119, 17, "Math"], [120, 23, 119, 21], [120, 24, 119, 22, "tan"], [120, 27, 119, 25], [120, 28, 119, 26, "tiltXrad"], [120, 36, 119, 34], [120, 37, 119, 35], [121, 6, 120, 4], [121, 12, 120, 10, "tanY"], [121, 16, 120, 14], [121, 19, 120, 17, "Math"], [121, 23, 120, 21], [121, 24, 120, 22, "tan"], [121, 27, 120, 25], [121, 28, 120, 26, "tiltYrad"], [121, 36, 120, 34], [121, 37, 120, 35], [122, 6, 121, 4, "azimuthAngle"], [122, 18, 121, 16], [122, 21, 121, 19, "Math"], [122, 25, 121, 23], [122, 26, 121, 24, "atan2"], [122, 31, 121, 29], [122, 32, 121, 30, "tanY"], [122, 36, 121, 34], [122, 38, 121, 36, "tanX"], [122, 42, 121, 40], [122, 43, 121, 41], [123, 6, 123, 4], [123, 10, 123, 8, "azimuthAngle"], [123, 22, 123, 20], [123, 25, 123, 23], [123, 26, 123, 24], [123, 28, 123, 26], [124, 8, 124, 6, "azimuthAngle"], [124, 20, 124, 18], [124, 24, 124, 22], [124, 25, 124, 23], [124, 28, 124, 26, "Math"], [124, 32, 124, 30], [124, 33, 124, 31, "PI"], [124, 35, 124, 33], [125, 6, 125, 4], [126, 4, 126, 2], [126, 5, 126, 3], [126, 6, 126, 4], [128, 4, 129, 2], [128, 8, 129, 6, "altitudeAngle"], [128, 21, 129, 19], [128, 24, 129, 22], [128, 25, 129, 23], [129, 4, 131, 2], [129, 8, 131, 6, "Math"], [129, 12, 131, 10], [129, 13, 131, 11, "abs"], [129, 16, 131, 14], [129, 17, 131, 15, "tiltX"], [129, 22, 131, 20], [129, 23, 131, 21], [129, 28, 131, 26], [129, 30, 131, 28], [129, 34, 131, 32, "Math"], [129, 38, 131, 36], [129, 39, 131, 37, "abs"], [129, 42, 131, 40], [129, 43, 131, 41, "tiltY"], [129, 48, 131, 46], [129, 49, 131, 47], [129, 54, 131, 52], [129, 56, 131, 54], [129, 58, 131, 56], [130, 6, 132, 4, "altitudeAngle"], [130, 19, 132, 17], [130, 22, 132, 20], [130, 23, 132, 21], [131, 4, 133, 2], [131, 5, 133, 3], [131, 11, 133, 9], [131, 15, 133, 13, "tiltX"], [131, 20, 133, 18], [131, 25, 133, 23], [131, 26, 133, 24], [131, 28, 133, 26], [132, 6, 134, 4, "altitudeAngle"], [132, 19, 134, 17], [132, 22, 134, 20, "Math"], [132, 26, 134, 24], [132, 27, 134, 25, "PI"], [132, 29, 134, 27], [132, 32, 134, 30], [132, 33, 134, 31], [132, 36, 134, 34, "Math"], [132, 40, 134, 38], [132, 41, 134, 39, "abs"], [132, 44, 134, 42], [132, 45, 134, 43, "tiltYrad"], [132, 53, 134, 51], [132, 54, 134, 52], [133, 4, 135, 2], [133, 5, 135, 3], [133, 11, 135, 9], [133, 15, 135, 13, "tiltY"], [133, 20, 135, 18], [133, 25, 135, 23], [133, 26, 135, 24], [133, 28, 135, 26], [134, 6, 136, 4, "altitudeAngle"], [134, 19, 136, 17], [134, 22, 136, 20, "Math"], [134, 26, 136, 24], [134, 27, 136, 25, "PI"], [134, 29, 136, 27], [134, 32, 136, 30], [134, 33, 136, 31], [134, 36, 136, 34, "Math"], [134, 40, 136, 38], [134, 41, 136, 39, "abs"], [134, 44, 136, 42], [134, 45, 136, 43, "tiltXrad"], [134, 53, 136, 51], [134, 54, 136, 52], [135, 4, 137, 2], [135, 5, 137, 3], [135, 11, 137, 9], [136, 6, 138, 4], [137, 6, 139, 4, "altitudeAngle"], [137, 19, 139, 17], [137, 22, 139, 20, "Math"], [137, 26, 139, 24], [137, 27, 139, 25, "atan"], [137, 31, 139, 29], [137, 32, 139, 30], [137, 35, 139, 33], [137, 38, 139, 36, "Math"], [137, 42, 139, 40], [137, 43, 139, 41, "sqrt"], [137, 47, 139, 45], [137, 48, 139, 46, "Math"], [137, 52, 139, 50], [137, 53, 139, 51, "pow"], [137, 56, 139, 54], [137, 57, 139, 55, "Math"], [137, 61, 139, 59], [137, 62, 139, 60, "tan"], [137, 65, 139, 63], [137, 66, 139, 64, "tiltXrad"], [137, 74, 139, 72], [137, 75, 139, 73], [137, 77, 139, 75], [137, 78, 139, 76], [137, 79, 139, 77], [137, 82, 139, 80, "Math"], [137, 86, 139, 84], [137, 87, 139, 85, "pow"], [137, 90, 139, 88], [137, 91, 139, 89, "Math"], [137, 95, 139, 93], [137, 96, 139, 94, "tan"], [137, 99, 139, 97], [137, 100, 139, 98, "tiltYrad"], [137, 108, 139, 106], [137, 109, 139, 107], [137, 111, 139, 109], [137, 112, 139, 110], [137, 113, 139, 111], [137, 114, 139, 112], [137, 115, 139, 113], [138, 4, 140, 2], [139, 4, 142, 2], [139, 11, 142, 9], [140, 6, 143, 4, "altitudeAngle"], [140, 19, 143, 17], [140, 21, 143, 19, "altitudeAngle"], [140, 34, 143, 32], [141, 6, 144, 4, "azimuthAngle"], [141, 18, 144, 16], [141, 20, 144, 18, "azimuthAngle"], [142, 4, 145, 2], [142, 5, 145, 3], [143, 2, 146, 0], [143, 3, 146, 1], [143, 4, 146, 2], [144, 2, 147, 0], [145, 2, 148, 0], [147, 2, 151, 0], [147, 11, 151, 9, "spherical2tilt"], [147, 25, 151, 23, "spherical2tilt"], [147, 26, 151, 24, "altitudeAngle"], [147, 39, 151, 37], [147, 41, 151, 39, "azimuthAngle"], [147, 53, 151, 51], [147, 55, 151, 53], [148, 4, 152, 2], [148, 10, 152, 8, "radToDeg"], [148, 18, 152, 16], [148, 21, 152, 19], [148, 24, 152, 22], [148, 27, 152, 25, "Math"], [148, 31, 152, 29], [148, 32, 152, 30, "PI"], [148, 34, 152, 32], [149, 4, 153, 2], [149, 8, 153, 6, "tiltXrad"], [149, 16, 153, 14], [149, 19, 153, 17], [149, 20, 153, 18], [150, 4, 154, 2], [150, 8, 154, 6, "tiltYrad"], [150, 16, 154, 14], [150, 19, 154, 17], [150, 20, 154, 18], [151, 4, 156, 2], [151, 8, 156, 6, "altitudeAngle"], [151, 21, 156, 19], [151, 26, 156, 24], [151, 27, 156, 25], [151, 29, 156, 27], [152, 6, 157, 4], [153, 6, 158, 4], [153, 10, 158, 8, "azimuthAngle"], [153, 22, 158, 20], [153, 27, 158, 25], [153, 28, 158, 26], [153, 32, 158, 30, "azimuthAngle"], [153, 44, 158, 42], [153, 49, 158, 47], [153, 50, 158, 48], [153, 53, 158, 51, "Math"], [153, 57, 158, 55], [153, 58, 158, 56, "PI"], [153, 60, 158, 58], [153, 62, 158, 60], [154, 8, 159, 6], [155, 8, 160, 6, "tiltXrad"], [155, 16, 160, 14], [155, 19, 160, 17, "Math"], [155, 23, 160, 21], [155, 24, 160, 22, "PI"], [155, 26, 160, 24], [155, 29, 160, 27], [155, 30, 160, 28], [156, 6, 161, 4], [157, 6, 163, 4], [157, 10, 163, 8, "azimuthAngle"], [157, 22, 163, 20], [157, 27, 163, 25, "Math"], [157, 31, 163, 29], [157, 32, 163, 30, "PI"], [157, 34, 163, 32], [157, 37, 163, 35], [157, 38, 163, 36], [157, 40, 163, 38], [158, 8, 164, 6], [159, 8, 165, 6, "tiltYrad"], [159, 16, 165, 14], [159, 19, 165, 17, "Math"], [159, 23, 165, 21], [159, 24, 165, 22, "PI"], [159, 26, 165, 24], [159, 29, 165, 27], [159, 30, 165, 28], [160, 6, 166, 4], [161, 6, 168, 4], [161, 10, 168, 8, "azimuthAngle"], [161, 22, 168, 20], [161, 27, 168, 25, "Math"], [161, 31, 168, 29], [161, 32, 168, 30, "PI"], [161, 34, 168, 32], [161, 36, 168, 34], [162, 8, 169, 6], [163, 8, 170, 6, "tiltXrad"], [163, 16, 170, 14], [163, 19, 170, 17], [163, 20, 170, 18, "Math"], [163, 24, 170, 22], [163, 25, 170, 23, "PI"], [163, 27, 170, 25], [163, 30, 170, 28], [163, 31, 170, 29], [164, 6, 171, 4], [165, 6, 173, 4], [165, 10, 173, 8, "azimuthAngle"], [165, 22, 173, 20], [165, 27, 173, 25], [165, 28, 173, 26], [165, 31, 173, 29, "Math"], [165, 35, 173, 33], [165, 36, 173, 34, "PI"], [165, 38, 173, 36], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [166, 8, 174, 6], [167, 8, 175, 6, "tiltYrad"], [167, 16, 175, 14], [167, 19, 175, 17], [167, 20, 175, 18, "Math"], [167, 24, 175, 22], [167, 25, 175, 23, "PI"], [167, 27, 175, 25], [167, 30, 175, 28], [167, 31, 175, 29], [168, 6, 176, 4], [169, 6, 178, 4], [169, 10, 178, 8, "azimuthAngle"], [169, 22, 178, 20], [169, 25, 178, 23], [169, 26, 178, 24], [169, 30, 178, 28, "azimuthAngle"], [169, 42, 178, 40], [169, 45, 178, 43, "Math"], [169, 49, 178, 47], [169, 50, 178, 48, "PI"], [169, 52, 178, 50], [169, 55, 178, 53], [169, 56, 178, 54], [169, 58, 178, 56], [170, 8, 179, 6, "tiltXrad"], [170, 16, 179, 14], [170, 19, 179, 17, "Math"], [170, 23, 179, 21], [170, 24, 179, 22, "PI"], [170, 26, 179, 24], [170, 29, 179, 27], [170, 30, 179, 28], [171, 8, 180, 6, "tiltYrad"], [171, 16, 180, 14], [171, 19, 180, 17, "Math"], [171, 23, 180, 21], [171, 24, 180, 22, "PI"], [171, 26, 180, 24], [171, 29, 180, 27], [171, 30, 180, 28], [172, 6, 181, 4], [173, 6, 183, 4], [173, 10, 183, 8, "azimuthAngle"], [173, 22, 183, 20], [173, 25, 183, 23, "Math"], [173, 29, 183, 27], [173, 30, 183, 28, "PI"], [173, 32, 183, 30], [173, 35, 183, 33], [173, 36, 183, 34], [173, 40, 183, 38, "azimuthAngle"], [173, 52, 183, 50], [173, 55, 183, 53, "Math"], [173, 59, 183, 57], [173, 60, 183, 58, "PI"], [173, 62, 183, 60], [173, 64, 183, 62], [174, 8, 184, 6, "tiltXrad"], [174, 16, 184, 14], [174, 19, 184, 17], [174, 20, 184, 18, "Math"], [174, 24, 184, 22], [174, 25, 184, 23, "PI"], [174, 27, 184, 25], [174, 30, 184, 28], [174, 31, 184, 29], [175, 8, 185, 6, "tiltYrad"], [175, 16, 185, 14], [175, 19, 185, 17, "Math"], [175, 23, 185, 21], [175, 24, 185, 22, "PI"], [175, 26, 185, 24], [175, 29, 185, 27], [175, 30, 185, 28], [176, 6, 186, 4], [177, 6, 188, 4], [177, 10, 188, 8, "azimuthAngle"], [177, 22, 188, 20], [177, 25, 188, 23, "Math"], [177, 29, 188, 27], [177, 30, 188, 28, "PI"], [177, 32, 188, 30], [177, 36, 188, 34, "azimuthAngle"], [177, 48, 188, 46], [177, 51, 188, 49], [177, 52, 188, 50], [177, 55, 188, 53, "Math"], [177, 59, 188, 57], [177, 60, 188, 58, "PI"], [177, 62, 188, 60], [177, 65, 188, 63], [177, 66, 188, 64], [177, 68, 188, 66], [178, 8, 189, 6, "tiltXrad"], [178, 16, 189, 14], [178, 19, 189, 17], [178, 20, 189, 18, "Math"], [178, 24, 189, 22], [178, 25, 189, 23, "PI"], [178, 27, 189, 25], [178, 30, 189, 28], [178, 31, 189, 29], [179, 8, 190, 6, "tiltYrad"], [179, 16, 190, 14], [179, 19, 190, 17], [179, 20, 190, 18, "Math"], [179, 24, 190, 22], [179, 25, 190, 23, "PI"], [179, 27, 190, 25], [179, 30, 190, 28], [179, 31, 190, 29], [180, 6, 191, 4], [181, 6, 193, 4], [181, 10, 193, 8, "azimuthAngle"], [181, 22, 193, 20], [181, 25, 193, 23], [181, 26, 193, 24], [181, 29, 193, 27, "Math"], [181, 33, 193, 31], [181, 34, 193, 32, "PI"], [181, 36, 193, 34], [181, 39, 193, 37], [181, 40, 193, 38], [181, 44, 193, 42, "azimuthAngle"], [181, 56, 193, 54], [181, 59, 193, 57], [181, 60, 193, 58], [181, 63, 193, 61, "Math"], [181, 67, 193, 65], [181, 68, 193, 66, "PI"], [181, 70, 193, 68], [181, 72, 193, 70], [182, 8, 194, 6, "tiltXrad"], [182, 16, 194, 14], [182, 19, 194, 17, "Math"], [182, 23, 194, 21], [182, 24, 194, 22, "PI"], [182, 26, 194, 24], [182, 29, 194, 27], [182, 30, 194, 28], [183, 8, 195, 6, "tiltYrad"], [183, 16, 195, 14], [183, 19, 195, 17], [183, 20, 195, 18, "Math"], [183, 24, 195, 22], [183, 25, 195, 23, "PI"], [183, 27, 195, 25], [183, 30, 195, 28], [183, 31, 195, 29], [184, 6, 196, 4], [185, 4, 197, 2], [186, 4, 199, 2], [186, 8, 199, 6, "altitudeAngle"], [186, 21, 199, 19], [186, 26, 199, 24], [186, 27, 199, 25], [186, 29, 199, 27], [187, 6, 200, 4], [187, 12, 200, 10, "tanAlt"], [187, 18, 200, 16], [187, 21, 200, 19, "Math"], [187, 25, 200, 23], [187, 26, 200, 24, "tan"], [187, 29, 200, 27], [187, 30, 200, 28, "altitudeAngle"], [187, 43, 200, 41], [187, 44, 200, 42], [188, 6, 201, 4, "tiltXrad"], [188, 14, 201, 12], [188, 17, 201, 15, "Math"], [188, 21, 201, 19], [188, 22, 201, 20, "atan"], [188, 26, 201, 24], [188, 27, 201, 25, "Math"], [188, 31, 201, 29], [188, 32, 201, 30, "cos"], [188, 35, 201, 33], [188, 36, 201, 34, "azimuthAngle"], [188, 48, 201, 46], [188, 49, 201, 47], [188, 52, 201, 50, "tanAlt"], [188, 58, 201, 56], [188, 59, 201, 57], [189, 6, 202, 4, "tiltYrad"], [189, 14, 202, 12], [189, 17, 202, 15, "Math"], [189, 21, 202, 19], [189, 22, 202, 20, "atan"], [189, 26, 202, 24], [189, 27, 202, 25, "Math"], [189, 31, 202, 29], [189, 32, 202, 30, "sin"], [189, 35, 202, 33], [189, 36, 202, 34, "azimuthAngle"], [189, 48, 202, 46], [189, 49, 202, 47], [189, 52, 202, 50, "tanAlt"], [189, 58, 202, 56], [189, 59, 202, 57], [190, 4, 203, 2], [191, 4, 205, 2], [191, 10, 205, 8, "tiltX"], [191, 15, 205, 13], [191, 18, 205, 16, "Math"], [191, 22, 205, 20], [191, 23, 205, 21, "round"], [191, 28, 205, 26], [191, 29, 205, 27, "tiltXrad"], [191, 37, 205, 35], [191, 40, 205, 38, "radToDeg"], [191, 48, 205, 46], [191, 49, 205, 47], [192, 4, 206, 2], [192, 10, 206, 8, "tiltY"], [192, 15, 206, 13], [192, 18, 206, 16, "Math"], [192, 22, 206, 20], [192, 23, 206, 21, "round"], [192, 28, 206, 26], [192, 29, 206, 27, "tiltYrad"], [192, 37, 206, 35], [192, 40, 206, 38, "radToDeg"], [192, 48, 206, 46], [192, 49, 206, 47], [193, 4, 207, 2], [193, 11, 207, 9], [194, 6, 208, 4, "tiltX"], [194, 11, 208, 9], [195, 6, 209, 4, "tiltY"], [196, 4, 210, 2], [196, 5, 210, 3], [197, 2, 211, 0], [198, 2, 213, 7], [198, 8, 213, 13, "RNSVGElements"], [198, 21, 213, 26], [198, 24, 213, 26, "exports"], [198, 31, 213, 26], [198, 32, 213, 26, "RNSVGElements"], [198, 45, 213, 26], [198, 48, 213, 29], [198, 52, 213, 33, "Set"], [198, 55, 213, 36], [198, 56, 213, 37], [198, 57, 213, 38], [198, 65, 213, 46], [198, 67, 213, 48], [198, 77, 213, 58], [198, 79, 213, 60], [198, 88, 213, 69], [198, 90, 213, 71], [198, 105, 213, 86], [198, 107, 213, 88], [198, 110, 213, 91], [198, 112, 213, 93], [198, 119, 213, 100], [198, 121, 213, 102], [198, 127, 213, 108], [198, 129, 213, 110], [198, 137, 213, 118], [198, 139, 213, 120], [198, 145, 213, 126], [198, 147, 213, 128], [198, 153, 213, 134], [198, 155, 213, 136], [198, 164, 213, 145], [198, 166, 213, 147], [198, 175, 213, 156], [198, 177, 213, 158], [198, 187, 213, 168], [198, 189, 213, 170], [198, 195, 213, 176], [198, 197, 213, 178], [198, 202, 213, 183], [198, 204, 213, 185], [198, 212, 213, 193], [198, 214, 213, 195], [198, 221, 213, 202], [198, 223, 213, 204], [198, 229, 213, 210], [198, 231, 213, 212], [198, 241, 213, 222], [198, 243, 213, 224], [198, 248, 213, 229], [198, 249, 213, 230], [198, 250, 213, 231], [198, 251, 213, 232], [198, 252, 213, 233], [199, 2, 214, 0], [200, 2, 215, 0], [201, 2, 216, 0], [202, 2, 217, 0], [203, 2, 218, 0], [204, 2, 219, 0], [205, 2, 220, 0], [206, 2, 221, 0], [208, 2, 223, 7], [208, 11, 223, 16, "isRNSVGElement"], [208, 25, 223, 30, "isRNSVGElement"], [208, 26, 223, 31, "viewRef"], [208, 33, 223, 38], [208, 35, 223, 40], [209, 4, 224, 2], [209, 10, 224, 8, "componentClassName"], [209, 28, 224, 26], [209, 31, 224, 29, "Object"], [209, 37, 224, 35], [209, 38, 224, 36, "getPrototypeOf"], [209, 52, 224, 50], [209, 53, 224, 51, "viewRef"], [209, 60, 224, 58], [209, 61, 224, 59], [209, 62, 224, 60, "constructor"], [209, 73, 224, 71], [209, 74, 224, 72, "name"], [209, 78, 224, 76], [210, 4, 225, 2], [210, 11, 225, 9, "RNSVGElements"], [210, 24, 225, 22], [210, 25, 225, 23, "has"], [210, 28, 225, 26], [210, 29, 225, 27, "componentClassName"], [210, 47, 225, 45], [210, 48, 225, 46], [210, 52, 225, 50, "Object"], [210, 58, 225, 56], [210, 59, 225, 57, "hasOwn"], [210, 65, 225, 63], [210, 66, 225, 64, "viewRef"], [210, 73, 225, 71], [210, 75, 225, 73], [210, 87, 225, 85], [210, 88, 225, 86], [211, 2, 226, 0], [211, 3, 226, 1], [211, 4, 226, 2], [212, 2, 227, 0], [213, 2, 228, 0], [214, 2, 229, 0], [215, 2, 230, 0], [217, 2, 232, 7], [217, 11, 232, 16, "isRNSVGNode"], [217, 22, 232, 27, "isRNSVGNode"], [217, 23, 232, 28, "node"], [217, 27, 232, 32], [217, 29, 232, 34], [218, 4, 233, 2], [218, 8, 233, 6, "_node$ref"], [218, 17, 233, 15], [218, 19, 233, 17, "_Object$getPrototypeO"], [218, 40, 233, 38], [218, 42, 233, 40, "_node$type"], [218, 52, 233, 50], [220, 4, 235, 2], [221, 4, 236, 2], [222, 4, 237, 2], [222, 8, 237, 6], [222, 9, 237, 7, "_node$ref"], [222, 18, 237, 16], [222, 21, 237, 19, "node"], [222, 25, 237, 23], [222, 26, 237, 24, "ref"], [222, 29, 237, 27], [222, 35, 237, 33], [222, 39, 237, 37], [222, 43, 237, 41, "_node$ref"], [222, 52, 237, 50], [222, 57, 237, 55], [222, 62, 237, 60], [222, 63, 237, 61], [222, 67, 237, 65, "_node$ref"], [222, 76, 237, 74], [222, 77, 237, 75, "rngh"], [222, 81, 237, 79], [222, 83, 237, 81], [223, 6, 238, 4], [223, 13, 238, 11], [223, 18, 238, 16], [224, 4, 239, 2], [225, 4, 241, 2], [225, 11, 241, 9], [225, 12, 241, 10], [225, 13, 241, 11, "_Object$getPrototypeO"], [225, 34, 241, 32], [225, 37, 241, 35, "Object"], [225, 43, 241, 41], [225, 44, 241, 42, "getPrototypeOf"], [225, 58, 241, 56], [225, 59, 241, 57, "node"], [225, 63, 241, 61], [225, 68, 241, 66], [225, 72, 241, 70], [225, 76, 241, 74, "node"], [225, 80, 241, 78], [225, 85, 241, 83], [225, 90, 241, 88], [225, 91, 241, 89], [225, 94, 241, 92], [225, 99, 241, 97], [225, 100, 241, 98], [225, 103, 241, 101, "node"], [225, 107, 241, 105], [225, 108, 241, 106, "type"], [225, 112, 241, 110], [225, 113, 241, 111], [225, 119, 241, 117], [225, 123, 241, 121], [225, 127, 241, 125, "_Object$getPrototypeO"], [225, 148, 241, 146], [225, 153, 241, 151], [225, 158, 241, 156], [225, 159, 241, 157], [225, 162, 241, 160], [225, 167, 241, 165], [225, 168, 241, 166], [225, 171, 241, 169, "_Object$getPrototypeO"], [225, 192, 241, 190], [225, 193, 241, 191, "name"], [225, 197, 241, 195], [225, 203, 241, 201], [225, 213, 241, 211], [225, 217, 241, 215, "RNSVGElements"], [225, 230, 241, 228], [225, 231, 241, 229, "has"], [225, 234, 241, 232], [225, 235, 241, 233, "node"], [225, 239, 241, 237], [225, 244, 241, 242], [225, 248, 241, 246], [225, 252, 241, 250, "node"], [225, 256, 241, 254], [225, 261, 241, 259], [225, 266, 241, 264], [225, 267, 241, 265], [225, 270, 241, 268], [225, 275, 241, 273], [225, 276, 241, 274], [225, 279, 241, 277], [225, 280, 241, 278, "_node$type"], [225, 290, 241, 288], [225, 293, 241, 291, "node"], [225, 297, 241, 295], [225, 298, 241, 296, "type"], [225, 302, 241, 300], [225, 308, 241, 306], [225, 312, 241, 310], [225, 316, 241, 314, "_node$type"], [225, 326, 241, 324], [225, 331, 241, 329], [225, 336, 241, 334], [225, 337, 241, 335], [225, 340, 241, 338], [225, 345, 241, 343], [225, 346, 241, 344], [225, 349, 241, 347, "_node$type"], [225, 359, 241, 357], [225, 360, 241, 358, "displayName"], [225, 371, 241, 369], [225, 372, 241, 370], [226, 2, 242, 0], [227, 0, 242, 1], [227, 3]], "functionMap": {"names": ["<global>", "isPointerInBounds", "degToRad", "coneToDeviation", "calculateViewScale", "tryExtractStylusData", "tilt2spherical", "spherical2tilt", "isRNSVGElement", "isRNSVGNode"], "mappings": "AAA;OCC;CDM;wBEE,kCF;+BGC,0CH;OIC;CJ6B;OKC;CLmD;AMK;CNgD;AOK;CP4D;OQY;CRG;OSM;CTU"}}, "type": "js/module"}]}