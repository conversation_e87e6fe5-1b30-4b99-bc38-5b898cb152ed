{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const GitCompareArrows = exports.default = (0, _createLucideIcon.default)(\"GitCompareArrows\", [[\"circle\", {\n    cx: \"5\",\n    cy: \"6\",\n    r: \"3\",\n    key: \"1qnov2\"\n  }], [\"path\", {\n    d: \"M12 6h5a2 2 0 0 1 2 2v7\",\n    key: \"1yj91y\"\n  }], [\"path\", {\n    d: \"m15 9-3-3 3-3\",\n    key: \"1lwv8l\"\n  }], [\"circle\", {\n    cx: \"19\",\n    cy: \"18\",\n    r: \"3\",\n    key: \"1qljk2\"\n  }], [\"path\", {\n    d: \"M12 18H7a2 2 0 0 1-2-2V9\",\n    key: \"16sdep\"\n  }], [\"path\", {\n    d: \"m9 15 3 3-3 3\",\n    key: \"1m3kbl\"\n  }]]);\n});", "lineCount": 38, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "GitCompareArrows"], [15, 24, 10, 22], [15, 27, 10, 22, "exports"], [15, 34, 10, 22], [15, 35, 10, 22, "default"], [15, 42, 10, 22], [15, 45, 10, 25], [15, 49, 10, 25, "createLucideIcon"], [15, 74, 10, 41], [15, 76, 10, 42], [15, 94, 10, 60], [15, 96, 10, 62], [15, 97, 11, 2], [15, 98, 11, 3], [15, 106, 11, 11], [15, 108, 11, 13], [16, 4, 11, 15, "cx"], [16, 6, 11, 17], [16, 8, 11, 19], [16, 11, 11, 22], [17, 4, 11, 24, "cy"], [17, 6, 11, 26], [17, 8, 11, 28], [17, 11, 11, 31], [18, 4, 11, 33, "r"], [18, 5, 11, 34], [18, 7, 11, 36], [18, 10, 11, 39], [19, 4, 11, 41, "key"], [19, 7, 11, 44], [19, 9, 11, 46], [20, 2, 11, 55], [20, 3, 11, 56], [20, 4, 11, 57], [20, 6, 12, 2], [20, 7, 12, 3], [20, 13, 12, 9], [20, 15, 12, 11], [21, 4, 12, 13, "d"], [21, 5, 12, 14], [21, 7, 12, 16], [21, 32, 12, 41], [22, 4, 12, 43, "key"], [22, 7, 12, 46], [22, 9, 12, 48], [23, 2, 12, 57], [23, 3, 12, 58], [23, 4, 12, 59], [23, 6, 13, 2], [23, 7, 13, 3], [23, 13, 13, 9], [23, 15, 13, 11], [24, 4, 13, 13, "d"], [24, 5, 13, 14], [24, 7, 13, 16], [24, 22, 13, 31], [25, 4, 13, 33, "key"], [25, 7, 13, 36], [25, 9, 13, 38], [26, 2, 13, 47], [26, 3, 13, 48], [26, 4, 13, 49], [26, 6, 14, 2], [26, 7, 14, 3], [26, 15, 14, 11], [26, 17, 14, 13], [27, 4, 14, 15, "cx"], [27, 6, 14, 17], [27, 8, 14, 19], [27, 12, 14, 23], [28, 4, 14, 25, "cy"], [28, 6, 14, 27], [28, 8, 14, 29], [28, 12, 14, 33], [29, 4, 14, 35, "r"], [29, 5, 14, 36], [29, 7, 14, 38], [29, 10, 14, 41], [30, 4, 14, 43, "key"], [30, 7, 14, 46], [30, 9, 14, 48], [31, 2, 14, 57], [31, 3, 14, 58], [31, 4, 14, 59], [31, 6, 15, 2], [31, 7, 15, 3], [31, 13, 15, 9], [31, 15, 15, 11], [32, 4, 15, 13, "d"], [32, 5, 15, 14], [32, 7, 15, 16], [32, 33, 15, 42], [33, 4, 15, 44, "key"], [33, 7, 15, 47], [33, 9, 15, 49], [34, 2, 15, 58], [34, 3, 15, 59], [34, 4, 15, 60], [34, 6, 16, 2], [34, 7, 16, 3], [34, 13, 16, 9], [34, 15, 16, 11], [35, 4, 16, 13, "d"], [35, 5, 16, 14], [35, 7, 16, 16], [35, 22, 16, 31], [36, 4, 16, 33, "key"], [36, 7, 16, 36], [36, 9, 16, 38], [37, 2, 16, 47], [37, 3, 16, 48], [37, 4, 16, 49], [37, 5, 17, 1], [37, 6, 17, 2], [38, 0, 17, 3], [38, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}