{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./AnimatedEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 65}}], "key": "c7AooxRYFqBD9mVagDev/GMMGiE=", "exportNames": ["*"]}}, {"name": "./AnimatedImplementation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 62}}], "key": "MmE1c5G8MIzpHpSfKBLhd7ZPBbI=", "exportNames": ["*"]}}, {"name": "./createAnimatedComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 64}}], "key": "ULBS35x9qf+879w0v+Zk4awjD2M=", "exportNames": ["*"]}}, {"name": "./nodes/AnimatedColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 50}}], "key": "gFGgLTIWH3dK+ZDh4iJRleH39vI=", "exportNames": ["*"]}}, {"name": "./nodes/AnimatedInterpolation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 66}}], "key": "7AvogLNMCq+0dYvYtQwojfD4N+E=", "exportNames": ["*"]}}, {"name": "./nodes/AnimatedNode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 48}}], "key": "PbjdhlWfw8UuzyhESFYeEh3/fNI=", "exportNames": ["*"]}}, {"name": "./nodes/AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 50}}], "key": "f81vU8CZKg/cTtdZZWovPFjkmVQ=", "exportNames": ["*"]}}, {"name": "./nodes/AnimatedValueXY", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 54}}], "key": "7SBCZjhpUHSM8w3orgZuIXhtT8I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _AnimatedEvent = require(_dependencyMap[1], \"./AnimatedEvent\");\n  var _AnimatedImplementation = _interopRequireDefault(require(_dependencyMap[2], \"./AnimatedImplementation\"));\n  var _createAnimatedComponent = _interopRequireDefault(require(_dependencyMap[3], \"./createAnimatedComponent\"));\n  var _AnimatedColor = _interopRequireDefault(require(_dependencyMap[4], \"./nodes/AnimatedColor\"));\n  var _AnimatedInterpolation = _interopRequireDefault(require(_dependencyMap[5], \"./nodes/AnimatedInterpolation\"));\n  var _AnimatedNode = _interopRequireDefault(require(_dependencyMap[6], \"./nodes/AnimatedNode\"));\n  var _AnimatedValue = _interopRequireDefault(require(_dependencyMap[7], \"./nodes/AnimatedValue\"));\n  var _AnimatedValueXY = _interopRequireDefault(require(_dependencyMap[8], \"./nodes/AnimatedValueXY\"));\n  var inAnimationCallback = false;\n  function mockAnimationStart(start) {\n    return callback => {\n      var guardedCallback = callback == null ? callback : function () {\n        if (inAnimationCallback) {\n          console.warn('Ignoring recursive animation callback when running mock animations');\n          return;\n        }\n        inAnimationCallback = true;\n        try {\n          callback(...arguments);\n        } finally {\n          inAnimationCallback = false;\n        }\n      };\n      start(guardedCallback);\n    };\n  }\n  var emptyAnimation = {\n    start: () => {},\n    stop: () => {},\n    reset: () => {},\n    _startNativeLoop: () => {},\n    _isUsingNativeDriver: () => {\n      return false;\n    }\n  };\n  var mockCompositeAnimation = animations => ({\n    ...emptyAnimation,\n    start: mockAnimationStart(callback => {\n      animations.forEach(animation => animation.start());\n      callback?.({\n        finished: true\n      });\n    })\n  });\n  var spring = function (value, config) {\n    var anyValue = value;\n    return {\n      ...emptyAnimation,\n      start: mockAnimationStart(callback => {\n        anyValue.setValue(config.toValue);\n        callback?.({\n          finished: true\n        });\n      })\n    };\n  };\n  var timing = function (value, config) {\n    var anyValue = value;\n    return {\n      ...emptyAnimation,\n      start: mockAnimationStart(callback => {\n        anyValue.setValue(config.toValue);\n        callback?.({\n          finished: true\n        });\n      })\n    };\n  };\n  var decay = function (value, config) {\n    return emptyAnimation;\n  };\n  var sequence = function (animations) {\n    return mockCompositeAnimation(animations);\n  };\n  var parallel = function (animations, config) {\n    return mockCompositeAnimation(animations);\n  };\n  var delay = function (time) {\n    return emptyAnimation;\n  };\n  var stagger = function (time, animations) {\n    return mockCompositeAnimation(animations);\n  };\n  var loop = function (animation) {\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      _ref$iterations = _ref.iterations,\n      iterations = _ref$iterations === void 0 ? -1 : _ref$iterations;\n    return emptyAnimation;\n  };\n  var _default = exports.default = {\n    Value: _AnimatedValue.default,\n    ValueXY: _AnimatedValueXY.default,\n    Color: _AnimatedColor.default,\n    Interpolation: _AnimatedInterpolation.default,\n    Node: _AnimatedNode.default,\n    decay,\n    timing,\n    spring,\n    add: _AnimatedImplementation.default.add,\n    subtract: _AnimatedImplementation.default.subtract,\n    divide: _AnimatedImplementation.default.divide,\n    multiply: _AnimatedImplementation.default.multiply,\n    modulo: _AnimatedImplementation.default.modulo,\n    diffClamp: _AnimatedImplementation.default.diffClamp,\n    delay,\n    sequence,\n    parallel,\n    stagger,\n    loop,\n    event: _AnimatedImplementation.default.event,\n    createAnimatedComponent: _createAnimatedComponent.default,\n    attachNativeEvent: _AnimatedEvent.attachNativeEvent,\n    forkEvent: _AnimatedImplementation.default.forkEvent,\n    unforkEvent: _AnimatedImplementation.default.unforkEvent,\n    Event: _AnimatedEvent.AnimatedEvent\n  };\n});", "lineCount": 125, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 20, 0], [9, 6, 20, 0, "_AnimatedEvent"], [9, 20, 20, 0], [9, 23, 20, 0, "require"], [9, 30, 20, 0], [9, 31, 20, 0, "_dependencyMap"], [9, 45, 20, 0], [10, 2, 21, 0], [10, 6, 21, 0, "_AnimatedImplementation"], [10, 29, 21, 0], [10, 32, 21, 0, "_interopRequireDefault"], [10, 54, 21, 0], [10, 55, 21, 0, "require"], [10, 62, 21, 0], [10, 63, 21, 0, "_dependencyMap"], [10, 77, 21, 0], [11, 2, 22, 0], [11, 6, 22, 0, "_createAnimatedComponent"], [11, 30, 22, 0], [11, 33, 22, 0, "_interopRequireDefault"], [11, 55, 22, 0], [11, 56, 22, 0, "require"], [11, 63, 22, 0], [11, 64, 22, 0, "_dependencyMap"], [11, 78, 22, 0], [12, 2, 23, 0], [12, 6, 23, 0, "_AnimatedColor"], [12, 20, 23, 0], [12, 23, 23, 0, "_interopRequireDefault"], [12, 45, 23, 0], [12, 46, 23, 0, "require"], [12, 53, 23, 0], [12, 54, 23, 0, "_dependencyMap"], [12, 68, 23, 0], [13, 2, 24, 0], [13, 6, 24, 0, "_AnimatedInterpolation"], [13, 28, 24, 0], [13, 31, 24, 0, "_interopRequireDefault"], [13, 53, 24, 0], [13, 54, 24, 0, "require"], [13, 61, 24, 0], [13, 62, 24, 0, "_dependencyMap"], [13, 76, 24, 0], [14, 2, 25, 0], [14, 6, 25, 0, "_AnimatedNode"], [14, 19, 25, 0], [14, 22, 25, 0, "_interopRequireDefault"], [14, 44, 25, 0], [14, 45, 25, 0, "require"], [14, 52, 25, 0], [14, 53, 25, 0, "_dependencyMap"], [14, 67, 25, 0], [15, 2, 26, 0], [15, 6, 26, 0, "_AnimatedValue"], [15, 20, 26, 0], [15, 23, 26, 0, "_interopRequireDefault"], [15, 45, 26, 0], [15, 46, 26, 0, "require"], [15, 53, 26, 0], [15, 54, 26, 0, "_dependencyMap"], [15, 68, 26, 0], [16, 2, 27, 0], [16, 6, 27, 0, "_AnimatedValueXY"], [16, 22, 27, 0], [16, 25, 27, 0, "_interopRequireDefault"], [16, 47, 27, 0], [16, 48, 27, 0, "require"], [16, 55, 27, 0], [16, 56, 27, 0, "_dependencyMap"], [16, 70, 27, 0], [17, 2, 38, 0], [17, 6, 38, 4, "inAnimationCallback"], [17, 25, 38, 23], [17, 28, 38, 26], [17, 33, 38, 31], [18, 2, 39, 0], [18, 11, 39, 9, "mockAnimationStart"], [18, 29, 39, 27, "mockAnimationStart"], [18, 30, 40, 2, "start"], [18, 35, 40, 42], [18, 37, 41, 37], [19, 4, 42, 2], [19, 11, 42, 9, "callback"], [19, 19, 42, 17], [19, 23, 42, 21], [20, 6, 43, 4], [20, 10, 43, 10, "guarded<PERSON><PERSON>back"], [20, 25, 43, 25], [20, 28, 44, 6, "callback"], [20, 36, 44, 14], [20, 40, 44, 18], [20, 44, 44, 22], [20, 47, 45, 10, "callback"], [20, 55, 45, 18], [20, 58, 46, 10], [20, 70, 46, 41], [21, 8, 47, 12], [21, 12, 47, 16, "inAnimationCallback"], [21, 31, 47, 35], [21, 33, 47, 37], [22, 10, 48, 14, "console"], [22, 17, 48, 21], [22, 18, 48, 22, "warn"], [22, 22, 48, 26], [22, 23, 49, 16], [22, 91, 50, 14], [22, 92, 50, 15], [23, 10, 51, 14], [24, 8, 52, 12], [25, 8, 53, 12, "inAnimationCallback"], [25, 27, 53, 31], [25, 30, 53, 34], [25, 34, 53, 38], [26, 8, 54, 12], [26, 12, 54, 16], [27, 10, 55, 14, "callback"], [27, 18, 55, 22], [27, 19, 55, 23], [27, 22, 55, 23, "arguments"], [27, 31, 55, 30], [27, 32, 55, 31], [28, 8, 56, 12], [28, 9, 56, 13], [28, 18, 56, 22], [29, 10, 57, 14, "inAnimationCallback"], [29, 29, 57, 33], [29, 32, 57, 36], [29, 37, 57, 41], [30, 8, 58, 12], [31, 6, 59, 10], [31, 7, 59, 11], [32, 6, 60, 4, "start"], [32, 11, 60, 9], [32, 12, 60, 10, "guarded<PERSON><PERSON>back"], [32, 27, 60, 25], [32, 28, 60, 26], [33, 4, 61, 2], [33, 5, 61, 3], [34, 2, 62, 0], [35, 2, 73, 0], [35, 6, 73, 6, "emptyAnimation"], [35, 20, 73, 20], [35, 23, 73, 23], [36, 4, 74, 2, "start"], [36, 9, 74, 7], [36, 11, 74, 9, "start"], [36, 12, 74, 9], [36, 17, 74, 15], [36, 18, 74, 16], [36, 19, 74, 17], [37, 4, 75, 2, "stop"], [37, 8, 75, 6], [37, 10, 75, 8, "stop"], [37, 11, 75, 8], [37, 16, 75, 14], [37, 17, 75, 15], [37, 18, 75, 16], [38, 4, 76, 2, "reset"], [38, 9, 76, 7], [38, 11, 76, 9, "reset"], [38, 12, 76, 9], [38, 17, 76, 15], [38, 18, 76, 16], [38, 19, 76, 17], [39, 4, 77, 2, "_startNativeLoop"], [39, 20, 77, 18], [39, 22, 77, 20, "_startNativeLoop"], [39, 23, 77, 20], [39, 28, 77, 26], [39, 29, 77, 27], [39, 30, 77, 28], [40, 4, 78, 2, "_isUsingNativeDriver"], [40, 24, 78, 22], [40, 26, 78, 24, "_isUsingNativeDriver"], [40, 27, 78, 24], [40, 32, 78, 30], [41, 6, 79, 4], [41, 13, 79, 11], [41, 18, 79, 16], [42, 4, 80, 2], [43, 2, 81, 0], [43, 3, 81, 1], [44, 2, 83, 0], [44, 6, 83, 6, "mockCompositeAnimation"], [44, 28, 83, 28], [44, 31, 84, 2, "animations"], [44, 41, 84, 39], [44, 46, 85, 26], [45, 4, 86, 2], [45, 7, 86, 5, "emptyAnimation"], [45, 21, 86, 19], [46, 4, 87, 2, "start"], [46, 9, 87, 7], [46, 11, 87, 9, "mockAnimationStart"], [46, 29, 87, 27], [46, 30, 87, 29, "callback"], [46, 38, 87, 52], [46, 42, 87, 63], [47, 6, 88, 4, "animations"], [47, 16, 88, 14], [47, 17, 88, 15, "for<PERSON>ach"], [47, 24, 88, 22], [47, 25, 88, 23, "animation"], [47, 34, 88, 32], [47, 38, 88, 36, "animation"], [47, 47, 88, 45], [47, 48, 88, 46, "start"], [47, 53, 88, 51], [47, 54, 88, 52], [47, 55, 88, 53], [47, 56, 88, 54], [48, 6, 89, 4, "callback"], [48, 14, 89, 12], [48, 17, 89, 15], [49, 8, 89, 16, "finished"], [49, 16, 89, 24], [49, 18, 89, 26], [50, 6, 89, 30], [50, 7, 89, 31], [50, 8, 89, 32], [51, 4, 90, 2], [51, 5, 90, 3], [52, 2, 91, 0], [52, 3, 91, 1], [52, 4, 91, 2], [53, 2, 93, 0], [53, 6, 93, 6, "spring"], [53, 12, 93, 12], [53, 15, 93, 15], [53, 24, 93, 15, "spring"], [53, 25, 94, 2, "value"], [53, 30, 94, 56], [53, 32, 95, 2, "config"], [53, 38, 95, 31], [53, 40, 96, 22], [54, 4, 97, 2], [54, 8, 97, 8, "anyValue"], [54, 16, 97, 21], [54, 19, 97, 24, "value"], [54, 24, 97, 29], [55, 4, 98, 2], [55, 11, 98, 9], [56, 6, 99, 4], [56, 9, 99, 7, "emptyAnimation"], [56, 23, 99, 21], [57, 6, 100, 4, "start"], [57, 11, 100, 9], [57, 13, 100, 11, "mockAnimationStart"], [57, 31, 100, 29], [57, 32, 100, 31, "callback"], [57, 40, 100, 54], [57, 44, 100, 65], [58, 8, 101, 6, "anyValue"], [58, 16, 101, 14], [58, 17, 101, 15, "setValue"], [58, 25, 101, 23], [58, 26, 101, 24, "config"], [58, 32, 101, 30], [58, 33, 101, 31, "toValue"], [58, 40, 101, 38], [58, 41, 101, 39], [59, 8, 102, 6, "callback"], [59, 16, 102, 14], [59, 19, 102, 17], [60, 10, 102, 18, "finished"], [60, 18, 102, 26], [60, 20, 102, 28], [61, 8, 102, 32], [61, 9, 102, 33], [61, 10, 102, 34], [62, 6, 103, 4], [62, 7, 103, 5], [63, 4, 104, 2], [63, 5, 104, 3], [64, 2, 105, 0], [64, 3, 105, 1], [65, 2, 107, 0], [65, 6, 107, 6, "timing"], [65, 12, 107, 12], [65, 15, 107, 15], [65, 24, 107, 15, "timing"], [65, 25, 108, 2, "value"], [65, 30, 108, 56], [65, 32, 109, 2, "config"], [65, 38, 109, 31], [65, 40, 110, 22], [66, 4, 111, 2], [66, 8, 111, 8, "anyValue"], [66, 16, 111, 21], [66, 19, 111, 24, "value"], [66, 24, 111, 29], [67, 4, 112, 2], [67, 11, 112, 9], [68, 6, 113, 4], [68, 9, 113, 7, "emptyAnimation"], [68, 23, 113, 21], [69, 6, 114, 4, "start"], [69, 11, 114, 9], [69, 13, 114, 11, "mockAnimationStart"], [69, 31, 114, 29], [69, 32, 114, 31, "callback"], [69, 40, 114, 54], [69, 44, 114, 65], [70, 8, 115, 6, "anyValue"], [70, 16, 115, 14], [70, 17, 115, 15, "setValue"], [70, 25, 115, 23], [70, 26, 115, 24, "config"], [70, 32, 115, 30], [70, 33, 115, 31, "toValue"], [70, 40, 115, 38], [70, 41, 115, 39], [71, 8, 116, 6, "callback"], [71, 16, 116, 14], [71, 19, 116, 17], [72, 10, 116, 18, "finished"], [72, 18, 116, 26], [72, 20, 116, 28], [73, 8, 116, 32], [73, 9, 116, 33], [73, 10, 116, 34], [74, 6, 117, 4], [74, 7, 117, 5], [75, 4, 118, 2], [75, 5, 118, 3], [76, 2, 119, 0], [76, 3, 119, 1], [77, 2, 121, 0], [77, 6, 121, 6, "decay"], [77, 11, 121, 11], [77, 14, 121, 14], [77, 23, 121, 14, "decay"], [77, 24, 122, 2, "value"], [77, 29, 122, 56], [77, 31, 123, 2, "config"], [77, 37, 123, 30], [77, 39, 124, 22], [78, 4, 125, 2], [78, 11, 125, 9, "emptyAnimation"], [78, 25, 125, 23], [79, 2, 126, 0], [79, 3, 126, 1], [80, 2, 128, 0], [80, 6, 128, 6, "sequence"], [80, 14, 128, 14], [80, 17, 128, 17], [80, 26, 128, 17, "sequence"], [80, 27, 129, 2, "animations"], [80, 37, 129, 39], [80, 39, 130, 22], [81, 4, 131, 2], [81, 11, 131, 9, "mockCompositeAnimation"], [81, 33, 131, 31], [81, 34, 131, 32, "animations"], [81, 44, 131, 42], [81, 45, 131, 43], [82, 2, 132, 0], [82, 3, 132, 1], [83, 2, 135, 0], [83, 6, 135, 6, "parallel"], [83, 14, 135, 14], [83, 17, 135, 17], [83, 26, 135, 17, "parallel"], [83, 27, 136, 2, "animations"], [83, 37, 136, 39], [83, 39, 137, 2, "config"], [83, 45, 137, 26], [83, 47, 138, 22], [84, 4, 139, 2], [84, 11, 139, 9, "mockCompositeAnimation"], [84, 33, 139, 31], [84, 34, 139, 32, "animations"], [84, 44, 139, 42], [84, 45, 139, 43], [85, 2, 140, 0], [85, 3, 140, 1], [86, 2, 142, 0], [86, 6, 142, 6, "delay"], [86, 11, 142, 11], [86, 14, 142, 14], [86, 23, 142, 14, "delay"], [86, 24, 142, 24, "time"], [86, 28, 142, 36], [86, 30, 142, 58], [87, 4, 143, 2], [87, 11, 143, 9, "emptyAnimation"], [87, 25, 143, 23], [88, 2, 144, 0], [88, 3, 144, 1], [89, 2, 146, 0], [89, 6, 146, 6, "stagger"], [89, 13, 146, 13], [89, 16, 146, 16], [89, 25, 146, 16, "stagger"], [89, 26, 147, 2, "time"], [89, 30, 147, 14], [89, 32, 148, 2, "animations"], [89, 42, 148, 39], [89, 44, 149, 22], [90, 4, 150, 2], [90, 11, 150, 9, "mockCompositeAnimation"], [90, 33, 150, 31], [90, 34, 150, 32, "animations"], [90, 44, 150, 42], [90, 45, 150, 43], [91, 2, 151, 0], [91, 3, 151, 1], [92, 2, 159, 0], [92, 6, 159, 6, "loop"], [92, 10, 159, 10], [92, 13, 159, 13], [92, 22, 159, 13, "loop"], [92, 23, 160, 2, "animation"], [92, 32, 160, 31], [92, 34, 163, 22], [93, 4, 163, 22], [93, 8, 163, 22, "_ref"], [93, 12, 163, 22], [93, 15, 163, 22, "arguments"], [93, 24, 163, 22], [93, 25, 163, 22, "length"], [93, 31, 163, 22], [93, 39, 163, 22, "arguments"], [93, 48, 163, 22], [93, 56, 163, 22, "undefined"], [93, 65, 163, 22], [93, 68, 163, 22, "arguments"], [93, 77, 163, 22], [93, 83, 162, 43], [93, 84, 162, 44], [93, 85, 162, 45], [94, 6, 162, 45, "_ref$iterations"], [94, 21, 162, 45], [94, 24, 162, 45, "_ref"], [94, 28, 162, 45], [94, 29, 162, 3, "iterations"], [94, 39, 162, 13], [95, 6, 162, 3, "iterations"], [95, 16, 162, 13], [95, 19, 162, 13, "_ref$iterations"], [95, 34, 162, 13], [95, 48, 162, 16], [95, 49, 162, 17], [95, 50, 162, 18], [95, 53, 162, 18, "_ref$iterations"], [95, 68, 162, 18], [96, 4, 164, 2], [96, 11, 164, 9, "emptyAnimation"], [96, 25, 164, 23], [97, 2, 165, 0], [97, 3, 165, 1], [98, 2, 165, 2], [98, 6, 165, 2, "_default"], [98, 14, 165, 2], [98, 17, 165, 2, "exports"], [98, 24, 165, 2], [98, 25, 165, 2, "default"], [98, 32, 165, 2], [98, 35, 169, 15], [99, 4, 170, 2, "Value"], [99, 9, 170, 7], [99, 11, 170, 9, "AnimatedValue"], [99, 33, 170, 22], [100, 4, 171, 2, "ValueXY"], [100, 11, 171, 9], [100, 13, 171, 11, "AnimatedValueXY"], [100, 37, 171, 26], [101, 4, 172, 2, "Color"], [101, 9, 172, 7], [101, 11, 172, 9, "AnimatedColor"], [101, 33, 172, 22], [102, 4, 173, 2, "Interpolation"], [102, 17, 173, 15], [102, 19, 173, 17, "AnimatedInterpolation"], [102, 49, 173, 38], [103, 4, 174, 2, "Node"], [103, 8, 174, 6], [103, 10, 174, 8, "AnimatedNode"], [103, 31, 174, 20], [104, 4, 175, 2, "decay"], [104, 9, 175, 7], [105, 4, 176, 2, "timing"], [105, 10, 176, 8], [106, 4, 177, 2, "spring"], [106, 10, 177, 8], [107, 4, 178, 2, "add"], [107, 7, 178, 5], [107, 9, 178, 7, "AnimatedImplementation"], [107, 40, 178, 29], [107, 41, 178, 30, "add"], [107, 44, 178, 33], [108, 4, 179, 2, "subtract"], [108, 12, 179, 10], [108, 14, 179, 12, "AnimatedImplementation"], [108, 45, 179, 34], [108, 46, 179, 35, "subtract"], [108, 54, 179, 43], [109, 4, 180, 2, "divide"], [109, 10, 180, 8], [109, 12, 180, 10, "AnimatedImplementation"], [109, 43, 180, 32], [109, 44, 180, 33, "divide"], [109, 50, 180, 39], [110, 4, 181, 2, "multiply"], [110, 12, 181, 10], [110, 14, 181, 12, "AnimatedImplementation"], [110, 45, 181, 34], [110, 46, 181, 35, "multiply"], [110, 54, 181, 43], [111, 4, 182, 2, "modulo"], [111, 10, 182, 8], [111, 12, 182, 10, "AnimatedImplementation"], [111, 43, 182, 32], [111, 44, 182, 33, "modulo"], [111, 50, 182, 39], [112, 4, 183, 2, "diffClamp"], [112, 13, 183, 11], [112, 15, 183, 13, "AnimatedImplementation"], [112, 46, 183, 35], [112, 47, 183, 36, "diffClamp"], [112, 56, 183, 45], [113, 4, 184, 2, "delay"], [113, 9, 184, 7], [114, 4, 185, 2, "sequence"], [114, 12, 185, 10], [115, 4, 186, 2, "parallel"], [115, 12, 186, 10], [116, 4, 187, 2, "stagger"], [116, 11, 187, 9], [117, 4, 188, 2, "loop"], [117, 8, 188, 6], [118, 4, 189, 2, "event"], [118, 9, 189, 7], [118, 11, 189, 9, "AnimatedImplementation"], [118, 42, 189, 31], [118, 43, 189, 32, "event"], [118, 48, 189, 37], [119, 4, 190, 2, "createAnimatedComponent"], [119, 27, 190, 25], [119, 29, 190, 2, "createAnimatedComponent"], [119, 61, 190, 25], [120, 4, 191, 2, "attachNativeEvent"], [120, 21, 191, 19], [120, 23, 191, 2, "attachNativeEvent"], [120, 55, 191, 19], [121, 4, 192, 2, "forkEvent"], [121, 13, 192, 11], [121, 15, 192, 13, "AnimatedImplementation"], [121, 46, 192, 35], [121, 47, 192, 36, "forkEvent"], [121, 56, 192, 45], [122, 4, 193, 2, "unforkEvent"], [122, 15, 193, 13], [122, 17, 193, 15, "AnimatedImplementation"], [122, 48, 193, 37], [122, 49, 193, 38, "unforkEvent"], [122, 60, 193, 49], [123, 4, 194, 2, "Event"], [123, 9, 194, 7], [123, 11, 194, 9, "AnimatedEvent"], [124, 2, 195, 0], [124, 3, 195, 1], [125, 0, 195, 1], [125, 3]], "functionMap": {"names": ["<global>", "mockAnimationStart", "<anonymous>", "emptyAnimation.start", "emptyAnimation.stop", "emptyAnimation.reset", "emptyAnimation._startNativeLoop", "emptyAnimation._isUsingNativeDriver", "mockCompositeAnimation", "mockAnimationStart$argument_0", "animations.forEach$argument_0", "spring", "timing", "decay", "sequence", "parallel", "delay", "stagger", "loop"], "mappings": "AAA;ACsC;SCG;GDmB;CDC;SGY,QH;QIC,QJ;SKC,QL;oBMC,QN;wBOC;GPE;+BQG;4BCI;uBCC,8BD;GDE;ERC;eWE;8BFO;KEG;CXE;eYE;8BHO;KGG;CZE;caE;CbK;iBcE;CdI;iBeG;CfK;cgBE;ChBE;gBiBE;CjBK;akBQ;ClBM"}}, "type": "js/module"}]}