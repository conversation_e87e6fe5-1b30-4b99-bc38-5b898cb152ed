{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 27}, "end": {"line": 2, "column": 60, "index": 87}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 88}, "end": {"line": 3, "column": 67, "index": 155}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 156}, "end": {"line": 4, "column": 52, "index": 208}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "./useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 209}, "end": {"line": 5, "column": 40, "index": 249}}], "key": "qBoul5KQ1+OEu8G3Sr6Tlb7g7CM=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = Header;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[3], \"react-native-safe-area-context\");\n  var _lucideReactNative = require(_dependencyMap[4], \"lucide-react-native\");\n  var _useColors = require(_dependencyMap[5], \"./useColors\");\n  var _jsxDevRuntime = require(_dependencyMap[6], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/components/Header.jsx\",\n    _s = $RefreshSig$();\n  function Header(_ref) {\n    _s();\n    var voiceMode = _ref.voiceMode,\n      onToggleVoiceMode = _ref.onToggleVoiceMode,\n      onDone = _ref.onDone;\n    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    var colors = (0, _useColors.useColors)();\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        paddingTop: insets.top + 16,\n        paddingHorizontal: 20,\n        paddingBottom: 16,\n        backgroundColor: colors.background,\n        borderBottomWidth: 1,\n        borderBottomColor: colors.outline,\n        flexDirection: 'row',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n        onPress: onToggleVoiceMode,\n        style: {\n          flexDirection: 'row',\n          alignItems: 'center',\n          backgroundColor: colors.primary,\n          paddingHorizontal: 12,\n          paddingVertical: 8,\n          borderRadius: 20,\n          gap: 6\n        },\n        children: voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n            size: 18,\n            color: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 14,\n              fontFamily: 'Poppins_500Medium',\n              color: colors.background\n            },\n            children: \"Voice\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Keyboard, {\n            size: 18,\n            color: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 14,\n              fontFamily: 'Poppins_500Medium',\n              color: colors.background\n            },\n            children: \"Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n        style: {\n          fontSize: 20,\n          fontFamily: 'Poppins_600SemiBold',\n          color: colors.text\n        },\n        children: \"Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n        onPress: onDone,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n          style: {\n            fontSize: 16,\n            fontFamily: 'Poppins_500Medium',\n            color: colors.primary\n          },\n          children: \"Done\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 5\n    }, this);\n  }\n  _s(Header, \"U5bwvIWPYnlnYlH7m0XAXWjwn7Q=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors];\n  });\n  _c = Header;\n  var _c;\n  $RefreshReg$(_c, \"Header\");\n});", "lineCount": 132, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_reactNativeSafeAreaContext"], [9, 33, 3, 0], [9, 36, 3, 0, "require"], [9, 43, 3, 0], [9, 44, 3, 0, "_dependencyMap"], [9, 58, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_lucideReactNative"], [10, 24, 4, 0], [10, 27, 4, 0, "require"], [10, 34, 4, 0], [10, 35, 4, 0, "_dependencyMap"], [10, 49, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_useColors"], [11, 16, 5, 0], [11, 19, 5, 0, "require"], [11, 26, 5, 0], [11, 27, 5, 0, "_dependencyMap"], [11, 41, 5, 0], [12, 2, 5, 40], [12, 6, 5, 40, "_jsxDevRuntime"], [12, 20, 5, 40], [12, 23, 5, 40, "require"], [12, 30, 5, 40], [12, 31, 5, 40, "_dependencyMap"], [12, 45, 5, 40], [13, 2, 5, 40], [13, 6, 5, 40, "_jsxFileName"], [13, 18, 5, 40], [14, 4, 5, 40, "_s"], [14, 6, 5, 40], [14, 9, 5, 40, "$RefreshSig$"], [14, 21, 5, 40], [15, 2, 7, 15], [15, 11, 7, 24, "Header"], [15, 17, 7, 30, "Header"], [15, 18, 7, 30, "_ref"], [15, 22, 7, 30], [15, 24, 7, 73], [16, 4, 7, 73, "_s"], [16, 6, 7, 73], [17, 4, 7, 73], [17, 8, 7, 33, "voiceMode"], [17, 17, 7, 42], [17, 20, 7, 42, "_ref"], [17, 24, 7, 42], [17, 25, 7, 33, "voiceMode"], [17, 34, 7, 42], [18, 6, 7, 44, "onToggleVoiceMode"], [18, 23, 7, 61], [18, 26, 7, 61, "_ref"], [18, 30, 7, 61], [18, 31, 7, 44, "onToggleVoiceMode"], [18, 48, 7, 61], [19, 6, 7, 63, "onDone"], [19, 12, 7, 69], [19, 15, 7, 69, "_ref"], [19, 19, 7, 69], [19, 20, 7, 63, "onDone"], [19, 26, 7, 69], [20, 4, 8, 2], [20, 8, 8, 8, "insets"], [20, 14, 8, 14], [20, 17, 8, 17], [20, 21, 8, 17, "useSafeAreaInsets"], [20, 66, 8, 34], [20, 68, 8, 35], [20, 69, 8, 36], [21, 4, 9, 2], [21, 8, 9, 8, "colors"], [21, 14, 9, 14], [21, 17, 9, 17], [21, 21, 9, 17, "useColors"], [21, 41, 9, 26], [21, 43, 9, 27], [21, 44, 9, 28], [22, 4, 11, 2], [22, 24, 12, 4], [22, 28, 12, 4, "_jsxDevRuntime"], [22, 42, 12, 4], [22, 43, 12, 4, "jsxDEV"], [22, 49, 12, 4], [22, 51, 12, 5, "_reactNative"], [22, 63, 12, 5], [22, 64, 12, 5, "View"], [22, 68, 12, 9], [23, 6, 13, 6, "style"], [23, 11, 13, 11], [23, 13, 13, 13], [24, 8, 14, 8, "paddingTop"], [24, 18, 14, 18], [24, 20, 14, 20, "insets"], [24, 26, 14, 26], [24, 27, 14, 27, "top"], [24, 30, 14, 30], [24, 33, 14, 33], [24, 35, 14, 35], [25, 8, 15, 8, "paddingHorizontal"], [25, 25, 15, 25], [25, 27, 15, 27], [25, 29, 15, 29], [26, 8, 16, 8, "paddingBottom"], [26, 21, 16, 21], [26, 23, 16, 23], [26, 25, 16, 25], [27, 8, 17, 8, "backgroundColor"], [27, 23, 17, 23], [27, 25, 17, 25, "colors"], [27, 31, 17, 31], [27, 32, 17, 32, "background"], [27, 42, 17, 42], [28, 8, 18, 8, "borderBottomWidth"], [28, 25, 18, 25], [28, 27, 18, 27], [28, 28, 18, 28], [29, 8, 19, 8, "borderBottomColor"], [29, 25, 19, 25], [29, 27, 19, 27, "colors"], [29, 33, 19, 33], [29, 34, 19, 34, "outline"], [29, 41, 19, 41], [30, 8, 20, 8, "flexDirection"], [30, 21, 20, 21], [30, 23, 20, 23], [30, 28, 20, 28], [31, 8, 21, 8, "alignItems"], [31, 18, 21, 18], [31, 20, 21, 20], [31, 28, 21, 28], [32, 8, 22, 8, "justifyContent"], [32, 22, 22, 22], [32, 24, 22, 24], [33, 6, 23, 6], [33, 7, 23, 8], [34, 6, 23, 8, "children"], [34, 14, 23, 8], [34, 30, 25, 6], [34, 34, 25, 6, "_jsxDevRuntime"], [34, 48, 25, 6], [34, 49, 25, 6, "jsxDEV"], [34, 55, 25, 6], [34, 57, 25, 7, "_reactNative"], [34, 69, 25, 7], [34, 70, 25, 7, "TouchableOpacity"], [34, 86, 25, 23], [35, 8, 26, 8, "onPress"], [35, 15, 26, 15], [35, 17, 26, 17, "onToggleVoiceMode"], [35, 34, 26, 35], [36, 8, 27, 8, "style"], [36, 13, 27, 13], [36, 15, 27, 15], [37, 10, 28, 10, "flexDirection"], [37, 23, 28, 23], [37, 25, 28, 25], [37, 30, 28, 30], [38, 10, 29, 10, "alignItems"], [38, 20, 29, 20], [38, 22, 29, 22], [38, 30, 29, 30], [39, 10, 30, 10, "backgroundColor"], [39, 25, 30, 25], [39, 27, 30, 27, "colors"], [39, 33, 30, 33], [39, 34, 30, 34, "primary"], [39, 41, 30, 41], [40, 10, 31, 10, "paddingHorizontal"], [40, 27, 31, 27], [40, 29, 31, 29], [40, 31, 31, 31], [41, 10, 32, 10, "paddingVertical"], [41, 25, 32, 25], [41, 27, 32, 27], [41, 28, 32, 28], [42, 10, 33, 10, "borderRadius"], [42, 22, 33, 22], [42, 24, 33, 24], [42, 26, 33, 26], [43, 10, 34, 10, "gap"], [43, 13, 34, 13], [43, 15, 34, 15], [44, 8, 35, 8], [44, 9, 35, 10], [45, 8, 35, 10, "children"], [45, 16, 35, 10], [45, 18, 37, 9, "voiceMode"], [45, 27, 37, 18], [45, 43, 38, 10], [45, 47, 38, 10, "_jsxDevRuntime"], [45, 61, 38, 10], [45, 62, 38, 10, "jsxDEV"], [45, 68, 38, 10], [45, 70, 38, 10, "_jsxDevRuntime"], [45, 84, 38, 10], [45, 85, 38, 10, "Fragment"], [45, 93, 38, 10], [46, 10, 38, 10, "children"], [46, 18, 38, 10], [46, 34, 39, 12], [46, 38, 39, 12, "_jsxDevRuntime"], [46, 52, 39, 12], [46, 53, 39, 12, "jsxDEV"], [46, 59, 39, 12], [46, 61, 39, 13, "_lucideReactNative"], [46, 79, 39, 13], [46, 80, 39, 13, "Mic"], [46, 83, 39, 16], [47, 12, 39, 17, "size"], [47, 16, 39, 21], [47, 18, 39, 23], [47, 20, 39, 26], [48, 12, 39, 27, "color"], [48, 17, 39, 32], [48, 19, 39, 34, "colors"], [48, 25, 39, 40], [48, 26, 39, 41, "background"], [49, 10, 39, 52], [50, 12, 39, 52, "fileName"], [50, 20, 39, 52], [50, 22, 39, 52, "_jsxFileName"], [50, 34, 39, 52], [51, 12, 39, 52, "lineNumber"], [51, 22, 39, 52], [52, 12, 39, 52, "columnNumber"], [52, 24, 39, 52], [53, 10, 39, 52], [53, 17, 39, 54], [53, 18, 39, 55], [53, 33, 40, 12], [53, 37, 40, 12, "_jsxDevRuntime"], [53, 51, 40, 12], [53, 52, 40, 12, "jsxDEV"], [53, 58, 40, 12], [53, 60, 40, 13, "_reactNative"], [53, 72, 40, 13], [53, 73, 40, 13, "Text"], [53, 77, 40, 17], [54, 12, 40, 18, "style"], [54, 17, 40, 23], [54, 19, 40, 25], [55, 14, 41, 14, "fontSize"], [55, 22, 41, 22], [55, 24, 41, 24], [55, 26, 41, 26], [56, 14, 42, 14, "fontFamily"], [56, 24, 42, 24], [56, 26, 42, 26], [56, 45, 42, 45], [57, 14, 43, 14, "color"], [57, 19, 43, 19], [57, 21, 43, 21, "colors"], [57, 27, 43, 27], [57, 28, 43, 28, "background"], [58, 12, 44, 12], [58, 13, 44, 14], [59, 12, 44, 14, "children"], [59, 20, 44, 14], [59, 22, 44, 15], [60, 10, 46, 12], [61, 12, 46, 12, "fileName"], [61, 20, 46, 12], [61, 22, 46, 12, "_jsxFileName"], [61, 34, 46, 12], [62, 12, 46, 12, "lineNumber"], [62, 22, 46, 12], [63, 12, 46, 12, "columnNumber"], [63, 24, 46, 12], [64, 10, 46, 12], [64, 17, 46, 18], [64, 18, 46, 19], [65, 8, 46, 19], [65, 23, 47, 12], [65, 24, 47, 13], [65, 40, 49, 10], [65, 44, 49, 10, "_jsxDevRuntime"], [65, 58, 49, 10], [65, 59, 49, 10, "jsxDEV"], [65, 65, 49, 10], [65, 67, 49, 10, "_jsxDevRuntime"], [65, 81, 49, 10], [65, 82, 49, 10, "Fragment"], [65, 90, 49, 10], [66, 10, 49, 10, "children"], [66, 18, 49, 10], [66, 34, 50, 12], [66, 38, 50, 12, "_jsxDevRuntime"], [66, 52, 50, 12], [66, 53, 50, 12, "jsxDEV"], [66, 59, 50, 12], [66, 61, 50, 13, "_lucideReactNative"], [66, 79, 50, 13], [66, 80, 50, 13, "Keyboard"], [66, 88, 50, 21], [67, 12, 50, 22, "size"], [67, 16, 50, 26], [67, 18, 50, 28], [67, 20, 50, 31], [68, 12, 50, 32, "color"], [68, 17, 50, 37], [68, 19, 50, 39, "colors"], [68, 25, 50, 45], [68, 26, 50, 46, "background"], [69, 10, 50, 57], [70, 12, 50, 57, "fileName"], [70, 20, 50, 57], [70, 22, 50, 57, "_jsxFileName"], [70, 34, 50, 57], [71, 12, 50, 57, "lineNumber"], [71, 22, 50, 57], [72, 12, 50, 57, "columnNumber"], [72, 24, 50, 57], [73, 10, 50, 57], [73, 17, 50, 59], [73, 18, 50, 60], [73, 33, 51, 12], [73, 37, 51, 12, "_jsxDevRuntime"], [73, 51, 51, 12], [73, 52, 51, 12, "jsxDEV"], [73, 58, 51, 12], [73, 60, 51, 13, "_reactNative"], [73, 72, 51, 13], [73, 73, 51, 13, "Text"], [73, 77, 51, 17], [74, 12, 51, 18, "style"], [74, 17, 51, 23], [74, 19, 51, 25], [75, 14, 52, 14, "fontSize"], [75, 22, 52, 22], [75, 24, 52, 24], [75, 26, 52, 26], [76, 14, 53, 14, "fontFamily"], [76, 24, 53, 24], [76, 26, 53, 26], [76, 45, 53, 45], [77, 14, 54, 14, "color"], [77, 19, 54, 19], [77, 21, 54, 21, "colors"], [77, 27, 54, 27], [77, 28, 54, 28, "background"], [78, 12, 55, 12], [78, 13, 55, 14], [79, 12, 55, 14, "children"], [79, 20, 55, 14], [79, 22, 55, 15], [80, 10, 57, 12], [81, 12, 57, 12, "fileName"], [81, 20, 57, 12], [81, 22, 57, 12, "_jsxFileName"], [81, 34, 57, 12], [82, 12, 57, 12, "lineNumber"], [82, 22, 57, 12], [83, 12, 57, 12, "columnNumber"], [83, 24, 57, 12], [84, 10, 57, 12], [84, 17, 57, 18], [84, 18, 57, 19], [85, 8, 57, 19], [85, 23, 58, 12], [86, 6, 59, 9], [87, 8, 59, 9, "fileName"], [87, 16, 59, 9], [87, 18, 59, 9, "_jsxFileName"], [87, 30, 59, 9], [88, 8, 59, 9, "lineNumber"], [88, 18, 59, 9], [89, 8, 59, 9, "columnNumber"], [89, 20, 59, 9], [90, 6, 59, 9], [90, 13, 60, 24], [90, 14, 60, 25], [90, 29, 61, 6], [90, 33, 61, 6, "_jsxDevRuntime"], [90, 47, 61, 6], [90, 48, 61, 6, "jsxDEV"], [90, 54, 61, 6], [90, 56, 61, 7, "_reactNative"], [90, 68, 61, 7], [90, 69, 61, 7, "Text"], [90, 73, 61, 11], [91, 8, 62, 8, "style"], [91, 13, 62, 13], [91, 15, 62, 15], [92, 10, 63, 10, "fontSize"], [92, 18, 63, 18], [92, 20, 63, 20], [92, 22, 63, 22], [93, 10, 64, 10, "fontFamily"], [93, 20, 64, 20], [93, 22, 64, 22], [93, 43, 64, 43], [94, 10, 65, 10, "color"], [94, 15, 65, 15], [94, 17, 65, 17, "colors"], [94, 23, 65, 23], [94, 24, 65, 24, "text"], [95, 8, 66, 8], [95, 9, 66, 10], [96, 8, 66, 10, "children"], [96, 16, 66, 10], [96, 18, 67, 7], [97, 6, 69, 6], [98, 8, 69, 6, "fileName"], [98, 16, 69, 6], [98, 18, 69, 6, "_jsxFileName"], [98, 30, 69, 6], [99, 8, 69, 6, "lineNumber"], [99, 18, 69, 6], [100, 8, 69, 6, "columnNumber"], [100, 20, 69, 6], [101, 6, 69, 6], [101, 13, 69, 12], [101, 14, 69, 13], [101, 29, 70, 6], [101, 33, 70, 6, "_jsxDevRuntime"], [101, 47, 70, 6], [101, 48, 70, 6, "jsxDEV"], [101, 54, 70, 6], [101, 56, 70, 7, "_reactNative"], [101, 68, 70, 7], [101, 69, 70, 7, "TouchableOpacity"], [101, 85, 70, 23], [102, 8, 70, 24, "onPress"], [102, 15, 70, 31], [102, 17, 70, 33, "onDone"], [102, 23, 70, 40], [103, 8, 70, 40, "children"], [103, 16, 70, 40], [103, 31, 71, 8], [103, 35, 71, 8, "_jsxDevRuntime"], [103, 49, 71, 8], [103, 50, 71, 8, "jsxDEV"], [103, 56, 71, 8], [103, 58, 71, 9, "_reactNative"], [103, 70, 71, 9], [103, 71, 71, 9, "Text"], [103, 75, 71, 13], [104, 10, 72, 10, "style"], [104, 15, 72, 15], [104, 17, 72, 17], [105, 12, 73, 12, "fontSize"], [105, 20, 73, 20], [105, 22, 73, 22], [105, 24, 73, 24], [106, 12, 74, 12, "fontFamily"], [106, 22, 74, 22], [106, 24, 74, 24], [106, 43, 74, 43], [107, 12, 75, 12, "color"], [107, 17, 75, 17], [107, 19, 75, 19, "colors"], [107, 25, 75, 25], [107, 26, 75, 26, "primary"], [108, 10, 76, 10], [108, 11, 76, 12], [109, 10, 76, 12, "children"], [109, 18, 76, 12], [109, 20, 77, 9], [110, 8, 79, 8], [111, 10, 79, 8, "fileName"], [111, 18, 79, 8], [111, 20, 79, 8, "_jsxFileName"], [111, 32, 79, 8], [112, 10, 79, 8, "lineNumber"], [112, 20, 79, 8], [113, 10, 79, 8, "columnNumber"], [113, 22, 79, 8], [114, 8, 79, 8], [114, 15, 79, 14], [115, 6, 79, 15], [116, 8, 79, 15, "fileName"], [116, 16, 79, 15], [116, 18, 79, 15, "_jsxFileName"], [116, 30, 79, 15], [117, 8, 79, 15, "lineNumber"], [117, 18, 79, 15], [118, 8, 79, 15, "columnNumber"], [118, 20, 79, 15], [119, 6, 79, 15], [119, 13, 80, 24], [119, 14, 80, 25], [120, 4, 80, 25], [121, 6, 80, 25, "fileName"], [121, 14, 80, 25], [121, 16, 80, 25, "_jsxFileName"], [121, 28, 80, 25], [122, 6, 80, 25, "lineNumber"], [122, 16, 80, 25], [123, 6, 80, 25, "columnNumber"], [123, 18, 80, 25], [124, 4, 80, 25], [124, 11, 81, 10], [124, 12, 81, 11], [125, 2, 83, 0], [126, 2, 83, 1, "_s"], [126, 4, 83, 1], [126, 5, 7, 24, "Header"], [126, 11, 7, 30], [127, 4, 7, 30], [127, 12, 8, 17, "useSafeAreaInsets"], [127, 57, 8, 34], [127, 59, 9, 17, "useColors"], [127, 79, 9, 26], [128, 2, 9, 26], [129, 2, 9, 26, "_c"], [129, 4, 9, 26], [129, 7, 7, 24, "Header"], [129, 13, 7, 30], [130, 2, 7, 30], [130, 6, 7, 30, "_c"], [130, 8, 7, 30], [131, 2, 7, 30, "$RefreshReg$"], [131, 14, 7, 30], [131, 15, 7, 30, "_c"], [131, 17, 7, 30], [132, 0, 7, 30], [132, 3]], "functionMap": {"names": ["<global>", "Header"], "mappings": "AAA;eCM;CD4E"}}, "type": "js/module"}]}