{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FadingTransition = FadingTransition;\n  function FadingTransition(name, transitionData) {\n    const {\n      translateX,\n      translateY,\n      scaleX,\n      scaleY\n    } = transitionData;\n    const fadingTransition = {\n      name,\n      style: {\n        0: {\n          opacity: 1,\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        20: {\n          opacity: 0,\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        60: {\n          opacity: 0,\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: `1,1`\n          }]\n        },\n        100: {\n          opacity: 1,\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: `1,1`\n          }]\n        }\n      },\n      duration: 300\n    };\n    return fadingTransition;\n  }\n});", "lineCount": 55, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "FadingTransition"], [7, 26, 1, 13], [7, 29, 1, 13, "FadingTransition"], [7, 45, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "FadingTransition"], [8, 27, 3, 32, "FadingTransition"], [8, 28, 3, 33, "name"], [8, 32, 3, 37], [8, 34, 3, 39, "transitionData"], [8, 48, 3, 53], [8, 50, 3, 55], [9, 4, 4, 2], [9, 10, 4, 8], [10, 6, 5, 4, "translateX"], [10, 16, 5, 14], [11, 6, 6, 4, "translateY"], [11, 16, 6, 14], [12, 6, 7, 4, "scaleX"], [12, 12, 7, 10], [13, 6, 8, 4, "scaleY"], [14, 4, 9, 2], [14, 5, 9, 3], [14, 8, 9, 6, "transitionData"], [14, 22, 9, 20], [15, 4, 10, 2], [15, 10, 10, 8, "fadingTransition"], [15, 26, 10, 24], [15, 29, 10, 27], [16, 6, 11, 4, "name"], [16, 10, 11, 8], [17, 6, 12, 4, "style"], [17, 11, 12, 9], [17, 13, 12, 11], [18, 8, 13, 6], [18, 9, 13, 7], [18, 11, 13, 9], [19, 10, 14, 8, "opacity"], [19, 17, 14, 15], [19, 19, 14, 17], [19, 20, 14, 18], [20, 10, 15, 8, "transform"], [20, 19, 15, 17], [20, 21, 15, 19], [20, 22, 15, 20], [21, 12, 16, 10, "translateX"], [21, 22, 16, 20], [21, 24, 16, 22], [21, 27, 16, 25, "translateX"], [21, 37, 16, 35], [21, 41, 16, 39], [22, 12, 17, 10, "translateY"], [22, 22, 17, 20], [22, 24, 17, 22], [22, 27, 17, 25, "translateY"], [22, 37, 17, 35], [22, 41, 17, 39], [23, 12, 18, 10, "scale"], [23, 17, 18, 15], [23, 19, 18, 17], [23, 22, 18, 20, "scaleX"], [23, 28, 18, 26], [23, 32, 18, 30, "scaleY"], [23, 38, 18, 36], [24, 10, 19, 8], [24, 11, 19, 9], [25, 8, 20, 6], [25, 9, 20, 7], [26, 8, 21, 6], [26, 10, 21, 8], [26, 12, 21, 10], [27, 10, 22, 8, "opacity"], [27, 17, 22, 15], [27, 19, 22, 17], [27, 20, 22, 18], [28, 10, 23, 8, "transform"], [28, 19, 23, 17], [28, 21, 23, 19], [28, 22, 23, 20], [29, 12, 24, 10, "translateX"], [29, 22, 24, 20], [29, 24, 24, 22], [29, 27, 24, 25, "translateX"], [29, 37, 24, 35], [29, 41, 24, 39], [30, 12, 25, 10, "translateY"], [30, 22, 25, 20], [30, 24, 25, 22], [30, 27, 25, 25, "translateY"], [30, 37, 25, 35], [30, 41, 25, 39], [31, 12, 26, 10, "scale"], [31, 17, 26, 15], [31, 19, 26, 17], [31, 22, 26, 20, "scaleX"], [31, 28, 26, 26], [31, 32, 26, 30, "scaleY"], [31, 38, 26, 36], [32, 10, 27, 8], [32, 11, 27, 9], [33, 8, 28, 6], [33, 9, 28, 7], [34, 8, 29, 6], [34, 10, 29, 8], [34, 12, 29, 10], [35, 10, 30, 8, "opacity"], [35, 17, 30, 15], [35, 19, 30, 17], [35, 20, 30, 18], [36, 10, 31, 8, "transform"], [36, 19, 31, 17], [36, 21, 31, 19], [36, 22, 31, 20], [37, 12, 32, 10, "translateX"], [37, 22, 32, 20], [37, 24, 32, 22], [37, 29, 32, 27], [38, 12, 33, 10, "translateY"], [38, 22, 33, 20], [38, 24, 33, 22], [38, 29, 33, 27], [39, 12, 34, 10, "scale"], [39, 17, 34, 15], [39, 19, 34, 17], [40, 10, 35, 8], [40, 11, 35, 9], [41, 8, 36, 6], [41, 9, 36, 7], [42, 8, 37, 6], [42, 11, 37, 9], [42, 13, 37, 11], [43, 10, 38, 8, "opacity"], [43, 17, 38, 15], [43, 19, 38, 17], [43, 20, 38, 18], [44, 10, 39, 8, "transform"], [44, 19, 39, 17], [44, 21, 39, 19], [44, 22, 39, 20], [45, 12, 40, 10, "translateX"], [45, 22, 40, 20], [45, 24, 40, 22], [45, 29, 40, 27], [46, 12, 41, 10, "translateY"], [46, 22, 41, 20], [46, 24, 41, 22], [46, 29, 41, 27], [47, 12, 42, 10, "scale"], [47, 17, 42, 15], [47, 19, 42, 17], [48, 10, 43, 8], [48, 11, 43, 9], [49, 8, 44, 6], [50, 6, 45, 4], [50, 7, 45, 5], [51, 6, 46, 4, "duration"], [51, 14, 46, 12], [51, 16, 46, 14], [52, 4, 47, 2], [52, 5, 47, 3], [53, 4, 48, 2], [53, 11, 48, 9, "fadingTransition"], [53, 27, 48, 25], [54, 2, 49, 0], [55, 0, 49, 1], [55, 3]], "functionMap": {"names": ["<global>", "FadingTransition"], "mappings": "AAA;OCE;CD8C"}}, "type": "js/module"}]}