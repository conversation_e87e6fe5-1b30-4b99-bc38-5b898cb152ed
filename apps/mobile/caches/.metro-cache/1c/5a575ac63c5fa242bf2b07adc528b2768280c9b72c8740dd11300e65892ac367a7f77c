{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./extractFill", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "wsh+qF5g/L+CGLymExOgsaz5RXE=", "exportNames": ["*"]}}, {"name": "./extractStroke", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 44, "index": 85}}], "key": "Y7kYnEqz/PO23k3Y0TkdoRHvAXI=", "exportNames": ["*"]}}, {"name": "./extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 86}, "end": {"line": 3, "column": 50, "index": 136}}], "key": "/eFg4kip4DvH5s9LoYzzDzsUESE=", "exportNames": ["*"]}}, {"name": "./extractResponder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 137}, "end": {"line": 4, "column": 50, "index": 187}}], "key": "QcseeXEXN3nbs4VNErUJNMeZQLE=", "exportNames": ["*"]}}, {"name": "./extractOpacity", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 188}, "end": {"line": 5, "column": 46, "index": 234}}], "key": "jjgYCrYDewRsVLpQaklu9NyKLaY=", "exportNames": ["*"]}}, {"name": "../util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 235}, "end": {"line": 6, "column": 36, "index": 271}}], "key": "DTmef1GE5grNQfsvpIzpBlINy9c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = extractProps;\n  exports.extract = extract;\n  exports.propsAndStyles = propsAndStyles;\n  exports.withoutXY = withoutXY;\n  var _extractFill = _interopRequireDefault(require(_dependencyMap[1], \"./extractFill\"));\n  var _extractStroke = _interopRequireDefault(require(_dependencyMap[2], \"./extractStroke\"));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[3], \"./extractTransform\"));\n  var _extractResponder = _interopRequireDefault(require(_dependencyMap[4], \"./extractResponder\"));\n  var _extractOpacity = _interopRequireDefault(require(_dependencyMap[5], \"./extractOpacity\"));\n  var _util = require(_dependencyMap[6], \"../util\");\n  var clipRules = {\n    evenodd: 0,\n    nonzero: 1\n  };\n  function propsAndStyles(props) {\n    var style = props.style;\n    return !style ? props : {\n      ...(Array.isArray(style) ? Object.assign({}, ...style) : style),\n      ...props\n    };\n  }\n  function getMarker(marker) {\n    if (!marker) {\n      return undefined;\n    }\n    var matched = marker.match(_util.idPattern);\n    return matched ? matched[1] : undefined;\n  }\n  function extractProps(props, ref) {\n    var id = props.id,\n      opacity = props.opacity,\n      onLayout = props.onLayout,\n      clipPath = props.clipPath,\n      clipRule = props.clipRule,\n      display = props.display,\n      mask = props.mask,\n      filter = props.filter,\n      marker = props.marker,\n      _props$markerStart = props.markerStart,\n      markerStart = _props$markerStart === void 0 ? marker : _props$markerStart,\n      _props$markerMid = props.markerMid,\n      markerMid = _props$markerMid === void 0 ? marker : _props$markerMid,\n      _props$markerEnd = props.markerEnd,\n      markerEnd = _props$markerEnd === void 0 ? marker : _props$markerEnd,\n      testID = props.testID,\n      accessibilityLabel = props.accessibilityLabel,\n      accessible = props.accessible;\n    var extracted = {};\n    var inherited = [];\n    (0, _extractResponder.default)(extracted, props, ref);\n    (0, _extractFill.default)(extracted, props, inherited);\n    (0, _extractStroke.default)(extracted, props, inherited);\n    if (props.color) {\n      extracted.color = props.color;\n    }\n    if (inherited.length) {\n      extracted.propList = inherited;\n    }\n    var matrix = (0, _extractTransform.default)(props);\n    if (matrix !== null) {\n      extracted.matrix = matrix;\n    }\n    if (opacity != null) {\n      extracted.opacity = (0, _extractOpacity.default)(opacity);\n    }\n    if (display != null) {\n      extracted.display = display === 'none' ? 'none' : undefined;\n    }\n    if (onLayout) {\n      extracted.onLayout = onLayout;\n    }\n    if (markerStart) {\n      extracted.markerStart = getMarker(markerStart);\n    }\n    if (markerMid) {\n      extracted.markerMid = getMarker(markerMid);\n    }\n    if (markerEnd) {\n      extracted.markerEnd = getMarker(markerEnd);\n    }\n    if (id) {\n      extracted.name = String(id);\n    }\n    if (testID) {\n      extracted.testID = testID;\n    }\n    if (accessibilityLabel) {\n      extracted.accessibilityLabel = accessibilityLabel;\n    }\n    if (accessible) {\n      extracted.accessible = accessible;\n    }\n    if (clipRule) {\n      extracted.clipRule = clipRules[clipRule] === 0 ? 0 : 1;\n    }\n    if (clipPath) {\n      var matched = clipPath.match(_util.idPattern);\n      if (matched) {\n        extracted.clipPath = matched[1];\n      } else {\n        console.warn('Invalid `clipPath` prop, expected a clipPath like \"#id\", but got: \"' + clipPath + '\"');\n      }\n    }\n    if (mask) {\n      var _matched = mask.match(_util.idPattern);\n      if (_matched) {\n        extracted.mask = _matched[1];\n      } else {\n        console.warn('Invalid `mask` prop, expected a mask like \"#id\", but got: \"' + mask + '\"');\n      }\n    }\n    if (filter) {\n      var _matched2 = filter.match(_util.idPattern);\n      if (_matched2) {\n        extracted.filter = _matched2[1];\n      } else {\n        console.warn('Invalid `filter` prop, expected a filter like \"#id\", but got: \"' + filter + '\"');\n      }\n    }\n    return extracted;\n  }\n  function extract(instance, props) {\n    return extractProps(propsAndStyles(props), instance);\n  }\n  function withoutXY(instance, props) {\n    return extractProps({\n      ...propsAndStyles(props),\n      x: null,\n      y: null\n    }, instance);\n  }\n});", "lineCount": 137, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_extractFill"], [10, 18, 1, 0], [10, 21, 1, 0, "_interopRequireDefault"], [10, 43, 1, 0], [10, 44, 1, 0, "require"], [10, 51, 1, 0], [10, 52, 1, 0, "_dependencyMap"], [10, 66, 1, 0], [11, 2, 2, 0], [11, 6, 2, 0, "_extractStroke"], [11, 20, 2, 0], [11, 23, 2, 0, "_interopRequireDefault"], [11, 45, 2, 0], [11, 46, 2, 0, "require"], [11, 53, 2, 0], [11, 54, 2, 0, "_dependencyMap"], [11, 68, 2, 0], [12, 2, 3, 0], [12, 6, 3, 0, "_extractTransform"], [12, 23, 3, 0], [12, 26, 3, 0, "_interopRequireDefault"], [12, 48, 3, 0], [12, 49, 3, 0, "require"], [12, 56, 3, 0], [12, 57, 3, 0, "_dependencyMap"], [12, 71, 3, 0], [13, 2, 4, 0], [13, 6, 4, 0, "_extractResponder"], [13, 23, 4, 0], [13, 26, 4, 0, "_interopRequireDefault"], [13, 48, 4, 0], [13, 49, 4, 0, "require"], [13, 56, 4, 0], [13, 57, 4, 0, "_dependencyMap"], [13, 71, 4, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_extractOpacity"], [14, 21, 5, 0], [14, 24, 5, 0, "_interopRequireDefault"], [14, 46, 5, 0], [14, 47, 5, 0, "require"], [14, 54, 5, 0], [14, 55, 5, 0, "_dependencyMap"], [14, 69, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_util"], [15, 11, 6, 0], [15, 14, 6, 0, "require"], [15, 21, 6, 0], [15, 22, 6, 0, "_dependencyMap"], [15, 36, 6, 0], [16, 2, 18, 0], [16, 6, 18, 6, "clipRules"], [16, 15, 18, 53], [16, 18, 18, 56], [17, 4, 19, 2, "evenodd"], [17, 11, 19, 9], [17, 13, 19, 11], [17, 14, 19, 12], [18, 4, 20, 2, "nonzero"], [18, 11, 20, 9], [18, 13, 20, 11], [19, 2, 21, 0], [19, 3, 21, 1], [20, 2, 23, 7], [20, 11, 23, 16, "propsAndStyles"], [20, 25, 23, 30, "propsAndStyles"], [20, 26, 23, 31, "props"], [20, 31, 23, 71], [20, 33, 23, 73], [21, 4, 24, 2], [21, 8, 24, 10, "style"], [21, 13, 24, 15], [21, 16, 24, 20, "props"], [21, 21, 24, 25], [21, 22, 24, 10, "style"], [21, 27, 24, 15], [22, 4, 25, 2], [22, 11, 25, 9], [22, 12, 25, 10, "style"], [22, 17, 25, 15], [22, 20, 26, 6, "props"], [22, 25, 26, 11], [22, 28, 27, 6], [23, 6, 28, 8], [23, 10, 28, 12, "Array"], [23, 15, 28, 17], [23, 16, 28, 18, "isArray"], [23, 23, 28, 25], [23, 24, 28, 26, "style"], [23, 29, 28, 31], [23, 30, 28, 32], [23, 33, 28, 35, "Object"], [23, 39, 28, 41], [23, 40, 28, 42, "assign"], [23, 46, 28, 48], [23, 47, 28, 49], [23, 48, 28, 50], [23, 49, 28, 51], [23, 51, 28, 53], [23, 54, 28, 56, "style"], [23, 59, 28, 61], [23, 60, 28, 62], [23, 63, 28, 65, "style"], [23, 68, 28, 70], [23, 69, 28, 71], [24, 6, 29, 8], [24, 9, 29, 11, "props"], [25, 4, 30, 6], [25, 5, 30, 7], [26, 2, 31, 0], [27, 2, 33, 0], [27, 11, 33, 9, "<PERSON><PERSON><PERSON><PERSON>"], [27, 20, 33, 18, "<PERSON><PERSON><PERSON><PERSON>"], [27, 21, 33, 19, "marker"], [27, 27, 33, 34], [27, 29, 33, 36], [28, 4, 34, 2], [28, 8, 34, 6], [28, 9, 34, 7, "marker"], [28, 15, 34, 13], [28, 17, 34, 15], [29, 6, 35, 4], [29, 13, 35, 11, "undefined"], [29, 22, 35, 20], [30, 4, 36, 2], [31, 4, 37, 2], [31, 8, 37, 8, "matched"], [31, 15, 37, 15], [31, 18, 37, 18, "marker"], [31, 24, 37, 24], [31, 25, 37, 25, "match"], [31, 30, 37, 30], [31, 31, 37, 31, "idPattern"], [31, 46, 37, 40], [31, 47, 37, 41], [32, 4, 38, 2], [32, 11, 38, 9, "matched"], [32, 18, 38, 16], [32, 21, 38, 19, "matched"], [32, 28, 38, 26], [32, 29, 38, 27], [32, 30, 38, 28], [32, 31, 38, 29], [32, 34, 38, 32, "undefined"], [32, 43, 38, 41], [33, 2, 39, 0], [34, 2, 41, 15], [34, 11, 41, 24, "extractProps"], [34, 23, 41, 36, "extractProps"], [34, 24, 42, 2, "props"], [34, 29, 62, 13], [34, 31, 63, 2, "ref"], [34, 34, 63, 13], [34, 36, 64, 2], [35, 4, 65, 2], [35, 8, 66, 4, "id"], [35, 10, 66, 6], [35, 13, 81, 6, "props"], [35, 18, 81, 11], [35, 19, 66, 4, "id"], [35, 21, 66, 6], [36, 6, 67, 4, "opacity"], [36, 13, 67, 11], [36, 16, 81, 6, "props"], [36, 21, 81, 11], [36, 22, 67, 4, "opacity"], [36, 29, 67, 11], [37, 6, 68, 4, "onLayout"], [37, 14, 68, 12], [37, 17, 81, 6, "props"], [37, 22, 81, 11], [37, 23, 68, 4, "onLayout"], [37, 31, 68, 12], [38, 6, 69, 4, "clipPath"], [38, 14, 69, 12], [38, 17, 81, 6, "props"], [38, 22, 81, 11], [38, 23, 69, 4, "clipPath"], [38, 31, 69, 12], [39, 6, 70, 4, "clipRule"], [39, 14, 70, 12], [39, 17, 81, 6, "props"], [39, 22, 81, 11], [39, 23, 70, 4, "clipRule"], [39, 31, 70, 12], [40, 6, 71, 4, "display"], [40, 13, 71, 11], [40, 16, 81, 6, "props"], [40, 21, 81, 11], [40, 22, 71, 4, "display"], [40, 29, 71, 11], [41, 6, 72, 4, "mask"], [41, 10, 72, 8], [41, 13, 81, 6, "props"], [41, 18, 81, 11], [41, 19, 72, 4, "mask"], [41, 23, 72, 8], [42, 6, 73, 4, "filter"], [42, 12, 73, 10], [42, 15, 81, 6, "props"], [42, 20, 81, 11], [42, 21, 73, 4, "filter"], [42, 27, 73, 10], [43, 6, 74, 4, "marker"], [43, 12, 74, 10], [43, 15, 81, 6, "props"], [43, 20, 81, 11], [43, 21, 74, 4, "marker"], [43, 27, 74, 10], [44, 6, 74, 10, "_props$markerStart"], [44, 24, 74, 10], [44, 27, 81, 6, "props"], [44, 32, 81, 11], [44, 33, 75, 4, "markerStart"], [44, 44, 75, 15], [45, 6, 75, 4, "markerStart"], [45, 17, 75, 15], [45, 20, 75, 15, "_props$markerStart"], [45, 38, 75, 15], [45, 52, 75, 18, "marker"], [45, 58, 75, 24], [45, 61, 75, 24, "_props$markerStart"], [45, 79, 75, 24], [46, 6, 75, 24, "_props$markerMid"], [46, 22, 75, 24], [46, 25, 81, 6, "props"], [46, 30, 81, 11], [46, 31, 76, 4, "markerMid"], [46, 40, 76, 13], [47, 6, 76, 4, "markerMid"], [47, 15, 76, 13], [47, 18, 76, 13, "_props$markerMid"], [47, 34, 76, 13], [47, 48, 76, 16, "marker"], [47, 54, 76, 22], [47, 57, 76, 22, "_props$markerMid"], [47, 73, 76, 22], [48, 6, 76, 22, "_props$markerEnd"], [48, 22, 76, 22], [48, 25, 81, 6, "props"], [48, 30, 81, 11], [48, 31, 77, 4, "markerEnd"], [48, 40, 77, 13], [49, 6, 77, 4, "markerEnd"], [49, 15, 77, 13], [49, 18, 77, 13, "_props$markerEnd"], [49, 34, 77, 13], [49, 48, 77, 16, "marker"], [49, 54, 77, 22], [49, 57, 77, 22, "_props$markerEnd"], [49, 73, 77, 22], [50, 6, 78, 4, "testID"], [50, 12, 78, 10], [50, 15, 81, 6, "props"], [50, 20, 81, 11], [50, 21, 78, 4, "testID"], [50, 27, 78, 10], [51, 6, 79, 4, "accessibilityLabel"], [51, 24, 79, 22], [51, 27, 81, 6, "props"], [51, 32, 81, 11], [51, 33, 79, 4, "accessibilityLabel"], [51, 51, 79, 22], [52, 6, 80, 4, "accessible"], [52, 16, 80, 14], [52, 19, 81, 6, "props"], [52, 24, 81, 11], [52, 25, 80, 4, "accessible"], [52, 35, 80, 14], [53, 4, 82, 2], [53, 8, 82, 8, "extracted"], [53, 17, 82, 33], [53, 20, 82, 36], [53, 21, 82, 37], [53, 22, 82, 38], [54, 4, 84, 2], [54, 8, 84, 8, "inherited"], [54, 17, 84, 27], [54, 20, 84, 30], [54, 22, 84, 32], [55, 4, 85, 2], [55, 8, 85, 2, "extractResponder"], [55, 33, 85, 18], [55, 35, 85, 19, "extracted"], [55, 44, 85, 28], [55, 46, 85, 30, "props"], [55, 51, 85, 35], [55, 53, 85, 37, "ref"], [55, 56, 85, 40], [55, 57, 85, 41], [56, 4, 86, 2], [56, 8, 86, 2, "extractFill"], [56, 28, 86, 13], [56, 30, 86, 14, "extracted"], [56, 39, 86, 23], [56, 41, 86, 25, "props"], [56, 46, 86, 30], [56, 48, 86, 32, "inherited"], [56, 57, 86, 41], [56, 58, 86, 42], [57, 4, 87, 2], [57, 8, 87, 2, "extractStroke"], [57, 30, 87, 15], [57, 32, 87, 16, "extracted"], [57, 41, 87, 25], [57, 43, 87, 27, "props"], [57, 48, 87, 32], [57, 50, 87, 34, "inherited"], [57, 59, 87, 43], [57, 60, 87, 44], [58, 4, 89, 2], [58, 8, 89, 6, "props"], [58, 13, 89, 11], [58, 14, 89, 12, "color"], [58, 19, 89, 17], [58, 21, 89, 19], [59, 6, 90, 4, "extracted"], [59, 15, 90, 13], [59, 16, 90, 14, "color"], [59, 21, 90, 19], [59, 24, 90, 22, "props"], [59, 29, 90, 27], [59, 30, 90, 28, "color"], [59, 35, 90, 33], [60, 4, 91, 2], [61, 4, 93, 2], [61, 8, 93, 6, "inherited"], [61, 17, 93, 15], [61, 18, 93, 16, "length"], [61, 24, 93, 22], [61, 26, 93, 24], [62, 6, 94, 4, "extracted"], [62, 15, 94, 13], [62, 16, 94, 14, "propList"], [62, 24, 94, 22], [62, 27, 94, 25, "inherited"], [62, 36, 94, 34], [63, 4, 95, 2], [64, 4, 97, 2], [64, 8, 97, 8, "matrix"], [64, 14, 97, 14], [64, 17, 97, 17], [64, 21, 97, 17, "extractTransform"], [64, 46, 97, 33], [64, 48, 97, 34, "props"], [64, 53, 97, 39], [64, 54, 97, 40], [65, 4, 98, 2], [65, 8, 98, 6, "matrix"], [65, 14, 98, 12], [65, 19, 98, 17], [65, 23, 98, 21], [65, 25, 98, 23], [66, 6, 99, 4, "extracted"], [66, 15, 99, 13], [66, 16, 99, 14, "matrix"], [66, 22, 99, 20], [66, 25, 99, 23, "matrix"], [66, 31, 99, 29], [67, 4, 100, 2], [68, 4, 102, 2], [68, 8, 102, 6, "opacity"], [68, 15, 102, 13], [68, 19, 102, 17], [68, 23, 102, 21], [68, 25, 102, 23], [69, 6, 103, 4, "extracted"], [69, 15, 103, 13], [69, 16, 103, 14, "opacity"], [69, 23, 103, 21], [69, 26, 103, 24], [69, 30, 103, 24, "extractOpacity"], [69, 53, 103, 38], [69, 55, 103, 39, "opacity"], [69, 62, 103, 46], [69, 63, 103, 47], [70, 4, 104, 2], [71, 4, 106, 2], [71, 8, 106, 6, "display"], [71, 15, 106, 13], [71, 19, 106, 17], [71, 23, 106, 21], [71, 25, 106, 23], [72, 6, 107, 4, "extracted"], [72, 15, 107, 13], [72, 16, 107, 14, "display"], [72, 23, 107, 21], [72, 26, 107, 24, "display"], [72, 33, 107, 31], [72, 38, 107, 36], [72, 44, 107, 42], [72, 47, 107, 45], [72, 53, 107, 51], [72, 56, 107, 54, "undefined"], [72, 65, 107, 63], [73, 4, 108, 2], [74, 4, 110, 2], [74, 8, 110, 6, "onLayout"], [74, 16, 110, 14], [74, 18, 110, 16], [75, 6, 111, 4, "extracted"], [75, 15, 111, 13], [75, 16, 111, 14, "onLayout"], [75, 24, 111, 22], [75, 27, 111, 25, "onLayout"], [75, 35, 111, 33], [76, 4, 112, 2], [77, 4, 114, 2], [77, 8, 114, 6, "markerStart"], [77, 19, 114, 17], [77, 21, 114, 19], [78, 6, 115, 4, "extracted"], [78, 15, 115, 13], [78, 16, 115, 14, "markerStart"], [78, 27, 115, 25], [78, 30, 115, 28, "<PERSON><PERSON><PERSON><PERSON>"], [78, 39, 115, 37], [78, 40, 115, 38, "markerStart"], [78, 51, 115, 49], [78, 52, 115, 50], [79, 4, 116, 2], [80, 4, 117, 2], [80, 8, 117, 6, "markerMid"], [80, 17, 117, 15], [80, 19, 117, 17], [81, 6, 118, 4, "extracted"], [81, 15, 118, 13], [81, 16, 118, 14, "markerMid"], [81, 25, 118, 23], [81, 28, 118, 26, "<PERSON><PERSON><PERSON><PERSON>"], [81, 37, 118, 35], [81, 38, 118, 36, "markerMid"], [81, 47, 118, 45], [81, 48, 118, 46], [82, 4, 119, 2], [83, 4, 120, 2], [83, 8, 120, 6, "markerEnd"], [83, 17, 120, 15], [83, 19, 120, 17], [84, 6, 121, 4, "extracted"], [84, 15, 121, 13], [84, 16, 121, 14, "markerEnd"], [84, 25, 121, 23], [84, 28, 121, 26, "<PERSON><PERSON><PERSON><PERSON>"], [84, 37, 121, 35], [84, 38, 121, 36, "markerEnd"], [84, 47, 121, 45], [84, 48, 121, 46], [85, 4, 122, 2], [86, 4, 124, 2], [86, 8, 124, 6, "id"], [86, 10, 124, 8], [86, 12, 124, 10], [87, 6, 125, 4, "extracted"], [87, 15, 125, 13], [87, 16, 125, 14, "name"], [87, 20, 125, 18], [87, 23, 125, 21, "String"], [87, 29, 125, 27], [87, 30, 125, 28, "id"], [87, 32, 125, 30], [87, 33, 125, 31], [88, 4, 126, 2], [89, 4, 128, 2], [89, 8, 128, 6, "testID"], [89, 14, 128, 12], [89, 16, 128, 14], [90, 6, 129, 4, "extracted"], [90, 15, 129, 13], [90, 16, 129, 14, "testID"], [90, 22, 129, 20], [90, 25, 129, 23, "testID"], [90, 31, 129, 29], [91, 4, 130, 2], [92, 4, 132, 2], [92, 8, 132, 6, "accessibilityLabel"], [92, 26, 132, 24], [92, 28, 132, 26], [93, 6, 133, 4, "extracted"], [93, 15, 133, 13], [93, 16, 133, 14, "accessibilityLabel"], [93, 34, 133, 32], [93, 37, 133, 35, "accessibilityLabel"], [93, 55, 133, 53], [94, 4, 134, 2], [95, 4, 136, 2], [95, 8, 136, 6, "accessible"], [95, 18, 136, 16], [95, 20, 136, 18], [96, 6, 137, 4, "extracted"], [96, 15, 137, 13], [96, 16, 137, 14, "accessible"], [96, 26, 137, 24], [96, 29, 137, 27, "accessible"], [96, 39, 137, 37], [97, 4, 138, 2], [98, 4, 140, 2], [98, 8, 140, 6, "clipRule"], [98, 16, 140, 14], [98, 18, 140, 16], [99, 6, 141, 4, "extracted"], [99, 15, 141, 13], [99, 16, 141, 14, "clipRule"], [99, 24, 141, 22], [99, 27, 141, 25, "clipRules"], [99, 36, 141, 34], [99, 37, 141, 35, "clipRule"], [99, 45, 141, 43], [99, 46, 141, 44], [99, 51, 141, 49], [99, 52, 141, 50], [99, 55, 141, 53], [99, 56, 141, 54], [99, 59, 141, 57], [99, 60, 141, 58], [100, 4, 142, 2], [101, 4, 143, 2], [101, 8, 143, 6, "clipPath"], [101, 16, 143, 14], [101, 18, 143, 16], [102, 6, 144, 4], [102, 10, 144, 10, "matched"], [102, 17, 144, 17], [102, 20, 144, 20, "clipPath"], [102, 28, 144, 28], [102, 29, 144, 29, "match"], [102, 34, 144, 34], [102, 35, 144, 35, "idPattern"], [102, 50, 144, 44], [102, 51, 144, 45], [103, 6, 145, 4], [103, 10, 145, 8, "matched"], [103, 17, 145, 15], [103, 19, 145, 17], [104, 8, 146, 6, "extracted"], [104, 17, 146, 15], [104, 18, 146, 16, "clipPath"], [104, 26, 146, 24], [104, 29, 146, 27, "matched"], [104, 36, 146, 34], [104, 37, 146, 35], [104, 38, 146, 36], [104, 39, 146, 37], [105, 6, 147, 4], [105, 7, 147, 5], [105, 13, 147, 11], [106, 8, 148, 6, "console"], [106, 15, 148, 13], [106, 16, 148, 14, "warn"], [106, 20, 148, 18], [106, 21, 149, 8], [106, 90, 149, 77], [106, 93, 150, 10, "clipPath"], [106, 101, 150, 18], [106, 104, 151, 10], [106, 107, 152, 6], [106, 108, 152, 7], [107, 6, 153, 4], [108, 4, 154, 2], [109, 4, 156, 2], [109, 8, 156, 6, "mask"], [109, 12, 156, 10], [109, 14, 156, 12], [110, 6, 157, 4], [110, 10, 157, 10, "matched"], [110, 18, 157, 17], [110, 21, 157, 20, "mask"], [110, 25, 157, 24], [110, 26, 157, 25, "match"], [110, 31, 157, 30], [110, 32, 157, 31, "idPattern"], [110, 47, 157, 40], [110, 48, 157, 41], [111, 6, 159, 4], [111, 10, 159, 8, "matched"], [111, 18, 159, 15], [111, 20, 159, 17], [112, 8, 160, 6, "extracted"], [112, 17, 160, 15], [112, 18, 160, 16, "mask"], [112, 22, 160, 20], [112, 25, 160, 23, "matched"], [112, 33, 160, 30], [112, 34, 160, 31], [112, 35, 160, 32], [112, 36, 160, 33], [113, 6, 161, 4], [113, 7, 161, 5], [113, 13, 161, 11], [114, 8, 162, 6, "console"], [114, 15, 162, 13], [114, 16, 162, 14, "warn"], [114, 20, 162, 18], [114, 21, 163, 8], [114, 82, 163, 69], [114, 85, 164, 10, "mask"], [114, 89, 164, 14], [114, 92, 165, 10], [114, 95, 166, 6], [114, 96, 166, 7], [115, 6, 167, 4], [116, 4, 168, 2], [117, 4, 170, 2], [117, 8, 170, 6, "filter"], [117, 14, 170, 12], [117, 16, 170, 14], [118, 6, 171, 4], [118, 10, 171, 10, "matched"], [118, 19, 171, 17], [118, 22, 171, 20, "filter"], [118, 28, 171, 26], [118, 29, 171, 27, "match"], [118, 34, 171, 32], [118, 35, 171, 33, "idPattern"], [118, 50, 171, 42], [118, 51, 171, 43], [119, 6, 173, 4], [119, 10, 173, 8, "matched"], [119, 19, 173, 15], [119, 21, 173, 17], [120, 8, 174, 6, "extracted"], [120, 17, 174, 15], [120, 18, 174, 16, "filter"], [120, 24, 174, 22], [120, 27, 174, 25, "matched"], [120, 36, 174, 32], [120, 37, 174, 33], [120, 38, 174, 34], [120, 39, 174, 35], [121, 6, 175, 4], [121, 7, 175, 5], [121, 13, 175, 11], [122, 8, 176, 6, "console"], [122, 15, 176, 13], [122, 16, 176, 14, "warn"], [122, 20, 176, 18], [122, 21, 177, 8], [122, 86, 177, 73], [122, 89, 178, 10, "filter"], [122, 95, 178, 16], [122, 98, 179, 10], [122, 101, 180, 6], [122, 102, 180, 7], [123, 6, 181, 4], [124, 4, 182, 2], [125, 4, 184, 2], [125, 11, 184, 9, "extracted"], [125, 20, 184, 18], [126, 2, 185, 0], [127, 2, 187, 7], [127, 11, 187, 16, "extract"], [127, 18, 187, 23, "extract"], [127, 19, 188, 2, "instance"], [127, 27, 188, 18], [127, 29, 189, 2, "props"], [127, 34, 189, 42], [127, 36, 190, 2], [128, 4, 191, 2], [128, 11, 191, 9, "extractProps"], [128, 23, 191, 21], [128, 24, 191, 22, "propsAndStyles"], [128, 38, 191, 36], [128, 39, 191, 37, "props"], [128, 44, 191, 42], [128, 45, 191, 43], [128, 47, 191, 45, "instance"], [128, 55, 191, 53], [128, 56, 191, 54], [129, 2, 192, 0], [130, 2, 194, 7], [130, 11, 194, 16, "withoutXY"], [130, 20, 194, 25, "withoutXY"], [130, 21, 195, 2, "instance"], [130, 29, 195, 18], [130, 31, 196, 2, "props"], [130, 36, 196, 42], [130, 38, 197, 2], [131, 4, 198, 2], [131, 11, 198, 9, "extractProps"], [131, 23, 198, 21], [131, 24, 198, 22], [132, 6, 198, 24], [132, 9, 198, 27, "propsAndStyles"], [132, 23, 198, 41], [132, 24, 198, 42, "props"], [132, 29, 198, 47], [132, 30, 198, 48], [133, 6, 198, 50, "x"], [133, 7, 198, 51], [133, 9, 198, 53], [133, 13, 198, 57], [134, 6, 198, 59, "y"], [134, 7, 198, 60], [134, 9, 198, 62], [135, 4, 198, 67], [135, 5, 198, 68], [135, 7, 198, 70, "instance"], [135, 15, 198, 78], [135, 16, 198, 79], [136, 2, 199, 0], [137, 0, 199, 1], [137, 3]], "functionMap": {"names": ["<global>", "propsAndStyles", "<PERSON><PERSON><PERSON><PERSON>", "extractProps", "extract", "withoutXY"], "mappings": "AAA;OCsB;CDQ;AEE;CFM;eGE;CHgJ;OIE;CJK;OKE;CLK"}}, "type": "js/module"}]}