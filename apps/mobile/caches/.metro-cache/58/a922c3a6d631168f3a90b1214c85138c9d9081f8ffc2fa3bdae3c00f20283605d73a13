{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Subscribable = void 0;\n  // src/subscribable.ts\n  var Subscribable = class {\n    constructor() {\n      this.listeners = /* @__PURE__ */new Set();\n      this.subscribe = this.subscribe.bind(this);\n    }\n    subscribe(listener) {\n      this.listeners.add(listener);\n      this.onSubscribe();\n      return () => {\n        this.listeners.delete(listener);\n        this.onUnsubscribe();\n      };\n    }\n    hasListeners() {\n      return this.listeners.size > 0;\n    }\n    onSubscribe() {}\n    onUnsubscribe() {}\n  };\n  exports.Subscribable = Subscribable;\n});", "lineCount": 27, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [7, 6, 2, 4, "Subscribable"], [7, 18, 2, 16], [7, 21, 2, 19], [7, 27, 2, 25], [8, 4, 3, 2, "constructor"], [8, 15, 3, 13, "constructor"], [8, 16, 3, 13], [8, 18, 3, 16], [9, 6, 4, 4], [9, 10, 4, 8], [9, 11, 4, 9, "listeners"], [9, 20, 4, 18], [9, 23, 4, 21], [9, 38, 4, 37], [9, 42, 4, 41, "Set"], [9, 45, 4, 44], [9, 46, 4, 45], [9, 47, 4, 46], [10, 6, 5, 4], [10, 10, 5, 8], [10, 11, 5, 9, "subscribe"], [10, 20, 5, 18], [10, 23, 5, 21], [10, 27, 5, 25], [10, 28, 5, 26, "subscribe"], [10, 37, 5, 35], [10, 38, 5, 36, "bind"], [10, 42, 5, 40], [10, 43, 5, 41], [10, 47, 5, 45], [10, 48, 5, 46], [11, 4, 6, 2], [12, 4, 7, 2, "subscribe"], [12, 13, 7, 11, "subscribe"], [12, 14, 7, 12, "listener"], [12, 22, 7, 20], [12, 24, 7, 22], [13, 6, 8, 4], [13, 10, 8, 8], [13, 11, 8, 9, "listeners"], [13, 20, 8, 18], [13, 21, 8, 19, "add"], [13, 24, 8, 22], [13, 25, 8, 23, "listener"], [13, 33, 8, 31], [13, 34, 8, 32], [14, 6, 9, 4], [14, 10, 9, 8], [14, 11, 9, 9, "onSubscribe"], [14, 22, 9, 20], [14, 23, 9, 21], [14, 24, 9, 22], [15, 6, 10, 4], [15, 13, 10, 11], [15, 19, 10, 17], [16, 8, 11, 6], [16, 12, 11, 10], [16, 13, 11, 11, "listeners"], [16, 22, 11, 20], [16, 23, 11, 21, "delete"], [16, 29, 11, 27], [16, 30, 11, 28, "listener"], [16, 38, 11, 36], [16, 39, 11, 37], [17, 8, 12, 6], [17, 12, 12, 10], [17, 13, 12, 11, "onUnsubscribe"], [17, 26, 12, 24], [17, 27, 12, 25], [17, 28, 12, 26], [18, 6, 13, 4], [18, 7, 13, 5], [19, 4, 14, 2], [20, 4, 15, 2, "hasListeners"], [20, 16, 15, 14, "hasListeners"], [20, 17, 15, 14], [20, 19, 15, 17], [21, 6, 16, 4], [21, 13, 16, 11], [21, 17, 16, 15], [21, 18, 16, 16, "listeners"], [21, 27, 16, 25], [21, 28, 16, 26, "size"], [21, 32, 16, 30], [21, 35, 16, 33], [21, 36, 16, 34], [22, 4, 17, 2], [23, 4, 18, 2, "onSubscribe"], [23, 15, 18, 13, "onSubscribe"], [23, 16, 18, 13], [23, 18, 18, 16], [23, 19, 19, 2], [24, 4, 20, 2, "onUnsubscribe"], [24, 17, 20, 15, "onUnsubscribe"], [24, 18, 20, 15], [24, 20, 20, 18], [24, 21, 21, 2], [25, 2, 22, 0], [25, 3, 22, 1], [26, 2, 22, 2, "exports"], [26, 9, 22, 2], [26, 10, 22, 2, "Subscribable"], [26, 22, 22, 2], [26, 25, 22, 2, "Subscribable"], [26, 37, 22, 2], [27, 0, 22, 2], [27, 3]], "functionMap": {"names": ["<global>", "Subscribable", "Subscribable#constructor", "Subscribable#subscribe", "<anonymous>", "Subscribable#hasListeners", "Subscribable#onSubscribe", "Subscribable#onUnsubscribe"], "mappings": "AAA;mBCC;ECC;GDG;EEC;WCG;KDG;GFC;EIC;GJE;EKC;GLC;EMC;GNC;CDC"}}, "type": "js/module"}]}