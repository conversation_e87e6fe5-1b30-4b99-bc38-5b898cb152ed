{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 50, "index": 231}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-svg", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 232}, "end": {"line": 9, "column": 46, "index": 278}}], "key": "lCMYlEpYXUxeSuxY/qJGK1buGwU=", "exportNames": ["*"]}}, {"name": "./defaultAttributes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 279}, "end": {"line": 10, "column": 83, "index": 362}}], "key": "jhIicveOKJFsp32zVLJ2qzocBIw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var NativeSvg = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-svg\"));\n  var _defaultAttributes = _interopRequireWildcard(require(_dependencyMap[2], \"./defaultAttributes.js\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Icon = exports.default = /*#__PURE__*/(0, _react.forwardRef)(({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    children,\n    iconNode,\n    ...rest\n  }, ref) => {\n    const customAttrs = {\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      ...rest\n    };\n    return /*#__PURE__*/(0, _react.createElement)(NativeSvg.Svg, {\n      ref,\n      ..._defaultAttributes.default,\n      width: size,\n      height: size,\n      ...customAttrs\n    }, [...iconNode.map(([tag, attrs]) => {\n      const upperCasedTag = tag.charAt(0).toUpperCase() + tag.slice(1);\n      return /*#__PURE__*/(0, _react.createElement)(NativeSvg[upperCasedTag], {\n        ..._defaultAttributes.childDefaultAttributes,\n        ...customAttrs,\n        ...attrs\n      });\n    }), ...((Array.isArray(children) ? children : [children]) || [])]);\n  });\n});", "lineCount": 46, "map": [[6, 2, 8, 0], [6, 6, 8, 0, "_react"], [6, 12, 8, 0], [6, 15, 8, 0, "require"], [6, 22, 8, 0], [6, 23, 8, 0, "_dependencyMap"], [6, 37, 8, 0], [7, 2, 9, 0], [7, 6, 9, 0, "NativeSvg"], [7, 15, 9, 0], [7, 18, 9, 0, "_interopRequireWildcard"], [7, 41, 9, 0], [7, 42, 9, 0, "require"], [7, 49, 9, 0], [7, 50, 9, 0, "_dependencyMap"], [7, 64, 9, 0], [8, 2, 10, 0], [8, 6, 10, 0, "_defaultAttributes"], [8, 24, 10, 0], [8, 27, 10, 0, "_interopRequireWildcard"], [8, 50, 10, 0], [8, 51, 10, 0, "require"], [8, 58, 10, 0], [8, 59, 10, 0, "_dependencyMap"], [8, 73, 10, 0], [9, 2, 10, 83], [9, 11, 10, 83, "_interopRequireWildcard"], [9, 35, 10, 83, "e"], [9, 36, 10, 83], [9, 38, 10, 83, "t"], [9, 39, 10, 83], [9, 68, 10, 83, "WeakMap"], [9, 75, 10, 83], [9, 81, 10, 83, "r"], [9, 82, 10, 83], [9, 89, 10, 83, "WeakMap"], [9, 96, 10, 83], [9, 100, 10, 83, "n"], [9, 101, 10, 83], [9, 108, 10, 83, "WeakMap"], [9, 115, 10, 83], [9, 127, 10, 83, "_interopRequireWildcard"], [9, 150, 10, 83], [9, 162, 10, 83, "_interopRequireWildcard"], [9, 163, 10, 83, "e"], [9, 164, 10, 83], [9, 166, 10, 83, "t"], [9, 167, 10, 83], [9, 176, 10, 83, "t"], [9, 177, 10, 83], [9, 181, 10, 83, "e"], [9, 182, 10, 83], [9, 186, 10, 83, "e"], [9, 187, 10, 83], [9, 188, 10, 83, "__esModule"], [9, 198, 10, 83], [9, 207, 10, 83, "e"], [9, 208, 10, 83], [9, 214, 10, 83, "o"], [9, 215, 10, 83], [9, 217, 10, 83, "i"], [9, 218, 10, 83], [9, 220, 10, 83, "f"], [9, 221, 10, 83], [9, 226, 10, 83, "__proto__"], [9, 235, 10, 83], [9, 243, 10, 83, "default"], [9, 250, 10, 83], [9, 252, 10, 83, "e"], [9, 253, 10, 83], [9, 270, 10, 83, "e"], [9, 271, 10, 83], [9, 294, 10, 83, "e"], [9, 295, 10, 83], [9, 320, 10, 83, "e"], [9, 321, 10, 83], [9, 330, 10, 83, "f"], [9, 331, 10, 83], [9, 337, 10, 83, "o"], [9, 338, 10, 83], [9, 341, 10, 83, "t"], [9, 342, 10, 83], [9, 345, 10, 83, "n"], [9, 346, 10, 83], [9, 349, 10, 83, "r"], [9, 350, 10, 83], [9, 358, 10, 83, "o"], [9, 359, 10, 83], [9, 360, 10, 83, "has"], [9, 363, 10, 83], [9, 364, 10, 83, "e"], [9, 365, 10, 83], [9, 375, 10, 83, "o"], [9, 376, 10, 83], [9, 377, 10, 83, "get"], [9, 380, 10, 83], [9, 381, 10, 83, "e"], [9, 382, 10, 83], [9, 385, 10, 83, "o"], [9, 386, 10, 83], [9, 387, 10, 83, "set"], [9, 390, 10, 83], [9, 391, 10, 83, "e"], [9, 392, 10, 83], [9, 394, 10, 83, "f"], [9, 395, 10, 83], [9, 411, 10, 83, "t"], [9, 412, 10, 83], [9, 416, 10, 83, "e"], [9, 417, 10, 83], [9, 433, 10, 83, "t"], [9, 434, 10, 83], [9, 441, 10, 83, "hasOwnProperty"], [9, 455, 10, 83], [9, 456, 10, 83, "call"], [9, 460, 10, 83], [9, 461, 10, 83, "e"], [9, 462, 10, 83], [9, 464, 10, 83, "t"], [9, 465, 10, 83], [9, 472, 10, 83, "i"], [9, 473, 10, 83], [9, 477, 10, 83, "o"], [9, 478, 10, 83], [9, 481, 10, 83, "Object"], [9, 487, 10, 83], [9, 488, 10, 83, "defineProperty"], [9, 502, 10, 83], [9, 507, 10, 83, "Object"], [9, 513, 10, 83], [9, 514, 10, 83, "getOwnPropertyDescriptor"], [9, 538, 10, 83], [9, 539, 10, 83, "e"], [9, 540, 10, 83], [9, 542, 10, 83, "t"], [9, 543, 10, 83], [9, 550, 10, 83, "i"], [9, 551, 10, 83], [9, 552, 10, 83, "get"], [9, 555, 10, 83], [9, 559, 10, 83, "i"], [9, 560, 10, 83], [9, 561, 10, 83, "set"], [9, 564, 10, 83], [9, 568, 10, 83, "o"], [9, 569, 10, 83], [9, 570, 10, 83, "f"], [9, 571, 10, 83], [9, 573, 10, 83, "t"], [9, 574, 10, 83], [9, 576, 10, 83, "i"], [9, 577, 10, 83], [9, 581, 10, 83, "f"], [9, 582, 10, 83], [9, 583, 10, 83, "t"], [9, 584, 10, 83], [9, 588, 10, 83, "e"], [9, 589, 10, 83], [9, 590, 10, 83, "t"], [9, 591, 10, 83], [9, 602, 10, 83, "f"], [9, 603, 10, 83], [9, 608, 10, 83, "e"], [9, 609, 10, 83], [9, 611, 10, 83, "t"], [9, 612, 10, 83], [10, 2, 1, 0], [11, 0, 2, 0], [12, 0, 3, 0], [13, 0, 4, 0], [14, 0, 5, 0], [15, 0, 6, 0], [17, 2, 12, 0], [17, 8, 12, 6, "Icon"], [17, 12, 12, 10], [17, 15, 12, 10, "exports"], [17, 22, 12, 10], [17, 23, 12, 10, "default"], [17, 30, 12, 10], [17, 46, 12, 13], [17, 50, 12, 13, "forwardRef"], [17, 67, 12, 23], [17, 69, 13, 2], [17, 70, 13, 3], [18, 4, 14, 4, "color"], [18, 9, 14, 9], [18, 12, 14, 12], [18, 26, 14, 26], [19, 4, 15, 4, "size"], [19, 8, 15, 8], [19, 11, 15, 11], [19, 13, 15, 13], [20, 4, 16, 4, "strokeWidth"], [20, 15, 16, 15], [20, 18, 16, 18], [20, 19, 16, 19], [21, 4, 17, 4, "absoluteStrokeWidth"], [21, 23, 17, 23], [22, 4, 18, 4, "children"], [22, 12, 18, 12], [23, 4, 19, 4, "iconNode"], [23, 12, 19, 12], [24, 4, 20, 4], [24, 7, 20, 7, "rest"], [25, 2, 21, 2], [25, 3, 21, 3], [25, 5, 21, 5, "ref"], [25, 8, 21, 8], [25, 13, 21, 13], [26, 4, 22, 4], [26, 10, 22, 10, "customAttrs"], [26, 21, 22, 21], [26, 24, 22, 24], [27, 6, 23, 6, "stroke"], [27, 12, 23, 12], [27, 14, 23, 14, "color"], [27, 19, 23, 19], [28, 6, 24, 6, "strokeWidth"], [28, 17, 24, 17], [28, 19, 24, 19, "absoluteStrokeWidth"], [28, 38, 24, 38], [28, 41, 24, 41, "Number"], [28, 47, 24, 47], [28, 48, 24, 48, "strokeWidth"], [28, 59, 24, 59], [28, 60, 24, 60], [28, 63, 24, 63], [28, 65, 24, 65], [28, 68, 24, 68, "Number"], [28, 74, 24, 74], [28, 75, 24, 75, "size"], [28, 79, 24, 79], [28, 80, 24, 80], [28, 83, 24, 83, "strokeWidth"], [28, 94, 24, 94], [29, 6, 25, 6], [29, 9, 25, 9, "rest"], [30, 4, 26, 4], [30, 5, 26, 5], [31, 4, 27, 4], [31, 24, 27, 11], [31, 28, 27, 11, "createElement"], [31, 48, 27, 24], [31, 50, 28, 6, "NativeSvg"], [31, 59, 28, 15], [31, 60, 28, 16, "Svg"], [31, 63, 28, 19], [31, 65, 29, 6], [32, 6, 30, 8, "ref"], [32, 9, 30, 11], [33, 6, 31, 8], [33, 9, 31, 11, "defaultAttributes"], [33, 35, 31, 28], [34, 6, 32, 8, "width"], [34, 11, 32, 13], [34, 13, 32, 15, "size"], [34, 17, 32, 19], [35, 6, 33, 8, "height"], [35, 12, 33, 14], [35, 14, 33, 16, "size"], [35, 18, 33, 20], [36, 6, 34, 8], [36, 9, 34, 11, "customAttrs"], [37, 4, 35, 6], [37, 5, 35, 7], [37, 7, 36, 6], [37, 8, 37, 8], [37, 11, 37, 11, "iconNode"], [37, 19, 37, 19], [37, 20, 37, 20, "map"], [37, 23, 37, 23], [37, 24, 37, 24], [37, 25, 37, 25], [37, 26, 37, 26, "tag"], [37, 29, 37, 29], [37, 31, 37, 31, "attrs"], [37, 36, 37, 36], [37, 37, 37, 37], [37, 42, 37, 42], [38, 6, 38, 10], [38, 12, 38, 16, "upperCasedTag"], [38, 25, 38, 29], [38, 28, 38, 32, "tag"], [38, 31, 38, 35], [38, 32, 38, 36, "char<PERSON>t"], [38, 38, 38, 42], [38, 39, 38, 43], [38, 40, 38, 44], [38, 41, 38, 45], [38, 42, 38, 46, "toUpperCase"], [38, 53, 38, 57], [38, 54, 38, 58], [38, 55, 38, 59], [38, 58, 38, 62, "tag"], [38, 61, 38, 65], [38, 62, 38, 66, "slice"], [38, 67, 38, 71], [38, 68, 38, 72], [38, 69, 38, 73], [38, 70, 38, 74], [39, 6, 39, 10], [39, 26, 39, 17], [39, 30, 39, 17, "createElement"], [39, 50, 39, 30], [39, 52, 40, 12, "NativeSvg"], [39, 61, 40, 21], [39, 62, 40, 22, "upperCasedTag"], [39, 75, 40, 35], [39, 76, 40, 36], [39, 78, 41, 12], [40, 8, 41, 14], [40, 11, 41, 17, "childDefaultAttributes"], [40, 52, 41, 39], [41, 8, 41, 41], [41, 11, 41, 44, "customAttrs"], [41, 22, 41, 55], [42, 8, 41, 57], [42, 11, 41, 60, "attrs"], [43, 6, 41, 66], [43, 7, 42, 10], [43, 8, 42, 11], [44, 4, 43, 8], [44, 5, 43, 9], [44, 6, 43, 10], [44, 8, 44, 8], [44, 12, 44, 11], [44, 13, 44, 12, "Array"], [44, 18, 44, 17], [44, 19, 44, 18, "isArray"], [44, 26, 44, 25], [44, 27, 44, 26, "children"], [44, 35, 44, 34], [44, 36, 44, 35], [44, 39, 44, 38, "children"], [44, 47, 44, 46], [44, 50, 44, 49], [44, 51, 44, 50, "children"], [44, 59, 44, 58], [44, 60, 44, 59], [44, 65, 44, 64], [44, 67, 44, 66], [44, 69, 46, 4], [44, 70, 46, 5], [45, 2, 47, 2], [45, 3, 48, 0], [45, 4, 48, 1], [46, 0, 48, 2], [46, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0", "iconNode.map$argument_0"], "mappings": "AAA;ECY;wBCwB;SDM;GDI"}}, "type": "js/module"}]}