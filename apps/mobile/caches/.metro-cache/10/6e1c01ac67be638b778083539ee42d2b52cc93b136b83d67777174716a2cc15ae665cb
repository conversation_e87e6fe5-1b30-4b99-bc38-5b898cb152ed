{"dependencies": [{"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 32, "index": 293}, "end": {"line": 7, "column": 48, "index": 309}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./Toast", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 16, "index": 328}, "end": {"line": 8, "column": 34, "index": 346}}], "key": "2fyEy6KC4z6QNiWht+1BUJ8pLHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _reactJsxDevRuntime = require(_dependencyMap[0], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/expo-router/build/views/SuspenseFallback.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SuspenseFallback = SuspenseFallback;\n  var react_1 = __importDefault(require(_dependencyMap[1], \"react\"));\n  var Toast_1 = require(_dependencyMap[2], \"./Toast\");\n  function SuspenseFallback(_ref) {\n    var route = _ref.route;\n    if (__DEV__) {\n      return /*#__PURE__*/_reactJsxDevRuntime.jsxDEV(Toast_1.ToastWrapper, {\n        children: /*#__PURE__*/_reactJsxDevRuntime.jsxDEV(Toast_1.Toast, {\n          filename: route?.contextKey,\n          children: \"Bundling...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this);\n    }\n    // TODO: Support user's customizing the fallback.\n    return null;\n  }\n});", "lineCount": 38, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_reactJsxDevRuntime"], [4, 25, 1, 13], [4, 28, 1, 13, "require"], [4, 35, 1, 13], [4, 36, 1, 13, "_dependencyMap"], [4, 50, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_jsxFileName"], [5, 18, 1, 13], [6, 2, 2, 0], [6, 6, 2, 4, "__importDefault"], [6, 21, 2, 19], [6, 24, 2, 23], [6, 28, 2, 27], [6, 32, 2, 31], [6, 36, 2, 35], [6, 37, 2, 36, "__importDefault"], [6, 52, 2, 51], [6, 56, 2, 56], [6, 66, 2, 66, "mod"], [6, 69, 2, 69], [6, 71, 2, 71], [7, 4, 3, 4], [7, 11, 3, 12, "mod"], [7, 14, 3, 15], [7, 18, 3, 19, "mod"], [7, 21, 3, 22], [7, 22, 3, 23, "__esModule"], [7, 32, 3, 33], [7, 35, 3, 37, "mod"], [7, 38, 3, 40], [7, 41, 3, 43], [8, 6, 3, 45], [8, 15, 3, 54], [8, 17, 3, 56, "mod"], [9, 4, 3, 60], [9, 5, 3, 61], [10, 2, 4, 0], [10, 3, 4, 1], [11, 2, 5, 0, "Object"], [11, 8, 5, 6], [11, 9, 5, 7, "defineProperty"], [11, 23, 5, 21], [11, 24, 5, 22, "exports"], [11, 31, 5, 29], [11, 33, 5, 31], [11, 45, 5, 43], [11, 47, 5, 45], [12, 4, 5, 47, "value"], [12, 9, 5, 52], [12, 11, 5, 54], [13, 2, 5, 59], [13, 3, 5, 60], [13, 4, 5, 61], [14, 2, 6, 0, "exports"], [14, 9, 6, 7], [14, 10, 6, 8, "SuspenseFallback"], [14, 26, 6, 24], [14, 29, 6, 27, "SuspenseFallback"], [14, 45, 6, 43], [15, 2, 7, 0], [15, 6, 7, 6, "react_1"], [15, 13, 7, 13], [15, 16, 7, 16, "__importDefault"], [15, 31, 7, 31], [15, 32, 7, 32, "require"], [15, 39, 7, 39], [15, 40, 7, 39, "_dependencyMap"], [15, 54, 7, 39], [15, 66, 7, 47], [15, 67, 7, 48], [15, 68, 7, 49], [16, 2, 8, 0], [16, 6, 8, 6, "Toast_1"], [16, 13, 8, 13], [16, 16, 8, 16, "require"], [16, 23, 8, 23], [16, 24, 8, 23, "_dependencyMap"], [16, 38, 8, 23], [16, 52, 8, 33], [16, 53, 8, 34], [17, 2, 9, 0], [17, 11, 9, 9, "SuspenseFallback"], [17, 27, 9, 25, "SuspenseFallback"], [17, 28, 9, 25, "_ref"], [17, 32, 9, 25], [17, 34, 9, 37], [18, 4, 9, 37], [18, 8, 9, 28, "route"], [18, 13, 9, 33], [18, 16, 9, 33, "_ref"], [18, 20, 9, 33], [18, 21, 9, 28, "route"], [18, 26, 9, 33], [19, 4, 10, 4], [19, 8, 10, 8, "__DEV__"], [19, 15, 10, 15], [19, 17, 10, 17], [20, 6, 11, 8], [20, 26, 11, 16, "_reactJsxDevRuntime"], [20, 45, 11, 16], [20, 46, 11, 16, "jsxDEV"], [20, 52, 11, 16], [20, 53, 11, 17, "Toast_1"], [20, 60, 11, 24], [20, 61, 11, 25, "ToastWrapper"], [20, 73, 11, 37], [21, 8, 11, 37, "children"], [21, 16, 11, 37], [21, 31, 12, 8, "_reactJsxDevRuntime"], [21, 50, 12, 8], [21, 51, 12, 8, "jsxDEV"], [21, 57, 12, 8], [21, 58, 12, 9, "Toast_1"], [21, 65, 12, 16], [21, 66, 12, 17, "Toast"], [21, 71, 12, 22], [22, 10, 12, 23, "filename"], [22, 18, 12, 31], [22, 20, 12, 33, "route"], [22, 25, 12, 38], [22, 27, 12, 40, "<PERSON><PERSON>ey"], [22, 37, 12, 51], [23, 10, 12, 51, "children"], [23, 18, 12, 51], [23, 20, 12, 52], [24, 8, 12, 63], [25, 10, 12, 63, "fileName"], [25, 18, 12, 63], [25, 20, 12, 63, "_jsxFileName"], [25, 32, 12, 63], [26, 10, 12, 63, "lineNumber"], [26, 20, 12, 63], [27, 10, 12, 63, "columnNumber"], [27, 22, 12, 63], [28, 8, 12, 63], [28, 15, 12, 78], [29, 6, 12, 79], [30, 8, 12, 79, "fileName"], [30, 16, 12, 79], [30, 18, 12, 79, "_jsxFileName"], [30, 30, 12, 79], [31, 8, 12, 79, "lineNumber"], [31, 18, 12, 79], [32, 8, 12, 79, "columnNumber"], [32, 20, 12, 79], [33, 6, 12, 79], [33, 13, 13, 28], [33, 14, 13, 29], [34, 4, 14, 4], [35, 4, 15, 4], [36, 4, 16, 4], [36, 11, 16, 11], [36, 15, 16, 15], [37, 2, 17, 0], [38, 0, 17, 1], [38, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "SuspenseFallback"], "mappings": "AAA;wDCC;CDE;AEK;CFQ"}}, "type": "js/module"}]}