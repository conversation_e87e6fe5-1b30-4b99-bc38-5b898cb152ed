{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.REGISTER_DOM_IMPERATIVE_HANDLE_PROPS = exports.NATIVE_ACTION_RESULT = exports.NATIVE_ACTION = exports.MATCH_CONTENTS_EVENT = exports.DOM_EVENT = void 0;\n  exports.getInjectBodySizeObserverScript = getInjectBodySizeObserverScript;\n  exports.getInjectEventScript = void 0;\n  const NATIVE_ACTION = exports.NATIVE_ACTION = '$$native_action';\n  const NATIVE_ACTION_RESULT = exports.NATIVE_ACTION_RESULT = '$$native_action_result';\n  const DOM_EVENT = exports.DOM_EVENT = '$$dom_event';\n  const MATCH_CONTENTS_EVENT = exports.MATCH_CONTENTS_EVENT = '$$match_contents_event';\n  const REGISTER_DOM_IMPERATIVE_HANDLE_PROPS = exports.REGISTER_DOM_IMPERATIVE_HANDLE_PROPS = '$$register_dom_imperative_handle_props';\n  const getInjectEventScript = detail => {\n    return `;(function() {\n  try {\n  window.dispatchEvent(new CustomEvent(\"${DOM_EVENT}\",${JSON.stringify({\n      detail\n    })}));\n  } catch (e) {}\n  })();\n  true;`;\n  };\n  exports.getInjectEventScript = getInjectEventScript;\n  function getInjectBodySizeObserverScript() {\n    return `;(function observeDocumentBodySize() {\n  window.addEventListener('DOMContentLoaded', () => {\n    new ResizeObserver(entries => {\n      const { width, height } = entries[0].contentRect;\n      window.ReactNativeWebView?.postMessage(JSON.stringify({\n        type: '${MATCH_CONTENTS_EVENT}',\n        data: {\n          width,\n          height,\n        },\n      }));\n    })\n    .observe(document.body);\n    window.ReactNativeWebView?.postMessage(JSON.stringify({\n      type: '${MATCH_CONTENTS_EVENT}',\n      data: {\n        width: document.body.clientWidth,\n        height: document.body.clientHeight,\n      },\n    }));\n  });\n  })();\n  true;`;\n  }\n});", "lineCount": 49, "map": [[8, 2, 3, 7], [8, 8, 3, 13, "NATIVE_ACTION"], [8, 21, 3, 26], [8, 24, 3, 26, "exports"], [8, 31, 3, 26], [8, 32, 3, 26, "NATIVE_ACTION"], [8, 45, 3, 26], [8, 48, 3, 29], [8, 65, 3, 46], [9, 2, 4, 7], [9, 8, 4, 13, "NATIVE_ACTION_RESULT"], [9, 28, 4, 33], [9, 31, 4, 33, "exports"], [9, 38, 4, 33], [9, 39, 4, 33, "NATIVE_ACTION_RESULT"], [9, 59, 4, 33], [9, 62, 4, 36], [9, 86, 4, 60], [10, 2, 5, 7], [10, 8, 5, 13, "DOM_EVENT"], [10, 17, 5, 22], [10, 20, 5, 22, "exports"], [10, 27, 5, 22], [10, 28, 5, 22, "DOM_EVENT"], [10, 37, 5, 22], [10, 40, 5, 25], [10, 53, 5, 38], [11, 2, 6, 7], [11, 8, 6, 13, "MATCH_CONTENTS_EVENT"], [11, 28, 6, 33], [11, 31, 6, 33, "exports"], [11, 38, 6, 33], [11, 39, 6, 33, "MATCH_CONTENTS_EVENT"], [11, 59, 6, 33], [11, 62, 6, 36], [11, 86, 6, 60], [12, 2, 7, 7], [12, 8, 7, 13, "REGISTER_DOM_IMPERATIVE_HANDLE_PROPS"], [12, 44, 7, 49], [12, 47, 7, 49, "exports"], [12, 54, 7, 49], [12, 55, 7, 49, "REGISTER_DOM_IMPERATIVE_HANDLE_PROPS"], [12, 91, 7, 49], [12, 94, 7, 52], [12, 134, 7, 92], [13, 2, 9, 7], [13, 8, 9, 13, "getInjectEventScript"], [13, 28, 9, 33], [13, 31, 9, 67, "detail"], [13, 37, 9, 76], [13, 41, 9, 81], [14, 4, 10, 2], [14, 11, 10, 9], [15, 0, 11, 0], [16, 0, 12, 0], [16, 42, 12, 42, "DOM_EVENT"], [16, 51, 12, 51], [16, 56, 12, 56, "JSON"], [16, 60, 12, 60], [16, 61, 12, 61, "stringify"], [16, 70, 12, 70], [16, 71, 12, 71], [17, 6, 12, 73, "detail"], [18, 4, 12, 80], [18, 5, 12, 81], [18, 6, 12, 82], [19, 0, 13, 0], [20, 0, 14, 0], [21, 0, 15, 0], [21, 8, 15, 8], [22, 2, 16, 0], [22, 3, 16, 1], [23, 2, 16, 2, "exports"], [23, 9, 16, 2], [23, 10, 16, 2, "getInjectEventScript"], [23, 30, 16, 2], [23, 33, 16, 2, "getInjectEventScript"], [23, 53, 16, 2], [24, 2, 18, 7], [24, 11, 18, 16, "getInjectBodySizeObserverScript"], [24, 42, 18, 47, "getInjectBodySizeObserverScript"], [24, 43, 18, 47], [24, 45, 18, 50], [25, 4, 19, 2], [25, 11, 19, 9], [26, 0, 20, 0], [27, 0, 21, 0], [28, 0, 22, 0], [29, 0, 23, 0], [30, 0, 24, 0], [30, 17, 24, 17, "MATCH_CONTENTS_EVENT"], [30, 37, 24, 37], [31, 0, 25, 0], [32, 0, 26, 0], [33, 0, 27, 0], [34, 0, 28, 0], [35, 0, 29, 0], [36, 0, 30, 0], [37, 0, 31, 0], [38, 0, 32, 0], [39, 0, 33, 0], [39, 15, 33, 15, "MATCH_CONTENTS_EVENT"], [39, 35, 33, 35], [40, 0, 34, 0], [41, 0, 35, 0], [42, 0, 36, 0], [43, 0, 37, 0], [44, 0, 38, 0], [45, 0, 39, 0], [46, 0, 40, 0], [47, 0, 41, 0], [47, 8, 41, 8], [48, 2, 42, 0], [49, 0, 42, 1], [49, 3]], "functionMap": {"names": ["<global>", "getInjectEventScript", "getInjectBodySizeObserverScript"], "mappings": "AAA;oCCQ;CDO;OEE;CFwB"}}, "type": "js/module"}]}