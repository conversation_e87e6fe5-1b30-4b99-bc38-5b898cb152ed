{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2F%40expo%2Fvector-icons%2Fbuild%2Fvendor%2Freact-native-vector-icons%2FFonts\",\n    \"scales\": [1],\n    \"hash\": \"f7c53c47a66934504fcbc7cc164895a7\",\n    \"name\": \"Octicons\",\n    \"type\": \"ttf\",\n    \"fileHashes\": [\"f7c53c47a66934504fcbc7cc164895a7\"]\n  });\n});", "lineCount": 11, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 145, 1, 165], [5, 4, 1, 166], [5, 12, 1, 174], [5, 14, 1, 175], [5, 15, 1, 176], [5, 16, 1, 177], [5, 17, 1, 178], [6, 4, 1, 179], [6, 10, 1, 185], [6, 12, 1, 186], [6, 46, 1, 220], [7, 4, 1, 221], [7, 10, 1, 227], [7, 12, 1, 228], [7, 22, 1, 238], [8, 4, 1, 239], [8, 10, 1, 245], [8, 12, 1, 246], [8, 17, 1, 251], [9, 4, 1, 252], [9, 16, 1, 264], [9, 18, 1, 265], [9, 19, 1, 266], [9, 53, 1, 300], [10, 2, 1, 301], [10, 3, 1, 302], [11, 0, 1, 302], [11, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}