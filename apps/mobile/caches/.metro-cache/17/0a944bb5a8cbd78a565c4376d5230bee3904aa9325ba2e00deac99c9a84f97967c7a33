{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/extends", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "yLIpKqfSeOZo7yhmpj6jeRbKj/A=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutPropertiesLoose", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 96, "index": 151}}], "key": "h/v2q98AsT4QTiU2QmCS7mQfUgY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 460}, "end": {"line": 14, "column": 31, "index": 491}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 492}, "end": {"line": 15, "column": 27, "index": 519}}], "key": "z+h67QhWT4Dd/ILcrpyPJ2FPLGs=", "exportNames": ["*"]}}, {"name": "../StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 520}, "end": {"line": 16, "column": 39, "index": 559}}], "key": "Pz10tXyA/z/1zTYUTTxDDbnOtjE=", "exportNames": ["*"]}}, {"name": "../../modules/canUseDom", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 560}, "end": {"line": 17, "column": 48, "index": 608}}], "key": "w0doQ61ImDsi56HxUhg3yNKNXVE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _extends2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/extends\"));\n  var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[4], \"../View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"../StyleSheet\"));\n  var _canUseDom = _interopRequireDefault(require(_dependencyMap[6], \"../../modules/canUseDom\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  var _excluded = [\"active\", \"children\", \"onRequestClose\", \"transparent\"];\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var ModalContent = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n    var active = props.active,\n      children = props.children,\n      onRequestClose = props.onRequestClose,\n      transparent = props.transparent,\n      rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n    React.useEffect(() => {\n      if (_canUseDom.default) {\n        var closeOnEscape = e => {\n          if (active && e.key === 'Escape') {\n            e.stopPropagation();\n            if (onRequestClose) {\n              onRequestClose();\n            }\n          }\n        };\n        document.addEventListener('keyup', closeOnEscape, false);\n        return () => document.removeEventListener('keyup', closeOnEscape, false);\n      }\n    }, [active, onRequestClose]);\n    var style = React.useMemo(() => {\n      return [styles.modal, transparent ? styles.modalTransparent : styles.modalOpaque];\n    }, [transparent]);\n    return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, {\n      \"aria-modal\": true,\n      ref: forwardedRef,\n      role: active ? 'dialog' : null,\n      style: style\n    }), /*#__PURE__*/React.createElement(_View.default, {\n      style: styles.container\n    }, children));\n  });\n  var styles = _StyleSheet.default.create({\n    modal: {\n      position: 'fixed',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    },\n    modalTransparent: {\n      backgroundColor: 'transparent'\n    },\n    modalOpaque: {\n      backgroundColor: 'white'\n    },\n    container: {\n      top: 0,\n      flex: 1\n    }\n  });\n  var _default = exports.default = ModalContent;\n});", "lineCount": 77, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_extends2"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_objectWithoutPropertiesLoose2"], [8, 36, 2, 0], [8, 39, 2, 0, "_interopRequireDefault"], [8, 61, 2, 0], [8, 62, 2, 0, "require"], [8, 69, 2, 0], [8, 70, 2, 0, "_dependencyMap"], [8, 84, 2, 0], [9, 2, 14, 0], [9, 6, 14, 0, "React"], [9, 11, 14, 0], [9, 14, 14, 0, "_interopRequireWildcard"], [9, 37, 14, 0], [9, 38, 14, 0, "require"], [9, 45, 14, 0], [9, 46, 14, 0, "_dependencyMap"], [9, 60, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_View"], [10, 11, 15, 0], [10, 14, 15, 0, "_interopRequireDefault"], [10, 36, 15, 0], [10, 37, 15, 0, "require"], [10, 44, 15, 0], [10, 45, 15, 0, "_dependencyMap"], [10, 59, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_StyleSheet"], [11, 17, 16, 0], [11, 20, 16, 0, "_interopRequireDefault"], [11, 42, 16, 0], [11, 43, 16, 0, "require"], [11, 50, 16, 0], [11, 51, 16, 0, "_dependencyMap"], [11, 65, 16, 0], [12, 2, 17, 0], [12, 6, 17, 0, "_canUseDom"], [12, 16, 17, 0], [12, 19, 17, 0, "_interopRequireDefault"], [12, 41, 17, 0], [12, 42, 17, 0, "require"], [12, 49, 17, 0], [12, 50, 17, 0, "_dependencyMap"], [12, 64, 17, 0], [13, 2, 17, 48], [13, 11, 17, 48, "_interopRequireWildcard"], [13, 35, 17, 48, "e"], [13, 36, 17, 48], [13, 38, 17, 48, "t"], [13, 39, 17, 48], [13, 68, 17, 48, "WeakMap"], [13, 75, 17, 48], [13, 81, 17, 48, "r"], [13, 82, 17, 48], [13, 89, 17, 48, "WeakMap"], [13, 96, 17, 48], [13, 100, 17, 48, "n"], [13, 101, 17, 48], [13, 108, 17, 48, "WeakMap"], [13, 115, 17, 48], [13, 127, 17, 48, "_interopRequireWildcard"], [13, 150, 17, 48], [13, 162, 17, 48, "_interopRequireWildcard"], [13, 163, 17, 48, "e"], [13, 164, 17, 48], [13, 166, 17, 48, "t"], [13, 167, 17, 48], [13, 176, 17, 48, "t"], [13, 177, 17, 48], [13, 181, 17, 48, "e"], [13, 182, 17, 48], [13, 186, 17, 48, "e"], [13, 187, 17, 48], [13, 188, 17, 48, "__esModule"], [13, 198, 17, 48], [13, 207, 17, 48, "e"], [13, 208, 17, 48], [13, 214, 17, 48, "o"], [13, 215, 17, 48], [13, 217, 17, 48, "i"], [13, 218, 17, 48], [13, 220, 17, 48, "f"], [13, 221, 17, 48], [13, 226, 17, 48, "__proto__"], [13, 235, 17, 48], [13, 243, 17, 48, "default"], [13, 250, 17, 48], [13, 252, 17, 48, "e"], [13, 253, 17, 48], [13, 270, 17, 48, "e"], [13, 271, 17, 48], [13, 294, 17, 48, "e"], [13, 295, 17, 48], [13, 320, 17, 48, "e"], [13, 321, 17, 48], [13, 330, 17, 48, "f"], [13, 331, 17, 48], [13, 337, 17, 48, "o"], [13, 338, 17, 48], [13, 341, 17, 48, "t"], [13, 342, 17, 48], [13, 345, 17, 48, "n"], [13, 346, 17, 48], [13, 349, 17, 48, "r"], [13, 350, 17, 48], [13, 358, 17, 48, "o"], [13, 359, 17, 48], [13, 360, 17, 48, "has"], [13, 363, 17, 48], [13, 364, 17, 48, "e"], [13, 365, 17, 48], [13, 375, 17, 48, "o"], [13, 376, 17, 48], [13, 377, 17, 48, "get"], [13, 380, 17, 48], [13, 381, 17, 48, "e"], [13, 382, 17, 48], [13, 385, 17, 48, "o"], [13, 386, 17, 48], [13, 387, 17, 48, "set"], [13, 390, 17, 48], [13, 391, 17, 48, "e"], [13, 392, 17, 48], [13, 394, 17, 48, "f"], [13, 395, 17, 48], [13, 411, 17, 48, "t"], [13, 412, 17, 48], [13, 416, 17, 48, "e"], [13, 417, 17, 48], [13, 433, 17, 48, "t"], [13, 434, 17, 48], [13, 441, 17, 48, "hasOwnProperty"], [13, 455, 17, 48], [13, 456, 17, 48, "call"], [13, 460, 17, 48], [13, 461, 17, 48, "e"], [13, 462, 17, 48], [13, 464, 17, 48, "t"], [13, 465, 17, 48], [13, 472, 17, 48, "i"], [13, 473, 17, 48], [13, 477, 17, 48, "o"], [13, 478, 17, 48], [13, 481, 17, 48, "Object"], [13, 487, 17, 48], [13, 488, 17, 48, "defineProperty"], [13, 502, 17, 48], [13, 507, 17, 48, "Object"], [13, 513, 17, 48], [13, 514, 17, 48, "getOwnPropertyDescriptor"], [13, 538, 17, 48], [13, 539, 17, 48, "e"], [13, 540, 17, 48], [13, 542, 17, 48, "t"], [13, 543, 17, 48], [13, 550, 17, 48, "i"], [13, 551, 17, 48], [13, 552, 17, 48, "get"], [13, 555, 17, 48], [13, 559, 17, 48, "i"], [13, 560, 17, 48], [13, 561, 17, 48, "set"], [13, 564, 17, 48], [13, 568, 17, 48, "o"], [13, 569, 17, 48], [13, 570, 17, 48, "f"], [13, 571, 17, 48], [13, 573, 17, 48, "t"], [13, 574, 17, 48], [13, 576, 17, 48, "i"], [13, 577, 17, 48], [13, 581, 17, 48, "f"], [13, 582, 17, 48], [13, 583, 17, 48, "t"], [13, 584, 17, 48], [13, 588, 17, 48, "e"], [13, 589, 17, 48], [13, 590, 17, 48, "t"], [13, 591, 17, 48], [13, 602, 17, 48, "f"], [13, 603, 17, 48], [13, 608, 17, 48, "e"], [13, 609, 17, 48], [13, 611, 17, 48, "t"], [13, 612, 17, 48], [14, 2, 3, 0], [14, 6, 3, 4, "_excluded"], [14, 15, 3, 13], [14, 18, 3, 16], [14, 19, 3, 17], [14, 27, 3, 25], [14, 29, 3, 27], [14, 39, 3, 37], [14, 41, 3, 39], [14, 57, 3, 55], [14, 59, 3, 57], [14, 72, 3, 70], [14, 73, 3, 71], [15, 2, 4, 0], [16, 0, 5, 0], [17, 0, 6, 0], [18, 0, 7, 0], [19, 0, 8, 0], [20, 0, 9, 0], [21, 0, 10, 0], [22, 0, 11, 0], [23, 0, 12, 0], [25, 2, 18, 0], [25, 6, 18, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [25, 18, 18, 16], [25, 21, 18, 19], [25, 34, 18, 32, "React"], [25, 39, 18, 37], [25, 40, 18, 38, "forwardRef"], [25, 50, 18, 48], [25, 51, 18, 49], [25, 52, 18, 50, "props"], [25, 57, 18, 55], [25, 59, 18, 57, "forwardedRef"], [25, 71, 18, 69], [25, 76, 18, 74], [26, 4, 19, 2], [26, 8, 19, 6, "active"], [26, 14, 19, 12], [26, 17, 19, 15, "props"], [26, 22, 19, 20], [26, 23, 19, 21, "active"], [26, 29, 19, 27], [27, 6, 20, 4, "children"], [27, 14, 20, 12], [27, 17, 20, 15, "props"], [27, 22, 20, 20], [27, 23, 20, 21, "children"], [27, 31, 20, 29], [28, 6, 21, 4, "onRequestClose"], [28, 20, 21, 18], [28, 23, 21, 21, "props"], [28, 28, 21, 26], [28, 29, 21, 27, "onRequestClose"], [28, 43, 21, 41], [29, 6, 22, 4, "transparent"], [29, 17, 22, 15], [29, 20, 22, 18, "props"], [29, 25, 22, 23], [29, 26, 22, 24, "transparent"], [29, 37, 22, 35], [30, 6, 23, 4, "rest"], [30, 10, 23, 8], [30, 13, 23, 11], [30, 17, 23, 11, "_objectWithoutPropertiesLoose"], [30, 55, 23, 40], [30, 57, 23, 41, "props"], [30, 62, 23, 46], [30, 64, 23, 48, "_excluded"], [30, 73, 23, 57], [30, 74, 23, 58], [31, 4, 24, 2, "React"], [31, 9, 24, 7], [31, 10, 24, 8, "useEffect"], [31, 19, 24, 17], [31, 20, 24, 18], [31, 26, 24, 24], [32, 6, 25, 4], [32, 10, 25, 8, "canUseDOM"], [32, 28, 25, 17], [32, 30, 25, 19], [33, 8, 26, 6], [33, 12, 26, 10, "closeOnEscape"], [33, 25, 26, 23], [33, 28, 26, 26, "e"], [33, 29, 26, 27], [33, 33, 26, 31], [34, 10, 27, 8], [34, 14, 27, 12, "active"], [34, 20, 27, 18], [34, 24, 27, 22, "e"], [34, 25, 27, 23], [34, 26, 27, 24, "key"], [34, 29, 27, 27], [34, 34, 27, 32], [34, 42, 27, 40], [34, 44, 27, 42], [35, 12, 28, 10, "e"], [35, 13, 28, 11], [35, 14, 28, 12, "stopPropagation"], [35, 29, 28, 27], [35, 30, 28, 28], [35, 31, 28, 29], [36, 12, 29, 10], [36, 16, 29, 14, "onRequestClose"], [36, 30, 29, 28], [36, 32, 29, 30], [37, 14, 30, 12, "onRequestClose"], [37, 28, 30, 26], [37, 29, 30, 27], [37, 30, 30, 28], [38, 12, 31, 10], [39, 10, 32, 8], [40, 8, 33, 6], [40, 9, 33, 7], [41, 8, 34, 6, "document"], [41, 16, 34, 14], [41, 17, 34, 15, "addEventListener"], [41, 33, 34, 31], [41, 34, 34, 32], [41, 41, 34, 39], [41, 43, 34, 41, "closeOnEscape"], [41, 56, 34, 54], [41, 58, 34, 56], [41, 63, 34, 61], [41, 64, 34, 62], [42, 8, 35, 6], [42, 15, 35, 13], [42, 21, 35, 19, "document"], [42, 29, 35, 27], [42, 30, 35, 28, "removeEventListener"], [42, 49, 35, 47], [42, 50, 35, 48], [42, 57, 35, 55], [42, 59, 35, 57, "closeOnEscape"], [42, 72, 35, 70], [42, 74, 35, 72], [42, 79, 35, 77], [42, 80, 35, 78], [43, 6, 36, 4], [44, 4, 37, 2], [44, 5, 37, 3], [44, 7, 37, 5], [44, 8, 37, 6, "active"], [44, 14, 37, 12], [44, 16, 37, 14, "onRequestClose"], [44, 30, 37, 28], [44, 31, 37, 29], [44, 32, 37, 30], [45, 4, 38, 2], [45, 8, 38, 6, "style"], [45, 13, 38, 11], [45, 16, 38, 14, "React"], [45, 21, 38, 19], [45, 22, 38, 20, "useMemo"], [45, 29, 38, 27], [45, 30, 38, 28], [45, 36, 38, 34], [46, 6, 39, 4], [46, 13, 39, 11], [46, 14, 39, 12, "styles"], [46, 20, 39, 18], [46, 21, 39, 19, "modal"], [46, 26, 39, 24], [46, 28, 39, 26, "transparent"], [46, 39, 39, 37], [46, 42, 39, 40, "styles"], [46, 48, 39, 46], [46, 49, 39, 47, "modalTransparent"], [46, 65, 39, 63], [46, 68, 39, 66, "styles"], [46, 74, 39, 72], [46, 75, 39, 73, "modalOpaque"], [46, 86, 39, 84], [46, 87, 39, 85], [47, 4, 40, 2], [47, 5, 40, 3], [47, 7, 40, 5], [47, 8, 40, 6, "transparent"], [47, 19, 40, 17], [47, 20, 40, 18], [47, 21, 40, 19], [48, 4, 41, 2], [48, 11, 41, 9], [48, 24, 41, 22, "React"], [48, 29, 41, 27], [48, 30, 41, 28, "createElement"], [48, 43, 41, 41], [48, 44, 41, 42, "View"], [48, 57, 41, 46], [48, 59, 41, 48], [48, 63, 41, 48, "_extends"], [48, 80, 41, 56], [48, 82, 41, 57], [48, 83, 41, 58], [48, 84, 41, 59], [48, 86, 41, 61, "rest"], [48, 90, 41, 65], [48, 92, 41, 67], [49, 6, 42, 4], [49, 18, 42, 16], [49, 20, 42, 18], [49, 24, 42, 22], [50, 6, 43, 4, "ref"], [50, 9, 43, 7], [50, 11, 43, 9, "forwardedRef"], [50, 23, 43, 21], [51, 6, 44, 4, "role"], [51, 10, 44, 8], [51, 12, 44, 10, "active"], [51, 18, 44, 16], [51, 21, 44, 19], [51, 29, 44, 27], [51, 32, 44, 30], [51, 36, 44, 34], [52, 6, 45, 4, "style"], [52, 11, 45, 9], [52, 13, 45, 11, "style"], [53, 4, 46, 2], [53, 5, 46, 3], [53, 6, 46, 4], [53, 8, 46, 6], [53, 21, 46, 19, "React"], [53, 26, 46, 24], [53, 27, 46, 25, "createElement"], [53, 40, 46, 38], [53, 41, 46, 39, "View"], [53, 54, 46, 43], [53, 56, 46, 45], [54, 6, 47, 4, "style"], [54, 11, 47, 9], [54, 13, 47, 11, "styles"], [54, 19, 47, 17], [54, 20, 47, 18, "container"], [55, 4, 48, 2], [55, 5, 48, 3], [55, 7, 48, 5, "children"], [55, 15, 48, 13], [55, 16, 48, 14], [55, 17, 48, 15], [56, 2, 49, 0], [56, 3, 49, 1], [56, 4, 49, 2], [57, 2, 50, 0], [57, 6, 50, 4, "styles"], [57, 12, 50, 10], [57, 15, 50, 13, "StyleSheet"], [57, 34, 50, 23], [57, 35, 50, 24, "create"], [57, 41, 50, 30], [57, 42, 50, 31], [58, 4, 51, 2, "modal"], [58, 9, 51, 7], [58, 11, 51, 9], [59, 6, 52, 4, "position"], [59, 14, 52, 12], [59, 16, 52, 14], [59, 23, 52, 21], [60, 6, 53, 4, "top"], [60, 9, 53, 7], [60, 11, 53, 9], [60, 12, 53, 10], [61, 6, 54, 4, "right"], [61, 11, 54, 9], [61, 13, 54, 11], [61, 14, 54, 12], [62, 6, 55, 4, "bottom"], [62, 12, 55, 10], [62, 14, 55, 12], [62, 15, 55, 13], [63, 6, 56, 4, "left"], [63, 10, 56, 8], [63, 12, 56, 10], [64, 4, 57, 2], [64, 5, 57, 3], [65, 4, 58, 2, "modalTransparent"], [65, 20, 58, 18], [65, 22, 58, 20], [66, 6, 59, 4, "backgroundColor"], [66, 21, 59, 19], [66, 23, 59, 21], [67, 4, 60, 2], [67, 5, 60, 3], [68, 4, 61, 2, "modalOpaque"], [68, 15, 61, 13], [68, 17, 61, 15], [69, 6, 62, 4, "backgroundColor"], [69, 21, 62, 19], [69, 23, 62, 21], [70, 4, 63, 2], [70, 5, 63, 3], [71, 4, 64, 2, "container"], [71, 13, 64, 11], [71, 15, 64, 13], [72, 6, 65, 4, "top"], [72, 9, 65, 7], [72, 11, 65, 9], [72, 12, 65, 10], [73, 6, 66, 4, "flex"], [73, 10, 66, 8], [73, 12, 66, 10], [74, 4, 67, 2], [75, 2, 68, 0], [75, 3, 68, 1], [75, 4, 68, 2], [76, 2, 68, 3], [76, 6, 68, 3, "_default"], [76, 14, 68, 3], [76, 17, 68, 3, "exports"], [76, 24, 68, 3], [76, 25, 68, 3, "default"], [76, 32, 68, 3], [76, 35, 69, 15, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [76, 47, 69, 27], [77, 0, 69, 27], [77, 3]], "functionMap": {"names": ["<global>", "React.forwardRef$argument_0", "React.useEffect$argument_0", "closeOnEscape", "<anonymous>", "React.useMemo$argument_0"], "mappings": "AAA;iDCiB;kBCM;0BCE;ODO;aEE,iEF;GDE;4BIC;GJE;CDS"}}, "type": "js/module"}]}