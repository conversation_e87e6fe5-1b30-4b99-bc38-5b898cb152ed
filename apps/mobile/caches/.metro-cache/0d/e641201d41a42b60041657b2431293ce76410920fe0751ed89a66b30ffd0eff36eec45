{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/bottom-tabs", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 22, "index": 252}, "end": {"line": 7, "column": 62, "index": 292}}], "key": "m8TZNYcjy2xLWr+rMb/67UFC1Gg=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 326}, "end": {"line": 8, "column": 48, "index": 342}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 23, "index": 368}, "end": {"line": 9, "column": 46, "index": 391}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "./withLayoutContext", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 28, "index": 421}, "end": {"line": 10, "column": 58, "index": 451}}], "key": "uI8DQ+0pBl5vWiQx60egJpSWI0Q=", "exportNames": ["*"]}}, {"name": "../link/Link", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 15, "index": 468}, "end": {"line": 11, "column": 38, "index": 491}}], "key": "mhtKivJ8lKis6RX59tHJXGvjzOc=", "exportNames": ["*"]}}, {"name": "./TabRouter", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 20, "index": 513}, "end": {"line": 12, "column": 42, "index": 535}}], "key": "9i/09DeG3ME2rBI+RqLtWit4PVg=", "exportNames": ["*"]}}, {"name": "../views/Protected", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 20, "index": 557}, "end": {"line": 13, "column": 49, "index": 586}}], "key": "k1+uDYZ/MvJqE4WVPvI1cbQswMs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _objectWithoutProperties = require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"href\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/layouts/TabsClient.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var bottom_tabs_1 = require(_dependencyMap[2], \"@react-navigation/bottom-tabs\");\n  var react_1 = __importDefault(require(_dependencyMap[3], \"react\"));\n  var react_native_1 = require(_dependencyMap[4], \"react-native\");\n  var withLayoutContext_1 = require(_dependencyMap[5], \"./withLayoutContext\");\n  var Link_1 = require(_dependencyMap[6], \"../link/Link\");\n  var TabRouter_1 = require(_dependencyMap[7], \"./TabRouter\");\n  var Protected_1 = require(_dependencyMap[8], \"../views/Protected\");\n  // This is the only way to access the navigator.\n  var BottomTabNavigator = (0, bottom_tabs_1.createBottomTabNavigator)().Navigator;\n  var ExpoTabs = (0, withLayoutContext_1.withLayoutContext)(BottomTabNavigator, screens => {\n    // Support the `href` shortcut prop.\n    return screens.map(screen => {\n      if (typeof screen.options !== 'function' && screen.options?.href !== undefined) {\n        var _screen$options = screen.options,\n          href = _screen$options.href,\n          options = _objectWithoutProperties(_screen$options, _excluded);\n        if (options.tabBarButton) {\n          throw new Error('Cannot use `href` and `tabBarButton` together.');\n        }\n        return {\n          ...screen,\n          options: {\n            ...options,\n            tabBarItemStyle: href == null ? {\n              display: 'none'\n            } : options.tabBarItemStyle,\n            // @ts-expect-error: TODO(@kitten): This isn't properly typed\n            tabBarButton: props => {\n              if (href == null) {\n                return null;\n              }\n              var children = react_native_1.Platform.OS === 'web' ? props.children : _reactNativeCssInteropJsxRuntime.jsx(react_native_1.Pressable, {\n                children: props.children\n              });\n              // TODO: React Navigation types these props as Animated.WithAnimatedValue<StyleProp<ViewStyle>>\n              //       While Link expects a TextStyle. We need to reconcile these types.\n              return _reactNativeCssInteropJsxRuntime.jsx(Link_1.Link, {\n                ...props,\n                style: [{\n                  display: 'flex'\n                }, props.style],\n                href: href,\n                asChild: react_native_1.Platform.OS !== 'web',\n                children: children\n              });\n            }\n          }\n        };\n      }\n      return screen;\n    });\n  });\n  var Tabs = Object.assign(props => {\n    return _reactNativeCssInteropJsxRuntime.jsx(ExpoTabs, {\n      ...props,\n      UNSTABLE_router: TabRouter_1.tabRouterOverride\n    });\n  }, {\n    Screen: ExpoTabs.Screen,\n    Protected: Protected_1.Protected\n  });\n  exports.default = Tabs;\n});", "lineCount": 79, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_reactNativeCssInteropJsxRuntime"], [5, 38, 2, 13], [5, 41, 2, 13, "require"], [5, 48, 2, 13], [5, 49, 2, 13, "_dependencyMap"], [5, 63, 2, 13], [6, 2, 2, 13], [6, 6, 2, 13, "_objectWithoutProperties"], [6, 30, 2, 13], [6, 33, 2, 13, "require"], [6, 40, 2, 13], [6, 41, 2, 13, "_dependencyMap"], [6, 55, 2, 13], [7, 2, 2, 13], [7, 6, 2, 13, "_excluded"], [7, 15, 2, 13], [8, 2, 2, 13], [8, 6, 2, 13, "_jsxFileName"], [8, 18, 2, 13], [9, 2, 3, 0], [9, 6, 3, 4, "__importDefault"], [9, 21, 3, 19], [9, 24, 3, 23], [9, 28, 3, 27], [9, 32, 3, 31], [9, 36, 3, 35], [9, 37, 3, 36, "__importDefault"], [9, 52, 3, 51], [9, 56, 3, 56], [9, 66, 3, 66, "mod"], [9, 69, 3, 69], [9, 71, 3, 71], [10, 4, 4, 4], [10, 11, 4, 12, "mod"], [10, 14, 4, 15], [10, 18, 4, 19, "mod"], [10, 21, 4, 22], [10, 22, 4, 23, "__esModule"], [10, 32, 4, 33], [10, 35, 4, 37, "mod"], [10, 38, 4, 40], [10, 41, 4, 43], [11, 6, 4, 45], [11, 15, 4, 54], [11, 17, 4, 56, "mod"], [12, 4, 4, 60], [12, 5, 4, 61], [13, 2, 5, 0], [13, 3, 5, 1], [14, 2, 6, 0, "Object"], [14, 8, 6, 6], [14, 9, 6, 7, "defineProperty"], [14, 23, 6, 21], [14, 24, 6, 22, "exports"], [14, 31, 6, 29], [14, 33, 6, 31], [14, 45, 6, 43], [14, 47, 6, 45], [15, 4, 6, 47, "value"], [15, 9, 6, 52], [15, 11, 6, 54], [16, 2, 6, 59], [16, 3, 6, 60], [16, 4, 6, 61], [17, 2, 7, 0], [17, 6, 7, 6, "bottom_tabs_1"], [17, 19, 7, 19], [17, 22, 7, 22, "require"], [17, 29, 7, 29], [17, 30, 7, 29, "_dependencyMap"], [17, 44, 7, 29], [17, 80, 7, 61], [17, 81, 7, 62], [18, 2, 8, 0], [18, 6, 8, 6, "react_1"], [18, 13, 8, 13], [18, 16, 8, 16, "__importDefault"], [18, 31, 8, 31], [18, 32, 8, 32, "require"], [18, 39, 8, 39], [18, 40, 8, 39, "_dependencyMap"], [18, 54, 8, 39], [18, 66, 8, 47], [18, 67, 8, 48], [18, 68, 8, 49], [19, 2, 9, 0], [19, 6, 9, 6, "react_native_1"], [19, 20, 9, 20], [19, 23, 9, 23, "require"], [19, 30, 9, 30], [19, 31, 9, 30, "_dependencyMap"], [19, 45, 9, 30], [19, 64, 9, 45], [19, 65, 9, 46], [20, 2, 10, 0], [20, 6, 10, 6, "withLayoutContext_1"], [20, 25, 10, 25], [20, 28, 10, 28, "require"], [20, 35, 10, 35], [20, 36, 10, 35, "_dependencyMap"], [20, 50, 10, 35], [20, 76, 10, 57], [20, 77, 10, 58], [21, 2, 11, 0], [21, 6, 11, 6, "Link_1"], [21, 12, 11, 12], [21, 15, 11, 15, "require"], [21, 22, 11, 22], [21, 23, 11, 22, "_dependencyMap"], [21, 37, 11, 22], [21, 56, 11, 37], [21, 57, 11, 38], [22, 2, 12, 0], [22, 6, 12, 6, "TabRouter_1"], [22, 17, 12, 17], [22, 20, 12, 20, "require"], [22, 27, 12, 27], [22, 28, 12, 27, "_dependencyMap"], [22, 42, 12, 27], [22, 60, 12, 41], [22, 61, 12, 42], [23, 2, 13, 0], [23, 6, 13, 6, "Protected_1"], [23, 17, 13, 17], [23, 20, 13, 20, "require"], [23, 27, 13, 27], [23, 28, 13, 27, "_dependencyMap"], [23, 42, 13, 27], [23, 67, 13, 48], [23, 68, 13, 49], [24, 2, 14, 0], [25, 2, 15, 0], [25, 6, 15, 6, "BottomTabNavigator"], [25, 24, 15, 24], [25, 27, 15, 27], [25, 28, 15, 28], [25, 29, 15, 29], [25, 31, 15, 31, "bottom_tabs_1"], [25, 44, 15, 44], [25, 45, 15, 45, "createBottomTabNavigator"], [25, 69, 15, 69], [25, 71, 15, 71], [25, 72, 15, 72], [25, 73, 15, 73, "Navigator"], [25, 82, 15, 82], [26, 2, 16, 0], [26, 6, 16, 6, "ExpoTabs"], [26, 14, 16, 14], [26, 17, 16, 17], [26, 18, 16, 18], [26, 19, 16, 19], [26, 21, 16, 21, "withLayoutContext_1"], [26, 40, 16, 40], [26, 41, 16, 41, "withLayoutContext"], [26, 58, 16, 58], [26, 60, 16, 60, "BottomTabNavigator"], [26, 78, 16, 78], [26, 80, 16, 81, "screens"], [26, 87, 16, 88], [26, 91, 16, 93], [27, 4, 17, 4], [28, 4, 18, 4], [28, 11, 18, 11, "screens"], [28, 18, 18, 18], [28, 19, 18, 19, "map"], [28, 22, 18, 22], [28, 23, 18, 24, "screen"], [28, 29, 18, 30], [28, 33, 18, 35], [29, 6, 19, 8], [29, 10, 19, 12], [29, 17, 19, 19, "screen"], [29, 23, 19, 25], [29, 24, 19, 26, "options"], [29, 31, 19, 33], [29, 36, 19, 38], [29, 46, 19, 48], [29, 50, 19, 52, "screen"], [29, 56, 19, 58], [29, 57, 19, 59, "options"], [29, 64, 19, 66], [29, 66, 19, 68, "href"], [29, 70, 19, 72], [29, 75, 19, 77, "undefined"], [29, 84, 19, 86], [29, 86, 19, 88], [30, 8, 20, 12], [30, 12, 20, 12, "_screen$options"], [30, 27, 20, 12], [30, 30, 20, 41, "screen"], [30, 36, 20, 47], [30, 37, 20, 48, "options"], [30, 44, 20, 55], [31, 10, 20, 20, "href"], [31, 14, 20, 24], [31, 17, 20, 24, "_screen$options"], [31, 32, 20, 24], [31, 33, 20, 20, "href"], [31, 37, 20, 24], [32, 10, 20, 29, "options"], [32, 17, 20, 36], [32, 20, 20, 36, "_objectWithoutProperties"], [32, 44, 20, 36], [32, 45, 20, 36, "_screen$options"], [32, 60, 20, 36], [32, 62, 20, 36, "_excluded"], [32, 71, 20, 36], [33, 8, 21, 12], [33, 12, 21, 16, "options"], [33, 19, 21, 23], [33, 20, 21, 24, "tabBarButton"], [33, 32, 21, 36], [33, 34, 21, 38], [34, 10, 22, 16], [34, 16, 22, 22], [34, 20, 22, 26, "Error"], [34, 25, 22, 31], [34, 26, 22, 32], [34, 74, 22, 80], [34, 75, 22, 81], [35, 8, 23, 12], [36, 8, 24, 12], [36, 15, 24, 19], [37, 10, 25, 16], [37, 13, 25, 19, "screen"], [37, 19, 25, 25], [38, 10, 26, 16, "options"], [38, 17, 26, 23], [38, 19, 26, 25], [39, 12, 27, 20], [39, 15, 27, 23, "options"], [39, 22, 27, 30], [40, 12, 28, 20, "tabBarItemStyle"], [40, 27, 28, 35], [40, 29, 28, 37, "href"], [40, 33, 28, 41], [40, 37, 28, 45], [40, 41, 28, 49], [40, 44, 28, 52], [41, 14, 28, 54, "display"], [41, 21, 28, 61], [41, 23, 28, 63], [42, 12, 28, 70], [42, 13, 28, 71], [42, 16, 28, 74, "options"], [42, 23, 28, 81], [42, 24, 28, 82, "tabBarItemStyle"], [42, 39, 28, 97], [43, 12, 29, 20], [44, 12, 30, 20, "tabBarButton"], [44, 24, 30, 32], [44, 26, 30, 35, "props"], [44, 31, 30, 40], [44, 35, 30, 45], [45, 14, 31, 24], [45, 18, 31, 28, "href"], [45, 22, 31, 32], [45, 26, 31, 36], [45, 30, 31, 40], [45, 32, 31, 42], [46, 16, 32, 28], [46, 23, 32, 35], [46, 27, 32, 39], [47, 14, 33, 24], [48, 14, 34, 24], [48, 18, 34, 30, "children"], [48, 26, 34, 38], [48, 29, 34, 41, "react_native_1"], [48, 43, 34, 55], [48, 44, 34, 56, "Platform"], [48, 52, 34, 64], [48, 53, 34, 65, "OS"], [48, 55, 34, 67], [48, 60, 34, 72], [48, 65, 34, 77], [48, 68, 34, 80, "props"], [48, 73, 34, 85], [48, 74, 34, 86, "children"], [48, 82, 34, 94], [48, 85, 34, 97, "_reactNativeCssInteropJsxRuntime"], [48, 117, 34, 97], [48, 118, 34, 97, "jsx"], [48, 121, 34, 97], [48, 122, 34, 98, "react_native_1"], [48, 136, 34, 112], [48, 137, 34, 113, "Pressable"], [48, 146, 34, 122], [49, 16, 34, 122, "children"], [49, 24, 34, 122], [49, 26, 34, 124, "props"], [49, 31, 34, 129], [49, 32, 34, 130, "children"], [50, 14, 34, 138], [50, 15, 34, 165], [50, 16, 34, 166], [51, 14, 35, 24], [52, 14, 36, 24], [53, 14, 37, 24], [53, 21, 37, 32, "_reactNativeCssInteropJsxRuntime"], [53, 53, 37, 32], [53, 54, 37, 32, "jsx"], [53, 57, 37, 32], [53, 58, 37, 33, "Link_1"], [53, 64, 37, 39], [53, 65, 37, 40, "Link"], [53, 69, 37, 44], [54, 16, 37, 44], [54, 19, 37, 49, "props"], [54, 24, 37, 54], [55, 16, 37, 56, "style"], [55, 21, 37, 61], [55, 23, 37, 63], [55, 24, 37, 64], [56, 18, 37, 66, "display"], [56, 25, 37, 73], [56, 27, 37, 75], [57, 16, 37, 82], [57, 17, 37, 83], [57, 19, 37, 85, "props"], [57, 24, 37, 90], [57, 25, 37, 91, "style"], [57, 30, 37, 96], [57, 31, 37, 98], [58, 16, 37, 99, "href"], [58, 20, 37, 103], [58, 22, 37, 105, "href"], [58, 26, 37, 110], [59, 16, 37, 111, "<PERSON><PERSON><PERSON><PERSON>"], [59, 23, 37, 118], [59, 25, 37, 120, "react_native_1"], [59, 39, 37, 134], [59, 40, 37, 135, "Platform"], [59, 48, 37, 143], [59, 49, 37, 144, "OS"], [59, 51, 37, 146], [59, 56, 37, 151], [59, 61, 37, 157], [60, 16, 37, 158, "children"], [60, 24, 37, 166], [60, 26, 37, 168, "children"], [61, 14, 37, 177], [61, 15, 37, 178], [61, 16, 37, 179], [62, 12, 38, 20], [63, 10, 39, 16], [64, 8, 40, 12], [64, 9, 40, 13], [65, 6, 41, 8], [66, 6, 42, 8], [66, 13, 42, 15, "screen"], [66, 19, 42, 21], [67, 4, 43, 4], [67, 5, 43, 5], [67, 6, 43, 6], [68, 2, 44, 0], [68, 3, 44, 1], [68, 4, 44, 2], [69, 2, 45, 0], [69, 6, 45, 6, "Tabs"], [69, 10, 45, 10], [69, 13, 45, 13, "Object"], [69, 19, 45, 19], [69, 20, 45, 20, "assign"], [69, 26, 45, 26], [69, 27, 45, 28, "props"], [69, 32, 45, 33], [69, 36, 45, 38], [70, 4, 46, 4], [70, 11, 46, 11, "_reactNativeCssInteropJsxRuntime"], [70, 43, 46, 11], [70, 44, 46, 11, "jsx"], [70, 47, 46, 11], [70, 48, 46, 12, "ExpoTabs"], [70, 56, 46, 20], [71, 6, 46, 20], [71, 9, 46, 25, "props"], [71, 14, 46, 30], [72, 6, 46, 32, "UNSTABLE_router"], [72, 21, 46, 47], [72, 23, 46, 49, "TabRouter_1"], [72, 34, 46, 60], [72, 35, 46, 61, "tabRouterOverride"], [73, 4, 46, 79], [73, 5, 46, 80], [73, 6, 46, 81], [74, 2, 47, 0], [74, 3, 47, 1], [74, 5, 47, 3], [75, 4, 48, 4, "Screen"], [75, 10, 48, 10], [75, 12, 48, 12, "ExpoTabs"], [75, 20, 48, 20], [75, 21, 48, 21, "Screen"], [75, 27, 48, 27], [76, 4, 49, 4, "Protected"], [76, 13, 49, 13], [76, 15, 49, 15, "Protected_1"], [76, 26, 49, 26], [76, 27, 49, 27, "Protected"], [77, 2, 50, 0], [77, 3, 50, 1], [77, 4, 50, 2], [78, 2, 51, 0, "exports"], [78, 9, 51, 7], [78, 10, 51, 8, "default"], [78, 17, 51, 15], [78, 20, 51, 18, "Tabs"], [78, 24, 51, 22], [79, 0, 51, 23], [79, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "screens.map$argument_0", "options.tabBarButton", "Object.assign$argument_0"], "mappings": "AAA;wDCE;CDE;gFCW;uBCE;kCCY;qBDQ;KDK;CDC;2BIC;CJE"}}, "type": "js/module"}]}