{"dependencies": [], "output": [{"data": {"code": "__d(function(global, require, _importDefaultUnused, _importAllUnused, module, exports, _dependencyMapUnused) {\n  module.exports = {\n  \"name\": \"react-native\",\n  \"version\": \"0.79.3\",\n  \"description\": \"A framework for building native apps using React\",\n  \"license\": \"MIT\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/facebook/react-native.git\",\n    \"directory\": \"packages/react-native\"\n  },\n  \"homepage\": \"https://reactnative.dev/\",\n  \"keywords\": [\n    \"react\",\n    \"react-native\",\n    \"android\",\n    \"ios\",\n    \"mobile\",\n    \"cross-platform\",\n    \"app-framework\",\n    \"mobile-development\"\n  ],\n  \"bugs\": \"https://github.com/facebook/react-native/issues\",\n  \"engines\": {\n    \"node\": \">=18\"\n  },\n  \"bin\": {\n    \"react-native\": \"cli.js\"\n  },\n  \"types\": \"types\",\n  \"jest-junit\": {\n    \"outputDirectory\": \"reports/junit\",\n    \"outputName\": \"js-test-results.xml\"\n  },\n  \"files\": [\n    \"android\",\n    \"build.gradle.kts\",\n    \"cli.js\",\n    \"flow\",\n    \"gradle.properties\",\n    \"gradle/libs.versions.toml\",\n    \"index.js\",\n    \"interface.js\",\n    \"jest-preset.js\",\n    \"jest\",\n    \"Libraries\",\n    \"LICENSE\",\n    \"React-Core.podspec\",\n    \"react-native.config.js\",\n    \"React.podspec\",\n    \"React\",\n    \"!React/Fabric/RCTThirdPartyFabricComponentsProvider.*\",\n    \"ReactAndroid\",\n    \"ReactApple\",\n    \"ReactCommon\",\n    \"README.md\",\n    \"rn-get-polyfills.js\",\n    \"scripts/compose-source-maps.js\",\n    \"scripts/find-node-for-xcode.sh\",\n    \"scripts/bundle.js\",\n    \"scripts/generate-codegen-artifacts.js\",\n    \"scripts/generate-provider-cli.js\",\n    \"scripts/generate-specs-cli.js\",\n    \"scripts/codegen\",\n    \"!scripts/codegen/__tests__\",\n    \"!scripts/codegen/__test_fixtures__\",\n    \"scripts/hermes/hermes-utils.js\",\n    \"scripts/hermes/prepare-hermes-for-build.js\",\n    \"scripts/ios-configure-glog.sh\",\n    \"scripts/xcode/ccache-clang++.sh\",\n    \"scripts/xcode/ccache-clang.sh\",\n    \"scripts/xcode/ccache.conf\",\n    \"scripts/xcode/with-environment.sh\",\n    \"scripts/native_modules.rb\",\n    \"scripts/node-binary.sh\",\n    \"scripts/packager.sh\",\n    \"scripts/packager-reporter.js\",\n    \"scripts/react_native_pods_utils/script_phases.rb\",\n    \"scripts/react_native_pods_utils/script_phases.sh\",\n    \"scripts/react_native_pods.rb\",\n    \"scripts/cocoapods\",\n    \"!scripts/cocoapods/__tests__\",\n    \"scripts/react-native-xcode.sh\",\n    \"sdks/.hermesversion\",\n    \"sdks/hermes-engine\",\n    \"sdks/hermesc\",\n    \"settings.gradle.kts\",\n    \"src\",\n    \"template.config.js\",\n    \"template\",\n    \"!template/node_modules\",\n    \"!template/package-lock.json\",\n    \"!template/yarn.lock\",\n    \"third-party-podspecs\",\n    \"types\"\n  ],\n  \"scripts\": {\n    \"prepack\": \"node ./scripts/prepack.js\",\n    \"featureflags\": \"node ./scripts/featureflags/index.js\"\n  },\n  \"peerDependencies\": {\n    \"@types/react\": \"^19.0.0\",\n    \"react\": \"^19.0.0\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@types/react\": {\n      \"optional\": true\n    }\n  },\n  \"dependencies\": {\n    \"@jest/create-cache-key-function\": \"^29.7.0\",\n    \"@react-native/assets-registry\": \"0.79.3\",\n    \"@react-native/codegen\": \"0.79.3\",\n    \"@react-native/community-cli-plugin\": \"0.79.3\",\n    \"@react-native/gradle-plugin\": \"0.79.3\",\n    \"@react-native/js-polyfills\": \"0.79.3\",\n    \"@react-native/normalize-colors\": \"0.79.3\",\n    \"@react-native/virtualized-lists\": \"0.79.3\",\n    \"abort-controller\": \"^3.0.0\",\n    \"anser\": \"^1.4.9\",\n    \"ansi-regex\": \"^5.0.0\",\n    \"babel-jest\": \"^29.7.0\",\n    \"babel-plugin-syntax-hermes-parser\": \"0.25.1\",\n    \"base64-js\": \"^1.5.1\",\n    \"chalk\": \"^4.0.0\",\n    \"commander\": \"^12.0.0\",\n    \"event-target-shim\": \"^5.0.1\",\n    \"flow-enums-runtime\": \"^0.0.6\",\n    \"glob\": \"^7.1.1\",\n    \"invariant\": \"^2.2.4\",\n    \"jest-environment-node\": \"^29.7.0\",\n    \"memoize-one\": \"^5.0.0\",\n    \"metro-runtime\": \"^0.82.0\",\n    \"metro-source-map\": \"^0.82.0\",\n    \"nullthrows\": \"^1.1.1\",\n    \"pretty-format\": \"^29.7.0\",\n    \"promise\": \"^8.3.0\",\n    \"react-devtools-core\": \"^6.1.1\",\n    \"react-refresh\": \"^0.14.0\",\n    \"regenerator-runtime\": \"^0.13.2\",\n    \"scheduler\": \"0.25.0\",\n    \"semver\": \"^7.1.3\",\n    \"stacktrace-parser\": \"^0.1.10\",\n    \"whatwg-fetch\": \"^3.0.0\",\n    \"ws\": \"^6.2.3\",\n    \"yargs\": \"^17.6.2\"\n  },\n  \"codegenConfig\": {\n    \"libraries\": [\n      {\n        \"name\": \"FBReactNativeSpec\",\n        \"type\": \"modules\",\n        \"ios\": {},\n        \"android\": {},\n        \"jsSrcsDir\": \"src\"\n      },\n      {\n        \"name\": \"rncore\",\n        \"type\": \"components\",\n        \"ios\": {},\n        \"android\": {},\n        \"jsSrcsDir\": \"src\"\n      }\n    ]\n  }\n}\n;\n});", "lineCount": 168, "map": [[168, 3]], "functionMap": null}, "type": "js/module"}]}