{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  module.exports = (string, separator) => {\n    if (!(typeof string === 'string' && typeof separator === 'string')) {\n      throw new TypeError('Expected the arguments to be of type `string`');\n    }\n    if (separator === '') {\n      return [string];\n    }\n    const separatorIndex = string.indexOf(separator);\n    if (separatorIndex === -1) {\n      return [string];\n    }\n    return [string.slice(0, separatorIndex), string.slice(separatorIndex + separator.length)];\n  };\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "module"], [4, 8, 3, 6], [4, 9, 3, 7, "exports"], [4, 16, 3, 14], [4, 19, 3, 17], [4, 20, 3, 18, "string"], [4, 26, 3, 24], [4, 28, 3, 26, "separator"], [4, 37, 3, 35], [4, 42, 3, 40], [5, 4, 4, 1], [5, 8, 4, 5], [5, 10, 4, 7], [5, 17, 4, 14, "string"], [5, 23, 4, 20], [5, 28, 4, 25], [5, 36, 4, 33], [5, 40, 4, 37], [5, 47, 4, 44, "separator"], [5, 56, 4, 53], [5, 61, 4, 58], [5, 69, 4, 66], [5, 70, 4, 67], [5, 72, 4, 69], [6, 6, 5, 2], [6, 12, 5, 8], [6, 16, 5, 12, "TypeError"], [6, 25, 5, 21], [6, 26, 5, 22], [6, 73, 5, 69], [6, 74, 5, 70], [7, 4, 6, 1], [8, 4, 8, 1], [8, 8, 8, 5, "separator"], [8, 17, 8, 14], [8, 22, 8, 19], [8, 24, 8, 21], [8, 26, 8, 23], [9, 6, 9, 2], [9, 13, 9, 9], [9, 14, 9, 10, "string"], [9, 20, 9, 16], [9, 21, 9, 17], [10, 4, 10, 1], [11, 4, 12, 1], [11, 10, 12, 7, "separatorIndex"], [11, 24, 12, 21], [11, 27, 12, 24, "string"], [11, 33, 12, 30], [11, 34, 12, 31, "indexOf"], [11, 41, 12, 38], [11, 42, 12, 39, "separator"], [11, 51, 12, 48], [11, 52, 12, 49], [12, 4, 14, 1], [12, 8, 14, 5, "separatorIndex"], [12, 22, 14, 19], [12, 27, 14, 24], [12, 28, 14, 25], [12, 29, 14, 26], [12, 31, 14, 28], [13, 6, 15, 2], [13, 13, 15, 9], [13, 14, 15, 10, "string"], [13, 20, 15, 16], [13, 21, 15, 17], [14, 4, 16, 1], [15, 4, 18, 1], [15, 11, 18, 8], [15, 12, 19, 2, "string"], [15, 18, 19, 8], [15, 19, 19, 9, "slice"], [15, 24, 19, 14], [15, 25, 19, 15], [15, 26, 19, 16], [15, 28, 19, 18, "separatorIndex"], [15, 42, 19, 32], [15, 43, 19, 33], [15, 45, 20, 2, "string"], [15, 51, 20, 8], [15, 52, 20, 9, "slice"], [15, 57, 20, 14], [15, 58, 20, 15, "separatorIndex"], [15, 72, 20, 29], [15, 75, 20, 32, "separator"], [15, 84, 20, 41], [15, 85, 20, 42, "length"], [15, 91, 20, 48], [15, 92, 20, 49], [15, 93, 21, 2], [16, 2, 22, 0], [16, 3, 22, 1], [17, 0, 22, 2], [17, 3]], "functionMap": {"names": ["<global>", "module.exports"], "mappings": "AAA;iBCE;CDmB"}}, "type": "js/module"}]}