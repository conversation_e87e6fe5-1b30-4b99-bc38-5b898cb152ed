{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const School = exports.default = (0, _createLucideIcon.default)(\"School\", [[\"path\", {\n    d: \"M14 22v-4a2 2 0 1 0-4 0v4\",\n    key: \"hhkicm\"\n  }], [\"path\", {\n    d: \"m18 10 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7.382a1 1 0 0 1 .553-.894L6 10\",\n    key: \"1xqip1\"\n  }], [\"path\", {\n    d: \"M18 5v17\",\n    key: \"1sw6gf\"\n  }], [\"path\", {\n    d: \"m4 6 7.106-3.553a2 2 0 0 1 1.788 0L20 6\",\n    key: \"9d2mlk\"\n  }], [\"path\", {\n    d: \"M6 5v17\",\n    key: \"1xfsm0\"\n  }], [\"circle\", {\n    cx: \"12\",\n    cy: \"9\",\n    r: \"2\",\n    key: \"1092wv\"\n  }]]);\n});", "lineCount": 36, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "School"], [15, 14, 10, 12], [15, 17, 10, 12, "exports"], [15, 24, 10, 12], [15, 25, 10, 12, "default"], [15, 32, 10, 12], [15, 35, 10, 15], [15, 39, 10, 15, "createLucideIcon"], [15, 64, 10, 31], [15, 66, 10, 32], [15, 74, 10, 40], [15, 76, 10, 42], [15, 77, 11, 2], [15, 78, 11, 3], [15, 84, 11, 9], [15, 86, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 34, 11, 43], [17, 4, 11, 45, "key"], [17, 7, 11, 48], [17, 9, 11, 50], [18, 2, 11, 59], [18, 3, 11, 60], [18, 4, 11, 61], [18, 6, 12, 2], [18, 7, 13, 4], [18, 13, 13, 10], [18, 15, 14, 4], [19, 4, 15, 6, "d"], [19, 5, 15, 7], [19, 7, 15, 9], [19, 111, 15, 113], [20, 4, 16, 6, "key"], [20, 7, 16, 9], [20, 9, 16, 11], [21, 2, 17, 4], [21, 3, 17, 5], [21, 4, 18, 3], [21, 6, 19, 2], [21, 7, 19, 3], [21, 13, 19, 9], [21, 15, 19, 11], [22, 4, 19, 13, "d"], [22, 5, 19, 14], [22, 7, 19, 16], [22, 17, 19, 26], [23, 4, 19, 28, "key"], [23, 7, 19, 31], [23, 9, 19, 33], [24, 2, 19, 42], [24, 3, 19, 43], [24, 4, 19, 44], [24, 6, 20, 2], [24, 7, 20, 3], [24, 13, 20, 9], [24, 15, 20, 11], [25, 4, 20, 13, "d"], [25, 5, 20, 14], [25, 7, 20, 16], [25, 48, 20, 57], [26, 4, 20, 59, "key"], [26, 7, 20, 62], [26, 9, 20, 64], [27, 2, 20, 73], [27, 3, 20, 74], [27, 4, 20, 75], [27, 6, 21, 2], [27, 7, 21, 3], [27, 13, 21, 9], [27, 15, 21, 11], [28, 4, 21, 13, "d"], [28, 5, 21, 14], [28, 7, 21, 16], [28, 16, 21, 25], [29, 4, 21, 27, "key"], [29, 7, 21, 30], [29, 9, 21, 32], [30, 2, 21, 41], [30, 3, 21, 42], [30, 4, 21, 43], [30, 6, 22, 2], [30, 7, 22, 3], [30, 15, 22, 11], [30, 17, 22, 13], [31, 4, 22, 15, "cx"], [31, 6, 22, 17], [31, 8, 22, 19], [31, 12, 22, 23], [32, 4, 22, 25, "cy"], [32, 6, 22, 27], [32, 8, 22, 29], [32, 11, 22, 32], [33, 4, 22, 34, "r"], [33, 5, 22, 35], [33, 7, 22, 37], [33, 10, 22, 40], [34, 4, 22, 42, "key"], [34, 7, 22, 45], [34, 9, 22, 47], [35, 2, 22, 56], [35, 3, 22, 57], [35, 4, 22, 58], [35, 5, 23, 1], [35, 6, 23, 2], [36, 0, 23, 3], [36, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}