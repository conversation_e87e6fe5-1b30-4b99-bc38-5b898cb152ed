{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var GitFork = exports.default = (0, _createLucideIcon.default)(\"GitFork\", [[\"circle\", {\n    cx: \"12\",\n    cy: \"18\",\n    r: \"3\",\n    key: \"1mpf1b\"\n  }], [\"circle\", {\n    cx: \"6\",\n    cy: \"6\",\n    r: \"3\",\n    key: \"1lh9wr\"\n  }], [\"circle\", {\n    cx: \"18\",\n    cy: \"6\",\n    r: \"3\",\n    key: \"1h7g24\"\n  }], [\"path\", {\n    d: \"M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9\",\n    key: \"1uq4wg\"\n  }], [\"path\", {\n    d: \"M12 12v3\",\n    key: \"158kv8\"\n  }]]);\n});", "lineCount": 37, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "GitFork"], [15, 13, 10, 13], [15, 16, 10, 13, "exports"], [15, 23, 10, 13], [15, 24, 10, 13, "default"], [15, 31, 10, 13], [15, 34, 10, 16], [15, 38, 10, 16, "createLucideIcon"], [15, 63, 10, 32], [15, 65, 10, 33], [15, 74, 10, 42], [15, 76, 10, 44], [15, 77, 11, 2], [15, 78, 11, 3], [15, 86, 11, 11], [15, 88, 11, 13], [16, 4, 11, 15, "cx"], [16, 6, 11, 17], [16, 8, 11, 19], [16, 12, 11, 23], [17, 4, 11, 25, "cy"], [17, 6, 11, 27], [17, 8, 11, 29], [17, 12, 11, 33], [18, 4, 11, 35, "r"], [18, 5, 11, 36], [18, 7, 11, 38], [18, 10, 11, 41], [19, 4, 11, 43, "key"], [19, 7, 11, 46], [19, 9, 11, 48], [20, 2, 11, 57], [20, 3, 11, 58], [20, 4, 11, 59], [20, 6, 12, 2], [20, 7, 12, 3], [20, 15, 12, 11], [20, 17, 12, 13], [21, 4, 12, 15, "cx"], [21, 6, 12, 17], [21, 8, 12, 19], [21, 11, 12, 22], [22, 4, 12, 24, "cy"], [22, 6, 12, 26], [22, 8, 12, 28], [22, 11, 12, 31], [23, 4, 12, 33, "r"], [23, 5, 12, 34], [23, 7, 12, 36], [23, 10, 12, 39], [24, 4, 12, 41, "key"], [24, 7, 12, 44], [24, 9, 12, 46], [25, 2, 12, 55], [25, 3, 12, 56], [25, 4, 12, 57], [25, 6, 13, 2], [25, 7, 13, 3], [25, 15, 13, 11], [25, 17, 13, 13], [26, 4, 13, 15, "cx"], [26, 6, 13, 17], [26, 8, 13, 19], [26, 12, 13, 23], [27, 4, 13, 25, "cy"], [27, 6, 13, 27], [27, 8, 13, 29], [27, 11, 13, 32], [28, 4, 13, 34, "r"], [28, 5, 13, 35], [28, 7, 13, 37], [28, 10, 13, 40], [29, 4, 13, 42, "key"], [29, 7, 13, 45], [29, 9, 13, 47], [30, 2, 13, 56], [30, 3, 13, 57], [30, 4, 13, 58], [30, 6, 14, 2], [30, 7, 14, 3], [30, 13, 14, 9], [30, 15, 14, 11], [31, 4, 14, 13, "d"], [31, 5, 14, 14], [31, 7, 14, 16], [31, 49, 14, 58], [32, 4, 14, 60, "key"], [32, 7, 14, 63], [32, 9, 14, 65], [33, 2, 14, 74], [33, 3, 14, 75], [33, 4, 14, 76], [33, 6, 15, 2], [33, 7, 15, 3], [33, 13, 15, 9], [33, 15, 15, 11], [34, 4, 15, 13, "d"], [34, 5, 15, 14], [34, 7, 15, 16], [34, 17, 15, 26], [35, 4, 15, 28, "key"], [35, 7, 15, 31], [35, 9, 15, 33], [36, 2, 15, 42], [36, 3, 15, 43], [36, 4, 15, 44], [36, 5, 16, 1], [36, 6, 16, 2], [37, 0, 16, 3], [37, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}