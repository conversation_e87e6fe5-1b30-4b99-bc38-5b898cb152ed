{"dependencies": [{"name": "./navigators/createNativeStackNavigator.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 37}, "end": {"line": 6, "column": 88, "index": 125}}], "key": "wHBmEI4BLcUMCY3QIy0o/UAiD5s=", "exportNames": ["*"]}}, {"name": "./views/NativeStackView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 144}, "end": {"line": 11, "column": 58, "index": 202}}], "key": "EWXckuKxkWvgivn2y+9lwPoTMc8=", "exportNames": ["*"]}}, {"name": "./utils/useAnimatedHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 221}, "end": {"line": 16, "column": 77, "index": 298}}], "key": "XDqX266AMkf/t8WF1zZRgcKpAGs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Navigators\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"NativeStackView\", {\n    enumerable: true,\n    get: function () {\n      return _NativeStackView.NativeStackView;\n    }\n  });\n  Object.defineProperty(exports, \"createNativeStackNavigator\", {\n    enumerable: true,\n    get: function () {\n      return _createNativeStackNavigator.createNativeStackNavigator;\n    }\n  });\n  Object.defineProperty(exports, \"useAnimatedHeaderHeight\", {\n    enumerable: true,\n    get: function () {\n      return _useAnimatedHeaderHeight.useAnimatedHeaderHeight;\n    }\n  });\n  var _createNativeStackNavigator = require(_dependencyMap[0], \"./navigators/createNativeStackNavigator.js\");\n  var _NativeStackView = require(_dependencyMap[1], \"./views/NativeStackView\");\n  var _useAnimatedHeaderHeight = require(_dependencyMap[2], \"./utils/useAnimatedHeaderHeight.js\");\n});", "lineCount": 31, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0, "Object"], [7, 8, 3, 0], [7, 9, 3, 0, "defineProperty"], [7, 23, 3, 0], [7, 24, 3, 0, "exports"], [7, 31, 3, 0], [8, 4, 3, 0, "value"], [8, 9, 3, 0], [9, 2, 3, 0], [10, 2, 3, 0, "Object"], [10, 8, 3, 0], [10, 9, 3, 0, "defineProperty"], [10, 23, 3, 0], [10, 24, 3, 0, "exports"], [10, 31, 3, 0], [11, 4, 3, 0, "enumerable"], [11, 14, 3, 0], [12, 4, 3, 0, "get"], [12, 7, 3, 0], [12, 18, 3, 0, "get"], [12, 19, 3, 0], [13, 6, 3, 0], [13, 13, 3, 0, "_NativeStackView"], [13, 29, 3, 0], [13, 30, 3, 0, "NativeStackView"], [13, 45, 3, 0], [14, 4, 3, 0], [15, 2, 3, 0], [16, 2, 3, 0, "Object"], [16, 8, 3, 0], [16, 9, 3, 0, "defineProperty"], [16, 23, 3, 0], [16, 24, 3, 0, "exports"], [16, 31, 3, 0], [17, 4, 3, 0, "enumerable"], [17, 14, 3, 0], [18, 4, 3, 0, "get"], [18, 7, 3, 0], [18, 18, 3, 0, "get"], [18, 19, 3, 0], [19, 6, 3, 0], [19, 13, 3, 0, "_createNativeStackNavigator"], [19, 40, 3, 0], [19, 41, 3, 0, "createNativeStackNavigator"], [19, 67, 3, 0], [20, 4, 3, 0], [21, 2, 3, 0], [22, 2, 3, 0, "Object"], [22, 8, 3, 0], [22, 9, 3, 0, "defineProperty"], [22, 23, 3, 0], [22, 24, 3, 0, "exports"], [22, 31, 3, 0], [23, 4, 3, 0, "enumerable"], [23, 14, 3, 0], [24, 4, 3, 0, "get"], [24, 7, 3, 0], [24, 18, 3, 0, "get"], [24, 19, 3, 0], [25, 6, 3, 0], [25, 13, 3, 0, "_useAnimatedHeaderHeight"], [25, 37, 3, 0], [25, 38, 3, 0, "useAnimatedHeaderHeight"], [25, 61, 3, 0], [26, 4, 3, 0], [27, 2, 3, 0], [28, 2, 6, 0], [28, 6, 6, 0, "_createNativeStackNavigator"], [28, 33, 6, 0], [28, 36, 6, 0, "require"], [28, 43, 6, 0], [28, 44, 6, 0, "_dependencyMap"], [28, 58, 6, 0], [29, 2, 11, 0], [29, 6, 11, 0, "_NativeStackView"], [29, 22, 11, 0], [29, 25, 11, 0, "require"], [29, 32, 11, 0], [29, 33, 11, 0, "_dependencyMap"], [29, 47, 11, 0], [30, 2, 16, 0], [30, 6, 16, 0, "_useAnimatedHeaderHeight"], [30, 30, 16, 0], [30, 33, 16, 0, "require"], [30, 40, 16, 0], [30, 41, 16, 0, "_dependencyMap"], [30, 55, 16, 0], [31, 0, 16, 77], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}