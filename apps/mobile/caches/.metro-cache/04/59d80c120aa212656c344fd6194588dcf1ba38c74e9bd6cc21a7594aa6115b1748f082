{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Portions Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  /**\n   * BezierEasing - use bezier curve for transition easing function\n   * https://github.com/gre/bezier-easing\n   * @copyright 2014-2015 Gaëtan Renaudeau. MIT License.\n   */\n\n  'use strict';\n\n  // These values are established by empiricism with tests (tradeoff: performance VS precision)\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = bezier;\n  var NEWTON_ITERATIONS = 4;\n  var NEWTON_MIN_SLOPE = 0.001;\n  var SUBDIVISION_PRECISION = 0.0000001;\n  var SUBDIVISION_MAX_ITERATIONS = 10;\n  var kSplineTableSize = 11;\n  var kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\n  var float32ArraySupported = typeof Float32Array === 'function';\n  function A(aA1, aA2) {\n    return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n  }\n  function B(aA1, aA2) {\n    return 3.0 * aA2 - 6.0 * aA1;\n  }\n  function C(aA1) {\n    return 3.0 * aA1;\n  }\n\n  // Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\n  function calcBezier(aT, aA1, aA2) {\n    return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n  }\n\n  // Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\n  function getSlope(aT, aA1, aA2) {\n    return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n  }\n  function binarySubdivide(aX, _aA, _aB, mX1, mX2) {\n    var currentX,\n      currentT,\n      i = 0,\n      aA = _aA,\n      aB = _aB;\n    do {\n      currentT = aA + (aB - aA) / 2.0;\n      currentX = calcBezier(currentT, mX1, mX2) - aX;\n      if (currentX > 0.0) {\n        aB = currentT;\n      } else {\n        aA = currentT;\n      }\n    } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n    return currentT;\n  }\n  function newtonRaphsonIterate(aX, _aGuessT, mX1, mX2) {\n    var aGuessT = _aGuessT;\n    for (var i = 0; i < NEWTON_ITERATIONS; ++i) {\n      var currentSlope = getSlope(aGuessT, mX1, mX2);\n      if (currentSlope === 0.0) {\n        return aGuessT;\n      }\n      var currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  }\n  function bezier(mX1, mY1, mX2, mY2) {\n    if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {\n      throw new Error('bezier x values must be in [0, 1] range');\n    }\n\n    // Precompute samples table\n    var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n    if (mX1 !== mY1 || mX2 !== mY2) {\n      for (var i = 0; i < kSplineTableSize; ++i) {\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n      }\n    }\n    function getTForX(aX) {\n      var intervalStart = 0.0;\n      var currentSample = 1;\n      var lastSample = kSplineTableSize - 1;\n      for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n        intervalStart += kSampleStepSize;\n      }\n      --currentSample;\n\n      // Interpolate to provide an initial guess for t\n      var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n      var guessForT = intervalStart + dist * kSampleStepSize;\n      var initialSlope = getSlope(guessForT, mX1, mX2);\n      if (initialSlope >= NEWTON_MIN_SLOPE) {\n        return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n      } else if (initialSlope === 0.0) {\n        return guessForT;\n      } else {\n        return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n      }\n    }\n    return function BezierEasing(x) {\n      if (mX1 === mY1 && mX2 === mY2) {\n        return x; // linear\n      }\n      // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n      if (x === 0) {\n        return 0;\n      }\n      if (x === 1) {\n        return 1;\n      }\n      return calcBezier(getTForX(x), mY1, mY2);\n    };\n  }\n  ;\n});", "lineCount": 128, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [18, 2, 17, 0], [18, 14, 17, 12], [20, 2, 19, 0], [21, 2, 19, 0, "Object"], [21, 8, 19, 0], [21, 9, 19, 0, "defineProperty"], [21, 23, 19, 0], [21, 24, 19, 0, "exports"], [21, 31, 19, 0], [22, 4, 19, 0, "value"], [22, 9, 19, 0], [23, 2, 19, 0], [24, 2, 19, 0, "exports"], [24, 9, 19, 0], [24, 10, 19, 0, "default"], [24, 17, 19, 0], [24, 20, 19, 0, "bezier"], [24, 26, 19, 0], [25, 2, 20, 0], [25, 6, 20, 4, "NEWTON_ITERATIONS"], [25, 23, 20, 21], [25, 26, 20, 24], [25, 27, 20, 25], [26, 2, 21, 0], [26, 6, 21, 4, "NEWTON_MIN_SLOPE"], [26, 22, 21, 20], [26, 25, 21, 23], [26, 30, 21, 28], [27, 2, 22, 0], [27, 6, 22, 4, "SUBDIVISION_PRECISION"], [27, 27, 22, 25], [27, 30, 22, 28], [27, 39, 22, 37], [28, 2, 23, 0], [28, 6, 23, 4, "SUBDIVISION_MAX_ITERATIONS"], [28, 32, 23, 30], [28, 35, 23, 33], [28, 37, 23, 35], [29, 2, 24, 0], [29, 6, 24, 4, "kSplineTableSize"], [29, 22, 24, 20], [29, 25, 24, 23], [29, 27, 24, 25], [30, 2, 25, 0], [30, 6, 25, 4, "kSampleStepSize"], [30, 21, 25, 19], [30, 24, 25, 22], [30, 27, 25, 25], [30, 31, 25, 29, "kSplineTableSize"], [30, 47, 25, 45], [30, 50, 25, 48], [30, 53, 25, 51], [30, 54, 25, 52], [31, 2, 26, 0], [31, 6, 26, 4, "float32ArraySupported"], [31, 27, 26, 25], [31, 30, 26, 28], [31, 37, 26, 35, "Float32Array"], [31, 49, 26, 47], [31, 54, 26, 52], [31, 64, 26, 62], [32, 2, 27, 0], [32, 11, 27, 9, "A"], [32, 12, 27, 10, "A"], [32, 13, 27, 11, "aA1"], [32, 16, 27, 14], [32, 18, 27, 16, "aA2"], [32, 21, 27, 19], [32, 23, 27, 21], [33, 4, 28, 2], [33, 11, 28, 9], [33, 14, 28, 12], [33, 17, 28, 15], [33, 20, 28, 18], [33, 23, 28, 21, "aA2"], [33, 26, 28, 24], [33, 29, 28, 27], [33, 32, 28, 30], [33, 35, 28, 33, "aA1"], [33, 38, 28, 36], [34, 2, 29, 0], [35, 2, 30, 0], [35, 11, 30, 9, "B"], [35, 12, 30, 10, "B"], [35, 13, 30, 11, "aA1"], [35, 16, 30, 14], [35, 18, 30, 16, "aA2"], [35, 21, 30, 19], [35, 23, 30, 21], [36, 4, 31, 2], [36, 11, 31, 9], [36, 14, 31, 12], [36, 17, 31, 15, "aA2"], [36, 20, 31, 18], [36, 23, 31, 21], [36, 26, 31, 24], [36, 29, 31, 27, "aA1"], [36, 32, 31, 30], [37, 2, 32, 0], [38, 2, 33, 0], [38, 11, 33, 9, "C"], [38, 12, 33, 10, "C"], [38, 13, 33, 11, "aA1"], [38, 16, 33, 14], [38, 18, 33, 16], [39, 4, 34, 2], [39, 11, 34, 9], [39, 14, 34, 12], [39, 17, 34, 15, "aA1"], [39, 20, 34, 18], [40, 2, 35, 0], [42, 2, 37, 0], [43, 2, 38, 0], [43, 11, 38, 9, "calcBezier"], [43, 21, 38, 19, "calcBezier"], [43, 22, 38, 20, "aT"], [43, 24, 38, 22], [43, 26, 38, 24, "aA1"], [43, 29, 38, 27], [43, 31, 38, 29, "aA2"], [43, 34, 38, 32], [43, 36, 38, 34], [44, 4, 39, 2], [44, 11, 39, 9], [44, 12, 39, 10], [44, 13, 39, 11, "A"], [44, 14, 39, 12], [44, 15, 39, 13, "aA1"], [44, 18, 39, 16], [44, 20, 39, 18, "aA2"], [44, 23, 39, 21], [44, 24, 39, 22], [44, 27, 39, 25, "aT"], [44, 29, 39, 27], [44, 32, 39, 30, "B"], [44, 33, 39, 31], [44, 34, 39, 32, "aA1"], [44, 37, 39, 35], [44, 39, 39, 37, "aA2"], [44, 42, 39, 40], [44, 43, 39, 41], [44, 47, 39, 45, "aT"], [44, 49, 39, 47], [44, 52, 39, 50, "C"], [44, 53, 39, 51], [44, 54, 39, 52, "aA1"], [44, 57, 39, 55], [44, 58, 39, 56], [44, 62, 39, 60, "aT"], [44, 64, 39, 62], [45, 2, 40, 0], [47, 2, 42, 0], [48, 2, 43, 0], [48, 11, 43, 9, "getSlope"], [48, 19, 43, 17, "getSlope"], [48, 20, 43, 18, "aT"], [48, 22, 43, 20], [48, 24, 43, 22, "aA1"], [48, 27, 43, 25], [48, 29, 43, 27, "aA2"], [48, 32, 43, 30], [48, 34, 43, 32], [49, 4, 44, 2], [49, 11, 44, 9], [49, 14, 44, 12], [49, 17, 44, 15, "A"], [49, 18, 44, 16], [49, 19, 44, 17, "aA1"], [49, 22, 44, 20], [49, 24, 44, 22, "aA2"], [49, 27, 44, 25], [49, 28, 44, 26], [49, 31, 44, 29, "aT"], [49, 33, 44, 31], [49, 36, 44, 34, "aT"], [49, 38, 44, 36], [49, 41, 44, 39], [49, 44, 44, 42], [49, 47, 44, 45, "B"], [49, 48, 44, 46], [49, 49, 44, 47, "aA1"], [49, 52, 44, 50], [49, 54, 44, 52, "aA2"], [49, 57, 44, 55], [49, 58, 44, 56], [49, 61, 44, 59, "aT"], [49, 63, 44, 61], [49, 66, 44, 64, "C"], [49, 67, 44, 65], [49, 68, 44, 66, "aA1"], [49, 71, 44, 69], [49, 72, 44, 70], [50, 2, 45, 0], [51, 2, 46, 0], [51, 11, 46, 9, "binarySubdivide"], [51, 26, 46, 24, "binarySubdivide"], [51, 27, 46, 25, "aX"], [51, 29, 46, 27], [51, 31, 46, 29, "_aA"], [51, 34, 46, 32], [51, 36, 46, 34, "_aB"], [51, 39, 46, 37], [51, 41, 46, 39, "mX1"], [51, 44, 46, 42], [51, 46, 46, 44, "mX2"], [51, 49, 46, 47], [51, 51, 46, 49], [52, 4, 47, 2], [52, 8, 47, 6, "currentX"], [52, 16, 47, 14], [53, 6, 48, 4, "currentT"], [53, 14, 48, 12], [54, 6, 49, 4, "i"], [54, 7, 49, 5], [54, 10, 49, 8], [54, 11, 49, 9], [55, 6, 50, 4, "aA"], [55, 8, 50, 6], [55, 11, 50, 9, "_aA"], [55, 14, 50, 12], [56, 6, 51, 4, "aB"], [56, 8, 51, 6], [56, 11, 51, 9, "_aB"], [56, 14, 51, 12], [57, 4, 52, 2], [57, 7, 52, 5], [58, 6, 53, 4, "currentT"], [58, 14, 53, 12], [58, 17, 53, 15, "aA"], [58, 19, 53, 17], [58, 22, 53, 20], [58, 23, 53, 21, "aB"], [58, 25, 53, 23], [58, 28, 53, 26, "aA"], [58, 30, 53, 28], [58, 34, 53, 32], [58, 37, 53, 35], [59, 6, 54, 4, "currentX"], [59, 14, 54, 12], [59, 17, 54, 15, "calcBezier"], [59, 27, 54, 25], [59, 28, 54, 26, "currentT"], [59, 36, 54, 34], [59, 38, 54, 36, "mX1"], [59, 41, 54, 39], [59, 43, 54, 41, "mX2"], [59, 46, 54, 44], [59, 47, 54, 45], [59, 50, 54, 48, "aX"], [59, 52, 54, 50], [60, 6, 55, 4], [60, 10, 55, 8, "currentX"], [60, 18, 55, 16], [60, 21, 55, 19], [60, 24, 55, 22], [60, 26, 55, 24], [61, 8, 56, 6, "aB"], [61, 10, 56, 8], [61, 13, 56, 11, "currentT"], [61, 21, 56, 19], [62, 6, 57, 4], [62, 7, 57, 5], [62, 13, 57, 11], [63, 8, 58, 6, "aA"], [63, 10, 58, 8], [63, 13, 58, 11, "currentT"], [63, 21, 58, 19], [64, 6, 59, 4], [65, 4, 60, 2], [65, 5, 60, 3], [65, 13, 60, 11, "Math"], [65, 17, 60, 15], [65, 18, 60, 16, "abs"], [65, 21, 60, 19], [65, 22, 60, 20, "currentX"], [65, 30, 60, 28], [65, 31, 60, 29], [65, 34, 60, 32, "SUBDIVISION_PRECISION"], [65, 55, 60, 53], [65, 59, 60, 57], [65, 61, 60, 59, "i"], [65, 62, 60, 60], [65, 65, 60, 63, "SUBDIVISION_MAX_ITERATIONS"], [65, 91, 60, 89], [66, 4, 61, 2], [66, 11, 61, 9, "currentT"], [66, 19, 61, 17], [67, 2, 62, 0], [68, 2, 63, 0], [68, 11, 63, 9, "newtonRaphsonIterate"], [68, 31, 63, 29, "newtonRaphsonIterate"], [68, 32, 63, 30, "aX"], [68, 34, 63, 32], [68, 36, 63, 34, "_aGuessT"], [68, 44, 63, 42], [68, 46, 63, 44, "mX1"], [68, 49, 63, 47], [68, 51, 63, 49, "mX2"], [68, 54, 63, 52], [68, 56, 63, 54], [69, 4, 64, 2], [69, 8, 64, 6, "aGuessT"], [69, 15, 64, 13], [69, 18, 64, 16, "_aGuessT"], [69, 26, 64, 24], [70, 4, 65, 2], [70, 9, 65, 7], [70, 13, 65, 11, "i"], [70, 14, 65, 12], [70, 17, 65, 15], [70, 18, 65, 16], [70, 20, 65, 18, "i"], [70, 21, 65, 19], [70, 24, 65, 22, "NEWTON_ITERATIONS"], [70, 41, 65, 39], [70, 43, 65, 41], [70, 45, 65, 43, "i"], [70, 46, 65, 44], [70, 48, 65, 46], [71, 6, 66, 4], [71, 10, 66, 8, "currentSlope"], [71, 22, 66, 20], [71, 25, 66, 23, "getSlope"], [71, 33, 66, 31], [71, 34, 66, 32, "aGuessT"], [71, 41, 66, 39], [71, 43, 66, 41, "mX1"], [71, 46, 66, 44], [71, 48, 66, 46, "mX2"], [71, 51, 66, 49], [71, 52, 66, 50], [72, 6, 67, 4], [72, 10, 67, 8, "currentSlope"], [72, 22, 67, 20], [72, 27, 67, 25], [72, 30, 67, 28], [72, 32, 67, 30], [73, 8, 68, 6], [73, 15, 68, 13, "aGuessT"], [73, 22, 68, 20], [74, 6, 69, 4], [75, 6, 70, 4], [75, 10, 70, 8, "currentX"], [75, 18, 70, 16], [75, 21, 70, 19, "calcBezier"], [75, 31, 70, 29], [75, 32, 70, 30, "aGuessT"], [75, 39, 70, 37], [75, 41, 70, 39, "mX1"], [75, 44, 70, 42], [75, 46, 70, 44, "mX2"], [75, 49, 70, 47], [75, 50, 70, 48], [75, 53, 70, 51, "aX"], [75, 55, 70, 53], [76, 6, 71, 4, "aGuessT"], [76, 13, 71, 11], [76, 17, 71, 15, "currentX"], [76, 25, 71, 23], [76, 28, 71, 26, "currentSlope"], [76, 40, 71, 38], [77, 4, 72, 2], [78, 4, 73, 2], [78, 11, 73, 9, "aGuessT"], [78, 18, 73, 16], [79, 2, 74, 0], [80, 2, 75, 15], [80, 11, 75, 24, "bezier"], [80, 17, 75, 30, "bezier"], [80, 18, 75, 31, "mX1"], [80, 21, 75, 34], [80, 23, 75, 36, "mY1"], [80, 26, 75, 39], [80, 28, 75, 41, "mX2"], [80, 31, 75, 44], [80, 33, 75, 46, "mY2"], [80, 36, 75, 49], [80, 38, 75, 51], [81, 4, 76, 2], [81, 8, 76, 6], [81, 10, 76, 8, "mX1"], [81, 13, 76, 11], [81, 17, 76, 15], [81, 18, 76, 16], [81, 22, 76, 20, "mX1"], [81, 25, 76, 23], [81, 29, 76, 27], [81, 30, 76, 28], [81, 34, 76, 32, "mX2"], [81, 37, 76, 35], [81, 41, 76, 39], [81, 42, 76, 40], [81, 46, 76, 44, "mX2"], [81, 49, 76, 47], [81, 53, 76, 51], [81, 54, 76, 52], [81, 55, 76, 53], [81, 57, 76, 55], [82, 6, 77, 4], [82, 12, 77, 10], [82, 16, 77, 14, "Error"], [82, 21, 77, 19], [82, 22, 77, 20], [82, 63, 77, 61], [82, 64, 77, 62], [83, 4, 78, 2], [85, 4, 80, 2], [86, 4, 81, 2], [86, 8, 81, 6, "sampleValues"], [86, 20, 81, 18], [86, 23, 81, 21, "float32ArraySupported"], [86, 44, 81, 42], [86, 47, 81, 45], [86, 51, 81, 49, "Float32Array"], [86, 63, 81, 61], [86, 64, 81, 62, "kSplineTableSize"], [86, 80, 81, 78], [86, 81, 81, 79], [86, 84, 81, 82], [86, 88, 81, 86, "Array"], [86, 93, 81, 91], [86, 94, 81, 92, "kSplineTableSize"], [86, 110, 81, 108], [86, 111, 81, 109], [87, 4, 82, 2], [87, 8, 82, 6, "mX1"], [87, 11, 82, 9], [87, 16, 82, 14, "mY1"], [87, 19, 82, 17], [87, 23, 82, 21, "mX2"], [87, 26, 82, 24], [87, 31, 82, 29, "mY2"], [87, 34, 82, 32], [87, 36, 82, 34], [88, 6, 83, 4], [88, 11, 83, 9], [88, 15, 83, 13, "i"], [88, 16, 83, 14], [88, 19, 83, 17], [88, 20, 83, 18], [88, 22, 83, 20, "i"], [88, 23, 83, 21], [88, 26, 83, 24, "kSplineTableSize"], [88, 42, 83, 40], [88, 44, 83, 42], [88, 46, 83, 44, "i"], [88, 47, 83, 45], [88, 49, 83, 47], [89, 8, 84, 6, "sampleValues"], [89, 20, 84, 18], [89, 21, 84, 19, "i"], [89, 22, 84, 20], [89, 23, 84, 21], [89, 26, 84, 24, "calcBezier"], [89, 36, 84, 34], [89, 37, 84, 35, "i"], [89, 38, 84, 36], [89, 41, 84, 39, "kSampleStepSize"], [89, 56, 84, 54], [89, 58, 84, 56, "mX1"], [89, 61, 84, 59], [89, 63, 84, 61, "mX2"], [89, 66, 84, 64], [89, 67, 84, 65], [90, 6, 85, 4], [91, 4, 86, 2], [92, 4, 87, 2], [92, 13, 87, 11, "getTForX"], [92, 21, 87, 19, "getTForX"], [92, 22, 87, 20, "aX"], [92, 24, 87, 22], [92, 26, 87, 24], [93, 6, 88, 4], [93, 10, 88, 8, "intervalStart"], [93, 23, 88, 21], [93, 26, 88, 24], [93, 29, 88, 27], [94, 6, 89, 4], [94, 10, 89, 8, "currentSample"], [94, 23, 89, 21], [94, 26, 89, 24], [94, 27, 89, 25], [95, 6, 90, 4], [95, 10, 90, 8, "lastSample"], [95, 20, 90, 18], [95, 23, 90, 21, "kSplineTableSize"], [95, 39, 90, 37], [95, 42, 90, 40], [95, 43, 90, 41], [96, 6, 91, 4], [96, 13, 91, 11, "currentSample"], [96, 26, 91, 24], [96, 31, 91, 29, "lastSample"], [96, 41, 91, 39], [96, 45, 91, 43, "sampleValues"], [96, 57, 91, 55], [96, 58, 91, 56, "currentSample"], [96, 71, 91, 69], [96, 72, 91, 70], [96, 76, 91, 74, "aX"], [96, 78, 91, 76], [96, 80, 91, 78], [96, 82, 91, 80, "currentSample"], [96, 95, 91, 93], [96, 97, 91, 95], [97, 8, 92, 6, "intervalStart"], [97, 21, 92, 19], [97, 25, 92, 23, "kSampleStepSize"], [97, 40, 92, 38], [98, 6, 93, 4], [99, 6, 94, 4], [99, 8, 94, 6, "currentSample"], [99, 21, 94, 19], [101, 6, 96, 4], [102, 6, 97, 4], [102, 10, 97, 8, "dist"], [102, 14, 97, 12], [102, 17, 97, 15], [102, 18, 97, 16, "aX"], [102, 20, 97, 18], [102, 23, 97, 21, "sampleValues"], [102, 35, 97, 33], [102, 36, 97, 34, "currentSample"], [102, 49, 97, 47], [102, 50, 97, 48], [102, 55, 97, 53, "sampleValues"], [102, 67, 97, 65], [102, 68, 97, 66, "currentSample"], [102, 81, 97, 79], [102, 84, 97, 82], [102, 85, 97, 83], [102, 86, 97, 84], [102, 89, 97, 87, "sampleValues"], [102, 101, 97, 99], [102, 102, 97, 100, "currentSample"], [102, 115, 97, 113], [102, 116, 97, 114], [102, 117, 97, 115], [103, 6, 98, 4], [103, 10, 98, 8, "guessForT"], [103, 19, 98, 17], [103, 22, 98, 20, "intervalStart"], [103, 35, 98, 33], [103, 38, 98, 36, "dist"], [103, 42, 98, 40], [103, 45, 98, 43, "kSampleStepSize"], [103, 60, 98, 58], [104, 6, 99, 4], [104, 10, 99, 8, "initialSlope"], [104, 22, 99, 20], [104, 25, 99, 23, "getSlope"], [104, 33, 99, 31], [104, 34, 99, 32, "guessForT"], [104, 43, 99, 41], [104, 45, 99, 43, "mX1"], [104, 48, 99, 46], [104, 50, 99, 48, "mX2"], [104, 53, 99, 51], [104, 54, 99, 52], [105, 6, 100, 4], [105, 10, 100, 8, "initialSlope"], [105, 22, 100, 20], [105, 26, 100, 24, "NEWTON_MIN_SLOPE"], [105, 42, 100, 40], [105, 44, 100, 42], [106, 8, 101, 6], [106, 15, 101, 13, "newtonRaphsonIterate"], [106, 35, 101, 33], [106, 36, 101, 34, "aX"], [106, 38, 101, 36], [106, 40, 101, 38, "guessForT"], [106, 49, 101, 47], [106, 51, 101, 49, "mX1"], [106, 54, 101, 52], [106, 56, 101, 54, "mX2"], [106, 59, 101, 57], [106, 60, 101, 58], [107, 6, 102, 4], [107, 7, 102, 5], [107, 13, 102, 11], [107, 17, 102, 15, "initialSlope"], [107, 29, 102, 27], [107, 34, 102, 32], [107, 37, 102, 35], [107, 39, 102, 37], [108, 8, 103, 6], [108, 15, 103, 13, "guessForT"], [108, 24, 103, 22], [109, 6, 104, 4], [109, 7, 104, 5], [109, 13, 104, 11], [110, 8, 105, 6], [110, 15, 105, 13, "binarySubdivide"], [110, 30, 105, 28], [110, 31, 105, 29, "aX"], [110, 33, 105, 31], [110, 35, 105, 33, "intervalStart"], [110, 48, 105, 46], [110, 50, 105, 48, "intervalStart"], [110, 63, 105, 61], [110, 66, 105, 64, "kSampleStepSize"], [110, 81, 105, 79], [110, 83, 105, 81, "mX1"], [110, 86, 105, 84], [110, 88, 105, 86, "mX2"], [110, 91, 105, 89], [110, 92, 105, 90], [111, 6, 106, 4], [112, 4, 107, 2], [113, 4, 108, 2], [113, 11, 108, 9], [113, 20, 108, 18, "BezierEasing"], [113, 32, 108, 30, "BezierEasing"], [113, 33, 108, 31, "x"], [113, 34, 108, 32], [113, 36, 108, 34], [114, 6, 109, 4], [114, 10, 109, 8, "mX1"], [114, 13, 109, 11], [114, 18, 109, 16, "mY1"], [114, 21, 109, 19], [114, 25, 109, 23, "mX2"], [114, 28, 109, 26], [114, 33, 109, 31, "mY2"], [114, 36, 109, 34], [114, 38, 109, 36], [115, 8, 110, 6], [115, 15, 110, 13, "x"], [115, 16, 110, 14], [115, 17, 110, 15], [115, 18, 110, 16], [116, 6, 111, 4], [117, 6, 112, 4], [118, 6, 113, 4], [118, 10, 113, 8, "x"], [118, 11, 113, 9], [118, 16, 113, 14], [118, 17, 113, 15], [118, 19, 113, 17], [119, 8, 114, 6], [119, 15, 114, 13], [119, 16, 114, 14], [120, 6, 115, 4], [121, 6, 116, 4], [121, 10, 116, 8, "x"], [121, 11, 116, 9], [121, 16, 116, 14], [121, 17, 116, 15], [121, 19, 116, 17], [122, 8, 117, 6], [122, 15, 117, 13], [122, 16, 117, 14], [123, 6, 118, 4], [124, 6, 119, 4], [124, 13, 119, 11, "calcBezier"], [124, 23, 119, 21], [124, 24, 119, 22, "getTForX"], [124, 32, 119, 30], [124, 33, 119, 31, "x"], [124, 34, 119, 32], [124, 35, 119, 33], [124, 37, 119, 35, "mY1"], [124, 40, 119, 38], [124, 42, 119, 40, "mY2"], [124, 45, 119, 43], [124, 46, 119, 44], [125, 4, 120, 2], [125, 5, 120, 3], [126, 2, 121, 0], [127, 2, 122, 0], [128, 0, 122, 1], [128, 3]], "functionMap": {"names": ["<global>", "A", "B", "C", "calcBezier", "getSlope", "binarySubdivide", "newtonRaphsonIterate", "bezier", "getTForX", "BezierEasing"], "mappings": "AAA;AC0B;CDE;AEC;CFE;AGC;CHE;AIG;CJE;AKG;CLE;AMC;CNgB;AOC;CPW;eQC;ECY;GDoB;SEC;GFY;CRC"}}, "type": "js/module"}]}