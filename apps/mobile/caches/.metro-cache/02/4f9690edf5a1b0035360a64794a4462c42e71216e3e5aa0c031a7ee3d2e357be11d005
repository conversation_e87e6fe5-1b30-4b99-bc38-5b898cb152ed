{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "hoist-non-react-statics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 75}, "end": {"line": 3, "column": 59, "index": 134}}], "key": "MoIuZFdUef7yl/jvHg7drnKZFmo=", "exportNames": ["*"]}}, {"name": "./GestureHandlerRootView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 135}, "end": {"line": 4, "column": 62, "index": 197}}], "key": "RWLneAcG75BjfgIQnRuzKrHXe1k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = gestureHandlerRootHOC;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _hoistNonReactStatics = _interopRequireDefault(require(_dependencyMap[4], \"hoist-non-react-statics\"));\n  var _GestureHandlerRootView = _interopRequireDefault(require(_dependencyMap[5], \"./GestureHandlerRootView\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function gestureHandlerRootHOC(Component, containerStyles) {\n    function Wrapper(props) {\n      return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_GestureHandlerRootView.default, {\n        style: [styles.container, containerStyles]\n      }, /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(Component, props));\n    }\n    Wrapper.displayName = `gestureHandlerRootHOC(${Component.displayName || Component.name})`; // @ts-ignore - hoistNonReactStatics uses old version of @types/react\n\n    (0, _hoistNonReactStatics.default)(Wrapper, Component);\n    return Wrapper;\n  }\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1\n    }\n  });\n});", "lineCount": 29, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 1, 31], [9, 6, 1, 31, "_StyleSheet"], [9, 17, 1, 31], [9, 20, 1, 31, "_interopRequireDefault"], [9, 42, 1, 31], [9, 43, 1, 31, "require"], [9, 50, 1, 31], [9, 51, 1, 31, "_dependencyMap"], [9, 65, 1, 31], [10, 2, 3, 0], [10, 6, 3, 0, "_hoistNonReactStatics"], [10, 27, 3, 0], [10, 30, 3, 0, "_interopRequireDefault"], [10, 52, 3, 0], [10, 53, 3, 0, "require"], [10, 60, 3, 0], [10, 61, 3, 0, "_dependencyMap"], [10, 75, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_GestureHandlerRootView"], [11, 29, 4, 0], [11, 32, 4, 0, "_interopRequireDefault"], [11, 54, 4, 0], [11, 55, 4, 0, "require"], [11, 62, 4, 0], [11, 63, 4, 0, "_dependencyMap"], [11, 77, 4, 0], [12, 2, 4, 62], [12, 11, 4, 62, "_interopRequireWildcard"], [12, 35, 4, 62, "e"], [12, 36, 4, 62], [12, 38, 4, 62, "t"], [12, 39, 4, 62], [12, 68, 4, 62, "WeakMap"], [12, 75, 4, 62], [12, 81, 4, 62, "r"], [12, 82, 4, 62], [12, 89, 4, 62, "WeakMap"], [12, 96, 4, 62], [12, 100, 4, 62, "n"], [12, 101, 4, 62], [12, 108, 4, 62, "WeakMap"], [12, 115, 4, 62], [12, 127, 4, 62, "_interopRequireWildcard"], [12, 150, 4, 62], [12, 162, 4, 62, "_interopRequireWildcard"], [12, 163, 4, 62, "e"], [12, 164, 4, 62], [12, 166, 4, 62, "t"], [12, 167, 4, 62], [12, 176, 4, 62, "t"], [12, 177, 4, 62], [12, 181, 4, 62, "e"], [12, 182, 4, 62], [12, 186, 4, 62, "e"], [12, 187, 4, 62], [12, 188, 4, 62, "__esModule"], [12, 198, 4, 62], [12, 207, 4, 62, "e"], [12, 208, 4, 62], [12, 214, 4, 62, "o"], [12, 215, 4, 62], [12, 217, 4, 62, "i"], [12, 218, 4, 62], [12, 220, 4, 62, "f"], [12, 221, 4, 62], [12, 226, 4, 62, "__proto__"], [12, 235, 4, 62], [12, 243, 4, 62, "default"], [12, 250, 4, 62], [12, 252, 4, 62, "e"], [12, 253, 4, 62], [12, 270, 4, 62, "e"], [12, 271, 4, 62], [12, 294, 4, 62, "e"], [12, 295, 4, 62], [12, 320, 4, 62, "e"], [12, 321, 4, 62], [12, 330, 4, 62, "f"], [12, 331, 4, 62], [12, 337, 4, 62, "o"], [12, 338, 4, 62], [12, 341, 4, 62, "t"], [12, 342, 4, 62], [12, 345, 4, 62, "n"], [12, 346, 4, 62], [12, 349, 4, 62, "r"], [12, 350, 4, 62], [12, 358, 4, 62, "o"], [12, 359, 4, 62], [12, 360, 4, 62, "has"], [12, 363, 4, 62], [12, 364, 4, 62, "e"], [12, 365, 4, 62], [12, 375, 4, 62, "o"], [12, 376, 4, 62], [12, 377, 4, 62, "get"], [12, 380, 4, 62], [12, 381, 4, 62, "e"], [12, 382, 4, 62], [12, 385, 4, 62, "o"], [12, 386, 4, 62], [12, 387, 4, 62, "set"], [12, 390, 4, 62], [12, 391, 4, 62, "e"], [12, 392, 4, 62], [12, 394, 4, 62, "f"], [12, 395, 4, 62], [12, 411, 4, 62, "t"], [12, 412, 4, 62], [12, 416, 4, 62, "e"], [12, 417, 4, 62], [12, 433, 4, 62, "t"], [12, 434, 4, 62], [12, 441, 4, 62, "hasOwnProperty"], [12, 455, 4, 62], [12, 456, 4, 62, "call"], [12, 460, 4, 62], [12, 461, 4, 62, "e"], [12, 462, 4, 62], [12, 464, 4, 62, "t"], [12, 465, 4, 62], [12, 472, 4, 62, "i"], [12, 473, 4, 62], [12, 477, 4, 62, "o"], [12, 478, 4, 62], [12, 481, 4, 62, "Object"], [12, 487, 4, 62], [12, 488, 4, 62, "defineProperty"], [12, 502, 4, 62], [12, 507, 4, 62, "Object"], [12, 513, 4, 62], [12, 514, 4, 62, "getOwnPropertyDescriptor"], [12, 538, 4, 62], [12, 539, 4, 62, "e"], [12, 540, 4, 62], [12, 542, 4, 62, "t"], [12, 543, 4, 62], [12, 550, 4, 62, "i"], [12, 551, 4, 62], [12, 552, 4, 62, "get"], [12, 555, 4, 62], [12, 559, 4, 62, "i"], [12, 560, 4, 62], [12, 561, 4, 62, "set"], [12, 564, 4, 62], [12, 568, 4, 62, "o"], [12, 569, 4, 62], [12, 570, 4, 62, "f"], [12, 571, 4, 62], [12, 573, 4, 62, "t"], [12, 574, 4, 62], [12, 576, 4, 62, "i"], [12, 577, 4, 62], [12, 581, 4, 62, "f"], [12, 582, 4, 62], [12, 583, 4, 62, "t"], [12, 584, 4, 62], [12, 588, 4, 62, "e"], [12, 589, 4, 62], [12, 590, 4, 62, "t"], [12, 591, 4, 62], [12, 602, 4, 62, "f"], [12, 603, 4, 62], [12, 608, 4, 62, "e"], [12, 609, 4, 62], [12, 611, 4, 62, "t"], [12, 612, 4, 62], [13, 2, 5, 15], [13, 11, 5, 24, "gestureHandlerRootHOC"], [13, 32, 5, 45, "gestureHandlerRootHOC"], [13, 33, 5, 46, "Component"], [13, 42, 5, 55], [13, 44, 5, 57, "containerStyles"], [13, 59, 5, 72], [13, 61, 5, 74], [14, 4, 6, 2], [14, 13, 6, 11, "Wrapper"], [14, 20, 6, 18, "Wrapper"], [14, 21, 6, 19, "props"], [14, 26, 6, 24], [14, 28, 6, 26], [15, 6, 7, 4], [15, 13, 7, 11], [15, 26, 7, 24, "_ReactNativeCSSInterop"], [15, 48, 7, 24], [15, 49, 7, 24, "createInteropElement"], [15, 69, 7, 24], [15, 70, 7, 44, "GestureHandlerRootView"], [15, 101, 7, 66], [15, 103, 7, 68], [16, 8, 8, 6, "style"], [16, 13, 8, 11], [16, 15, 8, 13], [16, 16, 8, 14, "styles"], [16, 22, 8, 20], [16, 23, 8, 21, "container"], [16, 32, 8, 30], [16, 34, 8, 32, "containerStyles"], [16, 49, 8, 47], [17, 6, 9, 4], [17, 7, 9, 5], [17, 9, 9, 7], [17, 22, 9, 20, "_ReactNativeCSSInterop"], [17, 44, 9, 20], [17, 45, 9, 20, "createInteropElement"], [17, 65, 9, 20], [17, 66, 9, 40, "Component"], [17, 75, 9, 49], [17, 77, 9, 51, "props"], [17, 82, 9, 56], [17, 83, 9, 57], [17, 84, 9, 58], [18, 4, 10, 2], [19, 4, 12, 2, "Wrapper"], [19, 11, 12, 9], [19, 12, 12, 10, "displayName"], [19, 23, 12, 21], [19, 26, 12, 24], [19, 51, 12, 49, "Component"], [19, 60, 12, 58], [19, 61, 12, 59, "displayName"], [19, 72, 12, 70], [19, 76, 12, 74, "Component"], [19, 85, 12, 83], [19, 86, 12, 84, "name"], [19, 90, 12, 88], [19, 93, 12, 91], [19, 94, 12, 92], [19, 95, 12, 93], [21, 4, 14, 2], [21, 8, 14, 2, "hoistNonReactStatics"], [21, 37, 14, 22], [21, 39, 14, 23, "Wrapper"], [21, 46, 14, 30], [21, 48, 14, 32, "Component"], [21, 57, 14, 41], [21, 58, 14, 42], [22, 4, 15, 2], [22, 11, 15, 9, "Wrapper"], [22, 18, 15, 16], [23, 2, 16, 0], [24, 2, 17, 0], [24, 8, 17, 6, "styles"], [24, 14, 17, 12], [24, 17, 17, 15, "StyleSheet"], [24, 36, 17, 25], [24, 37, 17, 26, "create"], [24, 43, 17, 32], [24, 44, 17, 33], [25, 4, 18, 2, "container"], [25, 13, 18, 11], [25, 15, 18, 13], [26, 6, 19, 4, "flex"], [26, 10, 19, 8], [26, 12, 19, 10], [27, 4, 20, 2], [28, 2, 21, 0], [28, 3, 21, 1], [28, 4, 21, 2], [29, 0, 21, 3], [29, 3]], "functionMap": {"names": ["<global>", "gestureHandlerRootHOC", "Wrapper"], "mappings": "AAA;eCI;ECC;GDI;CDM"}}, "type": "js/module"}]}