{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function deepFreezeAndThrowOnMutationInDev(object) {\n    if (__DEV__) {\n      if (typeof object !== 'object' || object === null || Object.isFrozen(object) || Object.isSealed(object)) {\n        return object;\n      }\n      var keys = Object.keys(object);\n      var _hasOwnProperty = Object.prototype.hasOwnProperty;\n      for (var i = 0; i < keys.length; i++) {\n        var key = keys[i];\n        if (_hasOwnProperty.call(object, key)) {\n          Object.defineProperty(object, key, {\n            get: identity.bind(null, object[key])\n          });\n          Object.defineProperty(object, key, {\n            set: throwOnImmutableMutation.bind(null, key)\n          });\n        }\n      }\n      Object.freeze(object);\n      Object.seal(object);\n      for (var _i = 0; _i < keys.length; _i++) {\n        var _key = keys[_i];\n        if (_hasOwnProperty.call(object, _key)) {\n          deepFreezeAndThrowOnMutationInDev(object[_key]);\n        }\n      }\n    }\n    return object;\n  }\n  function throwOnImmutableMutation(key, value) {\n    throw Error('You attempted to set the key `' + key + '` with the value `' + JSON.stringify(value) + '` on an object that is meant to be immutable ' + 'and has been frozen.');\n  }\n  function identity(value) {\n    return value;\n  }\n  var _default = exports.default = deepFreezeAndThrowOnMutationInDev;\n});", "lineCount": 44, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 30, 0], [8, 11, 30, 9, "deepFreezeAndThrowOnMutationInDev"], [8, 44, 30, 42, "deepFreezeAndThrowOnMutationInDev"], [8, 45, 31, 2, "object"], [8, 51, 31, 11], [8, 53, 32, 5], [9, 4, 33, 2], [9, 8, 33, 6, "__DEV__"], [9, 15, 33, 13], [9, 17, 33, 15], [10, 6, 34, 4], [10, 10, 35, 6], [10, 17, 35, 13, "object"], [10, 23, 35, 19], [10, 28, 35, 24], [10, 36, 35, 32], [10, 40, 36, 6, "object"], [10, 46, 36, 12], [10, 51, 36, 17], [10, 55, 36, 21], [10, 59, 37, 6, "Object"], [10, 65, 37, 12], [10, 66, 37, 13, "isFrozen"], [10, 74, 37, 21], [10, 75, 37, 22, "object"], [10, 81, 37, 28], [10, 82, 37, 29], [10, 86, 38, 6, "Object"], [10, 92, 38, 12], [10, 93, 38, 13, "isSealed"], [10, 101, 38, 21], [10, 102, 38, 22, "object"], [10, 108, 38, 28], [10, 109, 38, 29], [10, 111, 39, 6], [11, 8, 40, 6], [11, 15, 40, 13, "object"], [11, 21, 40, 19], [12, 6, 41, 4], [13, 6, 44, 4], [13, 10, 44, 10, "keys"], [13, 14, 44, 14], [13, 17, 44, 17, "Object"], [13, 23, 44, 23], [13, 24, 44, 24, "keys"], [13, 28, 44, 28], [13, 29, 44, 30, "object"], [13, 35, 44, 59], [13, 36, 44, 60], [14, 6, 46, 4], [14, 10, 46, 10, "hasOwnProperty"], [14, 25, 46, 24], [14, 28, 46, 27, "Object"], [14, 34, 46, 33], [14, 35, 46, 34, "prototype"], [14, 44, 46, 43], [14, 45, 46, 44, "hasOwnProperty"], [14, 59, 46, 58], [15, 6, 48, 4], [15, 11, 48, 9], [15, 15, 48, 13, "i"], [15, 16, 48, 14], [15, 19, 48, 17], [15, 20, 48, 18], [15, 22, 48, 20, "i"], [15, 23, 48, 21], [15, 26, 48, 24, "keys"], [15, 30, 48, 28], [15, 31, 48, 29, "length"], [15, 37, 48, 35], [15, 39, 48, 37, "i"], [15, 40, 48, 38], [15, 42, 48, 40], [15, 44, 48, 42], [16, 8, 49, 6], [16, 12, 49, 12, "key"], [16, 15, 49, 15], [16, 18, 49, 18, "keys"], [16, 22, 49, 22], [16, 23, 49, 23, "i"], [16, 24, 49, 24], [16, 25, 49, 25], [17, 8, 50, 6], [17, 12, 50, 10, "hasOwnProperty"], [17, 27, 50, 24], [17, 28, 50, 25, "call"], [17, 32, 50, 29], [17, 33, 50, 30, "object"], [17, 39, 50, 36], [17, 41, 50, 38, "key"], [17, 44, 50, 41], [17, 45, 50, 42], [17, 47, 50, 44], [18, 10, 51, 8, "Object"], [18, 16, 51, 14], [18, 17, 51, 15, "defineProperty"], [18, 31, 51, 29], [18, 32, 51, 30, "object"], [18, 38, 51, 36], [18, 40, 51, 38, "key"], [18, 43, 51, 41], [18, 45, 51, 43], [19, 12, 52, 10, "get"], [19, 15, 52, 13], [19, 17, 52, 15, "identity"], [19, 25, 52, 23], [19, 26, 52, 24, "bind"], [19, 30, 52, 28], [19, 31, 52, 29], [19, 35, 52, 33], [19, 37, 52, 35, "object"], [19, 43, 52, 41], [19, 44, 52, 42, "key"], [19, 47, 52, 45], [19, 48, 52, 46], [20, 10, 53, 8], [20, 11, 53, 9], [20, 12, 53, 10], [21, 10, 54, 8, "Object"], [21, 16, 54, 14], [21, 17, 54, 15, "defineProperty"], [21, 31, 54, 29], [21, 32, 54, 30, "object"], [21, 38, 54, 36], [21, 40, 54, 38, "key"], [21, 43, 54, 41], [21, 45, 54, 43], [22, 12, 55, 10, "set"], [22, 15, 55, 13], [22, 17, 55, 15, "throwOnImmutableMutation"], [22, 41, 55, 39], [22, 42, 55, 40, "bind"], [22, 46, 55, 44], [22, 47, 55, 45], [22, 51, 55, 49], [22, 53, 55, 51, "key"], [22, 56, 55, 54], [23, 10, 56, 8], [23, 11, 56, 9], [23, 12, 56, 10], [24, 8, 57, 6], [25, 6, 58, 4], [26, 6, 60, 4, "Object"], [26, 12, 60, 10], [26, 13, 60, 11, "freeze"], [26, 19, 60, 17], [26, 20, 60, 18, "object"], [26, 26, 60, 24], [26, 27, 60, 25], [27, 6, 61, 4, "Object"], [27, 12, 61, 10], [27, 13, 61, 11, "seal"], [27, 17, 61, 15], [27, 18, 61, 16, "object"], [27, 24, 61, 22], [27, 25, 61, 23], [28, 6, 63, 4], [28, 11, 63, 9], [28, 15, 63, 13, "i"], [28, 17, 63, 14], [28, 20, 63, 17], [28, 21, 63, 18], [28, 23, 63, 20, "i"], [28, 25, 63, 21], [28, 28, 63, 24, "keys"], [28, 32, 63, 28], [28, 33, 63, 29, "length"], [28, 39, 63, 35], [28, 41, 63, 37, "i"], [28, 43, 63, 38], [28, 45, 63, 40], [28, 47, 63, 42], [29, 8, 64, 6], [29, 12, 64, 12, "key"], [29, 16, 64, 15], [29, 19, 64, 18, "keys"], [29, 23, 64, 22], [29, 24, 64, 23, "i"], [29, 26, 64, 24], [29, 27, 64, 25], [30, 8, 65, 6], [30, 12, 65, 10, "hasOwnProperty"], [30, 27, 65, 24], [30, 28, 65, 25, "call"], [30, 32, 65, 29], [30, 33, 65, 30, "object"], [30, 39, 65, 36], [30, 41, 65, 38, "key"], [30, 45, 65, 41], [30, 46, 65, 42], [30, 48, 65, 44], [31, 10, 66, 8, "deepFreezeAndThrowOnMutationInDev"], [31, 43, 66, 41], [31, 44, 66, 42, "object"], [31, 50, 66, 48], [31, 51, 66, 49, "key"], [31, 55, 66, 52], [31, 56, 66, 53], [31, 57, 66, 54], [32, 8, 67, 6], [33, 6, 68, 4], [34, 4, 69, 2], [35, 4, 70, 2], [35, 11, 70, 9, "object"], [35, 17, 70, 15], [36, 2, 71, 0], [37, 2, 75, 0], [37, 11, 75, 9, "throwOnImmutableMutation"], [37, 35, 75, 33, "throwOnImmutableMutation"], [37, 36, 75, 34, "key"], [37, 39, 75, 44], [37, 41, 75, 46, "value"], [37, 46, 75, 51], [37, 48, 75, 53], [38, 4, 76, 2], [38, 10, 76, 8, "Error"], [38, 15, 76, 13], [38, 16, 77, 4], [38, 48, 77, 36], [38, 51, 78, 6, "key"], [38, 54, 78, 9], [38, 57, 79, 6], [38, 77, 79, 26], [38, 80, 80, 6, "JSON"], [38, 84, 80, 10], [38, 85, 80, 11, "stringify"], [38, 94, 80, 20], [38, 95, 80, 21, "value"], [38, 100, 80, 26], [38, 101, 80, 27], [38, 104, 81, 6], [38, 151, 81, 53], [38, 154, 82, 6], [38, 176, 83, 2], [38, 177, 83, 3], [39, 2, 84, 0], [40, 2, 86, 0], [40, 11, 86, 9, "identity"], [40, 19, 86, 17, "identity"], [40, 20, 86, 18, "value"], [40, 25, 86, 30], [40, 27, 86, 32], [41, 4, 87, 2], [41, 11, 87, 9, "value"], [41, 16, 87, 14], [42, 2, 88, 0], [43, 2, 88, 1], [43, 6, 88, 1, "_default"], [43, 14, 88, 1], [43, 17, 88, 1, "exports"], [43, 24, 88, 1], [43, 25, 88, 1, "default"], [43, 32, 88, 1], [43, 35, 90, 15, "deepFreezeAndThrowOnMutationInDev"], [43, 68, 90, 48], [44, 0, 90, 48], [44, 3]], "functionMap": {"names": ["<global>", "deepFreezeAndThrowOnMutationInDev", "throwOnImmutableMutation", "identity"], "mappings": "AAA;AC6B;CDyC;AEI;CFS;AGE;CHE"}}, "type": "js/module"}]}