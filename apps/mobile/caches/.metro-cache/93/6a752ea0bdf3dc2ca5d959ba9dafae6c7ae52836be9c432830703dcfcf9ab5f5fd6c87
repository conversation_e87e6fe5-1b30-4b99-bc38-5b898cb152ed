{"dependencies": [{"name": "@react-navigation/routers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 58, "index": 73}}], "key": "TumjUqgKkj40CL5/as2VxzLfO54=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 74}, "end": {"line": 4, "column": 31, "index": 105}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 106}, "end": {"line": 5, "column": 73, "index": 179}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useNavigationCache = useNavigationCache;\n  var _routers = require(_dependencyMap[0], \"@react-navigation/routers\");\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[2], \"./NavigationBuilderContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Hook to cache navigation objects for each screen in the navigator.\n   * It's important to cache them to make sure navigation objects don't change between renders.\n   * This lets us apply optimizations like `React.memo` to minimize re-rendering screens.\n   */\n  function useNavigationCache({\n    state,\n    getState,\n    navigation,\n    setOptions,\n    router,\n    emitter\n  }) {\n    const {\n      stackRef\n    } = React.useContext(_NavigationBuilderContext.NavigationBuilderContext);\n    const base = React.useMemo(() => {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const {\n        emit,\n        ...rest\n      } = navigation;\n      const actions = {\n        ...router.actionCreators,\n        ..._routers.CommonActions\n      };\n      const dispatch = () => {\n        throw new Error('Actions cannot be dispatched from a placeholder screen.');\n      };\n      const helpers = Object.keys(actions).reduce((acc, name) => {\n        acc[name] = dispatch;\n        return acc;\n      }, {});\n      return {\n        ...rest,\n        ...helpers,\n        addListener: () => {\n          // Event listeners are not supported for placeholder screens\n\n          return () => {\n            // Empty function\n          };\n        },\n        removeListener: () => {\n          // Event listeners are not supported for placeholder screens\n        },\n        dispatch,\n        getParent: id => {\n          if (id !== undefined && id === rest.getId()) {\n            return base;\n          }\n          return rest.getParent(id);\n        },\n        setOptions: () => {\n          throw new Error('Options cannot be set from a placeholder screen.');\n        },\n        isFocused: () => false\n      };\n    }, [navigation, router.actionCreators]);\n\n    // Cache object which holds navigation objects for each screen\n    // We use `React.useMemo` instead of `React.useRef` coz we want to invalidate it when deps change\n    // In reality, these deps will rarely change, if ever\n    const cache = React.useMemo(() => ({\n      current: {}\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [base, getState, navigation, setOptions, emitter]);\n    cache.current = state.routes.reduce((acc, route) => {\n      const previous = cache.current[route.key];\n      if (previous) {\n        // If a cached navigation object already exists, reuse it\n        acc[route.key] = previous;\n      } else {\n        const dispatch = thunk => {\n          const action = typeof thunk === 'function' ? thunk(getState()) : thunk;\n          if (action != null) {\n            navigation.dispatch({\n              source: route.key,\n              ...action\n            });\n          }\n        };\n        const withStack = callback => {\n          let isStackSet = false;\n          try {\n            if (process.env.NODE_ENV !== 'production' && stackRef && !stackRef.current) {\n              // Capture the stack trace for devtools\n              stackRef.current = new Error().stack;\n              isStackSet = true;\n            }\n            callback();\n          } finally {\n            if (isStackSet && stackRef) {\n              stackRef.current = undefined;\n            }\n          }\n        };\n        const actions = {\n          ...router.actionCreators,\n          ..._routers.CommonActions\n        };\n        const helpers = Object.keys(actions).reduce((acc, name) => {\n          acc[name] = (...args) => withStack(() =>\n          // @ts-expect-error: name is a valid key, but TypeScript is dumb\n          dispatch(actions[name](...args)));\n          return acc;\n        }, {});\n        acc[route.key] = {\n          ...base,\n          ...helpers,\n          // FIXME: too much work to fix the types for now\n          ...emitter.create(route.key),\n          dispatch: thunk => withStack(() => dispatch(thunk)),\n          getParent: id => {\n            if (id !== undefined && id === base.getId()) {\n              // If the passed id is the same as the current navigation id,\n              // we return the cached navigation object for the relevant route\n              return acc[route.key];\n            }\n            return base.getParent(id);\n          },\n          setOptions: options => {\n            setOptions(o => ({\n              ...o,\n              [route.key]: {\n                ...o[route.key],\n                ...options\n              }\n            }));\n          },\n          isFocused: () => {\n            const state = base.getState();\n            if (state.routes[state.index].key !== route.key) {\n              return false;\n            }\n\n            // If the current screen is focused, we also need to check if parent navigator is focused\n            // This makes sure that we return the focus state in the whole tree, not just this navigator\n            return navigation ? navigation.isFocused() : true;\n          }\n        };\n      }\n      return acc;\n    }, {});\n    return {\n      base,\n      navigations: cache.current\n    };\n  }\n});", "lineCount": 162, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useNavigationCache"], [7, 28, 1, 13], [7, 31, 1, 13, "useNavigationCache"], [7, 49, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_routers"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationBuilderContext"], [10, 31, 5, 0], [10, 34, 5, 0, "require"], [10, 41, 5, 0], [10, 42, 5, 0, "_dependencyMap"], [10, 56, 5, 0], [11, 2, 5, 73], [11, 11, 5, 73, "_interopRequireWildcard"], [11, 35, 5, 73, "e"], [11, 36, 5, 73], [11, 38, 5, 73, "t"], [11, 39, 5, 73], [11, 68, 5, 73, "WeakMap"], [11, 75, 5, 73], [11, 81, 5, 73, "r"], [11, 82, 5, 73], [11, 89, 5, 73, "WeakMap"], [11, 96, 5, 73], [11, 100, 5, 73, "n"], [11, 101, 5, 73], [11, 108, 5, 73, "WeakMap"], [11, 115, 5, 73], [11, 127, 5, 73, "_interopRequireWildcard"], [11, 150, 5, 73], [11, 162, 5, 73, "_interopRequireWildcard"], [11, 163, 5, 73, "e"], [11, 164, 5, 73], [11, 166, 5, 73, "t"], [11, 167, 5, 73], [11, 176, 5, 73, "t"], [11, 177, 5, 73], [11, 181, 5, 73, "e"], [11, 182, 5, 73], [11, 186, 5, 73, "e"], [11, 187, 5, 73], [11, 188, 5, 73, "__esModule"], [11, 198, 5, 73], [11, 207, 5, 73, "e"], [11, 208, 5, 73], [11, 214, 5, 73, "o"], [11, 215, 5, 73], [11, 217, 5, 73, "i"], [11, 218, 5, 73], [11, 220, 5, 73, "f"], [11, 221, 5, 73], [11, 226, 5, 73, "__proto__"], [11, 235, 5, 73], [11, 243, 5, 73, "default"], [11, 250, 5, 73], [11, 252, 5, 73, "e"], [11, 253, 5, 73], [11, 270, 5, 73, "e"], [11, 271, 5, 73], [11, 294, 5, 73, "e"], [11, 295, 5, 73], [11, 320, 5, 73, "e"], [11, 321, 5, 73], [11, 330, 5, 73, "f"], [11, 331, 5, 73], [11, 337, 5, 73, "o"], [11, 338, 5, 73], [11, 341, 5, 73, "t"], [11, 342, 5, 73], [11, 345, 5, 73, "n"], [11, 346, 5, 73], [11, 349, 5, 73, "r"], [11, 350, 5, 73], [11, 358, 5, 73, "o"], [11, 359, 5, 73], [11, 360, 5, 73, "has"], [11, 363, 5, 73], [11, 364, 5, 73, "e"], [11, 365, 5, 73], [11, 375, 5, 73, "o"], [11, 376, 5, 73], [11, 377, 5, 73, "get"], [11, 380, 5, 73], [11, 381, 5, 73, "e"], [11, 382, 5, 73], [11, 385, 5, 73, "o"], [11, 386, 5, 73], [11, 387, 5, 73, "set"], [11, 390, 5, 73], [11, 391, 5, 73, "e"], [11, 392, 5, 73], [11, 394, 5, 73, "f"], [11, 395, 5, 73], [11, 411, 5, 73, "t"], [11, 412, 5, 73], [11, 416, 5, 73, "e"], [11, 417, 5, 73], [11, 433, 5, 73, "t"], [11, 434, 5, 73], [11, 441, 5, 73, "hasOwnProperty"], [11, 455, 5, 73], [11, 456, 5, 73, "call"], [11, 460, 5, 73], [11, 461, 5, 73, "e"], [11, 462, 5, 73], [11, 464, 5, 73, "t"], [11, 465, 5, 73], [11, 472, 5, 73, "i"], [11, 473, 5, 73], [11, 477, 5, 73, "o"], [11, 478, 5, 73], [11, 481, 5, 73, "Object"], [11, 487, 5, 73], [11, 488, 5, 73, "defineProperty"], [11, 502, 5, 73], [11, 507, 5, 73, "Object"], [11, 513, 5, 73], [11, 514, 5, 73, "getOwnPropertyDescriptor"], [11, 538, 5, 73], [11, 539, 5, 73, "e"], [11, 540, 5, 73], [11, 542, 5, 73, "t"], [11, 543, 5, 73], [11, 550, 5, 73, "i"], [11, 551, 5, 73], [11, 552, 5, 73, "get"], [11, 555, 5, 73], [11, 559, 5, 73, "i"], [11, 560, 5, 73], [11, 561, 5, 73, "set"], [11, 564, 5, 73], [11, 568, 5, 73, "o"], [11, 569, 5, 73], [11, 570, 5, 73, "f"], [11, 571, 5, 73], [11, 573, 5, 73, "t"], [11, 574, 5, 73], [11, 576, 5, 73, "i"], [11, 577, 5, 73], [11, 581, 5, 73, "f"], [11, 582, 5, 73], [11, 583, 5, 73, "t"], [11, 584, 5, 73], [11, 588, 5, 73, "e"], [11, 589, 5, 73], [11, 590, 5, 73, "t"], [11, 591, 5, 73], [11, 602, 5, 73, "f"], [11, 603, 5, 73], [11, 608, 5, 73, "e"], [11, 609, 5, 73], [11, 611, 5, 73, "t"], [11, 612, 5, 73], [12, 2, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 2, 11, 7], [17, 11, 11, 16, "useNavigationCache"], [17, 29, 11, 34, "useNavigationCache"], [17, 30, 11, 35], [18, 4, 12, 2, "state"], [18, 9, 12, 7], [19, 4, 13, 2, "getState"], [19, 12, 13, 10], [20, 4, 14, 2, "navigation"], [20, 14, 14, 12], [21, 4, 15, 2, "setOptions"], [21, 14, 15, 12], [22, 4, 16, 2, "router"], [22, 10, 16, 8], [23, 4, 17, 2, "emitter"], [24, 2, 18, 0], [24, 3, 18, 1], [24, 5, 18, 3], [25, 4, 19, 2], [25, 10, 19, 8], [26, 6, 20, 4, "stackRef"], [27, 4, 21, 2], [27, 5, 21, 3], [27, 8, 21, 6, "React"], [27, 13, 21, 11], [27, 14, 21, 12, "useContext"], [27, 24, 21, 22], [27, 25, 21, 23, "NavigationBuilderContext"], [27, 75, 21, 47], [27, 76, 21, 48], [28, 4, 22, 2], [28, 10, 22, 8, "base"], [28, 14, 22, 12], [28, 17, 22, 15, "React"], [28, 22, 22, 20], [28, 23, 22, 21, "useMemo"], [28, 30, 22, 28], [28, 31, 22, 29], [28, 37, 22, 35], [29, 6, 23, 4], [30, 6, 24, 4], [30, 12, 24, 10], [31, 8, 25, 6, "emit"], [31, 12, 25, 10], [32, 8, 26, 6], [32, 11, 26, 9, "rest"], [33, 6, 27, 4], [33, 7, 27, 5], [33, 10, 27, 8, "navigation"], [33, 20, 27, 18], [34, 6, 28, 4], [34, 12, 28, 10, "actions"], [34, 19, 28, 17], [34, 22, 28, 20], [35, 8, 29, 6], [35, 11, 29, 9, "router"], [35, 17, 29, 15], [35, 18, 29, 16, "actionCreators"], [35, 32, 29, 30], [36, 8, 30, 6], [36, 11, 30, 9, "CommonActions"], [37, 6, 31, 4], [37, 7, 31, 5], [38, 6, 32, 4], [38, 12, 32, 10, "dispatch"], [38, 20, 32, 18], [38, 23, 32, 21, "dispatch"], [38, 24, 32, 21], [38, 29, 32, 27], [39, 8, 33, 6], [39, 14, 33, 12], [39, 18, 33, 16, "Error"], [39, 23, 33, 21], [39, 24, 33, 22], [39, 81, 33, 79], [39, 82, 33, 80], [40, 6, 34, 4], [40, 7, 34, 5], [41, 6, 35, 4], [41, 12, 35, 10, "helpers"], [41, 19, 35, 17], [41, 22, 35, 20, "Object"], [41, 28, 35, 26], [41, 29, 35, 27, "keys"], [41, 33, 35, 31], [41, 34, 35, 32, "actions"], [41, 41, 35, 39], [41, 42, 35, 40], [41, 43, 35, 41, "reduce"], [41, 49, 35, 47], [41, 50, 35, 48], [41, 51, 35, 49, "acc"], [41, 54, 35, 52], [41, 56, 35, 54, "name"], [41, 60, 35, 58], [41, 65, 35, 63], [42, 8, 36, 6, "acc"], [42, 11, 36, 9], [42, 12, 36, 10, "name"], [42, 16, 36, 14], [42, 17, 36, 15], [42, 20, 36, 18, "dispatch"], [42, 28, 36, 26], [43, 8, 37, 6], [43, 15, 37, 13, "acc"], [43, 18, 37, 16], [44, 6, 38, 4], [44, 7, 38, 5], [44, 9, 38, 7], [44, 10, 38, 8], [44, 11, 38, 9], [44, 12, 38, 10], [45, 6, 39, 4], [45, 13, 39, 11], [46, 8, 40, 6], [46, 11, 40, 9, "rest"], [46, 15, 40, 13], [47, 8, 41, 6], [47, 11, 41, 9, "helpers"], [47, 18, 41, 16], [48, 8, 42, 6, "addListener"], [48, 19, 42, 17], [48, 21, 42, 19, "addListener"], [48, 22, 42, 19], [48, 27, 42, 25], [49, 10, 43, 8], [51, 10, 45, 8], [51, 17, 45, 15], [51, 23, 45, 21], [52, 12, 46, 10], [53, 10, 46, 10], [53, 11, 47, 9], [54, 8, 48, 6], [54, 9, 48, 7], [55, 8, 49, 6, "removeListener"], [55, 22, 49, 20], [55, 24, 49, 22, "removeListener"], [55, 25, 49, 22], [55, 30, 49, 28], [56, 10, 50, 8], [57, 8, 50, 8], [57, 9, 51, 7], [58, 8, 52, 6, "dispatch"], [58, 16, 52, 14], [59, 8, 53, 6, "getParent"], [59, 17, 53, 15], [59, 19, 53, 17, "id"], [59, 21, 53, 19], [59, 25, 53, 23], [60, 10, 54, 8], [60, 14, 54, 12, "id"], [60, 16, 54, 14], [60, 21, 54, 19, "undefined"], [60, 30, 54, 28], [60, 34, 54, 32, "id"], [60, 36, 54, 34], [60, 41, 54, 39, "rest"], [60, 45, 54, 43], [60, 46, 54, 44, "getId"], [60, 51, 54, 49], [60, 52, 54, 50], [60, 53, 54, 51], [60, 55, 54, 53], [61, 12, 55, 10], [61, 19, 55, 17, "base"], [61, 23, 55, 21], [62, 10, 56, 8], [63, 10, 57, 8], [63, 17, 57, 15, "rest"], [63, 21, 57, 19], [63, 22, 57, 20, "getParent"], [63, 31, 57, 29], [63, 32, 57, 30, "id"], [63, 34, 57, 32], [63, 35, 57, 33], [64, 8, 58, 6], [64, 9, 58, 7], [65, 8, 59, 6, "setOptions"], [65, 18, 59, 16], [65, 20, 59, 18, "setOptions"], [65, 21, 59, 18], [65, 26, 59, 24], [66, 10, 60, 8], [66, 16, 60, 14], [66, 20, 60, 18, "Error"], [66, 25, 60, 23], [66, 26, 60, 24], [66, 76, 60, 74], [66, 77, 60, 75], [67, 8, 61, 6], [67, 9, 61, 7], [68, 8, 62, 6, "isFocused"], [68, 17, 62, 15], [68, 19, 62, 17, "isFocused"], [68, 20, 62, 17], [68, 25, 62, 23], [69, 6, 63, 4], [69, 7, 63, 5], [70, 4, 64, 2], [70, 5, 64, 3], [70, 7, 64, 5], [70, 8, 64, 6, "navigation"], [70, 18, 64, 16], [70, 20, 64, 18, "router"], [70, 26, 64, 24], [70, 27, 64, 25, "actionCreators"], [70, 41, 64, 39], [70, 42, 64, 40], [70, 43, 64, 41], [72, 4, 66, 2], [73, 4, 67, 2], [74, 4, 68, 2], [75, 4, 69, 2], [75, 10, 69, 8, "cache"], [75, 15, 69, 13], [75, 18, 69, 16, "React"], [75, 23, 69, 21], [75, 24, 69, 22, "useMemo"], [75, 31, 69, 29], [75, 32, 69, 30], [75, 39, 69, 37], [76, 6, 70, 4, "current"], [76, 13, 70, 11], [76, 15, 70, 13], [76, 16, 70, 14], [77, 4, 71, 2], [77, 5, 71, 3], [77, 6, 71, 4], [78, 4, 72, 2], [79, 4, 73, 2], [79, 5, 73, 3, "base"], [79, 9, 73, 7], [79, 11, 73, 9, "getState"], [79, 19, 73, 17], [79, 21, 73, 19, "navigation"], [79, 31, 73, 29], [79, 33, 73, 31, "setOptions"], [79, 43, 73, 41], [79, 45, 73, 43, "emitter"], [79, 52, 73, 50], [79, 53, 73, 51], [79, 54, 73, 52], [80, 4, 74, 2, "cache"], [80, 9, 74, 7], [80, 10, 74, 8, "current"], [80, 17, 74, 15], [80, 20, 74, 18, "state"], [80, 25, 74, 23], [80, 26, 74, 24, "routes"], [80, 32, 74, 30], [80, 33, 74, 31, "reduce"], [80, 39, 74, 37], [80, 40, 74, 38], [80, 41, 74, 39, "acc"], [80, 44, 74, 42], [80, 46, 74, 44, "route"], [80, 51, 74, 49], [80, 56, 74, 54], [81, 6, 75, 4], [81, 12, 75, 10, "previous"], [81, 20, 75, 18], [81, 23, 75, 21, "cache"], [81, 28, 75, 26], [81, 29, 75, 27, "current"], [81, 36, 75, 34], [81, 37, 75, 35, "route"], [81, 42, 75, 40], [81, 43, 75, 41, "key"], [81, 46, 75, 44], [81, 47, 75, 45], [82, 6, 76, 4], [82, 10, 76, 8, "previous"], [82, 18, 76, 16], [82, 20, 76, 18], [83, 8, 77, 6], [84, 8, 78, 6, "acc"], [84, 11, 78, 9], [84, 12, 78, 10, "route"], [84, 17, 78, 15], [84, 18, 78, 16, "key"], [84, 21, 78, 19], [84, 22, 78, 20], [84, 25, 78, 23, "previous"], [84, 33, 78, 31], [85, 6, 79, 4], [85, 7, 79, 5], [85, 13, 79, 11], [86, 8, 80, 6], [86, 14, 80, 12, "dispatch"], [86, 22, 80, 20], [86, 25, 80, 23, "thunk"], [86, 30, 80, 28], [86, 34, 80, 32], [87, 10, 81, 8], [87, 16, 81, 14, "action"], [87, 22, 81, 20], [87, 25, 81, 23], [87, 32, 81, 30, "thunk"], [87, 37, 81, 35], [87, 42, 81, 40], [87, 52, 81, 50], [87, 55, 81, 53, "thunk"], [87, 60, 81, 58], [87, 61, 81, 59, "getState"], [87, 69, 81, 67], [87, 70, 81, 68], [87, 71, 81, 69], [87, 72, 81, 70], [87, 75, 81, 73, "thunk"], [87, 80, 81, 78], [88, 10, 82, 8], [88, 14, 82, 12, "action"], [88, 20, 82, 18], [88, 24, 82, 22], [88, 28, 82, 26], [88, 30, 82, 28], [89, 12, 83, 10, "navigation"], [89, 22, 83, 20], [89, 23, 83, 21, "dispatch"], [89, 31, 83, 29], [89, 32, 83, 30], [90, 14, 84, 12, "source"], [90, 20, 84, 18], [90, 22, 84, 20, "route"], [90, 27, 84, 25], [90, 28, 84, 26, "key"], [90, 31, 84, 29], [91, 14, 85, 12], [91, 17, 85, 15, "action"], [92, 12, 86, 10], [92, 13, 86, 11], [92, 14, 86, 12], [93, 10, 87, 8], [94, 8, 88, 6], [94, 9, 88, 7], [95, 8, 89, 6], [95, 14, 89, 12, "withStack"], [95, 23, 89, 21], [95, 26, 89, 24, "callback"], [95, 34, 89, 32], [95, 38, 89, 36], [96, 10, 90, 8], [96, 14, 90, 12, "isStackSet"], [96, 24, 90, 22], [96, 27, 90, 25], [96, 32, 90, 30], [97, 10, 91, 8], [97, 14, 91, 12], [98, 12, 92, 10], [98, 16, 92, 14, "process"], [98, 23, 92, 21], [98, 24, 92, 22, "env"], [98, 27, 92, 25], [98, 28, 92, 26, "NODE_ENV"], [98, 36, 92, 34], [98, 41, 92, 39], [98, 53, 92, 51], [98, 57, 92, 55, "stackRef"], [98, 65, 92, 63], [98, 69, 92, 67], [98, 70, 92, 68, "stackRef"], [98, 78, 92, 76], [98, 79, 92, 77, "current"], [98, 86, 92, 84], [98, 88, 92, 86], [99, 14, 93, 12], [100, 14, 94, 12, "stackRef"], [100, 22, 94, 20], [100, 23, 94, 21, "current"], [100, 30, 94, 28], [100, 33, 94, 31], [100, 37, 94, 35, "Error"], [100, 42, 94, 40], [100, 43, 94, 41], [100, 44, 94, 42], [100, 45, 94, 43, "stack"], [100, 50, 94, 48], [101, 14, 95, 12, "isStackSet"], [101, 24, 95, 22], [101, 27, 95, 25], [101, 31, 95, 29], [102, 12, 96, 10], [103, 12, 97, 10, "callback"], [103, 20, 97, 18], [103, 21, 97, 19], [103, 22, 97, 20], [104, 10, 98, 8], [104, 11, 98, 9], [104, 20, 98, 18], [105, 12, 99, 10], [105, 16, 99, 14, "isStackSet"], [105, 26, 99, 24], [105, 30, 99, 28, "stackRef"], [105, 38, 99, 36], [105, 40, 99, 38], [106, 14, 100, 12, "stackRef"], [106, 22, 100, 20], [106, 23, 100, 21, "current"], [106, 30, 100, 28], [106, 33, 100, 31, "undefined"], [106, 42, 100, 40], [107, 12, 101, 10], [108, 10, 102, 8], [109, 8, 103, 6], [109, 9, 103, 7], [110, 8, 104, 6], [110, 14, 104, 12, "actions"], [110, 21, 104, 19], [110, 24, 104, 22], [111, 10, 105, 8], [111, 13, 105, 11, "router"], [111, 19, 105, 17], [111, 20, 105, 18, "actionCreators"], [111, 34, 105, 32], [112, 10, 106, 8], [112, 13, 106, 11, "CommonActions"], [113, 8, 107, 6], [113, 9, 107, 7], [114, 8, 108, 6], [114, 14, 108, 12, "helpers"], [114, 21, 108, 19], [114, 24, 108, 22, "Object"], [114, 30, 108, 28], [114, 31, 108, 29, "keys"], [114, 35, 108, 33], [114, 36, 108, 34, "actions"], [114, 43, 108, 41], [114, 44, 108, 42], [114, 45, 108, 43, "reduce"], [114, 51, 108, 49], [114, 52, 108, 50], [114, 53, 108, 51, "acc"], [114, 56, 108, 54], [114, 58, 108, 56, "name"], [114, 62, 108, 60], [114, 67, 108, 65], [115, 10, 109, 8, "acc"], [115, 13, 109, 11], [115, 14, 109, 12, "name"], [115, 18, 109, 16], [115, 19, 109, 17], [115, 22, 109, 20], [115, 23, 109, 21], [115, 26, 109, 24, "args"], [115, 30, 109, 28], [115, 35, 109, 33, "withStack"], [115, 44, 109, 42], [115, 45, 109, 43], [116, 10, 110, 8], [117, 10, 111, 8, "dispatch"], [117, 18, 111, 16], [117, 19, 111, 17, "actions"], [117, 26, 111, 24], [117, 27, 111, 25, "name"], [117, 31, 111, 29], [117, 32, 111, 30], [117, 33, 111, 31], [117, 36, 111, 34, "args"], [117, 40, 111, 38], [117, 41, 111, 39], [117, 42, 111, 40], [117, 43, 111, 41], [118, 10, 112, 8], [118, 17, 112, 15, "acc"], [118, 20, 112, 18], [119, 8, 113, 6], [119, 9, 113, 7], [119, 11, 113, 9], [119, 12, 113, 10], [119, 13, 113, 11], [119, 14, 113, 12], [120, 8, 114, 6, "acc"], [120, 11, 114, 9], [120, 12, 114, 10, "route"], [120, 17, 114, 15], [120, 18, 114, 16, "key"], [120, 21, 114, 19], [120, 22, 114, 20], [120, 25, 114, 23], [121, 10, 115, 8], [121, 13, 115, 11, "base"], [121, 17, 115, 15], [122, 10, 116, 8], [122, 13, 116, 11, "helpers"], [122, 20, 116, 18], [123, 10, 117, 8], [124, 10, 118, 8], [124, 13, 118, 11, "emitter"], [124, 20, 118, 18], [124, 21, 118, 19, "create"], [124, 27, 118, 25], [124, 28, 118, 26, "route"], [124, 33, 118, 31], [124, 34, 118, 32, "key"], [124, 37, 118, 35], [124, 38, 118, 36], [125, 10, 119, 8, "dispatch"], [125, 18, 119, 16], [125, 20, 119, 18, "thunk"], [125, 25, 119, 23], [125, 29, 119, 27, "withStack"], [125, 38, 119, 36], [125, 39, 119, 37], [125, 45, 119, 43, "dispatch"], [125, 53, 119, 51], [125, 54, 119, 52, "thunk"], [125, 59, 119, 57], [125, 60, 119, 58], [125, 61, 119, 59], [126, 10, 120, 8, "getParent"], [126, 19, 120, 17], [126, 21, 120, 19, "id"], [126, 23, 120, 21], [126, 27, 120, 25], [127, 12, 121, 10], [127, 16, 121, 14, "id"], [127, 18, 121, 16], [127, 23, 121, 21, "undefined"], [127, 32, 121, 30], [127, 36, 121, 34, "id"], [127, 38, 121, 36], [127, 43, 121, 41, "base"], [127, 47, 121, 45], [127, 48, 121, 46, "getId"], [127, 53, 121, 51], [127, 54, 121, 52], [127, 55, 121, 53], [127, 57, 121, 55], [128, 14, 122, 12], [129, 14, 123, 12], [130, 14, 124, 12], [130, 21, 124, 19, "acc"], [130, 24, 124, 22], [130, 25, 124, 23, "route"], [130, 30, 124, 28], [130, 31, 124, 29, "key"], [130, 34, 124, 32], [130, 35, 124, 33], [131, 12, 125, 10], [132, 12, 126, 10], [132, 19, 126, 17, "base"], [132, 23, 126, 21], [132, 24, 126, 22, "getParent"], [132, 33, 126, 31], [132, 34, 126, 32, "id"], [132, 36, 126, 34], [132, 37, 126, 35], [133, 10, 127, 8], [133, 11, 127, 9], [134, 10, 128, 8, "setOptions"], [134, 20, 128, 18], [134, 22, 128, 20, "options"], [134, 29, 128, 27], [134, 33, 128, 31], [135, 12, 129, 10, "setOptions"], [135, 22, 129, 20], [135, 23, 129, 21, "o"], [135, 24, 129, 22], [135, 29, 129, 27], [136, 14, 130, 12], [136, 17, 130, 15, "o"], [136, 18, 130, 16], [137, 14, 131, 12], [137, 15, 131, 13, "route"], [137, 20, 131, 18], [137, 21, 131, 19, "key"], [137, 24, 131, 22], [137, 27, 131, 25], [138, 16, 132, 14], [138, 19, 132, 17, "o"], [138, 20, 132, 18], [138, 21, 132, 19, "route"], [138, 26, 132, 24], [138, 27, 132, 25, "key"], [138, 30, 132, 28], [138, 31, 132, 29], [139, 16, 133, 14], [139, 19, 133, 17, "options"], [140, 14, 134, 12], [141, 12, 135, 10], [141, 13, 135, 11], [141, 14, 135, 12], [141, 15, 135, 13], [142, 10, 136, 8], [142, 11, 136, 9], [143, 10, 137, 8, "isFocused"], [143, 19, 137, 17], [143, 21, 137, 19, "isFocused"], [143, 22, 137, 19], [143, 27, 137, 25], [144, 12, 138, 10], [144, 18, 138, 16, "state"], [144, 23, 138, 21], [144, 26, 138, 24, "base"], [144, 30, 138, 28], [144, 31, 138, 29, "getState"], [144, 39, 138, 37], [144, 40, 138, 38], [144, 41, 138, 39], [145, 12, 139, 10], [145, 16, 139, 14, "state"], [145, 21, 139, 19], [145, 22, 139, 20, "routes"], [145, 28, 139, 26], [145, 29, 139, 27, "state"], [145, 34, 139, 32], [145, 35, 139, 33, "index"], [145, 40, 139, 38], [145, 41, 139, 39], [145, 42, 139, 40, "key"], [145, 45, 139, 43], [145, 50, 139, 48, "route"], [145, 55, 139, 53], [145, 56, 139, 54, "key"], [145, 59, 139, 57], [145, 61, 139, 59], [146, 14, 140, 12], [146, 21, 140, 19], [146, 26, 140, 24], [147, 12, 141, 10], [149, 12, 143, 10], [150, 12, 144, 10], [151, 12, 145, 10], [151, 19, 145, 17, "navigation"], [151, 29, 145, 27], [151, 32, 145, 30, "navigation"], [151, 42, 145, 40], [151, 43, 145, 41, "isFocused"], [151, 52, 145, 50], [151, 53, 145, 51], [151, 54, 145, 52], [151, 57, 145, 55], [151, 61, 145, 59], [152, 10, 146, 8], [153, 8, 147, 6], [153, 9, 147, 7], [154, 6, 148, 4], [155, 6, 149, 4], [155, 13, 149, 11, "acc"], [155, 16, 149, 14], [156, 4, 150, 2], [156, 5, 150, 3], [156, 7, 150, 5], [156, 8, 150, 6], [156, 9, 150, 7], [156, 10, 150, 8], [157, 4, 151, 2], [157, 11, 151, 9], [158, 6, 152, 4, "base"], [158, 10, 152, 8], [159, 6, 153, 4, "navigations"], [159, 17, 153, 15], [159, 19, 153, 17, "cache"], [159, 24, 153, 22], [159, 25, 153, 23, "current"], [160, 4, 154, 2], [160, 5, 154, 3], [161, 2, 155, 0], [162, 0, 155, 1], [162, 3]], "functionMap": {"names": ["<global>", "useNavigationCache", "React.useMemo$argument_0", "dispatch", "Object.keys.reduce$argument_0", "addListener", "<anonymous>", "removeListener", "getParent", "setOptions", "isFocused", "state.routes.reduce$argument_0", "withStack", "acc.name", "withStack$argument_0", "acc.route.key.dispatch", "acc.route.key.getParent", "acc.route.key.setOptions", "setOptions$argument_0", "acc.route.key.isFocused"], "mappings": "AAA;OCU;6BCW;qBCU;KDE;gDEC;KFG;mBGI;eCG;SDE;OHC;sBKC;OLE;iBME;ONK;kBOC;OPE;iBQC,WR;GDE;8BCK;IDE;sCUG;uBRM;OQQ;wBCC;ODc;kDPK;oBSC,uBC;wCDE,CT;OOE;kBIM,mBD,qBC,CJ;mBKC;SLO;oBMC;qBCC;YDM;SNC;mBQC;SRS;GVI;CDK"}}, "type": "js/module"}]}