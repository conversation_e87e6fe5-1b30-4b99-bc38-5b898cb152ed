{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Core/ExceptionsManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 78, "index": 78}}], "key": "nf2/pTHjhpoe+CFOYQW3I0P9FoM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ExceptionsManager = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Core/ExceptionsManager\"));\n  var _default = exports.default = _ExceptionsManager.default;\n});", "lineCount": 9, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_ExceptionsManager"], [7, 24, 1, 0], [7, 27, 1, 0, "_interopRequireDefault"], [7, 49, 1, 0], [7, 50, 1, 0, "require"], [7, 57, 1, 0], [7, 58, 1, 0, "_dependencyMap"], [7, 72, 1, 0], [8, 2, 1, 78], [8, 6, 1, 78, "_default"], [8, 14, 1, 78], [8, 17, 1, 78, "exports"], [8, 24, 1, 78], [8, 25, 1, 78, "default"], [8, 32, 1, 78], [8, 35, 3, 15, "ExceptionsManager"], [8, 61, 3, 32], [9, 0, 3, 32], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}