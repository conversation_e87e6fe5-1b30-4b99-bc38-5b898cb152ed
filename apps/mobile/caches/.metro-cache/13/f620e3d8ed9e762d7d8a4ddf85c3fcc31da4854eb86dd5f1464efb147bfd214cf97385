{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  /**\n   * This is a hack for Expo Router to support Fast Refresh on _layout files\n   *\n   * Fast Refresh only works when:\n   *  - Files only export React Components\n   *  - All inverse dependencies only export React Components\n   *\n   * Expo Router's _layout files support exporting both 'unstable_settings' and 'ErrorBoundary'\n   *\n   * 'unstable_settings':\n   *  - This is a plain object, so it will break Fast Refresh\n   *\n   * 'ErrorBoundary'\n   *  - While this is a React component, it is imported from 'expo-router'\n   *  - 'expo-router' has an inverse dependency on _ctx, which is a require.context object\n   *\n   * 'generateStaticParams'\n   *  - This is a function that is not a React Component, so it will break Fast Refresh\n   *\n   *\n   * To resolve this issue, we extend ReactRefresh to flag these exports as React components\n   *\n   * @see https://reactnative.dev/docs/fast-refresh\n   */\n  if (process.env.NODE_ENV === 'development') {\n    if (\n    // Should be a string at runtime\n    typeof __METRO_GLOBAL_PREFIX__ !== 'undefined' &&\n    // Should be set by Metro's require polyfill\n    global[__METRO_GLOBAL_PREFIX__ + '__ReactRefresh']) {\n      // source: https://github.com/facebook/metro/blob/main/packages/metro-runtime/src/polyfills/require.js\n      // TODO(@kitten): Add type for this and use `globalThis` over `global`\n      var Refresh = global[__METRO_GLOBAL_PREFIX__ + '__ReactRefresh'];\n      // Keep a reference to the original\n      var isLikelyComponentType = Refresh.isLikelyComponentType;\n      // Modules can be dereferenced at any time\n      var expoRouterExports = new WeakSet();\n      Object.assign(Refresh, {\n        /*\n         * isLikelyComponentType is called twice.\n         *   1. Initially with a modules export object\n         *   2. With each individual export of a module\n         */\n        isLikelyComponentType(value) {\n          try {\n            if (typeof value === 'object') {\n              if ('unstable_settings' in value) {\n                expoRouterExports.add(value.unstable_settings);\n              }\n              if ('ErrorBoundary' in value) {\n                expoRouterExports.add(value.ErrorBoundary);\n              }\n              if ('generateStaticParams' in value) {\n                expoRouterExports.add(value.generateStaticParams);\n              }\n            }\n          } catch {\n            // Ignore - we're just trying to avoid breaking Fast Refresh by using exports\n            // that aren't JS objects valid as keys for the WeakSet - like we've seen with\n            // some JSI::HostObject instances that are exported in a module - see #33670\n            // https://github.com/expo/expo/issues/33670\n          }\n          return expoRouterExports.has(value) || isLikelyComponentType(value);\n        }\n      });\n    }\n  }\n});", "lineCount": 74, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0], [8, 0, 4, 0], [9, 0, 5, 0], [10, 0, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 0, 20, 0], [25, 0, 21, 0], [26, 0, 22, 0], [27, 0, 23, 0], [28, 0, 24, 0], [29, 0, 25, 0], [30, 0, 26, 0], [31, 2, 27, 0], [31, 6, 27, 4, "process"], [31, 13, 27, 11], [31, 14, 27, 12, "env"], [31, 17, 27, 15], [31, 18, 27, 16, "NODE_ENV"], [31, 26, 27, 24], [31, 31, 27, 29], [31, 44, 27, 42], [31, 46, 27, 44], [32, 4, 28, 4], [33, 4, 29, 4], [34, 4, 30, 4], [34, 11, 30, 11, "__METRO_GLOBAL_PREFIX__"], [34, 34, 30, 34], [34, 39, 30, 39], [34, 50, 30, 50], [35, 4, 31, 8], [36, 4, 32, 8, "global"], [36, 10, 32, 14], [36, 11, 32, 15, "__METRO_GLOBAL_PREFIX__"], [36, 34, 32, 38], [36, 37, 32, 41], [36, 53, 32, 57], [36, 54, 32, 58], [36, 56, 32, 60], [37, 6, 33, 8], [38, 6, 34, 8], [39, 6, 35, 8], [39, 10, 35, 14, "Refresh"], [39, 17, 35, 21], [39, 20, 35, 24, "global"], [39, 26, 35, 30], [39, 27, 35, 31, "__METRO_GLOBAL_PREFIX__"], [39, 50, 35, 54], [39, 53, 35, 57], [39, 69, 35, 73], [39, 70, 35, 74], [40, 6, 36, 8], [41, 6, 37, 8], [41, 10, 37, 14, "isLikelyComponentType"], [41, 31, 37, 35], [41, 34, 37, 38, "Refresh"], [41, 41, 37, 45], [41, 42, 37, 46, "isLikelyComponentType"], [41, 63, 37, 67], [42, 6, 38, 8], [43, 6, 39, 8], [43, 10, 39, 14, "expoRouterExports"], [43, 27, 39, 31], [43, 30, 39, 34], [43, 34, 39, 38, "WeakSet"], [43, 41, 39, 45], [43, 42, 39, 46], [43, 43, 39, 47], [44, 6, 40, 8, "Object"], [44, 12, 40, 14], [44, 13, 40, 15, "assign"], [44, 19, 40, 21], [44, 20, 40, 22, "Refresh"], [44, 27, 40, 29], [44, 29, 40, 31], [45, 8, 41, 12], [46, 0, 42, 0], [47, 0, 43, 0], [48, 0, 44, 0], [49, 0, 45, 0], [50, 8, 46, 12, "isLikelyComponentType"], [50, 29, 46, 33, "isLikelyComponentType"], [50, 30, 46, 34, "value"], [50, 35, 46, 39], [50, 37, 46, 41], [51, 10, 47, 16], [51, 14, 47, 20], [52, 12, 48, 20], [52, 16, 48, 24], [52, 23, 48, 31, "value"], [52, 28, 48, 36], [52, 33, 48, 41], [52, 41, 48, 49], [52, 43, 48, 51], [53, 14, 49, 24], [53, 18, 49, 28], [53, 37, 49, 47], [53, 41, 49, 51, "value"], [53, 46, 49, 56], [53, 48, 49, 58], [54, 16, 50, 28, "expoRouterExports"], [54, 33, 50, 45], [54, 34, 50, 46, "add"], [54, 37, 50, 49], [54, 38, 50, 50, "value"], [54, 43, 50, 55], [54, 44, 50, 56, "unstable_settings"], [54, 61, 50, 73], [54, 62, 50, 74], [55, 14, 51, 24], [56, 14, 52, 24], [56, 18, 52, 28], [56, 33, 52, 43], [56, 37, 52, 47, "value"], [56, 42, 52, 52], [56, 44, 52, 54], [57, 16, 53, 28, "expoRouterExports"], [57, 33, 53, 45], [57, 34, 53, 46, "add"], [57, 37, 53, 49], [57, 38, 53, 50, "value"], [57, 43, 53, 55], [57, 44, 53, 56, "Error<PERSON>ou<PERSON><PERSON>"], [57, 57, 53, 69], [57, 58, 53, 70], [58, 14, 54, 24], [59, 14, 55, 24], [59, 18, 55, 28], [59, 40, 55, 50], [59, 44, 55, 54, "value"], [59, 49, 55, 59], [59, 51, 55, 61], [60, 16, 56, 28, "expoRouterExports"], [60, 33, 56, 45], [60, 34, 56, 46, "add"], [60, 37, 56, 49], [60, 38, 56, 50, "value"], [60, 43, 56, 55], [60, 44, 56, 56, "generateStaticParams"], [60, 64, 56, 76], [60, 65, 56, 77], [61, 14, 57, 24], [62, 12, 58, 20], [63, 10, 59, 16], [63, 11, 59, 17], [63, 12, 60, 16], [63, 18, 60, 22], [64, 12, 61, 20], [65, 12, 62, 20], [66, 12, 63, 20], [67, 12, 64, 20], [68, 10, 64, 20], [69, 10, 66, 16], [69, 17, 66, 23, "expoRouterExports"], [69, 34, 66, 40], [69, 35, 66, 41, "has"], [69, 38, 66, 44], [69, 39, 66, 45, "value"], [69, 44, 66, 50], [69, 45, 66, 51], [69, 49, 66, 55, "isLikelyComponentType"], [69, 70, 66, 76], [69, 71, 66, 77, "value"], [69, 76, 66, 82], [69, 77, 66, 83], [70, 8, 67, 12], [71, 6, 68, 8], [71, 7, 68, 9], [71, 8, 68, 10], [72, 4, 69, 4], [73, 2, 70, 0], [74, 0, 70, 1], [74, 3]], "functionMap": {"names": ["<global>", "Object.assign$argument_1.isLikelyComponentType"], "mappings": "AAA;YC6C;aDqB"}}, "type": "js/module"}]}