{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RNRenderer = void 0;\n  const RNRenderer = exports.RNRenderer = {\n    findHostInstance_DEPRECATED: _ref => null\n  };\n});", "lineCount": 9, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "<PERSON><PERSON><PERSON><PERSON>"], [6, 18, 1, 23], [6, 21, 1, 23, "exports"], [6, 28, 1, 23], [6, 29, 1, 23, "<PERSON><PERSON><PERSON><PERSON>"], [6, 39, 1, 23], [6, 42, 1, 26], [7, 4, 2, 2, "findHostInstance_DEPRECATED"], [7, 31, 2, 29], [7, 33, 2, 31, "_ref"], [7, 37, 2, 35], [7, 41, 2, 39], [8, 2, 3, 0], [8, 3, 3, 1], [9, 0, 3, 2], [9, 3]], "functionMap": {"names": ["<global>", "findHostInstance_DEPRECATED"], "mappings": "AAA;+BCC,YD"}}, "type": "js/module"}]}