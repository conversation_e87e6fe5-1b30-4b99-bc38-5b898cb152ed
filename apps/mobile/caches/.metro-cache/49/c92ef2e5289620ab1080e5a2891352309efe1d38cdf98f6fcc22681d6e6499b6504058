{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Videotape = exports.default = (0, _createLucideIcon.default)(\"Videotape\", [[\"rect\", {\n    width: \"20\",\n    height: \"16\",\n    x: \"2\",\n    y: \"4\",\n    rx: \"2\",\n    key: \"18n3k1\"\n  }], [\"path\", {\n    d: \"M2 8h20\",\n    key: \"d11cs7\"\n  }], [\"circle\", {\n    cx: \"8\",\n    cy: \"14\",\n    r: \"2\",\n    key: \"1k2qr5\"\n  }], [\"path\", {\n    d: \"M8 12h8\",\n    key: \"1wcyev\"\n  }], [\"circle\", {\n    cx: \"16\",\n    cy: \"14\",\n    r: \"2\",\n    key: \"14k7lr\"\n  }]]);\n});", "lineCount": 39, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Videotape"], [15, 17, 10, 15], [15, 20, 10, 15, "exports"], [15, 27, 10, 15], [15, 28, 10, 15, "default"], [15, 35, 10, 15], [15, 38, 10, 18], [15, 42, 10, 18, "createLucideIcon"], [15, 67, 10, 34], [15, 69, 10, 35], [15, 80, 10, 46], [15, 82, 10, 48], [15, 83, 11, 2], [15, 84, 11, 3], [15, 90, 11, 9], [15, 92, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 15, 11, 24], [17, 4, 11, 26, "height"], [17, 10, 11, 32], [17, 12, 11, 34], [17, 16, 11, 38], [18, 4, 11, 40, "x"], [18, 5, 11, 41], [18, 7, 11, 43], [18, 10, 11, 46], [19, 4, 11, 48, "y"], [19, 5, 11, 49], [19, 7, 11, 51], [19, 10, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "key"], [21, 7, 11, 68], [21, 9, 11, 70], [22, 2, 11, 79], [22, 3, 11, 80], [22, 4, 11, 81], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [22, 15, 12, 11], [23, 4, 12, 13, "d"], [23, 5, 12, 14], [23, 7, 12, 16], [23, 16, 12, 25], [24, 4, 12, 27, "key"], [24, 7, 12, 30], [24, 9, 12, 32], [25, 2, 12, 41], [25, 3, 12, 42], [25, 4, 12, 43], [25, 6, 13, 2], [25, 7, 13, 3], [25, 15, 13, 11], [25, 17, 13, 13], [26, 4, 13, 15, "cx"], [26, 6, 13, 17], [26, 8, 13, 19], [26, 11, 13, 22], [27, 4, 13, 24, "cy"], [27, 6, 13, 26], [27, 8, 13, 28], [27, 12, 13, 32], [28, 4, 13, 34, "r"], [28, 5, 13, 35], [28, 7, 13, 37], [28, 10, 13, 40], [29, 4, 13, 42, "key"], [29, 7, 13, 45], [29, 9, 13, 47], [30, 2, 13, 56], [30, 3, 13, 57], [30, 4, 13, 58], [30, 6, 14, 2], [30, 7, 14, 3], [30, 13, 14, 9], [30, 15, 14, 11], [31, 4, 14, 13, "d"], [31, 5, 14, 14], [31, 7, 14, 16], [31, 16, 14, 25], [32, 4, 14, 27, "key"], [32, 7, 14, 30], [32, 9, 14, 32], [33, 2, 14, 41], [33, 3, 14, 42], [33, 4, 14, 43], [33, 6, 15, 2], [33, 7, 15, 3], [33, 15, 15, 11], [33, 17, 15, 13], [34, 4, 15, 15, "cx"], [34, 6, 15, 17], [34, 8, 15, 19], [34, 12, 15, 23], [35, 4, 15, 25, "cy"], [35, 6, 15, 27], [35, 8, 15, 29], [35, 12, 15, 33], [36, 4, 15, 35, "r"], [36, 5, 15, 36], [36, 7, 15, 38], [36, 10, 15, 41], [37, 4, 15, 43, "key"], [37, 7, 15, 46], [37, 9, 15, 48], [38, 2, 15, 57], [38, 3, 15, 58], [38, 4, 15, 59], [38, 5, 16, 1], [38, 6, 16, 2], [39, 0, 16, 3], [39, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}