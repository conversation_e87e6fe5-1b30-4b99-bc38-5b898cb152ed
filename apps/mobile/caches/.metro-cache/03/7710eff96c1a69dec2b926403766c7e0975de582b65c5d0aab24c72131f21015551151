{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 44}, "end": {"line": 4, "column": 31, "index": 75}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "@tanstack/query-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 76}, "end": {"line": 5, "column": 56, "index": 132}}], "key": "GAsr4MDTe9ve1mRxgvML4iY2BZg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use client\";\n\n  // src/errorBoundaryUtils.ts\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useClearResetErrorBoundary = exports.getHasError = exports.ensurePreventErrorBoundaryRetry = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _queryCore = require(_dependencyMap[1], \"@tanstack/query-core\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n    if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n      if (!errorResetBoundary.isReset()) {\n        options.retryOnMount = false;\n      }\n    }\n  };\n  exports.ensurePreventErrorBoundaryRetry = ensurePreventErrorBoundaryRetry;\n  var useClearResetErrorBoundary = errorResetBoundary => {\n    React.useEffect(() => {\n      errorResetBoundary.clearReset();\n    }, [errorResetBoundary]);\n  };\n  exports.useClearResetErrorBoundary = useClearResetErrorBoundary;\n  var getHasError = _ref => {\n    var result = _ref.result,\n      errorResetBoundary = _ref.errorResetBoundary,\n      throwOnError = _ref.throwOnError,\n      query = _ref.query,\n      suspense = _ref.suspense;\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0, _queryCore.shouldThrowError)(throwOnError, [result.error, query]));\n  };\n  exports.getHasError = getHasError;\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 0], [5, 9, 3, 0, "defineProperty"], [5, 23, 3, 0], [5, 24, 3, 0, "exports"], [5, 31, 3, 0], [6, 4, 3, 0, "value"], [6, 9, 3, 0], [7, 2, 3, 0], [8, 2, 3, 0, "exports"], [8, 9, 3, 0], [8, 10, 3, 0, "useClearResetErrorBoundary"], [8, 36, 3, 0], [8, 39, 3, 0, "exports"], [8, 46, 3, 0], [8, 47, 3, 0, "getHasError"], [8, 58, 3, 0], [8, 61, 3, 0, "exports"], [8, 68, 3, 0], [8, 69, 3, 0, "ensurePreventErrorBoundaryRetry"], [8, 100, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "React"], [9, 11, 4, 0], [9, 14, 4, 0, "_interopRequireWildcard"], [9, 37, 4, 0], [9, 38, 4, 0, "require"], [9, 45, 4, 0], [9, 46, 4, 0, "_dependencyMap"], [9, 60, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_queryCore"], [10, 16, 5, 0], [10, 19, 5, 0, "require"], [10, 26, 5, 0], [10, 27, 5, 0, "_dependencyMap"], [10, 41, 5, 0], [11, 2, 5, 56], [11, 11, 5, 56, "_interopRequireWildcard"], [11, 35, 5, 56, "e"], [11, 36, 5, 56], [11, 38, 5, 56, "t"], [11, 39, 5, 56], [11, 68, 5, 56, "WeakMap"], [11, 75, 5, 56], [11, 81, 5, 56, "r"], [11, 82, 5, 56], [11, 89, 5, 56, "WeakMap"], [11, 96, 5, 56], [11, 100, 5, 56, "n"], [11, 101, 5, 56], [11, 108, 5, 56, "WeakMap"], [11, 115, 5, 56], [11, 127, 5, 56, "_interopRequireWildcard"], [11, 150, 5, 56], [11, 162, 5, 56, "_interopRequireWildcard"], [11, 163, 5, 56, "e"], [11, 164, 5, 56], [11, 166, 5, 56, "t"], [11, 167, 5, 56], [11, 176, 5, 56, "t"], [11, 177, 5, 56], [11, 181, 5, 56, "e"], [11, 182, 5, 56], [11, 186, 5, 56, "e"], [11, 187, 5, 56], [11, 188, 5, 56, "__esModule"], [11, 198, 5, 56], [11, 207, 5, 56, "e"], [11, 208, 5, 56], [11, 214, 5, 56, "o"], [11, 215, 5, 56], [11, 217, 5, 56, "i"], [11, 218, 5, 56], [11, 220, 5, 56, "f"], [11, 221, 5, 56], [11, 226, 5, 56, "__proto__"], [11, 235, 5, 56], [11, 243, 5, 56, "default"], [11, 250, 5, 56], [11, 252, 5, 56, "e"], [11, 253, 5, 56], [11, 270, 5, 56, "e"], [11, 271, 5, 56], [11, 294, 5, 56, "e"], [11, 295, 5, 56], [11, 320, 5, 56, "e"], [11, 321, 5, 56], [11, 330, 5, 56, "f"], [11, 331, 5, 56], [11, 337, 5, 56, "o"], [11, 338, 5, 56], [11, 341, 5, 56, "t"], [11, 342, 5, 56], [11, 345, 5, 56, "n"], [11, 346, 5, 56], [11, 349, 5, 56, "r"], [11, 350, 5, 56], [11, 358, 5, 56, "o"], [11, 359, 5, 56], [11, 360, 5, 56, "has"], [11, 363, 5, 56], [11, 364, 5, 56, "e"], [11, 365, 5, 56], [11, 375, 5, 56, "o"], [11, 376, 5, 56], [11, 377, 5, 56, "get"], [11, 380, 5, 56], [11, 381, 5, 56, "e"], [11, 382, 5, 56], [11, 385, 5, 56, "o"], [11, 386, 5, 56], [11, 387, 5, 56, "set"], [11, 390, 5, 56], [11, 391, 5, 56, "e"], [11, 392, 5, 56], [11, 394, 5, 56, "f"], [11, 395, 5, 56], [11, 409, 5, 56, "_t"], [11, 411, 5, 56], [11, 415, 5, 56, "e"], [11, 416, 5, 56], [11, 432, 5, 56, "_t"], [11, 434, 5, 56], [11, 441, 5, 56, "hasOwnProperty"], [11, 455, 5, 56], [11, 456, 5, 56, "call"], [11, 460, 5, 56], [11, 461, 5, 56, "e"], [11, 462, 5, 56], [11, 464, 5, 56, "_t"], [11, 466, 5, 56], [11, 473, 5, 56, "i"], [11, 474, 5, 56], [11, 478, 5, 56, "o"], [11, 479, 5, 56], [11, 482, 5, 56, "Object"], [11, 488, 5, 56], [11, 489, 5, 56, "defineProperty"], [11, 503, 5, 56], [11, 508, 5, 56, "Object"], [11, 514, 5, 56], [11, 515, 5, 56, "getOwnPropertyDescriptor"], [11, 539, 5, 56], [11, 540, 5, 56, "e"], [11, 541, 5, 56], [11, 543, 5, 56, "_t"], [11, 545, 5, 56], [11, 552, 5, 56, "i"], [11, 553, 5, 56], [11, 554, 5, 56, "get"], [11, 557, 5, 56], [11, 561, 5, 56, "i"], [11, 562, 5, 56], [11, 563, 5, 56, "set"], [11, 566, 5, 56], [11, 570, 5, 56, "o"], [11, 571, 5, 56], [11, 572, 5, 56, "f"], [11, 573, 5, 56], [11, 575, 5, 56, "_t"], [11, 577, 5, 56], [11, 579, 5, 56, "i"], [11, 580, 5, 56], [11, 584, 5, 56, "f"], [11, 585, 5, 56], [11, 586, 5, 56, "_t"], [11, 588, 5, 56], [11, 592, 5, 56, "e"], [11, 593, 5, 56], [11, 594, 5, 56, "_t"], [11, 596, 5, 56], [11, 607, 5, 56, "f"], [11, 608, 5, 56], [11, 613, 5, 56, "e"], [11, 614, 5, 56], [11, 616, 5, 56, "t"], [11, 617, 5, 56], [12, 2, 6, 0], [12, 6, 6, 4, "ensurePreventErrorBoundaryRetry"], [12, 37, 6, 35], [12, 40, 6, 38, "ensurePreventErrorBoundaryRetry"], [12, 41, 6, 39, "options"], [12, 48, 6, 46], [12, 50, 6, 48, "errorResetBoundary"], [12, 68, 6, 66], [12, 73, 6, 71], [13, 4, 7, 2], [13, 8, 7, 6, "options"], [13, 15, 7, 13], [13, 16, 7, 14, "suspense"], [13, 24, 7, 22], [13, 28, 7, 26, "options"], [13, 35, 7, 33], [13, 36, 7, 34, "throwOnError"], [13, 48, 7, 46], [13, 52, 7, 50, "options"], [13, 59, 7, 57], [13, 60, 7, 58, "experimental_prefetchInRender"], [13, 89, 7, 87], [13, 91, 7, 89], [14, 6, 8, 4], [14, 10, 8, 8], [14, 11, 8, 9, "errorResetBoundary"], [14, 29, 8, 27], [14, 30, 8, 28, "isReset"], [14, 37, 8, 35], [14, 38, 8, 36], [14, 39, 8, 37], [14, 41, 8, 39], [15, 8, 9, 6, "options"], [15, 15, 9, 13], [15, 16, 9, 14, "retryOnMount"], [15, 28, 9, 26], [15, 31, 9, 29], [15, 36, 9, 34], [16, 6, 10, 4], [17, 4, 11, 2], [18, 2, 12, 0], [18, 3, 12, 1], [19, 2, 12, 2, "exports"], [19, 9, 12, 2], [19, 10, 12, 2, "ensurePreventErrorBoundaryRetry"], [19, 41, 12, 2], [19, 44, 12, 2, "ensurePreventErrorBoundaryRetry"], [19, 75, 12, 2], [20, 2, 13, 0], [20, 6, 13, 4, "useClearResetErrorBoundary"], [20, 32, 13, 30], [20, 35, 13, 34, "errorResetBoundary"], [20, 53, 13, 52], [20, 57, 13, 57], [21, 4, 14, 2, "React"], [21, 9, 14, 7], [21, 10, 14, 8, "useEffect"], [21, 19, 14, 17], [21, 20, 14, 18], [21, 26, 14, 24], [22, 6, 15, 4, "errorResetBoundary"], [22, 24, 15, 22], [22, 25, 15, 23, "clear<PERSON><PERSON>t"], [22, 35, 15, 33], [22, 36, 15, 34], [22, 37, 15, 35], [23, 4, 16, 2], [23, 5, 16, 3], [23, 7, 16, 5], [23, 8, 16, 6, "errorResetBoundary"], [23, 26, 16, 24], [23, 27, 16, 25], [23, 28, 16, 26], [24, 2, 17, 0], [24, 3, 17, 1], [25, 2, 17, 2, "exports"], [25, 9, 17, 2], [25, 10, 17, 2, "useClearResetErrorBoundary"], [25, 36, 17, 2], [25, 39, 17, 2, "useClearResetErrorBoundary"], [25, 65, 17, 2], [26, 2, 18, 0], [26, 6, 18, 4, "getHasError"], [26, 17, 18, 15], [26, 20, 18, 18, "_ref"], [26, 24, 18, 18], [26, 28, 24, 6], [27, 4, 24, 6], [27, 8, 19, 2, "result"], [27, 14, 19, 8], [27, 17, 19, 8, "_ref"], [27, 21, 19, 8], [27, 22, 19, 2, "result"], [27, 28, 19, 8], [28, 6, 20, 2, "errorResetBoundary"], [28, 24, 20, 20], [28, 27, 20, 20, "_ref"], [28, 31, 20, 20], [28, 32, 20, 2, "errorResetBoundary"], [28, 50, 20, 20], [29, 6, 21, 2, "throwOnError"], [29, 18, 21, 14], [29, 21, 21, 14, "_ref"], [29, 25, 21, 14], [29, 26, 21, 2, "throwOnError"], [29, 38, 21, 14], [30, 6, 22, 2, "query"], [30, 11, 22, 7], [30, 14, 22, 7, "_ref"], [30, 18, 22, 7], [30, 19, 22, 2, "query"], [30, 24, 22, 7], [31, 6, 23, 2, "suspense"], [31, 14, 23, 10], [31, 17, 23, 10, "_ref"], [31, 21, 23, 10], [31, 22, 23, 2, "suspense"], [31, 30, 23, 10], [32, 4, 25, 2], [32, 11, 25, 9, "result"], [32, 17, 25, 15], [32, 18, 25, 16, "isError"], [32, 25, 25, 23], [32, 29, 25, 27], [32, 30, 25, 28, "errorResetBoundary"], [32, 48, 25, 46], [32, 49, 25, 47, "isReset"], [32, 56, 25, 54], [32, 57, 25, 55], [32, 58, 25, 56], [32, 62, 25, 60], [32, 63, 25, 61, "result"], [32, 69, 25, 67], [32, 70, 25, 68, "isFetching"], [32, 80, 25, 78], [32, 84, 25, 82, "query"], [32, 89, 25, 87], [32, 94, 25, 92, "suspense"], [32, 102, 25, 100], [32, 106, 25, 104, "result"], [32, 112, 25, 110], [32, 113, 25, 111, "data"], [32, 117, 25, 115], [32, 122, 25, 120], [32, 127, 25, 125], [32, 128, 25, 126], [32, 132, 25, 130], [32, 136, 25, 130, "shouldThrowError"], [32, 163, 25, 146], [32, 165, 25, 147, "throwOnError"], [32, 177, 25, 159], [32, 179, 25, 161], [32, 180, 25, 162, "result"], [32, 186, 25, 168], [32, 187, 25, 169, "error"], [32, 192, 25, 174], [32, 194, 25, 176, "query"], [32, 199, 25, 181], [32, 200, 25, 182], [32, 201, 25, 183], [32, 202, 25, 184], [33, 2, 26, 0], [33, 3, 26, 1], [34, 2, 26, 2, "exports"], [34, 9, 26, 2], [34, 10, 26, 2, "getHasError"], [34, 21, 26, 2], [34, 24, 26, 2, "getHasError"], [34, 35, 26, 2], [35, 0, 26, 2], [35, 3]], "functionMap": {"names": ["<global>", "ensurePreventErrorBoundaryRetry", "useClearResetErrorBoundary", "React.useEffect$argument_0", "getHasError"], "mappings": "AAA;sCCK;CDM;iCEC;kBCC;GDE;CFC;kBIC;CJQ"}}, "type": "js/module"}]}