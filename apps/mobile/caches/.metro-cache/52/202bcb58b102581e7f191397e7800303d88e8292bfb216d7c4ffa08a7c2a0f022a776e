{"dependencies": [{"name": "../../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 76, "index": 76}}], "key": "kfBwr9wX8MpuKuAI5utiPg2AKqw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.parseTransformProp = parseTransformProp;\n  exports.stringifyTransformProps = stringifyTransformProps;\n  var _extractTransform = require(_dependencyMap[0], \"../../lib/extract/extractTransform\");\n  function parseTransformProp(transform, props) {\n    const transformArray = [];\n    props && transformArray.push(...stringifyTransformProps(props));\n    if (Array.isArray(transform)) {\n      if (typeof transform[0] === 'number') {\n        transformArray.push(`matrix(${transform.join(' ')})`);\n      } else {\n        const stringifiedProps = (0, _extractTransform.transformsArrayToProps)(\n        // @ts-expect-error FIXME\n        transform);\n        transformArray.push(...stringifyTransformProps(stringifiedProps));\n      }\n    } else if (typeof transform === 'string') {\n      transformArray.push(transform);\n    }\n    return transformArray.length ? transformArray.join(' ') : undefined;\n  }\n  function stringifyTransformProps(transformProps) {\n    const transformArray = [];\n    if (transformProps.translate != null) {\n      transformArray.push(`translate(${transformProps.translate})`);\n    }\n    if (transformProps.translateX != null || transformProps.translateY != null) {\n      transformArray.push(`translate(${transformProps.translateX || 0}, ${transformProps.translateY || 0})`);\n    }\n    if (transformProps.scale != null) {\n      transformArray.push(`scale(${transformProps.scale})`);\n    }\n    if (transformProps.scaleX != null || transformProps.scaleY != null) {\n      transformArray.push(`scale(${transformProps.scaleX || 1}, ${transformProps.scaleY || 1})`);\n    }\n    // rotation maps to rotate, not to collide with the text rotate attribute (which acts per glyph rather than block)\n    if (transformProps.rotation != null) {\n      transformArray.push(`rotate(${transformProps.rotation})`);\n    }\n    if (transformProps.skewX != null) {\n      transformArray.push(`skewX(${transformProps.skewX})`);\n    }\n    if (transformProps.skewY != null) {\n      transformArray.push(`skewY(${transformProps.skewY})`);\n    }\n    return transformArray;\n  }\n});", "lineCount": 51, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_extractTransform"], [7, 23, 1, 0], [7, 26, 1, 0, "require"], [7, 33, 1, 0], [7, 34, 1, 0, "_dependencyMap"], [7, 48, 1, 0], [8, 2, 2, 7], [8, 11, 2, 16, "parseTransformProp"], [8, 29, 2, 34, "parseTransformProp"], [8, 30, 2, 35, "transform"], [8, 39, 2, 44], [8, 41, 2, 46, "props"], [8, 46, 2, 51], [8, 48, 2, 53], [9, 4, 3, 2], [9, 10, 3, 8, "transformArray"], [9, 24, 3, 22], [9, 27, 3, 25], [9, 29, 3, 27], [10, 4, 4, 2, "props"], [10, 9, 4, 7], [10, 13, 4, 11, "transformArray"], [10, 27, 4, 25], [10, 28, 4, 26, "push"], [10, 32, 4, 30], [10, 33, 4, 31], [10, 36, 4, 34, "stringifyTransformProps"], [10, 59, 4, 57], [10, 60, 4, 58, "props"], [10, 65, 4, 63], [10, 66, 4, 64], [10, 67, 4, 65], [11, 4, 5, 2], [11, 8, 5, 6, "Array"], [11, 13, 5, 11], [11, 14, 5, 12, "isArray"], [11, 21, 5, 19], [11, 22, 5, 20, "transform"], [11, 31, 5, 29], [11, 32, 5, 30], [11, 34, 5, 32], [12, 6, 6, 4], [12, 10, 6, 8], [12, 17, 6, 15, "transform"], [12, 26, 6, 24], [12, 27, 6, 25], [12, 28, 6, 26], [12, 29, 6, 27], [12, 34, 6, 32], [12, 42, 6, 40], [12, 44, 6, 42], [13, 8, 7, 6, "transformArray"], [13, 22, 7, 20], [13, 23, 7, 21, "push"], [13, 27, 7, 25], [13, 28, 7, 26], [13, 38, 7, 36, "transform"], [13, 47, 7, 45], [13, 48, 7, 46, "join"], [13, 52, 7, 50], [13, 53, 7, 51], [13, 56, 7, 54], [13, 57, 7, 55], [13, 60, 7, 58], [13, 61, 7, 59], [14, 6, 8, 4], [14, 7, 8, 5], [14, 13, 8, 11], [15, 8, 9, 6], [15, 14, 9, 12, "stringifiedProps"], [15, 30, 9, 28], [15, 33, 9, 31], [15, 37, 9, 31, "transformsArrayToProps"], [15, 77, 9, 53], [16, 8, 10, 6], [17, 8, 11, 6, "transform"], [17, 17, 11, 15], [17, 18, 11, 16], [18, 8, 12, 6, "transformArray"], [18, 22, 12, 20], [18, 23, 12, 21, "push"], [18, 27, 12, 25], [18, 28, 12, 26], [18, 31, 12, 29, "stringifyTransformProps"], [18, 54, 12, 52], [18, 55, 12, 53, "stringifiedProps"], [18, 71, 12, 69], [18, 72, 12, 70], [18, 73, 12, 71], [19, 6, 13, 4], [20, 4, 14, 2], [20, 5, 14, 3], [20, 11, 14, 9], [20, 15, 14, 13], [20, 22, 14, 20, "transform"], [20, 31, 14, 29], [20, 36, 14, 34], [20, 44, 14, 42], [20, 46, 14, 44], [21, 6, 15, 4, "transformArray"], [21, 20, 15, 18], [21, 21, 15, 19, "push"], [21, 25, 15, 23], [21, 26, 15, 24, "transform"], [21, 35, 15, 33], [21, 36, 15, 34], [22, 4, 16, 2], [23, 4, 17, 2], [23, 11, 17, 9, "transformArray"], [23, 25, 17, 23], [23, 26, 17, 24, "length"], [23, 32, 17, 30], [23, 35, 17, 33, "transformArray"], [23, 49, 17, 47], [23, 50, 17, 48, "join"], [23, 54, 17, 52], [23, 55, 17, 53], [23, 58, 17, 56], [23, 59, 17, 57], [23, 62, 17, 60, "undefined"], [23, 71, 17, 69], [24, 2, 18, 0], [25, 2, 19, 7], [25, 11, 19, 16, "stringifyTransformProps"], [25, 34, 19, 39, "stringifyTransformProps"], [25, 35, 19, 40, "transformProps"], [25, 49, 19, 54], [25, 51, 19, 56], [26, 4, 20, 2], [26, 10, 20, 8, "transformArray"], [26, 24, 20, 22], [26, 27, 20, 25], [26, 29, 20, 27], [27, 4, 21, 2], [27, 8, 21, 6, "transformProps"], [27, 22, 21, 20], [27, 23, 21, 21, "translate"], [27, 32, 21, 30], [27, 36, 21, 34], [27, 40, 21, 38], [27, 42, 21, 40], [28, 6, 22, 4, "transformArray"], [28, 20, 22, 18], [28, 21, 22, 19, "push"], [28, 25, 22, 23], [28, 26, 22, 24], [28, 39, 22, 37, "transformProps"], [28, 53, 22, 51], [28, 54, 22, 52, "translate"], [28, 63, 22, 61], [28, 66, 22, 64], [28, 67, 22, 65], [29, 4, 23, 2], [30, 4, 24, 2], [30, 8, 24, 6, "transformProps"], [30, 22, 24, 20], [30, 23, 24, 21, "translateX"], [30, 33, 24, 31], [30, 37, 24, 35], [30, 41, 24, 39], [30, 45, 24, 43, "transformProps"], [30, 59, 24, 57], [30, 60, 24, 58, "translateY"], [30, 70, 24, 68], [30, 74, 24, 72], [30, 78, 24, 76], [30, 80, 24, 78], [31, 6, 25, 4, "transformArray"], [31, 20, 25, 18], [31, 21, 25, 19, "push"], [31, 25, 25, 23], [31, 26, 25, 24], [31, 39, 25, 37, "transformProps"], [31, 53, 25, 51], [31, 54, 25, 52, "translateX"], [31, 64, 25, 62], [31, 68, 25, 66], [31, 69, 25, 67], [31, 74, 25, 72, "transformProps"], [31, 88, 25, 86], [31, 89, 25, 87, "translateY"], [31, 99, 25, 97], [31, 103, 25, 101], [31, 104, 25, 102], [31, 107, 25, 105], [31, 108, 25, 106], [32, 4, 26, 2], [33, 4, 27, 2], [33, 8, 27, 6, "transformProps"], [33, 22, 27, 20], [33, 23, 27, 21, "scale"], [33, 28, 27, 26], [33, 32, 27, 30], [33, 36, 27, 34], [33, 38, 27, 36], [34, 6, 28, 4, "transformArray"], [34, 20, 28, 18], [34, 21, 28, 19, "push"], [34, 25, 28, 23], [34, 26, 28, 24], [34, 35, 28, 33, "transformProps"], [34, 49, 28, 47], [34, 50, 28, 48, "scale"], [34, 55, 28, 53], [34, 58, 28, 56], [34, 59, 28, 57], [35, 4, 29, 2], [36, 4, 30, 2], [36, 8, 30, 6, "transformProps"], [36, 22, 30, 20], [36, 23, 30, 21, "scaleX"], [36, 29, 30, 27], [36, 33, 30, 31], [36, 37, 30, 35], [36, 41, 30, 39, "transformProps"], [36, 55, 30, 53], [36, 56, 30, 54, "scaleY"], [36, 62, 30, 60], [36, 66, 30, 64], [36, 70, 30, 68], [36, 72, 30, 70], [37, 6, 31, 4, "transformArray"], [37, 20, 31, 18], [37, 21, 31, 19, "push"], [37, 25, 31, 23], [37, 26, 31, 24], [37, 35, 31, 33, "transformProps"], [37, 49, 31, 47], [37, 50, 31, 48, "scaleX"], [37, 56, 31, 54], [37, 60, 31, 58], [37, 61, 31, 59], [37, 66, 31, 64, "transformProps"], [37, 80, 31, 78], [37, 81, 31, 79, "scaleY"], [37, 87, 31, 85], [37, 91, 31, 89], [37, 92, 31, 90], [37, 95, 31, 93], [37, 96, 31, 94], [38, 4, 32, 2], [39, 4, 33, 2], [40, 4, 34, 2], [40, 8, 34, 6, "transformProps"], [40, 22, 34, 20], [40, 23, 34, 21, "rotation"], [40, 31, 34, 29], [40, 35, 34, 33], [40, 39, 34, 37], [40, 41, 34, 39], [41, 6, 35, 4, "transformArray"], [41, 20, 35, 18], [41, 21, 35, 19, "push"], [41, 25, 35, 23], [41, 26, 35, 24], [41, 36, 35, 34, "transformProps"], [41, 50, 35, 48], [41, 51, 35, 49, "rotation"], [41, 59, 35, 57], [41, 62, 35, 60], [41, 63, 35, 61], [42, 4, 36, 2], [43, 4, 37, 2], [43, 8, 37, 6, "transformProps"], [43, 22, 37, 20], [43, 23, 37, 21, "skewX"], [43, 28, 37, 26], [43, 32, 37, 30], [43, 36, 37, 34], [43, 38, 37, 36], [44, 6, 38, 4, "transformArray"], [44, 20, 38, 18], [44, 21, 38, 19, "push"], [44, 25, 38, 23], [44, 26, 38, 24], [44, 35, 38, 33, "transformProps"], [44, 49, 38, 47], [44, 50, 38, 48, "skewX"], [44, 55, 38, 53], [44, 58, 38, 56], [44, 59, 38, 57], [45, 4, 39, 2], [46, 4, 40, 2], [46, 8, 40, 6, "transformProps"], [46, 22, 40, 20], [46, 23, 40, 21, "skewY"], [46, 28, 40, 26], [46, 32, 40, 30], [46, 36, 40, 34], [46, 38, 40, 36], [47, 6, 41, 4, "transformArray"], [47, 20, 41, 18], [47, 21, 41, 19, "push"], [47, 25, 41, 23], [47, 26, 41, 24], [47, 35, 41, 33, "transformProps"], [47, 49, 41, 47], [47, 50, 41, 48, "skewY"], [47, 55, 41, 53], [47, 58, 41, 56], [47, 59, 41, 57], [48, 4, 42, 2], [49, 4, 43, 2], [49, 11, 43, 9, "transformArray"], [49, 25, 43, 23], [50, 2, 44, 0], [51, 0, 44, 1], [51, 3]], "functionMap": {"names": ["<global>", "parseTransformProp", "stringifyTransformProps"], "mappings": "AAA;OCC;CDgB;OEC;CFyB"}}, "type": "js/module"}]}