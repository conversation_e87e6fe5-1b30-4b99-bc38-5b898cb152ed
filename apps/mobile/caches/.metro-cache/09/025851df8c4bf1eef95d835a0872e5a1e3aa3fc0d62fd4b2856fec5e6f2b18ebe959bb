{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "../BatchedBridge/NativeModules", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 63}}], "key": "vsf1Ls4L++VD3yF8UTrETFprAwI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.get = get;\n  exports.getEnforcing = getEnforcing;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"invariant\"));\n  var NativeModules = require(_dependencyMap[2], \"../BatchedBridge/NativeModules\").default;\n  var turboModuleProxy = global.__turboModuleProxy;\n  function requireModule(name) {\n    if (turboModuleProxy != null) {\n      var module = turboModuleProxy(name);\n      if (module != null) {\n        return module;\n      }\n    }\n    if (global.RN$Bridgeless !== true || global.RN$TurboInterop === true || global.RN$UnifiedNativeModuleProxy === true) {\n      var legacyModule = NativeModules[name];\n      if (legacyModule != null) {\n        return legacyModule;\n      }\n    }\n    return null;\n  }\n  function get(name) {\n    return requireModule(name);\n  }\n  function getEnforcing(name) {\n    var module = requireModule(name);\n    (0, _invariant.default)(module != null, `TurboModuleRegistry.getEnforcing(...): '${name}' could not be found. ` + 'Verify that a module by this name is registered in the native binary.');\n    return module;\n  }\n});", "lineCount": 34, "map": [[8, 2, 13, 0], [8, 6, 13, 0, "_invariant"], [8, 16, 13, 0], [8, 19, 13, 0, "_interopRequireDefault"], [8, 41, 13, 0], [8, 42, 13, 0, "require"], [8, 49, 13, 0], [8, 50, 13, 0, "_dependencyMap"], [8, 64, 13, 0], [9, 2, 15, 0], [9, 6, 15, 6, "NativeModules"], [9, 19, 15, 19], [9, 22, 15, 22, "require"], [9, 29, 15, 29], [9, 30, 15, 29, "_dependencyMap"], [9, 44, 15, 29], [9, 81, 15, 62], [9, 82, 15, 63], [9, 83, 15, 64, "default"], [9, 90, 15, 71], [10, 2, 17, 0], [10, 6, 17, 6, "turboModuleProxy"], [10, 22, 17, 22], [10, 25, 17, 25, "global"], [10, 31, 17, 31], [10, 32, 17, 32, "__turboModuleProxy"], [10, 50, 17, 50], [11, 2, 19, 0], [11, 11, 19, 9, "requireModule"], [11, 24, 19, 22, "requireModule"], [11, 25, 19, 39, "name"], [11, 29, 19, 51], [11, 31, 19, 57], [12, 4, 20, 2], [12, 8, 20, 6, "turboModuleProxy"], [12, 24, 20, 22], [12, 28, 20, 26], [12, 32, 20, 30], [12, 34, 20, 32], [13, 6, 21, 4], [13, 10, 21, 10, "module"], [13, 16, 21, 20], [13, 19, 21, 23, "turboModuleProxy"], [13, 35, 21, 39], [13, 36, 21, 40, "name"], [13, 40, 21, 44], [13, 41, 21, 45], [14, 6, 22, 4], [14, 10, 22, 8, "module"], [14, 16, 22, 14], [14, 20, 22, 18], [14, 24, 22, 22], [14, 26, 22, 24], [15, 8, 23, 6], [15, 15, 23, 13, "module"], [15, 21, 23, 19], [16, 6, 24, 4], [17, 4, 25, 2], [18, 4, 27, 2], [18, 8, 28, 4, "global"], [18, 14, 28, 10], [18, 15, 28, 11, "RN$Bridgeless"], [18, 28, 28, 24], [18, 33, 28, 29], [18, 37, 28, 33], [18, 41, 29, 4, "global"], [18, 47, 29, 10], [18, 48, 29, 11, "RN$TurboInterop"], [18, 63, 29, 26], [18, 68, 29, 31], [18, 72, 29, 35], [18, 76, 30, 4, "global"], [18, 82, 30, 10], [18, 83, 30, 11, "RN$UnifiedNativeModuleProxy"], [18, 110, 30, 38], [18, 115, 30, 43], [18, 119, 30, 47], [18, 121, 31, 4], [19, 6, 32, 4], [19, 10, 32, 10, "legacyModule"], [19, 22, 32, 26], [19, 25, 32, 29, "NativeModules"], [19, 38, 32, 42], [19, 39, 32, 43, "name"], [19, 43, 32, 47], [19, 44, 32, 48], [20, 6, 33, 4], [20, 10, 33, 8, "legacyModule"], [20, 22, 33, 20], [20, 26, 33, 24], [20, 30, 33, 28], [20, 32, 33, 30], [21, 8, 34, 6], [21, 15, 34, 13, "legacyModule"], [21, 27, 34, 25], [22, 6, 35, 4], [23, 4, 36, 2], [24, 4, 38, 2], [24, 11, 38, 9], [24, 15, 38, 13], [25, 2, 39, 0], [26, 2, 41, 7], [26, 11, 41, 16, "get"], [26, 14, 41, 19, "get"], [26, 15, 41, 36, "name"], [26, 19, 41, 48], [26, 21, 41, 54], [27, 4, 42, 2], [27, 11, 42, 9, "requireModule"], [27, 24, 42, 22], [27, 25, 42, 26, "name"], [27, 29, 42, 30], [27, 30, 42, 31], [28, 2, 43, 0], [29, 2, 45, 7], [29, 11, 45, 16, "getEnforcing"], [29, 23, 45, 28, "getEnforcing"], [29, 24, 45, 45, "name"], [29, 28, 45, 57], [29, 30, 45, 62], [30, 4, 46, 2], [30, 8, 46, 8, "module"], [30, 14, 46, 14], [30, 17, 46, 17, "requireModule"], [30, 30, 46, 30], [30, 31, 46, 34, "name"], [30, 35, 46, 38], [30, 36, 46, 39], [31, 4, 47, 2], [31, 8, 47, 2, "invariant"], [31, 26, 47, 11], [31, 28, 48, 4, "module"], [31, 34, 48, 10], [31, 38, 48, 14], [31, 42, 48, 18], [31, 44, 49, 4], [31, 87, 49, 47, "name"], [31, 91, 49, 51], [31, 115, 49, 75], [31, 118, 50, 6], [31, 189, 51, 2], [31, 190, 51, 3], [32, 4, 52, 2], [32, 11, 52, 9, "module"], [32, 17, 52, 15], [33, 2, 53, 0], [34, 0, 53, 1], [34, 3]], "functionMap": {"names": ["<global>", "requireModule", "get", "getEnforcing"], "mappings": "AAA;ACkB;CDoB;OEE;CFE;OGE"}}, "type": "js/module"}]}