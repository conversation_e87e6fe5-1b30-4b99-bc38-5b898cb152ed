{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 0, "index": 400}, "end": {"line": 14, "column": 3, "index": 503}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  // Internal export, not part of stable library API.\n\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNSFullWindowOverlay';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSFullWindowOverlay\",\n    validAttributes: {\n      accessibilityContainerViewIsModal: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "__INTERNAL_VIEW_CONFIG"], [8, 50, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_codegenNativeComponent"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 7, 0], [12, 2, 12, 0], [12, 6, 12, 0, "NativeComponentRegistry"], [12, 29, 14, 3], [12, 32, 12, 0, "require"], [12, 39, 14, 3], [12, 40, 14, 3, "_dependencyMap"], [12, 54, 14, 3], [12, 123, 14, 2], [12, 124, 14, 3], [13, 2, 12, 0], [13, 6, 12, 0, "nativeComponentName"], [13, 25, 14, 3], [13, 28, 12, 0], [13, 50, 14, 3], [14, 2, 12, 0], [14, 6, 12, 0, "__INTERNAL_VIEW_CONFIG"], [14, 28, 14, 3], [14, 31, 14, 3, "exports"], [14, 38, 14, 3], [14, 39, 14, 3, "__INTERNAL_VIEW_CONFIG"], [14, 61, 14, 3], [14, 64, 12, 0], [15, 4, 12, 0, "uiViewClassName"], [15, 19, 14, 3], [15, 21, 12, 0], [15, 43, 14, 3], [16, 4, 12, 0, "validAttributes"], [16, 19, 14, 3], [16, 21, 12, 0], [17, 6, 12, 0, "accessibilityContainerViewIsModal"], [17, 39, 14, 3], [17, 41, 12, 0], [18, 4, 14, 2], [19, 2, 14, 2], [19, 3, 14, 3], [20, 2, 14, 3], [20, 6, 14, 3, "_default"], [20, 14, 14, 3], [20, 17, 14, 3, "exports"], [20, 24, 14, 3], [20, 25, 14, 3, "default"], [20, 32, 14, 3], [20, 35, 12, 0, "NativeComponentRegistry"], [20, 58, 14, 3], [20, 59, 12, 0, "get"], [20, 62, 14, 3], [20, 63, 12, 0, "nativeComponentName"], [20, 82, 14, 3], [20, 84, 12, 0], [20, 90, 12, 0, "__INTERNAL_VIEW_CONFIG"], [20, 112, 14, 2], [20, 113, 14, 3], [21, 0, 14, 3], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}