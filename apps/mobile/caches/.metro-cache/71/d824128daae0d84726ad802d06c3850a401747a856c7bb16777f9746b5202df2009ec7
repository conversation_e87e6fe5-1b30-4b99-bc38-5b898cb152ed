{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  module.exports = function (obj, predicate) {\n    var ret = {};\n    var keys = Object.keys(obj);\n    var isArr = Array.isArray(predicate);\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      var val = obj[key];\n      if (isArr ? predicate.indexOf(key) !== -1 : predicate(key, val, obj)) {\n        ret[key] = val;\n      }\n    }\n    return ret;\n  };\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "module"], [4, 8, 2, 6], [4, 9, 2, 7, "exports"], [4, 16, 2, 14], [4, 19, 2, 17], [4, 29, 2, 27, "obj"], [4, 32, 2, 30], [4, 34, 2, 32, "predicate"], [4, 43, 2, 41], [4, 45, 2, 43], [5, 4, 3, 1], [5, 8, 3, 5, "ret"], [5, 11, 3, 8], [5, 14, 3, 11], [5, 15, 3, 12], [5, 16, 3, 13], [6, 4, 4, 1], [6, 8, 4, 5, "keys"], [6, 12, 4, 9], [6, 15, 4, 12, "Object"], [6, 21, 4, 18], [6, 22, 4, 19, "keys"], [6, 26, 4, 23], [6, 27, 4, 24, "obj"], [6, 30, 4, 27], [6, 31, 4, 28], [7, 4, 5, 1], [7, 8, 5, 5, "isArr"], [7, 13, 5, 10], [7, 16, 5, 13, "Array"], [7, 21, 5, 18], [7, 22, 5, 19, "isArray"], [7, 29, 5, 26], [7, 30, 5, 27, "predicate"], [7, 39, 5, 36], [7, 40, 5, 37], [8, 4, 7, 1], [8, 9, 7, 6], [8, 13, 7, 10, "i"], [8, 14, 7, 11], [8, 17, 7, 14], [8, 18, 7, 15], [8, 20, 7, 17, "i"], [8, 21, 7, 18], [8, 24, 7, 21, "keys"], [8, 28, 7, 25], [8, 29, 7, 26, "length"], [8, 35, 7, 32], [8, 37, 7, 34, "i"], [8, 38, 7, 35], [8, 40, 7, 37], [8, 42, 7, 39], [9, 6, 8, 2], [9, 10, 8, 6, "key"], [9, 13, 8, 9], [9, 16, 8, 12, "keys"], [9, 20, 8, 16], [9, 21, 8, 17, "i"], [9, 22, 8, 18], [9, 23, 8, 19], [10, 6, 9, 2], [10, 10, 9, 6, "val"], [10, 13, 9, 9], [10, 16, 9, 12, "obj"], [10, 19, 9, 15], [10, 20, 9, 16, "key"], [10, 23, 9, 19], [10, 24, 9, 20], [11, 6, 11, 2], [11, 10, 11, 6, "isArr"], [11, 15, 11, 11], [11, 18, 11, 14, "predicate"], [11, 27, 11, 23], [11, 28, 11, 24, "indexOf"], [11, 35, 11, 31], [11, 36, 11, 32, "key"], [11, 39, 11, 35], [11, 40, 11, 36], [11, 45, 11, 41], [11, 46, 11, 42], [11, 47, 11, 43], [11, 50, 11, 46, "predicate"], [11, 59, 11, 55], [11, 60, 11, 56, "key"], [11, 63, 11, 59], [11, 65, 11, 61, "val"], [11, 68, 11, 64], [11, 70, 11, 66, "obj"], [11, 73, 11, 69], [11, 74, 11, 70], [11, 76, 11, 72], [12, 8, 12, 3, "ret"], [12, 11, 12, 6], [12, 12, 12, 7, "key"], [12, 15, 12, 10], [12, 16, 12, 11], [12, 19, 12, 14, "val"], [12, 22, 12, 17], [13, 6, 13, 2], [14, 4, 14, 1], [15, 4, 16, 1], [15, 11, 16, 8, "ret"], [15, 14, 16, 11], [16, 2, 17, 0], [16, 3, 17, 1], [17, 0, 17, 2], [17, 3]], "functionMap": {"names": ["<global>", "module.exports"], "mappings": "AAA;iBCC;CDe"}}, "type": "js/module"}]}