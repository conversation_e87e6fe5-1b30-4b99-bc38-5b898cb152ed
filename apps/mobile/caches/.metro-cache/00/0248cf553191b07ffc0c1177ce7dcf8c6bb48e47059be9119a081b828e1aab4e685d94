{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "./notifyManager.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 26}, "end": {"line": 2, "column": 51, "index": 77}}], "key": "sE8Jk2BSATSKXEwal1fu77sfV3E=", "exportNames": ["*"]}}, {"name": "./queryObserver.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 78}, "end": {"line": 3, "column": 51, "index": 129}}], "key": "T5bdga4o8lhxjwmnTyuBuLPduVg=", "exportNames": ["*"]}}, {"name": "./subscribable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 130}, "end": {"line": 4, "column": 49, "index": 179}}], "key": "f0fxTGZggQRtb//cHMvH9AHIWOw=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 180}, "end": {"line": 5, "column": 46, "index": 226}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.QueriesObserver = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _notifyManager = require(_dependencyMap[8], \"./notifyManager.js\");\n  var _queryObserver = require(_dependencyMap[9], \"./queryObserver.js\");\n  var _subscribable = require(_dependencyMap[10], \"./subscribable.js\");\n  var _utils = require(_dependencyMap[11], \"./utils.js\");\n  var _client, _result, _queries, _options, _observers, _combinedResult, _lastCombine, _lastResult, _observerMatches, _trackResult, _combineResult, _findMatchingObservers, _onUpdate, _notify; // src/queriesObserver.ts\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function difference(array1, array2) {\n    var excludeSet = new Set(array2);\n    return array1.filter(x => !excludeSet.has(x));\n  }\n  function replaceAt(array, index, value) {\n    var copy = array.slice(0);\n    copy[index] = value;\n    return copy;\n  }\n  var QueriesObserver = exports.QueriesObserver = (_client = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"client\"), _result = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"result\"), _queries = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"queries\"), _options = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"options\"), _observers = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"observers\"), _combinedResult = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"combinedResult\"), _lastCombine = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"lastCombine\"), _lastResult = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"lastResult\"), _observerMatches = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"observerMatches\"), _trackResult = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"trackResult\"), _combineResult = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"combineResult\"), _findMatchingObservers = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"findMatchingObservers\"), _onUpdate = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onUpdate\"), _notify = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"notify\"), /*#__PURE__*/function (_Subscribable) {\n    function QueriesObserver(client, _queries2, _options2) {\n      var _this;\n      (0, _classCallCheck2.default)(this, QueriesObserver);\n      _this = _callSuper(this, QueriesObserver);\n      Object.defineProperty(_this, _notify, {\n        value: _notify2\n      });\n      Object.defineProperty(_this, _onUpdate, {\n        value: _onUpdate2\n      });\n      Object.defineProperty(_this, _findMatchingObservers, {\n        value: _findMatchingObservers2\n      });\n      Object.defineProperty(_this, _combineResult, {\n        value: _combineResult2\n      });\n      Object.defineProperty(_this, _trackResult, {\n        value: _trackResult2\n      });\n      Object.defineProperty(_this, _client, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _result, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _queries, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _options, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _observers, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _combinedResult, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _lastCombine, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _lastResult, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(_this, _observerMatches, {\n        writable: true,\n        value: []\n      });\n      (0, _classPrivateFieldLooseBase2.default)(_this, _client)[_client] = client;\n      (0, _classPrivateFieldLooseBase2.default)(_this, _options)[_options] = _options2;\n      (0, _classPrivateFieldLooseBase2.default)(_this, _queries)[_queries] = [];\n      (0, _classPrivateFieldLooseBase2.default)(_this, _observers)[_observers] = [];\n      (0, _classPrivateFieldLooseBase2.default)(_this, _result)[_result] = [];\n      _this.setQueries(_queries2);\n      return _this;\n    }\n    (0, _inherits2.default)(QueriesObserver, _Subscribable);\n    return (0, _createClass2.default)(QueriesObserver, [{\n      key: \"onSubscribe\",\n      value: function onSubscribe() {\n        if (this.listeners.size === 1) {\n          (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers].forEach(observer => {\n            observer.subscribe(result => {\n              (0, _classPrivateFieldLooseBase2.default)(this, _onUpdate)[_onUpdate](observer, result);\n            });\n          });\n        }\n      }\n    }, {\n      key: \"onUnsubscribe\",\n      value: function onUnsubscribe() {\n        if (!this.listeners.size) {\n          this.destroy();\n        }\n      }\n    }, {\n      key: \"destroy\",\n      value: function destroy() {\n        this.listeners = /* @__PURE__ */new Set();\n        (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers].forEach(observer => {\n          observer.destroy();\n        });\n      }\n    }, {\n      key: \"setQueries\",\n      value: function setQueries(queries, options) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _queries)[_queries] = queries;\n        (0, _classPrivateFieldLooseBase2.default)(this, _options)[_options] = options;\n        if (process.env.NODE_ENV !== \"production\") {\n          var queryHashes = queries.map(query => (0, _classPrivateFieldLooseBase2.default)(this, _client)[_client].defaultQueryOptions(query).queryHash);\n          if (new Set(queryHashes).size !== queryHashes.length) {\n            console.warn(\"[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.\");\n          }\n        }\n        _notifyManager.notifyManager.batch(() => {\n          var prevObservers = (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers];\n          var newObserverMatches = (0, _classPrivateFieldLooseBase2.default)(this, _findMatchingObservers)[_findMatchingObservers]((0, _classPrivateFieldLooseBase2.default)(this, _queries)[_queries]);\n          (0, _classPrivateFieldLooseBase2.default)(this, _observerMatches)[_observerMatches] = newObserverMatches;\n          newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions));\n          var newObservers = newObserverMatches.map(match => match.observer);\n          var newResult = newObservers.map(observer => observer.getCurrentResult());\n          var hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n          if (prevObservers.length === newObservers.length && !hasIndexChange) {\n            return;\n          }\n          (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers] = newObservers;\n          (0, _classPrivateFieldLooseBase2.default)(this, _result)[_result] = newResult;\n          if (!this.hasListeners()) {\n            return;\n          }\n          difference(prevObservers, newObservers).forEach(observer => {\n            observer.destroy();\n          });\n          difference(newObservers, prevObservers).forEach(observer => {\n            observer.subscribe(result => {\n              (0, _classPrivateFieldLooseBase2.default)(this, _onUpdate)[_onUpdate](observer, result);\n            });\n          });\n          (0, _classPrivateFieldLooseBase2.default)(this, _notify)[_notify]();\n        });\n      }\n    }, {\n      key: \"getCurrentResult\",\n      value: function getCurrentResult() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _result)[_result];\n      }\n    }, {\n      key: \"getQueries\",\n      value: function getQueries() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers].map(observer => observer.getCurrentQuery());\n      }\n    }, {\n      key: \"getObservers\",\n      value: function getObservers() {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers];\n      }\n    }, {\n      key: \"getOptimisticResult\",\n      value: function getOptimisticResult(queries, combine) {\n        var matches = (0, _classPrivateFieldLooseBase2.default)(this, _findMatchingObservers)[_findMatchingObservers](queries);\n        var result = matches.map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n        return [result, r => {\n          return (0, _classPrivateFieldLooseBase2.default)(this, _combineResult)[_combineResult](r ?? result, combine);\n        }, () => {\n          return (0, _classPrivateFieldLooseBase2.default)(this, _trackResult)[_trackResult](result, matches);\n        }];\n      }\n    }]);\n  }(_subscribable.Subscribable));\n  function _trackResult2(result, matches) {\n    return matches.map((match, index) => {\n      var observerResult = result[index];\n      return !match.defaultedQueryOptions.notifyOnChangeProps ? match.observer.trackResult(observerResult, accessedProp => {\n        matches.forEach(m => {\n          m.observer.trackProp(accessedProp);\n        });\n      }) : observerResult;\n    });\n  }\n  function _combineResult2(input, combine) {\n    if (combine) {\n      if (!(0, _classPrivateFieldLooseBase2.default)(this, _combinedResult)[_combinedResult] || (0, _classPrivateFieldLooseBase2.default)(this, _result)[_result] !== (0, _classPrivateFieldLooseBase2.default)(this, _lastResult)[_lastResult] || combine !== (0, _classPrivateFieldLooseBase2.default)(this, _lastCombine)[_lastCombine]) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _lastCombine)[_lastCombine] = combine;\n        (0, _classPrivateFieldLooseBase2.default)(this, _lastResult)[_lastResult] = (0, _classPrivateFieldLooseBase2.default)(this, _result)[_result];\n        (0, _classPrivateFieldLooseBase2.default)(this, _combinedResult)[_combinedResult] = (0, _utils.replaceEqualDeep)((0, _classPrivateFieldLooseBase2.default)(this, _combinedResult)[_combinedResult], combine(input));\n      }\n      return (0, _classPrivateFieldLooseBase2.default)(this, _combinedResult)[_combinedResult];\n    }\n    return input;\n  }\n  function _findMatchingObservers2(queries) {\n    var prevObserversMap = new Map((0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers].map(observer => [observer.options.queryHash, observer]));\n    var observers = [];\n    queries.forEach(options => {\n      var defaultedOptions = (0, _classPrivateFieldLooseBase2.default)(this, _client)[_client].defaultQueryOptions(options);\n      var match = prevObserversMap.get(defaultedOptions.queryHash);\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        });\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new _queryObserver.QueryObserver((0, _classPrivateFieldLooseBase2.default)(this, _client)[_client], defaultedOptions)\n        });\n      }\n    });\n    return observers;\n  }\n  function _onUpdate2(observer, result) {\n    var index = (0, _classPrivateFieldLooseBase2.default)(this, _observers)[_observers].indexOf(observer);\n    if (index !== -1) {\n      (0, _classPrivateFieldLooseBase2.default)(this, _result)[_result] = replaceAt((0, _classPrivateFieldLooseBase2.default)(this, _result)[_result], index, result);\n      (0, _classPrivateFieldLooseBase2.default)(this, _notify)[_notify]();\n    }\n  }\n  function _notify2() {\n    if (this.hasListeners()) {\n      var previousResult = (0, _classPrivateFieldLooseBase2.default)(this, _combinedResult)[_combinedResult];\n      var newTracked = (0, _classPrivateFieldLooseBase2.default)(this, _trackResult)[_trackResult]((0, _classPrivateFieldLooseBase2.default)(this, _result)[_result], (0, _classPrivateFieldLooseBase2.default)(this, _observerMatches)[_observerMatches]);\n      var newResult = (0, _classPrivateFieldLooseBase2.default)(this, _combineResult)[_combineResult](newTracked, (0, _classPrivateFieldLooseBase2.default)(this, _options)[_options]?.combine);\n      if (previousResult !== newResult) {\n        _notifyManager.notifyManager.batch(() => {\n          this.listeners.forEach(listener => {\n            listener((0, _classPrivateFieldLooseBase2.default)(this, _result)[_result]);\n          });\n        });\n      }\n    }\n  }\n});", "lineCount": 249, "map": [[14, 2, 2, 0], [14, 6, 2, 0, "_notifyManager"], [14, 20, 2, 0], [14, 23, 2, 0, "require"], [14, 30, 2, 0], [14, 31, 2, 0, "_dependencyMap"], [14, 45, 2, 0], [15, 2, 3, 0], [15, 6, 3, 0, "_queryObserver"], [15, 20, 3, 0], [15, 23, 3, 0, "require"], [15, 30, 3, 0], [15, 31, 3, 0, "_dependencyMap"], [15, 45, 3, 0], [16, 2, 4, 0], [16, 6, 4, 0, "_subscribable"], [16, 19, 4, 0], [16, 22, 4, 0, "require"], [16, 29, 4, 0], [16, 30, 4, 0, "_dependencyMap"], [16, 44, 4, 0], [17, 2, 5, 0], [17, 6, 5, 0, "_utils"], [17, 12, 5, 0], [17, 15, 5, 0, "require"], [17, 22, 5, 0], [17, 23, 5, 0, "_dependencyMap"], [17, 37, 5, 0], [18, 2, 5, 46], [18, 6, 5, 46, "_client"], [18, 13, 5, 46], [18, 15, 5, 46, "_result"], [18, 22, 5, 46], [18, 24, 5, 46, "_queries"], [18, 32, 5, 46], [18, 34, 5, 46, "_options"], [18, 42, 5, 46], [18, 44, 5, 46, "_observers"], [18, 54, 5, 46], [18, 56, 5, 46, "_combinedResult"], [18, 71, 5, 46], [18, 73, 5, 46, "_last<PERSON><PERSON><PERSON>"], [18, 85, 5, 46], [18, 87, 5, 46, "_lastResult"], [18, 98, 5, 46], [18, 100, 5, 46, "_observerMatches"], [18, 116, 5, 46], [18, 118, 5, 46, "_trackResult"], [18, 130, 5, 46], [18, 132, 5, 46, "_combineResult"], [18, 146, 5, 46], [18, 148, 5, 46, "_findMatchingObservers"], [18, 170, 5, 46], [18, 172, 5, 46, "_onUpdate"], [18, 181, 5, 46], [18, 183, 5, 46, "_notify"], [18, 190, 5, 46], [18, 192, 1, 0], [19, 2, 1, 0], [19, 11, 1, 0, "_callSuper"], [19, 22, 1, 0, "t"], [19, 23, 1, 0], [19, 25, 1, 0, "o"], [19, 26, 1, 0], [19, 28, 1, 0, "e"], [19, 29, 1, 0], [19, 40, 1, 0, "o"], [19, 41, 1, 0], [19, 48, 1, 0, "_getPrototypeOf2"], [19, 64, 1, 0], [19, 65, 1, 0, "default"], [19, 72, 1, 0], [19, 74, 1, 0, "o"], [19, 75, 1, 0], [19, 82, 1, 0, "_possibleConstructorReturn2"], [19, 109, 1, 0], [19, 110, 1, 0, "default"], [19, 117, 1, 0], [19, 119, 1, 0, "t"], [19, 120, 1, 0], [19, 122, 1, 0, "_isNativeReflectConstruct"], [19, 147, 1, 0], [19, 152, 1, 0, "Reflect"], [19, 159, 1, 0], [19, 160, 1, 0, "construct"], [19, 169, 1, 0], [19, 170, 1, 0, "o"], [19, 171, 1, 0], [19, 173, 1, 0, "e"], [19, 174, 1, 0], [19, 186, 1, 0, "_getPrototypeOf2"], [19, 202, 1, 0], [19, 203, 1, 0, "default"], [19, 210, 1, 0], [19, 212, 1, 0, "t"], [19, 213, 1, 0], [19, 215, 1, 0, "constructor"], [19, 226, 1, 0], [19, 230, 1, 0, "o"], [19, 231, 1, 0], [19, 232, 1, 0, "apply"], [19, 237, 1, 0], [19, 238, 1, 0, "t"], [19, 239, 1, 0], [19, 241, 1, 0, "e"], [19, 242, 1, 0], [20, 2, 1, 0], [20, 11, 1, 0, "_isNativeReflectConstruct"], [20, 37, 1, 0], [20, 51, 1, 0, "t"], [20, 52, 1, 0], [20, 56, 1, 0, "Boolean"], [20, 63, 1, 0], [20, 64, 1, 0, "prototype"], [20, 73, 1, 0], [20, 74, 1, 0, "valueOf"], [20, 81, 1, 0], [20, 82, 1, 0, "call"], [20, 86, 1, 0], [20, 87, 1, 0, "Reflect"], [20, 94, 1, 0], [20, 95, 1, 0, "construct"], [20, 104, 1, 0], [20, 105, 1, 0, "Boolean"], [20, 112, 1, 0], [20, 145, 1, 0, "t"], [20, 146, 1, 0], [20, 159, 1, 0, "_isNativeReflectConstruct"], [20, 184, 1, 0], [20, 196, 1, 0, "_isNativeReflectConstruct"], [20, 197, 1, 0], [20, 210, 1, 0, "t"], [20, 211, 1, 0], [21, 2, 6, 0], [21, 11, 6, 9, "difference"], [21, 21, 6, 19, "difference"], [21, 22, 6, 20, "array1"], [21, 28, 6, 26], [21, 30, 6, 28, "array2"], [21, 36, 6, 34], [21, 38, 6, 36], [22, 4, 7, 2], [22, 8, 7, 8, "excludeSet"], [22, 18, 7, 18], [22, 21, 7, 21], [22, 25, 7, 25, "Set"], [22, 28, 7, 28], [22, 29, 7, 29, "array2"], [22, 35, 7, 35], [22, 36, 7, 36], [23, 4, 8, 2], [23, 11, 8, 9, "array1"], [23, 17, 8, 15], [23, 18, 8, 16, "filter"], [23, 24, 8, 22], [23, 25, 8, 24, "x"], [23, 26, 8, 25], [23, 30, 8, 30], [23, 31, 8, 31, "excludeSet"], [23, 41, 8, 41], [23, 42, 8, 42, "has"], [23, 45, 8, 45], [23, 46, 8, 46, "x"], [23, 47, 8, 47], [23, 48, 8, 48], [23, 49, 8, 49], [24, 2, 9, 0], [25, 2, 10, 0], [25, 11, 10, 9, "replaceAt"], [25, 20, 10, 18, "replaceAt"], [25, 21, 10, 19, "array"], [25, 26, 10, 24], [25, 28, 10, 26, "index"], [25, 33, 10, 31], [25, 35, 10, 33, "value"], [25, 40, 10, 38], [25, 42, 10, 40], [26, 4, 11, 2], [26, 8, 11, 8, "copy"], [26, 12, 11, 12], [26, 15, 11, 15, "array"], [26, 20, 11, 20], [26, 21, 11, 21, "slice"], [26, 26, 11, 26], [26, 27, 11, 27], [26, 28, 11, 28], [26, 29, 11, 29], [27, 4, 12, 2, "copy"], [27, 8, 12, 6], [27, 9, 12, 7, "index"], [27, 14, 12, 12], [27, 15, 12, 13], [27, 18, 12, 16, "value"], [27, 23, 12, 21], [28, 4, 13, 2], [28, 11, 13, 9, "copy"], [28, 15, 13, 13], [29, 2, 14, 0], [30, 2, 15, 0], [30, 6, 15, 4, "QueriesObserver"], [30, 21, 15, 19], [30, 24, 15, 19, "exports"], [30, 31, 15, 19], [30, 32, 15, 19, "QueriesObserver"], [30, 47, 15, 19], [30, 51, 15, 19, "_client"], [30, 58, 15, 19], [30, 78, 15, 19, "_classPrivateFieldLooseKey2"], [30, 105, 15, 19], [30, 106, 15, 19, "default"], [30, 113, 15, 19], [30, 126, 15, 19, "_result"], [30, 133, 15, 19], [30, 153, 15, 19, "_classPrivateFieldLooseKey2"], [30, 180, 15, 19], [30, 181, 15, 19, "default"], [30, 188, 15, 19], [30, 201, 15, 19, "_queries"], [30, 209, 15, 19], [30, 229, 15, 19, "_classPrivateFieldLooseKey2"], [30, 256, 15, 19], [30, 257, 15, 19, "default"], [30, 264, 15, 19], [30, 278, 15, 19, "_options"], [30, 286, 15, 19], [30, 306, 15, 19, "_classPrivateFieldLooseKey2"], [30, 333, 15, 19], [30, 334, 15, 19, "default"], [30, 341, 15, 19], [30, 355, 15, 19, "_observers"], [30, 365, 15, 19], [30, 385, 15, 19, "_classPrivateFieldLooseKey2"], [30, 412, 15, 19], [30, 413, 15, 19, "default"], [30, 420, 15, 19], [30, 436, 15, 19, "_combinedResult"], [30, 451, 15, 19], [30, 471, 15, 19, "_classPrivateFieldLooseKey2"], [30, 498, 15, 19], [30, 499, 15, 19, "default"], [30, 506, 15, 19], [30, 527, 15, 19, "_last<PERSON><PERSON><PERSON>"], [30, 539, 15, 19], [30, 559, 15, 19, "_classPrivateFieldLooseKey2"], [30, 586, 15, 19], [30, 587, 15, 19, "default"], [30, 594, 15, 19], [30, 612, 15, 19, "_lastResult"], [30, 623, 15, 19], [30, 643, 15, 19, "_classPrivateFieldLooseKey2"], [30, 670, 15, 19], [30, 671, 15, 19, "default"], [30, 678, 15, 19], [30, 695, 15, 19, "_observerMatches"], [30, 711, 15, 19], [30, 731, 15, 19, "_classPrivateFieldLooseKey2"], [30, 758, 15, 19], [30, 759, 15, 19, "default"], [30, 766, 15, 19], [30, 788, 15, 19, "_trackResult"], [30, 800, 15, 19], [30, 820, 15, 19, "_classPrivateFieldLooseKey2"], [30, 847, 15, 19], [30, 848, 15, 19, "default"], [30, 855, 15, 19], [30, 873, 15, 19, "_combineResult"], [30, 887, 15, 19], [30, 907, 15, 19, "_classPrivateFieldLooseKey2"], [30, 934, 15, 19], [30, 935, 15, 19, "default"], [30, 942, 15, 19], [30, 962, 15, 19, "_findMatchingObservers"], [30, 984, 15, 19], [30, 1004, 15, 19, "_classPrivateFieldLooseKey2"], [30, 1031, 15, 19], [30, 1032, 15, 19, "default"], [30, 1039, 15, 19], [30, 1067, 15, 19, "_onUpdate"], [30, 1076, 15, 19], [30, 1096, 15, 19, "_classPrivateFieldLooseKey2"], [30, 1123, 15, 19], [30, 1124, 15, 19, "default"], [30, 1131, 15, 19], [30, 1146, 15, 19, "_notify"], [30, 1153, 15, 19], [30, 1173, 15, 19, "_classPrivateFieldLooseKey2"], [30, 1200, 15, 19], [30, 1201, 15, 19, "default"], [30, 1208, 15, 19], [30, 1244, 15, 19, "_Subscribable"], [30, 1257, 15, 19], [31, 4, 25, 2], [31, 13, 25, 2, "QueriesObserver"], [31, 29, 25, 14, "client"], [31, 35, 25, 20], [31, 37, 25, 22, "queries"], [31, 46, 25, 29], [31, 48, 25, 31, "options"], [31, 57, 25, 38], [31, 59, 25, 40], [32, 6, 25, 40], [32, 10, 25, 40, "_this"], [32, 15, 25, 40], [33, 6, 25, 40], [33, 10, 25, 40, "_classCallCheck2"], [33, 26, 25, 40], [33, 27, 25, 40, "default"], [33, 34, 25, 40], [33, 42, 25, 40, "QueriesObserver"], [33, 57, 25, 40], [34, 6, 26, 4, "_this"], [34, 11, 26, 4], [34, 14, 26, 4, "_callSuper"], [34, 24, 26, 4], [34, 31, 26, 4, "QueriesObserver"], [34, 46, 26, 4], [35, 6, 26, 12, "Object"], [35, 12, 26, 12], [35, 13, 26, 12, "defineProperty"], [35, 27, 26, 12], [35, 28, 26, 12, "_this"], [35, 33, 26, 12], [35, 35, 26, 12, "_notify"], [35, 42, 26, 12], [36, 8, 26, 12, "value"], [36, 13, 26, 12], [36, 15, 26, 12, "_notify2"], [37, 6, 26, 12], [38, 6, 26, 12, "Object"], [38, 12, 26, 12], [38, 13, 26, 12, "defineProperty"], [38, 27, 26, 12], [38, 28, 26, 12, "_this"], [38, 33, 26, 12], [38, 35, 26, 12, "_onUpdate"], [38, 44, 26, 12], [39, 8, 26, 12, "value"], [39, 13, 26, 12], [39, 15, 26, 12, "_onUpdate2"], [40, 6, 26, 12], [41, 6, 26, 12, "Object"], [41, 12, 26, 12], [41, 13, 26, 12, "defineProperty"], [41, 27, 26, 12], [41, 28, 26, 12, "_this"], [41, 33, 26, 12], [41, 35, 26, 12, "_findMatchingObservers"], [41, 57, 26, 12], [42, 8, 26, 12, "value"], [42, 13, 26, 12], [42, 15, 26, 12, "_findMatchingObservers2"], [43, 6, 26, 12], [44, 6, 26, 12, "Object"], [44, 12, 26, 12], [44, 13, 26, 12, "defineProperty"], [44, 27, 26, 12], [44, 28, 26, 12, "_this"], [44, 33, 26, 12], [44, 35, 26, 12, "_combineResult"], [44, 49, 26, 12], [45, 8, 26, 12, "value"], [45, 13, 26, 12], [45, 15, 26, 12, "_combineResult2"], [46, 6, 26, 12], [47, 6, 26, 12, "Object"], [47, 12, 26, 12], [47, 13, 26, 12, "defineProperty"], [47, 27, 26, 12], [47, 28, 26, 12, "_this"], [47, 33, 26, 12], [47, 35, 26, 12, "_trackResult"], [47, 47, 26, 12], [48, 8, 26, 12, "value"], [48, 13, 26, 12], [48, 15, 26, 12, "_trackResult2"], [49, 6, 26, 12], [50, 6, 26, 12, "Object"], [50, 12, 26, 12], [50, 13, 26, 12, "defineProperty"], [50, 27, 26, 12], [50, 28, 26, 12, "_this"], [50, 33, 26, 12], [50, 35, 26, 12, "_client"], [50, 42, 26, 12], [51, 8, 26, 12, "writable"], [51, 16, 26, 12], [52, 8, 26, 12, "value"], [52, 13, 26, 12], [53, 6, 26, 12], [54, 6, 26, 12, "Object"], [54, 12, 26, 12], [54, 13, 26, 12, "defineProperty"], [54, 27, 26, 12], [54, 28, 26, 12, "_this"], [54, 33, 26, 12], [54, 35, 26, 12, "_result"], [54, 42, 26, 12], [55, 8, 26, 12, "writable"], [55, 16, 26, 12], [56, 8, 26, 12, "value"], [56, 13, 26, 12], [57, 6, 26, 12], [58, 6, 26, 12, "Object"], [58, 12, 26, 12], [58, 13, 26, 12, "defineProperty"], [58, 27, 26, 12], [58, 28, 26, 12, "_this"], [58, 33, 26, 12], [58, 35, 26, 12, "_queries"], [58, 43, 26, 12], [59, 8, 26, 12, "writable"], [59, 16, 26, 12], [60, 8, 26, 12, "value"], [60, 13, 26, 12], [61, 6, 26, 12], [62, 6, 26, 12, "Object"], [62, 12, 26, 12], [62, 13, 26, 12, "defineProperty"], [62, 27, 26, 12], [62, 28, 26, 12, "_this"], [62, 33, 26, 12], [62, 35, 26, 12, "_options"], [62, 43, 26, 12], [63, 8, 26, 12, "writable"], [63, 16, 26, 12], [64, 8, 26, 12, "value"], [64, 13, 26, 12], [65, 6, 26, 12], [66, 6, 26, 12, "Object"], [66, 12, 26, 12], [66, 13, 26, 12, "defineProperty"], [66, 27, 26, 12], [66, 28, 26, 12, "_this"], [66, 33, 26, 12], [66, 35, 26, 12, "_observers"], [66, 45, 26, 12], [67, 8, 26, 12, "writable"], [67, 16, 26, 12], [68, 8, 26, 12, "value"], [68, 13, 26, 12], [69, 6, 26, 12], [70, 6, 26, 12, "Object"], [70, 12, 26, 12], [70, 13, 26, 12, "defineProperty"], [70, 27, 26, 12], [70, 28, 26, 12, "_this"], [70, 33, 26, 12], [70, 35, 26, 12, "_combinedResult"], [70, 50, 26, 12], [71, 8, 26, 12, "writable"], [71, 16, 26, 12], [72, 8, 26, 12, "value"], [72, 13, 26, 12], [73, 6, 26, 12], [74, 6, 26, 12, "Object"], [74, 12, 26, 12], [74, 13, 26, 12, "defineProperty"], [74, 27, 26, 12], [74, 28, 26, 12, "_this"], [74, 33, 26, 12], [74, 35, 26, 12, "_last<PERSON><PERSON><PERSON>"], [74, 47, 26, 12], [75, 8, 26, 12, "writable"], [75, 16, 26, 12], [76, 8, 26, 12, "value"], [76, 13, 26, 12], [77, 6, 26, 12], [78, 6, 26, 12, "Object"], [78, 12, 26, 12], [78, 13, 26, 12, "defineProperty"], [78, 27, 26, 12], [78, 28, 26, 12, "_this"], [78, 33, 26, 12], [78, 35, 26, 12, "_lastResult"], [78, 46, 26, 12], [79, 8, 26, 12, "writable"], [79, 16, 26, 12], [80, 8, 26, 12, "value"], [80, 13, 26, 12], [81, 6, 26, 12], [82, 6, 26, 12, "Object"], [82, 12, 26, 12], [82, 13, 26, 12, "defineProperty"], [82, 27, 26, 12], [82, 28, 26, 12, "_this"], [82, 33, 26, 12], [82, 35, 26, 12, "_observerMatches"], [82, 51, 26, 12], [83, 8, 26, 12, "writable"], [83, 16, 26, 12], [84, 8, 26, 12, "value"], [84, 13, 26, 12], [84, 15, 24, 21], [85, 6, 24, 23], [86, 6, 27, 4], [86, 10, 27, 4, "_classPrivateFieldLooseBase2"], [86, 38, 27, 4], [86, 39, 27, 4, "default"], [86, 46, 27, 4], [86, 48, 27, 4, "_this"], [86, 53, 27, 4], [86, 55, 27, 4, "_client"], [86, 62, 27, 4], [86, 64, 27, 4, "_client"], [86, 71, 27, 4], [86, 75, 27, 19, "client"], [86, 81, 27, 25], [87, 6, 28, 4], [87, 10, 28, 4, "_classPrivateFieldLooseBase2"], [87, 38, 28, 4], [87, 39, 28, 4, "default"], [87, 46, 28, 4], [87, 48, 28, 4, "_this"], [87, 53, 28, 4], [87, 55, 28, 4, "_options"], [87, 63, 28, 4], [87, 65, 28, 4, "_options"], [87, 73, 28, 4], [87, 77, 28, 20, "options"], [87, 86, 28, 27], [88, 6, 29, 4], [88, 10, 29, 4, "_classPrivateFieldLooseBase2"], [88, 38, 29, 4], [88, 39, 29, 4, "default"], [88, 46, 29, 4], [88, 48, 29, 4, "_this"], [88, 53, 29, 4], [88, 55, 29, 4, "_queries"], [88, 63, 29, 4], [88, 65, 29, 4, "_queries"], [88, 73, 29, 4], [88, 77, 29, 20], [88, 79, 29, 22], [89, 6, 30, 4], [89, 10, 30, 4, "_classPrivateFieldLooseBase2"], [89, 38, 30, 4], [89, 39, 30, 4, "default"], [89, 46, 30, 4], [89, 48, 30, 4, "_this"], [89, 53, 30, 4], [89, 55, 30, 4, "_observers"], [89, 65, 30, 4], [89, 67, 30, 4, "_observers"], [89, 77, 30, 4], [89, 81, 30, 22], [89, 83, 30, 24], [90, 6, 31, 4], [90, 10, 31, 4, "_classPrivateFieldLooseBase2"], [90, 38, 31, 4], [90, 39, 31, 4, "default"], [90, 46, 31, 4], [90, 48, 31, 4, "_this"], [90, 53, 31, 4], [90, 55, 31, 4, "_result"], [90, 62, 31, 4], [90, 64, 31, 4, "_result"], [90, 71, 31, 4], [90, 75, 31, 19], [90, 77, 31, 21], [91, 6, 32, 4, "_this"], [91, 11, 32, 4], [91, 12, 32, 9, "setQueries"], [91, 22, 32, 19], [91, 23, 32, 20, "queries"], [91, 32, 32, 27], [91, 33, 32, 28], [92, 6, 32, 29], [92, 13, 32, 29, "_this"], [92, 18, 32, 29], [93, 4, 33, 2], [94, 4, 33, 3], [94, 8, 33, 3, "_inherits2"], [94, 18, 33, 3], [94, 19, 33, 3, "default"], [94, 26, 33, 3], [94, 28, 33, 3, "QueriesObserver"], [94, 43, 33, 3], [94, 45, 33, 3, "_Subscribable"], [94, 58, 33, 3], [95, 4, 33, 3], [95, 15, 33, 3, "_createClass2"], [95, 28, 33, 3], [95, 29, 33, 3, "default"], [95, 36, 33, 3], [95, 38, 33, 3, "QueriesObserver"], [95, 53, 33, 3], [96, 6, 33, 3, "key"], [96, 9, 33, 3], [97, 6, 33, 3, "value"], [97, 11, 33, 3], [97, 13, 34, 2], [97, 22, 34, 2, "onSubscribe"], [97, 33, 34, 13, "onSubscribe"], [97, 34, 34, 13], [97, 36, 34, 16], [98, 8, 35, 4], [98, 12, 35, 8], [98, 16, 35, 12], [98, 17, 35, 13, "listeners"], [98, 26, 35, 22], [98, 27, 35, 23, "size"], [98, 31, 35, 27], [98, 36, 35, 32], [98, 37, 35, 33], [98, 39, 35, 35], [99, 10, 36, 6], [99, 14, 36, 6, "_classPrivateFieldLooseBase2"], [99, 42, 36, 6], [99, 43, 36, 6, "default"], [99, 50, 36, 6], [99, 56, 36, 10], [99, 58, 36, 10, "_observers"], [99, 68, 36, 10], [99, 70, 36, 10, "_observers"], [99, 80, 36, 10], [99, 82, 36, 22, "for<PERSON>ach"], [99, 89, 36, 29], [99, 90, 36, 31, "observer"], [99, 98, 36, 39], [99, 102, 36, 44], [100, 12, 37, 8, "observer"], [100, 20, 37, 16], [100, 21, 37, 17, "subscribe"], [100, 30, 37, 26], [100, 31, 37, 28, "result"], [100, 37, 37, 34], [100, 41, 37, 39], [101, 14, 38, 10], [101, 18, 38, 10, "_classPrivateFieldLooseBase2"], [101, 46, 38, 10], [101, 47, 38, 10, "default"], [101, 54, 38, 10], [101, 60, 38, 14], [101, 62, 38, 14, "_onUpdate"], [101, 71, 38, 14], [101, 73, 38, 14, "_onUpdate"], [101, 82, 38, 14], [101, 84, 38, 25, "observer"], [101, 92, 38, 33], [101, 94, 38, 35, "result"], [101, 100, 38, 41], [102, 12, 39, 8], [102, 13, 39, 9], [102, 14, 39, 10], [103, 10, 40, 6], [103, 11, 40, 7], [103, 12, 40, 8], [104, 8, 41, 4], [105, 6, 42, 2], [106, 4, 42, 3], [107, 6, 42, 3, "key"], [107, 9, 42, 3], [108, 6, 42, 3, "value"], [108, 11, 42, 3], [108, 13, 43, 2], [108, 22, 43, 2, "onUnsubscribe"], [108, 35, 43, 15, "onUnsubscribe"], [108, 36, 43, 15], [108, 38, 43, 18], [109, 8, 44, 4], [109, 12, 44, 8], [109, 13, 44, 9], [109, 17, 44, 13], [109, 18, 44, 14, "listeners"], [109, 27, 44, 23], [109, 28, 44, 24, "size"], [109, 32, 44, 28], [109, 34, 44, 30], [110, 10, 45, 6], [110, 14, 45, 10], [110, 15, 45, 11, "destroy"], [110, 22, 45, 18], [110, 23, 45, 19], [110, 24, 45, 20], [111, 8, 46, 4], [112, 6, 47, 2], [113, 4, 47, 3], [114, 6, 47, 3, "key"], [114, 9, 47, 3], [115, 6, 47, 3, "value"], [115, 11, 47, 3], [115, 13, 48, 2], [115, 22, 48, 2, "destroy"], [115, 29, 48, 9, "destroy"], [115, 30, 48, 9], [115, 32, 48, 12], [116, 8, 49, 4], [116, 12, 49, 8], [116, 13, 49, 9, "listeners"], [116, 22, 49, 18], [116, 25, 49, 21], [116, 40, 49, 37], [116, 44, 49, 41, "Set"], [116, 47, 49, 44], [116, 48, 49, 45], [116, 49, 49, 46], [117, 8, 50, 4], [117, 12, 50, 4, "_classPrivateFieldLooseBase2"], [117, 40, 50, 4], [117, 41, 50, 4, "default"], [117, 48, 50, 4], [117, 54, 50, 8], [117, 56, 50, 8, "_observers"], [117, 66, 50, 8], [117, 68, 50, 8, "_observers"], [117, 78, 50, 8], [117, 80, 50, 20, "for<PERSON>ach"], [117, 87, 50, 27], [117, 88, 50, 29, "observer"], [117, 96, 50, 37], [117, 100, 50, 42], [118, 10, 51, 6, "observer"], [118, 18, 51, 14], [118, 19, 51, 15, "destroy"], [118, 26, 51, 22], [118, 27, 51, 23], [118, 28, 51, 24], [119, 8, 52, 4], [119, 9, 52, 5], [119, 10, 52, 6], [120, 6, 53, 2], [121, 4, 53, 3], [122, 6, 53, 3, "key"], [122, 9, 53, 3], [123, 6, 53, 3, "value"], [123, 11, 53, 3], [123, 13, 54, 2], [123, 22, 54, 2, "setQueries"], [123, 32, 54, 12, "setQueries"], [123, 33, 54, 13, "queries"], [123, 40, 54, 20], [123, 42, 54, 22, "options"], [123, 49, 54, 29], [123, 51, 54, 31], [124, 8, 55, 4], [124, 12, 55, 4, "_classPrivateFieldLooseBase2"], [124, 40, 55, 4], [124, 41, 55, 4, "default"], [124, 48, 55, 4], [124, 54, 55, 8], [124, 56, 55, 8, "_queries"], [124, 64, 55, 8], [124, 66, 55, 8, "_queries"], [124, 74, 55, 8], [124, 78, 55, 20, "queries"], [124, 85, 55, 27], [125, 8, 56, 4], [125, 12, 56, 4, "_classPrivateFieldLooseBase2"], [125, 40, 56, 4], [125, 41, 56, 4, "default"], [125, 48, 56, 4], [125, 54, 56, 8], [125, 56, 56, 8, "_options"], [125, 64, 56, 8], [125, 66, 56, 8, "_options"], [125, 74, 56, 8], [125, 78, 56, 20, "options"], [125, 85, 56, 27], [126, 8, 57, 4], [126, 12, 57, 8, "process"], [126, 19, 57, 15], [126, 20, 57, 16, "env"], [126, 23, 57, 19], [126, 24, 57, 20, "NODE_ENV"], [126, 32, 57, 28], [126, 37, 57, 33], [126, 49, 57, 45], [126, 51, 57, 47], [127, 10, 58, 6], [127, 14, 58, 12, "queryHashes"], [127, 25, 58, 23], [127, 28, 58, 26, "queries"], [127, 35, 58, 33], [127, 36, 58, 34, "map"], [127, 39, 58, 37], [127, 40, 59, 9, "query"], [127, 45, 59, 14], [127, 49, 59, 19], [127, 53, 59, 19, "_classPrivateFieldLooseBase2"], [127, 81, 59, 19], [127, 82, 59, 19, "default"], [127, 89, 59, 19], [127, 95, 59, 23], [127, 97, 59, 23, "_client"], [127, 104, 59, 23], [127, 106, 59, 23, "_client"], [127, 113, 59, 23], [127, 115, 59, 32, "defaultQueryOptions"], [127, 134, 59, 51], [127, 135, 59, 52, "query"], [127, 140, 59, 57], [127, 141, 59, 58], [127, 142, 59, 59, "queryHash"], [127, 151, 60, 6], [127, 152, 60, 7], [128, 10, 61, 6], [128, 14, 61, 10], [128, 18, 61, 14, "Set"], [128, 21, 61, 17], [128, 22, 61, 18, "queryHashes"], [128, 33, 61, 29], [128, 34, 61, 30], [128, 35, 61, 31, "size"], [128, 39, 61, 35], [128, 44, 61, 40, "queryHashes"], [128, 55, 61, 51], [128, 56, 61, 52, "length"], [128, 62, 61, 58], [128, 64, 61, 60], [129, 12, 62, 8, "console"], [129, 19, 62, 15], [129, 20, 62, 16, "warn"], [129, 24, 62, 20], [129, 25, 63, 10], [129, 112, 64, 8], [129, 113, 64, 9], [130, 10, 65, 6], [131, 8, 66, 4], [132, 8, 67, 4, "notify<PERSON><PERSON>ger"], [132, 36, 67, 17], [132, 37, 67, 18, "batch"], [132, 42, 67, 23], [132, 43, 67, 24], [132, 49, 67, 30], [133, 10, 68, 6], [133, 14, 68, 12, "prevObservers"], [133, 27, 68, 25], [133, 34, 68, 25, "_classPrivateFieldLooseBase2"], [133, 62, 68, 25], [133, 63, 68, 25, "default"], [133, 70, 68, 25], [133, 72, 68, 28], [133, 76, 68, 32], [133, 78, 68, 32, "_observers"], [133, 88, 68, 32], [133, 90, 68, 32, "_observers"], [133, 100, 68, 32], [133, 101, 68, 43], [134, 10, 69, 6], [134, 14, 69, 12, "newObserverMatches"], [134, 32, 69, 30], [134, 39, 69, 30, "_classPrivateFieldLooseBase2"], [134, 67, 69, 30], [134, 68, 69, 30, "default"], [134, 75, 69, 30], [134, 77, 69, 33], [134, 81, 69, 37], [134, 83, 69, 37, "_findMatchingObservers"], [134, 105, 69, 37], [134, 107, 69, 37, "_findMatchingObservers"], [134, 129, 69, 37], [134, 135, 69, 37, "_classPrivateFieldLooseBase2"], [134, 163, 69, 37], [134, 164, 69, 37, "default"], [134, 171, 69, 37], [134, 173, 69, 61], [134, 177, 69, 65], [134, 179, 69, 65, "_queries"], [134, 187, 69, 65], [134, 189, 69, 65, "_queries"], [134, 197, 69, 65], [134, 199, 69, 75], [135, 10, 70, 6], [135, 14, 70, 6, "_classPrivateFieldLooseBase2"], [135, 42, 70, 6], [135, 43, 70, 6, "default"], [135, 50, 70, 6], [135, 56, 70, 10], [135, 58, 70, 10, "_observerMatches"], [135, 74, 70, 10], [135, 76, 70, 10, "_observerMatches"], [135, 92, 70, 10], [135, 96, 70, 30, "newObserverMatches"], [135, 114, 70, 48], [136, 10, 71, 6, "newObserverMatches"], [136, 28, 71, 24], [136, 29, 71, 25, "for<PERSON>ach"], [136, 36, 71, 32], [136, 37, 72, 9, "match"], [136, 42, 72, 14], [136, 46, 72, 19, "match"], [136, 51, 72, 24], [136, 52, 72, 25, "observer"], [136, 60, 72, 33], [136, 61, 72, 34, "setOptions"], [136, 71, 72, 44], [136, 72, 72, 45, "match"], [136, 77, 72, 50], [136, 78, 72, 51, "defaultedQueryOptions"], [136, 99, 72, 72], [136, 100, 73, 6], [136, 101, 73, 7], [137, 10, 74, 6], [137, 14, 74, 12, "newObservers"], [137, 26, 74, 24], [137, 29, 74, 27, "newObserverMatches"], [137, 47, 74, 45], [137, 48, 74, 46, "map"], [137, 51, 74, 49], [137, 52, 74, 51, "match"], [137, 57, 74, 56], [137, 61, 74, 61, "match"], [137, 66, 74, 66], [137, 67, 74, 67, "observer"], [137, 75, 74, 75], [137, 76, 74, 76], [138, 10, 75, 6], [138, 14, 75, 12, "newResult"], [138, 23, 75, 21], [138, 26, 75, 24, "newObservers"], [138, 38, 75, 36], [138, 39, 75, 37, "map"], [138, 42, 75, 40], [138, 43, 76, 9, "observer"], [138, 51, 76, 17], [138, 55, 76, 22, "observer"], [138, 63, 76, 30], [138, 64, 76, 31, "getCurrentResult"], [138, 80, 76, 47], [138, 81, 76, 48], [138, 82, 77, 6], [138, 83, 77, 7], [139, 10, 78, 6], [139, 14, 78, 12, "hasIndexChange"], [139, 28, 78, 26], [139, 31, 78, 29, "newObservers"], [139, 43, 78, 41], [139, 44, 78, 42, "some"], [139, 48, 78, 46], [139, 49, 79, 8], [139, 50, 79, 9, "observer"], [139, 58, 79, 17], [139, 60, 79, 19, "index"], [139, 65, 79, 24], [139, 70, 79, 29, "observer"], [139, 78, 79, 37], [139, 83, 79, 42, "prevObservers"], [139, 96, 79, 55], [139, 97, 79, 56, "index"], [139, 102, 79, 61], [139, 103, 80, 6], [139, 104, 80, 7], [140, 10, 81, 6], [140, 14, 81, 10, "prevObservers"], [140, 27, 81, 23], [140, 28, 81, 24, "length"], [140, 34, 81, 30], [140, 39, 81, 35, "newObservers"], [140, 51, 81, 47], [140, 52, 81, 48, "length"], [140, 58, 81, 54], [140, 62, 81, 58], [140, 63, 81, 59, "hasIndexChange"], [140, 77, 81, 73], [140, 79, 81, 75], [141, 12, 82, 8], [142, 10, 83, 6], [143, 10, 84, 6], [143, 14, 84, 6, "_classPrivateFieldLooseBase2"], [143, 42, 84, 6], [143, 43, 84, 6, "default"], [143, 50, 84, 6], [143, 56, 84, 10], [143, 58, 84, 10, "_observers"], [143, 68, 84, 10], [143, 70, 84, 10, "_observers"], [143, 80, 84, 10], [143, 84, 84, 24, "newObservers"], [143, 96, 84, 36], [144, 10, 85, 6], [144, 14, 85, 6, "_classPrivateFieldLooseBase2"], [144, 42, 85, 6], [144, 43, 85, 6, "default"], [144, 50, 85, 6], [144, 56, 85, 10], [144, 58, 85, 10, "_result"], [144, 65, 85, 10], [144, 67, 85, 10, "_result"], [144, 74, 85, 10], [144, 78, 85, 21, "newResult"], [144, 87, 85, 30], [145, 10, 86, 6], [145, 14, 86, 10], [145, 15, 86, 11], [145, 19, 86, 15], [145, 20, 86, 16, "hasListeners"], [145, 32, 86, 28], [145, 33, 86, 29], [145, 34, 86, 30], [145, 36, 86, 32], [146, 12, 87, 8], [147, 10, 88, 6], [148, 10, 89, 6, "difference"], [148, 20, 89, 16], [148, 21, 89, 17, "prevObservers"], [148, 34, 89, 30], [148, 36, 89, 32, "newObservers"], [148, 48, 89, 44], [148, 49, 89, 45], [148, 50, 89, 46, "for<PERSON>ach"], [148, 57, 89, 53], [148, 58, 89, 55, "observer"], [148, 66, 89, 63], [148, 70, 89, 68], [149, 12, 90, 8, "observer"], [149, 20, 90, 16], [149, 21, 90, 17, "destroy"], [149, 28, 90, 24], [149, 29, 90, 25], [149, 30, 90, 26], [150, 10, 91, 6], [150, 11, 91, 7], [150, 12, 91, 8], [151, 10, 92, 6, "difference"], [151, 20, 92, 16], [151, 21, 92, 17, "newObservers"], [151, 33, 92, 29], [151, 35, 92, 31, "prevObservers"], [151, 48, 92, 44], [151, 49, 92, 45], [151, 50, 92, 46, "for<PERSON>ach"], [151, 57, 92, 53], [151, 58, 92, 55, "observer"], [151, 66, 92, 63], [151, 70, 92, 68], [152, 12, 93, 8, "observer"], [152, 20, 93, 16], [152, 21, 93, 17, "subscribe"], [152, 30, 93, 26], [152, 31, 93, 28, "result"], [152, 37, 93, 34], [152, 41, 93, 39], [153, 14, 94, 10], [153, 18, 94, 10, "_classPrivateFieldLooseBase2"], [153, 46, 94, 10], [153, 47, 94, 10, "default"], [153, 54, 94, 10], [153, 60, 94, 14], [153, 62, 94, 14, "_onUpdate"], [153, 71, 94, 14], [153, 73, 94, 14, "_onUpdate"], [153, 82, 94, 14], [153, 84, 94, 25, "observer"], [153, 92, 94, 33], [153, 94, 94, 35, "result"], [153, 100, 94, 41], [154, 12, 95, 8], [154, 13, 95, 9], [154, 14, 95, 10], [155, 10, 96, 6], [155, 11, 96, 7], [155, 12, 96, 8], [156, 10, 97, 6], [156, 14, 97, 6, "_classPrivateFieldLooseBase2"], [156, 42, 97, 6], [156, 43, 97, 6, "default"], [156, 50, 97, 6], [156, 56, 97, 10], [156, 58, 97, 10, "_notify"], [156, 65, 97, 10], [156, 67, 97, 10, "_notify"], [156, 74, 97, 10], [157, 8, 98, 4], [157, 9, 98, 5], [157, 10, 98, 6], [158, 6, 99, 2], [159, 4, 99, 3], [160, 6, 99, 3, "key"], [160, 9, 99, 3], [161, 6, 99, 3, "value"], [161, 11, 99, 3], [161, 13, 100, 2], [161, 22, 100, 2, "getCurrentResult"], [161, 38, 100, 18, "getCurrentResult"], [161, 39, 100, 18], [161, 41, 100, 21], [162, 8, 101, 4], [162, 19, 101, 4, "_classPrivateFieldLooseBase2"], [162, 47, 101, 4], [162, 48, 101, 4, "default"], [162, 55, 101, 4], [162, 57, 101, 11], [162, 61, 101, 15], [162, 63, 101, 15, "_result"], [162, 70, 101, 15], [162, 72, 101, 15, "_result"], [162, 79, 101, 15], [163, 6, 102, 2], [164, 4, 102, 3], [165, 6, 102, 3, "key"], [165, 9, 102, 3], [166, 6, 102, 3, "value"], [166, 11, 102, 3], [166, 13, 103, 2], [166, 22, 103, 2, "getQueries"], [166, 32, 103, 12, "getQueries"], [166, 33, 103, 12], [166, 35, 103, 15], [167, 8, 104, 4], [167, 15, 104, 11], [167, 19, 104, 11, "_classPrivateFieldLooseBase2"], [167, 47, 104, 11], [167, 48, 104, 11, "default"], [167, 55, 104, 11], [167, 61, 104, 15], [167, 63, 104, 15, "_observers"], [167, 73, 104, 15], [167, 75, 104, 15, "_observers"], [167, 85, 104, 15], [167, 87, 104, 27, "map"], [167, 90, 104, 30], [167, 91, 104, 32, "observer"], [167, 99, 104, 40], [167, 103, 104, 45, "observer"], [167, 111, 104, 53], [167, 112, 104, 54, "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [167, 127, 104, 69], [167, 128, 104, 70], [167, 129, 104, 71], [167, 130, 104, 72], [168, 6, 105, 2], [169, 4, 105, 3], [170, 6, 105, 3, "key"], [170, 9, 105, 3], [171, 6, 105, 3, "value"], [171, 11, 105, 3], [171, 13, 106, 2], [171, 22, 106, 2, "getObservers"], [171, 34, 106, 14, "getObservers"], [171, 35, 106, 14], [171, 37, 106, 17], [172, 8, 107, 4], [172, 19, 107, 4, "_classPrivateFieldLooseBase2"], [172, 47, 107, 4], [172, 48, 107, 4, "default"], [172, 55, 107, 4], [172, 57, 107, 11], [172, 61, 107, 15], [172, 63, 107, 15, "_observers"], [172, 73, 107, 15], [172, 75, 107, 15, "_observers"], [172, 85, 107, 15], [173, 6, 108, 2], [174, 4, 108, 3], [175, 6, 108, 3, "key"], [175, 9, 108, 3], [176, 6, 108, 3, "value"], [176, 11, 108, 3], [176, 13, 109, 2], [176, 22, 109, 2, "getOptimisticResult"], [176, 41, 109, 21, "getOptimisticResult"], [176, 42, 109, 22, "queries"], [176, 49, 109, 29], [176, 51, 109, 31, "combine"], [176, 58, 109, 38], [176, 60, 109, 40], [177, 8, 110, 4], [177, 12, 110, 10, "matches"], [177, 19, 110, 17], [177, 26, 110, 17, "_classPrivateFieldLooseBase2"], [177, 54, 110, 17], [177, 55, 110, 17, "default"], [177, 62, 110, 17], [177, 64, 110, 20], [177, 68, 110, 24], [177, 70, 110, 24, "_findMatchingObservers"], [177, 92, 110, 24], [177, 94, 110, 24, "_findMatchingObservers"], [177, 116, 110, 24], [177, 118, 110, 48, "queries"], [177, 125, 110, 55], [177, 126, 110, 56], [178, 8, 111, 4], [178, 12, 111, 10, "result"], [178, 18, 111, 16], [178, 21, 111, 19, "matches"], [178, 28, 111, 26], [178, 29, 111, 27, "map"], [178, 32, 111, 30], [178, 33, 112, 7, "match"], [178, 38, 112, 12], [178, 42, 112, 17, "match"], [178, 47, 112, 22], [178, 48, 112, 23, "observer"], [178, 56, 112, 31], [178, 57, 112, 32, "getOptimisticResult"], [178, 76, 112, 51], [178, 77, 112, 52, "match"], [178, 82, 112, 57], [178, 83, 112, 58, "defaultedQueryOptions"], [178, 104, 112, 79], [178, 105, 113, 4], [178, 106, 113, 5], [179, 8, 114, 4], [179, 15, 114, 11], [179, 16, 115, 6, "result"], [179, 22, 115, 12], [179, 24, 116, 7, "r"], [179, 25, 116, 8], [179, 29, 116, 13], [180, 10, 117, 8], [180, 21, 117, 8, "_classPrivateFieldLooseBase2"], [180, 49, 117, 8], [180, 50, 117, 8, "default"], [180, 57, 117, 8], [180, 59, 117, 15], [180, 63, 117, 19], [180, 65, 117, 19, "_combineResult"], [180, 79, 117, 19], [180, 81, 117, 19, "_combineResult"], [180, 95, 117, 19], [180, 97, 117, 35, "r"], [180, 98, 117, 36], [180, 102, 117, 40, "result"], [180, 108, 117, 46], [180, 110, 117, 48, "combine"], [180, 117, 117, 55], [181, 8, 118, 6], [181, 9, 118, 7], [181, 11, 119, 6], [181, 17, 119, 12], [182, 10, 120, 8], [182, 21, 120, 8, "_classPrivateFieldLooseBase2"], [182, 49, 120, 8], [182, 50, 120, 8, "default"], [182, 57, 120, 8], [182, 59, 120, 15], [182, 63, 120, 19], [182, 65, 120, 19, "_trackResult"], [182, 77, 120, 19], [182, 79, 120, 19, "_trackResult"], [182, 91, 120, 19], [182, 93, 120, 33, "result"], [182, 99, 120, 39], [182, 101, 120, 41, "matches"], [182, 108, 120, 48], [183, 8, 121, 6], [183, 9, 121, 7], [183, 10, 122, 5], [184, 6, 123, 2], [185, 4, 123, 3], [186, 2, 123, 3], [186, 4, 15, 36, "Subscribable"], [186, 30, 15, 48], [186, 32, 191, 1], [187, 2, 191, 2], [187, 11, 191, 2, "_trackResult2"], [187, 25, 124, 15, "result"], [187, 31, 124, 21], [187, 33, 124, 23, "matches"], [187, 40, 124, 30], [187, 42, 124, 32], [188, 4, 125, 4], [188, 11, 125, 11, "matches"], [188, 18, 125, 18], [188, 19, 125, 19, "map"], [188, 22, 125, 22], [188, 23, 125, 23], [188, 24, 125, 24, "match"], [188, 29, 125, 29], [188, 31, 125, 31, "index"], [188, 36, 125, 36], [188, 41, 125, 41], [189, 6, 126, 6], [189, 10, 126, 12, "observerResult"], [189, 24, 126, 26], [189, 27, 126, 29, "result"], [189, 33, 126, 35], [189, 34, 126, 36, "index"], [189, 39, 126, 41], [189, 40, 126, 42], [190, 6, 127, 6], [190, 13, 127, 13], [190, 14, 127, 14, "match"], [190, 19, 127, 19], [190, 20, 127, 20, "defaultedQueryOptions"], [190, 41, 127, 41], [190, 42, 127, 42, "notifyOnChangeProps"], [190, 61, 127, 61], [190, 64, 127, 64, "match"], [190, 69, 127, 69], [190, 70, 127, 70, "observer"], [190, 78, 127, 78], [190, 79, 127, 79, "trackResult"], [190, 90, 127, 90], [190, 91, 127, 91, "observerResult"], [190, 105, 127, 105], [190, 107, 127, 108, "accessedProp"], [190, 119, 127, 120], [190, 123, 127, 125], [191, 8, 128, 8, "matches"], [191, 15, 128, 15], [191, 16, 128, 16, "for<PERSON>ach"], [191, 23, 128, 23], [191, 24, 128, 25, "m"], [191, 25, 128, 26], [191, 29, 128, 31], [192, 10, 129, 10, "m"], [192, 11, 129, 11], [192, 12, 129, 12, "observer"], [192, 20, 129, 20], [192, 21, 129, 21, "trackProp"], [192, 30, 129, 30], [192, 31, 129, 31, "accessedProp"], [192, 43, 129, 43], [192, 44, 129, 44], [193, 8, 130, 8], [193, 9, 130, 9], [193, 10, 130, 10], [194, 6, 131, 6], [194, 7, 131, 7], [194, 8, 131, 8], [194, 11, 131, 11, "observerResult"], [194, 25, 131, 25], [195, 4, 132, 4], [195, 5, 132, 5], [195, 6, 132, 6], [196, 2, 133, 2], [197, 2, 133, 3], [197, 11, 133, 3, "_combineResult2"], [197, 27, 134, 17, "input"], [197, 32, 134, 22], [197, 34, 134, 24, "combine"], [197, 41, 134, 31], [197, 43, 134, 33], [198, 4, 135, 4], [198, 8, 135, 8, "combine"], [198, 15, 135, 15], [198, 17, 135, 17], [199, 6, 136, 6], [199, 10, 136, 10], [199, 15, 136, 10, "_classPrivateFieldLooseBase2"], [199, 43, 136, 10], [199, 44, 136, 10, "default"], [199, 51, 136, 10], [199, 53, 136, 11], [199, 57, 136, 15], [199, 59, 136, 15, "_combinedResult"], [199, 74, 136, 15], [199, 76, 136, 15, "_combinedResult"], [199, 91, 136, 15], [199, 92, 136, 31], [199, 96, 136, 35], [199, 100, 136, 35, "_classPrivateFieldLooseBase2"], [199, 128, 136, 35], [199, 129, 136, 35, "default"], [199, 136, 136, 35], [199, 142, 136, 39], [199, 144, 136, 39, "_result"], [199, 151, 136, 39], [199, 153, 136, 39, "_result"], [199, 160, 136, 39], [199, 170, 136, 39, "_classPrivateFieldLooseBase2"], [199, 198, 136, 39], [199, 199, 136, 39, "default"], [199, 206, 136, 39], [199, 208, 136, 52], [199, 212, 136, 56], [199, 214, 136, 56, "_lastResult"], [199, 225, 136, 56], [199, 227, 136, 56, "_lastResult"], [199, 238, 136, 56], [199, 239, 136, 68], [199, 243, 136, 72, "combine"], [199, 250, 136, 79], [199, 259, 136, 79, "_classPrivateFieldLooseBase2"], [199, 287, 136, 79], [199, 288, 136, 79, "default"], [199, 295, 136, 79], [199, 297, 136, 84], [199, 301, 136, 88], [199, 303, 136, 88, "_last<PERSON><PERSON><PERSON>"], [199, 315, 136, 88], [199, 317, 136, 88, "_last<PERSON><PERSON><PERSON>"], [199, 329, 136, 88], [199, 330, 136, 101], [199, 332, 136, 103], [200, 8, 137, 8], [200, 12, 137, 8, "_classPrivateFieldLooseBase2"], [200, 40, 137, 8], [200, 41, 137, 8, "default"], [200, 48, 137, 8], [200, 54, 137, 12], [200, 56, 137, 12, "_last<PERSON><PERSON><PERSON>"], [200, 68, 137, 12], [200, 70, 137, 12, "_last<PERSON><PERSON><PERSON>"], [200, 82, 137, 12], [200, 86, 137, 28, "combine"], [200, 93, 137, 35], [201, 8, 138, 8], [201, 12, 138, 8, "_classPrivateFieldLooseBase2"], [201, 40, 138, 8], [201, 41, 138, 8, "default"], [201, 48, 138, 8], [201, 54, 138, 12], [201, 56, 138, 12, "_lastResult"], [201, 67, 138, 12], [201, 69, 138, 12, "_lastResult"], [201, 80, 138, 12], [201, 88, 138, 12, "_classPrivateFieldLooseBase2"], [201, 116, 138, 12], [201, 117, 138, 12, "default"], [201, 124, 138, 12], [201, 126, 138, 27], [201, 130, 138, 31], [201, 132, 138, 31, "_result"], [201, 139, 138, 31], [201, 141, 138, 31, "_result"], [201, 148, 138, 31], [201, 149, 138, 39], [202, 8, 139, 8], [202, 12, 139, 8, "_classPrivateFieldLooseBase2"], [202, 40, 139, 8], [202, 41, 139, 8, "default"], [202, 48, 139, 8], [202, 54, 139, 12], [202, 56, 139, 12, "_combinedResult"], [202, 71, 139, 12], [202, 73, 139, 12, "_combinedResult"], [202, 88, 139, 12], [202, 92, 139, 31], [202, 96, 139, 31, "replaceEqualDeep"], [202, 119, 139, 47], [202, 125, 139, 47, "_classPrivateFieldLooseBase2"], [202, 153, 139, 47], [202, 154, 139, 47, "default"], [202, 161, 139, 47], [202, 163, 140, 10], [202, 167, 140, 14], [202, 169, 140, 14, "_combinedResult"], [202, 184, 140, 14], [202, 186, 140, 14, "_combinedResult"], [202, 201, 140, 14], [202, 204, 141, 10, "combine"], [202, 211, 141, 17], [202, 212, 141, 18, "input"], [202, 217, 141, 23], [202, 218, 142, 8], [202, 219, 142, 9], [203, 6, 143, 6], [204, 6, 144, 6], [204, 17, 144, 6, "_classPrivateFieldLooseBase2"], [204, 45, 144, 6], [204, 46, 144, 6, "default"], [204, 53, 144, 6], [204, 55, 144, 13], [204, 59, 144, 17], [204, 61, 144, 17, "_combinedResult"], [204, 76, 144, 17], [204, 78, 144, 17, "_combinedResult"], [204, 93, 144, 17], [205, 4, 145, 4], [206, 4, 146, 4], [206, 11, 146, 11, "input"], [206, 16, 146, 16], [207, 2, 147, 2], [208, 2, 147, 3], [208, 11, 147, 3, "_findMatchingObservers2"], [208, 35, 148, 25, "queries"], [208, 42, 148, 32], [208, 44, 148, 34], [209, 4, 149, 4], [209, 8, 149, 10, "prevObserversMap"], [209, 24, 149, 26], [209, 27, 149, 29], [209, 31, 149, 33, "Map"], [209, 34, 149, 36], [209, 35, 150, 6], [209, 39, 150, 6, "_classPrivateFieldLooseBase2"], [209, 67, 150, 6], [209, 68, 150, 6, "default"], [209, 75, 150, 6], [209, 81, 150, 10], [209, 83, 150, 10, "_observers"], [209, 93, 150, 10], [209, 95, 150, 10, "_observers"], [209, 105, 150, 10], [209, 107, 150, 22, "map"], [209, 110, 150, 25], [209, 111, 150, 27, "observer"], [209, 119, 150, 35], [209, 123, 150, 40], [209, 124, 150, 41, "observer"], [209, 132, 150, 49], [209, 133, 150, 50, "options"], [209, 140, 150, 57], [209, 141, 150, 58, "queryHash"], [209, 150, 150, 67], [209, 152, 150, 69, "observer"], [209, 160, 150, 77], [209, 161, 150, 78], [209, 162, 151, 4], [209, 163, 151, 5], [210, 4, 152, 4], [210, 8, 152, 10, "observers"], [210, 17, 152, 19], [210, 20, 152, 22], [210, 22, 152, 24], [211, 4, 153, 4, "queries"], [211, 11, 153, 11], [211, 12, 153, 12, "for<PERSON>ach"], [211, 19, 153, 19], [211, 20, 153, 21, "options"], [211, 27, 153, 28], [211, 31, 153, 33], [212, 6, 154, 6], [212, 10, 154, 12, "defaultedOptions"], [212, 26, 154, 28], [212, 29, 154, 31], [212, 33, 154, 31, "_classPrivateFieldLooseBase2"], [212, 61, 154, 31], [212, 62, 154, 31, "default"], [212, 69, 154, 31], [212, 75, 154, 35], [212, 77, 154, 35, "_client"], [212, 84, 154, 35], [212, 86, 154, 35, "_client"], [212, 93, 154, 35], [212, 95, 154, 44, "defaultQueryOptions"], [212, 114, 154, 63], [212, 115, 154, 64, "options"], [212, 122, 154, 71], [212, 123, 154, 72], [213, 6, 155, 6], [213, 10, 155, 12, "match"], [213, 15, 155, 17], [213, 18, 155, 20, "prevObserversMap"], [213, 34, 155, 36], [213, 35, 155, 37, "get"], [213, 38, 155, 40], [213, 39, 155, 41, "defaultedOptions"], [213, 55, 155, 57], [213, 56, 155, 58, "queryHash"], [213, 65, 155, 67], [213, 66, 155, 68], [214, 6, 156, 6], [214, 10, 156, 10, "match"], [214, 15, 156, 15], [214, 17, 156, 17], [215, 8, 157, 8, "observers"], [215, 17, 157, 17], [215, 18, 157, 18, "push"], [215, 22, 157, 22], [215, 23, 157, 23], [216, 10, 158, 10, "defaultedQueryOptions"], [216, 31, 158, 31], [216, 33, 158, 33, "defaultedOptions"], [216, 49, 158, 49], [217, 10, 159, 10, "observer"], [217, 18, 159, 18], [217, 20, 159, 20, "match"], [218, 8, 160, 8], [218, 9, 160, 9], [218, 10, 160, 10], [219, 6, 161, 6], [219, 7, 161, 7], [219, 13, 161, 13], [220, 8, 162, 8, "observers"], [220, 17, 162, 17], [220, 18, 162, 18, "push"], [220, 22, 162, 22], [220, 23, 162, 23], [221, 10, 163, 10, "defaultedQueryOptions"], [221, 31, 163, 31], [221, 33, 163, 33, "defaultedOptions"], [221, 49, 163, 49], [222, 10, 164, 10, "observer"], [222, 18, 164, 18], [222, 20, 164, 20], [222, 24, 164, 24, "QueryObserver"], [222, 52, 164, 37], [222, 57, 164, 37, "_classPrivateFieldLooseBase2"], [222, 85, 164, 37], [222, 86, 164, 37, "default"], [222, 93, 164, 37], [222, 95, 164, 38], [222, 99, 164, 42], [222, 101, 164, 42, "_client"], [222, 108, 164, 42], [222, 110, 164, 42, "_client"], [222, 117, 164, 42], [222, 120, 164, 52, "defaultedOptions"], [222, 136, 164, 68], [223, 8, 165, 8], [223, 9, 165, 9], [223, 10, 165, 10], [224, 6, 166, 6], [225, 4, 167, 4], [225, 5, 167, 5], [225, 6, 167, 6], [226, 4, 168, 4], [226, 11, 168, 11, "observers"], [226, 20, 168, 20], [227, 2, 169, 2], [228, 2, 169, 3], [228, 11, 169, 3, "_onUpdate2"], [228, 22, 170, 12, "observer"], [228, 30, 170, 20], [228, 32, 170, 22, "result"], [228, 38, 170, 28], [228, 40, 170, 30], [229, 4, 171, 4], [229, 8, 171, 10, "index"], [229, 13, 171, 15], [229, 16, 171, 18], [229, 20, 171, 18, "_classPrivateFieldLooseBase2"], [229, 48, 171, 18], [229, 49, 171, 18, "default"], [229, 56, 171, 18], [229, 62, 171, 22], [229, 64, 171, 22, "_observers"], [229, 74, 171, 22], [229, 76, 171, 22, "_observers"], [229, 86, 171, 22], [229, 88, 171, 34, "indexOf"], [229, 95, 171, 41], [229, 96, 171, 42, "observer"], [229, 104, 171, 50], [229, 105, 171, 51], [230, 4, 172, 4], [230, 8, 172, 8, "index"], [230, 13, 172, 13], [230, 18, 172, 18], [230, 19, 172, 19], [230, 20, 172, 20], [230, 22, 172, 22], [231, 6, 173, 6], [231, 10, 173, 6, "_classPrivateFieldLooseBase2"], [231, 38, 173, 6], [231, 39, 173, 6, "default"], [231, 46, 173, 6], [231, 52, 173, 10], [231, 54, 173, 10, "_result"], [231, 61, 173, 10], [231, 63, 173, 10, "_result"], [231, 70, 173, 10], [231, 74, 173, 21, "replaceAt"], [231, 83, 173, 30], [231, 88, 173, 30, "_classPrivateFieldLooseBase2"], [231, 116, 173, 30], [231, 117, 173, 30, "default"], [231, 124, 173, 30], [231, 126, 173, 31], [231, 130, 173, 35], [231, 132, 173, 35, "_result"], [231, 139, 173, 35], [231, 141, 173, 35, "_result"], [231, 148, 173, 35], [231, 151, 173, 45, "index"], [231, 156, 173, 50], [231, 158, 173, 52, "result"], [231, 164, 173, 58], [231, 165, 173, 59], [232, 6, 174, 6], [232, 10, 174, 6, "_classPrivateFieldLooseBase2"], [232, 38, 174, 6], [232, 39, 174, 6, "default"], [232, 46, 174, 6], [232, 52, 174, 10], [232, 54, 174, 10, "_notify"], [232, 61, 174, 10], [232, 63, 174, 10, "_notify"], [232, 70, 174, 10], [233, 4, 175, 4], [234, 2, 176, 2], [235, 2, 176, 3], [235, 11, 176, 3, "_notify2"], [235, 20, 176, 3], [235, 22, 177, 12], [236, 4, 178, 4], [236, 8, 178, 8], [236, 12, 178, 12], [236, 13, 178, 13, "hasListeners"], [236, 25, 178, 25], [236, 26, 178, 26], [236, 27, 178, 27], [236, 29, 178, 29], [237, 6, 179, 6], [237, 10, 179, 12, "previousResult"], [237, 24, 179, 26], [237, 31, 179, 26, "_classPrivateFieldLooseBase2"], [237, 59, 179, 26], [237, 60, 179, 26, "default"], [237, 67, 179, 26], [237, 69, 179, 29], [237, 73, 179, 33], [237, 75, 179, 33, "_combinedResult"], [237, 90, 179, 33], [237, 92, 179, 33, "_combinedResult"], [237, 107, 179, 33], [237, 108, 179, 49], [238, 6, 180, 6], [238, 10, 180, 12, "newTracked"], [238, 20, 180, 22], [238, 27, 180, 22, "_classPrivateFieldLooseBase2"], [238, 55, 180, 22], [238, 56, 180, 22, "default"], [238, 63, 180, 22], [238, 65, 180, 25], [238, 69, 180, 29], [238, 71, 180, 29, "_trackResult"], [238, 83, 180, 29], [238, 85, 180, 29, "_trackResult"], [238, 97, 180, 29], [238, 103, 180, 29, "_classPrivateFieldLooseBase2"], [238, 131, 180, 29], [238, 132, 180, 29, "default"], [238, 139, 180, 29], [238, 141, 180, 43], [238, 145, 180, 47], [238, 147, 180, 47, "_result"], [238, 154, 180, 47], [238, 156, 180, 47, "_result"], [238, 163, 180, 47], [238, 170, 180, 47, "_classPrivateFieldLooseBase2"], [238, 198, 180, 47], [238, 199, 180, 47, "default"], [238, 206, 180, 47], [238, 208, 180, 57], [238, 212, 180, 61], [238, 214, 180, 61, "_observerMatches"], [238, 230, 180, 61], [238, 232, 180, 61, "_observerMatches"], [238, 248, 180, 61], [238, 250, 180, 79], [239, 6, 181, 6], [239, 10, 181, 12, "newResult"], [239, 19, 181, 21], [239, 26, 181, 21, "_classPrivateFieldLooseBase2"], [239, 54, 181, 21], [239, 55, 181, 21, "default"], [239, 62, 181, 21], [239, 64, 181, 24], [239, 68, 181, 28], [239, 70, 181, 28, "_combineResult"], [239, 84, 181, 28], [239, 86, 181, 28, "_combineResult"], [239, 100, 181, 28], [239, 102, 181, 44, "newTracked"], [239, 112, 181, 54], [239, 114, 181, 56], [239, 118, 181, 56, "_classPrivateFieldLooseBase2"], [239, 146, 181, 56], [239, 147, 181, 56, "default"], [239, 154, 181, 56], [239, 160, 181, 60], [239, 162, 181, 60, "_options"], [239, 170, 181, 60], [239, 172, 181, 60, "_options"], [239, 180, 181, 60], [239, 183, 181, 71, "combine"], [239, 190, 181, 78], [239, 191, 181, 79], [240, 6, 182, 6], [240, 10, 182, 10, "previousResult"], [240, 24, 182, 24], [240, 29, 182, 29, "newResult"], [240, 38, 182, 38], [240, 40, 182, 40], [241, 8, 183, 8, "notify<PERSON><PERSON>ger"], [241, 36, 183, 21], [241, 37, 183, 22, "batch"], [241, 42, 183, 27], [241, 43, 183, 28], [241, 49, 183, 34], [242, 10, 184, 10], [242, 14, 184, 14], [242, 15, 184, 15, "listeners"], [242, 24, 184, 24], [242, 25, 184, 25, "for<PERSON>ach"], [242, 32, 184, 32], [242, 33, 184, 34, "listener"], [242, 41, 184, 42], [242, 45, 184, 47], [243, 12, 185, 12, "listener"], [243, 20, 185, 20], [243, 25, 185, 20, "_classPrivateFieldLooseBase2"], [243, 53, 185, 20], [243, 54, 185, 20, "default"], [243, 61, 185, 20], [243, 63, 185, 21], [243, 67, 185, 25], [243, 69, 185, 25, "_result"], [243, 76, 185, 25], [243, 78, 185, 25, "_result"], [243, 85, 185, 25], [243, 86, 185, 33], [243, 87, 185, 34], [244, 10, 186, 10], [244, 11, 186, 11], [244, 12, 186, 12], [245, 8, 187, 8], [245, 9, 187, 9], [245, 10, 187, 10], [246, 6, 188, 6], [247, 4, 189, 4], [248, 2, 190, 2], [249, 0, 190, 3], [249, 3]], "functionMap": {"names": ["<global>", "difference", "array1.filter$argument_0", "replaceAt", "QueriesObserver", "QueriesObserver#constructor", "QueriesObserver#onSubscribe", "forEach$argument_0", "observer.subscribe$argument_0", "QueriesObserver#onUnsubscribe", "QueriesObserver#destroy", "QueriesObserver#setQueries", "queries.map$argument_0", "notifyManager.batch$argument_0", "newObserverMatches.forEach$argument_0", "newObserverMatches.map$argument_0", "newObservers.map$argument_0", "newObservers.some$argument_0", "difference.forEach$argument_0", "QueriesObserver#getCurrentResult", "QueriesObserver#getQueries", "map$argument_0", "QueriesObserver#getObservers", "QueriesObserver#getOptimisticResult", "matches.map$argument_0", "<anonymous>", "match.observer.trackResult$argument_1", "matches.forEach$argument_0", "queries.forEach$argument_0", "listeners.forEach$argument_0"], "mappings": "AAA;ACK;uBCE,yBD;CDC;AGC;CHI;sBIC;ECU;GDQ;EEC;8BCE;2BCC;SDE;ODC;GFE;EKC;GLI;EMC;4BHE;KGE;GNC;EOC;QCK,4DD;wBEQ;QCK,iED;kDEE,yBF;QGE,yCH;QIG,sDJ;sDKU;OLE;sDKC;2BVC;SUE;OLC;KFE;GPC;EeC;GfE;EgBC;+BCC,wCD;GhBC;EkBC;GlBE;EmBC;MCG,0ED;MEI;OFE;MEC;OFE;GnBE;EqBC;uBDC;2GEE;wBCC;SDE;OFC;KCC;GrBC;EqBC;GrBa;EqBC;0BJE,oDI;oBGG;KHc;GrBE;EqBC;GrBM;EqBC;4BZM;iCgBC;WhBE;SYC;GrBG;CJC"}}, "type": "js/module"}]}