{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const SquareRadical = exports.default = (0, _createLucideIcon.default)(\"SquareRadical\", [[\"path\", {\n    d: \"M7 12h2l2 5 2-10h4\",\n    key: \"1fxv6h\"\n  }], [\"rect\", {\n    x: \"3\",\n    y: \"3\",\n    width: \"18\",\n    height: \"18\",\n    rx: \"2\",\n    key: \"h1oib\"\n  }]]);\n});", "lineCount": 26, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "SquareRadical"], [15, 21, 10, 19], [15, 24, 10, 19, "exports"], [15, 31, 10, 19], [15, 32, 10, 19, "default"], [15, 39, 10, 19], [15, 42, 10, 22], [15, 46, 10, 22, "createLucideIcon"], [15, 71, 10, 38], [15, 73, 10, 39], [15, 88, 10, 54], [15, 90, 10, 56], [15, 91, 11, 2], [15, 92, 11, 3], [15, 98, 11, 9], [15, 100, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 27, 11, 36], [17, 4, 11, 38, "key"], [17, 7, 11, 41], [17, 9, 11, 43], [18, 2, 11, 52], [18, 3, 11, 53], [18, 4, 11, 54], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "x"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 10, 12, 19], [20, 4, 12, 21, "y"], [20, 5, 12, 22], [20, 7, 12, 24], [20, 10, 12, 27], [21, 4, 12, 29, "width"], [21, 9, 12, 34], [21, 11, 12, 36], [21, 15, 12, 40], [22, 4, 12, 42, "height"], [22, 10, 12, 48], [22, 12, 12, 50], [22, 16, 12, 54], [23, 4, 12, 56, "rx"], [23, 6, 12, 58], [23, 8, 12, 60], [23, 11, 12, 63], [24, 4, 12, 65, "key"], [24, 7, 12, 68], [24, 9, 12, 70], [25, 2, 12, 78], [25, 3, 12, 79], [25, 4, 12, 80], [25, 5, 13, 1], [25, 6, 13, 2], [26, 0, 13, 3], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}