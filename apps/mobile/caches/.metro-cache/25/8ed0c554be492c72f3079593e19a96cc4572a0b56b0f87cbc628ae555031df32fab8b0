{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _isNativeFunction(t) {\n    try {\n      return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n    } catch (n) {\n      return \"function\" == typeof t;\n    }\n  }\n  module.exports = _isNativeFunction, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_isNativeFunction"], [2, 28, 1, 26, "_isNativeFunction"], [2, 29, 1, 27, "t"], [2, 30, 1, 28], [2, 32, 1, 30], [3, 4, 2, 2], [3, 8, 2, 6], [4, 6, 3, 4], [4, 13, 3, 11], [4, 14, 3, 12], [4, 15, 3, 13], [4, 20, 3, 18, "Function"], [4, 28, 3, 26], [4, 29, 3, 27, "toString"], [4, 37, 3, 35], [4, 38, 3, 36, "call"], [4, 42, 3, 40], [4, 43, 3, 41, "t"], [4, 44, 3, 42], [4, 45, 3, 43], [4, 46, 3, 44, "indexOf"], [4, 53, 3, 51], [4, 54, 3, 52], [4, 69, 3, 67], [4, 70, 3, 68], [5, 4, 4, 2], [5, 5, 4, 3], [5, 6, 4, 4], [5, 13, 4, 11, "n"], [5, 14, 4, 12], [5, 16, 4, 14], [6, 6, 5, 4], [6, 13, 5, 11], [6, 23, 5, 21], [6, 27, 5, 25], [6, 34, 5, 32, "t"], [6, 35, 5, 33], [7, 4, 6, 2], [8, 2, 7, 0], [9, 2, 8, 0, "module"], [9, 8, 8, 6], [9, 9, 8, 7, "exports"], [9, 16, 8, 14], [9, 19, 8, 17, "_isNativeFunction"], [9, 36, 8, 34], [9, 38, 8, 36, "module"], [9, 44, 8, 42], [9, 45, 8, 43, "exports"], [9, 52, 8, 50], [9, 53, 8, 51, "__esModule"], [9, 63, 8, 61], [9, 66, 8, 64], [9, 70, 8, 68], [9, 72, 8, 70, "module"], [9, 78, 8, 76], [9, 79, 8, 77, "exports"], [9, 86, 8, 84], [9, 87, 8, 85], [9, 96, 8, 94], [9, 97, 8, 95], [9, 100, 8, 98, "module"], [9, 106, 8, 104], [9, 107, 8, 105, "exports"], [9, 114, 8, 112], [10, 0, 8, 113], [10, 3]], "functionMap": {"names": ["_isNativeFunction", "<global>"], "mappings": "AAA;CCM"}}, "type": "js/module"}]}