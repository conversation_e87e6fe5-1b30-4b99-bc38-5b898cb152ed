{"dependencies": [{"name": "../../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 50, "index": 65}}], "key": "Jq1DcLPs1AjY3ygtzPUe4D8IdoQ=", "exportNames": ["*"]}}, {"name": "../../logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 66}, "end": {"line": 4, "column": 47, "index": 113}}], "key": "6SNP0qYc6Dvb4y6pRCC6IV2Z4aU=", "exportNames": ["*"]}}, {"name": "../../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 114}, "end": {"line": 5, "column": 61, "index": 175}}], "key": "gUqK/inccYjUWjFbxEcDL+F40JA=", "exportNames": ["*"]}}, {"name": "./componentStyle.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 176}, "end": {"line": 6, "column": 68, "index": 244}}], "key": "JxDOem4+XEd8jLROs+J4XKes0Sc=", "exportNames": ["*"]}}, {"name": "./config.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 245}, "end": {"line": 7, "column": 41, "index": 286}}], "key": "bUPXFgGH+XQHosI76NH2QbmMaAI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.addHTMLMutationObserver = addHTMLMutationObserver;\n  exports.areDOMRectsEqual = areDOMRectsEqual;\n  exports.configureWebLayoutAnimations = configureWebLayoutAnimations;\n  exports.insertWebAnimation = insertWebAnimation;\n  exports.scheduleAnimationCleanup = scheduleAnimationCleanup;\n  var _errors = require(_dependencyMap[0], \"../../errors.js\");\n  var _index = require(_dependencyMap[1], \"../../logger/index.js\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../../PlatformChecker.js\");\n  var _componentStyle = require(_dependencyMap[3], \"./componentStyle.js\");\n  var _config = require(_dependencyMap[4], \"./config.js\");\n  const PREDEFINED_WEB_ANIMATIONS_ID = 'ReanimatedPredefinedWebAnimationsStyle';\n  const CUSTOM_WEB_ANIMATIONS_ID = 'ReanimatedCustomWebAnimationsStyle';\n\n  // Since we cannot remove keyframe from DOM by its name, we have to store its id\n  const animationNameToIndex = new Map();\n  const animationNameList = [];\n  let isObserverSet = false;\n\n  /**\n   * Creates `HTMLStyleElement`, inserts it into DOM and then inserts CSS rules\n   * into the stylesheet. If style element already exists, nothing happens.\n   */\n  function configureWebLayoutAnimations() {\n    if (!(0, _PlatformChecker.isWindowAvailable)() ||\n    // Without this check SSR crashes because document is undefined (NextExample on CI)\n    document.getElementById(PREDEFINED_WEB_ANIMATIONS_ID) !== null) {\n      return;\n    }\n    const predefinedAnimationsStyleTag = document.createElement('style');\n    predefinedAnimationsStyleTag.id = PREDEFINED_WEB_ANIMATIONS_ID;\n    predefinedAnimationsStyleTag.onload = () => {\n      if (!predefinedAnimationsStyleTag.sheet) {\n        _index.logger.error('Failed to create layout animations stylesheet.');\n        return;\n      }\n      for (const animationName in _config.Animations) {\n        predefinedAnimationsStyleTag.sheet.insertRule(_config.Animations[animationName].style);\n      }\n    };\n    const customAnimationsStyleTag = document.createElement('style');\n    customAnimationsStyleTag.id = CUSTOM_WEB_ANIMATIONS_ID;\n    document.head.appendChild(predefinedAnimationsStyleTag);\n    document.head.appendChild(customAnimationsStyleTag);\n  }\n  function insertWebAnimation(animationName, keyframe) {\n    // Without this check SSR crashes because document is undefined (NextExample on CI)\n    if (!(0, _PlatformChecker.isWindowAvailable)()) {\n      return;\n    }\n    const styleTag = document.getElementById(CUSTOM_WEB_ANIMATIONS_ID);\n    if (!styleTag.sheet) {\n      _index.logger.error('Failed to create layout animations stylesheet.');\n      return;\n    }\n    styleTag.sheet.insertRule(keyframe, 0);\n    animationNameList.unshift(animationName);\n    animationNameToIndex.set(animationName, 0);\n    for (let i = 1; i < animationNameList.length; ++i) {\n      const nextAnimationName = animationNameList[i];\n      const nextAnimationIndex = animationNameToIndex.get(nextAnimationName);\n      if (nextAnimationIndex === undefined) {\n        throw new _errors.ReanimatedError('Failed to obtain animation index.');\n      }\n      animationNameToIndex.set(animationNameList[i], nextAnimationIndex + 1);\n    }\n  }\n  function removeWebAnimation(animationName, animationRemoveCallback) {\n    // Without this check SSR crashes because document is undefined (NextExample on CI)\n    if (!(0, _PlatformChecker.isWindowAvailable)()) {\n      return;\n    }\n    const styleTag = document.getElementById(CUSTOM_WEB_ANIMATIONS_ID);\n    const currentAnimationIndex = animationNameToIndex.get(animationName);\n    if (currentAnimationIndex === undefined) {\n      throw new _errors.ReanimatedError('Failed to obtain animation index.');\n    }\n    animationRemoveCallback();\n    styleTag.sheet?.deleteRule(currentAnimationIndex);\n    animationNameList.splice(currentAnimationIndex, 1);\n    animationNameToIndex.delete(animationName);\n    for (let i = currentAnimationIndex; i < animationNameList.length; ++i) {\n      const nextAnimationName = animationNameList[i];\n      const nextAnimationIndex = animationNameToIndex.get(nextAnimationName);\n      if (nextAnimationIndex === undefined) {\n        throw new _errors.ReanimatedError('Failed to obtain animation index.');\n      }\n      animationNameToIndex.set(animationNameList[i], nextAnimationIndex - 1);\n    }\n  }\n  const timeoutScale = 1.25; // We use this value to enlarge timeout duration. It can prove useful if animation lags.\n  const frameDurationMs = 16; // Just an approximation.\n  const minimumFrames = 10;\n  function scheduleAnimationCleanup(animationName, animationDuration, animationRemoveCallback) {\n    // If duration is very short, we want to keep remove delay to at least 10 frames\n    // In our case it is exactly 160/1099 s, which is approximately 0.15s\n    const timeoutValue = Math.max(animationDuration * timeoutScale * 1000, animationDuration + frameDurationMs * minimumFrames);\n    setTimeout(() => removeWebAnimation(animationName, animationRemoveCallback), timeoutValue);\n  }\n  function reattachElementToAncestor(child, parent) {\n    const childSnapshot = _componentStyle.snapshots.get(child);\n    if (!childSnapshot) {\n      _index.logger.error('Failed to obtain snapshot.');\n      return;\n    }\n\n    // We use that so we don't end up in infinite loop\n    child.removedAfterAnimation = true;\n    parent.appendChild(child);\n    (0, _componentStyle.setElementPosition)(child, childSnapshot);\n    const originalOnAnimationEnd = child.onanimationend;\n    child.onanimationend = function (event) {\n      parent.removeChild(child);\n\n      // Given that this function overrides onAnimationEnd, it won't be null\n      originalOnAnimationEnd?.call(this, event);\n    };\n  }\n  function findDescendantWithExitingAnimation(node, root) {\n    // Node could be something else than HTMLElement, for example TextNode (treated as plain text, not as HTML object),\n    // therefore it won't have children prop and calling Array.from(node.children) will cause error.\n    if (!(node instanceof HTMLElement)) {\n      return;\n    }\n    if (node.reanimatedDummy && node.removedAfterAnimation === undefined) {\n      reattachElementToAncestor(node, root);\n    }\n    const children = Array.from(node.children);\n    for (let i = 0; i < children.length; ++i) {\n      findDescendantWithExitingAnimation(children[i], root);\n    }\n  }\n  function checkIfScreenWasChanged(mutationTarget) {\n    let reactFiberKey = '__reactFiber';\n    for (const key of Object.keys(mutationTarget)) {\n      if (key.startsWith('__reactFiber')) {\n        reactFiberKey = key;\n        break;\n      }\n    }\n    return mutationTarget[reactFiberKey]?.child?.memoizedProps?.navigation !== undefined;\n  }\n  function addHTMLMutationObserver() {\n    if (isObserverSet || !(0, _PlatformChecker.isWindowAvailable)()) {\n      return;\n    }\n    isObserverSet = true;\n    const observer = new MutationObserver(mutationsList => {\n      const rootMutation = mutationsList[mutationsList.length - 1];\n      if (checkIfScreenWasChanged(rootMutation.target)) {\n        return;\n      }\n      for (let i = 0; i < rootMutation.removedNodes.length; ++i) {\n        findDescendantWithExitingAnimation(rootMutation.removedNodes[i], rootMutation.target);\n      }\n    });\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n  }\n  function areDOMRectsEqual(r1, r2) {\n    // There are 4 more fields, but checking these should suffice\n    return r1.x === r2.x && r1.y === r2.y && r1.width === r2.width && r1.height === r2.height;\n  }\n});", "lineCount": 171, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "addHTMLMutationObserver"], [7, 33, 1, 13], [7, 36, 1, 13, "addHTMLMutationObserver"], [7, 59, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "areDOMRectsEqual"], [8, 26, 1, 13], [8, 29, 1, 13, "areDOMRectsEqual"], [8, 45, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "configureWebLayoutAnimations"], [9, 38, 1, 13], [9, 41, 1, 13, "configureWebLayoutAnimations"], [9, 69, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "insertWebAnimation"], [10, 28, 1, 13], [10, 31, 1, 13, "insertWebAnimation"], [10, 49, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "scheduleAnimationCleanup"], [11, 34, 1, 13], [11, 37, 1, 13, "scheduleAnimationCleanup"], [11, 61, 1, 13], [12, 2, 3, 0], [12, 6, 3, 0, "_errors"], [12, 13, 3, 0], [12, 16, 3, 0, "require"], [12, 23, 3, 0], [12, 24, 3, 0, "_dependencyMap"], [12, 38, 3, 0], [13, 2, 4, 0], [13, 6, 4, 0, "_index"], [13, 12, 4, 0], [13, 15, 4, 0, "require"], [13, 22, 4, 0], [13, 23, 4, 0, "_dependencyMap"], [13, 37, 4, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_PlatformChecker"], [14, 22, 5, 0], [14, 25, 5, 0, "require"], [14, 32, 5, 0], [14, 33, 5, 0, "_dependencyMap"], [14, 47, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_componentStyle"], [15, 21, 6, 0], [15, 24, 6, 0, "require"], [15, 31, 6, 0], [15, 32, 6, 0, "_dependencyMap"], [15, 46, 6, 0], [16, 2, 7, 0], [16, 6, 7, 0, "_config"], [16, 13, 7, 0], [16, 16, 7, 0, "require"], [16, 23, 7, 0], [16, 24, 7, 0, "_dependencyMap"], [16, 38, 7, 0], [17, 2, 8, 0], [17, 8, 8, 6, "PREDEFINED_WEB_ANIMATIONS_ID"], [17, 36, 8, 34], [17, 39, 8, 37], [17, 79, 8, 77], [18, 2, 9, 0], [18, 8, 9, 6, "CUSTOM_WEB_ANIMATIONS_ID"], [18, 32, 9, 30], [18, 35, 9, 33], [18, 71, 9, 69], [20, 2, 11, 0], [21, 2, 12, 0], [21, 8, 12, 6, "animationNameToIndex"], [21, 28, 12, 26], [21, 31, 12, 29], [21, 35, 12, 33, "Map"], [21, 38, 12, 36], [21, 39, 12, 37], [21, 40, 12, 38], [22, 2, 13, 0], [22, 8, 13, 6, "animationNameList"], [22, 25, 13, 23], [22, 28, 13, 26], [22, 30, 13, 28], [23, 2, 14, 0], [23, 6, 14, 4, "isObserverSet"], [23, 19, 14, 17], [23, 22, 14, 20], [23, 27, 14, 25], [25, 2, 16, 0], [26, 0, 17, 0], [27, 0, 18, 0], [28, 0, 19, 0], [29, 2, 20, 7], [29, 11, 20, 16, "configureWebLayoutAnimations"], [29, 39, 20, 44, "configureWebLayoutAnimations"], [29, 40, 20, 44], [29, 42, 20, 47], [30, 4, 21, 2], [30, 8, 21, 6], [30, 9, 21, 7], [30, 13, 21, 7, "isWindowAvailable"], [30, 47, 21, 24], [30, 49, 21, 25], [30, 50, 21, 26], [31, 4, 22, 2], [32, 4, 23, 2, "document"], [32, 12, 23, 10], [32, 13, 23, 11, "getElementById"], [32, 27, 23, 25], [32, 28, 23, 26, "PREDEFINED_WEB_ANIMATIONS_ID"], [32, 56, 23, 54], [32, 57, 23, 55], [32, 62, 23, 60], [32, 66, 23, 64], [32, 68, 23, 66], [33, 6, 24, 4], [34, 4, 25, 2], [35, 4, 26, 2], [35, 10, 26, 8, "predefinedAnimationsStyleTag"], [35, 38, 26, 36], [35, 41, 26, 39, "document"], [35, 49, 26, 47], [35, 50, 26, 48, "createElement"], [35, 63, 26, 61], [35, 64, 26, 62], [35, 71, 26, 69], [35, 72, 26, 70], [36, 4, 27, 2, "predefinedAnimationsStyleTag"], [36, 32, 27, 30], [36, 33, 27, 31, "id"], [36, 35, 27, 33], [36, 38, 27, 36, "PREDEFINED_WEB_ANIMATIONS_ID"], [36, 66, 27, 64], [37, 4, 28, 2, "predefinedAnimationsStyleTag"], [37, 32, 28, 30], [37, 33, 28, 31, "onload"], [37, 39, 28, 37], [37, 42, 28, 40], [37, 48, 28, 46], [38, 6, 29, 4], [38, 10, 29, 8], [38, 11, 29, 9, "predefinedAnimationsStyleTag"], [38, 39, 29, 37], [38, 40, 29, 38, "sheet"], [38, 45, 29, 43], [38, 47, 29, 45], [39, 8, 30, 6, "logger"], [39, 21, 30, 12], [39, 22, 30, 13, "error"], [39, 27, 30, 18], [39, 28, 30, 19], [39, 76, 30, 67], [39, 77, 30, 68], [40, 8, 31, 6], [41, 6, 32, 4], [42, 6, 33, 4], [42, 11, 33, 9], [42, 17, 33, 15, "animationName"], [42, 30, 33, 28], [42, 34, 33, 32, "Animations"], [42, 52, 33, 42], [42, 54, 33, 44], [43, 8, 34, 6, "predefinedAnimationsStyleTag"], [43, 36, 34, 34], [43, 37, 34, 35, "sheet"], [43, 42, 34, 40], [43, 43, 34, 41, "insertRule"], [43, 53, 34, 51], [43, 54, 34, 52, "Animations"], [43, 72, 34, 62], [43, 73, 34, 63, "animationName"], [43, 86, 34, 76], [43, 87, 34, 77], [43, 88, 34, 78, "style"], [43, 93, 34, 83], [43, 94, 34, 84], [44, 6, 35, 4], [45, 4, 36, 2], [45, 5, 36, 3], [46, 4, 37, 2], [46, 10, 37, 8, "customAnimationsStyleTag"], [46, 34, 37, 32], [46, 37, 37, 35, "document"], [46, 45, 37, 43], [46, 46, 37, 44, "createElement"], [46, 59, 37, 57], [46, 60, 37, 58], [46, 67, 37, 65], [46, 68, 37, 66], [47, 4, 38, 2, "customAnimationsStyleTag"], [47, 28, 38, 26], [47, 29, 38, 27, "id"], [47, 31, 38, 29], [47, 34, 38, 32, "CUSTOM_WEB_ANIMATIONS_ID"], [47, 58, 38, 56], [48, 4, 39, 2, "document"], [48, 12, 39, 10], [48, 13, 39, 11, "head"], [48, 17, 39, 15], [48, 18, 39, 16, "append<PERSON><PERSON><PERSON>"], [48, 29, 39, 27], [48, 30, 39, 28, "predefinedAnimationsStyleTag"], [48, 58, 39, 56], [48, 59, 39, 57], [49, 4, 40, 2, "document"], [49, 12, 40, 10], [49, 13, 40, 11, "head"], [49, 17, 40, 15], [49, 18, 40, 16, "append<PERSON><PERSON><PERSON>"], [49, 29, 40, 27], [49, 30, 40, 28, "customAnimationsStyleTag"], [49, 54, 40, 52], [49, 55, 40, 53], [50, 2, 41, 0], [51, 2, 42, 7], [51, 11, 42, 16, "insertWebAnimation"], [51, 29, 42, 34, "insertWebAnimation"], [51, 30, 42, 35, "animationName"], [51, 43, 42, 48], [51, 45, 42, 50, "keyframe"], [51, 53, 42, 58], [51, 55, 42, 60], [52, 4, 43, 2], [53, 4, 44, 2], [53, 8, 44, 6], [53, 9, 44, 7], [53, 13, 44, 7, "isWindowAvailable"], [53, 47, 44, 24], [53, 49, 44, 25], [53, 50, 44, 26], [53, 52, 44, 28], [54, 6, 45, 4], [55, 4, 46, 2], [56, 4, 47, 2], [56, 10, 47, 8, "styleTag"], [56, 18, 47, 16], [56, 21, 47, 19, "document"], [56, 29, 47, 27], [56, 30, 47, 28, "getElementById"], [56, 44, 47, 42], [56, 45, 47, 43, "CUSTOM_WEB_ANIMATIONS_ID"], [56, 69, 47, 67], [56, 70, 47, 68], [57, 4, 48, 2], [57, 8, 48, 6], [57, 9, 48, 7, "styleTag"], [57, 17, 48, 15], [57, 18, 48, 16, "sheet"], [57, 23, 48, 21], [57, 25, 48, 23], [58, 6, 49, 4, "logger"], [58, 19, 49, 10], [58, 20, 49, 11, "error"], [58, 25, 49, 16], [58, 26, 49, 17], [58, 74, 49, 65], [58, 75, 49, 66], [59, 6, 50, 4], [60, 4, 51, 2], [61, 4, 52, 2, "styleTag"], [61, 12, 52, 10], [61, 13, 52, 11, "sheet"], [61, 18, 52, 16], [61, 19, 52, 17, "insertRule"], [61, 29, 52, 27], [61, 30, 52, 28, "keyframe"], [61, 38, 52, 36], [61, 40, 52, 38], [61, 41, 52, 39], [61, 42, 52, 40], [62, 4, 53, 2, "animationNameList"], [62, 21, 53, 19], [62, 22, 53, 20, "unshift"], [62, 29, 53, 27], [62, 30, 53, 28, "animationName"], [62, 43, 53, 41], [62, 44, 53, 42], [63, 4, 54, 2, "animationNameToIndex"], [63, 24, 54, 22], [63, 25, 54, 23, "set"], [63, 28, 54, 26], [63, 29, 54, 27, "animationName"], [63, 42, 54, 40], [63, 44, 54, 42], [63, 45, 54, 43], [63, 46, 54, 44], [64, 4, 55, 2], [64, 9, 55, 7], [64, 13, 55, 11, "i"], [64, 14, 55, 12], [64, 17, 55, 15], [64, 18, 55, 16], [64, 20, 55, 18, "i"], [64, 21, 55, 19], [64, 24, 55, 22, "animationNameList"], [64, 41, 55, 39], [64, 42, 55, 40, "length"], [64, 48, 55, 46], [64, 50, 55, 48], [64, 52, 55, 50, "i"], [64, 53, 55, 51], [64, 55, 55, 53], [65, 6, 56, 4], [65, 12, 56, 10, "nextAnimationName"], [65, 29, 56, 27], [65, 32, 56, 30, "animationNameList"], [65, 49, 56, 47], [65, 50, 56, 48, "i"], [65, 51, 56, 49], [65, 52, 56, 50], [66, 6, 57, 4], [66, 12, 57, 10, "nextAnimationIndex"], [66, 30, 57, 28], [66, 33, 57, 31, "animationNameToIndex"], [66, 53, 57, 51], [66, 54, 57, 52, "get"], [66, 57, 57, 55], [66, 58, 57, 56, "nextAnimationName"], [66, 75, 57, 73], [66, 76, 57, 74], [67, 6, 58, 4], [67, 10, 58, 8, "nextAnimationIndex"], [67, 28, 58, 26], [67, 33, 58, 31, "undefined"], [67, 42, 58, 40], [67, 44, 58, 42], [68, 8, 59, 6], [68, 14, 59, 12], [68, 18, 59, 16, "ReanimatedError"], [68, 41, 59, 31], [68, 42, 59, 32], [68, 77, 59, 67], [68, 78, 59, 68], [69, 6, 60, 4], [70, 6, 61, 4, "animationNameToIndex"], [70, 26, 61, 24], [70, 27, 61, 25, "set"], [70, 30, 61, 28], [70, 31, 61, 29, "animationNameList"], [70, 48, 61, 46], [70, 49, 61, 47, "i"], [70, 50, 61, 48], [70, 51, 61, 49], [70, 53, 61, 51, "nextAnimationIndex"], [70, 71, 61, 69], [70, 74, 61, 72], [70, 75, 61, 73], [70, 76, 61, 74], [71, 4, 62, 2], [72, 2, 63, 0], [73, 2, 64, 0], [73, 11, 64, 9, "removeWebAnimation"], [73, 29, 64, 27, "removeWebAnimation"], [73, 30, 64, 28, "animationName"], [73, 43, 64, 41], [73, 45, 64, 43, "animationRemoveCallback"], [73, 68, 64, 66], [73, 70, 64, 68], [74, 4, 65, 2], [75, 4, 66, 2], [75, 8, 66, 6], [75, 9, 66, 7], [75, 13, 66, 7, "isWindowAvailable"], [75, 47, 66, 24], [75, 49, 66, 25], [75, 50, 66, 26], [75, 52, 66, 28], [76, 6, 67, 4], [77, 4, 68, 2], [78, 4, 69, 2], [78, 10, 69, 8, "styleTag"], [78, 18, 69, 16], [78, 21, 69, 19, "document"], [78, 29, 69, 27], [78, 30, 69, 28, "getElementById"], [78, 44, 69, 42], [78, 45, 69, 43, "CUSTOM_WEB_ANIMATIONS_ID"], [78, 69, 69, 67], [78, 70, 69, 68], [79, 4, 70, 2], [79, 10, 70, 8, "currentAnimationIndex"], [79, 31, 70, 29], [79, 34, 70, 32, "animationNameToIndex"], [79, 54, 70, 52], [79, 55, 70, 53, "get"], [79, 58, 70, 56], [79, 59, 70, 57, "animationName"], [79, 72, 70, 70], [79, 73, 70, 71], [80, 4, 71, 2], [80, 8, 71, 6, "currentAnimationIndex"], [80, 29, 71, 27], [80, 34, 71, 32, "undefined"], [80, 43, 71, 41], [80, 45, 71, 43], [81, 6, 72, 4], [81, 12, 72, 10], [81, 16, 72, 14, "ReanimatedError"], [81, 39, 72, 29], [81, 40, 72, 30], [81, 75, 72, 65], [81, 76, 72, 66], [82, 4, 73, 2], [83, 4, 74, 2, "animationRemoveCallback"], [83, 27, 74, 25], [83, 28, 74, 26], [83, 29, 74, 27], [84, 4, 75, 2, "styleTag"], [84, 12, 75, 10], [84, 13, 75, 11, "sheet"], [84, 18, 75, 16], [84, 20, 75, 18, "deleteRule"], [84, 30, 75, 28], [84, 31, 75, 29, "currentAnimationIndex"], [84, 52, 75, 50], [84, 53, 75, 51], [85, 4, 76, 2, "animationNameList"], [85, 21, 76, 19], [85, 22, 76, 20, "splice"], [85, 28, 76, 26], [85, 29, 76, 27, "currentAnimationIndex"], [85, 50, 76, 48], [85, 52, 76, 50], [85, 53, 76, 51], [85, 54, 76, 52], [86, 4, 77, 2, "animationNameToIndex"], [86, 24, 77, 22], [86, 25, 77, 23, "delete"], [86, 31, 77, 29], [86, 32, 77, 30, "animationName"], [86, 45, 77, 43], [86, 46, 77, 44], [87, 4, 78, 2], [87, 9, 78, 7], [87, 13, 78, 11, "i"], [87, 14, 78, 12], [87, 17, 78, 15, "currentAnimationIndex"], [87, 38, 78, 36], [87, 40, 78, 38, "i"], [87, 41, 78, 39], [87, 44, 78, 42, "animationNameList"], [87, 61, 78, 59], [87, 62, 78, 60, "length"], [87, 68, 78, 66], [87, 70, 78, 68], [87, 72, 78, 70, "i"], [87, 73, 78, 71], [87, 75, 78, 73], [88, 6, 79, 4], [88, 12, 79, 10, "nextAnimationName"], [88, 29, 79, 27], [88, 32, 79, 30, "animationNameList"], [88, 49, 79, 47], [88, 50, 79, 48, "i"], [88, 51, 79, 49], [88, 52, 79, 50], [89, 6, 80, 4], [89, 12, 80, 10, "nextAnimationIndex"], [89, 30, 80, 28], [89, 33, 80, 31, "animationNameToIndex"], [89, 53, 80, 51], [89, 54, 80, 52, "get"], [89, 57, 80, 55], [89, 58, 80, 56, "nextAnimationName"], [89, 75, 80, 73], [89, 76, 80, 74], [90, 6, 81, 4], [90, 10, 81, 8, "nextAnimationIndex"], [90, 28, 81, 26], [90, 33, 81, 31, "undefined"], [90, 42, 81, 40], [90, 44, 81, 42], [91, 8, 82, 6], [91, 14, 82, 12], [91, 18, 82, 16, "ReanimatedError"], [91, 41, 82, 31], [91, 42, 82, 32], [91, 77, 82, 67], [91, 78, 82, 68], [92, 6, 83, 4], [93, 6, 84, 4, "animationNameToIndex"], [93, 26, 84, 24], [93, 27, 84, 25, "set"], [93, 30, 84, 28], [93, 31, 84, 29, "animationNameList"], [93, 48, 84, 46], [93, 49, 84, 47, "i"], [93, 50, 84, 48], [93, 51, 84, 49], [93, 53, 84, 51, "nextAnimationIndex"], [93, 71, 84, 69], [93, 74, 84, 72], [93, 75, 84, 73], [93, 76, 84, 74], [94, 4, 85, 2], [95, 2, 86, 0], [96, 2, 87, 0], [96, 8, 87, 6, "timeoutScale"], [96, 20, 87, 18], [96, 23, 87, 21], [96, 27, 87, 25], [96, 28, 87, 26], [96, 29, 87, 27], [97, 2, 88, 0], [97, 8, 88, 6, "frameDurationMs"], [97, 23, 88, 21], [97, 26, 88, 24], [97, 28, 88, 26], [97, 29, 88, 27], [97, 30, 88, 28], [98, 2, 89, 0], [98, 8, 89, 6, "minimumFrames"], [98, 21, 89, 19], [98, 24, 89, 22], [98, 26, 89, 24], [99, 2, 90, 7], [99, 11, 90, 16, "scheduleAnimationCleanup"], [99, 35, 90, 40, "scheduleAnimationCleanup"], [99, 36, 90, 41, "animationName"], [99, 49, 90, 54], [99, 51, 90, 56, "animationDuration"], [99, 68, 90, 73], [99, 70, 90, 75, "animationRemoveCallback"], [99, 93, 90, 98], [99, 95, 90, 100], [100, 4, 91, 2], [101, 4, 92, 2], [102, 4, 93, 2], [102, 10, 93, 8, "timeoutValue"], [102, 22, 93, 20], [102, 25, 93, 23, "Math"], [102, 29, 93, 27], [102, 30, 93, 28, "max"], [102, 33, 93, 31], [102, 34, 93, 32, "animationDuration"], [102, 51, 93, 49], [102, 54, 93, 52, "timeoutScale"], [102, 66, 93, 64], [102, 69, 93, 67], [102, 73, 93, 71], [102, 75, 93, 73, "animationDuration"], [102, 92, 93, 90], [102, 95, 93, 93, "frameDurationMs"], [102, 110, 93, 108], [102, 113, 93, 111, "minimumFrames"], [102, 126, 93, 124], [102, 127, 93, 125], [103, 4, 94, 2, "setTimeout"], [103, 14, 94, 12], [103, 15, 94, 13], [103, 21, 94, 19, "removeWebAnimation"], [103, 39, 94, 37], [103, 40, 94, 38, "animationName"], [103, 53, 94, 51], [103, 55, 94, 53, "animationRemoveCallback"], [103, 78, 94, 76], [103, 79, 94, 77], [103, 81, 94, 79, "timeoutValue"], [103, 93, 94, 91], [103, 94, 94, 92], [104, 2, 95, 0], [105, 2, 96, 0], [105, 11, 96, 9, "reattachElementToAncestor"], [105, 36, 96, 34, "reattachElementToAncestor"], [105, 37, 96, 35, "child"], [105, 42, 96, 40], [105, 44, 96, 42, "parent"], [105, 50, 96, 48], [105, 52, 96, 50], [106, 4, 97, 2], [106, 10, 97, 8, "childSnapshot"], [106, 23, 97, 21], [106, 26, 97, 24, "snapshots"], [106, 51, 97, 33], [106, 52, 97, 34, "get"], [106, 55, 97, 37], [106, 56, 97, 38, "child"], [106, 61, 97, 43], [106, 62, 97, 44], [107, 4, 98, 2], [107, 8, 98, 6], [107, 9, 98, 7, "childSnapshot"], [107, 22, 98, 20], [107, 24, 98, 22], [108, 6, 99, 4, "logger"], [108, 19, 99, 10], [108, 20, 99, 11, "error"], [108, 25, 99, 16], [108, 26, 99, 17], [108, 54, 99, 45], [108, 55, 99, 46], [109, 6, 100, 4], [110, 4, 101, 2], [112, 4, 103, 2], [113, 4, 104, 2, "child"], [113, 9, 104, 7], [113, 10, 104, 8, "removedAfterAnimation"], [113, 31, 104, 29], [113, 34, 104, 32], [113, 38, 104, 36], [114, 4, 105, 2, "parent"], [114, 10, 105, 8], [114, 11, 105, 9, "append<PERSON><PERSON><PERSON>"], [114, 22, 105, 20], [114, 23, 105, 21, "child"], [114, 28, 105, 26], [114, 29, 105, 27], [115, 4, 106, 2], [115, 8, 106, 2, "setElementPosition"], [115, 42, 106, 20], [115, 44, 106, 21, "child"], [115, 49, 106, 26], [115, 51, 106, 28, "childSnapshot"], [115, 64, 106, 41], [115, 65, 106, 42], [116, 4, 107, 2], [116, 10, 107, 8, "originalOnAnimationEnd"], [116, 32, 107, 30], [116, 35, 107, 33, "child"], [116, 40, 107, 38], [116, 41, 107, 39, "onanimationend"], [116, 55, 107, 53], [117, 4, 108, 2, "child"], [117, 9, 108, 7], [117, 10, 108, 8, "onanimationend"], [117, 24, 108, 22], [117, 27, 108, 25], [117, 37, 108, 35, "event"], [117, 42, 108, 40], [117, 44, 108, 42], [118, 6, 109, 4, "parent"], [118, 12, 109, 10], [118, 13, 109, 11, "<PERSON><PERSON><PERSON><PERSON>"], [118, 24, 109, 22], [118, 25, 109, 23, "child"], [118, 30, 109, 28], [118, 31, 109, 29], [120, 6, 111, 4], [121, 6, 112, 4, "originalOnAnimationEnd"], [121, 28, 112, 26], [121, 30, 112, 28, "call"], [121, 34, 112, 32], [121, 35, 112, 33], [121, 39, 112, 37], [121, 41, 112, 39, "event"], [121, 46, 112, 44], [121, 47, 112, 45], [122, 4, 113, 2], [122, 5, 113, 3], [123, 2, 114, 0], [124, 2, 115, 0], [124, 11, 115, 9, "findDescendantWithExitingAnimation"], [124, 45, 115, 43, "findDescendantWithExitingAnimation"], [124, 46, 115, 44, "node"], [124, 50, 115, 48], [124, 52, 115, 50, "root"], [124, 56, 115, 54], [124, 58, 115, 56], [125, 4, 116, 2], [126, 4, 117, 2], [127, 4, 118, 2], [127, 8, 118, 6], [127, 10, 118, 8, "node"], [127, 14, 118, 12], [127, 26, 118, 24, "HTMLElement"], [127, 37, 118, 35], [127, 38, 118, 36], [127, 40, 118, 38], [128, 6, 119, 4], [129, 4, 120, 2], [130, 4, 121, 2], [130, 8, 121, 6, "node"], [130, 12, 121, 10], [130, 13, 121, 11, "reanimatedDummy"], [130, 28, 121, 26], [130, 32, 121, 30, "node"], [130, 36, 121, 34], [130, 37, 121, 35, "removedAfterAnimation"], [130, 58, 121, 56], [130, 63, 121, 61, "undefined"], [130, 72, 121, 70], [130, 74, 121, 72], [131, 6, 122, 4, "reattachElementToAncestor"], [131, 31, 122, 29], [131, 32, 122, 30, "node"], [131, 36, 122, 34], [131, 38, 122, 36, "root"], [131, 42, 122, 40], [131, 43, 122, 41], [132, 4, 123, 2], [133, 4, 124, 2], [133, 10, 124, 8, "children"], [133, 18, 124, 16], [133, 21, 124, 19, "Array"], [133, 26, 124, 24], [133, 27, 124, 25, "from"], [133, 31, 124, 29], [133, 32, 124, 30, "node"], [133, 36, 124, 34], [133, 37, 124, 35, "children"], [133, 45, 124, 43], [133, 46, 124, 44], [134, 4, 125, 2], [134, 9, 125, 7], [134, 13, 125, 11, "i"], [134, 14, 125, 12], [134, 17, 125, 15], [134, 18, 125, 16], [134, 20, 125, 18, "i"], [134, 21, 125, 19], [134, 24, 125, 22, "children"], [134, 32, 125, 30], [134, 33, 125, 31, "length"], [134, 39, 125, 37], [134, 41, 125, 39], [134, 43, 125, 41, "i"], [134, 44, 125, 42], [134, 46, 125, 44], [135, 6, 126, 4, "findDescendantWithExitingAnimation"], [135, 40, 126, 38], [135, 41, 126, 39, "children"], [135, 49, 126, 47], [135, 50, 126, 48, "i"], [135, 51, 126, 49], [135, 52, 126, 50], [135, 54, 126, 52, "root"], [135, 58, 126, 56], [135, 59, 126, 57], [136, 4, 127, 2], [137, 2, 128, 0], [138, 2, 129, 0], [138, 11, 129, 9, "checkIfScreenWasChanged"], [138, 34, 129, 32, "checkIfScreenWasChanged"], [138, 35, 129, 33, "<PERSON><PERSON><PERSON><PERSON>"], [138, 49, 129, 47], [138, 51, 129, 49], [139, 4, 130, 2], [139, 8, 130, 6, "reactFiberKey"], [139, 21, 130, 19], [139, 24, 130, 22], [139, 38, 130, 36], [140, 4, 131, 2], [140, 9, 131, 7], [140, 15, 131, 13, "key"], [140, 18, 131, 16], [140, 22, 131, 20, "Object"], [140, 28, 131, 26], [140, 29, 131, 27, "keys"], [140, 33, 131, 31], [140, 34, 131, 32, "<PERSON><PERSON><PERSON><PERSON>"], [140, 48, 131, 46], [140, 49, 131, 47], [140, 51, 131, 49], [141, 6, 132, 4], [141, 10, 132, 8, "key"], [141, 13, 132, 11], [141, 14, 132, 12, "startsWith"], [141, 24, 132, 22], [141, 25, 132, 23], [141, 39, 132, 37], [141, 40, 132, 38], [141, 42, 132, 40], [142, 8, 133, 6, "reactFiberKey"], [142, 21, 133, 19], [142, 24, 133, 22, "key"], [142, 27, 133, 25], [143, 8, 134, 6], [144, 6, 135, 4], [145, 4, 136, 2], [146, 4, 137, 2], [146, 11, 137, 9, "<PERSON><PERSON><PERSON><PERSON>"], [146, 25, 137, 23], [146, 26, 137, 24, "reactFiberKey"], [146, 39, 137, 37], [146, 40, 137, 38], [146, 42, 137, 40, "child"], [146, 47, 137, 45], [146, 49, 137, 47, "memoizedProps"], [146, 62, 137, 60], [146, 64, 137, 62, "navigation"], [146, 74, 137, 72], [146, 79, 137, 77, "undefined"], [146, 88, 137, 86], [147, 2, 138, 0], [148, 2, 139, 7], [148, 11, 139, 16, "addHTMLMutationObserver"], [148, 34, 139, 39, "addHTMLMutationObserver"], [148, 35, 139, 39], [148, 37, 139, 42], [149, 4, 140, 2], [149, 8, 140, 6, "isObserverSet"], [149, 21, 140, 19], [149, 25, 140, 23], [149, 26, 140, 24], [149, 30, 140, 24, "isWindowAvailable"], [149, 64, 140, 41], [149, 66, 140, 42], [149, 67, 140, 43], [149, 69, 140, 45], [150, 6, 141, 4], [151, 4, 142, 2], [152, 4, 143, 2, "isObserverSet"], [152, 17, 143, 15], [152, 20, 143, 18], [152, 24, 143, 22], [153, 4, 144, 2], [153, 10, 144, 8, "observer"], [153, 18, 144, 16], [153, 21, 144, 19], [153, 25, 144, 23, "MutationObserver"], [153, 41, 144, 39], [153, 42, 144, 40, "mutationsList"], [153, 55, 144, 53], [153, 59, 144, 57], [154, 6, 145, 4], [154, 12, 145, 10, "rootMutation"], [154, 24, 145, 22], [154, 27, 145, 25, "mutationsList"], [154, 40, 145, 38], [154, 41, 145, 39, "mutationsList"], [154, 54, 145, 52], [154, 55, 145, 53, "length"], [154, 61, 145, 59], [154, 64, 145, 62], [154, 65, 145, 63], [154, 66, 145, 64], [155, 6, 146, 4], [155, 10, 146, 8, "checkIfScreenWasChanged"], [155, 33, 146, 31], [155, 34, 146, 32, "rootMutation"], [155, 46, 146, 44], [155, 47, 146, 45, "target"], [155, 53, 146, 51], [155, 54, 146, 52], [155, 56, 146, 54], [156, 8, 147, 6], [157, 6, 148, 4], [158, 6, 149, 4], [158, 11, 149, 9], [158, 15, 149, 13, "i"], [158, 16, 149, 14], [158, 19, 149, 17], [158, 20, 149, 18], [158, 22, 149, 20, "i"], [158, 23, 149, 21], [158, 26, 149, 24, "rootMutation"], [158, 38, 149, 36], [158, 39, 149, 37, "removedNodes"], [158, 51, 149, 49], [158, 52, 149, 50, "length"], [158, 58, 149, 56], [158, 60, 149, 58], [158, 62, 149, 60, "i"], [158, 63, 149, 61], [158, 65, 149, 63], [159, 8, 150, 6, "findDescendantWithExitingAnimation"], [159, 42, 150, 40], [159, 43, 150, 41, "rootMutation"], [159, 55, 150, 53], [159, 56, 150, 54, "removedNodes"], [159, 68, 150, 66], [159, 69, 150, 67, "i"], [159, 70, 150, 68], [159, 71, 150, 69], [159, 73, 150, 71, "rootMutation"], [159, 85, 150, 83], [159, 86, 150, 84, "target"], [159, 92, 150, 90], [159, 93, 150, 91], [160, 6, 151, 4], [161, 4, 152, 2], [161, 5, 152, 3], [161, 6, 152, 4], [162, 4, 153, 2, "observer"], [162, 12, 153, 10], [162, 13, 153, 11, "observe"], [162, 20, 153, 18], [162, 21, 153, 19, "document"], [162, 29, 153, 27], [162, 30, 153, 28, "body"], [162, 34, 153, 32], [162, 36, 153, 34], [163, 6, 154, 4, "childList"], [163, 15, 154, 13], [163, 17, 154, 15], [163, 21, 154, 19], [164, 6, 155, 4, "subtree"], [164, 13, 155, 11], [164, 15, 155, 13], [165, 4, 156, 2], [165, 5, 156, 3], [165, 6, 156, 4], [166, 2, 157, 0], [167, 2, 158, 7], [167, 11, 158, 16, "areDOMRectsEqual"], [167, 27, 158, 32, "areDOMRectsEqual"], [167, 28, 158, 33, "r1"], [167, 30, 158, 35], [167, 32, 158, 37, "r2"], [167, 34, 158, 39], [167, 36, 158, 41], [168, 4, 159, 2], [169, 4, 160, 2], [169, 11, 160, 9, "r1"], [169, 13, 160, 11], [169, 14, 160, 12, "x"], [169, 15, 160, 13], [169, 20, 160, 18, "r2"], [169, 22, 160, 20], [169, 23, 160, 21, "x"], [169, 24, 160, 22], [169, 28, 160, 26, "r1"], [169, 30, 160, 28], [169, 31, 160, 29, "y"], [169, 32, 160, 30], [169, 37, 160, 35, "r2"], [169, 39, 160, 37], [169, 40, 160, 38, "y"], [169, 41, 160, 39], [169, 45, 160, 43, "r1"], [169, 47, 160, 45], [169, 48, 160, 46, "width"], [169, 53, 160, 51], [169, 58, 160, 56, "r2"], [169, 60, 160, 58], [169, 61, 160, 59, "width"], [169, 66, 160, 64], [169, 70, 160, 68, "r1"], [169, 72, 160, 70], [169, 73, 160, 71, "height"], [169, 79, 160, 77], [169, 84, 160, 82, "r2"], [169, 86, 160, 84], [169, 87, 160, 85, "height"], [169, 93, 160, 91], [170, 2, 161, 0], [171, 0, 161, 1], [171, 3]], "functionMap": {"names": ["<global>", "configureWebLayoutAnimations", "predefinedAnimationsStyleTag.onload", "insertWebAnimation", "removeWebAnimation", "scheduleAnimationCleanup", "setTimeout$argument_0", "reattachElementToAncestor", "child.onanimationend", "findDescendantWithExitingAnimation", "checkIfScreenWasChanged", "addHTMLMutationObserver", "MutationObserver$argument_0", "areDOMRectsEqual"], "mappings": "AAA;OCmB;wCCQ;GDQ;CDK;OGC;CHqB;AIC;CJsB;OKI;aCI,gED;CLC;AOC;yBCY;GDK;CPC;ASC;CTa;AUC;CVS;OWC;wCCK;GDQ;CXK;OaC;CbG"}}, "type": "js/module"}]}