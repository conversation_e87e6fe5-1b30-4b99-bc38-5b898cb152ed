{"dependencies": [{"name": "../Colors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 122}, "end": {"line": 4, "column": 52, "index": 174}}], "key": "vciIvByGV/VYcdc+1h/QRyZuAVI=", "exportNames": ["*"]}}, {"name": "../errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 175}, "end": {"line": 5, "column": 47, "index": 222}}], "key": "hqwpWRawU/ruYp+nBkn/8IqEHoU=", "exportNames": ["*"]}}, {"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 223}, "end": {"line": 6, "column": 73, "index": 296}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "../ReanimatedModule/js-reanimated/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 297}, "end": {"line": 7, "column": 76, "index": 373}}], "key": "fXWuc3v8Pe6ReMsVzhP/cURLdDA=", "exportNames": ["*"]}}, {"name": "../threads.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 374}, "end": {"line": 8, "column": 51, "index": 425}}], "key": "K4CZCGtE2IjiBjBQzdc2uYfV4CM=", "exportNames": ["*"]}}, {"name": "./processTransformOrigin.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 426}, "end": {"line": 9, "column": 69, "index": 495}}], "key": "pCE4c5bsyzkfqb5B9YaFIGSlBMw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable @typescript-eslint/no-redundant-type-constituents, @typescript-eslint/no-explicit-any */\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.updatePropsJestWrapper = exports.default = void 0;\n  var _Colors = require(_dependencyMap[0], \"../Colors.js\");\n  var _errors = require(_dependencyMap[1], \"../errors.js\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../PlatformChecker.js\");\n  var _index = require(_dependencyMap[3], \"../ReanimatedModule/js-reanimated/index.js\");\n  var _threads = require(_dependencyMap[4], \"../threads.js\");\n  var _processTransformOrigin = require(_dependencyMap[5], \"./processTransformOrigin.js\");\n  let updateProps;\n  const _worklet_14470221988614_init_data = {\n    code: \"function reactNativeReanimated_updatePropsJs1(viewDescriptors,updates,isAnimatedProps){const{_updatePropsJS}=this.__closure;var _viewDescriptors$valu;(_viewDescriptors$valu=viewDescriptors.value)===null||_viewDescriptors$valu===void 0||_viewDescriptors$valu.forEach(function(viewDescriptor){const component=viewDescriptor.tag;_updatePropsJS(updates,component,isAnimatedProps);});}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsJs1\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"isAnimatedProps\\\",\\\"_updatePropsJS\\\",\\\"__closure\\\",\\\"_viewDescriptors$valu\\\",\\\"value\\\",\\\"forEach\\\",\\\"viewDescriptor\\\",\\\"component\\\",\\\"tag\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\\\"],\\\"mappings\\\":\\\"AAWgB,QAAC,CAAAA,oCAA0BA,CAAAC,eAAoB,CAAAC,OAAA,CAAAC,eAAA,QAAAC,cAAA,OAAAC,SAAA,KAAAC,qBAAA,CAG3D,CAAAA,qBAAA,CAAAL,eAAe,CAACM,KAAK,UAAAD,qBAAA,WAArBA,qBAAA,CAAuBE,OAAO,CAAC,SAAAC,cAAc,CAAI,CAC/C,KAAM,CAAAC,SAAS,CAAGD,cAAc,CAACE,GAAG,CACpCP,cAAc,CAACF,OAAO,CAAEQ,SAAS,CAAEP,eAAe,CAAC,CACrD,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_13237998625751_init_data = {\n    code: \"function reactNativeReanimated_updatePropsJs2(viewDescriptors,updates){const{processColorsInProps,processTransformOrigin}=this.__closure;processColorsInProps(updates);if('transformOrigin'in updates){updates.transformOrigin=processTransformOrigin(updates.transformOrigin);}global.UpdatePropsManager.update(viewDescriptors,updates);}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsJs2\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"processColorsInProps\\\",\\\"processTransformOrigin\\\",\\\"__closure\\\",\\\"transformOrigin\\\",\\\"global\\\",\\\"UpdatePropsManager\\\",\\\"update\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\\\"],\\\"mappings\\\":\\\"AAoBgB,QAAC,CAAAA,oCAA6BA,CAAAC,eAAA,CAAAC,OAAA,QAAAC,oBAAA,CAAAC,sBAAA,OAAAC,SAAA,CAG1CF,oBAAoB,CAACD,OAAO,CAAC,CAC7B,GAAI,iBAAiB,EAAI,CAAAA,OAAO,CAAE,CAChCA,OAAO,CAACI,eAAe,CAAGF,sBAAsB,CAACF,OAAO,CAACI,eAAe,CAAC,CAC3E,CACAC,MAAM,CAACC,kBAAkB,CAACC,MAAM,CAACR,eAAe,CAAEC,OAAO,CAAC,CAC5D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    updateProps = function () {\n      const _e = [new global.Error(), -2, -27];\n      const reactNativeReanimated_updatePropsJs1 = function (viewDescriptors, updates, isAnimatedProps) {\n        viewDescriptors.value?.forEach(viewDescriptor => {\n          const component = viewDescriptor.tag;\n          (0, _index._updatePropsJS)(updates, component, isAnimatedProps);\n        });\n      };\n      reactNativeReanimated_updatePropsJs1.__closure = {\n        _updatePropsJS: _index._updatePropsJS\n      };\n      reactNativeReanimated_updatePropsJs1.__workletHash = 14470221988614;\n      reactNativeReanimated_updatePropsJs1.__initData = _worklet_14470221988614_init_data;\n      reactNativeReanimated_updatePropsJs1.__stackDetails = _e;\n      return reactNativeReanimated_updatePropsJs1;\n    }();\n  } else {\n    updateProps = function () {\n      const _e = [new global.Error(), -3, -27];\n      const reactNativeReanimated_updatePropsJs2 = function (viewDescriptors, updates) {\n        (0, _Colors.processColorsInProps)(updates);\n        if ('transformOrigin' in updates) {\n          updates.transformOrigin = (0, _processTransformOrigin.processTransformOrigin)(updates.transformOrigin);\n        }\n        global.UpdatePropsManager.update(viewDescriptors, updates);\n      };\n      reactNativeReanimated_updatePropsJs2.__closure = {\n        processColorsInProps: _Colors.processColorsInProps,\n        processTransformOrigin: _processTransformOrigin.processTransformOrigin\n      };\n      reactNativeReanimated_updatePropsJs2.__workletHash = 13237998625751;\n      reactNativeReanimated_updatePropsJs2.__initData = _worklet_13237998625751_init_data;\n      reactNativeReanimated_updatePropsJs2.__stackDetails = _e;\n      return reactNativeReanimated_updatePropsJs2;\n    }();\n  }\n  const updatePropsJestWrapper = (viewDescriptors, updates, animatedValues, adapters) => {\n    adapters.forEach(adapter => {\n      adapter(updates);\n    });\n    animatedValues.current.value = {\n      ...animatedValues.current.value,\n      ...updates\n    };\n    updateProps(viewDescriptors, updates);\n  };\n  exports.updatePropsJestWrapper = updatePropsJestWrapper;\n  var _default = exports.default = updateProps;\n  const _worklet_5349556006606_init_data = {\n    code: \"function reactNativeReanimated_updatePropsJs3(){const operations=[];return{update:function(viewDescriptors,updates){var _this=this;viewDescriptors.value.forEach(function(viewDescriptor){operations.push({shadowNodeWrapper:viewDescriptor.shadowNodeWrapper,updates:updates});if(operations.length===1){queueMicrotask(_this.flush);}});},flush:function(){global._updatePropsFabric(operations);operations.length=0;}};}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsJs3\\\",\\\"operations\\\",\\\"update\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"_this\\\",\\\"value\\\",\\\"forEach\\\",\\\"viewDescriptor\\\",\\\"push\\\",\\\"shadowNodeWrapper\\\",\\\"length\\\",\\\"queueMicrotask\\\",\\\"flush\\\",\\\"global\\\",\\\"_updatePropsFabric\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\\\"],\\\"mappings\\\":\\\"AAyC8C,SAAAA,oCAAMA,CAAA,EAIlD,KAAM,CAAAC,UAAU,CAAG,EAAE,CACrB,MAAO,CACLC,MAAM,SAAAA,CAACC,eAAe,CAAEC,OAAO,CAAE,KAAAC,KAAA,MAC/BF,eAAe,CAACG,KAAK,CAACC,OAAO,CAAC,SAAAC,cAAc,CAAI,CAC9CP,UAAU,CAACQ,IAAI,CAAC,CACdC,iBAAiB,CAAEF,cAAc,CAACE,iBAAiB,CACnDN,OAAA,CAAAA,OACF,CAAC,CAAC,CACF,GAAIH,UAAU,CAACU,MAAM,GAAK,CAAC,CAAE,CAC3BC,cAAc,CAACP,KAAI,CAACQ,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CACJ,CAAC,CACDA,KAAK,SAAAA,CAAA,CAAG,CACNC,MAAM,CAACC,kBAAkB,CAACd,UAAU,CAAC,CACrCA,UAAU,CAACU,MAAM,CAAG,CAAC,CACvB,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_481248343212_init_data = {\n    code: \"function reactNativeReanimated_updatePropsJs4(){const operations=[];return{update:function(viewDescriptors,updates){var _this=this;viewDescriptors.value.forEach(function(viewDescriptor){operations.push({tag:viewDescriptor.tag,name:viewDescriptor.name||'RCTView',updates:updates});if(operations.length===1){queueMicrotask(_this.flush);}});},flush:function(){global._updatePropsPaper(operations);operations.length=0;}};}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsJs4\\\",\\\"operations\\\",\\\"update\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"_this\\\",\\\"value\\\",\\\"forEach\\\",\\\"viewDescriptor\\\",\\\"push\\\",\\\"tag\\\",\\\"name\\\",\\\"length\\\",\\\"queueMicrotask\\\",\\\"flush\\\",\\\"global\\\",\\\"_updatePropsPaper\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\\\"],\\\"mappings\\\":\\\"AA+DI,SAAAA,oCAAMA,CAAA,EAIR,KAAM,CAAAC,UAAU,CAAG,EAAE,CACrB,MAAO,CACLC,MAAM,SAAAA,CAACC,eAAe,CAAEC,OAAO,CAAE,KAAAC,KAAA,MAC/BF,eAAe,CAACG,KAAK,CAACC,OAAO,CAAC,SAAAC,cAAc,CAAI,CAC9CP,UAAU,CAACQ,IAAI,CAAC,CACdC,GAAG,CAAEF,cAAc,CAACE,GAAG,CACvBC,IAAI,CAAEH,cAAc,CAACG,IAAI,EAAI,SAAS,CACtCP,OAAA,CAAAA,OACF,CAAC,CAAC,CACF,GAAIH,UAAU,CAACW,MAAM,GAAK,CAAC,CAAE,CAC3BC,cAAc,CAACR,KAAI,CAACS,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CACJ,CAAC,CACDA,KAAK,SAAAA,CAAA,CAAG,CACNC,MAAM,CAACC,iBAAiB,CAACf,UAAU,CAAC,CACpCA,UAAU,CAACW,MAAM,CAAG,CAAC,CACvB,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createUpdatePropsManager = (0, _PlatformChecker.isFabric)() ? function () {\n    const _e = [new global.Error(), 1, -27];\n    const reactNativeReanimated_updatePropsJs3 = function () {\n      // Fabric\n      const operations = [];\n      return {\n        update(viewDescriptors, updates) {\n          viewDescriptors.value.forEach(viewDescriptor => {\n            operations.push({\n              shadowNodeWrapper: viewDescriptor.shadowNodeWrapper,\n              updates\n            });\n            if (operations.length === 1) {\n              queueMicrotask(this.flush);\n            }\n          });\n        },\n        flush() {\n          global._updatePropsFabric(operations);\n          operations.length = 0;\n        }\n      };\n    };\n    reactNativeReanimated_updatePropsJs3.__closure = {};\n    reactNativeReanimated_updatePropsJs3.__workletHash = 5349556006606;\n    reactNativeReanimated_updatePropsJs3.__initData = _worklet_5349556006606_init_data;\n    reactNativeReanimated_updatePropsJs3.__stackDetails = _e;\n    return reactNativeReanimated_updatePropsJs3;\n  }() : function () {\n    const _e = [new global.Error(), 1, -27];\n    const reactNativeReanimated_updatePropsJs4 = function () {\n      // Paper\n      const operations = [];\n      return {\n        update(viewDescriptors, updates) {\n          viewDescriptors.value.forEach(viewDescriptor => {\n            operations.push({\n              tag: viewDescriptor.tag,\n              name: viewDescriptor.name || 'RCTView',\n              updates\n            });\n            if (operations.length === 1) {\n              queueMicrotask(this.flush);\n            }\n          });\n        },\n        flush() {\n          global._updatePropsPaper(operations);\n          operations.length = 0;\n        }\n      };\n    };\n    reactNativeReanimated_updatePropsJs4.__closure = {};\n    reactNativeReanimated_updatePropsJs4.__workletHash = 481248343212;\n    reactNativeReanimated_updatePropsJs4.__initData = _worklet_481248343212_init_data;\n    reactNativeReanimated_updatePropsJs4.__stackDetails = _e;\n    return reactNativeReanimated_updatePropsJs4;\n  }();\n  const _worklet_10551324478708_init_data = {\n    code: \"function reactNativeReanimated_updatePropsJs5(){const{createUpdatePropsManager}=this.__closure;global.UpdatePropsManager=createUpdatePropsManager();}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsJs5\\\",\\\"createUpdatePropsManager\\\",\\\"__closure\\\",\\\"global\\\",\\\"UpdatePropsManager\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/updateProps/updateProps.js\\\"],\\\"mappings\\\":\\\"AAuGqB,SAAAA,oCAAMA,CAAA,QAAAC,wBAAA,OAAAC,SAAA,CAGvBC,MAAM,CAACC,kBAAkB,CAAGH,wBAAwB,CAAC,CAAC,CACxD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    const maybeThrowError = () => {\n      // Jest attempts to access a property of this object to check if it is a Jest mock\n      // so we can't throw an error in the getter.\n      if (!(0, _PlatformChecker.isJest)()) {\n        throw new _errors.ReanimatedError('`UpdatePropsManager` is not available on non-native platform.');\n      }\n    };\n    global.UpdatePropsManager = new Proxy({}, {\n      get: maybeThrowError,\n      set: () => {\n        maybeThrowError();\n        return false;\n      }\n    });\n  } else {\n    (0, _threads.runOnUIImmediately)(function () {\n      const _e = [new global.Error(), -2, -27];\n      const reactNativeReanimated_updatePropsJs5 = function () {\n        global.UpdatePropsManager = createUpdatePropsManager();\n      };\n      reactNativeReanimated_updatePropsJs5.__closure = {\n        createUpdatePropsManager\n      };\n      reactNativeReanimated_updatePropsJs5.__workletHash = 10551324478708;\n      reactNativeReanimated_updatePropsJs5.__initData = _worklet_10551324478708_init_data;\n      reactNativeReanimated_updatePropsJs5.__stackDetails = _e;\n      return reactNativeReanimated_updatePropsJs5;\n    }())();\n  }\n\n  /**\n   * This used to be `SharedValue<Descriptors[]>` but objects holding just a\n   * single `value` prop are fine too.\n   */\n});", "lineCount": 188, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13, "Object"], [5, 8, 2, 13], [5, 9, 2, 13, "defineProperty"], [5, 23, 2, 13], [5, 24, 2, 13, "exports"], [5, 31, 2, 13], [6, 4, 2, 13, "value"], [6, 9, 2, 13], [7, 2, 2, 13], [8, 2, 2, 13, "exports"], [8, 9, 2, 13], [8, 10, 2, 13, "updatePropsJestWrapper"], [8, 32, 2, 13], [8, 35, 2, 13, "exports"], [8, 42, 2, 13], [8, 43, 2, 13, "default"], [8, 50, 2, 13], [9, 2, 4, 0], [9, 6, 4, 0, "_Colors"], [9, 13, 4, 0], [9, 16, 4, 0, "require"], [9, 23, 4, 0], [9, 24, 4, 0, "_dependencyMap"], [9, 38, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_errors"], [10, 13, 5, 0], [10, 16, 5, 0, "require"], [10, 23, 5, 0], [10, 24, 5, 0, "_dependencyMap"], [10, 38, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_PlatformChecker"], [11, 22, 6, 0], [11, 25, 6, 0, "require"], [11, 32, 6, 0], [11, 33, 6, 0, "_dependencyMap"], [11, 47, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_index"], [12, 12, 7, 0], [12, 15, 7, 0, "require"], [12, 22, 7, 0], [12, 23, 7, 0, "_dependencyMap"], [12, 37, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_threads"], [13, 14, 8, 0], [13, 17, 8, 0, "require"], [13, 24, 8, 0], [13, 25, 8, 0, "_dependencyMap"], [13, 39, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_processTransformOrigin"], [14, 29, 9, 0], [14, 32, 9, 0, "require"], [14, 39, 9, 0], [14, 40, 9, 0, "_dependencyMap"], [14, 54, 9, 0], [15, 2, 10, 0], [15, 6, 10, 4, "updateProps"], [15, 17, 10, 15], [16, 2, 10, 16], [16, 8, 10, 16, "_worklet_14470221988614_init_data"], [16, 41, 10, 16], [17, 4, 10, 16, "code"], [17, 8, 10, 16], [18, 4, 10, 16, "location"], [18, 12, 10, 16], [19, 4, 10, 16, "sourceMap"], [19, 13, 10, 16], [20, 4, 10, 16, "version"], [20, 11, 10, 16], [21, 2, 10, 16], [22, 2, 10, 16], [22, 8, 10, 16, "_worklet_13237998625751_init_data"], [22, 41, 10, 16], [23, 4, 10, 16, "code"], [23, 8, 10, 16], [24, 4, 10, 16, "location"], [24, 12, 10, 16], [25, 4, 10, 16, "sourceMap"], [25, 13, 10, 16], [26, 4, 10, 16, "version"], [26, 11, 10, 16], [27, 2, 10, 16], [28, 2, 11, 0], [28, 6, 11, 4], [28, 10, 11, 4, "shouldBeUseWeb"], [28, 41, 11, 18], [28, 43, 11, 19], [28, 44, 11, 20], [28, 46, 11, 22], [29, 4, 12, 2, "updateProps"], [29, 15, 12, 13], [29, 18, 12, 16], [30, 6, 12, 16], [30, 12, 12, 16, "_e"], [30, 14, 12, 16], [30, 22, 12, 16, "global"], [30, 28, 12, 16], [30, 29, 12, 16, "Error"], [30, 34, 12, 16], [31, 6, 12, 16], [31, 12, 12, 16, "reactNativeReanimated_updatePropsJs1"], [31, 48, 12, 16], [31, 60, 12, 16, "reactNativeReanimated_updatePropsJs1"], [31, 61, 12, 17, "viewDescriptors"], [31, 76, 12, 32], [31, 78, 12, 34, "updates"], [31, 85, 12, 41], [31, 87, 12, 43, "isAnimatedProps"], [31, 102, 12, 58], [31, 104, 12, 63], [32, 8, 15, 4, "viewDescriptors"], [32, 23, 15, 19], [32, 24, 15, 20, "value"], [32, 29, 15, 25], [32, 31, 15, 27, "for<PERSON>ach"], [32, 38, 15, 34], [32, 39, 15, 35, "viewDescriptor"], [32, 53, 15, 49], [32, 57, 15, 53], [33, 10, 16, 6], [33, 16, 16, 12, "component"], [33, 25, 16, 21], [33, 28, 16, 24, "viewDescriptor"], [33, 42, 16, 38], [33, 43, 16, 39, "tag"], [33, 46, 16, 42], [34, 10, 17, 6], [34, 14, 17, 6, "_updatePropsJS"], [34, 35, 17, 20], [34, 37, 17, 21, "updates"], [34, 44, 17, 28], [34, 46, 17, 30, "component"], [34, 55, 17, 39], [34, 57, 17, 41, "isAnimatedProps"], [34, 72, 17, 56], [34, 73, 17, 57], [35, 8, 18, 4], [35, 9, 18, 5], [35, 10, 18, 6], [36, 6, 19, 2], [36, 7, 19, 3], [37, 6, 19, 3, "reactNativeReanimated_updatePropsJs1"], [37, 42, 19, 3], [37, 43, 19, 3, "__closure"], [37, 52, 19, 3], [38, 8, 19, 3, "_updatePropsJS"], [38, 22, 19, 3], [38, 24, 17, 6, "_updatePropsJS"], [39, 6, 17, 20], [40, 6, 17, 20, "reactNativeReanimated_updatePropsJs1"], [40, 42, 17, 20], [40, 43, 17, 20, "__workletHash"], [40, 56, 17, 20], [41, 6, 17, 20, "reactNativeReanimated_updatePropsJs1"], [41, 42, 17, 20], [41, 43, 17, 20, "__initData"], [41, 53, 17, 20], [41, 56, 17, 20, "_worklet_14470221988614_init_data"], [41, 89, 17, 20], [42, 6, 17, 20, "reactNativeReanimated_updatePropsJs1"], [42, 42, 17, 20], [42, 43, 17, 20, "__stackDetails"], [42, 57, 17, 20], [42, 60, 17, 20, "_e"], [42, 62, 17, 20], [43, 6, 17, 20], [43, 13, 17, 20, "reactNativeReanimated_updatePropsJs1"], [43, 49, 17, 20], [44, 4, 17, 20], [44, 5, 12, 16], [44, 7, 19, 3], [45, 2, 20, 0], [45, 3, 20, 1], [45, 9, 20, 7], [46, 4, 21, 2, "updateProps"], [46, 15, 21, 13], [46, 18, 21, 16], [47, 6, 21, 16], [47, 12, 21, 16, "_e"], [47, 14, 21, 16], [47, 22, 21, 16, "global"], [47, 28, 21, 16], [47, 29, 21, 16, "Error"], [47, 34, 21, 16], [48, 6, 21, 16], [48, 12, 21, 16, "reactNativeReanimated_updatePropsJs2"], [48, 48, 21, 16], [48, 60, 21, 16, "reactNativeReanimated_updatePropsJs2"], [48, 61, 21, 17, "viewDescriptors"], [48, 76, 21, 32], [48, 78, 21, 34, "updates"], [48, 85, 21, 41], [48, 87, 21, 46], [49, 8, 24, 4], [49, 12, 24, 4, "processColorsInProps"], [49, 40, 24, 24], [49, 42, 24, 25, "updates"], [49, 49, 24, 32], [49, 50, 24, 33], [50, 8, 25, 4], [50, 12, 25, 8], [50, 29, 25, 25], [50, 33, 25, 29, "updates"], [50, 40, 25, 36], [50, 42, 25, 38], [51, 10, 26, 6, "updates"], [51, 17, 26, 13], [51, 18, 26, 14, "transform<PERSON><PERSON>in"], [51, 33, 26, 29], [51, 36, 26, 32], [51, 40, 26, 32, "processTransformOrigin"], [51, 86, 26, 54], [51, 88, 26, 55, "updates"], [51, 95, 26, 62], [51, 96, 26, 63, "transform<PERSON><PERSON>in"], [51, 111, 26, 78], [51, 112, 26, 79], [52, 8, 27, 4], [53, 8, 28, 4, "global"], [53, 14, 28, 10], [53, 15, 28, 11, "UpdatePropsManager"], [53, 33, 28, 29], [53, 34, 28, 30, "update"], [53, 40, 28, 36], [53, 41, 28, 37, "viewDescriptors"], [53, 56, 28, 52], [53, 58, 28, 54, "updates"], [53, 65, 28, 61], [53, 66, 28, 62], [54, 6, 29, 2], [54, 7, 29, 3], [55, 6, 29, 3, "reactNativeReanimated_updatePropsJs2"], [55, 42, 29, 3], [55, 43, 29, 3, "__closure"], [55, 52, 29, 3], [56, 8, 29, 3, "processColorsInProps"], [56, 28, 29, 3], [56, 30, 24, 4, "processColorsInProps"], [56, 58, 24, 24], [57, 8, 24, 24, "processTransformOrigin"], [57, 30, 24, 24], [57, 32, 26, 32, "processTransformOrigin"], [58, 6, 26, 54], [59, 6, 26, 54, "reactNativeReanimated_updatePropsJs2"], [59, 42, 26, 54], [59, 43, 26, 54, "__workletHash"], [59, 56, 26, 54], [60, 6, 26, 54, "reactNativeReanimated_updatePropsJs2"], [60, 42, 26, 54], [60, 43, 26, 54, "__initData"], [60, 53, 26, 54], [60, 56, 26, 54, "_worklet_13237998625751_init_data"], [60, 89, 26, 54], [61, 6, 26, 54, "reactNativeReanimated_updatePropsJs2"], [61, 42, 26, 54], [61, 43, 26, 54, "__stackDetails"], [61, 57, 26, 54], [61, 60, 26, 54, "_e"], [61, 62, 26, 54], [62, 6, 26, 54], [62, 13, 26, 54, "reactNativeReanimated_updatePropsJs2"], [62, 49, 26, 54], [63, 4, 26, 54], [63, 5, 21, 16], [63, 7, 29, 3], [64, 2, 30, 0], [65, 2, 31, 7], [65, 8, 31, 13, "updatePropsJestWrapper"], [65, 30, 31, 35], [65, 33, 31, 38, "updatePropsJestWrapper"], [65, 34, 31, 39, "viewDescriptors"], [65, 49, 31, 54], [65, 51, 31, 56, "updates"], [65, 58, 31, 63], [65, 60, 31, 65, "animatedValues"], [65, 74, 31, 79], [65, 76, 31, 81, "adapters"], [65, 84, 31, 89], [65, 89, 31, 94], [66, 4, 32, 2, "adapters"], [66, 12, 32, 10], [66, 13, 32, 11, "for<PERSON>ach"], [66, 20, 32, 18], [66, 21, 32, 19, "adapter"], [66, 28, 32, 26], [66, 32, 32, 30], [67, 6, 33, 4, "adapter"], [67, 13, 33, 11], [67, 14, 33, 12, "updates"], [67, 21, 33, 19], [67, 22, 33, 20], [68, 4, 34, 2], [68, 5, 34, 3], [68, 6, 34, 4], [69, 4, 35, 2, "animatedValues"], [69, 18, 35, 16], [69, 19, 35, 17, "current"], [69, 26, 35, 24], [69, 27, 35, 25, "value"], [69, 32, 35, 30], [69, 35, 35, 33], [70, 6, 36, 4], [70, 9, 36, 7, "animatedValues"], [70, 23, 36, 21], [70, 24, 36, 22, "current"], [70, 31, 36, 29], [70, 32, 36, 30, "value"], [70, 37, 36, 35], [71, 6, 37, 4], [71, 9, 37, 7, "updates"], [72, 4, 38, 2], [72, 5, 38, 3], [73, 4, 39, 2, "updateProps"], [73, 15, 39, 13], [73, 16, 39, 14, "viewDescriptors"], [73, 31, 39, 29], [73, 33, 39, 31, "updates"], [73, 40, 39, 38], [73, 41, 39, 39], [74, 2, 40, 0], [74, 3, 40, 1], [75, 2, 40, 2, "exports"], [75, 9, 40, 2], [75, 10, 40, 2, "updatePropsJestWrapper"], [75, 32, 40, 2], [75, 35, 40, 2, "updatePropsJestWrapper"], [75, 57, 40, 2], [76, 2, 40, 2], [76, 6, 40, 2, "_default"], [76, 14, 40, 2], [76, 17, 40, 2, "exports"], [76, 24, 40, 2], [76, 25, 40, 2, "default"], [76, 32, 40, 2], [76, 35, 41, 15, "updateProps"], [76, 46, 41, 26], [77, 2, 41, 26], [77, 8, 41, 26, "_worklet_5349556006606_init_data"], [77, 40, 41, 26], [78, 4, 41, 26, "code"], [78, 8, 41, 26], [79, 4, 41, 26, "location"], [79, 12, 41, 26], [80, 4, 41, 26, "sourceMap"], [80, 13, 41, 26], [81, 4, 41, 26, "version"], [81, 11, 41, 26], [82, 2, 41, 26], [83, 2, 41, 26], [83, 8, 41, 26, "_worklet_481248343212_init_data"], [83, 39, 41, 26], [84, 4, 41, 26, "code"], [84, 8, 41, 26], [85, 4, 41, 26, "location"], [85, 12, 41, 26], [86, 4, 41, 26, "sourceMap"], [86, 13, 41, 26], [87, 4, 41, 26, "version"], [87, 11, 41, 26], [88, 2, 41, 26], [89, 2, 42, 0], [89, 8, 42, 6, "createUpdatePropsManager"], [89, 32, 42, 30], [89, 35, 42, 33], [89, 39, 42, 33, "isF<PERSON><PERSON>"], [89, 64, 42, 41], [89, 66, 42, 42], [89, 67, 42, 43], [89, 70, 42, 46], [90, 4, 42, 46], [90, 10, 42, 46, "_e"], [90, 12, 42, 46], [90, 20, 42, 46, "global"], [90, 26, 42, 46], [90, 27, 42, 46, "Error"], [90, 32, 42, 46], [91, 4, 42, 46], [91, 10, 42, 46, "reactNativeReanimated_updatePropsJs3"], [91, 46, 42, 46], [91, 58, 42, 46, "reactNativeReanimated_updatePropsJs3"], [91, 59, 42, 46], [91, 61, 42, 52], [92, 6, 45, 2], [93, 6, 46, 2], [93, 12, 46, 8, "operations"], [93, 22, 46, 18], [93, 25, 46, 21], [93, 27, 46, 23], [94, 6, 47, 2], [94, 13, 47, 9], [95, 8, 48, 4, "update"], [95, 14, 48, 10, "update"], [95, 15, 48, 11, "viewDescriptors"], [95, 30, 48, 26], [95, 32, 48, 28, "updates"], [95, 39, 48, 35], [95, 41, 48, 37], [96, 10, 49, 6, "viewDescriptors"], [96, 25, 49, 21], [96, 26, 49, 22, "value"], [96, 31, 49, 27], [96, 32, 49, 28, "for<PERSON>ach"], [96, 39, 49, 35], [96, 40, 49, 36, "viewDescriptor"], [96, 54, 49, 50], [96, 58, 49, 54], [97, 12, 50, 8, "operations"], [97, 22, 50, 18], [97, 23, 50, 19, "push"], [97, 27, 50, 23], [97, 28, 50, 24], [98, 14, 51, 10, "shadowNodeWrapper"], [98, 31, 51, 27], [98, 33, 51, 29, "viewDescriptor"], [98, 47, 51, 43], [98, 48, 51, 44, "shadowNodeWrapper"], [98, 65, 51, 61], [99, 14, 52, 10, "updates"], [100, 12, 53, 8], [100, 13, 53, 9], [100, 14, 53, 10], [101, 12, 54, 8], [101, 16, 54, 12, "operations"], [101, 26, 54, 22], [101, 27, 54, 23, "length"], [101, 33, 54, 29], [101, 38, 54, 34], [101, 39, 54, 35], [101, 41, 54, 37], [102, 14, 55, 10, "queueMicrotask"], [102, 28, 55, 24], [102, 29, 55, 25], [102, 33, 55, 29], [102, 34, 55, 30, "flush"], [102, 39, 55, 35], [102, 40, 55, 36], [103, 12, 56, 8], [104, 10, 57, 6], [104, 11, 57, 7], [104, 12, 57, 8], [105, 8, 58, 4], [105, 9, 58, 5], [106, 8, 59, 4, "flush"], [106, 13, 59, 9, "flush"], [106, 14, 59, 9], [106, 16, 59, 12], [107, 10, 60, 6, "global"], [107, 16, 60, 12], [107, 17, 60, 13, "_updatePropsFabric"], [107, 35, 60, 31], [107, 36, 60, 32, "operations"], [107, 46, 60, 42], [107, 47, 60, 43], [108, 10, 61, 6, "operations"], [108, 20, 61, 16], [108, 21, 61, 17, "length"], [108, 27, 61, 23], [108, 30, 61, 26], [108, 31, 61, 27], [109, 8, 62, 4], [110, 6, 63, 2], [110, 7, 63, 3], [111, 4, 64, 0], [111, 5, 64, 1], [112, 4, 64, 1, "reactNativeReanimated_updatePropsJs3"], [112, 40, 64, 1], [112, 41, 64, 1, "__closure"], [112, 50, 64, 1], [113, 4, 64, 1, "reactNativeReanimated_updatePropsJs3"], [113, 40, 64, 1], [113, 41, 64, 1, "__workletHash"], [113, 54, 64, 1], [114, 4, 64, 1, "reactNativeReanimated_updatePropsJs3"], [114, 40, 64, 1], [114, 41, 64, 1, "__initData"], [114, 51, 64, 1], [114, 54, 64, 1, "_worklet_5349556006606_init_data"], [114, 86, 64, 1], [115, 4, 64, 1, "reactNativeReanimated_updatePropsJs3"], [115, 40, 64, 1], [115, 41, 64, 1, "__stackDetails"], [115, 55, 64, 1], [115, 58, 64, 1, "_e"], [115, 60, 64, 1], [116, 4, 64, 1], [116, 11, 64, 1, "reactNativeReanimated_updatePropsJs3"], [116, 47, 64, 1], [117, 2, 64, 1], [117, 3, 42, 46], [117, 8, 64, 4], [118, 4, 64, 4], [118, 10, 64, 4, "_e"], [118, 12, 64, 4], [118, 20, 64, 4, "global"], [118, 26, 64, 4], [118, 27, 64, 4, "Error"], [118, 32, 64, 4], [119, 4, 64, 4], [119, 10, 64, 4, "reactNativeReanimated_updatePropsJs4"], [119, 46, 64, 4], [119, 58, 64, 4, "reactNativeReanimated_updatePropsJs4"], [119, 59, 64, 4], [119, 61, 64, 10], [120, 6, 67, 2], [121, 6, 68, 2], [121, 12, 68, 8, "operations"], [121, 22, 68, 18], [121, 25, 68, 21], [121, 27, 68, 23], [122, 6, 69, 2], [122, 13, 69, 9], [123, 8, 70, 4, "update"], [123, 14, 70, 10, "update"], [123, 15, 70, 11, "viewDescriptors"], [123, 30, 70, 26], [123, 32, 70, 28, "updates"], [123, 39, 70, 35], [123, 41, 70, 37], [124, 10, 71, 6, "viewDescriptors"], [124, 25, 71, 21], [124, 26, 71, 22, "value"], [124, 31, 71, 27], [124, 32, 71, 28, "for<PERSON>ach"], [124, 39, 71, 35], [124, 40, 71, 36, "viewDescriptor"], [124, 54, 71, 50], [124, 58, 71, 54], [125, 12, 72, 8, "operations"], [125, 22, 72, 18], [125, 23, 72, 19, "push"], [125, 27, 72, 23], [125, 28, 72, 24], [126, 14, 73, 10, "tag"], [126, 17, 73, 13], [126, 19, 73, 15, "viewDescriptor"], [126, 33, 73, 29], [126, 34, 73, 30, "tag"], [126, 37, 73, 33], [127, 14, 74, 10, "name"], [127, 18, 74, 14], [127, 20, 74, 16, "viewDescriptor"], [127, 34, 74, 30], [127, 35, 74, 31, "name"], [127, 39, 74, 35], [127, 43, 74, 39], [127, 52, 74, 48], [128, 14, 75, 10, "updates"], [129, 12, 76, 8], [129, 13, 76, 9], [129, 14, 76, 10], [130, 12, 77, 8], [130, 16, 77, 12, "operations"], [130, 26, 77, 22], [130, 27, 77, 23, "length"], [130, 33, 77, 29], [130, 38, 77, 34], [130, 39, 77, 35], [130, 41, 77, 37], [131, 14, 78, 10, "queueMicrotask"], [131, 28, 78, 24], [131, 29, 78, 25], [131, 33, 78, 29], [131, 34, 78, 30, "flush"], [131, 39, 78, 35], [131, 40, 78, 36], [132, 12, 79, 8], [133, 10, 80, 6], [133, 11, 80, 7], [133, 12, 80, 8], [134, 8, 81, 4], [134, 9, 81, 5], [135, 8, 82, 4, "flush"], [135, 13, 82, 9, "flush"], [135, 14, 82, 9], [135, 16, 82, 12], [136, 10, 83, 6, "global"], [136, 16, 83, 12], [136, 17, 83, 13, "_updatePropsPaper"], [136, 34, 83, 30], [136, 35, 83, 31, "operations"], [136, 45, 83, 41], [136, 46, 83, 42], [137, 10, 84, 6, "operations"], [137, 20, 84, 16], [137, 21, 84, 17, "length"], [137, 27, 84, 23], [137, 30, 84, 26], [137, 31, 84, 27], [138, 8, 85, 4], [139, 6, 86, 2], [139, 7, 86, 3], [140, 4, 87, 0], [140, 5, 87, 1], [141, 4, 87, 1, "reactNativeReanimated_updatePropsJs4"], [141, 40, 87, 1], [141, 41, 87, 1, "__closure"], [141, 50, 87, 1], [142, 4, 87, 1, "reactNativeReanimated_updatePropsJs4"], [142, 40, 87, 1], [142, 41, 87, 1, "__workletHash"], [142, 54, 87, 1], [143, 4, 87, 1, "reactNativeReanimated_updatePropsJs4"], [143, 40, 87, 1], [143, 41, 87, 1, "__initData"], [143, 51, 87, 1], [143, 54, 87, 1, "_worklet_481248343212_init_data"], [143, 85, 87, 1], [144, 4, 87, 1, "reactNativeReanimated_updatePropsJs4"], [144, 40, 87, 1], [144, 41, 87, 1, "__stackDetails"], [144, 55, 87, 1], [144, 58, 87, 1, "_e"], [144, 60, 87, 1], [145, 4, 87, 1], [145, 11, 87, 1, "reactNativeReanimated_updatePropsJs4"], [145, 47, 87, 1], [146, 2, 87, 1], [146, 3, 64, 4], [146, 5, 87, 1], [147, 2, 87, 2], [147, 8, 87, 2, "_worklet_10551324478708_init_data"], [147, 41, 87, 2], [148, 4, 87, 2, "code"], [148, 8, 87, 2], [149, 4, 87, 2, "location"], [149, 12, 87, 2], [150, 4, 87, 2, "sourceMap"], [150, 13, 87, 2], [151, 4, 87, 2, "version"], [151, 11, 87, 2], [152, 2, 87, 2], [153, 2, 88, 0], [153, 6, 88, 4], [153, 10, 88, 4, "shouldBeUseWeb"], [153, 41, 88, 18], [153, 43, 88, 19], [153, 44, 88, 20], [153, 46, 88, 22], [154, 4, 89, 2], [154, 10, 89, 8, "maybeThrowError"], [154, 25, 89, 23], [154, 28, 89, 26, "maybeThrowError"], [154, 29, 89, 26], [154, 34, 89, 32], [155, 6, 90, 4], [156, 6, 91, 4], [157, 6, 92, 4], [157, 10, 92, 8], [157, 11, 92, 9], [157, 15, 92, 9, "isJest"], [157, 38, 92, 15], [157, 40, 92, 16], [157, 41, 92, 17], [157, 43, 92, 19], [158, 8, 93, 6], [158, 14, 93, 12], [158, 18, 93, 16, "ReanimatedError"], [158, 41, 93, 31], [158, 42, 93, 32], [158, 105, 93, 95], [158, 106, 93, 96], [159, 6, 94, 4], [160, 4, 95, 2], [160, 5, 95, 3], [161, 4, 96, 2, "global"], [161, 10, 96, 8], [161, 11, 96, 9, "UpdatePropsManager"], [161, 29, 96, 27], [161, 32, 96, 30], [161, 36, 96, 34, "Proxy"], [161, 41, 96, 39], [161, 42, 96, 40], [161, 43, 96, 41], [161, 44, 96, 42], [161, 46, 96, 44], [162, 6, 97, 4, "get"], [162, 9, 97, 7], [162, 11, 97, 9, "maybeThrowError"], [162, 26, 97, 24], [163, 6, 98, 4, "set"], [163, 9, 98, 7], [163, 11, 98, 9, "set"], [163, 12, 98, 9], [163, 17, 98, 15], [164, 8, 99, 6, "maybeThrowError"], [164, 23, 99, 21], [164, 24, 99, 22], [164, 25, 99, 23], [165, 8, 100, 6], [165, 15, 100, 13], [165, 20, 100, 18], [166, 6, 101, 4], [167, 4, 102, 2], [167, 5, 102, 3], [167, 6, 102, 4], [168, 2, 103, 0], [168, 3, 103, 1], [168, 9, 103, 7], [169, 4, 104, 2], [169, 8, 104, 2, "runOnUIImmediately"], [169, 35, 104, 20], [169, 37, 104, 21], [170, 6, 104, 21], [170, 12, 104, 21, "_e"], [170, 14, 104, 21], [170, 22, 104, 21, "global"], [170, 28, 104, 21], [170, 29, 104, 21, "Error"], [170, 34, 104, 21], [171, 6, 104, 21], [171, 12, 104, 21, "reactNativeReanimated_updatePropsJs5"], [171, 48, 104, 21], [171, 60, 104, 21, "reactNativeReanimated_updatePropsJs5"], [171, 61, 104, 21], [171, 63, 104, 27], [172, 8, 107, 4, "global"], [172, 14, 107, 10], [172, 15, 107, 11, "UpdatePropsManager"], [172, 33, 107, 29], [172, 36, 107, 32, "createUpdatePropsManager"], [172, 60, 107, 56], [172, 61, 107, 57], [172, 62, 107, 58], [173, 6, 108, 2], [173, 7, 108, 3], [174, 6, 108, 3, "reactNativeReanimated_updatePropsJs5"], [174, 42, 108, 3], [174, 43, 108, 3, "__closure"], [174, 52, 108, 3], [175, 8, 108, 3, "createUpdatePropsManager"], [176, 6, 108, 3], [177, 6, 108, 3, "reactNativeReanimated_updatePropsJs5"], [177, 42, 108, 3], [177, 43, 108, 3, "__workletHash"], [177, 56, 108, 3], [178, 6, 108, 3, "reactNativeReanimated_updatePropsJs5"], [178, 42, 108, 3], [178, 43, 108, 3, "__initData"], [178, 53, 108, 3], [178, 56, 108, 3, "_worklet_10551324478708_init_data"], [178, 89, 108, 3], [179, 6, 108, 3, "reactNativeReanimated_updatePropsJs5"], [179, 42, 108, 3], [179, 43, 108, 3, "__stackDetails"], [179, 57, 108, 3], [179, 60, 108, 3, "_e"], [179, 62, 108, 3], [180, 6, 108, 3], [180, 13, 108, 3, "reactNativeReanimated_updatePropsJs5"], [180, 49, 108, 3], [181, 4, 108, 3], [181, 5, 104, 21], [181, 7, 108, 3], [181, 8, 108, 4], [181, 9, 108, 5], [181, 10, 108, 6], [182, 2, 109, 0], [184, 2, 111, 0], [185, 0, 112, 0], [186, 0, 113, 0], [187, 0, 114, 0], [188, 0, 111, 0], [188, 3]], "functionMap": {"names": ["<global>", "updateProps", "viewDescriptors.value.forEach$argument_0", "updatePropsJestWrapper", "adapters.forEach$argument_0", "<anonymous>", "update", "flush", "maybeThrowError", "Proxy$argument_1.set", "runOnUIImmediately$argument_0"], "mappings": "AAA;gBCW;mCCG;KDG;GDC;gBCE;GDQ;sCGE;mBCC;GDE;CHM;8CKE;ICM;oCJC;OIQ;KDC;IEC;KFG;CLE,GK;ICM;oCJC;OIS;KDC;IEC;KFG;CLE;0BQE;GRM;SSG;KTG;qBUG;GVI"}}, "type": "js/module"}]}