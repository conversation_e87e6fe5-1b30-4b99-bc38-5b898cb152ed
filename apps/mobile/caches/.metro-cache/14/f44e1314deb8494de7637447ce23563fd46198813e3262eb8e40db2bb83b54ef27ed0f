{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Compare two arrays to check if the first array starts with the second array.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.arrayStartsWith = arrayStartsWith;\n  function arrayStartsWith(array, start) {\n    if (start.length > array.length) {\n      return false;\n    }\n    return start.every((it, index) => it === array[index]);\n  }\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0, "Object"], [7, 8, 3, 0], [7, 9, 3, 0, "defineProperty"], [7, 23, 3, 0], [7, 24, 3, 0, "exports"], [7, 31, 3, 0], [8, 4, 3, 0, "value"], [8, 9, 3, 0], [9, 2, 3, 0], [10, 2, 3, 0, "exports"], [10, 9, 3, 0], [10, 10, 3, 0, "arrayStartsWith"], [10, 25, 3, 0], [10, 28, 3, 0, "arrayStartsWith"], [10, 43, 3, 0], [11, 2, 6, 7], [11, 11, 6, 16, "arrayStartsWith"], [11, 26, 6, 31, "arrayStartsWith"], [11, 27, 6, 32, "array"], [11, 32, 6, 37], [11, 34, 6, 39, "start"], [11, 39, 6, 44], [11, 41, 6, 46], [12, 4, 7, 2], [12, 8, 7, 6, "start"], [12, 13, 7, 11], [12, 14, 7, 12, "length"], [12, 20, 7, 18], [12, 23, 7, 21, "array"], [12, 28, 7, 26], [12, 29, 7, 27, "length"], [12, 35, 7, 33], [12, 37, 7, 35], [13, 6, 8, 4], [13, 13, 8, 11], [13, 18, 8, 16], [14, 4, 9, 2], [15, 4, 10, 2], [15, 11, 10, 9, "start"], [15, 16, 10, 14], [15, 17, 10, 15, "every"], [15, 22, 10, 20], [15, 23, 10, 21], [15, 24, 10, 22, "it"], [15, 26, 10, 24], [15, 28, 10, 26, "index"], [15, 33, 10, 31], [15, 38, 10, 36, "it"], [15, 40, 10, 38], [15, 45, 10, 43, "array"], [15, 50, 10, 48], [15, 51, 10, 49, "index"], [15, 56, 10, 54], [15, 57, 10, 55], [15, 58, 10, 56], [16, 2, 11, 0], [17, 0, 11, 1], [17, 3]], "functionMap": {"names": ["<global>", "arrayStartsWith", "start.every$argument_0"], "mappings": "AAA;OCK;qBCI,kCD;CDC"}}, "type": "js/module"}]}