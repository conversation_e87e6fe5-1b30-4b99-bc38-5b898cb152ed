{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PressabilityDebugView = PressabilityDebugView;\n  // PressabilityDebugView is not implemented in react-native-web\n  function PressabilityDebugView() {\n    return null;\n  }\n});", "lineCount": 10, "map": [[6, 2, 1, 0], [7, 2, 2, 7], [7, 11, 2, 16, "PressabilityDebugView"], [7, 32, 2, 37, "PressabilityDebugView"], [7, 33, 2, 37], [7, 35, 2, 40], [8, 4, 3, 2], [8, 11, 3, 9], [8, 15, 3, 13], [9, 2, 4, 0], [10, 0, 4, 1], [10, 3]], "functionMap": {"names": ["<global>", "PressabilityDebugView"], "mappings": "AAA;OCC;CDE"}}, "type": "js/module"}]}