{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Amphora = exports.default = (0, _createLucideIcon.default)(\"Amphora\", [[\"path\", {\n    d: \"M10 2v5.632c0 .424-.272.795-.653.982A6 6 0 0 0 6 14c.006 4 3 7 5 8\",\n    key: \"1h8rid\"\n  }], [\"path\", {\n    d: \"M10 5H8a2 2 0 0 0 0 4h.68\",\n    key: \"3ezsi6\"\n  }], [\"path\", {\n    d: \"M14 2v5.632c0 .424.272.795.652.982A6 6 0 0 1 18 14c0 4-3 7-5 8\",\n    key: \"yt6q09\"\n  }], [\"path\", {\n    d: \"M14 5h2a2 2 0 0 1 0 4h-.68\",\n    key: \"8f95yk\"\n  }], [\"path\", {\n    d: \"M18 22H6\",\n    key: \"mg6kv4\"\n  }], [\"path\", {\n    d: \"M9 2h6\",\n    key: \"1jrp98\"\n  }]]);\n});", "lineCount": 34, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Amphora"], [15, 13, 10, 13], [15, 16, 10, 13, "exports"], [15, 23, 10, 13], [15, 24, 10, 13, "default"], [15, 31, 10, 13], [15, 34, 10, 16], [15, 38, 10, 16, "createLucideIcon"], [15, 63, 10, 32], [15, 65, 10, 33], [15, 74, 10, 42], [15, 76, 10, 44], [15, 77, 11, 2], [15, 78, 12, 4], [15, 84, 12, 10], [15, 86, 13, 4], [16, 4, 13, 6, "d"], [16, 5, 13, 7], [16, 7, 13, 9], [16, 75, 13, 77], [17, 4, 13, 79, "key"], [17, 7, 13, 82], [17, 9, 13, 84], [18, 2, 13, 93], [18, 3, 13, 94], [18, 4, 14, 3], [18, 6, 15, 2], [18, 7, 15, 3], [18, 13, 15, 9], [18, 15, 15, 11], [19, 4, 15, 13, "d"], [19, 5, 15, 14], [19, 7, 15, 16], [19, 34, 15, 43], [20, 4, 15, 45, "key"], [20, 7, 15, 48], [20, 9, 15, 50], [21, 2, 15, 59], [21, 3, 15, 60], [21, 4, 15, 61], [21, 6, 16, 2], [21, 7, 16, 3], [21, 13, 16, 9], [21, 15, 16, 11], [22, 4, 16, 13, "d"], [22, 5, 16, 14], [22, 7, 16, 16], [22, 71, 16, 80], [23, 4, 16, 82, "key"], [23, 7, 16, 85], [23, 9, 16, 87], [24, 2, 16, 96], [24, 3, 16, 97], [24, 4, 16, 98], [24, 6, 17, 2], [24, 7, 17, 3], [24, 13, 17, 9], [24, 15, 17, 11], [25, 4, 17, 13, "d"], [25, 5, 17, 14], [25, 7, 17, 16], [25, 35, 17, 44], [26, 4, 17, 46, "key"], [26, 7, 17, 49], [26, 9, 17, 51], [27, 2, 17, 60], [27, 3, 17, 61], [27, 4, 17, 62], [27, 6, 18, 2], [27, 7, 18, 3], [27, 13, 18, 9], [27, 15, 18, 11], [28, 4, 18, 13, "d"], [28, 5, 18, 14], [28, 7, 18, 16], [28, 17, 18, 26], [29, 4, 18, 28, "key"], [29, 7, 18, 31], [29, 9, 18, 33], [30, 2, 18, 42], [30, 3, 18, 43], [30, 4, 18, 44], [30, 6, 19, 2], [30, 7, 19, 3], [30, 13, 19, 9], [30, 15, 19, 11], [31, 4, 19, 13, "d"], [31, 5, 19, 14], [31, 7, 19, 16], [31, 15, 19, 24], [32, 4, 19, 26, "key"], [32, 7, 19, 29], [32, 9, 19, 31], [33, 2, 19, 40], [33, 3, 19, 41], [33, 4, 19, 42], [33, 5, 20, 1], [33, 6, 20, 2], [34, 0, 20, 3], [34, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}