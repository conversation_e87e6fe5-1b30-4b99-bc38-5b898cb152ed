{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "./Badge.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 91}, "end": {"line": 5, "column": 35, "index": 126}}], "key": "MZFkiMuDDsiAZkLPfZB0JBL51Ng=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 127}, "end": {"line": 6, "column": 63, "index": 190}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TabBarIcon = TabBarIcon;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Badge = require(_dependencyMap[4], \"./Badge.js\");\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  /**\n   * Icon sizes taken from Apple HIG\n   * https://developer.apple.com/design/human-interface-guidelines/tab-bars\n   */\n  const ICON_SIZE_WIDE = 31;\n  const ICON_SIZE_WIDE_COMPACT = 23;\n  const ICON_SIZE_TALL = 28;\n  const ICON_SIZE_TALL_COMPACT = 20;\n  const ICON_SIZE_ROUND = 25;\n  const ICON_SIZE_ROUND_COMPACT = 18;\n  const ICON_SIZE_MATERIAL = 24;\n  function TabBarIcon({\n    route: _,\n    variant,\n    size,\n    badge,\n    badgeStyle,\n    activeOpacity,\n    inactiveOpacity,\n    activeTintColor,\n    inactiveTintColor,\n    renderIcon,\n    allowFontScaling,\n    style\n  }) {\n    const iconSize = variant === 'material' ? ICON_SIZE_MATERIAL : size === 'compact' ? ICON_SIZE_ROUND_COMPACT : ICON_SIZE_ROUND;\n\n    // We render the icon twice at the same position on top of each other:\n    // active and inactive one, so we can fade between them.\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_View.default, {\n      style: [variant === 'material' ? styles.wrapperMaterial : size === 'compact' ? styles.wrapperUikitCompact : styles.wrapperUikit, style],\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n        style: [styles.icon, {\n          opacity: activeOpacity,\n          // Workaround for react-native >= 0.54 layout bug\n          minWidth: iconSize\n        }],\n        children: renderIcon({\n          focused: true,\n          size: iconSize,\n          color: activeTintColor\n        })\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n        style: [styles.icon, {\n          opacity: inactiveOpacity\n        }],\n        children: renderIcon({\n          focused: false,\n          size: iconSize,\n          color: inactiveTintColor\n        })\n      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_Badge.Badge, {\n        visible: badge != null,\n        size: iconSize * 0.75,\n        allowFontScaling: allowFontScaling,\n        style: [styles.badge, badgeStyle],\n        children: badge\n      })]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    icon: {\n      // We render the icon twice at the same position on top of each other:\n      // active and inactive one, so we can fade between them:\n      // Cover the whole iconContainer:\n      position: 'absolute',\n      alignSelf: 'center',\n      alignItems: 'center',\n      justifyContent: 'center',\n      height: '100%',\n      width: '100%'\n    },\n    wrapperUikit: {\n      width: ICON_SIZE_WIDE,\n      height: ICON_SIZE_TALL\n    },\n    wrapperUikitCompact: {\n      width: ICON_SIZE_WIDE_COMPACT,\n      height: ICON_SIZE_TALL_COMPACT\n    },\n    wrapperMaterial: {\n      width: ICON_SIZE_MATERIAL,\n      height: ICON_SIZE_MATERIAL\n    },\n    badge: {\n      position: 'absolute',\n      end: -3,\n      top: -3\n    }\n  });\n});", "lineCount": 104, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "TabBarIcon"], [8, 20, 1, 13], [8, 23, 1, 13, "TabBarIcon"], [8, 33, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_react"], [9, 12, 3, 0], [9, 15, 3, 0, "_interopRequireDefault"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 3, 26], [10, 6, 3, 26, "_StyleSheet"], [10, 17, 3, 26], [10, 20, 3, 26, "_interopRequireDefault"], [10, 42, 3, 26], [10, 43, 3, 26, "require"], [10, 50, 3, 26], [10, 51, 3, 26, "_dependencyMap"], [10, 65, 3, 26], [11, 2, 3, 26], [11, 6, 3, 26, "_View"], [11, 11, 3, 26], [11, 14, 3, 26, "_interopRequireDefault"], [11, 36, 3, 26], [11, 37, 3, 26, "require"], [11, 44, 3, 26], [11, 45, 3, 26, "_dependencyMap"], [11, 59, 3, 26], [12, 2, 5, 0], [12, 6, 5, 0, "_Badge"], [12, 12, 5, 0], [12, 15, 5, 0, "require"], [12, 22, 5, 0], [12, 23, 5, 0, "_dependencyMap"], [12, 37, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_jsxRuntime"], [13, 17, 6, 0], [13, 20, 6, 0, "require"], [13, 27, 6, 0], [13, 28, 6, 0, "_dependencyMap"], [13, 42, 6, 0], [14, 2, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 2, 11, 0], [18, 8, 11, 6, "ICON_SIZE_WIDE"], [18, 22, 11, 20], [18, 25, 11, 23], [18, 27, 11, 25], [19, 2, 12, 0], [19, 8, 12, 6, "ICON_SIZE_WIDE_COMPACT"], [19, 30, 12, 28], [19, 33, 12, 31], [19, 35, 12, 33], [20, 2, 13, 0], [20, 8, 13, 6, "ICON_SIZE_TALL"], [20, 22, 13, 20], [20, 25, 13, 23], [20, 27, 13, 25], [21, 2, 14, 0], [21, 8, 14, 6, "ICON_SIZE_TALL_COMPACT"], [21, 30, 14, 28], [21, 33, 14, 31], [21, 35, 14, 33], [22, 2, 15, 0], [22, 8, 15, 6, "ICON_SIZE_ROUND"], [22, 23, 15, 21], [22, 26, 15, 24], [22, 28, 15, 26], [23, 2, 16, 0], [23, 8, 16, 6, "ICON_SIZE_ROUND_COMPACT"], [23, 31, 16, 29], [23, 34, 16, 32], [23, 36, 16, 34], [24, 2, 17, 0], [24, 8, 17, 6, "ICON_SIZE_MATERIAL"], [24, 26, 17, 24], [24, 29, 17, 27], [24, 31, 17, 29], [25, 2, 18, 7], [25, 11, 18, 16, "TabBarIcon"], [25, 21, 18, 26, "TabBarIcon"], [25, 22, 18, 27], [26, 4, 19, 2, "route"], [26, 9, 19, 7], [26, 11, 19, 9, "_"], [26, 12, 19, 10], [27, 4, 20, 2, "variant"], [27, 11, 20, 9], [28, 4, 21, 2, "size"], [28, 8, 21, 6], [29, 4, 22, 2, "badge"], [29, 9, 22, 7], [30, 4, 23, 2, "badgeStyle"], [30, 14, 23, 12], [31, 4, 24, 2, "activeOpacity"], [31, 17, 24, 15], [32, 4, 25, 2, "inactiveOpacity"], [32, 19, 25, 17], [33, 4, 26, 2, "activeTintColor"], [33, 19, 26, 17], [34, 4, 27, 2, "inactiveTintColor"], [34, 21, 27, 19], [35, 4, 28, 2, "renderIcon"], [35, 14, 28, 12], [36, 4, 29, 2, "allowFontScaling"], [36, 20, 29, 18], [37, 4, 30, 2, "style"], [38, 2, 31, 0], [38, 3, 31, 1], [38, 5, 31, 3], [39, 4, 32, 2], [39, 10, 32, 8, "iconSize"], [39, 18, 32, 16], [39, 21, 32, 19, "variant"], [39, 28, 32, 26], [39, 33, 32, 31], [39, 43, 32, 41], [39, 46, 32, 44, "ICON_SIZE_MATERIAL"], [39, 64, 32, 62], [39, 67, 32, 65, "size"], [39, 71, 32, 69], [39, 76, 32, 74], [39, 85, 32, 83], [39, 88, 32, 86, "ICON_SIZE_ROUND_COMPACT"], [39, 111, 32, 109], [39, 114, 32, 112, "ICON_SIZE_ROUND"], [39, 129, 32, 127], [41, 4, 34, 2], [42, 4, 35, 2], [43, 4, 36, 2], [43, 11, 36, 9], [43, 24, 36, 22], [43, 28, 36, 22, "_jsxs"], [43, 44, 36, 27], [43, 46, 36, 28, "View"], [43, 59, 36, 32], [43, 61, 36, 34], [44, 6, 37, 4, "style"], [44, 11, 37, 9], [44, 13, 37, 11], [44, 14, 37, 12, "variant"], [44, 21, 37, 19], [44, 26, 37, 24], [44, 36, 37, 34], [44, 39, 37, 37, "styles"], [44, 45, 37, 43], [44, 46, 37, 44, "wrapperMaterial"], [44, 61, 37, 59], [44, 64, 37, 62, "size"], [44, 68, 37, 66], [44, 73, 37, 71], [44, 82, 37, 80], [44, 85, 37, 83, "styles"], [44, 91, 37, 89], [44, 92, 37, 90, "wrapperUikitCompact"], [44, 111, 37, 109], [44, 114, 37, 112, "styles"], [44, 120, 37, 118], [44, 121, 37, 119, "wrapperUikit"], [44, 133, 37, 131], [44, 135, 37, 133, "style"], [44, 140, 37, 138], [44, 141, 37, 139], [45, 6, 38, 4, "children"], [45, 14, 38, 12], [45, 16, 38, 14], [45, 17, 38, 15], [45, 30, 38, 28], [45, 34, 38, 28, "_jsx"], [45, 49, 38, 32], [45, 51, 38, 33, "View"], [45, 64, 38, 37], [45, 66, 38, 39], [46, 8, 39, 6, "style"], [46, 13, 39, 11], [46, 15, 39, 13], [46, 16, 39, 14, "styles"], [46, 22, 39, 20], [46, 23, 39, 21, "icon"], [46, 27, 39, 25], [46, 29, 39, 27], [47, 10, 40, 8, "opacity"], [47, 17, 40, 15], [47, 19, 40, 17, "activeOpacity"], [47, 32, 40, 30], [48, 10, 41, 8], [49, 10, 42, 8, "min<PERSON><PERSON><PERSON>"], [49, 18, 42, 16], [49, 20, 42, 18, "iconSize"], [50, 8, 43, 6], [50, 9, 43, 7], [50, 10, 43, 8], [51, 8, 44, 6, "children"], [51, 16, 44, 14], [51, 18, 44, 16, "renderIcon"], [51, 28, 44, 26], [51, 29, 44, 27], [52, 10, 45, 8, "focused"], [52, 17, 45, 15], [52, 19, 45, 17], [52, 23, 45, 21], [53, 10, 46, 8, "size"], [53, 14, 46, 12], [53, 16, 46, 14, "iconSize"], [53, 24, 46, 22], [54, 10, 47, 8, "color"], [54, 15, 47, 13], [54, 17, 47, 15, "activeTintColor"], [55, 8, 48, 6], [55, 9, 48, 7], [56, 6, 49, 4], [56, 7, 49, 5], [56, 8, 49, 6], [56, 10, 49, 8], [56, 23, 49, 21], [56, 27, 49, 21, "_jsx"], [56, 42, 49, 25], [56, 44, 49, 26, "View"], [56, 57, 49, 30], [56, 59, 49, 32], [57, 8, 50, 6, "style"], [57, 13, 50, 11], [57, 15, 50, 13], [57, 16, 50, 14, "styles"], [57, 22, 50, 20], [57, 23, 50, 21, "icon"], [57, 27, 50, 25], [57, 29, 50, 27], [58, 10, 51, 8, "opacity"], [58, 17, 51, 15], [58, 19, 51, 17, "inactiveOpacity"], [59, 8, 52, 6], [59, 9, 52, 7], [59, 10, 52, 8], [60, 8, 53, 6, "children"], [60, 16, 53, 14], [60, 18, 53, 16, "renderIcon"], [60, 28, 53, 26], [60, 29, 53, 27], [61, 10, 54, 8, "focused"], [61, 17, 54, 15], [61, 19, 54, 17], [61, 24, 54, 22], [62, 10, 55, 8, "size"], [62, 14, 55, 12], [62, 16, 55, 14, "iconSize"], [62, 24, 55, 22], [63, 10, 56, 8, "color"], [63, 15, 56, 13], [63, 17, 56, 15, "inactiveTintColor"], [64, 8, 57, 6], [64, 9, 57, 7], [65, 6, 58, 4], [65, 7, 58, 5], [65, 8, 58, 6], [65, 10, 58, 8], [65, 23, 58, 21], [65, 27, 58, 21, "_jsx"], [65, 42, 58, 25], [65, 44, 58, 26, "Badge"], [65, 56, 58, 31], [65, 58, 58, 33], [66, 8, 59, 6, "visible"], [66, 15, 59, 13], [66, 17, 59, 15, "badge"], [66, 22, 59, 20], [66, 26, 59, 24], [66, 30, 59, 28], [67, 8, 60, 6, "size"], [67, 12, 60, 10], [67, 14, 60, 12, "iconSize"], [67, 22, 60, 20], [67, 25, 60, 23], [67, 29, 60, 27], [68, 8, 61, 6, "allowFontScaling"], [68, 24, 61, 22], [68, 26, 61, 24, "allowFontScaling"], [68, 42, 61, 40], [69, 8, 62, 6, "style"], [69, 13, 62, 11], [69, 15, 62, 13], [69, 16, 62, 14, "styles"], [69, 22, 62, 20], [69, 23, 62, 21, "badge"], [69, 28, 62, 26], [69, 30, 62, 28, "badgeStyle"], [69, 40, 62, 38], [69, 41, 62, 39], [70, 8, 63, 6, "children"], [70, 16, 63, 14], [70, 18, 63, 16, "badge"], [71, 6, 64, 4], [71, 7, 64, 5], [71, 8, 64, 6], [72, 4, 65, 2], [72, 5, 65, 3], [72, 6, 65, 4], [73, 2, 66, 0], [74, 2, 67, 0], [74, 8, 67, 6, "styles"], [74, 14, 67, 12], [74, 17, 67, 15, "StyleSheet"], [74, 36, 67, 25], [74, 37, 67, 26, "create"], [74, 43, 67, 32], [74, 44, 67, 33], [75, 4, 68, 2, "icon"], [75, 8, 68, 6], [75, 10, 68, 8], [76, 6, 69, 4], [77, 6, 70, 4], [78, 6, 71, 4], [79, 6, 72, 4, "position"], [79, 14, 72, 12], [79, 16, 72, 14], [79, 26, 72, 24], [80, 6, 73, 4, "alignSelf"], [80, 15, 73, 13], [80, 17, 73, 15], [80, 25, 73, 23], [81, 6, 74, 4, "alignItems"], [81, 16, 74, 14], [81, 18, 74, 16], [81, 26, 74, 24], [82, 6, 75, 4, "justifyContent"], [82, 20, 75, 18], [82, 22, 75, 20], [82, 30, 75, 28], [83, 6, 76, 4, "height"], [83, 12, 76, 10], [83, 14, 76, 12], [83, 20, 76, 18], [84, 6, 77, 4, "width"], [84, 11, 77, 9], [84, 13, 77, 11], [85, 4, 78, 2], [85, 5, 78, 3], [86, 4, 79, 2, "wrapperUikit"], [86, 16, 79, 14], [86, 18, 79, 16], [87, 6, 80, 4, "width"], [87, 11, 80, 9], [87, 13, 80, 11, "ICON_SIZE_WIDE"], [87, 27, 80, 25], [88, 6, 81, 4, "height"], [88, 12, 81, 10], [88, 14, 81, 12, "ICON_SIZE_TALL"], [89, 4, 82, 2], [89, 5, 82, 3], [90, 4, 83, 2, "wrapperUikitCompact"], [90, 23, 83, 21], [90, 25, 83, 23], [91, 6, 84, 4, "width"], [91, 11, 84, 9], [91, 13, 84, 11, "ICON_SIZE_WIDE_COMPACT"], [91, 35, 84, 33], [92, 6, 85, 4, "height"], [92, 12, 85, 10], [92, 14, 85, 12, "ICON_SIZE_TALL_COMPACT"], [93, 4, 86, 2], [93, 5, 86, 3], [94, 4, 87, 2, "wrapperMaterial"], [94, 19, 87, 17], [94, 21, 87, 19], [95, 6, 88, 4, "width"], [95, 11, 88, 9], [95, 13, 88, 11, "ICON_SIZE_MATERIAL"], [95, 31, 88, 29], [96, 6, 89, 4, "height"], [96, 12, 89, 10], [96, 14, 89, 12, "ICON_SIZE_MATERIAL"], [97, 4, 90, 2], [97, 5, 90, 3], [98, 4, 91, 2, "badge"], [98, 9, 91, 7], [98, 11, 91, 9], [99, 6, 92, 4, "position"], [99, 14, 92, 12], [99, 16, 92, 14], [99, 26, 92, 24], [100, 6, 93, 4, "end"], [100, 9, 93, 7], [100, 11, 93, 9], [100, 12, 93, 10], [100, 13, 93, 11], [101, 6, 94, 4, "top"], [101, 9, 94, 7], [101, 11, 94, 9], [101, 12, 94, 10], [102, 4, 95, 2], [103, 2, 96, 0], [103, 3, 96, 1], [103, 4, 96, 2], [104, 0, 96, 3], [104, 3]], "functionMap": {"names": ["<global>", "TabBarIcon"], "mappings": "AAA;OCiB;CDgD"}}, "type": "js/module"}]}