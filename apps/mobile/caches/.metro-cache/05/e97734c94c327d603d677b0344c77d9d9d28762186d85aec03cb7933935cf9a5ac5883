{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  /**\n   * @internal\n   */\n  var _default = exports.default = (0, _expoModulesCore.requireNativeModule)('ExpoUpdates');\n});", "lineCount": 11, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_default"], [10, 14, 2, 0], [10, 17, 2, 0, "exports"], [10, 24, 2, 0], [10, 25, 2, 0, "default"], [10, 32, 2, 0], [10, 35, 5, 15], [10, 39, 5, 15, "requireNativeModule"], [10, 75, 5, 34], [10, 77, 5, 35], [10, 90, 5, 48], [10, 91, 5, 49], [11, 0, 5, 49], [11, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}