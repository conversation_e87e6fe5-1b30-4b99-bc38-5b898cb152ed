{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 45, "index": 45}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/PixelRatio", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5rdRioKC4qvLVlVTyxLOiQm3IeU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/NativeModules", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MRft9soElPgDBIQyAww27iefYeU=", "exportNames": ["*"]}}, {"name": "./AssetSourceResolver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 104}, "end": {"line": 3, "column": 56, "index": 160}}], "key": "1XREZLfiB40YsGJEOFyn6rrYKeQ=", "exportNames": ["*"]}}, {"name": "./PlatformUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 161}, "end": {"line": 4, "column": 64, "index": 225}}], "key": "vk5TSZJTws6vRpll7frKCvmMWgw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.resolveUri = resolveUri;\n  exports.selectAssetSource = selectAssetSource;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _PixelRatio = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/PixelRatio\"));\n  var _NativeModules = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/NativeModules\"));\n  var _AssetSourceResolver = _interopRequireDefault(require(_dependencyMap[4], \"./AssetSourceResolver\"));\n  var _PlatformUtils = require(_dependencyMap[5], \"./PlatformUtils\");\n  /**\n   * Selects the best file for the given asset (ex: choosing the best scale for images) and returns\n   * a { uri, hash } pair for the specific asset file.\n   *\n   * If the asset isn't an image with multiple scales, the first file is selected.\n   */\n  function selectAssetSource(meta) {\n    // This logic is based on that of AssetSourceResolver, with additional support for file hashes and\n    // explicitly provided URIs\n    const scale = _AssetSourceResolver.default.pickScale(meta.scales, _PixelRatio.default.get());\n    const index = meta.scales.findIndex(s => s === scale);\n    const hash = meta.fileHashes ? meta.fileHashes[index] ?? meta.fileHashes[0] : meta.hash;\n    // Allow asset processors to directly provide the URL to load\n    const uri = meta.fileUris ? meta.fileUris[index] ?? meta.fileUris[0] : meta.uri;\n    if (uri) {\n      return {\n        uri: resolveUri(uri),\n        hash\n      };\n    }\n    const fileScale = scale === 1 ? '' : `@${scale}x`;\n    const fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n    const suffix = `/${encodeURIComponent(meta.name)}${fileScale}${fileExtension}`;\n    const params = new URLSearchParams({\n      platform: _expoModulesCore.Platform.OS,\n      hash: meta.hash\n    });\n    // For assets with a specified absolute URL, we use the existing origin instead of prepending the\n    // development server or production CDN URL origin\n    if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n      const uri = meta.httpServerLocation + suffix + '?' + params;\n      return {\n        uri,\n        hash\n      };\n    }\n    // For assets during development using manifest2, we use the development server's URL origin\n    const manifest2 = (0, _PlatformUtils.getManifest2)();\n    const devServerUrl = manifest2?.extra?.expoGo?.developer ? 'http://' + manifest2.extra.expoGo.debuggerHost : null;\n    if (devServerUrl) {\n      const baseUrl = new URL(meta.httpServerLocation + suffix, devServerUrl);\n      baseUrl.searchParams.set('platform', _expoModulesCore.Platform.OS);\n      baseUrl.searchParams.set('hash', meta.hash);\n      return {\n        uri: baseUrl.href,\n        hash\n      };\n    }\n    // Temporary fallback for loading assets in Expo Go home\n    if (_NativeModules.default['ExponentKernel']) {\n      return {\n        uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`,\n        hash\n      };\n    }\n    // In correctly configured apps, we arrive here if the asset is locally available on disk due to\n    // being managed by expo-updates, and `getLocalAssetUri(hash)` must return a local URI for this\n    // hash. Since the asset is local, we don't have a remote URL and specify an invalid URL (an empty\n    // string) as a placeholder.\n    return {\n      uri: '',\n      hash\n    };\n  }\n  /**\n   * Resolves the given URI to an absolute URI. If the given URI is already an absolute URI, it is\n   * simply returned. Otherwise, if it is a relative URI, it is resolved relative to the manifest's\n   * base URI.\n   */\n  function resolveUri(uri) {\n    // `manifestBaseUrl` is always an absolute URL or `null`.\n    return _PlatformUtils.manifestBaseUrl ? new URL(uri, _PlatformUtils.manifestBaseUrl).href : uri;\n  }\n});", "lineCount": 86, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoModulesCore"], [8, 22, 1, 0], [8, 25, 1, 0, "require"], [8, 32, 1, 0], [8, 33, 1, 0, "_dependencyMap"], [8, 47, 1, 0], [9, 2, 1, 45], [9, 6, 1, 45, "_PixelRatio"], [9, 17, 1, 45], [9, 20, 1, 45, "_interopRequireDefault"], [9, 42, 1, 45], [9, 43, 1, 45, "require"], [9, 50, 1, 45], [9, 51, 1, 45, "_dependencyMap"], [9, 65, 1, 45], [10, 2, 1, 45], [10, 6, 1, 45, "_NativeModules"], [10, 20, 1, 45], [10, 23, 1, 45, "_interopRequireDefault"], [10, 45, 1, 45], [10, 46, 1, 45, "require"], [10, 53, 1, 45], [10, 54, 1, 45, "_dependencyMap"], [10, 68, 1, 45], [11, 2, 3, 0], [11, 6, 3, 0, "_AssetSourceResolver"], [11, 26, 3, 0], [11, 29, 3, 0, "_interopRequireDefault"], [11, 51, 3, 0], [11, 52, 3, 0, "require"], [11, 59, 3, 0], [11, 60, 3, 0, "_dependencyMap"], [11, 74, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_PlatformUtils"], [12, 20, 4, 0], [12, 23, 4, 0, "require"], [12, 30, 4, 0], [12, 31, 4, 0, "_dependencyMap"], [12, 45, 4, 0], [13, 2, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [17, 0, 9, 0], [18, 0, 10, 0], [19, 2, 11, 7], [19, 11, 11, 16, "selectAssetSource"], [19, 28, 11, 33, "selectAssetSource"], [19, 29, 11, 34, "meta"], [19, 33, 11, 38], [19, 35, 11, 40], [20, 4, 12, 4], [21, 4, 13, 4], [22, 4, 14, 4], [22, 10, 14, 10, "scale"], [22, 15, 14, 15], [22, 18, 14, 18, "AssetSourceResolver"], [22, 46, 14, 37], [22, 47, 14, 38, "pickScale"], [22, 56, 14, 47], [22, 57, 14, 48, "meta"], [22, 61, 14, 52], [22, 62, 14, 53, "scales"], [22, 68, 14, 59], [22, 70, 14, 61, "PixelRatio"], [22, 89, 14, 71], [22, 90, 14, 72, "get"], [22, 93, 14, 75], [22, 94, 14, 76], [22, 95, 14, 77], [22, 96, 14, 78], [23, 4, 15, 4], [23, 10, 15, 10, "index"], [23, 15, 15, 15], [23, 18, 15, 18, "meta"], [23, 22, 15, 22], [23, 23, 15, 23, "scales"], [23, 29, 15, 29], [23, 30, 15, 30, "findIndex"], [23, 39, 15, 39], [23, 40, 15, 41, "s"], [23, 41, 15, 42], [23, 45, 15, 47, "s"], [23, 46, 15, 48], [23, 51, 15, 53, "scale"], [23, 56, 15, 58], [23, 57, 15, 59], [24, 4, 16, 4], [24, 10, 16, 10, "hash"], [24, 14, 16, 14], [24, 17, 16, 17, "meta"], [24, 21, 16, 21], [24, 22, 16, 22, "fileHashes"], [24, 32, 16, 32], [24, 35, 16, 36, "meta"], [24, 39, 16, 40], [24, 40, 16, 41, "fileHashes"], [24, 50, 16, 51], [24, 51, 16, 52, "index"], [24, 56, 16, 57], [24, 57, 16, 58], [24, 61, 16, 62, "meta"], [24, 65, 16, 66], [24, 66, 16, 67, "fileHashes"], [24, 76, 16, 77], [24, 77, 16, 78], [24, 78, 16, 79], [24, 79, 16, 80], [24, 82, 16, 84, "meta"], [24, 86, 16, 88], [24, 87, 16, 89, "hash"], [24, 91, 16, 93], [25, 4, 17, 4], [26, 4, 18, 4], [26, 10, 18, 10, "uri"], [26, 13, 18, 13], [26, 16, 18, 16, "meta"], [26, 20, 18, 20], [26, 21, 18, 21, "fileUris"], [26, 29, 18, 29], [26, 32, 18, 33, "meta"], [26, 36, 18, 37], [26, 37, 18, 38, "fileUris"], [26, 45, 18, 46], [26, 46, 18, 47, "index"], [26, 51, 18, 52], [26, 52, 18, 53], [26, 56, 18, 57, "meta"], [26, 60, 18, 61], [26, 61, 18, 62, "fileUris"], [26, 69, 18, 70], [26, 70, 18, 71], [26, 71, 18, 72], [26, 72, 18, 73], [26, 75, 18, 77, "meta"], [26, 79, 18, 81], [26, 80, 18, 82, "uri"], [26, 83, 18, 85], [27, 4, 19, 4], [27, 8, 19, 8, "uri"], [27, 11, 19, 11], [27, 13, 19, 13], [28, 6, 20, 8], [28, 13, 20, 15], [29, 8, 20, 17, "uri"], [29, 11, 20, 20], [29, 13, 20, 22, "resolve<PERSON>ri"], [29, 23, 20, 32], [29, 24, 20, 33, "uri"], [29, 27, 20, 36], [29, 28, 20, 37], [30, 8, 20, 39, "hash"], [31, 6, 20, 44], [31, 7, 20, 45], [32, 4, 21, 4], [33, 4, 22, 4], [33, 10, 22, 10, "fileScale"], [33, 19, 22, 19], [33, 22, 22, 22, "scale"], [33, 27, 22, 27], [33, 32, 22, 32], [33, 33, 22, 33], [33, 36, 22, 36], [33, 38, 22, 38], [33, 41, 22, 41], [33, 45, 22, 45, "scale"], [33, 50, 22, 50], [33, 53, 22, 53], [34, 4, 23, 4], [34, 10, 23, 10, "fileExtension"], [34, 23, 23, 23], [34, 26, 23, 26, "meta"], [34, 30, 23, 30], [34, 31, 23, 31, "type"], [34, 35, 23, 35], [34, 38, 23, 38], [34, 42, 23, 42, "encodeURIComponent"], [34, 60, 23, 60], [34, 61, 23, 61, "meta"], [34, 65, 23, 65], [34, 66, 23, 66, "type"], [34, 70, 23, 70], [34, 71, 23, 71], [34, 73, 23, 73], [34, 76, 23, 76], [34, 78, 23, 78], [35, 4, 24, 4], [35, 10, 24, 10, "suffix"], [35, 16, 24, 16], [35, 19, 24, 19], [35, 23, 24, 23, "encodeURIComponent"], [35, 41, 24, 41], [35, 42, 24, 42, "meta"], [35, 46, 24, 46], [35, 47, 24, 47, "name"], [35, 51, 24, 51], [35, 52, 24, 52], [35, 55, 24, 55, "fileScale"], [35, 64, 24, 64], [35, 67, 24, 67, "fileExtension"], [35, 80, 24, 80], [35, 82, 24, 82], [36, 4, 25, 4], [36, 10, 25, 10, "params"], [36, 16, 25, 16], [36, 19, 25, 19], [36, 23, 25, 23, "URLSearchParams"], [36, 38, 25, 38], [36, 39, 25, 39], [37, 6, 26, 8, "platform"], [37, 14, 26, 16], [37, 16, 26, 18, "Platform"], [37, 41, 26, 26], [37, 42, 26, 27, "OS"], [37, 44, 26, 29], [38, 6, 27, 8, "hash"], [38, 10, 27, 12], [38, 12, 27, 14, "meta"], [38, 16, 27, 18], [38, 17, 27, 19, "hash"], [39, 4, 28, 4], [39, 5, 28, 5], [39, 6, 28, 6], [40, 4, 29, 4], [41, 4, 30, 4], [42, 4, 31, 4], [42, 8, 31, 8], [42, 22, 31, 22], [42, 23, 31, 23, "test"], [42, 27, 31, 27], [42, 28, 31, 28, "meta"], [42, 32, 31, 32], [42, 33, 31, 33, "httpServerLocation"], [42, 51, 31, 51], [42, 52, 31, 52], [42, 54, 31, 54], [43, 6, 32, 8], [43, 12, 32, 14, "uri"], [43, 15, 32, 17], [43, 18, 32, 20, "meta"], [43, 22, 32, 24], [43, 23, 32, 25, "httpServerLocation"], [43, 41, 32, 43], [43, 44, 32, 46, "suffix"], [43, 50, 32, 52], [43, 53, 32, 55], [43, 56, 32, 58], [43, 59, 32, 61, "params"], [43, 65, 32, 67], [44, 6, 33, 8], [44, 13, 33, 15], [45, 8, 33, 17, "uri"], [45, 11, 33, 20], [46, 8, 33, 22, "hash"], [47, 6, 33, 27], [47, 7, 33, 28], [48, 4, 34, 4], [49, 4, 35, 4], [50, 4, 36, 4], [50, 10, 36, 10, "manifest2"], [50, 19, 36, 19], [50, 22, 36, 22], [50, 26, 36, 22, "getManifest2"], [50, 53, 36, 34], [50, 55, 36, 35], [50, 56, 36, 36], [51, 4, 37, 4], [51, 10, 37, 10, "devServerUrl"], [51, 22, 37, 22], [51, 25, 37, 25, "manifest2"], [51, 34, 37, 34], [51, 36, 37, 36, "extra"], [51, 41, 37, 41], [51, 43, 37, 43, "expoGo"], [51, 49, 37, 49], [51, 51, 37, 51, "developer"], [51, 60, 37, 60], [51, 63, 38, 10], [51, 72, 38, 19], [51, 75, 38, 22, "manifest2"], [51, 84, 38, 31], [51, 85, 38, 32, "extra"], [51, 90, 38, 37], [51, 91, 38, 38, "expoGo"], [51, 97, 38, 44], [51, 98, 38, 45, "debuggerHost"], [51, 110, 38, 57], [51, 113, 39, 10], [51, 117, 39, 14], [52, 4, 40, 4], [52, 8, 40, 8, "devServerUrl"], [52, 20, 40, 20], [52, 22, 40, 22], [53, 6, 41, 8], [53, 12, 41, 14, "baseUrl"], [53, 19, 41, 21], [53, 22, 41, 24], [53, 26, 41, 28, "URL"], [53, 29, 41, 31], [53, 30, 41, 32, "meta"], [53, 34, 41, 36], [53, 35, 41, 37, "httpServerLocation"], [53, 53, 41, 55], [53, 56, 41, 58, "suffix"], [53, 62, 41, 64], [53, 64, 41, 66, "devServerUrl"], [53, 76, 41, 78], [53, 77, 41, 79], [54, 6, 42, 8, "baseUrl"], [54, 13, 42, 15], [54, 14, 42, 16, "searchParams"], [54, 26, 42, 28], [54, 27, 42, 29, "set"], [54, 30, 42, 32], [54, 31, 42, 33], [54, 41, 42, 43], [54, 43, 42, 45, "Platform"], [54, 68, 42, 53], [54, 69, 42, 54, "OS"], [54, 71, 42, 56], [54, 72, 42, 57], [55, 6, 43, 8, "baseUrl"], [55, 13, 43, 15], [55, 14, 43, 16, "searchParams"], [55, 26, 43, 28], [55, 27, 43, 29, "set"], [55, 30, 43, 32], [55, 31, 43, 33], [55, 37, 43, 39], [55, 39, 43, 41, "meta"], [55, 43, 43, 45], [55, 44, 43, 46, "hash"], [55, 48, 43, 50], [55, 49, 43, 51], [56, 6, 44, 8], [56, 13, 44, 15], [57, 8, 45, 12, "uri"], [57, 11, 45, 15], [57, 13, 45, 17, "baseUrl"], [57, 20, 45, 24], [57, 21, 45, 25, "href"], [57, 25, 45, 29], [58, 8, 46, 12, "hash"], [59, 6, 47, 8], [59, 7, 47, 9], [60, 4, 48, 4], [61, 4, 49, 4], [62, 4, 50, 4], [62, 8, 50, 8, "NativeModules"], [62, 30, 50, 21], [62, 31, 50, 22], [62, 47, 50, 38], [62, 48, 50, 39], [62, 50, 50, 41], [63, 6, 51, 8], [63, 13, 51, 15], [64, 8, 51, 17, "uri"], [64, 11, 51, 20], [64, 13, 51, 22], [64, 58, 51, 67, "encodeURIComponent"], [64, 76, 51, 85], [64, 77, 51, 86, "hash"], [64, 81, 51, 90], [64, 82, 51, 91], [64, 84, 51, 93], [65, 8, 51, 95, "hash"], [66, 6, 51, 100], [66, 7, 51, 101], [67, 4, 52, 4], [68, 4, 53, 4], [69, 4, 54, 4], [70, 4, 55, 4], [71, 4, 56, 4], [72, 4, 57, 4], [72, 11, 57, 11], [73, 6, 57, 13, "uri"], [73, 9, 57, 16], [73, 11, 57, 18], [73, 13, 57, 20], [74, 6, 57, 22, "hash"], [75, 4, 57, 27], [75, 5, 57, 28], [76, 2, 58, 0], [77, 2, 59, 0], [78, 0, 60, 0], [79, 0, 61, 0], [80, 0, 62, 0], [81, 0, 63, 0], [82, 2, 64, 7], [82, 11, 64, 16, "resolve<PERSON>ri"], [82, 21, 64, 26, "resolve<PERSON>ri"], [82, 22, 64, 27, "uri"], [82, 25, 64, 30], [82, 27, 64, 32], [83, 4, 65, 4], [84, 4, 66, 4], [84, 11, 66, 11, "manifestBaseUrl"], [84, 41, 66, 26], [84, 44, 66, 29], [84, 48, 66, 33, "URL"], [84, 51, 66, 36], [84, 52, 66, 37, "uri"], [84, 55, 66, 40], [84, 57, 66, 42, "manifestBaseUrl"], [84, 87, 66, 57], [84, 88, 66, 58], [84, 89, 66, 59, "href"], [84, 93, 66, 63], [84, 96, 66, 66, "uri"], [84, 99, 66, 69], [85, 2, 67, 0], [86, 0, 67, 1], [86, 3]], "functionMap": {"names": ["<global>", "selectAssetSource", "meta.scales.findIndex$argument_0", "resolve<PERSON>ri"], "mappings": "AAA;OCU;wCCI,kBD;CD2C;OGM;CHG"}}, "type": "js/module"}]}