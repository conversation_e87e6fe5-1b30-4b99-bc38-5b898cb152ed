{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 52}, "end": {"line": 2, "column": 60, "index": 112}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 113}, "end": {"line": 3, "column": 58, "index": 171}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "./useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 172}, "end": {"line": 4, "column": 40, "index": 212}}], "key": "qBoul5KQ1+OEu8G3Sr6Tlb7g7CM=", "exportNames": ["*"]}}, {"name": "./AudioVisualizer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 213}, "end": {"line": 5, "column": 48, "index": 261}}], "key": "PtmazYoURS57fsddHvpL2PH220Y=", "exportNames": ["*"]}}, {"name": "./RealtimeTranscript", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 54, "index": 316}}], "key": "r6gm1hrdQBRgTrcSPkygz/enaRs=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = VoiceMode;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _lucideReactNative = require(_dependencyMap[4], \"lucide-react-native\");\n  var _useColors = require(_dependencyMap[5], \"./useColors\");\n  var _AudioVisualizer = _interopRequireDefault(require(_dependencyMap[6], \"./AudioVisualizer\"));\n  var _RealtimeTranscript = _interopRequireDefault(require(_dependencyMap[7], \"./RealtimeTranscript\"));\n  var _jsxDevRuntime = require(_dependencyMap[8], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/components/VoiceMode.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function VoiceMode(_ref) {\n    _s();\n    var isRecording = _ref.isRecording,\n      onRecord = _ref.onRecord,\n      hasPermission = _ref.hasPermission,\n      isLoading = _ref.isLoading,\n      transcript = _ref.transcript,\n      isMuted = _ref.isMuted,\n      onMute = _ref.onMute;\n    var colors = (0, _useColors.useColors)();\n    var _useState = (0, _react.useState)(false),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      isSpeaking = _useState2[0],\n      setIsSpeaking = _useState2[1];\n    (0, _react.useEffect)(() => {\n      // Simulate speaking state for the visualizer\n      if (isRecording) {\n        setIsSpeaking(true);\n      } else {\n        setIsSpeaking(false);\n      }\n    }, [isRecording]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_AudioVisualizer.default, {\n        isSpeaking: isSpeaking\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_RealtimeTranscript.default, {\n        transcript: transcript\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          position: 'absolute',\n          bottom: 0,\n          left: 0,\n          right: 0,\n          alignItems: 'center',\n          gap: 12,\n          paddingHorizontal: 16,\n          paddingTop: 16,\n          paddingBottom: 16,\n          backgroundColor: 'transparent'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n          style: {\n            width: 72,\n            height: 72,\n            borderRadius: 36,\n            backgroundColor: isRecording ? '#FF3B30' : colors.primary,\n            alignItems: 'center',\n            justifyContent: 'center',\n            shadowColor: '#000',\n            shadowOffset: {\n              width: 0,\n              height: 2\n            },\n            shadowOpacity: 0.2,\n            shadowRadius: 4,\n            elevation: 4\n          },\n          onPress: onRecord,\n          disabled: !hasPermission || isLoading,\n          children: isRecording ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Square, {\n            size: 28,\n            color: colors.background,\n            fill: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n            size: 28,\n            color: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n          style: {\n            fontSize: 14,\n            fontFamily: 'Poppins_400Regular',\n            color: colors.textSecondary,\n            textAlign: 'center'\n          },\n          children: isRecording ? 'Tap to stop recording' : hasPermission ? 'Tap and hold to record' : 'Microphone permission needed'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n          onPress: onPause,\n          style: {\n            marginTop: 16\n          },\n          children: isPaused ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Play, {\n            size: 24,\n            color: colors.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Pause, {\n            size: 24,\n            color: colors.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 5\n    }, this);\n  }\n  _s(VoiceMode, \"je5mBwrzKqZY+g+64G/AzRwD0LM=\", false, function () {\n    return [_useColors.useColors];\n  });\n  _c = VoiceMode;\n  var _c;\n  $RefreshReg$(_c, \"VoiceMode\");\n});", "lineCount": 162, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_lucideReactNative"], [10, 24, 3, 0], [10, 27, 3, 0, "require"], [10, 34, 3, 0], [10, 35, 3, 0, "_dependencyMap"], [10, 49, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_useColors"], [11, 16, 4, 0], [11, 19, 4, 0, "require"], [11, 26, 4, 0], [11, 27, 4, 0, "_dependencyMap"], [11, 41, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_AudioVisualizer"], [12, 22, 5, 0], [12, 25, 5, 0, "_interopRequireDefault"], [12, 47, 5, 0], [12, 48, 5, 0, "require"], [12, 55, 5, 0], [12, 56, 5, 0, "_dependencyMap"], [12, 70, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_RealtimeTranscript"], [13, 25, 6, 0], [13, 28, 6, 0, "_interopRequireDefault"], [13, 50, 6, 0], [13, 51, 6, 0, "require"], [13, 58, 6, 0], [13, 59, 6, 0, "_dependencyMap"], [13, 73, 6, 0], [14, 2, 6, 54], [14, 6, 6, 54, "_jsxDevRuntime"], [14, 20, 6, 54], [14, 23, 6, 54, "require"], [14, 30, 6, 54], [14, 31, 6, 54, "_dependencyMap"], [14, 45, 6, 54], [15, 2, 6, 54], [15, 6, 6, 54, "_jsxFileName"], [15, 18, 6, 54], [16, 4, 6, 54, "_s"], [16, 6, 6, 54], [16, 9, 6, 54, "$RefreshSig$"], [16, 21, 6, 54], [17, 2, 6, 54], [17, 11, 6, 54, "_interopRequireWildcard"], [17, 35, 6, 54, "e"], [17, 36, 6, 54], [17, 38, 6, 54, "t"], [17, 39, 6, 54], [17, 68, 6, 54, "WeakMap"], [17, 75, 6, 54], [17, 81, 6, 54, "r"], [17, 82, 6, 54], [17, 89, 6, 54, "WeakMap"], [17, 96, 6, 54], [17, 100, 6, 54, "n"], [17, 101, 6, 54], [17, 108, 6, 54, "WeakMap"], [17, 115, 6, 54], [17, 127, 6, 54, "_interopRequireWildcard"], [17, 150, 6, 54], [17, 162, 6, 54, "_interopRequireWildcard"], [17, 163, 6, 54, "e"], [17, 164, 6, 54], [17, 166, 6, 54, "t"], [17, 167, 6, 54], [17, 176, 6, 54, "t"], [17, 177, 6, 54], [17, 181, 6, 54, "e"], [17, 182, 6, 54], [17, 186, 6, 54, "e"], [17, 187, 6, 54], [17, 188, 6, 54, "__esModule"], [17, 198, 6, 54], [17, 207, 6, 54, "e"], [17, 208, 6, 54], [17, 214, 6, 54, "o"], [17, 215, 6, 54], [17, 217, 6, 54, "i"], [17, 218, 6, 54], [17, 220, 6, 54, "f"], [17, 221, 6, 54], [17, 226, 6, 54, "__proto__"], [17, 235, 6, 54], [17, 243, 6, 54, "default"], [17, 250, 6, 54], [17, 252, 6, 54, "e"], [17, 253, 6, 54], [17, 270, 6, 54, "e"], [17, 271, 6, 54], [17, 294, 6, 54, "e"], [17, 295, 6, 54], [17, 320, 6, 54, "e"], [17, 321, 6, 54], [17, 330, 6, 54, "f"], [17, 331, 6, 54], [17, 337, 6, 54, "o"], [17, 338, 6, 54], [17, 341, 6, 54, "t"], [17, 342, 6, 54], [17, 345, 6, 54, "n"], [17, 346, 6, 54], [17, 349, 6, 54, "r"], [17, 350, 6, 54], [17, 358, 6, 54, "o"], [17, 359, 6, 54], [17, 360, 6, 54, "has"], [17, 363, 6, 54], [17, 364, 6, 54, "e"], [17, 365, 6, 54], [17, 375, 6, 54, "o"], [17, 376, 6, 54], [17, 377, 6, 54, "get"], [17, 380, 6, 54], [17, 381, 6, 54, "e"], [17, 382, 6, 54], [17, 385, 6, 54, "o"], [17, 386, 6, 54], [17, 387, 6, 54, "set"], [17, 390, 6, 54], [17, 391, 6, 54, "e"], [17, 392, 6, 54], [17, 394, 6, 54, "f"], [17, 395, 6, 54], [17, 409, 6, 54, "_t"], [17, 411, 6, 54], [17, 415, 6, 54, "e"], [17, 416, 6, 54], [17, 432, 6, 54, "_t"], [17, 434, 6, 54], [17, 441, 6, 54, "hasOwnProperty"], [17, 455, 6, 54], [17, 456, 6, 54, "call"], [17, 460, 6, 54], [17, 461, 6, 54, "e"], [17, 462, 6, 54], [17, 464, 6, 54, "_t"], [17, 466, 6, 54], [17, 473, 6, 54, "i"], [17, 474, 6, 54], [17, 478, 6, 54, "o"], [17, 479, 6, 54], [17, 482, 6, 54, "Object"], [17, 488, 6, 54], [17, 489, 6, 54, "defineProperty"], [17, 503, 6, 54], [17, 508, 6, 54, "Object"], [17, 514, 6, 54], [17, 515, 6, 54, "getOwnPropertyDescriptor"], [17, 539, 6, 54], [17, 540, 6, 54, "e"], [17, 541, 6, 54], [17, 543, 6, 54, "_t"], [17, 545, 6, 54], [17, 552, 6, 54, "i"], [17, 553, 6, 54], [17, 554, 6, 54, "get"], [17, 557, 6, 54], [17, 561, 6, 54, "i"], [17, 562, 6, 54], [17, 563, 6, 54, "set"], [17, 566, 6, 54], [17, 570, 6, 54, "o"], [17, 571, 6, 54], [17, 572, 6, 54, "f"], [17, 573, 6, 54], [17, 575, 6, 54, "_t"], [17, 577, 6, 54], [17, 579, 6, 54, "i"], [17, 580, 6, 54], [17, 584, 6, 54, "f"], [17, 585, 6, 54], [17, 586, 6, 54, "_t"], [17, 588, 6, 54], [17, 592, 6, 54, "e"], [17, 593, 6, 54], [17, 594, 6, 54, "_t"], [17, 596, 6, 54], [17, 607, 6, 54, "f"], [17, 608, 6, 54], [17, 613, 6, 54, "e"], [17, 614, 6, 54], [17, 616, 6, 54, "t"], [17, 617, 6, 54], [18, 2, 8, 15], [18, 11, 8, 24, "VoiceMode"], [18, 20, 8, 33, "VoiceMode"], [18, 21, 8, 33, "_ref"], [18, 25, 8, 33], [18, 27, 8, 116], [19, 4, 8, 116, "_s"], [19, 6, 8, 116], [20, 4, 8, 116], [20, 8, 8, 36, "isRecording"], [20, 19, 8, 47], [20, 22, 8, 47, "_ref"], [20, 26, 8, 47], [20, 27, 8, 36, "isRecording"], [20, 38, 8, 47], [21, 6, 8, 49, "onRecord"], [21, 14, 8, 57], [21, 17, 8, 57, "_ref"], [21, 21, 8, 57], [21, 22, 8, 49, "onRecord"], [21, 30, 8, 57], [22, 6, 8, 59, "hasPermission"], [22, 19, 8, 72], [22, 22, 8, 72, "_ref"], [22, 26, 8, 72], [22, 27, 8, 59, "hasPermission"], [22, 40, 8, 72], [23, 6, 8, 74, "isLoading"], [23, 15, 8, 83], [23, 18, 8, 83, "_ref"], [23, 22, 8, 83], [23, 23, 8, 74, "isLoading"], [23, 32, 8, 83], [24, 6, 8, 85, "transcript"], [24, 16, 8, 95], [24, 19, 8, 95, "_ref"], [24, 23, 8, 95], [24, 24, 8, 85, "transcript"], [24, 34, 8, 95], [25, 6, 8, 97, "isMuted"], [25, 13, 8, 104], [25, 16, 8, 104, "_ref"], [25, 20, 8, 104], [25, 21, 8, 97, "isMuted"], [25, 28, 8, 104], [26, 6, 8, 106, "onMute"], [26, 12, 8, 112], [26, 15, 8, 112, "_ref"], [26, 19, 8, 112], [26, 20, 8, 106, "onMute"], [26, 26, 8, 112], [27, 4, 9, 2], [27, 8, 9, 8, "colors"], [27, 14, 9, 14], [27, 17, 9, 17], [27, 21, 9, 17, "useColors"], [27, 41, 9, 26], [27, 43, 9, 27], [27, 44, 9, 28], [28, 4, 10, 2], [28, 8, 10, 2, "_useState"], [28, 17, 10, 2], [28, 20, 10, 38], [28, 24, 10, 38, "useState"], [28, 39, 10, 46], [28, 41, 10, 47], [28, 46, 10, 52], [28, 47, 10, 53], [29, 6, 10, 53, "_useState2"], [29, 16, 10, 53], [29, 23, 10, 53, "_slicedToArray2"], [29, 38, 10, 53], [29, 39, 10, 53, "default"], [29, 46, 10, 53], [29, 48, 10, 53, "_useState"], [29, 57, 10, 53], [30, 6, 10, 9, "isSpeaking"], [30, 16, 10, 19], [30, 19, 10, 19, "_useState2"], [30, 29, 10, 19], [31, 6, 10, 21, "setIsSpeaking"], [31, 19, 10, 34], [31, 22, 10, 34, "_useState2"], [31, 32, 10, 34], [32, 4, 12, 2], [32, 8, 12, 2, "useEffect"], [32, 24, 12, 11], [32, 26, 12, 12], [32, 32, 12, 18], [33, 6, 13, 4], [34, 6, 14, 4], [34, 10, 14, 8, "isRecording"], [34, 21, 14, 19], [34, 23, 14, 21], [35, 8, 15, 6, "setIsSpeaking"], [35, 21, 15, 19], [35, 22, 15, 20], [35, 26, 15, 24], [35, 27, 15, 25], [36, 6, 16, 4], [36, 7, 16, 5], [36, 13, 16, 11], [37, 8, 17, 6, "setIsSpeaking"], [37, 21, 17, 19], [37, 22, 17, 20], [37, 27, 17, 25], [37, 28, 17, 26], [38, 6, 18, 4], [39, 4, 19, 2], [39, 5, 19, 3], [39, 7, 19, 5], [39, 8, 19, 6, "isRecording"], [39, 19, 19, 17], [39, 20, 19, 18], [39, 21, 19, 19], [40, 4, 21, 2], [40, 24, 22, 4], [40, 28, 22, 4, "_jsxDevRuntime"], [40, 42, 22, 4], [40, 43, 22, 4, "jsxDEV"], [40, 49, 22, 4], [40, 51, 22, 5, "_reactNative"], [40, 63, 22, 5], [40, 64, 22, 5, "View"], [40, 68, 22, 9], [41, 6, 22, 10, "style"], [41, 11, 22, 15], [41, 13, 22, 17], [42, 8, 22, 19, "flex"], [42, 12, 22, 23], [42, 14, 22, 25], [43, 6, 22, 27], [43, 7, 22, 29], [44, 6, 22, 29, "children"], [44, 14, 22, 29], [44, 30, 23, 6], [44, 34, 23, 6, "_jsxDevRuntime"], [44, 48, 23, 6], [44, 49, 23, 6, "jsxDEV"], [44, 55, 23, 6], [44, 57, 23, 7, "_AudioVisualizer"], [44, 73, 23, 7], [44, 74, 23, 7, "default"], [44, 81, 23, 22], [45, 8, 23, 23, "isSpeaking"], [45, 18, 23, 33], [45, 20, 23, 35, "isSpeaking"], [46, 6, 23, 46], [47, 8, 23, 46, "fileName"], [47, 16, 23, 46], [47, 18, 23, 46, "_jsxFileName"], [47, 30, 23, 46], [48, 8, 23, 46, "lineNumber"], [48, 18, 23, 46], [49, 8, 23, 46, "columnNumber"], [49, 20, 23, 46], [50, 6, 23, 46], [50, 13, 23, 48], [50, 14, 23, 49], [50, 29, 24, 6], [50, 33, 24, 6, "_jsxDevRuntime"], [50, 47, 24, 6], [50, 48, 24, 6, "jsxDEV"], [50, 54, 24, 6], [50, 56, 24, 7, "_RealtimeTranscript"], [50, 75, 24, 7], [50, 76, 24, 7, "default"], [50, 83, 24, 25], [51, 8, 24, 26, "transcript"], [51, 18, 24, 36], [51, 20, 24, 38, "transcript"], [52, 6, 24, 49], [53, 8, 24, 49, "fileName"], [53, 16, 24, 49], [53, 18, 24, 49, "_jsxFileName"], [53, 30, 24, 49], [54, 8, 24, 49, "lineNumber"], [54, 18, 24, 49], [55, 8, 24, 49, "columnNumber"], [55, 20, 24, 49], [56, 6, 24, 49], [56, 13, 24, 51], [56, 14, 24, 52], [56, 29, 25, 6], [56, 33, 25, 6, "_jsxDevRuntime"], [56, 47, 25, 6], [56, 48, 25, 6, "jsxDEV"], [56, 54, 25, 6], [56, 56, 25, 7, "_reactNative"], [56, 68, 25, 7], [56, 69, 25, 7, "View"], [56, 73, 25, 11], [57, 8, 26, 8, "style"], [57, 13, 26, 13], [57, 15, 26, 15], [58, 10, 27, 10, "position"], [58, 18, 27, 18], [58, 20, 27, 20], [58, 30, 27, 30], [59, 10, 28, 10, "bottom"], [59, 16, 28, 16], [59, 18, 28, 18], [59, 19, 28, 19], [60, 10, 29, 10, "left"], [60, 14, 29, 14], [60, 16, 29, 16], [60, 17, 29, 17], [61, 10, 30, 10, "right"], [61, 15, 30, 15], [61, 17, 30, 17], [61, 18, 30, 18], [62, 10, 31, 10, "alignItems"], [62, 20, 31, 20], [62, 22, 31, 22], [62, 30, 31, 30], [63, 10, 32, 10, "gap"], [63, 13, 32, 13], [63, 15, 32, 15], [63, 17, 32, 17], [64, 10, 33, 10, "paddingHorizontal"], [64, 27, 33, 27], [64, 29, 33, 29], [64, 31, 33, 31], [65, 10, 34, 10, "paddingTop"], [65, 20, 34, 20], [65, 22, 34, 22], [65, 24, 34, 24], [66, 10, 35, 10, "paddingBottom"], [66, 23, 35, 23], [66, 25, 35, 25], [66, 27, 35, 27], [67, 10, 36, 10, "backgroundColor"], [67, 25, 36, 25], [67, 27, 36, 27], [68, 8, 37, 8], [68, 9, 37, 10], [69, 8, 37, 10, "children"], [69, 16, 37, 10], [69, 32, 39, 8], [69, 36, 39, 8, "_jsxDevRuntime"], [69, 50, 39, 8], [69, 51, 39, 8, "jsxDEV"], [69, 57, 39, 8], [69, 59, 39, 9, "_reactNative"], [69, 71, 39, 9], [69, 72, 39, 9, "TouchableOpacity"], [69, 88, 39, 25], [70, 10, 40, 10, "style"], [70, 15, 40, 15], [70, 17, 40, 17], [71, 12, 41, 12, "width"], [71, 17, 41, 17], [71, 19, 41, 19], [71, 21, 41, 21], [72, 12, 42, 12, "height"], [72, 18, 42, 18], [72, 20, 42, 20], [72, 22, 42, 22], [73, 12, 43, 12, "borderRadius"], [73, 24, 43, 24], [73, 26, 43, 26], [73, 28, 43, 28], [74, 12, 44, 12, "backgroundColor"], [74, 27, 44, 27], [74, 29, 44, 29, "isRecording"], [74, 40, 44, 40], [74, 43, 44, 43], [74, 52, 44, 52], [74, 55, 44, 55, "colors"], [74, 61, 44, 61], [74, 62, 44, 62, "primary"], [74, 69, 44, 69], [75, 12, 45, 12, "alignItems"], [75, 22, 45, 22], [75, 24, 45, 24], [75, 32, 45, 32], [76, 12, 46, 12, "justifyContent"], [76, 26, 46, 26], [76, 28, 46, 28], [76, 36, 46, 36], [77, 12, 47, 12, "shadowColor"], [77, 23, 47, 23], [77, 25, 47, 25], [77, 31, 47, 31], [78, 12, 48, 12, "shadowOffset"], [78, 24, 48, 24], [78, 26, 48, 26], [79, 14, 48, 28, "width"], [79, 19, 48, 33], [79, 21, 48, 35], [79, 22, 48, 36], [80, 14, 48, 38, "height"], [80, 20, 48, 44], [80, 22, 48, 46], [81, 12, 48, 48], [81, 13, 48, 49], [82, 12, 49, 12, "shadowOpacity"], [82, 25, 49, 25], [82, 27, 49, 27], [82, 30, 49, 30], [83, 12, 50, 12, "shadowRadius"], [83, 24, 50, 24], [83, 26, 50, 26], [83, 27, 50, 27], [84, 12, 51, 12, "elevation"], [84, 21, 51, 21], [84, 23, 51, 23], [85, 10, 52, 10], [85, 11, 52, 12], [86, 10, 53, 10, "onPress"], [86, 17, 53, 17], [86, 19, 53, 19, "onRecord"], [86, 27, 53, 28], [87, 10, 54, 10, "disabled"], [87, 18, 54, 18], [87, 20, 54, 20], [87, 21, 54, 21, "hasPermission"], [87, 34, 54, 34], [87, 38, 54, 38, "isLoading"], [87, 47, 54, 48], [88, 10, 54, 48, "children"], [88, 18, 54, 48], [88, 20, 56, 11, "isRecording"], [88, 31, 56, 22], [88, 47, 57, 12], [88, 51, 57, 12, "_jsxDevRuntime"], [88, 65, 57, 12], [88, 66, 57, 12, "jsxDEV"], [88, 72, 57, 12], [88, 74, 57, 13, "_lucideReactNative"], [88, 92, 57, 13], [88, 93, 57, 13, "Square"], [88, 99, 57, 19], [89, 12, 57, 20, "size"], [89, 16, 57, 24], [89, 18, 57, 26], [89, 20, 57, 29], [90, 12, 57, 30, "color"], [90, 17, 57, 35], [90, 19, 57, 37, "colors"], [90, 25, 57, 43], [90, 26, 57, 44, "background"], [90, 36, 57, 55], [91, 12, 57, 56, "fill"], [91, 16, 57, 60], [91, 18, 57, 62, "colors"], [91, 24, 57, 68], [91, 25, 57, 69, "background"], [92, 10, 57, 80], [93, 12, 57, 80, "fileName"], [93, 20, 57, 80], [93, 22, 57, 80, "_jsxFileName"], [93, 34, 57, 80], [94, 12, 57, 80, "lineNumber"], [94, 22, 57, 80], [95, 12, 57, 80, "columnNumber"], [95, 24, 57, 80], [96, 10, 57, 80], [96, 17, 57, 82], [96, 18, 57, 83], [96, 34, 59, 12], [96, 38, 59, 12, "_jsxDevRuntime"], [96, 52, 59, 12], [96, 53, 59, 12, "jsxDEV"], [96, 59, 59, 12], [96, 61, 59, 13, "_lucideReactNative"], [96, 79, 59, 13], [96, 80, 59, 13, "Mic"], [96, 83, 59, 16], [97, 12, 59, 17, "size"], [97, 16, 59, 21], [97, 18, 59, 23], [97, 20, 59, 26], [98, 12, 59, 27, "color"], [98, 17, 59, 32], [98, 19, 59, 34, "colors"], [98, 25, 59, 40], [98, 26, 59, 41, "background"], [99, 10, 59, 52], [100, 12, 59, 52, "fileName"], [100, 20, 59, 52], [100, 22, 59, 52, "_jsxFileName"], [100, 34, 59, 52], [101, 12, 59, 52, "lineNumber"], [101, 22, 59, 52], [102, 12, 59, 52, "columnNumber"], [102, 24, 59, 52], [103, 10, 59, 52], [103, 17, 59, 54], [104, 8, 60, 11], [105, 10, 60, 11, "fileName"], [105, 18, 60, 11], [105, 20, 60, 11, "_jsxFileName"], [105, 32, 60, 11], [106, 10, 60, 11, "lineNumber"], [106, 20, 60, 11], [107, 10, 60, 11, "columnNumber"], [107, 22, 60, 11], [108, 8, 60, 11], [108, 15, 61, 26], [108, 16, 61, 27], [108, 31, 62, 8], [108, 35, 62, 8, "_jsxDevRuntime"], [108, 49, 62, 8], [108, 50, 62, 8, "jsxDEV"], [108, 56, 62, 8], [108, 58, 62, 9, "_reactNative"], [108, 70, 62, 9], [108, 71, 62, 9, "Text"], [108, 75, 62, 13], [109, 10, 63, 10, "style"], [109, 15, 63, 15], [109, 17, 63, 17], [110, 12, 64, 12, "fontSize"], [110, 20, 64, 20], [110, 22, 64, 22], [110, 24, 64, 24], [111, 12, 65, 12, "fontFamily"], [111, 22, 65, 22], [111, 24, 65, 24], [111, 44, 65, 44], [112, 12, 66, 12, "color"], [112, 17, 66, 17], [112, 19, 66, 19, "colors"], [112, 25, 66, 25], [112, 26, 66, 26, "textSecondary"], [112, 39, 66, 39], [113, 12, 67, 12, "textAlign"], [113, 21, 67, 21], [113, 23, 67, 23], [114, 10, 68, 10], [114, 11, 68, 12], [115, 10, 68, 12, "children"], [115, 18, 68, 12], [115, 20, 70, 11, "isRecording"], [115, 31, 70, 22], [115, 34, 71, 14], [115, 57, 71, 37], [115, 60, 72, 14, "hasPermission"], [115, 73, 72, 27], [115, 76, 73, 16], [115, 100, 73, 40], [115, 103, 74, 16], [116, 8, 74, 46], [117, 10, 74, 46, "fileName"], [117, 18, 74, 46], [117, 20, 74, 46, "_jsxFileName"], [117, 32, 74, 46], [118, 10, 74, 46, "lineNumber"], [118, 20, 74, 46], [119, 10, 74, 46, "columnNumber"], [119, 22, 74, 46], [120, 8, 74, 46], [120, 15, 75, 14], [120, 16, 75, 15], [120, 31, 76, 8], [120, 35, 76, 8, "_jsxDevRuntime"], [120, 49, 76, 8], [120, 50, 76, 8, "jsxDEV"], [120, 56, 76, 8], [120, 58, 76, 9, "_reactNative"], [120, 70, 76, 9], [120, 71, 76, 9, "TouchableOpacity"], [120, 87, 76, 25], [121, 10, 76, 26, "onPress"], [121, 17, 76, 33], [121, 19, 76, 35, "onPause"], [121, 26, 76, 43], [122, 10, 76, 44, "style"], [122, 15, 76, 49], [122, 17, 76, 51], [123, 12, 76, 53, "marginTop"], [123, 21, 76, 62], [123, 23, 76, 64], [124, 10, 76, 67], [124, 11, 76, 69], [125, 10, 76, 69, "children"], [125, 18, 76, 69], [125, 20, 77, 11, "isPaused"], [125, 28, 77, 19], [125, 44, 78, 12], [125, 48, 78, 12, "_jsxDevRuntime"], [125, 62, 78, 12], [125, 63, 78, 12, "jsxDEV"], [125, 69, 78, 12], [125, 71, 78, 13, "Play"], [125, 75, 78, 17], [126, 12, 78, 18, "size"], [126, 16, 78, 22], [126, 18, 78, 24], [126, 20, 78, 27], [127, 12, 78, 28, "color"], [127, 17, 78, 33], [127, 19, 78, 35, "colors"], [127, 25, 78, 41], [127, 26, 78, 42, "text"], [128, 10, 78, 47], [129, 12, 78, 47, "fileName"], [129, 20, 78, 47], [129, 22, 78, 47, "_jsxFileName"], [129, 34, 78, 47], [130, 12, 78, 47, "lineNumber"], [130, 22, 78, 47], [131, 12, 78, 47, "columnNumber"], [131, 24, 78, 47], [132, 10, 78, 47], [132, 17, 78, 49], [132, 18, 78, 50], [132, 34, 80, 12], [132, 38, 80, 12, "_jsxDevRuntime"], [132, 52, 80, 12], [132, 53, 80, 12, "jsxDEV"], [132, 59, 80, 12], [132, 61, 80, 13, "Pause"], [132, 66, 80, 18], [133, 12, 80, 19, "size"], [133, 16, 80, 23], [133, 18, 80, 25], [133, 20, 80, 28], [134, 12, 80, 29, "color"], [134, 17, 80, 34], [134, 19, 80, 36, "colors"], [134, 25, 80, 42], [134, 26, 80, 43, "text"], [135, 10, 80, 48], [136, 12, 80, 48, "fileName"], [136, 20, 80, 48], [136, 22, 80, 48, "_jsxFileName"], [136, 34, 80, 48], [137, 12, 80, 48, "lineNumber"], [137, 22, 80, 48], [138, 12, 80, 48, "columnNumber"], [138, 24, 80, 48], [139, 10, 80, 48], [139, 17, 80, 50], [140, 8, 81, 11], [141, 10, 81, 11, "fileName"], [141, 18, 81, 11], [141, 20, 81, 11, "_jsxFileName"], [141, 32, 81, 11], [142, 10, 81, 11, "lineNumber"], [142, 20, 81, 11], [143, 10, 81, 11, "columnNumber"], [143, 22, 81, 11], [144, 8, 81, 11], [144, 15, 82, 26], [144, 16, 82, 27], [145, 6, 82, 27], [146, 8, 82, 27, "fileName"], [146, 16, 82, 27], [146, 18, 82, 27, "_jsxFileName"], [146, 30, 82, 27], [147, 8, 82, 27, "lineNumber"], [147, 18, 82, 27], [148, 8, 82, 27, "columnNumber"], [148, 20, 82, 27], [149, 6, 82, 27], [149, 13, 83, 12], [149, 14, 83, 13], [150, 4, 83, 13], [151, 6, 83, 13, "fileName"], [151, 14, 83, 13], [151, 16, 83, 13, "_jsxFileName"], [151, 28, 83, 13], [152, 6, 83, 13, "lineNumber"], [152, 16, 83, 13], [153, 6, 83, 13, "columnNumber"], [153, 18, 83, 13], [154, 4, 83, 13], [154, 11, 84, 10], [154, 12, 84, 11], [155, 2, 86, 0], [156, 2, 86, 1, "_s"], [156, 4, 86, 1], [156, 5, 8, 24, "VoiceMode"], [156, 14, 8, 33], [157, 4, 8, 33], [157, 12, 9, 17, "useColors"], [157, 32, 9, 26], [158, 2, 9, 26], [159, 2, 9, 26, "_c"], [159, 4, 9, 26], [159, 7, 8, 24, "VoiceMode"], [159, 16, 8, 33], [160, 2, 8, 33], [160, 6, 8, 33, "_c"], [160, 8, 8, 33], [161, 2, 8, 33, "$RefreshReg$"], [161, 14, 8, 33], [161, 15, 8, 33, "_c"], [161, 17, 8, 33], [162, 0, 8, 33], [162, 3]], "functionMap": {"names": ["<global>", "VoiceMode", "useEffect$argument_0"], "mappings": "AAA;eCO;YCI;GDO;CDmE"}}, "type": "js/module"}]}