{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Tag = exports.default = (0, _createLucideIcon.default)(\"Tag\", [[\"path\", {\n    d: \"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z\",\n    key: \"vktsd0\"\n  }], [\"circle\", {\n    cx: \"7.5\",\n    cy: \"7.5\",\n    r: \".5\",\n    fill: \"currentColor\",\n    key: \"kqv944\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Tag"], [15, 11, 10, 9], [15, 14, 10, 9, "exports"], [15, 21, 10, 9], [15, 22, 10, 9, "default"], [15, 29, 10, 9], [15, 32, 10, 12], [15, 36, 10, 12, "createLucideIcon"], [15, 61, 10, 28], [15, 63, 10, 29], [15, 68, 10, 34], [15, 70, 10, 36], [15, 71, 11, 2], [15, 72, 12, 4], [15, 78, 12, 10], [15, 80, 13, 4], [16, 4, 14, 6, "d"], [16, 5, 14, 7], [16, 7, 14, 9], [16, 157, 14, 159], [17, 4, 15, 6, "key"], [17, 7, 15, 9], [17, 9, 15, 11], [18, 2, 16, 4], [18, 3, 16, 5], [18, 4, 17, 3], [18, 6, 18, 2], [18, 7, 18, 3], [18, 15, 18, 11], [18, 17, 18, 13], [19, 4, 18, 15, "cx"], [19, 6, 18, 17], [19, 8, 18, 19], [19, 13, 18, 24], [20, 4, 18, 26, "cy"], [20, 6, 18, 28], [20, 8, 18, 30], [20, 13, 18, 35], [21, 4, 18, 37, "r"], [21, 5, 18, 38], [21, 7, 18, 40], [21, 11, 18, 44], [22, 4, 18, 46, "fill"], [22, 8, 18, 50], [22, 10, 18, 52], [22, 24, 18, 66], [23, 4, 18, 68, "key"], [23, 7, 18, 71], [23, 9, 18, 73], [24, 2, 18, 82], [24, 3, 18, 83], [24, 4, 18, 84], [24, 5, 19, 1], [24, 6, 19, 2], [25, 0, 19, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}