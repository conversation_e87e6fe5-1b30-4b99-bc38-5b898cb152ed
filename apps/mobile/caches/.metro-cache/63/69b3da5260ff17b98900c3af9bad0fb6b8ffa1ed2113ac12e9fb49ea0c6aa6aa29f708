{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const MessageCircleDashed = exports.default = (0, _createLucideIcon.default)(\"MessageCircleDashed\", [[\"path\", {\n    d: \"M13.5 3.1c-.5 0-1-.1-1.5-.1s-1 .1-1.5.1\",\n    key: \"16ll65\"\n  }], [\"path\", {\n    d: \"M19.3 6.8a10.45 10.45 0 0 0-2.1-2.1\",\n    key: \"1nq77a\"\n  }], [\"path\", {\n    d: \"M20.9 13.5c.1-.5.1-1 .1-1.5s-.1-1-.1-1.5\",\n    key: \"1sf7wn\"\n  }], [\"path\", {\n    d: \"M17.2 19.3a10.45 10.45 0 0 0 2.1-2.1\",\n    key: \"x1hs5g\"\n  }], [\"path\", {\n    d: \"M10.5 20.9c.5.1 1 .1 1.5.1s1-.1 1.5-.1\",\n    key: \"19m18z\"\n  }], [\"path\", {\n    d: \"M3.5 17.5 2 22l4.5-1.5\",\n    key: \"1f36qi\"\n  }], [\"path\", {\n    d: \"M3.1 10.5c0 .5-.1 1-.1 1.5s.1 1 .1 1.5\",\n    key: \"1vz3ju\"\n  }], [\"path\", {\n    d: \"M6.8 4.7a10.45 10.45 0 0 0-2.1 2.1\",\n    key: \"19f9do\"\n  }]]);\n});", "lineCount": 40, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "MessageCircleDashed"], [15, 27, 10, 25], [15, 30, 10, 25, "exports"], [15, 37, 10, 25], [15, 38, 10, 25, "default"], [15, 45, 10, 25], [15, 48, 10, 28], [15, 52, 10, 28, "createLucideIcon"], [15, 77, 10, 44], [15, 79, 10, 45], [15, 100, 10, 66], [15, 102, 10, 68], [15, 103, 11, 2], [15, 104, 11, 3], [15, 110, 11, 9], [15, 112, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 48, 11, 57], [17, 4, 11, 59, "key"], [17, 7, 11, 62], [17, 9, 11, 64], [18, 2, 11, 73], [18, 3, 11, 74], [18, 4, 11, 75], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 44, 12, 53], [20, 4, 12, 55, "key"], [20, 7, 12, 58], [20, 9, 12, 60], [21, 2, 12, 69], [21, 3, 12, 70], [21, 4, 12, 71], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 49, 13, 58], [23, 4, 13, 60, "key"], [23, 7, 13, 63], [23, 9, 13, 65], [24, 2, 13, 74], [24, 3, 13, 75], [24, 4, 13, 76], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 45, 14, 54], [26, 4, 14, 56, "key"], [26, 7, 14, 59], [26, 9, 14, 61], [27, 2, 14, 70], [27, 3, 14, 71], [27, 4, 14, 72], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 47, 15, 56], [29, 4, 15, 58, "key"], [29, 7, 15, 61], [29, 9, 15, 63], [30, 2, 15, 72], [30, 3, 15, 73], [30, 4, 15, 74], [30, 6, 16, 2], [30, 7, 16, 3], [30, 13, 16, 9], [30, 15, 16, 11], [31, 4, 16, 13, "d"], [31, 5, 16, 14], [31, 7, 16, 16], [31, 31, 16, 40], [32, 4, 16, 42, "key"], [32, 7, 16, 45], [32, 9, 16, 47], [33, 2, 16, 56], [33, 3, 16, 57], [33, 4, 16, 58], [33, 6, 17, 2], [33, 7, 17, 3], [33, 13, 17, 9], [33, 15, 17, 11], [34, 4, 17, 13, "d"], [34, 5, 17, 14], [34, 7, 17, 16], [34, 47, 17, 56], [35, 4, 17, 58, "key"], [35, 7, 17, 61], [35, 9, 17, 63], [36, 2, 17, 72], [36, 3, 17, 73], [36, 4, 17, 74], [36, 6, 18, 2], [36, 7, 18, 3], [36, 13, 18, 9], [36, 15, 18, 11], [37, 4, 18, 13, "d"], [37, 5, 18, 14], [37, 7, 18, 16], [37, 43, 18, 52], [38, 4, 18, 54, "key"], [38, 7, 18, 57], [38, 9, 18, 59], [39, 2, 18, 68], [39, 3, 18, 69], [39, 4, 18, 70], [39, 5, 19, 1], [39, 6, 19, 2], [40, 0, 19, 3], [40, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}