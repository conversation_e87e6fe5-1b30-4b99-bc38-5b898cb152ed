{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Mailbox = exports.default = (0, _createLucideIcon.default)(\"Mailbox\", [[\"path\", {\n    d: \"M22 17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9.5C2 7 4 5 6.5 5H18c2.2 0 4 1.8 4 4v8Z\",\n    key: \"1lbycx\"\n  }], [\"polyline\", {\n    points: \"15,9 18,9 18,11\",\n    key: \"1pm9c0\"\n  }], [\"path\", {\n    d: \"M6.5 5C9 5 11 7 11 9.5V17a2 2 0 0 1-2 2\",\n    key: \"15i455\"\n  }], [\"line\", {\n    x1: \"6\",\n    x2: \"7\",\n    y1: \"10\",\n    y2: \"10\",\n    key: \"1e2scm\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Mailbox"], [15, 15, 10, 13], [15, 18, 10, 13, "exports"], [15, 25, 10, 13], [15, 26, 10, 13, "default"], [15, 33, 10, 13], [15, 36, 10, 16], [15, 40, 10, 16, "createLucideIcon"], [15, 65, 10, 32], [15, 67, 10, 33], [15, 76, 10, 42], [15, 78, 10, 44], [15, 79, 11, 2], [15, 80, 12, 4], [15, 86, 12, 10], [15, 88, 13, 4], [16, 4, 14, 6, "d"], [16, 5, 14, 7], [16, 7, 14, 9], [16, 85, 14, 87], [17, 4, 15, 6, "key"], [17, 7, 15, 9], [17, 9, 15, 11], [18, 2, 16, 4], [18, 3, 16, 5], [18, 4, 17, 3], [18, 6, 18, 2], [18, 7, 18, 3], [18, 17, 18, 13], [18, 19, 18, 15], [19, 4, 18, 17, "points"], [19, 10, 18, 23], [19, 12, 18, 25], [19, 29, 18, 42], [20, 4, 18, 44, "key"], [20, 7, 18, 47], [20, 9, 18, 49], [21, 2, 18, 58], [21, 3, 18, 59], [21, 4, 18, 60], [21, 6, 19, 2], [21, 7, 19, 3], [21, 13, 19, 9], [21, 15, 19, 11], [22, 4, 19, 13, "d"], [22, 5, 19, 14], [22, 7, 19, 16], [22, 48, 19, 57], [23, 4, 19, 59, "key"], [23, 7, 19, 62], [23, 9, 19, 64], [24, 2, 19, 73], [24, 3, 19, 74], [24, 4, 19, 75], [24, 6, 20, 2], [24, 7, 20, 3], [24, 13, 20, 9], [24, 15, 20, 11], [25, 4, 20, 13, "x1"], [25, 6, 20, 15], [25, 8, 20, 17], [25, 11, 20, 20], [26, 4, 20, 22, "x2"], [26, 6, 20, 24], [26, 8, 20, 26], [26, 11, 20, 29], [27, 4, 20, 31, "y1"], [27, 6, 20, 33], [27, 8, 20, 35], [27, 12, 20, 39], [28, 4, 20, 41, "y2"], [28, 6, 20, 43], [28, 8, 20, 45], [28, 12, 20, 49], [29, 4, 20, 51, "key"], [29, 7, 20, 54], [29, 9, 20, 56], [30, 2, 20, 65], [30, 3, 20, 66], [30, 4, 20, 67], [30, 5, 21, 1], [30, 6, 21, 2], [31, 0, 21, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}