{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Components/SafeAreaView/SafeAreaView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 70}}], "key": "3QVUNIaP8S9RRUnN6oy5HbhEEko=", "exportNames": ["*"]}}, {"name": "../../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 46}}], "key": "OQ+UwuOXakCp1lTGDrZ4ulZZQC4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Text/Text", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 35}}], "key": "2Uowcf8dI9Q+9EqAhRxQzVpiZEk=", "exportNames": ["*"]}}, {"name": "./LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 42}}], "key": "M6ofQu070ZUTf+Oq+Zz+7FQEgjs=", "exportNames": ["*"]}}, {"name": "./LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 45}}], "key": "ZNPCT6BEiOEisuHiD4UE+iOD6VQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = LogBoxInspectorFooterButton;\n  var _SafeAreaView = _interopRequireDefault(require(_dependencyMap[1], \"../../Components/SafeAreaView/SafeAreaView\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"../../StyleSheet/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"../../Text/Text\"));\n  var _LogBoxButton = _interopRequireDefault(require(_dependencyMap[5], \"./LogBoxButton\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[6], \"./LogBoxStyle\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[8], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/UI/LogBoxInspectorFooterButton.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function LogBoxInspectorFooterButton(props) {\n    return (0, _jsxRuntime.jsx)(_SafeAreaView.default, {\n      style: styles.button,\n      children: (0, _jsxRuntime.jsx)(_LogBoxButton.default, {\n        id: props.id,\n        backgroundColor: {\n          default: 'transparent',\n          pressed: LogBoxStyle.getBackgroundDarkColor()\n        },\n        onPress: props.onPress,\n        children: (0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.buttonContent,\n          children: (0, _jsxRuntime.jsx)(_Text.default, {\n            style: styles.buttonLabel,\n            children: props.text\n          })\n        })\n      })\n    });\n  }\n  var styles = _StyleSheet.default.create({\n    button: {\n      flex: 1\n    },\n    buttonContent: {\n      alignItems: 'center',\n      height: 48,\n      justifyContent: 'center'\n    },\n    buttonLabel: {\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      includeFontPadding: false,\n      lineHeight: 20\n    }\n  });\n});", "lineCount": 53, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_SafeAreaView"], [7, 19, 11, 0], [7, 22, 11, 0, "_interopRequireDefault"], [7, 44, 11, 0], [7, 45, 11, 0, "require"], [7, 52, 11, 0], [7, 53, 11, 0, "_dependencyMap"], [7, 67, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_View"], [8, 11, 12, 0], [8, 14, 12, 0, "_interopRequireDefault"], [8, 36, 12, 0], [8, 37, 12, 0, "require"], [8, 44, 12, 0], [8, 45, 12, 0, "_dependencyMap"], [8, 59, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_StyleSheet"], [9, 17, 13, 0], [9, 20, 13, 0, "_interopRequireDefault"], [9, 42, 13, 0], [9, 43, 13, 0, "require"], [9, 50, 13, 0], [9, 51, 13, 0, "_dependencyMap"], [9, 65, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_Text"], [10, 11, 14, 0], [10, 14, 14, 0, "_interopRequireDefault"], [10, 36, 14, 0], [10, 37, 14, 0, "require"], [10, 44, 14, 0], [10, 45, 14, 0, "_dependencyMap"], [10, 59, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_LogBoxButton"], [11, 19, 15, 0], [11, 22, 15, 0, "_interopRequireDefault"], [11, 44, 15, 0], [11, 45, 15, 0, "require"], [11, 52, 15, 0], [11, 53, 15, 0, "_dependencyMap"], [11, 67, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "LogBoxStyle"], [12, 17, 16, 0], [12, 20, 16, 0, "_interopRequireWildcard"], [12, 43, 16, 0], [12, 44, 16, 0, "require"], [12, 51, 16, 0], [12, 52, 16, 0, "_dependencyMap"], [12, 66, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "React"], [13, 11, 17, 0], [13, 14, 17, 0, "_interopRequireWildcard"], [13, 37, 17, 0], [13, 38, 17, 0, "require"], [13, 45, 17, 0], [13, 46, 17, 0, "_dependencyMap"], [13, 60, 17, 0], [14, 2, 17, 31], [14, 6, 17, 31, "_jsxRuntime"], [14, 17, 17, 31], [14, 20, 17, 31, "require"], [14, 27, 17, 31], [14, 28, 17, 31, "_dependencyMap"], [14, 42, 17, 31], [15, 2, 17, 31], [15, 6, 17, 31, "_jsxFileName"], [15, 18, 17, 31], [16, 2, 17, 31], [16, 11, 17, 31, "_interopRequireWildcard"], [16, 35, 17, 31, "e"], [16, 36, 17, 31], [16, 38, 17, 31, "t"], [16, 39, 17, 31], [16, 68, 17, 31, "WeakMap"], [16, 75, 17, 31], [16, 81, 17, 31, "r"], [16, 82, 17, 31], [16, 89, 17, 31, "WeakMap"], [16, 96, 17, 31], [16, 100, 17, 31, "n"], [16, 101, 17, 31], [16, 108, 17, 31, "WeakMap"], [16, 115, 17, 31], [16, 127, 17, 31, "_interopRequireWildcard"], [16, 150, 17, 31], [16, 162, 17, 31, "_interopRequireWildcard"], [16, 163, 17, 31, "e"], [16, 164, 17, 31], [16, 166, 17, 31, "t"], [16, 167, 17, 31], [16, 176, 17, 31, "t"], [16, 177, 17, 31], [16, 181, 17, 31, "e"], [16, 182, 17, 31], [16, 186, 17, 31, "e"], [16, 187, 17, 31], [16, 188, 17, 31, "__esModule"], [16, 198, 17, 31], [16, 207, 17, 31, "e"], [16, 208, 17, 31], [16, 214, 17, 31, "o"], [16, 215, 17, 31], [16, 217, 17, 31, "i"], [16, 218, 17, 31], [16, 220, 17, 31, "f"], [16, 221, 17, 31], [16, 226, 17, 31, "__proto__"], [16, 235, 17, 31], [16, 243, 17, 31, "default"], [16, 250, 17, 31], [16, 252, 17, 31, "e"], [16, 253, 17, 31], [16, 270, 17, 31, "e"], [16, 271, 17, 31], [16, 294, 17, 31, "e"], [16, 295, 17, 31], [16, 320, 17, 31, "e"], [16, 321, 17, 31], [16, 330, 17, 31, "f"], [16, 331, 17, 31], [16, 337, 17, 31, "o"], [16, 338, 17, 31], [16, 341, 17, 31, "t"], [16, 342, 17, 31], [16, 345, 17, 31, "n"], [16, 346, 17, 31], [16, 349, 17, 31, "r"], [16, 350, 17, 31], [16, 358, 17, 31, "o"], [16, 359, 17, 31], [16, 360, 17, 31, "has"], [16, 363, 17, 31], [16, 364, 17, 31, "e"], [16, 365, 17, 31], [16, 375, 17, 31, "o"], [16, 376, 17, 31], [16, 377, 17, 31, "get"], [16, 380, 17, 31], [16, 381, 17, 31, "e"], [16, 382, 17, 31], [16, 385, 17, 31, "o"], [16, 386, 17, 31], [16, 387, 17, 31, "set"], [16, 390, 17, 31], [16, 391, 17, 31, "e"], [16, 392, 17, 31], [16, 394, 17, 31, "f"], [16, 395, 17, 31], [16, 409, 17, 31, "_t"], [16, 411, 17, 31], [16, 415, 17, 31, "e"], [16, 416, 17, 31], [16, 432, 17, 31, "_t"], [16, 434, 17, 31], [16, 441, 17, 31, "hasOwnProperty"], [16, 455, 17, 31], [16, 456, 17, 31, "call"], [16, 460, 17, 31], [16, 461, 17, 31, "e"], [16, 462, 17, 31], [16, 464, 17, 31, "_t"], [16, 466, 17, 31], [16, 473, 17, 31, "i"], [16, 474, 17, 31], [16, 478, 17, 31, "o"], [16, 479, 17, 31], [16, 482, 17, 31, "Object"], [16, 488, 17, 31], [16, 489, 17, 31, "defineProperty"], [16, 503, 17, 31], [16, 508, 17, 31, "Object"], [16, 514, 17, 31], [16, 515, 17, 31, "getOwnPropertyDescriptor"], [16, 539, 17, 31], [16, 540, 17, 31, "e"], [16, 541, 17, 31], [16, 543, 17, 31, "_t"], [16, 545, 17, 31], [16, 552, 17, 31, "i"], [16, 553, 17, 31], [16, 554, 17, 31, "get"], [16, 557, 17, 31], [16, 561, 17, 31, "i"], [16, 562, 17, 31], [16, 563, 17, 31, "set"], [16, 566, 17, 31], [16, 570, 17, 31, "o"], [16, 571, 17, 31], [16, 572, 17, 31, "f"], [16, 573, 17, 31], [16, 575, 17, 31, "_t"], [16, 577, 17, 31], [16, 579, 17, 31, "i"], [16, 580, 17, 31], [16, 584, 17, 31, "f"], [16, 585, 17, 31], [16, 586, 17, 31, "_t"], [16, 588, 17, 31], [16, 592, 17, 31, "e"], [16, 593, 17, 31], [16, 594, 17, 31, "_t"], [16, 596, 17, 31], [16, 607, 17, 31, "f"], [16, 608, 17, 31], [16, 613, 17, 31, "e"], [16, 614, 17, 31], [16, 616, 17, 31, "t"], [16, 617, 17, 31], [17, 2, 25, 15], [17, 11, 25, 24, "LogBoxInspectorFooterButton"], [17, 38, 25, 51, "LogBoxInspectorFooterButton"], [17, 39, 26, 2, "props"], [17, 44, 26, 20], [17, 46, 27, 14], [18, 4, 28, 2], [18, 11, 29, 4], [18, 15, 29, 4, "_jsxRuntime"], [18, 26, 29, 4], [18, 27, 29, 4, "jsx"], [18, 30, 29, 4], [18, 32, 29, 5, "_SafeAreaView"], [18, 45, 29, 5], [18, 46, 29, 5, "default"], [18, 53, 29, 17], [19, 6, 29, 18, "style"], [19, 11, 29, 23], [19, 13, 29, 25, "styles"], [19, 19, 29, 31], [19, 20, 29, 32, "button"], [19, 26, 29, 39], [20, 6, 29, 39, "children"], [20, 14, 29, 39], [20, 16, 30, 6], [20, 20, 30, 6, "_jsxRuntime"], [20, 31, 30, 6], [20, 32, 30, 6, "jsx"], [20, 35, 30, 6], [20, 37, 30, 7, "_LogBoxButton"], [20, 50, 30, 7], [20, 51, 30, 7, "default"], [20, 58, 30, 19], [21, 8, 31, 8, "id"], [21, 10, 31, 10], [21, 12, 31, 12, "props"], [21, 17, 31, 17], [21, 18, 31, 18, "id"], [21, 20, 31, 21], [22, 8, 32, 8, "backgroundColor"], [22, 23, 32, 23], [22, 25, 32, 25], [23, 10, 33, 10, "default"], [23, 17, 33, 17], [23, 19, 33, 19], [23, 32, 33, 32], [24, 10, 34, 10, "pressed"], [24, 17, 34, 17], [24, 19, 34, 19, "LogBoxStyle"], [24, 30, 34, 30], [24, 31, 34, 31, "getBackgroundDarkColor"], [24, 53, 34, 53], [24, 54, 34, 54], [25, 8, 35, 8], [25, 9, 35, 10], [26, 8, 36, 8, "onPress"], [26, 15, 36, 15], [26, 17, 36, 17, "props"], [26, 22, 36, 22], [26, 23, 36, 23, "onPress"], [26, 30, 36, 31], [27, 8, 36, 31, "children"], [27, 16, 36, 31], [27, 18, 37, 8], [27, 22, 37, 8, "_jsxRuntime"], [27, 33, 37, 8], [27, 34, 37, 8, "jsx"], [27, 37, 37, 8], [27, 39, 37, 9, "_View"], [27, 44, 37, 9], [27, 45, 37, 9, "default"], [27, 52, 37, 13], [28, 10, 37, 14, "style"], [28, 15, 37, 19], [28, 17, 37, 21, "styles"], [28, 23, 37, 27], [28, 24, 37, 28, "buttonContent"], [28, 37, 37, 42], [29, 10, 37, 42, "children"], [29, 18, 37, 42], [29, 20, 38, 10], [29, 24, 38, 10, "_jsxRuntime"], [29, 35, 38, 10], [29, 36, 38, 10, "jsx"], [29, 39, 38, 10], [29, 41, 38, 11, "_Text"], [29, 46, 38, 11], [29, 47, 38, 11, "default"], [29, 54, 38, 15], [30, 12, 38, 16, "style"], [30, 17, 38, 21], [30, 19, 38, 23, "styles"], [30, 25, 38, 29], [30, 26, 38, 30, "buttonLabel"], [30, 37, 38, 42], [31, 12, 38, 42, "children"], [31, 20, 38, 42], [31, 22, 38, 44, "props"], [31, 27, 38, 49], [31, 28, 38, 50, "text"], [32, 10, 38, 54], [32, 11, 38, 61], [33, 8, 38, 62], [33, 9, 39, 14], [34, 6, 39, 15], [34, 7, 40, 20], [35, 4, 40, 21], [35, 5, 41, 18], [35, 6, 41, 19], [36, 2, 43, 0], [37, 2, 45, 0], [37, 6, 45, 6, "styles"], [37, 12, 45, 12], [37, 15, 45, 15, "StyleSheet"], [37, 34, 45, 25], [37, 35, 45, 26, "create"], [37, 41, 45, 32], [37, 42, 45, 33], [38, 4, 46, 2, "button"], [38, 10, 46, 8], [38, 12, 46, 10], [39, 6, 47, 4, "flex"], [39, 10, 47, 8], [39, 12, 47, 10], [40, 4, 48, 2], [40, 5, 48, 3], [41, 4, 49, 2, "buttonContent"], [41, 17, 49, 15], [41, 19, 49, 17], [42, 6, 50, 4, "alignItems"], [42, 16, 50, 14], [42, 18, 50, 16], [42, 26, 50, 24], [43, 6, 51, 4, "height"], [43, 12, 51, 10], [43, 14, 51, 12], [43, 16, 51, 14], [44, 6, 52, 4, "justifyContent"], [44, 20, 52, 18], [44, 22, 52, 20], [45, 4, 53, 2], [45, 5, 53, 3], [46, 4, 54, 2, "buttonLabel"], [46, 15, 54, 13], [46, 17, 54, 15], [47, 6, 55, 4, "color"], [47, 11, 55, 9], [47, 13, 55, 11, "LogBoxStyle"], [47, 24, 55, 22], [47, 25, 55, 23, "getTextColor"], [47, 37, 55, 35], [47, 38, 55, 36], [47, 39, 55, 37], [47, 40, 55, 38], [48, 6, 56, 4, "fontSize"], [48, 14, 56, 12], [48, 16, 56, 14], [48, 18, 56, 16], [49, 6, 57, 4, "includeFontPadding"], [49, 24, 57, 22], [49, 26, 57, 24], [49, 31, 57, 29], [50, 6, 58, 4, "lineHeight"], [50, 16, 58, 14], [50, 18, 58, 16], [51, 4, 59, 2], [52, 2, 60, 0], [52, 3, 60, 1], [52, 4, 60, 2], [53, 0, 60, 3], [53, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorFooterButton"], "mappings": "AAA;eCwB;CDkB"}}, "type": "js/module"}]}