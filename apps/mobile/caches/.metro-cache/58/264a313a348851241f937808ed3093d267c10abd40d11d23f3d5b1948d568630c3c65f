{"dependencies": [{"name": "./core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 40, "index": 55}}], "key": "mJVVi7YU3vDVLm6ZethtbJGh1KY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.makeViewDescriptorsSet = makeViewDescriptorsSet;\n  var _core = require(_dependencyMap[0], \"./core.js\");\n  const _worklet_16792002340537_init_data = {\n    code: \"function reactNativeReanimated_ViewDescriptorsSetJs1(descriptors){const{item}=this.__closure;const index=descriptors.findIndex(function(descriptor){return descriptor.tag===item.tag;});if(index!==-1){descriptors[index]=item;}else{descriptors.push(item);}return descriptors;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/ViewDescriptorsSet.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ViewDescriptorsSetJs1\\\",\\\"descriptors\\\",\\\"item\\\",\\\"__closure\\\",\\\"index\\\",\\\"findIndex\\\",\\\"descriptor\\\",\\\"tag\\\",\\\"push\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/ViewDescriptorsSet.js\\\"],\\\"mappings\\\":\\\"AAQsC,SAAAA,2CAAeA,CAAAC,WAAA,QAAAC,IAAA,OAAAC,SAAA,CAG7C,KAAM,CAAAC,KAAK,CAAGH,WAAW,CAACI,SAAS,CAAC,SAAAC,UAAU,QAAI,CAAAA,UAAU,CAACC,GAAG,GAAKL,IAAI,CAACK,GAAG,GAAC,CAC9E,GAAIH,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBH,WAAW,CAACG,KAAK,CAAC,CAAGF,IAAI,CAC3B,CAAC,IAAM,CACLD,WAAW,CAACO,IAAI,CAACN,IAAI,CAAC,CACxB,CACA,MAAO,CAAAD,WAAW,CACpB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_2880512106780_init_data = {\n    code: \"function reactNativeReanimated_ViewDescriptorsSetJs2(descriptors){const{viewTag}=this.__closure;const index=descriptors.findIndex(function(descriptor){return descriptor.tag===viewTag;});if(index!==-1){descriptors.splice(index,1);}return descriptors;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/ViewDescriptorsSet.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ViewDescriptorsSetJs2\\\",\\\"descriptors\\\",\\\"viewTag\\\",\\\"__closure\\\",\\\"index\\\",\\\"findIndex\\\",\\\"descriptor\\\",\\\"tag\\\",\\\"splice\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/ViewDescriptorsSet.js\\\"],\\\"mappings\\\":\\\"AAqBsC,SAAAA,2CAAeA,CAAAC,WAAA,QAAAC,OAAA,OAAAC,SAAA,CAG7C,KAAM,CAAAC,KAAK,CAAGH,WAAW,CAACI,SAAS,CAAC,SAAAC,UAAU,QAAI,CAAAA,UAAU,CAACC,GAAG,GAAKL,OAAO,GAAC,CAC7E,GAAIE,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBH,WAAW,CAACO,MAAM,CAACJ,KAAK,CAAE,CAAC,CAAC,CAC9B,CACA,MAAO,CAAAH,WAAW,CACpB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function makeViewDescriptorsSet() {\n    const shareableViewDescriptors = (0, _core.makeMutable)([]);\n    const data = {\n      shareableViewDescriptors,\n      add: item => {\n        shareableViewDescriptors.modify(function () {\n          const _e = [new global.Error(), -2, -27];\n          const reactNativeReanimated_ViewDescriptorsSetJs1 = function (descriptors) {\n            const index = descriptors.findIndex(descriptor => descriptor.tag === item.tag);\n            if (index !== -1) {\n              descriptors[index] = item;\n            } else {\n              descriptors.push(item);\n            }\n            return descriptors;\n          };\n          reactNativeReanimated_ViewDescriptorsSetJs1.__closure = {\n            item\n          };\n          reactNativeReanimated_ViewDescriptorsSetJs1.__workletHash = 16792002340537;\n          reactNativeReanimated_ViewDescriptorsSetJs1.__initData = _worklet_16792002340537_init_data;\n          reactNativeReanimated_ViewDescriptorsSetJs1.__stackDetails = _e;\n          return reactNativeReanimated_ViewDescriptorsSetJs1;\n        }(), false);\n      },\n      remove: viewTag => {\n        shareableViewDescriptors.modify(function () {\n          const _e = [new global.Error(), -2, -27];\n          const reactNativeReanimated_ViewDescriptorsSetJs2 = function (descriptors) {\n            const index = descriptors.findIndex(descriptor => descriptor.tag === viewTag);\n            if (index !== -1) {\n              descriptors.splice(index, 1);\n            }\n            return descriptors;\n          };\n          reactNativeReanimated_ViewDescriptorsSetJs2.__closure = {\n            viewTag\n          };\n          reactNativeReanimated_ViewDescriptorsSetJs2.__workletHash = 2880512106780;\n          reactNativeReanimated_ViewDescriptorsSetJs2.__initData = _worklet_2880512106780_init_data;\n          reactNativeReanimated_ViewDescriptorsSetJs2.__stackDetails = _e;\n          return reactNativeReanimated_ViewDescriptorsSetJs2;\n        }(), false);\n      }\n    };\n    return data;\n  }\n});", "lineCount": 68, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "makeViewDescriptorsSet"], [7, 32, 1, 13], [7, 35, 1, 13, "makeViewDescriptorsSet"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 3, 40], [9, 8, 3, 40, "_worklet_16792002340537_init_data"], [9, 41, 3, 40], [10, 4, 3, 40, "code"], [10, 8, 3, 40], [11, 4, 3, 40, "location"], [11, 12, 3, 40], [12, 4, 3, 40, "sourceMap"], [12, 13, 3, 40], [13, 4, 3, 40, "version"], [13, 11, 3, 40], [14, 2, 3, 40], [15, 2, 3, 40], [15, 8, 3, 40, "_worklet_2880512106780_init_data"], [15, 40, 3, 40], [16, 4, 3, 40, "code"], [16, 8, 3, 40], [17, 4, 3, 40, "location"], [17, 12, 3, 40], [18, 4, 3, 40, "sourceMap"], [18, 13, 3, 40], [19, 4, 3, 40, "version"], [19, 11, 3, 40], [20, 2, 3, 40], [21, 2, 4, 7], [21, 11, 4, 16, "makeViewDescriptorsSet"], [21, 33, 4, 38, "makeViewDescriptorsSet"], [21, 34, 4, 38], [21, 36, 4, 41], [22, 4, 5, 2], [22, 10, 5, 8, "shareableViewDescriptors"], [22, 34, 5, 32], [22, 37, 5, 35], [22, 41, 5, 35, "makeMutable"], [22, 58, 5, 46], [22, 60, 5, 47], [22, 62, 5, 49], [22, 63, 5, 50], [23, 4, 6, 2], [23, 10, 6, 8, "data"], [23, 14, 6, 12], [23, 17, 6, 15], [24, 6, 7, 4, "shareableViewDescriptors"], [24, 30, 7, 28], [25, 6, 8, 4, "add"], [25, 9, 8, 7], [25, 11, 8, 9, "item"], [25, 15, 8, 13], [25, 19, 8, 17], [26, 8, 9, 6, "shareableViewDescriptors"], [26, 32, 9, 30], [26, 33, 9, 31, "modify"], [26, 39, 9, 37], [26, 40, 9, 38], [27, 10, 9, 38], [27, 16, 9, 38, "_e"], [27, 18, 9, 38], [27, 26, 9, 38, "global"], [27, 32, 9, 38], [27, 33, 9, 38, "Error"], [27, 38, 9, 38], [28, 10, 9, 38], [28, 16, 9, 38, "reactNativeReanimated_ViewDescriptorsSetJs1"], [28, 59, 9, 38], [28, 71, 9, 38, "reactNativeReanimated_ViewDescriptorsSetJs1"], [28, 72, 9, 38, "descriptors"], [28, 83, 9, 49], [28, 85, 9, 53], [29, 12, 12, 8], [29, 18, 12, 14, "index"], [29, 23, 12, 19], [29, 26, 12, 22, "descriptors"], [29, 37, 12, 33], [29, 38, 12, 34, "findIndex"], [29, 47, 12, 43], [29, 48, 12, 44, "descriptor"], [29, 58, 12, 54], [29, 62, 12, 58, "descriptor"], [29, 72, 12, 68], [29, 73, 12, 69, "tag"], [29, 76, 12, 72], [29, 81, 12, 77, "item"], [29, 85, 12, 81], [29, 86, 12, 82, "tag"], [29, 89, 12, 85], [29, 90, 12, 86], [30, 12, 13, 8], [30, 16, 13, 12, "index"], [30, 21, 13, 17], [30, 26, 13, 22], [30, 27, 13, 23], [30, 28, 13, 24], [30, 30, 13, 26], [31, 14, 14, 10, "descriptors"], [31, 25, 14, 21], [31, 26, 14, 22, "index"], [31, 31, 14, 27], [31, 32, 14, 28], [31, 35, 14, 31, "item"], [31, 39, 14, 35], [32, 12, 15, 8], [32, 13, 15, 9], [32, 19, 15, 15], [33, 14, 16, 10, "descriptors"], [33, 25, 16, 21], [33, 26, 16, 22, "push"], [33, 30, 16, 26], [33, 31, 16, 27, "item"], [33, 35, 16, 31], [33, 36, 16, 32], [34, 12, 17, 8], [35, 12, 18, 8], [35, 19, 18, 15, "descriptors"], [35, 30, 18, 26], [36, 10, 19, 6], [36, 11, 19, 7], [37, 10, 19, 7, "reactNativeReanimated_ViewDescriptorsSetJs1"], [37, 53, 19, 7], [37, 54, 19, 7, "__closure"], [37, 63, 19, 7], [38, 12, 19, 7, "item"], [39, 10, 19, 7], [40, 10, 19, 7, "reactNativeReanimated_ViewDescriptorsSetJs1"], [40, 53, 19, 7], [40, 54, 19, 7, "__workletHash"], [40, 67, 19, 7], [41, 10, 19, 7, "reactNativeReanimated_ViewDescriptorsSetJs1"], [41, 53, 19, 7], [41, 54, 19, 7, "__initData"], [41, 64, 19, 7], [41, 67, 19, 7, "_worklet_16792002340537_init_data"], [41, 100, 19, 7], [42, 10, 19, 7, "reactNativeReanimated_ViewDescriptorsSetJs1"], [42, 53, 19, 7], [42, 54, 19, 7, "__stackDetails"], [42, 68, 19, 7], [42, 71, 19, 7, "_e"], [42, 73, 19, 7], [43, 10, 19, 7], [43, 17, 19, 7, "reactNativeReanimated_ViewDescriptorsSetJs1"], [43, 60, 19, 7], [44, 8, 19, 7], [44, 9, 9, 38], [44, 13, 19, 9], [44, 18, 19, 14], [44, 19, 19, 15], [45, 6, 20, 4], [45, 7, 20, 5], [46, 6, 21, 4, "remove"], [46, 12, 21, 10], [46, 14, 21, 12, "viewTag"], [46, 21, 21, 19], [46, 25, 21, 23], [47, 8, 22, 6, "shareableViewDescriptors"], [47, 32, 22, 30], [47, 33, 22, 31, "modify"], [47, 39, 22, 37], [47, 40, 22, 38], [48, 10, 22, 38], [48, 16, 22, 38, "_e"], [48, 18, 22, 38], [48, 26, 22, 38, "global"], [48, 32, 22, 38], [48, 33, 22, 38, "Error"], [48, 38, 22, 38], [49, 10, 22, 38], [49, 16, 22, 38, "reactNativeReanimated_ViewDescriptorsSetJs2"], [49, 59, 22, 38], [49, 71, 22, 38, "reactNativeReanimated_ViewDescriptorsSetJs2"], [49, 72, 22, 38, "descriptors"], [49, 83, 22, 49], [49, 85, 22, 53], [50, 12, 25, 8], [50, 18, 25, 14, "index"], [50, 23, 25, 19], [50, 26, 25, 22, "descriptors"], [50, 37, 25, 33], [50, 38, 25, 34, "findIndex"], [50, 47, 25, 43], [50, 48, 25, 44, "descriptor"], [50, 58, 25, 54], [50, 62, 25, 58, "descriptor"], [50, 72, 25, 68], [50, 73, 25, 69, "tag"], [50, 76, 25, 72], [50, 81, 25, 77, "viewTag"], [50, 88, 25, 84], [50, 89, 25, 85], [51, 12, 26, 8], [51, 16, 26, 12, "index"], [51, 21, 26, 17], [51, 26, 26, 22], [51, 27, 26, 23], [51, 28, 26, 24], [51, 30, 26, 26], [52, 14, 27, 10, "descriptors"], [52, 25, 27, 21], [52, 26, 27, 22, "splice"], [52, 32, 27, 28], [52, 33, 27, 29, "index"], [52, 38, 27, 34], [52, 40, 27, 36], [52, 41, 27, 37], [52, 42, 27, 38], [53, 12, 28, 8], [54, 12, 29, 8], [54, 19, 29, 15, "descriptors"], [54, 30, 29, 26], [55, 10, 30, 6], [55, 11, 30, 7], [56, 10, 30, 7, "reactNativeReanimated_ViewDescriptorsSetJs2"], [56, 53, 30, 7], [56, 54, 30, 7, "__closure"], [56, 63, 30, 7], [57, 12, 30, 7, "viewTag"], [58, 10, 30, 7], [59, 10, 30, 7, "reactNativeReanimated_ViewDescriptorsSetJs2"], [59, 53, 30, 7], [59, 54, 30, 7, "__workletHash"], [59, 67, 30, 7], [60, 10, 30, 7, "reactNativeReanimated_ViewDescriptorsSetJs2"], [60, 53, 30, 7], [60, 54, 30, 7, "__initData"], [60, 64, 30, 7], [60, 67, 30, 7, "_worklet_2880512106780_init_data"], [60, 99, 30, 7], [61, 10, 30, 7, "reactNativeReanimated_ViewDescriptorsSetJs2"], [61, 53, 30, 7], [61, 54, 30, 7, "__stackDetails"], [61, 68, 30, 7], [61, 71, 30, 7, "_e"], [61, 73, 30, 7], [62, 10, 30, 7], [62, 17, 30, 7, "reactNativeReanimated_ViewDescriptorsSetJs2"], [62, 60, 30, 7], [63, 8, 30, 7], [63, 9, 22, 38], [63, 13, 30, 9], [63, 18, 30, 14], [63, 19, 30, 15], [64, 6, 31, 4], [65, 4, 32, 2], [65, 5, 32, 3], [66, 4, 33, 2], [66, 11, 33, 9, "data"], [66, 15, 33, 13], [67, 2, 34, 0], [68, 0, 34, 1], [68, 3]], "functionMap": {"names": ["<global>", "makeViewDescriptorsSet", "data.add", "shareableViewDescriptors.modify$argument_0", "descriptors.findIndex$argument_0", "data.remove"], "mappings": "AAA;OCG;SCI;sCCC;4CCG,yCD;ODO;KDC;YIC;sCFC;4CCG,wCD;OEK;KJC;CDG"}}, "type": "js/module"}]}