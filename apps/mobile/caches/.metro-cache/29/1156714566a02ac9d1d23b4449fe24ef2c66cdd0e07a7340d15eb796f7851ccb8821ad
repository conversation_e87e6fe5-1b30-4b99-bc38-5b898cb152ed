{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 43, "index": 43}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "whatwg-url-without-unicode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 44}, "end": {"line": 2, "column": 60, "index": 104}}], "key": "A+MCbLw/itdoI4zIyjSMR6P1C2g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.URL = void 0;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var _whatwgUrlWithoutUnicode = require(_dependencyMap[1], \"whatwg-url-without-unicode\");\n  var BLOB_URL_PREFIX = null;\n  var BlobModule = _reactNative.NativeModules.BlobModule;\n  if (BlobModule && typeof BlobModule.BLOB_URI_SCHEME === 'string') {\n    BLOB_URL_PREFIX = BlobModule.BLOB_URI_SCHEME + ':';\n    if (typeof BlobModule.BLOB_URI_HOST === 'string') {\n      BLOB_URL_PREFIX += `//${BlobModule.BLOB_URI_HOST}/`;\n    }\n  }\n\n  /**\n   * To allow Blobs be accessed via `content://` URIs,\n   * you need to register `Blob<PERSON>rovider` as a ContentProvider in your app's `AndroidManifest.xml`:\n   *\n   * ```xml\n   * <manifest>\n   *   <application>\n   *     <provider\n   *       android:name=\"com.facebook.react.modules.blob.BlobProvider\"\n   *       android:authorities=\"@string/blob_provider_authority\"\n   *       android:exported=\"false\"\n   *     />\n   *   </application>\n   * </manifest>\n   * ```\n   * And then define the `blob_provider_authority` string in `res/values/strings.xml`.\n   * Use a dotted name that's entirely unique to your app:\n   *\n   * ```xml\n   * <resources>\n   *   <string name=\"blob_provider_authority\">your.app.package.blobs</string>\n   * </resources>\n   * ```\n   */\n\n  _whatwgUrlWithoutUnicode.URL.createObjectURL = function createObjectURL(blob) {\n    if (BLOB_URL_PREFIX === null) {\n      throw new Error('Cannot create URL for blob!');\n    }\n    return `${BLOB_URL_PREFIX}${blob.data.blobId}?offset=${blob.data.offset}&size=${blob.size}`;\n  };\n  _whatwgUrlWithoutUnicode.URL.revokeObjectURL = function revokeObjectURL(url) {\n    // Do nothing.\n  };\n  var URL = exports.URL = _whatwgUrlWithoutUnicode.URL;\n});", "lineCount": 52, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_whatwgUrlWithoutUnicode"], [7, 30, 2, 0], [7, 33, 2, 0, "require"], [7, 40, 2, 0], [7, 41, 2, 0, "_dependencyMap"], [7, 55, 2, 0], [8, 2, 4, 0], [8, 6, 4, 4, "BLOB_URL_PREFIX"], [8, 21, 4, 19], [8, 24, 4, 22], [8, 28, 4, 26], [9, 2, 6, 0], [9, 6, 6, 7, "BlobModule"], [9, 16, 6, 17], [9, 19, 6, 21, "NativeModules"], [9, 45, 6, 34], [9, 46, 6, 7, "BlobModule"], [9, 56, 6, 17], [10, 2, 8, 0], [10, 6, 8, 4, "BlobModule"], [10, 16, 8, 14], [10, 20, 8, 18], [10, 27, 8, 25, "BlobModule"], [10, 37, 8, 35], [10, 38, 8, 36, "BLOB_URI_SCHEME"], [10, 53, 8, 51], [10, 58, 8, 56], [10, 66, 8, 64], [10, 68, 8, 66], [11, 4, 9, 2, "BLOB_URL_PREFIX"], [11, 19, 9, 17], [11, 22, 9, 20, "BlobModule"], [11, 32, 9, 30], [11, 33, 9, 31, "BLOB_URI_SCHEME"], [11, 48, 9, 46], [11, 51, 9, 49], [11, 54, 9, 52], [12, 4, 10, 2], [12, 8, 10, 6], [12, 15, 10, 13, "BlobModule"], [12, 25, 10, 23], [12, 26, 10, 24, "BLOB_URI_HOST"], [12, 39, 10, 37], [12, 44, 10, 42], [12, 52, 10, 50], [12, 54, 10, 52], [13, 6, 11, 4, "BLOB_URL_PREFIX"], [13, 21, 11, 19], [13, 25, 11, 23], [13, 30, 11, 28, "BlobModule"], [13, 40, 11, 38], [13, 41, 11, 39, "BLOB_URI_HOST"], [13, 54, 11, 52], [13, 57, 11, 55], [14, 4, 12, 2], [15, 2, 13, 0], [17, 2, 15, 0], [18, 0, 16, 0], [19, 0, 17, 0], [20, 0, 18, 0], [21, 0, 19, 0], [22, 0, 20, 0], [23, 0, 21, 0], [24, 0, 22, 0], [25, 0, 23, 0], [26, 0, 24, 0], [27, 0, 25, 0], [28, 0, 26, 0], [29, 0, 27, 0], [30, 0, 28, 0], [31, 0, 29, 0], [32, 0, 30, 0], [33, 0, 31, 0], [34, 0, 32, 0], [35, 0, 33, 0], [36, 0, 34, 0], [37, 0, 35, 0], [38, 0, 36, 0], [39, 0, 37, 0], [40, 0, 38, 0], [42, 2, 40, 0, "whatwgUrl"], [42, 30, 40, 9], [42, 31, 40, 10, "createObjectURL"], [42, 46, 40, 25], [42, 49, 40, 28], [42, 58, 40, 37, "createObjectURL"], [42, 73, 40, 52, "createObjectURL"], [42, 74, 40, 53, "blob"], [42, 78, 40, 57], [42, 80, 40, 59], [43, 4, 41, 2], [43, 8, 41, 6, "BLOB_URL_PREFIX"], [43, 23, 41, 21], [43, 28, 41, 26], [43, 32, 41, 30], [43, 34, 41, 32], [44, 6, 42, 4], [44, 12, 42, 10], [44, 16, 42, 14, "Error"], [44, 21, 42, 19], [44, 22, 42, 20], [44, 51, 42, 49], [44, 52, 42, 50], [45, 4, 43, 2], [46, 4, 44, 2], [46, 11, 44, 9], [46, 14, 44, 12, "BLOB_URL_PREFIX"], [46, 29, 44, 27], [46, 32, 44, 30, "blob"], [46, 36, 44, 34], [46, 37, 44, 35, "data"], [46, 41, 44, 39], [46, 42, 44, 40, "blobId"], [46, 48, 44, 46], [46, 59, 44, 57, "blob"], [46, 63, 44, 61], [46, 64, 44, 62, "data"], [46, 68, 44, 66], [46, 69, 44, 67, "offset"], [46, 75, 44, 73], [46, 84, 44, 82, "blob"], [46, 88, 44, 86], [46, 89, 44, 87, "size"], [46, 93, 44, 91], [46, 95, 44, 93], [47, 2, 45, 0], [47, 3, 45, 1], [48, 2, 47, 0, "whatwgUrl"], [48, 30, 47, 9], [48, 31, 47, 10, "revokeObjectURL"], [48, 46, 47, 25], [48, 49, 47, 28], [48, 58, 47, 37, "revokeObjectURL"], [48, 73, 47, 52, "revokeObjectURL"], [48, 74, 47, 53, "url"], [48, 77, 47, 56], [48, 79, 47, 58], [49, 4, 48, 2], [50, 2, 48, 2], [50, 3, 49, 1], [51, 2, 51, 7], [51, 6, 51, 13, "URL"], [51, 9, 51, 16], [51, 12, 51, 16, "exports"], [51, 19, 51, 16], [51, 20, 51, 16, "URL"], [51, 23, 51, 16], [51, 26, 51, 19, "whatwgUrl"], [51, 54, 51, 28], [52, 0, 51, 29], [52, 3]], "functionMap": {"names": ["<global>", "createObjectURL", "revokeObjectURL"], "mappings": "AAA;4BCuC;CDK;4BEE;CFE"}}, "type": "js/module"}]}