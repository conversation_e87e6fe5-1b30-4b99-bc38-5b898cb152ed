{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hasTouchableProperty = hasTouchableProperty;\n  function hasTouchableProperty(props) {\n    return !!(props.onPress || props.onPressIn || props.onPressOut || props.onLongPress);\n  }\n});", "lineCount": 9, "map": [[6, 2, 1, 7], [6, 11, 1, 16, "hasTouchableProperty"], [6, 31, 1, 36, "hasTouchableProperty"], [6, 32, 1, 37, "props"], [6, 37, 1, 42], [6, 39, 1, 44], [7, 4, 2, 2], [7, 11, 2, 9], [7, 12, 2, 10], [7, 14, 2, 12, "props"], [7, 19, 2, 17], [7, 20, 2, 18, "onPress"], [7, 27, 2, 25], [7, 31, 2, 29, "props"], [7, 36, 2, 34], [7, 37, 2, 35, "onPressIn"], [7, 46, 2, 44], [7, 50, 2, 48, "props"], [7, 55, 2, 53], [7, 56, 2, 54, "onPressOut"], [7, 66, 2, 64], [7, 70, 2, 68, "props"], [7, 75, 2, 73], [7, 76, 2, 74, "onLongPress"], [7, 87, 2, 85], [7, 88, 2, 86], [8, 2, 3, 0], [9, 0, 3, 1], [9, 3]], "functionMap": {"names": ["<global>", "hasTouchableProperty"], "mappings": "AAA,OC;CDE"}}, "type": "js/module"}]}