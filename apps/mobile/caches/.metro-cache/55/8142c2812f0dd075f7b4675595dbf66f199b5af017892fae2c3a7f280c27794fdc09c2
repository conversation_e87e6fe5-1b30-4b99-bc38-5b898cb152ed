{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const UserPlus = exports.default = (0, _createLucideIcon.default)(\"UserPlus\", [[\"path\", {\n    d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n    key: \"1yyitq\"\n  }], [\"circle\", {\n    cx: \"9\",\n    cy: \"7\",\n    r: \"4\",\n    key: \"nufk8\"\n  }], [\"line\", {\n    x1: \"19\",\n    x2: \"19\",\n    y1: \"8\",\n    y2: \"14\",\n    key: \"1bvyxn\"\n  }], [\"line\", {\n    x1: \"22\",\n    x2: \"16\",\n    y1: \"11\",\n    y2: \"11\",\n    key: \"1shjgl\"\n  }]]);\n});", "lineCount": 36, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "UserPlus"], [15, 16, 10, 14], [15, 19, 10, 14, "exports"], [15, 26, 10, 14], [15, 27, 10, 14, "default"], [15, 34, 10, 14], [15, 37, 10, 17], [15, 41, 10, 17, "createLucideIcon"], [15, 66, 10, 33], [15, 68, 10, 34], [15, 78, 10, 44], [15, 80, 10, 46], [15, 81, 11, 2], [15, 82, 11, 3], [15, 88, 11, 9], [15, 90, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 50, 11, 59], [17, 4, 11, 61, "key"], [17, 7, 11, 64], [17, 9, 11, 66], [18, 2, 11, 75], [18, 3, 11, 76], [18, 4, 11, 77], [18, 6, 12, 2], [18, 7, 12, 3], [18, 15, 12, 11], [18, 17, 12, 13], [19, 4, 12, 15, "cx"], [19, 6, 12, 17], [19, 8, 12, 19], [19, 11, 12, 22], [20, 4, 12, 24, "cy"], [20, 6, 12, 26], [20, 8, 12, 28], [20, 11, 12, 31], [21, 4, 12, 33, "r"], [21, 5, 12, 34], [21, 7, 12, 36], [21, 10, 12, 39], [22, 4, 12, 41, "key"], [22, 7, 12, 44], [22, 9, 12, 46], [23, 2, 12, 54], [23, 3, 12, 55], [23, 4, 12, 56], [23, 6, 13, 2], [23, 7, 13, 3], [23, 13, 13, 9], [23, 15, 13, 11], [24, 4, 13, 13, "x1"], [24, 6, 13, 15], [24, 8, 13, 17], [24, 12, 13, 21], [25, 4, 13, 23, "x2"], [25, 6, 13, 25], [25, 8, 13, 27], [25, 12, 13, 31], [26, 4, 13, 33, "y1"], [26, 6, 13, 35], [26, 8, 13, 37], [26, 11, 13, 40], [27, 4, 13, 42, "y2"], [27, 6, 13, 44], [27, 8, 13, 46], [27, 12, 13, 50], [28, 4, 13, 52, "key"], [28, 7, 13, 55], [28, 9, 13, 57], [29, 2, 13, 66], [29, 3, 13, 67], [29, 4, 13, 68], [29, 6, 14, 2], [29, 7, 14, 3], [29, 13, 14, 9], [29, 15, 14, 11], [30, 4, 14, 13, "x1"], [30, 6, 14, 15], [30, 8, 14, 17], [30, 12, 14, 21], [31, 4, 14, 23, "x2"], [31, 6, 14, 25], [31, 8, 14, 27], [31, 12, 14, 31], [32, 4, 14, 33, "y1"], [32, 6, 14, 35], [32, 8, 14, 37], [32, 12, 14, 41], [33, 4, 14, 43, "y2"], [33, 6, 14, 45], [33, 8, 14, 47], [33, 12, 14, 51], [34, 4, 14, 53, "key"], [34, 7, 14, 56], [34, 9, 14, 58], [35, 2, 14, 67], [35, 3, 14, 68], [35, 4, 14, 69], [35, 5, 15, 1], [35, 6, 15, 2], [36, 0, 15, 3], [36, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}