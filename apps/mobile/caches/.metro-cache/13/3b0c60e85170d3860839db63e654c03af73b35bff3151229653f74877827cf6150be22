{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 57, "index": 57}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "fontfaceobserver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 58}, "end": {"line": 2, "column": 44, "index": 102}}], "key": "lGu76V8RISdu7s/yI5G3mTCgZXY=", "exportNames": ["*"]}}, {"name": "./Font.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 103}, "end": {"line": 3, "column": 43, "index": 146}}], "key": "iwvcxaVgfIXdww6iPrKSgtcaZy8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports._createWebFontTemplate = _createWebFontTemplate;\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _fontfaceobserver = _interopRequireDefault(require(_dependencyMap[2], \"fontfaceobserver\"));\n  var _Font = require(_dependencyMap[3], \"./Font.types\");\n  function getFontFaceStyleSheet() {\n    if (!_expoModulesCore.Platform.isDOMAvailable) {\n      return null;\n    }\n    const styleSheet = getStyleElement();\n    return styleSheet.sheet ? styleSheet.sheet : null;\n  }\n  function getFontFaceRules() {\n    const sheet = getFontFaceStyleSheet();\n    if (sheet) {\n      // @ts-ignore: rule iterator\n      const rules = [...sheet.cssRules];\n      const items = [];\n      for (let i = 0; i < rules.length; i++) {\n        const rule = rules[i];\n        if (rule instanceof CSSFontFaceRule) {\n          items.push({\n            rule,\n            index: i\n          });\n        }\n      }\n      return items;\n    }\n    return [];\n  }\n  function getFontFaceRulesMatchingResource(fontFamilyName, options) {\n    const rules = getFontFaceRules();\n    return rules.filter(({\n      rule\n    }) => {\n      return rule.style.fontFamily === fontFamilyName && (options && options.display ? options.display === rule.style.fontDisplay : true);\n    });\n  }\n  const serverContext = new Set();\n  function getHeadElements() {\n    const entries = [...serverContext.entries()];\n    if (!entries.length) {\n      return [];\n    }\n    const css = entries.map(([{\n      css\n    }]) => css).join('\\n');\n    const links = entries.map(([{\n      resourceId\n    }]) => resourceId);\n    // TODO: Maybe return nothing if no fonts were loaded.\n    return [{\n      $$type: 'style',\n      children: css,\n      id: ID,\n      type: 'text/css'\n    }, ...links.map(resourceId => ({\n      $$type: 'link',\n      rel: 'preload',\n      href: resourceId,\n      as: 'font',\n      crossorigin: ''\n    }))];\n  }\n  var _default = exports.default = {\n    async unloadAllAsync() {\n      if (!_expoModulesCore.Platform.isDOMAvailable) return;\n      const element = document.getElementById(ID);\n      if (element && element instanceof HTMLStyleElement) {\n        document.removeChild(element);\n      }\n    },\n    async unloadAsync(fontFamilyName, options) {\n      const sheet = getFontFaceStyleSheet();\n      if (!sheet) return;\n      const items = getFontFaceRulesMatchingResource(fontFamilyName, options);\n      for (const item of items) {\n        sheet.deleteRule(item.index);\n      }\n    },\n    getServerResources() {\n      const elements = getHeadElements();\n      return elements.map(element => {\n        switch (element.$$type) {\n          case 'style':\n            return `<style id=\"${element.id}\" type=\"${element.type}\">${element.children}</style>`;\n          case 'link':\n            return `<link rel=\"${element.rel}\" href=\"${element.href}\" as=\"${element.as}\" crossorigin=\"${element.crossorigin}\" />`;\n          default:\n            return '';\n        }\n      }).filter(Boolean);\n    },\n    resetServerContext() {\n      serverContext.clear();\n    },\n    getLoadedFonts() {\n      if (typeof window === 'undefined') {\n        return [...serverContext.values()].map(({\n          name\n        }) => name);\n      }\n      const rules = getFontFaceRules();\n      return rules.map(({\n        rule\n      }) => rule.style.fontFamily);\n    },\n    isLoaded(fontFamilyName, resource = {}) {\n      if (typeof window === 'undefined') {\n        return !![...serverContext.values()].find(asset => {\n          return asset.name === fontFamilyName;\n        });\n      }\n      return getFontFaceRulesMatchingResource(fontFamilyName, resource)?.length > 0;\n    },\n    // NOTE(EvanBacon): No async keyword! This cannot return a promise in Node environments.\n    loadAsync(fontFamilyName, resource) {\n      if (typeof window === 'undefined') {\n        serverContext.add({\n          name: fontFamilyName,\n          css: _createWebFontTemplate(fontFamilyName, resource),\n          // @ts-expect-error: typeof string\n          resourceId: resource.uri\n        });\n        return Promise.resolve();\n      }\n      const canInjectStyle = document.head && typeof document.head.appendChild === 'function';\n      if (!canInjectStyle) {\n        throw new _expoModulesCore.CodedError('ERR_WEB_ENVIRONMENT', `The browser's \\`document.head\\` element doesn't support injecting fonts.`);\n      }\n      const style = getStyleElement();\n      document.head.appendChild(style);\n      const res = getFontFaceRulesMatchingResource(fontFamilyName, resource);\n      if (!res.length) {\n        _createWebStyle(fontFamilyName, resource);\n      }\n      if (!isFontLoadingListenerSupported()) {\n        return Promise.resolve();\n      }\n      return new _fontfaceobserver.default(fontFamilyName, {\n        // @ts-expect-error: TODO(@kitten): Typings indicate that the polyfill may not support this?\n        display: resource.display\n      }).load(null, 6000);\n    }\n  };\n  const ID = 'expo-generated-fonts';\n  function getStyleElement() {\n    const element = document.getElementById(ID);\n    if (element && element instanceof HTMLStyleElement) {\n      return element;\n    }\n    const styleElement = document.createElement('style');\n    styleElement.id = ID;\n    styleElement.type = 'text/css';\n    return styleElement;\n  }\n  function _createWebFontTemplate(fontFamily, resource) {\n    return `@font-face{font-family:${fontFamily};src:url(${resource.uri});font-display:${resource.display || _Font.FontDisplay.AUTO}}`;\n  }\n  function _createWebStyle(fontFamily, resource) {\n    const fontStyle = _createWebFontTemplate(fontFamily, resource);\n    const styleElement = getStyleElement();\n    // @ts-ignore: TypeScript does not define HTMLStyleElement::styleSheet. This is just for IE and\n    // possibly can be removed if it's unnecessary on IE 11.\n    if (styleElement.styleSheet) {\n      const styleElementIE = styleElement;\n      styleElementIE.styleSheet.cssText = styleElementIE.styleSheet.cssText ? styleElementIE.styleSheet.cssText + fontStyle : fontStyle;\n    } else {\n      const textNode = document.createTextNode(fontStyle);\n      styleElement.appendChild(textNode);\n    }\n    return styleElement;\n  }\n  function isFontLoadingListenerSupported() {\n    const {\n      userAgent\n    } = window.navigator;\n    // WebKit is broken https://github.com/bramstein/fontfaceobserver/issues/95\n    const isIOS = !!userAgent.match(/iPad|iPhone/i);\n    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n    // Edge is broken https://github.com/bramstein/fontfaceobserver/issues/109#issuecomment-333356795\n    const isEdge = userAgent.includes('Edge');\n    // Internet Explorer\n    const isIE = userAgent.includes('Trident');\n    // Firefox\n    const isFirefox = userAgent.includes('Firefox');\n    return !isSafari && !isIOS && !isEdge && !isIE && !isFirefox;\n  }\n});", "lineCount": 195, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoModulesCore"], [8, 22, 1, 0], [8, 25, 1, 0, "require"], [8, 32, 1, 0], [8, 33, 1, 0, "_dependencyMap"], [8, 47, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_fontfaceobserver"], [9, 23, 2, 0], [9, 26, 2, 0, "_interopRequireDefault"], [9, 48, 2, 0], [9, 49, 2, 0, "require"], [9, 56, 2, 0], [9, 57, 2, 0, "_dependencyMap"], [9, 71, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_Font"], [10, 11, 3, 0], [10, 14, 3, 0, "require"], [10, 21, 3, 0], [10, 22, 3, 0, "_dependencyMap"], [10, 36, 3, 0], [11, 2, 4, 0], [11, 11, 4, 9, "getFontFaceStyleSheet"], [11, 32, 4, 30, "getFontFaceStyleSheet"], [11, 33, 4, 30], [11, 35, 4, 33], [12, 4, 5, 4], [12, 8, 5, 8], [12, 9, 5, 9, "Platform"], [12, 34, 5, 17], [12, 35, 5, 18, "isDOMAvailable"], [12, 49, 5, 32], [12, 51, 5, 34], [13, 6, 6, 8], [13, 13, 6, 15], [13, 17, 6, 19], [14, 4, 7, 4], [15, 4, 8, 4], [15, 10, 8, 10, "styleSheet"], [15, 20, 8, 20], [15, 23, 8, 23, "getStyleElement"], [15, 38, 8, 38], [15, 39, 8, 39], [15, 40, 8, 40], [16, 4, 9, 4], [16, 11, 9, 11, "styleSheet"], [16, 21, 9, 21], [16, 22, 9, 22, "sheet"], [16, 27, 9, 27], [16, 30, 9, 30, "styleSheet"], [16, 40, 9, 40], [16, 41, 9, 41, "sheet"], [16, 46, 9, 46], [16, 49, 9, 49], [16, 53, 9, 53], [17, 2, 10, 0], [18, 2, 11, 0], [18, 11, 11, 9, "getFontFaceRules"], [18, 27, 11, 25, "getFontFaceRules"], [18, 28, 11, 25], [18, 30, 11, 28], [19, 4, 12, 4], [19, 10, 12, 10, "sheet"], [19, 15, 12, 15], [19, 18, 12, 18, "getFontFaceStyleSheet"], [19, 39, 12, 39], [19, 40, 12, 40], [19, 41, 12, 41], [20, 4, 13, 4], [20, 8, 13, 8, "sheet"], [20, 13, 13, 13], [20, 15, 13, 15], [21, 6, 14, 8], [22, 6, 15, 8], [22, 12, 15, 14, "rules"], [22, 17, 15, 19], [22, 20, 15, 22], [22, 21, 15, 23], [22, 24, 15, 26, "sheet"], [22, 29, 15, 31], [22, 30, 15, 32, "cssRules"], [22, 38, 15, 40], [22, 39, 15, 41], [23, 6, 16, 8], [23, 12, 16, 14, "items"], [23, 17, 16, 19], [23, 20, 16, 22], [23, 22, 16, 24], [24, 6, 17, 8], [24, 11, 17, 13], [24, 15, 17, 17, "i"], [24, 16, 17, 18], [24, 19, 17, 21], [24, 20, 17, 22], [24, 22, 17, 24, "i"], [24, 23, 17, 25], [24, 26, 17, 28, "rules"], [24, 31, 17, 33], [24, 32, 17, 34, "length"], [24, 38, 17, 40], [24, 40, 17, 42, "i"], [24, 41, 17, 43], [24, 43, 17, 45], [24, 45, 17, 47], [25, 8, 18, 12], [25, 14, 18, 18, "rule"], [25, 18, 18, 22], [25, 21, 18, 25, "rules"], [25, 26, 18, 30], [25, 27, 18, 31, "i"], [25, 28, 18, 32], [25, 29, 18, 33], [26, 8, 19, 12], [26, 12, 19, 16, "rule"], [26, 16, 19, 20], [26, 28, 19, 32, "CSSFontFaceRule"], [26, 43, 19, 47], [26, 45, 19, 49], [27, 10, 20, 16, "items"], [27, 15, 20, 21], [27, 16, 20, 22, "push"], [27, 20, 20, 26], [27, 21, 20, 27], [28, 12, 20, 29, "rule"], [28, 16, 20, 33], [29, 12, 20, 35, "index"], [29, 17, 20, 40], [29, 19, 20, 42, "i"], [30, 10, 20, 44], [30, 11, 20, 45], [30, 12, 20, 46], [31, 8, 21, 12], [32, 6, 22, 8], [33, 6, 23, 8], [33, 13, 23, 15, "items"], [33, 18, 23, 20], [34, 4, 24, 4], [35, 4, 25, 4], [35, 11, 25, 11], [35, 13, 25, 13], [36, 2, 26, 0], [37, 2, 27, 0], [37, 11, 27, 9, "getFontFaceRulesMatchingResource"], [37, 43, 27, 41, "getFontFaceRulesMatchingResource"], [37, 44, 27, 42, "fontFamilyName"], [37, 58, 27, 56], [37, 60, 27, 58, "options"], [37, 67, 27, 65], [37, 69, 27, 67], [38, 4, 28, 4], [38, 10, 28, 10, "rules"], [38, 15, 28, 15], [38, 18, 28, 18, "getFontFaceRules"], [38, 34, 28, 34], [38, 35, 28, 35], [38, 36, 28, 36], [39, 4, 29, 4], [39, 11, 29, 11, "rules"], [39, 16, 29, 16], [39, 17, 29, 17, "filter"], [39, 23, 29, 23], [39, 24, 29, 24], [39, 25, 29, 25], [40, 6, 29, 27, "rule"], [41, 4, 29, 32], [41, 5, 29, 33], [41, 10, 29, 38], [42, 6, 30, 8], [42, 13, 30, 16, "rule"], [42, 17, 30, 20], [42, 18, 30, 21, "style"], [42, 23, 30, 26], [42, 24, 30, 27, "fontFamily"], [42, 34, 30, 37], [42, 39, 30, 42, "fontFamilyName"], [42, 53, 30, 56], [42, 58, 31, 13, "options"], [42, 65, 31, 20], [42, 69, 31, 24, "options"], [42, 76, 31, 31], [42, 77, 31, 32, "display"], [42, 84, 31, 39], [42, 87, 31, 42, "options"], [42, 94, 31, 49], [42, 95, 31, 50, "display"], [42, 102, 31, 57], [42, 107, 31, 62, "rule"], [42, 111, 31, 66], [42, 112, 31, 67, "style"], [42, 117, 31, 72], [42, 118, 31, 73, "fontDisplay"], [42, 129, 31, 84], [42, 132, 31, 87], [42, 136, 31, 91], [42, 137, 31, 92], [43, 4, 32, 4], [43, 5, 32, 5], [43, 6, 32, 6], [44, 2, 33, 0], [45, 2, 34, 0], [45, 8, 34, 6, "serverContext"], [45, 21, 34, 19], [45, 24, 34, 22], [45, 28, 34, 26, "Set"], [45, 31, 34, 29], [45, 32, 34, 30], [45, 33, 34, 31], [46, 2, 35, 0], [46, 11, 35, 9, "getHeadElements"], [46, 26, 35, 24, "getHeadElements"], [46, 27, 35, 24], [46, 29, 35, 27], [47, 4, 36, 4], [47, 10, 36, 10, "entries"], [47, 17, 36, 17], [47, 20, 36, 20], [47, 21, 36, 21], [47, 24, 36, 24, "serverContext"], [47, 37, 36, 37], [47, 38, 36, 38, "entries"], [47, 45, 36, 45], [47, 46, 36, 46], [47, 47, 36, 47], [47, 48, 36, 48], [48, 4, 37, 4], [48, 8, 37, 8], [48, 9, 37, 9, "entries"], [48, 16, 37, 16], [48, 17, 37, 17, "length"], [48, 23, 37, 23], [48, 25, 37, 25], [49, 6, 38, 8], [49, 13, 38, 15], [49, 15, 38, 17], [50, 4, 39, 4], [51, 4, 40, 4], [51, 10, 40, 10, "css"], [51, 13, 40, 13], [51, 16, 40, 16, "entries"], [51, 23, 40, 23], [51, 24, 40, 24, "map"], [51, 27, 40, 27], [51, 28, 40, 28], [51, 29, 40, 29], [51, 30, 40, 30], [52, 6, 40, 32, "css"], [53, 4, 40, 36], [53, 5, 40, 37], [53, 6, 40, 38], [53, 11, 40, 43, "css"], [53, 14, 40, 46], [53, 15, 40, 47], [53, 16, 40, 48, "join"], [53, 20, 40, 52], [53, 21, 40, 53], [53, 25, 40, 57], [53, 26, 40, 58], [54, 4, 41, 4], [54, 10, 41, 10, "links"], [54, 15, 41, 15], [54, 18, 41, 18, "entries"], [54, 25, 41, 25], [54, 26, 41, 26, "map"], [54, 29, 41, 29], [54, 30, 41, 30], [54, 31, 41, 31], [54, 32, 41, 32], [55, 6, 41, 34, "resourceId"], [56, 4, 41, 45], [56, 5, 41, 46], [56, 6, 41, 47], [56, 11, 41, 52, "resourceId"], [56, 21, 41, 62], [56, 22, 41, 63], [57, 4, 42, 4], [58, 4, 43, 4], [58, 11, 43, 11], [58, 12, 44, 8], [59, 6, 45, 12, "$$type"], [59, 12, 45, 18], [59, 14, 45, 20], [59, 21, 45, 27], [60, 6, 46, 12, "children"], [60, 14, 46, 20], [60, 16, 46, 22, "css"], [60, 19, 46, 25], [61, 6, 47, 12, "id"], [61, 8, 47, 14], [61, 10, 47, 16, "ID"], [61, 12, 47, 18], [62, 6, 48, 12, "type"], [62, 10, 48, 16], [62, 12, 48, 18], [63, 4, 49, 8], [63, 5, 49, 9], [63, 7, 50, 8], [63, 10, 50, 11, "links"], [63, 15, 50, 16], [63, 16, 50, 17, "map"], [63, 19, 50, 20], [63, 20, 50, 22, "resourceId"], [63, 30, 50, 32], [63, 35, 50, 38], [64, 6, 51, 12, "$$type"], [64, 12, 51, 18], [64, 14, 51, 20], [64, 20, 51, 26], [65, 6, 52, 12, "rel"], [65, 9, 52, 15], [65, 11, 52, 17], [65, 20, 52, 26], [66, 6, 53, 12, "href"], [66, 10, 53, 16], [66, 12, 53, 18, "resourceId"], [66, 22, 53, 28], [67, 6, 54, 12, "as"], [67, 8, 54, 14], [67, 10, 54, 16], [67, 16, 54, 22], [68, 6, 55, 12, "crossorigin"], [68, 17, 55, 23], [68, 19, 55, 25], [69, 4, 56, 8], [69, 5, 56, 9], [69, 6, 56, 10], [69, 7, 56, 11], [69, 8, 57, 5], [70, 2, 58, 0], [71, 2, 58, 1], [71, 6, 58, 1, "_default"], [71, 14, 58, 1], [71, 17, 58, 1, "exports"], [71, 24, 58, 1], [71, 25, 58, 1, "default"], [71, 32, 58, 1], [71, 35, 59, 15], [72, 4, 60, 4], [72, 10, 60, 10, "unloadAllAsync"], [72, 24, 60, 24, "unloadAllAsync"], [72, 25, 60, 24], [72, 27, 60, 27], [73, 6, 61, 8], [73, 10, 61, 12], [73, 11, 61, 13, "Platform"], [73, 36, 61, 21], [73, 37, 61, 22, "isDOMAvailable"], [73, 51, 61, 36], [73, 53, 62, 12], [74, 6, 63, 8], [74, 12, 63, 14, "element"], [74, 19, 63, 21], [74, 22, 63, 24, "document"], [74, 30, 63, 32], [74, 31, 63, 33, "getElementById"], [74, 45, 63, 47], [74, 46, 63, 48, "ID"], [74, 48, 63, 50], [74, 49, 63, 51], [75, 6, 64, 8], [75, 10, 64, 12, "element"], [75, 17, 64, 19], [75, 21, 64, 23, "element"], [75, 28, 64, 30], [75, 40, 64, 42, "HTMLStyleElement"], [75, 56, 64, 58], [75, 58, 64, 60], [76, 8, 65, 12, "document"], [76, 16, 65, 20], [76, 17, 65, 21, "<PERSON><PERSON><PERSON><PERSON>"], [76, 28, 65, 32], [76, 29, 65, 33, "element"], [76, 36, 65, 40], [76, 37, 65, 41], [77, 6, 66, 8], [78, 4, 67, 4], [78, 5, 67, 5], [79, 4, 68, 4], [79, 10, 68, 10, "unloadAsync"], [79, 21, 68, 21, "unloadAsync"], [79, 22, 68, 22, "fontFamilyName"], [79, 36, 68, 36], [79, 38, 68, 38, "options"], [79, 45, 68, 45], [79, 47, 68, 47], [80, 6, 69, 8], [80, 12, 69, 14, "sheet"], [80, 17, 69, 19], [80, 20, 69, 22, "getFontFaceStyleSheet"], [80, 41, 69, 43], [80, 42, 69, 44], [80, 43, 69, 45], [81, 6, 70, 8], [81, 10, 70, 12], [81, 11, 70, 13, "sheet"], [81, 16, 70, 18], [81, 18, 71, 12], [82, 6, 72, 8], [82, 12, 72, 14, "items"], [82, 17, 72, 19], [82, 20, 72, 22, "getFontFaceRulesMatchingResource"], [82, 52, 72, 54], [82, 53, 72, 55, "fontFamilyName"], [82, 67, 72, 69], [82, 69, 72, 71, "options"], [82, 76, 72, 78], [82, 77, 72, 79], [83, 6, 73, 8], [83, 11, 73, 13], [83, 17, 73, 19, "item"], [83, 21, 73, 23], [83, 25, 73, 27, "items"], [83, 30, 73, 32], [83, 32, 73, 34], [84, 8, 74, 12, "sheet"], [84, 13, 74, 17], [84, 14, 74, 18, "deleteRule"], [84, 24, 74, 28], [84, 25, 74, 29, "item"], [84, 29, 74, 33], [84, 30, 74, 34, "index"], [84, 35, 74, 39], [84, 36, 74, 40], [85, 6, 75, 8], [86, 4, 76, 4], [86, 5, 76, 5], [87, 4, 77, 4, "getServerResources"], [87, 22, 77, 22, "getServerResources"], [87, 23, 77, 22], [87, 25, 77, 25], [88, 6, 78, 8], [88, 12, 78, 14, "elements"], [88, 20, 78, 22], [88, 23, 78, 25, "getHeadElements"], [88, 38, 78, 40], [88, 39, 78, 41], [88, 40, 78, 42], [89, 6, 79, 8], [89, 13, 79, 15, "elements"], [89, 21, 79, 23], [89, 22, 80, 13, "map"], [89, 25, 80, 16], [89, 26, 80, 18, "element"], [89, 33, 80, 25], [89, 37, 80, 30], [90, 8, 81, 12], [90, 16, 81, 20, "element"], [90, 23, 81, 27], [90, 24, 81, 28, "$$type"], [90, 30, 81, 34], [91, 10, 82, 16], [91, 15, 82, 21], [91, 22, 82, 28], [92, 12, 83, 20], [92, 19, 83, 27], [92, 33, 83, 41, "element"], [92, 40, 83, 48], [92, 41, 83, 49, "id"], [92, 43, 83, 51], [92, 54, 83, 62, "element"], [92, 61, 83, 69], [92, 62, 83, 70, "type"], [92, 66, 83, 74], [92, 71, 83, 79, "element"], [92, 78, 83, 86], [92, 79, 83, 87, "children"], [92, 87, 83, 95], [92, 97, 83, 105], [93, 10, 84, 16], [93, 15, 84, 21], [93, 21, 84, 27], [94, 12, 85, 20], [94, 19, 85, 27], [94, 33, 85, 41, "element"], [94, 40, 85, 48], [94, 41, 85, 49, "rel"], [94, 44, 85, 52], [94, 55, 85, 63, "element"], [94, 62, 85, 70], [94, 63, 85, 71, "href"], [94, 67, 85, 75], [94, 76, 85, 84, "element"], [94, 83, 85, 91], [94, 84, 85, 92, "as"], [94, 86, 85, 94], [94, 104, 85, 112, "element"], [94, 111, 85, 119], [94, 112, 85, 120, "crossorigin"], [94, 123, 85, 131], [94, 129, 85, 137], [95, 10, 86, 16], [96, 12, 87, 20], [96, 19, 87, 27], [96, 21, 87, 29], [97, 8, 88, 12], [98, 6, 89, 8], [98, 7, 89, 9], [98, 8, 89, 10], [98, 9, 90, 13, "filter"], [98, 15, 90, 19], [98, 16, 90, 20, "Boolean"], [98, 23, 90, 27], [98, 24, 90, 28], [99, 4, 91, 4], [99, 5, 91, 5], [100, 4, 92, 4, "resetServerContext"], [100, 22, 92, 22, "resetServerContext"], [100, 23, 92, 22], [100, 25, 92, 25], [101, 6, 93, 8, "serverContext"], [101, 19, 93, 21], [101, 20, 93, 22, "clear"], [101, 25, 93, 27], [101, 26, 93, 28], [101, 27, 93, 29], [102, 4, 94, 4], [102, 5, 94, 5], [103, 4, 95, 4, "getLoadedFonts"], [103, 18, 95, 18, "getLoadedFonts"], [103, 19, 95, 18], [103, 21, 95, 21], [104, 6, 96, 8], [104, 10, 96, 12], [104, 17, 96, 19, "window"], [104, 23, 96, 25], [104, 28, 96, 30], [104, 39, 96, 41], [104, 41, 96, 43], [105, 8, 97, 12], [105, 15, 97, 19], [105, 16, 97, 20], [105, 19, 97, 23, "serverContext"], [105, 32, 97, 36], [105, 33, 97, 37, "values"], [105, 39, 97, 43], [105, 40, 97, 44], [105, 41, 97, 45], [105, 42, 97, 46], [105, 43, 97, 47, "map"], [105, 46, 97, 50], [105, 47, 97, 51], [105, 48, 97, 52], [106, 10, 97, 54, "name"], [107, 8, 97, 59], [107, 9, 97, 60], [107, 14, 97, 65, "name"], [107, 18, 97, 69], [107, 19, 97, 70], [108, 6, 98, 8], [109, 6, 99, 8], [109, 12, 99, 14, "rules"], [109, 17, 99, 19], [109, 20, 99, 22, "getFontFaceRules"], [109, 36, 99, 38], [109, 37, 99, 39], [109, 38, 99, 40], [110, 6, 100, 8], [110, 13, 100, 15, "rules"], [110, 18, 100, 20], [110, 19, 100, 21, "map"], [110, 22, 100, 24], [110, 23, 100, 25], [110, 24, 100, 26], [111, 8, 100, 28, "rule"], [112, 6, 100, 33], [112, 7, 100, 34], [112, 12, 100, 39, "rule"], [112, 16, 100, 43], [112, 17, 100, 44, "style"], [112, 22, 100, 49], [112, 23, 100, 50, "fontFamily"], [112, 33, 100, 60], [112, 34, 100, 61], [113, 4, 101, 4], [113, 5, 101, 5], [114, 4, 102, 4, "isLoaded"], [114, 12, 102, 12, "isLoaded"], [114, 13, 102, 13, "fontFamilyName"], [114, 27, 102, 27], [114, 29, 102, 29, "resource"], [114, 37, 102, 37], [114, 40, 102, 40], [114, 41, 102, 41], [114, 42, 102, 42], [114, 44, 102, 44], [115, 6, 103, 8], [115, 10, 103, 12], [115, 17, 103, 19, "window"], [115, 23, 103, 25], [115, 28, 103, 30], [115, 39, 103, 41], [115, 41, 103, 43], [116, 8, 104, 12], [116, 15, 104, 19], [116, 16, 104, 20], [116, 17, 104, 21], [116, 18, 104, 22], [116, 21, 104, 25, "serverContext"], [116, 34, 104, 38], [116, 35, 104, 39, "values"], [116, 41, 104, 45], [116, 42, 104, 46], [116, 43, 104, 47], [116, 44, 104, 48], [116, 45, 104, 49, "find"], [116, 49, 104, 53], [116, 50, 104, 55, "asset"], [116, 55, 104, 60], [116, 59, 104, 65], [117, 10, 105, 16], [117, 17, 105, 23, "asset"], [117, 22, 105, 28], [117, 23, 105, 29, "name"], [117, 27, 105, 33], [117, 32, 105, 38, "fontFamilyName"], [117, 46, 105, 52], [118, 8, 106, 12], [118, 9, 106, 13], [118, 10, 106, 14], [119, 6, 107, 8], [120, 6, 108, 8], [120, 13, 108, 15, "getFontFaceRulesMatchingResource"], [120, 45, 108, 47], [120, 46, 108, 48, "fontFamilyName"], [120, 60, 108, 62], [120, 62, 108, 64, "resource"], [120, 70, 108, 72], [120, 71, 108, 73], [120, 73, 108, 75, "length"], [120, 79, 108, 81], [120, 82, 108, 84], [120, 83, 108, 85], [121, 4, 109, 4], [121, 5, 109, 5], [122, 4, 110, 4], [123, 4, 111, 4, "loadAsync"], [123, 13, 111, 13, "loadAsync"], [123, 14, 111, 14, "fontFamilyName"], [123, 28, 111, 28], [123, 30, 111, 30, "resource"], [123, 38, 111, 38], [123, 40, 111, 40], [124, 6, 112, 8], [124, 10, 112, 12], [124, 17, 112, 19, "window"], [124, 23, 112, 25], [124, 28, 112, 30], [124, 39, 112, 41], [124, 41, 112, 43], [125, 8, 113, 12, "serverContext"], [125, 21, 113, 25], [125, 22, 113, 26, "add"], [125, 25, 113, 29], [125, 26, 113, 30], [126, 10, 114, 16, "name"], [126, 14, 114, 20], [126, 16, 114, 22, "fontFamilyName"], [126, 30, 114, 36], [127, 10, 115, 16, "css"], [127, 13, 115, 19], [127, 15, 115, 21, "_createWebFontTemplate"], [127, 37, 115, 43], [127, 38, 115, 44, "fontFamilyName"], [127, 52, 115, 58], [127, 54, 115, 60, "resource"], [127, 62, 115, 68], [127, 63, 115, 69], [128, 10, 116, 16], [129, 10, 117, 16, "resourceId"], [129, 20, 117, 26], [129, 22, 117, 28, "resource"], [129, 30, 117, 36], [129, 31, 117, 37, "uri"], [130, 8, 118, 12], [130, 9, 118, 13], [130, 10, 118, 14], [131, 8, 119, 12], [131, 15, 119, 19, "Promise"], [131, 22, 119, 26], [131, 23, 119, 27, "resolve"], [131, 30, 119, 34], [131, 31, 119, 35], [131, 32, 119, 36], [132, 6, 120, 8], [133, 6, 121, 8], [133, 12, 121, 14, "canInjectStyle"], [133, 26, 121, 28], [133, 29, 121, 31, "document"], [133, 37, 121, 39], [133, 38, 121, 40, "head"], [133, 42, 121, 44], [133, 46, 121, 48], [133, 53, 121, 55, "document"], [133, 61, 121, 63], [133, 62, 121, 64, "head"], [133, 66, 121, 68], [133, 67, 121, 69, "append<PERSON><PERSON><PERSON>"], [133, 78, 121, 80], [133, 83, 121, 85], [133, 93, 121, 95], [134, 6, 122, 8], [134, 10, 122, 12], [134, 11, 122, 13, "canInjectStyle"], [134, 25, 122, 27], [134, 27, 122, 29], [135, 8, 123, 12], [135, 14, 123, 18], [135, 18, 123, 22, "CodedError"], [135, 45, 123, 32], [135, 46, 123, 33], [135, 67, 123, 54], [135, 69, 123, 56], [135, 143, 123, 130], [135, 144, 123, 131], [136, 6, 124, 8], [137, 6, 125, 8], [137, 12, 125, 14, "style"], [137, 17, 125, 19], [137, 20, 125, 22, "getStyleElement"], [137, 35, 125, 37], [137, 36, 125, 38], [137, 37, 125, 39], [138, 6, 126, 8, "document"], [138, 14, 126, 16], [138, 15, 126, 17, "head"], [138, 19, 126, 21], [138, 20, 126, 22, "append<PERSON><PERSON><PERSON>"], [138, 31, 126, 33], [138, 32, 126, 34, "style"], [138, 37, 126, 39], [138, 38, 126, 40], [139, 6, 127, 8], [139, 12, 127, 14, "res"], [139, 15, 127, 17], [139, 18, 127, 20, "getFontFaceRulesMatchingResource"], [139, 50, 127, 52], [139, 51, 127, 53, "fontFamilyName"], [139, 65, 127, 67], [139, 67, 127, 69, "resource"], [139, 75, 127, 77], [139, 76, 127, 78], [140, 6, 128, 8], [140, 10, 128, 12], [140, 11, 128, 13, "res"], [140, 14, 128, 16], [140, 15, 128, 17, "length"], [140, 21, 128, 23], [140, 23, 128, 25], [141, 8, 129, 12, "_createWebStyle"], [141, 23, 129, 27], [141, 24, 129, 28, "fontFamilyName"], [141, 38, 129, 42], [141, 40, 129, 44, "resource"], [141, 48, 129, 52], [141, 49, 129, 53], [142, 6, 130, 8], [143, 6, 131, 8], [143, 10, 131, 12], [143, 11, 131, 13, "isFontLoadingListenerSupported"], [143, 41, 131, 43], [143, 42, 131, 44], [143, 43, 131, 45], [143, 45, 131, 47], [144, 8, 132, 12], [144, 15, 132, 19, "Promise"], [144, 22, 132, 26], [144, 23, 132, 27, "resolve"], [144, 30, 132, 34], [144, 31, 132, 35], [144, 32, 132, 36], [145, 6, 133, 8], [146, 6, 134, 8], [146, 13, 134, 15], [146, 17, 134, 19, "FontObserver"], [146, 42, 134, 31], [146, 43, 134, 32, "fontFamilyName"], [146, 57, 134, 46], [146, 59, 134, 48], [147, 8, 135, 12], [148, 8, 136, 12, "display"], [148, 15, 136, 19], [148, 17, 136, 21, "resource"], [148, 25, 136, 29], [148, 26, 136, 30, "display"], [149, 6, 137, 8], [149, 7, 137, 9], [149, 8, 137, 10], [149, 9, 137, 11, "load"], [149, 13, 137, 15], [149, 14, 137, 16], [149, 18, 137, 20], [149, 20, 137, 22], [149, 24, 137, 26], [149, 25, 137, 27], [150, 4, 138, 4], [151, 2, 139, 0], [151, 3, 139, 1], [152, 2, 140, 0], [152, 8, 140, 6, "ID"], [152, 10, 140, 8], [152, 13, 140, 11], [152, 35, 140, 33], [153, 2, 141, 0], [153, 11, 141, 9, "getStyleElement"], [153, 26, 141, 24, "getStyleElement"], [153, 27, 141, 24], [153, 29, 141, 27], [154, 4, 142, 4], [154, 10, 142, 10, "element"], [154, 17, 142, 17], [154, 20, 142, 20, "document"], [154, 28, 142, 28], [154, 29, 142, 29, "getElementById"], [154, 43, 142, 43], [154, 44, 142, 44, "ID"], [154, 46, 142, 46], [154, 47, 142, 47], [155, 4, 143, 4], [155, 8, 143, 8, "element"], [155, 15, 143, 15], [155, 19, 143, 19, "element"], [155, 26, 143, 26], [155, 38, 143, 38, "HTMLStyleElement"], [155, 54, 143, 54], [155, 56, 143, 56], [156, 6, 144, 8], [156, 13, 144, 15, "element"], [156, 20, 144, 22], [157, 4, 145, 4], [158, 4, 146, 4], [158, 10, 146, 10, "styleElement"], [158, 22, 146, 22], [158, 25, 146, 25, "document"], [158, 33, 146, 33], [158, 34, 146, 34, "createElement"], [158, 47, 146, 47], [158, 48, 146, 48], [158, 55, 146, 55], [158, 56, 146, 56], [159, 4, 147, 4, "styleElement"], [159, 16, 147, 16], [159, 17, 147, 17, "id"], [159, 19, 147, 19], [159, 22, 147, 22, "ID"], [159, 24, 147, 24], [160, 4, 148, 4, "styleElement"], [160, 16, 148, 16], [160, 17, 148, 17, "type"], [160, 21, 148, 21], [160, 24, 148, 24], [160, 34, 148, 34], [161, 4, 149, 4], [161, 11, 149, 11, "styleElement"], [161, 23, 149, 23], [162, 2, 150, 0], [163, 2, 151, 7], [163, 11, 151, 16, "_createWebFontTemplate"], [163, 33, 151, 38, "_createWebFontTemplate"], [163, 34, 151, 39, "fontFamily"], [163, 44, 151, 49], [163, 46, 151, 51, "resource"], [163, 54, 151, 59], [163, 56, 151, 61], [164, 4, 152, 4], [164, 11, 152, 11], [164, 37, 152, 37, "fontFamily"], [164, 47, 152, 47], [164, 59, 152, 59, "resource"], [164, 67, 152, 67], [164, 68, 152, 68, "uri"], [164, 71, 152, 71], [164, 89, 152, 89, "resource"], [164, 97, 152, 97], [164, 98, 152, 98, "display"], [164, 105, 152, 105], [164, 109, 152, 109, "FontDisplay"], [164, 126, 152, 120], [164, 127, 152, 121, "AUTO"], [164, 131, 152, 125], [164, 134, 152, 128], [165, 2, 153, 0], [166, 2, 154, 0], [166, 11, 154, 9, "_createWebStyle"], [166, 26, 154, 24, "_createWebStyle"], [166, 27, 154, 25, "fontFamily"], [166, 37, 154, 35], [166, 39, 154, 37, "resource"], [166, 47, 154, 45], [166, 49, 154, 47], [167, 4, 155, 4], [167, 10, 155, 10, "fontStyle"], [167, 19, 155, 19], [167, 22, 155, 22, "_createWebFontTemplate"], [167, 44, 155, 44], [167, 45, 155, 45, "fontFamily"], [167, 55, 155, 55], [167, 57, 155, 57, "resource"], [167, 65, 155, 65], [167, 66, 155, 66], [168, 4, 156, 4], [168, 10, 156, 10, "styleElement"], [168, 22, 156, 22], [168, 25, 156, 25, "getStyleElement"], [168, 40, 156, 40], [168, 41, 156, 41], [168, 42, 156, 42], [169, 4, 157, 4], [170, 4, 158, 4], [171, 4, 159, 4], [171, 8, 159, 8, "styleElement"], [171, 20, 159, 20], [171, 21, 159, 21, "styleSheet"], [171, 31, 159, 31], [171, 33, 159, 33], [172, 6, 160, 8], [172, 12, 160, 14, "styleElementIE"], [172, 26, 160, 28], [172, 29, 160, 31, "styleElement"], [172, 41, 160, 43], [173, 6, 161, 8, "styleElementIE"], [173, 20, 161, 22], [173, 21, 161, 23, "styleSheet"], [173, 31, 161, 33], [173, 32, 161, 34, "cssText"], [173, 39, 161, 41], [173, 42, 161, 44, "styleElementIE"], [173, 56, 161, 58], [173, 57, 161, 59, "styleSheet"], [173, 67, 161, 69], [173, 68, 161, 70, "cssText"], [173, 75, 161, 77], [173, 78, 162, 14, "styleElementIE"], [173, 92, 162, 28], [173, 93, 162, 29, "styleSheet"], [173, 103, 162, 39], [173, 104, 162, 40, "cssText"], [173, 111, 162, 47], [173, 114, 162, 50, "fontStyle"], [173, 123, 162, 59], [173, 126, 163, 14, "fontStyle"], [173, 135, 163, 23], [174, 4, 164, 4], [174, 5, 164, 5], [174, 11, 165, 9], [175, 6, 166, 8], [175, 12, 166, 14, "textNode"], [175, 20, 166, 22], [175, 23, 166, 25, "document"], [175, 31, 166, 33], [175, 32, 166, 34, "createTextNode"], [175, 46, 166, 48], [175, 47, 166, 49, "fontStyle"], [175, 56, 166, 58], [175, 57, 166, 59], [176, 6, 167, 8, "styleElement"], [176, 18, 167, 20], [176, 19, 167, 21, "append<PERSON><PERSON><PERSON>"], [176, 30, 167, 32], [176, 31, 167, 33, "textNode"], [176, 39, 167, 41], [176, 40, 167, 42], [177, 4, 168, 4], [178, 4, 169, 4], [178, 11, 169, 11, "styleElement"], [178, 23, 169, 23], [179, 2, 170, 0], [180, 2, 171, 0], [180, 11, 171, 9, "isFontLoadingListenerSupported"], [180, 41, 171, 39, "isFontLoadingListenerSupported"], [180, 42, 171, 39], [180, 44, 171, 42], [181, 4, 172, 4], [181, 10, 172, 10], [182, 6, 172, 12, "userAgent"], [183, 4, 172, 22], [183, 5, 172, 23], [183, 8, 172, 26, "window"], [183, 14, 172, 32], [183, 15, 172, 33, "navigator"], [183, 24, 172, 42], [184, 4, 173, 4], [185, 4, 174, 4], [185, 10, 174, 10, "isIOS"], [185, 15, 174, 15], [185, 18, 174, 18], [185, 19, 174, 19], [185, 20, 174, 20, "userAgent"], [185, 29, 174, 29], [185, 30, 174, 30, "match"], [185, 35, 174, 35], [185, 36, 174, 36], [185, 50, 174, 50], [185, 51, 174, 51], [186, 4, 175, 4], [186, 10, 175, 10, "<PERSON><PERSON><PERSON><PERSON>"], [186, 18, 175, 18], [186, 21, 175, 21], [186, 53, 175, 53], [186, 54, 175, 54, "test"], [186, 58, 175, 58], [186, 59, 175, 59, "navigator"], [186, 68, 175, 68], [186, 69, 175, 69, "userAgent"], [186, 78, 175, 78], [186, 79, 175, 79], [187, 4, 176, 4], [188, 4, 177, 4], [188, 10, 177, 10, "isEdge"], [188, 16, 177, 16], [188, 19, 177, 19, "userAgent"], [188, 28, 177, 28], [188, 29, 177, 29, "includes"], [188, 37, 177, 37], [188, 38, 177, 38], [188, 44, 177, 44], [188, 45, 177, 45], [189, 4, 178, 4], [190, 4, 179, 4], [190, 10, 179, 10, "isIE"], [190, 14, 179, 14], [190, 17, 179, 17, "userAgent"], [190, 26, 179, 26], [190, 27, 179, 27, "includes"], [190, 35, 179, 35], [190, 36, 179, 36], [190, 45, 179, 45], [190, 46, 179, 46], [191, 4, 180, 4], [192, 4, 181, 4], [192, 10, 181, 10, "isFirefox"], [192, 19, 181, 19], [192, 22, 181, 22, "userAgent"], [192, 31, 181, 31], [192, 32, 181, 32, "includes"], [192, 40, 181, 40], [192, 41, 181, 41], [192, 50, 181, 50], [192, 51, 181, 51], [193, 4, 182, 4], [193, 11, 182, 11], [193, 12, 182, 12, "<PERSON><PERSON><PERSON><PERSON>"], [193, 20, 182, 20], [193, 24, 182, 24], [193, 25, 182, 25, "isIOS"], [193, 30, 182, 30], [193, 34, 182, 34], [193, 35, 182, 35, "isEdge"], [193, 41, 182, 41], [193, 45, 182, 45], [193, 46, 182, 46, "isIE"], [193, 50, 182, 50], [193, 54, 182, 54], [193, 55, 182, 55, "isFirefox"], [193, 64, 182, 64], [194, 2, 183, 0], [195, 0, 183, 1], [195, 3]], "functionMap": {"names": ["<global>", "getFontFaceStyleSheet", "getFontFaceRules", "getFontFaceRulesMatchingResource", "rules.filter$argument_0", "getHeadElements", "entries.map$argument_0", "links.map$argument_0", "default.unloadAllAsync", "default.unloadAsync", "default.getServerResources", "elements.map$argument_0", "default.resetServerContext", "default.getLoadedFonts", "map$argument_0", "rules.map$argument_0", "default.isLoaded", "find$argument_0", "default.loadAsync", "getStyleElement", "_createWebFontTemplate", "_createWebStyle", "isFontLoadingListenerSupported"], "mappings": "AAA;ACG;CDM;AEC;CFe;AGC;wBCE;KDG;CHC;AKE;4BCK,kBD;8BCC,gCD;qBES;UFM;CLE;IQE;KRO;ISC;KTQ;IUC;iBCG;SDS;KVE;IYC;KZE;IaC;mDCE,kBD;yBEG,mCF;KbC;IgBC;sDCE;aDE;KhBG;IkBE;KlB2B;AmBG;CnBS;OoBC;CpBE;AqBC;CrBgB;AsBC;CtBY"}}, "type": "js/module"}]}