{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../specs/RNGestureHandlerButtonNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 107}, "end": {"line": 3, "column": 99, "index": 206}}], "key": "b8q47uCZDbKXHCsiTxPv0e1F2oE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _RNGestureHandlerButtonNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../specs/RNGestureHandlerButtonNativeComponent\"));\n  var _default = exports.default = _RNGestureHandlerButtonNativeComponent.default;\n});", "lineCount": 9, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_RNGestureHandlerButtonNativeComponent"], [7, 44, 3, 0], [7, 47, 3, 0, "_interopRequireDefault"], [7, 69, 3, 0], [7, 70, 3, 0, "require"], [7, 77, 3, 0], [7, 78, 3, 0, "_dependencyMap"], [7, 92, 3, 0], [8, 2, 3, 99], [8, 6, 3, 99, "_default"], [8, 14, 3, 99], [8, 17, 3, 99, "exports"], [8, 24, 3, 99], [8, 25, 3, 99, "default"], [8, 32, 3, 99], [8, 35, 5, 15, "RNGestureHandlerButtonNativeComponent"], [8, 81, 5, 52], [9, 0, 5, 52], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}