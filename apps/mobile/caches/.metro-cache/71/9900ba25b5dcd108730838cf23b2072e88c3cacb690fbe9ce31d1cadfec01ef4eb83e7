{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Video = exports.default = (0, _createLucideIcon.default)(\"Video\", [[\"path\", {\n    d: \"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5\",\n    key: \"ftymec\"\n  }], [\"rect\", {\n    x: \"2\",\n    y: \"6\",\n    width: \"14\",\n    height: \"12\",\n    rx: \"2\",\n    key: \"158x01\"\n  }]]);\n});", "lineCount": 26, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Video"], [15, 13, 10, 11], [15, 16, 10, 11, "exports"], [15, 23, 10, 11], [15, 24, 10, 11, "default"], [15, 31, 10, 11], [15, 34, 10, 14], [15, 38, 10, 14, "createLucideIcon"], [15, 63, 10, 30], [15, 65, 10, 31], [15, 72, 10, 38], [15, 74, 10, 40], [15, 75, 11, 2], [15, 76, 12, 4], [15, 82, 12, 10], [15, 84, 13, 4], [16, 4, 14, 6, "d"], [16, 5, 14, 7], [16, 7, 14, 9], [16, 82, 14, 84], [17, 4, 15, 6, "key"], [17, 7, 15, 9], [17, 9, 15, 11], [18, 2, 16, 4], [18, 3, 16, 5], [18, 4, 17, 3], [18, 6, 18, 2], [18, 7, 18, 3], [18, 13, 18, 9], [18, 15, 18, 11], [19, 4, 18, 13, "x"], [19, 5, 18, 14], [19, 7, 18, 16], [19, 10, 18, 19], [20, 4, 18, 21, "y"], [20, 5, 18, 22], [20, 7, 18, 24], [20, 10, 18, 27], [21, 4, 18, 29, "width"], [21, 9, 18, 34], [21, 11, 18, 36], [21, 15, 18, 40], [22, 4, 18, 42, "height"], [22, 10, 18, 48], [22, 12, 18, 50], [22, 16, 18, 54], [23, 4, 18, 56, "rx"], [23, 6, 18, 58], [23, 8, 18, 60], [23, 11, 18, 63], [24, 4, 18, 65, "key"], [24, 7, 18, 68], [24, 9, 18, 70], [25, 2, 18, 79], [25, 3, 18, 80], [25, 4, 18, 81], [25, 5, 19, 1], [25, 6, 19, 2], [26, 0, 19, 3], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}