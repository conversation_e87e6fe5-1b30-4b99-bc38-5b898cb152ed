{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./EnsureSingleNavigator.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 67, "index": 114}}], "key": "Eeoj43oWyPbMgkhKsD7HCEmXypI=", "exportNames": ["*"]}}, {"name": "./NavigationFocusedRouteStateContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 115}, "end": {"line": 5, "column": 93, "index": 208}}], "key": "LvPfipyJ1qh0tCX4DRfxx1+v9xA=", "exportNames": ["*"]}}, {"name": "./NavigationStateContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 209}, "end": {"line": 6, "column": 69, "index": 278}}], "key": "vPXNy6i2DuFIp7nHtHgSOvNmS+U=", "exportNames": ["*"]}}, {"name": "./StaticContainer.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 279}, "end": {"line": 7, "column": 55, "index": 334}}], "key": "1lwu4xC1c9jAOsqz4Vr70sYD9fs=", "exportNames": ["*"]}}, {"name": "./useOptionsGetters.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 335}, "end": {"line": 8, "column": 59, "index": 394}}], "key": "ZNBMaad7yyX2HZIWNdpUkNUSHFc=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 395}, "end": {"line": 9, "column": 48, "index": 443}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SceneView = SceneView;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _EnsureSingleNavigator = require(_dependencyMap[1], \"./EnsureSingleNavigator.js\");\n  var _NavigationFocusedRouteStateContext = require(_dependencyMap[2], \"./NavigationFocusedRouteStateContext.js\");\n  var _NavigationStateContext = require(_dependencyMap[3], \"./NavigationStateContext.js\");\n  var _StaticContainer = require(_dependencyMap[4], \"./StaticContainer.js\");\n  var _useOptionsGetters = require(_dependencyMap[5], \"./useOptionsGetters.js\");\n  var _jsxRuntime = require(_dependencyMap[6], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Component which takes care of rendering the screen for a route.\n   * It provides all required contexts and applies optimizations when applicable.\n   */\n  function SceneView({\n    screen,\n    route,\n    navigation,\n    routeState,\n    getState,\n    setState,\n    options,\n    clearOptions\n  }) {\n    const navigatorKeyRef = React.useRef(undefined);\n    const getKey = React.useCallback(() => navigatorKeyRef.current, []);\n    const {\n      addOptionsGetter\n    } = (0, _useOptionsGetters.useOptionsGetters)({\n      key: route.key,\n      options,\n      navigation\n    });\n    const setKey = React.useCallback(key => {\n      navigatorKeyRef.current = key;\n    }, []);\n    const getCurrentState = React.useCallback(() => {\n      const state = getState();\n      const currentRoute = state.routes.find(r => r.key === route.key);\n      return currentRoute ? currentRoute.state : undefined;\n    }, [getState, route.key]);\n    const setCurrentState = React.useCallback(child => {\n      const state = getState();\n      setState({\n        ...state,\n        routes: state.routes.map(r => {\n          if (r.key !== route.key) {\n            return r;\n          }\n          const nextRoute = {\n            ...r,\n            state: child\n          };\n\n          // Before updating the state, cleanup any nested screen and state\n          // This will avoid the navigator trying to handle them again\n          if (nextRoute.params && ('state' in nextRoute.params && typeof nextRoute.params.state === 'object' && nextRoute.params.state !== null || 'screen' in nextRoute.params && typeof nextRoute.params.screen === 'string')) {\n            // @ts-expect-error: we don't have correct type for params\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const {\n              state,\n              screen,\n              params,\n              initial,\n              ...rest\n            } = nextRoute.params;\n            if (Object.keys(rest).length) {\n              nextRoute.params = rest;\n            } else {\n              delete nextRoute.params;\n            }\n          }\n          return nextRoute;\n        })\n      });\n    }, [getState, route.key, setState]);\n    const isInitialRef = React.useRef(true);\n    React.useEffect(() => {\n      isInitialRef.current = false;\n    });\n\n    // Clear options set by this screen when it is unmounted\n    React.useEffect(() => {\n      return clearOptions;\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    const getIsInitial = React.useCallback(() => isInitialRef.current, []);\n    const parentFocusedRouteState = React.useContext(_NavigationFocusedRouteStateContext.NavigationFocusedRouteStateContext);\n    const focusedRouteState = React.useMemo(() => {\n      const state = {\n        routes: [{\n          key: route.key,\n          name: route.name,\n          params: route.params,\n          path: route.path\n        }]\n      };\n\n      // Add our state to the innermost route of the parent state\n      const addState = parent => {\n        const parentRoute = parent?.routes[0];\n        if (parentRoute) {\n          return {\n            routes: [{\n              ...parentRoute,\n              state: addState(parentRoute.state)\n            }]\n          };\n        }\n        return state;\n      };\n      return addState(parentFocusedRouteState);\n    }, [parentFocusedRouteState, route.key, route.name, route.params, route.path]);\n    const context = React.useMemo(() => ({\n      state: routeState,\n      getState: getCurrentState,\n      setState: setCurrentState,\n      getKey,\n      setKey,\n      getIsInitial,\n      addOptionsGetter\n    }), [routeState, getCurrentState, setCurrentState, getKey, setKey, getIsInitial, addOptionsGetter]);\n    const ScreenComponent = screen.getComponent ? screen.getComponent() : screen.component;\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationStateContext.NavigationStateContext.Provider, {\n      value: context,\n      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationFocusedRouteStateContext.NavigationFocusedRouteStateContext.Provider, {\n        value: focusedRouteState,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_EnsureSingleNavigator.EnsureSingleNavigator, {\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_StaticContainer.StaticContainer, {\n            name: screen.name,\n            render: ScreenComponent || screen.children,\n            navigation: navigation,\n            route: route,\n            children: ScreenComponent !== undefined ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScreenComponent, {\n              navigation: navigation,\n              route: route\n            }) : screen.children !== undefined ? screen.children({\n              navigation,\n              route\n            }) : null\n          })\n        })\n      })\n    });\n  }\n});", "lineCount": 151, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "SceneView"], [7, 19, 1, 13], [7, 22, 1, 13, "SceneView"], [7, 31, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_EnsureSingleNavigator"], [9, 28, 4, 0], [9, 31, 4, 0, "require"], [9, 38, 4, 0], [9, 39, 4, 0, "_dependencyMap"], [9, 53, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationFocusedRouteStateContext"], [10, 41, 5, 0], [10, 44, 5, 0, "require"], [10, 51, 5, 0], [10, 52, 5, 0, "_dependencyMap"], [10, 66, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_NavigationStateContext"], [11, 29, 6, 0], [11, 32, 6, 0, "require"], [11, 39, 6, 0], [11, 40, 6, 0, "_dependencyMap"], [11, 54, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_StaticContainer"], [12, 22, 7, 0], [12, 25, 7, 0, "require"], [12, 32, 7, 0], [12, 33, 7, 0, "_dependencyMap"], [12, 47, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_useOptionsGetters"], [13, 24, 8, 0], [13, 27, 8, 0, "require"], [13, 34, 8, 0], [13, 35, 8, 0, "_dependencyMap"], [13, 49, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_jsxRuntime"], [14, 17, 9, 0], [14, 20, 9, 0, "require"], [14, 27, 9, 0], [14, 28, 9, 0, "_dependencyMap"], [14, 42, 9, 0], [15, 2, 9, 48], [15, 11, 9, 48, "_interopRequireWildcard"], [15, 35, 9, 48, "e"], [15, 36, 9, 48], [15, 38, 9, 48, "t"], [15, 39, 9, 48], [15, 68, 9, 48, "WeakMap"], [15, 75, 9, 48], [15, 81, 9, 48, "r"], [15, 82, 9, 48], [15, 89, 9, 48, "WeakMap"], [15, 96, 9, 48], [15, 100, 9, 48, "n"], [15, 101, 9, 48], [15, 108, 9, 48, "WeakMap"], [15, 115, 9, 48], [15, 127, 9, 48, "_interopRequireWildcard"], [15, 150, 9, 48], [15, 162, 9, 48, "_interopRequireWildcard"], [15, 163, 9, 48, "e"], [15, 164, 9, 48], [15, 166, 9, 48, "t"], [15, 167, 9, 48], [15, 176, 9, 48, "t"], [15, 177, 9, 48], [15, 181, 9, 48, "e"], [15, 182, 9, 48], [15, 186, 9, 48, "e"], [15, 187, 9, 48], [15, 188, 9, 48, "__esModule"], [15, 198, 9, 48], [15, 207, 9, 48, "e"], [15, 208, 9, 48], [15, 214, 9, 48, "o"], [15, 215, 9, 48], [15, 217, 9, 48, "i"], [15, 218, 9, 48], [15, 220, 9, 48, "f"], [15, 221, 9, 48], [15, 226, 9, 48, "__proto__"], [15, 235, 9, 48], [15, 243, 9, 48, "default"], [15, 250, 9, 48], [15, 252, 9, 48, "e"], [15, 253, 9, 48], [15, 270, 9, 48, "e"], [15, 271, 9, 48], [15, 294, 9, 48, "e"], [15, 295, 9, 48], [15, 320, 9, 48, "e"], [15, 321, 9, 48], [15, 330, 9, 48, "f"], [15, 331, 9, 48], [15, 337, 9, 48, "o"], [15, 338, 9, 48], [15, 341, 9, 48, "t"], [15, 342, 9, 48], [15, 345, 9, 48, "n"], [15, 346, 9, 48], [15, 349, 9, 48, "r"], [15, 350, 9, 48], [15, 358, 9, 48, "o"], [15, 359, 9, 48], [15, 360, 9, 48, "has"], [15, 363, 9, 48], [15, 364, 9, 48, "e"], [15, 365, 9, 48], [15, 375, 9, 48, "o"], [15, 376, 9, 48], [15, 377, 9, 48, "get"], [15, 380, 9, 48], [15, 381, 9, 48, "e"], [15, 382, 9, 48], [15, 385, 9, 48, "o"], [15, 386, 9, 48], [15, 387, 9, 48, "set"], [15, 390, 9, 48], [15, 391, 9, 48, "e"], [15, 392, 9, 48], [15, 394, 9, 48, "f"], [15, 395, 9, 48], [15, 411, 9, 48, "t"], [15, 412, 9, 48], [15, 416, 9, 48, "e"], [15, 417, 9, 48], [15, 433, 9, 48, "t"], [15, 434, 9, 48], [15, 441, 9, 48, "hasOwnProperty"], [15, 455, 9, 48], [15, 456, 9, 48, "call"], [15, 460, 9, 48], [15, 461, 9, 48, "e"], [15, 462, 9, 48], [15, 464, 9, 48, "t"], [15, 465, 9, 48], [15, 472, 9, 48, "i"], [15, 473, 9, 48], [15, 477, 9, 48, "o"], [15, 478, 9, 48], [15, 481, 9, 48, "Object"], [15, 487, 9, 48], [15, 488, 9, 48, "defineProperty"], [15, 502, 9, 48], [15, 507, 9, 48, "Object"], [15, 513, 9, 48], [15, 514, 9, 48, "getOwnPropertyDescriptor"], [15, 538, 9, 48], [15, 539, 9, 48, "e"], [15, 540, 9, 48], [15, 542, 9, 48, "t"], [15, 543, 9, 48], [15, 550, 9, 48, "i"], [15, 551, 9, 48], [15, 552, 9, 48, "get"], [15, 555, 9, 48], [15, 559, 9, 48, "i"], [15, 560, 9, 48], [15, 561, 9, 48, "set"], [15, 564, 9, 48], [15, 568, 9, 48, "o"], [15, 569, 9, 48], [15, 570, 9, 48, "f"], [15, 571, 9, 48], [15, 573, 9, 48, "t"], [15, 574, 9, 48], [15, 576, 9, 48, "i"], [15, 577, 9, 48], [15, 581, 9, 48, "f"], [15, 582, 9, 48], [15, 583, 9, 48, "t"], [15, 584, 9, 48], [15, 588, 9, 48, "e"], [15, 589, 9, 48], [15, 590, 9, 48, "t"], [15, 591, 9, 48], [15, 602, 9, 48, "f"], [15, 603, 9, 48], [15, 608, 9, 48, "e"], [15, 609, 9, 48], [15, 611, 9, 48, "t"], [15, 612, 9, 48], [16, 2, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 2, 14, 7], [20, 11, 14, 16, "SceneView"], [20, 20, 14, 25, "SceneView"], [20, 21, 14, 26], [21, 4, 15, 2, "screen"], [21, 10, 15, 8], [22, 4, 16, 2, "route"], [22, 9, 16, 7], [23, 4, 17, 2, "navigation"], [23, 14, 17, 12], [24, 4, 18, 2, "routeState"], [24, 14, 18, 12], [25, 4, 19, 2, "getState"], [25, 12, 19, 10], [26, 4, 20, 2, "setState"], [26, 12, 20, 10], [27, 4, 21, 2, "options"], [27, 11, 21, 9], [28, 4, 22, 2, "clearOptions"], [29, 2, 23, 0], [29, 3, 23, 1], [29, 5, 23, 3], [30, 4, 24, 2], [30, 10, 24, 8, "navigator<PERSON><PERSON><PERSON><PERSON>"], [30, 25, 24, 23], [30, 28, 24, 26, "React"], [30, 33, 24, 31], [30, 34, 24, 32, "useRef"], [30, 40, 24, 38], [30, 41, 24, 39, "undefined"], [30, 50, 24, 48], [30, 51, 24, 49], [31, 4, 25, 2], [31, 10, 25, 8, "<PERSON><PERSON><PERSON>"], [31, 16, 25, 14], [31, 19, 25, 17, "React"], [31, 24, 25, 22], [31, 25, 25, 23, "useCallback"], [31, 36, 25, 34], [31, 37, 25, 35], [31, 43, 25, 41, "navigator<PERSON><PERSON><PERSON><PERSON>"], [31, 58, 25, 56], [31, 59, 25, 57, "current"], [31, 66, 25, 64], [31, 68, 25, 66], [31, 70, 25, 68], [31, 71, 25, 69], [32, 4, 26, 2], [32, 10, 26, 8], [33, 6, 27, 4, "addOptionsGetter"], [34, 4, 28, 2], [34, 5, 28, 3], [34, 8, 28, 6], [34, 12, 28, 6, "useOptionsGetters"], [34, 48, 28, 23], [34, 50, 28, 24], [35, 6, 29, 4, "key"], [35, 9, 29, 7], [35, 11, 29, 9, "route"], [35, 16, 29, 14], [35, 17, 29, 15, "key"], [35, 20, 29, 18], [36, 6, 30, 4, "options"], [36, 13, 30, 11], [37, 6, 31, 4, "navigation"], [38, 4, 32, 2], [38, 5, 32, 3], [38, 6, 32, 4], [39, 4, 33, 2], [39, 10, 33, 8, "<PERSON><PERSON><PERSON>"], [39, 16, 33, 14], [39, 19, 33, 17, "React"], [39, 24, 33, 22], [39, 25, 33, 23, "useCallback"], [39, 36, 33, 34], [39, 37, 33, 35, "key"], [39, 40, 33, 38], [39, 44, 33, 42], [40, 6, 34, 4, "navigator<PERSON><PERSON><PERSON><PERSON>"], [40, 21, 34, 19], [40, 22, 34, 20, "current"], [40, 29, 34, 27], [40, 32, 34, 30, "key"], [40, 35, 34, 33], [41, 4, 35, 2], [41, 5, 35, 3], [41, 7, 35, 5], [41, 9, 35, 7], [41, 10, 35, 8], [42, 4, 36, 2], [42, 10, 36, 8, "getCurrentState"], [42, 25, 36, 23], [42, 28, 36, 26, "React"], [42, 33, 36, 31], [42, 34, 36, 32, "useCallback"], [42, 45, 36, 43], [42, 46, 36, 44], [42, 52, 36, 50], [43, 6, 37, 4], [43, 12, 37, 10, "state"], [43, 17, 37, 15], [43, 20, 37, 18, "getState"], [43, 28, 37, 26], [43, 29, 37, 27], [43, 30, 37, 28], [44, 6, 38, 4], [44, 12, 38, 10, "currentRoute"], [44, 24, 38, 22], [44, 27, 38, 25, "state"], [44, 32, 38, 30], [44, 33, 38, 31, "routes"], [44, 39, 38, 37], [44, 40, 38, 38, "find"], [44, 44, 38, 42], [44, 45, 38, 43, "r"], [44, 46, 38, 44], [44, 50, 38, 48, "r"], [44, 51, 38, 49], [44, 52, 38, 50, "key"], [44, 55, 38, 53], [44, 60, 38, 58, "route"], [44, 65, 38, 63], [44, 66, 38, 64, "key"], [44, 69, 38, 67], [44, 70, 38, 68], [45, 6, 39, 4], [45, 13, 39, 11, "currentRoute"], [45, 25, 39, 23], [45, 28, 39, 26, "currentRoute"], [45, 40, 39, 38], [45, 41, 39, 39, "state"], [45, 46, 39, 44], [45, 49, 39, 47, "undefined"], [45, 58, 39, 56], [46, 4, 40, 2], [46, 5, 40, 3], [46, 7, 40, 5], [46, 8, 40, 6, "getState"], [46, 16, 40, 14], [46, 18, 40, 16, "route"], [46, 23, 40, 21], [46, 24, 40, 22, "key"], [46, 27, 40, 25], [46, 28, 40, 26], [46, 29, 40, 27], [47, 4, 41, 2], [47, 10, 41, 8, "setCurrentState"], [47, 25, 41, 23], [47, 28, 41, 26, "React"], [47, 33, 41, 31], [47, 34, 41, 32, "useCallback"], [47, 45, 41, 43], [47, 46, 41, 44, "child"], [47, 51, 41, 49], [47, 55, 41, 53], [48, 6, 42, 4], [48, 12, 42, 10, "state"], [48, 17, 42, 15], [48, 20, 42, 18, "getState"], [48, 28, 42, 26], [48, 29, 42, 27], [48, 30, 42, 28], [49, 6, 43, 4, "setState"], [49, 14, 43, 12], [49, 15, 43, 13], [50, 8, 44, 6], [50, 11, 44, 9, "state"], [50, 16, 44, 14], [51, 8, 45, 6, "routes"], [51, 14, 45, 12], [51, 16, 45, 14, "state"], [51, 21, 45, 19], [51, 22, 45, 20, "routes"], [51, 28, 45, 26], [51, 29, 45, 27, "map"], [51, 32, 45, 30], [51, 33, 45, 31, "r"], [51, 34, 45, 32], [51, 38, 45, 36], [52, 10, 46, 8], [52, 14, 46, 12, "r"], [52, 15, 46, 13], [52, 16, 46, 14, "key"], [52, 19, 46, 17], [52, 24, 46, 22, "route"], [52, 29, 46, 27], [52, 30, 46, 28, "key"], [52, 33, 46, 31], [52, 35, 46, 33], [53, 12, 47, 10], [53, 19, 47, 17, "r"], [53, 20, 47, 18], [54, 10, 48, 8], [55, 10, 49, 8], [55, 16, 49, 14, "nextRoute"], [55, 25, 49, 23], [55, 28, 49, 26], [56, 12, 50, 10], [56, 15, 50, 13, "r"], [56, 16, 50, 14], [57, 12, 51, 10, "state"], [57, 17, 51, 15], [57, 19, 51, 17, "child"], [58, 10, 52, 8], [58, 11, 52, 9], [60, 10, 54, 8], [61, 10, 55, 8], [62, 10, 56, 8], [62, 14, 56, 12, "nextRoute"], [62, 23, 56, 21], [62, 24, 56, 22, "params"], [62, 30, 56, 28], [62, 35, 56, 33], [62, 42, 56, 40], [62, 46, 56, 44, "nextRoute"], [62, 55, 56, 53], [62, 56, 56, 54, "params"], [62, 62, 56, 60], [62, 66, 56, 64], [62, 73, 56, 71, "nextRoute"], [62, 82, 56, 80], [62, 83, 56, 81, "params"], [62, 89, 56, 87], [62, 90, 56, 88, "state"], [62, 95, 56, 93], [62, 100, 56, 98], [62, 108, 56, 106], [62, 112, 56, 110, "nextRoute"], [62, 121, 56, 119], [62, 122, 56, 120, "params"], [62, 128, 56, 126], [62, 129, 56, 127, "state"], [62, 134, 56, 132], [62, 139, 56, 137], [62, 143, 56, 141], [62, 147, 56, 145], [62, 155, 56, 153], [62, 159, 56, 157, "nextRoute"], [62, 168, 56, 166], [62, 169, 56, 167, "params"], [62, 175, 56, 173], [62, 179, 56, 177], [62, 186, 56, 184, "nextRoute"], [62, 195, 56, 193], [62, 196, 56, 194, "params"], [62, 202, 56, 200], [62, 203, 56, 201, "screen"], [62, 209, 56, 207], [62, 214, 56, 212], [62, 222, 56, 220], [62, 223, 56, 221], [62, 225, 56, 223], [63, 12, 57, 10], [64, 12, 58, 10], [65, 12, 59, 10], [65, 18, 59, 16], [66, 14, 60, 12, "state"], [66, 19, 60, 17], [67, 14, 61, 12, "screen"], [67, 20, 61, 18], [68, 14, 62, 12, "params"], [68, 20, 62, 18], [69, 14, 63, 12, "initial"], [69, 21, 63, 19], [70, 14, 64, 12], [70, 17, 64, 15, "rest"], [71, 12, 65, 10], [71, 13, 65, 11], [71, 16, 65, 14, "nextRoute"], [71, 25, 65, 23], [71, 26, 65, 24, "params"], [71, 32, 65, 30], [72, 12, 66, 10], [72, 16, 66, 14, "Object"], [72, 22, 66, 20], [72, 23, 66, 21, "keys"], [72, 27, 66, 25], [72, 28, 66, 26, "rest"], [72, 32, 66, 30], [72, 33, 66, 31], [72, 34, 66, 32, "length"], [72, 40, 66, 38], [72, 42, 66, 40], [73, 14, 67, 12, "nextRoute"], [73, 23, 67, 21], [73, 24, 67, 22, "params"], [73, 30, 67, 28], [73, 33, 67, 31, "rest"], [73, 37, 67, 35], [74, 12, 68, 10], [74, 13, 68, 11], [74, 19, 68, 17], [75, 14, 69, 12], [75, 21, 69, 19, "nextRoute"], [75, 30, 69, 28], [75, 31, 69, 29, "params"], [75, 37, 69, 35], [76, 12, 70, 10], [77, 10, 71, 8], [78, 10, 72, 8], [78, 17, 72, 15, "nextRoute"], [78, 26, 72, 24], [79, 8, 73, 6], [79, 9, 73, 7], [80, 6, 74, 4], [80, 7, 74, 5], [80, 8, 74, 6], [81, 4, 75, 2], [81, 5, 75, 3], [81, 7, 75, 5], [81, 8, 75, 6, "getState"], [81, 16, 75, 14], [81, 18, 75, 16, "route"], [81, 23, 75, 21], [81, 24, 75, 22, "key"], [81, 27, 75, 25], [81, 29, 75, 27, "setState"], [81, 37, 75, 35], [81, 38, 75, 36], [81, 39, 75, 37], [82, 4, 76, 2], [82, 10, 76, 8, "isInitialRef"], [82, 22, 76, 20], [82, 25, 76, 23, "React"], [82, 30, 76, 28], [82, 31, 76, 29, "useRef"], [82, 37, 76, 35], [82, 38, 76, 36], [82, 42, 76, 40], [82, 43, 76, 41], [83, 4, 77, 2, "React"], [83, 9, 77, 7], [83, 10, 77, 8, "useEffect"], [83, 19, 77, 17], [83, 20, 77, 18], [83, 26, 77, 24], [84, 6, 78, 4, "isInitialRef"], [84, 18, 78, 16], [84, 19, 78, 17, "current"], [84, 26, 78, 24], [84, 29, 78, 27], [84, 34, 78, 32], [85, 4, 79, 2], [85, 5, 79, 3], [85, 6, 79, 4], [87, 4, 81, 2], [88, 4, 82, 2, "React"], [88, 9, 82, 7], [88, 10, 82, 8, "useEffect"], [88, 19, 82, 17], [88, 20, 82, 18], [88, 26, 82, 24], [89, 6, 83, 4], [89, 13, 83, 11, "clearOptions"], [89, 25, 83, 23], [90, 6, 84, 4], [91, 4, 85, 2], [91, 5, 85, 3], [91, 7, 85, 5], [91, 9, 85, 7], [91, 10, 85, 8], [92, 4, 86, 2], [92, 10, 86, 8, "getIsInitial"], [92, 22, 86, 20], [92, 25, 86, 23, "React"], [92, 30, 86, 28], [92, 31, 86, 29, "useCallback"], [92, 42, 86, 40], [92, 43, 86, 41], [92, 49, 86, 47, "isInitialRef"], [92, 61, 86, 59], [92, 62, 86, 60, "current"], [92, 69, 86, 67], [92, 71, 86, 69], [92, 73, 86, 71], [92, 74, 86, 72], [93, 4, 87, 2], [93, 10, 87, 8, "parentFocusedRouteState"], [93, 33, 87, 31], [93, 36, 87, 34, "React"], [93, 41, 87, 39], [93, 42, 87, 40, "useContext"], [93, 52, 87, 50], [93, 53, 87, 51, "NavigationFocusedRouteStateContext"], [93, 123, 87, 85], [93, 124, 87, 86], [94, 4, 88, 2], [94, 10, 88, 8, "focusedRouteState"], [94, 27, 88, 25], [94, 30, 88, 28, "React"], [94, 35, 88, 33], [94, 36, 88, 34, "useMemo"], [94, 43, 88, 41], [94, 44, 88, 42], [94, 50, 88, 48], [95, 6, 89, 4], [95, 12, 89, 10, "state"], [95, 17, 89, 15], [95, 20, 89, 18], [96, 8, 90, 6, "routes"], [96, 14, 90, 12], [96, 16, 90, 14], [96, 17, 90, 15], [97, 10, 91, 8, "key"], [97, 13, 91, 11], [97, 15, 91, 13, "route"], [97, 20, 91, 18], [97, 21, 91, 19, "key"], [97, 24, 91, 22], [98, 10, 92, 8, "name"], [98, 14, 92, 12], [98, 16, 92, 14, "route"], [98, 21, 92, 19], [98, 22, 92, 20, "name"], [98, 26, 92, 24], [99, 10, 93, 8, "params"], [99, 16, 93, 14], [99, 18, 93, 16, "route"], [99, 23, 93, 21], [99, 24, 93, 22, "params"], [99, 30, 93, 28], [100, 10, 94, 8, "path"], [100, 14, 94, 12], [100, 16, 94, 14, "route"], [100, 21, 94, 19], [100, 22, 94, 20, "path"], [101, 8, 95, 6], [101, 9, 95, 7], [102, 6, 96, 4], [102, 7, 96, 5], [104, 6, 98, 4], [105, 6, 99, 4], [105, 12, 99, 10, "addState"], [105, 20, 99, 18], [105, 23, 99, 21, "parent"], [105, 29, 99, 27], [105, 33, 99, 31], [106, 8, 100, 6], [106, 14, 100, 12, "parentRoute"], [106, 25, 100, 23], [106, 28, 100, 26, "parent"], [106, 34, 100, 32], [106, 36, 100, 34, "routes"], [106, 42, 100, 40], [106, 43, 100, 41], [106, 44, 100, 42], [106, 45, 100, 43], [107, 8, 101, 6], [107, 12, 101, 10, "parentRoute"], [107, 23, 101, 21], [107, 25, 101, 23], [108, 10, 102, 8], [108, 17, 102, 15], [109, 12, 103, 10, "routes"], [109, 18, 103, 16], [109, 20, 103, 18], [109, 21, 103, 19], [110, 14, 104, 12], [110, 17, 104, 15, "parentRoute"], [110, 28, 104, 26], [111, 14, 105, 12, "state"], [111, 19, 105, 17], [111, 21, 105, 19, "addState"], [111, 29, 105, 27], [111, 30, 105, 28, "parentRoute"], [111, 41, 105, 39], [111, 42, 105, 40, "state"], [111, 47, 105, 45], [112, 12, 106, 10], [112, 13, 106, 11], [113, 10, 107, 8], [113, 11, 107, 9], [114, 8, 108, 6], [115, 8, 109, 6], [115, 15, 109, 13, "state"], [115, 20, 109, 18], [116, 6, 110, 4], [116, 7, 110, 5], [117, 6, 111, 4], [117, 13, 111, 11, "addState"], [117, 21, 111, 19], [117, 22, 111, 20, "parentFocusedRouteState"], [117, 45, 111, 43], [117, 46, 111, 44], [118, 4, 112, 2], [118, 5, 112, 3], [118, 7, 112, 5], [118, 8, 112, 6, "parentFocusedRouteState"], [118, 31, 112, 29], [118, 33, 112, 31, "route"], [118, 38, 112, 36], [118, 39, 112, 37, "key"], [118, 42, 112, 40], [118, 44, 112, 42, "route"], [118, 49, 112, 47], [118, 50, 112, 48, "name"], [118, 54, 112, 52], [118, 56, 112, 54, "route"], [118, 61, 112, 59], [118, 62, 112, 60, "params"], [118, 68, 112, 66], [118, 70, 112, 68, "route"], [118, 75, 112, 73], [118, 76, 112, 74, "path"], [118, 80, 112, 78], [118, 81, 112, 79], [118, 82, 112, 80], [119, 4, 113, 2], [119, 10, 113, 8, "context"], [119, 17, 113, 15], [119, 20, 113, 18, "React"], [119, 25, 113, 23], [119, 26, 113, 24, "useMemo"], [119, 33, 113, 31], [119, 34, 113, 32], [119, 41, 113, 39], [120, 6, 114, 4, "state"], [120, 11, 114, 9], [120, 13, 114, 11, "routeState"], [120, 23, 114, 21], [121, 6, 115, 4, "getState"], [121, 14, 115, 12], [121, 16, 115, 14, "getCurrentState"], [121, 31, 115, 29], [122, 6, 116, 4, "setState"], [122, 14, 116, 12], [122, 16, 116, 14, "setCurrentState"], [122, 31, 116, 29], [123, 6, 117, 4, "<PERSON><PERSON><PERSON>"], [123, 12, 117, 10], [124, 6, 118, 4, "<PERSON><PERSON><PERSON>"], [124, 12, 118, 10], [125, 6, 119, 4, "getIsInitial"], [125, 18, 119, 16], [126, 6, 120, 4, "addOptionsGetter"], [127, 4, 121, 2], [127, 5, 121, 3], [127, 6, 121, 4], [127, 8, 121, 6], [127, 9, 121, 7, "routeState"], [127, 19, 121, 17], [127, 21, 121, 19, "getCurrentState"], [127, 36, 121, 34], [127, 38, 121, 36, "setCurrentState"], [127, 53, 121, 51], [127, 55, 121, 53, "<PERSON><PERSON><PERSON>"], [127, 61, 121, 59], [127, 63, 121, 61, "<PERSON><PERSON><PERSON>"], [127, 69, 121, 67], [127, 71, 121, 69, "getIsInitial"], [127, 83, 121, 81], [127, 85, 121, 83, "addOptionsGetter"], [127, 101, 121, 99], [127, 102, 121, 100], [127, 103, 121, 101], [128, 4, 122, 2], [128, 10, 122, 8, "ScreenComponent"], [128, 25, 122, 23], [128, 28, 122, 26, "screen"], [128, 34, 122, 32], [128, 35, 122, 33, "getComponent"], [128, 47, 122, 45], [128, 50, 122, 48, "screen"], [128, 56, 122, 54], [128, 57, 122, 55, "getComponent"], [128, 69, 122, 67], [128, 70, 122, 68], [128, 71, 122, 69], [128, 74, 122, 72, "screen"], [128, 80, 122, 78], [128, 81, 122, 79, "component"], [128, 90, 122, 88], [129, 4, 123, 2], [129, 11, 123, 9], [129, 24, 123, 22], [129, 28, 123, 22, "_jsx"], [129, 43, 123, 26], [129, 45, 123, 27, "NavigationStateContext"], [129, 91, 123, 49], [129, 92, 123, 50, "Provider"], [129, 100, 123, 58], [129, 102, 123, 60], [130, 6, 124, 4, "value"], [130, 11, 124, 9], [130, 13, 124, 11, "context"], [130, 20, 124, 18], [131, 6, 125, 4, "children"], [131, 14, 125, 12], [131, 16, 125, 14], [131, 29, 125, 27], [131, 33, 125, 27, "_jsx"], [131, 48, 125, 31], [131, 50, 125, 32, "NavigationFocusedRouteStateContext"], [131, 120, 125, 66], [131, 121, 125, 67, "Provider"], [131, 129, 125, 75], [131, 131, 125, 77], [132, 8, 126, 6, "value"], [132, 13, 126, 11], [132, 15, 126, 13, "focusedRouteState"], [132, 32, 126, 30], [133, 8, 127, 6, "children"], [133, 16, 127, 14], [133, 18, 127, 16], [133, 31, 127, 29], [133, 35, 127, 29, "_jsx"], [133, 50, 127, 33], [133, 52, 127, 34, "EnsureSingleNavigator"], [133, 96, 127, 55], [133, 98, 127, 57], [134, 10, 128, 8, "children"], [134, 18, 128, 16], [134, 20, 128, 18], [134, 33, 128, 31], [134, 37, 128, 31, "_jsx"], [134, 52, 128, 35], [134, 54, 128, 36, "StaticContainer"], [134, 86, 128, 51], [134, 88, 128, 53], [135, 12, 129, 10, "name"], [135, 16, 129, 14], [135, 18, 129, 16, "screen"], [135, 24, 129, 22], [135, 25, 129, 23, "name"], [135, 29, 129, 27], [136, 12, 130, 10, "render"], [136, 18, 130, 16], [136, 20, 130, 18, "ScreenComponent"], [136, 35, 130, 33], [136, 39, 130, 37, "screen"], [136, 45, 130, 43], [136, 46, 130, 44, "children"], [136, 54, 130, 52], [137, 12, 131, 10, "navigation"], [137, 22, 131, 20], [137, 24, 131, 22, "navigation"], [137, 34, 131, 32], [138, 12, 132, 10, "route"], [138, 17, 132, 15], [138, 19, 132, 17, "route"], [138, 24, 132, 22], [139, 12, 133, 10, "children"], [139, 20, 133, 18], [139, 22, 133, 20, "ScreenComponent"], [139, 37, 133, 35], [139, 42, 133, 40, "undefined"], [139, 51, 133, 49], [139, 54, 133, 52], [139, 67, 133, 65], [139, 71, 133, 65, "_jsx"], [139, 86, 133, 69], [139, 88, 133, 70, "ScreenComponent"], [139, 103, 133, 85], [139, 105, 133, 87], [140, 14, 134, 12, "navigation"], [140, 24, 134, 22], [140, 26, 134, 24, "navigation"], [140, 36, 134, 34], [141, 14, 135, 12, "route"], [141, 19, 135, 17], [141, 21, 135, 19, "route"], [142, 12, 136, 10], [142, 13, 136, 11], [142, 14, 136, 12], [142, 17, 136, 15, "screen"], [142, 23, 136, 21], [142, 24, 136, 22, "children"], [142, 32, 136, 30], [142, 37, 136, 35, "undefined"], [142, 46, 136, 44], [142, 49, 136, 47, "screen"], [142, 55, 136, 53], [142, 56, 136, 54, "children"], [142, 64, 136, 62], [142, 65, 136, 63], [143, 14, 137, 12, "navigation"], [143, 24, 137, 22], [144, 14, 138, 12, "route"], [145, 12, 139, 10], [145, 13, 139, 11], [145, 14, 139, 12], [145, 17, 139, 15], [146, 10, 140, 8], [146, 11, 140, 9], [147, 8, 141, 6], [147, 9, 141, 7], [148, 6, 142, 4], [148, 7, 142, 5], [149, 4, 143, 2], [149, 5, 143, 3], [149, 6, 143, 4], [150, 2, 144, 0], [151, 0, 144, 1], [151, 3]], "functionMap": {"names": ["<global>", "SceneView", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getCurrentState", "state.routes.find$argument_0", "setCurrentState", "state.routes.map$argument_0", "React.useEffect$argument_0", "getIsInitial", "React.useMemo$argument_0", "addState"], "mappings": "AAA;OCa;mCCW,6BD;mCEQ;GFE;4CGC;2CCE,wBD;GHE;4CKC;+BCI;OD4B;GLE;kBOE;GPE;kBOG;GPG;yCQC,0BR;0CSE;qBCW;KDW;GTE;gCSC;ITQ;CDuB"}}, "type": "js/module"}]}