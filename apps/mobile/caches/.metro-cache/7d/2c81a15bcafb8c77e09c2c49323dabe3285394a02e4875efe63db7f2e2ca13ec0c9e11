{"dependencies": [{"name": "../../../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 62, "index": 77}}], "key": "E26DJoo8mWREzzvpA2kBJeN9Itc=", "exportNames": ["*"]}}, {"name": "../Easing.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 78}, "end": {"line": 4, "column": 51, "index": 129}}], "key": "Xd/tvg1AIicOGIP0CVWBGqf8AmU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CurvedTransition = CurvedTransition;\n  exports.prepareCurvedTransition = prepareCurvedTransition;\n  var _commonTypes = require(_dependencyMap[0], \"../../../commonTypes.js\");\n  var _EasingWeb = require(_dependencyMap[1], \"../Easing.web.js\");\n  function resetStyle(component) {\n    component.style.animationName = ''; // This line prevents unwanted entering animation\n    component.style.position = 'absolute';\n    component.style.top = '0px';\n    component.style.left = '0px';\n    component.style.margin = '0px';\n    component.style.width = '100%';\n    component.style.height = '100%';\n  }\n  function showChildren(parent, childrenDisplayProperty, shouldShow) {\n    for (let i = 0; i < parent.children.length; ++i) {\n      const child = parent.children[i];\n      if (shouldShow) {\n        child.style.display = childrenDisplayProperty.get(child);\n      } else {\n        childrenDisplayProperty.set(child, child.style.display);\n        child.style.display = 'none';\n      }\n    }\n  }\n  function prepareParent(element, dummy, animationConfig, transitionData) {\n    // Adjust configs for `CurvedTransition` and create config object for dummy\n    animationConfig.easing = (0, _EasingWeb.getEasingByName)(transitionData.easingX);\n    const childrenDisplayProperty = new Map();\n    showChildren(element, childrenDisplayProperty, false);\n    const originalBackgroundColor = element.style.backgroundColor;\n    element.style.backgroundColor = 'transparent';\n    const onFinalize = () => {\n      if (element.contains(dummy)) {\n        element.removeChild(dummy);\n      }\n      showChildren(element, childrenDisplayProperty, true);\n      element.style.backgroundColor = originalBackgroundColor;\n    };\n    const animationCancelCallback = () => {\n      onFinalize();\n      element.removeEventListener('animationcancel', animationCancelCallback);\n    };\n    const animationEndCallback = () => {\n      onFinalize();\n      element.removeEventListener('animationend', animationEndCallback);\n    };\n    element.addEventListener('animationend', animationEndCallback);\n    element.addEventListener('animationcancel', animationCancelCallback);\n    element.appendChild(dummy);\n  }\n  function prepareDummy(element, animationConfig, transitionData, dummyTransitionKeyframeName) {\n    const dummyAnimationConfig = {\n      animationName: dummyTransitionKeyframeName,\n      animationType: _commonTypes.LayoutAnimationType.LAYOUT,\n      duration: animationConfig.duration,\n      delay: animationConfig.delay,\n      easing: (0, _EasingWeb.getEasingByName)(transitionData.easingY),\n      callback: null,\n      reversed: false\n    };\n    const dummy = element.cloneNode(true);\n    resetStyle(dummy);\n    return {\n      dummy,\n      dummyAnimationConfig\n    };\n  }\n  function prepareCurvedTransition(element, animationConfig, transitionData, dummyTransitionKeyframeName) {\n    const {\n      dummy,\n      dummyAnimationConfig\n    } = prepareDummy(element, animationConfig, transitionData, dummyTransitionKeyframeName);\n    prepareParent(element, dummy, animationConfig, transitionData);\n    return {\n      dummy,\n      dummyAnimationConfig\n    };\n  }\n  function CurvedTransition(keyframeXName, keyframeYName, transitionData) {\n    const keyframeXObj = {\n      name: keyframeXName,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${transitionData.translateX}px`,\n            scale: `${transitionData.scaleX},${transitionData.scaleY}`\n          }]\n        }\n      },\n      duration: 300\n    };\n    const keyframeYObj = {\n      name: keyframeYName,\n      style: {\n        0: {\n          transform: [{\n            translateY: `${transitionData.translateY}px`,\n            scale: `${transitionData.scaleX},${transitionData.scaleY}`\n          }]\n        }\n      },\n      duration: 300\n    };\n    return {\n      firstKeyframeObj: keyframeXObj,\n      secondKeyframeObj: keyframeYObj\n    };\n  }\n});", "lineCount": 115, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "CurvedTransition"], [7, 26, 1, 13], [7, 29, 1, 13, "CurvedTransition"], [7, 45, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "prepareCurvedTransition"], [8, 33, 1, 13], [8, 36, 1, 13, "prepareCurvedTransition"], [8, 59, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_commonTypes"], [9, 18, 3, 0], [9, 21, 3, 0, "require"], [9, 28, 3, 0], [9, 29, 3, 0, "_dependencyMap"], [9, 43, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_EasingWeb"], [10, 16, 4, 0], [10, 19, 4, 0, "require"], [10, 26, 4, 0], [10, 27, 4, 0, "_dependencyMap"], [10, 41, 4, 0], [11, 2, 5, 0], [11, 11, 5, 9, "resetStyle"], [11, 21, 5, 19, "resetStyle"], [11, 22, 5, 20, "component"], [11, 31, 5, 29], [11, 33, 5, 31], [12, 4, 6, 2, "component"], [12, 13, 6, 11], [12, 14, 6, 12, "style"], [12, 19, 6, 17], [12, 20, 6, 18, "animationName"], [12, 33, 6, 31], [12, 36, 6, 34], [12, 38, 6, 36], [12, 39, 6, 37], [12, 40, 6, 38], [13, 4, 7, 2, "component"], [13, 13, 7, 11], [13, 14, 7, 12, "style"], [13, 19, 7, 17], [13, 20, 7, 18, "position"], [13, 28, 7, 26], [13, 31, 7, 29], [13, 41, 7, 39], [14, 4, 8, 2, "component"], [14, 13, 8, 11], [14, 14, 8, 12, "style"], [14, 19, 8, 17], [14, 20, 8, 18, "top"], [14, 23, 8, 21], [14, 26, 8, 24], [14, 31, 8, 29], [15, 4, 9, 2, "component"], [15, 13, 9, 11], [15, 14, 9, 12, "style"], [15, 19, 9, 17], [15, 20, 9, 18, "left"], [15, 24, 9, 22], [15, 27, 9, 25], [15, 32, 9, 30], [16, 4, 10, 2, "component"], [16, 13, 10, 11], [16, 14, 10, 12, "style"], [16, 19, 10, 17], [16, 20, 10, 18, "margin"], [16, 26, 10, 24], [16, 29, 10, 27], [16, 34, 10, 32], [17, 4, 11, 2, "component"], [17, 13, 11, 11], [17, 14, 11, 12, "style"], [17, 19, 11, 17], [17, 20, 11, 18, "width"], [17, 25, 11, 23], [17, 28, 11, 26], [17, 34, 11, 32], [18, 4, 12, 2, "component"], [18, 13, 12, 11], [18, 14, 12, 12, "style"], [18, 19, 12, 17], [18, 20, 12, 18, "height"], [18, 26, 12, 24], [18, 29, 12, 27], [18, 35, 12, 33], [19, 2, 13, 0], [20, 2, 14, 0], [20, 11, 14, 9, "showChildren"], [20, 23, 14, 21, "showChildren"], [20, 24, 14, 22, "parent"], [20, 30, 14, 28], [20, 32, 14, 30, "childrenDisplayProperty"], [20, 55, 14, 53], [20, 57, 14, 55, "shouldShow"], [20, 67, 14, 65], [20, 69, 14, 67], [21, 4, 15, 2], [21, 9, 15, 7], [21, 13, 15, 11, "i"], [21, 14, 15, 12], [21, 17, 15, 15], [21, 18, 15, 16], [21, 20, 15, 18, "i"], [21, 21, 15, 19], [21, 24, 15, 22, "parent"], [21, 30, 15, 28], [21, 31, 15, 29, "children"], [21, 39, 15, 37], [21, 40, 15, 38, "length"], [21, 46, 15, 44], [21, 48, 15, 46], [21, 50, 15, 48, "i"], [21, 51, 15, 49], [21, 53, 15, 51], [22, 6, 16, 4], [22, 12, 16, 10, "child"], [22, 17, 16, 15], [22, 20, 16, 18, "parent"], [22, 26, 16, 24], [22, 27, 16, 25, "children"], [22, 35, 16, 33], [22, 36, 16, 34, "i"], [22, 37, 16, 35], [22, 38, 16, 36], [23, 6, 17, 4], [23, 10, 17, 8, "shouldShow"], [23, 20, 17, 18], [23, 22, 17, 20], [24, 8, 18, 6, "child"], [24, 13, 18, 11], [24, 14, 18, 12, "style"], [24, 19, 18, 17], [24, 20, 18, 18, "display"], [24, 27, 18, 25], [24, 30, 18, 28, "childrenDisplayProperty"], [24, 53, 18, 51], [24, 54, 18, 52, "get"], [24, 57, 18, 55], [24, 58, 18, 56, "child"], [24, 63, 18, 61], [24, 64, 18, 62], [25, 6, 19, 4], [25, 7, 19, 5], [25, 13, 19, 11], [26, 8, 20, 6, "childrenDisplayProperty"], [26, 31, 20, 29], [26, 32, 20, 30, "set"], [26, 35, 20, 33], [26, 36, 20, 34, "child"], [26, 41, 20, 39], [26, 43, 20, 41, "child"], [26, 48, 20, 46], [26, 49, 20, 47, "style"], [26, 54, 20, 52], [26, 55, 20, 53, "display"], [26, 62, 20, 60], [26, 63, 20, 61], [27, 8, 21, 6, "child"], [27, 13, 21, 11], [27, 14, 21, 12, "style"], [27, 19, 21, 17], [27, 20, 21, 18, "display"], [27, 27, 21, 25], [27, 30, 21, 28], [27, 36, 21, 34], [28, 6, 22, 4], [29, 4, 23, 2], [30, 2, 24, 0], [31, 2, 25, 0], [31, 11, 25, 9, "prepareParent"], [31, 24, 25, 22, "prepareParent"], [31, 25, 25, 23, "element"], [31, 32, 25, 30], [31, 34, 25, 32, "dummy"], [31, 39, 25, 37], [31, 41, 25, 39, "animationConfig"], [31, 56, 25, 54], [31, 58, 25, 56, "transitionData"], [31, 72, 25, 70], [31, 74, 25, 72], [32, 4, 26, 2], [33, 4, 27, 2, "animationConfig"], [33, 19, 27, 17], [33, 20, 27, 18, "easing"], [33, 26, 27, 24], [33, 29, 27, 27], [33, 33, 27, 27, "getEasingByName"], [33, 59, 27, 42], [33, 61, 27, 43, "transitionData"], [33, 75, 27, 57], [33, 76, 27, 58, "easingX"], [33, 83, 27, 65], [33, 84, 27, 66], [34, 4, 28, 2], [34, 10, 28, 8, "childrenDisplayProperty"], [34, 33, 28, 31], [34, 36, 28, 34], [34, 40, 28, 38, "Map"], [34, 43, 28, 41], [34, 44, 28, 42], [34, 45, 28, 43], [35, 4, 29, 2, "showChildren"], [35, 16, 29, 14], [35, 17, 29, 15, "element"], [35, 24, 29, 22], [35, 26, 29, 24, "childrenDisplayProperty"], [35, 49, 29, 47], [35, 51, 29, 49], [35, 56, 29, 54], [35, 57, 29, 55], [36, 4, 30, 2], [36, 10, 30, 8, "originalBackgroundColor"], [36, 33, 30, 31], [36, 36, 30, 34, "element"], [36, 43, 30, 41], [36, 44, 30, 42, "style"], [36, 49, 30, 47], [36, 50, 30, 48, "backgroundColor"], [36, 65, 30, 63], [37, 4, 31, 2, "element"], [37, 11, 31, 9], [37, 12, 31, 10, "style"], [37, 17, 31, 15], [37, 18, 31, 16, "backgroundColor"], [37, 33, 31, 31], [37, 36, 31, 34], [37, 49, 31, 47], [38, 4, 32, 2], [38, 10, 32, 8, "onFinalize"], [38, 20, 32, 18], [38, 23, 32, 21, "onFinalize"], [38, 24, 32, 21], [38, 29, 32, 27], [39, 6, 33, 4], [39, 10, 33, 8, "element"], [39, 17, 33, 15], [39, 18, 33, 16, "contains"], [39, 26, 33, 24], [39, 27, 33, 25, "dummy"], [39, 32, 33, 30], [39, 33, 33, 31], [39, 35, 33, 33], [40, 8, 34, 6, "element"], [40, 15, 34, 13], [40, 16, 34, 14, "<PERSON><PERSON><PERSON><PERSON>"], [40, 27, 34, 25], [40, 28, 34, 26, "dummy"], [40, 33, 34, 31], [40, 34, 34, 32], [41, 6, 35, 4], [42, 6, 36, 4, "showChildren"], [42, 18, 36, 16], [42, 19, 36, 17, "element"], [42, 26, 36, 24], [42, 28, 36, 26, "childrenDisplayProperty"], [42, 51, 36, 49], [42, 53, 36, 51], [42, 57, 36, 55], [42, 58, 36, 56], [43, 6, 37, 4, "element"], [43, 13, 37, 11], [43, 14, 37, 12, "style"], [43, 19, 37, 17], [43, 20, 37, 18, "backgroundColor"], [43, 35, 37, 33], [43, 38, 37, 36, "originalBackgroundColor"], [43, 61, 37, 59], [44, 4, 38, 2], [44, 5, 38, 3], [45, 4, 39, 2], [45, 10, 39, 8, "animationCancelCallback"], [45, 33, 39, 31], [45, 36, 39, 34, "animationCancelCallback"], [45, 37, 39, 34], [45, 42, 39, 40], [46, 6, 40, 4, "onFinalize"], [46, 16, 40, 14], [46, 17, 40, 15], [46, 18, 40, 16], [47, 6, 41, 4, "element"], [47, 13, 41, 11], [47, 14, 41, 12, "removeEventListener"], [47, 33, 41, 31], [47, 34, 41, 32], [47, 51, 41, 49], [47, 53, 41, 51, "animationCancelCallback"], [47, 76, 41, 74], [47, 77, 41, 75], [48, 4, 42, 2], [48, 5, 42, 3], [49, 4, 43, 2], [49, 10, 43, 8, "animationEndCallback"], [49, 30, 43, 28], [49, 33, 43, 31, "animationEndCallback"], [49, 34, 43, 31], [49, 39, 43, 37], [50, 6, 44, 4, "onFinalize"], [50, 16, 44, 14], [50, 17, 44, 15], [50, 18, 44, 16], [51, 6, 45, 4, "element"], [51, 13, 45, 11], [51, 14, 45, 12, "removeEventListener"], [51, 33, 45, 31], [51, 34, 45, 32], [51, 48, 45, 46], [51, 50, 45, 48, "animationEndCallback"], [51, 70, 45, 68], [51, 71, 45, 69], [52, 4, 46, 2], [52, 5, 46, 3], [53, 4, 47, 2, "element"], [53, 11, 47, 9], [53, 12, 47, 10, "addEventListener"], [53, 28, 47, 26], [53, 29, 47, 27], [53, 43, 47, 41], [53, 45, 47, 43, "animationEndCallback"], [53, 65, 47, 63], [53, 66, 47, 64], [54, 4, 48, 2, "element"], [54, 11, 48, 9], [54, 12, 48, 10, "addEventListener"], [54, 28, 48, 26], [54, 29, 48, 27], [54, 46, 48, 44], [54, 48, 48, 46, "animationCancelCallback"], [54, 71, 48, 69], [54, 72, 48, 70], [55, 4, 49, 2, "element"], [55, 11, 49, 9], [55, 12, 49, 10, "append<PERSON><PERSON><PERSON>"], [55, 23, 49, 21], [55, 24, 49, 22, "dummy"], [55, 29, 49, 27], [55, 30, 49, 28], [56, 2, 50, 0], [57, 2, 51, 0], [57, 11, 51, 9, "prepareDummy"], [57, 23, 51, 21, "prepareDummy"], [57, 24, 51, 22, "element"], [57, 31, 51, 29], [57, 33, 51, 31, "animationConfig"], [57, 48, 51, 46], [57, 50, 51, 48, "transitionData"], [57, 64, 51, 62], [57, 66, 51, 64, "dummyTransitionKeyframeName"], [57, 93, 51, 91], [57, 95, 51, 93], [58, 4, 52, 2], [58, 10, 52, 8, "dummyAnimationConfig"], [58, 30, 52, 28], [58, 33, 52, 31], [59, 6, 53, 4, "animationName"], [59, 19, 53, 17], [59, 21, 53, 19, "dummyTransitionKeyframeName"], [59, 48, 53, 46], [60, 6, 54, 4, "animationType"], [60, 19, 54, 17], [60, 21, 54, 19, "LayoutAnimationType"], [60, 53, 54, 38], [60, 54, 54, 39, "LAYOUT"], [60, 60, 54, 45], [61, 6, 55, 4, "duration"], [61, 14, 55, 12], [61, 16, 55, 14, "animationConfig"], [61, 31, 55, 29], [61, 32, 55, 30, "duration"], [61, 40, 55, 38], [62, 6, 56, 4, "delay"], [62, 11, 56, 9], [62, 13, 56, 11, "animationConfig"], [62, 28, 56, 26], [62, 29, 56, 27, "delay"], [62, 34, 56, 32], [63, 6, 57, 4, "easing"], [63, 12, 57, 10], [63, 14, 57, 12], [63, 18, 57, 12, "getEasingByName"], [63, 44, 57, 27], [63, 46, 57, 28, "transitionData"], [63, 60, 57, 42], [63, 61, 57, 43, "easingY"], [63, 68, 57, 50], [63, 69, 57, 51], [64, 6, 58, 4, "callback"], [64, 14, 58, 12], [64, 16, 58, 14], [64, 20, 58, 18], [65, 6, 59, 4, "reversed"], [65, 14, 59, 12], [65, 16, 59, 14], [66, 4, 60, 2], [66, 5, 60, 3], [67, 4, 61, 2], [67, 10, 61, 8, "dummy"], [67, 15, 61, 13], [67, 18, 61, 16, "element"], [67, 25, 61, 23], [67, 26, 61, 24, "cloneNode"], [67, 35, 61, 33], [67, 36, 61, 34], [67, 40, 61, 38], [67, 41, 61, 39], [68, 4, 62, 2, "resetStyle"], [68, 14, 62, 12], [68, 15, 62, 13, "dummy"], [68, 20, 62, 18], [68, 21, 62, 19], [69, 4, 63, 2], [69, 11, 63, 9], [70, 6, 64, 4, "dummy"], [70, 11, 64, 9], [71, 6, 65, 4, "dummyAnimationConfig"], [72, 4, 66, 2], [72, 5, 66, 3], [73, 2, 67, 0], [74, 2, 68, 7], [74, 11, 68, 16, "prepareCurvedTransition"], [74, 34, 68, 39, "prepareCurvedTransition"], [74, 35, 68, 40, "element"], [74, 42, 68, 47], [74, 44, 68, 49, "animationConfig"], [74, 59, 68, 64], [74, 61, 68, 66, "transitionData"], [74, 75, 68, 80], [74, 77, 68, 82, "dummyTransitionKeyframeName"], [74, 104, 68, 109], [74, 106, 68, 111], [75, 4, 69, 2], [75, 10, 69, 8], [76, 6, 70, 4, "dummy"], [76, 11, 70, 9], [77, 6, 71, 4, "dummyAnimationConfig"], [78, 4, 72, 2], [78, 5, 72, 3], [78, 8, 72, 6, "prepareDummy"], [78, 20, 72, 18], [78, 21, 72, 19, "element"], [78, 28, 72, 26], [78, 30, 72, 28, "animationConfig"], [78, 45, 72, 43], [78, 47, 72, 45, "transitionData"], [78, 61, 72, 59], [78, 63, 72, 61, "dummyTransitionKeyframeName"], [78, 90, 72, 88], [78, 91, 72, 89], [79, 4, 73, 2, "prepareParent"], [79, 17, 73, 15], [79, 18, 73, 16, "element"], [79, 25, 73, 23], [79, 27, 73, 25, "dummy"], [79, 32, 73, 30], [79, 34, 73, 32, "animationConfig"], [79, 49, 73, 47], [79, 51, 73, 49, "transitionData"], [79, 65, 73, 63], [79, 66, 73, 64], [80, 4, 74, 2], [80, 11, 74, 9], [81, 6, 75, 4, "dummy"], [81, 11, 75, 9], [82, 6, 76, 4, "dummyAnimationConfig"], [83, 4, 77, 2], [83, 5, 77, 3], [84, 2, 78, 0], [85, 2, 79, 7], [85, 11, 79, 16, "CurvedTransition"], [85, 27, 79, 32, "CurvedTransition"], [85, 28, 79, 33, "keyframeXName"], [85, 41, 79, 46], [85, 43, 79, 48, "keyframeYName"], [85, 56, 79, 61], [85, 58, 79, 63, "transitionData"], [85, 72, 79, 77], [85, 74, 79, 79], [86, 4, 80, 2], [86, 10, 80, 8, "keyframeXObj"], [86, 22, 80, 20], [86, 25, 80, 23], [87, 6, 81, 4, "name"], [87, 10, 81, 8], [87, 12, 81, 10, "keyframeXName"], [87, 25, 81, 23], [88, 6, 82, 4, "style"], [88, 11, 82, 9], [88, 13, 82, 11], [89, 8, 83, 6], [89, 9, 83, 7], [89, 11, 83, 9], [90, 10, 84, 8, "transform"], [90, 19, 84, 17], [90, 21, 84, 19], [90, 22, 84, 20], [91, 12, 85, 10, "translateX"], [91, 22, 85, 20], [91, 24, 85, 22], [91, 27, 85, 25, "transitionData"], [91, 41, 85, 39], [91, 42, 85, 40, "translateX"], [91, 52, 85, 50], [91, 56, 85, 54], [92, 12, 86, 10, "scale"], [92, 17, 86, 15], [92, 19, 86, 17], [92, 22, 86, 20, "transitionData"], [92, 36, 86, 34], [92, 37, 86, 35, "scaleX"], [92, 43, 86, 41], [92, 47, 86, 45, "transitionData"], [92, 61, 86, 59], [92, 62, 86, 60, "scaleY"], [92, 68, 86, 66], [93, 10, 87, 8], [93, 11, 87, 9], [94, 8, 88, 6], [95, 6, 89, 4], [95, 7, 89, 5], [96, 6, 90, 4, "duration"], [96, 14, 90, 12], [96, 16, 90, 14], [97, 4, 91, 2], [97, 5, 91, 3], [98, 4, 92, 2], [98, 10, 92, 8, "keyframeYObj"], [98, 22, 92, 20], [98, 25, 92, 23], [99, 6, 93, 4, "name"], [99, 10, 93, 8], [99, 12, 93, 10, "keyframeYName"], [99, 25, 93, 23], [100, 6, 94, 4, "style"], [100, 11, 94, 9], [100, 13, 94, 11], [101, 8, 95, 6], [101, 9, 95, 7], [101, 11, 95, 9], [102, 10, 96, 8, "transform"], [102, 19, 96, 17], [102, 21, 96, 19], [102, 22, 96, 20], [103, 12, 97, 10, "translateY"], [103, 22, 97, 20], [103, 24, 97, 22], [103, 27, 97, 25, "transitionData"], [103, 41, 97, 39], [103, 42, 97, 40, "translateY"], [103, 52, 97, 50], [103, 56, 97, 54], [104, 12, 98, 10, "scale"], [104, 17, 98, 15], [104, 19, 98, 17], [104, 22, 98, 20, "transitionData"], [104, 36, 98, 34], [104, 37, 98, 35, "scaleX"], [104, 43, 98, 41], [104, 47, 98, 45, "transitionData"], [104, 61, 98, 59], [104, 62, 98, 60, "scaleY"], [104, 68, 98, 66], [105, 10, 99, 8], [105, 11, 99, 9], [106, 8, 100, 6], [107, 6, 101, 4], [107, 7, 101, 5], [108, 6, 102, 4, "duration"], [108, 14, 102, 12], [108, 16, 102, 14], [109, 4, 103, 2], [109, 5, 103, 3], [110, 4, 104, 2], [110, 11, 104, 9], [111, 6, 105, 4, "firstKeyframeObj"], [111, 22, 105, 20], [111, 24, 105, 22, "keyframeXObj"], [111, 36, 105, 34], [112, 6, 106, 4, "secondKeyframeObj"], [112, 23, 106, 21], [112, 25, 106, 23, "keyframeYObj"], [113, 4, 107, 2], [113, 5, 107, 3], [114, 2, 108, 0], [115, 0, 108, 1], [115, 3]], "functionMap": {"names": ["<global>", "resetStyle", "showChildren", "prepareParent", "onFinalize", "animationCancelCallback", "animationEndCallback", "prepareDummy", "prepareCurvedTransition", "CurvedTransition"], "mappings": "AAA;ACI;CDQ;AEC;CFU;AGC;qBCO;GDM;kCEC;GFG;+BGC;GHG;CHI;AOC;CPgB;OQC;CRU;OSC;CT6B"}}, "type": "js/module"}]}