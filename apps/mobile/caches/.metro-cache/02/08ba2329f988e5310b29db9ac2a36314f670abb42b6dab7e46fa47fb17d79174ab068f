{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 987}, "end": {"line": 33, "column": 40, "index": 1027}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "@tanstack/react-query", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1028}, "end": {"line": 34, "column": 68, "index": 1096}}], "key": "Pzwu/0TIyhnZOrC9PAkpZx92hFo=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _expoRouter = require(_dependencyMap[21], \"expo-router\");\n  var _reactQuery = require(_dependencyMap[22], \"@tanstack/react-query\");\n  var _jsxDevRuntime = require(_dependencyMap[23], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const router = (0, _expoRouter.useRouter)();\n    const queryClient = (0, _reactQuery.useQueryClient)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0, _react.useState)(null);\n    const [sessionStartTime, setSessionStartTime] = (0, _react.useState)(null);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = async () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n      setSessionStartTime(new Date());\n\n      // Create a new session in the database\n      try {\n        const response = await fetch('/api/sessions', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            title: 'New Brainstorming Session',\n            agent_type: 'general',\n            user_id: null // For now, anonymous sessions\n          })\n        });\n        if (response.ok) {\n          const {\n            session\n          } = await response.json();\n          setCurrentSessionId(session.id);\n\n          // Add initial messages to the session\n          for (const message of _fakeData.fakeMessages) {\n            await fetch('/api/messages', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                session_id: session.id,\n                role: message.role,\n                content: message.content\n              })\n            });\n          }\n        }\n      } catch (error) {\n        console.error('Error creating session:', error);\n      }\n    };\n    const handleSendMessage = async (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Save user message to database if we have a session\n      if (currentSessionId) {\n        try {\n          await fetch('/api/messages', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              session_id: currentSessionId,\n              role: 'user',\n              content: message\n            })\n          });\n        } catch (error) {\n          console.error('Error saving user message:', error);\n        }\n      }\n\n      // Simulate AI response\n      setTimeout(async () => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n\n        // Save AI response to database if we have a session\n        if (currentSessionId) {\n          try {\n            await fetch('/api/messages', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                session_id: currentSessionId,\n                role: 'assistant',\n                content: aiResponse.content,\n                quick_actions: _fakeData.fakeQuickActions\n              })\n            });\n          } catch (error) {\n            console.error('Error saving AI message:', error);\n          }\n        }\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n    const handleDone = async () => {\n      if (!currentSessionId) {\n        _Alert.default.alert(\"No Session\", \"No active session to save.\");\n        return;\n      }\n      try {\n        // Stop any active recording\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n\n        // Calculate session duration\n        const duration = sessionStartTime ? Math.floor((new Date() - sessionStartTime) / 1000) : 0;\n\n        // Generate a session title based on the first user message or use default\n        const firstUserMessage = messages.find(msg => msg.role === 'user');\n        const sessionTitle = firstUserMessage ? firstUserMessage.content.slice(0, 50) + (firstUserMessage.content.length > 50 ? '...' : '') : 'Brainstorming Session';\n\n        // Update session status to completed and add metadata\n        const response = await fetch(`/api/sessions/${currentSessionId}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            status: 'completed',\n            title: sessionTitle,\n            duration_seconds: duration,\n            summary: `Session completed with ${messages.length} messages`\n          })\n        });\n        if (response.ok) {\n          // Invalidate sessions cache to refresh history\n          queryClient.invalidateQueries({\n            queryKey: ['sessions']\n          });\n\n          // Navigate to session overview\n          router.push(`/session/${currentSessionId}`);\n\n          // Reset current session state\n          setCurrentSessionId(null);\n          setSessionStartTime(null);\n          setMessages([]);\n          setQuickActions([]);\n          setIsFirstTime(true);\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        } else {\n          throw new Error('Failed to save session');\n        }\n      } catch (error) {\n        console.error('Error saving session:', error);\n        _Alert.default.alert('Error', 'Failed to save session. Please try again.');\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: handleDone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"902VswXJGTYh6FyaM254sBNxdF8=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _expoRouter.useRouter, _reactQuery.useQueryClient, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 739, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 33, 0], [27, 6, 33, 0, "_expoRouter"], [27, 17, 33, 0], [27, 20, 33, 0, "require"], [27, 27, 33, 0], [27, 28, 33, 0, "_dependencyMap"], [27, 42, 33, 0], [28, 2, 34, 0], [28, 6, 34, 0, "_reactQuery"], [28, 17, 34, 0], [28, 20, 34, 0, "require"], [28, 27, 34, 0], [28, 28, 34, 0, "_dependencyMap"], [28, 42, 34, 0], [29, 2, 34, 68], [29, 6, 34, 68, "_jsxDevRuntime"], [29, 20, 34, 68], [29, 23, 34, 68, "require"], [29, 30, 34, 68], [29, 31, 34, 68, "_dependencyMap"], [29, 45, 34, 68], [30, 2, 34, 68], [30, 6, 34, 68, "_jsxFileName"], [30, 18, 34, 68], [31, 4, 34, 68, "_s"], [31, 6, 34, 68], [31, 9, 34, 68, "$RefreshSig$"], [31, 21, 34, 68], [32, 2, 34, 68], [32, 11, 34, 68, "_interopRequireWildcard"], [32, 35, 34, 68, "e"], [32, 36, 34, 68], [32, 38, 34, 68, "t"], [32, 39, 34, 68], [32, 68, 34, 68, "WeakMap"], [32, 75, 34, 68], [32, 81, 34, 68, "r"], [32, 82, 34, 68], [32, 89, 34, 68, "WeakMap"], [32, 96, 34, 68], [32, 100, 34, 68, "n"], [32, 101, 34, 68], [32, 108, 34, 68, "WeakMap"], [32, 115, 34, 68], [32, 127, 34, 68, "_interopRequireWildcard"], [32, 150, 34, 68], [32, 162, 34, 68, "_interopRequireWildcard"], [32, 163, 34, 68, "e"], [32, 164, 34, 68], [32, 166, 34, 68, "t"], [32, 167, 34, 68], [32, 176, 34, 68, "t"], [32, 177, 34, 68], [32, 181, 34, 68, "e"], [32, 182, 34, 68], [32, 186, 34, 68, "e"], [32, 187, 34, 68], [32, 188, 34, 68, "__esModule"], [32, 198, 34, 68], [32, 207, 34, 68, "e"], [32, 208, 34, 68], [32, 214, 34, 68, "o"], [32, 215, 34, 68], [32, 217, 34, 68, "i"], [32, 218, 34, 68], [32, 220, 34, 68, "f"], [32, 221, 34, 68], [32, 226, 34, 68, "__proto__"], [32, 235, 34, 68], [32, 243, 34, 68, "default"], [32, 250, 34, 68], [32, 252, 34, 68, "e"], [32, 253, 34, 68], [32, 270, 34, 68, "e"], [32, 271, 34, 68], [32, 294, 34, 68, "e"], [32, 295, 34, 68], [32, 320, 34, 68, "e"], [32, 321, 34, 68], [32, 330, 34, 68, "f"], [32, 331, 34, 68], [32, 337, 34, 68, "o"], [32, 338, 34, 68], [32, 341, 34, 68, "t"], [32, 342, 34, 68], [32, 345, 34, 68, "n"], [32, 346, 34, 68], [32, 349, 34, 68, "r"], [32, 350, 34, 68], [32, 358, 34, 68, "o"], [32, 359, 34, 68], [32, 360, 34, 68, "has"], [32, 363, 34, 68], [32, 364, 34, 68, "e"], [32, 365, 34, 68], [32, 375, 34, 68, "o"], [32, 376, 34, 68], [32, 377, 34, 68, "get"], [32, 380, 34, 68], [32, 381, 34, 68, "e"], [32, 382, 34, 68], [32, 385, 34, 68, "o"], [32, 386, 34, 68], [32, 387, 34, 68, "set"], [32, 390, 34, 68], [32, 391, 34, 68, "e"], [32, 392, 34, 68], [32, 394, 34, 68, "f"], [32, 395, 34, 68], [32, 411, 34, 68, "t"], [32, 412, 34, 68], [32, 416, 34, 68, "e"], [32, 417, 34, 68], [32, 433, 34, 68, "t"], [32, 434, 34, 68], [32, 441, 34, 68, "hasOwnProperty"], [32, 455, 34, 68], [32, 456, 34, 68, "call"], [32, 460, 34, 68], [32, 461, 34, 68, "e"], [32, 462, 34, 68], [32, 464, 34, 68, "t"], [32, 465, 34, 68], [32, 472, 34, 68, "i"], [32, 473, 34, 68], [32, 477, 34, 68, "o"], [32, 478, 34, 68], [32, 481, 34, 68, "Object"], [32, 487, 34, 68], [32, 488, 34, 68, "defineProperty"], [32, 502, 34, 68], [32, 507, 34, 68, "Object"], [32, 513, 34, 68], [32, 514, 34, 68, "getOwnPropertyDescriptor"], [32, 538, 34, 68], [32, 539, 34, 68, "e"], [32, 540, 34, 68], [32, 542, 34, 68, "t"], [32, 543, 34, 68], [32, 550, 34, 68, "i"], [32, 551, 34, 68], [32, 552, 34, 68, "get"], [32, 555, 34, 68], [32, 559, 34, 68, "i"], [32, 560, 34, 68], [32, 561, 34, 68, "set"], [32, 564, 34, 68], [32, 568, 34, 68, "o"], [32, 569, 34, 68], [32, 570, 34, 68, "f"], [32, 571, 34, 68], [32, 573, 34, 68, "t"], [32, 574, 34, 68], [32, 576, 34, 68, "i"], [32, 577, 34, 68], [32, 581, 34, 68, "f"], [32, 582, 34, 68], [32, 583, 34, 68, "t"], [32, 584, 34, 68], [32, 588, 34, 68, "e"], [32, 589, 34, 68], [32, 590, 34, 68, "t"], [32, 591, 34, 68], [32, 602, 34, 68, "f"], [32, 603, 34, 68], [32, 608, 34, 68, "e"], [32, 609, 34, 68], [32, 611, 34, 68, "t"], [32, 612, 34, 68], [33, 2, 36, 15], [33, 11, 36, 24, "BrainstormScreen"], [33, 27, 36, 40, "BrainstormScreen"], [33, 28, 36, 40], [33, 30, 36, 43], [34, 4, 36, 43, "_s"], [34, 6, 36, 43], [35, 4, 37, 2], [35, 10, 37, 8, "insets"], [35, 16, 37, 14], [35, 19, 37, 17], [35, 23, 37, 17, "useSafeAreaInsets"], [35, 68, 37, 34], [35, 70, 37, 35], [35, 71, 37, 36], [36, 4, 38, 2], [36, 10, 38, 8, "colors"], [36, 16, 38, 14], [36, 19, 38, 17], [36, 23, 38, 17, "useColors"], [36, 43, 38, 26], [36, 45, 38, 27], [36, 46, 38, 28], [37, 4, 39, 2], [37, 10, 39, 8, "router"], [37, 16, 39, 14], [37, 19, 39, 17], [37, 23, 39, 17, "useRouter"], [37, 44, 39, 26], [37, 46, 39, 27], [37, 47, 39, 28], [38, 4, 40, 2], [38, 10, 40, 8, "queryClient"], [38, 21, 40, 19], [38, 24, 40, 22], [38, 28, 40, 22, "useQueryClient"], [38, 54, 40, 36], [38, 56, 40, 37], [38, 57, 40, 38], [39, 4, 41, 2], [39, 10, 41, 8], [39, 11, 41, 9, "fontsLoaded"], [39, 22, 41, 20], [39, 23, 41, 21], [39, 26, 41, 24], [39, 30, 41, 24, "useFonts"], [39, 47, 41, 32], [39, 49, 41, 33], [40, 6, 42, 4, "Poppins_400Regular"], [40, 24, 42, 22], [40, 26, 42, 4, "Poppins_400Regular"], [40, 53, 42, 22], [41, 6, 43, 4, "Poppins_500Medium"], [41, 23, 43, 21], [41, 25, 43, 4, "Poppins_500Medium"], [41, 51, 43, 21], [42, 6, 44, 4, "Poppins_600SemiBold"], [42, 25, 44, 23], [42, 27, 44, 4, "Poppins_600SemiBold"], [43, 4, 45, 2], [43, 5, 45, 3], [43, 6, 45, 4], [44, 4, 47, 2], [44, 10, 47, 8, "recorder"], [44, 18, 47, 16], [44, 21, 47, 19], [44, 25, 47, 19, "useAudioRecorder"], [44, 52, 47, 35], [44, 54, 47, 36, "RecordingPresets"], [44, 81, 47, 52], [44, 82, 47, 53, "HIGH_QUALITY"], [44, 94, 47, 65], [44, 95, 47, 66], [45, 4, 48, 2], [45, 10, 48, 8, "recorderState"], [45, 23, 48, 21], [45, 26, 48, 24], [45, 30, 48, 24, "useAudioRecorderState"], [45, 62, 48, 45], [45, 64, 48, 46, "recorder"], [45, 72, 48, 54], [45, 73, 48, 55], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "messages"], [46, 19, 50, 17], [46, 21, 50, 19, "setMessages"], [46, 32, 50, 30], [46, 33, 50, 31], [46, 36, 50, 34], [46, 40, 50, 34, "useState"], [46, 55, 50, 42], [46, 57, 50, 43], [46, 59, 50, 45], [46, 60, 50, 46], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "inputText"], [47, 20, 51, 18], [47, 22, 51, 20, "setInputText"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 61, 51, 47], [47, 62, 51, 48], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "isFirstTime"], [48, 22, 52, 20], [48, 24, 52, 22, "setIsFirstTime"], [48, 38, 52, 36], [48, 39, 52, 37], [48, 42, 52, 40], [48, 46, 52, 40, "useState"], [48, 61, 52, 48], [48, 63, 52, 49], [48, 67, 52, 53], [48, 68, 52, 54], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isLoading"], [49, 20, 53, 18], [49, 22, 53, 20, "setIsLoading"], [49, 34, 53, 32], [49, 35, 53, 33], [49, 38, 53, 36], [49, 42, 53, 36, "useState"], [49, 57, 53, 44], [49, 59, 53, 45], [49, 64, 53, 50], [49, 65, 53, 51], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "quickActions"], [50, 23, 54, 21], [50, 25, 54, 23, "setQuickActions"], [50, 40, 54, 38], [50, 41, 54, 39], [50, 44, 54, 42], [50, 48, 54, 42, "useState"], [50, 63, 54, 50], [50, 65, 54, 51], [50, 67, 54, 53], [50, 68, 54, 54], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "voiceMode"], [51, 20, 55, 18], [51, 22, 55, 20, "setVoiceMode"], [51, 34, 55, 32], [51, 35, 55, 33], [51, 38, 55, 36], [51, 42, 55, 36, "useState"], [51, 57, 55, 44], [51, 59, 55, 45], [51, 63, 55, 49], [51, 64, 55, 50], [51, 65, 55, 51], [51, 66, 55, 52], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "hasPermission"], [52, 24, 56, 22], [52, 26, 56, 24, "setHasPermission"], [52, 42, 56, 40], [52, 43, 56, 41], [52, 46, 56, 44], [52, 50, 56, 44, "useState"], [52, 65, 56, 52], [52, 67, 56, 53], [52, 72, 56, 58], [52, 73, 56, 59], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isDictating"], [53, 22, 57, 20], [53, 24, 57, 22, "setIsDictating"], [53, 38, 57, 36], [53, 39, 57, 37], [53, 42, 57, 40], [53, 46, 57, 40, "useState"], [53, 61, 57, 48], [53, 63, 57, 49], [53, 68, 57, 54], [53, 69, 57, 55], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "longPressedMessage"], [54, 29, 58, 27], [54, 31, 58, 29, "setLongPressedMessage"], [54, 52, 58, 50], [54, 53, 58, 51], [54, 56, 58, 54], [54, 60, 58, 54, "useState"], [54, 75, 58, 62], [54, 77, 58, 63], [54, 81, 58, 67], [54, 82, 58, 68], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "isContextMenuVisible"], [55, 31, 59, 29], [55, 33, 59, 31, "setIsContextMenuVisible"], [55, 56, 59, 54], [55, 57, 59, 55], [55, 60, 59, 58], [55, 64, 59, 58, "useState"], [55, 79, 59, 66], [55, 81, 59, 67], [55, 86, 59, 72], [55, 87, 59, 73], [56, 4, 60, 2], [56, 10, 60, 8], [56, 11, 60, 9, "transcript"], [56, 21, 60, 19], [56, 23, 60, 21, "setTranscript"], [56, 36, 60, 34], [56, 37, 60, 35], [56, 40, 60, 38], [56, 44, 60, 38, "useState"], [56, 59, 60, 46], [56, 61, 60, 47], [56, 63, 60, 49], [56, 64, 60, 50], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "isMuted"], [57, 18, 61, 16], [57, 20, 61, 18, "setIsMuted"], [57, 30, 61, 28], [57, 31, 61, 29], [57, 34, 61, 32], [57, 38, 61, 32, "useState"], [57, 53, 61, 40], [57, 55, 61, 41], [57, 60, 61, 46], [57, 61, 61, 47], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "currentSessionId"], [58, 27, 62, 25], [58, 29, 62, 27, "setCurrentSessionId"], [58, 48, 62, 46], [58, 49, 62, 47], [58, 52, 62, 50], [58, 56, 62, 50, "useState"], [58, 71, 62, 58], [58, 73, 62, 59], [58, 77, 62, 63], [58, 78, 62, 64], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "sessionStartTime"], [59, 27, 63, 25], [59, 29, 63, 27, "setSessionStartTime"], [59, 48, 63, 46], [59, 49, 63, 47], [59, 52, 63, 50], [59, 56, 63, 50, "useState"], [59, 71, 63, 58], [59, 73, 63, 59], [59, 77, 63, 63], [59, 78, 63, 64], [60, 4, 65, 2], [60, 10, 65, 8, "scrollViewRef"], [60, 23, 65, 21], [60, 26, 65, 24], [60, 30, 65, 24, "useRef"], [60, 43, 65, 30], [60, 45, 65, 31], [60, 49, 65, 35], [60, 50, 65, 36], [62, 4, 67, 2], [63, 4, 68, 2], [63, 8, 68, 2, "useEffect"], [63, 24, 68, 11], [63, 26, 68, 12], [63, 32, 68, 18], [64, 6, 69, 4], [64, 7, 69, 5], [64, 19, 69, 17], [65, 8, 70, 6], [65, 14, 70, 12], [66, 10, 70, 14, "granted"], [67, 8, 70, 22], [67, 9, 70, 23], [67, 12, 70, 26], [67, 18, 70, 32], [67, 22, 70, 32, "requestRecordingPermissionsAsync"], [67, 65, 70, 64], [67, 67, 70, 65], [67, 68, 70, 66], [68, 8, 71, 6, "setHasPermission"], [68, 24, 71, 22], [68, 25, 71, 23, "granted"], [68, 32, 71, 30], [68, 33, 71, 31], [69, 8, 72, 6], [69, 12, 72, 10], [69, 13, 72, 11, "granted"], [69, 20, 72, 18], [69, 24, 72, 22, "voiceMode"], [69, 33, 72, 31], [69, 35, 72, 33], [70, 10, 73, 8, "<PERSON><PERSON>"], [70, 24, 73, 13], [70, 25, 73, 14, "alert"], [70, 30, 73, 19], [70, 31, 74, 10], [70, 52, 74, 31], [70, 54, 75, 10], [70, 133, 75, 89], [70, 135, 76, 10], [70, 136, 77, 12], [71, 12, 77, 14, "text"], [71, 16, 77, 18], [71, 18, 77, 20], [71, 33, 77, 35], [72, 12, 77, 37, "onPress"], [72, 19, 77, 44], [72, 21, 77, 46, "onPress"], [72, 22, 77, 46], [72, 27, 77, 52, "setVoiceMode"], [72, 39, 77, 64], [72, 40, 77, 65], [72, 45, 77, 70], [73, 10, 77, 72], [73, 11, 77, 73], [73, 13, 78, 12], [74, 12, 78, 14, "text"], [74, 16, 78, 18], [74, 18, 78, 20], [75, 10, 78, 25], [75, 11, 78, 26], [75, 12, 80, 8], [75, 13, 80, 9], [76, 8, 81, 6], [77, 6, 82, 4], [77, 7, 82, 5], [77, 9, 82, 7], [77, 10, 82, 8], [78, 4, 83, 2], [78, 5, 83, 3], [78, 7, 83, 5], [78, 8, 83, 6, "voiceMode"], [78, 17, 83, 15], [78, 18, 83, 16], [78, 19, 83, 17], [79, 4, 85, 2], [79, 10, 85, 8, "handleStartBrainstorming"], [79, 34, 85, 32], [79, 37, 85, 35], [79, 43, 85, 35, "handleStartBrainstorming"], [79, 44, 85, 35], [79, 49, 85, 47], [80, 6, 86, 4, "Haptics"], [80, 13, 86, 11], [80, 14, 86, 12, "impactAsync"], [80, 25, 86, 23], [80, 26, 86, 24, "Haptics"], [80, 33, 86, 31], [80, 34, 86, 32, "ImpactFeedbackStyle"], [80, 53, 86, 51], [80, 54, 86, 52, "Medium"], [80, 60, 86, 58], [80, 61, 86, 59], [81, 6, 87, 4, "setIsFirstTime"], [81, 20, 87, 18], [81, 21, 87, 19], [81, 26, 87, 24], [81, 27, 87, 25], [82, 6, 88, 4, "setMessages"], [82, 17, 88, 15], [82, 18, 88, 16, "fakeMessages"], [82, 40, 88, 28], [82, 41, 88, 29], [83, 6, 89, 4, "setQuickActions"], [83, 21, 89, 19], [83, 22, 89, 20, "fakeQuickActions"], [83, 48, 89, 36], [83, 49, 89, 37], [84, 6, 90, 4, "setSessionStartTime"], [84, 25, 90, 23], [84, 26, 90, 24], [84, 30, 90, 28, "Date"], [84, 34, 90, 32], [84, 35, 90, 33], [84, 36, 90, 34], [84, 37, 90, 35], [86, 6, 92, 4], [87, 6, 93, 4], [87, 10, 93, 8], [88, 8, 94, 6], [88, 14, 94, 12, "response"], [88, 22, 94, 20], [88, 25, 94, 23], [88, 31, 94, 29, "fetch"], [88, 36, 94, 34], [88, 37, 94, 35], [88, 52, 94, 50], [88, 54, 94, 52], [89, 10, 95, 8, "method"], [89, 16, 95, 14], [89, 18, 95, 16], [89, 24, 95, 22], [90, 10, 96, 8, "headers"], [90, 17, 96, 15], [90, 19, 96, 17], [91, 12, 96, 19], [91, 26, 96, 33], [91, 28, 96, 35], [92, 10, 96, 54], [92, 11, 96, 55], [93, 10, 97, 8, "body"], [93, 14, 97, 12], [93, 16, 97, 14, "JSON"], [93, 20, 97, 18], [93, 21, 97, 19, "stringify"], [93, 30, 97, 28], [93, 31, 97, 29], [94, 12, 98, 10, "title"], [94, 17, 98, 15], [94, 19, 98, 17], [94, 46, 98, 44], [95, 12, 99, 10, "agent_type"], [95, 22, 99, 20], [95, 24, 99, 22], [95, 33, 99, 31], [96, 12, 100, 10, "user_id"], [96, 19, 100, 17], [96, 21, 100, 19], [96, 25, 100, 23], [96, 26, 100, 25], [97, 10, 101, 8], [97, 11, 101, 9], [98, 8, 102, 6], [98, 9, 102, 7], [98, 10, 102, 8], [99, 8, 104, 6], [99, 12, 104, 10, "response"], [99, 20, 104, 18], [99, 21, 104, 19, "ok"], [99, 23, 104, 21], [99, 25, 104, 23], [100, 10, 105, 8], [100, 16, 105, 14], [101, 12, 105, 16, "session"], [102, 10, 105, 24], [102, 11, 105, 25], [102, 14, 105, 28], [102, 20, 105, 34, "response"], [102, 28, 105, 42], [102, 29, 105, 43, "json"], [102, 33, 105, 47], [102, 34, 105, 48], [102, 35, 105, 49], [103, 10, 106, 8, "setCurrentSessionId"], [103, 29, 106, 27], [103, 30, 106, 28, "session"], [103, 37, 106, 35], [103, 38, 106, 36, "id"], [103, 40, 106, 38], [103, 41, 106, 39], [105, 10, 108, 8], [106, 10, 109, 8], [106, 15, 109, 13], [106, 21, 109, 19, "message"], [106, 28, 109, 26], [106, 32, 109, 30, "fakeMessages"], [106, 54, 109, 42], [106, 56, 109, 44], [107, 12, 110, 10], [107, 18, 110, 16, "fetch"], [107, 23, 110, 21], [107, 24, 110, 22], [107, 39, 110, 37], [107, 41, 110, 39], [108, 14, 111, 12, "method"], [108, 20, 111, 18], [108, 22, 111, 20], [108, 28, 111, 26], [109, 14, 112, 12, "headers"], [109, 21, 112, 19], [109, 23, 112, 21], [110, 16, 112, 23], [110, 30, 112, 37], [110, 32, 112, 39], [111, 14, 112, 58], [111, 15, 112, 59], [112, 14, 113, 12, "body"], [112, 18, 113, 16], [112, 20, 113, 18, "JSON"], [112, 24, 113, 22], [112, 25, 113, 23, "stringify"], [112, 34, 113, 32], [112, 35, 113, 33], [113, 16, 114, 14, "session_id"], [113, 26, 114, 24], [113, 28, 114, 26, "session"], [113, 35, 114, 33], [113, 36, 114, 34, "id"], [113, 38, 114, 36], [114, 16, 115, 14, "role"], [114, 20, 115, 18], [114, 22, 115, 20, "message"], [114, 29, 115, 27], [114, 30, 115, 28, "role"], [114, 34, 115, 32], [115, 16, 116, 14, "content"], [115, 23, 116, 21], [115, 25, 116, 23, "message"], [115, 32, 116, 30], [115, 33, 116, 31, "content"], [116, 14, 117, 12], [116, 15, 117, 13], [117, 12, 118, 10], [117, 13, 118, 11], [117, 14, 118, 12], [118, 10, 119, 8], [119, 8, 120, 6], [120, 6, 121, 4], [120, 7, 121, 5], [120, 8, 121, 6], [120, 15, 121, 13, "error"], [120, 20, 121, 18], [120, 22, 121, 20], [121, 8, 122, 6, "console"], [121, 15, 122, 13], [121, 16, 122, 14, "error"], [121, 21, 122, 19], [121, 22, 122, 20], [121, 47, 122, 45], [121, 49, 122, 47, "error"], [121, 54, 122, 52], [121, 55, 122, 53], [122, 6, 123, 4], [123, 4, 124, 2], [123, 5, 124, 3], [124, 4, 126, 2], [124, 10, 126, 8, "handleSendMessage"], [124, 27, 126, 25], [124, 30, 126, 28], [124, 36, 126, 28, "handleSendMessage"], [124, 37, 126, 35, "message"], [124, 44, 126, 42], [124, 47, 126, 45, "inputText"], [124, 56, 126, 54], [124, 61, 126, 59], [125, 6, 127, 4], [125, 10, 127, 8], [125, 11, 127, 9, "message"], [125, 18, 127, 16], [125, 19, 127, 17, "trim"], [125, 23, 127, 21], [125, 24, 127, 22], [125, 25, 127, 23], [125, 27, 127, 25], [126, 6, 129, 4], [126, 12, 129, 10, "newUserMessage"], [126, 26, 129, 24], [126, 29, 129, 27], [127, 8, 129, 29, "role"], [127, 12, 129, 33], [127, 14, 129, 35], [127, 20, 129, 41], [128, 8, 129, 43, "content"], [128, 15, 129, 50], [128, 17, 129, 52, "message"], [129, 6, 129, 60], [129, 7, 129, 61], [130, 6, 130, 4, "setMessages"], [130, 17, 130, 15], [130, 18, 130, 17, "prev"], [130, 22, 130, 21], [130, 26, 130, 26], [130, 27, 130, 27], [130, 30, 130, 30, "prev"], [130, 34, 130, 34], [130, 36, 130, 36, "newUserMessage"], [130, 50, 130, 50], [130, 51, 130, 51], [130, 52, 130, 52], [131, 6, 131, 4, "setInputText"], [131, 18, 131, 16], [131, 19, 131, 17], [131, 21, 131, 19], [131, 22, 131, 20], [132, 6, 132, 4, "setQuickActions"], [132, 21, 132, 19], [132, 22, 132, 20], [132, 24, 132, 22], [132, 25, 132, 23], [133, 6, 133, 4, "setIsLoading"], [133, 18, 133, 16], [133, 19, 133, 17], [133, 23, 133, 21], [133, 24, 133, 22], [135, 6, 135, 4], [136, 6, 136, 4], [136, 10, 136, 8, "currentSessionId"], [136, 26, 136, 24], [136, 28, 136, 26], [137, 8, 137, 6], [137, 12, 137, 10], [138, 10, 138, 8], [138, 16, 138, 14, "fetch"], [138, 21, 138, 19], [138, 22, 138, 20], [138, 37, 138, 35], [138, 39, 138, 37], [139, 12, 139, 10, "method"], [139, 18, 139, 16], [139, 20, 139, 18], [139, 26, 139, 24], [140, 12, 140, 10, "headers"], [140, 19, 140, 17], [140, 21, 140, 19], [141, 14, 140, 21], [141, 28, 140, 35], [141, 30, 140, 37], [142, 12, 140, 56], [142, 13, 140, 57], [143, 12, 141, 10, "body"], [143, 16, 141, 14], [143, 18, 141, 16, "JSON"], [143, 22, 141, 20], [143, 23, 141, 21, "stringify"], [143, 32, 141, 30], [143, 33, 141, 31], [144, 14, 142, 12, "session_id"], [144, 24, 142, 22], [144, 26, 142, 24, "currentSessionId"], [144, 42, 142, 40], [145, 14, 143, 12, "role"], [145, 18, 143, 16], [145, 20, 143, 18], [145, 26, 143, 24], [146, 14, 144, 12, "content"], [146, 21, 144, 19], [146, 23, 144, 21, "message"], [147, 12, 145, 10], [147, 13, 145, 11], [148, 10, 146, 8], [148, 11, 146, 9], [148, 12, 146, 10], [149, 8, 147, 6], [149, 9, 147, 7], [149, 10, 147, 8], [149, 17, 147, 15, "error"], [149, 22, 147, 20], [149, 24, 147, 22], [150, 10, 148, 8, "console"], [150, 17, 148, 15], [150, 18, 148, 16, "error"], [150, 23, 148, 21], [150, 24, 148, 22], [150, 52, 148, 50], [150, 54, 148, 52, "error"], [150, 59, 148, 57], [150, 60, 148, 58], [151, 8, 149, 6], [152, 6, 150, 4], [154, 6, 152, 4], [155, 6, 153, 4, "setTimeout"], [155, 16, 153, 14], [155, 17, 153, 15], [155, 29, 153, 27], [156, 8, 154, 6], [156, 14, 154, 12, "aiResponse"], [156, 24, 154, 22], [156, 27, 154, 25], [157, 10, 155, 8, "role"], [157, 14, 155, 12], [157, 16, 155, 14], [157, 27, 155, 25], [158, 10, 156, 8, "content"], [158, 17, 156, 15], [158, 19, 156, 17], [159, 8, 157, 6], [159, 9, 157, 7], [160, 8, 158, 6, "setMessages"], [160, 19, 158, 17], [160, 20, 158, 19, "prev"], [160, 24, 158, 23], [160, 28, 158, 28], [160, 29, 158, 29], [160, 32, 158, 32, "prev"], [160, 36, 158, 36], [160, 38, 158, 38, "aiResponse"], [160, 48, 158, 48], [160, 49, 158, 49], [160, 50, 158, 50], [161, 8, 159, 6, "setQuickActions"], [161, 23, 159, 21], [161, 24, 159, 22, "fakeQuickActions"], [161, 50, 159, 38], [161, 51, 159, 39], [162, 8, 160, 6, "setIsLoading"], [162, 20, 160, 18], [162, 21, 160, 19], [162, 26, 160, 24], [162, 27, 160, 25], [164, 8, 162, 6], [165, 8, 163, 6], [165, 12, 163, 10, "currentSessionId"], [165, 28, 163, 26], [165, 30, 163, 28], [166, 10, 164, 8], [166, 14, 164, 12], [167, 12, 165, 10], [167, 18, 165, 16, "fetch"], [167, 23, 165, 21], [167, 24, 165, 22], [167, 39, 165, 37], [167, 41, 165, 39], [168, 14, 166, 12, "method"], [168, 20, 166, 18], [168, 22, 166, 20], [168, 28, 166, 26], [169, 14, 167, 12, "headers"], [169, 21, 167, 19], [169, 23, 167, 21], [170, 16, 167, 23], [170, 30, 167, 37], [170, 32, 167, 39], [171, 14, 167, 58], [171, 15, 167, 59], [172, 14, 168, 12, "body"], [172, 18, 168, 16], [172, 20, 168, 18, "JSON"], [172, 24, 168, 22], [172, 25, 168, 23, "stringify"], [172, 34, 168, 32], [172, 35, 168, 33], [173, 16, 169, 14, "session_id"], [173, 26, 169, 24], [173, 28, 169, 26, "currentSessionId"], [173, 44, 169, 42], [174, 16, 170, 14, "role"], [174, 20, 170, 18], [174, 22, 170, 20], [174, 33, 170, 31], [175, 16, 171, 14, "content"], [175, 23, 171, 21], [175, 25, 171, 23, "aiResponse"], [175, 35, 171, 33], [175, 36, 171, 34, "content"], [175, 43, 171, 41], [176, 16, 172, 14, "quick_actions"], [176, 29, 172, 27], [176, 31, 172, 29, "fakeQuickActions"], [177, 14, 173, 12], [177, 15, 173, 13], [178, 12, 174, 10], [178, 13, 174, 11], [178, 14, 174, 12], [179, 10, 175, 8], [179, 11, 175, 9], [179, 12, 175, 10], [179, 19, 175, 17, "error"], [179, 24, 175, 22], [179, 26, 175, 24], [180, 12, 176, 10, "console"], [180, 19, 176, 17], [180, 20, 176, 18, "error"], [180, 25, 176, 23], [180, 26, 176, 24], [180, 52, 176, 50], [180, 54, 176, 52, "error"], [180, 59, 176, 57], [180, 60, 176, 58], [181, 10, 177, 8], [182, 8, 178, 6], [183, 6, 179, 4], [183, 7, 179, 5], [183, 9, 179, 7], [183, 13, 179, 11], [183, 14, 179, 12], [184, 4, 180, 2], [184, 5, 180, 3], [185, 4, 182, 2], [185, 10, 182, 8, "handleQuickAction"], [185, 27, 182, 25], [185, 30, 182, 29, "action"], [185, 36, 182, 35], [185, 40, 182, 40], [186, 6, 183, 4, "Haptics"], [186, 13, 183, 11], [186, 14, 183, 12, "impactAsync"], [186, 25, 183, 23], [186, 26, 183, 24, "Haptics"], [186, 33, 183, 31], [186, 34, 183, 32, "ImpactFeedbackStyle"], [186, 53, 183, 51], [186, 54, 183, 52, "Light"], [186, 59, 183, 57], [186, 60, 183, 58], [187, 6, 184, 4, "handleSendMessage"], [187, 23, 184, 21], [187, 24, 184, 22, "action"], [187, 30, 184, 28], [187, 31, 184, 29], [188, 4, 185, 2], [188, 5, 185, 3], [189, 4, 187, 2], [189, 10, 187, 8, "toggleVoiceMode"], [189, 25, 187, 23], [189, 28, 187, 26], [189, 34, 187, 26, "toggleVoiceMode"], [189, 35, 187, 26], [189, 40, 187, 38], [190, 6, 188, 4], [190, 10, 188, 8], [190, 11, 188, 9, "voiceMode"], [190, 20, 188, 18], [190, 24, 188, 22], [190, 25, 188, 23, "hasPermission"], [190, 38, 188, 36], [190, 40, 188, 38], [191, 8, 189, 6, "<PERSON><PERSON>"], [191, 22, 189, 11], [191, 23, 189, 12, "alert"], [191, 28, 189, 17], [191, 29, 190, 8], [191, 50, 190, 29], [191, 52, 191, 8], [191, 102, 191, 58], [191, 104, 192, 8], [191, 105, 193, 10], [192, 10, 193, 12, "text"], [192, 14, 193, 16], [192, 16, 193, 18], [193, 8, 193, 27], [193, 9, 193, 28], [193, 11, 194, 10], [194, 10, 195, 12, "text"], [194, 14, 195, 16], [194, 16, 195, 18], [194, 34, 195, 36], [195, 10, 196, 12, "onPress"], [195, 17, 196, 19], [195, 19, 196, 21], [195, 25, 196, 21, "onPress"], [195, 26, 196, 21], [195, 31, 196, 33], [196, 12, 197, 14], [196, 18, 197, 20], [197, 14, 197, 22, "granted"], [198, 12, 197, 30], [198, 13, 197, 31], [198, 16, 197, 34], [198, 22, 197, 40], [198, 26, 197, 40, "requestRecordingPermissionsAsync"], [198, 69, 197, 72], [198, 71, 197, 73], [198, 72, 197, 74], [199, 12, 198, 14], [199, 16, 198, 18, "granted"], [199, 23, 198, 25], [199, 25, 198, 27], [200, 14, 199, 16, "setHasPermission"], [200, 30, 199, 32], [200, 31, 199, 33], [200, 35, 199, 37], [200, 36, 199, 38], [201, 14, 200, 16, "setVoiceMode"], [201, 26, 200, 28], [201, 27, 200, 29], [201, 31, 200, 33], [201, 32, 200, 34], [202, 14, 201, 16], [203, 14, 202, 16], [203, 20, 202, 22, "startVoiceSession"], [203, 37, 202, 39], [203, 38, 202, 40], [203, 39, 202, 41], [204, 12, 203, 14], [205, 10, 204, 12], [206, 8, 205, 10], [206, 9, 205, 11], [206, 10, 207, 6], [206, 11, 207, 7], [207, 8, 208, 6], [208, 6, 209, 4], [209, 6, 211, 4, "Haptics"], [209, 13, 211, 11], [209, 14, 211, 12, "impactAsync"], [209, 25, 211, 23], [209, 26, 211, 24, "Haptics"], [209, 33, 211, 31], [209, 34, 211, 32, "ImpactFeedbackStyle"], [209, 53, 211, 51], [209, 54, 211, 52, "Light"], [209, 59, 211, 57], [209, 60, 211, 58], [210, 6, 213, 4], [210, 10, 213, 8], [210, 11, 213, 9, "voiceMode"], [210, 20, 213, 18], [210, 22, 213, 20], [211, 8, 214, 6], [212, 8, 215, 6, "setVoiceMode"], [212, 20, 215, 18], [212, 21, 215, 19], [212, 25, 215, 23], [212, 26, 215, 24], [213, 8, 216, 6], [213, 12, 216, 10, "hasPermission"], [213, 25, 216, 23], [213, 27, 216, 25], [214, 10, 217, 8], [214, 16, 217, 14, "startVoiceSession"], [214, 33, 217, 31], [214, 34, 217, 32], [214, 35, 217, 33], [215, 8, 218, 6], [216, 6, 219, 4], [216, 7, 219, 5], [216, 13, 219, 11], [217, 8, 220, 6], [218, 8, 221, 6, "setVoiceMode"], [218, 20, 221, 18], [218, 21, 221, 19], [218, 26, 221, 24], [218, 27, 221, 25], [219, 8, 222, 6], [219, 12, 222, 10, "recorderState"], [219, 25, 222, 23], [219, 26, 222, 24, "isRecording"], [219, 37, 222, 35], [219, 39, 222, 37], [220, 10, 223, 8], [220, 16, 223, 14, "recorder"], [220, 24, 223, 22], [220, 25, 223, 23, "stop"], [220, 29, 223, 27], [220, 30, 223, 28], [220, 31, 223, 29], [221, 8, 224, 6], [222, 8, 225, 6, "setIsMuted"], [222, 18, 225, 16], [222, 19, 225, 17], [222, 24, 225, 22], [222, 25, 225, 23], [222, 26, 225, 24], [222, 27, 225, 25], [223, 6, 226, 4], [224, 4, 227, 2], [224, 5, 227, 3], [225, 4, 229, 2], [225, 10, 229, 8, "startVoiceSession"], [225, 27, 229, 25], [225, 30, 229, 28], [225, 36, 229, 28, "startVoiceSession"], [225, 37, 229, 28], [225, 42, 229, 40], [226, 6, 230, 4], [226, 10, 230, 8], [226, 11, 230, 9, "hasPermission"], [226, 24, 230, 22], [226, 26, 230, 24], [227, 6, 232, 4], [227, 10, 232, 8], [228, 8, 233, 6], [228, 14, 233, 12, "recorder"], [228, 22, 233, 20], [228, 23, 233, 21, "prepareToRecordAsync"], [228, 43, 233, 41], [228, 44, 233, 42], [228, 45, 233, 43], [229, 8, 234, 6, "recorder"], [229, 16, 234, 14], [229, 17, 234, 15, "record"], [229, 23, 234, 21], [229, 24, 234, 22], [229, 25, 234, 23], [230, 8, 235, 6, "setIsMuted"], [230, 18, 235, 16], [230, 19, 235, 17], [230, 24, 235, 22], [230, 25, 235, 23], [230, 26, 235, 24], [230, 27, 235, 25], [231, 8, 236, 6, "Haptics"], [231, 15, 236, 13], [231, 16, 236, 14, "impactAsync"], [231, 27, 236, 25], [231, 28, 236, 26, "Haptics"], [231, 35, 236, 33], [231, 36, 236, 34, "ImpactFeedbackStyle"], [231, 55, 236, 53], [231, 56, 236, 54, "Medium"], [231, 62, 236, 60], [231, 63, 236, 61], [232, 6, 237, 4], [232, 7, 237, 5], [232, 8, 237, 6], [232, 15, 237, 13, "error"], [232, 20, 237, 18], [232, 22, 237, 20], [233, 8, 238, 6, "console"], [233, 15, 238, 13], [233, 16, 238, 14, "error"], [233, 21, 238, 19], [233, 22, 238, 20], [233, 53, 238, 51], [233, 55, 238, 53, "error"], [233, 60, 238, 58], [233, 61, 238, 59], [234, 8, 239, 6, "<PERSON><PERSON>"], [234, 22, 239, 11], [234, 23, 239, 12, "alert"], [234, 28, 239, 17], [234, 29, 239, 18], [234, 36, 239, 25], [234, 38, 239, 27], [234, 69, 239, 58], [234, 70, 239, 59], [235, 6, 240, 4], [236, 4, 241, 2], [236, 5, 241, 3], [237, 4, 243, 2], [237, 10, 243, 8, "stopVoiceSession"], [237, 26, 243, 24], [237, 29, 243, 27], [237, 35, 243, 27, "stopVoiceSession"], [237, 36, 243, 27], [237, 41, 243, 39], [238, 6, 244, 4], [238, 10, 244, 8], [239, 8, 245, 6], [239, 12, 245, 10, "recorderState"], [239, 25, 245, 23], [239, 26, 245, 24, "isRecording"], [239, 37, 245, 35], [239, 39, 245, 37], [240, 10, 246, 8], [240, 16, 246, 14, "recorder"], [240, 24, 246, 22], [240, 25, 246, 23, "stop"], [240, 29, 246, 27], [240, 30, 246, 28], [240, 31, 246, 29], [241, 10, 247, 8, "Haptics"], [241, 17, 247, 15], [241, 18, 247, 16, "impactAsync"], [241, 29, 247, 27], [241, 30, 247, 28, "Haptics"], [241, 37, 247, 35], [241, 38, 247, 36, "ImpactFeedbackStyle"], [241, 57, 247, 55], [241, 58, 247, 56, "Medium"], [241, 64, 247, 62], [241, 65, 247, 63], [242, 8, 248, 6], [243, 6, 249, 4], [243, 7, 249, 5], [243, 8, 249, 6], [243, 15, 249, 13, "error"], [243, 20, 249, 18], [243, 22, 249, 20], [244, 8, 250, 6, "console"], [244, 15, 250, 13], [244, 16, 250, 14, "error"], [244, 21, 250, 19], [244, 22, 250, 20], [244, 53, 250, 51], [244, 55, 250, 53, "error"], [244, 60, 250, 58], [244, 61, 250, 59], [245, 6, 251, 4], [246, 4, 252, 2], [246, 5, 252, 3], [247, 4, 254, 2], [247, 10, 254, 8, "handleDictation"], [247, 25, 254, 23], [247, 28, 254, 26], [247, 34, 254, 26, "handleDictation"], [247, 35, 254, 26], [247, 40, 254, 38], [248, 6, 255, 4], [248, 10, 255, 8], [248, 11, 255, 9, "hasPermission"], [248, 24, 255, 22], [248, 26, 255, 24], [249, 8, 256, 6, "<PERSON><PERSON>"], [249, 22, 256, 11], [249, 23, 256, 12, "alert"], [249, 28, 256, 17], [249, 29, 257, 8], [249, 50, 257, 29], [249, 52, 258, 8], [249, 102, 259, 6], [249, 103, 259, 7], [250, 8, 260, 6], [251, 6, 261, 4], [252, 6, 263, 4], [252, 10, 263, 8], [253, 8, 264, 6], [253, 12, 264, 10, "isDictating"], [253, 23, 264, 21], [253, 25, 264, 23], [254, 10, 265, 8], [254, 16, 265, 14, "recorder"], [254, 24, 265, 22], [254, 25, 265, 23, "stop"], [254, 29, 265, 27], [254, 30, 265, 28], [254, 31, 265, 29], [255, 10, 266, 8], [255, 16, 266, 14, "mockTranscript"], [255, 30, 266, 28], [255, 33, 266, 31], [255, 62, 266, 60], [255, 63, 266, 61], [255, 64, 266, 62], [256, 10, 267, 8, "setInputText"], [256, 22, 267, 20], [256, 23, 267, 21, "inputText"], [256, 32, 267, 30], [256, 35, 267, 33, "mockTranscript"], [256, 49, 267, 47], [256, 50, 267, 48], [257, 10, 268, 8, "setIsDictating"], [257, 24, 268, 22], [257, 25, 268, 23], [257, 30, 268, 28], [257, 31, 268, 29], [258, 8, 269, 6], [258, 9, 269, 7], [258, 15, 269, 13], [259, 10, 270, 8], [259, 16, 270, 14, "recorder"], [259, 24, 270, 22], [259, 25, 270, 23, "prepareToRecordAsync"], [259, 45, 270, 43], [259, 46, 270, 44], [259, 47, 270, 45], [260, 10, 271, 8, "recorder"], [260, 18, 271, 16], [260, 19, 271, 17, "record"], [260, 25, 271, 23], [260, 26, 271, 24], [260, 27, 271, 25], [261, 10, 272, 8, "setIsDictating"], [261, 24, 272, 22], [261, 25, 272, 23], [261, 29, 272, 27], [261, 30, 272, 28], [262, 10, 273, 8, "Haptics"], [262, 17, 273, 15], [262, 18, 273, 16, "impactAsync"], [262, 29, 273, 27], [262, 30, 273, 28, "Haptics"], [262, 37, 273, 35], [262, 38, 273, 36, "ImpactFeedbackStyle"], [262, 57, 273, 55], [262, 58, 273, 56, "Medium"], [262, 64, 273, 62], [262, 65, 273, 63], [263, 8, 274, 6], [264, 6, 275, 4], [264, 7, 275, 5], [264, 8, 275, 6], [264, 15, 275, 13, "error"], [264, 20, 275, 18], [264, 22, 275, 20], [265, 8, 276, 6, "console"], [265, 15, 276, 13], [265, 16, 276, 14, "error"], [265, 21, 276, 19], [265, 22, 276, 20], [265, 45, 276, 43], [265, 47, 276, 45, "error"], [265, 52, 276, 50], [265, 53, 276, 51], [266, 8, 277, 6, "<PERSON><PERSON>"], [266, 22, 277, 11], [266, 23, 277, 12, "alert"], [266, 28, 277, 17], [266, 29, 277, 18], [266, 36, 277, 25], [266, 38, 277, 27], [266, 65, 277, 54], [266, 66, 277, 55], [267, 8, 278, 6, "setIsDictating"], [267, 22, 278, 20], [267, 23, 278, 21], [267, 28, 278, 26], [267, 29, 278, 27], [268, 6, 279, 4], [269, 4, 280, 2], [269, 5, 280, 3], [270, 4, 282, 2], [270, 10, 282, 8, "handleLongPress"], [270, 25, 282, 23], [270, 28, 282, 27, "message"], [270, 35, 282, 34], [270, 39, 282, 39], [271, 6, 283, 4, "setLongPressedMessage"], [271, 27, 283, 25], [271, 28, 283, 26, "message"], [271, 35, 283, 33], [271, 36, 283, 34], [272, 6, 284, 4, "setIsContextMenuVisible"], [272, 29, 284, 27], [272, 30, 284, 28], [272, 34, 284, 32], [272, 35, 284, 33], [273, 4, 285, 2], [273, 5, 285, 3], [274, 4, 287, 2], [274, 10, 287, 8, "handleCopyMessage"], [274, 27, 287, 25], [274, 30, 287, 28, "handleCopyMessage"], [274, 31, 287, 28], [274, 36, 287, 34], [275, 6, 288, 4], [275, 10, 288, 8, "longPressedMessage"], [275, 28, 288, 26], [275, 30, 288, 28], [276, 8, 289, 6, "Clipboard"], [276, 26, 289, 15], [276, 27, 289, 16, "setString"], [276, 36, 289, 25], [276, 37, 289, 26, "longPressedMessage"], [276, 55, 289, 44], [276, 56, 289, 45, "content"], [276, 63, 289, 52], [276, 64, 289, 53], [277, 8, 290, 6, "setIsContextMenuVisible"], [277, 31, 290, 29], [277, 32, 290, 30], [277, 37, 290, 35], [277, 38, 290, 36], [278, 8, 291, 6, "setLongPressedMessage"], [278, 29, 291, 27], [278, 30, 291, 28], [278, 34, 291, 32], [278, 35, 291, 33], [279, 6, 292, 4], [280, 4, 293, 2], [280, 5, 293, 3], [281, 4, 295, 2], [281, 10, 295, 8, "handleListenToMessage"], [281, 31, 295, 29], [281, 34, 295, 32, "handleListenToMessage"], [281, 35, 295, 32], [281, 40, 295, 38], [282, 6, 296, 4], [282, 10, 296, 8, "longPressedMessage"], [282, 28, 296, 26], [282, 30, 296, 28], [283, 8, 297, 6], [284, 8, 298, 6, "<PERSON><PERSON>"], [284, 22, 298, 11], [284, 23, 298, 12, "alert"], [284, 28, 298, 17], [284, 29, 298, 18], [284, 51, 298, 40], [284, 53, 298, 42, "longPressedMessage"], [284, 71, 298, 60], [284, 72, 298, 61, "content"], [284, 79, 298, 68], [284, 80, 298, 69], [285, 8, 299, 6, "setIsContextMenuVisible"], [285, 31, 299, 29], [285, 32, 299, 30], [285, 37, 299, 35], [285, 38, 299, 36], [286, 8, 300, 6, "setLongPressedMessage"], [286, 29, 300, 27], [286, 30, 300, 28], [286, 34, 300, 32], [286, 35, 300, 33], [287, 6, 301, 4], [288, 4, 302, 2], [288, 5, 302, 3], [289, 4, 304, 2], [289, 10, 304, 8, "handleMute"], [289, 20, 304, 18], [289, 23, 304, 21], [289, 29, 304, 21, "handleMute"], [289, 30, 304, 21], [289, 35, 304, 33], [290, 6, 305, 4], [290, 10, 305, 8], [290, 11, 305, 9, "hasPermission"], [290, 24, 305, 22], [290, 26, 305, 24], [291, 6, 307, 4], [291, 10, 307, 8], [292, 8, 308, 6], [292, 12, 308, 10, "isMuted"], [292, 19, 308, 17], [292, 21, 308, 19], [293, 10, 309, 8], [294, 10, 310, 8], [294, 14, 310, 12], [294, 15, 310, 13, "recorderState"], [294, 28, 310, 26], [294, 29, 310, 27, "isRecording"], [294, 40, 310, 38], [294, 42, 310, 40], [295, 12, 311, 10], [295, 18, 311, 16, "recorder"], [295, 26, 311, 24], [295, 27, 311, 25, "prepareToRecordAsync"], [295, 47, 311, 45], [295, 48, 311, 46], [295, 49, 311, 47], [296, 12, 312, 10, "recorder"], [296, 20, 312, 18], [296, 21, 312, 19, "record"], [296, 27, 312, 25], [296, 28, 312, 26], [296, 29, 312, 27], [297, 10, 313, 8], [298, 10, 314, 8, "setIsMuted"], [298, 20, 314, 18], [298, 21, 314, 19], [298, 26, 314, 24], [298, 27, 314, 25], [299, 10, 315, 8, "Haptics"], [299, 17, 315, 15], [299, 18, 315, 16, "impactAsync"], [299, 29, 315, 27], [299, 30, 315, 28, "Haptics"], [299, 37, 315, 35], [299, 38, 315, 36, "ImpactFeedbackStyle"], [299, 57, 315, 55], [299, 58, 315, 56, "Light"], [299, 63, 315, 61], [299, 64, 315, 62], [300, 8, 316, 6], [300, 9, 316, 7], [300, 15, 316, 13], [301, 10, 317, 8], [302, 10, 318, 8], [302, 14, 318, 12, "recorderState"], [302, 27, 318, 25], [302, 28, 318, 26, "isRecording"], [302, 39, 318, 37], [302, 41, 318, 39], [303, 12, 319, 10], [303, 18, 319, 16, "recorder"], [303, 26, 319, 24], [303, 27, 319, 25, "stop"], [303, 31, 319, 29], [303, 32, 319, 30], [303, 33, 319, 31], [304, 10, 320, 8], [305, 10, 321, 8, "setIsMuted"], [305, 20, 321, 18], [305, 21, 321, 19], [305, 25, 321, 23], [305, 26, 321, 24], [306, 10, 322, 8, "Haptics"], [306, 17, 322, 15], [306, 18, 322, 16, "impactAsync"], [306, 29, 322, 27], [306, 30, 322, 28, "Haptics"], [306, 37, 322, 35], [306, 38, 322, 36, "ImpactFeedbackStyle"], [306, 57, 322, 55], [306, 58, 322, 56, "Light"], [306, 63, 322, 61], [306, 64, 322, 62], [307, 8, 323, 6], [308, 6, 324, 4], [308, 7, 324, 5], [308, 8, 324, 6], [308, 15, 324, 13, "error"], [308, 20, 324, 18], [308, 22, 324, 20], [309, 8, 325, 6, "console"], [309, 15, 325, 13], [309, 16, 325, 14, "error"], [309, 21, 325, 19], [309, 22, 325, 20], [309, 47, 325, 45], [309, 49, 325, 47, "error"], [309, 54, 325, 52], [309, 55, 325, 53], [310, 6, 326, 4], [311, 4, 327, 2], [311, 5, 327, 3], [312, 4, 329, 2], [312, 10, 329, 8, "handleDone"], [312, 20, 329, 18], [312, 23, 329, 21], [312, 29, 329, 21, "handleDone"], [312, 30, 329, 21], [312, 35, 329, 33], [313, 6, 330, 4], [313, 10, 330, 8], [313, 11, 330, 9, "currentSessionId"], [313, 27, 330, 25], [313, 29, 330, 27], [314, 8, 331, 6, "<PERSON><PERSON>"], [314, 22, 331, 11], [314, 23, 331, 12, "alert"], [314, 28, 331, 17], [314, 29, 331, 18], [314, 41, 331, 30], [314, 43, 331, 32], [314, 71, 331, 60], [314, 72, 331, 61], [315, 8, 332, 6], [316, 6, 333, 4], [317, 6, 335, 4], [317, 10, 335, 8], [318, 8, 336, 6], [319, 8, 337, 6], [319, 12, 337, 10, "recorderState"], [319, 25, 337, 23], [319, 26, 337, 24, "isRecording"], [319, 37, 337, 35], [319, 39, 337, 37], [320, 10, 338, 8], [320, 16, 338, 14, "recorder"], [320, 24, 338, 22], [320, 25, 338, 23, "stop"], [320, 29, 338, 27], [320, 30, 338, 28], [320, 31, 338, 29], [321, 8, 339, 6], [323, 8, 341, 6], [324, 8, 342, 6], [324, 14, 342, 12, "duration"], [324, 22, 342, 20], [324, 25, 342, 23, "sessionStartTime"], [324, 41, 342, 39], [324, 44, 343, 10, "Math"], [324, 48, 343, 14], [324, 49, 343, 15, "floor"], [324, 54, 343, 20], [324, 55, 343, 21], [324, 56, 343, 22], [324, 60, 343, 26, "Date"], [324, 64, 343, 30], [324, 65, 343, 31], [324, 66, 343, 32], [324, 69, 343, 35, "sessionStartTime"], [324, 85, 343, 51], [324, 89, 343, 55], [324, 93, 343, 59], [324, 94, 343, 60], [324, 97, 344, 10], [324, 98, 344, 11], [326, 8, 346, 6], [327, 8, 347, 6], [327, 14, 347, 12, "firstUserMessage"], [327, 30, 347, 28], [327, 33, 347, 31, "messages"], [327, 41, 347, 39], [327, 42, 347, 40, "find"], [327, 46, 347, 44], [327, 47, 347, 45, "msg"], [327, 50, 347, 48], [327, 54, 347, 52, "msg"], [327, 57, 347, 55], [327, 58, 347, 56, "role"], [327, 62, 347, 60], [327, 67, 347, 65], [327, 73, 347, 71], [327, 74, 347, 72], [328, 8, 348, 6], [328, 14, 348, 12, "sessionTitle"], [328, 26, 348, 24], [328, 29, 348, 27, "firstUserMessage"], [328, 45, 348, 43], [328, 48, 349, 10, "firstUserMessage"], [328, 64, 349, 26], [328, 65, 349, 27, "content"], [328, 72, 349, 34], [328, 73, 349, 35, "slice"], [328, 78, 349, 40], [328, 79, 349, 41], [328, 80, 349, 42], [328, 82, 349, 44], [328, 84, 349, 46], [328, 85, 349, 47], [328, 89, 349, 51, "firstUserMessage"], [328, 105, 349, 67], [328, 106, 349, 68, "content"], [328, 113, 349, 75], [328, 114, 349, 76, "length"], [328, 120, 349, 82], [328, 123, 349, 85], [328, 125, 349, 87], [328, 128, 349, 90], [328, 133, 349, 95], [328, 136, 349, 98], [328, 138, 349, 100], [328, 139, 349, 101], [328, 142, 350, 10], [328, 165, 350, 33], [330, 8, 352, 6], [331, 8, 353, 6], [331, 14, 353, 12, "response"], [331, 22, 353, 20], [331, 25, 353, 23], [331, 31, 353, 29, "fetch"], [331, 36, 353, 34], [331, 37, 353, 35], [331, 54, 353, 52, "currentSessionId"], [331, 70, 353, 68], [331, 72, 353, 70], [331, 74, 353, 72], [332, 10, 354, 8, "method"], [332, 16, 354, 14], [332, 18, 354, 16], [332, 23, 354, 21], [333, 10, 355, 8, "headers"], [333, 17, 355, 15], [333, 19, 355, 17], [334, 12, 355, 19], [334, 26, 355, 33], [334, 28, 355, 35], [335, 10, 355, 54], [335, 11, 355, 55], [336, 10, 356, 8, "body"], [336, 14, 356, 12], [336, 16, 356, 14, "JSON"], [336, 20, 356, 18], [336, 21, 356, 19, "stringify"], [336, 30, 356, 28], [336, 31, 356, 29], [337, 12, 357, 10, "status"], [337, 18, 357, 16], [337, 20, 357, 18], [337, 31, 357, 29], [338, 12, 358, 10, "title"], [338, 17, 358, 15], [338, 19, 358, 17, "sessionTitle"], [338, 31, 358, 29], [339, 12, 359, 10, "duration_seconds"], [339, 28, 359, 26], [339, 30, 359, 28, "duration"], [339, 38, 359, 36], [340, 12, 360, 10, "summary"], [340, 19, 360, 17], [340, 21, 360, 19], [340, 47, 360, 45, "messages"], [340, 55, 360, 53], [340, 56, 360, 54, "length"], [340, 62, 360, 60], [341, 10, 361, 8], [341, 11, 361, 9], [342, 8, 362, 6], [342, 9, 362, 7], [342, 10, 362, 8], [343, 8, 364, 6], [343, 12, 364, 10, "response"], [343, 20, 364, 18], [343, 21, 364, 19, "ok"], [343, 23, 364, 21], [343, 25, 364, 23], [344, 10, 365, 8], [345, 10, 366, 8, "queryClient"], [345, 21, 366, 19], [345, 22, 366, 20, "invalidateQueries"], [345, 39, 366, 37], [345, 40, 366, 38], [346, 12, 366, 40, "query<PERSON><PERSON>"], [346, 20, 366, 48], [346, 22, 366, 50], [346, 23, 366, 51], [346, 33, 366, 61], [347, 10, 366, 63], [347, 11, 366, 64], [347, 12, 366, 65], [349, 10, 368, 8], [350, 10, 369, 8, "router"], [350, 16, 369, 14], [350, 17, 369, 15, "push"], [350, 21, 369, 19], [350, 22, 369, 20], [350, 34, 369, 32, "currentSessionId"], [350, 50, 369, 48], [350, 52, 369, 50], [350, 53, 369, 51], [352, 10, 371, 8], [353, 10, 372, 8, "setCurrentSessionId"], [353, 29, 372, 27], [353, 30, 372, 28], [353, 34, 372, 32], [353, 35, 372, 33], [354, 10, 373, 8, "setSessionStartTime"], [354, 29, 373, 27], [354, 30, 373, 28], [354, 34, 373, 32], [354, 35, 373, 33], [355, 10, 374, 8, "setMessages"], [355, 21, 374, 19], [355, 22, 374, 20], [355, 24, 374, 22], [355, 25, 374, 23], [356, 10, 375, 8, "setQuickActions"], [356, 25, 375, 23], [356, 26, 375, 24], [356, 28, 375, 26], [356, 29, 375, 27], [357, 10, 376, 8, "setIsFirstTime"], [357, 24, 376, 22], [357, 25, 376, 23], [357, 29, 376, 27], [357, 30, 376, 28], [358, 10, 377, 8, "setIsMuted"], [358, 20, 377, 18], [358, 21, 377, 19], [358, 26, 377, 24], [358, 27, 377, 25], [359, 10, 379, 8, "Haptics"], [359, 17, 379, 15], [359, 18, 379, 16, "impactAsync"], [359, 29, 379, 27], [359, 30, 379, 28, "Haptics"], [359, 37, 379, 35], [359, 38, 379, 36, "ImpactFeedbackStyle"], [359, 57, 379, 55], [359, 58, 379, 56, "Medium"], [359, 64, 379, 62], [359, 65, 379, 63], [360, 8, 380, 6], [360, 9, 380, 7], [360, 15, 380, 13], [361, 10, 381, 8], [361, 16, 381, 14], [361, 20, 381, 18, "Error"], [361, 25, 381, 23], [361, 26, 381, 24], [361, 50, 381, 48], [361, 51, 381, 49], [362, 8, 382, 6], [363, 6, 383, 4], [363, 7, 383, 5], [363, 8, 383, 6], [363, 15, 383, 13, "error"], [363, 20, 383, 18], [363, 22, 383, 20], [364, 8, 384, 6, "console"], [364, 15, 384, 13], [364, 16, 384, 14, "error"], [364, 21, 384, 19], [364, 22, 384, 20], [364, 45, 384, 43], [364, 47, 384, 45, "error"], [364, 52, 384, 50], [364, 53, 384, 51], [365, 8, 385, 6, "<PERSON><PERSON>"], [365, 22, 385, 11], [365, 23, 385, 12, "alert"], [365, 28, 385, 17], [365, 29, 385, 18], [365, 36, 385, 25], [365, 38, 385, 27], [365, 81, 385, 70], [365, 82, 385, 71], [366, 6, 386, 4], [367, 4, 387, 2], [367, 5, 387, 3], [369, 4, 390, 2], [370, 4, 391, 2], [370, 8, 391, 2, "useEffect"], [370, 24, 391, 11], [370, 26, 391, 12], [370, 32, 391, 18], [371, 6, 392, 4], [371, 10, 392, 8, "scrollViewRef"], [371, 23, 392, 21], [371, 24, 392, 22, "current"], [371, 31, 392, 29], [371, 33, 392, 31], [372, 8, 393, 6, "scrollViewRef"], [372, 21, 393, 19], [372, 22, 393, 20, "current"], [372, 29, 393, 27], [372, 30, 393, 28, "scrollToEnd"], [372, 41, 393, 39], [372, 42, 393, 40], [373, 10, 393, 42, "animated"], [373, 18, 393, 50], [373, 20, 393, 52], [374, 8, 393, 57], [374, 9, 393, 58], [374, 10, 393, 59], [375, 6, 394, 4], [376, 4, 395, 2], [376, 5, 395, 3], [376, 7, 395, 5], [376, 8, 395, 6, "messages"], [376, 16, 395, 14], [376, 17, 395, 15], [376, 18, 395, 16], [377, 4, 397, 2], [377, 8, 397, 6], [377, 9, 397, 7, "fontsLoaded"], [377, 20, 397, 18], [377, 22, 397, 20], [378, 6, 398, 4], [378, 13, 398, 11], [378, 17, 398, 15], [379, 4, 399, 2], [381, 4, 401, 2], [382, 4, 402, 2], [382, 8, 402, 6, "isFirstTime"], [382, 19, 402, 17], [382, 21, 402, 19], [383, 6, 403, 4], [383, 26, 404, 6], [383, 30, 404, 6, "_jsxDevRuntime"], [383, 44, 404, 6], [383, 45, 404, 6, "jsxDEV"], [383, 51, 404, 6], [383, 53, 404, 7, "_View"], [383, 58, 404, 7], [383, 59, 404, 7, "default"], [383, 66, 404, 11], [384, 8, 404, 12, "style"], [384, 13, 404, 17], [384, 15, 404, 19], [385, 10, 404, 21, "flex"], [385, 14, 404, 25], [385, 16, 404, 27], [385, 17, 404, 28], [386, 10, 404, 30, "backgroundColor"], [386, 25, 404, 45], [386, 27, 404, 47, "colors"], [386, 33, 404, 53], [386, 34, 404, 54, "background"], [387, 8, 404, 65], [387, 9, 404, 67], [388, 8, 404, 67, "children"], [388, 16, 404, 67], [388, 31, 405, 8], [388, 35, 405, 8, "_jsxDevRuntime"], [388, 49, 405, 8], [388, 50, 405, 8, "jsxDEV"], [388, 56, 405, 8], [388, 58, 405, 9, "_View"], [388, 63, 405, 9], [388, 64, 405, 9, "default"], [388, 71, 405, 13], [389, 10, 406, 10, "style"], [389, 15, 406, 15], [389, 17, 406, 17], [390, 12, 407, 12, "flex"], [390, 16, 407, 16], [390, 18, 407, 18], [390, 19, 407, 19], [391, 12, 408, 12, "paddingTop"], [391, 22, 408, 22], [391, 24, 408, 24, "insets"], [391, 30, 408, 30], [391, 31, 408, 31, "top"], [391, 34, 408, 34], [391, 37, 408, 37], [391, 39, 408, 39], [392, 12, 409, 12, "paddingHorizontal"], [392, 29, 409, 29], [392, 31, 409, 31], [392, 33, 409, 33], [393, 12, 410, 12, "paddingBottom"], [393, 25, 410, 25], [393, 27, 410, 27, "insets"], [393, 33, 410, 33], [393, 34, 410, 34, "bottom"], [393, 40, 410, 40], [393, 43, 410, 43], [393, 45, 410, 45], [394, 12, 411, 12, "alignItems"], [394, 22, 411, 22], [394, 24, 411, 24], [394, 32, 411, 32], [395, 12, 412, 12, "justifyContent"], [395, 26, 412, 26], [395, 28, 412, 28], [396, 10, 413, 10], [396, 11, 413, 12], [397, 10, 413, 12, "children"], [397, 18, 413, 12], [397, 34, 415, 10], [397, 38, 415, 10, "_jsxDevRuntime"], [397, 52, 415, 10], [397, 53, 415, 10, "jsxDEV"], [397, 59, 415, 10], [397, 61, 415, 11, "_View"], [397, 66, 415, 11], [397, 67, 415, 11, "default"], [397, 74, 415, 15], [398, 12, 416, 12, "style"], [398, 17, 416, 17], [398, 19, 416, 19], [399, 14, 417, 14, "width"], [399, 19, 417, 19], [399, 21, 417, 21], [399, 24, 417, 24], [400, 14, 418, 14, "height"], [400, 20, 418, 20], [400, 22, 418, 22], [400, 25, 418, 25], [401, 14, 419, 14, "borderRadius"], [401, 26, 419, 26], [401, 28, 419, 28], [401, 30, 419, 30], [402, 14, 420, 14, "backgroundColor"], [402, 29, 420, 29], [402, 31, 420, 31, "colors"], [402, 37, 420, 37], [402, 38, 420, 38, "primaryUltraLight"], [402, 55, 420, 55], [403, 14, 421, 14, "alignItems"], [403, 24, 421, 24], [403, 26, 421, 26], [403, 34, 421, 34], [404, 14, 422, 14, "justifyContent"], [404, 28, 422, 28], [404, 30, 422, 30], [404, 38, 422, 38], [405, 14, 423, 14, "marginBottom"], [405, 26, 423, 26], [405, 28, 423, 28], [406, 12, 424, 12], [406, 13, 424, 14], [407, 12, 424, 14, "children"], [407, 20, 424, 14], [407, 35, 425, 12], [407, 39, 425, 12, "_jsxDevRuntime"], [407, 53, 425, 12], [407, 54, 425, 12, "jsxDEV"], [407, 60, 425, 12], [407, 62, 425, 13, "_lucideReactNative"], [407, 80, 425, 13], [407, 81, 425, 13, "MessageSquare"], [407, 94, 425, 26], [408, 14, 425, 27, "size"], [408, 18, 425, 31], [408, 20, 425, 33], [408, 22, 425, 36], [409, 14, 425, 37, "color"], [409, 19, 425, 42], [409, 21, 425, 44, "colors"], [409, 27, 425, 50], [409, 28, 425, 51, "primary"], [410, 12, 425, 59], [411, 14, 425, 59, "fileName"], [411, 22, 425, 59], [411, 24, 425, 59, "_jsxFileName"], [411, 36, 425, 59], [412, 14, 425, 59, "lineNumber"], [412, 24, 425, 59], [413, 14, 425, 59, "columnNumber"], [413, 26, 425, 59], [414, 12, 425, 59], [414, 19, 425, 61], [415, 10, 425, 62], [416, 12, 425, 62, "fileName"], [416, 20, 425, 62], [416, 22, 425, 62, "_jsxFileName"], [416, 34, 425, 62], [417, 12, 425, 62, "lineNumber"], [417, 22, 425, 62], [418, 12, 425, 62, "columnNumber"], [418, 24, 425, 62], [419, 10, 425, 62], [419, 17, 426, 16], [419, 18, 426, 17], [419, 33, 429, 10], [419, 37, 429, 10, "_jsxDevRuntime"], [419, 51, 429, 10], [419, 52, 429, 10, "jsxDEV"], [419, 58, 429, 10], [419, 60, 429, 11, "_Text"], [419, 65, 429, 11], [419, 66, 429, 11, "default"], [419, 73, 429, 15], [420, 12, 430, 12, "style"], [420, 17, 430, 17], [420, 19, 430, 19], [421, 14, 431, 14, "fontSize"], [421, 22, 431, 22], [421, 24, 431, 24], [421, 26, 431, 26], [422, 14, 432, 14, "fontFamily"], [422, 24, 432, 24], [422, 26, 432, 26], [422, 47, 432, 47], [423, 14, 433, 14, "color"], [423, 19, 433, 19], [423, 21, 433, 21, "colors"], [423, 27, 433, 27], [423, 28, 433, 28, "text"], [423, 32, 433, 32], [424, 14, 434, 14, "textAlign"], [424, 23, 434, 23], [424, 25, 434, 25], [424, 33, 434, 33], [425, 14, 435, 14, "marginBottom"], [425, 26, 435, 26], [425, 28, 435, 28], [426, 12, 436, 12], [426, 13, 436, 14], [427, 12, 436, 14, "children"], [427, 20, 436, 14], [427, 22, 436, 15], [428, 10, 438, 10], [429, 12, 438, 10, "fileName"], [429, 20, 438, 10], [429, 22, 438, 10, "_jsxFileName"], [429, 34, 438, 10], [430, 12, 438, 10, "lineNumber"], [430, 22, 438, 10], [431, 12, 438, 10, "columnNumber"], [431, 24, 438, 10], [432, 10, 438, 10], [432, 17, 438, 16], [432, 18, 438, 17], [432, 33, 441, 10], [432, 37, 441, 10, "_jsxDevRuntime"], [432, 51, 441, 10], [432, 52, 441, 10, "jsxDEV"], [432, 58, 441, 10], [432, 60, 441, 11, "_Text"], [432, 65, 441, 11], [432, 66, 441, 11, "default"], [432, 73, 441, 15], [433, 12, 442, 12, "style"], [433, 17, 442, 17], [433, 19, 442, 19], [434, 14, 443, 14, "fontSize"], [434, 22, 443, 22], [434, 24, 443, 24], [434, 26, 443, 26], [435, 14, 444, 14, "fontFamily"], [435, 24, 444, 24], [435, 26, 444, 26], [435, 46, 444, 46], [436, 14, 445, 14, "color"], [436, 19, 445, 19], [436, 21, 445, 21, "colors"], [436, 27, 445, 27], [436, 28, 445, 28, "textSecondary"], [436, 41, 445, 41], [437, 14, 446, 14, "textAlign"], [437, 23, 446, 23], [437, 25, 446, 25], [437, 33, 446, 33], [438, 14, 447, 14, "lineHeight"], [438, 24, 447, 24], [438, 26, 447, 26], [438, 28, 447, 28], [439, 14, 448, 14, "marginBottom"], [439, 26, 448, 26], [439, 28, 448, 28], [440, 12, 449, 12], [440, 13, 449, 14], [441, 12, 449, 14, "children"], [441, 20, 449, 14], [441, 22, 449, 15], [442, 10, 452, 10], [443, 12, 452, 10, "fileName"], [443, 20, 452, 10], [443, 22, 452, 10, "_jsxFileName"], [443, 34, 452, 10], [444, 12, 452, 10, "lineNumber"], [444, 22, 452, 10], [445, 12, 452, 10, "columnNumber"], [445, 24, 452, 10], [446, 10, 452, 10], [446, 17, 452, 16], [446, 18, 452, 17], [446, 33, 455, 10], [446, 37, 455, 10, "_jsxDevRuntime"], [446, 51, 455, 10], [446, 52, 455, 10, "jsxDEV"], [446, 58, 455, 10], [446, 60, 455, 11, "_TouchableOpacity"], [446, 77, 455, 11], [446, 78, 455, 11, "default"], [446, 85, 455, 27], [447, 12, 456, 12, "style"], [447, 17, 456, 17], [447, 19, 456, 19], [448, 14, 457, 14, "backgroundColor"], [448, 29, 457, 29], [448, 31, 457, 31, "colors"], [448, 37, 457, 37], [448, 38, 457, 38, "primary"], [448, 45, 457, 45], [449, 14, 458, 14, "borderRadius"], [449, 26, 458, 26], [449, 28, 458, 28], [449, 30, 458, 30], [450, 14, 459, 14, "paddingHorizontal"], [450, 31, 459, 31], [450, 33, 459, 33], [450, 35, 459, 35], [451, 14, 460, 14, "paddingVertical"], [451, 29, 460, 29], [451, 31, 460, 31], [451, 33, 460, 33], [452, 14, 461, 14, "min<PERSON><PERSON><PERSON>"], [452, 22, 461, 22], [452, 24, 461, 24], [452, 27, 461, 27], [453, 14, 462, 14, "alignItems"], [453, 24, 462, 24], [453, 26, 462, 26], [454, 12, 463, 12], [454, 13, 463, 14], [455, 12, 464, 12, "onPress"], [455, 19, 464, 19], [455, 21, 464, 21, "handleStartBrainstorming"], [455, 45, 464, 46], [456, 12, 464, 46, "children"], [456, 20, 464, 46], [456, 35, 465, 12], [456, 39, 465, 12, "_jsxDevRuntime"], [456, 53, 465, 12], [456, 54, 465, 12, "jsxDEV"], [456, 60, 465, 12], [456, 62, 465, 13, "_Text"], [456, 67, 465, 13], [456, 68, 465, 13, "default"], [456, 75, 465, 17], [457, 14, 466, 14, "style"], [457, 19, 466, 19], [457, 21, 466, 21], [458, 16, 467, 16, "fontSize"], [458, 24, 467, 24], [458, 26, 467, 26], [458, 28, 467, 28], [459, 16, 468, 16, "fontFamily"], [459, 26, 468, 26], [459, 28, 468, 28], [459, 49, 468, 49], [460, 16, 469, 16, "color"], [460, 21, 469, 21], [460, 23, 469, 23, "colors"], [460, 29, 469, 29], [460, 30, 469, 30, "background"], [461, 14, 470, 14], [461, 15, 470, 16], [462, 14, 470, 16, "children"], [462, 22, 470, 16], [462, 24, 470, 17], [463, 12, 472, 12], [464, 14, 472, 12, "fileName"], [464, 22, 472, 12], [464, 24, 472, 12, "_jsxFileName"], [464, 36, 472, 12], [465, 14, 472, 12, "lineNumber"], [465, 24, 472, 12], [466, 14, 472, 12, "columnNumber"], [466, 26, 472, 12], [467, 12, 472, 12], [467, 19, 472, 18], [468, 10, 472, 19], [469, 12, 472, 19, "fileName"], [469, 20, 472, 19], [469, 22, 472, 19, "_jsxFileName"], [469, 34, 472, 19], [470, 12, 472, 19, "lineNumber"], [470, 22, 472, 19], [471, 12, 472, 19, "columnNumber"], [471, 24, 472, 19], [472, 10, 472, 19], [472, 17, 473, 28], [472, 18, 473, 29], [473, 8, 473, 29], [474, 10, 473, 29, "fileName"], [474, 18, 473, 29], [474, 20, 473, 29, "_jsxFileName"], [474, 32, 473, 29], [475, 10, 473, 29, "lineNumber"], [475, 20, 473, 29], [476, 10, 473, 29, "columnNumber"], [476, 22, 473, 29], [477, 8, 473, 29], [477, 15, 474, 14], [478, 6, 474, 15], [479, 8, 474, 15, "fileName"], [479, 16, 474, 15], [479, 18, 474, 15, "_jsxFileName"], [479, 30, 474, 15], [480, 8, 474, 15, "lineNumber"], [480, 18, 474, 15], [481, 8, 474, 15, "columnNumber"], [481, 20, 474, 15], [482, 6, 474, 15], [482, 13, 475, 12], [482, 14, 475, 13], [483, 4, 477, 2], [485, 4, 479, 2], [486, 4, 480, 2], [486, 24, 481, 4], [486, 28, 481, 4, "_jsxDevRuntime"], [486, 42, 481, 4], [486, 43, 481, 4, "jsxDEV"], [486, 49, 481, 4], [486, 51, 481, 5, "_View"], [486, 56, 481, 5], [486, 57, 481, 5, "default"], [486, 64, 481, 9], [487, 6, 481, 10, "style"], [487, 11, 481, 15], [487, 13, 481, 17], [488, 8, 481, 19, "flex"], [488, 12, 481, 23], [488, 14, 481, 25], [488, 15, 481, 26], [489, 8, 481, 28, "backgroundColor"], [489, 23, 481, 43], [489, 25, 481, 45, "colors"], [489, 31, 481, 51], [489, 32, 481, 52, "background"], [490, 6, 481, 63], [490, 7, 481, 65], [491, 6, 481, 65, "children"], [491, 14, 481, 65], [491, 30, 482, 6], [491, 34, 482, 6, "_jsxDevRuntime"], [491, 48, 482, 6], [491, 49, 482, 6, "jsxDEV"], [491, 55, 482, 6], [491, 57, 482, 7, "_Header"], [491, 64, 482, 7], [491, 65, 482, 7, "default"], [491, 72, 482, 13], [492, 8, 483, 8, "voiceMode"], [492, 17, 483, 17], [492, 19, 483, 19, "voiceMode"], [492, 28, 483, 29], [493, 8, 484, 8, "onToggleVoiceMode"], [493, 25, 484, 25], [493, 27, 484, 27, "toggleVoiceMode"], [493, 42, 484, 43], [494, 8, 485, 8, "onDone"], [494, 14, 485, 14], [494, 16, 485, 16, "handleDone"], [495, 6, 485, 27], [496, 8, 485, 27, "fileName"], [496, 16, 485, 27], [496, 18, 485, 27, "_jsxFileName"], [496, 30, 485, 27], [497, 8, 485, 27, "lineNumber"], [497, 18, 485, 27], [498, 8, 485, 27, "columnNumber"], [498, 20, 485, 27], [499, 6, 485, 27], [499, 13, 486, 7], [499, 14, 486, 8], [499, 16, 487, 7, "voiceMode"], [499, 25, 487, 16], [499, 41, 488, 8], [499, 45, 488, 8, "_jsxDevRuntime"], [499, 59, 488, 8], [499, 60, 488, 8, "jsxDEV"], [499, 66, 488, 8], [499, 68, 488, 9, "_VoiceMode"], [499, 78, 488, 9], [499, 79, 488, 9, "default"], [499, 86, 488, 18], [500, 8, 489, 10, "isRecording"], [500, 19, 489, 21], [500, 21, 489, 23, "recorderState"], [500, 34, 489, 36], [500, 35, 489, 37, "isRecording"], [500, 46, 489, 49], [501, 8, 490, 10, "hasPermission"], [501, 21, 490, 23], [501, 23, 490, 25, "hasPermission"], [501, 36, 490, 39], [502, 8, 491, 10, "isLoading"], [502, 17, 491, 19], [502, 19, 491, 21, "isLoading"], [502, 28, 491, 31], [503, 8, 492, 10, "transcript"], [503, 18, 492, 20], [503, 20, 492, 22, "transcript"], [503, 30, 492, 33], [504, 8, 493, 10, "isMuted"], [504, 15, 493, 17], [504, 17, 493, 19, "isMuted"], [504, 24, 493, 27], [505, 8, 494, 10, "onMute"], [505, 14, 494, 16], [505, 16, 494, 18, "handleMute"], [506, 6, 494, 29], [507, 8, 494, 29, "fileName"], [507, 16, 494, 29], [507, 18, 494, 29, "_jsxFileName"], [507, 30, 494, 29], [508, 8, 494, 29, "lineNumber"], [508, 18, 494, 29], [509, 8, 494, 29, "columnNumber"], [509, 20, 494, 29], [510, 6, 494, 29], [510, 13, 495, 9], [510, 14, 495, 10], [510, 30, 497, 8], [510, 34, 497, 8, "_jsxDevRuntime"], [510, 48, 497, 8], [510, 49, 497, 8, "jsxDEV"], [510, 55, 497, 8], [510, 57, 497, 9, "_KeyboardAvoidingAnimatedView"], [510, 86, 497, 9], [510, 87, 497, 9, "default"], [510, 94, 497, 37], [511, 8, 497, 38, "style"], [511, 13, 497, 43], [511, 15, 497, 45], [512, 10, 497, 47, "flex"], [512, 14, 497, 51], [512, 16, 497, 53], [513, 8, 497, 55], [513, 9, 497, 57], [514, 8, 497, 57, "children"], [514, 16, 497, 57], [514, 32, 499, 10], [514, 36, 499, 10, "_jsxDevRuntime"], [514, 50, 499, 10], [514, 51, 499, 10, "jsxDEV"], [514, 57, 499, 10], [514, 59, 499, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [514, 70, 499, 11], [514, 71, 499, 11, "default"], [514, 78, 499, 21], [515, 10, 500, 12, "ref"], [515, 13, 500, 15], [515, 15, 500, 17, "scrollViewRef"], [515, 28, 500, 31], [516, 10, 501, 12, "style"], [516, 15, 501, 17], [516, 17, 501, 19], [517, 12, 501, 21, "flex"], [517, 16, 501, 25], [517, 18, 501, 27], [518, 10, 501, 29], [518, 11, 501, 31], [519, 10, 502, 12, "contentContainerStyle"], [519, 31, 502, 33], [519, 33, 502, 35], [520, 12, 503, 14, "paddingHorizontal"], [520, 29, 503, 31], [520, 31, 503, 33], [520, 33, 503, 35], [521, 12, 504, 14, "paddingVertical"], [521, 27, 504, 29], [521, 29, 504, 31], [521, 31, 504, 33], [522, 12, 505, 14, "paddingBottom"], [522, 25, 505, 27], [522, 27, 505, 29], [522, 30, 505, 32], [522, 31, 505, 34], [523, 10, 506, 12], [523, 11, 506, 14], [524, 10, 507, 12, "showsVerticalScrollIndicator"], [524, 38, 507, 40], [524, 40, 507, 42], [524, 45, 507, 48], [525, 10, 507, 48, "children"], [525, 18, 507, 48], [525, 21, 508, 13, "messages"], [525, 29, 508, 21], [525, 30, 508, 22, "map"], [525, 33, 508, 25], [525, 34, 508, 26], [525, 35, 508, 27, "message"], [525, 42, 508, 34], [525, 44, 508, 36, "index"], [525, 49, 508, 41], [525, 67, 509, 14], [525, 71, 509, 14, "_jsxDevRuntime"], [525, 85, 509, 14], [525, 86, 509, 14, "jsxDEV"], [525, 92, 509, 14], [525, 94, 509, 15, "_MessageBubble"], [525, 108, 509, 15], [525, 109, 509, 15, "default"], [525, 116, 509, 28], [526, 12, 509, 41, "message"], [526, 19, 509, 48], [526, 21, 509, 50, "message"], [526, 28, 509, 58], [527, 12, 509, 59, "onLongPress"], [527, 23, 509, 70], [527, 25, 509, 72, "handleLongPress"], [528, 10, 509, 88], [528, 13, 509, 34, "index"], [528, 18, 509, 39], [529, 12, 509, 39, "fileName"], [529, 20, 509, 39], [529, 22, 509, 39, "_jsxFileName"], [529, 34, 509, 39], [530, 12, 509, 39, "lineNumber"], [530, 22, 509, 39], [531, 12, 509, 39, "columnNumber"], [531, 24, 509, 39], [532, 10, 509, 39], [532, 17, 509, 90], [532, 18, 510, 13], [532, 19, 510, 14], [532, 21, 513, 13, "isLoading"], [532, 30, 513, 22], [532, 47, 514, 14], [532, 51, 514, 14, "_jsxDevRuntime"], [532, 65, 514, 14], [532, 66, 514, 14, "jsxDEV"], [532, 72, 514, 14], [532, 74, 514, 15, "_View"], [532, 79, 514, 15], [532, 80, 514, 15, "default"], [532, 87, 514, 19], [533, 12, 515, 16, "style"], [533, 17, 515, 21], [533, 19, 515, 23], [534, 14, 516, 18, "marginBottom"], [534, 26, 516, 30], [534, 28, 516, 32], [534, 30, 516, 34], [535, 14, 517, 18, "alignSelf"], [535, 23, 517, 27], [535, 25, 517, 29], [535, 37, 517, 41], [536, 14, 518, 18, "max<PERSON><PERSON><PERSON>"], [536, 22, 518, 26], [536, 24, 518, 28], [537, 12, 519, 16], [537, 13, 519, 18], [538, 12, 519, 18, "children"], [538, 20, 519, 18], [538, 35, 520, 16], [538, 39, 520, 16, "_jsxDevRuntime"], [538, 53, 520, 16], [538, 54, 520, 16, "jsxDEV"], [538, 60, 520, 16], [538, 62, 520, 17, "_View"], [538, 67, 520, 17], [538, 68, 520, 17, "default"], [538, 75, 520, 21], [539, 14, 521, 18, "style"], [539, 19, 521, 23], [539, 21, 521, 25], [540, 16, 522, 20, "backgroundColor"], [540, 31, 522, 35], [540, 33, 522, 37, "colors"], [540, 39, 522, 43], [540, 40, 522, 44, "cardBackground"], [540, 54, 522, 58], [541, 16, 523, 20, "borderRadius"], [541, 28, 523, 32], [541, 30, 523, 34], [541, 32, 523, 36], [542, 16, 524, 20, "paddingHorizontal"], [542, 33, 524, 37], [542, 35, 524, 39], [542, 37, 524, 41], [543, 16, 525, 20, "paddingVertical"], [543, 31, 525, 35], [543, 33, 525, 37], [543, 35, 525, 39], [544, 16, 526, 20, "borderWidth"], [544, 27, 526, 31], [544, 29, 526, 33], [544, 30, 526, 34], [545, 16, 527, 20, "borderColor"], [545, 27, 527, 31], [545, 29, 527, 33, "colors"], [545, 35, 527, 39], [545, 36, 527, 40, "outline"], [546, 14, 528, 18], [546, 15, 528, 20], [547, 14, 528, 20, "children"], [547, 22, 528, 20], [547, 37, 529, 18], [547, 41, 529, 18, "_jsxDevRuntime"], [547, 55, 529, 18], [547, 56, 529, 18, "jsxDEV"], [547, 62, 529, 18], [547, 64, 529, 19, "_Text"], [547, 69, 529, 19], [547, 70, 529, 19, "default"], [547, 77, 529, 23], [548, 16, 530, 20, "style"], [548, 21, 530, 25], [548, 23, 530, 27], [549, 18, 531, 22, "fontSize"], [549, 26, 531, 30], [549, 28, 531, 32], [549, 30, 531, 34], [550, 18, 532, 22, "fontFamily"], [550, 28, 532, 32], [550, 30, 532, 34], [550, 50, 532, 54], [551, 18, 533, 22, "color"], [551, 23, 533, 27], [551, 25, 533, 29, "colors"], [551, 31, 533, 35], [551, 32, 533, 36, "textSecondary"], [551, 45, 533, 49], [552, 18, 534, 22, "lineHeight"], [552, 28, 534, 32], [552, 30, 534, 34], [553, 16, 535, 20], [553, 17, 535, 22], [554, 16, 535, 22, "children"], [554, 24, 535, 22], [554, 26, 535, 23], [555, 14, 537, 18], [556, 16, 537, 18, "fileName"], [556, 24, 537, 18], [556, 26, 537, 18, "_jsxFileName"], [556, 38, 537, 18], [557, 16, 537, 18, "lineNumber"], [557, 26, 537, 18], [558, 16, 537, 18, "columnNumber"], [558, 28, 537, 18], [559, 14, 537, 18], [559, 21, 537, 24], [560, 12, 537, 25], [561, 14, 537, 25, "fileName"], [561, 22, 537, 25], [561, 24, 537, 25, "_jsxFileName"], [561, 36, 537, 25], [562, 14, 537, 25, "lineNumber"], [562, 24, 537, 25], [563, 14, 537, 25, "columnNumber"], [563, 26, 537, 25], [564, 12, 537, 25], [564, 19, 538, 22], [565, 10, 538, 23], [566, 12, 538, 23, "fileName"], [566, 20, 538, 23], [566, 22, 538, 23, "_jsxFileName"], [566, 34, 538, 23], [567, 12, 538, 23, "lineNumber"], [567, 22, 538, 23], [568, 12, 538, 23, "columnNumber"], [568, 24, 538, 23], [569, 10, 538, 23], [569, 17, 539, 20], [569, 18, 540, 13], [570, 8, 540, 13], [571, 10, 540, 13, "fileName"], [571, 18, 540, 13], [571, 20, 540, 13, "_jsxFileName"], [571, 32, 540, 13], [572, 10, 540, 13, "lineNumber"], [572, 20, 540, 13], [573, 10, 540, 13, "columnNumber"], [573, 22, 540, 13], [574, 8, 540, 13], [574, 15, 541, 22], [574, 16, 541, 23], [574, 18, 544, 11, "quickActions"], [574, 30, 544, 23], [574, 31, 544, 24, "length"], [574, 37, 544, 30], [574, 40, 544, 33], [574, 41, 544, 34], [574, 58, 545, 12], [574, 62, 545, 12, "_jsxDevRuntime"], [574, 76, 545, 12], [574, 77, 545, 12, "jsxDEV"], [574, 83, 545, 12], [574, 85, 545, 13, "_View"], [574, 90, 545, 13], [574, 91, 545, 13, "default"], [574, 98, 545, 17], [575, 10, 546, 14, "style"], [575, 15, 546, 19], [575, 17, 546, 21], [576, 12, 547, 16, "position"], [576, 20, 547, 24], [576, 22, 547, 26], [576, 32, 547, 36], [577, 12, 548, 16, "bottom"], [577, 18, 548, 22], [577, 20, 548, 24, "insets"], [577, 26, 548, 30], [577, 27, 548, 31, "bottom"], [577, 33, 548, 37], [577, 36, 548, 40], [577, 39, 548, 43], [578, 12, 549, 16, "left"], [578, 16, 549, 20], [578, 18, 549, 22], [578, 20, 549, 24], [579, 12, 550, 16, "right"], [579, 17, 550, 21], [579, 19, 550, 23], [580, 10, 551, 14], [580, 11, 551, 16], [581, 10, 551, 16, "children"], [581, 18, 551, 16], [581, 33, 552, 14], [581, 37, 552, 14, "_jsxDevRuntime"], [581, 51, 552, 14], [581, 52, 552, 14, "jsxDEV"], [581, 58, 552, 14], [581, 60, 552, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [581, 71, 552, 15], [581, 72, 552, 15, "default"], [581, 79, 552, 25], [582, 12, 553, 16, "horizontal"], [582, 22, 553, 26], [583, 12, 554, 16, "showsHorizontalScrollIndicator"], [583, 42, 554, 46], [583, 44, 554, 48], [583, 49, 554, 54], [584, 12, 555, 16, "contentContainerStyle"], [584, 33, 555, 37], [584, 35, 555, 39], [585, 14, 555, 41, "gap"], [585, 17, 555, 44], [585, 19, 555, 46], [586, 12, 555, 48], [586, 13, 555, 50], [587, 12, 555, 50, "children"], [587, 20, 555, 50], [587, 22, 556, 17, "quickActions"], [587, 34, 556, 29], [587, 35, 556, 30, "map"], [587, 38, 556, 33], [587, 39, 556, 34], [587, 40, 556, 35, "action"], [587, 46, 556, 41], [587, 48, 556, 43, "index"], [587, 53, 556, 48], [587, 71, 557, 18], [587, 75, 557, 18, "_jsxDevRuntime"], [587, 89, 557, 18], [587, 90, 557, 18, "jsxDEV"], [587, 96, 557, 18], [587, 98, 557, 19, "_TouchableOpacity"], [587, 115, 557, 19], [587, 116, 557, 19, "default"], [587, 123, 557, 35], [588, 14, 559, 20, "style"], [588, 19, 559, 25], [588, 21, 559, 27], [589, 16, 560, 22, "backgroundColor"], [589, 31, 560, 37], [589, 33, 560, 39, "colors"], [589, 39, 560, 45], [589, 40, 560, 46, "primaryUltraLight"], [589, 57, 560, 63], [590, 16, 561, 22, "borderRadius"], [590, 28, 561, 34], [590, 30, 561, 36], [590, 32, 561, 38], [591, 16, 562, 22, "paddingHorizontal"], [591, 33, 562, 39], [591, 35, 562, 41], [591, 37, 562, 43], [592, 16, 563, 22, "paddingVertical"], [592, 31, 563, 37], [592, 33, 563, 39], [592, 34, 563, 40], [593, 16, 564, 22, "borderWidth"], [593, 27, 564, 33], [593, 29, 564, 35], [593, 30, 564, 36], [594, 16, 565, 22, "borderColor"], [594, 27, 565, 33], [594, 29, 565, 35, "colors"], [594, 35, 565, 41], [594, 36, 565, 42, "primary"], [595, 14, 566, 20], [595, 15, 566, 22], [596, 14, 567, 20, "onPress"], [596, 21, 567, 27], [596, 23, 567, 29, "onPress"], [596, 24, 567, 29], [596, 29, 567, 35, "handleQuickAction"], [596, 46, 567, 52], [596, 47, 567, 53, "action"], [596, 53, 567, 59], [596, 54, 567, 61], [597, 14, 567, 61, "children"], [597, 22, 567, 61], [597, 37, 568, 20], [597, 41, 568, 20, "_jsxDevRuntime"], [597, 55, 568, 20], [597, 56, 568, 20, "jsxDEV"], [597, 62, 568, 20], [597, 64, 568, 21, "_Text"], [597, 69, 568, 21], [597, 70, 568, 21, "default"], [597, 77, 568, 25], [598, 16, 569, 22, "style"], [598, 21, 569, 27], [598, 23, 569, 29], [599, 18, 570, 24, "fontSize"], [599, 26, 570, 32], [599, 28, 570, 34], [599, 30, 570, 36], [600, 18, 571, 24, "fontFamily"], [600, 28, 571, 34], [600, 30, 571, 36], [600, 49, 571, 55], [601, 18, 572, 24, "color"], [601, 23, 572, 29], [601, 25, 572, 31, "colors"], [601, 31, 572, 37], [601, 32, 572, 38, "primary"], [602, 16, 573, 22], [602, 17, 573, 24], [603, 16, 573, 24, "children"], [603, 24, 573, 24], [603, 26, 574, 23, "action"], [604, 14, 574, 29], [605, 16, 574, 29, "fileName"], [605, 24, 574, 29], [605, 26, 574, 29, "_jsxFileName"], [605, 38, 574, 29], [606, 16, 574, 29, "lineNumber"], [606, 26, 574, 29], [607, 16, 574, 29, "columnNumber"], [607, 28, 574, 29], [608, 14, 574, 29], [608, 21, 575, 26], [609, 12, 575, 27], [609, 15, 558, 25, "index"], [609, 20, 558, 30], [610, 14, 558, 30, "fileName"], [610, 22, 558, 30], [610, 24, 558, 30, "_jsxFileName"], [610, 36, 558, 30], [611, 14, 558, 30, "lineNumber"], [611, 24, 558, 30], [612, 14, 558, 30, "columnNumber"], [612, 26, 558, 30], [613, 12, 558, 30], [613, 19, 576, 36], [613, 20, 577, 17], [614, 10, 577, 18], [615, 12, 577, 18, "fileName"], [615, 20, 577, 18], [615, 22, 577, 18, "_jsxFileName"], [615, 34, 577, 18], [616, 12, 577, 18, "lineNumber"], [616, 22, 577, 18], [617, 12, 577, 18, "columnNumber"], [617, 24, 577, 18], [618, 10, 577, 18], [618, 17, 578, 26], [619, 8, 578, 27], [620, 10, 578, 27, "fileName"], [620, 18, 578, 27], [620, 20, 578, 27, "_jsxFileName"], [620, 32, 578, 27], [621, 10, 578, 27, "lineNumber"], [621, 20, 578, 27], [622, 10, 578, 27, "columnNumber"], [622, 22, 578, 27], [623, 8, 578, 27], [623, 15, 579, 18], [623, 16, 580, 11], [623, 31, 583, 10], [623, 35, 583, 10, "_jsxDevRuntime"], [623, 49, 583, 10], [623, 50, 583, 10, "jsxDEV"], [623, 56, 583, 10], [623, 58, 583, 11, "_View"], [623, 63, 583, 11], [623, 64, 583, 11, "default"], [623, 71, 583, 15], [624, 10, 584, 12, "style"], [624, 15, 584, 17], [624, 17, 584, 19], [625, 12, 585, 14, "position"], [625, 20, 585, 22], [625, 22, 585, 24], [625, 32, 585, 34], [626, 12, 586, 14, "bottom"], [626, 18, 586, 20], [626, 20, 586, 22], [626, 21, 586, 23], [627, 12, 587, 14, "left"], [627, 16, 587, 18], [627, 18, 587, 20], [627, 19, 587, 21], [628, 12, 588, 14, "right"], [628, 17, 588, 19], [628, 19, 588, 21], [629, 10, 589, 12], [629, 11, 589, 14], [630, 10, 589, 14, "children"], [630, 18, 589, 14], [630, 33, 590, 12], [630, 37, 590, 12, "_jsxDevRuntime"], [630, 51, 590, 12], [630, 52, 590, 12, "jsxDEV"], [630, 58, 590, 12], [630, 60, 590, 13, "_TextMode"], [630, 69, 590, 13], [630, 70, 590, 13, "default"], [630, 77, 590, 21], [631, 12, 591, 14, "inputText"], [631, 21, 591, 23], [631, 23, 591, 25, "inputText"], [631, 32, 591, 35], [632, 12, 592, 14, "onInputChange"], [632, 25, 592, 27], [632, 27, 592, 29, "setInputText"], [632, 39, 592, 42], [633, 12, 593, 14, "onSendMessage"], [633, 25, 593, 27], [633, 27, 593, 29, "onSendMessage"], [633, 28, 593, 29], [633, 33, 593, 35, "handleSendMessage"], [633, 50, 593, 52], [633, 51, 593, 53], [633, 52, 593, 55], [634, 12, 594, 14, "onStartDictation"], [634, 28, 594, 30], [634, 30, 594, 32, "handleDictation"], [635, 10, 594, 48], [636, 12, 594, 48, "fileName"], [636, 20, 594, 48], [636, 22, 594, 48, "_jsxFileName"], [636, 34, 594, 48], [637, 12, 594, 48, "lineNumber"], [637, 22, 594, 48], [638, 12, 594, 48, "columnNumber"], [638, 24, 594, 48], [639, 10, 594, 48], [639, 17, 595, 13], [640, 8, 595, 14], [641, 10, 595, 14, "fileName"], [641, 18, 595, 14], [641, 20, 595, 14, "_jsxFileName"], [641, 32, 595, 14], [642, 10, 595, 14, "lineNumber"], [642, 20, 595, 14], [643, 10, 595, 14, "columnNumber"], [643, 22, 595, 14], [644, 8, 595, 14], [644, 15, 596, 16], [644, 16, 596, 17], [645, 6, 596, 17], [646, 8, 596, 17, "fileName"], [646, 16, 596, 17], [646, 18, 596, 17, "_jsxFileName"], [646, 30, 596, 17], [647, 8, 596, 17, "lineNumber"], [647, 18, 596, 17], [648, 8, 596, 17, "columnNumber"], [648, 20, 596, 17], [649, 6, 596, 17], [649, 13, 597, 38], [649, 14, 598, 7], [649, 29, 601, 6], [649, 33, 601, 6, "_jsxDevRuntime"], [649, 47, 601, 6], [649, 48, 601, 6, "jsxDEV"], [649, 54, 601, 6], [649, 56, 601, 7, "_Modal"], [649, 62, 601, 7], [649, 63, 601, 7, "default"], [649, 70, 601, 12], [650, 8, 602, 8, "transparent"], [650, 19, 602, 19], [651, 8, 603, 8, "visible"], [651, 15, 603, 15], [651, 17, 603, 17, "isContextMenuVisible"], [651, 37, 603, 38], [652, 8, 604, 8, "onRequestClose"], [652, 22, 604, 22], [652, 24, 604, 24, "onRequestClose"], [652, 25, 604, 24], [652, 30, 604, 30, "setIsContextMenuVisible"], [652, 53, 604, 53], [652, 54, 604, 54], [652, 59, 604, 59], [652, 60, 604, 61], [653, 8, 604, 61, "children"], [653, 16, 604, 61], [653, 31, 606, 8], [653, 35, 606, 8, "_jsxDevRuntime"], [653, 49, 606, 8], [653, 50, 606, 8, "jsxDEV"], [653, 56, 606, 8], [653, 58, 606, 9, "_TouchableOpacity"], [653, 75, 606, 9], [653, 76, 606, 9, "default"], [653, 83, 606, 25], [654, 10, 607, 10, "style"], [654, 15, 607, 15], [654, 17, 607, 17], [655, 12, 607, 19, "flex"], [655, 16, 607, 23], [655, 18, 607, 25], [655, 19, 607, 26], [656, 12, 607, 28, "backgroundColor"], [656, 27, 607, 43], [656, 29, 607, 45], [656, 46, 607, 62], [657, 12, 607, 64, "justifyContent"], [657, 26, 607, 78], [657, 28, 607, 80], [657, 36, 607, 88], [658, 12, 607, 90, "alignItems"], [658, 22, 607, 100], [658, 24, 607, 102], [659, 10, 607, 111], [659, 11, 607, 113], [660, 10, 608, 10, "activeOpacity"], [660, 23, 608, 23], [660, 25, 608, 25], [660, 26, 608, 27], [661, 10, 609, 10, "onPressOut"], [661, 20, 609, 20], [661, 22, 609, 22, "onPressOut"], [661, 23, 609, 22], [661, 28, 609, 28, "setIsContextMenuVisible"], [661, 51, 609, 51], [661, 52, 609, 52], [661, 57, 609, 57], [661, 58, 609, 59], [662, 10, 609, 59, "children"], [662, 18, 609, 59], [662, 33, 611, 10], [662, 37, 611, 10, "_jsxDevRuntime"], [662, 51, 611, 10], [662, 52, 611, 10, "jsxDEV"], [662, 58, 611, 10], [662, 60, 611, 11, "_View"], [662, 65, 611, 11], [662, 66, 611, 11, "default"], [662, 73, 611, 15], [663, 12, 611, 16, "style"], [663, 17, 611, 21], [663, 19, 611, 23], [664, 14, 611, 25, "backgroundColor"], [664, 29, 611, 40], [664, 31, 611, 42, "colors"], [664, 37, 611, 48], [664, 38, 611, 49, "background"], [664, 48, 611, 59], [665, 14, 611, 61, "borderRadius"], [665, 26, 611, 73], [665, 28, 611, 75], [665, 30, 611, 77], [666, 14, 611, 79, "padding"], [666, 21, 611, 86], [666, 23, 611, 88], [666, 25, 611, 90], [667, 14, 611, 92, "width"], [667, 19, 611, 97], [667, 21, 611, 99], [668, 12, 611, 105], [668, 13, 611, 107], [669, 12, 611, 107, "children"], [669, 20, 611, 107], [669, 36, 612, 12], [669, 40, 612, 12, "_jsxDevRuntime"], [669, 54, 612, 12], [669, 55, 612, 12, "jsxDEV"], [669, 61, 612, 12], [669, 63, 612, 13, "_TouchableOpacity"], [669, 80, 612, 13], [669, 81, 612, 13, "default"], [669, 88, 612, 29], [670, 14, 612, 30, "onPress"], [670, 21, 612, 37], [670, 23, 612, 39, "handleCopyMessage"], [670, 40, 612, 57], [671, 14, 612, 58, "style"], [671, 19, 612, 63], [671, 21, 612, 65], [672, 16, 612, 67, "paddingVertical"], [672, 31, 612, 82], [672, 33, 612, 84], [673, 14, 612, 87], [673, 15, 612, 89], [674, 14, 612, 89, "children"], [674, 22, 612, 89], [674, 37, 613, 14], [674, 41, 613, 14, "_jsxDevRuntime"], [674, 55, 613, 14], [674, 56, 613, 14, "jsxDEV"], [674, 62, 613, 14], [674, 64, 613, 15, "_Text"], [674, 69, 613, 15], [674, 70, 613, 15, "default"], [674, 77, 613, 19], [675, 16, 613, 20, "style"], [675, 21, 613, 25], [675, 23, 613, 27], [676, 18, 613, 29, "fontSize"], [676, 26, 613, 37], [676, 28, 613, 39], [676, 30, 613, 41], [677, 18, 613, 43, "fontFamily"], [677, 28, 613, 53], [677, 30, 613, 55], [677, 49, 613, 74], [678, 18, 613, 76, "color"], [678, 23, 613, 81], [678, 25, 613, 83, "colors"], [678, 31, 613, 89], [678, 32, 613, 90, "text"], [679, 16, 613, 95], [679, 17, 613, 97], [680, 16, 613, 97, "children"], [680, 24, 613, 97], [680, 26, 613, 98], [681, 14, 613, 110], [682, 16, 613, 110, "fileName"], [682, 24, 613, 110], [682, 26, 613, 110, "_jsxFileName"], [682, 38, 613, 110], [683, 16, 613, 110, "lineNumber"], [683, 26, 613, 110], [684, 16, 613, 110, "columnNumber"], [684, 28, 613, 110], [685, 14, 613, 110], [685, 21, 613, 116], [686, 12, 613, 117], [687, 14, 613, 117, "fileName"], [687, 22, 613, 117], [687, 24, 613, 117, "_jsxFileName"], [687, 36, 613, 117], [688, 14, 613, 117, "lineNumber"], [688, 24, 613, 117], [689, 14, 613, 117, "columnNumber"], [689, 26, 613, 117], [690, 12, 613, 117], [690, 19, 614, 30], [690, 20, 614, 31], [690, 22, 615, 13, "longPressedMessage"], [690, 40, 615, 31], [690, 42, 615, 33, "role"], [690, 46, 615, 37], [690, 51, 615, 42], [690, 62, 615, 53], [690, 79, 616, 14], [690, 83, 616, 14, "_jsxDevRuntime"], [690, 97, 616, 14], [690, 98, 616, 14, "jsxDEV"], [690, 104, 616, 14], [690, 106, 616, 15, "_TouchableOpacity"], [690, 123, 616, 15], [690, 124, 616, 15, "default"], [690, 131, 616, 31], [691, 14, 616, 32, "onPress"], [691, 21, 616, 39], [691, 23, 616, 41, "handleListenToMessage"], [691, 44, 616, 63], [692, 14, 616, 64, "style"], [692, 19, 616, 69], [692, 21, 616, 71], [693, 16, 616, 73, "paddingVertical"], [693, 31, 616, 88], [693, 33, 616, 90], [694, 14, 616, 93], [694, 15, 616, 95], [695, 14, 616, 95, "children"], [695, 22, 616, 95], [695, 37, 617, 16], [695, 41, 617, 16, "_jsxDevRuntime"], [695, 55, 617, 16], [695, 56, 617, 16, "jsxDEV"], [695, 62, 617, 16], [695, 64, 617, 17, "_Text"], [695, 69, 617, 17], [695, 70, 617, 17, "default"], [695, 77, 617, 21], [696, 16, 617, 22, "style"], [696, 21, 617, 27], [696, 23, 617, 29], [697, 18, 617, 31, "fontSize"], [697, 26, 617, 39], [697, 28, 617, 41], [697, 30, 617, 43], [698, 18, 617, 45, "fontFamily"], [698, 28, 617, 55], [698, 30, 617, 57], [698, 49, 617, 76], [699, 18, 617, 78, "color"], [699, 23, 617, 83], [699, 25, 617, 85, "colors"], [699, 31, 617, 91], [699, 32, 617, 92, "text"], [700, 16, 617, 97], [700, 17, 617, 99], [701, 16, 617, 99, "children"], [701, 24, 617, 99], [701, 26, 617, 100], [702, 14, 617, 117], [703, 16, 617, 117, "fileName"], [703, 24, 617, 117], [703, 26, 617, 117, "_jsxFileName"], [703, 38, 617, 117], [704, 16, 617, 117, "lineNumber"], [704, 26, 617, 117], [705, 16, 617, 117, "columnNumber"], [705, 28, 617, 117], [706, 14, 617, 117], [706, 21, 617, 123], [707, 12, 617, 124], [708, 14, 617, 124, "fileName"], [708, 22, 617, 124], [708, 24, 617, 124, "_jsxFileName"], [708, 36, 617, 124], [709, 14, 617, 124, "lineNumber"], [709, 24, 617, 124], [710, 14, 617, 124, "columnNumber"], [710, 26, 617, 124], [711, 12, 617, 124], [711, 19, 618, 32], [711, 20, 619, 13], [712, 10, 619, 13], [713, 12, 619, 13, "fileName"], [713, 20, 619, 13], [713, 22, 619, 13, "_jsxFileName"], [713, 34, 619, 13], [714, 12, 619, 13, "lineNumber"], [714, 22, 619, 13], [715, 12, 619, 13, "columnNumber"], [715, 24, 619, 13], [716, 10, 619, 13], [716, 17, 620, 16], [717, 8, 620, 17], [718, 10, 620, 17, "fileName"], [718, 18, 620, 17], [718, 20, 620, 17, "_jsxFileName"], [718, 32, 620, 17], [719, 10, 620, 17, "lineNumber"], [719, 20, 620, 17], [720, 10, 620, 17, "columnNumber"], [720, 22, 620, 17], [721, 8, 620, 17], [721, 15, 621, 26], [722, 6, 621, 27], [723, 8, 621, 27, "fileName"], [723, 16, 621, 27], [723, 18, 621, 27, "_jsxFileName"], [723, 30, 621, 27], [724, 8, 621, 27, "lineNumber"], [724, 18, 621, 27], [725, 8, 621, 27, "columnNumber"], [725, 20, 621, 27], [726, 6, 621, 27], [726, 13, 622, 13], [726, 14, 622, 14], [727, 4, 622, 14], [728, 6, 622, 14, "fileName"], [728, 14, 622, 14], [728, 16, 622, 14, "_jsxFileName"], [728, 28, 622, 14], [729, 6, 622, 14, "lineNumber"], [729, 16, 622, 14], [730, 6, 622, 14, "columnNumber"], [730, 18, 622, 14], [731, 4, 622, 14], [731, 11, 623, 10], [731, 12, 623, 11], [732, 2, 625, 0], [733, 2, 625, 1, "_s"], [733, 4, 625, 1], [733, 5, 36, 24, "BrainstormScreen"], [733, 21, 36, 40], [734, 4, 36, 40], [734, 12, 37, 17, "useSafeAreaInsets"], [734, 57, 37, 34], [734, 59, 38, 17, "useColors"], [734, 79, 38, 26], [734, 81, 39, 17, "useRouter"], [734, 102, 39, 26], [734, 104, 40, 22, "useQueryClient"], [734, 130, 40, 36], [734, 132, 41, 24, "useFonts"], [734, 149, 41, 32], [734, 151, 47, 19, "useAudioRecorder"], [734, 178, 47, 35], [734, 180, 48, 24, "useAudioRecorderState"], [734, 212, 48, 45], [735, 2, 48, 45], [736, 2, 48, 45, "_c"], [736, 4, 48, 45], [736, 7, 36, 24, "BrainstormScreen"], [736, 23, 36, 40], [737, 2, 36, 40], [737, 6, 36, 40, "_c"], [737, 8, 36, 40], [738, 2, 36, 40, "$RefreshReg$"], [738, 14, 36, 40], [738, 15, 36, 40, "_c"], [738, 17, 36, 40], [739, 0, 36, 40], [739, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "handleDone", "messages.find$argument_0", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCmC;YCgC;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJuC;4BKE;gBCI,mCD;eEuB;kBDK,+BC;KFqB;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;qBiBE;6CCkB,0BD;GjBwC;YCI;GDI;0BmBiH;anBE;kCoB8C;6BCW,+BD;iBpBU;6BsBgB,yBtB;wBuBW,oCvB;sBwBK,oCxB"}}, "type": "js/module"}]}