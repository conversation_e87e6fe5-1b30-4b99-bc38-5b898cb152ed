{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../constants", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 20, "index": 187}, "end": {"line": 5, "column": 43, "index": 210}}], "key": "zmjjtqoQxi2W71eIMIIaEi1mOpU=", "exportNames": ["*"]}}, {"name": "../fork/getPathFromState-forks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 33, "index": 245}, "end": {"line": 6, "column": 74, "index": 286}}], "key": "Pwl2s8MefiXMrO7elNt6TT2k9Bo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.defaultRouteInfo = void 0;\n  exports.getRouteInfoFromState = getRouteInfoFromState;\n  var constants_1 = require(_dependencyMap[1], \"../constants\");\n  var getPathFromState_forks_1 = require(_dependencyMap[2], \"../fork/getPathFromState-forks\");\n  exports.defaultRouteInfo = {\n    unstable_globalHref: '',\n    searchParams: new URLSearchParams(),\n    pathname: '/',\n    params: {},\n    segments: [],\n    pathnameWithParams: '/',\n    // TODO: Remove this, it is not used anywhere\n    isIndex: false\n  };\n  function getRouteInfoFromState(state) {\n    if (!state) return exports.defaultRouteInfo;\n    var route = state.routes[0];\n    if (route.name !== constants_1.INTERNAL_SLOT_NAME) {\n      throw new Error(`Expected the first route to be ${constants_1.INTERNAL_SLOT_NAME}, but got ${route.name}`);\n    }\n    state = route.state;\n    var segments = [];\n    var params = Object.create(null);\n    while (state) {\n      route = state.routes['index' in state && state.index ? state.index : 0];\n      Object.assign(params, route.params);\n      var routeName = route.name;\n      if (routeName.startsWith('/')) {\n        routeName = routeName.slice(1);\n      }\n      segments.push(...routeName.split('/'));\n      state = route.state;\n    }\n    /**\n     * If React Navigation didn't render the entire tree (e.g it was interrupted in a layout)\n     * then the state maybe incomplete. The reset of the path is in the params, instead of being a route\n     */\n    var routeParams = route.params;\n    while (routeParams && 'screen' in routeParams) {\n      if (typeof routeParams.screen === 'string') {\n        var screen = routeParams.screen.startsWith('/') ? routeParams.screen.slice(1) : routeParams.screen;\n        segments.push(...screen.split('/'));\n      }\n      if (typeof routeParams.params === 'object' && !Array.isArray(routeParams.params)) {\n        routeParams = routeParams.params;\n      } else {\n        routeParams = undefined;\n      }\n    }\n    if (route.params && 'screen' in route.params && route.params.screen === 'string') {\n      var _screen = route.params.screen.startsWith('/') ? route.params.screen.slice(1) : route.params.screen;\n      segments.push(..._screen.split('/'));\n    }\n    if (segments[segments.length - 1] === 'index') {\n      segments.pop();\n    }\n    delete params['screen'];\n    delete params['params'];\n    var pathParams = new Set();\n    var pathname = '/' + segments.filter(segment => {\n      return !(segment.startsWith('(') && segment.endsWith(')'));\n    }).flatMap(segment => {\n      if (segment === '+not-found') {\n        var notFoundPath = params['not-found'];\n        pathParams.add('not-found');\n        if (typeof notFoundPath === 'undefined') {\n          // Not founds are optional, do nothing if its not present\n          return [];\n        } else if (Array.isArray(notFoundPath)) {\n          return notFoundPath;\n        } else {\n          return [notFoundPath];\n        }\n      } else if (segment.startsWith('[...') && segment.endsWith(']')) {\n        var paramName = segment.slice(4, -1);\n        // Legacy for React Navigation optional params\n        if (paramName.endsWith('?')) {\n          paramName = paramName.slice(0, -1);\n        }\n        var values = params[paramName];\n        pathParams.add(paramName);\n        // Catchall params are optional\n        return values || [];\n      } else if (segment.startsWith('[') && segment.endsWith(']')) {\n        var _paramName = segment.slice(1, -1);\n        var value = params[_paramName];\n        pathParams.add(_paramName);\n        // Optional params are optional\n        return value ? [value] : [];\n      } else {\n        return [segment];\n      }\n    }).join('/');\n    var searchParams = new URLSearchParams(Object.entries(params).flatMap(_ref => {\n      var _ref2 = _slicedToArray(_ref, 2),\n        key = _ref2[0],\n        value = _ref2[1];\n      // Search params should not include path params\n      if (pathParams.has(key)) {\n        return [];\n      } else if (Array.isArray(value)) {\n        return value.map(v => [key, v]);\n      }\n      return [[key, value]];\n    }));\n    var hash;\n    if (searchParams.has('#')) {\n      hash = searchParams.get('#') || undefined;\n      searchParams.delete('#');\n    }\n    // We cannot use searchParams.size because it is not included in the React Native polyfill\n    var searchParamString = searchParams.toString();\n    var pathnameWithParams = searchParamString ? pathname + '?' + searchParamString : pathname;\n    pathnameWithParams = hash ? pathnameWithParams + '#' + hash : pathnameWithParams;\n    return {\n      segments,\n      pathname,\n      params,\n      unstable_globalHref: (0, getPathFromState_forks_1.appendBaseUrl)(pathnameWithParams),\n      searchParams,\n      pathnameWithParams,\n      // TODO: Remove this, it is not used anywhere\n      isIndex: false\n    };\n  }\n});", "lineCount": 133, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_slicedToArray"], [4, 20, 1, 13], [4, 23, 1, 13, "require"], [4, 30, 1, 13], [4, 31, 1, 13, "_dependencyMap"], [4, 45, 1, 13], [5, 2, 2, 0, "Object"], [5, 8, 2, 6], [5, 9, 2, 7, "defineProperty"], [5, 23, 2, 21], [5, 24, 2, 22, "exports"], [5, 31, 2, 29], [5, 33, 2, 31], [5, 45, 2, 43], [5, 47, 2, 45], [6, 4, 2, 47, "value"], [6, 9, 2, 52], [6, 11, 2, 54], [7, 2, 2, 59], [7, 3, 2, 60], [7, 4, 2, 61], [8, 2, 3, 0, "exports"], [8, 9, 3, 7], [8, 10, 3, 8, "defaultRouteInfo"], [8, 26, 3, 24], [8, 29, 3, 27], [8, 34, 3, 32], [8, 35, 3, 33], [9, 2, 4, 0, "exports"], [9, 9, 4, 7], [9, 10, 4, 8, "getRouteInfoFromState"], [9, 31, 4, 29], [9, 34, 4, 32, "getRouteInfoFromState"], [9, 55, 4, 53], [10, 2, 5, 0], [10, 6, 5, 6, "constants_1"], [10, 17, 5, 17], [10, 20, 5, 20, "require"], [10, 27, 5, 27], [10, 28, 5, 27, "_dependencyMap"], [10, 42, 5, 27], [10, 61, 5, 42], [10, 62, 5, 43], [11, 2, 6, 0], [11, 6, 6, 6, "getPathFromState_forks_1"], [11, 30, 6, 30], [11, 33, 6, 33, "require"], [11, 40, 6, 40], [11, 41, 6, 40, "_dependencyMap"], [11, 55, 6, 40], [11, 92, 6, 73], [11, 93, 6, 74], [12, 2, 7, 0, "exports"], [12, 9, 7, 7], [12, 10, 7, 8, "defaultRouteInfo"], [12, 26, 7, 24], [12, 29, 7, 27], [13, 4, 8, 4, "unstable_globalHref"], [13, 23, 8, 23], [13, 25, 8, 25], [13, 27, 8, 27], [14, 4, 9, 4, "searchParams"], [14, 16, 9, 16], [14, 18, 9, 18], [14, 22, 9, 22, "URLSearchParams"], [14, 37, 9, 37], [14, 38, 9, 38], [14, 39, 9, 39], [15, 4, 10, 4, "pathname"], [15, 12, 10, 12], [15, 14, 10, 14], [15, 17, 10, 17], [16, 4, 11, 4, "params"], [16, 10, 11, 10], [16, 12, 11, 12], [16, 13, 11, 13], [16, 14, 11, 14], [17, 4, 12, 4, "segments"], [17, 12, 12, 12], [17, 14, 12, 14], [17, 16, 12, 16], [18, 4, 13, 4, "pathnameWithParams"], [18, 22, 13, 22], [18, 24, 13, 24], [18, 27, 13, 27], [19, 4, 14, 4], [20, 4, 15, 4, "isIndex"], [20, 11, 15, 11], [20, 13, 15, 13], [21, 2, 16, 0], [21, 3, 16, 1], [22, 2, 17, 0], [22, 11, 17, 9, "getRouteInfoFromState"], [22, 32, 17, 30, "getRouteInfoFromState"], [22, 33, 17, 31, "state"], [22, 38, 17, 36], [22, 40, 17, 38], [23, 4, 18, 4], [23, 8, 18, 8], [23, 9, 18, 9, "state"], [23, 14, 18, 14], [23, 16, 19, 8], [23, 23, 19, 15, "exports"], [23, 30, 19, 22], [23, 31, 19, 23, "defaultRouteInfo"], [23, 47, 19, 39], [24, 4, 20, 4], [24, 8, 20, 8, "route"], [24, 13, 20, 13], [24, 16, 20, 16, "state"], [24, 21, 20, 21], [24, 22, 20, 22, "routes"], [24, 28, 20, 28], [24, 29, 20, 29], [24, 30, 20, 30], [24, 31, 20, 31], [25, 4, 21, 4], [25, 8, 21, 8, "route"], [25, 13, 21, 13], [25, 14, 21, 14, "name"], [25, 18, 21, 18], [25, 23, 21, 23, "constants_1"], [25, 34, 21, 34], [25, 35, 21, 35, "INTERNAL_SLOT_NAME"], [25, 53, 21, 53], [25, 55, 21, 55], [26, 6, 22, 8], [26, 12, 22, 14], [26, 16, 22, 18, "Error"], [26, 21, 22, 23], [26, 22, 22, 24], [26, 56, 22, 58, "constants_1"], [26, 67, 22, 69], [26, 68, 22, 70, "INTERNAL_SLOT_NAME"], [26, 86, 22, 88], [26, 99, 22, 101, "route"], [26, 104, 22, 106], [26, 105, 22, 107, "name"], [26, 109, 22, 111], [26, 111, 22, 113], [26, 112, 22, 114], [27, 4, 23, 4], [28, 4, 24, 4, "state"], [28, 9, 24, 9], [28, 12, 24, 12, "route"], [28, 17, 24, 17], [28, 18, 24, 18, "state"], [28, 23, 24, 23], [29, 4, 25, 4], [29, 8, 25, 10, "segments"], [29, 16, 25, 18], [29, 19, 25, 21], [29, 21, 25, 23], [30, 4, 26, 4], [30, 8, 26, 10, "params"], [30, 14, 26, 16], [30, 17, 26, 19, "Object"], [30, 23, 26, 25], [30, 24, 26, 26, "create"], [30, 30, 26, 32], [30, 31, 26, 33], [30, 35, 26, 37], [30, 36, 26, 38], [31, 4, 27, 4], [31, 11, 27, 11, "state"], [31, 16, 27, 16], [31, 18, 27, 18], [32, 6, 28, 8, "route"], [32, 11, 28, 13], [32, 14, 28, 16, "state"], [32, 19, 28, 21], [32, 20, 28, 22, "routes"], [32, 26, 28, 28], [32, 27, 28, 29], [32, 34, 28, 36], [32, 38, 28, 40, "state"], [32, 43, 28, 45], [32, 47, 28, 49, "state"], [32, 52, 28, 54], [32, 53, 28, 55, "index"], [32, 58, 28, 60], [32, 61, 28, 63, "state"], [32, 66, 28, 68], [32, 67, 28, 69, "index"], [32, 72, 28, 74], [32, 75, 28, 77], [32, 76, 28, 78], [32, 77, 28, 79], [33, 6, 29, 8, "Object"], [33, 12, 29, 14], [33, 13, 29, 15, "assign"], [33, 19, 29, 21], [33, 20, 29, 22, "params"], [33, 26, 29, 28], [33, 28, 29, 30, "route"], [33, 33, 29, 35], [33, 34, 29, 36, "params"], [33, 40, 29, 42], [33, 41, 29, 43], [34, 6, 30, 8], [34, 10, 30, 12, "routeName"], [34, 19, 30, 21], [34, 22, 30, 24, "route"], [34, 27, 30, 29], [34, 28, 30, 30, "name"], [34, 32, 30, 34], [35, 6, 31, 8], [35, 10, 31, 12, "routeName"], [35, 19, 31, 21], [35, 20, 31, 22, "startsWith"], [35, 30, 31, 32], [35, 31, 31, 33], [35, 34, 31, 36], [35, 35, 31, 37], [35, 37, 31, 39], [36, 8, 32, 12, "routeName"], [36, 17, 32, 21], [36, 20, 32, 24, "routeName"], [36, 29, 32, 33], [36, 30, 32, 34, "slice"], [36, 35, 32, 39], [36, 36, 32, 40], [36, 37, 32, 41], [36, 38, 32, 42], [37, 6, 33, 8], [38, 6, 34, 8, "segments"], [38, 14, 34, 16], [38, 15, 34, 17, "push"], [38, 19, 34, 21], [38, 20, 34, 22], [38, 23, 34, 25, "routeName"], [38, 32, 34, 34], [38, 33, 34, 35, "split"], [38, 38, 34, 40], [38, 39, 34, 41], [38, 42, 34, 44], [38, 43, 34, 45], [38, 44, 34, 46], [39, 6, 35, 8, "state"], [39, 11, 35, 13], [39, 14, 35, 16, "route"], [39, 19, 35, 21], [39, 20, 35, 22, "state"], [39, 25, 35, 27], [40, 4, 36, 4], [41, 4, 37, 4], [42, 0, 38, 0], [43, 0, 39, 0], [44, 0, 40, 0], [45, 4, 41, 4], [45, 8, 41, 8, "routeParams"], [45, 19, 41, 19], [45, 22, 41, 22, "route"], [45, 27, 41, 27], [45, 28, 41, 28, "params"], [45, 34, 41, 34], [46, 4, 42, 4], [46, 11, 42, 11, "routeParams"], [46, 22, 42, 22], [46, 26, 42, 26], [46, 34, 42, 34], [46, 38, 42, 38, "routeParams"], [46, 49, 42, 49], [46, 51, 42, 51], [47, 6, 43, 8], [47, 10, 43, 12], [47, 17, 43, 19, "routeParams"], [47, 28, 43, 30], [47, 29, 43, 31, "screen"], [47, 35, 43, 37], [47, 40, 43, 42], [47, 48, 43, 50], [47, 50, 43, 52], [48, 8, 44, 12], [48, 12, 44, 18, "screen"], [48, 18, 44, 24], [48, 21, 44, 27, "routeParams"], [48, 32, 44, 38], [48, 33, 44, 39, "screen"], [48, 39, 44, 45], [48, 40, 44, 46, "startsWith"], [48, 50, 44, 56], [48, 51, 44, 57], [48, 54, 44, 60], [48, 55, 44, 61], [48, 58, 45, 18, "routeParams"], [48, 69, 45, 29], [48, 70, 45, 30, "screen"], [48, 76, 45, 36], [48, 77, 45, 37, "slice"], [48, 82, 45, 42], [48, 83, 45, 43], [48, 84, 45, 44], [48, 85, 45, 45], [48, 88, 46, 18, "routeParams"], [48, 99, 46, 29], [48, 100, 46, 30, "screen"], [48, 106, 46, 36], [49, 8, 47, 12, "segments"], [49, 16, 47, 20], [49, 17, 47, 21, "push"], [49, 21, 47, 25], [49, 22, 47, 26], [49, 25, 47, 29, "screen"], [49, 31, 47, 35], [49, 32, 47, 36, "split"], [49, 37, 47, 41], [49, 38, 47, 42], [49, 41, 47, 45], [49, 42, 47, 46], [49, 43, 47, 47], [50, 6, 48, 8], [51, 6, 49, 8], [51, 10, 49, 12], [51, 17, 49, 19, "routeParams"], [51, 28, 49, 30], [51, 29, 49, 31, "params"], [51, 35, 49, 37], [51, 40, 49, 42], [51, 48, 49, 50], [51, 52, 49, 54], [51, 53, 49, 55, "Array"], [51, 58, 49, 60], [51, 59, 49, 61, "isArray"], [51, 66, 49, 68], [51, 67, 49, 69, "routeParams"], [51, 78, 49, 80], [51, 79, 49, 81, "params"], [51, 85, 49, 87], [51, 86, 49, 88], [51, 88, 49, 90], [52, 8, 50, 12, "routeParams"], [52, 19, 50, 23], [52, 22, 50, 26, "routeParams"], [52, 33, 50, 37], [52, 34, 50, 38, "params"], [52, 40, 50, 44], [53, 6, 51, 8], [53, 7, 51, 9], [53, 13, 52, 13], [54, 8, 53, 12, "routeParams"], [54, 19, 53, 23], [54, 22, 53, 26, "undefined"], [54, 31, 53, 35], [55, 6, 54, 8], [56, 4, 55, 4], [57, 4, 56, 4], [57, 8, 56, 8, "route"], [57, 13, 56, 13], [57, 14, 56, 14, "params"], [57, 20, 56, 20], [57, 24, 56, 24], [57, 32, 56, 32], [57, 36, 56, 36, "route"], [57, 41, 56, 41], [57, 42, 56, 42, "params"], [57, 48, 56, 48], [57, 52, 56, 52, "route"], [57, 57, 56, 57], [57, 58, 56, 58, "params"], [57, 64, 56, 64], [57, 65, 56, 65, "screen"], [57, 71, 56, 71], [57, 76, 56, 76], [57, 84, 56, 84], [57, 86, 56, 86], [58, 6, 57, 8], [58, 10, 57, 14, "screen"], [58, 17, 57, 20], [58, 20, 57, 23, "route"], [58, 25, 57, 28], [58, 26, 57, 29, "params"], [58, 32, 57, 35], [58, 33, 57, 36, "screen"], [58, 39, 57, 42], [58, 40, 57, 43, "startsWith"], [58, 50, 57, 53], [58, 51, 57, 54], [58, 54, 57, 57], [58, 55, 57, 58], [58, 58, 58, 14, "route"], [58, 63, 58, 19], [58, 64, 58, 20, "params"], [58, 70, 58, 26], [58, 71, 58, 27, "screen"], [58, 77, 58, 33], [58, 78, 58, 34, "slice"], [58, 83, 58, 39], [58, 84, 58, 40], [58, 85, 58, 41], [58, 86, 58, 42], [58, 89, 59, 14, "route"], [58, 94, 59, 19], [58, 95, 59, 20, "params"], [58, 101, 59, 26], [58, 102, 59, 27, "screen"], [58, 108, 59, 33], [59, 6, 60, 8, "segments"], [59, 14, 60, 16], [59, 15, 60, 17, "push"], [59, 19, 60, 21], [59, 20, 60, 22], [59, 23, 60, 25, "screen"], [59, 30, 60, 31], [59, 31, 60, 32, "split"], [59, 36, 60, 37], [59, 37, 60, 38], [59, 40, 60, 41], [59, 41, 60, 42], [59, 42, 60, 43], [60, 4, 61, 4], [61, 4, 62, 4], [61, 8, 62, 8, "segments"], [61, 16, 62, 16], [61, 17, 62, 17, "segments"], [61, 25, 62, 25], [61, 26, 62, 26, "length"], [61, 32, 62, 32], [61, 35, 62, 35], [61, 36, 62, 36], [61, 37, 62, 37], [61, 42, 62, 42], [61, 49, 62, 49], [61, 51, 62, 51], [62, 6, 63, 8, "segments"], [62, 14, 63, 16], [62, 15, 63, 17, "pop"], [62, 18, 63, 20], [62, 19, 63, 21], [62, 20, 63, 22], [63, 4, 64, 4], [64, 4, 65, 4], [64, 11, 65, 11, "params"], [64, 17, 65, 17], [64, 18, 65, 18], [64, 26, 65, 26], [64, 27, 65, 27], [65, 4, 66, 4], [65, 11, 66, 11, "params"], [65, 17, 66, 17], [65, 18, 66, 18], [65, 26, 66, 26], [65, 27, 66, 27], [66, 4, 67, 4], [66, 8, 67, 10, "pathParams"], [66, 18, 67, 20], [66, 21, 67, 23], [66, 25, 67, 27, "Set"], [66, 28, 67, 30], [66, 29, 67, 31], [66, 30, 67, 32], [67, 4, 68, 4], [67, 8, 68, 10, "pathname"], [67, 16, 68, 18], [67, 19, 68, 21], [67, 22, 68, 24], [67, 25, 69, 8, "segments"], [67, 33, 69, 16], [67, 34, 70, 13, "filter"], [67, 40, 70, 19], [67, 41, 70, 21, "segment"], [67, 48, 70, 28], [67, 52, 70, 33], [68, 6, 71, 12], [68, 13, 71, 19], [68, 15, 71, 21, "segment"], [68, 22, 71, 28], [68, 23, 71, 29, "startsWith"], [68, 33, 71, 39], [68, 34, 71, 40], [68, 37, 71, 43], [68, 38, 71, 44], [68, 42, 71, 48, "segment"], [68, 49, 71, 55], [68, 50, 71, 56, "endsWith"], [68, 58, 71, 64], [68, 59, 71, 65], [68, 62, 71, 68], [68, 63, 71, 69], [68, 64, 71, 70], [69, 4, 72, 8], [69, 5, 72, 9], [69, 6, 72, 10], [69, 7, 73, 13, "flatMap"], [69, 14, 73, 20], [69, 15, 73, 22, "segment"], [69, 22, 73, 29], [69, 26, 73, 34], [70, 6, 74, 12], [70, 10, 74, 16, "segment"], [70, 17, 74, 23], [70, 22, 74, 28], [70, 34, 74, 40], [70, 36, 74, 42], [71, 8, 75, 16], [71, 12, 75, 22, "notFoundPath"], [71, 24, 75, 34], [71, 27, 75, 37, "params"], [71, 33, 75, 43], [71, 34, 75, 44], [71, 45, 75, 55], [71, 46, 75, 56], [72, 8, 76, 16, "pathParams"], [72, 18, 76, 26], [72, 19, 76, 27, "add"], [72, 22, 76, 30], [72, 23, 76, 31], [72, 34, 76, 42], [72, 35, 76, 43], [73, 8, 77, 16], [73, 12, 77, 20], [73, 19, 77, 27, "notFoundPath"], [73, 31, 77, 39], [73, 36, 77, 44], [73, 47, 77, 55], [73, 49, 77, 57], [74, 10, 78, 20], [75, 10, 79, 20], [75, 17, 79, 27], [75, 19, 79, 29], [76, 8, 80, 16], [76, 9, 80, 17], [76, 15, 81, 21], [76, 19, 81, 25, "Array"], [76, 24, 81, 30], [76, 25, 81, 31, "isArray"], [76, 32, 81, 38], [76, 33, 81, 39, "notFoundPath"], [76, 45, 81, 51], [76, 46, 81, 52], [76, 48, 81, 54], [77, 10, 82, 20], [77, 17, 82, 27, "notFoundPath"], [77, 29, 82, 39], [78, 8, 83, 16], [78, 9, 83, 17], [78, 15, 84, 21], [79, 10, 85, 20], [79, 17, 85, 27], [79, 18, 85, 28, "notFoundPath"], [79, 30, 85, 40], [79, 31, 85, 41], [80, 8, 86, 16], [81, 6, 87, 12], [81, 7, 87, 13], [81, 13, 88, 17], [81, 17, 88, 21, "segment"], [81, 24, 88, 28], [81, 25, 88, 29, "startsWith"], [81, 35, 88, 39], [81, 36, 88, 40], [81, 42, 88, 46], [81, 43, 88, 47], [81, 47, 88, 51, "segment"], [81, 54, 88, 58], [81, 55, 88, 59, "endsWith"], [81, 63, 88, 67], [81, 64, 88, 68], [81, 67, 88, 71], [81, 68, 88, 72], [81, 70, 88, 74], [82, 8, 89, 16], [82, 12, 89, 20, "paramName"], [82, 21, 89, 29], [82, 24, 89, 32, "segment"], [82, 31, 89, 39], [82, 32, 89, 40, "slice"], [82, 37, 89, 45], [82, 38, 89, 46], [82, 39, 89, 47], [82, 41, 89, 49], [82, 42, 89, 50], [82, 43, 89, 51], [82, 44, 89, 52], [83, 8, 90, 16], [84, 8, 91, 16], [84, 12, 91, 20, "paramName"], [84, 21, 91, 29], [84, 22, 91, 30, "endsWith"], [84, 30, 91, 38], [84, 31, 91, 39], [84, 34, 91, 42], [84, 35, 91, 43], [84, 37, 91, 45], [85, 10, 92, 20, "paramName"], [85, 19, 92, 29], [85, 22, 92, 32, "paramName"], [85, 31, 92, 41], [85, 32, 92, 42, "slice"], [85, 37, 92, 47], [85, 38, 92, 48], [85, 39, 92, 49], [85, 41, 92, 51], [85, 42, 92, 52], [85, 43, 92, 53], [85, 44, 92, 54], [86, 8, 93, 16], [87, 8, 94, 16], [87, 12, 94, 22, "values"], [87, 18, 94, 28], [87, 21, 94, 31, "params"], [87, 27, 94, 37], [87, 28, 94, 38, "paramName"], [87, 37, 94, 47], [87, 38, 94, 48], [88, 8, 95, 16, "pathParams"], [88, 18, 95, 26], [88, 19, 95, 27, "add"], [88, 22, 95, 30], [88, 23, 95, 31, "paramName"], [88, 32, 95, 40], [88, 33, 95, 41], [89, 8, 96, 16], [90, 8, 97, 16], [90, 15, 97, 23, "values"], [90, 21, 97, 29], [90, 25, 97, 33], [90, 27, 97, 35], [91, 6, 98, 12], [91, 7, 98, 13], [91, 13, 99, 17], [91, 17, 99, 21, "segment"], [91, 24, 99, 28], [91, 25, 99, 29, "startsWith"], [91, 35, 99, 39], [91, 36, 99, 40], [91, 39, 99, 43], [91, 40, 99, 44], [91, 44, 99, 48, "segment"], [91, 51, 99, 55], [91, 52, 99, 56, "endsWith"], [91, 60, 99, 64], [91, 61, 99, 65], [91, 64, 99, 68], [91, 65, 99, 69], [91, 67, 99, 71], [92, 8, 100, 16], [92, 12, 100, 22, "paramName"], [92, 22, 100, 31], [92, 25, 100, 34, "segment"], [92, 32, 100, 41], [92, 33, 100, 42, "slice"], [92, 38, 100, 47], [92, 39, 100, 48], [92, 40, 100, 49], [92, 42, 100, 51], [92, 43, 100, 52], [92, 44, 100, 53], [92, 45, 100, 54], [93, 8, 101, 16], [93, 12, 101, 22, "value"], [93, 17, 101, 27], [93, 20, 101, 30, "params"], [93, 26, 101, 36], [93, 27, 101, 37, "paramName"], [93, 37, 101, 46], [93, 38, 101, 47], [94, 8, 102, 16, "pathParams"], [94, 18, 102, 26], [94, 19, 102, 27, "add"], [94, 22, 102, 30], [94, 23, 102, 31, "paramName"], [94, 33, 102, 40], [94, 34, 102, 41], [95, 8, 103, 16], [96, 8, 104, 16], [96, 15, 104, 23, "value"], [96, 20, 104, 28], [96, 23, 104, 31], [96, 24, 104, 32, "value"], [96, 29, 104, 37], [96, 30, 104, 38], [96, 33, 104, 41], [96, 35, 104, 43], [97, 6, 105, 12], [97, 7, 105, 13], [97, 13, 106, 17], [98, 8, 107, 16], [98, 15, 107, 23], [98, 16, 107, 24, "segment"], [98, 23, 107, 31], [98, 24, 107, 32], [99, 6, 108, 12], [100, 4, 109, 8], [100, 5, 109, 9], [100, 6, 109, 10], [100, 7, 110, 13, "join"], [100, 11, 110, 17], [100, 12, 110, 18], [100, 15, 110, 21], [100, 16, 110, 22], [101, 4, 111, 4], [101, 8, 111, 10, "searchParams"], [101, 20, 111, 22], [101, 23, 111, 25], [101, 27, 111, 29, "URLSearchParams"], [101, 42, 111, 44], [101, 43, 111, 45, "Object"], [101, 49, 111, 51], [101, 50, 111, 52, "entries"], [101, 57, 111, 59], [101, 58, 111, 60, "params"], [101, 64, 111, 66], [101, 65, 111, 67], [101, 66, 111, 68, "flatMap"], [101, 73, 111, 75], [101, 74, 111, 76, "_ref"], [101, 78, 111, 76], [101, 82, 111, 94], [102, 6, 111, 94], [102, 10, 111, 94, "_ref2"], [102, 15, 111, 94], [102, 18, 111, 94, "_slicedToArray"], [102, 32, 111, 94], [102, 33, 111, 94, "_ref"], [102, 37, 111, 94], [103, 8, 111, 78, "key"], [103, 11, 111, 81], [103, 14, 111, 81, "_ref2"], [103, 19, 111, 81], [104, 8, 111, 83, "value"], [104, 13, 111, 88], [104, 16, 111, 88, "_ref2"], [104, 21, 111, 88], [105, 6, 112, 8], [106, 6, 113, 8], [106, 10, 113, 12, "pathParams"], [106, 20, 113, 22], [106, 21, 113, 23, "has"], [106, 24, 113, 26], [106, 25, 113, 27, "key"], [106, 28, 113, 30], [106, 29, 113, 31], [106, 31, 113, 33], [107, 8, 114, 12], [107, 15, 114, 19], [107, 17, 114, 21], [108, 6, 115, 8], [108, 7, 115, 9], [108, 13, 116, 13], [108, 17, 116, 17, "Array"], [108, 22, 116, 22], [108, 23, 116, 23, "isArray"], [108, 30, 116, 30], [108, 31, 116, 31, "value"], [108, 36, 116, 36], [108, 37, 116, 37], [108, 39, 116, 39], [109, 8, 117, 12], [109, 15, 117, 19, "value"], [109, 20, 117, 24], [109, 21, 117, 25, "map"], [109, 24, 117, 28], [109, 25, 117, 30, "v"], [109, 26, 117, 31], [109, 30, 117, 36], [109, 31, 117, 37, "key"], [109, 34, 117, 40], [109, 36, 117, 42, "v"], [109, 37, 117, 43], [109, 38, 117, 44], [109, 39, 117, 45], [110, 6, 118, 8], [111, 6, 119, 8], [111, 13, 119, 15], [111, 14, 119, 16], [111, 15, 119, 17, "key"], [111, 18, 119, 20], [111, 20, 119, 22, "value"], [111, 25, 119, 27], [111, 26, 119, 28], [111, 27, 119, 29], [112, 4, 120, 4], [112, 5, 120, 5], [112, 6, 120, 6], [112, 7, 120, 7], [113, 4, 121, 4], [113, 8, 121, 8, "hash"], [113, 12, 121, 12], [114, 4, 122, 4], [114, 8, 122, 8, "searchParams"], [114, 20, 122, 20], [114, 21, 122, 21, "has"], [114, 24, 122, 24], [114, 25, 122, 25], [114, 28, 122, 28], [114, 29, 122, 29], [114, 31, 122, 31], [115, 6, 123, 8, "hash"], [115, 10, 123, 12], [115, 13, 123, 15, "searchParams"], [115, 25, 123, 27], [115, 26, 123, 28, "get"], [115, 29, 123, 31], [115, 30, 123, 32], [115, 33, 123, 35], [115, 34, 123, 36], [115, 38, 123, 40, "undefined"], [115, 47, 123, 49], [116, 6, 124, 8, "searchParams"], [116, 18, 124, 20], [116, 19, 124, 21, "delete"], [116, 25, 124, 27], [116, 26, 124, 28], [116, 29, 124, 31], [116, 30, 124, 32], [117, 4, 125, 4], [118, 4, 126, 4], [119, 4, 127, 4], [119, 8, 127, 10, "searchParamString"], [119, 25, 127, 27], [119, 28, 127, 30, "searchParams"], [119, 40, 127, 42], [119, 41, 127, 43, "toString"], [119, 49, 127, 51], [119, 50, 127, 52], [119, 51, 127, 53], [120, 4, 128, 4], [120, 8, 128, 8, "pathnameWithParams"], [120, 26, 128, 26], [120, 29, 128, 29, "searchParamString"], [120, 46, 128, 46], [120, 49, 128, 49, "pathname"], [120, 57, 128, 57], [120, 60, 128, 60], [120, 63, 128, 63], [120, 66, 128, 66, "searchParamString"], [120, 83, 128, 83], [120, 86, 128, 86, "pathname"], [120, 94, 128, 94], [121, 4, 129, 4, "pathnameWithParams"], [121, 22, 129, 22], [121, 25, 129, 25, "hash"], [121, 29, 129, 29], [121, 32, 129, 32, "pathnameWithParams"], [121, 50, 129, 50], [121, 53, 129, 53], [121, 56, 129, 56], [121, 59, 129, 59, "hash"], [121, 63, 129, 63], [121, 66, 129, 66, "pathnameWithParams"], [121, 84, 129, 84], [122, 4, 130, 4], [122, 11, 130, 11], [123, 6, 131, 8, "segments"], [123, 14, 131, 16], [124, 6, 132, 8, "pathname"], [124, 14, 132, 16], [125, 6, 133, 8, "params"], [125, 12, 133, 14], [126, 6, 134, 8, "unstable_globalHref"], [126, 25, 134, 27], [126, 27, 134, 29], [126, 28, 134, 30], [126, 29, 134, 31], [126, 31, 134, 33, "getPathFromState_forks_1"], [126, 55, 134, 57], [126, 56, 134, 58, "appendBaseUrl"], [126, 69, 134, 71], [126, 71, 134, 73, "pathnameWithParams"], [126, 89, 134, 91], [126, 90, 134, 92], [127, 6, 135, 8, "searchParams"], [127, 18, 135, 20], [128, 6, 136, 8, "pathnameWithParams"], [128, 24, 136, 26], [129, 6, 137, 8], [130, 6, 138, 8, "isIndex"], [130, 13, 138, 15], [130, 15, 138, 17], [131, 4, 139, 4], [131, 5, 139, 5], [132, 2, 140, 0], [133, 0, 140, 1], [133, 3]], "functionMap": {"names": ["<global>", "getRouteInfoFromState", "segments.filter$argument_0", "segments.filter.flatMap$argument_0", "Object.entries.flatMap$argument_0", "value.map$argument_0"], "mappings": "AAA;ACgB;oBCqD;SDE;qBEC;SFoC;4EGE;6BCM,eD;KHG;CDoB"}}, "type": "js/module"}]}