{"dependencies": [{"name": "../ConfigHelper.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 53, "index": 68}}], "key": "gbxqekPN9YwWzwODJteGByBgHo4=", "exportNames": ["*"]}}, {"name": "../isSharedValue.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 69}, "end": {"line": 4, "column": 52, "index": 121}}], "key": "5U54DlUmC1sHQzl1QFdsXXrazfk=", "exportNames": ["*"]}}, {"name": "../mappers.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 122}, "end": {"line": 5, "column": 56, "index": 178}}], "key": "j3GmbWXYnnCA3ljy8S1N+IHDtWE=", "exportNames": ["*"]}}, {"name": "../updateProps/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 179}, "end": {"line": 6, "column": 54, "index": 233}}], "key": "qbpfy52wS90DxIiWiB1QiooQv/k=", "exportNames": ["*"]}}, {"name": "../ViewDescriptorsSet.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 234}, "end": {"line": 7, "column": 66, "index": 300}}], "key": "5rJBe2sTOa1QjbC233zArbM4pZs=", "exportNames": ["*"]}}, {"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 301}, "end": {"line": 8, "column": 42, "index": 343}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.InlinePropManager = void 0;\n  exports.getInlineStyle = getInlineStyle;\n  exports.hasInlineStyles = hasInlineStyles;\n  var _ConfigHelper = require(_dependencyMap[0], \"../ConfigHelper.js\");\n  var _isSharedValue = require(_dependencyMap[1], \"../isSharedValue.js\");\n  var _mappers = require(_dependencyMap[2], \"../mappers.js\");\n  var _index = require(_dependencyMap[3], \"../updateProps/index.js\");\n  var _ViewDescriptorsSet = require(_dependencyMap[4], \"../ViewDescriptorsSet.js\");\n  var _utils = require(_dependencyMap[5], \"./utils.js\");\n  function isInlineStyleTransform(transform) {\n    if (!Array.isArray(transform)) {\n      return false;\n    }\n    return transform.some(t => hasInlineStyles(t));\n  }\n  function inlinePropsHasChanged(styles1, styles2) {\n    if (Object.keys(styles1).length !== Object.keys(styles2).length) {\n      return true;\n    }\n    for (const key of Object.keys(styles1)) {\n      if (styles1[key] !== styles2[key]) {\n        return true;\n      }\n    }\n    return false;\n  }\n  const _worklet_11915436251318_init_data = {\n    code: \"function getInlinePropsUpdate_reactNativeReanimated_InlinePropManagerJs1(inlineProps){const getInlinePropsUpdate_reactNativeReanimated_InlinePropManagerJs1=this._recur;const{isSharedValue}=this.__closure;const update={};for(const[key,styleValue]of Object.entries(inlineProps)){if(isSharedValue(styleValue)){update[key]=styleValue.value;}else if(Array.isArray(styleValue)){update[key]=styleValue.map(function(item){return getInlinePropsUpdate_reactNativeReanimated_InlinePropManagerJs1(item);});}else if(typeof styleValue==='object'){update[key]=getInlinePropsUpdate_reactNativeReanimated_InlinePropManagerJs1(styleValue);}else{update[key]=styleValue;}}return update;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/createAnimatedComponent/InlinePropManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getInlinePropsUpdate_reactNativeReanimated_InlinePropManagerJs1\\\",\\\"inlineProps\\\",\\\"_recur\\\",\\\"isSharedValue\\\",\\\"__closure\\\",\\\"update\\\",\\\"key\\\",\\\"styleValue\\\",\\\"Object\\\",\\\"entries\\\",\\\"value\\\",\\\"Array\\\",\\\"isArray\\\",\\\"map\\\",\\\"item\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/createAnimatedComponent/InlinePropManager.js\\\"],\\\"mappings\\\":\\\"AAyBA,SAAAA,+DAA2CA,CAAAC,WAAA,QAAAD,+DAAA,MAAAE,MAAA,OAAAC,aAAA,OAAAC,SAAA,CAGzC,KAAM,CAAAC,MAAM,CAAG,CAAC,CAAC,CACjB,IAAK,KAAM,CAACC,GAAG,CAAEC,UAAU,CAAC,EAAI,CAAAC,MAAM,CAACC,OAAO,CAACR,WAAW,CAAC,CAAE,CAC3D,GAAIE,aAAa,CAACI,UAAU,CAAC,CAAE,CAC7BF,MAAM,CAACC,GAAG,CAAC,CAAGC,UAAU,CAACG,KAAK,CAChC,CAAC,IAAM,IAAIC,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,CAAE,CACpCF,MAAM,CAACC,GAAG,CAAC,CAAGC,UAAU,CAACM,GAAG,CAAC,SAAAC,IAAI,CAAI,CACnC,MAAO,CAAAd,+DAA0B,CAAAc,IAAA,EACnC,CAAC,CAAC,CACJ,CAAC,IAAM,IAAI,MAAO,CAAAP,UAAU,GAAK,QAAQ,CAAE,CACzCF,MAAM,CAACC,GAAG,CAAC,CAAGN,+DAAgC,CAAAO,UAAA,EAChD,CAAC,IAAM,CACLF,MAAM,CAACC,GAAG,CAAC,CAAGC,UAAU,CAC1B,CACF,CACA,MAAO,CAAAF,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const getInlinePropsUpdate = function () {\n    const _e = [new global.Error(), -2, -27];\n    const getInlinePropsUpdate = function (inlineProps) {\n      const update = {};\n      for (const [key, styleValue] of Object.entries(inlineProps)) {\n        if ((0, _isSharedValue.isSharedValue)(styleValue)) {\n          update[key] = styleValue.value;\n        } else if (Array.isArray(styleValue)) {\n          update[key] = styleValue.map(item => {\n            return getInlinePropsUpdate(item);\n          });\n        } else if (typeof styleValue === 'object') {\n          update[key] = getInlinePropsUpdate(styleValue);\n        } else {\n          update[key] = styleValue;\n        }\n      }\n      return update;\n    };\n    getInlinePropsUpdate.__closure = {\n      isSharedValue: _isSharedValue.isSharedValue\n    };\n    getInlinePropsUpdate.__workletHash = 11915436251318;\n    getInlinePropsUpdate.__initData = _worklet_11915436251318_init_data;\n    getInlinePropsUpdate.__stackDetails = _e;\n    return getInlinePropsUpdate;\n  }();\n  function extractSharedValuesMapFromProps(props) {\n    const inlineProps = {};\n    for (const key in props) {\n      const value = props[key];\n      if (key === 'style') {\n        const styles = (0, _utils.flattenArray)(props.style ?? []);\n        styles.forEach(style => {\n          if (!style) {\n            return;\n          }\n          for (const [styleKey, styleValue] of Object.entries(style)) {\n            if ((0, _isSharedValue.isSharedValue)(styleValue)) {\n              inlineProps[styleKey] = styleValue;\n            } else if (styleKey === 'transform' && isInlineStyleTransform(styleValue)) {\n              inlineProps[styleKey] = styleValue;\n            }\n          }\n        });\n      } else if ((0, _isSharedValue.isSharedValue)(value)) {\n        inlineProps[key] = value;\n      }\n    }\n    return inlineProps;\n  }\n  function hasInlineStyles(style) {\n    if (!style) {\n      return false;\n    }\n    return Object.keys(style).some(key => {\n      const styleValue = style[key];\n      return (0, _isSharedValue.isSharedValue)(styleValue) || key === 'transform' && isInlineStyleTransform(styleValue);\n    });\n  }\n  function getInlineStyle(style, isFirstRender) {\n    if (isFirstRender) {\n      return getInlinePropsUpdate(style);\n    }\n    const newStyle = {};\n    for (const [key, styleValue] of Object.entries(style)) {\n      if (!(0, _isSharedValue.isSharedValue)(styleValue) && !(key === 'transform' && isInlineStyleTransform(styleValue))) {\n        newStyle[key] = styleValue;\n      }\n    }\n    return newStyle;\n  }\n  const _worklet_5061273221373_init_data = {\n    code: \"function reactNativeReanimated_InlinePropManagerJs2(){const{getInlinePropsUpdate,newInlineProps,updateProps,shareableViewDescriptors}=this.__closure;const update=getInlinePropsUpdate(newInlineProps);updateProps(shareableViewDescriptors,update);}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/createAnimatedComponent/InlinePropManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_InlinePropManagerJs2\\\",\\\"getInlinePropsUpdate\\\",\\\"newInlineProps\\\",\\\"updateProps\\\",\\\"shareableViewDescriptors\\\",\\\"__closure\\\",\\\"update\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/createAnimatedComponent/InlinePropManager.js\\\"],\\\"mappings\\\":\\\"AAmH8B,SAAAA,0CAAMA,CAAA,QAAAC,oBAAA,CAAAC,cAAA,CAAAC,WAAA,CAAAC,wBAAA,OAAAC,SAAA,CAG5B,KAAM,CAAAC,MAAM,CAAGL,oBAAoB,CAACC,cAAc,CAAC,CACnDC,WAAW,CAACC,wBAAwB,CAAEE,MAAM,CAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  class InlinePropManager {\n    _inlinePropsViewDescriptors = null;\n    _inlinePropsMapperId = null;\n    _inlineProps = {};\n    attachInlineProps(animatedComponent, viewInfo) {\n      const newInlineProps = extractSharedValuesMapFromProps(animatedComponent.props);\n      const hasChanged = inlinePropsHasChanged(newInlineProps, this._inlineProps);\n      if (hasChanged) {\n        if (!this._inlinePropsViewDescriptors) {\n          this._inlinePropsViewDescriptors = (0, _ViewDescriptorsSet.makeViewDescriptorsSet)();\n          const {\n            viewTag,\n            viewName,\n            shadowNodeWrapper,\n            viewConfig\n          } = viewInfo;\n          if (Object.keys(newInlineProps).length && viewConfig) {\n            (0, _ConfigHelper.adaptViewConfig)(viewConfig);\n          }\n          this._inlinePropsViewDescriptors.add({\n            tag: viewTag,\n            name: viewName,\n            shadowNodeWrapper: shadowNodeWrapper\n          });\n        }\n        const shareableViewDescriptors = this._inlinePropsViewDescriptors.shareableViewDescriptors;\n        const updaterFunction = function () {\n          const _e = [new global.Error(), -5, -27];\n          const reactNativeReanimated_InlinePropManagerJs2 = function () {\n            const update = getInlinePropsUpdate(newInlineProps);\n            (0, _index.updateProps)(shareableViewDescriptors, update);\n          };\n          reactNativeReanimated_InlinePropManagerJs2.__closure = {\n            getInlinePropsUpdate,\n            newInlineProps,\n            updateProps: _index.updateProps,\n            shareableViewDescriptors\n          };\n          reactNativeReanimated_InlinePropManagerJs2.__workletHash = 5061273221373;\n          reactNativeReanimated_InlinePropManagerJs2.__initData = _worklet_5061273221373_init_data;\n          reactNativeReanimated_InlinePropManagerJs2.__stackDetails = _e;\n          return reactNativeReanimated_InlinePropManagerJs2;\n        }();\n        this._inlineProps = newInlineProps;\n        if (this._inlinePropsMapperId) {\n          (0, _mappers.stopMapper)(this._inlinePropsMapperId);\n        }\n        this._inlinePropsMapperId = null;\n        if (Object.keys(newInlineProps).length) {\n          this._inlinePropsMapperId = (0, _mappers.startMapper)(updaterFunction, Object.values(newInlineProps));\n        }\n      }\n    }\n    detachInlineProps() {\n      if (this._inlinePropsMapperId) {\n        (0, _mappers.stopMapper)(this._inlinePropsMapperId);\n      }\n    }\n  }\n  exports.InlinePropManager = InlinePropManager;\n});", "lineCount": 177, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "InlinePropManager"], [7, 27, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "getInlineStyle"], [8, 24, 1, 13], [8, 27, 1, 13, "getInlineStyle"], [8, 41, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "hasInlineStyles"], [9, 25, 1, 13], [9, 28, 1, 13, "hasInlineStyles"], [9, 43, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_ConfigHelper"], [10, 19, 3, 0], [10, 22, 3, 0, "require"], [10, 29, 3, 0], [10, 30, 3, 0, "_dependencyMap"], [10, 44, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_isSharedValue"], [11, 20, 4, 0], [11, 23, 4, 0, "require"], [11, 30, 4, 0], [11, 31, 4, 0, "_dependencyMap"], [11, 45, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_mappers"], [12, 14, 5, 0], [12, 17, 5, 0, "require"], [12, 24, 5, 0], [12, 25, 5, 0, "_dependencyMap"], [12, 39, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_index"], [13, 12, 6, 0], [13, 15, 6, 0, "require"], [13, 22, 6, 0], [13, 23, 6, 0, "_dependencyMap"], [13, 37, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_ViewDescriptorsSet"], [14, 25, 7, 0], [14, 28, 7, 0, "require"], [14, 35, 7, 0], [14, 36, 7, 0, "_dependencyMap"], [14, 50, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_utils"], [15, 12, 8, 0], [15, 15, 8, 0, "require"], [15, 22, 8, 0], [15, 23, 8, 0, "_dependencyMap"], [15, 37, 8, 0], [16, 2, 9, 0], [16, 11, 9, 9, "isInlineStyleTransform"], [16, 33, 9, 31, "isInlineStyleTransform"], [16, 34, 9, 32, "transform"], [16, 43, 9, 41], [16, 45, 9, 43], [17, 4, 10, 2], [17, 8, 10, 6], [17, 9, 10, 7, "Array"], [17, 14, 10, 12], [17, 15, 10, 13, "isArray"], [17, 22, 10, 20], [17, 23, 10, 21, "transform"], [17, 32, 10, 30], [17, 33, 10, 31], [17, 35, 10, 33], [18, 6, 11, 4], [18, 13, 11, 11], [18, 18, 11, 16], [19, 4, 12, 2], [20, 4, 13, 2], [20, 11, 13, 9, "transform"], [20, 20, 13, 18], [20, 21, 13, 19, "some"], [20, 25, 13, 23], [20, 26, 13, 24, "t"], [20, 27, 13, 25], [20, 31, 13, 29, "hasInlineStyles"], [20, 46, 13, 44], [20, 47, 13, 45, "t"], [20, 48, 13, 46], [20, 49, 13, 47], [20, 50, 13, 48], [21, 2, 14, 0], [22, 2, 15, 0], [22, 11, 15, 9, "inlinePropsHasChanged"], [22, 32, 15, 30, "inlinePropsHasChanged"], [22, 33, 15, 31, "styles1"], [22, 40, 15, 38], [22, 42, 15, 40, "styles2"], [22, 49, 15, 47], [22, 51, 15, 49], [23, 4, 16, 2], [23, 8, 16, 6, "Object"], [23, 14, 16, 12], [23, 15, 16, 13, "keys"], [23, 19, 16, 17], [23, 20, 16, 18, "styles1"], [23, 27, 16, 25], [23, 28, 16, 26], [23, 29, 16, 27, "length"], [23, 35, 16, 33], [23, 40, 16, 38, "Object"], [23, 46, 16, 44], [23, 47, 16, 45, "keys"], [23, 51, 16, 49], [23, 52, 16, 50, "styles2"], [23, 59, 16, 57], [23, 60, 16, 58], [23, 61, 16, 59, "length"], [23, 67, 16, 65], [23, 69, 16, 67], [24, 6, 17, 4], [24, 13, 17, 11], [24, 17, 17, 15], [25, 4, 18, 2], [26, 4, 19, 2], [26, 9, 19, 7], [26, 15, 19, 13, "key"], [26, 18, 19, 16], [26, 22, 19, 20, "Object"], [26, 28, 19, 26], [26, 29, 19, 27, "keys"], [26, 33, 19, 31], [26, 34, 19, 32, "styles1"], [26, 41, 19, 39], [26, 42, 19, 40], [26, 44, 19, 42], [27, 6, 20, 4], [27, 10, 20, 8, "styles1"], [27, 17, 20, 15], [27, 18, 20, 16, "key"], [27, 21, 20, 19], [27, 22, 20, 20], [27, 27, 20, 25, "styles2"], [27, 34, 20, 32], [27, 35, 20, 33, "key"], [27, 38, 20, 36], [27, 39, 20, 37], [27, 41, 20, 39], [28, 8, 21, 6], [28, 15, 21, 13], [28, 19, 21, 17], [29, 6, 22, 4], [30, 4, 23, 2], [31, 4, 24, 2], [31, 11, 24, 9], [31, 16, 24, 14], [32, 2, 25, 0], [33, 2, 25, 1], [33, 8, 25, 1, "_worklet_11915436251318_init_data"], [33, 41, 25, 1], [34, 4, 25, 1, "code"], [34, 8, 25, 1], [35, 4, 25, 1, "location"], [35, 12, 25, 1], [36, 4, 25, 1, "sourceMap"], [36, 13, 25, 1], [37, 4, 25, 1, "version"], [37, 11, 25, 1], [38, 2, 25, 1], [39, 2, 25, 1], [39, 8, 25, 1, "getInlinePropsUpdate"], [39, 28, 25, 1], [39, 31, 26, 0], [40, 4, 26, 0], [40, 10, 26, 0, "_e"], [40, 12, 26, 0], [40, 20, 26, 0, "global"], [40, 26, 26, 0], [40, 27, 26, 0, "Error"], [40, 32, 26, 0], [41, 4, 26, 0], [41, 10, 26, 0, "getInlinePropsUpdate"], [41, 30, 26, 0], [41, 42, 26, 0, "getInlinePropsUpdate"], [41, 43, 26, 30, "inlineProps"], [41, 54, 26, 41], [41, 56, 26, 43], [42, 6, 29, 2], [42, 12, 29, 8, "update"], [42, 18, 29, 14], [42, 21, 29, 17], [42, 22, 29, 18], [42, 23, 29, 19], [43, 6, 30, 2], [43, 11, 30, 7], [43, 17, 30, 13], [43, 18, 30, 14, "key"], [43, 21, 30, 17], [43, 23, 30, 19, "styleValue"], [43, 33, 30, 29], [43, 34, 30, 30], [43, 38, 30, 34, "Object"], [43, 44, 30, 40], [43, 45, 30, 41, "entries"], [43, 52, 30, 48], [43, 53, 30, 49, "inlineProps"], [43, 64, 30, 60], [43, 65, 30, 61], [43, 67, 30, 63], [44, 8, 31, 4], [44, 12, 31, 8], [44, 16, 31, 8, "isSharedValue"], [44, 44, 31, 21], [44, 46, 31, 22, "styleValue"], [44, 56, 31, 32], [44, 57, 31, 33], [44, 59, 31, 35], [45, 10, 32, 6, "update"], [45, 16, 32, 12], [45, 17, 32, 13, "key"], [45, 20, 32, 16], [45, 21, 32, 17], [45, 24, 32, 20, "styleValue"], [45, 34, 32, 30], [45, 35, 32, 31, "value"], [45, 40, 32, 36], [46, 8, 33, 4], [46, 9, 33, 5], [46, 15, 33, 11], [46, 19, 33, 15, "Array"], [46, 24, 33, 20], [46, 25, 33, 21, "isArray"], [46, 32, 33, 28], [46, 33, 33, 29, "styleValue"], [46, 43, 33, 39], [46, 44, 33, 40], [46, 46, 33, 42], [47, 10, 34, 6, "update"], [47, 16, 34, 12], [47, 17, 34, 13, "key"], [47, 20, 34, 16], [47, 21, 34, 17], [47, 24, 34, 20, "styleValue"], [47, 34, 34, 30], [47, 35, 34, 31, "map"], [47, 38, 34, 34], [47, 39, 34, 35, "item"], [47, 43, 34, 39], [47, 47, 34, 43], [48, 12, 35, 8], [48, 19, 35, 15, "getInlinePropsUpdate"], [48, 39, 35, 35], [48, 40, 35, 36, "item"], [48, 44, 35, 40], [48, 45, 35, 41], [49, 10, 36, 6], [49, 11, 36, 7], [49, 12, 36, 8], [50, 8, 37, 4], [50, 9, 37, 5], [50, 15, 37, 11], [50, 19, 37, 15], [50, 26, 37, 22, "styleValue"], [50, 36, 37, 32], [50, 41, 37, 37], [50, 49, 37, 45], [50, 51, 37, 47], [51, 10, 38, 6, "update"], [51, 16, 38, 12], [51, 17, 38, 13, "key"], [51, 20, 38, 16], [51, 21, 38, 17], [51, 24, 38, 20, "getInlinePropsUpdate"], [51, 44, 38, 40], [51, 45, 38, 41, "styleValue"], [51, 55, 38, 51], [51, 56, 38, 52], [52, 8, 39, 4], [52, 9, 39, 5], [52, 15, 39, 11], [53, 10, 40, 6, "update"], [53, 16, 40, 12], [53, 17, 40, 13, "key"], [53, 20, 40, 16], [53, 21, 40, 17], [53, 24, 40, 20, "styleValue"], [53, 34, 40, 30], [54, 8, 41, 4], [55, 6, 42, 2], [56, 6, 43, 2], [56, 13, 43, 9, "update"], [56, 19, 43, 15], [57, 4, 44, 0], [57, 5, 44, 1], [58, 4, 44, 1, "getInlinePropsUpdate"], [58, 24, 44, 1], [58, 25, 44, 1, "__closure"], [58, 34, 44, 1], [59, 6, 44, 1, "isSharedValue"], [59, 19, 44, 1], [59, 21, 31, 8, "isSharedValue"], [60, 4, 31, 21], [61, 4, 31, 21, "getInlinePropsUpdate"], [61, 24, 31, 21], [61, 25, 31, 21, "__workletHash"], [61, 38, 31, 21], [62, 4, 31, 21, "getInlinePropsUpdate"], [62, 24, 31, 21], [62, 25, 31, 21, "__initData"], [62, 35, 31, 21], [62, 38, 31, 21, "_worklet_11915436251318_init_data"], [62, 71, 31, 21], [63, 4, 31, 21, "getInlinePropsUpdate"], [63, 24, 31, 21], [63, 25, 31, 21, "__stackDetails"], [63, 39, 31, 21], [63, 42, 31, 21, "_e"], [63, 44, 31, 21], [64, 4, 31, 21], [64, 11, 31, 21, "getInlinePropsUpdate"], [64, 31, 31, 21], [65, 2, 31, 21], [65, 3, 26, 0], [66, 2, 45, 0], [66, 11, 45, 9, "extractSharedValuesMapFromProps"], [66, 42, 45, 40, "extractSharedValuesMapFromProps"], [66, 43, 45, 41, "props"], [66, 48, 45, 46], [66, 50, 45, 48], [67, 4, 46, 2], [67, 10, 46, 8, "inlineProps"], [67, 21, 46, 19], [67, 24, 46, 22], [67, 25, 46, 23], [67, 26, 46, 24], [68, 4, 47, 2], [68, 9, 47, 7], [68, 15, 47, 13, "key"], [68, 18, 47, 16], [68, 22, 47, 20, "props"], [68, 27, 47, 25], [68, 29, 47, 27], [69, 6, 48, 4], [69, 12, 48, 10, "value"], [69, 17, 48, 15], [69, 20, 48, 18, "props"], [69, 25, 48, 23], [69, 26, 48, 24, "key"], [69, 29, 48, 27], [69, 30, 48, 28], [70, 6, 49, 4], [70, 10, 49, 8, "key"], [70, 13, 49, 11], [70, 18, 49, 16], [70, 25, 49, 23], [70, 27, 49, 25], [71, 8, 50, 6], [71, 14, 50, 12, "styles"], [71, 20, 50, 18], [71, 23, 50, 21], [71, 27, 50, 21, "flattenArray"], [71, 46, 50, 33], [71, 48, 50, 34, "props"], [71, 53, 50, 39], [71, 54, 50, 40, "style"], [71, 59, 50, 45], [71, 63, 50, 49], [71, 65, 50, 51], [71, 66, 50, 52], [72, 8, 51, 6, "styles"], [72, 14, 51, 12], [72, 15, 51, 13, "for<PERSON>ach"], [72, 22, 51, 20], [72, 23, 51, 21, "style"], [72, 28, 51, 26], [72, 32, 51, 30], [73, 10, 52, 8], [73, 14, 52, 12], [73, 15, 52, 13, "style"], [73, 20, 52, 18], [73, 22, 52, 20], [74, 12, 53, 10], [75, 10, 54, 8], [76, 10, 55, 8], [76, 15, 55, 13], [76, 21, 55, 19], [76, 22, 55, 20, "styleKey"], [76, 30, 55, 28], [76, 32, 55, 30, "styleValue"], [76, 42, 55, 40], [76, 43, 55, 41], [76, 47, 55, 45, "Object"], [76, 53, 55, 51], [76, 54, 55, 52, "entries"], [76, 61, 55, 59], [76, 62, 55, 60, "style"], [76, 67, 55, 65], [76, 68, 55, 66], [76, 70, 55, 68], [77, 12, 56, 10], [77, 16, 56, 14], [77, 20, 56, 14, "isSharedValue"], [77, 48, 56, 27], [77, 50, 56, 28, "styleValue"], [77, 60, 56, 38], [77, 61, 56, 39], [77, 63, 56, 41], [78, 14, 57, 12, "inlineProps"], [78, 25, 57, 23], [78, 26, 57, 24, "styleKey"], [78, 34, 57, 32], [78, 35, 57, 33], [78, 38, 57, 36, "styleValue"], [78, 48, 57, 46], [79, 12, 58, 10], [79, 13, 58, 11], [79, 19, 58, 17], [79, 23, 58, 21, "styleKey"], [79, 31, 58, 29], [79, 36, 58, 34], [79, 47, 58, 45], [79, 51, 58, 49, "isInlineStyleTransform"], [79, 73, 58, 71], [79, 74, 58, 72, "styleValue"], [79, 84, 58, 82], [79, 85, 58, 83], [79, 87, 58, 85], [80, 14, 59, 12, "inlineProps"], [80, 25, 59, 23], [80, 26, 59, 24, "styleKey"], [80, 34, 59, 32], [80, 35, 59, 33], [80, 38, 59, 36, "styleValue"], [80, 48, 59, 46], [81, 12, 60, 10], [82, 10, 61, 8], [83, 8, 62, 6], [83, 9, 62, 7], [83, 10, 62, 8], [84, 6, 63, 4], [84, 7, 63, 5], [84, 13, 63, 11], [84, 17, 63, 15], [84, 21, 63, 15, "isSharedValue"], [84, 49, 63, 28], [84, 51, 63, 29, "value"], [84, 56, 63, 34], [84, 57, 63, 35], [84, 59, 63, 37], [85, 8, 64, 6, "inlineProps"], [85, 19, 64, 17], [85, 20, 64, 18, "key"], [85, 23, 64, 21], [85, 24, 64, 22], [85, 27, 64, 25, "value"], [85, 32, 64, 30], [86, 6, 65, 4], [87, 4, 66, 2], [88, 4, 67, 2], [88, 11, 67, 9, "inlineProps"], [88, 22, 67, 20], [89, 2, 68, 0], [90, 2, 69, 7], [90, 11, 69, 16, "hasInlineStyles"], [90, 26, 69, 31, "hasInlineStyles"], [90, 27, 69, 32, "style"], [90, 32, 69, 37], [90, 34, 69, 39], [91, 4, 70, 2], [91, 8, 70, 6], [91, 9, 70, 7, "style"], [91, 14, 70, 12], [91, 16, 70, 14], [92, 6, 71, 4], [92, 13, 71, 11], [92, 18, 71, 16], [93, 4, 72, 2], [94, 4, 73, 2], [94, 11, 73, 9, "Object"], [94, 17, 73, 15], [94, 18, 73, 16, "keys"], [94, 22, 73, 20], [94, 23, 73, 21, "style"], [94, 28, 73, 26], [94, 29, 73, 27], [94, 30, 73, 28, "some"], [94, 34, 73, 32], [94, 35, 73, 33, "key"], [94, 38, 73, 36], [94, 42, 73, 40], [95, 6, 74, 4], [95, 12, 74, 10, "styleValue"], [95, 22, 74, 20], [95, 25, 74, 23, "style"], [95, 30, 74, 28], [95, 31, 74, 29, "key"], [95, 34, 74, 32], [95, 35, 74, 33], [96, 6, 75, 4], [96, 13, 75, 11], [96, 17, 75, 11, "isSharedValue"], [96, 45, 75, 24], [96, 47, 75, 25, "styleValue"], [96, 57, 75, 35], [96, 58, 75, 36], [96, 62, 75, 40, "key"], [96, 65, 75, 43], [96, 70, 75, 48], [96, 81, 75, 59], [96, 85, 75, 63, "isInlineStyleTransform"], [96, 107, 75, 85], [96, 108, 75, 86, "styleValue"], [96, 118, 75, 96], [96, 119, 75, 97], [97, 4, 76, 2], [97, 5, 76, 3], [97, 6, 76, 4], [98, 2, 77, 0], [99, 2, 78, 7], [99, 11, 78, 16, "getInlineStyle"], [99, 25, 78, 30, "getInlineStyle"], [99, 26, 78, 31, "style"], [99, 31, 78, 36], [99, 33, 78, 38, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [99, 46, 78, 51], [99, 48, 78, 53], [100, 4, 79, 2], [100, 8, 79, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [100, 21, 79, 19], [100, 23, 79, 21], [101, 6, 80, 4], [101, 13, 80, 11, "getInlinePropsUpdate"], [101, 33, 80, 31], [101, 34, 80, 32, "style"], [101, 39, 80, 37], [101, 40, 80, 38], [102, 4, 81, 2], [103, 4, 82, 2], [103, 10, 82, 8, "newStyle"], [103, 18, 82, 16], [103, 21, 82, 19], [103, 22, 82, 20], [103, 23, 82, 21], [104, 4, 83, 2], [104, 9, 83, 7], [104, 15, 83, 13], [104, 16, 83, 14, "key"], [104, 19, 83, 17], [104, 21, 83, 19, "styleValue"], [104, 31, 83, 29], [104, 32, 83, 30], [104, 36, 83, 34, "Object"], [104, 42, 83, 40], [104, 43, 83, 41, "entries"], [104, 50, 83, 48], [104, 51, 83, 49, "style"], [104, 56, 83, 54], [104, 57, 83, 55], [104, 59, 83, 57], [105, 6, 84, 4], [105, 10, 84, 8], [105, 11, 84, 9], [105, 15, 84, 9, "isSharedValue"], [105, 43, 84, 22], [105, 45, 84, 23, "styleValue"], [105, 55, 84, 33], [105, 56, 84, 34], [105, 60, 84, 38], [105, 62, 84, 40, "key"], [105, 65, 84, 43], [105, 70, 84, 48], [105, 81, 84, 59], [105, 85, 84, 63, "isInlineStyleTransform"], [105, 107, 84, 85], [105, 108, 84, 86, "styleValue"], [105, 118, 84, 96], [105, 119, 84, 97], [105, 120, 84, 98], [105, 122, 84, 100], [106, 8, 85, 6, "newStyle"], [106, 16, 85, 14], [106, 17, 85, 15, "key"], [106, 20, 85, 18], [106, 21, 85, 19], [106, 24, 85, 22, "styleValue"], [106, 34, 85, 32], [107, 6, 86, 4], [108, 4, 87, 2], [109, 4, 88, 2], [109, 11, 88, 9, "newStyle"], [109, 19, 88, 17], [110, 2, 89, 0], [111, 2, 89, 1], [111, 8, 89, 1, "_worklet_5061273221373_init_data"], [111, 40, 89, 1], [112, 4, 89, 1, "code"], [112, 8, 89, 1], [113, 4, 89, 1, "location"], [113, 12, 89, 1], [114, 4, 89, 1, "sourceMap"], [114, 13, 89, 1], [115, 4, 89, 1, "version"], [115, 11, 89, 1], [116, 2, 89, 1], [117, 2, 90, 7], [117, 8, 90, 13, "InlinePropManager"], [117, 25, 90, 30], [117, 26, 90, 31], [118, 4, 91, 2, "_inlinePropsViewDescriptors"], [118, 31, 91, 29], [118, 34, 91, 32], [118, 38, 91, 36], [119, 4, 92, 2, "_inlinePropsMapperId"], [119, 24, 92, 22], [119, 27, 92, 25], [119, 31, 92, 29], [120, 4, 93, 2, "_inlineProps"], [120, 16, 93, 14], [120, 19, 93, 17], [120, 20, 93, 18], [120, 21, 93, 19], [121, 4, 94, 2, "attachInlineProps"], [121, 21, 94, 19, "attachInlineProps"], [121, 22, 94, 20, "animatedComponent"], [121, 39, 94, 37], [121, 41, 94, 39, "viewInfo"], [121, 49, 94, 47], [121, 51, 94, 49], [122, 6, 95, 4], [122, 12, 95, 10, "newInlineProps"], [122, 26, 95, 24], [122, 29, 95, 27, "extractSharedValuesMapFromProps"], [122, 60, 95, 58], [122, 61, 95, 59, "animatedComponent"], [122, 78, 95, 76], [122, 79, 95, 77, "props"], [122, 84, 95, 82], [122, 85, 95, 83], [123, 6, 96, 4], [123, 12, 96, 10, "has<PERSON><PERSON>ed"], [123, 22, 96, 20], [123, 25, 96, 23, "inlinePropsHasChanged"], [123, 46, 96, 44], [123, 47, 96, 45, "newInlineProps"], [123, 61, 96, 59], [123, 63, 96, 61], [123, 67, 96, 65], [123, 68, 96, 66, "_inlineProps"], [123, 80, 96, 78], [123, 81, 96, 79], [124, 6, 97, 4], [124, 10, 97, 8, "has<PERSON><PERSON>ed"], [124, 20, 97, 18], [124, 22, 97, 20], [125, 8, 98, 6], [125, 12, 98, 10], [125, 13, 98, 11], [125, 17, 98, 15], [125, 18, 98, 16, "_inlinePropsViewDescriptors"], [125, 45, 98, 43], [125, 47, 98, 45], [126, 10, 99, 8], [126, 14, 99, 12], [126, 15, 99, 13, "_inlinePropsViewDescriptors"], [126, 42, 99, 40], [126, 45, 99, 43], [126, 49, 99, 43, "makeViewDescriptorsSet"], [126, 91, 99, 65], [126, 93, 99, 66], [126, 94, 99, 67], [127, 10, 100, 8], [127, 16, 100, 14], [128, 12, 101, 10, "viewTag"], [128, 19, 101, 17], [129, 12, 102, 10, "viewName"], [129, 20, 102, 18], [130, 12, 103, 10, "shadowNodeWrapper"], [130, 29, 103, 27], [131, 12, 104, 10, "viewConfig"], [132, 10, 105, 8], [132, 11, 105, 9], [132, 14, 105, 12, "viewInfo"], [132, 22, 105, 20], [133, 10, 106, 8], [133, 14, 106, 12, "Object"], [133, 20, 106, 18], [133, 21, 106, 19, "keys"], [133, 25, 106, 23], [133, 26, 106, 24, "newInlineProps"], [133, 40, 106, 38], [133, 41, 106, 39], [133, 42, 106, 40, "length"], [133, 48, 106, 46], [133, 52, 106, 50, "viewConfig"], [133, 62, 106, 60], [133, 64, 106, 62], [134, 12, 107, 10], [134, 16, 107, 10, "adaptViewConfig"], [134, 45, 107, 25], [134, 47, 107, 26, "viewConfig"], [134, 57, 107, 36], [134, 58, 107, 37], [135, 10, 108, 8], [136, 10, 109, 8], [136, 14, 109, 12], [136, 15, 109, 13, "_inlinePropsViewDescriptors"], [136, 42, 109, 40], [136, 43, 109, 41, "add"], [136, 46, 109, 44], [136, 47, 109, 45], [137, 12, 110, 10, "tag"], [137, 15, 110, 13], [137, 17, 110, 15, "viewTag"], [137, 24, 110, 22], [138, 12, 111, 10, "name"], [138, 16, 111, 14], [138, 18, 111, 16, "viewName"], [138, 26, 111, 24], [139, 12, 112, 10, "shadowNodeWrapper"], [139, 29, 112, 27], [139, 31, 112, 29, "shadowNodeWrapper"], [140, 10, 113, 8], [140, 11, 113, 9], [140, 12, 113, 10], [141, 8, 114, 6], [142, 8, 115, 6], [142, 14, 115, 12, "shareableViewDescriptors"], [142, 38, 115, 36], [142, 41, 115, 39], [142, 45, 115, 43], [142, 46, 115, 44, "_inlinePropsViewDescriptors"], [142, 73, 115, 71], [142, 74, 115, 72, "shareableViewDescriptors"], [142, 98, 115, 96], [143, 8, 116, 6], [143, 14, 116, 12, "updaterFunction"], [143, 29, 116, 27], [143, 32, 116, 30], [144, 10, 116, 30], [144, 16, 116, 30, "_e"], [144, 18, 116, 30], [144, 26, 116, 30, "global"], [144, 32, 116, 30], [144, 33, 116, 30, "Error"], [144, 38, 116, 30], [145, 10, 116, 30], [145, 16, 116, 30, "reactNativeReanimated_InlinePropManagerJs2"], [145, 58, 116, 30], [145, 70, 116, 30, "reactNativeReanimated_InlinePropManagerJs2"], [145, 71, 116, 30], [145, 73, 116, 36], [146, 12, 119, 8], [146, 18, 119, 14, "update"], [146, 24, 119, 20], [146, 27, 119, 23, "getInlinePropsUpdate"], [146, 47, 119, 43], [146, 48, 119, 44, "newInlineProps"], [146, 62, 119, 58], [146, 63, 119, 59], [147, 12, 120, 8], [147, 16, 120, 8, "updateProps"], [147, 34, 120, 19], [147, 36, 120, 20, "shareableViewDescriptors"], [147, 60, 120, 44], [147, 62, 120, 46, "update"], [147, 68, 120, 52], [147, 69, 120, 53], [148, 10, 121, 6], [148, 11, 121, 7], [149, 10, 121, 7, "reactNativeReanimated_InlinePropManagerJs2"], [149, 52, 121, 7], [149, 53, 121, 7, "__closure"], [149, 62, 121, 7], [150, 12, 121, 7, "getInlinePropsUpdate"], [150, 32, 121, 7], [151, 12, 121, 7, "newInlineProps"], [151, 26, 121, 7], [152, 12, 121, 7, "updateProps"], [152, 23, 121, 7], [152, 25, 120, 8, "updateProps"], [152, 43, 120, 19], [153, 12, 120, 19, "shareableViewDescriptors"], [154, 10, 120, 19], [155, 10, 120, 19, "reactNativeReanimated_InlinePropManagerJs2"], [155, 52, 120, 19], [155, 53, 120, 19, "__workletHash"], [155, 66, 120, 19], [156, 10, 120, 19, "reactNativeReanimated_InlinePropManagerJs2"], [156, 52, 120, 19], [156, 53, 120, 19, "__initData"], [156, 63, 120, 19], [156, 66, 120, 19, "_worklet_5061273221373_init_data"], [156, 98, 120, 19], [157, 10, 120, 19, "reactNativeReanimated_InlinePropManagerJs2"], [157, 52, 120, 19], [157, 53, 120, 19, "__stackDetails"], [157, 67, 120, 19], [157, 70, 120, 19, "_e"], [157, 72, 120, 19], [158, 10, 120, 19], [158, 17, 120, 19, "reactNativeReanimated_InlinePropManagerJs2"], [158, 59, 120, 19], [159, 8, 120, 19], [159, 9, 116, 30], [159, 11, 121, 7], [160, 8, 122, 6], [160, 12, 122, 10], [160, 13, 122, 11, "_inlineProps"], [160, 25, 122, 23], [160, 28, 122, 26, "newInlineProps"], [160, 42, 122, 40], [161, 8, 123, 6], [161, 12, 123, 10], [161, 16, 123, 14], [161, 17, 123, 15, "_inlinePropsMapperId"], [161, 37, 123, 35], [161, 39, 123, 37], [162, 10, 124, 8], [162, 14, 124, 8, "stopMapper"], [162, 33, 124, 18], [162, 35, 124, 19], [162, 39, 124, 23], [162, 40, 124, 24, "_inlinePropsMapperId"], [162, 60, 124, 44], [162, 61, 124, 45], [163, 8, 125, 6], [164, 8, 126, 6], [164, 12, 126, 10], [164, 13, 126, 11, "_inlinePropsMapperId"], [164, 33, 126, 31], [164, 36, 126, 34], [164, 40, 126, 38], [165, 8, 127, 6], [165, 12, 127, 10, "Object"], [165, 18, 127, 16], [165, 19, 127, 17, "keys"], [165, 23, 127, 21], [165, 24, 127, 22, "newInlineProps"], [165, 38, 127, 36], [165, 39, 127, 37], [165, 40, 127, 38, "length"], [165, 46, 127, 44], [165, 48, 127, 46], [166, 10, 128, 8], [166, 14, 128, 12], [166, 15, 128, 13, "_inlinePropsMapperId"], [166, 35, 128, 33], [166, 38, 128, 36], [166, 42, 128, 36, "startMapper"], [166, 62, 128, 47], [166, 64, 128, 48, "updaterFunction"], [166, 79, 128, 63], [166, 81, 128, 65, "Object"], [166, 87, 128, 71], [166, 88, 128, 72, "values"], [166, 94, 128, 78], [166, 95, 128, 79, "newInlineProps"], [166, 109, 128, 93], [166, 110, 128, 94], [166, 111, 128, 95], [167, 8, 129, 6], [168, 6, 130, 4], [169, 4, 131, 2], [170, 4, 132, 2, "detachInlineProps"], [170, 21, 132, 19, "detachInlineProps"], [170, 22, 132, 19], [170, 24, 132, 22], [171, 6, 133, 4], [171, 10, 133, 8], [171, 14, 133, 12], [171, 15, 133, 13, "_inlinePropsMapperId"], [171, 35, 133, 33], [171, 37, 133, 35], [172, 8, 134, 6], [172, 12, 134, 6, "stopMapper"], [172, 31, 134, 16], [172, 33, 134, 17], [172, 37, 134, 21], [172, 38, 134, 22, "_inlinePropsMapperId"], [172, 58, 134, 42], [172, 59, 134, 43], [173, 6, 135, 4], [174, 4, 136, 2], [175, 2, 137, 0], [176, 2, 137, 1, "exports"], [176, 9, 137, 1], [176, 10, 137, 1, "InlinePropManager"], [176, 27, 137, 1], [176, 30, 137, 1, "InlinePropManager"], [176, 47, 137, 1], [177, 0, 137, 1], [177, 3]], "functionMap": {"names": ["<global>", "isInlineStyleTransform", "transform.some$argument_0", "inlinePropsHasChanged", "getInlinePropsUpdate", "styleValue.map$argument_0", "extractSharedValuesMapFromProps", "styles.forEach$argument_0", "hasInlineStyles", "Object.keys.some$argument_0", "getInlineStyle", "InlinePropManager", "attachInlineProps", "updaterFunction", "detachInlineProps"], "mappings": "AAA;ACQ;wBCI,uBD;CDC;AGC;CHU;AIC;mCCQ;ODE;CJQ;AMC;qBCM;ODW;CNM;OQC;iCCI;GDG;CRC;OUC;CVW;OWC;ECI;8BCsB;ODK;GDU;EGC;GHI;CXC"}}, "type": "js/module"}]}