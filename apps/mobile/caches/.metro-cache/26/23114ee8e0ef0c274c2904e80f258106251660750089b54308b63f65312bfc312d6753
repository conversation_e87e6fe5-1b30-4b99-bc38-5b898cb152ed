{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 34, "index": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 35}, "end": {"line": 2, "column": 60, "index": 95}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LegacyEventEmitter = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[3], \"invariant\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var nativeEmitterSubscriptionKey = '@@nativeEmitterSubscription@@';\n  /**\n   * @deprecated Deprecated in favor of `EventEmitter`.\n   */\n  var LegacyEventEmitter = exports.LegacyEventEmitter = /*#__PURE__*/function () {\n    // @ts-expect-error\n\n    // @ts-expect-error\n\n    function LegacyEventEmitter(nativeModule) {\n      (0, _classCallCheck2.default)(this, LegacyEventEmitter);\n      this._listenerCount = 0;\n      // If the native module is a new module, just return it back as it's already an event emitter.\n      // This is for backwards compatibility until we stop using this legacy class in other packages.\n      if (nativeModule.__expo_module_name__) {\n        // @ts-expect-error\n        return nativeModule;\n      }\n      this._nativeModule = nativeModule;\n      this._eventEmitter = new _reactNative.NativeEventEmitter(nativeModule);\n    }\n    return (0, _createClass2.default)(LegacyEventEmitter, [{\n      key: \"addListener\",\n      value: function addListener(eventName, listener) {\n        if (!this._listenerCount && _reactNative.Platform.OS !== 'ios' && this._nativeModule.startObserving) {\n          this._nativeModule.startObserving();\n        }\n        this._listenerCount++;\n        var nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n        var subscription = {\n          [nativeEmitterSubscriptionKey]: nativeEmitterSubscription,\n          remove: () => {\n            this.removeSubscription(subscription);\n          }\n        };\n        return subscription;\n      }\n    }, {\n      key: \"removeAllListeners\",\n      value: function removeAllListeners(eventName) {\n        // @ts-ignore: the EventEmitter interface has been changed in react-native@0.64.0\n        var removedListenerCount = this._eventEmitter.listenerCount ?\n        // @ts-ignore: this is available since 0.64\n        this._eventEmitter.listenerCount(eventName) :\n        // @ts-ignore: this is available in older versions\n        this._eventEmitter.listeners(eventName).length;\n        this._eventEmitter.removeAllListeners(eventName);\n        this._listenerCount -= removedListenerCount;\n        (0, _invariant.default)(this._listenerCount >= 0, `EventEmitter must have a non-negative number of listeners`);\n        if (!this._listenerCount && _reactNative.Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n          this._nativeModule.stopObserving();\n        }\n      }\n    }, {\n      key: \"removeSubscription\",\n      value: function removeSubscription(subscription) {\n        var state = subscription;\n        var nativeEmitterSubscription = state[nativeEmitterSubscriptionKey];\n        if (!nativeEmitterSubscription) {\n          return;\n        }\n        if ('remove' in nativeEmitterSubscription) {\n          nativeEmitterSubscription.remove?.();\n        }\n        this._listenerCount--;\n\n        // Ensure that the emitter's internal state remains correct even if `removeSubscription` is\n        // called again with the same subscription\n        delete state[nativeEmitterSubscriptionKey];\n\n        // Release closed-over references to the emitter\n        subscription.remove = () => {};\n        if (!this._listenerCount && _reactNative.Platform.OS !== 'ios' && this._nativeModule.stopObserving) {\n          this._nativeModule.stopObserving();\n        }\n      }\n    }, {\n      key: \"emit\",\n      value: function emit(eventName) {\n        for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          params[_key - 1] = arguments[_key];\n        }\n        this._eventEmitter.emit(eventName, ...params);\n      }\n    }]);\n  }();\n});", "lineCount": 97, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_invariant"], [9, 16, 1, 0], [9, 19, 1, 0, "_interopRequireDefault"], [9, 41, 1, 0], [9, 42, 1, 0, "require"], [9, 49, 1, 0], [9, 50, 1, 0, "_dependencyMap"], [9, 64, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_reactNative"], [10, 18, 2, 0], [10, 21, 2, 0, "require"], [10, 28, 2, 0], [10, 29, 2, 0, "_dependencyMap"], [10, 43, 2, 0], [11, 2, 6, 0], [11, 6, 6, 6, "nativeEmitterSubscriptionKey"], [11, 34, 6, 34], [11, 37, 6, 37], [11, 68, 6, 77], [12, 2, 26, 0], [13, 0, 27, 0], [14, 0, 28, 0], [15, 2, 26, 0], [15, 6, 29, 13, "LegacyEventEmitter"], [15, 24, 29, 31], [15, 27, 29, 31, "exports"], [15, 34, 29, 31], [15, 35, 29, 31, "LegacyEventEmitter"], [15, 53, 29, 31], [16, 4, 32, 2], [18, 4, 35, 2], [20, 4, 38, 2], [20, 13, 38, 2, "LegacyEventEmitter"], [20, 32, 38, 14, "nativeModule"], [20, 44, 38, 40], [20, 46, 38, 42], [21, 6, 38, 42], [21, 10, 38, 42, "_classCallCheck2"], [21, 26, 38, 42], [21, 27, 38, 42, "default"], [21, 34, 38, 42], [21, 42, 38, 42, "LegacyEventEmitter"], [21, 60, 38, 42], [22, 6, 38, 42], [22, 11, 30, 2, "_listenerCount"], [22, 25, 30, 16], [22, 28, 30, 19], [22, 29, 30, 20], [23, 6, 39, 4], [24, 6, 40, 4], [25, 6, 41, 4], [25, 10, 41, 8, "nativeModule"], [25, 22, 41, 20], [25, 23, 41, 21, "__expo_module_name__"], [25, 43, 41, 41], [25, 45, 41, 43], [26, 8, 42, 6], [27, 8, 43, 6], [27, 15, 43, 13, "nativeModule"], [27, 27, 43, 25], [28, 6, 44, 4], [29, 6, 45, 4], [29, 10, 45, 8], [29, 11, 45, 9, "_nativeModule"], [29, 24, 45, 22], [29, 27, 45, 25, "nativeModule"], [29, 39, 45, 37], [30, 6, 46, 4], [30, 10, 46, 8], [30, 11, 46, 9, "_eventEmitter"], [30, 24, 46, 22], [30, 27, 46, 25], [30, 31, 46, 29, "NativeEventEmitter"], [30, 62, 46, 47], [30, 63, 46, 48, "nativeModule"], [30, 75, 46, 67], [30, 76, 46, 68], [31, 4, 47, 2], [32, 4, 47, 3], [32, 15, 47, 3, "_createClass2"], [32, 28, 47, 3], [32, 29, 47, 3, "default"], [32, 36, 47, 3], [32, 38, 47, 3, "LegacyEventEmitter"], [32, 56, 47, 3], [33, 6, 47, 3, "key"], [33, 9, 47, 3], [34, 6, 47, 3, "value"], [34, 11, 47, 3], [34, 13, 49, 2], [34, 22, 49, 2, "addListener"], [34, 33, 49, 13, "addListener"], [34, 34, 49, 17, "eventName"], [34, 43, 49, 34], [34, 45, 49, 36, "listener"], [34, 53, 49, 64], [34, 55, 49, 85], [35, 8, 50, 4], [35, 12, 50, 8], [35, 13, 50, 9], [35, 17, 50, 13], [35, 18, 50, 14, "_listenerCount"], [35, 32, 50, 28], [35, 36, 50, 32, "Platform"], [35, 57, 50, 40], [35, 58, 50, 41, "OS"], [35, 60, 50, 43], [35, 65, 50, 48], [35, 70, 50, 53], [35, 74, 50, 57], [35, 78, 50, 61], [35, 79, 50, 62, "_nativeModule"], [35, 92, 50, 75], [35, 93, 50, 76, "startObserving"], [35, 107, 50, 90], [35, 109, 50, 92], [36, 10, 51, 6], [36, 14, 51, 10], [36, 15, 51, 11, "_nativeModule"], [36, 28, 51, 24], [36, 29, 51, 25, "startObserving"], [36, 43, 51, 39], [36, 44, 51, 40], [36, 45, 51, 41], [37, 8, 52, 4], [38, 8, 54, 4], [38, 12, 54, 8], [38, 13, 54, 9, "_listenerCount"], [38, 27, 54, 23], [38, 29, 54, 25], [39, 8, 55, 4], [39, 12, 55, 10, "nativeEmitterSubscription"], [39, 37, 55, 35], [39, 40, 55, 38], [39, 44, 55, 42], [39, 45, 55, 43, "_eventEmitter"], [39, 58, 55, 56], [39, 59, 55, 57, "addListener"], [39, 70, 55, 68], [39, 71, 55, 69, "eventName"], [39, 80, 55, 78], [39, 82, 55, 80, "listener"], [39, 90, 55, 88], [39, 91, 55, 89], [40, 8, 56, 4], [40, 12, 56, 10, "subscription"], [40, 24, 56, 41], [40, 27, 56, 44], [41, 10, 57, 6], [41, 11, 57, 7, "nativeEmitterSubscriptionKey"], [41, 39, 57, 35], [41, 42, 57, 38, "nativeEmitterSubscription"], [41, 67, 57, 63], [42, 10, 58, 6, "remove"], [42, 16, 58, 12], [42, 18, 58, 14, "remove"], [42, 19, 58, 14], [42, 24, 58, 20], [43, 12, 59, 8], [43, 16, 59, 12], [43, 17, 59, 13, "removeSubscription"], [43, 35, 59, 31], [43, 36, 59, 32, "subscription"], [43, 48, 59, 44], [43, 49, 59, 45], [44, 10, 60, 6], [45, 8, 61, 4], [45, 9, 61, 5], [46, 8, 62, 4], [46, 15, 62, 11, "subscription"], [46, 27, 62, 23], [47, 6, 63, 2], [48, 4, 63, 3], [49, 6, 63, 3, "key"], [49, 9, 63, 3], [50, 6, 63, 3, "value"], [50, 11, 63, 3], [50, 13, 65, 2], [50, 22, 65, 2, "removeAllListeners"], [50, 40, 65, 20, "removeAllListeners"], [50, 41, 65, 21, "eventName"], [50, 50, 65, 38], [50, 52, 65, 46], [51, 8, 66, 4], [52, 8, 67, 4], [52, 12, 67, 10, "removedListenerCount"], [52, 32, 67, 30], [52, 35, 67, 33], [52, 39, 67, 37], [52, 40, 67, 38, "_eventEmitter"], [52, 53, 67, 51], [52, 54, 67, 52, "listenerCount"], [52, 67, 67, 65], [53, 8, 68, 8], [54, 8, 69, 8], [54, 12, 69, 12], [54, 13, 69, 13, "_eventEmitter"], [54, 26, 69, 26], [54, 27, 69, 27, "listenerCount"], [54, 40, 69, 40], [54, 41, 69, 41, "eventName"], [54, 50, 69, 50], [54, 51, 69, 51], [55, 8, 70, 8], [56, 8, 71, 8], [56, 12, 71, 12], [56, 13, 71, 13, "_eventEmitter"], [56, 26, 71, 26], [56, 27, 71, 27, "listeners"], [56, 36, 71, 36], [56, 37, 71, 37, "eventName"], [56, 46, 71, 46], [56, 47, 71, 47], [56, 48, 71, 48, "length"], [56, 54, 71, 54], [57, 8, 72, 4], [57, 12, 72, 8], [57, 13, 72, 9, "_eventEmitter"], [57, 26, 72, 22], [57, 27, 72, 23, "removeAllListeners"], [57, 45, 72, 41], [57, 46, 72, 42, "eventName"], [57, 55, 72, 51], [57, 56, 72, 52], [58, 8, 73, 4], [58, 12, 73, 8], [58, 13, 73, 9, "_listenerCount"], [58, 27, 73, 23], [58, 31, 73, 27, "removedListenerCount"], [58, 51, 73, 47], [59, 8, 74, 4], [59, 12, 74, 4, "invariant"], [59, 30, 74, 13], [59, 32, 75, 6], [59, 36, 75, 10], [59, 37, 75, 11, "_listenerCount"], [59, 51, 75, 25], [59, 55, 75, 29], [59, 56, 75, 30], [59, 58, 76, 6], [59, 117, 77, 4], [59, 118, 77, 5], [60, 8, 79, 4], [60, 12, 79, 8], [60, 13, 79, 9], [60, 17, 79, 13], [60, 18, 79, 14, "_listenerCount"], [60, 32, 79, 28], [60, 36, 79, 32, "Platform"], [60, 57, 79, 40], [60, 58, 79, 41, "OS"], [60, 60, 79, 43], [60, 65, 79, 48], [60, 70, 79, 53], [60, 74, 79, 57], [60, 78, 79, 61], [60, 79, 79, 62, "_nativeModule"], [60, 92, 79, 75], [60, 93, 79, 76, "stopObserving"], [60, 106, 79, 89], [60, 108, 79, 91], [61, 10, 80, 6], [61, 14, 80, 10], [61, 15, 80, 11, "_nativeModule"], [61, 28, 80, 24], [61, 29, 80, 25, "stopObserving"], [61, 42, 80, 38], [61, 43, 80, 39], [61, 44, 80, 40], [62, 8, 81, 4], [63, 6, 82, 2], [64, 4, 82, 3], [65, 6, 82, 3, "key"], [65, 9, 82, 3], [66, 6, 82, 3, "value"], [66, 11, 82, 3], [66, 13, 84, 2], [66, 22, 84, 2, "removeSubscription"], [66, 40, 84, 20, "removeSubscription"], [66, 41, 84, 21, "subscription"], [66, 53, 84, 52], [66, 55, 84, 60], [67, 8, 85, 4], [67, 12, 85, 10, "state"], [67, 17, 85, 15], [67, 20, 85, 18, "subscription"], [67, 32, 85, 51], [68, 8, 86, 4], [68, 12, 86, 10, "nativeEmitterSubscription"], [68, 37, 86, 35], [68, 40, 86, 38, "state"], [68, 45, 86, 43], [68, 46, 86, 44, "nativeEmitterSubscriptionKey"], [68, 74, 86, 72], [68, 75, 86, 73], [69, 8, 87, 4], [69, 12, 87, 8], [69, 13, 87, 9, "nativeEmitterSubscription"], [69, 38, 87, 34], [69, 40, 87, 36], [70, 10, 88, 6], [71, 8, 89, 4], [72, 8, 91, 4], [72, 12, 91, 8], [72, 20, 91, 16], [72, 24, 91, 20, "nativeEmitterSubscription"], [72, 49, 91, 45], [72, 51, 91, 47], [73, 10, 92, 6, "nativeEmitterSubscription"], [73, 35, 92, 31], [73, 36, 92, 32, "remove"], [73, 42, 92, 38], [73, 45, 92, 41], [73, 46, 92, 42], [74, 8, 93, 4], [75, 8, 94, 4], [75, 12, 94, 8], [75, 13, 94, 9, "_listenerCount"], [75, 27, 94, 23], [75, 29, 94, 25], [77, 8, 96, 4], [78, 8, 97, 4], [79, 8, 98, 4], [79, 15, 98, 11, "state"], [79, 20, 98, 16], [79, 21, 98, 17, "nativeEmitterSubscriptionKey"], [79, 49, 98, 45], [79, 50, 98, 46], [81, 8, 100, 4], [82, 8, 101, 4, "subscription"], [82, 20, 101, 16], [82, 21, 101, 17, "remove"], [82, 27, 101, 23], [82, 30, 101, 26], [82, 36, 101, 32], [82, 37, 101, 33], [82, 38, 101, 34], [83, 8, 103, 4], [83, 12, 103, 8], [83, 13, 103, 9], [83, 17, 103, 13], [83, 18, 103, 14, "_listenerCount"], [83, 32, 103, 28], [83, 36, 103, 32, "Platform"], [83, 57, 103, 40], [83, 58, 103, 41, "OS"], [83, 60, 103, 43], [83, 65, 103, 48], [83, 70, 103, 53], [83, 74, 103, 57], [83, 78, 103, 61], [83, 79, 103, 62, "_nativeModule"], [83, 92, 103, 75], [83, 93, 103, 76, "stopObserving"], [83, 106, 103, 89], [83, 108, 103, 91], [84, 10, 104, 6], [84, 14, 104, 10], [84, 15, 104, 11, "_nativeModule"], [84, 28, 104, 24], [84, 29, 104, 25, "stopObserving"], [84, 42, 104, 38], [84, 43, 104, 39], [84, 44, 104, 40], [85, 8, 105, 4], [86, 6, 106, 2], [87, 4, 106, 3], [88, 6, 106, 3, "key"], [88, 9, 106, 3], [89, 6, 106, 3, "value"], [89, 11, 106, 3], [89, 13, 108, 2], [89, 22, 108, 2, "emit"], [89, 26, 108, 6, "emit"], [89, 27, 108, 7, "eventName"], [89, 36, 108, 24], [89, 38, 108, 50], [90, 8, 108, 50], [90, 17, 108, 50, "_len"], [90, 21, 108, 50], [90, 24, 108, 50, "arguments"], [90, 33, 108, 50], [90, 34, 108, 50, "length"], [90, 40, 108, 50], [90, 42, 108, 29, "params"], [90, 48, 108, 35], [90, 55, 108, 35, "Array"], [90, 60, 108, 35], [90, 61, 108, 35, "_len"], [90, 65, 108, 35], [90, 72, 108, 35, "_len"], [90, 76, 108, 35], [90, 87, 108, 35, "_key"], [90, 91, 108, 35], [90, 97, 108, 35, "_key"], [90, 101, 108, 35], [90, 104, 108, 35, "_len"], [90, 108, 108, 35], [90, 110, 108, 35, "_key"], [90, 114, 108, 35], [91, 10, 108, 29, "params"], [91, 16, 108, 35], [91, 17, 108, 35, "_key"], [91, 21, 108, 35], [91, 29, 108, 35, "arguments"], [91, 38, 108, 35], [91, 39, 108, 35, "_key"], [91, 43, 108, 35], [92, 8, 108, 35], [93, 8, 109, 4], [93, 12, 109, 8], [93, 13, 109, 9, "_eventEmitter"], [93, 26, 109, 22], [93, 27, 109, 23, "emit"], [93, 31, 109, 27], [93, 32, 109, 28, "eventName"], [93, 41, 109, 37], [93, 43, 109, 39], [93, 46, 109, 42, "params"], [93, 52, 109, 48], [93, 53, 109, 49], [94, 6, 110, 2], [95, 4, 110, 3], [96, 2, 110, 3], [97, 0, 110, 3], [97, 3]], "functionMap": {"names": ["<global>", "LegacyEventEmitter", "constructor", "addListener", "subscription.remove", "removeAllListeners", "removeSubscription", "emit"], "mappings": "AAA;OC4B;ECS;GDS;EEE;cCS;ODE;GFG;EIE;GJiB;EKE;0BFiB,QE;GLK;EME;GNE;CDC"}}, "type": "js/module"}]}