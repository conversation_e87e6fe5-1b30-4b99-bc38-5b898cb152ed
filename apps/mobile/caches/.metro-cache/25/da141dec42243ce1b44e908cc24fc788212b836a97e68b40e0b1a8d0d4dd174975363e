{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./lib/sha1", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "NdCKGhmZPiEaY46/oShwIPGewDA=", "exportNames": ["*"]}}, {"name": "./lib/v35", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 28, "index": 59}}], "key": "UGP90HkLcwFn5oliBa4ByjSrgpQ=", "exportNames": ["*"]}}, {"name": "./uuid.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 60}, "end": {"line": 3, "column": 53, "index": 113}}], "key": "ShnVy+zJjUvhB99WMorBhtzZ4s0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _sha = _interopRequireDefault(require(_dependencyMap[1], \"./lib/sha1\"));\n  var _v = _interopRequireDefault(require(_dependencyMap[2], \"./lib/v35\"));\n  var _uuid = require(_dependencyMap[3], \"./uuid.types\");\n  function uuidv4() {\n    if (\n    // We use this code path in jest-expo.\n    process.env.NODE_ENV === 'test' ||\n    // Node.js has supported global crypto since v15.\n    typeof crypto === 'undefined' &&\n    // Only use abstract imports in server environments.\n    typeof window === 'undefined') {\n      // NOTE: Metro statically extracts all `require` statements to resolve them for environments\n      // that don't support `require` natively. Here we check if we're running in a server environment\n      // by using the standard `typeof window` check, then running `eval` to skip Metro's static\n      // analysis and keep the `require` statement intact for runtime evaluation.\n      // eslint-disable-next-line no-eval\n      return eval('require')('node:crypto').randomUUID();\n    }\n    return crypto.randomUUID();\n  }\n  const uuid = {\n    v4: uuidv4,\n    v5: (0, _v.default)('v5', 0x50, _sha.default),\n    namespace: _uuid.Uuidv5Namespace\n  };\n  var _default = exports.default = uuid;\n});", "lineCount": 33, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_sha"], [7, 10, 1, 0], [7, 13, 1, 0, "_interopRequireDefault"], [7, 35, 1, 0], [7, 36, 1, 0, "require"], [7, 43, 1, 0], [7, 44, 1, 0, "_dependencyMap"], [7, 58, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_v"], [8, 8, 2, 0], [8, 11, 2, 0, "_interopRequireDefault"], [8, 33, 2, 0], [8, 34, 2, 0, "require"], [8, 41, 2, 0], [8, 42, 2, 0, "_dependencyMap"], [8, 56, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_uuid"], [9, 11, 3, 0], [9, 14, 3, 0, "require"], [9, 21, 3, 0], [9, 22, 3, 0, "_dependencyMap"], [9, 36, 3, 0], [10, 2, 5, 0], [10, 11, 5, 9, "uuidv4"], [10, 17, 5, 15, "uuidv4"], [10, 18, 5, 15], [10, 20, 5, 26], [11, 4, 6, 2], [12, 4, 7, 4], [13, 4, 8, 4, "process"], [13, 11, 8, 11], [13, 12, 8, 12, "env"], [13, 15, 8, 15], [13, 16, 8, 16, "NODE_ENV"], [13, 24, 8, 24], [13, 29, 8, 29], [13, 35, 8, 35], [14, 4, 9, 4], [15, 4, 10, 5], [15, 11, 10, 12, "crypto"], [15, 17, 10, 18], [15, 22, 10, 23], [15, 33, 10, 34], [16, 4, 11, 6], [17, 4, 12, 6], [17, 11, 12, 13, "window"], [17, 17, 12, 19], [17, 22, 12, 24], [17, 33, 12, 36], [17, 35, 13, 4], [18, 6, 14, 4], [19, 6, 15, 4], [20, 6, 16, 4], [21, 6, 17, 4], [22, 6, 18, 4], [23, 6, 19, 4], [23, 13, 19, 11, "eval"], [23, 17, 19, 15], [23, 18, 19, 16], [23, 27, 19, 25], [23, 28, 19, 26], [23, 29, 19, 27], [23, 42, 19, 40], [23, 43, 19, 41], [23, 44, 19, 42, "randomUUID"], [23, 54, 19, 52], [23, 55, 19, 53], [23, 56, 19, 54], [24, 4, 20, 2], [25, 4, 22, 2], [25, 11, 22, 9, "crypto"], [25, 17, 22, 15], [25, 18, 22, 16, "randomUUID"], [25, 28, 22, 26], [25, 29, 22, 27], [25, 30, 22, 28], [26, 2, 23, 0], [27, 2, 25, 0], [27, 8, 25, 6, "uuid"], [27, 12, 25, 16], [27, 15, 25, 19], [28, 4, 26, 2, "v4"], [28, 6, 26, 4], [28, 8, 26, 6, "uuidv4"], [28, 14, 26, 12], [29, 4, 27, 2, "v5"], [29, 6, 27, 4], [29, 8, 27, 6], [29, 12, 27, 6, "v35"], [29, 22, 27, 9], [29, 24, 27, 10], [29, 28, 27, 14], [29, 30, 27, 16], [29, 34, 27, 20], [29, 36, 27, 22, "sha1"], [29, 48, 27, 26], [29, 49, 27, 27], [30, 4, 28, 2, "namespace"], [30, 13, 28, 11], [30, 15, 28, 13, "Uuidv5Namespace"], [31, 2, 29, 0], [31, 3, 29, 1], [32, 2, 29, 2], [32, 6, 29, 2, "_default"], [32, 14, 29, 2], [32, 17, 29, 2, "exports"], [32, 24, 29, 2], [32, 25, 29, 2, "default"], [32, 32, 29, 2], [32, 35, 31, 15, "uuid"], [32, 39, 31, 19], [33, 0, 31, 19], [33, 3]], "functionMap": {"names": ["<global>", "uuidv4"], "mappings": "AAA;ACI;CDkB"}}, "type": "js/module"}]}