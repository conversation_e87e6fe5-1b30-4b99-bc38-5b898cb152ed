{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "./IndiscreteGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 37}, "end": {"line": 2, "column": 66, "index": 103}}], "key": "d7/3lZQfxBXmNrwL5D+QBRLWiAg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _IndiscreteGestureHandler = _interopRequireDefault(require(_dependencyMap[2], \"./IndiscreteGestureHandler\"));\n  class PinchGestureHandler extends _IndiscreteGestureHandler.default {\n    get name() {\n      return 'pinch';\n    }\n    get NativeGestureClass() {\n      return _hammerjs.default.Pinch;\n    }\n    transformNativeEvent({\n      scale,\n      velocity,\n      center\n    }) {\n      return {\n        focalX: center.x,\n        focalY: center.y,\n        velocity,\n        scale\n      };\n    }\n  }\n  var _default = exports.default = PinchGestureHandler;\n});", "lineCount": 30, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_hammerjs"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_IndiscreteGestureHandler"], [8, 31, 2, 0], [8, 34, 2, 0, "_interopRequireDefault"], [8, 56, 2, 0], [8, 57, 2, 0, "require"], [8, 64, 2, 0], [8, 65, 2, 0, "_dependencyMap"], [8, 79, 2, 0], [9, 2, 4, 0], [9, 8, 4, 6, "PinchGestureHandler"], [9, 27, 4, 25], [9, 36, 4, 34, "IndiscreteGestureHandler"], [9, 69, 4, 58], [9, 70, 4, 59], [10, 4, 5, 2], [10, 8, 5, 6, "name"], [10, 12, 5, 10, "name"], [10, 13, 5, 10], [10, 15, 5, 13], [11, 6, 6, 4], [11, 13, 6, 11], [11, 20, 6, 18], [12, 4, 7, 2], [13, 4, 9, 2], [13, 8, 9, 6, "NativeGestureClass"], [13, 26, 9, 24, "NativeGestureClass"], [13, 27, 9, 24], [13, 29, 9, 27], [14, 6, 10, 4], [14, 13, 10, 11, "Hammer"], [14, 30, 10, 17], [14, 31, 10, 18, "Pinch"], [14, 36, 10, 23], [15, 4, 11, 2], [16, 4, 13, 2, "transformNativeEvent"], [16, 24, 13, 22, "transformNativeEvent"], [16, 25, 13, 23], [17, 6, 14, 4, "scale"], [17, 11, 14, 9], [18, 6, 15, 4, "velocity"], [18, 14, 15, 12], [19, 6, 16, 4, "center"], [20, 4, 17, 2], [20, 5, 17, 3], [20, 7, 17, 5], [21, 6, 18, 4], [21, 13, 18, 11], [22, 8, 19, 6, "focalX"], [22, 14, 19, 12], [22, 16, 19, 14, "center"], [22, 22, 19, 20], [22, 23, 19, 21, "x"], [22, 24, 19, 22], [23, 8, 20, 6, "focalY"], [23, 14, 20, 12], [23, 16, 20, 14, "center"], [23, 22, 20, 20], [23, 23, 20, 21, "y"], [23, 24, 20, 22], [24, 8, 21, 6, "velocity"], [24, 16, 21, 14], [25, 8, 22, 6, "scale"], [26, 6, 23, 4], [26, 7, 23, 5], [27, 4, 24, 2], [28, 2, 26, 0], [29, 2, 26, 1], [29, 6, 26, 1, "_default"], [29, 14, 26, 1], [29, 17, 26, 1, "exports"], [29, 24, 26, 1], [29, 25, 26, 1, "default"], [29, 32, 26, 1], [29, 35, 28, 15, "PinchGestureHandler"], [29, 54, 28, 34], [30, 0, 28, 34], [30, 3]], "functionMap": {"names": ["<global>", "PinchGestureHandler", "get__name", "get__NativeGestureClass", "transformNativeEvent"], "mappings": "AAA;ACG;ECC;GDE;EEE;GFE;EGE;GHW;CDE"}}, "type": "js/module"}]}