{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = extractLengthList;\n  var spaceReg = /\\s+/;\n  var commaReg = /,/g;\n  function extractLengthList(lengthList) {\n    if (Array.isArray(lengthList)) {\n      return lengthList;\n    } else if (typeof lengthList === 'number') {\n      return [lengthList];\n    } else if (typeof lengthList === 'string') {\n      return lengthList.trim().replace(commaReg, ' ').split(spaceReg);\n    } else {\n      return [];\n    }\n  }\n});", "lineCount": 19, "map": [[6, 2, 3, 0], [6, 6, 3, 6, "spaceReg"], [6, 14, 3, 14], [6, 17, 3, 17], [6, 22, 3, 22], [7, 2, 4, 0], [7, 6, 4, 6, "commaReg"], [7, 14, 4, 14], [7, 17, 4, 17], [7, 21, 4, 21], [8, 2, 6, 15], [8, 11, 6, 24, "extractLengthList"], [8, 28, 6, 41, "extractLengthList"], [8, 29, 7, 2, "lengthList"], [8, 39, 7, 49], [8, 41, 8, 25], [9, 4, 9, 2], [9, 8, 9, 6, "Array"], [9, 13, 9, 11], [9, 14, 9, 12, "isArray"], [9, 21, 9, 19], [9, 22, 9, 20, "lengthList"], [9, 32, 9, 30], [9, 33, 9, 31], [9, 35, 9, 33], [10, 6, 10, 4], [10, 13, 10, 11, "lengthList"], [10, 23, 10, 21], [11, 4, 11, 2], [11, 5, 11, 3], [11, 11, 11, 9], [11, 15, 11, 13], [11, 22, 11, 20, "lengthList"], [11, 32, 11, 30], [11, 37, 11, 35], [11, 45, 11, 43], [11, 47, 11, 45], [12, 6, 12, 4], [12, 13, 12, 11], [12, 14, 12, 12, "lengthList"], [12, 24, 12, 22], [12, 25, 12, 23], [13, 4, 13, 2], [13, 5, 13, 3], [13, 11, 13, 9], [13, 15, 13, 13], [13, 22, 13, 20, "lengthList"], [13, 32, 13, 30], [13, 37, 13, 35], [13, 45, 13, 43], [13, 47, 13, 45], [14, 6, 14, 4], [14, 13, 14, 11, "lengthList"], [14, 23, 14, 21], [14, 24, 14, 22, "trim"], [14, 28, 14, 26], [14, 29, 14, 27], [14, 30, 14, 28], [14, 31, 14, 29, "replace"], [14, 38, 14, 36], [14, 39, 14, 37, "commaReg"], [14, 47, 14, 45], [14, 49, 14, 47], [14, 52, 14, 50], [14, 53, 14, 51], [14, 54, 14, 52, "split"], [14, 59, 14, 57], [14, 60, 14, 58, "spaceReg"], [14, 68, 14, 66], [14, 69, 14, 67], [15, 4, 15, 2], [15, 5, 15, 3], [15, 11, 15, 9], [16, 6, 16, 4], [16, 13, 16, 11], [16, 15, 16, 13], [17, 4, 17, 2], [18, 2, 18, 0], [19, 0, 18, 1], [19, 3]], "functionMap": {"names": ["<global>", "extractLengthList"], "mappings": "AAA;eCK;CDY"}}, "type": "js/module"}]}