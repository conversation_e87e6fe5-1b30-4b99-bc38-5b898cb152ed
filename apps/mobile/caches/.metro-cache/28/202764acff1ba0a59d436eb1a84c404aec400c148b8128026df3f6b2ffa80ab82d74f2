{"dependencies": [{"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 23, "index": 195}, "end": {"line": 5, "column": 46, "index": 218}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "../domComponents/emitDomEvent", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 23, "index": 243}, "end": {"line": 6, "column": 63, "index": 283}}], "key": "ytOKgRKkGsnNX3Cjp8ZQVtQ04/A=", "exportNames": ["*"]}}, {"name": "../fork/getPathFromState-forks", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 33, "index": 318}, "end": {"line": 7, "column": 74, "index": 359}}], "key": "Pwl2s8MefiXMrO7elNt6TT2k9Bo=", "exportNames": ["*"]}}, {"name": "../global-state/routing", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 18, "index": 379}, "end": {"line": 8, "column": 52, "index": 413}}], "key": "Cqbl9MOLPE8L7igowwxv8ngZpbk=", "exportNames": ["*"]}}, {"name": "../matchers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 19, "index": 434}, "end": {"line": 9, "column": 41, "index": 456}}], "key": "lD+VV93WPi10A3qv5+9m649ytvA=", "exportNames": ["*"]}}, {"name": "../utils/url", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 14, "index": 472}, "end": {"line": 10, "column": 37, "index": 495}}], "key": "cr4Bw7JAHaE1T5Tb4Y1vtviexXs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _objectWithoutProperties = require(_dependencyMap[0], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"href\"];\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useLinkToPathProps;\n  exports.shouldHandleMouseEvent = shouldHandleMouseEvent;\n  var react_native_1 = require(_dependencyMap[1], \"react-native\");\n  var emitDomEvent_1 = require(_dependencyMap[2], \"../domComponents/emitDomEvent\");\n  var getPathFromState_forks_1 = require(_dependencyMap[3], \"../fork/getPathFromState-forks\");\n  var routing_1 = require(_dependencyMap[4], \"../global-state/routing\");\n  var matchers_1 = require(_dependencyMap[5], \"../matchers\");\n  var url_1 = require(_dependencyMap[6], \"../utils/url\");\n  function eventShouldPreventDefault(e) {\n    if (e?.defaultPrevented) {\n      return false;\n    }\n    if (\n    // Only check MouseEvents\n    'button' in e &&\n    // ignore clicks with modifier keys\n    !e.metaKey && !e.altKey && !e.ctrlKey && !e.shiftKey && (e.button == null || e.button === 0) &&\n    // Only accept left clicks\n    [undefined, null, '', 'self'].includes(e.currentTarget.target) // let browser handle \"target=_blank\" etc.\n    ) {\n      return true;\n    }\n    return false;\n  }\n  function useLinkToPathProps(_ref) {\n    var href = _ref.href,\n      options = _objectWithoutProperties(_ref, _excluded);\n    var onPress = event => {\n      if (shouldHandleMouseEvent(event)) {\n        if ((0, emitDomEvent_1.emitDomLinkEvent)(href, options)) {\n          return;\n        }\n        (0, routing_1.linkTo)(href, options);\n      }\n    };\n    var strippedHref = (0, matchers_1.stripGroupSegmentsFromPath)(href) || '/';\n    // Append base url only if needed.\n    if (!(0, url_1.shouldLinkExternally)(strippedHref)) {\n      strippedHref = (0, getPathFromState_forks_1.appendBaseUrl)(strippedHref);\n    }\n    return {\n      href: strippedHref,\n      role: 'link',\n      onPress\n    };\n  }\n  function shouldHandleMouseEvent(event) {\n    if (react_native_1.Platform.OS !== 'web') {\n      return !event?.defaultPrevented;\n    }\n    if (event && eventShouldPreventDefault(event)) {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n});", "lineCount": 65, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_objectWithoutProperties"], [4, 30, 1, 13], [4, 33, 1, 13, "require"], [4, 40, 1, 13], [4, 41, 1, 13, "_dependencyMap"], [4, 55, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_excluded"], [5, 15, 1, 13], [6, 2, 2, 0, "Object"], [6, 8, 2, 6], [6, 9, 2, 7, "defineProperty"], [6, 23, 2, 21], [6, 24, 2, 22, "exports"], [6, 31, 2, 29], [6, 33, 2, 31], [6, 45, 2, 43], [6, 47, 2, 45], [7, 4, 2, 47, "value"], [7, 9, 2, 52], [7, 11, 2, 54], [8, 2, 2, 59], [8, 3, 2, 60], [8, 4, 2, 61], [9, 2, 3, 0, "exports"], [9, 9, 3, 7], [9, 10, 3, 8, "default"], [9, 17, 3, 15], [9, 20, 3, 18, "useLinkToPathProps"], [9, 38, 3, 36], [10, 2, 4, 0, "exports"], [10, 9, 4, 7], [10, 10, 4, 8, "shouldHandleMouseEvent"], [10, 32, 4, 30], [10, 35, 4, 33, "shouldHandleMouseEvent"], [10, 57, 4, 55], [11, 2, 5, 0], [11, 6, 5, 6, "react_native_1"], [11, 20, 5, 20], [11, 23, 5, 23, "require"], [11, 30, 5, 30], [11, 31, 5, 30, "_dependencyMap"], [11, 45, 5, 30], [11, 64, 5, 45], [11, 65, 5, 46], [12, 2, 6, 0], [12, 6, 6, 6, "emitDomEvent_1"], [12, 20, 6, 20], [12, 23, 6, 23, "require"], [12, 30, 6, 30], [12, 31, 6, 30, "_dependencyMap"], [12, 45, 6, 30], [12, 81, 6, 62], [12, 82, 6, 63], [13, 2, 7, 0], [13, 6, 7, 6, "getPathFromState_forks_1"], [13, 30, 7, 30], [13, 33, 7, 33, "require"], [13, 40, 7, 40], [13, 41, 7, 40, "_dependencyMap"], [13, 55, 7, 40], [13, 92, 7, 73], [13, 93, 7, 74], [14, 2, 8, 0], [14, 6, 8, 6, "routing_1"], [14, 15, 8, 15], [14, 18, 8, 18, "require"], [14, 25, 8, 25], [14, 26, 8, 25, "_dependencyMap"], [14, 40, 8, 25], [14, 70, 8, 51], [14, 71, 8, 52], [15, 2, 9, 0], [15, 6, 9, 6, "matchers_1"], [15, 16, 9, 16], [15, 19, 9, 19, "require"], [15, 26, 9, 26], [15, 27, 9, 26, "_dependencyMap"], [15, 41, 9, 26], [15, 59, 9, 40], [15, 60, 9, 41], [16, 2, 10, 0], [16, 6, 10, 6, "url_1"], [16, 11, 10, 11], [16, 14, 10, 14, "require"], [16, 21, 10, 21], [16, 22, 10, 21, "_dependencyMap"], [16, 36, 10, 21], [16, 55, 10, 36], [16, 56, 10, 37], [17, 2, 11, 0], [17, 11, 11, 9, "eventShouldPreventDefault"], [17, 36, 11, 34, "eventShouldPreventDefault"], [17, 37, 11, 35, "e"], [17, 38, 11, 36], [17, 40, 11, 38], [18, 4, 12, 4], [18, 8, 12, 8, "e"], [18, 9, 12, 9], [18, 11, 12, 11, "defaultPrevented"], [18, 27, 12, 27], [18, 29, 12, 29], [19, 6, 13, 8], [19, 13, 13, 15], [19, 18, 13, 20], [20, 4, 14, 4], [21, 4, 15, 4], [22, 4, 16, 4], [23, 4, 17, 4], [23, 12, 17, 12], [23, 16, 17, 16, "e"], [23, 17, 17, 17], [24, 4, 18, 8], [25, 4, 19, 8], [25, 5, 19, 9, "e"], [25, 6, 19, 10], [25, 7, 19, 11, "metaKey"], [25, 14, 19, 18], [25, 18, 20, 8], [25, 19, 20, 9, "e"], [25, 20, 20, 10], [25, 21, 20, 11, "altKey"], [25, 27, 20, 17], [25, 31, 21, 8], [25, 32, 21, 9, "e"], [25, 33, 21, 10], [25, 34, 21, 11, "ctrl<PERSON>ey"], [25, 41, 21, 18], [25, 45, 22, 8], [25, 46, 22, 9, "e"], [25, 47, 22, 10], [25, 48, 22, 11, "shift<PERSON>ey"], [25, 56, 22, 19], [25, 61, 23, 9, "e"], [25, 62, 23, 10], [25, 63, 23, 11, "button"], [25, 69, 23, 17], [25, 73, 23, 21], [25, 77, 23, 25], [25, 81, 23, 29, "e"], [25, 82, 23, 30], [25, 83, 23, 31, "button"], [25, 89, 23, 37], [25, 94, 23, 42], [25, 95, 23, 43], [25, 96, 23, 44], [26, 4, 23, 48], [27, 4, 24, 8], [27, 5, 24, 9, "undefined"], [27, 14, 24, 18], [27, 16, 24, 20], [27, 20, 24, 24], [27, 22, 24, 26], [27, 24, 24, 28], [27, 26, 24, 30], [27, 32, 24, 36], [27, 33, 24, 37], [27, 34, 24, 38, "includes"], [27, 42, 24, 46], [27, 43, 24, 47, "e"], [27, 44, 24, 48], [27, 45, 24, 49, "currentTarget"], [27, 58, 24, 62], [27, 59, 24, 63, "target"], [27, 65, 24, 69], [27, 66, 24, 70], [27, 67, 24, 71], [28, 4, 24, 71], [28, 6, 25, 6], [29, 6, 26, 8], [29, 13, 26, 15], [29, 17, 26, 19], [30, 4, 27, 4], [31, 4, 28, 4], [31, 11, 28, 11], [31, 16, 28, 16], [32, 2, 29, 0], [33, 2, 30, 0], [33, 11, 30, 9, "useLinkToPathProps"], [33, 29, 30, 27, "useLinkToPathProps"], [33, 30, 30, 27, "_ref"], [33, 34, 30, 27], [33, 36, 30, 50], [34, 4, 30, 50], [34, 8, 30, 30, "href"], [34, 12, 30, 34], [34, 15, 30, 34, "_ref"], [34, 19, 30, 34], [34, 20, 30, 30, "href"], [34, 24, 30, 34], [35, 6, 30, 39, "options"], [35, 13, 30, 46], [35, 16, 30, 46, "_objectWithoutProperties"], [35, 40, 30, 46], [35, 41, 30, 46, "_ref"], [35, 45, 30, 46], [35, 47, 30, 46, "_excluded"], [35, 56, 30, 46], [36, 4, 31, 4], [36, 8, 31, 10, "onPress"], [36, 15, 31, 17], [36, 18, 31, 21, "event"], [36, 23, 31, 26], [36, 27, 31, 31], [37, 6, 32, 8], [37, 10, 32, 12, "shouldHandleMouseEvent"], [37, 32, 32, 34], [37, 33, 32, 35, "event"], [37, 38, 32, 40], [37, 39, 32, 41], [37, 41, 32, 43], [38, 8, 33, 12], [38, 12, 33, 16], [38, 13, 33, 17], [38, 14, 33, 18], [38, 16, 33, 20, "emitDomEvent_1"], [38, 30, 33, 34], [38, 31, 33, 35, "emitDomLinkEvent"], [38, 47, 33, 51], [38, 49, 33, 53, "href"], [38, 53, 33, 57], [38, 55, 33, 59, "options"], [38, 62, 33, 66], [38, 63, 33, 67], [38, 65, 33, 69], [39, 10, 34, 16], [40, 8, 35, 12], [41, 8, 36, 12], [41, 9, 36, 13], [41, 10, 36, 14], [41, 12, 36, 16, "routing_1"], [41, 21, 36, 25], [41, 22, 36, 26, "linkTo"], [41, 28, 36, 32], [41, 30, 36, 34, "href"], [41, 34, 36, 38], [41, 36, 36, 40, "options"], [41, 43, 36, 47], [41, 44, 36, 48], [42, 6, 37, 8], [43, 4, 38, 4], [43, 5, 38, 5], [44, 4, 39, 4], [44, 8, 39, 8, "<PERSON><PERSON><PERSON>f"], [44, 20, 39, 20], [44, 23, 39, 23], [44, 24, 39, 24], [44, 25, 39, 25], [44, 27, 39, 27, "matchers_1"], [44, 37, 39, 37], [44, 38, 39, 38, "stripGroupSegmentsFromPath"], [44, 64, 39, 64], [44, 66, 39, 66, "href"], [44, 70, 39, 70], [44, 71, 39, 71], [44, 75, 39, 75], [44, 78, 39, 78], [45, 4, 40, 4], [46, 4, 41, 4], [46, 8, 41, 8], [46, 9, 41, 9], [46, 10, 41, 10], [46, 11, 41, 11], [46, 13, 41, 13, "url_1"], [46, 18, 41, 18], [46, 19, 41, 19, "shouldLinkExternally"], [46, 39, 41, 39], [46, 41, 41, 41, "<PERSON><PERSON><PERSON>f"], [46, 53, 41, 53], [46, 54, 41, 54], [46, 56, 41, 56], [47, 6, 42, 8, "<PERSON><PERSON><PERSON>f"], [47, 18, 42, 20], [47, 21, 42, 23], [47, 22, 42, 24], [47, 23, 42, 25], [47, 25, 42, 27, "getPathFromState_forks_1"], [47, 49, 42, 51], [47, 50, 42, 52, "appendBaseUrl"], [47, 63, 42, 65], [47, 65, 42, 67, "<PERSON><PERSON><PERSON>f"], [47, 77, 42, 79], [47, 78, 42, 80], [48, 4, 43, 4], [49, 4, 44, 4], [49, 11, 44, 11], [50, 6, 45, 8, "href"], [50, 10, 45, 12], [50, 12, 45, 14, "<PERSON><PERSON><PERSON>f"], [50, 24, 45, 26], [51, 6, 46, 8, "role"], [51, 10, 46, 12], [51, 12, 46, 14], [51, 18, 46, 20], [52, 6, 47, 8, "onPress"], [53, 4, 48, 4], [53, 5, 48, 5], [54, 2, 49, 0], [55, 2, 50, 0], [55, 11, 50, 9, "shouldHandleMouseEvent"], [55, 33, 50, 31, "shouldHandleMouseEvent"], [55, 34, 50, 32, "event"], [55, 39, 50, 37], [55, 41, 50, 39], [56, 4, 51, 4], [56, 8, 51, 8, "react_native_1"], [56, 22, 51, 22], [56, 23, 51, 23, "Platform"], [56, 31, 51, 31], [56, 32, 51, 32, "OS"], [56, 34, 51, 34], [56, 39, 51, 39], [56, 44, 51, 44], [56, 46, 51, 46], [57, 6, 52, 8], [57, 13, 52, 15], [57, 14, 52, 16, "event"], [57, 19, 52, 21], [57, 21, 52, 23, "defaultPrevented"], [57, 37, 52, 39], [58, 4, 53, 4], [59, 4, 54, 4], [59, 8, 54, 8, "event"], [59, 13, 54, 13], [59, 17, 54, 17, "eventShouldPreventDefault"], [59, 42, 54, 42], [59, 43, 54, 43, "event"], [59, 48, 54, 48], [59, 49, 54, 49], [59, 51, 54, 51], [60, 6, 55, 8, "event"], [60, 11, 55, 13], [60, 12, 55, 14, "preventDefault"], [60, 26, 55, 28], [60, 27, 55, 29], [60, 28, 55, 30], [61, 6, 56, 8], [61, 13, 56, 15], [61, 17, 56, 19], [62, 4, 57, 4], [63, 4, 58, 4], [63, 11, 58, 11], [63, 16, 58, 16], [64, 2, 59, 0], [65, 0, 59, 1], [65, 3]], "functionMap": {"names": ["<global>", "eventShouldPreventDefault", "useLinkToPathProps", "onPress", "shouldHandleMouseEvent"], "mappings": "AAA;ACU;CDkB;AEC;oBCC;KDO;CFW;AIC;CJS"}}, "type": "js/module"}]}