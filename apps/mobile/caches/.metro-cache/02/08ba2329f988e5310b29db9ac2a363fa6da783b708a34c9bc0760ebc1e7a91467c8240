{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 987}, "end": {"line": 33, "column": 40, "index": 1027}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "@tanstack/react-query", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1028}, "end": {"line": 34, "column": 68, "index": 1096}}], "key": "Pzwu/0TIyhnZOrC9PAkpZx92hFo=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _expoRouter = require(_dependencyMap[21], \"expo-router\");\n  var _reactQuery = require(_dependencyMap[22], \"@tanstack/react-query\");\n  var _jsxDevRuntime = require(_dependencyMap[23], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const router = (0, _expoRouter.useRouter)();\n    const queryClient = (0, _reactQuery.useQueryClient)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0, _react.useState)(null);\n    const [sessionStartTime, setSessionStartTime] = (0, _react.useState)(null);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = async () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n      setSessionStartTime(new Date());\n\n      // Create a new session in the database\n      try {\n        const response = await fetch('/api/sessions', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            title: 'New Brainstorming Session',\n            agent_type: 'general',\n            user_id: null // For now, anonymous sessions\n          })\n        });\n        if (response.ok) {\n          const {\n            session\n          } = await response.json();\n          setCurrentSessionId(session.id);\n\n          // Add initial messages to the session\n          for (const message of _fakeData.fakeMessages) {\n            await fetch('/api/messages', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                session_id: session.id,\n                role: message.role,\n                content: message.content\n              })\n            });\n          }\n        }\n      } catch (error) {\n        console.error('Error creating session:', error);\n      }\n    };\n    const handleSendMessage = (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"902VswXJGTYh6FyaM254sBNxdF8=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _expoRouter.useRouter, _reactQuery.useQueryClient, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 644, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 33, 0], [27, 6, 33, 0, "_expoRouter"], [27, 17, 33, 0], [27, 20, 33, 0, "require"], [27, 27, 33, 0], [27, 28, 33, 0, "_dependencyMap"], [27, 42, 33, 0], [28, 2, 34, 0], [28, 6, 34, 0, "_reactQuery"], [28, 17, 34, 0], [28, 20, 34, 0, "require"], [28, 27, 34, 0], [28, 28, 34, 0, "_dependencyMap"], [28, 42, 34, 0], [29, 2, 34, 68], [29, 6, 34, 68, "_jsxDevRuntime"], [29, 20, 34, 68], [29, 23, 34, 68, "require"], [29, 30, 34, 68], [29, 31, 34, 68, "_dependencyMap"], [29, 45, 34, 68], [30, 2, 34, 68], [30, 6, 34, 68, "_jsxFileName"], [30, 18, 34, 68], [31, 4, 34, 68, "_s"], [31, 6, 34, 68], [31, 9, 34, 68, "$RefreshSig$"], [31, 21, 34, 68], [32, 2, 34, 68], [32, 11, 34, 68, "_interopRequireWildcard"], [32, 35, 34, 68, "e"], [32, 36, 34, 68], [32, 38, 34, 68, "t"], [32, 39, 34, 68], [32, 68, 34, 68, "WeakMap"], [32, 75, 34, 68], [32, 81, 34, 68, "r"], [32, 82, 34, 68], [32, 89, 34, 68, "WeakMap"], [32, 96, 34, 68], [32, 100, 34, 68, "n"], [32, 101, 34, 68], [32, 108, 34, 68, "WeakMap"], [32, 115, 34, 68], [32, 127, 34, 68, "_interopRequireWildcard"], [32, 150, 34, 68], [32, 162, 34, 68, "_interopRequireWildcard"], [32, 163, 34, 68, "e"], [32, 164, 34, 68], [32, 166, 34, 68, "t"], [32, 167, 34, 68], [32, 176, 34, 68, "t"], [32, 177, 34, 68], [32, 181, 34, 68, "e"], [32, 182, 34, 68], [32, 186, 34, 68, "e"], [32, 187, 34, 68], [32, 188, 34, 68, "__esModule"], [32, 198, 34, 68], [32, 207, 34, 68, "e"], [32, 208, 34, 68], [32, 214, 34, 68, "o"], [32, 215, 34, 68], [32, 217, 34, 68, "i"], [32, 218, 34, 68], [32, 220, 34, 68, "f"], [32, 221, 34, 68], [32, 226, 34, 68, "__proto__"], [32, 235, 34, 68], [32, 243, 34, 68, "default"], [32, 250, 34, 68], [32, 252, 34, 68, "e"], [32, 253, 34, 68], [32, 270, 34, 68, "e"], [32, 271, 34, 68], [32, 294, 34, 68, "e"], [32, 295, 34, 68], [32, 320, 34, 68, "e"], [32, 321, 34, 68], [32, 330, 34, 68, "f"], [32, 331, 34, 68], [32, 337, 34, 68, "o"], [32, 338, 34, 68], [32, 341, 34, 68, "t"], [32, 342, 34, 68], [32, 345, 34, 68, "n"], [32, 346, 34, 68], [32, 349, 34, 68, "r"], [32, 350, 34, 68], [32, 358, 34, 68, "o"], [32, 359, 34, 68], [32, 360, 34, 68, "has"], [32, 363, 34, 68], [32, 364, 34, 68, "e"], [32, 365, 34, 68], [32, 375, 34, 68, "o"], [32, 376, 34, 68], [32, 377, 34, 68, "get"], [32, 380, 34, 68], [32, 381, 34, 68, "e"], [32, 382, 34, 68], [32, 385, 34, 68, "o"], [32, 386, 34, 68], [32, 387, 34, 68, "set"], [32, 390, 34, 68], [32, 391, 34, 68, "e"], [32, 392, 34, 68], [32, 394, 34, 68, "f"], [32, 395, 34, 68], [32, 411, 34, 68, "t"], [32, 412, 34, 68], [32, 416, 34, 68, "e"], [32, 417, 34, 68], [32, 433, 34, 68, "t"], [32, 434, 34, 68], [32, 441, 34, 68, "hasOwnProperty"], [32, 455, 34, 68], [32, 456, 34, 68, "call"], [32, 460, 34, 68], [32, 461, 34, 68, "e"], [32, 462, 34, 68], [32, 464, 34, 68, "t"], [32, 465, 34, 68], [32, 472, 34, 68, "i"], [32, 473, 34, 68], [32, 477, 34, 68, "o"], [32, 478, 34, 68], [32, 481, 34, 68, "Object"], [32, 487, 34, 68], [32, 488, 34, 68, "defineProperty"], [32, 502, 34, 68], [32, 507, 34, 68, "Object"], [32, 513, 34, 68], [32, 514, 34, 68, "getOwnPropertyDescriptor"], [32, 538, 34, 68], [32, 539, 34, 68, "e"], [32, 540, 34, 68], [32, 542, 34, 68, "t"], [32, 543, 34, 68], [32, 550, 34, 68, "i"], [32, 551, 34, 68], [32, 552, 34, 68, "get"], [32, 555, 34, 68], [32, 559, 34, 68, "i"], [32, 560, 34, 68], [32, 561, 34, 68, "set"], [32, 564, 34, 68], [32, 568, 34, 68, "o"], [32, 569, 34, 68], [32, 570, 34, 68, "f"], [32, 571, 34, 68], [32, 573, 34, 68, "t"], [32, 574, 34, 68], [32, 576, 34, 68, "i"], [32, 577, 34, 68], [32, 581, 34, 68, "f"], [32, 582, 34, 68], [32, 583, 34, 68, "t"], [32, 584, 34, 68], [32, 588, 34, 68, "e"], [32, 589, 34, 68], [32, 590, 34, 68, "t"], [32, 591, 34, 68], [32, 602, 34, 68, "f"], [32, 603, 34, 68], [32, 608, 34, 68, "e"], [32, 609, 34, 68], [32, 611, 34, 68, "t"], [32, 612, 34, 68], [33, 2, 36, 15], [33, 11, 36, 24, "BrainstormScreen"], [33, 27, 36, 40, "BrainstormScreen"], [33, 28, 36, 40], [33, 30, 36, 43], [34, 4, 36, 43, "_s"], [34, 6, 36, 43], [35, 4, 37, 2], [35, 10, 37, 8, "insets"], [35, 16, 37, 14], [35, 19, 37, 17], [35, 23, 37, 17, "useSafeAreaInsets"], [35, 68, 37, 34], [35, 70, 37, 35], [35, 71, 37, 36], [36, 4, 38, 2], [36, 10, 38, 8, "colors"], [36, 16, 38, 14], [36, 19, 38, 17], [36, 23, 38, 17, "useColors"], [36, 43, 38, 26], [36, 45, 38, 27], [36, 46, 38, 28], [37, 4, 39, 2], [37, 10, 39, 8, "router"], [37, 16, 39, 14], [37, 19, 39, 17], [37, 23, 39, 17, "useRouter"], [37, 44, 39, 26], [37, 46, 39, 27], [37, 47, 39, 28], [38, 4, 40, 2], [38, 10, 40, 8, "queryClient"], [38, 21, 40, 19], [38, 24, 40, 22], [38, 28, 40, 22, "useQueryClient"], [38, 54, 40, 36], [38, 56, 40, 37], [38, 57, 40, 38], [39, 4, 41, 2], [39, 10, 41, 8], [39, 11, 41, 9, "fontsLoaded"], [39, 22, 41, 20], [39, 23, 41, 21], [39, 26, 41, 24], [39, 30, 41, 24, "useFonts"], [39, 47, 41, 32], [39, 49, 41, 33], [40, 6, 42, 4, "Poppins_400Regular"], [40, 24, 42, 22], [40, 26, 42, 4, "Poppins_400Regular"], [40, 53, 42, 22], [41, 6, 43, 4, "Poppins_500Medium"], [41, 23, 43, 21], [41, 25, 43, 4, "Poppins_500Medium"], [41, 51, 43, 21], [42, 6, 44, 4, "Poppins_600SemiBold"], [42, 25, 44, 23], [42, 27, 44, 4, "Poppins_600SemiBold"], [43, 4, 45, 2], [43, 5, 45, 3], [43, 6, 45, 4], [44, 4, 47, 2], [44, 10, 47, 8, "recorder"], [44, 18, 47, 16], [44, 21, 47, 19], [44, 25, 47, 19, "useAudioRecorder"], [44, 52, 47, 35], [44, 54, 47, 36, "RecordingPresets"], [44, 81, 47, 52], [44, 82, 47, 53, "HIGH_QUALITY"], [44, 94, 47, 65], [44, 95, 47, 66], [45, 4, 48, 2], [45, 10, 48, 8, "recorderState"], [45, 23, 48, 21], [45, 26, 48, 24], [45, 30, 48, 24, "useAudioRecorderState"], [45, 62, 48, 45], [45, 64, 48, 46, "recorder"], [45, 72, 48, 54], [45, 73, 48, 55], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "messages"], [46, 19, 50, 17], [46, 21, 50, 19, "setMessages"], [46, 32, 50, 30], [46, 33, 50, 31], [46, 36, 50, 34], [46, 40, 50, 34, "useState"], [46, 55, 50, 42], [46, 57, 50, 43], [46, 59, 50, 45], [46, 60, 50, 46], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "inputText"], [47, 20, 51, 18], [47, 22, 51, 20, "setInputText"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 61, 51, 47], [47, 62, 51, 48], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "isFirstTime"], [48, 22, 52, 20], [48, 24, 52, 22, "setIsFirstTime"], [48, 38, 52, 36], [48, 39, 52, 37], [48, 42, 52, 40], [48, 46, 52, 40, "useState"], [48, 61, 52, 48], [48, 63, 52, 49], [48, 67, 52, 53], [48, 68, 52, 54], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isLoading"], [49, 20, 53, 18], [49, 22, 53, 20, "setIsLoading"], [49, 34, 53, 32], [49, 35, 53, 33], [49, 38, 53, 36], [49, 42, 53, 36, "useState"], [49, 57, 53, 44], [49, 59, 53, 45], [49, 64, 53, 50], [49, 65, 53, 51], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "quickActions"], [50, 23, 54, 21], [50, 25, 54, 23, "setQuickActions"], [50, 40, 54, 38], [50, 41, 54, 39], [50, 44, 54, 42], [50, 48, 54, 42, "useState"], [50, 63, 54, 50], [50, 65, 54, 51], [50, 67, 54, 53], [50, 68, 54, 54], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "voiceMode"], [51, 20, 55, 18], [51, 22, 55, 20, "setVoiceMode"], [51, 34, 55, 32], [51, 35, 55, 33], [51, 38, 55, 36], [51, 42, 55, 36, "useState"], [51, 57, 55, 44], [51, 59, 55, 45], [51, 63, 55, 49], [51, 64, 55, 50], [51, 65, 55, 51], [51, 66, 55, 52], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "hasPermission"], [52, 24, 56, 22], [52, 26, 56, 24, "setHasPermission"], [52, 42, 56, 40], [52, 43, 56, 41], [52, 46, 56, 44], [52, 50, 56, 44, "useState"], [52, 65, 56, 52], [52, 67, 56, 53], [52, 72, 56, 58], [52, 73, 56, 59], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isDictating"], [53, 22, 57, 20], [53, 24, 57, 22, "setIsDictating"], [53, 38, 57, 36], [53, 39, 57, 37], [53, 42, 57, 40], [53, 46, 57, 40, "useState"], [53, 61, 57, 48], [53, 63, 57, 49], [53, 68, 57, 54], [53, 69, 57, 55], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "longPressedMessage"], [54, 29, 58, 27], [54, 31, 58, 29, "setLongPressedMessage"], [54, 52, 58, 50], [54, 53, 58, 51], [54, 56, 58, 54], [54, 60, 58, 54, "useState"], [54, 75, 58, 62], [54, 77, 58, 63], [54, 81, 58, 67], [54, 82, 58, 68], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "isContextMenuVisible"], [55, 31, 59, 29], [55, 33, 59, 31, "setIsContextMenuVisible"], [55, 56, 59, 54], [55, 57, 59, 55], [55, 60, 59, 58], [55, 64, 59, 58, "useState"], [55, 79, 59, 66], [55, 81, 59, 67], [55, 86, 59, 72], [55, 87, 59, 73], [56, 4, 60, 2], [56, 10, 60, 8], [56, 11, 60, 9, "transcript"], [56, 21, 60, 19], [56, 23, 60, 21, "setTranscript"], [56, 36, 60, 34], [56, 37, 60, 35], [56, 40, 60, 38], [56, 44, 60, 38, "useState"], [56, 59, 60, 46], [56, 61, 60, 47], [56, 63, 60, 49], [56, 64, 60, 50], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "isMuted"], [57, 18, 61, 16], [57, 20, 61, 18, "setIsMuted"], [57, 30, 61, 28], [57, 31, 61, 29], [57, 34, 61, 32], [57, 38, 61, 32, "useState"], [57, 53, 61, 40], [57, 55, 61, 41], [57, 60, 61, 46], [57, 61, 61, 47], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "currentSessionId"], [58, 27, 62, 25], [58, 29, 62, 27, "setCurrentSessionId"], [58, 48, 62, 46], [58, 49, 62, 47], [58, 52, 62, 50], [58, 56, 62, 50, "useState"], [58, 71, 62, 58], [58, 73, 62, 59], [58, 77, 62, 63], [58, 78, 62, 64], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "sessionStartTime"], [59, 27, 63, 25], [59, 29, 63, 27, "setSessionStartTime"], [59, 48, 63, 46], [59, 49, 63, 47], [59, 52, 63, 50], [59, 56, 63, 50, "useState"], [59, 71, 63, 58], [59, 73, 63, 59], [59, 77, 63, 63], [59, 78, 63, 64], [60, 4, 65, 2], [60, 10, 65, 8, "scrollViewRef"], [60, 23, 65, 21], [60, 26, 65, 24], [60, 30, 65, 24, "useRef"], [60, 43, 65, 30], [60, 45, 65, 31], [60, 49, 65, 35], [60, 50, 65, 36], [62, 4, 67, 2], [63, 4, 68, 2], [63, 8, 68, 2, "useEffect"], [63, 24, 68, 11], [63, 26, 68, 12], [63, 32, 68, 18], [64, 6, 69, 4], [64, 7, 69, 5], [64, 19, 69, 17], [65, 8, 70, 6], [65, 14, 70, 12], [66, 10, 70, 14, "granted"], [67, 8, 70, 22], [67, 9, 70, 23], [67, 12, 70, 26], [67, 18, 70, 32], [67, 22, 70, 32, "requestRecordingPermissionsAsync"], [67, 65, 70, 64], [67, 67, 70, 65], [67, 68, 70, 66], [68, 8, 71, 6, "setHasPermission"], [68, 24, 71, 22], [68, 25, 71, 23, "granted"], [68, 32, 71, 30], [68, 33, 71, 31], [69, 8, 72, 6], [69, 12, 72, 10], [69, 13, 72, 11, "granted"], [69, 20, 72, 18], [69, 24, 72, 22, "voiceMode"], [69, 33, 72, 31], [69, 35, 72, 33], [70, 10, 73, 8, "<PERSON><PERSON>"], [70, 24, 73, 13], [70, 25, 73, 14, "alert"], [70, 30, 73, 19], [70, 31, 74, 10], [70, 52, 74, 31], [70, 54, 75, 10], [70, 133, 75, 89], [70, 135, 76, 10], [70, 136, 77, 12], [71, 12, 77, 14, "text"], [71, 16, 77, 18], [71, 18, 77, 20], [71, 33, 77, 35], [72, 12, 77, 37, "onPress"], [72, 19, 77, 44], [72, 21, 77, 46, "onPress"], [72, 22, 77, 46], [72, 27, 77, 52, "setVoiceMode"], [72, 39, 77, 64], [72, 40, 77, 65], [72, 45, 77, 70], [73, 10, 77, 72], [73, 11, 77, 73], [73, 13, 78, 12], [74, 12, 78, 14, "text"], [74, 16, 78, 18], [74, 18, 78, 20], [75, 10, 78, 25], [75, 11, 78, 26], [75, 12, 80, 8], [75, 13, 80, 9], [76, 8, 81, 6], [77, 6, 82, 4], [77, 7, 82, 5], [77, 9, 82, 7], [77, 10, 82, 8], [78, 4, 83, 2], [78, 5, 83, 3], [78, 7, 83, 5], [78, 8, 83, 6, "voiceMode"], [78, 17, 83, 15], [78, 18, 83, 16], [78, 19, 83, 17], [79, 4, 85, 2], [79, 10, 85, 8, "handleStartBrainstorming"], [79, 34, 85, 32], [79, 37, 85, 35], [79, 43, 85, 35, "handleStartBrainstorming"], [79, 44, 85, 35], [79, 49, 85, 47], [80, 6, 86, 4, "Haptics"], [80, 13, 86, 11], [80, 14, 86, 12, "impactAsync"], [80, 25, 86, 23], [80, 26, 86, 24, "Haptics"], [80, 33, 86, 31], [80, 34, 86, 32, "ImpactFeedbackStyle"], [80, 53, 86, 51], [80, 54, 86, 52, "Medium"], [80, 60, 86, 58], [80, 61, 86, 59], [81, 6, 87, 4, "setIsFirstTime"], [81, 20, 87, 18], [81, 21, 87, 19], [81, 26, 87, 24], [81, 27, 87, 25], [82, 6, 88, 4, "setMessages"], [82, 17, 88, 15], [82, 18, 88, 16, "fakeMessages"], [82, 40, 88, 28], [82, 41, 88, 29], [83, 6, 89, 4, "setQuickActions"], [83, 21, 89, 19], [83, 22, 89, 20, "fakeQuickActions"], [83, 48, 89, 36], [83, 49, 89, 37], [84, 6, 90, 4, "setSessionStartTime"], [84, 25, 90, 23], [84, 26, 90, 24], [84, 30, 90, 28, "Date"], [84, 34, 90, 32], [84, 35, 90, 33], [84, 36, 90, 34], [84, 37, 90, 35], [86, 6, 92, 4], [87, 6, 93, 4], [87, 10, 93, 8], [88, 8, 94, 6], [88, 14, 94, 12, "response"], [88, 22, 94, 20], [88, 25, 94, 23], [88, 31, 94, 29, "fetch"], [88, 36, 94, 34], [88, 37, 94, 35], [88, 52, 94, 50], [88, 54, 94, 52], [89, 10, 95, 8, "method"], [89, 16, 95, 14], [89, 18, 95, 16], [89, 24, 95, 22], [90, 10, 96, 8, "headers"], [90, 17, 96, 15], [90, 19, 96, 17], [91, 12, 96, 19], [91, 26, 96, 33], [91, 28, 96, 35], [92, 10, 96, 54], [92, 11, 96, 55], [93, 10, 97, 8, "body"], [93, 14, 97, 12], [93, 16, 97, 14, "JSON"], [93, 20, 97, 18], [93, 21, 97, 19, "stringify"], [93, 30, 97, 28], [93, 31, 97, 29], [94, 12, 98, 10, "title"], [94, 17, 98, 15], [94, 19, 98, 17], [94, 46, 98, 44], [95, 12, 99, 10, "agent_type"], [95, 22, 99, 20], [95, 24, 99, 22], [95, 33, 99, 31], [96, 12, 100, 10, "user_id"], [96, 19, 100, 17], [96, 21, 100, 19], [96, 25, 100, 23], [96, 26, 100, 25], [97, 10, 101, 8], [97, 11, 101, 9], [98, 8, 102, 6], [98, 9, 102, 7], [98, 10, 102, 8], [99, 8, 104, 6], [99, 12, 104, 10, "response"], [99, 20, 104, 18], [99, 21, 104, 19, "ok"], [99, 23, 104, 21], [99, 25, 104, 23], [100, 10, 105, 8], [100, 16, 105, 14], [101, 12, 105, 16, "session"], [102, 10, 105, 24], [102, 11, 105, 25], [102, 14, 105, 28], [102, 20, 105, 34, "response"], [102, 28, 105, 42], [102, 29, 105, 43, "json"], [102, 33, 105, 47], [102, 34, 105, 48], [102, 35, 105, 49], [103, 10, 106, 8, "setCurrentSessionId"], [103, 29, 106, 27], [103, 30, 106, 28, "session"], [103, 37, 106, 35], [103, 38, 106, 36, "id"], [103, 40, 106, 38], [103, 41, 106, 39], [105, 10, 108, 8], [106, 10, 109, 8], [106, 15, 109, 13], [106, 21, 109, 19, "message"], [106, 28, 109, 26], [106, 32, 109, 30, "fakeMessages"], [106, 54, 109, 42], [106, 56, 109, 44], [107, 12, 110, 10], [107, 18, 110, 16, "fetch"], [107, 23, 110, 21], [107, 24, 110, 22], [107, 39, 110, 37], [107, 41, 110, 39], [108, 14, 111, 12, "method"], [108, 20, 111, 18], [108, 22, 111, 20], [108, 28, 111, 26], [109, 14, 112, 12, "headers"], [109, 21, 112, 19], [109, 23, 112, 21], [110, 16, 112, 23], [110, 30, 112, 37], [110, 32, 112, 39], [111, 14, 112, 58], [111, 15, 112, 59], [112, 14, 113, 12, "body"], [112, 18, 113, 16], [112, 20, 113, 18, "JSON"], [112, 24, 113, 22], [112, 25, 113, 23, "stringify"], [112, 34, 113, 32], [112, 35, 113, 33], [113, 16, 114, 14, "session_id"], [113, 26, 114, 24], [113, 28, 114, 26, "session"], [113, 35, 114, 33], [113, 36, 114, 34, "id"], [113, 38, 114, 36], [114, 16, 115, 14, "role"], [114, 20, 115, 18], [114, 22, 115, 20, "message"], [114, 29, 115, 27], [114, 30, 115, 28, "role"], [114, 34, 115, 32], [115, 16, 116, 14, "content"], [115, 23, 116, 21], [115, 25, 116, 23, "message"], [115, 32, 116, 30], [115, 33, 116, 31, "content"], [116, 14, 117, 12], [116, 15, 117, 13], [117, 12, 118, 10], [117, 13, 118, 11], [117, 14, 118, 12], [118, 10, 119, 8], [119, 8, 120, 6], [120, 6, 121, 4], [120, 7, 121, 5], [120, 8, 121, 6], [120, 15, 121, 13, "error"], [120, 20, 121, 18], [120, 22, 121, 20], [121, 8, 122, 6, "console"], [121, 15, 122, 13], [121, 16, 122, 14, "error"], [121, 21, 122, 19], [121, 22, 122, 20], [121, 47, 122, 45], [121, 49, 122, 47, "error"], [121, 54, 122, 52], [121, 55, 122, 53], [122, 6, 123, 4], [123, 4, 124, 2], [123, 5, 124, 3], [124, 4, 126, 2], [124, 10, 126, 8, "handleSendMessage"], [124, 27, 126, 25], [124, 30, 126, 28, "handleSendMessage"], [124, 31, 126, 29, "message"], [124, 38, 126, 36], [124, 41, 126, 39, "inputText"], [124, 50, 126, 48], [124, 55, 126, 53], [125, 6, 127, 4], [125, 10, 127, 8], [125, 11, 127, 9, "message"], [125, 18, 127, 16], [125, 19, 127, 17, "trim"], [125, 23, 127, 21], [125, 24, 127, 22], [125, 25, 127, 23], [125, 27, 127, 25], [126, 6, 129, 4], [126, 12, 129, 10, "newUserMessage"], [126, 26, 129, 24], [126, 29, 129, 27], [127, 8, 129, 29, "role"], [127, 12, 129, 33], [127, 14, 129, 35], [127, 20, 129, 41], [128, 8, 129, 43, "content"], [128, 15, 129, 50], [128, 17, 129, 52, "message"], [129, 6, 129, 60], [129, 7, 129, 61], [130, 6, 130, 4, "setMessages"], [130, 17, 130, 15], [130, 18, 130, 17, "prev"], [130, 22, 130, 21], [130, 26, 130, 26], [130, 27, 130, 27], [130, 30, 130, 30, "prev"], [130, 34, 130, 34], [130, 36, 130, 36, "newUserMessage"], [130, 50, 130, 50], [130, 51, 130, 51], [130, 52, 130, 52], [131, 6, 131, 4, "setInputText"], [131, 18, 131, 16], [131, 19, 131, 17], [131, 21, 131, 19], [131, 22, 131, 20], [132, 6, 132, 4, "setQuickActions"], [132, 21, 132, 19], [132, 22, 132, 20], [132, 24, 132, 22], [132, 25, 132, 23], [133, 6, 133, 4, "setIsLoading"], [133, 18, 133, 16], [133, 19, 133, 17], [133, 23, 133, 21], [133, 24, 133, 22], [135, 6, 135, 4], [136, 6, 136, 4, "setTimeout"], [136, 16, 136, 14], [136, 17, 136, 15], [136, 23, 136, 21], [137, 8, 137, 6], [137, 14, 137, 12, "aiResponse"], [137, 24, 137, 22], [137, 27, 137, 25], [138, 10, 138, 8, "role"], [138, 14, 138, 12], [138, 16, 138, 14], [138, 27, 138, 25], [139, 10, 139, 8, "content"], [139, 17, 139, 15], [139, 19, 139, 17], [140, 8, 140, 6], [140, 9, 140, 7], [141, 8, 141, 6, "setMessages"], [141, 19, 141, 17], [141, 20, 141, 19, "prev"], [141, 24, 141, 23], [141, 28, 141, 28], [141, 29, 141, 29], [141, 32, 141, 32, "prev"], [141, 36, 141, 36], [141, 38, 141, 38, "aiResponse"], [141, 48, 141, 48], [141, 49, 141, 49], [141, 50, 141, 50], [142, 8, 142, 6, "setQuickActions"], [142, 23, 142, 21], [142, 24, 142, 22, "fakeQuickActions"], [142, 50, 142, 38], [142, 51, 142, 39], [143, 8, 143, 6, "setIsLoading"], [143, 20, 143, 18], [143, 21, 143, 19], [143, 26, 143, 24], [143, 27, 143, 25], [144, 6, 144, 4], [144, 7, 144, 5], [144, 9, 144, 7], [144, 13, 144, 11], [144, 14, 144, 12], [145, 4, 145, 2], [145, 5, 145, 3], [146, 4, 147, 2], [146, 10, 147, 8, "handleQuickAction"], [146, 27, 147, 25], [146, 30, 147, 29, "action"], [146, 36, 147, 35], [146, 40, 147, 40], [147, 6, 148, 4, "Haptics"], [147, 13, 148, 11], [147, 14, 148, 12, "impactAsync"], [147, 25, 148, 23], [147, 26, 148, 24, "Haptics"], [147, 33, 148, 31], [147, 34, 148, 32, "ImpactFeedbackStyle"], [147, 53, 148, 51], [147, 54, 148, 52, "Light"], [147, 59, 148, 57], [147, 60, 148, 58], [148, 6, 149, 4, "handleSendMessage"], [148, 23, 149, 21], [148, 24, 149, 22, "action"], [148, 30, 149, 28], [148, 31, 149, 29], [149, 4, 150, 2], [149, 5, 150, 3], [150, 4, 152, 2], [150, 10, 152, 8, "toggleVoiceMode"], [150, 25, 152, 23], [150, 28, 152, 26], [150, 34, 152, 26, "toggleVoiceMode"], [150, 35, 152, 26], [150, 40, 152, 38], [151, 6, 153, 4], [151, 10, 153, 8], [151, 11, 153, 9, "voiceMode"], [151, 20, 153, 18], [151, 24, 153, 22], [151, 25, 153, 23, "hasPermission"], [151, 38, 153, 36], [151, 40, 153, 38], [152, 8, 154, 6, "<PERSON><PERSON>"], [152, 22, 154, 11], [152, 23, 154, 12, "alert"], [152, 28, 154, 17], [152, 29, 155, 8], [152, 50, 155, 29], [152, 52, 156, 8], [152, 102, 156, 58], [152, 104, 157, 8], [152, 105, 158, 10], [153, 10, 158, 12, "text"], [153, 14, 158, 16], [153, 16, 158, 18], [154, 8, 158, 27], [154, 9, 158, 28], [154, 11, 159, 10], [155, 10, 160, 12, "text"], [155, 14, 160, 16], [155, 16, 160, 18], [155, 34, 160, 36], [156, 10, 161, 12, "onPress"], [156, 17, 161, 19], [156, 19, 161, 21], [156, 25, 161, 21, "onPress"], [156, 26, 161, 21], [156, 31, 161, 33], [157, 12, 162, 14], [157, 18, 162, 20], [158, 14, 162, 22, "granted"], [159, 12, 162, 30], [159, 13, 162, 31], [159, 16, 162, 34], [159, 22, 162, 40], [159, 26, 162, 40, "requestRecordingPermissionsAsync"], [159, 69, 162, 72], [159, 71, 162, 73], [159, 72, 162, 74], [160, 12, 163, 14], [160, 16, 163, 18, "granted"], [160, 23, 163, 25], [160, 25, 163, 27], [161, 14, 164, 16, "setHasPermission"], [161, 30, 164, 32], [161, 31, 164, 33], [161, 35, 164, 37], [161, 36, 164, 38], [162, 14, 165, 16, "setVoiceMode"], [162, 26, 165, 28], [162, 27, 165, 29], [162, 31, 165, 33], [162, 32, 165, 34], [163, 14, 166, 16], [164, 14, 167, 16], [164, 20, 167, 22, "startVoiceSession"], [164, 37, 167, 39], [164, 38, 167, 40], [164, 39, 167, 41], [165, 12, 168, 14], [166, 10, 169, 12], [167, 8, 170, 10], [167, 9, 170, 11], [167, 10, 172, 6], [167, 11, 172, 7], [168, 8, 173, 6], [169, 6, 174, 4], [170, 6, 176, 4, "Haptics"], [170, 13, 176, 11], [170, 14, 176, 12, "impactAsync"], [170, 25, 176, 23], [170, 26, 176, 24, "Haptics"], [170, 33, 176, 31], [170, 34, 176, 32, "ImpactFeedbackStyle"], [170, 53, 176, 51], [170, 54, 176, 52, "Light"], [170, 59, 176, 57], [170, 60, 176, 58], [171, 6, 178, 4], [171, 10, 178, 8], [171, 11, 178, 9, "voiceMode"], [171, 20, 178, 18], [171, 22, 178, 20], [172, 8, 179, 6], [173, 8, 180, 6, "setVoiceMode"], [173, 20, 180, 18], [173, 21, 180, 19], [173, 25, 180, 23], [173, 26, 180, 24], [174, 8, 181, 6], [174, 12, 181, 10, "hasPermission"], [174, 25, 181, 23], [174, 27, 181, 25], [175, 10, 182, 8], [175, 16, 182, 14, "startVoiceSession"], [175, 33, 182, 31], [175, 34, 182, 32], [175, 35, 182, 33], [176, 8, 183, 6], [177, 6, 184, 4], [177, 7, 184, 5], [177, 13, 184, 11], [178, 8, 185, 6], [179, 8, 186, 6, "setVoiceMode"], [179, 20, 186, 18], [179, 21, 186, 19], [179, 26, 186, 24], [179, 27, 186, 25], [180, 8, 187, 6], [180, 12, 187, 10, "recorderState"], [180, 25, 187, 23], [180, 26, 187, 24, "isRecording"], [180, 37, 187, 35], [180, 39, 187, 37], [181, 10, 188, 8], [181, 16, 188, 14, "recorder"], [181, 24, 188, 22], [181, 25, 188, 23, "stop"], [181, 29, 188, 27], [181, 30, 188, 28], [181, 31, 188, 29], [182, 8, 189, 6], [183, 8, 190, 6, "setIsMuted"], [183, 18, 190, 16], [183, 19, 190, 17], [183, 24, 190, 22], [183, 25, 190, 23], [183, 26, 190, 24], [183, 27, 190, 25], [184, 6, 191, 4], [185, 4, 192, 2], [185, 5, 192, 3], [186, 4, 194, 2], [186, 10, 194, 8, "startVoiceSession"], [186, 27, 194, 25], [186, 30, 194, 28], [186, 36, 194, 28, "startVoiceSession"], [186, 37, 194, 28], [186, 42, 194, 40], [187, 6, 195, 4], [187, 10, 195, 8], [187, 11, 195, 9, "hasPermission"], [187, 24, 195, 22], [187, 26, 195, 24], [188, 6, 197, 4], [188, 10, 197, 8], [189, 8, 198, 6], [189, 14, 198, 12, "recorder"], [189, 22, 198, 20], [189, 23, 198, 21, "prepareToRecordAsync"], [189, 43, 198, 41], [189, 44, 198, 42], [189, 45, 198, 43], [190, 8, 199, 6, "recorder"], [190, 16, 199, 14], [190, 17, 199, 15, "record"], [190, 23, 199, 21], [190, 24, 199, 22], [190, 25, 199, 23], [191, 8, 200, 6, "setIsMuted"], [191, 18, 200, 16], [191, 19, 200, 17], [191, 24, 200, 22], [191, 25, 200, 23], [191, 26, 200, 24], [191, 27, 200, 25], [192, 8, 201, 6, "Haptics"], [192, 15, 201, 13], [192, 16, 201, 14, "impactAsync"], [192, 27, 201, 25], [192, 28, 201, 26, "Haptics"], [192, 35, 201, 33], [192, 36, 201, 34, "ImpactFeedbackStyle"], [192, 55, 201, 53], [192, 56, 201, 54, "Medium"], [192, 62, 201, 60], [192, 63, 201, 61], [193, 6, 202, 4], [193, 7, 202, 5], [193, 8, 202, 6], [193, 15, 202, 13, "error"], [193, 20, 202, 18], [193, 22, 202, 20], [194, 8, 203, 6, "console"], [194, 15, 203, 13], [194, 16, 203, 14, "error"], [194, 21, 203, 19], [194, 22, 203, 20], [194, 53, 203, 51], [194, 55, 203, 53, "error"], [194, 60, 203, 58], [194, 61, 203, 59], [195, 8, 204, 6, "<PERSON><PERSON>"], [195, 22, 204, 11], [195, 23, 204, 12, "alert"], [195, 28, 204, 17], [195, 29, 204, 18], [195, 36, 204, 25], [195, 38, 204, 27], [195, 69, 204, 58], [195, 70, 204, 59], [196, 6, 205, 4], [197, 4, 206, 2], [197, 5, 206, 3], [198, 4, 208, 2], [198, 10, 208, 8, "stopVoiceSession"], [198, 26, 208, 24], [198, 29, 208, 27], [198, 35, 208, 27, "stopVoiceSession"], [198, 36, 208, 27], [198, 41, 208, 39], [199, 6, 209, 4], [199, 10, 209, 8], [200, 8, 210, 6], [200, 12, 210, 10, "recorderState"], [200, 25, 210, 23], [200, 26, 210, 24, "isRecording"], [200, 37, 210, 35], [200, 39, 210, 37], [201, 10, 211, 8], [201, 16, 211, 14, "recorder"], [201, 24, 211, 22], [201, 25, 211, 23, "stop"], [201, 29, 211, 27], [201, 30, 211, 28], [201, 31, 211, 29], [202, 10, 212, 8, "Haptics"], [202, 17, 212, 15], [202, 18, 212, 16, "impactAsync"], [202, 29, 212, 27], [202, 30, 212, 28, "Haptics"], [202, 37, 212, 35], [202, 38, 212, 36, "ImpactFeedbackStyle"], [202, 57, 212, 55], [202, 58, 212, 56, "Medium"], [202, 64, 212, 62], [202, 65, 212, 63], [203, 8, 213, 6], [204, 6, 214, 4], [204, 7, 214, 5], [204, 8, 214, 6], [204, 15, 214, 13, "error"], [204, 20, 214, 18], [204, 22, 214, 20], [205, 8, 215, 6, "console"], [205, 15, 215, 13], [205, 16, 215, 14, "error"], [205, 21, 215, 19], [205, 22, 215, 20], [205, 53, 215, 51], [205, 55, 215, 53, "error"], [205, 60, 215, 58], [205, 61, 215, 59], [206, 6, 216, 4], [207, 4, 217, 2], [207, 5, 217, 3], [208, 4, 219, 2], [208, 10, 219, 8, "handleDictation"], [208, 25, 219, 23], [208, 28, 219, 26], [208, 34, 219, 26, "handleDictation"], [208, 35, 219, 26], [208, 40, 219, 38], [209, 6, 220, 4], [209, 10, 220, 8], [209, 11, 220, 9, "hasPermission"], [209, 24, 220, 22], [209, 26, 220, 24], [210, 8, 221, 6, "<PERSON><PERSON>"], [210, 22, 221, 11], [210, 23, 221, 12, "alert"], [210, 28, 221, 17], [210, 29, 222, 8], [210, 50, 222, 29], [210, 52, 223, 8], [210, 102, 224, 6], [210, 103, 224, 7], [211, 8, 225, 6], [212, 6, 226, 4], [213, 6, 228, 4], [213, 10, 228, 8], [214, 8, 229, 6], [214, 12, 229, 10, "isDictating"], [214, 23, 229, 21], [214, 25, 229, 23], [215, 10, 230, 8], [215, 16, 230, 14, "recorder"], [215, 24, 230, 22], [215, 25, 230, 23, "stop"], [215, 29, 230, 27], [215, 30, 230, 28], [215, 31, 230, 29], [216, 10, 231, 8], [216, 16, 231, 14, "mockTranscript"], [216, 30, 231, 28], [216, 33, 231, 31], [216, 62, 231, 60], [216, 63, 231, 61], [216, 64, 231, 62], [217, 10, 232, 8, "setInputText"], [217, 22, 232, 20], [217, 23, 232, 21, "inputText"], [217, 32, 232, 30], [217, 35, 232, 33, "mockTranscript"], [217, 49, 232, 47], [217, 50, 232, 48], [218, 10, 233, 8, "setIsDictating"], [218, 24, 233, 22], [218, 25, 233, 23], [218, 30, 233, 28], [218, 31, 233, 29], [219, 8, 234, 6], [219, 9, 234, 7], [219, 15, 234, 13], [220, 10, 235, 8], [220, 16, 235, 14, "recorder"], [220, 24, 235, 22], [220, 25, 235, 23, "prepareToRecordAsync"], [220, 45, 235, 43], [220, 46, 235, 44], [220, 47, 235, 45], [221, 10, 236, 8, "recorder"], [221, 18, 236, 16], [221, 19, 236, 17, "record"], [221, 25, 236, 23], [221, 26, 236, 24], [221, 27, 236, 25], [222, 10, 237, 8, "setIsDictating"], [222, 24, 237, 22], [222, 25, 237, 23], [222, 29, 237, 27], [222, 30, 237, 28], [223, 10, 238, 8, "Haptics"], [223, 17, 238, 15], [223, 18, 238, 16, "impactAsync"], [223, 29, 238, 27], [223, 30, 238, 28, "Haptics"], [223, 37, 238, 35], [223, 38, 238, 36, "ImpactFeedbackStyle"], [223, 57, 238, 55], [223, 58, 238, 56, "Medium"], [223, 64, 238, 62], [223, 65, 238, 63], [224, 8, 239, 6], [225, 6, 240, 4], [225, 7, 240, 5], [225, 8, 240, 6], [225, 15, 240, 13, "error"], [225, 20, 240, 18], [225, 22, 240, 20], [226, 8, 241, 6, "console"], [226, 15, 241, 13], [226, 16, 241, 14, "error"], [226, 21, 241, 19], [226, 22, 241, 20], [226, 45, 241, 43], [226, 47, 241, 45, "error"], [226, 52, 241, 50], [226, 53, 241, 51], [227, 8, 242, 6, "<PERSON><PERSON>"], [227, 22, 242, 11], [227, 23, 242, 12, "alert"], [227, 28, 242, 17], [227, 29, 242, 18], [227, 36, 242, 25], [227, 38, 242, 27], [227, 65, 242, 54], [227, 66, 242, 55], [228, 8, 243, 6, "setIsDictating"], [228, 22, 243, 20], [228, 23, 243, 21], [228, 28, 243, 26], [228, 29, 243, 27], [229, 6, 244, 4], [230, 4, 245, 2], [230, 5, 245, 3], [231, 4, 247, 2], [231, 10, 247, 8, "handleLongPress"], [231, 25, 247, 23], [231, 28, 247, 27, "message"], [231, 35, 247, 34], [231, 39, 247, 39], [232, 6, 248, 4, "setLongPressedMessage"], [232, 27, 248, 25], [232, 28, 248, 26, "message"], [232, 35, 248, 33], [232, 36, 248, 34], [233, 6, 249, 4, "setIsContextMenuVisible"], [233, 29, 249, 27], [233, 30, 249, 28], [233, 34, 249, 32], [233, 35, 249, 33], [234, 4, 250, 2], [234, 5, 250, 3], [235, 4, 252, 2], [235, 10, 252, 8, "handleCopyMessage"], [235, 27, 252, 25], [235, 30, 252, 28, "handleCopyMessage"], [235, 31, 252, 28], [235, 36, 252, 34], [236, 6, 253, 4], [236, 10, 253, 8, "longPressedMessage"], [236, 28, 253, 26], [236, 30, 253, 28], [237, 8, 254, 6, "Clipboard"], [237, 26, 254, 15], [237, 27, 254, 16, "setString"], [237, 36, 254, 25], [237, 37, 254, 26, "longPressedMessage"], [237, 55, 254, 44], [237, 56, 254, 45, "content"], [237, 63, 254, 52], [237, 64, 254, 53], [238, 8, 255, 6, "setIsContextMenuVisible"], [238, 31, 255, 29], [238, 32, 255, 30], [238, 37, 255, 35], [238, 38, 255, 36], [239, 8, 256, 6, "setLongPressedMessage"], [239, 29, 256, 27], [239, 30, 256, 28], [239, 34, 256, 32], [239, 35, 256, 33], [240, 6, 257, 4], [241, 4, 258, 2], [241, 5, 258, 3], [242, 4, 260, 2], [242, 10, 260, 8, "handleListenToMessage"], [242, 31, 260, 29], [242, 34, 260, 32, "handleListenToMessage"], [242, 35, 260, 32], [242, 40, 260, 38], [243, 6, 261, 4], [243, 10, 261, 8, "longPressedMessage"], [243, 28, 261, 26], [243, 30, 261, 28], [244, 8, 262, 6], [245, 8, 263, 6, "<PERSON><PERSON>"], [245, 22, 263, 11], [245, 23, 263, 12, "alert"], [245, 28, 263, 17], [245, 29, 263, 18], [245, 51, 263, 40], [245, 53, 263, 42, "longPressedMessage"], [245, 71, 263, 60], [245, 72, 263, 61, "content"], [245, 79, 263, 68], [245, 80, 263, 69], [246, 8, 264, 6, "setIsContextMenuVisible"], [246, 31, 264, 29], [246, 32, 264, 30], [246, 37, 264, 35], [246, 38, 264, 36], [247, 8, 265, 6, "setLongPressedMessage"], [247, 29, 265, 27], [247, 30, 265, 28], [247, 34, 265, 32], [247, 35, 265, 33], [248, 6, 266, 4], [249, 4, 267, 2], [249, 5, 267, 3], [250, 4, 269, 2], [250, 10, 269, 8, "handleMute"], [250, 20, 269, 18], [250, 23, 269, 21], [250, 29, 269, 21, "handleMute"], [250, 30, 269, 21], [250, 35, 269, 33], [251, 6, 270, 4], [251, 10, 270, 8], [251, 11, 270, 9, "hasPermission"], [251, 24, 270, 22], [251, 26, 270, 24], [252, 6, 272, 4], [252, 10, 272, 8], [253, 8, 273, 6], [253, 12, 273, 10, "isMuted"], [253, 19, 273, 17], [253, 21, 273, 19], [254, 10, 274, 8], [255, 10, 275, 8], [255, 14, 275, 12], [255, 15, 275, 13, "recorderState"], [255, 28, 275, 26], [255, 29, 275, 27, "isRecording"], [255, 40, 275, 38], [255, 42, 275, 40], [256, 12, 276, 10], [256, 18, 276, 16, "recorder"], [256, 26, 276, 24], [256, 27, 276, 25, "prepareToRecordAsync"], [256, 47, 276, 45], [256, 48, 276, 46], [256, 49, 276, 47], [257, 12, 277, 10, "recorder"], [257, 20, 277, 18], [257, 21, 277, 19, "record"], [257, 27, 277, 25], [257, 28, 277, 26], [257, 29, 277, 27], [258, 10, 278, 8], [259, 10, 279, 8, "setIsMuted"], [259, 20, 279, 18], [259, 21, 279, 19], [259, 26, 279, 24], [259, 27, 279, 25], [260, 10, 280, 8, "Haptics"], [260, 17, 280, 15], [260, 18, 280, 16, "impactAsync"], [260, 29, 280, 27], [260, 30, 280, 28, "Haptics"], [260, 37, 280, 35], [260, 38, 280, 36, "ImpactFeedbackStyle"], [260, 57, 280, 55], [260, 58, 280, 56, "Light"], [260, 63, 280, 61], [260, 64, 280, 62], [261, 8, 281, 6], [261, 9, 281, 7], [261, 15, 281, 13], [262, 10, 282, 8], [263, 10, 283, 8], [263, 14, 283, 12, "recorderState"], [263, 27, 283, 25], [263, 28, 283, 26, "isRecording"], [263, 39, 283, 37], [263, 41, 283, 39], [264, 12, 284, 10], [264, 18, 284, 16, "recorder"], [264, 26, 284, 24], [264, 27, 284, 25, "stop"], [264, 31, 284, 29], [264, 32, 284, 30], [264, 33, 284, 31], [265, 10, 285, 8], [266, 10, 286, 8, "setIsMuted"], [266, 20, 286, 18], [266, 21, 286, 19], [266, 25, 286, 23], [266, 26, 286, 24], [267, 10, 287, 8, "Haptics"], [267, 17, 287, 15], [267, 18, 287, 16, "impactAsync"], [267, 29, 287, 27], [267, 30, 287, 28, "Haptics"], [267, 37, 287, 35], [267, 38, 287, 36, "ImpactFeedbackStyle"], [267, 57, 287, 55], [267, 58, 287, 56, "Light"], [267, 63, 287, 61], [267, 64, 287, 62], [268, 8, 288, 6], [269, 6, 289, 4], [269, 7, 289, 5], [269, 8, 289, 6], [269, 15, 289, 13, "error"], [269, 20, 289, 18], [269, 22, 289, 20], [270, 8, 290, 6, "console"], [270, 15, 290, 13], [270, 16, 290, 14, "error"], [270, 21, 290, 19], [270, 22, 290, 20], [270, 47, 290, 45], [270, 49, 290, 47, "error"], [270, 54, 290, 52], [270, 55, 290, 53], [271, 6, 291, 4], [272, 4, 292, 2], [272, 5, 292, 3], [274, 4, 295, 2], [275, 4, 296, 2], [275, 8, 296, 2, "useEffect"], [275, 24, 296, 11], [275, 26, 296, 12], [275, 32, 296, 18], [276, 6, 297, 4], [276, 10, 297, 8, "scrollViewRef"], [276, 23, 297, 21], [276, 24, 297, 22, "current"], [276, 31, 297, 29], [276, 33, 297, 31], [277, 8, 298, 6, "scrollViewRef"], [277, 21, 298, 19], [277, 22, 298, 20, "current"], [277, 29, 298, 27], [277, 30, 298, 28, "scrollToEnd"], [277, 41, 298, 39], [277, 42, 298, 40], [278, 10, 298, 42, "animated"], [278, 18, 298, 50], [278, 20, 298, 52], [279, 8, 298, 57], [279, 9, 298, 58], [279, 10, 298, 59], [280, 6, 299, 4], [281, 4, 300, 2], [281, 5, 300, 3], [281, 7, 300, 5], [281, 8, 300, 6, "messages"], [281, 16, 300, 14], [281, 17, 300, 15], [281, 18, 300, 16], [282, 4, 302, 2], [282, 8, 302, 6], [282, 9, 302, 7, "fontsLoaded"], [282, 20, 302, 18], [282, 22, 302, 20], [283, 6, 303, 4], [283, 13, 303, 11], [283, 17, 303, 15], [284, 4, 304, 2], [286, 4, 306, 2], [287, 4, 307, 2], [287, 8, 307, 6, "isFirstTime"], [287, 19, 307, 17], [287, 21, 307, 19], [288, 6, 308, 4], [288, 26, 309, 6], [288, 30, 309, 6, "_jsxDevRuntime"], [288, 44, 309, 6], [288, 45, 309, 6, "jsxDEV"], [288, 51, 309, 6], [288, 53, 309, 7, "_View"], [288, 58, 309, 7], [288, 59, 309, 7, "default"], [288, 66, 309, 11], [289, 8, 309, 12, "style"], [289, 13, 309, 17], [289, 15, 309, 19], [290, 10, 309, 21, "flex"], [290, 14, 309, 25], [290, 16, 309, 27], [290, 17, 309, 28], [291, 10, 309, 30, "backgroundColor"], [291, 25, 309, 45], [291, 27, 309, 47, "colors"], [291, 33, 309, 53], [291, 34, 309, 54, "background"], [292, 8, 309, 65], [292, 9, 309, 67], [293, 8, 309, 67, "children"], [293, 16, 309, 67], [293, 31, 310, 8], [293, 35, 310, 8, "_jsxDevRuntime"], [293, 49, 310, 8], [293, 50, 310, 8, "jsxDEV"], [293, 56, 310, 8], [293, 58, 310, 9, "_View"], [293, 63, 310, 9], [293, 64, 310, 9, "default"], [293, 71, 310, 13], [294, 10, 311, 10, "style"], [294, 15, 311, 15], [294, 17, 311, 17], [295, 12, 312, 12, "flex"], [295, 16, 312, 16], [295, 18, 312, 18], [295, 19, 312, 19], [296, 12, 313, 12, "paddingTop"], [296, 22, 313, 22], [296, 24, 313, 24, "insets"], [296, 30, 313, 30], [296, 31, 313, 31, "top"], [296, 34, 313, 34], [296, 37, 313, 37], [296, 39, 313, 39], [297, 12, 314, 12, "paddingHorizontal"], [297, 29, 314, 29], [297, 31, 314, 31], [297, 33, 314, 33], [298, 12, 315, 12, "paddingBottom"], [298, 25, 315, 25], [298, 27, 315, 27, "insets"], [298, 33, 315, 33], [298, 34, 315, 34, "bottom"], [298, 40, 315, 40], [298, 43, 315, 43], [298, 45, 315, 45], [299, 12, 316, 12, "alignItems"], [299, 22, 316, 22], [299, 24, 316, 24], [299, 32, 316, 32], [300, 12, 317, 12, "justifyContent"], [300, 26, 317, 26], [300, 28, 317, 28], [301, 10, 318, 10], [301, 11, 318, 12], [302, 10, 318, 12, "children"], [302, 18, 318, 12], [302, 34, 320, 10], [302, 38, 320, 10, "_jsxDevRuntime"], [302, 52, 320, 10], [302, 53, 320, 10, "jsxDEV"], [302, 59, 320, 10], [302, 61, 320, 11, "_View"], [302, 66, 320, 11], [302, 67, 320, 11, "default"], [302, 74, 320, 15], [303, 12, 321, 12, "style"], [303, 17, 321, 17], [303, 19, 321, 19], [304, 14, 322, 14, "width"], [304, 19, 322, 19], [304, 21, 322, 21], [304, 24, 322, 24], [305, 14, 323, 14, "height"], [305, 20, 323, 20], [305, 22, 323, 22], [305, 25, 323, 25], [306, 14, 324, 14, "borderRadius"], [306, 26, 324, 26], [306, 28, 324, 28], [306, 30, 324, 30], [307, 14, 325, 14, "backgroundColor"], [307, 29, 325, 29], [307, 31, 325, 31, "colors"], [307, 37, 325, 37], [307, 38, 325, 38, "primaryUltraLight"], [307, 55, 325, 55], [308, 14, 326, 14, "alignItems"], [308, 24, 326, 24], [308, 26, 326, 26], [308, 34, 326, 34], [309, 14, 327, 14, "justifyContent"], [309, 28, 327, 28], [309, 30, 327, 30], [309, 38, 327, 38], [310, 14, 328, 14, "marginBottom"], [310, 26, 328, 26], [310, 28, 328, 28], [311, 12, 329, 12], [311, 13, 329, 14], [312, 12, 329, 14, "children"], [312, 20, 329, 14], [312, 35, 330, 12], [312, 39, 330, 12, "_jsxDevRuntime"], [312, 53, 330, 12], [312, 54, 330, 12, "jsxDEV"], [312, 60, 330, 12], [312, 62, 330, 13, "_lucideReactNative"], [312, 80, 330, 13], [312, 81, 330, 13, "MessageSquare"], [312, 94, 330, 26], [313, 14, 330, 27, "size"], [313, 18, 330, 31], [313, 20, 330, 33], [313, 22, 330, 36], [314, 14, 330, 37, "color"], [314, 19, 330, 42], [314, 21, 330, 44, "colors"], [314, 27, 330, 50], [314, 28, 330, 51, "primary"], [315, 12, 330, 59], [316, 14, 330, 59, "fileName"], [316, 22, 330, 59], [316, 24, 330, 59, "_jsxFileName"], [316, 36, 330, 59], [317, 14, 330, 59, "lineNumber"], [317, 24, 330, 59], [318, 14, 330, 59, "columnNumber"], [318, 26, 330, 59], [319, 12, 330, 59], [319, 19, 330, 61], [320, 10, 330, 62], [321, 12, 330, 62, "fileName"], [321, 20, 330, 62], [321, 22, 330, 62, "_jsxFileName"], [321, 34, 330, 62], [322, 12, 330, 62, "lineNumber"], [322, 22, 330, 62], [323, 12, 330, 62, "columnNumber"], [323, 24, 330, 62], [324, 10, 330, 62], [324, 17, 331, 16], [324, 18, 331, 17], [324, 33, 334, 10], [324, 37, 334, 10, "_jsxDevRuntime"], [324, 51, 334, 10], [324, 52, 334, 10, "jsxDEV"], [324, 58, 334, 10], [324, 60, 334, 11, "_Text"], [324, 65, 334, 11], [324, 66, 334, 11, "default"], [324, 73, 334, 15], [325, 12, 335, 12, "style"], [325, 17, 335, 17], [325, 19, 335, 19], [326, 14, 336, 14, "fontSize"], [326, 22, 336, 22], [326, 24, 336, 24], [326, 26, 336, 26], [327, 14, 337, 14, "fontFamily"], [327, 24, 337, 24], [327, 26, 337, 26], [327, 47, 337, 47], [328, 14, 338, 14, "color"], [328, 19, 338, 19], [328, 21, 338, 21, "colors"], [328, 27, 338, 27], [328, 28, 338, 28, "text"], [328, 32, 338, 32], [329, 14, 339, 14, "textAlign"], [329, 23, 339, 23], [329, 25, 339, 25], [329, 33, 339, 33], [330, 14, 340, 14, "marginBottom"], [330, 26, 340, 26], [330, 28, 340, 28], [331, 12, 341, 12], [331, 13, 341, 14], [332, 12, 341, 14, "children"], [332, 20, 341, 14], [332, 22, 341, 15], [333, 10, 343, 10], [334, 12, 343, 10, "fileName"], [334, 20, 343, 10], [334, 22, 343, 10, "_jsxFileName"], [334, 34, 343, 10], [335, 12, 343, 10, "lineNumber"], [335, 22, 343, 10], [336, 12, 343, 10, "columnNumber"], [336, 24, 343, 10], [337, 10, 343, 10], [337, 17, 343, 16], [337, 18, 343, 17], [337, 33, 346, 10], [337, 37, 346, 10, "_jsxDevRuntime"], [337, 51, 346, 10], [337, 52, 346, 10, "jsxDEV"], [337, 58, 346, 10], [337, 60, 346, 11, "_Text"], [337, 65, 346, 11], [337, 66, 346, 11, "default"], [337, 73, 346, 15], [338, 12, 347, 12, "style"], [338, 17, 347, 17], [338, 19, 347, 19], [339, 14, 348, 14, "fontSize"], [339, 22, 348, 22], [339, 24, 348, 24], [339, 26, 348, 26], [340, 14, 349, 14, "fontFamily"], [340, 24, 349, 24], [340, 26, 349, 26], [340, 46, 349, 46], [341, 14, 350, 14, "color"], [341, 19, 350, 19], [341, 21, 350, 21, "colors"], [341, 27, 350, 27], [341, 28, 350, 28, "textSecondary"], [341, 41, 350, 41], [342, 14, 351, 14, "textAlign"], [342, 23, 351, 23], [342, 25, 351, 25], [342, 33, 351, 33], [343, 14, 352, 14, "lineHeight"], [343, 24, 352, 24], [343, 26, 352, 26], [343, 28, 352, 28], [344, 14, 353, 14, "marginBottom"], [344, 26, 353, 26], [344, 28, 353, 28], [345, 12, 354, 12], [345, 13, 354, 14], [346, 12, 354, 14, "children"], [346, 20, 354, 14], [346, 22, 354, 15], [347, 10, 357, 10], [348, 12, 357, 10, "fileName"], [348, 20, 357, 10], [348, 22, 357, 10, "_jsxFileName"], [348, 34, 357, 10], [349, 12, 357, 10, "lineNumber"], [349, 22, 357, 10], [350, 12, 357, 10, "columnNumber"], [350, 24, 357, 10], [351, 10, 357, 10], [351, 17, 357, 16], [351, 18, 357, 17], [351, 33, 360, 10], [351, 37, 360, 10, "_jsxDevRuntime"], [351, 51, 360, 10], [351, 52, 360, 10, "jsxDEV"], [351, 58, 360, 10], [351, 60, 360, 11, "_TouchableOpacity"], [351, 77, 360, 11], [351, 78, 360, 11, "default"], [351, 85, 360, 27], [352, 12, 361, 12, "style"], [352, 17, 361, 17], [352, 19, 361, 19], [353, 14, 362, 14, "backgroundColor"], [353, 29, 362, 29], [353, 31, 362, 31, "colors"], [353, 37, 362, 37], [353, 38, 362, 38, "primary"], [353, 45, 362, 45], [354, 14, 363, 14, "borderRadius"], [354, 26, 363, 26], [354, 28, 363, 28], [354, 30, 363, 30], [355, 14, 364, 14, "paddingHorizontal"], [355, 31, 364, 31], [355, 33, 364, 33], [355, 35, 364, 35], [356, 14, 365, 14, "paddingVertical"], [356, 29, 365, 29], [356, 31, 365, 31], [356, 33, 365, 33], [357, 14, 366, 14, "min<PERSON><PERSON><PERSON>"], [357, 22, 366, 22], [357, 24, 366, 24], [357, 27, 366, 27], [358, 14, 367, 14, "alignItems"], [358, 24, 367, 24], [358, 26, 367, 26], [359, 12, 368, 12], [359, 13, 368, 14], [360, 12, 369, 12, "onPress"], [360, 19, 369, 19], [360, 21, 369, 21, "handleStartBrainstorming"], [360, 45, 369, 46], [361, 12, 369, 46, "children"], [361, 20, 369, 46], [361, 35, 370, 12], [361, 39, 370, 12, "_jsxDevRuntime"], [361, 53, 370, 12], [361, 54, 370, 12, "jsxDEV"], [361, 60, 370, 12], [361, 62, 370, 13, "_Text"], [361, 67, 370, 13], [361, 68, 370, 13, "default"], [361, 75, 370, 17], [362, 14, 371, 14, "style"], [362, 19, 371, 19], [362, 21, 371, 21], [363, 16, 372, 16, "fontSize"], [363, 24, 372, 24], [363, 26, 372, 26], [363, 28, 372, 28], [364, 16, 373, 16, "fontFamily"], [364, 26, 373, 26], [364, 28, 373, 28], [364, 49, 373, 49], [365, 16, 374, 16, "color"], [365, 21, 374, 21], [365, 23, 374, 23, "colors"], [365, 29, 374, 29], [365, 30, 374, 30, "background"], [366, 14, 375, 14], [366, 15, 375, 16], [367, 14, 375, 16, "children"], [367, 22, 375, 16], [367, 24, 375, 17], [368, 12, 377, 12], [369, 14, 377, 12, "fileName"], [369, 22, 377, 12], [369, 24, 377, 12, "_jsxFileName"], [369, 36, 377, 12], [370, 14, 377, 12, "lineNumber"], [370, 24, 377, 12], [371, 14, 377, 12, "columnNumber"], [371, 26, 377, 12], [372, 12, 377, 12], [372, 19, 377, 18], [373, 10, 377, 19], [374, 12, 377, 19, "fileName"], [374, 20, 377, 19], [374, 22, 377, 19, "_jsxFileName"], [374, 34, 377, 19], [375, 12, 377, 19, "lineNumber"], [375, 22, 377, 19], [376, 12, 377, 19, "columnNumber"], [376, 24, 377, 19], [377, 10, 377, 19], [377, 17, 378, 28], [377, 18, 378, 29], [378, 8, 378, 29], [379, 10, 378, 29, "fileName"], [379, 18, 378, 29], [379, 20, 378, 29, "_jsxFileName"], [379, 32, 378, 29], [380, 10, 378, 29, "lineNumber"], [380, 20, 378, 29], [381, 10, 378, 29, "columnNumber"], [381, 22, 378, 29], [382, 8, 378, 29], [382, 15, 379, 14], [383, 6, 379, 15], [384, 8, 379, 15, "fileName"], [384, 16, 379, 15], [384, 18, 379, 15, "_jsxFileName"], [384, 30, 379, 15], [385, 8, 379, 15, "lineNumber"], [385, 18, 379, 15], [386, 8, 379, 15, "columnNumber"], [386, 20, 379, 15], [387, 6, 379, 15], [387, 13, 380, 12], [387, 14, 380, 13], [388, 4, 382, 2], [390, 4, 384, 2], [391, 4, 385, 2], [391, 24, 386, 4], [391, 28, 386, 4, "_jsxDevRuntime"], [391, 42, 386, 4], [391, 43, 386, 4, "jsxDEV"], [391, 49, 386, 4], [391, 51, 386, 5, "_View"], [391, 56, 386, 5], [391, 57, 386, 5, "default"], [391, 64, 386, 9], [392, 6, 386, 10, "style"], [392, 11, 386, 15], [392, 13, 386, 17], [393, 8, 386, 19, "flex"], [393, 12, 386, 23], [393, 14, 386, 25], [393, 15, 386, 26], [394, 8, 386, 28, "backgroundColor"], [394, 23, 386, 43], [394, 25, 386, 45, "colors"], [394, 31, 386, 51], [394, 32, 386, 52, "background"], [395, 6, 386, 63], [395, 7, 386, 65], [396, 6, 386, 65, "children"], [396, 14, 386, 65], [396, 30, 387, 6], [396, 34, 387, 6, "_jsxDevRuntime"], [396, 48, 387, 6], [396, 49, 387, 6, "jsxDEV"], [396, 55, 387, 6], [396, 57, 387, 7, "_Header"], [396, 64, 387, 7], [396, 65, 387, 7, "default"], [396, 72, 387, 13], [397, 8, 388, 8, "voiceMode"], [397, 17, 388, 17], [397, 19, 388, 19, "voiceMode"], [397, 28, 388, 29], [398, 8, 389, 8, "onToggleVoiceMode"], [398, 25, 389, 25], [398, 27, 389, 27, "toggleVoiceMode"], [398, 42, 389, 43], [399, 8, 390, 8, "onDone"], [399, 14, 390, 14], [399, 16, 390, 16, "onDone"], [399, 17, 390, 16], [399, 22, 390, 22, "<PERSON><PERSON>"], [399, 36, 390, 27], [399, 37, 390, 28, "alert"], [399, 42, 390, 33], [399, 43, 390, 34], [399, 57, 390, 48], [400, 6, 390, 50], [401, 8, 390, 50, "fileName"], [401, 16, 390, 50], [401, 18, 390, 50, "_jsxFileName"], [401, 30, 390, 50], [402, 8, 390, 50, "lineNumber"], [402, 18, 390, 50], [403, 8, 390, 50, "columnNumber"], [403, 20, 390, 50], [404, 6, 390, 50], [404, 13, 391, 7], [404, 14, 391, 8], [404, 16, 392, 7, "voiceMode"], [404, 25, 392, 16], [404, 41, 393, 8], [404, 45, 393, 8, "_jsxDevRuntime"], [404, 59, 393, 8], [404, 60, 393, 8, "jsxDEV"], [404, 66, 393, 8], [404, 68, 393, 9, "_VoiceMode"], [404, 78, 393, 9], [404, 79, 393, 9, "default"], [404, 86, 393, 18], [405, 8, 394, 10, "isRecording"], [405, 19, 394, 21], [405, 21, 394, 23, "recorderState"], [405, 34, 394, 36], [405, 35, 394, 37, "isRecording"], [405, 46, 394, 49], [406, 8, 395, 10, "hasPermission"], [406, 21, 395, 23], [406, 23, 395, 25, "hasPermission"], [406, 36, 395, 39], [407, 8, 396, 10, "isLoading"], [407, 17, 396, 19], [407, 19, 396, 21, "isLoading"], [407, 28, 396, 31], [408, 8, 397, 10, "transcript"], [408, 18, 397, 20], [408, 20, 397, 22, "transcript"], [408, 30, 397, 33], [409, 8, 398, 10, "isMuted"], [409, 15, 398, 17], [409, 17, 398, 19, "isMuted"], [409, 24, 398, 27], [410, 8, 399, 10, "onMute"], [410, 14, 399, 16], [410, 16, 399, 18, "handleMute"], [411, 6, 399, 29], [412, 8, 399, 29, "fileName"], [412, 16, 399, 29], [412, 18, 399, 29, "_jsxFileName"], [412, 30, 399, 29], [413, 8, 399, 29, "lineNumber"], [413, 18, 399, 29], [414, 8, 399, 29, "columnNumber"], [414, 20, 399, 29], [415, 6, 399, 29], [415, 13, 400, 9], [415, 14, 400, 10], [415, 30, 402, 8], [415, 34, 402, 8, "_jsxDevRuntime"], [415, 48, 402, 8], [415, 49, 402, 8, "jsxDEV"], [415, 55, 402, 8], [415, 57, 402, 9, "_KeyboardAvoidingAnimatedView"], [415, 86, 402, 9], [415, 87, 402, 9, "default"], [415, 94, 402, 37], [416, 8, 402, 38, "style"], [416, 13, 402, 43], [416, 15, 402, 45], [417, 10, 402, 47, "flex"], [417, 14, 402, 51], [417, 16, 402, 53], [418, 8, 402, 55], [418, 9, 402, 57], [419, 8, 402, 57, "children"], [419, 16, 402, 57], [419, 32, 404, 10], [419, 36, 404, 10, "_jsxDevRuntime"], [419, 50, 404, 10], [419, 51, 404, 10, "jsxDEV"], [419, 57, 404, 10], [419, 59, 404, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [419, 70, 404, 11], [419, 71, 404, 11, "default"], [419, 78, 404, 21], [420, 10, 405, 12, "ref"], [420, 13, 405, 15], [420, 15, 405, 17, "scrollViewRef"], [420, 28, 405, 31], [421, 10, 406, 12, "style"], [421, 15, 406, 17], [421, 17, 406, 19], [422, 12, 406, 21, "flex"], [422, 16, 406, 25], [422, 18, 406, 27], [423, 10, 406, 29], [423, 11, 406, 31], [424, 10, 407, 12, "contentContainerStyle"], [424, 31, 407, 33], [424, 33, 407, 35], [425, 12, 408, 14, "paddingHorizontal"], [425, 29, 408, 31], [425, 31, 408, 33], [425, 33, 408, 35], [426, 12, 409, 14, "paddingVertical"], [426, 27, 409, 29], [426, 29, 409, 31], [426, 31, 409, 33], [427, 12, 410, 14, "paddingBottom"], [427, 25, 410, 27], [427, 27, 410, 29], [427, 30, 410, 32], [427, 31, 410, 34], [428, 10, 411, 12], [428, 11, 411, 14], [429, 10, 412, 12, "showsVerticalScrollIndicator"], [429, 38, 412, 40], [429, 40, 412, 42], [429, 45, 412, 48], [430, 10, 412, 48, "children"], [430, 18, 412, 48], [430, 21, 413, 13, "messages"], [430, 29, 413, 21], [430, 30, 413, 22, "map"], [430, 33, 413, 25], [430, 34, 413, 26], [430, 35, 413, 27, "message"], [430, 42, 413, 34], [430, 44, 413, 36, "index"], [430, 49, 413, 41], [430, 67, 414, 14], [430, 71, 414, 14, "_jsxDevRuntime"], [430, 85, 414, 14], [430, 86, 414, 14, "jsxDEV"], [430, 92, 414, 14], [430, 94, 414, 15, "_MessageBubble"], [430, 108, 414, 15], [430, 109, 414, 15, "default"], [430, 116, 414, 28], [431, 12, 414, 41, "message"], [431, 19, 414, 48], [431, 21, 414, 50, "message"], [431, 28, 414, 58], [432, 12, 414, 59, "onLongPress"], [432, 23, 414, 70], [432, 25, 414, 72, "handleLongPress"], [433, 10, 414, 88], [433, 13, 414, 34, "index"], [433, 18, 414, 39], [434, 12, 414, 39, "fileName"], [434, 20, 414, 39], [434, 22, 414, 39, "_jsxFileName"], [434, 34, 414, 39], [435, 12, 414, 39, "lineNumber"], [435, 22, 414, 39], [436, 12, 414, 39, "columnNumber"], [436, 24, 414, 39], [437, 10, 414, 39], [437, 17, 414, 90], [437, 18, 415, 13], [437, 19, 415, 14], [437, 21, 418, 13, "isLoading"], [437, 30, 418, 22], [437, 47, 419, 14], [437, 51, 419, 14, "_jsxDevRuntime"], [437, 65, 419, 14], [437, 66, 419, 14, "jsxDEV"], [437, 72, 419, 14], [437, 74, 419, 15, "_View"], [437, 79, 419, 15], [437, 80, 419, 15, "default"], [437, 87, 419, 19], [438, 12, 420, 16, "style"], [438, 17, 420, 21], [438, 19, 420, 23], [439, 14, 421, 18, "marginBottom"], [439, 26, 421, 30], [439, 28, 421, 32], [439, 30, 421, 34], [440, 14, 422, 18, "alignSelf"], [440, 23, 422, 27], [440, 25, 422, 29], [440, 37, 422, 41], [441, 14, 423, 18, "max<PERSON><PERSON><PERSON>"], [441, 22, 423, 26], [441, 24, 423, 28], [442, 12, 424, 16], [442, 13, 424, 18], [443, 12, 424, 18, "children"], [443, 20, 424, 18], [443, 35, 425, 16], [443, 39, 425, 16, "_jsxDevRuntime"], [443, 53, 425, 16], [443, 54, 425, 16, "jsxDEV"], [443, 60, 425, 16], [443, 62, 425, 17, "_View"], [443, 67, 425, 17], [443, 68, 425, 17, "default"], [443, 75, 425, 21], [444, 14, 426, 18, "style"], [444, 19, 426, 23], [444, 21, 426, 25], [445, 16, 427, 20, "backgroundColor"], [445, 31, 427, 35], [445, 33, 427, 37, "colors"], [445, 39, 427, 43], [445, 40, 427, 44, "cardBackground"], [445, 54, 427, 58], [446, 16, 428, 20, "borderRadius"], [446, 28, 428, 32], [446, 30, 428, 34], [446, 32, 428, 36], [447, 16, 429, 20, "paddingHorizontal"], [447, 33, 429, 37], [447, 35, 429, 39], [447, 37, 429, 41], [448, 16, 430, 20, "paddingVertical"], [448, 31, 430, 35], [448, 33, 430, 37], [448, 35, 430, 39], [449, 16, 431, 20, "borderWidth"], [449, 27, 431, 31], [449, 29, 431, 33], [449, 30, 431, 34], [450, 16, 432, 20, "borderColor"], [450, 27, 432, 31], [450, 29, 432, 33, "colors"], [450, 35, 432, 39], [450, 36, 432, 40, "outline"], [451, 14, 433, 18], [451, 15, 433, 20], [452, 14, 433, 20, "children"], [452, 22, 433, 20], [452, 37, 434, 18], [452, 41, 434, 18, "_jsxDevRuntime"], [452, 55, 434, 18], [452, 56, 434, 18, "jsxDEV"], [452, 62, 434, 18], [452, 64, 434, 19, "_Text"], [452, 69, 434, 19], [452, 70, 434, 19, "default"], [452, 77, 434, 23], [453, 16, 435, 20, "style"], [453, 21, 435, 25], [453, 23, 435, 27], [454, 18, 436, 22, "fontSize"], [454, 26, 436, 30], [454, 28, 436, 32], [454, 30, 436, 34], [455, 18, 437, 22, "fontFamily"], [455, 28, 437, 32], [455, 30, 437, 34], [455, 50, 437, 54], [456, 18, 438, 22, "color"], [456, 23, 438, 27], [456, 25, 438, 29, "colors"], [456, 31, 438, 35], [456, 32, 438, 36, "textSecondary"], [456, 45, 438, 49], [457, 18, 439, 22, "lineHeight"], [457, 28, 439, 32], [457, 30, 439, 34], [458, 16, 440, 20], [458, 17, 440, 22], [459, 16, 440, 22, "children"], [459, 24, 440, 22], [459, 26, 440, 23], [460, 14, 442, 18], [461, 16, 442, 18, "fileName"], [461, 24, 442, 18], [461, 26, 442, 18, "_jsxFileName"], [461, 38, 442, 18], [462, 16, 442, 18, "lineNumber"], [462, 26, 442, 18], [463, 16, 442, 18, "columnNumber"], [463, 28, 442, 18], [464, 14, 442, 18], [464, 21, 442, 24], [465, 12, 442, 25], [466, 14, 442, 25, "fileName"], [466, 22, 442, 25], [466, 24, 442, 25, "_jsxFileName"], [466, 36, 442, 25], [467, 14, 442, 25, "lineNumber"], [467, 24, 442, 25], [468, 14, 442, 25, "columnNumber"], [468, 26, 442, 25], [469, 12, 442, 25], [469, 19, 443, 22], [470, 10, 443, 23], [471, 12, 443, 23, "fileName"], [471, 20, 443, 23], [471, 22, 443, 23, "_jsxFileName"], [471, 34, 443, 23], [472, 12, 443, 23, "lineNumber"], [472, 22, 443, 23], [473, 12, 443, 23, "columnNumber"], [473, 24, 443, 23], [474, 10, 443, 23], [474, 17, 444, 20], [474, 18, 445, 13], [475, 8, 445, 13], [476, 10, 445, 13, "fileName"], [476, 18, 445, 13], [476, 20, 445, 13, "_jsxFileName"], [476, 32, 445, 13], [477, 10, 445, 13, "lineNumber"], [477, 20, 445, 13], [478, 10, 445, 13, "columnNumber"], [478, 22, 445, 13], [479, 8, 445, 13], [479, 15, 446, 22], [479, 16, 446, 23], [479, 18, 449, 11, "quickActions"], [479, 30, 449, 23], [479, 31, 449, 24, "length"], [479, 37, 449, 30], [479, 40, 449, 33], [479, 41, 449, 34], [479, 58, 450, 12], [479, 62, 450, 12, "_jsxDevRuntime"], [479, 76, 450, 12], [479, 77, 450, 12, "jsxDEV"], [479, 83, 450, 12], [479, 85, 450, 13, "_View"], [479, 90, 450, 13], [479, 91, 450, 13, "default"], [479, 98, 450, 17], [480, 10, 451, 14, "style"], [480, 15, 451, 19], [480, 17, 451, 21], [481, 12, 452, 16, "position"], [481, 20, 452, 24], [481, 22, 452, 26], [481, 32, 452, 36], [482, 12, 453, 16, "bottom"], [482, 18, 453, 22], [482, 20, 453, 24, "insets"], [482, 26, 453, 30], [482, 27, 453, 31, "bottom"], [482, 33, 453, 37], [482, 36, 453, 40], [482, 39, 453, 43], [483, 12, 454, 16, "left"], [483, 16, 454, 20], [483, 18, 454, 22], [483, 20, 454, 24], [484, 12, 455, 16, "right"], [484, 17, 455, 21], [484, 19, 455, 23], [485, 10, 456, 14], [485, 11, 456, 16], [486, 10, 456, 16, "children"], [486, 18, 456, 16], [486, 33, 457, 14], [486, 37, 457, 14, "_jsxDevRuntime"], [486, 51, 457, 14], [486, 52, 457, 14, "jsxDEV"], [486, 58, 457, 14], [486, 60, 457, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [486, 71, 457, 15], [486, 72, 457, 15, "default"], [486, 79, 457, 25], [487, 12, 458, 16, "horizontal"], [487, 22, 458, 26], [488, 12, 459, 16, "showsHorizontalScrollIndicator"], [488, 42, 459, 46], [488, 44, 459, 48], [488, 49, 459, 54], [489, 12, 460, 16, "contentContainerStyle"], [489, 33, 460, 37], [489, 35, 460, 39], [490, 14, 460, 41, "gap"], [490, 17, 460, 44], [490, 19, 460, 46], [491, 12, 460, 48], [491, 13, 460, 50], [492, 12, 460, 50, "children"], [492, 20, 460, 50], [492, 22, 461, 17, "quickActions"], [492, 34, 461, 29], [492, 35, 461, 30, "map"], [492, 38, 461, 33], [492, 39, 461, 34], [492, 40, 461, 35, "action"], [492, 46, 461, 41], [492, 48, 461, 43, "index"], [492, 53, 461, 48], [492, 71, 462, 18], [492, 75, 462, 18, "_jsxDevRuntime"], [492, 89, 462, 18], [492, 90, 462, 18, "jsxDEV"], [492, 96, 462, 18], [492, 98, 462, 19, "_TouchableOpacity"], [492, 115, 462, 19], [492, 116, 462, 19, "default"], [492, 123, 462, 35], [493, 14, 464, 20, "style"], [493, 19, 464, 25], [493, 21, 464, 27], [494, 16, 465, 22, "backgroundColor"], [494, 31, 465, 37], [494, 33, 465, 39, "colors"], [494, 39, 465, 45], [494, 40, 465, 46, "primaryUltraLight"], [494, 57, 465, 63], [495, 16, 466, 22, "borderRadius"], [495, 28, 466, 34], [495, 30, 466, 36], [495, 32, 466, 38], [496, 16, 467, 22, "paddingHorizontal"], [496, 33, 467, 39], [496, 35, 467, 41], [496, 37, 467, 43], [497, 16, 468, 22, "paddingVertical"], [497, 31, 468, 37], [497, 33, 468, 39], [497, 34, 468, 40], [498, 16, 469, 22, "borderWidth"], [498, 27, 469, 33], [498, 29, 469, 35], [498, 30, 469, 36], [499, 16, 470, 22, "borderColor"], [499, 27, 470, 33], [499, 29, 470, 35, "colors"], [499, 35, 470, 41], [499, 36, 470, 42, "primary"], [500, 14, 471, 20], [500, 15, 471, 22], [501, 14, 472, 20, "onPress"], [501, 21, 472, 27], [501, 23, 472, 29, "onPress"], [501, 24, 472, 29], [501, 29, 472, 35, "handleQuickAction"], [501, 46, 472, 52], [501, 47, 472, 53, "action"], [501, 53, 472, 59], [501, 54, 472, 61], [502, 14, 472, 61, "children"], [502, 22, 472, 61], [502, 37, 473, 20], [502, 41, 473, 20, "_jsxDevRuntime"], [502, 55, 473, 20], [502, 56, 473, 20, "jsxDEV"], [502, 62, 473, 20], [502, 64, 473, 21, "_Text"], [502, 69, 473, 21], [502, 70, 473, 21, "default"], [502, 77, 473, 25], [503, 16, 474, 22, "style"], [503, 21, 474, 27], [503, 23, 474, 29], [504, 18, 475, 24, "fontSize"], [504, 26, 475, 32], [504, 28, 475, 34], [504, 30, 475, 36], [505, 18, 476, 24, "fontFamily"], [505, 28, 476, 34], [505, 30, 476, 36], [505, 49, 476, 55], [506, 18, 477, 24, "color"], [506, 23, 477, 29], [506, 25, 477, 31, "colors"], [506, 31, 477, 37], [506, 32, 477, 38, "primary"], [507, 16, 478, 22], [507, 17, 478, 24], [508, 16, 478, 24, "children"], [508, 24, 478, 24], [508, 26, 479, 23, "action"], [509, 14, 479, 29], [510, 16, 479, 29, "fileName"], [510, 24, 479, 29], [510, 26, 479, 29, "_jsxFileName"], [510, 38, 479, 29], [511, 16, 479, 29, "lineNumber"], [511, 26, 479, 29], [512, 16, 479, 29, "columnNumber"], [512, 28, 479, 29], [513, 14, 479, 29], [513, 21, 480, 26], [514, 12, 480, 27], [514, 15, 463, 25, "index"], [514, 20, 463, 30], [515, 14, 463, 30, "fileName"], [515, 22, 463, 30], [515, 24, 463, 30, "_jsxFileName"], [515, 36, 463, 30], [516, 14, 463, 30, "lineNumber"], [516, 24, 463, 30], [517, 14, 463, 30, "columnNumber"], [517, 26, 463, 30], [518, 12, 463, 30], [518, 19, 481, 36], [518, 20, 482, 17], [519, 10, 482, 18], [520, 12, 482, 18, "fileName"], [520, 20, 482, 18], [520, 22, 482, 18, "_jsxFileName"], [520, 34, 482, 18], [521, 12, 482, 18, "lineNumber"], [521, 22, 482, 18], [522, 12, 482, 18, "columnNumber"], [522, 24, 482, 18], [523, 10, 482, 18], [523, 17, 483, 26], [524, 8, 483, 27], [525, 10, 483, 27, "fileName"], [525, 18, 483, 27], [525, 20, 483, 27, "_jsxFileName"], [525, 32, 483, 27], [526, 10, 483, 27, "lineNumber"], [526, 20, 483, 27], [527, 10, 483, 27, "columnNumber"], [527, 22, 483, 27], [528, 8, 483, 27], [528, 15, 484, 18], [528, 16, 485, 11], [528, 31, 488, 10], [528, 35, 488, 10, "_jsxDevRuntime"], [528, 49, 488, 10], [528, 50, 488, 10, "jsxDEV"], [528, 56, 488, 10], [528, 58, 488, 11, "_View"], [528, 63, 488, 11], [528, 64, 488, 11, "default"], [528, 71, 488, 15], [529, 10, 489, 12, "style"], [529, 15, 489, 17], [529, 17, 489, 19], [530, 12, 490, 14, "position"], [530, 20, 490, 22], [530, 22, 490, 24], [530, 32, 490, 34], [531, 12, 491, 14, "bottom"], [531, 18, 491, 20], [531, 20, 491, 22], [531, 21, 491, 23], [532, 12, 492, 14, "left"], [532, 16, 492, 18], [532, 18, 492, 20], [532, 19, 492, 21], [533, 12, 493, 14, "right"], [533, 17, 493, 19], [533, 19, 493, 21], [534, 10, 494, 12], [534, 11, 494, 14], [535, 10, 494, 14, "children"], [535, 18, 494, 14], [535, 33, 495, 12], [535, 37, 495, 12, "_jsxDevRuntime"], [535, 51, 495, 12], [535, 52, 495, 12, "jsxDEV"], [535, 58, 495, 12], [535, 60, 495, 13, "_TextMode"], [535, 69, 495, 13], [535, 70, 495, 13, "default"], [535, 77, 495, 21], [536, 12, 496, 14, "inputText"], [536, 21, 496, 23], [536, 23, 496, 25, "inputText"], [536, 32, 496, 35], [537, 12, 497, 14, "onInputChange"], [537, 25, 497, 27], [537, 27, 497, 29, "setInputText"], [537, 39, 497, 42], [538, 12, 498, 14, "onSendMessage"], [538, 25, 498, 27], [538, 27, 498, 29, "onSendMessage"], [538, 28, 498, 29], [538, 33, 498, 35, "handleSendMessage"], [538, 50, 498, 52], [538, 51, 498, 53], [538, 52, 498, 55], [539, 12, 499, 14, "onStartDictation"], [539, 28, 499, 30], [539, 30, 499, 32, "handleDictation"], [540, 10, 499, 48], [541, 12, 499, 48, "fileName"], [541, 20, 499, 48], [541, 22, 499, 48, "_jsxFileName"], [541, 34, 499, 48], [542, 12, 499, 48, "lineNumber"], [542, 22, 499, 48], [543, 12, 499, 48, "columnNumber"], [543, 24, 499, 48], [544, 10, 499, 48], [544, 17, 500, 13], [545, 8, 500, 14], [546, 10, 500, 14, "fileName"], [546, 18, 500, 14], [546, 20, 500, 14, "_jsxFileName"], [546, 32, 500, 14], [547, 10, 500, 14, "lineNumber"], [547, 20, 500, 14], [548, 10, 500, 14, "columnNumber"], [548, 22, 500, 14], [549, 8, 500, 14], [549, 15, 501, 16], [549, 16, 501, 17], [550, 6, 501, 17], [551, 8, 501, 17, "fileName"], [551, 16, 501, 17], [551, 18, 501, 17, "_jsxFileName"], [551, 30, 501, 17], [552, 8, 501, 17, "lineNumber"], [552, 18, 501, 17], [553, 8, 501, 17, "columnNumber"], [553, 20, 501, 17], [554, 6, 501, 17], [554, 13, 502, 38], [554, 14, 503, 7], [554, 29, 506, 6], [554, 33, 506, 6, "_jsxDevRuntime"], [554, 47, 506, 6], [554, 48, 506, 6, "jsxDEV"], [554, 54, 506, 6], [554, 56, 506, 7, "_Modal"], [554, 62, 506, 7], [554, 63, 506, 7, "default"], [554, 70, 506, 12], [555, 8, 507, 8, "transparent"], [555, 19, 507, 19], [556, 8, 508, 8, "visible"], [556, 15, 508, 15], [556, 17, 508, 17, "isContextMenuVisible"], [556, 37, 508, 38], [557, 8, 509, 8, "onRequestClose"], [557, 22, 509, 22], [557, 24, 509, 24, "onRequestClose"], [557, 25, 509, 24], [557, 30, 509, 30, "setIsContextMenuVisible"], [557, 53, 509, 53], [557, 54, 509, 54], [557, 59, 509, 59], [557, 60, 509, 61], [558, 8, 509, 61, "children"], [558, 16, 509, 61], [558, 31, 511, 8], [558, 35, 511, 8, "_jsxDevRuntime"], [558, 49, 511, 8], [558, 50, 511, 8, "jsxDEV"], [558, 56, 511, 8], [558, 58, 511, 9, "_TouchableOpacity"], [558, 75, 511, 9], [558, 76, 511, 9, "default"], [558, 83, 511, 25], [559, 10, 512, 10, "style"], [559, 15, 512, 15], [559, 17, 512, 17], [560, 12, 512, 19, "flex"], [560, 16, 512, 23], [560, 18, 512, 25], [560, 19, 512, 26], [561, 12, 512, 28, "backgroundColor"], [561, 27, 512, 43], [561, 29, 512, 45], [561, 46, 512, 62], [562, 12, 512, 64, "justifyContent"], [562, 26, 512, 78], [562, 28, 512, 80], [562, 36, 512, 88], [563, 12, 512, 90, "alignItems"], [563, 22, 512, 100], [563, 24, 512, 102], [564, 10, 512, 111], [564, 11, 512, 113], [565, 10, 513, 10, "activeOpacity"], [565, 23, 513, 23], [565, 25, 513, 25], [565, 26, 513, 27], [566, 10, 514, 10, "onPressOut"], [566, 20, 514, 20], [566, 22, 514, 22, "onPressOut"], [566, 23, 514, 22], [566, 28, 514, 28, "setIsContextMenuVisible"], [566, 51, 514, 51], [566, 52, 514, 52], [566, 57, 514, 57], [566, 58, 514, 59], [567, 10, 514, 59, "children"], [567, 18, 514, 59], [567, 33, 516, 10], [567, 37, 516, 10, "_jsxDevRuntime"], [567, 51, 516, 10], [567, 52, 516, 10, "jsxDEV"], [567, 58, 516, 10], [567, 60, 516, 11, "_View"], [567, 65, 516, 11], [567, 66, 516, 11, "default"], [567, 73, 516, 15], [568, 12, 516, 16, "style"], [568, 17, 516, 21], [568, 19, 516, 23], [569, 14, 516, 25, "backgroundColor"], [569, 29, 516, 40], [569, 31, 516, 42, "colors"], [569, 37, 516, 48], [569, 38, 516, 49, "background"], [569, 48, 516, 59], [570, 14, 516, 61, "borderRadius"], [570, 26, 516, 73], [570, 28, 516, 75], [570, 30, 516, 77], [571, 14, 516, 79, "padding"], [571, 21, 516, 86], [571, 23, 516, 88], [571, 25, 516, 90], [572, 14, 516, 92, "width"], [572, 19, 516, 97], [572, 21, 516, 99], [573, 12, 516, 105], [573, 13, 516, 107], [574, 12, 516, 107, "children"], [574, 20, 516, 107], [574, 36, 517, 12], [574, 40, 517, 12, "_jsxDevRuntime"], [574, 54, 517, 12], [574, 55, 517, 12, "jsxDEV"], [574, 61, 517, 12], [574, 63, 517, 13, "_TouchableOpacity"], [574, 80, 517, 13], [574, 81, 517, 13, "default"], [574, 88, 517, 29], [575, 14, 517, 30, "onPress"], [575, 21, 517, 37], [575, 23, 517, 39, "handleCopyMessage"], [575, 40, 517, 57], [576, 14, 517, 58, "style"], [576, 19, 517, 63], [576, 21, 517, 65], [577, 16, 517, 67, "paddingVertical"], [577, 31, 517, 82], [577, 33, 517, 84], [578, 14, 517, 87], [578, 15, 517, 89], [579, 14, 517, 89, "children"], [579, 22, 517, 89], [579, 37, 518, 14], [579, 41, 518, 14, "_jsxDevRuntime"], [579, 55, 518, 14], [579, 56, 518, 14, "jsxDEV"], [579, 62, 518, 14], [579, 64, 518, 15, "_Text"], [579, 69, 518, 15], [579, 70, 518, 15, "default"], [579, 77, 518, 19], [580, 16, 518, 20, "style"], [580, 21, 518, 25], [580, 23, 518, 27], [581, 18, 518, 29, "fontSize"], [581, 26, 518, 37], [581, 28, 518, 39], [581, 30, 518, 41], [582, 18, 518, 43, "fontFamily"], [582, 28, 518, 53], [582, 30, 518, 55], [582, 49, 518, 74], [583, 18, 518, 76, "color"], [583, 23, 518, 81], [583, 25, 518, 83, "colors"], [583, 31, 518, 89], [583, 32, 518, 90, "text"], [584, 16, 518, 95], [584, 17, 518, 97], [585, 16, 518, 97, "children"], [585, 24, 518, 97], [585, 26, 518, 98], [586, 14, 518, 110], [587, 16, 518, 110, "fileName"], [587, 24, 518, 110], [587, 26, 518, 110, "_jsxFileName"], [587, 38, 518, 110], [588, 16, 518, 110, "lineNumber"], [588, 26, 518, 110], [589, 16, 518, 110, "columnNumber"], [589, 28, 518, 110], [590, 14, 518, 110], [590, 21, 518, 116], [591, 12, 518, 117], [592, 14, 518, 117, "fileName"], [592, 22, 518, 117], [592, 24, 518, 117, "_jsxFileName"], [592, 36, 518, 117], [593, 14, 518, 117, "lineNumber"], [593, 24, 518, 117], [594, 14, 518, 117, "columnNumber"], [594, 26, 518, 117], [595, 12, 518, 117], [595, 19, 519, 30], [595, 20, 519, 31], [595, 22, 520, 13, "longPressedMessage"], [595, 40, 520, 31], [595, 42, 520, 33, "role"], [595, 46, 520, 37], [595, 51, 520, 42], [595, 62, 520, 53], [595, 79, 521, 14], [595, 83, 521, 14, "_jsxDevRuntime"], [595, 97, 521, 14], [595, 98, 521, 14, "jsxDEV"], [595, 104, 521, 14], [595, 106, 521, 15, "_TouchableOpacity"], [595, 123, 521, 15], [595, 124, 521, 15, "default"], [595, 131, 521, 31], [596, 14, 521, 32, "onPress"], [596, 21, 521, 39], [596, 23, 521, 41, "handleListenToMessage"], [596, 44, 521, 63], [597, 14, 521, 64, "style"], [597, 19, 521, 69], [597, 21, 521, 71], [598, 16, 521, 73, "paddingVertical"], [598, 31, 521, 88], [598, 33, 521, 90], [599, 14, 521, 93], [599, 15, 521, 95], [600, 14, 521, 95, "children"], [600, 22, 521, 95], [600, 37, 522, 16], [600, 41, 522, 16, "_jsxDevRuntime"], [600, 55, 522, 16], [600, 56, 522, 16, "jsxDEV"], [600, 62, 522, 16], [600, 64, 522, 17, "_Text"], [600, 69, 522, 17], [600, 70, 522, 17, "default"], [600, 77, 522, 21], [601, 16, 522, 22, "style"], [601, 21, 522, 27], [601, 23, 522, 29], [602, 18, 522, 31, "fontSize"], [602, 26, 522, 39], [602, 28, 522, 41], [602, 30, 522, 43], [603, 18, 522, 45, "fontFamily"], [603, 28, 522, 55], [603, 30, 522, 57], [603, 49, 522, 76], [604, 18, 522, 78, "color"], [604, 23, 522, 83], [604, 25, 522, 85, "colors"], [604, 31, 522, 91], [604, 32, 522, 92, "text"], [605, 16, 522, 97], [605, 17, 522, 99], [606, 16, 522, 99, "children"], [606, 24, 522, 99], [606, 26, 522, 100], [607, 14, 522, 117], [608, 16, 522, 117, "fileName"], [608, 24, 522, 117], [608, 26, 522, 117, "_jsxFileName"], [608, 38, 522, 117], [609, 16, 522, 117, "lineNumber"], [609, 26, 522, 117], [610, 16, 522, 117, "columnNumber"], [610, 28, 522, 117], [611, 14, 522, 117], [611, 21, 522, 123], [612, 12, 522, 124], [613, 14, 522, 124, "fileName"], [613, 22, 522, 124], [613, 24, 522, 124, "_jsxFileName"], [613, 36, 522, 124], [614, 14, 522, 124, "lineNumber"], [614, 24, 522, 124], [615, 14, 522, 124, "columnNumber"], [615, 26, 522, 124], [616, 12, 522, 124], [616, 19, 523, 32], [616, 20, 524, 13], [617, 10, 524, 13], [618, 12, 524, 13, "fileName"], [618, 20, 524, 13], [618, 22, 524, 13, "_jsxFileName"], [618, 34, 524, 13], [619, 12, 524, 13, "lineNumber"], [619, 22, 524, 13], [620, 12, 524, 13, "columnNumber"], [620, 24, 524, 13], [621, 10, 524, 13], [621, 17, 525, 16], [622, 8, 525, 17], [623, 10, 525, 17, "fileName"], [623, 18, 525, 17], [623, 20, 525, 17, "_jsxFileName"], [623, 32, 525, 17], [624, 10, 525, 17, "lineNumber"], [624, 20, 525, 17], [625, 10, 525, 17, "columnNumber"], [625, 22, 525, 17], [626, 8, 525, 17], [626, 15, 526, 26], [627, 6, 526, 27], [628, 8, 526, 27, "fileName"], [628, 16, 526, 27], [628, 18, 526, 27, "_jsxFileName"], [628, 30, 526, 27], [629, 8, 526, 27, "lineNumber"], [629, 18, 526, 27], [630, 8, 526, 27, "columnNumber"], [630, 20, 526, 27], [631, 6, 526, 27], [631, 13, 527, 13], [631, 14, 527, 14], [632, 4, 527, 14], [633, 6, 527, 14, "fileName"], [633, 14, 527, 14], [633, 16, 527, 14, "_jsxFileName"], [633, 28, 527, 14], [634, 6, 527, 14, "lineNumber"], [634, 16, 527, 14], [635, 6, 527, 14, "columnNumber"], [635, 18, 527, 14], [636, 4, 527, 14], [636, 11, 528, 10], [636, 12, 528, 11], [637, 2, 530, 0], [638, 2, 530, 1, "_s"], [638, 4, 530, 1], [638, 5, 36, 24, "BrainstormScreen"], [638, 21, 36, 40], [639, 4, 36, 40], [639, 12, 37, 17, "useSafeAreaInsets"], [639, 57, 37, 34], [639, 59, 38, 17, "useColors"], [639, 79, 38, 26], [639, 81, 39, 17, "useRouter"], [639, 102, 39, 26], [639, 104, 40, 22, "useQueryClient"], [639, 130, 40, 36], [639, 132, 41, 24, "useFonts"], [639, 149, 41, 32], [639, 151, 47, 19, "useAudioRecorder"], [639, 178, 47, 35], [639, 180, 48, 24, "useAudioRecorderState"], [639, 212, 48, 45], [640, 2, 48, 45], [641, 2, 48, 45, "_c"], [641, 4, 48, 45], [641, 7, 36, 24, "BrainstormScreen"], [641, 23, 36, 40], [642, 2, 36, 40], [642, 6, 36, 40, "_c"], [642, 8, 36, 40], [643, 2, 36, 40, "$RefreshReg$"], [643, 14, 36, 40], [643, 15, 36, 40, "_c"], [643, 17, 36, 40], [644, 0, 36, 40], [644, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCmC;YCgC;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJuC;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;YCI;GDI;gBiB0F,iCjB;0BkBuB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}