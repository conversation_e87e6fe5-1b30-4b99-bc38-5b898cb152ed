{"dependencies": [{"name": "../Blob/Blob", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 43}}], "key": "CnOreM5x+el0dt4mgJPb+JN2GZg=", "exportNames": ["*"]}}, {"name": "../Utilities/binaryToBase64", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 23}, "end": {"line": 17, "column": 61}}], "key": "OwriluUpxPcDRh9CvV4cSRVywpQ=", "exportNames": ["*"]}}, {"name": "./FormData", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 49}}], "key": "GZ1SJER4hZ8R9eYxbnPasADeXmQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var Blob = require(_dependencyMap[0], \"../Blob/Blob\").default;\n  var binaryToBase64 = require(_dependencyMap[1], \"../Utilities/binaryToBase64\").default;\n  var FormData = require(_dependencyMap[2], \"./FormData\").default;\n  function convertRequestBody(body) {\n    if (typeof body === 'string') {\n      return {\n        string: body\n      };\n    }\n    if (body instanceof Blob) {\n      return {\n        blob: body.data\n      };\n    }\n    if (body instanceof FormData) {\n      return {\n        formData: body.getParts()\n      };\n    }\n    if (body instanceof ArrayBuffer || ArrayBuffer.isView(body)) {\n      return {\n        base64: binaryToBase64(body)\n      };\n    }\n    return body;\n  }\n  var _default = exports.default = convertRequestBody;\n});", "lineCount": 35, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 16, 0], [8, 6, 16, 6, "Blob"], [8, 10, 16, 17], [8, 13, 16, 20, "require"], [8, 20, 16, 27], [8, 21, 16, 27, "_dependencyMap"], [8, 35, 16, 27], [8, 54, 16, 42], [8, 55, 16, 43], [8, 56, 16, 44, "default"], [8, 63, 16, 51], [9, 2, 17, 0], [9, 6, 17, 6, "binaryToBase64"], [9, 20, 17, 20], [9, 23, 17, 23, "require"], [9, 30, 17, 30], [9, 31, 17, 30, "_dependencyMap"], [9, 45, 17, 30], [9, 79, 17, 60], [9, 80, 17, 61], [9, 81, 17, 62, "default"], [9, 88, 17, 69], [10, 2, 18, 0], [10, 6, 18, 6, "FormData"], [10, 14, 18, 25], [10, 17, 18, 28, "require"], [10, 24, 18, 35], [10, 25, 18, 35, "_dependencyMap"], [10, 39, 18, 35], [10, 56, 18, 48], [10, 57, 18, 49], [10, 58, 18, 50, "default"], [10, 65, 18, 57], [11, 2, 28, 0], [11, 11, 28, 9, "convertRequestBody"], [11, 29, 28, 27, "convertRequestBody"], [11, 30, 28, 28, "body"], [11, 34, 28, 45], [11, 36, 28, 55], [12, 4, 29, 2], [12, 8, 29, 6], [12, 15, 29, 13, "body"], [12, 19, 29, 17], [12, 24, 29, 22], [12, 32, 29, 30], [12, 34, 29, 32], [13, 6, 30, 4], [13, 13, 30, 11], [14, 8, 30, 12, "string"], [14, 14, 30, 18], [14, 16, 30, 20, "body"], [15, 6, 30, 24], [15, 7, 30, 25], [16, 4, 31, 2], [17, 4, 32, 2], [17, 8, 32, 6, "body"], [17, 12, 32, 10], [17, 24, 32, 22, "Blob"], [17, 28, 32, 26], [17, 30, 32, 28], [18, 6, 33, 4], [18, 13, 33, 11], [19, 8, 33, 12, "blob"], [19, 12, 33, 16], [19, 14, 33, 18, "body"], [19, 18, 33, 22], [19, 19, 33, 23, "data"], [20, 6, 33, 27], [20, 7, 33, 28], [21, 4, 34, 2], [22, 4, 35, 2], [22, 8, 35, 6, "body"], [22, 12, 35, 10], [22, 24, 35, 22, "FormData"], [22, 32, 35, 30], [22, 34, 35, 32], [23, 6, 36, 4], [23, 13, 36, 11], [24, 8, 36, 12, "formData"], [24, 16, 36, 20], [24, 18, 36, 22, "body"], [24, 22, 36, 26], [24, 23, 36, 27, "getParts"], [24, 31, 36, 35], [24, 32, 36, 36], [25, 6, 36, 37], [25, 7, 36, 38], [26, 4, 37, 2], [27, 4, 38, 2], [27, 8, 38, 6, "body"], [27, 12, 38, 10], [27, 24, 38, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [27, 35, 38, 33], [27, 39, 38, 37, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [27, 50, 38, 48], [27, 51, 38, 49, "<PERSON><PERSON><PERSON><PERSON>"], [27, 57, 38, 55], [27, 58, 38, 56, "body"], [27, 62, 38, 60], [27, 63, 38, 61], [27, 65, 38, 63], [28, 6, 41, 4], [28, 13, 41, 11], [29, 8, 41, 12, "base64"], [29, 14, 41, 18], [29, 16, 41, 20, "binaryToBase64"], [29, 30, 41, 34], [29, 31, 41, 35, "body"], [29, 35, 41, 39], [30, 6, 41, 40], [30, 7, 41, 41], [31, 4, 42, 2], [32, 4, 43, 2], [32, 11, 43, 9, "body"], [32, 15, 43, 13], [33, 2, 44, 0], [34, 2, 44, 1], [34, 6, 44, 1, "_default"], [34, 14, 44, 1], [34, 17, 44, 1, "exports"], [34, 24, 44, 1], [34, 25, 44, 1, "default"], [34, 32, 44, 1], [34, 35, 46, 15, "convertRequestBody"], [34, 53, 46, 33], [35, 0, 46, 33], [35, 3]], "functionMap": {"names": ["<global>", "convertRequestBody"], "mappings": "AAA;AC2B;CDgB"}}, "type": "js/module"}]}