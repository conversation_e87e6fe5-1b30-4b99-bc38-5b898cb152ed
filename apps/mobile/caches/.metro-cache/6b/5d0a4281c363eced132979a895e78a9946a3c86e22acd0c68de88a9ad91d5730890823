{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "../Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 34, "index": 92}}], "key": "UfNR+WZdGHHR+kk13ETrBegm38s=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 93}, "end": {"line": 5, "column": 48, "index": 141}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Label = Label;\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/StyleSheet\"));\n  var _Text = require(_dependencyMap[2], \"../Text.js\");\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  function Label({\n    tintColor,\n    style,\n    ...rest\n  }) {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.Text, {\n      numberOfLines: 1,\n      ...rest,\n      style: [styles.label, tintColor != null && {\n        color: tintColor\n      }, style]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    label: {\n      textAlign: 'center',\n      backgroundColor: 'transparent'\n    }\n  });\n});", "lineCount": 31, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Label"], [8, 15, 1, 13], [8, 18, 1, 13, "Label"], [8, 23, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_StyleSheet"], [9, 17, 1, 13], [9, 20, 1, 13, "_interopRequireDefault"], [9, 42, 1, 13], [9, 43, 1, 13, "require"], [9, 50, 1, 13], [9, 51, 1, 13, "_dependencyMap"], [9, 65, 1, 13], [10, 2, 4, 0], [10, 6, 4, 0, "_Text"], [10, 11, 4, 0], [10, 14, 4, 0, "require"], [10, 21, 4, 0], [10, 22, 4, 0, "_dependencyMap"], [10, 36, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_jsxRuntime"], [11, 17, 5, 0], [11, 20, 5, 0, "require"], [11, 27, 5, 0], [11, 28, 5, 0, "_dependencyMap"], [11, 42, 5, 0], [12, 2, 6, 7], [12, 11, 6, 16, "Label"], [12, 16, 6, 21, "Label"], [12, 17, 6, 22], [13, 4, 7, 2, "tintColor"], [13, 13, 7, 11], [14, 4, 8, 2, "style"], [14, 9, 8, 7], [15, 4, 9, 2], [15, 7, 9, 5, "rest"], [16, 2, 10, 0], [16, 3, 10, 1], [16, 5, 10, 3], [17, 4, 11, 2], [17, 11, 11, 9], [17, 24, 11, 22], [17, 28, 11, 22, "_jsx"], [17, 43, 11, 26], [17, 45, 11, 27, "Text"], [17, 55, 11, 31], [17, 57, 11, 33], [18, 6, 12, 4, "numberOfLines"], [18, 19, 12, 17], [18, 21, 12, 19], [18, 22, 12, 20], [19, 6, 13, 4], [19, 9, 13, 7, "rest"], [19, 13, 13, 11], [20, 6, 14, 4, "style"], [20, 11, 14, 9], [20, 13, 14, 11], [20, 14, 14, 12, "styles"], [20, 20, 14, 18], [20, 21, 14, 19, "label"], [20, 26, 14, 24], [20, 28, 14, 26, "tintColor"], [20, 37, 14, 35], [20, 41, 14, 39], [20, 45, 14, 43], [20, 49, 14, 47], [21, 8, 15, 6, "color"], [21, 13, 15, 11], [21, 15, 15, 13, "tintColor"], [22, 6, 16, 4], [22, 7, 16, 5], [22, 9, 16, 7, "style"], [22, 14, 16, 12], [23, 4, 17, 2], [23, 5, 17, 3], [23, 6, 17, 4], [24, 2, 18, 0], [25, 2, 19, 0], [25, 8, 19, 6, "styles"], [25, 14, 19, 12], [25, 17, 19, 15, "StyleSheet"], [25, 36, 19, 25], [25, 37, 19, 26, "create"], [25, 43, 19, 32], [25, 44, 19, 33], [26, 4, 20, 2, "label"], [26, 9, 20, 7], [26, 11, 20, 9], [27, 6, 21, 4, "textAlign"], [27, 15, 21, 13], [27, 17, 21, 15], [27, 25, 21, 23], [28, 6, 22, 4, "backgroundColor"], [28, 21, 22, 19], [28, 23, 22, 21], [29, 4, 23, 2], [30, 2, 24, 0], [30, 3, 24, 1], [30, 4, 24, 2], [31, 0, 24, 3], [31, 3]], "functionMap": {"names": ["<global>", "Label"], "mappings": "AAA;OCK;CDY"}}, "type": "js/module"}]}