{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./processColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 70}}], "key": "eUlcwF2XkbyXt6yUtZqHPlaBUq8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _processColor = _interopRequireDefault(require(_dependencyMap[1], \"./processColor\"));\n  var TRANSPARENT = 0;\n  function processColorArray(colors) {\n    return colors == null ? null : colors.map(processColorElement);\n  }\n  function processColorElement(color) {\n    var value = (0, _processColor.default)(color);\n    if (value == null) {\n      console.error('Invalid value in color array:', color);\n      return TRANSPARENT;\n    }\n    return value;\n  }\n  var _default = exports.default = processColorArray;\n});", "lineCount": 23, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 15, 0], [9, 6, 15, 0, "_processColor"], [9, 19, 15, 0], [9, 22, 15, 0, "_interopRequireDefault"], [9, 44, 15, 0], [9, 45, 15, 0, "require"], [9, 52, 15, 0], [9, 53, 15, 0, "_dependencyMap"], [9, 67, 15, 0], [10, 2, 17, 0], [10, 6, 17, 6, "TRANSPARENT"], [10, 17, 17, 17], [10, 20, 17, 20], [10, 21, 17, 21], [11, 2, 19, 0], [11, 11, 19, 9, "processColorArray"], [11, 28, 19, 26, "processColorArray"], [11, 29, 20, 2, "colors"], [11, 35, 20, 37], [11, 37, 21, 40], [12, 4, 22, 2], [12, 11, 22, 9, "colors"], [12, 17, 22, 15], [12, 21, 22, 19], [12, 25, 22, 23], [12, 28, 22, 26], [12, 32, 22, 30], [12, 35, 22, 33, "colors"], [12, 41, 22, 39], [12, 42, 22, 40, "map"], [12, 45, 22, 43], [12, 46, 22, 44, "processColorElement"], [12, 65, 22, 63], [12, 66, 22, 64], [13, 2, 23, 0], [14, 2, 25, 0], [14, 11, 25, 9, "processColorElement"], [14, 30, 25, 28, "processColorElement"], [14, 31, 25, 29, "color"], [14, 36, 25, 46], [14, 38, 25, 69], [15, 4, 26, 2], [15, 8, 26, 8, "value"], [15, 13, 26, 13], [15, 16, 26, 16], [15, 20, 26, 16, "processColor"], [15, 41, 26, 28], [15, 43, 26, 29, "color"], [15, 48, 26, 34], [15, 49, 26, 35], [16, 4, 28, 2], [16, 8, 28, 6, "value"], [16, 13, 28, 11], [16, 17, 28, 15], [16, 21, 28, 19], [16, 23, 28, 21], [17, 6, 29, 4, "console"], [17, 13, 29, 11], [17, 14, 29, 12, "error"], [17, 19, 29, 17], [17, 20, 29, 18], [17, 51, 29, 49], [17, 53, 29, 51, "color"], [17, 58, 29, 56], [17, 59, 29, 57], [18, 6, 30, 4], [18, 13, 30, 11, "TRANSPARENT"], [18, 24, 30, 22], [19, 4, 31, 2], [20, 4, 32, 2], [20, 11, 32, 9, "value"], [20, 16, 32, 14], [21, 2, 33, 0], [22, 2, 33, 1], [22, 6, 33, 1, "_default"], [22, 14, 33, 1], [22, 17, 33, 1, "exports"], [22, 24, 33, 1], [22, 25, 33, 1, "default"], [22, 32, 33, 1], [22, 35, 35, 15, "processColorArray"], [22, 52, 35, 32], [23, 0, 35, 32], [23, 3]], "functionMap": {"names": ["<global>", "processColorArray", "processColorElement"], "mappings": "AAA;ACkB;CDI;AEE;CFQ"}}, "type": "js/module"}]}