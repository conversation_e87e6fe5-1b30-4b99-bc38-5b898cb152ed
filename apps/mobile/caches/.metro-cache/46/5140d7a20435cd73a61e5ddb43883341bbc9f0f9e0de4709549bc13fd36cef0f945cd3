{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 43, "index": 254}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}, {"name": "./EventManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 255}, "end": {"line": 4, "column": 42, "index": 297}}], "key": "+tTRfUGWY5rcrw6ZVLEgvlFTMQc=", "exportNames": ["*"]}}, {"name": "../../PointerType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 298}, "end": {"line": 5, "column": 48, "index": 346}}], "key": "PNpP2j+zRZwLQ3w6ZmXPMJNakiU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _interfaces = require(_dependencyMap[1], \"../interfaces\");\n  var _EventManager = _interopRequireDefault(require(_dependencyMap[2], \"./EventManager\"));\n  var _PointerType = require(_dependencyMap[3], \"../../PointerType\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class KeyboardEventManager extends _EventManager.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"activationKeys\", ['Enter', ' ']);\n      _defineProperty(this, \"cancelationKeys\", ['Tab']);\n      _defineProperty(this, \"isPressed\", false);\n      _defineProperty(this, \"keyDownCallback\", event => {\n        if (this.cancelationKeys.indexOf(event.key) !== -1 && this.isPressed) {\n          this.dispatchEvent(event, _interfaces.EventTypes.CANCEL);\n          return;\n        }\n        if (this.activationKeys.indexOf(event.key) === -1) {\n          return;\n        }\n        this.dispatchEvent(event, _interfaces.EventTypes.DOWN);\n      });\n      _defineProperty(this, \"keyUpCallback\", event => {\n        if (this.activationKeys.indexOf(event.key) === -1 || !this.isPressed) {\n          return;\n        }\n        this.dispatchEvent(event, _interfaces.EventTypes.UP);\n      });\n    }\n    dispatchEvent(event, eventType) {\n      if (!(event.target instanceof HTMLElement)) {\n        return;\n      }\n      const adaptedEvent = this.mapEvent(event, eventType);\n      switch (eventType) {\n        case _interfaces.EventTypes.UP:\n          this.isPressed = false;\n          this.onPointerUp(adaptedEvent);\n          break;\n        case _interfaces.EventTypes.DOWN:\n          this.isPressed = true;\n          this.onPointerDown(adaptedEvent);\n          break;\n        case _interfaces.EventTypes.CANCEL:\n          this.isPressed = false;\n          this.onPointerCancel(adaptedEvent);\n          break;\n      }\n    }\n    registerListeners() {\n      this.view.addEventListener('keydown', this.keyDownCallback);\n      this.view.addEventListener('keyup', this.keyUpCallback);\n    }\n    unregisterListeners() {\n      this.view.removeEventListener('keydown', this.keyDownCallback);\n      this.view.removeEventListener('keyup', this.keyUpCallback);\n    }\n    mapEvent(event, eventType) {\n      const viewRect = event.target.getBoundingClientRect();\n      const viewportPosition = {\n        x: (viewRect === null || viewRect === void 0 ? void 0 : viewRect.x) + (viewRect === null || viewRect === void 0 ? void 0 : viewRect.width) / 2,\n        y: (viewRect === null || viewRect === void 0 ? void 0 : viewRect.y) + (viewRect === null || viewRect === void 0 ? void 0 : viewRect.height) / 2\n      };\n      const relativePosition = {\n        x: (viewRect === null || viewRect === void 0 ? void 0 : viewRect.width) / 2,\n        y: (viewRect === null || viewRect === void 0 ? void 0 : viewRect.height) / 2\n      };\n      return {\n        x: viewportPosition.x,\n        y: viewportPosition.y,\n        offsetX: relativePosition.x,\n        offsetY: relativePosition.y,\n        pointerId: 0,\n        eventType: eventType,\n        pointerType: _PointerType.PointerType.KEY,\n        time: event.timeStamp\n      };\n    }\n  }\n  exports.default = KeyboardEventManager;\n});", "lineCount": 97, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_interfaces"], [7, 17, 3, 0], [7, 20, 3, 0, "require"], [7, 27, 3, 0], [7, 28, 3, 0, "_dependencyMap"], [7, 42, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_EventManager"], [8, 19, 4, 0], [8, 22, 4, 0, "_interopRequireDefault"], [8, 44, 4, 0], [8, 45, 4, 0, "require"], [8, 52, 4, 0], [8, 53, 4, 0, "_dependencyMap"], [8, 67, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_PointerType"], [9, 18, 5, 0], [9, 21, 5, 0, "require"], [9, 28, 5, 0], [9, 29, 5, 0, "_dependencyMap"], [9, 43, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 6, 15], [23, 8, 6, 21, "KeyboardEventManager"], [23, 28, 6, 41], [23, 37, 6, 50, "EventManager"], [23, 58, 6, 62], [23, 59, 6, 63], [24, 4, 7, 2, "constructor"], [24, 15, 7, 13, "constructor"], [24, 16, 7, 14], [24, 19, 7, 17, "args"], [24, 23, 7, 21], [24, 25, 7, 23], [25, 6, 8, 4], [25, 11, 8, 9], [25, 12, 8, 10], [25, 15, 8, 13, "args"], [25, 19, 8, 17], [25, 20, 8, 18], [26, 6, 10, 4, "_defineProperty"], [26, 21, 10, 19], [26, 22, 10, 20], [26, 26, 10, 24], [26, 28, 10, 26], [26, 44, 10, 42], [26, 46, 10, 44], [26, 47, 10, 45], [26, 54, 10, 52], [26, 56, 10, 54], [26, 59, 10, 57], [26, 60, 10, 58], [26, 61, 10, 59], [27, 6, 12, 4, "_defineProperty"], [27, 21, 12, 19], [27, 22, 12, 20], [27, 26, 12, 24], [27, 28, 12, 26], [27, 45, 12, 43], [27, 47, 12, 45], [27, 48, 12, 46], [27, 53, 12, 51], [27, 54, 12, 52], [27, 55, 12, 53], [28, 6, 14, 4, "_defineProperty"], [28, 21, 14, 19], [28, 22, 14, 20], [28, 26, 14, 24], [28, 28, 14, 26], [28, 39, 14, 37], [28, 41, 14, 39], [28, 46, 14, 44], [28, 47, 14, 45], [29, 6, 16, 4, "_defineProperty"], [29, 21, 16, 19], [29, 22, 16, 20], [29, 26, 16, 24], [29, 28, 16, 26], [29, 45, 16, 43], [29, 47, 16, 45, "event"], [29, 52, 16, 50], [29, 56, 16, 54], [30, 8, 17, 6], [30, 12, 17, 10], [30, 16, 17, 14], [30, 17, 17, 15, "cancelationKeys"], [30, 32, 17, 30], [30, 33, 17, 31, "indexOf"], [30, 40, 17, 38], [30, 41, 17, 39, "event"], [30, 46, 17, 44], [30, 47, 17, 45, "key"], [30, 50, 17, 48], [30, 51, 17, 49], [30, 56, 17, 54], [30, 57, 17, 55], [30, 58, 17, 56], [30, 62, 17, 60], [30, 66, 17, 64], [30, 67, 17, 65, "isPressed"], [30, 76, 17, 74], [30, 78, 17, 76], [31, 10, 18, 8], [31, 14, 18, 12], [31, 15, 18, 13, "dispatchEvent"], [31, 28, 18, 26], [31, 29, 18, 27, "event"], [31, 34, 18, 32], [31, 36, 18, 34, "EventTypes"], [31, 58, 18, 44], [31, 59, 18, 45, "CANCEL"], [31, 65, 18, 51], [31, 66, 18, 52], [32, 10, 19, 8], [33, 8, 20, 6], [34, 8, 22, 6], [34, 12, 22, 10], [34, 16, 22, 14], [34, 17, 22, 15, "activationKeys"], [34, 31, 22, 29], [34, 32, 22, 30, "indexOf"], [34, 39, 22, 37], [34, 40, 22, 38, "event"], [34, 45, 22, 43], [34, 46, 22, 44, "key"], [34, 49, 22, 47], [34, 50, 22, 48], [34, 55, 22, 53], [34, 56, 22, 54], [34, 57, 22, 55], [34, 59, 22, 57], [35, 10, 23, 8], [36, 8, 24, 6], [37, 8, 26, 6], [37, 12, 26, 10], [37, 13, 26, 11, "dispatchEvent"], [37, 26, 26, 24], [37, 27, 26, 25, "event"], [37, 32, 26, 30], [37, 34, 26, 32, "EventTypes"], [37, 56, 26, 42], [37, 57, 26, 43, "DOWN"], [37, 61, 26, 47], [37, 62, 26, 48], [38, 6, 27, 4], [38, 7, 27, 5], [38, 8, 27, 6], [39, 6, 29, 4, "_defineProperty"], [39, 21, 29, 19], [39, 22, 29, 20], [39, 26, 29, 24], [39, 28, 29, 26], [39, 43, 29, 41], [39, 45, 29, 43, "event"], [39, 50, 29, 48], [39, 54, 29, 52], [40, 8, 30, 6], [40, 12, 30, 10], [40, 16, 30, 14], [40, 17, 30, 15, "activationKeys"], [40, 31, 30, 29], [40, 32, 30, 30, "indexOf"], [40, 39, 30, 37], [40, 40, 30, 38, "event"], [40, 45, 30, 43], [40, 46, 30, 44, "key"], [40, 49, 30, 47], [40, 50, 30, 48], [40, 55, 30, 53], [40, 56, 30, 54], [40, 57, 30, 55], [40, 61, 30, 59], [40, 62, 30, 60], [40, 66, 30, 64], [40, 67, 30, 65, "isPressed"], [40, 76, 30, 74], [40, 78, 30, 76], [41, 10, 31, 8], [42, 8, 32, 6], [43, 8, 34, 6], [43, 12, 34, 10], [43, 13, 34, 11, "dispatchEvent"], [43, 26, 34, 24], [43, 27, 34, 25, "event"], [43, 32, 34, 30], [43, 34, 34, 32, "EventTypes"], [43, 56, 34, 42], [43, 57, 34, 43, "UP"], [43, 59, 34, 45], [43, 60, 34, 46], [44, 6, 35, 4], [44, 7, 35, 5], [44, 8, 35, 6], [45, 4, 36, 2], [46, 4, 38, 2, "dispatchEvent"], [46, 17, 38, 15, "dispatchEvent"], [46, 18, 38, 16, "event"], [46, 23, 38, 21], [46, 25, 38, 23, "eventType"], [46, 34, 38, 32], [46, 36, 38, 34], [47, 6, 39, 4], [47, 10, 39, 8], [47, 12, 39, 10, "event"], [47, 17, 39, 15], [47, 18, 39, 16, "target"], [47, 24, 39, 22], [47, 36, 39, 34, "HTMLElement"], [47, 47, 39, 45], [47, 48, 39, 46], [47, 50, 39, 48], [48, 8, 40, 6], [49, 6, 41, 4], [50, 6, 43, 4], [50, 12, 43, 10, "adaptedEvent"], [50, 24, 43, 22], [50, 27, 43, 25], [50, 31, 43, 29], [50, 32, 43, 30, "mapEvent"], [50, 40, 43, 38], [50, 41, 43, 39, "event"], [50, 46, 43, 44], [50, 48, 43, 46, "eventType"], [50, 57, 43, 55], [50, 58, 43, 56], [51, 6, 45, 4], [51, 14, 45, 12, "eventType"], [51, 23, 45, 21], [52, 8, 46, 6], [52, 13, 46, 11, "EventTypes"], [52, 35, 46, 21], [52, 36, 46, 22, "UP"], [52, 38, 46, 24], [53, 10, 47, 8], [53, 14, 47, 12], [53, 15, 47, 13, "isPressed"], [53, 24, 47, 22], [53, 27, 47, 25], [53, 32, 47, 30], [54, 10, 48, 8], [54, 14, 48, 12], [54, 15, 48, 13, "onPointerUp"], [54, 26, 48, 24], [54, 27, 48, 25, "adaptedEvent"], [54, 39, 48, 37], [54, 40, 48, 38], [55, 10, 49, 8], [56, 8, 51, 6], [56, 13, 51, 11, "EventTypes"], [56, 35, 51, 21], [56, 36, 51, 22, "DOWN"], [56, 40, 51, 26], [57, 10, 52, 8], [57, 14, 52, 12], [57, 15, 52, 13, "isPressed"], [57, 24, 52, 22], [57, 27, 52, 25], [57, 31, 52, 29], [58, 10, 53, 8], [58, 14, 53, 12], [58, 15, 53, 13, "onPointerDown"], [58, 28, 53, 26], [58, 29, 53, 27, "adaptedEvent"], [58, 41, 53, 39], [58, 42, 53, 40], [59, 10, 54, 8], [60, 8, 56, 6], [60, 13, 56, 11, "EventTypes"], [60, 35, 56, 21], [60, 36, 56, 22, "CANCEL"], [60, 42, 56, 28], [61, 10, 57, 8], [61, 14, 57, 12], [61, 15, 57, 13, "isPressed"], [61, 24, 57, 22], [61, 27, 57, 25], [61, 32, 57, 30], [62, 10, 58, 8], [62, 14, 58, 12], [62, 15, 58, 13, "onPointerCancel"], [62, 30, 58, 28], [62, 31, 58, 29, "adaptedEvent"], [62, 43, 58, 41], [62, 44, 58, 42], [63, 10, 59, 8], [64, 6, 60, 4], [65, 4, 61, 2], [66, 4, 63, 2, "registerListeners"], [66, 21, 63, 19, "registerListeners"], [66, 22, 63, 19], [66, 24, 63, 22], [67, 6, 64, 4], [67, 10, 64, 8], [67, 11, 64, 9, "view"], [67, 15, 64, 13], [67, 16, 64, 14, "addEventListener"], [67, 32, 64, 30], [67, 33, 64, 31], [67, 42, 64, 40], [67, 44, 64, 42], [67, 48, 64, 46], [67, 49, 64, 47, "keyDownCallback"], [67, 64, 64, 62], [67, 65, 64, 63], [68, 6, 65, 4], [68, 10, 65, 8], [68, 11, 65, 9, "view"], [68, 15, 65, 13], [68, 16, 65, 14, "addEventListener"], [68, 32, 65, 30], [68, 33, 65, 31], [68, 40, 65, 38], [68, 42, 65, 40], [68, 46, 65, 44], [68, 47, 65, 45, "keyUp<PERSON><PERSON><PERSON>"], [68, 60, 65, 58], [68, 61, 65, 59], [69, 4, 66, 2], [70, 4, 68, 2, "unregisterListeners"], [70, 23, 68, 21, "unregisterListeners"], [70, 24, 68, 21], [70, 26, 68, 24], [71, 6, 69, 4], [71, 10, 69, 8], [71, 11, 69, 9, "view"], [71, 15, 69, 13], [71, 16, 69, 14, "removeEventListener"], [71, 35, 69, 33], [71, 36, 69, 34], [71, 45, 69, 43], [71, 47, 69, 45], [71, 51, 69, 49], [71, 52, 69, 50, "keyDownCallback"], [71, 67, 69, 65], [71, 68, 69, 66], [72, 6, 70, 4], [72, 10, 70, 8], [72, 11, 70, 9, "view"], [72, 15, 70, 13], [72, 16, 70, 14, "removeEventListener"], [72, 35, 70, 33], [72, 36, 70, 34], [72, 43, 70, 41], [72, 45, 70, 43], [72, 49, 70, 47], [72, 50, 70, 48, "keyUp<PERSON><PERSON><PERSON>"], [72, 63, 70, 61], [72, 64, 70, 62], [73, 4, 71, 2], [74, 4, 73, 2, "mapEvent"], [74, 12, 73, 10, "mapEvent"], [74, 13, 73, 11, "event"], [74, 18, 73, 16], [74, 20, 73, 18, "eventType"], [74, 29, 73, 27], [74, 31, 73, 29], [75, 6, 74, 4], [75, 12, 74, 10, "viewRect"], [75, 20, 74, 18], [75, 23, 74, 21, "event"], [75, 28, 74, 26], [75, 29, 74, 27, "target"], [75, 35, 74, 33], [75, 36, 74, 34, "getBoundingClientRect"], [75, 57, 74, 55], [75, 58, 74, 56], [75, 59, 74, 57], [76, 6, 75, 4], [76, 12, 75, 10, "viewportPosition"], [76, 28, 75, 26], [76, 31, 75, 29], [77, 8, 76, 6, "x"], [77, 9, 76, 7], [77, 11, 76, 9], [77, 12, 76, 10, "viewRect"], [77, 20, 76, 18], [77, 25, 76, 23], [77, 29, 76, 27], [77, 33, 76, 31, "viewRect"], [77, 41, 76, 39], [77, 46, 76, 44], [77, 51, 76, 49], [77, 52, 76, 50], [77, 55, 76, 53], [77, 60, 76, 58], [77, 61, 76, 59], [77, 64, 76, 62, "viewRect"], [77, 72, 76, 70], [77, 73, 76, 71, "x"], [77, 74, 76, 72], [77, 78, 76, 76], [77, 79, 76, 77, "viewRect"], [77, 87, 76, 85], [77, 92, 76, 90], [77, 96, 76, 94], [77, 100, 76, 98, "viewRect"], [77, 108, 76, 106], [77, 113, 76, 111], [77, 118, 76, 116], [77, 119, 76, 117], [77, 122, 76, 120], [77, 127, 76, 125], [77, 128, 76, 126], [77, 131, 76, 129, "viewRect"], [77, 139, 76, 137], [77, 140, 76, 138, "width"], [77, 145, 76, 143], [77, 149, 76, 147], [77, 150, 76, 148], [78, 8, 77, 6, "y"], [78, 9, 77, 7], [78, 11, 77, 9], [78, 12, 77, 10, "viewRect"], [78, 20, 77, 18], [78, 25, 77, 23], [78, 29, 77, 27], [78, 33, 77, 31, "viewRect"], [78, 41, 77, 39], [78, 46, 77, 44], [78, 51, 77, 49], [78, 52, 77, 50], [78, 55, 77, 53], [78, 60, 77, 58], [78, 61, 77, 59], [78, 64, 77, 62, "viewRect"], [78, 72, 77, 70], [78, 73, 77, 71, "y"], [78, 74, 77, 72], [78, 78, 77, 76], [78, 79, 77, 77, "viewRect"], [78, 87, 77, 85], [78, 92, 77, 90], [78, 96, 77, 94], [78, 100, 77, 98, "viewRect"], [78, 108, 77, 106], [78, 113, 77, 111], [78, 118, 77, 116], [78, 119, 77, 117], [78, 122, 77, 120], [78, 127, 77, 125], [78, 128, 77, 126], [78, 131, 77, 129, "viewRect"], [78, 139, 77, 137], [78, 140, 77, 138, "height"], [78, 146, 77, 144], [78, 150, 77, 148], [79, 6, 78, 4], [79, 7, 78, 5], [80, 6, 79, 4], [80, 12, 79, 10, "relativePosition"], [80, 28, 79, 26], [80, 31, 79, 29], [81, 8, 80, 6, "x"], [81, 9, 80, 7], [81, 11, 80, 9], [81, 12, 80, 10, "viewRect"], [81, 20, 80, 18], [81, 25, 80, 23], [81, 29, 80, 27], [81, 33, 80, 31, "viewRect"], [81, 41, 80, 39], [81, 46, 80, 44], [81, 51, 80, 49], [81, 52, 80, 50], [81, 55, 80, 53], [81, 60, 80, 58], [81, 61, 80, 59], [81, 64, 80, 62, "viewRect"], [81, 72, 80, 70], [81, 73, 80, 71, "width"], [81, 78, 80, 76], [81, 82, 80, 80], [81, 83, 80, 81], [82, 8, 81, 6, "y"], [82, 9, 81, 7], [82, 11, 81, 9], [82, 12, 81, 10, "viewRect"], [82, 20, 81, 18], [82, 25, 81, 23], [82, 29, 81, 27], [82, 33, 81, 31, "viewRect"], [82, 41, 81, 39], [82, 46, 81, 44], [82, 51, 81, 49], [82, 52, 81, 50], [82, 55, 81, 53], [82, 60, 81, 58], [82, 61, 81, 59], [82, 64, 81, 62, "viewRect"], [82, 72, 81, 70], [82, 73, 81, 71, "height"], [82, 79, 81, 77], [82, 83, 81, 81], [83, 6, 82, 4], [83, 7, 82, 5], [84, 6, 83, 4], [84, 13, 83, 11], [85, 8, 84, 6, "x"], [85, 9, 84, 7], [85, 11, 84, 9, "viewportPosition"], [85, 27, 84, 25], [85, 28, 84, 26, "x"], [85, 29, 84, 27], [86, 8, 85, 6, "y"], [86, 9, 85, 7], [86, 11, 85, 9, "viewportPosition"], [86, 27, 85, 25], [86, 28, 85, 26, "y"], [86, 29, 85, 27], [87, 8, 86, 6, "offsetX"], [87, 15, 86, 13], [87, 17, 86, 15, "relativePosition"], [87, 33, 86, 31], [87, 34, 86, 32, "x"], [87, 35, 86, 33], [88, 8, 87, 6, "offsetY"], [88, 15, 87, 13], [88, 17, 87, 15, "relativePosition"], [88, 33, 87, 31], [88, 34, 87, 32, "y"], [88, 35, 87, 33], [89, 8, 88, 6, "pointerId"], [89, 17, 88, 15], [89, 19, 88, 17], [89, 20, 88, 18], [90, 8, 89, 6, "eventType"], [90, 17, 89, 15], [90, 19, 89, 17, "eventType"], [90, 28, 89, 26], [91, 8, 90, 6, "pointerType"], [91, 19, 90, 17], [91, 21, 90, 19, "PointerType"], [91, 45, 90, 30], [91, 46, 90, 31, "KEY"], [91, 49, 90, 34], [92, 8, 91, 6, "time"], [92, 12, 91, 10], [92, 14, 91, 12, "event"], [92, 19, 91, 17], [92, 20, 91, 18, "timeStamp"], [93, 6, 92, 4], [93, 7, 92, 5], [94, 4, 93, 2], [95, 2, 95, 0], [96, 2, 95, 1, "exports"], [96, 9, 95, 1], [96, 10, 95, 1, "default"], [96, 17, 95, 1], [96, 20, 95, 1, "KeyboardEventManager"], [96, 40, 95, 1], [97, 0, 95, 1], [97, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "KeyboardEventManager", "constructor", "_defineProperty$argument_2", "dispatchEvent", "registerListeners", "unregisterListeners", "mapEvent"], "mappings": "AAA,iNC;eCK;ECC;6CCS;KDW;2CCE;KDM;GDC;EGE;GHuB;EIE;GJG;EKE;GLG;EME;GNoB;CDE"}}, "type": "js/module"}]}