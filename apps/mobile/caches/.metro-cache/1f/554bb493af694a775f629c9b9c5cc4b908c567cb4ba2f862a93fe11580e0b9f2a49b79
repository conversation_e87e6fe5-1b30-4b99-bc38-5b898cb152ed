{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /** Imported from react-native */\n\n  /* eslint-disable */\n  /**\n   * This is a helper function for when a component needs to be able to forward a\n   * ref to a child component, but still needs to have access to that component as\n   * part of its implementation.\n   *\n   * Its main use case is in wrappers for native components.\n   *\n   * Usage:\n   *\n   * Class MyView extends React.Component { _nativeRef = null;\n   *\n   *     _setNativeRef = setAndForwardRef({\n   *       getForwardedRef: () => this.props.forwardedRef,\n   *       setLocalRef: ref => {\n   *         this._nativeRef = ref;\n   *       },\n   *     });\n   *\n   *     render() {\n   *       return <View ref={this._setNativeRef} />;\n   *     }\n   *\n   * }\n   *\n   * Const MyViewWithRef = React.forwardRef((props, ref) => ( <MyView {...props}\n   * forwardedRef={ref} /> ));\n   *\n   * Module.exports = MyViewWithRef;\n   */\n  /* eslint-enable */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function setAndForwardRef({\n    getForwardedRef,\n    setLocalRef\n  }) {\n    return function forwardRef(ref) {\n      const forwardedRef = getForwardedRef();\n      setLocalRef(ref);\n\n      // Forward to user ref prop (if one has been specified)\n      if (typeof forwardedRef === 'function') {\n        // Handle function-based refs. String-based refs are handled as functions.\n        forwardedRef(ref);\n      } else if (typeof forwardedRef === 'object' && forwardedRef != null) {\n        // Handle createRef-based refs\n        forwardedRef.current = ref;\n      }\n    };\n  }\n  var _default = exports.default = setAndForwardRef;\n});", "lineCount": 60, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [6, 2, 5, 0], [7, 2, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 0, 17, 0], [19, 0, 18, 0], [20, 0, 19, 0], [21, 0, 20, 0], [22, 0, 21, 0], [23, 0, 22, 0], [24, 0, 23, 0], [25, 0, 24, 0], [26, 0, 25, 0], [27, 0, 26, 0], [28, 0, 27, 0], [29, 0, 28, 0], [30, 0, 29, 0], [31, 0, 30, 0], [32, 0, 31, 0], [33, 0, 32, 0], [34, 0, 33, 0], [35, 0, 34, 0], [36, 2, 35, 0], [37, 2, 35, 0, "Object"], [37, 8, 35, 0], [37, 9, 35, 0, "defineProperty"], [37, 23, 35, 0], [37, 24, 35, 0, "exports"], [37, 31, 35, 0], [38, 4, 35, 0, "value"], [38, 9, 35, 0], [39, 2, 35, 0], [40, 2, 35, 0, "exports"], [40, 9, 35, 0], [40, 10, 35, 0, "default"], [40, 17, 35, 0], [41, 2, 37, 0], [41, 11, 37, 9, "setAndForwardRef"], [41, 27, 37, 25, "setAndForwardRef"], [41, 28, 37, 26], [42, 4, 38, 2, "getForwardedRef"], [42, 19, 38, 17], [43, 4, 39, 2, "setLocalRef"], [44, 2, 40, 0], [44, 3, 40, 1], [44, 5, 40, 3], [45, 4, 41, 2], [45, 11, 41, 9], [45, 20, 41, 18, "forwardRef"], [45, 30, 41, 28, "forwardRef"], [45, 31, 41, 29, "ref"], [45, 34, 41, 32], [45, 36, 41, 34], [46, 6, 42, 4], [46, 12, 42, 10, "forwardedRef"], [46, 24, 42, 22], [46, 27, 42, 25, "getForwardedRef"], [46, 42, 42, 40], [46, 43, 42, 41], [46, 44, 42, 42], [47, 6, 43, 4, "setLocalRef"], [47, 17, 43, 15], [47, 18, 43, 16, "ref"], [47, 21, 43, 19], [47, 22, 43, 20], [49, 6, 45, 4], [50, 6, 46, 4], [50, 10, 46, 8], [50, 17, 46, 15, "forwardedRef"], [50, 29, 46, 27], [50, 34, 46, 32], [50, 44, 46, 42], [50, 46, 46, 44], [51, 8, 47, 6], [52, 8, 48, 6, "forwardedRef"], [52, 20, 48, 18], [52, 21, 48, 19, "ref"], [52, 24, 48, 22], [52, 25, 48, 23], [53, 6, 49, 4], [53, 7, 49, 5], [53, 13, 49, 11], [53, 17, 49, 15], [53, 24, 49, 22, "forwardedRef"], [53, 36, 49, 34], [53, 41, 49, 39], [53, 49, 49, 47], [53, 53, 49, 51, "forwardedRef"], [53, 65, 49, 63], [53, 69, 49, 67], [53, 73, 49, 71], [53, 75, 49, 73], [54, 8, 50, 6], [55, 8, 51, 6, "forwardedRef"], [55, 20, 51, 18], [55, 21, 51, 19, "current"], [55, 28, 51, 26], [55, 31, 51, 29, "ref"], [55, 34, 51, 32], [56, 6, 52, 4], [57, 4, 53, 2], [57, 5, 53, 3], [58, 2, 54, 0], [59, 2, 54, 1], [59, 6, 54, 1, "_default"], [59, 14, 54, 1], [59, 17, 54, 1, "exports"], [59, 24, 54, 1], [59, 25, 54, 1, "default"], [59, 32, 54, 1], [59, 35, 55, 15, "setAndForwardRef"], [59, 51, 55, 31], [60, 0, 55, 31], [60, 3]], "functionMap": {"names": ["<global>", "setAndForwardRef", "forwardRef"], "mappings": "AAA;ACoC;SCI;GDY;CDC"}}, "type": "js/module"}]}