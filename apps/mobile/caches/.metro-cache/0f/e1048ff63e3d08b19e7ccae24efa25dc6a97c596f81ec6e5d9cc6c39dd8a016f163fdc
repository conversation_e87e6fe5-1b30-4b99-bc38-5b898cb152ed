{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-asset", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 35, "index": 35}}], "key": "ZXJFWHziJpBZf3W7vl00wXf6fd8=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 36}, "end": {"line": 2, "column": 47, "index": 83}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./ExpoFontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 84}, "end": {"line": 3, "column": 46, "index": 130}}], "key": "7dk3JQGwGYesJt8OOG3pkBz+dtE=", "exportNames": ["*"]}}, {"name": "./Font.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 131}, "end": {"line": 4, "column": 43, "index": 174}}], "key": "iwvcxaVgfIXdww6iPrKSgtcaZy8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getAssetForSource = getAssetForSource;\n  exports.loadSingleFontAsync = loadSingleFontAsync;\n  var _expoAsset = require(_dependencyMap[1], \"expo-asset\");\n  var _expoModulesCore = require(_dependencyMap[2], \"expo-modules-core\");\n  var _ExpoFontLoader = _interopRequireDefault(require(_dependencyMap[3], \"./ExpoFontLoader\"));\n  var _Font = require(_dependencyMap[4], \"./Font.types\");\n  function uriFromFontSource(asset) {\n    if (typeof asset === 'string') {\n      return asset || null;\n    } else if (typeof asset === 'number') {\n      return uriFromFontSource(_expoAsset.Asset.fromModule(asset));\n    } else if (typeof asset === 'object' && typeof asset.uri === 'number') {\n      return uriFromFontSource(asset.uri);\n    } else if (typeof asset === 'object') {\n      return asset.uri || asset.localUri || asset.default || null;\n    }\n    return null;\n  }\n  function displayFromFontSource(asset) {\n    if (typeof asset === 'object' && 'display' in asset) {\n      return asset.display || _Font.FontDisplay.AUTO;\n    }\n    return _Font.FontDisplay.AUTO;\n  }\n  function getAssetForSource(source) {\n    const uri = uriFromFontSource(source);\n    const display = displayFromFontSource(source);\n    if (!uri || typeof uri !== 'string') {\n      throwInvalidSourceError(uri);\n    }\n    return {\n      uri,\n      display\n    };\n  }\n  function throwInvalidSourceError(source) {\n    let type = typeof source;\n    if (type === 'object') type = JSON.stringify(source, null, 2);\n    throw new _expoModulesCore.CodedError(`ERR_FONT_SOURCE`, `Expected font asset of type \\`string | FontResource | Asset\\` instead got: ${type}`);\n  }\n  // NOTE(EvanBacon): No async keyword!\n  function loadSingleFontAsync(name, input) {\n    if (typeof input !== 'object' || typeof input.uri !== 'string' || input.downloadAsync) {\n      throwInvalidSourceError(input);\n    }\n    try {\n      return _ExpoFontLoader.default.loadAsync(name, input);\n    } catch {\n      // No-op.\n    }\n    return Promise.resolve();\n  }\n});", "lineCount": 58, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoAsset"], [8, 16, 1, 0], [8, 19, 1, 0, "require"], [8, 26, 1, 0], [8, 27, 1, 0, "_dependencyMap"], [8, 41, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_expoModulesCore"], [9, 22, 2, 0], [9, 25, 2, 0, "require"], [9, 32, 2, 0], [9, 33, 2, 0, "_dependencyMap"], [9, 47, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_ExpoFontLoader"], [10, 21, 3, 0], [10, 24, 3, 0, "_interopRequireDefault"], [10, 46, 3, 0], [10, 47, 3, 0, "require"], [10, 54, 3, 0], [10, 55, 3, 0, "_dependencyMap"], [10, 69, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_Font"], [11, 11, 4, 0], [11, 14, 4, 0, "require"], [11, 21, 4, 0], [11, 22, 4, 0, "_dependencyMap"], [11, 36, 4, 0], [12, 2, 5, 0], [12, 11, 5, 9, "uriFromFontSource"], [12, 28, 5, 26, "uriFromFontSource"], [12, 29, 5, 27, "asset"], [12, 34, 5, 32], [12, 36, 5, 34], [13, 4, 6, 4], [13, 8, 6, 8], [13, 15, 6, 15, "asset"], [13, 20, 6, 20], [13, 25, 6, 25], [13, 33, 6, 33], [13, 35, 6, 35], [14, 6, 7, 8], [14, 13, 7, 15, "asset"], [14, 18, 7, 20], [14, 22, 7, 24], [14, 26, 7, 28], [15, 4, 8, 4], [15, 5, 8, 5], [15, 11, 9, 9], [15, 15, 9, 13], [15, 22, 9, 20, "asset"], [15, 27, 9, 25], [15, 32, 9, 30], [15, 40, 9, 38], [15, 42, 9, 40], [16, 6, 10, 8], [16, 13, 10, 15, "uriFromFontSource"], [16, 30, 10, 32], [16, 31, 10, 33, "<PERSON><PERSON>"], [16, 47, 10, 38], [16, 48, 10, 39, "fromModule"], [16, 58, 10, 49], [16, 59, 10, 50, "asset"], [16, 64, 10, 55], [16, 65, 10, 56], [16, 66, 10, 57], [17, 4, 11, 4], [17, 5, 11, 5], [17, 11, 12, 9], [17, 15, 12, 13], [17, 22, 12, 20, "asset"], [17, 27, 12, 25], [17, 32, 12, 30], [17, 40, 12, 38], [17, 44, 12, 42], [17, 51, 12, 49, "asset"], [17, 56, 12, 54], [17, 57, 12, 55, "uri"], [17, 60, 12, 58], [17, 65, 12, 63], [17, 73, 12, 71], [17, 75, 12, 73], [18, 6, 13, 8], [18, 13, 13, 15, "uriFromFontSource"], [18, 30, 13, 32], [18, 31, 13, 33, "asset"], [18, 36, 13, 38], [18, 37, 13, 39, "uri"], [18, 40, 13, 42], [18, 41, 13, 43], [19, 4, 14, 4], [19, 5, 14, 5], [19, 11, 15, 9], [19, 15, 15, 13], [19, 22, 15, 20, "asset"], [19, 27, 15, 25], [19, 32, 15, 30], [19, 40, 15, 38], [19, 42, 15, 40], [20, 6, 16, 8], [20, 13, 16, 15, "asset"], [20, 18, 16, 20], [20, 19, 16, 21, "uri"], [20, 22, 16, 24], [20, 26, 16, 28, "asset"], [20, 31, 16, 33], [20, 32, 16, 34, "localUri"], [20, 40, 16, 42], [20, 44, 16, 46, "asset"], [20, 49, 16, 51], [20, 50, 16, 52, "default"], [20, 57, 16, 59], [20, 61, 16, 63], [20, 65, 16, 67], [21, 4, 17, 4], [22, 4, 18, 4], [22, 11, 18, 11], [22, 15, 18, 15], [23, 2, 19, 0], [24, 2, 20, 0], [24, 11, 20, 9, "displayFromFontSource"], [24, 32, 20, 30, "displayFromFontSource"], [24, 33, 20, 31, "asset"], [24, 38, 20, 36], [24, 40, 20, 38], [25, 4, 21, 4], [25, 8, 21, 8], [25, 15, 21, 15, "asset"], [25, 20, 21, 20], [25, 25, 21, 25], [25, 33, 21, 33], [25, 37, 21, 37], [25, 46, 21, 46], [25, 50, 21, 50, "asset"], [25, 55, 21, 55], [25, 57, 21, 57], [26, 6, 22, 8], [26, 13, 22, 15, "asset"], [26, 18, 22, 20], [26, 19, 22, 21, "display"], [26, 26, 22, 28], [26, 30, 22, 32, "FontDisplay"], [26, 47, 22, 43], [26, 48, 22, 44, "AUTO"], [26, 52, 22, 48], [27, 4, 23, 4], [28, 4, 24, 4], [28, 11, 24, 11, "FontDisplay"], [28, 28, 24, 22], [28, 29, 24, 23, "AUTO"], [28, 33, 24, 27], [29, 2, 25, 0], [30, 2, 26, 7], [30, 11, 26, 16, "getAssetForSource"], [30, 28, 26, 33, "getAssetForSource"], [30, 29, 26, 34, "source"], [30, 35, 26, 40], [30, 37, 26, 42], [31, 4, 27, 4], [31, 10, 27, 10, "uri"], [31, 13, 27, 13], [31, 16, 27, 16, "uriFromFontSource"], [31, 33, 27, 33], [31, 34, 27, 34, "source"], [31, 40, 27, 40], [31, 41, 27, 41], [32, 4, 28, 4], [32, 10, 28, 10, "display"], [32, 17, 28, 17], [32, 20, 28, 20, "displayFromFontSource"], [32, 41, 28, 41], [32, 42, 28, 42, "source"], [32, 48, 28, 48], [32, 49, 28, 49], [33, 4, 29, 4], [33, 8, 29, 8], [33, 9, 29, 9, "uri"], [33, 12, 29, 12], [33, 16, 29, 16], [33, 23, 29, 23, "uri"], [33, 26, 29, 26], [33, 31, 29, 31], [33, 39, 29, 39], [33, 41, 29, 41], [34, 6, 30, 8, "throwInvalidSourceError"], [34, 29, 30, 31], [34, 30, 30, 32, "uri"], [34, 33, 30, 35], [34, 34, 30, 36], [35, 4, 31, 4], [36, 4, 32, 4], [36, 11, 32, 11], [37, 6, 33, 8, "uri"], [37, 9, 33, 11], [38, 6, 34, 8, "display"], [39, 4, 35, 4], [39, 5, 35, 5], [40, 2, 36, 0], [41, 2, 37, 0], [41, 11, 37, 9, "throwInvalidSourceError"], [41, 34, 37, 32, "throwInvalidSourceError"], [41, 35, 37, 33, "source"], [41, 41, 37, 39], [41, 43, 37, 41], [42, 4, 38, 4], [42, 8, 38, 8, "type"], [42, 12, 38, 12], [42, 15, 38, 15], [42, 22, 38, 22, "source"], [42, 28, 38, 28], [43, 4, 39, 4], [43, 8, 39, 8, "type"], [43, 12, 39, 12], [43, 17, 39, 17], [43, 25, 39, 25], [43, 27, 40, 8, "type"], [43, 31, 40, 12], [43, 34, 40, 15, "JSON"], [43, 38, 40, 19], [43, 39, 40, 20, "stringify"], [43, 48, 40, 29], [43, 49, 40, 30, "source"], [43, 55, 40, 36], [43, 57, 40, 38], [43, 61, 40, 42], [43, 63, 40, 44], [43, 64, 40, 45], [43, 65, 40, 46], [44, 4, 41, 4], [44, 10, 41, 10], [44, 14, 41, 14, "CodedError"], [44, 41, 41, 24], [44, 42, 41, 25], [44, 59, 41, 42], [44, 61, 41, 44], [44, 139, 41, 122, "type"], [44, 143, 41, 126], [44, 145, 41, 128], [44, 146, 41, 129], [45, 2, 42, 0], [46, 2, 43, 0], [47, 2, 44, 7], [47, 11, 44, 16, "loadSingleFontAsync"], [47, 30, 44, 35, "loadSingleFontAsync"], [47, 31, 44, 36, "name"], [47, 35, 44, 40], [47, 37, 44, 42, "input"], [47, 42, 44, 47], [47, 44, 44, 49], [48, 4, 45, 4], [48, 8, 45, 8], [48, 15, 45, 15, "input"], [48, 20, 45, 20], [48, 25, 45, 25], [48, 33, 45, 33], [48, 37, 45, 37], [48, 44, 45, 44, "input"], [48, 49, 45, 49], [48, 50, 45, 50, "uri"], [48, 53, 45, 53], [48, 58, 45, 58], [48, 66, 45, 66], [48, 70, 45, 70, "input"], [48, 75, 45, 75], [48, 76, 45, 76, "downloadAsync"], [48, 89, 45, 89], [48, 91, 45, 91], [49, 6, 46, 8, "throwInvalidSourceError"], [49, 29, 46, 31], [49, 30, 46, 32, "input"], [49, 35, 46, 37], [49, 36, 46, 38], [50, 4, 47, 4], [51, 4, 48, 4], [51, 8, 48, 8], [52, 6, 49, 8], [52, 13, 49, 15, "ExpoFontLoader"], [52, 36, 49, 29], [52, 37, 49, 30, "loadAsync"], [52, 46, 49, 39], [52, 47, 49, 40, "name"], [52, 51, 49, 44], [52, 53, 49, 46, "input"], [52, 58, 49, 51], [52, 59, 49, 52], [53, 4, 50, 4], [53, 5, 50, 5], [53, 6, 51, 4], [53, 12, 51, 10], [54, 6, 52, 8], [55, 4, 52, 8], [56, 4, 54, 4], [56, 11, 54, 11, "Promise"], [56, 18, 54, 18], [56, 19, 54, 19, "resolve"], [56, 26, 54, 26], [56, 27, 54, 27], [56, 28, 54, 28], [57, 2, 55, 0], [58, 0, 55, 1], [58, 3]], "functionMap": {"names": ["<global>", "uriFromFontSource", "displayFromFontSource", "getAssetForSource", "throwInvalidSourceError", "loadSingleFontAsync"], "mappings": "AAA;ACI;CDc;AEC;CFK;OGC;CHU;AIC;CJK;OKE;CLW"}}, "type": "js/module"}]}