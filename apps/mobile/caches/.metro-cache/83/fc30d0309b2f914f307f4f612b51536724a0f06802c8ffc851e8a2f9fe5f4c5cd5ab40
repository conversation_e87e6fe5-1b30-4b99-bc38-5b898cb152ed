{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.assign = exports.TouchMouseInput = exports.TouchInput = exports.TouchAction = exports.Tap = exports.Swipe = exports.SingleTouchInput = exports.STATE_RECOGNIZED = exports.STATE_POSSIBLE = exports.STATE_FAILED = exports.STATE_ENDED = exports.STATE_CHANGED = exports.STATE_CANCELLED = exports.STATE_BEGAN = exports.Rotate = exports.Recognizer = exports.Press = exports.PointerEventInput = exports.Pinch = exports.Pan = exports.MouseInput = exports.Manager = exports.Input = exports.INPUT_START = exports.INPUT_MOVE = exports.INPUT_END = exports.INPUT_CANCEL = exports.DIRECTION_VERTICAL = exports.DIRECTION_UP = exports.DIRECTION_RIGHT = exports.DIRECTION_NONE = exports.DIRECTION_LEFT = exports.DIRECTION_HORIZONTAL = exports.DIRECTION_DOWN = exports.DIRECTION_ALL = exports.AttrRecognizer = void 0;\n  exports.bindFn = bindFn;\n  exports.boolOrFn = boolOrFn;\n  exports.defaults = exports.default = void 0;\n  exports.each = each;\n  exports.extend = void 0;\n  exports.hasParent = hasParent;\n  exports.inArray = inArray;\n  exports.inherit = inherit;\n  exports.merge = void 0;\n  exports.removeEventListeners = exports.off = removeEventListeners;\n  exports.addEventListeners = exports.on = addEventListeners;\n  exports.prefixed = prefixed;\n  exports.splitStr = splitStr;\n  exports.toArray = toArray;\n  exports.uniqueArray = uniqueArray;\n  /*! Hammer.JS - v2.0.17-rc - 2019-12-16\n   * http://naver.github.io/egjs\n   *\n   * Forked By Naver egjs\n   * Copyright (c) hammerjs\n   * Licensed under the MIT license */\n  function _extends() {\n    _extends = Object.assign || function (target) {\n      for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i];\n        for (var key in source) {\n          if (Object.prototype.hasOwnProperty.call(source, key)) {\n            target[key] = source[key];\n          }\n        }\n      }\n      return target;\n    };\n    return _extends.apply(this, arguments);\n  }\n  function _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    subClass.__proto__ = superClass;\n  }\n  function _assertThisInitialized(self) {\n    if (self === void 0) {\n      throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n  }\n\n  /**\n   * @private\n   * extend object.\n   * means that properties in dest will be overwritten by the ones in src.\n   * @param {Object} target\n   * @param {...Object} objects_to_assign\n   * @returns {Object} target\n   */\n  var assign;\n  if (typeof Object.assign !== 'function') {\n    assign = function assign(target) {\n      if (target === undefined || target === null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n      }\n      var output = Object(target);\n      for (var index = 1; index < arguments.length; index++) {\n        var source = arguments[index];\n        if (source !== undefined && source !== null) {\n          for (var nextKey in source) {\n            if (source.hasOwnProperty(nextKey)) {\n              output[nextKey] = source[nextKey];\n            }\n          }\n        }\n      }\n      return output;\n    };\n  } else {\n    assign = Object.assign;\n  }\n  var assign$1 = exports.assign = assign;\n  var VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\n  var TEST_ELEMENT = typeof document === \"undefined\" ? {\n    style: {}\n  } : document.createElement('div');\n  var TYPE_FUNCTION = 'function';\n  var round = Math.round,\n    abs = Math.abs;\n  var now = Date.now;\n\n  /**\n   * @private\n   * get the prefixed property\n   * @param {Object} obj\n   * @param {String} property\n   * @returns {String|Undefined} prefixed\n   */\n\n  function prefixed(obj, property) {\n    var prefix;\n    var prop;\n    var camelProp = property[0].toUpperCase() + property.slice(1);\n    var i = 0;\n    while (i < VENDOR_PREFIXES.length) {\n      prefix = VENDOR_PREFIXES[i];\n      prop = prefix ? prefix + camelProp : property;\n      if (prop in obj) {\n        return prop;\n      }\n      i++;\n    }\n    return undefined;\n  }\n\n  /* eslint-disable no-new-func, no-nested-ternary */\n  var win;\n  if (typeof window === \"undefined\") {\n    // window is undefined in node.js\n    win = {};\n  } else {\n    win = window;\n  }\n  var PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\n  var NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n  function getTouchActionProps() {\n    if (!NATIVE_TOUCH_ACTION) {\n      return false;\n    }\n    var touchMap = {};\n    var cssSupports = win.CSS && win.CSS.supports;\n    ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach(function (val) {\n      // If css.supports is not supported but there is native touch-action assume it supports\n      // all values. This is the case for IE 10 and 11.\n      return touchMap[val] = cssSupports ? win.CSS.supports('touch-action', val) : true;\n    });\n    return touchMap;\n  }\n  var TOUCH_ACTION_COMPUTE = 'compute';\n  var TOUCH_ACTION_AUTO = 'auto';\n  var TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\n\n  var TOUCH_ACTION_NONE = 'none';\n  var TOUCH_ACTION_PAN_X = 'pan-x';\n  var TOUCH_ACTION_PAN_Y = 'pan-y';\n  var TOUCH_ACTION_MAP = getTouchActionProps();\n  var MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n  var SUPPORT_TOUCH = 'ontouchstart' in win;\n  var SUPPORT_POINTER_EVENTS = prefixed(win, 'PointerEvent') !== undefined;\n  var SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n  var INPUT_TYPE_TOUCH = 'touch';\n  var INPUT_TYPE_PEN = 'pen';\n  var INPUT_TYPE_MOUSE = 'mouse';\n  var INPUT_TYPE_KINECT = 'kinect';\n  var COMPUTE_INTERVAL = 25;\n  var INPUT_START = exports.INPUT_START = 1;\n  var INPUT_MOVE = exports.INPUT_MOVE = 2;\n  var INPUT_END = exports.INPUT_END = 4;\n  var INPUT_CANCEL = exports.INPUT_CANCEL = 8;\n  var DIRECTION_NONE = exports.DIRECTION_NONE = 1;\n  var DIRECTION_LEFT = exports.DIRECTION_LEFT = 2;\n  var DIRECTION_RIGHT = exports.DIRECTION_RIGHT = 4;\n  var DIRECTION_UP = exports.DIRECTION_UP = 8;\n  var DIRECTION_DOWN = exports.DIRECTION_DOWN = 16;\n  var DIRECTION_HORIZONTAL = exports.DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\n  var DIRECTION_VERTICAL = exports.DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\n  var DIRECTION_ALL = exports.DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n  var PROPS_XY = ['x', 'y'];\n  var PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\n  /**\n   * @private\n   * walk objects and arrays\n   * @param {Object} obj\n   * @param {Function} iterator\n   * @param {Object} context\n   */\n  function each(obj, iterator, context) {\n    var i;\n    if (!obj) {\n      return;\n    }\n    if (obj.forEach) {\n      obj.forEach(iterator, context);\n    } else if (obj.length !== undefined) {\n      i = 0;\n      while (i < obj.length) {\n        iterator.call(context, obj[i], i, obj);\n        i++;\n      }\n    } else {\n      for (i in obj) {\n        obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n      }\n    }\n  }\n\n  /**\n   * @private\n   * let a boolean value also be a function that must return a boolean\n   * this first item in args will be used as the context\n   * @param {Boolean|Function} val\n   * @param {Array} [args]\n   * @returns {Boolean}\n   */\n\n  function boolOrFn(val, args) {\n    if (typeof val === TYPE_FUNCTION) {\n      return val.apply(args ? args[0] || undefined : undefined, args);\n    }\n    return val;\n  }\n\n  /**\n   * @private\n   * small indexOf wrapper\n   * @param {String} str\n   * @param {String} find\n   * @returns {Boolean} found\n   */\n  function inStr(str, find) {\n    return str.indexOf(find) > -1;\n  }\n\n  /**\n   * @private\n   * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n   * @param {String} actions\n   * @returns {*}\n   */\n\n  function cleanTouchActions(actions) {\n    // none\n    if (inStr(actions, TOUCH_ACTION_NONE)) {\n      return TOUCH_ACTION_NONE;\n    }\n    var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n    var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y); // if both pan-x and pan-y are set (different recognizers\n    // for different directions, e.g. horizontal pan but vertical swipe?)\n    // we need none (as otherwise with pan-x pan-y combined none of these\n    // recognizers will work, since the browser would handle all panning\n\n    if (hasPanX && hasPanY) {\n      return TOUCH_ACTION_NONE;\n    } // pan-x OR pan-y\n\n    if (hasPanX || hasPanY) {\n      return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n    } // manipulation\n\n    if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n      return TOUCH_ACTION_MANIPULATION;\n    }\n    return TOUCH_ACTION_AUTO;\n  }\n\n  /**\n   * @private\n   * Touch Action\n   * sets the touchAction property or uses the js alternative\n   * @param {Manager} manager\n   * @param {String} value\n   * @constructor\n   */\n\n  var TouchAction = exports.TouchAction = /*#__PURE__*/\n  function () {\n    function TouchAction(manager, value) {\n      this.manager = manager;\n      this.set(value);\n    }\n    /**\n     * @private\n     * set the touchAction value on the element or enable the polyfill\n     * @param {String} value\n     */\n\n    var _proto = TouchAction.prototype;\n    _proto.set = function set(value) {\n      // find out the touch-action by the event handlers\n      if (value === TOUCH_ACTION_COMPUTE) {\n        value = this.compute();\n      }\n      if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n        this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n      }\n      this.actions = value.toLowerCase().trim();\n    };\n    /**\n     * @private\n     * just re-set the touchAction value\n     */\n\n    _proto.update = function update() {\n      this.set(this.manager.options.touchAction);\n    };\n    /**\n     * @private\n     * compute the value for the touchAction property based on the recognizer's settings\n     * @returns {String} value\n     */\n\n    _proto.compute = function compute() {\n      var actions = [];\n      each(this.manager.recognizers, function (recognizer) {\n        if (boolOrFn(recognizer.options.enable, [recognizer])) {\n          actions = actions.concat(recognizer.getTouchAction());\n        }\n      });\n      return cleanTouchActions(actions.join(' '));\n    };\n    /**\n     * @private\n     * this method is called on each input cycle and provides the preventing of the browser behavior\n     * @param {Object} input\n     */\n\n    _proto.preventDefaults = function preventDefaults(input) {\n      var srcEvent = input.srcEvent;\n      var direction = input.offsetDirection; // if the touch action did prevented once this session\n\n      if (this.manager.session.prevented) {\n        srcEvent.preventDefault();\n        return;\n      }\n      var actions = this.actions;\n      var hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n      var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n      var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n      if (hasNone) {\n        // do not prevent defaults if this is a tap gesture\n        var isTapPointer = input.pointers.length === 1;\n        var isTapMovement = input.distance < 2;\n        var isTapTouchTime = input.deltaTime < 250;\n        if (isTapPointer && isTapMovement && isTapTouchTime) {\n          return;\n        }\n      }\n      if (hasPanX && hasPanY) {\n        // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n        return;\n      }\n      if (hasNone || hasPanY && direction & DIRECTION_HORIZONTAL || hasPanX && direction & DIRECTION_VERTICAL) {\n        return this.preventSrc(srcEvent);\n      }\n    };\n    /**\n     * @private\n     * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n     * @param {Object} srcEvent\n     */\n\n    _proto.preventSrc = function preventSrc(srcEvent) {\n      this.manager.session.prevented = true;\n      srcEvent.preventDefault();\n    };\n    return TouchAction;\n  }();\n\n  /**\n   * @private\n   * find if a node is in the given parent\n   * @method hasParent\n   * @param {HTMLElement} node\n   * @param {HTMLElement} parent\n   * @return {Boolean} found\n   */\n  function hasParent(node, parent) {\n    while (node) {\n      if (node === parent) {\n        return true;\n      }\n      node = node.parentNode;\n    }\n    return false;\n  }\n\n  /**\n   * @private\n   * get the center of all the pointers\n   * @param {Array} pointers\n   * @return {Object} center contains `x` and `y` properties\n   */\n\n  function getCenter(pointers) {\n    var pointersLength = pointers.length; // no need to loop when only one touch\n\n    if (pointersLength === 1) {\n      return {\n        x: round(pointers[0].clientX),\n        y: round(pointers[0].clientY)\n      };\n    }\n    var x = 0;\n    var y = 0;\n    var i = 0;\n    while (i < pointersLength) {\n      x += pointers[i].clientX;\n      y += pointers[i].clientY;\n      i++;\n    }\n    return {\n      x: round(x / pointersLength),\n      y: round(y / pointersLength)\n    };\n  }\n\n  /**\n   * @private\n   * create a simple clone from the input used for storage of firstInput and firstMultiple\n   * @param {Object} input\n   * @returns {Object} clonedInputData\n   */\n\n  function simpleCloneInputData(input) {\n    // make a simple copy of the pointers because we will get a reference if we don't\n    // we only need clientXY for the calculations\n    var pointers = [];\n    var i = 0;\n    while (i < input.pointers.length) {\n      pointers[i] = {\n        clientX: round(input.pointers[i].clientX),\n        clientY: round(input.pointers[i].clientY)\n      };\n      i++;\n    }\n    return {\n      timeStamp: now(),\n      pointers: pointers,\n      center: getCenter(pointers),\n      deltaX: input.deltaX,\n      deltaY: input.deltaY\n    };\n  }\n\n  /**\n   * @private\n   * calculate the absolute distance between two points\n   * @param {Object} p1 {x, y}\n   * @param {Object} p2 {x, y}\n   * @param {Array} [props] containing x and y keys\n   * @return {Number} distance\n   */\n\n  function getDistance(p1, p2, props) {\n    if (!props) {\n      props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]];\n    var y = p2[props[1]] - p1[props[1]];\n    return Math.sqrt(x * x + y * y);\n  }\n\n  /**\n   * @private\n   * calculate the angle between two coordinates\n   * @param {Object} p1\n   * @param {Object} p2\n   * @param {Array} [props] containing x and y keys\n   * @return {Number} angle\n   */\n\n  function getAngle(p1, p2, props) {\n    if (!props) {\n      props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]];\n    var y = p2[props[1]] - p1[props[1]];\n    return Math.atan2(y, x) * 180 / Math.PI;\n  }\n\n  /**\n   * @private\n   * get the direction between two points\n   * @param {Number} x\n   * @param {Number} y\n   * @return {Number} direction\n   */\n\n  function getDirection(x, y) {\n    if (x === y) {\n      return DIRECTION_NONE;\n    }\n    if (abs(x) >= abs(y)) {\n      return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n  }\n  function computeDeltaXY(session, input) {\n    var center = input.center; // let { offsetDelta:offset = {}, prevDelta = {}, prevInput = {} } = session;\n    // jscs throwing error on defalut destructured values and without defaults tests fail\n\n    var offset = session.offsetDelta || {};\n    var prevDelta = session.prevDelta || {};\n    var prevInput = session.prevInput || {};\n    if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n      prevDelta = session.prevDelta = {\n        x: prevInput.deltaX || 0,\n        y: prevInput.deltaY || 0\n      };\n      offset = session.offsetDelta = {\n        x: center.x,\n        y: center.y\n      };\n    }\n    input.deltaX = prevDelta.x + (center.x - offset.x);\n    input.deltaY = prevDelta.y + (center.y - offset.y);\n  }\n\n  /**\n   * @private\n   * calculate the velocity between two points. unit is in px per ms.\n   * @param {Number} deltaTime\n   * @param {Number} x\n   * @param {Number} y\n   * @return {Object} velocity `x` and `y`\n   */\n  function getVelocity(deltaTime, x, y) {\n    return {\n      x: x / deltaTime || 0,\n      y: y / deltaTime || 0\n    };\n  }\n\n  /**\n   * @private\n   * calculate the scale factor between two pointersets\n   * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n   * @param {Array} start array of pointers\n   * @param {Array} end array of pointers\n   * @return {Number} scale\n   */\n\n  function getScale(start, end) {\n    return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n  }\n\n  /**\n   * @private\n   * calculate the rotation degrees between two pointersets\n   * @param {Array} start array of pointers\n   * @param {Array} end array of pointers\n   * @return {Number} rotation\n   */\n\n  function getRotation(start, end) {\n    return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n  }\n\n  /**\n   * @private\n   * velocity is calculated every x ms\n   * @param {Object} session\n   * @param {Object} input\n   */\n\n  function computeIntervalInputData(session, input) {\n    var last = session.lastInterval || input;\n    var deltaTime = input.timeStamp - last.timeStamp;\n    var velocity;\n    var velocityX;\n    var velocityY;\n    var direction;\n    if (input.eventType !== INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n      var deltaX = input.deltaX - last.deltaX;\n      var deltaY = input.deltaY - last.deltaY;\n      var v = getVelocity(deltaTime, deltaX, deltaY);\n      velocityX = v.x;\n      velocityY = v.y;\n      velocity = abs(v.x) > abs(v.y) ? v.x : v.y;\n      direction = getDirection(deltaX, deltaY);\n      session.lastInterval = input;\n    } else {\n      // use latest velocity info if it doesn't overtake a minimum period\n      velocity = last.velocity;\n      velocityX = last.velocityX;\n      velocityY = last.velocityY;\n      direction = last.direction;\n    }\n    input.velocity = velocity;\n    input.velocityX = velocityX;\n    input.velocityY = velocityY;\n    input.direction = direction;\n  }\n\n  /**\n  * @private\n   * extend the data with some usable properties like scale, rotate, velocity etc\n   * @param {Object} manager\n   * @param {Object} input\n   */\n\n  function computeInputData(manager, input) {\n    var session = manager.session;\n    var pointers = input.pointers;\n    var pointersLength = pointers.length; // store the first input to calculate the distance and direction\n\n    if (!session.firstInput) {\n      session.firstInput = simpleCloneInputData(input);\n    } // to compute scale and rotation we need to store the multiple touches\n\n    if (pointersLength > 1 && !session.firstMultiple) {\n      session.firstMultiple = simpleCloneInputData(input);\n    } else if (pointersLength === 1) {\n      session.firstMultiple = false;\n    }\n    var firstInput = session.firstInput,\n      firstMultiple = session.firstMultiple;\n    var offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n    var center = input.center = getCenter(pointers);\n    input.timeStamp = now();\n    input.deltaTime = input.timeStamp - firstInput.timeStamp;\n    input.angle = getAngle(offsetCenter, center);\n    input.distance = getDistance(offsetCenter, center);\n    computeDeltaXY(session, input);\n    input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n    var overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n    input.overallVelocityX = overallVelocity.x;\n    input.overallVelocityY = overallVelocity.y;\n    input.overallVelocity = abs(overallVelocity.x) > abs(overallVelocity.y) ? overallVelocity.x : overallVelocity.y;\n    input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n    input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n    input.maxPointers = !session.prevInput ? input.pointers.length : input.pointers.length > session.prevInput.maxPointers ? input.pointers.length : session.prevInput.maxPointers;\n    computeIntervalInputData(session, input); // find the correct target\n\n    var target = manager.element;\n    var srcEvent = input.srcEvent;\n    var srcEventTarget;\n    if (srcEvent.composedPath) {\n      srcEventTarget = srcEvent.composedPath()[0];\n    } else if (srcEvent.path) {\n      srcEventTarget = srcEvent.path[0];\n    } else {\n      srcEventTarget = srcEvent.target;\n    }\n    if (hasParent(srcEventTarget, target)) {\n      target = srcEventTarget;\n    }\n    input.target = target;\n  }\n\n  /**\n   * @private\n   * handle input events\n   * @param {Manager} manager\n   * @param {String} eventType\n   * @param {Object} input\n   */\n\n  function inputHandler(manager, eventType, input) {\n    var pointersLen = input.pointers.length;\n    var changedPointersLen = input.changedPointers.length;\n    var isFirst = eventType & INPUT_START && pointersLen - changedPointersLen === 0;\n    var isFinal = eventType & (INPUT_END | INPUT_CANCEL) && pointersLen - changedPointersLen === 0;\n    input.isFirst = !!isFirst;\n    input.isFinal = !!isFinal;\n    if (isFirst) {\n      manager.session = {};\n    } // source event is the normalized value of the domEvents\n    // like 'touchstart, mouseup, pointerdown'\n\n    input.eventType = eventType; // compute scale, rotation etc\n\n    computeInputData(manager, input); // emit secret event\n\n    manager.emit('hammer.input', input);\n    manager.recognize(input);\n    manager.session.prevInput = input;\n  }\n\n  /**\n   * @private\n   * split string on whitespace\n   * @param {String} str\n   * @returns {Array} words\n   */\n  function splitStr(str) {\n    return str.trim().split(/\\s+/g);\n  }\n\n  /**\n   * @private\n   * addEventListener with multiple events at once\n   * @param {EventTarget} target\n   * @param {String} types\n   * @param {Function} handler\n   */\n\n  function addEventListeners(target, types, handler) {\n    each(splitStr(types), function (type) {\n      target.addEventListener(type, handler, false);\n    });\n  }\n\n  /**\n   * @private\n   * removeEventListener with multiple events at once\n   * @param {EventTarget} target\n   * @param {String} types\n   * @param {Function} handler\n   */\n\n  function removeEventListeners(target, types, handler) {\n    each(splitStr(types), function (type) {\n      target.removeEventListener(type, handler, false);\n    });\n  }\n\n  /**\n   * @private\n   * get the window object of an element\n   * @param {HTMLElement} element\n   * @returns {DocumentView|Window}\n   */\n  function getWindowForElement(element) {\n    var doc = element.ownerDocument || element;\n    return doc.defaultView || doc.parentWindow || window;\n  }\n\n  /**\n   * @private\n   * create new input type manager\n   * @param {Manager} manager\n   * @param {Function} callback\n   * @returns {Input}\n   * @constructor\n   */\n\n  var Input = exports.Input = /*#__PURE__*/\n  function () {\n    function Input(manager, callback) {\n      var self = this;\n      this.manager = manager;\n      this.callback = callback;\n      this.element = manager.element;\n      this.target = manager.options.inputTarget; // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n      // so when disabled the input events are completely bypassed.\n\n      this.domHandler = function (ev) {\n        if (boolOrFn(manager.options.enable, [manager])) {\n          self.handler(ev);\n        }\n      };\n      this.init();\n    }\n    /**\n     * @private\n     * should handle the inputEvent data and trigger the callback\n     * @virtual\n     */\n\n    var _proto = Input.prototype;\n    _proto.handler = function handler() {};\n    /**\n     * @private\n     * bind the events\n     */\n\n    _proto.init = function init() {\n      this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n      this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n      this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    };\n    /**\n     * @private\n     * unbind the events\n     */\n\n    _proto.destroy = function destroy() {\n      this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n      this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n      this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    };\n    return Input;\n  }();\n\n  /**\n   * @private\n   * find if a array contains the object using indexOf or a simple polyFill\n   * @param {Array} src\n   * @param {String} find\n   * @param {String} [findByKey]\n   * @return {Boolean|Number} false when not found, or the index\n   */\n  function inArray(src, find, findByKey) {\n    if (src.indexOf && !findByKey) {\n      return src.indexOf(find);\n    } else {\n      var i = 0;\n      while (i < src.length) {\n        if (findByKey && src[i][findByKey] == find || !findByKey && src[i] === find) {\n          // do not use === here, test fails\n          return i;\n        }\n        i++;\n      }\n      return -1;\n    }\n  }\n  var POINTER_INPUT_MAP = {\n    pointerdown: INPUT_START,\n    pointermove: INPUT_MOVE,\n    pointerup: INPUT_END,\n    pointercancel: INPUT_CANCEL,\n    pointerout: INPUT_CANCEL\n  }; // in IE10 the pointer types is defined as an enum\n\n  var IE10_POINTER_TYPE_ENUM = {\n    2: INPUT_TYPE_TOUCH,\n    3: INPUT_TYPE_PEN,\n    4: INPUT_TYPE_MOUSE,\n    5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n  };\n  var POINTER_ELEMENT_EVENTS = 'pointerdown';\n  var POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel'; // IE10 has prefixed support, and case-sensitive\n\n  if (win.MSPointerEvent && !win.PointerEvent) {\n    POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n    POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n  }\n  /**\n   * @private\n   * Pointer events input\n   * @constructor\n   * @extends Input\n   */\n\n  var PointerEventInput = exports.PointerEventInput = /*#__PURE__*/\n  function (_Input) {\n    _inheritsLoose(PointerEventInput, _Input);\n    function PointerEventInput() {\n      var _this;\n      var proto = PointerEventInput.prototype;\n      proto.evEl = POINTER_ELEMENT_EVENTS;\n      proto.evWin = POINTER_WINDOW_EVENTS;\n      _this = _Input.apply(this, arguments) || this;\n      _this.store = _this.manager.session.pointerEvents = [];\n      return _this;\n    }\n    /**\n     * @private\n     * handle mouse events\n     * @param {Object} ev\n     */\n\n    var _proto = PointerEventInput.prototype;\n    _proto.handler = function handler(ev) {\n      var store = this.store;\n      var removePointer = false;\n      var eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n      var eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n      var pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n      var isTouch = pointerType === INPUT_TYPE_TOUCH; // get index of the event in the store\n\n      var storeIndex = inArray(store, ev.pointerId, 'pointerId'); // start and mouse must be down\n\n      if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n        if (storeIndex < 0) {\n          store.push(ev);\n          storeIndex = store.length - 1;\n        }\n      } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n        removePointer = true;\n      } // it not found, so the pointer hasn't been down (so it's probably a hover)\n\n      if (storeIndex < 0) {\n        return;\n      } // update the event in the store\n\n      store[storeIndex] = ev;\n      this.callback(this.manager, eventType, {\n        pointers: store,\n        changedPointers: [ev],\n        pointerType: pointerType,\n        srcEvent: ev\n      });\n      if (removePointer) {\n        // remove from the store\n        store.splice(storeIndex, 1);\n      }\n    };\n    return PointerEventInput;\n  }(Input);\n\n  /**\n   * @private\n   * convert array-like objects to real arrays\n   * @param {Object} obj\n   * @returns {Array}\n   */\n  function toArray(obj) {\n    return Array.prototype.slice.call(obj, 0);\n  }\n\n  /**\n   * @private\n   * unique array with objects based on a key (like 'id') or just by the array's value\n   * @param {Array} src [{id:1},{id:2},{id:1}]\n   * @param {String} [key]\n   * @param {Boolean} [sort=False]\n   * @returns {Array} [{id:1},{id:2}]\n   */\n\n  function uniqueArray(src, key, sort) {\n    var results = [];\n    var values = [];\n    var i = 0;\n    while (i < src.length) {\n      var val = key ? src[i][key] : src[i];\n      if (inArray(values, val) < 0) {\n        results.push(src[i]);\n      }\n      values[i] = val;\n      i++;\n    }\n    if (sort) {\n      if (!key) {\n        results = results.sort();\n      } else {\n        results = results.sort(function (a, b) {\n          return a[key] > b[key];\n        });\n      }\n    }\n    return results;\n  }\n  var TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n  };\n  var TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n  /**\n   * @private\n   * Multi-user touch events input\n   * @constructor\n   * @extends Input\n   */\n\n  var TouchInput = exports.TouchInput = /*#__PURE__*/\n  function (_Input) {\n    _inheritsLoose(TouchInput, _Input);\n    function TouchInput() {\n      var _this;\n      TouchInput.prototype.evTarget = TOUCH_TARGET_EVENTS;\n      _this = _Input.apply(this, arguments) || this;\n      _this.targetIds = {}; // this.evTarget = TOUCH_TARGET_EVENTS;\n\n      return _this;\n    }\n    var _proto = TouchInput.prototype;\n    _proto.handler = function handler(ev) {\n      var type = TOUCH_INPUT_MAP[ev.type];\n      var touches = getTouches.call(this, ev, type);\n      if (!touches) {\n        return;\n      }\n      this.callback(this.manager, type, {\n        pointers: touches[0],\n        changedPointers: touches[1],\n        pointerType: INPUT_TYPE_TOUCH,\n        srcEvent: ev\n      });\n    };\n    return TouchInput;\n  }(Input);\n  function getTouches(ev, type) {\n    var allTouches = toArray(ev.touches);\n    var targetIds = this.targetIds; // when there is only one touch, the process can be simplified\n\n    if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n      targetIds[allTouches[0].identifier] = true;\n      return [allTouches, allTouches];\n    }\n    var i;\n    var targetTouches;\n    var changedTouches = toArray(ev.changedTouches);\n    var changedTargetTouches = [];\n    var target = this.target; // get target touches from touches\n\n    targetTouches = allTouches.filter(function (touch) {\n      return hasParent(touch.target, target);\n    }); // collect touches\n\n    if (type === INPUT_START) {\n      i = 0;\n      while (i < targetTouches.length) {\n        targetIds[targetTouches[i].identifier] = true;\n        i++;\n      }\n    } // filter changed touches to only contain touches that exist in the collected target ids\n\n    i = 0;\n    while (i < changedTouches.length) {\n      if (targetIds[changedTouches[i].identifier]) {\n        changedTargetTouches.push(changedTouches[i]);\n      } // cleanup removed touches\n\n      if (type & (INPUT_END | INPUT_CANCEL)) {\n        delete targetIds[changedTouches[i].identifier];\n      }\n      i++;\n    }\n    if (!changedTargetTouches.length) {\n      return;\n    }\n    return [\n    // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n    uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true), changedTargetTouches];\n  }\n  var MOUSE_INPUT_MAP = {\n    mousedown: INPUT_START,\n    mousemove: INPUT_MOVE,\n    mouseup: INPUT_END\n  };\n  var MOUSE_ELEMENT_EVENTS = 'mousedown';\n  var MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n  /**\n   * @private\n   * Mouse events input\n   * @constructor\n   * @extends Input\n   */\n\n  var MouseInput = exports.MouseInput = /*#__PURE__*/\n  function (_Input) {\n    _inheritsLoose(MouseInput, _Input);\n    function MouseInput() {\n      var _this;\n      var proto = MouseInput.prototype;\n      proto.evEl = MOUSE_ELEMENT_EVENTS;\n      proto.evWin = MOUSE_WINDOW_EVENTS;\n      _this = _Input.apply(this, arguments) || this;\n      _this.pressed = false; // mousedown state\n\n      return _this;\n    }\n    /**\n     * @private\n     * handle mouse events\n     * @param {Object} ev\n     */\n\n    var _proto = MouseInput.prototype;\n    _proto.handler = function handler(ev) {\n      var eventType = MOUSE_INPUT_MAP[ev.type]; // on start we want to have the left mouse button down\n\n      if (eventType & INPUT_START && ev.button === 0) {\n        this.pressed = true;\n      }\n      if (eventType & INPUT_MOVE && ev.which !== 1) {\n        eventType = INPUT_END;\n      } // mouse must be down\n\n      if (!this.pressed) {\n        return;\n      }\n      if (eventType & INPUT_END) {\n        this.pressed = false;\n      }\n      this.callback(this.manager, eventType, {\n        pointers: [ev],\n        changedPointers: [ev],\n        pointerType: INPUT_TYPE_MOUSE,\n        srcEvent: ev\n      });\n    };\n    return MouseInput;\n  }(Input);\n\n  /**\n   * @private\n   * Combined touch and mouse input\n   *\n   * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n   * This because touch devices also emit mouse events while doing a touch.\n   *\n   * @constructor\n   * @extends Input\n   */\n\n  var DEDUP_TIMEOUT = 2500;\n  var DEDUP_DISTANCE = 25;\n  function setLastTouch(eventData) {\n    var _eventData$changedPoi = eventData.changedPointers,\n      touch = _eventData$changedPoi[0];\n    if (touch.identifier === this.primaryTouch) {\n      var lastTouch = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n      var lts = this.lastTouches;\n      this.lastTouches.push(lastTouch);\n      var removeLastTouch = function removeLastTouch() {\n        var i = lts.indexOf(lastTouch);\n        if (i > -1) {\n          lts.splice(i, 1);\n        }\n      };\n      setTimeout(removeLastTouch, DEDUP_TIMEOUT);\n    }\n  }\n  function recordTouches(eventType, eventData) {\n    if (eventType & INPUT_START) {\n      this.primaryTouch = eventData.changedPointers[0].identifier;\n      setLastTouch.call(this, eventData);\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n      setLastTouch.call(this, eventData);\n    }\n  }\n  function isSyntheticEvent(eventData) {\n    var x = eventData.srcEvent.clientX;\n    var y = eventData.srcEvent.clientY;\n    for (var i = 0; i < this.lastTouches.length; i++) {\n      var t = this.lastTouches[i];\n      var dx = Math.abs(x - t.x);\n      var dy = Math.abs(y - t.y);\n      if (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n        return true;\n      }\n    }\n    return false;\n  }\n  var TouchMouseInput = exports.TouchMouseInput = /*#__PURE__*/\n  function () {\n    var TouchMouseInput = /*#__PURE__*/\n    function (_Input) {\n      _inheritsLoose(TouchMouseInput, _Input);\n      function TouchMouseInput(_manager, callback) {\n        var _this;\n        _this = _Input.call(this, _manager, callback) || this;\n        _this.handler = function (manager, inputEvent, inputData) {\n          var isTouch = inputData.pointerType === INPUT_TYPE_TOUCH;\n          var isMouse = inputData.pointerType === INPUT_TYPE_MOUSE;\n          if (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n            return;\n          } // when we're in a touch event, record touches to  de-dupe synthetic mouse event\n\n          if (isTouch) {\n            recordTouches.call(_assertThisInitialized(_assertThisInitialized(_this)), inputEvent, inputData);\n          } else if (isMouse && isSyntheticEvent.call(_assertThisInitialized(_assertThisInitialized(_this)), inputData)) {\n            return;\n          }\n          _this.callback(manager, inputEvent, inputData);\n        };\n        _this.touch = new TouchInput(_this.manager, _this.handler);\n        _this.mouse = new MouseInput(_this.manager, _this.handler);\n        _this.primaryTouch = null;\n        _this.lastTouches = [];\n        return _this;\n      }\n      /**\n       * @private\n       * handle mouse and touch events\n       * @param {Hammer} manager\n       * @param {String} inputEvent\n       * @param {Object} inputData\n       */\n\n      var _proto = TouchMouseInput.prototype;\n\n      /**\n       * @private\n       * remove the event listeners\n       */\n      _proto.destroy = function destroy() {\n        this.touch.destroy();\n        this.mouse.destroy();\n      };\n      return TouchMouseInput;\n    }(Input);\n    return TouchMouseInput;\n  }();\n\n  /**\n   * @private\n   * create new input type manager\n   * called by the Manager constructor\n   * @param {Hammer} manager\n   * @returns {Input}\n   */\n\n  function createInputInstance(manager) {\n    var Type; // let inputClass = manager.options.inputClass;\n\n    var inputClass = manager.options.inputClass;\n    if (inputClass) {\n      Type = inputClass;\n    } else if (SUPPORT_POINTER_EVENTS) {\n      Type = PointerEventInput;\n    } else if (SUPPORT_ONLY_TOUCH) {\n      Type = TouchInput;\n    } else if (!SUPPORT_TOUCH) {\n      Type = MouseInput;\n    } else {\n      Type = TouchMouseInput;\n    }\n    return new Type(manager, inputHandler);\n  }\n\n  /**\n   * @private\n   * if the argument is an array, we want to execute the fn on each entry\n   * if it aint an array we don't want to do a thing.\n   * this is used by all the methods that accept a single and array argument.\n   * @param {*|Array} arg\n   * @param {String} fn\n   * @param {Object} [context]\n   * @returns {Boolean}\n   */\n\n  function invokeArrayArg(arg, fn, context) {\n    if (Array.isArray(arg)) {\n      each(arg, context[fn], context);\n      return true;\n    }\n    return false;\n  }\n  var STATE_POSSIBLE = exports.STATE_POSSIBLE = 1;\n  var STATE_BEGAN = exports.STATE_BEGAN = 2;\n  var STATE_CHANGED = exports.STATE_CHANGED = 4;\n  var STATE_ENDED = exports.STATE_ENDED = 8;\n  var STATE_RECOGNIZED = exports.STATE_RECOGNIZED = STATE_ENDED;\n  var STATE_CANCELLED = exports.STATE_CANCELLED = 16;\n  var STATE_FAILED = exports.STATE_FAILED = 32;\n\n  /**\n   * @private\n   * get a unique id\n   * @returns {number} uniqueId\n   */\n  var _uniqueId = 1;\n  function uniqueId() {\n    return _uniqueId++;\n  }\n\n  /**\n   * @private\n   * get a recognizer by name if it is bound to a manager\n   * @param {Recognizer|String} otherRecognizer\n   * @param {Recognizer} recognizer\n   * @returns {Recognizer}\n   */\n  function getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n    var manager = recognizer.manager;\n    if (manager) {\n      return manager.get(otherRecognizer);\n    }\n    return otherRecognizer;\n  }\n\n  /**\n   * @private\n   * get a usable string, used as event postfix\n   * @param {constant} state\n   * @returns {String} state\n   */\n\n  function stateStr(state) {\n    if (state & STATE_CANCELLED) {\n      return 'cancel';\n    } else if (state & STATE_ENDED) {\n      return 'end';\n    } else if (state & STATE_CHANGED) {\n      return 'move';\n    } else if (state & STATE_BEGAN) {\n      return 'start';\n    }\n    return '';\n  }\n\n  /**\n   * @private\n   * Recognizer flow explained; *\n   * All recognizers have the initial state of POSSIBLE when a input session starts.\n   * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n   * Example session for mouse-input: mousedown -> mousemove -> mouseup\n   *\n   * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n   * which determines with state it should be.\n   *\n   * If the recognizer has the state FAILED, CANCELLED or RECOGNIZED (equals ENDED), it is reset to\n   * POSSIBLE to give it another change on the next cycle.\n   *\n   *               Possible\n   *                  |\n   *            +-----+---------------+\n   *            |                     |\n   *      +-----+-----+               |\n   *      |           |               |\n   *   Failed      Cancelled          |\n   *                          +-------+------+\n   *                          |              |\n   *                      Recognized       Began\n   *                                         |\n   *                                      Changed\n   *                                         |\n   *                                  Ended/Recognized\n   */\n\n  /**\n   * @private\n   * Recognizer\n   * Every recognizer needs to extend from this class.\n   * @constructor\n   * @param {Object} options\n   */\n\n  var Recognizer = exports.Recognizer = /*#__PURE__*/\n  function () {\n    function Recognizer(options) {\n      if (options === void 0) {\n        options = {};\n      }\n      this.options = _extends({\n        enable: true\n      }, options);\n      this.id = uniqueId();\n      this.manager = null; // default is enable true\n\n      this.state = STATE_POSSIBLE;\n      this.simultaneous = {};\n      this.requireFail = [];\n    }\n    /**\n     * @private\n     * set options\n     * @param {Object} options\n     * @return {Recognizer}\n     */\n\n    var _proto = Recognizer.prototype;\n    _proto.set = function set(options) {\n      assign$1(this.options, options); // also update the touchAction, in case something changed about the directions/enabled state\n\n      this.manager && this.manager.touchAction.update();\n      return this;\n    };\n    /**\n     * @private\n     * recognize simultaneous with an other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n\n    _proto.recognizeWith = function recognizeWith(otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n        return this;\n      }\n      var simultaneous = this.simultaneous;\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      if (!simultaneous[otherRecognizer.id]) {\n        simultaneous[otherRecognizer.id] = otherRecognizer;\n        otherRecognizer.recognizeWith(this);\n      }\n      return this;\n    };\n    /**\n     * @private\n     * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n\n    _proto.dropRecognizeWith = function dropRecognizeWith(otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n        return this;\n      }\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      delete this.simultaneous[otherRecognizer.id];\n      return this;\n    };\n    /**\n     * @private\n     * recognizer can only run when an other is failing\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n\n    _proto.requireFailure = function requireFailure(otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n        return this;\n      }\n      var requireFail = this.requireFail;\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      if (inArray(requireFail, otherRecognizer) === -1) {\n        requireFail.push(otherRecognizer);\n        otherRecognizer.requireFailure(this);\n      }\n      return this;\n    };\n    /**\n     * @private\n     * drop the requireFailure link. it does not remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n\n    _proto.dropRequireFailure = function dropRequireFailure(otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n        return this;\n      }\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      var index = inArray(this.requireFail, otherRecognizer);\n      if (index > -1) {\n        this.requireFail.splice(index, 1);\n      }\n      return this;\n    };\n    /**\n     * @private\n     * has require failures boolean\n     * @returns {boolean}\n     */\n\n    _proto.hasRequireFailures = function hasRequireFailures() {\n      return this.requireFail.length > 0;\n    };\n    /**\n     * @private\n     * if the recognizer can recognize simultaneous with an other recognizer\n     * @param {Recognizer} otherRecognizer\n     * @returns {Boolean}\n     */\n\n    _proto.canRecognizeWith = function canRecognizeWith(otherRecognizer) {\n      return !!this.simultaneous[otherRecognizer.id];\n    };\n    /**\n     * @private\n     * You should use `tryEmit` instead of `emit` directly to check\n     * that all the needed recognizers has failed before emitting.\n     * @param {Object} input\n     */\n\n    _proto.emit = function emit(input) {\n      var self = this;\n      var state = this.state;\n      function emit(event) {\n        self.manager.emit(event, input);\n      } // 'panstart' and 'panmove'\n\n      if (state < STATE_ENDED) {\n        emit(self.options.event + stateStr(state));\n      }\n      emit(self.options.event); // simple 'eventName' events\n\n      if (input.additionalEvent) {\n        // additional event(panleft, panright, pinchin, pinchout...)\n        emit(input.additionalEvent);\n      } // panend and pancancel\n\n      if (state >= STATE_ENDED) {\n        emit(self.options.event + stateStr(state));\n      }\n    };\n    /**\n     * @private\n     * Check that all the require failure recognizers has failed,\n     * if true, it emits a gesture event,\n     * otherwise, setup the state to FAILED.\n     * @param {Object} input\n     */\n\n    _proto.tryEmit = function tryEmit(input) {\n      if (this.canEmit()) {\n        return this.emit(input);\n      } // it's failing anyway\n\n      this.state = STATE_FAILED;\n    };\n    /**\n     * @private\n     * can we emit?\n     * @returns {boolean}\n     */\n\n    _proto.canEmit = function canEmit() {\n      var i = 0;\n      while (i < this.requireFail.length) {\n        if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n          return false;\n        }\n        i++;\n      }\n      return true;\n    };\n    /**\n     * @private\n     * update the recognizer\n     * @param {Object} inputData\n     */\n\n    _proto.recognize = function recognize(inputData) {\n      // make a new copy of the inputData\n      // so we can change the inputData without messing up the other recognizers\n      var inputDataClone = assign$1({}, inputData); // is is enabled and allow recognizing?\n\n      if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n        this.reset();\n        this.state = STATE_FAILED;\n        return;\n      } // reset when we've reached the end\n\n      if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n        this.state = STATE_POSSIBLE;\n      }\n      this.state = this.process(inputDataClone); // the recognizer has recognized a gesture\n      // so trigger an event\n\n      if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n        this.tryEmit(inputDataClone);\n      }\n    };\n    /**\n     * @private\n     * return the state of the recognizer\n     * the actual recognizing happens in this method\n     * @virtual\n     * @param {Object} inputData\n     * @returns {constant} STATE\n     */\n\n    /* jshint ignore:start */\n\n    _proto.process = function process(inputData) {};\n    /* jshint ignore:end */\n\n    /**\n     * @private\n     * return the preferred touch-action\n     * @virtual\n     * @returns {Array}\n     */\n\n    _proto.getTouchAction = function getTouchAction() {};\n    /**\n     * @private\n     * called when the gesture isn't allowed to recognize\n     * like when another is being recognized or it is disabled\n     * @virtual\n     */\n\n    _proto.reset = function reset() {};\n    return Recognizer;\n  }();\n\n  /**\n   * @private\n   * A tap is recognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n   * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n   * a single tap.\n   *\n   * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n   * multi-taps being recognized.\n   * @constructor\n   * @extends Recognizer\n   */\n\n  var TapRecognizer = exports.Tap = /*#__PURE__*/\n  function (_Recognizer) {\n    _inheritsLoose(TapRecognizer, _Recognizer);\n    function TapRecognizer(options) {\n      var _this;\n      if (options === void 0) {\n        options = {};\n      }\n      _this = _Recognizer.call(this, _extends({\n        event: 'tap',\n        pointers: 1,\n        taps: 1,\n        interval: 300,\n        // max time between the multi-tap taps\n        time: 250,\n        // max time of the pointer to be down (like finger on the screen)\n        threshold: 9,\n        // a minimal movement is ok, but keep it low\n        posThreshold: 10\n      }, options)) || this; // previous time and center,\n      // used for tap counting\n\n      _this.pTime = false;\n      _this.pCenter = false;\n      _this._timer = null;\n      _this._input = null;\n      _this.count = 0;\n      return _this;\n    }\n    var _proto = TapRecognizer.prototype;\n    _proto.getTouchAction = function getTouchAction() {\n      return [TOUCH_ACTION_MANIPULATION];\n    };\n    _proto.process = function process(input) {\n      var _this2 = this;\n      var options = this.options;\n      var validPointers = input.pointers.length === options.pointers;\n      var validMovement = input.distance < options.threshold;\n      var validTouchTime = input.deltaTime < options.time;\n      this.reset();\n      if (input.eventType & INPUT_START && this.count === 0) {\n        return this.failTimeout();\n      } // we only allow little movement\n      // and we've reached an end event, so a tap is possible\n\n      if (validMovement && validTouchTime && validPointers) {\n        if (input.eventType !== INPUT_END) {\n          return this.failTimeout();\n        }\n        var validInterval = this.pTime ? input.timeStamp - this.pTime < options.interval : true;\n        var validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n        this.pTime = input.timeStamp;\n        this.pCenter = input.center;\n        if (!validMultiTap || !validInterval) {\n          this.count = 1;\n        } else {\n          this.count += 1;\n        }\n        this._input = input; // if tap count matches we have recognized it,\n        // else it has began recognizing...\n\n        var tapCount = this.count % options.taps;\n        if (tapCount === 0) {\n          // no failing requirements, immediately trigger the tap event\n          // or wait as long as the multitap interval to trigger\n          if (!this.hasRequireFailures()) {\n            return STATE_RECOGNIZED;\n          } else {\n            this._timer = setTimeout(function () {\n              _this2.state = STATE_RECOGNIZED;\n              _this2.tryEmit();\n            }, options.interval);\n            return STATE_BEGAN;\n          }\n        }\n      }\n      return STATE_FAILED;\n    };\n    _proto.failTimeout = function failTimeout() {\n      var _this3 = this;\n      this._timer = setTimeout(function () {\n        _this3.state = STATE_FAILED;\n      }, this.options.interval);\n      return STATE_FAILED;\n    };\n    _proto.reset = function reset() {\n      clearTimeout(this._timer);\n    };\n    _proto.emit = function emit() {\n      if (this.state === STATE_RECOGNIZED) {\n        this._input.tapCount = this.count;\n        this.manager.emit(this.options.event, this._input);\n      }\n    };\n    return TapRecognizer;\n  }(Recognizer);\n\n  /**\n   * @private\n   * This recognizer is just used as a base for the simple attribute recognizers.\n   * @constructor\n   * @extends Recognizer\n   */\n\n  var AttrRecognizer = exports.AttrRecognizer = /*#__PURE__*/\n  function (_Recognizer) {\n    _inheritsLoose(AttrRecognizer, _Recognizer);\n    function AttrRecognizer(options) {\n      if (options === void 0) {\n        options = {};\n      }\n      return _Recognizer.call(this, _extends({\n        pointers: 1\n      }, options)) || this;\n    }\n    /**\n     * @private\n     * Used to check if it the recognizer receives valid input, like input.distance > 10.\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {Boolean} recognized\n     */\n\n    var _proto = AttrRecognizer.prototype;\n    _proto.attrTest = function attrTest(input) {\n      var optionPointers = this.options.pointers;\n      return optionPointers === 0 || input.pointers.length === optionPointers;\n    };\n    /**\n     * @private\n     * Process the input and return the state for the recognizer\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {*} State\n     */\n\n    _proto.process = function process(input) {\n      var state = this.state;\n      var eventType = input.eventType;\n      var isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n      var isValid = this.attrTest(input); // on cancel input and we've recognized before, return STATE_CANCELLED\n\n      if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n        return state | STATE_CANCELLED;\n      } else if (isRecognized || isValid) {\n        if (eventType & INPUT_END) {\n          return state | STATE_ENDED;\n        } else if (!(state & STATE_BEGAN)) {\n          return STATE_BEGAN;\n        }\n        return state | STATE_CHANGED;\n      }\n      return STATE_FAILED;\n    };\n    return AttrRecognizer;\n  }(Recognizer);\n\n  /**\n   * @private\n   * direction cons to string\n   * @param {constant} direction\n   * @returns {String}\n   */\n\n  function directionStr(direction) {\n    if (direction === DIRECTION_DOWN) {\n      return 'down';\n    } else if (direction === DIRECTION_UP) {\n      return 'up';\n    } else if (direction === DIRECTION_LEFT) {\n      return 'left';\n    } else if (direction === DIRECTION_RIGHT) {\n      return 'right';\n    }\n    return '';\n  }\n\n  /**\n   * @private\n   * Pan\n   * Recognized when the pointer is down and moved in the allowed direction.\n   * @constructor\n   * @extends AttrRecognizer\n   */\n\n  var PanRecognizer = exports.Pan = /*#__PURE__*/\n  function (_AttrRecognizer) {\n    _inheritsLoose(PanRecognizer, _AttrRecognizer);\n    function PanRecognizer(options) {\n      var _this;\n      if (options === void 0) {\n        options = {};\n      }\n      _this = _AttrRecognizer.call(this, _extends({\n        event: 'pan',\n        threshold: 10,\n        pointers: 1,\n        direction: DIRECTION_ALL\n      }, options)) || this;\n      _this.pX = null;\n      _this.pY = null;\n      return _this;\n    }\n    var _proto = PanRecognizer.prototype;\n    _proto.getTouchAction = function getTouchAction() {\n      var direction = this.options.direction;\n      var actions = [];\n      if (direction & DIRECTION_HORIZONTAL) {\n        actions.push(TOUCH_ACTION_PAN_Y);\n      }\n      if (direction & DIRECTION_VERTICAL) {\n        actions.push(TOUCH_ACTION_PAN_X);\n      }\n      return actions;\n    };\n    _proto.directionTest = function directionTest(input) {\n      var options = this.options;\n      var hasMoved = true;\n      var distance = input.distance;\n      var direction = input.direction;\n      var x = input.deltaX;\n      var y = input.deltaY; // lock to axis?\n\n      if (!(direction & options.direction)) {\n        if (options.direction & DIRECTION_HORIZONTAL) {\n          direction = x === 0 ? DIRECTION_NONE : x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n          hasMoved = x !== this.pX;\n          distance = Math.abs(input.deltaX);\n        } else {\n          direction = y === 0 ? DIRECTION_NONE : y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n          hasMoved = y !== this.pY;\n          distance = Math.abs(input.deltaY);\n        }\n      }\n      input.direction = direction;\n      return hasMoved && distance > options.threshold && direction & options.direction;\n    };\n    _proto.attrTest = function attrTest(input) {\n      return AttrRecognizer.prototype.attrTest.call(this, input) && (\n      // replace with a super call\n      this.state & STATE_BEGAN || !(this.state & STATE_BEGAN) && this.directionTest(input));\n    };\n    _proto.emit = function emit(input) {\n      this.pX = input.deltaX;\n      this.pY = input.deltaY;\n      var direction = directionStr(input.direction);\n      if (direction) {\n        input.additionalEvent = this.options.event + direction;\n      }\n      _AttrRecognizer.prototype.emit.call(this, input);\n    };\n    return PanRecognizer;\n  }(AttrRecognizer);\n\n  /**\n   * @private\n   * Swipe\n   * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n   * @constructor\n   * @extends AttrRecognizer\n   */\n\n  var SwipeRecognizer = exports.Swipe = /*#__PURE__*/\n  function (_AttrRecognizer) {\n    _inheritsLoose(SwipeRecognizer, _AttrRecognizer);\n    function SwipeRecognizer(options) {\n      if (options === void 0) {\n        options = {};\n      }\n      return _AttrRecognizer.call(this, _extends({\n        event: 'swipe',\n        threshold: 10,\n        velocity: 0.3,\n        direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n        pointers: 1\n      }, options)) || this;\n    }\n    var _proto = SwipeRecognizer.prototype;\n    _proto.getTouchAction = function getTouchAction() {\n      return PanRecognizer.prototype.getTouchAction.call(this);\n    };\n    _proto.attrTest = function attrTest(input) {\n      var direction = this.options.direction;\n      var velocity;\n      if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n        velocity = input.overallVelocity;\n      } else if (direction & DIRECTION_HORIZONTAL) {\n        velocity = input.overallVelocityX;\n      } else if (direction & DIRECTION_VERTICAL) {\n        velocity = input.overallVelocityY;\n      }\n      return _AttrRecognizer.prototype.attrTest.call(this, input) && direction & input.offsetDirection && input.distance > this.options.threshold && input.maxPointers === this.options.pointers && abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n    };\n    _proto.emit = function emit(input) {\n      var direction = directionStr(input.offsetDirection);\n      if (direction) {\n        this.manager.emit(this.options.event + direction, input);\n      }\n      this.manager.emit(this.options.event, input);\n    };\n    return SwipeRecognizer;\n  }(AttrRecognizer);\n\n  /**\n   * @private\n   * Pinch\n   * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n   * @constructor\n   * @extends AttrRecognizer\n   */\n\n  var PinchRecognizer = exports.Pinch = /*#__PURE__*/\n  function (_AttrRecognizer) {\n    _inheritsLoose(PinchRecognizer, _AttrRecognizer);\n    function PinchRecognizer(options) {\n      if (options === void 0) {\n        options = {};\n      }\n      return _AttrRecognizer.call(this, _extends({\n        event: 'pinch',\n        threshold: 0,\n        pointers: 2\n      }, options)) || this;\n    }\n    var _proto = PinchRecognizer.prototype;\n    _proto.getTouchAction = function getTouchAction() {\n      return [TOUCH_ACTION_NONE];\n    };\n    _proto.attrTest = function attrTest(input) {\n      return _AttrRecognizer.prototype.attrTest.call(this, input) && (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n    };\n    _proto.emit = function emit(input) {\n      if (input.scale !== 1) {\n        var inOut = input.scale < 1 ? 'in' : 'out';\n        input.additionalEvent = this.options.event + inOut;\n      }\n      _AttrRecognizer.prototype.emit.call(this, input);\n    };\n    return PinchRecognizer;\n  }(AttrRecognizer);\n\n  /**\n   * @private\n   * Rotate\n   * Recognized when two or more pointer are moving in a circular motion.\n   * @constructor\n   * @extends AttrRecognizer\n   */\n\n  var RotateRecognizer = exports.Rotate = /*#__PURE__*/\n  function (_AttrRecognizer) {\n    _inheritsLoose(RotateRecognizer, _AttrRecognizer);\n    function RotateRecognizer(options) {\n      if (options === void 0) {\n        options = {};\n      }\n      return _AttrRecognizer.call(this, _extends({\n        event: 'rotate',\n        threshold: 0,\n        pointers: 2\n      }, options)) || this;\n    }\n    var _proto = RotateRecognizer.prototype;\n    _proto.getTouchAction = function getTouchAction() {\n      return [TOUCH_ACTION_NONE];\n    };\n    _proto.attrTest = function attrTest(input) {\n      return _AttrRecognizer.prototype.attrTest.call(this, input) && (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n    };\n    return RotateRecognizer;\n  }(AttrRecognizer);\n\n  /**\n   * @private\n   * Press\n   * Recognized when the pointer is down for x ms without any movement.\n   * @constructor\n   * @extends Recognizer\n   */\n\n  var PressRecognizer = exports.Press = /*#__PURE__*/\n  function (_Recognizer) {\n    _inheritsLoose(PressRecognizer, _Recognizer);\n    function PressRecognizer(options) {\n      var _this;\n      if (options === void 0) {\n        options = {};\n      }\n      _this = _Recognizer.call(this, _extends({\n        event: 'press',\n        pointers: 1,\n        time: 251,\n        // minimal time of the pointer to be pressed\n        threshold: 9\n      }, options)) || this;\n      _this._timer = null;\n      _this._input = null;\n      return _this;\n    }\n    var _proto = PressRecognizer.prototype;\n    _proto.getTouchAction = function getTouchAction() {\n      return [TOUCH_ACTION_AUTO];\n    };\n    _proto.process = function process(input) {\n      var _this2 = this;\n      var options = this.options;\n      var validPointers = input.pointers.length === options.pointers;\n      var validMovement = input.distance < options.threshold;\n      var validTime = input.deltaTime > options.time;\n      this._input = input; // we only allow little movement\n      // and we've reached an end event, so a tap is possible\n\n      if (!validMovement || !validPointers || input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime) {\n        this.reset();\n      } else if (input.eventType & INPUT_START) {\n        this.reset();\n        this._timer = setTimeout(function () {\n          _this2.state = STATE_RECOGNIZED;\n          _this2.tryEmit();\n        }, options.time);\n      } else if (input.eventType & INPUT_END) {\n        return STATE_RECOGNIZED;\n      }\n      return STATE_FAILED;\n    };\n    _proto.reset = function reset() {\n      clearTimeout(this._timer);\n    };\n    _proto.emit = function emit(input) {\n      if (this.state !== STATE_RECOGNIZED) {\n        return;\n      }\n      if (input && input.eventType & INPUT_END) {\n        this.manager.emit(this.options.event + \"up\", input);\n      } else {\n        this._input.timeStamp = now();\n        this.manager.emit(this.options.event, this._input);\n      }\n    };\n    return PressRecognizer;\n  }(Recognizer);\n  var defaults = {\n    /**\n     * @private\n     * set if DOM events are being triggered.\n     * But this is slower and unused by simple implementations, so disabled by default.\n     * @type {Boolean}\n     * @default false\n     */\n    domEvents: false,\n    /**\n     * @private\n     * The value for the touchAction property/fallback.\n     * When set to `compute` it will magically set the correct value based on the added recognizers.\n     * @type {String}\n     * @default compute\n     */\n    touchAction: TOUCH_ACTION_COMPUTE,\n    /**\n     * @private\n     * @type {Boolean}\n     * @default true\n     */\n    enable: true,\n    /**\n     * @private\n     * EXPERIMENTAL FEATURE -- can be removed/changed\n     * Change the parent input target element.\n     * If Null, then it is being set the to main element.\n     * @type {Null|EventTarget}\n     * @default null\n     */\n    inputTarget: null,\n    /**\n     * @private\n     * force an input class\n     * @type {Null|Function}\n     * @default null\n     */\n    inputClass: null,\n    /**\n     * @private\n     * Some CSS properties can be used to improve the working of Hammer.\n     * Add them to this method and they will be set when creating a new Manager.\n     * @namespace\n     */\n    cssProps: {\n      /**\n       * @private\n       * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n       * @type {String}\n       * @default 'none'\n       */\n      userSelect: \"none\",\n      /**\n       * @private\n       * Disable the Windows Phone grippers when pressing an element.\n       * @type {String}\n       * @default 'none'\n       */\n      touchSelect: \"none\",\n      /**\n       * @private\n       * Disables the default callout shown when you touch and hold a touch target.\n       * On iOS, when you touch and hold a touch target such as a link, Safari displays\n       * a callout containing information about the link. This property allows you to disable that callout.\n       * @type {String}\n       * @default 'none'\n       */\n      touchCallout: \"none\",\n      /**\n       * @private\n       * Specifies whether zooming is enabled. Used by IE10>\n       * @type {String}\n       * @default 'none'\n       */\n      contentZooming: \"none\",\n      /**\n       * @private\n       * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n       * @type {String}\n       * @default 'none'\n       */\n      userDrag: \"none\",\n      /**\n       * @private\n       * Overrides the highlight color shown when the user taps a link or a JavaScript\n       * clickable element in iOS. This property obeys the alpha value, if specified.\n       * @type {String}\n       * @default 'rgba(0,0,0,0)'\n       */\n      tapHighlightColor: \"rgba(0,0,0,0)\"\n    }\n  };\n  /**\n   * @private\n   * Default recognizer setup when calling `Hammer()`\n   * When creating a new Manager these will be skipped.\n   * This is separated with other defaults because of tree-shaking.\n   * @type {Array}\n   */\n\n  var preset = [[RotateRecognizer, {\n    enable: false\n  }], [PinchRecognizer, {\n    enable: false\n  }, ['rotate']], [SwipeRecognizer, {\n    direction: DIRECTION_HORIZONTAL\n  }], [PanRecognizer, {\n    direction: DIRECTION_HORIZONTAL\n  }, ['swipe']], [TapRecognizer], [TapRecognizer, {\n    event: 'doubletap',\n    taps: 2\n  }, ['tap']], [PressRecognizer]];\n  var STOP = 1;\n  var FORCED_STOP = 2;\n  /**\n   * @private\n   * add/remove the css properties as defined in manager.options.cssProps\n   * @param {Manager} manager\n   * @param {Boolean} add\n   */\n\n  function toggleCssProps(manager, add) {\n    var element = manager.element;\n    if (!element.style) {\n      return;\n    }\n    var prop;\n    each(manager.options.cssProps, function (value, name) {\n      prop = prefixed(element.style, name);\n      if (add) {\n        manager.oldCssProps[prop] = element.style[prop];\n        element.style[prop] = value;\n      } else {\n        element.style[prop] = manager.oldCssProps[prop] || \"\";\n      }\n    });\n    if (!add) {\n      manager.oldCssProps = {};\n    }\n  }\n  /**\n   * @private\n   * trigger dom event\n   * @param {String} event\n   * @param {Object} data\n   */\n\n  function triggerDomEvent(event, data) {\n    var gestureEvent = document.createEvent(\"Event\");\n    gestureEvent.initEvent(event, true, true);\n    gestureEvent.gesture = data;\n    data.target.dispatchEvent(gestureEvent);\n  }\n  /**\n  * @private\n   * Manager\n   * @param {HTMLElement} element\n   * @param {Object} [options]\n   * @constructor\n   */\n\n  var Manager = exports.Manager = /*#__PURE__*/\n  function () {\n    function Manager(element, options) {\n      var _this = this;\n      this.options = assign$1({}, defaults, options || {});\n      this.options.inputTarget = this.options.inputTarget || element;\n      this.handlers = {};\n      this.session = {};\n      this.recognizers = [];\n      this.oldCssProps = {};\n      this.element = element;\n      this.input = createInputInstance(this);\n      this.touchAction = new TouchAction(this, this.options.touchAction);\n      toggleCssProps(this, true);\n      each(this.options.recognizers, function (item) {\n        var recognizer = _this.add(new item[0](item[1]));\n        item[2] && recognizer.recognizeWith(item[2]);\n        item[3] && recognizer.requireFailure(item[3]);\n      }, this);\n    }\n    /**\n     * @private\n     * set options\n     * @param {Object} options\n     * @returns {Manager}\n     */\n\n    var _proto = Manager.prototype;\n    _proto.set = function set(options) {\n      assign$1(this.options, options); // Options that need a little more setup\n\n      if (options.touchAction) {\n        this.touchAction.update();\n      }\n      if (options.inputTarget) {\n        // Clean up existing event listeners and reinitialize\n        this.input.destroy();\n        this.input.target = options.inputTarget;\n        this.input.init();\n      }\n      return this;\n    };\n    /**\n     * @private\n     * stop recognizing for this session.\n     * This session will be discarded, when a new [input]start event is fired.\n     * When forced, the recognizer cycle is stopped immediately.\n     * @param {Boolean} [force]\n     */\n\n    _proto.stop = function stop(force) {\n      this.session.stopped = force ? FORCED_STOP : STOP;\n    };\n    /**\n     * @private\n     * run the recognizers!\n     * called by the inputHandler function on every movement of the pointers (touches)\n     * it walks through all the recognizers and tries to detect the gesture that is being made\n     * @param {Object} inputData\n     */\n\n    _proto.recognize = function recognize(inputData) {\n      var session = this.session;\n      if (session.stopped) {\n        return;\n      } // run the touch-action polyfill\n\n      this.touchAction.preventDefaults(inputData);\n      var recognizer;\n      var recognizers = this.recognizers; // this holds the recognizer that is being recognized.\n      // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n      // if no recognizer is detecting a thing, it is set to `null`\n\n      var curRecognizer = session.curRecognizer; // reset when the last recognizer is recognized\n      // or when we're in a new session\n\n      if (!curRecognizer || curRecognizer && curRecognizer.state & STATE_RECOGNIZED) {\n        session.curRecognizer = null;\n        curRecognizer = null;\n      }\n      var i = 0;\n      while (i < recognizers.length) {\n        recognizer = recognizers[i]; // find out if we are allowed try to recognize the input for this one.\n        // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n        // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n        //      that is being recognized.\n        // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n        //      this can be setup with the `recognizeWith()` method on the recognizer.\n\n        if (session.stopped !== FORCED_STOP && (\n        // 1\n        !curRecognizer || recognizer === curRecognizer ||\n        // 2\n        recognizer.canRecognizeWith(curRecognizer))) {\n          // 3\n          recognizer.recognize(inputData);\n        } else {\n          recognizer.reset();\n        } // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n        // current active recognizer. but only if we don't already have an active recognizer\n\n        if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n          session.curRecognizer = recognizer;\n          curRecognizer = recognizer;\n        }\n        i++;\n      }\n    };\n    /**\n     * @private\n     * get a recognizer by its event name.\n     * @param {Recognizer|String} recognizer\n     * @returns {Recognizer|Null}\n     */\n\n    _proto.get = function get(recognizer) {\n      if (recognizer instanceof Recognizer) {\n        return recognizer;\n      }\n      var recognizers = this.recognizers;\n      for (var i = 0; i < recognizers.length; i++) {\n        if (recognizers[i].options.event === recognizer) {\n          return recognizers[i];\n        }\n      }\n      return null;\n    };\n    /**\n     * @private add a recognizer to the manager\n     * existing recognizers with the same event name will be removed\n     * @param {Recognizer} recognizer\n     * @returns {Recognizer|Manager}\n     */\n\n    _proto.add = function add(recognizer) {\n      if (invokeArrayArg(recognizer, \"add\", this)) {\n        return this;\n      } // remove existing\n\n      var existing = this.get(recognizer.options.event);\n      if (existing) {\n        this.remove(existing);\n      }\n      this.recognizers.push(recognizer);\n      recognizer.manager = this;\n      this.touchAction.update();\n      return recognizer;\n    };\n    /**\n     * @private\n     * remove a recognizer by name or instance\n     * @param {Recognizer|String} recognizer\n     * @returns {Manager}\n     */\n\n    _proto.remove = function remove(recognizer) {\n      if (invokeArrayArg(recognizer, \"remove\", this)) {\n        return this;\n      }\n      var targetRecognizer = this.get(recognizer); // let's make sure this recognizer exists\n\n      if (recognizer) {\n        var recognizers = this.recognizers;\n        var index = inArray(recognizers, targetRecognizer);\n        if (index !== -1) {\n          recognizers.splice(index, 1);\n          this.touchAction.update();\n        }\n      }\n      return this;\n    };\n    /**\n     * @private\n     * bind event\n     * @param {String} events\n     * @param {Function} handler\n     * @returns {EventEmitter} this\n     */\n\n    _proto.on = function on(events, handler) {\n      if (events === undefined || handler === undefined) {\n        return this;\n      }\n      var handlers = this.handlers;\n      each(splitStr(events), function (event) {\n        handlers[event] = handlers[event] || [];\n        handlers[event].push(handler);\n      });\n      return this;\n    };\n    /**\n     * @private unbind event, leave emit blank to remove all handlers\n     * @param {String} events\n     * @param {Function} [handler]\n     * @returns {EventEmitter} this\n     */\n\n    _proto.off = function off(events, handler) {\n      if (events === undefined) {\n        return this;\n      }\n      var handlers = this.handlers;\n      each(splitStr(events), function (event) {\n        if (!handler) {\n          delete handlers[event];\n        } else {\n          handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n        }\n      });\n      return this;\n    };\n    /**\n     * @private emit event to the listeners\n     * @param {String} event\n     * @param {Object} data\n     */\n\n    _proto.emit = function emit(event, data) {\n      // we also want to trigger dom events\n      if (this.options.domEvents) {\n        triggerDomEvent(event, data);\n      } // no handlers, so skip it all\n\n      var handlers = this.handlers[event] && this.handlers[event].slice();\n      if (!handlers || !handlers.length) {\n        return;\n      }\n      data.type = event;\n      data.preventDefault = function () {\n        data.srcEvent.preventDefault();\n      };\n      var i = 0;\n      while (i < handlers.length) {\n        handlers[i](data);\n        i++;\n      }\n    };\n    /**\n     * @private\n     * destroy the manager and unbinds all events\n     * it doesn't unbind dom events, that is the user own responsibility\n     */\n\n    _proto.destroy = function destroy() {\n      this.element && toggleCssProps(this, false);\n      this.handlers = {};\n      this.session = {};\n      this.input.destroy();\n      this.element = null;\n    };\n    return Manager;\n  }();\n  var SINGLE_TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n  };\n  var SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\n  var SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n  /**\n   * @private\n   * Touch events input\n   * @constructor\n   * @extends Input\n   */\n\n  var SingleTouchInput = exports.SingleTouchInput = /*#__PURE__*/\n  function (_Input) {\n    _inheritsLoose(SingleTouchInput, _Input);\n    function SingleTouchInput() {\n      var _this;\n      var proto = SingleTouchInput.prototype;\n      proto.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n      proto.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n      _this = _Input.apply(this, arguments) || this;\n      _this.started = false;\n      return _this;\n    }\n    var _proto = SingleTouchInput.prototype;\n    _proto.handler = function handler(ev) {\n      var type = SINGLE_TOUCH_INPUT_MAP[ev.type]; // should we handle the touch events?\n\n      if (type === INPUT_START) {\n        this.started = true;\n      }\n      if (!this.started) {\n        return;\n      }\n      var touches = normalizeSingleTouches.call(this, ev, type); // when done, reset the started state\n\n      if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n        this.started = false;\n      }\n      this.callback(this.manager, type, {\n        pointers: touches[0],\n        changedPointers: touches[1],\n        pointerType: INPUT_TYPE_TOUCH,\n        srcEvent: ev\n      });\n    };\n    return SingleTouchInput;\n  }(Input);\n  function normalizeSingleTouches(ev, type) {\n    var all = toArray(ev.touches);\n    var changed = toArray(ev.changedTouches);\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n      all = uniqueArray(all.concat(changed), 'identifier', true);\n    }\n    return [all, changed];\n  }\n\n  /**\n   * @private\n   * wrap a method with a deprecation warning and stack trace\n   * @param {Function} method\n   * @param {String} name\n   * @param {String} message\n   * @returns {Function} A new function wrapping the supplied method.\n   */\n  function deprecate(method, name, message) {\n    var deprecationMessage = \"DEPRECATED METHOD: \" + name + \"\\n\" + message + \" AT \\n\";\n    return function () {\n      var e = new Error('get-stack-trace');\n      var stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '').replace(/^\\s+at\\s+/gm, '').replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n      var log = window.console && (window.console.warn || window.console.log);\n      if (log) {\n        log.call(window.console, deprecationMessage, stack);\n      }\n      return method.apply(this, arguments);\n    };\n  }\n\n  /**\n   * @private\n   * extend object.\n   * means that properties in dest will be overwritten by the ones in src.\n   * @param {Object} dest\n   * @param {Object} src\n   * @param {Boolean} [merge=false]\n   * @returns {Object} dest\n   */\n\n  var extend = exports.extend = deprecate(function (dest, src, merge) {\n    var keys = Object.keys(src);\n    var i = 0;\n    while (i < keys.length) {\n      if (!merge || merge && dest[keys[i]] === undefined) {\n        dest[keys[i]] = src[keys[i]];\n      }\n      i++;\n    }\n    return dest;\n  }, 'extend', 'Use `assign`.');\n\n  /**\n   * @private\n   * merge the values from src in the dest.\n   * means that properties that exist in dest will not be overwritten by src\n   * @param {Object} dest\n   * @param {Object} src\n   * @returns {Object} dest\n   */\n\n  var merge = exports.merge = deprecate(function (dest, src) {\n    return extend(dest, src, true);\n  }, 'merge', 'Use `assign`.');\n\n  /**\n   * @private\n   * simple class inheritance\n   * @param {Function} child\n   * @param {Function} base\n   * @param {Object} [properties]\n   */\n\n  function inherit(child, base, properties) {\n    var baseP = base.prototype;\n    var childP;\n    childP = child.prototype = Object.create(baseP);\n    childP.constructor = child;\n    childP._super = baseP;\n    if (properties) {\n      assign$1(childP, properties);\n    }\n  }\n\n  /**\n   * @private\n   * simple function bind\n   * @param {Function} fn\n   * @param {Object} context\n   * @returns {Function}\n   */\n  function bindFn(fn, context) {\n    return function boundFn() {\n      return fn.apply(context, arguments);\n    };\n  }\n\n  /**\n   * @private\n   * Simple way to create a manager with a default set of recognizers.\n   * @param {HTMLElement} element\n   * @param {Object} [options]\n   * @constructor\n   */\n\n  var Hammer = /*#__PURE__*/\n  function () {\n    var Hammer =\n    /**\n      * @private\n      * @const {string}\n      */\n    function Hammer(element, options) {\n      if (options === void 0) {\n        options = {};\n      }\n      return new Manager(element, _extends({\n        recognizers: preset.concat()\n      }, options));\n    };\n    Hammer.VERSION = \"2.0.17-rc\";\n    Hammer.DIRECTION_ALL = DIRECTION_ALL;\n    Hammer.DIRECTION_DOWN = DIRECTION_DOWN;\n    Hammer.DIRECTION_LEFT = DIRECTION_LEFT;\n    Hammer.DIRECTION_RIGHT = DIRECTION_RIGHT;\n    Hammer.DIRECTION_UP = DIRECTION_UP;\n    Hammer.DIRECTION_HORIZONTAL = DIRECTION_HORIZONTAL;\n    Hammer.DIRECTION_VERTICAL = DIRECTION_VERTICAL;\n    Hammer.DIRECTION_NONE = DIRECTION_NONE;\n    Hammer.DIRECTION_DOWN = DIRECTION_DOWN;\n    Hammer.INPUT_START = INPUT_START;\n    Hammer.INPUT_MOVE = INPUT_MOVE;\n    Hammer.INPUT_END = INPUT_END;\n    Hammer.INPUT_CANCEL = INPUT_CANCEL;\n    Hammer.STATE_POSSIBLE = STATE_POSSIBLE;\n    Hammer.STATE_BEGAN = STATE_BEGAN;\n    Hammer.STATE_CHANGED = STATE_CHANGED;\n    Hammer.STATE_ENDED = STATE_ENDED;\n    Hammer.STATE_RECOGNIZED = STATE_RECOGNIZED;\n    Hammer.STATE_CANCELLED = STATE_CANCELLED;\n    Hammer.STATE_FAILED = STATE_FAILED;\n    Hammer.Manager = Manager;\n    Hammer.Input = Input;\n    Hammer.TouchAction = TouchAction;\n    Hammer.TouchInput = TouchInput;\n    Hammer.MouseInput = MouseInput;\n    Hammer.PointerEventInput = PointerEventInput;\n    Hammer.TouchMouseInput = TouchMouseInput;\n    Hammer.SingleTouchInput = SingleTouchInput;\n    Hammer.Recognizer = Recognizer;\n    Hammer.AttrRecognizer = AttrRecognizer;\n    Hammer.Tap = TapRecognizer;\n    Hammer.Pan = PanRecognizer;\n    Hammer.Swipe = SwipeRecognizer;\n    Hammer.Pinch = PinchRecognizer;\n    Hammer.Rotate = RotateRecognizer;\n    Hammer.Press = PressRecognizer;\n    Hammer.on = addEventListeners;\n    Hammer.off = removeEventListeners;\n    Hammer.each = each;\n    Hammer.merge = merge;\n    Hammer.extend = extend;\n    Hammer.bindFn = bindFn;\n    Hammer.assign = assign$1;\n    Hammer.inherit = inherit;\n    Hammer.bindFn = bindFn;\n    Hammer.prefixed = prefixed;\n    Hammer.toArray = toArray;\n    Hammer.inArray = inArray;\n    Hammer.uniqueArray = uniqueArray;\n    Hammer.splitStr = splitStr;\n    Hammer.boolOrFn = boolOrFn;\n    Hammer.hasParent = hasParent;\n    Hammer.addEventListeners = addEventListeners;\n    Hammer.removeEventListeners = removeEventListeners;\n    Hammer.defaults = assign$1({}, defaults, {\n      preset: preset\n    });\n    return Hammer;\n  }();\n\n  //  style loader but by script tag, not by the loader.\n\n  var defaults$1 = exports.defaults = Hammer.defaults;\n  var _default = exports.default = Hammer;\n});", "lineCount": 2653, "map": [[21, 2, 1, 0], [22, 0, 2, 0], [23, 0, 3, 0], [24, 0, 4, 0], [25, 0, 5, 0], [26, 0, 6, 0], [27, 2, 7, 0], [27, 11, 7, 9, "_extends"], [27, 19, 7, 17, "_extends"], [27, 20, 7, 17], [27, 22, 7, 20], [28, 4, 8, 2, "_extends"], [28, 12, 8, 10], [28, 15, 8, 13, "Object"], [28, 21, 8, 19], [28, 22, 8, 20, "assign"], [28, 28, 8, 26], [28, 32, 8, 30], [28, 42, 8, 40, "target"], [28, 48, 8, 46], [28, 50, 8, 48], [29, 6, 9, 4], [29, 11, 9, 9], [29, 15, 9, 13, "i"], [29, 16, 9, 14], [29, 19, 9, 17], [29, 20, 9, 18], [29, 22, 9, 20, "i"], [29, 23, 9, 21], [29, 26, 9, 24, "arguments"], [29, 35, 9, 33], [29, 36, 9, 34, "length"], [29, 42, 9, 40], [29, 44, 9, 42, "i"], [29, 45, 9, 43], [29, 47, 9, 45], [29, 49, 9, 47], [30, 8, 10, 6], [30, 12, 10, 10, "source"], [30, 18, 10, 16], [30, 21, 10, 19, "arguments"], [30, 30, 10, 28], [30, 31, 10, 29, "i"], [30, 32, 10, 30], [30, 33, 10, 31], [31, 8, 12, 6], [31, 13, 12, 11], [31, 17, 12, 15, "key"], [31, 20, 12, 18], [31, 24, 12, 22, "source"], [31, 30, 12, 28], [31, 32, 12, 30], [32, 10, 13, 8], [32, 14, 13, 12, "Object"], [32, 20, 13, 18], [32, 21, 13, 19, "prototype"], [32, 30, 13, 28], [32, 31, 13, 29, "hasOwnProperty"], [32, 45, 13, 43], [32, 46, 13, 44, "call"], [32, 50, 13, 48], [32, 51, 13, 49, "source"], [32, 57, 13, 55], [32, 59, 13, 57, "key"], [32, 62, 13, 60], [32, 63, 13, 61], [32, 65, 13, 63], [33, 12, 14, 10, "target"], [33, 18, 14, 16], [33, 19, 14, 17, "key"], [33, 22, 14, 20], [33, 23, 14, 21], [33, 26, 14, 24, "source"], [33, 32, 14, 30], [33, 33, 14, 31, "key"], [33, 36, 14, 34], [33, 37, 14, 35], [34, 10, 15, 8], [35, 8, 16, 6], [36, 6, 17, 4], [37, 6, 19, 4], [37, 13, 19, 11, "target"], [37, 19, 19, 17], [38, 4, 20, 2], [38, 5, 20, 3], [39, 4, 22, 2], [39, 11, 22, 9, "_extends"], [39, 19, 22, 17], [39, 20, 22, 18, "apply"], [39, 25, 22, 23], [39, 26, 22, 24], [39, 30, 22, 28], [39, 32, 22, 30, "arguments"], [39, 41, 22, 39], [39, 42, 22, 40], [40, 2, 23, 0], [41, 2, 25, 0], [41, 11, 25, 9, "_inherits<PERSON><PERSON>e"], [41, 25, 25, 23, "_inherits<PERSON><PERSON>e"], [41, 26, 25, 24, "subClass"], [41, 34, 25, 32], [41, 36, 25, 34, "superClass"], [41, 46, 25, 44], [41, 48, 25, 46], [42, 4, 26, 2, "subClass"], [42, 12, 26, 10], [42, 13, 26, 11, "prototype"], [42, 22, 26, 20], [42, 25, 26, 23, "Object"], [42, 31, 26, 29], [42, 32, 26, 30, "create"], [42, 38, 26, 36], [42, 39, 26, 37, "superClass"], [42, 49, 26, 47], [42, 50, 26, 48, "prototype"], [42, 59, 26, 57], [42, 60, 26, 58], [43, 4, 27, 2, "subClass"], [43, 12, 27, 10], [43, 13, 27, 11, "prototype"], [43, 22, 27, 20], [43, 23, 27, 21, "constructor"], [43, 34, 27, 32], [43, 37, 27, 35, "subClass"], [43, 45, 27, 43], [44, 4, 28, 2, "subClass"], [44, 12, 28, 10], [44, 13, 28, 11, "__proto__"], [44, 22, 28, 20], [44, 25, 28, 23, "superClass"], [44, 35, 28, 33], [45, 2, 29, 0], [46, 2, 31, 0], [46, 11, 31, 9, "_assertThisInitialized"], [46, 33, 31, 31, "_assertThisInitialized"], [46, 34, 31, 32, "self"], [46, 38, 31, 36], [46, 40, 31, 38], [47, 4, 32, 2], [47, 8, 32, 6, "self"], [47, 12, 32, 10], [47, 17, 32, 15], [47, 22, 32, 20], [47, 23, 32, 21], [47, 25, 32, 23], [48, 6, 33, 4], [48, 12, 33, 10], [48, 16, 33, 14, "ReferenceError"], [48, 30, 33, 28], [48, 31, 33, 29], [48, 90, 33, 88], [48, 91, 33, 89], [49, 4, 34, 2], [50, 4, 36, 2], [50, 11, 36, 9, "self"], [50, 15, 36, 13], [51, 2, 37, 0], [53, 2, 39, 0], [54, 0, 40, 0], [55, 0, 41, 0], [56, 0, 42, 0], [57, 0, 43, 0], [58, 0, 44, 0], [59, 0, 45, 0], [60, 0, 46, 0], [61, 2, 47, 0], [61, 6, 47, 4, "assign"], [61, 12, 47, 10], [62, 2, 49, 0], [62, 6, 49, 4], [62, 13, 49, 11, "Object"], [62, 19, 49, 17], [62, 20, 49, 18, "assign"], [62, 26, 49, 24], [62, 31, 49, 29], [62, 41, 49, 39], [62, 43, 49, 41], [63, 4, 50, 2, "assign"], [63, 10, 50, 8], [63, 13, 50, 11], [63, 22, 50, 20, "assign"], [63, 28, 50, 26, "assign"], [63, 29, 50, 27, "target"], [63, 35, 50, 33], [63, 37, 50, 35], [64, 6, 51, 4], [64, 10, 51, 8, "target"], [64, 16, 51, 14], [64, 21, 51, 19, "undefined"], [64, 30, 51, 28], [64, 34, 51, 32, "target"], [64, 40, 51, 38], [64, 45, 51, 43], [64, 49, 51, 47], [64, 51, 51, 49], [65, 8, 52, 6], [65, 14, 52, 12], [65, 18, 52, 16, "TypeError"], [65, 27, 52, 25], [65, 28, 52, 26], [65, 72, 52, 70], [65, 73, 52, 71], [66, 6, 53, 4], [67, 6, 55, 4], [67, 10, 55, 8, "output"], [67, 16, 55, 14], [67, 19, 55, 17, "Object"], [67, 25, 55, 23], [67, 26, 55, 24, "target"], [67, 32, 55, 30], [67, 33, 55, 31], [68, 6, 57, 4], [68, 11, 57, 9], [68, 15, 57, 13, "index"], [68, 20, 57, 18], [68, 23, 57, 21], [68, 24, 57, 22], [68, 26, 57, 24, "index"], [68, 31, 57, 29], [68, 34, 57, 32, "arguments"], [68, 43, 57, 41], [68, 44, 57, 42, "length"], [68, 50, 57, 48], [68, 52, 57, 50, "index"], [68, 57, 57, 55], [68, 59, 57, 57], [68, 61, 57, 59], [69, 8, 58, 6], [69, 12, 58, 10, "source"], [69, 18, 58, 16], [69, 21, 58, 19, "arguments"], [69, 30, 58, 28], [69, 31, 58, 29, "index"], [69, 36, 58, 34], [69, 37, 58, 35], [70, 8, 60, 6], [70, 12, 60, 10, "source"], [70, 18, 60, 16], [70, 23, 60, 21, "undefined"], [70, 32, 60, 30], [70, 36, 60, 34, "source"], [70, 42, 60, 40], [70, 47, 60, 45], [70, 51, 60, 49], [70, 53, 60, 51], [71, 10, 61, 8], [71, 15, 61, 13], [71, 19, 61, 17, "<PERSON><PERSON><PERSON>"], [71, 26, 61, 24], [71, 30, 61, 28, "source"], [71, 36, 61, 34], [71, 38, 61, 36], [72, 12, 62, 10], [72, 16, 62, 14, "source"], [72, 22, 62, 20], [72, 23, 62, 21, "hasOwnProperty"], [72, 37, 62, 35], [72, 38, 62, 36, "<PERSON><PERSON><PERSON>"], [72, 45, 62, 43], [72, 46, 62, 44], [72, 48, 62, 46], [73, 14, 63, 12, "output"], [73, 20, 63, 18], [73, 21, 63, 19, "<PERSON><PERSON><PERSON>"], [73, 28, 63, 26], [73, 29, 63, 27], [73, 32, 63, 30, "source"], [73, 38, 63, 36], [73, 39, 63, 37, "<PERSON><PERSON><PERSON>"], [73, 46, 63, 44], [73, 47, 63, 45], [74, 12, 64, 10], [75, 10, 65, 8], [76, 8, 66, 6], [77, 6, 67, 4], [78, 6, 69, 4], [78, 13, 69, 11, "output"], [78, 19, 69, 17], [79, 4, 70, 2], [79, 5, 70, 3], [80, 2, 71, 0], [80, 3, 71, 1], [80, 9, 71, 7], [81, 4, 72, 2, "assign"], [81, 10, 72, 8], [81, 13, 72, 11, "Object"], [81, 19, 72, 17], [81, 20, 72, 18, "assign"], [81, 26, 72, 24], [82, 2, 73, 0], [83, 2, 75, 0], [83, 6, 75, 4, "assign$1"], [83, 14, 75, 12], [83, 17, 75, 12, "exports"], [83, 24, 75, 12], [83, 25, 75, 12, "assign"], [83, 31, 75, 12], [83, 34, 75, 15, "assign"], [83, 40, 75, 21], [84, 2, 77, 0], [84, 6, 77, 4, "VENDOR_PREFIXES"], [84, 21, 77, 19], [84, 24, 77, 22], [84, 25, 77, 23], [84, 27, 77, 25], [84, 29, 77, 27], [84, 37, 77, 35], [84, 39, 77, 37], [84, 44, 77, 42], [84, 46, 77, 44], [84, 50, 77, 48], [84, 52, 77, 50], [84, 56, 77, 54], [84, 58, 77, 56], [84, 61, 77, 59], [84, 62, 77, 60], [85, 2, 78, 0], [85, 6, 78, 4, "TEST_ELEMENT"], [85, 18, 78, 16], [85, 21, 78, 19], [85, 28, 78, 26, "document"], [85, 36, 78, 34], [85, 41, 78, 39], [85, 52, 78, 50], [85, 55, 78, 53], [86, 4, 79, 2, "style"], [86, 9, 79, 7], [86, 11, 79, 9], [86, 12, 79, 10], [87, 2, 80, 0], [87, 3, 80, 1], [87, 6, 80, 4, "document"], [87, 14, 80, 12], [87, 15, 80, 13, "createElement"], [87, 28, 80, 26], [87, 29, 80, 27], [87, 34, 80, 32], [87, 35, 80, 33], [88, 2, 81, 0], [88, 6, 81, 4, "TYPE_FUNCTION"], [88, 19, 81, 17], [88, 22, 81, 20], [88, 32, 81, 30], [89, 2, 82, 0], [89, 6, 82, 4, "round"], [89, 11, 82, 9], [89, 14, 82, 12, "Math"], [89, 18, 82, 16], [89, 19, 82, 17, "round"], [89, 24, 82, 22], [90, 4, 83, 4, "abs"], [90, 7, 83, 7], [90, 10, 83, 10, "Math"], [90, 14, 83, 14], [90, 15, 83, 15, "abs"], [90, 18, 83, 18], [91, 2, 84, 0], [91, 6, 84, 4, "now"], [91, 9, 84, 7], [91, 12, 84, 10, "Date"], [91, 16, 84, 14], [91, 17, 84, 15, "now"], [91, 20, 84, 18], [93, 2, 86, 0], [94, 0, 87, 0], [95, 0, 88, 0], [96, 0, 89, 0], [97, 0, 90, 0], [98, 0, 91, 0], [99, 0, 92, 0], [101, 2, 94, 0], [101, 11, 94, 9, "prefixed"], [101, 19, 94, 17, "prefixed"], [101, 20, 94, 18, "obj"], [101, 23, 94, 21], [101, 25, 94, 23, "property"], [101, 33, 94, 31], [101, 35, 94, 33], [102, 4, 95, 2], [102, 8, 95, 6, "prefix"], [102, 14, 95, 12], [103, 4, 96, 2], [103, 8, 96, 6, "prop"], [103, 12, 96, 10], [104, 4, 97, 2], [104, 8, 97, 6, "camelProp"], [104, 17, 97, 15], [104, 20, 97, 18, "property"], [104, 28, 97, 26], [104, 29, 97, 27], [104, 30, 97, 28], [104, 31, 97, 29], [104, 32, 97, 30, "toUpperCase"], [104, 43, 97, 41], [104, 44, 97, 42], [104, 45, 97, 43], [104, 48, 97, 46, "property"], [104, 56, 97, 54], [104, 57, 97, 55, "slice"], [104, 62, 97, 60], [104, 63, 97, 61], [104, 64, 97, 62], [104, 65, 97, 63], [105, 4, 98, 2], [105, 8, 98, 6, "i"], [105, 9, 98, 7], [105, 12, 98, 10], [105, 13, 98, 11], [106, 4, 100, 2], [106, 11, 100, 9, "i"], [106, 12, 100, 10], [106, 15, 100, 13, "VENDOR_PREFIXES"], [106, 30, 100, 28], [106, 31, 100, 29, "length"], [106, 37, 100, 35], [106, 39, 100, 37], [107, 6, 101, 4, "prefix"], [107, 12, 101, 10], [107, 15, 101, 13, "VENDOR_PREFIXES"], [107, 30, 101, 28], [107, 31, 101, 29, "i"], [107, 32, 101, 30], [107, 33, 101, 31], [108, 6, 102, 4, "prop"], [108, 10, 102, 8], [108, 13, 102, 11, "prefix"], [108, 19, 102, 17], [108, 22, 102, 20, "prefix"], [108, 28, 102, 26], [108, 31, 102, 29, "camelProp"], [108, 40, 102, 38], [108, 43, 102, 41, "property"], [108, 51, 102, 49], [109, 6, 104, 4], [109, 10, 104, 8, "prop"], [109, 14, 104, 12], [109, 18, 104, 16, "obj"], [109, 21, 104, 19], [109, 23, 104, 21], [110, 8, 105, 6], [110, 15, 105, 13, "prop"], [110, 19, 105, 17], [111, 6, 106, 4], [112, 6, 108, 4, "i"], [112, 7, 108, 5], [112, 9, 108, 7], [113, 4, 109, 2], [114, 4, 111, 2], [114, 11, 111, 9, "undefined"], [114, 20, 111, 18], [115, 2, 112, 0], [117, 2, 114, 0], [118, 2, 115, 0], [118, 6, 115, 4, "win"], [118, 9, 115, 7], [119, 2, 117, 0], [119, 6, 117, 4], [119, 13, 117, 11, "window"], [119, 19, 117, 17], [119, 24, 117, 22], [119, 35, 117, 33], [119, 37, 117, 35], [120, 4, 118, 2], [121, 4, 119, 2, "win"], [121, 7, 119, 5], [121, 10, 119, 8], [121, 11, 119, 9], [121, 12, 119, 10], [122, 2, 120, 0], [122, 3, 120, 1], [122, 9, 120, 7], [123, 4, 121, 2, "win"], [123, 7, 121, 5], [123, 10, 121, 8, "window"], [123, 16, 121, 14], [124, 2, 122, 0], [125, 2, 124, 0], [125, 6, 124, 4, "PREFIXED_TOUCH_ACTION"], [125, 27, 124, 25], [125, 30, 124, 28, "prefixed"], [125, 38, 124, 36], [125, 39, 124, 37, "TEST_ELEMENT"], [125, 51, 124, 49], [125, 52, 124, 50, "style"], [125, 57, 124, 55], [125, 59, 124, 57], [125, 72, 124, 70], [125, 73, 124, 71], [126, 2, 125, 0], [126, 6, 125, 4, "NATIVE_TOUCH_ACTION"], [126, 25, 125, 23], [126, 28, 125, 26, "PREFIXED_TOUCH_ACTION"], [126, 49, 125, 47], [126, 54, 125, 52, "undefined"], [126, 63, 125, 61], [127, 2, 126, 0], [127, 11, 126, 9, "getTouchActionProps"], [127, 30, 126, 28, "getTouchActionProps"], [127, 31, 126, 28], [127, 33, 126, 31], [128, 4, 127, 2], [128, 8, 127, 6], [128, 9, 127, 7, "NATIVE_TOUCH_ACTION"], [128, 28, 127, 26], [128, 30, 127, 28], [129, 6, 128, 4], [129, 13, 128, 11], [129, 18, 128, 16], [130, 4, 129, 2], [131, 4, 131, 2], [131, 8, 131, 6, "touchMap"], [131, 16, 131, 14], [131, 19, 131, 17], [131, 20, 131, 18], [131, 21, 131, 19], [132, 4, 132, 2], [132, 8, 132, 6, "cssSupports"], [132, 19, 132, 17], [132, 22, 132, 20, "win"], [132, 25, 132, 23], [132, 26, 132, 24, "CSS"], [132, 29, 132, 27], [132, 33, 132, 31, "win"], [132, 36, 132, 34], [132, 37, 132, 35, "CSS"], [132, 40, 132, 38], [132, 41, 132, 39, "supports"], [132, 49, 132, 47], [133, 4, 133, 2], [133, 5, 133, 3], [133, 11, 133, 9], [133, 13, 133, 11], [133, 27, 133, 25], [133, 29, 133, 27], [133, 36, 133, 34], [133, 38, 133, 36], [133, 45, 133, 43], [133, 47, 133, 45], [133, 60, 133, 58], [133, 62, 133, 60], [133, 68, 133, 66], [133, 69, 133, 67], [133, 70, 133, 68, "for<PERSON>ach"], [133, 77, 133, 75], [133, 78, 133, 76], [133, 88, 133, 86, "val"], [133, 91, 133, 89], [133, 93, 133, 91], [134, 6, 134, 4], [135, 6, 135, 4], [136, 6, 136, 4], [136, 13, 136, 11, "touchMap"], [136, 21, 136, 19], [136, 22, 136, 20, "val"], [136, 25, 136, 23], [136, 26, 136, 24], [136, 29, 136, 27, "cssSupports"], [136, 40, 136, 38], [136, 43, 136, 41, "win"], [136, 46, 136, 44], [136, 47, 136, 45, "CSS"], [136, 50, 136, 48], [136, 51, 136, 49, "supports"], [136, 59, 136, 57], [136, 60, 136, 58], [136, 74, 136, 72], [136, 76, 136, 74, "val"], [136, 79, 136, 77], [136, 80, 136, 78], [136, 83, 136, 81], [136, 87, 136, 85], [137, 4, 137, 2], [137, 5, 137, 3], [137, 6, 137, 4], [138, 4, 138, 2], [138, 11, 138, 9, "touchMap"], [138, 19, 138, 17], [139, 2, 139, 0], [140, 2, 141, 0], [140, 6, 141, 4, "TOUCH_ACTION_COMPUTE"], [140, 26, 141, 24], [140, 29, 141, 27], [140, 38, 141, 36], [141, 2, 142, 0], [141, 6, 142, 4, "TOUCH_ACTION_AUTO"], [141, 23, 142, 21], [141, 26, 142, 24], [141, 32, 142, 30], [142, 2, 143, 0], [142, 6, 143, 4, "TOUCH_ACTION_MANIPULATION"], [142, 31, 143, 29], [142, 34, 143, 32], [142, 48, 143, 46], [142, 49, 143, 47], [142, 50, 143, 48], [144, 2, 145, 0], [144, 6, 145, 4, "TOUCH_ACTION_NONE"], [144, 23, 145, 21], [144, 26, 145, 24], [144, 32, 145, 30], [145, 2, 146, 0], [145, 6, 146, 4, "TOUCH_ACTION_PAN_X"], [145, 24, 146, 22], [145, 27, 146, 25], [145, 34, 146, 32], [146, 2, 147, 0], [146, 6, 147, 4, "TOUCH_ACTION_PAN_Y"], [146, 24, 147, 22], [146, 27, 147, 25], [146, 34, 147, 32], [147, 2, 148, 0], [147, 6, 148, 4, "TOUCH_ACTION_MAP"], [147, 22, 148, 20], [147, 25, 148, 23, "getTouchActionProps"], [147, 44, 148, 42], [147, 45, 148, 43], [147, 46, 148, 44], [148, 2, 150, 0], [148, 6, 150, 4, "MOBILE_REGEX"], [148, 18, 150, 16], [148, 21, 150, 19], [148, 60, 150, 58], [149, 2, 151, 0], [149, 6, 151, 4, "SUPPORT_TOUCH"], [149, 19, 151, 17], [149, 22, 151, 20], [149, 36, 151, 34], [149, 40, 151, 38, "win"], [149, 43, 151, 41], [150, 2, 152, 0], [150, 6, 152, 4, "SUPPORT_POINTER_EVENTS"], [150, 28, 152, 26], [150, 31, 152, 29, "prefixed"], [150, 39, 152, 37], [150, 40, 152, 38, "win"], [150, 43, 152, 41], [150, 45, 152, 43], [150, 59, 152, 57], [150, 60, 152, 58], [150, 65, 152, 63, "undefined"], [150, 74, 152, 72], [151, 2, 153, 0], [151, 6, 153, 4, "SUPPORT_ONLY_TOUCH"], [151, 24, 153, 22], [151, 27, 153, 25, "SUPPORT_TOUCH"], [151, 40, 153, 38], [151, 44, 153, 42, "MOBILE_REGEX"], [151, 56, 153, 54], [151, 57, 153, 55, "test"], [151, 61, 153, 59], [151, 62, 153, 60, "navigator"], [151, 71, 153, 69], [151, 72, 153, 70, "userAgent"], [151, 81, 153, 79], [151, 82, 153, 80], [152, 2, 154, 0], [152, 6, 154, 4, "INPUT_TYPE_TOUCH"], [152, 22, 154, 20], [152, 25, 154, 23], [152, 32, 154, 30], [153, 2, 155, 0], [153, 6, 155, 4, "INPUT_TYPE_PEN"], [153, 20, 155, 18], [153, 23, 155, 21], [153, 28, 155, 26], [154, 2, 156, 0], [154, 6, 156, 4, "INPUT_TYPE_MOUSE"], [154, 22, 156, 20], [154, 25, 156, 23], [154, 32, 156, 30], [155, 2, 157, 0], [155, 6, 157, 4, "INPUT_TYPE_KINECT"], [155, 23, 157, 21], [155, 26, 157, 24], [155, 34, 157, 32], [156, 2, 158, 0], [156, 6, 158, 4, "COMPUTE_INTERVAL"], [156, 22, 158, 20], [156, 25, 158, 23], [156, 27, 158, 25], [157, 2, 159, 0], [157, 6, 159, 4, "INPUT_START"], [157, 17, 159, 15], [157, 20, 159, 15, "exports"], [157, 27, 159, 15], [157, 28, 159, 15, "INPUT_START"], [157, 39, 159, 15], [157, 42, 159, 18], [157, 43, 159, 19], [158, 2, 160, 0], [158, 6, 160, 4, "INPUT_MOVE"], [158, 16, 160, 14], [158, 19, 160, 14, "exports"], [158, 26, 160, 14], [158, 27, 160, 14, "INPUT_MOVE"], [158, 37, 160, 14], [158, 40, 160, 17], [158, 41, 160, 18], [159, 2, 161, 0], [159, 6, 161, 4, "INPUT_END"], [159, 15, 161, 13], [159, 18, 161, 13, "exports"], [159, 25, 161, 13], [159, 26, 161, 13, "INPUT_END"], [159, 35, 161, 13], [159, 38, 161, 16], [159, 39, 161, 17], [160, 2, 162, 0], [160, 6, 162, 4, "INPUT_CANCEL"], [160, 18, 162, 16], [160, 21, 162, 16, "exports"], [160, 28, 162, 16], [160, 29, 162, 16, "INPUT_CANCEL"], [160, 41, 162, 16], [160, 44, 162, 19], [160, 45, 162, 20], [161, 2, 163, 0], [161, 6, 163, 4, "DIRECTION_NONE"], [161, 20, 163, 18], [161, 23, 163, 18, "exports"], [161, 30, 163, 18], [161, 31, 163, 18, "DIRECTION_NONE"], [161, 45, 163, 18], [161, 48, 163, 21], [161, 49, 163, 22], [162, 2, 164, 0], [162, 6, 164, 4, "DIRECTION_LEFT"], [162, 20, 164, 18], [162, 23, 164, 18, "exports"], [162, 30, 164, 18], [162, 31, 164, 18, "DIRECTION_LEFT"], [162, 45, 164, 18], [162, 48, 164, 21], [162, 49, 164, 22], [163, 2, 165, 0], [163, 6, 165, 4, "DIRECTION_RIGHT"], [163, 21, 165, 19], [163, 24, 165, 19, "exports"], [163, 31, 165, 19], [163, 32, 165, 19, "DIRECTION_RIGHT"], [163, 47, 165, 19], [163, 50, 165, 22], [163, 51, 165, 23], [164, 2, 166, 0], [164, 6, 166, 4, "DIRECTION_UP"], [164, 18, 166, 16], [164, 21, 166, 16, "exports"], [164, 28, 166, 16], [164, 29, 166, 16, "DIRECTION_UP"], [164, 41, 166, 16], [164, 44, 166, 19], [164, 45, 166, 20], [165, 2, 167, 0], [165, 6, 167, 4, "DIRECTION_DOWN"], [165, 20, 167, 18], [165, 23, 167, 18, "exports"], [165, 30, 167, 18], [165, 31, 167, 18, "DIRECTION_DOWN"], [165, 45, 167, 18], [165, 48, 167, 21], [165, 50, 167, 23], [166, 2, 168, 0], [166, 6, 168, 4, "DIRECTION_HORIZONTAL"], [166, 26, 168, 24], [166, 29, 168, 24, "exports"], [166, 36, 168, 24], [166, 37, 168, 24, "DIRECTION_HORIZONTAL"], [166, 57, 168, 24], [166, 60, 168, 27, "DIRECTION_LEFT"], [166, 74, 168, 41], [166, 77, 168, 44, "DIRECTION_RIGHT"], [166, 92, 168, 59], [167, 2, 169, 0], [167, 6, 169, 4, "DIRECTION_VERTICAL"], [167, 24, 169, 22], [167, 27, 169, 22, "exports"], [167, 34, 169, 22], [167, 35, 169, 22, "DIRECTION_VERTICAL"], [167, 53, 169, 22], [167, 56, 169, 25, "DIRECTION_UP"], [167, 68, 169, 37], [167, 71, 169, 40, "DIRECTION_DOWN"], [167, 85, 169, 54], [168, 2, 170, 0], [168, 6, 170, 4, "DIRECTION_ALL"], [168, 19, 170, 17], [168, 22, 170, 17, "exports"], [168, 29, 170, 17], [168, 30, 170, 17, "DIRECTION_ALL"], [168, 43, 170, 17], [168, 46, 170, 20, "DIRECTION_HORIZONTAL"], [168, 66, 170, 40], [168, 69, 170, 43, "DIRECTION_VERTICAL"], [168, 87, 170, 61], [169, 2, 171, 0], [169, 6, 171, 4, "PROPS_XY"], [169, 14, 171, 12], [169, 17, 171, 15], [169, 18, 171, 16], [169, 21, 171, 19], [169, 23, 171, 21], [169, 26, 171, 24], [169, 27, 171, 25], [170, 2, 172, 0], [170, 6, 172, 4, "PROPS_CLIENT_XY"], [170, 21, 172, 19], [170, 24, 172, 22], [170, 25, 172, 23], [170, 34, 172, 32], [170, 36, 172, 34], [170, 45, 172, 43], [170, 46, 172, 44], [172, 2, 174, 0], [173, 0, 175, 0], [174, 0, 176, 0], [175, 0, 177, 0], [176, 0, 178, 0], [177, 0, 179, 0], [178, 0, 180, 0], [179, 2, 181, 0], [179, 11, 181, 9, "each"], [179, 15, 181, 13, "each"], [179, 16, 181, 14, "obj"], [179, 19, 181, 17], [179, 21, 181, 19, "iterator"], [179, 29, 181, 27], [179, 31, 181, 29, "context"], [179, 38, 181, 36], [179, 40, 181, 38], [180, 4, 182, 2], [180, 8, 182, 6, "i"], [180, 9, 182, 7], [181, 4, 184, 2], [181, 8, 184, 6], [181, 9, 184, 7, "obj"], [181, 12, 184, 10], [181, 14, 184, 12], [182, 6, 185, 4], [183, 4, 186, 2], [184, 4, 188, 2], [184, 8, 188, 6, "obj"], [184, 11, 188, 9], [184, 12, 188, 10, "for<PERSON>ach"], [184, 19, 188, 17], [184, 21, 188, 19], [185, 6, 189, 4, "obj"], [185, 9, 189, 7], [185, 10, 189, 8, "for<PERSON>ach"], [185, 17, 189, 15], [185, 18, 189, 16, "iterator"], [185, 26, 189, 24], [185, 28, 189, 26, "context"], [185, 35, 189, 33], [185, 36, 189, 34], [186, 4, 190, 2], [186, 5, 190, 3], [186, 11, 190, 9], [186, 15, 190, 13, "obj"], [186, 18, 190, 16], [186, 19, 190, 17, "length"], [186, 25, 190, 23], [186, 30, 190, 28, "undefined"], [186, 39, 190, 37], [186, 41, 190, 39], [187, 6, 191, 4, "i"], [187, 7, 191, 5], [187, 10, 191, 8], [187, 11, 191, 9], [188, 6, 193, 4], [188, 13, 193, 11, "i"], [188, 14, 193, 12], [188, 17, 193, 15, "obj"], [188, 20, 193, 18], [188, 21, 193, 19, "length"], [188, 27, 193, 25], [188, 29, 193, 27], [189, 8, 194, 6, "iterator"], [189, 16, 194, 14], [189, 17, 194, 15, "call"], [189, 21, 194, 19], [189, 22, 194, 20, "context"], [189, 29, 194, 27], [189, 31, 194, 29, "obj"], [189, 34, 194, 32], [189, 35, 194, 33, "i"], [189, 36, 194, 34], [189, 37, 194, 35], [189, 39, 194, 37, "i"], [189, 40, 194, 38], [189, 42, 194, 40, "obj"], [189, 45, 194, 43], [189, 46, 194, 44], [190, 8, 195, 6, "i"], [190, 9, 195, 7], [190, 11, 195, 9], [191, 6, 196, 4], [192, 4, 197, 2], [192, 5, 197, 3], [192, 11, 197, 9], [193, 6, 198, 4], [193, 11, 198, 9, "i"], [193, 12, 198, 10], [193, 16, 198, 14, "obj"], [193, 19, 198, 17], [193, 21, 198, 19], [194, 8, 199, 6, "obj"], [194, 11, 199, 9], [194, 12, 199, 10, "hasOwnProperty"], [194, 26, 199, 24], [194, 27, 199, 25, "i"], [194, 28, 199, 26], [194, 29, 199, 27], [194, 33, 199, 31, "iterator"], [194, 41, 199, 39], [194, 42, 199, 40, "call"], [194, 46, 199, 44], [194, 47, 199, 45, "context"], [194, 54, 199, 52], [194, 56, 199, 54, "obj"], [194, 59, 199, 57], [194, 60, 199, 58, "i"], [194, 61, 199, 59], [194, 62, 199, 60], [194, 64, 199, 62, "i"], [194, 65, 199, 63], [194, 67, 199, 65, "obj"], [194, 70, 199, 68], [194, 71, 199, 69], [195, 6, 200, 4], [196, 4, 201, 2], [197, 2, 202, 0], [199, 2, 204, 0], [200, 0, 205, 0], [201, 0, 206, 0], [202, 0, 207, 0], [203, 0, 208, 0], [204, 0, 209, 0], [205, 0, 210, 0], [206, 0, 211, 0], [208, 2, 213, 0], [208, 11, 213, 9, "boolOrFn"], [208, 19, 213, 17, "boolOrFn"], [208, 20, 213, 18, "val"], [208, 23, 213, 21], [208, 25, 213, 23, "args"], [208, 29, 213, 27], [208, 31, 213, 29], [209, 4, 214, 2], [209, 8, 214, 6], [209, 15, 214, 13, "val"], [209, 18, 214, 16], [209, 23, 214, 21, "TYPE_FUNCTION"], [209, 36, 214, 34], [209, 38, 214, 36], [210, 6, 215, 4], [210, 13, 215, 11, "val"], [210, 16, 215, 14], [210, 17, 215, 15, "apply"], [210, 22, 215, 20], [210, 23, 215, 21, "args"], [210, 27, 215, 25], [210, 30, 215, 28, "args"], [210, 34, 215, 32], [210, 35, 215, 33], [210, 36, 215, 34], [210, 37, 215, 35], [210, 41, 215, 39, "undefined"], [210, 50, 215, 48], [210, 53, 215, 51, "undefined"], [210, 62, 215, 60], [210, 64, 215, 62, "args"], [210, 68, 215, 66], [210, 69, 215, 67], [211, 4, 216, 2], [212, 4, 218, 2], [212, 11, 218, 9, "val"], [212, 14, 218, 12], [213, 2, 219, 0], [215, 2, 221, 0], [216, 0, 222, 0], [217, 0, 223, 0], [218, 0, 224, 0], [219, 0, 225, 0], [220, 0, 226, 0], [221, 0, 227, 0], [222, 2, 228, 0], [222, 11, 228, 9, "inStr"], [222, 16, 228, 14, "inStr"], [222, 17, 228, 15, "str"], [222, 20, 228, 18], [222, 22, 228, 20, "find"], [222, 26, 228, 24], [222, 28, 228, 26], [223, 4, 229, 2], [223, 11, 229, 9, "str"], [223, 14, 229, 12], [223, 15, 229, 13, "indexOf"], [223, 22, 229, 20], [223, 23, 229, 21, "find"], [223, 27, 229, 25], [223, 28, 229, 26], [223, 31, 229, 29], [223, 32, 229, 30], [223, 33, 229, 31], [224, 2, 230, 0], [226, 2, 232, 0], [227, 0, 233, 0], [228, 0, 234, 0], [229, 0, 235, 0], [230, 0, 236, 0], [231, 0, 237, 0], [233, 2, 239, 0], [233, 11, 239, 9, "cleanTouchActions"], [233, 28, 239, 26, "cleanTouchActions"], [233, 29, 239, 27, "actions"], [233, 36, 239, 34], [233, 38, 239, 36], [234, 4, 240, 2], [235, 4, 241, 2], [235, 8, 241, 6, "inStr"], [235, 13, 241, 11], [235, 14, 241, 12, "actions"], [235, 21, 241, 19], [235, 23, 241, 21, "TOUCH_ACTION_NONE"], [235, 40, 241, 38], [235, 41, 241, 39], [235, 43, 241, 41], [236, 6, 242, 4], [236, 13, 242, 11, "TOUCH_ACTION_NONE"], [236, 30, 242, 28], [237, 4, 243, 2], [238, 4, 245, 2], [238, 8, 245, 6, "hasPanX"], [238, 15, 245, 13], [238, 18, 245, 16, "inStr"], [238, 23, 245, 21], [238, 24, 245, 22, "actions"], [238, 31, 245, 29], [238, 33, 245, 31, "TOUCH_ACTION_PAN_X"], [238, 51, 245, 49], [238, 52, 245, 50], [239, 4, 246, 2], [239, 8, 246, 6, "hasPanY"], [239, 15, 246, 13], [239, 18, 246, 16, "inStr"], [239, 23, 246, 21], [239, 24, 246, 22, "actions"], [239, 31, 246, 29], [239, 33, 246, 31, "TOUCH_ACTION_PAN_Y"], [239, 51, 246, 49], [239, 52, 246, 50], [239, 53, 246, 51], [239, 54, 246, 52], [240, 4, 247, 2], [241, 4, 248, 2], [242, 4, 249, 2], [244, 4, 251, 2], [244, 8, 251, 6, "hasPanX"], [244, 15, 251, 13], [244, 19, 251, 17, "hasPanY"], [244, 26, 251, 24], [244, 28, 251, 26], [245, 6, 252, 4], [245, 13, 252, 11, "TOUCH_ACTION_NONE"], [245, 30, 252, 28], [246, 4, 253, 2], [246, 5, 253, 3], [246, 6, 253, 4], [248, 4, 256, 2], [248, 8, 256, 6, "hasPanX"], [248, 15, 256, 13], [248, 19, 256, 17, "hasPanY"], [248, 26, 256, 24], [248, 28, 256, 26], [249, 6, 257, 4], [249, 13, 257, 11, "hasPanX"], [249, 20, 257, 18], [249, 23, 257, 21, "TOUCH_ACTION_PAN_X"], [249, 41, 257, 39], [249, 44, 257, 42, "TOUCH_ACTION_PAN_Y"], [249, 62, 257, 60], [250, 4, 258, 2], [250, 5, 258, 3], [250, 6, 258, 4], [252, 4, 261, 2], [252, 8, 261, 6, "inStr"], [252, 13, 261, 11], [252, 14, 261, 12, "actions"], [252, 21, 261, 19], [252, 23, 261, 21, "TOUCH_ACTION_MANIPULATION"], [252, 48, 261, 46], [252, 49, 261, 47], [252, 51, 261, 49], [253, 6, 262, 4], [253, 13, 262, 11, "TOUCH_ACTION_MANIPULATION"], [253, 38, 262, 36], [254, 4, 263, 2], [255, 4, 265, 2], [255, 11, 265, 9, "TOUCH_ACTION_AUTO"], [255, 28, 265, 26], [256, 2, 266, 0], [258, 2, 268, 0], [259, 0, 269, 0], [260, 0, 270, 0], [261, 0, 271, 0], [262, 0, 272, 0], [263, 0, 273, 0], [264, 0, 274, 0], [265, 0, 275, 0], [267, 2, 277, 0], [267, 6, 277, 4, "TouchAction"], [267, 17, 277, 15], [267, 20, 277, 15, "exports"], [267, 27, 277, 15], [267, 28, 277, 15, "TouchAction"], [267, 39, 277, 15], [267, 42, 278, 0], [268, 2, 279, 0], [268, 14, 279, 12], [269, 4, 280, 2], [269, 13, 280, 11, "TouchAction"], [269, 24, 280, 22, "TouchAction"], [269, 25, 280, 23, "manager"], [269, 32, 280, 30], [269, 34, 280, 32, "value"], [269, 39, 280, 37], [269, 41, 280, 39], [270, 6, 281, 4], [270, 10, 281, 8], [270, 11, 281, 9, "manager"], [270, 18, 281, 16], [270, 21, 281, 19, "manager"], [270, 28, 281, 26], [271, 6, 282, 4], [271, 10, 282, 8], [271, 11, 282, 9, "set"], [271, 14, 282, 12], [271, 15, 282, 13, "value"], [271, 20, 282, 18], [271, 21, 282, 19], [272, 4, 283, 2], [273, 4, 284, 2], [274, 0, 285, 0], [275, 0, 286, 0], [276, 0, 287, 0], [277, 0, 288, 0], [279, 4, 291, 2], [279, 8, 291, 6, "_proto"], [279, 14, 291, 12], [279, 17, 291, 15, "TouchAction"], [279, 28, 291, 26], [279, 29, 291, 27, "prototype"], [279, 38, 291, 36], [280, 4, 293, 2, "_proto"], [280, 10, 293, 8], [280, 11, 293, 9, "set"], [280, 14, 293, 12], [280, 17, 293, 15], [280, 26, 293, 24, "set"], [280, 29, 293, 27, "set"], [280, 30, 293, 28, "value"], [280, 35, 293, 33], [280, 37, 293, 35], [281, 6, 294, 4], [282, 6, 295, 4], [282, 10, 295, 8, "value"], [282, 15, 295, 13], [282, 20, 295, 18, "TOUCH_ACTION_COMPUTE"], [282, 40, 295, 38], [282, 42, 295, 40], [283, 8, 296, 6, "value"], [283, 13, 296, 11], [283, 16, 296, 14], [283, 20, 296, 18], [283, 21, 296, 19, "compute"], [283, 28, 296, 26], [283, 29, 296, 27], [283, 30, 296, 28], [284, 6, 297, 4], [285, 6, 299, 4], [285, 10, 299, 8, "NATIVE_TOUCH_ACTION"], [285, 29, 299, 27], [285, 33, 299, 31], [285, 37, 299, 35], [285, 38, 299, 36, "manager"], [285, 45, 299, 43], [285, 46, 299, 44, "element"], [285, 53, 299, 51], [285, 54, 299, 52, "style"], [285, 59, 299, 57], [285, 63, 299, 61, "TOUCH_ACTION_MAP"], [285, 79, 299, 77], [285, 80, 299, 78, "value"], [285, 85, 299, 83], [285, 86, 299, 84], [285, 88, 299, 86], [286, 8, 300, 6], [286, 12, 300, 10], [286, 13, 300, 11, "manager"], [286, 20, 300, 18], [286, 21, 300, 19, "element"], [286, 28, 300, 26], [286, 29, 300, 27, "style"], [286, 34, 300, 32], [286, 35, 300, 33, "PREFIXED_TOUCH_ACTION"], [286, 56, 300, 54], [286, 57, 300, 55], [286, 60, 300, 58, "value"], [286, 65, 300, 63], [287, 6, 301, 4], [288, 6, 303, 4], [288, 10, 303, 8], [288, 11, 303, 9, "actions"], [288, 18, 303, 16], [288, 21, 303, 19, "value"], [288, 26, 303, 24], [288, 27, 303, 25, "toLowerCase"], [288, 38, 303, 36], [288, 39, 303, 37], [288, 40, 303, 38], [288, 41, 303, 39, "trim"], [288, 45, 303, 43], [288, 46, 303, 44], [288, 47, 303, 45], [289, 4, 304, 2], [289, 5, 304, 3], [290, 4, 305, 2], [291, 0, 306, 0], [292, 0, 307, 0], [293, 0, 308, 0], [295, 4, 311, 2, "_proto"], [295, 10, 311, 8], [295, 11, 311, 9, "update"], [295, 17, 311, 15], [295, 20, 311, 18], [295, 29, 311, 27, "update"], [295, 35, 311, 33, "update"], [295, 36, 311, 33], [295, 38, 311, 36], [296, 6, 312, 4], [296, 10, 312, 8], [296, 11, 312, 9, "set"], [296, 14, 312, 12], [296, 15, 312, 13], [296, 19, 312, 17], [296, 20, 312, 18, "manager"], [296, 27, 312, 25], [296, 28, 312, 26, "options"], [296, 35, 312, 33], [296, 36, 312, 34, "touchAction"], [296, 47, 312, 45], [296, 48, 312, 46], [297, 4, 313, 2], [297, 5, 313, 3], [298, 4, 314, 2], [299, 0, 315, 0], [300, 0, 316, 0], [301, 0, 317, 0], [302, 0, 318, 0], [304, 4, 321, 2, "_proto"], [304, 10, 321, 8], [304, 11, 321, 9, "compute"], [304, 18, 321, 16], [304, 21, 321, 19], [304, 30, 321, 28, "compute"], [304, 37, 321, 35, "compute"], [304, 38, 321, 35], [304, 40, 321, 38], [305, 6, 322, 4], [305, 10, 322, 8, "actions"], [305, 17, 322, 15], [305, 20, 322, 18], [305, 22, 322, 20], [306, 6, 323, 4, "each"], [306, 10, 323, 8], [306, 11, 323, 9], [306, 15, 323, 13], [306, 16, 323, 14, "manager"], [306, 23, 323, 21], [306, 24, 323, 22, "recognizers"], [306, 35, 323, 33], [306, 37, 323, 35], [306, 47, 323, 45, "recognizer"], [306, 57, 323, 55], [306, 59, 323, 57], [307, 8, 324, 6], [307, 12, 324, 10, "boolOrFn"], [307, 20, 324, 18], [307, 21, 324, 19, "recognizer"], [307, 31, 324, 29], [307, 32, 324, 30, "options"], [307, 39, 324, 37], [307, 40, 324, 38, "enable"], [307, 46, 324, 44], [307, 48, 324, 46], [307, 49, 324, 47, "recognizer"], [307, 59, 324, 57], [307, 60, 324, 58], [307, 61, 324, 59], [307, 63, 324, 61], [308, 10, 325, 8, "actions"], [308, 17, 325, 15], [308, 20, 325, 18, "actions"], [308, 27, 325, 25], [308, 28, 325, 26, "concat"], [308, 34, 325, 32], [308, 35, 325, 33, "recognizer"], [308, 45, 325, 43], [308, 46, 325, 44, "getTouchAction"], [308, 60, 325, 58], [308, 61, 325, 59], [308, 62, 325, 60], [308, 63, 325, 61], [309, 8, 326, 6], [310, 6, 327, 4], [310, 7, 327, 5], [310, 8, 327, 6], [311, 6, 328, 4], [311, 13, 328, 11, "cleanTouchActions"], [311, 30, 328, 28], [311, 31, 328, 29, "actions"], [311, 38, 328, 36], [311, 39, 328, 37, "join"], [311, 43, 328, 41], [311, 44, 328, 42], [311, 47, 328, 45], [311, 48, 328, 46], [311, 49, 328, 47], [312, 4, 329, 2], [312, 5, 329, 3], [313, 4, 330, 2], [314, 0, 331, 0], [315, 0, 332, 0], [316, 0, 333, 0], [317, 0, 334, 0], [319, 4, 337, 2, "_proto"], [319, 10, 337, 8], [319, 11, 337, 9, "preventDefaults"], [319, 26, 337, 24], [319, 29, 337, 27], [319, 38, 337, 36, "preventDefaults"], [319, 53, 337, 51, "preventDefaults"], [319, 54, 337, 52, "input"], [319, 59, 337, 57], [319, 61, 337, 59], [320, 6, 338, 4], [320, 10, 338, 8, "srcEvent"], [320, 18, 338, 16], [320, 21, 338, 19, "input"], [320, 26, 338, 24], [320, 27, 338, 25, "srcEvent"], [320, 35, 338, 33], [321, 6, 339, 4], [321, 10, 339, 8, "direction"], [321, 19, 339, 17], [321, 22, 339, 20, "input"], [321, 27, 339, 25], [321, 28, 339, 26, "offsetDirection"], [321, 43, 339, 41], [321, 44, 339, 42], [321, 45, 339, 43], [323, 6, 341, 4], [323, 10, 341, 8], [323, 14, 341, 12], [323, 15, 341, 13, "manager"], [323, 22, 341, 20], [323, 23, 341, 21, "session"], [323, 30, 341, 28], [323, 31, 341, 29, "prevented"], [323, 40, 341, 38], [323, 42, 341, 40], [324, 8, 342, 6, "srcEvent"], [324, 16, 342, 14], [324, 17, 342, 15, "preventDefault"], [324, 31, 342, 29], [324, 32, 342, 30], [324, 33, 342, 31], [325, 8, 343, 6], [326, 6, 344, 4], [327, 6, 346, 4], [327, 10, 346, 8, "actions"], [327, 17, 346, 15], [327, 20, 346, 18], [327, 24, 346, 22], [327, 25, 346, 23, "actions"], [327, 32, 346, 30], [328, 6, 347, 4], [328, 10, 347, 8, "hasNone"], [328, 17, 347, 15], [328, 20, 347, 18, "inStr"], [328, 25, 347, 23], [328, 26, 347, 24, "actions"], [328, 33, 347, 31], [328, 35, 347, 33, "TOUCH_ACTION_NONE"], [328, 52, 347, 50], [328, 53, 347, 51], [328, 57, 347, 55], [328, 58, 347, 56, "TOUCH_ACTION_MAP"], [328, 74, 347, 72], [328, 75, 347, 73, "TOUCH_ACTION_NONE"], [328, 92, 347, 90], [328, 93, 347, 91], [329, 6, 348, 4], [329, 10, 348, 8, "hasPanY"], [329, 17, 348, 15], [329, 20, 348, 18, "inStr"], [329, 25, 348, 23], [329, 26, 348, 24, "actions"], [329, 33, 348, 31], [329, 35, 348, 33, "TOUCH_ACTION_PAN_Y"], [329, 53, 348, 51], [329, 54, 348, 52], [329, 58, 348, 56], [329, 59, 348, 57, "TOUCH_ACTION_MAP"], [329, 75, 348, 73], [329, 76, 348, 74, "TOUCH_ACTION_PAN_Y"], [329, 94, 348, 92], [329, 95, 348, 93], [330, 6, 349, 4], [330, 10, 349, 8, "hasPanX"], [330, 17, 349, 15], [330, 20, 349, 18, "inStr"], [330, 25, 349, 23], [330, 26, 349, 24, "actions"], [330, 33, 349, 31], [330, 35, 349, 33, "TOUCH_ACTION_PAN_X"], [330, 53, 349, 51], [330, 54, 349, 52], [330, 58, 349, 56], [330, 59, 349, 57, "TOUCH_ACTION_MAP"], [330, 75, 349, 73], [330, 76, 349, 74, "TOUCH_ACTION_PAN_X"], [330, 94, 349, 92], [330, 95, 349, 93], [331, 6, 351, 4], [331, 10, 351, 8, "hasNone"], [331, 17, 351, 15], [331, 19, 351, 17], [332, 8, 352, 6], [333, 8, 353, 6], [333, 12, 353, 10, "isTapPointer"], [333, 24, 353, 22], [333, 27, 353, 25, "input"], [333, 32, 353, 30], [333, 33, 353, 31, "pointers"], [333, 41, 353, 39], [333, 42, 353, 40, "length"], [333, 48, 353, 46], [333, 53, 353, 51], [333, 54, 353, 52], [334, 8, 354, 6], [334, 12, 354, 10, "isTapMovement"], [334, 25, 354, 23], [334, 28, 354, 26, "input"], [334, 33, 354, 31], [334, 34, 354, 32, "distance"], [334, 42, 354, 40], [334, 45, 354, 43], [334, 46, 354, 44], [335, 8, 355, 6], [335, 12, 355, 10, "isTapTouchTime"], [335, 26, 355, 24], [335, 29, 355, 27, "input"], [335, 34, 355, 32], [335, 35, 355, 33, "deltaTime"], [335, 44, 355, 42], [335, 47, 355, 45], [335, 50, 355, 48], [336, 8, 357, 6], [336, 12, 357, 10, "isTapPointer"], [336, 24, 357, 22], [336, 28, 357, 26, "isTapMovement"], [336, 41, 357, 39], [336, 45, 357, 43, "isTapTouchTime"], [336, 59, 357, 57], [336, 61, 357, 59], [337, 10, 358, 8], [338, 8, 359, 6], [339, 6, 360, 4], [340, 6, 362, 4], [340, 10, 362, 8, "hasPanX"], [340, 17, 362, 15], [340, 21, 362, 19, "hasPanY"], [340, 28, 362, 26], [340, 30, 362, 28], [341, 8, 363, 6], [342, 8, 364, 6], [343, 6, 365, 4], [344, 6, 367, 4], [344, 10, 367, 8, "hasNone"], [344, 17, 367, 15], [344, 21, 367, 19, "hasPanY"], [344, 28, 367, 26], [344, 32, 367, 30, "direction"], [344, 41, 367, 39], [344, 44, 367, 42, "DIRECTION_HORIZONTAL"], [344, 64, 367, 62], [344, 68, 367, 66, "hasPanX"], [344, 75, 367, 73], [344, 79, 367, 77, "direction"], [344, 88, 367, 86], [344, 91, 367, 89, "DIRECTION_VERTICAL"], [344, 109, 367, 107], [344, 111, 367, 109], [345, 8, 368, 6], [345, 15, 368, 13], [345, 19, 368, 17], [345, 20, 368, 18, "preventSrc"], [345, 30, 368, 28], [345, 31, 368, 29, "srcEvent"], [345, 39, 368, 37], [345, 40, 368, 38], [346, 6, 369, 4], [347, 4, 370, 2], [347, 5, 370, 3], [348, 4, 371, 2], [349, 0, 372, 0], [350, 0, 373, 0], [351, 0, 374, 0], [352, 0, 375, 0], [354, 4, 378, 2, "_proto"], [354, 10, 378, 8], [354, 11, 378, 9, "preventSrc"], [354, 21, 378, 19], [354, 24, 378, 22], [354, 33, 378, 31, "preventSrc"], [354, 43, 378, 41, "preventSrc"], [354, 44, 378, 42, "srcEvent"], [354, 52, 378, 50], [354, 54, 378, 52], [355, 6, 379, 4], [355, 10, 379, 8], [355, 11, 379, 9, "manager"], [355, 18, 379, 16], [355, 19, 379, 17, "session"], [355, 26, 379, 24], [355, 27, 379, 25, "prevented"], [355, 36, 379, 34], [355, 39, 379, 37], [355, 43, 379, 41], [356, 6, 380, 4, "srcEvent"], [356, 14, 380, 12], [356, 15, 380, 13, "preventDefault"], [356, 29, 380, 27], [356, 30, 380, 28], [356, 31, 380, 29], [357, 4, 381, 2], [357, 5, 381, 3], [358, 4, 383, 2], [358, 11, 383, 9, "TouchAction"], [358, 22, 383, 20], [359, 2, 384, 0], [359, 3, 384, 1], [359, 4, 384, 2], [359, 5, 384, 3], [361, 2, 386, 0], [362, 0, 387, 0], [363, 0, 388, 0], [364, 0, 389, 0], [365, 0, 390, 0], [366, 0, 391, 0], [367, 0, 392, 0], [368, 0, 393, 0], [369, 2, 394, 0], [369, 11, 394, 9, "hasParent"], [369, 20, 394, 18, "hasParent"], [369, 21, 394, 19, "node"], [369, 25, 394, 23], [369, 27, 394, 25, "parent"], [369, 33, 394, 31], [369, 35, 394, 33], [370, 4, 395, 2], [370, 11, 395, 9, "node"], [370, 15, 395, 13], [370, 17, 395, 15], [371, 6, 396, 4], [371, 10, 396, 8, "node"], [371, 14, 396, 12], [371, 19, 396, 17, "parent"], [371, 25, 396, 23], [371, 27, 396, 25], [372, 8, 397, 6], [372, 15, 397, 13], [372, 19, 397, 17], [373, 6, 398, 4], [374, 6, 400, 4, "node"], [374, 10, 400, 8], [374, 13, 400, 11, "node"], [374, 17, 400, 15], [374, 18, 400, 16, "parentNode"], [374, 28, 400, 26], [375, 4, 401, 2], [376, 4, 403, 2], [376, 11, 403, 9], [376, 16, 403, 14], [377, 2, 404, 0], [379, 2, 406, 0], [380, 0, 407, 0], [381, 0, 408, 0], [382, 0, 409, 0], [383, 0, 410, 0], [384, 0, 411, 0], [386, 2, 413, 0], [386, 11, 413, 9, "getCenter"], [386, 20, 413, 18, "getCenter"], [386, 21, 413, 19, "pointers"], [386, 29, 413, 27], [386, 31, 413, 29], [387, 4, 414, 2], [387, 8, 414, 6, "pointers<PERSON><PERSON><PERSON>"], [387, 22, 414, 20], [387, 25, 414, 23, "pointers"], [387, 33, 414, 31], [387, 34, 414, 32, "length"], [387, 40, 414, 38], [387, 41, 414, 39], [387, 42, 414, 40], [389, 4, 416, 2], [389, 8, 416, 6, "pointers<PERSON><PERSON><PERSON>"], [389, 22, 416, 20], [389, 27, 416, 25], [389, 28, 416, 26], [389, 30, 416, 28], [390, 6, 417, 4], [390, 13, 417, 11], [391, 8, 418, 6, "x"], [391, 9, 418, 7], [391, 11, 418, 9, "round"], [391, 16, 418, 14], [391, 17, 418, 15, "pointers"], [391, 25, 418, 23], [391, 26, 418, 24], [391, 27, 418, 25], [391, 28, 418, 26], [391, 29, 418, 27, "clientX"], [391, 36, 418, 34], [391, 37, 418, 35], [392, 8, 419, 6, "y"], [392, 9, 419, 7], [392, 11, 419, 9, "round"], [392, 16, 419, 14], [392, 17, 419, 15, "pointers"], [392, 25, 419, 23], [392, 26, 419, 24], [392, 27, 419, 25], [392, 28, 419, 26], [392, 29, 419, 27, "clientY"], [392, 36, 419, 34], [393, 6, 420, 4], [393, 7, 420, 5], [394, 4, 421, 2], [395, 4, 423, 2], [395, 8, 423, 6, "x"], [395, 9, 423, 7], [395, 12, 423, 10], [395, 13, 423, 11], [396, 4, 424, 2], [396, 8, 424, 6, "y"], [396, 9, 424, 7], [396, 12, 424, 10], [396, 13, 424, 11], [397, 4, 425, 2], [397, 8, 425, 6, "i"], [397, 9, 425, 7], [397, 12, 425, 10], [397, 13, 425, 11], [398, 4, 427, 2], [398, 11, 427, 9, "i"], [398, 12, 427, 10], [398, 15, 427, 13, "pointers<PERSON><PERSON><PERSON>"], [398, 29, 427, 27], [398, 31, 427, 29], [399, 6, 428, 4, "x"], [399, 7, 428, 5], [399, 11, 428, 9, "pointers"], [399, 19, 428, 17], [399, 20, 428, 18, "i"], [399, 21, 428, 19], [399, 22, 428, 20], [399, 23, 428, 21, "clientX"], [399, 30, 428, 28], [400, 6, 429, 4, "y"], [400, 7, 429, 5], [400, 11, 429, 9, "pointers"], [400, 19, 429, 17], [400, 20, 429, 18, "i"], [400, 21, 429, 19], [400, 22, 429, 20], [400, 23, 429, 21, "clientY"], [400, 30, 429, 28], [401, 6, 430, 4, "i"], [401, 7, 430, 5], [401, 9, 430, 7], [402, 4, 431, 2], [403, 4, 433, 2], [403, 11, 433, 9], [404, 6, 434, 4, "x"], [404, 7, 434, 5], [404, 9, 434, 7, "round"], [404, 14, 434, 12], [404, 15, 434, 13, "x"], [404, 16, 434, 14], [404, 19, 434, 17, "pointers<PERSON><PERSON><PERSON>"], [404, 33, 434, 31], [404, 34, 434, 32], [405, 6, 435, 4, "y"], [405, 7, 435, 5], [405, 9, 435, 7, "round"], [405, 14, 435, 12], [405, 15, 435, 13, "y"], [405, 16, 435, 14], [405, 19, 435, 17, "pointers<PERSON><PERSON><PERSON>"], [405, 33, 435, 31], [406, 4, 436, 2], [406, 5, 436, 3], [407, 2, 437, 0], [409, 2, 439, 0], [410, 0, 440, 0], [411, 0, 441, 0], [412, 0, 442, 0], [413, 0, 443, 0], [414, 0, 444, 0], [416, 2, 446, 0], [416, 11, 446, 9, "simpleCloneInputData"], [416, 31, 446, 29, "simpleCloneInputData"], [416, 32, 446, 30, "input"], [416, 37, 446, 35], [416, 39, 446, 37], [417, 4, 447, 2], [418, 4, 448, 2], [419, 4, 449, 2], [419, 8, 449, 6, "pointers"], [419, 16, 449, 14], [419, 19, 449, 17], [419, 21, 449, 19], [420, 4, 450, 2], [420, 8, 450, 6, "i"], [420, 9, 450, 7], [420, 12, 450, 10], [420, 13, 450, 11], [421, 4, 452, 2], [421, 11, 452, 9, "i"], [421, 12, 452, 10], [421, 15, 452, 13, "input"], [421, 20, 452, 18], [421, 21, 452, 19, "pointers"], [421, 29, 452, 27], [421, 30, 452, 28, "length"], [421, 36, 452, 34], [421, 38, 452, 36], [422, 6, 453, 4, "pointers"], [422, 14, 453, 12], [422, 15, 453, 13, "i"], [422, 16, 453, 14], [422, 17, 453, 15], [422, 20, 453, 18], [423, 8, 454, 6, "clientX"], [423, 15, 454, 13], [423, 17, 454, 15, "round"], [423, 22, 454, 20], [423, 23, 454, 21, "input"], [423, 28, 454, 26], [423, 29, 454, 27, "pointers"], [423, 37, 454, 35], [423, 38, 454, 36, "i"], [423, 39, 454, 37], [423, 40, 454, 38], [423, 41, 454, 39, "clientX"], [423, 48, 454, 46], [423, 49, 454, 47], [424, 8, 455, 6, "clientY"], [424, 15, 455, 13], [424, 17, 455, 15, "round"], [424, 22, 455, 20], [424, 23, 455, 21, "input"], [424, 28, 455, 26], [424, 29, 455, 27, "pointers"], [424, 37, 455, 35], [424, 38, 455, 36, "i"], [424, 39, 455, 37], [424, 40, 455, 38], [424, 41, 455, 39, "clientY"], [424, 48, 455, 46], [425, 6, 456, 4], [425, 7, 456, 5], [426, 6, 457, 4, "i"], [426, 7, 457, 5], [426, 9, 457, 7], [427, 4, 458, 2], [428, 4, 460, 2], [428, 11, 460, 9], [429, 6, 461, 4, "timeStamp"], [429, 15, 461, 13], [429, 17, 461, 15, "now"], [429, 20, 461, 18], [429, 21, 461, 19], [429, 22, 461, 20], [430, 6, 462, 4, "pointers"], [430, 14, 462, 12], [430, 16, 462, 14, "pointers"], [430, 24, 462, 22], [431, 6, 463, 4, "center"], [431, 12, 463, 10], [431, 14, 463, 12, "getCenter"], [431, 23, 463, 21], [431, 24, 463, 22, "pointers"], [431, 32, 463, 30], [431, 33, 463, 31], [432, 6, 464, 4, "deltaX"], [432, 12, 464, 10], [432, 14, 464, 12, "input"], [432, 19, 464, 17], [432, 20, 464, 18, "deltaX"], [432, 26, 464, 24], [433, 6, 465, 4, "deltaY"], [433, 12, 465, 10], [433, 14, 465, 12, "input"], [433, 19, 465, 17], [433, 20, 465, 18, "deltaY"], [434, 4, 466, 2], [434, 5, 466, 3], [435, 2, 467, 0], [437, 2, 469, 0], [438, 0, 470, 0], [439, 0, 471, 0], [440, 0, 472, 0], [441, 0, 473, 0], [442, 0, 474, 0], [443, 0, 475, 0], [444, 0, 476, 0], [446, 2, 478, 0], [446, 11, 478, 9, "getDistance"], [446, 22, 478, 20, "getDistance"], [446, 23, 478, 21, "p1"], [446, 25, 478, 23], [446, 27, 478, 25, "p2"], [446, 29, 478, 27], [446, 31, 478, 29, "props"], [446, 36, 478, 34], [446, 38, 478, 36], [447, 4, 479, 2], [447, 8, 479, 6], [447, 9, 479, 7, "props"], [447, 14, 479, 12], [447, 16, 479, 14], [448, 6, 480, 4, "props"], [448, 11, 480, 9], [448, 14, 480, 12, "PROPS_XY"], [448, 22, 480, 20], [449, 4, 481, 2], [450, 4, 483, 2], [450, 8, 483, 6, "x"], [450, 9, 483, 7], [450, 12, 483, 10, "p2"], [450, 14, 483, 12], [450, 15, 483, 13, "props"], [450, 20, 483, 18], [450, 21, 483, 19], [450, 22, 483, 20], [450, 23, 483, 21], [450, 24, 483, 22], [450, 27, 483, 25, "p1"], [450, 29, 483, 27], [450, 30, 483, 28, "props"], [450, 35, 483, 33], [450, 36, 483, 34], [450, 37, 483, 35], [450, 38, 483, 36], [450, 39, 483, 37], [451, 4, 484, 2], [451, 8, 484, 6, "y"], [451, 9, 484, 7], [451, 12, 484, 10, "p2"], [451, 14, 484, 12], [451, 15, 484, 13, "props"], [451, 20, 484, 18], [451, 21, 484, 19], [451, 22, 484, 20], [451, 23, 484, 21], [451, 24, 484, 22], [451, 27, 484, 25, "p1"], [451, 29, 484, 27], [451, 30, 484, 28, "props"], [451, 35, 484, 33], [451, 36, 484, 34], [451, 37, 484, 35], [451, 38, 484, 36], [451, 39, 484, 37], [452, 4, 485, 2], [452, 11, 485, 9, "Math"], [452, 15, 485, 13], [452, 16, 485, 14, "sqrt"], [452, 20, 485, 18], [452, 21, 485, 19, "x"], [452, 22, 485, 20], [452, 25, 485, 23, "x"], [452, 26, 485, 24], [452, 29, 485, 27, "y"], [452, 30, 485, 28], [452, 33, 485, 31, "y"], [452, 34, 485, 32], [452, 35, 485, 33], [453, 2, 486, 0], [455, 2, 488, 0], [456, 0, 489, 0], [457, 0, 490, 0], [458, 0, 491, 0], [459, 0, 492, 0], [460, 0, 493, 0], [461, 0, 494, 0], [462, 0, 495, 0], [464, 2, 497, 0], [464, 11, 497, 9, "getAngle"], [464, 19, 497, 17, "getAngle"], [464, 20, 497, 18, "p1"], [464, 22, 497, 20], [464, 24, 497, 22, "p2"], [464, 26, 497, 24], [464, 28, 497, 26, "props"], [464, 33, 497, 31], [464, 35, 497, 33], [465, 4, 498, 2], [465, 8, 498, 6], [465, 9, 498, 7, "props"], [465, 14, 498, 12], [465, 16, 498, 14], [466, 6, 499, 4, "props"], [466, 11, 499, 9], [466, 14, 499, 12, "PROPS_XY"], [466, 22, 499, 20], [467, 4, 500, 2], [468, 4, 502, 2], [468, 8, 502, 6, "x"], [468, 9, 502, 7], [468, 12, 502, 10, "p2"], [468, 14, 502, 12], [468, 15, 502, 13, "props"], [468, 20, 502, 18], [468, 21, 502, 19], [468, 22, 502, 20], [468, 23, 502, 21], [468, 24, 502, 22], [468, 27, 502, 25, "p1"], [468, 29, 502, 27], [468, 30, 502, 28, "props"], [468, 35, 502, 33], [468, 36, 502, 34], [468, 37, 502, 35], [468, 38, 502, 36], [468, 39, 502, 37], [469, 4, 503, 2], [469, 8, 503, 6, "y"], [469, 9, 503, 7], [469, 12, 503, 10, "p2"], [469, 14, 503, 12], [469, 15, 503, 13, "props"], [469, 20, 503, 18], [469, 21, 503, 19], [469, 22, 503, 20], [469, 23, 503, 21], [469, 24, 503, 22], [469, 27, 503, 25, "p1"], [469, 29, 503, 27], [469, 30, 503, 28, "props"], [469, 35, 503, 33], [469, 36, 503, 34], [469, 37, 503, 35], [469, 38, 503, 36], [469, 39, 503, 37], [470, 4, 504, 2], [470, 11, 504, 9, "Math"], [470, 15, 504, 13], [470, 16, 504, 14, "atan2"], [470, 21, 504, 19], [470, 22, 504, 20, "y"], [470, 23, 504, 21], [470, 25, 504, 23, "x"], [470, 26, 504, 24], [470, 27, 504, 25], [470, 30, 504, 28], [470, 33, 504, 31], [470, 36, 504, 34, "Math"], [470, 40, 504, 38], [470, 41, 504, 39, "PI"], [470, 43, 504, 41], [471, 2, 505, 0], [473, 2, 507, 0], [474, 0, 508, 0], [475, 0, 509, 0], [476, 0, 510, 0], [477, 0, 511, 0], [478, 0, 512, 0], [479, 0, 513, 0], [481, 2, 515, 0], [481, 11, 515, 9, "getDirection"], [481, 23, 515, 21, "getDirection"], [481, 24, 515, 22, "x"], [481, 25, 515, 23], [481, 27, 515, 25, "y"], [481, 28, 515, 26], [481, 30, 515, 28], [482, 4, 516, 2], [482, 8, 516, 6, "x"], [482, 9, 516, 7], [482, 14, 516, 12, "y"], [482, 15, 516, 13], [482, 17, 516, 15], [483, 6, 517, 4], [483, 13, 517, 11, "DIRECTION_NONE"], [483, 27, 517, 25], [484, 4, 518, 2], [485, 4, 520, 2], [485, 8, 520, 6, "abs"], [485, 11, 520, 9], [485, 12, 520, 10, "x"], [485, 13, 520, 11], [485, 14, 520, 12], [485, 18, 520, 16, "abs"], [485, 21, 520, 19], [485, 22, 520, 20, "y"], [485, 23, 520, 21], [485, 24, 520, 22], [485, 26, 520, 24], [486, 6, 521, 4], [486, 13, 521, 11, "x"], [486, 14, 521, 12], [486, 17, 521, 15], [486, 18, 521, 16], [486, 21, 521, 19, "DIRECTION_LEFT"], [486, 35, 521, 33], [486, 38, 521, 36, "DIRECTION_RIGHT"], [486, 53, 521, 51], [487, 4, 522, 2], [488, 4, 524, 2], [488, 11, 524, 9, "y"], [488, 12, 524, 10], [488, 15, 524, 13], [488, 16, 524, 14], [488, 19, 524, 17, "DIRECTION_UP"], [488, 31, 524, 29], [488, 34, 524, 32, "DIRECTION_DOWN"], [488, 48, 524, 46], [489, 2, 525, 0], [490, 2, 527, 0], [490, 11, 527, 9, "computeDeltaXY"], [490, 25, 527, 23, "computeDeltaXY"], [490, 26, 527, 24, "session"], [490, 33, 527, 31], [490, 35, 527, 33, "input"], [490, 40, 527, 38], [490, 42, 527, 40], [491, 4, 528, 2], [491, 8, 528, 6, "center"], [491, 14, 528, 12], [491, 17, 528, 15, "input"], [491, 22, 528, 20], [491, 23, 528, 21, "center"], [491, 29, 528, 27], [491, 30, 528, 28], [491, 31, 528, 29], [492, 4, 529, 2], [494, 4, 531, 2], [494, 8, 531, 6, "offset"], [494, 14, 531, 12], [494, 17, 531, 15, "session"], [494, 24, 531, 22], [494, 25, 531, 23, "offsetDelta"], [494, 36, 531, 34], [494, 40, 531, 38], [494, 41, 531, 39], [494, 42, 531, 40], [495, 4, 532, 2], [495, 8, 532, 6, "prevDel<PERSON>"], [495, 17, 532, 15], [495, 20, 532, 18, "session"], [495, 27, 532, 25], [495, 28, 532, 26, "prevDel<PERSON>"], [495, 37, 532, 35], [495, 41, 532, 39], [495, 42, 532, 40], [495, 43, 532, 41], [496, 4, 533, 2], [496, 8, 533, 6, "prevInput"], [496, 17, 533, 15], [496, 20, 533, 18, "session"], [496, 27, 533, 25], [496, 28, 533, 26, "prevInput"], [496, 37, 533, 35], [496, 41, 533, 39], [496, 42, 533, 40], [496, 43, 533, 41], [497, 4, 535, 2], [497, 8, 535, 6, "input"], [497, 13, 535, 11], [497, 14, 535, 12, "eventType"], [497, 23, 535, 21], [497, 28, 535, 26, "INPUT_START"], [497, 39, 535, 37], [497, 43, 535, 41, "prevInput"], [497, 52, 535, 50], [497, 53, 535, 51, "eventType"], [497, 62, 535, 60], [497, 67, 535, 65, "INPUT_END"], [497, 76, 535, 74], [497, 78, 535, 76], [498, 6, 536, 4, "prevDel<PERSON>"], [498, 15, 536, 13], [498, 18, 536, 16, "session"], [498, 25, 536, 23], [498, 26, 536, 24, "prevDel<PERSON>"], [498, 35, 536, 33], [498, 38, 536, 36], [499, 8, 537, 6, "x"], [499, 9, 537, 7], [499, 11, 537, 9, "prevInput"], [499, 20, 537, 18], [499, 21, 537, 19, "deltaX"], [499, 27, 537, 25], [499, 31, 537, 29], [499, 32, 537, 30], [500, 8, 538, 6, "y"], [500, 9, 538, 7], [500, 11, 538, 9, "prevInput"], [500, 20, 538, 18], [500, 21, 538, 19, "deltaY"], [500, 27, 538, 25], [500, 31, 538, 29], [501, 6, 539, 4], [501, 7, 539, 5], [502, 6, 540, 4, "offset"], [502, 12, 540, 10], [502, 15, 540, 13, "session"], [502, 22, 540, 20], [502, 23, 540, 21, "offsetDelta"], [502, 34, 540, 32], [502, 37, 540, 35], [503, 8, 541, 6, "x"], [503, 9, 541, 7], [503, 11, 541, 9, "center"], [503, 17, 541, 15], [503, 18, 541, 16, "x"], [503, 19, 541, 17], [504, 8, 542, 6, "y"], [504, 9, 542, 7], [504, 11, 542, 9, "center"], [504, 17, 542, 15], [504, 18, 542, 16, "y"], [505, 6, 543, 4], [505, 7, 543, 5], [506, 4, 544, 2], [507, 4, 546, 2, "input"], [507, 9, 546, 7], [507, 10, 546, 8, "deltaX"], [507, 16, 546, 14], [507, 19, 546, 17, "prevDel<PERSON>"], [507, 28, 546, 26], [507, 29, 546, 27, "x"], [507, 30, 546, 28], [507, 34, 546, 32, "center"], [507, 40, 546, 38], [507, 41, 546, 39, "x"], [507, 42, 546, 40], [507, 45, 546, 43, "offset"], [507, 51, 546, 49], [507, 52, 546, 50, "x"], [507, 53, 546, 51], [507, 54, 546, 52], [508, 4, 547, 2, "input"], [508, 9, 547, 7], [508, 10, 547, 8, "deltaY"], [508, 16, 547, 14], [508, 19, 547, 17, "prevDel<PERSON>"], [508, 28, 547, 26], [508, 29, 547, 27, "y"], [508, 30, 547, 28], [508, 34, 547, 32, "center"], [508, 40, 547, 38], [508, 41, 547, 39, "y"], [508, 42, 547, 40], [508, 45, 547, 43, "offset"], [508, 51, 547, 49], [508, 52, 547, 50, "y"], [508, 53, 547, 51], [508, 54, 547, 52], [509, 2, 548, 0], [511, 2, 550, 0], [512, 0, 551, 0], [513, 0, 552, 0], [514, 0, 553, 0], [515, 0, 554, 0], [516, 0, 555, 0], [517, 0, 556, 0], [518, 0, 557, 0], [519, 2, 558, 0], [519, 11, 558, 9, "getVelocity"], [519, 22, 558, 20, "getVelocity"], [519, 23, 558, 21, "deltaTime"], [519, 32, 558, 30], [519, 34, 558, 32, "x"], [519, 35, 558, 33], [519, 37, 558, 35, "y"], [519, 38, 558, 36], [519, 40, 558, 38], [520, 4, 559, 2], [520, 11, 559, 9], [521, 6, 560, 4, "x"], [521, 7, 560, 5], [521, 9, 560, 7, "x"], [521, 10, 560, 8], [521, 13, 560, 11, "deltaTime"], [521, 22, 560, 20], [521, 26, 560, 24], [521, 27, 560, 25], [522, 6, 561, 4, "y"], [522, 7, 561, 5], [522, 9, 561, 7, "y"], [522, 10, 561, 8], [522, 13, 561, 11, "deltaTime"], [522, 22, 561, 20], [522, 26, 561, 24], [523, 4, 562, 2], [523, 5, 562, 3], [524, 2, 563, 0], [526, 2, 565, 0], [527, 0, 566, 0], [528, 0, 567, 0], [529, 0, 568, 0], [530, 0, 569, 0], [531, 0, 570, 0], [532, 0, 571, 0], [533, 0, 572, 0], [535, 2, 574, 0], [535, 11, 574, 9, "getScale"], [535, 19, 574, 17, "getScale"], [535, 20, 574, 18, "start"], [535, 25, 574, 23], [535, 27, 574, 25, "end"], [535, 30, 574, 28], [535, 32, 574, 30], [536, 4, 575, 2], [536, 11, 575, 9, "getDistance"], [536, 22, 575, 20], [536, 23, 575, 21, "end"], [536, 26, 575, 24], [536, 27, 575, 25], [536, 28, 575, 26], [536, 29, 575, 27], [536, 31, 575, 29, "end"], [536, 34, 575, 32], [536, 35, 575, 33], [536, 36, 575, 34], [536, 37, 575, 35], [536, 39, 575, 37, "PROPS_CLIENT_XY"], [536, 54, 575, 52], [536, 55, 575, 53], [536, 58, 575, 56, "getDistance"], [536, 69, 575, 67], [536, 70, 575, 68, "start"], [536, 75, 575, 73], [536, 76, 575, 74], [536, 77, 575, 75], [536, 78, 575, 76], [536, 80, 575, 78, "start"], [536, 85, 575, 83], [536, 86, 575, 84], [536, 87, 575, 85], [536, 88, 575, 86], [536, 90, 575, 88, "PROPS_CLIENT_XY"], [536, 105, 575, 103], [536, 106, 575, 104], [537, 2, 576, 0], [539, 2, 578, 0], [540, 0, 579, 0], [541, 0, 580, 0], [542, 0, 581, 0], [543, 0, 582, 0], [544, 0, 583, 0], [545, 0, 584, 0], [547, 2, 586, 0], [547, 11, 586, 9, "getRotation"], [547, 22, 586, 20, "getRotation"], [547, 23, 586, 21, "start"], [547, 28, 586, 26], [547, 30, 586, 28, "end"], [547, 33, 586, 31], [547, 35, 586, 33], [548, 4, 587, 2], [548, 11, 587, 9, "getAngle"], [548, 19, 587, 17], [548, 20, 587, 18, "end"], [548, 23, 587, 21], [548, 24, 587, 22], [548, 25, 587, 23], [548, 26, 587, 24], [548, 28, 587, 26, "end"], [548, 31, 587, 29], [548, 32, 587, 30], [548, 33, 587, 31], [548, 34, 587, 32], [548, 36, 587, 34, "PROPS_CLIENT_XY"], [548, 51, 587, 49], [548, 52, 587, 50], [548, 55, 587, 53, "getAngle"], [548, 63, 587, 61], [548, 64, 587, 62, "start"], [548, 69, 587, 67], [548, 70, 587, 68], [548, 71, 587, 69], [548, 72, 587, 70], [548, 74, 587, 72, "start"], [548, 79, 587, 77], [548, 80, 587, 78], [548, 81, 587, 79], [548, 82, 587, 80], [548, 84, 587, 82, "PROPS_CLIENT_XY"], [548, 99, 587, 97], [548, 100, 587, 98], [549, 2, 588, 0], [551, 2, 590, 0], [552, 0, 591, 0], [553, 0, 592, 0], [554, 0, 593, 0], [555, 0, 594, 0], [556, 0, 595, 0], [558, 2, 597, 0], [558, 11, 597, 9, "computeIntervalInputData"], [558, 35, 597, 33, "computeIntervalInputData"], [558, 36, 597, 34, "session"], [558, 43, 597, 41], [558, 45, 597, 43, "input"], [558, 50, 597, 48], [558, 52, 597, 50], [559, 4, 598, 2], [559, 8, 598, 6, "last"], [559, 12, 598, 10], [559, 15, 598, 13, "session"], [559, 22, 598, 20], [559, 23, 598, 21, "lastInterval"], [559, 35, 598, 33], [559, 39, 598, 37, "input"], [559, 44, 598, 42], [560, 4, 599, 2], [560, 8, 599, 6, "deltaTime"], [560, 17, 599, 15], [560, 20, 599, 18, "input"], [560, 25, 599, 23], [560, 26, 599, 24, "timeStamp"], [560, 35, 599, 33], [560, 38, 599, 36, "last"], [560, 42, 599, 40], [560, 43, 599, 41, "timeStamp"], [560, 52, 599, 50], [561, 4, 600, 2], [561, 8, 600, 6, "velocity"], [561, 16, 600, 14], [562, 4, 601, 2], [562, 8, 601, 6, "velocityX"], [562, 17, 601, 15], [563, 4, 602, 2], [563, 8, 602, 6, "velocityY"], [563, 17, 602, 15], [564, 4, 603, 2], [564, 8, 603, 6, "direction"], [564, 17, 603, 15], [565, 4, 605, 2], [565, 8, 605, 6, "input"], [565, 13, 605, 11], [565, 14, 605, 12, "eventType"], [565, 23, 605, 21], [565, 28, 605, 26, "INPUT_CANCEL"], [565, 40, 605, 38], [565, 45, 605, 43, "deltaTime"], [565, 54, 605, 52], [565, 57, 605, 55, "COMPUTE_INTERVAL"], [565, 73, 605, 71], [565, 77, 605, 75, "last"], [565, 81, 605, 79], [565, 82, 605, 80, "velocity"], [565, 90, 605, 88], [565, 95, 605, 93, "undefined"], [565, 104, 605, 102], [565, 105, 605, 103], [565, 107, 605, 105], [566, 6, 606, 4], [566, 10, 606, 8, "deltaX"], [566, 16, 606, 14], [566, 19, 606, 17, "input"], [566, 24, 606, 22], [566, 25, 606, 23, "deltaX"], [566, 31, 606, 29], [566, 34, 606, 32, "last"], [566, 38, 606, 36], [566, 39, 606, 37, "deltaX"], [566, 45, 606, 43], [567, 6, 607, 4], [567, 10, 607, 8, "deltaY"], [567, 16, 607, 14], [567, 19, 607, 17, "input"], [567, 24, 607, 22], [567, 25, 607, 23, "deltaY"], [567, 31, 607, 29], [567, 34, 607, 32, "last"], [567, 38, 607, 36], [567, 39, 607, 37, "deltaY"], [567, 45, 607, 43], [568, 6, 608, 4], [568, 10, 608, 8, "v"], [568, 11, 608, 9], [568, 14, 608, 12, "getVelocity"], [568, 25, 608, 23], [568, 26, 608, 24, "deltaTime"], [568, 35, 608, 33], [568, 37, 608, 35, "deltaX"], [568, 43, 608, 41], [568, 45, 608, 43, "deltaY"], [568, 51, 608, 49], [568, 52, 608, 50], [569, 6, 609, 4, "velocityX"], [569, 15, 609, 13], [569, 18, 609, 16, "v"], [569, 19, 609, 17], [569, 20, 609, 18, "x"], [569, 21, 609, 19], [570, 6, 610, 4, "velocityY"], [570, 15, 610, 13], [570, 18, 610, 16, "v"], [570, 19, 610, 17], [570, 20, 610, 18, "y"], [570, 21, 610, 19], [571, 6, 611, 4, "velocity"], [571, 14, 611, 12], [571, 17, 611, 15, "abs"], [571, 20, 611, 18], [571, 21, 611, 19, "v"], [571, 22, 611, 20], [571, 23, 611, 21, "x"], [571, 24, 611, 22], [571, 25, 611, 23], [571, 28, 611, 26, "abs"], [571, 31, 611, 29], [571, 32, 611, 30, "v"], [571, 33, 611, 31], [571, 34, 611, 32, "y"], [571, 35, 611, 33], [571, 36, 611, 34], [571, 39, 611, 37, "v"], [571, 40, 611, 38], [571, 41, 611, 39, "x"], [571, 42, 611, 40], [571, 45, 611, 43, "v"], [571, 46, 611, 44], [571, 47, 611, 45, "y"], [571, 48, 611, 46], [572, 6, 612, 4, "direction"], [572, 15, 612, 13], [572, 18, 612, 16, "getDirection"], [572, 30, 612, 28], [572, 31, 612, 29, "deltaX"], [572, 37, 612, 35], [572, 39, 612, 37, "deltaY"], [572, 45, 612, 43], [572, 46, 612, 44], [573, 6, 613, 4, "session"], [573, 13, 613, 11], [573, 14, 613, 12, "lastInterval"], [573, 26, 613, 24], [573, 29, 613, 27, "input"], [573, 34, 613, 32], [574, 4, 614, 2], [574, 5, 614, 3], [574, 11, 614, 9], [575, 6, 615, 4], [576, 6, 616, 4, "velocity"], [576, 14, 616, 12], [576, 17, 616, 15, "last"], [576, 21, 616, 19], [576, 22, 616, 20, "velocity"], [576, 30, 616, 28], [577, 6, 617, 4, "velocityX"], [577, 15, 617, 13], [577, 18, 617, 16, "last"], [577, 22, 617, 20], [577, 23, 617, 21, "velocityX"], [577, 32, 617, 30], [578, 6, 618, 4, "velocityY"], [578, 15, 618, 13], [578, 18, 618, 16, "last"], [578, 22, 618, 20], [578, 23, 618, 21, "velocityY"], [578, 32, 618, 30], [579, 6, 619, 4, "direction"], [579, 15, 619, 13], [579, 18, 619, 16, "last"], [579, 22, 619, 20], [579, 23, 619, 21, "direction"], [579, 32, 619, 30], [580, 4, 620, 2], [581, 4, 622, 2, "input"], [581, 9, 622, 7], [581, 10, 622, 8, "velocity"], [581, 18, 622, 16], [581, 21, 622, 19, "velocity"], [581, 29, 622, 27], [582, 4, 623, 2, "input"], [582, 9, 623, 7], [582, 10, 623, 8, "velocityX"], [582, 19, 623, 17], [582, 22, 623, 20, "velocityX"], [582, 31, 623, 29], [583, 4, 624, 2, "input"], [583, 9, 624, 7], [583, 10, 624, 8, "velocityY"], [583, 19, 624, 17], [583, 22, 624, 20, "velocityY"], [583, 31, 624, 29], [584, 4, 625, 2, "input"], [584, 9, 625, 7], [584, 10, 625, 8, "direction"], [584, 19, 625, 17], [584, 22, 625, 20, "direction"], [584, 31, 625, 29], [585, 2, 626, 0], [587, 2, 628, 0], [588, 0, 629, 0], [589, 0, 630, 0], [590, 0, 631, 0], [591, 0, 632, 0], [592, 0, 633, 0], [594, 2, 635, 0], [594, 11, 635, 9, "computeInputData"], [594, 27, 635, 25, "computeInputData"], [594, 28, 635, 26, "manager"], [594, 35, 635, 33], [594, 37, 635, 35, "input"], [594, 42, 635, 40], [594, 44, 635, 42], [595, 4, 636, 2], [595, 8, 636, 6, "session"], [595, 15, 636, 13], [595, 18, 636, 16, "manager"], [595, 25, 636, 23], [595, 26, 636, 24, "session"], [595, 33, 636, 31], [596, 4, 637, 2], [596, 8, 637, 6, "pointers"], [596, 16, 637, 14], [596, 19, 637, 17, "input"], [596, 24, 637, 22], [596, 25, 637, 23, "pointers"], [596, 33, 637, 31], [597, 4, 638, 2], [597, 8, 638, 6, "pointers<PERSON><PERSON><PERSON>"], [597, 22, 638, 20], [597, 25, 638, 23, "pointers"], [597, 33, 638, 31], [597, 34, 638, 32, "length"], [597, 40, 638, 38], [597, 41, 638, 39], [597, 42, 638, 40], [599, 4, 640, 2], [599, 8, 640, 6], [599, 9, 640, 7, "session"], [599, 16, 640, 14], [599, 17, 640, 15, "firstInput"], [599, 27, 640, 25], [599, 29, 640, 27], [600, 6, 641, 4, "session"], [600, 13, 641, 11], [600, 14, 641, 12, "firstInput"], [600, 24, 641, 22], [600, 27, 641, 25, "simpleCloneInputData"], [600, 47, 641, 45], [600, 48, 641, 46, "input"], [600, 53, 641, 51], [600, 54, 641, 52], [601, 4, 642, 2], [601, 5, 642, 3], [601, 6, 642, 4], [603, 4, 645, 2], [603, 8, 645, 6, "pointers<PERSON><PERSON><PERSON>"], [603, 22, 645, 20], [603, 25, 645, 23], [603, 26, 645, 24], [603, 30, 645, 28], [603, 31, 645, 29, "session"], [603, 38, 645, 36], [603, 39, 645, 37, "firstMultiple"], [603, 52, 645, 50], [603, 54, 645, 52], [604, 6, 646, 4, "session"], [604, 13, 646, 11], [604, 14, 646, 12, "firstMultiple"], [604, 27, 646, 25], [604, 30, 646, 28, "simpleCloneInputData"], [604, 50, 646, 48], [604, 51, 646, 49, "input"], [604, 56, 646, 54], [604, 57, 646, 55], [605, 4, 647, 2], [605, 5, 647, 3], [605, 11, 647, 9], [605, 15, 647, 13, "pointers<PERSON><PERSON><PERSON>"], [605, 29, 647, 27], [605, 34, 647, 32], [605, 35, 647, 33], [605, 37, 647, 35], [606, 6, 648, 4, "session"], [606, 13, 648, 11], [606, 14, 648, 12, "firstMultiple"], [606, 27, 648, 25], [606, 30, 648, 28], [606, 35, 648, 33], [607, 4, 649, 2], [608, 4, 651, 2], [608, 8, 651, 6, "firstInput"], [608, 18, 651, 16], [608, 21, 651, 19, "session"], [608, 28, 651, 26], [608, 29, 651, 27, "firstInput"], [608, 39, 651, 37], [609, 6, 652, 6, "firstMultiple"], [609, 19, 652, 19], [609, 22, 652, 22, "session"], [609, 29, 652, 29], [609, 30, 652, 30, "firstMultiple"], [609, 43, 652, 43], [610, 4, 653, 2], [610, 8, 653, 6, "offsetCenter"], [610, 20, 653, 18], [610, 23, 653, 21, "firstMultiple"], [610, 36, 653, 34], [610, 39, 653, 37, "firstMultiple"], [610, 52, 653, 50], [610, 53, 653, 51, "center"], [610, 59, 653, 57], [610, 62, 653, 60, "firstInput"], [610, 72, 653, 70], [610, 73, 653, 71, "center"], [610, 79, 653, 77], [611, 4, 654, 2], [611, 8, 654, 6, "center"], [611, 14, 654, 12], [611, 17, 654, 15, "input"], [611, 22, 654, 20], [611, 23, 654, 21, "center"], [611, 29, 654, 27], [611, 32, 654, 30, "getCenter"], [611, 41, 654, 39], [611, 42, 654, 40, "pointers"], [611, 50, 654, 48], [611, 51, 654, 49], [612, 4, 655, 2, "input"], [612, 9, 655, 7], [612, 10, 655, 8, "timeStamp"], [612, 19, 655, 17], [612, 22, 655, 20, "now"], [612, 25, 655, 23], [612, 26, 655, 24], [612, 27, 655, 25], [613, 4, 656, 2, "input"], [613, 9, 656, 7], [613, 10, 656, 8, "deltaTime"], [613, 19, 656, 17], [613, 22, 656, 20, "input"], [613, 27, 656, 25], [613, 28, 656, 26, "timeStamp"], [613, 37, 656, 35], [613, 40, 656, 38, "firstInput"], [613, 50, 656, 48], [613, 51, 656, 49, "timeStamp"], [613, 60, 656, 58], [614, 4, 657, 2, "input"], [614, 9, 657, 7], [614, 10, 657, 8, "angle"], [614, 15, 657, 13], [614, 18, 657, 16, "getAngle"], [614, 26, 657, 24], [614, 27, 657, 25, "offsetCenter"], [614, 39, 657, 37], [614, 41, 657, 39, "center"], [614, 47, 657, 45], [614, 48, 657, 46], [615, 4, 658, 2, "input"], [615, 9, 658, 7], [615, 10, 658, 8, "distance"], [615, 18, 658, 16], [615, 21, 658, 19, "getDistance"], [615, 32, 658, 30], [615, 33, 658, 31, "offsetCenter"], [615, 45, 658, 43], [615, 47, 658, 45, "center"], [615, 53, 658, 51], [615, 54, 658, 52], [616, 4, 659, 2, "computeDeltaXY"], [616, 18, 659, 16], [616, 19, 659, 17, "session"], [616, 26, 659, 24], [616, 28, 659, 26, "input"], [616, 33, 659, 31], [616, 34, 659, 32], [617, 4, 660, 2, "input"], [617, 9, 660, 7], [617, 10, 660, 8, "offsetDirection"], [617, 25, 660, 23], [617, 28, 660, 26, "getDirection"], [617, 40, 660, 38], [617, 41, 660, 39, "input"], [617, 46, 660, 44], [617, 47, 660, 45, "deltaX"], [617, 53, 660, 51], [617, 55, 660, 53, "input"], [617, 60, 660, 58], [617, 61, 660, 59, "deltaY"], [617, 67, 660, 65], [617, 68, 660, 66], [618, 4, 661, 2], [618, 8, 661, 6, "overallVelocity"], [618, 23, 661, 21], [618, 26, 661, 24, "getVelocity"], [618, 37, 661, 35], [618, 38, 661, 36, "input"], [618, 43, 661, 41], [618, 44, 661, 42, "deltaTime"], [618, 53, 661, 51], [618, 55, 661, 53, "input"], [618, 60, 661, 58], [618, 61, 661, 59, "deltaX"], [618, 67, 661, 65], [618, 69, 661, 67, "input"], [618, 74, 661, 72], [618, 75, 661, 73, "deltaY"], [618, 81, 661, 79], [618, 82, 661, 80], [619, 4, 662, 2, "input"], [619, 9, 662, 7], [619, 10, 662, 8, "overallVelocityX"], [619, 26, 662, 24], [619, 29, 662, 27, "overallVelocity"], [619, 44, 662, 42], [619, 45, 662, 43, "x"], [619, 46, 662, 44], [620, 4, 663, 2, "input"], [620, 9, 663, 7], [620, 10, 663, 8, "overallVelocityY"], [620, 26, 663, 24], [620, 29, 663, 27, "overallVelocity"], [620, 44, 663, 42], [620, 45, 663, 43, "y"], [620, 46, 663, 44], [621, 4, 664, 2, "input"], [621, 9, 664, 7], [621, 10, 664, 8, "overallVelocity"], [621, 25, 664, 23], [621, 28, 664, 26, "abs"], [621, 31, 664, 29], [621, 32, 664, 30, "overallVelocity"], [621, 47, 664, 45], [621, 48, 664, 46, "x"], [621, 49, 664, 47], [621, 50, 664, 48], [621, 53, 664, 51, "abs"], [621, 56, 664, 54], [621, 57, 664, 55, "overallVelocity"], [621, 72, 664, 70], [621, 73, 664, 71, "y"], [621, 74, 664, 72], [621, 75, 664, 73], [621, 78, 664, 76, "overallVelocity"], [621, 93, 664, 91], [621, 94, 664, 92, "x"], [621, 95, 664, 93], [621, 98, 664, 96, "overallVelocity"], [621, 113, 664, 111], [621, 114, 664, 112, "y"], [621, 115, 664, 113], [622, 4, 665, 2, "input"], [622, 9, 665, 7], [622, 10, 665, 8, "scale"], [622, 15, 665, 13], [622, 18, 665, 16, "firstMultiple"], [622, 31, 665, 29], [622, 34, 665, 32, "getScale"], [622, 42, 665, 40], [622, 43, 665, 41, "firstMultiple"], [622, 56, 665, 54], [622, 57, 665, 55, "pointers"], [622, 65, 665, 63], [622, 67, 665, 65, "pointers"], [622, 75, 665, 73], [622, 76, 665, 74], [622, 79, 665, 77], [622, 80, 665, 78], [623, 4, 666, 2, "input"], [623, 9, 666, 7], [623, 10, 666, 8, "rotation"], [623, 18, 666, 16], [623, 21, 666, 19, "firstMultiple"], [623, 34, 666, 32], [623, 37, 666, 35, "getRotation"], [623, 48, 666, 46], [623, 49, 666, 47, "firstMultiple"], [623, 62, 666, 60], [623, 63, 666, 61, "pointers"], [623, 71, 666, 69], [623, 73, 666, 71, "pointers"], [623, 81, 666, 79], [623, 82, 666, 80], [623, 85, 666, 83], [623, 86, 666, 84], [624, 4, 667, 2, "input"], [624, 9, 667, 7], [624, 10, 667, 8, "maxPointers"], [624, 21, 667, 19], [624, 24, 667, 22], [624, 25, 667, 23, "session"], [624, 32, 667, 30], [624, 33, 667, 31, "prevInput"], [624, 42, 667, 40], [624, 45, 667, 43, "input"], [624, 50, 667, 48], [624, 51, 667, 49, "pointers"], [624, 59, 667, 57], [624, 60, 667, 58, "length"], [624, 66, 667, 64], [624, 69, 667, 67, "input"], [624, 74, 667, 72], [624, 75, 667, 73, "pointers"], [624, 83, 667, 81], [624, 84, 667, 82, "length"], [624, 90, 667, 88], [624, 93, 667, 91, "session"], [624, 100, 667, 98], [624, 101, 667, 99, "prevInput"], [624, 110, 667, 108], [624, 111, 667, 109, "maxPointers"], [624, 122, 667, 120], [624, 125, 667, 123, "input"], [624, 130, 667, 128], [624, 131, 667, 129, "pointers"], [624, 139, 667, 137], [624, 140, 667, 138, "length"], [624, 146, 667, 144], [624, 149, 667, 147, "session"], [624, 156, 667, 154], [624, 157, 667, 155, "prevInput"], [624, 166, 667, 164], [624, 167, 667, 165, "maxPointers"], [624, 178, 667, 176], [625, 4, 668, 2, "computeIntervalInputData"], [625, 28, 668, 26], [625, 29, 668, 27, "session"], [625, 36, 668, 34], [625, 38, 668, 36, "input"], [625, 43, 668, 41], [625, 44, 668, 42], [625, 45, 668, 43], [625, 46, 668, 44], [627, 4, 670, 2], [627, 8, 670, 6, "target"], [627, 14, 670, 12], [627, 17, 670, 15, "manager"], [627, 24, 670, 22], [627, 25, 670, 23, "element"], [627, 32, 670, 30], [628, 4, 671, 2], [628, 8, 671, 6, "srcEvent"], [628, 16, 671, 14], [628, 19, 671, 17, "input"], [628, 24, 671, 22], [628, 25, 671, 23, "srcEvent"], [628, 33, 671, 31], [629, 4, 672, 2], [629, 8, 672, 6, "srcEventTarget"], [629, 22, 672, 20], [630, 4, 674, 2], [630, 8, 674, 6, "srcEvent"], [630, 16, 674, 14], [630, 17, 674, 15, "<PERSON><PERSON><PERSON>"], [630, 29, 674, 27], [630, 31, 674, 29], [631, 6, 675, 4, "srcEventTarget"], [631, 20, 675, 18], [631, 23, 675, 21, "srcEvent"], [631, 31, 675, 29], [631, 32, 675, 30, "<PERSON><PERSON><PERSON>"], [631, 44, 675, 42], [631, 45, 675, 43], [631, 46, 675, 44], [631, 47, 675, 45], [631, 48, 675, 46], [631, 49, 675, 47], [632, 4, 676, 2], [632, 5, 676, 3], [632, 11, 676, 9], [632, 15, 676, 13, "srcEvent"], [632, 23, 676, 21], [632, 24, 676, 22, "path"], [632, 28, 676, 26], [632, 30, 676, 28], [633, 6, 677, 4, "srcEventTarget"], [633, 20, 677, 18], [633, 23, 677, 21, "srcEvent"], [633, 31, 677, 29], [633, 32, 677, 30, "path"], [633, 36, 677, 34], [633, 37, 677, 35], [633, 38, 677, 36], [633, 39, 677, 37], [634, 4, 678, 2], [634, 5, 678, 3], [634, 11, 678, 9], [635, 6, 679, 4, "srcEventTarget"], [635, 20, 679, 18], [635, 23, 679, 21, "srcEvent"], [635, 31, 679, 29], [635, 32, 679, 30, "target"], [635, 38, 679, 36], [636, 4, 680, 2], [637, 4, 682, 2], [637, 8, 682, 6, "hasParent"], [637, 17, 682, 15], [637, 18, 682, 16, "srcEventTarget"], [637, 32, 682, 30], [637, 34, 682, 32, "target"], [637, 40, 682, 38], [637, 41, 682, 39], [637, 43, 682, 41], [638, 6, 683, 4, "target"], [638, 12, 683, 10], [638, 15, 683, 13, "srcEventTarget"], [638, 29, 683, 27], [639, 4, 684, 2], [640, 4, 686, 2, "input"], [640, 9, 686, 7], [640, 10, 686, 8, "target"], [640, 16, 686, 14], [640, 19, 686, 17, "target"], [640, 25, 686, 23], [641, 2, 687, 0], [643, 2, 689, 0], [644, 0, 690, 0], [645, 0, 691, 0], [646, 0, 692, 0], [647, 0, 693, 0], [648, 0, 694, 0], [649, 0, 695, 0], [651, 2, 697, 0], [651, 11, 697, 9, "inputHandler"], [651, 23, 697, 21, "inputHandler"], [651, 24, 697, 22, "manager"], [651, 31, 697, 29], [651, 33, 697, 31, "eventType"], [651, 42, 697, 40], [651, 44, 697, 42, "input"], [651, 49, 697, 47], [651, 51, 697, 49], [652, 4, 698, 2], [652, 8, 698, 6, "pointersLen"], [652, 19, 698, 17], [652, 22, 698, 20, "input"], [652, 27, 698, 25], [652, 28, 698, 26, "pointers"], [652, 36, 698, 34], [652, 37, 698, 35, "length"], [652, 43, 698, 41], [653, 4, 699, 2], [653, 8, 699, 6, "changedPointersLen"], [653, 26, 699, 24], [653, 29, 699, 27, "input"], [653, 34, 699, 32], [653, 35, 699, 33, "changedPointers"], [653, 50, 699, 48], [653, 51, 699, 49, "length"], [653, 57, 699, 55], [654, 4, 700, 2], [654, 8, 700, 6, "<PERSON><PERSON><PERSON><PERSON>"], [654, 15, 700, 13], [654, 18, 700, 16, "eventType"], [654, 27, 700, 25], [654, 30, 700, 28, "INPUT_START"], [654, 41, 700, 39], [654, 45, 700, 43, "pointersLen"], [654, 56, 700, 54], [654, 59, 700, 57, "changedPointersLen"], [654, 77, 700, 75], [654, 82, 700, 80], [654, 83, 700, 81], [655, 4, 701, 2], [655, 8, 701, 6, "isFinal"], [655, 15, 701, 13], [655, 18, 701, 16, "eventType"], [655, 27, 701, 25], [655, 31, 701, 29, "INPUT_END"], [655, 40, 701, 38], [655, 43, 701, 41, "INPUT_CANCEL"], [655, 55, 701, 53], [655, 56, 701, 54], [655, 60, 701, 58, "pointersLen"], [655, 71, 701, 69], [655, 74, 701, 72, "changedPointersLen"], [655, 92, 701, 90], [655, 97, 701, 95], [655, 98, 701, 96], [656, 4, 702, 2, "input"], [656, 9, 702, 7], [656, 10, 702, 8, "<PERSON><PERSON><PERSON><PERSON>"], [656, 17, 702, 15], [656, 20, 702, 18], [656, 21, 702, 19], [656, 22, 702, 20, "<PERSON><PERSON><PERSON><PERSON>"], [656, 29, 702, 27], [657, 4, 703, 2, "input"], [657, 9, 703, 7], [657, 10, 703, 8, "isFinal"], [657, 17, 703, 15], [657, 20, 703, 18], [657, 21, 703, 19], [657, 22, 703, 20, "isFinal"], [657, 29, 703, 27], [658, 4, 705, 2], [658, 8, 705, 6, "<PERSON><PERSON><PERSON><PERSON>"], [658, 15, 705, 13], [658, 17, 705, 15], [659, 6, 706, 4, "manager"], [659, 13, 706, 11], [659, 14, 706, 12, "session"], [659, 21, 706, 19], [659, 24, 706, 22], [659, 25, 706, 23], [659, 26, 706, 24], [660, 4, 707, 2], [660, 5, 707, 3], [660, 6, 707, 4], [661, 4, 708, 2], [663, 4, 711, 2, "input"], [663, 9, 711, 7], [663, 10, 711, 8, "eventType"], [663, 19, 711, 17], [663, 22, 711, 20, "eventType"], [663, 31, 711, 29], [663, 32, 711, 30], [663, 33, 711, 31], [665, 4, 713, 2, "computeInputData"], [665, 20, 713, 18], [665, 21, 713, 19, "manager"], [665, 28, 713, 26], [665, 30, 713, 28, "input"], [665, 35, 713, 33], [665, 36, 713, 34], [665, 37, 713, 35], [665, 38, 713, 36], [667, 4, 715, 2, "manager"], [667, 11, 715, 9], [667, 12, 715, 10, "emit"], [667, 16, 715, 14], [667, 17, 715, 15], [667, 31, 715, 29], [667, 33, 715, 31, "input"], [667, 38, 715, 36], [667, 39, 715, 37], [668, 4, 716, 2, "manager"], [668, 11, 716, 9], [668, 12, 716, 10, "recognize"], [668, 21, 716, 19], [668, 22, 716, 20, "input"], [668, 27, 716, 25], [668, 28, 716, 26], [669, 4, 717, 2, "manager"], [669, 11, 717, 9], [669, 12, 717, 10, "session"], [669, 19, 717, 17], [669, 20, 717, 18, "prevInput"], [669, 29, 717, 27], [669, 32, 717, 30, "input"], [669, 37, 717, 35], [670, 2, 718, 0], [672, 2, 720, 0], [673, 0, 721, 0], [674, 0, 722, 0], [675, 0, 723, 0], [676, 0, 724, 0], [677, 0, 725, 0], [678, 2, 726, 0], [678, 11, 726, 9, "splitStr"], [678, 19, 726, 17, "splitStr"], [678, 20, 726, 18, "str"], [678, 23, 726, 21], [678, 25, 726, 23], [679, 4, 727, 2], [679, 11, 727, 9, "str"], [679, 14, 727, 12], [679, 15, 727, 13, "trim"], [679, 19, 727, 17], [679, 20, 727, 18], [679, 21, 727, 19], [679, 22, 727, 20, "split"], [679, 27, 727, 25], [679, 28, 727, 26], [679, 34, 727, 32], [679, 35, 727, 33], [680, 2, 728, 0], [682, 2, 730, 0], [683, 0, 731, 0], [684, 0, 732, 0], [685, 0, 733, 0], [686, 0, 734, 0], [687, 0, 735, 0], [688, 0, 736, 0], [690, 2, 738, 0], [690, 11, 738, 9, "addEventListeners"], [690, 28, 738, 26, "addEventListeners"], [690, 29, 738, 27, "target"], [690, 35, 738, 33], [690, 37, 738, 35, "types"], [690, 42, 738, 40], [690, 44, 738, 42, "handler"], [690, 51, 738, 49], [690, 53, 738, 51], [691, 4, 739, 2, "each"], [691, 8, 739, 6], [691, 9, 739, 7, "splitStr"], [691, 17, 739, 15], [691, 18, 739, 16, "types"], [691, 23, 739, 21], [691, 24, 739, 22], [691, 26, 739, 24], [691, 36, 739, 34, "type"], [691, 40, 739, 38], [691, 42, 739, 40], [692, 6, 740, 4, "target"], [692, 12, 740, 10], [692, 13, 740, 11, "addEventListener"], [692, 29, 740, 27], [692, 30, 740, 28, "type"], [692, 34, 740, 32], [692, 36, 740, 34, "handler"], [692, 43, 740, 41], [692, 45, 740, 43], [692, 50, 740, 48], [692, 51, 740, 49], [693, 4, 741, 2], [693, 5, 741, 3], [693, 6, 741, 4], [694, 2, 742, 0], [696, 2, 744, 0], [697, 0, 745, 0], [698, 0, 746, 0], [699, 0, 747, 0], [700, 0, 748, 0], [701, 0, 749, 0], [702, 0, 750, 0], [704, 2, 752, 0], [704, 11, 752, 9, "removeEventListeners"], [704, 31, 752, 29, "removeEventListeners"], [704, 32, 752, 30, "target"], [704, 38, 752, 36], [704, 40, 752, 38, "types"], [704, 45, 752, 43], [704, 47, 752, 45, "handler"], [704, 54, 752, 52], [704, 56, 752, 54], [705, 4, 753, 2, "each"], [705, 8, 753, 6], [705, 9, 753, 7, "splitStr"], [705, 17, 753, 15], [705, 18, 753, 16, "types"], [705, 23, 753, 21], [705, 24, 753, 22], [705, 26, 753, 24], [705, 36, 753, 34, "type"], [705, 40, 753, 38], [705, 42, 753, 40], [706, 6, 754, 4, "target"], [706, 12, 754, 10], [706, 13, 754, 11, "removeEventListener"], [706, 32, 754, 30], [706, 33, 754, 31, "type"], [706, 37, 754, 35], [706, 39, 754, 37, "handler"], [706, 46, 754, 44], [706, 48, 754, 46], [706, 53, 754, 51], [706, 54, 754, 52], [707, 4, 755, 2], [707, 5, 755, 3], [707, 6, 755, 4], [708, 2, 756, 0], [710, 2, 758, 0], [711, 0, 759, 0], [712, 0, 760, 0], [713, 0, 761, 0], [714, 0, 762, 0], [715, 0, 763, 0], [716, 2, 764, 0], [716, 11, 764, 9, "getWindowForElement"], [716, 30, 764, 28, "getWindowForElement"], [716, 31, 764, 29, "element"], [716, 38, 764, 36], [716, 40, 764, 38], [717, 4, 765, 2], [717, 8, 765, 6, "doc"], [717, 11, 765, 9], [717, 14, 765, 12, "element"], [717, 21, 765, 19], [717, 22, 765, 20, "ownerDocument"], [717, 35, 765, 33], [717, 39, 765, 37, "element"], [717, 46, 765, 44], [718, 4, 766, 2], [718, 11, 766, 9, "doc"], [718, 14, 766, 12], [718, 15, 766, 13, "defaultView"], [718, 26, 766, 24], [718, 30, 766, 28, "doc"], [718, 33, 766, 31], [718, 34, 766, 32, "parentWindow"], [718, 46, 766, 44], [718, 50, 766, 48, "window"], [718, 56, 766, 54], [719, 2, 767, 0], [721, 2, 769, 0], [722, 0, 770, 0], [723, 0, 771, 0], [724, 0, 772, 0], [725, 0, 773, 0], [726, 0, 774, 0], [727, 0, 775, 0], [728, 0, 776, 0], [730, 2, 778, 0], [730, 6, 778, 4, "Input"], [730, 11, 778, 9], [730, 14, 778, 9, "exports"], [730, 21, 778, 9], [730, 22, 778, 9, "Input"], [730, 27, 778, 9], [730, 30, 779, 0], [731, 2, 780, 0], [731, 14, 780, 12], [732, 4, 781, 2], [732, 13, 781, 11, "Input"], [732, 18, 781, 16, "Input"], [732, 19, 781, 17, "manager"], [732, 26, 781, 24], [732, 28, 781, 26, "callback"], [732, 36, 781, 34], [732, 38, 781, 36], [733, 6, 782, 4], [733, 10, 782, 8, "self"], [733, 14, 782, 12], [733, 17, 782, 15], [733, 21, 782, 19], [734, 6, 783, 4], [734, 10, 783, 8], [734, 11, 783, 9, "manager"], [734, 18, 783, 16], [734, 21, 783, 19, "manager"], [734, 28, 783, 26], [735, 6, 784, 4], [735, 10, 784, 8], [735, 11, 784, 9, "callback"], [735, 19, 784, 17], [735, 22, 784, 20, "callback"], [735, 30, 784, 28], [736, 6, 785, 4], [736, 10, 785, 8], [736, 11, 785, 9, "element"], [736, 18, 785, 16], [736, 21, 785, 19, "manager"], [736, 28, 785, 26], [736, 29, 785, 27, "element"], [736, 36, 785, 34], [737, 6, 786, 4], [737, 10, 786, 8], [737, 11, 786, 9, "target"], [737, 17, 786, 15], [737, 20, 786, 18, "manager"], [737, 27, 786, 25], [737, 28, 786, 26, "options"], [737, 35, 786, 33], [737, 36, 786, 34, "inputTarget"], [737, 47, 786, 45], [737, 48, 786, 46], [737, 49, 786, 47], [738, 6, 787, 4], [740, 6, 789, 4], [740, 10, 789, 8], [740, 11, 789, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [740, 21, 789, 19], [740, 24, 789, 22], [740, 34, 789, 32, "ev"], [740, 36, 789, 34], [740, 38, 789, 36], [741, 8, 790, 6], [741, 12, 790, 10, "boolOrFn"], [741, 20, 790, 18], [741, 21, 790, 19, "manager"], [741, 28, 790, 26], [741, 29, 790, 27, "options"], [741, 36, 790, 34], [741, 37, 790, 35, "enable"], [741, 43, 790, 41], [741, 45, 790, 43], [741, 46, 790, 44, "manager"], [741, 53, 790, 51], [741, 54, 790, 52], [741, 55, 790, 53], [741, 57, 790, 55], [742, 10, 791, 8, "self"], [742, 14, 791, 12], [742, 15, 791, 13, "handler"], [742, 22, 791, 20], [742, 23, 791, 21, "ev"], [742, 25, 791, 23], [742, 26, 791, 24], [743, 8, 792, 6], [744, 6, 793, 4], [744, 7, 793, 5], [745, 6, 795, 4], [745, 10, 795, 8], [745, 11, 795, 9, "init"], [745, 15, 795, 13], [745, 16, 795, 14], [745, 17, 795, 15], [746, 4, 796, 2], [747, 4, 797, 2], [748, 0, 798, 0], [749, 0, 799, 0], [750, 0, 800, 0], [751, 0, 801, 0], [753, 4, 804, 2], [753, 8, 804, 6, "_proto"], [753, 14, 804, 12], [753, 17, 804, 15, "Input"], [753, 22, 804, 20], [753, 23, 804, 21, "prototype"], [753, 32, 804, 30], [754, 4, 806, 2, "_proto"], [754, 10, 806, 8], [754, 11, 806, 9, "handler"], [754, 18, 806, 16], [754, 21, 806, 19], [754, 30, 806, 28, "handler"], [754, 37, 806, 35, "handler"], [754, 38, 806, 35], [754, 40, 806, 38], [754, 41, 806, 39], [754, 42, 806, 40], [755, 4, 807, 2], [756, 0, 808, 0], [757, 0, 809, 0], [758, 0, 810, 0], [760, 4, 813, 2, "_proto"], [760, 10, 813, 8], [760, 11, 813, 9, "init"], [760, 15, 813, 13], [760, 18, 813, 16], [760, 27, 813, 25, "init"], [760, 31, 813, 29, "init"], [760, 32, 813, 29], [760, 34, 813, 32], [761, 6, 814, 4], [761, 10, 814, 8], [761, 11, 814, 9, "evEl"], [761, 15, 814, 13], [761, 19, 814, 17, "addEventListeners"], [761, 36, 814, 34], [761, 37, 814, 35], [761, 41, 814, 39], [761, 42, 814, 40, "element"], [761, 49, 814, 47], [761, 51, 814, 49], [761, 55, 814, 53], [761, 56, 814, 54, "evEl"], [761, 60, 814, 58], [761, 62, 814, 60], [761, 66, 814, 64], [761, 67, 814, 65, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [761, 77, 814, 75], [761, 78, 814, 76], [762, 6, 815, 4], [762, 10, 815, 8], [762, 11, 815, 9, "ev<PERSON><PERSON><PERSON>"], [762, 19, 815, 17], [762, 23, 815, 21, "addEventListeners"], [762, 40, 815, 38], [762, 41, 815, 39], [762, 45, 815, 43], [762, 46, 815, 44, "target"], [762, 52, 815, 50], [762, 54, 815, 52], [762, 58, 815, 56], [762, 59, 815, 57, "ev<PERSON><PERSON><PERSON>"], [762, 67, 815, 65], [762, 69, 815, 67], [762, 73, 815, 71], [762, 74, 815, 72, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [762, 84, 815, 82], [762, 85, 815, 83], [763, 6, 816, 4], [763, 10, 816, 8], [763, 11, 816, 9, "evWin"], [763, 16, 816, 14], [763, 20, 816, 18, "addEventListeners"], [763, 37, 816, 35], [763, 38, 816, 36, "getWindowForElement"], [763, 57, 816, 55], [763, 58, 816, 56], [763, 62, 816, 60], [763, 63, 816, 61, "element"], [763, 70, 816, 68], [763, 71, 816, 69], [763, 73, 816, 71], [763, 77, 816, 75], [763, 78, 816, 76, "evWin"], [763, 83, 816, 81], [763, 85, 816, 83], [763, 89, 816, 87], [763, 90, 816, 88, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [763, 100, 816, 98], [763, 101, 816, 99], [764, 4, 817, 2], [764, 5, 817, 3], [765, 4, 818, 2], [766, 0, 819, 0], [767, 0, 820, 0], [768, 0, 821, 0], [770, 4, 824, 2, "_proto"], [770, 10, 824, 8], [770, 11, 824, 9, "destroy"], [770, 18, 824, 16], [770, 21, 824, 19], [770, 30, 824, 28, "destroy"], [770, 37, 824, 35, "destroy"], [770, 38, 824, 35], [770, 40, 824, 38], [771, 6, 825, 4], [771, 10, 825, 8], [771, 11, 825, 9, "evEl"], [771, 15, 825, 13], [771, 19, 825, 17, "removeEventListeners"], [771, 39, 825, 37], [771, 40, 825, 38], [771, 44, 825, 42], [771, 45, 825, 43, "element"], [771, 52, 825, 50], [771, 54, 825, 52], [771, 58, 825, 56], [771, 59, 825, 57, "evEl"], [771, 63, 825, 61], [771, 65, 825, 63], [771, 69, 825, 67], [771, 70, 825, 68, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [771, 80, 825, 78], [771, 81, 825, 79], [772, 6, 826, 4], [772, 10, 826, 8], [772, 11, 826, 9, "ev<PERSON><PERSON><PERSON>"], [772, 19, 826, 17], [772, 23, 826, 21, "removeEventListeners"], [772, 43, 826, 41], [772, 44, 826, 42], [772, 48, 826, 46], [772, 49, 826, 47, "target"], [772, 55, 826, 53], [772, 57, 826, 55], [772, 61, 826, 59], [772, 62, 826, 60, "ev<PERSON><PERSON><PERSON>"], [772, 70, 826, 68], [772, 72, 826, 70], [772, 76, 826, 74], [772, 77, 826, 75, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [772, 87, 826, 85], [772, 88, 826, 86], [773, 6, 827, 4], [773, 10, 827, 8], [773, 11, 827, 9, "evWin"], [773, 16, 827, 14], [773, 20, 827, 18, "removeEventListeners"], [773, 40, 827, 38], [773, 41, 827, 39, "getWindowForElement"], [773, 60, 827, 58], [773, 61, 827, 59], [773, 65, 827, 63], [773, 66, 827, 64, "element"], [773, 73, 827, 71], [773, 74, 827, 72], [773, 76, 827, 74], [773, 80, 827, 78], [773, 81, 827, 79, "evWin"], [773, 86, 827, 84], [773, 88, 827, 86], [773, 92, 827, 90], [773, 93, 827, 91, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [773, 103, 827, 101], [773, 104, 827, 102], [774, 4, 828, 2], [774, 5, 828, 3], [775, 4, 830, 2], [775, 11, 830, 9, "Input"], [775, 16, 830, 14], [776, 2, 831, 0], [776, 3, 831, 1], [776, 4, 831, 2], [776, 5, 831, 3], [778, 2, 833, 0], [779, 0, 834, 0], [780, 0, 835, 0], [781, 0, 836, 0], [782, 0, 837, 0], [783, 0, 838, 0], [784, 0, 839, 0], [785, 0, 840, 0], [786, 2, 841, 0], [786, 11, 841, 9, "inArray"], [786, 18, 841, 16, "inArray"], [786, 19, 841, 17, "src"], [786, 22, 841, 20], [786, 24, 841, 22, "find"], [786, 28, 841, 26], [786, 30, 841, 28, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [786, 39, 841, 37], [786, 41, 841, 39], [787, 4, 842, 2], [787, 8, 842, 6, "src"], [787, 11, 842, 9], [787, 12, 842, 10, "indexOf"], [787, 19, 842, 17], [787, 23, 842, 21], [787, 24, 842, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [787, 33, 842, 31], [787, 35, 842, 33], [788, 6, 843, 4], [788, 13, 843, 11, "src"], [788, 16, 843, 14], [788, 17, 843, 15, "indexOf"], [788, 24, 843, 22], [788, 25, 843, 23, "find"], [788, 29, 843, 27], [788, 30, 843, 28], [789, 4, 844, 2], [789, 5, 844, 3], [789, 11, 844, 9], [790, 6, 845, 4], [790, 10, 845, 8, "i"], [790, 11, 845, 9], [790, 14, 845, 12], [790, 15, 845, 13], [791, 6, 847, 4], [791, 13, 847, 11, "i"], [791, 14, 847, 12], [791, 17, 847, 15, "src"], [791, 20, 847, 18], [791, 21, 847, 19, "length"], [791, 27, 847, 25], [791, 29, 847, 27], [792, 8, 848, 6], [792, 12, 848, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [792, 21, 848, 19], [792, 25, 848, 23, "src"], [792, 28, 848, 26], [792, 29, 848, 27, "i"], [792, 30, 848, 28], [792, 31, 848, 29], [792, 32, 848, 30, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [792, 41, 848, 39], [792, 42, 848, 40], [792, 46, 848, 44, "find"], [792, 50, 848, 48], [792, 54, 848, 52], [792, 55, 848, 53, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [792, 64, 848, 62], [792, 68, 848, 66, "src"], [792, 71, 848, 69], [792, 72, 848, 70, "i"], [792, 73, 848, 71], [792, 74, 848, 72], [792, 79, 848, 77, "find"], [792, 83, 848, 81], [792, 85, 848, 83], [793, 10, 849, 8], [794, 10, 850, 8], [794, 17, 850, 15, "i"], [794, 18, 850, 16], [795, 8, 851, 6], [796, 8, 853, 6, "i"], [796, 9, 853, 7], [796, 11, 853, 9], [797, 6, 854, 4], [798, 6, 856, 4], [798, 13, 856, 11], [798, 14, 856, 12], [798, 15, 856, 13], [799, 4, 857, 2], [800, 2, 858, 0], [801, 2, 860, 0], [801, 6, 860, 4, "POINTER_INPUT_MAP"], [801, 23, 860, 21], [801, 26, 860, 24], [802, 4, 861, 2, "pointerdown"], [802, 15, 861, 13], [802, 17, 861, 15, "INPUT_START"], [802, 28, 861, 26], [803, 4, 862, 2, "pointermove"], [803, 15, 862, 13], [803, 17, 862, 15, "INPUT_MOVE"], [803, 27, 862, 25], [804, 4, 863, 2, "pointerup"], [804, 13, 863, 11], [804, 15, 863, 13, "INPUT_END"], [804, 24, 863, 22], [805, 4, 864, 2, "pointercancel"], [805, 17, 864, 15], [805, 19, 864, 17, "INPUT_CANCEL"], [805, 31, 864, 29], [806, 4, 865, 2, "pointerout"], [806, 14, 865, 12], [806, 16, 865, 14, "INPUT_CANCEL"], [807, 2, 866, 0], [807, 3, 866, 1], [807, 4, 866, 2], [807, 5, 866, 3], [809, 2, 868, 0], [809, 6, 868, 4, "IE10_POINTER_TYPE_ENUM"], [809, 28, 868, 26], [809, 31, 868, 29], [810, 4, 869, 2], [810, 5, 869, 3], [810, 7, 869, 5, "INPUT_TYPE_TOUCH"], [810, 23, 869, 21], [811, 4, 870, 2], [811, 5, 870, 3], [811, 7, 870, 5, "INPUT_TYPE_PEN"], [811, 21, 870, 19], [812, 4, 871, 2], [812, 5, 871, 3], [812, 7, 871, 5, "INPUT_TYPE_MOUSE"], [812, 23, 871, 21], [813, 4, 872, 2], [813, 5, 872, 3], [813, 7, 872, 5, "INPUT_TYPE_KINECT"], [813, 24, 872, 22], [813, 25, 872, 23], [814, 2, 874, 0], [814, 3, 874, 1], [815, 2, 875, 0], [815, 6, 875, 4, "POINTER_ELEMENT_EVENTS"], [815, 28, 875, 26], [815, 31, 875, 29], [815, 44, 875, 42], [816, 2, 876, 0], [816, 6, 876, 4, "POINTER_WINDOW_EVENTS"], [816, 27, 876, 25], [816, 30, 876, 28], [816, 67, 876, 65], [816, 68, 876, 66], [816, 69, 876, 67], [818, 2, 878, 0], [818, 6, 878, 4, "win"], [818, 9, 878, 7], [818, 10, 878, 8, "MSPointerEvent"], [818, 24, 878, 22], [818, 28, 878, 26], [818, 29, 878, 27, "win"], [818, 32, 878, 30], [818, 33, 878, 31, "PointerEvent"], [818, 45, 878, 43], [818, 47, 878, 45], [819, 4, 879, 2, "POINTER_ELEMENT_EVENTS"], [819, 26, 879, 24], [819, 29, 879, 27], [819, 44, 879, 42], [820, 4, 880, 2, "POINTER_WINDOW_EVENTS"], [820, 25, 880, 23], [820, 28, 880, 26], [820, 71, 880, 69], [821, 2, 881, 0], [822, 2, 882, 0], [823, 0, 883, 0], [824, 0, 884, 0], [825, 0, 885, 0], [826, 0, 886, 0], [827, 0, 887, 0], [829, 2, 890, 0], [829, 6, 890, 4, "PointerEventInput"], [829, 23, 890, 21], [829, 26, 890, 21, "exports"], [829, 33, 890, 21], [829, 34, 890, 21, "PointerEventInput"], [829, 51, 890, 21], [829, 54, 891, 0], [830, 2, 892, 0], [830, 12, 892, 10, "_Input"], [830, 18, 892, 16], [830, 20, 892, 18], [831, 4, 893, 2, "_inherits<PERSON><PERSON>e"], [831, 18, 893, 16], [831, 19, 893, 17, "PointerEventInput"], [831, 36, 893, 34], [831, 38, 893, 36, "_Input"], [831, 44, 893, 42], [831, 45, 893, 43], [832, 4, 895, 2], [832, 13, 895, 11, "PointerEventInput"], [832, 30, 895, 28, "PointerEventInput"], [832, 31, 895, 28], [832, 33, 895, 31], [833, 6, 896, 4], [833, 10, 896, 8, "_this"], [833, 15, 896, 13], [834, 6, 898, 4], [834, 10, 898, 8, "proto"], [834, 15, 898, 13], [834, 18, 898, 16, "PointerEventInput"], [834, 35, 898, 33], [834, 36, 898, 34, "prototype"], [834, 45, 898, 43], [835, 6, 899, 4, "proto"], [835, 11, 899, 9], [835, 12, 899, 10, "evEl"], [835, 16, 899, 14], [835, 19, 899, 17, "POINTER_ELEMENT_EVENTS"], [835, 41, 899, 39], [836, 6, 900, 4, "proto"], [836, 11, 900, 9], [836, 12, 900, 10, "evWin"], [836, 17, 900, 15], [836, 20, 900, 18, "POINTER_WINDOW_EVENTS"], [836, 41, 900, 39], [837, 6, 901, 4, "_this"], [837, 11, 901, 9], [837, 14, 901, 12, "_Input"], [837, 20, 901, 18], [837, 21, 901, 19, "apply"], [837, 26, 901, 24], [837, 27, 901, 25], [837, 31, 901, 29], [837, 33, 901, 31, "arguments"], [837, 42, 901, 40], [837, 43, 901, 41], [837, 47, 901, 45], [837, 51, 901, 49], [838, 6, 902, 4, "_this"], [838, 11, 902, 9], [838, 12, 902, 10, "store"], [838, 17, 902, 15], [838, 20, 902, 18, "_this"], [838, 25, 902, 23], [838, 26, 902, 24, "manager"], [838, 33, 902, 31], [838, 34, 902, 32, "session"], [838, 41, 902, 39], [838, 42, 902, 40, "pointerEvents"], [838, 55, 902, 53], [838, 58, 902, 56], [838, 60, 902, 58], [839, 6, 903, 4], [839, 13, 903, 11, "_this"], [839, 18, 903, 16], [840, 4, 904, 2], [841, 4, 905, 2], [842, 0, 906, 0], [843, 0, 907, 0], [844, 0, 908, 0], [845, 0, 909, 0], [847, 4, 912, 2], [847, 8, 912, 6, "_proto"], [847, 14, 912, 12], [847, 17, 912, 15, "PointerEventInput"], [847, 34, 912, 32], [847, 35, 912, 33, "prototype"], [847, 44, 912, 42], [848, 4, 914, 2, "_proto"], [848, 10, 914, 8], [848, 11, 914, 9, "handler"], [848, 18, 914, 16], [848, 21, 914, 19], [848, 30, 914, 28, "handler"], [848, 37, 914, 35, "handler"], [848, 38, 914, 36, "ev"], [848, 40, 914, 38], [848, 42, 914, 40], [849, 6, 915, 4], [849, 10, 915, 8, "store"], [849, 15, 915, 13], [849, 18, 915, 16], [849, 22, 915, 20], [849, 23, 915, 21, "store"], [849, 28, 915, 26], [850, 6, 916, 4], [850, 10, 916, 8, "removePointer"], [850, 23, 916, 21], [850, 26, 916, 24], [850, 31, 916, 29], [851, 6, 917, 4], [851, 10, 917, 8, "eventTypeNormalized"], [851, 29, 917, 27], [851, 32, 917, 30, "ev"], [851, 34, 917, 32], [851, 35, 917, 33, "type"], [851, 39, 917, 37], [851, 40, 917, 38, "toLowerCase"], [851, 51, 917, 49], [851, 52, 917, 50], [851, 53, 917, 51], [851, 54, 917, 52, "replace"], [851, 61, 917, 59], [851, 62, 917, 60], [851, 66, 917, 64], [851, 68, 917, 66], [851, 70, 917, 68], [851, 71, 917, 69], [852, 6, 918, 4], [852, 10, 918, 8, "eventType"], [852, 19, 918, 17], [852, 22, 918, 20, "POINTER_INPUT_MAP"], [852, 39, 918, 37], [852, 40, 918, 38, "eventTypeNormalized"], [852, 59, 918, 57], [852, 60, 918, 58], [853, 6, 919, 4], [853, 10, 919, 8, "pointerType"], [853, 21, 919, 19], [853, 24, 919, 22, "IE10_POINTER_TYPE_ENUM"], [853, 46, 919, 44], [853, 47, 919, 45, "ev"], [853, 49, 919, 47], [853, 50, 919, 48, "pointerType"], [853, 61, 919, 59], [853, 62, 919, 60], [853, 66, 919, 64, "ev"], [853, 68, 919, 66], [853, 69, 919, 67, "pointerType"], [853, 80, 919, 78], [854, 6, 920, 4], [854, 10, 920, 8, "is<PERSON><PERSON>ch"], [854, 17, 920, 15], [854, 20, 920, 18, "pointerType"], [854, 31, 920, 29], [854, 36, 920, 34, "INPUT_TYPE_TOUCH"], [854, 52, 920, 50], [854, 53, 920, 51], [854, 54, 920, 52], [856, 6, 922, 4], [856, 10, 922, 8, "storeIndex"], [856, 20, 922, 18], [856, 23, 922, 21, "inArray"], [856, 30, 922, 28], [856, 31, 922, 29, "store"], [856, 36, 922, 34], [856, 38, 922, 36, "ev"], [856, 40, 922, 38], [856, 41, 922, 39, "pointerId"], [856, 50, 922, 48], [856, 52, 922, 50], [856, 63, 922, 61], [856, 64, 922, 62], [856, 65, 922, 63], [856, 66, 922, 64], [858, 6, 924, 4], [858, 10, 924, 8, "eventType"], [858, 19, 924, 17], [858, 22, 924, 20, "INPUT_START"], [858, 33, 924, 31], [858, 38, 924, 36, "ev"], [858, 40, 924, 38], [858, 41, 924, 39, "button"], [858, 47, 924, 45], [858, 52, 924, 50], [858, 53, 924, 51], [858, 57, 924, 55, "is<PERSON><PERSON>ch"], [858, 64, 924, 62], [858, 65, 924, 63], [858, 67, 924, 65], [859, 8, 925, 6], [859, 12, 925, 10, "storeIndex"], [859, 22, 925, 20], [859, 25, 925, 23], [859, 26, 925, 24], [859, 28, 925, 26], [860, 10, 926, 8, "store"], [860, 15, 926, 13], [860, 16, 926, 14, "push"], [860, 20, 926, 18], [860, 21, 926, 19, "ev"], [860, 23, 926, 21], [860, 24, 926, 22], [861, 10, 927, 8, "storeIndex"], [861, 20, 927, 18], [861, 23, 927, 21, "store"], [861, 28, 927, 26], [861, 29, 927, 27, "length"], [861, 35, 927, 33], [861, 38, 927, 36], [861, 39, 927, 37], [862, 8, 928, 6], [863, 6, 929, 4], [863, 7, 929, 5], [863, 13, 929, 11], [863, 17, 929, 15, "eventType"], [863, 26, 929, 24], [863, 30, 929, 28, "INPUT_END"], [863, 39, 929, 37], [863, 42, 929, 40, "INPUT_CANCEL"], [863, 54, 929, 52], [863, 55, 929, 53], [863, 57, 929, 55], [864, 8, 930, 6, "removePointer"], [864, 21, 930, 19], [864, 24, 930, 22], [864, 28, 930, 26], [865, 6, 931, 4], [865, 7, 931, 5], [865, 8, 931, 6], [867, 6, 934, 4], [867, 10, 934, 8, "storeIndex"], [867, 20, 934, 18], [867, 23, 934, 21], [867, 24, 934, 22], [867, 26, 934, 24], [868, 8, 935, 6], [869, 6, 936, 4], [869, 7, 936, 5], [869, 8, 936, 6], [871, 6, 939, 4, "store"], [871, 11, 939, 9], [871, 12, 939, 10, "storeIndex"], [871, 22, 939, 20], [871, 23, 939, 21], [871, 26, 939, 24, "ev"], [871, 28, 939, 26], [872, 6, 940, 4], [872, 10, 940, 8], [872, 11, 940, 9, "callback"], [872, 19, 940, 17], [872, 20, 940, 18], [872, 24, 940, 22], [872, 25, 940, 23, "manager"], [872, 32, 940, 30], [872, 34, 940, 32, "eventType"], [872, 43, 940, 41], [872, 45, 940, 43], [873, 8, 941, 6, "pointers"], [873, 16, 941, 14], [873, 18, 941, 16, "store"], [873, 23, 941, 21], [874, 8, 942, 6, "changedPointers"], [874, 23, 942, 21], [874, 25, 942, 23], [874, 26, 942, 24, "ev"], [874, 28, 942, 26], [874, 29, 942, 27], [875, 8, 943, 6, "pointerType"], [875, 19, 943, 17], [875, 21, 943, 19, "pointerType"], [875, 32, 943, 30], [876, 8, 944, 6, "srcEvent"], [876, 16, 944, 14], [876, 18, 944, 16, "ev"], [877, 6, 945, 4], [877, 7, 945, 5], [877, 8, 945, 6], [878, 6, 947, 4], [878, 10, 947, 8, "removePointer"], [878, 23, 947, 21], [878, 25, 947, 23], [879, 8, 948, 6], [880, 8, 949, 6, "store"], [880, 13, 949, 11], [880, 14, 949, 12, "splice"], [880, 20, 949, 18], [880, 21, 949, 19, "storeIndex"], [880, 31, 949, 29], [880, 33, 949, 31], [880, 34, 949, 32], [880, 35, 949, 33], [881, 6, 950, 4], [882, 4, 951, 2], [882, 5, 951, 3], [883, 4, 953, 2], [883, 11, 953, 9, "PointerEventInput"], [883, 28, 953, 26], [884, 2, 954, 0], [884, 3, 954, 1], [884, 4, 954, 2, "Input"], [884, 9, 954, 7], [884, 10, 954, 8], [886, 2, 956, 0], [887, 0, 957, 0], [888, 0, 958, 0], [889, 0, 959, 0], [890, 0, 960, 0], [891, 0, 961, 0], [892, 2, 962, 0], [892, 11, 962, 9, "toArray"], [892, 18, 962, 16, "toArray"], [892, 19, 962, 17, "obj"], [892, 22, 962, 20], [892, 24, 962, 22], [893, 4, 963, 2], [893, 11, 963, 9, "Array"], [893, 16, 963, 14], [893, 17, 963, 15, "prototype"], [893, 26, 963, 24], [893, 27, 963, 25, "slice"], [893, 32, 963, 30], [893, 33, 963, 31, "call"], [893, 37, 963, 35], [893, 38, 963, 36, "obj"], [893, 41, 963, 39], [893, 43, 963, 41], [893, 44, 963, 42], [893, 45, 963, 43], [894, 2, 964, 0], [896, 2, 966, 0], [897, 0, 967, 0], [898, 0, 968, 0], [899, 0, 969, 0], [900, 0, 970, 0], [901, 0, 971, 0], [902, 0, 972, 0], [903, 0, 973, 0], [905, 2, 975, 0], [905, 11, 975, 9, "uniqueArray"], [905, 22, 975, 20, "uniqueArray"], [905, 23, 975, 21, "src"], [905, 26, 975, 24], [905, 28, 975, 26, "key"], [905, 31, 975, 29], [905, 33, 975, 31, "sort"], [905, 37, 975, 35], [905, 39, 975, 37], [906, 4, 976, 2], [906, 8, 976, 6, "results"], [906, 15, 976, 13], [906, 18, 976, 16], [906, 20, 976, 18], [907, 4, 977, 2], [907, 8, 977, 6, "values"], [907, 14, 977, 12], [907, 17, 977, 15], [907, 19, 977, 17], [908, 4, 978, 2], [908, 8, 978, 6, "i"], [908, 9, 978, 7], [908, 12, 978, 10], [908, 13, 978, 11], [909, 4, 980, 2], [909, 11, 980, 9, "i"], [909, 12, 980, 10], [909, 15, 980, 13, "src"], [909, 18, 980, 16], [909, 19, 980, 17, "length"], [909, 25, 980, 23], [909, 27, 980, 25], [910, 6, 981, 4], [910, 10, 981, 8, "val"], [910, 13, 981, 11], [910, 16, 981, 14, "key"], [910, 19, 981, 17], [910, 22, 981, 20, "src"], [910, 25, 981, 23], [910, 26, 981, 24, "i"], [910, 27, 981, 25], [910, 28, 981, 26], [910, 29, 981, 27, "key"], [910, 32, 981, 30], [910, 33, 981, 31], [910, 36, 981, 34, "src"], [910, 39, 981, 37], [910, 40, 981, 38, "i"], [910, 41, 981, 39], [910, 42, 981, 40], [911, 6, 983, 4], [911, 10, 983, 8, "inArray"], [911, 17, 983, 15], [911, 18, 983, 16, "values"], [911, 24, 983, 22], [911, 26, 983, 24, "val"], [911, 29, 983, 27], [911, 30, 983, 28], [911, 33, 983, 31], [911, 34, 983, 32], [911, 36, 983, 34], [912, 8, 984, 6, "results"], [912, 15, 984, 13], [912, 16, 984, 14, "push"], [912, 20, 984, 18], [912, 21, 984, 19, "src"], [912, 24, 984, 22], [912, 25, 984, 23, "i"], [912, 26, 984, 24], [912, 27, 984, 25], [912, 28, 984, 26], [913, 6, 985, 4], [914, 6, 987, 4, "values"], [914, 12, 987, 10], [914, 13, 987, 11, "i"], [914, 14, 987, 12], [914, 15, 987, 13], [914, 18, 987, 16, "val"], [914, 21, 987, 19], [915, 6, 988, 4, "i"], [915, 7, 988, 5], [915, 9, 988, 7], [916, 4, 989, 2], [917, 4, 991, 2], [917, 8, 991, 6, "sort"], [917, 12, 991, 10], [917, 14, 991, 12], [918, 6, 992, 4], [918, 10, 992, 8], [918, 11, 992, 9, "key"], [918, 14, 992, 12], [918, 16, 992, 14], [919, 8, 993, 6, "results"], [919, 15, 993, 13], [919, 18, 993, 16, "results"], [919, 25, 993, 23], [919, 26, 993, 24, "sort"], [919, 30, 993, 28], [919, 31, 993, 29], [919, 32, 993, 30], [920, 6, 994, 4], [920, 7, 994, 5], [920, 13, 994, 11], [921, 8, 995, 6, "results"], [921, 15, 995, 13], [921, 18, 995, 16, "results"], [921, 25, 995, 23], [921, 26, 995, 24, "sort"], [921, 30, 995, 28], [921, 31, 995, 29], [921, 41, 995, 39, "a"], [921, 42, 995, 40], [921, 44, 995, 42, "b"], [921, 45, 995, 43], [921, 47, 995, 45], [922, 10, 996, 8], [922, 17, 996, 15, "a"], [922, 18, 996, 16], [922, 19, 996, 17, "key"], [922, 22, 996, 20], [922, 23, 996, 21], [922, 26, 996, 24, "b"], [922, 27, 996, 25], [922, 28, 996, 26, "key"], [922, 31, 996, 29], [922, 32, 996, 30], [923, 8, 997, 6], [923, 9, 997, 7], [923, 10, 997, 8], [924, 6, 998, 4], [925, 4, 999, 2], [926, 4, 1001, 2], [926, 11, 1001, 9, "results"], [926, 18, 1001, 16], [927, 2, 1002, 0], [928, 2, 1004, 0], [928, 6, 1004, 4, "TOUCH_INPUT_MAP"], [928, 21, 1004, 19], [928, 24, 1004, 22], [929, 4, 1005, 2, "touchstart"], [929, 14, 1005, 12], [929, 16, 1005, 14, "INPUT_START"], [929, 27, 1005, 25], [930, 4, 1006, 2, "touchmove"], [930, 13, 1006, 11], [930, 15, 1006, 13, "INPUT_MOVE"], [930, 25, 1006, 23], [931, 4, 1007, 2, "touchend"], [931, 12, 1007, 10], [931, 14, 1007, 12, "INPUT_END"], [931, 23, 1007, 21], [932, 4, 1008, 2, "touchcancel"], [932, 15, 1008, 13], [932, 17, 1008, 15, "INPUT_CANCEL"], [933, 2, 1009, 0], [933, 3, 1009, 1], [934, 2, 1010, 0], [934, 6, 1010, 4, "TOUCH_TARGET_EVENTS"], [934, 25, 1010, 23], [934, 28, 1010, 26], [934, 71, 1010, 69], [935, 2, 1011, 0], [936, 0, 1012, 0], [937, 0, 1013, 0], [938, 0, 1014, 0], [939, 0, 1015, 0], [940, 0, 1016, 0], [942, 2, 1018, 0], [942, 6, 1018, 4, "TouchInput"], [942, 16, 1018, 14], [942, 19, 1018, 14, "exports"], [942, 26, 1018, 14], [942, 27, 1018, 14, "TouchInput"], [942, 37, 1018, 14], [942, 40, 1019, 0], [943, 2, 1020, 0], [943, 12, 1020, 10, "_Input"], [943, 18, 1020, 16], [943, 20, 1020, 18], [944, 4, 1021, 2, "_inherits<PERSON><PERSON>e"], [944, 18, 1021, 16], [944, 19, 1021, 17, "TouchInput"], [944, 29, 1021, 27], [944, 31, 1021, 29, "_Input"], [944, 37, 1021, 35], [944, 38, 1021, 36], [945, 4, 1023, 2], [945, 13, 1023, 11, "TouchInput"], [945, 23, 1023, 21, "TouchInput"], [945, 24, 1023, 21], [945, 26, 1023, 24], [946, 6, 1024, 4], [946, 10, 1024, 8, "_this"], [946, 15, 1024, 13], [947, 6, 1026, 4, "TouchInput"], [947, 16, 1026, 14], [947, 17, 1026, 15, "prototype"], [947, 26, 1026, 24], [947, 27, 1026, 25, "ev<PERSON><PERSON><PERSON>"], [947, 35, 1026, 33], [947, 38, 1026, 36, "TOUCH_TARGET_EVENTS"], [947, 57, 1026, 55], [948, 6, 1027, 4, "_this"], [948, 11, 1027, 9], [948, 14, 1027, 12, "_Input"], [948, 20, 1027, 18], [948, 21, 1027, 19, "apply"], [948, 26, 1027, 24], [948, 27, 1027, 25], [948, 31, 1027, 29], [948, 33, 1027, 31, "arguments"], [948, 42, 1027, 40], [948, 43, 1027, 41], [948, 47, 1027, 45], [948, 51, 1027, 49], [949, 6, 1028, 4, "_this"], [949, 11, 1028, 9], [949, 12, 1028, 10, "targetIds"], [949, 21, 1028, 19], [949, 24, 1028, 22], [949, 25, 1028, 23], [949, 26, 1028, 24], [949, 27, 1028, 25], [949, 28, 1028, 26], [951, 6, 1030, 4], [951, 13, 1030, 11, "_this"], [951, 18, 1030, 16], [952, 4, 1031, 2], [953, 4, 1033, 2], [953, 8, 1033, 6, "_proto"], [953, 14, 1033, 12], [953, 17, 1033, 15, "TouchInput"], [953, 27, 1033, 25], [953, 28, 1033, 26, "prototype"], [953, 37, 1033, 35], [954, 4, 1035, 2, "_proto"], [954, 10, 1035, 8], [954, 11, 1035, 9, "handler"], [954, 18, 1035, 16], [954, 21, 1035, 19], [954, 30, 1035, 28, "handler"], [954, 37, 1035, 35, "handler"], [954, 38, 1035, 36, "ev"], [954, 40, 1035, 38], [954, 42, 1035, 40], [955, 6, 1036, 4], [955, 10, 1036, 8, "type"], [955, 14, 1036, 12], [955, 17, 1036, 15, "TOUCH_INPUT_MAP"], [955, 32, 1036, 30], [955, 33, 1036, 31, "ev"], [955, 35, 1036, 33], [955, 36, 1036, 34, "type"], [955, 40, 1036, 38], [955, 41, 1036, 39], [956, 6, 1037, 4], [956, 10, 1037, 8, "touches"], [956, 17, 1037, 15], [956, 20, 1037, 18, "getTouches"], [956, 30, 1037, 28], [956, 31, 1037, 29, "call"], [956, 35, 1037, 33], [956, 36, 1037, 34], [956, 40, 1037, 38], [956, 42, 1037, 40, "ev"], [956, 44, 1037, 42], [956, 46, 1037, 44, "type"], [956, 50, 1037, 48], [956, 51, 1037, 49], [957, 6, 1039, 4], [957, 10, 1039, 8], [957, 11, 1039, 9, "touches"], [957, 18, 1039, 16], [957, 20, 1039, 18], [958, 8, 1040, 6], [959, 6, 1041, 4], [960, 6, 1043, 4], [960, 10, 1043, 8], [960, 11, 1043, 9, "callback"], [960, 19, 1043, 17], [960, 20, 1043, 18], [960, 24, 1043, 22], [960, 25, 1043, 23, "manager"], [960, 32, 1043, 30], [960, 34, 1043, 32, "type"], [960, 38, 1043, 36], [960, 40, 1043, 38], [961, 8, 1044, 6, "pointers"], [961, 16, 1044, 14], [961, 18, 1044, 16, "touches"], [961, 25, 1044, 23], [961, 26, 1044, 24], [961, 27, 1044, 25], [961, 28, 1044, 26], [962, 8, 1045, 6, "changedPointers"], [962, 23, 1045, 21], [962, 25, 1045, 23, "touches"], [962, 32, 1045, 30], [962, 33, 1045, 31], [962, 34, 1045, 32], [962, 35, 1045, 33], [963, 8, 1046, 6, "pointerType"], [963, 19, 1046, 17], [963, 21, 1046, 19, "INPUT_TYPE_TOUCH"], [963, 37, 1046, 35], [964, 8, 1047, 6, "srcEvent"], [964, 16, 1047, 14], [964, 18, 1047, 16, "ev"], [965, 6, 1048, 4], [965, 7, 1048, 5], [965, 8, 1048, 6], [966, 4, 1049, 2], [966, 5, 1049, 3], [967, 4, 1051, 2], [967, 11, 1051, 9, "TouchInput"], [967, 21, 1051, 19], [968, 2, 1052, 0], [968, 3, 1052, 1], [968, 4, 1052, 2, "Input"], [968, 9, 1052, 7], [968, 10, 1052, 8], [969, 2, 1054, 0], [969, 11, 1054, 9, "getTouches"], [969, 21, 1054, 19, "getTouches"], [969, 22, 1054, 20, "ev"], [969, 24, 1054, 22], [969, 26, 1054, 24, "type"], [969, 30, 1054, 28], [969, 32, 1054, 30], [970, 4, 1055, 2], [970, 8, 1055, 6, "allTouches"], [970, 18, 1055, 16], [970, 21, 1055, 19, "toArray"], [970, 28, 1055, 26], [970, 29, 1055, 27, "ev"], [970, 31, 1055, 29], [970, 32, 1055, 30, "touches"], [970, 39, 1055, 37], [970, 40, 1055, 38], [971, 4, 1056, 2], [971, 8, 1056, 6, "targetIds"], [971, 17, 1056, 15], [971, 20, 1056, 18], [971, 24, 1056, 22], [971, 25, 1056, 23, "targetIds"], [971, 34, 1056, 32], [971, 35, 1056, 33], [971, 36, 1056, 34], [973, 4, 1058, 2], [973, 8, 1058, 6, "type"], [973, 12, 1058, 10], [973, 16, 1058, 14, "INPUT_START"], [973, 27, 1058, 25], [973, 30, 1058, 28, "INPUT_MOVE"], [973, 40, 1058, 38], [973, 41, 1058, 39], [973, 45, 1058, 43, "allTouches"], [973, 55, 1058, 53], [973, 56, 1058, 54, "length"], [973, 62, 1058, 60], [973, 67, 1058, 65], [973, 68, 1058, 66], [973, 70, 1058, 68], [974, 6, 1059, 4, "targetIds"], [974, 15, 1059, 13], [974, 16, 1059, 14, "allTouches"], [974, 26, 1059, 24], [974, 27, 1059, 25], [974, 28, 1059, 26], [974, 29, 1059, 27], [974, 30, 1059, 28, "identifier"], [974, 40, 1059, 38], [974, 41, 1059, 39], [974, 44, 1059, 42], [974, 48, 1059, 46], [975, 6, 1060, 4], [975, 13, 1060, 11], [975, 14, 1060, 12, "allTouches"], [975, 24, 1060, 22], [975, 26, 1060, 24, "allTouches"], [975, 36, 1060, 34], [975, 37, 1060, 35], [976, 4, 1061, 2], [977, 4, 1063, 2], [977, 8, 1063, 6, "i"], [977, 9, 1063, 7], [978, 4, 1064, 2], [978, 8, 1064, 6, "targetTouches"], [978, 21, 1064, 19], [979, 4, 1065, 2], [979, 8, 1065, 6, "changedTouches"], [979, 22, 1065, 20], [979, 25, 1065, 23, "toArray"], [979, 32, 1065, 30], [979, 33, 1065, 31, "ev"], [979, 35, 1065, 33], [979, 36, 1065, 34, "changedTouches"], [979, 50, 1065, 48], [979, 51, 1065, 49], [980, 4, 1066, 2], [980, 8, 1066, 6, "changedTargetTouches"], [980, 28, 1066, 26], [980, 31, 1066, 29], [980, 33, 1066, 31], [981, 4, 1067, 2], [981, 8, 1067, 6, "target"], [981, 14, 1067, 12], [981, 17, 1067, 15], [981, 21, 1067, 19], [981, 22, 1067, 20, "target"], [981, 28, 1067, 26], [981, 29, 1067, 27], [981, 30, 1067, 28], [983, 4, 1069, 2, "targetTouches"], [983, 17, 1069, 15], [983, 20, 1069, 18, "allTouches"], [983, 30, 1069, 28], [983, 31, 1069, 29, "filter"], [983, 37, 1069, 35], [983, 38, 1069, 36], [983, 48, 1069, 46, "touch"], [983, 53, 1069, 51], [983, 55, 1069, 53], [984, 6, 1070, 4], [984, 13, 1070, 11, "hasParent"], [984, 22, 1070, 20], [984, 23, 1070, 21, "touch"], [984, 28, 1070, 26], [984, 29, 1070, 27, "target"], [984, 35, 1070, 33], [984, 37, 1070, 35, "target"], [984, 43, 1070, 41], [984, 44, 1070, 42], [985, 4, 1071, 2], [985, 5, 1071, 3], [985, 6, 1071, 4], [985, 7, 1071, 5], [985, 8, 1071, 6], [987, 4, 1073, 2], [987, 8, 1073, 6, "type"], [987, 12, 1073, 10], [987, 17, 1073, 15, "INPUT_START"], [987, 28, 1073, 26], [987, 30, 1073, 28], [988, 6, 1074, 4, "i"], [988, 7, 1074, 5], [988, 10, 1074, 8], [988, 11, 1074, 9], [989, 6, 1076, 4], [989, 13, 1076, 11, "i"], [989, 14, 1076, 12], [989, 17, 1076, 15, "targetTouches"], [989, 30, 1076, 28], [989, 31, 1076, 29, "length"], [989, 37, 1076, 35], [989, 39, 1076, 37], [990, 8, 1077, 6, "targetIds"], [990, 17, 1077, 15], [990, 18, 1077, 16, "targetTouches"], [990, 31, 1077, 29], [990, 32, 1077, 30, "i"], [990, 33, 1077, 31], [990, 34, 1077, 32], [990, 35, 1077, 33, "identifier"], [990, 45, 1077, 43], [990, 46, 1077, 44], [990, 49, 1077, 47], [990, 53, 1077, 51], [991, 8, 1078, 6, "i"], [991, 9, 1078, 7], [991, 11, 1078, 9], [992, 6, 1079, 4], [993, 4, 1080, 2], [993, 5, 1080, 3], [993, 6, 1080, 4], [995, 4, 1083, 2, "i"], [995, 5, 1083, 3], [995, 8, 1083, 6], [995, 9, 1083, 7], [996, 4, 1085, 2], [996, 11, 1085, 9, "i"], [996, 12, 1085, 10], [996, 15, 1085, 13, "changedTouches"], [996, 29, 1085, 27], [996, 30, 1085, 28, "length"], [996, 36, 1085, 34], [996, 38, 1085, 36], [997, 6, 1086, 4], [997, 10, 1086, 8, "targetIds"], [997, 19, 1086, 17], [997, 20, 1086, 18, "changedTouches"], [997, 34, 1086, 32], [997, 35, 1086, 33, "i"], [997, 36, 1086, 34], [997, 37, 1086, 35], [997, 38, 1086, 36, "identifier"], [997, 48, 1086, 46], [997, 49, 1086, 47], [997, 51, 1086, 49], [998, 8, 1087, 6, "changedTargetTouches"], [998, 28, 1087, 26], [998, 29, 1087, 27, "push"], [998, 33, 1087, 31], [998, 34, 1087, 32, "changedTouches"], [998, 48, 1087, 46], [998, 49, 1087, 47, "i"], [998, 50, 1087, 48], [998, 51, 1087, 49], [998, 52, 1087, 50], [999, 6, 1088, 4], [999, 7, 1088, 5], [999, 8, 1088, 6], [1001, 6, 1091, 4], [1001, 10, 1091, 8, "type"], [1001, 14, 1091, 12], [1001, 18, 1091, 16, "INPUT_END"], [1001, 27, 1091, 25], [1001, 30, 1091, 28, "INPUT_CANCEL"], [1001, 42, 1091, 40], [1001, 43, 1091, 41], [1001, 45, 1091, 43], [1002, 8, 1092, 6], [1002, 15, 1092, 13, "targetIds"], [1002, 24, 1092, 22], [1002, 25, 1092, 23, "changedTouches"], [1002, 39, 1092, 37], [1002, 40, 1092, 38, "i"], [1002, 41, 1092, 39], [1002, 42, 1092, 40], [1002, 43, 1092, 41, "identifier"], [1002, 53, 1092, 51], [1002, 54, 1092, 52], [1003, 6, 1093, 4], [1004, 6, 1095, 4, "i"], [1004, 7, 1095, 5], [1004, 9, 1095, 7], [1005, 4, 1096, 2], [1006, 4, 1098, 2], [1006, 8, 1098, 6], [1006, 9, 1098, 7, "changedTargetTouches"], [1006, 29, 1098, 27], [1006, 30, 1098, 28, "length"], [1006, 36, 1098, 34], [1006, 38, 1098, 36], [1007, 6, 1099, 4], [1008, 4, 1100, 2], [1009, 4, 1102, 2], [1009, 11, 1102, 9], [1010, 4, 1102, 10], [1011, 4, 1103, 2, "uniqueArray"], [1011, 15, 1103, 13], [1011, 16, 1103, 14, "targetTouches"], [1011, 29, 1103, 27], [1011, 30, 1103, 28, "concat"], [1011, 36, 1103, 34], [1011, 37, 1103, 35, "changedTargetTouches"], [1011, 57, 1103, 55], [1011, 58, 1103, 56], [1011, 60, 1103, 58], [1011, 72, 1103, 70], [1011, 74, 1103, 72], [1011, 78, 1103, 76], [1011, 79, 1103, 77], [1011, 81, 1103, 79, "changedTargetTouches"], [1011, 101, 1103, 99], [1011, 102, 1103, 100], [1012, 2, 1104, 0], [1013, 2, 1106, 0], [1013, 6, 1106, 4, "MOUSE_INPUT_MAP"], [1013, 21, 1106, 19], [1013, 24, 1106, 22], [1014, 4, 1107, 2, "mousedown"], [1014, 13, 1107, 11], [1014, 15, 1107, 13, "INPUT_START"], [1014, 26, 1107, 24], [1015, 4, 1108, 2, "mousemove"], [1015, 13, 1108, 11], [1015, 15, 1108, 13, "INPUT_MOVE"], [1015, 25, 1108, 23], [1016, 4, 1109, 2, "mouseup"], [1016, 11, 1109, 9], [1016, 13, 1109, 11, "INPUT_END"], [1017, 2, 1110, 0], [1017, 3, 1110, 1], [1018, 2, 1111, 0], [1018, 6, 1111, 4, "MOUSE_ELEMENT_EVENTS"], [1018, 26, 1111, 24], [1018, 29, 1111, 27], [1018, 40, 1111, 38], [1019, 2, 1112, 0], [1019, 6, 1112, 4, "MOUSE_WINDOW_EVENTS"], [1019, 25, 1112, 23], [1019, 28, 1112, 26], [1019, 47, 1112, 45], [1020, 2, 1113, 0], [1021, 0, 1114, 0], [1022, 0, 1115, 0], [1023, 0, 1116, 0], [1024, 0, 1117, 0], [1025, 0, 1118, 0], [1027, 2, 1120, 0], [1027, 6, 1120, 4, "MouseInput"], [1027, 16, 1120, 14], [1027, 19, 1120, 14, "exports"], [1027, 26, 1120, 14], [1027, 27, 1120, 14, "MouseInput"], [1027, 37, 1120, 14], [1027, 40, 1121, 0], [1028, 2, 1122, 0], [1028, 12, 1122, 10, "_Input"], [1028, 18, 1122, 16], [1028, 20, 1122, 18], [1029, 4, 1123, 2, "_inherits<PERSON><PERSON>e"], [1029, 18, 1123, 16], [1029, 19, 1123, 17, "MouseInput"], [1029, 29, 1123, 27], [1029, 31, 1123, 29, "_Input"], [1029, 37, 1123, 35], [1029, 38, 1123, 36], [1030, 4, 1125, 2], [1030, 13, 1125, 11, "MouseInput"], [1030, 23, 1125, 21, "MouseInput"], [1030, 24, 1125, 21], [1030, 26, 1125, 24], [1031, 6, 1126, 4], [1031, 10, 1126, 8, "_this"], [1031, 15, 1126, 13], [1032, 6, 1128, 4], [1032, 10, 1128, 8, "proto"], [1032, 15, 1128, 13], [1032, 18, 1128, 16, "MouseInput"], [1032, 28, 1128, 26], [1032, 29, 1128, 27, "prototype"], [1032, 38, 1128, 36], [1033, 6, 1129, 4, "proto"], [1033, 11, 1129, 9], [1033, 12, 1129, 10, "evEl"], [1033, 16, 1129, 14], [1033, 19, 1129, 17, "MOUSE_ELEMENT_EVENTS"], [1033, 39, 1129, 37], [1034, 6, 1130, 4, "proto"], [1034, 11, 1130, 9], [1034, 12, 1130, 10, "evWin"], [1034, 17, 1130, 15], [1034, 20, 1130, 18, "MOUSE_WINDOW_EVENTS"], [1034, 39, 1130, 37], [1035, 6, 1131, 4, "_this"], [1035, 11, 1131, 9], [1035, 14, 1131, 12, "_Input"], [1035, 20, 1131, 18], [1035, 21, 1131, 19, "apply"], [1035, 26, 1131, 24], [1035, 27, 1131, 25], [1035, 31, 1131, 29], [1035, 33, 1131, 31, "arguments"], [1035, 42, 1131, 40], [1035, 43, 1131, 41], [1035, 47, 1131, 45], [1035, 51, 1131, 49], [1036, 6, 1132, 4, "_this"], [1036, 11, 1132, 9], [1036, 12, 1132, 10, "pressed"], [1036, 19, 1132, 17], [1036, 22, 1132, 20], [1036, 27, 1132, 25], [1036, 28, 1132, 26], [1036, 29, 1132, 27], [1038, 6, 1134, 4], [1038, 13, 1134, 11, "_this"], [1038, 18, 1134, 16], [1039, 4, 1135, 2], [1040, 4, 1136, 2], [1041, 0, 1137, 0], [1042, 0, 1138, 0], [1043, 0, 1139, 0], [1044, 0, 1140, 0], [1046, 4, 1143, 2], [1046, 8, 1143, 6, "_proto"], [1046, 14, 1143, 12], [1046, 17, 1143, 15, "MouseInput"], [1046, 27, 1143, 25], [1046, 28, 1143, 26, "prototype"], [1046, 37, 1143, 35], [1047, 4, 1145, 2, "_proto"], [1047, 10, 1145, 8], [1047, 11, 1145, 9, "handler"], [1047, 18, 1145, 16], [1047, 21, 1145, 19], [1047, 30, 1145, 28, "handler"], [1047, 37, 1145, 35, "handler"], [1047, 38, 1145, 36, "ev"], [1047, 40, 1145, 38], [1047, 42, 1145, 40], [1048, 6, 1146, 4], [1048, 10, 1146, 8, "eventType"], [1048, 19, 1146, 17], [1048, 22, 1146, 20, "MOUSE_INPUT_MAP"], [1048, 37, 1146, 35], [1048, 38, 1146, 36, "ev"], [1048, 40, 1146, 38], [1048, 41, 1146, 39, "type"], [1048, 45, 1146, 43], [1048, 46, 1146, 44], [1048, 47, 1146, 45], [1048, 48, 1146, 46], [1050, 6, 1148, 4], [1050, 10, 1148, 8, "eventType"], [1050, 19, 1148, 17], [1050, 22, 1148, 20, "INPUT_START"], [1050, 33, 1148, 31], [1050, 37, 1148, 35, "ev"], [1050, 39, 1148, 37], [1050, 40, 1148, 38, "button"], [1050, 46, 1148, 44], [1050, 51, 1148, 49], [1050, 52, 1148, 50], [1050, 54, 1148, 52], [1051, 8, 1149, 6], [1051, 12, 1149, 10], [1051, 13, 1149, 11, "pressed"], [1051, 20, 1149, 18], [1051, 23, 1149, 21], [1051, 27, 1149, 25], [1052, 6, 1150, 4], [1053, 6, 1152, 4], [1053, 10, 1152, 8, "eventType"], [1053, 19, 1152, 17], [1053, 22, 1152, 20, "INPUT_MOVE"], [1053, 32, 1152, 30], [1053, 36, 1152, 34, "ev"], [1053, 38, 1152, 36], [1053, 39, 1152, 37, "which"], [1053, 44, 1152, 42], [1053, 49, 1152, 47], [1053, 50, 1152, 48], [1053, 52, 1152, 50], [1054, 8, 1153, 6, "eventType"], [1054, 17, 1153, 15], [1054, 20, 1153, 18, "INPUT_END"], [1054, 29, 1153, 27], [1055, 6, 1154, 4], [1055, 7, 1154, 5], [1055, 8, 1154, 6], [1057, 6, 1157, 4], [1057, 10, 1157, 8], [1057, 11, 1157, 9], [1057, 15, 1157, 13], [1057, 16, 1157, 14, "pressed"], [1057, 23, 1157, 21], [1057, 25, 1157, 23], [1058, 8, 1158, 6], [1059, 6, 1159, 4], [1060, 6, 1161, 4], [1060, 10, 1161, 8, "eventType"], [1060, 19, 1161, 17], [1060, 22, 1161, 20, "INPUT_END"], [1060, 31, 1161, 29], [1060, 33, 1161, 31], [1061, 8, 1162, 6], [1061, 12, 1162, 10], [1061, 13, 1162, 11, "pressed"], [1061, 20, 1162, 18], [1061, 23, 1162, 21], [1061, 28, 1162, 26], [1062, 6, 1163, 4], [1063, 6, 1165, 4], [1063, 10, 1165, 8], [1063, 11, 1165, 9, "callback"], [1063, 19, 1165, 17], [1063, 20, 1165, 18], [1063, 24, 1165, 22], [1063, 25, 1165, 23, "manager"], [1063, 32, 1165, 30], [1063, 34, 1165, 32, "eventType"], [1063, 43, 1165, 41], [1063, 45, 1165, 43], [1064, 8, 1166, 6, "pointers"], [1064, 16, 1166, 14], [1064, 18, 1166, 16], [1064, 19, 1166, 17, "ev"], [1064, 21, 1166, 19], [1064, 22, 1166, 20], [1065, 8, 1167, 6, "changedPointers"], [1065, 23, 1167, 21], [1065, 25, 1167, 23], [1065, 26, 1167, 24, "ev"], [1065, 28, 1167, 26], [1065, 29, 1167, 27], [1066, 8, 1168, 6, "pointerType"], [1066, 19, 1168, 17], [1066, 21, 1168, 19, "INPUT_TYPE_MOUSE"], [1066, 37, 1168, 35], [1067, 8, 1169, 6, "srcEvent"], [1067, 16, 1169, 14], [1067, 18, 1169, 16, "ev"], [1068, 6, 1170, 4], [1068, 7, 1170, 5], [1068, 8, 1170, 6], [1069, 4, 1171, 2], [1069, 5, 1171, 3], [1070, 4, 1173, 2], [1070, 11, 1173, 9, "MouseInput"], [1070, 21, 1173, 19], [1071, 2, 1174, 0], [1071, 3, 1174, 1], [1071, 4, 1174, 2, "Input"], [1071, 9, 1174, 7], [1071, 10, 1174, 8], [1073, 2, 1176, 0], [1074, 0, 1177, 0], [1075, 0, 1178, 0], [1076, 0, 1179, 0], [1077, 0, 1180, 0], [1078, 0, 1181, 0], [1079, 0, 1182, 0], [1080, 0, 1183, 0], [1081, 0, 1184, 0], [1082, 0, 1185, 0], [1084, 2, 1187, 0], [1084, 6, 1187, 4, "DEDUP_TIMEOUT"], [1084, 19, 1187, 17], [1084, 22, 1187, 20], [1084, 26, 1187, 24], [1085, 2, 1188, 0], [1085, 6, 1188, 4, "DEDUP_DISTANCE"], [1085, 20, 1188, 18], [1085, 23, 1188, 21], [1085, 25, 1188, 23], [1086, 2, 1190, 0], [1086, 11, 1190, 9, "setLastTouch"], [1086, 23, 1190, 21, "setLastTouch"], [1086, 24, 1190, 22, "eventData"], [1086, 33, 1190, 31], [1086, 35, 1190, 33], [1087, 4, 1191, 2], [1087, 8, 1191, 6, "_eventData$changedPoi"], [1087, 29, 1191, 27], [1087, 32, 1191, 30, "eventData"], [1087, 41, 1191, 39], [1087, 42, 1191, 40, "changedPointers"], [1087, 57, 1191, 55], [1088, 6, 1192, 6, "touch"], [1088, 11, 1192, 11], [1088, 14, 1192, 14, "_eventData$changedPoi"], [1088, 35, 1192, 35], [1088, 36, 1192, 36], [1088, 37, 1192, 37], [1088, 38, 1192, 38], [1089, 4, 1194, 2], [1089, 8, 1194, 6, "touch"], [1089, 13, 1194, 11], [1089, 14, 1194, 12, "identifier"], [1089, 24, 1194, 22], [1089, 29, 1194, 27], [1089, 33, 1194, 31], [1089, 34, 1194, 32, "primaryTouch"], [1089, 46, 1194, 44], [1089, 48, 1194, 46], [1090, 6, 1195, 4], [1090, 10, 1195, 8, "lastTouch"], [1090, 19, 1195, 17], [1090, 22, 1195, 20], [1091, 8, 1196, 6, "x"], [1091, 9, 1196, 7], [1091, 11, 1196, 9, "touch"], [1091, 16, 1196, 14], [1091, 17, 1196, 15, "clientX"], [1091, 24, 1196, 22], [1092, 8, 1197, 6, "y"], [1092, 9, 1197, 7], [1092, 11, 1197, 9, "touch"], [1092, 16, 1197, 14], [1092, 17, 1197, 15, "clientY"], [1093, 6, 1198, 4], [1093, 7, 1198, 5], [1094, 6, 1199, 4], [1094, 10, 1199, 8, "lts"], [1094, 13, 1199, 11], [1094, 16, 1199, 14], [1094, 20, 1199, 18], [1094, 21, 1199, 19, "lastTouches"], [1094, 32, 1199, 30], [1095, 6, 1200, 4], [1095, 10, 1200, 8], [1095, 11, 1200, 9, "lastTouches"], [1095, 22, 1200, 20], [1095, 23, 1200, 21, "push"], [1095, 27, 1200, 25], [1095, 28, 1200, 26, "lastTouch"], [1095, 37, 1200, 35], [1095, 38, 1200, 36], [1096, 6, 1202, 4], [1096, 10, 1202, 8, "removeLastTouch"], [1096, 25, 1202, 23], [1096, 28, 1202, 26], [1096, 37, 1202, 35, "removeLastTouch"], [1096, 52, 1202, 50, "removeLastTouch"], [1096, 53, 1202, 50], [1096, 55, 1202, 53], [1097, 8, 1203, 6], [1097, 12, 1203, 10, "i"], [1097, 13, 1203, 11], [1097, 16, 1203, 14, "lts"], [1097, 19, 1203, 17], [1097, 20, 1203, 18, "indexOf"], [1097, 27, 1203, 25], [1097, 28, 1203, 26, "lastTouch"], [1097, 37, 1203, 35], [1097, 38, 1203, 36], [1098, 8, 1205, 6], [1098, 12, 1205, 10, "i"], [1098, 13, 1205, 11], [1098, 16, 1205, 14], [1098, 17, 1205, 15], [1098, 18, 1205, 16], [1098, 20, 1205, 18], [1099, 10, 1206, 8, "lts"], [1099, 13, 1206, 11], [1099, 14, 1206, 12, "splice"], [1099, 20, 1206, 18], [1099, 21, 1206, 19, "i"], [1099, 22, 1206, 20], [1099, 24, 1206, 22], [1099, 25, 1206, 23], [1099, 26, 1206, 24], [1100, 8, 1207, 6], [1101, 6, 1208, 4], [1101, 7, 1208, 5], [1102, 6, 1210, 4, "setTimeout"], [1102, 16, 1210, 14], [1102, 17, 1210, 15, "removeLastTouch"], [1102, 32, 1210, 30], [1102, 34, 1210, 32, "DEDUP_TIMEOUT"], [1102, 47, 1210, 45], [1102, 48, 1210, 46], [1103, 4, 1211, 2], [1104, 2, 1212, 0], [1105, 2, 1214, 0], [1105, 11, 1214, 9, "recordTouches"], [1105, 24, 1214, 22, "recordTouches"], [1105, 25, 1214, 23, "eventType"], [1105, 34, 1214, 32], [1105, 36, 1214, 34, "eventData"], [1105, 45, 1214, 43], [1105, 47, 1214, 45], [1106, 4, 1215, 2], [1106, 8, 1215, 6, "eventType"], [1106, 17, 1215, 15], [1106, 20, 1215, 18, "INPUT_START"], [1106, 31, 1215, 29], [1106, 33, 1215, 31], [1107, 6, 1216, 4], [1107, 10, 1216, 8], [1107, 11, 1216, 9, "primaryTouch"], [1107, 23, 1216, 21], [1107, 26, 1216, 24, "eventData"], [1107, 35, 1216, 33], [1107, 36, 1216, 34, "changedPointers"], [1107, 51, 1216, 49], [1107, 52, 1216, 50], [1107, 53, 1216, 51], [1107, 54, 1216, 52], [1107, 55, 1216, 53, "identifier"], [1107, 65, 1216, 63], [1108, 6, 1217, 4, "setLastTouch"], [1108, 18, 1217, 16], [1108, 19, 1217, 17, "call"], [1108, 23, 1217, 21], [1108, 24, 1217, 22], [1108, 28, 1217, 26], [1108, 30, 1217, 28, "eventData"], [1108, 39, 1217, 37], [1108, 40, 1217, 38], [1109, 4, 1218, 2], [1109, 5, 1218, 3], [1109, 11, 1218, 9], [1109, 15, 1218, 13, "eventType"], [1109, 24, 1218, 22], [1109, 28, 1218, 26, "INPUT_END"], [1109, 37, 1218, 35], [1109, 40, 1218, 38, "INPUT_CANCEL"], [1109, 52, 1218, 50], [1109, 53, 1218, 51], [1109, 55, 1218, 53], [1110, 6, 1219, 4, "setLastTouch"], [1110, 18, 1219, 16], [1110, 19, 1219, 17, "call"], [1110, 23, 1219, 21], [1110, 24, 1219, 22], [1110, 28, 1219, 26], [1110, 30, 1219, 28, "eventData"], [1110, 39, 1219, 37], [1110, 40, 1219, 38], [1111, 4, 1220, 2], [1112, 2, 1221, 0], [1113, 2, 1223, 0], [1113, 11, 1223, 9, "isSyntheticEvent"], [1113, 27, 1223, 25, "isSyntheticEvent"], [1113, 28, 1223, 26, "eventData"], [1113, 37, 1223, 35], [1113, 39, 1223, 37], [1114, 4, 1224, 2], [1114, 8, 1224, 6, "x"], [1114, 9, 1224, 7], [1114, 12, 1224, 10, "eventData"], [1114, 21, 1224, 19], [1114, 22, 1224, 20, "srcEvent"], [1114, 30, 1224, 28], [1114, 31, 1224, 29, "clientX"], [1114, 38, 1224, 36], [1115, 4, 1225, 2], [1115, 8, 1225, 6, "y"], [1115, 9, 1225, 7], [1115, 12, 1225, 10, "eventData"], [1115, 21, 1225, 19], [1115, 22, 1225, 20, "srcEvent"], [1115, 30, 1225, 28], [1115, 31, 1225, 29, "clientY"], [1115, 38, 1225, 36], [1116, 4, 1227, 2], [1116, 9, 1227, 7], [1116, 13, 1227, 11, "i"], [1116, 14, 1227, 12], [1116, 17, 1227, 15], [1116, 18, 1227, 16], [1116, 20, 1227, 18, "i"], [1116, 21, 1227, 19], [1116, 24, 1227, 22], [1116, 28, 1227, 26], [1116, 29, 1227, 27, "lastTouches"], [1116, 40, 1227, 38], [1116, 41, 1227, 39, "length"], [1116, 47, 1227, 45], [1116, 49, 1227, 47, "i"], [1116, 50, 1227, 48], [1116, 52, 1227, 50], [1116, 54, 1227, 52], [1117, 6, 1228, 4], [1117, 10, 1228, 8, "t"], [1117, 11, 1228, 9], [1117, 14, 1228, 12], [1117, 18, 1228, 16], [1117, 19, 1228, 17, "lastTouches"], [1117, 30, 1228, 28], [1117, 31, 1228, 29, "i"], [1117, 32, 1228, 30], [1117, 33, 1228, 31], [1118, 6, 1229, 4], [1118, 10, 1229, 8, "dx"], [1118, 12, 1229, 10], [1118, 15, 1229, 13, "Math"], [1118, 19, 1229, 17], [1118, 20, 1229, 18, "abs"], [1118, 23, 1229, 21], [1118, 24, 1229, 22, "x"], [1118, 25, 1229, 23], [1118, 28, 1229, 26, "t"], [1118, 29, 1229, 27], [1118, 30, 1229, 28, "x"], [1118, 31, 1229, 29], [1118, 32, 1229, 30], [1119, 6, 1230, 4], [1119, 10, 1230, 8, "dy"], [1119, 12, 1230, 10], [1119, 15, 1230, 13, "Math"], [1119, 19, 1230, 17], [1119, 20, 1230, 18, "abs"], [1119, 23, 1230, 21], [1119, 24, 1230, 22, "y"], [1119, 25, 1230, 23], [1119, 28, 1230, 26, "t"], [1119, 29, 1230, 27], [1119, 30, 1230, 28, "y"], [1119, 31, 1230, 29], [1119, 32, 1230, 30], [1120, 6, 1232, 4], [1120, 10, 1232, 8, "dx"], [1120, 12, 1232, 10], [1120, 16, 1232, 14, "DEDUP_DISTANCE"], [1120, 30, 1232, 28], [1120, 34, 1232, 32, "dy"], [1120, 36, 1232, 34], [1120, 40, 1232, 38, "DEDUP_DISTANCE"], [1120, 54, 1232, 52], [1120, 56, 1232, 54], [1121, 8, 1233, 6], [1121, 15, 1233, 13], [1121, 19, 1233, 17], [1122, 6, 1234, 4], [1123, 4, 1235, 2], [1124, 4, 1237, 2], [1124, 11, 1237, 9], [1124, 16, 1237, 14], [1125, 2, 1238, 0], [1126, 2, 1240, 0], [1126, 6, 1240, 4, "TouchMouseInput"], [1126, 21, 1240, 19], [1126, 24, 1240, 19, "exports"], [1126, 31, 1240, 19], [1126, 32, 1240, 19, "TouchMouseInput"], [1126, 47, 1240, 19], [1126, 50, 1241, 0], [1127, 2, 1242, 0], [1127, 14, 1242, 12], [1128, 4, 1243, 2], [1128, 8, 1243, 6, "TouchMouseInput"], [1128, 23, 1243, 21], [1128, 26, 1244, 2], [1129, 4, 1245, 2], [1129, 14, 1245, 12, "_Input"], [1129, 20, 1245, 18], [1129, 22, 1245, 20], [1130, 6, 1246, 4, "_inherits<PERSON><PERSON>e"], [1130, 20, 1246, 18], [1130, 21, 1246, 19, "TouchMouseInput"], [1130, 36, 1246, 34], [1130, 38, 1246, 36, "_Input"], [1130, 44, 1246, 42], [1130, 45, 1246, 43], [1131, 6, 1248, 4], [1131, 15, 1248, 13, "TouchMouseInput"], [1131, 30, 1248, 28, "TouchMouseInput"], [1131, 31, 1248, 29, "_manager"], [1131, 39, 1248, 37], [1131, 41, 1248, 39, "callback"], [1131, 49, 1248, 47], [1131, 51, 1248, 49], [1132, 8, 1249, 6], [1132, 12, 1249, 10, "_this"], [1132, 17, 1249, 15], [1133, 8, 1251, 6, "_this"], [1133, 13, 1251, 11], [1133, 16, 1251, 14, "_Input"], [1133, 22, 1251, 20], [1133, 23, 1251, 21, "call"], [1133, 27, 1251, 25], [1133, 28, 1251, 26], [1133, 32, 1251, 30], [1133, 34, 1251, 32, "_manager"], [1133, 42, 1251, 40], [1133, 44, 1251, 42, "callback"], [1133, 52, 1251, 50], [1133, 53, 1251, 51], [1133, 57, 1251, 55], [1133, 61, 1251, 59], [1134, 8, 1253, 6, "_this"], [1134, 13, 1253, 11], [1134, 14, 1253, 12, "handler"], [1134, 21, 1253, 19], [1134, 24, 1253, 22], [1134, 34, 1253, 32, "manager"], [1134, 41, 1253, 39], [1134, 43, 1253, 41, "inputEvent"], [1134, 53, 1253, 51], [1134, 55, 1253, 53, "inputData"], [1134, 64, 1253, 62], [1134, 66, 1253, 64], [1135, 10, 1254, 8], [1135, 14, 1254, 12, "is<PERSON><PERSON>ch"], [1135, 21, 1254, 19], [1135, 24, 1254, 22, "inputData"], [1135, 33, 1254, 31], [1135, 34, 1254, 32, "pointerType"], [1135, 45, 1254, 43], [1135, 50, 1254, 48, "INPUT_TYPE_TOUCH"], [1135, 66, 1254, 64], [1136, 10, 1255, 8], [1136, 14, 1255, 12, "isMouse"], [1136, 21, 1255, 19], [1136, 24, 1255, 22, "inputData"], [1136, 33, 1255, 31], [1136, 34, 1255, 32, "pointerType"], [1136, 45, 1255, 43], [1136, 50, 1255, 48, "INPUT_TYPE_MOUSE"], [1136, 66, 1255, 64], [1137, 10, 1257, 8], [1137, 14, 1257, 12, "isMouse"], [1137, 21, 1257, 19], [1137, 25, 1257, 23, "inputData"], [1137, 34, 1257, 32], [1137, 35, 1257, 33, "sourceCapabilities"], [1137, 53, 1257, 51], [1137, 57, 1257, 55, "inputData"], [1137, 66, 1257, 64], [1137, 67, 1257, 65, "sourceCapabilities"], [1137, 85, 1257, 83], [1137, 86, 1257, 84, "firesTouchEvents"], [1137, 102, 1257, 100], [1137, 104, 1257, 102], [1138, 12, 1258, 10], [1139, 10, 1259, 8], [1139, 11, 1259, 9], [1139, 12, 1259, 10], [1141, 10, 1262, 8], [1141, 14, 1262, 12, "is<PERSON><PERSON>ch"], [1141, 21, 1262, 19], [1141, 23, 1262, 21], [1142, 12, 1263, 10, "recordTouches"], [1142, 25, 1263, 23], [1142, 26, 1263, 24, "call"], [1142, 30, 1263, 28], [1142, 31, 1263, 29, "_assertThisInitialized"], [1142, 53, 1263, 51], [1142, 54, 1263, 52, "_assertThisInitialized"], [1142, 76, 1263, 74], [1142, 77, 1263, 75, "_this"], [1142, 82, 1263, 80], [1142, 83, 1263, 81], [1142, 84, 1263, 82], [1142, 86, 1263, 84, "inputEvent"], [1142, 96, 1263, 94], [1142, 98, 1263, 96, "inputData"], [1142, 107, 1263, 105], [1142, 108, 1263, 106], [1143, 10, 1264, 8], [1143, 11, 1264, 9], [1143, 17, 1264, 15], [1143, 21, 1264, 19, "isMouse"], [1143, 28, 1264, 26], [1143, 32, 1264, 30, "isSyntheticEvent"], [1143, 48, 1264, 46], [1143, 49, 1264, 47, "call"], [1143, 53, 1264, 51], [1143, 54, 1264, 52, "_assertThisInitialized"], [1143, 76, 1264, 74], [1143, 77, 1264, 75, "_assertThisInitialized"], [1143, 99, 1264, 97], [1143, 100, 1264, 98, "_this"], [1143, 105, 1264, 103], [1143, 106, 1264, 104], [1143, 107, 1264, 105], [1143, 109, 1264, 107, "inputData"], [1143, 118, 1264, 116], [1143, 119, 1264, 117], [1143, 121, 1264, 119], [1144, 12, 1265, 10], [1145, 10, 1266, 8], [1146, 10, 1268, 8, "_this"], [1146, 15, 1268, 13], [1146, 16, 1268, 14, "callback"], [1146, 24, 1268, 22], [1146, 25, 1268, 23, "manager"], [1146, 32, 1268, 30], [1146, 34, 1268, 32, "inputEvent"], [1146, 44, 1268, 42], [1146, 46, 1268, 44, "inputData"], [1146, 55, 1268, 53], [1146, 56, 1268, 54], [1147, 8, 1269, 6], [1147, 9, 1269, 7], [1148, 8, 1271, 6, "_this"], [1148, 13, 1271, 11], [1148, 14, 1271, 12, "touch"], [1148, 19, 1271, 17], [1148, 22, 1271, 20], [1148, 26, 1271, 24, "TouchInput"], [1148, 36, 1271, 34], [1148, 37, 1271, 35, "_this"], [1148, 42, 1271, 40], [1148, 43, 1271, 41, "manager"], [1148, 50, 1271, 48], [1148, 52, 1271, 50, "_this"], [1148, 57, 1271, 55], [1148, 58, 1271, 56, "handler"], [1148, 65, 1271, 63], [1148, 66, 1271, 64], [1149, 8, 1272, 6, "_this"], [1149, 13, 1272, 11], [1149, 14, 1272, 12, "mouse"], [1149, 19, 1272, 17], [1149, 22, 1272, 20], [1149, 26, 1272, 24, "MouseInput"], [1149, 36, 1272, 34], [1149, 37, 1272, 35, "_this"], [1149, 42, 1272, 40], [1149, 43, 1272, 41, "manager"], [1149, 50, 1272, 48], [1149, 52, 1272, 50, "_this"], [1149, 57, 1272, 55], [1149, 58, 1272, 56, "handler"], [1149, 65, 1272, 63], [1149, 66, 1272, 64], [1150, 8, 1273, 6, "_this"], [1150, 13, 1273, 11], [1150, 14, 1273, 12, "primaryTouch"], [1150, 26, 1273, 24], [1150, 29, 1273, 27], [1150, 33, 1273, 31], [1151, 8, 1274, 6, "_this"], [1151, 13, 1274, 11], [1151, 14, 1274, 12, "lastTouches"], [1151, 25, 1274, 23], [1151, 28, 1274, 26], [1151, 30, 1274, 28], [1152, 8, 1275, 6], [1152, 15, 1275, 13, "_this"], [1152, 20, 1275, 18], [1153, 6, 1276, 4], [1154, 6, 1277, 4], [1155, 0, 1278, 0], [1156, 0, 1279, 0], [1157, 0, 1280, 0], [1158, 0, 1281, 0], [1159, 0, 1282, 0], [1160, 0, 1283, 0], [1162, 6, 1286, 4], [1162, 10, 1286, 8, "_proto"], [1162, 16, 1286, 14], [1162, 19, 1286, 17, "TouchMouseInput"], [1162, 34, 1286, 32], [1162, 35, 1286, 33, "prototype"], [1162, 44, 1286, 42], [1164, 6, 1288, 4], [1165, 0, 1289, 0], [1166, 0, 1290, 0], [1167, 0, 1291, 0], [1168, 6, 1292, 4, "_proto"], [1168, 12, 1292, 10], [1168, 13, 1292, 11, "destroy"], [1168, 20, 1292, 18], [1168, 23, 1292, 21], [1168, 32, 1292, 30, "destroy"], [1168, 39, 1292, 37, "destroy"], [1168, 40, 1292, 37], [1168, 42, 1292, 40], [1169, 8, 1293, 6], [1169, 12, 1293, 10], [1169, 13, 1293, 11, "touch"], [1169, 18, 1293, 16], [1169, 19, 1293, 17, "destroy"], [1169, 26, 1293, 24], [1169, 27, 1293, 25], [1169, 28, 1293, 26], [1170, 8, 1294, 6], [1170, 12, 1294, 10], [1170, 13, 1294, 11, "mouse"], [1170, 18, 1294, 16], [1170, 19, 1294, 17, "destroy"], [1170, 26, 1294, 24], [1170, 27, 1294, 25], [1170, 28, 1294, 26], [1171, 6, 1295, 4], [1171, 7, 1295, 5], [1172, 6, 1297, 4], [1172, 13, 1297, 11, "TouchMouseInput"], [1172, 28, 1297, 26], [1173, 4, 1298, 2], [1173, 5, 1298, 3], [1173, 6, 1298, 4, "Input"], [1173, 11, 1298, 9], [1173, 12, 1298, 10], [1174, 4, 1300, 2], [1174, 11, 1300, 9, "TouchMouseInput"], [1174, 26, 1300, 24], [1175, 2, 1301, 0], [1175, 3, 1301, 1], [1175, 4, 1301, 2], [1175, 5, 1301, 3], [1177, 2, 1303, 0], [1178, 0, 1304, 0], [1179, 0, 1305, 0], [1180, 0, 1306, 0], [1181, 0, 1307, 0], [1182, 0, 1308, 0], [1183, 0, 1309, 0], [1185, 2, 1311, 0], [1185, 11, 1311, 9, "createInputInstance"], [1185, 30, 1311, 28, "createInputInstance"], [1185, 31, 1311, 29, "manager"], [1185, 38, 1311, 36], [1185, 40, 1311, 38], [1186, 4, 1312, 2], [1186, 8, 1312, 6, "Type"], [1186, 12, 1312, 10], [1186, 13, 1312, 11], [1186, 14, 1312, 12], [1188, 4, 1314, 2], [1188, 8, 1314, 6, "inputClass"], [1188, 18, 1314, 16], [1188, 21, 1314, 19, "manager"], [1188, 28, 1314, 26], [1188, 29, 1314, 27, "options"], [1188, 36, 1314, 34], [1188, 37, 1314, 35, "inputClass"], [1188, 47, 1314, 45], [1189, 4, 1316, 2], [1189, 8, 1316, 6, "inputClass"], [1189, 18, 1316, 16], [1189, 20, 1316, 18], [1190, 6, 1317, 4, "Type"], [1190, 10, 1317, 8], [1190, 13, 1317, 11, "inputClass"], [1190, 23, 1317, 21], [1191, 4, 1318, 2], [1191, 5, 1318, 3], [1191, 11, 1318, 9], [1191, 15, 1318, 13, "SUPPORT_POINTER_EVENTS"], [1191, 37, 1318, 35], [1191, 39, 1318, 37], [1192, 6, 1319, 4, "Type"], [1192, 10, 1319, 8], [1192, 13, 1319, 11, "PointerEventInput"], [1192, 30, 1319, 28], [1193, 4, 1320, 2], [1193, 5, 1320, 3], [1193, 11, 1320, 9], [1193, 15, 1320, 13, "SUPPORT_ONLY_TOUCH"], [1193, 33, 1320, 31], [1193, 35, 1320, 33], [1194, 6, 1321, 4, "Type"], [1194, 10, 1321, 8], [1194, 13, 1321, 11, "TouchInput"], [1194, 23, 1321, 21], [1195, 4, 1322, 2], [1195, 5, 1322, 3], [1195, 11, 1322, 9], [1195, 15, 1322, 13], [1195, 16, 1322, 14, "SUPPORT_TOUCH"], [1195, 29, 1322, 27], [1195, 31, 1322, 29], [1196, 6, 1323, 4, "Type"], [1196, 10, 1323, 8], [1196, 13, 1323, 11, "MouseInput"], [1196, 23, 1323, 21], [1197, 4, 1324, 2], [1197, 5, 1324, 3], [1197, 11, 1324, 9], [1198, 6, 1325, 4, "Type"], [1198, 10, 1325, 8], [1198, 13, 1325, 11, "TouchMouseInput"], [1198, 28, 1325, 26], [1199, 4, 1326, 2], [1200, 4, 1328, 2], [1200, 11, 1328, 9], [1200, 15, 1328, 13, "Type"], [1200, 19, 1328, 17], [1200, 20, 1328, 18, "manager"], [1200, 27, 1328, 25], [1200, 29, 1328, 27, "inputHandler"], [1200, 41, 1328, 39], [1200, 42, 1328, 40], [1201, 2, 1329, 0], [1203, 2, 1331, 0], [1204, 0, 1332, 0], [1205, 0, 1333, 0], [1206, 0, 1334, 0], [1207, 0, 1335, 0], [1208, 0, 1336, 0], [1209, 0, 1337, 0], [1210, 0, 1338, 0], [1211, 0, 1339, 0], [1212, 0, 1340, 0], [1214, 2, 1342, 0], [1214, 11, 1342, 9, "invokeArrayArg"], [1214, 25, 1342, 23, "invokeArrayArg"], [1214, 26, 1342, 24, "arg"], [1214, 29, 1342, 27], [1214, 31, 1342, 29, "fn"], [1214, 33, 1342, 31], [1214, 35, 1342, 33, "context"], [1214, 42, 1342, 40], [1214, 44, 1342, 42], [1215, 4, 1343, 2], [1215, 8, 1343, 6, "Array"], [1215, 13, 1343, 11], [1215, 14, 1343, 12, "isArray"], [1215, 21, 1343, 19], [1215, 22, 1343, 20, "arg"], [1215, 25, 1343, 23], [1215, 26, 1343, 24], [1215, 28, 1343, 26], [1216, 6, 1344, 4, "each"], [1216, 10, 1344, 8], [1216, 11, 1344, 9, "arg"], [1216, 14, 1344, 12], [1216, 16, 1344, 14, "context"], [1216, 23, 1344, 21], [1216, 24, 1344, 22, "fn"], [1216, 26, 1344, 24], [1216, 27, 1344, 25], [1216, 29, 1344, 27, "context"], [1216, 36, 1344, 34], [1216, 37, 1344, 35], [1217, 6, 1345, 4], [1217, 13, 1345, 11], [1217, 17, 1345, 15], [1218, 4, 1346, 2], [1219, 4, 1348, 2], [1219, 11, 1348, 9], [1219, 16, 1348, 14], [1220, 2, 1349, 0], [1221, 2, 1351, 0], [1221, 6, 1351, 4, "STATE_POSSIBLE"], [1221, 20, 1351, 18], [1221, 23, 1351, 18, "exports"], [1221, 30, 1351, 18], [1221, 31, 1351, 18, "STATE_POSSIBLE"], [1221, 45, 1351, 18], [1221, 48, 1351, 21], [1221, 49, 1351, 22], [1222, 2, 1352, 0], [1222, 6, 1352, 4, "STATE_BEGAN"], [1222, 17, 1352, 15], [1222, 20, 1352, 15, "exports"], [1222, 27, 1352, 15], [1222, 28, 1352, 15, "STATE_BEGAN"], [1222, 39, 1352, 15], [1222, 42, 1352, 18], [1222, 43, 1352, 19], [1223, 2, 1353, 0], [1223, 6, 1353, 4, "STATE_CHANGED"], [1223, 19, 1353, 17], [1223, 22, 1353, 17, "exports"], [1223, 29, 1353, 17], [1223, 30, 1353, 17, "STATE_CHANGED"], [1223, 43, 1353, 17], [1223, 46, 1353, 20], [1223, 47, 1353, 21], [1224, 2, 1354, 0], [1224, 6, 1354, 4, "STATE_ENDED"], [1224, 17, 1354, 15], [1224, 20, 1354, 15, "exports"], [1224, 27, 1354, 15], [1224, 28, 1354, 15, "STATE_ENDED"], [1224, 39, 1354, 15], [1224, 42, 1354, 18], [1224, 43, 1354, 19], [1225, 2, 1355, 0], [1225, 6, 1355, 4, "STATE_RECOGNIZED"], [1225, 22, 1355, 20], [1225, 25, 1355, 20, "exports"], [1225, 32, 1355, 20], [1225, 33, 1355, 20, "STATE_RECOGNIZED"], [1225, 49, 1355, 20], [1225, 52, 1355, 23, "STATE_ENDED"], [1225, 63, 1355, 34], [1226, 2, 1356, 0], [1226, 6, 1356, 4, "STATE_CANCELLED"], [1226, 21, 1356, 19], [1226, 24, 1356, 19, "exports"], [1226, 31, 1356, 19], [1226, 32, 1356, 19, "STATE_CANCELLED"], [1226, 47, 1356, 19], [1226, 50, 1356, 22], [1226, 52, 1356, 24], [1227, 2, 1357, 0], [1227, 6, 1357, 4, "STATE_FAILED"], [1227, 18, 1357, 16], [1227, 21, 1357, 16, "exports"], [1227, 28, 1357, 16], [1227, 29, 1357, 16, "STATE_FAILED"], [1227, 41, 1357, 16], [1227, 44, 1357, 19], [1227, 46, 1357, 21], [1229, 2, 1359, 0], [1230, 0, 1360, 0], [1231, 0, 1361, 0], [1232, 0, 1362, 0], [1233, 0, 1363, 0], [1234, 2, 1364, 0], [1234, 6, 1364, 4, "_uniqueId"], [1234, 15, 1364, 13], [1234, 18, 1364, 16], [1234, 19, 1364, 17], [1235, 2, 1365, 0], [1235, 11, 1365, 9, "uniqueId"], [1235, 19, 1365, 17, "uniqueId"], [1235, 20, 1365, 17], [1235, 22, 1365, 20], [1236, 4, 1366, 2], [1236, 11, 1366, 9, "_uniqueId"], [1236, 20, 1366, 18], [1236, 22, 1366, 20], [1237, 2, 1367, 0], [1239, 2, 1369, 0], [1240, 0, 1370, 0], [1241, 0, 1371, 0], [1242, 0, 1372, 0], [1243, 0, 1373, 0], [1244, 0, 1374, 0], [1245, 0, 1375, 0], [1246, 2, 1376, 0], [1246, 11, 1376, 9, "getRecognizerByNameIfManager"], [1246, 39, 1376, 37, "getRecognizerByNameIfManager"], [1246, 40, 1376, 38, "otherRecognizer"], [1246, 55, 1376, 53], [1246, 57, 1376, 55, "recognizer"], [1246, 67, 1376, 65], [1246, 69, 1376, 67], [1247, 4, 1377, 2], [1247, 8, 1377, 6, "manager"], [1247, 15, 1377, 13], [1247, 18, 1377, 16, "recognizer"], [1247, 28, 1377, 26], [1247, 29, 1377, 27, "manager"], [1247, 36, 1377, 34], [1248, 4, 1379, 2], [1248, 8, 1379, 6, "manager"], [1248, 15, 1379, 13], [1248, 17, 1379, 15], [1249, 6, 1380, 4], [1249, 13, 1380, 11, "manager"], [1249, 20, 1380, 18], [1249, 21, 1380, 19, "get"], [1249, 24, 1380, 22], [1249, 25, 1380, 23, "otherRecognizer"], [1249, 40, 1380, 38], [1249, 41, 1380, 39], [1250, 4, 1381, 2], [1251, 4, 1383, 2], [1251, 11, 1383, 9, "otherRecognizer"], [1251, 26, 1383, 24], [1252, 2, 1384, 0], [1254, 2, 1386, 0], [1255, 0, 1387, 0], [1256, 0, 1388, 0], [1257, 0, 1389, 0], [1258, 0, 1390, 0], [1259, 0, 1391, 0], [1261, 2, 1393, 0], [1261, 11, 1393, 9, "stateStr"], [1261, 19, 1393, 17, "stateStr"], [1261, 20, 1393, 18, "state"], [1261, 25, 1393, 23], [1261, 27, 1393, 25], [1262, 4, 1394, 2], [1262, 8, 1394, 6, "state"], [1262, 13, 1394, 11], [1262, 16, 1394, 14, "STATE_CANCELLED"], [1262, 31, 1394, 29], [1262, 33, 1394, 31], [1263, 6, 1395, 4], [1263, 13, 1395, 11], [1263, 21, 1395, 19], [1264, 4, 1396, 2], [1264, 5, 1396, 3], [1264, 11, 1396, 9], [1264, 15, 1396, 13, "state"], [1264, 20, 1396, 18], [1264, 23, 1396, 21, "STATE_ENDED"], [1264, 34, 1396, 32], [1264, 36, 1396, 34], [1265, 6, 1397, 4], [1265, 13, 1397, 11], [1265, 18, 1397, 16], [1266, 4, 1398, 2], [1266, 5, 1398, 3], [1266, 11, 1398, 9], [1266, 15, 1398, 13, "state"], [1266, 20, 1398, 18], [1266, 23, 1398, 21, "STATE_CHANGED"], [1266, 36, 1398, 34], [1266, 38, 1398, 36], [1267, 6, 1399, 4], [1267, 13, 1399, 11], [1267, 19, 1399, 17], [1268, 4, 1400, 2], [1268, 5, 1400, 3], [1268, 11, 1400, 9], [1268, 15, 1400, 13, "state"], [1268, 20, 1400, 18], [1268, 23, 1400, 21, "STATE_BEGAN"], [1268, 34, 1400, 32], [1268, 36, 1400, 34], [1269, 6, 1401, 4], [1269, 13, 1401, 11], [1269, 20, 1401, 18], [1270, 4, 1402, 2], [1271, 4, 1404, 2], [1271, 11, 1404, 9], [1271, 13, 1404, 11], [1272, 2, 1405, 0], [1274, 2, 1407, 0], [1275, 0, 1408, 0], [1276, 0, 1409, 0], [1277, 0, 1410, 0], [1278, 0, 1411, 0], [1279, 0, 1412, 0], [1280, 0, 1413, 0], [1281, 0, 1414, 0], [1282, 0, 1415, 0], [1283, 0, 1416, 0], [1284, 0, 1417, 0], [1285, 0, 1418, 0], [1286, 0, 1419, 0], [1287, 0, 1420, 0], [1288, 0, 1421, 0], [1289, 0, 1422, 0], [1290, 0, 1423, 0], [1291, 0, 1424, 0], [1292, 0, 1425, 0], [1293, 0, 1426, 0], [1294, 0, 1427, 0], [1295, 0, 1428, 0], [1296, 0, 1429, 0], [1297, 0, 1430, 0], [1298, 0, 1431, 0], [1299, 0, 1432, 0], [1300, 0, 1433, 0], [1301, 0, 1434, 0], [1303, 2, 1436, 0], [1304, 0, 1437, 0], [1305, 0, 1438, 0], [1306, 0, 1439, 0], [1307, 0, 1440, 0], [1308, 0, 1441, 0], [1309, 0, 1442, 0], [1311, 2, 1444, 0], [1311, 6, 1444, 4, "Recognizer"], [1311, 16, 1444, 14], [1311, 19, 1444, 14, "exports"], [1311, 26, 1444, 14], [1311, 27, 1444, 14, "Recognizer"], [1311, 37, 1444, 14], [1311, 40, 1445, 0], [1312, 2, 1446, 0], [1312, 14, 1446, 12], [1313, 4, 1447, 2], [1313, 13, 1447, 11, "Recognizer"], [1313, 23, 1447, 21, "Recognizer"], [1313, 24, 1447, 22, "options"], [1313, 31, 1447, 29], [1313, 33, 1447, 31], [1314, 6, 1448, 4], [1314, 10, 1448, 8, "options"], [1314, 17, 1448, 15], [1314, 22, 1448, 20], [1314, 27, 1448, 25], [1314, 28, 1448, 26], [1314, 30, 1448, 28], [1315, 8, 1449, 6, "options"], [1315, 15, 1449, 13], [1315, 18, 1449, 16], [1315, 19, 1449, 17], [1315, 20, 1449, 18], [1316, 6, 1450, 4], [1317, 6, 1452, 4], [1317, 10, 1452, 8], [1317, 11, 1452, 9, "options"], [1317, 18, 1452, 16], [1317, 21, 1452, 19, "_extends"], [1317, 29, 1452, 27], [1317, 30, 1452, 28], [1318, 8, 1453, 6, "enable"], [1318, 14, 1453, 12], [1318, 16, 1453, 14], [1319, 6, 1454, 4], [1319, 7, 1454, 5], [1319, 9, 1454, 7, "options"], [1319, 16, 1454, 14], [1319, 17, 1454, 15], [1320, 6, 1455, 4], [1320, 10, 1455, 8], [1320, 11, 1455, 9, "id"], [1320, 13, 1455, 11], [1320, 16, 1455, 14, "uniqueId"], [1320, 24, 1455, 22], [1320, 25, 1455, 23], [1320, 26, 1455, 24], [1321, 6, 1456, 4], [1321, 10, 1456, 8], [1321, 11, 1456, 9, "manager"], [1321, 18, 1456, 16], [1321, 21, 1456, 19], [1321, 25, 1456, 23], [1321, 26, 1456, 24], [1321, 27, 1456, 25], [1323, 6, 1458, 4], [1323, 10, 1458, 8], [1323, 11, 1458, 9, "state"], [1323, 16, 1458, 14], [1323, 19, 1458, 17, "STATE_POSSIBLE"], [1323, 33, 1458, 31], [1324, 6, 1459, 4], [1324, 10, 1459, 8], [1324, 11, 1459, 9, "simultaneous"], [1324, 23, 1459, 21], [1324, 26, 1459, 24], [1324, 27, 1459, 25], [1324, 28, 1459, 26], [1325, 6, 1460, 4], [1325, 10, 1460, 8], [1325, 11, 1460, 9, "requireFail"], [1325, 22, 1460, 20], [1325, 25, 1460, 23], [1325, 27, 1460, 25], [1326, 4, 1461, 2], [1327, 4, 1462, 2], [1328, 0, 1463, 0], [1329, 0, 1464, 0], [1330, 0, 1465, 0], [1331, 0, 1466, 0], [1332, 0, 1467, 0], [1334, 4, 1470, 2], [1334, 8, 1470, 6, "_proto"], [1334, 14, 1470, 12], [1334, 17, 1470, 15, "Recognizer"], [1334, 27, 1470, 25], [1334, 28, 1470, 26, "prototype"], [1334, 37, 1470, 35], [1335, 4, 1472, 2, "_proto"], [1335, 10, 1472, 8], [1335, 11, 1472, 9, "set"], [1335, 14, 1472, 12], [1335, 17, 1472, 15], [1335, 26, 1472, 24, "set"], [1335, 29, 1472, 27, "set"], [1335, 30, 1472, 28, "options"], [1335, 37, 1472, 35], [1335, 39, 1472, 37], [1336, 6, 1473, 4, "assign$1"], [1336, 14, 1473, 12], [1336, 15, 1473, 13], [1336, 19, 1473, 17], [1336, 20, 1473, 18, "options"], [1336, 27, 1473, 25], [1336, 29, 1473, 27, "options"], [1336, 36, 1473, 34], [1336, 37, 1473, 35], [1336, 38, 1473, 36], [1336, 39, 1473, 37], [1338, 6, 1475, 4], [1338, 10, 1475, 8], [1338, 11, 1475, 9, "manager"], [1338, 18, 1475, 16], [1338, 22, 1475, 20], [1338, 26, 1475, 24], [1338, 27, 1475, 25, "manager"], [1338, 34, 1475, 32], [1338, 35, 1475, 33, "touchAction"], [1338, 46, 1475, 44], [1338, 47, 1475, 45, "update"], [1338, 53, 1475, 51], [1338, 54, 1475, 52], [1338, 55, 1475, 53], [1339, 6, 1476, 4], [1339, 13, 1476, 11], [1339, 17, 1476, 15], [1340, 4, 1477, 2], [1340, 5, 1477, 3], [1341, 4, 1478, 2], [1342, 0, 1479, 0], [1343, 0, 1480, 0], [1344, 0, 1481, 0], [1345, 0, 1482, 0], [1346, 0, 1483, 0], [1348, 4, 1486, 2, "_proto"], [1348, 10, 1486, 8], [1348, 11, 1486, 9, "recognizeWith"], [1348, 24, 1486, 22], [1348, 27, 1486, 25], [1348, 36, 1486, 34, "recognizeWith"], [1348, 49, 1486, 47, "recognizeWith"], [1348, 50, 1486, 48, "otherRecognizer"], [1348, 65, 1486, 63], [1348, 67, 1486, 65], [1349, 6, 1487, 4], [1349, 10, 1487, 8, "invokeArrayArg"], [1349, 24, 1487, 22], [1349, 25, 1487, 23, "otherRecognizer"], [1349, 40, 1487, 38], [1349, 42, 1487, 40], [1349, 57, 1487, 55], [1349, 59, 1487, 57], [1349, 63, 1487, 61], [1349, 64, 1487, 62], [1349, 66, 1487, 64], [1350, 8, 1488, 6], [1350, 15, 1488, 13], [1350, 19, 1488, 17], [1351, 6, 1489, 4], [1352, 6, 1491, 4], [1352, 10, 1491, 8, "simultaneous"], [1352, 22, 1491, 20], [1352, 25, 1491, 23], [1352, 29, 1491, 27], [1352, 30, 1491, 28, "simultaneous"], [1352, 42, 1491, 40], [1353, 6, 1492, 4, "otherRecognizer"], [1353, 21, 1492, 19], [1353, 24, 1492, 22, "getRecognizerByNameIfManager"], [1353, 52, 1492, 50], [1353, 53, 1492, 51, "otherRecognizer"], [1353, 68, 1492, 66], [1353, 70, 1492, 68], [1353, 74, 1492, 72], [1353, 75, 1492, 73], [1354, 6, 1494, 4], [1354, 10, 1494, 8], [1354, 11, 1494, 9, "simultaneous"], [1354, 23, 1494, 21], [1354, 24, 1494, 22, "otherRecognizer"], [1354, 39, 1494, 37], [1354, 40, 1494, 38, "id"], [1354, 42, 1494, 40], [1354, 43, 1494, 41], [1354, 45, 1494, 43], [1355, 8, 1495, 6, "simultaneous"], [1355, 20, 1495, 18], [1355, 21, 1495, 19, "otherRecognizer"], [1355, 36, 1495, 34], [1355, 37, 1495, 35, "id"], [1355, 39, 1495, 37], [1355, 40, 1495, 38], [1355, 43, 1495, 41, "otherRecognizer"], [1355, 58, 1495, 56], [1356, 8, 1496, 6, "otherRecognizer"], [1356, 23, 1496, 21], [1356, 24, 1496, 22, "recognizeWith"], [1356, 37, 1496, 35], [1356, 38, 1496, 36], [1356, 42, 1496, 40], [1356, 43, 1496, 41], [1357, 6, 1497, 4], [1358, 6, 1499, 4], [1358, 13, 1499, 11], [1358, 17, 1499, 15], [1359, 4, 1500, 2], [1359, 5, 1500, 3], [1360, 4, 1501, 2], [1361, 0, 1502, 0], [1362, 0, 1503, 0], [1363, 0, 1504, 0], [1364, 0, 1505, 0], [1365, 0, 1506, 0], [1367, 4, 1509, 2, "_proto"], [1367, 10, 1509, 8], [1367, 11, 1509, 9, "dropRecognizeWith"], [1367, 28, 1509, 26], [1367, 31, 1509, 29], [1367, 40, 1509, 38, "dropRecognizeWith"], [1367, 57, 1509, 55, "dropRecognizeWith"], [1367, 58, 1509, 56, "otherRecognizer"], [1367, 73, 1509, 71], [1367, 75, 1509, 73], [1368, 6, 1510, 4], [1368, 10, 1510, 8, "invokeArrayArg"], [1368, 24, 1510, 22], [1368, 25, 1510, 23, "otherRecognizer"], [1368, 40, 1510, 38], [1368, 42, 1510, 40], [1368, 61, 1510, 59], [1368, 63, 1510, 61], [1368, 67, 1510, 65], [1368, 68, 1510, 66], [1368, 70, 1510, 68], [1369, 8, 1511, 6], [1369, 15, 1511, 13], [1369, 19, 1511, 17], [1370, 6, 1512, 4], [1371, 6, 1514, 4, "otherRecognizer"], [1371, 21, 1514, 19], [1371, 24, 1514, 22, "getRecognizerByNameIfManager"], [1371, 52, 1514, 50], [1371, 53, 1514, 51, "otherRecognizer"], [1371, 68, 1514, 66], [1371, 70, 1514, 68], [1371, 74, 1514, 72], [1371, 75, 1514, 73], [1372, 6, 1515, 4], [1372, 13, 1515, 11], [1372, 17, 1515, 15], [1372, 18, 1515, 16, "simultaneous"], [1372, 30, 1515, 28], [1372, 31, 1515, 29, "otherRecognizer"], [1372, 46, 1515, 44], [1372, 47, 1515, 45, "id"], [1372, 49, 1515, 47], [1372, 50, 1515, 48], [1373, 6, 1516, 4], [1373, 13, 1516, 11], [1373, 17, 1516, 15], [1374, 4, 1517, 2], [1374, 5, 1517, 3], [1375, 4, 1518, 2], [1376, 0, 1519, 0], [1377, 0, 1520, 0], [1378, 0, 1521, 0], [1379, 0, 1522, 0], [1380, 0, 1523, 0], [1382, 4, 1526, 2, "_proto"], [1382, 10, 1526, 8], [1382, 11, 1526, 9, "requireFailure"], [1382, 25, 1526, 23], [1382, 28, 1526, 26], [1382, 37, 1526, 35, "requireFailure"], [1382, 51, 1526, 49, "requireFailure"], [1382, 52, 1526, 50, "otherRecognizer"], [1382, 67, 1526, 65], [1382, 69, 1526, 67], [1383, 6, 1527, 4], [1383, 10, 1527, 8, "invokeArrayArg"], [1383, 24, 1527, 22], [1383, 25, 1527, 23, "otherRecognizer"], [1383, 40, 1527, 38], [1383, 42, 1527, 40], [1383, 58, 1527, 56], [1383, 60, 1527, 58], [1383, 64, 1527, 62], [1383, 65, 1527, 63], [1383, 67, 1527, 65], [1384, 8, 1528, 6], [1384, 15, 1528, 13], [1384, 19, 1528, 17], [1385, 6, 1529, 4], [1386, 6, 1531, 4], [1386, 10, 1531, 8, "requireFail"], [1386, 21, 1531, 19], [1386, 24, 1531, 22], [1386, 28, 1531, 26], [1386, 29, 1531, 27, "requireFail"], [1386, 40, 1531, 38], [1387, 6, 1532, 4, "otherRecognizer"], [1387, 21, 1532, 19], [1387, 24, 1532, 22, "getRecognizerByNameIfManager"], [1387, 52, 1532, 50], [1387, 53, 1532, 51, "otherRecognizer"], [1387, 68, 1532, 66], [1387, 70, 1532, 68], [1387, 74, 1532, 72], [1387, 75, 1532, 73], [1388, 6, 1534, 4], [1388, 10, 1534, 8, "inArray"], [1388, 17, 1534, 15], [1388, 18, 1534, 16, "requireFail"], [1388, 29, 1534, 27], [1388, 31, 1534, 29, "otherRecognizer"], [1388, 46, 1534, 44], [1388, 47, 1534, 45], [1388, 52, 1534, 50], [1388, 53, 1534, 51], [1388, 54, 1534, 52], [1388, 56, 1534, 54], [1389, 8, 1535, 6, "requireFail"], [1389, 19, 1535, 17], [1389, 20, 1535, 18, "push"], [1389, 24, 1535, 22], [1389, 25, 1535, 23, "otherRecognizer"], [1389, 40, 1535, 38], [1389, 41, 1535, 39], [1390, 8, 1536, 6, "otherRecognizer"], [1390, 23, 1536, 21], [1390, 24, 1536, 22, "requireFailure"], [1390, 38, 1536, 36], [1390, 39, 1536, 37], [1390, 43, 1536, 41], [1390, 44, 1536, 42], [1391, 6, 1537, 4], [1392, 6, 1539, 4], [1392, 13, 1539, 11], [1392, 17, 1539, 15], [1393, 4, 1540, 2], [1393, 5, 1540, 3], [1394, 4, 1541, 2], [1395, 0, 1542, 0], [1396, 0, 1543, 0], [1397, 0, 1544, 0], [1398, 0, 1545, 0], [1399, 0, 1546, 0], [1401, 4, 1549, 2, "_proto"], [1401, 10, 1549, 8], [1401, 11, 1549, 9, "dropRequireFailure"], [1401, 29, 1549, 27], [1401, 32, 1549, 30], [1401, 41, 1549, 39, "dropRequireFailure"], [1401, 59, 1549, 57, "dropRequireFailure"], [1401, 60, 1549, 58, "otherRecognizer"], [1401, 75, 1549, 73], [1401, 77, 1549, 75], [1402, 6, 1550, 4], [1402, 10, 1550, 8, "invokeArrayArg"], [1402, 24, 1550, 22], [1402, 25, 1550, 23, "otherRecognizer"], [1402, 40, 1550, 38], [1402, 42, 1550, 40], [1402, 62, 1550, 60], [1402, 64, 1550, 62], [1402, 68, 1550, 66], [1402, 69, 1550, 67], [1402, 71, 1550, 69], [1403, 8, 1551, 6], [1403, 15, 1551, 13], [1403, 19, 1551, 17], [1404, 6, 1552, 4], [1405, 6, 1554, 4, "otherRecognizer"], [1405, 21, 1554, 19], [1405, 24, 1554, 22, "getRecognizerByNameIfManager"], [1405, 52, 1554, 50], [1405, 53, 1554, 51, "otherRecognizer"], [1405, 68, 1554, 66], [1405, 70, 1554, 68], [1405, 74, 1554, 72], [1405, 75, 1554, 73], [1406, 6, 1555, 4], [1406, 10, 1555, 8, "index"], [1406, 15, 1555, 13], [1406, 18, 1555, 16, "inArray"], [1406, 25, 1555, 23], [1406, 26, 1555, 24], [1406, 30, 1555, 28], [1406, 31, 1555, 29, "requireFail"], [1406, 42, 1555, 40], [1406, 44, 1555, 42, "otherRecognizer"], [1406, 59, 1555, 57], [1406, 60, 1555, 58], [1407, 6, 1557, 4], [1407, 10, 1557, 8, "index"], [1407, 15, 1557, 13], [1407, 18, 1557, 16], [1407, 19, 1557, 17], [1407, 20, 1557, 18], [1407, 22, 1557, 20], [1408, 8, 1558, 6], [1408, 12, 1558, 10], [1408, 13, 1558, 11, "requireFail"], [1408, 24, 1558, 22], [1408, 25, 1558, 23, "splice"], [1408, 31, 1558, 29], [1408, 32, 1558, 30, "index"], [1408, 37, 1558, 35], [1408, 39, 1558, 37], [1408, 40, 1558, 38], [1408, 41, 1558, 39], [1409, 6, 1559, 4], [1410, 6, 1561, 4], [1410, 13, 1561, 11], [1410, 17, 1561, 15], [1411, 4, 1562, 2], [1411, 5, 1562, 3], [1412, 4, 1563, 2], [1413, 0, 1564, 0], [1414, 0, 1565, 0], [1415, 0, 1566, 0], [1416, 0, 1567, 0], [1418, 4, 1570, 2, "_proto"], [1418, 10, 1570, 8], [1418, 11, 1570, 9, "hasRequireFailures"], [1418, 29, 1570, 27], [1418, 32, 1570, 30], [1418, 41, 1570, 39, "hasRequireFailures"], [1418, 59, 1570, 57, "hasRequireFailures"], [1418, 60, 1570, 57], [1418, 62, 1570, 60], [1419, 6, 1571, 4], [1419, 13, 1571, 11], [1419, 17, 1571, 15], [1419, 18, 1571, 16, "requireFail"], [1419, 29, 1571, 27], [1419, 30, 1571, 28, "length"], [1419, 36, 1571, 34], [1419, 39, 1571, 37], [1419, 40, 1571, 38], [1420, 4, 1572, 2], [1420, 5, 1572, 3], [1421, 4, 1573, 2], [1422, 0, 1574, 0], [1423, 0, 1575, 0], [1424, 0, 1576, 0], [1425, 0, 1577, 0], [1426, 0, 1578, 0], [1428, 4, 1581, 2, "_proto"], [1428, 10, 1581, 8], [1428, 11, 1581, 9, "canRecognizeWith"], [1428, 27, 1581, 25], [1428, 30, 1581, 28], [1428, 39, 1581, 37, "canRecognizeWith"], [1428, 55, 1581, 53, "canRecognizeWith"], [1428, 56, 1581, 54, "otherRecognizer"], [1428, 71, 1581, 69], [1428, 73, 1581, 71], [1429, 6, 1582, 4], [1429, 13, 1582, 11], [1429, 14, 1582, 12], [1429, 15, 1582, 13], [1429, 19, 1582, 17], [1429, 20, 1582, 18, "simultaneous"], [1429, 32, 1582, 30], [1429, 33, 1582, 31, "otherRecognizer"], [1429, 48, 1582, 46], [1429, 49, 1582, 47, "id"], [1429, 51, 1582, 49], [1429, 52, 1582, 50], [1430, 4, 1583, 2], [1430, 5, 1583, 3], [1431, 4, 1584, 2], [1432, 0, 1585, 0], [1433, 0, 1586, 0], [1434, 0, 1587, 0], [1435, 0, 1588, 0], [1436, 0, 1589, 0], [1438, 4, 1592, 2, "_proto"], [1438, 10, 1592, 8], [1438, 11, 1592, 9, "emit"], [1438, 15, 1592, 13], [1438, 18, 1592, 16], [1438, 27, 1592, 25, "emit"], [1438, 31, 1592, 29, "emit"], [1438, 32, 1592, 30, "input"], [1438, 37, 1592, 35], [1438, 39, 1592, 37], [1439, 6, 1593, 4], [1439, 10, 1593, 8, "self"], [1439, 14, 1593, 12], [1439, 17, 1593, 15], [1439, 21, 1593, 19], [1440, 6, 1594, 4], [1440, 10, 1594, 8, "state"], [1440, 15, 1594, 13], [1440, 18, 1594, 16], [1440, 22, 1594, 20], [1440, 23, 1594, 21, "state"], [1440, 28, 1594, 26], [1441, 6, 1596, 4], [1441, 15, 1596, 13, "emit"], [1441, 19, 1596, 17, "emit"], [1441, 20, 1596, 18, "event"], [1441, 25, 1596, 23], [1441, 27, 1596, 25], [1442, 8, 1597, 6, "self"], [1442, 12, 1597, 10], [1442, 13, 1597, 11, "manager"], [1442, 20, 1597, 18], [1442, 21, 1597, 19, "emit"], [1442, 25, 1597, 23], [1442, 26, 1597, 24, "event"], [1442, 31, 1597, 29], [1442, 33, 1597, 31, "input"], [1442, 38, 1597, 36], [1442, 39, 1597, 37], [1443, 6, 1598, 4], [1443, 7, 1598, 5], [1443, 8, 1598, 6], [1445, 6, 1601, 4], [1445, 10, 1601, 8, "state"], [1445, 15, 1601, 13], [1445, 18, 1601, 16, "STATE_ENDED"], [1445, 29, 1601, 27], [1445, 31, 1601, 29], [1446, 8, 1602, 6, "emit"], [1446, 12, 1602, 10], [1446, 13, 1602, 11, "self"], [1446, 17, 1602, 15], [1446, 18, 1602, 16, "options"], [1446, 25, 1602, 23], [1446, 26, 1602, 24, "event"], [1446, 31, 1602, 29], [1446, 34, 1602, 32, "stateStr"], [1446, 42, 1602, 40], [1446, 43, 1602, 41, "state"], [1446, 48, 1602, 46], [1446, 49, 1602, 47], [1446, 50, 1602, 48], [1447, 6, 1603, 4], [1448, 6, 1605, 4, "emit"], [1448, 10, 1605, 8], [1448, 11, 1605, 9, "self"], [1448, 15, 1605, 13], [1448, 16, 1605, 14, "options"], [1448, 23, 1605, 21], [1448, 24, 1605, 22, "event"], [1448, 29, 1605, 27], [1448, 30, 1605, 28], [1448, 31, 1605, 29], [1448, 32, 1605, 30], [1450, 6, 1607, 4], [1450, 10, 1607, 8, "input"], [1450, 15, 1607, 13], [1450, 16, 1607, 14, "additionalEvent"], [1450, 31, 1607, 29], [1450, 33, 1607, 31], [1451, 8, 1608, 6], [1452, 8, 1609, 6, "emit"], [1452, 12, 1609, 10], [1452, 13, 1609, 11, "input"], [1452, 18, 1609, 16], [1452, 19, 1609, 17, "additionalEvent"], [1452, 34, 1609, 32], [1452, 35, 1609, 33], [1453, 6, 1610, 4], [1453, 7, 1610, 5], [1453, 8, 1610, 6], [1455, 6, 1613, 4], [1455, 10, 1613, 8, "state"], [1455, 15, 1613, 13], [1455, 19, 1613, 17, "STATE_ENDED"], [1455, 30, 1613, 28], [1455, 32, 1613, 30], [1456, 8, 1614, 6, "emit"], [1456, 12, 1614, 10], [1456, 13, 1614, 11, "self"], [1456, 17, 1614, 15], [1456, 18, 1614, 16, "options"], [1456, 25, 1614, 23], [1456, 26, 1614, 24, "event"], [1456, 31, 1614, 29], [1456, 34, 1614, 32, "stateStr"], [1456, 42, 1614, 40], [1456, 43, 1614, 41, "state"], [1456, 48, 1614, 46], [1456, 49, 1614, 47], [1456, 50, 1614, 48], [1457, 6, 1615, 4], [1458, 4, 1616, 2], [1458, 5, 1616, 3], [1459, 4, 1617, 2], [1460, 0, 1618, 0], [1461, 0, 1619, 0], [1462, 0, 1620, 0], [1463, 0, 1621, 0], [1464, 0, 1622, 0], [1465, 0, 1623, 0], [1467, 4, 1626, 2, "_proto"], [1467, 10, 1626, 8], [1467, 11, 1626, 9, "tryEmit"], [1467, 18, 1626, 16], [1467, 21, 1626, 19], [1467, 30, 1626, 28, "tryEmit"], [1467, 37, 1626, 35, "tryEmit"], [1467, 38, 1626, 36, "input"], [1467, 43, 1626, 41], [1467, 45, 1626, 43], [1468, 6, 1627, 4], [1468, 10, 1627, 8], [1468, 14, 1627, 12], [1468, 15, 1627, 13, "canEmit"], [1468, 22, 1627, 20], [1468, 23, 1627, 21], [1468, 24, 1627, 22], [1468, 26, 1627, 24], [1469, 8, 1628, 6], [1469, 15, 1628, 13], [1469, 19, 1628, 17], [1469, 20, 1628, 18, "emit"], [1469, 24, 1628, 22], [1469, 25, 1628, 23, "input"], [1469, 30, 1628, 28], [1469, 31, 1628, 29], [1470, 6, 1629, 4], [1470, 7, 1629, 5], [1470, 8, 1629, 6], [1472, 6, 1632, 4], [1472, 10, 1632, 8], [1472, 11, 1632, 9, "state"], [1472, 16, 1632, 14], [1472, 19, 1632, 17, "STATE_FAILED"], [1472, 31, 1632, 29], [1473, 4, 1633, 2], [1473, 5, 1633, 3], [1474, 4, 1634, 2], [1475, 0, 1635, 0], [1476, 0, 1636, 0], [1477, 0, 1637, 0], [1478, 0, 1638, 0], [1480, 4, 1641, 2, "_proto"], [1480, 10, 1641, 8], [1480, 11, 1641, 9, "canEmit"], [1480, 18, 1641, 16], [1480, 21, 1641, 19], [1480, 30, 1641, 28, "canEmit"], [1480, 37, 1641, 35, "canEmit"], [1480, 38, 1641, 35], [1480, 40, 1641, 38], [1481, 6, 1642, 4], [1481, 10, 1642, 8, "i"], [1481, 11, 1642, 9], [1481, 14, 1642, 12], [1481, 15, 1642, 13], [1482, 6, 1644, 4], [1482, 13, 1644, 11, "i"], [1482, 14, 1644, 12], [1482, 17, 1644, 15], [1482, 21, 1644, 19], [1482, 22, 1644, 20, "requireFail"], [1482, 33, 1644, 31], [1482, 34, 1644, 32, "length"], [1482, 40, 1644, 38], [1482, 42, 1644, 40], [1483, 8, 1645, 6], [1483, 12, 1645, 10], [1483, 14, 1645, 12], [1483, 18, 1645, 16], [1483, 19, 1645, 17, "requireFail"], [1483, 30, 1645, 28], [1483, 31, 1645, 29, "i"], [1483, 32, 1645, 30], [1483, 33, 1645, 31], [1483, 34, 1645, 32, "state"], [1483, 39, 1645, 37], [1483, 43, 1645, 41, "STATE_FAILED"], [1483, 55, 1645, 53], [1483, 58, 1645, 56, "STATE_POSSIBLE"], [1483, 72, 1645, 70], [1483, 73, 1645, 71], [1483, 74, 1645, 72], [1483, 76, 1645, 74], [1484, 10, 1646, 8], [1484, 17, 1646, 15], [1484, 22, 1646, 20], [1485, 8, 1647, 6], [1486, 8, 1649, 6, "i"], [1486, 9, 1649, 7], [1486, 11, 1649, 9], [1487, 6, 1650, 4], [1488, 6, 1652, 4], [1488, 13, 1652, 11], [1488, 17, 1652, 15], [1489, 4, 1653, 2], [1489, 5, 1653, 3], [1490, 4, 1654, 2], [1491, 0, 1655, 0], [1492, 0, 1656, 0], [1493, 0, 1657, 0], [1494, 0, 1658, 0], [1496, 4, 1661, 2, "_proto"], [1496, 10, 1661, 8], [1496, 11, 1661, 9, "recognize"], [1496, 20, 1661, 18], [1496, 23, 1661, 21], [1496, 32, 1661, 30, "recognize"], [1496, 41, 1661, 39, "recognize"], [1496, 42, 1661, 40, "inputData"], [1496, 51, 1661, 49], [1496, 53, 1661, 51], [1497, 6, 1662, 4], [1498, 6, 1663, 4], [1499, 6, 1664, 4], [1499, 10, 1664, 8, "inputDataClone"], [1499, 24, 1664, 22], [1499, 27, 1664, 25, "assign$1"], [1499, 35, 1664, 33], [1499, 36, 1664, 34], [1499, 37, 1664, 35], [1499, 38, 1664, 36], [1499, 40, 1664, 38, "inputData"], [1499, 49, 1664, 47], [1499, 50, 1664, 48], [1499, 51, 1664, 49], [1499, 52, 1664, 50], [1501, 6, 1666, 4], [1501, 10, 1666, 8], [1501, 11, 1666, 9, "boolOrFn"], [1501, 19, 1666, 17], [1501, 20, 1666, 18], [1501, 24, 1666, 22], [1501, 25, 1666, 23, "options"], [1501, 32, 1666, 30], [1501, 33, 1666, 31, "enable"], [1501, 39, 1666, 37], [1501, 41, 1666, 39], [1501, 42, 1666, 40], [1501, 46, 1666, 44], [1501, 48, 1666, 46, "inputDataClone"], [1501, 62, 1666, 60], [1501, 63, 1666, 61], [1501, 64, 1666, 62], [1501, 66, 1666, 64], [1502, 8, 1667, 6], [1502, 12, 1667, 10], [1502, 13, 1667, 11, "reset"], [1502, 18, 1667, 16], [1502, 19, 1667, 17], [1502, 20, 1667, 18], [1503, 8, 1668, 6], [1503, 12, 1668, 10], [1503, 13, 1668, 11, "state"], [1503, 18, 1668, 16], [1503, 21, 1668, 19, "STATE_FAILED"], [1503, 33, 1668, 31], [1504, 8, 1669, 6], [1505, 6, 1670, 4], [1505, 7, 1670, 5], [1505, 8, 1670, 6], [1507, 6, 1673, 4], [1507, 10, 1673, 8], [1507, 14, 1673, 12], [1507, 15, 1673, 13, "state"], [1507, 20, 1673, 18], [1507, 24, 1673, 22, "STATE_RECOGNIZED"], [1507, 40, 1673, 38], [1507, 43, 1673, 41, "STATE_CANCELLED"], [1507, 58, 1673, 56], [1507, 61, 1673, 59, "STATE_FAILED"], [1507, 73, 1673, 71], [1507, 74, 1673, 72], [1507, 76, 1673, 74], [1508, 8, 1674, 6], [1508, 12, 1674, 10], [1508, 13, 1674, 11, "state"], [1508, 18, 1674, 16], [1508, 21, 1674, 19, "STATE_POSSIBLE"], [1508, 35, 1674, 33], [1509, 6, 1675, 4], [1510, 6, 1677, 4], [1510, 10, 1677, 8], [1510, 11, 1677, 9, "state"], [1510, 16, 1677, 14], [1510, 19, 1677, 17], [1510, 23, 1677, 21], [1510, 24, 1677, 22, "process"], [1510, 31, 1677, 29], [1510, 32, 1677, 30, "inputDataClone"], [1510, 46, 1677, 44], [1510, 47, 1677, 45], [1510, 48, 1677, 46], [1510, 49, 1677, 47], [1511, 6, 1678, 4], [1513, 6, 1680, 4], [1513, 10, 1680, 8], [1513, 14, 1680, 12], [1513, 15, 1680, 13, "state"], [1513, 20, 1680, 18], [1513, 24, 1680, 22, "STATE_BEGAN"], [1513, 35, 1680, 33], [1513, 38, 1680, 36, "STATE_CHANGED"], [1513, 51, 1680, 49], [1513, 54, 1680, 52, "STATE_ENDED"], [1513, 65, 1680, 63], [1513, 68, 1680, 66, "STATE_CANCELLED"], [1513, 83, 1680, 81], [1513, 84, 1680, 82], [1513, 86, 1680, 84], [1514, 8, 1681, 6], [1514, 12, 1681, 10], [1514, 13, 1681, 11, "tryEmit"], [1514, 20, 1681, 18], [1514, 21, 1681, 19, "inputDataClone"], [1514, 35, 1681, 33], [1514, 36, 1681, 34], [1515, 6, 1682, 4], [1516, 4, 1683, 2], [1516, 5, 1683, 3], [1517, 4, 1684, 2], [1518, 0, 1685, 0], [1519, 0, 1686, 0], [1520, 0, 1687, 0], [1521, 0, 1688, 0], [1522, 0, 1689, 0], [1523, 0, 1690, 0], [1524, 0, 1691, 0], [1526, 4, 1693, 2], [1528, 4, 1696, 2, "_proto"], [1528, 10, 1696, 8], [1528, 11, 1696, 9, "process"], [1528, 18, 1696, 16], [1528, 21, 1696, 19], [1528, 30, 1696, 28, "process"], [1528, 37, 1696, 35, "process"], [1528, 38, 1696, 36, "inputData"], [1528, 47, 1696, 45], [1528, 49, 1696, 47], [1528, 50, 1696, 48], [1528, 51, 1696, 49], [1529, 4, 1697, 2], [1531, 4, 1699, 2], [1532, 0, 1700, 0], [1533, 0, 1701, 0], [1534, 0, 1702, 0], [1535, 0, 1703, 0], [1536, 0, 1704, 0], [1538, 4, 1707, 2, "_proto"], [1538, 10, 1707, 8], [1538, 11, 1707, 9, "getTouchAction"], [1538, 25, 1707, 23], [1538, 28, 1707, 26], [1538, 37, 1707, 35, "getTouchAction"], [1538, 51, 1707, 49, "getTouchAction"], [1538, 52, 1707, 49], [1538, 54, 1707, 52], [1538, 55, 1707, 53], [1538, 56, 1707, 54], [1539, 4, 1708, 2], [1540, 0, 1709, 0], [1541, 0, 1710, 0], [1542, 0, 1711, 0], [1543, 0, 1712, 0], [1544, 0, 1713, 0], [1546, 4, 1716, 2, "_proto"], [1546, 10, 1716, 8], [1546, 11, 1716, 9, "reset"], [1546, 16, 1716, 14], [1546, 19, 1716, 17], [1546, 28, 1716, 26, "reset"], [1546, 33, 1716, 31, "reset"], [1546, 34, 1716, 31], [1546, 36, 1716, 34], [1546, 37, 1716, 35], [1546, 38, 1716, 36], [1547, 4, 1718, 2], [1547, 11, 1718, 9, "Recognizer"], [1547, 21, 1718, 19], [1548, 2, 1719, 0], [1548, 3, 1719, 1], [1548, 4, 1719, 2], [1548, 5, 1719, 3], [1550, 2, 1721, 0], [1551, 0, 1722, 0], [1552, 0, 1723, 0], [1553, 0, 1724, 0], [1554, 0, 1725, 0], [1555, 0, 1726, 0], [1556, 0, 1727, 0], [1557, 0, 1728, 0], [1558, 0, 1729, 0], [1559, 0, 1730, 0], [1560, 0, 1731, 0], [1562, 2, 1733, 0], [1562, 6, 1733, 4, "TapRecognizer"], [1562, 19, 1733, 17], [1562, 22, 1733, 17, "exports"], [1562, 29, 1733, 17], [1562, 30, 1733, 17, "Tap"], [1562, 33, 1733, 17], [1562, 36, 1734, 0], [1563, 2, 1735, 0], [1563, 12, 1735, 10, "_Recognizer"], [1563, 23, 1735, 21], [1563, 25, 1735, 23], [1564, 4, 1736, 2, "_inherits<PERSON><PERSON>e"], [1564, 18, 1736, 16], [1564, 19, 1736, 17, "TapRecognizer"], [1564, 32, 1736, 30], [1564, 34, 1736, 32, "_Recognizer"], [1564, 45, 1736, 43], [1564, 46, 1736, 44], [1565, 4, 1738, 2], [1565, 13, 1738, 11, "TapRecognizer"], [1565, 26, 1738, 24, "TapRecognizer"], [1565, 27, 1738, 25, "options"], [1565, 34, 1738, 32], [1565, 36, 1738, 34], [1566, 6, 1739, 4], [1566, 10, 1739, 8, "_this"], [1566, 15, 1739, 13], [1567, 6, 1741, 4], [1567, 10, 1741, 8, "options"], [1567, 17, 1741, 15], [1567, 22, 1741, 20], [1567, 27, 1741, 25], [1567, 28, 1741, 26], [1567, 30, 1741, 28], [1568, 8, 1742, 6, "options"], [1568, 15, 1742, 13], [1568, 18, 1742, 16], [1568, 19, 1742, 17], [1568, 20, 1742, 18], [1569, 6, 1743, 4], [1570, 6, 1745, 4, "_this"], [1570, 11, 1745, 9], [1570, 14, 1745, 12, "_Recognizer"], [1570, 25, 1745, 23], [1570, 26, 1745, 24, "call"], [1570, 30, 1745, 28], [1570, 31, 1745, 29], [1570, 35, 1745, 33], [1570, 37, 1745, 35, "_extends"], [1570, 45, 1745, 43], [1570, 46, 1745, 44], [1571, 8, 1746, 6, "event"], [1571, 13, 1746, 11], [1571, 15, 1746, 13], [1571, 20, 1746, 18], [1572, 8, 1747, 6, "pointers"], [1572, 16, 1747, 14], [1572, 18, 1747, 16], [1572, 19, 1747, 17], [1573, 8, 1748, 6, "taps"], [1573, 12, 1748, 10], [1573, 14, 1748, 12], [1573, 15, 1748, 13], [1574, 8, 1749, 6, "interval"], [1574, 16, 1749, 14], [1574, 18, 1749, 16], [1574, 21, 1749, 19], [1575, 8, 1750, 6], [1576, 8, 1751, 6, "time"], [1576, 12, 1751, 10], [1576, 14, 1751, 12], [1576, 17, 1751, 15], [1577, 8, 1752, 6], [1578, 8, 1753, 6, "threshold"], [1578, 17, 1753, 15], [1578, 19, 1753, 17], [1578, 20, 1753, 18], [1579, 8, 1754, 6], [1580, 8, 1755, 6, "pos<PERSON><PERSON><PERSON><PERSON>"], [1580, 20, 1755, 18], [1580, 22, 1755, 20], [1581, 6, 1756, 4], [1581, 7, 1756, 5], [1581, 9, 1756, 7, "options"], [1581, 16, 1756, 14], [1581, 17, 1756, 15], [1581, 18, 1756, 16], [1581, 22, 1756, 20], [1581, 26, 1756, 24], [1581, 27, 1756, 25], [1581, 28, 1756, 26], [1582, 6, 1757, 4], [1584, 6, 1759, 4, "_this"], [1584, 11, 1759, 9], [1584, 12, 1759, 10, "pTime"], [1584, 17, 1759, 15], [1584, 20, 1759, 18], [1584, 25, 1759, 23], [1585, 6, 1760, 4, "_this"], [1585, 11, 1760, 9], [1585, 12, 1760, 10, "pCenter"], [1585, 19, 1760, 17], [1585, 22, 1760, 20], [1585, 27, 1760, 25], [1586, 6, 1761, 4, "_this"], [1586, 11, 1761, 9], [1586, 12, 1761, 10, "_timer"], [1586, 18, 1761, 16], [1586, 21, 1761, 19], [1586, 25, 1761, 23], [1587, 6, 1762, 4, "_this"], [1587, 11, 1762, 9], [1587, 12, 1762, 10, "_input"], [1587, 18, 1762, 16], [1587, 21, 1762, 19], [1587, 25, 1762, 23], [1588, 6, 1763, 4, "_this"], [1588, 11, 1763, 9], [1588, 12, 1763, 10, "count"], [1588, 17, 1763, 15], [1588, 20, 1763, 18], [1588, 21, 1763, 19], [1589, 6, 1764, 4], [1589, 13, 1764, 11, "_this"], [1589, 18, 1764, 16], [1590, 4, 1765, 2], [1591, 4, 1767, 2], [1591, 8, 1767, 6, "_proto"], [1591, 14, 1767, 12], [1591, 17, 1767, 15, "TapRecognizer"], [1591, 30, 1767, 28], [1591, 31, 1767, 29, "prototype"], [1591, 40, 1767, 38], [1592, 4, 1769, 2, "_proto"], [1592, 10, 1769, 8], [1592, 11, 1769, 9, "getTouchAction"], [1592, 25, 1769, 23], [1592, 28, 1769, 26], [1592, 37, 1769, 35, "getTouchAction"], [1592, 51, 1769, 49, "getTouchAction"], [1592, 52, 1769, 49], [1592, 54, 1769, 52], [1593, 6, 1770, 4], [1593, 13, 1770, 11], [1593, 14, 1770, 12, "TOUCH_ACTION_MANIPULATION"], [1593, 39, 1770, 37], [1593, 40, 1770, 38], [1594, 4, 1771, 2], [1594, 5, 1771, 3], [1595, 4, 1773, 2, "_proto"], [1595, 10, 1773, 8], [1595, 11, 1773, 9, "process"], [1595, 18, 1773, 16], [1595, 21, 1773, 19], [1595, 30, 1773, 28, "process"], [1595, 37, 1773, 35, "process"], [1595, 38, 1773, 36, "input"], [1595, 43, 1773, 41], [1595, 45, 1773, 43], [1596, 6, 1774, 4], [1596, 10, 1774, 8, "_this2"], [1596, 16, 1774, 14], [1596, 19, 1774, 17], [1596, 23, 1774, 21], [1597, 6, 1776, 4], [1597, 10, 1776, 8, "options"], [1597, 17, 1776, 15], [1597, 20, 1776, 18], [1597, 24, 1776, 22], [1597, 25, 1776, 23, "options"], [1597, 32, 1776, 30], [1598, 6, 1777, 4], [1598, 10, 1777, 8, "validPointers"], [1598, 23, 1777, 21], [1598, 26, 1777, 24, "input"], [1598, 31, 1777, 29], [1598, 32, 1777, 30, "pointers"], [1598, 40, 1777, 38], [1598, 41, 1777, 39, "length"], [1598, 47, 1777, 45], [1598, 52, 1777, 50, "options"], [1598, 59, 1777, 57], [1598, 60, 1777, 58, "pointers"], [1598, 68, 1777, 66], [1599, 6, 1778, 4], [1599, 10, 1778, 8, "validMovement"], [1599, 23, 1778, 21], [1599, 26, 1778, 24, "input"], [1599, 31, 1778, 29], [1599, 32, 1778, 30, "distance"], [1599, 40, 1778, 38], [1599, 43, 1778, 41, "options"], [1599, 50, 1778, 48], [1599, 51, 1778, 49, "threshold"], [1599, 60, 1778, 58], [1600, 6, 1779, 4], [1600, 10, 1779, 8, "validTouchTime"], [1600, 24, 1779, 22], [1600, 27, 1779, 25, "input"], [1600, 32, 1779, 30], [1600, 33, 1779, 31, "deltaTime"], [1600, 42, 1779, 40], [1600, 45, 1779, 43, "options"], [1600, 52, 1779, 50], [1600, 53, 1779, 51, "time"], [1600, 57, 1779, 55], [1601, 6, 1780, 4], [1601, 10, 1780, 8], [1601, 11, 1780, 9, "reset"], [1601, 16, 1780, 14], [1601, 17, 1780, 15], [1601, 18, 1780, 16], [1602, 6, 1782, 4], [1602, 10, 1782, 8, "input"], [1602, 15, 1782, 13], [1602, 16, 1782, 14, "eventType"], [1602, 25, 1782, 23], [1602, 28, 1782, 26, "INPUT_START"], [1602, 39, 1782, 37], [1602, 43, 1782, 41], [1602, 47, 1782, 45], [1602, 48, 1782, 46, "count"], [1602, 53, 1782, 51], [1602, 58, 1782, 56], [1602, 59, 1782, 57], [1602, 61, 1782, 59], [1603, 8, 1783, 6], [1603, 15, 1783, 13], [1603, 19, 1783, 17], [1603, 20, 1783, 18, "failTimeout"], [1603, 31, 1783, 29], [1603, 32, 1783, 30], [1603, 33, 1783, 31], [1604, 6, 1784, 4], [1604, 7, 1784, 5], [1604, 8, 1784, 6], [1605, 6, 1785, 4], [1607, 6, 1788, 4], [1607, 10, 1788, 8, "validMovement"], [1607, 23, 1788, 21], [1607, 27, 1788, 25, "validTouchTime"], [1607, 41, 1788, 39], [1607, 45, 1788, 43, "validPointers"], [1607, 58, 1788, 56], [1607, 60, 1788, 58], [1608, 8, 1789, 6], [1608, 12, 1789, 10, "input"], [1608, 17, 1789, 15], [1608, 18, 1789, 16, "eventType"], [1608, 27, 1789, 25], [1608, 32, 1789, 30, "INPUT_END"], [1608, 41, 1789, 39], [1608, 43, 1789, 41], [1609, 10, 1790, 8], [1609, 17, 1790, 15], [1609, 21, 1790, 19], [1609, 22, 1790, 20, "failTimeout"], [1609, 33, 1790, 31], [1609, 34, 1790, 32], [1609, 35, 1790, 33], [1610, 8, 1791, 6], [1611, 8, 1793, 6], [1611, 12, 1793, 10, "validInterval"], [1611, 25, 1793, 23], [1611, 28, 1793, 26], [1611, 32, 1793, 30], [1611, 33, 1793, 31, "pTime"], [1611, 38, 1793, 36], [1611, 41, 1793, 39, "input"], [1611, 46, 1793, 44], [1611, 47, 1793, 45, "timeStamp"], [1611, 56, 1793, 54], [1611, 59, 1793, 57], [1611, 63, 1793, 61], [1611, 64, 1793, 62, "pTime"], [1611, 69, 1793, 67], [1611, 72, 1793, 70, "options"], [1611, 79, 1793, 77], [1611, 80, 1793, 78, "interval"], [1611, 88, 1793, 86], [1611, 91, 1793, 89], [1611, 95, 1793, 93], [1612, 8, 1794, 6], [1612, 12, 1794, 10, "validMultiTap"], [1612, 25, 1794, 23], [1612, 28, 1794, 26], [1612, 29, 1794, 27], [1612, 33, 1794, 31], [1612, 34, 1794, 32, "pCenter"], [1612, 41, 1794, 39], [1612, 45, 1794, 43, "getDistance"], [1612, 56, 1794, 54], [1612, 57, 1794, 55], [1612, 61, 1794, 59], [1612, 62, 1794, 60, "pCenter"], [1612, 69, 1794, 67], [1612, 71, 1794, 69, "input"], [1612, 76, 1794, 74], [1612, 77, 1794, 75, "center"], [1612, 83, 1794, 81], [1612, 84, 1794, 82], [1612, 87, 1794, 85, "options"], [1612, 94, 1794, 92], [1612, 95, 1794, 93, "pos<PERSON><PERSON><PERSON><PERSON>"], [1612, 107, 1794, 105], [1613, 8, 1795, 6], [1613, 12, 1795, 10], [1613, 13, 1795, 11, "pTime"], [1613, 18, 1795, 16], [1613, 21, 1795, 19, "input"], [1613, 26, 1795, 24], [1613, 27, 1795, 25, "timeStamp"], [1613, 36, 1795, 34], [1614, 8, 1796, 6], [1614, 12, 1796, 10], [1614, 13, 1796, 11, "pCenter"], [1614, 20, 1796, 18], [1614, 23, 1796, 21, "input"], [1614, 28, 1796, 26], [1614, 29, 1796, 27, "center"], [1614, 35, 1796, 33], [1615, 8, 1798, 6], [1615, 12, 1798, 10], [1615, 13, 1798, 11, "validMultiTap"], [1615, 26, 1798, 24], [1615, 30, 1798, 28], [1615, 31, 1798, 29, "validInterval"], [1615, 44, 1798, 42], [1615, 46, 1798, 44], [1616, 10, 1799, 8], [1616, 14, 1799, 12], [1616, 15, 1799, 13, "count"], [1616, 20, 1799, 18], [1616, 23, 1799, 21], [1616, 24, 1799, 22], [1617, 8, 1800, 6], [1617, 9, 1800, 7], [1617, 15, 1800, 13], [1618, 10, 1801, 8], [1618, 14, 1801, 12], [1618, 15, 1801, 13, "count"], [1618, 20, 1801, 18], [1618, 24, 1801, 22], [1618, 25, 1801, 23], [1619, 8, 1802, 6], [1620, 8, 1804, 6], [1620, 12, 1804, 10], [1620, 13, 1804, 11, "_input"], [1620, 19, 1804, 17], [1620, 22, 1804, 20, "input"], [1620, 27, 1804, 25], [1620, 28, 1804, 26], [1620, 29, 1804, 27], [1621, 8, 1805, 6], [1623, 8, 1807, 6], [1623, 12, 1807, 10, "tapCount"], [1623, 20, 1807, 18], [1623, 23, 1807, 21], [1623, 27, 1807, 25], [1623, 28, 1807, 26, "count"], [1623, 33, 1807, 31], [1623, 36, 1807, 34, "options"], [1623, 43, 1807, 41], [1623, 44, 1807, 42, "taps"], [1623, 48, 1807, 46], [1624, 8, 1809, 6], [1624, 12, 1809, 10, "tapCount"], [1624, 20, 1809, 18], [1624, 25, 1809, 23], [1624, 26, 1809, 24], [1624, 28, 1809, 26], [1625, 10, 1810, 8], [1626, 10, 1811, 8], [1627, 10, 1812, 8], [1627, 14, 1812, 12], [1627, 15, 1812, 13], [1627, 19, 1812, 17], [1627, 20, 1812, 18, "hasRequireFailures"], [1627, 38, 1812, 36], [1627, 39, 1812, 37], [1627, 40, 1812, 38], [1627, 42, 1812, 40], [1628, 12, 1813, 10], [1628, 19, 1813, 17, "STATE_RECOGNIZED"], [1628, 35, 1813, 33], [1629, 10, 1814, 8], [1629, 11, 1814, 9], [1629, 17, 1814, 15], [1630, 12, 1815, 10], [1630, 16, 1815, 14], [1630, 17, 1815, 15, "_timer"], [1630, 23, 1815, 21], [1630, 26, 1815, 24, "setTimeout"], [1630, 36, 1815, 34], [1630, 37, 1815, 35], [1630, 49, 1815, 47], [1631, 14, 1816, 12, "_this2"], [1631, 20, 1816, 18], [1631, 21, 1816, 19, "state"], [1631, 26, 1816, 24], [1631, 29, 1816, 27, "STATE_RECOGNIZED"], [1631, 45, 1816, 43], [1632, 14, 1818, 12, "_this2"], [1632, 20, 1818, 18], [1632, 21, 1818, 19, "tryEmit"], [1632, 28, 1818, 26], [1632, 29, 1818, 27], [1632, 30, 1818, 28], [1633, 12, 1819, 10], [1633, 13, 1819, 11], [1633, 15, 1819, 13, "options"], [1633, 22, 1819, 20], [1633, 23, 1819, 21, "interval"], [1633, 31, 1819, 29], [1633, 32, 1819, 30], [1634, 12, 1820, 10], [1634, 19, 1820, 17, "STATE_BEGAN"], [1634, 30, 1820, 28], [1635, 10, 1821, 8], [1636, 8, 1822, 6], [1637, 6, 1823, 4], [1638, 6, 1825, 4], [1638, 13, 1825, 11, "STATE_FAILED"], [1638, 25, 1825, 23], [1639, 4, 1826, 2], [1639, 5, 1826, 3], [1640, 4, 1828, 2, "_proto"], [1640, 10, 1828, 8], [1640, 11, 1828, 9, "failTimeout"], [1640, 22, 1828, 20], [1640, 25, 1828, 23], [1640, 34, 1828, 32, "failTimeout"], [1640, 45, 1828, 43, "failTimeout"], [1640, 46, 1828, 43], [1640, 48, 1828, 46], [1641, 6, 1829, 4], [1641, 10, 1829, 8, "_this3"], [1641, 16, 1829, 14], [1641, 19, 1829, 17], [1641, 23, 1829, 21], [1642, 6, 1831, 4], [1642, 10, 1831, 8], [1642, 11, 1831, 9, "_timer"], [1642, 17, 1831, 15], [1642, 20, 1831, 18, "setTimeout"], [1642, 30, 1831, 28], [1642, 31, 1831, 29], [1642, 43, 1831, 41], [1643, 8, 1832, 6, "_this3"], [1643, 14, 1832, 12], [1643, 15, 1832, 13, "state"], [1643, 20, 1832, 18], [1643, 23, 1832, 21, "STATE_FAILED"], [1643, 35, 1832, 33], [1644, 6, 1833, 4], [1644, 7, 1833, 5], [1644, 9, 1833, 7], [1644, 13, 1833, 11], [1644, 14, 1833, 12, "options"], [1644, 21, 1833, 19], [1644, 22, 1833, 20, "interval"], [1644, 30, 1833, 28], [1644, 31, 1833, 29], [1645, 6, 1834, 4], [1645, 13, 1834, 11, "STATE_FAILED"], [1645, 25, 1834, 23], [1646, 4, 1835, 2], [1646, 5, 1835, 3], [1647, 4, 1837, 2, "_proto"], [1647, 10, 1837, 8], [1647, 11, 1837, 9, "reset"], [1647, 16, 1837, 14], [1647, 19, 1837, 17], [1647, 28, 1837, 26, "reset"], [1647, 33, 1837, 31, "reset"], [1647, 34, 1837, 31], [1647, 36, 1837, 34], [1648, 6, 1838, 4, "clearTimeout"], [1648, 18, 1838, 16], [1648, 19, 1838, 17], [1648, 23, 1838, 21], [1648, 24, 1838, 22, "_timer"], [1648, 30, 1838, 28], [1648, 31, 1838, 29], [1649, 4, 1839, 2], [1649, 5, 1839, 3], [1650, 4, 1841, 2, "_proto"], [1650, 10, 1841, 8], [1650, 11, 1841, 9, "emit"], [1650, 15, 1841, 13], [1650, 18, 1841, 16], [1650, 27, 1841, 25, "emit"], [1650, 31, 1841, 29, "emit"], [1650, 32, 1841, 29], [1650, 34, 1841, 32], [1651, 6, 1842, 4], [1651, 10, 1842, 8], [1651, 14, 1842, 12], [1651, 15, 1842, 13, "state"], [1651, 20, 1842, 18], [1651, 25, 1842, 23, "STATE_RECOGNIZED"], [1651, 41, 1842, 39], [1651, 43, 1842, 41], [1652, 8, 1843, 6], [1652, 12, 1843, 10], [1652, 13, 1843, 11, "_input"], [1652, 19, 1843, 17], [1652, 20, 1843, 18, "tapCount"], [1652, 28, 1843, 26], [1652, 31, 1843, 29], [1652, 35, 1843, 33], [1652, 36, 1843, 34, "count"], [1652, 41, 1843, 39], [1653, 8, 1844, 6], [1653, 12, 1844, 10], [1653, 13, 1844, 11, "manager"], [1653, 20, 1844, 18], [1653, 21, 1844, 19, "emit"], [1653, 25, 1844, 23], [1653, 26, 1844, 24], [1653, 30, 1844, 28], [1653, 31, 1844, 29, "options"], [1653, 38, 1844, 36], [1653, 39, 1844, 37, "event"], [1653, 44, 1844, 42], [1653, 46, 1844, 44], [1653, 50, 1844, 48], [1653, 51, 1844, 49, "_input"], [1653, 57, 1844, 55], [1653, 58, 1844, 56], [1654, 6, 1845, 4], [1655, 4, 1846, 2], [1655, 5, 1846, 3], [1656, 4, 1848, 2], [1656, 11, 1848, 9, "TapRecognizer"], [1656, 24, 1848, 22], [1657, 2, 1849, 0], [1657, 3, 1849, 1], [1657, 4, 1849, 2, "Recognizer"], [1657, 14, 1849, 12], [1657, 15, 1849, 13], [1659, 2, 1851, 0], [1660, 0, 1852, 0], [1661, 0, 1853, 0], [1662, 0, 1854, 0], [1663, 0, 1855, 0], [1664, 0, 1856, 0], [1666, 2, 1858, 0], [1666, 6, 1858, 4, "AttrRecognizer"], [1666, 20, 1858, 18], [1666, 23, 1858, 18, "exports"], [1666, 30, 1858, 18], [1666, 31, 1858, 18, "AttrRecognizer"], [1666, 45, 1858, 18], [1666, 48, 1859, 0], [1667, 2, 1860, 0], [1667, 12, 1860, 10, "_Recognizer"], [1667, 23, 1860, 21], [1667, 25, 1860, 23], [1668, 4, 1861, 2, "_inherits<PERSON><PERSON>e"], [1668, 18, 1861, 16], [1668, 19, 1861, 17, "AttrRecognizer"], [1668, 33, 1861, 31], [1668, 35, 1861, 33, "_Recognizer"], [1668, 46, 1861, 44], [1668, 47, 1861, 45], [1669, 4, 1863, 2], [1669, 13, 1863, 11, "AttrRecognizer"], [1669, 27, 1863, 25, "AttrRecognizer"], [1669, 28, 1863, 26, "options"], [1669, 35, 1863, 33], [1669, 37, 1863, 35], [1670, 6, 1864, 4], [1670, 10, 1864, 8, "options"], [1670, 17, 1864, 15], [1670, 22, 1864, 20], [1670, 27, 1864, 25], [1670, 28, 1864, 26], [1670, 30, 1864, 28], [1671, 8, 1865, 6, "options"], [1671, 15, 1865, 13], [1671, 18, 1865, 16], [1671, 19, 1865, 17], [1671, 20, 1865, 18], [1672, 6, 1866, 4], [1673, 6, 1868, 4], [1673, 13, 1868, 11, "_Recognizer"], [1673, 24, 1868, 22], [1673, 25, 1868, 23, "call"], [1673, 29, 1868, 27], [1673, 30, 1868, 28], [1673, 34, 1868, 32], [1673, 36, 1868, 34, "_extends"], [1673, 44, 1868, 42], [1673, 45, 1868, 43], [1674, 8, 1869, 6, "pointers"], [1674, 16, 1869, 14], [1674, 18, 1869, 16], [1675, 6, 1870, 4], [1675, 7, 1870, 5], [1675, 9, 1870, 7, "options"], [1675, 16, 1870, 14], [1675, 17, 1870, 15], [1675, 18, 1870, 16], [1675, 22, 1870, 20], [1675, 26, 1870, 24], [1676, 4, 1871, 2], [1677, 4, 1872, 2], [1678, 0, 1873, 0], [1679, 0, 1874, 0], [1680, 0, 1875, 0], [1681, 0, 1876, 0], [1682, 0, 1877, 0], [1683, 0, 1878, 0], [1685, 4, 1881, 2], [1685, 8, 1881, 6, "_proto"], [1685, 14, 1881, 12], [1685, 17, 1881, 15, "AttrRecognizer"], [1685, 31, 1881, 29], [1685, 32, 1881, 30, "prototype"], [1685, 41, 1881, 39], [1686, 4, 1883, 2, "_proto"], [1686, 10, 1883, 8], [1686, 11, 1883, 9, "attrTest"], [1686, 19, 1883, 17], [1686, 22, 1883, 20], [1686, 31, 1883, 29, "attrTest"], [1686, 39, 1883, 37, "attrTest"], [1686, 40, 1883, 38, "input"], [1686, 45, 1883, 43], [1686, 47, 1883, 45], [1687, 6, 1884, 4], [1687, 10, 1884, 8, "optionPointers"], [1687, 24, 1884, 22], [1687, 27, 1884, 25], [1687, 31, 1884, 29], [1687, 32, 1884, 30, "options"], [1687, 39, 1884, 37], [1687, 40, 1884, 38, "pointers"], [1687, 48, 1884, 46], [1688, 6, 1885, 4], [1688, 13, 1885, 11, "optionPointers"], [1688, 27, 1885, 25], [1688, 32, 1885, 30], [1688, 33, 1885, 31], [1688, 37, 1885, 35, "input"], [1688, 42, 1885, 40], [1688, 43, 1885, 41, "pointers"], [1688, 51, 1885, 49], [1688, 52, 1885, 50, "length"], [1688, 58, 1885, 56], [1688, 63, 1885, 61, "optionPointers"], [1688, 77, 1885, 75], [1689, 4, 1886, 2], [1689, 5, 1886, 3], [1690, 4, 1887, 2], [1691, 0, 1888, 0], [1692, 0, 1889, 0], [1693, 0, 1890, 0], [1694, 0, 1891, 0], [1695, 0, 1892, 0], [1696, 0, 1893, 0], [1698, 4, 1896, 2, "_proto"], [1698, 10, 1896, 8], [1698, 11, 1896, 9, "process"], [1698, 18, 1896, 16], [1698, 21, 1896, 19], [1698, 30, 1896, 28, "process"], [1698, 37, 1896, 35, "process"], [1698, 38, 1896, 36, "input"], [1698, 43, 1896, 41], [1698, 45, 1896, 43], [1699, 6, 1897, 4], [1699, 10, 1897, 8, "state"], [1699, 15, 1897, 13], [1699, 18, 1897, 16], [1699, 22, 1897, 20], [1699, 23, 1897, 21, "state"], [1699, 28, 1897, 26], [1700, 6, 1898, 4], [1700, 10, 1898, 8, "eventType"], [1700, 19, 1898, 17], [1700, 22, 1898, 20, "input"], [1700, 27, 1898, 25], [1700, 28, 1898, 26, "eventType"], [1700, 37, 1898, 35], [1701, 6, 1899, 4], [1701, 10, 1899, 8, "isRecognized"], [1701, 22, 1899, 20], [1701, 25, 1899, 23, "state"], [1701, 30, 1899, 28], [1701, 34, 1899, 32, "STATE_BEGAN"], [1701, 45, 1899, 43], [1701, 48, 1899, 46, "STATE_CHANGED"], [1701, 61, 1899, 59], [1701, 62, 1899, 60], [1702, 6, 1900, 4], [1702, 10, 1900, 8, "<PERSON><PERSON><PERSON><PERSON>"], [1702, 17, 1900, 15], [1702, 20, 1900, 18], [1702, 24, 1900, 22], [1702, 25, 1900, 23, "attrTest"], [1702, 33, 1900, 31], [1702, 34, 1900, 32, "input"], [1702, 39, 1900, 37], [1702, 40, 1900, 38], [1702, 41, 1900, 39], [1702, 42, 1900, 40], [1704, 6, 1902, 4], [1704, 10, 1902, 8, "isRecognized"], [1704, 22, 1902, 20], [1704, 27, 1902, 25, "eventType"], [1704, 36, 1902, 34], [1704, 39, 1902, 37, "INPUT_CANCEL"], [1704, 51, 1902, 49], [1704, 55, 1902, 53], [1704, 56, 1902, 54, "<PERSON><PERSON><PERSON><PERSON>"], [1704, 63, 1902, 61], [1704, 64, 1902, 62], [1704, 66, 1902, 64], [1705, 8, 1903, 6], [1705, 15, 1903, 13, "state"], [1705, 20, 1903, 18], [1705, 23, 1903, 21, "STATE_CANCELLED"], [1705, 38, 1903, 36], [1706, 6, 1904, 4], [1706, 7, 1904, 5], [1706, 13, 1904, 11], [1706, 17, 1904, 15, "isRecognized"], [1706, 29, 1904, 27], [1706, 33, 1904, 31, "<PERSON><PERSON><PERSON><PERSON>"], [1706, 40, 1904, 38], [1706, 42, 1904, 40], [1707, 8, 1905, 6], [1707, 12, 1905, 10, "eventType"], [1707, 21, 1905, 19], [1707, 24, 1905, 22, "INPUT_END"], [1707, 33, 1905, 31], [1707, 35, 1905, 33], [1708, 10, 1906, 8], [1708, 17, 1906, 15, "state"], [1708, 22, 1906, 20], [1708, 25, 1906, 23, "STATE_ENDED"], [1708, 36, 1906, 34], [1709, 8, 1907, 6], [1709, 9, 1907, 7], [1709, 15, 1907, 13], [1709, 19, 1907, 17], [1709, 21, 1907, 19, "state"], [1709, 26, 1907, 24], [1709, 29, 1907, 27, "STATE_BEGAN"], [1709, 40, 1907, 38], [1709, 41, 1907, 39], [1709, 43, 1907, 41], [1710, 10, 1908, 8], [1710, 17, 1908, 15, "STATE_BEGAN"], [1710, 28, 1908, 26], [1711, 8, 1909, 6], [1712, 8, 1911, 6], [1712, 15, 1911, 13, "state"], [1712, 20, 1911, 18], [1712, 23, 1911, 21, "STATE_CHANGED"], [1712, 36, 1911, 34], [1713, 6, 1912, 4], [1714, 6, 1914, 4], [1714, 13, 1914, 11, "STATE_FAILED"], [1714, 25, 1914, 23], [1715, 4, 1915, 2], [1715, 5, 1915, 3], [1716, 4, 1917, 2], [1716, 11, 1917, 9, "AttrRecognizer"], [1716, 25, 1917, 23], [1717, 2, 1918, 0], [1717, 3, 1918, 1], [1717, 4, 1918, 2, "Recognizer"], [1717, 14, 1918, 12], [1717, 15, 1918, 13], [1719, 2, 1920, 0], [1720, 0, 1921, 0], [1721, 0, 1922, 0], [1722, 0, 1923, 0], [1723, 0, 1924, 0], [1724, 0, 1925, 0], [1726, 2, 1927, 0], [1726, 11, 1927, 9, "directionStr"], [1726, 23, 1927, 21, "directionStr"], [1726, 24, 1927, 22, "direction"], [1726, 33, 1927, 31], [1726, 35, 1927, 33], [1727, 4, 1928, 2], [1727, 8, 1928, 6, "direction"], [1727, 17, 1928, 15], [1727, 22, 1928, 20, "DIRECTION_DOWN"], [1727, 36, 1928, 34], [1727, 38, 1928, 36], [1728, 6, 1929, 4], [1728, 13, 1929, 11], [1728, 19, 1929, 17], [1729, 4, 1930, 2], [1729, 5, 1930, 3], [1729, 11, 1930, 9], [1729, 15, 1930, 13, "direction"], [1729, 24, 1930, 22], [1729, 29, 1930, 27, "DIRECTION_UP"], [1729, 41, 1930, 39], [1729, 43, 1930, 41], [1730, 6, 1931, 4], [1730, 13, 1931, 11], [1730, 17, 1931, 15], [1731, 4, 1932, 2], [1731, 5, 1932, 3], [1731, 11, 1932, 9], [1731, 15, 1932, 13, "direction"], [1731, 24, 1932, 22], [1731, 29, 1932, 27, "DIRECTION_LEFT"], [1731, 43, 1932, 41], [1731, 45, 1932, 43], [1732, 6, 1933, 4], [1732, 13, 1933, 11], [1732, 19, 1933, 17], [1733, 4, 1934, 2], [1733, 5, 1934, 3], [1733, 11, 1934, 9], [1733, 15, 1934, 13, "direction"], [1733, 24, 1934, 22], [1733, 29, 1934, 27, "DIRECTION_RIGHT"], [1733, 44, 1934, 42], [1733, 46, 1934, 44], [1734, 6, 1935, 4], [1734, 13, 1935, 11], [1734, 20, 1935, 18], [1735, 4, 1936, 2], [1736, 4, 1938, 2], [1736, 11, 1938, 9], [1736, 13, 1938, 11], [1737, 2, 1939, 0], [1739, 2, 1941, 0], [1740, 0, 1942, 0], [1741, 0, 1943, 0], [1742, 0, 1944, 0], [1743, 0, 1945, 0], [1744, 0, 1946, 0], [1745, 0, 1947, 0], [1747, 2, 1949, 0], [1747, 6, 1949, 4, "PanRecognizer"], [1747, 19, 1949, 17], [1747, 22, 1949, 17, "exports"], [1747, 29, 1949, 17], [1747, 30, 1949, 17, "Pan"], [1747, 33, 1949, 17], [1747, 36, 1950, 0], [1748, 2, 1951, 0], [1748, 12, 1951, 10, "_AttrRecognizer"], [1748, 27, 1951, 25], [1748, 29, 1951, 27], [1749, 4, 1952, 2, "_inherits<PERSON><PERSON>e"], [1749, 18, 1952, 16], [1749, 19, 1952, 17, "PanRecognizer"], [1749, 32, 1952, 30], [1749, 34, 1952, 32, "_AttrRecognizer"], [1749, 49, 1952, 47], [1749, 50, 1952, 48], [1750, 4, 1954, 2], [1750, 13, 1954, 11, "PanRecognizer"], [1750, 26, 1954, 24, "PanRecognizer"], [1750, 27, 1954, 25, "options"], [1750, 34, 1954, 32], [1750, 36, 1954, 34], [1751, 6, 1955, 4], [1751, 10, 1955, 8, "_this"], [1751, 15, 1955, 13], [1752, 6, 1957, 4], [1752, 10, 1957, 8, "options"], [1752, 17, 1957, 15], [1752, 22, 1957, 20], [1752, 27, 1957, 25], [1752, 28, 1957, 26], [1752, 30, 1957, 28], [1753, 8, 1958, 6, "options"], [1753, 15, 1958, 13], [1753, 18, 1958, 16], [1753, 19, 1958, 17], [1753, 20, 1958, 18], [1754, 6, 1959, 4], [1755, 6, 1961, 4, "_this"], [1755, 11, 1961, 9], [1755, 14, 1961, 12, "_AttrRecognizer"], [1755, 29, 1961, 27], [1755, 30, 1961, 28, "call"], [1755, 34, 1961, 32], [1755, 35, 1961, 33], [1755, 39, 1961, 37], [1755, 41, 1961, 39, "_extends"], [1755, 49, 1961, 47], [1755, 50, 1961, 48], [1756, 8, 1962, 6, "event"], [1756, 13, 1962, 11], [1756, 15, 1962, 13], [1756, 20, 1962, 18], [1757, 8, 1963, 6, "threshold"], [1757, 17, 1963, 15], [1757, 19, 1963, 17], [1757, 21, 1963, 19], [1758, 8, 1964, 6, "pointers"], [1758, 16, 1964, 14], [1758, 18, 1964, 16], [1758, 19, 1964, 17], [1759, 8, 1965, 6, "direction"], [1759, 17, 1965, 15], [1759, 19, 1965, 17, "DIRECTION_ALL"], [1760, 6, 1966, 4], [1760, 7, 1966, 5], [1760, 9, 1966, 7, "options"], [1760, 16, 1966, 14], [1760, 17, 1966, 15], [1760, 18, 1966, 16], [1760, 22, 1966, 20], [1760, 26, 1966, 24], [1761, 6, 1967, 4, "_this"], [1761, 11, 1967, 9], [1761, 12, 1967, 10, "pX"], [1761, 14, 1967, 12], [1761, 17, 1967, 15], [1761, 21, 1967, 19], [1762, 6, 1968, 4, "_this"], [1762, 11, 1968, 9], [1762, 12, 1968, 10, "pY"], [1762, 14, 1968, 12], [1762, 17, 1968, 15], [1762, 21, 1968, 19], [1763, 6, 1969, 4], [1763, 13, 1969, 11, "_this"], [1763, 18, 1969, 16], [1764, 4, 1970, 2], [1765, 4, 1972, 2], [1765, 8, 1972, 6, "_proto"], [1765, 14, 1972, 12], [1765, 17, 1972, 15, "PanRecognizer"], [1765, 30, 1972, 28], [1765, 31, 1972, 29, "prototype"], [1765, 40, 1972, 38], [1766, 4, 1974, 2, "_proto"], [1766, 10, 1974, 8], [1766, 11, 1974, 9, "getTouchAction"], [1766, 25, 1974, 23], [1766, 28, 1974, 26], [1766, 37, 1974, 35, "getTouchAction"], [1766, 51, 1974, 49, "getTouchAction"], [1766, 52, 1974, 49], [1766, 54, 1974, 52], [1767, 6, 1975, 4], [1767, 10, 1975, 8, "direction"], [1767, 19, 1975, 17], [1767, 22, 1975, 20], [1767, 26, 1975, 24], [1767, 27, 1975, 25, "options"], [1767, 34, 1975, 32], [1767, 35, 1975, 33, "direction"], [1767, 44, 1975, 42], [1768, 6, 1976, 4], [1768, 10, 1976, 8, "actions"], [1768, 17, 1976, 15], [1768, 20, 1976, 18], [1768, 22, 1976, 20], [1769, 6, 1978, 4], [1769, 10, 1978, 8, "direction"], [1769, 19, 1978, 17], [1769, 22, 1978, 20, "DIRECTION_HORIZONTAL"], [1769, 42, 1978, 40], [1769, 44, 1978, 42], [1770, 8, 1979, 6, "actions"], [1770, 15, 1979, 13], [1770, 16, 1979, 14, "push"], [1770, 20, 1979, 18], [1770, 21, 1979, 19, "TOUCH_ACTION_PAN_Y"], [1770, 39, 1979, 37], [1770, 40, 1979, 38], [1771, 6, 1980, 4], [1772, 6, 1982, 4], [1772, 10, 1982, 8, "direction"], [1772, 19, 1982, 17], [1772, 22, 1982, 20, "DIRECTION_VERTICAL"], [1772, 40, 1982, 38], [1772, 42, 1982, 40], [1773, 8, 1983, 6, "actions"], [1773, 15, 1983, 13], [1773, 16, 1983, 14, "push"], [1773, 20, 1983, 18], [1773, 21, 1983, 19, "TOUCH_ACTION_PAN_X"], [1773, 39, 1983, 37], [1773, 40, 1983, 38], [1774, 6, 1984, 4], [1775, 6, 1986, 4], [1775, 13, 1986, 11, "actions"], [1775, 20, 1986, 18], [1776, 4, 1987, 2], [1776, 5, 1987, 3], [1777, 4, 1989, 2, "_proto"], [1777, 10, 1989, 8], [1777, 11, 1989, 9, "directionTest"], [1777, 24, 1989, 22], [1777, 27, 1989, 25], [1777, 36, 1989, 34, "directionTest"], [1777, 49, 1989, 47, "directionTest"], [1777, 50, 1989, 48, "input"], [1777, 55, 1989, 53], [1777, 57, 1989, 55], [1778, 6, 1990, 4], [1778, 10, 1990, 8, "options"], [1778, 17, 1990, 15], [1778, 20, 1990, 18], [1778, 24, 1990, 22], [1778, 25, 1990, 23, "options"], [1778, 32, 1990, 30], [1779, 6, 1991, 4], [1779, 10, 1991, 8, "hasMoved"], [1779, 18, 1991, 16], [1779, 21, 1991, 19], [1779, 25, 1991, 23], [1780, 6, 1992, 4], [1780, 10, 1992, 8, "distance"], [1780, 18, 1992, 16], [1780, 21, 1992, 19, "input"], [1780, 26, 1992, 24], [1780, 27, 1992, 25, "distance"], [1780, 35, 1992, 33], [1781, 6, 1993, 4], [1781, 10, 1993, 8, "direction"], [1781, 19, 1993, 17], [1781, 22, 1993, 20, "input"], [1781, 27, 1993, 25], [1781, 28, 1993, 26, "direction"], [1781, 37, 1993, 35], [1782, 6, 1994, 4], [1782, 10, 1994, 8, "x"], [1782, 11, 1994, 9], [1782, 14, 1994, 12, "input"], [1782, 19, 1994, 17], [1782, 20, 1994, 18, "deltaX"], [1782, 26, 1994, 24], [1783, 6, 1995, 4], [1783, 10, 1995, 8, "y"], [1783, 11, 1995, 9], [1783, 14, 1995, 12, "input"], [1783, 19, 1995, 17], [1783, 20, 1995, 18, "deltaY"], [1783, 26, 1995, 24], [1783, 27, 1995, 25], [1783, 28, 1995, 26], [1785, 6, 1997, 4], [1785, 10, 1997, 8], [1785, 12, 1997, 10, "direction"], [1785, 21, 1997, 19], [1785, 24, 1997, 22, "options"], [1785, 31, 1997, 29], [1785, 32, 1997, 30, "direction"], [1785, 41, 1997, 39], [1785, 42, 1997, 40], [1785, 44, 1997, 42], [1786, 8, 1998, 6], [1786, 12, 1998, 10, "options"], [1786, 19, 1998, 17], [1786, 20, 1998, 18, "direction"], [1786, 29, 1998, 27], [1786, 32, 1998, 30, "DIRECTION_HORIZONTAL"], [1786, 52, 1998, 50], [1786, 54, 1998, 52], [1787, 10, 1999, 8, "direction"], [1787, 19, 1999, 17], [1787, 22, 1999, 20, "x"], [1787, 23, 1999, 21], [1787, 28, 1999, 26], [1787, 29, 1999, 27], [1787, 32, 1999, 30, "DIRECTION_NONE"], [1787, 46, 1999, 44], [1787, 49, 1999, 47, "x"], [1787, 50, 1999, 48], [1787, 53, 1999, 51], [1787, 54, 1999, 52], [1787, 57, 1999, 55, "DIRECTION_LEFT"], [1787, 71, 1999, 69], [1787, 74, 1999, 72, "DIRECTION_RIGHT"], [1787, 89, 1999, 87], [1788, 10, 2000, 8, "hasMoved"], [1788, 18, 2000, 16], [1788, 21, 2000, 19, "x"], [1788, 22, 2000, 20], [1788, 27, 2000, 25], [1788, 31, 2000, 29], [1788, 32, 2000, 30, "pX"], [1788, 34, 2000, 32], [1789, 10, 2001, 8, "distance"], [1789, 18, 2001, 16], [1789, 21, 2001, 19, "Math"], [1789, 25, 2001, 23], [1789, 26, 2001, 24, "abs"], [1789, 29, 2001, 27], [1789, 30, 2001, 28, "input"], [1789, 35, 2001, 33], [1789, 36, 2001, 34, "deltaX"], [1789, 42, 2001, 40], [1789, 43, 2001, 41], [1790, 8, 2002, 6], [1790, 9, 2002, 7], [1790, 15, 2002, 13], [1791, 10, 2003, 8, "direction"], [1791, 19, 2003, 17], [1791, 22, 2003, 20, "y"], [1791, 23, 2003, 21], [1791, 28, 2003, 26], [1791, 29, 2003, 27], [1791, 32, 2003, 30, "DIRECTION_NONE"], [1791, 46, 2003, 44], [1791, 49, 2003, 47, "y"], [1791, 50, 2003, 48], [1791, 53, 2003, 51], [1791, 54, 2003, 52], [1791, 57, 2003, 55, "DIRECTION_UP"], [1791, 69, 2003, 67], [1791, 72, 2003, 70, "DIRECTION_DOWN"], [1791, 86, 2003, 84], [1792, 10, 2004, 8, "hasMoved"], [1792, 18, 2004, 16], [1792, 21, 2004, 19, "y"], [1792, 22, 2004, 20], [1792, 27, 2004, 25], [1792, 31, 2004, 29], [1792, 32, 2004, 30, "pY"], [1792, 34, 2004, 32], [1793, 10, 2005, 8, "distance"], [1793, 18, 2005, 16], [1793, 21, 2005, 19, "Math"], [1793, 25, 2005, 23], [1793, 26, 2005, 24, "abs"], [1793, 29, 2005, 27], [1793, 30, 2005, 28, "input"], [1793, 35, 2005, 33], [1793, 36, 2005, 34, "deltaY"], [1793, 42, 2005, 40], [1793, 43, 2005, 41], [1794, 8, 2006, 6], [1795, 6, 2007, 4], [1796, 6, 2009, 4, "input"], [1796, 11, 2009, 9], [1796, 12, 2009, 10, "direction"], [1796, 21, 2009, 19], [1796, 24, 2009, 22, "direction"], [1796, 33, 2009, 31], [1797, 6, 2010, 4], [1797, 13, 2010, 11, "hasMoved"], [1797, 21, 2010, 19], [1797, 25, 2010, 23, "distance"], [1797, 33, 2010, 31], [1797, 36, 2010, 34, "options"], [1797, 43, 2010, 41], [1797, 44, 2010, 42, "threshold"], [1797, 53, 2010, 51], [1797, 57, 2010, 55, "direction"], [1797, 66, 2010, 64], [1797, 69, 2010, 67, "options"], [1797, 76, 2010, 74], [1797, 77, 2010, 75, "direction"], [1797, 86, 2010, 84], [1798, 4, 2011, 2], [1798, 5, 2011, 3], [1799, 4, 2013, 2, "_proto"], [1799, 10, 2013, 8], [1799, 11, 2013, 9, "attrTest"], [1799, 19, 2013, 17], [1799, 22, 2013, 20], [1799, 31, 2013, 29, "attrTest"], [1799, 39, 2013, 37, "attrTest"], [1799, 40, 2013, 38, "input"], [1799, 45, 2013, 43], [1799, 47, 2013, 45], [1800, 6, 2014, 4], [1800, 13, 2014, 11, "AttrRecognizer"], [1800, 27, 2014, 25], [1800, 28, 2014, 26, "prototype"], [1800, 37, 2014, 35], [1800, 38, 2014, 36, "attrTest"], [1800, 46, 2014, 44], [1800, 47, 2014, 45, "call"], [1800, 51, 2014, 49], [1800, 52, 2014, 50], [1800, 56, 2014, 54], [1800, 58, 2014, 56, "input"], [1800, 63, 2014, 61], [1800, 64, 2014, 62], [1801, 6, 2014, 68], [1802, 6, 2015, 4], [1802, 10, 2015, 8], [1802, 11, 2015, 9, "state"], [1802, 16, 2015, 14], [1802, 19, 2015, 17, "STATE_BEGAN"], [1802, 30, 2015, 28], [1802, 34, 2015, 32], [1802, 36, 2015, 34], [1802, 40, 2015, 38], [1802, 41, 2015, 39, "state"], [1802, 46, 2015, 44], [1802, 49, 2015, 47, "STATE_BEGAN"], [1802, 60, 2015, 58], [1802, 61, 2015, 59], [1802, 65, 2015, 63], [1802, 69, 2015, 67], [1802, 70, 2015, 68, "directionTest"], [1802, 83, 2015, 81], [1802, 84, 2015, 82, "input"], [1802, 89, 2015, 87], [1802, 90, 2015, 88], [1802, 91, 2015, 89], [1803, 4, 2016, 2], [1803, 5, 2016, 3], [1804, 4, 2018, 2, "_proto"], [1804, 10, 2018, 8], [1804, 11, 2018, 9, "emit"], [1804, 15, 2018, 13], [1804, 18, 2018, 16], [1804, 27, 2018, 25, "emit"], [1804, 31, 2018, 29, "emit"], [1804, 32, 2018, 30, "input"], [1804, 37, 2018, 35], [1804, 39, 2018, 37], [1805, 6, 2019, 4], [1805, 10, 2019, 8], [1805, 11, 2019, 9, "pX"], [1805, 13, 2019, 11], [1805, 16, 2019, 14, "input"], [1805, 21, 2019, 19], [1805, 22, 2019, 20, "deltaX"], [1805, 28, 2019, 26], [1806, 6, 2020, 4], [1806, 10, 2020, 8], [1806, 11, 2020, 9, "pY"], [1806, 13, 2020, 11], [1806, 16, 2020, 14, "input"], [1806, 21, 2020, 19], [1806, 22, 2020, 20, "deltaY"], [1806, 28, 2020, 26], [1807, 6, 2021, 4], [1807, 10, 2021, 8, "direction"], [1807, 19, 2021, 17], [1807, 22, 2021, 20, "directionStr"], [1807, 34, 2021, 32], [1807, 35, 2021, 33, "input"], [1807, 40, 2021, 38], [1807, 41, 2021, 39, "direction"], [1807, 50, 2021, 48], [1807, 51, 2021, 49], [1808, 6, 2023, 4], [1808, 10, 2023, 8, "direction"], [1808, 19, 2023, 17], [1808, 21, 2023, 19], [1809, 8, 2024, 6, "input"], [1809, 13, 2024, 11], [1809, 14, 2024, 12, "additionalEvent"], [1809, 29, 2024, 27], [1809, 32, 2024, 30], [1809, 36, 2024, 34], [1809, 37, 2024, 35, "options"], [1809, 44, 2024, 42], [1809, 45, 2024, 43, "event"], [1809, 50, 2024, 48], [1809, 53, 2024, 51, "direction"], [1809, 62, 2024, 60], [1810, 6, 2025, 4], [1811, 6, 2027, 4, "_AttrRecognizer"], [1811, 21, 2027, 19], [1811, 22, 2027, 20, "prototype"], [1811, 31, 2027, 29], [1811, 32, 2027, 30, "emit"], [1811, 36, 2027, 34], [1811, 37, 2027, 35, "call"], [1811, 41, 2027, 39], [1811, 42, 2027, 40], [1811, 46, 2027, 44], [1811, 48, 2027, 46, "input"], [1811, 53, 2027, 51], [1811, 54, 2027, 52], [1812, 4, 2028, 2], [1812, 5, 2028, 3], [1813, 4, 2030, 2], [1813, 11, 2030, 9, "PanRecognizer"], [1813, 24, 2030, 22], [1814, 2, 2031, 0], [1814, 3, 2031, 1], [1814, 4, 2031, 2, "AttrRecognizer"], [1814, 18, 2031, 16], [1814, 19, 2031, 17], [1816, 2, 2033, 0], [1817, 0, 2034, 0], [1818, 0, 2035, 0], [1819, 0, 2036, 0], [1820, 0, 2037, 0], [1821, 0, 2038, 0], [1822, 0, 2039, 0], [1824, 2, 2041, 0], [1824, 6, 2041, 4, "SwipeRecognizer"], [1824, 21, 2041, 19], [1824, 24, 2041, 19, "exports"], [1824, 31, 2041, 19], [1824, 32, 2041, 19, "Swipe"], [1824, 37, 2041, 19], [1824, 40, 2042, 0], [1825, 2, 2043, 0], [1825, 12, 2043, 10, "_AttrRecognizer"], [1825, 27, 2043, 25], [1825, 29, 2043, 27], [1826, 4, 2044, 2, "_inherits<PERSON><PERSON>e"], [1826, 18, 2044, 16], [1826, 19, 2044, 17, "SwipeRecognizer"], [1826, 34, 2044, 32], [1826, 36, 2044, 34, "_AttrRecognizer"], [1826, 51, 2044, 49], [1826, 52, 2044, 50], [1827, 4, 2046, 2], [1827, 13, 2046, 11, "SwipeRecognizer"], [1827, 28, 2046, 26, "SwipeRecognizer"], [1827, 29, 2046, 27, "options"], [1827, 36, 2046, 34], [1827, 38, 2046, 36], [1828, 6, 2047, 4], [1828, 10, 2047, 8, "options"], [1828, 17, 2047, 15], [1828, 22, 2047, 20], [1828, 27, 2047, 25], [1828, 28, 2047, 26], [1828, 30, 2047, 28], [1829, 8, 2048, 6, "options"], [1829, 15, 2048, 13], [1829, 18, 2048, 16], [1829, 19, 2048, 17], [1829, 20, 2048, 18], [1830, 6, 2049, 4], [1831, 6, 2051, 4], [1831, 13, 2051, 11, "_AttrRecognizer"], [1831, 28, 2051, 26], [1831, 29, 2051, 27, "call"], [1831, 33, 2051, 31], [1831, 34, 2051, 32], [1831, 38, 2051, 36], [1831, 40, 2051, 38, "_extends"], [1831, 48, 2051, 46], [1831, 49, 2051, 47], [1832, 8, 2052, 6, "event"], [1832, 13, 2052, 11], [1832, 15, 2052, 13], [1832, 22, 2052, 20], [1833, 8, 2053, 6, "threshold"], [1833, 17, 2053, 15], [1833, 19, 2053, 17], [1833, 21, 2053, 19], [1834, 8, 2054, 6, "velocity"], [1834, 16, 2054, 14], [1834, 18, 2054, 16], [1834, 21, 2054, 19], [1835, 8, 2055, 6, "direction"], [1835, 17, 2055, 15], [1835, 19, 2055, 17, "DIRECTION_HORIZONTAL"], [1835, 39, 2055, 37], [1835, 42, 2055, 40, "DIRECTION_VERTICAL"], [1835, 60, 2055, 58], [1836, 8, 2056, 6, "pointers"], [1836, 16, 2056, 14], [1836, 18, 2056, 16], [1837, 6, 2057, 4], [1837, 7, 2057, 5], [1837, 9, 2057, 7, "options"], [1837, 16, 2057, 14], [1837, 17, 2057, 15], [1837, 18, 2057, 16], [1837, 22, 2057, 20], [1837, 26, 2057, 24], [1838, 4, 2058, 2], [1839, 4, 2060, 2], [1839, 8, 2060, 6, "_proto"], [1839, 14, 2060, 12], [1839, 17, 2060, 15, "SwipeRecognizer"], [1839, 32, 2060, 30], [1839, 33, 2060, 31, "prototype"], [1839, 42, 2060, 40], [1840, 4, 2062, 2, "_proto"], [1840, 10, 2062, 8], [1840, 11, 2062, 9, "getTouchAction"], [1840, 25, 2062, 23], [1840, 28, 2062, 26], [1840, 37, 2062, 35, "getTouchAction"], [1840, 51, 2062, 49, "getTouchAction"], [1840, 52, 2062, 49], [1840, 54, 2062, 52], [1841, 6, 2063, 4], [1841, 13, 2063, 11, "PanRecognizer"], [1841, 26, 2063, 24], [1841, 27, 2063, 25, "prototype"], [1841, 36, 2063, 34], [1841, 37, 2063, 35, "getTouchAction"], [1841, 51, 2063, 49], [1841, 52, 2063, 50, "call"], [1841, 56, 2063, 54], [1841, 57, 2063, 55], [1841, 61, 2063, 59], [1841, 62, 2063, 60], [1842, 4, 2064, 2], [1842, 5, 2064, 3], [1843, 4, 2066, 2, "_proto"], [1843, 10, 2066, 8], [1843, 11, 2066, 9, "attrTest"], [1843, 19, 2066, 17], [1843, 22, 2066, 20], [1843, 31, 2066, 29, "attrTest"], [1843, 39, 2066, 37, "attrTest"], [1843, 40, 2066, 38, "input"], [1843, 45, 2066, 43], [1843, 47, 2066, 45], [1844, 6, 2067, 4], [1844, 10, 2067, 8, "direction"], [1844, 19, 2067, 17], [1844, 22, 2067, 20], [1844, 26, 2067, 24], [1844, 27, 2067, 25, "options"], [1844, 34, 2067, 32], [1844, 35, 2067, 33, "direction"], [1844, 44, 2067, 42], [1845, 6, 2068, 4], [1845, 10, 2068, 8, "velocity"], [1845, 18, 2068, 16], [1846, 6, 2070, 4], [1846, 10, 2070, 8, "direction"], [1846, 19, 2070, 17], [1846, 23, 2070, 21, "DIRECTION_HORIZONTAL"], [1846, 43, 2070, 41], [1846, 46, 2070, 44, "DIRECTION_VERTICAL"], [1846, 64, 2070, 62], [1846, 65, 2070, 63], [1846, 67, 2070, 65], [1847, 8, 2071, 6, "velocity"], [1847, 16, 2071, 14], [1847, 19, 2071, 17, "input"], [1847, 24, 2071, 22], [1847, 25, 2071, 23, "overallVelocity"], [1847, 40, 2071, 38], [1848, 6, 2072, 4], [1848, 7, 2072, 5], [1848, 13, 2072, 11], [1848, 17, 2072, 15, "direction"], [1848, 26, 2072, 24], [1848, 29, 2072, 27, "DIRECTION_HORIZONTAL"], [1848, 49, 2072, 47], [1848, 51, 2072, 49], [1849, 8, 2073, 6, "velocity"], [1849, 16, 2073, 14], [1849, 19, 2073, 17, "input"], [1849, 24, 2073, 22], [1849, 25, 2073, 23, "overallVelocityX"], [1849, 41, 2073, 39], [1850, 6, 2074, 4], [1850, 7, 2074, 5], [1850, 13, 2074, 11], [1850, 17, 2074, 15, "direction"], [1850, 26, 2074, 24], [1850, 29, 2074, 27, "DIRECTION_VERTICAL"], [1850, 47, 2074, 45], [1850, 49, 2074, 47], [1851, 8, 2075, 6, "velocity"], [1851, 16, 2075, 14], [1851, 19, 2075, 17, "input"], [1851, 24, 2075, 22], [1851, 25, 2075, 23, "overallVelocityY"], [1851, 41, 2075, 39], [1852, 6, 2076, 4], [1853, 6, 2078, 4], [1853, 13, 2078, 11, "_AttrRecognizer"], [1853, 28, 2078, 26], [1853, 29, 2078, 27, "prototype"], [1853, 38, 2078, 36], [1853, 39, 2078, 37, "attrTest"], [1853, 47, 2078, 45], [1853, 48, 2078, 46, "call"], [1853, 52, 2078, 50], [1853, 53, 2078, 51], [1853, 57, 2078, 55], [1853, 59, 2078, 57, "input"], [1853, 64, 2078, 62], [1853, 65, 2078, 63], [1853, 69, 2078, 67, "direction"], [1853, 78, 2078, 76], [1853, 81, 2078, 79, "input"], [1853, 86, 2078, 84], [1853, 87, 2078, 85, "offsetDirection"], [1853, 102, 2078, 100], [1853, 106, 2078, 104, "input"], [1853, 111, 2078, 109], [1853, 112, 2078, 110, "distance"], [1853, 120, 2078, 118], [1853, 123, 2078, 121], [1853, 127, 2078, 125], [1853, 128, 2078, 126, "options"], [1853, 135, 2078, 133], [1853, 136, 2078, 134, "threshold"], [1853, 145, 2078, 143], [1853, 149, 2078, 147, "input"], [1853, 154, 2078, 152], [1853, 155, 2078, 153, "maxPointers"], [1853, 166, 2078, 164], [1853, 171, 2078, 169], [1853, 175, 2078, 173], [1853, 176, 2078, 174, "options"], [1853, 183, 2078, 181], [1853, 184, 2078, 182, "pointers"], [1853, 192, 2078, 190], [1853, 196, 2078, 194, "abs"], [1853, 199, 2078, 197], [1853, 200, 2078, 198, "velocity"], [1853, 208, 2078, 206], [1853, 209, 2078, 207], [1853, 212, 2078, 210], [1853, 216, 2078, 214], [1853, 217, 2078, 215, "options"], [1853, 224, 2078, 222], [1853, 225, 2078, 223, "velocity"], [1853, 233, 2078, 231], [1853, 237, 2078, 235, "input"], [1853, 242, 2078, 240], [1853, 243, 2078, 241, "eventType"], [1853, 252, 2078, 250], [1853, 255, 2078, 253, "INPUT_END"], [1853, 264, 2078, 262], [1854, 4, 2079, 2], [1854, 5, 2079, 3], [1855, 4, 2081, 2, "_proto"], [1855, 10, 2081, 8], [1855, 11, 2081, 9, "emit"], [1855, 15, 2081, 13], [1855, 18, 2081, 16], [1855, 27, 2081, 25, "emit"], [1855, 31, 2081, 29, "emit"], [1855, 32, 2081, 30, "input"], [1855, 37, 2081, 35], [1855, 39, 2081, 37], [1856, 6, 2082, 4], [1856, 10, 2082, 8, "direction"], [1856, 19, 2082, 17], [1856, 22, 2082, 20, "directionStr"], [1856, 34, 2082, 32], [1856, 35, 2082, 33, "input"], [1856, 40, 2082, 38], [1856, 41, 2082, 39, "offsetDirection"], [1856, 56, 2082, 54], [1856, 57, 2082, 55], [1857, 6, 2084, 4], [1857, 10, 2084, 8, "direction"], [1857, 19, 2084, 17], [1857, 21, 2084, 19], [1858, 8, 2085, 6], [1858, 12, 2085, 10], [1858, 13, 2085, 11, "manager"], [1858, 20, 2085, 18], [1858, 21, 2085, 19, "emit"], [1858, 25, 2085, 23], [1858, 26, 2085, 24], [1858, 30, 2085, 28], [1858, 31, 2085, 29, "options"], [1858, 38, 2085, 36], [1858, 39, 2085, 37, "event"], [1858, 44, 2085, 42], [1858, 47, 2085, 45, "direction"], [1858, 56, 2085, 54], [1858, 58, 2085, 56, "input"], [1858, 63, 2085, 61], [1858, 64, 2085, 62], [1859, 6, 2086, 4], [1860, 6, 2088, 4], [1860, 10, 2088, 8], [1860, 11, 2088, 9, "manager"], [1860, 18, 2088, 16], [1860, 19, 2088, 17, "emit"], [1860, 23, 2088, 21], [1860, 24, 2088, 22], [1860, 28, 2088, 26], [1860, 29, 2088, 27, "options"], [1860, 36, 2088, 34], [1860, 37, 2088, 35, "event"], [1860, 42, 2088, 40], [1860, 44, 2088, 42, "input"], [1860, 49, 2088, 47], [1860, 50, 2088, 48], [1861, 4, 2089, 2], [1861, 5, 2089, 3], [1862, 4, 2091, 2], [1862, 11, 2091, 9, "SwipeRecognizer"], [1862, 26, 2091, 24], [1863, 2, 2092, 0], [1863, 3, 2092, 1], [1863, 4, 2092, 2, "AttrRecognizer"], [1863, 18, 2092, 16], [1863, 19, 2092, 17], [1865, 2, 2094, 0], [1866, 0, 2095, 0], [1867, 0, 2096, 0], [1868, 0, 2097, 0], [1869, 0, 2098, 0], [1870, 0, 2099, 0], [1871, 0, 2100, 0], [1873, 2, 2102, 0], [1873, 6, 2102, 4, "PinchRecognizer"], [1873, 21, 2102, 19], [1873, 24, 2102, 19, "exports"], [1873, 31, 2102, 19], [1873, 32, 2102, 19, "Pinch"], [1873, 37, 2102, 19], [1873, 40, 2103, 0], [1874, 2, 2104, 0], [1874, 12, 2104, 10, "_AttrRecognizer"], [1874, 27, 2104, 25], [1874, 29, 2104, 27], [1875, 4, 2105, 2, "_inherits<PERSON><PERSON>e"], [1875, 18, 2105, 16], [1875, 19, 2105, 17, "PinchRecognizer"], [1875, 34, 2105, 32], [1875, 36, 2105, 34, "_AttrRecognizer"], [1875, 51, 2105, 49], [1875, 52, 2105, 50], [1876, 4, 2107, 2], [1876, 13, 2107, 11, "PinchRecognizer"], [1876, 28, 2107, 26, "PinchRecognizer"], [1876, 29, 2107, 27, "options"], [1876, 36, 2107, 34], [1876, 38, 2107, 36], [1877, 6, 2108, 4], [1877, 10, 2108, 8, "options"], [1877, 17, 2108, 15], [1877, 22, 2108, 20], [1877, 27, 2108, 25], [1877, 28, 2108, 26], [1877, 30, 2108, 28], [1878, 8, 2109, 6, "options"], [1878, 15, 2109, 13], [1878, 18, 2109, 16], [1878, 19, 2109, 17], [1878, 20, 2109, 18], [1879, 6, 2110, 4], [1880, 6, 2112, 4], [1880, 13, 2112, 11, "_AttrRecognizer"], [1880, 28, 2112, 26], [1880, 29, 2112, 27, "call"], [1880, 33, 2112, 31], [1880, 34, 2112, 32], [1880, 38, 2112, 36], [1880, 40, 2112, 38, "_extends"], [1880, 48, 2112, 46], [1880, 49, 2112, 47], [1881, 8, 2113, 6, "event"], [1881, 13, 2113, 11], [1881, 15, 2113, 13], [1881, 22, 2113, 20], [1882, 8, 2114, 6, "threshold"], [1882, 17, 2114, 15], [1882, 19, 2114, 17], [1882, 20, 2114, 18], [1883, 8, 2115, 6, "pointers"], [1883, 16, 2115, 14], [1883, 18, 2115, 16], [1884, 6, 2116, 4], [1884, 7, 2116, 5], [1884, 9, 2116, 7, "options"], [1884, 16, 2116, 14], [1884, 17, 2116, 15], [1884, 18, 2116, 16], [1884, 22, 2116, 20], [1884, 26, 2116, 24], [1885, 4, 2117, 2], [1886, 4, 2119, 2], [1886, 8, 2119, 6, "_proto"], [1886, 14, 2119, 12], [1886, 17, 2119, 15, "PinchRecognizer"], [1886, 32, 2119, 30], [1886, 33, 2119, 31, "prototype"], [1886, 42, 2119, 40], [1887, 4, 2121, 2, "_proto"], [1887, 10, 2121, 8], [1887, 11, 2121, 9, "getTouchAction"], [1887, 25, 2121, 23], [1887, 28, 2121, 26], [1887, 37, 2121, 35, "getTouchAction"], [1887, 51, 2121, 49, "getTouchAction"], [1887, 52, 2121, 49], [1887, 54, 2121, 52], [1888, 6, 2122, 4], [1888, 13, 2122, 11], [1888, 14, 2122, 12, "TOUCH_ACTION_NONE"], [1888, 31, 2122, 29], [1888, 32, 2122, 30], [1889, 4, 2123, 2], [1889, 5, 2123, 3], [1890, 4, 2125, 2, "_proto"], [1890, 10, 2125, 8], [1890, 11, 2125, 9, "attrTest"], [1890, 19, 2125, 17], [1890, 22, 2125, 20], [1890, 31, 2125, 29, "attrTest"], [1890, 39, 2125, 37, "attrTest"], [1890, 40, 2125, 38, "input"], [1890, 45, 2125, 43], [1890, 47, 2125, 45], [1891, 6, 2126, 4], [1891, 13, 2126, 11, "_AttrRecognizer"], [1891, 28, 2126, 26], [1891, 29, 2126, 27, "prototype"], [1891, 38, 2126, 36], [1891, 39, 2126, 37, "attrTest"], [1891, 47, 2126, 45], [1891, 48, 2126, 46, "call"], [1891, 52, 2126, 50], [1891, 53, 2126, 51], [1891, 57, 2126, 55], [1891, 59, 2126, 57, "input"], [1891, 64, 2126, 62], [1891, 65, 2126, 63], [1891, 70, 2126, 68, "Math"], [1891, 74, 2126, 72], [1891, 75, 2126, 73, "abs"], [1891, 78, 2126, 76], [1891, 79, 2126, 77, "input"], [1891, 84, 2126, 82], [1891, 85, 2126, 83, "scale"], [1891, 90, 2126, 88], [1891, 93, 2126, 91], [1891, 94, 2126, 92], [1891, 95, 2126, 93], [1891, 98, 2126, 96], [1891, 102, 2126, 100], [1891, 103, 2126, 101, "options"], [1891, 110, 2126, 108], [1891, 111, 2126, 109, "threshold"], [1891, 120, 2126, 118], [1891, 124, 2126, 122], [1891, 128, 2126, 126], [1891, 129, 2126, 127, "state"], [1891, 134, 2126, 132], [1891, 137, 2126, 135, "STATE_BEGAN"], [1891, 148, 2126, 146], [1891, 149, 2126, 147], [1892, 4, 2127, 2], [1892, 5, 2127, 3], [1893, 4, 2129, 2, "_proto"], [1893, 10, 2129, 8], [1893, 11, 2129, 9, "emit"], [1893, 15, 2129, 13], [1893, 18, 2129, 16], [1893, 27, 2129, 25, "emit"], [1893, 31, 2129, 29, "emit"], [1893, 32, 2129, 30, "input"], [1893, 37, 2129, 35], [1893, 39, 2129, 37], [1894, 6, 2130, 4], [1894, 10, 2130, 8, "input"], [1894, 15, 2130, 13], [1894, 16, 2130, 14, "scale"], [1894, 21, 2130, 19], [1894, 26, 2130, 24], [1894, 27, 2130, 25], [1894, 29, 2130, 27], [1895, 8, 2131, 6], [1895, 12, 2131, 10, "inOut"], [1895, 17, 2131, 15], [1895, 20, 2131, 18, "input"], [1895, 25, 2131, 23], [1895, 26, 2131, 24, "scale"], [1895, 31, 2131, 29], [1895, 34, 2131, 32], [1895, 35, 2131, 33], [1895, 38, 2131, 36], [1895, 42, 2131, 40], [1895, 45, 2131, 43], [1895, 50, 2131, 48], [1896, 8, 2132, 6, "input"], [1896, 13, 2132, 11], [1896, 14, 2132, 12, "additionalEvent"], [1896, 29, 2132, 27], [1896, 32, 2132, 30], [1896, 36, 2132, 34], [1896, 37, 2132, 35, "options"], [1896, 44, 2132, 42], [1896, 45, 2132, 43, "event"], [1896, 50, 2132, 48], [1896, 53, 2132, 51, "inOut"], [1896, 58, 2132, 56], [1897, 6, 2133, 4], [1898, 6, 2135, 4, "_AttrRecognizer"], [1898, 21, 2135, 19], [1898, 22, 2135, 20, "prototype"], [1898, 31, 2135, 29], [1898, 32, 2135, 30, "emit"], [1898, 36, 2135, 34], [1898, 37, 2135, 35, "call"], [1898, 41, 2135, 39], [1898, 42, 2135, 40], [1898, 46, 2135, 44], [1898, 48, 2135, 46, "input"], [1898, 53, 2135, 51], [1898, 54, 2135, 52], [1899, 4, 2136, 2], [1899, 5, 2136, 3], [1900, 4, 2138, 2], [1900, 11, 2138, 9, "PinchRecognizer"], [1900, 26, 2138, 24], [1901, 2, 2139, 0], [1901, 3, 2139, 1], [1901, 4, 2139, 2, "AttrRecognizer"], [1901, 18, 2139, 16], [1901, 19, 2139, 17], [1903, 2, 2141, 0], [1904, 0, 2142, 0], [1905, 0, 2143, 0], [1906, 0, 2144, 0], [1907, 0, 2145, 0], [1908, 0, 2146, 0], [1909, 0, 2147, 0], [1911, 2, 2149, 0], [1911, 6, 2149, 4, "RotateRecognizer"], [1911, 22, 2149, 20], [1911, 25, 2149, 20, "exports"], [1911, 32, 2149, 20], [1911, 33, 2149, 20, "Rotate"], [1911, 39, 2149, 20], [1911, 42, 2150, 0], [1912, 2, 2151, 0], [1912, 12, 2151, 10, "_AttrRecognizer"], [1912, 27, 2151, 25], [1912, 29, 2151, 27], [1913, 4, 2152, 2, "_inherits<PERSON><PERSON>e"], [1913, 18, 2152, 16], [1913, 19, 2152, 17, "RotateRecognizer"], [1913, 35, 2152, 33], [1913, 37, 2152, 35, "_AttrRecognizer"], [1913, 52, 2152, 50], [1913, 53, 2152, 51], [1914, 4, 2154, 2], [1914, 13, 2154, 11, "RotateRecognizer"], [1914, 29, 2154, 27, "RotateRecognizer"], [1914, 30, 2154, 28, "options"], [1914, 37, 2154, 35], [1914, 39, 2154, 37], [1915, 6, 2155, 4], [1915, 10, 2155, 8, "options"], [1915, 17, 2155, 15], [1915, 22, 2155, 20], [1915, 27, 2155, 25], [1915, 28, 2155, 26], [1915, 30, 2155, 28], [1916, 8, 2156, 6, "options"], [1916, 15, 2156, 13], [1916, 18, 2156, 16], [1916, 19, 2156, 17], [1916, 20, 2156, 18], [1917, 6, 2157, 4], [1918, 6, 2159, 4], [1918, 13, 2159, 11, "_AttrRecognizer"], [1918, 28, 2159, 26], [1918, 29, 2159, 27, "call"], [1918, 33, 2159, 31], [1918, 34, 2159, 32], [1918, 38, 2159, 36], [1918, 40, 2159, 38, "_extends"], [1918, 48, 2159, 46], [1918, 49, 2159, 47], [1919, 8, 2160, 6, "event"], [1919, 13, 2160, 11], [1919, 15, 2160, 13], [1919, 23, 2160, 21], [1920, 8, 2161, 6, "threshold"], [1920, 17, 2161, 15], [1920, 19, 2161, 17], [1920, 20, 2161, 18], [1921, 8, 2162, 6, "pointers"], [1921, 16, 2162, 14], [1921, 18, 2162, 16], [1922, 6, 2163, 4], [1922, 7, 2163, 5], [1922, 9, 2163, 7, "options"], [1922, 16, 2163, 14], [1922, 17, 2163, 15], [1922, 18, 2163, 16], [1922, 22, 2163, 20], [1922, 26, 2163, 24], [1923, 4, 2164, 2], [1924, 4, 2166, 2], [1924, 8, 2166, 6, "_proto"], [1924, 14, 2166, 12], [1924, 17, 2166, 15, "RotateRecognizer"], [1924, 33, 2166, 31], [1924, 34, 2166, 32, "prototype"], [1924, 43, 2166, 41], [1925, 4, 2168, 2, "_proto"], [1925, 10, 2168, 8], [1925, 11, 2168, 9, "getTouchAction"], [1925, 25, 2168, 23], [1925, 28, 2168, 26], [1925, 37, 2168, 35, "getTouchAction"], [1925, 51, 2168, 49, "getTouchAction"], [1925, 52, 2168, 49], [1925, 54, 2168, 52], [1926, 6, 2169, 4], [1926, 13, 2169, 11], [1926, 14, 2169, 12, "TOUCH_ACTION_NONE"], [1926, 31, 2169, 29], [1926, 32, 2169, 30], [1927, 4, 2170, 2], [1927, 5, 2170, 3], [1928, 4, 2172, 2, "_proto"], [1928, 10, 2172, 8], [1928, 11, 2172, 9, "attrTest"], [1928, 19, 2172, 17], [1928, 22, 2172, 20], [1928, 31, 2172, 29, "attrTest"], [1928, 39, 2172, 37, "attrTest"], [1928, 40, 2172, 38, "input"], [1928, 45, 2172, 43], [1928, 47, 2172, 45], [1929, 6, 2173, 4], [1929, 13, 2173, 11, "_AttrRecognizer"], [1929, 28, 2173, 26], [1929, 29, 2173, 27, "prototype"], [1929, 38, 2173, 36], [1929, 39, 2173, 37, "attrTest"], [1929, 47, 2173, 45], [1929, 48, 2173, 46, "call"], [1929, 52, 2173, 50], [1929, 53, 2173, 51], [1929, 57, 2173, 55], [1929, 59, 2173, 57, "input"], [1929, 64, 2173, 62], [1929, 65, 2173, 63], [1929, 70, 2173, 68, "Math"], [1929, 74, 2173, 72], [1929, 75, 2173, 73, "abs"], [1929, 78, 2173, 76], [1929, 79, 2173, 77, "input"], [1929, 84, 2173, 82], [1929, 85, 2173, 83, "rotation"], [1929, 93, 2173, 91], [1929, 94, 2173, 92], [1929, 97, 2173, 95], [1929, 101, 2173, 99], [1929, 102, 2173, 100, "options"], [1929, 109, 2173, 107], [1929, 110, 2173, 108, "threshold"], [1929, 119, 2173, 117], [1929, 123, 2173, 121], [1929, 127, 2173, 125], [1929, 128, 2173, 126, "state"], [1929, 133, 2173, 131], [1929, 136, 2173, 134, "STATE_BEGAN"], [1929, 147, 2173, 145], [1929, 148, 2173, 146], [1930, 4, 2174, 2], [1930, 5, 2174, 3], [1931, 4, 2176, 2], [1931, 11, 2176, 9, "RotateRecognizer"], [1931, 27, 2176, 25], [1932, 2, 2177, 0], [1932, 3, 2177, 1], [1932, 4, 2177, 2, "AttrRecognizer"], [1932, 18, 2177, 16], [1932, 19, 2177, 17], [1934, 2, 2179, 0], [1935, 0, 2180, 0], [1936, 0, 2181, 0], [1937, 0, 2182, 0], [1938, 0, 2183, 0], [1939, 0, 2184, 0], [1940, 0, 2185, 0], [1942, 2, 2187, 0], [1942, 6, 2187, 4, "PressRecognizer"], [1942, 21, 2187, 19], [1942, 24, 2187, 19, "exports"], [1942, 31, 2187, 19], [1942, 32, 2187, 19, "Press"], [1942, 37, 2187, 19], [1942, 40, 2188, 0], [1943, 2, 2189, 0], [1943, 12, 2189, 10, "_Recognizer"], [1943, 23, 2189, 21], [1943, 25, 2189, 23], [1944, 4, 2190, 2, "_inherits<PERSON><PERSON>e"], [1944, 18, 2190, 16], [1944, 19, 2190, 17, "PressRecognizer"], [1944, 34, 2190, 32], [1944, 36, 2190, 34, "_Recognizer"], [1944, 47, 2190, 45], [1944, 48, 2190, 46], [1945, 4, 2192, 2], [1945, 13, 2192, 11, "PressRecognizer"], [1945, 28, 2192, 26, "PressRecognizer"], [1945, 29, 2192, 27, "options"], [1945, 36, 2192, 34], [1945, 38, 2192, 36], [1946, 6, 2193, 4], [1946, 10, 2193, 8, "_this"], [1946, 15, 2193, 13], [1947, 6, 2195, 4], [1947, 10, 2195, 8, "options"], [1947, 17, 2195, 15], [1947, 22, 2195, 20], [1947, 27, 2195, 25], [1947, 28, 2195, 26], [1947, 30, 2195, 28], [1948, 8, 2196, 6, "options"], [1948, 15, 2196, 13], [1948, 18, 2196, 16], [1948, 19, 2196, 17], [1948, 20, 2196, 18], [1949, 6, 2197, 4], [1950, 6, 2199, 4, "_this"], [1950, 11, 2199, 9], [1950, 14, 2199, 12, "_Recognizer"], [1950, 25, 2199, 23], [1950, 26, 2199, 24, "call"], [1950, 30, 2199, 28], [1950, 31, 2199, 29], [1950, 35, 2199, 33], [1950, 37, 2199, 35, "_extends"], [1950, 45, 2199, 43], [1950, 46, 2199, 44], [1951, 8, 2200, 6, "event"], [1951, 13, 2200, 11], [1951, 15, 2200, 13], [1951, 22, 2200, 20], [1952, 8, 2201, 6, "pointers"], [1952, 16, 2201, 14], [1952, 18, 2201, 16], [1952, 19, 2201, 17], [1953, 8, 2202, 6, "time"], [1953, 12, 2202, 10], [1953, 14, 2202, 12], [1953, 17, 2202, 15], [1954, 8, 2203, 6], [1955, 8, 2204, 6, "threshold"], [1955, 17, 2204, 15], [1955, 19, 2204, 17], [1956, 6, 2205, 4], [1956, 7, 2205, 5], [1956, 9, 2205, 7, "options"], [1956, 16, 2205, 14], [1956, 17, 2205, 15], [1956, 18, 2205, 16], [1956, 22, 2205, 20], [1956, 26, 2205, 24], [1957, 6, 2206, 4, "_this"], [1957, 11, 2206, 9], [1957, 12, 2206, 10, "_timer"], [1957, 18, 2206, 16], [1957, 21, 2206, 19], [1957, 25, 2206, 23], [1958, 6, 2207, 4, "_this"], [1958, 11, 2207, 9], [1958, 12, 2207, 10, "_input"], [1958, 18, 2207, 16], [1958, 21, 2207, 19], [1958, 25, 2207, 23], [1959, 6, 2208, 4], [1959, 13, 2208, 11, "_this"], [1959, 18, 2208, 16], [1960, 4, 2209, 2], [1961, 4, 2211, 2], [1961, 8, 2211, 6, "_proto"], [1961, 14, 2211, 12], [1961, 17, 2211, 15, "PressRecognizer"], [1961, 32, 2211, 30], [1961, 33, 2211, 31, "prototype"], [1961, 42, 2211, 40], [1962, 4, 2213, 2, "_proto"], [1962, 10, 2213, 8], [1962, 11, 2213, 9, "getTouchAction"], [1962, 25, 2213, 23], [1962, 28, 2213, 26], [1962, 37, 2213, 35, "getTouchAction"], [1962, 51, 2213, 49, "getTouchAction"], [1962, 52, 2213, 49], [1962, 54, 2213, 52], [1963, 6, 2214, 4], [1963, 13, 2214, 11], [1963, 14, 2214, 12, "TOUCH_ACTION_AUTO"], [1963, 31, 2214, 29], [1963, 32, 2214, 30], [1964, 4, 2215, 2], [1964, 5, 2215, 3], [1965, 4, 2217, 2, "_proto"], [1965, 10, 2217, 8], [1965, 11, 2217, 9, "process"], [1965, 18, 2217, 16], [1965, 21, 2217, 19], [1965, 30, 2217, 28, "process"], [1965, 37, 2217, 35, "process"], [1965, 38, 2217, 36, "input"], [1965, 43, 2217, 41], [1965, 45, 2217, 43], [1966, 6, 2218, 4], [1966, 10, 2218, 8, "_this2"], [1966, 16, 2218, 14], [1966, 19, 2218, 17], [1966, 23, 2218, 21], [1967, 6, 2220, 4], [1967, 10, 2220, 8, "options"], [1967, 17, 2220, 15], [1967, 20, 2220, 18], [1967, 24, 2220, 22], [1967, 25, 2220, 23, "options"], [1967, 32, 2220, 30], [1968, 6, 2221, 4], [1968, 10, 2221, 8, "validPointers"], [1968, 23, 2221, 21], [1968, 26, 2221, 24, "input"], [1968, 31, 2221, 29], [1968, 32, 2221, 30, "pointers"], [1968, 40, 2221, 38], [1968, 41, 2221, 39, "length"], [1968, 47, 2221, 45], [1968, 52, 2221, 50, "options"], [1968, 59, 2221, 57], [1968, 60, 2221, 58, "pointers"], [1968, 68, 2221, 66], [1969, 6, 2222, 4], [1969, 10, 2222, 8, "validMovement"], [1969, 23, 2222, 21], [1969, 26, 2222, 24, "input"], [1969, 31, 2222, 29], [1969, 32, 2222, 30, "distance"], [1969, 40, 2222, 38], [1969, 43, 2222, 41, "options"], [1969, 50, 2222, 48], [1969, 51, 2222, 49, "threshold"], [1969, 60, 2222, 58], [1970, 6, 2223, 4], [1970, 10, 2223, 8, "validTime"], [1970, 19, 2223, 17], [1970, 22, 2223, 20, "input"], [1970, 27, 2223, 25], [1970, 28, 2223, 26, "deltaTime"], [1970, 37, 2223, 35], [1970, 40, 2223, 38, "options"], [1970, 47, 2223, 45], [1970, 48, 2223, 46, "time"], [1970, 52, 2223, 50], [1971, 6, 2224, 4], [1971, 10, 2224, 8], [1971, 11, 2224, 9, "_input"], [1971, 17, 2224, 15], [1971, 20, 2224, 18, "input"], [1971, 25, 2224, 23], [1971, 26, 2224, 24], [1971, 27, 2224, 25], [1972, 6, 2225, 4], [1974, 6, 2227, 4], [1974, 10, 2227, 8], [1974, 11, 2227, 9, "validMovement"], [1974, 24, 2227, 22], [1974, 28, 2227, 26], [1974, 29, 2227, 27, "validPointers"], [1974, 42, 2227, 40], [1974, 46, 2227, 44, "input"], [1974, 51, 2227, 49], [1974, 52, 2227, 50, "eventType"], [1974, 61, 2227, 59], [1974, 65, 2227, 63, "INPUT_END"], [1974, 74, 2227, 72], [1974, 77, 2227, 75, "INPUT_CANCEL"], [1974, 89, 2227, 87], [1974, 90, 2227, 88], [1974, 94, 2227, 92], [1974, 95, 2227, 93, "validTime"], [1974, 104, 2227, 102], [1974, 106, 2227, 104], [1975, 8, 2228, 6], [1975, 12, 2228, 10], [1975, 13, 2228, 11, "reset"], [1975, 18, 2228, 16], [1975, 19, 2228, 17], [1975, 20, 2228, 18], [1976, 6, 2229, 4], [1976, 7, 2229, 5], [1976, 13, 2229, 11], [1976, 17, 2229, 15, "input"], [1976, 22, 2229, 20], [1976, 23, 2229, 21, "eventType"], [1976, 32, 2229, 30], [1976, 35, 2229, 33, "INPUT_START"], [1976, 46, 2229, 44], [1976, 48, 2229, 46], [1977, 8, 2230, 6], [1977, 12, 2230, 10], [1977, 13, 2230, 11, "reset"], [1977, 18, 2230, 16], [1977, 19, 2230, 17], [1977, 20, 2230, 18], [1978, 8, 2231, 6], [1978, 12, 2231, 10], [1978, 13, 2231, 11, "_timer"], [1978, 19, 2231, 17], [1978, 22, 2231, 20, "setTimeout"], [1978, 32, 2231, 30], [1978, 33, 2231, 31], [1978, 45, 2231, 43], [1979, 10, 2232, 8, "_this2"], [1979, 16, 2232, 14], [1979, 17, 2232, 15, "state"], [1979, 22, 2232, 20], [1979, 25, 2232, 23, "STATE_RECOGNIZED"], [1979, 41, 2232, 39], [1980, 10, 2234, 8, "_this2"], [1980, 16, 2234, 14], [1980, 17, 2234, 15, "tryEmit"], [1980, 24, 2234, 22], [1980, 25, 2234, 23], [1980, 26, 2234, 24], [1981, 8, 2235, 6], [1981, 9, 2235, 7], [1981, 11, 2235, 9, "options"], [1981, 18, 2235, 16], [1981, 19, 2235, 17, "time"], [1981, 23, 2235, 21], [1981, 24, 2235, 22], [1982, 6, 2236, 4], [1982, 7, 2236, 5], [1982, 13, 2236, 11], [1982, 17, 2236, 15, "input"], [1982, 22, 2236, 20], [1982, 23, 2236, 21, "eventType"], [1982, 32, 2236, 30], [1982, 35, 2236, 33, "INPUT_END"], [1982, 44, 2236, 42], [1982, 46, 2236, 44], [1983, 8, 2237, 6], [1983, 15, 2237, 13, "STATE_RECOGNIZED"], [1983, 31, 2237, 29], [1984, 6, 2238, 4], [1985, 6, 2240, 4], [1985, 13, 2240, 11, "STATE_FAILED"], [1985, 25, 2240, 23], [1986, 4, 2241, 2], [1986, 5, 2241, 3], [1987, 4, 2243, 2, "_proto"], [1987, 10, 2243, 8], [1987, 11, 2243, 9, "reset"], [1987, 16, 2243, 14], [1987, 19, 2243, 17], [1987, 28, 2243, 26, "reset"], [1987, 33, 2243, 31, "reset"], [1987, 34, 2243, 31], [1987, 36, 2243, 34], [1988, 6, 2244, 4, "clearTimeout"], [1988, 18, 2244, 16], [1988, 19, 2244, 17], [1988, 23, 2244, 21], [1988, 24, 2244, 22, "_timer"], [1988, 30, 2244, 28], [1988, 31, 2244, 29], [1989, 4, 2245, 2], [1989, 5, 2245, 3], [1990, 4, 2247, 2, "_proto"], [1990, 10, 2247, 8], [1990, 11, 2247, 9, "emit"], [1990, 15, 2247, 13], [1990, 18, 2247, 16], [1990, 27, 2247, 25, "emit"], [1990, 31, 2247, 29, "emit"], [1990, 32, 2247, 30, "input"], [1990, 37, 2247, 35], [1990, 39, 2247, 37], [1991, 6, 2248, 4], [1991, 10, 2248, 8], [1991, 14, 2248, 12], [1991, 15, 2248, 13, "state"], [1991, 20, 2248, 18], [1991, 25, 2248, 23, "STATE_RECOGNIZED"], [1991, 41, 2248, 39], [1991, 43, 2248, 41], [1992, 8, 2249, 6], [1993, 6, 2250, 4], [1994, 6, 2252, 4], [1994, 10, 2252, 8, "input"], [1994, 15, 2252, 13], [1994, 19, 2252, 17, "input"], [1994, 24, 2252, 22], [1994, 25, 2252, 23, "eventType"], [1994, 34, 2252, 32], [1994, 37, 2252, 35, "INPUT_END"], [1994, 46, 2252, 44], [1994, 48, 2252, 46], [1995, 8, 2253, 6], [1995, 12, 2253, 10], [1995, 13, 2253, 11, "manager"], [1995, 20, 2253, 18], [1995, 21, 2253, 19, "emit"], [1995, 25, 2253, 23], [1995, 26, 2253, 24], [1995, 30, 2253, 28], [1995, 31, 2253, 29, "options"], [1995, 38, 2253, 36], [1995, 39, 2253, 37, "event"], [1995, 44, 2253, 42], [1995, 47, 2253, 45], [1995, 51, 2253, 49], [1995, 53, 2253, 51, "input"], [1995, 58, 2253, 56], [1995, 59, 2253, 57], [1996, 6, 2254, 4], [1996, 7, 2254, 5], [1996, 13, 2254, 11], [1997, 8, 2255, 6], [1997, 12, 2255, 10], [1997, 13, 2255, 11, "_input"], [1997, 19, 2255, 17], [1997, 20, 2255, 18, "timeStamp"], [1997, 29, 2255, 27], [1997, 32, 2255, 30, "now"], [1997, 35, 2255, 33], [1997, 36, 2255, 34], [1997, 37, 2255, 35], [1998, 8, 2256, 6], [1998, 12, 2256, 10], [1998, 13, 2256, 11, "manager"], [1998, 20, 2256, 18], [1998, 21, 2256, 19, "emit"], [1998, 25, 2256, 23], [1998, 26, 2256, 24], [1998, 30, 2256, 28], [1998, 31, 2256, 29, "options"], [1998, 38, 2256, 36], [1998, 39, 2256, 37, "event"], [1998, 44, 2256, 42], [1998, 46, 2256, 44], [1998, 50, 2256, 48], [1998, 51, 2256, 49, "_input"], [1998, 57, 2256, 55], [1998, 58, 2256, 56], [1999, 6, 2257, 4], [2000, 4, 2258, 2], [2000, 5, 2258, 3], [2001, 4, 2260, 2], [2001, 11, 2260, 9, "PressRecognizer"], [2001, 26, 2260, 24], [2002, 2, 2261, 0], [2002, 3, 2261, 1], [2002, 4, 2261, 2, "Recognizer"], [2002, 14, 2261, 12], [2002, 15, 2261, 13], [2003, 2, 2263, 0], [2003, 6, 2263, 4, "defaults"], [2003, 14, 2263, 12], [2003, 17, 2263, 15], [2004, 4, 2264, 2], [2005, 0, 2265, 0], [2006, 0, 2266, 0], [2007, 0, 2267, 0], [2008, 0, 2268, 0], [2009, 0, 2269, 0], [2010, 0, 2270, 0], [2011, 4, 2271, 2, "domEvents"], [2011, 13, 2271, 11], [2011, 15, 2271, 13], [2011, 20, 2271, 18], [2012, 4, 2273, 2], [2013, 0, 2274, 0], [2014, 0, 2275, 0], [2015, 0, 2276, 0], [2016, 0, 2277, 0], [2017, 0, 2278, 0], [2018, 0, 2279, 0], [2019, 4, 2280, 2, "touchAction"], [2019, 15, 2280, 13], [2019, 17, 2280, 15, "TOUCH_ACTION_COMPUTE"], [2019, 37, 2280, 35], [2020, 4, 2282, 2], [2021, 0, 2283, 0], [2022, 0, 2284, 0], [2023, 0, 2285, 0], [2024, 0, 2286, 0], [2025, 4, 2287, 2, "enable"], [2025, 10, 2287, 8], [2025, 12, 2287, 10], [2025, 16, 2287, 14], [2026, 4, 2289, 2], [2027, 0, 2290, 0], [2028, 0, 2291, 0], [2029, 0, 2292, 0], [2030, 0, 2293, 0], [2031, 0, 2294, 0], [2032, 0, 2295, 0], [2033, 0, 2296, 0], [2034, 4, 2297, 2, "inputTarget"], [2034, 15, 2297, 13], [2034, 17, 2297, 15], [2034, 21, 2297, 19], [2035, 4, 2299, 2], [2036, 0, 2300, 0], [2037, 0, 2301, 0], [2038, 0, 2302, 0], [2039, 0, 2303, 0], [2040, 0, 2304, 0], [2041, 4, 2305, 2, "inputClass"], [2041, 14, 2305, 12], [2041, 16, 2305, 14], [2041, 20, 2305, 18], [2042, 4, 2307, 2], [2043, 0, 2308, 0], [2044, 0, 2309, 0], [2045, 0, 2310, 0], [2046, 0, 2311, 0], [2047, 0, 2312, 0], [2048, 4, 2313, 2, "cssProps"], [2048, 12, 2313, 10], [2048, 14, 2313, 12], [2049, 6, 2314, 4], [2050, 0, 2315, 0], [2051, 0, 2316, 0], [2052, 0, 2317, 0], [2053, 0, 2318, 0], [2054, 0, 2319, 0], [2055, 6, 2320, 4, "userSelect"], [2055, 16, 2320, 14], [2055, 18, 2320, 16], [2055, 24, 2320, 22], [2056, 6, 2322, 4], [2057, 0, 2323, 0], [2058, 0, 2324, 0], [2059, 0, 2325, 0], [2060, 0, 2326, 0], [2061, 0, 2327, 0], [2062, 6, 2328, 4, "touchSelect"], [2062, 17, 2328, 15], [2062, 19, 2328, 17], [2062, 25, 2328, 23], [2063, 6, 2330, 4], [2064, 0, 2331, 0], [2065, 0, 2332, 0], [2066, 0, 2333, 0], [2067, 0, 2334, 0], [2068, 0, 2335, 0], [2069, 0, 2336, 0], [2070, 0, 2337, 0], [2071, 6, 2338, 4, "touchCallout"], [2071, 18, 2338, 16], [2071, 20, 2338, 18], [2071, 26, 2338, 24], [2072, 6, 2340, 4], [2073, 0, 2341, 0], [2074, 0, 2342, 0], [2075, 0, 2343, 0], [2076, 0, 2344, 0], [2077, 0, 2345, 0], [2078, 6, 2346, 4, "contentZooming"], [2078, 20, 2346, 18], [2078, 22, 2346, 20], [2078, 28, 2346, 26], [2079, 6, 2348, 4], [2080, 0, 2349, 0], [2081, 0, 2350, 0], [2082, 0, 2351, 0], [2083, 0, 2352, 0], [2084, 0, 2353, 0], [2085, 6, 2354, 4, "userDrag"], [2085, 14, 2354, 12], [2085, 16, 2354, 14], [2085, 22, 2354, 20], [2086, 6, 2356, 4], [2087, 0, 2357, 0], [2088, 0, 2358, 0], [2089, 0, 2359, 0], [2090, 0, 2360, 0], [2091, 0, 2361, 0], [2092, 0, 2362, 0], [2093, 6, 2363, 4, "tapHighlightColor"], [2093, 23, 2363, 21], [2093, 25, 2363, 23], [2094, 4, 2364, 2], [2095, 2, 2365, 0], [2095, 3, 2365, 1], [2096, 2, 2366, 0], [2097, 0, 2367, 0], [2098, 0, 2368, 0], [2099, 0, 2369, 0], [2100, 0, 2370, 0], [2101, 0, 2371, 0], [2102, 0, 2372, 0], [2104, 2, 2374, 0], [2104, 6, 2374, 4, "preset"], [2104, 12, 2374, 10], [2104, 15, 2374, 13], [2104, 16, 2374, 14], [2104, 17, 2374, 15, "RotateRecognizer"], [2104, 33, 2374, 31], [2104, 35, 2374, 33], [2105, 4, 2375, 2, "enable"], [2105, 10, 2375, 8], [2105, 12, 2375, 10], [2106, 2, 2376, 0], [2106, 3, 2376, 1], [2106, 4, 2376, 2], [2106, 6, 2376, 4], [2106, 7, 2376, 5, "PinchRecognizer"], [2106, 22, 2376, 20], [2106, 24, 2376, 22], [2107, 4, 2377, 2, "enable"], [2107, 10, 2377, 8], [2107, 12, 2377, 10], [2108, 2, 2378, 0], [2108, 3, 2378, 1], [2108, 5, 2378, 3], [2108, 6, 2378, 4], [2108, 14, 2378, 12], [2108, 15, 2378, 13], [2108, 16, 2378, 14], [2108, 18, 2378, 16], [2108, 19, 2378, 17, "SwipeRecognizer"], [2108, 34, 2378, 32], [2108, 36, 2378, 34], [2109, 4, 2379, 2, "direction"], [2109, 13, 2379, 11], [2109, 15, 2379, 13, "DIRECTION_HORIZONTAL"], [2110, 2, 2380, 0], [2110, 3, 2380, 1], [2110, 4, 2380, 2], [2110, 6, 2380, 4], [2110, 7, 2380, 5, "PanRecognizer"], [2110, 20, 2380, 18], [2110, 22, 2380, 20], [2111, 4, 2381, 2, "direction"], [2111, 13, 2381, 11], [2111, 15, 2381, 13, "DIRECTION_HORIZONTAL"], [2112, 2, 2382, 0], [2112, 3, 2382, 1], [2112, 5, 2382, 3], [2112, 6, 2382, 4], [2112, 13, 2382, 11], [2112, 14, 2382, 12], [2112, 15, 2382, 13], [2112, 17, 2382, 15], [2112, 18, 2382, 16, "TapRecognizer"], [2112, 31, 2382, 29], [2112, 32, 2382, 30], [2112, 34, 2382, 32], [2112, 35, 2382, 33, "TapRecognizer"], [2112, 48, 2382, 46], [2112, 50, 2382, 48], [2113, 4, 2383, 2, "event"], [2113, 9, 2383, 7], [2113, 11, 2383, 9], [2113, 22, 2383, 20], [2114, 4, 2384, 2, "taps"], [2114, 8, 2384, 6], [2114, 10, 2384, 8], [2115, 2, 2385, 0], [2115, 3, 2385, 1], [2115, 5, 2385, 3], [2115, 6, 2385, 4], [2115, 11, 2385, 9], [2115, 12, 2385, 10], [2115, 13, 2385, 11], [2115, 15, 2385, 13], [2115, 16, 2385, 14, "PressRecognizer"], [2115, 31, 2385, 29], [2115, 32, 2385, 30], [2115, 33, 2385, 31], [2116, 2, 2387, 0], [2116, 6, 2387, 4, "STOP"], [2116, 10, 2387, 8], [2116, 13, 2387, 11], [2116, 14, 2387, 12], [2117, 2, 2388, 0], [2117, 6, 2388, 4, "FORCED_STOP"], [2117, 17, 2388, 15], [2117, 20, 2388, 18], [2117, 21, 2388, 19], [2118, 2, 2389, 0], [2119, 0, 2390, 0], [2120, 0, 2391, 0], [2121, 0, 2392, 0], [2122, 0, 2393, 0], [2123, 0, 2394, 0], [2125, 2, 2396, 0], [2125, 11, 2396, 9, "toggleCssProps"], [2125, 25, 2396, 23, "toggleCssProps"], [2125, 26, 2396, 24, "manager"], [2125, 33, 2396, 31], [2125, 35, 2396, 33, "add"], [2125, 38, 2396, 36], [2125, 40, 2396, 38], [2126, 4, 2397, 2], [2126, 8, 2397, 6, "element"], [2126, 15, 2397, 13], [2126, 18, 2397, 16, "manager"], [2126, 25, 2397, 23], [2126, 26, 2397, 24, "element"], [2126, 33, 2397, 31], [2127, 4, 2399, 2], [2127, 8, 2399, 6], [2127, 9, 2399, 7, "element"], [2127, 16, 2399, 14], [2127, 17, 2399, 15, "style"], [2127, 22, 2399, 20], [2127, 24, 2399, 22], [2128, 6, 2400, 4], [2129, 4, 2401, 2], [2130, 4, 2403, 2], [2130, 8, 2403, 6, "prop"], [2130, 12, 2403, 10], [2131, 4, 2404, 2, "each"], [2131, 8, 2404, 6], [2131, 9, 2404, 7, "manager"], [2131, 16, 2404, 14], [2131, 17, 2404, 15, "options"], [2131, 24, 2404, 22], [2131, 25, 2404, 23, "cssProps"], [2131, 33, 2404, 31], [2131, 35, 2404, 33], [2131, 45, 2404, 43, "value"], [2131, 50, 2404, 48], [2131, 52, 2404, 50, "name"], [2131, 56, 2404, 54], [2131, 58, 2404, 56], [2132, 6, 2405, 4, "prop"], [2132, 10, 2405, 8], [2132, 13, 2405, 11, "prefixed"], [2132, 21, 2405, 19], [2132, 22, 2405, 20, "element"], [2132, 29, 2405, 27], [2132, 30, 2405, 28, "style"], [2132, 35, 2405, 33], [2132, 37, 2405, 35, "name"], [2132, 41, 2405, 39], [2132, 42, 2405, 40], [2133, 6, 2407, 4], [2133, 10, 2407, 8, "add"], [2133, 13, 2407, 11], [2133, 15, 2407, 13], [2134, 8, 2408, 6, "manager"], [2134, 15, 2408, 13], [2134, 16, 2408, 14, "oldCssProps"], [2134, 27, 2408, 25], [2134, 28, 2408, 26, "prop"], [2134, 32, 2408, 30], [2134, 33, 2408, 31], [2134, 36, 2408, 34, "element"], [2134, 43, 2408, 41], [2134, 44, 2408, 42, "style"], [2134, 49, 2408, 47], [2134, 50, 2408, 48, "prop"], [2134, 54, 2408, 52], [2134, 55, 2408, 53], [2135, 8, 2409, 6, "element"], [2135, 15, 2409, 13], [2135, 16, 2409, 14, "style"], [2135, 21, 2409, 19], [2135, 22, 2409, 20, "prop"], [2135, 26, 2409, 24], [2135, 27, 2409, 25], [2135, 30, 2409, 28, "value"], [2135, 35, 2409, 33], [2136, 6, 2410, 4], [2136, 7, 2410, 5], [2136, 13, 2410, 11], [2137, 8, 2411, 6, "element"], [2137, 15, 2411, 13], [2137, 16, 2411, 14, "style"], [2137, 21, 2411, 19], [2137, 22, 2411, 20, "prop"], [2137, 26, 2411, 24], [2137, 27, 2411, 25], [2137, 30, 2411, 28, "manager"], [2137, 37, 2411, 35], [2137, 38, 2411, 36, "oldCssProps"], [2137, 49, 2411, 47], [2137, 50, 2411, 48, "prop"], [2137, 54, 2411, 52], [2137, 55, 2411, 53], [2137, 59, 2411, 57], [2137, 61, 2411, 59], [2138, 6, 2412, 4], [2139, 4, 2413, 2], [2139, 5, 2413, 3], [2139, 6, 2413, 4], [2140, 4, 2415, 2], [2140, 8, 2415, 6], [2140, 9, 2415, 7, "add"], [2140, 12, 2415, 10], [2140, 14, 2415, 12], [2141, 6, 2416, 4, "manager"], [2141, 13, 2416, 11], [2141, 14, 2416, 12, "oldCssProps"], [2141, 25, 2416, 23], [2141, 28, 2416, 26], [2141, 29, 2416, 27], [2141, 30, 2416, 28], [2142, 4, 2417, 2], [2143, 2, 2418, 0], [2144, 2, 2419, 0], [2145, 0, 2420, 0], [2146, 0, 2421, 0], [2147, 0, 2422, 0], [2148, 0, 2423, 0], [2149, 0, 2424, 0], [2151, 2, 2427, 0], [2151, 11, 2427, 9, "triggerDomEvent"], [2151, 26, 2427, 24, "triggerDomEvent"], [2151, 27, 2427, 25, "event"], [2151, 32, 2427, 30], [2151, 34, 2427, 32, "data"], [2151, 38, 2427, 36], [2151, 40, 2427, 38], [2152, 4, 2428, 2], [2152, 8, 2428, 6, "gestureEvent"], [2152, 20, 2428, 18], [2152, 23, 2428, 21, "document"], [2152, 31, 2428, 29], [2152, 32, 2428, 30, "createEvent"], [2152, 43, 2428, 41], [2152, 44, 2428, 42], [2152, 51, 2428, 49], [2152, 52, 2428, 50], [2153, 4, 2429, 2, "gestureEvent"], [2153, 16, 2429, 14], [2153, 17, 2429, 15, "initEvent"], [2153, 26, 2429, 24], [2153, 27, 2429, 25, "event"], [2153, 32, 2429, 30], [2153, 34, 2429, 32], [2153, 38, 2429, 36], [2153, 40, 2429, 38], [2153, 44, 2429, 42], [2153, 45, 2429, 43], [2154, 4, 2430, 2, "gestureEvent"], [2154, 16, 2430, 14], [2154, 17, 2430, 15, "gesture"], [2154, 24, 2430, 22], [2154, 27, 2430, 25, "data"], [2154, 31, 2430, 29], [2155, 4, 2431, 2, "data"], [2155, 8, 2431, 6], [2155, 9, 2431, 7, "target"], [2155, 15, 2431, 13], [2155, 16, 2431, 14, "dispatchEvent"], [2155, 29, 2431, 27], [2155, 30, 2431, 28, "gestureEvent"], [2155, 42, 2431, 40], [2155, 43, 2431, 41], [2156, 2, 2432, 0], [2157, 2, 2433, 0], [2158, 0, 2434, 0], [2159, 0, 2435, 0], [2160, 0, 2436, 0], [2161, 0, 2437, 0], [2162, 0, 2438, 0], [2163, 0, 2439, 0], [2165, 2, 2442, 0], [2165, 6, 2442, 4, "Manager"], [2165, 13, 2442, 11], [2165, 16, 2442, 11, "exports"], [2165, 23, 2442, 11], [2165, 24, 2442, 11, "Manager"], [2165, 31, 2442, 11], [2165, 34, 2443, 0], [2166, 2, 2444, 0], [2166, 14, 2444, 12], [2167, 4, 2445, 2], [2167, 13, 2445, 11, "Manager"], [2167, 20, 2445, 18, "Manager"], [2167, 21, 2445, 19, "element"], [2167, 28, 2445, 26], [2167, 30, 2445, 28, "options"], [2167, 37, 2445, 35], [2167, 39, 2445, 37], [2168, 6, 2446, 4], [2168, 10, 2446, 8, "_this"], [2168, 15, 2446, 13], [2168, 18, 2446, 16], [2168, 22, 2446, 20], [2169, 6, 2448, 4], [2169, 10, 2448, 8], [2169, 11, 2448, 9, "options"], [2169, 18, 2448, 16], [2169, 21, 2448, 19, "assign$1"], [2169, 29, 2448, 27], [2169, 30, 2448, 28], [2169, 31, 2448, 29], [2169, 32, 2448, 30], [2169, 34, 2448, 32, "defaults"], [2169, 42, 2448, 40], [2169, 44, 2448, 42, "options"], [2169, 51, 2448, 49], [2169, 55, 2448, 53], [2169, 56, 2448, 54], [2169, 57, 2448, 55], [2169, 58, 2448, 56], [2170, 6, 2449, 4], [2170, 10, 2449, 8], [2170, 11, 2449, 9, "options"], [2170, 18, 2449, 16], [2170, 19, 2449, 17, "inputTarget"], [2170, 30, 2449, 28], [2170, 33, 2449, 31], [2170, 37, 2449, 35], [2170, 38, 2449, 36, "options"], [2170, 45, 2449, 43], [2170, 46, 2449, 44, "inputTarget"], [2170, 57, 2449, 55], [2170, 61, 2449, 59, "element"], [2170, 68, 2449, 66], [2171, 6, 2450, 4], [2171, 10, 2450, 8], [2171, 11, 2450, 9, "handlers"], [2171, 19, 2450, 17], [2171, 22, 2450, 20], [2171, 23, 2450, 21], [2171, 24, 2450, 22], [2172, 6, 2451, 4], [2172, 10, 2451, 8], [2172, 11, 2451, 9, "session"], [2172, 18, 2451, 16], [2172, 21, 2451, 19], [2172, 22, 2451, 20], [2172, 23, 2451, 21], [2173, 6, 2452, 4], [2173, 10, 2452, 8], [2173, 11, 2452, 9, "recognizers"], [2173, 22, 2452, 20], [2173, 25, 2452, 23], [2173, 27, 2452, 25], [2174, 6, 2453, 4], [2174, 10, 2453, 8], [2174, 11, 2453, 9, "oldCssProps"], [2174, 22, 2453, 20], [2174, 25, 2453, 23], [2174, 26, 2453, 24], [2174, 27, 2453, 25], [2175, 6, 2454, 4], [2175, 10, 2454, 8], [2175, 11, 2454, 9, "element"], [2175, 18, 2454, 16], [2175, 21, 2454, 19, "element"], [2175, 28, 2454, 26], [2176, 6, 2455, 4], [2176, 10, 2455, 8], [2176, 11, 2455, 9, "input"], [2176, 16, 2455, 14], [2176, 19, 2455, 17, "createInputInstance"], [2176, 38, 2455, 36], [2176, 39, 2455, 37], [2176, 43, 2455, 41], [2176, 44, 2455, 42], [2177, 6, 2456, 4], [2177, 10, 2456, 8], [2177, 11, 2456, 9, "touchAction"], [2177, 22, 2456, 20], [2177, 25, 2456, 23], [2177, 29, 2456, 27, "TouchAction"], [2177, 40, 2456, 38], [2177, 41, 2456, 39], [2177, 45, 2456, 43], [2177, 47, 2456, 45], [2177, 51, 2456, 49], [2177, 52, 2456, 50, "options"], [2177, 59, 2456, 57], [2177, 60, 2456, 58, "touchAction"], [2177, 71, 2456, 69], [2177, 72, 2456, 70], [2178, 6, 2457, 4, "toggleCssProps"], [2178, 20, 2457, 18], [2178, 21, 2457, 19], [2178, 25, 2457, 23], [2178, 27, 2457, 25], [2178, 31, 2457, 29], [2178, 32, 2457, 30], [2179, 6, 2458, 4, "each"], [2179, 10, 2458, 8], [2179, 11, 2458, 9], [2179, 15, 2458, 13], [2179, 16, 2458, 14, "options"], [2179, 23, 2458, 21], [2179, 24, 2458, 22, "recognizers"], [2179, 35, 2458, 33], [2179, 37, 2458, 35], [2179, 47, 2458, 45, "item"], [2179, 51, 2458, 49], [2179, 53, 2458, 51], [2180, 8, 2459, 6], [2180, 12, 2459, 10, "recognizer"], [2180, 22, 2459, 20], [2180, 25, 2459, 23, "_this"], [2180, 30, 2459, 28], [2180, 31, 2459, 29, "add"], [2180, 34, 2459, 32], [2180, 35, 2459, 33], [2180, 39, 2459, 37, "item"], [2180, 43, 2459, 41], [2180, 44, 2459, 42], [2180, 45, 2459, 43], [2180, 46, 2459, 44], [2180, 47, 2459, 45, "item"], [2180, 51, 2459, 49], [2180, 52, 2459, 50], [2180, 53, 2459, 51], [2180, 54, 2459, 52], [2180, 55, 2459, 53], [2180, 56, 2459, 54], [2181, 8, 2461, 6, "item"], [2181, 12, 2461, 10], [2181, 13, 2461, 11], [2181, 14, 2461, 12], [2181, 15, 2461, 13], [2181, 19, 2461, 17, "recognizer"], [2181, 29, 2461, 27], [2181, 30, 2461, 28, "recognizeWith"], [2181, 43, 2461, 41], [2181, 44, 2461, 42, "item"], [2181, 48, 2461, 46], [2181, 49, 2461, 47], [2181, 50, 2461, 48], [2181, 51, 2461, 49], [2181, 52, 2461, 50], [2182, 8, 2462, 6, "item"], [2182, 12, 2462, 10], [2182, 13, 2462, 11], [2182, 14, 2462, 12], [2182, 15, 2462, 13], [2182, 19, 2462, 17, "recognizer"], [2182, 29, 2462, 27], [2182, 30, 2462, 28, "requireFailure"], [2182, 44, 2462, 42], [2182, 45, 2462, 43, "item"], [2182, 49, 2462, 47], [2182, 50, 2462, 48], [2182, 51, 2462, 49], [2182, 52, 2462, 50], [2182, 53, 2462, 51], [2183, 6, 2463, 4], [2183, 7, 2463, 5], [2183, 9, 2463, 7], [2183, 13, 2463, 11], [2183, 14, 2463, 12], [2184, 4, 2464, 2], [2185, 4, 2465, 2], [2186, 0, 2466, 0], [2187, 0, 2467, 0], [2188, 0, 2468, 0], [2189, 0, 2469, 0], [2190, 0, 2470, 0], [2192, 4, 2473, 2], [2192, 8, 2473, 6, "_proto"], [2192, 14, 2473, 12], [2192, 17, 2473, 15, "Manager"], [2192, 24, 2473, 22], [2192, 25, 2473, 23, "prototype"], [2192, 34, 2473, 32], [2193, 4, 2475, 2, "_proto"], [2193, 10, 2475, 8], [2193, 11, 2475, 9, "set"], [2193, 14, 2475, 12], [2193, 17, 2475, 15], [2193, 26, 2475, 24, "set"], [2193, 29, 2475, 27, "set"], [2193, 30, 2475, 28, "options"], [2193, 37, 2475, 35], [2193, 39, 2475, 37], [2194, 6, 2476, 4, "assign$1"], [2194, 14, 2476, 12], [2194, 15, 2476, 13], [2194, 19, 2476, 17], [2194, 20, 2476, 18, "options"], [2194, 27, 2476, 25], [2194, 29, 2476, 27, "options"], [2194, 36, 2476, 34], [2194, 37, 2476, 35], [2194, 38, 2476, 36], [2194, 39, 2476, 37], [2196, 6, 2478, 4], [2196, 10, 2478, 8, "options"], [2196, 17, 2478, 15], [2196, 18, 2478, 16, "touchAction"], [2196, 29, 2478, 27], [2196, 31, 2478, 29], [2197, 8, 2479, 6], [2197, 12, 2479, 10], [2197, 13, 2479, 11, "touchAction"], [2197, 24, 2479, 22], [2197, 25, 2479, 23, "update"], [2197, 31, 2479, 29], [2197, 32, 2479, 30], [2197, 33, 2479, 31], [2198, 6, 2480, 4], [2199, 6, 2482, 4], [2199, 10, 2482, 8, "options"], [2199, 17, 2482, 15], [2199, 18, 2482, 16, "inputTarget"], [2199, 29, 2482, 27], [2199, 31, 2482, 29], [2200, 8, 2483, 6], [2201, 8, 2484, 6], [2201, 12, 2484, 10], [2201, 13, 2484, 11, "input"], [2201, 18, 2484, 16], [2201, 19, 2484, 17, "destroy"], [2201, 26, 2484, 24], [2201, 27, 2484, 25], [2201, 28, 2484, 26], [2202, 8, 2485, 6], [2202, 12, 2485, 10], [2202, 13, 2485, 11, "input"], [2202, 18, 2485, 16], [2202, 19, 2485, 17, "target"], [2202, 25, 2485, 23], [2202, 28, 2485, 26, "options"], [2202, 35, 2485, 33], [2202, 36, 2485, 34, "inputTarget"], [2202, 47, 2485, 45], [2203, 8, 2486, 6], [2203, 12, 2486, 10], [2203, 13, 2486, 11, "input"], [2203, 18, 2486, 16], [2203, 19, 2486, 17, "init"], [2203, 23, 2486, 21], [2203, 24, 2486, 22], [2203, 25, 2486, 23], [2204, 6, 2487, 4], [2205, 6, 2489, 4], [2205, 13, 2489, 11], [2205, 17, 2489, 15], [2206, 4, 2490, 2], [2206, 5, 2490, 3], [2207, 4, 2491, 2], [2208, 0, 2492, 0], [2209, 0, 2493, 0], [2210, 0, 2494, 0], [2211, 0, 2495, 0], [2212, 0, 2496, 0], [2213, 0, 2497, 0], [2215, 4, 2500, 2, "_proto"], [2215, 10, 2500, 8], [2215, 11, 2500, 9, "stop"], [2215, 15, 2500, 13], [2215, 18, 2500, 16], [2215, 27, 2500, 25, "stop"], [2215, 31, 2500, 29, "stop"], [2215, 32, 2500, 30, "force"], [2215, 37, 2500, 35], [2215, 39, 2500, 37], [2216, 6, 2501, 4], [2216, 10, 2501, 8], [2216, 11, 2501, 9, "session"], [2216, 18, 2501, 16], [2216, 19, 2501, 17, "stopped"], [2216, 26, 2501, 24], [2216, 29, 2501, 27, "force"], [2216, 34, 2501, 32], [2216, 37, 2501, 35, "FORCED_STOP"], [2216, 48, 2501, 46], [2216, 51, 2501, 49, "STOP"], [2216, 55, 2501, 53], [2217, 4, 2502, 2], [2217, 5, 2502, 3], [2218, 4, 2503, 2], [2219, 0, 2504, 0], [2220, 0, 2505, 0], [2221, 0, 2506, 0], [2222, 0, 2507, 0], [2223, 0, 2508, 0], [2224, 0, 2509, 0], [2226, 4, 2512, 2, "_proto"], [2226, 10, 2512, 8], [2226, 11, 2512, 9, "recognize"], [2226, 20, 2512, 18], [2226, 23, 2512, 21], [2226, 32, 2512, 30, "recognize"], [2226, 41, 2512, 39, "recognize"], [2226, 42, 2512, 40, "inputData"], [2226, 51, 2512, 49], [2226, 53, 2512, 51], [2227, 6, 2513, 4], [2227, 10, 2513, 8, "session"], [2227, 17, 2513, 15], [2227, 20, 2513, 18], [2227, 24, 2513, 22], [2227, 25, 2513, 23, "session"], [2227, 32, 2513, 30], [2228, 6, 2515, 4], [2228, 10, 2515, 8, "session"], [2228, 17, 2515, 15], [2228, 18, 2515, 16, "stopped"], [2228, 25, 2515, 23], [2228, 27, 2515, 25], [2229, 8, 2516, 6], [2230, 6, 2517, 4], [2230, 7, 2517, 5], [2230, 8, 2517, 6], [2232, 6, 2520, 4], [2232, 10, 2520, 8], [2232, 11, 2520, 9, "touchAction"], [2232, 22, 2520, 20], [2232, 23, 2520, 21, "preventDefaults"], [2232, 38, 2520, 36], [2232, 39, 2520, 37, "inputData"], [2232, 48, 2520, 46], [2232, 49, 2520, 47], [2233, 6, 2521, 4], [2233, 10, 2521, 8, "recognizer"], [2233, 20, 2521, 18], [2234, 6, 2522, 4], [2234, 10, 2522, 8, "recognizers"], [2234, 21, 2522, 19], [2234, 24, 2522, 22], [2234, 28, 2522, 26], [2234, 29, 2522, 27, "recognizers"], [2234, 40, 2522, 38], [2234, 41, 2522, 39], [2234, 42, 2522, 40], [2235, 6, 2523, 4], [2236, 6, 2524, 4], [2238, 6, 2526, 4], [2238, 10, 2526, 8, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2238, 23, 2526, 21], [2238, 26, 2526, 24, "session"], [2238, 33, 2526, 31], [2238, 34, 2526, 32, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2238, 47, 2526, 45], [2238, 48, 2526, 46], [2238, 49, 2526, 47], [2239, 6, 2527, 4], [2241, 6, 2529, 4], [2241, 10, 2529, 8], [2241, 11, 2529, 9, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2241, 24, 2529, 22], [2241, 28, 2529, 26, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2241, 41, 2529, 39], [2241, 45, 2529, 43, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2241, 58, 2529, 56], [2241, 59, 2529, 57, "state"], [2241, 64, 2529, 62], [2241, 67, 2529, 65, "STATE_RECOGNIZED"], [2241, 83, 2529, 81], [2241, 85, 2529, 83], [2242, 8, 2530, 6, "session"], [2242, 15, 2530, 13], [2242, 16, 2530, 14, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2242, 29, 2530, 27], [2242, 32, 2530, 30], [2242, 36, 2530, 34], [2243, 8, 2531, 6, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2243, 21, 2531, 19], [2243, 24, 2531, 22], [2243, 28, 2531, 26], [2244, 6, 2532, 4], [2245, 6, 2534, 4], [2245, 10, 2534, 8, "i"], [2245, 11, 2534, 9], [2245, 14, 2534, 12], [2245, 15, 2534, 13], [2246, 6, 2536, 4], [2246, 13, 2536, 11, "i"], [2246, 14, 2536, 12], [2246, 17, 2536, 15, "recognizers"], [2246, 28, 2536, 26], [2246, 29, 2536, 27, "length"], [2246, 35, 2536, 33], [2246, 37, 2536, 35], [2247, 8, 2537, 6, "recognizer"], [2247, 18, 2537, 16], [2247, 21, 2537, 19, "recognizers"], [2247, 32, 2537, 30], [2247, 33, 2537, 31, "i"], [2247, 34, 2537, 32], [2247, 35, 2537, 33], [2247, 36, 2537, 34], [2247, 37, 2537, 35], [2248, 8, 2538, 6], [2249, 8, 2539, 6], [2250, 8, 2540, 6], [2251, 8, 2541, 6], [2252, 8, 2542, 6], [2254, 8, 2544, 6], [2254, 12, 2544, 10, "session"], [2254, 19, 2544, 17], [2254, 20, 2544, 18, "stopped"], [2254, 27, 2544, 25], [2254, 32, 2544, 30, "FORCED_STOP"], [2254, 43, 2544, 41], [2255, 8, 2544, 47], [2256, 8, 2545, 6], [2256, 9, 2545, 7, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2256, 22, 2545, 20], [2256, 26, 2545, 24, "recognizer"], [2256, 36, 2545, 34], [2256, 41, 2545, 39, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2256, 54, 2545, 52], [2257, 8, 2545, 56], [2258, 8, 2546, 6, "recognizer"], [2258, 18, 2546, 16], [2258, 19, 2546, 17, "canRecognizeWith"], [2258, 35, 2546, 33], [2258, 36, 2546, 34, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2258, 49, 2546, 47], [2258, 50, 2546, 48], [2258, 51, 2546, 49], [2258, 53, 2546, 51], [2259, 10, 2547, 8], [2260, 10, 2548, 8, "recognizer"], [2260, 20, 2548, 18], [2260, 21, 2548, 19, "recognize"], [2260, 30, 2548, 28], [2260, 31, 2548, 29, "inputData"], [2260, 40, 2548, 38], [2260, 41, 2548, 39], [2261, 8, 2549, 6], [2261, 9, 2549, 7], [2261, 15, 2549, 13], [2262, 10, 2550, 8, "recognizer"], [2262, 20, 2550, 18], [2262, 21, 2550, 19, "reset"], [2262, 26, 2550, 24], [2262, 27, 2550, 25], [2262, 28, 2550, 26], [2263, 8, 2551, 6], [2263, 9, 2551, 7], [2263, 10, 2551, 8], [2264, 8, 2552, 6], [2266, 8, 2555, 6], [2266, 12, 2555, 10], [2266, 13, 2555, 11, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2266, 26, 2555, 24], [2266, 30, 2555, 28, "recognizer"], [2266, 40, 2555, 38], [2266, 41, 2555, 39, "state"], [2266, 46, 2555, 44], [2266, 50, 2555, 48, "STATE_BEGAN"], [2266, 61, 2555, 59], [2266, 64, 2555, 62, "STATE_CHANGED"], [2266, 77, 2555, 75], [2266, 80, 2555, 78, "STATE_ENDED"], [2266, 91, 2555, 89], [2266, 92, 2555, 90], [2266, 94, 2555, 92], [2267, 10, 2556, 8, "session"], [2267, 17, 2556, 15], [2267, 18, 2556, 16, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2267, 31, 2556, 29], [2267, 34, 2556, 32, "recognizer"], [2267, 44, 2556, 42], [2268, 10, 2557, 8, "cur<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2268, 23, 2557, 21], [2268, 26, 2557, 24, "recognizer"], [2268, 36, 2557, 34], [2269, 8, 2558, 6], [2270, 8, 2560, 6, "i"], [2270, 9, 2560, 7], [2270, 11, 2560, 9], [2271, 6, 2561, 4], [2272, 4, 2562, 2], [2272, 5, 2562, 3], [2273, 4, 2563, 2], [2274, 0, 2564, 0], [2275, 0, 2565, 0], [2276, 0, 2566, 0], [2277, 0, 2567, 0], [2278, 0, 2568, 0], [2280, 4, 2571, 2, "_proto"], [2280, 10, 2571, 8], [2280, 11, 2571, 9, "get"], [2280, 14, 2571, 12], [2280, 17, 2571, 15], [2280, 26, 2571, 24, "get"], [2280, 29, 2571, 27, "get"], [2280, 30, 2571, 28, "recognizer"], [2280, 40, 2571, 38], [2280, 42, 2571, 40], [2281, 6, 2572, 4], [2281, 10, 2572, 8, "recognizer"], [2281, 20, 2572, 18], [2281, 32, 2572, 30, "Recognizer"], [2281, 42, 2572, 40], [2281, 44, 2572, 42], [2282, 8, 2573, 6], [2282, 15, 2573, 13, "recognizer"], [2282, 25, 2573, 23], [2283, 6, 2574, 4], [2284, 6, 2576, 4], [2284, 10, 2576, 8, "recognizers"], [2284, 21, 2576, 19], [2284, 24, 2576, 22], [2284, 28, 2576, 26], [2284, 29, 2576, 27, "recognizers"], [2284, 40, 2576, 38], [2285, 6, 2578, 4], [2285, 11, 2578, 9], [2285, 15, 2578, 13, "i"], [2285, 16, 2578, 14], [2285, 19, 2578, 17], [2285, 20, 2578, 18], [2285, 22, 2578, 20, "i"], [2285, 23, 2578, 21], [2285, 26, 2578, 24, "recognizers"], [2285, 37, 2578, 35], [2285, 38, 2578, 36, "length"], [2285, 44, 2578, 42], [2285, 46, 2578, 44, "i"], [2285, 47, 2578, 45], [2285, 49, 2578, 47], [2285, 51, 2578, 49], [2286, 8, 2579, 6], [2286, 12, 2579, 10, "recognizers"], [2286, 23, 2579, 21], [2286, 24, 2579, 22, "i"], [2286, 25, 2579, 23], [2286, 26, 2579, 24], [2286, 27, 2579, 25, "options"], [2286, 34, 2579, 32], [2286, 35, 2579, 33, "event"], [2286, 40, 2579, 38], [2286, 45, 2579, 43, "recognizer"], [2286, 55, 2579, 53], [2286, 57, 2579, 55], [2287, 10, 2580, 8], [2287, 17, 2580, 15, "recognizers"], [2287, 28, 2580, 26], [2287, 29, 2580, 27, "i"], [2287, 30, 2580, 28], [2287, 31, 2580, 29], [2288, 8, 2581, 6], [2289, 6, 2582, 4], [2290, 6, 2584, 4], [2290, 13, 2584, 11], [2290, 17, 2584, 15], [2291, 4, 2585, 2], [2291, 5, 2585, 3], [2292, 4, 2586, 2], [2293, 0, 2587, 0], [2294, 0, 2588, 0], [2295, 0, 2589, 0], [2296, 0, 2590, 0], [2297, 0, 2591, 0], [2299, 4, 2594, 2, "_proto"], [2299, 10, 2594, 8], [2299, 11, 2594, 9, "add"], [2299, 14, 2594, 12], [2299, 17, 2594, 15], [2299, 26, 2594, 24, "add"], [2299, 29, 2594, 27, "add"], [2299, 30, 2594, 28, "recognizer"], [2299, 40, 2594, 38], [2299, 42, 2594, 40], [2300, 6, 2595, 4], [2300, 10, 2595, 8, "invokeArrayArg"], [2300, 24, 2595, 22], [2300, 25, 2595, 23, "recognizer"], [2300, 35, 2595, 33], [2300, 37, 2595, 35], [2300, 42, 2595, 40], [2300, 44, 2595, 42], [2300, 48, 2595, 46], [2300, 49, 2595, 47], [2300, 51, 2595, 49], [2301, 8, 2596, 6], [2301, 15, 2596, 13], [2301, 19, 2596, 17], [2302, 6, 2597, 4], [2302, 7, 2597, 5], [2302, 8, 2597, 6], [2304, 6, 2600, 4], [2304, 10, 2600, 8, "existing"], [2304, 18, 2600, 16], [2304, 21, 2600, 19], [2304, 25, 2600, 23], [2304, 26, 2600, 24, "get"], [2304, 29, 2600, 27], [2304, 30, 2600, 28, "recognizer"], [2304, 40, 2600, 38], [2304, 41, 2600, 39, "options"], [2304, 48, 2600, 46], [2304, 49, 2600, 47, "event"], [2304, 54, 2600, 52], [2304, 55, 2600, 53], [2305, 6, 2602, 4], [2305, 10, 2602, 8, "existing"], [2305, 18, 2602, 16], [2305, 20, 2602, 18], [2306, 8, 2603, 6], [2306, 12, 2603, 10], [2306, 13, 2603, 11, "remove"], [2306, 19, 2603, 17], [2306, 20, 2603, 18, "existing"], [2306, 28, 2603, 26], [2306, 29, 2603, 27], [2307, 6, 2604, 4], [2308, 6, 2606, 4], [2308, 10, 2606, 8], [2308, 11, 2606, 9, "recognizers"], [2308, 22, 2606, 20], [2308, 23, 2606, 21, "push"], [2308, 27, 2606, 25], [2308, 28, 2606, 26, "recognizer"], [2308, 38, 2606, 36], [2308, 39, 2606, 37], [2309, 6, 2607, 4, "recognizer"], [2309, 16, 2607, 14], [2309, 17, 2607, 15, "manager"], [2309, 24, 2607, 22], [2309, 27, 2607, 25], [2309, 31, 2607, 29], [2310, 6, 2608, 4], [2310, 10, 2608, 8], [2310, 11, 2608, 9, "touchAction"], [2310, 22, 2608, 20], [2310, 23, 2608, 21, "update"], [2310, 29, 2608, 27], [2310, 30, 2608, 28], [2310, 31, 2608, 29], [2311, 6, 2609, 4], [2311, 13, 2609, 11, "recognizer"], [2311, 23, 2609, 21], [2312, 4, 2610, 2], [2312, 5, 2610, 3], [2313, 4, 2611, 2], [2314, 0, 2612, 0], [2315, 0, 2613, 0], [2316, 0, 2614, 0], [2317, 0, 2615, 0], [2318, 0, 2616, 0], [2320, 4, 2619, 2, "_proto"], [2320, 10, 2619, 8], [2320, 11, 2619, 9, "remove"], [2320, 17, 2619, 15], [2320, 20, 2619, 18], [2320, 29, 2619, 27, "remove"], [2320, 35, 2619, 33, "remove"], [2320, 36, 2619, 34, "recognizer"], [2320, 46, 2619, 44], [2320, 48, 2619, 46], [2321, 6, 2620, 4], [2321, 10, 2620, 8, "invokeArrayArg"], [2321, 24, 2620, 22], [2321, 25, 2620, 23, "recognizer"], [2321, 35, 2620, 33], [2321, 37, 2620, 35], [2321, 45, 2620, 43], [2321, 47, 2620, 45], [2321, 51, 2620, 49], [2321, 52, 2620, 50], [2321, 54, 2620, 52], [2322, 8, 2621, 6], [2322, 15, 2621, 13], [2322, 19, 2621, 17], [2323, 6, 2622, 4], [2324, 6, 2624, 4], [2324, 10, 2624, 8, "targetRecognizer"], [2324, 26, 2624, 24], [2324, 29, 2624, 27], [2324, 33, 2624, 31], [2324, 34, 2624, 32, "get"], [2324, 37, 2624, 35], [2324, 38, 2624, 36, "recognizer"], [2324, 48, 2624, 46], [2324, 49, 2624, 47], [2324, 50, 2624, 48], [2324, 51, 2624, 49], [2326, 6, 2626, 4], [2326, 10, 2626, 8, "recognizer"], [2326, 20, 2626, 18], [2326, 22, 2626, 20], [2327, 8, 2627, 6], [2327, 12, 2627, 10, "recognizers"], [2327, 23, 2627, 21], [2327, 26, 2627, 24], [2327, 30, 2627, 28], [2327, 31, 2627, 29, "recognizers"], [2327, 42, 2627, 40], [2328, 8, 2628, 6], [2328, 12, 2628, 10, "index"], [2328, 17, 2628, 15], [2328, 20, 2628, 18, "inArray"], [2328, 27, 2628, 25], [2328, 28, 2628, 26, "recognizers"], [2328, 39, 2628, 37], [2328, 41, 2628, 39, "targetRecognizer"], [2328, 57, 2628, 55], [2328, 58, 2628, 56], [2329, 8, 2630, 6], [2329, 12, 2630, 10, "index"], [2329, 17, 2630, 15], [2329, 22, 2630, 20], [2329, 23, 2630, 21], [2329, 24, 2630, 22], [2329, 26, 2630, 24], [2330, 10, 2631, 8, "recognizers"], [2330, 21, 2631, 19], [2330, 22, 2631, 20, "splice"], [2330, 28, 2631, 26], [2330, 29, 2631, 27, "index"], [2330, 34, 2631, 32], [2330, 36, 2631, 34], [2330, 37, 2631, 35], [2330, 38, 2631, 36], [2331, 10, 2632, 8], [2331, 14, 2632, 12], [2331, 15, 2632, 13, "touchAction"], [2331, 26, 2632, 24], [2331, 27, 2632, 25, "update"], [2331, 33, 2632, 31], [2331, 34, 2632, 32], [2331, 35, 2632, 33], [2332, 8, 2633, 6], [2333, 6, 2634, 4], [2334, 6, 2636, 4], [2334, 13, 2636, 11], [2334, 17, 2636, 15], [2335, 4, 2637, 2], [2335, 5, 2637, 3], [2336, 4, 2638, 2], [2337, 0, 2639, 0], [2338, 0, 2640, 0], [2339, 0, 2641, 0], [2340, 0, 2642, 0], [2341, 0, 2643, 0], [2342, 0, 2644, 0], [2344, 4, 2647, 2, "_proto"], [2344, 10, 2647, 8], [2344, 11, 2647, 9, "on"], [2344, 13, 2647, 11], [2344, 16, 2647, 14], [2344, 25, 2647, 23, "on"], [2344, 27, 2647, 25, "on"], [2344, 28, 2647, 26, "events"], [2344, 34, 2647, 32], [2344, 36, 2647, 34, "handler"], [2344, 43, 2647, 41], [2344, 45, 2647, 43], [2345, 6, 2648, 4], [2345, 10, 2648, 8, "events"], [2345, 16, 2648, 14], [2345, 21, 2648, 19, "undefined"], [2345, 30, 2648, 28], [2345, 34, 2648, 32, "handler"], [2345, 41, 2648, 39], [2345, 46, 2648, 44, "undefined"], [2345, 55, 2648, 53], [2345, 57, 2648, 55], [2346, 8, 2649, 6], [2346, 15, 2649, 13], [2346, 19, 2649, 17], [2347, 6, 2650, 4], [2348, 6, 2652, 4], [2348, 10, 2652, 8, "handlers"], [2348, 18, 2652, 16], [2348, 21, 2652, 19], [2348, 25, 2652, 23], [2348, 26, 2652, 24, "handlers"], [2348, 34, 2652, 32], [2349, 6, 2653, 4, "each"], [2349, 10, 2653, 8], [2349, 11, 2653, 9, "splitStr"], [2349, 19, 2653, 17], [2349, 20, 2653, 18, "events"], [2349, 26, 2653, 24], [2349, 27, 2653, 25], [2349, 29, 2653, 27], [2349, 39, 2653, 37, "event"], [2349, 44, 2653, 42], [2349, 46, 2653, 44], [2350, 8, 2654, 6, "handlers"], [2350, 16, 2654, 14], [2350, 17, 2654, 15, "event"], [2350, 22, 2654, 20], [2350, 23, 2654, 21], [2350, 26, 2654, 24, "handlers"], [2350, 34, 2654, 32], [2350, 35, 2654, 33, "event"], [2350, 40, 2654, 38], [2350, 41, 2654, 39], [2350, 45, 2654, 43], [2350, 47, 2654, 45], [2351, 8, 2655, 6, "handlers"], [2351, 16, 2655, 14], [2351, 17, 2655, 15, "event"], [2351, 22, 2655, 20], [2351, 23, 2655, 21], [2351, 24, 2655, 22, "push"], [2351, 28, 2655, 26], [2351, 29, 2655, 27, "handler"], [2351, 36, 2655, 34], [2351, 37, 2655, 35], [2352, 6, 2656, 4], [2352, 7, 2656, 5], [2352, 8, 2656, 6], [2353, 6, 2657, 4], [2353, 13, 2657, 11], [2353, 17, 2657, 15], [2354, 4, 2658, 2], [2354, 5, 2658, 3], [2355, 4, 2659, 2], [2356, 0, 2660, 0], [2357, 0, 2661, 0], [2358, 0, 2662, 0], [2359, 0, 2663, 0], [2360, 0, 2664, 0], [2362, 4, 2667, 2, "_proto"], [2362, 10, 2667, 8], [2362, 11, 2667, 9, "off"], [2362, 14, 2667, 12], [2362, 17, 2667, 15], [2362, 26, 2667, 24, "off"], [2362, 29, 2667, 27, "off"], [2362, 30, 2667, 28, "events"], [2362, 36, 2667, 34], [2362, 38, 2667, 36, "handler"], [2362, 45, 2667, 43], [2362, 47, 2667, 45], [2363, 6, 2668, 4], [2363, 10, 2668, 8, "events"], [2363, 16, 2668, 14], [2363, 21, 2668, 19, "undefined"], [2363, 30, 2668, 28], [2363, 32, 2668, 30], [2364, 8, 2669, 6], [2364, 15, 2669, 13], [2364, 19, 2669, 17], [2365, 6, 2670, 4], [2366, 6, 2672, 4], [2366, 10, 2672, 8, "handlers"], [2366, 18, 2672, 16], [2366, 21, 2672, 19], [2366, 25, 2672, 23], [2366, 26, 2672, 24, "handlers"], [2366, 34, 2672, 32], [2367, 6, 2673, 4, "each"], [2367, 10, 2673, 8], [2367, 11, 2673, 9, "splitStr"], [2367, 19, 2673, 17], [2367, 20, 2673, 18, "events"], [2367, 26, 2673, 24], [2367, 27, 2673, 25], [2367, 29, 2673, 27], [2367, 39, 2673, 37, "event"], [2367, 44, 2673, 42], [2367, 46, 2673, 44], [2368, 8, 2674, 6], [2368, 12, 2674, 10], [2368, 13, 2674, 11, "handler"], [2368, 20, 2674, 18], [2368, 22, 2674, 20], [2369, 10, 2675, 8], [2369, 17, 2675, 15, "handlers"], [2369, 25, 2675, 23], [2369, 26, 2675, 24, "event"], [2369, 31, 2675, 29], [2369, 32, 2675, 30], [2370, 8, 2676, 6], [2370, 9, 2676, 7], [2370, 15, 2676, 13], [2371, 10, 2677, 8, "handlers"], [2371, 18, 2677, 16], [2371, 19, 2677, 17, "event"], [2371, 24, 2677, 22], [2371, 25, 2677, 23], [2371, 29, 2677, 27, "handlers"], [2371, 37, 2677, 35], [2371, 38, 2677, 36, "event"], [2371, 43, 2677, 41], [2371, 44, 2677, 42], [2371, 45, 2677, 43, "splice"], [2371, 51, 2677, 49], [2371, 52, 2677, 50, "inArray"], [2371, 59, 2677, 57], [2371, 60, 2677, 58, "handlers"], [2371, 68, 2677, 66], [2371, 69, 2677, 67, "event"], [2371, 74, 2677, 72], [2371, 75, 2677, 73], [2371, 77, 2677, 75, "handler"], [2371, 84, 2677, 82], [2371, 85, 2677, 83], [2371, 87, 2677, 85], [2371, 88, 2677, 86], [2371, 89, 2677, 87], [2372, 8, 2678, 6], [2373, 6, 2679, 4], [2373, 7, 2679, 5], [2373, 8, 2679, 6], [2374, 6, 2680, 4], [2374, 13, 2680, 11], [2374, 17, 2680, 15], [2375, 4, 2681, 2], [2375, 5, 2681, 3], [2376, 4, 2682, 2], [2377, 0, 2683, 0], [2378, 0, 2684, 0], [2379, 0, 2685, 0], [2380, 0, 2686, 0], [2382, 4, 2689, 2, "_proto"], [2382, 10, 2689, 8], [2382, 11, 2689, 9, "emit"], [2382, 15, 2689, 13], [2382, 18, 2689, 16], [2382, 27, 2689, 25, "emit"], [2382, 31, 2689, 29, "emit"], [2382, 32, 2689, 30, "event"], [2382, 37, 2689, 35], [2382, 39, 2689, 37, "data"], [2382, 43, 2689, 41], [2382, 45, 2689, 43], [2383, 6, 2690, 4], [2384, 6, 2691, 4], [2384, 10, 2691, 8], [2384, 14, 2691, 12], [2384, 15, 2691, 13, "options"], [2384, 22, 2691, 20], [2384, 23, 2691, 21, "domEvents"], [2384, 32, 2691, 30], [2384, 34, 2691, 32], [2385, 8, 2692, 6, "triggerDomEvent"], [2385, 23, 2692, 21], [2385, 24, 2692, 22, "event"], [2385, 29, 2692, 27], [2385, 31, 2692, 29, "data"], [2385, 35, 2692, 33], [2385, 36, 2692, 34], [2386, 6, 2693, 4], [2386, 7, 2693, 5], [2386, 8, 2693, 6], [2388, 6, 2696, 4], [2388, 10, 2696, 8, "handlers"], [2388, 18, 2696, 16], [2388, 21, 2696, 19], [2388, 25, 2696, 23], [2388, 26, 2696, 24, "handlers"], [2388, 34, 2696, 32], [2388, 35, 2696, 33, "event"], [2388, 40, 2696, 38], [2388, 41, 2696, 39], [2388, 45, 2696, 43], [2388, 49, 2696, 47], [2388, 50, 2696, 48, "handlers"], [2388, 58, 2696, 56], [2388, 59, 2696, 57, "event"], [2388, 64, 2696, 62], [2388, 65, 2696, 63], [2388, 66, 2696, 64, "slice"], [2388, 71, 2696, 69], [2388, 72, 2696, 70], [2388, 73, 2696, 71], [2389, 6, 2698, 4], [2389, 10, 2698, 8], [2389, 11, 2698, 9, "handlers"], [2389, 19, 2698, 17], [2389, 23, 2698, 21], [2389, 24, 2698, 22, "handlers"], [2389, 32, 2698, 30], [2389, 33, 2698, 31, "length"], [2389, 39, 2698, 37], [2389, 41, 2698, 39], [2390, 8, 2699, 6], [2391, 6, 2700, 4], [2392, 6, 2702, 4, "data"], [2392, 10, 2702, 8], [2392, 11, 2702, 9, "type"], [2392, 15, 2702, 13], [2392, 18, 2702, 16, "event"], [2392, 23, 2702, 21], [2393, 6, 2704, 4, "data"], [2393, 10, 2704, 8], [2393, 11, 2704, 9, "preventDefault"], [2393, 25, 2704, 23], [2393, 28, 2704, 26], [2393, 40, 2704, 38], [2394, 8, 2705, 6, "data"], [2394, 12, 2705, 10], [2394, 13, 2705, 11, "srcEvent"], [2394, 21, 2705, 19], [2394, 22, 2705, 20, "preventDefault"], [2394, 36, 2705, 34], [2394, 37, 2705, 35], [2394, 38, 2705, 36], [2395, 6, 2706, 4], [2395, 7, 2706, 5], [2396, 6, 2708, 4], [2396, 10, 2708, 8, "i"], [2396, 11, 2708, 9], [2396, 14, 2708, 12], [2396, 15, 2708, 13], [2397, 6, 2710, 4], [2397, 13, 2710, 11, "i"], [2397, 14, 2710, 12], [2397, 17, 2710, 15, "handlers"], [2397, 25, 2710, 23], [2397, 26, 2710, 24, "length"], [2397, 32, 2710, 30], [2397, 34, 2710, 32], [2398, 8, 2711, 6, "handlers"], [2398, 16, 2711, 14], [2398, 17, 2711, 15, "i"], [2398, 18, 2711, 16], [2398, 19, 2711, 17], [2398, 20, 2711, 18, "data"], [2398, 24, 2711, 22], [2398, 25, 2711, 23], [2399, 8, 2712, 6, "i"], [2399, 9, 2712, 7], [2399, 11, 2712, 9], [2400, 6, 2713, 4], [2401, 4, 2714, 2], [2401, 5, 2714, 3], [2402, 4, 2715, 2], [2403, 0, 2716, 0], [2404, 0, 2717, 0], [2405, 0, 2718, 0], [2406, 0, 2719, 0], [2408, 4, 2722, 2, "_proto"], [2408, 10, 2722, 8], [2408, 11, 2722, 9, "destroy"], [2408, 18, 2722, 16], [2408, 21, 2722, 19], [2408, 30, 2722, 28, "destroy"], [2408, 37, 2722, 35, "destroy"], [2408, 38, 2722, 35], [2408, 40, 2722, 38], [2409, 6, 2723, 4], [2409, 10, 2723, 8], [2409, 11, 2723, 9, "element"], [2409, 18, 2723, 16], [2409, 22, 2723, 20, "toggleCssProps"], [2409, 36, 2723, 34], [2409, 37, 2723, 35], [2409, 41, 2723, 39], [2409, 43, 2723, 41], [2409, 48, 2723, 46], [2409, 49, 2723, 47], [2410, 6, 2724, 4], [2410, 10, 2724, 8], [2410, 11, 2724, 9, "handlers"], [2410, 19, 2724, 17], [2410, 22, 2724, 20], [2410, 23, 2724, 21], [2410, 24, 2724, 22], [2411, 6, 2725, 4], [2411, 10, 2725, 8], [2411, 11, 2725, 9, "session"], [2411, 18, 2725, 16], [2411, 21, 2725, 19], [2411, 22, 2725, 20], [2411, 23, 2725, 21], [2412, 6, 2726, 4], [2412, 10, 2726, 8], [2412, 11, 2726, 9, "input"], [2412, 16, 2726, 14], [2412, 17, 2726, 15, "destroy"], [2412, 24, 2726, 22], [2412, 25, 2726, 23], [2412, 26, 2726, 24], [2413, 6, 2727, 4], [2413, 10, 2727, 8], [2413, 11, 2727, 9, "element"], [2413, 18, 2727, 16], [2413, 21, 2727, 19], [2413, 25, 2727, 23], [2414, 4, 2728, 2], [2414, 5, 2728, 3], [2415, 4, 2730, 2], [2415, 11, 2730, 9, "Manager"], [2415, 18, 2730, 16], [2416, 2, 2731, 0], [2416, 3, 2731, 1], [2416, 4, 2731, 2], [2416, 5, 2731, 3], [2417, 2, 2733, 0], [2417, 6, 2733, 4, "SINGLE_TOUCH_INPUT_MAP"], [2417, 28, 2733, 26], [2417, 31, 2733, 29], [2418, 4, 2734, 2, "touchstart"], [2418, 14, 2734, 12], [2418, 16, 2734, 14, "INPUT_START"], [2418, 27, 2734, 25], [2419, 4, 2735, 2, "touchmove"], [2419, 13, 2735, 11], [2419, 15, 2735, 13, "INPUT_MOVE"], [2419, 25, 2735, 23], [2420, 4, 2736, 2, "touchend"], [2420, 12, 2736, 10], [2420, 14, 2736, 12, "INPUT_END"], [2420, 23, 2736, 21], [2421, 4, 2737, 2, "touchcancel"], [2421, 15, 2737, 13], [2421, 17, 2737, 15, "INPUT_CANCEL"], [2422, 2, 2738, 0], [2422, 3, 2738, 1], [2423, 2, 2739, 0], [2423, 6, 2739, 4, "SINGLE_TOUCH_TARGET_EVENTS"], [2423, 32, 2739, 30], [2423, 35, 2739, 33], [2423, 47, 2739, 45], [2424, 2, 2740, 0], [2424, 6, 2740, 4, "SINGLE_TOUCH_WINDOW_EVENTS"], [2424, 32, 2740, 30], [2424, 35, 2740, 33], [2424, 78, 2740, 76], [2425, 2, 2741, 0], [2426, 0, 2742, 0], [2427, 0, 2743, 0], [2428, 0, 2744, 0], [2429, 0, 2745, 0], [2430, 0, 2746, 0], [2432, 2, 2748, 0], [2432, 6, 2748, 4, "SingleTouchInput"], [2432, 22, 2748, 20], [2432, 25, 2748, 20, "exports"], [2432, 32, 2748, 20], [2432, 33, 2748, 20, "SingleTouchInput"], [2432, 49, 2748, 20], [2432, 52, 2749, 0], [2433, 2, 2750, 0], [2433, 12, 2750, 10, "_Input"], [2433, 18, 2750, 16], [2433, 20, 2750, 18], [2434, 4, 2751, 2, "_inherits<PERSON><PERSON>e"], [2434, 18, 2751, 16], [2434, 19, 2751, 17, "SingleTouchInput"], [2434, 35, 2751, 33], [2434, 37, 2751, 35, "_Input"], [2434, 43, 2751, 41], [2434, 44, 2751, 42], [2435, 4, 2753, 2], [2435, 13, 2753, 11, "SingleTouchInput"], [2435, 29, 2753, 27, "SingleTouchInput"], [2435, 30, 2753, 27], [2435, 32, 2753, 30], [2436, 6, 2754, 4], [2436, 10, 2754, 8, "_this"], [2436, 15, 2754, 13], [2437, 6, 2756, 4], [2437, 10, 2756, 8, "proto"], [2437, 15, 2756, 13], [2437, 18, 2756, 16, "SingleTouchInput"], [2437, 34, 2756, 32], [2437, 35, 2756, 33, "prototype"], [2437, 44, 2756, 42], [2438, 6, 2757, 4, "proto"], [2438, 11, 2757, 9], [2438, 12, 2757, 10, "ev<PERSON><PERSON><PERSON>"], [2438, 20, 2757, 18], [2438, 23, 2757, 21, "SINGLE_TOUCH_TARGET_EVENTS"], [2438, 49, 2757, 47], [2439, 6, 2758, 4, "proto"], [2439, 11, 2758, 9], [2439, 12, 2758, 10, "evWin"], [2439, 17, 2758, 15], [2439, 20, 2758, 18, "SINGLE_TOUCH_WINDOW_EVENTS"], [2439, 46, 2758, 44], [2440, 6, 2759, 4, "_this"], [2440, 11, 2759, 9], [2440, 14, 2759, 12, "_Input"], [2440, 20, 2759, 18], [2440, 21, 2759, 19, "apply"], [2440, 26, 2759, 24], [2440, 27, 2759, 25], [2440, 31, 2759, 29], [2440, 33, 2759, 31, "arguments"], [2440, 42, 2759, 40], [2440, 43, 2759, 41], [2440, 47, 2759, 45], [2440, 51, 2759, 49], [2441, 6, 2760, 4, "_this"], [2441, 11, 2760, 9], [2441, 12, 2760, 10, "started"], [2441, 19, 2760, 17], [2441, 22, 2760, 20], [2441, 27, 2760, 25], [2442, 6, 2761, 4], [2442, 13, 2761, 11, "_this"], [2442, 18, 2761, 16], [2443, 4, 2762, 2], [2444, 4, 2764, 2], [2444, 8, 2764, 6, "_proto"], [2444, 14, 2764, 12], [2444, 17, 2764, 15, "SingleTouchInput"], [2444, 33, 2764, 31], [2444, 34, 2764, 32, "prototype"], [2444, 43, 2764, 41], [2445, 4, 2766, 2, "_proto"], [2445, 10, 2766, 8], [2445, 11, 2766, 9, "handler"], [2445, 18, 2766, 16], [2445, 21, 2766, 19], [2445, 30, 2766, 28, "handler"], [2445, 37, 2766, 35, "handler"], [2445, 38, 2766, 36, "ev"], [2445, 40, 2766, 38], [2445, 42, 2766, 40], [2446, 6, 2767, 4], [2446, 10, 2767, 8, "type"], [2446, 14, 2767, 12], [2446, 17, 2767, 15, "SINGLE_TOUCH_INPUT_MAP"], [2446, 39, 2767, 37], [2446, 40, 2767, 38, "ev"], [2446, 42, 2767, 40], [2446, 43, 2767, 41, "type"], [2446, 47, 2767, 45], [2446, 48, 2767, 46], [2446, 49, 2767, 47], [2446, 50, 2767, 48], [2448, 6, 2769, 4], [2448, 10, 2769, 8, "type"], [2448, 14, 2769, 12], [2448, 19, 2769, 17, "INPUT_START"], [2448, 30, 2769, 28], [2448, 32, 2769, 30], [2449, 8, 2770, 6], [2449, 12, 2770, 10], [2449, 13, 2770, 11, "started"], [2449, 20, 2770, 18], [2449, 23, 2770, 21], [2449, 27, 2770, 25], [2450, 6, 2771, 4], [2451, 6, 2773, 4], [2451, 10, 2773, 8], [2451, 11, 2773, 9], [2451, 15, 2773, 13], [2451, 16, 2773, 14, "started"], [2451, 23, 2773, 21], [2451, 25, 2773, 23], [2452, 8, 2774, 6], [2453, 6, 2775, 4], [2454, 6, 2777, 4], [2454, 10, 2777, 8, "touches"], [2454, 17, 2777, 15], [2454, 20, 2777, 18, "normalizeSingleTouches"], [2454, 42, 2777, 40], [2454, 43, 2777, 41, "call"], [2454, 47, 2777, 45], [2454, 48, 2777, 46], [2454, 52, 2777, 50], [2454, 54, 2777, 52, "ev"], [2454, 56, 2777, 54], [2454, 58, 2777, 56, "type"], [2454, 62, 2777, 60], [2454, 63, 2777, 61], [2454, 64, 2777, 62], [2454, 65, 2777, 63], [2456, 6, 2779, 4], [2456, 10, 2779, 8, "type"], [2456, 14, 2779, 12], [2456, 18, 2779, 16, "INPUT_END"], [2456, 27, 2779, 25], [2456, 30, 2779, 28, "INPUT_CANCEL"], [2456, 42, 2779, 40], [2456, 43, 2779, 41], [2456, 47, 2779, 45, "touches"], [2456, 54, 2779, 52], [2456, 55, 2779, 53], [2456, 56, 2779, 54], [2456, 57, 2779, 55], [2456, 58, 2779, 56, "length"], [2456, 64, 2779, 62], [2456, 67, 2779, 65, "touches"], [2456, 74, 2779, 72], [2456, 75, 2779, 73], [2456, 76, 2779, 74], [2456, 77, 2779, 75], [2456, 78, 2779, 76, "length"], [2456, 84, 2779, 82], [2456, 89, 2779, 87], [2456, 90, 2779, 88], [2456, 92, 2779, 90], [2457, 8, 2780, 6], [2457, 12, 2780, 10], [2457, 13, 2780, 11, "started"], [2457, 20, 2780, 18], [2457, 23, 2780, 21], [2457, 28, 2780, 26], [2458, 6, 2781, 4], [2459, 6, 2783, 4], [2459, 10, 2783, 8], [2459, 11, 2783, 9, "callback"], [2459, 19, 2783, 17], [2459, 20, 2783, 18], [2459, 24, 2783, 22], [2459, 25, 2783, 23, "manager"], [2459, 32, 2783, 30], [2459, 34, 2783, 32, "type"], [2459, 38, 2783, 36], [2459, 40, 2783, 38], [2460, 8, 2784, 6, "pointers"], [2460, 16, 2784, 14], [2460, 18, 2784, 16, "touches"], [2460, 25, 2784, 23], [2460, 26, 2784, 24], [2460, 27, 2784, 25], [2460, 28, 2784, 26], [2461, 8, 2785, 6, "changedPointers"], [2461, 23, 2785, 21], [2461, 25, 2785, 23, "touches"], [2461, 32, 2785, 30], [2461, 33, 2785, 31], [2461, 34, 2785, 32], [2461, 35, 2785, 33], [2462, 8, 2786, 6, "pointerType"], [2462, 19, 2786, 17], [2462, 21, 2786, 19, "INPUT_TYPE_TOUCH"], [2462, 37, 2786, 35], [2463, 8, 2787, 6, "srcEvent"], [2463, 16, 2787, 14], [2463, 18, 2787, 16, "ev"], [2464, 6, 2788, 4], [2464, 7, 2788, 5], [2464, 8, 2788, 6], [2465, 4, 2789, 2], [2465, 5, 2789, 3], [2466, 4, 2791, 2], [2466, 11, 2791, 9, "SingleTouchInput"], [2466, 27, 2791, 25], [2467, 2, 2792, 0], [2467, 3, 2792, 1], [2467, 4, 2792, 2, "Input"], [2467, 9, 2792, 7], [2467, 10, 2792, 8], [2468, 2, 2794, 0], [2468, 11, 2794, 9, "normalizeSingleTouches"], [2468, 33, 2794, 31, "normalizeSingleTouches"], [2468, 34, 2794, 32, "ev"], [2468, 36, 2794, 34], [2468, 38, 2794, 36, "type"], [2468, 42, 2794, 40], [2468, 44, 2794, 42], [2469, 4, 2795, 2], [2469, 8, 2795, 6, "all"], [2469, 11, 2795, 9], [2469, 14, 2795, 12, "toArray"], [2469, 21, 2795, 19], [2469, 22, 2795, 20, "ev"], [2469, 24, 2795, 22], [2469, 25, 2795, 23, "touches"], [2469, 32, 2795, 30], [2469, 33, 2795, 31], [2470, 4, 2796, 2], [2470, 8, 2796, 6, "changed"], [2470, 15, 2796, 13], [2470, 18, 2796, 16, "toArray"], [2470, 25, 2796, 23], [2470, 26, 2796, 24, "ev"], [2470, 28, 2796, 26], [2470, 29, 2796, 27, "changedTouches"], [2470, 43, 2796, 41], [2470, 44, 2796, 42], [2471, 4, 2798, 2], [2471, 8, 2798, 6, "type"], [2471, 12, 2798, 10], [2471, 16, 2798, 14, "INPUT_END"], [2471, 25, 2798, 23], [2471, 28, 2798, 26, "INPUT_CANCEL"], [2471, 40, 2798, 38], [2471, 41, 2798, 39], [2471, 43, 2798, 41], [2472, 6, 2799, 4, "all"], [2472, 9, 2799, 7], [2472, 12, 2799, 10, "uniqueArray"], [2472, 23, 2799, 21], [2472, 24, 2799, 22, "all"], [2472, 27, 2799, 25], [2472, 28, 2799, 26, "concat"], [2472, 34, 2799, 32], [2472, 35, 2799, 33, "changed"], [2472, 42, 2799, 40], [2472, 43, 2799, 41], [2472, 45, 2799, 43], [2472, 57, 2799, 55], [2472, 59, 2799, 57], [2472, 63, 2799, 61], [2472, 64, 2799, 62], [2473, 4, 2800, 2], [2474, 4, 2802, 2], [2474, 11, 2802, 9], [2474, 12, 2802, 10, "all"], [2474, 15, 2802, 13], [2474, 17, 2802, 15, "changed"], [2474, 24, 2802, 22], [2474, 25, 2802, 23], [2475, 2, 2803, 0], [2477, 2, 2805, 0], [2478, 0, 2806, 0], [2479, 0, 2807, 0], [2480, 0, 2808, 0], [2481, 0, 2809, 0], [2482, 0, 2810, 0], [2483, 0, 2811, 0], [2484, 0, 2812, 0], [2485, 2, 2813, 0], [2485, 11, 2813, 9, "deprecate"], [2485, 20, 2813, 18, "deprecate"], [2485, 21, 2813, 19, "method"], [2485, 27, 2813, 25], [2485, 29, 2813, 27, "name"], [2485, 33, 2813, 31], [2485, 35, 2813, 33, "message"], [2485, 42, 2813, 40], [2485, 44, 2813, 42], [2486, 4, 2814, 2], [2486, 8, 2814, 6, "deprecationMessage"], [2486, 26, 2814, 24], [2486, 29, 2814, 27], [2486, 50, 2814, 48], [2486, 53, 2814, 51, "name"], [2486, 57, 2814, 55], [2486, 60, 2814, 58], [2486, 64, 2814, 62], [2486, 67, 2814, 65, "message"], [2486, 74, 2814, 72], [2486, 77, 2814, 75], [2486, 85, 2814, 83], [2487, 4, 2815, 2], [2487, 11, 2815, 9], [2487, 23, 2815, 21], [2488, 6, 2816, 4], [2488, 10, 2816, 8, "e"], [2488, 11, 2816, 9], [2488, 14, 2816, 12], [2488, 18, 2816, 16, "Error"], [2488, 23, 2816, 21], [2488, 24, 2816, 22], [2488, 41, 2816, 39], [2488, 42, 2816, 40], [2489, 6, 2817, 4], [2489, 10, 2817, 8, "stack"], [2489, 15, 2817, 13], [2489, 18, 2817, 16, "e"], [2489, 19, 2817, 17], [2489, 23, 2817, 21, "e"], [2489, 24, 2817, 22], [2489, 25, 2817, 23, "stack"], [2489, 30, 2817, 28], [2489, 33, 2817, 31, "e"], [2489, 34, 2817, 32], [2489, 35, 2817, 33, "stack"], [2489, 40, 2817, 38], [2489, 41, 2817, 39, "replace"], [2489, 48, 2817, 46], [2489, 49, 2817, 47], [2489, 66, 2817, 64], [2489, 68, 2817, 66], [2489, 70, 2817, 68], [2489, 71, 2817, 69], [2489, 72, 2817, 70, "replace"], [2489, 79, 2817, 77], [2489, 80, 2817, 78], [2489, 93, 2817, 91], [2489, 95, 2817, 93], [2489, 97, 2817, 95], [2489, 98, 2817, 96], [2489, 99, 2817, 97, "replace"], [2489, 106, 2817, 104], [2489, 107, 2817, 105], [2489, 135, 2817, 133], [2489, 137, 2817, 135], [2489, 153, 2817, 151], [2489, 154, 2817, 152], [2489, 157, 2817, 155], [2489, 178, 2817, 176], [2490, 6, 2818, 4], [2490, 10, 2818, 8, "log"], [2490, 13, 2818, 11], [2490, 16, 2818, 14, "window"], [2490, 22, 2818, 20], [2490, 23, 2818, 21, "console"], [2490, 30, 2818, 28], [2490, 35, 2818, 33, "window"], [2490, 41, 2818, 39], [2490, 42, 2818, 40, "console"], [2490, 49, 2818, 47], [2490, 50, 2818, 48, "warn"], [2490, 54, 2818, 52], [2490, 58, 2818, 56, "window"], [2490, 64, 2818, 62], [2490, 65, 2818, 63, "console"], [2490, 72, 2818, 70], [2490, 73, 2818, 71, "log"], [2490, 76, 2818, 74], [2490, 77, 2818, 75], [2491, 6, 2820, 4], [2491, 10, 2820, 8, "log"], [2491, 13, 2820, 11], [2491, 15, 2820, 13], [2492, 8, 2821, 6, "log"], [2492, 11, 2821, 9], [2492, 12, 2821, 10, "call"], [2492, 16, 2821, 14], [2492, 17, 2821, 15, "window"], [2492, 23, 2821, 21], [2492, 24, 2821, 22, "console"], [2492, 31, 2821, 29], [2492, 33, 2821, 31, "deprecationMessage"], [2492, 51, 2821, 49], [2492, 53, 2821, 51, "stack"], [2492, 58, 2821, 56], [2492, 59, 2821, 57], [2493, 6, 2822, 4], [2494, 6, 2824, 4], [2494, 13, 2824, 11, "method"], [2494, 19, 2824, 17], [2494, 20, 2824, 18, "apply"], [2494, 25, 2824, 23], [2494, 26, 2824, 24], [2494, 30, 2824, 28], [2494, 32, 2824, 30, "arguments"], [2494, 41, 2824, 39], [2494, 42, 2824, 40], [2495, 4, 2825, 2], [2495, 5, 2825, 3], [2496, 2, 2826, 0], [2498, 2, 2828, 0], [2499, 0, 2829, 0], [2500, 0, 2830, 0], [2501, 0, 2831, 0], [2502, 0, 2832, 0], [2503, 0, 2833, 0], [2504, 0, 2834, 0], [2505, 0, 2835, 0], [2506, 0, 2836, 0], [2508, 2, 2838, 0], [2508, 6, 2838, 4, "extend"], [2508, 12, 2838, 10], [2508, 15, 2838, 10, "exports"], [2508, 22, 2838, 10], [2508, 23, 2838, 10, "extend"], [2508, 29, 2838, 10], [2508, 32, 2838, 13, "deprecate"], [2508, 41, 2838, 22], [2508, 42, 2838, 23], [2508, 52, 2838, 33, "dest"], [2508, 56, 2838, 37], [2508, 58, 2838, 39, "src"], [2508, 61, 2838, 42], [2508, 63, 2838, 44, "merge"], [2508, 68, 2838, 49], [2508, 70, 2838, 51], [2509, 4, 2839, 2], [2509, 8, 2839, 6, "keys"], [2509, 12, 2839, 10], [2509, 15, 2839, 13, "Object"], [2509, 21, 2839, 19], [2509, 22, 2839, 20, "keys"], [2509, 26, 2839, 24], [2509, 27, 2839, 25, "src"], [2509, 30, 2839, 28], [2509, 31, 2839, 29], [2510, 4, 2840, 2], [2510, 8, 2840, 6, "i"], [2510, 9, 2840, 7], [2510, 12, 2840, 10], [2510, 13, 2840, 11], [2511, 4, 2842, 2], [2511, 11, 2842, 9, "i"], [2511, 12, 2842, 10], [2511, 15, 2842, 13, "keys"], [2511, 19, 2842, 17], [2511, 20, 2842, 18, "length"], [2511, 26, 2842, 24], [2511, 28, 2842, 26], [2512, 6, 2843, 4], [2512, 10, 2843, 8], [2512, 11, 2843, 9, "merge"], [2512, 16, 2843, 14], [2512, 20, 2843, 18, "merge"], [2512, 25, 2843, 23], [2512, 29, 2843, 27, "dest"], [2512, 33, 2843, 31], [2512, 34, 2843, 32, "keys"], [2512, 38, 2843, 36], [2512, 39, 2843, 37, "i"], [2512, 40, 2843, 38], [2512, 41, 2843, 39], [2512, 42, 2843, 40], [2512, 47, 2843, 45, "undefined"], [2512, 56, 2843, 54], [2512, 58, 2843, 56], [2513, 8, 2844, 6, "dest"], [2513, 12, 2844, 10], [2513, 13, 2844, 11, "keys"], [2513, 17, 2844, 15], [2513, 18, 2844, 16, "i"], [2513, 19, 2844, 17], [2513, 20, 2844, 18], [2513, 21, 2844, 19], [2513, 24, 2844, 22, "src"], [2513, 27, 2844, 25], [2513, 28, 2844, 26, "keys"], [2513, 32, 2844, 30], [2513, 33, 2844, 31, "i"], [2513, 34, 2844, 32], [2513, 35, 2844, 33], [2513, 36, 2844, 34], [2514, 6, 2845, 4], [2515, 6, 2847, 4, "i"], [2515, 7, 2847, 5], [2515, 9, 2847, 7], [2516, 4, 2848, 2], [2517, 4, 2850, 2], [2517, 11, 2850, 9, "dest"], [2517, 15, 2850, 13], [2518, 2, 2851, 0], [2518, 3, 2851, 1], [2518, 5, 2851, 3], [2518, 13, 2851, 11], [2518, 15, 2851, 13], [2518, 30, 2851, 28], [2518, 31, 2851, 29], [2520, 2, 2853, 0], [2521, 0, 2854, 0], [2522, 0, 2855, 0], [2523, 0, 2856, 0], [2524, 0, 2857, 0], [2525, 0, 2858, 0], [2526, 0, 2859, 0], [2527, 0, 2860, 0], [2529, 2, 2862, 0], [2529, 6, 2862, 4, "merge"], [2529, 11, 2862, 9], [2529, 14, 2862, 9, "exports"], [2529, 21, 2862, 9], [2529, 22, 2862, 9, "merge"], [2529, 27, 2862, 9], [2529, 30, 2862, 12, "deprecate"], [2529, 39, 2862, 21], [2529, 40, 2862, 22], [2529, 50, 2862, 32, "dest"], [2529, 54, 2862, 36], [2529, 56, 2862, 38, "src"], [2529, 59, 2862, 41], [2529, 61, 2862, 43], [2530, 4, 2863, 2], [2530, 11, 2863, 9, "extend"], [2530, 17, 2863, 15], [2530, 18, 2863, 16, "dest"], [2530, 22, 2863, 20], [2530, 24, 2863, 22, "src"], [2530, 27, 2863, 25], [2530, 29, 2863, 27], [2530, 33, 2863, 31], [2530, 34, 2863, 32], [2531, 2, 2864, 0], [2531, 3, 2864, 1], [2531, 5, 2864, 3], [2531, 12, 2864, 10], [2531, 14, 2864, 12], [2531, 29, 2864, 27], [2531, 30, 2864, 28], [2533, 2, 2866, 0], [2534, 0, 2867, 0], [2535, 0, 2868, 0], [2536, 0, 2869, 0], [2537, 0, 2870, 0], [2538, 0, 2871, 0], [2539, 0, 2872, 0], [2541, 2, 2874, 0], [2541, 11, 2874, 9, "inherit"], [2541, 18, 2874, 16, "inherit"], [2541, 19, 2874, 17, "child"], [2541, 24, 2874, 22], [2541, 26, 2874, 24, "base"], [2541, 30, 2874, 28], [2541, 32, 2874, 30, "properties"], [2541, 42, 2874, 40], [2541, 44, 2874, 42], [2542, 4, 2875, 2], [2542, 8, 2875, 6, "baseP"], [2542, 13, 2875, 11], [2542, 16, 2875, 14, "base"], [2542, 20, 2875, 18], [2542, 21, 2875, 19, "prototype"], [2542, 30, 2875, 28], [2543, 4, 2876, 2], [2543, 8, 2876, 6, "childP"], [2543, 14, 2876, 12], [2544, 4, 2877, 2, "childP"], [2544, 10, 2877, 8], [2544, 13, 2877, 11, "child"], [2544, 18, 2877, 16], [2544, 19, 2877, 17, "prototype"], [2544, 28, 2877, 26], [2544, 31, 2877, 29, "Object"], [2544, 37, 2877, 35], [2544, 38, 2877, 36, "create"], [2544, 44, 2877, 42], [2544, 45, 2877, 43, "baseP"], [2544, 50, 2877, 48], [2544, 51, 2877, 49], [2545, 4, 2878, 2, "childP"], [2545, 10, 2878, 8], [2545, 11, 2878, 9, "constructor"], [2545, 22, 2878, 20], [2545, 25, 2878, 23, "child"], [2545, 30, 2878, 28], [2546, 4, 2879, 2, "childP"], [2546, 10, 2879, 8], [2546, 11, 2879, 9, "_super"], [2546, 17, 2879, 15], [2546, 20, 2879, 18, "baseP"], [2546, 25, 2879, 23], [2547, 4, 2881, 2], [2547, 8, 2881, 6, "properties"], [2547, 18, 2881, 16], [2547, 20, 2881, 18], [2548, 6, 2882, 4, "assign$1"], [2548, 14, 2882, 12], [2548, 15, 2882, 13, "childP"], [2548, 21, 2882, 19], [2548, 23, 2882, 21, "properties"], [2548, 33, 2882, 31], [2548, 34, 2882, 32], [2549, 4, 2883, 2], [2550, 2, 2884, 0], [2552, 2, 2886, 0], [2553, 0, 2887, 0], [2554, 0, 2888, 0], [2555, 0, 2889, 0], [2556, 0, 2890, 0], [2557, 0, 2891, 0], [2558, 0, 2892, 0], [2559, 2, 2893, 0], [2559, 11, 2893, 9, "bindFn"], [2559, 17, 2893, 15, "bindFn"], [2559, 18, 2893, 16, "fn"], [2559, 20, 2893, 18], [2559, 22, 2893, 20, "context"], [2559, 29, 2893, 27], [2559, 31, 2893, 29], [2560, 4, 2894, 2], [2560, 11, 2894, 9], [2560, 20, 2894, 18, "boundFn"], [2560, 27, 2894, 25, "boundFn"], [2560, 28, 2894, 25], [2560, 30, 2894, 28], [2561, 6, 2895, 4], [2561, 13, 2895, 11, "fn"], [2561, 15, 2895, 13], [2561, 16, 2895, 14, "apply"], [2561, 21, 2895, 19], [2561, 22, 2895, 20, "context"], [2561, 29, 2895, 27], [2561, 31, 2895, 29, "arguments"], [2561, 40, 2895, 38], [2561, 41, 2895, 39], [2562, 4, 2896, 2], [2562, 5, 2896, 3], [2563, 2, 2897, 0], [2565, 2, 2899, 0], [2566, 0, 2900, 0], [2567, 0, 2901, 0], [2568, 0, 2902, 0], [2569, 0, 2903, 0], [2570, 0, 2904, 0], [2571, 0, 2905, 0], [2573, 2, 2907, 0], [2573, 6, 2907, 4, "Hammer"], [2573, 12, 2907, 10], [2573, 15, 2908, 0], [2574, 2, 2909, 0], [2574, 14, 2909, 12], [2575, 4, 2910, 2], [2575, 8, 2910, 6, "Hammer"], [2575, 14, 2910, 12], [2576, 4, 2911, 2], [2577, 0, 2912, 0], [2578, 0, 2913, 0], [2579, 0, 2914, 0], [2580, 4, 2915, 2], [2580, 13, 2915, 11, "Hammer"], [2580, 19, 2915, 17, "Hammer"], [2580, 20, 2915, 18, "element"], [2580, 27, 2915, 25], [2580, 29, 2915, 27, "options"], [2580, 36, 2915, 34], [2580, 38, 2915, 36], [2581, 6, 2916, 4], [2581, 10, 2916, 8, "options"], [2581, 17, 2916, 15], [2581, 22, 2916, 20], [2581, 27, 2916, 25], [2581, 28, 2916, 26], [2581, 30, 2916, 28], [2582, 8, 2917, 6, "options"], [2582, 15, 2917, 13], [2582, 18, 2917, 16], [2582, 19, 2917, 17], [2582, 20, 2917, 18], [2583, 6, 2918, 4], [2584, 6, 2920, 4], [2584, 13, 2920, 11], [2584, 17, 2920, 15, "Manager"], [2584, 24, 2920, 22], [2584, 25, 2920, 23, "element"], [2584, 32, 2920, 30], [2584, 34, 2920, 32, "_extends"], [2584, 42, 2920, 40], [2584, 43, 2920, 41], [2585, 8, 2921, 6, "recognizers"], [2585, 19, 2921, 17], [2585, 21, 2921, 19, "preset"], [2585, 27, 2921, 25], [2585, 28, 2921, 26, "concat"], [2585, 34, 2921, 32], [2585, 35, 2921, 33], [2586, 6, 2922, 4], [2586, 7, 2922, 5], [2586, 9, 2922, 7, "options"], [2586, 16, 2922, 14], [2586, 17, 2922, 15], [2586, 18, 2922, 16], [2587, 4, 2923, 2], [2587, 5, 2923, 3], [2588, 4, 2925, 2, "Hammer"], [2588, 10, 2925, 8], [2588, 11, 2925, 9, "VERSION"], [2588, 18, 2925, 16], [2588, 21, 2925, 19], [2588, 32, 2925, 30], [2589, 4, 2926, 2, "Hammer"], [2589, 10, 2926, 8], [2589, 11, 2926, 9, "DIRECTION_ALL"], [2589, 24, 2926, 22], [2589, 27, 2926, 25, "DIRECTION_ALL"], [2589, 40, 2926, 38], [2590, 4, 2927, 2, "Hammer"], [2590, 10, 2927, 8], [2590, 11, 2927, 9, "DIRECTION_DOWN"], [2590, 25, 2927, 23], [2590, 28, 2927, 26, "DIRECTION_DOWN"], [2590, 42, 2927, 40], [2591, 4, 2928, 2, "Hammer"], [2591, 10, 2928, 8], [2591, 11, 2928, 9, "DIRECTION_LEFT"], [2591, 25, 2928, 23], [2591, 28, 2928, 26, "DIRECTION_LEFT"], [2591, 42, 2928, 40], [2592, 4, 2929, 2, "Hammer"], [2592, 10, 2929, 8], [2592, 11, 2929, 9, "DIRECTION_RIGHT"], [2592, 26, 2929, 24], [2592, 29, 2929, 27, "DIRECTION_RIGHT"], [2592, 44, 2929, 42], [2593, 4, 2930, 2, "Hammer"], [2593, 10, 2930, 8], [2593, 11, 2930, 9, "DIRECTION_UP"], [2593, 23, 2930, 21], [2593, 26, 2930, 24, "DIRECTION_UP"], [2593, 38, 2930, 36], [2594, 4, 2931, 2, "Hammer"], [2594, 10, 2931, 8], [2594, 11, 2931, 9, "DIRECTION_HORIZONTAL"], [2594, 31, 2931, 29], [2594, 34, 2931, 32, "DIRECTION_HORIZONTAL"], [2594, 54, 2931, 52], [2595, 4, 2932, 2, "Hammer"], [2595, 10, 2932, 8], [2595, 11, 2932, 9, "DIRECTION_VERTICAL"], [2595, 29, 2932, 27], [2595, 32, 2932, 30, "DIRECTION_VERTICAL"], [2595, 50, 2932, 48], [2596, 4, 2933, 2, "Hammer"], [2596, 10, 2933, 8], [2596, 11, 2933, 9, "DIRECTION_NONE"], [2596, 25, 2933, 23], [2596, 28, 2933, 26, "DIRECTION_NONE"], [2596, 42, 2933, 40], [2597, 4, 2934, 2, "Hammer"], [2597, 10, 2934, 8], [2597, 11, 2934, 9, "DIRECTION_DOWN"], [2597, 25, 2934, 23], [2597, 28, 2934, 26, "DIRECTION_DOWN"], [2597, 42, 2934, 40], [2598, 4, 2935, 2, "Hammer"], [2598, 10, 2935, 8], [2598, 11, 2935, 9, "INPUT_START"], [2598, 22, 2935, 20], [2598, 25, 2935, 23, "INPUT_START"], [2598, 36, 2935, 34], [2599, 4, 2936, 2, "Hammer"], [2599, 10, 2936, 8], [2599, 11, 2936, 9, "INPUT_MOVE"], [2599, 21, 2936, 19], [2599, 24, 2936, 22, "INPUT_MOVE"], [2599, 34, 2936, 32], [2600, 4, 2937, 2, "Hammer"], [2600, 10, 2937, 8], [2600, 11, 2937, 9, "INPUT_END"], [2600, 20, 2937, 18], [2600, 23, 2937, 21, "INPUT_END"], [2600, 32, 2937, 30], [2601, 4, 2938, 2, "Hammer"], [2601, 10, 2938, 8], [2601, 11, 2938, 9, "INPUT_CANCEL"], [2601, 23, 2938, 21], [2601, 26, 2938, 24, "INPUT_CANCEL"], [2601, 38, 2938, 36], [2602, 4, 2939, 2, "Hammer"], [2602, 10, 2939, 8], [2602, 11, 2939, 9, "STATE_POSSIBLE"], [2602, 25, 2939, 23], [2602, 28, 2939, 26, "STATE_POSSIBLE"], [2602, 42, 2939, 40], [2603, 4, 2940, 2, "Hammer"], [2603, 10, 2940, 8], [2603, 11, 2940, 9, "STATE_BEGAN"], [2603, 22, 2940, 20], [2603, 25, 2940, 23, "STATE_BEGAN"], [2603, 36, 2940, 34], [2604, 4, 2941, 2, "Hammer"], [2604, 10, 2941, 8], [2604, 11, 2941, 9, "STATE_CHANGED"], [2604, 24, 2941, 22], [2604, 27, 2941, 25, "STATE_CHANGED"], [2604, 40, 2941, 38], [2605, 4, 2942, 2, "Hammer"], [2605, 10, 2942, 8], [2605, 11, 2942, 9, "STATE_ENDED"], [2605, 22, 2942, 20], [2605, 25, 2942, 23, "STATE_ENDED"], [2605, 36, 2942, 34], [2606, 4, 2943, 2, "Hammer"], [2606, 10, 2943, 8], [2606, 11, 2943, 9, "STATE_RECOGNIZED"], [2606, 27, 2943, 25], [2606, 30, 2943, 28, "STATE_RECOGNIZED"], [2606, 46, 2943, 44], [2607, 4, 2944, 2, "Hammer"], [2607, 10, 2944, 8], [2607, 11, 2944, 9, "STATE_CANCELLED"], [2607, 26, 2944, 24], [2607, 29, 2944, 27, "STATE_CANCELLED"], [2607, 44, 2944, 42], [2608, 4, 2945, 2, "Hammer"], [2608, 10, 2945, 8], [2608, 11, 2945, 9, "STATE_FAILED"], [2608, 23, 2945, 21], [2608, 26, 2945, 24, "STATE_FAILED"], [2608, 38, 2945, 36], [2609, 4, 2946, 2, "Hammer"], [2609, 10, 2946, 8], [2609, 11, 2946, 9, "Manager"], [2609, 18, 2946, 16], [2609, 21, 2946, 19, "Manager"], [2609, 28, 2946, 26], [2610, 4, 2947, 2, "Hammer"], [2610, 10, 2947, 8], [2610, 11, 2947, 9, "Input"], [2610, 16, 2947, 14], [2610, 19, 2947, 17, "Input"], [2610, 24, 2947, 22], [2611, 4, 2948, 2, "Hammer"], [2611, 10, 2948, 8], [2611, 11, 2948, 9, "TouchAction"], [2611, 22, 2948, 20], [2611, 25, 2948, 23, "TouchAction"], [2611, 36, 2948, 34], [2612, 4, 2949, 2, "Hammer"], [2612, 10, 2949, 8], [2612, 11, 2949, 9, "TouchInput"], [2612, 21, 2949, 19], [2612, 24, 2949, 22, "TouchInput"], [2612, 34, 2949, 32], [2613, 4, 2950, 2, "Hammer"], [2613, 10, 2950, 8], [2613, 11, 2950, 9, "MouseInput"], [2613, 21, 2950, 19], [2613, 24, 2950, 22, "MouseInput"], [2613, 34, 2950, 32], [2614, 4, 2951, 2, "Hammer"], [2614, 10, 2951, 8], [2614, 11, 2951, 9, "PointerEventInput"], [2614, 28, 2951, 26], [2614, 31, 2951, 29, "PointerEventInput"], [2614, 48, 2951, 46], [2615, 4, 2952, 2, "Hammer"], [2615, 10, 2952, 8], [2615, 11, 2952, 9, "TouchMouseInput"], [2615, 26, 2952, 24], [2615, 29, 2952, 27, "TouchMouseInput"], [2615, 44, 2952, 42], [2616, 4, 2953, 2, "Hammer"], [2616, 10, 2953, 8], [2616, 11, 2953, 9, "SingleTouchInput"], [2616, 27, 2953, 25], [2616, 30, 2953, 28, "SingleTouchInput"], [2616, 46, 2953, 44], [2617, 4, 2954, 2, "Hammer"], [2617, 10, 2954, 8], [2617, 11, 2954, 9, "Recognizer"], [2617, 21, 2954, 19], [2617, 24, 2954, 22, "Recognizer"], [2617, 34, 2954, 32], [2618, 4, 2955, 2, "Hammer"], [2618, 10, 2955, 8], [2618, 11, 2955, 9, "AttrRecognizer"], [2618, 25, 2955, 23], [2618, 28, 2955, 26, "AttrRecognizer"], [2618, 42, 2955, 40], [2619, 4, 2956, 2, "Hammer"], [2619, 10, 2956, 8], [2619, 11, 2956, 9, "Tap"], [2619, 14, 2956, 12], [2619, 17, 2956, 15, "TapRecognizer"], [2619, 30, 2956, 28], [2620, 4, 2957, 2, "Hammer"], [2620, 10, 2957, 8], [2620, 11, 2957, 9, "Pan"], [2620, 14, 2957, 12], [2620, 17, 2957, 15, "PanRecognizer"], [2620, 30, 2957, 28], [2621, 4, 2958, 2, "Hammer"], [2621, 10, 2958, 8], [2621, 11, 2958, 9, "Swipe"], [2621, 16, 2958, 14], [2621, 19, 2958, 17, "SwipeRecognizer"], [2621, 34, 2958, 32], [2622, 4, 2959, 2, "Hammer"], [2622, 10, 2959, 8], [2622, 11, 2959, 9, "Pinch"], [2622, 16, 2959, 14], [2622, 19, 2959, 17, "PinchRecognizer"], [2622, 34, 2959, 32], [2623, 4, 2960, 2, "Hammer"], [2623, 10, 2960, 8], [2623, 11, 2960, 9, "Rotate"], [2623, 17, 2960, 15], [2623, 20, 2960, 18, "RotateRecognizer"], [2623, 36, 2960, 34], [2624, 4, 2961, 2, "Hammer"], [2624, 10, 2961, 8], [2624, 11, 2961, 9, "Press"], [2624, 16, 2961, 14], [2624, 19, 2961, 17, "PressRecognizer"], [2624, 34, 2961, 32], [2625, 4, 2962, 2, "Hammer"], [2625, 10, 2962, 8], [2625, 11, 2962, 9, "on"], [2625, 13, 2962, 11], [2625, 16, 2962, 14, "addEventListeners"], [2625, 33, 2962, 31], [2626, 4, 2963, 2, "Hammer"], [2626, 10, 2963, 8], [2626, 11, 2963, 9, "off"], [2626, 14, 2963, 12], [2626, 17, 2963, 15, "removeEventListeners"], [2626, 37, 2963, 35], [2627, 4, 2964, 2, "Hammer"], [2627, 10, 2964, 8], [2627, 11, 2964, 9, "each"], [2627, 15, 2964, 13], [2627, 18, 2964, 16, "each"], [2627, 22, 2964, 20], [2628, 4, 2965, 2, "Hammer"], [2628, 10, 2965, 8], [2628, 11, 2965, 9, "merge"], [2628, 16, 2965, 14], [2628, 19, 2965, 17, "merge"], [2628, 24, 2965, 22], [2629, 4, 2966, 2, "Hammer"], [2629, 10, 2966, 8], [2629, 11, 2966, 9, "extend"], [2629, 17, 2966, 15], [2629, 20, 2966, 18, "extend"], [2629, 26, 2966, 24], [2630, 4, 2967, 2, "Hammer"], [2630, 10, 2967, 8], [2630, 11, 2967, 9, "bindFn"], [2630, 17, 2967, 15], [2630, 20, 2967, 18, "bindFn"], [2630, 26, 2967, 24], [2631, 4, 2968, 2, "Hammer"], [2631, 10, 2968, 8], [2631, 11, 2968, 9, "assign"], [2631, 17, 2968, 15], [2631, 20, 2968, 18, "assign$1"], [2631, 28, 2968, 26], [2632, 4, 2969, 2, "Hammer"], [2632, 10, 2969, 8], [2632, 11, 2969, 9, "inherit"], [2632, 18, 2969, 16], [2632, 21, 2969, 19, "inherit"], [2632, 28, 2969, 26], [2633, 4, 2970, 2, "Hammer"], [2633, 10, 2970, 8], [2633, 11, 2970, 9, "bindFn"], [2633, 17, 2970, 15], [2633, 20, 2970, 18, "bindFn"], [2633, 26, 2970, 24], [2634, 4, 2971, 2, "Hammer"], [2634, 10, 2971, 8], [2634, 11, 2971, 9, "prefixed"], [2634, 19, 2971, 17], [2634, 22, 2971, 20, "prefixed"], [2634, 30, 2971, 28], [2635, 4, 2972, 2, "Hammer"], [2635, 10, 2972, 8], [2635, 11, 2972, 9, "toArray"], [2635, 18, 2972, 16], [2635, 21, 2972, 19, "toArray"], [2635, 28, 2972, 26], [2636, 4, 2973, 2, "Hammer"], [2636, 10, 2973, 8], [2636, 11, 2973, 9, "inArray"], [2636, 18, 2973, 16], [2636, 21, 2973, 19, "inArray"], [2636, 28, 2973, 26], [2637, 4, 2974, 2, "Hammer"], [2637, 10, 2974, 8], [2637, 11, 2974, 9, "uniqueArray"], [2637, 22, 2974, 20], [2637, 25, 2974, 23, "uniqueArray"], [2637, 36, 2974, 34], [2638, 4, 2975, 2, "Hammer"], [2638, 10, 2975, 8], [2638, 11, 2975, 9, "splitStr"], [2638, 19, 2975, 17], [2638, 22, 2975, 20, "splitStr"], [2638, 30, 2975, 28], [2639, 4, 2976, 2, "Hammer"], [2639, 10, 2976, 8], [2639, 11, 2976, 9, "boolOrFn"], [2639, 19, 2976, 17], [2639, 22, 2976, 20, "boolOrFn"], [2639, 30, 2976, 28], [2640, 4, 2977, 2, "Hammer"], [2640, 10, 2977, 8], [2640, 11, 2977, 9, "hasParent"], [2640, 20, 2977, 18], [2640, 23, 2977, 21, "hasParent"], [2640, 32, 2977, 30], [2641, 4, 2978, 2, "Hammer"], [2641, 10, 2978, 8], [2641, 11, 2978, 9, "addEventListeners"], [2641, 28, 2978, 26], [2641, 31, 2978, 29, "addEventListeners"], [2641, 48, 2978, 46], [2642, 4, 2979, 2, "Hammer"], [2642, 10, 2979, 8], [2642, 11, 2979, 9, "removeEventListeners"], [2642, 31, 2979, 29], [2642, 34, 2979, 32, "removeEventListeners"], [2642, 54, 2979, 52], [2643, 4, 2980, 2, "Hammer"], [2643, 10, 2980, 8], [2643, 11, 2980, 9, "defaults"], [2643, 19, 2980, 17], [2643, 22, 2980, 20, "assign$1"], [2643, 30, 2980, 28], [2643, 31, 2980, 29], [2643, 32, 2980, 30], [2643, 33, 2980, 31], [2643, 35, 2980, 33, "defaults"], [2643, 43, 2980, 41], [2643, 45, 2980, 43], [2644, 6, 2981, 4, "preset"], [2644, 12, 2981, 10], [2644, 14, 2981, 12, "preset"], [2645, 4, 2982, 2], [2645, 5, 2982, 3], [2645, 6, 2982, 4], [2646, 4, 2983, 2], [2646, 11, 2983, 9, "Hammer"], [2646, 17, 2983, 15], [2647, 2, 2984, 0], [2647, 3, 2984, 1], [2647, 4, 2984, 2], [2647, 5, 2984, 3], [2649, 2, 2986, 0], [2651, 2, 2988, 0], [2651, 6, 2988, 4, "defaults$1"], [2651, 16, 2988, 14], [2651, 19, 2988, 14, "exports"], [2651, 26, 2988, 14], [2651, 27, 2988, 14, "defaults"], [2651, 35, 2988, 14], [2651, 38, 2988, 17, "Hammer"], [2651, 44, 2988, 23], [2651, 45, 2988, 24, "defaults"], [2651, 53, 2988, 32], [2652, 2, 2988, 33], [2652, 6, 2988, 33, "_default"], [2652, 14, 2988, 33], [2652, 17, 2988, 33, "exports"], [2652, 24, 2988, 33], [2652, 25, 2988, 33, "default"], [2652, 32, 2988, 33], [2652, 35, 2990, 15, "Hammer"], [2652, 41, 2990, 21], [2653, 0, 2990, 21], [2653, 3]], "functionMap": {"names": ["<global>", "_extends", "<anonymous>", "_inherits<PERSON><PERSON>e", "_assertThisInitialized", "assign", "prefixed", "getTouchActionProps", "forEach$argument_0", "each", "boolOrFn", "inStr", "cleanTouchActions", "TouchAction", "set", "update", "compute", "each$argument_1", "preventDefaults", "preventSrc", "hasParent", "getCenter", "simpleCloneInputData", "getDistance", "getAngle", "getDirection", "computeDeltaXY", "getVelocity", "getScale", "getRotation", "computeIntervalInputData", "computeInputData", "inputHandler", "splitStr", "addEventListeners", "removeEventListeners", "getWindowForElement", "Input", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "init", "destroy", "inArray", "PointerEventInput", "toArray", "uniqueArray", "results.sort$argument_0", "TouchInput", "getTouches", "allTouches.filter$argument_0", "MouseInput", "setLastTouch", "removeLastTouch", "recordTouches", "isSyntheticEvent", "TouchMouseInput", "_this.handler", "createInputInstance", "invokeArrayArg", "uniqueId", "getRecognizerByNameIfManager", "stateStr", "Recognizer", "recognizeWith", "dropRecognizeWith", "requireFailure", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "emit", "tryEmit", "canEmit", "recognize", "process", "getTouchAction", "reset", "TapRecognizer", "setTimeout$argument_0", "failTimeout", "AttrRecognizer", "attrTest", "directionStr", "PanRecognizer", "directionTest", "SwipeRecognizer", "PinchRecognizer", "RotateRecognizer", "PressRecognizer", "toggleCssProps", "triggerDomEvent", "Manager", "stop", "get", "add", "remove", "on", "off", "data.preventDefault", "SingleTouchInput", "normalizeSingleTouches", "deprecate", "deprecate$argument_0", "inherit", "bindFn", "boundFn", "Hammer"], "mappings": "AAA;ACM;8BCC;GDY;CDG;AGE;CHI;AIE;CJM;WKa;GLoB;AMwB;CNkB;AOc;4ECO;GDI;CPE;AS0C;CTqB;AUW;CVM;AWS;CXE;AYS;CZ2B;AEa;EWC;GXG;eYU;GZW;kBaO;GbE;mBcQ;mCCE;KDI;GdE;2BgBQ;GhBiC;sBiBQ;GjBG;CFG;AoBU;CpBU;AqBS;CrBwB;AsBS;CtBqB;AuBW;CvBQ;AwBW;CxBQ;AyBU;CzBU;A0BE;C1BqB;A2BU;C3BK;A4BW;C5BE;A6BU;C7BE;A8BS;C9B6B;A+BS;C/BoD;AgCU;ChCqB;AiCQ;CjCE;AkCU;wBjBC;GiBE;ClCC;AmCU;wBlBC;GkBE;CnCC;AoCQ;CpCG;AEa;EmCC;sBCQ;KDI;GnCG;mBqCU,qBrC;gBsCO;GtCI;mBuCO;GvCI;CFG;A0CU;C1CiB;AEkC;EyCG;GzCS;mBqCU;GrCqC;CFG;A4CQ;C5CE;A6CW;6BCoB;ODE;C7CK;AEkB;E6CG;G7CQ;mBqCI;GrCc;CFG;AgDE;oCCe;GDE;ChDiC;AEkB;EgDG;GhDU;mBqCU;GrC0B;CFG;AmDgB;0BCY;KDM;CnDI;AqDE;CrDO;AsDE;CtDe;AEI;IqDM;sBCK;ODgB;KrDO;qBuCgB;KvCG;CFM;AyDU;CzDkB;A0Da;C1DO;A2DgB;C3DE;A4DS;C5DQ;A6DS;C7DY;AEyC;E4DC;G5Dc;eYW;GZK;yB6DS;G7Dc;6B8DS;G9DQ;0B+DS;G/Dc;8BgES;GhEa;8BiEQ;GjEE;4BkES;GlEE;gBmES;GnEwB;mBoEU;GpEO;mBqEQ;GrEY;qBsEQ;GtEsB;mBuEa,8BvE;0BwEW,4BxE;iByES,mBzE;CFG;AEgB;E0EG;G1E2B;0BwEI;GxEE;mBuEE;mCI0C;WJI;GvEO;uB4EE;6BDG;KCE;G5EE;iByEE;GzEE;gBmEE;GnEK;CFG;AEW;E6EG;G7EQ;oB8EY;G9EG;mBuEU;GvEmB;CFG;AiFS;CjFY;AEY;EgFG;GhFgB;0BwEI;GxEa;yBiFE;GjFsB;oB8EE;G9EG;gBmEE;GnEU;CFG;AEY;EkFG;GlFY;0BwEI;GxEE;oB8EE;G9Ea;gBmEE;GnEQ;CFG;AEY;EmFG;GnFU;0BwEI;GxEE;oB8EE;G9EE;gBmEE;GnEO;CFG;AEY;EoFG;GpFU;0BwEI;GxEE;oB8EE;G9EE;CFG;AEY;EqFG;GrFiB;0BwEI;GxEE;mBuEE;+BIc;OJI;GvEM;iByEE;GzEE;gBmEE;GnEW;CFG;AwFuI;iCvEQ;GuES;CxFK;AyFS;CzFK;AEY;EwFC;mCzEa;KyEK;GxFC;eYW;GZe;gByFU;GzFE;qBsEU;GtEkD;e0FS;G1Fc;e2FS;G3FgB;kB4FS;G5FkB;c6FU;2B9EM;K8EG;G7FE;e8FS;2B/EM;K+EM;G9FE;gBmEQ;0B4Be;K5BE;GnEQ;mBuCQ;GvCM;CFG;AEmB;EgGG;GhGS;mBqCI;GrCuB;CFG;AmGE;CnGS;AoGU;SlGE;GkGU;CpGC;uBqGY;CrGa;sBqGW;CrGE;AsGU;CtGU;AuGS;SCC;GDE;CvGC;AEY;EuGM;GvGQ;CF6D"}}, "type": "js/module"}]}