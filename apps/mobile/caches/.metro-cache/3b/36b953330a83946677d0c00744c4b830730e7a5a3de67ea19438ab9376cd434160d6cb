{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 80}, "end": {"line": 4, "column": 36, "index": 116}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "./constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 117}, "end": {"line": 5, "column": 40, "index": 157}}], "key": "waDaw5D7vDr2hRFu0z1BqRCTzP4=", "exportNames": ["*"]}}, {"name": "./Errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 158}, "end": {"line": 6, "column": 44, "index": 202}}], "key": "Hc24lHtszv0n4sfQahKq/F+wSE4=", "exportNames": ["*"]}}, {"name": "./DraggingGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 203}, "end": {"line": 7, "column": 62, "index": 265}}], "key": "veIIVtZ+Znwl7KqPaA+sdGzF8LE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 266}, "end": {"line": 8, "column": 32, "index": 298}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _constants = require(_dependencyMap[2], \"./constants\");\n  var _Errors = require(_dependencyMap[3], \"./Errors\");\n  var _DraggingGestureHandler = _interopRequireDefault(require(_dependencyMap[4], \"./DraggingGestureHandler\"));\n  var _utils = require(_dependencyMap[5], \"./utils\");\n  /* eslint-disable eslint-comments/no-unlimited-disable */\n\n  /* eslint-disable */\n\n  class FlingGestureHandler extends _DraggingGestureHandler.default {\n    get name() {\n      return 'swipe';\n    }\n    get NativeGestureClass() {\n      return _hammerjs.default.Swipe;\n    }\n    onGestureActivated(event) {\n      this.sendEvent({\n        ...event,\n        eventType: _hammerjs.default.INPUT_MOVE,\n        isFinal: false,\n        isFirst: true\n      });\n      this.isGestureRunning = false;\n      this.hasGestureFailed = false;\n      this.sendEvent({\n        ...event,\n        eventType: _hammerjs.default.INPUT_END,\n        isFinal: true\n      });\n    }\n    onRawEvent(ev) {\n      super.onRawEvent(ev);\n      if (this.hasGestureFailed) {\n        return;\n      } // Hammer doesn't send a `cancel` event for taps.\n      // Manually fail the event.\n\n      if (ev.isFinal) {\n        setTimeout(() => {\n          if (this.isGestureRunning) {\n            this.cancelEvent(ev);\n          }\n        });\n      } else if (!this.hasGestureFailed && !this.isGestureRunning) {\n        // Tap Gesture start event\n        const gesture = this.hammer.get(this.name); // @ts-ignore FIXME(TS)\n\n        if (gesture.options.enable(gesture, ev)) {\n          this.onStart(ev);\n          this.sendEvent(ev);\n        }\n      }\n    }\n    getHammerConfig() {\n      return {\n        // @ts-ignore FIXME(TS)\n        pointers: this.config.numberOfPointers,\n        direction: this.getDirection()\n      };\n    }\n    getTargetDirections(direction) {\n      const directions = [];\n      if (direction & _constants.Direction.RIGHT) {\n        directions.push(_hammerjs.default.DIRECTION_RIGHT);\n      }\n      if (direction & _constants.Direction.LEFT) {\n        directions.push(_hammerjs.default.DIRECTION_LEFT);\n      }\n      if (direction & _constants.Direction.UP) {\n        directions.push(_hammerjs.default.DIRECTION_UP);\n      }\n      if (direction & _constants.Direction.DOWN) {\n        directions.push(_hammerjs.default.DIRECTION_DOWN);\n      } // const hammerDirection = directions.reduce((a, b) => a | b, 0);\n\n      return directions;\n    }\n    getDirection() {\n      // @ts-ignore FIXME(TS)\n      const {\n        direction\n      } = this.getConfig();\n      let directions = [];\n      if (direction & _constants.Direction.RIGHT) {\n        directions.push(_hammerjs.default.DIRECTION_HORIZONTAL);\n      }\n      if (direction & _constants.Direction.LEFT) {\n        directions.push(_hammerjs.default.DIRECTION_HORIZONTAL);\n      }\n      if (direction & _constants.Direction.UP) {\n        directions.push(_hammerjs.default.DIRECTION_VERTICAL);\n      }\n      if (direction & _constants.Direction.DOWN) {\n        directions.push(_hammerjs.default.DIRECTION_VERTICAL);\n      }\n      directions = [...new Set(directions)];\n      if (directions.length === 0) return _hammerjs.default.DIRECTION_NONE;\n      if (directions.length === 1) return directions[0];\n      return _hammerjs.default.DIRECTION_ALL;\n    }\n    isGestureEnabledForEvent({\n      numberOfPointers\n    }, _recognizer, {\n      maxPointers: pointerLength\n    }) {\n      const validPointerCount = pointerLength === numberOfPointers;\n      if (!validPointerCount && this.isGestureRunning) {\n        return {\n          failed: true\n        };\n      }\n      return {\n        success: validPointerCount\n      };\n    }\n    updateGestureConfig({\n      numberOfPointers = 1,\n      direction,\n      ...props\n    }) {\n      if ((0, _utils.isnan)(direction) || typeof direction !== 'number') {\n        throw new _Errors.GesturePropError('direction', direction, 'number');\n      }\n      return super.updateGestureConfig({\n        numberOfPointers,\n        direction,\n        ...props\n      });\n    }\n  }\n  var _default = exports.default = FlingGestureHandler;\n});", "lineCount": 139, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_hammerjs"], [7, 15, 4, 0], [7, 18, 4, 0, "_interopRequireDefault"], [7, 40, 4, 0], [7, 41, 4, 0, "require"], [7, 48, 4, 0], [7, 49, 4, 0, "_dependencyMap"], [7, 63, 4, 0], [8, 2, 5, 0], [8, 6, 5, 0, "_constants"], [8, 16, 5, 0], [8, 19, 5, 0, "require"], [8, 26, 5, 0], [8, 27, 5, 0, "_dependencyMap"], [8, 41, 5, 0], [9, 2, 6, 0], [9, 6, 6, 0, "_Errors"], [9, 13, 6, 0], [9, 16, 6, 0, "require"], [9, 23, 6, 0], [9, 24, 6, 0, "_dependencyMap"], [9, 38, 6, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_DraggingGestureHandler"], [10, 29, 7, 0], [10, 32, 7, 0, "_interopRequireDefault"], [10, 54, 7, 0], [10, 55, 7, 0, "require"], [10, 62, 7, 0], [10, 63, 7, 0, "_dependencyMap"], [10, 77, 7, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_utils"], [11, 12, 8, 0], [11, 15, 8, 0, "require"], [11, 22, 8, 0], [11, 23, 8, 0, "_dependencyMap"], [11, 37, 8, 0], [12, 2, 1, 0], [14, 2, 3, 0], [16, 2, 10, 0], [16, 8, 10, 6, "FlingGestureHandler"], [16, 27, 10, 25], [16, 36, 10, 34, "DraggingGestureHandler"], [16, 67, 10, 56], [16, 68, 10, 57], [17, 4, 11, 2], [17, 8, 11, 6, "name"], [17, 12, 11, 10, "name"], [17, 13, 11, 10], [17, 15, 11, 13], [18, 6, 12, 4], [18, 13, 12, 11], [18, 20, 12, 18], [19, 4, 13, 2], [20, 4, 15, 2], [20, 8, 15, 6, "NativeGestureClass"], [20, 26, 15, 24, "NativeGestureClass"], [20, 27, 15, 24], [20, 29, 15, 27], [21, 6, 16, 4], [21, 13, 16, 11, "Hammer"], [21, 30, 16, 17], [21, 31, 16, 18, "Swipe"], [21, 36, 16, 23], [22, 4, 17, 2], [23, 4, 19, 2, "onGestureActivated"], [23, 22, 19, 20, "onGestureActivated"], [23, 23, 19, 21, "event"], [23, 28, 19, 26], [23, 30, 19, 28], [24, 6, 20, 4], [24, 10, 20, 8], [24, 11, 20, 9, "sendEvent"], [24, 20, 20, 18], [24, 21, 20, 19], [25, 8, 20, 21], [25, 11, 20, 24, "event"], [25, 16, 20, 29], [26, 8, 21, 6, "eventType"], [26, 17, 21, 15], [26, 19, 21, 17, "Hammer"], [26, 36, 21, 23], [26, 37, 21, 24, "INPUT_MOVE"], [26, 47, 21, 34], [27, 8, 22, 6, "isFinal"], [27, 15, 22, 13], [27, 17, 22, 15], [27, 22, 22, 20], [28, 8, 23, 6, "<PERSON><PERSON><PERSON><PERSON>"], [28, 15, 23, 13], [28, 17, 23, 15], [29, 6, 24, 4], [29, 7, 24, 5], [29, 8, 24, 6], [30, 6, 25, 4], [30, 10, 25, 8], [30, 11, 25, 9, "isGestureRunning"], [30, 27, 25, 25], [30, 30, 25, 28], [30, 35, 25, 33], [31, 6, 26, 4], [31, 10, 26, 8], [31, 11, 26, 9, "hasGestureFailed"], [31, 27, 26, 25], [31, 30, 26, 28], [31, 35, 26, 33], [32, 6, 27, 4], [32, 10, 27, 8], [32, 11, 27, 9, "sendEvent"], [32, 20, 27, 18], [32, 21, 27, 19], [33, 8, 27, 21], [33, 11, 27, 24, "event"], [33, 16, 27, 29], [34, 8, 28, 6, "eventType"], [34, 17, 28, 15], [34, 19, 28, 17, "Hammer"], [34, 36, 28, 23], [34, 37, 28, 24, "INPUT_END"], [34, 46, 28, 33], [35, 8, 29, 6, "isFinal"], [35, 15, 29, 13], [35, 17, 29, 15], [36, 6, 30, 4], [36, 7, 30, 5], [36, 8, 30, 6], [37, 4, 31, 2], [38, 4, 33, 2, "onRawEvent"], [38, 14, 33, 12, "onRawEvent"], [38, 15, 33, 13, "ev"], [38, 17, 33, 15], [38, 19, 33, 17], [39, 6, 34, 4], [39, 11, 34, 9], [39, 12, 34, 10, "onRawEvent"], [39, 22, 34, 20], [39, 23, 34, 21, "ev"], [39, 25, 34, 23], [39, 26, 34, 24], [40, 6, 36, 4], [40, 10, 36, 8], [40, 14, 36, 12], [40, 15, 36, 13, "hasGestureFailed"], [40, 31, 36, 29], [40, 33, 36, 31], [41, 8, 37, 6], [42, 6, 38, 4], [42, 7, 38, 5], [42, 8, 38, 6], [43, 6, 39, 4], [45, 6, 42, 4], [45, 10, 42, 8, "ev"], [45, 12, 42, 10], [45, 13, 42, 11, "isFinal"], [45, 20, 42, 18], [45, 22, 42, 20], [46, 8, 43, 6, "setTimeout"], [46, 18, 43, 16], [46, 19, 43, 17], [46, 25, 43, 23], [47, 10, 44, 8], [47, 14, 44, 12], [47, 18, 44, 16], [47, 19, 44, 17, "isGestureRunning"], [47, 35, 44, 33], [47, 37, 44, 35], [48, 12, 45, 10], [48, 16, 45, 14], [48, 17, 45, 15, "cancelEvent"], [48, 28, 45, 26], [48, 29, 45, 27, "ev"], [48, 31, 45, 29], [48, 32, 45, 30], [49, 10, 46, 8], [50, 8, 47, 6], [50, 9, 47, 7], [50, 10, 47, 8], [51, 6, 48, 4], [51, 7, 48, 5], [51, 13, 48, 11], [51, 17, 48, 15], [51, 18, 48, 16], [51, 22, 48, 20], [51, 23, 48, 21, "hasGestureFailed"], [51, 39, 48, 37], [51, 43, 48, 41], [51, 44, 48, 42], [51, 48, 48, 46], [51, 49, 48, 47, "isGestureRunning"], [51, 65, 48, 63], [51, 67, 48, 65], [52, 8, 49, 6], [53, 8, 50, 6], [53, 14, 50, 12, "gesture"], [53, 21, 50, 19], [53, 24, 50, 22], [53, 28, 50, 26], [53, 29, 50, 27, "hammer"], [53, 35, 50, 33], [53, 36, 50, 34, "get"], [53, 39, 50, 37], [53, 40, 50, 38], [53, 44, 50, 42], [53, 45, 50, 43, "name"], [53, 49, 50, 47], [53, 50, 50, 48], [53, 51, 50, 49], [53, 52, 50, 50], [55, 8, 52, 6], [55, 12, 52, 10, "gesture"], [55, 19, 52, 17], [55, 20, 52, 18, "options"], [55, 27, 52, 25], [55, 28, 52, 26, "enable"], [55, 34, 52, 32], [55, 35, 52, 33, "gesture"], [55, 42, 52, 40], [55, 44, 52, 42, "ev"], [55, 46, 52, 44], [55, 47, 52, 45], [55, 49, 52, 47], [56, 10, 53, 8], [56, 14, 53, 12], [56, 15, 53, 13, "onStart"], [56, 22, 53, 20], [56, 23, 53, 21, "ev"], [56, 25, 53, 23], [56, 26, 53, 24], [57, 10, 54, 8], [57, 14, 54, 12], [57, 15, 54, 13, "sendEvent"], [57, 24, 54, 22], [57, 25, 54, 23, "ev"], [57, 27, 54, 25], [57, 28, 54, 26], [58, 8, 55, 6], [59, 6, 56, 4], [60, 4, 57, 2], [61, 4, 59, 2, "getHammerConfig"], [61, 19, 59, 17, "getHammerConfig"], [61, 20, 59, 17], [61, 22, 59, 20], [62, 6, 60, 4], [62, 13, 60, 11], [63, 8, 61, 6], [64, 8, 62, 6, "pointers"], [64, 16, 62, 14], [64, 18, 62, 16], [64, 22, 62, 20], [64, 23, 62, 21, "config"], [64, 29, 62, 27], [64, 30, 62, 28, "numberOfPointers"], [64, 46, 62, 44], [65, 8, 63, 6, "direction"], [65, 17, 63, 15], [65, 19, 63, 17], [65, 23, 63, 21], [65, 24, 63, 22, "getDirection"], [65, 36, 63, 34], [65, 37, 63, 35], [66, 6, 64, 4], [66, 7, 64, 5], [67, 4, 65, 2], [68, 4, 67, 2, "getTargetDirections"], [68, 23, 67, 21, "getTargetDirections"], [68, 24, 67, 22, "direction"], [68, 33, 67, 31], [68, 35, 67, 33], [69, 6, 68, 4], [69, 12, 68, 10, "directions"], [69, 22, 68, 20], [69, 25, 68, 23], [69, 27, 68, 25], [70, 6, 70, 4], [70, 10, 70, 8, "direction"], [70, 19, 70, 17], [70, 22, 70, 20, "Direction"], [70, 42, 70, 29], [70, 43, 70, 30, "RIGHT"], [70, 48, 70, 35], [70, 50, 70, 37], [71, 8, 71, 6, "directions"], [71, 18, 71, 16], [71, 19, 71, 17, "push"], [71, 23, 71, 21], [71, 24, 71, 22, "Hammer"], [71, 41, 71, 28], [71, 42, 71, 29, "DIRECTION_RIGHT"], [71, 57, 71, 44], [71, 58, 71, 45], [72, 6, 72, 4], [73, 6, 74, 4], [73, 10, 74, 8, "direction"], [73, 19, 74, 17], [73, 22, 74, 20, "Direction"], [73, 42, 74, 29], [73, 43, 74, 30, "LEFT"], [73, 47, 74, 34], [73, 49, 74, 36], [74, 8, 75, 6, "directions"], [74, 18, 75, 16], [74, 19, 75, 17, "push"], [74, 23, 75, 21], [74, 24, 75, 22, "Hammer"], [74, 41, 75, 28], [74, 42, 75, 29, "DIRECTION_LEFT"], [74, 56, 75, 43], [74, 57, 75, 44], [75, 6, 76, 4], [76, 6, 78, 4], [76, 10, 78, 8, "direction"], [76, 19, 78, 17], [76, 22, 78, 20, "Direction"], [76, 42, 78, 29], [76, 43, 78, 30, "UP"], [76, 45, 78, 32], [76, 47, 78, 34], [77, 8, 79, 6, "directions"], [77, 18, 79, 16], [77, 19, 79, 17, "push"], [77, 23, 79, 21], [77, 24, 79, 22, "Hammer"], [77, 41, 79, 28], [77, 42, 79, 29, "DIRECTION_UP"], [77, 54, 79, 41], [77, 55, 79, 42], [78, 6, 80, 4], [79, 6, 82, 4], [79, 10, 82, 8, "direction"], [79, 19, 82, 17], [79, 22, 82, 20, "Direction"], [79, 42, 82, 29], [79, 43, 82, 30, "DOWN"], [79, 47, 82, 34], [79, 49, 82, 36], [80, 8, 83, 6, "directions"], [80, 18, 83, 16], [80, 19, 83, 17, "push"], [80, 23, 83, 21], [80, 24, 83, 22, "Hammer"], [80, 41, 83, 28], [80, 42, 83, 29, "DIRECTION_DOWN"], [80, 56, 83, 43], [80, 57, 83, 44], [81, 6, 84, 4], [81, 7, 84, 5], [81, 8, 84, 6], [83, 6, 87, 4], [83, 13, 87, 11, "directions"], [83, 23, 87, 21], [84, 4, 88, 2], [85, 4, 90, 2, "getDirection"], [85, 16, 90, 14, "getDirection"], [85, 17, 90, 14], [85, 19, 90, 17], [86, 6, 91, 4], [87, 6, 92, 4], [87, 12, 92, 10], [88, 8, 93, 6, "direction"], [89, 6, 94, 4], [89, 7, 94, 5], [89, 10, 94, 8], [89, 14, 94, 12], [89, 15, 94, 13, "getConfig"], [89, 24, 94, 22], [89, 25, 94, 23], [89, 26, 94, 24], [90, 6, 95, 4], [90, 10, 95, 8, "directions"], [90, 20, 95, 18], [90, 23, 95, 21], [90, 25, 95, 23], [91, 6, 97, 4], [91, 10, 97, 8, "direction"], [91, 19, 97, 17], [91, 22, 97, 20, "Direction"], [91, 42, 97, 29], [91, 43, 97, 30, "RIGHT"], [91, 48, 97, 35], [91, 50, 97, 37], [92, 8, 98, 6, "directions"], [92, 18, 98, 16], [92, 19, 98, 17, "push"], [92, 23, 98, 21], [92, 24, 98, 22, "Hammer"], [92, 41, 98, 28], [92, 42, 98, 29, "DIRECTION_HORIZONTAL"], [92, 62, 98, 49], [92, 63, 98, 50], [93, 6, 99, 4], [94, 6, 101, 4], [94, 10, 101, 8, "direction"], [94, 19, 101, 17], [94, 22, 101, 20, "Direction"], [94, 42, 101, 29], [94, 43, 101, 30, "LEFT"], [94, 47, 101, 34], [94, 49, 101, 36], [95, 8, 102, 6, "directions"], [95, 18, 102, 16], [95, 19, 102, 17, "push"], [95, 23, 102, 21], [95, 24, 102, 22, "Hammer"], [95, 41, 102, 28], [95, 42, 102, 29, "DIRECTION_HORIZONTAL"], [95, 62, 102, 49], [95, 63, 102, 50], [96, 6, 103, 4], [97, 6, 105, 4], [97, 10, 105, 8, "direction"], [97, 19, 105, 17], [97, 22, 105, 20, "Direction"], [97, 42, 105, 29], [97, 43, 105, 30, "UP"], [97, 45, 105, 32], [97, 47, 105, 34], [98, 8, 106, 6, "directions"], [98, 18, 106, 16], [98, 19, 106, 17, "push"], [98, 23, 106, 21], [98, 24, 106, 22, "Hammer"], [98, 41, 106, 28], [98, 42, 106, 29, "DIRECTION_VERTICAL"], [98, 60, 106, 47], [98, 61, 106, 48], [99, 6, 107, 4], [100, 6, 109, 4], [100, 10, 109, 8, "direction"], [100, 19, 109, 17], [100, 22, 109, 20, "Direction"], [100, 42, 109, 29], [100, 43, 109, 30, "DOWN"], [100, 47, 109, 34], [100, 49, 109, 36], [101, 8, 110, 6, "directions"], [101, 18, 110, 16], [101, 19, 110, 17, "push"], [101, 23, 110, 21], [101, 24, 110, 22, "Hammer"], [101, 41, 110, 28], [101, 42, 110, 29, "DIRECTION_VERTICAL"], [101, 60, 110, 47], [101, 61, 110, 48], [102, 6, 111, 4], [103, 6, 113, 4, "directions"], [103, 16, 113, 14], [103, 19, 113, 17], [103, 20, 113, 18], [103, 23, 113, 21], [103, 27, 113, 25, "Set"], [103, 30, 113, 28], [103, 31, 113, 29, "directions"], [103, 41, 113, 39], [103, 42, 113, 40], [103, 43, 113, 41], [104, 6, 114, 4], [104, 10, 114, 8, "directions"], [104, 20, 114, 18], [104, 21, 114, 19, "length"], [104, 27, 114, 25], [104, 32, 114, 30], [104, 33, 114, 31], [104, 35, 114, 33], [104, 42, 114, 40, "Hammer"], [104, 59, 114, 46], [104, 60, 114, 47, "DIRECTION_NONE"], [104, 74, 114, 61], [105, 6, 115, 4], [105, 10, 115, 8, "directions"], [105, 20, 115, 18], [105, 21, 115, 19, "length"], [105, 27, 115, 25], [105, 32, 115, 30], [105, 33, 115, 31], [105, 35, 115, 33], [105, 42, 115, 40, "directions"], [105, 52, 115, 50], [105, 53, 115, 51], [105, 54, 115, 52], [105, 55, 115, 53], [106, 6, 116, 4], [106, 13, 116, 11, "Hammer"], [106, 30, 116, 17], [106, 31, 116, 18, "DIRECTION_ALL"], [106, 44, 116, 31], [107, 4, 117, 2], [108, 4, 119, 2, "isGestureEnabledForEvent"], [108, 28, 119, 26, "isGestureEnabledForEvent"], [108, 29, 119, 27], [109, 6, 120, 4, "numberOfPointers"], [110, 4, 121, 2], [110, 5, 121, 3], [110, 7, 121, 5, "_recognizer"], [110, 18, 121, 16], [110, 20, 121, 18], [111, 6, 122, 4, "maxPointers"], [111, 17, 122, 15], [111, 19, 122, 17, "pointer<PERSON><PERSON><PERSON>"], [112, 4, 123, 2], [112, 5, 123, 3], [112, 7, 123, 5], [113, 6, 124, 4], [113, 12, 124, 10, "validPointerCount"], [113, 29, 124, 27], [113, 32, 124, 30, "pointer<PERSON><PERSON><PERSON>"], [113, 45, 124, 43], [113, 50, 124, 48, "numberOfPointers"], [113, 66, 124, 64], [114, 6, 126, 4], [114, 10, 126, 8], [114, 11, 126, 9, "validPointerCount"], [114, 28, 126, 26], [114, 32, 126, 30], [114, 36, 126, 34], [114, 37, 126, 35, "isGestureRunning"], [114, 53, 126, 51], [114, 55, 126, 53], [115, 8, 127, 6], [115, 15, 127, 13], [116, 10, 128, 8, "failed"], [116, 16, 128, 14], [116, 18, 128, 16], [117, 8, 129, 6], [117, 9, 129, 7], [118, 6, 130, 4], [119, 6, 132, 4], [119, 13, 132, 11], [120, 8, 133, 6, "success"], [120, 15, 133, 13], [120, 17, 133, 15, "validPointerCount"], [121, 6, 134, 4], [121, 7, 134, 5], [122, 4, 135, 2], [123, 4, 137, 2, "updateGestureConfig"], [123, 23, 137, 21, "updateGestureConfig"], [123, 24, 137, 22], [124, 6, 138, 4, "numberOfPointers"], [124, 22, 138, 20], [124, 25, 138, 23], [124, 26, 138, 24], [125, 6, 139, 4, "direction"], [125, 15, 139, 13], [126, 6, 140, 4], [126, 9, 140, 7, "props"], [127, 4, 141, 2], [127, 5, 141, 3], [127, 7, 141, 5], [128, 6, 142, 4], [128, 10, 142, 8], [128, 14, 142, 8, "isnan"], [128, 26, 142, 13], [128, 28, 142, 14, "direction"], [128, 37, 142, 23], [128, 38, 142, 24], [128, 42, 142, 28], [128, 49, 142, 35, "direction"], [128, 58, 142, 44], [128, 63, 142, 49], [128, 71, 142, 57], [128, 73, 142, 59], [129, 8, 143, 6], [129, 14, 143, 12], [129, 18, 143, 16, "GesturePropError"], [129, 42, 143, 32], [129, 43, 143, 33], [129, 54, 143, 44], [129, 56, 143, 46, "direction"], [129, 65, 143, 55], [129, 67, 143, 57], [129, 75, 143, 65], [129, 76, 143, 66], [130, 6, 144, 4], [131, 6, 146, 4], [131, 13, 146, 11], [131, 18, 146, 16], [131, 19, 146, 17, "updateGestureConfig"], [131, 38, 146, 36], [131, 39, 146, 37], [132, 8, 147, 6, "numberOfPointers"], [132, 24, 147, 22], [133, 8, 148, 6, "direction"], [133, 17, 148, 15], [134, 8, 149, 6], [134, 11, 149, 9, "props"], [135, 6, 150, 4], [135, 7, 150, 5], [135, 8, 150, 6], [136, 4, 151, 2], [137, 2, 153, 0], [138, 2, 153, 1], [138, 6, 153, 1, "_default"], [138, 14, 153, 1], [138, 17, 153, 1, "exports"], [138, 24, 153, 1], [138, 25, 153, 1, "default"], [138, 32, 153, 1], [138, 35, 155, 15, "FlingGestureHandler"], [138, 54, 155, 34], [139, 0, 155, 34], [139, 3]], "functionMap": {"names": ["<global>", "FlingGestureHandler", "get__name", "get__NativeGestureClass", "onGestureActivated", "onRawEvent", "setTimeout$argument_0", "getHammerConfig", "getTargetDirections", "getDirection", "isGestureEnabledForEvent", "updateGestureConfig"], "mappings": "AAA;ACS;ECC;GDE;EEE;GFE;EGE;GHY;EIE;iBCU;ODI;GJU;EME;GNM;EOE;GPqB;EQE;GR2B;ESE;GTgB;EUE;GVc;CDE"}}, "type": "js/module"}]}