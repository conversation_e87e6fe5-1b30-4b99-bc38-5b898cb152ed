{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const ScissorsLineDashed = exports.default = (0, _createLucideIcon.default)(\"ScissorsLineDashed\", [[\"path\", {\n    d: \"M5.42 9.42 8 12\",\n    key: \"12pkuq\"\n  }], [\"circle\", {\n    cx: \"4\",\n    cy: \"8\",\n    r: \"2\",\n    key: \"107mxr\"\n  }], [\"path\", {\n    d: \"m14 6-8.58 8.58\",\n    key: \"gvzu5l\"\n  }], [\"circle\", {\n    cx: \"4\",\n    cy: \"16\",\n    r: \"2\",\n    key: \"1ehqvc\"\n  }], [\"path\", {\n    d: \"M10.8 14.8 14 18\",\n    key: \"ax7m9r\"\n  }], [\"path\", {\n    d: \"M16 12h-2\",\n    key: \"10asgb\"\n  }], [\"path\", {\n    d: \"M22 12h-2\",\n    key: \"14jgyd\"\n  }]]);\n});", "lineCount": 41, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "ScissorsLineDashed"], [15, 26, 10, 24], [15, 29, 10, 24, "exports"], [15, 36, 10, 24], [15, 37, 10, 24, "default"], [15, 44, 10, 24], [15, 47, 10, 27], [15, 51, 10, 27, "createLucideIcon"], [15, 76, 10, 43], [15, 78, 10, 44], [15, 98, 10, 64], [15, 100, 10, 66], [15, 101, 11, 2], [15, 102, 11, 3], [15, 108, 11, 9], [15, 110, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 24, 11, 33], [17, 4, 11, 35, "key"], [17, 7, 11, 38], [17, 9, 11, 40], [18, 2, 11, 49], [18, 3, 11, 50], [18, 4, 11, 51], [18, 6, 12, 2], [18, 7, 12, 3], [18, 15, 12, 11], [18, 17, 12, 13], [19, 4, 12, 15, "cx"], [19, 6, 12, 17], [19, 8, 12, 19], [19, 11, 12, 22], [20, 4, 12, 24, "cy"], [20, 6, 12, 26], [20, 8, 12, 28], [20, 11, 12, 31], [21, 4, 12, 33, "r"], [21, 5, 12, 34], [21, 7, 12, 36], [21, 10, 12, 39], [22, 4, 12, 41, "key"], [22, 7, 12, 44], [22, 9, 12, 46], [23, 2, 12, 55], [23, 3, 12, 56], [23, 4, 12, 57], [23, 6, 13, 2], [23, 7, 13, 3], [23, 13, 13, 9], [23, 15, 13, 11], [24, 4, 13, 13, "d"], [24, 5, 13, 14], [24, 7, 13, 16], [24, 24, 13, 33], [25, 4, 13, 35, "key"], [25, 7, 13, 38], [25, 9, 13, 40], [26, 2, 13, 49], [26, 3, 13, 50], [26, 4, 13, 51], [26, 6, 14, 2], [26, 7, 14, 3], [26, 15, 14, 11], [26, 17, 14, 13], [27, 4, 14, 15, "cx"], [27, 6, 14, 17], [27, 8, 14, 19], [27, 11, 14, 22], [28, 4, 14, 24, "cy"], [28, 6, 14, 26], [28, 8, 14, 28], [28, 12, 14, 32], [29, 4, 14, 34, "r"], [29, 5, 14, 35], [29, 7, 14, 37], [29, 10, 14, 40], [30, 4, 14, 42, "key"], [30, 7, 14, 45], [30, 9, 14, 47], [31, 2, 14, 56], [31, 3, 14, 57], [31, 4, 14, 58], [31, 6, 15, 2], [31, 7, 15, 3], [31, 13, 15, 9], [31, 15, 15, 11], [32, 4, 15, 13, "d"], [32, 5, 15, 14], [32, 7, 15, 16], [32, 25, 15, 34], [33, 4, 15, 36, "key"], [33, 7, 15, 39], [33, 9, 15, 41], [34, 2, 15, 50], [34, 3, 15, 51], [34, 4, 15, 52], [34, 6, 16, 2], [34, 7, 16, 3], [34, 13, 16, 9], [34, 15, 16, 11], [35, 4, 16, 13, "d"], [35, 5, 16, 14], [35, 7, 16, 16], [35, 18, 16, 27], [36, 4, 16, 29, "key"], [36, 7, 16, 32], [36, 9, 16, 34], [37, 2, 16, 43], [37, 3, 16, 44], [37, 4, 16, 45], [37, 6, 17, 2], [37, 7, 17, 3], [37, 13, 17, 9], [37, 15, 17, 11], [38, 4, 17, 13, "d"], [38, 5, 17, 14], [38, 7, 17, 16], [38, 18, 17, 27], [39, 4, 17, 29, "key"], [39, 7, 17, 32], [39, 9, 17, 34], [40, 2, 17, 43], [40, 3, 17, 44], [40, 4, 17, 45], [40, 5, 18, 1], [40, 6, 18, 2], [41, 0, 18, 3], [41, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}