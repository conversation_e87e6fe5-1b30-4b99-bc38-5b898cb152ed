{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  /* eslint-disable @typescript-eslint/no-empty-function */\n  class EventManager {\n    constructor(view) {\n      _defineProperty(this, \"view\", void 0);\n      _defineProperty(this, \"pointersInBounds\", []);\n      _defineProperty(this, \"activePointersCounter\", void 0);\n      this.view = view;\n      this.activePointersCounter = 0;\n    }\n    onPointerDown(_event) {}\n    onPointerAdd(_event) {}\n    onPointerUp(_event) {}\n    onPointerRemove(_event) {}\n    onPointerMove(_event) {}\n    onPointerLeave(_event) {} // Called only when pointer is pressed (or touching)\n\n    onPointerEnter(_event) {} // Called only when pointer is pressed (or touching)\n\n    onPointerCancel(_event) {// When pointer cancel is triggered and there are more pointers on the view, only one pointer is cancelled\n      // Because we want all pointers to be cancelled by that event, we are doing it manually by reseting handler and changing activePointersCounter to 0\n      // Events that correspond to removing the pointer (pointerup, touchend) have condition, that they don't perform any action when activePointersCounter\n      // is equal to 0. This prevents counter from going to negative values, when pointers are removed from view after one of them has been cancelled\n    }\n    onPointerOutOfBounds(_event) {}\n    onPointerMoveOver(_event) {}\n    onPointerMoveOut(_event) {}\n    onWheel(_event) {}\n    setOnPointerDown(callback) {\n      this.onPointerDown = callback;\n    }\n    setOnPointerAdd(callback) {\n      this.onPointerAdd = callback;\n    }\n    setOnPointerUp(callback) {\n      this.onPointerUp = callback;\n    }\n    setOnPointerRemove(callback) {\n      this.onPointerRemove = callback;\n    }\n    setOnPointerMove(callback) {\n      this.onPointerMove = callback;\n    }\n    setOnPointerLeave(callback) {\n      this.onPointerLeave = callback;\n    }\n    setOnPointerEnter(callback) {\n      this.onPointerEnter = callback;\n    }\n    setOnPointerCancel(callback) {\n      this.onPointerCancel = callback;\n    }\n    setOnPointerOutOfBounds(callback) {\n      this.onPointerOutOfBounds = callback;\n    }\n    setOnPointerMoveOver(callback) {\n      this.onPointerMoveOver = callback;\n    }\n    setOnPointerMoveOut(callback) {\n      this.onPointerMoveOut = callback;\n    }\n    setOnWheel(callback) {\n      this.onWheel = callback;\n    }\n    markAsInBounds(pointerId) {\n      if (this.pointersInBounds.indexOf(pointerId) >= 0) {\n        return;\n      }\n      this.pointersInBounds.push(pointerId);\n    }\n    markAsOutOfBounds(pointerId) {\n      const index = this.pointersInBounds.indexOf(pointerId);\n      if (index < 0) {\n        return;\n      }\n      this.pointersInBounds.splice(index, 1);\n    }\n    resetManager() {\n      // Reseting activePointersCounter is necessary to make gestures such as pinch work properly\n      // There are gestures that end when there is still one active pointer (like pinch/rotation)\n      // When these gestures end, they are reset, but they still receive events from pointer that is active\n      // This causes trouble, since only onPointerDown registers gesture in orchestrator, and while gestures receive\n      // Events from active pointer after they finished, next pointerdown event will be registered as additional pointer, not the first one\n      // This casues trouble like gestures getting stuck in END state, even though they should have gone to UNDETERMINED\n      this.activePointersCounter = 0;\n      this.pointersInBounds = [];\n    }\n  }\n  exports.default = EventManager;\n});", "lineCount": 108, "map": [[6, 2, 1, 0], [6, 11, 1, 9, "_defineProperty"], [6, 26, 1, 24, "_defineProperty"], [6, 27, 1, 25, "obj"], [6, 30, 1, 28], [6, 32, 1, 30, "key"], [6, 35, 1, 33], [6, 37, 1, 35, "value"], [6, 42, 1, 40], [6, 44, 1, 42], [7, 4, 1, 44], [7, 8, 1, 48, "key"], [7, 11, 1, 51], [7, 15, 1, 55, "obj"], [7, 18, 1, 58], [7, 20, 1, 60], [8, 6, 1, 62, "Object"], [8, 12, 1, 68], [8, 13, 1, 69, "defineProperty"], [8, 27, 1, 83], [8, 28, 1, 84, "obj"], [8, 31, 1, 87], [8, 33, 1, 89, "key"], [8, 36, 1, 92], [8, 38, 1, 94], [9, 8, 1, 96, "value"], [9, 13, 1, 101], [9, 15, 1, 103, "value"], [9, 20, 1, 108], [10, 8, 1, 110, "enumerable"], [10, 18, 1, 120], [10, 20, 1, 122], [10, 24, 1, 126], [11, 8, 1, 128, "configurable"], [11, 20, 1, 140], [11, 22, 1, 142], [11, 26, 1, 146], [12, 8, 1, 148, "writable"], [12, 16, 1, 156], [12, 18, 1, 158], [13, 6, 1, 163], [13, 7, 1, 164], [13, 8, 1, 165], [14, 4, 1, 167], [14, 5, 1, 168], [14, 11, 1, 174], [15, 6, 1, 176, "obj"], [15, 9, 1, 179], [15, 10, 1, 180, "key"], [15, 13, 1, 183], [15, 14, 1, 184], [15, 17, 1, 187, "value"], [15, 22, 1, 192], [16, 4, 1, 194], [17, 4, 1, 196], [17, 11, 1, 203, "obj"], [17, 14, 1, 206], [18, 2, 1, 208], [20, 2, 3, 0], [21, 2, 4, 15], [21, 8, 4, 21, "EventManager"], [21, 20, 4, 33], [21, 21, 4, 34], [22, 4, 5, 2, "constructor"], [22, 15, 5, 13, "constructor"], [22, 16, 5, 14, "view"], [22, 20, 5, 18], [22, 22, 5, 20], [23, 6, 6, 4, "_defineProperty"], [23, 21, 6, 19], [23, 22, 6, 20], [23, 26, 6, 24], [23, 28, 6, 26], [23, 34, 6, 32], [23, 36, 6, 34], [23, 41, 6, 39], [23, 42, 6, 40], [23, 43, 6, 41], [24, 6, 8, 4, "_defineProperty"], [24, 21, 8, 19], [24, 22, 8, 20], [24, 26, 8, 24], [24, 28, 8, 26], [24, 46, 8, 44], [24, 48, 8, 46], [24, 50, 8, 48], [24, 51, 8, 49], [25, 6, 10, 4, "_defineProperty"], [25, 21, 10, 19], [25, 22, 10, 20], [25, 26, 10, 24], [25, 28, 10, 26], [25, 51, 10, 49], [25, 53, 10, 51], [25, 58, 10, 56], [25, 59, 10, 57], [25, 60, 10, 58], [26, 6, 12, 4], [26, 10, 12, 8], [26, 11, 12, 9, "view"], [26, 15, 12, 13], [26, 18, 12, 16, "view"], [26, 22, 12, 20], [27, 6, 13, 4], [27, 10, 13, 8], [27, 11, 13, 9, "activePointersCounter"], [27, 32, 13, 30], [27, 35, 13, 33], [27, 36, 13, 34], [28, 4, 14, 2], [29, 4, 16, 2, "onPointerDown"], [29, 17, 16, 15, "onPointerDown"], [29, 18, 16, 16, "_event"], [29, 24, 16, 22], [29, 26, 16, 24], [29, 27, 16, 25], [30, 4, 18, 2, "onPointerAdd"], [30, 16, 18, 14, "onPointerAdd"], [30, 17, 18, 15, "_event"], [30, 23, 18, 21], [30, 25, 18, 23], [30, 26, 18, 24], [31, 4, 20, 2, "onPointerUp"], [31, 15, 20, 13, "onPointerUp"], [31, 16, 20, 14, "_event"], [31, 22, 20, 20], [31, 24, 20, 22], [31, 25, 20, 23], [32, 4, 22, 2, "onPointerRemove"], [32, 19, 22, 17, "onPointerRemove"], [32, 20, 22, 18, "_event"], [32, 26, 22, 24], [32, 28, 22, 26], [32, 29, 22, 27], [33, 4, 24, 2, "onPointerMove"], [33, 17, 24, 15, "onPointerMove"], [33, 18, 24, 16, "_event"], [33, 24, 24, 22], [33, 26, 24, 24], [33, 27, 24, 25], [34, 4, 26, 2, "onPointerLeave"], [34, 18, 26, 16, "onPointerLeave"], [34, 19, 26, 17, "_event"], [34, 25, 26, 23], [34, 27, 26, 25], [34, 28, 26, 26], [34, 29, 26, 27], [34, 30, 26, 28], [36, 4, 29, 2, "onPointerEnter"], [36, 18, 29, 16, "onPointerEnter"], [36, 19, 29, 17, "_event"], [36, 25, 29, 23], [36, 27, 29, 25], [36, 28, 29, 26], [36, 29, 29, 27], [36, 30, 29, 28], [38, 4, 32, 2, "onPointerCancel"], [38, 19, 32, 17, "onPointerCancel"], [38, 20, 32, 18, "_event"], [38, 26, 32, 24], [38, 28, 32, 26], [38, 29, 32, 27], [39, 6, 33, 4], [40, 6, 34, 4], [41, 6, 35, 4], [42, 4, 35, 4], [43, 4, 38, 2, "onPointerOutOfBounds"], [43, 24, 38, 22, "onPointerOutOfBounds"], [43, 25, 38, 23, "_event"], [43, 31, 38, 29], [43, 33, 38, 31], [43, 34, 38, 32], [44, 4, 40, 2, "onPointerMoveOver"], [44, 21, 40, 19, "onPointerMoveOver"], [44, 22, 40, 20, "_event"], [44, 28, 40, 26], [44, 30, 40, 28], [44, 31, 40, 29], [45, 4, 42, 2, "onPointerMoveOut"], [45, 20, 42, 18, "onPointerMoveOut"], [45, 21, 42, 19, "_event"], [45, 27, 42, 25], [45, 29, 42, 27], [45, 30, 42, 28], [46, 4, 44, 2, "onWheel"], [46, 11, 44, 9, "onWheel"], [46, 12, 44, 10, "_event"], [46, 18, 44, 16], [46, 20, 44, 18], [46, 21, 44, 19], [47, 4, 46, 2, "setOnPointerDown"], [47, 20, 46, 18, "setOnPointerDown"], [47, 21, 46, 19, "callback"], [47, 29, 46, 27], [47, 31, 46, 29], [48, 6, 47, 4], [48, 10, 47, 8], [48, 11, 47, 9, "onPointerDown"], [48, 24, 47, 22], [48, 27, 47, 25, "callback"], [48, 35, 47, 33], [49, 4, 48, 2], [50, 4, 50, 2, "setOnPointerAdd"], [50, 19, 50, 17, "setOnPointerAdd"], [50, 20, 50, 18, "callback"], [50, 28, 50, 26], [50, 30, 50, 28], [51, 6, 51, 4], [51, 10, 51, 8], [51, 11, 51, 9, "onPointerAdd"], [51, 23, 51, 21], [51, 26, 51, 24, "callback"], [51, 34, 51, 32], [52, 4, 52, 2], [53, 4, 54, 2, "setOnPointerUp"], [53, 18, 54, 16, "setOnPointerUp"], [53, 19, 54, 17, "callback"], [53, 27, 54, 25], [53, 29, 54, 27], [54, 6, 55, 4], [54, 10, 55, 8], [54, 11, 55, 9, "onPointerUp"], [54, 22, 55, 20], [54, 25, 55, 23, "callback"], [54, 33, 55, 31], [55, 4, 56, 2], [56, 4, 58, 2, "setOnPointerRemove"], [56, 22, 58, 20, "setOnPointerRemove"], [56, 23, 58, 21, "callback"], [56, 31, 58, 29], [56, 33, 58, 31], [57, 6, 59, 4], [57, 10, 59, 8], [57, 11, 59, 9, "onPointerRemove"], [57, 26, 59, 24], [57, 29, 59, 27, "callback"], [57, 37, 59, 35], [58, 4, 60, 2], [59, 4, 62, 2, "setOnPointerMove"], [59, 20, 62, 18, "setOnPointerMove"], [59, 21, 62, 19, "callback"], [59, 29, 62, 27], [59, 31, 62, 29], [60, 6, 63, 4], [60, 10, 63, 8], [60, 11, 63, 9, "onPointerMove"], [60, 24, 63, 22], [60, 27, 63, 25, "callback"], [60, 35, 63, 33], [61, 4, 64, 2], [62, 4, 66, 2, "setOnPointerLeave"], [62, 21, 66, 19, "setOnPointerLeave"], [62, 22, 66, 20, "callback"], [62, 30, 66, 28], [62, 32, 66, 30], [63, 6, 67, 4], [63, 10, 67, 8], [63, 11, 67, 9, "onPointerLeave"], [63, 25, 67, 23], [63, 28, 67, 26, "callback"], [63, 36, 67, 34], [64, 4, 68, 2], [65, 4, 70, 2, "setOnPointerEnter"], [65, 21, 70, 19, "setOnPointerEnter"], [65, 22, 70, 20, "callback"], [65, 30, 70, 28], [65, 32, 70, 30], [66, 6, 71, 4], [66, 10, 71, 8], [66, 11, 71, 9, "onPointerEnter"], [66, 25, 71, 23], [66, 28, 71, 26, "callback"], [66, 36, 71, 34], [67, 4, 72, 2], [68, 4, 74, 2, "setOnPointerCancel"], [68, 22, 74, 20, "setOnPointerCancel"], [68, 23, 74, 21, "callback"], [68, 31, 74, 29], [68, 33, 74, 31], [69, 6, 75, 4], [69, 10, 75, 8], [69, 11, 75, 9, "onPointerCancel"], [69, 26, 75, 24], [69, 29, 75, 27, "callback"], [69, 37, 75, 35], [70, 4, 76, 2], [71, 4, 78, 2, "setOnPointerOutOfBounds"], [71, 27, 78, 25, "setOnPointerOutOfBounds"], [71, 28, 78, 26, "callback"], [71, 36, 78, 34], [71, 38, 78, 36], [72, 6, 79, 4], [72, 10, 79, 8], [72, 11, 79, 9, "onPointerOutOfBounds"], [72, 31, 79, 29], [72, 34, 79, 32, "callback"], [72, 42, 79, 40], [73, 4, 80, 2], [74, 4, 82, 2, "setOnPointerMoveOver"], [74, 24, 82, 22, "setOnPointerMoveOver"], [74, 25, 82, 23, "callback"], [74, 33, 82, 31], [74, 35, 82, 33], [75, 6, 83, 4], [75, 10, 83, 8], [75, 11, 83, 9, "onPointerMoveOver"], [75, 28, 83, 26], [75, 31, 83, 29, "callback"], [75, 39, 83, 37], [76, 4, 84, 2], [77, 4, 86, 2, "setOnPointerMoveOut"], [77, 23, 86, 21, "setOnPointerMoveOut"], [77, 24, 86, 22, "callback"], [77, 32, 86, 30], [77, 34, 86, 32], [78, 6, 87, 4], [78, 10, 87, 8], [78, 11, 87, 9, "onPointerMoveOut"], [78, 27, 87, 25], [78, 30, 87, 28, "callback"], [78, 38, 87, 36], [79, 4, 88, 2], [80, 4, 90, 2, "setOnWheel"], [80, 14, 90, 12, "setOnWheel"], [80, 15, 90, 13, "callback"], [80, 23, 90, 21], [80, 25, 90, 23], [81, 6, 91, 4], [81, 10, 91, 8], [81, 11, 91, 9, "onWheel"], [81, 18, 91, 16], [81, 21, 91, 19, "callback"], [81, 29, 91, 27], [82, 4, 92, 2], [83, 4, 94, 2, "markAsInBounds"], [83, 18, 94, 16, "markAsInBounds"], [83, 19, 94, 17, "pointerId"], [83, 28, 94, 26], [83, 30, 94, 28], [84, 6, 95, 4], [84, 10, 95, 8], [84, 14, 95, 12], [84, 15, 95, 13, "pointersInBounds"], [84, 31, 95, 29], [84, 32, 95, 30, "indexOf"], [84, 39, 95, 37], [84, 40, 95, 38, "pointerId"], [84, 49, 95, 47], [84, 50, 95, 48], [84, 54, 95, 52], [84, 55, 95, 53], [84, 57, 95, 55], [85, 8, 96, 6], [86, 6, 97, 4], [87, 6, 99, 4], [87, 10, 99, 8], [87, 11, 99, 9, "pointersInBounds"], [87, 27, 99, 25], [87, 28, 99, 26, "push"], [87, 32, 99, 30], [87, 33, 99, 31, "pointerId"], [87, 42, 99, 40], [87, 43, 99, 41], [88, 4, 100, 2], [89, 4, 102, 2, "markAsOutOfBounds"], [89, 21, 102, 19, "markAsOutOfBounds"], [89, 22, 102, 20, "pointerId"], [89, 31, 102, 29], [89, 33, 102, 31], [90, 6, 103, 4], [90, 12, 103, 10, "index"], [90, 17, 103, 15], [90, 20, 103, 18], [90, 24, 103, 22], [90, 25, 103, 23, "pointersInBounds"], [90, 41, 103, 39], [90, 42, 103, 40, "indexOf"], [90, 49, 103, 47], [90, 50, 103, 48, "pointerId"], [90, 59, 103, 57], [90, 60, 103, 58], [91, 6, 105, 4], [91, 10, 105, 8, "index"], [91, 15, 105, 13], [91, 18, 105, 16], [91, 19, 105, 17], [91, 21, 105, 19], [92, 8, 106, 6], [93, 6, 107, 4], [94, 6, 109, 4], [94, 10, 109, 8], [94, 11, 109, 9, "pointersInBounds"], [94, 27, 109, 25], [94, 28, 109, 26, "splice"], [94, 34, 109, 32], [94, 35, 109, 33, "index"], [94, 40, 109, 38], [94, 42, 109, 40], [94, 43, 109, 41], [94, 44, 109, 42], [95, 4, 110, 2], [96, 4, 112, 2, "resetManager"], [96, 16, 112, 14, "resetManager"], [96, 17, 112, 14], [96, 19, 112, 17], [97, 6, 113, 4], [98, 6, 114, 4], [99, 6, 115, 4], [100, 6, 116, 4], [101, 6, 117, 4], [102, 6, 118, 4], [103, 6, 119, 4], [103, 10, 119, 8], [103, 11, 119, 9, "activePointersCounter"], [103, 32, 119, 30], [103, 35, 119, 33], [103, 36, 119, 34], [104, 6, 120, 4], [104, 10, 120, 8], [104, 11, 120, 9, "pointersInBounds"], [104, 27, 120, 25], [104, 30, 120, 28], [104, 32, 120, 30], [105, 4, 121, 2], [106, 2, 123, 0], [107, 2, 123, 1, "exports"], [107, 9, 123, 1], [107, 10, 123, 1, "default"], [107, 17, 123, 1], [107, 20, 123, 1, "EventManager"], [107, 32, 123, 1], [108, 0, 123, 1], [108, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "EventManager", "constructor", "onPointerDown", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerLeave", "onPointerEnter", "onPointerCancel", "onPointerOutOfBounds", "onPointerMoveOver", "onPointerMoveOut", "onWheel", "setOnPointerDown", "setOnPointerAdd", "setOnPointerUp", "setOnPointerRemove", "setOnPointerMove", "setOnPointerLeave", "setOnPointerEnter", "setOnPointerCancel", "setOnPointerOutOfBounds", "setOnPointerMoveOver", "setOnPointerMoveOut", "setOnWheel", "markAsInBounds", "markAsOutOfBounds", "resetManager"], "mappings": "AAA,iNC;eCG;ECC;GDS;EEE,wBF;EGE,uBH;EIE,sBJ;EKE,0BL;EME,wBN;EOE,yBP;EQG,yBR;ESG;GTI;EUE,+BV;EWE,4BX;EYE,2BZ;EaE,kBb;EcE;GdE;EeE;GfE;EgBE;GhBE;EiBE;GjBE;EkBE;GlBE;EmBE;GnBE;EoBE;GpBE;EqBE;GrBE;EsBE;GtBE;EuBE;GvBE;EwBE;GxBE;EyBE;GzBE;E0BE;G1BM;E2BE;G3BQ;E4BE;G5BS;CDE"}}, "type": "js/module"}]}