{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./js/ios10Fix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 23, "index": 23}}], "key": "xc9pJEJol9RM0URTC7BfHnzt6LM=", "exportNames": ["*"]}}, {"name": "./package.json", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 25}, "end": {"line": 3, "column": 41, "index": 66}}], "key": "DP0fDkSnqMAR4GXBbGZPJNMWNZM=", "exportNames": ["*"]}}, {"name": "./js/URL", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 68}, "end": {"line": 5, "column": 25, "index": 93}}], "key": "dO7YM/MHo8RgRqUyGYpxO7wIrLo=", "exportNames": ["*"]}}, {"name": "./js/URLSearchParams", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 94}, "end": {"line": 6, "column": 37, "index": 131}}], "key": "ADto5iwbJo8KdjJmUo/bu0s2n08=", "exportNames": ["*"]}}, {"name": "./js/URL", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 19, "index": 277}, "end": {"line": 11, "column": 38, "index": 296}}], "key": "tC+ncN6ySV0RCJP9h+H39VCxVbY=", "exportNames": ["*"]}}, {"name": "./js/URLSearchParams", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 31, "index": 333}, "end": {"line": 12, "column": 62, "index": 364}}], "key": "paUDmk/SkjuPhlazuLoJeLZssGo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    setupURLPolyfill: true\n  };\n  exports.setupURLPolyfill = setupURLPolyfill;\n  require(_dependencyMap[1], \"./js/ios10Fix\");\n  var _package = _interopRequireDefault(require(_dependencyMap[2], \"./package.json\"));\n  var _URL = require(_dependencyMap[3], \"./js/URL\");\n  Object.keys(_URL).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _URL[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _URL[key];\n      }\n    });\n  });\n  var _URLSearchParams = require(_dependencyMap[4], \"./js/URLSearchParams\");\n  Object.keys(_URLSearchParams).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _URLSearchParams[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _URLSearchParams[key];\n      }\n    });\n  });\n  function setupURLPolyfill() {\n    globalThis.REACT_NATIVE_URL_POLYFILL = `${_package.default.name}@${_package.default.version}`;\n    globalThis.URL = require(_dependencyMap[5], \"./js/URL\").URL;\n    globalThis.URLSearchParams = require(_dependencyMap[6], \"./js/URLSearchParams\").URLSearchParams;\n  }\n});", "lineCount": 41, "map": [[10, 2, 1, 0, "require"], [10, 9, 1, 0], [10, 10, 1, 0, "_dependencyMap"], [10, 24, 1, 0], [11, 2, 3, 0], [11, 6, 3, 0, "_package"], [11, 14, 3, 0], [11, 17, 3, 0, "_interopRequireDefault"], [11, 39, 3, 0], [11, 40, 3, 0, "require"], [11, 47, 3, 0], [11, 48, 3, 0, "_dependencyMap"], [11, 62, 3, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_URL"], [12, 10, 5, 0], [12, 13, 5, 0, "require"], [12, 20, 5, 0], [12, 21, 5, 0, "_dependencyMap"], [12, 35, 5, 0], [13, 2, 5, 0, "Object"], [13, 8, 5, 0], [13, 9, 5, 0, "keys"], [13, 13, 5, 0], [13, 14, 5, 0, "_URL"], [13, 18, 5, 0], [13, 20, 5, 0, "for<PERSON>ach"], [13, 27, 5, 0], [13, 38, 5, 0, "key"], [13, 41, 5, 0], [14, 4, 5, 0], [14, 8, 5, 0, "key"], [14, 11, 5, 0], [14, 29, 5, 0, "key"], [14, 32, 5, 0], [15, 4, 5, 0], [15, 8, 5, 0, "Object"], [15, 14, 5, 0], [15, 15, 5, 0, "prototype"], [15, 24, 5, 0], [15, 25, 5, 0, "hasOwnProperty"], [15, 39, 5, 0], [15, 40, 5, 0, "call"], [15, 44, 5, 0], [15, 45, 5, 0, "_exportNames"], [15, 57, 5, 0], [15, 59, 5, 0, "key"], [15, 62, 5, 0], [16, 4, 5, 0], [16, 8, 5, 0, "key"], [16, 11, 5, 0], [16, 15, 5, 0, "exports"], [16, 22, 5, 0], [16, 26, 5, 0, "exports"], [16, 33, 5, 0], [16, 34, 5, 0, "key"], [16, 37, 5, 0], [16, 43, 5, 0, "_URL"], [16, 47, 5, 0], [16, 48, 5, 0, "key"], [16, 51, 5, 0], [17, 4, 5, 0, "Object"], [17, 10, 5, 0], [17, 11, 5, 0, "defineProperty"], [17, 25, 5, 0], [17, 26, 5, 0, "exports"], [17, 33, 5, 0], [17, 35, 5, 0, "key"], [17, 38, 5, 0], [18, 6, 5, 0, "enumerable"], [18, 16, 5, 0], [19, 6, 5, 0, "get"], [19, 9, 5, 0], [19, 20, 5, 0, "get"], [19, 21, 5, 0], [20, 8, 5, 0], [20, 15, 5, 0, "_URL"], [20, 19, 5, 0], [20, 20, 5, 0, "key"], [20, 23, 5, 0], [21, 6, 5, 0], [22, 4, 5, 0], [23, 2, 5, 0], [24, 2, 6, 0], [24, 6, 6, 0, "_URLSearchParams"], [24, 22, 6, 0], [24, 25, 6, 0, "require"], [24, 32, 6, 0], [24, 33, 6, 0, "_dependencyMap"], [24, 47, 6, 0], [25, 2, 6, 0, "Object"], [25, 8, 6, 0], [25, 9, 6, 0, "keys"], [25, 13, 6, 0], [25, 14, 6, 0, "_URLSearchParams"], [25, 30, 6, 0], [25, 32, 6, 0, "for<PERSON>ach"], [25, 39, 6, 0], [25, 50, 6, 0, "key"], [25, 53, 6, 0], [26, 4, 6, 0], [26, 8, 6, 0, "key"], [26, 11, 6, 0], [26, 29, 6, 0, "key"], [26, 32, 6, 0], [27, 4, 6, 0], [27, 8, 6, 0, "Object"], [27, 14, 6, 0], [27, 15, 6, 0, "prototype"], [27, 24, 6, 0], [27, 25, 6, 0, "hasOwnProperty"], [27, 39, 6, 0], [27, 40, 6, 0, "call"], [27, 44, 6, 0], [27, 45, 6, 0, "_exportNames"], [27, 57, 6, 0], [27, 59, 6, 0, "key"], [27, 62, 6, 0], [28, 4, 6, 0], [28, 8, 6, 0, "key"], [28, 11, 6, 0], [28, 15, 6, 0, "exports"], [28, 22, 6, 0], [28, 26, 6, 0, "exports"], [28, 33, 6, 0], [28, 34, 6, 0, "key"], [28, 37, 6, 0], [28, 43, 6, 0, "_URLSearchParams"], [28, 59, 6, 0], [28, 60, 6, 0, "key"], [28, 63, 6, 0], [29, 4, 6, 0, "Object"], [29, 10, 6, 0], [29, 11, 6, 0, "defineProperty"], [29, 25, 6, 0], [29, 26, 6, 0, "exports"], [29, 33, 6, 0], [29, 35, 6, 0, "key"], [29, 38, 6, 0], [30, 6, 6, 0, "enumerable"], [30, 16, 6, 0], [31, 6, 6, 0, "get"], [31, 9, 6, 0], [31, 20, 6, 0, "get"], [31, 21, 6, 0], [32, 8, 6, 0], [32, 15, 6, 0, "_URLSearchParams"], [32, 31, 6, 0], [32, 32, 6, 0, "key"], [32, 35, 6, 0], [33, 6, 6, 0], [34, 4, 6, 0], [35, 2, 6, 0], [36, 2, 8, 7], [36, 11, 8, 16, "setupURLPolyfill"], [36, 27, 8, 32, "setupURLPolyfill"], [36, 28, 8, 32], [36, 30, 8, 35], [37, 4, 9, 2, "globalThis"], [37, 14, 9, 12], [37, 15, 9, 13, "REACT_NATIVE_URL_POLYFILL"], [37, 40, 9, 38], [37, 43, 9, 41], [37, 46, 9, 44, "packageJson"], [37, 62, 9, 55], [37, 63, 9, 56, "name"], [37, 67, 9, 60], [37, 71, 9, 64, "packageJson"], [37, 87, 9, 75], [37, 88, 9, 76, "version"], [37, 95, 9, 83], [37, 97, 9, 85], [38, 4, 11, 2, "globalThis"], [38, 14, 11, 12], [38, 15, 11, 13, "URL"], [38, 18, 11, 16], [38, 21, 11, 19, "require"], [38, 28, 11, 26], [38, 29, 11, 26, "_dependencyMap"], [38, 43, 11, 26], [38, 58, 11, 37], [38, 59, 11, 38], [38, 60, 11, 39, "URL"], [38, 63, 11, 42], [39, 4, 12, 2, "globalThis"], [39, 14, 12, 12], [39, 15, 12, 13, "URLSearchParams"], [39, 30, 12, 28], [39, 33, 12, 31, "require"], [39, 40, 12, 38], [39, 41, 12, 38, "_dependencyMap"], [39, 55, 12, 38], [39, 82, 12, 61], [39, 83, 12, 62], [39, 84, 12, 63, "URLSearchParams"], [39, 99, 12, 78], [40, 2, 13, 0], [41, 0, 13, 1], [41, 3]], "functionMap": {"names": ["<global>", "setupURLPolyfill"], "mappings": "AAA;OCO;CDK"}}, "type": "js/module"}]}