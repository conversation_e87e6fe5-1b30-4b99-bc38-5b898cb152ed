{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 76, "index": 76}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 77}, "end": {"line": 2, "column": 55, "index": 132}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./Constants.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 133}, "end": {"line": 3, "column": 92, "index": 225}}], "key": "Wdl+AhyCqp62a9CFe5EzEsVXOhQ=", "exportNames": ["*"]}}, {"name": "./ExponentConstants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 226}, "end": {"line": 4, "column": 52, "index": 278}}], "key": "LaDYf8On52EUZlKeu8DGpYCWckE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"AppOwnership\", {\n    enumerable: true,\n    get: function () {\n      return _Constants.AppOwnership;\n    }\n  });\n  Object.defineProperty(exports, \"ExecutionEnvironment\", {\n    enumerable: true,\n    get: function () {\n      return _Constants.ExecutionEnvironment;\n    }\n  });\n  Object.defineProperty(exports, \"UserInterfaceIdiom\", {\n    enumerable: true,\n    get: function () {\n      return _Constants.UserInterfaceIdiom;\n    }\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _expoModulesCore = require(_dependencyMap[2], \"expo-modules-core\");\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _Constants = require(_dependencyMap[4], \"./Constants.types\");\n  var _ExponentConstants = _interopRequireDefault(require(_dependencyMap[5], \"./ExponentConstants\"));\n  if (!_ExponentConstants.default) {\n    console.warn(\"No native ExponentConstants module found, are you sure the expo-constants's module is linked properly?\");\n  }\n  var ExpoUpdates = (0, _expoModulesCore.requireOptionalNativeModule)('ExpoUpdates');\n  var rawUpdatesManifest = null;\n  // If expo-updates defines a non-empty manifest, prefer that one\n  if (ExpoUpdates) {\n    var updatesManifest;\n    if (ExpoUpdates.manifest) {\n      updatesManifest = ExpoUpdates.manifest;\n    } else if (ExpoUpdates.manifestString) {\n      updatesManifest = JSON.parse(ExpoUpdates.manifestString);\n    }\n    if (updatesManifest && Object.keys(updatesManifest).length > 0) {\n      rawUpdatesManifest = updatesManifest;\n    }\n  }\n  // If dev-launcher defines a non-empty manifest, prefer that one\n  var rawDevLauncherManifest = null;\n  if (_reactNative.NativeModules.EXDevLauncher) {\n    var devLauncherManifest;\n    if (_reactNative.NativeModules.EXDevLauncher.manifestString) {\n      devLauncherManifest = JSON.parse(_reactNative.NativeModules.EXDevLauncher.manifestString);\n    }\n    if (devLauncherManifest && Object.keys(devLauncherManifest).length > 0) {\n      rawDevLauncherManifest = devLauncherManifest;\n    }\n  }\n  // Fall back to ExponentConstants.manifest if we don't have one from Updates\n  var rawAppConfig = null;\n  if (_ExponentConstants.default && _ExponentConstants.default.manifest) {\n    var appConfig = _ExponentConstants.default.manifest;\n    // On Android we pass the manifest in JSON form so this step is necessary\n    if (typeof appConfig === 'string') {\n      rawAppConfig = JSON.parse(appConfig);\n    } else {\n      rawAppConfig = appConfig;\n    }\n  }\n  var rawManifest = rawUpdatesManifest ?? rawDevLauncherManifest ?? rawAppConfig;\n  var _ref = _ExponentConstants.default || {},\n    name = _ref.name,\n    appOwnership = _ref.appOwnership,\n    nativeConstants = (0, _objectWithoutProperties2.default)(_ref, [\"name\", \"appOwnership\"]);\n  var constants = {\n    ...nativeConstants,\n    // Ensure this is null in bare workflow\n    appOwnership: appOwnership ?? null\n  };\n  Object.defineProperties(constants, {\n    /**\n     * Use `manifest` property by default.\n     * This property is only used for internal purposes.\n     * It behaves similarly to the original one, but suppresses warning upon no manifest available.\n     * `expo-asset` uses it to prevent users from seeing mentioned warning.\n     */\n    __unsafeNoWarnManifest: {\n      get() {\n        var maybeManifest = getManifest(true);\n        if (!maybeManifest || !isEmbeddedManifest(maybeManifest)) {\n          return null;\n        }\n        return maybeManifest;\n      },\n      enumerable: false\n    },\n    __unsafeNoWarnManifest2: {\n      get() {\n        var maybeManifest = getManifest(true);\n        if (!maybeManifest || !isExpoUpdatesManifest(maybeManifest)) {\n          return null;\n        }\n        return maybeManifest;\n      },\n      enumerable: false\n    },\n    manifest: {\n      get() {\n        var maybeManifest = getManifest();\n        if (!maybeManifest || !isEmbeddedManifest(maybeManifest)) {\n          return null;\n        }\n        return maybeManifest;\n      },\n      enumerable: true\n    },\n    manifest2: {\n      get() {\n        var maybeManifest = getManifest();\n        if (!maybeManifest || !isExpoUpdatesManifest(maybeManifest)) {\n          return null;\n        }\n        return maybeManifest;\n      },\n      enumerable: true\n    },\n    expoConfig: {\n      get() {\n        var maybeManifest = getManifest(true);\n        if (!maybeManifest) {\n          return null;\n        }\n        // if running an embedded update, maybeManifest is a EmbeddedManifest which doesn't have\n        // the expo config. Instead, the embedded expo-constants app.config should be used.\n        if (ExpoUpdates && ExpoUpdates.isEmbeddedLaunch) {\n          return rawAppConfig;\n        }\n        if (isExpoUpdatesManifest(maybeManifest)) {\n          return maybeManifest.extra?.expoClient ?? null;\n        } else if (isEmbeddedManifest(maybeManifest)) {\n          return maybeManifest;\n        }\n        return null;\n      },\n      enumerable: true\n    },\n    expoGoConfig: {\n      get() {\n        var maybeManifest = getManifest(true);\n        if (!maybeManifest) {\n          return null;\n        }\n        if (isExpoUpdatesManifest(maybeManifest)) {\n          return maybeManifest.extra?.expoGo ?? null;\n        } else if (isEmbeddedManifest(maybeManifest)) {\n          return maybeManifest;\n        }\n        return null;\n      },\n      enumerable: true\n    },\n    easConfig: {\n      get() {\n        var maybeManifest = getManifest(true);\n        if (!maybeManifest) {\n          return null;\n        }\n        if (isExpoUpdatesManifest(maybeManifest)) {\n          return maybeManifest.extra?.eas ?? null;\n        } else if (isEmbeddedManifest(maybeManifest)) {\n          return maybeManifest;\n        }\n        return null;\n      },\n      enumerable: true\n    },\n    __rawManifest_TEST: {\n      get() {\n        return rawManifest;\n      },\n      set(value) {\n        rawManifest = value;\n      },\n      enumerable: false\n    }\n  });\n  function isEmbeddedManifest(manifest) {\n    return !isExpoUpdatesManifest(manifest);\n  }\n  function isExpoUpdatesManifest(manifest) {\n    return 'metadata' in manifest;\n  }\n  function getManifest() {\n    var suppressWarning = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!rawManifest) {\n      var invalidManifestType = rawManifest === null ? 'null' : 'undefined';\n      if (nativeConstants.executionEnvironment === _Constants.ExecutionEnvironment.Bare && _reactNative.Platform.OS !== 'web') {\n        if (!suppressWarning) {\n          console.warn(`Constants.manifest is ${invalidManifestType} because the embedded app.config could not be read. Ensure that you have installed the expo-constants build scripts if you need to read from Constants.manifest.`);\n        }\n      } else if (nativeConstants.executionEnvironment === _Constants.ExecutionEnvironment.StoreClient || nativeConstants.executionEnvironment === _Constants.ExecutionEnvironment.Standalone) {\n        // If we somehow get here, this is a truly exceptional state to be in.\n        // Constants.manifest should *always* be defined in those contexts.\n        throw new _expoModulesCore.CodedError('ERR_CONSTANTS_MANIFEST_UNAVAILABLE', `Constants.manifest is ${invalidManifestType}, must be an object.`);\n      }\n    }\n    return rawManifest;\n  }\n  var _default = exports.default = constants;\n});", "lineCount": 209, "map": [[26, 2, 1, 0], [26, 6, 1, 0, "_expoModulesCore"], [26, 22, 1, 0], [26, 25, 1, 0, "require"], [26, 32, 1, 0], [26, 33, 1, 0, "_dependencyMap"], [26, 47, 1, 0], [27, 2, 2, 0], [27, 6, 2, 0, "_reactNative"], [27, 18, 2, 0], [27, 21, 2, 0, "require"], [27, 28, 2, 0], [27, 29, 2, 0, "_dependencyMap"], [27, 43, 2, 0], [28, 2, 3, 0], [28, 6, 3, 0, "_Constants"], [28, 16, 3, 0], [28, 19, 3, 0, "require"], [28, 26, 3, 0], [28, 27, 3, 0, "_dependencyMap"], [28, 41, 3, 0], [29, 2, 4, 0], [29, 6, 4, 0, "_ExponentConstants"], [29, 24, 4, 0], [29, 27, 4, 0, "_interopRequireDefault"], [29, 49, 4, 0], [29, 50, 4, 0, "require"], [29, 57, 4, 0], [29, 58, 4, 0, "_dependencyMap"], [29, 72, 4, 0], [30, 2, 6, 0], [30, 6, 6, 4], [30, 7, 6, 5, "ExponentConstants"], [30, 33, 6, 22], [30, 35, 6, 24], [31, 4, 7, 4, "console"], [31, 11, 7, 11], [31, 12, 7, 12, "warn"], [31, 16, 7, 16], [31, 17, 7, 17], [31, 121, 7, 121], [31, 122, 7, 122], [32, 2, 8, 0], [33, 2, 9, 0], [33, 6, 9, 6, "ExpoUpdates"], [33, 17, 9, 17], [33, 20, 9, 20], [33, 24, 9, 20, "requireOptionalNativeModule"], [33, 68, 9, 47], [33, 70, 9, 48], [33, 83, 9, 61], [33, 84, 9, 62], [34, 2, 10, 0], [34, 6, 10, 4, "rawUpdatesManifest"], [34, 24, 10, 22], [34, 27, 10, 25], [34, 31, 10, 29], [35, 2, 11, 0], [36, 2, 12, 0], [36, 6, 12, 4, "ExpoUpdates"], [36, 17, 12, 15], [36, 19, 12, 17], [37, 4, 13, 4], [37, 8, 13, 8, "updatesManifest"], [37, 23, 13, 23], [38, 4, 14, 4], [38, 8, 14, 8, "ExpoUpdates"], [38, 19, 14, 19], [38, 20, 14, 20, "manifest"], [38, 28, 14, 28], [38, 30, 14, 30], [39, 6, 15, 8, "updatesManifest"], [39, 21, 15, 23], [39, 24, 15, 26, "ExpoUpdates"], [39, 35, 15, 37], [39, 36, 15, 38, "manifest"], [39, 44, 15, 46], [40, 4, 16, 4], [40, 5, 16, 5], [40, 11, 17, 9], [40, 15, 17, 13, "ExpoUpdates"], [40, 26, 17, 24], [40, 27, 17, 25, "manifestString"], [40, 41, 17, 39], [40, 43, 17, 41], [41, 6, 18, 8, "updatesManifest"], [41, 21, 18, 23], [41, 24, 18, 26, "JSON"], [41, 28, 18, 30], [41, 29, 18, 31, "parse"], [41, 34, 18, 36], [41, 35, 18, 37, "ExpoUpdates"], [41, 46, 18, 48], [41, 47, 18, 49, "manifestString"], [41, 61, 18, 63], [41, 62, 18, 64], [42, 4, 19, 4], [43, 4, 20, 4], [43, 8, 20, 8, "updatesManifest"], [43, 23, 20, 23], [43, 27, 20, 27, "Object"], [43, 33, 20, 33], [43, 34, 20, 34, "keys"], [43, 38, 20, 38], [43, 39, 20, 39, "updatesManifest"], [43, 54, 20, 54], [43, 55, 20, 55], [43, 56, 20, 56, "length"], [43, 62, 20, 62], [43, 65, 20, 65], [43, 66, 20, 66], [43, 68, 20, 68], [44, 6, 21, 8, "rawUpdatesManifest"], [44, 24, 21, 26], [44, 27, 21, 29, "updatesManifest"], [44, 42, 21, 44], [45, 4, 22, 4], [46, 2, 23, 0], [47, 2, 24, 0], [48, 2, 25, 0], [48, 6, 25, 4, "rawDevLauncherManifest"], [48, 28, 25, 26], [48, 31, 25, 29], [48, 35, 25, 33], [49, 2, 26, 0], [49, 6, 26, 4, "NativeModules"], [49, 32, 26, 17], [49, 33, 26, 18, "EXDevLauncher"], [49, 46, 26, 31], [49, 48, 26, 33], [50, 4, 27, 4], [50, 8, 27, 8, "devLauncherManifest"], [50, 27, 27, 27], [51, 4, 28, 4], [51, 8, 28, 8, "NativeModules"], [51, 34, 28, 21], [51, 35, 28, 22, "EXDevLauncher"], [51, 48, 28, 35], [51, 49, 28, 36, "manifestString"], [51, 63, 28, 50], [51, 65, 28, 52], [52, 6, 29, 8, "devLauncherManifest"], [52, 25, 29, 27], [52, 28, 29, 30, "JSON"], [52, 32, 29, 34], [52, 33, 29, 35, "parse"], [52, 38, 29, 40], [52, 39, 29, 41, "NativeModules"], [52, 65, 29, 54], [52, 66, 29, 55, "EXDevLauncher"], [52, 79, 29, 68], [52, 80, 29, 69, "manifestString"], [52, 94, 29, 83], [52, 95, 29, 84], [53, 4, 30, 4], [54, 4, 31, 4], [54, 8, 31, 8, "devLauncherManifest"], [54, 27, 31, 27], [54, 31, 31, 31, "Object"], [54, 37, 31, 37], [54, 38, 31, 38, "keys"], [54, 42, 31, 42], [54, 43, 31, 43, "devLauncherManifest"], [54, 62, 31, 62], [54, 63, 31, 63], [54, 64, 31, 64, "length"], [54, 70, 31, 70], [54, 73, 31, 73], [54, 74, 31, 74], [54, 76, 31, 76], [55, 6, 32, 8, "rawDevLauncherManifest"], [55, 28, 32, 30], [55, 31, 32, 33, "devLauncherManifest"], [55, 50, 32, 52], [56, 4, 33, 4], [57, 2, 34, 0], [58, 2, 35, 0], [59, 2, 36, 0], [59, 6, 36, 4, "rawAppConfig"], [59, 18, 36, 16], [59, 21, 36, 19], [59, 25, 36, 23], [60, 2, 37, 0], [60, 6, 37, 4, "ExponentConstants"], [60, 32, 37, 21], [60, 36, 37, 25, "ExponentConstants"], [60, 62, 37, 42], [60, 63, 37, 43, "manifest"], [60, 71, 37, 51], [60, 73, 37, 53], [61, 4, 38, 4], [61, 8, 38, 10, "appConfig"], [61, 17, 38, 19], [61, 20, 38, 22, "ExponentConstants"], [61, 46, 38, 39], [61, 47, 38, 40, "manifest"], [61, 55, 38, 48], [62, 4, 39, 4], [63, 4, 40, 4], [63, 8, 40, 8], [63, 15, 40, 15, "appConfig"], [63, 24, 40, 24], [63, 29, 40, 29], [63, 37, 40, 37], [63, 39, 40, 39], [64, 6, 41, 8, "rawAppConfig"], [64, 18, 41, 20], [64, 21, 41, 23, "JSON"], [64, 25, 41, 27], [64, 26, 41, 28, "parse"], [64, 31, 41, 33], [64, 32, 41, 34, "appConfig"], [64, 41, 41, 43], [64, 42, 41, 44], [65, 4, 42, 4], [65, 5, 42, 5], [65, 11, 43, 9], [66, 6, 44, 8, "rawAppConfig"], [66, 18, 44, 20], [66, 21, 44, 23, "appConfig"], [66, 30, 44, 32], [67, 4, 45, 4], [68, 2, 46, 0], [69, 2, 47, 0], [69, 6, 47, 4, "rawManifest"], [69, 17, 47, 15], [69, 20, 47, 18, "rawUpdatesManifest"], [69, 38, 47, 36], [69, 42, 47, 40, "rawDevLauncherManifest"], [69, 64, 47, 62], [69, 68, 47, 66, "rawAppConfig"], [69, 80, 47, 78], [70, 2, 48, 0], [70, 6, 48, 0, "_ref"], [70, 10, 48, 0], [70, 13, 48, 52, "ExponentConstants"], [70, 39, 48, 69], [70, 43, 48, 73], [70, 44, 48, 74], [70, 45, 48, 75], [71, 4, 48, 8, "name"], [71, 8, 48, 12], [71, 11, 48, 12, "_ref"], [71, 15, 48, 12], [71, 16, 48, 8, "name"], [71, 20, 48, 12], [72, 4, 48, 14, "appOwnership"], [72, 16, 48, 26], [72, 19, 48, 26, "_ref"], [72, 23, 48, 26], [72, 24, 48, 14, "appOwnership"], [72, 36, 48, 26], [73, 4, 48, 31, "nativeConstants"], [73, 19, 48, 46], [73, 26, 48, 46, "_objectWithoutProperties2"], [73, 51, 48, 46], [73, 52, 48, 46, "default"], [73, 59, 48, 46], [73, 61, 48, 46, "_ref"], [73, 65, 48, 46], [74, 2, 49, 0], [74, 6, 49, 6, "constants"], [74, 15, 49, 15], [74, 18, 49, 18], [75, 4, 50, 4], [75, 7, 50, 7, "nativeConstants"], [75, 22, 50, 22], [76, 4, 51, 4], [77, 4, 52, 4, "appOwnership"], [77, 16, 52, 16], [77, 18, 52, 18, "appOwnership"], [77, 30, 52, 30], [77, 34, 52, 34], [78, 2, 53, 0], [78, 3, 53, 1], [79, 2, 54, 0, "Object"], [79, 8, 54, 6], [79, 9, 54, 7, "defineProperties"], [79, 25, 54, 23], [79, 26, 54, 24, "constants"], [79, 35, 54, 33], [79, 37, 54, 35], [80, 4, 55, 4], [81, 0, 56, 0], [82, 0, 57, 0], [83, 0, 58, 0], [84, 0, 59, 0], [85, 0, 60, 0], [86, 4, 61, 4, "__unsafeNoWarnManifest"], [86, 26, 61, 26], [86, 28, 61, 28], [87, 6, 62, 8, "get"], [87, 9, 62, 11, "get"], [87, 10, 62, 11], [87, 12, 62, 14], [88, 8, 63, 12], [88, 12, 63, 18, "maybeManifest"], [88, 25, 63, 31], [88, 28, 63, 34, "getManifest"], [88, 39, 63, 45], [88, 40, 63, 46], [88, 44, 63, 50], [88, 45, 63, 51], [89, 8, 64, 12], [89, 12, 64, 16], [89, 13, 64, 17, "maybeManifest"], [89, 26, 64, 30], [89, 30, 64, 34], [89, 31, 64, 35, "isEmbeddedManifest"], [89, 49, 64, 53], [89, 50, 64, 54, "maybeManifest"], [89, 63, 64, 67], [89, 64, 64, 68], [89, 66, 64, 70], [90, 10, 65, 16], [90, 17, 65, 23], [90, 21, 65, 27], [91, 8, 66, 12], [92, 8, 67, 12], [92, 15, 67, 19, "maybeManifest"], [92, 28, 67, 32], [93, 6, 68, 8], [93, 7, 68, 9], [94, 6, 69, 8, "enumerable"], [94, 16, 69, 18], [94, 18, 69, 20], [95, 4, 70, 4], [95, 5, 70, 5], [96, 4, 71, 4, "__unsafeNoWarnManifest2"], [96, 27, 71, 27], [96, 29, 71, 29], [97, 6, 72, 8, "get"], [97, 9, 72, 11, "get"], [97, 10, 72, 11], [97, 12, 72, 14], [98, 8, 73, 12], [98, 12, 73, 18, "maybeManifest"], [98, 25, 73, 31], [98, 28, 73, 34, "getManifest"], [98, 39, 73, 45], [98, 40, 73, 46], [98, 44, 73, 50], [98, 45, 73, 51], [99, 8, 74, 12], [99, 12, 74, 16], [99, 13, 74, 17, "maybeManifest"], [99, 26, 74, 30], [99, 30, 74, 34], [99, 31, 74, 35, "isExpoUpdatesManifest"], [99, 52, 74, 56], [99, 53, 74, 57, "maybeManifest"], [99, 66, 74, 70], [99, 67, 74, 71], [99, 69, 74, 73], [100, 10, 75, 16], [100, 17, 75, 23], [100, 21, 75, 27], [101, 8, 76, 12], [102, 8, 77, 12], [102, 15, 77, 19, "maybeManifest"], [102, 28, 77, 32], [103, 6, 78, 8], [103, 7, 78, 9], [104, 6, 79, 8, "enumerable"], [104, 16, 79, 18], [104, 18, 79, 20], [105, 4, 80, 4], [105, 5, 80, 5], [106, 4, 81, 4, "manifest"], [106, 12, 81, 12], [106, 14, 81, 14], [107, 6, 82, 8, "get"], [107, 9, 82, 11, "get"], [107, 10, 82, 11], [107, 12, 82, 14], [108, 8, 83, 12], [108, 12, 83, 18, "maybeManifest"], [108, 25, 83, 31], [108, 28, 83, 34, "getManifest"], [108, 39, 83, 45], [108, 40, 83, 46], [108, 41, 83, 47], [109, 8, 84, 12], [109, 12, 84, 16], [109, 13, 84, 17, "maybeManifest"], [109, 26, 84, 30], [109, 30, 84, 34], [109, 31, 84, 35, "isEmbeddedManifest"], [109, 49, 84, 53], [109, 50, 84, 54, "maybeManifest"], [109, 63, 84, 67], [109, 64, 84, 68], [109, 66, 84, 70], [110, 10, 85, 16], [110, 17, 85, 23], [110, 21, 85, 27], [111, 8, 86, 12], [112, 8, 87, 12], [112, 15, 87, 19, "maybeManifest"], [112, 28, 87, 32], [113, 6, 88, 8], [113, 7, 88, 9], [114, 6, 89, 8, "enumerable"], [114, 16, 89, 18], [114, 18, 89, 20], [115, 4, 90, 4], [115, 5, 90, 5], [116, 4, 91, 4, "manifest2"], [116, 13, 91, 13], [116, 15, 91, 15], [117, 6, 92, 8, "get"], [117, 9, 92, 11, "get"], [117, 10, 92, 11], [117, 12, 92, 14], [118, 8, 93, 12], [118, 12, 93, 18, "maybeManifest"], [118, 25, 93, 31], [118, 28, 93, 34, "getManifest"], [118, 39, 93, 45], [118, 40, 93, 46], [118, 41, 93, 47], [119, 8, 94, 12], [119, 12, 94, 16], [119, 13, 94, 17, "maybeManifest"], [119, 26, 94, 30], [119, 30, 94, 34], [119, 31, 94, 35, "isExpoUpdatesManifest"], [119, 52, 94, 56], [119, 53, 94, 57, "maybeManifest"], [119, 66, 94, 70], [119, 67, 94, 71], [119, 69, 94, 73], [120, 10, 95, 16], [120, 17, 95, 23], [120, 21, 95, 27], [121, 8, 96, 12], [122, 8, 97, 12], [122, 15, 97, 19, "maybeManifest"], [122, 28, 97, 32], [123, 6, 98, 8], [123, 7, 98, 9], [124, 6, 99, 8, "enumerable"], [124, 16, 99, 18], [124, 18, 99, 20], [125, 4, 100, 4], [125, 5, 100, 5], [126, 4, 101, 4, "expoConfig"], [126, 14, 101, 14], [126, 16, 101, 16], [127, 6, 102, 8, "get"], [127, 9, 102, 11, "get"], [127, 10, 102, 11], [127, 12, 102, 14], [128, 8, 103, 12], [128, 12, 103, 18, "maybeManifest"], [128, 25, 103, 31], [128, 28, 103, 34, "getManifest"], [128, 39, 103, 45], [128, 40, 103, 46], [128, 44, 103, 50], [128, 45, 103, 51], [129, 8, 104, 12], [129, 12, 104, 16], [129, 13, 104, 17, "maybeManifest"], [129, 26, 104, 30], [129, 28, 104, 32], [130, 10, 105, 16], [130, 17, 105, 23], [130, 21, 105, 27], [131, 8, 106, 12], [132, 8, 107, 12], [133, 8, 108, 12], [134, 8, 109, 12], [134, 12, 109, 16, "ExpoUpdates"], [134, 23, 109, 27], [134, 27, 109, 31, "ExpoUpdates"], [134, 38, 109, 42], [134, 39, 109, 43, "isEmbeddedLaunch"], [134, 55, 109, 59], [134, 57, 109, 61], [135, 10, 110, 16], [135, 17, 110, 23, "rawAppConfig"], [135, 29, 110, 35], [136, 8, 111, 12], [137, 8, 112, 12], [137, 12, 112, 16, "isExpoUpdatesManifest"], [137, 33, 112, 37], [137, 34, 112, 38, "maybeManifest"], [137, 47, 112, 51], [137, 48, 112, 52], [137, 50, 112, 54], [138, 10, 113, 16], [138, 17, 113, 23, "maybeManifest"], [138, 30, 113, 36], [138, 31, 113, 37, "extra"], [138, 36, 113, 42], [138, 38, 113, 44, "expoClient"], [138, 48, 113, 54], [138, 52, 113, 58], [138, 56, 113, 62], [139, 8, 114, 12], [139, 9, 114, 13], [139, 15, 115, 17], [139, 19, 115, 21, "isEmbeddedManifest"], [139, 37, 115, 39], [139, 38, 115, 40, "maybeManifest"], [139, 51, 115, 53], [139, 52, 115, 54], [139, 54, 115, 56], [140, 10, 116, 16], [140, 17, 116, 23, "maybeManifest"], [140, 30, 116, 36], [141, 8, 117, 12], [142, 8, 118, 12], [142, 15, 118, 19], [142, 19, 118, 23], [143, 6, 119, 8], [143, 7, 119, 9], [144, 6, 120, 8, "enumerable"], [144, 16, 120, 18], [144, 18, 120, 20], [145, 4, 121, 4], [145, 5, 121, 5], [146, 4, 122, 4, "expoGoConfig"], [146, 16, 122, 16], [146, 18, 122, 18], [147, 6, 123, 8, "get"], [147, 9, 123, 11, "get"], [147, 10, 123, 11], [147, 12, 123, 14], [148, 8, 124, 12], [148, 12, 124, 18, "maybeManifest"], [148, 25, 124, 31], [148, 28, 124, 34, "getManifest"], [148, 39, 124, 45], [148, 40, 124, 46], [148, 44, 124, 50], [148, 45, 124, 51], [149, 8, 125, 12], [149, 12, 125, 16], [149, 13, 125, 17, "maybeManifest"], [149, 26, 125, 30], [149, 28, 125, 32], [150, 10, 126, 16], [150, 17, 126, 23], [150, 21, 126, 27], [151, 8, 127, 12], [152, 8, 128, 12], [152, 12, 128, 16, "isExpoUpdatesManifest"], [152, 33, 128, 37], [152, 34, 128, 38, "maybeManifest"], [152, 47, 128, 51], [152, 48, 128, 52], [152, 50, 128, 54], [153, 10, 129, 16], [153, 17, 129, 23, "maybeManifest"], [153, 30, 129, 36], [153, 31, 129, 37, "extra"], [153, 36, 129, 42], [153, 38, 129, 44, "expoGo"], [153, 44, 129, 50], [153, 48, 129, 54], [153, 52, 129, 58], [154, 8, 130, 12], [154, 9, 130, 13], [154, 15, 131, 17], [154, 19, 131, 21, "isEmbeddedManifest"], [154, 37, 131, 39], [154, 38, 131, 40, "maybeManifest"], [154, 51, 131, 53], [154, 52, 131, 54], [154, 54, 131, 56], [155, 10, 132, 16], [155, 17, 132, 23, "maybeManifest"], [155, 30, 132, 36], [156, 8, 133, 12], [157, 8, 134, 12], [157, 15, 134, 19], [157, 19, 134, 23], [158, 6, 135, 8], [158, 7, 135, 9], [159, 6, 136, 8, "enumerable"], [159, 16, 136, 18], [159, 18, 136, 20], [160, 4, 137, 4], [160, 5, 137, 5], [161, 4, 138, 4, "easConfig"], [161, 13, 138, 13], [161, 15, 138, 15], [162, 6, 139, 8, "get"], [162, 9, 139, 11, "get"], [162, 10, 139, 11], [162, 12, 139, 14], [163, 8, 140, 12], [163, 12, 140, 18, "maybeManifest"], [163, 25, 140, 31], [163, 28, 140, 34, "getManifest"], [163, 39, 140, 45], [163, 40, 140, 46], [163, 44, 140, 50], [163, 45, 140, 51], [164, 8, 141, 12], [164, 12, 141, 16], [164, 13, 141, 17, "maybeManifest"], [164, 26, 141, 30], [164, 28, 141, 32], [165, 10, 142, 16], [165, 17, 142, 23], [165, 21, 142, 27], [166, 8, 143, 12], [167, 8, 144, 12], [167, 12, 144, 16, "isExpoUpdatesManifest"], [167, 33, 144, 37], [167, 34, 144, 38, "maybeManifest"], [167, 47, 144, 51], [167, 48, 144, 52], [167, 50, 144, 54], [168, 10, 145, 16], [168, 17, 145, 23, "maybeManifest"], [168, 30, 145, 36], [168, 31, 145, 37, "extra"], [168, 36, 145, 42], [168, 38, 145, 44, "eas"], [168, 41, 145, 47], [168, 45, 145, 51], [168, 49, 145, 55], [169, 8, 146, 12], [169, 9, 146, 13], [169, 15, 147, 17], [169, 19, 147, 21, "isEmbeddedManifest"], [169, 37, 147, 39], [169, 38, 147, 40, "maybeManifest"], [169, 51, 147, 53], [169, 52, 147, 54], [169, 54, 147, 56], [170, 10, 148, 16], [170, 17, 148, 23, "maybeManifest"], [170, 30, 148, 36], [171, 8, 149, 12], [172, 8, 150, 12], [172, 15, 150, 19], [172, 19, 150, 23], [173, 6, 151, 8], [173, 7, 151, 9], [174, 6, 152, 8, "enumerable"], [174, 16, 152, 18], [174, 18, 152, 20], [175, 4, 153, 4], [175, 5, 153, 5], [176, 4, 154, 4, "__rawManifest_TEST"], [176, 22, 154, 22], [176, 24, 154, 24], [177, 6, 155, 8, "get"], [177, 9, 155, 11, "get"], [177, 10, 155, 11], [177, 12, 155, 14], [178, 8, 156, 12], [178, 15, 156, 19, "rawManifest"], [178, 26, 156, 30], [179, 6, 157, 8], [179, 7, 157, 9], [180, 6, 158, 8, "set"], [180, 9, 158, 11, "set"], [180, 10, 158, 12, "value"], [180, 15, 158, 17], [180, 17, 158, 19], [181, 8, 159, 12, "rawManifest"], [181, 19, 159, 23], [181, 22, 159, 26, "value"], [181, 27, 159, 31], [182, 6, 160, 8], [182, 7, 160, 9], [183, 6, 161, 8, "enumerable"], [183, 16, 161, 18], [183, 18, 161, 20], [184, 4, 162, 4], [185, 2, 163, 0], [185, 3, 163, 1], [185, 4, 163, 2], [186, 2, 164, 0], [186, 11, 164, 9, "isEmbeddedManifest"], [186, 29, 164, 27, "isEmbeddedManifest"], [186, 30, 164, 28, "manifest"], [186, 38, 164, 36], [186, 40, 164, 38], [187, 4, 165, 4], [187, 11, 165, 11], [187, 12, 165, 12, "isExpoUpdatesManifest"], [187, 33, 165, 33], [187, 34, 165, 34, "manifest"], [187, 42, 165, 42], [187, 43, 165, 43], [188, 2, 166, 0], [189, 2, 167, 0], [189, 11, 167, 9, "isExpoUpdatesManifest"], [189, 32, 167, 30, "isExpoUpdatesManifest"], [189, 33, 167, 31, "manifest"], [189, 41, 167, 39], [189, 43, 167, 41], [190, 4, 168, 4], [190, 11, 168, 11], [190, 21, 168, 21], [190, 25, 168, 25, "manifest"], [190, 33, 168, 33], [191, 2, 169, 0], [192, 2, 170, 0], [192, 11, 170, 9, "getManifest"], [192, 22, 170, 20, "getManifest"], [192, 23, 170, 20], [192, 25, 170, 46], [193, 4, 170, 46], [193, 8, 170, 21, "suppressWarning"], [193, 23, 170, 36], [193, 26, 170, 36, "arguments"], [193, 35, 170, 36], [193, 36, 170, 36, "length"], [193, 42, 170, 36], [193, 50, 170, 36, "arguments"], [193, 59, 170, 36], [193, 67, 170, 36, "undefined"], [193, 76, 170, 36], [193, 79, 170, 36, "arguments"], [193, 88, 170, 36], [193, 94, 170, 39], [193, 99, 170, 44], [194, 4, 171, 4], [194, 8, 171, 8], [194, 9, 171, 9, "rawManifest"], [194, 20, 171, 20], [194, 22, 171, 22], [195, 6, 172, 8], [195, 10, 172, 14, "invalidManifestType"], [195, 29, 172, 33], [195, 32, 172, 36, "rawManifest"], [195, 43, 172, 47], [195, 48, 172, 52], [195, 52, 172, 56], [195, 55, 172, 59], [195, 61, 172, 65], [195, 64, 172, 68], [195, 75, 172, 79], [196, 6, 173, 8], [196, 10, 173, 12, "nativeConstants"], [196, 25, 173, 27], [196, 26, 173, 28, "executionEnvironment"], [196, 46, 173, 48], [196, 51, 173, 53, "ExecutionEnvironment"], [196, 82, 173, 73], [196, 83, 173, 74, "<PERSON><PERSON>"], [196, 87, 173, 78], [196, 91, 174, 12, "Platform"], [196, 112, 174, 20], [196, 113, 174, 21, "OS"], [196, 115, 174, 23], [196, 120, 174, 28], [196, 125, 174, 33], [196, 127, 174, 35], [197, 8, 175, 12], [197, 12, 175, 16], [197, 13, 175, 17, "suppressWarning"], [197, 28, 175, 32], [197, 30, 175, 34], [198, 10, 176, 16, "console"], [198, 17, 176, 23], [198, 18, 176, 24, "warn"], [198, 22, 176, 28], [198, 23, 176, 29], [198, 48, 176, 54, "invalidManifestType"], [198, 67, 176, 73], [198, 229, 176, 235], [198, 230, 176, 236], [199, 8, 177, 12], [200, 6, 178, 8], [200, 7, 178, 9], [200, 13, 179, 13], [200, 17, 179, 17, "nativeConstants"], [200, 32, 179, 32], [200, 33, 179, 33, "executionEnvironment"], [200, 53, 179, 53], [200, 58, 179, 58, "ExecutionEnvironment"], [200, 89, 179, 78], [200, 90, 179, 79, "StoreClient"], [200, 101, 179, 90], [200, 105, 180, 12, "nativeConstants"], [200, 120, 180, 27], [200, 121, 180, 28, "executionEnvironment"], [200, 141, 180, 48], [200, 146, 180, 53, "ExecutionEnvironment"], [200, 177, 180, 73], [200, 178, 180, 74, "Standalone"], [200, 188, 180, 84], [200, 190, 180, 86], [201, 8, 181, 12], [202, 8, 182, 12], [203, 8, 183, 12], [203, 14, 183, 18], [203, 18, 183, 22, "CodedError"], [203, 45, 183, 32], [203, 46, 183, 33], [203, 82, 183, 69], [203, 84, 183, 71], [203, 109, 183, 96, "invalidManifestType"], [203, 128, 183, 115], [203, 150, 183, 137], [203, 151, 183, 138], [204, 6, 184, 8], [205, 4, 185, 4], [206, 4, 186, 4], [206, 11, 186, 11, "rawManifest"], [206, 22, 186, 22], [207, 2, 187, 0], [208, 2, 187, 1], [208, 6, 187, 1, "_default"], [208, 14, 187, 1], [208, 17, 187, 1, "exports"], [208, 24, 187, 1], [208, 25, 187, 1, "default"], [208, 32, 187, 1], [208, 35, 188, 15, "constants"], [208, 44, 188, 24], [209, 0, 188, 24], [209, 3]], "functionMap": {"names": ["<global>", "Object.defineProperties$argument_1.__unsafeNoWarnManifest.get", "Object.defineProperties$argument_1.__unsafeNoWarnManifest2.get", "Object.defineProperties$argument_1.manifest.get", "Object.defineProperties$argument_1.manifest2.get", "Object.defineProperties$argument_1.expoConfig.get", "Object.defineProperties$argument_1.expoGoConfig.get", "Object.defineProperties$argument_1.easConfig.get", "Object.defineProperties$argument_1.__rawManifest_TEST.get", "Object.defineProperties$argument_1.__rawManifest_TEST.set", "isEmbeddedManifest", "isExpoUpdatesManifest", "getManifest"], "mappings": "AAA;QC6D;SDM;QEI;SFM;QGI;SHM;QII;SJM;QKI;SLiB;QMI;SNY;QOI;SPY;QQI;SRE;QSC;STE;AUI;CVE;AWC;CXE;AYC;CZiB"}}, "type": "js/module"}]}