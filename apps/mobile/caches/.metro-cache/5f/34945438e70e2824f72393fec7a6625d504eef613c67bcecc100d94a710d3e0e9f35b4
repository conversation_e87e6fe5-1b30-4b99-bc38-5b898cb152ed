{"dependencies": [{"name": "./Asset.fx", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 20, "index": 20}}], "key": "d6+GFVWWjoMV2Q4aVdpbLrIZRf8=", "exportNames": ["*"]}}, {"name": "./Asset", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 21}, "end": {"line": 2, "column": 24, "index": 45}}], "key": "TwnMoPBJu+ST6a0NSE4l343cBbk=", "exportNames": ["*"]}}, {"name": "./AssetHooks", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 46}, "end": {"line": 3, "column": 29, "index": 75}}], "key": "ZUAvHcyztIp87Dr/bRIeSoy29Zk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  require(_dependencyMap[0], \"./Asset.fx\");\n  var _Asset2 = require(_dependencyMap[1], \"./Asset\");\n  Object.keys(_Asset2).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _Asset2[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Asset2[key];\n      }\n    });\n  });\n  var _AssetHooks = require(_dependencyMap[2], \"./AssetHooks\");\n  Object.keys(_AssetHooks).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _AssetHooks[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _AssetHooks[key];\n      }\n    });\n  });\n});", "lineCount": 28, "map": [[5, 2, 1, 0, "require"], [5, 9, 1, 0], [5, 10, 1, 0, "_dependencyMap"], [5, 24, 1, 0], [6, 2, 2, 0], [6, 6, 2, 0, "_Asset2"], [6, 13, 2, 0], [6, 16, 2, 0, "require"], [6, 23, 2, 0], [6, 24, 2, 0, "_dependencyMap"], [6, 38, 2, 0], [7, 2, 2, 0, "Object"], [7, 8, 2, 0], [7, 9, 2, 0, "keys"], [7, 13, 2, 0], [7, 14, 2, 0, "_Asset2"], [7, 21, 2, 0], [7, 23, 2, 0, "for<PERSON>ach"], [7, 30, 2, 0], [7, 41, 2, 0, "key"], [7, 44, 2, 0], [8, 4, 2, 0], [8, 8, 2, 0, "key"], [8, 11, 2, 0], [8, 29, 2, 0, "key"], [8, 32, 2, 0], [9, 4, 2, 0], [9, 8, 2, 0, "key"], [9, 11, 2, 0], [9, 15, 2, 0, "exports"], [9, 22, 2, 0], [9, 26, 2, 0, "exports"], [9, 33, 2, 0], [9, 34, 2, 0, "key"], [9, 37, 2, 0], [9, 43, 2, 0, "_Asset2"], [9, 50, 2, 0], [9, 51, 2, 0, "key"], [9, 54, 2, 0], [10, 4, 2, 0, "Object"], [10, 10, 2, 0], [10, 11, 2, 0, "defineProperty"], [10, 25, 2, 0], [10, 26, 2, 0, "exports"], [10, 33, 2, 0], [10, 35, 2, 0, "key"], [10, 38, 2, 0], [11, 6, 2, 0, "enumerable"], [11, 16, 2, 0], [12, 6, 2, 0, "get"], [12, 9, 2, 0], [12, 20, 2, 0, "get"], [12, 21, 2, 0], [13, 8, 2, 0], [13, 15, 2, 0, "_Asset2"], [13, 22, 2, 0], [13, 23, 2, 0, "key"], [13, 26, 2, 0], [14, 6, 2, 0], [15, 4, 2, 0], [16, 2, 2, 0], [17, 2, 3, 0], [17, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON>s"], [17, 17, 3, 0], [17, 20, 3, 0, "require"], [17, 27, 3, 0], [17, 28, 3, 0, "_dependencyMap"], [17, 42, 3, 0], [18, 2, 3, 0, "Object"], [18, 8, 3, 0], [18, 9, 3, 0, "keys"], [18, 13, 3, 0], [18, 14, 3, 0, "_<PERSON><PERSON><PERSON><PERSON>s"], [18, 25, 3, 0], [18, 27, 3, 0, "for<PERSON>ach"], [18, 34, 3, 0], [18, 45, 3, 0, "key"], [18, 48, 3, 0], [19, 4, 3, 0], [19, 8, 3, 0, "key"], [19, 11, 3, 0], [19, 29, 3, 0, "key"], [19, 32, 3, 0], [20, 4, 3, 0], [20, 8, 3, 0, "key"], [20, 11, 3, 0], [20, 15, 3, 0, "exports"], [20, 22, 3, 0], [20, 26, 3, 0, "exports"], [20, 33, 3, 0], [20, 34, 3, 0, "key"], [20, 37, 3, 0], [20, 43, 3, 0, "_<PERSON><PERSON><PERSON><PERSON>s"], [20, 54, 3, 0], [20, 55, 3, 0, "key"], [20, 58, 3, 0], [21, 4, 3, 0, "Object"], [21, 10, 3, 0], [21, 11, 3, 0, "defineProperty"], [21, 25, 3, 0], [21, 26, 3, 0, "exports"], [21, 33, 3, 0], [21, 35, 3, 0, "key"], [21, 38, 3, 0], [22, 6, 3, 0, "enumerable"], [22, 16, 3, 0], [23, 6, 3, 0, "get"], [23, 9, 3, 0], [23, 20, 3, 0, "get"], [23, 21, 3, 0], [24, 8, 3, 0], [24, 15, 3, 0, "_<PERSON><PERSON><PERSON><PERSON>s"], [24, 26, 3, 0], [24, 27, 3, 0, "key"], [24, 30, 3, 0], [25, 6, 3, 0], [26, 4, 3, 0], [27, 2, 3, 0], [28, 0, 3, 29], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}