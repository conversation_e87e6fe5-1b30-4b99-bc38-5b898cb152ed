{"dependencies": [{"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-is", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 46, "index": 93}}], "key": "nMRUu046PLZz3vjxw7Fgw3UH3xY=", "exportNames": ["*"]}}, {"name": "./useRoute.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 94}, "end": {"line": 5, "column": 41, "index": 135}}], "key": "099N+Zv4K9pUFNBPk2MtNNMStD8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 808}, "end": {"line": 30, "column": 48, "index": 856}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createComponentForStaticNavigation = createComponentForStaticNavigation;\n  exports.createPathConfigForStaticNavigation = createPathConfigForStaticNavigation;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[0], \"react-native-css-interop\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactIs = require(_dependencyMap[2], \"react-is\");\n  var _useRoute = require(_dependencyMap[3], \"./useRoute.js\");\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Flatten a type to remove all type alias names, unions etc.\n   * This will show a plain object when hovering over the type.\n   */\n\n  /**\n   * keyof T doesn't work for union types. We can use distributive conditional types instead.\n   * https://www.typescriptlang.org/docs/handbook/2/conditional-types.html#distributive-conditional-types\n   */\n\n  /**\n   * We get a union type when using keyof, but we want an intersection instead.\n   * https://stackoverflow.com/a/50375286/1665026\n   */\n\n  /**\n   * Props for a screen component which is rendered by a static navigator.\n   * Takes the route params as a generic argument.\n   */\n\n  /**\n   * Infer the param list from the static navigation config.\n   */\n\n  const MemoizedScreen = /*#__PURE__*/React.memo(({\n    component\n  }) => {\n    const route = (0, _useRoute.useRoute)();\n    const children = /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(component, {\n      route\n    });\n    return children;\n  });\n  MemoizedScreen.displayName = 'Memo(Screen)';\n  const getItemsFromScreens = (Screen, screens) => {\n    return Object.entries(screens).map(([name, item]) => {\n      let component;\n      let props = {};\n      let useIf;\n      let isNavigator = false;\n      if ('screen' in item) {\n        const {\n          screen,\n          if: _if,\n          ...rest\n        } = item;\n        useIf = _if;\n        props = rest;\n        if ((0, _reactIs.isValidElementType)(screen)) {\n          component = screen;\n        } else if ('config' in screen) {\n          isNavigator = true;\n          component = createComponentForStaticNavigation(screen, `${name}Navigator`);\n        }\n      } else if ((0, _reactIs.isValidElementType)(item)) {\n        component = item;\n      } else if ('config' in item) {\n        isNavigator = true;\n        component = createComponentForStaticNavigation(item, `${name}Navigator`);\n      }\n      if (component == null) {\n        throw new Error(`Couldn't find a 'screen' property for the screen '${name}'. This can happen if you passed 'undefined'. You likely forgot to export your component from the file it's defined in, or mixed up default import and named import when importing.`);\n      }\n      const element = isNavigator ? (/*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(component, {})) : /*#__PURE__*/(0, _jsxRuntime.jsx)(MemoizedScreen, {\n        component: component\n      });\n      return () => {\n        const shouldRender = useIf == null || useIf();\n        if (!shouldRender) {\n          return null;\n        }\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(Screen, {\n          name: name,\n          ...props,\n          children: () => element\n        }, name);\n      };\n    });\n  };\n\n  /**\n   * Create a component that renders a navigator based on the static configuration.\n   *\n   * @param tree Static navigation config.\n   * @param displayName Name of the component to be displayed in React DevTools.\n   * @returns A component which renders the navigator.\n   */\n  function createComponentForStaticNavigation(tree, displayName) {\n    const {\n      Navigator,\n      Group,\n      Screen,\n      config\n    } = tree;\n    const {\n      screens,\n      groups,\n      ...rest\n    } = config;\n    if (screens == null && groups == null) {\n      throw new Error(\"Couldn't find a 'screens' or 'groups' property. Make sure to define your screens under a 'screens' property in the configuration.\");\n    }\n    const items = [];\n\n    // Loop through the config to find screens and groups\n    // So we add the screens and groups in the same order as they are defined\n    for (const key in config) {\n      if (key === 'screens' && screens) {\n        items.push(...getItemsFromScreens(Screen, screens));\n      }\n      if (key === 'groups' && groups) {\n        items.push(...Object.entries(groups).map(([key, {\n          if: useIf,\n          ...group\n        }]) => {\n          const groupItems = getItemsFromScreens(Screen, group.screens);\n          return () => {\n            // Call unconditionally since screen configs may contain `useIf` hooks\n            const children = groupItems.map(item => item());\n            const shouldRender = useIf == null || useIf();\n            if (!shouldRender) {\n              return null;\n            }\n            return /*#__PURE__*/(0, _jsxRuntime.jsx)(Group, {\n              navigationKey: key,\n              ...group,\n              children: children\n            }, key);\n          };\n        }));\n      }\n    }\n    const NavigatorComponent = () => {\n      const children = items.map(item => item());\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(Navigator, {\n        ...rest,\n        children: children\n      });\n    };\n    NavigatorComponent.displayName = displayName;\n    return NavigatorComponent;\n  }\n  /**\n   * Create a path config object from a static navigation config for deep linking.\n   *\n   * @param tree Static navigation config.\n   * @param options Additional options from `linking.config`.\n   * @param auto Whether to automatically generate paths for leaf screens.\n   * @returns Path config object to use in linking config.\n   *\n   * @example\n   * ```js\n   * const config = {\n   *   screens: {\n   *     Home: {\n   *       screens: createPathConfigForStaticNavigation(HomeTabs),\n   *     },\n   *   },\n   * };\n   * ```\n   */\n  function createPathConfigForStaticNavigation(tree, options, auto) {\n    let initialScreenConfig;\n    const createPathConfigForTree = (t, o, skipInitialDetection) => {\n      const createPathConfigForScreens = (screens, initialRouteName) => {\n        return Object.fromEntries(Object.entries(screens)\n        // Re-order to move the initial route to the front\n        // This way we can detect the initial route correctly\n        .sort(([a], [b]) => {\n          if (a === initialRouteName) {\n            return -1;\n          }\n          if (b === initialRouteName) {\n            return 1;\n          }\n          return 0;\n        }).map(([key, item]) => {\n          const screenConfig = {};\n          if ('linking' in item) {\n            if (typeof item.linking === 'string') {\n              screenConfig.path = item.linking;\n            } else {\n              Object.assign(screenConfig, item.linking);\n            }\n            if (typeof screenConfig.path === 'string') {\n              screenConfig.path = screenConfig.path.replace(/^\\//, '') // Remove extra leading slash\n              .replace(/\\/$/, ''); // Remove extra trailing slash\n            }\n          }\n          let screens;\n          const skipInitialDetectionInChild = skipInitialDetection || screenConfig.path != null && screenConfig.path !== '';\n          if ('config' in item) {\n            screens = createPathConfigForTree(item, undefined, skipInitialDetectionInChild);\n          } else if ('screen' in item && 'config' in item.screen && (item.screen.config.screens || item.screen.config.groups)) {\n            screens = createPathConfigForTree(item.screen, undefined, skipInitialDetectionInChild);\n          }\n          if (screens) {\n            screenConfig.screens = screens;\n          }\n          if (auto && !screenConfig.screens &&\n          // Skip generating path for screens that specify linking config as `undefined` or `null` explicitly\n          !('linking' in item && item.linking == null)) {\n            if (screenConfig.path != null) {\n              if (!skipInitialDetection && screenConfig.path === '') {\n                // We encounter a leaf screen with empty path,\n                // Clear the initial screen config as it's not needed anymore\n                initialScreenConfig = undefined;\n              }\n            } else {\n              if (!skipInitialDetection && initialScreenConfig == null) {\n                initialScreenConfig = screenConfig;\n              }\n              screenConfig.path = key.replace(/([A-Z]+)/g, '-$1').replace(/^-/, '').toLowerCase();\n            }\n          }\n          return [key, screenConfig];\n        }).filter(([, screen]) => Object.keys(screen).length > 0));\n      };\n      const screens = {};\n\n      // Loop through the config to find screens and groups\n      // So we add the screens and groups in the same order as they are defined\n      for (const key in t.config) {\n        if (key === 'screens' && t.config.screens) {\n          Object.assign(screens, createPathConfigForScreens(t.config.screens, o?.initialRouteName ?? t.config.initialRouteName));\n        }\n        if (key === 'groups' && t.config.groups) {\n          Object.entries(t.config.groups).forEach(([, group]) => {\n            Object.assign(screens, createPathConfigForScreens(group.screens, o?.initialRouteName ?? t.config.initialRouteName));\n          });\n        }\n      }\n      if (Object.keys(screens).length === 0) {\n        return undefined;\n      }\n      return screens;\n    };\n    const screens = createPathConfigForTree(tree, options, false);\n    if (auto && initialScreenConfig) {\n      initialScreenConfig.path = '';\n    }\n    return screens;\n  }\n});", "lineCount": 258, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createComponentForStaticNavigation"], [7, 44, 1, 13], [7, 47, 1, 13, "createComponentForStaticNavigation"], [7, 81, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createPathConfigForStaticNavigation"], [8, 45, 1, 13], [8, 48, 1, 13, "createPathConfigForStaticNavigation"], [8, 83, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_ReactNativeCSSInterop"], [9, 28, 1, 13], [9, 31, 1, 13, "_interopRequireWildcard"], [9, 54, 1, 13], [9, 55, 1, 13, "require"], [9, 62, 1, 13], [9, 63, 1, 13, "_dependencyMap"], [9, 77, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_reactIs"], [11, 14, 4, 0], [11, 17, 4, 0, "require"], [11, 24, 4, 0], [11, 25, 4, 0, "_dependencyMap"], [11, 39, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_useRoute"], [12, 15, 5, 0], [12, 18, 5, 0, "require"], [12, 25, 5, 0], [12, 26, 5, 0, "_dependencyMap"], [12, 40, 5, 0], [13, 2, 30, 0], [13, 6, 30, 0, "_jsxRuntime"], [13, 17, 30, 0], [13, 20, 30, 0, "require"], [13, 27, 30, 0], [13, 28, 30, 0, "_dependencyMap"], [13, 42, 30, 0], [14, 2, 30, 48], [14, 11, 30, 48, "_interopRequireWildcard"], [14, 35, 30, 48, "e"], [14, 36, 30, 48], [14, 38, 30, 48, "t"], [14, 39, 30, 48], [14, 68, 30, 48, "WeakMap"], [14, 75, 30, 48], [14, 81, 30, 48, "r"], [14, 82, 30, 48], [14, 89, 30, 48, "WeakMap"], [14, 96, 30, 48], [14, 100, 30, 48, "n"], [14, 101, 30, 48], [14, 108, 30, 48, "WeakMap"], [14, 115, 30, 48], [14, 127, 30, 48, "_interopRequireWildcard"], [14, 150, 30, 48], [14, 162, 30, 48, "_interopRequireWildcard"], [14, 163, 30, 48, "e"], [14, 164, 30, 48], [14, 166, 30, 48, "t"], [14, 167, 30, 48], [14, 176, 30, 48, "t"], [14, 177, 30, 48], [14, 181, 30, 48, "e"], [14, 182, 30, 48], [14, 186, 30, 48, "e"], [14, 187, 30, 48], [14, 188, 30, 48, "__esModule"], [14, 198, 30, 48], [14, 207, 30, 48, "e"], [14, 208, 30, 48], [14, 214, 30, 48, "o"], [14, 215, 30, 48], [14, 217, 30, 48, "i"], [14, 218, 30, 48], [14, 220, 30, 48, "f"], [14, 221, 30, 48], [14, 226, 30, 48, "__proto__"], [14, 235, 30, 48], [14, 243, 30, 48, "default"], [14, 250, 30, 48], [14, 252, 30, 48, "e"], [14, 253, 30, 48], [14, 270, 30, 48, "e"], [14, 271, 30, 48], [14, 294, 30, 48, "e"], [14, 295, 30, 48], [14, 320, 30, 48, "e"], [14, 321, 30, 48], [14, 330, 30, 48, "f"], [14, 331, 30, 48], [14, 337, 30, 48, "o"], [14, 338, 30, 48], [14, 341, 30, 48, "t"], [14, 342, 30, 48], [14, 345, 30, 48, "n"], [14, 346, 30, 48], [14, 349, 30, 48, "r"], [14, 350, 30, 48], [14, 358, 30, 48, "o"], [14, 359, 30, 48], [14, 360, 30, 48, "has"], [14, 363, 30, 48], [14, 364, 30, 48, "e"], [14, 365, 30, 48], [14, 375, 30, 48, "o"], [14, 376, 30, 48], [14, 377, 30, 48, "get"], [14, 380, 30, 48], [14, 381, 30, 48, "e"], [14, 382, 30, 48], [14, 385, 30, 48, "o"], [14, 386, 30, 48], [14, 387, 30, 48, "set"], [14, 390, 30, 48], [14, 391, 30, 48, "e"], [14, 392, 30, 48], [14, 394, 30, 48, "f"], [14, 395, 30, 48], [14, 411, 30, 48, "t"], [14, 412, 30, 48], [14, 416, 30, 48, "e"], [14, 417, 30, 48], [14, 433, 30, 48, "t"], [14, 434, 30, 48], [14, 441, 30, 48, "hasOwnProperty"], [14, 455, 30, 48], [14, 456, 30, 48, "call"], [14, 460, 30, 48], [14, 461, 30, 48, "e"], [14, 462, 30, 48], [14, 464, 30, 48, "t"], [14, 465, 30, 48], [14, 472, 30, 48, "i"], [14, 473, 30, 48], [14, 477, 30, 48, "o"], [14, 478, 30, 48], [14, 481, 30, 48, "Object"], [14, 487, 30, 48], [14, 488, 30, 48, "defineProperty"], [14, 502, 30, 48], [14, 507, 30, 48, "Object"], [14, 513, 30, 48], [14, 514, 30, 48, "getOwnPropertyDescriptor"], [14, 538, 30, 48], [14, 539, 30, 48, "e"], [14, 540, 30, 48], [14, 542, 30, 48, "t"], [14, 543, 30, 48], [14, 550, 30, 48, "i"], [14, 551, 30, 48], [14, 552, 30, 48, "get"], [14, 555, 30, 48], [14, 559, 30, 48, "i"], [14, 560, 30, 48], [14, 561, 30, 48, "set"], [14, 564, 30, 48], [14, 568, 30, 48, "o"], [14, 569, 30, 48], [14, 570, 30, 48, "f"], [14, 571, 30, 48], [14, 573, 30, 48, "t"], [14, 574, 30, 48], [14, 576, 30, 48, "i"], [14, 577, 30, 48], [14, 581, 30, 48, "f"], [14, 582, 30, 48], [14, 583, 30, 48, "t"], [14, 584, 30, 48], [14, 588, 30, 48, "e"], [14, 589, 30, 48], [14, 590, 30, 48, "t"], [14, 591, 30, 48], [14, 602, 30, 48, "f"], [14, 603, 30, 48], [14, 608, 30, 48, "e"], [14, 609, 30, 48], [14, 611, 30, 48, "t"], [14, 612, 30, 48], [15, 2, 7, 0], [16, 0, 8, 0], [17, 0, 9, 0], [18, 0, 10, 0], [20, 2, 12, 0], [21, 0, 13, 0], [22, 0, 14, 0], [23, 0, 15, 0], [25, 2, 17, 0], [26, 0, 18, 0], [27, 0, 19, 0], [28, 0, 20, 0], [30, 2, 22, 0], [31, 0, 23, 0], [32, 0, 24, 0], [33, 0, 25, 0], [35, 2, 27, 0], [36, 0, 28, 0], [37, 0, 29, 0], [39, 2, 31, 0], [39, 8, 31, 6, "MemoizedScreen"], [39, 22, 31, 20], [39, 25, 31, 23], [39, 38, 31, 36, "React"], [39, 43, 31, 41], [39, 44, 31, 42, "memo"], [39, 48, 31, 46], [39, 49, 31, 47], [39, 50, 31, 48], [40, 4, 32, 2, "component"], [41, 2, 33, 0], [41, 3, 33, 1], [41, 8, 33, 6], [42, 4, 34, 2], [42, 10, 34, 8, "route"], [42, 15, 34, 13], [42, 18, 34, 16], [42, 22, 34, 16, "useRoute"], [42, 40, 34, 24], [42, 42, 34, 25], [42, 43, 34, 26], [43, 4, 35, 2], [43, 10, 35, 8, "children"], [43, 18, 35, 16], [43, 21, 35, 19], [43, 34, 35, 32, "_ReactNativeCSSInterop"], [43, 56, 35, 32], [43, 57, 35, 32, "createInteropElement"], [43, 77, 35, 32], [43, 78, 35, 52, "component"], [43, 87, 35, 61], [43, 89, 35, 63], [44, 6, 36, 4, "route"], [45, 4, 37, 2], [45, 5, 37, 3], [45, 6, 37, 4], [46, 4, 38, 2], [46, 11, 38, 9, "children"], [46, 19, 38, 17], [47, 2, 39, 0], [47, 3, 39, 1], [47, 4, 39, 2], [48, 2, 40, 0, "MemoizedScreen"], [48, 16, 40, 14], [48, 17, 40, 15, "displayName"], [48, 28, 40, 26], [48, 31, 40, 29], [48, 45, 40, 43], [49, 2, 41, 0], [49, 8, 41, 6, "getItemsFromScreens"], [49, 27, 41, 25], [49, 30, 41, 28, "getItemsFromScreens"], [49, 31, 41, 29, "Screen"], [49, 37, 41, 35], [49, 39, 41, 37, "screens"], [49, 46, 41, 44], [49, 51, 41, 49], [50, 4, 42, 2], [50, 11, 42, 9, "Object"], [50, 17, 42, 15], [50, 18, 42, 16, "entries"], [50, 25, 42, 23], [50, 26, 42, 24, "screens"], [50, 33, 42, 31], [50, 34, 42, 32], [50, 35, 42, 33, "map"], [50, 38, 42, 36], [50, 39, 42, 37], [50, 40, 42, 38], [50, 41, 42, 39, "name"], [50, 45, 42, 43], [50, 47, 42, 45, "item"], [50, 51, 42, 49], [50, 52, 42, 50], [50, 57, 42, 55], [51, 6, 43, 4], [51, 10, 43, 8, "component"], [51, 19, 43, 17], [52, 6, 44, 4], [52, 10, 44, 8, "props"], [52, 15, 44, 13], [52, 18, 44, 16], [52, 19, 44, 17], [52, 20, 44, 18], [53, 6, 45, 4], [53, 10, 45, 8, "useIf"], [53, 15, 45, 13], [54, 6, 46, 4], [54, 10, 46, 8, "isNavigator"], [54, 21, 46, 19], [54, 24, 46, 22], [54, 29, 46, 27], [55, 6, 47, 4], [55, 10, 47, 8], [55, 18, 47, 16], [55, 22, 47, 20, "item"], [55, 26, 47, 24], [55, 28, 47, 26], [56, 8, 48, 6], [56, 14, 48, 12], [57, 10, 49, 8, "screen"], [57, 16, 49, 14], [58, 10, 50, 8, "if"], [58, 12, 50, 10], [58, 14, 50, 12, "_if"], [58, 17, 50, 15], [59, 10, 51, 8], [59, 13, 51, 11, "rest"], [60, 8, 52, 6], [60, 9, 52, 7], [60, 12, 52, 10, "item"], [60, 16, 52, 14], [61, 8, 53, 6, "useIf"], [61, 13, 53, 11], [61, 16, 53, 14, "_if"], [61, 19, 53, 17], [62, 8, 54, 6, "props"], [62, 13, 54, 11], [62, 16, 54, 14, "rest"], [62, 20, 54, 18], [63, 8, 55, 6], [63, 12, 55, 10], [63, 16, 55, 10, "isValidElementType"], [63, 43, 55, 28], [63, 45, 55, 29, "screen"], [63, 51, 55, 35], [63, 52, 55, 36], [63, 54, 55, 38], [64, 10, 56, 8, "component"], [64, 19, 56, 17], [64, 22, 56, 20, "screen"], [64, 28, 56, 26], [65, 8, 57, 6], [65, 9, 57, 7], [65, 15, 57, 13], [65, 19, 57, 17], [65, 27, 57, 25], [65, 31, 57, 29, "screen"], [65, 37, 57, 35], [65, 39, 57, 37], [66, 10, 58, 8, "isNavigator"], [66, 21, 58, 19], [66, 24, 58, 22], [66, 28, 58, 26], [67, 10, 59, 8, "component"], [67, 19, 59, 17], [67, 22, 59, 20, "createComponentForStaticNavigation"], [67, 56, 59, 54], [67, 57, 59, 55, "screen"], [67, 63, 59, 61], [67, 65, 59, 63], [67, 68, 59, 66, "name"], [67, 72, 59, 70], [67, 83, 59, 81], [67, 84, 59, 82], [68, 8, 60, 6], [69, 6, 61, 4], [69, 7, 61, 5], [69, 13, 61, 11], [69, 17, 61, 15], [69, 21, 61, 15, "isValidElementType"], [69, 48, 61, 33], [69, 50, 61, 34, "item"], [69, 54, 61, 38], [69, 55, 61, 39], [69, 57, 61, 41], [70, 8, 62, 6, "component"], [70, 17, 62, 15], [70, 20, 62, 18, "item"], [70, 24, 62, 22], [71, 6, 63, 4], [71, 7, 63, 5], [71, 13, 63, 11], [71, 17, 63, 15], [71, 25, 63, 23], [71, 29, 63, 27, "item"], [71, 33, 63, 31], [71, 35, 63, 33], [72, 8, 64, 6, "isNavigator"], [72, 19, 64, 17], [72, 22, 64, 20], [72, 26, 64, 24], [73, 8, 65, 6, "component"], [73, 17, 65, 15], [73, 20, 65, 18, "createComponentForStaticNavigation"], [73, 54, 65, 52], [73, 55, 65, 53, "item"], [73, 59, 65, 57], [73, 61, 65, 59], [73, 64, 65, 62, "name"], [73, 68, 65, 66], [73, 79, 65, 77], [73, 80, 65, 78], [74, 6, 66, 4], [75, 6, 67, 4], [75, 10, 67, 8, "component"], [75, 19, 67, 17], [75, 23, 67, 21], [75, 27, 67, 25], [75, 29, 67, 27], [76, 8, 68, 6], [76, 14, 68, 12], [76, 18, 68, 16, "Error"], [76, 23, 68, 21], [76, 24, 68, 22], [76, 77, 68, 75, "name"], [76, 81, 68, 79], [76, 262, 68, 260], [76, 263, 68, 261], [77, 6, 69, 4], [78, 6, 70, 4], [78, 12, 70, 10, "element"], [78, 19, 70, 17], [78, 22, 70, 20, "isNavigator"], [78, 33, 70, 31], [78, 37, 70, 35], [78, 50, 70, 48, "_ReactNativeCSSInterop"], [78, 72, 70, 48], [78, 73, 70, 48, "createInteropElement"], [78, 93, 70, 48], [78, 94, 70, 68, "component"], [78, 103, 70, 77], [78, 105, 70, 79], [78, 106, 70, 80], [78, 107, 70, 81], [78, 108, 70, 82], [78, 112, 70, 86], [78, 125, 70, 99], [78, 129, 70, 99, "_jsx"], [78, 144, 70, 103], [78, 146, 70, 104, "MemoizedScreen"], [78, 160, 70, 118], [78, 162, 70, 120], [79, 8, 71, 6, "component"], [79, 17, 71, 15], [79, 19, 71, 17, "component"], [80, 6, 72, 4], [80, 7, 72, 5], [80, 8, 72, 6], [81, 6, 73, 4], [81, 13, 73, 11], [81, 19, 73, 17], [82, 8, 74, 6], [82, 14, 74, 12, "shouldRender"], [82, 26, 74, 24], [82, 29, 74, 27, "useIf"], [82, 34, 74, 32], [82, 38, 74, 36], [82, 42, 74, 40], [82, 46, 74, 44, "useIf"], [82, 51, 74, 49], [82, 52, 74, 50], [82, 53, 74, 51], [83, 8, 75, 6], [83, 12, 75, 10], [83, 13, 75, 11, "shouldRender"], [83, 25, 75, 23], [83, 27, 75, 25], [84, 10, 76, 8], [84, 17, 76, 15], [84, 21, 76, 19], [85, 8, 77, 6], [86, 8, 78, 6], [86, 15, 78, 13], [86, 28, 78, 26], [86, 32, 78, 26, "_jsx"], [86, 47, 78, 30], [86, 49, 78, 31, "Screen"], [86, 55, 78, 37], [86, 57, 78, 39], [87, 10, 79, 8, "name"], [87, 14, 79, 12], [87, 16, 79, 14, "name"], [87, 20, 79, 18], [88, 10, 80, 8], [88, 13, 80, 11, "props"], [88, 18, 80, 16], [89, 10, 81, 8, "children"], [89, 18, 81, 16], [89, 20, 81, 18, "children"], [89, 21, 81, 18], [89, 26, 81, 24, "element"], [90, 8, 82, 6], [90, 9, 82, 7], [90, 11, 82, 9, "name"], [90, 15, 82, 13], [90, 16, 82, 14], [91, 6, 83, 4], [91, 7, 83, 5], [92, 4, 84, 2], [92, 5, 84, 3], [92, 6, 84, 4], [93, 2, 85, 0], [93, 3, 85, 1], [95, 2, 87, 0], [96, 0, 88, 0], [97, 0, 89, 0], [98, 0, 90, 0], [99, 0, 91, 0], [100, 0, 92, 0], [101, 0, 93, 0], [102, 2, 94, 7], [102, 11, 94, 16, "createComponentForStaticNavigation"], [102, 45, 94, 50, "createComponentForStaticNavigation"], [102, 46, 94, 51, "tree"], [102, 50, 94, 55], [102, 52, 94, 57, "displayName"], [102, 63, 94, 68], [102, 65, 94, 70], [103, 4, 95, 2], [103, 10, 95, 8], [104, 6, 96, 4, "Navigator"], [104, 15, 96, 13], [105, 6, 97, 4, "Group"], [105, 11, 97, 9], [106, 6, 98, 4, "Screen"], [106, 12, 98, 10], [107, 6, 99, 4, "config"], [108, 4, 100, 2], [108, 5, 100, 3], [108, 8, 100, 6, "tree"], [108, 12, 100, 10], [109, 4, 101, 2], [109, 10, 101, 8], [110, 6, 102, 4, "screens"], [110, 13, 102, 11], [111, 6, 103, 4, "groups"], [111, 12, 103, 10], [112, 6, 104, 4], [112, 9, 104, 7, "rest"], [113, 4, 105, 2], [113, 5, 105, 3], [113, 8, 105, 6, "config"], [113, 14, 105, 12], [114, 4, 106, 2], [114, 8, 106, 6, "screens"], [114, 15, 106, 13], [114, 19, 106, 17], [114, 23, 106, 21], [114, 27, 106, 25, "groups"], [114, 33, 106, 31], [114, 37, 106, 35], [114, 41, 106, 39], [114, 43, 106, 41], [115, 6, 107, 4], [115, 12, 107, 10], [115, 16, 107, 14, "Error"], [115, 21, 107, 19], [115, 22, 107, 20], [115, 153, 107, 151], [115, 154, 107, 152], [116, 4, 108, 2], [117, 4, 109, 2], [117, 10, 109, 8, "items"], [117, 15, 109, 13], [117, 18, 109, 16], [117, 20, 109, 18], [119, 4, 111, 2], [120, 4, 112, 2], [121, 4, 113, 2], [121, 9, 113, 7], [121, 15, 113, 13, "key"], [121, 18, 113, 16], [121, 22, 113, 20, "config"], [121, 28, 113, 26], [121, 30, 113, 28], [122, 6, 114, 4], [122, 10, 114, 8, "key"], [122, 13, 114, 11], [122, 18, 114, 16], [122, 27, 114, 25], [122, 31, 114, 29, "screens"], [122, 38, 114, 36], [122, 40, 114, 38], [123, 8, 115, 6, "items"], [123, 13, 115, 11], [123, 14, 115, 12, "push"], [123, 18, 115, 16], [123, 19, 115, 17], [123, 22, 115, 20, "getItemsFromScreens"], [123, 41, 115, 39], [123, 42, 115, 40, "Screen"], [123, 48, 115, 46], [123, 50, 115, 48, "screens"], [123, 57, 115, 55], [123, 58, 115, 56], [123, 59, 115, 57], [124, 6, 116, 4], [125, 6, 117, 4], [125, 10, 117, 8, "key"], [125, 13, 117, 11], [125, 18, 117, 16], [125, 26, 117, 24], [125, 30, 117, 28, "groups"], [125, 36, 117, 34], [125, 38, 117, 36], [126, 8, 118, 6, "items"], [126, 13, 118, 11], [126, 14, 118, 12, "push"], [126, 18, 118, 16], [126, 19, 118, 17], [126, 22, 118, 20, "Object"], [126, 28, 118, 26], [126, 29, 118, 27, "entries"], [126, 36, 118, 34], [126, 37, 118, 35, "groups"], [126, 43, 118, 41], [126, 44, 118, 42], [126, 45, 118, 43, "map"], [126, 48, 118, 46], [126, 49, 118, 47], [126, 50, 118, 48], [126, 51, 118, 49, "key"], [126, 54, 118, 52], [126, 56, 118, 54], [127, 10, 119, 8, "if"], [127, 12, 119, 10], [127, 14, 119, 12, "useIf"], [127, 19, 119, 17], [128, 10, 120, 8], [128, 13, 120, 11, "group"], [129, 8, 121, 6], [129, 9, 121, 7], [129, 10, 121, 8], [129, 15, 121, 13], [130, 10, 122, 8], [130, 16, 122, 14, "groupItems"], [130, 26, 122, 24], [130, 29, 122, 27, "getItemsFromScreens"], [130, 48, 122, 46], [130, 49, 122, 47, "Screen"], [130, 55, 122, 53], [130, 57, 122, 55, "group"], [130, 62, 122, 60], [130, 63, 122, 61, "screens"], [130, 70, 122, 68], [130, 71, 122, 69], [131, 10, 123, 8], [131, 17, 123, 15], [131, 23, 123, 21], [132, 12, 124, 10], [133, 12, 125, 10], [133, 18, 125, 16, "children"], [133, 26, 125, 24], [133, 29, 125, 27, "groupItems"], [133, 39, 125, 37], [133, 40, 125, 38, "map"], [133, 43, 125, 41], [133, 44, 125, 42, "item"], [133, 48, 125, 46], [133, 52, 125, 50, "item"], [133, 56, 125, 54], [133, 57, 125, 55], [133, 58, 125, 56], [133, 59, 125, 57], [134, 12, 126, 10], [134, 18, 126, 16, "shouldRender"], [134, 30, 126, 28], [134, 33, 126, 31, "useIf"], [134, 38, 126, 36], [134, 42, 126, 40], [134, 46, 126, 44], [134, 50, 126, 48, "useIf"], [134, 55, 126, 53], [134, 56, 126, 54], [134, 57, 126, 55], [135, 12, 127, 10], [135, 16, 127, 14], [135, 17, 127, 15, "shouldRender"], [135, 29, 127, 27], [135, 31, 127, 29], [136, 14, 128, 12], [136, 21, 128, 19], [136, 25, 128, 23], [137, 12, 129, 10], [138, 12, 130, 10], [138, 19, 130, 17], [138, 32, 130, 30], [138, 36, 130, 30, "_jsx"], [138, 51, 130, 34], [138, 53, 130, 35, "Group"], [138, 58, 130, 40], [138, 60, 130, 42], [139, 14, 131, 12, "navigationKey"], [139, 27, 131, 25], [139, 29, 131, 27, "key"], [139, 32, 131, 30], [140, 14, 132, 12], [140, 17, 132, 15, "group"], [140, 22, 132, 20], [141, 14, 133, 12, "children"], [141, 22, 133, 20], [141, 24, 133, 22, "children"], [142, 12, 134, 10], [142, 13, 134, 11], [142, 15, 134, 13, "key"], [142, 18, 134, 16], [142, 19, 134, 17], [143, 10, 135, 8], [143, 11, 135, 9], [144, 8, 136, 6], [144, 9, 136, 7], [144, 10, 136, 8], [144, 11, 136, 9], [145, 6, 137, 4], [146, 4, 138, 2], [147, 4, 139, 2], [147, 10, 139, 8, "NavigatorComponent"], [147, 28, 139, 26], [147, 31, 139, 29, "NavigatorComponent"], [147, 32, 139, 29], [147, 37, 139, 35], [148, 6, 140, 4], [148, 12, 140, 10, "children"], [148, 20, 140, 18], [148, 23, 140, 21, "items"], [148, 28, 140, 26], [148, 29, 140, 27, "map"], [148, 32, 140, 30], [148, 33, 140, 31, "item"], [148, 37, 140, 35], [148, 41, 140, 39, "item"], [148, 45, 140, 43], [148, 46, 140, 44], [148, 47, 140, 45], [148, 48, 140, 46], [149, 6, 141, 4], [149, 13, 141, 11], [149, 26, 141, 24], [149, 30, 141, 24, "_jsx"], [149, 45, 141, 28], [149, 47, 141, 29, "Navigator"], [149, 56, 141, 38], [149, 58, 141, 40], [150, 8, 142, 6], [150, 11, 142, 9, "rest"], [150, 15, 142, 13], [151, 8, 143, 6, "children"], [151, 16, 143, 14], [151, 18, 143, 16, "children"], [152, 6, 144, 4], [152, 7, 144, 5], [152, 8, 144, 6], [153, 4, 145, 2], [153, 5, 145, 3], [154, 4, 146, 2, "NavigatorComponent"], [154, 22, 146, 20], [154, 23, 146, 21, "displayName"], [154, 34, 146, 32], [154, 37, 146, 35, "displayName"], [154, 48, 146, 46], [155, 4, 147, 2], [155, 11, 147, 9, "NavigatorComponent"], [155, 29, 147, 27], [156, 2, 148, 0], [157, 2, 149, 0], [158, 0, 150, 0], [159, 0, 151, 0], [160, 0, 152, 0], [161, 0, 153, 0], [162, 0, 154, 0], [163, 0, 155, 0], [164, 0, 156, 0], [165, 0, 157, 0], [166, 0, 158, 0], [167, 0, 159, 0], [168, 0, 160, 0], [169, 0, 161, 0], [170, 0, 162, 0], [171, 0, 163, 0], [172, 0, 164, 0], [173, 0, 165, 0], [174, 0, 166, 0], [175, 0, 167, 0], [176, 2, 168, 7], [176, 11, 168, 16, "createPathConfigForStaticNavigation"], [176, 46, 168, 51, "createPathConfigForStaticNavigation"], [176, 47, 168, 52, "tree"], [176, 51, 168, 56], [176, 53, 168, 58, "options"], [176, 60, 168, 65], [176, 62, 168, 67, "auto"], [176, 66, 168, 71], [176, 68, 168, 73], [177, 4, 169, 2], [177, 8, 169, 6, "initialScreenConfig"], [177, 27, 169, 25], [178, 4, 170, 2], [178, 10, 170, 8, "createPathConfigForTree"], [178, 33, 170, 31], [178, 36, 170, 34, "createPathConfigForTree"], [178, 37, 170, 35, "t"], [178, 38, 170, 36], [178, 40, 170, 38, "o"], [178, 41, 170, 39], [178, 43, 170, 41, "skipInitialDetection"], [178, 63, 170, 61], [178, 68, 170, 66], [179, 6, 171, 4], [179, 12, 171, 10, "createPathConfigForScreens"], [179, 38, 171, 36], [179, 41, 171, 39, "createPathConfigForScreens"], [179, 42, 171, 40, "screens"], [179, 49, 171, 47], [179, 51, 171, 49, "initialRouteName"], [179, 67, 171, 65], [179, 72, 171, 70], [180, 8, 172, 6], [180, 15, 172, 13, "Object"], [180, 21, 172, 19], [180, 22, 172, 20, "fromEntries"], [180, 33, 172, 31], [180, 34, 172, 32, "Object"], [180, 40, 172, 38], [180, 41, 172, 39, "entries"], [180, 48, 172, 46], [180, 49, 172, 47, "screens"], [180, 56, 172, 54], [181, 8, 173, 6], [182, 8, 174, 6], [183, 8, 174, 6], [183, 9, 175, 7, "sort"], [183, 13, 175, 11], [183, 14, 175, 12], [183, 15, 175, 13], [183, 16, 175, 14, "a"], [183, 17, 175, 15], [183, 18, 175, 16], [183, 20, 175, 18], [183, 21, 175, 19, "b"], [183, 22, 175, 20], [183, 23, 175, 21], [183, 28, 175, 26], [184, 10, 176, 8], [184, 14, 176, 12, "a"], [184, 15, 176, 13], [184, 20, 176, 18, "initialRouteName"], [184, 36, 176, 34], [184, 38, 176, 36], [185, 12, 177, 10], [185, 19, 177, 17], [185, 20, 177, 18], [185, 21, 177, 19], [186, 10, 178, 8], [187, 10, 179, 8], [187, 14, 179, 12, "b"], [187, 15, 179, 13], [187, 20, 179, 18, "initialRouteName"], [187, 36, 179, 34], [187, 38, 179, 36], [188, 12, 180, 10], [188, 19, 180, 17], [188, 20, 180, 18], [189, 10, 181, 8], [190, 10, 182, 8], [190, 17, 182, 15], [190, 18, 182, 16], [191, 8, 183, 6], [191, 9, 183, 7], [191, 10, 183, 8], [191, 11, 183, 9, "map"], [191, 14, 183, 12], [191, 15, 183, 13], [191, 16, 183, 14], [191, 17, 183, 15, "key"], [191, 20, 183, 18], [191, 22, 183, 20, "item"], [191, 26, 183, 24], [191, 27, 183, 25], [191, 32, 183, 30], [192, 10, 184, 8], [192, 16, 184, 14, "screenConfig"], [192, 28, 184, 26], [192, 31, 184, 29], [192, 32, 184, 30], [192, 33, 184, 31], [193, 10, 185, 8], [193, 14, 185, 12], [193, 23, 185, 21], [193, 27, 185, 25, "item"], [193, 31, 185, 29], [193, 33, 185, 31], [194, 12, 186, 10], [194, 16, 186, 14], [194, 23, 186, 21, "item"], [194, 27, 186, 25], [194, 28, 186, 26, "linking"], [194, 35, 186, 33], [194, 40, 186, 38], [194, 48, 186, 46], [194, 50, 186, 48], [195, 14, 187, 12, "screenConfig"], [195, 26, 187, 24], [195, 27, 187, 25, "path"], [195, 31, 187, 29], [195, 34, 187, 32, "item"], [195, 38, 187, 36], [195, 39, 187, 37, "linking"], [195, 46, 187, 44], [196, 12, 188, 10], [196, 13, 188, 11], [196, 19, 188, 17], [197, 14, 189, 12, "Object"], [197, 20, 189, 18], [197, 21, 189, 19, "assign"], [197, 27, 189, 25], [197, 28, 189, 26, "screenConfig"], [197, 40, 189, 38], [197, 42, 189, 40, "item"], [197, 46, 189, 44], [197, 47, 189, 45, "linking"], [197, 54, 189, 52], [197, 55, 189, 53], [198, 12, 190, 10], [199, 12, 191, 10], [199, 16, 191, 14], [199, 23, 191, 21, "screenConfig"], [199, 35, 191, 33], [199, 36, 191, 34, "path"], [199, 40, 191, 38], [199, 45, 191, 43], [199, 53, 191, 51], [199, 55, 191, 53], [200, 14, 192, 12, "screenConfig"], [200, 26, 192, 24], [200, 27, 192, 25, "path"], [200, 31, 192, 29], [200, 34, 192, 32, "screenConfig"], [200, 46, 192, 44], [200, 47, 192, 45, "path"], [200, 51, 192, 49], [200, 52, 192, 50, "replace"], [200, 59, 192, 57], [200, 60, 192, 58], [200, 65, 192, 63], [200, 67, 192, 65], [200, 69, 192, 67], [200, 70, 192, 68], [200, 71, 192, 69], [201, 14, 192, 69], [201, 15, 193, 13, "replace"], [201, 22, 193, 20], [201, 23, 193, 21], [201, 28, 193, 26], [201, 30, 193, 28], [201, 32, 193, 30], [201, 33, 193, 31], [201, 34, 193, 32], [201, 35, 193, 33], [202, 12, 194, 10], [203, 10, 195, 8], [204, 10, 196, 8], [204, 14, 196, 12, "screens"], [204, 21, 196, 19], [205, 10, 197, 8], [205, 16, 197, 14, "skipInitialDetectionInChild"], [205, 43, 197, 41], [205, 46, 197, 44, "skipInitialDetection"], [205, 66, 197, 64], [205, 70, 197, 68, "screenConfig"], [205, 82, 197, 80], [205, 83, 197, 81, "path"], [205, 87, 197, 85], [205, 91, 197, 89], [205, 95, 197, 93], [205, 99, 197, 97, "screenConfig"], [205, 111, 197, 109], [205, 112, 197, 110, "path"], [205, 116, 197, 114], [205, 121, 197, 119], [205, 123, 197, 121], [206, 10, 198, 8], [206, 14, 198, 12], [206, 22, 198, 20], [206, 26, 198, 24, "item"], [206, 30, 198, 28], [206, 32, 198, 30], [207, 12, 199, 10, "screens"], [207, 19, 199, 17], [207, 22, 199, 20, "createPathConfigForTree"], [207, 45, 199, 43], [207, 46, 199, 44, "item"], [207, 50, 199, 48], [207, 52, 199, 50, "undefined"], [207, 61, 199, 59], [207, 63, 199, 61, "skipInitialDetectionInChild"], [207, 90, 199, 88], [207, 91, 199, 89], [208, 10, 200, 8], [208, 11, 200, 9], [208, 17, 200, 15], [208, 21, 200, 19], [208, 29, 200, 27], [208, 33, 200, 31, "item"], [208, 37, 200, 35], [208, 41, 200, 39], [208, 49, 200, 47], [208, 53, 200, 51, "item"], [208, 57, 200, 55], [208, 58, 200, 56, "screen"], [208, 64, 200, 62], [208, 69, 200, 67, "item"], [208, 73, 200, 71], [208, 74, 200, 72, "screen"], [208, 80, 200, 78], [208, 81, 200, 79, "config"], [208, 87, 200, 85], [208, 88, 200, 86, "screens"], [208, 95, 200, 93], [208, 99, 200, 97, "item"], [208, 103, 200, 101], [208, 104, 200, 102, "screen"], [208, 110, 200, 108], [208, 111, 200, 109, "config"], [208, 117, 200, 115], [208, 118, 200, 116, "groups"], [208, 124, 200, 122], [208, 125, 200, 123], [208, 127, 200, 125], [209, 12, 201, 10, "screens"], [209, 19, 201, 17], [209, 22, 201, 20, "createPathConfigForTree"], [209, 45, 201, 43], [209, 46, 201, 44, "item"], [209, 50, 201, 48], [209, 51, 201, 49, "screen"], [209, 57, 201, 55], [209, 59, 201, 57, "undefined"], [209, 68, 201, 66], [209, 70, 201, 68, "skipInitialDetectionInChild"], [209, 97, 201, 95], [209, 98, 201, 96], [210, 10, 202, 8], [211, 10, 203, 8], [211, 14, 203, 12, "screens"], [211, 21, 203, 19], [211, 23, 203, 21], [212, 12, 204, 10, "screenConfig"], [212, 24, 204, 22], [212, 25, 204, 23, "screens"], [212, 32, 204, 30], [212, 35, 204, 33, "screens"], [212, 42, 204, 40], [213, 10, 205, 8], [214, 10, 206, 8], [214, 14, 206, 12, "auto"], [214, 18, 206, 16], [214, 22, 206, 20], [214, 23, 206, 21, "screenConfig"], [214, 35, 206, 33], [214, 36, 206, 34, "screens"], [214, 43, 206, 41], [215, 10, 207, 8], [216, 10, 208, 8], [216, 12, 208, 10], [216, 21, 208, 19], [216, 25, 208, 23, "item"], [216, 29, 208, 27], [216, 33, 208, 31, "item"], [216, 37, 208, 35], [216, 38, 208, 36, "linking"], [216, 45, 208, 43], [216, 49, 208, 47], [216, 53, 208, 51], [216, 54, 208, 52], [216, 56, 208, 54], [217, 12, 209, 10], [217, 16, 209, 14, "screenConfig"], [217, 28, 209, 26], [217, 29, 209, 27, "path"], [217, 33, 209, 31], [217, 37, 209, 35], [217, 41, 209, 39], [217, 43, 209, 41], [218, 14, 210, 12], [218, 18, 210, 16], [218, 19, 210, 17, "skipInitialDetection"], [218, 39, 210, 37], [218, 43, 210, 41, "screenConfig"], [218, 55, 210, 53], [218, 56, 210, 54, "path"], [218, 60, 210, 58], [218, 65, 210, 63], [218, 67, 210, 65], [218, 69, 210, 67], [219, 16, 211, 14], [220, 16, 212, 14], [221, 16, 213, 14, "initialScreenConfig"], [221, 35, 213, 33], [221, 38, 213, 36, "undefined"], [221, 47, 213, 45], [222, 14, 214, 12], [223, 12, 215, 10], [223, 13, 215, 11], [223, 19, 215, 17], [224, 14, 216, 12], [224, 18, 216, 16], [224, 19, 216, 17, "skipInitialDetection"], [224, 39, 216, 37], [224, 43, 216, 41, "initialScreenConfig"], [224, 62, 216, 60], [224, 66, 216, 64], [224, 70, 216, 68], [224, 72, 216, 70], [225, 16, 217, 14, "initialScreenConfig"], [225, 35, 217, 33], [225, 38, 217, 36, "screenConfig"], [225, 50, 217, 48], [226, 14, 218, 12], [227, 14, 219, 12, "screenConfig"], [227, 26, 219, 24], [227, 27, 219, 25, "path"], [227, 31, 219, 29], [227, 34, 219, 32, "key"], [227, 37, 219, 35], [227, 38, 219, 36, "replace"], [227, 45, 219, 43], [227, 46, 219, 44], [227, 57, 219, 55], [227, 59, 219, 57], [227, 64, 219, 62], [227, 65, 219, 63], [227, 66, 219, 64, "replace"], [227, 73, 219, 71], [227, 74, 219, 72], [227, 78, 219, 76], [227, 80, 219, 78], [227, 82, 219, 80], [227, 83, 219, 81], [227, 84, 219, 82, "toLowerCase"], [227, 95, 219, 93], [227, 96, 219, 94], [227, 97, 219, 95], [228, 12, 220, 10], [229, 10, 221, 8], [230, 10, 222, 8], [230, 17, 222, 15], [230, 18, 222, 16, "key"], [230, 21, 222, 19], [230, 23, 222, 21, "screenConfig"], [230, 35, 222, 33], [230, 36, 222, 34], [231, 8, 223, 6], [231, 9, 223, 7], [231, 10, 223, 8], [231, 11, 223, 9, "filter"], [231, 17, 223, 15], [231, 18, 223, 16], [231, 19, 223, 17], [231, 22, 223, 20, "screen"], [231, 28, 223, 26], [231, 29, 223, 27], [231, 34, 223, 32, "Object"], [231, 40, 223, 38], [231, 41, 223, 39, "keys"], [231, 45, 223, 43], [231, 46, 223, 44, "screen"], [231, 52, 223, 50], [231, 53, 223, 51], [231, 54, 223, 52, "length"], [231, 60, 223, 58], [231, 63, 223, 61], [231, 64, 223, 62], [231, 65, 223, 63], [231, 66, 223, 64], [232, 6, 224, 4], [232, 7, 224, 5], [233, 6, 225, 4], [233, 12, 225, 10, "screens"], [233, 19, 225, 17], [233, 22, 225, 20], [233, 23, 225, 21], [233, 24, 225, 22], [235, 6, 227, 4], [236, 6, 228, 4], [237, 6, 229, 4], [237, 11, 229, 9], [237, 17, 229, 15, "key"], [237, 20, 229, 18], [237, 24, 229, 22, "t"], [237, 25, 229, 23], [237, 26, 229, 24, "config"], [237, 32, 229, 30], [237, 34, 229, 32], [238, 8, 230, 6], [238, 12, 230, 10, "key"], [238, 15, 230, 13], [238, 20, 230, 18], [238, 29, 230, 27], [238, 33, 230, 31, "t"], [238, 34, 230, 32], [238, 35, 230, 33, "config"], [238, 41, 230, 39], [238, 42, 230, 40, "screens"], [238, 49, 230, 47], [238, 51, 230, 49], [239, 10, 231, 8, "Object"], [239, 16, 231, 14], [239, 17, 231, 15, "assign"], [239, 23, 231, 21], [239, 24, 231, 22, "screens"], [239, 31, 231, 29], [239, 33, 231, 31, "createPathConfigForScreens"], [239, 59, 231, 57], [239, 60, 231, 58, "t"], [239, 61, 231, 59], [239, 62, 231, 60, "config"], [239, 68, 231, 66], [239, 69, 231, 67, "screens"], [239, 76, 231, 74], [239, 78, 231, 76, "o"], [239, 79, 231, 77], [239, 81, 231, 79, "initialRouteName"], [239, 97, 231, 95], [239, 101, 231, 99, "t"], [239, 102, 231, 100], [239, 103, 231, 101, "config"], [239, 109, 231, 107], [239, 110, 231, 108, "initialRouteName"], [239, 126, 231, 124], [239, 127, 231, 125], [239, 128, 231, 126], [240, 8, 232, 6], [241, 8, 233, 6], [241, 12, 233, 10, "key"], [241, 15, 233, 13], [241, 20, 233, 18], [241, 28, 233, 26], [241, 32, 233, 30, "t"], [241, 33, 233, 31], [241, 34, 233, 32, "config"], [241, 40, 233, 38], [241, 41, 233, 39, "groups"], [241, 47, 233, 45], [241, 49, 233, 47], [242, 10, 234, 8, "Object"], [242, 16, 234, 14], [242, 17, 234, 15, "entries"], [242, 24, 234, 22], [242, 25, 234, 23, "t"], [242, 26, 234, 24], [242, 27, 234, 25, "config"], [242, 33, 234, 31], [242, 34, 234, 32, "groups"], [242, 40, 234, 38], [242, 41, 234, 39], [242, 42, 234, 40, "for<PERSON>ach"], [242, 49, 234, 47], [242, 50, 234, 48], [242, 51, 234, 49], [242, 54, 234, 52, "group"], [242, 59, 234, 57], [242, 60, 234, 58], [242, 65, 234, 63], [243, 12, 235, 10, "Object"], [243, 18, 235, 16], [243, 19, 235, 17, "assign"], [243, 25, 235, 23], [243, 26, 235, 24, "screens"], [243, 33, 235, 31], [243, 35, 235, 33, "createPathConfigForScreens"], [243, 61, 235, 59], [243, 62, 235, 60, "group"], [243, 67, 235, 65], [243, 68, 235, 66, "screens"], [243, 75, 235, 73], [243, 77, 235, 75, "o"], [243, 78, 235, 76], [243, 80, 235, 78, "initialRouteName"], [243, 96, 235, 94], [243, 100, 235, 98, "t"], [243, 101, 235, 99], [243, 102, 235, 100, "config"], [243, 108, 235, 106], [243, 109, 235, 107, "initialRouteName"], [243, 125, 235, 123], [243, 126, 235, 124], [243, 127, 235, 125], [244, 10, 236, 8], [244, 11, 236, 9], [244, 12, 236, 10], [245, 8, 237, 6], [246, 6, 238, 4], [247, 6, 239, 4], [247, 10, 239, 8, "Object"], [247, 16, 239, 14], [247, 17, 239, 15, "keys"], [247, 21, 239, 19], [247, 22, 239, 20, "screens"], [247, 29, 239, 27], [247, 30, 239, 28], [247, 31, 239, 29, "length"], [247, 37, 239, 35], [247, 42, 239, 40], [247, 43, 239, 41], [247, 45, 239, 43], [248, 8, 240, 6], [248, 15, 240, 13, "undefined"], [248, 24, 240, 22], [249, 6, 241, 4], [250, 6, 242, 4], [250, 13, 242, 11, "screens"], [250, 20, 242, 18], [251, 4, 243, 2], [251, 5, 243, 3], [252, 4, 244, 2], [252, 10, 244, 8, "screens"], [252, 17, 244, 15], [252, 20, 244, 18, "createPathConfigForTree"], [252, 43, 244, 41], [252, 44, 244, 42, "tree"], [252, 48, 244, 46], [252, 50, 244, 48, "options"], [252, 57, 244, 55], [252, 59, 244, 57], [252, 64, 244, 62], [252, 65, 244, 63], [253, 4, 245, 2], [253, 8, 245, 6, "auto"], [253, 12, 245, 10], [253, 16, 245, 14, "initialScreenConfig"], [253, 35, 245, 33], [253, 37, 245, 35], [254, 6, 246, 4, "initialScreenConfig"], [254, 25, 246, 23], [254, 26, 246, 24, "path"], [254, 30, 246, 28], [254, 33, 246, 31], [254, 35, 246, 33], [255, 4, 247, 2], [256, 4, 248, 2], [256, 11, 248, 9, "screens"], [256, 18, 248, 16], [257, 2, 249, 0], [258, 0, 249, 1], [258, 3]], "functionMap": {"names": ["<global>", "React.memo$argument_0", "getItemsFromScreens", "Object.entries.map$argument_0", "<anonymous>", "_jsx$argument_1.children", "createComponentForStaticNavigation", "groupItems.map$argument_0", "NavigatorComponent", "items.map$argument_0", "createPathConfigForStaticNavigation", "createPathConfigForTree", "createPathConfigForScreens", "Object.entries.sort$argument_0", "Object.entries.sort.map$argument_0", "Object.entries.sort.map.filter$argument_0", "Object.entries.forEach$argument_0"], "mappings": "AAA;+CC8B;CDQ;4BEE;qCCC;WC+B;kBCQ,aD;KDE;GDC;CFC;OMS;+CHwB;eCK;0CGE,cH;SDU;OGC;6BEG;+BCC,cD;GFK;CNG;OUoB;kCCE;uCCC;YCI;ODQ,ME;OFwC,SG,8CH;KDC;gDKU;SLE;GDO;CVM"}}, "type": "js/module"}]}