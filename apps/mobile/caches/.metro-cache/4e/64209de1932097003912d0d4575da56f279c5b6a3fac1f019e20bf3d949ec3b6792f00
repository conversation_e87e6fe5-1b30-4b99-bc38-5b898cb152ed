{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./EventManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 42, "index": 253}}], "key": "+tTRfUGWY5rcrw6ZVLEgvlFTMQc=", "exportNames": ["*"]}}, {"name": "../../handlers/gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 254}, "end": {"line": 4, "column": 66, "index": 320}}], "key": "xaaqCODkGxAwJpzGKT+4pXLUAXk=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 321}, "end": {"line": 5, "column": 43, "index": 364}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 365}, "end": {"line": 6, "column": 107, "index": 472}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../../PointerType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 473}, "end": {"line": 7, "column": 48, "index": 521}}], "key": "PNpP2j+zRZwLQ3w6ZmXPMJNakiU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _EventManager = _interopRequireDefault(require(_dependencyMap[1], \"./EventManager\"));\n  var _gestureHandlerCommon = require(_dependencyMap[2], \"../../handlers/gestureHandlerCommon\");\n  var _interfaces = require(_dependencyMap[3], \"../interfaces\");\n  var _utils = require(_dependencyMap[4], \"../utils\");\n  var _PointerType = require(_dependencyMap[5], \"../../PointerType\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const POINTER_CAPTURE_EXCLUDE_LIST = new Set(['SELECT', 'INPUT']);\n  class PointerEventManager extends _EventManager.default {\n    constructor(view) {\n      super(view);\n      _defineProperty(this, \"trackedPointers\", new Set());\n      _defineProperty(this, \"mouseButtonsMapper\", new Map());\n      _defineProperty(this, \"lastPosition\", void 0);\n      _defineProperty(this, \"pointerDownCallback\", event => {\n        if (!(0, _utils.isPointerInBounds)(this.view, {\n          x: event.clientX,\n          y: event.clientY\n        })) {\n          return;\n        }\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.DOWN);\n        const target = event.target;\n        if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n          target.setPointerCapture(adaptedEvent.pointerId);\n        }\n        this.markAsInBounds(adaptedEvent.pointerId);\n        this.trackedPointers.add(adaptedEvent.pointerId);\n        if (++this.activePointersCounter > 1) {\n          adaptedEvent.eventType = _interfaces.EventTypes.ADDITIONAL_POINTER_DOWN;\n          this.onPointerAdd(adaptedEvent);\n        } else {\n          this.onPointerDown(adaptedEvent);\n        }\n      });\n      _defineProperty(this, \"pointerUpCallback\", event => {\n        // When we call reset on gesture handlers, it also resets their event managers\n        // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n        // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n        // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n        if (this.activePointersCounter === 0) {\n          return;\n        }\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.UP);\n        const target = event.target;\n        if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n          target.releasePointerCapture(adaptedEvent.pointerId);\n        }\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n        this.trackedPointers.delete(adaptedEvent.pointerId);\n        if (--this.activePointersCounter > 0) {\n          adaptedEvent.eventType = _interfaces.EventTypes.ADDITIONAL_POINTER_UP;\n          this.onPointerRemove(adaptedEvent);\n        } else {\n          this.onPointerUp(adaptedEvent);\n        }\n      });\n      _defineProperty(this, \"pointerMoveCallback\", event => {\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.MOVE);\n        const target = event.target; // You may be wondering why are we setting pointer capture here, when we\n        // already set it in `pointerdown` handler. Well, that's a great question,\n        // for which I don't have an answer. Specification (https://www.w3.org/TR/pointerevents2/#dom-element-setpointercapture)\n        // says that the requirement for `setPointerCapture` to work is that pointer\n        // must be in 'active buttons state`, otherwise it will fail silently, which\n        // is lovely. Obviously, when `pointerdown` is fired, one of the buttons\n        // (when using mouse) is pressed, but that doesn't mean that `setPointerCapture`\n        // will succeed, for some reason. Since it fails silently, we don't actually know\n        // if it worked or not (there's `gotpointercapture` event, but the complexity of\n        // incorporating it here seems stupid), so we just call it again here, every time\n        // pointer moves until it succeeds.\n        // God, I do love web development.\n\n        if (!target.hasPointerCapture(event.pointerId) && !POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n          target.setPointerCapture(event.pointerId);\n        }\n        const inBounds = (0, _utils.isPointerInBounds)(this.view, {\n          x: adaptedEvent.x,\n          y: adaptedEvent.y\n        });\n        const pointerIndex = this.pointersInBounds.indexOf(adaptedEvent.pointerId);\n        if (inBounds) {\n          if (pointerIndex < 0) {\n            adaptedEvent.eventType = _interfaces.EventTypes.ENTER;\n            this.onPointerEnter(adaptedEvent);\n            this.markAsInBounds(adaptedEvent.pointerId);\n          } else {\n            this.onPointerMove(adaptedEvent);\n          }\n        } else {\n          if (pointerIndex >= 0) {\n            adaptedEvent.eventType = _interfaces.EventTypes.LEAVE;\n            this.onPointerLeave(adaptedEvent);\n            this.markAsOutOfBounds(adaptedEvent.pointerId);\n          } else {\n            this.onPointerOutOfBounds(adaptedEvent);\n          }\n        }\n        this.lastPosition.x = event.x;\n        this.lastPosition.y = event.y;\n      });\n      _defineProperty(this, \"pointerCancelCallback\", event => {\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.CANCEL);\n        this.onPointerCancel(adaptedEvent);\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n        this.activePointersCounter = 0;\n        this.trackedPointers.clear();\n      });\n      _defineProperty(this, \"pointerEnterCallback\", event => {\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.ENTER);\n        this.onPointerMoveOver(adaptedEvent);\n      });\n      _defineProperty(this, \"pointerLeaveCallback\", event => {\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.LEAVE);\n        this.onPointerMoveOut(adaptedEvent);\n      });\n      _defineProperty(this, \"lostPointerCaptureCallback\", event => {\n        const adaptedEvent = this.mapEvent(event, _interfaces.EventTypes.CANCEL);\n        if (this.trackedPointers.has(adaptedEvent.pointerId)) {\n          // In some cases the `pointerup` event is not fired, but `lostpointercapture` is.\n          // Here we simulate the `pointercancel` event to make sure the gesture handler stops tracking it.\n          this.onPointerCancel(adaptedEvent);\n          this.activePointersCounter = 0;\n          this.trackedPointers.clear();\n        }\n      });\n      this.mouseButtonsMapper.set(0, _gestureHandlerCommon.MouseButton.LEFT);\n      this.mouseButtonsMapper.set(1, _gestureHandlerCommon.MouseButton.MIDDLE);\n      this.mouseButtonsMapper.set(2, _gestureHandlerCommon.MouseButton.RIGHT);\n      this.mouseButtonsMapper.set(3, _gestureHandlerCommon.MouseButton.BUTTON_4);\n      this.mouseButtonsMapper.set(4, _gestureHandlerCommon.MouseButton.BUTTON_5);\n      this.lastPosition = {\n        x: -Infinity,\n        y: -Infinity\n      };\n    }\n    registerListeners() {\n      this.view.addEventListener('pointerdown', this.pointerDownCallback);\n      this.view.addEventListener('pointerup', this.pointerUpCallback);\n      this.view.addEventListener('pointermove', this.pointerMoveCallback);\n      this.view.addEventListener('pointercancel', this.pointerCancelCallback); // onPointerEnter and onPointerLeave are triggered by a custom logic responsible for\n      // handling shouldCancelWhenOutside flag, and are unreliable unless the pointer is down.\n      // We therefore use pointerenter and pointerleave events to handle the hover gesture,\n      // mapping them to onPointerMoveOver and onPointerMoveOut respectively.\n\n      this.view.addEventListener('pointerenter', this.pointerEnterCallback);\n      this.view.addEventListener('pointerleave', this.pointerLeaveCallback);\n      this.view.addEventListener('lostpointercapture', this.lostPointerCaptureCallback);\n    }\n    unregisterListeners() {\n      this.view.removeEventListener('pointerdown', this.pointerDownCallback);\n      this.view.removeEventListener('pointerup', this.pointerUpCallback);\n      this.view.removeEventListener('pointermove', this.pointerMoveCallback);\n      this.view.removeEventListener('pointercancel', this.pointerCancelCallback);\n      this.view.removeEventListener('pointerenter', this.pointerEnterCallback);\n      this.view.removeEventListener('pointerleave', this.pointerLeaveCallback);\n      this.view.removeEventListener('lostpointercapture', this.lostPointerCaptureCallback);\n    }\n    mapEvent(event, eventType) {\n      var _PointerTypeMapping$g;\n      const rect = this.view.getBoundingClientRect();\n      const {\n        scaleX,\n        scaleY\n      } = (0, _utils.calculateViewScale)(this.view);\n      return {\n        x: event.clientX,\n        y: event.clientY,\n        offsetX: (event.clientX - rect.left) / scaleX,\n        offsetY: (event.clientY - rect.top) / scaleY,\n        pointerId: event.pointerId,\n        eventType: eventType,\n        pointerType: (_PointerTypeMapping$g = _utils.PointerTypeMapping.get(event.pointerType)) !== null && _PointerTypeMapping$g !== void 0 ? _PointerTypeMapping$g : _PointerType.PointerType.OTHER,\n        button: this.mouseButtonsMapper.get(event.button),\n        time: event.timeStamp,\n        stylusData: (0, _utils.tryExtractStylusData)(event)\n      };\n    }\n    resetManager() {\n      super.resetManager();\n      this.trackedPointers.clear();\n    }\n  }\n  exports.default = PointerEventManager;\n});", "lineCount": 201, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_EventManager"], [7, 19, 3, 0], [7, 22, 3, 0, "_interopRequireDefault"], [7, 44, 3, 0], [7, 45, 3, 0, "require"], [7, 52, 3, 0], [7, 53, 3, 0, "_dependencyMap"], [7, 67, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 27, 4, 0], [8, 30, 4, 0, "require"], [8, 37, 4, 0], [8, 38, 4, 0, "_dependencyMap"], [8, 52, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_interfaces"], [9, 17, 5, 0], [9, 20, 5, 0, "require"], [9, 27, 5, 0], [9, 28, 5, 0, "_dependencyMap"], [9, 42, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_utils"], [10, 12, 6, 0], [10, 15, 6, 0, "require"], [10, 22, 6, 0], [10, 23, 6, 0, "_dependencyMap"], [10, 37, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_PointerType"], [11, 18, 7, 0], [11, 21, 7, 0, "require"], [11, 28, 7, 0], [11, 29, 7, 0, "_dependencyMap"], [11, 43, 7, 0], [12, 2, 1, 0], [12, 11, 1, 9, "_defineProperty"], [12, 26, 1, 24, "_defineProperty"], [12, 27, 1, 25, "obj"], [12, 30, 1, 28], [12, 32, 1, 30, "key"], [12, 35, 1, 33], [12, 37, 1, 35, "value"], [12, 42, 1, 40], [12, 44, 1, 42], [13, 4, 1, 44], [13, 8, 1, 48, "key"], [13, 11, 1, 51], [13, 15, 1, 55, "obj"], [13, 18, 1, 58], [13, 20, 1, 60], [14, 6, 1, 62, "Object"], [14, 12, 1, 68], [14, 13, 1, 69, "defineProperty"], [14, 27, 1, 83], [14, 28, 1, 84, "obj"], [14, 31, 1, 87], [14, 33, 1, 89, "key"], [14, 36, 1, 92], [14, 38, 1, 94], [15, 8, 1, 96, "value"], [15, 13, 1, 101], [15, 15, 1, 103, "value"], [15, 20, 1, 108], [16, 8, 1, 110, "enumerable"], [16, 18, 1, 120], [16, 20, 1, 122], [16, 24, 1, 126], [17, 8, 1, 128, "configurable"], [17, 20, 1, 140], [17, 22, 1, 142], [17, 26, 1, 146], [18, 8, 1, 148, "writable"], [18, 16, 1, 156], [18, 18, 1, 158], [19, 6, 1, 163], [19, 7, 1, 164], [19, 8, 1, 165], [20, 4, 1, 167], [20, 5, 1, 168], [20, 11, 1, 174], [21, 6, 1, 176, "obj"], [21, 9, 1, 179], [21, 10, 1, 180, "key"], [21, 13, 1, 183], [21, 14, 1, 184], [21, 17, 1, 187, "value"], [21, 22, 1, 192], [22, 4, 1, 194], [23, 4, 1, 196], [23, 11, 1, 203, "obj"], [23, 14, 1, 206], [24, 2, 1, 208], [25, 2, 8, 0], [25, 8, 8, 6, "POINTER_CAPTURE_EXCLUDE_LIST"], [25, 36, 8, 34], [25, 39, 8, 37], [25, 43, 8, 41, "Set"], [25, 46, 8, 44], [25, 47, 8, 45], [25, 48, 8, 46], [25, 56, 8, 54], [25, 58, 8, 56], [25, 65, 8, 63], [25, 66, 8, 64], [25, 67, 8, 65], [26, 2, 9, 15], [26, 8, 9, 21, "PointerEventManager"], [26, 27, 9, 40], [26, 36, 9, 49, "EventManager"], [26, 57, 9, 61], [26, 58, 9, 62], [27, 4, 10, 2, "constructor"], [27, 15, 10, 13, "constructor"], [27, 16, 10, 14, "view"], [27, 20, 10, 18], [27, 22, 10, 20], [28, 6, 11, 4], [28, 11, 11, 9], [28, 12, 11, 10, "view"], [28, 16, 11, 14], [28, 17, 11, 15], [29, 6, 13, 4, "_defineProperty"], [29, 21, 13, 19], [29, 22, 13, 20], [29, 26, 13, 24], [29, 28, 13, 26], [29, 45, 13, 43], [29, 47, 13, 45], [29, 51, 13, 49, "Set"], [29, 54, 13, 52], [29, 55, 13, 53], [29, 56, 13, 54], [29, 57, 13, 55], [30, 6, 15, 4, "_defineProperty"], [30, 21, 15, 19], [30, 22, 15, 20], [30, 26, 15, 24], [30, 28, 15, 26], [30, 48, 15, 46], [30, 50, 15, 48], [30, 54, 15, 52, "Map"], [30, 57, 15, 55], [30, 58, 15, 56], [30, 59, 15, 57], [30, 60, 15, 58], [31, 6, 17, 4, "_defineProperty"], [31, 21, 17, 19], [31, 22, 17, 20], [31, 26, 17, 24], [31, 28, 17, 26], [31, 42, 17, 40], [31, 44, 17, 42], [31, 49, 17, 47], [31, 50, 17, 48], [31, 51, 17, 49], [32, 6, 19, 4, "_defineProperty"], [32, 21, 19, 19], [32, 22, 19, 20], [32, 26, 19, 24], [32, 28, 19, 26], [32, 49, 19, 47], [32, 51, 19, 49, "event"], [32, 56, 19, 54], [32, 60, 19, 58], [33, 8, 20, 6], [33, 12, 20, 10], [33, 13, 20, 11], [33, 17, 20, 11, "isPointerInBounds"], [33, 41, 20, 28], [33, 43, 20, 29], [33, 47, 20, 33], [33, 48, 20, 34, "view"], [33, 52, 20, 38], [33, 54, 20, 40], [34, 10, 21, 8, "x"], [34, 11, 21, 9], [34, 13, 21, 11, "event"], [34, 18, 21, 16], [34, 19, 21, 17, "clientX"], [34, 26, 21, 24], [35, 10, 22, 8, "y"], [35, 11, 22, 9], [35, 13, 22, 11, "event"], [35, 18, 22, 16], [35, 19, 22, 17, "clientY"], [36, 8, 23, 6], [36, 9, 23, 7], [36, 10, 23, 8], [36, 12, 23, 10], [37, 10, 24, 8], [38, 8, 25, 6], [39, 8, 27, 6], [39, 14, 27, 12, "adaptedEvent"], [39, 26, 27, 24], [39, 29, 27, 27], [39, 33, 27, 31], [39, 34, 27, 32, "mapEvent"], [39, 42, 27, 40], [39, 43, 27, 41, "event"], [39, 48, 27, 46], [39, 50, 27, 48, "EventTypes"], [39, 72, 27, 58], [39, 73, 27, 59, "DOWN"], [39, 77, 27, 63], [39, 78, 27, 64], [40, 8, 28, 6], [40, 14, 28, 12, "target"], [40, 20, 28, 18], [40, 23, 28, 21, "event"], [40, 28, 28, 26], [40, 29, 28, 27, "target"], [40, 35, 28, 33], [41, 8, 30, 6], [41, 12, 30, 10], [41, 13, 30, 11, "POINTER_CAPTURE_EXCLUDE_LIST"], [41, 41, 30, 39], [41, 42, 30, 40, "has"], [41, 45, 30, 43], [41, 46, 30, 44, "target"], [41, 52, 30, 50], [41, 53, 30, 51, "tagName"], [41, 60, 30, 58], [41, 61, 30, 59], [41, 63, 30, 61], [42, 10, 31, 8, "target"], [42, 16, 31, 14], [42, 17, 31, 15, "setPointerCapture"], [42, 34, 31, 32], [42, 35, 31, 33, "adaptedEvent"], [42, 47, 31, 45], [42, 48, 31, 46, "pointerId"], [42, 57, 31, 55], [42, 58, 31, 56], [43, 8, 32, 6], [44, 8, 34, 6], [44, 12, 34, 10], [44, 13, 34, 11, "markAsInBounds"], [44, 27, 34, 25], [44, 28, 34, 26, "adaptedEvent"], [44, 40, 34, 38], [44, 41, 34, 39, "pointerId"], [44, 50, 34, 48], [44, 51, 34, 49], [45, 8, 35, 6], [45, 12, 35, 10], [45, 13, 35, 11, "trackedPointers"], [45, 28, 35, 26], [45, 29, 35, 27, "add"], [45, 32, 35, 30], [45, 33, 35, 31, "adaptedEvent"], [45, 45, 35, 43], [45, 46, 35, 44, "pointerId"], [45, 55, 35, 53], [45, 56, 35, 54], [46, 8, 37, 6], [46, 12, 37, 10], [46, 14, 37, 12], [46, 18, 37, 16], [46, 19, 37, 17, "activePointersCounter"], [46, 40, 37, 38], [46, 43, 37, 41], [46, 44, 37, 42], [46, 46, 37, 44], [47, 10, 38, 8, "adaptedEvent"], [47, 22, 38, 20], [47, 23, 38, 21, "eventType"], [47, 32, 38, 30], [47, 35, 38, 33, "EventTypes"], [47, 57, 38, 43], [47, 58, 38, 44, "ADDITIONAL_POINTER_DOWN"], [47, 81, 38, 67], [48, 10, 39, 8], [48, 14, 39, 12], [48, 15, 39, 13, "onPointerAdd"], [48, 27, 39, 25], [48, 28, 39, 26, "adaptedEvent"], [48, 40, 39, 38], [48, 41, 39, 39], [49, 8, 40, 6], [49, 9, 40, 7], [49, 15, 40, 13], [50, 10, 41, 8], [50, 14, 41, 12], [50, 15, 41, 13, "onPointerDown"], [50, 28, 41, 26], [50, 29, 41, 27, "adaptedEvent"], [50, 41, 41, 39], [50, 42, 41, 40], [51, 8, 42, 6], [52, 6, 43, 4], [52, 7, 43, 5], [52, 8, 43, 6], [53, 6, 45, 4, "_defineProperty"], [53, 21, 45, 19], [53, 22, 45, 20], [53, 26, 45, 24], [53, 28, 45, 26], [53, 47, 45, 45], [53, 49, 45, 47, "event"], [53, 54, 45, 52], [53, 58, 45, 56], [54, 8, 46, 6], [55, 8, 47, 6], [56, 8, 48, 6], [57, 8, 49, 6], [58, 8, 50, 6], [58, 12, 50, 10], [58, 16, 50, 14], [58, 17, 50, 15, "activePointersCounter"], [58, 38, 50, 36], [58, 43, 50, 41], [58, 44, 50, 42], [58, 46, 50, 44], [59, 10, 51, 8], [60, 8, 52, 6], [61, 8, 54, 6], [61, 14, 54, 12, "adaptedEvent"], [61, 26, 54, 24], [61, 29, 54, 27], [61, 33, 54, 31], [61, 34, 54, 32, "mapEvent"], [61, 42, 54, 40], [61, 43, 54, 41, "event"], [61, 48, 54, 46], [61, 50, 54, 48, "EventTypes"], [61, 72, 54, 58], [61, 73, 54, 59, "UP"], [61, 75, 54, 61], [61, 76, 54, 62], [62, 8, 55, 6], [62, 14, 55, 12, "target"], [62, 20, 55, 18], [62, 23, 55, 21, "event"], [62, 28, 55, 26], [62, 29, 55, 27, "target"], [62, 35, 55, 33], [63, 8, 57, 6], [63, 12, 57, 10], [63, 13, 57, 11, "POINTER_CAPTURE_EXCLUDE_LIST"], [63, 41, 57, 39], [63, 42, 57, 40, "has"], [63, 45, 57, 43], [63, 46, 57, 44, "target"], [63, 52, 57, 50], [63, 53, 57, 51, "tagName"], [63, 60, 57, 58], [63, 61, 57, 59], [63, 63, 57, 61], [64, 10, 58, 8, "target"], [64, 16, 58, 14], [64, 17, 58, 15, "releasePointerCapture"], [64, 38, 58, 36], [64, 39, 58, 37, "adaptedEvent"], [64, 51, 58, 49], [64, 52, 58, 50, "pointerId"], [64, 61, 58, 59], [64, 62, 58, 60], [65, 8, 59, 6], [66, 8, 61, 6], [66, 12, 61, 10], [66, 13, 61, 11, "markAsOutOfBounds"], [66, 30, 61, 28], [66, 31, 61, 29, "adaptedEvent"], [66, 43, 61, 41], [66, 44, 61, 42, "pointerId"], [66, 53, 61, 51], [66, 54, 61, 52], [67, 8, 62, 6], [67, 12, 62, 10], [67, 13, 62, 11, "trackedPointers"], [67, 28, 62, 26], [67, 29, 62, 27, "delete"], [67, 35, 62, 33], [67, 36, 62, 34, "adaptedEvent"], [67, 48, 62, 46], [67, 49, 62, 47, "pointerId"], [67, 58, 62, 56], [67, 59, 62, 57], [68, 8, 64, 6], [68, 12, 64, 10], [68, 14, 64, 12], [68, 18, 64, 16], [68, 19, 64, 17, "activePointersCounter"], [68, 40, 64, 38], [68, 43, 64, 41], [68, 44, 64, 42], [68, 46, 64, 44], [69, 10, 65, 8, "adaptedEvent"], [69, 22, 65, 20], [69, 23, 65, 21, "eventType"], [69, 32, 65, 30], [69, 35, 65, 33, "EventTypes"], [69, 57, 65, 43], [69, 58, 65, 44, "ADDITIONAL_POINTER_UP"], [69, 79, 65, 65], [70, 10, 66, 8], [70, 14, 66, 12], [70, 15, 66, 13, "onPointerRemove"], [70, 30, 66, 28], [70, 31, 66, 29, "adaptedEvent"], [70, 43, 66, 41], [70, 44, 66, 42], [71, 8, 67, 6], [71, 9, 67, 7], [71, 15, 67, 13], [72, 10, 68, 8], [72, 14, 68, 12], [72, 15, 68, 13, "onPointerUp"], [72, 26, 68, 24], [72, 27, 68, 25, "adaptedEvent"], [72, 39, 68, 37], [72, 40, 68, 38], [73, 8, 69, 6], [74, 6, 70, 4], [74, 7, 70, 5], [74, 8, 70, 6], [75, 6, 72, 4, "_defineProperty"], [75, 21, 72, 19], [75, 22, 72, 20], [75, 26, 72, 24], [75, 28, 72, 26], [75, 49, 72, 47], [75, 51, 72, 49, "event"], [75, 56, 72, 54], [75, 60, 72, 58], [76, 8, 73, 6], [76, 14, 73, 12, "adaptedEvent"], [76, 26, 73, 24], [76, 29, 73, 27], [76, 33, 73, 31], [76, 34, 73, 32, "mapEvent"], [76, 42, 73, 40], [76, 43, 73, 41, "event"], [76, 48, 73, 46], [76, 50, 73, 48, "EventTypes"], [76, 72, 73, 58], [76, 73, 73, 59, "MOVE"], [76, 77, 73, 63], [76, 78, 73, 64], [77, 8, 74, 6], [77, 14, 74, 12, "target"], [77, 20, 74, 18], [77, 23, 74, 21, "event"], [77, 28, 74, 26], [77, 29, 74, 27, "target"], [77, 35, 74, 33], [77, 36, 74, 34], [77, 37, 74, 35], [78, 8, 75, 6], [79, 8, 76, 6], [80, 8, 77, 6], [81, 8, 78, 6], [82, 8, 79, 6], [83, 8, 80, 6], [84, 8, 81, 6], [85, 8, 82, 6], [86, 8, 83, 6], [87, 8, 84, 6], [88, 8, 85, 6], [90, 8, 87, 6], [90, 12, 87, 10], [90, 13, 87, 11, "target"], [90, 19, 87, 17], [90, 20, 87, 18, "hasPointerCapture"], [90, 37, 87, 35], [90, 38, 87, 36, "event"], [90, 43, 87, 41], [90, 44, 87, 42, "pointerId"], [90, 53, 87, 51], [90, 54, 87, 52], [90, 58, 87, 56], [90, 59, 87, 57, "POINTER_CAPTURE_EXCLUDE_LIST"], [90, 87, 87, 85], [90, 88, 87, 86, "has"], [90, 91, 87, 89], [90, 92, 87, 90, "target"], [90, 98, 87, 96], [90, 99, 87, 97, "tagName"], [90, 106, 87, 104], [90, 107, 87, 105], [90, 109, 87, 107], [91, 10, 88, 8, "target"], [91, 16, 88, 14], [91, 17, 88, 15, "setPointerCapture"], [91, 34, 88, 32], [91, 35, 88, 33, "event"], [91, 40, 88, 38], [91, 41, 88, 39, "pointerId"], [91, 50, 88, 48], [91, 51, 88, 49], [92, 8, 89, 6], [93, 8, 91, 6], [93, 14, 91, 12, "inBounds"], [93, 22, 91, 20], [93, 25, 91, 23], [93, 29, 91, 23, "isPointerInBounds"], [93, 53, 91, 40], [93, 55, 91, 41], [93, 59, 91, 45], [93, 60, 91, 46, "view"], [93, 64, 91, 50], [93, 66, 91, 52], [94, 10, 92, 8, "x"], [94, 11, 92, 9], [94, 13, 92, 11, "adaptedEvent"], [94, 25, 92, 23], [94, 26, 92, 24, "x"], [94, 27, 92, 25], [95, 10, 93, 8, "y"], [95, 11, 93, 9], [95, 13, 93, 11, "adaptedEvent"], [95, 25, 93, 23], [95, 26, 93, 24, "y"], [96, 8, 94, 6], [96, 9, 94, 7], [96, 10, 94, 8], [97, 8, 95, 6], [97, 14, 95, 12, "pointerIndex"], [97, 26, 95, 24], [97, 29, 95, 27], [97, 33, 95, 31], [97, 34, 95, 32, "pointersInBounds"], [97, 50, 95, 48], [97, 51, 95, 49, "indexOf"], [97, 58, 95, 56], [97, 59, 95, 57, "adaptedEvent"], [97, 71, 95, 69], [97, 72, 95, 70, "pointerId"], [97, 81, 95, 79], [97, 82, 95, 80], [98, 8, 97, 6], [98, 12, 97, 10, "inBounds"], [98, 20, 97, 18], [98, 22, 97, 20], [99, 10, 98, 8], [99, 14, 98, 12, "pointerIndex"], [99, 26, 98, 24], [99, 29, 98, 27], [99, 30, 98, 28], [99, 32, 98, 30], [100, 12, 99, 10, "adaptedEvent"], [100, 24, 99, 22], [100, 25, 99, 23, "eventType"], [100, 34, 99, 32], [100, 37, 99, 35, "EventTypes"], [100, 59, 99, 45], [100, 60, 99, 46, "ENTER"], [100, 65, 99, 51], [101, 12, 100, 10], [101, 16, 100, 14], [101, 17, 100, 15, "onPointerEnter"], [101, 31, 100, 29], [101, 32, 100, 30, "adaptedEvent"], [101, 44, 100, 42], [101, 45, 100, 43], [102, 12, 101, 10], [102, 16, 101, 14], [102, 17, 101, 15, "markAsInBounds"], [102, 31, 101, 29], [102, 32, 101, 30, "adaptedEvent"], [102, 44, 101, 42], [102, 45, 101, 43, "pointerId"], [102, 54, 101, 52], [102, 55, 101, 53], [103, 10, 102, 8], [103, 11, 102, 9], [103, 17, 102, 15], [104, 12, 103, 10], [104, 16, 103, 14], [104, 17, 103, 15, "onPointerMove"], [104, 30, 103, 28], [104, 31, 103, 29, "adaptedEvent"], [104, 43, 103, 41], [104, 44, 103, 42], [105, 10, 104, 8], [106, 8, 105, 6], [106, 9, 105, 7], [106, 15, 105, 13], [107, 10, 106, 8], [107, 14, 106, 12, "pointerIndex"], [107, 26, 106, 24], [107, 30, 106, 28], [107, 31, 106, 29], [107, 33, 106, 31], [108, 12, 107, 10, "adaptedEvent"], [108, 24, 107, 22], [108, 25, 107, 23, "eventType"], [108, 34, 107, 32], [108, 37, 107, 35, "EventTypes"], [108, 59, 107, 45], [108, 60, 107, 46, "LEAVE"], [108, 65, 107, 51], [109, 12, 108, 10], [109, 16, 108, 14], [109, 17, 108, 15, "onPointerLeave"], [109, 31, 108, 29], [109, 32, 108, 30, "adaptedEvent"], [109, 44, 108, 42], [109, 45, 108, 43], [110, 12, 109, 10], [110, 16, 109, 14], [110, 17, 109, 15, "markAsOutOfBounds"], [110, 34, 109, 32], [110, 35, 109, 33, "adaptedEvent"], [110, 47, 109, 45], [110, 48, 109, 46, "pointerId"], [110, 57, 109, 55], [110, 58, 109, 56], [111, 10, 110, 8], [111, 11, 110, 9], [111, 17, 110, 15], [112, 12, 111, 10], [112, 16, 111, 14], [112, 17, 111, 15, "onPointerOutOfBounds"], [112, 37, 111, 35], [112, 38, 111, 36, "adaptedEvent"], [112, 50, 111, 48], [112, 51, 111, 49], [113, 10, 112, 8], [114, 8, 113, 6], [115, 8, 115, 6], [115, 12, 115, 10], [115, 13, 115, 11, "lastPosition"], [115, 25, 115, 23], [115, 26, 115, 24, "x"], [115, 27, 115, 25], [115, 30, 115, 28, "event"], [115, 35, 115, 33], [115, 36, 115, 34, "x"], [115, 37, 115, 35], [116, 8, 116, 6], [116, 12, 116, 10], [116, 13, 116, 11, "lastPosition"], [116, 25, 116, 23], [116, 26, 116, 24, "y"], [116, 27, 116, 25], [116, 30, 116, 28, "event"], [116, 35, 116, 33], [116, 36, 116, 34, "y"], [116, 37, 116, 35], [117, 6, 117, 4], [117, 7, 117, 5], [117, 8, 117, 6], [118, 6, 119, 4, "_defineProperty"], [118, 21, 119, 19], [118, 22, 119, 20], [118, 26, 119, 24], [118, 28, 119, 26], [118, 51, 119, 49], [118, 53, 119, 51, "event"], [118, 58, 119, 56], [118, 62, 119, 60], [119, 8, 120, 6], [119, 14, 120, 12, "adaptedEvent"], [119, 26, 120, 24], [119, 29, 120, 27], [119, 33, 120, 31], [119, 34, 120, 32, "mapEvent"], [119, 42, 120, 40], [119, 43, 120, 41, "event"], [119, 48, 120, 46], [119, 50, 120, 48, "EventTypes"], [119, 72, 120, 58], [119, 73, 120, 59, "CANCEL"], [119, 79, 120, 65], [119, 80, 120, 66], [120, 8, 121, 6], [120, 12, 121, 10], [120, 13, 121, 11, "onPointerCancel"], [120, 28, 121, 26], [120, 29, 121, 27, "adaptedEvent"], [120, 41, 121, 39], [120, 42, 121, 40], [121, 8, 122, 6], [121, 12, 122, 10], [121, 13, 122, 11, "markAsOutOfBounds"], [121, 30, 122, 28], [121, 31, 122, 29, "adaptedEvent"], [121, 43, 122, 41], [121, 44, 122, 42, "pointerId"], [121, 53, 122, 51], [121, 54, 122, 52], [122, 8, 123, 6], [122, 12, 123, 10], [122, 13, 123, 11, "activePointersCounter"], [122, 34, 123, 32], [122, 37, 123, 35], [122, 38, 123, 36], [123, 8, 124, 6], [123, 12, 124, 10], [123, 13, 124, 11, "trackedPointers"], [123, 28, 124, 26], [123, 29, 124, 27, "clear"], [123, 34, 124, 32], [123, 35, 124, 33], [123, 36, 124, 34], [124, 6, 125, 4], [124, 7, 125, 5], [124, 8, 125, 6], [125, 6, 127, 4, "_defineProperty"], [125, 21, 127, 19], [125, 22, 127, 20], [125, 26, 127, 24], [125, 28, 127, 26], [125, 50, 127, 48], [125, 52, 127, 50, "event"], [125, 57, 127, 55], [125, 61, 127, 59], [126, 8, 128, 6], [126, 14, 128, 12, "adaptedEvent"], [126, 26, 128, 24], [126, 29, 128, 27], [126, 33, 128, 31], [126, 34, 128, 32, "mapEvent"], [126, 42, 128, 40], [126, 43, 128, 41, "event"], [126, 48, 128, 46], [126, 50, 128, 48, "EventTypes"], [126, 72, 128, 58], [126, 73, 128, 59, "ENTER"], [126, 78, 128, 64], [126, 79, 128, 65], [127, 8, 129, 6], [127, 12, 129, 10], [127, 13, 129, 11, "onPointerMoveOver"], [127, 30, 129, 28], [127, 31, 129, 29, "adaptedEvent"], [127, 43, 129, 41], [127, 44, 129, 42], [128, 6, 130, 4], [128, 7, 130, 5], [128, 8, 130, 6], [129, 6, 132, 4, "_defineProperty"], [129, 21, 132, 19], [129, 22, 132, 20], [129, 26, 132, 24], [129, 28, 132, 26], [129, 50, 132, 48], [129, 52, 132, 50, "event"], [129, 57, 132, 55], [129, 61, 132, 59], [130, 8, 133, 6], [130, 14, 133, 12, "adaptedEvent"], [130, 26, 133, 24], [130, 29, 133, 27], [130, 33, 133, 31], [130, 34, 133, 32, "mapEvent"], [130, 42, 133, 40], [130, 43, 133, 41, "event"], [130, 48, 133, 46], [130, 50, 133, 48, "EventTypes"], [130, 72, 133, 58], [130, 73, 133, 59, "LEAVE"], [130, 78, 133, 64], [130, 79, 133, 65], [131, 8, 134, 6], [131, 12, 134, 10], [131, 13, 134, 11, "onPointerMoveOut"], [131, 29, 134, 27], [131, 30, 134, 28, "adaptedEvent"], [131, 42, 134, 40], [131, 43, 134, 41], [132, 6, 135, 4], [132, 7, 135, 5], [132, 8, 135, 6], [133, 6, 137, 4, "_defineProperty"], [133, 21, 137, 19], [133, 22, 137, 20], [133, 26, 137, 24], [133, 28, 137, 26], [133, 56, 137, 54], [133, 58, 137, 56, "event"], [133, 63, 137, 61], [133, 67, 137, 65], [134, 8, 138, 6], [134, 14, 138, 12, "adaptedEvent"], [134, 26, 138, 24], [134, 29, 138, 27], [134, 33, 138, 31], [134, 34, 138, 32, "mapEvent"], [134, 42, 138, 40], [134, 43, 138, 41, "event"], [134, 48, 138, 46], [134, 50, 138, 48, "EventTypes"], [134, 72, 138, 58], [134, 73, 138, 59, "CANCEL"], [134, 79, 138, 65], [134, 80, 138, 66], [135, 8, 140, 6], [135, 12, 140, 10], [135, 16, 140, 14], [135, 17, 140, 15, "trackedPointers"], [135, 32, 140, 30], [135, 33, 140, 31, "has"], [135, 36, 140, 34], [135, 37, 140, 35, "adaptedEvent"], [135, 49, 140, 47], [135, 50, 140, 48, "pointerId"], [135, 59, 140, 57], [135, 60, 140, 58], [135, 62, 140, 60], [136, 10, 141, 8], [137, 10, 142, 8], [138, 10, 143, 8], [138, 14, 143, 12], [138, 15, 143, 13, "onPointerCancel"], [138, 30, 143, 28], [138, 31, 143, 29, "adaptedEvent"], [138, 43, 143, 41], [138, 44, 143, 42], [139, 10, 144, 8], [139, 14, 144, 12], [139, 15, 144, 13, "activePointersCounter"], [139, 36, 144, 34], [139, 39, 144, 37], [139, 40, 144, 38], [140, 10, 145, 8], [140, 14, 145, 12], [140, 15, 145, 13, "trackedPointers"], [140, 30, 145, 28], [140, 31, 145, 29, "clear"], [140, 36, 145, 34], [140, 37, 145, 35], [140, 38, 145, 36], [141, 8, 146, 6], [142, 6, 147, 4], [142, 7, 147, 5], [142, 8, 147, 6], [143, 6, 149, 4], [143, 10, 149, 8], [143, 11, 149, 9, "mouseButtonsMapper"], [143, 29, 149, 27], [143, 30, 149, 28, "set"], [143, 33, 149, 31], [143, 34, 149, 32], [143, 35, 149, 33], [143, 37, 149, 35, "MouseB<PERSON>on"], [143, 70, 149, 46], [143, 71, 149, 47, "LEFT"], [143, 75, 149, 51], [143, 76, 149, 52], [144, 6, 150, 4], [144, 10, 150, 8], [144, 11, 150, 9, "mouseButtonsMapper"], [144, 29, 150, 27], [144, 30, 150, 28, "set"], [144, 33, 150, 31], [144, 34, 150, 32], [144, 35, 150, 33], [144, 37, 150, 35, "MouseB<PERSON>on"], [144, 70, 150, 46], [144, 71, 150, 47, "MIDDLE"], [144, 77, 150, 53], [144, 78, 150, 54], [145, 6, 151, 4], [145, 10, 151, 8], [145, 11, 151, 9, "mouseButtonsMapper"], [145, 29, 151, 27], [145, 30, 151, 28, "set"], [145, 33, 151, 31], [145, 34, 151, 32], [145, 35, 151, 33], [145, 37, 151, 35, "MouseB<PERSON>on"], [145, 70, 151, 46], [145, 71, 151, 47, "RIGHT"], [145, 76, 151, 52], [145, 77, 151, 53], [146, 6, 152, 4], [146, 10, 152, 8], [146, 11, 152, 9, "mouseButtonsMapper"], [146, 29, 152, 27], [146, 30, 152, 28, "set"], [146, 33, 152, 31], [146, 34, 152, 32], [146, 35, 152, 33], [146, 37, 152, 35, "MouseB<PERSON>on"], [146, 70, 152, 46], [146, 71, 152, 47, "BUTTON_4"], [146, 79, 152, 55], [146, 80, 152, 56], [147, 6, 153, 4], [147, 10, 153, 8], [147, 11, 153, 9, "mouseButtonsMapper"], [147, 29, 153, 27], [147, 30, 153, 28, "set"], [147, 33, 153, 31], [147, 34, 153, 32], [147, 35, 153, 33], [147, 37, 153, 35, "MouseB<PERSON>on"], [147, 70, 153, 46], [147, 71, 153, 47, "BUTTON_5"], [147, 79, 153, 55], [147, 80, 153, 56], [148, 6, 154, 4], [148, 10, 154, 8], [148, 11, 154, 9, "lastPosition"], [148, 23, 154, 21], [148, 26, 154, 24], [149, 8, 155, 6, "x"], [149, 9, 155, 7], [149, 11, 155, 9], [149, 12, 155, 10, "Infinity"], [149, 20, 155, 18], [150, 8, 156, 6, "y"], [150, 9, 156, 7], [150, 11, 156, 9], [150, 12, 156, 10, "Infinity"], [151, 6, 157, 4], [151, 7, 157, 5], [152, 4, 158, 2], [153, 4, 160, 2, "registerListeners"], [153, 21, 160, 19, "registerListeners"], [153, 22, 160, 19], [153, 24, 160, 22], [154, 6, 161, 4], [154, 10, 161, 8], [154, 11, 161, 9, "view"], [154, 15, 161, 13], [154, 16, 161, 14, "addEventListener"], [154, 32, 161, 30], [154, 33, 161, 31], [154, 46, 161, 44], [154, 48, 161, 46], [154, 52, 161, 50], [154, 53, 161, 51, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [154, 72, 161, 70], [154, 73, 161, 71], [155, 6, 162, 4], [155, 10, 162, 8], [155, 11, 162, 9, "view"], [155, 15, 162, 13], [155, 16, 162, 14, "addEventListener"], [155, 32, 162, 30], [155, 33, 162, 31], [155, 44, 162, 42], [155, 46, 162, 44], [155, 50, 162, 48], [155, 51, 162, 49, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [155, 68, 162, 66], [155, 69, 162, 67], [156, 6, 163, 4], [156, 10, 163, 8], [156, 11, 163, 9, "view"], [156, 15, 163, 13], [156, 16, 163, 14, "addEventListener"], [156, 32, 163, 30], [156, 33, 163, 31], [156, 46, 163, 44], [156, 48, 163, 46], [156, 52, 163, 50], [156, 53, 163, 51, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [156, 72, 163, 70], [156, 73, 163, 71], [157, 6, 164, 4], [157, 10, 164, 8], [157, 11, 164, 9, "view"], [157, 15, 164, 13], [157, 16, 164, 14, "addEventListener"], [157, 32, 164, 30], [157, 33, 164, 31], [157, 48, 164, 46], [157, 50, 164, 48], [157, 54, 164, 52], [157, 55, 164, 53, "pointerCancelCallback"], [157, 76, 164, 74], [157, 77, 164, 75], [157, 78, 164, 76], [157, 79, 164, 77], [158, 6, 165, 4], [159, 6, 166, 4], [160, 6, 167, 4], [162, 6, 169, 4], [162, 10, 169, 8], [162, 11, 169, 9, "view"], [162, 15, 169, 13], [162, 16, 169, 14, "addEventListener"], [162, 32, 169, 30], [162, 33, 169, 31], [162, 47, 169, 45], [162, 49, 169, 47], [162, 53, 169, 51], [162, 54, 169, 52, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [162, 74, 169, 72], [162, 75, 169, 73], [163, 6, 170, 4], [163, 10, 170, 8], [163, 11, 170, 9, "view"], [163, 15, 170, 13], [163, 16, 170, 14, "addEventListener"], [163, 32, 170, 30], [163, 33, 170, 31], [163, 47, 170, 45], [163, 49, 170, 47], [163, 53, 170, 51], [163, 54, 170, 52, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [163, 74, 170, 72], [163, 75, 170, 73], [164, 6, 171, 4], [164, 10, 171, 8], [164, 11, 171, 9, "view"], [164, 15, 171, 13], [164, 16, 171, 14, "addEventListener"], [164, 32, 171, 30], [164, 33, 171, 31], [164, 53, 171, 51], [164, 55, 171, 53], [164, 59, 171, 57], [164, 60, 171, 58, "lostPointerCaptureCallback"], [164, 86, 171, 84], [164, 87, 171, 85], [165, 4, 172, 2], [166, 4, 174, 2, "unregisterListeners"], [166, 23, 174, 21, "unregisterListeners"], [166, 24, 174, 21], [166, 26, 174, 24], [167, 6, 175, 4], [167, 10, 175, 8], [167, 11, 175, 9, "view"], [167, 15, 175, 13], [167, 16, 175, 14, "removeEventListener"], [167, 35, 175, 33], [167, 36, 175, 34], [167, 49, 175, 47], [167, 51, 175, 49], [167, 55, 175, 53], [167, 56, 175, 54, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [167, 75, 175, 73], [167, 76, 175, 74], [168, 6, 176, 4], [168, 10, 176, 8], [168, 11, 176, 9, "view"], [168, 15, 176, 13], [168, 16, 176, 14, "removeEventListener"], [168, 35, 176, 33], [168, 36, 176, 34], [168, 47, 176, 45], [168, 49, 176, 47], [168, 53, 176, 51], [168, 54, 176, 52, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [168, 71, 176, 69], [168, 72, 176, 70], [169, 6, 177, 4], [169, 10, 177, 8], [169, 11, 177, 9, "view"], [169, 15, 177, 13], [169, 16, 177, 14, "removeEventListener"], [169, 35, 177, 33], [169, 36, 177, 34], [169, 49, 177, 47], [169, 51, 177, 49], [169, 55, 177, 53], [169, 56, 177, 54, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [169, 75, 177, 73], [169, 76, 177, 74], [170, 6, 178, 4], [170, 10, 178, 8], [170, 11, 178, 9, "view"], [170, 15, 178, 13], [170, 16, 178, 14, "removeEventListener"], [170, 35, 178, 33], [170, 36, 178, 34], [170, 51, 178, 49], [170, 53, 178, 51], [170, 57, 178, 55], [170, 58, 178, 56, "pointerCancelCallback"], [170, 79, 178, 77], [170, 80, 178, 78], [171, 6, 179, 4], [171, 10, 179, 8], [171, 11, 179, 9, "view"], [171, 15, 179, 13], [171, 16, 179, 14, "removeEventListener"], [171, 35, 179, 33], [171, 36, 179, 34], [171, 50, 179, 48], [171, 52, 179, 50], [171, 56, 179, 54], [171, 57, 179, 55, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>"], [171, 77, 179, 75], [171, 78, 179, 76], [172, 6, 180, 4], [172, 10, 180, 8], [172, 11, 180, 9, "view"], [172, 15, 180, 13], [172, 16, 180, 14, "removeEventListener"], [172, 35, 180, 33], [172, 36, 180, 34], [172, 50, 180, 48], [172, 52, 180, 50], [172, 56, 180, 54], [172, 57, 180, 55, "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [172, 77, 180, 75], [172, 78, 180, 76], [173, 6, 181, 4], [173, 10, 181, 8], [173, 11, 181, 9, "view"], [173, 15, 181, 13], [173, 16, 181, 14, "removeEventListener"], [173, 35, 181, 33], [173, 36, 181, 34], [173, 56, 181, 54], [173, 58, 181, 56], [173, 62, 181, 60], [173, 63, 181, 61, "lostPointerCaptureCallback"], [173, 89, 181, 87], [173, 90, 181, 88], [174, 4, 182, 2], [175, 4, 184, 2, "mapEvent"], [175, 12, 184, 10, "mapEvent"], [175, 13, 184, 11, "event"], [175, 18, 184, 16], [175, 20, 184, 18, "eventType"], [175, 29, 184, 27], [175, 31, 184, 29], [176, 6, 185, 4], [176, 10, 185, 8, "_PointerTypeMapping$g"], [176, 31, 185, 29], [177, 6, 187, 4], [177, 12, 187, 10, "rect"], [177, 16, 187, 14], [177, 19, 187, 17], [177, 23, 187, 21], [177, 24, 187, 22, "view"], [177, 28, 187, 26], [177, 29, 187, 27, "getBoundingClientRect"], [177, 50, 187, 48], [177, 51, 187, 49], [177, 52, 187, 50], [178, 6, 188, 4], [178, 12, 188, 10], [179, 8, 189, 6, "scaleX"], [179, 14, 189, 12], [180, 8, 190, 6, "scaleY"], [181, 6, 191, 4], [181, 7, 191, 5], [181, 10, 191, 8], [181, 14, 191, 8, "calculateViewScale"], [181, 39, 191, 26], [181, 41, 191, 27], [181, 45, 191, 31], [181, 46, 191, 32, "view"], [181, 50, 191, 36], [181, 51, 191, 37], [182, 6, 192, 4], [182, 13, 192, 11], [183, 8, 193, 6, "x"], [183, 9, 193, 7], [183, 11, 193, 9, "event"], [183, 16, 193, 14], [183, 17, 193, 15, "clientX"], [183, 24, 193, 22], [184, 8, 194, 6, "y"], [184, 9, 194, 7], [184, 11, 194, 9, "event"], [184, 16, 194, 14], [184, 17, 194, 15, "clientY"], [184, 24, 194, 22], [185, 8, 195, 6, "offsetX"], [185, 15, 195, 13], [185, 17, 195, 15], [185, 18, 195, 16, "event"], [185, 23, 195, 21], [185, 24, 195, 22, "clientX"], [185, 31, 195, 29], [185, 34, 195, 32, "rect"], [185, 38, 195, 36], [185, 39, 195, 37, "left"], [185, 43, 195, 41], [185, 47, 195, 45, "scaleX"], [185, 53, 195, 51], [186, 8, 196, 6, "offsetY"], [186, 15, 196, 13], [186, 17, 196, 15], [186, 18, 196, 16, "event"], [186, 23, 196, 21], [186, 24, 196, 22, "clientY"], [186, 31, 196, 29], [186, 34, 196, 32, "rect"], [186, 38, 196, 36], [186, 39, 196, 37, "top"], [186, 42, 196, 40], [186, 46, 196, 44, "scaleY"], [186, 52, 196, 50], [187, 8, 197, 6, "pointerId"], [187, 17, 197, 15], [187, 19, 197, 17, "event"], [187, 24, 197, 22], [187, 25, 197, 23, "pointerId"], [187, 34, 197, 32], [188, 8, 198, 6, "eventType"], [188, 17, 198, 15], [188, 19, 198, 17, "eventType"], [188, 28, 198, 26], [189, 8, 199, 6, "pointerType"], [189, 19, 199, 17], [189, 21, 199, 19], [189, 22, 199, 20, "_PointerTypeMapping$g"], [189, 43, 199, 41], [189, 46, 199, 44, "PointerTypeMapping"], [189, 71, 199, 62], [189, 72, 199, 63, "get"], [189, 75, 199, 66], [189, 76, 199, 67, "event"], [189, 81, 199, 72], [189, 82, 199, 73, "pointerType"], [189, 93, 199, 84], [189, 94, 199, 85], [189, 100, 199, 91], [189, 104, 199, 95], [189, 108, 199, 99, "_PointerTypeMapping$g"], [189, 129, 199, 120], [189, 134, 199, 125], [189, 139, 199, 130], [189, 140, 199, 131], [189, 143, 199, 134, "_PointerTypeMapping$g"], [189, 164, 199, 155], [189, 167, 199, 158, "PointerType"], [189, 191, 199, 169], [189, 192, 199, 170, "OTHER"], [189, 197, 199, 175], [190, 8, 200, 6, "button"], [190, 14, 200, 12], [190, 16, 200, 14], [190, 20, 200, 18], [190, 21, 200, 19, "mouseButtonsMapper"], [190, 39, 200, 37], [190, 40, 200, 38, "get"], [190, 43, 200, 41], [190, 44, 200, 42, "event"], [190, 49, 200, 47], [190, 50, 200, 48, "button"], [190, 56, 200, 54], [190, 57, 200, 55], [191, 8, 201, 6, "time"], [191, 12, 201, 10], [191, 14, 201, 12, "event"], [191, 19, 201, 17], [191, 20, 201, 18, "timeStamp"], [191, 29, 201, 27], [192, 8, 202, 6, "stylusData"], [192, 18, 202, 16], [192, 20, 202, 18], [192, 24, 202, 18, "tryExtractStylusData"], [192, 51, 202, 38], [192, 53, 202, 39, "event"], [192, 58, 202, 44], [193, 6, 203, 4], [193, 7, 203, 5], [194, 4, 204, 2], [195, 4, 206, 2, "resetManager"], [195, 16, 206, 14, "resetManager"], [195, 17, 206, 14], [195, 19, 206, 17], [196, 6, 207, 4], [196, 11, 207, 9], [196, 12, 207, 10, "resetManager"], [196, 24, 207, 22], [196, 25, 207, 23], [196, 26, 207, 24], [197, 6, 208, 4], [197, 10, 208, 8], [197, 11, 208, 9, "trackedPointers"], [197, 26, 208, 24], [197, 27, 208, 25, "clear"], [197, 32, 208, 30], [197, 33, 208, 31], [197, 34, 208, 32], [198, 4, 209, 2], [199, 2, 211, 0], [200, 2, 211, 1, "exports"], [200, 9, 211, 1], [200, 10, 211, 1, "default"], [200, 17, 211, 1], [200, 20, 211, 1, "PointerEventManager"], [200, 39, 211, 1], [201, 0, 211, 1], [201, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "PointerEventManager", "constructor", "_defineProperty$argument_2", "registerListeners", "unregisterListeners", "mapEvent", "resetManager"], "mappings": "AAA,iNC;eCQ;ECC;iDCS;KDwB;+CCE;KDyB;iDCE;KD6C;mDCE;KDM;kDCE;KDG;kDCE;KDG;wDCE;KDU;GDW;EGE;GHY;EIE;GJQ;EKE;GLoB;EME;GNG;CDE"}}, "type": "js/module"}]}