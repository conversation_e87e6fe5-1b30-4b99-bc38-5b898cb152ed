{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /* eslint-disable @typescript-eslint/no-explicit-any */\n\n  // TODO: Clean this up since 0.74 is the minimum supported version now.\n  // This is a makeshift solution to handle both 0.73 and 0.74 versions of React Native.\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getViewInfo = void 0;\n  let getViewInfo = element => {\n    if (element._nativeTag !== undefined && element.__nativeTag !== null) {\n      exports.getViewInfo = getViewInfo = getViewInfo73;\n      return getViewInfo73(element);\n    } else if (element.__nativeTag !== undefined && element.__nativeTag !== null) {\n      exports.getViewInfo = getViewInfo = getViewInfoLatest;\n      return getViewInfoLatest(element);\n    }\n    return getViewInfo73(element);\n  };\n  exports.getViewInfo = getViewInfo;\n  function getViewInfo73(element) {\n    return {\n      // we can access view tag in the same way it's accessed here https://github.com/facebook/react/blob/e3f4eb7272d4ca0ee49f27577156b57eeb07cf73/packages/react-native-renderer/src/ReactFabric.js#L146\n      viewName: element?.viewConfig?.uiViewClassName,\n      /**\n       * RN uses viewConfig for components for storing different properties of the\n       * component(example:\n       * https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js#L24).\n       * The name we're looking for is in the field named uiViewClassName.\n       */\n      viewTag: element?._nativeTag,\n      viewConfig: element?.viewConfig\n    };\n  }\n  function getViewInfoLatest(element) {\n    return {\n      viewName: element?._viewConfig?.uiViewClassName,\n      viewTag: element?.__nativeTag,\n      viewConfig: element?._viewConfig\n    };\n  }\n});", "lineCount": 44, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [6, 2, 5, 0], [7, 2, 6, 0], [8, 2, 6, 0, "Object"], [8, 8, 6, 0], [8, 9, 6, 0, "defineProperty"], [8, 23, 6, 0], [8, 24, 6, 0, "exports"], [8, 31, 6, 0], [9, 4, 6, 0, "value"], [9, 9, 6, 0], [10, 2, 6, 0], [11, 2, 6, 0, "exports"], [11, 9, 6, 0], [11, 10, 6, 0, "getViewInfo"], [11, 21, 6, 0], [12, 2, 7, 7], [12, 6, 7, 11, "getViewInfo"], [12, 17, 7, 22], [12, 20, 7, 25, "element"], [12, 27, 7, 32], [12, 31, 7, 36], [13, 4, 8, 2], [13, 8, 8, 6, "element"], [13, 15, 8, 13], [13, 16, 8, 14, "_nativeTag"], [13, 26, 8, 24], [13, 31, 8, 29, "undefined"], [13, 40, 8, 38], [13, 44, 8, 42, "element"], [13, 51, 8, 49], [13, 52, 8, 50, "__nativeTag"], [13, 63, 8, 61], [13, 68, 8, 66], [13, 72, 8, 70], [13, 74, 8, 72], [14, 6, 9, 4, "exports"], [14, 13, 9, 4], [14, 14, 9, 4, "getViewInfo"], [14, 25, 9, 4], [14, 28, 9, 4, "getViewInfo"], [14, 39, 9, 15], [14, 42, 9, 18, "getViewInfo73"], [14, 55, 9, 31], [15, 6, 10, 4], [15, 13, 10, 11, "getViewInfo73"], [15, 26, 10, 24], [15, 27, 10, 25, "element"], [15, 34, 10, 32], [15, 35, 10, 33], [16, 4, 11, 2], [16, 5, 11, 3], [16, 11, 11, 9], [16, 15, 11, 13, "element"], [16, 22, 11, 20], [16, 23, 11, 21, "__nativeTag"], [16, 34, 11, 32], [16, 39, 11, 37, "undefined"], [16, 48, 11, 46], [16, 52, 11, 50, "element"], [16, 59, 11, 57], [16, 60, 11, 58, "__nativeTag"], [16, 71, 11, 69], [16, 76, 11, 74], [16, 80, 11, 78], [16, 82, 11, 80], [17, 6, 12, 4, "exports"], [17, 13, 12, 4], [17, 14, 12, 4, "getViewInfo"], [17, 25, 12, 4], [17, 28, 12, 4, "getViewInfo"], [17, 39, 12, 15], [17, 42, 12, 18, "getViewInfoLatest"], [17, 59, 12, 35], [18, 6, 13, 4], [18, 13, 13, 11, "getViewInfoLatest"], [18, 30, 13, 28], [18, 31, 13, 29, "element"], [18, 38, 13, 36], [18, 39, 13, 37], [19, 4, 14, 2], [20, 4, 15, 2], [20, 11, 15, 9, "getViewInfo73"], [20, 24, 15, 22], [20, 25, 15, 23, "element"], [20, 32, 15, 30], [20, 33, 15, 31], [21, 2, 16, 0], [21, 3, 16, 1], [22, 2, 16, 2, "exports"], [22, 9, 16, 2], [22, 10, 16, 2, "getViewInfo"], [22, 21, 16, 2], [22, 24, 16, 2, "getViewInfo"], [22, 35, 16, 2], [23, 2, 17, 0], [23, 11, 17, 9, "getViewInfo73"], [23, 24, 17, 22, "getViewInfo73"], [23, 25, 17, 23, "element"], [23, 32, 17, 30], [23, 34, 17, 32], [24, 4, 18, 2], [24, 11, 18, 9], [25, 6, 19, 4], [26, 6, 20, 4, "viewName"], [26, 14, 20, 12], [26, 16, 20, 14, "element"], [26, 23, 20, 21], [26, 25, 20, 23, "viewConfig"], [26, 35, 20, 33], [26, 37, 20, 35, "uiViewClassName"], [26, 52, 20, 50], [27, 6, 21, 4], [28, 0, 22, 0], [29, 0, 23, 0], [30, 0, 24, 0], [31, 0, 25, 0], [32, 0, 26, 0], [33, 6, 27, 4, "viewTag"], [33, 13, 27, 11], [33, 15, 27, 13, "element"], [33, 22, 27, 20], [33, 24, 27, 22, "_nativeTag"], [33, 34, 27, 32], [34, 6, 28, 4, "viewConfig"], [34, 16, 28, 14], [34, 18, 28, 16, "element"], [34, 25, 28, 23], [34, 27, 28, 25, "viewConfig"], [35, 4, 29, 2], [35, 5, 29, 3], [36, 2, 30, 0], [37, 2, 31, 0], [37, 11, 31, 9, "getViewInfoLatest"], [37, 28, 31, 26, "getViewInfoLatest"], [37, 29, 31, 27, "element"], [37, 36, 31, 34], [37, 38, 31, 36], [38, 4, 32, 2], [38, 11, 32, 9], [39, 6, 33, 4, "viewName"], [39, 14, 33, 12], [39, 16, 33, 14, "element"], [39, 23, 33, 21], [39, 25, 33, 23, "_viewConfig"], [39, 36, 33, 34], [39, 38, 33, 36, "uiViewClassName"], [39, 53, 33, 51], [40, 6, 34, 4, "viewTag"], [40, 13, 34, 11], [40, 15, 34, 13, "element"], [40, 22, 34, 20], [40, 24, 34, 22, "__nativeTag"], [40, 35, 34, 33], [41, 6, 35, 4, "viewConfig"], [41, 16, 35, 14], [41, 18, 35, 16, "element"], [41, 25, 35, 23], [41, 27, 35, 25, "_viewConfig"], [42, 4, 36, 2], [42, 5, 36, 3], [43, 2, 37, 0], [44, 0, 37, 1], [44, 3]], "functionMap": {"names": ["<global>", "getViewInfo", "getViewInfo73", "getViewInfoLatest"], "mappings": "AAA;yBCM;CDS;AEC;CFa;AGC;CHM"}}, "type": "js/module"}]}