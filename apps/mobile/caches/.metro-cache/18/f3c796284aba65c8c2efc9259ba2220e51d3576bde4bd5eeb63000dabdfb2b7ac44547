{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SHEET_FIT_TO_CONTENTS = exports.SHEET_DIMMED_ALWAYS = exports.SHEET_COMPAT_MEDIUM = exports.SHEET_COMPAT_LARGE = exports.SHEET_COMPAT_ALL = void 0;\n  exports.assertDetentsArrayIsSorted = assertDetentsArrayIsSorted;\n  exports.resolveSheetAllowedDetents = resolveSheetAllowedDetents;\n  exports.resolveSheetInitialDetentIndex = resolveSheetInitialDetentIndex;\n  exports.resolveSheetLargestUndimmedDetent = resolveSheetLargestUndimmedDetent;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  // This value must be kept in sync with native side.\n  var SHEET_FIT_TO_CONTENTS = exports.SHEET_FIT_TO_CONTENTS = [-1];\n  var SHEET_COMPAT_LARGE = exports.SHEET_COMPAT_LARGE = [1.0];\n  var SHEET_COMPAT_MEDIUM = exports.SHEET_COMPAT_MEDIUM = [0.5];\n  var SHEET_COMPAT_ALL = exports.SHEET_COMPAT_ALL = [0.5, 1.0];\n  var SHEET_DIMMED_ALWAYS = exports.SHEET_DIMMED_ALWAYS = -1;\n  function assertDetentsArrayIsSorted(array) {\n    for (var i = 1; i < array.length; i++) {\n      if (array[i - 1] > array[i]) {\n        throw new Error('[RNScreens] The detent array is not sorted in ascending order!');\n      }\n    }\n  }\n\n  // These exist to transform old 'legacy' values used by the formsheet API to the new API shape.\n  // We can get rid of it, once we get rid of support for legacy values: 'large', 'medium', 'all'.\n  function resolveSheetAllowedDetents(allowedDetentsCompat) {\n    if (Array.isArray(allowedDetentsCompat)) {\n      if (_reactNative.Platform.OS === 'android' && allowedDetentsCompat.length > 3) {\n        if (__DEV__) {\n          console.warn('[RNScreens] Sheets API on Android do accept only up to 3 values. Any surplus value are ignored.');\n        }\n        allowedDetentsCompat = allowedDetentsCompat.slice(0, 3);\n      }\n      if (__DEV__) {\n        assertDetentsArrayIsSorted(allowedDetentsCompat);\n      }\n      return allowedDetentsCompat;\n    } else if (allowedDetentsCompat === 'fitToContents') {\n      return SHEET_FIT_TO_CONTENTS;\n    } else if (allowedDetentsCompat === 'large') {\n      return SHEET_COMPAT_LARGE;\n    } else if (allowedDetentsCompat === 'medium') {\n      return SHEET_COMPAT_MEDIUM;\n    } else if (allowedDetentsCompat === 'all') {\n      return SHEET_COMPAT_ALL;\n    } else {\n      // Safe default, only large detent is allowed.\n      return SHEET_COMPAT_LARGE;\n    }\n  }\n  function resolveSheetLargestUndimmedDetent(lud, lastDetentIndex) {\n    if (typeof lud === 'number') {\n      if (!isIndexInClosedRange(lud, SHEET_DIMMED_ALWAYS, lastDetentIndex)) {\n        if (__DEV__) {\n          throw new Error(\"[RNScreens] Provided value of 'sheetLargestUndimmedDetentIndex' prop is out of bounds of 'sheetAllowedDetents' array.\");\n        }\n        // Return default in production\n        return SHEET_DIMMED_ALWAYS;\n      }\n      return lud;\n    } else if (lud === 'last') {\n      return lastDetentIndex;\n    } else if (lud === 'none' || lud === 'all') {\n      return SHEET_DIMMED_ALWAYS;\n    } else if (lud === 'large') {\n      return 1;\n    } else if (lud === 'medium') {\n      return 0;\n    } else {\n      // Safe default, every detent is dimmed\n      return SHEET_DIMMED_ALWAYS;\n    }\n  }\n  function resolveSheetInitialDetentIndex(index, lastDetentIndex) {\n    if (index === 'last') {\n      index = lastDetentIndex;\n    } else if (index == null) {\n      // Intentional check for undefined & null ^\n      index = 0;\n    }\n    if (!isIndexInClosedRange(index, 0, lastDetentIndex)) {\n      if (__DEV__) {\n        throw new Error(\"[RNScreens] Provided value of 'sheetInitialDetentIndex' prop is out of bounds of 'sheetAllowedDetents' array.\");\n      }\n      // Return default in production\n      return 0;\n    }\n    return index;\n  }\n  function isIndexInClosedRange(value, lowerBound, upperBound) {\n    return Number.isInteger(value) && value >= lowerBound && value <= upperBound;\n  }\n});", "lineCount": 94, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_reactNative"], [10, 18, 1, 0], [10, 21, 1, 0, "require"], [10, 28, 1, 0], [10, 29, 1, 0, "_dependencyMap"], [10, 43, 1, 0], [11, 2, 4, 0], [12, 2, 5, 7], [12, 6, 5, 13, "SHEET_FIT_TO_CONTENTS"], [12, 27, 5, 34], [12, 30, 5, 34, "exports"], [12, 37, 5, 34], [12, 38, 5, 34, "SHEET_FIT_TO_CONTENTS"], [12, 59, 5, 34], [12, 62, 5, 37], [12, 63, 5, 38], [12, 64, 5, 39], [12, 65, 5, 40], [12, 66, 5, 41], [13, 2, 6, 7], [13, 6, 6, 13, "SHEET_COMPAT_LARGE"], [13, 24, 6, 31], [13, 27, 6, 31, "exports"], [13, 34, 6, 31], [13, 35, 6, 31, "SHEET_COMPAT_LARGE"], [13, 53, 6, 31], [13, 56, 6, 34], [13, 57, 6, 35], [13, 60, 6, 38], [13, 61, 6, 39], [14, 2, 7, 7], [14, 6, 7, 13, "SHEET_COMPAT_MEDIUM"], [14, 25, 7, 32], [14, 28, 7, 32, "exports"], [14, 35, 7, 32], [14, 36, 7, 32, "SHEET_COMPAT_MEDIUM"], [14, 55, 7, 32], [14, 58, 7, 35], [14, 59, 7, 36], [14, 62, 7, 39], [14, 63, 7, 40], [15, 2, 8, 7], [15, 6, 8, 13, "SHEET_COMPAT_ALL"], [15, 22, 8, 29], [15, 25, 8, 29, "exports"], [15, 32, 8, 29], [15, 33, 8, 29, "SHEET_COMPAT_ALL"], [15, 49, 8, 29], [15, 52, 8, 32], [15, 53, 8, 33], [15, 56, 8, 36], [15, 58, 8, 38], [15, 61, 8, 41], [15, 62, 8, 42], [16, 2, 9, 7], [16, 6, 9, 13, "SHEET_DIMMED_ALWAYS"], [16, 25, 9, 32], [16, 28, 9, 32, "exports"], [16, 35, 9, 32], [16, 36, 9, 32, "SHEET_DIMMED_ALWAYS"], [16, 55, 9, 32], [16, 58, 9, 35], [16, 59, 9, 36], [16, 60, 9, 37], [17, 2, 11, 7], [17, 11, 11, 16, "assertDetentsArrayIsSorted"], [17, 37, 11, 42, "assertDetentsArrayIsSorted"], [17, 38, 11, 43, "array"], [17, 43, 11, 58], [17, 45, 11, 60], [18, 4, 12, 2], [18, 9, 12, 7], [18, 13, 12, 11, "i"], [18, 14, 12, 12], [18, 17, 12, 15], [18, 18, 12, 16], [18, 20, 12, 18, "i"], [18, 21, 12, 19], [18, 24, 12, 22, "array"], [18, 29, 12, 27], [18, 30, 12, 28, "length"], [18, 36, 12, 34], [18, 38, 12, 36, "i"], [18, 39, 12, 37], [18, 41, 12, 39], [18, 43, 12, 41], [19, 6, 13, 4], [19, 10, 13, 8, "array"], [19, 15, 13, 13], [19, 16, 13, 14, "i"], [19, 17, 13, 15], [19, 20, 13, 18], [19, 21, 13, 19], [19, 22, 13, 20], [19, 25, 13, 23, "array"], [19, 30, 13, 28], [19, 31, 13, 29, "i"], [19, 32, 13, 30], [19, 33, 13, 31], [19, 35, 13, 33], [20, 8, 14, 6], [20, 14, 14, 12], [20, 18, 14, 16, "Error"], [20, 23, 14, 21], [20, 24, 15, 8], [20, 88, 16, 6], [20, 89, 16, 7], [21, 6, 17, 4], [22, 4, 18, 2], [23, 2, 19, 0], [25, 2, 21, 0], [26, 2, 22, 0], [27, 2, 23, 7], [27, 11, 23, 16, "resolveSheetAllowedDetents"], [27, 37, 23, 42, "resolveSheetAllowedDetents"], [27, 38, 24, 2, "allowedDetentsCompat"], [27, 58, 24, 58], [27, 60, 25, 12], [28, 4, 26, 2], [28, 8, 26, 6, "Array"], [28, 13, 26, 11], [28, 14, 26, 12, "isArray"], [28, 21, 26, 19], [28, 22, 26, 20, "allowedDetentsCompat"], [28, 42, 26, 40], [28, 43, 26, 41], [28, 45, 26, 43], [29, 6, 27, 4], [29, 10, 27, 8, "Platform"], [29, 31, 27, 16], [29, 32, 27, 17, "OS"], [29, 34, 27, 19], [29, 39, 27, 24], [29, 48, 27, 33], [29, 52, 27, 37, "allowedDetentsCompat"], [29, 72, 27, 57], [29, 73, 27, 58, "length"], [29, 79, 27, 64], [29, 82, 27, 67], [29, 83, 27, 68], [29, 85, 27, 70], [30, 8, 28, 6], [30, 12, 28, 10, "__DEV__"], [30, 19, 28, 17], [30, 21, 28, 19], [31, 10, 29, 8, "console"], [31, 17, 29, 15], [31, 18, 29, 16, "warn"], [31, 22, 29, 20], [31, 23, 30, 10], [31, 120, 31, 8], [31, 121, 31, 9], [32, 8, 32, 6], [33, 8, 33, 6, "allowedDetentsCompat"], [33, 28, 33, 26], [33, 31, 33, 29, "allowedDetentsCompat"], [33, 51, 33, 49], [33, 52, 33, 50, "slice"], [33, 57, 33, 55], [33, 58, 33, 56], [33, 59, 33, 57], [33, 61, 33, 59], [33, 62, 33, 60], [33, 63, 33, 61], [34, 6, 34, 4], [35, 6, 35, 4], [35, 10, 35, 8, "__DEV__"], [35, 17, 35, 15], [35, 19, 35, 17], [36, 8, 36, 6, "assertDetentsArrayIsSorted"], [36, 34, 36, 32], [36, 35, 36, 33, "allowedDetentsCompat"], [36, 55, 36, 53], [36, 56, 36, 54], [37, 6, 37, 4], [38, 6, 38, 4], [38, 13, 38, 11, "allowedDetentsCompat"], [38, 33, 38, 31], [39, 4, 39, 2], [39, 5, 39, 3], [39, 11, 39, 9], [39, 15, 39, 13, "allowedDetentsCompat"], [39, 35, 39, 33], [39, 40, 39, 38], [39, 55, 39, 53], [39, 57, 39, 55], [40, 6, 40, 4], [40, 13, 40, 11, "SHEET_FIT_TO_CONTENTS"], [40, 34, 40, 32], [41, 4, 41, 2], [41, 5, 41, 3], [41, 11, 41, 9], [41, 15, 41, 13, "allowedDetentsCompat"], [41, 35, 41, 33], [41, 40, 41, 38], [41, 47, 41, 45], [41, 49, 41, 47], [42, 6, 42, 4], [42, 13, 42, 11, "SHEET_COMPAT_LARGE"], [42, 31, 42, 29], [43, 4, 43, 2], [43, 5, 43, 3], [43, 11, 43, 9], [43, 15, 43, 13, "allowedDetentsCompat"], [43, 35, 43, 33], [43, 40, 43, 38], [43, 48, 43, 46], [43, 50, 43, 48], [44, 6, 44, 4], [44, 13, 44, 11, "SHEET_COMPAT_MEDIUM"], [44, 32, 44, 30], [45, 4, 45, 2], [45, 5, 45, 3], [45, 11, 45, 9], [45, 15, 45, 13, "allowedDetentsCompat"], [45, 35, 45, 33], [45, 40, 45, 38], [45, 45, 45, 43], [45, 47, 45, 45], [46, 6, 46, 4], [46, 13, 46, 11, "SHEET_COMPAT_ALL"], [46, 29, 46, 27], [47, 4, 47, 2], [47, 5, 47, 3], [47, 11, 47, 9], [48, 6, 48, 4], [49, 6, 49, 4], [49, 13, 49, 11, "SHEET_COMPAT_LARGE"], [49, 31, 49, 29], [50, 4, 50, 2], [51, 2, 51, 0], [52, 2, 53, 7], [52, 11, 53, 16, "resolveSheetLargestUndimmedDetent"], [52, 44, 53, 49, "resolveSheetLargestUndimmedDetent"], [52, 45, 54, 2, "lud"], [52, 48, 54, 53], [52, 50, 55, 2, "lastDetentIndex"], [52, 65, 55, 25], [52, 67, 56, 10], [53, 4, 57, 2], [53, 8, 57, 6], [53, 15, 57, 13, "lud"], [53, 18, 57, 16], [53, 23, 57, 21], [53, 31, 57, 29], [53, 33, 57, 31], [54, 6, 58, 4], [54, 10, 58, 8], [54, 11, 58, 9, "isIndexInClosedRange"], [54, 31, 58, 29], [54, 32, 58, 30, "lud"], [54, 35, 58, 33], [54, 37, 58, 35, "SHEET_DIMMED_ALWAYS"], [54, 56, 58, 54], [54, 58, 58, 56, "lastDetentIndex"], [54, 73, 58, 71], [54, 74, 58, 72], [54, 76, 58, 74], [55, 8, 59, 6], [55, 12, 59, 10, "__DEV__"], [55, 19, 59, 17], [55, 21, 59, 19], [56, 10, 60, 8], [56, 16, 60, 14], [56, 20, 60, 18, "Error"], [56, 25, 60, 23], [56, 26, 61, 10], [56, 145, 62, 8], [56, 146, 62, 9], [57, 8, 63, 6], [58, 8, 64, 6], [59, 8, 65, 6], [59, 15, 65, 13, "SHEET_DIMMED_ALWAYS"], [59, 34, 65, 32], [60, 6, 66, 4], [61, 6, 67, 4], [61, 13, 67, 11, "lud"], [61, 16, 67, 14], [62, 4, 68, 2], [62, 5, 68, 3], [62, 11, 68, 9], [62, 15, 68, 13, "lud"], [62, 18, 68, 16], [62, 23, 68, 21], [62, 29, 68, 27], [62, 31, 68, 29], [63, 6, 69, 4], [63, 13, 69, 11, "lastDetentIndex"], [63, 28, 69, 26], [64, 4, 70, 2], [64, 5, 70, 3], [64, 11, 70, 9], [64, 15, 70, 13, "lud"], [64, 18, 70, 16], [64, 23, 70, 21], [64, 29, 70, 27], [64, 33, 70, 31, "lud"], [64, 36, 70, 34], [64, 41, 70, 39], [64, 46, 70, 44], [64, 48, 70, 46], [65, 6, 71, 4], [65, 13, 71, 11, "SHEET_DIMMED_ALWAYS"], [65, 32, 71, 30], [66, 4, 72, 2], [66, 5, 72, 3], [66, 11, 72, 9], [66, 15, 72, 13, "lud"], [66, 18, 72, 16], [66, 23, 72, 21], [66, 30, 72, 28], [66, 32, 72, 30], [67, 6, 73, 4], [67, 13, 73, 11], [67, 14, 73, 12], [68, 4, 74, 2], [68, 5, 74, 3], [68, 11, 74, 9], [68, 15, 74, 13, "lud"], [68, 18, 74, 16], [68, 23, 74, 21], [68, 31, 74, 29], [68, 33, 74, 31], [69, 6, 75, 4], [69, 13, 75, 11], [69, 14, 75, 12], [70, 4, 76, 2], [70, 5, 76, 3], [70, 11, 76, 9], [71, 6, 77, 4], [72, 6, 78, 4], [72, 13, 78, 11, "SHEET_DIMMED_ALWAYS"], [72, 32, 78, 30], [73, 4, 79, 2], [74, 2, 80, 0], [75, 2, 82, 7], [75, 11, 82, 16, "resolveSheetInitialDetentIndex"], [75, 41, 82, 46, "resolveSheetInitialDetentIndex"], [75, 42, 83, 2, "index"], [75, 47, 83, 47], [75, 49, 84, 2, "lastDetentIndex"], [75, 64, 84, 25], [75, 66, 85, 10], [76, 4, 86, 2], [76, 8, 86, 6, "index"], [76, 13, 86, 11], [76, 18, 86, 16], [76, 24, 86, 22], [76, 26, 86, 24], [77, 6, 87, 4, "index"], [77, 11, 87, 9], [77, 14, 87, 12, "lastDetentIndex"], [77, 29, 87, 27], [78, 4, 88, 2], [78, 5, 88, 3], [78, 11, 88, 9], [78, 15, 88, 13, "index"], [78, 20, 88, 18], [78, 24, 88, 22], [78, 28, 88, 26], [78, 30, 88, 28], [79, 6, 89, 4], [80, 6, 90, 4, "index"], [80, 11, 90, 9], [80, 14, 90, 12], [80, 15, 90, 13], [81, 4, 91, 2], [82, 4, 92, 2], [82, 8, 92, 6], [82, 9, 92, 7, "isIndexInClosedRange"], [82, 29, 92, 27], [82, 30, 92, 28, "index"], [82, 35, 92, 33], [82, 37, 92, 35], [82, 38, 92, 36], [82, 40, 92, 38, "lastDetentIndex"], [82, 55, 92, 53], [82, 56, 92, 54], [82, 58, 92, 56], [83, 6, 93, 4], [83, 10, 93, 8, "__DEV__"], [83, 17, 93, 15], [83, 19, 93, 17], [84, 8, 94, 6], [84, 14, 94, 12], [84, 18, 94, 16, "Error"], [84, 23, 94, 21], [84, 24, 95, 8], [84, 135, 96, 6], [84, 136, 96, 7], [85, 6, 97, 4], [86, 6, 98, 4], [87, 6, 99, 4], [87, 13, 99, 11], [87, 14, 99, 12], [88, 4, 100, 2], [89, 4, 101, 2], [89, 11, 101, 9, "index"], [89, 16, 101, 14], [90, 2, 102, 0], [91, 2, 104, 0], [91, 11, 104, 9, "isIndexInClosedRange"], [91, 31, 104, 29, "isIndexInClosedRange"], [91, 32, 105, 2, "value"], [91, 37, 105, 15], [91, 39, 106, 2, "lowerBound"], [91, 49, 106, 20], [91, 51, 107, 2, "upperBound"], [91, 61, 107, 20], [91, 63, 108, 11], [92, 4, 109, 2], [92, 11, 109, 9, "Number"], [92, 17, 109, 15], [92, 18, 109, 16, "isInteger"], [92, 27, 109, 25], [92, 28, 109, 26, "value"], [92, 33, 109, 31], [92, 34, 109, 32], [92, 38, 109, 36, "value"], [92, 43, 109, 41], [92, 47, 109, 45, "lowerBound"], [92, 57, 109, 55], [92, 61, 109, 59, "value"], [92, 66, 109, 64], [92, 70, 109, 68, "upperBound"], [92, 80, 109, 78], [93, 2, 110, 0], [94, 0, 110, 1], [94, 3]], "functionMap": {"names": ["<global>", "assertDetentsArrayIsSorted", "resolveSheetAllowedDetents", "resolveSheetLargestUndimmedDetent", "resolveSheetInitialDetentIndex", "isIndexInClosedRange"], "mappings": "AAA;OCU;CDQ;OEI;CF4B;OGE;CH2B;OIE;CJoB;AKE;CLM"}}, "type": "js/module"}]}