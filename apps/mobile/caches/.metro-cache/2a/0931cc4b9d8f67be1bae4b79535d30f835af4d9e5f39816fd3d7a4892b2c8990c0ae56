{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WheelDevice = exports.TouchEventType = exports.EventTypes = void 0;\n  let EventTypes = exports.EventTypes = void 0;\n  (function (EventTypes) {\n    EventTypes[EventTypes[\"DOWN\"] = 0] = \"DOWN\";\n    EventTypes[EventTypes[\"ADDITIONAL_POINTER_DOWN\"] = 1] = \"ADDITIONAL_POINTER_DOWN\";\n    EventTypes[EventTypes[\"UP\"] = 2] = \"UP\";\n    EventTypes[EventTypes[\"ADDITIONAL_POINTER_UP\"] = 3] = \"ADDITIONAL_POINTER_UP\";\n    EventTypes[EventTypes[\"MOVE\"] = 4] = \"MOVE\";\n    EventTypes[EventTypes[\"ENTER\"] = 5] = \"ENTER\";\n    EventTypes[EventTypes[\"LEAVE\"] = 6] = \"LEAVE\";\n    EventTypes[EventTypes[\"CANCEL\"] = 7] = \"CANCEL\";\n  })(EventTypes || (exports.EventTypes = EventTypes = {}));\n  let TouchEventType = exports.TouchEventType = void 0;\n  (function (TouchEventType) {\n    TouchEventType[TouchEventType[\"UNDETERMINED\"] = 0] = \"UNDETERMINED\";\n    TouchEventType[TouchEventType[\"DOWN\"] = 1] = \"DOWN\";\n    TouchEventType[TouchEventType[\"MOVE\"] = 2] = \"MOVE\";\n    TouchEventType[TouchEventType[\"UP\"] = 3] = \"UP\";\n    TouchEventType[TouchEventType[\"CANCELLED\"] = 4] = \"CANCELLED\";\n  })(TouchEventType || (exports.TouchEventType = TouchEventType = {}));\n  let WheelDevice = exports.WheelDevice = void 0;\n  (function (WheelDevice) {\n    WheelDevice[WheelDevice[\"UNDETERMINED\"] = 0] = \"UNDETERMINED\";\n    WheelDevice[WheelDevice[\"MOUSE\"] = 1] = \"MOUSE\";\n    WheelDevice[WheelDevice[\"TOUCHPAD\"] = 2] = \"TOUCHPAD\";\n  })(WheelDevice || (exports.WheelDevice = WheelDevice = {}));\n});", "lineCount": 31, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "EventTypes"], [6, 16, 1, 21], [6, 19, 1, 21, "exports"], [6, 26, 1, 21], [6, 27, 1, 21, "EventTypes"], [6, 37, 1, 21], [7, 2, 3, 0], [7, 3, 3, 1], [7, 13, 3, 11, "EventTypes"], [7, 23, 3, 21], [7, 25, 3, 23], [8, 4, 4, 2, "EventTypes"], [8, 14, 4, 12], [8, 15, 4, 13, "EventTypes"], [8, 25, 4, 23], [8, 26, 4, 24], [8, 32, 4, 30], [8, 33, 4, 31], [8, 36, 4, 34], [8, 37, 4, 35], [8, 38, 4, 36], [8, 41, 4, 39], [8, 47, 4, 45], [9, 4, 5, 2, "EventTypes"], [9, 14, 5, 12], [9, 15, 5, 13, "EventTypes"], [9, 25, 5, 23], [9, 26, 5, 24], [9, 51, 5, 49], [9, 52, 5, 50], [9, 55, 5, 53], [9, 56, 5, 54], [9, 57, 5, 55], [9, 60, 5, 58], [9, 85, 5, 83], [10, 4, 6, 2, "EventTypes"], [10, 14, 6, 12], [10, 15, 6, 13, "EventTypes"], [10, 25, 6, 23], [10, 26, 6, 24], [10, 30, 6, 28], [10, 31, 6, 29], [10, 34, 6, 32], [10, 35, 6, 33], [10, 36, 6, 34], [10, 39, 6, 37], [10, 43, 6, 41], [11, 4, 7, 2, "EventTypes"], [11, 14, 7, 12], [11, 15, 7, 13, "EventTypes"], [11, 25, 7, 23], [11, 26, 7, 24], [11, 49, 7, 47], [11, 50, 7, 48], [11, 53, 7, 51], [11, 54, 7, 52], [11, 55, 7, 53], [11, 58, 7, 56], [11, 81, 7, 79], [12, 4, 8, 2, "EventTypes"], [12, 14, 8, 12], [12, 15, 8, 13, "EventTypes"], [12, 25, 8, 23], [12, 26, 8, 24], [12, 32, 8, 30], [12, 33, 8, 31], [12, 36, 8, 34], [12, 37, 8, 35], [12, 38, 8, 36], [12, 41, 8, 39], [12, 47, 8, 45], [13, 4, 9, 2, "EventTypes"], [13, 14, 9, 12], [13, 15, 9, 13, "EventTypes"], [13, 25, 9, 23], [13, 26, 9, 24], [13, 33, 9, 31], [13, 34, 9, 32], [13, 37, 9, 35], [13, 38, 9, 36], [13, 39, 9, 37], [13, 42, 9, 40], [13, 49, 9, 47], [14, 4, 10, 2, "EventTypes"], [14, 14, 10, 12], [14, 15, 10, 13, "EventTypes"], [14, 25, 10, 23], [14, 26, 10, 24], [14, 33, 10, 31], [14, 34, 10, 32], [14, 37, 10, 35], [14, 38, 10, 36], [14, 39, 10, 37], [14, 42, 10, 40], [14, 49, 10, 47], [15, 4, 11, 2, "EventTypes"], [15, 14, 11, 12], [15, 15, 11, 13, "EventTypes"], [15, 25, 11, 23], [15, 26, 11, 24], [15, 34, 11, 32], [15, 35, 11, 33], [15, 38, 11, 36], [15, 39, 11, 37], [15, 40, 11, 38], [15, 43, 11, 41], [15, 51, 11, 49], [16, 2, 12, 0], [16, 3, 12, 1], [16, 5, 12, 3, "EventTypes"], [16, 15, 12, 13], [16, 20, 12, 13, "exports"], [16, 27, 12, 13], [16, 28, 12, 13, "EventTypes"], [16, 38, 12, 13], [16, 41, 12, 18, "EventTypes"], [16, 51, 12, 28], [16, 54, 12, 31], [16, 55, 12, 32], [16, 56, 12, 33], [16, 57, 12, 34], [16, 58, 12, 35], [17, 2, 14, 7], [17, 6, 14, 11, "TouchEventType"], [17, 20, 14, 25], [17, 23, 14, 25, "exports"], [17, 30, 14, 25], [17, 31, 14, 25, "TouchEventType"], [17, 45, 14, 25], [18, 2, 16, 0], [18, 3, 16, 1], [18, 13, 16, 11, "TouchEventType"], [18, 27, 16, 25], [18, 29, 16, 27], [19, 4, 17, 2, "TouchEventType"], [19, 18, 17, 16], [19, 19, 17, 17, "TouchEventType"], [19, 33, 17, 31], [19, 34, 17, 32], [19, 48, 17, 46], [19, 49, 17, 47], [19, 52, 17, 50], [19, 53, 17, 51], [19, 54, 17, 52], [19, 57, 17, 55], [19, 71, 17, 69], [20, 4, 18, 2, "TouchEventType"], [20, 18, 18, 16], [20, 19, 18, 17, "TouchEventType"], [20, 33, 18, 31], [20, 34, 18, 32], [20, 40, 18, 38], [20, 41, 18, 39], [20, 44, 18, 42], [20, 45, 18, 43], [20, 46, 18, 44], [20, 49, 18, 47], [20, 55, 18, 53], [21, 4, 19, 2, "TouchEventType"], [21, 18, 19, 16], [21, 19, 19, 17, "TouchEventType"], [21, 33, 19, 31], [21, 34, 19, 32], [21, 40, 19, 38], [21, 41, 19, 39], [21, 44, 19, 42], [21, 45, 19, 43], [21, 46, 19, 44], [21, 49, 19, 47], [21, 55, 19, 53], [22, 4, 20, 2, "TouchEventType"], [22, 18, 20, 16], [22, 19, 20, 17, "TouchEventType"], [22, 33, 20, 31], [22, 34, 20, 32], [22, 38, 20, 36], [22, 39, 20, 37], [22, 42, 20, 40], [22, 43, 20, 41], [22, 44, 20, 42], [22, 47, 20, 45], [22, 51, 20, 49], [23, 4, 21, 2, "TouchEventType"], [23, 18, 21, 16], [23, 19, 21, 17, "TouchEventType"], [23, 33, 21, 31], [23, 34, 21, 32], [23, 45, 21, 43], [23, 46, 21, 44], [23, 49, 21, 47], [23, 50, 21, 48], [23, 51, 21, 49], [23, 54, 21, 52], [23, 65, 21, 63], [24, 2, 22, 0], [24, 3, 22, 1], [24, 5, 22, 3, "TouchEventType"], [24, 19, 22, 17], [24, 24, 22, 17, "exports"], [24, 31, 22, 17], [24, 32, 22, 17, "TouchEventType"], [24, 46, 22, 17], [24, 49, 22, 22, "TouchEventType"], [24, 63, 22, 36], [24, 66, 22, 39], [24, 67, 22, 40], [24, 68, 22, 41], [24, 69, 22, 42], [24, 70, 22, 43], [25, 2, 24, 7], [25, 6, 24, 11, "WheelDevice"], [25, 17, 24, 22], [25, 20, 24, 22, "exports"], [25, 27, 24, 22], [25, 28, 24, 22, "WheelDevice"], [25, 39, 24, 22], [26, 2, 26, 0], [26, 3, 26, 1], [26, 13, 26, 11, "WheelDevice"], [26, 24, 26, 22], [26, 26, 26, 24], [27, 4, 27, 2, "WheelDevice"], [27, 15, 27, 13], [27, 16, 27, 14, "WheelDevice"], [27, 27, 27, 25], [27, 28, 27, 26], [27, 42, 27, 40], [27, 43, 27, 41], [27, 46, 27, 44], [27, 47, 27, 45], [27, 48, 27, 46], [27, 51, 27, 49], [27, 65, 27, 63], [28, 4, 28, 2, "WheelDevice"], [28, 15, 28, 13], [28, 16, 28, 14, "WheelDevice"], [28, 27, 28, 25], [28, 28, 28, 26], [28, 35, 28, 33], [28, 36, 28, 34], [28, 39, 28, 37], [28, 40, 28, 38], [28, 41, 28, 39], [28, 44, 28, 42], [28, 51, 28, 49], [29, 4, 29, 2, "WheelDevice"], [29, 15, 29, 13], [29, 16, 29, 14, "WheelDevice"], [29, 27, 29, 25], [29, 28, 29, 26], [29, 38, 29, 36], [29, 39, 29, 37], [29, 42, 29, 40], [29, 43, 29, 41], [29, 44, 29, 42], [29, 47, 29, 45], [29, 57, 29, 55], [30, 2, 30, 0], [30, 3, 30, 1], [30, 5, 30, 3, "WheelDevice"], [30, 16, 30, 14], [30, 21, 30, 14, "exports"], [30, 28, 30, 14], [30, 29, 30, 14, "WheelDevice"], [30, 40, 30, 14], [30, 43, 30, 19, "WheelDevice"], [30, 54, 30, 30], [30, 57, 30, 33], [30, 58, 30, 34], [30, 59, 30, 35], [30, 60, 30, 36], [30, 61, 30, 37], [31, 0, 30, 38], [31, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;CCE;CDS;CCI;CDM;CCI;CDI"}}, "type": "js/module"}]}