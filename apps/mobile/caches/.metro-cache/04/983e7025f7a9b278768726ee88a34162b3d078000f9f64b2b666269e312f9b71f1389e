{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useKeyedChildListeners = useKeyedChildListeners;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook which lets child navigators add getters to be called for obtaining rehydrated state.\n   */\n  function useKeyedChildListeners() {\n    var _React$useRef = React.useRef(Object.assign(Object.create(null), {\n        getState: {},\n        beforeRemove: {}\n      })),\n      keyedListeners = _React$useRef.current;\n    var addKeyedListener = React.useCallback((type, key, listener) => {\n      // @ts-expect-error: according to ref stated above you can use `key` to index type\n      keyedListeners[type][key] = listener;\n      return () => {\n        // @ts-expect-error: according to ref stated above you can use `key` to index type\n        keyedListeners[type][key] = undefined;\n      };\n    }, [keyedListeners]);\n    return {\n      keyedListeners,\n      addKeyedListener\n    };\n  }\n});", "lineCount": 32, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useKeyedChildListeners"], [7, 32, 1, 13], [7, 35, 1, 13, "useKeyedChildListeners"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 11, 3, 31, "_interopRequireWildcard"], [9, 35, 3, 31, "e"], [9, 36, 3, 31], [9, 38, 3, 31, "t"], [9, 39, 3, 31], [9, 68, 3, 31, "WeakMap"], [9, 75, 3, 31], [9, 81, 3, 31, "r"], [9, 82, 3, 31], [9, 89, 3, 31, "WeakMap"], [9, 96, 3, 31], [9, 100, 3, 31, "n"], [9, 101, 3, 31], [9, 108, 3, 31, "WeakMap"], [9, 115, 3, 31], [9, 127, 3, 31, "_interopRequireWildcard"], [9, 150, 3, 31], [9, 162, 3, 31, "_interopRequireWildcard"], [9, 163, 3, 31, "e"], [9, 164, 3, 31], [9, 166, 3, 31, "t"], [9, 167, 3, 31], [9, 176, 3, 31, "t"], [9, 177, 3, 31], [9, 181, 3, 31, "e"], [9, 182, 3, 31], [9, 186, 3, 31, "e"], [9, 187, 3, 31], [9, 188, 3, 31, "__esModule"], [9, 198, 3, 31], [9, 207, 3, 31, "e"], [9, 208, 3, 31], [9, 214, 3, 31, "o"], [9, 215, 3, 31], [9, 217, 3, 31, "i"], [9, 218, 3, 31], [9, 220, 3, 31, "f"], [9, 221, 3, 31], [9, 226, 3, 31, "__proto__"], [9, 235, 3, 31], [9, 243, 3, 31, "default"], [9, 250, 3, 31], [9, 252, 3, 31, "e"], [9, 253, 3, 31], [9, 270, 3, 31, "e"], [9, 271, 3, 31], [9, 294, 3, 31, "e"], [9, 295, 3, 31], [9, 320, 3, 31, "e"], [9, 321, 3, 31], [9, 330, 3, 31, "f"], [9, 331, 3, 31], [9, 337, 3, 31, "o"], [9, 338, 3, 31], [9, 341, 3, 31, "t"], [9, 342, 3, 31], [9, 345, 3, 31, "n"], [9, 346, 3, 31], [9, 349, 3, 31, "r"], [9, 350, 3, 31], [9, 358, 3, 31, "o"], [9, 359, 3, 31], [9, 360, 3, 31, "has"], [9, 363, 3, 31], [9, 364, 3, 31, "e"], [9, 365, 3, 31], [9, 375, 3, 31, "o"], [9, 376, 3, 31], [9, 377, 3, 31, "get"], [9, 380, 3, 31], [9, 381, 3, 31, "e"], [9, 382, 3, 31], [9, 385, 3, 31, "o"], [9, 386, 3, 31], [9, 387, 3, 31, "set"], [9, 390, 3, 31], [9, 391, 3, 31, "e"], [9, 392, 3, 31], [9, 394, 3, 31, "f"], [9, 395, 3, 31], [9, 409, 3, 31, "_t"], [9, 411, 3, 31], [9, 415, 3, 31, "e"], [9, 416, 3, 31], [9, 432, 3, 31, "_t"], [9, 434, 3, 31], [9, 441, 3, 31, "hasOwnProperty"], [9, 455, 3, 31], [9, 456, 3, 31, "call"], [9, 460, 3, 31], [9, 461, 3, 31, "e"], [9, 462, 3, 31], [9, 464, 3, 31, "_t"], [9, 466, 3, 31], [9, 473, 3, 31, "i"], [9, 474, 3, 31], [9, 478, 3, 31, "o"], [9, 479, 3, 31], [9, 482, 3, 31, "Object"], [9, 488, 3, 31], [9, 489, 3, 31, "defineProperty"], [9, 503, 3, 31], [9, 508, 3, 31, "Object"], [9, 514, 3, 31], [9, 515, 3, 31, "getOwnPropertyDescriptor"], [9, 539, 3, 31], [9, 540, 3, 31, "e"], [9, 541, 3, 31], [9, 543, 3, 31, "_t"], [9, 545, 3, 31], [9, 552, 3, 31, "i"], [9, 553, 3, 31], [9, 554, 3, 31, "get"], [9, 557, 3, 31], [9, 561, 3, 31, "i"], [9, 562, 3, 31], [9, 563, 3, 31, "set"], [9, 566, 3, 31], [9, 570, 3, 31, "o"], [9, 571, 3, 31], [9, 572, 3, 31, "f"], [9, 573, 3, 31], [9, 575, 3, 31, "_t"], [9, 577, 3, 31], [9, 579, 3, 31, "i"], [9, 580, 3, 31], [9, 584, 3, 31, "f"], [9, 585, 3, 31], [9, 586, 3, 31, "_t"], [9, 588, 3, 31], [9, 592, 3, 31, "e"], [9, 593, 3, 31], [9, 594, 3, 31, "_t"], [9, 596, 3, 31], [9, 607, 3, 31, "f"], [9, 608, 3, 31], [9, 613, 3, 31, "e"], [9, 614, 3, 31], [9, 616, 3, 31, "t"], [9, 617, 3, 31], [10, 2, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 2, 7, 7], [13, 11, 7, 16, "useKeyedChildListeners"], [13, 33, 7, 38, "useKeyedChildListeners"], [13, 34, 7, 38], [13, 36, 7, 41], [14, 4, 8, 2], [14, 8, 8, 2, "_React$useRef"], [14, 21, 8, 2], [14, 24, 10, 6, "React"], [14, 29, 10, 11], [14, 30, 10, 12, "useRef"], [14, 36, 10, 18], [14, 37, 10, 19, "Object"], [14, 43, 10, 25], [14, 44, 10, 26, "assign"], [14, 50, 10, 32], [14, 51, 10, 33, "Object"], [14, 57, 10, 39], [14, 58, 10, 40, "create"], [14, 64, 10, 46], [14, 65, 10, 47], [14, 69, 10, 51], [14, 70, 10, 52], [14, 72, 10, 54], [15, 8, 11, 4, "getState"], [15, 16, 11, 12], [15, 18, 11, 14], [15, 19, 11, 15], [15, 20, 11, 16], [16, 8, 12, 4, "beforeRemove"], [16, 20, 12, 16], [16, 22, 12, 18], [16, 23, 12, 19], [17, 6, 13, 2], [17, 7, 13, 3], [17, 8, 13, 4], [17, 9, 13, 5], [18, 6, 9, 13, "keyedListeners"], [18, 20, 9, 27], [18, 23, 9, 27, "_React$useRef"], [18, 36, 9, 27], [18, 37, 9, 4, "current"], [18, 44, 9, 11], [19, 4, 14, 2], [19, 8, 14, 8, "addKeyedListener"], [19, 24, 14, 24], [19, 27, 14, 27, "React"], [19, 32, 14, 32], [19, 33, 14, 33, "useCallback"], [19, 44, 14, 44], [19, 45, 14, 45], [19, 46, 14, 46, "type"], [19, 50, 14, 50], [19, 52, 14, 52, "key"], [19, 55, 14, 55], [19, 57, 14, 57, "listener"], [19, 65, 14, 65], [19, 70, 14, 70], [20, 6, 15, 4], [21, 6, 16, 4, "keyedListeners"], [21, 20, 16, 18], [21, 21, 16, 19, "type"], [21, 25, 16, 23], [21, 26, 16, 24], [21, 27, 16, 25, "key"], [21, 30, 16, 28], [21, 31, 16, 29], [21, 34, 16, 32, "listener"], [21, 42, 16, 40], [22, 6, 17, 4], [22, 13, 17, 11], [22, 19, 17, 17], [23, 8, 18, 6], [24, 8, 19, 6, "keyedListeners"], [24, 22, 19, 20], [24, 23, 19, 21, "type"], [24, 27, 19, 25], [24, 28, 19, 26], [24, 29, 19, 27, "key"], [24, 32, 19, 30], [24, 33, 19, 31], [24, 36, 19, 34, "undefined"], [24, 45, 19, 43], [25, 6, 20, 4], [25, 7, 20, 5], [26, 4, 21, 2], [26, 5, 21, 3], [26, 7, 21, 5], [26, 8, 21, 6, "keyedListeners"], [26, 22, 21, 20], [26, 23, 21, 21], [26, 24, 21, 22], [27, 4, 22, 2], [27, 11, 22, 9], [28, 6, 23, 4, "keyedListeners"], [28, 20, 23, 18], [29, 6, 24, 4, "addKeyedListener"], [30, 4, 25, 2], [30, 5, 25, 3], [31, 2, 26, 0], [32, 0, 26, 1], [32, 3]], "functionMap": {"names": ["<global>", "useKeyedChildListeners", "addKeyedListener", "<anonymous>"], "mappings": "AAA;OCM;6CCO;WCG;KDG;GDC;CDK"}}, "type": "js/module"}]}