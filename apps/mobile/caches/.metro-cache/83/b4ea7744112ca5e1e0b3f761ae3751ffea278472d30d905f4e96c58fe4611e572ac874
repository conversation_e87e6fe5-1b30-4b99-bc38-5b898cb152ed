{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var id = 0;\n  function _classPrivateFieldKey(e) {\n    return \"__private_\" + id++ + \"_\" + e;\n  }\n  module.exports = _classPrivateFieldKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 7, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "id"], [2, 8, 1, 6], [2, 11, 1, 9], [2, 12, 1, 10], [3, 2, 2, 0], [3, 11, 2, 9, "_classPrivateFieldKey"], [3, 32, 2, 30, "_classPrivateFieldKey"], [3, 33, 2, 31, "e"], [3, 34, 2, 32], [3, 36, 2, 34], [4, 4, 3, 2], [4, 11, 3, 9], [4, 23, 3, 21], [4, 26, 3, 24, "id"], [4, 28, 3, 26], [4, 30, 3, 28], [4, 33, 3, 31], [4, 36, 3, 34], [4, 39, 3, 37, "e"], [4, 40, 3, 38], [5, 2, 4, 0], [6, 2, 5, 0, "module"], [6, 8, 5, 6], [6, 9, 5, 7, "exports"], [6, 16, 5, 14], [6, 19, 5, 17, "_classPrivateFieldKey"], [6, 40, 5, 38], [6, 42, 5, 40, "module"], [6, 48, 5, 46], [6, 49, 5, 47, "exports"], [6, 56, 5, 54], [6, 57, 5, 55, "__esModule"], [6, 67, 5, 65], [6, 70, 5, 68], [6, 74, 5, 72], [6, 76, 5, 74, "module"], [6, 82, 5, 80], [6, 83, 5, 81, "exports"], [6, 90, 5, 88], [6, 91, 5, 89], [6, 100, 5, 98], [6, 101, 5, 99], [6, 104, 5, 102, "module"], [6, 110, 5, 108], [6, 111, 5, 109, "exports"], [6, 118, 5, 116], [7, 0, 5, 117], [7, 3]], "functionMap": {"names": ["<global>", "_classPrivateFieldKey"], "mappings": "AAA;ACC;CDE"}}, "type": "js/module"}]}