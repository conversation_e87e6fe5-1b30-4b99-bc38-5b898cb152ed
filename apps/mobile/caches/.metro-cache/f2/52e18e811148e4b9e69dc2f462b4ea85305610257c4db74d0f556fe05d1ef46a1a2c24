{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 46, "index": 46}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[1], \"./GestureHandler\"));\n  /**\n   * The base class for **Rotation** and **Pinch** gesture handlers.\n   */\n\n  class IndiscreteGestureHandler extends _GestureHandler.default {\n    get shouldEnableGestureOnSetup() {\n      return false;\n    }\n    updateGestureConfig({\n      minPointers = 2,\n      maxPointers = 2,\n      ...props\n    }) {\n      return super.updateGestureConfig({\n        minPointers,\n        maxPointers,\n        ...props\n      });\n    }\n    isGestureEnabledForEvent({\n      minPointers,\n      maxPointers\n    }, _recognizer, {\n      maxPointers: pointerLength\n    }) {\n      if (pointerLength > maxPointers) {\n        return {\n          failed: true\n        };\n      }\n      const validPointerCount = pointerLength >= minPointers;\n      return {\n        success: validPointerCount\n      };\n    }\n  }\n  var _default = exports.default = IndiscreteGestureHandler;\n});", "lineCount": 45, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_Gesture<PERSON><PERSON>ler"], [7, 21, 1, 0], [7, 24, 1, 0, "_interopRequireDefault"], [7, 46, 1, 0], [7, 47, 1, 0, "require"], [7, 54, 1, 0], [7, 55, 1, 0, "_dependencyMap"], [7, 69, 1, 0], [8, 2, 2, 0], [9, 0, 3, 0], [10, 0, 4, 0], [12, 2, 6, 0], [12, 8, 6, 6, "IndiscreteGestureHandler"], [12, 32, 6, 30], [12, 41, 6, 39, "Gesture<PERSON>andler"], [12, 64, 6, 53], [12, 65, 6, 54], [13, 4, 7, 2], [13, 8, 7, 6, "shouldEnableGestureOnSetup"], [13, 34, 7, 32, "shouldEnableGestureOnSetup"], [13, 35, 7, 32], [13, 37, 7, 35], [14, 6, 8, 4], [14, 13, 8, 11], [14, 18, 8, 16], [15, 4, 9, 2], [16, 4, 11, 2, "updateGestureConfig"], [16, 23, 11, 21, "updateGestureConfig"], [16, 24, 11, 22], [17, 6, 12, 4, "minPointers"], [17, 17, 12, 15], [17, 20, 12, 18], [17, 21, 12, 19], [18, 6, 13, 4, "maxPointers"], [18, 17, 13, 15], [18, 20, 13, 18], [18, 21, 13, 19], [19, 6, 14, 4], [19, 9, 14, 7, "props"], [20, 4, 15, 2], [20, 5, 15, 3], [20, 7, 15, 5], [21, 6, 16, 4], [21, 13, 16, 11], [21, 18, 16, 16], [21, 19, 16, 17, "updateGestureConfig"], [21, 38, 16, 36], [21, 39, 16, 37], [22, 8, 17, 6, "minPointers"], [22, 19, 17, 17], [23, 8, 18, 6, "maxPointers"], [23, 19, 18, 17], [24, 8, 19, 6], [24, 11, 19, 9, "props"], [25, 6, 20, 4], [25, 7, 20, 5], [25, 8, 20, 6], [26, 4, 21, 2], [27, 4, 23, 2, "isGestureEnabledForEvent"], [27, 28, 23, 26, "isGestureEnabledForEvent"], [27, 29, 23, 27], [28, 6, 24, 4, "minPointers"], [28, 17, 24, 15], [29, 6, 25, 4, "maxPointers"], [30, 4, 26, 2], [30, 5, 26, 3], [30, 7, 26, 5, "_recognizer"], [30, 18, 26, 16], [30, 20, 26, 18], [31, 6, 27, 4, "maxPointers"], [31, 17, 27, 15], [31, 19, 27, 17, "pointer<PERSON><PERSON><PERSON>"], [32, 4, 28, 2], [32, 5, 28, 3], [32, 7, 28, 5], [33, 6, 29, 4], [33, 10, 29, 8, "pointer<PERSON><PERSON><PERSON>"], [33, 23, 29, 21], [33, 26, 29, 24, "maxPointers"], [33, 37, 29, 35], [33, 39, 29, 37], [34, 8, 30, 6], [34, 15, 30, 13], [35, 10, 31, 8, "failed"], [35, 16, 31, 14], [35, 18, 31, 16], [36, 8, 32, 6], [36, 9, 32, 7], [37, 6, 33, 4], [38, 6, 35, 4], [38, 12, 35, 10, "validPointerCount"], [38, 29, 35, 27], [38, 32, 35, 30, "pointer<PERSON><PERSON><PERSON>"], [38, 45, 35, 43], [38, 49, 35, 47, "minPointers"], [38, 60, 35, 58], [39, 6, 36, 4], [39, 13, 36, 11], [40, 8, 37, 6, "success"], [40, 15, 37, 13], [40, 17, 37, 15, "validPointerCount"], [41, 6, 38, 4], [41, 7, 38, 5], [42, 4, 39, 2], [43, 2, 41, 0], [44, 2, 41, 1], [44, 6, 41, 1, "_default"], [44, 14, 41, 1], [44, 17, 41, 1, "exports"], [44, 24, 41, 1], [44, 25, 41, 1, "default"], [44, 32, 41, 1], [44, 35, 43, 15, "IndiscreteGestureHandler"], [44, 59, 43, 39], [45, 0, 43, 39], [45, 3]], "functionMap": {"names": ["<global>", "IndiscreteGestureHandler", "get__shouldEnableGestureOnSetup", "updateGestureConfig", "isGestureEnabledForEvent"], "mappings": "AAA;ACK;ECC;GDE;EEE;GFU;EGE;GHgB;CDE"}}, "type": "js/module"}]}