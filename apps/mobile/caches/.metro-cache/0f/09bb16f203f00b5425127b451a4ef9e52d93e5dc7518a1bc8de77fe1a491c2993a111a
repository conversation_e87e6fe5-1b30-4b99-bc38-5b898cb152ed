{"dependencies": [{"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 18}, "end": {"line": 13, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var invariant = require(_dependencyMap[0], \"invariant\");\n  function reject(error) {\n    setTimeout(() => {\n      throw error;\n    }, 0);\n  }\n  var InteractionManagerStub = {\n    Events: {\n      interactionStart: 'interactionStart',\n      interactionComplete: 'interactionComplete'\n    },\n    runAfterInteractions(task) {\n      var immediateID;\n      var promise = new Promise(resolve => {\n        immediateID = setImmediate(() => {\n          if (typeof task === 'object' && task !== null) {\n            if (typeof task.gen === 'function') {\n              task.gen().then(resolve, reject);\n            } else if (typeof task.run === 'function') {\n              try {\n                task.run();\n                resolve();\n              } catch (error) {\n                reject(error);\n              }\n            } else {\n              reject(new TypeError(`Task \"${task.name}\" missing gen or run.`));\n            }\n          } else if (typeof task === 'function') {\n            try {\n              task();\n              resolve();\n            } catch (error) {\n              reject(error);\n            }\n          } else {\n            reject(new TypeError('Invalid task of type: ' + typeof task));\n          }\n        });\n      });\n      return {\n        then: promise.then.bind(promise),\n        cancel() {\n          clearImmediate(immediateID);\n        }\n      };\n    },\n    createInteractionHandle() {\n      return -1;\n    },\n    clearInteractionHandle(handle) {\n      invariant(!!handle, 'InteractionManager: Must provide a handle to clear.');\n    },\n    addListener() {\n      return {\n        remove() {}\n      };\n    },\n    setDeadline(deadline) {}\n  };\n  var _default = exports.default = InteractionManagerStub;\n});", "lineCount": 67, "map": [[6, 2, 13, 0], [6, 6, 13, 6, "invariant"], [6, 15, 13, 15], [6, 18, 13, 18, "require"], [6, 25, 13, 25], [6, 26, 13, 25, "_dependencyMap"], [6, 40, 13, 25], [6, 56, 13, 37], [6, 57, 13, 38], [7, 2, 30, 0], [7, 11, 30, 9, "reject"], [7, 17, 30, 15, "reject"], [7, 18, 30, 16, "error"], [7, 23, 30, 28], [7, 25, 30, 36], [8, 4, 31, 2, "setTimeout"], [8, 14, 31, 12], [8, 15, 31, 13], [8, 21, 31, 19], [9, 6, 32, 4], [9, 12, 32, 10, "error"], [9, 17, 32, 15], [10, 4, 33, 2], [10, 5, 33, 3], [10, 7, 33, 5], [10, 8, 33, 6], [10, 9, 33, 7], [11, 2, 34, 0], [12, 2, 87, 0], [12, 6, 87, 6, "InteractionManagerStub"], [12, 28, 87, 28], [12, 31, 87, 31], [13, 4, 88, 2, "Events"], [13, 10, 88, 8], [13, 12, 88, 10], [14, 6, 89, 4, "interactionStart"], [14, 22, 89, 20], [14, 24, 89, 22], [14, 42, 89, 40], [15, 6, 90, 4, "interactionComplete"], [15, 25, 90, 23], [15, 27, 90, 25], [16, 4, 91, 2], [16, 5, 91, 3], [17, 4, 99, 2, "runAfterInteractions"], [17, 24, 99, 22, "runAfterInteractions"], [17, 25, 99, 23, "task"], [17, 29, 99, 34], [17, 31, 106, 4], [18, 6, 107, 4], [18, 10, 107, 8, "immediateID"], [18, 21, 107, 32], [19, 6, 108, 4], [19, 10, 108, 10, "promise"], [19, 17, 108, 17], [19, 20, 108, 20], [19, 24, 108, 24, "Promise"], [19, 31, 108, 31], [19, 32, 108, 32, "resolve"], [19, 39, 108, 39], [19, 43, 108, 43], [20, 8, 109, 6, "immediateID"], [20, 19, 109, 17], [20, 22, 109, 20, "setImmediate"], [20, 34, 109, 32], [20, 35, 109, 33], [20, 41, 109, 39], [21, 10, 110, 8], [21, 14, 110, 12], [21, 21, 110, 19, "task"], [21, 25, 110, 23], [21, 30, 110, 28], [21, 38, 110, 36], [21, 42, 110, 40, "task"], [21, 46, 110, 44], [21, 51, 110, 49], [21, 55, 110, 53], [21, 57, 110, 55], [22, 12, 111, 10], [22, 16, 111, 14], [22, 23, 111, 21, "task"], [22, 27, 111, 25], [22, 28, 111, 26, "gen"], [22, 31, 111, 29], [22, 36, 111, 34], [22, 46, 111, 44], [22, 48, 111, 46], [23, 14, 112, 12, "task"], [23, 18, 112, 16], [23, 19, 112, 17, "gen"], [23, 22, 112, 20], [23, 23, 112, 21], [23, 24, 112, 22], [23, 25, 112, 23, "then"], [23, 29, 112, 27], [23, 30, 112, 28, "resolve"], [23, 37, 112, 35], [23, 39, 112, 37, "reject"], [23, 45, 112, 43], [23, 46, 112, 44], [24, 12, 113, 10], [24, 13, 113, 11], [24, 19, 113, 17], [24, 23, 113, 21], [24, 30, 113, 28, "task"], [24, 34, 113, 32], [24, 35, 113, 33, "run"], [24, 38, 113, 36], [24, 43, 113, 41], [24, 53, 113, 51], [24, 55, 113, 53], [25, 14, 114, 12], [25, 18, 114, 16], [26, 16, 115, 14, "task"], [26, 20, 115, 18], [26, 21, 115, 19, "run"], [26, 24, 115, 22], [26, 25, 115, 23], [26, 26, 115, 24], [27, 16, 116, 14, "resolve"], [27, 23, 116, 21], [27, 24, 116, 22], [27, 25, 116, 23], [28, 14, 117, 12], [28, 15, 117, 13], [28, 16, 117, 14], [28, 23, 117, 21, "error"], [28, 28, 117, 26], [28, 30, 117, 28], [29, 16, 118, 14, "reject"], [29, 22, 118, 20], [29, 23, 118, 21, "error"], [29, 28, 118, 26], [29, 29, 118, 27], [30, 14, 119, 12], [31, 12, 120, 10], [31, 13, 120, 11], [31, 19, 120, 17], [32, 14, 121, 12, "reject"], [32, 20, 121, 18], [32, 21, 121, 19], [32, 25, 121, 23, "TypeError"], [32, 34, 121, 32], [32, 35, 121, 33], [32, 44, 121, 42, "task"], [32, 48, 121, 46], [32, 49, 121, 47, "name"], [32, 53, 121, 51], [32, 76, 121, 74], [32, 77, 121, 75], [32, 78, 121, 76], [33, 12, 122, 10], [34, 10, 123, 8], [34, 11, 123, 9], [34, 17, 123, 15], [34, 21, 123, 19], [34, 28, 123, 26, "task"], [34, 32, 123, 30], [34, 37, 123, 35], [34, 47, 123, 45], [34, 49, 123, 47], [35, 12, 124, 10], [35, 16, 124, 14], [36, 14, 125, 12, "task"], [36, 18, 125, 16], [36, 19, 125, 17], [36, 20, 125, 18], [37, 14, 126, 12, "resolve"], [37, 21, 126, 19], [37, 22, 126, 20], [37, 23, 126, 21], [38, 12, 127, 10], [38, 13, 127, 11], [38, 14, 127, 12], [38, 21, 127, 19, "error"], [38, 26, 127, 24], [38, 28, 127, 26], [39, 14, 128, 12, "reject"], [39, 20, 128, 18], [39, 21, 128, 19, "error"], [39, 26, 128, 24], [39, 27, 128, 25], [40, 12, 129, 10], [41, 10, 130, 8], [41, 11, 130, 9], [41, 17, 130, 15], [42, 12, 131, 10, "reject"], [42, 18, 131, 16], [42, 19, 131, 17], [42, 23, 131, 21, "TypeError"], [42, 32, 131, 30], [42, 33, 131, 31], [42, 57, 131, 55], [42, 60, 131, 58], [42, 67, 131, 65, "task"], [42, 71, 131, 69], [42, 72, 131, 70], [42, 73, 131, 71], [43, 10, 132, 8], [44, 8, 133, 6], [44, 9, 133, 7], [44, 10, 133, 8], [45, 6, 134, 4], [45, 7, 134, 5], [45, 8, 134, 6], [46, 6, 136, 4], [46, 13, 136, 11], [47, 8, 138, 6, "then"], [47, 12, 138, 10], [47, 14, 138, 12, "promise"], [47, 21, 138, 19], [47, 22, 138, 20, "then"], [47, 26, 138, 24], [47, 27, 138, 25, "bind"], [47, 31, 138, 29], [47, 32, 138, 30, "promise"], [47, 39, 138, 37], [47, 40, 138, 38], [48, 8, 139, 6, "cancel"], [48, 14, 139, 12, "cancel"], [48, 15, 139, 12], [48, 17, 139, 15], [49, 10, 140, 8, "clearImmediate"], [49, 24, 140, 22], [49, 25, 140, 23, "immediateID"], [49, 36, 140, 34], [49, 37, 140, 35], [50, 8, 141, 6], [51, 6, 142, 4], [51, 7, 142, 5], [52, 4, 143, 2], [52, 5, 143, 3], [53, 4, 150, 2, "createInteractionHandle"], [53, 27, 150, 25, "createInteractionHandle"], [53, 28, 150, 25], [53, 30, 150, 36], [54, 6, 151, 4], [54, 13, 151, 11], [54, 14, 151, 12], [54, 15, 151, 13], [55, 4, 152, 2], [55, 5, 152, 3], [56, 4, 159, 2, "clearInteractionHandle"], [56, 26, 159, 24, "clearInteractionHandle"], [56, 27, 159, 25, "handle"], [56, 33, 159, 39], [56, 35, 159, 41], [57, 6, 160, 4, "invariant"], [57, 15, 160, 13], [57, 16, 160, 14], [57, 17, 160, 15], [57, 18, 160, 16, "handle"], [57, 24, 160, 22], [57, 26, 160, 24], [57, 79, 160, 77], [57, 80, 160, 78], [58, 4, 161, 2], [58, 5, 161, 3], [59, 4, 166, 2, "addListener"], [59, 15, 166, 13, "addListener"], [59, 16, 166, 13], [59, 18, 166, 35], [60, 6, 167, 4], [60, 13, 167, 11], [61, 8, 168, 6, "remove"], [61, 14, 168, 12, "remove"], [61, 15, 168, 12], [61, 17, 168, 15], [61, 18, 168, 16], [62, 6, 169, 4], [62, 7, 169, 5], [63, 4, 170, 2], [63, 5, 170, 3], [64, 4, 179, 2, "setDeadline"], [64, 15, 179, 13, "setDeadline"], [64, 16, 179, 14, "deadline"], [64, 24, 179, 30], [64, 26, 179, 32], [64, 27, 181, 2], [65, 2, 182, 0], [65, 3, 182, 1], [66, 2, 182, 2], [66, 6, 182, 2, "_default"], [66, 14, 182, 2], [66, 17, 182, 2, "exports"], [66, 24, 182, 2], [66, 25, 182, 2, "default"], [66, 32, 182, 2], [66, 35, 184, 15, "InteractionManagerStub"], [66, 57, 184, 37], [67, 0, 184, 37], [67, 3]], "functionMap": {"names": ["<global>", "reject", "setTimeout$argument_0", "runAfterInteractions", "Promise$argument_0", "setImmediate$argument_0", "cancel", "createInteractionHandle", "clearInteractionHandle", "addListener", "remove", "setDeadline"], "mappings": "AAA;AC6B;aCC;GDE;CDC;EGiE;gCCS;iCCC;ODwB;KDC;MGK;OHE;GHE;EOO;GPE;EQO;GRE;ESK;MCE,WD;GTE;EWS;GXE"}}, "type": "js/module"}]}