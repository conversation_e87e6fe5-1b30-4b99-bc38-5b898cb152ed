{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 50, "index": 261}}], "key": "7WCfl5ukEzbrFE5cZL7ghHcLcjU=", "exportNames": ["*"]}}, {"name": "./PointerEventManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 262}, "end": {"line": 4, "column": 56, "index": 318}}], "key": "UeoMUqPamXNn79uoukJ/OqFDf5Y=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 319}, "end": {"line": 5, "column": 36, "index": 355}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 356}, "end": {"line": 6, "column": 45, "index": 401}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../../handlers/gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 402}, "end": {"line": 7, "column": 66, "index": 468}}], "key": "xaaqCODkGxAwJpzGKT+4pXLUAXk=", "exportNames": ["*"]}}, {"name": "./KeyboardEventManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 469}, "end": {"line": 8, "column": 58, "index": 527}}], "key": "+6r4sNedtPkJwl73VREFb0QO0VE=", "exportNames": ["*"]}}, {"name": "./WheelEventManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 528}, "end": {"line": 9, "column": 52, "index": 580}}], "key": "tSF8ivZnkb/HP7bce4dNfOkhjrQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GestureHandlerWebDelegate = void 0;\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[1], \"../../findNodeHandle\"));\n  var _PointerEventManager = _interopRequireDefault(require(_dependencyMap[2], \"./PointerEventManager\"));\n  var _State = require(_dependencyMap[3], \"../../State\");\n  var _utils = require(_dependencyMap[4], \"../utils\");\n  var _gestureHandlerCommon = require(_dependencyMap[5], \"../../handlers/gestureHandlerCommon\");\n  var _KeyboardEventManager = _interopRequireDefault(require(_dependencyMap[6], \"./KeyboardEventManager\"));\n  var _WheelEventManager = _interopRequireDefault(require(_dependencyMap[7], \"./WheelEventManager\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class GestureHandlerWebDelegate {\n    constructor() {\n      _defineProperty(this, \"isInitialized\", false);\n      _defineProperty(this, \"_view\", void 0);\n      _defineProperty(this, \"gestureHandler\", void 0);\n      _defineProperty(this, \"eventManagers\", []);\n      _defineProperty(this, \"defaultViewStyles\", {\n        userSelect: '',\n        touchAction: ''\n      });\n    }\n    init(viewRef, handler) {\n      if (!viewRef) {\n        throw new Error(`Cannot find HTML Element for handler ${handler.handlerTag}`);\n      }\n      this.isInitialized = true;\n      this.gestureHandler = handler;\n      this.view = (0, _findNodeHandle.default)(viewRef);\n      this.defaultViewStyles = {\n        userSelect: this.view.style.userSelect,\n        touchAction: this.view.style.touchAction\n      };\n      const config = handler.config;\n      this.setUserSelect(config.enabled);\n      this.setTouchAction(config.enabled);\n      this.setContextMenu(config.enabled);\n      this.eventManagers.push(new _PointerEventManager.default(this.view));\n      this.eventManagers.push(new _KeyboardEventManager.default(this.view));\n      this.eventManagers.push(new _WheelEventManager.default(this.view));\n      this.eventManagers.forEach(manager => this.gestureHandler.attachEventManager(manager));\n    }\n    isPointerInBounds({\n      x,\n      y\n    }) {\n      return (0, _utils.isPointerInBounds)(this.view, {\n        x,\n        y\n      });\n    }\n    measureView() {\n      const rect = this.view.getBoundingClientRect();\n      return {\n        pageX: rect.left,\n        pageY: rect.top,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    reset() {\n      this.eventManagers.forEach(manager => manager.resetManager());\n    }\n    tryResetCursor() {\n      const config = this.gestureHandler.config;\n      if (config.activeCursor && config.activeCursor !== 'auto' && this.gestureHandler.state === _State.State.ACTIVE) {\n        this.view.style.cursor = 'auto';\n      }\n    }\n    shouldDisableContextMenu(config) {\n      return config.enableContextMenu === undefined && this.gestureHandler.isButtonInConfig(_gestureHandlerCommon.MouseButton.RIGHT) || config.enableContextMenu === false;\n    }\n    addContextMenuListeners(config) {\n      if (this.shouldDisableContextMenu(config)) {\n        this.view.addEventListener('contextmenu', this.disableContextMenu);\n      } else if (config.enableContextMenu) {\n        this.view.addEventListener('contextmenu', this.enableContextMenu);\n      }\n    }\n    removeContextMenuListeners(config) {\n      if (this.shouldDisableContextMenu(config)) {\n        this.view.removeEventListener('contextmenu', this.disableContextMenu);\n      } else if (config.enableContextMenu) {\n        this.view.removeEventListener('contextmenu', this.enableContextMenu);\n      }\n    }\n    disableContextMenu(e) {\n      e.preventDefault();\n    }\n    enableContextMenu(e) {\n      e.stopPropagation();\n    }\n    setUserSelect(isHandlerEnabled) {\n      const {\n        userSelect\n      } = this.gestureHandler.config;\n      this.view.style['userSelect'] = isHandlerEnabled ? userSelect !== null && userSelect !== void 0 ? userSelect : 'none' : this.defaultViewStyles.userSelect;\n      this.view.style['webkitUserSelect'] = isHandlerEnabled ? userSelect !== null && userSelect !== void 0 ? userSelect : 'none' : this.defaultViewStyles.userSelect;\n    }\n    setTouchAction(isHandlerEnabled) {\n      const {\n        touchAction\n      } = this.gestureHandler.config;\n      this.view.style['touchAction'] = isHandlerEnabled ? touchAction !== null && touchAction !== void 0 ? touchAction : 'none' : this.defaultViewStyles.touchAction; // @ts-ignore This one disables default events on Safari\n\n      this.view.style['WebkitTouchCallout'] = isHandlerEnabled ? touchAction !== null && touchAction !== void 0 ? touchAction : 'none' : this.defaultViewStyles.touchAction;\n    }\n    setContextMenu(isHandlerEnabled) {\n      const config = this.gestureHandler.config;\n      if (isHandlerEnabled) {\n        this.addContextMenuListeners(config);\n      } else {\n        this.removeContextMenuListeners(config);\n      }\n    }\n    onEnabledChange(enabled) {\n      if (!this.isInitialized) {\n        return;\n      }\n      this.setUserSelect(enabled);\n      this.setTouchAction(enabled);\n      this.setContextMenu(enabled);\n      if (enabled) {\n        this.eventManagers.forEach(manager => {\n          // It may look like managers will be registered twice when handler is mounted for the first time.\n          // However, `init` method is called AFTER `updateGestureConfig` - it means that delegate has not\n          // been initialized yet, so this code won't be executed.\n          //\n          // Also, because we use defined functions, not lambdas, they will not be registered multiple times.\n          manager.registerListeners();\n        });\n      } else {\n        this.eventManagers.forEach(manager => {\n          manager.unregisterListeners();\n        });\n      }\n    }\n    onBegin() {// no-op for now\n    }\n    onActivate() {\n      const config = this.gestureHandler.config;\n      if ((!this.view.style.cursor || this.view.style.cursor === 'auto') && config.activeCursor) {\n        this.view.style.cursor = config.activeCursor;\n      }\n    }\n    onEnd() {\n      this.tryResetCursor();\n    }\n    onCancel() {\n      this.tryResetCursor();\n    }\n    onFail() {\n      this.tryResetCursor();\n    }\n    destroy(config) {\n      this.removeContextMenuListeners(config);\n      this.eventManagers.forEach(manager => {\n        manager.unregisterListeners();\n      });\n    }\n    get view() {\n      return this._view;\n    }\n    set view(value) {\n      this._view = value;\n    }\n  }\n  exports.GestureHandlerWebDelegate = GestureHandlerWebDelegate;\n});", "lineCount": 184, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_findNodeHandle"], [7, 21, 3, 0], [7, 24, 3, 0, "_interopRequireDefault"], [7, 46, 3, 0], [7, 47, 3, 0, "require"], [7, 54, 3, 0], [7, 55, 3, 0, "_dependencyMap"], [7, 69, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_PointerEventManager"], [8, 26, 4, 0], [8, 29, 4, 0, "_interopRequireDefault"], [8, 51, 4, 0], [8, 52, 4, 0, "require"], [8, 59, 4, 0], [8, 60, 4, 0, "_dependencyMap"], [8, 74, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_State"], [9, 12, 5, 0], [9, 15, 5, 0, "require"], [9, 22, 5, 0], [9, 23, 5, 0, "_dependencyMap"], [9, 37, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_utils"], [10, 12, 6, 0], [10, 15, 6, 0, "require"], [10, 22, 6, 0], [10, 23, 6, 0, "_dependencyMap"], [10, 37, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 27, 7, 0], [11, 30, 7, 0, "require"], [11, 37, 7, 0], [11, 38, 7, 0, "_dependencyMap"], [11, 52, 7, 0], [12, 2, 8, 0], [12, 6, 8, 0, "_KeyboardEventManager"], [12, 27, 8, 0], [12, 30, 8, 0, "_interopRequireDefault"], [12, 52, 8, 0], [12, 53, 8, 0, "require"], [12, 60, 8, 0], [12, 61, 8, 0, "_dependencyMap"], [12, 75, 8, 0], [13, 2, 9, 0], [13, 6, 9, 0, "_WheelEventManager"], [13, 24, 9, 0], [13, 27, 9, 0, "_interopRequireDefault"], [13, 49, 9, 0], [13, 50, 9, 0, "require"], [13, 57, 9, 0], [13, 58, 9, 0, "_dependencyMap"], [13, 72, 9, 0], [14, 2, 1, 0], [14, 11, 1, 9, "_defineProperty"], [14, 26, 1, 24, "_defineProperty"], [14, 27, 1, 25, "obj"], [14, 30, 1, 28], [14, 32, 1, 30, "key"], [14, 35, 1, 33], [14, 37, 1, 35, "value"], [14, 42, 1, 40], [14, 44, 1, 42], [15, 4, 1, 44], [15, 8, 1, 48, "key"], [15, 11, 1, 51], [15, 15, 1, 55, "obj"], [15, 18, 1, 58], [15, 20, 1, 60], [16, 6, 1, 62, "Object"], [16, 12, 1, 68], [16, 13, 1, 69, "defineProperty"], [16, 27, 1, 83], [16, 28, 1, 84, "obj"], [16, 31, 1, 87], [16, 33, 1, 89, "key"], [16, 36, 1, 92], [16, 38, 1, 94], [17, 8, 1, 96, "value"], [17, 13, 1, 101], [17, 15, 1, 103, "value"], [17, 20, 1, 108], [18, 8, 1, 110, "enumerable"], [18, 18, 1, 120], [18, 20, 1, 122], [18, 24, 1, 126], [19, 8, 1, 128, "configurable"], [19, 20, 1, 140], [19, 22, 1, 142], [19, 26, 1, 146], [20, 8, 1, 148, "writable"], [20, 16, 1, 156], [20, 18, 1, 158], [21, 6, 1, 163], [21, 7, 1, 164], [21, 8, 1, 165], [22, 4, 1, 167], [22, 5, 1, 168], [22, 11, 1, 174], [23, 6, 1, 176, "obj"], [23, 9, 1, 179], [23, 10, 1, 180, "key"], [23, 13, 1, 183], [23, 14, 1, 184], [23, 17, 1, 187, "value"], [23, 22, 1, 192], [24, 4, 1, 194], [25, 4, 1, 196], [25, 11, 1, 203, "obj"], [25, 14, 1, 206], [26, 2, 1, 208], [27, 2, 10, 7], [27, 8, 10, 13, "GestureHandlerWebDelegate"], [27, 33, 10, 38], [27, 34, 10, 39], [28, 4, 11, 2, "constructor"], [28, 15, 11, 13, "constructor"], [28, 16, 11, 13], [28, 18, 11, 16], [29, 6, 12, 4, "_defineProperty"], [29, 21, 12, 19], [29, 22, 12, 20], [29, 26, 12, 24], [29, 28, 12, 26], [29, 43, 12, 41], [29, 45, 12, 43], [29, 50, 12, 48], [29, 51, 12, 49], [30, 6, 14, 4, "_defineProperty"], [30, 21, 14, 19], [30, 22, 14, 20], [30, 26, 14, 24], [30, 28, 14, 26], [30, 35, 14, 33], [30, 37, 14, 35], [30, 42, 14, 40], [30, 43, 14, 41], [30, 44, 14, 42], [31, 6, 16, 4, "_defineProperty"], [31, 21, 16, 19], [31, 22, 16, 20], [31, 26, 16, 24], [31, 28, 16, 26], [31, 44, 16, 42], [31, 46, 16, 44], [31, 51, 16, 49], [31, 52, 16, 50], [31, 53, 16, 51], [32, 6, 18, 4, "_defineProperty"], [32, 21, 18, 19], [32, 22, 18, 20], [32, 26, 18, 24], [32, 28, 18, 26], [32, 43, 18, 41], [32, 45, 18, 43], [32, 47, 18, 45], [32, 48, 18, 46], [33, 6, 20, 4, "_defineProperty"], [33, 21, 20, 19], [33, 22, 20, 20], [33, 26, 20, 24], [33, 28, 20, 26], [33, 47, 20, 45], [33, 49, 20, 47], [34, 8, 21, 6, "userSelect"], [34, 18, 21, 16], [34, 20, 21, 18], [34, 22, 21, 20], [35, 8, 22, 6, "touchAction"], [35, 19, 22, 17], [35, 21, 22, 19], [36, 6, 23, 4], [36, 7, 23, 5], [36, 8, 23, 6], [37, 4, 24, 2], [38, 4, 26, 2, "init"], [38, 8, 26, 6, "init"], [38, 9, 26, 7, "viewRef"], [38, 16, 26, 14], [38, 18, 26, 16, "handler"], [38, 25, 26, 23], [38, 27, 26, 25], [39, 6, 27, 4], [39, 10, 27, 8], [39, 11, 27, 9, "viewRef"], [39, 18, 27, 16], [39, 20, 27, 18], [40, 8, 28, 6], [40, 14, 28, 12], [40, 18, 28, 16, "Error"], [40, 23, 28, 21], [40, 24, 28, 22], [40, 64, 28, 62, "handler"], [40, 71, 28, 69], [40, 72, 28, 70, "handlerTag"], [40, 82, 28, 80], [40, 84, 28, 82], [40, 85, 28, 83], [41, 6, 29, 4], [42, 6, 31, 4], [42, 10, 31, 8], [42, 11, 31, 9, "isInitialized"], [42, 24, 31, 22], [42, 27, 31, 25], [42, 31, 31, 29], [43, 6, 32, 4], [43, 10, 32, 8], [43, 11, 32, 9, "<PERSON><PERSON><PERSON><PERSON>"], [43, 25, 32, 23], [43, 28, 32, 26, "handler"], [43, 35, 32, 33], [44, 6, 33, 4], [44, 10, 33, 8], [44, 11, 33, 9, "view"], [44, 15, 33, 13], [44, 18, 33, 16], [44, 22, 33, 16, "findNodeHandle"], [44, 45, 33, 30], [44, 47, 33, 31, "viewRef"], [44, 54, 33, 38], [44, 55, 33, 39], [45, 6, 34, 4], [45, 10, 34, 8], [45, 11, 34, 9, "defaultViewStyles"], [45, 28, 34, 26], [45, 31, 34, 29], [46, 8, 35, 6, "userSelect"], [46, 18, 35, 16], [46, 20, 35, 18], [46, 24, 35, 22], [46, 25, 35, 23, "view"], [46, 29, 35, 27], [46, 30, 35, 28, "style"], [46, 35, 35, 33], [46, 36, 35, 34, "userSelect"], [46, 46, 35, 44], [47, 8, 36, 6, "touchAction"], [47, 19, 36, 17], [47, 21, 36, 19], [47, 25, 36, 23], [47, 26, 36, 24, "view"], [47, 30, 36, 28], [47, 31, 36, 29, "style"], [47, 36, 36, 34], [47, 37, 36, 35, "touchAction"], [48, 6, 37, 4], [48, 7, 37, 5], [49, 6, 38, 4], [49, 12, 38, 10, "config"], [49, 18, 38, 16], [49, 21, 38, 19, "handler"], [49, 28, 38, 26], [49, 29, 38, 27, "config"], [49, 35, 38, 33], [50, 6, 39, 4], [50, 10, 39, 8], [50, 11, 39, 9, "setUserSelect"], [50, 24, 39, 22], [50, 25, 39, 23, "config"], [50, 31, 39, 29], [50, 32, 39, 30, "enabled"], [50, 39, 39, 37], [50, 40, 39, 38], [51, 6, 40, 4], [51, 10, 40, 8], [51, 11, 40, 9, "setTouchAction"], [51, 25, 40, 23], [51, 26, 40, 24, "config"], [51, 32, 40, 30], [51, 33, 40, 31, "enabled"], [51, 40, 40, 38], [51, 41, 40, 39], [52, 6, 41, 4], [52, 10, 41, 8], [52, 11, 41, 9, "setContextMenu"], [52, 25, 41, 23], [52, 26, 41, 24, "config"], [52, 32, 41, 30], [52, 33, 41, 31, "enabled"], [52, 40, 41, 38], [52, 41, 41, 39], [53, 6, 42, 4], [53, 10, 42, 8], [53, 11, 42, 9, "eventManagers"], [53, 24, 42, 22], [53, 25, 42, 23, "push"], [53, 29, 42, 27], [53, 30, 42, 28], [53, 34, 42, 32, "PointerEventManager"], [53, 62, 42, 51], [53, 63, 42, 52], [53, 67, 42, 56], [53, 68, 42, 57, "view"], [53, 72, 42, 61], [53, 73, 42, 62], [53, 74, 42, 63], [54, 6, 43, 4], [54, 10, 43, 8], [54, 11, 43, 9, "eventManagers"], [54, 24, 43, 22], [54, 25, 43, 23, "push"], [54, 29, 43, 27], [54, 30, 43, 28], [54, 34, 43, 32, "KeyboardEventManager"], [54, 63, 43, 52], [54, 64, 43, 53], [54, 68, 43, 57], [54, 69, 43, 58, "view"], [54, 73, 43, 62], [54, 74, 43, 63], [54, 75, 43, 64], [55, 6, 44, 4], [55, 10, 44, 8], [55, 11, 44, 9, "eventManagers"], [55, 24, 44, 22], [55, 25, 44, 23, "push"], [55, 29, 44, 27], [55, 30, 44, 28], [55, 34, 44, 32, "WheelEventManager"], [55, 60, 44, 49], [55, 61, 44, 50], [55, 65, 44, 54], [55, 66, 44, 55, "view"], [55, 70, 44, 59], [55, 71, 44, 60], [55, 72, 44, 61], [56, 6, 45, 4], [56, 10, 45, 8], [56, 11, 45, 9, "eventManagers"], [56, 24, 45, 22], [56, 25, 45, 23, "for<PERSON>ach"], [56, 32, 45, 30], [56, 33, 45, 31, "manager"], [56, 40, 45, 38], [56, 44, 45, 42], [56, 48, 45, 46], [56, 49, 45, 47, "<PERSON><PERSON><PERSON><PERSON>"], [56, 63, 45, 61], [56, 64, 45, 62, "attachEventManager"], [56, 82, 45, 80], [56, 83, 45, 81, "manager"], [56, 90, 45, 88], [56, 91, 45, 89], [56, 92, 45, 90], [57, 4, 46, 2], [58, 4, 48, 2, "isPointerInBounds"], [58, 21, 48, 19, "isPointerInBounds"], [58, 22, 48, 20], [59, 6, 49, 4, "x"], [59, 7, 49, 5], [60, 6, 50, 4, "y"], [61, 4, 51, 2], [61, 5, 51, 3], [61, 7, 51, 5], [62, 6, 52, 4], [62, 13, 52, 11], [62, 17, 52, 11, "isPointerInBounds"], [62, 41, 52, 28], [62, 43, 52, 29], [62, 47, 52, 33], [62, 48, 52, 34, "view"], [62, 52, 52, 38], [62, 54, 52, 40], [63, 8, 53, 6, "x"], [63, 9, 53, 7], [64, 8, 54, 6, "y"], [65, 6, 55, 4], [65, 7, 55, 5], [65, 8, 55, 6], [66, 4, 56, 2], [67, 4, 58, 2, "measure<PERSON>iew"], [67, 15, 58, 13, "measure<PERSON>iew"], [67, 16, 58, 13], [67, 18, 58, 16], [68, 6, 59, 4], [68, 12, 59, 10, "rect"], [68, 16, 59, 14], [68, 19, 59, 17], [68, 23, 59, 21], [68, 24, 59, 22, "view"], [68, 28, 59, 26], [68, 29, 59, 27, "getBoundingClientRect"], [68, 50, 59, 48], [68, 51, 59, 49], [68, 52, 59, 50], [69, 6, 60, 4], [69, 13, 60, 11], [70, 8, 61, 6, "pageX"], [70, 13, 61, 11], [70, 15, 61, 13, "rect"], [70, 19, 61, 17], [70, 20, 61, 18, "left"], [70, 24, 61, 22], [71, 8, 62, 6, "pageY"], [71, 13, 62, 11], [71, 15, 62, 13, "rect"], [71, 19, 62, 17], [71, 20, 62, 18, "top"], [71, 23, 62, 21], [72, 8, 63, 6, "width"], [72, 13, 63, 11], [72, 15, 63, 13, "rect"], [72, 19, 63, 17], [72, 20, 63, 18, "width"], [72, 25, 63, 23], [73, 8, 64, 6, "height"], [73, 14, 64, 12], [73, 16, 64, 14, "rect"], [73, 20, 64, 18], [73, 21, 64, 19, "height"], [74, 6, 65, 4], [74, 7, 65, 5], [75, 4, 66, 2], [76, 4, 68, 2, "reset"], [76, 9, 68, 7, "reset"], [76, 10, 68, 7], [76, 12, 68, 10], [77, 6, 69, 4], [77, 10, 69, 8], [77, 11, 69, 9, "eventManagers"], [77, 24, 69, 22], [77, 25, 69, 23, "for<PERSON>ach"], [77, 32, 69, 30], [77, 33, 69, 31, "manager"], [77, 40, 69, 38], [77, 44, 69, 42, "manager"], [77, 51, 69, 49], [77, 52, 69, 50, "resetManager"], [77, 64, 69, 62], [77, 65, 69, 63], [77, 66, 69, 64], [77, 67, 69, 65], [78, 4, 70, 2], [79, 4, 72, 2, "tryResetCursor"], [79, 18, 72, 16, "tryResetCursor"], [79, 19, 72, 16], [79, 21, 72, 19], [80, 6, 73, 4], [80, 12, 73, 10, "config"], [80, 18, 73, 16], [80, 21, 73, 19], [80, 25, 73, 23], [80, 26, 73, 24, "<PERSON><PERSON><PERSON><PERSON>"], [80, 40, 73, 38], [80, 41, 73, 39, "config"], [80, 47, 73, 45], [81, 6, 75, 4], [81, 10, 75, 8, "config"], [81, 16, 75, 14], [81, 17, 75, 15, "activeCursor"], [81, 29, 75, 27], [81, 33, 75, 31, "config"], [81, 39, 75, 37], [81, 40, 75, 38, "activeCursor"], [81, 52, 75, 50], [81, 57, 75, 55], [81, 63, 75, 61], [81, 67, 75, 65], [81, 71, 75, 69], [81, 72, 75, 70, "<PERSON><PERSON><PERSON><PERSON>"], [81, 86, 75, 84], [81, 87, 75, 85, "state"], [81, 92, 75, 90], [81, 97, 75, 95, "State"], [81, 109, 75, 100], [81, 110, 75, 101, "ACTIVE"], [81, 116, 75, 107], [81, 118, 75, 109], [82, 8, 76, 6], [82, 12, 76, 10], [82, 13, 76, 11, "view"], [82, 17, 76, 15], [82, 18, 76, 16, "style"], [82, 23, 76, 21], [82, 24, 76, 22, "cursor"], [82, 30, 76, 28], [82, 33, 76, 31], [82, 39, 76, 37], [83, 6, 77, 4], [84, 4, 78, 2], [85, 4, 80, 2, "shouldDisableContextMenu"], [85, 28, 80, 26, "shouldDisableContextMenu"], [85, 29, 80, 27, "config"], [85, 35, 80, 33], [85, 37, 80, 35], [86, 6, 81, 4], [86, 13, 81, 11, "config"], [86, 19, 81, 17], [86, 20, 81, 18, "enableContextMenu"], [86, 37, 81, 35], [86, 42, 81, 40, "undefined"], [86, 51, 81, 49], [86, 55, 81, 53], [86, 59, 81, 57], [86, 60, 81, 58, "<PERSON><PERSON><PERSON><PERSON>"], [86, 74, 81, 72], [86, 75, 81, 73, "isButtonInConfig"], [86, 91, 81, 89], [86, 92, 81, 90, "MouseB<PERSON>on"], [86, 125, 81, 101], [86, 126, 81, 102, "RIGHT"], [86, 131, 81, 107], [86, 132, 81, 108], [86, 136, 81, 112, "config"], [86, 142, 81, 118], [86, 143, 81, 119, "enableContextMenu"], [86, 160, 81, 136], [86, 165, 81, 141], [86, 170, 81, 146], [87, 4, 82, 2], [88, 4, 84, 2, "addContextMenuListeners"], [88, 27, 84, 25, "addContextMenuListeners"], [88, 28, 84, 26, "config"], [88, 34, 84, 32], [88, 36, 84, 34], [89, 6, 85, 4], [89, 10, 85, 8], [89, 14, 85, 12], [89, 15, 85, 13, "shouldDisableContextMenu"], [89, 39, 85, 37], [89, 40, 85, 38, "config"], [89, 46, 85, 44], [89, 47, 85, 45], [89, 49, 85, 47], [90, 8, 86, 6], [90, 12, 86, 10], [90, 13, 86, 11, "view"], [90, 17, 86, 15], [90, 18, 86, 16, "addEventListener"], [90, 34, 86, 32], [90, 35, 86, 33], [90, 48, 86, 46], [90, 50, 86, 48], [90, 54, 86, 52], [90, 55, 86, 53, "disableContextMenu"], [90, 73, 86, 71], [90, 74, 86, 72], [91, 6, 87, 4], [91, 7, 87, 5], [91, 13, 87, 11], [91, 17, 87, 15, "config"], [91, 23, 87, 21], [91, 24, 87, 22, "enableContextMenu"], [91, 41, 87, 39], [91, 43, 87, 41], [92, 8, 88, 6], [92, 12, 88, 10], [92, 13, 88, 11, "view"], [92, 17, 88, 15], [92, 18, 88, 16, "addEventListener"], [92, 34, 88, 32], [92, 35, 88, 33], [92, 48, 88, 46], [92, 50, 88, 48], [92, 54, 88, 52], [92, 55, 88, 53, "enableContextMenu"], [92, 72, 88, 70], [92, 73, 88, 71], [93, 6, 89, 4], [94, 4, 90, 2], [95, 4, 92, 2, "removeContextMenuListeners"], [95, 30, 92, 28, "removeContextMenuListeners"], [95, 31, 92, 29, "config"], [95, 37, 92, 35], [95, 39, 92, 37], [96, 6, 93, 4], [96, 10, 93, 8], [96, 14, 93, 12], [96, 15, 93, 13, "shouldDisableContextMenu"], [96, 39, 93, 37], [96, 40, 93, 38, "config"], [96, 46, 93, 44], [96, 47, 93, 45], [96, 49, 93, 47], [97, 8, 94, 6], [97, 12, 94, 10], [97, 13, 94, 11, "view"], [97, 17, 94, 15], [97, 18, 94, 16, "removeEventListener"], [97, 37, 94, 35], [97, 38, 94, 36], [97, 51, 94, 49], [97, 53, 94, 51], [97, 57, 94, 55], [97, 58, 94, 56, "disableContextMenu"], [97, 76, 94, 74], [97, 77, 94, 75], [98, 6, 95, 4], [98, 7, 95, 5], [98, 13, 95, 11], [98, 17, 95, 15, "config"], [98, 23, 95, 21], [98, 24, 95, 22, "enableContextMenu"], [98, 41, 95, 39], [98, 43, 95, 41], [99, 8, 96, 6], [99, 12, 96, 10], [99, 13, 96, 11, "view"], [99, 17, 96, 15], [99, 18, 96, 16, "removeEventListener"], [99, 37, 96, 35], [99, 38, 96, 36], [99, 51, 96, 49], [99, 53, 96, 51], [99, 57, 96, 55], [99, 58, 96, 56, "enableContextMenu"], [99, 75, 96, 73], [99, 76, 96, 74], [100, 6, 97, 4], [101, 4, 98, 2], [102, 4, 100, 2, "disableContextMenu"], [102, 22, 100, 20, "disableContextMenu"], [102, 23, 100, 21, "e"], [102, 24, 100, 22], [102, 26, 100, 24], [103, 6, 101, 4, "e"], [103, 7, 101, 5], [103, 8, 101, 6, "preventDefault"], [103, 22, 101, 20], [103, 23, 101, 21], [103, 24, 101, 22], [104, 4, 102, 2], [105, 4, 104, 2, "enableContextMenu"], [105, 21, 104, 19, "enableContextMenu"], [105, 22, 104, 20, "e"], [105, 23, 104, 21], [105, 25, 104, 23], [106, 6, 105, 4, "e"], [106, 7, 105, 5], [106, 8, 105, 6, "stopPropagation"], [106, 23, 105, 21], [106, 24, 105, 22], [106, 25, 105, 23], [107, 4, 106, 2], [108, 4, 108, 2, "setUserSelect"], [108, 17, 108, 15, "setUserSelect"], [108, 18, 108, 16, "isHandlerEnabled"], [108, 34, 108, 32], [108, 36, 108, 34], [109, 6, 109, 4], [109, 12, 109, 10], [110, 8, 110, 6, "userSelect"], [111, 6, 111, 4], [111, 7, 111, 5], [111, 10, 111, 8], [111, 14, 111, 12], [111, 15, 111, 13, "<PERSON><PERSON><PERSON><PERSON>"], [111, 29, 111, 27], [111, 30, 111, 28, "config"], [111, 36, 111, 34], [112, 6, 112, 4], [112, 10, 112, 8], [112, 11, 112, 9, "view"], [112, 15, 112, 13], [112, 16, 112, 14, "style"], [112, 21, 112, 19], [112, 22, 112, 20], [112, 34, 112, 32], [112, 35, 112, 33], [112, 38, 112, 36, "isHandlerEnabled"], [112, 54, 112, 52], [112, 57, 112, 55, "userSelect"], [112, 67, 112, 65], [112, 72, 112, 70], [112, 76, 112, 74], [112, 80, 112, 78, "userSelect"], [112, 90, 112, 88], [112, 95, 112, 93], [112, 100, 112, 98], [112, 101, 112, 99], [112, 104, 112, 102, "userSelect"], [112, 114, 112, 112], [112, 117, 112, 115], [112, 123, 112, 121], [112, 126, 112, 124], [112, 130, 112, 128], [112, 131, 112, 129, "defaultViewStyles"], [112, 148, 112, 146], [112, 149, 112, 147, "userSelect"], [112, 159, 112, 157], [113, 6, 113, 4], [113, 10, 113, 8], [113, 11, 113, 9, "view"], [113, 15, 113, 13], [113, 16, 113, 14, "style"], [113, 21, 113, 19], [113, 22, 113, 20], [113, 40, 113, 38], [113, 41, 113, 39], [113, 44, 113, 42, "isHandlerEnabled"], [113, 60, 113, 58], [113, 63, 113, 61, "userSelect"], [113, 73, 113, 71], [113, 78, 113, 76], [113, 82, 113, 80], [113, 86, 113, 84, "userSelect"], [113, 96, 113, 94], [113, 101, 113, 99], [113, 106, 113, 104], [113, 107, 113, 105], [113, 110, 113, 108, "userSelect"], [113, 120, 113, 118], [113, 123, 113, 121], [113, 129, 113, 127], [113, 132, 113, 130], [113, 136, 113, 134], [113, 137, 113, 135, "defaultViewStyles"], [113, 154, 113, 152], [113, 155, 113, 153, "userSelect"], [113, 165, 113, 163], [114, 4, 114, 2], [115, 4, 116, 2, "setTouchAction"], [115, 18, 116, 16, "setTouchAction"], [115, 19, 116, 17, "isHandlerEnabled"], [115, 35, 116, 33], [115, 37, 116, 35], [116, 6, 117, 4], [116, 12, 117, 10], [117, 8, 118, 6, "touchAction"], [118, 6, 119, 4], [118, 7, 119, 5], [118, 10, 119, 8], [118, 14, 119, 12], [118, 15, 119, 13, "<PERSON><PERSON><PERSON><PERSON>"], [118, 29, 119, 27], [118, 30, 119, 28, "config"], [118, 36, 119, 34], [119, 6, 120, 4], [119, 10, 120, 8], [119, 11, 120, 9, "view"], [119, 15, 120, 13], [119, 16, 120, 14, "style"], [119, 21, 120, 19], [119, 22, 120, 20], [119, 35, 120, 33], [119, 36, 120, 34], [119, 39, 120, 37, "isHandlerEnabled"], [119, 55, 120, 53], [119, 58, 120, 56, "touchAction"], [119, 69, 120, 67], [119, 74, 120, 72], [119, 78, 120, 76], [119, 82, 120, 80, "touchAction"], [119, 93, 120, 91], [119, 98, 120, 96], [119, 103, 120, 101], [119, 104, 120, 102], [119, 107, 120, 105, "touchAction"], [119, 118, 120, 116], [119, 121, 120, 119], [119, 127, 120, 125], [119, 130, 120, 128], [119, 134, 120, 132], [119, 135, 120, 133, "defaultViewStyles"], [119, 152, 120, 150], [119, 153, 120, 151, "touchAction"], [119, 164, 120, 162], [119, 165, 120, 163], [119, 166, 120, 164], [121, 6, 122, 4], [121, 10, 122, 8], [121, 11, 122, 9, "view"], [121, 15, 122, 13], [121, 16, 122, 14, "style"], [121, 21, 122, 19], [121, 22, 122, 20], [121, 42, 122, 40], [121, 43, 122, 41], [121, 46, 122, 44, "isHandlerEnabled"], [121, 62, 122, 60], [121, 65, 122, 63, "touchAction"], [121, 76, 122, 74], [121, 81, 122, 79], [121, 85, 122, 83], [121, 89, 122, 87, "touchAction"], [121, 100, 122, 98], [121, 105, 122, 103], [121, 110, 122, 108], [121, 111, 122, 109], [121, 114, 122, 112, "touchAction"], [121, 125, 122, 123], [121, 128, 122, 126], [121, 134, 122, 132], [121, 137, 122, 135], [121, 141, 122, 139], [121, 142, 122, 140, "defaultViewStyles"], [121, 159, 122, 157], [121, 160, 122, 158, "touchAction"], [121, 171, 122, 169], [122, 4, 123, 2], [123, 4, 125, 2, "setContextMenu"], [123, 18, 125, 16, "setContextMenu"], [123, 19, 125, 17, "isHandlerEnabled"], [123, 35, 125, 33], [123, 37, 125, 35], [124, 6, 126, 4], [124, 12, 126, 10, "config"], [124, 18, 126, 16], [124, 21, 126, 19], [124, 25, 126, 23], [124, 26, 126, 24, "<PERSON><PERSON><PERSON><PERSON>"], [124, 40, 126, 38], [124, 41, 126, 39, "config"], [124, 47, 126, 45], [125, 6, 128, 4], [125, 10, 128, 8, "isHandlerEnabled"], [125, 26, 128, 24], [125, 28, 128, 26], [126, 8, 129, 6], [126, 12, 129, 10], [126, 13, 129, 11, "addContextMenuListeners"], [126, 36, 129, 34], [126, 37, 129, 35, "config"], [126, 43, 129, 41], [126, 44, 129, 42], [127, 6, 130, 4], [127, 7, 130, 5], [127, 13, 130, 11], [128, 8, 131, 6], [128, 12, 131, 10], [128, 13, 131, 11, "removeContextMenuListeners"], [128, 39, 131, 37], [128, 40, 131, 38, "config"], [128, 46, 131, 44], [128, 47, 131, 45], [129, 6, 132, 4], [130, 4, 133, 2], [131, 4, 135, 2, "onEnabledChange"], [131, 19, 135, 17, "onEnabledChange"], [131, 20, 135, 18, "enabled"], [131, 27, 135, 25], [131, 29, 135, 27], [132, 6, 136, 4], [132, 10, 136, 8], [132, 11, 136, 9], [132, 15, 136, 13], [132, 16, 136, 14, "isInitialized"], [132, 29, 136, 27], [132, 31, 136, 29], [133, 8, 137, 6], [134, 6, 138, 4], [135, 6, 140, 4], [135, 10, 140, 8], [135, 11, 140, 9, "setUserSelect"], [135, 24, 140, 22], [135, 25, 140, 23, "enabled"], [135, 32, 140, 30], [135, 33, 140, 31], [136, 6, 141, 4], [136, 10, 141, 8], [136, 11, 141, 9, "setTouchAction"], [136, 25, 141, 23], [136, 26, 141, 24, "enabled"], [136, 33, 141, 31], [136, 34, 141, 32], [137, 6, 142, 4], [137, 10, 142, 8], [137, 11, 142, 9, "setContextMenu"], [137, 25, 142, 23], [137, 26, 142, 24, "enabled"], [137, 33, 142, 31], [137, 34, 142, 32], [138, 6, 144, 4], [138, 10, 144, 8, "enabled"], [138, 17, 144, 15], [138, 19, 144, 17], [139, 8, 145, 6], [139, 12, 145, 10], [139, 13, 145, 11, "eventManagers"], [139, 26, 145, 24], [139, 27, 145, 25, "for<PERSON>ach"], [139, 34, 145, 32], [139, 35, 145, 33, "manager"], [139, 42, 145, 40], [139, 46, 145, 44], [140, 10, 146, 8], [141, 10, 147, 8], [142, 10, 148, 8], [143, 10, 149, 8], [144, 10, 150, 8], [145, 10, 151, 8, "manager"], [145, 17, 151, 15], [145, 18, 151, 16, "registerListeners"], [145, 35, 151, 33], [145, 36, 151, 34], [145, 37, 151, 35], [146, 8, 152, 6], [146, 9, 152, 7], [146, 10, 152, 8], [147, 6, 153, 4], [147, 7, 153, 5], [147, 13, 153, 11], [148, 8, 154, 6], [148, 12, 154, 10], [148, 13, 154, 11, "eventManagers"], [148, 26, 154, 24], [148, 27, 154, 25, "for<PERSON>ach"], [148, 34, 154, 32], [148, 35, 154, 33, "manager"], [148, 42, 154, 40], [148, 46, 154, 44], [149, 10, 155, 8, "manager"], [149, 17, 155, 15], [149, 18, 155, 16, "unregisterListeners"], [149, 37, 155, 35], [149, 38, 155, 36], [149, 39, 155, 37], [150, 8, 156, 6], [150, 9, 156, 7], [150, 10, 156, 8], [151, 6, 157, 4], [152, 4, 158, 2], [153, 4, 160, 2, "onBegin"], [153, 11, 160, 9, "onBegin"], [153, 12, 160, 9], [153, 14, 160, 12], [153, 15, 160, 13], [154, 4, 160, 13], [155, 4, 163, 2, "onActivate"], [155, 14, 163, 12, "onActivate"], [155, 15, 163, 12], [155, 17, 163, 15], [156, 6, 164, 4], [156, 12, 164, 10, "config"], [156, 18, 164, 16], [156, 21, 164, 19], [156, 25, 164, 23], [156, 26, 164, 24, "<PERSON><PERSON><PERSON><PERSON>"], [156, 40, 164, 38], [156, 41, 164, 39, "config"], [156, 47, 164, 45], [157, 6, 166, 4], [157, 10, 166, 8], [157, 11, 166, 9], [157, 12, 166, 10], [157, 16, 166, 14], [157, 17, 166, 15, "view"], [157, 21, 166, 19], [157, 22, 166, 20, "style"], [157, 27, 166, 25], [157, 28, 166, 26, "cursor"], [157, 34, 166, 32], [157, 38, 166, 36], [157, 42, 166, 40], [157, 43, 166, 41, "view"], [157, 47, 166, 45], [157, 48, 166, 46, "style"], [157, 53, 166, 51], [157, 54, 166, 52, "cursor"], [157, 60, 166, 58], [157, 65, 166, 63], [157, 71, 166, 69], [157, 76, 166, 74, "config"], [157, 82, 166, 80], [157, 83, 166, 81, "activeCursor"], [157, 95, 166, 93], [157, 97, 166, 95], [158, 8, 167, 6], [158, 12, 167, 10], [158, 13, 167, 11, "view"], [158, 17, 167, 15], [158, 18, 167, 16, "style"], [158, 23, 167, 21], [158, 24, 167, 22, "cursor"], [158, 30, 167, 28], [158, 33, 167, 31, "config"], [158, 39, 167, 37], [158, 40, 167, 38, "activeCursor"], [158, 52, 167, 50], [159, 6, 168, 4], [160, 4, 169, 2], [161, 4, 171, 2, "onEnd"], [161, 9, 171, 7, "onEnd"], [161, 10, 171, 7], [161, 12, 171, 10], [162, 6, 172, 4], [162, 10, 172, 8], [162, 11, 172, 9, "tryResetCursor"], [162, 25, 172, 23], [162, 26, 172, 24], [162, 27, 172, 25], [163, 4, 173, 2], [164, 4, 175, 2, "onCancel"], [164, 12, 175, 10, "onCancel"], [164, 13, 175, 10], [164, 15, 175, 13], [165, 6, 176, 4], [165, 10, 176, 8], [165, 11, 176, 9, "tryResetCursor"], [165, 25, 176, 23], [165, 26, 176, 24], [165, 27, 176, 25], [166, 4, 177, 2], [167, 4, 179, 2, "onFail"], [167, 10, 179, 8, "onFail"], [167, 11, 179, 8], [167, 13, 179, 11], [168, 6, 180, 4], [168, 10, 180, 8], [168, 11, 180, 9, "tryResetCursor"], [168, 25, 180, 23], [168, 26, 180, 24], [168, 27, 180, 25], [169, 4, 181, 2], [170, 4, 183, 2, "destroy"], [170, 11, 183, 9, "destroy"], [170, 12, 183, 10, "config"], [170, 18, 183, 16], [170, 20, 183, 18], [171, 6, 184, 4], [171, 10, 184, 8], [171, 11, 184, 9, "removeContextMenuListeners"], [171, 37, 184, 35], [171, 38, 184, 36, "config"], [171, 44, 184, 42], [171, 45, 184, 43], [172, 6, 185, 4], [172, 10, 185, 8], [172, 11, 185, 9, "eventManagers"], [172, 24, 185, 22], [172, 25, 185, 23, "for<PERSON>ach"], [172, 32, 185, 30], [172, 33, 185, 31, "manager"], [172, 40, 185, 38], [172, 44, 185, 42], [173, 8, 186, 6, "manager"], [173, 15, 186, 13], [173, 16, 186, 14, "unregisterListeners"], [173, 35, 186, 33], [173, 36, 186, 34], [173, 37, 186, 35], [174, 6, 187, 4], [174, 7, 187, 5], [174, 8, 187, 6], [175, 4, 188, 2], [176, 4, 190, 2], [176, 8, 190, 6, "view"], [176, 12, 190, 10, "view"], [176, 13, 190, 10], [176, 15, 190, 13], [177, 6, 191, 4], [177, 13, 191, 11], [177, 17, 191, 15], [177, 18, 191, 16, "_view"], [177, 23, 191, 21], [178, 4, 192, 2], [179, 4, 194, 2], [179, 8, 194, 6, "view"], [179, 12, 194, 10, "view"], [179, 13, 194, 11, "value"], [179, 18, 194, 16], [179, 20, 194, 18], [180, 6, 195, 4], [180, 10, 195, 8], [180, 11, 195, 9, "_view"], [180, 16, 195, 14], [180, 19, 195, 17, "value"], [180, 24, 195, 22], [181, 4, 196, 2], [182, 2, 198, 0], [183, 2, 198, 1, "exports"], [183, 9, 198, 1], [183, 10, 198, 1, "GestureHandlerWebDelegate"], [183, 35, 198, 1], [183, 38, 198, 1, "GestureHandlerWebDelegate"], [183, 63, 198, 1], [184, 0, 198, 1], [184, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "GestureHandlerWebDelegate", "constructor", "init", "eventManagers.forEach$argument_0", "isPointerInBounds", "measure<PERSON>iew", "reset", "tryResetCursor", "shouldDisableContextMenu", "addContextMenuListeners", "removeContextMenuListeners", "disableContextMenu", "enableContextMenu", "setUserSelect", "setTouchAction", "setContextMenu", "onEnabledChange", "onBegin", "onActivate", "onEnd", "onCancel", "onFail", "destroy", "get__view", "set__view"], "mappings": "AAA,iNC;OCS;ECC;GDa;EEE;+BCmB,0DD;GFC;EIE;GJQ;EKE;GLQ;EME;+BHC,iCG;GNC;EOE;GPM;EQE;GRE;ESE;GTM;EUE;GVM;EWE;GXE;EYE;GZE;EaE;GbM;EcE;GdO;EeE;GfQ;EgBE;iCbU;OaO;iCbE;OaE;GhBE;EiBE;GjBC;EkBE;GlBM;EmBE;GnBE;EoBE;GpBE;EqBE;GrBE;EsBE;+BnBE;KmBE;GtBC;EuBE;GvBE;EwBE;GxBE;CDE"}}, "type": "js/module"}]}