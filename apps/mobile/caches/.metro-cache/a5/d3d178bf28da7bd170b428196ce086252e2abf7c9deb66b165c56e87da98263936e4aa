{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Ligature = exports.default = (0, _createLucideIcon.default)(\"Ligature\", [[\"path\", {\n    d: \"M14 12h2v8\",\n    key: \"c1fccl\"\n  }], [\"path\", {\n    d: \"M14 20h4\",\n    key: \"lzx1xo\"\n  }], [\"path\", {\n    d: \"M6 12h4\",\n    key: \"a4o3ry\"\n  }], [\"path\", {\n    d: \"M6 20h4\",\n    key: \"1i6q5t\"\n  }], [\"path\", {\n    d: \"M8 20V8a4 4 0 0 1 7.464-2\",\n    key: \"wk9t6r\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Ligature"], [15, 16, 10, 14], [15, 19, 10, 14, "exports"], [15, 26, 10, 14], [15, 27, 10, 14, "default"], [15, 34, 10, 14], [15, 37, 10, 17], [15, 41, 10, 17, "createLucideIcon"], [15, 66, 10, 33], [15, 68, 10, 34], [15, 78, 10, 44], [15, 80, 10, 46], [15, 81, 11, 2], [15, 82, 11, 3], [15, 88, 11, 9], [15, 90, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 19, 11, 28], [17, 4, 11, 30, "key"], [17, 7, 11, 33], [17, 9, 11, 35], [18, 2, 11, 44], [18, 3, 11, 45], [18, 4, 11, 46], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 17, 12, 26], [20, 4, 12, 28, "key"], [20, 7, 12, 31], [20, 9, 12, 33], [21, 2, 12, 42], [21, 3, 12, 43], [21, 4, 12, 44], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 16, 13, 25], [23, 4, 13, 27, "key"], [23, 7, 13, 30], [23, 9, 13, 32], [24, 2, 13, 41], [24, 3, 13, 42], [24, 4, 13, 43], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 16, 14, 25], [26, 4, 14, 27, "key"], [26, 7, 14, 30], [26, 9, 14, 32], [27, 2, 14, 41], [27, 3, 14, 42], [27, 4, 14, 43], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 34, 15, 43], [29, 4, 15, 45, "key"], [29, 7, 15, 48], [29, 9, 15, 50], [30, 2, 15, 59], [30, 3, 15, 60], [30, 4, 15, 61], [30, 5, 16, 1], [30, 6, 16, 2], [31, 0, 16, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}