{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 12, "index": 26}, "end": {"line": 2, "column": 28, "index": 42}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var React = require(_dependencyMap[0], \"react\");\n  /**\n   * Use `useEffect` during SSR and `useLayoutEffect` in the Browser & React Native to avoid warnings.\n   */\n  var useClientLayoutEffect = typeof document !== 'undefined' || typeof navigator !== 'undefined' && navigator.product === 'ReactNative' ? React.useLayoutEffect : React.useEffect;\n  /**\n   * React hook which returns the latest callback without changing the reference.\n   */\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  function useLatestCallback(callback) {\n    var ref = React.useRef(callback);\n    var latestCallback = React.useRef(function latestCallback() {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return ref.current.apply(this, args);\n    }).current;\n    useClientLayoutEffect(function () {\n      ref.current = callback;\n    });\n    return latestCallback;\n  }\n  module.exports = useLatestCallback;\n});", "lineCount": 28, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "React"], [4, 11, 2, 9], [4, 14, 2, 12, "require"], [4, 21, 2, 19], [4, 22, 2, 19, "_dependencyMap"], [4, 36, 2, 19], [4, 48, 2, 27], [4, 49, 2, 28], [5, 2, 3, 0], [6, 0, 4, 0], [7, 0, 5, 0], [8, 2, 6, 0], [8, 6, 6, 4, "useClientLayoutEffect"], [8, 27, 6, 25], [8, 30, 6, 28], [8, 37, 6, 35, "document"], [8, 45, 6, 43], [8, 50, 6, 48], [8, 61, 6, 59], [8, 65, 7, 5], [8, 72, 7, 12, "navigator"], [8, 81, 7, 21], [8, 86, 7, 26], [8, 97, 7, 37], [8, 101, 7, 41, "navigator"], [8, 110, 7, 50], [8, 111, 7, 51, "product"], [8, 118, 7, 58], [8, 123, 7, 63], [8, 136, 7, 77], [8, 139, 8, 6, "React"], [8, 144, 8, 11], [8, 145, 8, 12, "useLayoutEffect"], [8, 160, 8, 27], [8, 163, 9, 6, "React"], [8, 168, 9, 11], [8, 169, 9, 12, "useEffect"], [8, 178, 9, 21], [9, 2, 10, 0], [10, 0, 11, 0], [11, 0, 12, 0], [12, 2, 13, 0], [13, 2, 14, 0], [13, 11, 14, 9, "useLatestCallback"], [13, 28, 14, 26, "useLatestCallback"], [13, 29, 14, 27, "callback"], [13, 37, 14, 35], [13, 39, 14, 37], [14, 4, 15, 4], [14, 8, 15, 8, "ref"], [14, 11, 15, 11], [14, 14, 15, 14, "React"], [14, 19, 15, 19], [14, 20, 15, 20, "useRef"], [14, 26, 15, 26], [14, 27, 15, 27, "callback"], [14, 35, 15, 35], [14, 36, 15, 36], [15, 4, 16, 4], [15, 8, 16, 8, "latestCallback"], [15, 22, 16, 22], [15, 25, 16, 25, "React"], [15, 30, 16, 30], [15, 31, 16, 31, "useRef"], [15, 37, 16, 37], [15, 38, 16, 38], [15, 47, 16, 47, "latestCallback"], [15, 61, 16, 61, "latestCallback"], [15, 62, 16, 61], [15, 64, 16, 64], [16, 6, 17, 8], [16, 10, 17, 12, "args"], [16, 14, 17, 16], [16, 17, 17, 19], [16, 19, 17, 21], [17, 6, 18, 8], [17, 11, 18, 13], [17, 15, 18, 17, "_i"], [17, 17, 18, 19], [17, 20, 18, 22], [17, 21, 18, 23], [17, 23, 18, 25, "_i"], [17, 25, 18, 27], [17, 28, 18, 30, "arguments"], [17, 37, 18, 39], [17, 38, 18, 40, "length"], [17, 44, 18, 46], [17, 46, 18, 48, "_i"], [17, 48, 18, 50], [17, 50, 18, 52], [17, 52, 18, 54], [18, 8, 19, 12, "args"], [18, 12, 19, 16], [18, 13, 19, 17, "_i"], [18, 15, 19, 19], [18, 16, 19, 20], [18, 19, 19, 23, "arguments"], [18, 28, 19, 32], [18, 29, 19, 33, "_i"], [18, 31, 19, 35], [18, 32, 19, 36], [19, 6, 20, 8], [20, 6, 21, 8], [20, 13, 21, 15, "ref"], [20, 16, 21, 18], [20, 17, 21, 19, "current"], [20, 24, 21, 26], [20, 25, 21, 27, "apply"], [20, 30, 21, 32], [20, 31, 21, 33], [20, 35, 21, 37], [20, 37, 21, 39, "args"], [20, 41, 21, 43], [20, 42, 21, 44], [21, 4, 22, 4], [21, 5, 22, 5], [21, 6, 22, 6], [21, 7, 22, 7, "current"], [21, 14, 22, 14], [22, 4, 23, 4, "useClientLayoutEffect"], [22, 25, 23, 25], [22, 26, 23, 26], [22, 38, 23, 38], [23, 6, 24, 8, "ref"], [23, 9, 24, 11], [23, 10, 24, 12, "current"], [23, 17, 24, 19], [23, 20, 24, 22, "callback"], [23, 28, 24, 30], [24, 4, 25, 4], [24, 5, 25, 5], [24, 6, 25, 6], [25, 4, 26, 4], [25, 11, 26, 11, "latestCallback"], [25, 25, 26, 25], [26, 2, 27, 0], [27, 2, 28, 0, "module"], [27, 8, 28, 6], [27, 9, 28, 7, "exports"], [27, 16, 28, 14], [27, 19, 28, 17, "useLatestCallback"], [27, 36, 28, 34], [28, 0, 28, 35], [28, 3]], "functionMap": {"names": ["<global>", "useLatestCallback", "latestCallback", "useClientLayoutEffect$argument_0"], "mappings": "AAA;ACa;sCCE;KDM;0BEC;KFE;CDE"}}, "type": "js/module"}]}