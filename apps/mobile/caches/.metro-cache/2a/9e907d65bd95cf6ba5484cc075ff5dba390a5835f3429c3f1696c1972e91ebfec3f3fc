{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.checkDuplicateRouteNames = checkDuplicateRouteNames;\n  function checkDuplicateRouteNames(state) {\n    var duplicates = [];\n    var getRouteNames = (location, state) => {\n      state.routes.forEach(route => {\n        var currentLocation = location ? `${location} > ${route.name}` : route.name;\n        route.state?.routeNames?.forEach(routeName => {\n          if (routeName === route.name) {\n            duplicates.push([currentLocation, `${currentLocation} > ${route.name}`]);\n          }\n        });\n        if (route.state) {\n          getRouteNames(currentLocation, route.state);\n        }\n      });\n    };\n    getRouteNames('', state);\n    return duplicates;\n  }\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "checkDuplicateRouteNames"], [7, 34, 1, 13], [7, 37, 1, 13, "checkDuplicateRouteNames"], [7, 61, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "checkDuplicateRouteNames"], [8, 35, 3, 40, "checkDuplicateRouteNames"], [8, 36, 3, 41, "state"], [8, 41, 3, 46], [8, 43, 3, 48], [9, 4, 4, 2], [9, 8, 4, 8, "duplicates"], [9, 18, 4, 18], [9, 21, 4, 21], [9, 23, 4, 23], [10, 4, 5, 2], [10, 8, 5, 8, "getRouteNames"], [10, 21, 5, 21], [10, 24, 5, 24, "getRouteNames"], [10, 25, 5, 25, "location"], [10, 33, 5, 33], [10, 35, 5, 35, "state"], [10, 40, 5, 40], [10, 45, 5, 45], [11, 6, 6, 4, "state"], [11, 11, 6, 9], [11, 12, 6, 10, "routes"], [11, 18, 6, 16], [11, 19, 6, 17, "for<PERSON>ach"], [11, 26, 6, 24], [11, 27, 6, 25, "route"], [11, 32, 6, 30], [11, 36, 6, 34], [12, 8, 7, 6], [12, 12, 7, 12, "currentLocation"], [12, 27, 7, 27], [12, 30, 7, 30, "location"], [12, 38, 7, 38], [12, 41, 7, 41], [12, 44, 7, 44, "location"], [12, 52, 7, 52], [12, 58, 7, 58, "route"], [12, 63, 7, 63], [12, 64, 7, 64, "name"], [12, 68, 7, 68], [12, 70, 7, 70], [12, 73, 7, 73, "route"], [12, 78, 7, 78], [12, 79, 7, 79, "name"], [12, 83, 7, 83], [13, 8, 8, 6, "route"], [13, 13, 8, 11], [13, 14, 8, 12, "state"], [13, 19, 8, 17], [13, 21, 8, 19, "routeNames"], [13, 31, 8, 29], [13, 33, 8, 31, "for<PERSON>ach"], [13, 40, 8, 38], [13, 41, 8, 39, "routeName"], [13, 50, 8, 48], [13, 54, 8, 52], [14, 10, 9, 8], [14, 14, 9, 12, "routeName"], [14, 23, 9, 21], [14, 28, 9, 26, "route"], [14, 33, 9, 31], [14, 34, 9, 32, "name"], [14, 38, 9, 36], [14, 40, 9, 38], [15, 12, 10, 10, "duplicates"], [15, 22, 10, 20], [15, 23, 10, 21, "push"], [15, 27, 10, 25], [15, 28, 10, 26], [15, 29, 10, 27, "currentLocation"], [15, 44, 10, 42], [15, 46, 10, 44], [15, 49, 10, 47, "currentLocation"], [15, 64, 10, 62], [15, 70, 10, 68, "route"], [15, 75, 10, 73], [15, 76, 10, 74, "name"], [15, 80, 10, 78], [15, 82, 10, 80], [15, 83, 10, 81], [15, 84, 10, 82], [16, 10, 11, 8], [17, 8, 12, 6], [17, 9, 12, 7], [17, 10, 12, 8], [18, 8, 13, 6], [18, 12, 13, 10, "route"], [18, 17, 13, 15], [18, 18, 13, 16, "state"], [18, 23, 13, 21], [18, 25, 13, 23], [19, 10, 14, 8, "getRouteNames"], [19, 23, 14, 21], [19, 24, 14, 22, "currentLocation"], [19, 39, 14, 37], [19, 41, 14, 39, "route"], [19, 46, 14, 44], [19, 47, 14, 45, "state"], [19, 52, 14, 50], [19, 53, 14, 51], [20, 8, 15, 6], [21, 6, 16, 4], [21, 7, 16, 5], [21, 8, 16, 6], [22, 4, 17, 2], [22, 5, 17, 3], [23, 4, 18, 2, "getRouteNames"], [23, 17, 18, 15], [23, 18, 18, 16], [23, 20, 18, 18], [23, 22, 18, 20, "state"], [23, 27, 18, 25], [23, 28, 18, 26], [24, 4, 19, 2], [24, 11, 19, 9, "duplicates"], [24, 21, 19, 19], [25, 2, 20, 0], [26, 0, 20, 1], [26, 3]], "functionMap": {"names": ["<global>", "checkDuplicateRouteNames", "getRouteNames", "state.routes.forEach$argument_0", "route.state.routeNames.forEach$argument_0"], "mappings": "AAA;OCE;wBCE;yBCC;uCCE;ODI;KDI;GDC;CDG"}}, "type": "js/module"}]}