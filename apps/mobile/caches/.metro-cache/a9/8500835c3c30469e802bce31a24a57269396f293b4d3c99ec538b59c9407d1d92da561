{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getHeaderTitle = getHeaderTitle;\n  function getHeaderTitle(options, fallback) {\n    return typeof options.headerTitle === 'string' ? options.headerTitle : options.title !== undefined ? options.title : fallback;\n  }\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getHeaderTitle"], [7, 24, 1, 13], [7, 27, 1, 13, "getHeaderTitle"], [7, 41, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "getHeaderTitle"], [8, 25, 3, 30, "getHeaderTitle"], [8, 26, 3, 31, "options"], [8, 33, 3, 38], [8, 35, 3, 40, "fallback"], [8, 43, 3, 48], [8, 45, 3, 50], [9, 4, 4, 2], [9, 11, 4, 9], [9, 18, 4, 16, "options"], [9, 25, 4, 23], [9, 26, 4, 24, "headerTitle"], [9, 37, 4, 35], [9, 42, 4, 40], [9, 50, 4, 48], [9, 53, 4, 51, "options"], [9, 60, 4, 58], [9, 61, 4, 59, "headerTitle"], [9, 72, 4, 70], [9, 75, 4, 73, "options"], [9, 82, 4, 80], [9, 83, 4, 81, "title"], [9, 88, 4, 86], [9, 93, 4, 91, "undefined"], [9, 102, 4, 100], [9, 105, 4, 103, "options"], [9, 112, 4, 110], [9, 113, 4, 111, "title"], [9, 118, 4, 116], [9, 121, 4, 119, "fallback"], [9, 129, 4, 127], [10, 2, 5, 0], [11, 0, 5, 1], [11, 3]], "functionMap": {"names": ["<global>", "getHeaderTitle"], "mappings": "AAA;OCE;CDE"}}, "type": "js/module"}]}