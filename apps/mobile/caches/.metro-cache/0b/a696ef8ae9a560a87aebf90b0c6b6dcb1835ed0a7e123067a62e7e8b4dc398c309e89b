{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.tabRouterOverride = void 0;\n  var tabRouterOverride = original => {\n    return {\n      ...original,\n      getStateForAction: (state, action, options) => {\n        if (action.target && action.target !== state.key) {\n          return null;\n        }\n        if (isReplaceAction(action)) {\n          // Generate the state as if we were using JUMP_TO\n          var nextState = original.getStateForAction(state, {\n            ...action,\n            type: 'JUMP_TO'\n          }, options);\n          if (!nextState || nextState.index === undefined || !Array.isArray(nextState.history)) {\n            return null;\n          }\n          // If the state is valid and we didn't JUMP_TO a single history state,\n          // then remove the previous state.\n          if (nextState.index !== 0) {\n            var previousIndex = nextState.index - 1;\n            nextState = {\n              ...nextState,\n              key: `${nextState.key}-replace`,\n              // Omit the previous history entry that we are replacing\n              history: [...nextState.history.slice(0, previousIndex), ...nextState.history.splice(nextState.index)]\n            };\n          }\n          return nextState;\n        }\n        return original.getStateForAction(state, action, options);\n      }\n    };\n  };\n  exports.tabRouterOverride = tabRouterOverride;\n  function isReplaceAction(action) {\n    return action.type === 'REPLACE';\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 3, 0, "Object"], [5, 8, 3, 6], [5, 9, 3, 7, "defineProperty"], [5, 23, 3, 21], [5, 24, 3, 22, "exports"], [5, 31, 3, 29], [5, 33, 3, 31], [5, 45, 3, 43], [5, 47, 3, 45], [6, 4, 3, 47, "value"], [6, 9, 3, 52], [6, 11, 3, 54], [7, 2, 3, 59], [7, 3, 3, 60], [7, 4, 3, 61], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "tabRouterOverride"], [8, 27, 4, 25], [8, 30, 4, 28], [8, 35, 4, 33], [8, 36, 4, 34], [9, 2, 5, 0], [9, 6, 5, 6, "tabRouterOverride"], [9, 23, 5, 23], [9, 26, 5, 27, "original"], [9, 34, 5, 35], [9, 38, 5, 40], [10, 4, 6, 4], [10, 11, 6, 11], [11, 6, 7, 8], [11, 9, 7, 11, "original"], [11, 17, 7, 19], [12, 6, 8, 8, "getStateForAction"], [12, 23, 8, 25], [12, 25, 8, 27, "getStateForAction"], [12, 26, 8, 28, "state"], [12, 31, 8, 33], [12, 33, 8, 35, "action"], [12, 39, 8, 41], [12, 41, 8, 43, "options"], [12, 48, 8, 50], [12, 53, 8, 55], [13, 8, 9, 12], [13, 12, 9, 16, "action"], [13, 18, 9, 22], [13, 19, 9, 23, "target"], [13, 25, 9, 29], [13, 29, 9, 33, "action"], [13, 35, 9, 39], [13, 36, 9, 40, "target"], [13, 42, 9, 46], [13, 47, 9, 51, "state"], [13, 52, 9, 56], [13, 53, 9, 57, "key"], [13, 56, 9, 60], [13, 58, 9, 62], [14, 10, 10, 16], [14, 17, 10, 23], [14, 21, 10, 27], [15, 8, 11, 12], [16, 8, 12, 12], [16, 12, 12, 16, "isReplaceAction"], [16, 27, 12, 31], [16, 28, 12, 32, "action"], [16, 34, 12, 38], [16, 35, 12, 39], [16, 37, 12, 41], [17, 10, 13, 16], [18, 10, 14, 16], [18, 14, 14, 20, "nextState"], [18, 23, 14, 29], [18, 26, 14, 32, "original"], [18, 34, 14, 40], [18, 35, 14, 41, "getStateForAction"], [18, 52, 14, 58], [18, 53, 14, 59, "state"], [18, 58, 14, 64], [18, 60, 14, 66], [19, 12, 15, 20], [19, 15, 15, 23, "action"], [19, 21, 15, 29], [20, 12, 16, 20, "type"], [20, 16, 16, 24], [20, 18, 16, 26], [21, 10, 17, 16], [21, 11, 17, 17], [21, 13, 17, 19, "options"], [21, 20, 17, 26], [21, 21, 17, 27], [22, 10, 18, 16], [22, 14, 18, 20], [22, 15, 18, 21, "nextState"], [22, 24, 18, 30], [22, 28, 18, 34, "nextState"], [22, 37, 18, 43], [22, 38, 18, 44, "index"], [22, 43, 18, 49], [22, 48, 18, 54, "undefined"], [22, 57, 18, 63], [22, 61, 18, 67], [22, 62, 18, 68, "Array"], [22, 67, 18, 73], [22, 68, 18, 74, "isArray"], [22, 75, 18, 81], [22, 76, 18, 82, "nextState"], [22, 85, 18, 91], [22, 86, 18, 92, "history"], [22, 93, 18, 99], [22, 94, 18, 100], [22, 96, 18, 102], [23, 12, 19, 20], [23, 19, 19, 27], [23, 23, 19, 31], [24, 10, 20, 16], [25, 10, 21, 16], [26, 10, 22, 16], [27, 10, 23, 16], [27, 14, 23, 20, "nextState"], [27, 23, 23, 29], [27, 24, 23, 30, "index"], [27, 29, 23, 35], [27, 34, 23, 40], [27, 35, 23, 41], [27, 37, 23, 43], [28, 12, 24, 20], [28, 16, 24, 26, "previousIndex"], [28, 29, 24, 39], [28, 32, 24, 42, "nextState"], [28, 41, 24, 51], [28, 42, 24, 52, "index"], [28, 47, 24, 57], [28, 50, 24, 60], [28, 51, 24, 61], [29, 12, 25, 20, "nextState"], [29, 21, 25, 29], [29, 24, 25, 32], [30, 14, 26, 24], [30, 17, 26, 27, "nextState"], [30, 26, 26, 36], [31, 14, 27, 24, "key"], [31, 17, 27, 27], [31, 19, 27, 29], [31, 22, 27, 32, "nextState"], [31, 31, 27, 41], [31, 32, 27, 42, "key"], [31, 35, 27, 45], [31, 45, 27, 55], [32, 14, 28, 24], [33, 14, 29, 24, "history"], [33, 21, 29, 31], [33, 23, 29, 33], [33, 24, 30, 28], [33, 27, 30, 31, "nextState"], [33, 36, 30, 40], [33, 37, 30, 41, "history"], [33, 44, 30, 48], [33, 45, 30, 49, "slice"], [33, 50, 30, 54], [33, 51, 30, 55], [33, 52, 30, 56], [33, 54, 30, 58, "previousIndex"], [33, 67, 30, 71], [33, 68, 30, 72], [33, 70, 31, 28], [33, 73, 31, 31, "nextState"], [33, 82, 31, 40], [33, 83, 31, 41, "history"], [33, 90, 31, 48], [33, 91, 31, 49, "splice"], [33, 97, 31, 55], [33, 98, 31, 56, "nextState"], [33, 107, 31, 65], [33, 108, 31, 66, "index"], [33, 113, 31, 71], [33, 114, 31, 72], [34, 12, 33, 20], [34, 13, 33, 21], [35, 10, 34, 16], [36, 10, 35, 16], [36, 17, 35, 23, "nextState"], [36, 26, 35, 32], [37, 8, 36, 12], [38, 8, 37, 12], [38, 15, 37, 19, "original"], [38, 23, 37, 27], [38, 24, 37, 28, "getStateForAction"], [38, 41, 37, 45], [38, 42, 37, 46, "state"], [38, 47, 37, 51], [38, 49, 37, 53, "action"], [38, 55, 37, 59], [38, 57, 37, 61, "options"], [38, 64, 37, 68], [38, 65, 37, 69], [39, 6, 38, 8], [40, 4, 39, 4], [40, 5, 39, 5], [41, 2, 40, 0], [41, 3, 40, 1], [42, 2, 41, 0, "exports"], [42, 9, 41, 7], [42, 10, 41, 8, "tabRouterOverride"], [42, 27, 41, 25], [42, 30, 41, 28, "tabRouterOverride"], [42, 47, 41, 45], [43, 2, 42, 0], [43, 11, 42, 9, "isReplaceAction"], [43, 26, 42, 24, "isReplaceAction"], [43, 27, 42, 25, "action"], [43, 33, 42, 31], [43, 35, 42, 33], [44, 4, 43, 4], [44, 11, 43, 11, "action"], [44, 17, 43, 17], [44, 18, 43, 18, "type"], [44, 22, 43, 22], [44, 27, 43, 27], [44, 36, 43, 36], [45, 2, 44, 0], [46, 0, 44, 1], [46, 3]], "functionMap": {"names": ["<global>", "tabRouterOverride", "getStateForAction", "isReplaceAction"], "mappings": "AAA;0BCI;2BCG;SD8B;CDE;AGE;CHE"}}, "type": "js/module"}]}