{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 70, "index": 70}}], "key": "DfKH1NNXqDIAaDOtB+YKkBB07j8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = resolveAssetSource;\n  var _registry = require(_dependencyMap[0], \"@react-native/assets-registry/registry\");\n  function resolveAssetSource(assetId) {\n    const asset = (0, _registry.getAssetByID)(assetId);\n    if (!asset) {\n      return null;\n    }\n    const type = !asset.type ? '' : `.${asset.type}`;\n    const assetPath = __DEV__ ? asset.httpServerLocation + '/' + asset.name + type : asset.httpServerLocation.replace(/\\.\\.\\//g, '_') + '/' + asset.name + type;\n    const fromUrl = new URL(assetPath, 'https://expo.dev');\n    return {\n      uri: fromUrl.toString().replace(fromUrl.origin, '')\n    };\n  }\n});", "lineCount": 19, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_registry"], [6, 15, 1, 0], [6, 18, 1, 0, "require"], [6, 25, 1, 0], [6, 26, 1, 0, "_dependencyMap"], [6, 40, 1, 0], [7, 2, 2, 15], [7, 11, 2, 24, "resolveAssetSource"], [7, 29, 2, 42, "resolveAssetSource"], [7, 30, 2, 43, "assetId"], [7, 37, 2, 50], [7, 39, 2, 52], [8, 4, 3, 4], [8, 10, 3, 10, "asset"], [8, 15, 3, 15], [8, 18, 3, 18], [8, 22, 3, 18, "getAssetByID"], [8, 44, 3, 30], [8, 46, 3, 31, "assetId"], [8, 53, 3, 38], [8, 54, 3, 39], [9, 4, 4, 4], [9, 8, 4, 8], [9, 9, 4, 9, "asset"], [9, 14, 4, 14], [9, 16, 4, 16], [10, 6, 5, 8], [10, 13, 5, 15], [10, 17, 5, 19], [11, 4, 6, 4], [12, 4, 7, 4], [12, 10, 7, 10, "type"], [12, 14, 7, 14], [12, 17, 7, 17], [12, 18, 7, 18, "asset"], [12, 23, 7, 23], [12, 24, 7, 24, "type"], [12, 28, 7, 28], [12, 31, 7, 31], [12, 33, 7, 33], [12, 36, 7, 36], [12, 40, 7, 40, "asset"], [12, 45, 7, 45], [12, 46, 7, 46, "type"], [12, 50, 7, 50], [12, 52, 7, 52], [13, 4, 8, 4], [13, 10, 8, 10, "assetPath"], [13, 19, 8, 19], [13, 22, 8, 22, "__DEV__"], [13, 29, 8, 29], [13, 32, 9, 10, "asset"], [13, 37, 9, 15], [13, 38, 9, 16, "httpServerLocation"], [13, 56, 9, 34], [13, 59, 9, 37], [13, 62, 9, 40], [13, 65, 9, 43, "asset"], [13, 70, 9, 48], [13, 71, 9, 49, "name"], [13, 75, 9, 53], [13, 78, 9, 56, "type"], [13, 82, 9, 60], [13, 85, 10, 10, "asset"], [13, 90, 10, 15], [13, 91, 10, 16, "httpServerLocation"], [13, 109, 10, 34], [13, 110, 10, 35, "replace"], [13, 117, 10, 42], [13, 118, 10, 43], [13, 127, 10, 52], [13, 129, 10, 54], [13, 132, 10, 57], [13, 133, 10, 58], [13, 136, 10, 61], [13, 139, 10, 64], [13, 142, 10, 67, "asset"], [13, 147, 10, 72], [13, 148, 10, 73, "name"], [13, 152, 10, 77], [13, 155, 10, 80, "type"], [13, 159, 10, 84], [14, 4, 11, 4], [14, 10, 11, 10, "fromUrl"], [14, 17, 11, 17], [14, 20, 11, 20], [14, 24, 11, 24, "URL"], [14, 27, 11, 27], [14, 28, 11, 28, "assetPath"], [14, 37, 11, 37], [14, 39, 11, 39], [14, 57, 11, 57], [14, 58, 11, 58], [15, 4, 12, 4], [15, 11, 12, 11], [16, 6, 12, 13, "uri"], [16, 9, 12, 16], [16, 11, 12, 18, "fromUrl"], [16, 18, 12, 25], [16, 19, 12, 26, "toString"], [16, 27, 12, 34], [16, 28, 12, 35], [16, 29, 12, 36], [16, 30, 12, 37, "replace"], [16, 37, 12, 44], [16, 38, 12, 45, "fromUrl"], [16, 45, 12, 52], [16, 46, 12, 53, "origin"], [16, 52, 12, 59], [16, 54, 12, 61], [16, 56, 12, 63], [17, 4, 12, 65], [17, 5, 12, 66], [18, 2, 13, 0], [19, 0, 13, 1], [19, 3]], "functionMap": {"names": ["<global>", "resolveAssetSource"], "mappings": "AAA;eCC;CDW"}}, "type": "js/module"}]}