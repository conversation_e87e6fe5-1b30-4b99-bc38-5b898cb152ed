{"dependencies": [{"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "zustand", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 33, "index": 33}}], "key": "POPvx7yS3Y3wz+S/9OAefiXNs0Q=", "exportNames": ["*"]}}, {"name": "expo-secure-store", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 34}, "end": {"line": 2, "column": 49, "index": 83}}], "key": "BU2XtfznZ4PiVldqd/oueHCCaLo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAuthStore = exports.useAuthModal = exports.authKey = void 0;\n  var _env2 = require(_dependencyMap[0], \"expo/virtual/env\");\n  var _zustand = require(_dependencyMap[1], \"zustand\");\n  var SecureStore = _interopRequireWildcard(require(_dependencyMap[2], \"expo-secure-store\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var authKey = exports.authKey = `${_env2.env.EXPO_PUBLIC_PROJECT_GROUP_ID}-jwt`;\n\n  /**\n   * This store manages the authentication state of the application.\n   */\n  var useAuthStore = exports.useAuthStore = (0, _zustand.create)(set => ({\n    isReady: false,\n    auth: null,\n    setAuth: auth => {\n      if (auth) {\n        SecureStore.setItemAsync(authKey, JSON.stringify(auth));\n      } else {\n        SecureStore.deleteItemAsync(authKey);\n      }\n      set({\n        auth\n      });\n    }\n  }));\n\n  /**\n   * This store manages the state of the authentication modal.\n   */\n  var useAuthModal = exports.useAuthModal = (0, _zustand.create)(set => ({\n    isOpen: false,\n    mode: 'signup',\n    open: options => set({\n      isOpen: true,\n      mode: options?.mode || 'signup'\n    }),\n    close: () => set({\n      isOpen: false\n    })\n  }));\n});", "lineCount": 44, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_zustand"], [7, 14, 1, 0], [7, 17, 1, 0, "require"], [7, 24, 1, 0], [7, 25, 1, 0, "_dependencyMap"], [7, 39, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "SecureStore"], [8, 17, 2, 0], [8, 20, 2, 0, "_interopRequireWildcard"], [8, 43, 2, 0], [8, 44, 2, 0, "require"], [8, 51, 2, 0], [8, 52, 2, 0, "_dependencyMap"], [8, 66, 2, 0], [9, 2, 2, 49], [9, 11, 2, 49, "_interopRequireWildcard"], [9, 35, 2, 49, "e"], [9, 36, 2, 49], [9, 38, 2, 49, "t"], [9, 39, 2, 49], [9, 68, 2, 49, "WeakMap"], [9, 75, 2, 49], [9, 81, 2, 49, "r"], [9, 82, 2, 49], [9, 89, 2, 49, "WeakMap"], [9, 96, 2, 49], [9, 100, 2, 49, "n"], [9, 101, 2, 49], [9, 108, 2, 49, "WeakMap"], [9, 115, 2, 49], [9, 127, 2, 49, "_interopRequireWildcard"], [9, 150, 2, 49], [9, 162, 2, 49, "_interopRequireWildcard"], [9, 163, 2, 49, "e"], [9, 164, 2, 49], [9, 166, 2, 49, "t"], [9, 167, 2, 49], [9, 176, 2, 49, "t"], [9, 177, 2, 49], [9, 181, 2, 49, "e"], [9, 182, 2, 49], [9, 186, 2, 49, "e"], [9, 187, 2, 49], [9, 188, 2, 49, "__esModule"], [9, 198, 2, 49], [9, 207, 2, 49, "e"], [9, 208, 2, 49], [9, 214, 2, 49, "o"], [9, 215, 2, 49], [9, 217, 2, 49, "i"], [9, 218, 2, 49], [9, 220, 2, 49, "f"], [9, 221, 2, 49], [9, 226, 2, 49, "__proto__"], [9, 235, 2, 49], [9, 243, 2, 49, "default"], [9, 250, 2, 49], [9, 252, 2, 49, "e"], [9, 253, 2, 49], [9, 270, 2, 49, "e"], [9, 271, 2, 49], [9, 294, 2, 49, "e"], [9, 295, 2, 49], [9, 320, 2, 49, "e"], [9, 321, 2, 49], [9, 330, 2, 49, "f"], [9, 331, 2, 49], [9, 337, 2, 49, "o"], [9, 338, 2, 49], [9, 341, 2, 49, "t"], [9, 342, 2, 49], [9, 345, 2, 49, "n"], [9, 346, 2, 49], [9, 349, 2, 49, "r"], [9, 350, 2, 49], [9, 358, 2, 49, "o"], [9, 359, 2, 49], [9, 360, 2, 49, "has"], [9, 363, 2, 49], [9, 364, 2, 49, "e"], [9, 365, 2, 49], [9, 375, 2, 49, "o"], [9, 376, 2, 49], [9, 377, 2, 49, "get"], [9, 380, 2, 49], [9, 381, 2, 49, "e"], [9, 382, 2, 49], [9, 385, 2, 49, "o"], [9, 386, 2, 49], [9, 387, 2, 49, "set"], [9, 390, 2, 49], [9, 391, 2, 49, "e"], [9, 392, 2, 49], [9, 394, 2, 49, "f"], [9, 395, 2, 49], [9, 409, 2, 49, "_t"], [9, 411, 2, 49], [9, 415, 2, 49, "e"], [9, 416, 2, 49], [9, 432, 2, 49, "_t"], [9, 434, 2, 49], [9, 441, 2, 49, "hasOwnProperty"], [9, 455, 2, 49], [9, 456, 2, 49, "call"], [9, 460, 2, 49], [9, 461, 2, 49, "e"], [9, 462, 2, 49], [9, 464, 2, 49, "_t"], [9, 466, 2, 49], [9, 473, 2, 49, "i"], [9, 474, 2, 49], [9, 478, 2, 49, "o"], [9, 479, 2, 49], [9, 482, 2, 49, "Object"], [9, 488, 2, 49], [9, 489, 2, 49, "defineProperty"], [9, 503, 2, 49], [9, 508, 2, 49, "Object"], [9, 514, 2, 49], [9, 515, 2, 49, "getOwnPropertyDescriptor"], [9, 539, 2, 49], [9, 540, 2, 49, "e"], [9, 541, 2, 49], [9, 543, 2, 49, "_t"], [9, 545, 2, 49], [9, 552, 2, 49, "i"], [9, 553, 2, 49], [9, 554, 2, 49, "get"], [9, 557, 2, 49], [9, 561, 2, 49, "i"], [9, 562, 2, 49], [9, 563, 2, 49, "set"], [9, 566, 2, 49], [9, 570, 2, 49, "o"], [9, 571, 2, 49], [9, 572, 2, 49, "f"], [9, 573, 2, 49], [9, 575, 2, 49, "_t"], [9, 577, 2, 49], [9, 579, 2, 49, "i"], [9, 580, 2, 49], [9, 584, 2, 49, "f"], [9, 585, 2, 49], [9, 586, 2, 49, "_t"], [9, 588, 2, 49], [9, 592, 2, 49, "e"], [9, 593, 2, 49], [9, 594, 2, 49, "_t"], [9, 596, 2, 49], [9, 607, 2, 49, "f"], [9, 608, 2, 49], [9, 613, 2, 49, "e"], [9, 614, 2, 49], [9, 616, 2, 49, "t"], [9, 617, 2, 49], [10, 2, 4, 7], [10, 6, 4, 13, "auth<PERSON><PERSON>"], [10, 13, 4, 20], [10, 16, 4, 20, "exports"], [10, 23, 4, 20], [10, 24, 4, 20, "auth<PERSON><PERSON>"], [10, 31, 4, 20], [10, 34, 4, 23], [10, 37, 4, 23, "_env2"], [10, 42, 4, 23], [10, 43, 4, 23, "env"], [10, 46, 4, 23], [10, 47, 4, 23, "EXPO_PUBLIC_PROJECT_GROUP_ID"], [10, 75, 4, 23], [10, 81, 4, 72], [12, 2, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 2, 9, 7], [15, 6, 9, 13, "useAuthStore"], [15, 18, 9, 25], [15, 21, 9, 25, "exports"], [15, 28, 9, 25], [15, 29, 9, 25, "useAuthStore"], [15, 41, 9, 25], [15, 44, 9, 28], [15, 48, 9, 28, "create"], [15, 63, 9, 34], [15, 65, 9, 36, "set"], [15, 68, 9, 39], [15, 73, 9, 45], [16, 4, 10, 2, "isReady"], [16, 11, 10, 9], [16, 13, 10, 11], [16, 18, 10, 16], [17, 4, 11, 2, "auth"], [17, 8, 11, 6], [17, 10, 11, 8], [17, 14, 11, 12], [18, 4, 12, 2, "setAuth"], [18, 11, 12, 9], [18, 13, 12, 12, "auth"], [18, 17, 12, 16], [18, 21, 12, 21], [19, 6, 13, 4], [19, 10, 13, 8, "auth"], [19, 14, 13, 12], [19, 16, 13, 14], [20, 8, 14, 6, "SecureStore"], [20, 19, 14, 17], [20, 20, 14, 18, "setItemAsync"], [20, 32, 14, 30], [20, 33, 14, 31, "auth<PERSON><PERSON>"], [20, 40, 14, 38], [20, 42, 14, 40, "JSON"], [20, 46, 14, 44], [20, 47, 14, 45, "stringify"], [20, 56, 14, 54], [20, 57, 14, 55, "auth"], [20, 61, 14, 59], [20, 62, 14, 60], [20, 63, 14, 61], [21, 6, 15, 4], [21, 7, 15, 5], [21, 13, 15, 11], [22, 8, 16, 6, "SecureStore"], [22, 19, 16, 17], [22, 20, 16, 18, "deleteItemAsync"], [22, 35, 16, 33], [22, 36, 16, 34, "auth<PERSON><PERSON>"], [22, 43, 16, 41], [22, 44, 16, 42], [23, 6, 17, 4], [24, 6, 18, 4, "set"], [24, 9, 18, 7], [24, 10, 18, 8], [25, 8, 18, 10, "auth"], [26, 6, 18, 15], [26, 7, 18, 16], [26, 8, 18, 17], [27, 4, 19, 2], [28, 2, 20, 0], [28, 3, 20, 1], [28, 4, 20, 2], [28, 5, 20, 3], [30, 2, 22, 0], [31, 0, 23, 0], [32, 0, 24, 0], [33, 2, 25, 7], [33, 6, 25, 13, "useAuthModal"], [33, 18, 25, 25], [33, 21, 25, 25, "exports"], [33, 28, 25, 25], [33, 29, 25, 25, "useAuthModal"], [33, 41, 25, 25], [33, 44, 25, 28], [33, 48, 25, 28, "create"], [33, 63, 25, 34], [33, 65, 25, 36, "set"], [33, 68, 25, 39], [33, 73, 25, 45], [34, 4, 26, 2, "isOpen"], [34, 10, 26, 8], [34, 12, 26, 10], [34, 17, 26, 15], [35, 4, 27, 2, "mode"], [35, 8, 27, 6], [35, 10, 27, 8], [35, 18, 27, 16], [36, 4, 28, 2, "open"], [36, 8, 28, 6], [36, 10, 28, 9, "options"], [36, 17, 28, 16], [36, 21, 28, 21, "set"], [36, 24, 28, 24], [36, 25, 28, 25], [37, 6, 28, 27, "isOpen"], [37, 12, 28, 33], [37, 14, 28, 35], [37, 18, 28, 39], [38, 6, 28, 41, "mode"], [38, 10, 28, 45], [38, 12, 28, 47, "options"], [38, 19, 28, 54], [38, 21, 28, 56, "mode"], [38, 25, 28, 60], [38, 29, 28, 64], [39, 4, 28, 73], [39, 5, 28, 74], [39, 6, 28, 75], [40, 4, 29, 2, "close"], [40, 9, 29, 7], [40, 11, 29, 9, "close"], [40, 12, 29, 9], [40, 17, 29, 15, "set"], [40, 20, 29, 18], [40, 21, 29, 19], [41, 6, 29, 21, "isOpen"], [41, 12, 29, 27], [41, 14, 29, 29], [42, 4, 29, 35], [42, 5, 29, 36], [43, 2, 30, 0], [43, 3, 30, 1], [43, 4, 30, 2], [43, 5, 30, 3], [44, 0, 30, 4], [44, 3]], "functionMap": {"names": ["<global>", "create$argument_0", "setAuth", "open", "close"], "mappings": "AAA;mCCQ;WCG;GDO;EDC;mCCK;QEG,mEF;SGC,4BH;EDC"}}, "type": "js/module"}]}