{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n});", "lineCount": 3, "map": [[2, 2, 1, 0, "module"], [2, 8, 1, 6], [2, 9, 1, 7, "exports"], [2, 16, 1, 14], [2, 19, 1, 17, "Function"], [2, 27, 1, 25], [2, 28, 1, 26, "call"], [2, 32, 1, 30], [2, 33, 1, 31, "bind"], [2, 37, 1, 35], [2, 38, 1, 36, "Object"], [2, 44, 1, 42], [2, 45, 1, 43, "prototype"], [2, 54, 1, 52], [2, 55, 1, 53, "hasOwnProperty"], [2, 69, 1, 67], [2, 70, 1, 68], [3, 0, 1, 69], [3, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}