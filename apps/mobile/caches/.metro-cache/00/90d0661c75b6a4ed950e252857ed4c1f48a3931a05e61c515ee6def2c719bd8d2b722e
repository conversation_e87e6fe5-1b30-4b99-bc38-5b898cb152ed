{"dependencies": [{"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 65}, "end": {"line": 3, "column": 51, "index": 116}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.shareableMappingFlag = exports.shareableMappingCache = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"./PlatformChecker\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n\n  /**\n   * This symbol is used to represent a mapping from the value to itself.\n   *\n   * It's used to prevent converting a shareable that's already converted - for\n   * example a Shared Value that's in worklet's closure.\n   */\n  var shareableMappingFlag = exports.shareableMappingFlag = Symbol('shareable flag');\n\n  /*\n  During a fast refresh, <PERSON><PERSON> holds the same instance of a Mutable\n  (that's guaranteed by `useRef`) but `shareableCache` gets regenerated and thus\n  becoming empty. This happens when editing the file that contains the definition of this cache.\n  \n  Because of it, `makeShareableCloneRecursive` can't find given mapping\n  in `shareableCache` for the Mutable and tries to clone it as if it was a regular JS object.\n  During cloning we use `Object.entries` to iterate over the keys which throws an error on accessing `_value`.\n  For convenience we moved this cache to a separate file so it doesn't scare us with red squiggles.\n  */\n\n  var cache = SHOULD_BE_USE_WEB ? null : new WeakMap();\n  var shareableMappingCache = exports.shareableMappingCache = SHOULD_BE_USE_WEB ? {\n    set() {\n      // NOOP\n    },\n    get() {\n      return null;\n    }\n  } : {\n    set(shareable, shareableRef) {\n      cache.set(shareable, shareableRef || shareableMappingFlag);\n    },\n    get: cache.get.bind(cache)\n  };\n});", "lineCount": 44, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "shareableMappingFlag"], [7, 30, 1, 13], [7, 33, 1, 13, "exports"], [7, 40, 1, 13], [7, 41, 1, 13, "shareableMappingCache"], [7, 62, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 5, 0], [9, 6, 5, 6, "SHOULD_BE_USE_WEB"], [9, 23, 5, 23], [9, 26, 5, 26], [9, 30, 5, 26, "shouldBeUseWeb"], [9, 61, 5, 40], [9, 63, 5, 41], [9, 64, 5, 42], [11, 2, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 2, 13, 7], [17, 6, 13, 13, "shareableMappingFlag"], [17, 26, 13, 33], [17, 29, 13, 33, "exports"], [17, 36, 13, 33], [17, 37, 13, 33, "shareableMappingFlag"], [17, 57, 13, 33], [17, 60, 13, 36, "Symbol"], [17, 66, 13, 42], [17, 67, 13, 43], [17, 83, 13, 59], [17, 84, 13, 60], [19, 2, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 0, 20, 0], [25, 0, 21, 0], [26, 0, 22, 0], [27, 0, 23, 0], [28, 0, 24, 0], [30, 2, 26, 0], [30, 6, 26, 6, "cache"], [30, 11, 26, 11], [30, 14, 26, 14, "SHOULD_BE_USE_WEB"], [30, 31, 26, 31], [30, 34, 27, 4], [30, 38, 27, 8], [30, 41, 28, 4], [30, 45, 28, 8, "WeakMap"], [30, 52, 28, 15], [30, 53, 28, 47], [30, 54, 28, 48], [31, 2, 30, 7], [31, 6, 30, 13, "shareableMappingCache"], [31, 27, 30, 34], [31, 30, 30, 34, "exports"], [31, 37, 30, 34], [31, 38, 30, 34, "shareableMappingCache"], [31, 59, 30, 34], [31, 62, 30, 37, "SHOULD_BE_USE_WEB"], [31, 79, 30, 54], [31, 82, 31, 4], [32, 4, 32, 6, "set"], [32, 7, 32, 9, "set"], [32, 8, 32, 9], [32, 10, 32, 12], [33, 6, 33, 8], [34, 4, 33, 8], [34, 5, 34, 7], [35, 4, 35, 6, "get"], [35, 7, 35, 9, "get"], [35, 8, 35, 9], [35, 10, 35, 12], [36, 6, 36, 8], [36, 13, 36, 15], [36, 17, 36, 19], [37, 4, 37, 6], [38, 2, 38, 4], [38, 3, 38, 5], [38, 6, 39, 4], [39, 4, 40, 6, "set"], [39, 7, 40, 9, "set"], [39, 8, 40, 10, "shareable"], [39, 17, 40, 27], [39, 19, 40, 29, "shareableRef"], [39, 31, 40, 56], [39, 33, 40, 64], [40, 6, 41, 8, "cache"], [40, 11, 41, 13], [40, 12, 41, 15, "set"], [40, 15, 41, 18], [40, 16, 41, 19, "shareable"], [40, 25, 41, 28], [40, 27, 41, 30, "shareableRef"], [40, 39, 41, 42], [40, 43, 41, 46, "shareableMappingFlag"], [40, 63, 41, 66], [40, 64, 41, 67], [41, 4, 42, 6], [41, 5, 42, 7], [42, 4, 43, 6, "get"], [42, 7, 43, 9], [42, 9, 43, 11, "cache"], [42, 14, 43, 16], [42, 15, 43, 18, "get"], [42, 18, 43, 21], [42, 19, 43, 22, "bind"], [42, 23, 43, 26], [42, 24, 43, 27, "cache"], [42, 29, 43, 32], [43, 2, 44, 4], [43, 3, 44, 5], [44, 0, 44, 6], [44, 3]], "functionMap": {"names": ["<global>", "set", "get"], "mappings": "AAA;MC+B;ODE;MEC;OFE;MCG;ODE"}}, "type": "js/module"}]}