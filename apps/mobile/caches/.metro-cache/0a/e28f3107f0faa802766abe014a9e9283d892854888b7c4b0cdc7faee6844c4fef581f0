{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./queryObserver.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 51, "index": 83}}], "key": "T5bdga4o8lhxjwmnTyuBuLPduVg=", "exportNames": ["*"]}}, {"name": "./infiniteQueryBehavior.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 84}, "end": {"line": 7, "column": 36, "index": 187}}], "key": "Dj+TcYB7Bmtxbfu2mfnKFBaMfIM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.InfiniteQueryObserver = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _queryObserver = require(_dependencyMap[7], \"./queryObserver.js\");\n  var _infiniteQueryBehavior = require(_dependencyMap[8], \"./infiniteQueryBehavior.js\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; } // src/infiniteQueryObserver.ts\n  var InfiniteQueryObserver = exports.InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n    function InfiniteQueryObserver(client, options) {\n      (0, _classCallCheck2.default)(this, InfiniteQueryObserver);\n      return _callSuper(this, InfiniteQueryObserver, [client, options]);\n    }\n    (0, _inherits2.default)(InfiniteQueryObserver, _QueryObserver);\n    return (0, _createClass2.default)(InfiniteQueryObserver, [{\n      key: \"bindMethods\",\n      value: function bindMethods() {\n        _superPropGet(InfiniteQueryObserver, \"bindMethods\", this, 3)([]);\n        this.fetchNextPage = this.fetchNextPage.bind(this);\n        this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n      }\n    }, {\n      key: \"setOptions\",\n      value: function setOptions(options) {\n        _superPropGet(InfiniteQueryObserver, \"setOptions\", this, 3)([{\n          ...options,\n          behavior: (0, _infiniteQueryBehavior.infiniteQueryBehavior)()\n        }]);\n      }\n    }, {\n      key: \"getOptimisticResult\",\n      value: function getOptimisticResult(options) {\n        options.behavior = (0, _infiniteQueryBehavior.infiniteQueryBehavior)();\n        return _superPropGet(InfiniteQueryObserver, \"getOptimisticResult\", this, 3)([options]);\n      }\n    }, {\n      key: \"fetchNextPage\",\n      value: function fetchNextPage(options) {\n        return this.fetch({\n          ...options,\n          meta: {\n            fetchMore: {\n              direction: \"forward\"\n            }\n          }\n        });\n      }\n    }, {\n      key: \"fetchPreviousPage\",\n      value: function fetchPreviousPage(options) {\n        return this.fetch({\n          ...options,\n          meta: {\n            fetchMore: {\n              direction: \"backward\"\n            }\n          }\n        });\n      }\n    }, {\n      key: \"createResult\",\n      value: function createResult(query, options) {\n        var state = query.state;\n        var parentResult = _superPropGet(InfiniteQueryObserver, \"createResult\", this, 3)([query, options]);\n        var isFetching = parentResult.isFetching,\n          isRefetching = parentResult.isRefetching,\n          isError = parentResult.isError,\n          isRefetchError = parentResult.isRefetchError;\n        var fetchDirection = state.fetchMeta?.fetchMore?.direction;\n        var isFetchNextPageError = isError && fetchDirection === \"forward\";\n        var isFetchingNextPage = isFetching && fetchDirection === \"forward\";\n        var isFetchPreviousPageError = isError && fetchDirection === \"backward\";\n        var isFetchingPreviousPage = isFetching && fetchDirection === \"backward\";\n        var result = {\n          ...parentResult,\n          fetchNextPage: this.fetchNextPage,\n          fetchPreviousPage: this.fetchPreviousPage,\n          hasNextPage: (0, _infiniteQueryBehavior.hasNextPage)(options, state.data),\n          hasPreviousPage: (0, _infiniteQueryBehavior.hasPreviousPage)(options, state.data),\n          isFetchNextPageError,\n          isFetchingNextPage,\n          isFetchPreviousPageError,\n          isFetchingPreviousPage,\n          isRefetchError: isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n          isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n        };\n        return result;\n      }\n    }]);\n  }(_queryObserver.QueryObserver);\n});", "lineCount": 100, "map": [[13, 2, 2, 0], [13, 6, 2, 0, "_queryObserver"], [13, 20, 2, 0], [13, 23, 2, 0, "require"], [13, 30, 2, 0], [13, 31, 2, 0, "_dependencyMap"], [13, 45, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_infiniteQueryBehavior"], [14, 28, 3, 0], [14, 31, 3, 0, "require"], [14, 38, 3, 0], [14, 39, 3, 0, "_dependencyMap"], [14, 53, 3, 0], [15, 2, 7, 36], [15, 11, 7, 36, "_callSuper"], [15, 22, 7, 36, "t"], [15, 23, 7, 36], [15, 25, 7, 36, "o"], [15, 26, 7, 36], [15, 28, 7, 36, "e"], [15, 29, 7, 36], [15, 40, 7, 36, "o"], [15, 41, 7, 36], [15, 48, 7, 36, "_getPrototypeOf2"], [15, 64, 7, 36], [15, 65, 7, 36, "default"], [15, 72, 7, 36], [15, 74, 7, 36, "o"], [15, 75, 7, 36], [15, 82, 7, 36, "_possibleConstructorReturn2"], [15, 109, 7, 36], [15, 110, 7, 36, "default"], [15, 117, 7, 36], [15, 119, 7, 36, "t"], [15, 120, 7, 36], [15, 122, 7, 36, "_isNativeReflectConstruct"], [15, 147, 7, 36], [15, 152, 7, 36, "Reflect"], [15, 159, 7, 36], [15, 160, 7, 36, "construct"], [15, 169, 7, 36], [15, 170, 7, 36, "o"], [15, 171, 7, 36], [15, 173, 7, 36, "e"], [15, 174, 7, 36], [15, 186, 7, 36, "_getPrototypeOf2"], [15, 202, 7, 36], [15, 203, 7, 36, "default"], [15, 210, 7, 36], [15, 212, 7, 36, "t"], [15, 213, 7, 36], [15, 215, 7, 36, "constructor"], [15, 226, 7, 36], [15, 230, 7, 36, "o"], [15, 231, 7, 36], [15, 232, 7, 36, "apply"], [15, 237, 7, 36], [15, 238, 7, 36, "t"], [15, 239, 7, 36], [15, 241, 7, 36, "e"], [15, 242, 7, 36], [16, 2, 7, 36], [16, 11, 7, 36, "_isNativeReflectConstruct"], [16, 37, 7, 36], [16, 51, 7, 36, "t"], [16, 52, 7, 36], [16, 56, 7, 36, "Boolean"], [16, 63, 7, 36], [16, 64, 7, 36, "prototype"], [16, 73, 7, 36], [16, 74, 7, 36, "valueOf"], [16, 81, 7, 36], [16, 82, 7, 36, "call"], [16, 86, 7, 36], [16, 87, 7, 36, "Reflect"], [16, 94, 7, 36], [16, 95, 7, 36, "construct"], [16, 104, 7, 36], [16, 105, 7, 36, "Boolean"], [16, 112, 7, 36], [16, 145, 7, 36, "t"], [16, 146, 7, 36], [16, 159, 7, 36, "_isNativeReflectConstruct"], [16, 184, 7, 36], [16, 196, 7, 36, "_isNativeReflectConstruct"], [16, 197, 7, 36], [16, 210, 7, 36, "t"], [16, 211, 7, 36], [17, 2, 7, 36], [17, 11, 7, 36, "_superPropGet"], [17, 25, 7, 36, "t"], [17, 26, 7, 36], [17, 28, 7, 36, "o"], [17, 29, 7, 36], [17, 31, 7, 36, "e"], [17, 32, 7, 36], [17, 34, 7, 36, "r"], [17, 35, 7, 36], [17, 43, 7, 36, "p"], [17, 44, 7, 36], [17, 51, 7, 36, "_get2"], [17, 56, 7, 36], [17, 57, 7, 36, "default"], [17, 64, 7, 36], [17, 70, 7, 36, "_getPrototypeOf2"], [17, 86, 7, 36], [17, 87, 7, 36, "default"], [17, 94, 7, 36], [17, 100, 7, 36, "r"], [17, 101, 7, 36], [17, 104, 7, 36, "t"], [17, 105, 7, 36], [17, 106, 7, 36, "prototype"], [17, 115, 7, 36], [17, 118, 7, 36, "t"], [17, 119, 7, 36], [17, 122, 7, 36, "o"], [17, 123, 7, 36], [17, 125, 7, 36, "e"], [17, 126, 7, 36], [17, 140, 7, 36, "r"], [17, 141, 7, 36], [17, 166, 7, 36, "p"], [17, 167, 7, 36], [17, 180, 7, 36, "t"], [17, 181, 7, 36], [17, 192, 7, 36, "p"], [17, 193, 7, 36], [17, 194, 7, 36, "apply"], [17, 199, 7, 36], [17, 200, 7, 36, "e"], [17, 201, 7, 36], [17, 203, 7, 36, "t"], [17, 204, 7, 36], [17, 211, 7, 36, "p"], [17, 212, 7, 36], [17, 216, 1, 0], [18, 2, 8, 0], [18, 6, 8, 4, "InfiniteQueryObserver"], [18, 27, 8, 25], [18, 30, 8, 25, "exports"], [18, 37, 8, 25], [18, 38, 8, 25, "InfiniteQueryObserver"], [18, 59, 8, 25], [18, 85, 8, 25, "_QueryObserver"], [18, 99, 8, 25], [19, 4, 9, 2], [19, 13, 9, 2, "InfiniteQueryObserver"], [19, 35, 9, 14, "client"], [19, 41, 9, 20], [19, 43, 9, 22, "options"], [19, 50, 9, 29], [19, 52, 9, 31], [20, 6, 9, 31], [20, 10, 9, 31, "_classCallCheck2"], [20, 26, 9, 31], [20, 27, 9, 31, "default"], [20, 34, 9, 31], [20, 42, 9, 31, "InfiniteQueryObserver"], [20, 63, 9, 31], [21, 6, 9, 31], [21, 13, 9, 31, "_callSuper"], [21, 23, 9, 31], [21, 30, 9, 31, "InfiniteQueryObserver"], [21, 51, 9, 31], [21, 54, 10, 10, "client"], [21, 60, 10, 16], [21, 62, 10, 18, "options"], [21, 69, 10, 25], [22, 4, 11, 2], [23, 4, 11, 3], [23, 8, 11, 3, "_inherits2"], [23, 18, 11, 3], [23, 19, 11, 3, "default"], [23, 26, 11, 3], [23, 28, 11, 3, "InfiniteQueryObserver"], [23, 49, 11, 3], [23, 51, 11, 3, "_QueryObserver"], [23, 65, 11, 3], [24, 4, 11, 3], [24, 15, 11, 3, "_createClass2"], [24, 28, 11, 3], [24, 29, 11, 3, "default"], [24, 36, 11, 3], [24, 38, 11, 3, "InfiniteQueryObserver"], [24, 59, 11, 3], [25, 6, 11, 3, "key"], [25, 9, 11, 3], [26, 6, 11, 3, "value"], [26, 11, 11, 3], [26, 13, 12, 2], [26, 22, 12, 2, "bindMethods"], [26, 33, 12, 13, "bindMethods"], [26, 34, 12, 13], [26, 36, 12, 16], [27, 8, 13, 4, "_superPropGet"], [27, 21, 13, 4], [27, 22, 13, 4, "InfiniteQueryObserver"], [27, 43, 13, 4], [28, 8, 14, 4], [28, 12, 14, 8], [28, 13, 14, 9, "fetchNextPage"], [28, 26, 14, 22], [28, 29, 14, 25], [28, 33, 14, 29], [28, 34, 14, 30, "fetchNextPage"], [28, 47, 14, 43], [28, 48, 14, 44, "bind"], [28, 52, 14, 48], [28, 53, 14, 49], [28, 57, 14, 53], [28, 58, 14, 54], [29, 8, 15, 4], [29, 12, 15, 8], [29, 13, 15, 9, "fetchPreviousPage"], [29, 30, 15, 26], [29, 33, 15, 29], [29, 37, 15, 33], [29, 38, 15, 34, "fetchPreviousPage"], [29, 55, 15, 51], [29, 56, 15, 52, "bind"], [29, 60, 15, 56], [29, 61, 15, 57], [29, 65, 15, 61], [29, 66, 15, 62], [30, 6, 16, 2], [31, 4, 16, 3], [32, 6, 16, 3, "key"], [32, 9, 16, 3], [33, 6, 16, 3, "value"], [33, 11, 16, 3], [33, 13, 17, 2], [33, 22, 17, 2, "setOptions"], [33, 32, 17, 12, "setOptions"], [33, 33, 17, 13, "options"], [33, 40, 17, 20], [33, 42, 17, 22], [34, 8, 18, 4, "_superPropGet"], [34, 21, 18, 4], [34, 22, 18, 4, "InfiniteQueryObserver"], [34, 43, 18, 4], [34, 69, 18, 21], [35, 10, 19, 6], [35, 13, 19, 9, "options"], [35, 20, 19, 16], [36, 10, 20, 6, "behavior"], [36, 18, 20, 14], [36, 20, 20, 16], [36, 24, 20, 16, "infiniteQueryBehavior"], [36, 68, 20, 37], [36, 70, 20, 38], [37, 8, 21, 4], [37, 9, 21, 5], [38, 6, 22, 2], [39, 4, 22, 3], [40, 6, 22, 3, "key"], [40, 9, 22, 3], [41, 6, 22, 3, "value"], [41, 11, 22, 3], [41, 13, 23, 2], [41, 22, 23, 2, "getOptimisticResult"], [41, 41, 23, 21, "getOptimisticResult"], [41, 42, 23, 22, "options"], [41, 49, 23, 29], [41, 51, 23, 31], [42, 8, 24, 4, "options"], [42, 15, 24, 11], [42, 16, 24, 12, "behavior"], [42, 24, 24, 20], [42, 27, 24, 23], [42, 31, 24, 23, "infiniteQueryBehavior"], [42, 75, 24, 44], [42, 77, 24, 45], [42, 78, 24, 46], [43, 8, 25, 4], [43, 15, 25, 4, "_superPropGet"], [43, 28, 25, 4], [43, 29, 25, 4, "InfiniteQueryObserver"], [43, 50, 25, 4], [43, 85, 25, 37, "options"], [43, 92, 25, 44], [44, 6, 26, 2], [45, 4, 26, 3], [46, 6, 26, 3, "key"], [46, 9, 26, 3], [47, 6, 26, 3, "value"], [47, 11, 26, 3], [47, 13, 27, 2], [47, 22, 27, 2, "fetchNextPage"], [47, 35, 27, 15, "fetchNextPage"], [47, 36, 27, 16, "options"], [47, 43, 27, 23], [47, 45, 27, 25], [48, 8, 28, 4], [48, 15, 28, 11], [48, 19, 28, 15], [48, 20, 28, 16, "fetch"], [48, 25, 28, 21], [48, 26, 28, 22], [49, 10, 29, 6], [49, 13, 29, 9, "options"], [49, 20, 29, 16], [50, 10, 30, 6, "meta"], [50, 14, 30, 10], [50, 16, 30, 12], [51, 12, 31, 8, "fetchMore"], [51, 21, 31, 17], [51, 23, 31, 19], [52, 14, 31, 21, "direction"], [52, 23, 31, 30], [52, 25, 31, 32], [53, 12, 31, 42], [54, 10, 32, 6], [55, 8, 33, 4], [55, 9, 33, 5], [55, 10, 33, 6], [56, 6, 34, 2], [57, 4, 34, 3], [58, 6, 34, 3, "key"], [58, 9, 34, 3], [59, 6, 34, 3, "value"], [59, 11, 34, 3], [59, 13, 35, 2], [59, 22, 35, 2, "fetchPreviousPage"], [59, 39, 35, 19, "fetchPreviousPage"], [59, 40, 35, 20, "options"], [59, 47, 35, 27], [59, 49, 35, 29], [60, 8, 36, 4], [60, 15, 36, 11], [60, 19, 36, 15], [60, 20, 36, 16, "fetch"], [60, 25, 36, 21], [60, 26, 36, 22], [61, 10, 37, 6], [61, 13, 37, 9, "options"], [61, 20, 37, 16], [62, 10, 38, 6, "meta"], [62, 14, 38, 10], [62, 16, 38, 12], [63, 12, 39, 8, "fetchMore"], [63, 21, 39, 17], [63, 23, 39, 19], [64, 14, 39, 21, "direction"], [64, 23, 39, 30], [64, 25, 39, 32], [65, 12, 39, 43], [66, 10, 40, 6], [67, 8, 41, 4], [67, 9, 41, 5], [67, 10, 41, 6], [68, 6, 42, 2], [69, 4, 42, 3], [70, 6, 42, 3, "key"], [70, 9, 42, 3], [71, 6, 42, 3, "value"], [71, 11, 42, 3], [71, 13, 43, 2], [71, 22, 43, 2, "createResult"], [71, 34, 43, 14, "createResult"], [71, 35, 43, 15, "query"], [71, 40, 43, 20], [71, 42, 43, 22, "options"], [71, 49, 43, 29], [71, 51, 43, 31], [72, 8, 44, 4], [72, 12, 44, 12, "state"], [72, 17, 44, 17], [72, 20, 44, 22, "query"], [72, 25, 44, 27], [72, 26, 44, 12, "state"], [72, 31, 44, 17], [73, 8, 45, 4], [73, 12, 45, 10, "parentResult"], [73, 24, 45, 22], [73, 27, 45, 22, "_superPropGet"], [73, 40, 45, 22], [73, 41, 45, 22, "InfiniteQueryObserver"], [73, 62, 45, 22], [73, 90, 45, 44, "query"], [73, 95, 45, 49], [73, 97, 45, 51, "options"], [73, 104, 45, 58], [73, 106, 45, 59], [74, 8, 46, 4], [74, 12, 46, 12, "isFetching"], [74, 22, 46, 22], [74, 25, 46, 66, "parentResult"], [74, 37, 46, 78], [74, 38, 46, 12, "isFetching"], [74, 48, 46, 22], [75, 10, 46, 24, "isRefetching"], [75, 22, 46, 36], [75, 25, 46, 66, "parentResult"], [75, 37, 46, 78], [75, 38, 46, 24, "isRefetching"], [75, 50, 46, 36], [76, 10, 46, 38, "isError"], [76, 17, 46, 45], [76, 20, 46, 66, "parentResult"], [76, 32, 46, 78], [76, 33, 46, 38, "isError"], [76, 40, 46, 45], [77, 10, 46, 47, "isRefetchError"], [77, 24, 46, 61], [77, 27, 46, 66, "parentResult"], [77, 39, 46, 78], [77, 40, 46, 47, "isRefetchError"], [77, 54, 46, 61], [78, 8, 47, 4], [78, 12, 47, 10, "fetchDirection"], [78, 26, 47, 24], [78, 29, 47, 27, "state"], [78, 34, 47, 32], [78, 35, 47, 33, "fetchMeta"], [78, 44, 47, 42], [78, 46, 47, 44, "fetchMore"], [78, 55, 47, 53], [78, 57, 47, 55, "direction"], [78, 66, 47, 64], [79, 8, 48, 4], [79, 12, 48, 10, "isFetchNextPageError"], [79, 32, 48, 30], [79, 35, 48, 33, "isError"], [79, 42, 48, 40], [79, 46, 48, 44, "fetchDirection"], [79, 60, 48, 58], [79, 65, 48, 63], [79, 74, 48, 72], [80, 8, 49, 4], [80, 12, 49, 10, "isFetchingNextPage"], [80, 30, 49, 28], [80, 33, 49, 31, "isFetching"], [80, 43, 49, 41], [80, 47, 49, 45, "fetchDirection"], [80, 61, 49, 59], [80, 66, 49, 64], [80, 75, 49, 73], [81, 8, 50, 4], [81, 12, 50, 10, "isFetchPreviousPageError"], [81, 36, 50, 34], [81, 39, 50, 37, "isError"], [81, 46, 50, 44], [81, 50, 50, 48, "fetchDirection"], [81, 64, 50, 62], [81, 69, 50, 67], [81, 79, 50, 77], [82, 8, 51, 4], [82, 12, 51, 10, "isFetchingPreviousPage"], [82, 34, 51, 32], [82, 37, 51, 35, "isFetching"], [82, 47, 51, 45], [82, 51, 51, 49, "fetchDirection"], [82, 65, 51, 63], [82, 70, 51, 68], [82, 80, 51, 78], [83, 8, 52, 4], [83, 12, 52, 10, "result"], [83, 18, 52, 16], [83, 21, 52, 19], [84, 10, 53, 6], [84, 13, 53, 9, "parentResult"], [84, 25, 53, 21], [85, 10, 54, 6, "fetchNextPage"], [85, 23, 54, 19], [85, 25, 54, 21], [85, 29, 54, 25], [85, 30, 54, 26, "fetchNextPage"], [85, 43, 54, 39], [86, 10, 55, 6, "fetchPreviousPage"], [86, 27, 55, 23], [86, 29, 55, 25], [86, 33, 55, 29], [86, 34, 55, 30, "fetchPreviousPage"], [86, 51, 55, 47], [87, 10, 56, 6, "hasNextPage"], [87, 21, 56, 17], [87, 23, 56, 19], [87, 27, 56, 19, "hasNextPage"], [87, 61, 56, 30], [87, 63, 56, 31, "options"], [87, 70, 56, 38], [87, 72, 56, 40, "state"], [87, 77, 56, 45], [87, 78, 56, 46, "data"], [87, 82, 56, 50], [87, 83, 56, 51], [88, 10, 57, 6, "hasPreviousPage"], [88, 25, 57, 21], [88, 27, 57, 23], [88, 31, 57, 23, "hasPreviousPage"], [88, 69, 57, 38], [88, 71, 57, 39, "options"], [88, 78, 57, 46], [88, 80, 57, 48, "state"], [88, 85, 57, 53], [88, 86, 57, 54, "data"], [88, 90, 57, 58], [88, 91, 57, 59], [89, 10, 58, 6, "isFetchNextPageError"], [89, 30, 58, 26], [90, 10, 59, 6, "isFetchingNextPage"], [90, 28, 59, 24], [91, 10, 60, 6, "isFetchPreviousPageError"], [91, 34, 60, 30], [92, 10, 61, 6, "isFetchingPreviousPage"], [92, 32, 61, 28], [93, 10, 62, 6, "isRefetchError"], [93, 24, 62, 20], [93, 26, 62, 22, "isRefetchError"], [93, 40, 62, 36], [93, 44, 62, 40], [93, 45, 62, 41, "isFetchNextPageError"], [93, 65, 62, 61], [93, 69, 62, 65], [93, 70, 62, 66, "isFetchPreviousPageError"], [93, 94, 62, 90], [94, 10, 63, 6, "isRefetching"], [94, 22, 63, 18], [94, 24, 63, 20, "isRefetching"], [94, 36, 63, 32], [94, 40, 63, 36], [94, 41, 63, 37, "isFetchingNextPage"], [94, 59, 63, 55], [94, 63, 63, 59], [94, 64, 63, 60, "isFetchingPreviousPage"], [95, 8, 64, 4], [95, 9, 64, 5], [96, 8, 65, 4], [96, 15, 65, 11, "result"], [96, 21, 65, 17], [97, 6, 66, 2], [98, 4, 66, 3], [99, 2, 66, 3], [99, 4, 8, 42, "QueryObserver"], [99, 32, 8, 55], [99, 33, 67, 1], [100, 0, 67, 2], [100, 3]], "functionMap": {"names": ["<global>", "InfiniteQueryObserver", "InfiniteQueryObserver#constructor", "InfiniteQueryObserver#bindMethods", "InfiniteQueryObserver#setOptions", "InfiniteQueryObserver#getOptimisticResult", "InfiniteQueryObserver#fetchNextPage", "InfiniteQueryObserver#fetchPreviousPage", "InfiniteQueryObserver#createResult"], "mappings": "AAA;4BCO;ECC;GDE;EEC;GFI;EGC;GHK;EIC;GJG;EKC;GLO;EMC;GNO;EOC;GPuB;CDC"}}, "type": "js/module"}]}