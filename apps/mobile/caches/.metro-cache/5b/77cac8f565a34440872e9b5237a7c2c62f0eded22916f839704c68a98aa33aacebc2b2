{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getDefaultSidebarWidth = void 0;\n  const APPROX_APP_BAR_HEIGHT = 56;\n  const DEFAULT_DRAWER_WIDTH = 360;\n  const getDefaultSidebarWidth = ({\n    width\n  }) => {\n    /**\n     * Default sidebar width is 360dp\n     * On screens smaller than 320dp, ideally the drawer would collapse to a tab bar\n     * https://m3.material.io/components/navigation-drawer/specs\n     */\n    if (width - APPROX_APP_BAR_HEIGHT <= 360) {\n      return width - APPROX_APP_BAR_HEIGHT;\n    }\n    return DEFAULT_DRAWER_WIDTH;\n  };\n  exports.getDefaultSidebarWidth = getDefaultSidebarWidth;\n});", "lineCount": 24, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getDefaultSidebarWidth"], [7, 32, 1, 13], [8, 2, 3, 0], [8, 8, 3, 6, "APPROX_APP_BAR_HEIGHT"], [8, 29, 3, 27], [8, 32, 3, 30], [8, 34, 3, 32], [9, 2, 4, 0], [9, 8, 4, 6, "DEFAULT_DRAWER_WIDTH"], [9, 28, 4, 26], [9, 31, 4, 29], [9, 34, 4, 32], [10, 2, 5, 7], [10, 8, 5, 13, "getDefaultSidebarWidth"], [10, 30, 5, 35], [10, 33, 5, 38, "getDefaultSidebarWidth"], [10, 34, 5, 39], [11, 4, 6, 2, "width"], [12, 2, 7, 0], [12, 3, 7, 1], [12, 8, 7, 6], [13, 4, 8, 2], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 4, 13, 2], [18, 8, 13, 6, "width"], [18, 13, 13, 11], [18, 16, 13, 14, "APPROX_APP_BAR_HEIGHT"], [18, 37, 13, 35], [18, 41, 13, 39], [18, 44, 13, 42], [18, 46, 13, 44], [19, 6, 14, 4], [19, 13, 14, 11, "width"], [19, 18, 14, 16], [19, 21, 14, 19, "APPROX_APP_BAR_HEIGHT"], [19, 42, 14, 40], [20, 4, 15, 2], [21, 4, 16, 2], [21, 11, 16, 9, "DEFAULT_DRAWER_WIDTH"], [21, 31, 16, 29], [22, 2, 17, 0], [22, 3, 17, 1], [23, 2, 17, 2, "exports"], [23, 9, 17, 2], [23, 10, 17, 2, "getDefaultSidebarWidth"], [23, 32, 17, 2], [23, 35, 17, 2, "getDefaultSidebarWidth"], [23, 57, 17, 2], [24, 0, 17, 2], [24, 3]], "functionMap": {"names": ["<global>", "getDefaultSidebarWidth"], "mappings": "AAA;sCCI;CDY"}}, "type": "js/module"}]}