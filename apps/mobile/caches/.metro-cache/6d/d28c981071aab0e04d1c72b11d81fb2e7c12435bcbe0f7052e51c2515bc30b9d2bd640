{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 63, "index": 78}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 79}, "end": {"line": 4, "column": 31, "index": 110}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "../assets/back-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 187}, "end": {"line": 6, "column": 47, "index": 234}}], "key": "HkeccI6hsuVTlj25dEjFpK2xoJM=", "exportNames": ["*"]}}, {"name": "../assets/back-icon-mask.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 235}, "end": {"line": 7, "column": 56, "index": 291}}], "key": "AmHFzlS4CVlJZw6ZbDJ43fmMtBk=", "exportNames": ["*"]}}, {"name": "../MaskedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 292}, "end": {"line": 8, "column": 43, "index": 335}}], "key": "BHKqLDT9VAjLwsU321BC8dwD7Us=", "exportNames": ["*"]}}, {"name": "./HeaderButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 336}, "end": {"line": 9, "column": 49, "index": 385}}], "key": "5Mfp2bWqztZ2HFy80uJBbvbN6HA=", "exportNames": ["*"]}}, {"name": "./HeaderIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 386}, "end": {"line": 10, "column": 58, "index": 444}}], "key": "0JPASIZzwd0DulPaj/kDrorllj8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 445}, "end": {"line": 11, "column": 63, "index": 508}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderBackButton = HeaderBackButton;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Animated\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Image\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/View\"));\n  var _backIcon = _interopRequireDefault(require(_dependencyMap[8], \"../assets/back-icon.png\"));\n  var _backIconMask = _interopRequireDefault(require(_dependencyMap[9], \"../assets/back-icon-mask.png\"));\n  var _MaskedView = require(_dependencyMap[10], \"../MaskedView\");\n  var _HeaderButton = require(_dependencyMap[11], \"./HeaderButton.js\");\n  var _HeaderIcon = require(_dependencyMap[12], \"./HeaderIcon.js\");\n  var _jsxRuntime = require(_dependencyMap[13], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function HeaderBackButton({\n    disabled,\n    allowFontScaling,\n    backImage,\n    label,\n    labelStyle,\n    displayMode = _Platform.default.OS === 'ios' ? 'default' : 'minimal',\n    onLabelLayout,\n    onPress,\n    pressColor,\n    pressOpacity,\n    screenLayout,\n    tintColor,\n    titleLayout,\n    truncatedLabel = 'Back',\n    accessibilityLabel = label && label !== 'Back' ? `${label}, back` : 'Go back',\n    testID,\n    style,\n    href\n  }) {\n    const {\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    const {\n      direction\n    } = (0, _native.useLocale)();\n    const [labelWidth, setLabelWidth] = React.useState(null);\n    const [truncatedLabelWidth, setTruncatedLabelWidth] = React.useState(null);\n    const renderBackImage = () => {\n      if (backImage) {\n        return backImage({\n          tintColor: tintColor ?? colors.text\n        });\n      } else {\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {\n          source: _backIcon.default,\n          tintColor: tintColor,\n          style: [styles.icon, displayMode !== 'minimal' && styles.iconWithLabel]\n        });\n      }\n    };\n    const renderLabel = () => {\n      if (displayMode === 'minimal') {\n        return null;\n      }\n      const availableSpace = titleLayout && screenLayout ? (screenLayout.width - titleLayout.width) / 2 - (ICON_WIDTH + _HeaderIcon.ICON_MARGIN) : null;\n      const potentialLabelText = displayMode === 'default' ? label : truncatedLabel;\n      const finalLabelText = availableSpace && labelWidth && truncatedLabelWidth ? availableSpace > labelWidth ? potentialLabelText : availableSpace > truncatedLabelWidth ? truncatedLabel : null : potentialLabelText;\n      const commonStyle = [fonts.regular, styles.label, labelStyle];\n      const hiddenStyle = [commonStyle, {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        opacity: 0\n      }];\n      const labelElement = /*#__PURE__*/(0, _jsxRuntime.jsxs)(_View.default, {\n        style: styles.labelWrapper,\n        children: [label && displayMode === 'default' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.Text, {\n          style: hiddenStyle,\n          numberOfLines: 1,\n          onLayout: e => setLabelWidth(e.nativeEvent.layout.width),\n          children: label\n        }) : null, truncatedLabel ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.Text, {\n          style: hiddenStyle,\n          numberOfLines: 1,\n          onLayout: e => setTruncatedLabelWidth(e.nativeEvent.layout.width),\n          children: truncatedLabel\n        }) : null, finalLabelText ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.Text, {\n          accessible: false,\n          onLayout: onLabelLayout,\n          style: [tintColor ? {\n            color: tintColor\n          } : null, commonStyle],\n          numberOfLines: 1,\n          allowFontScaling: !!allowFontScaling,\n          children: finalLabelText\n        }) : null]\n      });\n      if (backImage || _Platform.default.OS !== 'ios') {\n        // When a custom backimage is specified, we can't mask the label\n        // Otherwise there might be weird effect due to our mask not being the same as the image\n        return labelElement;\n      }\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_MaskedView.MaskedView, {\n        maskElement: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_View.default, {\n          style: [styles.iconMaskContainer,\n          // Extend the mask to the center of the screen so that label isn't clipped during animation\n          screenLayout ? {\n            minWidth: screenLayout.width / 2 - 27\n          } : null],\n          children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_Image.default, {\n            source: _backIconMask.default,\n            resizeMode: \"contain\",\n            style: [styles.iconMask, direction === 'rtl' && styles.flip]\n          }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n            style: styles.iconMaskFillerRect\n          })]\n        }),\n        children: labelElement\n      });\n    };\n    const handlePress = () => {\n      if (onPress) {\n        requestAnimationFrame(() => onPress());\n      }\n    };\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderButton.HeaderButton, {\n      disabled: disabled,\n      href: href,\n      accessibilityLabel: accessibilityLabel,\n      testID: testID,\n      onPress: handlePress,\n      pressColor: pressColor,\n      pressOpacity: pressOpacity,\n      style: [styles.container, style],\n      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {\n        children: [renderBackImage(), renderLabel()]\n      })\n    });\n  }\n  const ICON_WIDTH = _Platform.default.OS === 'ios' ? 13 : 24;\n  const ICON_MARGIN_END = _Platform.default.OS === 'ios' ? 22 : 3;\n  const styles = _StyleSheet.default.create({\n    container: {\n      paddingHorizontal: 0,\n      minWidth: _StyleSheet.default.hairlineWidth,\n      // Avoid collapsing when title is long\n      ..._Platform.default.select({\n        ios: null,\n        default: {\n          marginVertical: 3,\n          marginHorizontal: 11\n        }\n      })\n    },\n    label: {\n      fontSize: 17,\n      // Title and back label are a bit different width due to title being bold\n      // Adjusting the letterSpacing makes them coincide better\n      letterSpacing: 0.35\n    },\n    labelWrapper: {\n      // These styles will make sure that the label doesn't fill the available space\n      // Otherwise it messes with the measurement of the label\n      flexDirection: 'row',\n      alignItems: 'flex-start',\n      marginEnd: _HeaderIcon.ICON_MARGIN\n    },\n    icon: {\n      width: ICON_WIDTH,\n      marginEnd: ICON_MARGIN_END\n    },\n    iconWithLabel: _Platform.default.OS === 'ios' ? {\n      marginEnd: 6\n    } : {},\n    iconMaskContainer: {\n      flex: 1,\n      flexDirection: 'row',\n      justifyContent: 'center'\n    },\n    iconMaskFillerRect: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    iconMask: {\n      height: 21,\n      width: 13,\n      marginStart: -14.5,\n      marginVertical: 12,\n      alignSelf: 'center'\n    },\n    flip: {\n      transform: 'scaleX(-1)'\n    }\n  });\n});", "lineCount": 199, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 26, 1, 13], [8, 29, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 45, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "React"], [10, 11, 4, 0], [10, 14, 4, 0, "_interopRequireWildcard"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 4, 31], [11, 6, 4, 31, "_Animated"], [11, 15, 4, 31], [11, 18, 4, 31, "_interopRequireDefault"], [11, 40, 4, 31], [11, 41, 4, 31, "require"], [11, 48, 4, 31], [11, 49, 4, 31, "_dependencyMap"], [11, 63, 4, 31], [12, 2, 4, 31], [12, 6, 4, 31, "_Image"], [12, 12, 4, 31], [12, 15, 4, 31, "_interopRequireDefault"], [12, 37, 4, 31], [12, 38, 4, 31, "require"], [12, 45, 4, 31], [12, 46, 4, 31, "_dependencyMap"], [12, 60, 4, 31], [13, 2, 4, 31], [13, 6, 4, 31, "_Platform"], [13, 15, 4, 31], [13, 18, 4, 31, "_interopRequireDefault"], [13, 40, 4, 31], [13, 41, 4, 31, "require"], [13, 48, 4, 31], [13, 49, 4, 31, "_dependencyMap"], [13, 63, 4, 31], [14, 2, 4, 31], [14, 6, 4, 31, "_StyleSheet"], [14, 17, 4, 31], [14, 20, 4, 31, "_interopRequireDefault"], [14, 42, 4, 31], [14, 43, 4, 31, "require"], [14, 50, 4, 31], [14, 51, 4, 31, "_dependencyMap"], [14, 65, 4, 31], [15, 2, 4, 31], [15, 6, 4, 31, "_View"], [15, 11, 4, 31], [15, 14, 4, 31, "_interopRequireDefault"], [15, 36, 4, 31], [15, 37, 4, 31, "require"], [15, 44, 4, 31], [15, 45, 4, 31, "_dependencyMap"], [15, 59, 4, 31], [16, 2, 6, 0], [16, 6, 6, 0, "_backIcon"], [16, 15, 6, 0], [16, 18, 6, 0, "_interopRequireDefault"], [16, 40, 6, 0], [16, 41, 6, 0, "require"], [16, 48, 6, 0], [16, 49, 6, 0, "_dependencyMap"], [16, 63, 6, 0], [17, 2, 7, 0], [17, 6, 7, 0, "_backIconMask"], [17, 19, 7, 0], [17, 22, 7, 0, "_interopRequireDefault"], [17, 44, 7, 0], [17, 45, 7, 0, "require"], [17, 52, 7, 0], [17, 53, 7, 0, "_dependencyMap"], [17, 67, 7, 0], [18, 2, 8, 0], [18, 6, 8, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 17, 8, 0], [18, 20, 8, 0, "require"], [18, 27, 8, 0], [18, 28, 8, 0, "_dependencyMap"], [18, 42, 8, 0], [19, 2, 9, 0], [19, 6, 9, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, 19, 9, 0], [19, 22, 9, 0, "require"], [19, 29, 9, 0], [19, 30, 9, 0, "_dependencyMap"], [19, 44, 9, 0], [20, 2, 10, 0], [20, 6, 10, 0, "_HeaderIcon"], [20, 17, 10, 0], [20, 20, 10, 0, "require"], [20, 27, 10, 0], [20, 28, 10, 0, "_dependencyMap"], [20, 42, 10, 0], [21, 2, 11, 0], [21, 6, 11, 0, "_jsxRuntime"], [21, 17, 11, 0], [21, 20, 11, 0, "require"], [21, 27, 11, 0], [21, 28, 11, 0, "_dependencyMap"], [21, 42, 11, 0], [22, 2, 11, 63], [22, 11, 11, 63, "_interopRequireWildcard"], [22, 35, 11, 63, "e"], [22, 36, 11, 63], [22, 38, 11, 63, "t"], [22, 39, 11, 63], [22, 68, 11, 63, "WeakMap"], [22, 75, 11, 63], [22, 81, 11, 63, "r"], [22, 82, 11, 63], [22, 89, 11, 63, "WeakMap"], [22, 96, 11, 63], [22, 100, 11, 63, "n"], [22, 101, 11, 63], [22, 108, 11, 63, "WeakMap"], [22, 115, 11, 63], [22, 127, 11, 63, "_interopRequireWildcard"], [22, 150, 11, 63], [22, 162, 11, 63, "_interopRequireWildcard"], [22, 163, 11, 63, "e"], [22, 164, 11, 63], [22, 166, 11, 63, "t"], [22, 167, 11, 63], [22, 176, 11, 63, "t"], [22, 177, 11, 63], [22, 181, 11, 63, "e"], [22, 182, 11, 63], [22, 186, 11, 63, "e"], [22, 187, 11, 63], [22, 188, 11, 63, "__esModule"], [22, 198, 11, 63], [22, 207, 11, 63, "e"], [22, 208, 11, 63], [22, 214, 11, 63, "o"], [22, 215, 11, 63], [22, 217, 11, 63, "i"], [22, 218, 11, 63], [22, 220, 11, 63, "f"], [22, 221, 11, 63], [22, 226, 11, 63, "__proto__"], [22, 235, 11, 63], [22, 243, 11, 63, "default"], [22, 250, 11, 63], [22, 252, 11, 63, "e"], [22, 253, 11, 63], [22, 270, 11, 63, "e"], [22, 271, 11, 63], [22, 294, 11, 63, "e"], [22, 295, 11, 63], [22, 320, 11, 63, "e"], [22, 321, 11, 63], [22, 330, 11, 63, "f"], [22, 331, 11, 63], [22, 337, 11, 63, "o"], [22, 338, 11, 63], [22, 341, 11, 63, "t"], [22, 342, 11, 63], [22, 345, 11, 63, "n"], [22, 346, 11, 63], [22, 349, 11, 63, "r"], [22, 350, 11, 63], [22, 358, 11, 63, "o"], [22, 359, 11, 63], [22, 360, 11, 63, "has"], [22, 363, 11, 63], [22, 364, 11, 63, "e"], [22, 365, 11, 63], [22, 375, 11, 63, "o"], [22, 376, 11, 63], [22, 377, 11, 63, "get"], [22, 380, 11, 63], [22, 381, 11, 63, "e"], [22, 382, 11, 63], [22, 385, 11, 63, "o"], [22, 386, 11, 63], [22, 387, 11, 63, "set"], [22, 390, 11, 63], [22, 391, 11, 63, "e"], [22, 392, 11, 63], [22, 394, 11, 63, "f"], [22, 395, 11, 63], [22, 411, 11, 63, "t"], [22, 412, 11, 63], [22, 416, 11, 63, "e"], [22, 417, 11, 63], [22, 433, 11, 63, "t"], [22, 434, 11, 63], [22, 441, 11, 63, "hasOwnProperty"], [22, 455, 11, 63], [22, 456, 11, 63, "call"], [22, 460, 11, 63], [22, 461, 11, 63, "e"], [22, 462, 11, 63], [22, 464, 11, 63, "t"], [22, 465, 11, 63], [22, 472, 11, 63, "i"], [22, 473, 11, 63], [22, 477, 11, 63, "o"], [22, 478, 11, 63], [22, 481, 11, 63, "Object"], [22, 487, 11, 63], [22, 488, 11, 63, "defineProperty"], [22, 502, 11, 63], [22, 507, 11, 63, "Object"], [22, 513, 11, 63], [22, 514, 11, 63, "getOwnPropertyDescriptor"], [22, 538, 11, 63], [22, 539, 11, 63, "e"], [22, 540, 11, 63], [22, 542, 11, 63, "t"], [22, 543, 11, 63], [22, 550, 11, 63, "i"], [22, 551, 11, 63], [22, 552, 11, 63, "get"], [22, 555, 11, 63], [22, 559, 11, 63, "i"], [22, 560, 11, 63], [22, 561, 11, 63, "set"], [22, 564, 11, 63], [22, 568, 11, 63, "o"], [22, 569, 11, 63], [22, 570, 11, 63, "f"], [22, 571, 11, 63], [22, 573, 11, 63, "t"], [22, 574, 11, 63], [22, 576, 11, 63, "i"], [22, 577, 11, 63], [22, 581, 11, 63, "f"], [22, 582, 11, 63], [22, 583, 11, 63, "t"], [22, 584, 11, 63], [22, 588, 11, 63, "e"], [22, 589, 11, 63], [22, 590, 11, 63, "t"], [22, 591, 11, 63], [22, 602, 11, 63, "f"], [22, 603, 11, 63], [22, 608, 11, 63, "e"], [22, 609, 11, 63], [22, 611, 11, 63, "t"], [22, 612, 11, 63], [23, 2, 12, 7], [23, 11, 12, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 27, 12, 32, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 28, 12, 33], [24, 4, 13, 2, "disabled"], [24, 12, 13, 10], [25, 4, 14, 2, "allowFontScaling"], [25, 20, 14, 18], [26, 4, 15, 2, "backImage"], [26, 13, 15, 11], [27, 4, 16, 2, "label"], [27, 9, 16, 7], [28, 4, 17, 2, "labelStyle"], [28, 14, 17, 12], [29, 4, 18, 2, "displayMode"], [29, 15, 18, 13], [29, 18, 18, 16, "Platform"], [29, 35, 18, 24], [29, 36, 18, 25, "OS"], [29, 38, 18, 27], [29, 43, 18, 32], [29, 48, 18, 37], [29, 51, 18, 40], [29, 60, 18, 49], [29, 63, 18, 52], [29, 72, 18, 61], [30, 4, 19, 2, "onLabelLayout"], [30, 17, 19, 15], [31, 4, 20, 2, "onPress"], [31, 11, 20, 9], [32, 4, 21, 2, "pressColor"], [32, 14, 21, 12], [33, 4, 22, 2, "pressOpacity"], [33, 16, 22, 14], [34, 4, 23, 2, "screenLayout"], [34, 16, 23, 14], [35, 4, 24, 2, "tintColor"], [35, 13, 24, 11], [36, 4, 25, 2, "titleLayout"], [36, 15, 25, 13], [37, 4, 26, 2, "truncatedLabel"], [37, 18, 26, 16], [37, 21, 26, 19], [37, 27, 26, 25], [38, 4, 27, 2, "accessibilityLabel"], [38, 22, 27, 20], [38, 25, 27, 23, "label"], [38, 30, 27, 28], [38, 34, 27, 32, "label"], [38, 39, 27, 37], [38, 44, 27, 42], [38, 50, 27, 48], [38, 53, 27, 51], [38, 56, 27, 54, "label"], [38, 61, 27, 59], [38, 69, 27, 67], [38, 72, 27, 70], [38, 81, 27, 79], [39, 4, 28, 2, "testID"], [39, 10, 28, 8], [40, 4, 29, 2, "style"], [40, 9, 29, 7], [41, 4, 30, 2, "href"], [42, 2, 31, 0], [42, 3, 31, 1], [42, 5, 31, 3], [43, 4, 32, 2], [43, 10, 32, 8], [44, 6, 33, 4, "colors"], [44, 12, 33, 10], [45, 6, 34, 4, "fonts"], [46, 4, 35, 2], [46, 5, 35, 3], [46, 8, 35, 6], [46, 12, 35, 6, "useTheme"], [46, 28, 35, 14], [46, 30, 35, 15], [46, 31, 35, 16], [47, 4, 36, 2], [47, 10, 36, 8], [48, 6, 37, 4, "direction"], [49, 4, 38, 2], [49, 5, 38, 3], [49, 8, 38, 6], [49, 12, 38, 6, "useLocale"], [49, 29, 38, 15], [49, 31, 38, 16], [49, 32, 38, 17], [50, 4, 39, 2], [50, 10, 39, 8], [50, 11, 39, 9, "labelWidth"], [50, 21, 39, 19], [50, 23, 39, 21, "<PERSON><PERSON><PERSON><PERSON>"], [50, 36, 39, 34], [50, 37, 39, 35], [50, 40, 39, 38, "React"], [50, 45, 39, 43], [50, 46, 39, 44, "useState"], [50, 54, 39, 52], [50, 55, 39, 53], [50, 59, 39, 57], [50, 60, 39, 58], [51, 4, 40, 2], [51, 10, 40, 8], [51, 11, 40, 9, "truncated<PERSON><PERSON><PERSON><PERSON>"], [51, 30, 40, 28], [51, 32, 40, 30, "setT<PERSON><PERSON><PERSON><PERSON><PERSON>"], [51, 54, 40, 52], [51, 55, 40, 53], [51, 58, 40, 56, "React"], [51, 63, 40, 61], [51, 64, 40, 62, "useState"], [51, 72, 40, 70], [51, 73, 40, 71], [51, 77, 40, 75], [51, 78, 40, 76], [52, 4, 41, 2], [52, 10, 41, 8, "renderBackImage"], [52, 25, 41, 23], [52, 28, 41, 26, "renderBackImage"], [52, 29, 41, 26], [52, 34, 41, 32], [53, 6, 42, 4], [53, 10, 42, 8, "backImage"], [53, 19, 42, 17], [53, 21, 42, 19], [54, 8, 43, 6], [54, 15, 43, 13, "backImage"], [54, 24, 43, 22], [54, 25, 43, 23], [55, 10, 44, 8, "tintColor"], [55, 19, 44, 17], [55, 21, 44, 19, "tintColor"], [55, 30, 44, 28], [55, 34, 44, 32, "colors"], [55, 40, 44, 38], [55, 41, 44, 39, "text"], [56, 8, 45, 6], [56, 9, 45, 7], [56, 10, 45, 8], [57, 6, 46, 4], [57, 7, 46, 5], [57, 13, 46, 11], [58, 8, 47, 6], [58, 15, 47, 13], [58, 28, 47, 26], [58, 32, 47, 26, "_jsx"], [58, 47, 47, 30], [58, 49, 47, 31, "HeaderIcon"], [58, 71, 47, 41], [58, 73, 47, 43], [59, 10, 48, 8, "source"], [59, 16, 48, 14], [59, 18, 48, 16, "backIcon"], [59, 35, 48, 24], [60, 10, 49, 8, "tintColor"], [60, 19, 49, 17], [60, 21, 49, 19, "tintColor"], [60, 30, 49, 28], [61, 10, 50, 8, "style"], [61, 15, 50, 13], [61, 17, 50, 15], [61, 18, 50, 16, "styles"], [61, 24, 50, 22], [61, 25, 50, 23, "icon"], [61, 29, 50, 27], [61, 31, 50, 29, "displayMode"], [61, 42, 50, 40], [61, 47, 50, 45], [61, 56, 50, 54], [61, 60, 50, 58, "styles"], [61, 66, 50, 64], [61, 67, 50, 65, "iconWithLabel"], [61, 80, 50, 78], [62, 8, 51, 6], [62, 9, 51, 7], [62, 10, 51, 8], [63, 6, 52, 4], [64, 4, 53, 2], [64, 5, 53, 3], [65, 4, 54, 2], [65, 10, 54, 8, "renderLabel"], [65, 21, 54, 19], [65, 24, 54, 22, "renderLabel"], [65, 25, 54, 22], [65, 30, 54, 28], [66, 6, 55, 4], [66, 10, 55, 8, "displayMode"], [66, 21, 55, 19], [66, 26, 55, 24], [66, 35, 55, 33], [66, 37, 55, 35], [67, 8, 56, 6], [67, 15, 56, 13], [67, 19, 56, 17], [68, 6, 57, 4], [69, 6, 58, 4], [69, 12, 58, 10, "availableSpace"], [69, 26, 58, 24], [69, 29, 58, 27, "titleLayout"], [69, 40, 58, 38], [69, 44, 58, 42, "screenLayout"], [69, 56, 58, 54], [69, 59, 58, 57], [69, 60, 58, 58, "screenLayout"], [69, 72, 58, 70], [69, 73, 58, 71, "width"], [69, 78, 58, 76], [69, 81, 58, 79, "titleLayout"], [69, 92, 58, 90], [69, 93, 58, 91, "width"], [69, 98, 58, 96], [69, 102, 58, 100], [69, 103, 58, 101], [69, 107, 58, 105, "ICON_WIDTH"], [69, 117, 58, 115], [69, 120, 58, 118, "ICON_MARGIN"], [69, 143, 58, 129], [69, 144, 58, 130], [69, 147, 58, 133], [69, 151, 58, 137], [70, 6, 59, 4], [70, 12, 59, 10, "potentialLabelText"], [70, 30, 59, 28], [70, 33, 59, 31, "displayMode"], [70, 44, 59, 42], [70, 49, 59, 47], [70, 58, 59, 56], [70, 61, 59, 59, "label"], [70, 66, 59, 64], [70, 69, 59, 67, "truncatedLabel"], [70, 83, 59, 81], [71, 6, 60, 4], [71, 12, 60, 10, "finalLabelText"], [71, 26, 60, 24], [71, 29, 60, 27, "availableSpace"], [71, 43, 60, 41], [71, 47, 60, 45, "labelWidth"], [71, 57, 60, 55], [71, 61, 60, 59, "truncated<PERSON><PERSON><PERSON><PERSON>"], [71, 80, 60, 78], [71, 83, 60, 81, "availableSpace"], [71, 97, 60, 95], [71, 100, 60, 98, "labelWidth"], [71, 110, 60, 108], [71, 113, 60, 111, "potentialLabelText"], [71, 131, 60, 129], [71, 134, 60, 132, "availableSpace"], [71, 148, 60, 146], [71, 151, 60, 149, "truncated<PERSON><PERSON><PERSON><PERSON>"], [71, 170, 60, 168], [71, 173, 60, 171, "truncatedLabel"], [71, 187, 60, 185], [71, 190, 60, 188], [71, 194, 60, 192], [71, 197, 60, 195, "potentialLabelText"], [71, 215, 60, 213], [72, 6, 61, 4], [72, 12, 61, 10, "commonStyle"], [72, 23, 61, 21], [72, 26, 61, 24], [72, 27, 61, 25, "fonts"], [72, 32, 61, 30], [72, 33, 61, 31, "regular"], [72, 40, 61, 38], [72, 42, 61, 40, "styles"], [72, 48, 61, 46], [72, 49, 61, 47, "label"], [72, 54, 61, 52], [72, 56, 61, 54, "labelStyle"], [72, 66, 61, 64], [72, 67, 61, 65], [73, 6, 62, 4], [73, 12, 62, 10, "hiddenStyle"], [73, 23, 62, 21], [73, 26, 62, 24], [73, 27, 62, 25, "commonStyle"], [73, 38, 62, 36], [73, 40, 62, 38], [74, 8, 63, 6, "position"], [74, 16, 63, 14], [74, 18, 63, 16], [74, 28, 63, 26], [75, 8, 64, 6, "top"], [75, 11, 64, 9], [75, 13, 64, 11], [75, 14, 64, 12], [76, 8, 65, 6, "left"], [76, 12, 65, 10], [76, 14, 65, 12], [76, 15, 65, 13], [77, 8, 66, 6, "opacity"], [77, 15, 66, 13], [77, 17, 66, 15], [78, 6, 67, 4], [78, 7, 67, 5], [78, 8, 67, 6], [79, 6, 68, 4], [79, 12, 68, 10, "labelElement"], [79, 24, 68, 22], [79, 27, 68, 25], [79, 40, 68, 38], [79, 44, 68, 38, "_jsxs"], [79, 60, 68, 43], [79, 62, 68, 44, "View"], [79, 75, 68, 48], [79, 77, 68, 50], [80, 8, 69, 6, "style"], [80, 13, 69, 11], [80, 15, 69, 13, "styles"], [80, 21, 69, 19], [80, 22, 69, 20, "labelWrapper"], [80, 34, 69, 32], [81, 8, 70, 6, "children"], [81, 16, 70, 14], [81, 18, 70, 16], [81, 19, 70, 17, "label"], [81, 24, 70, 22], [81, 28, 70, 26, "displayMode"], [81, 39, 70, 37], [81, 44, 70, 42], [81, 53, 70, 51], [81, 56, 70, 54], [81, 69, 70, 67], [81, 73, 70, 67, "_jsx"], [81, 88, 70, 71], [81, 90, 70, 72, "Animated"], [81, 107, 70, 80], [81, 108, 70, 81, "Text"], [81, 112, 70, 85], [81, 114, 70, 87], [82, 10, 71, 8, "style"], [82, 15, 71, 13], [82, 17, 71, 15, "hiddenStyle"], [82, 28, 71, 26], [83, 10, 72, 8, "numberOfLines"], [83, 23, 72, 21], [83, 25, 72, 23], [83, 26, 72, 24], [84, 10, 73, 8, "onLayout"], [84, 18, 73, 16], [84, 20, 73, 18, "e"], [84, 21, 73, 19], [84, 25, 73, 23, "<PERSON><PERSON><PERSON><PERSON>"], [84, 38, 73, 36], [84, 39, 73, 37, "e"], [84, 40, 73, 38], [84, 41, 73, 39, "nativeEvent"], [84, 52, 73, 50], [84, 53, 73, 51, "layout"], [84, 59, 73, 57], [84, 60, 73, 58, "width"], [84, 65, 73, 63], [84, 66, 73, 64], [85, 10, 74, 8, "children"], [85, 18, 74, 16], [85, 20, 74, 18, "label"], [86, 8, 75, 6], [86, 9, 75, 7], [86, 10, 75, 8], [86, 13, 75, 11], [86, 17, 75, 15], [86, 19, 75, 17, "truncatedLabel"], [86, 33, 75, 31], [86, 36, 75, 34], [86, 49, 75, 47], [86, 53, 75, 47, "_jsx"], [86, 68, 75, 51], [86, 70, 75, 52, "Animated"], [86, 87, 75, 60], [86, 88, 75, 61, "Text"], [86, 92, 75, 65], [86, 94, 75, 67], [87, 10, 76, 8, "style"], [87, 15, 76, 13], [87, 17, 76, 15, "hiddenStyle"], [87, 28, 76, 26], [88, 10, 77, 8, "numberOfLines"], [88, 23, 77, 21], [88, 25, 77, 23], [88, 26, 77, 24], [89, 10, 78, 8, "onLayout"], [89, 18, 78, 16], [89, 20, 78, 18, "e"], [89, 21, 78, 19], [89, 25, 78, 23, "setT<PERSON><PERSON><PERSON><PERSON><PERSON>"], [89, 47, 78, 45], [89, 48, 78, 46, "e"], [89, 49, 78, 47], [89, 50, 78, 48, "nativeEvent"], [89, 61, 78, 59], [89, 62, 78, 60, "layout"], [89, 68, 78, 66], [89, 69, 78, 67, "width"], [89, 74, 78, 72], [89, 75, 78, 73], [90, 10, 79, 8, "children"], [90, 18, 79, 16], [90, 20, 79, 18, "truncatedLabel"], [91, 8, 80, 6], [91, 9, 80, 7], [91, 10, 80, 8], [91, 13, 80, 11], [91, 17, 80, 15], [91, 19, 80, 17, "finalLabelText"], [91, 33, 80, 31], [91, 36, 80, 34], [91, 49, 80, 47], [91, 53, 80, 47, "_jsx"], [91, 68, 80, 51], [91, 70, 80, 52, "Animated"], [91, 87, 80, 60], [91, 88, 80, 61, "Text"], [91, 92, 80, 65], [91, 94, 80, 67], [92, 10, 81, 8, "accessible"], [92, 20, 81, 18], [92, 22, 81, 20], [92, 27, 81, 25], [93, 10, 82, 8, "onLayout"], [93, 18, 82, 16], [93, 20, 82, 18, "onLabelLayout"], [93, 33, 82, 31], [94, 10, 83, 8, "style"], [94, 15, 83, 13], [94, 17, 83, 15], [94, 18, 83, 16, "tintColor"], [94, 27, 83, 25], [94, 30, 83, 28], [95, 12, 84, 10, "color"], [95, 17, 84, 15], [95, 19, 84, 17, "tintColor"], [96, 10, 85, 8], [96, 11, 85, 9], [96, 14, 85, 12], [96, 18, 85, 16], [96, 20, 85, 18, "commonStyle"], [96, 31, 85, 29], [96, 32, 85, 30], [97, 10, 86, 8, "numberOfLines"], [97, 23, 86, 21], [97, 25, 86, 23], [97, 26, 86, 24], [98, 10, 87, 8, "allowFontScaling"], [98, 26, 87, 24], [98, 28, 87, 26], [98, 29, 87, 27], [98, 30, 87, 28, "allowFontScaling"], [98, 46, 87, 44], [99, 10, 88, 8, "children"], [99, 18, 88, 16], [99, 20, 88, 18, "finalLabelText"], [100, 8, 89, 6], [100, 9, 89, 7], [100, 10, 89, 8], [100, 13, 89, 11], [100, 17, 89, 15], [101, 6, 90, 4], [101, 7, 90, 5], [101, 8, 90, 6], [102, 6, 91, 4], [102, 10, 91, 8, "backImage"], [102, 19, 91, 17], [102, 23, 91, 21, "Platform"], [102, 40, 91, 29], [102, 41, 91, 30, "OS"], [102, 43, 91, 32], [102, 48, 91, 37], [102, 53, 91, 42], [102, 55, 91, 44], [103, 8, 92, 6], [104, 8, 93, 6], [105, 8, 94, 6], [105, 15, 94, 13, "labelElement"], [105, 27, 94, 25], [106, 6, 95, 4], [107, 6, 96, 4], [107, 13, 96, 11], [107, 26, 96, 24], [107, 30, 96, 24, "_jsx"], [107, 45, 96, 28], [107, 47, 96, 29, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [107, 69, 96, 39], [107, 71, 96, 41], [108, 8, 97, 6, "maskElement"], [108, 19, 97, 17], [108, 21, 97, 19], [108, 34, 97, 32], [108, 38, 97, 32, "_jsxs"], [108, 54, 97, 37], [108, 56, 97, 38, "View"], [108, 69, 97, 42], [108, 71, 97, 44], [109, 10, 98, 8, "style"], [109, 15, 98, 13], [109, 17, 98, 15], [109, 18, 98, 16, "styles"], [109, 24, 98, 22], [109, 25, 98, 23, "iconMaskContainer"], [109, 42, 98, 40], [110, 10, 99, 8], [111, 10, 100, 8, "screenLayout"], [111, 22, 100, 20], [111, 25, 100, 23], [112, 12, 101, 10, "min<PERSON><PERSON><PERSON>"], [112, 20, 101, 18], [112, 22, 101, 20, "screenLayout"], [112, 34, 101, 32], [112, 35, 101, 33, "width"], [112, 40, 101, 38], [112, 43, 101, 41], [112, 44, 101, 42], [112, 47, 101, 45], [113, 10, 102, 8], [113, 11, 102, 9], [113, 14, 102, 12], [113, 18, 102, 16], [113, 19, 102, 17], [114, 10, 103, 8, "children"], [114, 18, 103, 16], [114, 20, 103, 18], [114, 21, 103, 19], [114, 34, 103, 32], [114, 38, 103, 32, "_jsx"], [114, 53, 103, 36], [114, 55, 103, 37, "Image"], [114, 69, 103, 42], [114, 71, 103, 44], [115, 12, 104, 10, "source"], [115, 18, 104, 16], [115, 20, 104, 18, "backIconMask"], [115, 41, 104, 30], [116, 12, 105, 10, "resizeMode"], [116, 22, 105, 20], [116, 24, 105, 22], [116, 33, 105, 31], [117, 12, 106, 10, "style"], [117, 17, 106, 15], [117, 19, 106, 17], [117, 20, 106, 18, "styles"], [117, 26, 106, 24], [117, 27, 106, 25, "iconMask"], [117, 35, 106, 33], [117, 37, 106, 35, "direction"], [117, 46, 106, 44], [117, 51, 106, 49], [117, 56, 106, 54], [117, 60, 106, 58, "styles"], [117, 66, 106, 64], [117, 67, 106, 65, "flip"], [117, 71, 106, 69], [118, 10, 107, 8], [118, 11, 107, 9], [118, 12, 107, 10], [118, 14, 107, 12], [118, 27, 107, 25], [118, 31, 107, 25, "_jsx"], [118, 46, 107, 29], [118, 48, 107, 30, "View"], [118, 61, 107, 34], [118, 63, 107, 36], [119, 12, 108, 10, "style"], [119, 17, 108, 15], [119, 19, 108, 17, "styles"], [119, 25, 108, 23], [119, 26, 108, 24, "iconMaskFillerRect"], [120, 10, 109, 8], [120, 11, 109, 9], [120, 12, 109, 10], [121, 8, 110, 6], [121, 9, 110, 7], [121, 10, 110, 8], [122, 8, 111, 6, "children"], [122, 16, 111, 14], [122, 18, 111, 16, "labelElement"], [123, 6, 112, 4], [123, 7, 112, 5], [123, 8, 112, 6], [124, 4, 113, 2], [124, 5, 113, 3], [125, 4, 114, 2], [125, 10, 114, 8, "handlePress"], [125, 21, 114, 19], [125, 24, 114, 22, "handlePress"], [125, 25, 114, 22], [125, 30, 114, 28], [126, 6, 115, 4], [126, 10, 115, 8, "onPress"], [126, 17, 115, 15], [126, 19, 115, 17], [127, 8, 116, 6, "requestAnimationFrame"], [127, 29, 116, 27], [127, 30, 116, 28], [127, 36, 116, 34, "onPress"], [127, 43, 116, 41], [127, 44, 116, 42], [127, 45, 116, 43], [127, 46, 116, 44], [128, 6, 117, 4], [129, 4, 118, 2], [129, 5, 118, 3], [130, 4, 119, 2], [130, 11, 119, 9], [130, 24, 119, 22], [130, 28, 119, 22, "_jsx"], [130, 43, 119, 26], [130, 45, 119, 27, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [130, 71, 119, 39], [130, 73, 119, 41], [131, 6, 120, 4, "disabled"], [131, 14, 120, 12], [131, 16, 120, 14, "disabled"], [131, 24, 120, 22], [132, 6, 121, 4, "href"], [132, 10, 121, 8], [132, 12, 121, 10, "href"], [132, 16, 121, 14], [133, 6, 122, 4, "accessibilityLabel"], [133, 24, 122, 22], [133, 26, 122, 24, "accessibilityLabel"], [133, 44, 122, 42], [134, 6, 123, 4, "testID"], [134, 12, 123, 10], [134, 14, 123, 12, "testID"], [134, 20, 123, 18], [135, 6, 124, 4, "onPress"], [135, 13, 124, 11], [135, 15, 124, 13, "handlePress"], [135, 26, 124, 24], [136, 6, 125, 4, "pressColor"], [136, 16, 125, 14], [136, 18, 125, 16, "pressColor"], [136, 28, 125, 26], [137, 6, 126, 4, "pressOpacity"], [137, 18, 126, 16], [137, 20, 126, 18, "pressOpacity"], [137, 32, 126, 30], [138, 6, 127, 4, "style"], [138, 11, 127, 9], [138, 13, 127, 11], [138, 14, 127, 12, "styles"], [138, 20, 127, 18], [138, 21, 127, 19, "container"], [138, 30, 127, 28], [138, 32, 127, 30, "style"], [138, 37, 127, 35], [138, 38, 127, 36], [139, 6, 128, 4, "children"], [139, 14, 128, 12], [139, 16, 128, 14], [139, 29, 128, 27], [139, 33, 128, 27, "_jsxs"], [139, 49, 128, 32], [139, 51, 128, 33, "React"], [139, 56, 128, 38], [139, 57, 128, 39, "Fragment"], [139, 65, 128, 47], [139, 67, 128, 49], [140, 8, 129, 6, "children"], [140, 16, 129, 14], [140, 18, 129, 16], [140, 19, 129, 17, "renderBackImage"], [140, 34, 129, 32], [140, 35, 129, 33], [140, 36, 129, 34], [140, 38, 129, 36, "renderLabel"], [140, 49, 129, 47], [140, 50, 129, 48], [140, 51, 129, 49], [141, 6, 130, 4], [141, 7, 130, 5], [142, 4, 131, 2], [142, 5, 131, 3], [142, 6, 131, 4], [143, 2, 132, 0], [144, 2, 133, 0], [144, 8, 133, 6, "ICON_WIDTH"], [144, 18, 133, 16], [144, 21, 133, 19, "Platform"], [144, 38, 133, 27], [144, 39, 133, 28, "OS"], [144, 41, 133, 30], [144, 46, 133, 35], [144, 51, 133, 40], [144, 54, 133, 43], [144, 56, 133, 45], [144, 59, 133, 48], [144, 61, 133, 50], [145, 2, 134, 0], [145, 8, 134, 6, "ICON_MARGIN_END"], [145, 23, 134, 21], [145, 26, 134, 24, "Platform"], [145, 43, 134, 32], [145, 44, 134, 33, "OS"], [145, 46, 134, 35], [145, 51, 134, 40], [145, 56, 134, 45], [145, 59, 134, 48], [145, 61, 134, 50], [145, 64, 134, 53], [145, 65, 134, 54], [146, 2, 135, 0], [146, 8, 135, 6, "styles"], [146, 14, 135, 12], [146, 17, 135, 15, "StyleSheet"], [146, 36, 135, 25], [146, 37, 135, 26, "create"], [146, 43, 135, 32], [146, 44, 135, 33], [147, 4, 136, 2, "container"], [147, 13, 136, 11], [147, 15, 136, 13], [148, 6, 137, 4, "paddingHorizontal"], [148, 23, 137, 21], [148, 25, 137, 23], [148, 26, 137, 24], [149, 6, 138, 4, "min<PERSON><PERSON><PERSON>"], [149, 14, 138, 12], [149, 16, 138, 14, "StyleSheet"], [149, 35, 138, 24], [149, 36, 138, 25, "hairlineWidth"], [149, 49, 138, 38], [150, 6, 139, 4], [151, 6, 140, 4], [151, 9, 140, 7, "Platform"], [151, 26, 140, 15], [151, 27, 140, 16, "select"], [151, 33, 140, 22], [151, 34, 140, 23], [152, 8, 141, 6, "ios"], [152, 11, 141, 9], [152, 13, 141, 11], [152, 17, 141, 15], [153, 8, 142, 6, "default"], [153, 15, 142, 13], [153, 17, 142, 15], [154, 10, 143, 8, "marginVertical"], [154, 24, 143, 22], [154, 26, 143, 24], [154, 27, 143, 25], [155, 10, 144, 8, "marginHorizontal"], [155, 26, 144, 24], [155, 28, 144, 26], [156, 8, 145, 6], [157, 6, 146, 4], [157, 7, 146, 5], [158, 4, 147, 2], [158, 5, 147, 3], [159, 4, 148, 2, "label"], [159, 9, 148, 7], [159, 11, 148, 9], [160, 6, 149, 4, "fontSize"], [160, 14, 149, 12], [160, 16, 149, 14], [160, 18, 149, 16], [161, 6, 150, 4], [162, 6, 151, 4], [163, 6, 152, 4, "letterSpacing"], [163, 19, 152, 17], [163, 21, 152, 19], [164, 4, 153, 2], [164, 5, 153, 3], [165, 4, 154, 2, "labelWrapper"], [165, 16, 154, 14], [165, 18, 154, 16], [166, 6, 155, 4], [167, 6, 156, 4], [168, 6, 157, 4, "flexDirection"], [168, 19, 157, 17], [168, 21, 157, 19], [168, 26, 157, 24], [169, 6, 158, 4, "alignItems"], [169, 16, 158, 14], [169, 18, 158, 16], [169, 30, 158, 28], [170, 6, 159, 4, "marginEnd"], [170, 15, 159, 13], [170, 17, 159, 15, "ICON_MARGIN"], [171, 4, 160, 2], [171, 5, 160, 3], [172, 4, 161, 2, "icon"], [172, 8, 161, 6], [172, 10, 161, 8], [173, 6, 162, 4, "width"], [173, 11, 162, 9], [173, 13, 162, 11, "ICON_WIDTH"], [173, 23, 162, 21], [174, 6, 163, 4, "marginEnd"], [174, 15, 163, 13], [174, 17, 163, 15, "ICON_MARGIN_END"], [175, 4, 164, 2], [175, 5, 164, 3], [176, 4, 165, 2, "iconWithLabel"], [176, 17, 165, 15], [176, 19, 165, 17, "Platform"], [176, 36, 165, 25], [176, 37, 165, 26, "OS"], [176, 39, 165, 28], [176, 44, 165, 33], [176, 49, 165, 38], [176, 52, 165, 41], [177, 6, 166, 4, "marginEnd"], [177, 15, 166, 13], [177, 17, 166, 15], [178, 4, 167, 2], [178, 5, 167, 3], [178, 8, 167, 6], [178, 9, 167, 7], [178, 10, 167, 8], [179, 4, 168, 2, "iconMaskContainer"], [179, 21, 168, 19], [179, 23, 168, 21], [180, 6, 169, 4, "flex"], [180, 10, 169, 8], [180, 12, 169, 10], [180, 13, 169, 11], [181, 6, 170, 4, "flexDirection"], [181, 19, 170, 17], [181, 21, 170, 19], [181, 26, 170, 24], [182, 6, 171, 4, "justifyContent"], [182, 20, 171, 18], [182, 22, 171, 20], [183, 4, 172, 2], [183, 5, 172, 3], [184, 4, 173, 2, "iconMaskFillerRect"], [184, 22, 173, 20], [184, 24, 173, 22], [185, 6, 174, 4, "flex"], [185, 10, 174, 8], [185, 12, 174, 10], [185, 13, 174, 11], [186, 6, 175, 4, "backgroundColor"], [186, 21, 175, 19], [186, 23, 175, 21], [187, 4, 176, 2], [187, 5, 176, 3], [188, 4, 177, 2, "iconMask"], [188, 12, 177, 10], [188, 14, 177, 12], [189, 6, 178, 4, "height"], [189, 12, 178, 10], [189, 14, 178, 12], [189, 16, 178, 14], [190, 6, 179, 4, "width"], [190, 11, 179, 9], [190, 13, 179, 11], [190, 15, 179, 13], [191, 6, 180, 4, "marginStart"], [191, 17, 180, 15], [191, 19, 180, 17], [191, 20, 180, 18], [191, 24, 180, 22], [192, 6, 181, 4, "marginVertical"], [192, 20, 181, 18], [192, 22, 181, 20], [192, 24, 181, 22], [193, 6, 182, 4, "alignSelf"], [193, 15, 182, 13], [193, 17, 182, 15], [194, 4, 183, 2], [194, 5, 183, 3], [195, 4, 184, 2, "flip"], [195, 8, 184, 6], [195, 10, 184, 8], [196, 6, 185, 4, "transform"], [196, 15, 185, 13], [196, 17, 185, 15], [197, 4, 186, 2], [198, 2, 187, 0], [198, 3, 187, 1], [198, 4, 187, 2], [199, 0, 187, 3], [199, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderBackImage", "renderLabel", "_jsx$argument_1.onLayout", "handlePress", "requestAnimationFrame$argument_0"], "mappings": "AAA;OCW;0BC6B;GDY;sBEC;kBCmB,8CD;kBCK,uDD;GFmC;sBIC;4BCE,eD;GJE;CDc"}}, "type": "js/module"}]}