{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.childDefaultAttributes = void 0;\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const defaultAttributes = exports.default = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  };\n  const childDefaultAttributes = exports.childDefaultAttributes = {\n    fill: defaultAttributes.fill,\n    stroke: defaultAttributes.stroke,\n    strokeWidth: defaultAttributes.strokeWidth,\n    strokeLinecap: defaultAttributes.strokeLinecap,\n    strokeLinejoin: defaultAttributes.strokeLinejoin\n  };\n});", "lineCount": 31, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [13, 2, 8, 0], [13, 8, 8, 6, "defaultAttributes"], [13, 25, 8, 23], [13, 28, 8, 23, "exports"], [13, 35, 8, 23], [13, 36, 8, 23, "default"], [13, 43, 8, 23], [13, 46, 8, 26], [14, 4, 9, 2, "xmlns"], [14, 9, 9, 7], [14, 11, 9, 9], [14, 39, 9, 37], [15, 4, 10, 2, "width"], [15, 9, 10, 7], [15, 11, 10, 9], [15, 13, 10, 11], [16, 4, 11, 2, "height"], [16, 10, 11, 8], [16, 12, 11, 10], [16, 14, 11, 12], [17, 4, 12, 2, "viewBox"], [17, 11, 12, 9], [17, 13, 12, 11], [17, 24, 12, 22], [18, 4, 13, 2, "fill"], [18, 8, 13, 6], [18, 10, 13, 8], [18, 16, 13, 14], [19, 4, 14, 2, "stroke"], [19, 10, 14, 8], [19, 12, 14, 10], [19, 26, 14, 24], [20, 4, 15, 2, "strokeWidth"], [20, 15, 15, 13], [20, 17, 15, 15], [20, 18, 15, 16], [21, 4, 16, 2, "strokeLinecap"], [21, 17, 16, 15], [21, 19, 16, 17], [21, 26, 16, 24], [22, 4, 17, 2, "strokeLinejoin"], [22, 18, 17, 16], [22, 20, 17, 18], [23, 2, 18, 0], [23, 3, 18, 1], [24, 2, 19, 0], [24, 8, 19, 6, "childDefaultAttributes"], [24, 30, 19, 28], [24, 33, 19, 28, "exports"], [24, 40, 19, 28], [24, 41, 19, 28, "childDefaultAttributes"], [24, 63, 19, 28], [24, 66, 19, 31], [25, 4, 20, 2, "fill"], [25, 8, 20, 6], [25, 10, 20, 8, "defaultAttributes"], [25, 27, 20, 25], [25, 28, 20, 26, "fill"], [25, 32, 20, 30], [26, 4, 21, 2, "stroke"], [26, 10, 21, 8], [26, 12, 21, 10, "defaultAttributes"], [26, 29, 21, 27], [26, 30, 21, 28, "stroke"], [26, 36, 21, 34], [27, 4, 22, 2, "strokeWidth"], [27, 15, 22, 13], [27, 17, 22, 15, "defaultAttributes"], [27, 34, 22, 32], [27, 35, 22, 33, "strokeWidth"], [27, 46, 22, 44], [28, 4, 23, 2, "strokeLinecap"], [28, 17, 23, 15], [28, 19, 23, 17, "defaultAttributes"], [28, 36, 23, 34], [28, 37, 23, 35, "strokeLinecap"], [28, 50, 23, 48], [29, 4, 24, 2, "strokeLinejoin"], [29, 18, 24, 16], [29, 20, 24, 18, "defaultAttributes"], [29, 37, 24, 35], [29, 38, 24, 36, "strokeLinejoin"], [30, 2, 25, 0], [30, 3, 25, 1], [31, 0, 25, 2], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}