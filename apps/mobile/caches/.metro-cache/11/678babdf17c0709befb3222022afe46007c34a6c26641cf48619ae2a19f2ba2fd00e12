{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var ClipboardType = exports.default = (0, _createLucideIcon.default)(\"ClipboardType\", [[\"rect\", {\n    width: \"8\",\n    height: \"4\",\n    x: \"8\",\n    y: \"2\",\n    rx: \"1\",\n    ry: \"1\",\n    key: \"tgr4d6\"\n  }], [\"path\", {\n    d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\",\n    key: \"116196\"\n  }], [\"path\", {\n    d: \"M9 12v-1h6v1\",\n    key: \"iehl6m\"\n  }], [\"path\", {\n    d: \"M11 17h2\",\n    key: \"12w5me\"\n  }], [\"path\", {\n    d: \"M12 11v6\",\n    key: \"1bwqyc\"\n  }]]);\n});", "lineCount": 36, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "ClipboardType"], [15, 19, 10, 19], [15, 22, 10, 19, "exports"], [15, 29, 10, 19], [15, 30, 10, 19, "default"], [15, 37, 10, 19], [15, 40, 10, 22], [15, 44, 10, 22, "createLucideIcon"], [15, 69, 10, 38], [15, 71, 10, 39], [15, 86, 10, 54], [15, 88, 10, 56], [15, 89, 11, 2], [15, 90, 11, 3], [15, 96, 11, 9], [15, 98, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 14, 11, 23], [17, 4, 11, 25, "height"], [17, 10, 11, 31], [17, 12, 11, 33], [17, 15, 11, 36], [18, 4, 11, 38, "x"], [18, 5, 11, 39], [18, 7, 11, 41], [18, 10, 11, 44], [19, 4, 11, 46, "y"], [19, 5, 11, 47], [19, 7, 11, 49], [19, 10, 11, 52], [20, 4, 11, 54, "rx"], [20, 6, 11, 56], [20, 8, 11, 58], [20, 11, 11, 61], [21, 4, 11, 63, "ry"], [21, 6, 11, 65], [21, 8, 11, 67], [21, 11, 11, 70], [22, 4, 11, 72, "key"], [22, 7, 11, 75], [22, 9, 11, 77], [23, 2, 11, 86], [23, 3, 11, 87], [23, 4, 11, 88], [23, 6, 12, 2], [23, 7, 13, 4], [23, 13, 13, 10], [23, 15, 14, 4], [24, 4, 15, 6, "d"], [24, 5, 15, 7], [24, 7, 15, 9], [24, 81, 15, 83], [25, 4, 16, 6, "key"], [25, 7, 16, 9], [25, 9, 16, 11], [26, 2, 17, 4], [26, 3, 17, 5], [26, 4, 18, 3], [26, 6, 19, 2], [26, 7, 19, 3], [26, 13, 19, 9], [26, 15, 19, 11], [27, 4, 19, 13, "d"], [27, 5, 19, 14], [27, 7, 19, 16], [27, 21, 19, 30], [28, 4, 19, 32, "key"], [28, 7, 19, 35], [28, 9, 19, 37], [29, 2, 19, 46], [29, 3, 19, 47], [29, 4, 19, 48], [29, 6, 20, 2], [29, 7, 20, 3], [29, 13, 20, 9], [29, 15, 20, 11], [30, 4, 20, 13, "d"], [30, 5, 20, 14], [30, 7, 20, 16], [30, 17, 20, 26], [31, 4, 20, 28, "key"], [31, 7, 20, 31], [31, 9, 20, 33], [32, 2, 20, 42], [32, 3, 20, 43], [32, 4, 20, 44], [32, 6, 21, 2], [32, 7, 21, 3], [32, 13, 21, 9], [32, 15, 21, 11], [33, 4, 21, 13, "d"], [33, 5, 21, 14], [33, 7, 21, 16], [33, 17, 21, 26], [34, 4, 21, 28, "key"], [34, 7, 21, 31], [34, 9, 21, 33], [35, 2, 21, 42], [35, 3, 21, 43], [35, 4, 21, 44], [35, 5, 22, 1], [35, 6, 22, 2], [36, 0, 22, 3], [36, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}