{"dependencies": [{"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 56, "index": 71}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rubberBandDecay = void 0;\n  var _utils = require(_dependencyMap[0], \"./utils.js\");\n  const DERIVATIVE_EPS = 0.1;\n  const _worklet_10345176740994_init_data = {\n    code: \"function rubberBandDecay_reactNativeReanimated_rubberBandDecayJs1(animation,now,config){const{SLOPE_FACTOR,DERIVATIVE_EPS,VELOCITY_EPS}=this.__closure;const{lastTimestamp:lastTimestamp,startTimestamp:startTimestamp,current:current,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);const clampIndex=Math.abs(current-config.clamp[0])<Math.abs(current-config.clamp[1])?0:1;let derivative=0;if(current<config.clamp[0]||current>config.clamp[1]){derivative=current-config.clamp[clampIndex];}const v=velocity*Math.exp(-(1-config.deceleration)*(now-startTimestamp)*SLOPE_FACTOR)-derivative*config.rubberBandFactor;if(Math.abs(derivative)>DERIVATIVE_EPS){animation.springActive=true;}else if(animation.springActive){animation.current=config.clamp[clampIndex];return true;}else if(Math.abs(v)<VELOCITY_EPS){return true;}animation.current=current+v*config.velocityFactor*deltaTime/1000;animation.velocity=v;animation.lastTimestamp=now;return false;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/rubberBandDecay.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"rubberBandDecay_reactNativeReanimated_rubberBandDecayJs1\\\",\\\"animation\\\",\\\"now\\\",\\\"config\\\",\\\"SLOPE_FACTOR\\\",\\\"DERIVATIVE_EPS\\\",\\\"VELOCITY_EPS\\\",\\\"__closure\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"current\\\",\\\"velocity\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"clampIndex\\\",\\\"abs\\\",\\\"clamp\\\",\\\"derivative\\\",\\\"v\\\",\\\"exp\\\",\\\"deceleration\\\",\\\"rubberBandFactor\\\",\\\"springActive\\\",\\\"velocityFactor\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/rubberBandDecay.js\\\"],\\\"mappings\\\":\\\"AAIO,SAAAA,wDAAiDA,CAAAC,SAAA,CAAAC,GAAA,CAAAC,MAAA,QAAAC,YAAA,CAAAC,cAAA,CAAAC,YAAA,OAAAC,SAAA,CAGtD,KAAM,CACJC,aAAa,CAAbA,aAAa,CACbC,cAAc,CAAdA,cAAc,CACdC,OAAO,CAAPA,OAAO,CACPC,QAAA,CAAAA,QACF,CAAC,CAAGV,SAAS,CACb,KAAM,CAAAW,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACZ,GAAG,CAAGM,aAAa,CAAE,EAAE,CAAC,CACnD,KAAM,CAAAO,UAAU,CAAGF,IAAI,CAACG,GAAG,CAACN,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAGJ,IAAI,CAACG,GAAG,CAACN,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACpG,GAAI,CAAAC,UAAU,CAAG,CAAC,CAClB,GAAIR,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,EAAIP,OAAO,CAAGP,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAE,CAC1DC,UAAU,CAAGR,OAAO,CAAGP,MAAM,CAACc,KAAK,CAACF,UAAU,CAAC,CACjD,CACA,KAAM,CAAAI,CAAC,CAAGR,QAAQ,CAAGE,IAAI,CAACO,GAAG,CAAC,EAAE,CAAC,CAAGjB,MAAM,CAACkB,YAAY,CAAC,EAAInB,GAAG,CAAGO,cAAc,CAAC,CAAGL,YAAY,CAAC,CAAGc,UAAU,CAAGf,MAAM,CAACmB,gBAAgB,CACxI,GAAIT,IAAI,CAACG,GAAG,CAACE,UAAU,CAAC,CAAGb,cAAc,CAAE,CACzCJ,SAAS,CAACsB,YAAY,CAAG,IAAI,CAC/B,CAAC,IAAM,IAAItB,SAAS,CAACsB,YAAY,CAAE,CACjCtB,SAAS,CAACS,OAAO,CAAGP,MAAM,CAACc,KAAK,CAACF,UAAU,CAAC,CAC5C,MAAO,KAAI,CACb,CAAC,IAAM,IAAIF,IAAI,CAACG,GAAG,CAACG,CAAC,CAAC,CAAGb,YAAY,CAAE,CACrC,MAAO,KAAI,CACb,CACAL,SAAS,CAACS,OAAO,CAAGA,OAAO,CAAGS,CAAC,CAAGhB,MAAM,CAACqB,cAAc,CAAGZ,SAAS,CAAG,IAAI,CAC1EX,SAAS,CAACU,QAAQ,CAAGQ,CAAC,CACtBlB,SAAS,CAACO,aAAa,CAAGN,GAAG,CAC7B,MAAO,MAAK,CACd\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rubberBandDecay = exports.rubberBandDecay = function () {\n    const _e = [new global.Error(), -4, -27];\n    const rubberBandDecay = function (animation, now, config) {\n      const {\n        lastTimestamp,\n        startTimestamp,\n        current,\n        velocity\n      } = animation;\n      const deltaTime = Math.min(now - lastTimestamp, 64);\n      const clampIndex = Math.abs(current - config.clamp[0]) < Math.abs(current - config.clamp[1]) ? 0 : 1;\n      let derivative = 0;\n      if (current < config.clamp[0] || current > config.clamp[1]) {\n        derivative = current - config.clamp[clampIndex];\n      }\n      const v = velocity * Math.exp(-(1 - config.deceleration) * (now - startTimestamp) * _utils.SLOPE_FACTOR) - derivative * config.rubberBandFactor;\n      if (Math.abs(derivative) > DERIVATIVE_EPS) {\n        animation.springActive = true;\n      } else if (animation.springActive) {\n        animation.current = config.clamp[clampIndex];\n        return true;\n      } else if (Math.abs(v) < _utils.VELOCITY_EPS) {\n        return true;\n      }\n      animation.current = current + v * config.velocityFactor * deltaTime / 1000;\n      animation.velocity = v;\n      animation.lastTimestamp = now;\n      return false;\n    };\n    rubberBandDecay.__closure = {\n      SLOPE_FACTOR: _utils.SLOPE_FACTOR,\n      DERIVATIVE_EPS,\n      VELOCITY_EPS: _utils.VELOCITY_EPS\n    };\n    rubberBandDecay.__workletHash = 10345176740994;\n    rubberBandDecay.__initData = _worklet_10345176740994_init_data;\n    rubberBandDecay.__stackDetails = _e;\n    return rubberBandDecay;\n  }();\n});", "lineCount": 55, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "rubberBandDecay"], [7, 25, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_utils"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "DERIVATIVE_EPS"], [9, 22, 4, 20], [9, 25, 4, 23], [9, 28, 4, 26], [10, 2, 4, 27], [10, 8, 4, 27, "_worklet_10345176740994_init_data"], [10, 41, 4, 27], [11, 4, 4, 27, "code"], [11, 8, 4, 27], [12, 4, 4, 27, "location"], [12, 12, 4, 27], [13, 4, 4, 27, "sourceMap"], [13, 13, 4, 27], [14, 4, 4, 27, "version"], [14, 11, 4, 27], [15, 2, 4, 27], [16, 2, 4, 27], [16, 8, 4, 27, "rubberBandDecay"], [16, 23, 4, 27], [16, 26, 4, 27, "exports"], [16, 33, 4, 27], [16, 34, 4, 27, "rubberBandDecay"], [16, 49, 4, 27], [16, 52, 5, 7], [17, 4, 5, 7], [17, 10, 5, 7, "_e"], [17, 12, 5, 7], [17, 20, 5, 7, "global"], [17, 26, 5, 7], [17, 27, 5, 7, "Error"], [17, 32, 5, 7], [18, 4, 5, 7], [18, 10, 5, 7, "rubberBandDecay"], [18, 25, 5, 7], [18, 37, 5, 7, "rubberBandDecay"], [18, 38, 5, 32, "animation"], [18, 47, 5, 41], [18, 49, 5, 43, "now"], [18, 52, 5, 46], [18, 54, 5, 48, "config"], [18, 60, 5, 54], [18, 62, 5, 56], [19, 6, 8, 2], [19, 12, 8, 8], [20, 8, 9, 4, "lastTimestamp"], [20, 21, 9, 17], [21, 8, 10, 4, "startTimestamp"], [21, 22, 10, 18], [22, 8, 11, 4, "current"], [22, 15, 11, 11], [23, 8, 12, 4, "velocity"], [24, 6, 13, 2], [24, 7, 13, 3], [24, 10, 13, 6, "animation"], [24, 19, 13, 15], [25, 6, 14, 2], [25, 12, 14, 8, "deltaTime"], [25, 21, 14, 17], [25, 24, 14, 20, "Math"], [25, 28, 14, 24], [25, 29, 14, 25, "min"], [25, 32, 14, 28], [25, 33, 14, 29, "now"], [25, 36, 14, 32], [25, 39, 14, 35, "lastTimestamp"], [25, 52, 14, 48], [25, 54, 14, 50], [25, 56, 14, 52], [25, 57, 14, 53], [26, 6, 15, 2], [26, 12, 15, 8, "clampIndex"], [26, 22, 15, 18], [26, 25, 15, 21, "Math"], [26, 29, 15, 25], [26, 30, 15, 26, "abs"], [26, 33, 15, 29], [26, 34, 15, 30, "current"], [26, 41, 15, 37], [26, 44, 15, 40, "config"], [26, 50, 15, 46], [26, 51, 15, 47, "clamp"], [26, 56, 15, 52], [26, 57, 15, 53], [26, 58, 15, 54], [26, 59, 15, 55], [26, 60, 15, 56], [26, 63, 15, 59, "Math"], [26, 67, 15, 63], [26, 68, 15, 64, "abs"], [26, 71, 15, 67], [26, 72, 15, 68, "current"], [26, 79, 15, 75], [26, 82, 15, 78, "config"], [26, 88, 15, 84], [26, 89, 15, 85, "clamp"], [26, 94, 15, 90], [26, 95, 15, 91], [26, 96, 15, 92], [26, 97, 15, 93], [26, 98, 15, 94], [26, 101, 15, 97], [26, 102, 15, 98], [26, 105, 15, 101], [26, 106, 15, 102], [27, 6, 16, 2], [27, 10, 16, 6, "derivative"], [27, 20, 16, 16], [27, 23, 16, 19], [27, 24, 16, 20], [28, 6, 17, 2], [28, 10, 17, 6, "current"], [28, 17, 17, 13], [28, 20, 17, 16, "config"], [28, 26, 17, 22], [28, 27, 17, 23, "clamp"], [28, 32, 17, 28], [28, 33, 17, 29], [28, 34, 17, 30], [28, 35, 17, 31], [28, 39, 17, 35, "current"], [28, 46, 17, 42], [28, 49, 17, 45, "config"], [28, 55, 17, 51], [28, 56, 17, 52, "clamp"], [28, 61, 17, 57], [28, 62, 17, 58], [28, 63, 17, 59], [28, 64, 17, 60], [28, 66, 17, 62], [29, 8, 18, 4, "derivative"], [29, 18, 18, 14], [29, 21, 18, 17, "current"], [29, 28, 18, 24], [29, 31, 18, 27, "config"], [29, 37, 18, 33], [29, 38, 18, 34, "clamp"], [29, 43, 18, 39], [29, 44, 18, 40, "clampIndex"], [29, 54, 18, 50], [29, 55, 18, 51], [30, 6, 19, 2], [31, 6, 20, 2], [31, 12, 20, 8, "v"], [31, 13, 20, 9], [31, 16, 20, 12, "velocity"], [31, 24, 20, 20], [31, 27, 20, 23, "Math"], [31, 31, 20, 27], [31, 32, 20, 28, "exp"], [31, 35, 20, 31], [31, 36, 20, 32], [31, 38, 20, 34], [31, 39, 20, 35], [31, 42, 20, 38, "config"], [31, 48, 20, 44], [31, 49, 20, 45, "deceleration"], [31, 61, 20, 57], [31, 62, 20, 58], [31, 66, 20, 62, "now"], [31, 69, 20, 65], [31, 72, 20, 68, "startTimestamp"], [31, 86, 20, 82], [31, 87, 20, 83], [31, 90, 20, 86, "SLOPE_FACTOR"], [31, 109, 20, 98], [31, 110, 20, 99], [31, 113, 20, 102, "derivative"], [31, 123, 20, 112], [31, 126, 20, 115, "config"], [31, 132, 20, 121], [31, 133, 20, 122, "rubberBandFactor"], [31, 149, 20, 138], [32, 6, 21, 2], [32, 10, 21, 6, "Math"], [32, 14, 21, 10], [32, 15, 21, 11, "abs"], [32, 18, 21, 14], [32, 19, 21, 15, "derivative"], [32, 29, 21, 25], [32, 30, 21, 26], [32, 33, 21, 29, "DERIVATIVE_EPS"], [32, 47, 21, 43], [32, 49, 21, 45], [33, 8, 22, 4, "animation"], [33, 17, 22, 13], [33, 18, 22, 14, "springActive"], [33, 30, 22, 26], [33, 33, 22, 29], [33, 37, 22, 33], [34, 6, 23, 2], [34, 7, 23, 3], [34, 13, 23, 9], [34, 17, 23, 13, "animation"], [34, 26, 23, 22], [34, 27, 23, 23, "springActive"], [34, 39, 23, 35], [34, 41, 23, 37], [35, 8, 24, 4, "animation"], [35, 17, 24, 13], [35, 18, 24, 14, "current"], [35, 25, 24, 21], [35, 28, 24, 24, "config"], [35, 34, 24, 30], [35, 35, 24, 31, "clamp"], [35, 40, 24, 36], [35, 41, 24, 37, "clampIndex"], [35, 51, 24, 47], [35, 52, 24, 48], [36, 8, 25, 4], [36, 15, 25, 11], [36, 19, 25, 15], [37, 6, 26, 2], [37, 7, 26, 3], [37, 13, 26, 9], [37, 17, 26, 13, "Math"], [37, 21, 26, 17], [37, 22, 26, 18, "abs"], [37, 25, 26, 21], [37, 26, 26, 22, "v"], [37, 27, 26, 23], [37, 28, 26, 24], [37, 31, 26, 27, "VELOCITY_EPS"], [37, 50, 26, 39], [37, 52, 26, 41], [38, 8, 27, 4], [38, 15, 27, 11], [38, 19, 27, 15], [39, 6, 28, 2], [40, 6, 29, 2, "animation"], [40, 15, 29, 11], [40, 16, 29, 12, "current"], [40, 23, 29, 19], [40, 26, 29, 22, "current"], [40, 33, 29, 29], [40, 36, 29, 32, "v"], [40, 37, 29, 33], [40, 40, 29, 36, "config"], [40, 46, 29, 42], [40, 47, 29, 43, "velocityFactor"], [40, 61, 29, 57], [40, 64, 29, 60, "deltaTime"], [40, 73, 29, 69], [40, 76, 29, 72], [40, 80, 29, 76], [41, 6, 30, 2, "animation"], [41, 15, 30, 11], [41, 16, 30, 12, "velocity"], [41, 24, 30, 20], [41, 27, 30, 23, "v"], [41, 28, 30, 24], [42, 6, 31, 2, "animation"], [42, 15, 31, 11], [42, 16, 31, 12, "lastTimestamp"], [42, 29, 31, 25], [42, 32, 31, 28, "now"], [42, 35, 31, 31], [43, 6, 32, 2], [43, 13, 32, 9], [43, 18, 32, 14], [44, 4, 33, 0], [44, 5, 33, 1], [45, 4, 33, 1, "rubberBandDecay"], [45, 19, 33, 1], [45, 20, 33, 1, "__closure"], [45, 29, 33, 1], [46, 6, 33, 1, "SLOPE_FACTOR"], [46, 18, 33, 1], [46, 20, 20, 86, "SLOPE_FACTOR"], [46, 39, 20, 98], [47, 6, 20, 98, "DERIVATIVE_EPS"], [47, 20, 20, 98], [48, 6, 20, 98, "VELOCITY_EPS"], [48, 18, 20, 98], [48, 20, 26, 27, "VELOCITY_EPS"], [49, 4, 26, 39], [50, 4, 26, 39, "rubberBandDecay"], [50, 19, 26, 39], [50, 20, 26, 39, "__workletHash"], [50, 33, 26, 39], [51, 4, 26, 39, "rubberBandDecay"], [51, 19, 26, 39], [51, 20, 26, 39, "__initData"], [51, 30, 26, 39], [51, 33, 26, 39, "_worklet_10345176740994_init_data"], [51, 66, 26, 39], [52, 4, 26, 39, "rubberBandDecay"], [52, 19, 26, 39], [52, 20, 26, 39, "__stackDetails"], [52, 34, 26, 39], [52, 37, 26, 39, "_e"], [52, 39, 26, 39], [53, 4, 26, 39], [53, 11, 26, 39, "rubberBandDecay"], [53, 26, 26, 39], [54, 2, 26, 39], [54, 3, 5, 7], [55, 0, 5, 7], [55, 3]], "functionMap": {"names": ["<global>", "rubberBandDecay"], "mappings": "AAA;OCI;CD4B"}}, "type": "js/module"}]}