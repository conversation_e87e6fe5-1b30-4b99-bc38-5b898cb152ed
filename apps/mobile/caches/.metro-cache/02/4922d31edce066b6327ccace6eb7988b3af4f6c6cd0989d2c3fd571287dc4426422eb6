{"dependencies": [{"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./matchers", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 19, "index": 256}, "end": {"line": 7, "column": 40, "index": 277}}], "key": "89ylKT57ef0l7ma8+p1HhPaMj94=", "exportNames": ["*"]}}, {"name": "./utils/url", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 14, "index": 293}, "end": {"line": 8, "column": 36, "index": 315}}], "key": "KwepoOiDJIvcZxDqcbtt8RBrwgA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _slicedToArray = require(_dependencyMap[0], \"@babel/runtime/helpers/slicedToArray\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getRoutes = getRoutes;\n  exports.getIgnoreList = getIgnoreList;\n  exports.extrapolateGroups = extrapolateGroups;\n  exports.generateDynamic = generateDynamic;\n  var matchers_1 = require(_dependencyMap[1], \"./matchers\");\n  var url_1 = require(_dependencyMap[2], \"./utils/url\");\n  var validPlatforms = new Set(['android', 'ios', 'native', 'web']);\n  /**\n   * Given a Metro context module, return an array of nested routes.\n   *\n   * This is a two step process:\n   *  1. Convert the RequireContext keys (file paths) into a directory tree.\n   *      - This should extrapolate array syntax into multiple routes\n   *      - Routes are given a specificity score\n   *  2. Flatten the directory tree into routes\n   *      - Routes in directories without _layout files are hoisted to the nearest _layout\n   *      - The name of the route is relative to the nearest _layout\n   *      - If multiple routes have the same name, the most specific route is used\n   */\n  function getRoutes(contextModule, options) {\n    var directoryTree = getDirectoryTree(contextModule, options);\n    // If there are no routes\n    if (!directoryTree) {\n      return null;\n    }\n    var rootNode = flattenDirectoryTreeToRoutes(directoryTree, options);\n    if (!options.ignoreEntryPoints) {\n      crawlAndAppendInitialRoutesAndEntryFiles(rootNode, options);\n    }\n    return rootNode;\n  }\n  /**\n   * Converts the RequireContext keys (file paths) into a directory tree.\n   */\n  function getDirectoryTree(contextModule, options) {\n    var importMode = options.importMode || \"sync\";\n    var ignoreList = [/^\\.\\/\\+(html|native-intent)\\.[tj]sx?$/]; // Ignore the top level ./+html file\n    if (options.ignore) {\n      ignoreList.push(...options.ignore);\n    }\n    if (!options.preserveApiRoutes) {\n      ignoreList.push(/\\+api$/, /\\+api\\.[tj]sx?$/);\n    }\n    var rootDirectory = {\n      files: new Map(),\n      subdirectories: new Map()\n    };\n    var hasRoutes = false;\n    var isValid = false;\n    var contextKeys = contextModule.keys();\n    var redirects = {};\n    var rewrites = {};\n    var validRedirectDestinations;\n    // If we are keeping redirects as valid routes, then we need to add them to the contextKeys\n    // This is useful for generating a sitemap with redirects, or static site generation that includes redirects\n    if (options.preserveRedirectAndRewrites) {\n      if (options.redirects) {\n        var _loop = function () {\n            // Remove the leading `./` or `/`\n            var source = redirect.source.replace(/^\\.?\\//, '');\n            var isExternalRedirect = (0, url_1.shouldLinkExternally)(redirect.destination);\n            var targetDestination = isExternalRedirect ? redirect.destination : (0, matchers_1.stripInvisibleSegmentsFromPath)((0, matchers_1.removeFileSystemDots)((0, matchers_1.removeFileSystemExtensions)(redirect.destination.replace(/^\\.?\\/?/, ''))));\n            var normalizedSource = (0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(source));\n            if (ignoreList.some(regex => regex.test(normalizedSource))) {\n              return 0; // continue\n            }\n            // Loop over this once and cache the valid destinations\n            validRedirectDestinations ??= contextKeys.map(key => {\n              return [(0, matchers_1.stripInvisibleSegmentsFromPath)((0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(key))), key];\n            });\n            var destination = isExternalRedirect ? targetDestination : validRedirectDestinations.find(key => key[0] === targetDestination)?.[1];\n            if (!destination) {\n              /*\n               * Only throw the error when we are preserving the api routes\n               * When doing a static export, API routes will not exist so the redirect destination may not exist.\n               * The desired behavior for this error is to warn the user when running `expo start`, so its ok if\n               * `expo export` swallows this error.\n               */\n              if (options.preserveApiRoutes) {\n                throw new Error(`Redirect destination \"${redirect.destination}\" does not exist.`);\n              }\n              return 0; // continue\n            }\n            var fakeContextKey = (0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(source));\n            contextKeys.push(fakeContextKey);\n            redirects[fakeContextKey] = {\n              source,\n              destination,\n              permanent: Boolean(redirect.permanent),\n              external: isExternalRedirect,\n              methods: redirect.methods\n            };\n          },\n          _ret;\n        for (var redirect of options.redirects) {\n          _ret = _loop();\n          if (_ret === 0) continue;\n        }\n      }\n      if (options.rewrites) {\n        var _loop2 = function () {\n            // Remove the leading `./` or `/`\n            var source = rewrite.source.replace(/^\\.?\\//, '');\n            var targetDestination = (0, matchers_1.stripInvisibleSegmentsFromPath)((0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(rewrite.destination)));\n            var normalizedSource = (0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(source));\n            if (ignoreList.some(regex => regex.test(normalizedSource))) {\n              return 0; // continue\n            }\n            // Loop over this once and cache the valid destinations\n            validRedirectDestinations ??= contextKeys.map(key => {\n              return [(0, matchers_1.stripInvisibleSegmentsFromPath)((0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(key))), key];\n            });\n            var destination = validRedirectDestinations.find(key => key[0] === targetDestination)?.[1];\n            if (!destination) {\n              /*\n               * Only throw the error when we are preserving the api routes\n               * When doing a static export, API routes will not exist so the redirect destination may not exist.\n               * The desired behavior for this error is to warn the user when running `expo start`, so its ok if\n               * `expo export` swallows this error.\n               */\n              if (options.preserveApiRoutes) {\n                throw new Error(`Redirect destination \"${rewrite.destination}\" does not exist.`);\n              }\n              return 0; // continue\n            }\n            // Add a fake context key\n            var fakeContextKey = `./${source}.tsx`;\n            contextKeys.push(fakeContextKey);\n            rewrites[fakeContextKey] = {\n              source,\n              destination,\n              methods: rewrite.methods\n            };\n          },\n          _ret2;\n        for (var rewrite of options.rewrites) {\n          _ret2 = _loop2();\n          if (_ret2 === 0) continue;\n        }\n      }\n    }\n    var _loop3 = function (filePath) {\n        if (ignoreList.some(regex => regex.test(filePath))) {\n          return 0; // continue\n        }\n        isValid = true;\n        var meta = getFileMeta(filePath, options, redirects, rewrites);\n        // This is a file that should be ignored. e.g maybe it has an invalid platform?\n        if (meta.specificity < 0) {\n          return 0; // continue\n        }\n        var node = {\n          type: meta.isApi ? 'api' : meta.isLayout ? 'layout' : 'route',\n          loadRoute() {\n            var routeModule;\n            if (options.ignoreRequireErrors) {\n              try {\n                routeModule = contextModule(filePath);\n              } catch {\n                routeModule = {};\n              }\n            } else {\n              routeModule = contextModule(filePath);\n            }\n            if (process.env.NODE_ENV === 'development' && importMode === 'sync') {\n              // In development mode, when async routes are disabled, add some extra error handling to improve the developer experience.\n              // This can be useful when you accidentally use an async function in a route file for the default export.\n              if (routeModule instanceof Promise) {\n                throw new Error(`Route \"${filePath}\" cannot be a promise when async routes is disabled.`);\n              }\n              var defaultExport = routeModule?.default;\n              if (defaultExport instanceof Promise) {\n                throw new Error(`The default export from route \"${filePath}\" is a promise. Ensure the React Component does not use async or promises.`);\n              }\n              // check if default is an async function without invoking it\n              if (defaultExport instanceof Function &&\n              // This only works on web because Hermes support async functions so we have to transform them out.\n              defaultExport.constructor.name === 'AsyncFunction') {\n                throw new Error(`The default export from route \"${filePath}\" is an async function. Ensure the React Component does not use async or promises.`);\n              }\n            }\n            return routeModule;\n          },\n          contextKey: filePath,\n          route: '',\n          // This is overwritten during hoisting based upon the _layout\n          dynamic: null,\n          children: [] // While we are building the directory tree, we don't know the node's children just yet. This is added during hoisting\n        };\n        if (meta.isRedirect) {\n          node.destinationContextKey = redirects[filePath].destination;\n          node.permanent = redirects[filePath].permanent;\n          node.generated = true;\n          if (node.type === 'route') {\n            node = options.getSystemRoute({\n              type: 'redirect',\n              route: (0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(node.destinationContextKey))\n            }, node);\n          }\n          if (redirects[filePath].methods) {\n            node.methods = redirects[filePath].methods;\n          }\n          node.type = 'redirect';\n        }\n        if (meta.isRewrite) {\n          node.destinationContextKey = rewrites[filePath].destination;\n          node.generated = true;\n          if (node.type === 'route') {\n            node = options.getSystemRoute({\n              type: 'rewrite',\n              route: (0, matchers_1.removeFileSystemDots)((0, matchers_1.removeSupportedExtensions)(node.destinationContextKey))\n            }, node);\n          }\n          if (redirects[filePath].methods) {\n            node.methods = redirects[filePath].methods;\n          }\n          node.type = 'rewrite';\n        }\n        if (process.env.NODE_ENV === 'development') {\n          // If the user has set the `EXPO_ROUTER_IMPORT_MODE` to `sync` then we should\n          // filter the missing routes.\n          if (node.type !== 'api' && importMode === 'sync') {\n            var routeItem = node.loadRoute();\n            // Have a warning for nullish ex\n            var route = routeItem?.default;\n            if (route == null) {\n              // Do not throw an error since a user may just be creating a new route.\n              console.warn(`Route \"${filePath}\" is missing the required default export. Ensure a React component is exported as default.`);\n              return 0; // continue\n            }\n            if (['boolean', 'number', 'string'].includes(typeof route)) {\n              throw new Error(`The default export from route \"${filePath}\" is an unsupported type: \"${typeof route}\". Only React Components are supported as default exports from route files.`);\n            }\n          }\n        }\n        /**\n         * A single filepath may be extrapolated into multiple routes if it contains array syntax.\n         * Another way to thinking about is that a filepath node is present in multiple leaves of the directory tree.\n         */\n        for (var _route of extrapolateGroups(meta.route)) {\n          // Traverse the directory tree to its leaf node, creating any missing directories along the way\n          var subdirectoryParts = _route.split('/').slice(0, -1);\n          // Start at the root directory and traverse the path to the leaf directory\n          var directory = rootDirectory;\n          for (var part of subdirectoryParts) {\n            var subDirectory = directory.subdirectories.get(part);\n            // Create any missing subdirectories\n            if (!subDirectory) {\n              subDirectory = {\n                files: new Map(),\n                subdirectories: new Map()\n              };\n              directory.subdirectories.set(part, subDirectory);\n            }\n            directory = subDirectory;\n          }\n          // Clone the node for this route\n          node = {\n            ...node,\n            route: _route\n          };\n          if (meta.isLayout) {\n            directory.layout ??= [];\n            var existing = directory.layout[meta.specificity];\n            if (existing) {\n              // In production, use the first route found\n              if (process.env.NODE_ENV !== 'production') {\n                throw new Error(`The layouts \"${filePath}\" and \"${existing.contextKey}\" conflict on the route \"/${_route}\". Remove or rename one of these files.`);\n              }\n            } else {\n              node = getLayoutNode(node, options);\n              directory.layout[meta.specificity] = node;\n            }\n          } else if (meta.isApi) {\n            var fileKey = `${_route}+api`;\n            var nodes = directory.files.get(fileKey);\n            if (!nodes) {\n              nodes = [];\n              directory.files.set(fileKey, nodes);\n            }\n            // API Routes have no specificity, they are always the first node\n            var _existing = nodes[0];\n            if (_existing) {\n              // In production, use the first route found\n              if (process.env.NODE_ENV !== 'production') {\n                throw new Error(`The API route file \"${filePath}\" and \"${_existing.contextKey}\" conflict on the route \"/${_route}\". Remove or rename one of these files.`);\n              }\n            } else {\n              nodes[0] = node;\n            }\n          } else {\n            var _nodes = directory.files.get(_route);\n            if (!_nodes) {\n              _nodes = [];\n              directory.files.set(_route, _nodes);\n            }\n            /**\n             * If there is an existing node with the same specificity, then we have a conflict.\n             * NOTE(Platform Routes):\n             *    We cannot check for specificity conflicts here, as we haven't processed all the context keys yet!\n             *    This will be checked during hoisting, as well as enforcing that all routes have a non-platform route.\n             */\n            var _existing2 = _nodes[meta.specificity];\n            if (_existing2) {\n              // In production, use the first route found\n              if (process.env.NODE_ENV !== 'production') {\n                throw new Error(`The route files \"${filePath}\" and \"${_existing2.contextKey}\" conflict on the route \"/${_route}\". Remove or rename one of these files.`);\n              }\n            } else {\n              hasRoutes ||= true;\n              _nodes[meta.specificity] = node;\n            }\n          }\n        }\n      },\n      _ret3;\n    for (var filePath of contextKeys) {\n      _ret3 = _loop3(filePath);\n      if (_ret3 === 0) continue;\n    }\n    // If there are no routes/layouts then we should display the tutorial.\n    if (!isValid) {\n      return null;\n    }\n    /**\n     * If there are no top-level _layout, add a default _layout\n     * While this is a generated route, it will still be generated even if skipGenerated is true.\n     */\n    if (!rootDirectory.layout) {\n      rootDirectory.layout = [options.getSystemRoute({\n        type: 'layout',\n        route: ''\n      })];\n    }\n    // Only include the sitemap if there are routes.\n    if (!options.skipGenerated) {\n      if (hasRoutes && options.sitemap !== false) {\n        appendSitemapRoute(rootDirectory, options);\n      }\n      if (options.notFound !== false) {\n        appendNotFoundRoute(rootDirectory, options);\n      }\n    }\n    return rootDirectory;\n  }\n  /**\n   * Flatten the directory tree into routes, hoisting routes to the nearest _layout.\n   */\n  function flattenDirectoryTreeToRoutes(directory, options, /* The nearest _layout file in the directory tree */\n  layout) {\n    var pathToRemove = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '';\n    /**\n     * This directory has a _layout file so it becomes the new target for hoisting routes.\n     */\n    if (directory.layout) {\n      var previousLayout = layout;\n      layout = getMostSpecific(directory.layout);\n      // Add the new layout as a child of its parent\n      if (previousLayout) {\n        previousLayout.children.push(layout);\n      }\n      if (options.internal_stripLoadRoute) {\n        delete layout.loadRoute;\n      }\n      // `route` is the absolute pathname. We need to make this relative to the last _layout\n      var newRoute = layout.route.replace(pathToRemove, '');\n      pathToRemove = layout.route ? `${layout.route}/` : '';\n      // Now update this layout with the new relative route and dynamic conventions\n      layout.route = newRoute;\n      layout.dynamic = generateDynamic(layout.contextKey.slice(0));\n    }\n    // This should never occur as there will always be a root layout, but it makes the type system happy\n    if (!layout) throw new Error('Expo Router Internal Error: No nearest layout');\n    for (var routes of directory.files.values()) {\n      var routeNode = getMostSpecific(routes);\n      // `route` is the absolute pathname. We need to make this relative to the nearest layout\n      routeNode.route = routeNode.route.replace(pathToRemove, '');\n      routeNode.dynamic = generateDynamic(routeNode.route);\n      if (options.internal_stripLoadRoute) {\n        delete routeNode.loadRoute;\n      }\n      layout.children.push(routeNode);\n    }\n    // Recursively flatten the subdirectories\n    for (var child of directory.subdirectories.values()) {\n      flattenDirectoryTreeToRoutes(child, options, layout, pathToRemove);\n    }\n    return layout;\n  }\n  function getFileMeta(originalKey, options, redirects, rewrites) {\n    // Remove the leading `./`\n    var key = (0, matchers_1.removeSupportedExtensions)((0, matchers_1.removeFileSystemDots)(originalKey));\n    var route = key;\n    var parts = (0, matchers_1.removeFileSystemDots)(originalKey).split('/');\n    var filename = parts[parts.length - 1];\n    var _split = (0, matchers_1.removeSupportedExtensions)(filename).split('.'),\n      _split2 = _slicedToArray(_split, 2),\n      filenameWithoutExtensions = _split2[0],\n      platformExtension = _split2[1];\n    var isLayout = filenameWithoutExtensions === '_layout';\n    var isApi = originalKey.match(/\\+api\\.(\\w+\\.)?[jt]sx?$/);\n    if (filenameWithoutExtensions.startsWith('(') && filenameWithoutExtensions.endsWith(')')) {\n      throw new Error(`Invalid route ${originalKey}. Routes cannot end with '(group)' syntax`);\n    }\n    // Nested routes cannot start with the '+' character, except for the '+not-found' route\n    if (!isApi && filename.startsWith('+') && filenameWithoutExtensions !== '+not-found') {\n      var renamedRoute = [...parts.slice(0, -1), filename.slice(1)].join('/');\n      throw new Error(`Invalid route ${originalKey}. Route nodes cannot start with the '+' character. \"Rename it to ${renamedRoute}\"`);\n    }\n    var specificity = 0;\n    var hasPlatformExtension = validPlatforms.has(platformExtension);\n    var usePlatformRoutes = options.platformRoutes ?? true;\n    if (hasPlatformExtension) {\n      if (!usePlatformRoutes) {\n        // If the user has disabled platform routes, then we should ignore this file\n        specificity = -1;\n      } else if (!options.platform) {\n        // If we don't have a platform, then we should ignore this file\n        // This used by typed routes, sitemap, etc\n        specificity = -1;\n      } else if (platformExtension === options.platform) {\n        // If the platform extension is the same as the options.platform, then it is the most specific\n        specificity = 2;\n      } else if (platformExtension === 'native' && options.platform !== 'web') {\n        // `native` is allow but isn't as specific as the platform\n        specificity = 1;\n      } else if (platformExtension !== options.platform) {\n        // Somehow we have a platform extension that doesn't match the options.platform and it isn't native\n        // This is an invalid file and we will ignore it\n        specificity = -1;\n      }\n      if (isApi && specificity !== 0) {\n        throw new Error(`API routes cannot have platform extensions. Remove '.${platformExtension}' from '${originalKey}'`);\n      }\n      route = route.replace(new RegExp(`.${platformExtension}$`), '');\n    }\n    return {\n      route,\n      specificity,\n      isLayout,\n      isApi,\n      isRedirect: key in redirects,\n      isRewrite: key in rewrites\n    };\n  }\n  function getIgnoreList(options) {\n    var ignore = [/^\\.\\/\\+html\\.[tj]sx?$/, ...(options?.ignore ?? [])];\n    if (options?.preserveApiRoutes !== true) {\n      ignore.push(/\\+api\\.[tj]sx?$/);\n    }\n    return ignore;\n  }\n  /**\n   * Generates a set of strings which have the router array syntax extrapolated.\n   *\n   * /(a,b)/(c,d)/e.tsx => new Set(['a/c/e.tsx', 'a/d/e.tsx', 'b/c/e.tsx', 'b/d/e.tsx'])\n   */\n  function extrapolateGroups(key) {\n    var keys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Set();\n    var match = (0, matchers_1.matchArrayGroupName)(key);\n    if (!match) {\n      keys.add(key);\n      return keys;\n    }\n    var groups = match.split(',');\n    var groupsSet = new Set(groups);\n    if (groupsSet.size !== groups.length) {\n      throw new Error(`Array syntax cannot contain duplicate group name \"${groups}\" in \"${key}\".`);\n    }\n    if (groups.length === 1) {\n      keys.add(key);\n      return keys;\n    }\n    for (var group of groups) {\n      extrapolateGroups(key.replace(match, group.trim()), keys);\n    }\n    return keys;\n  }\n  function generateDynamic(path) {\n    var dynamic = path.split('/').map(part => {\n      if (part === '+not-found') {\n        return {\n          name: '+not-found',\n          deep: true,\n          notFound: true\n        };\n      }\n      return (0, matchers_1.matchDynamicName)(part) ?? null;\n    }).filter(part => !!part);\n    return dynamic.length === 0 ? null : dynamic;\n  }\n  function appendSitemapRoute(directory, options) {\n    if (!directory.files.has('_sitemap') && options.getSystemRoute) {\n      directory.files.set('_sitemap', [options.getSystemRoute({\n        type: 'route',\n        route: '_sitemap'\n      })]);\n    }\n  }\n  function appendNotFoundRoute(directory, options) {\n    if (!directory.files.has('+not-found') && options.getSystemRoute) {\n      directory.files.set('+not-found', [options.getSystemRoute({\n        type: 'route',\n        route: '+not-found'\n      })]);\n    }\n  }\n  function getLayoutNode(node, options) {\n    /**\n     * A file called `(a,b)/(c)/_layout.tsx` will generate two _layout routes: `(a)/(c)/_layout` and `(b)/(c)/_layout`.\n     * Each of these layouts will have a different anchor based upon the first group name.\n     */\n    // We may strip loadRoute during testing\n    var groupName = (0, matchers_1.matchLastGroupName)(node.route);\n    var childMatchingGroup = node.children.find(child => {\n      return child.route.replace(/\\/index$/, '') === groupName;\n    });\n    var anchor = childMatchingGroup?.route;\n    var loaded = node.loadRoute();\n    if (loaded?.unstable_settings) {\n      try {\n        // Allow unstable_settings={ initialRouteName: '...' } to override the default initial route name.\n        anchor = loaded.unstable_settings.anchor ?? loaded.unstable_settings.initialRouteName ?? anchor;\n      } catch (error) {\n        if (error instanceof Error) {\n          if (!error.message.match(/You cannot dot into a client module/)) {\n            throw error;\n          }\n        }\n      }\n      if (groupName) {\n        // Allow unstable_settings={ 'custom': { initialRouteName: '...' } } to override the less specific initial route name.\n        var groupSpecificInitialRouteName = loaded.unstable_settings?.[groupName]?.anchor ?? loaded.unstable_settings?.[groupName]?.initialRouteName;\n        anchor = groupSpecificInitialRouteName ?? anchor;\n      }\n    }\n    return {\n      ...node,\n      route: node.route.replace(/\\/?_layout$/, ''),\n      children: [],\n      // Each layout should have its own children\n      initialRouteName: anchor\n    };\n  }\n  function crawlAndAppendInitialRoutesAndEntryFiles(node, options) {\n    var entryPoints = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    if (node.type === 'route') {\n      node.entryPoints = [...new Set([...entryPoints, node.contextKey])];\n    } else if (node.type === 'redirect') {\n      node.entryPoints = [...new Set([...entryPoints, node.destinationContextKey])];\n    } else if (node.type === 'layout') {\n      if (!node.children) {\n        throw new Error(`Layout \"${node.contextKey}\" does not contain any child routes`);\n      }\n      // Every node below this layout will have it as an entryPoint\n      entryPoints = [...entryPoints, node.contextKey];\n      /**\n       * Calculate the initialRouteNode\n       *\n       * A file called `(a,b)/(c)/_layout.tsx` will generate two _layout routes: `(a)/(c)/_layout` and `(b)/(c)/_layout`.\n       * Each of these layouts will have a different anchor based upon the first group.\n       */\n      var groupName = (0, matchers_1.matchGroupName)(node.route);\n      var childMatchingGroup = node.children.find(child => {\n        return child.route.replace(/\\/index$/, '') === groupName;\n      });\n      var anchor = childMatchingGroup?.route;\n      // We may strip loadRoute during testing\n      if (!options.internal_stripLoadRoute) {\n        var loaded = node.loadRoute();\n        if (loaded?.unstable_settings) {\n          try {\n            // Allow unstable_settings={ initialRouteName: '...' } to override the default initial route name.\n            anchor = loaded.unstable_settings.anchor ?? loaded.unstable_settings.initialRouteName ?? anchor;\n          } catch (error) {\n            if (error instanceof Error) {\n              if (!error.message.match(/You cannot dot into a client module/)) {\n                throw error;\n              }\n            }\n          }\n          if (groupName) {\n            // Allow unstable_settings={ 'custom': { initialRouteName: '...' } } to override the less specific initial route name.\n            var groupSpecificInitialRouteName = loaded.unstable_settings?.[groupName]?.anchor ?? loaded.unstable_settings?.[groupName]?.initialRouteName;\n            anchor = groupSpecificInitialRouteName ?? anchor;\n          }\n        }\n      }\n      if (anchor) {\n        var anchorRoute = node.children.find(child => child.route === anchor);\n        if (!anchorRoute) {\n          var validAnchorRoutes = node.children.filter(child => !child.generated).map(child => `'${child.route}'`).join(', ');\n          if (groupName) {\n            throw new Error(`Layout ${node.contextKey} has invalid anchor '${anchor}' for group '(${groupName})'. Valid options are: ${validAnchorRoutes}`);\n          } else {\n            throw new Error(`Layout ${node.contextKey} has invalid anchor '${anchor}'. Valid options are: ${validAnchorRoutes}`);\n          }\n        }\n        // Navigators can add initialsRoutes into the history, so they need to be to be included in the entryPoints\n        node.initialRouteName = anchor;\n        entryPoints.push(anchorRoute.contextKey);\n      }\n      for (var child of node.children) {\n        crawlAndAppendInitialRoutesAndEntryFiles(child, options, entryPoints);\n      }\n    }\n  }\n  function getMostSpecific(routes) {\n    var route = routes[routes.length - 1];\n    if (!routes[0]) {\n      throw new Error(`The file ${route.contextKey} does not have a fallback sibling file without a platform extension.`);\n    }\n    // This works even tho routes is holey array (e.g it might have index 0 and 2 but not 1)\n    // `.length` includes the holes in its count\n    return routes[routes.length - 1];\n  }\n});", "lineCount": 624, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_slicedToArray"], [4, 20, 1, 13], [4, 23, 1, 13, "require"], [4, 30, 1, 13], [4, 31, 1, 13, "_dependencyMap"], [4, 45, 1, 13], [5, 2, 2, 0, "Object"], [5, 8, 2, 6], [5, 9, 2, 7, "defineProperty"], [5, 23, 2, 21], [5, 24, 2, 22, "exports"], [5, 31, 2, 29], [5, 33, 2, 31], [5, 45, 2, 43], [5, 47, 2, 45], [6, 4, 2, 47, "value"], [6, 9, 2, 52], [6, 11, 2, 54], [7, 2, 2, 59], [7, 3, 2, 60], [7, 4, 2, 61], [8, 2, 3, 0, "exports"], [8, 9, 3, 7], [8, 10, 3, 8, "getRoutes"], [8, 19, 3, 17], [8, 22, 3, 20, "getRoutes"], [8, 31, 3, 29], [9, 2, 4, 0, "exports"], [9, 9, 4, 7], [9, 10, 4, 8, "getIgnoreList"], [9, 23, 4, 21], [9, 26, 4, 24, "getIgnoreList"], [9, 39, 4, 37], [10, 2, 5, 0, "exports"], [10, 9, 5, 7], [10, 10, 5, 8, "extrapolateGroups"], [10, 27, 5, 25], [10, 30, 5, 28, "extrapolateGroups"], [10, 47, 5, 45], [11, 2, 6, 0, "exports"], [11, 9, 6, 7], [11, 10, 6, 8, "generateDynamic"], [11, 25, 6, 23], [11, 28, 6, 26, "generateDynamic"], [11, 43, 6, 41], [12, 2, 7, 0], [12, 6, 7, 6, "matchers_1"], [12, 16, 7, 16], [12, 19, 7, 19, "require"], [12, 26, 7, 26], [12, 27, 7, 26, "_dependencyMap"], [12, 41, 7, 26], [12, 58, 7, 39], [12, 59, 7, 40], [13, 2, 8, 0], [13, 6, 8, 6, "url_1"], [13, 11, 8, 11], [13, 14, 8, 14, "require"], [13, 21, 8, 21], [13, 22, 8, 21, "_dependencyMap"], [13, 36, 8, 21], [13, 54, 8, 35], [13, 55, 8, 36], [14, 2, 9, 0], [14, 6, 9, 6, "validPlatforms"], [14, 20, 9, 20], [14, 23, 9, 23], [14, 27, 9, 27, "Set"], [14, 30, 9, 30], [14, 31, 9, 31], [14, 32, 9, 32], [14, 41, 9, 41], [14, 43, 9, 43], [14, 48, 9, 48], [14, 50, 9, 50], [14, 58, 9, 58], [14, 60, 9, 60], [14, 65, 9, 65], [14, 66, 9, 66], [14, 67, 9, 67], [15, 2, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 2, 22, 0], [27, 11, 22, 9, "getRoutes"], [27, 20, 22, 18, "getRoutes"], [27, 21, 22, 19, "contextModule"], [27, 34, 22, 32], [27, 36, 22, 34, "options"], [27, 43, 22, 41], [27, 45, 22, 43], [28, 4, 23, 4], [28, 8, 23, 10, "directoryTree"], [28, 21, 23, 23], [28, 24, 23, 26, "getDirectoryTree"], [28, 40, 23, 42], [28, 41, 23, 43, "contextModule"], [28, 54, 23, 56], [28, 56, 23, 58, "options"], [28, 63, 23, 65], [28, 64, 23, 66], [29, 4, 24, 4], [30, 4, 25, 4], [30, 8, 25, 8], [30, 9, 25, 9, "directoryTree"], [30, 22, 25, 22], [30, 24, 25, 24], [31, 6, 26, 8], [31, 13, 26, 15], [31, 17, 26, 19], [32, 4, 27, 4], [33, 4, 28, 4], [33, 8, 28, 10, "rootNode"], [33, 16, 28, 18], [33, 19, 28, 21, "flattenDirectoryTreeToRoutes"], [33, 47, 28, 49], [33, 48, 28, 50, "directoryTree"], [33, 61, 28, 63], [33, 63, 28, 65, "options"], [33, 70, 28, 72], [33, 71, 28, 73], [34, 4, 29, 4], [34, 8, 29, 8], [34, 9, 29, 9, "options"], [34, 16, 29, 16], [34, 17, 29, 17, "ignoreEntryPoints"], [34, 34, 29, 34], [34, 36, 29, 36], [35, 6, 30, 8, "crawlAndAppendInitialRoutesAndEntryFiles"], [35, 46, 30, 48], [35, 47, 30, 49, "rootNode"], [35, 55, 30, 57], [35, 57, 30, 59, "options"], [35, 64, 30, 66], [35, 65, 30, 67], [36, 4, 31, 4], [37, 4, 32, 4], [37, 11, 32, 11, "rootNode"], [37, 19, 32, 19], [38, 2, 33, 0], [39, 2, 34, 0], [40, 0, 35, 0], [41, 0, 36, 0], [42, 2, 37, 0], [42, 11, 37, 9, "getDirectoryTree"], [42, 27, 37, 25, "getDirectoryTree"], [42, 28, 37, 26, "contextModule"], [42, 41, 37, 39], [42, 43, 37, 41, "options"], [42, 50, 37, 48], [42, 52, 37, 50], [43, 4, 38, 4], [43, 8, 38, 10, "importMode"], [43, 18, 38, 20], [43, 21, 38, 23, "options"], [43, 28, 38, 30], [43, 29, 38, 31, "importMode"], [43, 39, 38, 41], [43, 49, 38, 80], [44, 4, 39, 4], [44, 8, 39, 10, "ignoreList"], [44, 18, 39, 20], [44, 21, 39, 23], [44, 22, 39, 24], [44, 61, 39, 63], [44, 62, 39, 64], [44, 63, 39, 65], [44, 64, 39, 66], [45, 4, 40, 4], [45, 8, 40, 8, "options"], [45, 15, 40, 15], [45, 16, 40, 16, "ignore"], [45, 22, 40, 22], [45, 24, 40, 24], [46, 6, 41, 8, "ignoreList"], [46, 16, 41, 18], [46, 17, 41, 19, "push"], [46, 21, 41, 23], [46, 22, 41, 24], [46, 25, 41, 27, "options"], [46, 32, 41, 34], [46, 33, 41, 35, "ignore"], [46, 39, 41, 41], [46, 40, 41, 42], [47, 4, 42, 4], [48, 4, 43, 4], [48, 8, 43, 8], [48, 9, 43, 9, "options"], [48, 16, 43, 16], [48, 17, 43, 17, "preserveApiRoutes"], [48, 34, 43, 34], [48, 36, 43, 36], [49, 6, 44, 8, "ignoreList"], [49, 16, 44, 18], [49, 17, 44, 19, "push"], [49, 21, 44, 23], [49, 22, 44, 24], [49, 30, 44, 32], [49, 32, 44, 34], [49, 49, 44, 51], [49, 50, 44, 52], [50, 4, 45, 4], [51, 4, 46, 4], [51, 8, 46, 10, "rootDirectory"], [51, 21, 46, 23], [51, 24, 46, 26], [52, 6, 47, 8, "files"], [52, 11, 47, 13], [52, 13, 47, 15], [52, 17, 47, 19, "Map"], [52, 20, 47, 22], [52, 21, 47, 23], [52, 22, 47, 24], [53, 6, 48, 8, "subdirectories"], [53, 20, 48, 22], [53, 22, 48, 24], [53, 26, 48, 28, "Map"], [53, 29, 48, 31], [53, 30, 48, 32], [54, 4, 49, 4], [54, 5, 49, 5], [55, 4, 50, 4], [55, 8, 50, 8, "hasRoutes"], [55, 17, 50, 17], [55, 20, 50, 20], [55, 25, 50, 25], [56, 4, 51, 4], [56, 8, 51, 8, "<PERSON><PERSON><PERSON><PERSON>"], [56, 15, 51, 15], [56, 18, 51, 18], [56, 23, 51, 23], [57, 4, 52, 4], [57, 8, 52, 10, "contextKeys"], [57, 19, 52, 21], [57, 22, 52, 24, "contextModule"], [57, 35, 52, 37], [57, 36, 52, 38, "keys"], [57, 40, 52, 42], [57, 41, 52, 43], [57, 42, 52, 44], [58, 4, 53, 4], [58, 8, 53, 10, "redirects"], [58, 17, 53, 19], [58, 20, 53, 22], [58, 21, 53, 23], [58, 22, 53, 24], [59, 4, 54, 4], [59, 8, 54, 10, "rewrites"], [59, 16, 54, 18], [59, 19, 54, 21], [59, 20, 54, 22], [59, 21, 54, 23], [60, 4, 55, 4], [60, 8, 55, 8, "validRedirectDestinations"], [60, 33, 55, 33], [61, 4, 56, 4], [62, 4, 57, 4], [63, 4, 58, 4], [63, 8, 58, 8, "options"], [63, 15, 58, 15], [63, 16, 58, 16, "preserveRedirectAndRewrites"], [63, 43, 58, 43], [63, 45, 58, 45], [64, 6, 59, 8], [64, 10, 59, 12, "options"], [64, 17, 59, 19], [64, 18, 59, 20, "redirects"], [64, 27, 59, 29], [64, 29, 59, 31], [65, 8, 59, 31], [65, 12, 59, 31, "_loop"], [65, 17, 59, 31], [65, 29, 59, 31, "_loop"], [65, 30, 59, 31], [65, 32, 60, 54], [66, 12, 61, 16], [67, 12, 62, 16], [67, 16, 62, 22, "source"], [67, 22, 62, 28], [67, 25, 62, 31, "redirect"], [67, 33, 62, 39], [67, 34, 62, 40, "source"], [67, 40, 62, 46], [67, 41, 62, 47, "replace"], [67, 48, 62, 54], [67, 49, 62, 55], [67, 57, 62, 63], [67, 59, 62, 65], [67, 61, 62, 67], [67, 62, 62, 68], [68, 12, 63, 16], [68, 16, 63, 22, "isExternalRedirect"], [68, 34, 63, 40], [68, 37, 63, 43], [68, 38, 63, 44], [68, 39, 63, 45], [68, 41, 63, 47, "url_1"], [68, 46, 63, 52], [68, 47, 63, 53, "shouldLinkExternally"], [68, 67, 63, 73], [68, 69, 63, 75, "redirect"], [68, 77, 63, 83], [68, 78, 63, 84, "destination"], [68, 89, 63, 95], [68, 90, 63, 96], [69, 12, 64, 16], [69, 16, 64, 22, "targetDestination"], [69, 33, 64, 39], [69, 36, 64, 42, "isExternalRedirect"], [69, 54, 64, 60], [69, 57, 65, 22, "redirect"], [69, 65, 65, 30], [69, 66, 65, 31, "destination"], [69, 77, 65, 42], [69, 80, 66, 22], [69, 81, 66, 23], [69, 82, 66, 24], [69, 84, 66, 26, "matchers_1"], [69, 94, 66, 36], [69, 95, 66, 37, "stripInvisibleSegmentsFromPath"], [69, 125, 66, 67], [69, 127, 66, 69], [69, 128, 66, 70], [69, 129, 66, 71], [69, 131, 66, 73, "matchers_1"], [69, 141, 66, 83], [69, 142, 66, 84, "removeFileSystemDots"], [69, 162, 66, 104], [69, 164, 66, 106], [69, 165, 66, 107], [69, 166, 66, 108], [69, 168, 66, 110, "matchers_1"], [69, 178, 66, 120], [69, 179, 66, 121, "removeFileSystemExtensions"], [69, 205, 66, 147], [69, 207, 66, 149, "redirect"], [69, 215, 66, 157], [69, 216, 66, 158, "destination"], [69, 227, 66, 169], [69, 228, 66, 170, "replace"], [69, 235, 66, 177], [69, 236, 66, 178], [69, 245, 66, 187], [69, 247, 66, 189], [69, 249, 66, 191], [69, 250, 66, 192], [69, 251, 66, 193], [69, 252, 66, 194], [69, 253, 66, 195], [70, 12, 67, 16], [70, 16, 67, 22, "normalizedSource"], [70, 32, 67, 38], [70, 35, 67, 41], [70, 36, 67, 42], [70, 37, 67, 43], [70, 39, 67, 45, "matchers_1"], [70, 49, 67, 55], [70, 50, 67, 56, "removeFileSystemDots"], [70, 70, 67, 76], [70, 72, 67, 78], [70, 73, 67, 79], [70, 74, 67, 80], [70, 76, 67, 82, "matchers_1"], [70, 86, 67, 92], [70, 87, 67, 93, "removeSupportedExtensions"], [70, 112, 67, 118], [70, 114, 67, 120, "source"], [70, 120, 67, 126], [70, 121, 67, 127], [70, 122, 67, 128], [71, 12, 68, 16], [71, 16, 68, 20, "ignoreList"], [71, 26, 68, 30], [71, 27, 68, 31, "some"], [71, 31, 68, 35], [71, 32, 68, 37, "regex"], [71, 37, 68, 42], [71, 41, 68, 47, "regex"], [71, 46, 68, 52], [71, 47, 68, 53, "test"], [71, 51, 68, 57], [71, 52, 68, 58, "normalizedSource"], [71, 68, 68, 74], [71, 69, 68, 75], [71, 70, 68, 76], [71, 72, 68, 78], [72, 14, 68, 78], [73, 12, 70, 16], [74, 12, 71, 16], [75, 12, 72, 16, "validRedirectDestinations"], [75, 37, 72, 41], [75, 42, 72, 46, "contextKeys"], [75, 53, 72, 57], [75, 54, 72, 58, "map"], [75, 57, 72, 61], [75, 58, 72, 63, "key"], [75, 61, 72, 66], [75, 65, 72, 71], [76, 14, 73, 20], [76, 21, 73, 27], [76, 22, 74, 24], [76, 23, 74, 25], [76, 24, 74, 26], [76, 26, 74, 28, "matchers_1"], [76, 36, 74, 38], [76, 37, 74, 39, "stripInvisibleSegmentsFromPath"], [76, 67, 74, 69], [76, 69, 74, 71], [76, 70, 74, 72], [76, 71, 74, 73], [76, 73, 74, 75, "matchers_1"], [76, 83, 74, 85], [76, 84, 74, 86, "removeFileSystemDots"], [76, 104, 74, 106], [76, 106, 74, 108], [76, 107, 74, 109], [76, 108, 74, 110], [76, 110, 74, 112, "matchers_1"], [76, 120, 74, 122], [76, 121, 74, 123, "removeSupportedExtensions"], [76, 146, 74, 148], [76, 148, 74, 150, "key"], [76, 151, 74, 153], [76, 152, 74, 154], [76, 153, 74, 155], [76, 154, 74, 156], [76, 156, 75, 24, "key"], [76, 159, 75, 27], [76, 160, 76, 21], [77, 12, 77, 16], [77, 13, 77, 17], [77, 14, 77, 18], [78, 12, 78, 16], [78, 16, 78, 22, "destination"], [78, 27, 78, 33], [78, 30, 78, 36, "isExternalRedirect"], [78, 48, 78, 54], [78, 51, 79, 22, "targetDestination"], [78, 68, 79, 39], [78, 71, 80, 22, "validRedirectDestinations"], [78, 96, 80, 47], [78, 97, 80, 48, "find"], [78, 101, 80, 52], [78, 102, 80, 54, "key"], [78, 105, 80, 57], [78, 109, 80, 62, "key"], [78, 112, 80, 65], [78, 113, 80, 66], [78, 114, 80, 67], [78, 115, 80, 68], [78, 120, 80, 73, "targetDestination"], [78, 137, 80, 90], [78, 138, 80, 91], [78, 141, 80, 94], [78, 142, 80, 95], [78, 143, 80, 96], [79, 12, 81, 16], [79, 16, 81, 20], [79, 17, 81, 21, "destination"], [79, 28, 81, 32], [79, 30, 81, 34], [80, 14, 82, 20], [81, 0, 83, 0], [82, 0, 84, 0], [83, 0, 85, 0], [84, 0, 86, 0], [85, 0, 87, 0], [86, 14, 88, 20], [86, 18, 88, 24, "options"], [86, 25, 88, 31], [86, 26, 88, 32, "preserveApiRoutes"], [86, 43, 88, 49], [86, 45, 88, 51], [87, 16, 89, 24], [87, 22, 89, 30], [87, 26, 89, 34, "Error"], [87, 31, 89, 39], [87, 32, 89, 40], [87, 57, 89, 65, "redirect"], [87, 65, 89, 73], [87, 66, 89, 74, "destination"], [87, 77, 89, 85], [87, 96, 89, 104], [87, 97, 89, 105], [88, 14, 90, 20], [89, 14, 90, 21], [90, 12, 92, 16], [91, 12, 93, 16], [91, 16, 93, 22, "fakeContextKey"], [91, 30, 93, 36], [91, 33, 93, 39], [91, 34, 93, 40], [91, 35, 93, 41], [91, 37, 93, 43, "matchers_1"], [91, 47, 93, 53], [91, 48, 93, 54, "removeFileSystemDots"], [91, 68, 93, 74], [91, 70, 93, 76], [91, 71, 93, 77], [91, 72, 93, 78], [91, 74, 93, 80, "matchers_1"], [91, 84, 93, 90], [91, 85, 93, 91, "removeSupportedExtensions"], [91, 110, 93, 116], [91, 112, 93, 118, "source"], [91, 118, 93, 124], [91, 119, 93, 125], [91, 120, 93, 126], [92, 12, 94, 16, "contextKeys"], [92, 23, 94, 27], [92, 24, 94, 28, "push"], [92, 28, 94, 32], [92, 29, 94, 33, "fakeContextKey"], [92, 43, 94, 47], [92, 44, 94, 48], [93, 12, 95, 16, "redirects"], [93, 21, 95, 25], [93, 22, 95, 26, "fakeContextKey"], [93, 36, 95, 40], [93, 37, 95, 41], [93, 40, 95, 44], [94, 14, 96, 20, "source"], [94, 20, 96, 26], [95, 14, 97, 20, "destination"], [95, 25, 97, 31], [96, 14, 98, 20, "permanent"], [96, 23, 98, 29], [96, 25, 98, 31, "Boolean"], [96, 32, 98, 38], [96, 33, 98, 39, "redirect"], [96, 41, 98, 47], [96, 42, 98, 48, "permanent"], [96, 51, 98, 57], [96, 52, 98, 58], [97, 14, 99, 20, "external"], [97, 22, 99, 28], [97, 24, 99, 30, "isExternalRedirect"], [97, 42, 99, 48], [98, 14, 100, 20, "methods"], [98, 21, 100, 27], [98, 23, 100, 29, "redirect"], [98, 31, 100, 37], [98, 32, 100, 38, "methods"], [99, 12, 101, 16], [99, 13, 101, 17], [100, 10, 102, 12], [100, 11, 102, 13], [101, 10, 102, 13, "_ret"], [101, 14, 102, 13], [102, 8, 60, 12], [102, 13, 60, 17], [102, 17, 60, 23, "redirect"], [102, 25, 60, 31], [102, 29, 60, 35, "options"], [102, 36, 60, 42], [102, 37, 60, 43, "redirects"], [102, 46, 60, 52], [103, 10, 60, 52, "_ret"], [103, 14, 60, 52], [103, 17, 60, 52, "_loop"], [103, 22, 60, 52], [104, 10, 60, 52], [104, 14, 60, 52, "_ret"], [104, 18, 60, 52], [104, 26, 69, 20], [105, 8, 69, 29], [106, 6, 103, 8], [107, 6, 104, 8], [107, 10, 104, 12, "options"], [107, 17, 104, 19], [107, 18, 104, 20, "rewrites"], [107, 26, 104, 28], [107, 28, 104, 30], [108, 8, 104, 30], [108, 12, 104, 30, "_loop2"], [108, 18, 104, 30], [108, 30, 104, 30, "_loop2"], [108, 31, 104, 30], [108, 33, 105, 52], [109, 12, 106, 16], [110, 12, 107, 16], [110, 16, 107, 22, "source"], [110, 22, 107, 28], [110, 25, 107, 31, "rewrite"], [110, 32, 107, 38], [110, 33, 107, 39, "source"], [110, 39, 107, 45], [110, 40, 107, 46, "replace"], [110, 47, 107, 53], [110, 48, 107, 54], [110, 56, 107, 62], [110, 58, 107, 64], [110, 60, 107, 66], [110, 61, 107, 67], [111, 12, 108, 16], [111, 16, 108, 22, "targetDestination"], [111, 33, 108, 39], [111, 36, 108, 42], [111, 37, 108, 43], [111, 38, 108, 44], [111, 40, 108, 46, "matchers_1"], [111, 50, 108, 56], [111, 51, 108, 57, "stripInvisibleSegmentsFromPath"], [111, 81, 108, 87], [111, 83, 108, 89], [111, 84, 108, 90], [111, 85, 108, 91], [111, 87, 108, 93, "matchers_1"], [111, 97, 108, 103], [111, 98, 108, 104, "removeFileSystemDots"], [111, 118, 108, 124], [111, 120, 108, 126], [111, 121, 108, 127], [111, 122, 108, 128], [111, 124, 108, 130, "matchers_1"], [111, 134, 108, 140], [111, 135, 108, 141, "removeSupportedExtensions"], [111, 160, 108, 166], [111, 162, 108, 168, "rewrite"], [111, 169, 108, 175], [111, 170, 108, 176, "destination"], [111, 181, 108, 187], [111, 182, 108, 188], [111, 183, 108, 189], [111, 184, 108, 190], [112, 12, 109, 16], [112, 16, 109, 22, "normalizedSource"], [112, 32, 109, 38], [112, 35, 109, 41], [112, 36, 109, 42], [112, 37, 109, 43], [112, 39, 109, 45, "matchers_1"], [112, 49, 109, 55], [112, 50, 109, 56, "removeFileSystemDots"], [112, 70, 109, 76], [112, 72, 109, 78], [112, 73, 109, 79], [112, 74, 109, 80], [112, 76, 109, 82, "matchers_1"], [112, 86, 109, 92], [112, 87, 109, 93, "removeSupportedExtensions"], [112, 112, 109, 118], [112, 114, 109, 120, "source"], [112, 120, 109, 126], [112, 121, 109, 127], [112, 122, 109, 128], [113, 12, 110, 16], [113, 16, 110, 20, "ignoreList"], [113, 26, 110, 30], [113, 27, 110, 31, "some"], [113, 31, 110, 35], [113, 32, 110, 37, "regex"], [113, 37, 110, 42], [113, 41, 110, 47, "regex"], [113, 46, 110, 52], [113, 47, 110, 53, "test"], [113, 51, 110, 57], [113, 52, 110, 58, "normalizedSource"], [113, 68, 110, 74], [113, 69, 110, 75], [113, 70, 110, 76], [113, 72, 110, 78], [114, 14, 110, 78], [115, 12, 112, 16], [116, 12, 113, 16], [117, 12, 114, 16, "validRedirectDestinations"], [117, 37, 114, 41], [117, 42, 114, 46, "contextKeys"], [117, 53, 114, 57], [117, 54, 114, 58, "map"], [117, 57, 114, 61], [117, 58, 114, 63, "key"], [117, 61, 114, 66], [117, 65, 114, 71], [118, 14, 115, 20], [118, 21, 115, 27], [118, 22, 116, 24], [118, 23, 116, 25], [118, 24, 116, 26], [118, 26, 116, 28, "matchers_1"], [118, 36, 116, 38], [118, 37, 116, 39, "stripInvisibleSegmentsFromPath"], [118, 67, 116, 69], [118, 69, 116, 71], [118, 70, 116, 72], [118, 71, 116, 73], [118, 73, 116, 75, "matchers_1"], [118, 83, 116, 85], [118, 84, 116, 86, "removeFileSystemDots"], [118, 104, 116, 106], [118, 106, 116, 108], [118, 107, 116, 109], [118, 108, 116, 110], [118, 110, 116, 112, "matchers_1"], [118, 120, 116, 122], [118, 121, 116, 123, "removeSupportedExtensions"], [118, 146, 116, 148], [118, 148, 116, 150, "key"], [118, 151, 116, 153], [118, 152, 116, 154], [118, 153, 116, 155], [118, 154, 116, 156], [118, 156, 117, 24, "key"], [118, 159, 117, 27], [118, 160, 118, 21], [119, 12, 119, 16], [119, 13, 119, 17], [119, 14, 119, 18], [120, 12, 120, 16], [120, 16, 120, 22, "destination"], [120, 27, 120, 33], [120, 30, 120, 36, "validRedirectDestinations"], [120, 55, 120, 61], [120, 56, 120, 62, "find"], [120, 60, 120, 66], [120, 61, 120, 68, "key"], [120, 64, 120, 71], [120, 68, 120, 76, "key"], [120, 71, 120, 79], [120, 72, 120, 80], [120, 73, 120, 81], [120, 74, 120, 82], [120, 79, 120, 87, "targetDestination"], [120, 96, 120, 104], [120, 97, 120, 105], [120, 100, 120, 108], [120, 101, 120, 109], [120, 102, 120, 110], [121, 12, 121, 16], [121, 16, 121, 20], [121, 17, 121, 21, "destination"], [121, 28, 121, 32], [121, 30, 121, 34], [122, 14, 122, 20], [123, 0, 123, 0], [124, 0, 124, 0], [125, 0, 125, 0], [126, 0, 126, 0], [127, 0, 127, 0], [128, 14, 128, 20], [128, 18, 128, 24, "options"], [128, 25, 128, 31], [128, 26, 128, 32, "preserveApiRoutes"], [128, 43, 128, 49], [128, 45, 128, 51], [129, 16, 129, 24], [129, 22, 129, 30], [129, 26, 129, 34, "Error"], [129, 31, 129, 39], [129, 32, 129, 40], [129, 57, 129, 65, "rewrite"], [129, 64, 129, 72], [129, 65, 129, 73, "destination"], [129, 76, 129, 84], [129, 95, 129, 103], [129, 96, 129, 104], [130, 14, 130, 20], [131, 14, 130, 21], [132, 12, 132, 16], [133, 12, 133, 16], [134, 12, 134, 16], [134, 16, 134, 22, "fakeContextKey"], [134, 30, 134, 36], [134, 33, 134, 39], [134, 38, 134, 44, "source"], [134, 44, 134, 50], [134, 50, 134, 56], [135, 12, 135, 16, "contextKeys"], [135, 23, 135, 27], [135, 24, 135, 28, "push"], [135, 28, 135, 32], [135, 29, 135, 33, "fakeContextKey"], [135, 43, 135, 47], [135, 44, 135, 48], [136, 12, 136, 16, "rewrites"], [136, 20, 136, 24], [136, 21, 136, 25, "fakeContextKey"], [136, 35, 136, 39], [136, 36, 136, 40], [136, 39, 136, 43], [137, 14, 136, 45, "source"], [137, 20, 136, 51], [138, 14, 136, 53, "destination"], [138, 25, 136, 64], [139, 14, 136, 66, "methods"], [139, 21, 136, 73], [139, 23, 136, 75, "rewrite"], [139, 30, 136, 82], [139, 31, 136, 83, "methods"], [140, 12, 136, 91], [140, 13, 136, 92], [141, 10, 137, 12], [141, 11, 137, 13], [142, 10, 137, 13, "_ret2"], [142, 15, 137, 13], [143, 8, 105, 12], [143, 13, 105, 17], [143, 17, 105, 23, "rewrite"], [143, 24, 105, 30], [143, 28, 105, 34, "options"], [143, 35, 105, 41], [143, 36, 105, 42, "rewrites"], [143, 44, 105, 50], [144, 10, 105, 50, "_ret2"], [144, 15, 105, 50], [144, 18, 105, 50, "_loop2"], [144, 24, 105, 50], [145, 10, 105, 50], [145, 14, 105, 50, "_ret2"], [145, 19, 105, 50], [145, 27, 111, 20], [146, 8, 111, 29], [147, 6, 138, 8], [148, 4, 139, 4], [149, 4, 139, 5], [149, 8, 139, 5, "_loop3"], [149, 14, 139, 5], [149, 26, 139, 5, "_loop3"], [149, 27, 139, 5, "filePath"], [149, 35, 139, 5], [149, 37, 140, 40], [150, 8, 141, 8], [150, 12, 141, 12, "ignoreList"], [150, 22, 141, 22], [150, 23, 141, 23, "some"], [150, 27, 141, 27], [150, 28, 141, 29, "regex"], [150, 33, 141, 34], [150, 37, 141, 39, "regex"], [150, 42, 141, 44], [150, 43, 141, 45, "test"], [150, 47, 141, 49], [150, 48, 141, 50, "filePath"], [150, 56, 141, 58], [150, 57, 141, 59], [150, 58, 141, 60], [150, 60, 141, 62], [151, 10, 141, 62], [152, 8, 143, 8], [153, 8, 144, 8, "<PERSON><PERSON><PERSON><PERSON>"], [153, 15, 144, 15], [153, 18, 144, 18], [153, 22, 144, 22], [154, 8, 145, 8], [154, 12, 145, 14, "meta"], [154, 16, 145, 18], [154, 19, 145, 21, "getFileMeta"], [154, 30, 145, 32], [154, 31, 145, 33, "filePath"], [154, 39, 145, 41], [154, 41, 145, 43, "options"], [154, 48, 145, 50], [154, 50, 145, 52, "redirects"], [154, 59, 145, 61], [154, 61, 145, 63, "rewrites"], [154, 69, 145, 71], [154, 70, 145, 72], [155, 8, 146, 8], [156, 8, 147, 8], [156, 12, 147, 12, "meta"], [156, 16, 147, 16], [156, 17, 147, 17, "specificity"], [156, 28, 147, 28], [156, 31, 147, 31], [156, 32, 147, 32], [156, 34, 147, 34], [157, 10, 147, 34], [158, 8, 149, 8], [159, 8, 150, 8], [159, 12, 150, 12, "node"], [159, 16, 150, 16], [159, 19, 150, 19], [160, 10, 151, 12, "type"], [160, 14, 151, 16], [160, 16, 151, 18, "meta"], [160, 20, 151, 22], [160, 21, 151, 23, "isApi"], [160, 26, 151, 28], [160, 29, 151, 31], [160, 34, 151, 36], [160, 37, 151, 39, "meta"], [160, 41, 151, 43], [160, 42, 151, 44, "isLayout"], [160, 50, 151, 52], [160, 53, 151, 55], [160, 61, 151, 63], [160, 64, 151, 66], [160, 71, 151, 73], [161, 10, 152, 12, "loadRoute"], [161, 19, 152, 21, "loadRoute"], [161, 20, 152, 21], [161, 22, 152, 24], [162, 12, 153, 16], [162, 16, 153, 20, "routeModule"], [162, 27, 153, 31], [163, 12, 154, 16], [163, 16, 154, 20, "options"], [163, 23, 154, 27], [163, 24, 154, 28, "ignoreRequireErrors"], [163, 43, 154, 47], [163, 45, 154, 49], [164, 14, 155, 20], [164, 18, 155, 24], [165, 16, 156, 24, "routeModule"], [165, 27, 156, 35], [165, 30, 156, 38, "contextModule"], [165, 43, 156, 51], [165, 44, 156, 52, "filePath"], [165, 52, 156, 60], [165, 53, 156, 61], [166, 14, 157, 20], [166, 15, 157, 21], [166, 16, 158, 20], [166, 22, 158, 26], [167, 16, 159, 24, "routeModule"], [167, 27, 159, 35], [167, 30, 159, 38], [167, 31, 159, 39], [167, 32, 159, 40], [168, 14, 160, 20], [169, 12, 161, 16], [169, 13, 161, 17], [169, 19, 162, 21], [170, 14, 163, 20, "routeModule"], [170, 25, 163, 31], [170, 28, 163, 34, "contextModule"], [170, 41, 163, 47], [170, 42, 163, 48, "filePath"], [170, 50, 163, 56], [170, 51, 163, 57], [171, 12, 164, 16], [172, 12, 165, 16], [172, 16, 165, 20, "process"], [172, 23, 165, 27], [172, 24, 165, 28, "env"], [172, 27, 165, 31], [172, 28, 165, 32, "NODE_ENV"], [172, 36, 165, 40], [172, 41, 165, 45], [172, 54, 165, 58], [172, 58, 165, 62, "importMode"], [172, 68, 165, 72], [172, 73, 165, 77], [172, 79, 165, 83], [172, 81, 165, 85], [173, 14, 166, 20], [174, 14, 167, 20], [175, 14, 168, 20], [175, 18, 168, 24, "routeModule"], [175, 29, 168, 35], [175, 41, 168, 47, "Promise"], [175, 48, 168, 54], [175, 50, 168, 56], [176, 16, 169, 24], [176, 22, 169, 30], [176, 26, 169, 34, "Error"], [176, 31, 169, 39], [176, 32, 169, 40], [176, 42, 169, 50, "filePath"], [176, 50, 169, 58], [176, 104, 169, 112], [176, 105, 169, 113], [177, 14, 170, 20], [178, 14, 171, 20], [178, 18, 171, 26, "defaultExport"], [178, 31, 171, 39], [178, 34, 171, 42, "routeModule"], [178, 45, 171, 53], [178, 47, 171, 55, "default"], [178, 54, 171, 62], [179, 14, 172, 20], [179, 18, 172, 24, "defaultExport"], [179, 31, 172, 37], [179, 43, 172, 49, "Promise"], [179, 50, 172, 56], [179, 52, 172, 58], [180, 16, 173, 24], [180, 22, 173, 30], [180, 26, 173, 34, "Error"], [180, 31, 173, 39], [180, 32, 173, 40], [180, 66, 173, 74, "filePath"], [180, 74, 173, 82], [180, 150, 173, 158], [180, 151, 173, 159], [181, 14, 174, 20], [182, 14, 175, 20], [183, 14, 176, 20], [183, 18, 176, 24, "defaultExport"], [183, 31, 176, 37], [183, 43, 176, 49, "Function"], [183, 51, 176, 57], [184, 14, 177, 24], [185, 14, 178, 24, "defaultExport"], [185, 27, 178, 37], [185, 28, 178, 38, "constructor"], [185, 39, 178, 49], [185, 40, 178, 50, "name"], [185, 44, 178, 54], [185, 49, 178, 59], [185, 64, 178, 74], [185, 66, 178, 76], [186, 16, 179, 24], [186, 22, 179, 30], [186, 26, 179, 34, "Error"], [186, 31, 179, 39], [186, 32, 179, 40], [186, 66, 179, 74, "filePath"], [186, 74, 179, 82], [186, 158, 179, 166], [186, 159, 179, 167], [187, 14, 180, 20], [188, 12, 181, 16], [189, 12, 182, 16], [189, 19, 182, 23, "routeModule"], [189, 30, 182, 34], [190, 10, 183, 12], [190, 11, 183, 13], [191, 10, 184, 12, "<PERSON><PERSON>ey"], [191, 20, 184, 22], [191, 22, 184, 24, "filePath"], [191, 30, 184, 32], [192, 10, 185, 12, "route"], [192, 15, 185, 17], [192, 17, 185, 19], [192, 19, 185, 21], [193, 10, 185, 23], [194, 10, 186, 12, "dynamic"], [194, 17, 186, 19], [194, 19, 186, 21], [194, 23, 186, 25], [195, 10, 187, 12, "children"], [195, 18, 187, 20], [195, 20, 187, 22], [195, 22, 187, 24], [195, 23, 187, 26], [196, 8, 188, 8], [196, 9, 188, 9], [197, 8, 189, 8], [197, 12, 189, 12, "meta"], [197, 16, 189, 16], [197, 17, 189, 17, "isRedirect"], [197, 27, 189, 27], [197, 29, 189, 29], [198, 10, 190, 12, "node"], [198, 14, 190, 16], [198, 15, 190, 17, "destinationContextKey"], [198, 36, 190, 38], [198, 39, 190, 41, "redirects"], [198, 48, 190, 50], [198, 49, 190, 51, "filePath"], [198, 57, 190, 59], [198, 58, 190, 60], [198, 59, 190, 61, "destination"], [198, 70, 190, 72], [199, 10, 191, 12, "node"], [199, 14, 191, 16], [199, 15, 191, 17, "permanent"], [199, 24, 191, 26], [199, 27, 191, 29, "redirects"], [199, 36, 191, 38], [199, 37, 191, 39, "filePath"], [199, 45, 191, 47], [199, 46, 191, 48], [199, 47, 191, 49, "permanent"], [199, 56, 191, 58], [200, 10, 192, 12, "node"], [200, 14, 192, 16], [200, 15, 192, 17, "generated"], [200, 24, 192, 26], [200, 27, 192, 29], [200, 31, 192, 33], [201, 10, 193, 12], [201, 14, 193, 16, "node"], [201, 18, 193, 20], [201, 19, 193, 21, "type"], [201, 23, 193, 25], [201, 28, 193, 30], [201, 35, 193, 37], [201, 37, 193, 39], [202, 12, 194, 16, "node"], [202, 16, 194, 20], [202, 19, 194, 23, "options"], [202, 26, 194, 30], [202, 27, 194, 31, "getSystemRoute"], [202, 41, 194, 45], [202, 42, 194, 46], [203, 14, 195, 20, "type"], [203, 18, 195, 24], [203, 20, 195, 26], [203, 30, 195, 36], [204, 14, 196, 20, "route"], [204, 19, 196, 25], [204, 21, 196, 27], [204, 22, 196, 28], [204, 23, 196, 29], [204, 25, 196, 31, "matchers_1"], [204, 35, 196, 41], [204, 36, 196, 42, "removeFileSystemDots"], [204, 56, 196, 62], [204, 58, 196, 64], [204, 59, 196, 65], [204, 60, 196, 66], [204, 62, 196, 68, "matchers_1"], [204, 72, 196, 78], [204, 73, 196, 79, "removeSupportedExtensions"], [204, 98, 196, 104], [204, 100, 196, 106, "node"], [204, 104, 196, 110], [204, 105, 196, 111, "destinationContextKey"], [204, 126, 196, 132], [204, 127, 196, 133], [205, 12, 197, 16], [205, 13, 197, 17], [205, 15, 197, 19, "node"], [205, 19, 197, 23], [205, 20, 197, 24], [206, 10, 198, 12], [207, 10, 199, 12], [207, 14, 199, 16, "redirects"], [207, 23, 199, 25], [207, 24, 199, 26, "filePath"], [207, 32, 199, 34], [207, 33, 199, 35], [207, 34, 199, 36, "methods"], [207, 41, 199, 43], [207, 43, 199, 45], [208, 12, 200, 16, "node"], [208, 16, 200, 20], [208, 17, 200, 21, "methods"], [208, 24, 200, 28], [208, 27, 200, 31, "redirects"], [208, 36, 200, 40], [208, 37, 200, 41, "filePath"], [208, 45, 200, 49], [208, 46, 200, 50], [208, 47, 200, 51, "methods"], [208, 54, 200, 58], [209, 10, 201, 12], [210, 10, 202, 12, "node"], [210, 14, 202, 16], [210, 15, 202, 17, "type"], [210, 19, 202, 21], [210, 22, 202, 24], [210, 32, 202, 34], [211, 8, 203, 8], [212, 8, 204, 8], [212, 12, 204, 12, "meta"], [212, 16, 204, 16], [212, 17, 204, 17, "isRewrite"], [212, 26, 204, 26], [212, 28, 204, 28], [213, 10, 205, 12, "node"], [213, 14, 205, 16], [213, 15, 205, 17, "destinationContextKey"], [213, 36, 205, 38], [213, 39, 205, 41, "rewrites"], [213, 47, 205, 49], [213, 48, 205, 50, "filePath"], [213, 56, 205, 58], [213, 57, 205, 59], [213, 58, 205, 60, "destination"], [213, 69, 205, 71], [214, 10, 206, 12, "node"], [214, 14, 206, 16], [214, 15, 206, 17, "generated"], [214, 24, 206, 26], [214, 27, 206, 29], [214, 31, 206, 33], [215, 10, 207, 12], [215, 14, 207, 16, "node"], [215, 18, 207, 20], [215, 19, 207, 21, "type"], [215, 23, 207, 25], [215, 28, 207, 30], [215, 35, 207, 37], [215, 37, 207, 39], [216, 12, 208, 16, "node"], [216, 16, 208, 20], [216, 19, 208, 23, "options"], [216, 26, 208, 30], [216, 27, 208, 31, "getSystemRoute"], [216, 41, 208, 45], [216, 42, 208, 46], [217, 14, 209, 20, "type"], [217, 18, 209, 24], [217, 20, 209, 26], [217, 29, 209, 35], [218, 14, 210, 20, "route"], [218, 19, 210, 25], [218, 21, 210, 27], [218, 22, 210, 28], [218, 23, 210, 29], [218, 25, 210, 31, "matchers_1"], [218, 35, 210, 41], [218, 36, 210, 42, "removeFileSystemDots"], [218, 56, 210, 62], [218, 58, 210, 64], [218, 59, 210, 65], [218, 60, 210, 66], [218, 62, 210, 68, "matchers_1"], [218, 72, 210, 78], [218, 73, 210, 79, "removeSupportedExtensions"], [218, 98, 210, 104], [218, 100, 210, 106, "node"], [218, 104, 210, 110], [218, 105, 210, 111, "destinationContextKey"], [218, 126, 210, 132], [218, 127, 210, 133], [219, 12, 211, 16], [219, 13, 211, 17], [219, 15, 211, 19, "node"], [219, 19, 211, 23], [219, 20, 211, 24], [220, 10, 212, 12], [221, 10, 213, 12], [221, 14, 213, 16, "redirects"], [221, 23, 213, 25], [221, 24, 213, 26, "filePath"], [221, 32, 213, 34], [221, 33, 213, 35], [221, 34, 213, 36, "methods"], [221, 41, 213, 43], [221, 43, 213, 45], [222, 12, 214, 16, "node"], [222, 16, 214, 20], [222, 17, 214, 21, "methods"], [222, 24, 214, 28], [222, 27, 214, 31, "redirects"], [222, 36, 214, 40], [222, 37, 214, 41, "filePath"], [222, 45, 214, 49], [222, 46, 214, 50], [222, 47, 214, 51, "methods"], [222, 54, 214, 58], [223, 10, 215, 12], [224, 10, 216, 12, "node"], [224, 14, 216, 16], [224, 15, 216, 17, "type"], [224, 19, 216, 21], [224, 22, 216, 24], [224, 31, 216, 33], [225, 8, 217, 8], [226, 8, 218, 8], [226, 12, 218, 12, "process"], [226, 19, 218, 19], [226, 20, 218, 20, "env"], [226, 23, 218, 23], [226, 24, 218, 24, "NODE_ENV"], [226, 32, 218, 32], [226, 37, 218, 37], [226, 50, 218, 50], [226, 52, 218, 52], [227, 10, 219, 12], [228, 10, 220, 12], [229, 10, 221, 12], [229, 14, 221, 16, "node"], [229, 18, 221, 20], [229, 19, 221, 21, "type"], [229, 23, 221, 25], [229, 28, 221, 30], [229, 33, 221, 35], [229, 37, 221, 39, "importMode"], [229, 47, 221, 49], [229, 52, 221, 54], [229, 58, 221, 60], [229, 60, 221, 62], [230, 12, 222, 16], [230, 16, 222, 22, "routeItem"], [230, 25, 222, 31], [230, 28, 222, 34, "node"], [230, 32, 222, 38], [230, 33, 222, 39, "loadRoute"], [230, 42, 222, 48], [230, 43, 222, 49], [230, 44, 222, 50], [231, 12, 223, 16], [232, 12, 224, 16], [232, 16, 224, 22, "route"], [232, 21, 224, 27], [232, 24, 224, 30, "routeItem"], [232, 33, 224, 39], [232, 35, 224, 41, "default"], [232, 42, 224, 48], [233, 12, 225, 16], [233, 16, 225, 20, "route"], [233, 21, 225, 25], [233, 25, 225, 29], [233, 29, 225, 33], [233, 31, 225, 35], [234, 14, 226, 20], [235, 14, 227, 20, "console"], [235, 21, 227, 27], [235, 22, 227, 28, "warn"], [235, 26, 227, 32], [235, 27, 227, 33], [235, 37, 227, 43, "filePath"], [235, 45, 227, 51], [235, 137, 227, 143], [235, 138, 227, 144], [236, 14, 227, 145], [237, 12, 229, 16], [238, 12, 230, 16], [238, 16, 230, 20], [238, 17, 230, 21], [238, 26, 230, 30], [238, 28, 230, 32], [238, 36, 230, 40], [238, 38, 230, 42], [238, 46, 230, 50], [238, 47, 230, 51], [238, 48, 230, 52, "includes"], [238, 56, 230, 60], [238, 57, 230, 61], [238, 64, 230, 68, "route"], [238, 69, 230, 73], [238, 70, 230, 74], [238, 72, 230, 76], [239, 14, 231, 20], [239, 20, 231, 26], [239, 24, 231, 30, "Error"], [239, 29, 231, 35], [239, 30, 231, 36], [239, 64, 231, 70, "filePath"], [239, 72, 231, 78], [239, 102, 231, 108], [239, 109, 231, 115, "route"], [239, 114, 231, 120], [239, 191, 231, 197], [239, 192, 231, 198], [240, 12, 232, 16], [241, 10, 233, 12], [242, 8, 234, 8], [243, 8, 235, 8], [244, 0, 236, 0], [245, 0, 237, 0], [246, 0, 238, 0], [247, 8, 239, 8], [247, 13, 239, 13], [247, 17, 239, 19, "route"], [247, 23, 239, 24], [247, 27, 239, 28, "extrapolateGroups"], [247, 44, 239, 45], [247, 45, 239, 46, "meta"], [247, 49, 239, 50], [247, 50, 239, 51, "route"], [247, 55, 239, 56], [247, 56, 239, 57], [247, 58, 239, 59], [248, 10, 240, 12], [249, 10, 241, 12], [249, 14, 241, 18, "subdirectoryParts"], [249, 31, 241, 35], [249, 34, 241, 38, "route"], [249, 40, 241, 43], [249, 41, 241, 44, "split"], [249, 46, 241, 49], [249, 47, 241, 50], [249, 50, 241, 53], [249, 51, 241, 54], [249, 52, 241, 55, "slice"], [249, 57, 241, 60], [249, 58, 241, 61], [249, 59, 241, 62], [249, 61, 241, 64], [249, 62, 241, 65], [249, 63, 241, 66], [249, 64, 241, 67], [250, 10, 242, 12], [251, 10, 243, 12], [251, 14, 243, 16, "directory"], [251, 23, 243, 25], [251, 26, 243, 28, "rootDirectory"], [251, 39, 243, 41], [252, 10, 244, 12], [252, 15, 244, 17], [252, 19, 244, 23, "part"], [252, 23, 244, 27], [252, 27, 244, 31, "subdirectoryParts"], [252, 44, 244, 48], [252, 46, 244, 50], [253, 12, 245, 16], [253, 16, 245, 20, "subDirectory"], [253, 28, 245, 32], [253, 31, 245, 35, "directory"], [253, 40, 245, 44], [253, 41, 245, 45, "subdirectories"], [253, 55, 245, 59], [253, 56, 245, 60, "get"], [253, 59, 245, 63], [253, 60, 245, 64, "part"], [253, 64, 245, 68], [253, 65, 245, 69], [254, 12, 246, 16], [255, 12, 247, 16], [255, 16, 247, 20], [255, 17, 247, 21, "subDirectory"], [255, 29, 247, 33], [255, 31, 247, 35], [256, 14, 248, 20, "subDirectory"], [256, 26, 248, 32], [256, 29, 248, 35], [257, 16, 249, 24, "files"], [257, 21, 249, 29], [257, 23, 249, 31], [257, 27, 249, 35, "Map"], [257, 30, 249, 38], [257, 31, 249, 39], [257, 32, 249, 40], [258, 16, 250, 24, "subdirectories"], [258, 30, 250, 38], [258, 32, 250, 40], [258, 36, 250, 44, "Map"], [258, 39, 250, 47], [258, 40, 250, 48], [259, 14, 251, 20], [259, 15, 251, 21], [260, 14, 252, 20, "directory"], [260, 23, 252, 29], [260, 24, 252, 30, "subdirectories"], [260, 38, 252, 44], [260, 39, 252, 45, "set"], [260, 42, 252, 48], [260, 43, 252, 49, "part"], [260, 47, 252, 53], [260, 49, 252, 55, "subDirectory"], [260, 61, 252, 67], [260, 62, 252, 68], [261, 12, 253, 16], [262, 12, 254, 16, "directory"], [262, 21, 254, 25], [262, 24, 254, 28, "subDirectory"], [262, 36, 254, 40], [263, 10, 255, 12], [264, 10, 256, 12], [265, 10, 257, 12, "node"], [265, 14, 257, 16], [265, 17, 257, 19], [266, 12, 257, 21], [266, 15, 257, 24, "node"], [266, 19, 257, 28], [267, 12, 257, 30, "route"], [267, 17, 257, 35], [267, 19, 257, 30, "route"], [268, 10, 257, 36], [268, 11, 257, 37], [269, 10, 258, 12], [269, 14, 258, 16, "meta"], [269, 18, 258, 20], [269, 19, 258, 21, "isLayout"], [269, 27, 258, 29], [269, 29, 258, 31], [270, 12, 259, 16, "directory"], [270, 21, 259, 25], [270, 22, 259, 26, "layout"], [270, 28, 259, 32], [270, 33, 259, 37], [270, 35, 259, 39], [271, 12, 260, 16], [271, 16, 260, 22, "existing"], [271, 24, 260, 30], [271, 27, 260, 33, "directory"], [271, 36, 260, 42], [271, 37, 260, 43, "layout"], [271, 43, 260, 49], [271, 44, 260, 50, "meta"], [271, 48, 260, 54], [271, 49, 260, 55, "specificity"], [271, 60, 260, 66], [271, 61, 260, 67], [272, 12, 261, 16], [272, 16, 261, 20, "existing"], [272, 24, 261, 28], [272, 26, 261, 30], [273, 14, 262, 20], [274, 14, 263, 20], [274, 18, 263, 24, "process"], [274, 25, 263, 31], [274, 26, 263, 32, "env"], [274, 29, 263, 35], [274, 30, 263, 36, "NODE_ENV"], [274, 38, 263, 44], [274, 43, 263, 49], [274, 55, 263, 61], [274, 57, 263, 63], [275, 16, 264, 24], [275, 22, 264, 30], [275, 26, 264, 34, "Error"], [275, 31, 264, 39], [275, 32, 264, 40], [275, 48, 264, 56, "filePath"], [275, 56, 264, 64], [275, 66, 264, 74, "existing"], [275, 74, 264, 82], [275, 75, 264, 83, "<PERSON><PERSON>ey"], [275, 85, 264, 93], [275, 114, 264, 122, "route"], [275, 120, 264, 127], [275, 161, 264, 168], [275, 162, 264, 169], [276, 14, 265, 20], [277, 12, 266, 16], [277, 13, 266, 17], [277, 19, 267, 21], [278, 14, 268, 20, "node"], [278, 18, 268, 24], [278, 21, 268, 27, "getLayoutNode"], [278, 34, 268, 40], [278, 35, 268, 41, "node"], [278, 39, 268, 45], [278, 41, 268, 47, "options"], [278, 48, 268, 54], [278, 49, 268, 55], [279, 14, 269, 20, "directory"], [279, 23, 269, 29], [279, 24, 269, 30, "layout"], [279, 30, 269, 36], [279, 31, 269, 37, "meta"], [279, 35, 269, 41], [279, 36, 269, 42, "specificity"], [279, 47, 269, 53], [279, 48, 269, 54], [279, 51, 269, 57, "node"], [279, 55, 269, 61], [280, 12, 270, 16], [281, 10, 271, 12], [281, 11, 271, 13], [281, 17, 272, 17], [281, 21, 272, 21, "meta"], [281, 25, 272, 25], [281, 26, 272, 26, "isApi"], [281, 31, 272, 31], [281, 33, 272, 33], [282, 12, 273, 16], [282, 16, 273, 22, "fileKey"], [282, 23, 273, 29], [282, 26, 273, 32], [282, 29, 273, 35, "route"], [282, 35, 273, 40], [282, 41, 273, 46], [283, 12, 274, 16], [283, 16, 274, 20, "nodes"], [283, 21, 274, 25], [283, 24, 274, 28, "directory"], [283, 33, 274, 37], [283, 34, 274, 38, "files"], [283, 39, 274, 43], [283, 40, 274, 44, "get"], [283, 43, 274, 47], [283, 44, 274, 48, "fileKey"], [283, 51, 274, 55], [283, 52, 274, 56], [284, 12, 275, 16], [284, 16, 275, 20], [284, 17, 275, 21, "nodes"], [284, 22, 275, 26], [284, 24, 275, 28], [285, 14, 276, 20, "nodes"], [285, 19, 276, 25], [285, 22, 276, 28], [285, 24, 276, 30], [286, 14, 277, 20, "directory"], [286, 23, 277, 29], [286, 24, 277, 30, "files"], [286, 29, 277, 35], [286, 30, 277, 36, "set"], [286, 33, 277, 39], [286, 34, 277, 40, "fileKey"], [286, 41, 277, 47], [286, 43, 277, 49, "nodes"], [286, 48, 277, 54], [286, 49, 277, 55], [287, 12, 278, 16], [288, 12, 279, 16], [289, 12, 280, 16], [289, 16, 280, 22, "existing"], [289, 25, 280, 30], [289, 28, 280, 33, "nodes"], [289, 33, 280, 38], [289, 34, 280, 39], [289, 35, 280, 40], [289, 36, 280, 41], [290, 12, 281, 16], [290, 16, 281, 20, "existing"], [290, 25, 281, 28], [290, 27, 281, 30], [291, 14, 282, 20], [292, 14, 283, 20], [292, 18, 283, 24, "process"], [292, 25, 283, 31], [292, 26, 283, 32, "env"], [292, 29, 283, 35], [292, 30, 283, 36, "NODE_ENV"], [292, 38, 283, 44], [292, 43, 283, 49], [292, 55, 283, 61], [292, 57, 283, 63], [293, 16, 284, 24], [293, 22, 284, 30], [293, 26, 284, 34, "Error"], [293, 31, 284, 39], [293, 32, 284, 40], [293, 55, 284, 63, "filePath"], [293, 63, 284, 71], [293, 73, 284, 81, "existing"], [293, 82, 284, 89], [293, 83, 284, 90, "<PERSON><PERSON>ey"], [293, 93, 284, 100], [293, 122, 284, 129, "route"], [293, 128, 284, 134], [293, 169, 284, 175], [293, 170, 284, 176], [294, 14, 285, 20], [295, 12, 286, 16], [295, 13, 286, 17], [295, 19, 287, 21], [296, 14, 288, 20, "nodes"], [296, 19, 288, 25], [296, 20, 288, 26], [296, 21, 288, 27], [296, 22, 288, 28], [296, 25, 288, 31, "node"], [296, 29, 288, 35], [297, 12, 289, 16], [298, 10, 290, 12], [298, 11, 290, 13], [298, 17, 291, 17], [299, 12, 292, 16], [299, 16, 292, 20, "nodes"], [299, 22, 292, 25], [299, 25, 292, 28, "directory"], [299, 34, 292, 37], [299, 35, 292, 38, "files"], [299, 40, 292, 43], [299, 41, 292, 44, "get"], [299, 44, 292, 47], [299, 45, 292, 48, "route"], [299, 51, 292, 53], [299, 52, 292, 54], [300, 12, 293, 16], [300, 16, 293, 20], [300, 17, 293, 21, "nodes"], [300, 23, 293, 26], [300, 25, 293, 28], [301, 14, 294, 20, "nodes"], [301, 20, 294, 25], [301, 23, 294, 28], [301, 25, 294, 30], [302, 14, 295, 20, "directory"], [302, 23, 295, 29], [302, 24, 295, 30, "files"], [302, 29, 295, 35], [302, 30, 295, 36, "set"], [302, 33, 295, 39], [302, 34, 295, 40, "route"], [302, 40, 295, 45], [302, 42, 295, 47, "nodes"], [302, 48, 295, 52], [302, 49, 295, 53], [303, 12, 296, 16], [304, 12, 297, 16], [305, 0, 298, 0], [306, 0, 299, 0], [307, 0, 300, 0], [308, 0, 301, 0], [309, 0, 302, 0], [310, 12, 303, 16], [310, 16, 303, 22, "existing"], [310, 26, 303, 30], [310, 29, 303, 33, "nodes"], [310, 35, 303, 38], [310, 36, 303, 39, "meta"], [310, 40, 303, 43], [310, 41, 303, 44, "specificity"], [310, 52, 303, 55], [310, 53, 303, 56], [311, 12, 304, 16], [311, 16, 304, 20, "existing"], [311, 26, 304, 28], [311, 28, 304, 30], [312, 14, 305, 20], [313, 14, 306, 20], [313, 18, 306, 24, "process"], [313, 25, 306, 31], [313, 26, 306, 32, "env"], [313, 29, 306, 35], [313, 30, 306, 36, "NODE_ENV"], [313, 38, 306, 44], [313, 43, 306, 49], [313, 55, 306, 61], [313, 57, 306, 63], [314, 16, 307, 24], [314, 22, 307, 30], [314, 26, 307, 34, "Error"], [314, 31, 307, 39], [314, 32, 307, 40], [314, 52, 307, 60, "filePath"], [314, 60, 307, 68], [314, 70, 307, 78, "existing"], [314, 80, 307, 86], [314, 81, 307, 87, "<PERSON><PERSON>ey"], [314, 91, 307, 97], [314, 120, 307, 126, "route"], [314, 126, 307, 131], [314, 167, 307, 172], [314, 168, 307, 173], [315, 14, 308, 20], [316, 12, 309, 16], [316, 13, 309, 17], [316, 19, 310, 21], [317, 14, 311, 20, "hasRoutes"], [317, 23, 311, 29], [317, 28, 311, 34], [317, 32, 311, 38], [318, 14, 312, 20, "nodes"], [318, 20, 312, 25], [318, 21, 312, 26, "meta"], [318, 25, 312, 30], [318, 26, 312, 31, "specificity"], [318, 37, 312, 42], [318, 38, 312, 43], [318, 41, 312, 46, "node"], [318, 45, 312, 50], [319, 12, 313, 16], [320, 10, 314, 12], [321, 8, 315, 8], [322, 6, 316, 4], [322, 7, 316, 5], [323, 6, 316, 5, "_ret3"], [323, 11, 316, 5], [324, 4, 140, 4], [324, 9, 140, 9], [324, 13, 140, 15, "filePath"], [324, 21, 140, 23], [324, 25, 140, 27, "contextKeys"], [324, 36, 140, 38], [325, 6, 140, 38, "_ret3"], [325, 11, 140, 38], [325, 14, 140, 38, "_loop3"], [325, 20, 140, 38], [325, 21, 140, 38, "filePath"], [325, 29, 140, 38], [326, 6, 140, 38], [326, 10, 140, 38, "_ret3"], [326, 15, 140, 38], [326, 23, 142, 12], [327, 4, 142, 21], [328, 4, 317, 4], [329, 4, 318, 4], [329, 8, 318, 8], [329, 9, 318, 9, "<PERSON><PERSON><PERSON><PERSON>"], [329, 16, 318, 16], [329, 18, 318, 18], [330, 6, 319, 8], [330, 13, 319, 15], [330, 17, 319, 19], [331, 4, 320, 4], [332, 4, 321, 4], [333, 0, 322, 0], [334, 0, 323, 0], [335, 0, 324, 0], [336, 4, 325, 4], [336, 8, 325, 8], [336, 9, 325, 9, "rootDirectory"], [336, 22, 325, 22], [336, 23, 325, 23, "layout"], [336, 29, 325, 29], [336, 31, 325, 31], [337, 6, 326, 8, "rootDirectory"], [337, 19, 326, 21], [337, 20, 326, 22, "layout"], [337, 26, 326, 28], [337, 29, 326, 31], [337, 30, 327, 12, "options"], [337, 37, 327, 19], [337, 38, 327, 20, "getSystemRoute"], [337, 52, 327, 34], [337, 53, 327, 35], [338, 8, 328, 16, "type"], [338, 12, 328, 20], [338, 14, 328, 22], [338, 22, 328, 30], [339, 8, 329, 16, "route"], [339, 13, 329, 21], [339, 15, 329, 23], [340, 6, 330, 12], [340, 7, 330, 13], [340, 8, 330, 14], [340, 9, 331, 9], [341, 4, 332, 4], [342, 4, 333, 4], [343, 4, 334, 4], [343, 8, 334, 8], [343, 9, 334, 9, "options"], [343, 16, 334, 16], [343, 17, 334, 17, "skipGenerated"], [343, 30, 334, 30], [343, 32, 334, 32], [344, 6, 335, 8], [344, 10, 335, 12, "hasRoutes"], [344, 19, 335, 21], [344, 23, 335, 25, "options"], [344, 30, 335, 32], [344, 31, 335, 33, "sitemap"], [344, 38, 335, 40], [344, 43, 335, 45], [344, 48, 335, 50], [344, 50, 335, 52], [345, 8, 336, 12, "appendSitemapRoute"], [345, 26, 336, 30], [345, 27, 336, 31, "rootDirectory"], [345, 40, 336, 44], [345, 42, 336, 46, "options"], [345, 49, 336, 53], [345, 50, 336, 54], [346, 6, 337, 8], [347, 6, 338, 8], [347, 10, 338, 12, "options"], [347, 17, 338, 19], [347, 18, 338, 20, "notFound"], [347, 26, 338, 28], [347, 31, 338, 33], [347, 36, 338, 38], [347, 38, 338, 40], [348, 8, 339, 12, "appendNotFoundRoute"], [348, 27, 339, 31], [348, 28, 339, 32, "rootDirectory"], [348, 41, 339, 45], [348, 43, 339, 47, "options"], [348, 50, 339, 54], [348, 51, 339, 55], [349, 6, 340, 8], [350, 4, 341, 4], [351, 4, 342, 4], [351, 11, 342, 11, "rootDirectory"], [351, 24, 342, 24], [352, 2, 343, 0], [353, 2, 344, 0], [354, 0, 345, 0], [355, 0, 346, 0], [356, 2, 347, 0], [356, 11, 347, 9, "flattenDirectoryTreeToRoutes"], [356, 39, 347, 37, "flattenDirectoryTreeToRoutes"], [356, 40, 347, 38, "directory"], [356, 49, 347, 47], [356, 51, 347, 49, "options"], [356, 58, 347, 56], [356, 60, 348, 0], [357, 2, 349, 0, "layout"], [357, 8, 349, 6], [357, 10, 351, 19], [358, 4, 351, 19], [358, 8, 351, 0, "pathToRemove"], [358, 20, 351, 12], [358, 23, 351, 12, "arguments"], [358, 32, 351, 12], [358, 33, 351, 12, "length"], [358, 39, 351, 12], [358, 47, 351, 12, "arguments"], [358, 56, 351, 12], [358, 64, 351, 12, "undefined"], [358, 73, 351, 12], [358, 76, 351, 12, "arguments"], [358, 85, 351, 12], [358, 91, 351, 15], [358, 93, 351, 17], [359, 4, 352, 4], [360, 0, 353, 0], [361, 0, 354, 0], [362, 4, 355, 4], [362, 8, 355, 8, "directory"], [362, 17, 355, 17], [362, 18, 355, 18, "layout"], [362, 24, 355, 24], [362, 26, 355, 26], [363, 6, 356, 8], [363, 10, 356, 14, "previousLayout"], [363, 24, 356, 28], [363, 27, 356, 31, "layout"], [363, 33, 356, 37], [364, 6, 357, 8, "layout"], [364, 12, 357, 14], [364, 15, 357, 17, "getMostSpecific"], [364, 30, 357, 32], [364, 31, 357, 33, "directory"], [364, 40, 357, 42], [364, 41, 357, 43, "layout"], [364, 47, 357, 49], [364, 48, 357, 50], [365, 6, 358, 8], [366, 6, 359, 8], [366, 10, 359, 12, "previousLayout"], [366, 24, 359, 26], [366, 26, 359, 28], [367, 8, 360, 12, "previousLayout"], [367, 22, 360, 26], [367, 23, 360, 27, "children"], [367, 31, 360, 35], [367, 32, 360, 36, "push"], [367, 36, 360, 40], [367, 37, 360, 41, "layout"], [367, 43, 360, 47], [367, 44, 360, 48], [368, 6, 361, 8], [369, 6, 362, 8], [369, 10, 362, 12, "options"], [369, 17, 362, 19], [369, 18, 362, 20, "internal_stripLoadRoute"], [369, 41, 362, 43], [369, 43, 362, 45], [370, 8, 363, 12], [370, 15, 363, 19, "layout"], [370, 21, 363, 25], [370, 22, 363, 26, "loadRoute"], [370, 31, 363, 35], [371, 6, 364, 8], [372, 6, 365, 8], [373, 6, 366, 8], [373, 10, 366, 14, "newRoute"], [373, 18, 366, 22], [373, 21, 366, 25, "layout"], [373, 27, 366, 31], [373, 28, 366, 32, "route"], [373, 33, 366, 37], [373, 34, 366, 38, "replace"], [373, 41, 366, 45], [373, 42, 366, 46, "pathToRemove"], [373, 54, 366, 58], [373, 56, 366, 60], [373, 58, 366, 62], [373, 59, 366, 63], [374, 6, 367, 8, "pathToRemove"], [374, 18, 367, 20], [374, 21, 367, 23, "layout"], [374, 27, 367, 29], [374, 28, 367, 30, "route"], [374, 33, 367, 35], [374, 36, 367, 38], [374, 39, 367, 41, "layout"], [374, 45, 367, 47], [374, 46, 367, 48, "route"], [374, 51, 367, 53], [374, 54, 367, 56], [374, 57, 367, 59], [374, 59, 367, 61], [375, 6, 368, 8], [376, 6, 369, 8, "layout"], [376, 12, 369, 14], [376, 13, 369, 15, "route"], [376, 18, 369, 20], [376, 21, 369, 23, "newRoute"], [376, 29, 369, 31], [377, 6, 370, 8, "layout"], [377, 12, 370, 14], [377, 13, 370, 15, "dynamic"], [377, 20, 370, 22], [377, 23, 370, 25, "generateDynamic"], [377, 38, 370, 40], [377, 39, 370, 41, "layout"], [377, 45, 370, 47], [377, 46, 370, 48, "<PERSON><PERSON>ey"], [377, 56, 370, 58], [377, 57, 370, 59, "slice"], [377, 62, 370, 64], [377, 63, 370, 65], [377, 64, 370, 66], [377, 65, 370, 67], [377, 66, 370, 68], [378, 4, 371, 4], [379, 4, 372, 4], [380, 4, 373, 4], [380, 8, 373, 8], [380, 9, 373, 9, "layout"], [380, 15, 373, 15], [380, 17, 374, 8], [380, 23, 374, 14], [380, 27, 374, 18, "Error"], [380, 32, 374, 23], [380, 33, 374, 24], [380, 80, 374, 71], [380, 81, 374, 72], [381, 4, 375, 4], [381, 9, 375, 9], [381, 13, 375, 15, "routes"], [381, 19, 375, 21], [381, 23, 375, 25, "directory"], [381, 32, 375, 34], [381, 33, 375, 35, "files"], [381, 38, 375, 40], [381, 39, 375, 41, "values"], [381, 45, 375, 47], [381, 46, 375, 48], [381, 47, 375, 49], [381, 49, 375, 51], [382, 6, 376, 8], [382, 10, 376, 14, "routeNode"], [382, 19, 376, 23], [382, 22, 376, 26, "getMostSpecific"], [382, 37, 376, 41], [382, 38, 376, 42, "routes"], [382, 44, 376, 48], [382, 45, 376, 49], [383, 6, 377, 8], [384, 6, 378, 8, "routeNode"], [384, 15, 378, 17], [384, 16, 378, 18, "route"], [384, 21, 378, 23], [384, 24, 378, 26, "routeNode"], [384, 33, 378, 35], [384, 34, 378, 36, "route"], [384, 39, 378, 41], [384, 40, 378, 42, "replace"], [384, 47, 378, 49], [384, 48, 378, 50, "pathToRemove"], [384, 60, 378, 62], [384, 62, 378, 64], [384, 64, 378, 66], [384, 65, 378, 67], [385, 6, 379, 8, "routeNode"], [385, 15, 379, 17], [385, 16, 379, 18, "dynamic"], [385, 23, 379, 25], [385, 26, 379, 28, "generateDynamic"], [385, 41, 379, 43], [385, 42, 379, 44, "routeNode"], [385, 51, 379, 53], [385, 52, 379, 54, "route"], [385, 57, 379, 59], [385, 58, 379, 60], [386, 6, 380, 8], [386, 10, 380, 12, "options"], [386, 17, 380, 19], [386, 18, 380, 20, "internal_stripLoadRoute"], [386, 41, 380, 43], [386, 43, 380, 45], [387, 8, 381, 12], [387, 15, 381, 19, "routeNode"], [387, 24, 381, 28], [387, 25, 381, 29, "loadRoute"], [387, 34, 381, 38], [388, 6, 382, 8], [389, 6, 383, 8, "layout"], [389, 12, 383, 14], [389, 13, 383, 15, "children"], [389, 21, 383, 23], [389, 22, 383, 24, "push"], [389, 26, 383, 28], [389, 27, 383, 29, "routeNode"], [389, 36, 383, 38], [389, 37, 383, 39], [390, 4, 384, 4], [391, 4, 385, 4], [392, 4, 386, 4], [392, 9, 386, 9], [392, 13, 386, 15, "child"], [392, 18, 386, 20], [392, 22, 386, 24, "directory"], [392, 31, 386, 33], [392, 32, 386, 34, "subdirectories"], [392, 46, 386, 48], [392, 47, 386, 49, "values"], [392, 53, 386, 55], [392, 54, 386, 56], [392, 55, 386, 57], [392, 57, 386, 59], [393, 6, 387, 8, "flattenDirectoryTreeToRoutes"], [393, 34, 387, 36], [393, 35, 387, 37, "child"], [393, 40, 387, 42], [393, 42, 387, 44, "options"], [393, 49, 387, 51], [393, 51, 387, 53, "layout"], [393, 57, 387, 59], [393, 59, 387, 61, "pathToRemove"], [393, 71, 387, 73], [393, 72, 387, 74], [394, 4, 388, 4], [395, 4, 389, 4], [395, 11, 389, 11, "layout"], [395, 17, 389, 17], [396, 2, 390, 0], [397, 2, 391, 0], [397, 11, 391, 9, "getFileMeta"], [397, 22, 391, 20, "getFileMeta"], [397, 23, 391, 21, "original<PERSON>ey"], [397, 34, 391, 32], [397, 36, 391, 34, "options"], [397, 43, 391, 41], [397, 45, 391, 43, "redirects"], [397, 54, 391, 52], [397, 56, 391, 54, "rewrites"], [397, 64, 391, 62], [397, 66, 391, 64], [398, 4, 392, 4], [399, 4, 393, 4], [399, 8, 393, 10, "key"], [399, 11, 393, 13], [399, 14, 393, 16], [399, 15, 393, 17], [399, 16, 393, 18], [399, 18, 393, 20, "matchers_1"], [399, 28, 393, 30], [399, 29, 393, 31, "removeSupportedExtensions"], [399, 54, 393, 56], [399, 56, 393, 58], [399, 57, 393, 59], [399, 58, 393, 60], [399, 60, 393, 62, "matchers_1"], [399, 70, 393, 72], [399, 71, 393, 73, "removeFileSystemDots"], [399, 91, 393, 93], [399, 93, 393, 95, "original<PERSON>ey"], [399, 104, 393, 106], [399, 105, 393, 107], [399, 106, 393, 108], [400, 4, 394, 4], [400, 8, 394, 8, "route"], [400, 13, 394, 13], [400, 16, 394, 16, "key"], [400, 19, 394, 19], [401, 4, 395, 4], [401, 8, 395, 10, "parts"], [401, 13, 395, 15], [401, 16, 395, 18], [401, 17, 395, 19], [401, 18, 395, 20], [401, 20, 395, 22, "matchers_1"], [401, 30, 395, 32], [401, 31, 395, 33, "removeFileSystemDots"], [401, 51, 395, 53], [401, 53, 395, 55, "original<PERSON>ey"], [401, 64, 395, 66], [401, 65, 395, 67], [401, 66, 395, 68, "split"], [401, 71, 395, 73], [401, 72, 395, 74], [401, 75, 395, 77], [401, 76, 395, 78], [402, 4, 396, 4], [402, 8, 396, 10, "filename"], [402, 16, 396, 18], [402, 19, 396, 21, "parts"], [402, 24, 396, 26], [402, 25, 396, 27, "parts"], [402, 30, 396, 32], [402, 31, 396, 33, "length"], [402, 37, 396, 39], [402, 40, 396, 42], [402, 41, 396, 43], [402, 42, 396, 44], [403, 4, 397, 4], [403, 8, 397, 4, "_split"], [403, 14, 397, 4], [403, 17, 397, 59], [403, 18, 397, 60], [403, 19, 397, 61], [403, 21, 397, 63, "matchers_1"], [403, 31, 397, 73], [403, 32, 397, 74, "removeSupportedExtensions"], [403, 57, 397, 99], [403, 59, 397, 101, "filename"], [403, 67, 397, 109], [403, 68, 397, 110], [403, 69, 397, 111, "split"], [403, 74, 397, 116], [403, 75, 397, 117], [403, 78, 397, 120], [403, 79, 397, 121], [404, 6, 397, 121, "_split2"], [404, 13, 397, 121], [404, 16, 397, 121, "_slicedToArray"], [404, 30, 397, 121], [404, 31, 397, 121, "_split"], [404, 37, 397, 121], [405, 6, 397, 11, "filenameWithoutExtensions"], [405, 31, 397, 36], [405, 34, 397, 36, "_split2"], [405, 41, 397, 36], [406, 6, 397, 38, "platformExtension"], [406, 23, 397, 55], [406, 26, 397, 55, "_split2"], [406, 33, 397, 55], [407, 4, 398, 4], [407, 8, 398, 10, "isLayout"], [407, 16, 398, 18], [407, 19, 398, 21, "filenameWithoutExtensions"], [407, 44, 398, 46], [407, 49, 398, 51], [407, 58, 398, 60], [408, 4, 399, 4], [408, 8, 399, 10, "isApi"], [408, 13, 399, 15], [408, 16, 399, 18, "original<PERSON>ey"], [408, 27, 399, 29], [408, 28, 399, 30, "match"], [408, 33, 399, 35], [408, 34, 399, 36], [408, 59, 399, 61], [408, 60, 399, 62], [409, 4, 400, 4], [409, 8, 400, 8, "filenameWithoutExtensions"], [409, 33, 400, 33], [409, 34, 400, 34, "startsWith"], [409, 44, 400, 44], [409, 45, 400, 45], [409, 48, 400, 48], [409, 49, 400, 49], [409, 53, 400, 53, "filenameWithoutExtensions"], [409, 78, 400, 78], [409, 79, 400, 79, "endsWith"], [409, 87, 400, 87], [409, 88, 400, 88], [409, 91, 400, 91], [409, 92, 400, 92], [409, 94, 400, 94], [410, 6, 401, 8], [410, 12, 401, 14], [410, 16, 401, 18, "Error"], [410, 21, 401, 23], [410, 22, 401, 24], [410, 39, 401, 41, "original<PERSON>ey"], [410, 50, 401, 52], [410, 93, 401, 95], [410, 94, 401, 96], [411, 4, 402, 4], [412, 4, 403, 4], [413, 4, 404, 4], [413, 8, 404, 8], [413, 9, 404, 9, "isApi"], [413, 14, 404, 14], [413, 18, 404, 18, "filename"], [413, 26, 404, 26], [413, 27, 404, 27, "startsWith"], [413, 37, 404, 37], [413, 38, 404, 38], [413, 41, 404, 41], [413, 42, 404, 42], [413, 46, 404, 46, "filenameWithoutExtensions"], [413, 71, 404, 71], [413, 76, 404, 76], [413, 88, 404, 88], [413, 90, 404, 90], [414, 6, 405, 8], [414, 10, 405, 14, "renamedRoute"], [414, 22, 405, 26], [414, 25, 405, 29], [414, 26, 405, 30], [414, 29, 405, 33, "parts"], [414, 34, 405, 38], [414, 35, 405, 39, "slice"], [414, 40, 405, 44], [414, 41, 405, 45], [414, 42, 405, 46], [414, 44, 405, 48], [414, 45, 405, 49], [414, 46, 405, 50], [414, 47, 405, 51], [414, 49, 405, 53, "filename"], [414, 57, 405, 61], [414, 58, 405, 62, "slice"], [414, 63, 405, 67], [414, 64, 405, 68], [414, 65, 405, 69], [414, 66, 405, 70], [414, 67, 405, 71], [414, 68, 405, 72, "join"], [414, 72, 405, 76], [414, 73, 405, 77], [414, 76, 405, 80], [414, 77, 405, 81], [415, 6, 406, 8], [415, 12, 406, 14], [415, 16, 406, 18, "Error"], [415, 21, 406, 23], [415, 22, 406, 24], [415, 39, 406, 41, "original<PERSON>ey"], [415, 50, 406, 52], [415, 118, 406, 120, "renamedRoute"], [415, 130, 406, 132], [415, 133, 406, 135], [415, 134, 406, 136], [416, 4, 407, 4], [417, 4, 408, 4], [417, 8, 408, 8, "specificity"], [417, 19, 408, 19], [417, 22, 408, 22], [417, 23, 408, 23], [418, 4, 409, 4], [418, 8, 409, 10, "hasPlatformExtension"], [418, 28, 409, 30], [418, 31, 409, 33, "validPlatforms"], [418, 45, 409, 47], [418, 46, 409, 48, "has"], [418, 49, 409, 51], [418, 50, 409, 52, "platformExtension"], [418, 67, 409, 69], [418, 68, 409, 70], [419, 4, 410, 4], [419, 8, 410, 10, "usePlatformRoutes"], [419, 25, 410, 27], [419, 28, 410, 30, "options"], [419, 35, 410, 37], [419, 36, 410, 38, "platformRoutes"], [419, 50, 410, 52], [419, 54, 410, 56], [419, 58, 410, 60], [420, 4, 411, 4], [420, 8, 411, 8, "hasPlatformExtension"], [420, 28, 411, 28], [420, 30, 411, 30], [421, 6, 412, 8], [421, 10, 412, 12], [421, 11, 412, 13, "usePlatformRoutes"], [421, 28, 412, 30], [421, 30, 412, 32], [422, 8, 413, 12], [423, 8, 414, 12, "specificity"], [423, 19, 414, 23], [423, 22, 414, 26], [423, 23, 414, 27], [423, 24, 414, 28], [424, 6, 415, 8], [424, 7, 415, 9], [424, 13, 416, 13], [424, 17, 416, 17], [424, 18, 416, 18, "options"], [424, 25, 416, 25], [424, 26, 416, 26, "platform"], [424, 34, 416, 34], [424, 36, 416, 36], [425, 8, 417, 12], [426, 8, 418, 12], [427, 8, 419, 12, "specificity"], [427, 19, 419, 23], [427, 22, 419, 26], [427, 23, 419, 27], [427, 24, 419, 28], [428, 6, 420, 8], [428, 7, 420, 9], [428, 13, 421, 13], [428, 17, 421, 17, "platformExtension"], [428, 34, 421, 34], [428, 39, 421, 39, "options"], [428, 46, 421, 46], [428, 47, 421, 47, "platform"], [428, 55, 421, 55], [428, 57, 421, 57], [429, 8, 422, 12], [430, 8, 423, 12, "specificity"], [430, 19, 423, 23], [430, 22, 423, 26], [430, 23, 423, 27], [431, 6, 424, 8], [431, 7, 424, 9], [431, 13, 425, 13], [431, 17, 425, 17, "platformExtension"], [431, 34, 425, 34], [431, 39, 425, 39], [431, 47, 425, 47], [431, 51, 425, 51, "options"], [431, 58, 425, 58], [431, 59, 425, 59, "platform"], [431, 67, 425, 67], [431, 72, 425, 72], [431, 77, 425, 77], [431, 79, 425, 79], [432, 8, 426, 12], [433, 8, 427, 12, "specificity"], [433, 19, 427, 23], [433, 22, 427, 26], [433, 23, 427, 27], [434, 6, 428, 8], [434, 7, 428, 9], [434, 13, 429, 13], [434, 17, 429, 17, "platformExtension"], [434, 34, 429, 34], [434, 39, 429, 39, "options"], [434, 46, 429, 46], [434, 47, 429, 47, "platform"], [434, 55, 429, 55], [434, 57, 429, 57], [435, 8, 430, 12], [436, 8, 431, 12], [437, 8, 432, 12, "specificity"], [437, 19, 432, 23], [437, 22, 432, 26], [437, 23, 432, 27], [437, 24, 432, 28], [438, 6, 433, 8], [439, 6, 434, 8], [439, 10, 434, 12, "isApi"], [439, 15, 434, 17], [439, 19, 434, 21, "specificity"], [439, 30, 434, 32], [439, 35, 434, 37], [439, 36, 434, 38], [439, 38, 434, 40], [440, 8, 435, 12], [440, 14, 435, 18], [440, 18, 435, 22, "Error"], [440, 23, 435, 27], [440, 24, 435, 28], [440, 80, 435, 84, "platformExtension"], [440, 97, 435, 101], [440, 108, 435, 112, "original<PERSON>ey"], [440, 119, 435, 123], [440, 122, 435, 126], [440, 123, 435, 127], [441, 6, 436, 8], [442, 6, 437, 8, "route"], [442, 11, 437, 13], [442, 14, 437, 16, "route"], [442, 19, 437, 21], [442, 20, 437, 22, "replace"], [442, 27, 437, 29], [442, 28, 437, 30], [442, 32, 437, 34, "RegExp"], [442, 38, 437, 40], [442, 39, 437, 41], [442, 43, 437, 45, "platformExtension"], [442, 60, 437, 62], [442, 63, 437, 65], [442, 64, 437, 66], [442, 66, 437, 68], [442, 68, 437, 70], [442, 69, 437, 71], [443, 4, 438, 4], [444, 4, 439, 4], [444, 11, 439, 11], [445, 6, 440, 8, "route"], [445, 11, 440, 13], [446, 6, 441, 8, "specificity"], [446, 17, 441, 19], [447, 6, 442, 8, "isLayout"], [447, 14, 442, 16], [448, 6, 443, 8, "isApi"], [448, 11, 443, 13], [449, 6, 444, 8, "isRedirect"], [449, 16, 444, 18], [449, 18, 444, 20, "key"], [449, 21, 444, 23], [449, 25, 444, 27, "redirects"], [449, 34, 444, 36], [450, 6, 445, 8, "isRewrite"], [450, 15, 445, 17], [450, 17, 445, 19, "key"], [450, 20, 445, 22], [450, 24, 445, 26, "rewrites"], [451, 4, 446, 4], [451, 5, 446, 5], [452, 2, 447, 0], [453, 2, 448, 0], [453, 11, 448, 9, "getIgnoreList"], [453, 24, 448, 22, "getIgnoreList"], [453, 25, 448, 23, "options"], [453, 32, 448, 30], [453, 34, 448, 32], [454, 4, 449, 4], [454, 8, 449, 10, "ignore"], [454, 14, 449, 16], [454, 17, 449, 19], [454, 18, 449, 20], [454, 41, 449, 43], [454, 43, 449, 45], [454, 47, 449, 49, "options"], [454, 54, 449, 56], [454, 56, 449, 58, "ignore"], [454, 62, 449, 64], [454, 66, 449, 68], [454, 68, 449, 70], [454, 69, 449, 71], [454, 70, 449, 72], [455, 4, 450, 4], [455, 8, 450, 8, "options"], [455, 15, 450, 15], [455, 17, 450, 17, "preserveApiRoutes"], [455, 34, 450, 34], [455, 39, 450, 39], [455, 43, 450, 43], [455, 45, 450, 45], [456, 6, 451, 8, "ignore"], [456, 12, 451, 14], [456, 13, 451, 15, "push"], [456, 17, 451, 19], [456, 18, 451, 20], [456, 35, 451, 37], [456, 36, 451, 38], [457, 4, 452, 4], [458, 4, 453, 4], [458, 11, 453, 11, "ignore"], [458, 17, 453, 17], [459, 2, 454, 0], [460, 2, 455, 0], [461, 0, 456, 0], [462, 0, 457, 0], [463, 0, 458, 0], [464, 0, 459, 0], [465, 2, 460, 0], [465, 11, 460, 9, "extrapolateGroups"], [465, 28, 460, 26, "extrapolateGroups"], [465, 29, 460, 27, "key"], [465, 32, 460, 30], [465, 34, 460, 50], [466, 4, 460, 50], [466, 8, 460, 32, "keys"], [466, 12, 460, 36], [466, 15, 460, 36, "arguments"], [466, 24, 460, 36], [466, 25, 460, 36, "length"], [466, 31, 460, 36], [466, 39, 460, 36, "arguments"], [466, 48, 460, 36], [466, 56, 460, 36, "undefined"], [466, 65, 460, 36], [466, 68, 460, 36, "arguments"], [466, 77, 460, 36], [466, 83, 460, 39], [466, 87, 460, 43, "Set"], [466, 90, 460, 46], [466, 91, 460, 47], [466, 92, 460, 48], [467, 4, 461, 4], [467, 8, 461, 10, "match"], [467, 13, 461, 15], [467, 16, 461, 18], [467, 17, 461, 19], [467, 18, 461, 20], [467, 20, 461, 22, "matchers_1"], [467, 30, 461, 32], [467, 31, 461, 33, "matchArrayGroupName"], [467, 50, 461, 52], [467, 52, 461, 54, "key"], [467, 55, 461, 57], [467, 56, 461, 58], [468, 4, 462, 4], [468, 8, 462, 8], [468, 9, 462, 9, "match"], [468, 14, 462, 14], [468, 16, 462, 16], [469, 6, 463, 8, "keys"], [469, 10, 463, 12], [469, 11, 463, 13, "add"], [469, 14, 463, 16], [469, 15, 463, 17, "key"], [469, 18, 463, 20], [469, 19, 463, 21], [470, 6, 464, 8], [470, 13, 464, 15, "keys"], [470, 17, 464, 19], [471, 4, 465, 4], [472, 4, 466, 4], [472, 8, 466, 10, "groups"], [472, 14, 466, 16], [472, 17, 466, 19, "match"], [472, 22, 466, 24], [472, 23, 466, 25, "split"], [472, 28, 466, 30], [472, 29, 466, 31], [472, 32, 466, 34], [472, 33, 466, 35], [473, 4, 467, 4], [473, 8, 467, 10, "groupsSet"], [473, 17, 467, 19], [473, 20, 467, 22], [473, 24, 467, 26, "Set"], [473, 27, 467, 29], [473, 28, 467, 30, "groups"], [473, 34, 467, 36], [473, 35, 467, 37], [474, 4, 468, 4], [474, 8, 468, 8, "groupsSet"], [474, 17, 468, 17], [474, 18, 468, 18, "size"], [474, 22, 468, 22], [474, 27, 468, 27, "groups"], [474, 33, 468, 33], [474, 34, 468, 34, "length"], [474, 40, 468, 40], [474, 42, 468, 42], [475, 6, 469, 8], [475, 12, 469, 14], [475, 16, 469, 18, "Error"], [475, 21, 469, 23], [475, 22, 469, 24], [475, 75, 469, 77, "groups"], [475, 81, 469, 83], [475, 90, 469, 92, "key"], [475, 93, 469, 95], [475, 97, 469, 99], [475, 98, 469, 100], [476, 4, 470, 4], [477, 4, 471, 4], [477, 8, 471, 8, "groups"], [477, 14, 471, 14], [477, 15, 471, 15, "length"], [477, 21, 471, 21], [477, 26, 471, 26], [477, 27, 471, 27], [477, 29, 471, 29], [478, 6, 472, 8, "keys"], [478, 10, 472, 12], [478, 11, 472, 13, "add"], [478, 14, 472, 16], [478, 15, 472, 17, "key"], [478, 18, 472, 20], [478, 19, 472, 21], [479, 6, 473, 8], [479, 13, 473, 15, "keys"], [479, 17, 473, 19], [480, 4, 474, 4], [481, 4, 475, 4], [481, 9, 475, 9], [481, 13, 475, 15, "group"], [481, 18, 475, 20], [481, 22, 475, 24, "groups"], [481, 28, 475, 30], [481, 30, 475, 32], [482, 6, 476, 8, "extrapolateGroups"], [482, 23, 476, 25], [482, 24, 476, 26, "key"], [482, 27, 476, 29], [482, 28, 476, 30, "replace"], [482, 35, 476, 37], [482, 36, 476, 38, "match"], [482, 41, 476, 43], [482, 43, 476, 45, "group"], [482, 48, 476, 50], [482, 49, 476, 51, "trim"], [482, 53, 476, 55], [482, 54, 476, 56], [482, 55, 476, 57], [482, 56, 476, 58], [482, 58, 476, 60, "keys"], [482, 62, 476, 64], [482, 63, 476, 65], [483, 4, 477, 4], [484, 4, 478, 4], [484, 11, 478, 11, "keys"], [484, 15, 478, 15], [485, 2, 479, 0], [486, 2, 480, 0], [486, 11, 480, 9, "generateDynamic"], [486, 26, 480, 24, "generateDynamic"], [486, 27, 480, 25, "path"], [486, 31, 480, 29], [486, 33, 480, 31], [487, 4, 481, 4], [487, 8, 481, 10, "dynamic"], [487, 15, 481, 17], [487, 18, 481, 20, "path"], [487, 22, 481, 24], [487, 23, 482, 9, "split"], [487, 28, 482, 14], [487, 29, 482, 15], [487, 32, 482, 18], [487, 33, 482, 19], [487, 34, 483, 9, "map"], [487, 37, 483, 12], [487, 38, 483, 14, "part"], [487, 42, 483, 18], [487, 46, 483, 23], [488, 6, 484, 8], [488, 10, 484, 12, "part"], [488, 14, 484, 16], [488, 19, 484, 21], [488, 31, 484, 33], [488, 33, 484, 35], [489, 8, 485, 12], [489, 15, 485, 19], [490, 10, 486, 16, "name"], [490, 14, 486, 20], [490, 16, 486, 22], [490, 28, 486, 34], [491, 10, 487, 16, "deep"], [491, 14, 487, 20], [491, 16, 487, 22], [491, 20, 487, 26], [492, 10, 488, 16, "notFound"], [492, 18, 488, 24], [492, 20, 488, 26], [493, 8, 489, 12], [493, 9, 489, 13], [494, 6, 490, 8], [495, 6, 491, 8], [495, 13, 491, 15], [495, 14, 491, 16], [495, 15, 491, 17], [495, 17, 491, 19, "matchers_1"], [495, 27, 491, 29], [495, 28, 491, 30, "matchDynamicName"], [495, 44, 491, 46], [495, 46, 491, 48, "part"], [495, 50, 491, 52], [495, 51, 491, 53], [495, 55, 491, 57], [495, 59, 491, 61], [496, 4, 492, 4], [496, 5, 492, 5], [496, 6, 492, 6], [496, 7, 493, 9, "filter"], [496, 13, 493, 15], [496, 14, 493, 17, "part"], [496, 18, 493, 21], [496, 22, 493, 26], [496, 23, 493, 27], [496, 24, 493, 28, "part"], [496, 28, 493, 32], [496, 29, 493, 33], [497, 4, 494, 4], [497, 11, 494, 11, "dynamic"], [497, 18, 494, 18], [497, 19, 494, 19, "length"], [497, 25, 494, 25], [497, 30, 494, 30], [497, 31, 494, 31], [497, 34, 494, 34], [497, 38, 494, 38], [497, 41, 494, 41, "dynamic"], [497, 48, 494, 48], [498, 2, 495, 0], [499, 2, 496, 0], [499, 11, 496, 9, "appendSitemapRoute"], [499, 29, 496, 27, "appendSitemapRoute"], [499, 30, 496, 28, "directory"], [499, 39, 496, 37], [499, 41, 496, 39, "options"], [499, 48, 496, 46], [499, 50, 496, 48], [500, 4, 497, 4], [500, 8, 497, 8], [500, 9, 497, 9, "directory"], [500, 18, 497, 18], [500, 19, 497, 19, "files"], [500, 24, 497, 24], [500, 25, 497, 25, "has"], [500, 28, 497, 28], [500, 29, 497, 29], [500, 39, 497, 39], [500, 40, 497, 40], [500, 44, 497, 44, "options"], [500, 51, 497, 51], [500, 52, 497, 52, "getSystemRoute"], [500, 66, 497, 66], [500, 68, 497, 68], [501, 6, 498, 8, "directory"], [501, 15, 498, 17], [501, 16, 498, 18, "files"], [501, 21, 498, 23], [501, 22, 498, 24, "set"], [501, 25, 498, 27], [501, 26, 498, 28], [501, 36, 498, 38], [501, 38, 498, 40], [501, 39, 499, 12, "options"], [501, 46, 499, 19], [501, 47, 499, 20, "getSystemRoute"], [501, 61, 499, 34], [501, 62, 499, 35], [502, 8, 500, 16, "type"], [502, 12, 500, 20], [502, 14, 500, 22], [502, 21, 500, 29], [503, 8, 501, 16, "route"], [503, 13, 501, 21], [503, 15, 501, 23], [504, 6, 502, 12], [504, 7, 502, 13], [504, 8, 502, 14], [504, 9, 503, 9], [504, 10, 503, 10], [505, 4, 504, 4], [506, 2, 505, 0], [507, 2, 506, 0], [507, 11, 506, 9, "appendNotFoundRoute"], [507, 30, 506, 28, "appendNotFoundRoute"], [507, 31, 506, 29, "directory"], [507, 40, 506, 38], [507, 42, 506, 40, "options"], [507, 49, 506, 47], [507, 51, 506, 49], [508, 4, 507, 4], [508, 8, 507, 8], [508, 9, 507, 9, "directory"], [508, 18, 507, 18], [508, 19, 507, 19, "files"], [508, 24, 507, 24], [508, 25, 507, 25, "has"], [508, 28, 507, 28], [508, 29, 507, 29], [508, 41, 507, 41], [508, 42, 507, 42], [508, 46, 507, 46, "options"], [508, 53, 507, 53], [508, 54, 507, 54, "getSystemRoute"], [508, 68, 507, 68], [508, 70, 507, 70], [509, 6, 508, 8, "directory"], [509, 15, 508, 17], [509, 16, 508, 18, "files"], [509, 21, 508, 23], [509, 22, 508, 24, "set"], [509, 25, 508, 27], [509, 26, 508, 28], [509, 38, 508, 40], [509, 40, 508, 42], [509, 41, 509, 12, "options"], [509, 48, 509, 19], [509, 49, 509, 20, "getSystemRoute"], [509, 63, 509, 34], [509, 64, 509, 35], [510, 8, 510, 16, "type"], [510, 12, 510, 20], [510, 14, 510, 22], [510, 21, 510, 29], [511, 8, 511, 16, "route"], [511, 13, 511, 21], [511, 15, 511, 23], [512, 6, 512, 12], [512, 7, 512, 13], [512, 8, 512, 14], [512, 9, 513, 9], [512, 10, 513, 10], [513, 4, 514, 4], [514, 2, 515, 0], [515, 2, 516, 0], [515, 11, 516, 9, "getLayoutNode"], [515, 24, 516, 22, "getLayoutNode"], [515, 25, 516, 23, "node"], [515, 29, 516, 27], [515, 31, 516, 29, "options"], [515, 38, 516, 36], [515, 40, 516, 38], [516, 4, 517, 4], [517, 0, 518, 0], [518, 0, 519, 0], [519, 0, 520, 0], [520, 4, 521, 4], [521, 4, 522, 4], [521, 8, 522, 10, "groupName"], [521, 17, 522, 19], [521, 20, 522, 22], [521, 21, 522, 23], [521, 22, 522, 24], [521, 24, 522, 26, "matchers_1"], [521, 34, 522, 36], [521, 35, 522, 37, "matchLastGroupName"], [521, 53, 522, 55], [521, 55, 522, 57, "node"], [521, 59, 522, 61], [521, 60, 522, 62, "route"], [521, 65, 522, 67], [521, 66, 522, 68], [522, 4, 523, 4], [522, 8, 523, 10, "childMatchingGroup"], [522, 26, 523, 28], [522, 29, 523, 31, "node"], [522, 33, 523, 35], [522, 34, 523, 36, "children"], [522, 42, 523, 44], [522, 43, 523, 45, "find"], [522, 47, 523, 49], [522, 48, 523, 51, "child"], [522, 53, 523, 56], [522, 57, 523, 61], [523, 6, 524, 8], [523, 13, 524, 15, "child"], [523, 18, 524, 20], [523, 19, 524, 21, "route"], [523, 24, 524, 26], [523, 25, 524, 27, "replace"], [523, 32, 524, 34], [523, 33, 524, 35], [523, 43, 524, 45], [523, 45, 524, 47], [523, 47, 524, 49], [523, 48, 524, 50], [523, 53, 524, 55, "groupName"], [523, 62, 524, 64], [524, 4, 525, 4], [524, 5, 525, 5], [524, 6, 525, 6], [525, 4, 526, 4], [525, 8, 526, 8, "anchor"], [525, 14, 526, 14], [525, 17, 526, 17, "childMatchingGroup"], [525, 35, 526, 35], [525, 37, 526, 37, "route"], [525, 42, 526, 42], [526, 4, 527, 4], [526, 8, 527, 10, "loaded"], [526, 14, 527, 16], [526, 17, 527, 19, "node"], [526, 21, 527, 23], [526, 22, 527, 24, "loadRoute"], [526, 31, 527, 33], [526, 32, 527, 34], [526, 33, 527, 35], [527, 4, 528, 4], [527, 8, 528, 8, "loaded"], [527, 14, 528, 14], [527, 16, 528, 16, "unstable_settings"], [527, 33, 528, 33], [527, 35, 528, 35], [528, 6, 529, 8], [528, 10, 529, 12], [529, 8, 530, 12], [530, 8, 531, 12, "anchor"], [530, 14, 531, 18], [530, 17, 532, 16, "loaded"], [530, 23, 532, 22], [530, 24, 532, 23, "unstable_settings"], [530, 41, 532, 40], [530, 42, 532, 41, "anchor"], [530, 48, 532, 47], [530, 52, 532, 51, "loaded"], [530, 58, 532, 57], [530, 59, 532, 58, "unstable_settings"], [530, 76, 532, 75], [530, 77, 532, 76, "initialRouteName"], [530, 93, 532, 92], [530, 97, 532, 96, "anchor"], [530, 103, 532, 102], [531, 6, 533, 8], [531, 7, 533, 9], [531, 8, 534, 8], [531, 15, 534, 15, "error"], [531, 20, 534, 20], [531, 22, 534, 22], [532, 8, 535, 12], [532, 12, 535, 16, "error"], [532, 17, 535, 21], [532, 29, 535, 33, "Error"], [532, 34, 535, 38], [532, 36, 535, 40], [533, 10, 536, 16], [533, 14, 536, 20], [533, 15, 536, 21, "error"], [533, 20, 536, 26], [533, 21, 536, 27, "message"], [533, 28, 536, 34], [533, 29, 536, 35, "match"], [533, 34, 536, 40], [533, 35, 536, 41], [533, 72, 536, 78], [533, 73, 536, 79], [533, 75, 536, 81], [534, 12, 537, 20], [534, 18, 537, 26, "error"], [534, 23, 537, 31], [535, 10, 538, 16], [536, 8, 539, 12], [537, 6, 540, 8], [538, 6, 541, 8], [538, 10, 541, 12, "groupName"], [538, 19, 541, 21], [538, 21, 541, 23], [539, 8, 542, 12], [540, 8, 543, 12], [540, 12, 543, 18, "groupSpecificInitialRouteName"], [540, 41, 543, 47], [540, 44, 543, 50, "loaded"], [540, 50, 543, 56], [540, 51, 543, 57, "unstable_settings"], [540, 68, 543, 74], [540, 71, 543, 77, "groupName"], [540, 80, 543, 86], [540, 81, 543, 87], [540, 83, 543, 89, "anchor"], [540, 89, 543, 95], [540, 93, 544, 16, "loaded"], [540, 99, 544, 22], [540, 100, 544, 23, "unstable_settings"], [540, 117, 544, 40], [540, 120, 544, 43, "groupName"], [540, 129, 544, 52], [540, 130, 544, 53], [540, 132, 544, 55, "initialRouteName"], [540, 148, 544, 71], [541, 8, 545, 12, "anchor"], [541, 14, 545, 18], [541, 17, 545, 21, "groupSpecificInitialRouteName"], [541, 46, 545, 50], [541, 50, 545, 54, "anchor"], [541, 56, 545, 60], [542, 6, 546, 8], [543, 4, 547, 4], [544, 4, 548, 4], [544, 11, 548, 11], [545, 6, 549, 8], [545, 9, 549, 11, "node"], [545, 13, 549, 15], [546, 6, 550, 8, "route"], [546, 11, 550, 13], [546, 13, 550, 15, "node"], [546, 17, 550, 19], [546, 18, 550, 20, "route"], [546, 23, 550, 25], [546, 24, 550, 26, "replace"], [546, 31, 550, 33], [546, 32, 550, 34], [546, 45, 550, 47], [546, 47, 550, 49], [546, 49, 550, 51], [546, 50, 550, 52], [547, 6, 551, 8, "children"], [547, 14, 551, 16], [547, 16, 551, 18], [547, 18, 551, 20], [548, 6, 551, 22], [549, 6, 552, 8, "initialRouteName"], [549, 22, 552, 24], [549, 24, 552, 26, "anchor"], [550, 4, 553, 4], [550, 5, 553, 5], [551, 2, 554, 0], [552, 2, 555, 0], [552, 11, 555, 9, "crawlAndAppendInitialRoutesAndEntryFiles"], [552, 51, 555, 49, "crawlAndAppendInitialRoutesAndEntryFiles"], [552, 52, 555, 50, "node"], [552, 56, 555, 54], [552, 58, 555, 56, "options"], [552, 65, 555, 63], [552, 67, 555, 83], [553, 4, 555, 83], [553, 8, 555, 65, "entryPoints"], [553, 19, 555, 76], [553, 22, 555, 76, "arguments"], [553, 31, 555, 76], [553, 32, 555, 76, "length"], [553, 38, 555, 76], [553, 46, 555, 76, "arguments"], [553, 55, 555, 76], [553, 63, 555, 76, "undefined"], [553, 72, 555, 76], [553, 75, 555, 76, "arguments"], [553, 84, 555, 76], [553, 90, 555, 79], [553, 92, 555, 81], [554, 4, 556, 4], [554, 8, 556, 8, "node"], [554, 12, 556, 12], [554, 13, 556, 13, "type"], [554, 17, 556, 17], [554, 22, 556, 22], [554, 29, 556, 29], [554, 31, 556, 31], [555, 6, 557, 8, "node"], [555, 10, 557, 12], [555, 11, 557, 13, "entryPoints"], [555, 22, 557, 24], [555, 25, 557, 27], [555, 26, 557, 28], [555, 29, 557, 31], [555, 33, 557, 35, "Set"], [555, 36, 557, 38], [555, 37, 557, 39], [555, 38, 557, 40], [555, 41, 557, 43, "entryPoints"], [555, 52, 557, 54], [555, 54, 557, 56, "node"], [555, 58, 557, 60], [555, 59, 557, 61, "<PERSON><PERSON>ey"], [555, 69, 557, 71], [555, 70, 557, 72], [555, 71, 557, 73], [555, 72, 557, 74], [556, 4, 558, 4], [556, 5, 558, 5], [556, 11, 559, 9], [556, 15, 559, 13, "node"], [556, 19, 559, 17], [556, 20, 559, 18, "type"], [556, 24, 559, 22], [556, 29, 559, 27], [556, 39, 559, 37], [556, 41, 559, 39], [557, 6, 560, 8, "node"], [557, 10, 560, 12], [557, 11, 560, 13, "entryPoints"], [557, 22, 560, 24], [557, 25, 560, 27], [557, 26, 560, 28], [557, 29, 560, 31], [557, 33, 560, 35, "Set"], [557, 36, 560, 38], [557, 37, 560, 39], [557, 38, 560, 40], [557, 41, 560, 43, "entryPoints"], [557, 52, 560, 54], [557, 54, 560, 56, "node"], [557, 58, 560, 60], [557, 59, 560, 61, "destinationContextKey"], [557, 80, 560, 82], [557, 81, 560, 83], [557, 82, 560, 84], [557, 83, 560, 85], [558, 4, 561, 4], [558, 5, 561, 5], [558, 11, 562, 9], [558, 15, 562, 13, "node"], [558, 19, 562, 17], [558, 20, 562, 18, "type"], [558, 24, 562, 22], [558, 29, 562, 27], [558, 37, 562, 35], [558, 39, 562, 37], [559, 6, 563, 8], [559, 10, 563, 12], [559, 11, 563, 13, "node"], [559, 15, 563, 17], [559, 16, 563, 18, "children"], [559, 24, 563, 26], [559, 26, 563, 28], [560, 8, 564, 12], [560, 14, 564, 18], [560, 18, 564, 22, "Error"], [560, 23, 564, 27], [560, 24, 564, 28], [560, 35, 564, 39, "node"], [560, 39, 564, 43], [560, 40, 564, 44, "<PERSON><PERSON>ey"], [560, 50, 564, 54], [560, 87, 564, 91], [560, 88, 564, 92], [561, 6, 565, 8], [562, 6, 566, 8], [563, 6, 567, 8, "entryPoints"], [563, 17, 567, 19], [563, 20, 567, 22], [563, 21, 567, 23], [563, 24, 567, 26, "entryPoints"], [563, 35, 567, 37], [563, 37, 567, 39, "node"], [563, 41, 567, 43], [563, 42, 567, 44, "<PERSON><PERSON>ey"], [563, 52, 567, 54], [563, 53, 567, 55], [564, 6, 568, 8], [565, 0, 569, 0], [566, 0, 570, 0], [567, 0, 571, 0], [568, 0, 572, 0], [569, 0, 573, 0], [570, 6, 574, 8], [570, 10, 574, 14, "groupName"], [570, 19, 574, 23], [570, 22, 574, 26], [570, 23, 574, 27], [570, 24, 574, 28], [570, 26, 574, 30, "matchers_1"], [570, 36, 574, 40], [570, 37, 574, 41, "matchGroupName"], [570, 51, 574, 55], [570, 53, 574, 57, "node"], [570, 57, 574, 61], [570, 58, 574, 62, "route"], [570, 63, 574, 67], [570, 64, 574, 68], [571, 6, 575, 8], [571, 10, 575, 14, "childMatchingGroup"], [571, 28, 575, 32], [571, 31, 575, 35, "node"], [571, 35, 575, 39], [571, 36, 575, 40, "children"], [571, 44, 575, 48], [571, 45, 575, 49, "find"], [571, 49, 575, 53], [571, 50, 575, 55, "child"], [571, 55, 575, 60], [571, 59, 575, 65], [572, 8, 576, 12], [572, 15, 576, 19, "child"], [572, 20, 576, 24], [572, 21, 576, 25, "route"], [572, 26, 576, 30], [572, 27, 576, 31, "replace"], [572, 34, 576, 38], [572, 35, 576, 39], [572, 45, 576, 49], [572, 47, 576, 51], [572, 49, 576, 53], [572, 50, 576, 54], [572, 55, 576, 59, "groupName"], [572, 64, 576, 68], [573, 6, 577, 8], [573, 7, 577, 9], [573, 8, 577, 10], [574, 6, 578, 8], [574, 10, 578, 12, "anchor"], [574, 16, 578, 18], [574, 19, 578, 21, "childMatchingGroup"], [574, 37, 578, 39], [574, 39, 578, 41, "route"], [574, 44, 578, 46], [575, 6, 579, 8], [576, 6, 580, 8], [576, 10, 580, 12], [576, 11, 580, 13, "options"], [576, 18, 580, 20], [576, 19, 580, 21, "internal_stripLoadRoute"], [576, 42, 580, 44], [576, 44, 580, 46], [577, 8, 581, 12], [577, 12, 581, 18, "loaded"], [577, 18, 581, 24], [577, 21, 581, 27, "node"], [577, 25, 581, 31], [577, 26, 581, 32, "loadRoute"], [577, 35, 581, 41], [577, 36, 581, 42], [577, 37, 581, 43], [578, 8, 582, 12], [578, 12, 582, 16, "loaded"], [578, 18, 582, 22], [578, 20, 582, 24, "unstable_settings"], [578, 37, 582, 41], [578, 39, 582, 43], [579, 10, 583, 16], [579, 14, 583, 20], [580, 12, 584, 20], [581, 12, 585, 20, "anchor"], [581, 18, 585, 26], [581, 21, 586, 24, "loaded"], [581, 27, 586, 30], [581, 28, 586, 31, "unstable_settings"], [581, 45, 586, 48], [581, 46, 586, 49, "anchor"], [581, 52, 586, 55], [581, 56, 586, 59, "loaded"], [581, 62, 586, 65], [581, 63, 586, 66, "unstable_settings"], [581, 80, 586, 83], [581, 81, 586, 84, "initialRouteName"], [581, 97, 586, 100], [581, 101, 586, 104, "anchor"], [581, 107, 586, 110], [582, 10, 587, 16], [582, 11, 587, 17], [582, 12, 588, 16], [582, 19, 588, 23, "error"], [582, 24, 588, 28], [582, 26, 588, 30], [583, 12, 589, 20], [583, 16, 589, 24, "error"], [583, 21, 589, 29], [583, 33, 589, 41, "Error"], [583, 38, 589, 46], [583, 40, 589, 48], [584, 14, 590, 24], [584, 18, 590, 28], [584, 19, 590, 29, "error"], [584, 24, 590, 34], [584, 25, 590, 35, "message"], [584, 32, 590, 42], [584, 33, 590, 43, "match"], [584, 38, 590, 48], [584, 39, 590, 49], [584, 76, 590, 86], [584, 77, 590, 87], [584, 79, 590, 89], [585, 16, 591, 28], [585, 22, 591, 34, "error"], [585, 27, 591, 39], [586, 14, 592, 24], [587, 12, 593, 20], [588, 10, 594, 16], [589, 10, 595, 16], [589, 14, 595, 20, "groupName"], [589, 23, 595, 29], [589, 25, 595, 31], [590, 12, 596, 20], [591, 12, 597, 20], [591, 16, 597, 26, "groupSpecificInitialRouteName"], [591, 45, 597, 55], [591, 48, 597, 58, "loaded"], [591, 54, 597, 64], [591, 55, 597, 65, "unstable_settings"], [591, 72, 597, 82], [591, 75, 597, 85, "groupName"], [591, 84, 597, 94], [591, 85, 597, 95], [591, 87, 597, 97, "anchor"], [591, 93, 597, 103], [591, 97, 598, 24, "loaded"], [591, 103, 598, 30], [591, 104, 598, 31, "unstable_settings"], [591, 121, 598, 48], [591, 124, 598, 51, "groupName"], [591, 133, 598, 60], [591, 134, 598, 61], [591, 136, 598, 63, "initialRouteName"], [591, 152, 598, 79], [592, 12, 599, 20, "anchor"], [592, 18, 599, 26], [592, 21, 599, 29, "groupSpecificInitialRouteName"], [592, 50, 599, 58], [592, 54, 599, 62, "anchor"], [592, 60, 599, 68], [593, 10, 600, 16], [594, 8, 601, 12], [595, 6, 602, 8], [596, 6, 603, 8], [596, 10, 603, 12, "anchor"], [596, 16, 603, 18], [596, 18, 603, 20], [597, 8, 604, 12], [597, 12, 604, 18, "anchorRoute"], [597, 23, 604, 29], [597, 26, 604, 32, "node"], [597, 30, 604, 36], [597, 31, 604, 37, "children"], [597, 39, 604, 45], [597, 40, 604, 46, "find"], [597, 44, 604, 50], [597, 45, 604, 52, "child"], [597, 50, 604, 57], [597, 54, 604, 62, "child"], [597, 59, 604, 67], [597, 60, 604, 68, "route"], [597, 65, 604, 73], [597, 70, 604, 78, "anchor"], [597, 76, 604, 84], [597, 77, 604, 85], [598, 8, 605, 12], [598, 12, 605, 16], [598, 13, 605, 17, "anchorRoute"], [598, 24, 605, 28], [598, 26, 605, 30], [599, 10, 606, 16], [599, 14, 606, 22, "validAnchorRoutes"], [599, 31, 606, 39], [599, 34, 606, 42, "node"], [599, 38, 606, 46], [599, 39, 606, 47, "children"], [599, 47, 606, 55], [599, 48, 607, 21, "filter"], [599, 54, 607, 27], [599, 55, 607, 29, "child"], [599, 60, 607, 34], [599, 64, 607, 39], [599, 65, 607, 40, "child"], [599, 70, 607, 45], [599, 71, 607, 46, "generated"], [599, 80, 607, 55], [599, 81, 607, 56], [599, 82, 608, 21, "map"], [599, 85, 608, 24], [599, 86, 608, 26, "child"], [599, 91, 608, 31], [599, 95, 608, 36], [599, 99, 608, 40, "child"], [599, 104, 608, 45], [599, 105, 608, 46, "route"], [599, 110, 608, 51], [599, 113, 608, 54], [599, 114, 608, 55], [599, 115, 609, 21, "join"], [599, 119, 609, 25], [599, 120, 609, 26], [599, 124, 609, 30], [599, 125, 609, 31], [600, 10, 610, 16], [600, 14, 610, 20, "groupName"], [600, 23, 610, 29], [600, 25, 610, 31], [601, 12, 611, 20], [601, 18, 611, 26], [601, 22, 611, 30, "Error"], [601, 27, 611, 35], [601, 28, 611, 36], [601, 38, 611, 46, "node"], [601, 42, 611, 50], [601, 43, 611, 51, "<PERSON><PERSON>ey"], [601, 53, 611, 61], [601, 77, 611, 85, "anchor"], [601, 83, 611, 91], [601, 100, 611, 108, "groupName"], [601, 109, 611, 117], [601, 135, 611, 143, "validAnchorRoutes"], [601, 152, 611, 160], [601, 154, 611, 162], [601, 155, 611, 163], [602, 10, 612, 16], [602, 11, 612, 17], [602, 17, 613, 21], [603, 12, 614, 20], [603, 18, 614, 26], [603, 22, 614, 30, "Error"], [603, 27, 614, 35], [603, 28, 614, 36], [603, 38, 614, 46, "node"], [603, 42, 614, 50], [603, 43, 614, 51, "<PERSON><PERSON>ey"], [603, 53, 614, 61], [603, 77, 614, 85, "anchor"], [603, 83, 614, 91], [603, 108, 614, 116, "validAnchorRoutes"], [603, 125, 614, 133], [603, 127, 614, 135], [603, 128, 614, 136], [604, 10, 615, 16], [605, 8, 616, 12], [606, 8, 617, 12], [607, 8, 618, 12, "node"], [607, 12, 618, 16], [607, 13, 618, 17, "initialRouteName"], [607, 29, 618, 33], [607, 32, 618, 36, "anchor"], [607, 38, 618, 42], [608, 8, 619, 12, "entryPoints"], [608, 19, 619, 23], [608, 20, 619, 24, "push"], [608, 24, 619, 28], [608, 25, 619, 29, "anchorRoute"], [608, 36, 619, 40], [608, 37, 619, 41, "<PERSON><PERSON>ey"], [608, 47, 619, 51], [608, 48, 619, 52], [609, 6, 620, 8], [610, 6, 621, 8], [610, 11, 621, 13], [610, 15, 621, 19, "child"], [610, 20, 621, 24], [610, 24, 621, 28, "node"], [610, 28, 621, 32], [610, 29, 621, 33, "children"], [610, 37, 621, 41], [610, 39, 621, 43], [611, 8, 622, 12, "crawlAndAppendInitialRoutesAndEntryFiles"], [611, 48, 622, 52], [611, 49, 622, 53, "child"], [611, 54, 622, 58], [611, 56, 622, 60, "options"], [611, 63, 622, 67], [611, 65, 622, 69, "entryPoints"], [611, 76, 622, 80], [611, 77, 622, 81], [612, 6, 623, 8], [613, 4, 624, 4], [614, 2, 625, 0], [615, 2, 626, 0], [615, 11, 626, 9, "getMostSpecific"], [615, 26, 626, 24, "getMostSpecific"], [615, 27, 626, 25, "routes"], [615, 33, 626, 31], [615, 35, 626, 33], [616, 4, 627, 4], [616, 8, 627, 10, "route"], [616, 13, 627, 15], [616, 16, 627, 18, "routes"], [616, 22, 627, 24], [616, 23, 627, 25, "routes"], [616, 29, 627, 31], [616, 30, 627, 32, "length"], [616, 36, 627, 38], [616, 39, 627, 41], [616, 40, 627, 42], [616, 41, 627, 43], [617, 4, 628, 4], [617, 8, 628, 8], [617, 9, 628, 9, "routes"], [617, 15, 628, 15], [617, 16, 628, 16], [617, 17, 628, 17], [617, 18, 628, 18], [617, 20, 628, 20], [618, 6, 629, 8], [618, 12, 629, 14], [618, 16, 629, 18, "Error"], [618, 21, 629, 23], [618, 22, 629, 24], [618, 34, 629, 36, "route"], [618, 39, 629, 41], [618, 40, 629, 42, "<PERSON><PERSON>ey"], [618, 50, 629, 52], [618, 120, 629, 122], [618, 121, 629, 123], [619, 4, 630, 4], [620, 4, 631, 4], [621, 4, 632, 4], [622, 4, 633, 4], [622, 11, 633, 11, "routes"], [622, 17, 633, 17], [622, 18, 633, 18, "routes"], [622, 24, 633, 24], [622, 25, 633, 25, "length"], [622, 31, 633, 31], [622, 34, 633, 34], [622, 35, 633, 35], [622, 36, 633, 36], [623, 2, 634, 0], [624, 0, 634, 1], [624, 3]], "functionMap": {"names": ["<global>", "getRoutes", "getDirectoryTree", "ignoreList.some$argument_0", "contextKeys.map$argument_0", "validRedirectDestinations.find$argument_0", "node.loadRoute", "flattenDirectoryTreeToRoutes", "getFileMeta", "getIgnoreList", "extrapolateGroups", "generateDynamic", "path.split.map$argument_0", "path.split.map.filter$argument_0", "appendSitemapRoute", "appendNotFoundRoute", "getLayoutNode", "node.children.find$argument_0", "crawlAndAppendInitialRoutesAndEntryFiles", "node.children.filter$argument_0", "node.children.filter.map$argument_0", "getMostSpecific"], "mappings": "AAA;ACqB;CDW;AEI;oCC+B,uCD;8DEI;iBFK;qDGG,qCH;oCC8B,uCD;8DEI;iBFK;mEGC,qCH;4BCqB,+BD;YIW;aJ+B;CFgK;AOI;CP2C;AQC;CRwD;ASC;CTM;AUM;CVmB;AWC;aCG;KDS;gBEC,gBF;CXE;AcC;CdS;AeC;CfS;AgBC;kDCO;KDE;ChB6B;AkBC;sDDoB;SCE;mDD2B,iCC;4BCG,2BD;yBEC,6BF;ClBiB;AqBC;CrBQ"}}, "type": "js/module"}]}