{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createSquare = createSquare;\n  exports.normalizeRect = normalizeRect;\n  function createSquare(size) {\n    return {\n      bottom: size,\n      left: size,\n      right: size,\n      top: size\n    };\n  }\n  function normalizeRect(rectOrSize) {\n    return typeof rectOrSize === 'number' ? createSquare(rectOrSize) : rectOrSize;\n  }\n});", "lineCount": 18, "map": [[7, 2, 20, 7], [7, 11, 20, 16, "createSquare"], [7, 23, 20, 28, "createSquare"], [7, 24, 20, 29, "size"], [7, 28, 20, 41], [7, 30, 20, 49], [8, 4, 21, 2], [8, 11, 21, 9], [9, 6, 21, 10, "bottom"], [9, 12, 21, 16], [9, 14, 21, 18, "size"], [9, 18, 21, 22], [10, 6, 21, 24, "left"], [10, 10, 21, 28], [10, 12, 21, 30, "size"], [10, 16, 21, 34], [11, 6, 21, 36, "right"], [11, 11, 21, 41], [11, 13, 21, 43, "size"], [11, 17, 21, 47], [12, 6, 21, 49, "top"], [12, 9, 21, 52], [12, 11, 21, 54, "size"], [13, 4, 21, 58], [13, 5, 21, 59], [14, 2, 22, 0], [15, 2, 24, 7], [15, 11, 24, 16, "normalizeRect"], [15, 24, 24, 29, "normalizeRect"], [15, 25, 24, 30, "rectOrSize"], [15, 35, 24, 53], [15, 37, 24, 62], [16, 4, 25, 2], [16, 11, 25, 9], [16, 18, 25, 16, "rectOrSize"], [16, 28, 25, 26], [16, 33, 25, 31], [16, 41, 25, 39], [16, 44, 25, 42, "createSquare"], [16, 56, 25, 54], [16, 57, 25, 55, "rectOrSize"], [16, 67, 25, 65], [16, 68, 25, 66], [16, 71, 25, 69, "rectOrSize"], [16, 81, 25, 79], [17, 2, 26, 0], [18, 0, 26, 1], [18, 3]], "functionMap": {"names": ["<global>", "createSquare", "normalizeRect"], "mappings": "AAA;OCmB;CDE;OEE"}}, "type": "js/module"}]}