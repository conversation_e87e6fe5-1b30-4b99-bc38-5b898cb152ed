{"dependencies": [{"name": "../../../src/private/specs_DEPRECATED/modules/NativeStatusBarManagerIOS", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 88}}], "key": "Zv5L3uPO+KQSOpEjMQ2LtpU8B2w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _NativeStatusBarManagerIOS = _interopRequireWildcard(require(_dependencyMap[0], \"../../../src/private/specs_DEPRECATED/modules/NativeStatusBarManagerIOS\"));\n  Object.keys(_NativeStatusBarManagerIOS).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _NativeStatusBarManagerIOS[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _NativeStatusBarManagerIOS[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _NativeStatusBarManagerIOS.default;\n});", "lineCount": 21, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeStatusBarManagerIOS"], [7, 32, 11, 0], [7, 35, 11, 0, "_interopRequireWildcard"], [7, 58, 11, 0], [7, 59, 11, 0, "require"], [7, 66, 11, 0], [7, 67, 11, 0, "_dependencyMap"], [7, 81, 11, 0], [8, 2, 11, 0, "Object"], [8, 8, 11, 0], [8, 9, 11, 0, "keys"], [8, 13, 11, 0], [8, 14, 11, 0, "_NativeStatusBarManagerIOS"], [8, 40, 11, 0], [8, 42, 11, 0, "for<PERSON>ach"], [8, 49, 11, 0], [8, 60, 11, 0, "key"], [8, 63, 11, 0], [9, 4, 11, 0], [9, 8, 11, 0, "key"], [9, 11, 11, 0], [9, 29, 11, 0, "key"], [9, 32, 11, 0], [10, 4, 11, 0], [10, 8, 11, 0, "Object"], [10, 14, 11, 0], [10, 15, 11, 0, "prototype"], [10, 24, 11, 0], [10, 25, 11, 0, "hasOwnProperty"], [10, 39, 11, 0], [10, 40, 11, 0, "call"], [10, 44, 11, 0], [10, 45, 11, 0, "_exportNames"], [10, 57, 11, 0], [10, 59, 11, 0, "key"], [10, 62, 11, 0], [11, 4, 11, 0], [11, 8, 11, 0, "key"], [11, 11, 11, 0], [11, 15, 11, 0, "exports"], [11, 22, 11, 0], [11, 26, 11, 0, "exports"], [11, 33, 11, 0], [11, 34, 11, 0, "key"], [11, 37, 11, 0], [11, 43, 11, 0, "_NativeStatusBarManagerIOS"], [11, 69, 11, 0], [11, 70, 11, 0, "key"], [11, 73, 11, 0], [12, 4, 11, 0, "Object"], [12, 10, 11, 0], [12, 11, 11, 0, "defineProperty"], [12, 25, 11, 0], [12, 26, 11, 0, "exports"], [12, 33, 11, 0], [12, 35, 11, 0, "key"], [12, 38, 11, 0], [13, 6, 11, 0, "enumerable"], [13, 16, 11, 0], [14, 6, 11, 0, "get"], [14, 9, 11, 0], [14, 20, 11, 0, "get"], [14, 21, 11, 0], [15, 8, 11, 0], [15, 15, 11, 0, "_NativeStatusBarManagerIOS"], [15, 41, 11, 0], [15, 42, 11, 0, "key"], [15, 45, 11, 0], [16, 6, 11, 0], [17, 4, 11, 0], [18, 2, 11, 0], [19, 2, 11, 88], [19, 11, 11, 88, "_interopRequireWildcard"], [19, 35, 11, 88, "e"], [19, 36, 11, 88], [19, 38, 11, 88, "t"], [19, 39, 11, 88], [19, 68, 11, 88, "WeakMap"], [19, 75, 11, 88], [19, 81, 11, 88, "r"], [19, 82, 11, 88], [19, 89, 11, 88, "WeakMap"], [19, 96, 11, 88], [19, 100, 11, 88, "n"], [19, 101, 11, 88], [19, 108, 11, 88, "WeakMap"], [19, 115, 11, 88], [19, 127, 11, 88, "_interopRequireWildcard"], [19, 150, 11, 88], [19, 162, 11, 88, "_interopRequireWildcard"], [19, 163, 11, 88, "e"], [19, 164, 11, 88], [19, 166, 11, 88, "t"], [19, 167, 11, 88], [19, 176, 11, 88, "t"], [19, 177, 11, 88], [19, 181, 11, 88, "e"], [19, 182, 11, 88], [19, 186, 11, 88, "e"], [19, 187, 11, 88], [19, 188, 11, 88, "__esModule"], [19, 198, 11, 88], [19, 207, 11, 88, "e"], [19, 208, 11, 88], [19, 214, 11, 88, "o"], [19, 215, 11, 88], [19, 217, 11, 88, "i"], [19, 218, 11, 88], [19, 220, 11, 88, "f"], [19, 221, 11, 88], [19, 226, 11, 88, "__proto__"], [19, 235, 11, 88], [19, 243, 11, 88, "default"], [19, 250, 11, 88], [19, 252, 11, 88, "e"], [19, 253, 11, 88], [19, 270, 11, 88, "e"], [19, 271, 11, 88], [19, 294, 11, 88, "e"], [19, 295, 11, 88], [19, 320, 11, 88, "e"], [19, 321, 11, 88], [19, 330, 11, 88, "f"], [19, 331, 11, 88], [19, 337, 11, 88, "o"], [19, 338, 11, 88], [19, 341, 11, 88, "t"], [19, 342, 11, 88], [19, 345, 11, 88, "n"], [19, 346, 11, 88], [19, 349, 11, 88, "r"], [19, 350, 11, 88], [19, 358, 11, 88, "o"], [19, 359, 11, 88], [19, 360, 11, 88, "has"], [19, 363, 11, 88], [19, 364, 11, 88, "e"], [19, 365, 11, 88], [19, 375, 11, 88, "o"], [19, 376, 11, 88], [19, 377, 11, 88, "get"], [19, 380, 11, 88], [19, 381, 11, 88, "e"], [19, 382, 11, 88], [19, 385, 11, 88, "o"], [19, 386, 11, 88], [19, 387, 11, 88, "set"], [19, 390, 11, 88], [19, 391, 11, 88, "e"], [19, 392, 11, 88], [19, 394, 11, 88, "f"], [19, 395, 11, 88], [19, 409, 11, 88, "_t"], [19, 411, 11, 88], [19, 415, 11, 88, "e"], [19, 416, 11, 88], [19, 432, 11, 88, "_t"], [19, 434, 11, 88], [19, 441, 11, 88, "hasOwnProperty"], [19, 455, 11, 88], [19, 456, 11, 88, "call"], [19, 460, 11, 88], [19, 461, 11, 88, "e"], [19, 462, 11, 88], [19, 464, 11, 88, "_t"], [19, 466, 11, 88], [19, 473, 11, 88, "i"], [19, 474, 11, 88], [19, 478, 11, 88, "o"], [19, 479, 11, 88], [19, 482, 11, 88, "Object"], [19, 488, 11, 88], [19, 489, 11, 88, "defineProperty"], [19, 503, 11, 88], [19, 508, 11, 88, "Object"], [19, 514, 11, 88], [19, 515, 11, 88, "getOwnPropertyDescriptor"], [19, 539, 11, 88], [19, 540, 11, 88, "e"], [19, 541, 11, 88], [19, 543, 11, 88, "_t"], [19, 545, 11, 88], [19, 552, 11, 88, "i"], [19, 553, 11, 88], [19, 554, 11, 88, "get"], [19, 557, 11, 88], [19, 561, 11, 88, "i"], [19, 562, 11, 88], [19, 563, 11, 88, "set"], [19, 566, 11, 88], [19, 570, 11, 88, "o"], [19, 571, 11, 88], [19, 572, 11, 88, "f"], [19, 573, 11, 88], [19, 575, 11, 88, "_t"], [19, 577, 11, 88], [19, 579, 11, 88, "i"], [19, 580, 11, 88], [19, 584, 11, 88, "f"], [19, 585, 11, 88], [19, 586, 11, 88, "_t"], [19, 588, 11, 88], [19, 592, 11, 88, "e"], [19, 593, 11, 88], [19, 594, 11, 88, "_t"], [19, 596, 11, 88], [19, 607, 11, 88, "f"], [19, 608, 11, 88], [19, 613, 11, 88, "e"], [19, 614, 11, 88], [19, 616, 11, 88, "t"], [19, 617, 11, 88], [20, 2, 11, 88], [20, 6, 11, 88, "_default"], [20, 14, 11, 88], [20, 17, 11, 88, "exports"], [20, 24, 11, 88], [20, 25, 11, 88, "default"], [20, 32, 11, 88], [20, 35, 13, 15, "NativeStatusBarManagerIOS"], [20, 69, 13, 40], [21, 0, 13, 40], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}