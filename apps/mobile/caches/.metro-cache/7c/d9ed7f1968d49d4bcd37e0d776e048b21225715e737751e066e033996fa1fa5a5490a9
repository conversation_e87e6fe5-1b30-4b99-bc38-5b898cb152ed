{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const SkipBack = exports.default = (0, _createLucideIcon.default)(\"SkipBack\", [[\"polygon\", {\n    points: \"19 20 9 12 19 4 19 20\",\n    key: \"o2sva\"\n  }], [\"line\", {\n    x1: \"5\",\n    x2: \"5\",\n    y1: \"19\",\n    y2: \"5\",\n    key: \"1ocqjk\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "SkipBack"], [15, 16, 10, 14], [15, 19, 10, 14, "exports"], [15, 26, 10, 14], [15, 27, 10, 14, "default"], [15, 34, 10, 14], [15, 37, 10, 17], [15, 41, 10, 17, "createLucideIcon"], [15, 66, 10, 33], [15, 68, 10, 34], [15, 78, 10, 44], [15, 80, 10, 46], [15, 81, 11, 2], [15, 82, 11, 3], [15, 91, 11, 12], [15, 93, 11, 14], [16, 4, 11, 16, "points"], [16, 10, 11, 22], [16, 12, 11, 24], [16, 35, 11, 47], [17, 4, 11, 49, "key"], [17, 7, 11, 52], [17, 9, 11, 54], [18, 2, 11, 62], [18, 3, 11, 63], [18, 4, 11, 64], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "x1"], [19, 6, 12, 15], [19, 8, 12, 17], [19, 11, 12, 20], [20, 4, 12, 22, "x2"], [20, 6, 12, 24], [20, 8, 12, 26], [20, 11, 12, 29], [21, 4, 12, 31, "y1"], [21, 6, 12, 33], [21, 8, 12, 35], [21, 12, 12, 39], [22, 4, 12, 41, "y2"], [22, 6, 12, 43], [22, 8, 12, 45], [22, 11, 12, 48], [23, 4, 12, 50, "key"], [23, 7, 12, 53], [23, 9, 12, 55], [24, 2, 12, 64], [24, 3, 12, 65], [24, 4, 12, 66], [24, 5, 13, 1], [24, 6, 13, 2], [25, 0, 13, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}