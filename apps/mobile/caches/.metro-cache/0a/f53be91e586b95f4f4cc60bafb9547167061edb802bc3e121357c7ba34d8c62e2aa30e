{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 48}, "end": {"line": 8, "column": 15, "index": 146}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 147}, "end": {"line": 9, "column": 40, "index": 187}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../../findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 188}, "end": {"line": 10, "column": 53, "index": 241}}], "key": "k+xfarWxri7fB3IShKFMK0oi5UQ=", "exportNames": ["*"]}}, {"name": "../../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 411}, "end": {"line": 14, "column": 43, "index": 454}}], "key": "4wo4OYT4MSo2InL8kiWmZxvepwE=", "exportNames": ["*"]}}, {"name": "../../../GestureHandlerRootViewContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 456}, "end": {"line": 16, "column": 83, "index": 539}}], "key": "v6b9cfauRqYeWu9wWOEUTyMIHSA=", "exportNames": ["*"]}}, {"name": "./useAnimatedGesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 610}, "end": {"line": 18, "column": 58, "index": 668}}], "key": "2qsvw/0Wn5ZQ0k+d9VbJV8PW2us=", "exportNames": ["*"]}}, {"name": "./attachHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 669}, "end": {"line": 19, "column": 50, "index": 719}}], "key": "3mjR74KCCo5t43evU8Hvoyi9yu0=", "exportNames": ["*"]}}, {"name": "./needsToReattach", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 720}, "end": {"line": 20, "column": 52, "index": 772}}], "key": "AnC4N1Crd90FP+3Mxk358neOkRo=", "exportNames": ["*"]}}, {"name": "./dropHandlers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 773}, "end": {"line": 21, "column": 46, "index": 819}}], "key": "3pg09hFbTrtcJ+KzQ97dAmmPlSE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 820}, "end": {"line": 22, "column": 46, "index": 866}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "./Wrap", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 867}, "end": {"line": 23, "column": 44, "index": 911}}], "key": "3O9fTt6BDgaEKEy9t1chSR0HFNQ=", "exportNames": ["*"]}}, {"name": "./useDetectorUpdater", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 912}, "end": {"line": 24, "column": 58, "index": 970}}], "key": "707zRwYZ3uzpHSm+Rwc2R1MvfJw=", "exportNames": ["*"]}}, {"name": "./useViewRefHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 971}, "end": {"line": 25, "column": 56, "index": 1027}}], "key": "tm4O9dzaDCUn7KS1TB05c8nzNaA=", "exportNames": ["*"]}}, {"name": "./useMountReactions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 1028}, "end": {"line": 26, "column": 56, "index": 1084}}], "key": "PrdD1p6YXRuOP5neQrVF/37n7P4=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GestureDetector = void 0;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[3], \"../../../findNodeHandle\"));\n  var _utils = require(_dependencyMap[4], \"../../../utils\");\n  var _GestureHandlerRootViewContext = _interopRequireDefault(require(_dependencyMap[5], \"../../../GestureHandlerRootViewContext\"));\n  var _useAnimatedGesture = require(_dependencyMap[6], \"./useAnimatedGesture\");\n  var _attachHandlers = require(_dependencyMap[7], \"./attachHandlers\");\n  var _needsToReattach = require(_dependencyMap[8], \"./needsToReattach\");\n  var _dropHandlers = require(_dependencyMap[9], \"./dropHandlers\");\n  var _utils2 = require(_dependencyMap[10], \"./utils\");\n  var _Wrap = require(_dependencyMap[11], \"./Wrap\");\n  var _useDetectorUpdater = require(_dependencyMap[12], \"./useDetectorUpdater\");\n  var _useViewRefHandler = require(_dependencyMap[13], \"./useViewRefHandler\");\n  var _useMountReactions = require(_dependencyMap[14], \"./useMountReactions\");\n  var _jsxDevRuntime = require(_dependencyMap[15], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-gesture-handler/src/handlers/gestures/GestureDetector/index.tsx\";\n  /* eslint-disable react/no-unused-prop-types */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function propagateDetectorConfig(props, gesture) {\n    var keysToPropagate = ['userSelect', 'enableContextMenu', 'touchAction'];\n    for (var key of keysToPropagate) {\n      var value = props[key];\n      if (value === undefined) {\n        continue;\n      }\n      for (var g of gesture.toGestureArray()) {\n        var config = g.config;\n        config[key] = value;\n      }\n    }\n  }\n  /**\n   * `GestureDetector` is responsible for creating and updating native gesture handlers based on the config of provided gesture.\n   *\n   * ### Props\n   * - `gesture`\n   * - `userSelect` (**Web only**)\n   * - `enableContextMenu` (**Web only**)\n   * - `touchAction` (**Web only**)\n   *\n   * ### Remarks\n   * - Gesture Detector will use first native view in its subtree to recognize gestures, however if this view is used only to group its children it may get automatically collapsed.\n   * - Using the same instance of a gesture across multiple Gesture Detectors is not possible.\n   *\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/gesture-detector\n   */\n  var GestureDetector = props => {\n    var rootViewContext = (0, _react.useContext)(_GestureHandlerRootViewContext.default);\n    if (__DEV__ && !rootViewContext && !(0, _utils.isTestEnv)() && _reactNative.Platform.OS !== 'web') {\n      throw new Error('GestureDetector must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.');\n    }\n\n    // Gesture config should be wrapped with useMemo to prevent unnecessary re-renders\n    var gestureConfig = props.gesture;\n    propagateDetectorConfig(props, gestureConfig);\n    var gesturesToAttach = (0, _react.useMemo)(() => gestureConfig.toGestureArray(), [gestureConfig]);\n    var shouldUseReanimated = gesturesToAttach.some(g => g.shouldUseReanimated);\n    var webEventHandlersRef = (0, _utils2.useWebEventHandlers)();\n    // Store state in ref to prevent unnecessary renders\n    var state = (0, _react.useRef)({\n      firstRender: true,\n      viewRef: null,\n      previousViewTag: -1,\n      forceRebuildReanimatedEvent: false\n    }).current;\n    var preparedGesture = _react.default.useRef({\n      attachedGestures: [],\n      animatedEventHandler: null,\n      animatedHandlers: null,\n      shouldUseReanimated: shouldUseReanimated,\n      isMounted: false\n    }).current;\n    var updateAttachedGestures = (0, _useDetectorUpdater.useDetectorUpdater)(state, preparedGesture, gesturesToAttach, gestureConfig, webEventHandlersRef);\n    var refHandler = (0, _useViewRefHandler.useViewRefHandler)(state, updateAttachedGestures);\n\n    // Reanimated event should be rebuilt only when gestures are reattached, otherwise\n    // config update will be enough as all necessary items are stored in shared values anyway\n    var needsToRebuildReanimatedEvent = state.firstRender || state.forceRebuildReanimatedEvent || (0, _needsToReattach.needsToReattach)(preparedGesture, gesturesToAttach);\n    state.forceRebuildReanimatedEvent = false;\n    (0, _useAnimatedGesture.useAnimatedGesture)(preparedGesture, needsToRebuildReanimatedEvent);\n    (0, _react.useLayoutEffect)(() => {\n      var viewTag = (0, _findNodeHandle.default)(state.viewRef);\n      preparedGesture.isMounted = true;\n      (0, _attachHandlers.attachHandlers)({\n        preparedGesture,\n        gestureConfig,\n        gesturesToAttach,\n        webEventHandlersRef,\n        viewTag\n      });\n      return () => {\n        preparedGesture.isMounted = false;\n        (0, _dropHandlers.dropHandlers)(preparedGesture);\n      };\n    }, []);\n    (0, _react.useEffect)(() => {\n      if (state.firstRender) {\n        state.firstRender = false;\n      } else {\n        updateAttachedGestures();\n      }\n    }, [props]);\n    (0, _useMountReactions.useMountReactions)(updateAttachedGestures, preparedGesture);\n    if (shouldUseReanimated) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Wrap.AnimatedWrap, {\n        ref: refHandler,\n        onGestureHandlerEvent: preparedGesture.animatedEventHandler,\n        children: props.children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 7\n      }, this);\n    } else {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Wrap.Wrap, {\n        ref: refHandler,\n        children: props.children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 12\n      }, this);\n    }\n  };\n  exports.GestureDetector = GestureDetector;\n});", "lineCount": 132, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_react"], [7, 12, 2, 0], [7, 15, 2, 0, "_interopRequireWildcard"], [7, 38, 2, 0], [7, 39, 2, 0, "require"], [7, 46, 2, 0], [7, 47, 2, 0, "_dependencyMap"], [7, 61, 2, 0], [8, 2, 9, 0], [8, 6, 9, 0, "_reactNative"], [8, 18, 9, 0], [8, 21, 9, 0, "require"], [8, 28, 9, 0], [8, 29, 9, 0, "_dependencyMap"], [8, 43, 9, 0], [9, 2, 10, 0], [9, 6, 10, 0, "_findNodeHandle"], [9, 21, 10, 0], [9, 24, 10, 0, "_interopRequireDefault"], [9, 46, 10, 0], [9, 47, 10, 0, "require"], [9, 54, 10, 0], [9, 55, 10, 0, "_dependencyMap"], [9, 69, 10, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_utils"], [10, 12, 14, 0], [10, 15, 14, 0, "require"], [10, 22, 14, 0], [10, 23, 14, 0, "_dependencyMap"], [10, 37, 14, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_GestureHandlerRootViewContext"], [11, 36, 16, 0], [11, 39, 16, 0, "_interopRequireDefault"], [11, 61, 16, 0], [11, 62, 16, 0, "require"], [11, 69, 16, 0], [11, 70, 16, 0, "_dependencyMap"], [11, 84, 16, 0], [12, 2, 18, 0], [12, 6, 18, 0, "_useAnimatedGesture"], [12, 25, 18, 0], [12, 28, 18, 0, "require"], [12, 35, 18, 0], [12, 36, 18, 0, "_dependencyMap"], [12, 50, 18, 0], [13, 2, 19, 0], [13, 6, 19, 0, "_attachHandlers"], [13, 21, 19, 0], [13, 24, 19, 0, "require"], [13, 31, 19, 0], [13, 32, 19, 0, "_dependencyMap"], [13, 46, 19, 0], [14, 2, 20, 0], [14, 6, 20, 0, "_needsToReattach"], [14, 22, 20, 0], [14, 25, 20, 0, "require"], [14, 32, 20, 0], [14, 33, 20, 0, "_dependencyMap"], [14, 47, 20, 0], [15, 2, 21, 0], [15, 6, 21, 0, "_dropHandlers"], [15, 19, 21, 0], [15, 22, 21, 0, "require"], [15, 29, 21, 0], [15, 30, 21, 0, "_dependencyMap"], [15, 44, 21, 0], [16, 2, 22, 0], [16, 6, 22, 0, "_utils2"], [16, 13, 22, 0], [16, 16, 22, 0, "require"], [16, 23, 22, 0], [16, 24, 22, 0, "_dependencyMap"], [16, 38, 22, 0], [17, 2, 23, 0], [17, 6, 23, 0, "_Wrap"], [17, 11, 23, 0], [17, 14, 23, 0, "require"], [17, 21, 23, 0], [17, 22, 23, 0, "_dependencyMap"], [17, 36, 23, 0], [18, 2, 24, 0], [18, 6, 24, 0, "_useDetectorUpdater"], [18, 25, 24, 0], [18, 28, 24, 0, "require"], [18, 35, 24, 0], [18, 36, 24, 0, "_dependencyMap"], [18, 50, 24, 0], [19, 2, 25, 0], [19, 6, 25, 0, "_useViewRefHandler"], [19, 24, 25, 0], [19, 27, 25, 0, "require"], [19, 34, 25, 0], [19, 35, 25, 0, "_dependencyMap"], [19, 49, 25, 0], [20, 2, 26, 0], [20, 6, 26, 0, "_useMountReactions"], [20, 24, 26, 0], [20, 27, 26, 0, "require"], [20, 34, 26, 0], [20, 35, 26, 0, "_dependencyMap"], [20, 49, 26, 0], [21, 2, 26, 56], [21, 6, 26, 56, "_jsxDevRuntime"], [21, 20, 26, 56], [21, 23, 26, 56, "require"], [21, 30, 26, 56], [21, 31, 26, 56, "_dependencyMap"], [21, 45, 26, 56], [22, 2, 26, 56], [22, 6, 26, 56, "_jsxFileName"], [22, 18, 26, 56], [23, 2, 1, 0], [24, 2, 1, 0], [24, 11, 1, 0, "_interopRequireWildcard"], [24, 35, 1, 0, "e"], [24, 36, 1, 0], [24, 38, 1, 0, "t"], [24, 39, 1, 0], [24, 68, 1, 0, "WeakMap"], [24, 75, 1, 0], [24, 81, 1, 0, "r"], [24, 82, 1, 0], [24, 89, 1, 0, "WeakMap"], [24, 96, 1, 0], [24, 100, 1, 0, "n"], [24, 101, 1, 0], [24, 108, 1, 0, "WeakMap"], [24, 115, 1, 0], [24, 127, 1, 0, "_interopRequireWildcard"], [24, 150, 1, 0], [24, 162, 1, 0, "_interopRequireWildcard"], [24, 163, 1, 0, "e"], [24, 164, 1, 0], [24, 166, 1, 0, "t"], [24, 167, 1, 0], [24, 176, 1, 0, "t"], [24, 177, 1, 0], [24, 181, 1, 0, "e"], [24, 182, 1, 0], [24, 186, 1, 0, "e"], [24, 187, 1, 0], [24, 188, 1, 0, "__esModule"], [24, 198, 1, 0], [24, 207, 1, 0, "e"], [24, 208, 1, 0], [24, 214, 1, 0, "o"], [24, 215, 1, 0], [24, 217, 1, 0, "i"], [24, 218, 1, 0], [24, 220, 1, 0, "f"], [24, 221, 1, 0], [24, 226, 1, 0, "__proto__"], [24, 235, 1, 0], [24, 243, 1, 0, "default"], [24, 250, 1, 0], [24, 252, 1, 0, "e"], [24, 253, 1, 0], [24, 270, 1, 0, "e"], [24, 271, 1, 0], [24, 294, 1, 0, "e"], [24, 295, 1, 0], [24, 320, 1, 0, "e"], [24, 321, 1, 0], [24, 330, 1, 0, "f"], [24, 331, 1, 0], [24, 337, 1, 0, "o"], [24, 338, 1, 0], [24, 341, 1, 0, "t"], [24, 342, 1, 0], [24, 345, 1, 0, "n"], [24, 346, 1, 0], [24, 349, 1, 0, "r"], [24, 350, 1, 0], [24, 358, 1, 0, "o"], [24, 359, 1, 0], [24, 360, 1, 0, "has"], [24, 363, 1, 0], [24, 364, 1, 0, "e"], [24, 365, 1, 0], [24, 375, 1, 0, "o"], [24, 376, 1, 0], [24, 377, 1, 0, "get"], [24, 380, 1, 0], [24, 381, 1, 0, "e"], [24, 382, 1, 0], [24, 385, 1, 0, "o"], [24, 386, 1, 0], [24, 387, 1, 0, "set"], [24, 390, 1, 0], [24, 391, 1, 0, "e"], [24, 392, 1, 0], [24, 394, 1, 0, "f"], [24, 395, 1, 0], [24, 409, 1, 0, "_t"], [24, 411, 1, 0], [24, 415, 1, 0, "e"], [24, 416, 1, 0], [24, 432, 1, 0, "_t"], [24, 434, 1, 0], [24, 441, 1, 0, "hasOwnProperty"], [24, 455, 1, 0], [24, 456, 1, 0, "call"], [24, 460, 1, 0], [24, 461, 1, 0, "e"], [24, 462, 1, 0], [24, 464, 1, 0, "_t"], [24, 466, 1, 0], [24, 473, 1, 0, "i"], [24, 474, 1, 0], [24, 478, 1, 0, "o"], [24, 479, 1, 0], [24, 482, 1, 0, "Object"], [24, 488, 1, 0], [24, 489, 1, 0, "defineProperty"], [24, 503, 1, 0], [24, 508, 1, 0, "Object"], [24, 514, 1, 0], [24, 515, 1, 0, "getOwnPropertyDescriptor"], [24, 539, 1, 0], [24, 540, 1, 0, "e"], [24, 541, 1, 0], [24, 543, 1, 0, "_t"], [24, 545, 1, 0], [24, 552, 1, 0, "i"], [24, 553, 1, 0], [24, 554, 1, 0, "get"], [24, 557, 1, 0], [24, 561, 1, 0, "i"], [24, 562, 1, 0], [24, 563, 1, 0, "set"], [24, 566, 1, 0], [24, 570, 1, 0, "o"], [24, 571, 1, 0], [24, 572, 1, 0, "f"], [24, 573, 1, 0], [24, 575, 1, 0, "_t"], [24, 577, 1, 0], [24, 579, 1, 0, "i"], [24, 580, 1, 0], [24, 584, 1, 0, "f"], [24, 585, 1, 0], [24, 586, 1, 0, "_t"], [24, 588, 1, 0], [24, 592, 1, 0, "e"], [24, 593, 1, 0], [24, 594, 1, 0, "_t"], [24, 596, 1, 0], [24, 607, 1, 0, "f"], [24, 608, 1, 0], [24, 613, 1, 0, "e"], [24, 614, 1, 0], [24, 616, 1, 0, "t"], [24, 617, 1, 0], [25, 2, 28, 0], [25, 11, 28, 9, "propagateDetectorConfig"], [25, 34, 28, 32, "propagateDetectorConfig"], [25, 35, 29, 2, "props"], [25, 40, 29, 29], [25, 42, 30, 2, "gesture"], [25, 49, 30, 40], [25, 51, 31, 2], [26, 4, 32, 2], [26, 8, 32, 8, "keysToPropagate"], [26, 23, 32, 55], [26, 26, 32, 58], [26, 27, 33, 4], [26, 39, 33, 16], [26, 41, 34, 4], [26, 60, 34, 23], [26, 62, 35, 4], [26, 75, 35, 17], [26, 76, 36, 3], [27, 4, 38, 2], [27, 9, 38, 7], [27, 13, 38, 13, "key"], [27, 16, 38, 16], [27, 20, 38, 20, "keysToPropagate"], [27, 35, 38, 35], [27, 37, 38, 37], [28, 6, 39, 4], [28, 10, 39, 10, "value"], [28, 15, 39, 15], [28, 18, 39, 18, "props"], [28, 23, 39, 23], [28, 24, 39, 24, "key"], [28, 27, 39, 27], [28, 28, 39, 28], [29, 6, 40, 4], [29, 10, 40, 8, "value"], [29, 15, 40, 13], [29, 20, 40, 18, "undefined"], [29, 29, 40, 27], [29, 31, 40, 29], [30, 8, 41, 6], [31, 6, 42, 4], [32, 6, 44, 4], [32, 11, 44, 9], [32, 15, 44, 15, "g"], [32, 16, 44, 16], [32, 20, 44, 20, "gesture"], [32, 27, 44, 27], [32, 28, 44, 28, "toGestureArray"], [32, 42, 44, 42], [32, 43, 44, 43], [32, 44, 44, 44], [32, 46, 44, 46], [33, 8, 45, 6], [33, 12, 45, 12, "config"], [33, 18, 45, 18], [33, 21, 45, 21, "g"], [33, 22, 45, 22], [33, 23, 45, 23, "config"], [33, 29, 45, 59], [34, 8, 46, 6, "config"], [34, 14, 46, 12], [34, 15, 46, 13, "key"], [34, 18, 46, 16], [34, 19, 46, 17], [34, 22, 46, 20, "value"], [34, 27, 46, 25], [35, 6, 47, 4], [36, 4, 48, 2], [37, 2, 49, 0], [38, 2, 80, 0], [39, 0, 81, 0], [40, 0, 82, 0], [41, 0, 83, 0], [42, 0, 84, 0], [43, 0, 85, 0], [44, 0, 86, 0], [45, 0, 87, 0], [46, 0, 88, 0], [47, 0, 89, 0], [48, 0, 90, 0], [49, 0, 91, 0], [50, 0, 92, 0], [51, 0, 93, 0], [52, 0, 94, 0], [53, 2, 95, 7], [53, 6, 95, 13, "GestureDetector"], [53, 21, 95, 28], [53, 24, 95, 32, "props"], [53, 29, 95, 59], [53, 33, 95, 64], [54, 4, 96, 2], [54, 8, 96, 8, "rootViewContext"], [54, 23, 96, 23], [54, 26, 96, 26], [54, 30, 96, 26, "useContext"], [54, 47, 96, 36], [54, 49, 96, 37, "GestureHandlerRootViewContext"], [54, 87, 96, 66], [54, 88, 96, 67], [55, 4, 97, 2], [55, 8, 97, 6, "__DEV__"], [55, 15, 97, 13], [55, 19, 97, 17], [55, 20, 97, 18, "rootViewContext"], [55, 35, 97, 33], [55, 39, 97, 37], [55, 40, 97, 38], [55, 44, 97, 38, "isTestEnv"], [55, 60, 97, 47], [55, 62, 97, 48], [55, 63, 97, 49], [55, 67, 97, 53, "Platform"], [55, 88, 97, 61], [55, 89, 97, 62, "OS"], [55, 91, 97, 64], [55, 96, 97, 69], [55, 101, 97, 74], [55, 103, 97, 76], [56, 6, 98, 4], [56, 12, 98, 10], [56, 16, 98, 14, "Error"], [56, 21, 98, 19], [56, 22, 99, 6], [56, 238, 100, 4], [56, 239, 100, 5], [57, 4, 101, 2], [59, 4, 103, 2], [60, 4, 104, 2], [60, 8, 104, 8, "gestureConfig"], [60, 21, 104, 21], [60, 24, 104, 24, "props"], [60, 29, 104, 29], [60, 30, 104, 30, "gesture"], [60, 37, 104, 37], [61, 4, 105, 2, "propagateDetectorConfig"], [61, 27, 105, 25], [61, 28, 105, 26, "props"], [61, 33, 105, 31], [61, 35, 105, 33, "gestureConfig"], [61, 48, 105, 46], [61, 49, 105, 47], [62, 4, 107, 2], [62, 8, 107, 8, "gestures<PERSON>oAtta<PERSON>"], [62, 24, 107, 24], [62, 27, 107, 27], [62, 31, 107, 27, "useMemo"], [62, 45, 107, 34], [62, 47, 108, 4], [62, 53, 108, 10, "gestureConfig"], [62, 66, 108, 23], [62, 67, 108, 24, "toGestureArray"], [62, 81, 108, 38], [62, 82, 108, 39], [62, 83, 108, 40], [62, 85, 109, 4], [62, 86, 109, 5, "gestureConfig"], [62, 99, 109, 18], [62, 100, 110, 2], [62, 101, 110, 3], [63, 4, 111, 2], [63, 8, 111, 8, "shouldUseReanimated"], [63, 27, 111, 27], [63, 30, 111, 30, "gestures<PERSON>oAtta<PERSON>"], [63, 46, 111, 46], [63, 47, 111, 47, "some"], [63, 51, 111, 51], [63, 52, 112, 5, "g"], [63, 53, 112, 6], [63, 57, 112, 11, "g"], [63, 58, 112, 12], [63, 59, 112, 13, "shouldUseReanimated"], [63, 78, 113, 2], [63, 79, 113, 3], [64, 4, 115, 2], [64, 8, 115, 8, "webEventHandlersRef"], [64, 27, 115, 27], [64, 30, 115, 30], [64, 34, 115, 30, "useWebEventHandlers"], [64, 61, 115, 49], [64, 63, 115, 50], [64, 64, 115, 51], [65, 4, 116, 2], [66, 4, 117, 2], [66, 8, 117, 8, "state"], [66, 13, 117, 13], [66, 16, 117, 16], [66, 20, 117, 16, "useRef"], [66, 33, 117, 22], [66, 35, 117, 45], [67, 6, 118, 4, "firstRender"], [67, 17, 118, 15], [67, 19, 118, 17], [67, 23, 118, 21], [68, 6, 119, 4, "viewRef"], [68, 13, 119, 11], [68, 15, 119, 13], [68, 19, 119, 17], [69, 6, 120, 4, "previousViewTag"], [69, 21, 120, 19], [69, 23, 120, 21], [69, 24, 120, 22], [69, 25, 120, 23], [70, 6, 121, 4, "forceRebuildReanimatedEvent"], [70, 33, 121, 31], [70, 35, 121, 33], [71, 4, 122, 2], [71, 5, 122, 3], [71, 6, 122, 4], [71, 7, 122, 5, "current"], [71, 14, 122, 12], [72, 4, 124, 2], [72, 8, 124, 8, "preparedGesture"], [72, 23, 124, 23], [72, 26, 124, 26, "React"], [72, 40, 124, 31], [72, 41, 124, 32, "useRef"], [72, 47, 124, 38], [72, 48, 124, 61], [73, 6, 125, 4, "attachedGestures"], [73, 22, 125, 20], [73, 24, 125, 22], [73, 26, 125, 24], [74, 6, 126, 4, "animatedEventHandler"], [74, 26, 126, 24], [74, 28, 126, 26], [74, 32, 126, 30], [75, 6, 127, 4, "animatedHandlers"], [75, 22, 127, 20], [75, 24, 127, 22], [75, 28, 127, 26], [76, 6, 128, 4, "shouldUseReanimated"], [76, 25, 128, 23], [76, 27, 128, 25, "shouldUseReanimated"], [76, 46, 128, 44], [77, 6, 129, 4, "isMounted"], [77, 15, 129, 13], [77, 17, 129, 15], [78, 4, 130, 2], [78, 5, 130, 3], [78, 6, 130, 4], [78, 7, 130, 5, "current"], [78, 14, 130, 12], [79, 4, 132, 2], [79, 8, 132, 8, "updateAttachedGestures"], [79, 30, 132, 30], [79, 33, 132, 33], [79, 37, 132, 33, "useDetectorUpdater"], [79, 75, 132, 51], [79, 77, 133, 4, "state"], [79, 82, 133, 9], [79, 84, 134, 4, "preparedGesture"], [79, 99, 134, 19], [79, 101, 135, 4, "gestures<PERSON>oAtta<PERSON>"], [79, 117, 135, 20], [79, 119, 136, 4, "gestureConfig"], [79, 132, 136, 17], [79, 134, 137, 4, "webEventHandlersRef"], [79, 153, 138, 2], [79, 154, 138, 3], [80, 4, 140, 2], [80, 8, 140, 8, "ref<PERSON><PERSON><PERSON>"], [80, 18, 140, 18], [80, 21, 140, 21], [80, 25, 140, 21, "useViewRefHandler"], [80, 61, 140, 38], [80, 63, 140, 39, "state"], [80, 68, 140, 44], [80, 70, 140, 46, "updateAttachedGestures"], [80, 92, 140, 68], [80, 93, 140, 69], [82, 4, 142, 2], [83, 4, 143, 2], [84, 4, 144, 2], [84, 8, 144, 8, "needsToRebuildReanimatedEvent"], [84, 37, 144, 37], [84, 40, 145, 4, "state"], [84, 45, 145, 9], [84, 46, 145, 10, "firstRender"], [84, 57, 145, 21], [84, 61, 146, 4, "state"], [84, 66, 146, 9], [84, 67, 146, 10, "forceRebuildReanimatedEvent"], [84, 94, 146, 37], [84, 98, 147, 4], [84, 102, 147, 4, "needsToReattach"], [84, 134, 147, 19], [84, 136, 147, 20, "preparedGesture"], [84, 151, 147, 35], [84, 153, 147, 37, "gestures<PERSON>oAtta<PERSON>"], [84, 169, 147, 53], [84, 170, 147, 54], [85, 4, 148, 2, "state"], [85, 9, 148, 7], [85, 10, 148, 8, "forceRebuildReanimatedEvent"], [85, 37, 148, 35], [85, 40, 148, 38], [85, 45, 148, 43], [86, 4, 150, 2], [86, 8, 150, 2, "useAnimatedGesture"], [86, 46, 150, 20], [86, 48, 150, 21, "preparedGesture"], [86, 63, 150, 36], [86, 65, 150, 38, "needsToRebuildReanimatedEvent"], [86, 94, 150, 67], [86, 95, 150, 68], [87, 4, 152, 2], [87, 8, 152, 2, "useLayoutEffect"], [87, 30, 152, 17], [87, 32, 152, 18], [87, 38, 152, 24], [88, 6, 153, 4], [88, 10, 153, 10, "viewTag"], [88, 17, 153, 17], [88, 20, 153, 20], [88, 24, 153, 20, "findNodeHandle"], [88, 47, 153, 34], [88, 49, 153, 35, "state"], [88, 54, 153, 40], [88, 55, 153, 41, "viewRef"], [88, 62, 153, 48], [88, 63, 153, 59], [89, 6, 154, 4, "preparedGesture"], [89, 21, 154, 19], [89, 22, 154, 20, "isMounted"], [89, 31, 154, 29], [89, 34, 154, 32], [89, 38, 154, 36], [90, 6, 156, 4], [90, 10, 156, 4, "attachHandlers"], [90, 40, 156, 18], [90, 42, 156, 19], [91, 8, 157, 6, "preparedGesture"], [91, 23, 157, 21], [92, 8, 158, 6, "gestureConfig"], [92, 21, 158, 19], [93, 8, 159, 6, "gestures<PERSON>oAtta<PERSON>"], [93, 24, 159, 22], [94, 8, 160, 6, "webEventHandlersRef"], [94, 27, 160, 25], [95, 8, 161, 6, "viewTag"], [96, 6, 162, 4], [96, 7, 162, 5], [96, 8, 162, 6], [97, 6, 164, 4], [97, 13, 164, 11], [97, 19, 164, 17], [98, 8, 165, 6, "preparedGesture"], [98, 23, 165, 21], [98, 24, 165, 22, "isMounted"], [98, 33, 165, 31], [98, 36, 165, 34], [98, 41, 165, 39], [99, 8, 166, 6], [99, 12, 166, 6, "dropHandlers"], [99, 38, 166, 18], [99, 40, 166, 19, "preparedGesture"], [99, 55, 166, 34], [99, 56, 166, 35], [100, 6, 167, 4], [100, 7, 167, 5], [101, 4, 168, 2], [101, 5, 168, 3], [101, 7, 168, 5], [101, 9, 168, 7], [101, 10, 168, 8], [102, 4, 170, 2], [102, 8, 170, 2, "useEffect"], [102, 24, 170, 11], [102, 26, 170, 12], [102, 32, 170, 18], [103, 6, 171, 4], [103, 10, 171, 8, "state"], [103, 15, 171, 13], [103, 16, 171, 14, "firstRender"], [103, 27, 171, 25], [103, 29, 171, 27], [104, 8, 172, 6, "state"], [104, 13, 172, 11], [104, 14, 172, 12, "firstRender"], [104, 25, 172, 23], [104, 28, 172, 26], [104, 33, 172, 31], [105, 6, 173, 4], [105, 7, 173, 5], [105, 13, 173, 11], [106, 8, 174, 6, "updateAttachedGestures"], [106, 30, 174, 28], [106, 31, 174, 29], [106, 32, 174, 30], [107, 6, 175, 4], [108, 4, 176, 2], [108, 5, 176, 3], [108, 7, 176, 5], [108, 8, 176, 6, "props"], [108, 13, 176, 11], [108, 14, 176, 12], [108, 15, 176, 13], [109, 4, 178, 2], [109, 8, 178, 2, "useMountReactions"], [109, 44, 178, 19], [109, 46, 178, 20, "updateAttachedGestures"], [109, 68, 178, 42], [109, 70, 178, 44, "preparedGesture"], [109, 85, 178, 59], [109, 86, 178, 60], [110, 4, 180, 2], [110, 8, 180, 6, "shouldUseReanimated"], [110, 27, 180, 25], [110, 29, 180, 27], [111, 6, 181, 4], [111, 26, 182, 6], [111, 30, 182, 6, "_jsxDevRuntime"], [111, 44, 182, 6], [111, 45, 182, 6, "jsxDEV"], [111, 51, 182, 6], [111, 53, 182, 7, "_Wrap"], [111, 58, 182, 7], [111, 59, 182, 7, "AnimatedWrap"], [111, 71, 182, 19], [112, 8, 183, 8, "ref"], [112, 11, 183, 11], [112, 13, 183, 13, "ref<PERSON><PERSON><PERSON>"], [112, 23, 183, 24], [113, 8, 184, 8, "onGestureHandlerEvent"], [113, 29, 184, 29], [113, 31, 184, 31, "preparedGesture"], [113, 46, 184, 46], [113, 47, 184, 47, "animatedEventHandler"], [113, 67, 184, 68], [114, 8, 184, 68, "children"], [114, 16, 184, 68], [114, 18, 185, 9, "props"], [114, 23, 185, 14], [114, 24, 185, 15, "children"], [115, 6, 185, 23], [116, 8, 185, 23, "fileName"], [116, 16, 185, 23], [116, 18, 185, 23, "_jsxFileName"], [116, 30, 185, 23], [117, 8, 185, 23, "lineNumber"], [117, 18, 185, 23], [118, 8, 185, 23, "columnNumber"], [118, 20, 185, 23], [119, 6, 185, 23], [119, 13, 186, 20], [119, 14, 186, 21], [120, 4, 188, 2], [120, 5, 188, 3], [120, 11, 188, 9], [121, 6, 189, 4], [121, 26, 189, 11], [121, 30, 189, 11, "_jsxDevRuntime"], [121, 44, 189, 11], [121, 45, 189, 11, "jsxDEV"], [121, 51, 189, 11], [121, 53, 189, 12, "_Wrap"], [121, 58, 189, 12], [121, 59, 189, 12, "Wrap"], [121, 63, 189, 16], [122, 8, 189, 17, "ref"], [122, 11, 189, 20], [122, 13, 189, 22, "ref<PERSON><PERSON><PERSON>"], [122, 23, 189, 33], [123, 8, 189, 33, "children"], [123, 16, 189, 33], [123, 18, 189, 35, "props"], [123, 23, 189, 40], [123, 24, 189, 41, "children"], [124, 6, 189, 49], [125, 8, 189, 49, "fileName"], [125, 16, 189, 49], [125, 18, 189, 49, "_jsxFileName"], [125, 30, 189, 49], [126, 8, 189, 49, "lineNumber"], [126, 18, 189, 49], [127, 8, 189, 49, "columnNumber"], [127, 20, 189, 49], [128, 6, 189, 49], [128, 13, 189, 56], [128, 14, 189, 57], [129, 4, 190, 2], [130, 2, 191, 0], [130, 3, 191, 1], [131, 2, 191, 2, "exports"], [131, 9, 191, 2], [131, 10, 191, 2, "GestureDetector"], [131, 25, 191, 2], [131, 28, 191, 2, "GestureDetector"], [131, 43, 191, 2], [132, 0, 191, 2], [132, 3]], "functionMap": {"names": ["<global>", "propagateDetectorConfig", "GestureDetector", "useMemo$argument_0", "gesturesToAttach.some$argument_0", "useLayoutEffect$argument_0", "<anonymous>", "useEffect$argument_0"], "mappings": "AAA;AC2B;CDqB;+BE8C;ICa,oCD;IEI,4BF;kBGwC;WCY;KDG;GHC;YKE;GLM;CFe"}}, "type": "js/module"}]}