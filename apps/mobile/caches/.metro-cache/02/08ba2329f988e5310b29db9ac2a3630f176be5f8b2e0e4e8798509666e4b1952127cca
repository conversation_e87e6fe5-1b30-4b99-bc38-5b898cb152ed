{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 987}, "end": {"line": 33, "column": 40, "index": 1027}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "@tanstack/react-query", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1028}, "end": {"line": 34, "column": 68, "index": 1096}}], "key": "Pzwu/0TIyhnZOrC9PAkpZx92hFo=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _expoRouter = require(_dependencyMap[21], \"expo-router\");\n  var _reactQuery = require(_dependencyMap[22], \"@tanstack/react-query\");\n  var _jsxDevRuntime = require(_dependencyMap[23], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const router = (0, _expoRouter.useRouter)();\n    const queryClient = (0, _reactQuery.useQueryClient)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0, _react.useState)(null);\n    const [sessionStartTime, setSessionStartTime] = (0, _react.useState)(null);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n    };\n    const handleSendMessage = (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"902VswXJGTYh6FyaM254sBNxdF8=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _expoRouter.useRouter, _reactQuery.useQueryClient, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 605, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 33, 0], [27, 6, 33, 0, "_expoRouter"], [27, 17, 33, 0], [27, 20, 33, 0, "require"], [27, 27, 33, 0], [27, 28, 33, 0, "_dependencyMap"], [27, 42, 33, 0], [28, 2, 34, 0], [28, 6, 34, 0, "_reactQuery"], [28, 17, 34, 0], [28, 20, 34, 0, "require"], [28, 27, 34, 0], [28, 28, 34, 0, "_dependencyMap"], [28, 42, 34, 0], [29, 2, 34, 68], [29, 6, 34, 68, "_jsxDevRuntime"], [29, 20, 34, 68], [29, 23, 34, 68, "require"], [29, 30, 34, 68], [29, 31, 34, 68, "_dependencyMap"], [29, 45, 34, 68], [30, 2, 34, 68], [30, 6, 34, 68, "_jsxFileName"], [30, 18, 34, 68], [31, 4, 34, 68, "_s"], [31, 6, 34, 68], [31, 9, 34, 68, "$RefreshSig$"], [31, 21, 34, 68], [32, 2, 34, 68], [32, 11, 34, 68, "_interopRequireWildcard"], [32, 35, 34, 68, "e"], [32, 36, 34, 68], [32, 38, 34, 68, "t"], [32, 39, 34, 68], [32, 68, 34, 68, "WeakMap"], [32, 75, 34, 68], [32, 81, 34, 68, "r"], [32, 82, 34, 68], [32, 89, 34, 68, "WeakMap"], [32, 96, 34, 68], [32, 100, 34, 68, "n"], [32, 101, 34, 68], [32, 108, 34, 68, "WeakMap"], [32, 115, 34, 68], [32, 127, 34, 68, "_interopRequireWildcard"], [32, 150, 34, 68], [32, 162, 34, 68, "_interopRequireWildcard"], [32, 163, 34, 68, "e"], [32, 164, 34, 68], [32, 166, 34, 68, "t"], [32, 167, 34, 68], [32, 176, 34, 68, "t"], [32, 177, 34, 68], [32, 181, 34, 68, "e"], [32, 182, 34, 68], [32, 186, 34, 68, "e"], [32, 187, 34, 68], [32, 188, 34, 68, "__esModule"], [32, 198, 34, 68], [32, 207, 34, 68, "e"], [32, 208, 34, 68], [32, 214, 34, 68, "o"], [32, 215, 34, 68], [32, 217, 34, 68, "i"], [32, 218, 34, 68], [32, 220, 34, 68, "f"], [32, 221, 34, 68], [32, 226, 34, 68, "__proto__"], [32, 235, 34, 68], [32, 243, 34, 68, "default"], [32, 250, 34, 68], [32, 252, 34, 68, "e"], [32, 253, 34, 68], [32, 270, 34, 68, "e"], [32, 271, 34, 68], [32, 294, 34, 68, "e"], [32, 295, 34, 68], [32, 320, 34, 68, "e"], [32, 321, 34, 68], [32, 330, 34, 68, "f"], [32, 331, 34, 68], [32, 337, 34, 68, "o"], [32, 338, 34, 68], [32, 341, 34, 68, "t"], [32, 342, 34, 68], [32, 345, 34, 68, "n"], [32, 346, 34, 68], [32, 349, 34, 68, "r"], [32, 350, 34, 68], [32, 358, 34, 68, "o"], [32, 359, 34, 68], [32, 360, 34, 68, "has"], [32, 363, 34, 68], [32, 364, 34, 68, "e"], [32, 365, 34, 68], [32, 375, 34, 68, "o"], [32, 376, 34, 68], [32, 377, 34, 68, "get"], [32, 380, 34, 68], [32, 381, 34, 68, "e"], [32, 382, 34, 68], [32, 385, 34, 68, "o"], [32, 386, 34, 68], [32, 387, 34, 68, "set"], [32, 390, 34, 68], [32, 391, 34, 68, "e"], [32, 392, 34, 68], [32, 394, 34, 68, "f"], [32, 395, 34, 68], [32, 411, 34, 68, "t"], [32, 412, 34, 68], [32, 416, 34, 68, "e"], [32, 417, 34, 68], [32, 433, 34, 68, "t"], [32, 434, 34, 68], [32, 441, 34, 68, "hasOwnProperty"], [32, 455, 34, 68], [32, 456, 34, 68, "call"], [32, 460, 34, 68], [32, 461, 34, 68, "e"], [32, 462, 34, 68], [32, 464, 34, 68, "t"], [32, 465, 34, 68], [32, 472, 34, 68, "i"], [32, 473, 34, 68], [32, 477, 34, 68, "o"], [32, 478, 34, 68], [32, 481, 34, 68, "Object"], [32, 487, 34, 68], [32, 488, 34, 68, "defineProperty"], [32, 502, 34, 68], [32, 507, 34, 68, "Object"], [32, 513, 34, 68], [32, 514, 34, 68, "getOwnPropertyDescriptor"], [32, 538, 34, 68], [32, 539, 34, 68, "e"], [32, 540, 34, 68], [32, 542, 34, 68, "t"], [32, 543, 34, 68], [32, 550, 34, 68, "i"], [32, 551, 34, 68], [32, 552, 34, 68, "get"], [32, 555, 34, 68], [32, 559, 34, 68, "i"], [32, 560, 34, 68], [32, 561, 34, 68, "set"], [32, 564, 34, 68], [32, 568, 34, 68, "o"], [32, 569, 34, 68], [32, 570, 34, 68, "f"], [32, 571, 34, 68], [32, 573, 34, 68, "t"], [32, 574, 34, 68], [32, 576, 34, 68, "i"], [32, 577, 34, 68], [32, 581, 34, 68, "f"], [32, 582, 34, 68], [32, 583, 34, 68, "t"], [32, 584, 34, 68], [32, 588, 34, 68, "e"], [32, 589, 34, 68], [32, 590, 34, 68, "t"], [32, 591, 34, 68], [32, 602, 34, 68, "f"], [32, 603, 34, 68], [32, 608, 34, 68, "e"], [32, 609, 34, 68], [32, 611, 34, 68, "t"], [32, 612, 34, 68], [33, 2, 36, 15], [33, 11, 36, 24, "BrainstormScreen"], [33, 27, 36, 40, "BrainstormScreen"], [33, 28, 36, 40], [33, 30, 36, 43], [34, 4, 36, 43, "_s"], [34, 6, 36, 43], [35, 4, 37, 2], [35, 10, 37, 8, "insets"], [35, 16, 37, 14], [35, 19, 37, 17], [35, 23, 37, 17, "useSafeAreaInsets"], [35, 68, 37, 34], [35, 70, 37, 35], [35, 71, 37, 36], [36, 4, 38, 2], [36, 10, 38, 8, "colors"], [36, 16, 38, 14], [36, 19, 38, 17], [36, 23, 38, 17, "useColors"], [36, 43, 38, 26], [36, 45, 38, 27], [36, 46, 38, 28], [37, 4, 39, 2], [37, 10, 39, 8, "router"], [37, 16, 39, 14], [37, 19, 39, 17], [37, 23, 39, 17, "useRouter"], [37, 44, 39, 26], [37, 46, 39, 27], [37, 47, 39, 28], [38, 4, 40, 2], [38, 10, 40, 8, "queryClient"], [38, 21, 40, 19], [38, 24, 40, 22], [38, 28, 40, 22, "useQueryClient"], [38, 54, 40, 36], [38, 56, 40, 37], [38, 57, 40, 38], [39, 4, 41, 2], [39, 10, 41, 8], [39, 11, 41, 9, "fontsLoaded"], [39, 22, 41, 20], [39, 23, 41, 21], [39, 26, 41, 24], [39, 30, 41, 24, "useFonts"], [39, 47, 41, 32], [39, 49, 41, 33], [40, 6, 42, 4, "Poppins_400Regular"], [40, 24, 42, 22], [40, 26, 42, 4, "Poppins_400Regular"], [40, 53, 42, 22], [41, 6, 43, 4, "Poppins_500Medium"], [41, 23, 43, 21], [41, 25, 43, 4, "Poppins_500Medium"], [41, 51, 43, 21], [42, 6, 44, 4, "Poppins_600SemiBold"], [42, 25, 44, 23], [42, 27, 44, 4, "Poppins_600SemiBold"], [43, 4, 45, 2], [43, 5, 45, 3], [43, 6, 45, 4], [44, 4, 47, 2], [44, 10, 47, 8, "recorder"], [44, 18, 47, 16], [44, 21, 47, 19], [44, 25, 47, 19, "useAudioRecorder"], [44, 52, 47, 35], [44, 54, 47, 36, "RecordingPresets"], [44, 81, 47, 52], [44, 82, 47, 53, "HIGH_QUALITY"], [44, 94, 47, 65], [44, 95, 47, 66], [45, 4, 48, 2], [45, 10, 48, 8, "recorderState"], [45, 23, 48, 21], [45, 26, 48, 24], [45, 30, 48, 24, "useAudioRecorderState"], [45, 62, 48, 45], [45, 64, 48, 46, "recorder"], [45, 72, 48, 54], [45, 73, 48, 55], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "messages"], [46, 19, 50, 17], [46, 21, 50, 19, "setMessages"], [46, 32, 50, 30], [46, 33, 50, 31], [46, 36, 50, 34], [46, 40, 50, 34, "useState"], [46, 55, 50, 42], [46, 57, 50, 43], [46, 59, 50, 45], [46, 60, 50, 46], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "inputText"], [47, 20, 51, 18], [47, 22, 51, 20, "setInputText"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 61, 51, 47], [47, 62, 51, 48], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "isFirstTime"], [48, 22, 52, 20], [48, 24, 52, 22, "setIsFirstTime"], [48, 38, 52, 36], [48, 39, 52, 37], [48, 42, 52, 40], [48, 46, 52, 40, "useState"], [48, 61, 52, 48], [48, 63, 52, 49], [48, 67, 52, 53], [48, 68, 52, 54], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isLoading"], [49, 20, 53, 18], [49, 22, 53, 20, "setIsLoading"], [49, 34, 53, 32], [49, 35, 53, 33], [49, 38, 53, 36], [49, 42, 53, 36, "useState"], [49, 57, 53, 44], [49, 59, 53, 45], [49, 64, 53, 50], [49, 65, 53, 51], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "quickActions"], [50, 23, 54, 21], [50, 25, 54, 23, "setQuickActions"], [50, 40, 54, 38], [50, 41, 54, 39], [50, 44, 54, 42], [50, 48, 54, 42, "useState"], [50, 63, 54, 50], [50, 65, 54, 51], [50, 67, 54, 53], [50, 68, 54, 54], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "voiceMode"], [51, 20, 55, 18], [51, 22, 55, 20, "setVoiceMode"], [51, 34, 55, 32], [51, 35, 55, 33], [51, 38, 55, 36], [51, 42, 55, 36, "useState"], [51, 57, 55, 44], [51, 59, 55, 45], [51, 63, 55, 49], [51, 64, 55, 50], [51, 65, 55, 51], [51, 66, 55, 52], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "hasPermission"], [52, 24, 56, 22], [52, 26, 56, 24, "setHasPermission"], [52, 42, 56, 40], [52, 43, 56, 41], [52, 46, 56, 44], [52, 50, 56, 44, "useState"], [52, 65, 56, 52], [52, 67, 56, 53], [52, 72, 56, 58], [52, 73, 56, 59], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isDictating"], [53, 22, 57, 20], [53, 24, 57, 22, "setIsDictating"], [53, 38, 57, 36], [53, 39, 57, 37], [53, 42, 57, 40], [53, 46, 57, 40, "useState"], [53, 61, 57, 48], [53, 63, 57, 49], [53, 68, 57, 54], [53, 69, 57, 55], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "longPressedMessage"], [54, 29, 58, 27], [54, 31, 58, 29, "setLongPressedMessage"], [54, 52, 58, 50], [54, 53, 58, 51], [54, 56, 58, 54], [54, 60, 58, 54, "useState"], [54, 75, 58, 62], [54, 77, 58, 63], [54, 81, 58, 67], [54, 82, 58, 68], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "isContextMenuVisible"], [55, 31, 59, 29], [55, 33, 59, 31, "setIsContextMenuVisible"], [55, 56, 59, 54], [55, 57, 59, 55], [55, 60, 59, 58], [55, 64, 59, 58, "useState"], [55, 79, 59, 66], [55, 81, 59, 67], [55, 86, 59, 72], [55, 87, 59, 73], [56, 4, 60, 2], [56, 10, 60, 8], [56, 11, 60, 9, "transcript"], [56, 21, 60, 19], [56, 23, 60, 21, "setTranscript"], [56, 36, 60, 34], [56, 37, 60, 35], [56, 40, 60, 38], [56, 44, 60, 38, "useState"], [56, 59, 60, 46], [56, 61, 60, 47], [56, 63, 60, 49], [56, 64, 60, 50], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "isMuted"], [57, 18, 61, 16], [57, 20, 61, 18, "setIsMuted"], [57, 30, 61, 28], [57, 31, 61, 29], [57, 34, 61, 32], [57, 38, 61, 32, "useState"], [57, 53, 61, 40], [57, 55, 61, 41], [57, 60, 61, 46], [57, 61, 61, 47], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "currentSessionId"], [58, 27, 62, 25], [58, 29, 62, 27, "setCurrentSessionId"], [58, 48, 62, 46], [58, 49, 62, 47], [58, 52, 62, 50], [58, 56, 62, 50, "useState"], [58, 71, 62, 58], [58, 73, 62, 59], [58, 77, 62, 63], [58, 78, 62, 64], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "sessionStartTime"], [59, 27, 63, 25], [59, 29, 63, 27, "setSessionStartTime"], [59, 48, 63, 46], [59, 49, 63, 47], [59, 52, 63, 50], [59, 56, 63, 50, "useState"], [59, 71, 63, 58], [59, 73, 63, 59], [59, 77, 63, 63], [59, 78, 63, 64], [60, 4, 65, 2], [60, 10, 65, 8, "scrollViewRef"], [60, 23, 65, 21], [60, 26, 65, 24], [60, 30, 65, 24, "useRef"], [60, 43, 65, 30], [60, 45, 65, 31], [60, 49, 65, 35], [60, 50, 65, 36], [62, 4, 67, 2], [63, 4, 68, 2], [63, 8, 68, 2, "useEffect"], [63, 24, 68, 11], [63, 26, 68, 12], [63, 32, 68, 18], [64, 6, 69, 4], [64, 7, 69, 5], [64, 19, 69, 17], [65, 8, 70, 6], [65, 14, 70, 12], [66, 10, 70, 14, "granted"], [67, 8, 70, 22], [67, 9, 70, 23], [67, 12, 70, 26], [67, 18, 70, 32], [67, 22, 70, 32, "requestRecordingPermissionsAsync"], [67, 65, 70, 64], [67, 67, 70, 65], [67, 68, 70, 66], [68, 8, 71, 6, "setHasPermission"], [68, 24, 71, 22], [68, 25, 71, 23, "granted"], [68, 32, 71, 30], [68, 33, 71, 31], [69, 8, 72, 6], [69, 12, 72, 10], [69, 13, 72, 11, "granted"], [69, 20, 72, 18], [69, 24, 72, 22, "voiceMode"], [69, 33, 72, 31], [69, 35, 72, 33], [70, 10, 73, 8, "<PERSON><PERSON>"], [70, 24, 73, 13], [70, 25, 73, 14, "alert"], [70, 30, 73, 19], [70, 31, 74, 10], [70, 52, 74, 31], [70, 54, 75, 10], [70, 133, 75, 89], [70, 135, 76, 10], [70, 136, 77, 12], [71, 12, 77, 14, "text"], [71, 16, 77, 18], [71, 18, 77, 20], [71, 33, 77, 35], [72, 12, 77, 37, "onPress"], [72, 19, 77, 44], [72, 21, 77, 46, "onPress"], [72, 22, 77, 46], [72, 27, 77, 52, "setVoiceMode"], [72, 39, 77, 64], [72, 40, 77, 65], [72, 45, 77, 70], [73, 10, 77, 72], [73, 11, 77, 73], [73, 13, 78, 12], [74, 12, 78, 14, "text"], [74, 16, 78, 18], [74, 18, 78, 20], [75, 10, 78, 25], [75, 11, 78, 26], [75, 12, 80, 8], [75, 13, 80, 9], [76, 8, 81, 6], [77, 6, 82, 4], [77, 7, 82, 5], [77, 9, 82, 7], [77, 10, 82, 8], [78, 4, 83, 2], [78, 5, 83, 3], [78, 7, 83, 5], [78, 8, 83, 6, "voiceMode"], [78, 17, 83, 15], [78, 18, 83, 16], [78, 19, 83, 17], [79, 4, 85, 2], [79, 10, 85, 8, "handleStartBrainstorming"], [79, 34, 85, 32], [79, 37, 85, 35, "handleStartBrainstorming"], [79, 38, 85, 35], [79, 43, 85, 41], [80, 6, 86, 4, "Haptics"], [80, 13, 86, 11], [80, 14, 86, 12, "impactAsync"], [80, 25, 86, 23], [80, 26, 86, 24, "Haptics"], [80, 33, 86, 31], [80, 34, 86, 32, "ImpactFeedbackStyle"], [80, 53, 86, 51], [80, 54, 86, 52, "Medium"], [80, 60, 86, 58], [80, 61, 86, 59], [81, 6, 87, 4, "setIsFirstTime"], [81, 20, 87, 18], [81, 21, 87, 19], [81, 26, 87, 24], [81, 27, 87, 25], [82, 6, 88, 4, "setMessages"], [82, 17, 88, 15], [82, 18, 88, 16, "fakeMessages"], [82, 40, 88, 28], [82, 41, 88, 29], [83, 6, 89, 4, "setQuickActions"], [83, 21, 89, 19], [83, 22, 89, 20, "fakeQuickActions"], [83, 48, 89, 36], [83, 49, 89, 37], [84, 4, 90, 2], [84, 5, 90, 3], [85, 4, 92, 2], [85, 10, 92, 8, "handleSendMessage"], [85, 27, 92, 25], [85, 30, 92, 28, "handleSendMessage"], [85, 31, 92, 29, "message"], [85, 38, 92, 36], [85, 41, 92, 39, "inputText"], [85, 50, 92, 48], [85, 55, 92, 53], [86, 6, 93, 4], [86, 10, 93, 8], [86, 11, 93, 9, "message"], [86, 18, 93, 16], [86, 19, 93, 17, "trim"], [86, 23, 93, 21], [86, 24, 93, 22], [86, 25, 93, 23], [86, 27, 93, 25], [87, 6, 95, 4], [87, 12, 95, 10, "newUserMessage"], [87, 26, 95, 24], [87, 29, 95, 27], [88, 8, 95, 29, "role"], [88, 12, 95, 33], [88, 14, 95, 35], [88, 20, 95, 41], [89, 8, 95, 43, "content"], [89, 15, 95, 50], [89, 17, 95, 52, "message"], [90, 6, 95, 60], [90, 7, 95, 61], [91, 6, 96, 4, "setMessages"], [91, 17, 96, 15], [91, 18, 96, 17, "prev"], [91, 22, 96, 21], [91, 26, 96, 26], [91, 27, 96, 27], [91, 30, 96, 30, "prev"], [91, 34, 96, 34], [91, 36, 96, 36, "newUserMessage"], [91, 50, 96, 50], [91, 51, 96, 51], [91, 52, 96, 52], [92, 6, 97, 4, "setInputText"], [92, 18, 97, 16], [92, 19, 97, 17], [92, 21, 97, 19], [92, 22, 97, 20], [93, 6, 98, 4, "setQuickActions"], [93, 21, 98, 19], [93, 22, 98, 20], [93, 24, 98, 22], [93, 25, 98, 23], [94, 6, 99, 4, "setIsLoading"], [94, 18, 99, 16], [94, 19, 99, 17], [94, 23, 99, 21], [94, 24, 99, 22], [96, 6, 101, 4], [97, 6, 102, 4, "setTimeout"], [97, 16, 102, 14], [97, 17, 102, 15], [97, 23, 102, 21], [98, 8, 103, 6], [98, 14, 103, 12, "aiResponse"], [98, 24, 103, 22], [98, 27, 103, 25], [99, 10, 104, 8, "role"], [99, 14, 104, 12], [99, 16, 104, 14], [99, 27, 104, 25], [100, 10, 105, 8, "content"], [100, 17, 105, 15], [100, 19, 105, 17], [101, 8, 106, 6], [101, 9, 106, 7], [102, 8, 107, 6, "setMessages"], [102, 19, 107, 17], [102, 20, 107, 19, "prev"], [102, 24, 107, 23], [102, 28, 107, 28], [102, 29, 107, 29], [102, 32, 107, 32, "prev"], [102, 36, 107, 36], [102, 38, 107, 38, "aiResponse"], [102, 48, 107, 48], [102, 49, 107, 49], [102, 50, 107, 50], [103, 8, 108, 6, "setQuickActions"], [103, 23, 108, 21], [103, 24, 108, 22, "fakeQuickActions"], [103, 50, 108, 38], [103, 51, 108, 39], [104, 8, 109, 6, "setIsLoading"], [104, 20, 109, 18], [104, 21, 109, 19], [104, 26, 109, 24], [104, 27, 109, 25], [105, 6, 110, 4], [105, 7, 110, 5], [105, 9, 110, 7], [105, 13, 110, 11], [105, 14, 110, 12], [106, 4, 111, 2], [106, 5, 111, 3], [107, 4, 113, 2], [107, 10, 113, 8, "handleQuickAction"], [107, 27, 113, 25], [107, 30, 113, 29, "action"], [107, 36, 113, 35], [107, 40, 113, 40], [108, 6, 114, 4, "Haptics"], [108, 13, 114, 11], [108, 14, 114, 12, "impactAsync"], [108, 25, 114, 23], [108, 26, 114, 24, "Haptics"], [108, 33, 114, 31], [108, 34, 114, 32, "ImpactFeedbackStyle"], [108, 53, 114, 51], [108, 54, 114, 52, "Light"], [108, 59, 114, 57], [108, 60, 114, 58], [109, 6, 115, 4, "handleSendMessage"], [109, 23, 115, 21], [109, 24, 115, 22, "action"], [109, 30, 115, 28], [109, 31, 115, 29], [110, 4, 116, 2], [110, 5, 116, 3], [111, 4, 118, 2], [111, 10, 118, 8, "toggleVoiceMode"], [111, 25, 118, 23], [111, 28, 118, 26], [111, 34, 118, 26, "toggleVoiceMode"], [111, 35, 118, 26], [111, 40, 118, 38], [112, 6, 119, 4], [112, 10, 119, 8], [112, 11, 119, 9, "voiceMode"], [112, 20, 119, 18], [112, 24, 119, 22], [112, 25, 119, 23, "hasPermission"], [112, 38, 119, 36], [112, 40, 119, 38], [113, 8, 120, 6, "<PERSON><PERSON>"], [113, 22, 120, 11], [113, 23, 120, 12, "alert"], [113, 28, 120, 17], [113, 29, 121, 8], [113, 50, 121, 29], [113, 52, 122, 8], [113, 102, 122, 58], [113, 104, 123, 8], [113, 105, 124, 10], [114, 10, 124, 12, "text"], [114, 14, 124, 16], [114, 16, 124, 18], [115, 8, 124, 27], [115, 9, 124, 28], [115, 11, 125, 10], [116, 10, 126, 12, "text"], [116, 14, 126, 16], [116, 16, 126, 18], [116, 34, 126, 36], [117, 10, 127, 12, "onPress"], [117, 17, 127, 19], [117, 19, 127, 21], [117, 25, 127, 21, "onPress"], [117, 26, 127, 21], [117, 31, 127, 33], [118, 12, 128, 14], [118, 18, 128, 20], [119, 14, 128, 22, "granted"], [120, 12, 128, 30], [120, 13, 128, 31], [120, 16, 128, 34], [120, 22, 128, 40], [120, 26, 128, 40, "requestRecordingPermissionsAsync"], [120, 69, 128, 72], [120, 71, 128, 73], [120, 72, 128, 74], [121, 12, 129, 14], [121, 16, 129, 18, "granted"], [121, 23, 129, 25], [121, 25, 129, 27], [122, 14, 130, 16, "setHasPermission"], [122, 30, 130, 32], [122, 31, 130, 33], [122, 35, 130, 37], [122, 36, 130, 38], [123, 14, 131, 16, "setVoiceMode"], [123, 26, 131, 28], [123, 27, 131, 29], [123, 31, 131, 33], [123, 32, 131, 34], [124, 14, 132, 16], [125, 14, 133, 16], [125, 20, 133, 22, "startVoiceSession"], [125, 37, 133, 39], [125, 38, 133, 40], [125, 39, 133, 41], [126, 12, 134, 14], [127, 10, 135, 12], [128, 8, 136, 10], [128, 9, 136, 11], [128, 10, 138, 6], [128, 11, 138, 7], [129, 8, 139, 6], [130, 6, 140, 4], [131, 6, 142, 4, "Haptics"], [131, 13, 142, 11], [131, 14, 142, 12, "impactAsync"], [131, 25, 142, 23], [131, 26, 142, 24, "Haptics"], [131, 33, 142, 31], [131, 34, 142, 32, "ImpactFeedbackStyle"], [131, 53, 142, 51], [131, 54, 142, 52, "Light"], [131, 59, 142, 57], [131, 60, 142, 58], [132, 6, 144, 4], [132, 10, 144, 8], [132, 11, 144, 9, "voiceMode"], [132, 20, 144, 18], [132, 22, 144, 20], [133, 8, 145, 6], [134, 8, 146, 6, "setVoiceMode"], [134, 20, 146, 18], [134, 21, 146, 19], [134, 25, 146, 23], [134, 26, 146, 24], [135, 8, 147, 6], [135, 12, 147, 10, "hasPermission"], [135, 25, 147, 23], [135, 27, 147, 25], [136, 10, 148, 8], [136, 16, 148, 14, "startVoiceSession"], [136, 33, 148, 31], [136, 34, 148, 32], [136, 35, 148, 33], [137, 8, 149, 6], [138, 6, 150, 4], [138, 7, 150, 5], [138, 13, 150, 11], [139, 8, 151, 6], [140, 8, 152, 6, "setVoiceMode"], [140, 20, 152, 18], [140, 21, 152, 19], [140, 26, 152, 24], [140, 27, 152, 25], [141, 8, 153, 6], [141, 12, 153, 10, "recorderState"], [141, 25, 153, 23], [141, 26, 153, 24, "isRecording"], [141, 37, 153, 35], [141, 39, 153, 37], [142, 10, 154, 8], [142, 16, 154, 14, "recorder"], [142, 24, 154, 22], [142, 25, 154, 23, "stop"], [142, 29, 154, 27], [142, 30, 154, 28], [142, 31, 154, 29], [143, 8, 155, 6], [144, 8, 156, 6, "setIsMuted"], [144, 18, 156, 16], [144, 19, 156, 17], [144, 24, 156, 22], [144, 25, 156, 23], [144, 26, 156, 24], [144, 27, 156, 25], [145, 6, 157, 4], [146, 4, 158, 2], [146, 5, 158, 3], [147, 4, 160, 2], [147, 10, 160, 8, "startVoiceSession"], [147, 27, 160, 25], [147, 30, 160, 28], [147, 36, 160, 28, "startVoiceSession"], [147, 37, 160, 28], [147, 42, 160, 40], [148, 6, 161, 4], [148, 10, 161, 8], [148, 11, 161, 9, "hasPermission"], [148, 24, 161, 22], [148, 26, 161, 24], [149, 6, 163, 4], [149, 10, 163, 8], [150, 8, 164, 6], [150, 14, 164, 12, "recorder"], [150, 22, 164, 20], [150, 23, 164, 21, "prepareToRecordAsync"], [150, 43, 164, 41], [150, 44, 164, 42], [150, 45, 164, 43], [151, 8, 165, 6, "recorder"], [151, 16, 165, 14], [151, 17, 165, 15, "record"], [151, 23, 165, 21], [151, 24, 165, 22], [151, 25, 165, 23], [152, 8, 166, 6, "setIsMuted"], [152, 18, 166, 16], [152, 19, 166, 17], [152, 24, 166, 22], [152, 25, 166, 23], [152, 26, 166, 24], [152, 27, 166, 25], [153, 8, 167, 6, "Haptics"], [153, 15, 167, 13], [153, 16, 167, 14, "impactAsync"], [153, 27, 167, 25], [153, 28, 167, 26, "Haptics"], [153, 35, 167, 33], [153, 36, 167, 34, "ImpactFeedbackStyle"], [153, 55, 167, 53], [153, 56, 167, 54, "Medium"], [153, 62, 167, 60], [153, 63, 167, 61], [154, 6, 168, 4], [154, 7, 168, 5], [154, 8, 168, 6], [154, 15, 168, 13, "error"], [154, 20, 168, 18], [154, 22, 168, 20], [155, 8, 169, 6, "console"], [155, 15, 169, 13], [155, 16, 169, 14, "error"], [155, 21, 169, 19], [155, 22, 169, 20], [155, 53, 169, 51], [155, 55, 169, 53, "error"], [155, 60, 169, 58], [155, 61, 169, 59], [156, 8, 170, 6, "<PERSON><PERSON>"], [156, 22, 170, 11], [156, 23, 170, 12, "alert"], [156, 28, 170, 17], [156, 29, 170, 18], [156, 36, 170, 25], [156, 38, 170, 27], [156, 69, 170, 58], [156, 70, 170, 59], [157, 6, 171, 4], [158, 4, 172, 2], [158, 5, 172, 3], [159, 4, 174, 2], [159, 10, 174, 8, "stopVoiceSession"], [159, 26, 174, 24], [159, 29, 174, 27], [159, 35, 174, 27, "stopVoiceSession"], [159, 36, 174, 27], [159, 41, 174, 39], [160, 6, 175, 4], [160, 10, 175, 8], [161, 8, 176, 6], [161, 12, 176, 10, "recorderState"], [161, 25, 176, 23], [161, 26, 176, 24, "isRecording"], [161, 37, 176, 35], [161, 39, 176, 37], [162, 10, 177, 8], [162, 16, 177, 14, "recorder"], [162, 24, 177, 22], [162, 25, 177, 23, "stop"], [162, 29, 177, 27], [162, 30, 177, 28], [162, 31, 177, 29], [163, 10, 178, 8, "Haptics"], [163, 17, 178, 15], [163, 18, 178, 16, "impactAsync"], [163, 29, 178, 27], [163, 30, 178, 28, "Haptics"], [163, 37, 178, 35], [163, 38, 178, 36, "ImpactFeedbackStyle"], [163, 57, 178, 55], [163, 58, 178, 56, "Medium"], [163, 64, 178, 62], [163, 65, 178, 63], [164, 8, 179, 6], [165, 6, 180, 4], [165, 7, 180, 5], [165, 8, 180, 6], [165, 15, 180, 13, "error"], [165, 20, 180, 18], [165, 22, 180, 20], [166, 8, 181, 6, "console"], [166, 15, 181, 13], [166, 16, 181, 14, "error"], [166, 21, 181, 19], [166, 22, 181, 20], [166, 53, 181, 51], [166, 55, 181, 53, "error"], [166, 60, 181, 58], [166, 61, 181, 59], [167, 6, 182, 4], [168, 4, 183, 2], [168, 5, 183, 3], [169, 4, 185, 2], [169, 10, 185, 8, "handleDictation"], [169, 25, 185, 23], [169, 28, 185, 26], [169, 34, 185, 26, "handleDictation"], [169, 35, 185, 26], [169, 40, 185, 38], [170, 6, 186, 4], [170, 10, 186, 8], [170, 11, 186, 9, "hasPermission"], [170, 24, 186, 22], [170, 26, 186, 24], [171, 8, 187, 6, "<PERSON><PERSON>"], [171, 22, 187, 11], [171, 23, 187, 12, "alert"], [171, 28, 187, 17], [171, 29, 188, 8], [171, 50, 188, 29], [171, 52, 189, 8], [171, 102, 190, 6], [171, 103, 190, 7], [172, 8, 191, 6], [173, 6, 192, 4], [174, 6, 194, 4], [174, 10, 194, 8], [175, 8, 195, 6], [175, 12, 195, 10, "isDictating"], [175, 23, 195, 21], [175, 25, 195, 23], [176, 10, 196, 8], [176, 16, 196, 14, "recorder"], [176, 24, 196, 22], [176, 25, 196, 23, "stop"], [176, 29, 196, 27], [176, 30, 196, 28], [176, 31, 196, 29], [177, 10, 197, 8], [177, 16, 197, 14, "mockTranscript"], [177, 30, 197, 28], [177, 33, 197, 31], [177, 62, 197, 60], [177, 63, 197, 61], [177, 64, 197, 62], [178, 10, 198, 8, "setInputText"], [178, 22, 198, 20], [178, 23, 198, 21, "inputText"], [178, 32, 198, 30], [178, 35, 198, 33, "mockTranscript"], [178, 49, 198, 47], [178, 50, 198, 48], [179, 10, 199, 8, "setIsDictating"], [179, 24, 199, 22], [179, 25, 199, 23], [179, 30, 199, 28], [179, 31, 199, 29], [180, 8, 200, 6], [180, 9, 200, 7], [180, 15, 200, 13], [181, 10, 201, 8], [181, 16, 201, 14, "recorder"], [181, 24, 201, 22], [181, 25, 201, 23, "prepareToRecordAsync"], [181, 45, 201, 43], [181, 46, 201, 44], [181, 47, 201, 45], [182, 10, 202, 8, "recorder"], [182, 18, 202, 16], [182, 19, 202, 17, "record"], [182, 25, 202, 23], [182, 26, 202, 24], [182, 27, 202, 25], [183, 10, 203, 8, "setIsDictating"], [183, 24, 203, 22], [183, 25, 203, 23], [183, 29, 203, 27], [183, 30, 203, 28], [184, 10, 204, 8, "Haptics"], [184, 17, 204, 15], [184, 18, 204, 16, "impactAsync"], [184, 29, 204, 27], [184, 30, 204, 28, "Haptics"], [184, 37, 204, 35], [184, 38, 204, 36, "ImpactFeedbackStyle"], [184, 57, 204, 55], [184, 58, 204, 56, "Medium"], [184, 64, 204, 62], [184, 65, 204, 63], [185, 8, 205, 6], [186, 6, 206, 4], [186, 7, 206, 5], [186, 8, 206, 6], [186, 15, 206, 13, "error"], [186, 20, 206, 18], [186, 22, 206, 20], [187, 8, 207, 6, "console"], [187, 15, 207, 13], [187, 16, 207, 14, "error"], [187, 21, 207, 19], [187, 22, 207, 20], [187, 45, 207, 43], [187, 47, 207, 45, "error"], [187, 52, 207, 50], [187, 53, 207, 51], [188, 8, 208, 6, "<PERSON><PERSON>"], [188, 22, 208, 11], [188, 23, 208, 12, "alert"], [188, 28, 208, 17], [188, 29, 208, 18], [188, 36, 208, 25], [188, 38, 208, 27], [188, 65, 208, 54], [188, 66, 208, 55], [189, 8, 209, 6, "setIsDictating"], [189, 22, 209, 20], [189, 23, 209, 21], [189, 28, 209, 26], [189, 29, 209, 27], [190, 6, 210, 4], [191, 4, 211, 2], [191, 5, 211, 3], [192, 4, 213, 2], [192, 10, 213, 8, "handleLongPress"], [192, 25, 213, 23], [192, 28, 213, 27, "message"], [192, 35, 213, 34], [192, 39, 213, 39], [193, 6, 214, 4, "setLongPressedMessage"], [193, 27, 214, 25], [193, 28, 214, 26, "message"], [193, 35, 214, 33], [193, 36, 214, 34], [194, 6, 215, 4, "setIsContextMenuVisible"], [194, 29, 215, 27], [194, 30, 215, 28], [194, 34, 215, 32], [194, 35, 215, 33], [195, 4, 216, 2], [195, 5, 216, 3], [196, 4, 218, 2], [196, 10, 218, 8, "handleCopyMessage"], [196, 27, 218, 25], [196, 30, 218, 28, "handleCopyMessage"], [196, 31, 218, 28], [196, 36, 218, 34], [197, 6, 219, 4], [197, 10, 219, 8, "longPressedMessage"], [197, 28, 219, 26], [197, 30, 219, 28], [198, 8, 220, 6, "Clipboard"], [198, 26, 220, 15], [198, 27, 220, 16, "setString"], [198, 36, 220, 25], [198, 37, 220, 26, "longPressedMessage"], [198, 55, 220, 44], [198, 56, 220, 45, "content"], [198, 63, 220, 52], [198, 64, 220, 53], [199, 8, 221, 6, "setIsContextMenuVisible"], [199, 31, 221, 29], [199, 32, 221, 30], [199, 37, 221, 35], [199, 38, 221, 36], [200, 8, 222, 6, "setLongPressedMessage"], [200, 29, 222, 27], [200, 30, 222, 28], [200, 34, 222, 32], [200, 35, 222, 33], [201, 6, 223, 4], [202, 4, 224, 2], [202, 5, 224, 3], [203, 4, 226, 2], [203, 10, 226, 8, "handleListenToMessage"], [203, 31, 226, 29], [203, 34, 226, 32, "handleListenToMessage"], [203, 35, 226, 32], [203, 40, 226, 38], [204, 6, 227, 4], [204, 10, 227, 8, "longPressedMessage"], [204, 28, 227, 26], [204, 30, 227, 28], [205, 8, 228, 6], [206, 8, 229, 6, "<PERSON><PERSON>"], [206, 22, 229, 11], [206, 23, 229, 12, "alert"], [206, 28, 229, 17], [206, 29, 229, 18], [206, 51, 229, 40], [206, 53, 229, 42, "longPressedMessage"], [206, 71, 229, 60], [206, 72, 229, 61, "content"], [206, 79, 229, 68], [206, 80, 229, 69], [207, 8, 230, 6, "setIsContextMenuVisible"], [207, 31, 230, 29], [207, 32, 230, 30], [207, 37, 230, 35], [207, 38, 230, 36], [208, 8, 231, 6, "setLongPressedMessage"], [208, 29, 231, 27], [208, 30, 231, 28], [208, 34, 231, 32], [208, 35, 231, 33], [209, 6, 232, 4], [210, 4, 233, 2], [210, 5, 233, 3], [211, 4, 235, 2], [211, 10, 235, 8, "handleMute"], [211, 20, 235, 18], [211, 23, 235, 21], [211, 29, 235, 21, "handleMute"], [211, 30, 235, 21], [211, 35, 235, 33], [212, 6, 236, 4], [212, 10, 236, 8], [212, 11, 236, 9, "hasPermission"], [212, 24, 236, 22], [212, 26, 236, 24], [213, 6, 238, 4], [213, 10, 238, 8], [214, 8, 239, 6], [214, 12, 239, 10, "isMuted"], [214, 19, 239, 17], [214, 21, 239, 19], [215, 10, 240, 8], [216, 10, 241, 8], [216, 14, 241, 12], [216, 15, 241, 13, "recorderState"], [216, 28, 241, 26], [216, 29, 241, 27, "isRecording"], [216, 40, 241, 38], [216, 42, 241, 40], [217, 12, 242, 10], [217, 18, 242, 16, "recorder"], [217, 26, 242, 24], [217, 27, 242, 25, "prepareToRecordAsync"], [217, 47, 242, 45], [217, 48, 242, 46], [217, 49, 242, 47], [218, 12, 243, 10, "recorder"], [218, 20, 243, 18], [218, 21, 243, 19, "record"], [218, 27, 243, 25], [218, 28, 243, 26], [218, 29, 243, 27], [219, 10, 244, 8], [220, 10, 245, 8, "setIsMuted"], [220, 20, 245, 18], [220, 21, 245, 19], [220, 26, 245, 24], [220, 27, 245, 25], [221, 10, 246, 8, "Haptics"], [221, 17, 246, 15], [221, 18, 246, 16, "impactAsync"], [221, 29, 246, 27], [221, 30, 246, 28, "Haptics"], [221, 37, 246, 35], [221, 38, 246, 36, "ImpactFeedbackStyle"], [221, 57, 246, 55], [221, 58, 246, 56, "Light"], [221, 63, 246, 61], [221, 64, 246, 62], [222, 8, 247, 6], [222, 9, 247, 7], [222, 15, 247, 13], [223, 10, 248, 8], [224, 10, 249, 8], [224, 14, 249, 12, "recorderState"], [224, 27, 249, 25], [224, 28, 249, 26, "isRecording"], [224, 39, 249, 37], [224, 41, 249, 39], [225, 12, 250, 10], [225, 18, 250, 16, "recorder"], [225, 26, 250, 24], [225, 27, 250, 25, "stop"], [225, 31, 250, 29], [225, 32, 250, 30], [225, 33, 250, 31], [226, 10, 251, 8], [227, 10, 252, 8, "setIsMuted"], [227, 20, 252, 18], [227, 21, 252, 19], [227, 25, 252, 23], [227, 26, 252, 24], [228, 10, 253, 8, "Haptics"], [228, 17, 253, 15], [228, 18, 253, 16, "impactAsync"], [228, 29, 253, 27], [228, 30, 253, 28, "Haptics"], [228, 37, 253, 35], [228, 38, 253, 36, "ImpactFeedbackStyle"], [228, 57, 253, 55], [228, 58, 253, 56, "Light"], [228, 63, 253, 61], [228, 64, 253, 62], [229, 8, 254, 6], [230, 6, 255, 4], [230, 7, 255, 5], [230, 8, 255, 6], [230, 15, 255, 13, "error"], [230, 20, 255, 18], [230, 22, 255, 20], [231, 8, 256, 6, "console"], [231, 15, 256, 13], [231, 16, 256, 14, "error"], [231, 21, 256, 19], [231, 22, 256, 20], [231, 47, 256, 45], [231, 49, 256, 47, "error"], [231, 54, 256, 52], [231, 55, 256, 53], [232, 6, 257, 4], [233, 4, 258, 2], [233, 5, 258, 3], [235, 4, 261, 2], [236, 4, 262, 2], [236, 8, 262, 2, "useEffect"], [236, 24, 262, 11], [236, 26, 262, 12], [236, 32, 262, 18], [237, 6, 263, 4], [237, 10, 263, 8, "scrollViewRef"], [237, 23, 263, 21], [237, 24, 263, 22, "current"], [237, 31, 263, 29], [237, 33, 263, 31], [238, 8, 264, 6, "scrollViewRef"], [238, 21, 264, 19], [238, 22, 264, 20, "current"], [238, 29, 264, 27], [238, 30, 264, 28, "scrollToEnd"], [238, 41, 264, 39], [238, 42, 264, 40], [239, 10, 264, 42, "animated"], [239, 18, 264, 50], [239, 20, 264, 52], [240, 8, 264, 57], [240, 9, 264, 58], [240, 10, 264, 59], [241, 6, 265, 4], [242, 4, 266, 2], [242, 5, 266, 3], [242, 7, 266, 5], [242, 8, 266, 6, "messages"], [242, 16, 266, 14], [242, 17, 266, 15], [242, 18, 266, 16], [243, 4, 268, 2], [243, 8, 268, 6], [243, 9, 268, 7, "fontsLoaded"], [243, 20, 268, 18], [243, 22, 268, 20], [244, 6, 269, 4], [244, 13, 269, 11], [244, 17, 269, 15], [245, 4, 270, 2], [247, 4, 272, 2], [248, 4, 273, 2], [248, 8, 273, 6, "isFirstTime"], [248, 19, 273, 17], [248, 21, 273, 19], [249, 6, 274, 4], [249, 26, 275, 6], [249, 30, 275, 6, "_jsxDevRuntime"], [249, 44, 275, 6], [249, 45, 275, 6, "jsxDEV"], [249, 51, 275, 6], [249, 53, 275, 7, "_View"], [249, 58, 275, 7], [249, 59, 275, 7, "default"], [249, 66, 275, 11], [250, 8, 275, 12, "style"], [250, 13, 275, 17], [250, 15, 275, 19], [251, 10, 275, 21, "flex"], [251, 14, 275, 25], [251, 16, 275, 27], [251, 17, 275, 28], [252, 10, 275, 30, "backgroundColor"], [252, 25, 275, 45], [252, 27, 275, 47, "colors"], [252, 33, 275, 53], [252, 34, 275, 54, "background"], [253, 8, 275, 65], [253, 9, 275, 67], [254, 8, 275, 67, "children"], [254, 16, 275, 67], [254, 31, 276, 8], [254, 35, 276, 8, "_jsxDevRuntime"], [254, 49, 276, 8], [254, 50, 276, 8, "jsxDEV"], [254, 56, 276, 8], [254, 58, 276, 9, "_View"], [254, 63, 276, 9], [254, 64, 276, 9, "default"], [254, 71, 276, 13], [255, 10, 277, 10, "style"], [255, 15, 277, 15], [255, 17, 277, 17], [256, 12, 278, 12, "flex"], [256, 16, 278, 16], [256, 18, 278, 18], [256, 19, 278, 19], [257, 12, 279, 12, "paddingTop"], [257, 22, 279, 22], [257, 24, 279, 24, "insets"], [257, 30, 279, 30], [257, 31, 279, 31, "top"], [257, 34, 279, 34], [257, 37, 279, 37], [257, 39, 279, 39], [258, 12, 280, 12, "paddingHorizontal"], [258, 29, 280, 29], [258, 31, 280, 31], [258, 33, 280, 33], [259, 12, 281, 12, "paddingBottom"], [259, 25, 281, 25], [259, 27, 281, 27, "insets"], [259, 33, 281, 33], [259, 34, 281, 34, "bottom"], [259, 40, 281, 40], [259, 43, 281, 43], [259, 45, 281, 45], [260, 12, 282, 12, "alignItems"], [260, 22, 282, 22], [260, 24, 282, 24], [260, 32, 282, 32], [261, 12, 283, 12, "justifyContent"], [261, 26, 283, 26], [261, 28, 283, 28], [262, 10, 284, 10], [262, 11, 284, 12], [263, 10, 284, 12, "children"], [263, 18, 284, 12], [263, 34, 286, 10], [263, 38, 286, 10, "_jsxDevRuntime"], [263, 52, 286, 10], [263, 53, 286, 10, "jsxDEV"], [263, 59, 286, 10], [263, 61, 286, 11, "_View"], [263, 66, 286, 11], [263, 67, 286, 11, "default"], [263, 74, 286, 15], [264, 12, 287, 12, "style"], [264, 17, 287, 17], [264, 19, 287, 19], [265, 14, 288, 14, "width"], [265, 19, 288, 19], [265, 21, 288, 21], [265, 24, 288, 24], [266, 14, 289, 14, "height"], [266, 20, 289, 20], [266, 22, 289, 22], [266, 25, 289, 25], [267, 14, 290, 14, "borderRadius"], [267, 26, 290, 26], [267, 28, 290, 28], [267, 30, 290, 30], [268, 14, 291, 14, "backgroundColor"], [268, 29, 291, 29], [268, 31, 291, 31, "colors"], [268, 37, 291, 37], [268, 38, 291, 38, "primaryUltraLight"], [268, 55, 291, 55], [269, 14, 292, 14, "alignItems"], [269, 24, 292, 24], [269, 26, 292, 26], [269, 34, 292, 34], [270, 14, 293, 14, "justifyContent"], [270, 28, 293, 28], [270, 30, 293, 30], [270, 38, 293, 38], [271, 14, 294, 14, "marginBottom"], [271, 26, 294, 26], [271, 28, 294, 28], [272, 12, 295, 12], [272, 13, 295, 14], [273, 12, 295, 14, "children"], [273, 20, 295, 14], [273, 35, 296, 12], [273, 39, 296, 12, "_jsxDevRuntime"], [273, 53, 296, 12], [273, 54, 296, 12, "jsxDEV"], [273, 60, 296, 12], [273, 62, 296, 13, "_lucideReactNative"], [273, 80, 296, 13], [273, 81, 296, 13, "MessageSquare"], [273, 94, 296, 26], [274, 14, 296, 27, "size"], [274, 18, 296, 31], [274, 20, 296, 33], [274, 22, 296, 36], [275, 14, 296, 37, "color"], [275, 19, 296, 42], [275, 21, 296, 44, "colors"], [275, 27, 296, 50], [275, 28, 296, 51, "primary"], [276, 12, 296, 59], [277, 14, 296, 59, "fileName"], [277, 22, 296, 59], [277, 24, 296, 59, "_jsxFileName"], [277, 36, 296, 59], [278, 14, 296, 59, "lineNumber"], [278, 24, 296, 59], [279, 14, 296, 59, "columnNumber"], [279, 26, 296, 59], [280, 12, 296, 59], [280, 19, 296, 61], [281, 10, 296, 62], [282, 12, 296, 62, "fileName"], [282, 20, 296, 62], [282, 22, 296, 62, "_jsxFileName"], [282, 34, 296, 62], [283, 12, 296, 62, "lineNumber"], [283, 22, 296, 62], [284, 12, 296, 62, "columnNumber"], [284, 24, 296, 62], [285, 10, 296, 62], [285, 17, 297, 16], [285, 18, 297, 17], [285, 33, 300, 10], [285, 37, 300, 10, "_jsxDevRuntime"], [285, 51, 300, 10], [285, 52, 300, 10, "jsxDEV"], [285, 58, 300, 10], [285, 60, 300, 11, "_Text"], [285, 65, 300, 11], [285, 66, 300, 11, "default"], [285, 73, 300, 15], [286, 12, 301, 12, "style"], [286, 17, 301, 17], [286, 19, 301, 19], [287, 14, 302, 14, "fontSize"], [287, 22, 302, 22], [287, 24, 302, 24], [287, 26, 302, 26], [288, 14, 303, 14, "fontFamily"], [288, 24, 303, 24], [288, 26, 303, 26], [288, 47, 303, 47], [289, 14, 304, 14, "color"], [289, 19, 304, 19], [289, 21, 304, 21, "colors"], [289, 27, 304, 27], [289, 28, 304, 28, "text"], [289, 32, 304, 32], [290, 14, 305, 14, "textAlign"], [290, 23, 305, 23], [290, 25, 305, 25], [290, 33, 305, 33], [291, 14, 306, 14, "marginBottom"], [291, 26, 306, 26], [291, 28, 306, 28], [292, 12, 307, 12], [292, 13, 307, 14], [293, 12, 307, 14, "children"], [293, 20, 307, 14], [293, 22, 307, 15], [294, 10, 309, 10], [295, 12, 309, 10, "fileName"], [295, 20, 309, 10], [295, 22, 309, 10, "_jsxFileName"], [295, 34, 309, 10], [296, 12, 309, 10, "lineNumber"], [296, 22, 309, 10], [297, 12, 309, 10, "columnNumber"], [297, 24, 309, 10], [298, 10, 309, 10], [298, 17, 309, 16], [298, 18, 309, 17], [298, 33, 312, 10], [298, 37, 312, 10, "_jsxDevRuntime"], [298, 51, 312, 10], [298, 52, 312, 10, "jsxDEV"], [298, 58, 312, 10], [298, 60, 312, 11, "_Text"], [298, 65, 312, 11], [298, 66, 312, 11, "default"], [298, 73, 312, 15], [299, 12, 313, 12, "style"], [299, 17, 313, 17], [299, 19, 313, 19], [300, 14, 314, 14, "fontSize"], [300, 22, 314, 22], [300, 24, 314, 24], [300, 26, 314, 26], [301, 14, 315, 14, "fontFamily"], [301, 24, 315, 24], [301, 26, 315, 26], [301, 46, 315, 46], [302, 14, 316, 14, "color"], [302, 19, 316, 19], [302, 21, 316, 21, "colors"], [302, 27, 316, 27], [302, 28, 316, 28, "textSecondary"], [302, 41, 316, 41], [303, 14, 317, 14, "textAlign"], [303, 23, 317, 23], [303, 25, 317, 25], [303, 33, 317, 33], [304, 14, 318, 14, "lineHeight"], [304, 24, 318, 24], [304, 26, 318, 26], [304, 28, 318, 28], [305, 14, 319, 14, "marginBottom"], [305, 26, 319, 26], [305, 28, 319, 28], [306, 12, 320, 12], [306, 13, 320, 14], [307, 12, 320, 14, "children"], [307, 20, 320, 14], [307, 22, 320, 15], [308, 10, 323, 10], [309, 12, 323, 10, "fileName"], [309, 20, 323, 10], [309, 22, 323, 10, "_jsxFileName"], [309, 34, 323, 10], [310, 12, 323, 10, "lineNumber"], [310, 22, 323, 10], [311, 12, 323, 10, "columnNumber"], [311, 24, 323, 10], [312, 10, 323, 10], [312, 17, 323, 16], [312, 18, 323, 17], [312, 33, 326, 10], [312, 37, 326, 10, "_jsxDevRuntime"], [312, 51, 326, 10], [312, 52, 326, 10, "jsxDEV"], [312, 58, 326, 10], [312, 60, 326, 11, "_TouchableOpacity"], [312, 77, 326, 11], [312, 78, 326, 11, "default"], [312, 85, 326, 27], [313, 12, 327, 12, "style"], [313, 17, 327, 17], [313, 19, 327, 19], [314, 14, 328, 14, "backgroundColor"], [314, 29, 328, 29], [314, 31, 328, 31, "colors"], [314, 37, 328, 37], [314, 38, 328, 38, "primary"], [314, 45, 328, 45], [315, 14, 329, 14, "borderRadius"], [315, 26, 329, 26], [315, 28, 329, 28], [315, 30, 329, 30], [316, 14, 330, 14, "paddingHorizontal"], [316, 31, 330, 31], [316, 33, 330, 33], [316, 35, 330, 35], [317, 14, 331, 14, "paddingVertical"], [317, 29, 331, 29], [317, 31, 331, 31], [317, 33, 331, 33], [318, 14, 332, 14, "min<PERSON><PERSON><PERSON>"], [318, 22, 332, 22], [318, 24, 332, 24], [318, 27, 332, 27], [319, 14, 333, 14, "alignItems"], [319, 24, 333, 24], [319, 26, 333, 26], [320, 12, 334, 12], [320, 13, 334, 14], [321, 12, 335, 12, "onPress"], [321, 19, 335, 19], [321, 21, 335, 21, "handleStartBrainstorming"], [321, 45, 335, 46], [322, 12, 335, 46, "children"], [322, 20, 335, 46], [322, 35, 336, 12], [322, 39, 336, 12, "_jsxDevRuntime"], [322, 53, 336, 12], [322, 54, 336, 12, "jsxDEV"], [322, 60, 336, 12], [322, 62, 336, 13, "_Text"], [322, 67, 336, 13], [322, 68, 336, 13, "default"], [322, 75, 336, 17], [323, 14, 337, 14, "style"], [323, 19, 337, 19], [323, 21, 337, 21], [324, 16, 338, 16, "fontSize"], [324, 24, 338, 24], [324, 26, 338, 26], [324, 28, 338, 28], [325, 16, 339, 16, "fontFamily"], [325, 26, 339, 26], [325, 28, 339, 28], [325, 49, 339, 49], [326, 16, 340, 16, "color"], [326, 21, 340, 21], [326, 23, 340, 23, "colors"], [326, 29, 340, 29], [326, 30, 340, 30, "background"], [327, 14, 341, 14], [327, 15, 341, 16], [328, 14, 341, 16, "children"], [328, 22, 341, 16], [328, 24, 341, 17], [329, 12, 343, 12], [330, 14, 343, 12, "fileName"], [330, 22, 343, 12], [330, 24, 343, 12, "_jsxFileName"], [330, 36, 343, 12], [331, 14, 343, 12, "lineNumber"], [331, 24, 343, 12], [332, 14, 343, 12, "columnNumber"], [332, 26, 343, 12], [333, 12, 343, 12], [333, 19, 343, 18], [334, 10, 343, 19], [335, 12, 343, 19, "fileName"], [335, 20, 343, 19], [335, 22, 343, 19, "_jsxFileName"], [335, 34, 343, 19], [336, 12, 343, 19, "lineNumber"], [336, 22, 343, 19], [337, 12, 343, 19, "columnNumber"], [337, 24, 343, 19], [338, 10, 343, 19], [338, 17, 344, 28], [338, 18, 344, 29], [339, 8, 344, 29], [340, 10, 344, 29, "fileName"], [340, 18, 344, 29], [340, 20, 344, 29, "_jsxFileName"], [340, 32, 344, 29], [341, 10, 344, 29, "lineNumber"], [341, 20, 344, 29], [342, 10, 344, 29, "columnNumber"], [342, 22, 344, 29], [343, 8, 344, 29], [343, 15, 345, 14], [344, 6, 345, 15], [345, 8, 345, 15, "fileName"], [345, 16, 345, 15], [345, 18, 345, 15, "_jsxFileName"], [345, 30, 345, 15], [346, 8, 345, 15, "lineNumber"], [346, 18, 345, 15], [347, 8, 345, 15, "columnNumber"], [347, 20, 345, 15], [348, 6, 345, 15], [348, 13, 346, 12], [348, 14, 346, 13], [349, 4, 348, 2], [351, 4, 350, 2], [352, 4, 351, 2], [352, 24, 352, 4], [352, 28, 352, 4, "_jsxDevRuntime"], [352, 42, 352, 4], [352, 43, 352, 4, "jsxDEV"], [352, 49, 352, 4], [352, 51, 352, 5, "_View"], [352, 56, 352, 5], [352, 57, 352, 5, "default"], [352, 64, 352, 9], [353, 6, 352, 10, "style"], [353, 11, 352, 15], [353, 13, 352, 17], [354, 8, 352, 19, "flex"], [354, 12, 352, 23], [354, 14, 352, 25], [354, 15, 352, 26], [355, 8, 352, 28, "backgroundColor"], [355, 23, 352, 43], [355, 25, 352, 45, "colors"], [355, 31, 352, 51], [355, 32, 352, 52, "background"], [356, 6, 352, 63], [356, 7, 352, 65], [357, 6, 352, 65, "children"], [357, 14, 352, 65], [357, 30, 353, 6], [357, 34, 353, 6, "_jsxDevRuntime"], [357, 48, 353, 6], [357, 49, 353, 6, "jsxDEV"], [357, 55, 353, 6], [357, 57, 353, 7, "_Header"], [357, 64, 353, 7], [357, 65, 353, 7, "default"], [357, 72, 353, 13], [358, 8, 354, 8, "voiceMode"], [358, 17, 354, 17], [358, 19, 354, 19, "voiceMode"], [358, 28, 354, 29], [359, 8, 355, 8, "onToggleVoiceMode"], [359, 25, 355, 25], [359, 27, 355, 27, "toggleVoiceMode"], [359, 42, 355, 43], [360, 8, 356, 8, "onDone"], [360, 14, 356, 14], [360, 16, 356, 16, "onDone"], [360, 17, 356, 16], [360, 22, 356, 22, "<PERSON><PERSON>"], [360, 36, 356, 27], [360, 37, 356, 28, "alert"], [360, 42, 356, 33], [360, 43, 356, 34], [360, 57, 356, 48], [361, 6, 356, 50], [362, 8, 356, 50, "fileName"], [362, 16, 356, 50], [362, 18, 356, 50, "_jsxFileName"], [362, 30, 356, 50], [363, 8, 356, 50, "lineNumber"], [363, 18, 356, 50], [364, 8, 356, 50, "columnNumber"], [364, 20, 356, 50], [365, 6, 356, 50], [365, 13, 357, 7], [365, 14, 357, 8], [365, 16, 358, 7, "voiceMode"], [365, 25, 358, 16], [365, 41, 359, 8], [365, 45, 359, 8, "_jsxDevRuntime"], [365, 59, 359, 8], [365, 60, 359, 8, "jsxDEV"], [365, 66, 359, 8], [365, 68, 359, 9, "_VoiceMode"], [365, 78, 359, 9], [365, 79, 359, 9, "default"], [365, 86, 359, 18], [366, 8, 360, 10, "isRecording"], [366, 19, 360, 21], [366, 21, 360, 23, "recorderState"], [366, 34, 360, 36], [366, 35, 360, 37, "isRecording"], [366, 46, 360, 49], [367, 8, 361, 10, "hasPermission"], [367, 21, 361, 23], [367, 23, 361, 25, "hasPermission"], [367, 36, 361, 39], [368, 8, 362, 10, "isLoading"], [368, 17, 362, 19], [368, 19, 362, 21, "isLoading"], [368, 28, 362, 31], [369, 8, 363, 10, "transcript"], [369, 18, 363, 20], [369, 20, 363, 22, "transcript"], [369, 30, 363, 33], [370, 8, 364, 10, "isMuted"], [370, 15, 364, 17], [370, 17, 364, 19, "isMuted"], [370, 24, 364, 27], [371, 8, 365, 10, "onMute"], [371, 14, 365, 16], [371, 16, 365, 18, "handleMute"], [372, 6, 365, 29], [373, 8, 365, 29, "fileName"], [373, 16, 365, 29], [373, 18, 365, 29, "_jsxFileName"], [373, 30, 365, 29], [374, 8, 365, 29, "lineNumber"], [374, 18, 365, 29], [375, 8, 365, 29, "columnNumber"], [375, 20, 365, 29], [376, 6, 365, 29], [376, 13, 366, 9], [376, 14, 366, 10], [376, 30, 368, 8], [376, 34, 368, 8, "_jsxDevRuntime"], [376, 48, 368, 8], [376, 49, 368, 8, "jsxDEV"], [376, 55, 368, 8], [376, 57, 368, 9, "_KeyboardAvoidingAnimatedView"], [376, 86, 368, 9], [376, 87, 368, 9, "default"], [376, 94, 368, 37], [377, 8, 368, 38, "style"], [377, 13, 368, 43], [377, 15, 368, 45], [378, 10, 368, 47, "flex"], [378, 14, 368, 51], [378, 16, 368, 53], [379, 8, 368, 55], [379, 9, 368, 57], [380, 8, 368, 57, "children"], [380, 16, 368, 57], [380, 32, 370, 10], [380, 36, 370, 10, "_jsxDevRuntime"], [380, 50, 370, 10], [380, 51, 370, 10, "jsxDEV"], [380, 57, 370, 10], [380, 59, 370, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [380, 70, 370, 11], [380, 71, 370, 11, "default"], [380, 78, 370, 21], [381, 10, 371, 12, "ref"], [381, 13, 371, 15], [381, 15, 371, 17, "scrollViewRef"], [381, 28, 371, 31], [382, 10, 372, 12, "style"], [382, 15, 372, 17], [382, 17, 372, 19], [383, 12, 372, 21, "flex"], [383, 16, 372, 25], [383, 18, 372, 27], [384, 10, 372, 29], [384, 11, 372, 31], [385, 10, 373, 12, "contentContainerStyle"], [385, 31, 373, 33], [385, 33, 373, 35], [386, 12, 374, 14, "paddingHorizontal"], [386, 29, 374, 31], [386, 31, 374, 33], [386, 33, 374, 35], [387, 12, 375, 14, "paddingVertical"], [387, 27, 375, 29], [387, 29, 375, 31], [387, 31, 375, 33], [388, 12, 376, 14, "paddingBottom"], [388, 25, 376, 27], [388, 27, 376, 29], [388, 30, 376, 32], [388, 31, 376, 34], [389, 10, 377, 12], [389, 11, 377, 14], [390, 10, 378, 12, "showsVerticalScrollIndicator"], [390, 38, 378, 40], [390, 40, 378, 42], [390, 45, 378, 48], [391, 10, 378, 48, "children"], [391, 18, 378, 48], [391, 21, 379, 13, "messages"], [391, 29, 379, 21], [391, 30, 379, 22, "map"], [391, 33, 379, 25], [391, 34, 379, 26], [391, 35, 379, 27, "message"], [391, 42, 379, 34], [391, 44, 379, 36, "index"], [391, 49, 379, 41], [391, 67, 380, 14], [391, 71, 380, 14, "_jsxDevRuntime"], [391, 85, 380, 14], [391, 86, 380, 14, "jsxDEV"], [391, 92, 380, 14], [391, 94, 380, 15, "_MessageBubble"], [391, 108, 380, 15], [391, 109, 380, 15, "default"], [391, 116, 380, 28], [392, 12, 380, 41, "message"], [392, 19, 380, 48], [392, 21, 380, 50, "message"], [392, 28, 380, 58], [393, 12, 380, 59, "onLongPress"], [393, 23, 380, 70], [393, 25, 380, 72, "handleLongPress"], [394, 10, 380, 88], [394, 13, 380, 34, "index"], [394, 18, 380, 39], [395, 12, 380, 39, "fileName"], [395, 20, 380, 39], [395, 22, 380, 39, "_jsxFileName"], [395, 34, 380, 39], [396, 12, 380, 39, "lineNumber"], [396, 22, 380, 39], [397, 12, 380, 39, "columnNumber"], [397, 24, 380, 39], [398, 10, 380, 39], [398, 17, 380, 90], [398, 18, 381, 13], [398, 19, 381, 14], [398, 21, 384, 13, "isLoading"], [398, 30, 384, 22], [398, 47, 385, 14], [398, 51, 385, 14, "_jsxDevRuntime"], [398, 65, 385, 14], [398, 66, 385, 14, "jsxDEV"], [398, 72, 385, 14], [398, 74, 385, 15, "_View"], [398, 79, 385, 15], [398, 80, 385, 15, "default"], [398, 87, 385, 19], [399, 12, 386, 16, "style"], [399, 17, 386, 21], [399, 19, 386, 23], [400, 14, 387, 18, "marginBottom"], [400, 26, 387, 30], [400, 28, 387, 32], [400, 30, 387, 34], [401, 14, 388, 18, "alignSelf"], [401, 23, 388, 27], [401, 25, 388, 29], [401, 37, 388, 41], [402, 14, 389, 18, "max<PERSON><PERSON><PERSON>"], [402, 22, 389, 26], [402, 24, 389, 28], [403, 12, 390, 16], [403, 13, 390, 18], [404, 12, 390, 18, "children"], [404, 20, 390, 18], [404, 35, 391, 16], [404, 39, 391, 16, "_jsxDevRuntime"], [404, 53, 391, 16], [404, 54, 391, 16, "jsxDEV"], [404, 60, 391, 16], [404, 62, 391, 17, "_View"], [404, 67, 391, 17], [404, 68, 391, 17, "default"], [404, 75, 391, 21], [405, 14, 392, 18, "style"], [405, 19, 392, 23], [405, 21, 392, 25], [406, 16, 393, 20, "backgroundColor"], [406, 31, 393, 35], [406, 33, 393, 37, "colors"], [406, 39, 393, 43], [406, 40, 393, 44, "cardBackground"], [406, 54, 393, 58], [407, 16, 394, 20, "borderRadius"], [407, 28, 394, 32], [407, 30, 394, 34], [407, 32, 394, 36], [408, 16, 395, 20, "paddingHorizontal"], [408, 33, 395, 37], [408, 35, 395, 39], [408, 37, 395, 41], [409, 16, 396, 20, "paddingVertical"], [409, 31, 396, 35], [409, 33, 396, 37], [409, 35, 396, 39], [410, 16, 397, 20, "borderWidth"], [410, 27, 397, 31], [410, 29, 397, 33], [410, 30, 397, 34], [411, 16, 398, 20, "borderColor"], [411, 27, 398, 31], [411, 29, 398, 33, "colors"], [411, 35, 398, 39], [411, 36, 398, 40, "outline"], [412, 14, 399, 18], [412, 15, 399, 20], [413, 14, 399, 20, "children"], [413, 22, 399, 20], [413, 37, 400, 18], [413, 41, 400, 18, "_jsxDevRuntime"], [413, 55, 400, 18], [413, 56, 400, 18, "jsxDEV"], [413, 62, 400, 18], [413, 64, 400, 19, "_Text"], [413, 69, 400, 19], [413, 70, 400, 19, "default"], [413, 77, 400, 23], [414, 16, 401, 20, "style"], [414, 21, 401, 25], [414, 23, 401, 27], [415, 18, 402, 22, "fontSize"], [415, 26, 402, 30], [415, 28, 402, 32], [415, 30, 402, 34], [416, 18, 403, 22, "fontFamily"], [416, 28, 403, 32], [416, 30, 403, 34], [416, 50, 403, 54], [417, 18, 404, 22, "color"], [417, 23, 404, 27], [417, 25, 404, 29, "colors"], [417, 31, 404, 35], [417, 32, 404, 36, "textSecondary"], [417, 45, 404, 49], [418, 18, 405, 22, "lineHeight"], [418, 28, 405, 32], [418, 30, 405, 34], [419, 16, 406, 20], [419, 17, 406, 22], [420, 16, 406, 22, "children"], [420, 24, 406, 22], [420, 26, 406, 23], [421, 14, 408, 18], [422, 16, 408, 18, "fileName"], [422, 24, 408, 18], [422, 26, 408, 18, "_jsxFileName"], [422, 38, 408, 18], [423, 16, 408, 18, "lineNumber"], [423, 26, 408, 18], [424, 16, 408, 18, "columnNumber"], [424, 28, 408, 18], [425, 14, 408, 18], [425, 21, 408, 24], [426, 12, 408, 25], [427, 14, 408, 25, "fileName"], [427, 22, 408, 25], [427, 24, 408, 25, "_jsxFileName"], [427, 36, 408, 25], [428, 14, 408, 25, "lineNumber"], [428, 24, 408, 25], [429, 14, 408, 25, "columnNumber"], [429, 26, 408, 25], [430, 12, 408, 25], [430, 19, 409, 22], [431, 10, 409, 23], [432, 12, 409, 23, "fileName"], [432, 20, 409, 23], [432, 22, 409, 23, "_jsxFileName"], [432, 34, 409, 23], [433, 12, 409, 23, "lineNumber"], [433, 22, 409, 23], [434, 12, 409, 23, "columnNumber"], [434, 24, 409, 23], [435, 10, 409, 23], [435, 17, 410, 20], [435, 18, 411, 13], [436, 8, 411, 13], [437, 10, 411, 13, "fileName"], [437, 18, 411, 13], [437, 20, 411, 13, "_jsxFileName"], [437, 32, 411, 13], [438, 10, 411, 13, "lineNumber"], [438, 20, 411, 13], [439, 10, 411, 13, "columnNumber"], [439, 22, 411, 13], [440, 8, 411, 13], [440, 15, 412, 22], [440, 16, 412, 23], [440, 18, 415, 11, "quickActions"], [440, 30, 415, 23], [440, 31, 415, 24, "length"], [440, 37, 415, 30], [440, 40, 415, 33], [440, 41, 415, 34], [440, 58, 416, 12], [440, 62, 416, 12, "_jsxDevRuntime"], [440, 76, 416, 12], [440, 77, 416, 12, "jsxDEV"], [440, 83, 416, 12], [440, 85, 416, 13, "_View"], [440, 90, 416, 13], [440, 91, 416, 13, "default"], [440, 98, 416, 17], [441, 10, 417, 14, "style"], [441, 15, 417, 19], [441, 17, 417, 21], [442, 12, 418, 16, "position"], [442, 20, 418, 24], [442, 22, 418, 26], [442, 32, 418, 36], [443, 12, 419, 16, "bottom"], [443, 18, 419, 22], [443, 20, 419, 24, "insets"], [443, 26, 419, 30], [443, 27, 419, 31, "bottom"], [443, 33, 419, 37], [443, 36, 419, 40], [443, 39, 419, 43], [444, 12, 420, 16, "left"], [444, 16, 420, 20], [444, 18, 420, 22], [444, 20, 420, 24], [445, 12, 421, 16, "right"], [445, 17, 421, 21], [445, 19, 421, 23], [446, 10, 422, 14], [446, 11, 422, 16], [447, 10, 422, 16, "children"], [447, 18, 422, 16], [447, 33, 423, 14], [447, 37, 423, 14, "_jsxDevRuntime"], [447, 51, 423, 14], [447, 52, 423, 14, "jsxDEV"], [447, 58, 423, 14], [447, 60, 423, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [447, 71, 423, 15], [447, 72, 423, 15, "default"], [447, 79, 423, 25], [448, 12, 424, 16, "horizontal"], [448, 22, 424, 26], [449, 12, 425, 16, "showsHorizontalScrollIndicator"], [449, 42, 425, 46], [449, 44, 425, 48], [449, 49, 425, 54], [450, 12, 426, 16, "contentContainerStyle"], [450, 33, 426, 37], [450, 35, 426, 39], [451, 14, 426, 41, "gap"], [451, 17, 426, 44], [451, 19, 426, 46], [452, 12, 426, 48], [452, 13, 426, 50], [453, 12, 426, 50, "children"], [453, 20, 426, 50], [453, 22, 427, 17, "quickActions"], [453, 34, 427, 29], [453, 35, 427, 30, "map"], [453, 38, 427, 33], [453, 39, 427, 34], [453, 40, 427, 35, "action"], [453, 46, 427, 41], [453, 48, 427, 43, "index"], [453, 53, 427, 48], [453, 71, 428, 18], [453, 75, 428, 18, "_jsxDevRuntime"], [453, 89, 428, 18], [453, 90, 428, 18, "jsxDEV"], [453, 96, 428, 18], [453, 98, 428, 19, "_TouchableOpacity"], [453, 115, 428, 19], [453, 116, 428, 19, "default"], [453, 123, 428, 35], [454, 14, 430, 20, "style"], [454, 19, 430, 25], [454, 21, 430, 27], [455, 16, 431, 22, "backgroundColor"], [455, 31, 431, 37], [455, 33, 431, 39, "colors"], [455, 39, 431, 45], [455, 40, 431, 46, "primaryUltraLight"], [455, 57, 431, 63], [456, 16, 432, 22, "borderRadius"], [456, 28, 432, 34], [456, 30, 432, 36], [456, 32, 432, 38], [457, 16, 433, 22, "paddingHorizontal"], [457, 33, 433, 39], [457, 35, 433, 41], [457, 37, 433, 43], [458, 16, 434, 22, "paddingVertical"], [458, 31, 434, 37], [458, 33, 434, 39], [458, 34, 434, 40], [459, 16, 435, 22, "borderWidth"], [459, 27, 435, 33], [459, 29, 435, 35], [459, 30, 435, 36], [460, 16, 436, 22, "borderColor"], [460, 27, 436, 33], [460, 29, 436, 35, "colors"], [460, 35, 436, 41], [460, 36, 436, 42, "primary"], [461, 14, 437, 20], [461, 15, 437, 22], [462, 14, 438, 20, "onPress"], [462, 21, 438, 27], [462, 23, 438, 29, "onPress"], [462, 24, 438, 29], [462, 29, 438, 35, "handleQuickAction"], [462, 46, 438, 52], [462, 47, 438, 53, "action"], [462, 53, 438, 59], [462, 54, 438, 61], [463, 14, 438, 61, "children"], [463, 22, 438, 61], [463, 37, 439, 20], [463, 41, 439, 20, "_jsxDevRuntime"], [463, 55, 439, 20], [463, 56, 439, 20, "jsxDEV"], [463, 62, 439, 20], [463, 64, 439, 21, "_Text"], [463, 69, 439, 21], [463, 70, 439, 21, "default"], [463, 77, 439, 25], [464, 16, 440, 22, "style"], [464, 21, 440, 27], [464, 23, 440, 29], [465, 18, 441, 24, "fontSize"], [465, 26, 441, 32], [465, 28, 441, 34], [465, 30, 441, 36], [466, 18, 442, 24, "fontFamily"], [466, 28, 442, 34], [466, 30, 442, 36], [466, 49, 442, 55], [467, 18, 443, 24, "color"], [467, 23, 443, 29], [467, 25, 443, 31, "colors"], [467, 31, 443, 37], [467, 32, 443, 38, "primary"], [468, 16, 444, 22], [468, 17, 444, 24], [469, 16, 444, 24, "children"], [469, 24, 444, 24], [469, 26, 445, 23, "action"], [470, 14, 445, 29], [471, 16, 445, 29, "fileName"], [471, 24, 445, 29], [471, 26, 445, 29, "_jsxFileName"], [471, 38, 445, 29], [472, 16, 445, 29, "lineNumber"], [472, 26, 445, 29], [473, 16, 445, 29, "columnNumber"], [473, 28, 445, 29], [474, 14, 445, 29], [474, 21, 446, 26], [475, 12, 446, 27], [475, 15, 429, 25, "index"], [475, 20, 429, 30], [476, 14, 429, 30, "fileName"], [476, 22, 429, 30], [476, 24, 429, 30, "_jsxFileName"], [476, 36, 429, 30], [477, 14, 429, 30, "lineNumber"], [477, 24, 429, 30], [478, 14, 429, 30, "columnNumber"], [478, 26, 429, 30], [479, 12, 429, 30], [479, 19, 447, 36], [479, 20, 448, 17], [480, 10, 448, 18], [481, 12, 448, 18, "fileName"], [481, 20, 448, 18], [481, 22, 448, 18, "_jsxFileName"], [481, 34, 448, 18], [482, 12, 448, 18, "lineNumber"], [482, 22, 448, 18], [483, 12, 448, 18, "columnNumber"], [483, 24, 448, 18], [484, 10, 448, 18], [484, 17, 449, 26], [485, 8, 449, 27], [486, 10, 449, 27, "fileName"], [486, 18, 449, 27], [486, 20, 449, 27, "_jsxFileName"], [486, 32, 449, 27], [487, 10, 449, 27, "lineNumber"], [487, 20, 449, 27], [488, 10, 449, 27, "columnNumber"], [488, 22, 449, 27], [489, 8, 449, 27], [489, 15, 450, 18], [489, 16, 451, 11], [489, 31, 454, 10], [489, 35, 454, 10, "_jsxDevRuntime"], [489, 49, 454, 10], [489, 50, 454, 10, "jsxDEV"], [489, 56, 454, 10], [489, 58, 454, 11, "_View"], [489, 63, 454, 11], [489, 64, 454, 11, "default"], [489, 71, 454, 15], [490, 10, 455, 12, "style"], [490, 15, 455, 17], [490, 17, 455, 19], [491, 12, 456, 14, "position"], [491, 20, 456, 22], [491, 22, 456, 24], [491, 32, 456, 34], [492, 12, 457, 14, "bottom"], [492, 18, 457, 20], [492, 20, 457, 22], [492, 21, 457, 23], [493, 12, 458, 14, "left"], [493, 16, 458, 18], [493, 18, 458, 20], [493, 19, 458, 21], [494, 12, 459, 14, "right"], [494, 17, 459, 19], [494, 19, 459, 21], [495, 10, 460, 12], [495, 11, 460, 14], [496, 10, 460, 14, "children"], [496, 18, 460, 14], [496, 33, 461, 12], [496, 37, 461, 12, "_jsxDevRuntime"], [496, 51, 461, 12], [496, 52, 461, 12, "jsxDEV"], [496, 58, 461, 12], [496, 60, 461, 13, "_TextMode"], [496, 69, 461, 13], [496, 70, 461, 13, "default"], [496, 77, 461, 21], [497, 12, 462, 14, "inputText"], [497, 21, 462, 23], [497, 23, 462, 25, "inputText"], [497, 32, 462, 35], [498, 12, 463, 14, "onInputChange"], [498, 25, 463, 27], [498, 27, 463, 29, "setInputText"], [498, 39, 463, 42], [499, 12, 464, 14, "onSendMessage"], [499, 25, 464, 27], [499, 27, 464, 29, "onSendMessage"], [499, 28, 464, 29], [499, 33, 464, 35, "handleSendMessage"], [499, 50, 464, 52], [499, 51, 464, 53], [499, 52, 464, 55], [500, 12, 465, 14, "onStartDictation"], [500, 28, 465, 30], [500, 30, 465, 32, "handleDictation"], [501, 10, 465, 48], [502, 12, 465, 48, "fileName"], [502, 20, 465, 48], [502, 22, 465, 48, "_jsxFileName"], [502, 34, 465, 48], [503, 12, 465, 48, "lineNumber"], [503, 22, 465, 48], [504, 12, 465, 48, "columnNumber"], [504, 24, 465, 48], [505, 10, 465, 48], [505, 17, 466, 13], [506, 8, 466, 14], [507, 10, 466, 14, "fileName"], [507, 18, 466, 14], [507, 20, 466, 14, "_jsxFileName"], [507, 32, 466, 14], [508, 10, 466, 14, "lineNumber"], [508, 20, 466, 14], [509, 10, 466, 14, "columnNumber"], [509, 22, 466, 14], [510, 8, 466, 14], [510, 15, 467, 16], [510, 16, 467, 17], [511, 6, 467, 17], [512, 8, 467, 17, "fileName"], [512, 16, 467, 17], [512, 18, 467, 17, "_jsxFileName"], [512, 30, 467, 17], [513, 8, 467, 17, "lineNumber"], [513, 18, 467, 17], [514, 8, 467, 17, "columnNumber"], [514, 20, 467, 17], [515, 6, 467, 17], [515, 13, 468, 38], [515, 14, 469, 7], [515, 29, 472, 6], [515, 33, 472, 6, "_jsxDevRuntime"], [515, 47, 472, 6], [515, 48, 472, 6, "jsxDEV"], [515, 54, 472, 6], [515, 56, 472, 7, "_Modal"], [515, 62, 472, 7], [515, 63, 472, 7, "default"], [515, 70, 472, 12], [516, 8, 473, 8, "transparent"], [516, 19, 473, 19], [517, 8, 474, 8, "visible"], [517, 15, 474, 15], [517, 17, 474, 17, "isContextMenuVisible"], [517, 37, 474, 38], [518, 8, 475, 8, "onRequestClose"], [518, 22, 475, 22], [518, 24, 475, 24, "onRequestClose"], [518, 25, 475, 24], [518, 30, 475, 30, "setIsContextMenuVisible"], [518, 53, 475, 53], [518, 54, 475, 54], [518, 59, 475, 59], [518, 60, 475, 61], [519, 8, 475, 61, "children"], [519, 16, 475, 61], [519, 31, 477, 8], [519, 35, 477, 8, "_jsxDevRuntime"], [519, 49, 477, 8], [519, 50, 477, 8, "jsxDEV"], [519, 56, 477, 8], [519, 58, 477, 9, "_TouchableOpacity"], [519, 75, 477, 9], [519, 76, 477, 9, "default"], [519, 83, 477, 25], [520, 10, 478, 10, "style"], [520, 15, 478, 15], [520, 17, 478, 17], [521, 12, 478, 19, "flex"], [521, 16, 478, 23], [521, 18, 478, 25], [521, 19, 478, 26], [522, 12, 478, 28, "backgroundColor"], [522, 27, 478, 43], [522, 29, 478, 45], [522, 46, 478, 62], [523, 12, 478, 64, "justifyContent"], [523, 26, 478, 78], [523, 28, 478, 80], [523, 36, 478, 88], [524, 12, 478, 90, "alignItems"], [524, 22, 478, 100], [524, 24, 478, 102], [525, 10, 478, 111], [525, 11, 478, 113], [526, 10, 479, 10, "activeOpacity"], [526, 23, 479, 23], [526, 25, 479, 25], [526, 26, 479, 27], [527, 10, 480, 10, "onPressOut"], [527, 20, 480, 20], [527, 22, 480, 22, "onPressOut"], [527, 23, 480, 22], [527, 28, 480, 28, "setIsContextMenuVisible"], [527, 51, 480, 51], [527, 52, 480, 52], [527, 57, 480, 57], [527, 58, 480, 59], [528, 10, 480, 59, "children"], [528, 18, 480, 59], [528, 33, 482, 10], [528, 37, 482, 10, "_jsxDevRuntime"], [528, 51, 482, 10], [528, 52, 482, 10, "jsxDEV"], [528, 58, 482, 10], [528, 60, 482, 11, "_View"], [528, 65, 482, 11], [528, 66, 482, 11, "default"], [528, 73, 482, 15], [529, 12, 482, 16, "style"], [529, 17, 482, 21], [529, 19, 482, 23], [530, 14, 482, 25, "backgroundColor"], [530, 29, 482, 40], [530, 31, 482, 42, "colors"], [530, 37, 482, 48], [530, 38, 482, 49, "background"], [530, 48, 482, 59], [531, 14, 482, 61, "borderRadius"], [531, 26, 482, 73], [531, 28, 482, 75], [531, 30, 482, 77], [532, 14, 482, 79, "padding"], [532, 21, 482, 86], [532, 23, 482, 88], [532, 25, 482, 90], [533, 14, 482, 92, "width"], [533, 19, 482, 97], [533, 21, 482, 99], [534, 12, 482, 105], [534, 13, 482, 107], [535, 12, 482, 107, "children"], [535, 20, 482, 107], [535, 36, 483, 12], [535, 40, 483, 12, "_jsxDevRuntime"], [535, 54, 483, 12], [535, 55, 483, 12, "jsxDEV"], [535, 61, 483, 12], [535, 63, 483, 13, "_TouchableOpacity"], [535, 80, 483, 13], [535, 81, 483, 13, "default"], [535, 88, 483, 29], [536, 14, 483, 30, "onPress"], [536, 21, 483, 37], [536, 23, 483, 39, "handleCopyMessage"], [536, 40, 483, 57], [537, 14, 483, 58, "style"], [537, 19, 483, 63], [537, 21, 483, 65], [538, 16, 483, 67, "paddingVertical"], [538, 31, 483, 82], [538, 33, 483, 84], [539, 14, 483, 87], [539, 15, 483, 89], [540, 14, 483, 89, "children"], [540, 22, 483, 89], [540, 37, 484, 14], [540, 41, 484, 14, "_jsxDevRuntime"], [540, 55, 484, 14], [540, 56, 484, 14, "jsxDEV"], [540, 62, 484, 14], [540, 64, 484, 15, "_Text"], [540, 69, 484, 15], [540, 70, 484, 15, "default"], [540, 77, 484, 19], [541, 16, 484, 20, "style"], [541, 21, 484, 25], [541, 23, 484, 27], [542, 18, 484, 29, "fontSize"], [542, 26, 484, 37], [542, 28, 484, 39], [542, 30, 484, 41], [543, 18, 484, 43, "fontFamily"], [543, 28, 484, 53], [543, 30, 484, 55], [543, 49, 484, 74], [544, 18, 484, 76, "color"], [544, 23, 484, 81], [544, 25, 484, 83, "colors"], [544, 31, 484, 89], [544, 32, 484, 90, "text"], [545, 16, 484, 95], [545, 17, 484, 97], [546, 16, 484, 97, "children"], [546, 24, 484, 97], [546, 26, 484, 98], [547, 14, 484, 110], [548, 16, 484, 110, "fileName"], [548, 24, 484, 110], [548, 26, 484, 110, "_jsxFileName"], [548, 38, 484, 110], [549, 16, 484, 110, "lineNumber"], [549, 26, 484, 110], [550, 16, 484, 110, "columnNumber"], [550, 28, 484, 110], [551, 14, 484, 110], [551, 21, 484, 116], [552, 12, 484, 117], [553, 14, 484, 117, "fileName"], [553, 22, 484, 117], [553, 24, 484, 117, "_jsxFileName"], [553, 36, 484, 117], [554, 14, 484, 117, "lineNumber"], [554, 24, 484, 117], [555, 14, 484, 117, "columnNumber"], [555, 26, 484, 117], [556, 12, 484, 117], [556, 19, 485, 30], [556, 20, 485, 31], [556, 22, 486, 13, "longPressedMessage"], [556, 40, 486, 31], [556, 42, 486, 33, "role"], [556, 46, 486, 37], [556, 51, 486, 42], [556, 62, 486, 53], [556, 79, 487, 14], [556, 83, 487, 14, "_jsxDevRuntime"], [556, 97, 487, 14], [556, 98, 487, 14, "jsxDEV"], [556, 104, 487, 14], [556, 106, 487, 15, "_TouchableOpacity"], [556, 123, 487, 15], [556, 124, 487, 15, "default"], [556, 131, 487, 31], [557, 14, 487, 32, "onPress"], [557, 21, 487, 39], [557, 23, 487, 41, "handleListenToMessage"], [557, 44, 487, 63], [558, 14, 487, 64, "style"], [558, 19, 487, 69], [558, 21, 487, 71], [559, 16, 487, 73, "paddingVertical"], [559, 31, 487, 88], [559, 33, 487, 90], [560, 14, 487, 93], [560, 15, 487, 95], [561, 14, 487, 95, "children"], [561, 22, 487, 95], [561, 37, 488, 16], [561, 41, 488, 16, "_jsxDevRuntime"], [561, 55, 488, 16], [561, 56, 488, 16, "jsxDEV"], [561, 62, 488, 16], [561, 64, 488, 17, "_Text"], [561, 69, 488, 17], [561, 70, 488, 17, "default"], [561, 77, 488, 21], [562, 16, 488, 22, "style"], [562, 21, 488, 27], [562, 23, 488, 29], [563, 18, 488, 31, "fontSize"], [563, 26, 488, 39], [563, 28, 488, 41], [563, 30, 488, 43], [564, 18, 488, 45, "fontFamily"], [564, 28, 488, 55], [564, 30, 488, 57], [564, 49, 488, 76], [565, 18, 488, 78, "color"], [565, 23, 488, 83], [565, 25, 488, 85, "colors"], [565, 31, 488, 91], [565, 32, 488, 92, "text"], [566, 16, 488, 97], [566, 17, 488, 99], [567, 16, 488, 99, "children"], [567, 24, 488, 99], [567, 26, 488, 100], [568, 14, 488, 117], [569, 16, 488, 117, "fileName"], [569, 24, 488, 117], [569, 26, 488, 117, "_jsxFileName"], [569, 38, 488, 117], [570, 16, 488, 117, "lineNumber"], [570, 26, 488, 117], [571, 16, 488, 117, "columnNumber"], [571, 28, 488, 117], [572, 14, 488, 117], [572, 21, 488, 123], [573, 12, 488, 124], [574, 14, 488, 124, "fileName"], [574, 22, 488, 124], [574, 24, 488, 124, "_jsxFileName"], [574, 36, 488, 124], [575, 14, 488, 124, "lineNumber"], [575, 24, 488, 124], [576, 14, 488, 124, "columnNumber"], [576, 26, 488, 124], [577, 12, 488, 124], [577, 19, 489, 32], [577, 20, 490, 13], [578, 10, 490, 13], [579, 12, 490, 13, "fileName"], [579, 20, 490, 13], [579, 22, 490, 13, "_jsxFileName"], [579, 34, 490, 13], [580, 12, 490, 13, "lineNumber"], [580, 22, 490, 13], [581, 12, 490, 13, "columnNumber"], [581, 24, 490, 13], [582, 10, 490, 13], [582, 17, 491, 16], [583, 8, 491, 17], [584, 10, 491, 17, "fileName"], [584, 18, 491, 17], [584, 20, 491, 17, "_jsxFileName"], [584, 32, 491, 17], [585, 10, 491, 17, "lineNumber"], [585, 20, 491, 17], [586, 10, 491, 17, "columnNumber"], [586, 22, 491, 17], [587, 8, 491, 17], [587, 15, 492, 26], [588, 6, 492, 27], [589, 8, 492, 27, "fileName"], [589, 16, 492, 27], [589, 18, 492, 27, "_jsxFileName"], [589, 30, 492, 27], [590, 8, 492, 27, "lineNumber"], [590, 18, 492, 27], [591, 8, 492, 27, "columnNumber"], [591, 20, 492, 27], [592, 6, 492, 27], [592, 13, 493, 13], [592, 14, 493, 14], [593, 4, 493, 14], [594, 6, 493, 14, "fileName"], [594, 14, 493, 14], [594, 16, 493, 14, "_jsxFileName"], [594, 28, 493, 14], [595, 6, 493, 14, "lineNumber"], [595, 16, 493, 14], [596, 6, 493, 14, "columnNumber"], [596, 18, 493, 14], [597, 4, 493, 14], [597, 11, 494, 10], [597, 12, 494, 11], [598, 2, 496, 0], [599, 2, 496, 1, "_s"], [599, 4, 496, 1], [599, 5, 36, 24, "BrainstormScreen"], [599, 21, 36, 40], [600, 4, 36, 40], [600, 12, 37, 17, "useSafeAreaInsets"], [600, 57, 37, 34], [600, 59, 38, 17, "useColors"], [600, 79, 38, 26], [600, 81, 39, 17, "useRouter"], [600, 102, 39, 26], [600, 104, 40, 22, "useQueryClient"], [600, 130, 40, 36], [600, 132, 41, 24, "useFonts"], [600, 149, 41, 32], [600, 151, 47, 19, "useAudioRecorder"], [600, 178, 47, 35], [600, 180, 48, 24, "useAudioRecorderState"], [600, 212, 48, 45], [601, 2, 48, 45], [602, 2, 48, 45, "_c"], [602, 4, 48, 45], [602, 7, 36, 24, "BrainstormScreen"], [602, 23, 36, 40], [603, 2, 36, 40], [603, 6, 36, 40, "_c"], [603, 8, 36, 40], [604, 2, 36, 40, "$RefreshReg$"], [604, 14, 36, 40], [604, 15, 36, 40, "_c"], [604, 17, 36, 40], [605, 0, 36, 40], [605, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCmC;YCgC;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJK;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;YCI;GDI;gBiB0F,iCjB;0BkBuB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}