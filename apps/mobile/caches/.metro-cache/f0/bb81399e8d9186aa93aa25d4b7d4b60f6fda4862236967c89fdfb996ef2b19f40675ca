{"dependencies": [{"name": "./animation/Bounce.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "hBcNX6TBaqqQkobVlekcaQf7D48=", "exportNames": ["*"]}}, {"name": "./animation/Fade.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 109}, "end": {"line": 4, "column": 83, "index": 192}}], "key": "CecHPqfjUBJn3wpt16aDNBFog+E=", "exportNames": ["*"]}}, {"name": "./animation/Flip.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 193}, "end": {"line": 5, "column": 83, "index": 276}}], "key": "f+0qHX7JCu/LPV6vra41WSSxcbo=", "exportNames": ["*"]}}, {"name": "./animation/Lightspeed.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 277}, "end": {"line": 6, "column": 113, "index": 390}}], "key": "uocyVkovLUiHWIhkfQOzaW/tbWY=", "exportNames": ["*"]}}, {"name": "./animation/Pinwheel.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 391}, "end": {"line": 7, "column": 69, "index": 460}}], "key": "6M80boPYSO7yXCvKYFoyycYTc9k=", "exportNames": ["*"]}}, {"name": "./animation/Roll.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 461}, "end": {"line": 8, "column": 83, "index": 544}}], "key": "PI83E06Jnm0VWWSof2qLT+trU1A=", "exportNames": ["*"]}}, {"name": "./animation/Rotate.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 545}, "end": {"line": 9, "column": 93, "index": 638}}], "key": "pFuOtzrHotozIiC4lXWMkvnG8i8=", "exportNames": ["*"]}}, {"name": "./animation/Slide.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 639}, "end": {"line": 10, "column": 88, "index": 727}}], "key": "5lXZWfeN1hK5PbntT/Zw83bLPuE=", "exportNames": ["*"]}}, {"name": "./animation/Stretch.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 728}, "end": {"line": 11, "column": 98, "index": 826}}], "key": "7ggv0JAqMQlI/nU+qdBLSyq1J2w=", "exportNames": ["*"]}}, {"name": "./animation/Zoom.web.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 827}, "end": {"line": 12, "column": 83, "index": 910}}], "key": "6LZ5VWCTqTZB7a/CT+A8MlaIX0Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TransitionType = exports.AnimationsData = exports.Animations = void 0;\n  var _BounceWeb = require(_dependencyMap[0], \"./animation/Bounce.web.js\");\n  var _FadeWeb = require(_dependencyMap[1], \"./animation/Fade.web.js\");\n  var _FlipWeb = require(_dependencyMap[2], \"./animation/Flip.web.js\");\n  var _LightspeedWeb = require(_dependencyMap[3], \"./animation/Lightspeed.web.js\");\n  var _PinwheelWeb = require(_dependencyMap[4], \"./animation/Pinwheel.web.js\");\n  var _RollWeb = require(_dependencyMap[5], \"./animation/Roll.web.js\");\n  var _RotateWeb = require(_dependencyMap[6], \"./animation/Rotate.web.js\");\n  var _SlideWeb = require(_dependencyMap[7], \"./animation/Slide.web.js\");\n  var _StretchWeb = require(_dependencyMap[8], \"./animation/Stretch.web.js\");\n  var _ZoomWeb = require(_dependencyMap[9], \"./animation/Zoom.web.js\");\n  let TransitionType = exports.TransitionType = /*#__PURE__*/function (TransitionType) {\n    TransitionType[TransitionType[\"LINEAR\"] = 0] = \"LINEAR\";\n    TransitionType[TransitionType[\"SEQUENCED\"] = 1] = \"SEQUENCED\";\n    TransitionType[TransitionType[\"FADING\"] = 2] = \"FADING\";\n    TransitionType[TransitionType[\"JUMPING\"] = 3] = \"JUMPING\";\n    TransitionType[TransitionType[\"CURVED\"] = 4] = \"CURVED\";\n    TransitionType[TransitionType[\"ENTRY_EXIT\"] = 5] = \"ENTRY_EXIT\";\n    return TransitionType;\n  }({});\n  const AnimationsData = exports.AnimationsData = {\n    ..._FadeWeb.FadeInData,\n    ..._FadeWeb.FadeOutData,\n    ..._BounceWeb.BounceInData,\n    ..._BounceWeb.BounceOutData,\n    ..._FlipWeb.FlipInData,\n    ..._FlipWeb.FlipOutData,\n    ..._StretchWeb.StretchInData,\n    ..._StretchWeb.StretchOutData,\n    ..._ZoomWeb.ZoomInData,\n    ..._ZoomWeb.ZoomOutData,\n    ..._SlideWeb.SlideInData,\n    ..._SlideWeb.SlideOutData,\n    ..._LightspeedWeb.LightSpeedInData,\n    ..._LightspeedWeb.LightSpeedOutData,\n    ..._PinwheelWeb.PinwheelData,\n    ..._RotateWeb.RotateInData,\n    ..._RotateWeb.RotateOutData,\n    ..._RollWeb.RollInData,\n    ..._RollWeb.RollOutData\n  };\n  const Animations = exports.Animations = {\n    ..._FadeWeb.FadeIn,\n    ..._FadeWeb.FadeOut,\n    ..._BounceWeb.BounceIn,\n    ..._BounceWeb.BounceOut,\n    ..._FlipWeb.FlipIn,\n    ..._FlipWeb.FlipOut,\n    ..._StretchWeb.StretchIn,\n    ..._StretchWeb.StretchOut,\n    ..._ZoomWeb.ZoomIn,\n    ..._ZoomWeb.ZoomOut,\n    ..._SlideWeb.SlideIn,\n    ..._SlideWeb.SlideOut,\n    ..._LightspeedWeb.LightSpeedIn,\n    ..._LightspeedWeb.LightSpeedOut,\n    ..._PinwheelWeb.Pinwheel,\n    ..._RotateWeb.RotateIn,\n    ..._RotateWeb.RotateOut,\n    ..._RollWeb.RollIn,\n    ..._RollWeb.RollOut\n  };\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "TransitionType"], [7, 24, 1, 13], [7, 27, 1, 13, "exports"], [7, 34, 1, 13], [7, 35, 1, 13, "AnimationsData"], [7, 49, 1, 13], [7, 52, 1, 13, "exports"], [7, 59, 1, 13], [7, 60, 1, 13, "Animations"], [7, 70, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_FadeWeb"], [9, 14, 4, 0], [9, 17, 4, 0, "require"], [9, 24, 4, 0], [9, 25, 4, 0, "_dependencyMap"], [9, 39, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_FlipWeb"], [10, 14, 5, 0], [10, 17, 5, 0, "require"], [10, 24, 5, 0], [10, 25, 5, 0, "_dependencyMap"], [10, 39, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_LightspeedWeb"], [11, 20, 6, 0], [11, 23, 6, 0, "require"], [11, 30, 6, 0], [11, 31, 6, 0, "_dependencyMap"], [11, 45, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_PinwheelWeb"], [12, 18, 7, 0], [12, 21, 7, 0, "require"], [12, 28, 7, 0], [12, 29, 7, 0, "_dependencyMap"], [12, 43, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_RollWeb"], [13, 14, 8, 0], [13, 17, 8, 0, "require"], [13, 24, 8, 0], [13, 25, 8, 0, "_dependencyMap"], [13, 39, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_RotateWeb"], [14, 16, 9, 0], [14, 19, 9, 0, "require"], [14, 26, 9, 0], [14, 27, 9, 0, "_dependencyMap"], [14, 41, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_SlideWeb"], [15, 15, 10, 0], [15, 18, 10, 0, "require"], [15, 25, 10, 0], [15, 26, 10, 0, "_dependencyMap"], [15, 40, 10, 0], [16, 2, 11, 0], [16, 6, 11, 0, "_StretchWeb"], [16, 17, 11, 0], [16, 20, 11, 0, "require"], [16, 27, 11, 0], [16, 28, 11, 0, "_dependencyMap"], [16, 42, 11, 0], [17, 2, 12, 0], [17, 6, 12, 0, "_ZoomWeb"], [17, 14, 12, 0], [17, 17, 12, 0, "require"], [17, 24, 12, 0], [17, 25, 12, 0, "_dependencyMap"], [17, 39, 12, 0], [18, 2, 13, 7], [18, 6, 13, 11, "TransitionType"], [18, 20, 13, 25], [18, 23, 13, 25, "exports"], [18, 30, 13, 25], [18, 31, 13, 25, "TransitionType"], [18, 45, 13, 25], [18, 48, 13, 28], [18, 61, 13, 41], [18, 71, 13, 51, "TransitionType"], [18, 85, 13, 65], [18, 87, 13, 67], [19, 4, 14, 2, "TransitionType"], [19, 18, 14, 16], [19, 19, 14, 17, "TransitionType"], [19, 33, 14, 31], [19, 34, 14, 32], [19, 42, 14, 40], [19, 43, 14, 41], [19, 46, 14, 44], [19, 47, 14, 45], [19, 48, 14, 46], [19, 51, 14, 49], [19, 59, 14, 57], [20, 4, 15, 2, "TransitionType"], [20, 18, 15, 16], [20, 19, 15, 17, "TransitionType"], [20, 33, 15, 31], [20, 34, 15, 32], [20, 45, 15, 43], [20, 46, 15, 44], [20, 49, 15, 47], [20, 50, 15, 48], [20, 51, 15, 49], [20, 54, 15, 52], [20, 65, 15, 63], [21, 4, 16, 2, "TransitionType"], [21, 18, 16, 16], [21, 19, 16, 17, "TransitionType"], [21, 33, 16, 31], [21, 34, 16, 32], [21, 42, 16, 40], [21, 43, 16, 41], [21, 46, 16, 44], [21, 47, 16, 45], [21, 48, 16, 46], [21, 51, 16, 49], [21, 59, 16, 57], [22, 4, 17, 2, "TransitionType"], [22, 18, 17, 16], [22, 19, 17, 17, "TransitionType"], [22, 33, 17, 31], [22, 34, 17, 32], [22, 43, 17, 41], [22, 44, 17, 42], [22, 47, 17, 45], [22, 48, 17, 46], [22, 49, 17, 47], [22, 52, 17, 50], [22, 61, 17, 59], [23, 4, 18, 2, "TransitionType"], [23, 18, 18, 16], [23, 19, 18, 17, "TransitionType"], [23, 33, 18, 31], [23, 34, 18, 32], [23, 42, 18, 40], [23, 43, 18, 41], [23, 46, 18, 44], [23, 47, 18, 45], [23, 48, 18, 46], [23, 51, 18, 49], [23, 59, 18, 57], [24, 4, 19, 2, "TransitionType"], [24, 18, 19, 16], [24, 19, 19, 17, "TransitionType"], [24, 33, 19, 31], [24, 34, 19, 32], [24, 46, 19, 44], [24, 47, 19, 45], [24, 50, 19, 48], [24, 51, 19, 49], [24, 52, 19, 50], [24, 55, 19, 53], [24, 67, 19, 65], [25, 4, 20, 2], [25, 11, 20, 9, "TransitionType"], [25, 25, 20, 23], [26, 2, 21, 0], [26, 3, 21, 1], [26, 4, 21, 2], [26, 5, 21, 3], [26, 6, 21, 4], [26, 7, 21, 5], [27, 2, 22, 7], [27, 8, 22, 13, "AnimationsData"], [27, 22, 22, 27], [27, 25, 22, 27, "exports"], [27, 32, 22, 27], [27, 33, 22, 27, "AnimationsData"], [27, 47, 22, 27], [27, 50, 22, 30], [28, 4, 23, 2], [28, 7, 23, 5, "FadeInData"], [28, 26, 23, 15], [29, 4, 24, 2], [29, 7, 24, 5, "FadeOutData"], [29, 27, 24, 16], [30, 4, 25, 2], [30, 7, 25, 5, "BounceInData"], [30, 30, 25, 17], [31, 4, 26, 2], [31, 7, 26, 5, "BounceOutData"], [31, 31, 26, 18], [32, 4, 27, 2], [32, 7, 27, 5, "FlipInData"], [32, 26, 27, 15], [33, 4, 28, 2], [33, 7, 28, 5, "FlipOutData"], [33, 27, 28, 16], [34, 4, 29, 2], [34, 7, 29, 5, "StretchInData"], [34, 32, 29, 18], [35, 4, 30, 2], [35, 7, 30, 5, "StretchOutData"], [35, 33, 30, 19], [36, 4, 31, 2], [36, 7, 31, 5, "ZoomInData"], [36, 26, 31, 15], [37, 4, 32, 2], [37, 7, 32, 5, "ZoomOutData"], [37, 27, 32, 16], [38, 4, 33, 2], [38, 7, 33, 5, "SlideInData"], [38, 28, 33, 16], [39, 4, 34, 2], [39, 7, 34, 5, "SlideOutData"], [39, 29, 34, 17], [40, 4, 35, 2], [40, 7, 35, 5, "LightSpeedInData"], [40, 38, 35, 21], [41, 4, 36, 2], [41, 7, 36, 5, "LightSpeedOutData"], [41, 39, 36, 22], [42, 4, 37, 2], [42, 7, 37, 5, "PinwheelData"], [42, 32, 37, 17], [43, 4, 38, 2], [43, 7, 38, 5, "RotateInData"], [43, 30, 38, 17], [44, 4, 39, 2], [44, 7, 39, 5, "RotateOutData"], [44, 31, 39, 18], [45, 4, 40, 2], [45, 7, 40, 5, "RollInData"], [45, 26, 40, 15], [46, 4, 41, 2], [46, 7, 41, 5, "RollOutData"], [47, 2, 42, 0], [47, 3, 42, 1], [48, 2, 43, 7], [48, 8, 43, 13, "Animations"], [48, 18, 43, 23], [48, 21, 43, 23, "exports"], [48, 28, 43, 23], [48, 29, 43, 23, "Animations"], [48, 39, 43, 23], [48, 42, 43, 26], [49, 4, 44, 2], [49, 7, 44, 5, "FadeIn"], [49, 22, 44, 11], [50, 4, 45, 2], [50, 7, 45, 5, "FadeOut"], [50, 23, 45, 12], [51, 4, 46, 2], [51, 7, 46, 5, "BounceIn"], [51, 26, 46, 13], [52, 4, 47, 2], [52, 7, 47, 5, "BounceOut"], [52, 27, 47, 14], [53, 4, 48, 2], [53, 7, 48, 5, "FlipIn"], [53, 22, 48, 11], [54, 4, 49, 2], [54, 7, 49, 5, "FlipOut"], [54, 23, 49, 12], [55, 4, 50, 2], [55, 7, 50, 5, "StretchIn"], [55, 28, 50, 14], [56, 4, 51, 2], [56, 7, 51, 5, "StretchOut"], [56, 29, 51, 15], [57, 4, 52, 2], [57, 7, 52, 5, "ZoomIn"], [57, 22, 52, 11], [58, 4, 53, 2], [58, 7, 53, 5, "ZoomOut"], [58, 23, 53, 12], [59, 4, 54, 2], [59, 7, 54, 5, "SlideIn"], [59, 24, 54, 12], [60, 4, 55, 2], [60, 7, 55, 5, "SlideOut"], [60, 25, 55, 13], [61, 4, 56, 2], [61, 7, 56, 5, "LightSpeedIn"], [61, 34, 56, 17], [62, 4, 57, 2], [62, 7, 57, 5, "LightSpeedOut"], [62, 35, 57, 18], [63, 4, 58, 2], [63, 7, 58, 5, "Pinwheel"], [63, 28, 58, 13], [64, 4, 59, 2], [64, 7, 59, 5, "RotateIn"], [64, 26, 59, 13], [65, 4, 60, 2], [65, 7, 60, 5, "RotateOut"], [65, 27, 60, 14], [66, 4, 61, 2], [66, 7, 61, 5, "RollIn"], [66, 22, 61, 11], [67, 4, 62, 2], [67, 7, 62, 5, "RollOut"], [68, 2, 63, 0], [68, 3, 63, 1], [69, 0, 63, 2], [69, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;yCCY;CDQ"}}, "type": "js/module"}]}