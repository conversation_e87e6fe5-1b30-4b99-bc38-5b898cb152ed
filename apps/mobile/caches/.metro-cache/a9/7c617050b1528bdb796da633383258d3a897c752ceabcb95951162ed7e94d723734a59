{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 52}, "end": {"line": 2, "column": 60, "index": 112}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 113}, "end": {"line": 3, "column": 58, "index": 171}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "./useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 172}, "end": {"line": 4, "column": 40, "index": 212}}], "key": "qBoul5KQ1+OEu8G3Sr6Tlb7g7CM=", "exportNames": ["*"]}}, {"name": "./AudioVisualizer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 213}, "end": {"line": 5, "column": 48, "index": 261}}], "key": "PtmazYoURS57fsddHvpL2PH220Y=", "exportNames": ["*"]}}, {"name": "./RealtimeTranscript", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 54, "index": 316}}], "key": "r6gm1hrdQBRgTrcSPkygz/enaRs=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = VoiceMode;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _lucideReactNative = require(_dependencyMap[4], \"lucide-react-native\");\n  var _useColors = require(_dependencyMap[5], \"./useColors\");\n  var _AudioVisualizer = _interopRequireDefault(require(_dependencyMap[6], \"./AudioVisualizer\"));\n  var _RealtimeTranscript = _interopRequireDefault(require(_dependencyMap[7], \"./RealtimeTranscript\"));\n  var _jsxDevRuntime = require(_dependencyMap[8], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/components/VoiceMode.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function VoiceMode(_ref) {\n    _s();\n    var isRecording = _ref.isRecording,\n      onRecord = _ref.onRecord,\n      hasPermission = _ref.hasPermission,\n      isLoading = _ref.isLoading,\n      transcript = _ref.transcript,\n      isMuted = _ref.isMuted,\n      onMute = _ref.onMute;\n    var colors = (0, _useColors.useColors)();\n    var _useState = (0, _react.useState)(false),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      isSpeaking = _useState2[0],\n      setIsSpeaking = _useState2[1];\n    (0, _react.useEffect)(() => {\n      // Simulate speaking state for the visualizer\n      if (isRecording) {\n        setIsSpeaking(true);\n      } else {\n        setIsSpeaking(false);\n      }\n    }, [isRecording]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_AudioVisualizer.default, {\n        isSpeaking: isSpeaking\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_RealtimeTranscript.default, {\n        transcript: transcript\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          position: 'absolute',\n          bottom: 0,\n          left: 0,\n          right: 0,\n          alignItems: 'center',\n          gap: 12,\n          paddingHorizontal: 16,\n          paddingTop: 16,\n          paddingBottom: 16,\n          backgroundColor: 'transparent'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n          style: {\n            width: 72,\n            height: 72,\n            borderRadius: 36,\n            backgroundColor: isRecording ? '#FF3B30' : colors.primary,\n            alignItems: 'center',\n            justifyContent: 'center',\n            shadowColor: '#000',\n            shadowOffset: {\n              width: 0,\n              height: 2\n            },\n            shadowOpacity: 0.2,\n            shadowRadius: 4,\n            elevation: 4\n          },\n          onPress: onRecord,\n          disabled: !hasPermission || isLoading,\n          children: isRecording ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Square, {\n            size: 28,\n            color: colors.background,\n            fill: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n            size: 28,\n            color: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n          style: {\n            fontSize: 14,\n            fontFamily: 'Poppins_400Regular',\n            color: colors.textSecondary,\n            textAlign: 'center'\n          },\n          children: isRecording ? 'Always listening - tap to stop' : hasPermission ? 'Tap to start voice session' : 'Microphone permission needed'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n        style: {\n          position: 'absolute',\n          bottom: 32,\n          right: 24,\n          width: 56,\n          height: 56,\n          borderRadius: 28,\n          backgroundColor: isMuted ? '#FF3B30' : colors.primary,\n          alignItems: 'center',\n          justifyContent: 'center',\n          shadowColor: '#000',\n          shadowOffset: {\n            width: 0,\n            height: 2\n          },\n          shadowOpacity: 0.2,\n          shadowRadius: 4,\n          elevation: 4\n        },\n        onPress: onMute,\n        children: isMuted ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MicOff, {\n          size: 24,\n          color: colors.background\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n          size: 24,\n          color: colors.background\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 5\n    }, this);\n  }\n  _s(VoiceMode, \"je5mBwrzKqZY+g+64G/AzRwD0LM=\", false, function () {\n    return [_useColors.useColors];\n  });\n  _c = VoiceMode;\n  var _c;\n  $RefreshReg$(_c, \"VoiceMode\");\n});", "lineCount": 178, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_lucideReactNative"], [10, 24, 3, 0], [10, 27, 3, 0, "require"], [10, 34, 3, 0], [10, 35, 3, 0, "_dependencyMap"], [10, 49, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_useColors"], [11, 16, 4, 0], [11, 19, 4, 0, "require"], [11, 26, 4, 0], [11, 27, 4, 0, "_dependencyMap"], [11, 41, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_AudioVisualizer"], [12, 22, 5, 0], [12, 25, 5, 0, "_interopRequireDefault"], [12, 47, 5, 0], [12, 48, 5, 0, "require"], [12, 55, 5, 0], [12, 56, 5, 0, "_dependencyMap"], [12, 70, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_RealtimeTranscript"], [13, 25, 6, 0], [13, 28, 6, 0, "_interopRequireDefault"], [13, 50, 6, 0], [13, 51, 6, 0, "require"], [13, 58, 6, 0], [13, 59, 6, 0, "_dependencyMap"], [13, 73, 6, 0], [14, 2, 6, 54], [14, 6, 6, 54, "_jsxDevRuntime"], [14, 20, 6, 54], [14, 23, 6, 54, "require"], [14, 30, 6, 54], [14, 31, 6, 54, "_dependencyMap"], [14, 45, 6, 54], [15, 2, 6, 54], [15, 6, 6, 54, "_jsxFileName"], [15, 18, 6, 54], [16, 4, 6, 54, "_s"], [16, 6, 6, 54], [16, 9, 6, 54, "$RefreshSig$"], [16, 21, 6, 54], [17, 2, 6, 54], [17, 11, 6, 54, "_interopRequireWildcard"], [17, 35, 6, 54, "e"], [17, 36, 6, 54], [17, 38, 6, 54, "t"], [17, 39, 6, 54], [17, 68, 6, 54, "WeakMap"], [17, 75, 6, 54], [17, 81, 6, 54, "r"], [17, 82, 6, 54], [17, 89, 6, 54, "WeakMap"], [17, 96, 6, 54], [17, 100, 6, 54, "n"], [17, 101, 6, 54], [17, 108, 6, 54, "WeakMap"], [17, 115, 6, 54], [17, 127, 6, 54, "_interopRequireWildcard"], [17, 150, 6, 54], [17, 162, 6, 54, "_interopRequireWildcard"], [17, 163, 6, 54, "e"], [17, 164, 6, 54], [17, 166, 6, 54, "t"], [17, 167, 6, 54], [17, 176, 6, 54, "t"], [17, 177, 6, 54], [17, 181, 6, 54, "e"], [17, 182, 6, 54], [17, 186, 6, 54, "e"], [17, 187, 6, 54], [17, 188, 6, 54, "__esModule"], [17, 198, 6, 54], [17, 207, 6, 54, "e"], [17, 208, 6, 54], [17, 214, 6, 54, "o"], [17, 215, 6, 54], [17, 217, 6, 54, "i"], [17, 218, 6, 54], [17, 220, 6, 54, "f"], [17, 221, 6, 54], [17, 226, 6, 54, "__proto__"], [17, 235, 6, 54], [17, 243, 6, 54, "default"], [17, 250, 6, 54], [17, 252, 6, 54, "e"], [17, 253, 6, 54], [17, 270, 6, 54, "e"], [17, 271, 6, 54], [17, 294, 6, 54, "e"], [17, 295, 6, 54], [17, 320, 6, 54, "e"], [17, 321, 6, 54], [17, 330, 6, 54, "f"], [17, 331, 6, 54], [17, 337, 6, 54, "o"], [17, 338, 6, 54], [17, 341, 6, 54, "t"], [17, 342, 6, 54], [17, 345, 6, 54, "n"], [17, 346, 6, 54], [17, 349, 6, 54, "r"], [17, 350, 6, 54], [17, 358, 6, 54, "o"], [17, 359, 6, 54], [17, 360, 6, 54, "has"], [17, 363, 6, 54], [17, 364, 6, 54, "e"], [17, 365, 6, 54], [17, 375, 6, 54, "o"], [17, 376, 6, 54], [17, 377, 6, 54, "get"], [17, 380, 6, 54], [17, 381, 6, 54, "e"], [17, 382, 6, 54], [17, 385, 6, 54, "o"], [17, 386, 6, 54], [17, 387, 6, 54, "set"], [17, 390, 6, 54], [17, 391, 6, 54, "e"], [17, 392, 6, 54], [17, 394, 6, 54, "f"], [17, 395, 6, 54], [17, 409, 6, 54, "_t"], [17, 411, 6, 54], [17, 415, 6, 54, "e"], [17, 416, 6, 54], [17, 432, 6, 54, "_t"], [17, 434, 6, 54], [17, 441, 6, 54, "hasOwnProperty"], [17, 455, 6, 54], [17, 456, 6, 54, "call"], [17, 460, 6, 54], [17, 461, 6, 54, "e"], [17, 462, 6, 54], [17, 464, 6, 54, "_t"], [17, 466, 6, 54], [17, 473, 6, 54, "i"], [17, 474, 6, 54], [17, 478, 6, 54, "o"], [17, 479, 6, 54], [17, 482, 6, 54, "Object"], [17, 488, 6, 54], [17, 489, 6, 54, "defineProperty"], [17, 503, 6, 54], [17, 508, 6, 54, "Object"], [17, 514, 6, 54], [17, 515, 6, 54, "getOwnPropertyDescriptor"], [17, 539, 6, 54], [17, 540, 6, 54, "e"], [17, 541, 6, 54], [17, 543, 6, 54, "_t"], [17, 545, 6, 54], [17, 552, 6, 54, "i"], [17, 553, 6, 54], [17, 554, 6, 54, "get"], [17, 557, 6, 54], [17, 561, 6, 54, "i"], [17, 562, 6, 54], [17, 563, 6, 54, "set"], [17, 566, 6, 54], [17, 570, 6, 54, "o"], [17, 571, 6, 54], [17, 572, 6, 54, "f"], [17, 573, 6, 54], [17, 575, 6, 54, "_t"], [17, 577, 6, 54], [17, 579, 6, 54, "i"], [17, 580, 6, 54], [17, 584, 6, 54, "f"], [17, 585, 6, 54], [17, 586, 6, 54, "_t"], [17, 588, 6, 54], [17, 592, 6, 54, "e"], [17, 593, 6, 54], [17, 594, 6, 54, "_t"], [17, 596, 6, 54], [17, 607, 6, 54, "f"], [17, 608, 6, 54], [17, 613, 6, 54, "e"], [17, 614, 6, 54], [17, 616, 6, 54, "t"], [17, 617, 6, 54], [18, 2, 8, 15], [18, 11, 8, 24, "VoiceMode"], [18, 20, 8, 33, "VoiceMode"], [18, 21, 8, 33, "_ref"], [18, 25, 8, 33], [18, 27, 8, 116], [19, 4, 8, 116, "_s"], [19, 6, 8, 116], [20, 4, 8, 116], [20, 8, 8, 36, "isRecording"], [20, 19, 8, 47], [20, 22, 8, 47, "_ref"], [20, 26, 8, 47], [20, 27, 8, 36, "isRecording"], [20, 38, 8, 47], [21, 6, 8, 49, "onRecord"], [21, 14, 8, 57], [21, 17, 8, 57, "_ref"], [21, 21, 8, 57], [21, 22, 8, 49, "onRecord"], [21, 30, 8, 57], [22, 6, 8, 59, "hasPermission"], [22, 19, 8, 72], [22, 22, 8, 72, "_ref"], [22, 26, 8, 72], [22, 27, 8, 59, "hasPermission"], [22, 40, 8, 72], [23, 6, 8, 74, "isLoading"], [23, 15, 8, 83], [23, 18, 8, 83, "_ref"], [23, 22, 8, 83], [23, 23, 8, 74, "isLoading"], [23, 32, 8, 83], [24, 6, 8, 85, "transcript"], [24, 16, 8, 95], [24, 19, 8, 95, "_ref"], [24, 23, 8, 95], [24, 24, 8, 85, "transcript"], [24, 34, 8, 95], [25, 6, 8, 97, "isMuted"], [25, 13, 8, 104], [25, 16, 8, 104, "_ref"], [25, 20, 8, 104], [25, 21, 8, 97, "isMuted"], [25, 28, 8, 104], [26, 6, 8, 106, "onMute"], [26, 12, 8, 112], [26, 15, 8, 112, "_ref"], [26, 19, 8, 112], [26, 20, 8, 106, "onMute"], [26, 26, 8, 112], [27, 4, 9, 2], [27, 8, 9, 8, "colors"], [27, 14, 9, 14], [27, 17, 9, 17], [27, 21, 9, 17, "useColors"], [27, 41, 9, 26], [27, 43, 9, 27], [27, 44, 9, 28], [28, 4, 10, 2], [28, 8, 10, 2, "_useState"], [28, 17, 10, 2], [28, 20, 10, 38], [28, 24, 10, 38, "useState"], [28, 39, 10, 46], [28, 41, 10, 47], [28, 46, 10, 52], [28, 47, 10, 53], [29, 6, 10, 53, "_useState2"], [29, 16, 10, 53], [29, 23, 10, 53, "_slicedToArray2"], [29, 38, 10, 53], [29, 39, 10, 53, "default"], [29, 46, 10, 53], [29, 48, 10, 53, "_useState"], [29, 57, 10, 53], [30, 6, 10, 9, "isSpeaking"], [30, 16, 10, 19], [30, 19, 10, 19, "_useState2"], [30, 29, 10, 19], [31, 6, 10, 21, "setIsSpeaking"], [31, 19, 10, 34], [31, 22, 10, 34, "_useState2"], [31, 32, 10, 34], [32, 4, 12, 2], [32, 8, 12, 2, "useEffect"], [32, 24, 12, 11], [32, 26, 12, 12], [32, 32, 12, 18], [33, 6, 13, 4], [34, 6, 14, 4], [34, 10, 14, 8, "isRecording"], [34, 21, 14, 19], [34, 23, 14, 21], [35, 8, 15, 6, "setIsSpeaking"], [35, 21, 15, 19], [35, 22, 15, 20], [35, 26, 15, 24], [35, 27, 15, 25], [36, 6, 16, 4], [36, 7, 16, 5], [36, 13, 16, 11], [37, 8, 17, 6, "setIsSpeaking"], [37, 21, 17, 19], [37, 22, 17, 20], [37, 27, 17, 25], [37, 28, 17, 26], [38, 6, 18, 4], [39, 4, 19, 2], [39, 5, 19, 3], [39, 7, 19, 5], [39, 8, 19, 6, "isRecording"], [39, 19, 19, 17], [39, 20, 19, 18], [39, 21, 19, 19], [40, 4, 21, 2], [40, 24, 22, 4], [40, 28, 22, 4, "_jsxDevRuntime"], [40, 42, 22, 4], [40, 43, 22, 4, "jsxDEV"], [40, 49, 22, 4], [40, 51, 22, 5, "_reactNative"], [40, 63, 22, 5], [40, 64, 22, 5, "View"], [40, 68, 22, 9], [41, 6, 22, 10, "style"], [41, 11, 22, 15], [41, 13, 22, 17], [42, 8, 22, 19, "flex"], [42, 12, 22, 23], [42, 14, 22, 25], [43, 6, 22, 27], [43, 7, 22, 29], [44, 6, 22, 29, "children"], [44, 14, 22, 29], [44, 30, 23, 6], [44, 34, 23, 6, "_jsxDevRuntime"], [44, 48, 23, 6], [44, 49, 23, 6, "jsxDEV"], [44, 55, 23, 6], [44, 57, 23, 7, "_AudioVisualizer"], [44, 73, 23, 7], [44, 74, 23, 7, "default"], [44, 81, 23, 22], [45, 8, 23, 23, "isSpeaking"], [45, 18, 23, 33], [45, 20, 23, 35, "isSpeaking"], [46, 6, 23, 46], [47, 8, 23, 46, "fileName"], [47, 16, 23, 46], [47, 18, 23, 46, "_jsxFileName"], [47, 30, 23, 46], [48, 8, 23, 46, "lineNumber"], [48, 18, 23, 46], [49, 8, 23, 46, "columnNumber"], [49, 20, 23, 46], [50, 6, 23, 46], [50, 13, 23, 48], [50, 14, 23, 49], [50, 29, 24, 6], [50, 33, 24, 6, "_jsxDevRuntime"], [50, 47, 24, 6], [50, 48, 24, 6, "jsxDEV"], [50, 54, 24, 6], [50, 56, 24, 7, "_RealtimeTranscript"], [50, 75, 24, 7], [50, 76, 24, 7, "default"], [50, 83, 24, 25], [51, 8, 24, 26, "transcript"], [51, 18, 24, 36], [51, 20, 24, 38, "transcript"], [52, 6, 24, 49], [53, 8, 24, 49, "fileName"], [53, 16, 24, 49], [53, 18, 24, 49, "_jsxFileName"], [53, 30, 24, 49], [54, 8, 24, 49, "lineNumber"], [54, 18, 24, 49], [55, 8, 24, 49, "columnNumber"], [55, 20, 24, 49], [56, 6, 24, 49], [56, 13, 24, 51], [56, 14, 24, 52], [56, 29, 27, 6], [56, 33, 27, 6, "_jsxDevRuntime"], [56, 47, 27, 6], [56, 48, 27, 6, "jsxDEV"], [56, 54, 27, 6], [56, 56, 27, 7, "_reactNative"], [56, 68, 27, 7], [56, 69, 27, 7, "View"], [56, 73, 27, 11], [57, 8, 28, 8, "style"], [57, 13, 28, 13], [57, 15, 28, 15], [58, 10, 29, 10, "position"], [58, 18, 29, 18], [58, 20, 29, 20], [58, 30, 29, 30], [59, 10, 30, 10, "bottom"], [59, 16, 30, 16], [59, 18, 30, 18], [59, 19, 30, 19], [60, 10, 31, 10, "left"], [60, 14, 31, 14], [60, 16, 31, 16], [60, 17, 31, 17], [61, 10, 32, 10, "right"], [61, 15, 32, 15], [61, 17, 32, 17], [61, 18, 32, 18], [62, 10, 33, 10, "alignItems"], [62, 20, 33, 20], [62, 22, 33, 22], [62, 30, 33, 30], [63, 10, 34, 10, "gap"], [63, 13, 34, 13], [63, 15, 34, 15], [63, 17, 34, 17], [64, 10, 35, 10, "paddingHorizontal"], [64, 27, 35, 27], [64, 29, 35, 29], [64, 31, 35, 31], [65, 10, 36, 10, "paddingTop"], [65, 20, 36, 20], [65, 22, 36, 22], [65, 24, 36, 24], [66, 10, 37, 10, "paddingBottom"], [66, 23, 37, 23], [66, 25, 37, 25], [66, 27, 37, 27], [67, 10, 38, 10, "backgroundColor"], [67, 25, 38, 25], [67, 27, 38, 27], [68, 8, 39, 8], [68, 9, 39, 10], [69, 8, 39, 10, "children"], [69, 16, 39, 10], [69, 32, 41, 8], [69, 36, 41, 8, "_jsxDevRuntime"], [69, 50, 41, 8], [69, 51, 41, 8, "jsxDEV"], [69, 57, 41, 8], [69, 59, 41, 9, "_reactNative"], [69, 71, 41, 9], [69, 72, 41, 9, "TouchableOpacity"], [69, 88, 41, 25], [70, 10, 42, 10, "style"], [70, 15, 42, 15], [70, 17, 42, 17], [71, 12, 43, 12, "width"], [71, 17, 43, 17], [71, 19, 43, 19], [71, 21, 43, 21], [72, 12, 44, 12, "height"], [72, 18, 44, 18], [72, 20, 44, 20], [72, 22, 44, 22], [73, 12, 45, 12, "borderRadius"], [73, 24, 45, 24], [73, 26, 45, 26], [73, 28, 45, 28], [74, 12, 46, 12, "backgroundColor"], [74, 27, 46, 27], [74, 29, 46, 29, "isRecording"], [74, 40, 46, 40], [74, 43, 46, 43], [74, 52, 46, 52], [74, 55, 46, 55, "colors"], [74, 61, 46, 61], [74, 62, 46, 62, "primary"], [74, 69, 46, 69], [75, 12, 47, 12, "alignItems"], [75, 22, 47, 22], [75, 24, 47, 24], [75, 32, 47, 32], [76, 12, 48, 12, "justifyContent"], [76, 26, 48, 26], [76, 28, 48, 28], [76, 36, 48, 36], [77, 12, 49, 12, "shadowColor"], [77, 23, 49, 23], [77, 25, 49, 25], [77, 31, 49, 31], [78, 12, 50, 12, "shadowOffset"], [78, 24, 50, 24], [78, 26, 50, 26], [79, 14, 50, 28, "width"], [79, 19, 50, 33], [79, 21, 50, 35], [79, 22, 50, 36], [80, 14, 50, 38, "height"], [80, 20, 50, 44], [80, 22, 50, 46], [81, 12, 50, 48], [81, 13, 50, 49], [82, 12, 51, 12, "shadowOpacity"], [82, 25, 51, 25], [82, 27, 51, 27], [82, 30, 51, 30], [83, 12, 52, 12, "shadowRadius"], [83, 24, 52, 24], [83, 26, 52, 26], [83, 27, 52, 27], [84, 12, 53, 12, "elevation"], [84, 21, 53, 21], [84, 23, 53, 23], [85, 10, 54, 10], [85, 11, 54, 12], [86, 10, 55, 10, "onPress"], [86, 17, 55, 17], [86, 19, 55, 19, "onRecord"], [86, 27, 55, 28], [87, 10, 56, 10, "disabled"], [87, 18, 56, 18], [87, 20, 56, 20], [87, 21, 56, 21, "hasPermission"], [87, 34, 56, 34], [87, 38, 56, 38, "isLoading"], [87, 47, 56, 48], [88, 10, 56, 48, "children"], [88, 18, 56, 48], [88, 20, 58, 11, "isRecording"], [88, 31, 58, 22], [88, 47, 59, 12], [88, 51, 59, 12, "_jsxDevRuntime"], [88, 65, 59, 12], [88, 66, 59, 12, "jsxDEV"], [88, 72, 59, 12], [88, 74, 59, 13, "_lucideReactNative"], [88, 92, 59, 13], [88, 93, 59, 13, "Square"], [88, 99, 59, 19], [89, 12, 59, 20, "size"], [89, 16, 59, 24], [89, 18, 59, 26], [89, 20, 59, 29], [90, 12, 59, 30, "color"], [90, 17, 59, 35], [90, 19, 59, 37, "colors"], [90, 25, 59, 43], [90, 26, 59, 44, "background"], [90, 36, 59, 55], [91, 12, 59, 56, "fill"], [91, 16, 59, 60], [91, 18, 59, 62, "colors"], [91, 24, 59, 68], [91, 25, 59, 69, "background"], [92, 10, 59, 80], [93, 12, 59, 80, "fileName"], [93, 20, 59, 80], [93, 22, 59, 80, "_jsxFileName"], [93, 34, 59, 80], [94, 12, 59, 80, "lineNumber"], [94, 22, 59, 80], [95, 12, 59, 80, "columnNumber"], [95, 24, 59, 80], [96, 10, 59, 80], [96, 17, 59, 82], [96, 18, 59, 83], [96, 34, 61, 12], [96, 38, 61, 12, "_jsxDevRuntime"], [96, 52, 61, 12], [96, 53, 61, 12, "jsxDEV"], [96, 59, 61, 12], [96, 61, 61, 13, "_lucideReactNative"], [96, 79, 61, 13], [96, 80, 61, 13, "Mic"], [96, 83, 61, 16], [97, 12, 61, 17, "size"], [97, 16, 61, 21], [97, 18, 61, 23], [97, 20, 61, 26], [98, 12, 61, 27, "color"], [98, 17, 61, 32], [98, 19, 61, 34, "colors"], [98, 25, 61, 40], [98, 26, 61, 41, "background"], [99, 10, 61, 52], [100, 12, 61, 52, "fileName"], [100, 20, 61, 52], [100, 22, 61, 52, "_jsxFileName"], [100, 34, 61, 52], [101, 12, 61, 52, "lineNumber"], [101, 22, 61, 52], [102, 12, 61, 52, "columnNumber"], [102, 24, 61, 52], [103, 10, 61, 52], [103, 17, 61, 54], [104, 8, 62, 11], [105, 10, 62, 11, "fileName"], [105, 18, 62, 11], [105, 20, 62, 11, "_jsxFileName"], [105, 32, 62, 11], [106, 10, 62, 11, "lineNumber"], [106, 20, 62, 11], [107, 10, 62, 11, "columnNumber"], [107, 22, 62, 11], [108, 8, 62, 11], [108, 15, 63, 26], [108, 16, 63, 27], [108, 31, 64, 8], [108, 35, 64, 8, "_jsxDevRuntime"], [108, 49, 64, 8], [108, 50, 64, 8, "jsxDEV"], [108, 56, 64, 8], [108, 58, 64, 9, "_reactNative"], [108, 70, 64, 9], [108, 71, 64, 9, "Text"], [108, 75, 64, 13], [109, 10, 65, 10, "style"], [109, 15, 65, 15], [109, 17, 65, 17], [110, 12, 66, 12, "fontSize"], [110, 20, 66, 20], [110, 22, 66, 22], [110, 24, 66, 24], [111, 12, 67, 12, "fontFamily"], [111, 22, 67, 22], [111, 24, 67, 24], [111, 44, 67, 44], [112, 12, 68, 12, "color"], [112, 17, 68, 17], [112, 19, 68, 19, "colors"], [112, 25, 68, 25], [112, 26, 68, 26, "textSecondary"], [112, 39, 68, 39], [113, 12, 69, 12, "textAlign"], [113, 21, 69, 21], [113, 23, 69, 23], [114, 10, 70, 10], [114, 11, 70, 12], [115, 10, 70, 12, "children"], [115, 18, 70, 12], [115, 20, 72, 11, "isRecording"], [115, 31, 72, 22], [115, 34, 73, 14], [115, 66, 73, 46], [115, 69, 74, 14, "hasPermission"], [115, 82, 74, 27], [115, 85, 75, 16], [115, 113, 75, 44], [115, 116, 76, 16], [116, 8, 76, 46], [117, 10, 76, 46, "fileName"], [117, 18, 76, 46], [117, 20, 76, 46, "_jsxFileName"], [117, 32, 76, 46], [118, 10, 76, 46, "lineNumber"], [118, 20, 76, 46], [119, 10, 76, 46, "columnNumber"], [119, 22, 76, 46], [120, 8, 76, 46], [120, 15, 77, 14], [120, 16, 77, 15], [121, 6, 77, 15], [122, 8, 77, 15, "fileName"], [122, 16, 77, 15], [122, 18, 77, 15, "_jsxFileName"], [122, 30, 77, 15], [123, 8, 77, 15, "lineNumber"], [123, 18, 77, 15], [124, 8, 77, 15, "columnNumber"], [124, 20, 77, 15], [125, 6, 77, 15], [125, 13, 78, 12], [125, 14, 78, 13], [125, 29, 81, 6], [125, 33, 81, 6, "_jsxDevRuntime"], [125, 47, 81, 6], [125, 48, 81, 6, "jsxDEV"], [125, 54, 81, 6], [125, 56, 81, 7, "_reactNative"], [125, 68, 81, 7], [125, 69, 81, 7, "TouchableOpacity"], [125, 85, 81, 23], [126, 8, 82, 8, "style"], [126, 13, 82, 13], [126, 15, 82, 15], [127, 10, 83, 10, "position"], [127, 18, 83, 18], [127, 20, 83, 20], [127, 30, 83, 30], [128, 10, 84, 10, "bottom"], [128, 16, 84, 16], [128, 18, 84, 18], [128, 20, 84, 20], [129, 10, 85, 10, "right"], [129, 15, 85, 15], [129, 17, 85, 17], [129, 19, 85, 19], [130, 10, 86, 10, "width"], [130, 15, 86, 15], [130, 17, 86, 17], [130, 19, 86, 19], [131, 10, 87, 10, "height"], [131, 16, 87, 16], [131, 18, 87, 18], [131, 20, 87, 20], [132, 10, 88, 10, "borderRadius"], [132, 22, 88, 22], [132, 24, 88, 24], [132, 26, 88, 26], [133, 10, 89, 10, "backgroundColor"], [133, 25, 89, 25], [133, 27, 89, 27, "isMuted"], [133, 34, 89, 34], [133, 37, 89, 37], [133, 46, 89, 46], [133, 49, 89, 49, "colors"], [133, 55, 89, 55], [133, 56, 89, 56, "primary"], [133, 63, 89, 63], [134, 10, 90, 10, "alignItems"], [134, 20, 90, 20], [134, 22, 90, 22], [134, 30, 90, 30], [135, 10, 91, 10, "justifyContent"], [135, 24, 91, 24], [135, 26, 91, 26], [135, 34, 91, 34], [136, 10, 92, 10, "shadowColor"], [136, 21, 92, 21], [136, 23, 92, 23], [136, 29, 92, 29], [137, 10, 93, 10, "shadowOffset"], [137, 22, 93, 22], [137, 24, 93, 24], [138, 12, 93, 26, "width"], [138, 17, 93, 31], [138, 19, 93, 33], [138, 20, 93, 34], [139, 12, 93, 36, "height"], [139, 18, 93, 42], [139, 20, 93, 44], [140, 10, 93, 46], [140, 11, 93, 47], [141, 10, 94, 10, "shadowOpacity"], [141, 23, 94, 23], [141, 25, 94, 25], [141, 28, 94, 28], [142, 10, 95, 10, "shadowRadius"], [142, 22, 95, 22], [142, 24, 95, 24], [142, 25, 95, 25], [143, 10, 96, 10, "elevation"], [143, 19, 96, 19], [143, 21, 96, 21], [144, 8, 97, 8], [144, 9, 97, 10], [145, 8, 98, 8, "onPress"], [145, 15, 98, 15], [145, 17, 98, 17, "onMute"], [145, 23, 98, 24], [146, 8, 98, 24, "children"], [146, 16, 98, 24], [146, 18, 100, 9, "isMuted"], [146, 25, 100, 16], [146, 41, 101, 10], [146, 45, 101, 10, "_jsxDevRuntime"], [146, 59, 101, 10], [146, 60, 101, 10, "jsxDEV"], [146, 66, 101, 10], [146, 68, 101, 11, "_lucideReactNative"], [146, 86, 101, 11], [146, 87, 101, 11, "<PERSON><PERSON><PERSON><PERSON>"], [146, 93, 101, 17], [147, 10, 101, 18, "size"], [147, 14, 101, 22], [147, 16, 101, 24], [147, 18, 101, 27], [148, 10, 101, 28, "color"], [148, 15, 101, 33], [148, 17, 101, 35, "colors"], [148, 23, 101, 41], [148, 24, 101, 42, "background"], [149, 8, 101, 53], [150, 10, 101, 53, "fileName"], [150, 18, 101, 53], [150, 20, 101, 53, "_jsxFileName"], [150, 32, 101, 53], [151, 10, 101, 53, "lineNumber"], [151, 20, 101, 53], [152, 10, 101, 53, "columnNumber"], [152, 22, 101, 53], [153, 8, 101, 53], [153, 15, 101, 55], [153, 16, 101, 56], [153, 32, 103, 10], [153, 36, 103, 10, "_jsxDevRuntime"], [153, 50, 103, 10], [153, 51, 103, 10, "jsxDEV"], [153, 57, 103, 10], [153, 59, 103, 11, "_lucideReactNative"], [153, 77, 103, 11], [153, 78, 103, 11, "Mic"], [153, 81, 103, 14], [154, 10, 103, 15, "size"], [154, 14, 103, 19], [154, 16, 103, 21], [154, 18, 103, 24], [155, 10, 103, 25, "color"], [155, 15, 103, 30], [155, 17, 103, 32, "colors"], [155, 23, 103, 38], [155, 24, 103, 39, "background"], [156, 8, 103, 50], [157, 10, 103, 50, "fileName"], [157, 18, 103, 50], [157, 20, 103, 50, "_jsxFileName"], [157, 32, 103, 50], [158, 10, 103, 50, "lineNumber"], [158, 20, 103, 50], [159, 10, 103, 50, "columnNumber"], [159, 22, 103, 50], [160, 8, 103, 50], [160, 15, 103, 52], [161, 6, 104, 9], [162, 8, 104, 9, "fileName"], [162, 16, 104, 9], [162, 18, 104, 9, "_jsxFileName"], [162, 30, 104, 9], [163, 8, 104, 9, "lineNumber"], [163, 18, 104, 9], [164, 8, 104, 9, "columnNumber"], [164, 20, 104, 9], [165, 6, 104, 9], [165, 13, 105, 24], [165, 14, 105, 25], [166, 4, 105, 25], [167, 6, 105, 25, "fileName"], [167, 14, 105, 25], [167, 16, 105, 25, "_jsxFileName"], [167, 28, 105, 25], [168, 6, 105, 25, "lineNumber"], [168, 16, 105, 25], [169, 6, 105, 25, "columnNumber"], [169, 18, 105, 25], [170, 4, 105, 25], [170, 11, 106, 10], [170, 12, 106, 11], [171, 2, 108, 0], [172, 2, 108, 1, "_s"], [172, 4, 108, 1], [172, 5, 8, 24, "VoiceMode"], [172, 14, 8, 33], [173, 4, 8, 33], [173, 12, 9, 17, "useColors"], [173, 32, 9, 26], [174, 2, 9, 26], [175, 2, 9, 26, "_c"], [175, 4, 9, 26], [175, 7, 8, 24, "VoiceMode"], [175, 16, 8, 33], [176, 2, 8, 33], [176, 6, 8, 33, "_c"], [176, 8, 8, 33], [177, 2, 8, 33, "$RefreshReg$"], [177, 14, 8, 33], [177, 15, 8, 33, "_c"], [177, 17, 8, 33], [178, 0, 8, 33], [178, 3]], "functionMap": {"names": ["<global>", "VoiceMode", "useEffect$argument_0"], "mappings": "AAA;eCO;YCI;GDO;CDyF"}}, "type": "js/module"}]}