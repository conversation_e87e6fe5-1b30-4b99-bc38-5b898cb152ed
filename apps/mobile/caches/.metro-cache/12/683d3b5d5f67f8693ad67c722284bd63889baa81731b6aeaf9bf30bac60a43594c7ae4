{"dependencies": [{"name": "../../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 96}}], "key": "2arAsAkFZXMuZ9gEet1IAz5yEiQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[0], \"../../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = TurboModuleRegistry.getEnforcing('NativeMicrotasksCxx');\n});", "lineCount": 9, "map": [[6, 2, 13, 0], [6, 6, 13, 0, "TurboModuleRegistry"], [6, 25, 13, 0], [6, 28, 13, 0, "_interopRequireWildcard"], [6, 51, 13, 0], [6, 52, 13, 0, "require"], [6, 59, 13, 0], [6, 60, 13, 0, "_dependencyMap"], [6, 74, 13, 0], [7, 2, 13, 96], [7, 11, 13, 96, "_interopRequireWildcard"], [7, 35, 13, 96, "e"], [7, 36, 13, 96], [7, 38, 13, 96, "t"], [7, 39, 13, 96], [7, 68, 13, 96, "WeakMap"], [7, 75, 13, 96], [7, 81, 13, 96, "r"], [7, 82, 13, 96], [7, 89, 13, 96, "WeakMap"], [7, 96, 13, 96], [7, 100, 13, 96, "n"], [7, 101, 13, 96], [7, 108, 13, 96, "WeakMap"], [7, 115, 13, 96], [7, 127, 13, 96, "_interopRequireWildcard"], [7, 150, 13, 96], [7, 162, 13, 96, "_interopRequireWildcard"], [7, 163, 13, 96, "e"], [7, 164, 13, 96], [7, 166, 13, 96, "t"], [7, 167, 13, 96], [7, 176, 13, 96, "t"], [7, 177, 13, 96], [7, 181, 13, 96, "e"], [7, 182, 13, 96], [7, 186, 13, 96, "e"], [7, 187, 13, 96], [7, 188, 13, 96, "__esModule"], [7, 198, 13, 96], [7, 207, 13, 96, "e"], [7, 208, 13, 96], [7, 214, 13, 96, "o"], [7, 215, 13, 96], [7, 217, 13, 96, "i"], [7, 218, 13, 96], [7, 220, 13, 96, "f"], [7, 221, 13, 96], [7, 226, 13, 96, "__proto__"], [7, 235, 13, 96], [7, 243, 13, 96, "default"], [7, 250, 13, 96], [7, 252, 13, 96, "e"], [7, 253, 13, 96], [7, 270, 13, 96, "e"], [7, 271, 13, 96], [7, 294, 13, 96, "e"], [7, 295, 13, 96], [7, 320, 13, 96, "e"], [7, 321, 13, 96], [7, 330, 13, 96, "f"], [7, 331, 13, 96], [7, 337, 13, 96, "o"], [7, 338, 13, 96], [7, 341, 13, 96, "t"], [7, 342, 13, 96], [7, 345, 13, 96, "n"], [7, 346, 13, 96], [7, 349, 13, 96, "r"], [7, 350, 13, 96], [7, 358, 13, 96, "o"], [7, 359, 13, 96], [7, 360, 13, 96, "has"], [7, 363, 13, 96], [7, 364, 13, 96, "e"], [7, 365, 13, 96], [7, 375, 13, 96, "o"], [7, 376, 13, 96], [7, 377, 13, 96, "get"], [7, 380, 13, 96], [7, 381, 13, 96, "e"], [7, 382, 13, 96], [7, 385, 13, 96, "o"], [7, 386, 13, 96], [7, 387, 13, 96, "set"], [7, 390, 13, 96], [7, 391, 13, 96, "e"], [7, 392, 13, 96], [7, 394, 13, 96, "f"], [7, 395, 13, 96], [7, 409, 13, 96, "_t"], [7, 411, 13, 96], [7, 415, 13, 96, "e"], [7, 416, 13, 96], [7, 432, 13, 96, "_t"], [7, 434, 13, 96], [7, 441, 13, 96, "hasOwnProperty"], [7, 455, 13, 96], [7, 456, 13, 96, "call"], [7, 460, 13, 96], [7, 461, 13, 96, "e"], [7, 462, 13, 96], [7, 464, 13, 96, "_t"], [7, 466, 13, 96], [7, 473, 13, 96, "i"], [7, 474, 13, 96], [7, 478, 13, 96, "o"], [7, 479, 13, 96], [7, 482, 13, 96, "Object"], [7, 488, 13, 96], [7, 489, 13, 96, "defineProperty"], [7, 503, 13, 96], [7, 508, 13, 96, "Object"], [7, 514, 13, 96], [7, 515, 13, 96, "getOwnPropertyDescriptor"], [7, 539, 13, 96], [7, 540, 13, 96, "e"], [7, 541, 13, 96], [7, 543, 13, 96, "_t"], [7, 545, 13, 96], [7, 552, 13, 96, "i"], [7, 553, 13, 96], [7, 554, 13, 96, "get"], [7, 557, 13, 96], [7, 561, 13, 96, "i"], [7, 562, 13, 96], [7, 563, 13, 96, "set"], [7, 566, 13, 96], [7, 570, 13, 96, "o"], [7, 571, 13, 96], [7, 572, 13, 96, "f"], [7, 573, 13, 96], [7, 575, 13, 96, "_t"], [7, 577, 13, 96], [7, 579, 13, 96, "i"], [7, 580, 13, 96], [7, 584, 13, 96, "f"], [7, 585, 13, 96], [7, 586, 13, 96, "_t"], [7, 588, 13, 96], [7, 592, 13, 96, "e"], [7, 593, 13, 96], [7, 594, 13, 96, "_t"], [7, 596, 13, 96], [7, 607, 13, 96, "f"], [7, 608, 13, 96], [7, 613, 13, 96, "e"], [7, 614, 13, 96], [7, 616, 13, 96, "t"], [7, 617, 13, 96], [8, 2, 13, 96], [8, 6, 13, 96, "_default"], [8, 14, 13, 96], [8, 17, 13, 96, "exports"], [8, 24, 13, 96], [8, 25, 13, 96, "default"], [8, 32, 13, 96], [8, 35, 19, 16, "TurboModuleRegistry"], [8, 54, 19, 35], [8, 55, 19, 36, "getEnforcing"], [8, 67, 19, 48], [8, 68, 20, 2], [8, 89, 21, 0], [8, 90, 21, 1], [9, 0, 21, 1], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}