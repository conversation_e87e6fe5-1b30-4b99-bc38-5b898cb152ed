{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/PixelRatio", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5rdRioKC4qvLVlVTyxLOiQm3IeU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getDefaultHeaderHeight = getDefaultHeaderHeight;\n  var _PixelRatio = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/PixelRatio\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  function getDefaultHeaderHeight(layout, modalPresentation, topInset) {\n    let headerHeight;\n\n    // On models with Dynamic Island the status bar height is smaller than the safe area top inset.\n    const hasDynamicIsland = _Platform.default.OS === 'ios' && topInset > 50;\n    const statusBarHeight = hasDynamicIsland ? topInset - (5 + 1 / _PixelRatio.default.get()) : topInset;\n    const isLandscape = layout.width > layout.height;\n    if (_Platform.default.OS === 'ios') {\n      if (_Platform.default.isPad || _Platform.default.isTV) {\n        if (modalPresentation) {\n          headerHeight = 56;\n        } else {\n          headerHeight = 50;\n        }\n      } else {\n        if (isLandscape) {\n          headerHeight = 32;\n        } else {\n          if (modalPresentation) {\n            headerHeight = 56;\n          } else {\n            headerHeight = 44;\n          }\n        }\n      }\n    } else {\n      headerHeight = 64;\n    }\n    return headerHeight + statusBarHeight;\n  }\n});", "lineCount": 41, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "getDefaultHeaderHeight"], [8, 32, 1, 13], [8, 35, 1, 13, "getDefaultHeaderHeight"], [8, 57, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_PixelRatio"], [9, 17, 1, 13], [9, 20, 1, 13, "_interopRequireDefault"], [9, 42, 1, 13], [9, 43, 1, 13, "require"], [9, 50, 1, 13], [9, 51, 1, 13, "_dependencyMap"], [9, 65, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_Platform"], [10, 15, 1, 13], [10, 18, 1, 13, "_interopRequireDefault"], [10, 40, 1, 13], [10, 41, 1, 13, "require"], [10, 48, 1, 13], [10, 49, 1, 13, "_dependencyMap"], [10, 63, 1, 13], [11, 2, 4, 7], [11, 11, 4, 16, "getDefaultHeaderHeight"], [11, 33, 4, 38, "getDefaultHeaderHeight"], [11, 34, 4, 39, "layout"], [11, 40, 4, 45], [11, 42, 4, 47, "modalPresentation"], [11, 59, 4, 64], [11, 61, 4, 66, "topInset"], [11, 69, 4, 74], [11, 71, 4, 76], [12, 4, 5, 2], [12, 8, 5, 6, "headerHeight"], [12, 20, 5, 18], [14, 4, 7, 2], [15, 4, 8, 2], [15, 10, 8, 8, "hasDynamicIsland"], [15, 26, 8, 24], [15, 29, 8, 27, "Platform"], [15, 46, 8, 35], [15, 47, 8, 36, "OS"], [15, 49, 8, 38], [15, 54, 8, 43], [15, 59, 8, 48], [15, 63, 8, 52, "topInset"], [15, 71, 8, 60], [15, 74, 8, 63], [15, 76, 8, 65], [16, 4, 9, 2], [16, 10, 9, 8, "statusBarHeight"], [16, 25, 9, 23], [16, 28, 9, 26, "hasDynamicIsland"], [16, 44, 9, 42], [16, 47, 9, 45, "topInset"], [16, 55, 9, 53], [16, 59, 9, 57], [16, 60, 9, 58], [16, 63, 9, 61], [16, 64, 9, 62], [16, 67, 9, 65, "PixelRatio"], [16, 86, 9, 75], [16, 87, 9, 76, "get"], [16, 90, 9, 79], [16, 91, 9, 80], [16, 92, 9, 81], [16, 93, 9, 82], [16, 96, 9, 85, "topInset"], [16, 104, 9, 93], [17, 4, 10, 2], [17, 10, 10, 8, "isLandscape"], [17, 21, 10, 19], [17, 24, 10, 22, "layout"], [17, 30, 10, 28], [17, 31, 10, 29, "width"], [17, 36, 10, 34], [17, 39, 10, 37, "layout"], [17, 45, 10, 43], [17, 46, 10, 44, "height"], [17, 52, 10, 50], [18, 4, 11, 2], [18, 8, 11, 6, "Platform"], [18, 25, 11, 14], [18, 26, 11, 15, "OS"], [18, 28, 11, 17], [18, 33, 11, 22], [18, 38, 11, 27], [18, 40, 11, 29], [19, 6, 12, 4], [19, 10, 12, 8, "Platform"], [19, 27, 12, 16], [19, 28, 12, 17, "isPad"], [19, 33, 12, 22], [19, 37, 12, 26, "Platform"], [19, 54, 12, 34], [19, 55, 12, 35, "isTV"], [19, 59, 12, 39], [19, 61, 12, 41], [20, 8, 13, 6], [20, 12, 13, 10, "modalPresentation"], [20, 29, 13, 27], [20, 31, 13, 29], [21, 10, 14, 8, "headerHeight"], [21, 22, 14, 20], [21, 25, 14, 23], [21, 27, 14, 25], [22, 8, 15, 6], [22, 9, 15, 7], [22, 15, 15, 13], [23, 10, 16, 8, "headerHeight"], [23, 22, 16, 20], [23, 25, 16, 23], [23, 27, 16, 25], [24, 8, 17, 6], [25, 6, 18, 4], [25, 7, 18, 5], [25, 13, 18, 11], [26, 8, 19, 6], [26, 12, 19, 10, "isLandscape"], [26, 23, 19, 21], [26, 25, 19, 23], [27, 10, 20, 8, "headerHeight"], [27, 22, 20, 20], [27, 25, 20, 23], [27, 27, 20, 25], [28, 8, 21, 6], [28, 9, 21, 7], [28, 15, 21, 13], [29, 10, 22, 8], [29, 14, 22, 12, "modalPresentation"], [29, 31, 22, 29], [29, 33, 22, 31], [30, 12, 23, 10, "headerHeight"], [30, 24, 23, 22], [30, 27, 23, 25], [30, 29, 23, 27], [31, 10, 24, 8], [31, 11, 24, 9], [31, 17, 24, 15], [32, 12, 25, 10, "headerHeight"], [32, 24, 25, 22], [32, 27, 25, 25], [32, 29, 25, 27], [33, 10, 26, 8], [34, 8, 27, 6], [35, 6, 28, 4], [36, 4, 29, 2], [36, 5, 29, 3], [36, 11, 29, 9], [37, 6, 30, 4, "headerHeight"], [37, 18, 30, 16], [37, 21, 30, 19], [37, 23, 30, 21], [38, 4, 31, 2], [39, 4, 32, 2], [39, 11, 32, 9, "headerHeight"], [39, 23, 32, 21], [39, 26, 32, 24, "statusBarHeight"], [39, 41, 32, 39], [40, 2, 33, 0], [41, 0, 33, 1], [41, 3]], "functionMap": {"names": ["<global>", "getDefaultHeaderHeight"], "mappings": "AAA;OCG;CD6B"}}, "type": "js/module"}]}