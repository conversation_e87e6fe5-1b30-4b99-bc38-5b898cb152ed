{"dependencies": [{"name": "./lib/ReactPropTypesSecret", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 29, "index": 308}, "end": {"line": 13, "column": 66, "index": 345}}], "key": "nI7mz6E9mIX/HhF+XrBCbg3+oVQ=", "exportNames": ["*"]}}, {"name": "./lib/has", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 12, "index": 390}, "end": {"line": 15, "column": 32, "index": 410}}], "key": "B5BoKwe0SDQ1uv1Tb15O/EuAio8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  'use strict';\n\n  var printWarning = function () {};\n  if (process.env.NODE_ENV !== 'production') {\n    var ReactPropTypesSecret = require(_dependencyMap[0], \"./lib/ReactPropTypesSecret\");\n    var loggedTypeFailures = {};\n    var has = require(_dependencyMap[1], \"./lib/has\");\n    printWarning = function (text) {\n      var message = 'Warning: ' + text;\n      if (typeof console !== 'undefined') {\n        console.error(message);\n      }\n      try {\n        // --- Welcome to debugging React ---\n        // This error was thrown as a convenience so that you can use this stack\n        // to find the callsite that caused this warning to fire.\n        throw new Error(message);\n      } catch (x) {/**/}\n    };\n  }\n\n  /**\n   * Assert that the values match with the type specs.\n   * Error messages are memorized and will only be shown once.\n   *\n   * @param {object} typeSpecs Map of name to a ReactPropType\n   * @param {object} values Runtime values that need to be type-checked\n   * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n   * @param {string} componentName Name of the component for error messages.\n   * @param {?Function} getStack Returns the component stack.\n   * @private\n   */\n  function checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n    if (process.env.NODE_ENV !== 'production') {\n      for (var typeSpecName in typeSpecs) {\n        if (has(typeSpecs, typeSpecName)) {\n          var error;\n          // Prop type validation may throw. In case they do, we don't want to\n          // fail the render phase where it didn't fail before. So we log it.\n          // After these have been cleaned up, we'll let them throw.\n          try {\n            // This is intentionally an invariant that gets caught. It's the same\n            // behavior as without this statement except with a better message.\n            if (typeof typeSpecs[typeSpecName] !== 'function') {\n              var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n              err.name = 'Invariant Violation';\n              throw err;\n            }\n            error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n          } catch (ex) {\n            error = ex;\n          }\n          if (error && !(error instanceof Error)) {\n            printWarning((componentName || 'React class') + ': type specification of ' + location + ' `' + typeSpecName + '` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a ' + typeof error + '. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).');\n          }\n          if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n            // Only monitor this failure once because there tends to be a lot of the\n            // same error.\n            loggedTypeFailures[error.message] = true;\n            var stack = getStack ? getStack() : '';\n            printWarning('Failed ' + location + ' type: ' + error.message + (stack != null ? stack : ''));\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Resets warning cache when testing.\n   *\n   * @private\n   */\n  checkPropTypes.resetWarningCache = function () {\n    if (process.env.NODE_ENV !== 'production') {\n      loggedTypeFailures = {};\n    }\n  };\n  module.exports = checkPropTypes;\n});", "lineCount": 87, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [9, 2, 8, 0], [9, 14, 8, 12], [11, 2, 10, 0], [11, 6, 10, 4, "printWarning"], [11, 18, 10, 16], [11, 21, 10, 19], [11, 30, 10, 19, "printWarning"], [11, 31, 10, 19], [11, 33, 10, 30], [11, 34, 10, 31], [11, 35, 10, 32], [12, 2, 12, 0], [12, 6, 12, 4, "process"], [12, 13, 12, 11], [12, 14, 12, 12, "env"], [12, 17, 12, 15], [12, 18, 12, 16, "NODE_ENV"], [12, 26, 12, 24], [12, 31, 12, 29], [12, 43, 12, 41], [12, 45, 12, 43], [13, 4, 13, 2], [13, 8, 13, 6, "ReactPropTypesSecret"], [13, 28, 13, 26], [13, 31, 13, 29, "require"], [13, 38, 13, 36], [13, 39, 13, 36, "_dependencyMap"], [13, 53, 13, 36], [13, 86, 13, 65], [13, 87, 13, 66], [14, 4, 14, 2], [14, 8, 14, 6, "loggedTypeFailures"], [14, 26, 14, 24], [14, 29, 14, 27], [14, 30, 14, 28], [14, 31, 14, 29], [15, 4, 15, 2], [15, 8, 15, 6, "has"], [15, 11, 15, 9], [15, 14, 15, 12, "require"], [15, 21, 15, 19], [15, 22, 15, 19, "_dependencyMap"], [15, 36, 15, 19], [15, 52, 15, 31], [15, 53, 15, 32], [16, 4, 17, 2, "printWarning"], [16, 16, 17, 14], [16, 19, 17, 17], [16, 28, 17, 17, "printWarning"], [16, 29, 17, 26, "text"], [16, 33, 17, 30], [16, 35, 17, 32], [17, 6, 18, 4], [17, 10, 18, 8, "message"], [17, 17, 18, 15], [17, 20, 18, 18], [17, 31, 18, 29], [17, 34, 18, 32, "text"], [17, 38, 18, 36], [18, 6, 19, 4], [18, 10, 19, 8], [18, 17, 19, 15, "console"], [18, 24, 19, 22], [18, 29, 19, 27], [18, 40, 19, 38], [18, 42, 19, 40], [19, 8, 20, 6, "console"], [19, 15, 20, 13], [19, 16, 20, 14, "error"], [19, 21, 20, 19], [19, 22, 20, 20, "message"], [19, 29, 20, 27], [19, 30, 20, 28], [20, 6, 21, 4], [21, 6, 22, 4], [21, 10, 22, 8], [22, 8, 23, 6], [23, 8, 24, 6], [24, 8, 25, 6], [25, 8, 26, 6], [25, 14, 26, 12], [25, 18, 26, 16, "Error"], [25, 23, 26, 21], [25, 24, 26, 22, "message"], [25, 31, 26, 29], [25, 32, 26, 30], [26, 6, 27, 4], [26, 7, 27, 5], [26, 8, 27, 6], [26, 15, 27, 13, "x"], [26, 16, 27, 14], [26, 18, 27, 16], [26, 19, 27, 18], [27, 4, 28, 2], [27, 5, 28, 3], [28, 2, 29, 0], [30, 2, 31, 0], [31, 0, 32, 0], [32, 0, 33, 0], [33, 0, 34, 0], [34, 0, 35, 0], [35, 0, 36, 0], [36, 0, 37, 0], [37, 0, 38, 0], [38, 0, 39, 0], [39, 0, 40, 0], [40, 0, 41, 0], [41, 2, 42, 0], [41, 11, 42, 9, "checkPropTypes"], [41, 25, 42, 23, "checkPropTypes"], [41, 26, 42, 24, "typeSpecs"], [41, 35, 42, 33], [41, 37, 42, 35, "values"], [41, 43, 42, 41], [41, 45, 42, 43, "location"], [41, 53, 42, 51], [41, 55, 42, 53, "componentName"], [41, 68, 42, 66], [41, 70, 42, 68, "getStack"], [41, 78, 42, 76], [41, 80, 42, 78], [42, 4, 43, 2], [42, 8, 43, 6, "process"], [42, 15, 43, 13], [42, 16, 43, 14, "env"], [42, 19, 43, 17], [42, 20, 43, 18, "NODE_ENV"], [42, 28, 43, 26], [42, 33, 43, 31], [42, 45, 43, 43], [42, 47, 43, 45], [43, 6, 44, 4], [43, 11, 44, 9], [43, 15, 44, 13, "typeSpecName"], [43, 27, 44, 25], [43, 31, 44, 29, "typeSpecs"], [43, 40, 44, 38], [43, 42, 44, 40], [44, 8, 45, 6], [44, 12, 45, 10, "has"], [44, 15, 45, 13], [44, 16, 45, 14, "typeSpecs"], [44, 25, 45, 23], [44, 27, 45, 25, "typeSpecName"], [44, 39, 45, 37], [44, 40, 45, 38], [44, 42, 45, 40], [45, 10, 46, 8], [45, 14, 46, 12, "error"], [45, 19, 46, 17], [46, 10, 47, 8], [47, 10, 48, 8], [48, 10, 49, 8], [49, 10, 50, 8], [49, 14, 50, 12], [50, 12, 51, 10], [51, 12, 52, 10], [52, 12, 53, 10], [52, 16, 53, 14], [52, 23, 53, 21, "typeSpecs"], [52, 32, 53, 30], [52, 33, 53, 31, "typeSpecName"], [52, 45, 53, 43], [52, 46, 53, 44], [52, 51, 53, 49], [52, 61, 53, 59], [52, 63, 53, 61], [53, 14, 54, 12], [53, 18, 54, 16, "err"], [53, 21, 54, 19], [53, 24, 54, 22, "Error"], [53, 29, 54, 27], [53, 30, 55, 14], [53, 31, 55, 15, "componentName"], [53, 44, 55, 28], [53, 48, 55, 32], [53, 61, 55, 45], [53, 65, 55, 49], [53, 69, 55, 53], [53, 72, 55, 56, "location"], [53, 80, 55, 64], [53, 83, 55, 67], [53, 92, 55, 76], [53, 95, 55, 79, "typeSpecName"], [53, 107, 55, 91], [53, 110, 55, 94], [53, 126, 55, 110], [53, 129, 56, 14], [53, 207, 56, 92], [53, 210, 56, 95], [53, 217, 56, 102, "typeSpecs"], [53, 226, 56, 111], [53, 227, 56, 112, "typeSpecName"], [53, 239, 56, 124], [53, 240, 56, 125], [53, 243, 56, 128], [53, 247, 56, 132], [53, 250, 57, 14], [53, 345, 58, 12], [53, 346, 58, 13], [54, 14, 59, 12, "err"], [54, 17, 59, 15], [54, 18, 59, 16, "name"], [54, 22, 59, 20], [54, 25, 59, 23], [54, 46, 59, 44], [55, 14, 60, 12], [55, 20, 60, 18, "err"], [55, 23, 60, 21], [56, 12, 61, 10], [57, 12, 62, 10, "error"], [57, 17, 62, 15], [57, 20, 62, 18, "typeSpecs"], [57, 29, 62, 27], [57, 30, 62, 28, "typeSpecName"], [57, 42, 62, 40], [57, 43, 62, 41], [57, 44, 62, 42, "values"], [57, 50, 62, 48], [57, 52, 62, 50, "typeSpecName"], [57, 64, 62, 62], [57, 66, 62, 64, "componentName"], [57, 79, 62, 77], [57, 81, 62, 79, "location"], [57, 89, 62, 87], [57, 91, 62, 89], [57, 95, 62, 93], [57, 97, 62, 95, "ReactPropTypesSecret"], [57, 117, 62, 115], [57, 118, 62, 116], [58, 10, 63, 8], [58, 11, 63, 9], [58, 12, 63, 10], [58, 19, 63, 17, "ex"], [58, 21, 63, 19], [58, 23, 63, 21], [59, 12, 64, 10, "error"], [59, 17, 64, 15], [59, 20, 64, 18, "ex"], [59, 22, 64, 20], [60, 10, 65, 8], [61, 10, 66, 8], [61, 14, 66, 12, "error"], [61, 19, 66, 17], [61, 23, 66, 21], [61, 25, 66, 23, "error"], [61, 30, 66, 28], [61, 42, 66, 40, "Error"], [61, 47, 66, 45], [61, 48, 66, 46], [61, 50, 66, 48], [62, 12, 67, 10, "printWarning"], [62, 24, 67, 22], [62, 25, 68, 12], [62, 26, 68, 13, "componentName"], [62, 39, 68, 26], [62, 43, 68, 30], [62, 56, 68, 43], [62, 60, 68, 47], [62, 86, 68, 73], [62, 89, 69, 12, "location"], [62, 97, 69, 20], [62, 100, 69, 23], [62, 104, 69, 27], [62, 107, 69, 30, "typeSpecName"], [62, 119, 69, 42], [62, 122, 69, 45], [62, 155, 69, 78], [62, 158, 70, 12], [62, 217, 70, 71], [62, 220, 70, 74], [62, 227, 70, 81, "error"], [62, 232, 70, 86], [62, 235, 70, 89], [62, 239, 70, 93], [62, 242, 71, 12], [62, 307, 71, 77], [62, 310, 72, 12], [62, 374, 72, 76], [62, 377, 73, 12], [62, 410, 74, 10], [62, 411, 74, 11], [63, 10, 75, 8], [64, 10, 76, 8], [64, 14, 76, 12, "error"], [64, 19, 76, 17], [64, 31, 76, 29, "Error"], [64, 36, 76, 34], [64, 40, 76, 38], [64, 42, 76, 40, "error"], [64, 47, 76, 45], [64, 48, 76, 46, "message"], [64, 55, 76, 53], [64, 59, 76, 57, "loggedTypeFailures"], [64, 77, 76, 75], [64, 78, 76, 76], [64, 80, 76, 78], [65, 12, 77, 10], [66, 12, 78, 10], [67, 12, 79, 10, "loggedTypeFailures"], [67, 30, 79, 28], [67, 31, 79, 29, "error"], [67, 36, 79, 34], [67, 37, 79, 35, "message"], [67, 44, 79, 42], [67, 45, 79, 43], [67, 48, 79, 46], [67, 52, 79, 50], [68, 12, 81, 10], [68, 16, 81, 14, "stack"], [68, 21, 81, 19], [68, 24, 81, 22, "getStack"], [68, 32, 81, 30], [68, 35, 81, 33, "getStack"], [68, 43, 81, 41], [68, 44, 81, 42], [68, 45, 81, 43], [68, 48, 81, 46], [68, 50, 81, 48], [69, 12, 83, 10, "printWarning"], [69, 24, 83, 22], [69, 25, 84, 12], [69, 34, 84, 21], [69, 37, 84, 24, "location"], [69, 45, 84, 32], [69, 48, 84, 35], [69, 57, 84, 44], [69, 60, 84, 47, "error"], [69, 65, 84, 52], [69, 66, 84, 53, "message"], [69, 73, 84, 60], [69, 77, 84, 64, "stack"], [69, 82, 84, 69], [69, 86, 84, 73], [69, 90, 84, 77], [69, 93, 84, 80, "stack"], [69, 98, 84, 85], [69, 101, 84, 88], [69, 103, 84, 90], [69, 104, 85, 10], [69, 105, 85, 11], [70, 10, 86, 8], [71, 8, 87, 6], [72, 6, 88, 4], [73, 4, 89, 2], [74, 2, 90, 0], [76, 2, 92, 0], [77, 0, 93, 0], [78, 0, 94, 0], [79, 0, 95, 0], [80, 0, 96, 0], [81, 2, 97, 0, "checkPropTypes"], [81, 16, 97, 14], [81, 17, 97, 15, "resetWarningCache"], [81, 34, 97, 32], [81, 37, 97, 35], [81, 49, 97, 46], [82, 4, 98, 2], [82, 8, 98, 6, "process"], [82, 15, 98, 13], [82, 16, 98, 14, "env"], [82, 19, 98, 17], [82, 20, 98, 18, "NODE_ENV"], [82, 28, 98, 26], [82, 33, 98, 31], [82, 45, 98, 43], [82, 47, 98, 45], [83, 6, 99, 4, "loggedTypeFailures"], [83, 24, 99, 22], [83, 27, 99, 25], [83, 28, 99, 26], [83, 29, 99, 27], [84, 4, 100, 2], [85, 2, 101, 0], [85, 3, 101, 1], [86, 2, 103, 0, "module"], [86, 8, 103, 6], [86, 9, 103, 7, "exports"], [86, 16, 103, 14], [86, 19, 103, 17, "checkPropTypes"], [86, 33, 103, 31], [87, 0, 103, 32], [87, 3]], "functionMap": {"names": ["<global>", "printWarning", "checkPropTypes", "resetWarningCache"], "mappings": "AAA;mBCS,aD;iBCO;GDW;AEc;CFgD;mCGO;CHI"}}, "type": "js/module"}]}