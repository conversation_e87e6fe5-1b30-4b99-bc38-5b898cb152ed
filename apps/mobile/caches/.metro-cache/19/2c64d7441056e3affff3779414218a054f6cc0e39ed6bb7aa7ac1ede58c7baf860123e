{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "./ReactNativeVersion", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 58}}], "key": "i/FfB0LFWIS8flpujJDW6cFjbds=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.checkVersions = checkVersions;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../Utilities/Platform\"));\n  var ReactNativeVersion = require(_dependencyMap[2], \"./ReactNativeVersion\");\n  function checkVersions() {\n    var nativeVersion = _Platform.default.constants.reactNativeVersion;\n    if (ReactNativeVersion.version.major !== nativeVersion.major || ReactNativeVersion.version.minor !== nativeVersion.minor) {\n      console.error(`React Native version mismatch.\\n\\nJavaScript version: ${_formatVersion(ReactNativeVersion.version)}\\n` + `Native version: ${_formatVersion(nativeVersion)}\\n\\n` + 'Make sure that you have rebuilt the native code. If the problem ' + 'persists try clearing the Watchman and packager caches with ' + '`watchman watch-del-all && npx react-native start --reset-cache`.');\n    }\n  }\n  function _formatVersion(version) {\n    return `${version.major}.${version.minor}.${version.patch}` + (version.prerelease != undefined ? `-${version.prerelease}` : '');\n  }\n});", "lineCount": 18, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_Platform"], [7, 15, 11, 0], [7, 18, 11, 0, "_interopRequireDefault"], [7, 40, 11, 0], [7, 41, 11, 0, "require"], [7, 48, 11, 0], [7, 49, 11, 0, "_dependencyMap"], [7, 63, 11, 0], [8, 2, 13, 0], [8, 6, 13, 6, "ReactNativeVersion"], [8, 24, 13, 24], [8, 27, 13, 27, "require"], [8, 34, 13, 34], [8, 35, 13, 34, "_dependencyMap"], [8, 49, 13, 34], [8, 76, 13, 57], [8, 77, 13, 58], [9, 2, 24, 7], [9, 11, 24, 16, "checkVersions"], [9, 24, 24, 29, "checkVersions"], [9, 25, 24, 29], [9, 27, 24, 38], [10, 4, 25, 2], [10, 8, 25, 8, "nativeVersion"], [10, 21, 25, 21], [10, 24, 25, 24, "Platform"], [10, 41, 25, 32], [10, 42, 25, 33, "constants"], [10, 51, 25, 42], [10, 52, 25, 43, "reactNativeVersion"], [10, 70, 25, 61], [11, 4, 26, 2], [11, 8, 27, 4, "ReactNativeVersion"], [11, 26, 27, 22], [11, 27, 27, 23, "version"], [11, 34, 27, 30], [11, 35, 27, 31, "major"], [11, 40, 27, 36], [11, 45, 27, 41, "nativeVersion"], [11, 58, 27, 54], [11, 59, 27, 55, "major"], [11, 64, 27, 60], [11, 68, 28, 4, "ReactNativeVersion"], [11, 86, 28, 22], [11, 87, 28, 23, "version"], [11, 94, 28, 30], [11, 95, 28, 31, "minor"], [11, 100, 28, 36], [11, 105, 28, 41, "nativeVersion"], [11, 118, 28, 54], [11, 119, 28, 55, "minor"], [11, 124, 28, 60], [11, 126, 29, 4], [12, 6, 30, 4, "console"], [12, 13, 30, 11], [12, 14, 30, 12, "error"], [12, 19, 30, 17], [12, 20, 31, 6], [12, 77, 31, 63, "_formatVersion"], [12, 91, 31, 77], [12, 92, 32, 9, "ReactNativeVersion"], [12, 110, 32, 27], [12, 111, 32, 28, "version"], [12, 118, 33, 6], [12, 119, 33, 7], [12, 123, 33, 11], [12, 126, 34, 8], [12, 145, 34, 27, "_formatVersion"], [12, 159, 34, 41], [12, 160, 34, 42, "nativeVersion"], [12, 173, 34, 55], [12, 174, 34, 56], [12, 180, 34, 62], [12, 183, 35, 8], [12, 249, 35, 74], [12, 252, 36, 8], [12, 314, 36, 70], [12, 317, 37, 8], [12, 384, 38, 4], [12, 385, 38, 5], [13, 4, 39, 2], [14, 2, 40, 0], [15, 2, 42, 0], [15, 11, 42, 9, "_formatVersion"], [15, 25, 42, 23, "_formatVersion"], [15, 26, 43, 2, "version"], [15, 33, 43, 63], [15, 35, 44, 10], [16, 4, 45, 2], [16, 11, 46, 4], [16, 14, 46, 7, "version"], [16, 21, 46, 14], [16, 22, 46, 15, "major"], [16, 27, 46, 20], [16, 31, 46, 24, "version"], [16, 38, 46, 31], [16, 39, 46, 32, "minor"], [16, 44, 46, 37], [16, 48, 46, 41, "version"], [16, 55, 46, 48], [16, 56, 46, 49, "patch"], [16, 61, 46, 54], [16, 63, 46, 56], [16, 67, 48, 5, "version"], [16, 74, 48, 12], [16, 75, 48, 13, "prerelease"], [16, 85, 48, 23], [16, 89, 48, 27, "undefined"], [16, 98, 48, 36], [16, 101, 48, 39], [16, 105, 48, 43, "version"], [16, 112, 48, 50], [16, 113, 48, 51, "prerelease"], [16, 123, 48, 61], [16, 125, 48, 63], [16, 128, 48, 66], [16, 130, 48, 68], [16, 131, 48, 69], [17, 2, 50, 0], [18, 0, 50, 1], [18, 3]], "functionMap": {"names": ["<global>", "checkVersions", "_formatVersion"], "mappings": "AAA;OCuB;CDgB;AEE"}}, "type": "js/module"}]}