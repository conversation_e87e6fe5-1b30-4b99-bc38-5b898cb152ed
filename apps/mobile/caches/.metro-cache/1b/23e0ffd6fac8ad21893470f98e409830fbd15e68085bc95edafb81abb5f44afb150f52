{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createReactNativeDocumentElementInstanceHandle = createReactNativeDocumentElementInstanceHandle;\n  exports.getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle = getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle;\n  exports.getPublicInstanceFromReactNativeDocumentElementInstanceHandle = getPublicInstanceFromReactNativeDocumentElementInstanceHandle;\n  exports.isReactNativeDocumentElementInstanceHandle = isReactNativeDocumentElementInstanceHandle;\n  exports.setNativeElementReferenceForReactNativeDocumentElementInstanceHandle = setNativeElementReferenceForReactNativeDocumentElementInstanceHandle;\n  exports.setPublicInstanceForReactNativeDocumentElementInstanceHandle = setPublicInstanceForReactNativeDocumentElementInstanceHandle;\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var ReactNativeDocumentElementInstanceHandleImpl = /*#__PURE__*/(0, _createClass2.default)(function ReactNativeDocumentElementInstanceHandleImpl() {\n    (0, _classCallCheck2.default)(this, ReactNativeDocumentElementInstanceHandleImpl);\n  });\n  function createReactNativeDocumentElementInstanceHandle() {\n    return new ReactNativeDocumentElementInstanceHandleImpl();\n  }\n  function getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle(instanceHandle) {\n    return instanceHandle.nativeElementReference;\n  }\n  function setNativeElementReferenceForReactNativeDocumentElementInstanceHandle(instanceHandle, nativeElementReference) {\n    instanceHandle.nativeElementReference = nativeElementReference;\n  }\n  function getPublicInstanceFromReactNativeDocumentElementInstanceHandle(instanceHandle) {\n    return instanceHandle.publicInstance;\n  }\n  function setPublicInstanceForReactNativeDocumentElementInstanceHandle(instanceHandle, publicInstance) {\n    instanceHandle.publicInstance = publicInstance;\n  }\n  function isReactNativeDocumentElementInstanceHandle(instanceHandle) {\n    return instanceHandle instanceof ReactNativeDocumentElementInstanceHandleImpl;\n  }\n});", "lineCount": 35, "map": [[14, 6, 14, 6, "ReactNativeDocumentElementInstanceHandleImpl"], [14, 50, 14, 50], [14, 70, 14, 50, "_createClass2"], [14, 83, 14, 50], [14, 84, 14, 50, "default"], [14, 91, 14, 50], [14, 102, 14, 50, "ReactNativeDocumentElementInstanceHandleImpl"], [14, 147, 14, 50], [15, 4, 14, 50], [15, 8, 14, 50, "_classCallCheck2"], [15, 24, 14, 50], [15, 25, 14, 50, "default"], [15, 32, 14, 50], [15, 40, 14, 50, "ReactNativeDocumentElementInstanceHandleImpl"], [15, 84, 14, 50], [16, 2, 14, 50], [17, 2, 21, 7], [17, 11, 21, 16, "createReactNativeDocumentElementInstanceHandle"], [17, 57, 21, 62, "createReactNativeDocumentElementInstanceHandle"], [17, 58, 21, 62], [17, 60, 21, 107], [18, 4, 22, 2], [18, 11, 22, 9], [18, 15, 22, 13, "ReactNativeDocumentElementInstanceHandleImpl"], [18, 59, 22, 57], [18, 60, 22, 58], [18, 61, 22, 59], [19, 2, 23, 0], [20, 2, 25, 7], [20, 11, 25, 16, "getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle"], [20, 80, 25, 85, "getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle"], [20, 81, 26, 2, "instanceHandle"], [20, 95, 26, 58], [20, 97, 27, 27], [21, 4, 28, 2], [21, 11, 28, 9, "instanceHandle"], [21, 25, 28, 23], [21, 26, 28, 24, "nativeElementReference"], [21, 48, 28, 46], [22, 2, 29, 0], [23, 2, 31, 7], [23, 11, 31, 16, "setNativeElementReferenceForReactNativeDocumentElementInstanceHandle"], [23, 79, 31, 84, "setNativeElementReferenceForReactNativeDocumentElementInstanceHandle"], [23, 80, 32, 2, "instanceHandle"], [23, 94, 32, 58], [23, 96, 33, 2, "nativeElementReference"], [23, 118, 33, 49], [23, 120, 34, 8], [24, 4, 35, 2, "instanceHandle"], [24, 18, 35, 16], [24, 19, 35, 17, "nativeElementReference"], [24, 41, 35, 39], [24, 44, 35, 42, "nativeElementReference"], [24, 66, 35, 64], [25, 2, 36, 0], [26, 2, 38, 7], [26, 11, 38, 16, "getPublicInstanceFromReactNativeDocumentElementInstanceHandle"], [26, 72, 38, 77, "getPublicInstanceFromReactNativeDocumentElementInstanceHandle"], [26, 73, 39, 2, "instanceHandle"], [26, 87, 39, 58], [26, 89, 40, 17], [27, 4, 41, 2], [27, 11, 41, 9, "instanceHandle"], [27, 25, 41, 23], [27, 26, 41, 24, "publicInstance"], [27, 40, 41, 38], [28, 2, 42, 0], [29, 2, 44, 7], [29, 11, 44, 16, "setPublicInstanceForReactNativeDocumentElementInstanceHandle"], [29, 71, 44, 76, "setPublicInstanceForReactNativeDocumentElementInstanceHandle"], [29, 72, 45, 2, "instanceHandle"], [29, 86, 45, 58], [29, 88, 46, 2, "publicInstance"], [29, 102, 46, 31], [29, 104, 47, 8], [30, 4, 48, 2, "instanceHandle"], [30, 18, 48, 16], [30, 19, 48, 17, "publicInstance"], [30, 33, 48, 31], [30, 36, 48, 34, "publicInstance"], [30, 50, 48, 48], [31, 2, 49, 0], [32, 2, 51, 7], [32, 11, 51, 16, "isReactNativeDocumentElementInstanceHandle"], [32, 53, 51, 58, "isReactNativeDocumentElementInstanceHandle"], [32, 54, 52, 2, "instanceHandle"], [32, 68, 52, 23], [32, 70, 53, 62], [33, 4, 54, 2], [33, 11, 54, 9, "instanceHandle"], [33, 25, 54, 23], [33, 37, 54, 35, "ReactNativeDocumentElementInstanceHandleImpl"], [33, 81, 54, 79], [34, 2, 55, 0], [35, 0, 55, 1], [35, 3]], "functionMap": {"names": ["<global>", "ReactNativeDocumentElementInstanceHandleImpl", "createReactNativeDocumentElementInstanceHandle", "getNativeElementReferenceFromReactNativeDocumentElementInstanceHandle", "setNativeElementReferenceForReactNativeDocumentElementInstanceHandle", "getPublicInstanceFromReactNativeDocumentElementInstanceHandle", "setPublicInstanceForReactNativeDocumentElementInstanceHandle", "isReactNativeDocumentElementInstanceHandle"], "mappings": "AAA;ACa;CDG;OEI;CFE;OGE;CHI;OIE;CJK;OKE;CLI;OME;CNK;OOE"}}, "type": "js/module"}]}