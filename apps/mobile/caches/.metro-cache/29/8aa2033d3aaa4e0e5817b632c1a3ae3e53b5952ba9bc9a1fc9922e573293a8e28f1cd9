{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 98}}], "key": "fdTx5edELD8GYD7vaakWfKKte1Y=", "exportNames": ["*"]}}, {"name": "../../src/private/featureflags/specs/NativeReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 111}}], "key": "NejBJTGtZ8egYXexrx+ej/SB8V4=", "exportNames": ["*"]}}, {"name": "../Components/View/ReactNativeStyleAttributes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 87}}], "key": "LRZpG48GR8owQQPluG+E1VB5zh8=", "exportNames": ["*"]}}, {"name": "./ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 19, "column": 28}}], "key": "GOS7wR7hJbEacSQhF8FI1Fg9tiM=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 201, "column": 29}, "end": {"line": 201, "column": 66}}, {"start": {"line": 205, "column": 25}, "end": {"line": 205, "column": 62}}, {"start": {"line": 221, "column": 25}, "end": {"line": 221, "column": 62}}, {"start": {"line": 222, "column": 30}, "end": {"line": 222, "column": 67}}, {"start": {"line": 248, "column": 28}, "end": {"line": 248, "column": 65}}, {"start": {"line": 250, "column": 30}, "end": {"line": 250, "column": 67}}, {"start": {"line": 252, "column": 31}, "end": {"line": 252, "column": 68}}, {"start": {"line": 254, "column": 29}, "end": {"line": 254, "column": 66}}, {"start": {"line": 257, "column": 30}, "end": {"line": 257, "column": 67}}, {"start": {"line": 259, "column": 13}, "end": {"line": 259, "column": 50}}, {"start": {"line": 263, "column": 28}, "end": {"line": 263, "column": 65}}, {"start": {"line": 264, "column": 33}, "end": {"line": 264, "column": 70}}], "key": "I0Lk++/6Upr1uZbth/i3RrMPl94=", "exportNames": ["*"]}}, {"name": "../Utilities/differ/sizesDiffer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 206, "column": 23}, "end": {"line": 206, "column": 65}}], "key": "tW+/0bQV3nPvDV87rLRrBolqwBo=", "exportNames": ["*"]}}, {"name": "../Utilities/differ/matrices<PERSON><PERSON>er", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 212, "column": 20}, "end": {"line": 212, "column": 65}}], "key": "pUkOgPTmXrMaamR7P8Zy2VbJ+K8=", "exportNames": ["*"]}}, {"name": "../Utilities/differ/insetsDiffer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 227, "column": 18}, "end": {"line": 227, "column": 61}}], "key": "KxuLXa88VxMso/Uwq2CreKnxRGc=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processFilter", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 235, "column": 19}, "end": {"line": 235, "column": 57}}], "key": "PH3OhIHU7mHt5nNvgvnyCytfZGA=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processBoxShadow", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 242, "column": 19}, "end": {"line": 242, "column": 60}}], "key": "Y9zjfv4IO0Tw1qs0sMCmU4vk3IE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[1], \"../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  var _NativeReactNativeFeatureFlags = _interopRequireDefault(require(_dependencyMap[2], \"../../src/private/featureflags/specs/NativeReactNativeFeatureFlags\"));\n  var _ReactNativeStyleAttributes = _interopRequireDefault(require(_dependencyMap[3], \"../Components/View/ReactNativeStyleAttributes\"));\n  var _ViewConfigIgnore = require(_dependencyMap[4], \"./ViewConfigIgnore\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var bubblingEventTypes = {\n    topPress: {\n      phasedRegistrationNames: {\n        bubbled: 'onPress',\n        captured: 'onPressCapture'\n      }\n    },\n    topChange: {\n      phasedRegistrationNames: {\n        bubbled: 'onChange',\n        captured: 'onChangeCapture'\n      }\n    },\n    topFocus: {\n      phasedRegistrationNames: {\n        bubbled: 'onFocus',\n        captured: 'onFocusCapture'\n      }\n    },\n    topBlur: {\n      phasedRegistrationNames: {\n        bubbled: 'onBlur',\n        captured: 'onBlurCapture'\n      }\n    },\n    topSubmitEditing: {\n      phasedRegistrationNames: {\n        bubbled: 'onSubmitEditing',\n        captured: 'onSubmitEditingCapture'\n      }\n    },\n    topEndEditing: {\n      phasedRegistrationNames: {\n        bubbled: 'onEndEditing',\n        captured: 'onEndEditingCapture'\n      }\n    },\n    topKeyPress: {\n      phasedRegistrationNames: {\n        bubbled: 'onKeyPress',\n        captured: 'onKeyPressCapture'\n      }\n    },\n    topTouchStart: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchStart',\n        captured: 'onTouchStartCapture'\n      }\n    },\n    topTouchMove: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchMove',\n        captured: 'onTouchMoveCapture'\n      }\n    },\n    topTouchCancel: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchCancel',\n        captured: 'onTouchCancelCapture'\n      }\n    },\n    topTouchEnd: {\n      phasedRegistrationNames: {\n        bubbled: 'onTouchEnd',\n        captured: 'onTouchEndCapture'\n      }\n    },\n    topClick: {\n      phasedRegistrationNames: {\n        captured: 'onClickCapture',\n        bubbled: 'onClick'\n      }\n    },\n    topPointerCancel: {\n      phasedRegistrationNames: {\n        captured: 'onPointerCancelCapture',\n        bubbled: 'onPointerCancel'\n      }\n    },\n    topPointerDown: {\n      phasedRegistrationNames: {\n        captured: 'onPointerDownCapture',\n        bubbled: 'onPointerDown'\n      }\n    },\n    topPointerMove: {\n      phasedRegistrationNames: {\n        captured: 'onPointerMoveCapture',\n        bubbled: 'onPointerMove'\n      }\n    },\n    topPointerUp: {\n      phasedRegistrationNames: {\n        captured: 'onPointerUpCapture',\n        bubbled: 'onPointerUp'\n      }\n    },\n    topPointerEnter: {\n      phasedRegistrationNames: {\n        captured: 'onPointerEnterCapture',\n        bubbled: 'onPointerEnter',\n        skipBubbling: true\n      }\n    },\n    topPointerLeave: {\n      phasedRegistrationNames: {\n        captured: 'onPointerLeaveCapture',\n        bubbled: 'onPointerLeave',\n        skipBubbling: true\n      }\n    },\n    topPointerOver: {\n      phasedRegistrationNames: {\n        captured: 'onPointerOverCapture',\n        bubbled: 'onPointerOver'\n      }\n    },\n    topPointerOut: {\n      phasedRegistrationNames: {\n        captured: 'onPointerOutCapture',\n        bubbled: 'onPointerOut'\n      }\n    },\n    topGotPointerCapture: {\n      phasedRegistrationNames: {\n        captured: 'onGotPointerCaptureCapture',\n        bubbled: 'onGotPointerCapture'\n      }\n    },\n    topLostPointerCapture: {\n      phasedRegistrationNames: {\n        captured: 'onLostPointerCaptureCapture',\n        bubbled: 'onLostPointerCapture'\n      }\n    }\n  };\n  var directEventTypes = {\n    topAccessibilityAction: {\n      registrationName: 'onAccessibilityAction'\n    },\n    topAccessibilityTap: {\n      registrationName: 'onAccessibilityTap'\n    },\n    topMagicTap: {\n      registrationName: 'onMagicTap'\n    },\n    topAccessibilityEscape: {\n      registrationName: 'onAccessibilityEscape'\n    },\n    topLayout: {\n      registrationName: 'onLayout'\n    },\n    onGestureHandlerEvent: (0, _ViewConfigIgnore.DynamicallyInjectedByGestureHandler)({\n      registrationName: 'onGestureHandlerEvent'\n    }),\n    onGestureHandlerStateChange: (0, _ViewConfigIgnore.DynamicallyInjectedByGestureHandler)({\n      registrationName: 'onGestureHandlerStateChange'\n    })\n  };\n  var validAttributesForNonEventProps = {\n    accessible: true,\n    accessibilityActions: true,\n    accessibilityLabel: true,\n    accessibilityHint: true,\n    accessibilityLanguage: true,\n    accessibilityValue: true,\n    accessibilityViewIsModal: true,\n    accessibilityElementsHidden: true,\n    accessibilityIgnoresInvertColors: true,\n    accessibilityShowsLargeContentViewer: true,\n    accessibilityLargeContentTitle: true,\n    testID: true,\n    backgroundColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    backfaceVisibility: true,\n    cursor: true,\n    opacity: true,\n    shadowColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    shadowOffset: {\n      diff: require(_dependencyMap[6], \"../Utilities/differ/sizesDiffer\").default\n    },\n    shadowOpacity: true,\n    shadowRadius: true,\n    needsOffscreenAlphaCompositing: true,\n    overflow: true,\n    shouldRasterizeIOS: true,\n    transform: {\n      diff: require(_dependencyMap[7], \"../Utilities/differ/matricesDiffer\").default\n    },\n    transformOrigin: true,\n    accessibilityRole: true,\n    accessibilityState: true,\n    nativeID: true,\n    pointerEvents: true,\n    removeClippedSubviews: true,\n    role: true,\n    borderRadius: true,\n    borderColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBlockColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderCurve: true,\n    borderWidth: true,\n    borderBlockWidth: true,\n    borderStyle: true,\n    hitSlop: {\n      diff: require(_dependencyMap[8], \"../Utilities/differ/insetsDiffer\").default\n    },\n    collapsable: true,\n    collapsableChildren: true,\n    filter: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {\n      process: require(_dependencyMap[9], \"../StyleSheet/processFilter\").default\n    },\n    boxShadow: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {\n      process: require(_dependencyMap[10], \"../StyleSheet/processBoxShadow\").default\n    },\n    mixBlendMode: true,\n    isolation: true,\n    borderTopWidth: true,\n    borderTopColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderRightWidth: true,\n    borderRightColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBottomWidth: true,\n    borderBottomColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderLeftWidth: true,\n    borderLeftColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderStartWidth: true,\n    borderBlockStartWidth: true,\n    borderStartColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBlockStartColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderEndWidth: true,\n    borderBlockEndWidth: true,\n    borderEndColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderBlockEndColor: {\n      process: require(_dependencyMap[5], \"../StyleSheet/processColor\").default\n    },\n    borderTopLeftRadius: true,\n    borderTopRightRadius: true,\n    borderTopStartRadius: true,\n    borderTopEndRadius: true,\n    borderBottomLeftRadius: true,\n    borderBottomRightRadius: true,\n    borderBottomStartRadius: true,\n    borderBottomEndRadius: true,\n    borderEndEndRadius: true,\n    borderEndStartRadius: true,\n    borderStartEndRadius: true,\n    borderStartStartRadius: true,\n    display: true,\n    zIndex: true,\n    top: true,\n    right: true,\n    start: true,\n    end: true,\n    bottom: true,\n    left: true,\n    inset: true,\n    insetBlock: true,\n    insetBlockEnd: true,\n    insetBlockStart: true,\n    insetInline: true,\n    insetInlineEnd: true,\n    insetInlineStart: true,\n    width: true,\n    height: true,\n    minWidth: true,\n    maxWidth: true,\n    minHeight: true,\n    maxHeight: true,\n    margin: true,\n    marginBlock: true,\n    marginBlockEnd: true,\n    marginBlockStart: true,\n    marginBottom: true,\n    marginEnd: true,\n    marginHorizontal: true,\n    marginInline: true,\n    marginInlineEnd: true,\n    marginInlineStart: true,\n    marginLeft: true,\n    marginRight: true,\n    marginStart: true,\n    marginTop: true,\n    marginVertical: true,\n    padding: true,\n    paddingBlock: true,\n    paddingBlockEnd: true,\n    paddingBlockStart: true,\n    paddingBottom: true,\n    paddingEnd: true,\n    paddingHorizontal: true,\n    paddingInline: true,\n    paddingInlineEnd: true,\n    paddingInlineStart: true,\n    paddingLeft: true,\n    paddingRight: true,\n    paddingStart: true,\n    paddingTop: true,\n    paddingVertical: true,\n    flex: true,\n    flexGrow: true,\n    rowGap: true,\n    columnGap: true,\n    gap: true,\n    flexShrink: true,\n    flexBasis: true,\n    flexDirection: true,\n    flexWrap: true,\n    justifyContent: true,\n    alignItems: true,\n    alignSelf: true,\n    alignContent: true,\n    position: true,\n    aspectRatio: true,\n    boxSizing: true,\n    direction: true,\n    style: _ReactNativeStyleAttributes.default\n  };\n  var validAttributesForEventProps = (0, _ViewConfigIgnore.ConditionallyIgnoredEventHandlers)({\n    onLayout: true,\n    onMagicTap: true,\n    onAccessibilityAction: true,\n    onAccessibilityEscape: true,\n    onAccessibilityTap: true,\n    onMoveShouldSetResponder: true,\n    onMoveShouldSetResponderCapture: true,\n    onStartShouldSetResponder: true,\n    onStartShouldSetResponderCapture: true,\n    onResponderGrant: true,\n    onResponderReject: true,\n    onResponderStart: true,\n    onResponderEnd: true,\n    onResponderRelease: true,\n    onResponderMove: true,\n    onResponderTerminate: true,\n    onResponderTerminationRequest: true,\n    onShouldBlockNativeResponder: true,\n    onTouchStart: true,\n    onTouchMove: true,\n    onTouchEnd: true,\n    onTouchCancel: true,\n    onClick: true,\n    onClickCapture: true,\n    onPointerUp: true,\n    onPointerDown: true,\n    onPointerCancel: true,\n    onPointerEnter: true,\n    onPointerMove: true,\n    onPointerLeave: true,\n    onPointerOver: true,\n    onPointerOut: true,\n    onGotPointerCapture: true,\n    onLostPointerCapture: true\n  });\n  var PlatformBaseViewConfigIos = {\n    bubblingEventTypes,\n    directEventTypes,\n    validAttributes: {\n      ...validAttributesForNonEventProps,\n      ...validAttributesForEventProps\n    }\n  };\n  var _default = exports.default = PlatformBaseViewConfigIos;\n});", "lineCount": 394, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "ReactNativeFeatureFlags"], [7, 29, 13, 0], [7, 32, 13, 0, "_interopRequireWildcard"], [7, 55, 13, 0], [7, 56, 13, 0, "require"], [7, 63, 13, 0], [7, 64, 13, 0, "_dependencyMap"], [7, 78, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_NativeReactNativeFeatureFlags"], [8, 36, 14, 0], [8, 39, 14, 0, "_interopRequireDefault"], [8, 61, 14, 0], [8, 62, 14, 0, "require"], [8, 69, 14, 0], [8, 70, 14, 0, "_dependencyMap"], [8, 84, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_ReactNativeStyleAttributes"], [9, 33, 15, 0], [9, 36, 15, 0, "_interopRequireDefault"], [9, 58, 15, 0], [9, 59, 15, 0, "require"], [9, 66, 15, 0], [9, 67, 15, 0, "_dependencyMap"], [9, 81, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_ViewConfigIgnore"], [10, 23, 16, 0], [10, 26, 16, 0, "require"], [10, 33, 16, 0], [10, 34, 16, 0, "_dependencyMap"], [10, 48, 16, 0], [11, 2, 19, 28], [11, 11, 19, 28, "_interopRequireWildcard"], [11, 35, 19, 28, "e"], [11, 36, 19, 28], [11, 38, 19, 28, "t"], [11, 39, 19, 28], [11, 68, 19, 28, "WeakMap"], [11, 75, 19, 28], [11, 81, 19, 28, "r"], [11, 82, 19, 28], [11, 89, 19, 28, "WeakMap"], [11, 96, 19, 28], [11, 100, 19, 28, "n"], [11, 101, 19, 28], [11, 108, 19, 28, "WeakMap"], [11, 115, 19, 28], [11, 127, 19, 28, "_interopRequireWildcard"], [11, 150, 19, 28], [11, 162, 19, 28, "_interopRequireWildcard"], [11, 163, 19, 28, "e"], [11, 164, 19, 28], [11, 166, 19, 28, "t"], [11, 167, 19, 28], [11, 176, 19, 28, "t"], [11, 177, 19, 28], [11, 181, 19, 28, "e"], [11, 182, 19, 28], [11, 186, 19, 28, "e"], [11, 187, 19, 28], [11, 188, 19, 28, "__esModule"], [11, 198, 19, 28], [11, 207, 19, 28, "e"], [11, 208, 19, 28], [11, 214, 19, 28, "o"], [11, 215, 19, 28], [11, 217, 19, 28, "i"], [11, 218, 19, 28], [11, 220, 19, 28, "f"], [11, 221, 19, 28], [11, 226, 19, 28, "__proto__"], [11, 235, 19, 28], [11, 243, 19, 28, "default"], [11, 250, 19, 28], [11, 252, 19, 28, "e"], [11, 253, 19, 28], [11, 270, 19, 28, "e"], [11, 271, 19, 28], [11, 294, 19, 28, "e"], [11, 295, 19, 28], [11, 320, 19, 28, "e"], [11, 321, 19, 28], [11, 330, 19, 28, "f"], [11, 331, 19, 28], [11, 337, 19, 28, "o"], [11, 338, 19, 28], [11, 341, 19, 28, "t"], [11, 342, 19, 28], [11, 345, 19, 28, "n"], [11, 346, 19, 28], [11, 349, 19, 28, "r"], [11, 350, 19, 28], [11, 358, 19, 28, "o"], [11, 359, 19, 28], [11, 360, 19, 28, "has"], [11, 363, 19, 28], [11, 364, 19, 28, "e"], [11, 365, 19, 28], [11, 375, 19, 28, "o"], [11, 376, 19, 28], [11, 377, 19, 28, "get"], [11, 380, 19, 28], [11, 381, 19, 28, "e"], [11, 382, 19, 28], [11, 385, 19, 28, "o"], [11, 386, 19, 28], [11, 387, 19, 28, "set"], [11, 390, 19, 28], [11, 391, 19, 28, "e"], [11, 392, 19, 28], [11, 394, 19, 28, "f"], [11, 395, 19, 28], [11, 409, 19, 28, "_t"], [11, 411, 19, 28], [11, 415, 19, 28, "e"], [11, 416, 19, 28], [11, 432, 19, 28, "_t"], [11, 434, 19, 28], [11, 441, 19, 28, "hasOwnProperty"], [11, 455, 19, 28], [11, 456, 19, 28, "call"], [11, 460, 19, 28], [11, 461, 19, 28, "e"], [11, 462, 19, 28], [11, 464, 19, 28, "_t"], [11, 466, 19, 28], [11, 473, 19, 28, "i"], [11, 474, 19, 28], [11, 478, 19, 28, "o"], [11, 479, 19, 28], [11, 482, 19, 28, "Object"], [11, 488, 19, 28], [11, 489, 19, 28, "defineProperty"], [11, 503, 19, 28], [11, 508, 19, 28, "Object"], [11, 514, 19, 28], [11, 515, 19, 28, "getOwnPropertyDescriptor"], [11, 539, 19, 28], [11, 540, 19, 28, "e"], [11, 541, 19, 28], [11, 543, 19, 28, "_t"], [11, 545, 19, 28], [11, 552, 19, 28, "i"], [11, 553, 19, 28], [11, 554, 19, 28, "get"], [11, 557, 19, 28], [11, 561, 19, 28, "i"], [11, 562, 19, 28], [11, 563, 19, 28, "set"], [11, 566, 19, 28], [11, 570, 19, 28, "o"], [11, 571, 19, 28], [11, 572, 19, 28, "f"], [11, 573, 19, 28], [11, 575, 19, 28, "_t"], [11, 577, 19, 28], [11, 579, 19, 28, "i"], [11, 580, 19, 28], [11, 584, 19, 28, "f"], [11, 585, 19, 28], [11, 586, 19, 28, "_t"], [11, 588, 19, 28], [11, 592, 19, 28, "e"], [11, 593, 19, 28], [11, 594, 19, 28, "_t"], [11, 596, 19, 28], [11, 607, 19, 28, "f"], [11, 608, 19, 28], [11, 613, 19, 28, "e"], [11, 614, 19, 28], [11, 616, 19, 28, "t"], [11, 617, 19, 28], [12, 2, 21, 0], [12, 6, 21, 6, "bubblingEventTypes"], [12, 24, 21, 24], [12, 27, 21, 27], [13, 4, 23, 2, "topPress"], [13, 12, 23, 10], [13, 14, 23, 12], [14, 6, 24, 4, "phasedRegistrationNames"], [14, 29, 24, 27], [14, 31, 24, 29], [15, 8, 25, 6, "bubbled"], [15, 15, 25, 13], [15, 17, 25, 15], [15, 26, 25, 24], [16, 8, 26, 6, "captured"], [16, 16, 26, 14], [16, 18, 26, 16], [17, 6, 27, 4], [18, 4, 28, 2], [18, 5, 28, 3], [19, 4, 29, 2, "topChange"], [19, 13, 29, 11], [19, 15, 29, 13], [20, 6, 30, 4, "phasedRegistrationNames"], [20, 29, 30, 27], [20, 31, 30, 29], [21, 8, 31, 6, "bubbled"], [21, 15, 31, 13], [21, 17, 31, 15], [21, 27, 31, 25], [22, 8, 32, 6, "captured"], [22, 16, 32, 14], [22, 18, 32, 16], [23, 6, 33, 4], [24, 4, 34, 2], [24, 5, 34, 3], [25, 4, 35, 2, "topFocus"], [25, 12, 35, 10], [25, 14, 35, 12], [26, 6, 36, 4, "phasedRegistrationNames"], [26, 29, 36, 27], [26, 31, 36, 29], [27, 8, 37, 6, "bubbled"], [27, 15, 37, 13], [27, 17, 37, 15], [27, 26, 37, 24], [28, 8, 38, 6, "captured"], [28, 16, 38, 14], [28, 18, 38, 16], [29, 6, 39, 4], [30, 4, 40, 2], [30, 5, 40, 3], [31, 4, 41, 2, "topBlur"], [31, 11, 41, 9], [31, 13, 41, 11], [32, 6, 42, 4, "phasedRegistrationNames"], [32, 29, 42, 27], [32, 31, 42, 29], [33, 8, 43, 6, "bubbled"], [33, 15, 43, 13], [33, 17, 43, 15], [33, 25, 43, 23], [34, 8, 44, 6, "captured"], [34, 16, 44, 14], [34, 18, 44, 16], [35, 6, 45, 4], [36, 4, 46, 2], [36, 5, 46, 3], [37, 4, 47, 2, "topSubmitEditing"], [37, 20, 47, 18], [37, 22, 47, 20], [38, 6, 48, 4, "phasedRegistrationNames"], [38, 29, 48, 27], [38, 31, 48, 29], [39, 8, 49, 6, "bubbled"], [39, 15, 49, 13], [39, 17, 49, 15], [39, 34, 49, 32], [40, 8, 50, 6, "captured"], [40, 16, 50, 14], [40, 18, 50, 16], [41, 6, 51, 4], [42, 4, 52, 2], [42, 5, 52, 3], [43, 4, 53, 2, "topEndEditing"], [43, 17, 53, 15], [43, 19, 53, 17], [44, 6, 54, 4, "phasedRegistrationNames"], [44, 29, 54, 27], [44, 31, 54, 29], [45, 8, 55, 6, "bubbled"], [45, 15, 55, 13], [45, 17, 55, 15], [45, 31, 55, 29], [46, 8, 56, 6, "captured"], [46, 16, 56, 14], [46, 18, 56, 16], [47, 6, 57, 4], [48, 4, 58, 2], [48, 5, 58, 3], [49, 4, 59, 2, "topKeyPress"], [49, 15, 59, 13], [49, 17, 59, 15], [50, 6, 60, 4, "phasedRegistrationNames"], [50, 29, 60, 27], [50, 31, 60, 29], [51, 8, 61, 6, "bubbled"], [51, 15, 61, 13], [51, 17, 61, 15], [51, 29, 61, 27], [52, 8, 62, 6, "captured"], [52, 16, 62, 14], [52, 18, 62, 16], [53, 6, 63, 4], [54, 4, 64, 2], [54, 5, 64, 3], [55, 4, 67, 2, "topTouchStart"], [55, 17, 67, 15], [55, 19, 67, 17], [56, 6, 68, 4, "phasedRegistrationNames"], [56, 29, 68, 27], [56, 31, 68, 29], [57, 8, 69, 6, "bubbled"], [57, 15, 69, 13], [57, 17, 69, 15], [57, 31, 69, 29], [58, 8, 70, 6, "captured"], [58, 16, 70, 14], [58, 18, 70, 16], [59, 6, 71, 4], [60, 4, 72, 2], [60, 5, 72, 3], [61, 4, 73, 2, "topTouchMove"], [61, 16, 73, 14], [61, 18, 73, 16], [62, 6, 74, 4, "phasedRegistrationNames"], [62, 29, 74, 27], [62, 31, 74, 29], [63, 8, 75, 6, "bubbled"], [63, 15, 75, 13], [63, 17, 75, 15], [63, 30, 75, 28], [64, 8, 76, 6, "captured"], [64, 16, 76, 14], [64, 18, 76, 16], [65, 6, 77, 4], [66, 4, 78, 2], [66, 5, 78, 3], [67, 4, 79, 2, "topTouchCancel"], [67, 18, 79, 16], [67, 20, 79, 18], [68, 6, 80, 4, "phasedRegistrationNames"], [68, 29, 80, 27], [68, 31, 80, 29], [69, 8, 81, 6, "bubbled"], [69, 15, 81, 13], [69, 17, 81, 15], [69, 32, 81, 30], [70, 8, 82, 6, "captured"], [70, 16, 82, 14], [70, 18, 82, 16], [71, 6, 83, 4], [72, 4, 84, 2], [72, 5, 84, 3], [73, 4, 85, 2, "topTouchEnd"], [73, 15, 85, 13], [73, 17, 85, 15], [74, 6, 86, 4, "phasedRegistrationNames"], [74, 29, 86, 27], [74, 31, 86, 29], [75, 8, 87, 6, "bubbled"], [75, 15, 87, 13], [75, 17, 87, 15], [75, 29, 87, 27], [76, 8, 88, 6, "captured"], [76, 16, 88, 14], [76, 18, 88, 16], [77, 6, 89, 4], [78, 4, 90, 2], [78, 5, 90, 3], [79, 4, 93, 2, "topClick"], [79, 12, 93, 10], [79, 14, 93, 12], [80, 6, 94, 4, "phasedRegistrationNames"], [80, 29, 94, 27], [80, 31, 94, 29], [81, 8, 95, 6, "captured"], [81, 16, 95, 14], [81, 18, 95, 16], [81, 34, 95, 32], [82, 8, 96, 6, "bubbled"], [82, 15, 96, 13], [82, 17, 96, 15], [83, 6, 97, 4], [84, 4, 98, 2], [84, 5, 98, 3], [85, 4, 99, 2, "topPointerCancel"], [85, 20, 99, 18], [85, 22, 99, 20], [86, 6, 100, 4, "phasedRegistrationNames"], [86, 29, 100, 27], [86, 31, 100, 29], [87, 8, 101, 6, "captured"], [87, 16, 101, 14], [87, 18, 101, 16], [87, 42, 101, 40], [88, 8, 102, 6, "bubbled"], [88, 15, 102, 13], [88, 17, 102, 15], [89, 6, 103, 4], [90, 4, 104, 2], [90, 5, 104, 3], [91, 4, 105, 2, "topPointerDown"], [91, 18, 105, 16], [91, 20, 105, 18], [92, 6, 106, 4, "phasedRegistrationNames"], [92, 29, 106, 27], [92, 31, 106, 29], [93, 8, 107, 6, "captured"], [93, 16, 107, 14], [93, 18, 107, 16], [93, 40, 107, 38], [94, 8, 108, 6, "bubbled"], [94, 15, 108, 13], [94, 17, 108, 15], [95, 6, 109, 4], [96, 4, 110, 2], [96, 5, 110, 3], [97, 4, 111, 2, "topPointerMove"], [97, 18, 111, 16], [97, 20, 111, 18], [98, 6, 112, 4, "phasedRegistrationNames"], [98, 29, 112, 27], [98, 31, 112, 29], [99, 8, 113, 6, "captured"], [99, 16, 113, 14], [99, 18, 113, 16], [99, 40, 113, 38], [100, 8, 114, 6, "bubbled"], [100, 15, 114, 13], [100, 17, 114, 15], [101, 6, 115, 4], [102, 4, 116, 2], [102, 5, 116, 3], [103, 4, 117, 2, "topPointerUp"], [103, 16, 117, 14], [103, 18, 117, 16], [104, 6, 118, 4, "phasedRegistrationNames"], [104, 29, 118, 27], [104, 31, 118, 29], [105, 8, 119, 6, "captured"], [105, 16, 119, 14], [105, 18, 119, 16], [105, 38, 119, 36], [106, 8, 120, 6, "bubbled"], [106, 15, 120, 13], [106, 17, 120, 15], [107, 6, 121, 4], [108, 4, 122, 2], [108, 5, 122, 3], [109, 4, 123, 2, "topPointerEnter"], [109, 19, 123, 17], [109, 21, 123, 19], [110, 6, 124, 4, "phasedRegistrationNames"], [110, 29, 124, 27], [110, 31, 124, 29], [111, 8, 125, 6, "captured"], [111, 16, 125, 14], [111, 18, 125, 16], [111, 41, 125, 39], [112, 8, 126, 6, "bubbled"], [112, 15, 126, 13], [112, 17, 126, 15], [112, 33, 126, 31], [113, 8, 127, 6, "skipBubbling"], [113, 20, 127, 18], [113, 22, 127, 20], [114, 6, 128, 4], [115, 4, 129, 2], [115, 5, 129, 3], [116, 4, 130, 2, "topPointerLeave"], [116, 19, 130, 17], [116, 21, 130, 19], [117, 6, 131, 4, "phasedRegistrationNames"], [117, 29, 131, 27], [117, 31, 131, 29], [118, 8, 132, 6, "captured"], [118, 16, 132, 14], [118, 18, 132, 16], [118, 41, 132, 39], [119, 8, 133, 6, "bubbled"], [119, 15, 133, 13], [119, 17, 133, 15], [119, 33, 133, 31], [120, 8, 134, 6, "skipBubbling"], [120, 20, 134, 18], [120, 22, 134, 20], [121, 6, 135, 4], [122, 4, 136, 2], [122, 5, 136, 3], [123, 4, 137, 2, "topPointerOver"], [123, 18, 137, 16], [123, 20, 137, 18], [124, 6, 138, 4, "phasedRegistrationNames"], [124, 29, 138, 27], [124, 31, 138, 29], [125, 8, 139, 6, "captured"], [125, 16, 139, 14], [125, 18, 139, 16], [125, 40, 139, 38], [126, 8, 140, 6, "bubbled"], [126, 15, 140, 13], [126, 17, 140, 15], [127, 6, 141, 4], [128, 4, 142, 2], [128, 5, 142, 3], [129, 4, 143, 2, "topPointerOut"], [129, 17, 143, 15], [129, 19, 143, 17], [130, 6, 144, 4, "phasedRegistrationNames"], [130, 29, 144, 27], [130, 31, 144, 29], [131, 8, 145, 6, "captured"], [131, 16, 145, 14], [131, 18, 145, 16], [131, 39, 145, 37], [132, 8, 146, 6, "bubbled"], [132, 15, 146, 13], [132, 17, 146, 15], [133, 6, 147, 4], [134, 4, 148, 2], [134, 5, 148, 3], [135, 4, 149, 2, "topGotPointerCapture"], [135, 24, 149, 22], [135, 26, 149, 24], [136, 6, 150, 4, "phasedRegistrationNames"], [136, 29, 150, 27], [136, 31, 150, 29], [137, 8, 151, 6, "captured"], [137, 16, 151, 14], [137, 18, 151, 16], [137, 46, 151, 44], [138, 8, 152, 6, "bubbled"], [138, 15, 152, 13], [138, 17, 152, 15], [139, 6, 153, 4], [140, 4, 154, 2], [140, 5, 154, 3], [141, 4, 155, 2, "topLostPointerCapture"], [141, 25, 155, 23], [141, 27, 155, 25], [142, 6, 156, 4, "phasedRegistrationNames"], [142, 29, 156, 27], [142, 31, 156, 29], [143, 8, 157, 6, "captured"], [143, 16, 157, 14], [143, 18, 157, 16], [143, 47, 157, 45], [144, 8, 158, 6, "bubbled"], [144, 15, 158, 13], [144, 17, 158, 15], [145, 6, 159, 4], [146, 4, 160, 2], [147, 2, 161, 0], [147, 3, 161, 1], [148, 2, 163, 0], [148, 6, 163, 6, "directEventTypes"], [148, 22, 163, 22], [148, 25, 163, 25], [149, 4, 164, 2, "topAccessibilityAction"], [149, 26, 164, 24], [149, 28, 164, 26], [150, 6, 165, 4, "registrationName"], [150, 22, 165, 20], [150, 24, 165, 22], [151, 4, 166, 2], [151, 5, 166, 3], [152, 4, 167, 2, "topAccessibilityTap"], [152, 23, 167, 21], [152, 25, 167, 23], [153, 6, 168, 4, "registrationName"], [153, 22, 168, 20], [153, 24, 168, 22], [154, 4, 169, 2], [154, 5, 169, 3], [155, 4, 170, 2, "topMagicTap"], [155, 15, 170, 13], [155, 17, 170, 15], [156, 6, 171, 4, "registrationName"], [156, 22, 171, 20], [156, 24, 171, 22], [157, 4, 172, 2], [157, 5, 172, 3], [158, 4, 173, 2, "topAccessibilityEscape"], [158, 26, 173, 24], [158, 28, 173, 26], [159, 6, 174, 4, "registrationName"], [159, 22, 174, 20], [159, 24, 174, 22], [160, 4, 175, 2], [160, 5, 175, 3], [161, 4, 176, 2, "topLayout"], [161, 13, 176, 11], [161, 15, 176, 13], [162, 6, 177, 4, "registrationName"], [162, 22, 177, 20], [162, 24, 177, 22], [163, 4, 178, 2], [163, 5, 178, 3], [164, 4, 179, 2, "onGestureHandlerEvent"], [164, 25, 179, 23], [164, 27, 179, 25], [164, 31, 179, 25, "DynamicallyInjectedByGestureHandler"], [164, 84, 179, 60], [164, 86, 179, 61], [165, 6, 180, 4, "registrationName"], [165, 22, 180, 20], [165, 24, 180, 22], [166, 4, 181, 2], [166, 5, 181, 3], [166, 6, 181, 4], [167, 4, 182, 2, "onGestureHandlerStateChange"], [167, 31, 182, 29], [167, 33, 182, 31], [167, 37, 182, 31, "DynamicallyInjectedByGestureHandler"], [167, 90, 182, 66], [167, 92, 182, 67], [168, 6, 183, 4, "registrationName"], [168, 22, 183, 20], [168, 24, 183, 22], [169, 4, 184, 2], [169, 5, 184, 3], [170, 2, 185, 0], [170, 3, 185, 1], [171, 2, 187, 0], [171, 6, 187, 6, "validAttributesForNonEventProps"], [171, 37, 187, 37], [171, 40, 187, 40], [172, 4, 189, 2, "accessible"], [172, 14, 189, 12], [172, 16, 189, 14], [172, 20, 189, 18], [173, 4, 190, 2, "accessibilityActions"], [173, 24, 190, 22], [173, 26, 190, 24], [173, 30, 190, 28], [174, 4, 191, 2, "accessibilityLabel"], [174, 22, 191, 20], [174, 24, 191, 22], [174, 28, 191, 26], [175, 4, 192, 2, "accessibilityHint"], [175, 21, 192, 19], [175, 23, 192, 21], [175, 27, 192, 25], [176, 4, 193, 2, "accessibilityLanguage"], [176, 25, 193, 23], [176, 27, 193, 25], [176, 31, 193, 29], [177, 4, 194, 2, "accessibilityValue"], [177, 22, 194, 20], [177, 24, 194, 22], [177, 28, 194, 26], [178, 4, 195, 2, "accessibilityViewIsModal"], [178, 28, 195, 26], [178, 30, 195, 28], [178, 34, 195, 32], [179, 4, 196, 2, "accessibilityElementsHidden"], [179, 31, 196, 29], [179, 33, 196, 31], [179, 37, 196, 35], [180, 4, 197, 2, "accessibilityIgnoresInvertColors"], [180, 36, 197, 34], [180, 38, 197, 36], [180, 42, 197, 40], [181, 4, 198, 2, "accessibilityShowsLargeContentViewer"], [181, 40, 198, 38], [181, 42, 198, 40], [181, 46, 198, 44], [182, 4, 199, 2, "accessibilityLargeContentTitle"], [182, 34, 199, 32], [182, 36, 199, 34], [182, 40, 199, 38], [183, 4, 200, 2, "testID"], [183, 10, 200, 8], [183, 12, 200, 10], [183, 16, 200, 14], [184, 4, 201, 2, "backgroundColor"], [184, 19, 201, 17], [184, 21, 201, 19], [185, 6, 201, 20, "process"], [185, 13, 201, 27], [185, 15, 201, 29, "require"], [185, 22, 201, 36], [185, 23, 201, 36, "_dependencyMap"], [185, 37, 201, 36], [185, 70, 201, 65], [185, 71, 201, 66], [185, 72, 201, 67, "default"], [186, 4, 201, 74], [186, 5, 201, 75], [187, 4, 202, 2, "backfaceVisibility"], [187, 22, 202, 20], [187, 24, 202, 22], [187, 28, 202, 26], [188, 4, 203, 2, "cursor"], [188, 10, 203, 8], [188, 12, 203, 10], [188, 16, 203, 14], [189, 4, 204, 2, "opacity"], [189, 11, 204, 9], [189, 13, 204, 11], [189, 17, 204, 15], [190, 4, 205, 2, "shadowColor"], [190, 15, 205, 13], [190, 17, 205, 15], [191, 6, 205, 16, "process"], [191, 13, 205, 23], [191, 15, 205, 25, "require"], [191, 22, 205, 32], [191, 23, 205, 32, "_dependencyMap"], [191, 37, 205, 32], [191, 70, 205, 61], [191, 71, 205, 62], [191, 72, 205, 63, "default"], [192, 4, 205, 70], [192, 5, 205, 71], [193, 4, 206, 2, "shadowOffset"], [193, 16, 206, 14], [193, 18, 206, 16], [194, 6, 206, 17, "diff"], [194, 10, 206, 21], [194, 12, 206, 23, "require"], [194, 19, 206, 30], [194, 20, 206, 30, "_dependencyMap"], [194, 34, 206, 30], [194, 72, 206, 64], [194, 73, 206, 65], [194, 74, 206, 66, "default"], [195, 4, 206, 73], [195, 5, 206, 74], [196, 4, 207, 2, "shadowOpacity"], [196, 17, 207, 15], [196, 19, 207, 17], [196, 23, 207, 21], [197, 4, 208, 2, "shadowRadius"], [197, 16, 208, 14], [197, 18, 208, 16], [197, 22, 208, 20], [198, 4, 209, 2, "needsOffscreenAlphaCompositing"], [198, 34, 209, 32], [198, 36, 209, 34], [198, 40, 209, 38], [199, 4, 210, 2, "overflow"], [199, 12, 210, 10], [199, 14, 210, 12], [199, 18, 210, 16], [200, 4, 211, 2, "shouldRasterizeIOS"], [200, 22, 211, 20], [200, 24, 211, 22], [200, 28, 211, 26], [201, 4, 212, 2, "transform"], [201, 13, 212, 11], [201, 15, 212, 13], [202, 6, 212, 14, "diff"], [202, 10, 212, 18], [202, 12, 212, 20, "require"], [202, 19, 212, 27], [202, 20, 212, 27, "_dependencyMap"], [202, 34, 212, 27], [202, 75, 212, 64], [202, 76, 212, 65], [202, 77, 212, 66, "default"], [203, 4, 212, 73], [203, 5, 212, 74], [204, 4, 213, 2, "transform<PERSON><PERSON>in"], [204, 19, 213, 17], [204, 21, 213, 19], [204, 25, 213, 23], [205, 4, 214, 2, "accessibilityRole"], [205, 21, 214, 19], [205, 23, 214, 21], [205, 27, 214, 25], [206, 4, 215, 2, "accessibilityState"], [206, 22, 215, 20], [206, 24, 215, 22], [206, 28, 215, 26], [207, 4, 216, 2, "nativeID"], [207, 12, 216, 10], [207, 14, 216, 12], [207, 18, 216, 16], [208, 4, 217, 2, "pointerEvents"], [208, 17, 217, 15], [208, 19, 217, 17], [208, 23, 217, 21], [209, 4, 218, 2, "removeClippedSubviews"], [209, 25, 218, 23], [209, 27, 218, 25], [209, 31, 218, 29], [210, 4, 219, 2, "role"], [210, 8, 219, 6], [210, 10, 219, 8], [210, 14, 219, 12], [211, 4, 220, 2, "borderRadius"], [211, 16, 220, 14], [211, 18, 220, 16], [211, 22, 220, 20], [212, 4, 221, 2, "borderColor"], [212, 15, 221, 13], [212, 17, 221, 15], [213, 6, 221, 16, "process"], [213, 13, 221, 23], [213, 15, 221, 25, "require"], [213, 22, 221, 32], [213, 23, 221, 32, "_dependencyMap"], [213, 37, 221, 32], [213, 70, 221, 61], [213, 71, 221, 62], [213, 72, 221, 63, "default"], [214, 4, 221, 70], [214, 5, 221, 71], [215, 4, 222, 2, "borderBlockColor"], [215, 20, 222, 18], [215, 22, 222, 20], [216, 6, 222, 21, "process"], [216, 13, 222, 28], [216, 15, 222, 30, "require"], [216, 22, 222, 37], [216, 23, 222, 37, "_dependencyMap"], [216, 37, 222, 37], [216, 70, 222, 66], [216, 71, 222, 67], [216, 72, 222, 68, "default"], [217, 4, 222, 75], [217, 5, 222, 76], [218, 4, 223, 2, "borderCurve"], [218, 15, 223, 13], [218, 17, 223, 15], [218, 21, 223, 19], [219, 4, 224, 2, "borderWidth"], [219, 15, 224, 13], [219, 17, 224, 15], [219, 21, 224, 19], [220, 4, 225, 2, "borderBlockWidth"], [220, 20, 225, 18], [220, 22, 225, 20], [220, 26, 225, 24], [221, 4, 226, 2, "borderStyle"], [221, 15, 226, 13], [221, 17, 226, 15], [221, 21, 226, 19], [222, 4, 227, 2, "hitSlop"], [222, 11, 227, 9], [222, 13, 227, 11], [223, 6, 227, 12, "diff"], [223, 10, 227, 16], [223, 12, 227, 18, "require"], [223, 19, 227, 25], [223, 20, 227, 25, "_dependencyMap"], [223, 34, 227, 25], [223, 73, 227, 60], [223, 74, 227, 61], [223, 75, 227, 62, "default"], [224, 4, 227, 69], [224, 5, 227, 70], [225, 4, 228, 2, "collapsable"], [225, 15, 228, 13], [225, 17, 228, 15], [225, 21, 228, 19], [226, 4, 229, 2, "collaps<PERSON><PERSON><PERSON><PERSON><PERSON>"], [226, 23, 229, 21], [226, 25, 229, 23], [226, 29, 229, 27], [227, 4, 230, 2, "filter"], [227, 10, 230, 8], [227, 12, 231, 4, "NativeReactNativeFeatureFlags"], [227, 50, 231, 33], [227, 54, 231, 37], [227, 58, 231, 41], [227, 62, 232, 4, "ReactNativeFeatureFlags"], [227, 85, 232, 27], [227, 86, 232, 28, "enableNativeCSSParsing"], [227, 108, 232, 50], [227, 109, 232, 51], [227, 110, 232, 52], [227, 113, 233, 8], [227, 117, 233, 12], [227, 120, 234, 8], [228, 6, 235, 10, "process"], [228, 13, 235, 17], [228, 15, 235, 19, "require"], [228, 22, 235, 26], [228, 23, 235, 26, "_dependencyMap"], [228, 37, 235, 26], [228, 71, 235, 56], [228, 72, 235, 57], [228, 73, 235, 58, "default"], [229, 4, 236, 8], [229, 5, 236, 9], [230, 4, 237, 2, "boxShadow"], [230, 13, 237, 11], [230, 15, 238, 4, "NativeReactNativeFeatureFlags"], [230, 53, 238, 33], [230, 57, 238, 37], [230, 61, 238, 41], [230, 65, 239, 4, "ReactNativeFeatureFlags"], [230, 88, 239, 27], [230, 89, 239, 28, "enableNativeCSSParsing"], [230, 111, 239, 50], [230, 112, 239, 51], [230, 113, 239, 52], [230, 116, 240, 8], [230, 120, 240, 12], [230, 123, 241, 8], [231, 6, 242, 10, "process"], [231, 13, 242, 17], [231, 15, 242, 19, "require"], [231, 22, 242, 26], [231, 23, 242, 26, "_dependencyMap"], [231, 37, 242, 26], [231, 75, 242, 59], [231, 76, 242, 60], [231, 77, 242, 61, "default"], [232, 4, 243, 8], [232, 5, 243, 9], [233, 4, 244, 2, "mixBlendMode"], [233, 16, 244, 14], [233, 18, 244, 16], [233, 22, 244, 20], [234, 4, 245, 2, "isolation"], [234, 13, 245, 11], [234, 15, 245, 13], [234, 19, 245, 17], [235, 4, 247, 2, "borderTopWidth"], [235, 18, 247, 16], [235, 20, 247, 18], [235, 24, 247, 22], [236, 4, 248, 2, "borderTopColor"], [236, 18, 248, 16], [236, 20, 248, 18], [237, 6, 248, 19, "process"], [237, 13, 248, 26], [237, 15, 248, 28, "require"], [237, 22, 248, 35], [237, 23, 248, 35, "_dependencyMap"], [237, 37, 248, 35], [237, 70, 248, 64], [237, 71, 248, 65], [237, 72, 248, 66, "default"], [238, 4, 248, 73], [238, 5, 248, 74], [239, 4, 249, 2, "borderRightWidth"], [239, 20, 249, 18], [239, 22, 249, 20], [239, 26, 249, 24], [240, 4, 250, 2, "borderRightColor"], [240, 20, 250, 18], [240, 22, 250, 20], [241, 6, 250, 21, "process"], [241, 13, 250, 28], [241, 15, 250, 30, "require"], [241, 22, 250, 37], [241, 23, 250, 37, "_dependencyMap"], [241, 37, 250, 37], [241, 70, 250, 66], [241, 71, 250, 67], [241, 72, 250, 68, "default"], [242, 4, 250, 75], [242, 5, 250, 76], [243, 4, 251, 2, "borderBottomWidth"], [243, 21, 251, 19], [243, 23, 251, 21], [243, 27, 251, 25], [244, 4, 252, 2, "borderBottomColor"], [244, 21, 252, 19], [244, 23, 252, 21], [245, 6, 252, 22, "process"], [245, 13, 252, 29], [245, 15, 252, 31, "require"], [245, 22, 252, 38], [245, 23, 252, 38, "_dependencyMap"], [245, 37, 252, 38], [245, 70, 252, 67], [245, 71, 252, 68], [245, 72, 252, 69, "default"], [246, 4, 252, 76], [246, 5, 252, 77], [247, 4, 253, 2, "borderLeftWidth"], [247, 19, 253, 17], [247, 21, 253, 19], [247, 25, 253, 23], [248, 4, 254, 2, "borderLeftColor"], [248, 19, 254, 17], [248, 21, 254, 19], [249, 6, 254, 20, "process"], [249, 13, 254, 27], [249, 15, 254, 29, "require"], [249, 22, 254, 36], [249, 23, 254, 36, "_dependencyMap"], [249, 37, 254, 36], [249, 70, 254, 65], [249, 71, 254, 66], [249, 72, 254, 67, "default"], [250, 4, 254, 74], [250, 5, 254, 75], [251, 4, 255, 2, "borderStartWidth"], [251, 20, 255, 18], [251, 22, 255, 20], [251, 26, 255, 24], [252, 4, 256, 2, "borderBlockStartWidth"], [252, 25, 256, 23], [252, 27, 256, 25], [252, 31, 256, 29], [253, 4, 257, 2, "borderStartColor"], [253, 20, 257, 18], [253, 22, 257, 20], [254, 6, 257, 21, "process"], [254, 13, 257, 28], [254, 15, 257, 30, "require"], [254, 22, 257, 37], [254, 23, 257, 37, "_dependencyMap"], [254, 37, 257, 37], [254, 70, 257, 66], [254, 71, 257, 67], [254, 72, 257, 68, "default"], [255, 4, 257, 75], [255, 5, 257, 76], [256, 4, 258, 2, "borderBlockStartColor"], [256, 25, 258, 23], [256, 27, 258, 25], [257, 6, 259, 4, "process"], [257, 13, 259, 11], [257, 15, 259, 13, "require"], [257, 22, 259, 20], [257, 23, 259, 20, "_dependencyMap"], [257, 37, 259, 20], [257, 70, 259, 49], [257, 71, 259, 50], [257, 72, 259, 51, "default"], [258, 4, 260, 2], [258, 5, 260, 3], [259, 4, 261, 2, "borderEndWidth"], [259, 18, 261, 16], [259, 20, 261, 18], [259, 24, 261, 22], [260, 4, 262, 2, "borderBlockEndWidth"], [260, 23, 262, 21], [260, 25, 262, 23], [260, 29, 262, 27], [261, 4, 263, 2, "borderEndColor"], [261, 18, 263, 16], [261, 20, 263, 18], [262, 6, 263, 19, "process"], [262, 13, 263, 26], [262, 15, 263, 28, "require"], [262, 22, 263, 35], [262, 23, 263, 35, "_dependencyMap"], [262, 37, 263, 35], [262, 70, 263, 64], [262, 71, 263, 65], [262, 72, 263, 66, "default"], [263, 4, 263, 73], [263, 5, 263, 74], [264, 4, 264, 2, "borderBlockEndColor"], [264, 23, 264, 21], [264, 25, 264, 23], [265, 6, 264, 24, "process"], [265, 13, 264, 31], [265, 15, 264, 33, "require"], [265, 22, 264, 40], [265, 23, 264, 40, "_dependencyMap"], [265, 37, 264, 40], [265, 70, 264, 69], [265, 71, 264, 70], [265, 72, 264, 71, "default"], [266, 4, 264, 78], [266, 5, 264, 79], [267, 4, 266, 2, "borderTopLeftRadius"], [267, 23, 266, 21], [267, 25, 266, 23], [267, 29, 266, 27], [268, 4, 267, 2, "borderTopRightRadius"], [268, 24, 267, 22], [268, 26, 267, 24], [268, 30, 267, 28], [269, 4, 268, 2, "borderTopStartRadius"], [269, 24, 268, 22], [269, 26, 268, 24], [269, 30, 268, 28], [270, 4, 269, 2, "borderTopEndRadius"], [270, 22, 269, 20], [270, 24, 269, 22], [270, 28, 269, 26], [271, 4, 270, 2, "borderBottomLeftRadius"], [271, 26, 270, 24], [271, 28, 270, 26], [271, 32, 270, 30], [272, 4, 271, 2, "borderBottomRightRadius"], [272, 27, 271, 25], [272, 29, 271, 27], [272, 33, 271, 31], [273, 4, 272, 2, "borderBottomStartRadius"], [273, 27, 272, 25], [273, 29, 272, 27], [273, 33, 272, 31], [274, 4, 273, 2, "borderBottomEndRadius"], [274, 25, 273, 23], [274, 27, 273, 25], [274, 31, 273, 29], [275, 4, 274, 2, "borderEndEndRadius"], [275, 22, 274, 20], [275, 24, 274, 22], [275, 28, 274, 26], [276, 4, 275, 2, "borderEndStartRadius"], [276, 24, 275, 22], [276, 26, 275, 24], [276, 30, 275, 28], [277, 4, 276, 2, "borderStartEndRadius"], [277, 24, 276, 22], [277, 26, 276, 24], [277, 30, 276, 28], [278, 4, 277, 2, "borderStartStartRadius"], [278, 26, 277, 24], [278, 28, 277, 26], [278, 32, 277, 30], [279, 4, 278, 2, "display"], [279, 11, 278, 9], [279, 13, 278, 11], [279, 17, 278, 15], [280, 4, 279, 2, "zIndex"], [280, 10, 279, 8], [280, 12, 279, 10], [280, 16, 279, 14], [281, 4, 282, 2, "top"], [281, 7, 282, 5], [281, 9, 282, 7], [281, 13, 282, 11], [282, 4, 283, 2, "right"], [282, 9, 283, 7], [282, 11, 283, 9], [282, 15, 283, 13], [283, 4, 284, 2, "start"], [283, 9, 284, 7], [283, 11, 284, 9], [283, 15, 284, 13], [284, 4, 285, 2, "end"], [284, 7, 285, 5], [284, 9, 285, 7], [284, 13, 285, 11], [285, 4, 286, 2, "bottom"], [285, 10, 286, 8], [285, 12, 286, 10], [285, 16, 286, 14], [286, 4, 287, 2, "left"], [286, 8, 287, 6], [286, 10, 287, 8], [286, 14, 287, 12], [287, 4, 289, 2, "inset"], [287, 9, 289, 7], [287, 11, 289, 9], [287, 15, 289, 13], [288, 4, 290, 2, "insetBlock"], [288, 14, 290, 12], [288, 16, 290, 14], [288, 20, 290, 18], [289, 4, 291, 2, "insetBlockEnd"], [289, 17, 291, 15], [289, 19, 291, 17], [289, 23, 291, 21], [290, 4, 292, 2, "insetBlockStart"], [290, 19, 292, 17], [290, 21, 292, 19], [290, 25, 292, 23], [291, 4, 293, 2, "insetInline"], [291, 15, 293, 13], [291, 17, 293, 15], [291, 21, 293, 19], [292, 4, 294, 2, "insetInlineEnd"], [292, 18, 294, 16], [292, 20, 294, 18], [292, 24, 294, 22], [293, 4, 295, 2, "insetInlineStart"], [293, 20, 295, 18], [293, 22, 295, 20], [293, 26, 295, 24], [294, 4, 297, 2, "width"], [294, 9, 297, 7], [294, 11, 297, 9], [294, 15, 297, 13], [295, 4, 298, 2, "height"], [295, 10, 298, 8], [295, 12, 298, 10], [295, 16, 298, 14], [296, 4, 300, 2, "min<PERSON><PERSON><PERSON>"], [296, 12, 300, 10], [296, 14, 300, 12], [296, 18, 300, 16], [297, 4, 301, 2, "max<PERSON><PERSON><PERSON>"], [297, 12, 301, 10], [297, 14, 301, 12], [297, 18, 301, 16], [298, 4, 302, 2, "minHeight"], [298, 13, 302, 11], [298, 15, 302, 13], [298, 19, 302, 17], [299, 4, 303, 2, "maxHeight"], [299, 13, 303, 11], [299, 15, 303, 13], [299, 19, 303, 17], [300, 4, 314, 2, "margin"], [300, 10, 314, 8], [300, 12, 314, 10], [300, 16, 314, 14], [301, 4, 315, 2, "marginBlock"], [301, 15, 315, 13], [301, 17, 315, 15], [301, 21, 315, 19], [302, 4, 316, 2, "marginBlockEnd"], [302, 18, 316, 16], [302, 20, 316, 18], [302, 24, 316, 22], [303, 4, 317, 2, "marginBlockStart"], [303, 20, 317, 18], [303, 22, 317, 20], [303, 26, 317, 24], [304, 4, 318, 2, "marginBottom"], [304, 16, 318, 14], [304, 18, 318, 16], [304, 22, 318, 20], [305, 4, 319, 2, "marginEnd"], [305, 13, 319, 11], [305, 15, 319, 13], [305, 19, 319, 17], [306, 4, 320, 2, "marginHorizontal"], [306, 20, 320, 18], [306, 22, 320, 20], [306, 26, 320, 24], [307, 4, 321, 2, "marginInline"], [307, 16, 321, 14], [307, 18, 321, 16], [307, 22, 321, 20], [308, 4, 322, 2, "marginInlineEnd"], [308, 19, 322, 17], [308, 21, 322, 19], [308, 25, 322, 23], [309, 4, 323, 2, "marginInlineStart"], [309, 21, 323, 19], [309, 23, 323, 21], [309, 27, 323, 25], [310, 4, 324, 2, "marginLeft"], [310, 14, 324, 12], [310, 16, 324, 14], [310, 20, 324, 18], [311, 4, 325, 2, "marginRight"], [311, 15, 325, 13], [311, 17, 325, 15], [311, 21, 325, 19], [312, 4, 326, 2, "marginStart"], [312, 15, 326, 13], [312, 17, 326, 15], [312, 21, 326, 19], [313, 4, 327, 2, "marginTop"], [313, 13, 327, 11], [313, 15, 327, 13], [313, 19, 327, 17], [314, 4, 328, 2, "marginVertical"], [314, 18, 328, 16], [314, 20, 328, 18], [314, 24, 328, 22], [315, 4, 330, 2, "padding"], [315, 11, 330, 9], [315, 13, 330, 11], [315, 17, 330, 15], [316, 4, 331, 2, "paddingBlock"], [316, 16, 331, 14], [316, 18, 331, 16], [316, 22, 331, 20], [317, 4, 332, 2, "paddingBlockEnd"], [317, 19, 332, 17], [317, 21, 332, 19], [317, 25, 332, 23], [318, 4, 333, 2, "paddingBlockStart"], [318, 21, 333, 19], [318, 23, 333, 21], [318, 27, 333, 25], [319, 4, 334, 2, "paddingBottom"], [319, 17, 334, 15], [319, 19, 334, 17], [319, 23, 334, 21], [320, 4, 335, 2, "paddingEnd"], [320, 14, 335, 12], [320, 16, 335, 14], [320, 20, 335, 18], [321, 4, 336, 2, "paddingHorizontal"], [321, 21, 336, 19], [321, 23, 336, 21], [321, 27, 336, 25], [322, 4, 337, 2, "paddingInline"], [322, 17, 337, 15], [322, 19, 337, 17], [322, 23, 337, 21], [323, 4, 338, 2, "paddingInlineEnd"], [323, 20, 338, 18], [323, 22, 338, 20], [323, 26, 338, 24], [324, 4, 339, 2, "paddingInlineStart"], [324, 22, 339, 20], [324, 24, 339, 22], [324, 28, 339, 26], [325, 4, 340, 2, "paddingLeft"], [325, 15, 340, 13], [325, 17, 340, 15], [325, 21, 340, 19], [326, 4, 341, 2, "paddingRight"], [326, 16, 341, 14], [326, 18, 341, 16], [326, 22, 341, 20], [327, 4, 342, 2, "paddingStart"], [327, 16, 342, 14], [327, 18, 342, 16], [327, 22, 342, 20], [328, 4, 343, 2, "paddingTop"], [328, 14, 343, 12], [328, 16, 343, 14], [328, 20, 343, 18], [329, 4, 344, 2, "paddingVertical"], [329, 19, 344, 17], [329, 21, 344, 19], [329, 25, 344, 23], [330, 4, 346, 2, "flex"], [330, 8, 346, 6], [330, 10, 346, 8], [330, 14, 346, 12], [331, 4, 347, 2, "flexGrow"], [331, 12, 347, 10], [331, 14, 347, 12], [331, 18, 347, 16], [332, 4, 348, 2, "rowGap"], [332, 10, 348, 8], [332, 12, 348, 10], [332, 16, 348, 14], [333, 4, 349, 2, "columnGap"], [333, 13, 349, 11], [333, 15, 349, 13], [333, 19, 349, 17], [334, 4, 350, 2, "gap"], [334, 7, 350, 5], [334, 9, 350, 7], [334, 13, 350, 11], [335, 4, 351, 2, "flexShrink"], [335, 14, 351, 12], [335, 16, 351, 14], [335, 20, 351, 18], [336, 4, 352, 2, "flexBasis"], [336, 13, 352, 11], [336, 15, 352, 13], [336, 19, 352, 17], [337, 4, 353, 2, "flexDirection"], [337, 17, 353, 15], [337, 19, 353, 17], [337, 23, 353, 21], [338, 4, 354, 2, "flexWrap"], [338, 12, 354, 10], [338, 14, 354, 12], [338, 18, 354, 16], [339, 4, 355, 2, "justifyContent"], [339, 18, 355, 16], [339, 20, 355, 18], [339, 24, 355, 22], [340, 4, 356, 2, "alignItems"], [340, 14, 356, 12], [340, 16, 356, 14], [340, 20, 356, 18], [341, 4, 357, 2, "alignSelf"], [341, 13, 357, 11], [341, 15, 357, 13], [341, 19, 357, 17], [342, 4, 358, 2, "align<PERSON><PERSON><PERSON>"], [342, 16, 358, 14], [342, 18, 358, 16], [342, 22, 358, 20], [343, 4, 359, 2, "position"], [343, 12, 359, 10], [343, 14, 359, 12], [343, 18, 359, 16], [344, 4, 360, 2, "aspectRatio"], [344, 15, 360, 13], [344, 17, 360, 15], [344, 21, 360, 19], [345, 4, 361, 2, "boxSizing"], [345, 13, 361, 11], [345, 15, 361, 13], [345, 19, 361, 17], [346, 4, 367, 2, "direction"], [346, 13, 367, 11], [346, 15, 367, 13], [346, 19, 367, 17], [347, 4, 369, 2, "style"], [347, 9, 369, 7], [347, 11, 369, 9, "ReactNativeStyleAttributes"], [348, 2, 370, 0], [348, 3, 370, 1], [349, 2, 373, 0], [349, 6, 373, 6, "validAttributesForEventProps"], [349, 34, 373, 34], [349, 37, 373, 37], [349, 41, 373, 37, "ConditionallyIgnoredEventHandlers"], [349, 92, 373, 70], [349, 94, 373, 71], [350, 4, 374, 2, "onLayout"], [350, 12, 374, 10], [350, 14, 374, 12], [350, 18, 374, 16], [351, 4, 375, 2, "onMagicTap"], [351, 14, 375, 12], [351, 16, 375, 14], [351, 20, 375, 18], [352, 4, 378, 2, "onAccessibilityAction"], [352, 25, 378, 23], [352, 27, 378, 25], [352, 31, 378, 29], [353, 4, 379, 2, "onAccessibilityEscape"], [353, 25, 379, 23], [353, 27, 379, 25], [353, 31, 379, 29], [354, 4, 380, 2, "onAccessibilityTap"], [354, 22, 380, 20], [354, 24, 380, 22], [354, 28, 380, 26], [355, 4, 383, 2, "onMoveShouldSetResponder"], [355, 28, 383, 26], [355, 30, 383, 28], [355, 34, 383, 32], [356, 4, 384, 2, "onMoveShouldSetResponderCapture"], [356, 35, 384, 33], [356, 37, 384, 35], [356, 41, 384, 39], [357, 4, 385, 2, "onStartShouldSetResponder"], [357, 29, 385, 27], [357, 31, 385, 29], [357, 35, 385, 33], [358, 4, 386, 2, "onStartShouldSetResponderCapture"], [358, 36, 386, 34], [358, 38, 386, 36], [358, 42, 386, 40], [359, 4, 387, 2, "onResponderGrant"], [359, 20, 387, 18], [359, 22, 387, 20], [359, 26, 387, 24], [360, 4, 388, 2, "onResponderReject"], [360, 21, 388, 19], [360, 23, 388, 21], [360, 27, 388, 25], [361, 4, 389, 2, "onResponderStart"], [361, 20, 389, 18], [361, 22, 389, 20], [361, 26, 389, 24], [362, 4, 390, 2, "onResponderEnd"], [362, 18, 390, 16], [362, 20, 390, 18], [362, 24, 390, 22], [363, 4, 391, 2, "onResponderRelease"], [363, 22, 391, 20], [363, 24, 391, 22], [363, 28, 391, 26], [364, 4, 392, 2, "onResponderMove"], [364, 19, 392, 17], [364, 21, 392, 19], [364, 25, 392, 23], [365, 4, 393, 2, "onResponderTerminate"], [365, 24, 393, 22], [365, 26, 393, 24], [365, 30, 393, 28], [366, 4, 394, 2, "onResponderTerminationRequest"], [366, 33, 394, 31], [366, 35, 394, 33], [366, 39, 394, 37], [367, 4, 395, 2, "onShouldBlockNativeResponder"], [367, 32, 395, 30], [367, 34, 395, 32], [367, 38, 395, 36], [368, 4, 398, 2, "onTouchStart"], [368, 16, 398, 14], [368, 18, 398, 16], [368, 22, 398, 20], [369, 4, 399, 2, "onTouchMove"], [369, 15, 399, 13], [369, 17, 399, 15], [369, 21, 399, 19], [370, 4, 400, 2, "onTouchEnd"], [370, 14, 400, 12], [370, 16, 400, 14], [370, 20, 400, 18], [371, 4, 401, 2, "onTouchCancel"], [371, 17, 401, 15], [371, 19, 401, 17], [371, 23, 401, 21], [372, 4, 404, 2, "onClick"], [372, 11, 404, 9], [372, 13, 404, 11], [372, 17, 404, 15], [373, 4, 405, 2, "onClickCapture"], [373, 18, 405, 16], [373, 20, 405, 18], [373, 24, 405, 22], [374, 4, 406, 2, "onPointerUp"], [374, 15, 406, 13], [374, 17, 406, 15], [374, 21, 406, 19], [375, 4, 407, 2, "onPointerDown"], [375, 17, 407, 15], [375, 19, 407, 17], [375, 23, 407, 21], [376, 4, 408, 2, "onPointerCancel"], [376, 19, 408, 17], [376, 21, 408, 19], [376, 25, 408, 23], [377, 4, 409, 2, "onPointerEnter"], [377, 18, 409, 16], [377, 20, 409, 18], [377, 24, 409, 22], [378, 4, 410, 2, "onPointerMove"], [378, 17, 410, 15], [378, 19, 410, 17], [378, 23, 410, 21], [379, 4, 411, 2, "onPointerLeave"], [379, 18, 411, 16], [379, 20, 411, 18], [379, 24, 411, 22], [380, 4, 412, 2, "onPointerOver"], [380, 17, 412, 15], [380, 19, 412, 17], [380, 23, 412, 21], [381, 4, 413, 2, "onPointerOut"], [381, 16, 413, 14], [381, 18, 413, 16], [381, 22, 413, 20], [382, 4, 414, 2, "onGotPointerCapture"], [382, 23, 414, 21], [382, 25, 414, 23], [382, 29, 414, 27], [383, 4, 415, 2, "onLostPointerCapture"], [383, 24, 415, 22], [383, 26, 415, 24], [384, 2, 416, 0], [384, 3, 416, 1], [384, 4, 416, 2], [385, 2, 422, 0], [385, 6, 422, 6, "PlatformBaseViewConfigIos"], [385, 31, 422, 61], [385, 34, 422, 64], [386, 4, 423, 2, "bubblingEventTypes"], [386, 22, 423, 20], [387, 4, 424, 2, "directEventTypes"], [387, 20, 424, 18], [388, 4, 425, 2, "validAttributes"], [388, 19, 425, 17], [388, 21, 425, 19], [389, 6, 426, 4], [389, 9, 426, 7, "validAttributesForNonEventProps"], [389, 40, 426, 38], [390, 6, 427, 4], [390, 9, 427, 7, "validAttributesForEventProps"], [391, 4, 428, 2], [392, 2, 429, 0], [392, 3, 429, 1], [393, 2, 429, 2], [393, 6, 429, 2, "_default"], [393, 14, 429, 2], [393, 17, 429, 2, "exports"], [393, 24, 429, 2], [393, 25, 429, 2, "default"], [393, 32, 429, 2], [393, 35, 431, 15, "PlatformBaseViewConfigIos"], [393, 60, 431, 40], [394, 0, 431, 40], [394, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}