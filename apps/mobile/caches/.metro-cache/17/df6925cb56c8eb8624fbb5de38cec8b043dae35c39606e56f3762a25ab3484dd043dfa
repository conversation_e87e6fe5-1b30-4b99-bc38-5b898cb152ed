{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./Sensor.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 33, "index": 48}}], "key": "sgp3ZMi7V93XedeToPLesiTiTJQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SensorContainer = void 0;\n  var _Sensor = _interopRequireDefault(require(_dependencyMap[1], \"./Sensor.js\"));\n  class SensorContainer {\n    nativeSensors = new Map();\n    getSensorId(sensorType, config) {\n      return sensorType * 100 + config.iosReferenceFrame * 10 + Number(config.adjustToInterfaceOrientation);\n    }\n    initializeSensor(sensorType, config) {\n      const sensorId = this.getSensorId(sensorType, config);\n      if (!this.nativeSensors.has(sensorId)) {\n        const newSensor = new _Sensor.default(sensorType, config);\n        this.nativeSensors.set(sensorId, newSensor);\n      }\n      const sensor = this.nativeSensors.get(sensorId);\n      return sensor.getSharedValue();\n    }\n    registerSensor(sensorType, config, handler) {\n      const sensorId = this.getSensorId(sensorType, config);\n      if (!this.nativeSensors.has(sensorId)) {\n        return -1;\n      }\n      const sensor = this.nativeSensors.get(sensorId);\n      if (sensor && sensor.isAvailable() && (sensor.isRunning() || sensor.register(handler))) {\n        sensor.listenersNumber++;\n        return sensorId;\n      }\n      return -1;\n    }\n    unregisterSensor(sensorId) {\n      if (this.nativeSensors.has(sensorId)) {\n        const sensor = this.nativeSensors.get(sensorId);\n        if (sensor && sensor.isRunning()) {\n          sensor.listenersNumber--;\n          if (sensor.listenersNumber === 0) {\n            sensor.unregister();\n          }\n        }\n      }\n    }\n  }\n  exports.SensorContainer = SensorContainer;\n});", "lineCount": 49, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "SensorContainer"], [8, 25, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_Sensor"], [9, 13, 3, 0], [9, 16, 3, 0, "_interopRequireDefault"], [9, 38, 3, 0], [9, 39, 3, 0, "require"], [9, 46, 3, 0], [9, 47, 3, 0, "_dependencyMap"], [9, 61, 3, 0], [10, 2, 4, 7], [10, 8, 4, 13, "SensorContainer"], [10, 23, 4, 28], [10, 24, 4, 29], [11, 4, 5, 2, "nativeSensors"], [11, 17, 5, 15], [11, 20, 5, 18], [11, 24, 5, 22, "Map"], [11, 27, 5, 25], [11, 28, 5, 26], [11, 29, 5, 27], [12, 4, 6, 2, "getSensorId"], [12, 15, 6, 13, "getSensorId"], [12, 16, 6, 14, "sensorType"], [12, 26, 6, 24], [12, 28, 6, 26, "config"], [12, 34, 6, 32], [12, 36, 6, 34], [13, 6, 7, 4], [13, 13, 7, 11, "sensorType"], [13, 23, 7, 21], [13, 26, 7, 24], [13, 29, 7, 27], [13, 32, 7, 30, "config"], [13, 38, 7, 36], [13, 39, 7, 37, "iosReferenceFrame"], [13, 56, 7, 54], [13, 59, 7, 57], [13, 61, 7, 59], [13, 64, 7, 62, "Number"], [13, 70, 7, 68], [13, 71, 7, 69, "config"], [13, 77, 7, 75], [13, 78, 7, 76, "adjustToInterfaceOrientation"], [13, 106, 7, 104], [13, 107, 7, 105], [14, 4, 8, 2], [15, 4, 9, 2, "initializeSensor"], [15, 20, 9, 18, "initializeSensor"], [15, 21, 9, 19, "sensorType"], [15, 31, 9, 29], [15, 33, 9, 31, "config"], [15, 39, 9, 37], [15, 41, 9, 39], [16, 6, 10, 4], [16, 12, 10, 10, "sensorId"], [16, 20, 10, 18], [16, 23, 10, 21], [16, 27, 10, 25], [16, 28, 10, 26, "getSensorId"], [16, 39, 10, 37], [16, 40, 10, 38, "sensorType"], [16, 50, 10, 48], [16, 52, 10, 50, "config"], [16, 58, 10, 56], [16, 59, 10, 57], [17, 6, 11, 4], [17, 10, 11, 8], [17, 11, 11, 9], [17, 15, 11, 13], [17, 16, 11, 14, "nativeSensors"], [17, 29, 11, 27], [17, 30, 11, 28, "has"], [17, 33, 11, 31], [17, 34, 11, 32, "sensorId"], [17, 42, 11, 40], [17, 43, 11, 41], [17, 45, 11, 43], [18, 8, 12, 6], [18, 14, 12, 12, "newSensor"], [18, 23, 12, 21], [18, 26, 12, 24], [18, 30, 12, 28, "Sensor"], [18, 45, 12, 34], [18, 46, 12, 35, "sensorType"], [18, 56, 12, 45], [18, 58, 12, 47, "config"], [18, 64, 12, 53], [18, 65, 12, 54], [19, 8, 13, 6], [19, 12, 13, 10], [19, 13, 13, 11, "nativeSensors"], [19, 26, 13, 24], [19, 27, 13, 25, "set"], [19, 30, 13, 28], [19, 31, 13, 29, "sensorId"], [19, 39, 13, 37], [19, 41, 13, 39, "newSensor"], [19, 50, 13, 48], [19, 51, 13, 49], [20, 6, 14, 4], [21, 6, 15, 4], [21, 12, 15, 10, "sensor"], [21, 18, 15, 16], [21, 21, 15, 19], [21, 25, 15, 23], [21, 26, 15, 24, "nativeSensors"], [21, 39, 15, 37], [21, 40, 15, 38, "get"], [21, 43, 15, 41], [21, 44, 15, 42, "sensorId"], [21, 52, 15, 50], [21, 53, 15, 51], [22, 6, 16, 4], [22, 13, 16, 11, "sensor"], [22, 19, 16, 17], [22, 20, 16, 18, "getSharedValue"], [22, 34, 16, 32], [22, 35, 16, 33], [22, 36, 16, 34], [23, 4, 17, 2], [24, 4, 18, 2, "registerSensor"], [24, 18, 18, 16, "registerSensor"], [24, 19, 18, 17, "sensorType"], [24, 29, 18, 27], [24, 31, 18, 29, "config"], [24, 37, 18, 35], [24, 39, 18, 37, "handler"], [24, 46, 18, 44], [24, 48, 18, 46], [25, 6, 19, 4], [25, 12, 19, 10, "sensorId"], [25, 20, 19, 18], [25, 23, 19, 21], [25, 27, 19, 25], [25, 28, 19, 26, "getSensorId"], [25, 39, 19, 37], [25, 40, 19, 38, "sensorType"], [25, 50, 19, 48], [25, 52, 19, 50, "config"], [25, 58, 19, 56], [25, 59, 19, 57], [26, 6, 20, 4], [26, 10, 20, 8], [26, 11, 20, 9], [26, 15, 20, 13], [26, 16, 20, 14, "nativeSensors"], [26, 29, 20, 27], [26, 30, 20, 28, "has"], [26, 33, 20, 31], [26, 34, 20, 32, "sensorId"], [26, 42, 20, 40], [26, 43, 20, 41], [26, 45, 20, 43], [27, 8, 21, 6], [27, 15, 21, 13], [27, 16, 21, 14], [27, 17, 21, 15], [28, 6, 22, 4], [29, 6, 23, 4], [29, 12, 23, 10, "sensor"], [29, 18, 23, 16], [29, 21, 23, 19], [29, 25, 23, 23], [29, 26, 23, 24, "nativeSensors"], [29, 39, 23, 37], [29, 40, 23, 38, "get"], [29, 43, 23, 41], [29, 44, 23, 42, "sensorId"], [29, 52, 23, 50], [29, 53, 23, 51], [30, 6, 24, 4], [30, 10, 24, 8, "sensor"], [30, 16, 24, 14], [30, 20, 24, 18, "sensor"], [30, 26, 24, 24], [30, 27, 24, 25, "isAvailable"], [30, 38, 24, 36], [30, 39, 24, 37], [30, 40, 24, 38], [30, 45, 24, 43, "sensor"], [30, 51, 24, 49], [30, 52, 24, 50, "isRunning"], [30, 61, 24, 59], [30, 62, 24, 60], [30, 63, 24, 61], [30, 67, 24, 65, "sensor"], [30, 73, 24, 71], [30, 74, 24, 72, "register"], [30, 82, 24, 80], [30, 83, 24, 81, "handler"], [30, 90, 24, 88], [30, 91, 24, 89], [30, 92, 24, 90], [30, 94, 24, 92], [31, 8, 25, 6, "sensor"], [31, 14, 25, 12], [31, 15, 25, 13, "listenersNumber"], [31, 30, 25, 28], [31, 32, 25, 30], [32, 8, 26, 6], [32, 15, 26, 13, "sensorId"], [32, 23, 26, 21], [33, 6, 27, 4], [34, 6, 28, 4], [34, 13, 28, 11], [34, 14, 28, 12], [34, 15, 28, 13], [35, 4, 29, 2], [36, 4, 30, 2, "unregisterSensor"], [36, 20, 30, 18, "unregisterSensor"], [36, 21, 30, 19, "sensorId"], [36, 29, 30, 27], [36, 31, 30, 29], [37, 6, 31, 4], [37, 10, 31, 8], [37, 14, 31, 12], [37, 15, 31, 13, "nativeSensors"], [37, 28, 31, 26], [37, 29, 31, 27, "has"], [37, 32, 31, 30], [37, 33, 31, 31, "sensorId"], [37, 41, 31, 39], [37, 42, 31, 40], [37, 44, 31, 42], [38, 8, 32, 6], [38, 14, 32, 12, "sensor"], [38, 20, 32, 18], [38, 23, 32, 21], [38, 27, 32, 25], [38, 28, 32, 26, "nativeSensors"], [38, 41, 32, 39], [38, 42, 32, 40, "get"], [38, 45, 32, 43], [38, 46, 32, 44, "sensorId"], [38, 54, 32, 52], [38, 55, 32, 53], [39, 8, 33, 6], [39, 12, 33, 10, "sensor"], [39, 18, 33, 16], [39, 22, 33, 20, "sensor"], [39, 28, 33, 26], [39, 29, 33, 27, "isRunning"], [39, 38, 33, 36], [39, 39, 33, 37], [39, 40, 33, 38], [39, 42, 33, 40], [40, 10, 34, 8, "sensor"], [40, 16, 34, 14], [40, 17, 34, 15, "listenersNumber"], [40, 32, 34, 30], [40, 34, 34, 32], [41, 10, 35, 8], [41, 14, 35, 12, "sensor"], [41, 20, 35, 18], [41, 21, 35, 19, "listenersNumber"], [41, 36, 35, 34], [41, 41, 35, 39], [41, 42, 35, 40], [41, 44, 35, 42], [42, 12, 36, 10, "sensor"], [42, 18, 36, 16], [42, 19, 36, 17, "unregister"], [42, 29, 36, 27], [42, 30, 36, 28], [42, 31, 36, 29], [43, 10, 37, 8], [44, 8, 38, 6], [45, 6, 39, 4], [46, 4, 40, 2], [47, 2, 41, 0], [48, 2, 41, 1, "exports"], [48, 9, 41, 1], [48, 10, 41, 1, "SensorContainer"], [48, 25, 41, 1], [48, 28, 41, 1, "SensorContainer"], [48, 43, 41, 1], [49, 0, 41, 1], [49, 3]], "functionMap": {"names": ["<global>", "SensorContainer", "getSensorId", "initializeSensor", "registerSensor", "unregisterSensor"], "mappings": "AAA;OCG;ECE;GDE;EEC;GFQ;EGC;GHW;EIC;GJU;CDC"}}, "type": "js/module"}]}