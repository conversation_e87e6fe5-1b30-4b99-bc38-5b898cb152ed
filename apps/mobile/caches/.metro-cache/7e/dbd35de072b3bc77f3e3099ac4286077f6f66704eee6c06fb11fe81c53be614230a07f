{"dependencies": [{"name": "../../ReanimatedModule/js-reanimated/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 79, "index": 94}}], "key": "8qYfXx8WDVmSHeEzcYjMpSHQIRY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.makeElementVisible = makeElementVisible;\n  exports.setElementPosition = setElementPosition;\n  exports.snapshots = void 0;\n  var _index = require(_dependencyMap[0], \"../../ReanimatedModule/js-reanimated/index.js\");\n  const snapshots = exports.snapshots = new WeakMap();\n  function makeElementVisible(element, delay) {\n    if (delay === 0) {\n      (0, _index._updatePropsJS)({\n        visibility: 'initial'\n      }, element);\n    } else {\n      setTimeout(() => {\n        (0, _index._updatePropsJS)({\n          visibility: 'initial'\n        }, element);\n      }, delay * 1000);\n    }\n  }\n  function fixElementPosition(element, parent, snapshot) {\n    const parentRect = parent.getBoundingClientRect();\n    const parentBorderTopValue = parseInt(getComputedStyle(parent).borderTopWidth);\n    const parentBorderLeftValue = parseInt(getComputedStyle(parent).borderLeftWidth);\n    const dummyRect = element.getBoundingClientRect();\n    // getBoundingClientRect returns DOMRect with position of the element with respect to document body.\n    // However, using position `absolute` doesn't guarantee, that the dummy will be placed relative to body element.\n    // The trick below allows us to once again get position relative to body, by comparing snapshot with new position of the dummy.\n    if (dummyRect.top !== snapshot.top) {\n      element.style.top = `${snapshot.top - parentRect.top - parentBorderTopValue}px`;\n    }\n    if (dummyRect.left !== snapshot.left) {\n      element.style.left = `${snapshot.left - parentRect.left - parentBorderLeftValue}px`;\n    }\n  }\n  function setElementPosition(element, snapshot) {\n    element.style.transform = '';\n    element.style.position = 'absolute';\n    element.style.top = `${snapshot.top}px`;\n    element.style.left = `${snapshot.left}px`;\n    element.style.width = `${snapshot.width}px`;\n    element.style.height = `${snapshot.height}px`;\n    element.style.margin = '0px'; // tmpElement has absolute position, so margin is not necessary\n\n    if (element.parentElement) {\n      fixElementPosition(element, element.parentElement, snapshot);\n    }\n  }\n});", "lineCount": 53, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "makeElementVisible"], [7, 28, 1, 13], [7, 31, 1, 13, "makeElementVisible"], [7, 49, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "setElementPosition"], [8, 28, 1, 13], [8, 31, 1, 13, "setElementPosition"], [8, 49, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "snapshots"], [9, 19, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_index"], [10, 12, 3, 0], [10, 15, 3, 0, "require"], [10, 22, 3, 0], [10, 23, 3, 0, "_dependencyMap"], [10, 37, 3, 0], [11, 2, 4, 7], [11, 8, 4, 13, "snapshots"], [11, 17, 4, 22], [11, 20, 4, 22, "exports"], [11, 27, 4, 22], [11, 28, 4, 22, "snapshots"], [11, 37, 4, 22], [11, 40, 4, 25], [11, 44, 4, 29, "WeakMap"], [11, 51, 4, 36], [11, 52, 4, 37], [11, 53, 4, 38], [12, 2, 5, 7], [12, 11, 5, 16, "makeElementVisible"], [12, 29, 5, 34, "makeElementVisible"], [12, 30, 5, 35, "element"], [12, 37, 5, 42], [12, 39, 5, 44, "delay"], [12, 44, 5, 49], [12, 46, 5, 51], [13, 4, 6, 2], [13, 8, 6, 6, "delay"], [13, 13, 6, 11], [13, 18, 6, 16], [13, 19, 6, 17], [13, 21, 6, 19], [14, 6, 7, 4], [14, 10, 7, 4, "_updatePropsJS"], [14, 31, 7, 18], [14, 33, 7, 19], [15, 8, 8, 6, "visibility"], [15, 18, 8, 16], [15, 20, 8, 18], [16, 6, 9, 4], [16, 7, 9, 5], [16, 9, 9, 7, "element"], [16, 16, 9, 14], [16, 17, 9, 15], [17, 4, 10, 2], [17, 5, 10, 3], [17, 11, 10, 9], [18, 6, 11, 4, "setTimeout"], [18, 16, 11, 14], [18, 17, 11, 15], [18, 23, 11, 21], [19, 8, 12, 6], [19, 12, 12, 6, "_updatePropsJS"], [19, 33, 12, 20], [19, 35, 12, 21], [20, 10, 13, 8, "visibility"], [20, 20, 13, 18], [20, 22, 13, 20], [21, 8, 14, 6], [21, 9, 14, 7], [21, 11, 14, 9, "element"], [21, 18, 14, 16], [21, 19, 14, 17], [22, 6, 15, 4], [22, 7, 15, 5], [22, 9, 15, 7, "delay"], [22, 14, 15, 12], [22, 17, 15, 15], [22, 21, 15, 19], [22, 22, 15, 20], [23, 4, 16, 2], [24, 2, 17, 0], [25, 2, 18, 0], [25, 11, 18, 9, "fixElementPosition"], [25, 29, 18, 27, "fixElementPosition"], [25, 30, 18, 28, "element"], [25, 37, 18, 35], [25, 39, 18, 37, "parent"], [25, 45, 18, 43], [25, 47, 18, 45, "snapshot"], [25, 55, 18, 53], [25, 57, 18, 55], [26, 4, 19, 2], [26, 10, 19, 8, "parentRect"], [26, 20, 19, 18], [26, 23, 19, 21, "parent"], [26, 29, 19, 27], [26, 30, 19, 28, "getBoundingClientRect"], [26, 51, 19, 49], [26, 52, 19, 50], [26, 53, 19, 51], [27, 4, 20, 2], [27, 10, 20, 8, "parentBorderTopValue"], [27, 30, 20, 28], [27, 33, 20, 31, "parseInt"], [27, 41, 20, 39], [27, 42, 20, 40, "getComputedStyle"], [27, 58, 20, 56], [27, 59, 20, 57, "parent"], [27, 65, 20, 63], [27, 66, 20, 64], [27, 67, 20, 65, "borderTopWidth"], [27, 81, 20, 79], [27, 82, 20, 80], [28, 4, 21, 2], [28, 10, 21, 8, "parentBorderLeftValue"], [28, 31, 21, 29], [28, 34, 21, 32, "parseInt"], [28, 42, 21, 40], [28, 43, 21, 41, "getComputedStyle"], [28, 59, 21, 57], [28, 60, 21, 58, "parent"], [28, 66, 21, 64], [28, 67, 21, 65], [28, 68, 21, 66, "borderLeftWidth"], [28, 83, 21, 81], [28, 84, 21, 82], [29, 4, 22, 2], [29, 10, 22, 8, "dummyRect"], [29, 19, 22, 17], [29, 22, 22, 20, "element"], [29, 29, 22, 27], [29, 30, 22, 28, "getBoundingClientRect"], [29, 51, 22, 49], [29, 52, 22, 50], [29, 53, 22, 51], [30, 4, 23, 2], [31, 4, 24, 2], [32, 4, 25, 2], [33, 4, 26, 2], [33, 8, 26, 6, "dummyRect"], [33, 17, 26, 15], [33, 18, 26, 16, "top"], [33, 21, 26, 19], [33, 26, 26, 24, "snapshot"], [33, 34, 26, 32], [33, 35, 26, 33, "top"], [33, 38, 26, 36], [33, 40, 26, 38], [34, 6, 27, 4, "element"], [34, 13, 27, 11], [34, 14, 27, 12, "style"], [34, 19, 27, 17], [34, 20, 27, 18, "top"], [34, 23, 27, 21], [34, 26, 27, 24], [34, 29, 27, 27, "snapshot"], [34, 37, 27, 35], [34, 38, 27, 36, "top"], [34, 41, 27, 39], [34, 44, 27, 42, "parentRect"], [34, 54, 27, 52], [34, 55, 27, 53, "top"], [34, 58, 27, 56], [34, 61, 27, 59, "parentBorderTopValue"], [34, 81, 27, 79], [34, 85, 27, 83], [35, 4, 28, 2], [36, 4, 29, 2], [36, 8, 29, 6, "dummyRect"], [36, 17, 29, 15], [36, 18, 29, 16, "left"], [36, 22, 29, 20], [36, 27, 29, 25, "snapshot"], [36, 35, 29, 33], [36, 36, 29, 34, "left"], [36, 40, 29, 38], [36, 42, 29, 40], [37, 6, 30, 4, "element"], [37, 13, 30, 11], [37, 14, 30, 12, "style"], [37, 19, 30, 17], [37, 20, 30, 18, "left"], [37, 24, 30, 22], [37, 27, 30, 25], [37, 30, 30, 28, "snapshot"], [37, 38, 30, 36], [37, 39, 30, 37, "left"], [37, 43, 30, 41], [37, 46, 30, 44, "parentRect"], [37, 56, 30, 54], [37, 57, 30, 55, "left"], [37, 61, 30, 59], [37, 64, 30, 62, "parentBorderLeftValue"], [37, 85, 30, 83], [37, 89, 30, 87], [38, 4, 31, 2], [39, 2, 32, 0], [40, 2, 33, 7], [40, 11, 33, 16, "setElementPosition"], [40, 29, 33, 34, "setElementPosition"], [40, 30, 33, 35, "element"], [40, 37, 33, 42], [40, 39, 33, 44, "snapshot"], [40, 47, 33, 52], [40, 49, 33, 54], [41, 4, 34, 2, "element"], [41, 11, 34, 9], [41, 12, 34, 10, "style"], [41, 17, 34, 15], [41, 18, 34, 16, "transform"], [41, 27, 34, 25], [41, 30, 34, 28], [41, 32, 34, 30], [42, 4, 35, 2, "element"], [42, 11, 35, 9], [42, 12, 35, 10, "style"], [42, 17, 35, 15], [42, 18, 35, 16, "position"], [42, 26, 35, 24], [42, 29, 35, 27], [42, 39, 35, 37], [43, 4, 36, 2, "element"], [43, 11, 36, 9], [43, 12, 36, 10, "style"], [43, 17, 36, 15], [43, 18, 36, 16, "top"], [43, 21, 36, 19], [43, 24, 36, 22], [43, 27, 36, 25, "snapshot"], [43, 35, 36, 33], [43, 36, 36, 34, "top"], [43, 39, 36, 37], [43, 43, 36, 41], [44, 4, 37, 2, "element"], [44, 11, 37, 9], [44, 12, 37, 10, "style"], [44, 17, 37, 15], [44, 18, 37, 16, "left"], [44, 22, 37, 20], [44, 25, 37, 23], [44, 28, 37, 26, "snapshot"], [44, 36, 37, 34], [44, 37, 37, 35, "left"], [44, 41, 37, 39], [44, 45, 37, 43], [45, 4, 38, 2, "element"], [45, 11, 38, 9], [45, 12, 38, 10, "style"], [45, 17, 38, 15], [45, 18, 38, 16, "width"], [45, 23, 38, 21], [45, 26, 38, 24], [45, 29, 38, 27, "snapshot"], [45, 37, 38, 35], [45, 38, 38, 36, "width"], [45, 43, 38, 41], [45, 47, 38, 45], [46, 4, 39, 2, "element"], [46, 11, 39, 9], [46, 12, 39, 10, "style"], [46, 17, 39, 15], [46, 18, 39, 16, "height"], [46, 24, 39, 22], [46, 27, 39, 25], [46, 30, 39, 28, "snapshot"], [46, 38, 39, 36], [46, 39, 39, 37, "height"], [46, 45, 39, 43], [46, 49, 39, 47], [47, 4, 40, 2, "element"], [47, 11, 40, 9], [47, 12, 40, 10, "style"], [47, 17, 40, 15], [47, 18, 40, 16, "margin"], [47, 24, 40, 22], [47, 27, 40, 25], [47, 32, 40, 30], [47, 33, 40, 31], [47, 34, 40, 32], [49, 4, 42, 2], [49, 8, 42, 6, "element"], [49, 15, 42, 13], [49, 16, 42, 14, "parentElement"], [49, 29, 42, 27], [49, 31, 42, 29], [50, 6, 43, 4, "fixElementPosition"], [50, 24, 43, 22], [50, 25, 43, 23, "element"], [50, 32, 43, 30], [50, 34, 43, 32, "element"], [50, 41, 43, 39], [50, 42, 43, 40, "parentElement"], [50, 55, 43, 53], [50, 57, 43, 55, "snapshot"], [50, 65, 43, 63], [50, 66, 43, 64], [51, 4, 44, 2], [52, 2, 45, 0], [53, 0, 45, 1], [53, 3]], "functionMap": {"names": ["<global>", "makeElementVisible", "setTimeout$argument_0", "fixElementPosition", "setElementPosition"], "mappings": "AAA;OCI;eCM;KDI;CDE;AGC;CHc;OIC;CJY"}}, "type": "js/module"}]}