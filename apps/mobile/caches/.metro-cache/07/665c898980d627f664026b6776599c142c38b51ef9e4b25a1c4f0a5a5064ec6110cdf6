{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 92}}], "key": "jzP+LUi0+8ZCeIUw7GN35c9PLT4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 26, "column": 32}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 26, "column": 32}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../../../../Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RCTInputAccessoryView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RCTInputAccessoryView\",\n    validAttributes: {\n      backgroundColor: {\n        process: require(_dependencyMap[3], \"react-native/Libraries/StyleSheet/processColor\").default\n      }\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 19, "map": [[7, 2, 15, 0], [7, 6, 15, 0, "_codegenNativeComponent"], [7, 29, 15, 0], [7, 32, 15, 0, "_interopRequireDefault"], [7, 54, 15, 0], [7, 55, 15, 0, "require"], [7, 62, 15, 0], [7, 63, 15, 0, "_dependencyMap"], [7, 77, 15, 0], [8, 2, 22, 0], [8, 6, 22, 0, "NativeComponentRegistry"], [8, 29, 26, 32], [8, 32, 22, 0, "require"], [8, 39, 26, 32], [8, 40, 26, 32, "_dependencyMap"], [8, 54, 26, 32], [8, 123, 26, 31], [8, 124, 26, 32], [9, 2, 22, 0], [9, 6, 22, 0, "nativeComponentName"], [9, 25, 26, 32], [9, 28, 22, 0], [9, 51, 26, 32], [10, 2, 22, 0], [10, 6, 22, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 26, 32], [10, 31, 26, 32, "exports"], [10, 38, 26, 32], [10, 39, 26, 32, "__INTERNAL_VIEW_CONFIG"], [10, 61, 26, 32], [10, 64, 22, 0], [11, 4, 22, 0, "uiViewClassName"], [11, 19, 26, 32], [11, 21, 22, 0], [11, 44, 26, 32], [12, 4, 22, 0, "validAttributes"], [12, 19, 26, 32], [12, 21, 22, 0], [13, 6, 22, 0, "backgroundColor"], [13, 21, 26, 32], [13, 23, 22, 0], [14, 8, 22, 0, "process"], [14, 15, 26, 32], [14, 17, 22, 0, "require"], [14, 24, 26, 32], [14, 25, 26, 32, "_dependencyMap"], [14, 39, 26, 32], [14, 92, 26, 31], [14, 93, 26, 32], [14, 94, 22, 0, "default"], [15, 6, 26, 31], [16, 4, 26, 31], [17, 2, 26, 31], [17, 3, 26, 32], [18, 2, 26, 32], [18, 6, 26, 32, "_default"], [18, 14, 26, 32], [18, 17, 26, 32, "exports"], [18, 24, 26, 32], [18, 25, 26, 32, "default"], [18, 32, 26, 32], [18, 35, 22, 0, "NativeComponentRegistry"], [18, 58, 26, 32], [18, 59, 22, 0, "get"], [18, 62, 26, 32], [18, 63, 22, 0, "nativeComponentName"], [18, 82, 26, 32], [18, 84, 22, 0], [18, 90, 22, 0, "__INTERNAL_VIEW_CONFIG"], [18, 112, 26, 31], [18, 113, 26, 32], [19, 0, 26, 32], [19, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}