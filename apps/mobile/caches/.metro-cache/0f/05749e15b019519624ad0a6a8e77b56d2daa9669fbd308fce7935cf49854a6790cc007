{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.matchDynamicName = matchDynamicName;\n  exports.testNotFound = testNotFound;\n  exports.matchGroupName = matchGroupName;\n  exports.matchLastGroupName = matchLastGroupName;\n  exports.matchArrayGroupName = matchArrayGroupName;\n  exports.getNameFromFilePath = getNameFromFilePath;\n  exports.getContextKey = getContextKey;\n  exports.removeSupportedExtensions = removeSupportedExtensions;\n  exports.removeFileSystemExtensions = removeFileSystemExtensions;\n  exports.removeFileSystemDots = removeFileSystemDots;\n  exports.stripGroupSegmentsFromPath = stripGroupSegmentsFromPath;\n  exports.stripInvisibleSegmentsFromPath = stripInvisibleSegmentsFromPath;\n  exports.isTypedRoute = isTypedRoute;\n  /** Match `[page]` -> `page` or `[...group]` -> `...group` */\n  const dynamicNameRe = /^\\[([^[\\]]+?)\\]$/;\n  /** Match `[page]` -> `page` */\n  function matchDynamicName(name) {\n    const paramName = name.match(dynamicNameRe)?.[1];\n    if (paramName == null) {\n      return undefined;\n    } else if (paramName.startsWith('...')) {\n      return {\n        name: paramName.slice(3),\n        deep: true\n      };\n    } else {\n      return {\n        name: paramName,\n        deep: false\n      };\n    }\n  }\n  /** Test `/` -> `page` */\n  function testNotFound(name) {\n    return /\\+not-found$/.test(name);\n  }\n  /** Match `(page)` -> `page` */\n  function matchGroupName(name) {\n    return name.match(/^(?:[^\\\\()])*?\\(([^\\\\/]+)\\)/)?.[1];\n  }\n  /** Match `(app)/(page)` -> `page` */\n  function matchLastGroupName(name) {\n    return name.match(/.*(?:\\/|^)\\(([^\\\\/]+)\\)[^\\s]*$/)?.[1];\n  }\n  /** Match the first array group name `(a,b,c)/(d,c)` -> `'a,b,c'` */\n  function matchArrayGroupName(name) {\n    return name.match(/(?:[^\\\\()])*?\\(([^\\\\/]+,[^\\\\/]+)\\)/)?.[1];\n  }\n  function getNameFromFilePath(name) {\n    return removeSupportedExtensions(removeFileSystemDots(name));\n  }\n  function getContextKey(name) {\n    // The root path is `` (empty string) so always prepend `/` to ensure\n    // there is some value.\n    const normal = '/' + getNameFromFilePath(name);\n    if (!normal.endsWith('_layout')) {\n      return normal;\n    }\n    return normal.replace(/\\/?_layout$/, '');\n  }\n  /** Remove `.js`, `.ts`, `.jsx`, `.tsx`, and the +api suffix */\n  function removeSupportedExtensions(name) {\n    return name.replace(/(\\+api)?\\.[jt]sx?$/g, '');\n  }\n  /** Remove `.js`, `.ts`, `.jsx`, `.tsx` */\n  function removeFileSystemExtensions(name) {\n    return name.replace(/\\.[jt]sx?$/g, '');\n  }\n  // Remove any amount of `./` and `../` from the start of the string\n  function removeFileSystemDots(filePath) {\n    return filePath.replace(/^(?:\\.\\.?\\/)+/g, '');\n  }\n  function stripGroupSegmentsFromPath(path) {\n    return path.split('/').reduce((acc, v) => {\n      if (matchGroupName(v) == null) {\n        acc.push(v);\n      }\n      return acc;\n    }, []).join('/');\n  }\n  function stripInvisibleSegmentsFromPath(path) {\n    return stripGroupSegmentsFromPath(path).replace(/\\/?index$/, '');\n  }\n  /**\n   * Match:\n   *  - _layout files, +html, +not-found, string+api, etc\n   *  - Routes can still use `+`, but it cannot be in the last segment.\n   */\n  function isTypedRoute(name) {\n    return !name.startsWith('+') && name.match(/(_layout|[^/]*?\\+[^/]*?)\\.[tj]sx?$/) === null;\n  }\n});", "lineCount": 98, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "matchDynamicName"], [7, 26, 3, 24], [7, 29, 3, 27, "matchDynamicName"], [7, 45, 3, 43], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "testNotFound"], [8, 22, 4, 20], [8, 25, 4, 23, "testNotFound"], [8, 37, 4, 35], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "matchGroupName"], [9, 24, 5, 22], [9, 27, 5, 25, "matchGroupName"], [9, 41, 5, 39], [10, 2, 6, 0, "exports"], [10, 9, 6, 7], [10, 10, 6, 8, "matchLastGroupName"], [10, 28, 6, 26], [10, 31, 6, 29, "matchLastGroupName"], [10, 49, 6, 47], [11, 2, 7, 0, "exports"], [11, 9, 7, 7], [11, 10, 7, 8, "matchArrayGroupName"], [11, 29, 7, 27], [11, 32, 7, 30, "matchArrayGroupName"], [11, 51, 7, 49], [12, 2, 8, 0, "exports"], [12, 9, 8, 7], [12, 10, 8, 8, "getNameFromFilePath"], [12, 29, 8, 27], [12, 32, 8, 30, "getNameFromFilePath"], [12, 51, 8, 49], [13, 2, 9, 0, "exports"], [13, 9, 9, 7], [13, 10, 9, 8, "getContextKey"], [13, 23, 9, 21], [13, 26, 9, 24, "getContextKey"], [13, 39, 9, 37], [14, 2, 10, 0, "exports"], [14, 9, 10, 7], [14, 10, 10, 8, "removeSupportedExtensions"], [14, 35, 10, 33], [14, 38, 10, 36, "removeSupportedExtensions"], [14, 63, 10, 61], [15, 2, 11, 0, "exports"], [15, 9, 11, 7], [15, 10, 11, 8, "removeFileSystemExtensions"], [15, 36, 11, 34], [15, 39, 11, 37, "removeFileSystemExtensions"], [15, 65, 11, 63], [16, 2, 12, 0, "exports"], [16, 9, 12, 7], [16, 10, 12, 8, "removeFileSystemDots"], [16, 30, 12, 28], [16, 33, 12, 31, "removeFileSystemDots"], [16, 53, 12, 51], [17, 2, 13, 0, "exports"], [17, 9, 13, 7], [17, 10, 13, 8, "stripGroupSegmentsFromPath"], [17, 36, 13, 34], [17, 39, 13, 37, "stripGroupSegmentsFromPath"], [17, 65, 13, 63], [18, 2, 14, 0, "exports"], [18, 9, 14, 7], [18, 10, 14, 8, "stripInvisibleSegmentsFromPath"], [18, 40, 14, 38], [18, 43, 14, 41, "stripInvisibleSegmentsFromPath"], [18, 73, 14, 71], [19, 2, 15, 0, "exports"], [19, 9, 15, 7], [19, 10, 15, 8, "isTypedRoute"], [19, 22, 15, 20], [19, 25, 15, 23, "isTypedRoute"], [19, 37, 15, 35], [20, 2, 16, 0], [21, 2, 17, 0], [21, 8, 17, 6, "dynamicNameRe"], [21, 21, 17, 19], [21, 24, 17, 22], [21, 42, 17, 40], [22, 2, 18, 0], [23, 2, 19, 0], [23, 11, 19, 9, "matchDynamicName"], [23, 27, 19, 25, "matchDynamicName"], [23, 28, 19, 26, "name"], [23, 32, 19, 30], [23, 34, 19, 32], [24, 4, 20, 4], [24, 10, 20, 10, "paramName"], [24, 19, 20, 19], [24, 22, 20, 22, "name"], [24, 26, 20, 26], [24, 27, 20, 27, "match"], [24, 32, 20, 32], [24, 33, 20, 33, "dynamicNameRe"], [24, 46, 20, 46], [24, 47, 20, 47], [24, 50, 20, 50], [24, 51, 20, 51], [24, 52, 20, 52], [25, 4, 21, 4], [25, 8, 21, 8, "paramName"], [25, 17, 21, 17], [25, 21, 21, 21], [25, 25, 21, 25], [25, 27, 21, 27], [26, 6, 22, 8], [26, 13, 22, 15, "undefined"], [26, 22, 22, 24], [27, 4, 23, 4], [27, 5, 23, 5], [27, 11, 24, 9], [27, 15, 24, 13, "paramName"], [27, 24, 24, 22], [27, 25, 24, 23, "startsWith"], [27, 35, 24, 33], [27, 36, 24, 34], [27, 41, 24, 39], [27, 42, 24, 40], [27, 44, 24, 42], [28, 6, 25, 8], [28, 13, 25, 15], [29, 8, 25, 17, "name"], [29, 12, 25, 21], [29, 14, 25, 23, "paramName"], [29, 23, 25, 32], [29, 24, 25, 33, "slice"], [29, 29, 25, 38], [29, 30, 25, 39], [29, 31, 25, 40], [29, 32, 25, 41], [30, 8, 25, 43, "deep"], [30, 12, 25, 47], [30, 14, 25, 49], [31, 6, 25, 54], [31, 7, 25, 55], [32, 4, 26, 4], [32, 5, 26, 5], [32, 11, 27, 9], [33, 6, 28, 8], [33, 13, 28, 15], [34, 8, 28, 17, "name"], [34, 12, 28, 21], [34, 14, 28, 23, "paramName"], [34, 23, 28, 32], [35, 8, 28, 34, "deep"], [35, 12, 28, 38], [35, 14, 28, 40], [36, 6, 28, 46], [36, 7, 28, 47], [37, 4, 29, 4], [38, 2, 30, 0], [39, 2, 31, 0], [40, 2, 32, 0], [40, 11, 32, 9, "testNotFound"], [40, 23, 32, 21, "testNotFound"], [40, 24, 32, 22, "name"], [40, 28, 32, 26], [40, 30, 32, 28], [41, 4, 33, 4], [41, 11, 33, 11], [41, 25, 33, 25], [41, 26, 33, 26, "test"], [41, 30, 33, 30], [41, 31, 33, 31, "name"], [41, 35, 33, 35], [41, 36, 33, 36], [42, 2, 34, 0], [43, 2, 35, 0], [44, 2, 36, 0], [44, 11, 36, 9, "matchGroupName"], [44, 25, 36, 23, "matchGroupName"], [44, 26, 36, 24, "name"], [44, 30, 36, 28], [44, 32, 36, 30], [45, 4, 37, 4], [45, 11, 37, 11, "name"], [45, 15, 37, 15], [45, 16, 37, 16, "match"], [45, 21, 37, 21], [45, 22, 37, 22], [45, 51, 37, 51], [45, 52, 37, 52], [45, 55, 37, 55], [45, 56, 37, 56], [45, 57, 37, 57], [46, 2, 38, 0], [47, 2, 39, 0], [48, 2, 40, 0], [48, 11, 40, 9, "matchLastGroupName"], [48, 29, 40, 27, "matchLastGroupName"], [48, 30, 40, 28, "name"], [48, 34, 40, 32], [48, 36, 40, 34], [49, 4, 41, 4], [49, 11, 41, 11, "name"], [49, 15, 41, 15], [49, 16, 41, 16, "match"], [49, 21, 41, 21], [49, 22, 41, 22], [49, 54, 41, 54], [49, 55, 41, 55], [49, 58, 41, 58], [49, 59, 41, 59], [49, 60, 41, 60], [50, 2, 42, 0], [51, 2, 43, 0], [52, 2, 44, 0], [52, 11, 44, 9, "matchArrayGroupName"], [52, 30, 44, 28, "matchArrayGroupName"], [52, 31, 44, 29, "name"], [52, 35, 44, 33], [52, 37, 44, 35], [53, 4, 45, 4], [53, 11, 45, 11, "name"], [53, 15, 45, 15], [53, 16, 45, 16, "match"], [53, 21, 45, 21], [53, 22, 45, 22], [53, 58, 45, 58], [53, 59, 45, 59], [53, 62, 45, 62], [53, 63, 45, 63], [53, 64, 45, 64], [54, 2, 46, 0], [55, 2, 47, 0], [55, 11, 47, 9, "getNameFromFilePath"], [55, 30, 47, 28, "getNameFromFilePath"], [55, 31, 47, 29, "name"], [55, 35, 47, 33], [55, 37, 47, 35], [56, 4, 48, 4], [56, 11, 48, 11, "removeSupportedExtensions"], [56, 36, 48, 36], [56, 37, 48, 37, "removeFileSystemDots"], [56, 57, 48, 57], [56, 58, 48, 58, "name"], [56, 62, 48, 62], [56, 63, 48, 63], [56, 64, 48, 64], [57, 2, 49, 0], [58, 2, 50, 0], [58, 11, 50, 9, "getContextKey"], [58, 24, 50, 22, "getContextKey"], [58, 25, 50, 23, "name"], [58, 29, 50, 27], [58, 31, 50, 29], [59, 4, 51, 4], [60, 4, 52, 4], [61, 4, 53, 4], [61, 10, 53, 10, "normal"], [61, 16, 53, 16], [61, 19, 53, 19], [61, 22, 53, 22], [61, 25, 53, 25, "getNameFromFilePath"], [61, 44, 53, 44], [61, 45, 53, 45, "name"], [61, 49, 53, 49], [61, 50, 53, 50], [62, 4, 54, 4], [62, 8, 54, 8], [62, 9, 54, 9, "normal"], [62, 15, 54, 15], [62, 16, 54, 16, "endsWith"], [62, 24, 54, 24], [62, 25, 54, 25], [62, 34, 54, 34], [62, 35, 54, 35], [62, 37, 54, 37], [63, 6, 55, 8], [63, 13, 55, 15, "normal"], [63, 19, 55, 21], [64, 4, 56, 4], [65, 4, 57, 4], [65, 11, 57, 11, "normal"], [65, 17, 57, 17], [65, 18, 57, 18, "replace"], [65, 25, 57, 25], [65, 26, 57, 26], [65, 39, 57, 39], [65, 41, 57, 41], [65, 43, 57, 43], [65, 44, 57, 44], [66, 2, 58, 0], [67, 2, 59, 0], [68, 2, 60, 0], [68, 11, 60, 9, "removeSupportedExtensions"], [68, 36, 60, 34, "removeSupportedExtensions"], [68, 37, 60, 35, "name"], [68, 41, 60, 39], [68, 43, 60, 41], [69, 4, 61, 4], [69, 11, 61, 11, "name"], [69, 15, 61, 15], [69, 16, 61, 16, "replace"], [69, 23, 61, 23], [69, 24, 61, 24], [69, 45, 61, 45], [69, 47, 61, 47], [69, 49, 61, 49], [69, 50, 61, 50], [70, 2, 62, 0], [71, 2, 63, 0], [72, 2, 64, 0], [72, 11, 64, 9, "removeFileSystemExtensions"], [72, 37, 64, 35, "removeFileSystemExtensions"], [72, 38, 64, 36, "name"], [72, 42, 64, 40], [72, 44, 64, 42], [73, 4, 65, 4], [73, 11, 65, 11, "name"], [73, 15, 65, 15], [73, 16, 65, 16, "replace"], [73, 23, 65, 23], [73, 24, 65, 24], [73, 37, 65, 37], [73, 39, 65, 39], [73, 41, 65, 41], [73, 42, 65, 42], [74, 2, 66, 0], [75, 2, 67, 0], [76, 2, 68, 0], [76, 11, 68, 9, "removeFileSystemDots"], [76, 31, 68, 29, "removeFileSystemDots"], [76, 32, 68, 30, "filePath"], [76, 40, 68, 38], [76, 42, 68, 40], [77, 4, 69, 4], [77, 11, 69, 11, "filePath"], [77, 19, 69, 19], [77, 20, 69, 20, "replace"], [77, 27, 69, 27], [77, 28, 69, 28], [77, 44, 69, 44], [77, 46, 69, 46], [77, 48, 69, 48], [77, 49, 69, 49], [78, 2, 70, 0], [79, 2, 71, 0], [79, 11, 71, 9, "stripGroupSegmentsFromPath"], [79, 37, 71, 35, "stripGroupSegmentsFromPath"], [79, 38, 71, 36, "path"], [79, 42, 71, 40], [79, 44, 71, 42], [80, 4, 72, 4], [80, 11, 72, 11, "path"], [80, 15, 72, 15], [80, 16, 73, 9, "split"], [80, 21, 73, 14], [80, 22, 73, 15], [80, 25, 73, 18], [80, 26, 73, 19], [80, 27, 74, 9, "reduce"], [80, 33, 74, 15], [80, 34, 74, 16], [80, 35, 74, 17, "acc"], [80, 38, 74, 20], [80, 40, 74, 22, "v"], [80, 41, 74, 23], [80, 46, 74, 28], [81, 6, 75, 8], [81, 10, 75, 12, "matchGroupName"], [81, 24, 75, 26], [81, 25, 75, 27, "v"], [81, 26, 75, 28], [81, 27, 75, 29], [81, 31, 75, 33], [81, 35, 75, 37], [81, 37, 75, 39], [82, 8, 76, 12, "acc"], [82, 11, 76, 15], [82, 12, 76, 16, "push"], [82, 16, 76, 20], [82, 17, 76, 21, "v"], [82, 18, 76, 22], [82, 19, 76, 23], [83, 6, 77, 8], [84, 6, 78, 8], [84, 13, 78, 15, "acc"], [84, 16, 78, 18], [85, 4, 79, 4], [85, 5, 79, 5], [85, 7, 79, 7], [85, 9, 79, 9], [85, 10, 79, 10], [85, 11, 80, 9, "join"], [85, 15, 80, 13], [85, 16, 80, 14], [85, 19, 80, 17], [85, 20, 80, 18], [86, 2, 81, 0], [87, 2, 82, 0], [87, 11, 82, 9, "stripInvisibleSegmentsFromPath"], [87, 41, 82, 39, "stripInvisibleSegmentsFromPath"], [87, 42, 82, 40, "path"], [87, 46, 82, 44], [87, 48, 82, 46], [88, 4, 83, 4], [88, 11, 83, 11, "stripGroupSegmentsFromPath"], [88, 37, 83, 37], [88, 38, 83, 38, "path"], [88, 42, 83, 42], [88, 43, 83, 43], [88, 44, 83, 44, "replace"], [88, 51, 83, 51], [88, 52, 83, 52], [88, 63, 83, 63], [88, 65, 83, 65], [88, 67, 83, 67], [88, 68, 83, 68], [89, 2, 84, 0], [90, 2, 85, 0], [91, 0, 86, 0], [92, 0, 87, 0], [93, 0, 88, 0], [94, 0, 89, 0], [95, 2, 90, 0], [95, 11, 90, 9, "isTypedRoute"], [95, 23, 90, 21, "isTypedRoute"], [95, 24, 90, 22, "name"], [95, 28, 90, 26], [95, 30, 90, 28], [96, 4, 91, 4], [96, 11, 91, 11], [96, 12, 91, 12, "name"], [96, 16, 91, 16], [96, 17, 91, 17, "startsWith"], [96, 27, 91, 27], [96, 28, 91, 28], [96, 31, 91, 31], [96, 32, 91, 32], [96, 36, 91, 36, "name"], [96, 40, 91, 40], [96, 41, 91, 41, "match"], [96, 46, 91, 46], [96, 47, 91, 47], [96, 83, 91, 83], [96, 84, 91, 84], [96, 89, 91, 89], [96, 93, 91, 93], [97, 2, 92, 0], [98, 0, 92, 1], [98, 3]], "functionMap": {"names": ["<global>", "matchDynamicName", "testNotFound", "matchGroupName", "matchLastGroupName", "matchArrayGroupName", "getNameFromFilePath", "getContextKey", "removeSupportedExtensions", "removeFileSystemExtensions", "removeFileSystemDots", "stripGroupSegmentsFromPath", "path.split.reduce$argument_0", "stripInvisibleSegmentsFromPath", "isTypedRoute"], "mappings": "AAA;ACkB;CDW;AEE;CFE;AGE;CHE;AIE;CJE;AKE;CLE;AMC;CNE;AOC;CPQ;AQE;CRE;ASE;CTE;AUE;CVE;AWC;gBCG;KDK;CXE;AaC;CbE;AcM;CdE"}}, "type": "js/module"}]}