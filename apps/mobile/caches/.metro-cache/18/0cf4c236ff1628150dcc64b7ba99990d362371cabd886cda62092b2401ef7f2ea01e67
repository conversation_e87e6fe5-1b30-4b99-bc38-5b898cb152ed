{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeSoundManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 54}}], "key": "LJTg1g7phwZj/7SqYP8dHnGXpac=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeSoundManager = _interopRequireDefault(require(_dependencyMap[1], \"./NativeSoundManager\"));\n  var SoundManager = {\n    playTouchSound: function () {\n      if (_NativeSoundManager.default) {\n        _NativeSoundManager.default.playTouchSound();\n      }\n    }\n  };\n  var _default = exports.default = SoundManager;\n});", "lineCount": 16, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeSoundManager"], [7, 25, 11, 0], [7, 28, 11, 0, "_interopRequireDefault"], [7, 50, 11, 0], [7, 51, 11, 0, "require"], [7, 58, 11, 0], [7, 59, 11, 0, "_dependencyMap"], [7, 73, 11, 0], [8, 2, 13, 0], [8, 6, 13, 6, "SoundManager"], [8, 18, 13, 18], [8, 21, 13, 21], [9, 4, 14, 2, "playTouchSound"], [9, 18, 14, 16], [9, 20, 14, 18], [9, 29, 14, 18, "playTouchSound"], [9, 30, 14, 18], [9, 32, 14, 36], [10, 6, 15, 4], [10, 10, 15, 8, "NativeSoundManager"], [10, 37, 15, 26], [10, 39, 15, 28], [11, 8, 16, 6, "NativeSoundManager"], [11, 35, 16, 24], [11, 36, 16, 25, "playTouchSound"], [11, 50, 16, 39], [11, 51, 16, 40], [11, 52, 16, 41], [12, 6, 17, 4], [13, 4, 18, 2], [14, 2, 19, 0], [14, 3, 19, 1], [15, 2, 19, 2], [15, 6, 19, 2, "_default"], [15, 14, 19, 2], [15, 17, 19, 2, "exports"], [15, 24, 19, 2], [15, 25, 19, 2, "default"], [15, 32, 19, 2], [15, 35, 21, 15, "SoundManager"], [15, 47, 21, 27], [16, 0, 21, 27], [16, 3]], "functionMap": {"names": ["<global>", "playTouchSound"], "mappings": "AAA;kBCa;GDI"}}, "type": "js/module"}]}