{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 113}, "end": {"line": 3, "column": 58, "index": 171}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "./useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 172}, "end": {"line": 4, "column": 40, "index": 212}}], "key": "qBoul5KQ1+OEu8G3Sr6Tlb7g7CM=", "exportNames": ["*"]}}, {"name": "./AudioVisualizer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 213}, "end": {"line": 5, "column": 48, "index": 261}}], "key": "PtmazYoURS57fsddHvpL2PH220Y=", "exportNames": ["*"]}}, {"name": "./RealtimeTranscript", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 54, "index": 316}}], "key": "r6gm1hrdQBRgTrcSPkygz/enaRs=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = VoiceMode;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _lucideReactNative = require(_dependencyMap[5], \"lucide-react-native\");\n  var _useColors = require(_dependencyMap[6], \"./useColors\");\n  var _AudioVisualizer = _interopRequireDefault(require(_dependencyMap[7], \"./AudioVisualizer\"));\n  var _RealtimeTranscript = _interopRequireDefault(require(_dependencyMap[8], \"./RealtimeTranscript\"));\n  var _jsxDevRuntime = require(_dependencyMap[9], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/components/VoiceMode.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function VoiceMode({\n    isRecording,\n    hasPermission,\n    isLoading,\n    transcript,\n    isMuted,\n    onMute\n  }) {\n    _s();\n    const colors = (0, _useColors.useColors)();\n    const [isSpeaking, setIsSpeaking] = (0, _react.useState)(false);\n    (0, _react.useEffect)(() => {\n      // Simulate speaking state for the visualizer\n      // Only show as speaking if recording and not muted\n      if (isRecording && !isMuted) {\n        setIsSpeaking(true);\n      } else {\n        setIsSpeaking(false);\n      }\n    }, [isRecording, isMuted]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_AudioVisualizer.default, {\n        isSpeaking: isSpeaking\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_RealtimeTranscript.default, {\n        transcript: transcript\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          position: 'absolute',\n          bottom: 0,\n          left: 0,\n          right: 0,\n          alignItems: 'center',\n          gap: 12,\n          paddingHorizontal: 16,\n          paddingTop: 16,\n          paddingBottom: 16,\n          backgroundColor: 'transparent'\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            width: 72,\n            height: 72,\n            borderRadius: 36,\n            backgroundColor: isRecording && !isMuted ? '#00C851' : isRecording ? '#FFA500' : colors.primary,\n            alignItems: 'center',\n            justifyContent: 'center',\n            shadowColor: '#000',\n            shadowOffset: {\n              width: 0,\n              height: 2\n            },\n            shadowOpacity: 0.2,\n            shadowRadius: 4,\n            elevation: 4\n          },\n          onPress: onRecord,\n          disabled: !hasPermission || isLoading,\n          children: isRecording ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Square, {\n            size: 28,\n            color: colors.background,\n            fill: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n            size: 28,\n            color: colors.background\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: {\n            fontSize: 14,\n            fontFamily: 'Poppins_400Regular',\n            color: colors.textSecondary,\n            textAlign: 'center'\n          },\n          children: isRecording ? isMuted ? 'Voice session active (muted) - tap to end' : 'Voice session active - tap to end' : hasPermission ? 'Tap to start voice session' : 'Microphone permission needed'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n        style: {\n          position: 'absolute',\n          bottom: 32,\n          right: 24,\n          width: 56,\n          height: 56,\n          borderRadius: 28,\n          backgroundColor: isMuted ? '#FF3B30' : colors.primary,\n          alignItems: 'center',\n          justifyContent: 'center',\n          shadowColor: '#000',\n          shadowOffset: {\n            width: 0,\n            height: 2\n          },\n          shadowOpacity: 0.2,\n          shadowRadius: 4,\n          elevation: 4\n        },\n        onPress: onMute,\n        children: isMuted ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MicOff, {\n          size: 24,\n          color: colors.background\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n          size: 24,\n          color: colors.background\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 5\n    }, this);\n  }\n  _s(VoiceMode, \"je5mBwrzKqZY+g+64G/AzRwD0LM=\", false, function () {\n    return [_useColors.useColors];\n  });\n  _c = VoiceMode;\n  var _c;\n  $RefreshReg$(_c, \"VoiceMode\");\n});", "lineCount": 177, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 51], [8, 6, 1, 51, "_View"], [8, 11, 1, 51], [8, 14, 1, 51, "_interopRequireDefault"], [8, 36, 1, 51], [8, 37, 1, 51, "require"], [8, 44, 1, 51], [8, 45, 1, 51, "_dependencyMap"], [8, 59, 1, 51], [9, 2, 1, 51], [9, 6, 1, 51, "_Text"], [9, 11, 1, 51], [9, 14, 1, 51, "_interopRequireDefault"], [9, 36, 1, 51], [9, 37, 1, 51, "require"], [9, 44, 1, 51], [9, 45, 1, 51, "_dependencyMap"], [9, 59, 1, 51], [10, 2, 1, 51], [10, 6, 1, 51, "_TouchableOpacity"], [10, 23, 1, 51], [10, 26, 1, 51, "_interopRequireDefault"], [10, 48, 1, 51], [10, 49, 1, 51, "require"], [10, 56, 1, 51], [10, 57, 1, 51, "_dependencyMap"], [10, 71, 1, 51], [11, 2, 3, 0], [11, 6, 3, 0, "_lucideReactNative"], [11, 24, 3, 0], [11, 27, 3, 0, "require"], [11, 34, 3, 0], [11, 35, 3, 0, "_dependencyMap"], [11, 49, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_useColors"], [12, 16, 4, 0], [12, 19, 4, 0, "require"], [12, 26, 4, 0], [12, 27, 4, 0, "_dependencyMap"], [12, 41, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_AudioVisualizer"], [13, 22, 5, 0], [13, 25, 5, 0, "_interopRequireDefault"], [13, 47, 5, 0], [13, 48, 5, 0, "require"], [13, 55, 5, 0], [13, 56, 5, 0, "_dependencyMap"], [13, 70, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_RealtimeTranscript"], [14, 25, 6, 0], [14, 28, 6, 0, "_interopRequireDefault"], [14, 50, 6, 0], [14, 51, 6, 0, "require"], [14, 58, 6, 0], [14, 59, 6, 0, "_dependencyMap"], [14, 73, 6, 0], [15, 2, 6, 54], [15, 6, 6, 54, "_jsxDevRuntime"], [15, 20, 6, 54], [15, 23, 6, 54, "require"], [15, 30, 6, 54], [15, 31, 6, 54, "_dependencyMap"], [15, 45, 6, 54], [16, 2, 6, 54], [16, 6, 6, 54, "_jsxFileName"], [16, 18, 6, 54], [17, 4, 6, 54, "_s"], [17, 6, 6, 54], [17, 9, 6, 54, "$RefreshSig$"], [17, 21, 6, 54], [18, 2, 6, 54], [18, 11, 6, 54, "_interopRequireWildcard"], [18, 35, 6, 54, "e"], [18, 36, 6, 54], [18, 38, 6, 54, "t"], [18, 39, 6, 54], [18, 68, 6, 54, "WeakMap"], [18, 75, 6, 54], [18, 81, 6, 54, "r"], [18, 82, 6, 54], [18, 89, 6, 54, "WeakMap"], [18, 96, 6, 54], [18, 100, 6, 54, "n"], [18, 101, 6, 54], [18, 108, 6, 54, "WeakMap"], [18, 115, 6, 54], [18, 127, 6, 54, "_interopRequireWildcard"], [18, 150, 6, 54], [18, 162, 6, 54, "_interopRequireWildcard"], [18, 163, 6, 54, "e"], [18, 164, 6, 54], [18, 166, 6, 54, "t"], [18, 167, 6, 54], [18, 176, 6, 54, "t"], [18, 177, 6, 54], [18, 181, 6, 54, "e"], [18, 182, 6, 54], [18, 186, 6, 54, "e"], [18, 187, 6, 54], [18, 188, 6, 54, "__esModule"], [18, 198, 6, 54], [18, 207, 6, 54, "e"], [18, 208, 6, 54], [18, 214, 6, 54, "o"], [18, 215, 6, 54], [18, 217, 6, 54, "i"], [18, 218, 6, 54], [18, 220, 6, 54, "f"], [18, 221, 6, 54], [18, 226, 6, 54, "__proto__"], [18, 235, 6, 54], [18, 243, 6, 54, "default"], [18, 250, 6, 54], [18, 252, 6, 54, "e"], [18, 253, 6, 54], [18, 270, 6, 54, "e"], [18, 271, 6, 54], [18, 294, 6, 54, "e"], [18, 295, 6, 54], [18, 320, 6, 54, "e"], [18, 321, 6, 54], [18, 330, 6, 54, "f"], [18, 331, 6, 54], [18, 337, 6, 54, "o"], [18, 338, 6, 54], [18, 341, 6, 54, "t"], [18, 342, 6, 54], [18, 345, 6, 54, "n"], [18, 346, 6, 54], [18, 349, 6, 54, "r"], [18, 350, 6, 54], [18, 358, 6, 54, "o"], [18, 359, 6, 54], [18, 360, 6, 54, "has"], [18, 363, 6, 54], [18, 364, 6, 54, "e"], [18, 365, 6, 54], [18, 375, 6, 54, "o"], [18, 376, 6, 54], [18, 377, 6, 54, "get"], [18, 380, 6, 54], [18, 381, 6, 54, "e"], [18, 382, 6, 54], [18, 385, 6, 54, "o"], [18, 386, 6, 54], [18, 387, 6, 54, "set"], [18, 390, 6, 54], [18, 391, 6, 54, "e"], [18, 392, 6, 54], [18, 394, 6, 54, "f"], [18, 395, 6, 54], [18, 411, 6, 54, "t"], [18, 412, 6, 54], [18, 416, 6, 54, "e"], [18, 417, 6, 54], [18, 433, 6, 54, "t"], [18, 434, 6, 54], [18, 441, 6, 54, "hasOwnProperty"], [18, 455, 6, 54], [18, 456, 6, 54, "call"], [18, 460, 6, 54], [18, 461, 6, 54, "e"], [18, 462, 6, 54], [18, 464, 6, 54, "t"], [18, 465, 6, 54], [18, 472, 6, 54, "i"], [18, 473, 6, 54], [18, 477, 6, 54, "o"], [18, 478, 6, 54], [18, 481, 6, 54, "Object"], [18, 487, 6, 54], [18, 488, 6, 54, "defineProperty"], [18, 502, 6, 54], [18, 507, 6, 54, "Object"], [18, 513, 6, 54], [18, 514, 6, 54, "getOwnPropertyDescriptor"], [18, 538, 6, 54], [18, 539, 6, 54, "e"], [18, 540, 6, 54], [18, 542, 6, 54, "t"], [18, 543, 6, 54], [18, 550, 6, 54, "i"], [18, 551, 6, 54], [18, 552, 6, 54, "get"], [18, 555, 6, 54], [18, 559, 6, 54, "i"], [18, 560, 6, 54], [18, 561, 6, 54, "set"], [18, 564, 6, 54], [18, 568, 6, 54, "o"], [18, 569, 6, 54], [18, 570, 6, 54, "f"], [18, 571, 6, 54], [18, 573, 6, 54, "t"], [18, 574, 6, 54], [18, 576, 6, 54, "i"], [18, 577, 6, 54], [18, 581, 6, 54, "f"], [18, 582, 6, 54], [18, 583, 6, 54, "t"], [18, 584, 6, 54], [18, 588, 6, 54, "e"], [18, 589, 6, 54], [18, 590, 6, 54, "t"], [18, 591, 6, 54], [18, 602, 6, 54, "f"], [18, 603, 6, 54], [18, 608, 6, 54, "e"], [18, 609, 6, 54], [18, 611, 6, 54, "t"], [18, 612, 6, 54], [19, 2, 8, 15], [19, 11, 8, 24, "VoiceMode"], [19, 20, 8, 33, "VoiceMode"], [19, 21, 8, 34], [20, 4, 8, 36, "isRecording"], [20, 15, 8, 47], [21, 4, 8, 49, "hasPermission"], [21, 17, 8, 62], [22, 4, 8, 64, "isLoading"], [22, 13, 8, 73], [23, 4, 8, 75, "transcript"], [23, 14, 8, 85], [24, 4, 8, 87, "isMuted"], [24, 11, 8, 94], [25, 4, 8, 96, "onMute"], [26, 2, 8, 103], [26, 3, 8, 104], [26, 5, 8, 106], [27, 4, 8, 106, "_s"], [27, 6, 8, 106], [28, 4, 9, 2], [28, 10, 9, 8, "colors"], [28, 16, 9, 14], [28, 19, 9, 17], [28, 23, 9, 17, "useColors"], [28, 43, 9, 26], [28, 45, 9, 27], [28, 46, 9, 28], [29, 4, 10, 2], [29, 10, 10, 8], [29, 11, 10, 9, "isSpeaking"], [29, 21, 10, 19], [29, 23, 10, 21, "setIsSpeaking"], [29, 36, 10, 34], [29, 37, 10, 35], [29, 40, 10, 38], [29, 44, 10, 38, "useState"], [29, 59, 10, 46], [29, 61, 10, 47], [29, 66, 10, 52], [29, 67, 10, 53], [30, 4, 12, 2], [30, 8, 12, 2, "useEffect"], [30, 24, 12, 11], [30, 26, 12, 12], [30, 32, 12, 18], [31, 6, 13, 4], [32, 6, 14, 4], [33, 6, 15, 4], [33, 10, 15, 8, "isRecording"], [33, 21, 15, 19], [33, 25, 15, 23], [33, 26, 15, 24, "isMuted"], [33, 33, 15, 31], [33, 35, 15, 33], [34, 8, 16, 6, "setIsSpeaking"], [34, 21, 16, 19], [34, 22, 16, 20], [34, 26, 16, 24], [34, 27, 16, 25], [35, 6, 17, 4], [35, 7, 17, 5], [35, 13, 17, 11], [36, 8, 18, 6, "setIsSpeaking"], [36, 21, 18, 19], [36, 22, 18, 20], [36, 27, 18, 25], [36, 28, 18, 26], [37, 6, 19, 4], [38, 4, 20, 2], [38, 5, 20, 3], [38, 7, 20, 5], [38, 8, 20, 6, "isRecording"], [38, 19, 20, 17], [38, 21, 20, 19, "isMuted"], [38, 28, 20, 26], [38, 29, 20, 27], [38, 30, 20, 28], [39, 4, 22, 2], [39, 24, 23, 4], [39, 28, 23, 4, "_jsxDevRuntime"], [39, 42, 23, 4], [39, 43, 23, 4, "jsxDEV"], [39, 49, 23, 4], [39, 51, 23, 5, "_View"], [39, 56, 23, 5], [39, 57, 23, 5, "default"], [39, 64, 23, 9], [40, 6, 23, 10, "style"], [40, 11, 23, 15], [40, 13, 23, 17], [41, 8, 23, 19, "flex"], [41, 12, 23, 23], [41, 14, 23, 25], [42, 6, 23, 27], [42, 7, 23, 29], [43, 6, 23, 29, "children"], [43, 14, 23, 29], [43, 30, 24, 6], [43, 34, 24, 6, "_jsxDevRuntime"], [43, 48, 24, 6], [43, 49, 24, 6, "jsxDEV"], [43, 55, 24, 6], [43, 57, 24, 7, "_AudioVisualizer"], [43, 73, 24, 7], [43, 74, 24, 7, "default"], [43, 81, 24, 22], [44, 8, 24, 23, "isSpeaking"], [44, 18, 24, 33], [44, 20, 24, 35, "isSpeaking"], [45, 6, 24, 46], [46, 8, 24, 46, "fileName"], [46, 16, 24, 46], [46, 18, 24, 46, "_jsxFileName"], [46, 30, 24, 46], [47, 8, 24, 46, "lineNumber"], [47, 18, 24, 46], [48, 8, 24, 46, "columnNumber"], [48, 20, 24, 46], [49, 6, 24, 46], [49, 13, 24, 48], [49, 14, 24, 49], [49, 29, 25, 6], [49, 33, 25, 6, "_jsxDevRuntime"], [49, 47, 25, 6], [49, 48, 25, 6, "jsxDEV"], [49, 54, 25, 6], [49, 56, 25, 7, "_RealtimeTranscript"], [49, 75, 25, 7], [49, 76, 25, 7, "default"], [49, 83, 25, 25], [50, 8, 25, 26, "transcript"], [50, 18, 25, 36], [50, 20, 25, 38, "transcript"], [51, 6, 25, 49], [52, 8, 25, 49, "fileName"], [52, 16, 25, 49], [52, 18, 25, 49, "_jsxFileName"], [52, 30, 25, 49], [53, 8, 25, 49, "lineNumber"], [53, 18, 25, 49], [54, 8, 25, 49, "columnNumber"], [54, 20, 25, 49], [55, 6, 25, 49], [55, 13, 25, 51], [55, 14, 25, 52], [55, 29, 28, 6], [55, 33, 28, 6, "_jsxDevRuntime"], [55, 47, 28, 6], [55, 48, 28, 6, "jsxDEV"], [55, 54, 28, 6], [55, 56, 28, 7, "_View"], [55, 61, 28, 7], [55, 62, 28, 7, "default"], [55, 69, 28, 11], [56, 8, 29, 8, "style"], [56, 13, 29, 13], [56, 15, 29, 15], [57, 10, 30, 10, "position"], [57, 18, 30, 18], [57, 20, 30, 20], [57, 30, 30, 30], [58, 10, 31, 10, "bottom"], [58, 16, 31, 16], [58, 18, 31, 18], [58, 19, 31, 19], [59, 10, 32, 10, "left"], [59, 14, 32, 14], [59, 16, 32, 16], [59, 17, 32, 17], [60, 10, 33, 10, "right"], [60, 15, 33, 15], [60, 17, 33, 17], [60, 18, 33, 18], [61, 10, 34, 10, "alignItems"], [61, 20, 34, 20], [61, 22, 34, 22], [61, 30, 34, 30], [62, 10, 35, 10, "gap"], [62, 13, 35, 13], [62, 15, 35, 15], [62, 17, 35, 17], [63, 10, 36, 10, "paddingHorizontal"], [63, 27, 36, 27], [63, 29, 36, 29], [63, 31, 36, 31], [64, 10, 37, 10, "paddingTop"], [64, 20, 37, 20], [64, 22, 37, 22], [64, 24, 37, 24], [65, 10, 38, 10, "paddingBottom"], [65, 23, 38, 23], [65, 25, 38, 25], [65, 27, 38, 27], [66, 10, 39, 10, "backgroundColor"], [66, 25, 39, 25], [66, 27, 39, 27], [67, 8, 40, 8], [67, 9, 40, 10], [68, 8, 40, 10, "children"], [68, 16, 40, 10], [68, 32, 42, 8], [68, 36, 42, 8, "_jsxDevRuntime"], [68, 50, 42, 8], [68, 51, 42, 8, "jsxDEV"], [68, 57, 42, 8], [68, 59, 42, 9, "_TouchableOpacity"], [68, 76, 42, 9], [68, 77, 42, 9, "default"], [68, 84, 42, 25], [69, 10, 43, 10, "style"], [69, 15, 43, 15], [69, 17, 43, 17], [70, 12, 44, 12, "width"], [70, 17, 44, 17], [70, 19, 44, 19], [70, 21, 44, 21], [71, 12, 45, 12, "height"], [71, 18, 45, 18], [71, 20, 45, 20], [71, 22, 45, 22], [72, 12, 46, 12, "borderRadius"], [72, 24, 46, 24], [72, 26, 46, 26], [72, 28, 46, 28], [73, 12, 47, 12, "backgroundColor"], [73, 27, 47, 27], [73, 29, 47, 29, "isRecording"], [73, 40, 47, 40], [73, 44, 47, 44], [73, 45, 47, 45, "isMuted"], [73, 52, 47, 52], [73, 55, 47, 55], [73, 64, 47, 64], [73, 67, 47, 67, "isRecording"], [73, 78, 47, 78], [73, 81, 47, 81], [73, 90, 47, 90], [73, 93, 47, 93, "colors"], [73, 99, 47, 99], [73, 100, 47, 100, "primary"], [73, 107, 47, 107], [74, 12, 48, 12, "alignItems"], [74, 22, 48, 22], [74, 24, 48, 24], [74, 32, 48, 32], [75, 12, 49, 12, "justifyContent"], [75, 26, 49, 26], [75, 28, 49, 28], [75, 36, 49, 36], [76, 12, 50, 12, "shadowColor"], [76, 23, 50, 23], [76, 25, 50, 25], [76, 31, 50, 31], [77, 12, 51, 12, "shadowOffset"], [77, 24, 51, 24], [77, 26, 51, 26], [78, 14, 51, 28, "width"], [78, 19, 51, 33], [78, 21, 51, 35], [78, 22, 51, 36], [79, 14, 51, 38, "height"], [79, 20, 51, 44], [79, 22, 51, 46], [80, 12, 51, 48], [80, 13, 51, 49], [81, 12, 52, 12, "shadowOpacity"], [81, 25, 52, 25], [81, 27, 52, 27], [81, 30, 52, 30], [82, 12, 53, 12, "shadowRadius"], [82, 24, 53, 24], [82, 26, 53, 26], [82, 27, 53, 27], [83, 12, 54, 12, "elevation"], [83, 21, 54, 21], [83, 23, 54, 23], [84, 10, 55, 10], [84, 11, 55, 12], [85, 10, 56, 10, "onPress"], [85, 17, 56, 17], [85, 19, 56, 19, "onRecord"], [85, 27, 56, 28], [86, 10, 57, 10, "disabled"], [86, 18, 57, 18], [86, 20, 57, 20], [86, 21, 57, 21, "hasPermission"], [86, 34, 57, 34], [86, 38, 57, 38, "isLoading"], [86, 47, 57, 48], [87, 10, 57, 48, "children"], [87, 18, 57, 48], [87, 20, 59, 11, "isRecording"], [87, 31, 59, 22], [87, 47, 60, 12], [87, 51, 60, 12, "_jsxDevRuntime"], [87, 65, 60, 12], [87, 66, 60, 12, "jsxDEV"], [87, 72, 60, 12], [87, 74, 60, 13, "_lucideReactNative"], [87, 92, 60, 13], [87, 93, 60, 13, "Square"], [87, 99, 60, 19], [88, 12, 60, 20, "size"], [88, 16, 60, 24], [88, 18, 60, 26], [88, 20, 60, 29], [89, 12, 60, 30, "color"], [89, 17, 60, 35], [89, 19, 60, 37, "colors"], [89, 25, 60, 43], [89, 26, 60, 44, "background"], [89, 36, 60, 55], [90, 12, 60, 56, "fill"], [90, 16, 60, 60], [90, 18, 60, 62, "colors"], [90, 24, 60, 68], [90, 25, 60, 69, "background"], [91, 10, 60, 80], [92, 12, 60, 80, "fileName"], [92, 20, 60, 80], [92, 22, 60, 80, "_jsxFileName"], [92, 34, 60, 80], [93, 12, 60, 80, "lineNumber"], [93, 22, 60, 80], [94, 12, 60, 80, "columnNumber"], [94, 24, 60, 80], [95, 10, 60, 80], [95, 17, 60, 82], [95, 18, 60, 83], [95, 34, 62, 12], [95, 38, 62, 12, "_jsxDevRuntime"], [95, 52, 62, 12], [95, 53, 62, 12, "jsxDEV"], [95, 59, 62, 12], [95, 61, 62, 13, "_lucideReactNative"], [95, 79, 62, 13], [95, 80, 62, 13, "Mic"], [95, 83, 62, 16], [96, 12, 62, 17, "size"], [96, 16, 62, 21], [96, 18, 62, 23], [96, 20, 62, 26], [97, 12, 62, 27, "color"], [97, 17, 62, 32], [97, 19, 62, 34, "colors"], [97, 25, 62, 40], [97, 26, 62, 41, "background"], [98, 10, 62, 52], [99, 12, 62, 52, "fileName"], [99, 20, 62, 52], [99, 22, 62, 52, "_jsxFileName"], [99, 34, 62, 52], [100, 12, 62, 52, "lineNumber"], [100, 22, 62, 52], [101, 12, 62, 52, "columnNumber"], [101, 24, 62, 52], [102, 10, 62, 52], [102, 17, 62, 54], [103, 8, 63, 11], [104, 10, 63, 11, "fileName"], [104, 18, 63, 11], [104, 20, 63, 11, "_jsxFileName"], [104, 32, 63, 11], [105, 10, 63, 11, "lineNumber"], [105, 20, 63, 11], [106, 10, 63, 11, "columnNumber"], [106, 22, 63, 11], [107, 8, 63, 11], [107, 15, 64, 26], [107, 16, 64, 27], [107, 31, 65, 8], [107, 35, 65, 8, "_jsxDevRuntime"], [107, 49, 65, 8], [107, 50, 65, 8, "jsxDEV"], [107, 56, 65, 8], [107, 58, 65, 9, "_Text"], [107, 63, 65, 9], [107, 64, 65, 9, "default"], [107, 71, 65, 13], [108, 10, 66, 10, "style"], [108, 15, 66, 15], [108, 17, 66, 17], [109, 12, 67, 12, "fontSize"], [109, 20, 67, 20], [109, 22, 67, 22], [109, 24, 67, 24], [110, 12, 68, 12, "fontFamily"], [110, 22, 68, 22], [110, 24, 68, 24], [110, 44, 68, 44], [111, 12, 69, 12, "color"], [111, 17, 69, 17], [111, 19, 69, 19, "colors"], [111, 25, 69, 25], [111, 26, 69, 26, "textSecondary"], [111, 39, 69, 39], [112, 12, 70, 12, "textAlign"], [112, 21, 70, 21], [112, 23, 70, 23], [113, 10, 71, 10], [113, 11, 71, 12], [114, 10, 71, 12, "children"], [114, 18, 71, 12], [114, 20, 73, 11, "isRecording"], [114, 31, 73, 22], [114, 34, 74, 14, "isMuted"], [114, 41, 74, 21], [114, 44, 75, 16], [114, 87, 75, 59], [114, 90, 76, 16], [114, 125, 76, 51], [114, 128, 77, 14, "hasPermission"], [114, 141, 77, 27], [114, 144, 78, 16], [114, 172, 78, 44], [114, 175, 79, 16], [115, 8, 79, 46], [116, 10, 79, 46, "fileName"], [116, 18, 79, 46], [116, 20, 79, 46, "_jsxFileName"], [116, 32, 79, 46], [117, 10, 79, 46, "lineNumber"], [117, 20, 79, 46], [118, 10, 79, 46, "columnNumber"], [118, 22, 79, 46], [119, 8, 79, 46], [119, 15, 80, 14], [119, 16, 80, 15], [120, 6, 80, 15], [121, 8, 80, 15, "fileName"], [121, 16, 80, 15], [121, 18, 80, 15, "_jsxFileName"], [121, 30, 80, 15], [122, 8, 80, 15, "lineNumber"], [122, 18, 80, 15], [123, 8, 80, 15, "columnNumber"], [123, 20, 80, 15], [124, 6, 80, 15], [124, 13, 81, 12], [124, 14, 81, 13], [124, 29, 84, 6], [124, 33, 84, 6, "_jsxDevRuntime"], [124, 47, 84, 6], [124, 48, 84, 6, "jsxDEV"], [124, 54, 84, 6], [124, 56, 84, 7, "_TouchableOpacity"], [124, 73, 84, 7], [124, 74, 84, 7, "default"], [124, 81, 84, 23], [125, 8, 85, 8, "style"], [125, 13, 85, 13], [125, 15, 85, 15], [126, 10, 86, 10, "position"], [126, 18, 86, 18], [126, 20, 86, 20], [126, 30, 86, 30], [127, 10, 87, 10, "bottom"], [127, 16, 87, 16], [127, 18, 87, 18], [127, 20, 87, 20], [128, 10, 88, 10, "right"], [128, 15, 88, 15], [128, 17, 88, 17], [128, 19, 88, 19], [129, 10, 89, 10, "width"], [129, 15, 89, 15], [129, 17, 89, 17], [129, 19, 89, 19], [130, 10, 90, 10, "height"], [130, 16, 90, 16], [130, 18, 90, 18], [130, 20, 90, 20], [131, 10, 91, 10, "borderRadius"], [131, 22, 91, 22], [131, 24, 91, 24], [131, 26, 91, 26], [132, 10, 92, 10, "backgroundColor"], [132, 25, 92, 25], [132, 27, 92, 27, "isMuted"], [132, 34, 92, 34], [132, 37, 92, 37], [132, 46, 92, 46], [132, 49, 92, 49, "colors"], [132, 55, 92, 55], [132, 56, 92, 56, "primary"], [132, 63, 92, 63], [133, 10, 93, 10, "alignItems"], [133, 20, 93, 20], [133, 22, 93, 22], [133, 30, 93, 30], [134, 10, 94, 10, "justifyContent"], [134, 24, 94, 24], [134, 26, 94, 26], [134, 34, 94, 34], [135, 10, 95, 10, "shadowColor"], [135, 21, 95, 21], [135, 23, 95, 23], [135, 29, 95, 29], [136, 10, 96, 10, "shadowOffset"], [136, 22, 96, 22], [136, 24, 96, 24], [137, 12, 96, 26, "width"], [137, 17, 96, 31], [137, 19, 96, 33], [137, 20, 96, 34], [138, 12, 96, 36, "height"], [138, 18, 96, 42], [138, 20, 96, 44], [139, 10, 96, 46], [139, 11, 96, 47], [140, 10, 97, 10, "shadowOpacity"], [140, 23, 97, 23], [140, 25, 97, 25], [140, 28, 97, 28], [141, 10, 98, 10, "shadowRadius"], [141, 22, 98, 22], [141, 24, 98, 24], [141, 25, 98, 25], [142, 10, 99, 10, "elevation"], [142, 19, 99, 19], [142, 21, 99, 21], [143, 8, 100, 8], [143, 9, 100, 10], [144, 8, 101, 8, "onPress"], [144, 15, 101, 15], [144, 17, 101, 17, "onMute"], [144, 23, 101, 24], [145, 8, 101, 24, "children"], [145, 16, 101, 24], [145, 18, 103, 9, "isMuted"], [145, 25, 103, 16], [145, 41, 104, 10], [145, 45, 104, 10, "_jsxDevRuntime"], [145, 59, 104, 10], [145, 60, 104, 10, "jsxDEV"], [145, 66, 104, 10], [145, 68, 104, 11, "_lucideReactNative"], [145, 86, 104, 11], [145, 87, 104, 11, "<PERSON><PERSON><PERSON><PERSON>"], [145, 93, 104, 17], [146, 10, 104, 18, "size"], [146, 14, 104, 22], [146, 16, 104, 24], [146, 18, 104, 27], [147, 10, 104, 28, "color"], [147, 15, 104, 33], [147, 17, 104, 35, "colors"], [147, 23, 104, 41], [147, 24, 104, 42, "background"], [148, 8, 104, 53], [149, 10, 104, 53, "fileName"], [149, 18, 104, 53], [149, 20, 104, 53, "_jsxFileName"], [149, 32, 104, 53], [150, 10, 104, 53, "lineNumber"], [150, 20, 104, 53], [151, 10, 104, 53, "columnNumber"], [151, 22, 104, 53], [152, 8, 104, 53], [152, 15, 104, 55], [152, 16, 104, 56], [152, 32, 106, 10], [152, 36, 106, 10, "_jsxDevRuntime"], [152, 50, 106, 10], [152, 51, 106, 10, "jsxDEV"], [152, 57, 106, 10], [152, 59, 106, 11, "_lucideReactNative"], [152, 77, 106, 11], [152, 78, 106, 11, "Mic"], [152, 81, 106, 14], [153, 10, 106, 15, "size"], [153, 14, 106, 19], [153, 16, 106, 21], [153, 18, 106, 24], [154, 10, 106, 25, "color"], [154, 15, 106, 30], [154, 17, 106, 32, "colors"], [154, 23, 106, 38], [154, 24, 106, 39, "background"], [155, 8, 106, 50], [156, 10, 106, 50, "fileName"], [156, 18, 106, 50], [156, 20, 106, 50, "_jsxFileName"], [156, 32, 106, 50], [157, 10, 106, 50, "lineNumber"], [157, 20, 106, 50], [158, 10, 106, 50, "columnNumber"], [158, 22, 106, 50], [159, 8, 106, 50], [159, 15, 106, 52], [160, 6, 107, 9], [161, 8, 107, 9, "fileName"], [161, 16, 107, 9], [161, 18, 107, 9, "_jsxFileName"], [161, 30, 107, 9], [162, 8, 107, 9, "lineNumber"], [162, 18, 107, 9], [163, 8, 107, 9, "columnNumber"], [163, 20, 107, 9], [164, 6, 107, 9], [164, 13, 108, 24], [164, 14, 108, 25], [165, 4, 108, 25], [166, 6, 108, 25, "fileName"], [166, 14, 108, 25], [166, 16, 108, 25, "_jsxFileName"], [166, 28, 108, 25], [167, 6, 108, 25, "lineNumber"], [167, 16, 108, 25], [168, 6, 108, 25, "columnNumber"], [168, 18, 108, 25], [169, 4, 108, 25], [169, 11, 109, 10], [169, 12, 109, 11], [170, 2, 111, 0], [171, 2, 111, 1, "_s"], [171, 4, 111, 1], [171, 5, 8, 24, "VoiceMode"], [171, 14, 8, 33], [172, 4, 8, 33], [172, 12, 9, 17, "useColors"], [172, 32, 9, 26], [173, 2, 9, 26], [174, 2, 9, 26, "_c"], [174, 4, 9, 26], [174, 7, 8, 24, "VoiceMode"], [174, 16, 8, 33], [175, 2, 8, 33], [175, 6, 8, 33, "_c"], [175, 8, 8, 33], [176, 2, 8, 33, "$RefreshReg$"], [176, 14, 8, 33], [176, 15, 8, 33, "_c"], [176, 17, 8, 33], [177, 0, 8, 33], [177, 3]], "functionMap": {"names": ["<global>", "VoiceMode", "useEffect$argument_0"], "mappings": "AAA;eCO;YCI;GDQ;CD2F"}}, "type": "js/module"}]}