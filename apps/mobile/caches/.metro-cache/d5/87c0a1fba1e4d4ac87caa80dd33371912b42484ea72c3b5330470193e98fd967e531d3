{"dependencies": [{"name": "./core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 87, "index": 102}}], "key": "mJVVi7YU3vDVLm6ZethtbJGh1KY=", "exportNames": ["*"]}}, {"name": "./PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 103}, "end": {"line": 4, "column": 64, "index": 167}}], "key": "6AA7RQghlqlrd3hVWNoLh/rI420=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.updateLayoutAnimations = void 0;\n  var _core = require(_dependencyMap[0], \"./core.js\");\n  var _PlatformChecker = require(_dependencyMap[1], \"./PlatformChecker.js\");\n  function createUpdateManager() {\n    const animations = [];\n    // When a stack is rerendered we reconfigure all the shared elements.\n    // To do that we want them to appear in our batch in the correct order,\n    // so we defer some of the updates to appear at the end of the batch.\n    const deferredAnimations = [];\n    return {\n      update(batchItem, isUnmounting) {\n        if (isUnmounting) {\n          deferredAnimations.push(batchItem);\n        } else {\n          animations.push(batchItem);\n        }\n        if (animations.length + deferredAnimations.length === 1) {\n          (0, _PlatformChecker.isFabric)() ? this.flush() : setImmediate(this.flush);\n        }\n      },\n      flush() {\n        (0, _core.configureLayoutAnimationBatch)(animations.concat(deferredAnimations));\n        animations.length = 0;\n        deferredAnimations.length = 0;\n      }\n    };\n  }\n\n  /**\n   * Lets you update the current configuration of the layout animation or shared\n   * element transition for a given component. Configurations are batched and\n   * applied at the end of the current execution block, right before sending the\n   * response back to native.\n   *\n   * @param viewTag - The tag of the component you'd like to configure.\n   * @param type - The type of the animation you'd like to configure -\n   *   {@link LayoutAnimationType}.\n   * @param config - The animation configuration - {@link LayoutAnimationFunction},\n   *   {@link SharedTransitionAnimationsFunction}, {@link ProgressAnimationCallback}\n   *   or {@link Keyframe}. Passing `undefined` will remove the animation.\n   * @param sharedTransitionTag - The tag of the shared element transition you'd\n   *   like to configure. Passing `undefined` will remove the transition.\n   * @param isUnmounting - Determines whether the configuration should be included\n   *   at the end of the batch, after all the non-deferred configurations (even\n   *   those that were updated later). This is used to retain the correct ordering\n   *   of shared elements. Defaults to `false`.\n   */\n  let updateLayoutAnimations = exports.updateLayoutAnimations = void 0;\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    exports.updateLayoutAnimations = updateLayoutAnimations = () => {\n      // no-op\n    };\n  } else {\n    const updateLayoutAnimationsManager = createUpdateManager();\n    exports.updateLayoutAnimations = updateLayoutAnimations = (viewTag, type, config, sharedTransitionTag, isUnmounting) => updateLayoutAnimationsManager.update({\n      viewTag,\n      type,\n      config: config ? (0, _core.makeShareableCloneRecursive)(config) : undefined,\n      sharedTransitionTag\n    }, isUnmounting);\n  }\n});", "lineCount": 68, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "updateLayoutAnimations"], [7, 32, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_PlatformChecker"], [9, 22, 4, 0], [9, 25, 4, 0, "require"], [9, 32, 4, 0], [9, 33, 4, 0, "_dependencyMap"], [9, 47, 4, 0], [10, 2, 5, 0], [10, 11, 5, 9, "createUpdateManager"], [10, 30, 5, 28, "createUpdateManager"], [10, 31, 5, 28], [10, 33, 5, 31], [11, 4, 6, 2], [11, 10, 6, 8, "animations"], [11, 20, 6, 18], [11, 23, 6, 21], [11, 25, 6, 23], [12, 4, 7, 2], [13, 4, 8, 2], [14, 4, 9, 2], [15, 4, 10, 2], [15, 10, 10, 8, "deferredAnimations"], [15, 28, 10, 26], [15, 31, 10, 29], [15, 33, 10, 31], [16, 4, 11, 2], [16, 11, 11, 9], [17, 6, 12, 4, "update"], [17, 12, 12, 10, "update"], [17, 13, 12, 11, "batchItem"], [17, 22, 12, 20], [17, 24, 12, 22, "isUnmounting"], [17, 36, 12, 34], [17, 38, 12, 36], [18, 8, 13, 6], [18, 12, 13, 10, "isUnmounting"], [18, 24, 13, 22], [18, 26, 13, 24], [19, 10, 14, 8, "deferredAnimations"], [19, 28, 14, 26], [19, 29, 14, 27, "push"], [19, 33, 14, 31], [19, 34, 14, 32, "batchItem"], [19, 43, 14, 41], [19, 44, 14, 42], [20, 8, 15, 6], [20, 9, 15, 7], [20, 15, 15, 13], [21, 10, 16, 8, "animations"], [21, 20, 16, 18], [21, 21, 16, 19, "push"], [21, 25, 16, 23], [21, 26, 16, 24, "batchItem"], [21, 35, 16, 33], [21, 36, 16, 34], [22, 8, 17, 6], [23, 8, 18, 6], [23, 12, 18, 10, "animations"], [23, 22, 18, 20], [23, 23, 18, 21, "length"], [23, 29, 18, 27], [23, 32, 18, 30, "deferredAnimations"], [23, 50, 18, 48], [23, 51, 18, 49, "length"], [23, 57, 18, 55], [23, 62, 18, 60], [23, 63, 18, 61], [23, 65, 18, 63], [24, 10, 19, 8], [24, 14, 19, 8, "isF<PERSON><PERSON>"], [24, 39, 19, 16], [24, 41, 19, 17], [24, 42, 19, 18], [24, 45, 19, 21], [24, 49, 19, 25], [24, 50, 19, 26, "flush"], [24, 55, 19, 31], [24, 56, 19, 32], [24, 57, 19, 33], [24, 60, 19, 36, "setImmediate"], [24, 72, 19, 48], [24, 73, 19, 49], [24, 77, 19, 53], [24, 78, 19, 54, "flush"], [24, 83, 19, 59], [24, 84, 19, 60], [25, 8, 20, 6], [26, 6, 21, 4], [26, 7, 21, 5], [27, 6, 22, 4, "flush"], [27, 11, 22, 9, "flush"], [27, 12, 22, 9], [27, 14, 22, 12], [28, 8, 23, 6], [28, 12, 23, 6, "configureLayoutAnimationBatch"], [28, 47, 23, 35], [28, 49, 23, 36, "animations"], [28, 59, 23, 46], [28, 60, 23, 47, "concat"], [28, 66, 23, 53], [28, 67, 23, 54, "deferredAnimations"], [28, 85, 23, 72], [28, 86, 23, 73], [28, 87, 23, 74], [29, 8, 24, 6, "animations"], [29, 18, 24, 16], [29, 19, 24, 17, "length"], [29, 25, 24, 23], [29, 28, 24, 26], [29, 29, 24, 27], [30, 8, 25, 6, "deferredAnimations"], [30, 26, 25, 24], [30, 27, 25, 25, "length"], [30, 33, 25, 31], [30, 36, 25, 34], [30, 37, 25, 35], [31, 6, 26, 4], [32, 4, 27, 2], [32, 5, 27, 3], [33, 2, 28, 0], [35, 2, 30, 0], [36, 0, 31, 0], [37, 0, 32, 0], [38, 0, 33, 0], [39, 0, 34, 0], [40, 0, 35, 0], [41, 0, 36, 0], [42, 0, 37, 0], [43, 0, 38, 0], [44, 0, 39, 0], [45, 0, 40, 0], [46, 0, 41, 0], [47, 0, 42, 0], [48, 0, 43, 0], [49, 0, 44, 0], [50, 0, 45, 0], [51, 0, 46, 0], [52, 0, 47, 0], [53, 0, 48, 0], [54, 2, 49, 7], [54, 6, 49, 11, "updateLayoutAnimations"], [54, 28, 49, 33], [54, 31, 49, 33, "exports"], [54, 38, 49, 33], [54, 39, 49, 33, "updateLayoutAnimations"], [54, 61, 49, 33], [55, 2, 50, 0], [55, 6, 50, 4], [55, 10, 50, 4, "shouldBeUseWeb"], [55, 41, 50, 18], [55, 43, 50, 19], [55, 44, 50, 20], [55, 46, 50, 22], [56, 4, 51, 2, "exports"], [56, 11, 51, 2], [56, 12, 51, 2, "updateLayoutAnimations"], [56, 34, 51, 2], [56, 37, 51, 2, "updateLayoutAnimations"], [56, 59, 51, 24], [56, 62, 51, 27, "updateLayoutAnimations"], [56, 63, 51, 27], [56, 68, 51, 33], [57, 6, 52, 4], [58, 4, 52, 4], [58, 5, 53, 3], [59, 2, 54, 0], [59, 3, 54, 1], [59, 9, 54, 7], [60, 4, 55, 2], [60, 10, 55, 8, "updateLayoutAnimationsManager"], [60, 39, 55, 37], [60, 42, 55, 40, "createUpdateManager"], [60, 61, 55, 59], [60, 62, 55, 60], [60, 63, 55, 61], [61, 4, 56, 2, "exports"], [61, 11, 56, 2], [61, 12, 56, 2, "updateLayoutAnimations"], [61, 34, 56, 2], [61, 37, 56, 2, "updateLayoutAnimations"], [61, 59, 56, 24], [61, 62, 56, 27, "updateLayoutAnimations"], [61, 63, 56, 28, "viewTag"], [61, 70, 56, 35], [61, 72, 56, 37, "type"], [61, 76, 56, 41], [61, 78, 56, 43, "config"], [61, 84, 56, 49], [61, 86, 56, 51, "sharedTransitionTag"], [61, 105, 56, 70], [61, 107, 56, 72, "isUnmounting"], [61, 119, 56, 84], [61, 124, 56, 89, "updateLayoutAnimationsManager"], [61, 153, 56, 118], [61, 154, 56, 119, "update"], [61, 160, 56, 125], [61, 161, 56, 126], [62, 6, 57, 4, "viewTag"], [62, 13, 57, 11], [63, 6, 58, 4, "type"], [63, 10, 58, 8], [64, 6, 59, 4, "config"], [64, 12, 59, 10], [64, 14, 59, 12, "config"], [64, 20, 59, 18], [64, 23, 59, 21], [64, 27, 59, 21, "makeShareableCloneRecursive"], [64, 60, 59, 48], [64, 62, 59, 49, "config"], [64, 68, 59, 55], [64, 69, 59, 56], [64, 72, 59, 59, "undefined"], [64, 81, 59, 68], [65, 6, 60, 4, "sharedTransitionTag"], [66, 4, 61, 2], [66, 5, 61, 3], [66, 7, 61, 5, "isUnmounting"], [66, 19, 61, 17], [66, 20, 61, 18], [67, 2, 62, 0], [68, 0, 62, 1], [68, 3]], "functionMap": {"names": ["<global>", "createUpdateManager", "update", "flush", "updateLayoutAnimations"], "mappings": "AAA;ACI;ICO;KDS;IEC;KFI;CDE;2BIuB;GJE;2BIG;kBJK"}}, "type": "js/module"}]}