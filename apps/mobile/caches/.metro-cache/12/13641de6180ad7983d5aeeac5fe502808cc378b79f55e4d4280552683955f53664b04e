{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../NativeModules/specs/NativeDialogManagerAndroid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 91}}], "key": "jeQj868OYCvokrB4FXLZLUpO7ek=", "exportNames": ["*"]}}, {"name": "./NativePermissionsAndroid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 66}}], "key": "AufAnsI6a6Gj1f6yByRXJreqTw4=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 17}, "end": {"line": 15, "column": 49}}], "key": "4a+BOpVYP2jviYQTOV6MRNF0tRc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _NativeDialogManagerAndroid = _interopRequireDefault(require(_dependencyMap[4], \"../NativeModules/specs/NativeDialogManagerAndroid\"));\n  var _NativePermissionsAndroid = _interopRequireDefault(require(_dependencyMap[5], \"./NativePermissionsAndroid\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[6], \"invariant\"));\n  var Platform = require(_dependencyMap[7], \"../Utilities/Platform\").default;\n  var PERMISSION_REQUEST_RESULT = Object.freeze({\n    GRANTED: 'granted',\n    DENIED: 'denied',\n    NEVER_ASK_AGAIN: 'never_ask_again'\n  });\n  var PERMISSIONS = Object.freeze({\n    READ_CALENDAR: 'android.permission.READ_CALENDAR',\n    WRITE_CALENDAR: 'android.permission.WRITE_CALENDAR',\n    CAMERA: 'android.permission.CAMERA',\n    READ_CONTACTS: 'android.permission.READ_CONTACTS',\n    WRITE_CONTACTS: 'android.permission.WRITE_CONTACTS',\n    GET_ACCOUNTS: 'android.permission.GET_ACCOUNTS',\n    ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',\n    ACCESS_COARSE_LOCATION: 'android.permission.ACCESS_COARSE_LOCATION',\n    ACCESS_BACKGROUND_LOCATION: 'android.permission.ACCESS_BACKGROUND_LOCATION',\n    RECORD_AUDIO: 'android.permission.RECORD_AUDIO',\n    READ_PHONE_STATE: 'android.permission.READ_PHONE_STATE',\n    CALL_PHONE: 'android.permission.CALL_PHONE',\n    READ_CALL_LOG: 'android.permission.READ_CALL_LOG',\n    WRITE_CALL_LOG: 'android.permission.WRITE_CALL_LOG',\n    ADD_VOICEMAIL: 'com.android.voicemail.permission.ADD_VOICEMAIL',\n    READ_VOICEMAIL: 'com.android.voicemail.permission.READ_VOICEMAIL',\n    WRITE_VOICEMAIL: 'com.android.voicemail.permission.WRITE_VOICEMAIL',\n    USE_SIP: 'android.permission.USE_SIP',\n    PROCESS_OUTGOING_CALLS: 'android.permission.PROCESS_OUTGOING_CALLS',\n    BODY_SENSORS: 'android.permission.BODY_SENSORS',\n    BODY_SENSORS_BACKGROUND: 'android.permission.BODY_SENSORS_BACKGROUND',\n    SEND_SMS: 'android.permission.SEND_SMS',\n    RECEIVE_SMS: 'android.permission.RECEIVE_SMS',\n    READ_SMS: 'android.permission.READ_SMS',\n    RECEIVE_WAP_PUSH: 'android.permission.RECEIVE_WAP_PUSH',\n    RECEIVE_MMS: 'android.permission.RECEIVE_MMS',\n    READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',\n    READ_MEDIA_IMAGES: 'android.permission.READ_MEDIA_IMAGES',\n    READ_MEDIA_VIDEO: 'android.permission.READ_MEDIA_VIDEO',\n    READ_MEDIA_AUDIO: 'android.permission.READ_MEDIA_AUDIO',\n    READ_MEDIA_VISUAL_USER_SELECTED: 'android.permission.READ_MEDIA_VISUAL_USER_SELECTED',\n    WRITE_EXTERNAL_STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',\n    BLUETOOTH_CONNECT: 'android.permission.BLUETOOTH_CONNECT',\n    BLUETOOTH_SCAN: 'android.permission.BLUETOOTH_SCAN',\n    BLUETOOTH_ADVERTISE: 'android.permission.BLUETOOTH_ADVERTISE',\n    ACCESS_MEDIA_LOCATION: 'android.permission.ACCESS_MEDIA_LOCATION',\n    ACCEPT_HANDOVER: 'android.permission.ACCEPT_HANDOVER',\n    ACTIVITY_RECOGNITION: 'android.permission.ACTIVITY_RECOGNITION',\n    ANSWER_PHONE_CALLS: 'android.permission.ANSWER_PHONE_CALLS',\n    READ_PHONE_NUMBERS: 'android.permission.READ_PHONE_NUMBERS',\n    UWB_RANGING: 'android.permission.UWB_RANGING',\n    POST_NOTIFICATIONS: 'android.permission.POST_NOTIFICATIONS',\n    NEARBY_WIFI_DEVICES: 'android.permission.NEARBY_WIFI_DEVICES'\n  });\n  var PermissionsAndroid = /*#__PURE__*/function () {\n    function PermissionsAndroid() {\n      (0, _classCallCheck2.default)(this, PermissionsAndroid);\n      this.PERMISSIONS = PERMISSIONS;\n      this.RESULTS = PERMISSION_REQUEST_RESULT;\n    }\n    return (0, _createClass2.default)(PermissionsAndroid, [{\n      key: \"checkPermission\",\n      value: function checkPermission(permission) {\n        console.warn('\"PermissionsAndroid.checkPermission\" is deprecated. Use \"PermissionsAndroid.check\" instead');\n        if (Platform.OS !== 'android') {\n          console.warn('\"PermissionsAndroid\" module works only for Android platform.');\n          return Promise.resolve(false);\n        }\n        (0, _invariant.default)(_NativePermissionsAndroid.default, 'PermissionsAndroid is not installed correctly.');\n        return _NativePermissionsAndroid.default.checkPermission(permission);\n      }\n    }, {\n      key: \"check\",\n      value: function check(permission) {\n        if (Platform.OS !== 'android') {\n          console.warn('\"PermissionsAndroid\" module works only for Android platform.');\n          return Promise.resolve(false);\n        }\n        (0, _invariant.default)(_NativePermissionsAndroid.default, 'PermissionsAndroid is not installed correctly.');\n        return _NativePermissionsAndroid.default.checkPermission(permission);\n      }\n    }, {\n      key: \"requestPermission\",\n      value: function () {\n        var _requestPermission = (0, _asyncToGenerator2.default)(function* (permission, rationale) {\n          console.warn('\"PermissionsAndroid.requestPermission\" is deprecated. Use \"PermissionsAndroid.request\" instead');\n          if (Platform.OS !== 'android') {\n            console.warn('\"PermissionsAndroid\" module works only for Android platform.');\n            return Promise.resolve(false);\n          }\n          var response = yield this.request(permission, rationale);\n          return response === this.RESULTS.GRANTED;\n        });\n        function requestPermission(_x, _x2) {\n          return _requestPermission.apply(this, arguments);\n        }\n        return requestPermission;\n      }()\n    }, {\n      key: \"request\",\n      value: function () {\n        var _request = (0, _asyncToGenerator2.default)(function* (permission, rationale) {\n          if (Platform.OS !== 'android') {\n            console.warn('\"PermissionsAndroid\" module works only for Android platform.');\n            return Promise.resolve(this.RESULTS.DENIED);\n          }\n          (0, _invariant.default)(_NativePermissionsAndroid.default, 'PermissionsAndroid is not installed correctly.');\n          if (rationale) {\n            var shouldShowRationale = yield _NativePermissionsAndroid.default.shouldShowRequestPermissionRationale(permission);\n            if (shouldShowRationale && !!_NativeDialogManagerAndroid.default) {\n              return new Promise((resolve, reject) => {\n                var options = {\n                  ...rationale\n                };\n                _NativeDialogManagerAndroid.default.showAlert(options, () => reject(new Error('Error showing rationale')), () => resolve(_NativePermissionsAndroid.default.requestPermission(permission)));\n              });\n            }\n          }\n          return _NativePermissionsAndroid.default.requestPermission(permission);\n        });\n        function request(_x3, _x4) {\n          return _request.apply(this, arguments);\n        }\n        return request;\n      }()\n    }, {\n      key: \"requestMultiple\",\n      value: function requestMultiple(permissions) {\n        if (Platform.OS !== 'android') {\n          console.warn('\"PermissionsAndroid\" module works only for Android platform.');\n          return Promise.resolve({});\n        }\n        (0, _invariant.default)(_NativePermissionsAndroid.default, 'PermissionsAndroid is not installed correctly.');\n        return _NativePermissionsAndroid.default.requestMultiplePermissions(permissions);\n      }\n    }]);\n  }();\n  var PermissionsAndroidInstance = new PermissionsAndroid();\n  var _default = exports.default = PermissionsAndroidInstance;\n});", "lineCount": 149, "map": [[10, 2, 11, 0], [10, 6, 11, 0, "_NativeDialogManagerAndroid"], [10, 33, 11, 0], [10, 36, 11, 0, "_interopRequireDefault"], [10, 58, 11, 0], [10, 59, 11, 0, "require"], [10, 66, 11, 0], [10, 67, 11, 0, "_dependencyMap"], [10, 81, 11, 0], [11, 2, 12, 0], [11, 6, 12, 0, "_NativePermissionsAndroid"], [11, 31, 12, 0], [11, 34, 12, 0, "_interopRequireDefault"], [11, 56, 12, 0], [11, 57, 12, 0, "require"], [11, 64, 12, 0], [11, 65, 12, 0, "_dependencyMap"], [11, 79, 12, 0], [12, 2, 13, 0], [12, 6, 13, 0, "_invariant"], [12, 16, 13, 0], [12, 19, 13, 0, "_interopRequireDefault"], [12, 41, 13, 0], [12, 42, 13, 0, "require"], [12, 49, 13, 0], [12, 50, 13, 0, "_dependencyMap"], [12, 64, 13, 0], [13, 2, 15, 0], [13, 6, 15, 6, "Platform"], [13, 14, 15, 14], [13, 17, 15, 17, "require"], [13, 24, 15, 24], [13, 25, 15, 24, "_dependencyMap"], [13, 39, 15, 24], [13, 67, 15, 48], [13, 68, 15, 49], [13, 69, 15, 50, "default"], [13, 76, 15, 57], [14, 2, 26, 0], [14, 6, 26, 6, "PERMISSION_REQUEST_RESULT"], [14, 31, 26, 31], [14, 34, 26, 34, "Object"], [14, 40, 26, 40], [14, 41, 26, 41, "freeze"], [14, 47, 26, 47], [14, 48, 26, 48], [15, 4, 27, 2, "GRANTED"], [15, 11, 27, 9], [15, 13, 27, 11], [15, 22, 27, 20], [16, 4, 28, 2, "DENIED"], [16, 10, 28, 8], [16, 12, 28, 10], [16, 20, 28, 18], [17, 4, 29, 2, "NEVER_ASK_AGAIN"], [17, 19, 29, 17], [17, 21, 29, 19], [18, 2, 30, 0], [18, 3, 30, 1], [18, 4, 30, 2], [19, 2, 81, 0], [19, 6, 81, 6, "PERMISSIONS"], [19, 17, 81, 17], [19, 20, 81, 20, "Object"], [19, 26, 81, 26], [19, 27, 81, 27, "freeze"], [19, 33, 81, 33], [19, 34, 81, 34], [20, 4, 82, 2, "READ_CALENDAR"], [20, 17, 82, 15], [20, 19, 82, 17], [20, 53, 82, 51], [21, 4, 83, 2, "WRITE_CALENDAR"], [21, 18, 83, 16], [21, 20, 83, 18], [21, 55, 83, 53], [22, 4, 84, 2, "CAMERA"], [22, 10, 84, 8], [22, 12, 84, 10], [22, 39, 84, 37], [23, 4, 85, 2, "READ_CONTACTS"], [23, 17, 85, 15], [23, 19, 85, 17], [23, 53, 85, 51], [24, 4, 86, 2, "WRITE_CONTACTS"], [24, 18, 86, 16], [24, 20, 86, 18], [24, 55, 86, 53], [25, 4, 87, 2, "GET_ACCOUNTS"], [25, 16, 87, 14], [25, 18, 87, 16], [25, 51, 87, 49], [26, 4, 88, 2, "ACCESS_FINE_LOCATION"], [26, 24, 88, 22], [26, 26, 88, 24], [26, 67, 88, 65], [27, 4, 89, 2, "ACCESS_COARSE_LOCATION"], [27, 26, 89, 24], [27, 28, 89, 26], [27, 71, 89, 69], [28, 4, 90, 2, "ACCESS_BACKGROUND_LOCATION"], [28, 30, 90, 28], [28, 32, 90, 30], [28, 79, 90, 77], [29, 4, 91, 2, "RECORD_AUDIO"], [29, 16, 91, 14], [29, 18, 91, 16], [29, 51, 91, 49], [30, 4, 92, 2, "READ_PHONE_STATE"], [30, 20, 92, 18], [30, 22, 92, 20], [30, 59, 92, 57], [31, 4, 93, 2, "CALL_PHONE"], [31, 14, 93, 12], [31, 16, 93, 14], [31, 47, 93, 45], [32, 4, 94, 2, "READ_CALL_LOG"], [32, 17, 94, 15], [32, 19, 94, 17], [32, 53, 94, 51], [33, 4, 95, 2, "WRITE_CALL_LOG"], [33, 18, 95, 16], [33, 20, 95, 18], [33, 55, 95, 53], [34, 4, 96, 2, "ADD_VOICEMAIL"], [34, 17, 96, 15], [34, 19, 96, 17], [34, 67, 96, 65], [35, 4, 97, 2, "READ_VOICEMAIL"], [35, 18, 97, 16], [35, 20, 97, 18], [35, 69, 97, 67], [36, 4, 98, 2, "WRITE_VOICEMAIL"], [36, 19, 98, 17], [36, 21, 98, 19], [36, 71, 98, 69], [37, 4, 99, 2, "USE_SIP"], [37, 11, 99, 9], [37, 13, 99, 11], [37, 41, 99, 39], [38, 4, 100, 2, "PROCESS_OUTGOING_CALLS"], [38, 26, 100, 24], [38, 28, 100, 26], [38, 71, 100, 69], [39, 4, 101, 2, "BODY_SENSORS"], [39, 16, 101, 14], [39, 18, 101, 16], [39, 51, 101, 49], [40, 4, 102, 2, "BODY_SENSORS_BACKGROUND"], [40, 27, 102, 25], [40, 29, 102, 27], [40, 73, 102, 71], [41, 4, 103, 2, "SEND_SMS"], [41, 12, 103, 10], [41, 14, 103, 12], [41, 43, 103, 41], [42, 4, 104, 2, "RECEIVE_SMS"], [42, 15, 104, 13], [42, 17, 104, 15], [42, 49, 104, 47], [43, 4, 105, 2, "READ_SMS"], [43, 12, 105, 10], [43, 14, 105, 12], [43, 43, 105, 41], [44, 4, 106, 2, "RECEIVE_WAP_PUSH"], [44, 20, 106, 18], [44, 22, 106, 20], [44, 59, 106, 57], [45, 4, 107, 2, "RECEIVE_MMS"], [45, 15, 107, 13], [45, 17, 107, 15], [45, 49, 107, 47], [46, 4, 108, 2, "READ_EXTERNAL_STORAGE"], [46, 25, 108, 23], [46, 27, 108, 25], [46, 69, 108, 67], [47, 4, 109, 2, "READ_MEDIA_IMAGES"], [47, 21, 109, 19], [47, 23, 109, 21], [47, 61, 109, 59], [48, 4, 110, 2, "READ_MEDIA_VIDEO"], [48, 20, 110, 18], [48, 22, 110, 20], [48, 59, 110, 57], [49, 4, 111, 2, "READ_MEDIA_AUDIO"], [49, 20, 111, 18], [49, 22, 111, 20], [49, 59, 111, 57], [50, 4, 112, 2, "READ_MEDIA_VISUAL_USER_SELECTED"], [50, 35, 112, 33], [50, 37, 113, 4], [50, 89, 113, 56], [51, 4, 114, 2, "WRITE_EXTERNAL_STORAGE"], [51, 26, 114, 24], [51, 28, 114, 26], [51, 71, 114, 69], [52, 4, 115, 2, "BLUETOOTH_CONNECT"], [52, 21, 115, 19], [52, 23, 115, 21], [52, 61, 115, 59], [53, 4, 116, 2, "BLUETOOTH_SCAN"], [53, 18, 116, 16], [53, 20, 116, 18], [53, 55, 116, 53], [54, 4, 117, 2, "BLUETOOTH_ADVERTISE"], [54, 23, 117, 21], [54, 25, 117, 23], [54, 65, 117, 63], [55, 4, 118, 2, "ACCESS_MEDIA_LOCATION"], [55, 25, 118, 23], [55, 27, 118, 25], [55, 69, 118, 67], [56, 4, 119, 2, "ACCEPT_HANDOVER"], [56, 19, 119, 17], [56, 21, 119, 19], [56, 57, 119, 55], [57, 4, 120, 2, "ACTIVITY_RECOGNITION"], [57, 24, 120, 22], [57, 26, 120, 24], [57, 67, 120, 65], [58, 4, 121, 2, "ANSWER_PHONE_CALLS"], [58, 22, 121, 20], [58, 24, 121, 22], [58, 63, 121, 61], [59, 4, 122, 2, "READ_PHONE_NUMBERS"], [59, 22, 122, 20], [59, 24, 122, 22], [59, 63, 122, 61], [60, 4, 123, 2, "UWB_RANGING"], [60, 15, 123, 13], [60, 17, 123, 15], [60, 49, 123, 47], [61, 4, 124, 2, "POST_NOTIFICATIONS"], [61, 22, 124, 20], [61, 24, 124, 22], [61, 63, 124, 61], [62, 4, 125, 2, "NEARBY_WIFI_DEVICES"], [62, 23, 125, 21], [62, 25, 125, 23], [63, 2, 126, 0], [63, 3, 126, 1], [63, 4, 126, 21], [64, 2, 126, 22], [64, 6, 133, 6, "PermissionsAndroid"], [64, 24, 133, 24], [65, 4, 133, 24], [65, 13, 133, 24, "PermissionsAndroid"], [65, 32, 133, 24], [66, 6, 133, 24], [66, 10, 133, 24, "_classCallCheck2"], [66, 26, 133, 24], [66, 27, 133, 24, "default"], [66, 34, 133, 24], [66, 42, 133, 24, "PermissionsAndroid"], [66, 60, 133, 24], [67, 6, 133, 24], [67, 11, 134, 2, "PERMISSIONS"], [67, 22, 134, 13], [67, 25, 134, 33, "PERMISSIONS"], [67, 36, 134, 44], [68, 6, 134, 44], [68, 11, 135, 2, "RESULTS"], [68, 18, 135, 9], [68, 21, 139, 7, "PERMISSION_REQUEST_RESULT"], [68, 46, 139, 32], [69, 4, 139, 32], [70, 4, 139, 32], [70, 15, 139, 32, "_createClass2"], [70, 28, 139, 32], [70, 29, 139, 32, "default"], [70, 36, 139, 32], [70, 38, 139, 32, "PermissionsAndroid"], [70, 56, 139, 32], [71, 6, 139, 32, "key"], [71, 9, 139, 32], [72, 6, 139, 32, "value"], [72, 11, 139, 32], [72, 13, 149, 2], [72, 22, 149, 2, "checkPermission"], [72, 37, 149, 17, "checkPermission"], [72, 38, 149, 18, "permission"], [72, 48, 149, 40], [72, 50, 149, 60], [73, 8, 150, 4, "console"], [73, 15, 150, 11], [73, 16, 150, 12, "warn"], [73, 20, 150, 16], [73, 21, 151, 6], [73, 113, 152, 4], [73, 114, 152, 5], [74, 8, 153, 4], [74, 12, 153, 8, "Platform"], [74, 20, 153, 16], [74, 21, 153, 17, "OS"], [74, 23, 153, 19], [74, 28, 153, 24], [74, 37, 153, 33], [74, 39, 153, 35], [75, 10, 154, 6, "console"], [75, 17, 154, 13], [75, 18, 154, 14, "warn"], [75, 22, 154, 18], [75, 23, 155, 8], [75, 85, 156, 6], [75, 86, 156, 7], [76, 10, 157, 6], [76, 17, 157, 13, "Promise"], [76, 24, 157, 20], [76, 25, 157, 21, "resolve"], [76, 32, 157, 28], [76, 33, 157, 29], [76, 38, 157, 34], [76, 39, 157, 35], [77, 8, 158, 4], [78, 8, 160, 4], [78, 12, 160, 4, "invariant"], [78, 30, 160, 13], [78, 32, 161, 6, "NativePermissionsAndroid"], [78, 65, 161, 30], [78, 67, 162, 6], [78, 115, 163, 4], [78, 116, 163, 5], [79, 8, 165, 4], [79, 15, 165, 11, "NativePermissionsAndroid"], [79, 48, 165, 35], [79, 49, 165, 36, "checkPermission"], [79, 64, 165, 51], [79, 65, 165, 52, "permission"], [79, 75, 165, 62], [79, 76, 165, 63], [80, 6, 166, 2], [81, 4, 166, 3], [82, 6, 166, 3, "key"], [82, 9, 166, 3], [83, 6, 166, 3, "value"], [83, 11, 166, 3], [83, 13, 174, 2], [83, 22, 174, 2, "check"], [83, 27, 174, 7, "check"], [83, 28, 174, 8, "permission"], [83, 38, 174, 30], [83, 40, 174, 50], [84, 8, 175, 4], [84, 12, 175, 8, "Platform"], [84, 20, 175, 16], [84, 21, 175, 17, "OS"], [84, 23, 175, 19], [84, 28, 175, 24], [84, 37, 175, 33], [84, 39, 175, 35], [85, 10, 176, 6, "console"], [85, 17, 176, 13], [85, 18, 176, 14, "warn"], [85, 22, 176, 18], [85, 23, 177, 8], [85, 85, 178, 6], [85, 86, 178, 7], [86, 10, 179, 6], [86, 17, 179, 13, "Promise"], [86, 24, 179, 20], [86, 25, 179, 21, "resolve"], [86, 32, 179, 28], [86, 33, 179, 29], [86, 38, 179, 34], [86, 39, 179, 35], [87, 8, 180, 4], [88, 8, 182, 4], [88, 12, 182, 4, "invariant"], [88, 30, 182, 13], [88, 32, 183, 6, "NativePermissionsAndroid"], [88, 65, 183, 30], [88, 67, 184, 6], [88, 115, 185, 4], [88, 116, 185, 5], [89, 8, 187, 4], [89, 15, 187, 11, "NativePermissionsAndroid"], [89, 48, 187, 35], [89, 49, 187, 36, "checkPermission"], [89, 64, 187, 51], [89, 65, 187, 52, "permission"], [89, 75, 187, 62], [89, 76, 187, 63], [90, 6, 188, 2], [91, 4, 188, 3], [92, 6, 188, 3, "key"], [92, 9, 188, 3], [93, 6, 188, 3, "value"], [93, 11, 188, 3], [94, 8, 188, 3], [94, 12, 188, 3, "_requestPermission"], [94, 30, 188, 3], [94, 37, 188, 3, "_asyncToGenerator2"], [94, 55, 188, 3], [94, 56, 188, 3, "default"], [94, 63, 188, 3], [94, 65, 204, 2], [94, 76, 205, 4, "permission"], [94, 86, 205, 26], [94, 88, 206, 4, "rationale"], [94, 97, 206, 25], [94, 99, 207, 22], [95, 10, 208, 4, "console"], [95, 17, 208, 11], [95, 18, 208, 12, "warn"], [95, 22, 208, 16], [95, 23, 209, 6], [95, 119, 210, 4], [95, 120, 210, 5], [96, 10, 211, 4], [96, 14, 211, 8, "Platform"], [96, 22, 211, 16], [96, 23, 211, 17, "OS"], [96, 25, 211, 19], [96, 30, 211, 24], [96, 39, 211, 33], [96, 41, 211, 35], [97, 12, 212, 6, "console"], [97, 19, 212, 13], [97, 20, 212, 14, "warn"], [97, 24, 212, 18], [97, 25, 213, 8], [97, 87, 214, 6], [97, 88, 214, 7], [98, 12, 215, 6], [98, 19, 215, 13, "Promise"], [98, 26, 215, 20], [98, 27, 215, 21, "resolve"], [98, 34, 215, 28], [98, 35, 215, 29], [98, 40, 215, 34], [98, 41, 215, 35], [99, 10, 216, 4], [100, 10, 218, 4], [100, 14, 218, 10, "response"], [100, 22, 218, 18], [100, 31, 218, 27], [100, 35, 218, 31], [100, 36, 218, 32, "request"], [100, 43, 218, 39], [100, 44, 218, 40, "permission"], [100, 54, 218, 50], [100, 56, 218, 52, "rationale"], [100, 65, 218, 61], [100, 66, 218, 62], [101, 10, 219, 4], [101, 17, 219, 11, "response"], [101, 25, 219, 19], [101, 30, 219, 24], [101, 34, 219, 28], [101, 35, 219, 29, "RESULTS"], [101, 42, 219, 36], [101, 43, 219, 37, "GRANTED"], [101, 50, 219, 44], [102, 8, 220, 2], [102, 9, 220, 3], [103, 8, 220, 3], [103, 17, 204, 8, "requestPermission"], [103, 34, 204, 25, "requestPermission"], [103, 35, 204, 25, "_x"], [103, 37, 204, 25], [103, 39, 204, 25, "_x2"], [103, 42, 204, 25], [104, 10, 204, 25], [104, 17, 204, 25, "_requestPermission"], [104, 35, 204, 25], [104, 36, 204, 25, "apply"], [104, 41, 204, 25], [104, 48, 204, 25, "arguments"], [104, 57, 204, 25], [105, 8, 204, 25], [106, 8, 204, 25], [106, 15, 204, 8, "requestPermission"], [106, 32, 204, 25], [107, 6, 204, 25], [108, 4, 204, 25], [109, 6, 204, 25, "key"], [109, 9, 204, 25], [110, 6, 204, 25, "value"], [110, 11, 204, 25], [111, 8, 204, 25], [111, 12, 204, 25, "_request"], [111, 20, 204, 25], [111, 27, 204, 25, "_asyncToGenerator2"], [111, 45, 204, 25], [111, 46, 204, 25, "default"], [111, 53, 204, 25], [111, 55, 228, 2], [111, 66, 229, 4, "permission"], [111, 76, 229, 26], [111, 78, 230, 4, "rationale"], [111, 87, 230, 25], [111, 89, 231, 31], [112, 10, 232, 4], [112, 14, 232, 8, "Platform"], [112, 22, 232, 16], [112, 23, 232, 17, "OS"], [112, 25, 232, 19], [112, 30, 232, 24], [112, 39, 232, 33], [112, 41, 232, 35], [113, 12, 233, 6, "console"], [113, 19, 233, 13], [113, 20, 233, 14, "warn"], [113, 24, 233, 18], [113, 25, 234, 8], [113, 87, 235, 6], [113, 88, 235, 7], [114, 12, 236, 6], [114, 19, 236, 13, "Promise"], [114, 26, 236, 20], [114, 27, 236, 21, "resolve"], [114, 34, 236, 28], [114, 35, 236, 29], [114, 39, 236, 33], [114, 40, 236, 34, "RESULTS"], [114, 47, 236, 41], [114, 48, 236, 42, "DENIED"], [114, 54, 236, 48], [114, 55, 236, 49], [115, 10, 237, 4], [116, 10, 239, 4], [116, 14, 239, 4, "invariant"], [116, 32, 239, 13], [116, 34, 240, 6, "NativePermissionsAndroid"], [116, 67, 240, 30], [116, 69, 241, 6], [116, 117, 242, 4], [116, 118, 242, 5], [117, 10, 244, 4], [117, 14, 244, 8, "rationale"], [117, 23, 244, 17], [117, 25, 244, 19], [118, 12, 245, 6], [118, 16, 245, 12, "shouldShowRationale"], [118, 35, 245, 31], [118, 44, 246, 14, "NativePermissionsAndroid"], [118, 77, 246, 38], [118, 78, 246, 39, "shouldShowRequestPermissionRationale"], [118, 114, 246, 75], [118, 115, 247, 10, "permission"], [118, 125, 248, 8], [118, 126, 248, 9], [119, 12, 250, 6], [119, 16, 250, 10, "shouldShowRationale"], [119, 35, 250, 29], [119, 39, 250, 33], [119, 40, 250, 34], [119, 41, 250, 35, "NativeDialogManagerAndroid"], [119, 76, 250, 61], [119, 78, 250, 63], [120, 14, 251, 8], [120, 21, 251, 15], [120, 25, 251, 19, "Promise"], [120, 32, 251, 26], [120, 33, 251, 27], [120, 34, 251, 28, "resolve"], [120, 41, 251, 35], [120, 43, 251, 37, "reject"], [120, 49, 251, 43], [120, 54, 251, 48], [121, 16, 252, 10], [121, 20, 252, 16, "options"], [121, 27, 252, 23], [121, 30, 252, 26], [122, 18, 253, 12], [122, 21, 253, 15, "rationale"], [123, 16, 254, 10], [123, 17, 254, 11], [124, 16, 255, 10, "NativeDialogManagerAndroid"], [124, 51, 255, 36], [124, 52, 255, 37, "show<PERSON><PERSON><PERSON>"], [124, 61, 255, 46], [124, 62, 260, 12, "options"], [124, 69, 260, 19], [124, 71, 261, 12], [124, 77, 261, 18, "reject"], [124, 83, 261, 24], [124, 84, 261, 25], [124, 88, 261, 29, "Error"], [124, 93, 261, 34], [124, 94, 261, 35], [124, 119, 261, 60], [124, 120, 261, 61], [124, 121, 261, 62], [124, 123, 262, 12], [124, 129, 264, 14, "resolve"], [124, 136, 264, 21], [124, 137, 264, 22, "NativePermissionsAndroid"], [124, 170, 264, 46], [124, 171, 264, 47, "requestPermission"], [124, 188, 264, 64], [124, 189, 264, 65, "permission"], [124, 199, 264, 75], [124, 200, 264, 76], [124, 201, 265, 10], [124, 202, 265, 11], [125, 14, 266, 8], [125, 15, 266, 9], [125, 16, 266, 10], [126, 12, 267, 6], [127, 10, 268, 4], [128, 10, 270, 4], [128, 17, 270, 11, "NativePermissionsAndroid"], [128, 50, 270, 35], [128, 51, 270, 36, "requestPermission"], [128, 68, 270, 53], [128, 69, 270, 54, "permission"], [128, 79, 270, 64], [128, 80, 270, 65], [129, 8, 271, 2], [129, 9, 271, 3], [130, 8, 271, 3], [130, 17, 228, 8, "request"], [130, 24, 228, 15, "request"], [130, 25, 228, 15, "_x3"], [130, 28, 228, 15], [130, 30, 228, 15, "_x4"], [130, 33, 228, 15], [131, 10, 228, 15], [131, 17, 228, 15, "_request"], [131, 25, 228, 15], [131, 26, 228, 15, "apply"], [131, 31, 228, 15], [131, 38, 228, 15, "arguments"], [131, 47, 228, 15], [132, 8, 228, 15], [133, 8, 228, 15], [133, 15, 228, 8, "request"], [133, 22, 228, 15], [134, 6, 228, 15], [135, 4, 228, 15], [136, 6, 228, 15, "key"], [136, 9, 228, 15], [137, 6, 228, 15, "value"], [137, 11, 228, 15], [137, 13, 280, 2], [137, 22, 280, 2, "requestMultiple"], [137, 37, 280, 17, "requestMultiple"], [137, 38, 281, 4, "permissions"], [137, 49, 281, 34], [137, 51, 282, 64], [138, 8, 283, 4], [138, 12, 283, 8, "Platform"], [138, 20, 283, 16], [138, 21, 283, 17, "OS"], [138, 23, 283, 19], [138, 28, 283, 24], [138, 37, 283, 33], [138, 39, 283, 35], [139, 10, 284, 6, "console"], [139, 17, 284, 13], [139, 18, 284, 14, "warn"], [139, 22, 284, 18], [139, 23, 285, 8], [139, 85, 286, 6], [139, 86, 286, 7], [140, 10, 287, 6], [140, 17, 287, 13, "Promise"], [140, 24, 287, 20], [140, 25, 287, 21, "resolve"], [140, 32, 287, 28], [140, 33, 287, 29], [140, 34, 287, 30], [140, 35, 287, 31], [140, 36, 287, 32], [141, 8, 288, 4], [142, 8, 290, 4], [142, 12, 290, 4, "invariant"], [142, 30, 290, 13], [142, 32, 291, 6, "NativePermissionsAndroid"], [142, 65, 291, 30], [142, 67, 292, 6], [142, 115, 293, 4], [142, 116, 293, 5], [143, 8, 296, 4], [143, 15, 296, 11, "NativePermissionsAndroid"], [143, 48, 296, 35], [143, 49, 296, 36, "requestMultiplePermissions"], [143, 75, 296, 62], [143, 76, 296, 63, "permissions"], [143, 87, 296, 74], [143, 88, 296, 75], [144, 6, 297, 2], [145, 4, 297, 3], [146, 2, 297, 3], [147, 2, 300, 0], [147, 6, 300, 6, "PermissionsAndroidInstance"], [147, 32, 300, 52], [147, 35, 300, 55], [147, 39, 300, 59, "PermissionsAndroid"], [147, 57, 300, 77], [147, 58, 300, 78], [147, 59, 300, 79], [148, 2, 300, 80], [148, 6, 300, 80, "_default"], [148, 14, 300, 80], [148, 17, 300, 80, "exports"], [148, 24, 300, 80], [148, 25, 300, 80, "default"], [148, 32, 300, 80], [148, 35, 301, 15, "PermissionsAndroidInstance"], [148, 61, 301, 41], [149, 0, 301, 41], [149, 3]], "functionMap": {"names": ["<global>", "PermissionsAndroid", "checkPermission", "check", "requestPermission", "request", "Promise$argument_0", "NativeDialogManagerAndroid.showAlert$argument_1", "NativeDialogManagerAndroid.showAlert$argument_2", "requestMultiple"], "mappings": "AAA;ACoI;ECgB;GDiB;EEQ;GFc;EGgB;GHgB;EIQ;2BCuB;YCU,kDD;YEC;6EFE;SDE;GJK;EQS;GRiB;CDC"}}, "type": "js/module"}]}