{"dependencies": [{"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 56, "index": 71}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rigidDecay = void 0;\n  var _utils = require(_dependencyMap[0], \"./utils.js\");\n  const _worklet_2807821538137_init_data = {\n    code: \"function rigidDecay_reactNativeReanimated_rigidDecayJs1(animation,now,config){const{SLOPE_FACTOR,VELOCITY_EPS}=this.__closure;const{lastTimestamp:lastTimestamp,startTimestamp:startTimestamp,initialVelocity:initialVelocity,current:current,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);const v=velocity*Math.exp(-(1-config.deceleration)*(now-startTimestamp)*SLOPE_FACTOR);animation.current=current+v*config.velocityFactor*deltaTime/1000;animation.velocity=v;animation.lastTimestamp=now;if(config.clamp){if(initialVelocity<0&&animation.current<=config.clamp[0]){animation.current=config.clamp[0];return true;}else if(initialVelocity>0&&animation.current>=config.clamp[1]){animation.current=config.clamp[1];return true;}}return Math.abs(v)<VELOCITY_EPS;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/rigidDecay.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"rigidDecay_reactNativeReanimated_rigidDecayJs1\\\",\\\"animation\\\",\\\"now\\\",\\\"config\\\",\\\"SLOPE_FACTOR\\\",\\\"VELOCITY_EPS\\\",\\\"__closure\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"initialVelocity\\\",\\\"current\\\",\\\"velocity\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"v\\\",\\\"exp\\\",\\\"deceleration\\\",\\\"velocityFactor\\\",\\\"clamp\\\",\\\"abs\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/decay/rigidDecay.js\\\"],\\\"mappings\\\":\\\"AAGO,SAAAA,8CAA4CA,CAAAC,SAAA,CAAAC,GAAA,CAAAC,MAAA,QAAAC,YAAA,CAAAC,YAAA,OAAAC,SAAA,CAGjD,KAAM,CACJC,aAAa,CAAbA,aAAa,CACbC,cAAc,CAAdA,cAAc,CACdC,eAAe,CAAfA,eAAe,CACfC,OAAO,CAAPA,OAAO,CACPC,QAAA,CAAAA,QACF,CAAC,CAAGV,SAAS,CACb,KAAM,CAAAW,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACZ,GAAG,CAAGK,aAAa,CAAE,EAAE,CAAC,CACnD,KAAM,CAAAQ,CAAC,CAAGJ,QAAQ,CAAGE,IAAI,CAACG,GAAG,CAAC,EAAE,CAAC,CAAGb,MAAM,CAACc,YAAY,CAAC,EAAIf,GAAG,CAAGM,cAAc,CAAC,CAAGJ,YAAY,CAAC,CACjGH,SAAS,CAACS,OAAO,CAAGA,OAAO,CAAGK,CAAC,CAAGZ,MAAM,CAACe,cAAc,CAAGN,SAAS,CAAG,IAAI,CAC1EX,SAAS,CAACU,QAAQ,CAAGI,CAAC,CACtBd,SAAS,CAACM,aAAa,CAAGL,GAAG,CAC7B,GAAIC,MAAM,CAACgB,KAAK,CAAE,CAChB,GAAIV,eAAe,CAAG,CAAC,EAAIR,SAAS,CAACS,OAAO,EAAIP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAE,CAC/DlB,SAAS,CAACS,OAAO,CAAGP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CACnC,MAAO,KAAI,CACb,CAAC,IAAM,IAAIV,eAAe,CAAG,CAAC,EAAIR,SAAS,CAACS,OAAO,EAAIP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAE,CACtElB,SAAS,CAACS,OAAO,CAAGP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CACnC,MAAO,KAAI,CACb,CACF,CACA,MAAO,CAAAN,IAAI,CAACO,GAAG,CAACL,CAAC,CAAC,CAAGV,YAAY,CACnC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rigidDecay = exports.rigidDecay = function () {\n    const _e = [new global.Error(), -3, -27];\n    const rigidDecay = function (animation, now, config) {\n      const {\n        lastTimestamp,\n        startTimestamp,\n        initialVelocity,\n        current,\n        velocity\n      } = animation;\n      const deltaTime = Math.min(now - lastTimestamp, 64);\n      const v = velocity * Math.exp(-(1 - config.deceleration) * (now - startTimestamp) * _utils.SLOPE_FACTOR);\n      animation.current = current + v * config.velocityFactor * deltaTime / 1000;\n      animation.velocity = v;\n      animation.lastTimestamp = now;\n      if (config.clamp) {\n        if (initialVelocity < 0 && animation.current <= config.clamp[0]) {\n          animation.current = config.clamp[0];\n          return true;\n        } else if (initialVelocity > 0 && animation.current >= config.clamp[1]) {\n          animation.current = config.clamp[1];\n          return true;\n        }\n      }\n      return Math.abs(v) < _utils.VELOCITY_EPS;\n    };\n    rigidDecay.__closure = {\n      SLOPE_FACTOR: _utils.SLOPE_FACTOR,\n      VELOCITY_EPS: _utils.VELOCITY_EPS\n    };\n    rigidDecay.__workletHash = 2807821538137;\n    rigidDecay.__initData = _worklet_2807821538137_init_data;\n    rigidDecay.__stackDetails = _e;\n    return rigidDecay;\n  }();\n});", "lineCount": 50, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "rigidDecay"], [7, 20, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_utils"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 3, 56], [9, 8, 3, 56, "_worklet_2807821538137_init_data"], [9, 40, 3, 56], [10, 4, 3, 56, "code"], [10, 8, 3, 56], [11, 4, 3, 56, "location"], [11, 12, 3, 56], [12, 4, 3, 56, "sourceMap"], [12, 13, 3, 56], [13, 4, 3, 56, "version"], [13, 11, 3, 56], [14, 2, 3, 56], [15, 2, 3, 56], [15, 8, 3, 56, "rigidDecay"], [15, 18, 3, 56], [15, 21, 3, 56, "exports"], [15, 28, 3, 56], [15, 29, 3, 56, "rigidDecay"], [15, 39, 3, 56], [15, 42, 4, 7], [16, 4, 4, 7], [16, 10, 4, 7, "_e"], [16, 12, 4, 7], [16, 20, 4, 7, "global"], [16, 26, 4, 7], [16, 27, 4, 7, "Error"], [16, 32, 4, 7], [17, 4, 4, 7], [17, 10, 4, 7, "rigidDecay"], [17, 20, 4, 7], [17, 32, 4, 7, "rigidDecay"], [17, 33, 4, 27, "animation"], [17, 42, 4, 36], [17, 44, 4, 38, "now"], [17, 47, 4, 41], [17, 49, 4, 43, "config"], [17, 55, 4, 49], [17, 57, 4, 51], [18, 6, 7, 2], [18, 12, 7, 8], [19, 8, 8, 4, "lastTimestamp"], [19, 21, 8, 17], [20, 8, 9, 4, "startTimestamp"], [20, 22, 9, 18], [21, 8, 10, 4, "initialVelocity"], [21, 23, 10, 19], [22, 8, 11, 4, "current"], [22, 15, 11, 11], [23, 8, 12, 4, "velocity"], [24, 6, 13, 2], [24, 7, 13, 3], [24, 10, 13, 6, "animation"], [24, 19, 13, 15], [25, 6, 14, 2], [25, 12, 14, 8, "deltaTime"], [25, 21, 14, 17], [25, 24, 14, 20, "Math"], [25, 28, 14, 24], [25, 29, 14, 25, "min"], [25, 32, 14, 28], [25, 33, 14, 29, "now"], [25, 36, 14, 32], [25, 39, 14, 35, "lastTimestamp"], [25, 52, 14, 48], [25, 54, 14, 50], [25, 56, 14, 52], [25, 57, 14, 53], [26, 6, 15, 2], [26, 12, 15, 8, "v"], [26, 13, 15, 9], [26, 16, 15, 12, "velocity"], [26, 24, 15, 20], [26, 27, 15, 23, "Math"], [26, 31, 15, 27], [26, 32, 15, 28, "exp"], [26, 35, 15, 31], [26, 36, 15, 32], [26, 38, 15, 34], [26, 39, 15, 35], [26, 42, 15, 38, "config"], [26, 48, 15, 44], [26, 49, 15, 45, "deceleration"], [26, 61, 15, 57], [26, 62, 15, 58], [26, 66, 15, 62, "now"], [26, 69, 15, 65], [26, 72, 15, 68, "startTimestamp"], [26, 86, 15, 82], [26, 87, 15, 83], [26, 90, 15, 86, "SLOPE_FACTOR"], [26, 109, 15, 98], [26, 110, 15, 99], [27, 6, 16, 2, "animation"], [27, 15, 16, 11], [27, 16, 16, 12, "current"], [27, 23, 16, 19], [27, 26, 16, 22, "current"], [27, 33, 16, 29], [27, 36, 16, 32, "v"], [27, 37, 16, 33], [27, 40, 16, 36, "config"], [27, 46, 16, 42], [27, 47, 16, 43, "velocityFactor"], [27, 61, 16, 57], [27, 64, 16, 60, "deltaTime"], [27, 73, 16, 69], [27, 76, 16, 72], [27, 80, 16, 76], [28, 6, 17, 2, "animation"], [28, 15, 17, 11], [28, 16, 17, 12, "velocity"], [28, 24, 17, 20], [28, 27, 17, 23, "v"], [28, 28, 17, 24], [29, 6, 18, 2, "animation"], [29, 15, 18, 11], [29, 16, 18, 12, "lastTimestamp"], [29, 29, 18, 25], [29, 32, 18, 28, "now"], [29, 35, 18, 31], [30, 6, 19, 2], [30, 10, 19, 6, "config"], [30, 16, 19, 12], [30, 17, 19, 13, "clamp"], [30, 22, 19, 18], [30, 24, 19, 20], [31, 8, 20, 4], [31, 12, 20, 8, "initialVelocity"], [31, 27, 20, 23], [31, 30, 20, 26], [31, 31, 20, 27], [31, 35, 20, 31, "animation"], [31, 44, 20, 40], [31, 45, 20, 41, "current"], [31, 52, 20, 48], [31, 56, 20, 52, "config"], [31, 62, 20, 58], [31, 63, 20, 59, "clamp"], [31, 68, 20, 64], [31, 69, 20, 65], [31, 70, 20, 66], [31, 71, 20, 67], [31, 73, 20, 69], [32, 10, 21, 6, "animation"], [32, 19, 21, 15], [32, 20, 21, 16, "current"], [32, 27, 21, 23], [32, 30, 21, 26, "config"], [32, 36, 21, 32], [32, 37, 21, 33, "clamp"], [32, 42, 21, 38], [32, 43, 21, 39], [32, 44, 21, 40], [32, 45, 21, 41], [33, 10, 22, 6], [33, 17, 22, 13], [33, 21, 22, 17], [34, 8, 23, 4], [34, 9, 23, 5], [34, 15, 23, 11], [34, 19, 23, 15, "initialVelocity"], [34, 34, 23, 30], [34, 37, 23, 33], [34, 38, 23, 34], [34, 42, 23, 38, "animation"], [34, 51, 23, 47], [34, 52, 23, 48, "current"], [34, 59, 23, 55], [34, 63, 23, 59, "config"], [34, 69, 23, 65], [34, 70, 23, 66, "clamp"], [34, 75, 23, 71], [34, 76, 23, 72], [34, 77, 23, 73], [34, 78, 23, 74], [34, 80, 23, 76], [35, 10, 24, 6, "animation"], [35, 19, 24, 15], [35, 20, 24, 16, "current"], [35, 27, 24, 23], [35, 30, 24, 26, "config"], [35, 36, 24, 32], [35, 37, 24, 33, "clamp"], [35, 42, 24, 38], [35, 43, 24, 39], [35, 44, 24, 40], [35, 45, 24, 41], [36, 10, 25, 6], [36, 17, 25, 13], [36, 21, 25, 17], [37, 8, 26, 4], [38, 6, 27, 2], [39, 6, 28, 2], [39, 13, 28, 9, "Math"], [39, 17, 28, 13], [39, 18, 28, 14, "abs"], [39, 21, 28, 17], [39, 22, 28, 18, "v"], [39, 23, 28, 19], [39, 24, 28, 20], [39, 27, 28, 23, "VELOCITY_EPS"], [39, 46, 28, 35], [40, 4, 29, 0], [40, 5, 29, 1], [41, 4, 29, 1, "rigidDecay"], [41, 14, 29, 1], [41, 15, 29, 1, "__closure"], [41, 24, 29, 1], [42, 6, 29, 1, "SLOPE_FACTOR"], [42, 18, 29, 1], [42, 20, 15, 86, "SLOPE_FACTOR"], [42, 39, 15, 98], [43, 6, 15, 98, "VELOCITY_EPS"], [43, 18, 15, 98], [43, 20, 28, 23, "VELOCITY_EPS"], [44, 4, 28, 35], [45, 4, 28, 35, "rigidDecay"], [45, 14, 28, 35], [45, 15, 28, 35, "__workletHash"], [45, 28, 28, 35], [46, 4, 28, 35, "rigidDecay"], [46, 14, 28, 35], [46, 15, 28, 35, "__initData"], [46, 25, 28, 35], [46, 28, 28, 35, "_worklet_2807821538137_init_data"], [46, 60, 28, 35], [47, 4, 28, 35, "rigidDecay"], [47, 14, 28, 35], [47, 15, 28, 35, "__stackDetails"], [47, 29, 28, 35], [47, 32, 28, 35, "_e"], [47, 34, 28, 35], [48, 4, 28, 35], [48, 11, 28, 35, "rigidDecay"], [48, 21, 28, 35], [49, 2, 28, 35], [49, 3, 4, 7], [50, 0, 4, 7], [50, 3]], "functionMap": {"names": ["<global>", "rigidDecay"], "mappings": "AAA;OCG;CDyB"}}, "type": "js/module"}]}