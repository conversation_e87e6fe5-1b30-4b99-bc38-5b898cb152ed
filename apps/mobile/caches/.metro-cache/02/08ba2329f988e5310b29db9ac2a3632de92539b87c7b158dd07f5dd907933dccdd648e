{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _jsxDevRuntime = require(_dependencyMap[21], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n    };\n    const handleSendMessage = (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"9C1WkPoISXmHucsqM0n1HH2DnhY=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 599, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 32, 55], [27, 6, 32, 55, "_jsxDevRuntime"], [27, 20, 32, 55], [27, 23, 32, 55, "require"], [27, 30, 32, 55], [27, 31, 32, 55, "_dependencyMap"], [27, 45, 32, 55], [28, 2, 32, 55], [28, 6, 32, 55, "_jsxFileName"], [28, 18, 32, 55], [29, 4, 32, 55, "_s"], [29, 6, 32, 55], [29, 9, 32, 55, "$RefreshSig$"], [29, 21, 32, 55], [30, 2, 32, 55], [30, 11, 32, 55, "_interopRequireWildcard"], [30, 35, 32, 55, "e"], [30, 36, 32, 55], [30, 38, 32, 55, "t"], [30, 39, 32, 55], [30, 68, 32, 55, "WeakMap"], [30, 75, 32, 55], [30, 81, 32, 55, "r"], [30, 82, 32, 55], [30, 89, 32, 55, "WeakMap"], [30, 96, 32, 55], [30, 100, 32, 55, "n"], [30, 101, 32, 55], [30, 108, 32, 55, "WeakMap"], [30, 115, 32, 55], [30, 127, 32, 55, "_interopRequireWildcard"], [30, 150, 32, 55], [30, 162, 32, 55, "_interopRequireWildcard"], [30, 163, 32, 55, "e"], [30, 164, 32, 55], [30, 166, 32, 55, "t"], [30, 167, 32, 55], [30, 176, 32, 55, "t"], [30, 177, 32, 55], [30, 181, 32, 55, "e"], [30, 182, 32, 55], [30, 186, 32, 55, "e"], [30, 187, 32, 55], [30, 188, 32, 55, "__esModule"], [30, 198, 32, 55], [30, 207, 32, 55, "e"], [30, 208, 32, 55], [30, 214, 32, 55, "o"], [30, 215, 32, 55], [30, 217, 32, 55, "i"], [30, 218, 32, 55], [30, 220, 32, 55, "f"], [30, 221, 32, 55], [30, 226, 32, 55, "__proto__"], [30, 235, 32, 55], [30, 243, 32, 55, "default"], [30, 250, 32, 55], [30, 252, 32, 55, "e"], [30, 253, 32, 55], [30, 270, 32, 55, "e"], [30, 271, 32, 55], [30, 294, 32, 55, "e"], [30, 295, 32, 55], [30, 320, 32, 55, "e"], [30, 321, 32, 55], [30, 330, 32, 55, "f"], [30, 331, 32, 55], [30, 337, 32, 55, "o"], [30, 338, 32, 55], [30, 341, 32, 55, "t"], [30, 342, 32, 55], [30, 345, 32, 55, "n"], [30, 346, 32, 55], [30, 349, 32, 55, "r"], [30, 350, 32, 55], [30, 358, 32, 55, "o"], [30, 359, 32, 55], [30, 360, 32, 55, "has"], [30, 363, 32, 55], [30, 364, 32, 55, "e"], [30, 365, 32, 55], [30, 375, 32, 55, "o"], [30, 376, 32, 55], [30, 377, 32, 55, "get"], [30, 380, 32, 55], [30, 381, 32, 55, "e"], [30, 382, 32, 55], [30, 385, 32, 55, "o"], [30, 386, 32, 55], [30, 387, 32, 55, "set"], [30, 390, 32, 55], [30, 391, 32, 55, "e"], [30, 392, 32, 55], [30, 394, 32, 55, "f"], [30, 395, 32, 55], [30, 411, 32, 55, "t"], [30, 412, 32, 55], [30, 416, 32, 55, "e"], [30, 417, 32, 55], [30, 433, 32, 55, "t"], [30, 434, 32, 55], [30, 441, 32, 55, "hasOwnProperty"], [30, 455, 32, 55], [30, 456, 32, 55, "call"], [30, 460, 32, 55], [30, 461, 32, 55, "e"], [30, 462, 32, 55], [30, 464, 32, 55, "t"], [30, 465, 32, 55], [30, 472, 32, 55, "i"], [30, 473, 32, 55], [30, 477, 32, 55, "o"], [30, 478, 32, 55], [30, 481, 32, 55, "Object"], [30, 487, 32, 55], [30, 488, 32, 55, "defineProperty"], [30, 502, 32, 55], [30, 507, 32, 55, "Object"], [30, 513, 32, 55], [30, 514, 32, 55, "getOwnPropertyDescriptor"], [30, 538, 32, 55], [30, 539, 32, 55, "e"], [30, 540, 32, 55], [30, 542, 32, 55, "t"], [30, 543, 32, 55], [30, 550, 32, 55, "i"], [30, 551, 32, 55], [30, 552, 32, 55, "get"], [30, 555, 32, 55], [30, 559, 32, 55, "i"], [30, 560, 32, 55], [30, 561, 32, 55, "set"], [30, 564, 32, 55], [30, 568, 32, 55, "o"], [30, 569, 32, 55], [30, 570, 32, 55, "f"], [30, 571, 32, 55], [30, 573, 32, 55, "t"], [30, 574, 32, 55], [30, 576, 32, 55, "i"], [30, 577, 32, 55], [30, 581, 32, 55, "f"], [30, 582, 32, 55], [30, 583, 32, 55, "t"], [30, 584, 32, 55], [30, 588, 32, 55, "e"], [30, 589, 32, 55], [30, 590, 32, 55, "t"], [30, 591, 32, 55], [30, 602, 32, 55, "f"], [30, 603, 32, 55], [30, 608, 32, 55, "e"], [30, 609, 32, 55], [30, 611, 32, 55, "t"], [30, 612, 32, 55], [31, 2, 34, 15], [31, 11, 34, 24, "BrainstormScreen"], [31, 27, 34, 40, "BrainstormScreen"], [31, 28, 34, 40], [31, 30, 34, 43], [32, 4, 34, 43, "_s"], [32, 6, 34, 43], [33, 4, 35, 2], [33, 10, 35, 8, "insets"], [33, 16, 35, 14], [33, 19, 35, 17], [33, 23, 35, 17, "useSafeAreaInsets"], [33, 68, 35, 34], [33, 70, 35, 35], [33, 71, 35, 36], [34, 4, 36, 2], [34, 10, 36, 8, "colors"], [34, 16, 36, 14], [34, 19, 36, 17], [34, 23, 36, 17, "useColors"], [34, 43, 36, 26], [34, 45, 36, 27], [34, 46, 36, 28], [35, 4, 37, 2], [35, 10, 37, 8], [35, 11, 37, 9, "fontsLoaded"], [35, 22, 37, 20], [35, 23, 37, 21], [35, 26, 37, 24], [35, 30, 37, 24, "useFonts"], [35, 47, 37, 32], [35, 49, 37, 33], [36, 6, 38, 4, "Poppins_400Regular"], [36, 24, 38, 22], [36, 26, 38, 4, "Poppins_400Regular"], [36, 53, 38, 22], [37, 6, 39, 4, "Poppins_500Medium"], [37, 23, 39, 21], [37, 25, 39, 4, "Poppins_500Medium"], [37, 51, 39, 21], [38, 6, 40, 4, "Poppins_600SemiBold"], [38, 25, 40, 23], [38, 27, 40, 4, "Poppins_600SemiBold"], [39, 4, 41, 2], [39, 5, 41, 3], [39, 6, 41, 4], [40, 4, 43, 2], [40, 10, 43, 8, "recorder"], [40, 18, 43, 16], [40, 21, 43, 19], [40, 25, 43, 19, "useAudioRecorder"], [40, 52, 43, 35], [40, 54, 43, 36, "RecordingPresets"], [40, 81, 43, 52], [40, 82, 43, 53, "HIGH_QUALITY"], [40, 94, 43, 65], [40, 95, 43, 66], [41, 4, 44, 2], [41, 10, 44, 8, "recorderState"], [41, 23, 44, 21], [41, 26, 44, 24], [41, 30, 44, 24, "useAudioRecorderState"], [41, 62, 44, 45], [41, 64, 44, 46, "recorder"], [41, 72, 44, 54], [41, 73, 44, 55], [42, 4, 46, 2], [42, 10, 46, 8], [42, 11, 46, 9, "messages"], [42, 19, 46, 17], [42, 21, 46, 19, "setMessages"], [42, 32, 46, 30], [42, 33, 46, 31], [42, 36, 46, 34], [42, 40, 46, 34, "useState"], [42, 55, 46, 42], [42, 57, 46, 43], [42, 59, 46, 45], [42, 60, 46, 46], [43, 4, 47, 2], [43, 10, 47, 8], [43, 11, 47, 9, "inputText"], [43, 20, 47, 18], [43, 22, 47, 20, "setInputText"], [43, 34, 47, 32], [43, 35, 47, 33], [43, 38, 47, 36], [43, 42, 47, 36, "useState"], [43, 57, 47, 44], [43, 59, 47, 45], [43, 61, 47, 47], [43, 62, 47, 48], [44, 4, 48, 2], [44, 10, 48, 8], [44, 11, 48, 9, "isFirstTime"], [44, 22, 48, 20], [44, 24, 48, 22, "setIsFirstTime"], [44, 38, 48, 36], [44, 39, 48, 37], [44, 42, 48, 40], [44, 46, 48, 40, "useState"], [44, 61, 48, 48], [44, 63, 48, 49], [44, 67, 48, 53], [44, 68, 48, 54], [45, 4, 49, 2], [45, 10, 49, 8], [45, 11, 49, 9, "isLoading"], [45, 20, 49, 18], [45, 22, 49, 20, "setIsLoading"], [45, 34, 49, 32], [45, 35, 49, 33], [45, 38, 49, 36], [45, 42, 49, 36, "useState"], [45, 57, 49, 44], [45, 59, 49, 45], [45, 64, 49, 50], [45, 65, 49, 51], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "quickActions"], [46, 23, 50, 21], [46, 25, 50, 23, "setQuickActions"], [46, 40, 50, 38], [46, 41, 50, 39], [46, 44, 50, 42], [46, 48, 50, 42, "useState"], [46, 63, 50, 50], [46, 65, 50, 51], [46, 67, 50, 53], [46, 68, 50, 54], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "voiceMode"], [47, 20, 51, 18], [47, 22, 51, 20, "setVoiceMode"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 63, 51, 49], [47, 64, 51, 50], [47, 65, 51, 51], [47, 66, 51, 52], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "hasPermission"], [48, 24, 52, 22], [48, 26, 52, 24, "setHasPermission"], [48, 42, 52, 40], [48, 43, 52, 41], [48, 46, 52, 44], [48, 50, 52, 44, "useState"], [48, 65, 52, 52], [48, 67, 52, 53], [48, 72, 52, 58], [48, 73, 52, 59], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isDictating"], [49, 22, 53, 20], [49, 24, 53, 22, "setIsDictating"], [49, 38, 53, 36], [49, 39, 53, 37], [49, 42, 53, 40], [49, 46, 53, 40, "useState"], [49, 61, 53, 48], [49, 63, 53, 49], [49, 68, 53, 54], [49, 69, 53, 55], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "longPressedMessage"], [50, 29, 54, 27], [50, 31, 54, 29, "setLongPressedMessage"], [50, 52, 54, 50], [50, 53, 54, 51], [50, 56, 54, 54], [50, 60, 54, 54, "useState"], [50, 75, 54, 62], [50, 77, 54, 63], [50, 81, 54, 67], [50, 82, 54, 68], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "isContextMenuVisible"], [51, 31, 55, 29], [51, 33, 55, 31, "setIsContextMenuVisible"], [51, 56, 55, 54], [51, 57, 55, 55], [51, 60, 55, 58], [51, 64, 55, 58, "useState"], [51, 79, 55, 66], [51, 81, 55, 67], [51, 86, 55, 72], [51, 87, 55, 73], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "transcript"], [52, 21, 56, 19], [52, 23, 56, 21, "setTranscript"], [52, 36, 56, 34], [52, 37, 56, 35], [52, 40, 56, 38], [52, 44, 56, 38, "useState"], [52, 59, 56, 46], [52, 61, 56, 47], [52, 63, 56, 49], [52, 64, 56, 50], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isMuted"], [53, 18, 57, 16], [53, 20, 57, 18, "setIsMuted"], [53, 30, 57, 28], [53, 31, 57, 29], [53, 34, 57, 32], [53, 38, 57, 32, "useState"], [53, 53, 57, 40], [53, 55, 57, 41], [53, 60, 57, 46], [53, 61, 57, 47], [54, 4, 59, 2], [54, 10, 59, 8, "scrollViewRef"], [54, 23, 59, 21], [54, 26, 59, 24], [54, 30, 59, 24, "useRef"], [54, 43, 59, 30], [54, 45, 59, 31], [54, 49, 59, 35], [54, 50, 59, 36], [56, 4, 61, 2], [57, 4, 62, 2], [57, 8, 62, 2, "useEffect"], [57, 24, 62, 11], [57, 26, 62, 12], [57, 32, 62, 18], [58, 6, 63, 4], [58, 7, 63, 5], [58, 19, 63, 17], [59, 8, 64, 6], [59, 14, 64, 12], [60, 10, 64, 14, "granted"], [61, 8, 64, 22], [61, 9, 64, 23], [61, 12, 64, 26], [61, 18, 64, 32], [61, 22, 64, 32, "requestRecordingPermissionsAsync"], [61, 65, 64, 64], [61, 67, 64, 65], [61, 68, 64, 66], [62, 8, 65, 6, "setHasPermission"], [62, 24, 65, 22], [62, 25, 65, 23, "granted"], [62, 32, 65, 30], [62, 33, 65, 31], [63, 8, 66, 6], [63, 12, 66, 10], [63, 13, 66, 11, "granted"], [63, 20, 66, 18], [63, 24, 66, 22, "voiceMode"], [63, 33, 66, 31], [63, 35, 66, 33], [64, 10, 67, 8, "<PERSON><PERSON>"], [64, 24, 67, 13], [64, 25, 67, 14, "alert"], [64, 30, 67, 19], [64, 31, 68, 10], [64, 52, 68, 31], [64, 54, 69, 10], [64, 133, 69, 89], [64, 135, 70, 10], [64, 136, 71, 12], [65, 12, 71, 14, "text"], [65, 16, 71, 18], [65, 18, 71, 20], [65, 33, 71, 35], [66, 12, 71, 37, "onPress"], [66, 19, 71, 44], [66, 21, 71, 46, "onPress"], [66, 22, 71, 46], [66, 27, 71, 52, "setVoiceMode"], [66, 39, 71, 64], [66, 40, 71, 65], [66, 45, 71, 70], [67, 10, 71, 72], [67, 11, 71, 73], [67, 13, 72, 12], [68, 12, 72, 14, "text"], [68, 16, 72, 18], [68, 18, 72, 20], [69, 10, 72, 25], [69, 11, 72, 26], [69, 12, 74, 8], [69, 13, 74, 9], [70, 8, 75, 6], [71, 6, 76, 4], [71, 7, 76, 5], [71, 9, 76, 7], [71, 10, 76, 8], [72, 4, 77, 2], [72, 5, 77, 3], [72, 7, 77, 5], [72, 8, 77, 6, "voiceMode"], [72, 17, 77, 15], [72, 18, 77, 16], [72, 19, 77, 17], [73, 4, 79, 2], [73, 10, 79, 8, "handleStartBrainstorming"], [73, 34, 79, 32], [73, 37, 79, 35, "handleStartBrainstorming"], [73, 38, 79, 35], [73, 43, 79, 41], [74, 6, 80, 4, "Haptics"], [74, 13, 80, 11], [74, 14, 80, 12, "impactAsync"], [74, 25, 80, 23], [74, 26, 80, 24, "Haptics"], [74, 33, 80, 31], [74, 34, 80, 32, "ImpactFeedbackStyle"], [74, 53, 80, 51], [74, 54, 80, 52, "Medium"], [74, 60, 80, 58], [74, 61, 80, 59], [75, 6, 81, 4, "setIsFirstTime"], [75, 20, 81, 18], [75, 21, 81, 19], [75, 26, 81, 24], [75, 27, 81, 25], [76, 6, 82, 4, "setMessages"], [76, 17, 82, 15], [76, 18, 82, 16, "fakeMessages"], [76, 40, 82, 28], [76, 41, 82, 29], [77, 6, 83, 4, "setQuickActions"], [77, 21, 83, 19], [77, 22, 83, 20, "fakeQuickActions"], [77, 48, 83, 36], [77, 49, 83, 37], [78, 4, 84, 2], [78, 5, 84, 3], [79, 4, 86, 2], [79, 10, 86, 8, "handleSendMessage"], [79, 27, 86, 25], [79, 30, 86, 28, "handleSendMessage"], [79, 31, 86, 29, "message"], [79, 38, 86, 36], [79, 41, 86, 39, "inputText"], [79, 50, 86, 48], [79, 55, 86, 53], [80, 6, 87, 4], [80, 10, 87, 8], [80, 11, 87, 9, "message"], [80, 18, 87, 16], [80, 19, 87, 17, "trim"], [80, 23, 87, 21], [80, 24, 87, 22], [80, 25, 87, 23], [80, 27, 87, 25], [81, 6, 89, 4], [81, 12, 89, 10, "newUserMessage"], [81, 26, 89, 24], [81, 29, 89, 27], [82, 8, 89, 29, "role"], [82, 12, 89, 33], [82, 14, 89, 35], [82, 20, 89, 41], [83, 8, 89, 43, "content"], [83, 15, 89, 50], [83, 17, 89, 52, "message"], [84, 6, 89, 60], [84, 7, 89, 61], [85, 6, 90, 4, "setMessages"], [85, 17, 90, 15], [85, 18, 90, 17, "prev"], [85, 22, 90, 21], [85, 26, 90, 26], [85, 27, 90, 27], [85, 30, 90, 30, "prev"], [85, 34, 90, 34], [85, 36, 90, 36, "newUserMessage"], [85, 50, 90, 50], [85, 51, 90, 51], [85, 52, 90, 52], [86, 6, 91, 4, "setInputText"], [86, 18, 91, 16], [86, 19, 91, 17], [86, 21, 91, 19], [86, 22, 91, 20], [87, 6, 92, 4, "setQuickActions"], [87, 21, 92, 19], [87, 22, 92, 20], [87, 24, 92, 22], [87, 25, 92, 23], [88, 6, 93, 4, "setIsLoading"], [88, 18, 93, 16], [88, 19, 93, 17], [88, 23, 93, 21], [88, 24, 93, 22], [90, 6, 95, 4], [91, 6, 96, 4, "setTimeout"], [91, 16, 96, 14], [91, 17, 96, 15], [91, 23, 96, 21], [92, 8, 97, 6], [92, 14, 97, 12, "aiResponse"], [92, 24, 97, 22], [92, 27, 97, 25], [93, 10, 98, 8, "role"], [93, 14, 98, 12], [93, 16, 98, 14], [93, 27, 98, 25], [94, 10, 99, 8, "content"], [94, 17, 99, 15], [94, 19, 99, 17], [95, 8, 100, 6], [95, 9, 100, 7], [96, 8, 101, 6, "setMessages"], [96, 19, 101, 17], [96, 20, 101, 19, "prev"], [96, 24, 101, 23], [96, 28, 101, 28], [96, 29, 101, 29], [96, 32, 101, 32, "prev"], [96, 36, 101, 36], [96, 38, 101, 38, "aiResponse"], [96, 48, 101, 48], [96, 49, 101, 49], [96, 50, 101, 50], [97, 8, 102, 6, "setQuickActions"], [97, 23, 102, 21], [97, 24, 102, 22, "fakeQuickActions"], [97, 50, 102, 38], [97, 51, 102, 39], [98, 8, 103, 6, "setIsLoading"], [98, 20, 103, 18], [98, 21, 103, 19], [98, 26, 103, 24], [98, 27, 103, 25], [99, 6, 104, 4], [99, 7, 104, 5], [99, 9, 104, 7], [99, 13, 104, 11], [99, 14, 104, 12], [100, 4, 105, 2], [100, 5, 105, 3], [101, 4, 107, 2], [101, 10, 107, 8, "handleQuickAction"], [101, 27, 107, 25], [101, 30, 107, 29, "action"], [101, 36, 107, 35], [101, 40, 107, 40], [102, 6, 108, 4, "Haptics"], [102, 13, 108, 11], [102, 14, 108, 12, "impactAsync"], [102, 25, 108, 23], [102, 26, 108, 24, "Haptics"], [102, 33, 108, 31], [102, 34, 108, 32, "ImpactFeedbackStyle"], [102, 53, 108, 51], [102, 54, 108, 52, "Light"], [102, 59, 108, 57], [102, 60, 108, 58], [103, 6, 109, 4, "handleSendMessage"], [103, 23, 109, 21], [103, 24, 109, 22, "action"], [103, 30, 109, 28], [103, 31, 109, 29], [104, 4, 110, 2], [104, 5, 110, 3], [105, 4, 112, 2], [105, 10, 112, 8, "toggleVoiceMode"], [105, 25, 112, 23], [105, 28, 112, 26], [105, 34, 112, 26, "toggleVoiceMode"], [105, 35, 112, 26], [105, 40, 112, 38], [106, 6, 113, 4], [106, 10, 113, 8], [106, 11, 113, 9, "voiceMode"], [106, 20, 113, 18], [106, 24, 113, 22], [106, 25, 113, 23, "hasPermission"], [106, 38, 113, 36], [106, 40, 113, 38], [107, 8, 114, 6, "<PERSON><PERSON>"], [107, 22, 114, 11], [107, 23, 114, 12, "alert"], [107, 28, 114, 17], [107, 29, 115, 8], [107, 50, 115, 29], [107, 52, 116, 8], [107, 102, 116, 58], [107, 104, 117, 8], [107, 105, 118, 10], [108, 10, 118, 12, "text"], [108, 14, 118, 16], [108, 16, 118, 18], [109, 8, 118, 27], [109, 9, 118, 28], [109, 11, 119, 10], [110, 10, 120, 12, "text"], [110, 14, 120, 16], [110, 16, 120, 18], [110, 34, 120, 36], [111, 10, 121, 12, "onPress"], [111, 17, 121, 19], [111, 19, 121, 21], [111, 25, 121, 21, "onPress"], [111, 26, 121, 21], [111, 31, 121, 33], [112, 12, 122, 14], [112, 18, 122, 20], [113, 14, 122, 22, "granted"], [114, 12, 122, 30], [114, 13, 122, 31], [114, 16, 122, 34], [114, 22, 122, 40], [114, 26, 122, 40, "requestRecordingPermissionsAsync"], [114, 69, 122, 72], [114, 71, 122, 73], [114, 72, 122, 74], [115, 12, 123, 14], [115, 16, 123, 18, "granted"], [115, 23, 123, 25], [115, 25, 123, 27], [116, 14, 124, 16, "setHasPermission"], [116, 30, 124, 32], [116, 31, 124, 33], [116, 35, 124, 37], [116, 36, 124, 38], [117, 14, 125, 16, "setVoiceMode"], [117, 26, 125, 28], [117, 27, 125, 29], [117, 31, 125, 33], [117, 32, 125, 34], [118, 14, 126, 16], [119, 14, 127, 16], [119, 20, 127, 22, "startVoiceSession"], [119, 37, 127, 39], [119, 38, 127, 40], [119, 39, 127, 41], [120, 12, 128, 14], [121, 10, 129, 12], [122, 8, 130, 10], [122, 9, 130, 11], [122, 10, 132, 6], [122, 11, 132, 7], [123, 8, 133, 6], [124, 6, 134, 4], [125, 6, 136, 4, "Haptics"], [125, 13, 136, 11], [125, 14, 136, 12, "impactAsync"], [125, 25, 136, 23], [125, 26, 136, 24, "Haptics"], [125, 33, 136, 31], [125, 34, 136, 32, "ImpactFeedbackStyle"], [125, 53, 136, 51], [125, 54, 136, 52, "Light"], [125, 59, 136, 57], [125, 60, 136, 58], [126, 6, 138, 4], [126, 10, 138, 8], [126, 11, 138, 9, "voiceMode"], [126, 20, 138, 18], [126, 22, 138, 20], [127, 8, 139, 6], [128, 8, 140, 6, "setVoiceMode"], [128, 20, 140, 18], [128, 21, 140, 19], [128, 25, 140, 23], [128, 26, 140, 24], [129, 8, 141, 6], [129, 12, 141, 10, "hasPermission"], [129, 25, 141, 23], [129, 27, 141, 25], [130, 10, 142, 8], [130, 16, 142, 14, "startVoiceSession"], [130, 33, 142, 31], [130, 34, 142, 32], [130, 35, 142, 33], [131, 8, 143, 6], [132, 6, 144, 4], [132, 7, 144, 5], [132, 13, 144, 11], [133, 8, 145, 6], [134, 8, 146, 6, "setVoiceMode"], [134, 20, 146, 18], [134, 21, 146, 19], [134, 26, 146, 24], [134, 27, 146, 25], [135, 8, 147, 6], [135, 12, 147, 10, "recorderState"], [135, 25, 147, 23], [135, 26, 147, 24, "isRecording"], [135, 37, 147, 35], [135, 39, 147, 37], [136, 10, 148, 8], [136, 16, 148, 14, "recorder"], [136, 24, 148, 22], [136, 25, 148, 23, "stop"], [136, 29, 148, 27], [136, 30, 148, 28], [136, 31, 148, 29], [137, 8, 149, 6], [138, 8, 150, 6, "setIsMuted"], [138, 18, 150, 16], [138, 19, 150, 17], [138, 24, 150, 22], [138, 25, 150, 23], [138, 26, 150, 24], [138, 27, 150, 25], [139, 6, 151, 4], [140, 4, 152, 2], [140, 5, 152, 3], [141, 4, 154, 2], [141, 10, 154, 8, "startVoiceSession"], [141, 27, 154, 25], [141, 30, 154, 28], [141, 36, 154, 28, "startVoiceSession"], [141, 37, 154, 28], [141, 42, 154, 40], [142, 6, 155, 4], [142, 10, 155, 8], [142, 11, 155, 9, "hasPermission"], [142, 24, 155, 22], [142, 26, 155, 24], [143, 6, 157, 4], [143, 10, 157, 8], [144, 8, 158, 6], [144, 14, 158, 12, "recorder"], [144, 22, 158, 20], [144, 23, 158, 21, "prepareToRecordAsync"], [144, 43, 158, 41], [144, 44, 158, 42], [144, 45, 158, 43], [145, 8, 159, 6, "recorder"], [145, 16, 159, 14], [145, 17, 159, 15, "record"], [145, 23, 159, 21], [145, 24, 159, 22], [145, 25, 159, 23], [146, 8, 160, 6, "setIsMuted"], [146, 18, 160, 16], [146, 19, 160, 17], [146, 24, 160, 22], [146, 25, 160, 23], [146, 26, 160, 24], [146, 27, 160, 25], [147, 8, 161, 6, "Haptics"], [147, 15, 161, 13], [147, 16, 161, 14, "impactAsync"], [147, 27, 161, 25], [147, 28, 161, 26, "Haptics"], [147, 35, 161, 33], [147, 36, 161, 34, "ImpactFeedbackStyle"], [147, 55, 161, 53], [147, 56, 161, 54, "Medium"], [147, 62, 161, 60], [147, 63, 161, 61], [148, 6, 162, 4], [148, 7, 162, 5], [148, 8, 162, 6], [148, 15, 162, 13, "error"], [148, 20, 162, 18], [148, 22, 162, 20], [149, 8, 163, 6, "console"], [149, 15, 163, 13], [149, 16, 163, 14, "error"], [149, 21, 163, 19], [149, 22, 163, 20], [149, 53, 163, 51], [149, 55, 163, 53, "error"], [149, 60, 163, 58], [149, 61, 163, 59], [150, 8, 164, 6, "<PERSON><PERSON>"], [150, 22, 164, 11], [150, 23, 164, 12, "alert"], [150, 28, 164, 17], [150, 29, 164, 18], [150, 36, 164, 25], [150, 38, 164, 27], [150, 69, 164, 58], [150, 70, 164, 59], [151, 6, 165, 4], [152, 4, 166, 2], [152, 5, 166, 3], [153, 4, 168, 2], [153, 10, 168, 8, "stopVoiceSession"], [153, 26, 168, 24], [153, 29, 168, 27], [153, 35, 168, 27, "stopVoiceSession"], [153, 36, 168, 27], [153, 41, 168, 39], [154, 6, 169, 4], [154, 10, 169, 8], [155, 8, 170, 6], [155, 12, 170, 10, "recorderState"], [155, 25, 170, 23], [155, 26, 170, 24, "isRecording"], [155, 37, 170, 35], [155, 39, 170, 37], [156, 10, 171, 8], [156, 16, 171, 14, "recorder"], [156, 24, 171, 22], [156, 25, 171, 23, "stop"], [156, 29, 171, 27], [156, 30, 171, 28], [156, 31, 171, 29], [157, 10, 172, 8, "Haptics"], [157, 17, 172, 15], [157, 18, 172, 16, "impactAsync"], [157, 29, 172, 27], [157, 30, 172, 28, "Haptics"], [157, 37, 172, 35], [157, 38, 172, 36, "ImpactFeedbackStyle"], [157, 57, 172, 55], [157, 58, 172, 56, "Medium"], [157, 64, 172, 62], [157, 65, 172, 63], [158, 8, 173, 6], [159, 6, 174, 4], [159, 7, 174, 5], [159, 8, 174, 6], [159, 15, 174, 13, "error"], [159, 20, 174, 18], [159, 22, 174, 20], [160, 8, 175, 6, "console"], [160, 15, 175, 13], [160, 16, 175, 14, "error"], [160, 21, 175, 19], [160, 22, 175, 20], [160, 53, 175, 51], [160, 55, 175, 53, "error"], [160, 60, 175, 58], [160, 61, 175, 59], [161, 6, 176, 4], [162, 4, 177, 2], [162, 5, 177, 3], [163, 4, 179, 2], [163, 10, 179, 8, "handleDictation"], [163, 25, 179, 23], [163, 28, 179, 26], [163, 34, 179, 26, "handleDictation"], [163, 35, 179, 26], [163, 40, 179, 38], [164, 6, 180, 4], [164, 10, 180, 8], [164, 11, 180, 9, "hasPermission"], [164, 24, 180, 22], [164, 26, 180, 24], [165, 8, 181, 6, "<PERSON><PERSON>"], [165, 22, 181, 11], [165, 23, 181, 12, "alert"], [165, 28, 181, 17], [165, 29, 182, 8], [165, 50, 182, 29], [165, 52, 183, 8], [165, 102, 184, 6], [165, 103, 184, 7], [166, 8, 185, 6], [167, 6, 186, 4], [168, 6, 188, 4], [168, 10, 188, 8], [169, 8, 189, 6], [169, 12, 189, 10, "isDictating"], [169, 23, 189, 21], [169, 25, 189, 23], [170, 10, 190, 8], [170, 16, 190, 14, "recorder"], [170, 24, 190, 22], [170, 25, 190, 23, "stop"], [170, 29, 190, 27], [170, 30, 190, 28], [170, 31, 190, 29], [171, 10, 191, 8], [171, 16, 191, 14, "mockTranscript"], [171, 30, 191, 28], [171, 33, 191, 31], [171, 62, 191, 60], [171, 63, 191, 61], [171, 64, 191, 62], [172, 10, 192, 8, "setInputText"], [172, 22, 192, 20], [172, 23, 192, 21, "inputText"], [172, 32, 192, 30], [172, 35, 192, 33, "mockTranscript"], [172, 49, 192, 47], [172, 50, 192, 48], [173, 10, 193, 8, "setIsDictating"], [173, 24, 193, 22], [173, 25, 193, 23], [173, 30, 193, 28], [173, 31, 193, 29], [174, 8, 194, 6], [174, 9, 194, 7], [174, 15, 194, 13], [175, 10, 195, 8], [175, 16, 195, 14, "recorder"], [175, 24, 195, 22], [175, 25, 195, 23, "prepareToRecordAsync"], [175, 45, 195, 43], [175, 46, 195, 44], [175, 47, 195, 45], [176, 10, 196, 8, "recorder"], [176, 18, 196, 16], [176, 19, 196, 17, "record"], [176, 25, 196, 23], [176, 26, 196, 24], [176, 27, 196, 25], [177, 10, 197, 8, "setIsDictating"], [177, 24, 197, 22], [177, 25, 197, 23], [177, 29, 197, 27], [177, 30, 197, 28], [178, 10, 198, 8, "Haptics"], [178, 17, 198, 15], [178, 18, 198, 16, "impactAsync"], [178, 29, 198, 27], [178, 30, 198, 28, "Haptics"], [178, 37, 198, 35], [178, 38, 198, 36, "ImpactFeedbackStyle"], [178, 57, 198, 55], [178, 58, 198, 56, "Medium"], [178, 64, 198, 62], [178, 65, 198, 63], [179, 8, 199, 6], [180, 6, 200, 4], [180, 7, 200, 5], [180, 8, 200, 6], [180, 15, 200, 13, "error"], [180, 20, 200, 18], [180, 22, 200, 20], [181, 8, 201, 6, "console"], [181, 15, 201, 13], [181, 16, 201, 14, "error"], [181, 21, 201, 19], [181, 22, 201, 20], [181, 45, 201, 43], [181, 47, 201, 45, "error"], [181, 52, 201, 50], [181, 53, 201, 51], [182, 8, 202, 6, "<PERSON><PERSON>"], [182, 22, 202, 11], [182, 23, 202, 12, "alert"], [182, 28, 202, 17], [182, 29, 202, 18], [182, 36, 202, 25], [182, 38, 202, 27], [182, 65, 202, 54], [182, 66, 202, 55], [183, 8, 203, 6, "setIsDictating"], [183, 22, 203, 20], [183, 23, 203, 21], [183, 28, 203, 26], [183, 29, 203, 27], [184, 6, 204, 4], [185, 4, 205, 2], [185, 5, 205, 3], [186, 4, 207, 2], [186, 10, 207, 8, "handleLongPress"], [186, 25, 207, 23], [186, 28, 207, 27, "message"], [186, 35, 207, 34], [186, 39, 207, 39], [187, 6, 208, 4, "setLongPressedMessage"], [187, 27, 208, 25], [187, 28, 208, 26, "message"], [187, 35, 208, 33], [187, 36, 208, 34], [188, 6, 209, 4, "setIsContextMenuVisible"], [188, 29, 209, 27], [188, 30, 209, 28], [188, 34, 209, 32], [188, 35, 209, 33], [189, 4, 210, 2], [189, 5, 210, 3], [190, 4, 212, 2], [190, 10, 212, 8, "handleCopyMessage"], [190, 27, 212, 25], [190, 30, 212, 28, "handleCopyMessage"], [190, 31, 212, 28], [190, 36, 212, 34], [191, 6, 213, 4], [191, 10, 213, 8, "longPressedMessage"], [191, 28, 213, 26], [191, 30, 213, 28], [192, 8, 214, 6, "Clipboard"], [192, 26, 214, 15], [192, 27, 214, 16, "setString"], [192, 36, 214, 25], [192, 37, 214, 26, "longPressedMessage"], [192, 55, 214, 44], [192, 56, 214, 45, "content"], [192, 63, 214, 52], [192, 64, 214, 53], [193, 8, 215, 6, "setIsContextMenuVisible"], [193, 31, 215, 29], [193, 32, 215, 30], [193, 37, 215, 35], [193, 38, 215, 36], [194, 8, 216, 6, "setLongPressedMessage"], [194, 29, 216, 27], [194, 30, 216, 28], [194, 34, 216, 32], [194, 35, 216, 33], [195, 6, 217, 4], [196, 4, 218, 2], [196, 5, 218, 3], [197, 4, 220, 2], [197, 10, 220, 8, "handleListenToMessage"], [197, 31, 220, 29], [197, 34, 220, 32, "handleListenToMessage"], [197, 35, 220, 32], [197, 40, 220, 38], [198, 6, 221, 4], [198, 10, 221, 8, "longPressedMessage"], [198, 28, 221, 26], [198, 30, 221, 28], [199, 8, 222, 6], [200, 8, 223, 6, "<PERSON><PERSON>"], [200, 22, 223, 11], [200, 23, 223, 12, "alert"], [200, 28, 223, 17], [200, 29, 223, 18], [200, 51, 223, 40], [200, 53, 223, 42, "longPressedMessage"], [200, 71, 223, 60], [200, 72, 223, 61, "content"], [200, 79, 223, 68], [200, 80, 223, 69], [201, 8, 224, 6, "setIsContextMenuVisible"], [201, 31, 224, 29], [201, 32, 224, 30], [201, 37, 224, 35], [201, 38, 224, 36], [202, 8, 225, 6, "setLongPressedMessage"], [202, 29, 225, 27], [202, 30, 225, 28], [202, 34, 225, 32], [202, 35, 225, 33], [203, 6, 226, 4], [204, 4, 227, 2], [204, 5, 227, 3], [205, 4, 229, 2], [205, 10, 229, 8, "handleMute"], [205, 20, 229, 18], [205, 23, 229, 21], [205, 29, 229, 21, "handleMute"], [205, 30, 229, 21], [205, 35, 229, 33], [206, 6, 230, 4], [206, 10, 230, 8], [206, 11, 230, 9, "hasPermission"], [206, 24, 230, 22], [206, 26, 230, 24], [207, 6, 232, 4], [207, 10, 232, 8], [208, 8, 233, 6], [208, 12, 233, 10, "isMuted"], [208, 19, 233, 17], [208, 21, 233, 19], [209, 10, 234, 8], [210, 10, 235, 8], [210, 14, 235, 12], [210, 15, 235, 13, "recorderState"], [210, 28, 235, 26], [210, 29, 235, 27, "isRecording"], [210, 40, 235, 38], [210, 42, 235, 40], [211, 12, 236, 10], [211, 18, 236, 16, "recorder"], [211, 26, 236, 24], [211, 27, 236, 25, "prepareToRecordAsync"], [211, 47, 236, 45], [211, 48, 236, 46], [211, 49, 236, 47], [212, 12, 237, 10, "recorder"], [212, 20, 237, 18], [212, 21, 237, 19, "record"], [212, 27, 237, 25], [212, 28, 237, 26], [212, 29, 237, 27], [213, 10, 238, 8], [214, 10, 239, 8, "setIsMuted"], [214, 20, 239, 18], [214, 21, 239, 19], [214, 26, 239, 24], [214, 27, 239, 25], [215, 10, 240, 8, "Haptics"], [215, 17, 240, 15], [215, 18, 240, 16, "impactAsync"], [215, 29, 240, 27], [215, 30, 240, 28, "Haptics"], [215, 37, 240, 35], [215, 38, 240, 36, "ImpactFeedbackStyle"], [215, 57, 240, 55], [215, 58, 240, 56, "Light"], [215, 63, 240, 61], [215, 64, 240, 62], [216, 8, 241, 6], [216, 9, 241, 7], [216, 15, 241, 13], [217, 10, 242, 8], [218, 10, 243, 8], [218, 14, 243, 12, "recorderState"], [218, 27, 243, 25], [218, 28, 243, 26, "isRecording"], [218, 39, 243, 37], [218, 41, 243, 39], [219, 12, 244, 10], [219, 18, 244, 16, "recorder"], [219, 26, 244, 24], [219, 27, 244, 25, "stop"], [219, 31, 244, 29], [219, 32, 244, 30], [219, 33, 244, 31], [220, 10, 245, 8], [221, 10, 246, 8, "setIsMuted"], [221, 20, 246, 18], [221, 21, 246, 19], [221, 25, 246, 23], [221, 26, 246, 24], [222, 10, 247, 8, "Haptics"], [222, 17, 247, 15], [222, 18, 247, 16, "impactAsync"], [222, 29, 247, 27], [222, 30, 247, 28, "Haptics"], [222, 37, 247, 35], [222, 38, 247, 36, "ImpactFeedbackStyle"], [222, 57, 247, 55], [222, 58, 247, 56, "Light"], [222, 63, 247, 61], [222, 64, 247, 62], [223, 8, 248, 6], [224, 6, 249, 4], [224, 7, 249, 5], [224, 8, 249, 6], [224, 15, 249, 13, "error"], [224, 20, 249, 18], [224, 22, 249, 20], [225, 8, 250, 6, "console"], [225, 15, 250, 13], [225, 16, 250, 14, "error"], [225, 21, 250, 19], [225, 22, 250, 20], [225, 47, 250, 45], [225, 49, 250, 47, "error"], [225, 54, 250, 52], [225, 55, 250, 53], [226, 6, 251, 4], [227, 4, 252, 2], [227, 5, 252, 3], [229, 4, 255, 2], [230, 4, 256, 2], [230, 8, 256, 2, "useEffect"], [230, 24, 256, 11], [230, 26, 256, 12], [230, 32, 256, 18], [231, 6, 257, 4], [231, 10, 257, 8, "scrollViewRef"], [231, 23, 257, 21], [231, 24, 257, 22, "current"], [231, 31, 257, 29], [231, 33, 257, 31], [232, 8, 258, 6, "scrollViewRef"], [232, 21, 258, 19], [232, 22, 258, 20, "current"], [232, 29, 258, 27], [232, 30, 258, 28, "scrollToEnd"], [232, 41, 258, 39], [232, 42, 258, 40], [233, 10, 258, 42, "animated"], [233, 18, 258, 50], [233, 20, 258, 52], [234, 8, 258, 57], [234, 9, 258, 58], [234, 10, 258, 59], [235, 6, 259, 4], [236, 4, 260, 2], [236, 5, 260, 3], [236, 7, 260, 5], [236, 8, 260, 6, "messages"], [236, 16, 260, 14], [236, 17, 260, 15], [236, 18, 260, 16], [237, 4, 262, 2], [237, 8, 262, 6], [237, 9, 262, 7, "fontsLoaded"], [237, 20, 262, 18], [237, 22, 262, 20], [238, 6, 263, 4], [238, 13, 263, 11], [238, 17, 263, 15], [239, 4, 264, 2], [241, 4, 266, 2], [242, 4, 267, 2], [242, 8, 267, 6, "isFirstTime"], [242, 19, 267, 17], [242, 21, 267, 19], [243, 6, 268, 4], [243, 26, 269, 6], [243, 30, 269, 6, "_jsxDevRuntime"], [243, 44, 269, 6], [243, 45, 269, 6, "jsxDEV"], [243, 51, 269, 6], [243, 53, 269, 7, "_View"], [243, 58, 269, 7], [243, 59, 269, 7, "default"], [243, 66, 269, 11], [244, 8, 269, 12, "style"], [244, 13, 269, 17], [244, 15, 269, 19], [245, 10, 269, 21, "flex"], [245, 14, 269, 25], [245, 16, 269, 27], [245, 17, 269, 28], [246, 10, 269, 30, "backgroundColor"], [246, 25, 269, 45], [246, 27, 269, 47, "colors"], [246, 33, 269, 53], [246, 34, 269, 54, "background"], [247, 8, 269, 65], [247, 9, 269, 67], [248, 8, 269, 67, "children"], [248, 16, 269, 67], [248, 31, 270, 8], [248, 35, 270, 8, "_jsxDevRuntime"], [248, 49, 270, 8], [248, 50, 270, 8, "jsxDEV"], [248, 56, 270, 8], [248, 58, 270, 9, "_View"], [248, 63, 270, 9], [248, 64, 270, 9, "default"], [248, 71, 270, 13], [249, 10, 271, 10, "style"], [249, 15, 271, 15], [249, 17, 271, 17], [250, 12, 272, 12, "flex"], [250, 16, 272, 16], [250, 18, 272, 18], [250, 19, 272, 19], [251, 12, 273, 12, "paddingTop"], [251, 22, 273, 22], [251, 24, 273, 24, "insets"], [251, 30, 273, 30], [251, 31, 273, 31, "top"], [251, 34, 273, 34], [251, 37, 273, 37], [251, 39, 273, 39], [252, 12, 274, 12, "paddingHorizontal"], [252, 29, 274, 29], [252, 31, 274, 31], [252, 33, 274, 33], [253, 12, 275, 12, "paddingBottom"], [253, 25, 275, 25], [253, 27, 275, 27, "insets"], [253, 33, 275, 33], [253, 34, 275, 34, "bottom"], [253, 40, 275, 40], [253, 43, 275, 43], [253, 45, 275, 45], [254, 12, 276, 12, "alignItems"], [254, 22, 276, 22], [254, 24, 276, 24], [254, 32, 276, 32], [255, 12, 277, 12, "justifyContent"], [255, 26, 277, 26], [255, 28, 277, 28], [256, 10, 278, 10], [256, 11, 278, 12], [257, 10, 278, 12, "children"], [257, 18, 278, 12], [257, 34, 280, 10], [257, 38, 280, 10, "_jsxDevRuntime"], [257, 52, 280, 10], [257, 53, 280, 10, "jsxDEV"], [257, 59, 280, 10], [257, 61, 280, 11, "_View"], [257, 66, 280, 11], [257, 67, 280, 11, "default"], [257, 74, 280, 15], [258, 12, 281, 12, "style"], [258, 17, 281, 17], [258, 19, 281, 19], [259, 14, 282, 14, "width"], [259, 19, 282, 19], [259, 21, 282, 21], [259, 24, 282, 24], [260, 14, 283, 14, "height"], [260, 20, 283, 20], [260, 22, 283, 22], [260, 25, 283, 25], [261, 14, 284, 14, "borderRadius"], [261, 26, 284, 26], [261, 28, 284, 28], [261, 30, 284, 30], [262, 14, 285, 14, "backgroundColor"], [262, 29, 285, 29], [262, 31, 285, 31, "colors"], [262, 37, 285, 37], [262, 38, 285, 38, "primaryUltraLight"], [262, 55, 285, 55], [263, 14, 286, 14, "alignItems"], [263, 24, 286, 24], [263, 26, 286, 26], [263, 34, 286, 34], [264, 14, 287, 14, "justifyContent"], [264, 28, 287, 28], [264, 30, 287, 30], [264, 38, 287, 38], [265, 14, 288, 14, "marginBottom"], [265, 26, 288, 26], [265, 28, 288, 28], [266, 12, 289, 12], [266, 13, 289, 14], [267, 12, 289, 14, "children"], [267, 20, 289, 14], [267, 35, 290, 12], [267, 39, 290, 12, "_jsxDevRuntime"], [267, 53, 290, 12], [267, 54, 290, 12, "jsxDEV"], [267, 60, 290, 12], [267, 62, 290, 13, "_lucideReactNative"], [267, 80, 290, 13], [267, 81, 290, 13, "MessageSquare"], [267, 94, 290, 26], [268, 14, 290, 27, "size"], [268, 18, 290, 31], [268, 20, 290, 33], [268, 22, 290, 36], [269, 14, 290, 37, "color"], [269, 19, 290, 42], [269, 21, 290, 44, "colors"], [269, 27, 290, 50], [269, 28, 290, 51, "primary"], [270, 12, 290, 59], [271, 14, 290, 59, "fileName"], [271, 22, 290, 59], [271, 24, 290, 59, "_jsxFileName"], [271, 36, 290, 59], [272, 14, 290, 59, "lineNumber"], [272, 24, 290, 59], [273, 14, 290, 59, "columnNumber"], [273, 26, 290, 59], [274, 12, 290, 59], [274, 19, 290, 61], [275, 10, 290, 62], [276, 12, 290, 62, "fileName"], [276, 20, 290, 62], [276, 22, 290, 62, "_jsxFileName"], [276, 34, 290, 62], [277, 12, 290, 62, "lineNumber"], [277, 22, 290, 62], [278, 12, 290, 62, "columnNumber"], [278, 24, 290, 62], [279, 10, 290, 62], [279, 17, 291, 16], [279, 18, 291, 17], [279, 33, 294, 10], [279, 37, 294, 10, "_jsxDevRuntime"], [279, 51, 294, 10], [279, 52, 294, 10, "jsxDEV"], [279, 58, 294, 10], [279, 60, 294, 11, "_Text"], [279, 65, 294, 11], [279, 66, 294, 11, "default"], [279, 73, 294, 15], [280, 12, 295, 12, "style"], [280, 17, 295, 17], [280, 19, 295, 19], [281, 14, 296, 14, "fontSize"], [281, 22, 296, 22], [281, 24, 296, 24], [281, 26, 296, 26], [282, 14, 297, 14, "fontFamily"], [282, 24, 297, 24], [282, 26, 297, 26], [282, 47, 297, 47], [283, 14, 298, 14, "color"], [283, 19, 298, 19], [283, 21, 298, 21, "colors"], [283, 27, 298, 27], [283, 28, 298, 28, "text"], [283, 32, 298, 32], [284, 14, 299, 14, "textAlign"], [284, 23, 299, 23], [284, 25, 299, 25], [284, 33, 299, 33], [285, 14, 300, 14, "marginBottom"], [285, 26, 300, 26], [285, 28, 300, 28], [286, 12, 301, 12], [286, 13, 301, 14], [287, 12, 301, 14, "children"], [287, 20, 301, 14], [287, 22, 301, 15], [288, 10, 303, 10], [289, 12, 303, 10, "fileName"], [289, 20, 303, 10], [289, 22, 303, 10, "_jsxFileName"], [289, 34, 303, 10], [290, 12, 303, 10, "lineNumber"], [290, 22, 303, 10], [291, 12, 303, 10, "columnNumber"], [291, 24, 303, 10], [292, 10, 303, 10], [292, 17, 303, 16], [292, 18, 303, 17], [292, 33, 306, 10], [292, 37, 306, 10, "_jsxDevRuntime"], [292, 51, 306, 10], [292, 52, 306, 10, "jsxDEV"], [292, 58, 306, 10], [292, 60, 306, 11, "_Text"], [292, 65, 306, 11], [292, 66, 306, 11, "default"], [292, 73, 306, 15], [293, 12, 307, 12, "style"], [293, 17, 307, 17], [293, 19, 307, 19], [294, 14, 308, 14, "fontSize"], [294, 22, 308, 22], [294, 24, 308, 24], [294, 26, 308, 26], [295, 14, 309, 14, "fontFamily"], [295, 24, 309, 24], [295, 26, 309, 26], [295, 46, 309, 46], [296, 14, 310, 14, "color"], [296, 19, 310, 19], [296, 21, 310, 21, "colors"], [296, 27, 310, 27], [296, 28, 310, 28, "textSecondary"], [296, 41, 310, 41], [297, 14, 311, 14, "textAlign"], [297, 23, 311, 23], [297, 25, 311, 25], [297, 33, 311, 33], [298, 14, 312, 14, "lineHeight"], [298, 24, 312, 24], [298, 26, 312, 26], [298, 28, 312, 28], [299, 14, 313, 14, "marginBottom"], [299, 26, 313, 26], [299, 28, 313, 28], [300, 12, 314, 12], [300, 13, 314, 14], [301, 12, 314, 14, "children"], [301, 20, 314, 14], [301, 22, 314, 15], [302, 10, 317, 10], [303, 12, 317, 10, "fileName"], [303, 20, 317, 10], [303, 22, 317, 10, "_jsxFileName"], [303, 34, 317, 10], [304, 12, 317, 10, "lineNumber"], [304, 22, 317, 10], [305, 12, 317, 10, "columnNumber"], [305, 24, 317, 10], [306, 10, 317, 10], [306, 17, 317, 16], [306, 18, 317, 17], [306, 33, 320, 10], [306, 37, 320, 10, "_jsxDevRuntime"], [306, 51, 320, 10], [306, 52, 320, 10, "jsxDEV"], [306, 58, 320, 10], [306, 60, 320, 11, "_TouchableOpacity"], [306, 77, 320, 11], [306, 78, 320, 11, "default"], [306, 85, 320, 27], [307, 12, 321, 12, "style"], [307, 17, 321, 17], [307, 19, 321, 19], [308, 14, 322, 14, "backgroundColor"], [308, 29, 322, 29], [308, 31, 322, 31, "colors"], [308, 37, 322, 37], [308, 38, 322, 38, "primary"], [308, 45, 322, 45], [309, 14, 323, 14, "borderRadius"], [309, 26, 323, 26], [309, 28, 323, 28], [309, 30, 323, 30], [310, 14, 324, 14, "paddingHorizontal"], [310, 31, 324, 31], [310, 33, 324, 33], [310, 35, 324, 35], [311, 14, 325, 14, "paddingVertical"], [311, 29, 325, 29], [311, 31, 325, 31], [311, 33, 325, 33], [312, 14, 326, 14, "min<PERSON><PERSON><PERSON>"], [312, 22, 326, 22], [312, 24, 326, 24], [312, 27, 326, 27], [313, 14, 327, 14, "alignItems"], [313, 24, 327, 24], [313, 26, 327, 26], [314, 12, 328, 12], [314, 13, 328, 14], [315, 12, 329, 12, "onPress"], [315, 19, 329, 19], [315, 21, 329, 21, "handleStartBrainstorming"], [315, 45, 329, 46], [316, 12, 329, 46, "children"], [316, 20, 329, 46], [316, 35, 330, 12], [316, 39, 330, 12, "_jsxDevRuntime"], [316, 53, 330, 12], [316, 54, 330, 12, "jsxDEV"], [316, 60, 330, 12], [316, 62, 330, 13, "_Text"], [316, 67, 330, 13], [316, 68, 330, 13, "default"], [316, 75, 330, 17], [317, 14, 331, 14, "style"], [317, 19, 331, 19], [317, 21, 331, 21], [318, 16, 332, 16, "fontSize"], [318, 24, 332, 24], [318, 26, 332, 26], [318, 28, 332, 28], [319, 16, 333, 16, "fontFamily"], [319, 26, 333, 26], [319, 28, 333, 28], [319, 49, 333, 49], [320, 16, 334, 16, "color"], [320, 21, 334, 21], [320, 23, 334, 23, "colors"], [320, 29, 334, 29], [320, 30, 334, 30, "background"], [321, 14, 335, 14], [321, 15, 335, 16], [322, 14, 335, 16, "children"], [322, 22, 335, 16], [322, 24, 335, 17], [323, 12, 337, 12], [324, 14, 337, 12, "fileName"], [324, 22, 337, 12], [324, 24, 337, 12, "_jsxFileName"], [324, 36, 337, 12], [325, 14, 337, 12, "lineNumber"], [325, 24, 337, 12], [326, 14, 337, 12, "columnNumber"], [326, 26, 337, 12], [327, 12, 337, 12], [327, 19, 337, 18], [328, 10, 337, 19], [329, 12, 337, 19, "fileName"], [329, 20, 337, 19], [329, 22, 337, 19, "_jsxFileName"], [329, 34, 337, 19], [330, 12, 337, 19, "lineNumber"], [330, 22, 337, 19], [331, 12, 337, 19, "columnNumber"], [331, 24, 337, 19], [332, 10, 337, 19], [332, 17, 338, 28], [332, 18, 338, 29], [333, 8, 338, 29], [334, 10, 338, 29, "fileName"], [334, 18, 338, 29], [334, 20, 338, 29, "_jsxFileName"], [334, 32, 338, 29], [335, 10, 338, 29, "lineNumber"], [335, 20, 338, 29], [336, 10, 338, 29, "columnNumber"], [336, 22, 338, 29], [337, 8, 338, 29], [337, 15, 339, 14], [338, 6, 339, 15], [339, 8, 339, 15, "fileName"], [339, 16, 339, 15], [339, 18, 339, 15, "_jsxFileName"], [339, 30, 339, 15], [340, 8, 339, 15, "lineNumber"], [340, 18, 339, 15], [341, 8, 339, 15, "columnNumber"], [341, 20, 339, 15], [342, 6, 339, 15], [342, 13, 340, 12], [342, 14, 340, 13], [343, 4, 342, 2], [345, 4, 344, 2], [346, 4, 345, 2], [346, 24, 346, 4], [346, 28, 346, 4, "_jsxDevRuntime"], [346, 42, 346, 4], [346, 43, 346, 4, "jsxDEV"], [346, 49, 346, 4], [346, 51, 346, 5, "_View"], [346, 56, 346, 5], [346, 57, 346, 5, "default"], [346, 64, 346, 9], [347, 6, 346, 10, "style"], [347, 11, 346, 15], [347, 13, 346, 17], [348, 8, 346, 19, "flex"], [348, 12, 346, 23], [348, 14, 346, 25], [348, 15, 346, 26], [349, 8, 346, 28, "backgroundColor"], [349, 23, 346, 43], [349, 25, 346, 45, "colors"], [349, 31, 346, 51], [349, 32, 346, 52, "background"], [350, 6, 346, 63], [350, 7, 346, 65], [351, 6, 346, 65, "children"], [351, 14, 346, 65], [351, 30, 347, 6], [351, 34, 347, 6, "_jsxDevRuntime"], [351, 48, 347, 6], [351, 49, 347, 6, "jsxDEV"], [351, 55, 347, 6], [351, 57, 347, 7, "_Header"], [351, 64, 347, 7], [351, 65, 347, 7, "default"], [351, 72, 347, 13], [352, 8, 348, 8, "voiceMode"], [352, 17, 348, 17], [352, 19, 348, 19, "voiceMode"], [352, 28, 348, 29], [353, 8, 349, 8, "onToggleVoiceMode"], [353, 25, 349, 25], [353, 27, 349, 27, "toggleVoiceMode"], [353, 42, 349, 43], [354, 8, 350, 8, "onDone"], [354, 14, 350, 14], [354, 16, 350, 16, "onDone"], [354, 17, 350, 16], [354, 22, 350, 22, "<PERSON><PERSON>"], [354, 36, 350, 27], [354, 37, 350, 28, "alert"], [354, 42, 350, 33], [354, 43, 350, 34], [354, 57, 350, 48], [355, 6, 350, 50], [356, 8, 350, 50, "fileName"], [356, 16, 350, 50], [356, 18, 350, 50, "_jsxFileName"], [356, 30, 350, 50], [357, 8, 350, 50, "lineNumber"], [357, 18, 350, 50], [358, 8, 350, 50, "columnNumber"], [358, 20, 350, 50], [359, 6, 350, 50], [359, 13, 351, 7], [359, 14, 351, 8], [359, 16, 352, 7, "voiceMode"], [359, 25, 352, 16], [359, 41, 353, 8], [359, 45, 353, 8, "_jsxDevRuntime"], [359, 59, 353, 8], [359, 60, 353, 8, "jsxDEV"], [359, 66, 353, 8], [359, 68, 353, 9, "_VoiceMode"], [359, 78, 353, 9], [359, 79, 353, 9, "default"], [359, 86, 353, 18], [360, 8, 354, 10, "isRecording"], [360, 19, 354, 21], [360, 21, 354, 23, "recorderState"], [360, 34, 354, 36], [360, 35, 354, 37, "isRecording"], [360, 46, 354, 49], [361, 8, 355, 10, "hasPermission"], [361, 21, 355, 23], [361, 23, 355, 25, "hasPermission"], [361, 36, 355, 39], [362, 8, 356, 10, "isLoading"], [362, 17, 356, 19], [362, 19, 356, 21, "isLoading"], [362, 28, 356, 31], [363, 8, 357, 10, "transcript"], [363, 18, 357, 20], [363, 20, 357, 22, "transcript"], [363, 30, 357, 33], [364, 8, 358, 10, "isMuted"], [364, 15, 358, 17], [364, 17, 358, 19, "isMuted"], [364, 24, 358, 27], [365, 8, 359, 10, "onMute"], [365, 14, 359, 16], [365, 16, 359, 18, "handleMute"], [366, 6, 359, 29], [367, 8, 359, 29, "fileName"], [367, 16, 359, 29], [367, 18, 359, 29, "_jsxFileName"], [367, 30, 359, 29], [368, 8, 359, 29, "lineNumber"], [368, 18, 359, 29], [369, 8, 359, 29, "columnNumber"], [369, 20, 359, 29], [370, 6, 359, 29], [370, 13, 360, 9], [370, 14, 360, 10], [370, 30, 362, 8], [370, 34, 362, 8, "_jsxDevRuntime"], [370, 48, 362, 8], [370, 49, 362, 8, "jsxDEV"], [370, 55, 362, 8], [370, 57, 362, 9, "_KeyboardAvoidingAnimatedView"], [370, 86, 362, 9], [370, 87, 362, 9, "default"], [370, 94, 362, 37], [371, 8, 362, 38, "style"], [371, 13, 362, 43], [371, 15, 362, 45], [372, 10, 362, 47, "flex"], [372, 14, 362, 51], [372, 16, 362, 53], [373, 8, 362, 55], [373, 9, 362, 57], [374, 8, 362, 57, "children"], [374, 16, 362, 57], [374, 32, 364, 10], [374, 36, 364, 10, "_jsxDevRuntime"], [374, 50, 364, 10], [374, 51, 364, 10, "jsxDEV"], [374, 57, 364, 10], [374, 59, 364, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [374, 70, 364, 11], [374, 71, 364, 11, "default"], [374, 78, 364, 21], [375, 10, 365, 12, "ref"], [375, 13, 365, 15], [375, 15, 365, 17, "scrollViewRef"], [375, 28, 365, 31], [376, 10, 366, 12, "style"], [376, 15, 366, 17], [376, 17, 366, 19], [377, 12, 366, 21, "flex"], [377, 16, 366, 25], [377, 18, 366, 27], [378, 10, 366, 29], [378, 11, 366, 31], [379, 10, 367, 12, "contentContainerStyle"], [379, 31, 367, 33], [379, 33, 367, 35], [380, 12, 368, 14, "paddingHorizontal"], [380, 29, 368, 31], [380, 31, 368, 33], [380, 33, 368, 35], [381, 12, 369, 14, "paddingVertical"], [381, 27, 369, 29], [381, 29, 369, 31], [381, 31, 369, 33], [382, 12, 370, 14, "paddingBottom"], [382, 25, 370, 27], [382, 27, 370, 29], [382, 30, 370, 32], [382, 31, 370, 34], [383, 10, 371, 12], [383, 11, 371, 14], [384, 10, 372, 12, "showsVerticalScrollIndicator"], [384, 38, 372, 40], [384, 40, 372, 42], [384, 45, 372, 48], [385, 10, 372, 48, "children"], [385, 18, 372, 48], [385, 21, 373, 13, "messages"], [385, 29, 373, 21], [385, 30, 373, 22, "map"], [385, 33, 373, 25], [385, 34, 373, 26], [385, 35, 373, 27, "message"], [385, 42, 373, 34], [385, 44, 373, 36, "index"], [385, 49, 373, 41], [385, 67, 374, 14], [385, 71, 374, 14, "_jsxDevRuntime"], [385, 85, 374, 14], [385, 86, 374, 14, "jsxDEV"], [385, 92, 374, 14], [385, 94, 374, 15, "_MessageBubble"], [385, 108, 374, 15], [385, 109, 374, 15, "default"], [385, 116, 374, 28], [386, 12, 374, 41, "message"], [386, 19, 374, 48], [386, 21, 374, 50, "message"], [386, 28, 374, 58], [387, 12, 374, 59, "onLongPress"], [387, 23, 374, 70], [387, 25, 374, 72, "handleLongPress"], [388, 10, 374, 88], [388, 13, 374, 34, "index"], [388, 18, 374, 39], [389, 12, 374, 39, "fileName"], [389, 20, 374, 39], [389, 22, 374, 39, "_jsxFileName"], [389, 34, 374, 39], [390, 12, 374, 39, "lineNumber"], [390, 22, 374, 39], [391, 12, 374, 39, "columnNumber"], [391, 24, 374, 39], [392, 10, 374, 39], [392, 17, 374, 90], [392, 18, 375, 13], [392, 19, 375, 14], [392, 21, 378, 13, "isLoading"], [392, 30, 378, 22], [392, 47, 379, 14], [392, 51, 379, 14, "_jsxDevRuntime"], [392, 65, 379, 14], [392, 66, 379, 14, "jsxDEV"], [392, 72, 379, 14], [392, 74, 379, 15, "_View"], [392, 79, 379, 15], [392, 80, 379, 15, "default"], [392, 87, 379, 19], [393, 12, 380, 16, "style"], [393, 17, 380, 21], [393, 19, 380, 23], [394, 14, 381, 18, "marginBottom"], [394, 26, 381, 30], [394, 28, 381, 32], [394, 30, 381, 34], [395, 14, 382, 18, "alignSelf"], [395, 23, 382, 27], [395, 25, 382, 29], [395, 37, 382, 41], [396, 14, 383, 18, "max<PERSON><PERSON><PERSON>"], [396, 22, 383, 26], [396, 24, 383, 28], [397, 12, 384, 16], [397, 13, 384, 18], [398, 12, 384, 18, "children"], [398, 20, 384, 18], [398, 35, 385, 16], [398, 39, 385, 16, "_jsxDevRuntime"], [398, 53, 385, 16], [398, 54, 385, 16, "jsxDEV"], [398, 60, 385, 16], [398, 62, 385, 17, "_View"], [398, 67, 385, 17], [398, 68, 385, 17, "default"], [398, 75, 385, 21], [399, 14, 386, 18, "style"], [399, 19, 386, 23], [399, 21, 386, 25], [400, 16, 387, 20, "backgroundColor"], [400, 31, 387, 35], [400, 33, 387, 37, "colors"], [400, 39, 387, 43], [400, 40, 387, 44, "cardBackground"], [400, 54, 387, 58], [401, 16, 388, 20, "borderRadius"], [401, 28, 388, 32], [401, 30, 388, 34], [401, 32, 388, 36], [402, 16, 389, 20, "paddingHorizontal"], [402, 33, 389, 37], [402, 35, 389, 39], [402, 37, 389, 41], [403, 16, 390, 20, "paddingVertical"], [403, 31, 390, 35], [403, 33, 390, 37], [403, 35, 390, 39], [404, 16, 391, 20, "borderWidth"], [404, 27, 391, 31], [404, 29, 391, 33], [404, 30, 391, 34], [405, 16, 392, 20, "borderColor"], [405, 27, 392, 31], [405, 29, 392, 33, "colors"], [405, 35, 392, 39], [405, 36, 392, 40, "outline"], [406, 14, 393, 18], [406, 15, 393, 20], [407, 14, 393, 20, "children"], [407, 22, 393, 20], [407, 37, 394, 18], [407, 41, 394, 18, "_jsxDevRuntime"], [407, 55, 394, 18], [407, 56, 394, 18, "jsxDEV"], [407, 62, 394, 18], [407, 64, 394, 19, "_Text"], [407, 69, 394, 19], [407, 70, 394, 19, "default"], [407, 77, 394, 23], [408, 16, 395, 20, "style"], [408, 21, 395, 25], [408, 23, 395, 27], [409, 18, 396, 22, "fontSize"], [409, 26, 396, 30], [409, 28, 396, 32], [409, 30, 396, 34], [410, 18, 397, 22, "fontFamily"], [410, 28, 397, 32], [410, 30, 397, 34], [410, 50, 397, 54], [411, 18, 398, 22, "color"], [411, 23, 398, 27], [411, 25, 398, 29, "colors"], [411, 31, 398, 35], [411, 32, 398, 36, "textSecondary"], [411, 45, 398, 49], [412, 18, 399, 22, "lineHeight"], [412, 28, 399, 32], [412, 30, 399, 34], [413, 16, 400, 20], [413, 17, 400, 22], [414, 16, 400, 22, "children"], [414, 24, 400, 22], [414, 26, 400, 23], [415, 14, 402, 18], [416, 16, 402, 18, "fileName"], [416, 24, 402, 18], [416, 26, 402, 18, "_jsxFileName"], [416, 38, 402, 18], [417, 16, 402, 18, "lineNumber"], [417, 26, 402, 18], [418, 16, 402, 18, "columnNumber"], [418, 28, 402, 18], [419, 14, 402, 18], [419, 21, 402, 24], [420, 12, 402, 25], [421, 14, 402, 25, "fileName"], [421, 22, 402, 25], [421, 24, 402, 25, "_jsxFileName"], [421, 36, 402, 25], [422, 14, 402, 25, "lineNumber"], [422, 24, 402, 25], [423, 14, 402, 25, "columnNumber"], [423, 26, 402, 25], [424, 12, 402, 25], [424, 19, 403, 22], [425, 10, 403, 23], [426, 12, 403, 23, "fileName"], [426, 20, 403, 23], [426, 22, 403, 23, "_jsxFileName"], [426, 34, 403, 23], [427, 12, 403, 23, "lineNumber"], [427, 22, 403, 23], [428, 12, 403, 23, "columnNumber"], [428, 24, 403, 23], [429, 10, 403, 23], [429, 17, 404, 20], [429, 18, 405, 13], [430, 8, 405, 13], [431, 10, 405, 13, "fileName"], [431, 18, 405, 13], [431, 20, 405, 13, "_jsxFileName"], [431, 32, 405, 13], [432, 10, 405, 13, "lineNumber"], [432, 20, 405, 13], [433, 10, 405, 13, "columnNumber"], [433, 22, 405, 13], [434, 8, 405, 13], [434, 15, 406, 22], [434, 16, 406, 23], [434, 18, 409, 11, "quickActions"], [434, 30, 409, 23], [434, 31, 409, 24, "length"], [434, 37, 409, 30], [434, 40, 409, 33], [434, 41, 409, 34], [434, 58, 410, 12], [434, 62, 410, 12, "_jsxDevRuntime"], [434, 76, 410, 12], [434, 77, 410, 12, "jsxDEV"], [434, 83, 410, 12], [434, 85, 410, 13, "_View"], [434, 90, 410, 13], [434, 91, 410, 13, "default"], [434, 98, 410, 17], [435, 10, 411, 14, "style"], [435, 15, 411, 19], [435, 17, 411, 21], [436, 12, 412, 16, "position"], [436, 20, 412, 24], [436, 22, 412, 26], [436, 32, 412, 36], [437, 12, 413, 16, "bottom"], [437, 18, 413, 22], [437, 20, 413, 24, "insets"], [437, 26, 413, 30], [437, 27, 413, 31, "bottom"], [437, 33, 413, 37], [437, 36, 413, 40], [437, 39, 413, 43], [438, 12, 414, 16, "left"], [438, 16, 414, 20], [438, 18, 414, 22], [438, 20, 414, 24], [439, 12, 415, 16, "right"], [439, 17, 415, 21], [439, 19, 415, 23], [440, 10, 416, 14], [440, 11, 416, 16], [441, 10, 416, 16, "children"], [441, 18, 416, 16], [441, 33, 417, 14], [441, 37, 417, 14, "_jsxDevRuntime"], [441, 51, 417, 14], [441, 52, 417, 14, "jsxDEV"], [441, 58, 417, 14], [441, 60, 417, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [441, 71, 417, 15], [441, 72, 417, 15, "default"], [441, 79, 417, 25], [442, 12, 418, 16, "horizontal"], [442, 22, 418, 26], [443, 12, 419, 16, "showsHorizontalScrollIndicator"], [443, 42, 419, 46], [443, 44, 419, 48], [443, 49, 419, 54], [444, 12, 420, 16, "contentContainerStyle"], [444, 33, 420, 37], [444, 35, 420, 39], [445, 14, 420, 41, "gap"], [445, 17, 420, 44], [445, 19, 420, 46], [446, 12, 420, 48], [446, 13, 420, 50], [447, 12, 420, 50, "children"], [447, 20, 420, 50], [447, 22, 421, 17, "quickActions"], [447, 34, 421, 29], [447, 35, 421, 30, "map"], [447, 38, 421, 33], [447, 39, 421, 34], [447, 40, 421, 35, "action"], [447, 46, 421, 41], [447, 48, 421, 43, "index"], [447, 53, 421, 48], [447, 71, 422, 18], [447, 75, 422, 18, "_jsxDevRuntime"], [447, 89, 422, 18], [447, 90, 422, 18, "jsxDEV"], [447, 96, 422, 18], [447, 98, 422, 19, "_TouchableOpacity"], [447, 115, 422, 19], [447, 116, 422, 19, "default"], [447, 123, 422, 35], [448, 14, 424, 20, "style"], [448, 19, 424, 25], [448, 21, 424, 27], [449, 16, 425, 22, "backgroundColor"], [449, 31, 425, 37], [449, 33, 425, 39, "colors"], [449, 39, 425, 45], [449, 40, 425, 46, "primaryUltraLight"], [449, 57, 425, 63], [450, 16, 426, 22, "borderRadius"], [450, 28, 426, 34], [450, 30, 426, 36], [450, 32, 426, 38], [451, 16, 427, 22, "paddingHorizontal"], [451, 33, 427, 39], [451, 35, 427, 41], [451, 37, 427, 43], [452, 16, 428, 22, "paddingVertical"], [452, 31, 428, 37], [452, 33, 428, 39], [452, 34, 428, 40], [453, 16, 429, 22, "borderWidth"], [453, 27, 429, 33], [453, 29, 429, 35], [453, 30, 429, 36], [454, 16, 430, 22, "borderColor"], [454, 27, 430, 33], [454, 29, 430, 35, "colors"], [454, 35, 430, 41], [454, 36, 430, 42, "primary"], [455, 14, 431, 20], [455, 15, 431, 22], [456, 14, 432, 20, "onPress"], [456, 21, 432, 27], [456, 23, 432, 29, "onPress"], [456, 24, 432, 29], [456, 29, 432, 35, "handleQuickAction"], [456, 46, 432, 52], [456, 47, 432, 53, "action"], [456, 53, 432, 59], [456, 54, 432, 61], [457, 14, 432, 61, "children"], [457, 22, 432, 61], [457, 37, 433, 20], [457, 41, 433, 20, "_jsxDevRuntime"], [457, 55, 433, 20], [457, 56, 433, 20, "jsxDEV"], [457, 62, 433, 20], [457, 64, 433, 21, "_Text"], [457, 69, 433, 21], [457, 70, 433, 21, "default"], [457, 77, 433, 25], [458, 16, 434, 22, "style"], [458, 21, 434, 27], [458, 23, 434, 29], [459, 18, 435, 24, "fontSize"], [459, 26, 435, 32], [459, 28, 435, 34], [459, 30, 435, 36], [460, 18, 436, 24, "fontFamily"], [460, 28, 436, 34], [460, 30, 436, 36], [460, 49, 436, 55], [461, 18, 437, 24, "color"], [461, 23, 437, 29], [461, 25, 437, 31, "colors"], [461, 31, 437, 37], [461, 32, 437, 38, "primary"], [462, 16, 438, 22], [462, 17, 438, 24], [463, 16, 438, 24, "children"], [463, 24, 438, 24], [463, 26, 439, 23, "action"], [464, 14, 439, 29], [465, 16, 439, 29, "fileName"], [465, 24, 439, 29], [465, 26, 439, 29, "_jsxFileName"], [465, 38, 439, 29], [466, 16, 439, 29, "lineNumber"], [466, 26, 439, 29], [467, 16, 439, 29, "columnNumber"], [467, 28, 439, 29], [468, 14, 439, 29], [468, 21, 440, 26], [469, 12, 440, 27], [469, 15, 423, 25, "index"], [469, 20, 423, 30], [470, 14, 423, 30, "fileName"], [470, 22, 423, 30], [470, 24, 423, 30, "_jsxFileName"], [470, 36, 423, 30], [471, 14, 423, 30, "lineNumber"], [471, 24, 423, 30], [472, 14, 423, 30, "columnNumber"], [472, 26, 423, 30], [473, 12, 423, 30], [473, 19, 441, 36], [473, 20, 442, 17], [474, 10, 442, 18], [475, 12, 442, 18, "fileName"], [475, 20, 442, 18], [475, 22, 442, 18, "_jsxFileName"], [475, 34, 442, 18], [476, 12, 442, 18, "lineNumber"], [476, 22, 442, 18], [477, 12, 442, 18, "columnNumber"], [477, 24, 442, 18], [478, 10, 442, 18], [478, 17, 443, 26], [479, 8, 443, 27], [480, 10, 443, 27, "fileName"], [480, 18, 443, 27], [480, 20, 443, 27, "_jsxFileName"], [480, 32, 443, 27], [481, 10, 443, 27, "lineNumber"], [481, 20, 443, 27], [482, 10, 443, 27, "columnNumber"], [482, 22, 443, 27], [483, 8, 443, 27], [483, 15, 444, 18], [483, 16, 445, 11], [483, 31, 448, 10], [483, 35, 448, 10, "_jsxDevRuntime"], [483, 49, 448, 10], [483, 50, 448, 10, "jsxDEV"], [483, 56, 448, 10], [483, 58, 448, 11, "_View"], [483, 63, 448, 11], [483, 64, 448, 11, "default"], [483, 71, 448, 15], [484, 10, 449, 12, "style"], [484, 15, 449, 17], [484, 17, 449, 19], [485, 12, 450, 14, "position"], [485, 20, 450, 22], [485, 22, 450, 24], [485, 32, 450, 34], [486, 12, 451, 14, "bottom"], [486, 18, 451, 20], [486, 20, 451, 22], [486, 21, 451, 23], [487, 12, 452, 14, "left"], [487, 16, 452, 18], [487, 18, 452, 20], [487, 19, 452, 21], [488, 12, 453, 14, "right"], [488, 17, 453, 19], [488, 19, 453, 21], [489, 10, 454, 12], [489, 11, 454, 14], [490, 10, 454, 14, "children"], [490, 18, 454, 14], [490, 33, 455, 12], [490, 37, 455, 12, "_jsxDevRuntime"], [490, 51, 455, 12], [490, 52, 455, 12, "jsxDEV"], [490, 58, 455, 12], [490, 60, 455, 13, "_TextMode"], [490, 69, 455, 13], [490, 70, 455, 13, "default"], [490, 77, 455, 21], [491, 12, 456, 14, "inputText"], [491, 21, 456, 23], [491, 23, 456, 25, "inputText"], [491, 32, 456, 35], [492, 12, 457, 14, "onInputChange"], [492, 25, 457, 27], [492, 27, 457, 29, "setInputText"], [492, 39, 457, 42], [493, 12, 458, 14, "onSendMessage"], [493, 25, 458, 27], [493, 27, 458, 29, "onSendMessage"], [493, 28, 458, 29], [493, 33, 458, 35, "handleSendMessage"], [493, 50, 458, 52], [493, 51, 458, 53], [493, 52, 458, 55], [494, 12, 459, 14, "onStartDictation"], [494, 28, 459, 30], [494, 30, 459, 32, "handleDictation"], [495, 10, 459, 48], [496, 12, 459, 48, "fileName"], [496, 20, 459, 48], [496, 22, 459, 48, "_jsxFileName"], [496, 34, 459, 48], [497, 12, 459, 48, "lineNumber"], [497, 22, 459, 48], [498, 12, 459, 48, "columnNumber"], [498, 24, 459, 48], [499, 10, 459, 48], [499, 17, 460, 13], [500, 8, 460, 14], [501, 10, 460, 14, "fileName"], [501, 18, 460, 14], [501, 20, 460, 14, "_jsxFileName"], [501, 32, 460, 14], [502, 10, 460, 14, "lineNumber"], [502, 20, 460, 14], [503, 10, 460, 14, "columnNumber"], [503, 22, 460, 14], [504, 8, 460, 14], [504, 15, 461, 16], [504, 16, 461, 17], [505, 6, 461, 17], [506, 8, 461, 17, "fileName"], [506, 16, 461, 17], [506, 18, 461, 17, "_jsxFileName"], [506, 30, 461, 17], [507, 8, 461, 17, "lineNumber"], [507, 18, 461, 17], [508, 8, 461, 17, "columnNumber"], [508, 20, 461, 17], [509, 6, 461, 17], [509, 13, 462, 38], [509, 14, 463, 7], [509, 29, 466, 6], [509, 33, 466, 6, "_jsxDevRuntime"], [509, 47, 466, 6], [509, 48, 466, 6, "jsxDEV"], [509, 54, 466, 6], [509, 56, 466, 7, "_Modal"], [509, 62, 466, 7], [509, 63, 466, 7, "default"], [509, 70, 466, 12], [510, 8, 467, 8, "transparent"], [510, 19, 467, 19], [511, 8, 468, 8, "visible"], [511, 15, 468, 15], [511, 17, 468, 17, "isContextMenuVisible"], [511, 37, 468, 38], [512, 8, 469, 8, "onRequestClose"], [512, 22, 469, 22], [512, 24, 469, 24, "onRequestClose"], [512, 25, 469, 24], [512, 30, 469, 30, "setIsContextMenuVisible"], [512, 53, 469, 53], [512, 54, 469, 54], [512, 59, 469, 59], [512, 60, 469, 61], [513, 8, 469, 61, "children"], [513, 16, 469, 61], [513, 31, 471, 8], [513, 35, 471, 8, "_jsxDevRuntime"], [513, 49, 471, 8], [513, 50, 471, 8, "jsxDEV"], [513, 56, 471, 8], [513, 58, 471, 9, "_TouchableOpacity"], [513, 75, 471, 9], [513, 76, 471, 9, "default"], [513, 83, 471, 25], [514, 10, 472, 10, "style"], [514, 15, 472, 15], [514, 17, 472, 17], [515, 12, 472, 19, "flex"], [515, 16, 472, 23], [515, 18, 472, 25], [515, 19, 472, 26], [516, 12, 472, 28, "backgroundColor"], [516, 27, 472, 43], [516, 29, 472, 45], [516, 46, 472, 62], [517, 12, 472, 64, "justifyContent"], [517, 26, 472, 78], [517, 28, 472, 80], [517, 36, 472, 88], [518, 12, 472, 90, "alignItems"], [518, 22, 472, 100], [518, 24, 472, 102], [519, 10, 472, 111], [519, 11, 472, 113], [520, 10, 473, 10, "activeOpacity"], [520, 23, 473, 23], [520, 25, 473, 25], [520, 26, 473, 27], [521, 10, 474, 10, "onPressOut"], [521, 20, 474, 20], [521, 22, 474, 22, "onPressOut"], [521, 23, 474, 22], [521, 28, 474, 28, "setIsContextMenuVisible"], [521, 51, 474, 51], [521, 52, 474, 52], [521, 57, 474, 57], [521, 58, 474, 59], [522, 10, 474, 59, "children"], [522, 18, 474, 59], [522, 33, 476, 10], [522, 37, 476, 10, "_jsxDevRuntime"], [522, 51, 476, 10], [522, 52, 476, 10, "jsxDEV"], [522, 58, 476, 10], [522, 60, 476, 11, "_View"], [522, 65, 476, 11], [522, 66, 476, 11, "default"], [522, 73, 476, 15], [523, 12, 476, 16, "style"], [523, 17, 476, 21], [523, 19, 476, 23], [524, 14, 476, 25, "backgroundColor"], [524, 29, 476, 40], [524, 31, 476, 42, "colors"], [524, 37, 476, 48], [524, 38, 476, 49, "background"], [524, 48, 476, 59], [525, 14, 476, 61, "borderRadius"], [525, 26, 476, 73], [525, 28, 476, 75], [525, 30, 476, 77], [526, 14, 476, 79, "padding"], [526, 21, 476, 86], [526, 23, 476, 88], [526, 25, 476, 90], [527, 14, 476, 92, "width"], [527, 19, 476, 97], [527, 21, 476, 99], [528, 12, 476, 105], [528, 13, 476, 107], [529, 12, 476, 107, "children"], [529, 20, 476, 107], [529, 36, 477, 12], [529, 40, 477, 12, "_jsxDevRuntime"], [529, 54, 477, 12], [529, 55, 477, 12, "jsxDEV"], [529, 61, 477, 12], [529, 63, 477, 13, "_TouchableOpacity"], [529, 80, 477, 13], [529, 81, 477, 13, "default"], [529, 88, 477, 29], [530, 14, 477, 30, "onPress"], [530, 21, 477, 37], [530, 23, 477, 39, "handleCopyMessage"], [530, 40, 477, 57], [531, 14, 477, 58, "style"], [531, 19, 477, 63], [531, 21, 477, 65], [532, 16, 477, 67, "paddingVertical"], [532, 31, 477, 82], [532, 33, 477, 84], [533, 14, 477, 87], [533, 15, 477, 89], [534, 14, 477, 89, "children"], [534, 22, 477, 89], [534, 37, 478, 14], [534, 41, 478, 14, "_jsxDevRuntime"], [534, 55, 478, 14], [534, 56, 478, 14, "jsxDEV"], [534, 62, 478, 14], [534, 64, 478, 15, "_Text"], [534, 69, 478, 15], [534, 70, 478, 15, "default"], [534, 77, 478, 19], [535, 16, 478, 20, "style"], [535, 21, 478, 25], [535, 23, 478, 27], [536, 18, 478, 29, "fontSize"], [536, 26, 478, 37], [536, 28, 478, 39], [536, 30, 478, 41], [537, 18, 478, 43, "fontFamily"], [537, 28, 478, 53], [537, 30, 478, 55], [537, 49, 478, 74], [538, 18, 478, 76, "color"], [538, 23, 478, 81], [538, 25, 478, 83, "colors"], [538, 31, 478, 89], [538, 32, 478, 90, "text"], [539, 16, 478, 95], [539, 17, 478, 97], [540, 16, 478, 97, "children"], [540, 24, 478, 97], [540, 26, 478, 98], [541, 14, 478, 110], [542, 16, 478, 110, "fileName"], [542, 24, 478, 110], [542, 26, 478, 110, "_jsxFileName"], [542, 38, 478, 110], [543, 16, 478, 110, "lineNumber"], [543, 26, 478, 110], [544, 16, 478, 110, "columnNumber"], [544, 28, 478, 110], [545, 14, 478, 110], [545, 21, 478, 116], [546, 12, 478, 117], [547, 14, 478, 117, "fileName"], [547, 22, 478, 117], [547, 24, 478, 117, "_jsxFileName"], [547, 36, 478, 117], [548, 14, 478, 117, "lineNumber"], [548, 24, 478, 117], [549, 14, 478, 117, "columnNumber"], [549, 26, 478, 117], [550, 12, 478, 117], [550, 19, 479, 30], [550, 20, 479, 31], [550, 22, 480, 13, "longPressedMessage"], [550, 40, 480, 31], [550, 42, 480, 33, "role"], [550, 46, 480, 37], [550, 51, 480, 42], [550, 62, 480, 53], [550, 79, 481, 14], [550, 83, 481, 14, "_jsxDevRuntime"], [550, 97, 481, 14], [550, 98, 481, 14, "jsxDEV"], [550, 104, 481, 14], [550, 106, 481, 15, "_TouchableOpacity"], [550, 123, 481, 15], [550, 124, 481, 15, "default"], [550, 131, 481, 31], [551, 14, 481, 32, "onPress"], [551, 21, 481, 39], [551, 23, 481, 41, "handleListenToMessage"], [551, 44, 481, 63], [552, 14, 481, 64, "style"], [552, 19, 481, 69], [552, 21, 481, 71], [553, 16, 481, 73, "paddingVertical"], [553, 31, 481, 88], [553, 33, 481, 90], [554, 14, 481, 93], [554, 15, 481, 95], [555, 14, 481, 95, "children"], [555, 22, 481, 95], [555, 37, 482, 16], [555, 41, 482, 16, "_jsxDevRuntime"], [555, 55, 482, 16], [555, 56, 482, 16, "jsxDEV"], [555, 62, 482, 16], [555, 64, 482, 17, "_Text"], [555, 69, 482, 17], [555, 70, 482, 17, "default"], [555, 77, 482, 21], [556, 16, 482, 22, "style"], [556, 21, 482, 27], [556, 23, 482, 29], [557, 18, 482, 31, "fontSize"], [557, 26, 482, 39], [557, 28, 482, 41], [557, 30, 482, 43], [558, 18, 482, 45, "fontFamily"], [558, 28, 482, 55], [558, 30, 482, 57], [558, 49, 482, 76], [559, 18, 482, 78, "color"], [559, 23, 482, 83], [559, 25, 482, 85, "colors"], [559, 31, 482, 91], [559, 32, 482, 92, "text"], [560, 16, 482, 97], [560, 17, 482, 99], [561, 16, 482, 99, "children"], [561, 24, 482, 99], [561, 26, 482, 100], [562, 14, 482, 117], [563, 16, 482, 117, "fileName"], [563, 24, 482, 117], [563, 26, 482, 117, "_jsxFileName"], [563, 38, 482, 117], [564, 16, 482, 117, "lineNumber"], [564, 26, 482, 117], [565, 16, 482, 117, "columnNumber"], [565, 28, 482, 117], [566, 14, 482, 117], [566, 21, 482, 123], [567, 12, 482, 124], [568, 14, 482, 124, "fileName"], [568, 22, 482, 124], [568, 24, 482, 124, "_jsxFileName"], [568, 36, 482, 124], [569, 14, 482, 124, "lineNumber"], [569, 24, 482, 124], [570, 14, 482, 124, "columnNumber"], [570, 26, 482, 124], [571, 12, 482, 124], [571, 19, 483, 32], [571, 20, 484, 13], [572, 10, 484, 13], [573, 12, 484, 13, "fileName"], [573, 20, 484, 13], [573, 22, 484, 13, "_jsxFileName"], [573, 34, 484, 13], [574, 12, 484, 13, "lineNumber"], [574, 22, 484, 13], [575, 12, 484, 13, "columnNumber"], [575, 24, 484, 13], [576, 10, 484, 13], [576, 17, 485, 16], [577, 8, 485, 17], [578, 10, 485, 17, "fileName"], [578, 18, 485, 17], [578, 20, 485, 17, "_jsxFileName"], [578, 32, 485, 17], [579, 10, 485, 17, "lineNumber"], [579, 20, 485, 17], [580, 10, 485, 17, "columnNumber"], [580, 22, 485, 17], [581, 8, 485, 17], [581, 15, 486, 26], [582, 6, 486, 27], [583, 8, 486, 27, "fileName"], [583, 16, 486, 27], [583, 18, 486, 27, "_jsxFileName"], [583, 30, 486, 27], [584, 8, 486, 27, "lineNumber"], [584, 18, 486, 27], [585, 8, 486, 27, "columnNumber"], [585, 20, 486, 27], [586, 6, 486, 27], [586, 13, 487, 13], [586, 14, 487, 14], [587, 4, 487, 14], [588, 6, 487, 14, "fileName"], [588, 14, 487, 14], [588, 16, 487, 14, "_jsxFileName"], [588, 28, 487, 14], [589, 6, 487, 14, "lineNumber"], [589, 16, 487, 14], [590, 6, 487, 14, "columnNumber"], [590, 18, 487, 14], [591, 4, 487, 14], [591, 11, 488, 10], [591, 12, 488, 11], [592, 2, 490, 0], [593, 2, 490, 1, "_s"], [593, 4, 490, 1], [593, 5, 34, 24, "BrainstormScreen"], [593, 21, 34, 40], [594, 4, 34, 40], [594, 12, 35, 17, "useSafeAreaInsets"], [594, 57, 35, 34], [594, 59, 36, 17, "useColors"], [594, 79, 36, 26], [594, 81, 37, 24, "useFonts"], [594, 98, 37, 32], [594, 100, 43, 19, "useAudioRecorder"], [594, 127, 43, 35], [594, 129, 44, 24, "useAudioRecorderState"], [594, 161, 44, 45], [595, 2, 44, 45], [596, 2, 44, 45, "_c"], [596, 4, 44, 45], [596, 7, 34, 24, "BrainstormScreen"], [596, 23, 34, 40], [597, 2, 34, 40], [597, 6, 34, 40, "_c"], [597, 8, 34, 40], [598, 2, 34, 40, "$RefreshReg$"], [598, 14, 34, 40], [598, 15, 34, 40, "_c"], [598, 17, 34, 40], [599, 0, 34, 40], [599, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCiC;YC4B;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJK;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;YCI;GDI;gBiB0F,iCjB;0BkBuB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}