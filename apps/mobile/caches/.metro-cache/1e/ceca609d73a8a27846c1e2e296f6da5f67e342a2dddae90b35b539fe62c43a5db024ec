{"dependencies": [{"name": "./PlatformUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "vk5TSZJTws6vRpll7frKCvmMWgw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getLocalAssetUri = getLocalAssetUri;\n  var _PlatformUtils = require(_dependencyMap[0], \"./PlatformUtils\");\n  // localAssets are provided by the expo-updates module\n  var localAssets = (0, _PlatformUtils.getLocalAssets)();\n  /**\n   * Returns the URI of a local asset from its hash, or null if the asset is not available locally\n   */\n  function getLocalAssetUri(hash, type) {\n    var localAssetsKey = hash;\n    var legacyLocalAssetsKey = `${hash}.${type ?? ''}`;\n    switch (true) {\n      case localAssetsKey in localAssets:\n        {\n          return localAssets[localAssetsKey];\n        }\n      case legacyLocalAssetsKey in localAssets:\n        {\n          // legacy updates store assets with an extension\n          return localAssets[legacyLocalAssetsKey];\n        }\n      default:\n        return null;\n    }\n  }\n});", "lineCount": 29, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_PlatformUtils"], [6, 20, 1, 0], [6, 23, 1, 0, "require"], [6, 30, 1, 0], [6, 31, 1, 0, "_dependencyMap"], [6, 45, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [8, 6, 3, 6, "localAssets"], [8, 17, 3, 17], [8, 20, 3, 20], [8, 24, 3, 20, "getLocalAssets"], [8, 53, 3, 34], [8, 55, 3, 35], [8, 56, 3, 36], [9, 2, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 2, 7, 7], [12, 11, 7, 16, "getLocalAssetUri"], [12, 27, 7, 32, "getLocalAssetUri"], [12, 28, 7, 33, "hash"], [12, 32, 7, 37], [12, 34, 7, 39, "type"], [12, 38, 7, 43], [12, 40, 7, 45], [13, 4, 8, 4], [13, 8, 8, 10, "localAssetsKey"], [13, 22, 8, 24], [13, 25, 8, 27, "hash"], [13, 29, 8, 31], [14, 4, 9, 4], [14, 8, 9, 10, "legacyLocalAssetsKey"], [14, 28, 9, 30], [14, 31, 9, 33], [14, 34, 9, 36, "hash"], [14, 38, 9, 40], [14, 42, 9, 44, "type"], [14, 46, 9, 48], [14, 50, 9, 52], [14, 52, 9, 54], [14, 54, 9, 56], [15, 4, 10, 4], [15, 12, 10, 12], [15, 16, 10, 16], [16, 6, 11, 8], [16, 11, 11, 13, "localAssetsKey"], [16, 25, 11, 27], [16, 29, 11, 31, "localAssets"], [16, 40, 11, 42], [17, 8, 11, 44], [18, 10, 12, 12], [18, 17, 12, 19, "localAssets"], [18, 28, 12, 30], [18, 29, 12, 31, "localAssetsKey"], [18, 43, 12, 45], [18, 44, 12, 46], [19, 8, 13, 8], [20, 6, 14, 8], [20, 11, 14, 13, "legacyLocalAssetsKey"], [20, 31, 14, 33], [20, 35, 14, 37, "localAssets"], [20, 46, 14, 48], [21, 8, 14, 50], [22, 10, 15, 12], [23, 10, 16, 12], [23, 17, 16, 19, "localAssets"], [23, 28, 16, 30], [23, 29, 16, 31, "legacyLocalAssetsKey"], [23, 49, 16, 51], [23, 50, 16, 52], [24, 8, 17, 8], [25, 6, 18, 8], [26, 8, 19, 12], [26, 15, 19, 19], [26, 19, 19, 23], [27, 4, 20, 4], [28, 2, 21, 0], [29, 0, 21, 1], [29, 3]], "functionMap": {"names": ["<global>", "getLocalAssetUri"], "mappings": "AAA;OCM;CDc"}}, "type": "js/module"}]}