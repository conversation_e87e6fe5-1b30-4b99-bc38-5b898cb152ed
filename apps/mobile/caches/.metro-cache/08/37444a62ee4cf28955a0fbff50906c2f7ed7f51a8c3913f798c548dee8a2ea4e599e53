{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./create-icon-set", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 46, "index": 46}}], "key": "ykDYFotwaBIIQE+Hu52dtn65pqo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createIconSetFromFontello;\n  var _createIconSet = _interopRequireDefault(require(_dependencyMap[1], \"./create-icon-set\"));\n  function createIconSetFromFontello(config, fontFamilyArg, fontFile) {\n    const glyphMap = {};\n    config.glyphs.forEach(glyph => {\n      glyphMap[glyph.css] = glyph.code;\n    });\n    const fontFamily = fontFamilyArg || config.name || 'fontello';\n    return (0, _createIconSet.default)(glyphMap, fontFamily, fontFile || `${fontFamily}.ttf`);\n  }\n});", "lineCount": 16, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_createIconSet"], [7, 20, 1, 0], [7, 23, 1, 0, "_interopRequireDefault"], [7, 45, 1, 0], [7, 46, 1, 0, "require"], [7, 53, 1, 0], [7, 54, 1, 0, "_dependencyMap"], [7, 68, 1, 0], [8, 2, 3, 15], [8, 11, 3, 24, "createIconSetFromFontello"], [8, 36, 3, 49, "createIconSetFromFontello"], [8, 37, 4, 2, "config"], [8, 43, 4, 8], [8, 45, 5, 2, "fontFamilyArg"], [8, 58, 5, 15], [8, 60, 6, 2, "fontFile"], [8, 68, 6, 10], [8, 70, 7, 2], [9, 4, 8, 2], [9, 10, 8, 8, "glyphMap"], [9, 18, 8, 16], [9, 21, 8, 19], [9, 22, 8, 20], [9, 23, 8, 21], [10, 4, 9, 2, "config"], [10, 10, 9, 8], [10, 11, 9, 9, "glyphs"], [10, 17, 9, 15], [10, 18, 9, 16, "for<PERSON>ach"], [10, 25, 9, 23], [10, 26, 9, 24, "glyph"], [10, 31, 9, 29], [10, 35, 9, 33], [11, 6, 10, 4, "glyphMap"], [11, 14, 10, 12], [11, 15, 10, 13, "glyph"], [11, 20, 10, 18], [11, 21, 10, 19, "css"], [11, 24, 10, 22], [11, 25, 10, 23], [11, 28, 10, 26, "glyph"], [11, 33, 10, 31], [11, 34, 10, 32, "code"], [11, 38, 10, 36], [12, 4, 11, 2], [12, 5, 11, 3], [12, 6, 11, 4], [13, 4, 13, 2], [13, 10, 13, 8, "fontFamily"], [13, 20, 13, 18], [13, 23, 13, 21, "fontFamilyArg"], [13, 36, 13, 34], [13, 40, 13, 38, "config"], [13, 46, 13, 44], [13, 47, 13, 45, "name"], [13, 51, 13, 49], [13, 55, 13, 53], [13, 65, 13, 63], [14, 4, 15, 2], [14, 11, 15, 9], [14, 15, 15, 9, "createIconSet"], [14, 37, 15, 22], [14, 39, 15, 23, "glyphMap"], [14, 47, 15, 31], [14, 49, 15, 33, "fontFamily"], [14, 59, 15, 43], [14, 61, 15, 45, "fontFile"], [14, 69, 15, 53], [14, 73, 15, 57], [14, 76, 15, 60, "fontFamily"], [14, 86, 15, 70], [14, 92, 15, 76], [14, 93, 15, 77], [15, 2, 16, 0], [16, 0, 16, 1], [16, 3]], "functionMap": {"names": ["<global>", "createIconSetFromFontello", "config.glyphs.forEach$argument_0"], "mappings": "AAA;eCE;wBCM;GDE;CDK"}}, "type": "js/module"}]}