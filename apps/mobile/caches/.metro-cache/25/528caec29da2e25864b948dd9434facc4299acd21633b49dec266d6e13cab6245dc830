{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hide = hide;\n  exports.hideAsync = hideAsync;\n  exports.preventAutoHideAsync = preventAutoHideAsync;\n  exports.setOptions = setOptions;\n  // @needsAudit\n  /**\n   * Makes the native splash screen (configured in `app.json`) remain visible until `hideAsync` is called.\n   *\n   * > **Important note**: It is recommended to call this in global scope without awaiting, rather than\n   * > inside React components or hooks, because otherwise this might be called too late,\n   * > when the splash screen is already hidden.\n   *\n   * @example\n   * ```ts\n   * import * as SplashScreen from 'expo-splash-screen';\n   *\n   * SplashScreen.preventAutoHideAsync();\n   *\n   * export default function App() {\n   *  // ...\n   * }\n   * ```\n   */\n  async function preventAutoHideAsync() {\n    return false;\n  }\n  /**\n   *\n   * Configures the splashscreens default animation behavior.\n   *\n   */\n  function setOptions(options) {}\n  // @needsAudit\n  /**\n   * Hides the native splash screen immediately. Be careful to ensure that your app has content ready\n   * to display when you hide the splash screen, or you may see a blank screen briefly. See the\n   * [\"Usage\"](#usage) section for an example.\n   */\n  function hide() {}\n  /**\n   * Hides the native splash screen immediately. This method is provided for backwards compatability. See the\n   * [\"Usage\"](#usage) section for an example.\n   */\n  async function hideAsync() {}\n});", "lineCount": 49, "map": [[9, 2, 1, 0], [10, 2, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [17, 0, 9, 0], [18, 0, 10, 0], [19, 0, 11, 0], [20, 0, 12, 0], [21, 0, 13, 0], [22, 0, 14, 0], [23, 0, 15, 0], [24, 0, 16, 0], [25, 0, 17, 0], [26, 0, 18, 0], [27, 0, 19, 0], [28, 2, 20, 7], [28, 17, 20, 22, "preventAutoHideAsync"], [28, 37, 20, 42, "preventAutoHideAsync"], [28, 38, 20, 42], [28, 40, 20, 45], [29, 4, 21, 4], [29, 11, 21, 11], [29, 16, 21, 16], [30, 2, 22, 0], [31, 2, 23, 0], [32, 0, 24, 0], [33, 0, 25, 0], [34, 0, 26, 0], [35, 0, 27, 0], [36, 2, 28, 7], [36, 11, 28, 16, "setOptions"], [36, 21, 28, 26, "setOptions"], [36, 22, 28, 27, "options"], [36, 29, 28, 34], [36, 31, 28, 36], [36, 32, 28, 38], [37, 2, 29, 0], [38, 2, 30, 0], [39, 0, 31, 0], [40, 0, 32, 0], [41, 0, 33, 0], [42, 0, 34, 0], [43, 2, 35, 7], [43, 11, 35, 16, "hide"], [43, 15, 35, 20, "hide"], [43, 16, 35, 20], [43, 18, 35, 23], [43, 19, 35, 25], [44, 2, 36, 0], [45, 0, 37, 0], [46, 0, 38, 0], [47, 0, 39, 0], [48, 2, 40, 7], [48, 17, 40, 22, "<PERSON><PERSON><PERSON>"], [48, 26, 40, 31, "<PERSON><PERSON><PERSON>"], [48, 27, 40, 31], [48, 29, 40, 34], [48, 30, 40, 36], [49, 0, 40, 37], [49, 3]], "functionMap": {"names": ["<global>", "preventAutoHideAsync", "setOptions", "hide", "<PERSON><PERSON><PERSON>"], "mappings": "AAA;OCmB;CDE;OEM,gCF;OGO,mBH;OIK,8BJ"}}, "type": "js/module"}]}