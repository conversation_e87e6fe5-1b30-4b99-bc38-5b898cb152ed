{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 113}, "end": {"line": 3, "column": 58, "index": 171}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "./useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 172}, "end": {"line": 4, "column": 40, "index": 212}}], "key": "qBoul5KQ1+OEu8G3Sr6Tlb7g7CM=", "exportNames": ["*"]}}, {"name": "./AudioVisualizer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 213}, "end": {"line": 5, "column": 48, "index": 261}}], "key": "PtmazYoURS57fsddHvpL2PH220Y=", "exportNames": ["*"]}}, {"name": "./RealtimeTranscript", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 262}, "end": {"line": 6, "column": 54, "index": 316}}], "key": "r6gm1hrdQBRgTrcSPkygz/enaRs=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = VoiceMode;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _lucideReactNative = require(_dependencyMap[5], \"lucide-react-native\");\n  var _useColors = require(_dependencyMap[6], \"./useColors\");\n  var _AudioVisualizer = _interopRequireDefault(require(_dependencyMap[7], \"./AudioVisualizer\"));\n  var _RealtimeTranscript = _interopRequireDefault(require(_dependencyMap[8], \"./RealtimeTranscript\"));\n  var _jsxDevRuntime = require(_dependencyMap[9], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/components/VoiceMode.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function VoiceMode({\n    isRecording,\n    hasPermission,\n    isLoading,\n    transcript,\n    isMuted,\n    onMute\n  }) {\n    _s();\n    const colors = (0, _useColors.useColors)();\n    const [isSpeaking, setIsSpeaking] = (0, _react.useState)(false);\n    (0, _react.useEffect)(() => {\n      // Simulate speaking state for the visualizer\n      // Only show as speaking if recording and not muted\n      if (isRecording && !isMuted) {\n        setIsSpeaking(true);\n      } else {\n        setIsSpeaking(false);\n      }\n    }, [isRecording, isMuted]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_AudioVisualizer.default, {\n        isSpeaking: isSpeaking\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_RealtimeTranscript.default, {\n        transcript: transcript\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          position: 'absolute',\n          bottom: 32,\n          left: 0,\n          right: 0,\n          alignItems: 'center',\n          paddingHorizontal: 16,\n          backgroundColor: 'transparent'\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            paddingHorizontal: 20,\n            paddingVertical: 12,\n            backgroundColor: 'rgba(0, 0, 0, 0.7)',\n            borderRadius: 20,\n            alignItems: 'center'\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 14,\n              fontFamily: 'Poppins_500Medium',\n              color: 'white',\n              textAlign: 'center'\n            },\n            children: !hasPermission ? 'Microphone permission needed' : isMuted ? 'Voice session active (muted)' : 'Voice session active - listening'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n        style: {\n          position: 'absolute',\n          bottom: 32,\n          right: 24,\n          width: 56,\n          height: 56,\n          borderRadius: 28,\n          backgroundColor: isMuted ? '#FF3B30' : colors.primary,\n          alignItems: 'center',\n          justifyContent: 'center',\n          shadowColor: '#000',\n          shadowOffset: {\n            width: 0,\n            height: 2\n          },\n          shadowOpacity: 0.2,\n          shadowRadius: 4,\n          elevation: 4\n        },\n        onPress: onMute,\n        children: isMuted ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MicOff, {\n          size: 24,\n          color: colors.background\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Mic, {\n          size: 24,\n          color: colors.background\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 5\n    }, this);\n  }\n  _s(VoiceMode, \"je5mBwrzKqZY+g+64G/AzRwD0LM=\", false, function () {\n    return [_useColors.useColors];\n  });\n  _c = VoiceMode;\n  var _c;\n  $RefreshReg$(_c, \"VoiceMode\");\n});", "lineCount": 148, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 51], [8, 6, 1, 51, "_View"], [8, 11, 1, 51], [8, 14, 1, 51, "_interopRequireDefault"], [8, 36, 1, 51], [8, 37, 1, 51, "require"], [8, 44, 1, 51], [8, 45, 1, 51, "_dependencyMap"], [8, 59, 1, 51], [9, 2, 1, 51], [9, 6, 1, 51, "_Text"], [9, 11, 1, 51], [9, 14, 1, 51, "_interopRequireDefault"], [9, 36, 1, 51], [9, 37, 1, 51, "require"], [9, 44, 1, 51], [9, 45, 1, 51, "_dependencyMap"], [9, 59, 1, 51], [10, 2, 1, 51], [10, 6, 1, 51, "_TouchableOpacity"], [10, 23, 1, 51], [10, 26, 1, 51, "_interopRequireDefault"], [10, 48, 1, 51], [10, 49, 1, 51, "require"], [10, 56, 1, 51], [10, 57, 1, 51, "_dependencyMap"], [10, 71, 1, 51], [11, 2, 3, 0], [11, 6, 3, 0, "_lucideReactNative"], [11, 24, 3, 0], [11, 27, 3, 0, "require"], [11, 34, 3, 0], [11, 35, 3, 0, "_dependencyMap"], [11, 49, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_useColors"], [12, 16, 4, 0], [12, 19, 4, 0, "require"], [12, 26, 4, 0], [12, 27, 4, 0, "_dependencyMap"], [12, 41, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_AudioVisualizer"], [13, 22, 5, 0], [13, 25, 5, 0, "_interopRequireDefault"], [13, 47, 5, 0], [13, 48, 5, 0, "require"], [13, 55, 5, 0], [13, 56, 5, 0, "_dependencyMap"], [13, 70, 5, 0], [14, 2, 6, 0], [14, 6, 6, 0, "_RealtimeTranscript"], [14, 25, 6, 0], [14, 28, 6, 0, "_interopRequireDefault"], [14, 50, 6, 0], [14, 51, 6, 0, "require"], [14, 58, 6, 0], [14, 59, 6, 0, "_dependencyMap"], [14, 73, 6, 0], [15, 2, 6, 54], [15, 6, 6, 54, "_jsxDevRuntime"], [15, 20, 6, 54], [15, 23, 6, 54, "require"], [15, 30, 6, 54], [15, 31, 6, 54, "_dependencyMap"], [15, 45, 6, 54], [16, 2, 6, 54], [16, 6, 6, 54, "_jsxFileName"], [16, 18, 6, 54], [17, 4, 6, 54, "_s"], [17, 6, 6, 54], [17, 9, 6, 54, "$RefreshSig$"], [17, 21, 6, 54], [18, 2, 6, 54], [18, 11, 6, 54, "_interopRequireWildcard"], [18, 35, 6, 54, "e"], [18, 36, 6, 54], [18, 38, 6, 54, "t"], [18, 39, 6, 54], [18, 68, 6, 54, "WeakMap"], [18, 75, 6, 54], [18, 81, 6, 54, "r"], [18, 82, 6, 54], [18, 89, 6, 54, "WeakMap"], [18, 96, 6, 54], [18, 100, 6, 54, "n"], [18, 101, 6, 54], [18, 108, 6, 54, "WeakMap"], [18, 115, 6, 54], [18, 127, 6, 54, "_interopRequireWildcard"], [18, 150, 6, 54], [18, 162, 6, 54, "_interopRequireWildcard"], [18, 163, 6, 54, "e"], [18, 164, 6, 54], [18, 166, 6, 54, "t"], [18, 167, 6, 54], [18, 176, 6, 54, "t"], [18, 177, 6, 54], [18, 181, 6, 54, "e"], [18, 182, 6, 54], [18, 186, 6, 54, "e"], [18, 187, 6, 54], [18, 188, 6, 54, "__esModule"], [18, 198, 6, 54], [18, 207, 6, 54, "e"], [18, 208, 6, 54], [18, 214, 6, 54, "o"], [18, 215, 6, 54], [18, 217, 6, 54, "i"], [18, 218, 6, 54], [18, 220, 6, 54, "f"], [18, 221, 6, 54], [18, 226, 6, 54, "__proto__"], [18, 235, 6, 54], [18, 243, 6, 54, "default"], [18, 250, 6, 54], [18, 252, 6, 54, "e"], [18, 253, 6, 54], [18, 270, 6, 54, "e"], [18, 271, 6, 54], [18, 294, 6, 54, "e"], [18, 295, 6, 54], [18, 320, 6, 54, "e"], [18, 321, 6, 54], [18, 330, 6, 54, "f"], [18, 331, 6, 54], [18, 337, 6, 54, "o"], [18, 338, 6, 54], [18, 341, 6, 54, "t"], [18, 342, 6, 54], [18, 345, 6, 54, "n"], [18, 346, 6, 54], [18, 349, 6, 54, "r"], [18, 350, 6, 54], [18, 358, 6, 54, "o"], [18, 359, 6, 54], [18, 360, 6, 54, "has"], [18, 363, 6, 54], [18, 364, 6, 54, "e"], [18, 365, 6, 54], [18, 375, 6, 54, "o"], [18, 376, 6, 54], [18, 377, 6, 54, "get"], [18, 380, 6, 54], [18, 381, 6, 54, "e"], [18, 382, 6, 54], [18, 385, 6, 54, "o"], [18, 386, 6, 54], [18, 387, 6, 54, "set"], [18, 390, 6, 54], [18, 391, 6, 54, "e"], [18, 392, 6, 54], [18, 394, 6, 54, "f"], [18, 395, 6, 54], [18, 411, 6, 54, "t"], [18, 412, 6, 54], [18, 416, 6, 54, "e"], [18, 417, 6, 54], [18, 433, 6, 54, "t"], [18, 434, 6, 54], [18, 441, 6, 54, "hasOwnProperty"], [18, 455, 6, 54], [18, 456, 6, 54, "call"], [18, 460, 6, 54], [18, 461, 6, 54, "e"], [18, 462, 6, 54], [18, 464, 6, 54, "t"], [18, 465, 6, 54], [18, 472, 6, 54, "i"], [18, 473, 6, 54], [18, 477, 6, 54, "o"], [18, 478, 6, 54], [18, 481, 6, 54, "Object"], [18, 487, 6, 54], [18, 488, 6, 54, "defineProperty"], [18, 502, 6, 54], [18, 507, 6, 54, "Object"], [18, 513, 6, 54], [18, 514, 6, 54, "getOwnPropertyDescriptor"], [18, 538, 6, 54], [18, 539, 6, 54, "e"], [18, 540, 6, 54], [18, 542, 6, 54, "t"], [18, 543, 6, 54], [18, 550, 6, 54, "i"], [18, 551, 6, 54], [18, 552, 6, 54, "get"], [18, 555, 6, 54], [18, 559, 6, 54, "i"], [18, 560, 6, 54], [18, 561, 6, 54, "set"], [18, 564, 6, 54], [18, 568, 6, 54, "o"], [18, 569, 6, 54], [18, 570, 6, 54, "f"], [18, 571, 6, 54], [18, 573, 6, 54, "t"], [18, 574, 6, 54], [18, 576, 6, 54, "i"], [18, 577, 6, 54], [18, 581, 6, 54, "f"], [18, 582, 6, 54], [18, 583, 6, 54, "t"], [18, 584, 6, 54], [18, 588, 6, 54, "e"], [18, 589, 6, 54], [18, 590, 6, 54, "t"], [18, 591, 6, 54], [18, 602, 6, 54, "f"], [18, 603, 6, 54], [18, 608, 6, 54, "e"], [18, 609, 6, 54], [18, 611, 6, 54, "t"], [18, 612, 6, 54], [19, 2, 8, 15], [19, 11, 8, 24, "VoiceMode"], [19, 20, 8, 33, "VoiceMode"], [19, 21, 8, 34], [20, 4, 8, 36, "isRecording"], [20, 15, 8, 47], [21, 4, 8, 49, "hasPermission"], [21, 17, 8, 62], [22, 4, 8, 64, "isLoading"], [22, 13, 8, 73], [23, 4, 8, 75, "transcript"], [23, 14, 8, 85], [24, 4, 8, 87, "isMuted"], [24, 11, 8, 94], [25, 4, 8, 96, "onMute"], [26, 2, 8, 103], [26, 3, 8, 104], [26, 5, 8, 106], [27, 4, 8, 106, "_s"], [27, 6, 8, 106], [28, 4, 9, 2], [28, 10, 9, 8, "colors"], [28, 16, 9, 14], [28, 19, 9, 17], [28, 23, 9, 17, "useColors"], [28, 43, 9, 26], [28, 45, 9, 27], [28, 46, 9, 28], [29, 4, 10, 2], [29, 10, 10, 8], [29, 11, 10, 9, "isSpeaking"], [29, 21, 10, 19], [29, 23, 10, 21, "setIsSpeaking"], [29, 36, 10, 34], [29, 37, 10, 35], [29, 40, 10, 38], [29, 44, 10, 38, "useState"], [29, 59, 10, 46], [29, 61, 10, 47], [29, 66, 10, 52], [29, 67, 10, 53], [30, 4, 12, 2], [30, 8, 12, 2, "useEffect"], [30, 24, 12, 11], [30, 26, 12, 12], [30, 32, 12, 18], [31, 6, 13, 4], [32, 6, 14, 4], [33, 6, 15, 4], [33, 10, 15, 8, "isRecording"], [33, 21, 15, 19], [33, 25, 15, 23], [33, 26, 15, 24, "isMuted"], [33, 33, 15, 31], [33, 35, 15, 33], [34, 8, 16, 6, "setIsSpeaking"], [34, 21, 16, 19], [34, 22, 16, 20], [34, 26, 16, 24], [34, 27, 16, 25], [35, 6, 17, 4], [35, 7, 17, 5], [35, 13, 17, 11], [36, 8, 18, 6, "setIsSpeaking"], [36, 21, 18, 19], [36, 22, 18, 20], [36, 27, 18, 25], [36, 28, 18, 26], [37, 6, 19, 4], [38, 4, 20, 2], [38, 5, 20, 3], [38, 7, 20, 5], [38, 8, 20, 6, "isRecording"], [38, 19, 20, 17], [38, 21, 20, 19, "isMuted"], [38, 28, 20, 26], [38, 29, 20, 27], [38, 30, 20, 28], [39, 4, 22, 2], [39, 24, 23, 4], [39, 28, 23, 4, "_jsxDevRuntime"], [39, 42, 23, 4], [39, 43, 23, 4, "jsxDEV"], [39, 49, 23, 4], [39, 51, 23, 5, "_View"], [39, 56, 23, 5], [39, 57, 23, 5, "default"], [39, 64, 23, 9], [40, 6, 23, 10, "style"], [40, 11, 23, 15], [40, 13, 23, 17], [41, 8, 23, 19, "flex"], [41, 12, 23, 23], [41, 14, 23, 25], [42, 6, 23, 27], [42, 7, 23, 29], [43, 6, 23, 29, "children"], [43, 14, 23, 29], [43, 30, 24, 6], [43, 34, 24, 6, "_jsxDevRuntime"], [43, 48, 24, 6], [43, 49, 24, 6, "jsxDEV"], [43, 55, 24, 6], [43, 57, 24, 7, "_AudioVisualizer"], [43, 73, 24, 7], [43, 74, 24, 7, "default"], [43, 81, 24, 22], [44, 8, 24, 23, "isSpeaking"], [44, 18, 24, 33], [44, 20, 24, 35, "isSpeaking"], [45, 6, 24, 46], [46, 8, 24, 46, "fileName"], [46, 16, 24, 46], [46, 18, 24, 46, "_jsxFileName"], [46, 30, 24, 46], [47, 8, 24, 46, "lineNumber"], [47, 18, 24, 46], [48, 8, 24, 46, "columnNumber"], [48, 20, 24, 46], [49, 6, 24, 46], [49, 13, 24, 48], [49, 14, 24, 49], [49, 29, 25, 6], [49, 33, 25, 6, "_jsxDevRuntime"], [49, 47, 25, 6], [49, 48, 25, 6, "jsxDEV"], [49, 54, 25, 6], [49, 56, 25, 7, "_RealtimeTranscript"], [49, 75, 25, 7], [49, 76, 25, 7, "default"], [49, 83, 25, 25], [50, 8, 25, 26, "transcript"], [50, 18, 25, 36], [50, 20, 25, 38, "transcript"], [51, 6, 25, 49], [52, 8, 25, 49, "fileName"], [52, 16, 25, 49], [52, 18, 25, 49, "_jsxFileName"], [52, 30, 25, 49], [53, 8, 25, 49, "lineNumber"], [53, 18, 25, 49], [54, 8, 25, 49, "columnNumber"], [54, 20, 25, 49], [55, 6, 25, 49], [55, 13, 25, 51], [55, 14, 25, 52], [55, 29, 28, 6], [55, 33, 28, 6, "_jsxDevRuntime"], [55, 47, 28, 6], [55, 48, 28, 6, "jsxDEV"], [55, 54, 28, 6], [55, 56, 28, 7, "_View"], [55, 61, 28, 7], [55, 62, 28, 7, "default"], [55, 69, 28, 11], [56, 8, 29, 8, "style"], [56, 13, 29, 13], [56, 15, 29, 15], [57, 10, 30, 10, "position"], [57, 18, 30, 18], [57, 20, 30, 20], [57, 30, 30, 30], [58, 10, 31, 10, "bottom"], [58, 16, 31, 16], [58, 18, 31, 18], [58, 20, 31, 20], [59, 10, 32, 10, "left"], [59, 14, 32, 14], [59, 16, 32, 16], [59, 17, 32, 17], [60, 10, 33, 10, "right"], [60, 15, 33, 15], [60, 17, 33, 17], [60, 18, 33, 18], [61, 10, 34, 10, "alignItems"], [61, 20, 34, 20], [61, 22, 34, 22], [61, 30, 34, 30], [62, 10, 35, 10, "paddingHorizontal"], [62, 27, 35, 27], [62, 29, 35, 29], [62, 31, 35, 31], [63, 10, 36, 10, "backgroundColor"], [63, 25, 36, 25], [63, 27, 36, 27], [64, 8, 37, 8], [64, 9, 37, 10], [65, 8, 37, 10, "children"], [65, 16, 37, 10], [65, 31, 39, 8], [65, 35, 39, 8, "_jsxDevRuntime"], [65, 49, 39, 8], [65, 50, 39, 8, "jsxDEV"], [65, 56, 39, 8], [65, 58, 39, 9, "_View"], [65, 63, 39, 9], [65, 64, 39, 9, "default"], [65, 71, 39, 13], [66, 10, 40, 10, "style"], [66, 15, 40, 15], [66, 17, 40, 17], [67, 12, 41, 12, "paddingHorizontal"], [67, 29, 41, 29], [67, 31, 41, 31], [67, 33, 41, 33], [68, 12, 42, 12, "paddingVertical"], [68, 27, 42, 27], [68, 29, 42, 29], [68, 31, 42, 31], [69, 12, 43, 12, "backgroundColor"], [69, 27, 43, 27], [69, 29, 43, 29], [69, 49, 43, 49], [70, 12, 44, 12, "borderRadius"], [70, 24, 44, 24], [70, 26, 44, 26], [70, 28, 44, 28], [71, 12, 45, 12, "alignItems"], [71, 22, 45, 22], [71, 24, 45, 24], [72, 10, 46, 10], [72, 11, 46, 12], [73, 10, 46, 12, "children"], [73, 18, 46, 12], [73, 33, 48, 10], [73, 37, 48, 10, "_jsxDevRuntime"], [73, 51, 48, 10], [73, 52, 48, 10, "jsxDEV"], [73, 58, 48, 10], [73, 60, 48, 11, "_Text"], [73, 65, 48, 11], [73, 66, 48, 11, "default"], [73, 73, 48, 15], [74, 12, 49, 12, "style"], [74, 17, 49, 17], [74, 19, 49, 19], [75, 14, 50, 14, "fontSize"], [75, 22, 50, 22], [75, 24, 50, 24], [75, 26, 50, 26], [76, 14, 51, 14, "fontFamily"], [76, 24, 51, 24], [76, 26, 51, 26], [76, 45, 51, 45], [77, 14, 52, 14, "color"], [77, 19, 52, 19], [77, 21, 52, 21], [77, 28, 52, 28], [78, 14, 53, 14, "textAlign"], [78, 23, 53, 23], [78, 25, 53, 25], [79, 12, 54, 12], [79, 13, 54, 14], [80, 12, 54, 14, "children"], [80, 20, 54, 14], [80, 22, 56, 13], [80, 23, 56, 14, "hasPermission"], [80, 36, 56, 27], [80, 39, 57, 16], [80, 69, 57, 46], [80, 72, 58, 16, "isMuted"], [80, 79, 58, 23], [80, 82, 59, 18], [80, 112, 59, 48], [80, 115, 60, 18], [81, 10, 60, 52], [82, 12, 60, 52, "fileName"], [82, 20, 60, 52], [82, 22, 60, 52, "_jsxFileName"], [82, 34, 60, 52], [83, 12, 60, 52, "lineNumber"], [83, 22, 60, 52], [84, 12, 60, 52, "columnNumber"], [84, 24, 60, 52], [85, 10, 60, 52], [85, 17, 61, 16], [86, 8, 61, 17], [87, 10, 61, 17, "fileName"], [87, 18, 61, 17], [87, 20, 61, 17, "_jsxFileName"], [87, 32, 61, 17], [88, 10, 61, 17, "lineNumber"], [88, 20, 61, 17], [89, 10, 61, 17, "columnNumber"], [89, 22, 61, 17], [90, 8, 61, 17], [90, 15, 62, 14], [91, 6, 62, 15], [92, 8, 62, 15, "fileName"], [92, 16, 62, 15], [92, 18, 62, 15, "_jsxFileName"], [92, 30, 62, 15], [93, 8, 62, 15, "lineNumber"], [93, 18, 62, 15], [94, 8, 62, 15, "columnNumber"], [94, 20, 62, 15], [95, 6, 62, 15], [95, 13, 63, 12], [95, 14, 63, 13], [95, 29, 66, 6], [95, 33, 66, 6, "_jsxDevRuntime"], [95, 47, 66, 6], [95, 48, 66, 6, "jsxDEV"], [95, 54, 66, 6], [95, 56, 66, 7, "_TouchableOpacity"], [95, 73, 66, 7], [95, 74, 66, 7, "default"], [95, 81, 66, 23], [96, 8, 67, 8, "style"], [96, 13, 67, 13], [96, 15, 67, 15], [97, 10, 68, 10, "position"], [97, 18, 68, 18], [97, 20, 68, 20], [97, 30, 68, 30], [98, 10, 69, 10, "bottom"], [98, 16, 69, 16], [98, 18, 69, 18], [98, 20, 69, 20], [99, 10, 70, 10, "right"], [99, 15, 70, 15], [99, 17, 70, 17], [99, 19, 70, 19], [100, 10, 71, 10, "width"], [100, 15, 71, 15], [100, 17, 71, 17], [100, 19, 71, 19], [101, 10, 72, 10, "height"], [101, 16, 72, 16], [101, 18, 72, 18], [101, 20, 72, 20], [102, 10, 73, 10, "borderRadius"], [102, 22, 73, 22], [102, 24, 73, 24], [102, 26, 73, 26], [103, 10, 74, 10, "backgroundColor"], [103, 25, 74, 25], [103, 27, 74, 27, "isMuted"], [103, 34, 74, 34], [103, 37, 74, 37], [103, 46, 74, 46], [103, 49, 74, 49, "colors"], [103, 55, 74, 55], [103, 56, 74, 56, "primary"], [103, 63, 74, 63], [104, 10, 75, 10, "alignItems"], [104, 20, 75, 20], [104, 22, 75, 22], [104, 30, 75, 30], [105, 10, 76, 10, "justifyContent"], [105, 24, 76, 24], [105, 26, 76, 26], [105, 34, 76, 34], [106, 10, 77, 10, "shadowColor"], [106, 21, 77, 21], [106, 23, 77, 23], [106, 29, 77, 29], [107, 10, 78, 10, "shadowOffset"], [107, 22, 78, 22], [107, 24, 78, 24], [108, 12, 78, 26, "width"], [108, 17, 78, 31], [108, 19, 78, 33], [108, 20, 78, 34], [109, 12, 78, 36, "height"], [109, 18, 78, 42], [109, 20, 78, 44], [110, 10, 78, 46], [110, 11, 78, 47], [111, 10, 79, 10, "shadowOpacity"], [111, 23, 79, 23], [111, 25, 79, 25], [111, 28, 79, 28], [112, 10, 80, 10, "shadowRadius"], [112, 22, 80, 22], [112, 24, 80, 24], [112, 25, 80, 25], [113, 10, 81, 10, "elevation"], [113, 19, 81, 19], [113, 21, 81, 21], [114, 8, 82, 8], [114, 9, 82, 10], [115, 8, 83, 8, "onPress"], [115, 15, 83, 15], [115, 17, 83, 17, "onMute"], [115, 23, 83, 24], [116, 8, 83, 24, "children"], [116, 16, 83, 24], [116, 18, 85, 9, "isMuted"], [116, 25, 85, 16], [116, 41, 86, 10], [116, 45, 86, 10, "_jsxDevRuntime"], [116, 59, 86, 10], [116, 60, 86, 10, "jsxDEV"], [116, 66, 86, 10], [116, 68, 86, 11, "_lucideReactNative"], [116, 86, 86, 11], [116, 87, 86, 11, "<PERSON><PERSON><PERSON><PERSON>"], [116, 93, 86, 17], [117, 10, 86, 18, "size"], [117, 14, 86, 22], [117, 16, 86, 24], [117, 18, 86, 27], [118, 10, 86, 28, "color"], [118, 15, 86, 33], [118, 17, 86, 35, "colors"], [118, 23, 86, 41], [118, 24, 86, 42, "background"], [119, 8, 86, 53], [120, 10, 86, 53, "fileName"], [120, 18, 86, 53], [120, 20, 86, 53, "_jsxFileName"], [120, 32, 86, 53], [121, 10, 86, 53, "lineNumber"], [121, 20, 86, 53], [122, 10, 86, 53, "columnNumber"], [122, 22, 86, 53], [123, 8, 86, 53], [123, 15, 86, 55], [123, 16, 86, 56], [123, 32, 88, 10], [123, 36, 88, 10, "_jsxDevRuntime"], [123, 50, 88, 10], [123, 51, 88, 10, "jsxDEV"], [123, 57, 88, 10], [123, 59, 88, 11, "_lucideReactNative"], [123, 77, 88, 11], [123, 78, 88, 11, "Mic"], [123, 81, 88, 14], [124, 10, 88, 15, "size"], [124, 14, 88, 19], [124, 16, 88, 21], [124, 18, 88, 24], [125, 10, 88, 25, "color"], [125, 15, 88, 30], [125, 17, 88, 32, "colors"], [125, 23, 88, 38], [125, 24, 88, 39, "background"], [126, 8, 88, 50], [127, 10, 88, 50, "fileName"], [127, 18, 88, 50], [127, 20, 88, 50, "_jsxFileName"], [127, 32, 88, 50], [128, 10, 88, 50, "lineNumber"], [128, 20, 88, 50], [129, 10, 88, 50, "columnNumber"], [129, 22, 88, 50], [130, 8, 88, 50], [130, 15, 88, 52], [131, 6, 89, 9], [132, 8, 89, 9, "fileName"], [132, 16, 89, 9], [132, 18, 89, 9, "_jsxFileName"], [132, 30, 89, 9], [133, 8, 89, 9, "lineNumber"], [133, 18, 89, 9], [134, 8, 89, 9, "columnNumber"], [134, 20, 89, 9], [135, 6, 89, 9], [135, 13, 90, 24], [135, 14, 90, 25], [136, 4, 90, 25], [137, 6, 90, 25, "fileName"], [137, 14, 90, 25], [137, 16, 90, 25, "_jsxFileName"], [137, 28, 90, 25], [138, 6, 90, 25, "lineNumber"], [138, 16, 90, 25], [139, 6, 90, 25, "columnNumber"], [139, 18, 90, 25], [140, 4, 90, 25], [140, 11, 91, 10], [140, 12, 91, 11], [141, 2, 93, 0], [142, 2, 93, 1, "_s"], [142, 4, 93, 1], [142, 5, 8, 24, "VoiceMode"], [142, 14, 8, 33], [143, 4, 8, 33], [143, 12, 9, 17, "useColors"], [143, 32, 9, 26], [144, 2, 9, 26], [145, 2, 9, 26, "_c"], [145, 4, 9, 26], [145, 7, 8, 24, "VoiceMode"], [145, 16, 8, 33], [146, 2, 8, 33], [146, 6, 8, 33, "_c"], [146, 8, 8, 33], [147, 2, 8, 33, "$RefreshReg$"], [147, 14, 8, 33], [147, 15, 8, 33, "_c"], [147, 17, 8, 33], [148, 0, 8, 33], [148, 3]], "functionMap": {"names": ["<global>", "VoiceMode", "useEffect$argument_0"], "mappings": "AAA;eCO;YCI;GDQ;CDyE"}}, "type": "js/module"}]}