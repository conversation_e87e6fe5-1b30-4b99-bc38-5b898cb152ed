{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 46, "index": 46}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[1], \"./GestureHandler\"));\n  class ManualGestureHandler extends _GestureHandler.default {\n    onPointerDown(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerDown(event);\n      this.begin();\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerAdd(event);\n    }\n    onPointerMove(event) {\n      this.tracker.track(event);\n      super.onPointerMove(event);\n    }\n    onPointerOutOfBounds(event) {\n      this.tracker.track(event);\n      super.onPointerOutOfBounds(event);\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      this.tracker.removeFromTracker(event.pointerId);\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.tracker.removeFromTracker(event.pointerId);\n    }\n  }\n  exports.default = ManualGestureHandler;\n});", "lineCount": 37, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_Gesture<PERSON><PERSON>ler"], [7, 21, 1, 0], [7, 24, 1, 0, "_interopRequireDefault"], [7, 46, 1, 0], [7, 47, 1, 0, "require"], [7, 54, 1, 0], [7, 55, 1, 0, "_dependencyMap"], [7, 69, 1, 0], [8, 2, 2, 15], [8, 8, 2, 21, "ManualGestureHandler"], [8, 28, 2, 41], [8, 37, 2, 50, "Gesture<PERSON>andler"], [8, 60, 2, 64], [8, 61, 2, 65], [9, 4, 3, 2, "onPointerDown"], [9, 17, 3, 15, "onPointerDown"], [9, 18, 3, 16, "event"], [9, 23, 3, 21], [9, 25, 3, 23], [10, 6, 4, 4], [10, 10, 4, 8], [10, 11, 4, 9, "tracker"], [10, 18, 4, 16], [10, 19, 4, 17, "addToTracker"], [10, 31, 4, 29], [10, 32, 4, 30, "event"], [10, 37, 4, 35], [10, 38, 4, 36], [11, 6, 5, 4], [11, 11, 5, 9], [11, 12, 5, 10, "onPointerDown"], [11, 25, 5, 23], [11, 26, 5, 24, "event"], [11, 31, 5, 29], [11, 32, 5, 30], [12, 6, 6, 4], [12, 10, 6, 8], [12, 11, 6, 9, "begin"], [12, 16, 6, 14], [12, 17, 6, 15], [12, 18, 6, 16], [13, 6, 7, 4], [13, 10, 7, 8], [13, 11, 7, 9, "tryToSendTouchEvent"], [13, 30, 7, 28], [13, 31, 7, 29, "event"], [13, 36, 7, 34], [13, 37, 7, 35], [14, 4, 8, 2], [15, 4, 10, 2, "onPointerAdd"], [15, 16, 10, 14, "onPointerAdd"], [15, 17, 10, 15, "event"], [15, 22, 10, 20], [15, 24, 10, 22], [16, 6, 11, 4], [16, 10, 11, 8], [16, 11, 11, 9, "tracker"], [16, 18, 11, 16], [16, 19, 11, 17, "addToTracker"], [16, 31, 11, 29], [16, 32, 11, 30, "event"], [16, 37, 11, 35], [16, 38, 11, 36], [17, 6, 12, 4], [17, 11, 12, 9], [17, 12, 12, 10, "onPointerAdd"], [17, 24, 12, 22], [17, 25, 12, 23, "event"], [17, 30, 12, 28], [17, 31, 12, 29], [18, 4, 13, 2], [19, 4, 15, 2, "onPointerMove"], [19, 17, 15, 15, "onPointerMove"], [19, 18, 15, 16, "event"], [19, 23, 15, 21], [19, 25, 15, 23], [20, 6, 16, 4], [20, 10, 16, 8], [20, 11, 16, 9, "tracker"], [20, 18, 16, 16], [20, 19, 16, 17, "track"], [20, 24, 16, 22], [20, 25, 16, 23, "event"], [20, 30, 16, 28], [20, 31, 16, 29], [21, 6, 17, 4], [21, 11, 17, 9], [21, 12, 17, 10, "onPointerMove"], [21, 25, 17, 23], [21, 26, 17, 24, "event"], [21, 31, 17, 29], [21, 32, 17, 30], [22, 4, 18, 2], [23, 4, 20, 2, "onPointerOutOfBounds"], [23, 24, 20, 22, "onPointerOutOfBounds"], [23, 25, 20, 23, "event"], [23, 30, 20, 28], [23, 32, 20, 30], [24, 6, 21, 4], [24, 10, 21, 8], [24, 11, 21, 9, "tracker"], [24, 18, 21, 16], [24, 19, 21, 17, "track"], [24, 24, 21, 22], [24, 25, 21, 23, "event"], [24, 30, 21, 28], [24, 31, 21, 29], [25, 6, 22, 4], [25, 11, 22, 9], [25, 12, 22, 10, "onPointerOutOfBounds"], [25, 32, 22, 30], [25, 33, 22, 31, "event"], [25, 38, 22, 36], [25, 39, 22, 37], [26, 4, 23, 2], [27, 4, 25, 2, "onPointerUp"], [27, 15, 25, 13, "onPointerUp"], [27, 16, 25, 14, "event"], [27, 21, 25, 19], [27, 23, 25, 21], [28, 6, 26, 4], [28, 11, 26, 9], [28, 12, 26, 10, "onPointerUp"], [28, 23, 26, 21], [28, 24, 26, 22, "event"], [28, 29, 26, 27], [28, 30, 26, 28], [29, 6, 27, 4], [29, 10, 27, 8], [29, 11, 27, 9, "tracker"], [29, 18, 27, 16], [29, 19, 27, 17, "removeFromTracker"], [29, 36, 27, 34], [29, 37, 27, 35, "event"], [29, 42, 27, 40], [29, 43, 27, 41, "pointerId"], [29, 52, 27, 50], [29, 53, 27, 51], [30, 4, 28, 2], [31, 4, 30, 2, "onPointerRemove"], [31, 19, 30, 17, "onPointerRemove"], [31, 20, 30, 18, "event"], [31, 25, 30, 23], [31, 27, 30, 25], [32, 6, 31, 4], [32, 11, 31, 9], [32, 12, 31, 10, "onPointerRemove"], [32, 27, 31, 25], [32, 28, 31, 26, "event"], [32, 33, 31, 31], [32, 34, 31, 32], [33, 6, 32, 4], [33, 10, 32, 8], [33, 11, 32, 9, "tracker"], [33, 18, 32, 16], [33, 19, 32, 17, "removeFromTracker"], [33, 36, 32, 34], [33, 37, 32, 35, "event"], [33, 42, 32, 40], [33, 43, 32, 41, "pointerId"], [33, 52, 32, 50], [33, 53, 32, 51], [34, 4, 33, 2], [35, 2, 35, 0], [36, 2, 35, 1, "exports"], [36, 9, 35, 1], [36, 10, 35, 1, "default"], [36, 17, 35, 1], [36, 20, 35, 1, "ManualGestureHandler"], [36, 40, 35, 1], [37, 0, 35, 1], [37, 3]], "functionMap": {"names": ["<global>", "ManualGestureHandler", "onPointerDown", "onPointerAdd", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onPointerRemove"], "mappings": "AAA;eCC;ECC;GDK;EEE;GFG;EGE;GHG;EIE;GJG;EKE;GLG;EME;GNG;CDE"}}, "type": "js/module"}]}