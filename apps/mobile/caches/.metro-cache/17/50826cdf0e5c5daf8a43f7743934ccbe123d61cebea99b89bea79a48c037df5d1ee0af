{"dependencies": [{"name": "./measure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 111}, "end": {"line": 5, "column": 36, "index": 147}}], "key": "B5IHqoArBd4o1xEAyGBYJRJiEtY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getRelativeCoords = void 0;\n  var _measure = require(_dependencyMap[0], \"./measure\");\n  /** An object which contains relative coordinates. */\n  var _worklet_1583333301789_init_data = {\n    code: \"function getRelativeCoords_reactNativeReanimated_getRelativeCoordsTs1(animatedRef,absoluteX,absoluteY){const{measure}=this.__closure;const parentCoords=measure(animatedRef);if(parentCoords===null){return null;}return{x:absoluteX-parentCoords.pageX,y:absoluteY-parentCoords.pageY};}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/src/platformFunctions/getRelativeCoords.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getRelativeCoords_reactNativeReanimated_getRelativeCoordsTs1\\\",\\\"animatedRef\\\",\\\"absoluteX\\\",\\\"absoluteY\\\",\\\"measure\\\",\\\"__closure\\\",\\\"parentCoords\\\",\\\"x\\\",\\\"pageX\\\",\\\"y\\\",\\\"pageY\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/src/platformFunctions/getRelativeCoords.ts\\\"],\\\"mappings\\\":\\\"AAwBO,SAAAA,4DAImBA,CAAAC,WAAA,CAAAC,SAAA,CAAAC,SAAA,QAAAC,OAAA,OAAAC,SAAA,CAExB,KAAM,CAAAC,YAAY,CAAGF,OAAO,CAACH,WAAW,CAAC,CACzC,GAAIK,YAAY,GAAK,IAAI,CAAE,CACzB,MAAO,KAAI,CACb,CACA,MAAO,CACLC,CAAC,CAAEL,SAAS,CAAGI,YAAY,CAACE,KAAK,CACjCC,CAAC,CAAEN,SAAS,CAAGG,YAAY,CAACI,KAC9B,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  /**\n   * Lets you determines the location on the screen, relative to the given view.\n   *\n   * @param animatedRef - An [animated\n   *   ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns)\n   *   connected to the component you'd want to get the coordinates from.\n   * @param absoluteX - A number which is an absolute x coordinate.\n   * @param absoluteY - A number which is an absolute y coordinate.\n   * @returns An object which contains relative coordinates -\n   *   {@link ComponentCoords}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/getRelativeCoords\n   */\n  var getRelativeCoords = exports.getRelativeCoords = function () {\n    var _e = [new global.Error(), -2, -27];\n    var getRelativeCoords = function (animatedRef, absoluteX, absoluteY) {\n      var parentCoords = (0, _measure.measure)(animatedRef);\n      if (parentCoords === null) {\n        return null;\n      }\n      return {\n        x: absoluteX - parentCoords.pageX,\n        y: absoluteY - parentCoords.pageY\n      };\n    };\n    getRelativeCoords.__closure = {\n      measure: _measure.measure\n    };\n    getRelativeCoords.__workletHash = 1583333301789;\n    getRelativeCoords.__initData = _worklet_1583333301789_init_data;\n    getRelativeCoords.__stackDetails = _e;\n    return getRelativeCoords;\n  }();\n});", "lineCount": 48, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getRelativeCoords"], [7, 27, 1, 13], [8, 2, 5, 0], [8, 6, 5, 0, "_measure"], [8, 14, 5, 0], [8, 17, 5, 0, "require"], [8, 24, 5, 0], [8, 25, 5, 0, "_dependencyMap"], [8, 39, 5, 0], [9, 2, 7, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_worklet_1583333301789_init_data"], [10, 38, 7, 0], [11, 4, 7, 0, "code"], [11, 8, 7, 0], [12, 4, 7, 0, "location"], [12, 12, 7, 0], [13, 4, 7, 0, "sourceMap"], [13, 13, 7, 0], [14, 4, 7, 0, "version"], [14, 11, 7, 0], [15, 2, 7, 0], [16, 2, 13, 0], [17, 0, 14, 0], [18, 0, 15, 0], [19, 0, 16, 0], [20, 0, 17, 0], [21, 0, 18, 0], [22, 0, 19, 0], [23, 0, 20, 0], [24, 0, 21, 0], [25, 0, 22, 0], [26, 0, 23, 0], [27, 0, 24, 0], [28, 2, 13, 0], [28, 6, 13, 0, "getRelativeCoords"], [28, 23, 13, 0], [28, 26, 13, 0, "exports"], [28, 33, 13, 0], [28, 34, 13, 0, "getRelativeCoords"], [28, 51, 13, 0], [28, 54, 25, 7], [29, 4, 25, 7], [29, 8, 25, 7, "_e"], [29, 10, 25, 7], [29, 18, 25, 7, "global"], [29, 24, 25, 7], [29, 25, 25, 7, "Error"], [29, 30, 25, 7], [30, 4, 25, 7], [30, 8, 25, 7, "getRelativeCoords"], [30, 25, 25, 7], [30, 37, 25, 7, "getRelativeCoords"], [30, 38, 26, 2, "animatedRef"], [30, 49, 26, 37], [30, 51, 27, 2, "absoluteX"], [30, 60, 27, 19], [30, 62, 28, 2, "absoluteY"], [30, 71, 28, 19], [30, 73, 29, 26], [31, 6, 31, 2], [31, 10, 31, 8, "parentCoords"], [31, 22, 31, 20], [31, 25, 31, 23], [31, 29, 31, 23, "measure"], [31, 45, 31, 30], [31, 47, 31, 31, "animatedRef"], [31, 58, 31, 42], [31, 59, 31, 43], [32, 6, 32, 2], [32, 10, 32, 6, "parentCoords"], [32, 22, 32, 18], [32, 27, 32, 23], [32, 31, 32, 27], [32, 33, 32, 29], [33, 8, 33, 4], [33, 15, 33, 11], [33, 19, 33, 15], [34, 6, 34, 2], [35, 6, 35, 2], [35, 13, 35, 9], [36, 8, 36, 4, "x"], [36, 9, 36, 5], [36, 11, 36, 7, "absoluteX"], [36, 20, 36, 16], [36, 23, 36, 19, "parentCoords"], [36, 35, 36, 31], [36, 36, 36, 32, "pageX"], [36, 41, 36, 37], [37, 8, 37, 4, "y"], [37, 9, 37, 5], [37, 11, 37, 7, "absoluteY"], [37, 20, 37, 16], [37, 23, 37, 19, "parentCoords"], [37, 35, 37, 31], [37, 36, 37, 32, "pageY"], [38, 6, 38, 2], [38, 7, 38, 3], [39, 4, 39, 0], [39, 5, 39, 1], [40, 4, 39, 1, "getRelativeCoords"], [40, 21, 39, 1], [40, 22, 39, 1, "__closure"], [40, 31, 39, 1], [41, 6, 39, 1, "measure"], [41, 13, 39, 1], [41, 15, 31, 23, "measure"], [42, 4, 31, 30], [43, 4, 31, 30, "getRelativeCoords"], [43, 21, 31, 30], [43, 22, 31, 30, "__workletHash"], [43, 35, 31, 30], [44, 4, 31, 30, "getRelativeCoords"], [44, 21, 31, 30], [44, 22, 31, 30, "__initData"], [44, 32, 31, 30], [44, 35, 31, 30, "_worklet_1583333301789_init_data"], [44, 67, 31, 30], [45, 4, 31, 30, "getRelativeCoords"], [45, 21, 31, 30], [45, 22, 31, 30, "__stackDetails"], [45, 36, 31, 30], [45, 39, 31, 30, "_e"], [45, 41, 31, 30], [46, 4, 31, 30], [46, 11, 31, 30, "getRelativeCoords"], [46, 28, 31, 30], [47, 2, 31, 30], [47, 3, 25, 7], [48, 0, 25, 7], [48, 3]], "functionMap": {"names": ["<global>", "getRelativeCoords"], "mappings": "AAA;OCwB;CDc"}}, "type": "js/module"}]}