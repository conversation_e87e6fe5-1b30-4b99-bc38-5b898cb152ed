{"dependencies": [{"name": "../constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 50, "index": 261}}], "key": "eTOOXVNPpMK2U8dOAmBWjbEJ4yE=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 262}, "end": {"line": 4, "column": 43, "index": 305}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _constants = require(_dependencyMap[0], \"../constants\");\n  var _interfaces = require(_dependencyMap[1], \"../interfaces\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class ScaleGestureDetector {\n    constructor(callbacks) {\n      _defineProperty(this, \"onScaleBegin\", void 0);\n      _defineProperty(this, \"onScale\", void 0);\n      _defineProperty(this, \"onScaleEnd\", void 0);\n      _defineProperty(this, \"_focusX\", void 0);\n      _defineProperty(this, \"_focusY\", void 0);\n      _defineProperty(this, \"_currentSpan\", void 0);\n      _defineProperty(this, \"prevSpan\", void 0);\n      _defineProperty(this, \"initialSpan\", void 0);\n      _defineProperty(this, \"currentTime\", void 0);\n      _defineProperty(this, \"prevTime\", void 0);\n      _defineProperty(this, \"inProgress\", false);\n      _defineProperty(this, \"spanSlop\", void 0);\n      _defineProperty(this, \"minSpan\", void 0);\n      this.onScaleBegin = callbacks.onScaleBegin;\n      this.onScale = callbacks.onScale;\n      this.onScaleEnd = callbacks.onScaleEnd;\n      this.spanSlop = _constants.DEFAULT_TOUCH_SLOP * 2;\n      this.minSpan = 0;\n    }\n    onTouchEvent(event, tracker) {\n      this.currentTime = event.time;\n      const action = event.eventType;\n      const numOfPointers = tracker.trackedPointersCount;\n      const streamComplete = action === _interfaces.EventTypes.UP || action === _interfaces.EventTypes.ADDITIONAL_POINTER_UP || action === _interfaces.EventTypes.CANCEL;\n      if (action === _interfaces.EventTypes.DOWN || streamComplete) {\n        if (this.inProgress) {\n          this.onScaleEnd(this);\n          this.inProgress = false;\n          this.initialSpan = 0;\n        }\n        if (streamComplete) {\n          return true;\n        }\n      }\n      const configChanged = action === _interfaces.EventTypes.DOWN || action === _interfaces.EventTypes.ADDITIONAL_POINTER_UP || action === _interfaces.EventTypes.ADDITIONAL_POINTER_DOWN;\n      const pointerUp = action === _interfaces.EventTypes.ADDITIONAL_POINTER_UP;\n      const ignoredPointer = pointerUp ? event.pointerId : undefined; // Determine focal point\n\n      const div = pointerUp ? numOfPointers - 1 : numOfPointers;\n      const coordsSum = tracker.getAbsoluteCoordsSum();\n      const focusX = coordsSum.x / div;\n      const focusY = coordsSum.y / div; // Determine average deviation from focal point\n\n      let devSumX = 0;\n      let devSumY = 0;\n      tracker.trackedPointers.forEach((value, key) => {\n        if (key === ignoredPointer) {\n          return;\n        }\n        devSumX += Math.abs(value.abosoluteCoords.x - focusX);\n        devSumY += Math.abs(value.abosoluteCoords.y - focusY);\n      });\n      const devX = devSumX / div;\n      const devY = devSumY / div;\n      const spanX = devX * 2;\n      const spanY = devY * 2;\n      const span = Math.hypot(spanX, spanY); // Begin/end events\n\n      const wasInProgress = this.inProgress;\n      this._focusX = focusX;\n      this._focusY = focusY;\n      if (this.inProgress && (span < this.minSpan || configChanged)) {\n        this.onScaleEnd(this);\n        this.inProgress = false;\n        this.initialSpan = span;\n      }\n      if (configChanged) {\n        this.initialSpan = this.prevSpan = this._currentSpan = span;\n      }\n      if (!this.inProgress && span >= this.minSpan && (wasInProgress || Math.abs(span - this.initialSpan) > this.spanSlop)) {\n        this.prevSpan = this._currentSpan = span;\n        this.prevTime = this.currentTime;\n        this.inProgress = this.onScaleBegin(this);\n      } // Handle motion\n\n      if (action !== _interfaces.EventTypes.MOVE) {\n        return true;\n      }\n      this._currentSpan = span;\n      if (this.inProgress && !this.onScale(this)) {\n        return true;\n      }\n      this.prevSpan = this.currentSpan;\n      this.prevTime = this.currentTime;\n      return true;\n    }\n    calculateScaleFactor(numOfPointers) {\n      if (numOfPointers < 2) {\n        return 1;\n      }\n      return this.prevSpan > 0 ? this.currentSpan / this.prevSpan : 1;\n    }\n    get currentSpan() {\n      return this._currentSpan;\n    }\n    get focusX() {\n      return this._focusX;\n    }\n    get focusY() {\n      return this._focusY;\n    }\n    get timeDelta() {\n      return this.currentTime - this.prevTime;\n    }\n  }\n  exports.default = ScaleGestureDetector;\n});", "lineCount": 129, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_constants"], [6, 16, 3, 0], [6, 19, 3, 0, "require"], [6, 26, 3, 0], [6, 27, 3, 0, "_dependencyMap"], [6, 41, 3, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_interfaces"], [7, 17, 4, 0], [7, 20, 4, 0, "require"], [7, 27, 4, 0], [7, 28, 4, 0, "_dependencyMap"], [7, 42, 4, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "obj"], [8, 30, 1, 28], [8, 32, 1, 30, "key"], [8, 35, 1, 33], [8, 37, 1, 35, "value"], [8, 42, 1, 40], [8, 44, 1, 42], [9, 4, 1, 44], [9, 8, 1, 48, "key"], [9, 11, 1, 51], [9, 15, 1, 55, "obj"], [9, 18, 1, 58], [9, 20, 1, 60], [10, 6, 1, 62, "Object"], [10, 12, 1, 68], [10, 13, 1, 69, "defineProperty"], [10, 27, 1, 83], [10, 28, 1, 84, "obj"], [10, 31, 1, 87], [10, 33, 1, 89, "key"], [10, 36, 1, 92], [10, 38, 1, 94], [11, 8, 1, 96, "value"], [11, 13, 1, 101], [11, 15, 1, 103, "value"], [11, 20, 1, 108], [12, 8, 1, 110, "enumerable"], [12, 18, 1, 120], [12, 20, 1, 122], [12, 24, 1, 126], [13, 8, 1, 128, "configurable"], [13, 20, 1, 140], [13, 22, 1, 142], [13, 26, 1, 146], [14, 8, 1, 148, "writable"], [14, 16, 1, 156], [14, 18, 1, 158], [15, 6, 1, 163], [15, 7, 1, 164], [15, 8, 1, 165], [16, 4, 1, 167], [16, 5, 1, 168], [16, 11, 1, 174], [17, 6, 1, 176, "obj"], [17, 9, 1, 179], [17, 10, 1, 180, "key"], [17, 13, 1, 183], [17, 14, 1, 184], [17, 17, 1, 187, "value"], [17, 22, 1, 192], [18, 4, 1, 194], [19, 4, 1, 196], [19, 11, 1, 203, "obj"], [19, 14, 1, 206], [20, 2, 1, 208], [21, 2, 5, 15], [21, 8, 5, 21, "ScaleGestureDetector"], [21, 28, 5, 41], [21, 29, 5, 42], [22, 4, 6, 2, "constructor"], [22, 15, 6, 13, "constructor"], [22, 16, 6, 14, "callbacks"], [22, 25, 6, 23], [22, 27, 6, 25], [23, 6, 7, 4, "_defineProperty"], [23, 21, 7, 19], [23, 22, 7, 20], [23, 26, 7, 24], [23, 28, 7, 26], [23, 42, 7, 40], [23, 44, 7, 42], [23, 49, 7, 47], [23, 50, 7, 48], [23, 51, 7, 49], [24, 6, 9, 4, "_defineProperty"], [24, 21, 9, 19], [24, 22, 9, 20], [24, 26, 9, 24], [24, 28, 9, 26], [24, 37, 9, 35], [24, 39, 9, 37], [24, 44, 9, 42], [24, 45, 9, 43], [24, 46, 9, 44], [25, 6, 11, 4, "_defineProperty"], [25, 21, 11, 19], [25, 22, 11, 20], [25, 26, 11, 24], [25, 28, 11, 26], [25, 40, 11, 38], [25, 42, 11, 40], [25, 47, 11, 45], [25, 48, 11, 46], [25, 49, 11, 47], [26, 6, 13, 4, "_defineProperty"], [26, 21, 13, 19], [26, 22, 13, 20], [26, 26, 13, 24], [26, 28, 13, 26], [26, 37, 13, 35], [26, 39, 13, 37], [26, 44, 13, 42], [26, 45, 13, 43], [26, 46, 13, 44], [27, 6, 15, 4, "_defineProperty"], [27, 21, 15, 19], [27, 22, 15, 20], [27, 26, 15, 24], [27, 28, 15, 26], [27, 37, 15, 35], [27, 39, 15, 37], [27, 44, 15, 42], [27, 45, 15, 43], [27, 46, 15, 44], [28, 6, 17, 4, "_defineProperty"], [28, 21, 17, 19], [28, 22, 17, 20], [28, 26, 17, 24], [28, 28, 17, 26], [28, 42, 17, 40], [28, 44, 17, 42], [28, 49, 17, 47], [28, 50, 17, 48], [28, 51, 17, 49], [29, 6, 19, 4, "_defineProperty"], [29, 21, 19, 19], [29, 22, 19, 20], [29, 26, 19, 24], [29, 28, 19, 26], [29, 38, 19, 36], [29, 40, 19, 38], [29, 45, 19, 43], [29, 46, 19, 44], [29, 47, 19, 45], [30, 6, 21, 4, "_defineProperty"], [30, 21, 21, 19], [30, 22, 21, 20], [30, 26, 21, 24], [30, 28, 21, 26], [30, 41, 21, 39], [30, 43, 21, 41], [30, 48, 21, 46], [30, 49, 21, 47], [30, 50, 21, 48], [31, 6, 23, 4, "_defineProperty"], [31, 21, 23, 19], [31, 22, 23, 20], [31, 26, 23, 24], [31, 28, 23, 26], [31, 41, 23, 39], [31, 43, 23, 41], [31, 48, 23, 46], [31, 49, 23, 47], [31, 50, 23, 48], [32, 6, 25, 4, "_defineProperty"], [32, 21, 25, 19], [32, 22, 25, 20], [32, 26, 25, 24], [32, 28, 25, 26], [32, 38, 25, 36], [32, 40, 25, 38], [32, 45, 25, 43], [32, 46, 25, 44], [32, 47, 25, 45], [33, 6, 27, 4, "_defineProperty"], [33, 21, 27, 19], [33, 22, 27, 20], [33, 26, 27, 24], [33, 28, 27, 26], [33, 40, 27, 38], [33, 42, 27, 40], [33, 47, 27, 45], [33, 48, 27, 46], [34, 6, 29, 4, "_defineProperty"], [34, 21, 29, 19], [34, 22, 29, 20], [34, 26, 29, 24], [34, 28, 29, 26], [34, 38, 29, 36], [34, 40, 29, 38], [34, 45, 29, 43], [34, 46, 29, 44], [34, 47, 29, 45], [35, 6, 31, 4, "_defineProperty"], [35, 21, 31, 19], [35, 22, 31, 20], [35, 26, 31, 24], [35, 28, 31, 26], [35, 37, 31, 35], [35, 39, 31, 37], [35, 44, 31, 42], [35, 45, 31, 43], [35, 46, 31, 44], [36, 6, 33, 4], [36, 10, 33, 8], [36, 11, 33, 9, "onScaleBegin"], [36, 23, 33, 21], [36, 26, 33, 24, "callbacks"], [36, 35, 33, 33], [36, 36, 33, 34, "onScaleBegin"], [36, 48, 33, 46], [37, 6, 34, 4], [37, 10, 34, 8], [37, 11, 34, 9, "onScale"], [37, 18, 34, 16], [37, 21, 34, 19, "callbacks"], [37, 30, 34, 28], [37, 31, 34, 29, "onScale"], [37, 38, 34, 36], [38, 6, 35, 4], [38, 10, 35, 8], [38, 11, 35, 9, "onScaleEnd"], [38, 21, 35, 19], [38, 24, 35, 22, "callbacks"], [38, 33, 35, 31], [38, 34, 35, 32, "onScaleEnd"], [38, 44, 35, 42], [39, 6, 36, 4], [39, 10, 36, 8], [39, 11, 36, 9, "spanSlop"], [39, 19, 36, 17], [39, 22, 36, 20, "DEFAULT_TOUCH_SLOP"], [39, 51, 36, 38], [39, 54, 36, 41], [39, 55, 36, 42], [40, 6, 37, 4], [40, 10, 37, 8], [40, 11, 37, 9, "minSpan"], [40, 18, 37, 16], [40, 21, 37, 19], [40, 22, 37, 20], [41, 4, 38, 2], [42, 4, 40, 2, "onTouchEvent"], [42, 16, 40, 14, "onTouchEvent"], [42, 17, 40, 15, "event"], [42, 22, 40, 20], [42, 24, 40, 22, "tracker"], [42, 31, 40, 29], [42, 33, 40, 31], [43, 6, 41, 4], [43, 10, 41, 8], [43, 11, 41, 9, "currentTime"], [43, 22, 41, 20], [43, 25, 41, 23, "event"], [43, 30, 41, 28], [43, 31, 41, 29, "time"], [43, 35, 41, 33], [44, 6, 42, 4], [44, 12, 42, 10, "action"], [44, 18, 42, 16], [44, 21, 42, 19, "event"], [44, 26, 42, 24], [44, 27, 42, 25, "eventType"], [44, 36, 42, 34], [45, 6, 43, 4], [45, 12, 43, 10, "numOfPointers"], [45, 25, 43, 23], [45, 28, 43, 26, "tracker"], [45, 35, 43, 33], [45, 36, 43, 34, "trackedPointersCount"], [45, 56, 43, 54], [46, 6, 44, 4], [46, 12, 44, 10, "streamComplete"], [46, 26, 44, 24], [46, 29, 44, 27, "action"], [46, 35, 44, 33], [46, 40, 44, 38, "EventTypes"], [46, 62, 44, 48], [46, 63, 44, 49, "UP"], [46, 65, 44, 51], [46, 69, 44, 55, "action"], [46, 75, 44, 61], [46, 80, 44, 66, "EventTypes"], [46, 102, 44, 76], [46, 103, 44, 77, "ADDITIONAL_POINTER_UP"], [46, 124, 44, 98], [46, 128, 44, 102, "action"], [46, 134, 44, 108], [46, 139, 44, 113, "EventTypes"], [46, 161, 44, 123], [46, 162, 44, 124, "CANCEL"], [46, 168, 44, 130], [47, 6, 46, 4], [47, 10, 46, 8, "action"], [47, 16, 46, 14], [47, 21, 46, 19, "EventTypes"], [47, 43, 46, 29], [47, 44, 46, 30, "DOWN"], [47, 48, 46, 34], [47, 52, 46, 38, "streamComplete"], [47, 66, 46, 52], [47, 68, 46, 54], [48, 8, 47, 6], [48, 12, 47, 10], [48, 16, 47, 14], [48, 17, 47, 15, "inProgress"], [48, 27, 47, 25], [48, 29, 47, 27], [49, 10, 48, 8], [49, 14, 48, 12], [49, 15, 48, 13, "onScaleEnd"], [49, 25, 48, 23], [49, 26, 48, 24], [49, 30, 48, 28], [49, 31, 48, 29], [50, 10, 49, 8], [50, 14, 49, 12], [50, 15, 49, 13, "inProgress"], [50, 25, 49, 23], [50, 28, 49, 26], [50, 33, 49, 31], [51, 10, 50, 8], [51, 14, 50, 12], [51, 15, 50, 13, "initialSpan"], [51, 26, 50, 24], [51, 29, 50, 27], [51, 30, 50, 28], [52, 8, 51, 6], [53, 8, 53, 6], [53, 12, 53, 10, "streamComplete"], [53, 26, 53, 24], [53, 28, 53, 26], [54, 10, 54, 8], [54, 17, 54, 15], [54, 21, 54, 19], [55, 8, 55, 6], [56, 6, 56, 4], [57, 6, 58, 4], [57, 12, 58, 10, "config<PERSON><PERSON><PERSON>"], [57, 25, 58, 23], [57, 28, 58, 26, "action"], [57, 34, 58, 32], [57, 39, 58, 37, "EventTypes"], [57, 61, 58, 47], [57, 62, 58, 48, "DOWN"], [57, 66, 58, 52], [57, 70, 58, 56, "action"], [57, 76, 58, 62], [57, 81, 58, 67, "EventTypes"], [57, 103, 58, 77], [57, 104, 58, 78, "ADDITIONAL_POINTER_UP"], [57, 125, 58, 99], [57, 129, 58, 103, "action"], [57, 135, 58, 109], [57, 140, 58, 114, "EventTypes"], [57, 162, 58, 124], [57, 163, 58, 125, "ADDITIONAL_POINTER_DOWN"], [57, 186, 58, 148], [58, 6, 59, 4], [58, 12, 59, 10, "pointerUp"], [58, 21, 59, 19], [58, 24, 59, 22, "action"], [58, 30, 59, 28], [58, 35, 59, 33, "EventTypes"], [58, 57, 59, 43], [58, 58, 59, 44, "ADDITIONAL_POINTER_UP"], [58, 79, 59, 65], [59, 6, 60, 4], [59, 12, 60, 10, "ignoredPointer"], [59, 26, 60, 24], [59, 29, 60, 27, "pointerUp"], [59, 38, 60, 36], [59, 41, 60, 39, "event"], [59, 46, 60, 44], [59, 47, 60, 45, "pointerId"], [59, 56, 60, 54], [59, 59, 60, 57, "undefined"], [59, 68, 60, 66], [59, 69, 60, 67], [59, 70, 60, 68], [61, 6, 62, 4], [61, 12, 62, 10, "div"], [61, 15, 62, 13], [61, 18, 62, 16, "pointerUp"], [61, 27, 62, 25], [61, 30, 62, 28, "numOfPointers"], [61, 43, 62, 41], [61, 46, 62, 44], [61, 47, 62, 45], [61, 50, 62, 48, "numOfPointers"], [61, 63, 62, 61], [62, 6, 63, 4], [62, 12, 63, 10, "coordsSum"], [62, 21, 63, 19], [62, 24, 63, 22, "tracker"], [62, 31, 63, 29], [62, 32, 63, 30, "getAbsoluteCoordsSum"], [62, 52, 63, 50], [62, 53, 63, 51], [62, 54, 63, 52], [63, 6, 64, 4], [63, 12, 64, 10, "focusX"], [63, 18, 64, 16], [63, 21, 64, 19, "coordsSum"], [63, 30, 64, 28], [63, 31, 64, 29, "x"], [63, 32, 64, 30], [63, 35, 64, 33, "div"], [63, 38, 64, 36], [64, 6, 65, 4], [64, 12, 65, 10, "focusY"], [64, 18, 65, 16], [64, 21, 65, 19, "coordsSum"], [64, 30, 65, 28], [64, 31, 65, 29, "y"], [64, 32, 65, 30], [64, 35, 65, 33, "div"], [64, 38, 65, 36], [64, 39, 65, 37], [64, 40, 65, 38], [66, 6, 67, 4], [66, 10, 67, 8, "devSumX"], [66, 17, 67, 15], [66, 20, 67, 18], [66, 21, 67, 19], [67, 6, 68, 4], [67, 10, 68, 8, "devSumY"], [67, 17, 68, 15], [67, 20, 68, 18], [67, 21, 68, 19], [68, 6, 69, 4, "tracker"], [68, 13, 69, 11], [68, 14, 69, 12, "trackedPointers"], [68, 29, 69, 27], [68, 30, 69, 28, "for<PERSON>ach"], [68, 37, 69, 35], [68, 38, 69, 36], [68, 39, 69, 37, "value"], [68, 44, 69, 42], [68, 46, 69, 44, "key"], [68, 49, 69, 47], [68, 54, 69, 52], [69, 8, 70, 6], [69, 12, 70, 10, "key"], [69, 15, 70, 13], [69, 20, 70, 18, "ignoredPointer"], [69, 34, 70, 32], [69, 36, 70, 34], [70, 10, 71, 8], [71, 8, 72, 6], [72, 8, 74, 6, "devSumX"], [72, 15, 74, 13], [72, 19, 74, 17, "Math"], [72, 23, 74, 21], [72, 24, 74, 22, "abs"], [72, 27, 74, 25], [72, 28, 74, 26, "value"], [72, 33, 74, 31], [72, 34, 74, 32, "abosoluteCoords"], [72, 49, 74, 47], [72, 50, 74, 48, "x"], [72, 51, 74, 49], [72, 54, 74, 52, "focusX"], [72, 60, 74, 58], [72, 61, 74, 59], [73, 8, 75, 6, "devSumY"], [73, 15, 75, 13], [73, 19, 75, 17, "Math"], [73, 23, 75, 21], [73, 24, 75, 22, "abs"], [73, 27, 75, 25], [73, 28, 75, 26, "value"], [73, 33, 75, 31], [73, 34, 75, 32, "abosoluteCoords"], [73, 49, 75, 47], [73, 50, 75, 48, "y"], [73, 51, 75, 49], [73, 54, 75, 52, "focusY"], [73, 60, 75, 58], [73, 61, 75, 59], [74, 6, 76, 4], [74, 7, 76, 5], [74, 8, 76, 6], [75, 6, 77, 4], [75, 12, 77, 10, "devX"], [75, 16, 77, 14], [75, 19, 77, 17, "devSumX"], [75, 26, 77, 24], [75, 29, 77, 27, "div"], [75, 32, 77, 30], [76, 6, 78, 4], [76, 12, 78, 10, "devY"], [76, 16, 78, 14], [76, 19, 78, 17, "devSumY"], [76, 26, 78, 24], [76, 29, 78, 27, "div"], [76, 32, 78, 30], [77, 6, 79, 4], [77, 12, 79, 10, "spanX"], [77, 17, 79, 15], [77, 20, 79, 18, "devX"], [77, 24, 79, 22], [77, 27, 79, 25], [77, 28, 79, 26], [78, 6, 80, 4], [78, 12, 80, 10, "spanY"], [78, 17, 80, 15], [78, 20, 80, 18, "devY"], [78, 24, 80, 22], [78, 27, 80, 25], [78, 28, 80, 26], [79, 6, 81, 4], [79, 12, 81, 10, "span"], [79, 16, 81, 14], [79, 19, 81, 17, "Math"], [79, 23, 81, 21], [79, 24, 81, 22, "hypot"], [79, 29, 81, 27], [79, 30, 81, 28, "spanX"], [79, 35, 81, 33], [79, 37, 81, 35, "spanY"], [79, 42, 81, 40], [79, 43, 81, 41], [79, 44, 81, 42], [79, 45, 81, 43], [81, 6, 83, 4], [81, 12, 83, 10, "wasInProgress"], [81, 25, 83, 23], [81, 28, 83, 26], [81, 32, 83, 30], [81, 33, 83, 31, "inProgress"], [81, 43, 83, 41], [82, 6, 84, 4], [82, 10, 84, 8], [82, 11, 84, 9, "_focusX"], [82, 18, 84, 16], [82, 21, 84, 19, "focusX"], [82, 27, 84, 25], [83, 6, 85, 4], [83, 10, 85, 8], [83, 11, 85, 9, "_focusY"], [83, 18, 85, 16], [83, 21, 85, 19, "focusY"], [83, 27, 85, 25], [84, 6, 87, 4], [84, 10, 87, 8], [84, 14, 87, 12], [84, 15, 87, 13, "inProgress"], [84, 25, 87, 23], [84, 30, 87, 28, "span"], [84, 34, 87, 32], [84, 37, 87, 35], [84, 41, 87, 39], [84, 42, 87, 40, "minSpan"], [84, 49, 87, 47], [84, 53, 87, 51, "config<PERSON><PERSON><PERSON>"], [84, 66, 87, 64], [84, 67, 87, 65], [84, 69, 87, 67], [85, 8, 88, 6], [85, 12, 88, 10], [85, 13, 88, 11, "onScaleEnd"], [85, 23, 88, 21], [85, 24, 88, 22], [85, 28, 88, 26], [85, 29, 88, 27], [86, 8, 89, 6], [86, 12, 89, 10], [86, 13, 89, 11, "inProgress"], [86, 23, 89, 21], [86, 26, 89, 24], [86, 31, 89, 29], [87, 8, 90, 6], [87, 12, 90, 10], [87, 13, 90, 11, "initialSpan"], [87, 24, 90, 22], [87, 27, 90, 25, "span"], [87, 31, 90, 29], [88, 6, 91, 4], [89, 6, 93, 4], [89, 10, 93, 8, "config<PERSON><PERSON><PERSON>"], [89, 23, 93, 21], [89, 25, 93, 23], [90, 8, 94, 6], [90, 12, 94, 10], [90, 13, 94, 11, "initialSpan"], [90, 24, 94, 22], [90, 27, 94, 25], [90, 31, 94, 29], [90, 32, 94, 30, "prevSpan"], [90, 40, 94, 38], [90, 43, 94, 41], [90, 47, 94, 45], [90, 48, 94, 46, "_currentSpan"], [90, 60, 94, 58], [90, 63, 94, 61, "span"], [90, 67, 94, 65], [91, 6, 95, 4], [92, 6, 97, 4], [92, 10, 97, 8], [92, 11, 97, 9], [92, 15, 97, 13], [92, 16, 97, 14, "inProgress"], [92, 26, 97, 24], [92, 30, 97, 28, "span"], [92, 34, 97, 32], [92, 38, 97, 36], [92, 42, 97, 40], [92, 43, 97, 41, "minSpan"], [92, 50, 97, 48], [92, 55, 97, 53, "wasInProgress"], [92, 68, 97, 66], [92, 72, 97, 70, "Math"], [92, 76, 97, 74], [92, 77, 97, 75, "abs"], [92, 80, 97, 78], [92, 81, 97, 79, "span"], [92, 85, 97, 83], [92, 88, 97, 86], [92, 92, 97, 90], [92, 93, 97, 91, "initialSpan"], [92, 104, 97, 102], [92, 105, 97, 103], [92, 108, 97, 106], [92, 112, 97, 110], [92, 113, 97, 111, "spanSlop"], [92, 121, 97, 119], [92, 122, 97, 120], [92, 124, 97, 122], [93, 8, 98, 6], [93, 12, 98, 10], [93, 13, 98, 11, "prevSpan"], [93, 21, 98, 19], [93, 24, 98, 22], [93, 28, 98, 26], [93, 29, 98, 27, "_currentSpan"], [93, 41, 98, 39], [93, 44, 98, 42, "span"], [93, 48, 98, 46], [94, 8, 99, 6], [94, 12, 99, 10], [94, 13, 99, 11, "prevTime"], [94, 21, 99, 19], [94, 24, 99, 22], [94, 28, 99, 26], [94, 29, 99, 27, "currentTime"], [94, 40, 99, 38], [95, 8, 100, 6], [95, 12, 100, 10], [95, 13, 100, 11, "inProgress"], [95, 23, 100, 21], [95, 26, 100, 24], [95, 30, 100, 28], [95, 31, 100, 29, "onScaleBegin"], [95, 43, 100, 41], [95, 44, 100, 42], [95, 48, 100, 46], [95, 49, 100, 47], [96, 6, 101, 4], [96, 7, 101, 5], [96, 8, 101, 6], [98, 6, 104, 4], [98, 10, 104, 8, "action"], [98, 16, 104, 14], [98, 21, 104, 19, "EventTypes"], [98, 43, 104, 29], [98, 44, 104, 30, "MOVE"], [98, 48, 104, 34], [98, 50, 104, 36], [99, 8, 105, 6], [99, 15, 105, 13], [99, 19, 105, 17], [100, 6, 106, 4], [101, 6, 108, 4], [101, 10, 108, 8], [101, 11, 108, 9, "_currentSpan"], [101, 23, 108, 21], [101, 26, 108, 24, "span"], [101, 30, 108, 28], [102, 6, 110, 4], [102, 10, 110, 8], [102, 14, 110, 12], [102, 15, 110, 13, "inProgress"], [102, 25, 110, 23], [102, 29, 110, 27], [102, 30, 110, 28], [102, 34, 110, 32], [102, 35, 110, 33, "onScale"], [102, 42, 110, 40], [102, 43, 110, 41], [102, 47, 110, 45], [102, 48, 110, 46], [102, 50, 110, 48], [103, 8, 111, 6], [103, 15, 111, 13], [103, 19, 111, 17], [104, 6, 112, 4], [105, 6, 114, 4], [105, 10, 114, 8], [105, 11, 114, 9, "prevSpan"], [105, 19, 114, 17], [105, 22, 114, 20], [105, 26, 114, 24], [105, 27, 114, 25, "currentSpan"], [105, 38, 114, 36], [106, 6, 115, 4], [106, 10, 115, 8], [106, 11, 115, 9, "prevTime"], [106, 19, 115, 17], [106, 22, 115, 20], [106, 26, 115, 24], [106, 27, 115, 25, "currentTime"], [106, 38, 115, 36], [107, 6, 116, 4], [107, 13, 116, 11], [107, 17, 116, 15], [108, 4, 117, 2], [109, 4, 119, 2, "calculateScaleFactor"], [109, 24, 119, 22, "calculateScaleFactor"], [109, 25, 119, 23, "numOfPointers"], [109, 38, 119, 36], [109, 40, 119, 38], [110, 6, 120, 4], [110, 10, 120, 8, "numOfPointers"], [110, 23, 120, 21], [110, 26, 120, 24], [110, 27, 120, 25], [110, 29, 120, 27], [111, 8, 121, 6], [111, 15, 121, 13], [111, 16, 121, 14], [112, 6, 122, 4], [113, 6, 124, 4], [113, 13, 124, 11], [113, 17, 124, 15], [113, 18, 124, 16, "prevSpan"], [113, 26, 124, 24], [113, 29, 124, 27], [113, 30, 124, 28], [113, 33, 124, 31], [113, 37, 124, 35], [113, 38, 124, 36, "currentSpan"], [113, 49, 124, 47], [113, 52, 124, 50], [113, 56, 124, 54], [113, 57, 124, 55, "prevSpan"], [113, 65, 124, 63], [113, 68, 124, 66], [113, 69, 124, 67], [114, 4, 125, 2], [115, 4, 127, 2], [115, 8, 127, 6, "currentSpan"], [115, 19, 127, 17, "currentSpan"], [115, 20, 127, 17], [115, 22, 127, 20], [116, 6, 128, 4], [116, 13, 128, 11], [116, 17, 128, 15], [116, 18, 128, 16, "_currentSpan"], [116, 30, 128, 28], [117, 4, 129, 2], [118, 4, 131, 2], [118, 8, 131, 6, "focusX"], [118, 14, 131, 12, "focusX"], [118, 15, 131, 12], [118, 17, 131, 15], [119, 6, 132, 4], [119, 13, 132, 11], [119, 17, 132, 15], [119, 18, 132, 16, "_focusX"], [119, 25, 132, 23], [120, 4, 133, 2], [121, 4, 135, 2], [121, 8, 135, 6, "focusY"], [121, 14, 135, 12, "focusY"], [121, 15, 135, 12], [121, 17, 135, 15], [122, 6, 136, 4], [122, 13, 136, 11], [122, 17, 136, 15], [122, 18, 136, 16, "_focusY"], [122, 25, 136, 23], [123, 4, 137, 2], [124, 4, 139, 2], [124, 8, 139, 6, "<PERSON><PERSON><PERSON><PERSON>"], [124, 17, 139, 15, "<PERSON><PERSON><PERSON><PERSON>"], [124, 18, 139, 15], [124, 20, 139, 18], [125, 6, 140, 4], [125, 13, 140, 11], [125, 17, 140, 15], [125, 18, 140, 16, "currentTime"], [125, 29, 140, 27], [125, 32, 140, 30], [125, 36, 140, 34], [125, 37, 140, 35, "prevTime"], [125, 45, 140, 43], [126, 4, 141, 2], [127, 2, 143, 0], [128, 2, 143, 1, "exports"], [128, 9, 143, 1], [128, 10, 143, 1, "default"], [128, 17, 143, 1], [128, 20, 143, 1, "ScaleGestureDetector"], [128, 40, 143, 1], [129, 0, 143, 1], [129, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "ScaleGestureDetector", "constructor", "onTouchEvent", "tracker.trackedPointers.forEach$argument_0", "calculateScaleFactor", "get__currentSpan", "get__focusX", "get__focusY", "get__time<PERSON><PERSON><PERSON>"], "mappings": "AAA,iNC;eCI;ECC;GDgC;EEE;oCC6B;KDO;GFyC;EIE;GJM;EKE;GLE;EME;GNE;EOE;GPE;EQE;GRE;CDE"}}, "type": "js/module"}]}