{"dependencies": [{"name": "./api", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 14, "index": 118}, "end": {"line": 4, "column": 30, "index": 134}}], "key": "gR+OLYNEYAFJQY/iXy79rrSgjmk=", "exportNames": ["*"]}}, {"name": "./third-party-libs/react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 41, "index": 177}, "end": {"line": 5, "column": 101, "index": 237}}], "key": "5gusNuZtNLDFdLTlS2mHqKGbv0k=", "exportNames": ["*"]}}, {"name": "./components", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 12, "index": 468}, "end": {"line": 12, "column": 35, "index": 491}}], "key": "rey9w3Yoxs2VzDVPeTjulBvVQ8c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = wrapJSX;\n  const api_1 = require(_dependencyMap[0], \"./api\");\n  const react_native_safe_area_context_1 = require(_dependencyMap[1], \"./third-party-libs/react-native-safe-area-context\");\n  function wrapJSX(jsx) {\n    return function (type, props, ...rest) {\n      if (type === \"react-native-css-interop-jsx-pragma-check\") {\n        return true;\n      }\n      if (process.env.NODE_ENV !== \"test\") require(_dependencyMap[2], \"./components\");\n      type = (0, react_native_safe_area_context_1.maybeHijackSafeAreaProvider)(type);\n      if (props && props.cssInterop === false) {\n        delete props.cssInterop;\n      } else {\n        type = api_1.interopComponents.get(type) ?? type;\n      }\n      return jsx.call(jsx, type, props, ...rest);\n    };\n  }\n});", "lineCount": 25, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "default"], [7, 17, 3, 15], [7, 20, 3, 18, "wrapJSX"], [7, 27, 3, 25], [8, 2, 4, 0], [8, 8, 4, 6, "api_1"], [8, 13, 4, 11], [8, 16, 4, 14, "require"], [8, 23, 4, 21], [8, 24, 4, 21, "_dependencyMap"], [8, 38, 4, 21], [8, 50, 4, 29], [8, 51, 4, 30], [9, 2, 5, 0], [9, 8, 5, 6, "react_native_safe_area_context_1"], [9, 40, 5, 38], [9, 43, 5, 41, "require"], [9, 50, 5, 48], [9, 51, 5, 48, "_dependencyMap"], [9, 65, 5, 48], [9, 121, 5, 100], [9, 122, 5, 101], [10, 2, 6, 0], [10, 11, 6, 9, "wrapJSX"], [10, 18, 6, 16, "wrapJSX"], [10, 19, 6, 17, "jsx"], [10, 22, 6, 20], [10, 24, 6, 22], [11, 4, 7, 4], [11, 11, 7, 11], [11, 21, 7, 21, "type"], [11, 25, 7, 25], [11, 27, 7, 27, "props"], [11, 32, 7, 32], [11, 34, 7, 34], [11, 37, 7, 37, "rest"], [11, 41, 7, 41], [11, 43, 7, 43], [12, 6, 8, 8], [12, 10, 8, 12, "type"], [12, 14, 8, 16], [12, 19, 8, 21], [12, 62, 8, 64], [12, 64, 8, 66], [13, 8, 9, 12], [13, 15, 9, 19], [13, 19, 9, 23], [14, 6, 10, 8], [15, 6, 11, 8], [15, 10, 11, 12, "process"], [15, 17, 11, 19], [15, 18, 11, 20, "env"], [15, 21, 11, 23], [15, 22, 11, 24, "NODE_ENV"], [15, 30, 11, 32], [15, 35, 11, 37], [15, 41, 11, 43], [15, 43, 12, 12, "require"], [15, 50, 12, 19], [15, 51, 12, 19, "_dependencyMap"], [15, 65, 12, 19], [15, 84, 12, 34], [15, 85, 12, 35], [16, 6, 13, 8, "type"], [16, 10, 13, 12], [16, 13, 13, 15], [16, 14, 13, 16], [16, 15, 13, 17], [16, 17, 13, 19, "react_native_safe_area_context_1"], [16, 49, 13, 51], [16, 50, 13, 52, "maybeHijackSafeAreaProvider"], [16, 77, 13, 79], [16, 79, 13, 81, "type"], [16, 83, 13, 85], [16, 84, 13, 86], [17, 6, 14, 8], [17, 10, 14, 12, "props"], [17, 15, 14, 17], [17, 19, 14, 21, "props"], [17, 24, 14, 26], [17, 25, 14, 27, "cssInterop"], [17, 35, 14, 37], [17, 40, 14, 42], [17, 45, 14, 47], [17, 47, 14, 49], [18, 8, 15, 12], [18, 15, 15, 19, "props"], [18, 20, 15, 24], [18, 21, 15, 25, "cssInterop"], [18, 31, 15, 35], [19, 6, 16, 8], [19, 7, 16, 9], [19, 13, 17, 13], [20, 8, 18, 12, "type"], [20, 12, 18, 16], [20, 15, 18, 19, "api_1"], [20, 20, 18, 24], [20, 21, 18, 25, "interopComponents"], [20, 38, 18, 42], [20, 39, 18, 43, "get"], [20, 42, 18, 46], [20, 43, 18, 47, "type"], [20, 47, 18, 51], [20, 48, 18, 52], [20, 52, 18, 56, "type"], [20, 56, 18, 60], [21, 6, 19, 8], [22, 6, 20, 8], [22, 13, 20, 15, "jsx"], [22, 16, 20, 18], [22, 17, 20, 19, "call"], [22, 21, 20, 23], [22, 22, 20, 24, "jsx"], [22, 25, 20, 27], [22, 27, 20, 29, "type"], [22, 31, 20, 33], [22, 33, 20, 35, "props"], [22, 38, 20, 40], [22, 40, 20, 42], [22, 43, 20, 45, "rest"], [22, 47, 20, 49], [22, 48, 20, 50], [23, 4, 21, 4], [23, 5, 21, 5], [24, 2, 22, 0], [25, 0, 22, 1], [25, 3]], "functionMap": {"names": ["<global>", "wrapJSX", "<anonymous>"], "mappings": "AAA;ACK;WCC;KDc;CDC"}}, "type": "js/module"}]}