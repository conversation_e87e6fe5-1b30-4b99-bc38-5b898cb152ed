{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.queryOptions = queryOptions;\n  // src/queryOptions.ts\n  function queryOptions(options) {\n    return options;\n  }\n});", "lineCount": 10, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [7, 11, 2, 9, "queryOptions"], [7, 23, 2, 21, "queryOptions"], [7, 24, 2, 22, "options"], [7, 31, 2, 29], [7, 33, 2, 31], [8, 4, 3, 2], [8, 11, 3, 9, "options"], [8, 18, 3, 16], [9, 2, 4, 0], [10, 0, 4, 1], [10, 3]], "functionMap": {"names": ["<global>", "queryOptions"], "mappings": "AAA;ACC;CDE"}}, "type": "js/module"}]}