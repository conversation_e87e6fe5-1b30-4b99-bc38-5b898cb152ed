{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/extends", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "yLIpKqfSeOZo7yhmpj6jeRbKj/A=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutPropertiesLoose", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 96, "index": 151}}], "key": "h/v2q98AsT4QTiU2QmCS7mQfUgY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 504}, "end": {"line": 13, "column": 31, "index": 535}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 536}, "end": {"line": 14, "column": 39, "index": 575}}], "key": "Pz10tXyA/z/1zTYUTTxDDbnOtjE=", "exportNames": ["*"]}}, {"name": "../View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 576}, "end": {"line": 15, "column": 27, "index": 603}}], "key": "z+h67QhWT4Dd/ILcrpyPJ2FPLGs=", "exportNames": ["*"]}}, {"name": "../../modules/useMergeRefs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 604}, "end": {"line": 16, "column": 54, "index": 658}}], "key": "oyajprDCZUWctC+xesKf9XgogFo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _extends2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/extends\"));\n  var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[5], \"../View\"));\n  var _useMergeRefs = _interopRequireDefault(require(_dependencyMap[6], \"../../modules/useMergeRefs\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  var _excluded = [\"onScroll\", \"onTouchMove\", \"onWheel\", \"scrollEnabled\", \"scrollEventThrottle\", \"showsHorizontalScrollIndicator\", \"showsVerticalScrollIndicator\", \"style\"];\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  function normalizeScrollEvent(e) {\n    return {\n      nativeEvent: {\n        contentOffset: {\n          get x() {\n            return e.target.scrollLeft;\n          },\n          get y() {\n            return e.target.scrollTop;\n          }\n        },\n        contentSize: {\n          get height() {\n            return e.target.scrollHeight;\n          },\n          get width() {\n            return e.target.scrollWidth;\n          }\n        },\n        layoutMeasurement: {\n          get height() {\n            return e.target.offsetHeight;\n          },\n          get width() {\n            return e.target.offsetWidth;\n          }\n        }\n      },\n      timeStamp: Date.now()\n    };\n  }\n  function shouldEmitScrollEvent(lastTick, eventThrottle) {\n    var timeSinceLastTick = Date.now() - lastTick;\n    return eventThrottle > 0 && timeSinceLastTick >= eventThrottle;\n  }\n\n  /**\n   * Encapsulates the Web-specific scroll throttling and disabling logic\n   */\n  var ScrollViewBase = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n    var onScroll = props.onScroll,\n      onTouchMove = props.onTouchMove,\n      onWheel = props.onWheel,\n      _props$scrollEnabled = props.scrollEnabled,\n      scrollEnabled = _props$scrollEnabled === void 0 ? true : _props$scrollEnabled,\n      _props$scrollEventThr = props.scrollEventThrottle,\n      scrollEventThrottle = _props$scrollEventThr === void 0 ? 0 : _props$scrollEventThr,\n      showsHorizontalScrollIndicator = props.showsHorizontalScrollIndicator,\n      showsVerticalScrollIndicator = props.showsVerticalScrollIndicator,\n      style = props.style,\n      rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n    var scrollState = React.useRef({\n      isScrolling: false,\n      scrollLastTick: 0\n    });\n    var scrollTimeout = React.useRef(null);\n    var scrollRef = React.useRef(null);\n    function createPreventableScrollHandler(handler) {\n      return e => {\n        if (scrollEnabled) {\n          if (handler) {\n            handler(e);\n          }\n        }\n      };\n    }\n    function handleScroll(e) {\n      e.stopPropagation();\n      if (e.target === scrollRef.current) {\n        e.persist();\n        // A scroll happened, so the scroll resets the scrollend timeout.\n        if (scrollTimeout.current != null) {\n          clearTimeout(scrollTimeout.current);\n        }\n        scrollTimeout.current = setTimeout(() => {\n          handleScrollEnd(e);\n        }, 100);\n        if (scrollState.current.isScrolling) {\n          // Scroll last tick may have changed, check if we need to notify\n          if (shouldEmitScrollEvent(scrollState.current.scrollLastTick, scrollEventThrottle)) {\n            handleScrollTick(e);\n          }\n        } else {\n          // Weren't scrolling, so we must have just started\n          handleScrollStart(e);\n        }\n      }\n    }\n    function handleScrollStart(e) {\n      scrollState.current.isScrolling = true;\n      handleScrollTick(e);\n    }\n    function handleScrollTick(e) {\n      scrollState.current.scrollLastTick = Date.now();\n      if (onScroll) {\n        onScroll(normalizeScrollEvent(e));\n      }\n    }\n    function handleScrollEnd(e) {\n      scrollState.current.isScrolling = false;\n      if (onScroll) {\n        onScroll(normalizeScrollEvent(e));\n      }\n    }\n    var hideScrollbar = showsHorizontalScrollIndicator === false || showsVerticalScrollIndicator === false;\n    return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, {\n      onScroll: handleScroll,\n      onTouchMove: createPreventableScrollHandler(onTouchMove),\n      onWheel: createPreventableScrollHandler(onWheel),\n      ref: (0, _useMergeRefs.default)(scrollRef, forwardedRef),\n      style: [style, !scrollEnabled && styles.scrollDisabled, hideScrollbar && styles.hideScrollbar]\n    }));\n  });\n\n  // Chrome doesn't support e.preventDefault in this case; touch-action must be\n  // used to disable scrolling.\n  // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n  var styles = _StyleSheet.default.create({\n    scrollDisabled: {\n      overflowX: 'hidden',\n      overflowY: 'hidden',\n      touchAction: 'none'\n    },\n    hideScrollbar: {\n      scrollbarWidth: 'none'\n    }\n  });\n  var _default = exports.default = ScrollViewBase;\n});", "lineCount": 152, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_extends2"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_objectWithoutPropertiesLoose2"], [8, 36, 2, 0], [8, 39, 2, 0, "_interopRequireDefault"], [8, 61, 2, 0], [8, 62, 2, 0, "require"], [8, 69, 2, 0], [8, 70, 2, 0, "_dependencyMap"], [8, 84, 2, 0], [9, 2, 13, 0], [9, 6, 13, 0, "React"], [9, 11, 13, 0], [9, 14, 13, 0, "_interopRequireWildcard"], [9, 37, 13, 0], [9, 38, 13, 0, "require"], [9, 45, 13, 0], [9, 46, 13, 0, "_dependencyMap"], [9, 60, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_StyleSheet"], [10, 17, 14, 0], [10, 20, 14, 0, "_interopRequireDefault"], [10, 42, 14, 0], [10, 43, 14, 0, "require"], [10, 50, 14, 0], [10, 51, 14, 0, "_dependencyMap"], [10, 65, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_View"], [11, 11, 15, 0], [11, 14, 15, 0, "_interopRequireDefault"], [11, 36, 15, 0], [11, 37, 15, 0, "require"], [11, 44, 15, 0], [11, 45, 15, 0, "_dependencyMap"], [11, 59, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_useMergeRefs"], [12, 19, 16, 0], [12, 22, 16, 0, "_interopRequireDefault"], [12, 44, 16, 0], [12, 45, 16, 0, "require"], [12, 52, 16, 0], [12, 53, 16, 0, "_dependencyMap"], [12, 67, 16, 0], [13, 2, 16, 54], [13, 11, 16, 54, "_interopRequireWildcard"], [13, 35, 16, 54, "e"], [13, 36, 16, 54], [13, 38, 16, 54, "t"], [13, 39, 16, 54], [13, 68, 16, 54, "WeakMap"], [13, 75, 16, 54], [13, 81, 16, 54, "r"], [13, 82, 16, 54], [13, 89, 16, 54, "WeakMap"], [13, 96, 16, 54], [13, 100, 16, 54, "n"], [13, 101, 16, 54], [13, 108, 16, 54, "WeakMap"], [13, 115, 16, 54], [13, 127, 16, 54, "_interopRequireWildcard"], [13, 150, 16, 54], [13, 162, 16, 54, "_interopRequireWildcard"], [13, 163, 16, 54, "e"], [13, 164, 16, 54], [13, 166, 16, 54, "t"], [13, 167, 16, 54], [13, 176, 16, 54, "t"], [13, 177, 16, 54], [13, 181, 16, 54, "e"], [13, 182, 16, 54], [13, 186, 16, 54, "e"], [13, 187, 16, 54], [13, 188, 16, 54, "__esModule"], [13, 198, 16, 54], [13, 207, 16, 54, "e"], [13, 208, 16, 54], [13, 214, 16, 54, "o"], [13, 215, 16, 54], [13, 217, 16, 54, "i"], [13, 218, 16, 54], [13, 220, 16, 54, "f"], [13, 221, 16, 54], [13, 226, 16, 54, "__proto__"], [13, 235, 16, 54], [13, 243, 16, 54, "default"], [13, 250, 16, 54], [13, 252, 16, 54, "e"], [13, 253, 16, 54], [13, 270, 16, 54, "e"], [13, 271, 16, 54], [13, 294, 16, 54, "e"], [13, 295, 16, 54], [13, 320, 16, 54, "e"], [13, 321, 16, 54], [13, 330, 16, 54, "f"], [13, 331, 16, 54], [13, 337, 16, 54, "o"], [13, 338, 16, 54], [13, 341, 16, 54, "t"], [13, 342, 16, 54], [13, 345, 16, 54, "n"], [13, 346, 16, 54], [13, 349, 16, 54, "r"], [13, 350, 16, 54], [13, 358, 16, 54, "o"], [13, 359, 16, 54], [13, 360, 16, 54, "has"], [13, 363, 16, 54], [13, 364, 16, 54, "e"], [13, 365, 16, 54], [13, 375, 16, 54, "o"], [13, 376, 16, 54], [13, 377, 16, 54, "get"], [13, 380, 16, 54], [13, 381, 16, 54, "e"], [13, 382, 16, 54], [13, 385, 16, 54, "o"], [13, 386, 16, 54], [13, 387, 16, 54, "set"], [13, 390, 16, 54], [13, 391, 16, 54, "e"], [13, 392, 16, 54], [13, 394, 16, 54, "f"], [13, 395, 16, 54], [13, 411, 16, 54, "t"], [13, 412, 16, 54], [13, 416, 16, 54, "e"], [13, 417, 16, 54], [13, 433, 16, 54, "t"], [13, 434, 16, 54], [13, 441, 16, 54, "hasOwnProperty"], [13, 455, 16, 54], [13, 456, 16, 54, "call"], [13, 460, 16, 54], [13, 461, 16, 54, "e"], [13, 462, 16, 54], [13, 464, 16, 54, "t"], [13, 465, 16, 54], [13, 472, 16, 54, "i"], [13, 473, 16, 54], [13, 477, 16, 54, "o"], [13, 478, 16, 54], [13, 481, 16, 54, "Object"], [13, 487, 16, 54], [13, 488, 16, 54, "defineProperty"], [13, 502, 16, 54], [13, 507, 16, 54, "Object"], [13, 513, 16, 54], [13, 514, 16, 54, "getOwnPropertyDescriptor"], [13, 538, 16, 54], [13, 539, 16, 54, "e"], [13, 540, 16, 54], [13, 542, 16, 54, "t"], [13, 543, 16, 54], [13, 550, 16, 54, "i"], [13, 551, 16, 54], [13, 552, 16, 54, "get"], [13, 555, 16, 54], [13, 559, 16, 54, "i"], [13, 560, 16, 54], [13, 561, 16, 54, "set"], [13, 564, 16, 54], [13, 568, 16, 54, "o"], [13, 569, 16, 54], [13, 570, 16, 54, "f"], [13, 571, 16, 54], [13, 573, 16, 54, "t"], [13, 574, 16, 54], [13, 576, 16, 54, "i"], [13, 577, 16, 54], [13, 581, 16, 54, "f"], [13, 582, 16, 54], [13, 583, 16, 54, "t"], [13, 584, 16, 54], [13, 588, 16, 54, "e"], [13, 589, 16, 54], [13, 590, 16, 54, "t"], [13, 591, 16, 54], [13, 602, 16, 54, "f"], [13, 603, 16, 54], [13, 608, 16, 54, "e"], [13, 609, 16, 54], [13, 611, 16, 54, "t"], [13, 612, 16, 54], [14, 2, 3, 0], [14, 6, 3, 4, "_excluded"], [14, 15, 3, 13], [14, 18, 3, 16], [14, 19, 3, 17], [14, 29, 3, 27], [14, 31, 3, 29], [14, 44, 3, 42], [14, 46, 3, 44], [14, 55, 3, 53], [14, 57, 3, 55], [14, 72, 3, 70], [14, 74, 3, 72], [14, 95, 3, 93], [14, 97, 3, 95], [14, 129, 3, 127], [14, 131, 3, 129], [14, 161, 3, 159], [14, 163, 3, 161], [14, 170, 3, 168], [14, 171, 3, 169], [15, 2, 4, 0], [16, 0, 5, 0], [17, 0, 6, 0], [18, 0, 7, 0], [19, 0, 8, 0], [20, 0, 9, 0], [21, 0, 10, 0], [22, 0, 11, 0], [24, 2, 17, 0], [24, 11, 17, 9, "normalizeScrollEvent"], [24, 31, 17, 29, "normalizeScrollEvent"], [24, 32, 17, 30, "e"], [24, 33, 17, 31], [24, 35, 17, 33], [25, 4, 18, 2], [25, 11, 18, 9], [26, 6, 19, 4, "nativeEvent"], [26, 17, 19, 15], [26, 19, 19, 17], [27, 8, 20, 6, "contentOffset"], [27, 21, 20, 19], [27, 23, 20, 21], [28, 10, 21, 8], [28, 14, 21, 12, "x"], [28, 15, 21, 13, "x"], [28, 16, 21, 13], [28, 18, 21, 16], [29, 12, 22, 10], [29, 19, 22, 17, "e"], [29, 20, 22, 18], [29, 21, 22, 19, "target"], [29, 27, 22, 25], [29, 28, 22, 26, "scrollLeft"], [29, 38, 22, 36], [30, 10, 23, 8], [30, 11, 23, 9], [31, 10, 24, 8], [31, 14, 24, 12, "y"], [31, 15, 24, 13, "y"], [31, 16, 24, 13], [31, 18, 24, 16], [32, 12, 25, 10], [32, 19, 25, 17, "e"], [32, 20, 25, 18], [32, 21, 25, 19, "target"], [32, 27, 25, 25], [32, 28, 25, 26, "scrollTop"], [32, 37, 25, 35], [33, 10, 26, 8], [34, 8, 27, 6], [34, 9, 27, 7], [35, 8, 28, 6, "contentSize"], [35, 19, 28, 17], [35, 21, 28, 19], [36, 10, 29, 8], [36, 14, 29, 12, "height"], [36, 20, 29, 18, "height"], [36, 21, 29, 18], [36, 23, 29, 21], [37, 12, 30, 10], [37, 19, 30, 17, "e"], [37, 20, 30, 18], [37, 21, 30, 19, "target"], [37, 27, 30, 25], [37, 28, 30, 26, "scrollHeight"], [37, 40, 30, 38], [38, 10, 31, 8], [38, 11, 31, 9], [39, 10, 32, 8], [39, 14, 32, 12, "width"], [39, 19, 32, 17, "width"], [39, 20, 32, 17], [39, 22, 32, 20], [40, 12, 33, 10], [40, 19, 33, 17, "e"], [40, 20, 33, 18], [40, 21, 33, 19, "target"], [40, 27, 33, 25], [40, 28, 33, 26, "scrollWidth"], [40, 39, 33, 37], [41, 10, 34, 8], [42, 8, 35, 6], [42, 9, 35, 7], [43, 8, 36, 6, "layoutMeasurement"], [43, 25, 36, 23], [43, 27, 36, 25], [44, 10, 37, 8], [44, 14, 37, 12, "height"], [44, 20, 37, 18, "height"], [44, 21, 37, 18], [44, 23, 37, 21], [45, 12, 38, 10], [45, 19, 38, 17, "e"], [45, 20, 38, 18], [45, 21, 38, 19, "target"], [45, 27, 38, 25], [45, 28, 38, 26, "offsetHeight"], [45, 40, 38, 38], [46, 10, 39, 8], [46, 11, 39, 9], [47, 10, 40, 8], [47, 14, 40, 12, "width"], [47, 19, 40, 17, "width"], [47, 20, 40, 17], [47, 22, 40, 20], [48, 12, 41, 10], [48, 19, 41, 17, "e"], [48, 20, 41, 18], [48, 21, 41, 19, "target"], [48, 27, 41, 25], [48, 28, 41, 26, "offsetWidth"], [48, 39, 41, 37], [49, 10, 42, 8], [50, 8, 43, 6], [51, 6, 44, 4], [51, 7, 44, 5], [52, 6, 45, 4, "timeStamp"], [52, 15, 45, 13], [52, 17, 45, 15, "Date"], [52, 21, 45, 19], [52, 22, 45, 20, "now"], [52, 25, 45, 23], [52, 26, 45, 24], [53, 4, 46, 2], [53, 5, 46, 3], [54, 2, 47, 0], [55, 2, 48, 0], [55, 11, 48, 9, "shouldEmitScrollEvent"], [55, 32, 48, 30, "shouldEmitScrollEvent"], [55, 33, 48, 31, "lastTick"], [55, 41, 48, 39], [55, 43, 48, 41, "eventThrottle"], [55, 56, 48, 54], [55, 58, 48, 56], [56, 4, 49, 2], [56, 8, 49, 6, "timeSinceLastTick"], [56, 25, 49, 23], [56, 28, 49, 26, "Date"], [56, 32, 49, 30], [56, 33, 49, 31, "now"], [56, 36, 49, 34], [56, 37, 49, 35], [56, 38, 49, 36], [56, 41, 49, 39, "lastTick"], [56, 49, 49, 47], [57, 4, 50, 2], [57, 11, 50, 9, "eventThrottle"], [57, 24, 50, 22], [57, 27, 50, 25], [57, 28, 50, 26], [57, 32, 50, 30, "timeSinceLastTick"], [57, 49, 50, 47], [57, 53, 50, 51, "eventThrottle"], [57, 66, 50, 64], [58, 2, 51, 0], [60, 2, 53, 0], [61, 0, 54, 0], [62, 0, 55, 0], [63, 2, 56, 0], [63, 6, 56, 4, "ScrollViewBase"], [63, 20, 56, 18], [63, 23, 56, 21], [63, 36, 56, 34, "React"], [63, 41, 56, 39], [63, 42, 56, 40, "forwardRef"], [63, 52, 56, 50], [63, 53, 56, 51], [63, 54, 56, 52, "props"], [63, 59, 56, 57], [63, 61, 56, 59, "forwardedRef"], [63, 73, 56, 71], [63, 78, 56, 76], [64, 4, 57, 2], [64, 8, 57, 6, "onScroll"], [64, 16, 57, 14], [64, 19, 57, 17, "props"], [64, 24, 57, 22], [64, 25, 57, 23, "onScroll"], [64, 33, 57, 31], [65, 6, 58, 4, "onTouchMove"], [65, 17, 58, 15], [65, 20, 58, 18, "props"], [65, 25, 58, 23], [65, 26, 58, 24, "onTouchMove"], [65, 37, 58, 35], [66, 6, 59, 4, "onWheel"], [66, 13, 59, 11], [66, 16, 59, 14, "props"], [66, 21, 59, 19], [66, 22, 59, 20, "onWheel"], [66, 29, 59, 27], [67, 6, 60, 4, "_props$scrollEnabled"], [67, 26, 60, 24], [67, 29, 60, 27, "props"], [67, 34, 60, 32], [67, 35, 60, 33, "scrollEnabled"], [67, 48, 60, 46], [68, 6, 61, 4, "scrollEnabled"], [68, 19, 61, 17], [68, 22, 61, 20, "_props$scrollEnabled"], [68, 42, 61, 40], [68, 47, 61, 45], [68, 52, 61, 50], [68, 53, 61, 51], [68, 56, 61, 54], [68, 60, 61, 58], [68, 63, 61, 61, "_props$scrollEnabled"], [68, 83, 61, 81], [69, 6, 62, 4, "_props$scrollEventThr"], [69, 27, 62, 25], [69, 30, 62, 28, "props"], [69, 35, 62, 33], [69, 36, 62, 34, "scrollEventThrottle"], [69, 55, 62, 53], [70, 6, 63, 4, "scrollEventThrottle"], [70, 25, 63, 23], [70, 28, 63, 26, "_props$scrollEventThr"], [70, 49, 63, 47], [70, 54, 63, 52], [70, 59, 63, 57], [70, 60, 63, 58], [70, 63, 63, 61], [70, 64, 63, 62], [70, 67, 63, 65, "_props$scrollEventThr"], [70, 88, 63, 86], [71, 6, 64, 4, "showsHorizontalScrollIndicator"], [71, 36, 64, 34], [71, 39, 64, 37, "props"], [71, 44, 64, 42], [71, 45, 64, 43, "showsHorizontalScrollIndicator"], [71, 75, 64, 73], [72, 6, 65, 4, "showsVerticalScrollIndicator"], [72, 34, 65, 32], [72, 37, 65, 35, "props"], [72, 42, 65, 40], [72, 43, 65, 41, "showsVerticalScrollIndicator"], [72, 71, 65, 69], [73, 6, 66, 4, "style"], [73, 11, 66, 9], [73, 14, 66, 12, "props"], [73, 19, 66, 17], [73, 20, 66, 18, "style"], [73, 25, 66, 23], [74, 6, 67, 4, "rest"], [74, 10, 67, 8], [74, 13, 67, 11], [74, 17, 67, 11, "_objectWithoutPropertiesLoose"], [74, 55, 67, 40], [74, 57, 67, 41, "props"], [74, 62, 67, 46], [74, 64, 67, 48, "_excluded"], [74, 73, 67, 57], [74, 74, 67, 58], [75, 4, 68, 2], [75, 8, 68, 6, "scrollState"], [75, 19, 68, 17], [75, 22, 68, 20, "React"], [75, 27, 68, 25], [75, 28, 68, 26, "useRef"], [75, 34, 68, 32], [75, 35, 68, 33], [76, 6, 69, 4, "isScrolling"], [76, 17, 69, 15], [76, 19, 69, 17], [76, 24, 69, 22], [77, 6, 70, 4, "scrollLastTick"], [77, 20, 70, 18], [77, 22, 70, 20], [78, 4, 71, 2], [78, 5, 71, 3], [78, 6, 71, 4], [79, 4, 72, 2], [79, 8, 72, 6, "scrollTimeout"], [79, 21, 72, 19], [79, 24, 72, 22, "React"], [79, 29, 72, 27], [79, 30, 72, 28, "useRef"], [79, 36, 72, 34], [79, 37, 72, 35], [79, 41, 72, 39], [79, 42, 72, 40], [80, 4, 73, 2], [80, 8, 73, 6, "scrollRef"], [80, 17, 73, 15], [80, 20, 73, 18, "React"], [80, 25, 73, 23], [80, 26, 73, 24, "useRef"], [80, 32, 73, 30], [80, 33, 73, 31], [80, 37, 73, 35], [80, 38, 73, 36], [81, 4, 74, 2], [81, 13, 74, 11, "createPreventableScrollHandler"], [81, 43, 74, 41, "createPreventableScrollHandler"], [81, 44, 74, 42, "handler"], [81, 51, 74, 49], [81, 53, 74, 51], [82, 6, 75, 4], [82, 13, 75, 11, "e"], [82, 14, 75, 12], [82, 18, 75, 16], [83, 8, 76, 6], [83, 12, 76, 10, "scrollEnabled"], [83, 25, 76, 23], [83, 27, 76, 25], [84, 10, 77, 8], [84, 14, 77, 12, "handler"], [84, 21, 77, 19], [84, 23, 77, 21], [85, 12, 78, 10, "handler"], [85, 19, 78, 17], [85, 20, 78, 18, "e"], [85, 21, 78, 19], [85, 22, 78, 20], [86, 10, 79, 8], [87, 8, 80, 6], [88, 6, 81, 4], [88, 7, 81, 5], [89, 4, 82, 2], [90, 4, 83, 2], [90, 13, 83, 11, "handleScroll"], [90, 25, 83, 23, "handleScroll"], [90, 26, 83, 24, "e"], [90, 27, 83, 25], [90, 29, 83, 27], [91, 6, 84, 4, "e"], [91, 7, 84, 5], [91, 8, 84, 6, "stopPropagation"], [91, 23, 84, 21], [91, 24, 84, 22], [91, 25, 84, 23], [92, 6, 85, 4], [92, 10, 85, 8, "e"], [92, 11, 85, 9], [92, 12, 85, 10, "target"], [92, 18, 85, 16], [92, 23, 85, 21, "scrollRef"], [92, 32, 85, 30], [92, 33, 85, 31, "current"], [92, 40, 85, 38], [92, 42, 85, 40], [93, 8, 86, 6, "e"], [93, 9, 86, 7], [93, 10, 86, 8, "persist"], [93, 17, 86, 15], [93, 18, 86, 16], [93, 19, 86, 17], [94, 8, 87, 6], [95, 8, 88, 6], [95, 12, 88, 10, "scrollTimeout"], [95, 25, 88, 23], [95, 26, 88, 24, "current"], [95, 33, 88, 31], [95, 37, 88, 35], [95, 41, 88, 39], [95, 43, 88, 41], [96, 10, 89, 8, "clearTimeout"], [96, 22, 89, 20], [96, 23, 89, 21, "scrollTimeout"], [96, 36, 89, 34], [96, 37, 89, 35, "current"], [96, 44, 89, 42], [96, 45, 89, 43], [97, 8, 90, 6], [98, 8, 91, 6, "scrollTimeout"], [98, 21, 91, 19], [98, 22, 91, 20, "current"], [98, 29, 91, 27], [98, 32, 91, 30, "setTimeout"], [98, 42, 91, 40], [98, 43, 91, 41], [98, 49, 91, 47], [99, 10, 92, 8, "handleScrollEnd"], [99, 25, 92, 23], [99, 26, 92, 24, "e"], [99, 27, 92, 25], [99, 28, 92, 26], [100, 8, 93, 6], [100, 9, 93, 7], [100, 11, 93, 9], [100, 14, 93, 12], [100, 15, 93, 13], [101, 8, 94, 6], [101, 12, 94, 10, "scrollState"], [101, 23, 94, 21], [101, 24, 94, 22, "current"], [101, 31, 94, 29], [101, 32, 94, 30, "isScrolling"], [101, 43, 94, 41], [101, 45, 94, 43], [102, 10, 95, 8], [103, 10, 96, 8], [103, 14, 96, 12, "shouldEmitScrollEvent"], [103, 35, 96, 33], [103, 36, 96, 34, "scrollState"], [103, 47, 96, 45], [103, 48, 96, 46, "current"], [103, 55, 96, 53], [103, 56, 96, 54, "scrollLastTick"], [103, 70, 96, 68], [103, 72, 96, 70, "scrollEventThrottle"], [103, 91, 96, 89], [103, 92, 96, 90], [103, 94, 96, 92], [104, 12, 97, 10, "handleScrollTick"], [104, 28, 97, 26], [104, 29, 97, 27, "e"], [104, 30, 97, 28], [104, 31, 97, 29], [105, 10, 98, 8], [106, 8, 99, 6], [106, 9, 99, 7], [106, 15, 99, 13], [107, 10, 100, 8], [108, 10, 101, 8, "handleScrollStart"], [108, 27, 101, 25], [108, 28, 101, 26, "e"], [108, 29, 101, 27], [108, 30, 101, 28], [109, 8, 102, 6], [110, 6, 103, 4], [111, 4, 104, 2], [112, 4, 105, 2], [112, 13, 105, 11, "handleScrollStart"], [112, 30, 105, 28, "handleScrollStart"], [112, 31, 105, 29, "e"], [112, 32, 105, 30], [112, 34, 105, 32], [113, 6, 106, 4, "scrollState"], [113, 17, 106, 15], [113, 18, 106, 16, "current"], [113, 25, 106, 23], [113, 26, 106, 24, "isScrolling"], [113, 37, 106, 35], [113, 40, 106, 38], [113, 44, 106, 42], [114, 6, 107, 4, "handleScrollTick"], [114, 22, 107, 20], [114, 23, 107, 21, "e"], [114, 24, 107, 22], [114, 25, 107, 23], [115, 4, 108, 2], [116, 4, 109, 2], [116, 13, 109, 11, "handleScrollTick"], [116, 29, 109, 27, "handleScrollTick"], [116, 30, 109, 28, "e"], [116, 31, 109, 29], [116, 33, 109, 31], [117, 6, 110, 4, "scrollState"], [117, 17, 110, 15], [117, 18, 110, 16, "current"], [117, 25, 110, 23], [117, 26, 110, 24, "scrollLastTick"], [117, 40, 110, 38], [117, 43, 110, 41, "Date"], [117, 47, 110, 45], [117, 48, 110, 46, "now"], [117, 51, 110, 49], [117, 52, 110, 50], [117, 53, 110, 51], [118, 6, 111, 4], [118, 10, 111, 8, "onScroll"], [118, 18, 111, 16], [118, 20, 111, 18], [119, 8, 112, 6, "onScroll"], [119, 16, 112, 14], [119, 17, 112, 15, "normalizeScrollEvent"], [119, 37, 112, 35], [119, 38, 112, 36, "e"], [119, 39, 112, 37], [119, 40, 112, 38], [119, 41, 112, 39], [120, 6, 113, 4], [121, 4, 114, 2], [122, 4, 115, 2], [122, 13, 115, 11, "handleScrollEnd"], [122, 28, 115, 26, "handleScrollEnd"], [122, 29, 115, 27, "e"], [122, 30, 115, 28], [122, 32, 115, 30], [123, 6, 116, 4, "scrollState"], [123, 17, 116, 15], [123, 18, 116, 16, "current"], [123, 25, 116, 23], [123, 26, 116, 24, "isScrolling"], [123, 37, 116, 35], [123, 40, 116, 38], [123, 45, 116, 43], [124, 6, 117, 4], [124, 10, 117, 8, "onScroll"], [124, 18, 117, 16], [124, 20, 117, 18], [125, 8, 118, 6, "onScroll"], [125, 16, 118, 14], [125, 17, 118, 15, "normalizeScrollEvent"], [125, 37, 118, 35], [125, 38, 118, 36, "e"], [125, 39, 118, 37], [125, 40, 118, 38], [125, 41, 118, 39], [126, 6, 119, 4], [127, 4, 120, 2], [128, 4, 121, 2], [128, 8, 121, 6, "hideScrollbar"], [128, 21, 121, 19], [128, 24, 121, 22, "showsHorizontalScrollIndicator"], [128, 54, 121, 52], [128, 59, 121, 57], [128, 64, 121, 62], [128, 68, 121, 66, "showsVerticalScrollIndicator"], [128, 96, 121, 94], [128, 101, 121, 99], [128, 106, 121, 104], [129, 4, 122, 2], [129, 11, 122, 9], [129, 24, 122, 22, "React"], [129, 29, 122, 27], [129, 30, 122, 28, "createElement"], [129, 43, 122, 41], [129, 44, 122, 42, "View"], [129, 57, 122, 46], [129, 59, 122, 48], [129, 63, 122, 48, "_extends"], [129, 80, 122, 56], [129, 82, 122, 57], [129, 83, 122, 58], [129, 84, 122, 59], [129, 86, 122, 61, "rest"], [129, 90, 122, 65], [129, 92, 122, 67], [130, 6, 123, 4, "onScroll"], [130, 14, 123, 12], [130, 16, 123, 14, "handleScroll"], [130, 28, 123, 26], [131, 6, 124, 4, "onTouchMove"], [131, 17, 124, 15], [131, 19, 124, 17, "createPreventableScrollHandler"], [131, 49, 124, 47], [131, 50, 124, 48, "onTouchMove"], [131, 61, 124, 59], [131, 62, 124, 60], [132, 6, 125, 4, "onWheel"], [132, 13, 125, 11], [132, 15, 125, 13, "createPreventableScrollHandler"], [132, 45, 125, 43], [132, 46, 125, 44, "onWheel"], [132, 53, 125, 51], [132, 54, 125, 52], [133, 6, 126, 4, "ref"], [133, 9, 126, 7], [133, 11, 126, 9], [133, 15, 126, 9, "useMergeRefs"], [133, 36, 126, 21], [133, 38, 126, 22, "scrollRef"], [133, 47, 126, 31], [133, 49, 126, 33, "forwardedRef"], [133, 61, 126, 45], [133, 62, 126, 46], [134, 6, 127, 4, "style"], [134, 11, 127, 9], [134, 13, 127, 11], [134, 14, 127, 12, "style"], [134, 19, 127, 17], [134, 21, 127, 19], [134, 22, 127, 20, "scrollEnabled"], [134, 35, 127, 33], [134, 39, 127, 37, "styles"], [134, 45, 127, 43], [134, 46, 127, 44, "scrollDisabled"], [134, 60, 127, 58], [134, 62, 127, 60, "hideScrollbar"], [134, 75, 127, 73], [134, 79, 127, 77, "styles"], [134, 85, 127, 83], [134, 86, 127, 84, "hideScrollbar"], [134, 99, 127, 97], [135, 4, 128, 2], [135, 5, 128, 3], [135, 6, 128, 4], [135, 7, 128, 5], [136, 2, 129, 0], [136, 3, 129, 1], [136, 4, 129, 2], [138, 2, 131, 0], [139, 2, 132, 0], [140, 2, 133, 0], [141, 2, 134, 0], [141, 6, 134, 4, "styles"], [141, 12, 134, 10], [141, 15, 134, 13, "StyleSheet"], [141, 34, 134, 23], [141, 35, 134, 24, "create"], [141, 41, 134, 30], [141, 42, 134, 31], [142, 4, 135, 2, "scrollDisabled"], [142, 18, 135, 16], [142, 20, 135, 18], [143, 6, 136, 4, "overflowX"], [143, 15, 136, 13], [143, 17, 136, 15], [143, 25, 136, 23], [144, 6, 137, 4, "overflowY"], [144, 15, 137, 13], [144, 17, 137, 15], [144, 25, 137, 23], [145, 6, 138, 4, "touchAction"], [145, 17, 138, 15], [145, 19, 138, 17], [146, 4, 139, 2], [146, 5, 139, 3], [147, 4, 140, 2, "hideScrollbar"], [147, 17, 140, 15], [147, 19, 140, 17], [148, 6, 141, 4, "scrollbarWidth"], [148, 20, 141, 18], [148, 22, 141, 20], [149, 4, 142, 2], [150, 2, 143, 0], [150, 3, 143, 1], [150, 4, 143, 2], [151, 2, 143, 3], [151, 6, 143, 3, "_default"], [151, 14, 143, 3], [151, 17, 143, 3, "exports"], [151, 24, 143, 3], [151, 25, 143, 3, "default"], [151, 32, 143, 3], [151, 35, 144, 15, "ScrollViewBase"], [151, 49, 144, 29], [152, 0, 144, 29], [152, 3]], "functionMap": {"names": ["<global>", "normalizeScrollEvent", "nativeEvent.contentOffset.get__x", "nativeEvent.contentOffset.get__y", "nativeEvent.contentSize.get__height", "nativeEvent.contentSize.get__width", "nativeEvent.layoutMeasurement.get__height", "nativeEvent.layoutMeasurement.get__width", "shouldEmitScrollEvent", "React.forwardRef$argument_0", "createPreventableScrollHandler", "<anonymous>", "handleScroll", "setTimeout$argument_0", "handleScrollStart", "handleScrollTick", "handleScrollEnd"], "mappings": "AAA;ACgB;QCI;SDE;QEC;SFE;QGG;SHE;QIC;SJE;QKG;SLE;QMC;SNE;CDK;AQC;CRG;mDSK;ECkB;WCC;KDM;GDC;EGC;yCCQ;ODE;GHW;EKC;GLG;EMC;GNK;EOC;GPK;CTS"}}, "type": "js/module"}]}