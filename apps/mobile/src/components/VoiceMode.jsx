import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Mic, Square, MicOff } from 'lucide-react-native';
import { useColors } from './useColors';
import AudioVisualizer from './AudioVisualizer';
import RealtimeTranscript from './RealtimeTranscript';

export default function VoiceMode({ isRecording, onRecord, hasPermission, isLoading, transcript, isMuted, onMute }) {
  const colors = useColors();
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    // Simulate speaking state for the visualizer
    if (isRecording) {
      setIsSpeaking(true);
    } else {
      setIsSpeaking(false);
    }
  }, [isRecording]);

  return (
    <View style={{ flex: 1 }}>
      <AudioVisualizer isSpeaking={isSpeaking} />
      <RealtimeTranscript transcript={transcript} />

      {/* Main record button - centered at bottom */}
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          alignItems: 'center',
          gap: 12,
          paddingHorizontal: 16,
          paddingTop: 16,
          paddingBottom: 16,
          backgroundColor: 'transparent',
        }}
      >
        <TouchableOpacity
          style={{
            width: 72,
            height: 72,
            borderRadius: 36,
            backgroundColor: isRecording ? '#FF3B30' : colors.primary,
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            elevation: 4,
          }}
          onPress={onRecord}
          disabled={!hasPermission || isLoading}
        >
          {isRecording ? (
            <Square size={28} color={colors.background} fill={colors.background} />
          ) : (
            <Mic size={28} color={colors.background} />
          )}
        </TouchableOpacity>
        <Text
          style={{
            fontSize: 14,
            fontFamily: 'Poppins_400Regular',
            color: colors.textSecondary,
            textAlign: 'center',
          }}
        >
          {isRecording
            ? 'Always listening - tap to stop'
            : hasPermission
              ? 'Tap to start voice session'
              : 'Microphone permission needed'}
        </Text>
      </View>

      {/* Mute/Unmute button - bottom right */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 32,
          right: 24,
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: isMuted ? '#FF3B30' : colors.primary,
          alignItems: 'center',
          justifyContent: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
          elevation: 4,
        }}
        onPress={onMute}
      >
        {isMuted ? (
          <MicOff size={24} color={colors.background} />
        ) : (
          <Mic size={24} color={colors.background} />
        )}
      </TouchableOpacity>
    </View>
  );
}
