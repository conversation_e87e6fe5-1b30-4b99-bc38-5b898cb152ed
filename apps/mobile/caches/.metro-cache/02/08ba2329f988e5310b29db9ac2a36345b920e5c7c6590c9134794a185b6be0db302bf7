{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "expo-router", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 33, "column": 0, "index": 987}, "end": {"line": 33, "column": 40, "index": 1027}}], "key": "/+ErnBisjrT6aDU+GRp5Qz/lYoY=", "exportNames": ["*"]}}, {"name": "@tanstack/react-query", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1028}, "end": {"line": 34, "column": 68, "index": 1096}}], "key": "Pzwu/0TIyhnZOrC9PAkpZx92hFo=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _expoRouter = require(_dependencyMap[21], \"expo-router\");\n  var _reactQuery = require(_dependencyMap[22], \"@tanstack/react-query\");\n  var _jsxDevRuntime = require(_dependencyMap[23], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n    };\n    const handleSendMessage = (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"9C1WkPoISXmHucsqM0n1HH2DnhY=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 601, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 33, 0], [27, 6, 33, 0, "_expoRouter"], [27, 17, 33, 0], [27, 20, 33, 0, "require"], [27, 27, 33, 0], [27, 28, 33, 0, "_dependencyMap"], [27, 42, 33, 0], [28, 2, 34, 0], [28, 6, 34, 0, "_reactQuery"], [28, 17, 34, 0], [28, 20, 34, 0, "require"], [28, 27, 34, 0], [28, 28, 34, 0, "_dependencyMap"], [28, 42, 34, 0], [29, 2, 34, 68], [29, 6, 34, 68, "_jsxDevRuntime"], [29, 20, 34, 68], [29, 23, 34, 68, "require"], [29, 30, 34, 68], [29, 31, 34, 68, "_dependencyMap"], [29, 45, 34, 68], [30, 2, 34, 68], [30, 6, 34, 68, "_jsxFileName"], [30, 18, 34, 68], [31, 4, 34, 68, "_s"], [31, 6, 34, 68], [31, 9, 34, 68, "$RefreshSig$"], [31, 21, 34, 68], [32, 2, 34, 68], [32, 11, 34, 68, "_interopRequireWildcard"], [32, 35, 34, 68, "e"], [32, 36, 34, 68], [32, 38, 34, 68, "t"], [32, 39, 34, 68], [32, 68, 34, 68, "WeakMap"], [32, 75, 34, 68], [32, 81, 34, 68, "r"], [32, 82, 34, 68], [32, 89, 34, 68, "WeakMap"], [32, 96, 34, 68], [32, 100, 34, 68, "n"], [32, 101, 34, 68], [32, 108, 34, 68, "WeakMap"], [32, 115, 34, 68], [32, 127, 34, 68, "_interopRequireWildcard"], [32, 150, 34, 68], [32, 162, 34, 68, "_interopRequireWildcard"], [32, 163, 34, 68, "e"], [32, 164, 34, 68], [32, 166, 34, 68, "t"], [32, 167, 34, 68], [32, 176, 34, 68, "t"], [32, 177, 34, 68], [32, 181, 34, 68, "e"], [32, 182, 34, 68], [32, 186, 34, 68, "e"], [32, 187, 34, 68], [32, 188, 34, 68, "__esModule"], [32, 198, 34, 68], [32, 207, 34, 68, "e"], [32, 208, 34, 68], [32, 214, 34, 68, "o"], [32, 215, 34, 68], [32, 217, 34, 68, "i"], [32, 218, 34, 68], [32, 220, 34, 68, "f"], [32, 221, 34, 68], [32, 226, 34, 68, "__proto__"], [32, 235, 34, 68], [32, 243, 34, 68, "default"], [32, 250, 34, 68], [32, 252, 34, 68, "e"], [32, 253, 34, 68], [32, 270, 34, 68, "e"], [32, 271, 34, 68], [32, 294, 34, 68, "e"], [32, 295, 34, 68], [32, 320, 34, 68, "e"], [32, 321, 34, 68], [32, 330, 34, 68, "f"], [32, 331, 34, 68], [32, 337, 34, 68, "o"], [32, 338, 34, 68], [32, 341, 34, 68, "t"], [32, 342, 34, 68], [32, 345, 34, 68, "n"], [32, 346, 34, 68], [32, 349, 34, 68, "r"], [32, 350, 34, 68], [32, 358, 34, 68, "o"], [32, 359, 34, 68], [32, 360, 34, 68, "has"], [32, 363, 34, 68], [32, 364, 34, 68, "e"], [32, 365, 34, 68], [32, 375, 34, 68, "o"], [32, 376, 34, 68], [32, 377, 34, 68, "get"], [32, 380, 34, 68], [32, 381, 34, 68, "e"], [32, 382, 34, 68], [32, 385, 34, 68, "o"], [32, 386, 34, 68], [32, 387, 34, 68, "set"], [32, 390, 34, 68], [32, 391, 34, 68, "e"], [32, 392, 34, 68], [32, 394, 34, 68, "f"], [32, 395, 34, 68], [32, 411, 34, 68, "t"], [32, 412, 34, 68], [32, 416, 34, 68, "e"], [32, 417, 34, 68], [32, 433, 34, 68, "t"], [32, 434, 34, 68], [32, 441, 34, 68, "hasOwnProperty"], [32, 455, 34, 68], [32, 456, 34, 68, "call"], [32, 460, 34, 68], [32, 461, 34, 68, "e"], [32, 462, 34, 68], [32, 464, 34, 68, "t"], [32, 465, 34, 68], [32, 472, 34, 68, "i"], [32, 473, 34, 68], [32, 477, 34, 68, "o"], [32, 478, 34, 68], [32, 481, 34, 68, "Object"], [32, 487, 34, 68], [32, 488, 34, 68, "defineProperty"], [32, 502, 34, 68], [32, 507, 34, 68, "Object"], [32, 513, 34, 68], [32, 514, 34, 68, "getOwnPropertyDescriptor"], [32, 538, 34, 68], [32, 539, 34, 68, "e"], [32, 540, 34, 68], [32, 542, 34, 68, "t"], [32, 543, 34, 68], [32, 550, 34, 68, "i"], [32, 551, 34, 68], [32, 552, 34, 68, "get"], [32, 555, 34, 68], [32, 559, 34, 68, "i"], [32, 560, 34, 68], [32, 561, 34, 68, "set"], [32, 564, 34, 68], [32, 568, 34, 68, "o"], [32, 569, 34, 68], [32, 570, 34, 68, "f"], [32, 571, 34, 68], [32, 573, 34, 68, "t"], [32, 574, 34, 68], [32, 576, 34, 68, "i"], [32, 577, 34, 68], [32, 581, 34, 68, "f"], [32, 582, 34, 68], [32, 583, 34, 68, "t"], [32, 584, 34, 68], [32, 588, 34, 68, "e"], [32, 589, 34, 68], [32, 590, 34, 68, "t"], [32, 591, 34, 68], [32, 602, 34, 68, "f"], [32, 603, 34, 68], [32, 608, 34, 68, "e"], [32, 609, 34, 68], [32, 611, 34, 68, "t"], [32, 612, 34, 68], [33, 2, 36, 15], [33, 11, 36, 24, "BrainstormScreen"], [33, 27, 36, 40, "BrainstormScreen"], [33, 28, 36, 40], [33, 30, 36, 43], [34, 4, 36, 43, "_s"], [34, 6, 36, 43], [35, 4, 37, 2], [35, 10, 37, 8, "insets"], [35, 16, 37, 14], [35, 19, 37, 17], [35, 23, 37, 17, "useSafeAreaInsets"], [35, 68, 37, 34], [35, 70, 37, 35], [35, 71, 37, 36], [36, 4, 38, 2], [36, 10, 38, 8, "colors"], [36, 16, 38, 14], [36, 19, 38, 17], [36, 23, 38, 17, "useColors"], [36, 43, 38, 26], [36, 45, 38, 27], [36, 46, 38, 28], [37, 4, 39, 2], [37, 10, 39, 8], [37, 11, 39, 9, "fontsLoaded"], [37, 22, 39, 20], [37, 23, 39, 21], [37, 26, 39, 24], [37, 30, 39, 24, "useFonts"], [37, 47, 39, 32], [37, 49, 39, 33], [38, 6, 40, 4, "Poppins_400Regular"], [38, 24, 40, 22], [38, 26, 40, 4, "Poppins_400Regular"], [38, 53, 40, 22], [39, 6, 41, 4, "Poppins_500Medium"], [39, 23, 41, 21], [39, 25, 41, 4, "Poppins_500Medium"], [39, 51, 41, 21], [40, 6, 42, 4, "Poppins_600SemiBold"], [40, 25, 42, 23], [40, 27, 42, 4, "Poppins_600SemiBold"], [41, 4, 43, 2], [41, 5, 43, 3], [41, 6, 43, 4], [42, 4, 45, 2], [42, 10, 45, 8, "recorder"], [42, 18, 45, 16], [42, 21, 45, 19], [42, 25, 45, 19, "useAudioRecorder"], [42, 52, 45, 35], [42, 54, 45, 36, "RecordingPresets"], [42, 81, 45, 52], [42, 82, 45, 53, "HIGH_QUALITY"], [42, 94, 45, 65], [42, 95, 45, 66], [43, 4, 46, 2], [43, 10, 46, 8, "recorderState"], [43, 23, 46, 21], [43, 26, 46, 24], [43, 30, 46, 24, "useAudioRecorderState"], [43, 62, 46, 45], [43, 64, 46, 46, "recorder"], [43, 72, 46, 54], [43, 73, 46, 55], [44, 4, 48, 2], [44, 10, 48, 8], [44, 11, 48, 9, "messages"], [44, 19, 48, 17], [44, 21, 48, 19, "setMessages"], [44, 32, 48, 30], [44, 33, 48, 31], [44, 36, 48, 34], [44, 40, 48, 34, "useState"], [44, 55, 48, 42], [44, 57, 48, 43], [44, 59, 48, 45], [44, 60, 48, 46], [45, 4, 49, 2], [45, 10, 49, 8], [45, 11, 49, 9, "inputText"], [45, 20, 49, 18], [45, 22, 49, 20, "setInputText"], [45, 34, 49, 32], [45, 35, 49, 33], [45, 38, 49, 36], [45, 42, 49, 36, "useState"], [45, 57, 49, 44], [45, 59, 49, 45], [45, 61, 49, 47], [45, 62, 49, 48], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "isFirstTime"], [46, 22, 50, 20], [46, 24, 50, 22, "setIsFirstTime"], [46, 38, 50, 36], [46, 39, 50, 37], [46, 42, 50, 40], [46, 46, 50, 40, "useState"], [46, 61, 50, 48], [46, 63, 50, 49], [46, 67, 50, 53], [46, 68, 50, 54], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "isLoading"], [47, 20, 51, 18], [47, 22, 51, 20, "setIsLoading"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 64, 51, 50], [47, 65, 51, 51], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "quickActions"], [48, 23, 52, 21], [48, 25, 52, 23, "setQuickActions"], [48, 40, 52, 38], [48, 41, 52, 39], [48, 44, 52, 42], [48, 48, 52, 42, "useState"], [48, 63, 52, 50], [48, 65, 52, 51], [48, 67, 52, 53], [48, 68, 52, 54], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "voiceMode"], [49, 20, 53, 18], [49, 22, 53, 20, "setVoiceMode"], [49, 34, 53, 32], [49, 35, 53, 33], [49, 38, 53, 36], [49, 42, 53, 36, "useState"], [49, 57, 53, 44], [49, 59, 53, 45], [49, 63, 53, 49], [49, 64, 53, 50], [49, 65, 53, 51], [49, 66, 53, 52], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "hasPermission"], [50, 24, 54, 22], [50, 26, 54, 24, "setHasPermission"], [50, 42, 54, 40], [50, 43, 54, 41], [50, 46, 54, 44], [50, 50, 54, 44, "useState"], [50, 65, 54, 52], [50, 67, 54, 53], [50, 72, 54, 58], [50, 73, 54, 59], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "isDictating"], [51, 22, 55, 20], [51, 24, 55, 22, "setIsDictating"], [51, 38, 55, 36], [51, 39, 55, 37], [51, 42, 55, 40], [51, 46, 55, 40, "useState"], [51, 61, 55, 48], [51, 63, 55, 49], [51, 68, 55, 54], [51, 69, 55, 55], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "longPressedMessage"], [52, 29, 56, 27], [52, 31, 56, 29, "setLongPressedMessage"], [52, 52, 56, 50], [52, 53, 56, 51], [52, 56, 56, 54], [52, 60, 56, 54, "useState"], [52, 75, 56, 62], [52, 77, 56, 63], [52, 81, 56, 67], [52, 82, 56, 68], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isContextMenuVisible"], [53, 31, 57, 29], [53, 33, 57, 31, "setIsContextMenuVisible"], [53, 56, 57, 54], [53, 57, 57, 55], [53, 60, 57, 58], [53, 64, 57, 58, "useState"], [53, 79, 57, 66], [53, 81, 57, 67], [53, 86, 57, 72], [53, 87, 57, 73], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "transcript"], [54, 21, 58, 19], [54, 23, 58, 21, "setTranscript"], [54, 36, 58, 34], [54, 37, 58, 35], [54, 40, 58, 38], [54, 44, 58, 38, "useState"], [54, 59, 58, 46], [54, 61, 58, 47], [54, 63, 58, 49], [54, 64, 58, 50], [55, 4, 59, 2], [55, 10, 59, 8], [55, 11, 59, 9, "isMuted"], [55, 18, 59, 16], [55, 20, 59, 18, "setIsMuted"], [55, 30, 59, 28], [55, 31, 59, 29], [55, 34, 59, 32], [55, 38, 59, 32, "useState"], [55, 53, 59, 40], [55, 55, 59, 41], [55, 60, 59, 46], [55, 61, 59, 47], [56, 4, 61, 2], [56, 10, 61, 8, "scrollViewRef"], [56, 23, 61, 21], [56, 26, 61, 24], [56, 30, 61, 24, "useRef"], [56, 43, 61, 30], [56, 45, 61, 31], [56, 49, 61, 35], [56, 50, 61, 36], [58, 4, 63, 2], [59, 4, 64, 2], [59, 8, 64, 2, "useEffect"], [59, 24, 64, 11], [59, 26, 64, 12], [59, 32, 64, 18], [60, 6, 65, 4], [60, 7, 65, 5], [60, 19, 65, 17], [61, 8, 66, 6], [61, 14, 66, 12], [62, 10, 66, 14, "granted"], [63, 8, 66, 22], [63, 9, 66, 23], [63, 12, 66, 26], [63, 18, 66, 32], [63, 22, 66, 32, "requestRecordingPermissionsAsync"], [63, 65, 66, 64], [63, 67, 66, 65], [63, 68, 66, 66], [64, 8, 67, 6, "setHasPermission"], [64, 24, 67, 22], [64, 25, 67, 23, "granted"], [64, 32, 67, 30], [64, 33, 67, 31], [65, 8, 68, 6], [65, 12, 68, 10], [65, 13, 68, 11, "granted"], [65, 20, 68, 18], [65, 24, 68, 22, "voiceMode"], [65, 33, 68, 31], [65, 35, 68, 33], [66, 10, 69, 8, "<PERSON><PERSON>"], [66, 24, 69, 13], [66, 25, 69, 14, "alert"], [66, 30, 69, 19], [66, 31, 70, 10], [66, 52, 70, 31], [66, 54, 71, 10], [66, 133, 71, 89], [66, 135, 72, 10], [66, 136, 73, 12], [67, 12, 73, 14, "text"], [67, 16, 73, 18], [67, 18, 73, 20], [67, 33, 73, 35], [68, 12, 73, 37, "onPress"], [68, 19, 73, 44], [68, 21, 73, 46, "onPress"], [68, 22, 73, 46], [68, 27, 73, 52, "setVoiceMode"], [68, 39, 73, 64], [68, 40, 73, 65], [68, 45, 73, 70], [69, 10, 73, 72], [69, 11, 73, 73], [69, 13, 74, 12], [70, 12, 74, 14, "text"], [70, 16, 74, 18], [70, 18, 74, 20], [71, 10, 74, 25], [71, 11, 74, 26], [71, 12, 76, 8], [71, 13, 76, 9], [72, 8, 77, 6], [73, 6, 78, 4], [73, 7, 78, 5], [73, 9, 78, 7], [73, 10, 78, 8], [74, 4, 79, 2], [74, 5, 79, 3], [74, 7, 79, 5], [74, 8, 79, 6, "voiceMode"], [74, 17, 79, 15], [74, 18, 79, 16], [74, 19, 79, 17], [75, 4, 81, 2], [75, 10, 81, 8, "handleStartBrainstorming"], [75, 34, 81, 32], [75, 37, 81, 35, "handleStartBrainstorming"], [75, 38, 81, 35], [75, 43, 81, 41], [76, 6, 82, 4, "Haptics"], [76, 13, 82, 11], [76, 14, 82, 12, "impactAsync"], [76, 25, 82, 23], [76, 26, 82, 24, "Haptics"], [76, 33, 82, 31], [76, 34, 82, 32, "ImpactFeedbackStyle"], [76, 53, 82, 51], [76, 54, 82, 52, "Medium"], [76, 60, 82, 58], [76, 61, 82, 59], [77, 6, 83, 4, "setIsFirstTime"], [77, 20, 83, 18], [77, 21, 83, 19], [77, 26, 83, 24], [77, 27, 83, 25], [78, 6, 84, 4, "setMessages"], [78, 17, 84, 15], [78, 18, 84, 16, "fakeMessages"], [78, 40, 84, 28], [78, 41, 84, 29], [79, 6, 85, 4, "setQuickActions"], [79, 21, 85, 19], [79, 22, 85, 20, "fakeQuickActions"], [79, 48, 85, 36], [79, 49, 85, 37], [80, 4, 86, 2], [80, 5, 86, 3], [81, 4, 88, 2], [81, 10, 88, 8, "handleSendMessage"], [81, 27, 88, 25], [81, 30, 88, 28, "handleSendMessage"], [81, 31, 88, 29, "message"], [81, 38, 88, 36], [81, 41, 88, 39, "inputText"], [81, 50, 88, 48], [81, 55, 88, 53], [82, 6, 89, 4], [82, 10, 89, 8], [82, 11, 89, 9, "message"], [82, 18, 89, 16], [82, 19, 89, 17, "trim"], [82, 23, 89, 21], [82, 24, 89, 22], [82, 25, 89, 23], [82, 27, 89, 25], [83, 6, 91, 4], [83, 12, 91, 10, "newUserMessage"], [83, 26, 91, 24], [83, 29, 91, 27], [84, 8, 91, 29, "role"], [84, 12, 91, 33], [84, 14, 91, 35], [84, 20, 91, 41], [85, 8, 91, 43, "content"], [85, 15, 91, 50], [85, 17, 91, 52, "message"], [86, 6, 91, 60], [86, 7, 91, 61], [87, 6, 92, 4, "setMessages"], [87, 17, 92, 15], [87, 18, 92, 17, "prev"], [87, 22, 92, 21], [87, 26, 92, 26], [87, 27, 92, 27], [87, 30, 92, 30, "prev"], [87, 34, 92, 34], [87, 36, 92, 36, "newUserMessage"], [87, 50, 92, 50], [87, 51, 92, 51], [87, 52, 92, 52], [88, 6, 93, 4, "setInputText"], [88, 18, 93, 16], [88, 19, 93, 17], [88, 21, 93, 19], [88, 22, 93, 20], [89, 6, 94, 4, "setQuickActions"], [89, 21, 94, 19], [89, 22, 94, 20], [89, 24, 94, 22], [89, 25, 94, 23], [90, 6, 95, 4, "setIsLoading"], [90, 18, 95, 16], [90, 19, 95, 17], [90, 23, 95, 21], [90, 24, 95, 22], [92, 6, 97, 4], [93, 6, 98, 4, "setTimeout"], [93, 16, 98, 14], [93, 17, 98, 15], [93, 23, 98, 21], [94, 8, 99, 6], [94, 14, 99, 12, "aiResponse"], [94, 24, 99, 22], [94, 27, 99, 25], [95, 10, 100, 8, "role"], [95, 14, 100, 12], [95, 16, 100, 14], [95, 27, 100, 25], [96, 10, 101, 8, "content"], [96, 17, 101, 15], [96, 19, 101, 17], [97, 8, 102, 6], [97, 9, 102, 7], [98, 8, 103, 6, "setMessages"], [98, 19, 103, 17], [98, 20, 103, 19, "prev"], [98, 24, 103, 23], [98, 28, 103, 28], [98, 29, 103, 29], [98, 32, 103, 32, "prev"], [98, 36, 103, 36], [98, 38, 103, 38, "aiResponse"], [98, 48, 103, 48], [98, 49, 103, 49], [98, 50, 103, 50], [99, 8, 104, 6, "setQuickActions"], [99, 23, 104, 21], [99, 24, 104, 22, "fakeQuickActions"], [99, 50, 104, 38], [99, 51, 104, 39], [100, 8, 105, 6, "setIsLoading"], [100, 20, 105, 18], [100, 21, 105, 19], [100, 26, 105, 24], [100, 27, 105, 25], [101, 6, 106, 4], [101, 7, 106, 5], [101, 9, 106, 7], [101, 13, 106, 11], [101, 14, 106, 12], [102, 4, 107, 2], [102, 5, 107, 3], [103, 4, 109, 2], [103, 10, 109, 8, "handleQuickAction"], [103, 27, 109, 25], [103, 30, 109, 29, "action"], [103, 36, 109, 35], [103, 40, 109, 40], [104, 6, 110, 4, "Haptics"], [104, 13, 110, 11], [104, 14, 110, 12, "impactAsync"], [104, 25, 110, 23], [104, 26, 110, 24, "Haptics"], [104, 33, 110, 31], [104, 34, 110, 32, "ImpactFeedbackStyle"], [104, 53, 110, 51], [104, 54, 110, 52, "Light"], [104, 59, 110, 57], [104, 60, 110, 58], [105, 6, 111, 4, "handleSendMessage"], [105, 23, 111, 21], [105, 24, 111, 22, "action"], [105, 30, 111, 28], [105, 31, 111, 29], [106, 4, 112, 2], [106, 5, 112, 3], [107, 4, 114, 2], [107, 10, 114, 8, "toggleVoiceMode"], [107, 25, 114, 23], [107, 28, 114, 26], [107, 34, 114, 26, "toggleVoiceMode"], [107, 35, 114, 26], [107, 40, 114, 38], [108, 6, 115, 4], [108, 10, 115, 8], [108, 11, 115, 9, "voiceMode"], [108, 20, 115, 18], [108, 24, 115, 22], [108, 25, 115, 23, "hasPermission"], [108, 38, 115, 36], [108, 40, 115, 38], [109, 8, 116, 6, "<PERSON><PERSON>"], [109, 22, 116, 11], [109, 23, 116, 12, "alert"], [109, 28, 116, 17], [109, 29, 117, 8], [109, 50, 117, 29], [109, 52, 118, 8], [109, 102, 118, 58], [109, 104, 119, 8], [109, 105, 120, 10], [110, 10, 120, 12, "text"], [110, 14, 120, 16], [110, 16, 120, 18], [111, 8, 120, 27], [111, 9, 120, 28], [111, 11, 121, 10], [112, 10, 122, 12, "text"], [112, 14, 122, 16], [112, 16, 122, 18], [112, 34, 122, 36], [113, 10, 123, 12, "onPress"], [113, 17, 123, 19], [113, 19, 123, 21], [113, 25, 123, 21, "onPress"], [113, 26, 123, 21], [113, 31, 123, 33], [114, 12, 124, 14], [114, 18, 124, 20], [115, 14, 124, 22, "granted"], [116, 12, 124, 30], [116, 13, 124, 31], [116, 16, 124, 34], [116, 22, 124, 40], [116, 26, 124, 40, "requestRecordingPermissionsAsync"], [116, 69, 124, 72], [116, 71, 124, 73], [116, 72, 124, 74], [117, 12, 125, 14], [117, 16, 125, 18, "granted"], [117, 23, 125, 25], [117, 25, 125, 27], [118, 14, 126, 16, "setHasPermission"], [118, 30, 126, 32], [118, 31, 126, 33], [118, 35, 126, 37], [118, 36, 126, 38], [119, 14, 127, 16, "setVoiceMode"], [119, 26, 127, 28], [119, 27, 127, 29], [119, 31, 127, 33], [119, 32, 127, 34], [120, 14, 128, 16], [121, 14, 129, 16], [121, 20, 129, 22, "startVoiceSession"], [121, 37, 129, 39], [121, 38, 129, 40], [121, 39, 129, 41], [122, 12, 130, 14], [123, 10, 131, 12], [124, 8, 132, 10], [124, 9, 132, 11], [124, 10, 134, 6], [124, 11, 134, 7], [125, 8, 135, 6], [126, 6, 136, 4], [127, 6, 138, 4, "Haptics"], [127, 13, 138, 11], [127, 14, 138, 12, "impactAsync"], [127, 25, 138, 23], [127, 26, 138, 24, "Haptics"], [127, 33, 138, 31], [127, 34, 138, 32, "ImpactFeedbackStyle"], [127, 53, 138, 51], [127, 54, 138, 52, "Light"], [127, 59, 138, 57], [127, 60, 138, 58], [128, 6, 140, 4], [128, 10, 140, 8], [128, 11, 140, 9, "voiceMode"], [128, 20, 140, 18], [128, 22, 140, 20], [129, 8, 141, 6], [130, 8, 142, 6, "setVoiceMode"], [130, 20, 142, 18], [130, 21, 142, 19], [130, 25, 142, 23], [130, 26, 142, 24], [131, 8, 143, 6], [131, 12, 143, 10, "hasPermission"], [131, 25, 143, 23], [131, 27, 143, 25], [132, 10, 144, 8], [132, 16, 144, 14, "startVoiceSession"], [132, 33, 144, 31], [132, 34, 144, 32], [132, 35, 144, 33], [133, 8, 145, 6], [134, 6, 146, 4], [134, 7, 146, 5], [134, 13, 146, 11], [135, 8, 147, 6], [136, 8, 148, 6, "setVoiceMode"], [136, 20, 148, 18], [136, 21, 148, 19], [136, 26, 148, 24], [136, 27, 148, 25], [137, 8, 149, 6], [137, 12, 149, 10, "recorderState"], [137, 25, 149, 23], [137, 26, 149, 24, "isRecording"], [137, 37, 149, 35], [137, 39, 149, 37], [138, 10, 150, 8], [138, 16, 150, 14, "recorder"], [138, 24, 150, 22], [138, 25, 150, 23, "stop"], [138, 29, 150, 27], [138, 30, 150, 28], [138, 31, 150, 29], [139, 8, 151, 6], [140, 8, 152, 6, "setIsMuted"], [140, 18, 152, 16], [140, 19, 152, 17], [140, 24, 152, 22], [140, 25, 152, 23], [140, 26, 152, 24], [140, 27, 152, 25], [141, 6, 153, 4], [142, 4, 154, 2], [142, 5, 154, 3], [143, 4, 156, 2], [143, 10, 156, 8, "startVoiceSession"], [143, 27, 156, 25], [143, 30, 156, 28], [143, 36, 156, 28, "startVoiceSession"], [143, 37, 156, 28], [143, 42, 156, 40], [144, 6, 157, 4], [144, 10, 157, 8], [144, 11, 157, 9, "hasPermission"], [144, 24, 157, 22], [144, 26, 157, 24], [145, 6, 159, 4], [145, 10, 159, 8], [146, 8, 160, 6], [146, 14, 160, 12, "recorder"], [146, 22, 160, 20], [146, 23, 160, 21, "prepareToRecordAsync"], [146, 43, 160, 41], [146, 44, 160, 42], [146, 45, 160, 43], [147, 8, 161, 6, "recorder"], [147, 16, 161, 14], [147, 17, 161, 15, "record"], [147, 23, 161, 21], [147, 24, 161, 22], [147, 25, 161, 23], [148, 8, 162, 6, "setIsMuted"], [148, 18, 162, 16], [148, 19, 162, 17], [148, 24, 162, 22], [148, 25, 162, 23], [148, 26, 162, 24], [148, 27, 162, 25], [149, 8, 163, 6, "Haptics"], [149, 15, 163, 13], [149, 16, 163, 14, "impactAsync"], [149, 27, 163, 25], [149, 28, 163, 26, "Haptics"], [149, 35, 163, 33], [149, 36, 163, 34, "ImpactFeedbackStyle"], [149, 55, 163, 53], [149, 56, 163, 54, "Medium"], [149, 62, 163, 60], [149, 63, 163, 61], [150, 6, 164, 4], [150, 7, 164, 5], [150, 8, 164, 6], [150, 15, 164, 13, "error"], [150, 20, 164, 18], [150, 22, 164, 20], [151, 8, 165, 6, "console"], [151, 15, 165, 13], [151, 16, 165, 14, "error"], [151, 21, 165, 19], [151, 22, 165, 20], [151, 53, 165, 51], [151, 55, 165, 53, "error"], [151, 60, 165, 58], [151, 61, 165, 59], [152, 8, 166, 6, "<PERSON><PERSON>"], [152, 22, 166, 11], [152, 23, 166, 12, "alert"], [152, 28, 166, 17], [152, 29, 166, 18], [152, 36, 166, 25], [152, 38, 166, 27], [152, 69, 166, 58], [152, 70, 166, 59], [153, 6, 167, 4], [154, 4, 168, 2], [154, 5, 168, 3], [155, 4, 170, 2], [155, 10, 170, 8, "stopVoiceSession"], [155, 26, 170, 24], [155, 29, 170, 27], [155, 35, 170, 27, "stopVoiceSession"], [155, 36, 170, 27], [155, 41, 170, 39], [156, 6, 171, 4], [156, 10, 171, 8], [157, 8, 172, 6], [157, 12, 172, 10, "recorderState"], [157, 25, 172, 23], [157, 26, 172, 24, "isRecording"], [157, 37, 172, 35], [157, 39, 172, 37], [158, 10, 173, 8], [158, 16, 173, 14, "recorder"], [158, 24, 173, 22], [158, 25, 173, 23, "stop"], [158, 29, 173, 27], [158, 30, 173, 28], [158, 31, 173, 29], [159, 10, 174, 8, "Haptics"], [159, 17, 174, 15], [159, 18, 174, 16, "impactAsync"], [159, 29, 174, 27], [159, 30, 174, 28, "Haptics"], [159, 37, 174, 35], [159, 38, 174, 36, "ImpactFeedbackStyle"], [159, 57, 174, 55], [159, 58, 174, 56, "Medium"], [159, 64, 174, 62], [159, 65, 174, 63], [160, 8, 175, 6], [161, 6, 176, 4], [161, 7, 176, 5], [161, 8, 176, 6], [161, 15, 176, 13, "error"], [161, 20, 176, 18], [161, 22, 176, 20], [162, 8, 177, 6, "console"], [162, 15, 177, 13], [162, 16, 177, 14, "error"], [162, 21, 177, 19], [162, 22, 177, 20], [162, 53, 177, 51], [162, 55, 177, 53, "error"], [162, 60, 177, 58], [162, 61, 177, 59], [163, 6, 178, 4], [164, 4, 179, 2], [164, 5, 179, 3], [165, 4, 181, 2], [165, 10, 181, 8, "handleDictation"], [165, 25, 181, 23], [165, 28, 181, 26], [165, 34, 181, 26, "handleDictation"], [165, 35, 181, 26], [165, 40, 181, 38], [166, 6, 182, 4], [166, 10, 182, 8], [166, 11, 182, 9, "hasPermission"], [166, 24, 182, 22], [166, 26, 182, 24], [167, 8, 183, 6, "<PERSON><PERSON>"], [167, 22, 183, 11], [167, 23, 183, 12, "alert"], [167, 28, 183, 17], [167, 29, 184, 8], [167, 50, 184, 29], [167, 52, 185, 8], [167, 102, 186, 6], [167, 103, 186, 7], [168, 8, 187, 6], [169, 6, 188, 4], [170, 6, 190, 4], [170, 10, 190, 8], [171, 8, 191, 6], [171, 12, 191, 10, "isDictating"], [171, 23, 191, 21], [171, 25, 191, 23], [172, 10, 192, 8], [172, 16, 192, 14, "recorder"], [172, 24, 192, 22], [172, 25, 192, 23, "stop"], [172, 29, 192, 27], [172, 30, 192, 28], [172, 31, 192, 29], [173, 10, 193, 8], [173, 16, 193, 14, "mockTranscript"], [173, 30, 193, 28], [173, 33, 193, 31], [173, 62, 193, 60], [173, 63, 193, 61], [173, 64, 193, 62], [174, 10, 194, 8, "setInputText"], [174, 22, 194, 20], [174, 23, 194, 21, "inputText"], [174, 32, 194, 30], [174, 35, 194, 33, "mockTranscript"], [174, 49, 194, 47], [174, 50, 194, 48], [175, 10, 195, 8, "setIsDictating"], [175, 24, 195, 22], [175, 25, 195, 23], [175, 30, 195, 28], [175, 31, 195, 29], [176, 8, 196, 6], [176, 9, 196, 7], [176, 15, 196, 13], [177, 10, 197, 8], [177, 16, 197, 14, "recorder"], [177, 24, 197, 22], [177, 25, 197, 23, "prepareToRecordAsync"], [177, 45, 197, 43], [177, 46, 197, 44], [177, 47, 197, 45], [178, 10, 198, 8, "recorder"], [178, 18, 198, 16], [178, 19, 198, 17, "record"], [178, 25, 198, 23], [178, 26, 198, 24], [178, 27, 198, 25], [179, 10, 199, 8, "setIsDictating"], [179, 24, 199, 22], [179, 25, 199, 23], [179, 29, 199, 27], [179, 30, 199, 28], [180, 10, 200, 8, "Haptics"], [180, 17, 200, 15], [180, 18, 200, 16, "impactAsync"], [180, 29, 200, 27], [180, 30, 200, 28, "Haptics"], [180, 37, 200, 35], [180, 38, 200, 36, "ImpactFeedbackStyle"], [180, 57, 200, 55], [180, 58, 200, 56, "Medium"], [180, 64, 200, 62], [180, 65, 200, 63], [181, 8, 201, 6], [182, 6, 202, 4], [182, 7, 202, 5], [182, 8, 202, 6], [182, 15, 202, 13, "error"], [182, 20, 202, 18], [182, 22, 202, 20], [183, 8, 203, 6, "console"], [183, 15, 203, 13], [183, 16, 203, 14, "error"], [183, 21, 203, 19], [183, 22, 203, 20], [183, 45, 203, 43], [183, 47, 203, 45, "error"], [183, 52, 203, 50], [183, 53, 203, 51], [184, 8, 204, 6, "<PERSON><PERSON>"], [184, 22, 204, 11], [184, 23, 204, 12, "alert"], [184, 28, 204, 17], [184, 29, 204, 18], [184, 36, 204, 25], [184, 38, 204, 27], [184, 65, 204, 54], [184, 66, 204, 55], [185, 8, 205, 6, "setIsDictating"], [185, 22, 205, 20], [185, 23, 205, 21], [185, 28, 205, 26], [185, 29, 205, 27], [186, 6, 206, 4], [187, 4, 207, 2], [187, 5, 207, 3], [188, 4, 209, 2], [188, 10, 209, 8, "handleLongPress"], [188, 25, 209, 23], [188, 28, 209, 27, "message"], [188, 35, 209, 34], [188, 39, 209, 39], [189, 6, 210, 4, "setLongPressedMessage"], [189, 27, 210, 25], [189, 28, 210, 26, "message"], [189, 35, 210, 33], [189, 36, 210, 34], [190, 6, 211, 4, "setIsContextMenuVisible"], [190, 29, 211, 27], [190, 30, 211, 28], [190, 34, 211, 32], [190, 35, 211, 33], [191, 4, 212, 2], [191, 5, 212, 3], [192, 4, 214, 2], [192, 10, 214, 8, "handleCopyMessage"], [192, 27, 214, 25], [192, 30, 214, 28, "handleCopyMessage"], [192, 31, 214, 28], [192, 36, 214, 34], [193, 6, 215, 4], [193, 10, 215, 8, "longPressedMessage"], [193, 28, 215, 26], [193, 30, 215, 28], [194, 8, 216, 6, "Clipboard"], [194, 26, 216, 15], [194, 27, 216, 16, "setString"], [194, 36, 216, 25], [194, 37, 216, 26, "longPressedMessage"], [194, 55, 216, 44], [194, 56, 216, 45, "content"], [194, 63, 216, 52], [194, 64, 216, 53], [195, 8, 217, 6, "setIsContextMenuVisible"], [195, 31, 217, 29], [195, 32, 217, 30], [195, 37, 217, 35], [195, 38, 217, 36], [196, 8, 218, 6, "setLongPressedMessage"], [196, 29, 218, 27], [196, 30, 218, 28], [196, 34, 218, 32], [196, 35, 218, 33], [197, 6, 219, 4], [198, 4, 220, 2], [198, 5, 220, 3], [199, 4, 222, 2], [199, 10, 222, 8, "handleListenToMessage"], [199, 31, 222, 29], [199, 34, 222, 32, "handleListenToMessage"], [199, 35, 222, 32], [199, 40, 222, 38], [200, 6, 223, 4], [200, 10, 223, 8, "longPressedMessage"], [200, 28, 223, 26], [200, 30, 223, 28], [201, 8, 224, 6], [202, 8, 225, 6, "<PERSON><PERSON>"], [202, 22, 225, 11], [202, 23, 225, 12, "alert"], [202, 28, 225, 17], [202, 29, 225, 18], [202, 51, 225, 40], [202, 53, 225, 42, "longPressedMessage"], [202, 71, 225, 60], [202, 72, 225, 61, "content"], [202, 79, 225, 68], [202, 80, 225, 69], [203, 8, 226, 6, "setIsContextMenuVisible"], [203, 31, 226, 29], [203, 32, 226, 30], [203, 37, 226, 35], [203, 38, 226, 36], [204, 8, 227, 6, "setLongPressedMessage"], [204, 29, 227, 27], [204, 30, 227, 28], [204, 34, 227, 32], [204, 35, 227, 33], [205, 6, 228, 4], [206, 4, 229, 2], [206, 5, 229, 3], [207, 4, 231, 2], [207, 10, 231, 8, "handleMute"], [207, 20, 231, 18], [207, 23, 231, 21], [207, 29, 231, 21, "handleMute"], [207, 30, 231, 21], [207, 35, 231, 33], [208, 6, 232, 4], [208, 10, 232, 8], [208, 11, 232, 9, "hasPermission"], [208, 24, 232, 22], [208, 26, 232, 24], [209, 6, 234, 4], [209, 10, 234, 8], [210, 8, 235, 6], [210, 12, 235, 10, "isMuted"], [210, 19, 235, 17], [210, 21, 235, 19], [211, 10, 236, 8], [212, 10, 237, 8], [212, 14, 237, 12], [212, 15, 237, 13, "recorderState"], [212, 28, 237, 26], [212, 29, 237, 27, "isRecording"], [212, 40, 237, 38], [212, 42, 237, 40], [213, 12, 238, 10], [213, 18, 238, 16, "recorder"], [213, 26, 238, 24], [213, 27, 238, 25, "prepareToRecordAsync"], [213, 47, 238, 45], [213, 48, 238, 46], [213, 49, 238, 47], [214, 12, 239, 10, "recorder"], [214, 20, 239, 18], [214, 21, 239, 19, "record"], [214, 27, 239, 25], [214, 28, 239, 26], [214, 29, 239, 27], [215, 10, 240, 8], [216, 10, 241, 8, "setIsMuted"], [216, 20, 241, 18], [216, 21, 241, 19], [216, 26, 241, 24], [216, 27, 241, 25], [217, 10, 242, 8, "Haptics"], [217, 17, 242, 15], [217, 18, 242, 16, "impactAsync"], [217, 29, 242, 27], [217, 30, 242, 28, "Haptics"], [217, 37, 242, 35], [217, 38, 242, 36, "ImpactFeedbackStyle"], [217, 57, 242, 55], [217, 58, 242, 56, "Light"], [217, 63, 242, 61], [217, 64, 242, 62], [218, 8, 243, 6], [218, 9, 243, 7], [218, 15, 243, 13], [219, 10, 244, 8], [220, 10, 245, 8], [220, 14, 245, 12, "recorderState"], [220, 27, 245, 25], [220, 28, 245, 26, "isRecording"], [220, 39, 245, 37], [220, 41, 245, 39], [221, 12, 246, 10], [221, 18, 246, 16, "recorder"], [221, 26, 246, 24], [221, 27, 246, 25, "stop"], [221, 31, 246, 29], [221, 32, 246, 30], [221, 33, 246, 31], [222, 10, 247, 8], [223, 10, 248, 8, "setIsMuted"], [223, 20, 248, 18], [223, 21, 248, 19], [223, 25, 248, 23], [223, 26, 248, 24], [224, 10, 249, 8, "Haptics"], [224, 17, 249, 15], [224, 18, 249, 16, "impactAsync"], [224, 29, 249, 27], [224, 30, 249, 28, "Haptics"], [224, 37, 249, 35], [224, 38, 249, 36, "ImpactFeedbackStyle"], [224, 57, 249, 55], [224, 58, 249, 56, "Light"], [224, 63, 249, 61], [224, 64, 249, 62], [225, 8, 250, 6], [226, 6, 251, 4], [226, 7, 251, 5], [226, 8, 251, 6], [226, 15, 251, 13, "error"], [226, 20, 251, 18], [226, 22, 251, 20], [227, 8, 252, 6, "console"], [227, 15, 252, 13], [227, 16, 252, 14, "error"], [227, 21, 252, 19], [227, 22, 252, 20], [227, 47, 252, 45], [227, 49, 252, 47, "error"], [227, 54, 252, 52], [227, 55, 252, 53], [228, 6, 253, 4], [229, 4, 254, 2], [229, 5, 254, 3], [231, 4, 257, 2], [232, 4, 258, 2], [232, 8, 258, 2, "useEffect"], [232, 24, 258, 11], [232, 26, 258, 12], [232, 32, 258, 18], [233, 6, 259, 4], [233, 10, 259, 8, "scrollViewRef"], [233, 23, 259, 21], [233, 24, 259, 22, "current"], [233, 31, 259, 29], [233, 33, 259, 31], [234, 8, 260, 6, "scrollViewRef"], [234, 21, 260, 19], [234, 22, 260, 20, "current"], [234, 29, 260, 27], [234, 30, 260, 28, "scrollToEnd"], [234, 41, 260, 39], [234, 42, 260, 40], [235, 10, 260, 42, "animated"], [235, 18, 260, 50], [235, 20, 260, 52], [236, 8, 260, 57], [236, 9, 260, 58], [236, 10, 260, 59], [237, 6, 261, 4], [238, 4, 262, 2], [238, 5, 262, 3], [238, 7, 262, 5], [238, 8, 262, 6, "messages"], [238, 16, 262, 14], [238, 17, 262, 15], [238, 18, 262, 16], [239, 4, 264, 2], [239, 8, 264, 6], [239, 9, 264, 7, "fontsLoaded"], [239, 20, 264, 18], [239, 22, 264, 20], [240, 6, 265, 4], [240, 13, 265, 11], [240, 17, 265, 15], [241, 4, 266, 2], [243, 4, 268, 2], [244, 4, 269, 2], [244, 8, 269, 6, "isFirstTime"], [244, 19, 269, 17], [244, 21, 269, 19], [245, 6, 270, 4], [245, 26, 271, 6], [245, 30, 271, 6, "_jsxDevRuntime"], [245, 44, 271, 6], [245, 45, 271, 6, "jsxDEV"], [245, 51, 271, 6], [245, 53, 271, 7, "_View"], [245, 58, 271, 7], [245, 59, 271, 7, "default"], [245, 66, 271, 11], [246, 8, 271, 12, "style"], [246, 13, 271, 17], [246, 15, 271, 19], [247, 10, 271, 21, "flex"], [247, 14, 271, 25], [247, 16, 271, 27], [247, 17, 271, 28], [248, 10, 271, 30, "backgroundColor"], [248, 25, 271, 45], [248, 27, 271, 47, "colors"], [248, 33, 271, 53], [248, 34, 271, 54, "background"], [249, 8, 271, 65], [249, 9, 271, 67], [250, 8, 271, 67, "children"], [250, 16, 271, 67], [250, 31, 272, 8], [250, 35, 272, 8, "_jsxDevRuntime"], [250, 49, 272, 8], [250, 50, 272, 8, "jsxDEV"], [250, 56, 272, 8], [250, 58, 272, 9, "_View"], [250, 63, 272, 9], [250, 64, 272, 9, "default"], [250, 71, 272, 13], [251, 10, 273, 10, "style"], [251, 15, 273, 15], [251, 17, 273, 17], [252, 12, 274, 12, "flex"], [252, 16, 274, 16], [252, 18, 274, 18], [252, 19, 274, 19], [253, 12, 275, 12, "paddingTop"], [253, 22, 275, 22], [253, 24, 275, 24, "insets"], [253, 30, 275, 30], [253, 31, 275, 31, "top"], [253, 34, 275, 34], [253, 37, 275, 37], [253, 39, 275, 39], [254, 12, 276, 12, "paddingHorizontal"], [254, 29, 276, 29], [254, 31, 276, 31], [254, 33, 276, 33], [255, 12, 277, 12, "paddingBottom"], [255, 25, 277, 25], [255, 27, 277, 27, "insets"], [255, 33, 277, 33], [255, 34, 277, 34, "bottom"], [255, 40, 277, 40], [255, 43, 277, 43], [255, 45, 277, 45], [256, 12, 278, 12, "alignItems"], [256, 22, 278, 22], [256, 24, 278, 24], [256, 32, 278, 32], [257, 12, 279, 12, "justifyContent"], [257, 26, 279, 26], [257, 28, 279, 28], [258, 10, 280, 10], [258, 11, 280, 12], [259, 10, 280, 12, "children"], [259, 18, 280, 12], [259, 34, 282, 10], [259, 38, 282, 10, "_jsxDevRuntime"], [259, 52, 282, 10], [259, 53, 282, 10, "jsxDEV"], [259, 59, 282, 10], [259, 61, 282, 11, "_View"], [259, 66, 282, 11], [259, 67, 282, 11, "default"], [259, 74, 282, 15], [260, 12, 283, 12, "style"], [260, 17, 283, 17], [260, 19, 283, 19], [261, 14, 284, 14, "width"], [261, 19, 284, 19], [261, 21, 284, 21], [261, 24, 284, 24], [262, 14, 285, 14, "height"], [262, 20, 285, 20], [262, 22, 285, 22], [262, 25, 285, 25], [263, 14, 286, 14, "borderRadius"], [263, 26, 286, 26], [263, 28, 286, 28], [263, 30, 286, 30], [264, 14, 287, 14, "backgroundColor"], [264, 29, 287, 29], [264, 31, 287, 31, "colors"], [264, 37, 287, 37], [264, 38, 287, 38, "primaryUltraLight"], [264, 55, 287, 55], [265, 14, 288, 14, "alignItems"], [265, 24, 288, 24], [265, 26, 288, 26], [265, 34, 288, 34], [266, 14, 289, 14, "justifyContent"], [266, 28, 289, 28], [266, 30, 289, 30], [266, 38, 289, 38], [267, 14, 290, 14, "marginBottom"], [267, 26, 290, 26], [267, 28, 290, 28], [268, 12, 291, 12], [268, 13, 291, 14], [269, 12, 291, 14, "children"], [269, 20, 291, 14], [269, 35, 292, 12], [269, 39, 292, 12, "_jsxDevRuntime"], [269, 53, 292, 12], [269, 54, 292, 12, "jsxDEV"], [269, 60, 292, 12], [269, 62, 292, 13, "_lucideReactNative"], [269, 80, 292, 13], [269, 81, 292, 13, "MessageSquare"], [269, 94, 292, 26], [270, 14, 292, 27, "size"], [270, 18, 292, 31], [270, 20, 292, 33], [270, 22, 292, 36], [271, 14, 292, 37, "color"], [271, 19, 292, 42], [271, 21, 292, 44, "colors"], [271, 27, 292, 50], [271, 28, 292, 51, "primary"], [272, 12, 292, 59], [273, 14, 292, 59, "fileName"], [273, 22, 292, 59], [273, 24, 292, 59, "_jsxFileName"], [273, 36, 292, 59], [274, 14, 292, 59, "lineNumber"], [274, 24, 292, 59], [275, 14, 292, 59, "columnNumber"], [275, 26, 292, 59], [276, 12, 292, 59], [276, 19, 292, 61], [277, 10, 292, 62], [278, 12, 292, 62, "fileName"], [278, 20, 292, 62], [278, 22, 292, 62, "_jsxFileName"], [278, 34, 292, 62], [279, 12, 292, 62, "lineNumber"], [279, 22, 292, 62], [280, 12, 292, 62, "columnNumber"], [280, 24, 292, 62], [281, 10, 292, 62], [281, 17, 293, 16], [281, 18, 293, 17], [281, 33, 296, 10], [281, 37, 296, 10, "_jsxDevRuntime"], [281, 51, 296, 10], [281, 52, 296, 10, "jsxDEV"], [281, 58, 296, 10], [281, 60, 296, 11, "_Text"], [281, 65, 296, 11], [281, 66, 296, 11, "default"], [281, 73, 296, 15], [282, 12, 297, 12, "style"], [282, 17, 297, 17], [282, 19, 297, 19], [283, 14, 298, 14, "fontSize"], [283, 22, 298, 22], [283, 24, 298, 24], [283, 26, 298, 26], [284, 14, 299, 14, "fontFamily"], [284, 24, 299, 24], [284, 26, 299, 26], [284, 47, 299, 47], [285, 14, 300, 14, "color"], [285, 19, 300, 19], [285, 21, 300, 21, "colors"], [285, 27, 300, 27], [285, 28, 300, 28, "text"], [285, 32, 300, 32], [286, 14, 301, 14, "textAlign"], [286, 23, 301, 23], [286, 25, 301, 25], [286, 33, 301, 33], [287, 14, 302, 14, "marginBottom"], [287, 26, 302, 26], [287, 28, 302, 28], [288, 12, 303, 12], [288, 13, 303, 14], [289, 12, 303, 14, "children"], [289, 20, 303, 14], [289, 22, 303, 15], [290, 10, 305, 10], [291, 12, 305, 10, "fileName"], [291, 20, 305, 10], [291, 22, 305, 10, "_jsxFileName"], [291, 34, 305, 10], [292, 12, 305, 10, "lineNumber"], [292, 22, 305, 10], [293, 12, 305, 10, "columnNumber"], [293, 24, 305, 10], [294, 10, 305, 10], [294, 17, 305, 16], [294, 18, 305, 17], [294, 33, 308, 10], [294, 37, 308, 10, "_jsxDevRuntime"], [294, 51, 308, 10], [294, 52, 308, 10, "jsxDEV"], [294, 58, 308, 10], [294, 60, 308, 11, "_Text"], [294, 65, 308, 11], [294, 66, 308, 11, "default"], [294, 73, 308, 15], [295, 12, 309, 12, "style"], [295, 17, 309, 17], [295, 19, 309, 19], [296, 14, 310, 14, "fontSize"], [296, 22, 310, 22], [296, 24, 310, 24], [296, 26, 310, 26], [297, 14, 311, 14, "fontFamily"], [297, 24, 311, 24], [297, 26, 311, 26], [297, 46, 311, 46], [298, 14, 312, 14, "color"], [298, 19, 312, 19], [298, 21, 312, 21, "colors"], [298, 27, 312, 27], [298, 28, 312, 28, "textSecondary"], [298, 41, 312, 41], [299, 14, 313, 14, "textAlign"], [299, 23, 313, 23], [299, 25, 313, 25], [299, 33, 313, 33], [300, 14, 314, 14, "lineHeight"], [300, 24, 314, 24], [300, 26, 314, 26], [300, 28, 314, 28], [301, 14, 315, 14, "marginBottom"], [301, 26, 315, 26], [301, 28, 315, 28], [302, 12, 316, 12], [302, 13, 316, 14], [303, 12, 316, 14, "children"], [303, 20, 316, 14], [303, 22, 316, 15], [304, 10, 319, 10], [305, 12, 319, 10, "fileName"], [305, 20, 319, 10], [305, 22, 319, 10, "_jsxFileName"], [305, 34, 319, 10], [306, 12, 319, 10, "lineNumber"], [306, 22, 319, 10], [307, 12, 319, 10, "columnNumber"], [307, 24, 319, 10], [308, 10, 319, 10], [308, 17, 319, 16], [308, 18, 319, 17], [308, 33, 322, 10], [308, 37, 322, 10, "_jsxDevRuntime"], [308, 51, 322, 10], [308, 52, 322, 10, "jsxDEV"], [308, 58, 322, 10], [308, 60, 322, 11, "_TouchableOpacity"], [308, 77, 322, 11], [308, 78, 322, 11, "default"], [308, 85, 322, 27], [309, 12, 323, 12, "style"], [309, 17, 323, 17], [309, 19, 323, 19], [310, 14, 324, 14, "backgroundColor"], [310, 29, 324, 29], [310, 31, 324, 31, "colors"], [310, 37, 324, 37], [310, 38, 324, 38, "primary"], [310, 45, 324, 45], [311, 14, 325, 14, "borderRadius"], [311, 26, 325, 26], [311, 28, 325, 28], [311, 30, 325, 30], [312, 14, 326, 14, "paddingHorizontal"], [312, 31, 326, 31], [312, 33, 326, 33], [312, 35, 326, 35], [313, 14, 327, 14, "paddingVertical"], [313, 29, 327, 29], [313, 31, 327, 31], [313, 33, 327, 33], [314, 14, 328, 14, "min<PERSON><PERSON><PERSON>"], [314, 22, 328, 22], [314, 24, 328, 24], [314, 27, 328, 27], [315, 14, 329, 14, "alignItems"], [315, 24, 329, 24], [315, 26, 329, 26], [316, 12, 330, 12], [316, 13, 330, 14], [317, 12, 331, 12, "onPress"], [317, 19, 331, 19], [317, 21, 331, 21, "handleStartBrainstorming"], [317, 45, 331, 46], [318, 12, 331, 46, "children"], [318, 20, 331, 46], [318, 35, 332, 12], [318, 39, 332, 12, "_jsxDevRuntime"], [318, 53, 332, 12], [318, 54, 332, 12, "jsxDEV"], [318, 60, 332, 12], [318, 62, 332, 13, "_Text"], [318, 67, 332, 13], [318, 68, 332, 13, "default"], [318, 75, 332, 17], [319, 14, 333, 14, "style"], [319, 19, 333, 19], [319, 21, 333, 21], [320, 16, 334, 16, "fontSize"], [320, 24, 334, 24], [320, 26, 334, 26], [320, 28, 334, 28], [321, 16, 335, 16, "fontFamily"], [321, 26, 335, 26], [321, 28, 335, 28], [321, 49, 335, 49], [322, 16, 336, 16, "color"], [322, 21, 336, 21], [322, 23, 336, 23, "colors"], [322, 29, 336, 29], [322, 30, 336, 30, "background"], [323, 14, 337, 14], [323, 15, 337, 16], [324, 14, 337, 16, "children"], [324, 22, 337, 16], [324, 24, 337, 17], [325, 12, 339, 12], [326, 14, 339, 12, "fileName"], [326, 22, 339, 12], [326, 24, 339, 12, "_jsxFileName"], [326, 36, 339, 12], [327, 14, 339, 12, "lineNumber"], [327, 24, 339, 12], [328, 14, 339, 12, "columnNumber"], [328, 26, 339, 12], [329, 12, 339, 12], [329, 19, 339, 18], [330, 10, 339, 19], [331, 12, 339, 19, "fileName"], [331, 20, 339, 19], [331, 22, 339, 19, "_jsxFileName"], [331, 34, 339, 19], [332, 12, 339, 19, "lineNumber"], [332, 22, 339, 19], [333, 12, 339, 19, "columnNumber"], [333, 24, 339, 19], [334, 10, 339, 19], [334, 17, 340, 28], [334, 18, 340, 29], [335, 8, 340, 29], [336, 10, 340, 29, "fileName"], [336, 18, 340, 29], [336, 20, 340, 29, "_jsxFileName"], [336, 32, 340, 29], [337, 10, 340, 29, "lineNumber"], [337, 20, 340, 29], [338, 10, 340, 29, "columnNumber"], [338, 22, 340, 29], [339, 8, 340, 29], [339, 15, 341, 14], [340, 6, 341, 15], [341, 8, 341, 15, "fileName"], [341, 16, 341, 15], [341, 18, 341, 15, "_jsxFileName"], [341, 30, 341, 15], [342, 8, 341, 15, "lineNumber"], [342, 18, 341, 15], [343, 8, 341, 15, "columnNumber"], [343, 20, 341, 15], [344, 6, 341, 15], [344, 13, 342, 12], [344, 14, 342, 13], [345, 4, 344, 2], [347, 4, 346, 2], [348, 4, 347, 2], [348, 24, 348, 4], [348, 28, 348, 4, "_jsxDevRuntime"], [348, 42, 348, 4], [348, 43, 348, 4, "jsxDEV"], [348, 49, 348, 4], [348, 51, 348, 5, "_View"], [348, 56, 348, 5], [348, 57, 348, 5, "default"], [348, 64, 348, 9], [349, 6, 348, 10, "style"], [349, 11, 348, 15], [349, 13, 348, 17], [350, 8, 348, 19, "flex"], [350, 12, 348, 23], [350, 14, 348, 25], [350, 15, 348, 26], [351, 8, 348, 28, "backgroundColor"], [351, 23, 348, 43], [351, 25, 348, 45, "colors"], [351, 31, 348, 51], [351, 32, 348, 52, "background"], [352, 6, 348, 63], [352, 7, 348, 65], [353, 6, 348, 65, "children"], [353, 14, 348, 65], [353, 30, 349, 6], [353, 34, 349, 6, "_jsxDevRuntime"], [353, 48, 349, 6], [353, 49, 349, 6, "jsxDEV"], [353, 55, 349, 6], [353, 57, 349, 7, "_Header"], [353, 64, 349, 7], [353, 65, 349, 7, "default"], [353, 72, 349, 13], [354, 8, 350, 8, "voiceMode"], [354, 17, 350, 17], [354, 19, 350, 19, "voiceMode"], [354, 28, 350, 29], [355, 8, 351, 8, "onToggleVoiceMode"], [355, 25, 351, 25], [355, 27, 351, 27, "toggleVoiceMode"], [355, 42, 351, 43], [356, 8, 352, 8, "onDone"], [356, 14, 352, 14], [356, 16, 352, 16, "onDone"], [356, 17, 352, 16], [356, 22, 352, 22, "<PERSON><PERSON>"], [356, 36, 352, 27], [356, 37, 352, 28, "alert"], [356, 42, 352, 33], [356, 43, 352, 34], [356, 57, 352, 48], [357, 6, 352, 50], [358, 8, 352, 50, "fileName"], [358, 16, 352, 50], [358, 18, 352, 50, "_jsxFileName"], [358, 30, 352, 50], [359, 8, 352, 50, "lineNumber"], [359, 18, 352, 50], [360, 8, 352, 50, "columnNumber"], [360, 20, 352, 50], [361, 6, 352, 50], [361, 13, 353, 7], [361, 14, 353, 8], [361, 16, 354, 7, "voiceMode"], [361, 25, 354, 16], [361, 41, 355, 8], [361, 45, 355, 8, "_jsxDevRuntime"], [361, 59, 355, 8], [361, 60, 355, 8, "jsxDEV"], [361, 66, 355, 8], [361, 68, 355, 9, "_VoiceMode"], [361, 78, 355, 9], [361, 79, 355, 9, "default"], [361, 86, 355, 18], [362, 8, 356, 10, "isRecording"], [362, 19, 356, 21], [362, 21, 356, 23, "recorderState"], [362, 34, 356, 36], [362, 35, 356, 37, "isRecording"], [362, 46, 356, 49], [363, 8, 357, 10, "hasPermission"], [363, 21, 357, 23], [363, 23, 357, 25, "hasPermission"], [363, 36, 357, 39], [364, 8, 358, 10, "isLoading"], [364, 17, 358, 19], [364, 19, 358, 21, "isLoading"], [364, 28, 358, 31], [365, 8, 359, 10, "transcript"], [365, 18, 359, 20], [365, 20, 359, 22, "transcript"], [365, 30, 359, 33], [366, 8, 360, 10, "isMuted"], [366, 15, 360, 17], [366, 17, 360, 19, "isMuted"], [366, 24, 360, 27], [367, 8, 361, 10, "onMute"], [367, 14, 361, 16], [367, 16, 361, 18, "handleMute"], [368, 6, 361, 29], [369, 8, 361, 29, "fileName"], [369, 16, 361, 29], [369, 18, 361, 29, "_jsxFileName"], [369, 30, 361, 29], [370, 8, 361, 29, "lineNumber"], [370, 18, 361, 29], [371, 8, 361, 29, "columnNumber"], [371, 20, 361, 29], [372, 6, 361, 29], [372, 13, 362, 9], [372, 14, 362, 10], [372, 30, 364, 8], [372, 34, 364, 8, "_jsxDevRuntime"], [372, 48, 364, 8], [372, 49, 364, 8, "jsxDEV"], [372, 55, 364, 8], [372, 57, 364, 9, "_KeyboardAvoidingAnimatedView"], [372, 86, 364, 9], [372, 87, 364, 9, "default"], [372, 94, 364, 37], [373, 8, 364, 38, "style"], [373, 13, 364, 43], [373, 15, 364, 45], [374, 10, 364, 47, "flex"], [374, 14, 364, 51], [374, 16, 364, 53], [375, 8, 364, 55], [375, 9, 364, 57], [376, 8, 364, 57, "children"], [376, 16, 364, 57], [376, 32, 366, 10], [376, 36, 366, 10, "_jsxDevRuntime"], [376, 50, 366, 10], [376, 51, 366, 10, "jsxDEV"], [376, 57, 366, 10], [376, 59, 366, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [376, 70, 366, 11], [376, 71, 366, 11, "default"], [376, 78, 366, 21], [377, 10, 367, 12, "ref"], [377, 13, 367, 15], [377, 15, 367, 17, "scrollViewRef"], [377, 28, 367, 31], [378, 10, 368, 12, "style"], [378, 15, 368, 17], [378, 17, 368, 19], [379, 12, 368, 21, "flex"], [379, 16, 368, 25], [379, 18, 368, 27], [380, 10, 368, 29], [380, 11, 368, 31], [381, 10, 369, 12, "contentContainerStyle"], [381, 31, 369, 33], [381, 33, 369, 35], [382, 12, 370, 14, "paddingHorizontal"], [382, 29, 370, 31], [382, 31, 370, 33], [382, 33, 370, 35], [383, 12, 371, 14, "paddingVertical"], [383, 27, 371, 29], [383, 29, 371, 31], [383, 31, 371, 33], [384, 12, 372, 14, "paddingBottom"], [384, 25, 372, 27], [384, 27, 372, 29], [384, 30, 372, 32], [384, 31, 372, 34], [385, 10, 373, 12], [385, 11, 373, 14], [386, 10, 374, 12, "showsVerticalScrollIndicator"], [386, 38, 374, 40], [386, 40, 374, 42], [386, 45, 374, 48], [387, 10, 374, 48, "children"], [387, 18, 374, 48], [387, 21, 375, 13, "messages"], [387, 29, 375, 21], [387, 30, 375, 22, "map"], [387, 33, 375, 25], [387, 34, 375, 26], [387, 35, 375, 27, "message"], [387, 42, 375, 34], [387, 44, 375, 36, "index"], [387, 49, 375, 41], [387, 67, 376, 14], [387, 71, 376, 14, "_jsxDevRuntime"], [387, 85, 376, 14], [387, 86, 376, 14, "jsxDEV"], [387, 92, 376, 14], [387, 94, 376, 15, "_MessageBubble"], [387, 108, 376, 15], [387, 109, 376, 15, "default"], [387, 116, 376, 28], [388, 12, 376, 41, "message"], [388, 19, 376, 48], [388, 21, 376, 50, "message"], [388, 28, 376, 58], [389, 12, 376, 59, "onLongPress"], [389, 23, 376, 70], [389, 25, 376, 72, "handleLongPress"], [390, 10, 376, 88], [390, 13, 376, 34, "index"], [390, 18, 376, 39], [391, 12, 376, 39, "fileName"], [391, 20, 376, 39], [391, 22, 376, 39, "_jsxFileName"], [391, 34, 376, 39], [392, 12, 376, 39, "lineNumber"], [392, 22, 376, 39], [393, 12, 376, 39, "columnNumber"], [393, 24, 376, 39], [394, 10, 376, 39], [394, 17, 376, 90], [394, 18, 377, 13], [394, 19, 377, 14], [394, 21, 380, 13, "isLoading"], [394, 30, 380, 22], [394, 47, 381, 14], [394, 51, 381, 14, "_jsxDevRuntime"], [394, 65, 381, 14], [394, 66, 381, 14, "jsxDEV"], [394, 72, 381, 14], [394, 74, 381, 15, "_View"], [394, 79, 381, 15], [394, 80, 381, 15, "default"], [394, 87, 381, 19], [395, 12, 382, 16, "style"], [395, 17, 382, 21], [395, 19, 382, 23], [396, 14, 383, 18, "marginBottom"], [396, 26, 383, 30], [396, 28, 383, 32], [396, 30, 383, 34], [397, 14, 384, 18, "alignSelf"], [397, 23, 384, 27], [397, 25, 384, 29], [397, 37, 384, 41], [398, 14, 385, 18, "max<PERSON><PERSON><PERSON>"], [398, 22, 385, 26], [398, 24, 385, 28], [399, 12, 386, 16], [399, 13, 386, 18], [400, 12, 386, 18, "children"], [400, 20, 386, 18], [400, 35, 387, 16], [400, 39, 387, 16, "_jsxDevRuntime"], [400, 53, 387, 16], [400, 54, 387, 16, "jsxDEV"], [400, 60, 387, 16], [400, 62, 387, 17, "_View"], [400, 67, 387, 17], [400, 68, 387, 17, "default"], [400, 75, 387, 21], [401, 14, 388, 18, "style"], [401, 19, 388, 23], [401, 21, 388, 25], [402, 16, 389, 20, "backgroundColor"], [402, 31, 389, 35], [402, 33, 389, 37, "colors"], [402, 39, 389, 43], [402, 40, 389, 44, "cardBackground"], [402, 54, 389, 58], [403, 16, 390, 20, "borderRadius"], [403, 28, 390, 32], [403, 30, 390, 34], [403, 32, 390, 36], [404, 16, 391, 20, "paddingHorizontal"], [404, 33, 391, 37], [404, 35, 391, 39], [404, 37, 391, 41], [405, 16, 392, 20, "paddingVertical"], [405, 31, 392, 35], [405, 33, 392, 37], [405, 35, 392, 39], [406, 16, 393, 20, "borderWidth"], [406, 27, 393, 31], [406, 29, 393, 33], [406, 30, 393, 34], [407, 16, 394, 20, "borderColor"], [407, 27, 394, 31], [407, 29, 394, 33, "colors"], [407, 35, 394, 39], [407, 36, 394, 40, "outline"], [408, 14, 395, 18], [408, 15, 395, 20], [409, 14, 395, 20, "children"], [409, 22, 395, 20], [409, 37, 396, 18], [409, 41, 396, 18, "_jsxDevRuntime"], [409, 55, 396, 18], [409, 56, 396, 18, "jsxDEV"], [409, 62, 396, 18], [409, 64, 396, 19, "_Text"], [409, 69, 396, 19], [409, 70, 396, 19, "default"], [409, 77, 396, 23], [410, 16, 397, 20, "style"], [410, 21, 397, 25], [410, 23, 397, 27], [411, 18, 398, 22, "fontSize"], [411, 26, 398, 30], [411, 28, 398, 32], [411, 30, 398, 34], [412, 18, 399, 22, "fontFamily"], [412, 28, 399, 32], [412, 30, 399, 34], [412, 50, 399, 54], [413, 18, 400, 22, "color"], [413, 23, 400, 27], [413, 25, 400, 29, "colors"], [413, 31, 400, 35], [413, 32, 400, 36, "textSecondary"], [413, 45, 400, 49], [414, 18, 401, 22, "lineHeight"], [414, 28, 401, 32], [414, 30, 401, 34], [415, 16, 402, 20], [415, 17, 402, 22], [416, 16, 402, 22, "children"], [416, 24, 402, 22], [416, 26, 402, 23], [417, 14, 404, 18], [418, 16, 404, 18, "fileName"], [418, 24, 404, 18], [418, 26, 404, 18, "_jsxFileName"], [418, 38, 404, 18], [419, 16, 404, 18, "lineNumber"], [419, 26, 404, 18], [420, 16, 404, 18, "columnNumber"], [420, 28, 404, 18], [421, 14, 404, 18], [421, 21, 404, 24], [422, 12, 404, 25], [423, 14, 404, 25, "fileName"], [423, 22, 404, 25], [423, 24, 404, 25, "_jsxFileName"], [423, 36, 404, 25], [424, 14, 404, 25, "lineNumber"], [424, 24, 404, 25], [425, 14, 404, 25, "columnNumber"], [425, 26, 404, 25], [426, 12, 404, 25], [426, 19, 405, 22], [427, 10, 405, 23], [428, 12, 405, 23, "fileName"], [428, 20, 405, 23], [428, 22, 405, 23, "_jsxFileName"], [428, 34, 405, 23], [429, 12, 405, 23, "lineNumber"], [429, 22, 405, 23], [430, 12, 405, 23, "columnNumber"], [430, 24, 405, 23], [431, 10, 405, 23], [431, 17, 406, 20], [431, 18, 407, 13], [432, 8, 407, 13], [433, 10, 407, 13, "fileName"], [433, 18, 407, 13], [433, 20, 407, 13, "_jsxFileName"], [433, 32, 407, 13], [434, 10, 407, 13, "lineNumber"], [434, 20, 407, 13], [435, 10, 407, 13, "columnNumber"], [435, 22, 407, 13], [436, 8, 407, 13], [436, 15, 408, 22], [436, 16, 408, 23], [436, 18, 411, 11, "quickActions"], [436, 30, 411, 23], [436, 31, 411, 24, "length"], [436, 37, 411, 30], [436, 40, 411, 33], [436, 41, 411, 34], [436, 58, 412, 12], [436, 62, 412, 12, "_jsxDevRuntime"], [436, 76, 412, 12], [436, 77, 412, 12, "jsxDEV"], [436, 83, 412, 12], [436, 85, 412, 13, "_View"], [436, 90, 412, 13], [436, 91, 412, 13, "default"], [436, 98, 412, 17], [437, 10, 413, 14, "style"], [437, 15, 413, 19], [437, 17, 413, 21], [438, 12, 414, 16, "position"], [438, 20, 414, 24], [438, 22, 414, 26], [438, 32, 414, 36], [439, 12, 415, 16, "bottom"], [439, 18, 415, 22], [439, 20, 415, 24, "insets"], [439, 26, 415, 30], [439, 27, 415, 31, "bottom"], [439, 33, 415, 37], [439, 36, 415, 40], [439, 39, 415, 43], [440, 12, 416, 16, "left"], [440, 16, 416, 20], [440, 18, 416, 22], [440, 20, 416, 24], [441, 12, 417, 16, "right"], [441, 17, 417, 21], [441, 19, 417, 23], [442, 10, 418, 14], [442, 11, 418, 16], [443, 10, 418, 16, "children"], [443, 18, 418, 16], [443, 33, 419, 14], [443, 37, 419, 14, "_jsxDevRuntime"], [443, 51, 419, 14], [443, 52, 419, 14, "jsxDEV"], [443, 58, 419, 14], [443, 60, 419, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [443, 71, 419, 15], [443, 72, 419, 15, "default"], [443, 79, 419, 25], [444, 12, 420, 16, "horizontal"], [444, 22, 420, 26], [445, 12, 421, 16, "showsHorizontalScrollIndicator"], [445, 42, 421, 46], [445, 44, 421, 48], [445, 49, 421, 54], [446, 12, 422, 16, "contentContainerStyle"], [446, 33, 422, 37], [446, 35, 422, 39], [447, 14, 422, 41, "gap"], [447, 17, 422, 44], [447, 19, 422, 46], [448, 12, 422, 48], [448, 13, 422, 50], [449, 12, 422, 50, "children"], [449, 20, 422, 50], [449, 22, 423, 17, "quickActions"], [449, 34, 423, 29], [449, 35, 423, 30, "map"], [449, 38, 423, 33], [449, 39, 423, 34], [449, 40, 423, 35, "action"], [449, 46, 423, 41], [449, 48, 423, 43, "index"], [449, 53, 423, 48], [449, 71, 424, 18], [449, 75, 424, 18, "_jsxDevRuntime"], [449, 89, 424, 18], [449, 90, 424, 18, "jsxDEV"], [449, 96, 424, 18], [449, 98, 424, 19, "_TouchableOpacity"], [449, 115, 424, 19], [449, 116, 424, 19, "default"], [449, 123, 424, 35], [450, 14, 426, 20, "style"], [450, 19, 426, 25], [450, 21, 426, 27], [451, 16, 427, 22, "backgroundColor"], [451, 31, 427, 37], [451, 33, 427, 39, "colors"], [451, 39, 427, 45], [451, 40, 427, 46, "primaryUltraLight"], [451, 57, 427, 63], [452, 16, 428, 22, "borderRadius"], [452, 28, 428, 34], [452, 30, 428, 36], [452, 32, 428, 38], [453, 16, 429, 22, "paddingHorizontal"], [453, 33, 429, 39], [453, 35, 429, 41], [453, 37, 429, 43], [454, 16, 430, 22, "paddingVertical"], [454, 31, 430, 37], [454, 33, 430, 39], [454, 34, 430, 40], [455, 16, 431, 22, "borderWidth"], [455, 27, 431, 33], [455, 29, 431, 35], [455, 30, 431, 36], [456, 16, 432, 22, "borderColor"], [456, 27, 432, 33], [456, 29, 432, 35, "colors"], [456, 35, 432, 41], [456, 36, 432, 42, "primary"], [457, 14, 433, 20], [457, 15, 433, 22], [458, 14, 434, 20, "onPress"], [458, 21, 434, 27], [458, 23, 434, 29, "onPress"], [458, 24, 434, 29], [458, 29, 434, 35, "handleQuickAction"], [458, 46, 434, 52], [458, 47, 434, 53, "action"], [458, 53, 434, 59], [458, 54, 434, 61], [459, 14, 434, 61, "children"], [459, 22, 434, 61], [459, 37, 435, 20], [459, 41, 435, 20, "_jsxDevRuntime"], [459, 55, 435, 20], [459, 56, 435, 20, "jsxDEV"], [459, 62, 435, 20], [459, 64, 435, 21, "_Text"], [459, 69, 435, 21], [459, 70, 435, 21, "default"], [459, 77, 435, 25], [460, 16, 436, 22, "style"], [460, 21, 436, 27], [460, 23, 436, 29], [461, 18, 437, 24, "fontSize"], [461, 26, 437, 32], [461, 28, 437, 34], [461, 30, 437, 36], [462, 18, 438, 24, "fontFamily"], [462, 28, 438, 34], [462, 30, 438, 36], [462, 49, 438, 55], [463, 18, 439, 24, "color"], [463, 23, 439, 29], [463, 25, 439, 31, "colors"], [463, 31, 439, 37], [463, 32, 439, 38, "primary"], [464, 16, 440, 22], [464, 17, 440, 24], [465, 16, 440, 24, "children"], [465, 24, 440, 24], [465, 26, 441, 23, "action"], [466, 14, 441, 29], [467, 16, 441, 29, "fileName"], [467, 24, 441, 29], [467, 26, 441, 29, "_jsxFileName"], [467, 38, 441, 29], [468, 16, 441, 29, "lineNumber"], [468, 26, 441, 29], [469, 16, 441, 29, "columnNumber"], [469, 28, 441, 29], [470, 14, 441, 29], [470, 21, 442, 26], [471, 12, 442, 27], [471, 15, 425, 25, "index"], [471, 20, 425, 30], [472, 14, 425, 30, "fileName"], [472, 22, 425, 30], [472, 24, 425, 30, "_jsxFileName"], [472, 36, 425, 30], [473, 14, 425, 30, "lineNumber"], [473, 24, 425, 30], [474, 14, 425, 30, "columnNumber"], [474, 26, 425, 30], [475, 12, 425, 30], [475, 19, 443, 36], [475, 20, 444, 17], [476, 10, 444, 18], [477, 12, 444, 18, "fileName"], [477, 20, 444, 18], [477, 22, 444, 18, "_jsxFileName"], [477, 34, 444, 18], [478, 12, 444, 18, "lineNumber"], [478, 22, 444, 18], [479, 12, 444, 18, "columnNumber"], [479, 24, 444, 18], [480, 10, 444, 18], [480, 17, 445, 26], [481, 8, 445, 27], [482, 10, 445, 27, "fileName"], [482, 18, 445, 27], [482, 20, 445, 27, "_jsxFileName"], [482, 32, 445, 27], [483, 10, 445, 27, "lineNumber"], [483, 20, 445, 27], [484, 10, 445, 27, "columnNumber"], [484, 22, 445, 27], [485, 8, 445, 27], [485, 15, 446, 18], [485, 16, 447, 11], [485, 31, 450, 10], [485, 35, 450, 10, "_jsxDevRuntime"], [485, 49, 450, 10], [485, 50, 450, 10, "jsxDEV"], [485, 56, 450, 10], [485, 58, 450, 11, "_View"], [485, 63, 450, 11], [485, 64, 450, 11, "default"], [485, 71, 450, 15], [486, 10, 451, 12, "style"], [486, 15, 451, 17], [486, 17, 451, 19], [487, 12, 452, 14, "position"], [487, 20, 452, 22], [487, 22, 452, 24], [487, 32, 452, 34], [488, 12, 453, 14, "bottom"], [488, 18, 453, 20], [488, 20, 453, 22], [488, 21, 453, 23], [489, 12, 454, 14, "left"], [489, 16, 454, 18], [489, 18, 454, 20], [489, 19, 454, 21], [490, 12, 455, 14, "right"], [490, 17, 455, 19], [490, 19, 455, 21], [491, 10, 456, 12], [491, 11, 456, 14], [492, 10, 456, 14, "children"], [492, 18, 456, 14], [492, 33, 457, 12], [492, 37, 457, 12, "_jsxDevRuntime"], [492, 51, 457, 12], [492, 52, 457, 12, "jsxDEV"], [492, 58, 457, 12], [492, 60, 457, 13, "_TextMode"], [492, 69, 457, 13], [492, 70, 457, 13, "default"], [492, 77, 457, 21], [493, 12, 458, 14, "inputText"], [493, 21, 458, 23], [493, 23, 458, 25, "inputText"], [493, 32, 458, 35], [494, 12, 459, 14, "onInputChange"], [494, 25, 459, 27], [494, 27, 459, 29, "setInputText"], [494, 39, 459, 42], [495, 12, 460, 14, "onSendMessage"], [495, 25, 460, 27], [495, 27, 460, 29, "onSendMessage"], [495, 28, 460, 29], [495, 33, 460, 35, "handleSendMessage"], [495, 50, 460, 52], [495, 51, 460, 53], [495, 52, 460, 55], [496, 12, 461, 14, "onStartDictation"], [496, 28, 461, 30], [496, 30, 461, 32, "handleDictation"], [497, 10, 461, 48], [498, 12, 461, 48, "fileName"], [498, 20, 461, 48], [498, 22, 461, 48, "_jsxFileName"], [498, 34, 461, 48], [499, 12, 461, 48, "lineNumber"], [499, 22, 461, 48], [500, 12, 461, 48, "columnNumber"], [500, 24, 461, 48], [501, 10, 461, 48], [501, 17, 462, 13], [502, 8, 462, 14], [503, 10, 462, 14, "fileName"], [503, 18, 462, 14], [503, 20, 462, 14, "_jsxFileName"], [503, 32, 462, 14], [504, 10, 462, 14, "lineNumber"], [504, 20, 462, 14], [505, 10, 462, 14, "columnNumber"], [505, 22, 462, 14], [506, 8, 462, 14], [506, 15, 463, 16], [506, 16, 463, 17], [507, 6, 463, 17], [508, 8, 463, 17, "fileName"], [508, 16, 463, 17], [508, 18, 463, 17, "_jsxFileName"], [508, 30, 463, 17], [509, 8, 463, 17, "lineNumber"], [509, 18, 463, 17], [510, 8, 463, 17, "columnNumber"], [510, 20, 463, 17], [511, 6, 463, 17], [511, 13, 464, 38], [511, 14, 465, 7], [511, 29, 468, 6], [511, 33, 468, 6, "_jsxDevRuntime"], [511, 47, 468, 6], [511, 48, 468, 6, "jsxDEV"], [511, 54, 468, 6], [511, 56, 468, 7, "_Modal"], [511, 62, 468, 7], [511, 63, 468, 7, "default"], [511, 70, 468, 12], [512, 8, 469, 8, "transparent"], [512, 19, 469, 19], [513, 8, 470, 8, "visible"], [513, 15, 470, 15], [513, 17, 470, 17, "isContextMenuVisible"], [513, 37, 470, 38], [514, 8, 471, 8, "onRequestClose"], [514, 22, 471, 22], [514, 24, 471, 24, "onRequestClose"], [514, 25, 471, 24], [514, 30, 471, 30, "setIsContextMenuVisible"], [514, 53, 471, 53], [514, 54, 471, 54], [514, 59, 471, 59], [514, 60, 471, 61], [515, 8, 471, 61, "children"], [515, 16, 471, 61], [515, 31, 473, 8], [515, 35, 473, 8, "_jsxDevRuntime"], [515, 49, 473, 8], [515, 50, 473, 8, "jsxDEV"], [515, 56, 473, 8], [515, 58, 473, 9, "_TouchableOpacity"], [515, 75, 473, 9], [515, 76, 473, 9, "default"], [515, 83, 473, 25], [516, 10, 474, 10, "style"], [516, 15, 474, 15], [516, 17, 474, 17], [517, 12, 474, 19, "flex"], [517, 16, 474, 23], [517, 18, 474, 25], [517, 19, 474, 26], [518, 12, 474, 28, "backgroundColor"], [518, 27, 474, 43], [518, 29, 474, 45], [518, 46, 474, 62], [519, 12, 474, 64, "justifyContent"], [519, 26, 474, 78], [519, 28, 474, 80], [519, 36, 474, 88], [520, 12, 474, 90, "alignItems"], [520, 22, 474, 100], [520, 24, 474, 102], [521, 10, 474, 111], [521, 11, 474, 113], [522, 10, 475, 10, "activeOpacity"], [522, 23, 475, 23], [522, 25, 475, 25], [522, 26, 475, 27], [523, 10, 476, 10, "onPressOut"], [523, 20, 476, 20], [523, 22, 476, 22, "onPressOut"], [523, 23, 476, 22], [523, 28, 476, 28, "setIsContextMenuVisible"], [523, 51, 476, 51], [523, 52, 476, 52], [523, 57, 476, 57], [523, 58, 476, 59], [524, 10, 476, 59, "children"], [524, 18, 476, 59], [524, 33, 478, 10], [524, 37, 478, 10, "_jsxDevRuntime"], [524, 51, 478, 10], [524, 52, 478, 10, "jsxDEV"], [524, 58, 478, 10], [524, 60, 478, 11, "_View"], [524, 65, 478, 11], [524, 66, 478, 11, "default"], [524, 73, 478, 15], [525, 12, 478, 16, "style"], [525, 17, 478, 21], [525, 19, 478, 23], [526, 14, 478, 25, "backgroundColor"], [526, 29, 478, 40], [526, 31, 478, 42, "colors"], [526, 37, 478, 48], [526, 38, 478, 49, "background"], [526, 48, 478, 59], [527, 14, 478, 61, "borderRadius"], [527, 26, 478, 73], [527, 28, 478, 75], [527, 30, 478, 77], [528, 14, 478, 79, "padding"], [528, 21, 478, 86], [528, 23, 478, 88], [528, 25, 478, 90], [529, 14, 478, 92, "width"], [529, 19, 478, 97], [529, 21, 478, 99], [530, 12, 478, 105], [530, 13, 478, 107], [531, 12, 478, 107, "children"], [531, 20, 478, 107], [531, 36, 479, 12], [531, 40, 479, 12, "_jsxDevRuntime"], [531, 54, 479, 12], [531, 55, 479, 12, "jsxDEV"], [531, 61, 479, 12], [531, 63, 479, 13, "_TouchableOpacity"], [531, 80, 479, 13], [531, 81, 479, 13, "default"], [531, 88, 479, 29], [532, 14, 479, 30, "onPress"], [532, 21, 479, 37], [532, 23, 479, 39, "handleCopyMessage"], [532, 40, 479, 57], [533, 14, 479, 58, "style"], [533, 19, 479, 63], [533, 21, 479, 65], [534, 16, 479, 67, "paddingVertical"], [534, 31, 479, 82], [534, 33, 479, 84], [535, 14, 479, 87], [535, 15, 479, 89], [536, 14, 479, 89, "children"], [536, 22, 479, 89], [536, 37, 480, 14], [536, 41, 480, 14, "_jsxDevRuntime"], [536, 55, 480, 14], [536, 56, 480, 14, "jsxDEV"], [536, 62, 480, 14], [536, 64, 480, 15, "_Text"], [536, 69, 480, 15], [536, 70, 480, 15, "default"], [536, 77, 480, 19], [537, 16, 480, 20, "style"], [537, 21, 480, 25], [537, 23, 480, 27], [538, 18, 480, 29, "fontSize"], [538, 26, 480, 37], [538, 28, 480, 39], [538, 30, 480, 41], [539, 18, 480, 43, "fontFamily"], [539, 28, 480, 53], [539, 30, 480, 55], [539, 49, 480, 74], [540, 18, 480, 76, "color"], [540, 23, 480, 81], [540, 25, 480, 83, "colors"], [540, 31, 480, 89], [540, 32, 480, 90, "text"], [541, 16, 480, 95], [541, 17, 480, 97], [542, 16, 480, 97, "children"], [542, 24, 480, 97], [542, 26, 480, 98], [543, 14, 480, 110], [544, 16, 480, 110, "fileName"], [544, 24, 480, 110], [544, 26, 480, 110, "_jsxFileName"], [544, 38, 480, 110], [545, 16, 480, 110, "lineNumber"], [545, 26, 480, 110], [546, 16, 480, 110, "columnNumber"], [546, 28, 480, 110], [547, 14, 480, 110], [547, 21, 480, 116], [548, 12, 480, 117], [549, 14, 480, 117, "fileName"], [549, 22, 480, 117], [549, 24, 480, 117, "_jsxFileName"], [549, 36, 480, 117], [550, 14, 480, 117, "lineNumber"], [550, 24, 480, 117], [551, 14, 480, 117, "columnNumber"], [551, 26, 480, 117], [552, 12, 480, 117], [552, 19, 481, 30], [552, 20, 481, 31], [552, 22, 482, 13, "longPressedMessage"], [552, 40, 482, 31], [552, 42, 482, 33, "role"], [552, 46, 482, 37], [552, 51, 482, 42], [552, 62, 482, 53], [552, 79, 483, 14], [552, 83, 483, 14, "_jsxDevRuntime"], [552, 97, 483, 14], [552, 98, 483, 14, "jsxDEV"], [552, 104, 483, 14], [552, 106, 483, 15, "_TouchableOpacity"], [552, 123, 483, 15], [552, 124, 483, 15, "default"], [552, 131, 483, 31], [553, 14, 483, 32, "onPress"], [553, 21, 483, 39], [553, 23, 483, 41, "handleListenToMessage"], [553, 44, 483, 63], [554, 14, 483, 64, "style"], [554, 19, 483, 69], [554, 21, 483, 71], [555, 16, 483, 73, "paddingVertical"], [555, 31, 483, 88], [555, 33, 483, 90], [556, 14, 483, 93], [556, 15, 483, 95], [557, 14, 483, 95, "children"], [557, 22, 483, 95], [557, 37, 484, 16], [557, 41, 484, 16, "_jsxDevRuntime"], [557, 55, 484, 16], [557, 56, 484, 16, "jsxDEV"], [557, 62, 484, 16], [557, 64, 484, 17, "_Text"], [557, 69, 484, 17], [557, 70, 484, 17, "default"], [557, 77, 484, 21], [558, 16, 484, 22, "style"], [558, 21, 484, 27], [558, 23, 484, 29], [559, 18, 484, 31, "fontSize"], [559, 26, 484, 39], [559, 28, 484, 41], [559, 30, 484, 43], [560, 18, 484, 45, "fontFamily"], [560, 28, 484, 55], [560, 30, 484, 57], [560, 49, 484, 76], [561, 18, 484, 78, "color"], [561, 23, 484, 83], [561, 25, 484, 85, "colors"], [561, 31, 484, 91], [561, 32, 484, 92, "text"], [562, 16, 484, 97], [562, 17, 484, 99], [563, 16, 484, 99, "children"], [563, 24, 484, 99], [563, 26, 484, 100], [564, 14, 484, 117], [565, 16, 484, 117, "fileName"], [565, 24, 484, 117], [565, 26, 484, 117, "_jsxFileName"], [565, 38, 484, 117], [566, 16, 484, 117, "lineNumber"], [566, 26, 484, 117], [567, 16, 484, 117, "columnNumber"], [567, 28, 484, 117], [568, 14, 484, 117], [568, 21, 484, 123], [569, 12, 484, 124], [570, 14, 484, 124, "fileName"], [570, 22, 484, 124], [570, 24, 484, 124, "_jsxFileName"], [570, 36, 484, 124], [571, 14, 484, 124, "lineNumber"], [571, 24, 484, 124], [572, 14, 484, 124, "columnNumber"], [572, 26, 484, 124], [573, 12, 484, 124], [573, 19, 485, 32], [573, 20, 486, 13], [574, 10, 486, 13], [575, 12, 486, 13, "fileName"], [575, 20, 486, 13], [575, 22, 486, 13, "_jsxFileName"], [575, 34, 486, 13], [576, 12, 486, 13, "lineNumber"], [576, 22, 486, 13], [577, 12, 486, 13, "columnNumber"], [577, 24, 486, 13], [578, 10, 486, 13], [578, 17, 487, 16], [579, 8, 487, 17], [580, 10, 487, 17, "fileName"], [580, 18, 487, 17], [580, 20, 487, 17, "_jsxFileName"], [580, 32, 487, 17], [581, 10, 487, 17, "lineNumber"], [581, 20, 487, 17], [582, 10, 487, 17, "columnNumber"], [582, 22, 487, 17], [583, 8, 487, 17], [583, 15, 488, 26], [584, 6, 488, 27], [585, 8, 488, 27, "fileName"], [585, 16, 488, 27], [585, 18, 488, 27, "_jsxFileName"], [585, 30, 488, 27], [586, 8, 488, 27, "lineNumber"], [586, 18, 488, 27], [587, 8, 488, 27, "columnNumber"], [587, 20, 488, 27], [588, 6, 488, 27], [588, 13, 489, 13], [588, 14, 489, 14], [589, 4, 489, 14], [590, 6, 489, 14, "fileName"], [590, 14, 489, 14], [590, 16, 489, 14, "_jsxFileName"], [590, 28, 489, 14], [591, 6, 489, 14, "lineNumber"], [591, 16, 489, 14], [592, 6, 489, 14, "columnNumber"], [592, 18, 489, 14], [593, 4, 489, 14], [593, 11, 490, 10], [593, 12, 490, 11], [594, 2, 492, 0], [595, 2, 492, 1, "_s"], [595, 4, 492, 1], [595, 5, 36, 24, "BrainstormScreen"], [595, 21, 36, 40], [596, 4, 36, 40], [596, 12, 37, 17, "useSafeAreaInsets"], [596, 57, 37, 34], [596, 59, 38, 17, "useColors"], [596, 79, 38, 26], [596, 81, 39, 24, "useFonts"], [596, 98, 39, 32], [596, 100, 45, 19, "useAudioRecorder"], [596, 127, 45, 35], [596, 129, 46, 24, "useAudioRecorderState"], [596, 161, 46, 45], [597, 2, 46, 45], [598, 2, 46, 45, "_c"], [598, 4, 46, 45], [598, 7, 36, 24, "BrainstormScreen"], [598, 23, 36, 40], [599, 2, 36, 40], [599, 6, 36, 40, "_c"], [599, 8, 36, 40], [600, 2, 36, 40, "$RefreshReg$"], [600, 14, 36, 40], [600, 15, 36, 40, "_c"], [600, 17, 36, 40], [601, 0, 36, 40], [601, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCmC;YC4B;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJK;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;YCI;GDI;gBiB0F,iCjB;0BkBuB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}