{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = {\n    uri: \"/assets/?unstable_path=.%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets/search-icon.png\",\n    width: 96,\n    height: 96\n  };\n});", "lineCount": 7, "map": [[7, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}