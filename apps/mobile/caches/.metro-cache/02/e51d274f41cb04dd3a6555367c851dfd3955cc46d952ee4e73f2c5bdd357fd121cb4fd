{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./BottomTabBarHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 75, "index": 122}}], "key": "BS7MoYNRbzb0k5dw/PEy/Z/IgGU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useBottomTabBarHeight = useBottomTabBarHeight;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _BottomTabBarHeightContext = require(_dependencyMap[1], \"./BottomTabBarHeightContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useBottomTabBarHeight() {\n    var height = React.useContext(_BottomTabBarHeightContext.BottomTabBarHeightContext);\n    if (height === undefined) {\n      throw new Error(\"Couldn't find the bottom tab bar height. Are you inside a screen in Bottom Tab Navigator?\");\n    }\n    return height;\n  }\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useBottomTabBarHeight"], [7, 31, 1, 13], [7, 34, 1, 13, "useBottomTabBarHeight"], [7, 55, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_BottomTabBarHeightContext"], [9, 32, 4, 0], [9, 35, 4, 0, "require"], [9, 42, 4, 0], [9, 43, 4, 0, "_dependencyMap"], [9, 57, 4, 0], [10, 2, 4, 75], [10, 11, 4, 75, "_interopRequireWildcard"], [10, 35, 4, 75, "e"], [10, 36, 4, 75], [10, 38, 4, 75, "t"], [10, 39, 4, 75], [10, 68, 4, 75, "WeakMap"], [10, 75, 4, 75], [10, 81, 4, 75, "r"], [10, 82, 4, 75], [10, 89, 4, 75, "WeakMap"], [10, 96, 4, 75], [10, 100, 4, 75, "n"], [10, 101, 4, 75], [10, 108, 4, 75, "WeakMap"], [10, 115, 4, 75], [10, 127, 4, 75, "_interopRequireWildcard"], [10, 150, 4, 75], [10, 162, 4, 75, "_interopRequireWildcard"], [10, 163, 4, 75, "e"], [10, 164, 4, 75], [10, 166, 4, 75, "t"], [10, 167, 4, 75], [10, 176, 4, 75, "t"], [10, 177, 4, 75], [10, 181, 4, 75, "e"], [10, 182, 4, 75], [10, 186, 4, 75, "e"], [10, 187, 4, 75], [10, 188, 4, 75, "__esModule"], [10, 198, 4, 75], [10, 207, 4, 75, "e"], [10, 208, 4, 75], [10, 214, 4, 75, "o"], [10, 215, 4, 75], [10, 217, 4, 75, "i"], [10, 218, 4, 75], [10, 220, 4, 75, "f"], [10, 221, 4, 75], [10, 226, 4, 75, "__proto__"], [10, 235, 4, 75], [10, 243, 4, 75, "default"], [10, 250, 4, 75], [10, 252, 4, 75, "e"], [10, 253, 4, 75], [10, 270, 4, 75, "e"], [10, 271, 4, 75], [10, 294, 4, 75, "e"], [10, 295, 4, 75], [10, 320, 4, 75, "e"], [10, 321, 4, 75], [10, 330, 4, 75, "f"], [10, 331, 4, 75], [10, 337, 4, 75, "o"], [10, 338, 4, 75], [10, 341, 4, 75, "t"], [10, 342, 4, 75], [10, 345, 4, 75, "n"], [10, 346, 4, 75], [10, 349, 4, 75, "r"], [10, 350, 4, 75], [10, 358, 4, 75, "o"], [10, 359, 4, 75], [10, 360, 4, 75, "has"], [10, 363, 4, 75], [10, 364, 4, 75, "e"], [10, 365, 4, 75], [10, 375, 4, 75, "o"], [10, 376, 4, 75], [10, 377, 4, 75, "get"], [10, 380, 4, 75], [10, 381, 4, 75, "e"], [10, 382, 4, 75], [10, 385, 4, 75, "o"], [10, 386, 4, 75], [10, 387, 4, 75, "set"], [10, 390, 4, 75], [10, 391, 4, 75, "e"], [10, 392, 4, 75], [10, 394, 4, 75, "f"], [10, 395, 4, 75], [10, 409, 4, 75, "_t"], [10, 411, 4, 75], [10, 415, 4, 75, "e"], [10, 416, 4, 75], [10, 432, 4, 75, "_t"], [10, 434, 4, 75], [10, 441, 4, 75, "hasOwnProperty"], [10, 455, 4, 75], [10, 456, 4, 75, "call"], [10, 460, 4, 75], [10, 461, 4, 75, "e"], [10, 462, 4, 75], [10, 464, 4, 75, "_t"], [10, 466, 4, 75], [10, 473, 4, 75, "i"], [10, 474, 4, 75], [10, 478, 4, 75, "o"], [10, 479, 4, 75], [10, 482, 4, 75, "Object"], [10, 488, 4, 75], [10, 489, 4, 75, "defineProperty"], [10, 503, 4, 75], [10, 508, 4, 75, "Object"], [10, 514, 4, 75], [10, 515, 4, 75, "getOwnPropertyDescriptor"], [10, 539, 4, 75], [10, 540, 4, 75, "e"], [10, 541, 4, 75], [10, 543, 4, 75, "_t"], [10, 545, 4, 75], [10, 552, 4, 75, "i"], [10, 553, 4, 75], [10, 554, 4, 75, "get"], [10, 557, 4, 75], [10, 561, 4, 75, "i"], [10, 562, 4, 75], [10, 563, 4, 75, "set"], [10, 566, 4, 75], [10, 570, 4, 75, "o"], [10, 571, 4, 75], [10, 572, 4, 75, "f"], [10, 573, 4, 75], [10, 575, 4, 75, "_t"], [10, 577, 4, 75], [10, 579, 4, 75, "i"], [10, 580, 4, 75], [10, 584, 4, 75, "f"], [10, 585, 4, 75], [10, 586, 4, 75, "_t"], [10, 588, 4, 75], [10, 592, 4, 75, "e"], [10, 593, 4, 75], [10, 594, 4, 75, "_t"], [10, 596, 4, 75], [10, 607, 4, 75, "f"], [10, 608, 4, 75], [10, 613, 4, 75, "e"], [10, 614, 4, 75], [10, 616, 4, 75, "t"], [10, 617, 4, 75], [11, 2, 5, 7], [11, 11, 5, 16, "useBottomTabBarHeight"], [11, 32, 5, 37, "useBottomTabBarHeight"], [11, 33, 5, 37], [11, 35, 5, 40], [12, 4, 6, 2], [12, 8, 6, 8, "height"], [12, 14, 6, 14], [12, 17, 6, 17, "React"], [12, 22, 6, 22], [12, 23, 6, 23, "useContext"], [12, 33, 6, 33], [12, 34, 6, 34, "BottomTabBarHeightContext"], [12, 86, 6, 59], [12, 87, 6, 60], [13, 4, 7, 2], [13, 8, 7, 6, "height"], [13, 14, 7, 12], [13, 19, 7, 17, "undefined"], [13, 28, 7, 26], [13, 30, 7, 28], [14, 6, 8, 4], [14, 12, 8, 10], [14, 16, 8, 14, "Error"], [14, 21, 8, 19], [14, 22, 8, 20], [14, 113, 8, 111], [14, 114, 8, 112], [15, 4, 9, 2], [16, 4, 10, 2], [16, 11, 10, 9, "height"], [16, 17, 10, 15], [17, 2, 11, 0], [18, 0, 11, 1], [18, 3]], "functionMap": {"names": ["<global>", "useBottomTabBarHeight"], "mappings": "AAA;OCI;CDM"}}, "type": "js/module"}]}