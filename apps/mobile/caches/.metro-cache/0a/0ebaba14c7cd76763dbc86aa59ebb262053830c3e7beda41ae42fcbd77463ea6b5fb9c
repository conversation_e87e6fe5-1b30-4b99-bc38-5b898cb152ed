{"dependencies": [{"name": "../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 48, "index": 62}}], "key": "K1yKq+VUoHdgwBY7Fz9TrE1h5uU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.prepareUIRegistry = void 0;\n  var _threads = require(_dependencyMap[0], \"../threads\");\n  var _worklet_5591855314696_init_data = {\n    code: \"function reactNativeReanimated_FrameCallbackRegistryUITs1(){const frameCallbackRegistry={frameCallbackRegistry:new Map(),activeFrameCallbacks:new Set(),previousFrameTimestamp:null,nextCallId:0,runCallbacks:function(callId){var _this=this;const loop=function(timestamp){if(callId!==_this.nextCallId){return;}if(_this.previousFrameTimestamp===null){_this.previousFrameTimestamp=timestamp;}const delta=timestamp-_this.previousFrameTimestamp;_this.activeFrameCallbacks.forEach(function(callbackId){const callbackDetails=_this.frameCallbackRegistry.get(callbackId);const{startTime:startTime}=callbackDetails;if(startTime===null){callbackDetails.startTime=timestamp;callbackDetails.callback({timestamp:timestamp,timeSincePreviousFrame:null,timeSinceFirstFrame:0});}else{callbackDetails.callback({timestamp:timestamp,timeSincePreviousFrame:delta,timeSinceFirstFrame:timestamp-startTime});}});if(_this.activeFrameCallbacks.size>0){_this.previousFrameTimestamp=timestamp;requestAnimationFrame(loop);}else{_this.previousFrameTimestamp=null;}};if(this.activeFrameCallbacks.size===1&&callId===this.nextCallId){requestAnimationFrame(loop);}},registerFrameCallback:function(callback,callbackId){this.frameCallbackRegistry.set(callbackId,{callback:callback,startTime:null});},unregisterFrameCallback:function(callbackId){this.manageStateFrameCallback(callbackId,false);this.frameCallbackRegistry.delete(callbackId);},manageStateFrameCallback:function(callbackId,state){if(callbackId===-1){return;}if(state){this.activeFrameCallbacks.add(callbackId);this.runCallbacks(this.nextCallId);}else{const callback=this.frameCallbackRegistry.get(callbackId);callback.startTime=null;this.activeFrameCallbacks.delete(callbackId);if(this.activeFrameCallbacks.size===0){this.nextCallId+=1;}}}};global._frameCallbackRegistry=frameCallbackRegistry;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/frameCallback/FrameCallbackRegistryUI.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_FrameCallbackRegistryUITs1\\\",\\\"frameCallbackRegistry\\\",\\\"Map\\\",\\\"activeFrameCallbacks\\\",\\\"Set\\\",\\\"previousFrameTimestamp\\\",\\\"nextCallId\\\",\\\"runCallbacks\\\",\\\"callId\\\",\\\"_this\\\",\\\"loop\\\",\\\"timestamp\\\",\\\"delta\\\",\\\"forEach\\\",\\\"callbackId\\\",\\\"callbackDetails\\\",\\\"get\\\",\\\"startTime\\\",\\\"callback\\\",\\\"timeSincePreviousFrame\\\",\\\"timeSinceFirstFrame\\\",\\\"size\\\",\\\"requestAnimationFrame\\\",\\\"registerFrameCallback\\\",\\\"set\\\",\\\"unregisterFrameCallback\\\",\\\"manageStateFrameCallback\\\",\\\"delete\\\",\\\"state\\\",\\\"add\\\",\\\"global\\\",\\\"_frameCallbackRegistry\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/frameCallback/FrameCallbackRegistryUI.ts\\\"],\\\"mappings\\\":\\\"AA4BoD,SAAAA,gDAAMA,CAAA,EAGxD,KAAM,CAAAC,qBAA8C,CAAG,CACrDA,qBAAqB,CAAE,GAAI,CAAAC,GAAG,CAA0B,CAAC,CACzDC,oBAAoB,CAAE,GAAI,CAAAC,GAAG,CAAS,CAAC,CACvCC,sBAAsB,CAAE,IAAI,CAC5BC,UAAU,CAAE,CAAC,CAEbC,YAAY,SAAAA,CAACC,MAAM,CAAE,KAAAC,KAAA,MACnB,KAAM,CAAAC,IAAI,CAAG,QAAAA,CAACC,SAAiB,CAAK,CAClC,GAAIH,MAAM,GAAKC,KAAI,CAACH,UAAU,CAAE,CAC9B,OACF,CACA,GAAIG,KAAI,CAACJ,sBAAsB,GAAK,IAAI,CAAE,CACxCI,KAAI,CAACJ,sBAAsB,CAAGM,SAAS,CACzC,CAEA,KAAM,CAAAC,KAAK,CAAGD,SAAS,CAAGF,KAAI,CAACJ,sBAAsB,CAErDI,KAAI,CAACN,oBAAoB,CAACU,OAAO,CAAC,SAACC,UAAkB,CAAK,CACxD,KAAM,CAAAC,eAAe,CAAGN,KAAI,CAACR,qBAAqB,CAACe,GAAG,CAACF,UAAU,CAAE,CAEnE,KAAM,CAAEG,SAAA,CAAAA,SAAU,CAAC,CAAGF,eAAe,CAErC,GAAIE,SAAS,GAAK,IAAI,CAAE,CAEtBF,eAAe,CAACE,SAAS,CAAGN,SAAS,CAErCI,eAAe,CAACG,QAAQ,CAAC,CACvBP,SAAS,CAATA,SAAS,CACTQ,sBAAsB,CAAE,IAAI,CAC5BC,mBAAmB,CAAE,CACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CAELL,eAAe,CAACG,QAAQ,CAAC,CACvBP,SAAS,CAATA,SAAS,CACTQ,sBAAsB,CAAEP,KAAK,CAC7BQ,mBAAmB,CAAET,SAAS,CAAGM,SACnC,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,GAAIR,KAAI,CAACN,oBAAoB,CAACkB,IAAI,CAAG,CAAC,CAAE,CACtCZ,KAAI,CAACJ,sBAAsB,CAAGM,SAAS,CACvCW,qBAAqB,CAACZ,IAAI,CAAC,CAC7B,CAAC,IAAM,CACLD,KAAI,CAACJ,sBAAsB,CAAG,IAAI,CACpC,CACF,CAAC,CAKD,GAAI,IAAI,CAACF,oBAAoB,CAACkB,IAAI,GAAK,CAAC,EAAIb,MAAM,GAAK,IAAI,CAACF,UAAU,CAAE,CACtEgB,qBAAqB,CAACZ,IAAI,CAAC,CAC7B,CACF,CAAC,CAEDa,qBAAqB,SAAAA,CACnBL,QAAwC,CACxCJ,UAAkB,CAClB,CACA,IAAI,CAACb,qBAAqB,CAACuB,GAAG,CAACV,UAAU,CAAE,CACzCI,QAAQ,CAARA,QAAQ,CACRD,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,CAEDQ,uBAAuB,SAAAA,CAACX,UAAkB,CAAE,CAC1C,IAAI,CAACY,wBAAwB,CAACZ,UAAU,CAAE,KAAK,CAAC,CAChD,IAAI,CAACb,qBAAqB,CAAC0B,MAAM,CAACb,UAAU,CAAC,CAC/C,CAAC,CAEDY,wBAAwB,SAAAA,CAACZ,UAAkB,CAAEc,KAAc,CAAE,CAC3D,GAAId,UAAU,GAAK,CAAC,CAAC,CAAE,CACrB,OACF,CACA,GAAIc,KAAK,CAAE,CACT,IAAI,CAACzB,oBAAoB,CAAC0B,GAAG,CAACf,UAAU,CAAC,CACzC,IAAI,CAACP,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC,CACpC,CAAC,IAAM,CACL,KAAM,CAAAY,QAAQ,CAAG,IAAI,CAACjB,qBAAqB,CAACe,GAAG,CAACF,UAAU,CAAE,CAC5DI,QAAQ,CAACD,SAAS,CAAG,IAAI,CAEzB,IAAI,CAACd,oBAAoB,CAACwB,MAAM,CAACb,UAAU,CAAC,CAC5C,GAAI,IAAI,CAACX,oBAAoB,CAACkB,IAAI,GAAK,CAAC,CAAE,CACxC,IAAI,CAACf,UAAU,EAAI,CAAC,CACtB,CACF,CACF,CACF,CAAC,CAEDwB,MAAM,CAACC,sBAAsB,CAAG9B,qBAAqB,CACvD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var prepareUIRegistry = exports.prepareUIRegistry = (0, _threads.runOnUIImmediately)(function () {\n    var _e = [new global.Error(), 1, -27];\n    var reactNativeReanimated_FrameCallbackRegistryUITs1 = function () {\n      var frameCallbackRegistry = {\n        frameCallbackRegistry: new Map(),\n        activeFrameCallbacks: new Set(),\n        previousFrameTimestamp: null,\n        nextCallId: 0,\n        runCallbacks(callId) {\n          var loop = timestamp => {\n            if (callId !== this.nextCallId) {\n              return;\n            }\n            if (this.previousFrameTimestamp === null) {\n              this.previousFrameTimestamp = timestamp;\n            }\n            var delta = timestamp - this.previousFrameTimestamp;\n            this.activeFrameCallbacks.forEach(callbackId => {\n              var callbackDetails = this.frameCallbackRegistry.get(callbackId);\n              var startTime = callbackDetails.startTime;\n              if (startTime === null) {\n                // First frame\n                callbackDetails.startTime = timestamp;\n                callbackDetails.callback({\n                  timestamp,\n                  timeSincePreviousFrame: null,\n                  timeSinceFirstFrame: 0\n                });\n              } else {\n                // Next frame\n                callbackDetails.callback({\n                  timestamp,\n                  timeSincePreviousFrame: delta,\n                  timeSinceFirstFrame: timestamp - startTime\n                });\n              }\n            });\n            if (this.activeFrameCallbacks.size > 0) {\n              this.previousFrameTimestamp = timestamp;\n              requestAnimationFrame(loop);\n            } else {\n              this.previousFrameTimestamp = null;\n            }\n          };\n\n          // runCallback() should only be called after registering a callback,\n          // so if there is only one active callback, then it means that there were\n          // zero previously and the loop isn't running yet.\n          if (this.activeFrameCallbacks.size === 1 && callId === this.nextCallId) {\n            requestAnimationFrame(loop);\n          }\n        },\n        registerFrameCallback(callback, callbackId) {\n          this.frameCallbackRegistry.set(callbackId, {\n            callback,\n            startTime: null\n          });\n        },\n        unregisterFrameCallback(callbackId) {\n          this.manageStateFrameCallback(callbackId, false);\n          this.frameCallbackRegistry.delete(callbackId);\n        },\n        manageStateFrameCallback(callbackId, state) {\n          if (callbackId === -1) {\n            return;\n          }\n          if (state) {\n            this.activeFrameCallbacks.add(callbackId);\n            this.runCallbacks(this.nextCallId);\n          } else {\n            var callback = this.frameCallbackRegistry.get(callbackId);\n            callback.startTime = null;\n            this.activeFrameCallbacks.delete(callbackId);\n            if (this.activeFrameCallbacks.size === 0) {\n              this.nextCallId += 1;\n            }\n          }\n        }\n      };\n      global._frameCallbackRegistry = frameCallbackRegistry;\n    };\n    reactNativeReanimated_FrameCallbackRegistryUITs1.__closure = {};\n    reactNativeReanimated_FrameCallbackRegistryUITs1.__workletHash = 5591855314696;\n    reactNativeReanimated_FrameCallbackRegistryUITs1.__initData = _worklet_5591855314696_init_data;\n    reactNativeReanimated_FrameCallbackRegistryUITs1.__stackDetails = _e;\n    return reactNativeReanimated_FrameCallbackRegistryUITs1;\n  }());\n});", "lineCount": 102, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "prepareUIRegistry"], [7, 27, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_threads"], [8, 14, 2, 0], [8, 17, 2, 0, "require"], [8, 24, 2, 0], [8, 25, 2, 0, "_dependencyMap"], [8, 39, 2, 0], [9, 2, 2, 48], [9, 6, 2, 48, "_worklet_5591855314696_init_data"], [9, 38, 2, 48], [10, 4, 2, 48, "code"], [10, 8, 2, 48], [11, 4, 2, 48, "location"], [11, 12, 2, 48], [12, 4, 2, 48, "sourceMap"], [12, 13, 2, 48], [13, 4, 2, 48, "version"], [13, 11, 2, 48], [14, 2, 2, 48], [15, 2, 29, 7], [15, 6, 29, 13, "prepareUIRegistry"], [15, 23, 29, 30], [15, 26, 29, 30, "exports"], [15, 33, 29, 30], [15, 34, 29, 30, "prepareUIRegistry"], [15, 51, 29, 30], [15, 54, 29, 33], [15, 58, 29, 33, "runOnUIImmediately"], [15, 85, 29, 51], [15, 87, 29, 52], [16, 4, 29, 52], [16, 8, 29, 52, "_e"], [16, 10, 29, 52], [16, 18, 29, 52, "global"], [16, 24, 29, 52], [16, 25, 29, 52, "Error"], [16, 30, 29, 52], [17, 4, 29, 52], [17, 8, 29, 52, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [17, 56, 29, 52], [17, 68, 29, 52, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [17, 69, 29, 52], [17, 71, 29, 58], [18, 6, 32, 2], [18, 10, 32, 8, "frameCallbackRegistry"], [18, 31, 32, 54], [18, 34, 32, 57], [19, 8, 33, 4, "frameCallbackRegistry"], [19, 29, 33, 25], [19, 31, 33, 27], [19, 35, 33, 31, "Map"], [19, 38, 33, 34], [19, 39, 33, 60], [19, 40, 33, 61], [20, 8, 34, 4, "activeFrameCallbacks"], [20, 28, 34, 24], [20, 30, 34, 26], [20, 34, 34, 30, "Set"], [20, 37, 34, 33], [20, 38, 34, 42], [20, 39, 34, 43], [21, 8, 35, 4, "previousFrameTimestamp"], [21, 30, 35, 26], [21, 32, 35, 28], [21, 36, 35, 32], [22, 8, 36, 4, "nextCallId"], [22, 18, 36, 14], [22, 20, 36, 16], [22, 21, 36, 17], [23, 8, 38, 4, "runCallbacks"], [23, 20, 38, 16, "runCallbacks"], [23, 21, 38, 17, "callId"], [23, 27, 38, 23], [23, 29, 38, 25], [24, 10, 39, 6], [24, 14, 39, 12, "loop"], [24, 18, 39, 16], [24, 21, 39, 20, "timestamp"], [24, 30, 39, 37], [24, 34, 39, 42], [25, 12, 40, 8], [25, 16, 40, 12, "callId"], [25, 22, 40, 18], [25, 27, 40, 23], [25, 31, 40, 27], [25, 32, 40, 28, "nextCallId"], [25, 42, 40, 38], [25, 44, 40, 40], [26, 14, 41, 10], [27, 12, 42, 8], [28, 12, 43, 8], [28, 16, 43, 12], [28, 20, 43, 16], [28, 21, 43, 17, "previousFrameTimestamp"], [28, 43, 43, 39], [28, 48, 43, 44], [28, 52, 43, 48], [28, 54, 43, 50], [29, 14, 44, 10], [29, 18, 44, 14], [29, 19, 44, 15, "previousFrameTimestamp"], [29, 41, 44, 37], [29, 44, 44, 40, "timestamp"], [29, 53, 44, 49], [30, 12, 45, 8], [31, 12, 47, 8], [31, 16, 47, 14, "delta"], [31, 21, 47, 19], [31, 24, 47, 22, "timestamp"], [31, 33, 47, 31], [31, 36, 47, 34], [31, 40, 47, 38], [31, 41, 47, 39, "previousFrameTimestamp"], [31, 63, 47, 61], [32, 12, 49, 8], [32, 16, 49, 12], [32, 17, 49, 13, "activeFrameCallbacks"], [32, 37, 49, 33], [32, 38, 49, 34, "for<PERSON>ach"], [32, 45, 49, 41], [32, 46, 49, 43, "callbackId"], [32, 56, 49, 61], [32, 60, 49, 66], [33, 14, 50, 10], [33, 18, 50, 16, "callbackDetails"], [33, 33, 50, 31], [33, 36, 50, 34], [33, 40, 50, 38], [33, 41, 50, 39, "frameCallbackRegistry"], [33, 62, 50, 60], [33, 63, 50, 61, "get"], [33, 66, 50, 64], [33, 67, 50, 65, "callbackId"], [33, 77, 50, 75], [33, 78, 50, 77], [34, 14, 52, 10], [34, 18, 52, 18, "startTime"], [34, 27, 52, 27], [34, 30, 52, 32, "callbackDetails"], [34, 45, 52, 47], [34, 46, 52, 18, "startTime"], [34, 55, 52, 27], [35, 14, 54, 10], [35, 18, 54, 14, "startTime"], [35, 27, 54, 23], [35, 32, 54, 28], [35, 36, 54, 32], [35, 38, 54, 34], [36, 16, 55, 12], [37, 16, 56, 12, "callbackDetails"], [37, 31, 56, 27], [37, 32, 56, 28, "startTime"], [37, 41, 56, 37], [37, 44, 56, 40, "timestamp"], [37, 53, 56, 49], [38, 16, 58, 12, "callbackDetails"], [38, 31, 58, 27], [38, 32, 58, 28, "callback"], [38, 40, 58, 36], [38, 41, 58, 37], [39, 18, 59, 14, "timestamp"], [39, 27, 59, 23], [40, 18, 60, 14, "timeSincePreviousFrame"], [40, 40, 60, 36], [40, 42, 60, 38], [40, 46, 60, 42], [41, 18, 61, 14, "timeSinceFirstFrame"], [41, 37, 61, 33], [41, 39, 61, 35], [42, 16, 62, 12], [42, 17, 62, 13], [42, 18, 62, 14], [43, 14, 63, 10], [43, 15, 63, 11], [43, 21, 63, 17], [44, 16, 64, 12], [45, 16, 65, 12, "callbackDetails"], [45, 31, 65, 27], [45, 32, 65, 28, "callback"], [45, 40, 65, 36], [45, 41, 65, 37], [46, 18, 66, 14, "timestamp"], [46, 27, 66, 23], [47, 18, 67, 14, "timeSincePreviousFrame"], [47, 40, 67, 36], [47, 42, 67, 38, "delta"], [47, 47, 67, 43], [48, 18, 68, 14, "timeSinceFirstFrame"], [48, 37, 68, 33], [48, 39, 68, 35, "timestamp"], [48, 48, 68, 44], [48, 51, 68, 47, "startTime"], [49, 16, 69, 12], [49, 17, 69, 13], [49, 18, 69, 14], [50, 14, 70, 10], [51, 12, 71, 8], [51, 13, 71, 9], [51, 14, 71, 10], [52, 12, 73, 8], [52, 16, 73, 12], [52, 20, 73, 16], [52, 21, 73, 17, "activeFrameCallbacks"], [52, 41, 73, 37], [52, 42, 73, 38, "size"], [52, 46, 73, 42], [52, 49, 73, 45], [52, 50, 73, 46], [52, 52, 73, 48], [53, 14, 74, 10], [53, 18, 74, 14], [53, 19, 74, 15, "previousFrameTimestamp"], [53, 41, 74, 37], [53, 44, 74, 40, "timestamp"], [53, 53, 74, 49], [54, 14, 75, 10, "requestAnimationFrame"], [54, 35, 75, 31], [54, 36, 75, 32, "loop"], [54, 40, 75, 36], [54, 41, 75, 37], [55, 12, 76, 8], [55, 13, 76, 9], [55, 19, 76, 15], [56, 14, 77, 10], [56, 18, 77, 14], [56, 19, 77, 15, "previousFrameTimestamp"], [56, 41, 77, 37], [56, 44, 77, 40], [56, 48, 77, 44], [57, 12, 78, 8], [58, 10, 79, 6], [58, 11, 79, 7], [60, 10, 81, 6], [61, 10, 82, 6], [62, 10, 83, 6], [63, 10, 84, 6], [63, 14, 84, 10], [63, 18, 84, 14], [63, 19, 84, 15, "activeFrameCallbacks"], [63, 39, 84, 35], [63, 40, 84, 36, "size"], [63, 44, 84, 40], [63, 49, 84, 45], [63, 50, 84, 46], [63, 54, 84, 50, "callId"], [63, 60, 84, 56], [63, 65, 84, 61], [63, 69, 84, 65], [63, 70, 84, 66, "nextCallId"], [63, 80, 84, 76], [63, 82, 84, 78], [64, 12, 85, 8, "requestAnimationFrame"], [64, 33, 85, 29], [64, 34, 85, 30, "loop"], [64, 38, 85, 34], [64, 39, 85, 35], [65, 10, 86, 6], [66, 8, 87, 4], [66, 9, 87, 5], [67, 8, 89, 4, "registerFrameCallback"], [67, 29, 89, 25, "registerFrameCallback"], [67, 30, 90, 6, "callback"], [67, 38, 90, 46], [67, 40, 91, 6, "callbackId"], [67, 50, 91, 24], [67, 52, 92, 6], [68, 10, 93, 6], [68, 14, 93, 10], [68, 15, 93, 11, "frameCallbackRegistry"], [68, 36, 93, 32], [68, 37, 93, 33, "set"], [68, 40, 93, 36], [68, 41, 93, 37, "callbackId"], [68, 51, 93, 47], [68, 53, 93, 49], [69, 12, 94, 8, "callback"], [69, 20, 94, 16], [70, 12, 95, 8, "startTime"], [70, 21, 95, 17], [70, 23, 95, 19], [71, 10, 96, 6], [71, 11, 96, 7], [71, 12, 96, 8], [72, 8, 97, 4], [72, 9, 97, 5], [73, 8, 99, 4, "unregisterFrameCallback"], [73, 31, 99, 27, "unregisterFrameCallback"], [73, 32, 99, 28, "callbackId"], [73, 42, 99, 46], [73, 44, 99, 48], [74, 10, 100, 6], [74, 14, 100, 10], [74, 15, 100, 11, "manageStateFrameCallback"], [74, 39, 100, 35], [74, 40, 100, 36, "callbackId"], [74, 50, 100, 46], [74, 52, 100, 48], [74, 57, 100, 53], [74, 58, 100, 54], [75, 10, 101, 6], [75, 14, 101, 10], [75, 15, 101, 11, "frameCallbackRegistry"], [75, 36, 101, 32], [75, 37, 101, 33, "delete"], [75, 43, 101, 39], [75, 44, 101, 40, "callbackId"], [75, 54, 101, 50], [75, 55, 101, 51], [76, 8, 102, 4], [76, 9, 102, 5], [77, 8, 104, 4, "manageStateFrameCallback"], [77, 32, 104, 28, "manageStateFrameCallback"], [77, 33, 104, 29, "callbackId"], [77, 43, 104, 47], [77, 45, 104, 49, "state"], [77, 50, 104, 63], [77, 52, 104, 65], [78, 10, 105, 6], [78, 14, 105, 10, "callbackId"], [78, 24, 105, 20], [78, 29, 105, 25], [78, 30, 105, 26], [78, 31, 105, 27], [78, 33, 105, 29], [79, 12, 106, 8], [80, 10, 107, 6], [81, 10, 108, 6], [81, 14, 108, 10, "state"], [81, 19, 108, 15], [81, 21, 108, 17], [82, 12, 109, 8], [82, 16, 109, 12], [82, 17, 109, 13, "activeFrameCallbacks"], [82, 37, 109, 33], [82, 38, 109, 34, "add"], [82, 41, 109, 37], [82, 42, 109, 38, "callbackId"], [82, 52, 109, 48], [82, 53, 109, 49], [83, 12, 110, 8], [83, 16, 110, 12], [83, 17, 110, 13, "runCallbacks"], [83, 29, 110, 25], [83, 30, 110, 26], [83, 34, 110, 30], [83, 35, 110, 31, "nextCallId"], [83, 45, 110, 41], [83, 46, 110, 42], [84, 10, 111, 6], [84, 11, 111, 7], [84, 17, 111, 13], [85, 12, 112, 8], [85, 16, 112, 14, "callback"], [85, 24, 112, 22], [85, 27, 112, 25], [85, 31, 112, 29], [85, 32, 112, 30, "frameCallbackRegistry"], [85, 53, 112, 51], [85, 54, 112, 52, "get"], [85, 57, 112, 55], [85, 58, 112, 56, "callbackId"], [85, 68, 112, 66], [85, 69, 112, 68], [86, 12, 113, 8, "callback"], [86, 20, 113, 16], [86, 21, 113, 17, "startTime"], [86, 30, 113, 26], [86, 33, 113, 29], [86, 37, 113, 33], [87, 12, 115, 8], [87, 16, 115, 12], [87, 17, 115, 13, "activeFrameCallbacks"], [87, 37, 115, 33], [87, 38, 115, 34, "delete"], [87, 44, 115, 40], [87, 45, 115, 41, "callbackId"], [87, 55, 115, 51], [87, 56, 115, 52], [88, 12, 116, 8], [88, 16, 116, 12], [88, 20, 116, 16], [88, 21, 116, 17, "activeFrameCallbacks"], [88, 41, 116, 37], [88, 42, 116, 38, "size"], [88, 46, 116, 42], [88, 51, 116, 47], [88, 52, 116, 48], [88, 54, 116, 50], [89, 14, 117, 10], [89, 18, 117, 14], [89, 19, 117, 15, "nextCallId"], [89, 29, 117, 25], [89, 33, 117, 29], [89, 34, 117, 30], [90, 12, 118, 8], [91, 10, 119, 6], [92, 8, 120, 4], [93, 6, 121, 2], [93, 7, 121, 3], [94, 6, 123, 2, "global"], [94, 12, 123, 8], [94, 13, 123, 9, "_frameCallbackRegistry"], [94, 35, 123, 31], [94, 38, 123, 34, "frameCallbackRegistry"], [94, 59, 123, 55], [95, 4, 124, 0], [95, 5, 124, 1], [96, 4, 124, 1, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [96, 52, 124, 1], [96, 53, 124, 1, "__closure"], [96, 62, 124, 1], [97, 4, 124, 1, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [97, 52, 124, 1], [97, 53, 124, 1, "__workletHash"], [97, 66, 124, 1], [98, 4, 124, 1, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [98, 52, 124, 1], [98, 53, 124, 1, "__initData"], [98, 63, 124, 1], [98, 66, 124, 1, "_worklet_5591855314696_init_data"], [98, 98, 124, 1], [99, 4, 124, 1, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [99, 52, 124, 1], [99, 53, 124, 1, "__stackDetails"], [99, 67, 124, 1], [99, 70, 124, 1, "_e"], [99, 72, 124, 1], [100, 4, 124, 1], [100, 11, 124, 1, "reactNativeReanimated_FrameCallbackRegistryUITs1"], [100, 59, 124, 1], [101, 2, 124, 1], [101, 3, 29, 52], [101, 5, 124, 1], [101, 6, 124, 2], [102, 0, 124, 3], [102, 3]], "functionMap": {"names": ["<global>", "runOnUIImmediately$argument_0", "frameCallbackRegistry.runCallbacks", "loop", "activeFrameCallbacks.forEach$argument_0", "frameCallbackRegistry.registerFrameCallback", "frameCallbackRegistry.unregisterFrameCallback", "frameCallbackRegistry.manageStateFrameCallback"], "mappings": "AAA;oDC4B;ICS;mBCC;0CCU;SDsB;ODQ;KDQ;IIE;KJQ;IKE;KLG;IME;KNgB;CDI"}}, "type": "js/module"}]}