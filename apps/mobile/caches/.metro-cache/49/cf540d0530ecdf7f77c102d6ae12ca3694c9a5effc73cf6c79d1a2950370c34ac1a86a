{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 85, "index": 100}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 101}, "end": {"line": 4, "column": 31, "index": 132}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 182}, "end": {"line": 6, "column": 67, "index": 249}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "./Background.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 250}, "end": {"line": 7, "column": 45, "index": 295}}], "key": "rS5c0BsCtRGU5EOTkhE8gBPdgTE=", "exportNames": ["*"]}}, {"name": "./Header/getDefaultHeaderHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 296}, "end": {"line": 8, "column": 76, "index": 372}}], "key": "3odPzSL37tOWjaOt2BJGt37Bg6o=", "exportNames": ["*"]}}, {"name": "./Header/HeaderHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 373}, "end": {"line": 9, "column": 70, "index": 443}}], "key": "stZawU7KzLasMJlrjWF3s0um3fY=", "exportNames": ["*"]}}, {"name": "./Header/HeaderShownContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 444}, "end": {"line": 10, "column": 68, "index": 512}}], "key": "IveGqOWZUvFpozXTUMOpsU/p17I=", "exportNames": ["*"]}}, {"name": "./useFrameSize.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 513}, "end": {"line": 11, "column": 49, "index": 562}}], "key": "dRzp9Mme73SbFUGqz80tDHJoVo0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 563}, "end": {"line": 12, "column": 63, "index": 626}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Screen = Screen;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/View\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[5], \"react-native-safe-area-context\");\n  var _Background = require(_dependencyMap[6], \"./Background.js\");\n  var _getDefaultHeaderHeight = require(_dependencyMap[7], \"./Header/getDefaultHeaderHeight.js\");\n  var _HeaderHeightContext = require(_dependencyMap[8], \"./Header/HeaderHeightContext.js\");\n  var _HeaderShownContext = require(_dependencyMap[9], \"./Header/HeaderShownContext.js\");\n  var _useFrameSize = require(_dependencyMap[10], \"./useFrameSize.js\");\n  var _jsxRuntime = require(_dependencyMap[11], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function Screen(props) {\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const isParentHeaderShown = React.useContext(_HeaderShownContext.HeaderShownContext);\n    const parentHeaderHeight = React.useContext(_HeaderHeightContext.HeaderHeightContext);\n    const {\n      focused,\n      modal = false,\n      header,\n      headerShown = true,\n      headerTransparent,\n      headerStatusBarHeight = isParentHeaderShown ? 0 : insets.top,\n      navigation,\n      route,\n      children,\n      style\n    } = props;\n    const defaultHeaderHeight = (0, _useFrameSize.useFrameSize)(size => (0, _getDefaultHeaderHeight.getDefaultHeaderHeight)(size, modal, headerStatusBarHeight));\n    const [headerHeight, setHeaderHeight] = React.useState(defaultHeaderHeight);\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_Background.Background, {\n      \"aria-hidden\": !focused,\n      style: [styles.container, style]\n      // On Fabric we need to disable collapsing for the background to ensure\n      // that we won't render unnecessary views due to the view flattening.\n      ,\n\n      collapsable: false,\n      children: [headerShown ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationContext.Provider, {\n        value: navigation,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationRouteContext.Provider, {\n          value: route,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n            pointerEvents: \"box-none\",\n            onLayout: e => {\n              const {\n                height\n              } = e.nativeEvent.layout;\n              setHeaderHeight(height);\n            },\n            style: [styles.header, headerTransparent ? styles.absolute : null],\n            children: header\n          })\n        })\n      }) : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n        style: styles.content,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderShownContext.HeaderShownContext.Provider, {\n          value: isParentHeaderShown || headerShown !== false,\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderHeightContext.HeaderHeightContext.Provider, {\n            value: headerShown ? headerHeight : parentHeaderHeight ?? 0,\n            children: children\n          })\n        })\n      })]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1\n    },\n    content: {\n      flex: 1\n    },\n    header: {\n      zIndex: 1\n    },\n    absolute: {\n      position: 'absolute',\n      top: 0,\n      start: 0,\n      end: 0\n    }\n  });\n});", "lineCount": 92, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Screen"], [8, 16, 1, 13], [8, 19, 1, 13, "Screen"], [8, 25, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "React"], [10, 11, 4, 0], [10, 14, 4, 0, "_interopRequireWildcard"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 4, 31], [11, 6, 4, 31, "_StyleSheet"], [11, 17, 4, 31], [11, 20, 4, 31, "_interopRequireDefault"], [11, 42, 4, 31], [11, 43, 4, 31, "require"], [11, 50, 4, 31], [11, 51, 4, 31, "_dependencyMap"], [11, 65, 4, 31], [12, 2, 4, 31], [12, 6, 4, 31, "_View"], [12, 11, 4, 31], [12, 14, 4, 31, "_interopRequireDefault"], [12, 36, 4, 31], [12, 37, 4, 31, "require"], [12, 44, 4, 31], [12, 45, 4, 31, "_dependencyMap"], [12, 59, 4, 31], [13, 2, 6, 0], [13, 6, 6, 0, "_reactNativeSafeAreaContext"], [13, 33, 6, 0], [13, 36, 6, 0, "require"], [13, 43, 6, 0], [13, 44, 6, 0, "_dependencyMap"], [13, 58, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_Background"], [14, 17, 7, 0], [14, 20, 7, 0, "require"], [14, 27, 7, 0], [14, 28, 7, 0, "_dependencyMap"], [14, 42, 7, 0], [15, 2, 8, 0], [15, 6, 8, 0, "_getDefaultHeaderHeight"], [15, 29, 8, 0], [15, 32, 8, 0, "require"], [15, 39, 8, 0], [15, 40, 8, 0, "_dependencyMap"], [15, 54, 8, 0], [16, 2, 9, 0], [16, 6, 9, 0, "_HeaderHeightContext"], [16, 26, 9, 0], [16, 29, 9, 0, "require"], [16, 36, 9, 0], [16, 37, 9, 0, "_dependencyMap"], [16, 51, 9, 0], [17, 2, 10, 0], [17, 6, 10, 0, "_HeaderShownContext"], [17, 25, 10, 0], [17, 28, 10, 0, "require"], [17, 35, 10, 0], [17, 36, 10, 0, "_dependencyMap"], [17, 50, 10, 0], [18, 2, 11, 0], [18, 6, 11, 0, "_useFrameSize"], [18, 19, 11, 0], [18, 22, 11, 0, "require"], [18, 29, 11, 0], [18, 30, 11, 0, "_dependencyMap"], [18, 44, 11, 0], [19, 2, 12, 0], [19, 6, 12, 0, "_jsxRuntime"], [19, 17, 12, 0], [19, 20, 12, 0, "require"], [19, 27, 12, 0], [19, 28, 12, 0, "_dependencyMap"], [19, 42, 12, 0], [20, 2, 12, 63], [20, 11, 12, 63, "_interopRequireWildcard"], [20, 35, 12, 63, "e"], [20, 36, 12, 63], [20, 38, 12, 63, "t"], [20, 39, 12, 63], [20, 68, 12, 63, "WeakMap"], [20, 75, 12, 63], [20, 81, 12, 63, "r"], [20, 82, 12, 63], [20, 89, 12, 63, "WeakMap"], [20, 96, 12, 63], [20, 100, 12, 63, "n"], [20, 101, 12, 63], [20, 108, 12, 63, "WeakMap"], [20, 115, 12, 63], [20, 127, 12, 63, "_interopRequireWildcard"], [20, 150, 12, 63], [20, 162, 12, 63, "_interopRequireWildcard"], [20, 163, 12, 63, "e"], [20, 164, 12, 63], [20, 166, 12, 63, "t"], [20, 167, 12, 63], [20, 176, 12, 63, "t"], [20, 177, 12, 63], [20, 181, 12, 63, "e"], [20, 182, 12, 63], [20, 186, 12, 63, "e"], [20, 187, 12, 63], [20, 188, 12, 63, "__esModule"], [20, 198, 12, 63], [20, 207, 12, 63, "e"], [20, 208, 12, 63], [20, 214, 12, 63, "o"], [20, 215, 12, 63], [20, 217, 12, 63, "i"], [20, 218, 12, 63], [20, 220, 12, 63, "f"], [20, 221, 12, 63], [20, 226, 12, 63, "__proto__"], [20, 235, 12, 63], [20, 243, 12, 63, "default"], [20, 250, 12, 63], [20, 252, 12, 63, "e"], [20, 253, 12, 63], [20, 270, 12, 63, "e"], [20, 271, 12, 63], [20, 294, 12, 63, "e"], [20, 295, 12, 63], [20, 320, 12, 63, "e"], [20, 321, 12, 63], [20, 330, 12, 63, "f"], [20, 331, 12, 63], [20, 337, 12, 63, "o"], [20, 338, 12, 63], [20, 341, 12, 63, "t"], [20, 342, 12, 63], [20, 345, 12, 63, "n"], [20, 346, 12, 63], [20, 349, 12, 63, "r"], [20, 350, 12, 63], [20, 358, 12, 63, "o"], [20, 359, 12, 63], [20, 360, 12, 63, "has"], [20, 363, 12, 63], [20, 364, 12, 63, "e"], [20, 365, 12, 63], [20, 375, 12, 63, "o"], [20, 376, 12, 63], [20, 377, 12, 63, "get"], [20, 380, 12, 63], [20, 381, 12, 63, "e"], [20, 382, 12, 63], [20, 385, 12, 63, "o"], [20, 386, 12, 63], [20, 387, 12, 63, "set"], [20, 390, 12, 63], [20, 391, 12, 63, "e"], [20, 392, 12, 63], [20, 394, 12, 63, "f"], [20, 395, 12, 63], [20, 411, 12, 63, "t"], [20, 412, 12, 63], [20, 416, 12, 63, "e"], [20, 417, 12, 63], [20, 433, 12, 63, "t"], [20, 434, 12, 63], [20, 441, 12, 63, "hasOwnProperty"], [20, 455, 12, 63], [20, 456, 12, 63, "call"], [20, 460, 12, 63], [20, 461, 12, 63, "e"], [20, 462, 12, 63], [20, 464, 12, 63, "t"], [20, 465, 12, 63], [20, 472, 12, 63, "i"], [20, 473, 12, 63], [20, 477, 12, 63, "o"], [20, 478, 12, 63], [20, 481, 12, 63, "Object"], [20, 487, 12, 63], [20, 488, 12, 63, "defineProperty"], [20, 502, 12, 63], [20, 507, 12, 63, "Object"], [20, 513, 12, 63], [20, 514, 12, 63, "getOwnPropertyDescriptor"], [20, 538, 12, 63], [20, 539, 12, 63, "e"], [20, 540, 12, 63], [20, 542, 12, 63, "t"], [20, 543, 12, 63], [20, 550, 12, 63, "i"], [20, 551, 12, 63], [20, 552, 12, 63, "get"], [20, 555, 12, 63], [20, 559, 12, 63, "i"], [20, 560, 12, 63], [20, 561, 12, 63, "set"], [20, 564, 12, 63], [20, 568, 12, 63, "o"], [20, 569, 12, 63], [20, 570, 12, 63, "f"], [20, 571, 12, 63], [20, 573, 12, 63, "t"], [20, 574, 12, 63], [20, 576, 12, 63, "i"], [20, 577, 12, 63], [20, 581, 12, 63, "f"], [20, 582, 12, 63], [20, 583, 12, 63, "t"], [20, 584, 12, 63], [20, 588, 12, 63, "e"], [20, 589, 12, 63], [20, 590, 12, 63, "t"], [20, 591, 12, 63], [20, 602, 12, 63, "f"], [20, 603, 12, 63], [20, 608, 12, 63, "e"], [20, 609, 12, 63], [20, 611, 12, 63, "t"], [20, 612, 12, 63], [21, 2, 13, 7], [21, 11, 13, 16, "Screen"], [21, 17, 13, 22, "Screen"], [21, 18, 13, 23, "props"], [21, 23, 13, 28], [21, 25, 13, 30], [22, 4, 14, 2], [22, 10, 14, 8, "insets"], [22, 16, 14, 14], [22, 19, 14, 17], [22, 23, 14, 17, "useSafeAreaInsets"], [22, 68, 14, 34], [22, 70, 14, 35], [22, 71, 14, 36], [23, 4, 15, 2], [23, 10, 15, 8, "isParentHeaderShown"], [23, 29, 15, 27], [23, 32, 15, 30, "React"], [23, 37, 15, 35], [23, 38, 15, 36, "useContext"], [23, 48, 15, 46], [23, 49, 15, 47, "HeaderShownContext"], [23, 87, 15, 65], [23, 88, 15, 66], [24, 4, 16, 2], [24, 10, 16, 8, "parentHeaderHeight"], [24, 28, 16, 26], [24, 31, 16, 29, "React"], [24, 36, 16, 34], [24, 37, 16, 35, "useContext"], [24, 47, 16, 45], [24, 48, 16, 46, "HeaderHeightContext"], [24, 88, 16, 65], [24, 89, 16, 66], [25, 4, 17, 2], [25, 10, 17, 8], [26, 6, 18, 4, "focused"], [26, 13, 18, 11], [27, 6, 19, 4, "modal"], [27, 11, 19, 9], [27, 14, 19, 12], [27, 19, 19, 17], [28, 6, 20, 4, "header"], [28, 12, 20, 10], [29, 6, 21, 4, "headerShown"], [29, 17, 21, 15], [29, 20, 21, 18], [29, 24, 21, 22], [30, 6, 22, 4, "headerTransparent"], [30, 23, 22, 21], [31, 6, 23, 4, "headerStatusBarHeight"], [31, 27, 23, 25], [31, 30, 23, 28, "isParentHeaderShown"], [31, 49, 23, 47], [31, 52, 23, 50], [31, 53, 23, 51], [31, 56, 23, 54, "insets"], [31, 62, 23, 60], [31, 63, 23, 61, "top"], [31, 66, 23, 64], [32, 6, 24, 4, "navigation"], [32, 16, 24, 14], [33, 6, 25, 4, "route"], [33, 11, 25, 9], [34, 6, 26, 4, "children"], [34, 14, 26, 12], [35, 6, 27, 4, "style"], [36, 4, 28, 2], [36, 5, 28, 3], [36, 8, 28, 6, "props"], [36, 13, 28, 11], [37, 4, 29, 2], [37, 10, 29, 8, "defaultHeaderHeight"], [37, 29, 29, 27], [37, 32, 29, 30], [37, 36, 29, 30, "useFrameSize"], [37, 62, 29, 42], [37, 64, 29, 43, "size"], [37, 68, 29, 47], [37, 72, 29, 51], [37, 76, 29, 51, "getDefaultHeaderHeight"], [37, 122, 29, 73], [37, 124, 29, 74, "size"], [37, 128, 29, 78], [37, 130, 29, 80, "modal"], [37, 135, 29, 85], [37, 137, 29, 87, "headerStatusBarHeight"], [37, 158, 29, 108], [37, 159, 29, 109], [37, 160, 29, 110], [38, 4, 30, 2], [38, 10, 30, 8], [38, 11, 30, 9, "headerHeight"], [38, 23, 30, 21], [38, 25, 30, 23, "setHeaderHeight"], [38, 40, 30, 38], [38, 41, 30, 39], [38, 44, 30, 42, "React"], [38, 49, 30, 47], [38, 50, 30, 48, "useState"], [38, 58, 30, 56], [38, 59, 30, 57, "defaultHeaderHeight"], [38, 78, 30, 76], [38, 79, 30, 77], [39, 4, 31, 2], [39, 11, 31, 9], [39, 24, 31, 22], [39, 28, 31, 22, "_jsxs"], [39, 44, 31, 27], [39, 46, 31, 28, "Background"], [39, 68, 31, 38], [39, 70, 31, 40], [40, 6, 32, 4], [40, 19, 32, 17], [40, 21, 32, 19], [40, 22, 32, 20, "focused"], [40, 29, 32, 27], [41, 6, 33, 4, "style"], [41, 11, 33, 9], [41, 13, 33, 11], [41, 14, 33, 12, "styles"], [41, 20, 33, 18], [41, 21, 33, 19, "container"], [41, 30, 33, 28], [41, 32, 33, 30, "style"], [41, 37, 33, 35], [42, 6, 34, 4], [43, 6, 35, 4], [44, 6, 35, 4], [46, 6, 37, 4, "collapsable"], [46, 17, 37, 15], [46, 19, 37, 17], [46, 24, 37, 22], [47, 6, 38, 4, "children"], [47, 14, 38, 12], [47, 16, 38, 14], [47, 17, 38, 15, "headerShown"], [47, 28, 38, 26], [47, 31, 38, 29], [47, 44, 38, 42], [47, 48, 38, 42, "_jsx"], [47, 63, 38, 46], [47, 65, 38, 47, "NavigationContext"], [47, 90, 38, 64], [47, 91, 38, 65, "Provider"], [47, 99, 38, 73], [47, 101, 38, 75], [48, 8, 39, 6, "value"], [48, 13, 39, 11], [48, 15, 39, 13, "navigation"], [48, 25, 39, 23], [49, 8, 40, 6, "children"], [49, 16, 40, 14], [49, 18, 40, 16], [49, 31, 40, 29], [49, 35, 40, 29, "_jsx"], [49, 50, 40, 33], [49, 52, 40, 34, "NavigationRouteContext"], [49, 82, 40, 56], [49, 83, 40, 57, "Provider"], [49, 91, 40, 65], [49, 93, 40, 67], [50, 10, 41, 8, "value"], [50, 15, 41, 13], [50, 17, 41, 15, "route"], [50, 22, 41, 20], [51, 10, 42, 8, "children"], [51, 18, 42, 16], [51, 20, 42, 18], [51, 33, 42, 31], [51, 37, 42, 31, "_jsx"], [51, 52, 42, 35], [51, 54, 42, 36, "View"], [51, 67, 42, 40], [51, 69, 42, 42], [52, 12, 43, 10, "pointerEvents"], [52, 25, 43, 23], [52, 27, 43, 25], [52, 37, 43, 35], [53, 12, 44, 10, "onLayout"], [53, 20, 44, 18], [53, 22, 44, 20, "e"], [53, 23, 44, 21], [53, 27, 44, 25], [54, 14, 45, 12], [54, 20, 45, 18], [55, 16, 46, 14, "height"], [56, 14, 47, 12], [56, 15, 47, 13], [56, 18, 47, 16, "e"], [56, 19, 47, 17], [56, 20, 47, 18, "nativeEvent"], [56, 31, 47, 29], [56, 32, 47, 30, "layout"], [56, 38, 47, 36], [57, 14, 48, 12, "setHeaderHeight"], [57, 29, 48, 27], [57, 30, 48, 28, "height"], [57, 36, 48, 34], [57, 37, 48, 35], [58, 12, 49, 10], [58, 13, 49, 11], [59, 12, 50, 10, "style"], [59, 17, 50, 15], [59, 19, 50, 17], [59, 20, 50, 18, "styles"], [59, 26, 50, 24], [59, 27, 50, 25, "header"], [59, 33, 50, 31], [59, 35, 50, 33, "headerTransparent"], [59, 52, 50, 50], [59, 55, 50, 53, "styles"], [59, 61, 50, 59], [59, 62, 50, 60, "absolute"], [59, 70, 50, 68], [59, 73, 50, 71], [59, 77, 50, 75], [59, 78, 50, 76], [60, 12, 51, 10, "children"], [60, 20, 51, 18], [60, 22, 51, 20, "header"], [61, 10, 52, 8], [61, 11, 52, 9], [62, 8, 53, 6], [62, 9, 53, 7], [63, 6, 54, 4], [63, 7, 54, 5], [63, 8, 54, 6], [63, 11, 54, 9], [63, 15, 54, 13], [63, 17, 54, 15], [63, 30, 54, 28], [63, 34, 54, 28, "_jsx"], [63, 49, 54, 32], [63, 51, 54, 33, "View"], [63, 64, 54, 37], [63, 66, 54, 39], [64, 8, 55, 6, "style"], [64, 13, 55, 11], [64, 15, 55, 13, "styles"], [64, 21, 55, 19], [64, 22, 55, 20, "content"], [64, 29, 55, 27], [65, 8, 56, 6, "children"], [65, 16, 56, 14], [65, 18, 56, 16], [65, 31, 56, 29], [65, 35, 56, 29, "_jsx"], [65, 50, 56, 33], [65, 52, 56, 34, "HeaderShownContext"], [65, 90, 56, 52], [65, 91, 56, 53, "Provider"], [65, 99, 56, 61], [65, 101, 56, 63], [66, 10, 57, 8, "value"], [66, 15, 57, 13], [66, 17, 57, 15, "isParentHeaderShown"], [66, 36, 57, 34], [66, 40, 57, 38, "headerShown"], [66, 51, 57, 49], [66, 56, 57, 54], [66, 61, 57, 59], [67, 10, 58, 8, "children"], [67, 18, 58, 16], [67, 20, 58, 18], [67, 33, 58, 31], [67, 37, 58, 31, "_jsx"], [67, 52, 58, 35], [67, 54, 58, 36, "HeaderHeightContext"], [67, 94, 58, 55], [67, 95, 58, 56, "Provider"], [67, 103, 58, 64], [67, 105, 58, 66], [68, 12, 59, 10, "value"], [68, 17, 59, 15], [68, 19, 59, 17, "headerShown"], [68, 30, 59, 28], [68, 33, 59, 31, "headerHeight"], [68, 45, 59, 43], [68, 48, 59, 46, "parentHeaderHeight"], [68, 66, 59, 64], [68, 70, 59, 68], [68, 71, 59, 69], [69, 12, 60, 10, "children"], [69, 20, 60, 18], [69, 22, 60, 20, "children"], [70, 10, 61, 8], [70, 11, 61, 9], [71, 8, 62, 6], [71, 9, 62, 7], [72, 6, 63, 4], [72, 7, 63, 5], [72, 8, 63, 6], [73, 4, 64, 2], [73, 5, 64, 3], [73, 6, 64, 4], [74, 2, 65, 0], [75, 2, 66, 0], [75, 8, 66, 6, "styles"], [75, 14, 66, 12], [75, 17, 66, 15, "StyleSheet"], [75, 36, 66, 25], [75, 37, 66, 26, "create"], [75, 43, 66, 32], [75, 44, 66, 33], [76, 4, 67, 2, "container"], [76, 13, 67, 11], [76, 15, 67, 13], [77, 6, 68, 4, "flex"], [77, 10, 68, 8], [77, 12, 68, 10], [78, 4, 69, 2], [78, 5, 69, 3], [79, 4, 70, 2, "content"], [79, 11, 70, 9], [79, 13, 70, 11], [80, 6, 71, 4, "flex"], [80, 10, 71, 8], [80, 12, 71, 10], [81, 4, 72, 2], [81, 5, 72, 3], [82, 4, 73, 2, "header"], [82, 10, 73, 8], [82, 12, 73, 10], [83, 6, 74, 4, "zIndex"], [83, 12, 74, 10], [83, 14, 74, 12], [84, 4, 75, 2], [84, 5, 75, 3], [85, 4, 76, 2, "absolute"], [85, 12, 76, 10], [85, 14, 76, 12], [86, 6, 77, 4, "position"], [86, 14, 77, 12], [86, 16, 77, 14], [86, 26, 77, 24], [87, 6, 78, 4, "top"], [87, 9, 78, 7], [87, 11, 78, 9], [87, 12, 78, 10], [88, 6, 79, 4, "start"], [88, 11, 79, 9], [88, 13, 79, 11], [88, 14, 79, 12], [89, 6, 80, 4, "end"], [89, 9, 80, 7], [89, 11, 80, 9], [90, 4, 81, 2], [91, 2, 82, 0], [91, 3, 82, 1], [91, 4, 82, 2], [92, 0, 82, 3], [92, 3]], "functionMap": {"names": ["<global>", "Screen", "useFrameSize$argument_0", "_jsx$argument_1.onLayout"], "mappings": "AAA;OCY;2CCgB,kED;oBEe;WFK;CDgB"}}, "type": "js/module"}]}