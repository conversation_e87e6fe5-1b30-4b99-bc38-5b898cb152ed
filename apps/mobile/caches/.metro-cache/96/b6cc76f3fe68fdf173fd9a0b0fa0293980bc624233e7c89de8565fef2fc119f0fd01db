{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/NativeModules", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MRft9soElPgDBIQyAww27iefYeU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = ensureNativeModuleAvailable;\n  var _NativeModules = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/NativeModules\"));\n  const NativeIconAPI = _NativeModules.default.RNVectorIconsManager || _NativeModules.default.RNVectorIconsModule;\n  function ensureNativeModuleAvailable() {\n    if (!NativeIconAPI) {\n      throw new Error('The native RNVectorIcons API is not available, did you properly integrate the module? Please verify your autolinking setup and recompile.');\n    }\n  }\n});", "lineCount": 14, "map": [[8, 2, 3, 0], [8, 8, 3, 6, "NativeIconAPI"], [8, 21, 3, 19], [8, 24, 4, 2, "NativeModules"], [8, 46, 4, 15], [8, 47, 4, 16, "RNVectorIconsManager"], [8, 67, 4, 36], [8, 71, 4, 40, "NativeModules"], [8, 93, 4, 53], [8, 94, 4, 54, "RNVectorIconsModule"], [8, 113, 4, 73], [9, 2, 6, 15], [9, 11, 6, 24, "ensureNativeModuleAvailable"], [9, 38, 6, 51, "ensureNativeModuleAvailable"], [9, 39, 6, 51], [9, 41, 6, 54], [10, 4, 7, 2], [10, 8, 7, 6], [10, 9, 7, 7, "NativeIconAPI"], [10, 22, 7, 20], [10, 24, 7, 22], [11, 6, 8, 4], [11, 12, 8, 10], [11, 16, 8, 14, "Error"], [11, 21, 8, 19], [11, 22, 9, 6], [11, 161, 10, 4], [11, 162, 10, 5], [12, 4, 11, 2], [13, 2, 12, 0], [14, 0, 12, 1], [14, 3]], "functionMap": {"names": ["<global>", "ensureNativeModuleAvailable"], "mappings": "AAA;eCK;CDM"}}, "type": "js/module"}]}