{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getLabel = getLabel;\n  function getLabel(options, fallback) {\n    return options.label !== undefined ? options.label : options.title !== undefined ? options.title : fallback;\n  }\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "get<PERSON><PERSON><PERSON>"], [7, 18, 1, 13], [7, 21, 1, 13, "get<PERSON><PERSON><PERSON>"], [7, 29, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "get<PERSON><PERSON><PERSON>"], [8, 19, 3, 24, "get<PERSON><PERSON><PERSON>"], [8, 20, 3, 25, "options"], [8, 27, 3, 32], [8, 29, 3, 34, "fallback"], [8, 37, 3, 42], [8, 39, 3, 44], [9, 4, 4, 2], [9, 11, 4, 9, "options"], [9, 18, 4, 16], [9, 19, 4, 17, "label"], [9, 24, 4, 22], [9, 29, 4, 27, "undefined"], [9, 38, 4, 36], [9, 41, 4, 39, "options"], [9, 48, 4, 46], [9, 49, 4, 47, "label"], [9, 54, 4, 52], [9, 57, 4, 55, "options"], [9, 64, 4, 62], [9, 65, 4, 63, "title"], [9, 70, 4, 68], [9, 75, 4, 73, "undefined"], [9, 84, 4, 82], [9, 87, 4, 85, "options"], [9, 94, 4, 92], [9, 95, 4, 93, "title"], [9, 100, 4, 98], [9, 103, 4, 101, "fallback"], [9, 111, 4, 109], [10, 2, 5, 0], [11, 0, 5, 1], [11, 3]], "functionMap": {"names": ["<global>", "get<PERSON><PERSON><PERSON>"], "mappings": "AAA;OCE;CDE"}}, "type": "js/module"}]}