{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var AlarmClock = exports.default = (0, _createLucideIcon.default)(\"AlarmClock\", [[\"circle\", {\n    cx: \"12\",\n    cy: \"13\",\n    r: \"8\",\n    key: \"3y4lt7\"\n  }], [\"path\", {\n    d: \"M12 9v4l2 2\",\n    key: \"1c63tq\"\n  }], [\"path\", {\n    d: \"M5 3 2 6\",\n    key: \"18tl5t\"\n  }], [\"path\", {\n    d: \"m22 6-3-3\",\n    key: \"1opdir\"\n  }], [\"path\", {\n    d: \"M6.38 18.7 4 21\",\n    key: \"17xu3x\"\n  }], [\"path\", {\n    d: \"M17.64 18.67 20 21\",\n    key: \"kv2oe2\"\n  }]]);\n});", "lineCount": 36, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "AlarmClock"], [15, 16, 10, 16], [15, 19, 10, 16, "exports"], [15, 26, 10, 16], [15, 27, 10, 16, "default"], [15, 34, 10, 16], [15, 37, 10, 19], [15, 41, 10, 19, "createLucideIcon"], [15, 66, 10, 35], [15, 68, 10, 36], [15, 80, 10, 48], [15, 82, 10, 50], [15, 83, 11, 2], [15, 84, 11, 3], [15, 92, 11, 11], [15, 94, 11, 13], [16, 4, 11, 15, "cx"], [16, 6, 11, 17], [16, 8, 11, 19], [16, 12, 11, 23], [17, 4, 11, 25, "cy"], [17, 6, 11, 27], [17, 8, 11, 29], [17, 12, 11, 33], [18, 4, 11, 35, "r"], [18, 5, 11, 36], [18, 7, 11, 38], [18, 10, 11, 41], [19, 4, 11, 43, "key"], [19, 7, 11, 46], [19, 9, 11, 48], [20, 2, 11, 57], [20, 3, 11, 58], [20, 4, 11, 59], [20, 6, 12, 2], [20, 7, 12, 3], [20, 13, 12, 9], [20, 15, 12, 11], [21, 4, 12, 13, "d"], [21, 5, 12, 14], [21, 7, 12, 16], [21, 20, 12, 29], [22, 4, 12, 31, "key"], [22, 7, 12, 34], [22, 9, 12, 36], [23, 2, 12, 45], [23, 3, 12, 46], [23, 4, 12, 47], [23, 6, 13, 2], [23, 7, 13, 3], [23, 13, 13, 9], [23, 15, 13, 11], [24, 4, 13, 13, "d"], [24, 5, 13, 14], [24, 7, 13, 16], [24, 17, 13, 26], [25, 4, 13, 28, "key"], [25, 7, 13, 31], [25, 9, 13, 33], [26, 2, 13, 42], [26, 3, 13, 43], [26, 4, 13, 44], [26, 6, 14, 2], [26, 7, 14, 3], [26, 13, 14, 9], [26, 15, 14, 11], [27, 4, 14, 13, "d"], [27, 5, 14, 14], [27, 7, 14, 16], [27, 18, 14, 27], [28, 4, 14, 29, "key"], [28, 7, 14, 32], [28, 9, 14, 34], [29, 2, 14, 43], [29, 3, 14, 44], [29, 4, 14, 45], [29, 6, 15, 2], [29, 7, 15, 3], [29, 13, 15, 9], [29, 15, 15, 11], [30, 4, 15, 13, "d"], [30, 5, 15, 14], [30, 7, 15, 16], [30, 24, 15, 33], [31, 4, 15, 35, "key"], [31, 7, 15, 38], [31, 9, 15, 40], [32, 2, 15, 49], [32, 3, 15, 50], [32, 4, 15, 51], [32, 6, 16, 2], [32, 7, 16, 3], [32, 13, 16, 9], [32, 15, 16, 11], [33, 4, 16, 13, "d"], [33, 5, 16, 14], [33, 7, 16, 16], [33, 27, 16, 36], [34, 4, 16, 38, "key"], [34, 7, 16, 41], [34, 9, 16, 43], [35, 2, 16, 52], [35, 3, 16, 53], [35, 4, 16, 54], [35, 5, 17, 1], [35, 6, 17, 2], [36, 0, 17, 3], [36, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}