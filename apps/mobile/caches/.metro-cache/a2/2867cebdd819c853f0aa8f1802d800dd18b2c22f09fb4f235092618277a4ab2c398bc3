{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/PixelRatio", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5rdRioKC4qvLVlVTyxLOiQm3IeU=", "exportNames": ["*"]}}, {"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 114}, "end": {"line": 3, "column": 70, "index": 184}}], "key": "DfKH1NNXqDIAaDOtB+YKkBB07j8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.resolveAssetUri = resolveAssetUri;\n  var _PixelRatio = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/PixelRatio\"));\n  var _registry = require(_dependencyMap[2], \"@react-native/assets-registry/registry\");\n  // @ts-expect-error react-native/assets-registry doesn't export types.\n\n  const svgDataUriPattern = /^(data:image\\/svg\\+xml;utf8,)(.*)/;\n\n  // Based on that function: https://github.com/necolas/react-native-web/blob/54c14d64dabd175e8055e1dc92e9196c821f9b7d/packages/react-native-web/src/exports/Image/index.js#L118-L156\n  function resolveAssetUri(source) {\n    let src = {};\n    if (typeof source === 'number') {\n      // get the URI from the packager\n      const asset = (0, _registry.getAssetByID)(source);\n      if (asset == null) {\n        throw new Error(`Image: asset with ID \"${source}\" could not be found. Please check the image source or packager.`);\n      }\n      src = {\n        width: asset.width,\n        height: asset.height,\n        scale: asset.scales[0]\n      };\n      if (asset.scales.length > 1) {\n        const preferredScale = _PixelRatio.default.get();\n        // Get the scale which is closest to the preferred scale\n        src.scale = asset.scales.reduce((prev, curr) => Math.abs(curr - preferredScale) < Math.abs(prev - preferredScale) ? curr : prev);\n      }\n      const scaleSuffix = src.scale !== 1 ? `@${src.scale}x` : '';\n      src.uri = asset ? `${asset.httpServerLocation}/${asset.name}${scaleSuffix}.${asset.type}` : '';\n    } else if (typeof source === 'string') {\n      src.uri = source;\n    } else if (source && !Array.isArray(source) && typeof source.uri === 'string') {\n      src.uri = source.uri;\n    }\n    if (src.uri) {\n      var _src;\n      const match = (_src = src) === null || _src === void 0 || (_src = _src.uri) === null || _src === void 0 ? void 0 : _src.match(svgDataUriPattern);\n      // inline SVG markup may contain characters (e.g., #, \") that need to be escaped\n      if (match) {\n        const [, prefix, svg] = match;\n        const encodedSvg = encodeURIComponent(svg);\n        src.uri = `${prefix}${encodedSvg}`;\n        return src;\n      }\n    }\n    return src;\n  }\n});", "lineCount": 52, "map": [[8, 2, 3, 0], [8, 6, 3, 0, "_registry"], [8, 15, 3, 0], [8, 18, 3, 0, "require"], [8, 25, 3, 0], [8, 26, 3, 0, "_dependencyMap"], [8, 40, 3, 0], [9, 2, 2, 0], [11, 2, 4, 0], [11, 8, 4, 6, "svgDataUriPattern"], [11, 25, 4, 23], [11, 28, 4, 26], [11, 63, 4, 61], [13, 2, 6, 0], [14, 2, 7, 7], [14, 11, 7, 16, "resolveAssetUri"], [14, 26, 7, 31, "resolveAssetUri"], [14, 27, 7, 32, "source"], [14, 33, 7, 38], [14, 35, 7, 40], [15, 4, 8, 2], [15, 8, 8, 6, "src"], [15, 11, 8, 9], [15, 14, 8, 12], [15, 15, 8, 13], [15, 16, 8, 14], [16, 4, 9, 2], [16, 8, 9, 6], [16, 15, 9, 13, "source"], [16, 21, 9, 19], [16, 26, 9, 24], [16, 34, 9, 32], [16, 36, 9, 34], [17, 6, 10, 4], [18, 6, 11, 4], [18, 12, 11, 10, "asset"], [18, 17, 11, 15], [18, 20, 11, 18], [18, 24, 11, 18, "getAssetByID"], [18, 46, 11, 30], [18, 48, 11, 31, "source"], [18, 54, 11, 37], [18, 55, 11, 38], [19, 6, 12, 4], [19, 10, 12, 8, "asset"], [19, 15, 12, 13], [19, 19, 12, 17], [19, 23, 12, 21], [19, 25, 12, 23], [20, 8, 13, 6], [20, 14, 13, 12], [20, 18, 13, 16, "Error"], [20, 23, 13, 21], [20, 24, 13, 22], [20, 49, 13, 47, "source"], [20, 55, 13, 53], [20, 121, 13, 119], [20, 122, 13, 120], [21, 6, 14, 4], [22, 6, 15, 4, "src"], [22, 9, 15, 7], [22, 12, 15, 10], [23, 8, 16, 6, "width"], [23, 13, 16, 11], [23, 15, 16, 13, "asset"], [23, 20, 16, 18], [23, 21, 16, 19, "width"], [23, 26, 16, 24], [24, 8, 17, 6, "height"], [24, 14, 17, 12], [24, 16, 17, 14, "asset"], [24, 21, 17, 19], [24, 22, 17, 20, "height"], [24, 28, 17, 26], [25, 8, 18, 6, "scale"], [25, 13, 18, 11], [25, 15, 18, 13, "asset"], [25, 20, 18, 18], [25, 21, 18, 19, "scales"], [25, 27, 18, 25], [25, 28, 18, 26], [25, 29, 18, 27], [26, 6, 19, 4], [26, 7, 19, 5], [27, 6, 20, 4], [27, 10, 20, 8, "asset"], [27, 15, 20, 13], [27, 16, 20, 14, "scales"], [27, 22, 20, 20], [27, 23, 20, 21, "length"], [27, 29, 20, 27], [27, 32, 20, 30], [27, 33, 20, 31], [27, 35, 20, 33], [28, 8, 21, 6], [28, 14, 21, 12, "preferredScale"], [28, 28, 21, 26], [28, 31, 21, 29, "PixelRatio"], [28, 50, 21, 39], [28, 51, 21, 40, "get"], [28, 54, 21, 43], [28, 55, 21, 44], [28, 56, 21, 45], [29, 8, 22, 6], [30, 8, 23, 6, "src"], [30, 11, 23, 9], [30, 12, 23, 10, "scale"], [30, 17, 23, 15], [30, 20, 23, 18, "asset"], [30, 25, 23, 23], [30, 26, 23, 24, "scales"], [30, 32, 23, 30], [30, 33, 23, 31, "reduce"], [30, 39, 23, 37], [30, 40, 23, 38], [30, 41, 23, 39, "prev"], [30, 45, 23, 43], [30, 47, 23, 45, "curr"], [30, 51, 23, 49], [30, 56, 23, 54, "Math"], [30, 60, 23, 58], [30, 61, 23, 59, "abs"], [30, 64, 23, 62], [30, 65, 23, 63, "curr"], [30, 69, 23, 67], [30, 72, 23, 70, "preferredScale"], [30, 86, 23, 84], [30, 87, 23, 85], [30, 90, 23, 88, "Math"], [30, 94, 23, 92], [30, 95, 23, 93, "abs"], [30, 98, 23, 96], [30, 99, 23, 97, "prev"], [30, 103, 23, 101], [30, 106, 23, 104, "preferredScale"], [30, 120, 23, 118], [30, 121, 23, 119], [30, 124, 23, 122, "curr"], [30, 128, 23, 126], [30, 131, 23, 129, "prev"], [30, 135, 23, 133], [30, 136, 23, 134], [31, 6, 24, 4], [32, 6, 25, 4], [32, 12, 25, 10, "scaleSuffix"], [32, 23, 25, 21], [32, 26, 25, 24, "src"], [32, 29, 25, 27], [32, 30, 25, 28, "scale"], [32, 35, 25, 33], [32, 40, 25, 38], [32, 41, 25, 39], [32, 44, 25, 42], [32, 48, 25, 46, "src"], [32, 51, 25, 49], [32, 52, 25, 50, "scale"], [32, 57, 25, 55], [32, 60, 25, 58], [32, 63, 25, 61], [32, 65, 25, 63], [33, 6, 26, 4, "src"], [33, 9, 26, 7], [33, 10, 26, 8, "uri"], [33, 13, 26, 11], [33, 16, 26, 14, "asset"], [33, 21, 26, 19], [33, 24, 26, 22], [33, 27, 26, 25, "asset"], [33, 32, 26, 30], [33, 33, 26, 31, "httpServerLocation"], [33, 51, 26, 49], [33, 55, 26, 53, "asset"], [33, 60, 26, 58], [33, 61, 26, 59, "name"], [33, 65, 26, 63], [33, 68, 26, 66, "scaleSuffix"], [33, 79, 26, 77], [33, 83, 26, 81, "asset"], [33, 88, 26, 86], [33, 89, 26, 87, "type"], [33, 93, 26, 91], [33, 95, 26, 93], [33, 98, 26, 96], [33, 100, 26, 98], [34, 4, 27, 2], [34, 5, 27, 3], [34, 11, 27, 9], [34, 15, 27, 13], [34, 22, 27, 20, "source"], [34, 28, 27, 26], [34, 33, 27, 31], [34, 41, 27, 39], [34, 43, 27, 41], [35, 6, 28, 4, "src"], [35, 9, 28, 7], [35, 10, 28, 8, "uri"], [35, 13, 28, 11], [35, 16, 28, 14, "source"], [35, 22, 28, 20], [36, 4, 29, 2], [36, 5, 29, 3], [36, 11, 29, 9], [36, 15, 29, 13, "source"], [36, 21, 29, 19], [36, 25, 29, 23], [36, 26, 29, 24, "Array"], [36, 31, 29, 29], [36, 32, 29, 30, "isArray"], [36, 39, 29, 37], [36, 40, 29, 38, "source"], [36, 46, 29, 44], [36, 47, 29, 45], [36, 51, 29, 49], [36, 58, 29, 56, "source"], [36, 64, 29, 62], [36, 65, 29, 63, "uri"], [36, 68, 29, 66], [36, 73, 29, 71], [36, 81, 29, 79], [36, 83, 29, 81], [37, 6, 30, 4, "src"], [37, 9, 30, 7], [37, 10, 30, 8, "uri"], [37, 13, 30, 11], [37, 16, 30, 14, "source"], [37, 22, 30, 20], [37, 23, 30, 21, "uri"], [37, 26, 30, 24], [38, 4, 31, 2], [39, 4, 32, 2], [39, 8, 32, 6, "src"], [39, 11, 32, 9], [39, 12, 32, 10, "uri"], [39, 15, 32, 13], [39, 17, 32, 15], [40, 6, 33, 4], [40, 10, 33, 8, "_src"], [40, 14, 33, 12], [41, 6, 34, 4], [41, 12, 34, 10, "match"], [41, 17, 34, 15], [41, 20, 34, 18], [41, 21, 34, 19, "_src"], [41, 25, 34, 23], [41, 28, 34, 26, "src"], [41, 31, 34, 29], [41, 37, 34, 35], [41, 41, 34, 39], [41, 45, 34, 43, "_src"], [41, 49, 34, 47], [41, 54, 34, 52], [41, 59, 34, 57], [41, 60, 34, 58], [41, 64, 34, 62], [41, 65, 34, 63, "_src"], [41, 69, 34, 67], [41, 72, 34, 70, "_src"], [41, 76, 34, 74], [41, 77, 34, 75, "uri"], [41, 80, 34, 78], [41, 86, 34, 84], [41, 90, 34, 88], [41, 94, 34, 92, "_src"], [41, 98, 34, 96], [41, 103, 34, 101], [41, 108, 34, 106], [41, 109, 34, 107], [41, 112, 34, 110], [41, 117, 34, 115], [41, 118, 34, 116], [41, 121, 34, 119, "_src"], [41, 125, 34, 123], [41, 126, 34, 124, "match"], [41, 131, 34, 129], [41, 132, 34, 130, "svgDataUriPattern"], [41, 149, 34, 147], [41, 150, 34, 148], [42, 6, 35, 4], [43, 6, 36, 4], [43, 10, 36, 8, "match"], [43, 15, 36, 13], [43, 17, 36, 15], [44, 8, 37, 6], [44, 14, 37, 12], [44, 17, 37, 15, "prefix"], [44, 23, 37, 21], [44, 25, 37, 23, "svg"], [44, 28, 37, 26], [44, 29, 37, 27], [44, 32, 37, 30, "match"], [44, 37, 37, 35], [45, 8, 38, 6], [45, 14, 38, 12, "encodedSvg"], [45, 24, 38, 22], [45, 27, 38, 25, "encodeURIComponent"], [45, 45, 38, 43], [45, 46, 38, 44, "svg"], [45, 49, 38, 47], [45, 50, 38, 48], [46, 8, 39, 6, "src"], [46, 11, 39, 9], [46, 12, 39, 10, "uri"], [46, 15, 39, 13], [46, 18, 39, 16], [46, 21, 39, 19, "prefix"], [46, 27, 39, 25], [46, 30, 39, 28, "encodedSvg"], [46, 40, 39, 38], [46, 42, 39, 40], [47, 8, 40, 6], [47, 15, 40, 13, "src"], [47, 18, 40, 16], [48, 6, 41, 4], [49, 4, 42, 2], [50, 4, 43, 2], [50, 11, 43, 9, "src"], [50, 14, 43, 12], [51, 2, 44, 0], [52, 0, 44, 1], [52, 3]], "functionMap": {"names": ["<global>", "resolveAssetUri", "asset.scales.reduce$argument_0"], "mappings": "AAA;OCM;sCCgB,+FD;CDqB"}}, "type": "js/module"}]}