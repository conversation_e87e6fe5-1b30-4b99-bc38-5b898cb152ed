{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const NotebookText = exports.default = (0, _createLucideIcon.default)(\"NotebookText\", [[\"path\", {\n    d: \"M2 6h4\",\n    key: \"aawbzj\"\n  }], [\"path\", {\n    d: \"M2 10h4\",\n    key: \"l0bgd4\"\n  }], [\"path\", {\n    d: \"M2 14h4\",\n    key: \"1gsvsf\"\n  }], [\"path\", {\n    d: \"M2 18h4\",\n    key: \"1bu2t1\"\n  }], [\"rect\", {\n    width: \"16\",\n    height: \"20\",\n    x: \"4\",\n    y: \"2\",\n    rx: \"2\",\n    key: \"1nb95v\"\n  }], [\"path\", {\n    d: \"M9.5 8h5\",\n    key: \"11mslq\"\n  }], [\"path\", {\n    d: \"M9.5 12H16\",\n    key: \"ktog6x\"\n  }], [\"path\", {\n    d: \"M9.5 16H14\",\n    key: \"p1seyn\"\n  }]]);\n});", "lineCount": 44, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "NotebookText"], [15, 20, 10, 18], [15, 23, 10, 18, "exports"], [15, 30, 10, 18], [15, 31, 10, 18, "default"], [15, 38, 10, 18], [15, 41, 10, 21], [15, 45, 10, 21, "createLucideIcon"], [15, 70, 10, 37], [15, 72, 10, 38], [15, 86, 10, 52], [15, 88, 10, 54], [15, 89, 11, 2], [15, 90, 11, 3], [15, 96, 11, 9], [15, 98, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 15, 11, 24], [17, 4, 11, 26, "key"], [17, 7, 11, 29], [17, 9, 11, 31], [18, 2, 11, 40], [18, 3, 11, 41], [18, 4, 11, 42], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 16, 13, 25], [23, 4, 13, 27, "key"], [23, 7, 13, 30], [23, 9, 13, 32], [24, 2, 13, 41], [24, 3, 13, 42], [24, 4, 13, 43], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 16, 14, 25], [26, 4, 14, 27, "key"], [26, 7, 14, 30], [26, 9, 14, 32], [27, 2, 14, 41], [27, 3, 14, 42], [27, 4, 14, 43], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "width"], [28, 9, 15, 18], [28, 11, 15, 20], [28, 15, 15, 24], [29, 4, 15, 26, "height"], [29, 10, 15, 32], [29, 12, 15, 34], [29, 16, 15, 38], [30, 4, 15, 40, "x"], [30, 5, 15, 41], [30, 7, 15, 43], [30, 10, 15, 46], [31, 4, 15, 48, "y"], [31, 5, 15, 49], [31, 7, 15, 51], [31, 10, 15, 54], [32, 4, 15, 56, "rx"], [32, 6, 15, 58], [32, 8, 15, 60], [32, 11, 15, 63], [33, 4, 15, 65, "key"], [33, 7, 15, 68], [33, 9, 15, 70], [34, 2, 15, 79], [34, 3, 15, 80], [34, 4, 15, 81], [34, 6, 16, 2], [34, 7, 16, 3], [34, 13, 16, 9], [34, 15, 16, 11], [35, 4, 16, 13, "d"], [35, 5, 16, 14], [35, 7, 16, 16], [35, 17, 16, 26], [36, 4, 16, 28, "key"], [36, 7, 16, 31], [36, 9, 16, 33], [37, 2, 16, 42], [37, 3, 16, 43], [37, 4, 16, 44], [37, 6, 17, 2], [37, 7, 17, 3], [37, 13, 17, 9], [37, 15, 17, 11], [38, 4, 17, 13, "d"], [38, 5, 17, 14], [38, 7, 17, 16], [38, 19, 17, 28], [39, 4, 17, 30, "key"], [39, 7, 17, 33], [39, 9, 17, 35], [40, 2, 17, 44], [40, 3, 17, 45], [40, 4, 17, 46], [40, 6, 18, 2], [40, 7, 18, 3], [40, 13, 18, 9], [40, 15, 18, 11], [41, 4, 18, 13, "d"], [41, 5, 18, 14], [41, 7, 18, 16], [41, 19, 18, 28], [42, 4, 18, 30, "key"], [42, 7, 18, 33], [42, 9, 18, 35], [43, 2, 18, 44], [43, 3, 18, 45], [43, 4, 18, 46], [43, 5, 19, 1], [43, 6, 19, 2], [44, 0, 19, 3], [44, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}