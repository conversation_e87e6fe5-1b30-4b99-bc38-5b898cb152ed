{"dependencies": [{"name": "./lib/ReactPropTypesSecret", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 27, "index": 226}, "end": {"line": 10, "column": 64, "index": 263}}], "key": "nI7mz6E9mIX/HhF+XrBCbg3+oVQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  'use strict';\n\n  var ReactPropTypesSecret = require(_dependencyMap[0], \"./lib/ReactPropTypesSecret\");\n  function emptyFunction() {}\n  function emptyFunctionWithReset() {}\n  emptyFunctionWithReset.resetWarningCache = emptyFunction;\n  module.exports = function () {\n    function shim(props, propName, componentName, location, propFullName, secret) {\n      if (secret === ReactPropTypesSecret) {\n        // It is still safe when called from React.\n        return;\n      }\n      var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n      err.name = 'Invariant Violation';\n      throw err;\n    }\n    ;\n    shim.isRequired = shim;\n    function getShim() {\n      return shim;\n    }\n    ;\n    // Important!\n    // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n    var ReactPropTypes = {\n      array: shim,\n      bigint: shim,\n      bool: shim,\n      func: shim,\n      number: shim,\n      object: shim,\n      string: shim,\n      symbol: shim,\n      any: shim,\n      arrayOf: getShim,\n      element: shim,\n      elementType: shim,\n      instanceOf: getShim,\n      node: shim,\n      objectOf: getShim,\n      oneOf: getShim,\n      oneOfType: getShim,\n      shape: getShim,\n      exact: getShim,\n      checkPropTypes: emptyFunctionWithReset,\n      resetWarningCache: emptyFunction\n    };\n    ReactPropTypes.PropTypes = ReactPropTypes;\n    return ReactPropTypes;\n  };\n});", "lineCount": 59, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [9, 2, 8, 0], [9, 14, 8, 12], [11, 2, 10, 0], [11, 6, 10, 4, "ReactPropTypesSecret"], [11, 26, 10, 24], [11, 29, 10, 27, "require"], [11, 36, 10, 34], [11, 37, 10, 34, "_dependencyMap"], [11, 51, 10, 34], [11, 84, 10, 63], [11, 85, 10, 64], [12, 2, 12, 0], [12, 11, 12, 9, "emptyFunction"], [12, 24, 12, 22, "emptyFunction"], [12, 25, 12, 22], [12, 27, 12, 25], [12, 28, 12, 26], [13, 2, 13, 0], [13, 11, 13, 9, "emptyFunctionWithReset"], [13, 33, 13, 31, "emptyFunctionWithReset"], [13, 34, 13, 31], [13, 36, 13, 34], [13, 37, 13, 35], [14, 2, 14, 0, "emptyFunctionWithReset"], [14, 24, 14, 22], [14, 25, 14, 23, "resetWarningCache"], [14, 42, 14, 40], [14, 45, 14, 43, "emptyFunction"], [14, 58, 14, 56], [15, 2, 16, 0, "module"], [15, 8, 16, 6], [15, 9, 16, 7, "exports"], [15, 16, 16, 14], [15, 19, 16, 17], [15, 31, 16, 28], [16, 4, 17, 2], [16, 13, 17, 11, "shim"], [16, 17, 17, 15, "shim"], [16, 18, 17, 16, "props"], [16, 23, 17, 21], [16, 25, 17, 23, "propName"], [16, 33, 17, 31], [16, 35, 17, 33, "componentName"], [16, 48, 17, 46], [16, 50, 17, 48, "location"], [16, 58, 17, 56], [16, 60, 17, 58, "prop<PERSON><PERSON><PERSON><PERSON>"], [16, 72, 17, 70], [16, 74, 17, 72, "secret"], [16, 80, 17, 78], [16, 82, 17, 80], [17, 6, 18, 4], [17, 10, 18, 8, "secret"], [17, 16, 18, 14], [17, 21, 18, 19, "ReactPropTypesSecret"], [17, 41, 18, 39], [17, 43, 18, 41], [18, 8, 19, 6], [19, 8, 20, 6], [20, 6, 21, 4], [21, 6, 22, 4], [21, 10, 22, 8, "err"], [21, 13, 22, 11], [21, 16, 22, 14], [21, 20, 22, 18, "Error"], [21, 25, 22, 23], [21, 26, 23, 6], [21, 112, 23, 92], [21, 115, 24, 6], [21, 162, 24, 53], [21, 165, 25, 6], [21, 213, 26, 4], [21, 214, 26, 5], [22, 6, 27, 4, "err"], [22, 9, 27, 7], [22, 10, 27, 8, "name"], [22, 14, 27, 12], [22, 17, 27, 15], [22, 38, 27, 36], [23, 6, 28, 4], [23, 12, 28, 10, "err"], [23, 15, 28, 13], [24, 4, 29, 2], [25, 4, 29, 3], [26, 4, 30, 2, "shim"], [26, 8, 30, 6], [26, 9, 30, 7, "isRequired"], [26, 19, 30, 17], [26, 22, 30, 20, "shim"], [26, 26, 30, 24], [27, 4, 31, 2], [27, 13, 31, 11, "getShim"], [27, 20, 31, 18, "getShim"], [27, 21, 31, 18], [27, 23, 31, 21], [28, 6, 32, 4], [28, 13, 32, 11, "shim"], [28, 17, 32, 15], [29, 4, 33, 2], [30, 4, 33, 3], [31, 4, 34, 2], [32, 4, 35, 2], [33, 4, 36, 2], [33, 8, 36, 6, "ReactPropTypes"], [33, 22, 36, 20], [33, 25, 36, 23], [34, 6, 37, 4, "array"], [34, 11, 37, 9], [34, 13, 37, 11, "shim"], [34, 17, 37, 15], [35, 6, 38, 4, "bigint"], [35, 12, 38, 10], [35, 14, 38, 12, "shim"], [35, 18, 38, 16], [36, 6, 39, 4, "bool"], [36, 10, 39, 8], [36, 12, 39, 10, "shim"], [36, 16, 39, 14], [37, 6, 40, 4, "func"], [37, 10, 40, 8], [37, 12, 40, 10, "shim"], [37, 16, 40, 14], [38, 6, 41, 4, "number"], [38, 12, 41, 10], [38, 14, 41, 12, "shim"], [38, 18, 41, 16], [39, 6, 42, 4, "object"], [39, 12, 42, 10], [39, 14, 42, 12, "shim"], [39, 18, 42, 16], [40, 6, 43, 4, "string"], [40, 12, 43, 10], [40, 14, 43, 12, "shim"], [40, 18, 43, 16], [41, 6, 44, 4, "symbol"], [41, 12, 44, 10], [41, 14, 44, 12, "shim"], [41, 18, 44, 16], [42, 6, 46, 4, "any"], [42, 9, 46, 7], [42, 11, 46, 9, "shim"], [42, 15, 46, 13], [43, 6, 47, 4, "arrayOf"], [43, 13, 47, 11], [43, 15, 47, 13, "getShim"], [43, 22, 47, 20], [44, 6, 48, 4, "element"], [44, 13, 48, 11], [44, 15, 48, 13, "shim"], [44, 19, 48, 17], [45, 6, 49, 4, "elementType"], [45, 17, 49, 15], [45, 19, 49, 17, "shim"], [45, 23, 49, 21], [46, 6, 50, 4, "instanceOf"], [46, 16, 50, 14], [46, 18, 50, 16, "getShim"], [46, 25, 50, 23], [47, 6, 51, 4, "node"], [47, 10, 51, 8], [47, 12, 51, 10, "shim"], [47, 16, 51, 14], [48, 6, 52, 4, "objectOf"], [48, 14, 52, 12], [48, 16, 52, 14, "getShim"], [48, 23, 52, 21], [49, 6, 53, 4, "oneOf"], [49, 11, 53, 9], [49, 13, 53, 11, "getShim"], [49, 20, 53, 18], [50, 6, 54, 4, "oneOfType"], [50, 15, 54, 13], [50, 17, 54, 15, "getShim"], [50, 24, 54, 22], [51, 6, 55, 4, "shape"], [51, 11, 55, 9], [51, 13, 55, 11, "getShim"], [51, 20, 55, 18], [52, 6, 56, 4, "exact"], [52, 11, 56, 9], [52, 13, 56, 11, "getShim"], [52, 20, 56, 18], [53, 6, 58, 4, "checkPropTypes"], [53, 20, 58, 18], [53, 22, 58, 20, "emptyFunctionWithReset"], [53, 44, 58, 42], [54, 6, 59, 4, "resetWarningCache"], [54, 23, 59, 21], [54, 25, 59, 23, "emptyFunction"], [55, 4, 60, 2], [55, 5, 60, 3], [56, 4, 62, 2, "ReactPropTypes"], [56, 18, 62, 16], [56, 19, 62, 17, "PropTypes"], [56, 28, 62, 26], [56, 31, 62, 29, "ReactPropTypes"], [56, 45, 62, 43], [57, 4, 64, 2], [57, 11, 64, 9, "ReactPropTypes"], [57, 25, 64, 23], [58, 2, 65, 0], [58, 3, 65, 1], [59, 0, 65, 2], [59, 3]], "functionMap": {"names": ["<global>", "emptyFunction", "emptyFunctionWithReset", "module.exports", "shim", "getShim"], "mappings": "AAA;ACW,2BD;AEC,oCF;iBGG;ECC;GDY;EEE;GFE;CHgC"}}, "type": "js/module"}]}