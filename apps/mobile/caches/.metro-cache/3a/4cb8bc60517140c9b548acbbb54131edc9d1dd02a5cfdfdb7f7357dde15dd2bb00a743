{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.camelCaseToDashed = void 0;\n  exports.encodeSvg = encodeSvg;\n  exports.getBoundingClientRect = void 0;\n  exports.remeasure = remeasure;\n  const camelCaseToDashed = camelCase => {\n    return camelCase.replace(/[A-Z]/g, m => '-' + m.toLowerCase());\n  };\n  exports.camelCaseToDashed = camelCaseToDashed;\n  const getBoundingClientRect = node => {\n    if (node) {\n      const isElement = node.nodeType === 1; /* Node.ELEMENT_NODE */\n      if (isElement && typeof node.getBoundingClientRect === 'function') {\n        return node.getBoundingClientRect();\n      }\n    }\n    throw new Error('Can not get boundingClientRect of ' + node || 'undefined');\n  };\n  exports.getBoundingClientRect = getBoundingClientRect;\n  const measureLayout = (node, callback) => {\n    const relativeNode = node === null || node === void 0 ? void 0 : node.parentNode;\n    if (relativeNode) {\n      setTimeout(() => {\n        // @ts-expect-error TODO: handle it better\n        const relativeRect = getBoundingClientRect(relativeNode);\n        const {\n          height,\n          left,\n          top,\n          width\n        } = getBoundingClientRect(node);\n        const x = left - relativeRect.left;\n        const y = top - relativeRect.top;\n        callback(x, y, width, height, left, top);\n      }, 0);\n    }\n  };\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  function remeasure() {\n    const tag = this.state.touchable.responderID;\n    if (tag === null) {\n      return;\n    }\n    measureLayout(tag, this._handleQueryLayout);\n  }\n\n  /* Taken from here: https://gist.github.com/jennyknuth/222825e315d45a738ed9d6e04c7a88d0 */\n  function encodeSvg(svgString) {\n    return svgString.replace('<svg', ~svgString.indexOf('xmlns') ? '<svg' : '<svg xmlns=\"http://www.w3.org/2000/svg\"').replace(/\"/g, \"'\").replace(/%/g, '%25').replace(/#/g, '%23').replace(/{/g, '%7B').replace(/}/g, '%7D').replace(/</g, '%3C').replace(/>/g, '%3E').replace(/\\s+/g, ' ');\n  }\n});", "lineCount": 55, "map": [[9, 2, 1, 7], [9, 8, 1, 13, "camelCaseToDashed"], [9, 25, 1, 30], [9, 28, 1, 33, "camelCase"], [9, 37, 1, 42], [9, 41, 1, 46], [10, 4, 2, 2], [10, 11, 2, 9, "camelCase"], [10, 20, 2, 18], [10, 21, 2, 19, "replace"], [10, 28, 2, 26], [10, 29, 2, 27], [10, 37, 2, 35], [10, 39, 2, 37, "m"], [10, 40, 2, 38], [10, 44, 2, 42], [10, 47, 2, 45], [10, 50, 2, 48, "m"], [10, 51, 2, 49], [10, 52, 2, 50, "toLowerCase"], [10, 63, 2, 61], [10, 64, 2, 62], [10, 65, 2, 63], [10, 66, 2, 64], [11, 2, 3, 0], [11, 3, 3, 1], [12, 2, 3, 2, "exports"], [12, 9, 3, 2], [12, 10, 3, 2, "camelCaseToDashed"], [12, 27, 3, 2], [12, 30, 3, 2, "camelCaseToDashed"], [12, 47, 3, 2], [13, 2, 4, 7], [13, 8, 4, 13, "getBoundingClientRect"], [13, 29, 4, 34], [13, 32, 4, 37, "node"], [13, 36, 4, 41], [13, 40, 4, 45], [14, 4, 5, 2], [14, 8, 5, 6, "node"], [14, 12, 5, 10], [14, 14, 5, 12], [15, 6, 6, 4], [15, 12, 6, 10, "isElement"], [15, 21, 6, 19], [15, 24, 6, 22, "node"], [15, 28, 6, 26], [15, 29, 6, 27, "nodeType"], [15, 37, 6, 35], [15, 42, 6, 40], [15, 43, 6, 41], [15, 44, 6, 42], [15, 45, 6, 43], [16, 6, 7, 4], [16, 10, 7, 8, "isElement"], [16, 19, 7, 17], [16, 23, 7, 21], [16, 30, 7, 28, "node"], [16, 34, 7, 32], [16, 35, 7, 33, "getBoundingClientRect"], [16, 56, 7, 54], [16, 61, 7, 59], [16, 71, 7, 69], [16, 73, 7, 71], [17, 8, 8, 6], [17, 15, 8, 13, "node"], [17, 19, 8, 17], [17, 20, 8, 18, "getBoundingClientRect"], [17, 41, 8, 39], [17, 42, 8, 40], [17, 43, 8, 41], [18, 6, 9, 4], [19, 4, 10, 2], [20, 4, 11, 2], [20, 10, 11, 8], [20, 14, 11, 12, "Error"], [20, 19, 11, 17], [20, 20, 11, 18], [20, 56, 11, 54], [20, 59, 11, 57, "node"], [20, 63, 11, 61], [20, 67, 11, 65], [20, 78, 11, 76], [20, 79, 11, 77], [21, 2, 12, 0], [21, 3, 12, 1], [22, 2, 12, 2, "exports"], [22, 9, 12, 2], [22, 10, 12, 2, "getBoundingClientRect"], [22, 31, 12, 2], [22, 34, 12, 2, "getBoundingClientRect"], [22, 55, 12, 2], [23, 2, 13, 0], [23, 8, 13, 6, "measureLayout"], [23, 21, 13, 19], [23, 24, 13, 22, "measureLayout"], [23, 25, 13, 23, "node"], [23, 29, 13, 27], [23, 31, 13, 29, "callback"], [23, 39, 13, 37], [23, 44, 13, 42], [24, 4, 14, 2], [24, 10, 14, 8, "relativeNode"], [24, 22, 14, 20], [24, 25, 14, 23, "node"], [24, 29, 14, 27], [24, 34, 14, 32], [24, 38, 14, 36], [24, 42, 14, 40, "node"], [24, 46, 14, 44], [24, 51, 14, 49], [24, 56, 14, 54], [24, 57, 14, 55], [24, 60, 14, 58], [24, 65, 14, 63], [24, 66, 14, 64], [24, 69, 14, 67, "node"], [24, 73, 14, 71], [24, 74, 14, 72, "parentNode"], [24, 84, 14, 82], [25, 4, 15, 2], [25, 8, 15, 6, "relativeNode"], [25, 20, 15, 18], [25, 22, 15, 20], [26, 6, 16, 4, "setTimeout"], [26, 16, 16, 14], [26, 17, 16, 15], [26, 23, 16, 21], [27, 8, 17, 6], [28, 8, 18, 6], [28, 14, 18, 12, "relativeRect"], [28, 26, 18, 24], [28, 29, 18, 27, "getBoundingClientRect"], [28, 50, 18, 48], [28, 51, 18, 49, "relativeNode"], [28, 63, 18, 61], [28, 64, 18, 62], [29, 8, 19, 6], [29, 14, 19, 12], [30, 10, 20, 8, "height"], [30, 16, 20, 14], [31, 10, 21, 8, "left"], [31, 14, 21, 12], [32, 10, 22, 8, "top"], [32, 13, 22, 11], [33, 10, 23, 8, "width"], [34, 8, 24, 6], [34, 9, 24, 7], [34, 12, 24, 10, "getBoundingClientRect"], [34, 33, 24, 31], [34, 34, 24, 32, "node"], [34, 38, 24, 36], [34, 39, 24, 37], [35, 8, 25, 6], [35, 14, 25, 12, "x"], [35, 15, 25, 13], [35, 18, 25, 16, "left"], [35, 22, 25, 20], [35, 25, 25, 23, "relativeRect"], [35, 37, 25, 35], [35, 38, 25, 36, "left"], [35, 42, 25, 40], [36, 8, 26, 6], [36, 14, 26, 12, "y"], [36, 15, 26, 13], [36, 18, 26, 16, "top"], [36, 21, 26, 19], [36, 24, 26, 22, "relativeRect"], [36, 36, 26, 34], [36, 37, 26, 35, "top"], [36, 40, 26, 38], [37, 8, 27, 6, "callback"], [37, 16, 27, 14], [37, 17, 27, 15, "x"], [37, 18, 27, 16], [37, 20, 27, 18, "y"], [37, 21, 27, 19], [37, 23, 27, 21, "width"], [37, 28, 27, 26], [37, 30, 27, 28, "height"], [37, 36, 27, 34], [37, 38, 27, 36, "left"], [37, 42, 27, 40], [37, 44, 27, 42, "top"], [37, 47, 27, 45], [37, 48, 27, 46], [38, 6, 28, 4], [38, 7, 28, 5], [38, 9, 28, 7], [38, 10, 28, 8], [38, 11, 28, 9], [39, 4, 29, 2], [40, 2, 30, 0], [40, 3, 30, 1], [42, 2, 32, 0], [43, 2, 33, 7], [43, 11, 33, 16, "remeasure"], [43, 20, 33, 25, "remeasure"], [43, 21, 33, 25], [43, 23, 33, 28], [44, 4, 34, 2], [44, 10, 34, 8, "tag"], [44, 13, 34, 11], [44, 16, 34, 14], [44, 20, 34, 18], [44, 21, 34, 19, "state"], [44, 26, 34, 24], [44, 27, 34, 25, "touchable"], [44, 36, 34, 34], [44, 37, 34, 35, "responderID"], [44, 48, 34, 46], [45, 4, 35, 2], [45, 8, 35, 6, "tag"], [45, 11, 35, 9], [45, 16, 35, 14], [45, 20, 35, 18], [45, 22, 35, 20], [46, 6, 36, 4], [47, 4, 37, 2], [48, 4, 38, 2, "measureLayout"], [48, 17, 38, 15], [48, 18, 38, 16, "tag"], [48, 21, 38, 19], [48, 23, 38, 21], [48, 27, 38, 25], [48, 28, 38, 26, "_handleQueryLayout"], [48, 46, 38, 44], [48, 47, 38, 45], [49, 2, 39, 0], [51, 2, 41, 0], [52, 2, 42, 7], [52, 11, 42, 16, "encodeSvg"], [52, 20, 42, 25, "encodeSvg"], [52, 21, 42, 26, "svgString"], [52, 30, 42, 35], [52, 32, 42, 37], [53, 4, 43, 2], [53, 11, 43, 9, "svgString"], [53, 20, 43, 18], [53, 21, 43, 19, "replace"], [53, 28, 43, 26], [53, 29, 43, 27], [53, 35, 43, 33], [53, 37, 43, 35], [53, 38, 43, 36, "svgString"], [53, 47, 43, 45], [53, 48, 43, 46, "indexOf"], [53, 55, 43, 53], [53, 56, 43, 54], [53, 63, 43, 61], [53, 64, 43, 62], [53, 67, 43, 65], [53, 73, 43, 71], [53, 76, 43, 74], [53, 117, 43, 115], [53, 118, 43, 116], [53, 119, 43, 117, "replace"], [53, 126, 43, 124], [53, 127, 43, 125], [53, 131, 43, 129], [53, 133, 43, 131], [53, 136, 43, 134], [53, 137, 43, 135], [53, 138, 43, 136, "replace"], [53, 145, 43, 143], [53, 146, 43, 144], [53, 150, 43, 148], [53, 152, 43, 150], [53, 157, 43, 155], [53, 158, 43, 156], [53, 159, 43, 157, "replace"], [53, 166, 43, 164], [53, 167, 43, 165], [53, 171, 43, 169], [53, 173, 43, 171], [53, 178, 43, 176], [53, 179, 43, 177], [53, 180, 43, 178, "replace"], [53, 187, 43, 185], [53, 188, 43, 186], [53, 192, 43, 190], [53, 194, 43, 192], [53, 199, 43, 197], [53, 200, 43, 198], [53, 201, 43, 199, "replace"], [53, 208, 43, 206], [53, 209, 43, 207], [53, 213, 43, 211], [53, 215, 43, 213], [53, 220, 43, 218], [53, 221, 43, 219], [53, 222, 43, 220, "replace"], [53, 229, 43, 227], [53, 230, 43, 228], [53, 234, 43, 232], [53, 236, 43, 234], [53, 241, 43, 239], [53, 242, 43, 240], [53, 243, 43, 241, "replace"], [53, 250, 43, 248], [53, 251, 43, 249], [53, 255, 43, 253], [53, 257, 43, 255], [53, 262, 43, 260], [53, 263, 43, 261], [53, 264, 43, 262, "replace"], [53, 271, 43, 269], [53, 272, 43, 270], [53, 278, 43, 276], [53, 280, 43, 278], [53, 283, 43, 281], [53, 284, 43, 282], [54, 2, 44, 0], [55, 0, 44, 1], [55, 3]], "functionMap": {"names": ["<global>", "camelCaseToDashed", "camelCase.replace$argument_1", "getBoundingClientRect", "measureLayout", "setTimeout$argument_0", "remeasure", "encodeSvg"], "mappings": "AAA,iCC;qCCC,0BD;CDC;qCGC;CHQ;sBIC;eCG;KDY;CJE;OMG;CNM;OOG;CPE"}}, "type": "js/module"}]}