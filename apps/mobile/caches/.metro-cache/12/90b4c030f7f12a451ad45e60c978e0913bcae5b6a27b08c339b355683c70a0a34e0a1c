{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = addNewValuesOnly;\n  function addIfNew(list, value) {\n    if (list.indexOf(value) === -1) {\n      list.push(value);\n    }\n  }\n  function addNewValuesOnly(list, values) {\n    if (Array.isArray(values)) {\n      for (var i = 0, len = values.length; i < len; ++i) {\n        addIfNew(list, values[i]);\n      }\n    } else {\n      addIfNew(list, values);\n    }\n  }\n});", "lineCount": 22, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "default"], [7, 17, 6, 15], [7, 20, 6, 18, "addNewValuesOnly"], [7, 36, 6, 34], [8, 2, 7, 0], [8, 11, 7, 9, "addIfNew"], [8, 19, 7, 17, "addIfNew"], [8, 20, 7, 18, "list"], [8, 24, 7, 22], [8, 26, 7, 24, "value"], [8, 31, 7, 29], [8, 33, 7, 31], [9, 4, 8, 2], [9, 8, 8, 6, "list"], [9, 12, 8, 10], [9, 13, 8, 11, "indexOf"], [9, 20, 8, 18], [9, 21, 8, 19, "value"], [9, 26, 8, 24], [9, 27, 8, 25], [9, 32, 8, 30], [9, 33, 8, 31], [9, 34, 8, 32], [9, 36, 8, 34], [10, 6, 9, 4, "list"], [10, 10, 9, 8], [10, 11, 9, 9, "push"], [10, 15, 9, 13], [10, 16, 9, 14, "value"], [10, 21, 9, 19], [10, 22, 9, 20], [11, 4, 10, 2], [12, 2, 11, 0], [13, 2, 13, 0], [13, 11, 13, 9, "addNewValuesOnly"], [13, 27, 13, 25, "addNewValuesOnly"], [13, 28, 13, 26, "list"], [13, 32, 13, 30], [13, 34, 13, 32, "values"], [13, 40, 13, 38], [13, 42, 13, 40], [14, 4, 14, 2], [14, 8, 14, 6, "Array"], [14, 13, 14, 11], [14, 14, 14, 12, "isArray"], [14, 21, 14, 19], [14, 22, 14, 20, "values"], [14, 28, 14, 26], [14, 29, 14, 27], [14, 31, 14, 29], [15, 6, 15, 4], [15, 11, 15, 9], [15, 15, 15, 13, "i"], [15, 16, 15, 14], [15, 19, 15, 17], [15, 20, 15, 18], [15, 22, 15, 20, "len"], [15, 25, 15, 23], [15, 28, 15, 26, "values"], [15, 34, 15, 32], [15, 35, 15, 33, "length"], [15, 41, 15, 39], [15, 43, 15, 41, "i"], [15, 44, 15, 42], [15, 47, 15, 45, "len"], [15, 50, 15, 48], [15, 52, 15, 50], [15, 54, 15, 52, "i"], [15, 55, 15, 53], [15, 57, 15, 55], [16, 8, 16, 6, "addIfNew"], [16, 16, 16, 14], [16, 17, 16, 15, "list"], [16, 21, 16, 19], [16, 23, 16, 21, "values"], [16, 29, 16, 27], [16, 30, 16, 28, "i"], [16, 31, 16, 29], [16, 32, 16, 30], [16, 33, 16, 31], [17, 6, 17, 4], [18, 4, 18, 2], [18, 5, 18, 3], [18, 11, 18, 9], [19, 6, 19, 4, "addIfNew"], [19, 14, 19, 12], [19, 15, 19, 13, "list"], [19, 19, 19, 17], [19, 21, 19, 19, "values"], [19, 27, 19, 25], [19, 28, 19, 26], [20, 4, 20, 2], [21, 2, 21, 0], [22, 0, 21, 1], [22, 3]], "functionMap": {"names": ["<global>", "addIfNew", "addNewValuesOnly"], "mappings": "AAA;ACM;CDI;AEE"}}, "type": "js/module"}]}