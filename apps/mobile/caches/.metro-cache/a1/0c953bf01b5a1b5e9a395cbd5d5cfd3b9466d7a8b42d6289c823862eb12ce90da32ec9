{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const SaudiRiyal = exports.default = (0, _createLucideIcon.default)(\"SaudiRiyal\", [[\"path\", {\n    d: \"m20 19.5-5.5 1.2\",\n    key: \"1aenhr\"\n  }], [\"path\", {\n    d: \"M14.5 4v11.22a1 1 0 0 0 1.242.97L20 15.2\",\n    key: \"2rtezt\"\n  }], [\"path\", {\n    d: \"m2.978 19.351 5.549-1.363A2 2 0 0 0 10 16V2\",\n    key: \"1kbm92\"\n  }], [\"path\", {\n    d: \"M20 10 4 13.5\",\n    key: \"8nums9\"\n  }]]);\n});", "lineCount": 28, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "SaudiRiyal"], [15, 18, 10, 16], [15, 21, 10, 16, "exports"], [15, 28, 10, 16], [15, 29, 10, 16, "default"], [15, 36, 10, 16], [15, 39, 10, 19], [15, 43, 10, 19, "createLucideIcon"], [15, 68, 10, 35], [15, 70, 10, 36], [15, 82, 10, 48], [15, 84, 10, 50], [15, 85, 11, 2], [15, 86, 11, 3], [15, 92, 11, 9], [15, 94, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 25, 11, 34], [17, 4, 11, 36, "key"], [17, 7, 11, 39], [17, 9, 11, 41], [18, 2, 11, 50], [18, 3, 11, 51], [18, 4, 11, 52], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 49, 12, 58], [20, 4, 12, 60, "key"], [20, 7, 12, 63], [20, 9, 12, 65], [21, 2, 12, 74], [21, 3, 12, 75], [21, 4, 12, 76], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 52, 13, 61], [23, 4, 13, 63, "key"], [23, 7, 13, 66], [23, 9, 13, 68], [24, 2, 13, 77], [24, 3, 13, 78], [24, 4, 13, 79], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 22, 14, 31], [26, 4, 14, 33, "key"], [26, 7, 14, 36], [26, 9, 14, 38], [27, 2, 14, 47], [27, 3, 14, 48], [27, 4, 14, 49], [27, 5, 15, 1], [27, 6, 15, 2], [28, 0, 15, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}