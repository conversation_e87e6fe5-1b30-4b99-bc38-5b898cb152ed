{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const SquareSquare = exports.default = (0, _createLucideIcon.default)(\"SquareSquare\", [[\"rect\", {\n    x: \"3\",\n    y: \"3\",\n    width: \"18\",\n    height: \"18\",\n    rx: \"2\",\n    key: \"h1oib\"\n  }], [\"rect\", {\n    x: \"8\",\n    y: \"8\",\n    width: \"8\",\n    height: \"8\",\n    rx: \"1\",\n    key: \"z9xiuo\"\n  }]]);\n});", "lineCount": 30, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "SquareSquare"], [15, 20, 10, 18], [15, 23, 10, 18, "exports"], [15, 30, 10, 18], [15, 31, 10, 18, "default"], [15, 38, 10, 18], [15, 41, 10, 21], [15, 45, 10, 21, "createLucideIcon"], [15, 70, 10, 37], [15, 72, 10, 38], [15, 86, 10, 52], [15, 88, 10, 54], [15, 89, 11, 2], [15, 90, 11, 3], [15, 96, 11, 9], [15, 98, 11, 11], [16, 4, 11, 13, "x"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 10, 11, 19], [17, 4, 11, 21, "y"], [17, 5, 11, 22], [17, 7, 11, 24], [17, 10, 11, 27], [18, 4, 11, 29, "width"], [18, 9, 11, 34], [18, 11, 11, 36], [18, 15, 11, 40], [19, 4, 11, 42, "height"], [19, 10, 11, 48], [19, 12, 11, 50], [19, 16, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "key"], [21, 7, 11, 68], [21, 9, 11, 70], [22, 2, 11, 78], [22, 3, 11, 79], [22, 4, 11, 80], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [22, 15, 12, 11], [23, 4, 12, 13, "x"], [23, 5, 12, 14], [23, 7, 12, 16], [23, 10, 12, 19], [24, 4, 12, 21, "y"], [24, 5, 12, 22], [24, 7, 12, 24], [24, 10, 12, 27], [25, 4, 12, 29, "width"], [25, 9, 12, 34], [25, 11, 12, 36], [25, 14, 12, 39], [26, 4, 12, 41, "height"], [26, 10, 12, 47], [26, 12, 12, 49], [26, 15, 12, 52], [27, 4, 12, 54, "rx"], [27, 6, 12, 56], [27, 8, 12, 58], [27, 11, 12, 61], [28, 4, 12, 63, "key"], [28, 7, 12, 66], [28, 9, 12, 68], [29, 2, 12, 77], [29, 3, 12, 78], [29, 4, 12, 79], [29, 5, 13, 1], [29, 6, 13, 2], [30, 0, 13, 3], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}