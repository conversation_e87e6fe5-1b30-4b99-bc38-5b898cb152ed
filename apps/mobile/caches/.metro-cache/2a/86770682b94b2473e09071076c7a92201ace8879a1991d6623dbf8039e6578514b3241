{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function emptyFunction() {}\n  var BackHandler = {\n    exitApp: emptyFunction,\n    addEventListener(_eventName, _handler) {\n      return {\n        remove: emptyFunction\n      };\n    }\n  };\n  var _default = exports.default = BackHandler;\n});", "lineCount": 16, "map": [[6, 2, 14, 0], [6, 11, 14, 9, "emptyFunction"], [6, 24, 14, 22, "emptyFunction"], [6, 25, 14, 22], [6, 27, 14, 31], [6, 28, 14, 32], [7, 2, 24, 0], [7, 6, 24, 6, "<PERSON><PERSON><PERSON><PERSON>"], [7, 17, 24, 31], [7, 20, 24, 34], [8, 4, 25, 2, "exitApp"], [8, 11, 25, 9], [8, 13, 25, 11, "emptyFunction"], [8, 26, 25, 24], [9, 4, 26, 2, "addEventListener"], [9, 20, 26, 18, "addEventListener"], [9, 21, 26, 19, "_eventName"], [9, 31, 26, 49], [9, 33, 26, 51, "_handler"], [9, 41, 26, 77], [9, 43, 26, 79], [10, 6, 27, 4], [10, 13, 27, 11], [11, 8, 28, 6, "remove"], [11, 14, 28, 12], [11, 16, 28, 14, "emptyFunction"], [12, 6, 29, 4], [12, 7, 29, 5], [13, 4, 30, 2], [14, 2, 31, 0], [14, 3, 31, 1], [15, 2, 31, 2], [15, 6, 31, 2, "_default"], [15, 14, 31, 2], [15, 17, 31, 2, "exports"], [15, 24, 31, 2], [15, 25, 31, 2, "default"], [15, 32, 31, 2], [15, 35, 33, 15, "<PERSON><PERSON><PERSON><PERSON>"], [15, 46, 33, 26], [16, 0, 33, 26], [16, 3]], "functionMap": {"names": ["<global>", "emptyFunction", "addEventListener"], "mappings": "AAA;ACa,iCD;EEY;GFI"}}, "type": "js/module"}]}