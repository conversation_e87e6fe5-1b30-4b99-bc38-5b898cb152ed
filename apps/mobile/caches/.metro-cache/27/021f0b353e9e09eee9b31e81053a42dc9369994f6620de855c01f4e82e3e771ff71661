{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 31, "index": 266}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 267}, "end": {"line": 12, "column": 39, "index": 306}}], "key": "Pz10tXyA/z/1zTYUTTxDDbnOtjE=", "exportNames": ["*"]}}, {"name": "../createElement", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 307}, "end": {"line": 13, "column": 45, "index": 352}}], "key": "a/6mvAbqab8PE8fNO0smlzNgt84=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"../StyleSheet\"));\n  var _createElement = _interopRequireDefault(require(_dependencyMap[3], \"../createElement\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var ANIMATION_DURATION = 300;\n  function getAnimationStyle(animationType, visible) {\n    if (animationType === 'slide') {\n      return visible ? animatedSlideInStyles : animatedSlideOutStyles;\n    }\n    if (animationType === 'fade') {\n      return visible ? animatedFadeInStyles : animatedFadeOutStyles;\n    }\n    return visible ? styles.container : styles.hidden;\n  }\n  function ModalAnimation(props) {\n    var animationType = props.animationType,\n      children = props.children,\n      onDismiss = props.onDismiss,\n      onShow = props.onShow,\n      visible = props.visible;\n    var _React$useState = React.useState(false),\n      isRendering = _React$useState[0],\n      setIsRendering = _React$useState[1];\n    var wasVisible = React.useRef(false);\n    var wasRendering = React.useRef(false);\n    var isAnimated = animationType && animationType !== 'none';\n    var animationEndCallback = React.useCallback(e => {\n      if (e && e.currentTarget !== e.target) {\n        // If the event was generated for something NOT this element we\n        // should ignore it as it's not relevant to us\n        return;\n      }\n      if (visible) {\n        if (onShow) {\n          onShow();\n        }\n      } else {\n        setIsRendering(false);\n      }\n    }, [onShow, visible]);\n    React.useEffect(() => {\n      if (wasRendering.current && !isRendering && onDismiss) {\n        onDismiss();\n      }\n      wasRendering.current = isRendering;\n    }, [isRendering, onDismiss]);\n    React.useEffect(() => {\n      if (visible) {\n        setIsRendering(true);\n      }\n      if (visible !== wasVisible.current && !isAnimated) {\n        // Manually call `animationEndCallback` if no animation is used\n        animationEndCallback();\n      }\n      wasVisible.current = visible;\n    }, [isAnimated, visible, animationEndCallback]);\n    return isRendering || visible ? (0, _createElement.default)('div', {\n      style: isRendering ? getAnimationStyle(animationType, visible) : styles.hidden,\n      onAnimationEnd: animationEndCallback,\n      children\n    }) : null;\n  }\n  var styles = _StyleSheet.default.create({\n    container: {\n      position: 'fixed',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      zIndex: 9999\n    },\n    animatedIn: {\n      animationDuration: ANIMATION_DURATION + \"ms\",\n      animationTimingFunction: 'ease-in'\n    },\n    animatedOut: {\n      pointerEvents: 'none',\n      animationDuration: ANIMATION_DURATION + \"ms\",\n      animationTimingFunction: 'ease-out'\n    },\n    fadeIn: {\n      opacity: 1,\n      animationKeyframes: {\n        '0%': {\n          opacity: 0\n        },\n        '100%': {\n          opacity: 1\n        }\n      }\n    },\n    fadeOut: {\n      opacity: 0,\n      animationKeyframes: {\n        '0%': {\n          opacity: 1\n        },\n        '100%': {\n          opacity: 0\n        }\n      }\n    },\n    slideIn: {\n      transform: 'translateY(0%)',\n      animationKeyframes: {\n        '0%': {\n          transform: 'translateY(100%)'\n        },\n        '100%': {\n          transform: 'translateY(0%)'\n        }\n      }\n    },\n    slideOut: {\n      transform: 'translateY(100%)',\n      animationKeyframes: {\n        '0%': {\n          transform: 'translateY(0%)'\n        },\n        '100%': {\n          transform: 'translateY(100%)'\n        }\n      }\n    },\n    hidden: {\n      opacity: 0\n    }\n  });\n  var animatedSlideInStyles = [styles.container, styles.animatedIn, styles.slideIn];\n  var animatedSlideOutStyles = [styles.container, styles.animatedOut, styles.slideOut];\n  var animatedFadeInStyles = [styles.container, styles.animatedIn, styles.fadeIn];\n  var animatedFadeOutStyles = [styles.container, styles.animatedOut, styles.fadeOut];\n  var _default = exports.default = ModalAnimation;\n});", "lineCount": 150, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "React"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireWildcard"], [7, 37, 11, 0], [7, 38, 11, 0, "require"], [7, 45, 11, 0], [7, 46, 11, 0, "_dependencyMap"], [7, 60, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_StyleSheet"], [8, 17, 12, 0], [8, 20, 12, 0, "_interopRequireDefault"], [8, 42, 12, 0], [8, 43, 12, 0, "require"], [8, 50, 12, 0], [8, 51, 12, 0, "_dependencyMap"], [8, 65, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_createElement"], [9, 20, 13, 0], [9, 23, 13, 0, "_interopRequireDefault"], [9, 45, 13, 0], [9, 46, 13, 0, "require"], [9, 53, 13, 0], [9, 54, 13, 0, "_dependencyMap"], [9, 68, 13, 0], [10, 2, 13, 45], [10, 11, 13, 45, "_interopRequireWildcard"], [10, 35, 13, 45, "e"], [10, 36, 13, 45], [10, 38, 13, 45, "t"], [10, 39, 13, 45], [10, 68, 13, 45, "WeakMap"], [10, 75, 13, 45], [10, 81, 13, 45, "r"], [10, 82, 13, 45], [10, 89, 13, 45, "WeakMap"], [10, 96, 13, 45], [10, 100, 13, 45, "n"], [10, 101, 13, 45], [10, 108, 13, 45, "WeakMap"], [10, 115, 13, 45], [10, 127, 13, 45, "_interopRequireWildcard"], [10, 150, 13, 45], [10, 162, 13, 45, "_interopRequireWildcard"], [10, 163, 13, 45, "e"], [10, 164, 13, 45], [10, 166, 13, 45, "t"], [10, 167, 13, 45], [10, 176, 13, 45, "t"], [10, 177, 13, 45], [10, 181, 13, 45, "e"], [10, 182, 13, 45], [10, 186, 13, 45, "e"], [10, 187, 13, 45], [10, 188, 13, 45, "__esModule"], [10, 198, 13, 45], [10, 207, 13, 45, "e"], [10, 208, 13, 45], [10, 214, 13, 45, "o"], [10, 215, 13, 45], [10, 217, 13, 45, "i"], [10, 218, 13, 45], [10, 220, 13, 45, "f"], [10, 221, 13, 45], [10, 226, 13, 45, "__proto__"], [10, 235, 13, 45], [10, 243, 13, 45, "default"], [10, 250, 13, 45], [10, 252, 13, 45, "e"], [10, 253, 13, 45], [10, 270, 13, 45, "e"], [10, 271, 13, 45], [10, 294, 13, 45, "e"], [10, 295, 13, 45], [10, 320, 13, 45, "e"], [10, 321, 13, 45], [10, 330, 13, 45, "f"], [10, 331, 13, 45], [10, 337, 13, 45, "o"], [10, 338, 13, 45], [10, 341, 13, 45, "t"], [10, 342, 13, 45], [10, 345, 13, 45, "n"], [10, 346, 13, 45], [10, 349, 13, 45, "r"], [10, 350, 13, 45], [10, 358, 13, 45, "o"], [10, 359, 13, 45], [10, 360, 13, 45, "has"], [10, 363, 13, 45], [10, 364, 13, 45, "e"], [10, 365, 13, 45], [10, 375, 13, 45, "o"], [10, 376, 13, 45], [10, 377, 13, 45, "get"], [10, 380, 13, 45], [10, 381, 13, 45, "e"], [10, 382, 13, 45], [10, 385, 13, 45, "o"], [10, 386, 13, 45], [10, 387, 13, 45, "set"], [10, 390, 13, 45], [10, 391, 13, 45, "e"], [10, 392, 13, 45], [10, 394, 13, 45, "f"], [10, 395, 13, 45], [10, 411, 13, 45, "t"], [10, 412, 13, 45], [10, 416, 13, 45, "e"], [10, 417, 13, 45], [10, 433, 13, 45, "t"], [10, 434, 13, 45], [10, 441, 13, 45, "hasOwnProperty"], [10, 455, 13, 45], [10, 456, 13, 45, "call"], [10, 460, 13, 45], [10, 461, 13, 45, "e"], [10, 462, 13, 45], [10, 464, 13, 45, "t"], [10, 465, 13, 45], [10, 472, 13, 45, "i"], [10, 473, 13, 45], [10, 477, 13, 45, "o"], [10, 478, 13, 45], [10, 481, 13, 45, "Object"], [10, 487, 13, 45], [10, 488, 13, 45, "defineProperty"], [10, 502, 13, 45], [10, 507, 13, 45, "Object"], [10, 513, 13, 45], [10, 514, 13, 45, "getOwnPropertyDescriptor"], [10, 538, 13, 45], [10, 539, 13, 45, "e"], [10, 540, 13, 45], [10, 542, 13, 45, "t"], [10, 543, 13, 45], [10, 550, 13, 45, "i"], [10, 551, 13, 45], [10, 552, 13, 45, "get"], [10, 555, 13, 45], [10, 559, 13, 45, "i"], [10, 560, 13, 45], [10, 561, 13, 45, "set"], [10, 564, 13, 45], [10, 568, 13, 45, "o"], [10, 569, 13, 45], [10, 570, 13, 45, "f"], [10, 571, 13, 45], [10, 573, 13, 45, "t"], [10, 574, 13, 45], [10, 576, 13, 45, "i"], [10, 577, 13, 45], [10, 581, 13, 45, "f"], [10, 582, 13, 45], [10, 583, 13, 45, "t"], [10, 584, 13, 45], [10, 588, 13, 45, "e"], [10, 589, 13, 45], [10, 590, 13, 45, "t"], [10, 591, 13, 45], [10, 602, 13, 45, "f"], [10, 603, 13, 45], [10, 608, 13, 45, "e"], [10, 609, 13, 45], [10, 611, 13, 45, "t"], [10, 612, 13, 45], [11, 2, 1, 0], [12, 0, 2, 0], [13, 0, 3, 0], [14, 0, 4, 0], [15, 0, 5, 0], [16, 0, 6, 0], [17, 0, 7, 0], [18, 0, 8, 0], [19, 0, 9, 0], [21, 2, 14, 0], [21, 6, 14, 4, "ANIMATION_DURATION"], [21, 24, 14, 22], [21, 27, 14, 25], [21, 30, 14, 28], [22, 2, 15, 0], [22, 11, 15, 9, "getAnimationStyle"], [22, 28, 15, 26, "getAnimationStyle"], [22, 29, 15, 27, "animationType"], [22, 42, 15, 40], [22, 44, 15, 42, "visible"], [22, 51, 15, 49], [22, 53, 15, 51], [23, 4, 16, 2], [23, 8, 16, 6, "animationType"], [23, 21, 16, 19], [23, 26, 16, 24], [23, 33, 16, 31], [23, 35, 16, 33], [24, 6, 17, 4], [24, 13, 17, 11, "visible"], [24, 20, 17, 18], [24, 23, 17, 21, "animatedSlideInStyles"], [24, 44, 17, 42], [24, 47, 17, 45, "animatedSlideOutStyles"], [24, 69, 17, 67], [25, 4, 18, 2], [26, 4, 19, 2], [26, 8, 19, 6, "animationType"], [26, 21, 19, 19], [26, 26, 19, 24], [26, 32, 19, 30], [26, 34, 19, 32], [27, 6, 20, 4], [27, 13, 20, 11, "visible"], [27, 20, 20, 18], [27, 23, 20, 21, "animatedFadeInStyles"], [27, 43, 20, 41], [27, 46, 20, 44, "animatedFadeOutStyles"], [27, 67, 20, 65], [28, 4, 21, 2], [29, 4, 22, 2], [29, 11, 22, 9, "visible"], [29, 18, 22, 16], [29, 21, 22, 19, "styles"], [29, 27, 22, 25], [29, 28, 22, 26, "container"], [29, 37, 22, 35], [29, 40, 22, 38, "styles"], [29, 46, 22, 44], [29, 47, 22, 45, "hidden"], [29, 53, 22, 51], [30, 2, 23, 0], [31, 2, 24, 0], [31, 11, 24, 9, "ModalAnimation"], [31, 25, 24, 23, "ModalAnimation"], [31, 26, 24, 24, "props"], [31, 31, 24, 29], [31, 33, 24, 31], [32, 4, 25, 2], [32, 8, 25, 6, "animationType"], [32, 21, 25, 19], [32, 24, 25, 22, "props"], [32, 29, 25, 27], [32, 30, 25, 28, "animationType"], [32, 43, 25, 41], [33, 6, 26, 4, "children"], [33, 14, 26, 12], [33, 17, 26, 15, "props"], [33, 22, 26, 20], [33, 23, 26, 21, "children"], [33, 31, 26, 29], [34, 6, 27, 4, "on<PERSON><PERSON><PERSON>"], [34, 15, 27, 13], [34, 18, 27, 16, "props"], [34, 23, 27, 21], [34, 24, 27, 22, "on<PERSON><PERSON><PERSON>"], [34, 33, 27, 31], [35, 6, 28, 4, "onShow"], [35, 12, 28, 10], [35, 15, 28, 13, "props"], [35, 20, 28, 18], [35, 21, 28, 19, "onShow"], [35, 27, 28, 25], [36, 6, 29, 4, "visible"], [36, 13, 29, 11], [36, 16, 29, 14, "props"], [36, 21, 29, 19], [36, 22, 29, 20, "visible"], [36, 29, 29, 27], [37, 4, 30, 2], [37, 8, 30, 6, "_React$useState"], [37, 23, 30, 21], [37, 26, 30, 24, "React"], [37, 31, 30, 29], [37, 32, 30, 30, "useState"], [37, 40, 30, 38], [37, 41, 30, 39], [37, 46, 30, 44], [37, 47, 30, 45], [38, 6, 31, 4, "isRendering"], [38, 17, 31, 15], [38, 20, 31, 18, "_React$useState"], [38, 35, 31, 33], [38, 36, 31, 34], [38, 37, 31, 35], [38, 38, 31, 36], [39, 6, 32, 4, "setIsRendering"], [39, 20, 32, 18], [39, 23, 32, 21, "_React$useState"], [39, 38, 32, 36], [39, 39, 32, 37], [39, 40, 32, 38], [39, 41, 32, 39], [40, 4, 33, 2], [40, 8, 33, 6, "wasVisible"], [40, 18, 33, 16], [40, 21, 33, 19, "React"], [40, 26, 33, 24], [40, 27, 33, 25, "useRef"], [40, 33, 33, 31], [40, 34, 33, 32], [40, 39, 33, 37], [40, 40, 33, 38], [41, 4, 34, 2], [41, 8, 34, 6, "wasRendering"], [41, 20, 34, 18], [41, 23, 34, 21, "React"], [41, 28, 34, 26], [41, 29, 34, 27, "useRef"], [41, 35, 34, 33], [41, 36, 34, 34], [41, 41, 34, 39], [41, 42, 34, 40], [42, 4, 35, 2], [42, 8, 35, 6, "isAnimated"], [42, 18, 35, 16], [42, 21, 35, 19, "animationType"], [42, 34, 35, 32], [42, 38, 35, 36, "animationType"], [42, 51, 35, 49], [42, 56, 35, 54], [42, 62, 35, 60], [43, 4, 36, 2], [43, 8, 36, 6, "animationEndCallback"], [43, 28, 36, 26], [43, 31, 36, 29, "React"], [43, 36, 36, 34], [43, 37, 36, 35, "useCallback"], [43, 48, 36, 46], [43, 49, 36, 47, "e"], [43, 50, 36, 48], [43, 54, 36, 52], [44, 6, 37, 4], [44, 10, 37, 8, "e"], [44, 11, 37, 9], [44, 15, 37, 13, "e"], [44, 16, 37, 14], [44, 17, 37, 15, "currentTarget"], [44, 30, 37, 28], [44, 35, 37, 33, "e"], [44, 36, 37, 34], [44, 37, 37, 35, "target"], [44, 43, 37, 41], [44, 45, 37, 43], [45, 8, 38, 6], [46, 8, 39, 6], [47, 8, 40, 6], [48, 6, 41, 4], [49, 6, 42, 4], [49, 10, 42, 8, "visible"], [49, 17, 42, 15], [49, 19, 42, 17], [50, 8, 43, 6], [50, 12, 43, 10, "onShow"], [50, 18, 43, 16], [50, 20, 43, 18], [51, 10, 44, 8, "onShow"], [51, 16, 44, 14], [51, 17, 44, 15], [51, 18, 44, 16], [52, 8, 45, 6], [53, 6, 46, 4], [53, 7, 46, 5], [53, 13, 46, 11], [54, 8, 47, 6, "setIsRendering"], [54, 22, 47, 20], [54, 23, 47, 21], [54, 28, 47, 26], [54, 29, 47, 27], [55, 6, 48, 4], [56, 4, 49, 2], [56, 5, 49, 3], [56, 7, 49, 5], [56, 8, 49, 6, "onShow"], [56, 14, 49, 12], [56, 16, 49, 14, "visible"], [56, 23, 49, 21], [56, 24, 49, 22], [56, 25, 49, 23], [57, 4, 50, 2, "React"], [57, 9, 50, 7], [57, 10, 50, 8, "useEffect"], [57, 19, 50, 17], [57, 20, 50, 18], [57, 26, 50, 24], [58, 6, 51, 4], [58, 10, 51, 8, "wasRendering"], [58, 22, 51, 20], [58, 23, 51, 21, "current"], [58, 30, 51, 28], [58, 34, 51, 32], [58, 35, 51, 33, "isRendering"], [58, 46, 51, 44], [58, 50, 51, 48, "on<PERSON><PERSON><PERSON>"], [58, 59, 51, 57], [58, 61, 51, 59], [59, 8, 52, 6, "on<PERSON><PERSON><PERSON>"], [59, 17, 52, 15], [59, 18, 52, 16], [59, 19, 52, 17], [60, 6, 53, 4], [61, 6, 54, 4, "wasRendering"], [61, 18, 54, 16], [61, 19, 54, 17, "current"], [61, 26, 54, 24], [61, 29, 54, 27, "isRendering"], [61, 40, 54, 38], [62, 4, 55, 2], [62, 5, 55, 3], [62, 7, 55, 5], [62, 8, 55, 6, "isRendering"], [62, 19, 55, 17], [62, 21, 55, 19, "on<PERSON><PERSON><PERSON>"], [62, 30, 55, 28], [62, 31, 55, 29], [62, 32, 55, 30], [63, 4, 56, 2, "React"], [63, 9, 56, 7], [63, 10, 56, 8, "useEffect"], [63, 19, 56, 17], [63, 20, 56, 18], [63, 26, 56, 24], [64, 6, 57, 4], [64, 10, 57, 8, "visible"], [64, 17, 57, 15], [64, 19, 57, 17], [65, 8, 58, 6, "setIsRendering"], [65, 22, 58, 20], [65, 23, 58, 21], [65, 27, 58, 25], [65, 28, 58, 26], [66, 6, 59, 4], [67, 6, 60, 4], [67, 10, 60, 8, "visible"], [67, 17, 60, 15], [67, 22, 60, 20, "wasVisible"], [67, 32, 60, 30], [67, 33, 60, 31, "current"], [67, 40, 60, 38], [67, 44, 60, 42], [67, 45, 60, 43, "isAnimated"], [67, 55, 60, 53], [67, 57, 60, 55], [68, 8, 61, 6], [69, 8, 62, 6, "animationEndCallback"], [69, 28, 62, 26], [69, 29, 62, 27], [69, 30, 62, 28], [70, 6, 63, 4], [71, 6, 64, 4, "wasVisible"], [71, 16, 64, 14], [71, 17, 64, 15, "current"], [71, 24, 64, 22], [71, 27, 64, 25, "visible"], [71, 34, 64, 32], [72, 4, 65, 2], [72, 5, 65, 3], [72, 7, 65, 5], [72, 8, 65, 6, "isAnimated"], [72, 18, 65, 16], [72, 20, 65, 18, "visible"], [72, 27, 65, 25], [72, 29, 65, 27, "animationEndCallback"], [72, 49, 65, 47], [72, 50, 65, 48], [72, 51, 65, 49], [73, 4, 66, 2], [73, 11, 66, 9, "isRendering"], [73, 22, 66, 20], [73, 26, 66, 24, "visible"], [73, 33, 66, 31], [73, 36, 66, 34], [73, 40, 66, 34, "createElement"], [73, 62, 66, 47], [73, 64, 66, 48], [73, 69, 66, 53], [73, 71, 66, 55], [74, 6, 67, 4, "style"], [74, 11, 67, 9], [74, 13, 67, 11, "isRendering"], [74, 24, 67, 22], [74, 27, 67, 25, "getAnimationStyle"], [74, 44, 67, 42], [74, 45, 67, 43, "animationType"], [74, 58, 67, 56], [74, 60, 67, 58, "visible"], [74, 67, 67, 65], [74, 68, 67, 66], [74, 71, 67, 69, "styles"], [74, 77, 67, 75], [74, 78, 67, 76, "hidden"], [74, 84, 67, 82], [75, 6, 68, 4, "onAnimationEnd"], [75, 20, 68, 18], [75, 22, 68, 20, "animationEndCallback"], [75, 42, 68, 40], [76, 6, 69, 4, "children"], [77, 4, 70, 2], [77, 5, 70, 3], [77, 6, 70, 4], [77, 9, 70, 7], [77, 13, 70, 11], [78, 2, 71, 0], [79, 2, 72, 0], [79, 6, 72, 4, "styles"], [79, 12, 72, 10], [79, 15, 72, 13, "StyleSheet"], [79, 34, 72, 23], [79, 35, 72, 24, "create"], [79, 41, 72, 30], [79, 42, 72, 31], [80, 4, 73, 2, "container"], [80, 13, 73, 11], [80, 15, 73, 13], [81, 6, 74, 4, "position"], [81, 14, 74, 12], [81, 16, 74, 14], [81, 23, 74, 21], [82, 6, 75, 4, "top"], [82, 9, 75, 7], [82, 11, 75, 9], [82, 12, 75, 10], [83, 6, 76, 4, "right"], [83, 11, 76, 9], [83, 13, 76, 11], [83, 14, 76, 12], [84, 6, 77, 4, "bottom"], [84, 12, 77, 10], [84, 14, 77, 12], [84, 15, 77, 13], [85, 6, 78, 4, "left"], [85, 10, 78, 8], [85, 12, 78, 10], [85, 13, 78, 11], [86, 6, 79, 4, "zIndex"], [86, 12, 79, 10], [86, 14, 79, 12], [87, 4, 80, 2], [87, 5, 80, 3], [88, 4, 81, 2, "animatedIn"], [88, 14, 81, 12], [88, 16, 81, 14], [89, 6, 82, 4, "animationDuration"], [89, 23, 82, 21], [89, 25, 82, 23, "ANIMATION_DURATION"], [89, 43, 82, 41], [89, 46, 82, 44], [89, 50, 82, 48], [90, 6, 83, 4, "animationTimingFunction"], [90, 29, 83, 27], [90, 31, 83, 29], [91, 4, 84, 2], [91, 5, 84, 3], [92, 4, 85, 2, "animatedOut"], [92, 15, 85, 13], [92, 17, 85, 15], [93, 6, 86, 4, "pointerEvents"], [93, 19, 86, 17], [93, 21, 86, 19], [93, 27, 86, 25], [94, 6, 87, 4, "animationDuration"], [94, 23, 87, 21], [94, 25, 87, 23, "ANIMATION_DURATION"], [94, 43, 87, 41], [94, 46, 87, 44], [94, 50, 87, 48], [95, 6, 88, 4, "animationTimingFunction"], [95, 29, 88, 27], [95, 31, 88, 29], [96, 4, 89, 2], [96, 5, 89, 3], [97, 4, 90, 2, "fadeIn"], [97, 10, 90, 8], [97, 12, 90, 10], [98, 6, 91, 4, "opacity"], [98, 13, 91, 11], [98, 15, 91, 13], [98, 16, 91, 14], [99, 6, 92, 4, "animationKeyframes"], [99, 24, 92, 22], [99, 26, 92, 24], [100, 8, 93, 6], [100, 12, 93, 10], [100, 14, 93, 12], [101, 10, 94, 8, "opacity"], [101, 17, 94, 15], [101, 19, 94, 17], [102, 8, 95, 6], [102, 9, 95, 7], [103, 8, 96, 6], [103, 14, 96, 12], [103, 16, 96, 14], [104, 10, 97, 8, "opacity"], [104, 17, 97, 15], [104, 19, 97, 17], [105, 8, 98, 6], [106, 6, 99, 4], [107, 4, 100, 2], [107, 5, 100, 3], [108, 4, 101, 2, "fadeOut"], [108, 11, 101, 9], [108, 13, 101, 11], [109, 6, 102, 4, "opacity"], [109, 13, 102, 11], [109, 15, 102, 13], [109, 16, 102, 14], [110, 6, 103, 4, "animationKeyframes"], [110, 24, 103, 22], [110, 26, 103, 24], [111, 8, 104, 6], [111, 12, 104, 10], [111, 14, 104, 12], [112, 10, 105, 8, "opacity"], [112, 17, 105, 15], [112, 19, 105, 17], [113, 8, 106, 6], [113, 9, 106, 7], [114, 8, 107, 6], [114, 14, 107, 12], [114, 16, 107, 14], [115, 10, 108, 8, "opacity"], [115, 17, 108, 15], [115, 19, 108, 17], [116, 8, 109, 6], [117, 6, 110, 4], [118, 4, 111, 2], [118, 5, 111, 3], [119, 4, 112, 2, "slideIn"], [119, 11, 112, 9], [119, 13, 112, 11], [120, 6, 113, 4, "transform"], [120, 15, 113, 13], [120, 17, 113, 15], [120, 33, 113, 31], [121, 6, 114, 4, "animationKeyframes"], [121, 24, 114, 22], [121, 26, 114, 24], [122, 8, 115, 6], [122, 12, 115, 10], [122, 14, 115, 12], [123, 10, 116, 8, "transform"], [123, 19, 116, 17], [123, 21, 116, 19], [124, 8, 117, 6], [124, 9, 117, 7], [125, 8, 118, 6], [125, 14, 118, 12], [125, 16, 118, 14], [126, 10, 119, 8, "transform"], [126, 19, 119, 17], [126, 21, 119, 19], [127, 8, 120, 6], [128, 6, 121, 4], [129, 4, 122, 2], [129, 5, 122, 3], [130, 4, 123, 2, "slideOut"], [130, 12, 123, 10], [130, 14, 123, 12], [131, 6, 124, 4, "transform"], [131, 15, 124, 13], [131, 17, 124, 15], [131, 35, 124, 33], [132, 6, 125, 4, "animationKeyframes"], [132, 24, 125, 22], [132, 26, 125, 24], [133, 8, 126, 6], [133, 12, 126, 10], [133, 14, 126, 12], [134, 10, 127, 8, "transform"], [134, 19, 127, 17], [134, 21, 127, 19], [135, 8, 128, 6], [135, 9, 128, 7], [136, 8, 129, 6], [136, 14, 129, 12], [136, 16, 129, 14], [137, 10, 130, 8, "transform"], [137, 19, 130, 17], [137, 21, 130, 19], [138, 8, 131, 6], [139, 6, 132, 4], [140, 4, 133, 2], [140, 5, 133, 3], [141, 4, 134, 2, "hidden"], [141, 10, 134, 8], [141, 12, 134, 10], [142, 6, 135, 4, "opacity"], [142, 13, 135, 11], [142, 15, 135, 13], [143, 4, 136, 2], [144, 2, 137, 0], [144, 3, 137, 1], [144, 4, 137, 2], [145, 2, 138, 0], [145, 6, 138, 4, "animatedSlideInStyles"], [145, 27, 138, 25], [145, 30, 138, 28], [145, 31, 138, 29, "styles"], [145, 37, 138, 35], [145, 38, 138, 36, "container"], [145, 47, 138, 45], [145, 49, 138, 47, "styles"], [145, 55, 138, 53], [145, 56, 138, 54, "animatedIn"], [145, 66, 138, 64], [145, 68, 138, 66, "styles"], [145, 74, 138, 72], [145, 75, 138, 73, "slideIn"], [145, 82, 138, 80], [145, 83, 138, 81], [146, 2, 139, 0], [146, 6, 139, 4, "animatedSlideOutStyles"], [146, 28, 139, 26], [146, 31, 139, 29], [146, 32, 139, 30, "styles"], [146, 38, 139, 36], [146, 39, 139, 37, "container"], [146, 48, 139, 46], [146, 50, 139, 48, "styles"], [146, 56, 139, 54], [146, 57, 139, 55, "animatedOut"], [146, 68, 139, 66], [146, 70, 139, 68, "styles"], [146, 76, 139, 74], [146, 77, 139, 75, "slideOut"], [146, 85, 139, 83], [146, 86, 139, 84], [147, 2, 140, 0], [147, 6, 140, 4, "animatedFadeInStyles"], [147, 26, 140, 24], [147, 29, 140, 27], [147, 30, 140, 28, "styles"], [147, 36, 140, 34], [147, 37, 140, 35, "container"], [147, 46, 140, 44], [147, 48, 140, 46, "styles"], [147, 54, 140, 52], [147, 55, 140, 53, "animatedIn"], [147, 65, 140, 63], [147, 67, 140, 65, "styles"], [147, 73, 140, 71], [147, 74, 140, 72, "fadeIn"], [147, 80, 140, 78], [147, 81, 140, 79], [148, 2, 141, 0], [148, 6, 141, 4, "animatedFadeOutStyles"], [148, 27, 141, 25], [148, 30, 141, 28], [148, 31, 141, 29, "styles"], [148, 37, 141, 35], [148, 38, 141, 36, "container"], [148, 47, 141, 45], [148, 49, 141, 47, "styles"], [148, 55, 141, 53], [148, 56, 141, 54, "animatedOut"], [148, 67, 141, 65], [148, 69, 141, 67, "styles"], [148, 75, 141, 73], [148, 76, 141, 74, "fadeOut"], [148, 83, 141, 81], [148, 84, 141, 82], [149, 2, 141, 83], [149, 6, 141, 83, "_default"], [149, 14, 141, 83], [149, 17, 141, 83, "exports"], [149, 24, 141, 83], [149, 25, 141, 83, "default"], [149, 32, 141, 83], [149, 35, 142, 15, "ModalAnimation"], [149, 49, 142, 29], [150, 0, 142, 29], [150, 3]], "functionMap": {"names": ["<global>", "getAnimationStyle", "ModalAnimation", "animationEndCallback", "React.useEffect$argument_0"], "mappings": "AAA;ACc;CDQ;AEC;+CCY;GDa;kBEC;GFK;kBEC;GFS;CFM"}}, "type": "js/module"}]}