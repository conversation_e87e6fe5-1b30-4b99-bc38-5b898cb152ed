{"dependencies": [{"name": "@tanstack/query-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 42}, "end": {"line": 4, "column": 61, "index": 103}}], "key": "GAsr4MDTe9ve1mRxgvML4iY2BZg=", "exportNames": ["*"]}}, {"name": "./useBaseQuery.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 104}, "end": {"line": 5, "column": 49, "index": 153}}], "key": "B/WlyzBImhiAFsm4X6W26jtrxRI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use client\";\n\n  // src/useInfiniteQuery.ts\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useInfiniteQuery = useInfiniteQuery;\n  var _queryCore = require(_dependencyMap[0], \"@tanstack/query-core\");\n  var _useBaseQuery = require(_dependencyMap[1], \"./useBaseQuery.js\");\n  function useInfiniteQuery(options, queryClient) {\n    return (0, _useBaseQuery.useBaseQuery)(options, _queryCore.InfiniteQueryObserver, queryClient);\n  }\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 0], [5, 9, 3, 0, "defineProperty"], [5, 23, 3, 0], [5, 24, 3, 0, "exports"], [5, 31, 3, 0], [6, 4, 3, 0, "value"], [6, 9, 3, 0], [7, 2, 3, 0], [8, 2, 3, 0, "exports"], [8, 9, 3, 0], [8, 10, 3, 0, "useInfiniteQuery"], [8, 26, 3, 0], [8, 29, 3, 0, "useInfiniteQuery"], [8, 45, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_queryCore"], [9, 16, 4, 0], [9, 19, 4, 0, "require"], [9, 26, 4, 0], [9, 27, 4, 0, "_dependencyMap"], [9, 41, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_useBaseQuery"], [10, 19, 5, 0], [10, 22, 5, 0, "require"], [10, 29, 5, 0], [10, 30, 5, 0, "_dependencyMap"], [10, 44, 5, 0], [11, 2, 6, 0], [11, 11, 6, 9, "useInfiniteQuery"], [11, 27, 6, 25, "useInfiniteQuery"], [11, 28, 6, 26, "options"], [11, 35, 6, 33], [11, 37, 6, 35, "queryClient"], [11, 48, 6, 46], [11, 50, 6, 48], [12, 4, 7, 2], [12, 11, 7, 9], [12, 15, 7, 9, "useBaseQuery"], [12, 41, 7, 21], [12, 43, 8, 4, "options"], [12, 50, 8, 11], [12, 52, 9, 4, "InfiniteQueryObserver"], [12, 84, 9, 25], [12, 86, 10, 4, "queryClient"], [12, 97, 11, 2], [12, 98, 11, 3], [13, 2, 12, 0], [14, 0, 12, 1], [14, 3]], "functionMap": {"names": ["<global>", "useInfiniteQuery"], "mappings": "AAA;ACK;CDM"}}, "type": "js/module"}]}