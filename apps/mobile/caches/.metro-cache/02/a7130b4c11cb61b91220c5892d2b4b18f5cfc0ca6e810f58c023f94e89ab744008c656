{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/Animated/shouldUseTurboAnimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 103}}], "key": "A7s8VomG95Nce3yRj9Pbur02G5g=", "exportNames": ["*"]}}, {"name": "../../../../Libraries/TurboModule/TurboModuleRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 93}}], "key": "H+9Pk6sLVUPsBv6YXnwcNYMfH5g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _shouldUseTurboAnimatedModule = _interopRequireDefault(require(_dependencyMap[1], \"../../../../Libraries/Animated/shouldUseTurboAnimatedModule\"));\n  var TurboModuleRegistry = _interopRequireWildcard(require(_dependencyMap[2], \"../../../../Libraries/TurboModule/TurboModuleRegistry\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var NativeModule = !(0, _shouldUseTurboAnimatedModule.default)() ? TurboModuleRegistry.get('NativeAnimatedModule') : null;\n  var _default = exports.default = NativeModule;\n});", "lineCount": 12, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_shouldUseTurboAnimatedModule"], [7, 35, 13, 0], [7, 38, 13, 0, "_interopRequireDefault"], [7, 60, 13, 0], [7, 61, 13, 0, "require"], [7, 68, 13, 0], [7, 69, 13, 0, "_dependencyMap"], [7, 83, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "TurboModuleRegistry"], [8, 25, 14, 0], [8, 28, 14, 0, "_interopRequireWildcard"], [8, 51, 14, 0], [8, 52, 14, 0, "require"], [8, 59, 14, 0], [8, 60, 14, 0, "_dependencyMap"], [8, 74, 14, 0], [9, 2, 14, 93], [9, 11, 14, 93, "_interopRequireWildcard"], [9, 35, 14, 93, "e"], [9, 36, 14, 93], [9, 38, 14, 93, "t"], [9, 39, 14, 93], [9, 68, 14, 93, "WeakMap"], [9, 75, 14, 93], [9, 81, 14, 93, "r"], [9, 82, 14, 93], [9, 89, 14, 93, "WeakMap"], [9, 96, 14, 93], [9, 100, 14, 93, "n"], [9, 101, 14, 93], [9, 108, 14, 93, "WeakMap"], [9, 115, 14, 93], [9, 127, 14, 93, "_interopRequireWildcard"], [9, 150, 14, 93], [9, 162, 14, 93, "_interopRequireWildcard"], [9, 163, 14, 93, "e"], [9, 164, 14, 93], [9, 166, 14, 93, "t"], [9, 167, 14, 93], [9, 176, 14, 93, "t"], [9, 177, 14, 93], [9, 181, 14, 93, "e"], [9, 182, 14, 93], [9, 186, 14, 93, "e"], [9, 187, 14, 93], [9, 188, 14, 93, "__esModule"], [9, 198, 14, 93], [9, 207, 14, 93, "e"], [9, 208, 14, 93], [9, 214, 14, 93, "o"], [9, 215, 14, 93], [9, 217, 14, 93, "i"], [9, 218, 14, 93], [9, 220, 14, 93, "f"], [9, 221, 14, 93], [9, 226, 14, 93, "__proto__"], [9, 235, 14, 93], [9, 243, 14, 93, "default"], [9, 250, 14, 93], [9, 252, 14, 93, "e"], [9, 253, 14, 93], [9, 270, 14, 93, "e"], [9, 271, 14, 93], [9, 294, 14, 93, "e"], [9, 295, 14, 93], [9, 320, 14, 93, "e"], [9, 321, 14, 93], [9, 330, 14, 93, "f"], [9, 331, 14, 93], [9, 337, 14, 93, "o"], [9, 338, 14, 93], [9, 341, 14, 93, "t"], [9, 342, 14, 93], [9, 345, 14, 93, "n"], [9, 346, 14, 93], [9, 349, 14, 93, "r"], [9, 350, 14, 93], [9, 358, 14, 93, "o"], [9, 359, 14, 93], [9, 360, 14, 93, "has"], [9, 363, 14, 93], [9, 364, 14, 93, "e"], [9, 365, 14, 93], [9, 375, 14, 93, "o"], [9, 376, 14, 93], [9, 377, 14, 93, "get"], [9, 380, 14, 93], [9, 381, 14, 93, "e"], [9, 382, 14, 93], [9, 385, 14, 93, "o"], [9, 386, 14, 93], [9, 387, 14, 93, "set"], [9, 390, 14, 93], [9, 391, 14, 93, "e"], [9, 392, 14, 93], [9, 394, 14, 93, "f"], [9, 395, 14, 93], [9, 409, 14, 93, "_t"], [9, 411, 14, 93], [9, 415, 14, 93, "e"], [9, 416, 14, 93], [9, 432, 14, 93, "_t"], [9, 434, 14, 93], [9, 441, 14, 93, "hasOwnProperty"], [9, 455, 14, 93], [9, 456, 14, 93, "call"], [9, 460, 14, 93], [9, 461, 14, 93, "e"], [9, 462, 14, 93], [9, 464, 14, 93, "_t"], [9, 466, 14, 93], [9, 473, 14, 93, "i"], [9, 474, 14, 93], [9, 478, 14, 93, "o"], [9, 479, 14, 93], [9, 482, 14, 93, "Object"], [9, 488, 14, 93], [9, 489, 14, 93, "defineProperty"], [9, 503, 14, 93], [9, 508, 14, 93, "Object"], [9, 514, 14, 93], [9, 515, 14, 93, "getOwnPropertyDescriptor"], [9, 539, 14, 93], [9, 540, 14, 93, "e"], [9, 541, 14, 93], [9, 543, 14, 93, "_t"], [9, 545, 14, 93], [9, 552, 14, 93, "i"], [9, 553, 14, 93], [9, 554, 14, 93, "get"], [9, 557, 14, 93], [9, 561, 14, 93, "i"], [9, 562, 14, 93], [9, 563, 14, 93, "set"], [9, 566, 14, 93], [9, 570, 14, 93, "o"], [9, 571, 14, 93], [9, 572, 14, 93, "f"], [9, 573, 14, 93], [9, 575, 14, 93, "_t"], [9, 577, 14, 93], [9, 579, 14, 93, "i"], [9, 580, 14, 93], [9, 584, 14, 93, "f"], [9, 585, 14, 93], [9, 586, 14, 93, "_t"], [9, 588, 14, 93], [9, 592, 14, 93, "e"], [9, 593, 14, 93], [9, 594, 14, 93, "_t"], [9, 596, 14, 93], [9, 607, 14, 93, "f"], [9, 608, 14, 93], [9, 613, 14, 93, "e"], [9, 614, 14, 93], [9, 616, 14, 93, "t"], [9, 617, 14, 93], [10, 2, 74, 0], [10, 6, 74, 6, "NativeModule"], [10, 18, 74, 25], [10, 21, 74, 28], [10, 22, 74, 29], [10, 26, 74, 29, "shouldUseTurboAnimatedModule"], [10, 63, 74, 57], [10, 65, 74, 58], [10, 66, 74, 59], [10, 69, 75, 4, "TurboModuleRegistry"], [10, 88, 75, 23], [10, 89, 75, 24, "get"], [10, 92, 75, 27], [10, 93, 75, 34], [10, 115, 75, 56], [10, 116, 75, 57], [10, 119, 76, 4], [10, 123, 76, 8], [11, 2, 76, 9], [11, 6, 76, 9, "_default"], [11, 14, 76, 9], [11, 17, 76, 9, "exports"], [11, 24, 76, 9], [11, 25, 76, 9, "default"], [11, 32, 76, 9], [11, 35, 77, 15, "NativeModule"], [11, 47, 77, 27], [12, 0, 77, 27], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}