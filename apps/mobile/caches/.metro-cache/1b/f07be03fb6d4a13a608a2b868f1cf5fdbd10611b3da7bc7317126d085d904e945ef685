{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "../View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 32}}], "key": "shwEdrNunlxo+53+9BUO15YMxuY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./RCTSafeAreaViewNativeComponent", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 50}}], "key": "8J5V7MtoEJGbCa7TI8cCxMWBgUE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../../Utilities/Platform\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../View/View\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var exported = _Platform.default.select({\n    ios: require(_dependencyMap[4], \"./RCTSafeAreaViewNativeComponent\").default,\n    default: _View.default\n  });\n  var _default = exports.default = exported;\n});", "lineCount": 16, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_Platform"], [7, 15, 13, 0], [7, 18, 13, 0, "_interopRequireDefault"], [7, 40, 13, 0], [7, 41, 13, 0, "require"], [7, 48, 13, 0], [7, 49, 13, 0, "_dependencyMap"], [7, 63, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_View"], [8, 11, 14, 0], [8, 14, 14, 0, "_interopRequireDefault"], [8, 36, 14, 0], [8, 37, 14, 0, "require"], [8, 44, 14, 0], [8, 45, 14, 0, "_dependencyMap"], [8, 59, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "React"], [9, 11, 15, 0], [9, 14, 15, 0, "_interopRequireWildcard"], [9, 37, 15, 0], [9, 38, 15, 0, "require"], [9, 45, 15, 0], [9, 46, 15, 0, "_dependencyMap"], [9, 60, 15, 0], [10, 2, 15, 31], [10, 11, 15, 31, "_interopRequireWildcard"], [10, 35, 15, 31, "e"], [10, 36, 15, 31], [10, 38, 15, 31, "t"], [10, 39, 15, 31], [10, 68, 15, 31, "WeakMap"], [10, 75, 15, 31], [10, 81, 15, 31, "r"], [10, 82, 15, 31], [10, 89, 15, 31, "WeakMap"], [10, 96, 15, 31], [10, 100, 15, 31, "n"], [10, 101, 15, 31], [10, 108, 15, 31, "WeakMap"], [10, 115, 15, 31], [10, 127, 15, 31, "_interopRequireWildcard"], [10, 150, 15, 31], [10, 162, 15, 31, "_interopRequireWildcard"], [10, 163, 15, 31, "e"], [10, 164, 15, 31], [10, 166, 15, 31, "t"], [10, 167, 15, 31], [10, 176, 15, 31, "t"], [10, 177, 15, 31], [10, 181, 15, 31, "e"], [10, 182, 15, 31], [10, 186, 15, 31, "e"], [10, 187, 15, 31], [10, 188, 15, 31, "__esModule"], [10, 198, 15, 31], [10, 207, 15, 31, "e"], [10, 208, 15, 31], [10, 214, 15, 31, "o"], [10, 215, 15, 31], [10, 217, 15, 31, "i"], [10, 218, 15, 31], [10, 220, 15, 31, "f"], [10, 221, 15, 31], [10, 226, 15, 31, "__proto__"], [10, 235, 15, 31], [10, 243, 15, 31, "default"], [10, 250, 15, 31], [10, 252, 15, 31, "e"], [10, 253, 15, 31], [10, 270, 15, 31, "e"], [10, 271, 15, 31], [10, 294, 15, 31, "e"], [10, 295, 15, 31], [10, 320, 15, 31, "e"], [10, 321, 15, 31], [10, 330, 15, 31, "f"], [10, 331, 15, 31], [10, 337, 15, 31, "o"], [10, 338, 15, 31], [10, 341, 15, 31, "t"], [10, 342, 15, 31], [10, 345, 15, 31, "n"], [10, 346, 15, 31], [10, 349, 15, 31, "r"], [10, 350, 15, 31], [10, 358, 15, 31, "o"], [10, 359, 15, 31], [10, 360, 15, 31, "has"], [10, 363, 15, 31], [10, 364, 15, 31, "e"], [10, 365, 15, 31], [10, 375, 15, 31, "o"], [10, 376, 15, 31], [10, 377, 15, 31, "get"], [10, 380, 15, 31], [10, 381, 15, 31, "e"], [10, 382, 15, 31], [10, 385, 15, 31, "o"], [10, 386, 15, 31], [10, 387, 15, 31, "set"], [10, 390, 15, 31], [10, 391, 15, 31, "e"], [10, 392, 15, 31], [10, 394, 15, 31, "f"], [10, 395, 15, 31], [10, 409, 15, 31, "_t"], [10, 411, 15, 31], [10, 415, 15, 31, "e"], [10, 416, 15, 31], [10, 432, 15, 31, "_t"], [10, 434, 15, 31], [10, 441, 15, 31, "hasOwnProperty"], [10, 455, 15, 31], [10, 456, 15, 31, "call"], [10, 460, 15, 31], [10, 461, 15, 31, "e"], [10, 462, 15, 31], [10, 464, 15, 31, "_t"], [10, 466, 15, 31], [10, 473, 15, 31, "i"], [10, 474, 15, 31], [10, 478, 15, 31, "o"], [10, 479, 15, 31], [10, 482, 15, 31, "Object"], [10, 488, 15, 31], [10, 489, 15, 31, "defineProperty"], [10, 503, 15, 31], [10, 508, 15, 31, "Object"], [10, 514, 15, 31], [10, 515, 15, 31, "getOwnPropertyDescriptor"], [10, 539, 15, 31], [10, 540, 15, 31, "e"], [10, 541, 15, 31], [10, 543, 15, 31, "_t"], [10, 545, 15, 31], [10, 552, 15, 31, "i"], [10, 553, 15, 31], [10, 554, 15, 31, "get"], [10, 557, 15, 31], [10, 561, 15, 31, "i"], [10, 562, 15, 31], [10, 563, 15, 31, "set"], [10, 566, 15, 31], [10, 570, 15, 31, "o"], [10, 571, 15, 31], [10, 572, 15, 31, "f"], [10, 573, 15, 31], [10, 575, 15, 31, "_t"], [10, 577, 15, 31], [10, 579, 15, 31, "i"], [10, 580, 15, 31], [10, 584, 15, 31, "f"], [10, 585, 15, 31], [10, 586, 15, 31, "_t"], [10, 588, 15, 31], [10, 592, 15, 31, "e"], [10, 593, 15, 31], [10, 594, 15, 31, "_t"], [10, 596, 15, 31], [10, 607, 15, 31, "f"], [10, 608, 15, 31], [10, 613, 15, 31, "e"], [10, 614, 15, 31], [10, 616, 15, 31, "t"], [10, 617, 15, 31], [11, 2, 26, 0], [11, 6, 26, 6, "exported"], [11, 14, 29, 1], [11, 17, 29, 4, "Platform"], [11, 34, 29, 12], [11, 35, 29, 13, "select"], [11, 41, 29, 19], [11, 42, 29, 20], [12, 4, 30, 2, "ios"], [12, 7, 30, 5], [12, 9, 30, 7, "require"], [12, 16, 30, 14], [12, 17, 30, 14, "_dependencyMap"], [12, 31, 30, 14], [12, 70, 30, 49], [12, 71, 30, 50], [12, 72, 30, 51, "default"], [12, 79, 30, 58], [13, 4, 31, 2, "default"], [13, 11, 31, 9], [13, 13, 31, 11, "View"], [14, 2, 32, 0], [14, 3, 32, 1], [14, 4, 32, 2], [15, 2, 32, 3], [15, 6, 32, 3, "_default"], [15, 14, 32, 3], [15, 17, 32, 3, "exports"], [15, 24, 32, 3], [15, 25, 32, 3, "default"], [15, 32, 32, 3], [15, 35, 34, 15, "exported"], [15, 43, 34, 23], [16, 0, 34, 23], [16, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}