{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Slack = exports.default = (0, _createLucideIcon.default)(\"Slack\", [[\"rect\", {\n    width: \"3\",\n    height: \"8\",\n    x: \"13\",\n    y: \"2\",\n    rx: \"1.5\",\n    key: \"diqz80\"\n  }], [\"path\", {\n    d: \"M19 8.5V10h1.5A1.5 1.5 0 1 0 19 8.5\",\n    key: \"183iwg\"\n  }], [\"rect\", {\n    width: \"3\",\n    height: \"8\",\n    x: \"8\",\n    y: \"14\",\n    rx: \"1.5\",\n    key: \"hqg7r1\"\n  }], [\"path\", {\n    d: \"M5 15.5V14H3.5A1.5 1.5 0 1 0 5 15.5\",\n    key: \"76g71w\"\n  }], [\"rect\", {\n    width: \"8\",\n    height: \"3\",\n    x: \"14\",\n    y: \"13\",\n    rx: \"1.5\",\n    key: \"1kmz0a\"\n  }], [\"path\", {\n    d: \"M15.5 19H14v1.5a1.5 1.5 0 1 0 1.5-1.5\",\n    key: \"jc4sz0\"\n  }], [\"rect\", {\n    width: \"8\",\n    height: \"3\",\n    x: \"2\",\n    y: \"8\",\n    rx: \"1.5\",\n    key: \"1omvl4\"\n  }], [\"path\", {\n    d: \"M8.5 5H10V3.5A1.5 1.5 0 1 0 8.5 5\",\n    key: \"16f3cl\"\n  }]]);\n});", "lineCount": 56, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "<PERSON><PERSON>ck"], [15, 11, 10, 11], [15, 14, 10, 11, "exports"], [15, 21, 10, 11], [15, 22, 10, 11, "default"], [15, 29, 10, 11], [15, 32, 10, 14], [15, 36, 10, 14, "createLucideIcon"], [15, 61, 10, 30], [15, 63, 10, 31], [15, 70, 10, 38], [15, 72, 10, 40], [15, 73, 11, 2], [15, 74, 11, 3], [15, 80, 11, 9], [15, 82, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 14, 11, 23], [17, 4, 11, 25, "height"], [17, 10, 11, 31], [17, 12, 11, 33], [17, 15, 11, 36], [18, 4, 11, 38, "x"], [18, 5, 11, 39], [18, 7, 11, 41], [18, 11, 11, 45], [19, 4, 11, 47, "y"], [19, 5, 11, 48], [19, 7, 11, 50], [19, 10, 11, 53], [20, 4, 11, 55, "rx"], [20, 6, 11, 57], [20, 8, 11, 59], [20, 13, 11, 64], [21, 4, 11, 66, "key"], [21, 7, 11, 69], [21, 9, 11, 71], [22, 2, 11, 80], [22, 3, 11, 81], [22, 4, 11, 82], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [22, 15, 12, 11], [23, 4, 12, 13, "d"], [23, 5, 12, 14], [23, 7, 12, 16], [23, 44, 12, 53], [24, 4, 12, 55, "key"], [24, 7, 12, 58], [24, 9, 12, 60], [25, 2, 12, 69], [25, 3, 12, 70], [25, 4, 12, 71], [25, 6, 13, 2], [25, 7, 13, 3], [25, 13, 13, 9], [25, 15, 13, 11], [26, 4, 13, 13, "width"], [26, 9, 13, 18], [26, 11, 13, 20], [26, 14, 13, 23], [27, 4, 13, 25, "height"], [27, 10, 13, 31], [27, 12, 13, 33], [27, 15, 13, 36], [28, 4, 13, 38, "x"], [28, 5, 13, 39], [28, 7, 13, 41], [28, 10, 13, 44], [29, 4, 13, 46, "y"], [29, 5, 13, 47], [29, 7, 13, 49], [29, 11, 13, 53], [30, 4, 13, 55, "rx"], [30, 6, 13, 57], [30, 8, 13, 59], [30, 13, 13, 64], [31, 4, 13, 66, "key"], [31, 7, 13, 69], [31, 9, 13, 71], [32, 2, 13, 80], [32, 3, 13, 81], [32, 4, 13, 82], [32, 6, 14, 2], [32, 7, 14, 3], [32, 13, 14, 9], [32, 15, 14, 11], [33, 4, 14, 13, "d"], [33, 5, 14, 14], [33, 7, 14, 16], [33, 44, 14, 53], [34, 4, 14, 55, "key"], [34, 7, 14, 58], [34, 9, 14, 60], [35, 2, 14, 69], [35, 3, 14, 70], [35, 4, 14, 71], [35, 6, 15, 2], [35, 7, 15, 3], [35, 13, 15, 9], [35, 15, 15, 11], [36, 4, 15, 13, "width"], [36, 9, 15, 18], [36, 11, 15, 20], [36, 14, 15, 23], [37, 4, 15, 25, "height"], [37, 10, 15, 31], [37, 12, 15, 33], [37, 15, 15, 36], [38, 4, 15, 38, "x"], [38, 5, 15, 39], [38, 7, 15, 41], [38, 11, 15, 45], [39, 4, 15, 47, "y"], [39, 5, 15, 48], [39, 7, 15, 50], [39, 11, 15, 54], [40, 4, 15, 56, "rx"], [40, 6, 15, 58], [40, 8, 15, 60], [40, 13, 15, 65], [41, 4, 15, 67, "key"], [41, 7, 15, 70], [41, 9, 15, 72], [42, 2, 15, 81], [42, 3, 15, 82], [42, 4, 15, 83], [42, 6, 16, 2], [42, 7, 16, 3], [42, 13, 16, 9], [42, 15, 16, 11], [43, 4, 16, 13, "d"], [43, 5, 16, 14], [43, 7, 16, 16], [43, 46, 16, 55], [44, 4, 16, 57, "key"], [44, 7, 16, 60], [44, 9, 16, 62], [45, 2, 16, 71], [45, 3, 16, 72], [45, 4, 16, 73], [45, 6, 17, 2], [45, 7, 17, 3], [45, 13, 17, 9], [45, 15, 17, 11], [46, 4, 17, 13, "width"], [46, 9, 17, 18], [46, 11, 17, 20], [46, 14, 17, 23], [47, 4, 17, 25, "height"], [47, 10, 17, 31], [47, 12, 17, 33], [47, 15, 17, 36], [48, 4, 17, 38, "x"], [48, 5, 17, 39], [48, 7, 17, 41], [48, 10, 17, 44], [49, 4, 17, 46, "y"], [49, 5, 17, 47], [49, 7, 17, 49], [49, 10, 17, 52], [50, 4, 17, 54, "rx"], [50, 6, 17, 56], [50, 8, 17, 58], [50, 13, 17, 63], [51, 4, 17, 65, "key"], [51, 7, 17, 68], [51, 9, 17, 70], [52, 2, 17, 79], [52, 3, 17, 80], [52, 4, 17, 81], [52, 6, 18, 2], [52, 7, 18, 3], [52, 13, 18, 9], [52, 15, 18, 11], [53, 4, 18, 13, "d"], [53, 5, 18, 14], [53, 7, 18, 16], [53, 42, 18, 51], [54, 4, 18, 53, "key"], [54, 7, 18, 56], [54, 9, 18, 58], [55, 2, 18, 67], [55, 3, 18, 68], [55, 4, 18, 69], [55, 5, 19, 1], [55, 6, 19, 2], [56, 0, 19, 3], [56, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}