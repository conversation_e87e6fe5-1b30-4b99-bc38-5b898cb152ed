{"dependencies": [{"name": "react-refresh/runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 28, "index": 83}, "end": {"line": 3, "column": 60, "index": 115}}], "key": "aJ0aDUxMOQ5TKhQl6UgKSzS2+dc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  // This needs to run before the renderer initializes.\n\n  const ReactRefreshRuntime = require(_dependencyMap[0], \"react-refresh/runtime\");\n  ReactRefreshRuntime.injectIntoGlobalHook(global);\n  const Refresh = {\n    performFullRefresh() {\n      location.reload();\n    },\n    createSignatureFunctionForTransform: ReactRefreshRuntime.createSignatureFunctionForTransform,\n    isLikelyComponentType: ReactRefreshRuntime.isLikelyComponentType,\n    getFamilyByType: ReactRefreshRuntime.getFamilyByType,\n    register: ReactRefreshRuntime.register,\n    performReactRefresh() {\n      if (ReactRefreshRuntime.hasUnrecoverableErrors()) {\n        location.reload();\n        return;\n      }\n      ReactRefreshRuntime.performReactRefresh();\n    }\n  };\n\n  // The metro require polyfill can not have dependencies (applies for all polyfills).\n  // Expose `Refresh` by assigning it to global to make it available in the polyfill.\n  global[(global.__METRO_GLOBAL_PREFIX__ || '') + '__ReactRefresh'] = Refresh;\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [4, 2, 3, 0], [4, 8, 3, 6, "ReactRefreshRuntime"], [4, 27, 3, 25], [4, 30, 3, 28, "require"], [4, 37, 3, 35], [4, 38, 3, 35, "_dependencyMap"], [4, 52, 3, 35], [4, 80, 3, 59], [4, 81, 3, 60], [5, 2, 4, 0, "ReactRefreshRuntime"], [5, 21, 4, 19], [5, 22, 4, 20, "injectIntoGlobalHook"], [5, 42, 4, 40], [5, 43, 4, 41, "global"], [5, 49, 4, 47], [5, 50, 4, 48], [6, 2, 6, 0], [6, 8, 6, 6, "Refresh"], [6, 15, 6, 13], [6, 18, 6, 16], [7, 4, 7, 2, "performFullRefresh"], [7, 22, 7, 20, "performFullRefresh"], [7, 23, 7, 20], [7, 25, 7, 23], [8, 6, 8, 4, "location"], [8, 14, 8, 12], [8, 15, 8, 13, "reload"], [8, 21, 8, 19], [8, 22, 8, 20], [8, 23, 8, 21], [9, 4, 9, 2], [9, 5, 9, 3], [10, 4, 11, 2, "createSignatureFunctionForTransform"], [10, 39, 11, 37], [10, 41, 11, 39, "ReactRefreshRuntime"], [10, 60, 11, 58], [10, 61, 11, 59, "createSignatureFunctionForTransform"], [10, 96, 11, 94], [11, 4, 13, 2, "isLikelyComponentType"], [11, 25, 13, 23], [11, 27, 13, 25, "ReactRefreshRuntime"], [11, 46, 13, 44], [11, 47, 13, 45, "isLikelyComponentType"], [11, 68, 13, 66], [12, 4, 15, 2, "getFamilyByType"], [12, 19, 15, 17], [12, 21, 15, 19, "ReactRefreshRuntime"], [12, 40, 15, 38], [12, 41, 15, 39, "getFamilyByType"], [12, 56, 15, 54], [13, 4, 17, 2, "register"], [13, 12, 17, 10], [13, 14, 17, 12, "ReactRefreshRuntime"], [13, 33, 17, 31], [13, 34, 17, 32, "register"], [13, 42, 17, 40], [14, 4, 19, 2, "performReactRefresh"], [14, 23, 19, 21, "performReactRefresh"], [14, 24, 19, 21], [14, 26, 19, 24], [15, 6, 20, 4], [15, 10, 20, 8, "ReactRefreshRuntime"], [15, 29, 20, 27], [15, 30, 20, 28, "hasUnrecoverableErrors"], [15, 52, 20, 50], [15, 53, 20, 51], [15, 54, 20, 52], [15, 56, 20, 54], [16, 8, 21, 6, "location"], [16, 16, 21, 14], [16, 17, 21, 15, "reload"], [16, 23, 21, 21], [16, 24, 21, 22], [16, 25, 21, 23], [17, 8, 22, 6], [18, 6, 23, 4], [19, 6, 24, 4, "ReactRefreshRuntime"], [19, 25, 24, 23], [19, 26, 24, 24, "performReactRefresh"], [19, 45, 24, 43], [19, 46, 24, 44], [19, 47, 24, 45], [20, 4, 25, 2], [21, 2, 26, 0], [21, 3, 26, 1], [23, 2, 28, 0], [24, 2, 29, 0], [25, 2, 30, 0, "global"], [25, 8, 30, 6], [25, 9, 30, 7], [25, 10, 30, 8, "global"], [25, 16, 30, 14], [25, 17, 30, 15, "__METRO_GLOBAL_PREFIX__"], [25, 40, 30, 38], [25, 44, 30, 42], [25, 46, 30, 44], [25, 50, 30, 48], [25, 66, 30, 64], [25, 67, 30, 65], [25, 70, 30, 68, "Refresh"], [25, 77, 30, 75], [26, 0, 30, 76], [26, 3]], "functionMap": {"names": ["<global>", "Refresh.performFullRefresh", "Refresh.performReactRefresh"], "mappings": "AAA;ECM;GDE;EEU;GFM"}}, "type": "js/module"}]}