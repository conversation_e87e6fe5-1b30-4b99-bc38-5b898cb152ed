{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _jsxDevRuntime = require(_dependencyMap[21], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n    };\n    const handleSendMessage = (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const handleVoiceRecord = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (recorderState.isRecording) {\n          // Stop the current recording session\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        } else {\n          // Start continuous listening session\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with voice recording:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        onRecord: handleVoiceRecord,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"9C1WkPoISXmHucsqM0n1HH2DnhY=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 599, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 32, 55], [27, 6, 32, 55, "_jsxDevRuntime"], [27, 20, 32, 55], [27, 23, 32, 55, "require"], [27, 30, 32, 55], [27, 31, 32, 55, "_dependencyMap"], [27, 45, 32, 55], [28, 2, 32, 55], [28, 6, 32, 55, "_jsxFileName"], [28, 18, 32, 55], [29, 4, 32, 55, "_s"], [29, 6, 32, 55], [29, 9, 32, 55, "$RefreshSig$"], [29, 21, 32, 55], [30, 2, 32, 55], [30, 11, 32, 55, "_interopRequireWildcard"], [30, 35, 32, 55, "e"], [30, 36, 32, 55], [30, 38, 32, 55, "t"], [30, 39, 32, 55], [30, 68, 32, 55, "WeakMap"], [30, 75, 32, 55], [30, 81, 32, 55, "r"], [30, 82, 32, 55], [30, 89, 32, 55, "WeakMap"], [30, 96, 32, 55], [30, 100, 32, 55, "n"], [30, 101, 32, 55], [30, 108, 32, 55, "WeakMap"], [30, 115, 32, 55], [30, 127, 32, 55, "_interopRequireWildcard"], [30, 150, 32, 55], [30, 162, 32, 55, "_interopRequireWildcard"], [30, 163, 32, 55, "e"], [30, 164, 32, 55], [30, 166, 32, 55, "t"], [30, 167, 32, 55], [30, 176, 32, 55, "t"], [30, 177, 32, 55], [30, 181, 32, 55, "e"], [30, 182, 32, 55], [30, 186, 32, 55, "e"], [30, 187, 32, 55], [30, 188, 32, 55, "__esModule"], [30, 198, 32, 55], [30, 207, 32, 55, "e"], [30, 208, 32, 55], [30, 214, 32, 55, "o"], [30, 215, 32, 55], [30, 217, 32, 55, "i"], [30, 218, 32, 55], [30, 220, 32, 55, "f"], [30, 221, 32, 55], [30, 226, 32, 55, "__proto__"], [30, 235, 32, 55], [30, 243, 32, 55, "default"], [30, 250, 32, 55], [30, 252, 32, 55, "e"], [30, 253, 32, 55], [30, 270, 32, 55, "e"], [30, 271, 32, 55], [30, 294, 32, 55, "e"], [30, 295, 32, 55], [30, 320, 32, 55, "e"], [30, 321, 32, 55], [30, 330, 32, 55, "f"], [30, 331, 32, 55], [30, 337, 32, 55, "o"], [30, 338, 32, 55], [30, 341, 32, 55, "t"], [30, 342, 32, 55], [30, 345, 32, 55, "n"], [30, 346, 32, 55], [30, 349, 32, 55, "r"], [30, 350, 32, 55], [30, 358, 32, 55, "o"], [30, 359, 32, 55], [30, 360, 32, 55, "has"], [30, 363, 32, 55], [30, 364, 32, 55, "e"], [30, 365, 32, 55], [30, 375, 32, 55, "o"], [30, 376, 32, 55], [30, 377, 32, 55, "get"], [30, 380, 32, 55], [30, 381, 32, 55, "e"], [30, 382, 32, 55], [30, 385, 32, 55, "o"], [30, 386, 32, 55], [30, 387, 32, 55, "set"], [30, 390, 32, 55], [30, 391, 32, 55, "e"], [30, 392, 32, 55], [30, 394, 32, 55, "f"], [30, 395, 32, 55], [30, 411, 32, 55, "t"], [30, 412, 32, 55], [30, 416, 32, 55, "e"], [30, 417, 32, 55], [30, 433, 32, 55, "t"], [30, 434, 32, 55], [30, 441, 32, 55, "hasOwnProperty"], [30, 455, 32, 55], [30, 456, 32, 55, "call"], [30, 460, 32, 55], [30, 461, 32, 55, "e"], [30, 462, 32, 55], [30, 464, 32, 55, "t"], [30, 465, 32, 55], [30, 472, 32, 55, "i"], [30, 473, 32, 55], [30, 477, 32, 55, "o"], [30, 478, 32, 55], [30, 481, 32, 55, "Object"], [30, 487, 32, 55], [30, 488, 32, 55, "defineProperty"], [30, 502, 32, 55], [30, 507, 32, 55, "Object"], [30, 513, 32, 55], [30, 514, 32, 55, "getOwnPropertyDescriptor"], [30, 538, 32, 55], [30, 539, 32, 55, "e"], [30, 540, 32, 55], [30, 542, 32, 55, "t"], [30, 543, 32, 55], [30, 550, 32, 55, "i"], [30, 551, 32, 55], [30, 552, 32, 55, "get"], [30, 555, 32, 55], [30, 559, 32, 55, "i"], [30, 560, 32, 55], [30, 561, 32, 55, "set"], [30, 564, 32, 55], [30, 568, 32, 55, "o"], [30, 569, 32, 55], [30, 570, 32, 55, "f"], [30, 571, 32, 55], [30, 573, 32, 55, "t"], [30, 574, 32, 55], [30, 576, 32, 55, "i"], [30, 577, 32, 55], [30, 581, 32, 55, "f"], [30, 582, 32, 55], [30, 583, 32, 55, "t"], [30, 584, 32, 55], [30, 588, 32, 55, "e"], [30, 589, 32, 55], [30, 590, 32, 55, "t"], [30, 591, 32, 55], [30, 602, 32, 55, "f"], [30, 603, 32, 55], [30, 608, 32, 55, "e"], [30, 609, 32, 55], [30, 611, 32, 55, "t"], [30, 612, 32, 55], [31, 2, 34, 15], [31, 11, 34, 24, "BrainstormScreen"], [31, 27, 34, 40, "BrainstormScreen"], [31, 28, 34, 40], [31, 30, 34, 43], [32, 4, 34, 43, "_s"], [32, 6, 34, 43], [33, 4, 35, 2], [33, 10, 35, 8, "insets"], [33, 16, 35, 14], [33, 19, 35, 17], [33, 23, 35, 17, "useSafeAreaInsets"], [33, 68, 35, 34], [33, 70, 35, 35], [33, 71, 35, 36], [34, 4, 36, 2], [34, 10, 36, 8, "colors"], [34, 16, 36, 14], [34, 19, 36, 17], [34, 23, 36, 17, "useColors"], [34, 43, 36, 26], [34, 45, 36, 27], [34, 46, 36, 28], [35, 4, 37, 2], [35, 10, 37, 8], [35, 11, 37, 9, "fontsLoaded"], [35, 22, 37, 20], [35, 23, 37, 21], [35, 26, 37, 24], [35, 30, 37, 24, "useFonts"], [35, 47, 37, 32], [35, 49, 37, 33], [36, 6, 38, 4, "Poppins_400Regular"], [36, 24, 38, 22], [36, 26, 38, 4, "Poppins_400Regular"], [36, 53, 38, 22], [37, 6, 39, 4, "Poppins_500Medium"], [37, 23, 39, 21], [37, 25, 39, 4, "Poppins_500Medium"], [37, 51, 39, 21], [38, 6, 40, 4, "Poppins_600SemiBold"], [38, 25, 40, 23], [38, 27, 40, 4, "Poppins_600SemiBold"], [39, 4, 41, 2], [39, 5, 41, 3], [39, 6, 41, 4], [40, 4, 43, 2], [40, 10, 43, 8, "recorder"], [40, 18, 43, 16], [40, 21, 43, 19], [40, 25, 43, 19, "useAudioRecorder"], [40, 52, 43, 35], [40, 54, 43, 36, "RecordingPresets"], [40, 81, 43, 52], [40, 82, 43, 53, "HIGH_QUALITY"], [40, 94, 43, 65], [40, 95, 43, 66], [41, 4, 44, 2], [41, 10, 44, 8, "recorderState"], [41, 23, 44, 21], [41, 26, 44, 24], [41, 30, 44, 24, "useAudioRecorderState"], [41, 62, 44, 45], [41, 64, 44, 46, "recorder"], [41, 72, 44, 54], [41, 73, 44, 55], [42, 4, 46, 2], [42, 10, 46, 8], [42, 11, 46, 9, "messages"], [42, 19, 46, 17], [42, 21, 46, 19, "setMessages"], [42, 32, 46, 30], [42, 33, 46, 31], [42, 36, 46, 34], [42, 40, 46, 34, "useState"], [42, 55, 46, 42], [42, 57, 46, 43], [42, 59, 46, 45], [42, 60, 46, 46], [43, 4, 47, 2], [43, 10, 47, 8], [43, 11, 47, 9, "inputText"], [43, 20, 47, 18], [43, 22, 47, 20, "setInputText"], [43, 34, 47, 32], [43, 35, 47, 33], [43, 38, 47, 36], [43, 42, 47, 36, "useState"], [43, 57, 47, 44], [43, 59, 47, 45], [43, 61, 47, 47], [43, 62, 47, 48], [44, 4, 48, 2], [44, 10, 48, 8], [44, 11, 48, 9, "isFirstTime"], [44, 22, 48, 20], [44, 24, 48, 22, "setIsFirstTime"], [44, 38, 48, 36], [44, 39, 48, 37], [44, 42, 48, 40], [44, 46, 48, 40, "useState"], [44, 61, 48, 48], [44, 63, 48, 49], [44, 67, 48, 53], [44, 68, 48, 54], [45, 4, 49, 2], [45, 10, 49, 8], [45, 11, 49, 9, "isLoading"], [45, 20, 49, 18], [45, 22, 49, 20, "setIsLoading"], [45, 34, 49, 32], [45, 35, 49, 33], [45, 38, 49, 36], [45, 42, 49, 36, "useState"], [45, 57, 49, 44], [45, 59, 49, 45], [45, 64, 49, 50], [45, 65, 49, 51], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "quickActions"], [46, 23, 50, 21], [46, 25, 50, 23, "setQuickActions"], [46, 40, 50, 38], [46, 41, 50, 39], [46, 44, 50, 42], [46, 48, 50, 42, "useState"], [46, 63, 50, 50], [46, 65, 50, 51], [46, 67, 50, 53], [46, 68, 50, 54], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "voiceMode"], [47, 20, 51, 18], [47, 22, 51, 20, "setVoiceMode"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 63, 51, 49], [47, 64, 51, 50], [47, 65, 51, 51], [47, 66, 51, 52], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "hasPermission"], [48, 24, 52, 22], [48, 26, 52, 24, "setHasPermission"], [48, 42, 52, 40], [48, 43, 52, 41], [48, 46, 52, 44], [48, 50, 52, 44, "useState"], [48, 65, 52, 52], [48, 67, 52, 53], [48, 72, 52, 58], [48, 73, 52, 59], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isDictating"], [49, 22, 53, 20], [49, 24, 53, 22, "setIsDictating"], [49, 38, 53, 36], [49, 39, 53, 37], [49, 42, 53, 40], [49, 46, 53, 40, "useState"], [49, 61, 53, 48], [49, 63, 53, 49], [49, 68, 53, 54], [49, 69, 53, 55], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "longPressedMessage"], [50, 29, 54, 27], [50, 31, 54, 29, "setLongPressedMessage"], [50, 52, 54, 50], [50, 53, 54, 51], [50, 56, 54, 54], [50, 60, 54, 54, "useState"], [50, 75, 54, 62], [50, 77, 54, 63], [50, 81, 54, 67], [50, 82, 54, 68], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "isContextMenuVisible"], [51, 31, 55, 29], [51, 33, 55, 31, "setIsContextMenuVisible"], [51, 56, 55, 54], [51, 57, 55, 55], [51, 60, 55, 58], [51, 64, 55, 58, "useState"], [51, 79, 55, 66], [51, 81, 55, 67], [51, 86, 55, 72], [51, 87, 55, 73], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "transcript"], [52, 21, 56, 19], [52, 23, 56, 21, "setTranscript"], [52, 36, 56, 34], [52, 37, 56, 35], [52, 40, 56, 38], [52, 44, 56, 38, "useState"], [52, 59, 56, 46], [52, 61, 56, 47], [52, 63, 56, 49], [52, 64, 56, 50], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isMuted"], [53, 18, 57, 16], [53, 20, 57, 18, "setIsMuted"], [53, 30, 57, 28], [53, 31, 57, 29], [53, 34, 57, 32], [53, 38, 57, 32, "useState"], [53, 53, 57, 40], [53, 55, 57, 41], [53, 60, 57, 46], [53, 61, 57, 47], [54, 4, 59, 2], [54, 10, 59, 8, "scrollViewRef"], [54, 23, 59, 21], [54, 26, 59, 24], [54, 30, 59, 24, "useRef"], [54, 43, 59, 30], [54, 45, 59, 31], [54, 49, 59, 35], [54, 50, 59, 36], [56, 4, 61, 2], [57, 4, 62, 2], [57, 8, 62, 2, "useEffect"], [57, 24, 62, 11], [57, 26, 62, 12], [57, 32, 62, 18], [58, 6, 63, 4], [58, 7, 63, 5], [58, 19, 63, 17], [59, 8, 64, 6], [59, 14, 64, 12], [60, 10, 64, 14, "granted"], [61, 8, 64, 22], [61, 9, 64, 23], [61, 12, 64, 26], [61, 18, 64, 32], [61, 22, 64, 32, "requestRecordingPermissionsAsync"], [61, 65, 64, 64], [61, 67, 64, 65], [61, 68, 64, 66], [62, 8, 65, 6, "setHasPermission"], [62, 24, 65, 22], [62, 25, 65, 23, "granted"], [62, 32, 65, 30], [62, 33, 65, 31], [63, 8, 66, 6], [63, 12, 66, 10], [63, 13, 66, 11, "granted"], [63, 20, 66, 18], [63, 24, 66, 22, "voiceMode"], [63, 33, 66, 31], [63, 35, 66, 33], [64, 10, 67, 8, "<PERSON><PERSON>"], [64, 24, 67, 13], [64, 25, 67, 14, "alert"], [64, 30, 67, 19], [64, 31, 68, 10], [64, 52, 68, 31], [64, 54, 69, 10], [64, 133, 69, 89], [64, 135, 70, 10], [64, 136, 71, 12], [65, 12, 71, 14, "text"], [65, 16, 71, 18], [65, 18, 71, 20], [65, 33, 71, 35], [66, 12, 71, 37, "onPress"], [66, 19, 71, 44], [66, 21, 71, 46, "onPress"], [66, 22, 71, 46], [66, 27, 71, 52, "setVoiceMode"], [66, 39, 71, 64], [66, 40, 71, 65], [66, 45, 71, 70], [67, 10, 71, 72], [67, 11, 71, 73], [67, 13, 72, 12], [68, 12, 72, 14, "text"], [68, 16, 72, 18], [68, 18, 72, 20], [69, 10, 72, 25], [69, 11, 72, 26], [69, 12, 74, 8], [69, 13, 74, 9], [70, 8, 75, 6], [71, 6, 76, 4], [71, 7, 76, 5], [71, 9, 76, 7], [71, 10, 76, 8], [72, 4, 77, 2], [72, 5, 77, 3], [72, 7, 77, 5], [72, 8, 77, 6, "voiceMode"], [72, 17, 77, 15], [72, 18, 77, 16], [72, 19, 77, 17], [73, 4, 79, 2], [73, 10, 79, 8, "handleStartBrainstorming"], [73, 34, 79, 32], [73, 37, 79, 35, "handleStartBrainstorming"], [73, 38, 79, 35], [73, 43, 79, 41], [74, 6, 80, 4, "Haptics"], [74, 13, 80, 11], [74, 14, 80, 12, "impactAsync"], [74, 25, 80, 23], [74, 26, 80, 24, "Haptics"], [74, 33, 80, 31], [74, 34, 80, 32, "ImpactFeedbackStyle"], [74, 53, 80, 51], [74, 54, 80, 52, "Medium"], [74, 60, 80, 58], [74, 61, 80, 59], [75, 6, 81, 4, "setIsFirstTime"], [75, 20, 81, 18], [75, 21, 81, 19], [75, 26, 81, 24], [75, 27, 81, 25], [76, 6, 82, 4, "setMessages"], [76, 17, 82, 15], [76, 18, 82, 16, "fakeMessages"], [76, 40, 82, 28], [76, 41, 82, 29], [77, 6, 83, 4, "setQuickActions"], [77, 21, 83, 19], [77, 22, 83, 20, "fakeQuickActions"], [77, 48, 83, 36], [77, 49, 83, 37], [78, 4, 84, 2], [78, 5, 84, 3], [79, 4, 86, 2], [79, 10, 86, 8, "handleSendMessage"], [79, 27, 86, 25], [79, 30, 86, 28, "handleSendMessage"], [79, 31, 86, 29, "message"], [79, 38, 86, 36], [79, 41, 86, 39, "inputText"], [79, 50, 86, 48], [79, 55, 86, 53], [80, 6, 87, 4], [80, 10, 87, 8], [80, 11, 87, 9, "message"], [80, 18, 87, 16], [80, 19, 87, 17, "trim"], [80, 23, 87, 21], [80, 24, 87, 22], [80, 25, 87, 23], [80, 27, 87, 25], [81, 6, 89, 4], [81, 12, 89, 10, "newUserMessage"], [81, 26, 89, 24], [81, 29, 89, 27], [82, 8, 89, 29, "role"], [82, 12, 89, 33], [82, 14, 89, 35], [82, 20, 89, 41], [83, 8, 89, 43, "content"], [83, 15, 89, 50], [83, 17, 89, 52, "message"], [84, 6, 89, 60], [84, 7, 89, 61], [85, 6, 90, 4, "setMessages"], [85, 17, 90, 15], [85, 18, 90, 17, "prev"], [85, 22, 90, 21], [85, 26, 90, 26], [85, 27, 90, 27], [85, 30, 90, 30, "prev"], [85, 34, 90, 34], [85, 36, 90, 36, "newUserMessage"], [85, 50, 90, 50], [85, 51, 90, 51], [85, 52, 90, 52], [86, 6, 91, 4, "setInputText"], [86, 18, 91, 16], [86, 19, 91, 17], [86, 21, 91, 19], [86, 22, 91, 20], [87, 6, 92, 4, "setQuickActions"], [87, 21, 92, 19], [87, 22, 92, 20], [87, 24, 92, 22], [87, 25, 92, 23], [88, 6, 93, 4, "setIsLoading"], [88, 18, 93, 16], [88, 19, 93, 17], [88, 23, 93, 21], [88, 24, 93, 22], [90, 6, 95, 4], [91, 6, 96, 4, "setTimeout"], [91, 16, 96, 14], [91, 17, 96, 15], [91, 23, 96, 21], [92, 8, 97, 6], [92, 14, 97, 12, "aiResponse"], [92, 24, 97, 22], [92, 27, 97, 25], [93, 10, 98, 8, "role"], [93, 14, 98, 12], [93, 16, 98, 14], [93, 27, 98, 25], [94, 10, 99, 8, "content"], [94, 17, 99, 15], [94, 19, 99, 17], [95, 8, 100, 6], [95, 9, 100, 7], [96, 8, 101, 6, "setMessages"], [96, 19, 101, 17], [96, 20, 101, 19, "prev"], [96, 24, 101, 23], [96, 28, 101, 28], [96, 29, 101, 29], [96, 32, 101, 32, "prev"], [96, 36, 101, 36], [96, 38, 101, 38, "aiResponse"], [96, 48, 101, 48], [96, 49, 101, 49], [96, 50, 101, 50], [97, 8, 102, 6, "setQuickActions"], [97, 23, 102, 21], [97, 24, 102, 22, "fakeQuickActions"], [97, 50, 102, 38], [97, 51, 102, 39], [98, 8, 103, 6, "setIsLoading"], [98, 20, 103, 18], [98, 21, 103, 19], [98, 26, 103, 24], [98, 27, 103, 25], [99, 6, 104, 4], [99, 7, 104, 5], [99, 9, 104, 7], [99, 13, 104, 11], [99, 14, 104, 12], [100, 4, 105, 2], [100, 5, 105, 3], [101, 4, 107, 2], [101, 10, 107, 8, "handleQuickAction"], [101, 27, 107, 25], [101, 30, 107, 29, "action"], [101, 36, 107, 35], [101, 40, 107, 40], [102, 6, 108, 4, "Haptics"], [102, 13, 108, 11], [102, 14, 108, 12, "impactAsync"], [102, 25, 108, 23], [102, 26, 108, 24, "Haptics"], [102, 33, 108, 31], [102, 34, 108, 32, "ImpactFeedbackStyle"], [102, 53, 108, 51], [102, 54, 108, 52, "Light"], [102, 59, 108, 57], [102, 60, 108, 58], [103, 6, 109, 4, "handleSendMessage"], [103, 23, 109, 21], [103, 24, 109, 22, "action"], [103, 30, 109, 28], [103, 31, 109, 29], [104, 4, 110, 2], [104, 5, 110, 3], [105, 4, 112, 2], [105, 10, 112, 8, "toggleVoiceMode"], [105, 25, 112, 23], [105, 28, 112, 26], [105, 34, 112, 26, "toggleVoiceMode"], [105, 35, 112, 26], [105, 40, 112, 38], [106, 6, 113, 4], [106, 10, 113, 8], [106, 11, 113, 9, "voiceMode"], [106, 20, 113, 18], [106, 24, 113, 22], [106, 25, 113, 23, "hasPermission"], [106, 38, 113, 36], [106, 40, 113, 38], [107, 8, 114, 6, "<PERSON><PERSON>"], [107, 22, 114, 11], [107, 23, 114, 12, "alert"], [107, 28, 114, 17], [107, 29, 115, 8], [107, 50, 115, 29], [107, 52, 116, 8], [107, 102, 116, 58], [107, 104, 117, 8], [107, 105, 118, 10], [108, 10, 118, 12, "text"], [108, 14, 118, 16], [108, 16, 118, 18], [109, 8, 118, 27], [109, 9, 118, 28], [109, 11, 119, 10], [110, 10, 120, 12, "text"], [110, 14, 120, 16], [110, 16, 120, 18], [110, 34, 120, 36], [111, 10, 121, 12, "onPress"], [111, 17, 121, 19], [111, 19, 121, 21], [111, 25, 121, 21, "onPress"], [111, 26, 121, 21], [111, 31, 121, 33], [112, 12, 122, 14], [112, 18, 122, 20], [113, 14, 122, 22, "granted"], [114, 12, 122, 30], [114, 13, 122, 31], [114, 16, 122, 34], [114, 22, 122, 40], [114, 26, 122, 40, "requestRecordingPermissionsAsync"], [114, 69, 122, 72], [114, 71, 122, 73], [114, 72, 122, 74], [115, 12, 123, 14], [115, 16, 123, 18, "granted"], [115, 23, 123, 25], [115, 25, 123, 27], [116, 14, 124, 16, "setHasPermission"], [116, 30, 124, 32], [116, 31, 124, 33], [116, 35, 124, 37], [116, 36, 124, 38], [117, 14, 125, 16, "setVoiceMode"], [117, 26, 125, 28], [117, 27, 125, 29], [117, 31, 125, 33], [117, 32, 125, 34], [118, 14, 126, 16], [119, 14, 127, 16], [119, 20, 127, 22, "startVoiceSession"], [119, 37, 127, 39], [119, 38, 127, 40], [119, 39, 127, 41], [120, 12, 128, 14], [121, 10, 129, 12], [122, 8, 130, 10], [122, 9, 130, 11], [122, 10, 132, 6], [122, 11, 132, 7], [123, 8, 133, 6], [124, 6, 134, 4], [125, 6, 136, 4, "Haptics"], [125, 13, 136, 11], [125, 14, 136, 12, "impactAsync"], [125, 25, 136, 23], [125, 26, 136, 24, "Haptics"], [125, 33, 136, 31], [125, 34, 136, 32, "ImpactFeedbackStyle"], [125, 53, 136, 51], [125, 54, 136, 52, "Light"], [125, 59, 136, 57], [125, 60, 136, 58], [126, 6, 138, 4], [126, 10, 138, 8], [126, 11, 138, 9, "voiceMode"], [126, 20, 138, 18], [126, 22, 138, 20], [127, 8, 139, 6], [128, 8, 140, 6, "setVoiceMode"], [128, 20, 140, 18], [128, 21, 140, 19], [128, 25, 140, 23], [128, 26, 140, 24], [129, 8, 141, 6], [129, 12, 141, 10, "hasPermission"], [129, 25, 141, 23], [129, 27, 141, 25], [130, 10, 142, 8], [130, 16, 142, 14, "startVoiceSession"], [130, 33, 142, 31], [130, 34, 142, 32], [130, 35, 142, 33], [131, 8, 143, 6], [132, 6, 144, 4], [132, 7, 144, 5], [132, 13, 144, 11], [133, 8, 145, 6], [134, 8, 146, 6, "setVoiceMode"], [134, 20, 146, 18], [134, 21, 146, 19], [134, 26, 146, 24], [134, 27, 146, 25], [135, 8, 147, 6], [135, 12, 147, 10, "recorderState"], [135, 25, 147, 23], [135, 26, 147, 24, "isRecording"], [135, 37, 147, 35], [135, 39, 147, 37], [136, 10, 148, 8], [136, 16, 148, 14, "recorder"], [136, 24, 148, 22], [136, 25, 148, 23, "stop"], [136, 29, 148, 27], [136, 30, 148, 28], [136, 31, 148, 29], [137, 8, 149, 6], [138, 8, 150, 6, "setIsMuted"], [138, 18, 150, 16], [138, 19, 150, 17], [138, 24, 150, 22], [138, 25, 150, 23], [138, 26, 150, 24], [138, 27, 150, 25], [139, 6, 151, 4], [140, 4, 152, 2], [140, 5, 152, 3], [141, 4, 154, 2], [141, 10, 154, 8, "handleVoiceRecord"], [141, 27, 154, 25], [141, 30, 154, 28], [141, 36, 154, 28, "handleVoiceRecord"], [141, 37, 154, 28], [141, 42, 154, 40], [142, 6, 155, 4], [142, 10, 155, 8], [142, 11, 155, 9, "hasPermission"], [142, 24, 155, 22], [142, 26, 155, 24], [143, 8, 156, 6, "<PERSON><PERSON>"], [143, 22, 156, 11], [143, 23, 156, 12, "alert"], [143, 28, 156, 17], [143, 29, 157, 8], [143, 50, 157, 29], [143, 52, 158, 8], [143, 102, 159, 6], [143, 103, 159, 7], [144, 8, 160, 6], [145, 6, 161, 4], [146, 6, 163, 4], [146, 10, 163, 8], [147, 8, 164, 6], [147, 12, 164, 10, "recorderState"], [147, 25, 164, 23], [147, 26, 164, 24, "isRecording"], [147, 37, 164, 35], [147, 39, 164, 37], [148, 10, 165, 8], [149, 10, 166, 8], [149, 16, 166, 14, "recorder"], [149, 24, 166, 22], [149, 25, 166, 23, "stop"], [149, 29, 166, 27], [149, 30, 166, 28], [149, 31, 166, 29], [150, 10, 167, 8, "Haptics"], [150, 17, 167, 15], [150, 18, 167, 16, "impactAsync"], [150, 29, 167, 27], [150, 30, 167, 28, "Haptics"], [150, 37, 167, 35], [150, 38, 167, 36, "ImpactFeedbackStyle"], [150, 57, 167, 55], [150, 58, 167, 56, "Medium"], [150, 64, 167, 62], [150, 65, 167, 63], [151, 8, 168, 6], [151, 9, 168, 7], [151, 15, 168, 13], [152, 10, 169, 8], [153, 10, 170, 8], [153, 16, 170, 14, "recorder"], [153, 24, 170, 22], [153, 25, 170, 23, "prepareToRecordAsync"], [153, 45, 170, 43], [153, 46, 170, 44], [153, 47, 170, 45], [154, 10, 171, 8, "recorder"], [154, 18, 171, 16], [154, 19, 171, 17, "record"], [154, 25, 171, 23], [154, 26, 171, 24], [154, 27, 171, 25], [155, 10, 172, 8, "Haptics"], [155, 17, 172, 15], [155, 18, 172, 16, "impactAsync"], [155, 29, 172, 27], [155, 30, 172, 28, "Haptics"], [155, 37, 172, 35], [155, 38, 172, 36, "ImpactFeedbackStyle"], [155, 57, 172, 55], [155, 58, 172, 56, "Medium"], [155, 64, 172, 62], [155, 65, 172, 63], [156, 8, 173, 6], [157, 6, 174, 4], [157, 7, 174, 5], [157, 8, 174, 6], [157, 15, 174, 13, "error"], [157, 20, 174, 18], [157, 22, 174, 20], [158, 8, 175, 6, "console"], [158, 15, 175, 13], [158, 16, 175, 14, "error"], [158, 21, 175, 19], [158, 22, 175, 20], [158, 51, 175, 49], [158, 53, 175, 51, "error"], [158, 58, 175, 56], [158, 59, 175, 57], [159, 8, 176, 6, "<PERSON><PERSON>"], [159, 22, 176, 11], [159, 23, 176, 12, "alert"], [159, 28, 176, 17], [159, 29, 176, 18], [159, 36, 176, 25], [159, 38, 176, 27], [159, 69, 176, 58], [159, 70, 176, 59], [160, 6, 177, 4], [161, 4, 178, 2], [161, 5, 178, 3], [162, 4, 180, 2], [162, 10, 180, 8, "handleDictation"], [162, 25, 180, 23], [162, 28, 180, 26], [162, 34, 180, 26, "handleDictation"], [162, 35, 180, 26], [162, 40, 180, 38], [163, 6, 181, 4], [163, 10, 181, 8], [163, 11, 181, 9, "hasPermission"], [163, 24, 181, 22], [163, 26, 181, 24], [164, 8, 182, 6, "<PERSON><PERSON>"], [164, 22, 182, 11], [164, 23, 182, 12, "alert"], [164, 28, 182, 17], [164, 29, 183, 8], [164, 50, 183, 29], [164, 52, 184, 8], [164, 102, 185, 6], [164, 103, 185, 7], [165, 8, 186, 6], [166, 6, 187, 4], [167, 6, 189, 4], [167, 10, 189, 8], [168, 8, 190, 6], [168, 12, 190, 10, "isDictating"], [168, 23, 190, 21], [168, 25, 190, 23], [169, 10, 191, 8], [169, 16, 191, 14, "recorder"], [169, 24, 191, 22], [169, 25, 191, 23, "stop"], [169, 29, 191, 27], [169, 30, 191, 28], [169, 31, 191, 29], [170, 10, 192, 8], [170, 16, 192, 14, "mockTranscript"], [170, 30, 192, 28], [170, 33, 192, 31], [170, 62, 192, 60], [170, 63, 192, 61], [170, 64, 192, 62], [171, 10, 193, 8, "setInputText"], [171, 22, 193, 20], [171, 23, 193, 21, "inputText"], [171, 32, 193, 30], [171, 35, 193, 33, "mockTranscript"], [171, 49, 193, 47], [171, 50, 193, 48], [172, 10, 194, 8, "setIsDictating"], [172, 24, 194, 22], [172, 25, 194, 23], [172, 30, 194, 28], [172, 31, 194, 29], [173, 8, 195, 6], [173, 9, 195, 7], [173, 15, 195, 13], [174, 10, 196, 8], [174, 16, 196, 14, "recorder"], [174, 24, 196, 22], [174, 25, 196, 23, "prepareToRecordAsync"], [174, 45, 196, 43], [174, 46, 196, 44], [174, 47, 196, 45], [175, 10, 197, 8, "recorder"], [175, 18, 197, 16], [175, 19, 197, 17, "record"], [175, 25, 197, 23], [175, 26, 197, 24], [175, 27, 197, 25], [176, 10, 198, 8, "setIsDictating"], [176, 24, 198, 22], [176, 25, 198, 23], [176, 29, 198, 27], [176, 30, 198, 28], [177, 10, 199, 8, "Haptics"], [177, 17, 199, 15], [177, 18, 199, 16, "impactAsync"], [177, 29, 199, 27], [177, 30, 199, 28, "Haptics"], [177, 37, 199, 35], [177, 38, 199, 36, "ImpactFeedbackStyle"], [177, 57, 199, 55], [177, 58, 199, 56, "Medium"], [177, 64, 199, 62], [177, 65, 199, 63], [178, 8, 200, 6], [179, 6, 201, 4], [179, 7, 201, 5], [179, 8, 201, 6], [179, 15, 201, 13, "error"], [179, 20, 201, 18], [179, 22, 201, 20], [180, 8, 202, 6, "console"], [180, 15, 202, 13], [180, 16, 202, 14, "error"], [180, 21, 202, 19], [180, 22, 202, 20], [180, 45, 202, 43], [180, 47, 202, 45, "error"], [180, 52, 202, 50], [180, 53, 202, 51], [181, 8, 203, 6, "<PERSON><PERSON>"], [181, 22, 203, 11], [181, 23, 203, 12, "alert"], [181, 28, 203, 17], [181, 29, 203, 18], [181, 36, 203, 25], [181, 38, 203, 27], [181, 65, 203, 54], [181, 66, 203, 55], [182, 8, 204, 6, "setIsDictating"], [182, 22, 204, 20], [182, 23, 204, 21], [182, 28, 204, 26], [182, 29, 204, 27], [183, 6, 205, 4], [184, 4, 206, 2], [184, 5, 206, 3], [185, 4, 208, 2], [185, 10, 208, 8, "handleLongPress"], [185, 25, 208, 23], [185, 28, 208, 27, "message"], [185, 35, 208, 34], [185, 39, 208, 39], [186, 6, 209, 4, "setLongPressedMessage"], [186, 27, 209, 25], [186, 28, 209, 26, "message"], [186, 35, 209, 33], [186, 36, 209, 34], [187, 6, 210, 4, "setIsContextMenuVisible"], [187, 29, 210, 27], [187, 30, 210, 28], [187, 34, 210, 32], [187, 35, 210, 33], [188, 4, 211, 2], [188, 5, 211, 3], [189, 4, 213, 2], [189, 10, 213, 8, "handleCopyMessage"], [189, 27, 213, 25], [189, 30, 213, 28, "handleCopyMessage"], [189, 31, 213, 28], [189, 36, 213, 34], [190, 6, 214, 4], [190, 10, 214, 8, "longPressedMessage"], [190, 28, 214, 26], [190, 30, 214, 28], [191, 8, 215, 6, "Clipboard"], [191, 26, 215, 15], [191, 27, 215, 16, "setString"], [191, 36, 215, 25], [191, 37, 215, 26, "longPressedMessage"], [191, 55, 215, 44], [191, 56, 215, 45, "content"], [191, 63, 215, 52], [191, 64, 215, 53], [192, 8, 216, 6, "setIsContextMenuVisible"], [192, 31, 216, 29], [192, 32, 216, 30], [192, 37, 216, 35], [192, 38, 216, 36], [193, 8, 217, 6, "setLongPressedMessage"], [193, 29, 217, 27], [193, 30, 217, 28], [193, 34, 217, 32], [193, 35, 217, 33], [194, 6, 218, 4], [195, 4, 219, 2], [195, 5, 219, 3], [196, 4, 221, 2], [196, 10, 221, 8, "handleListenToMessage"], [196, 31, 221, 29], [196, 34, 221, 32, "handleListenToMessage"], [196, 35, 221, 32], [196, 40, 221, 38], [197, 6, 222, 4], [197, 10, 222, 8, "longPressedMessage"], [197, 28, 222, 26], [197, 30, 222, 28], [198, 8, 223, 6], [199, 8, 224, 6, "<PERSON><PERSON>"], [199, 22, 224, 11], [199, 23, 224, 12, "alert"], [199, 28, 224, 17], [199, 29, 224, 18], [199, 51, 224, 40], [199, 53, 224, 42, "longPressedMessage"], [199, 71, 224, 60], [199, 72, 224, 61, "content"], [199, 79, 224, 68], [199, 80, 224, 69], [200, 8, 225, 6, "setIsContextMenuVisible"], [200, 31, 225, 29], [200, 32, 225, 30], [200, 37, 225, 35], [200, 38, 225, 36], [201, 8, 226, 6, "setLongPressedMessage"], [201, 29, 226, 27], [201, 30, 226, 28], [201, 34, 226, 32], [201, 35, 226, 33], [202, 6, 227, 4], [203, 4, 228, 2], [203, 5, 228, 3], [204, 4, 230, 2], [204, 10, 230, 8, "handleMute"], [204, 20, 230, 18], [204, 23, 230, 21], [204, 29, 230, 21, "handleMute"], [204, 30, 230, 21], [204, 35, 230, 33], [205, 6, 231, 4], [205, 10, 231, 8], [205, 11, 231, 9, "hasPermission"], [205, 24, 231, 22], [205, 26, 231, 24], [206, 6, 233, 4], [206, 10, 233, 8], [207, 8, 234, 6], [207, 12, 234, 10, "isMuted"], [207, 19, 234, 17], [207, 21, 234, 19], [208, 10, 235, 8], [209, 10, 236, 8], [209, 14, 236, 12], [209, 15, 236, 13, "recorderState"], [209, 28, 236, 26], [209, 29, 236, 27, "isRecording"], [209, 40, 236, 38], [209, 42, 236, 40], [210, 12, 237, 10], [210, 18, 237, 16, "recorder"], [210, 26, 237, 24], [210, 27, 237, 25, "prepareToRecordAsync"], [210, 47, 237, 45], [210, 48, 237, 46], [210, 49, 237, 47], [211, 12, 238, 10, "recorder"], [211, 20, 238, 18], [211, 21, 238, 19, "record"], [211, 27, 238, 25], [211, 28, 238, 26], [211, 29, 238, 27], [212, 10, 239, 8], [213, 10, 240, 8, "setIsMuted"], [213, 20, 240, 18], [213, 21, 240, 19], [213, 26, 240, 24], [213, 27, 240, 25], [214, 10, 241, 8, "Haptics"], [214, 17, 241, 15], [214, 18, 241, 16, "impactAsync"], [214, 29, 241, 27], [214, 30, 241, 28, "Haptics"], [214, 37, 241, 35], [214, 38, 241, 36, "ImpactFeedbackStyle"], [214, 57, 241, 55], [214, 58, 241, 56, "Light"], [214, 63, 241, 61], [214, 64, 241, 62], [215, 8, 242, 6], [215, 9, 242, 7], [215, 15, 242, 13], [216, 10, 243, 8], [217, 10, 244, 8], [217, 14, 244, 12, "recorderState"], [217, 27, 244, 25], [217, 28, 244, 26, "isRecording"], [217, 39, 244, 37], [217, 41, 244, 39], [218, 12, 245, 10], [218, 18, 245, 16, "recorder"], [218, 26, 245, 24], [218, 27, 245, 25, "stop"], [218, 31, 245, 29], [218, 32, 245, 30], [218, 33, 245, 31], [219, 10, 246, 8], [220, 10, 247, 8, "setIsMuted"], [220, 20, 247, 18], [220, 21, 247, 19], [220, 25, 247, 23], [220, 26, 247, 24], [221, 10, 248, 8, "Haptics"], [221, 17, 248, 15], [221, 18, 248, 16, "impactAsync"], [221, 29, 248, 27], [221, 30, 248, 28, "Haptics"], [221, 37, 248, 35], [221, 38, 248, 36, "ImpactFeedbackStyle"], [221, 57, 248, 55], [221, 58, 248, 56, "Light"], [221, 63, 248, 61], [221, 64, 248, 62], [222, 8, 249, 6], [223, 6, 250, 4], [223, 7, 250, 5], [223, 8, 250, 6], [223, 15, 250, 13, "error"], [223, 20, 250, 18], [223, 22, 250, 20], [224, 8, 251, 6, "console"], [224, 15, 251, 13], [224, 16, 251, 14, "error"], [224, 21, 251, 19], [224, 22, 251, 20], [224, 47, 251, 45], [224, 49, 251, 47, "error"], [224, 54, 251, 52], [224, 55, 251, 53], [225, 6, 252, 4], [226, 4, 253, 2], [226, 5, 253, 3], [228, 4, 256, 2], [229, 4, 257, 2], [229, 8, 257, 2, "useEffect"], [229, 24, 257, 11], [229, 26, 257, 12], [229, 32, 257, 18], [230, 6, 258, 4], [230, 10, 258, 8, "scrollViewRef"], [230, 23, 258, 21], [230, 24, 258, 22, "current"], [230, 31, 258, 29], [230, 33, 258, 31], [231, 8, 259, 6, "scrollViewRef"], [231, 21, 259, 19], [231, 22, 259, 20, "current"], [231, 29, 259, 27], [231, 30, 259, 28, "scrollToEnd"], [231, 41, 259, 39], [231, 42, 259, 40], [232, 10, 259, 42, "animated"], [232, 18, 259, 50], [232, 20, 259, 52], [233, 8, 259, 57], [233, 9, 259, 58], [233, 10, 259, 59], [234, 6, 260, 4], [235, 4, 261, 2], [235, 5, 261, 3], [235, 7, 261, 5], [235, 8, 261, 6, "messages"], [235, 16, 261, 14], [235, 17, 261, 15], [235, 18, 261, 16], [236, 4, 263, 2], [236, 8, 263, 6], [236, 9, 263, 7, "fontsLoaded"], [236, 20, 263, 18], [236, 22, 263, 20], [237, 6, 264, 4], [237, 13, 264, 11], [237, 17, 264, 15], [238, 4, 265, 2], [240, 4, 267, 2], [241, 4, 268, 2], [241, 8, 268, 6, "isFirstTime"], [241, 19, 268, 17], [241, 21, 268, 19], [242, 6, 269, 4], [242, 26, 270, 6], [242, 30, 270, 6, "_jsxDevRuntime"], [242, 44, 270, 6], [242, 45, 270, 6, "jsxDEV"], [242, 51, 270, 6], [242, 53, 270, 7, "_View"], [242, 58, 270, 7], [242, 59, 270, 7, "default"], [242, 66, 270, 11], [243, 8, 270, 12, "style"], [243, 13, 270, 17], [243, 15, 270, 19], [244, 10, 270, 21, "flex"], [244, 14, 270, 25], [244, 16, 270, 27], [244, 17, 270, 28], [245, 10, 270, 30, "backgroundColor"], [245, 25, 270, 45], [245, 27, 270, 47, "colors"], [245, 33, 270, 53], [245, 34, 270, 54, "background"], [246, 8, 270, 65], [246, 9, 270, 67], [247, 8, 270, 67, "children"], [247, 16, 270, 67], [247, 31, 271, 8], [247, 35, 271, 8, "_jsxDevRuntime"], [247, 49, 271, 8], [247, 50, 271, 8, "jsxDEV"], [247, 56, 271, 8], [247, 58, 271, 9, "_View"], [247, 63, 271, 9], [247, 64, 271, 9, "default"], [247, 71, 271, 13], [248, 10, 272, 10, "style"], [248, 15, 272, 15], [248, 17, 272, 17], [249, 12, 273, 12, "flex"], [249, 16, 273, 16], [249, 18, 273, 18], [249, 19, 273, 19], [250, 12, 274, 12, "paddingTop"], [250, 22, 274, 22], [250, 24, 274, 24, "insets"], [250, 30, 274, 30], [250, 31, 274, 31, "top"], [250, 34, 274, 34], [250, 37, 274, 37], [250, 39, 274, 39], [251, 12, 275, 12, "paddingHorizontal"], [251, 29, 275, 29], [251, 31, 275, 31], [251, 33, 275, 33], [252, 12, 276, 12, "paddingBottom"], [252, 25, 276, 25], [252, 27, 276, 27, "insets"], [252, 33, 276, 33], [252, 34, 276, 34, "bottom"], [252, 40, 276, 40], [252, 43, 276, 43], [252, 45, 276, 45], [253, 12, 277, 12, "alignItems"], [253, 22, 277, 22], [253, 24, 277, 24], [253, 32, 277, 32], [254, 12, 278, 12, "justifyContent"], [254, 26, 278, 26], [254, 28, 278, 28], [255, 10, 279, 10], [255, 11, 279, 12], [256, 10, 279, 12, "children"], [256, 18, 279, 12], [256, 34, 281, 10], [256, 38, 281, 10, "_jsxDevRuntime"], [256, 52, 281, 10], [256, 53, 281, 10, "jsxDEV"], [256, 59, 281, 10], [256, 61, 281, 11, "_View"], [256, 66, 281, 11], [256, 67, 281, 11, "default"], [256, 74, 281, 15], [257, 12, 282, 12, "style"], [257, 17, 282, 17], [257, 19, 282, 19], [258, 14, 283, 14, "width"], [258, 19, 283, 19], [258, 21, 283, 21], [258, 24, 283, 24], [259, 14, 284, 14, "height"], [259, 20, 284, 20], [259, 22, 284, 22], [259, 25, 284, 25], [260, 14, 285, 14, "borderRadius"], [260, 26, 285, 26], [260, 28, 285, 28], [260, 30, 285, 30], [261, 14, 286, 14, "backgroundColor"], [261, 29, 286, 29], [261, 31, 286, 31, "colors"], [261, 37, 286, 37], [261, 38, 286, 38, "primaryUltraLight"], [261, 55, 286, 55], [262, 14, 287, 14, "alignItems"], [262, 24, 287, 24], [262, 26, 287, 26], [262, 34, 287, 34], [263, 14, 288, 14, "justifyContent"], [263, 28, 288, 28], [263, 30, 288, 30], [263, 38, 288, 38], [264, 14, 289, 14, "marginBottom"], [264, 26, 289, 26], [264, 28, 289, 28], [265, 12, 290, 12], [265, 13, 290, 14], [266, 12, 290, 14, "children"], [266, 20, 290, 14], [266, 35, 291, 12], [266, 39, 291, 12, "_jsxDevRuntime"], [266, 53, 291, 12], [266, 54, 291, 12, "jsxDEV"], [266, 60, 291, 12], [266, 62, 291, 13, "_lucideReactNative"], [266, 80, 291, 13], [266, 81, 291, 13, "MessageSquare"], [266, 94, 291, 26], [267, 14, 291, 27, "size"], [267, 18, 291, 31], [267, 20, 291, 33], [267, 22, 291, 36], [268, 14, 291, 37, "color"], [268, 19, 291, 42], [268, 21, 291, 44, "colors"], [268, 27, 291, 50], [268, 28, 291, 51, "primary"], [269, 12, 291, 59], [270, 14, 291, 59, "fileName"], [270, 22, 291, 59], [270, 24, 291, 59, "_jsxFileName"], [270, 36, 291, 59], [271, 14, 291, 59, "lineNumber"], [271, 24, 291, 59], [272, 14, 291, 59, "columnNumber"], [272, 26, 291, 59], [273, 12, 291, 59], [273, 19, 291, 61], [274, 10, 291, 62], [275, 12, 291, 62, "fileName"], [275, 20, 291, 62], [275, 22, 291, 62, "_jsxFileName"], [275, 34, 291, 62], [276, 12, 291, 62, "lineNumber"], [276, 22, 291, 62], [277, 12, 291, 62, "columnNumber"], [277, 24, 291, 62], [278, 10, 291, 62], [278, 17, 292, 16], [278, 18, 292, 17], [278, 33, 295, 10], [278, 37, 295, 10, "_jsxDevRuntime"], [278, 51, 295, 10], [278, 52, 295, 10, "jsxDEV"], [278, 58, 295, 10], [278, 60, 295, 11, "_Text"], [278, 65, 295, 11], [278, 66, 295, 11, "default"], [278, 73, 295, 15], [279, 12, 296, 12, "style"], [279, 17, 296, 17], [279, 19, 296, 19], [280, 14, 297, 14, "fontSize"], [280, 22, 297, 22], [280, 24, 297, 24], [280, 26, 297, 26], [281, 14, 298, 14, "fontFamily"], [281, 24, 298, 24], [281, 26, 298, 26], [281, 47, 298, 47], [282, 14, 299, 14, "color"], [282, 19, 299, 19], [282, 21, 299, 21, "colors"], [282, 27, 299, 27], [282, 28, 299, 28, "text"], [282, 32, 299, 32], [283, 14, 300, 14, "textAlign"], [283, 23, 300, 23], [283, 25, 300, 25], [283, 33, 300, 33], [284, 14, 301, 14, "marginBottom"], [284, 26, 301, 26], [284, 28, 301, 28], [285, 12, 302, 12], [285, 13, 302, 14], [286, 12, 302, 14, "children"], [286, 20, 302, 14], [286, 22, 302, 15], [287, 10, 304, 10], [288, 12, 304, 10, "fileName"], [288, 20, 304, 10], [288, 22, 304, 10, "_jsxFileName"], [288, 34, 304, 10], [289, 12, 304, 10, "lineNumber"], [289, 22, 304, 10], [290, 12, 304, 10, "columnNumber"], [290, 24, 304, 10], [291, 10, 304, 10], [291, 17, 304, 16], [291, 18, 304, 17], [291, 33, 307, 10], [291, 37, 307, 10, "_jsxDevRuntime"], [291, 51, 307, 10], [291, 52, 307, 10, "jsxDEV"], [291, 58, 307, 10], [291, 60, 307, 11, "_Text"], [291, 65, 307, 11], [291, 66, 307, 11, "default"], [291, 73, 307, 15], [292, 12, 308, 12, "style"], [292, 17, 308, 17], [292, 19, 308, 19], [293, 14, 309, 14, "fontSize"], [293, 22, 309, 22], [293, 24, 309, 24], [293, 26, 309, 26], [294, 14, 310, 14, "fontFamily"], [294, 24, 310, 24], [294, 26, 310, 26], [294, 46, 310, 46], [295, 14, 311, 14, "color"], [295, 19, 311, 19], [295, 21, 311, 21, "colors"], [295, 27, 311, 27], [295, 28, 311, 28, "textSecondary"], [295, 41, 311, 41], [296, 14, 312, 14, "textAlign"], [296, 23, 312, 23], [296, 25, 312, 25], [296, 33, 312, 33], [297, 14, 313, 14, "lineHeight"], [297, 24, 313, 24], [297, 26, 313, 26], [297, 28, 313, 28], [298, 14, 314, 14, "marginBottom"], [298, 26, 314, 26], [298, 28, 314, 28], [299, 12, 315, 12], [299, 13, 315, 14], [300, 12, 315, 14, "children"], [300, 20, 315, 14], [300, 22, 315, 15], [301, 10, 318, 10], [302, 12, 318, 10, "fileName"], [302, 20, 318, 10], [302, 22, 318, 10, "_jsxFileName"], [302, 34, 318, 10], [303, 12, 318, 10, "lineNumber"], [303, 22, 318, 10], [304, 12, 318, 10, "columnNumber"], [304, 24, 318, 10], [305, 10, 318, 10], [305, 17, 318, 16], [305, 18, 318, 17], [305, 33, 321, 10], [305, 37, 321, 10, "_jsxDevRuntime"], [305, 51, 321, 10], [305, 52, 321, 10, "jsxDEV"], [305, 58, 321, 10], [305, 60, 321, 11, "_TouchableOpacity"], [305, 77, 321, 11], [305, 78, 321, 11, "default"], [305, 85, 321, 27], [306, 12, 322, 12, "style"], [306, 17, 322, 17], [306, 19, 322, 19], [307, 14, 323, 14, "backgroundColor"], [307, 29, 323, 29], [307, 31, 323, 31, "colors"], [307, 37, 323, 37], [307, 38, 323, 38, "primary"], [307, 45, 323, 45], [308, 14, 324, 14, "borderRadius"], [308, 26, 324, 26], [308, 28, 324, 28], [308, 30, 324, 30], [309, 14, 325, 14, "paddingHorizontal"], [309, 31, 325, 31], [309, 33, 325, 33], [309, 35, 325, 35], [310, 14, 326, 14, "paddingVertical"], [310, 29, 326, 29], [310, 31, 326, 31], [310, 33, 326, 33], [311, 14, 327, 14, "min<PERSON><PERSON><PERSON>"], [311, 22, 327, 22], [311, 24, 327, 24], [311, 27, 327, 27], [312, 14, 328, 14, "alignItems"], [312, 24, 328, 24], [312, 26, 328, 26], [313, 12, 329, 12], [313, 13, 329, 14], [314, 12, 330, 12, "onPress"], [314, 19, 330, 19], [314, 21, 330, 21, "handleStartBrainstorming"], [314, 45, 330, 46], [315, 12, 330, 46, "children"], [315, 20, 330, 46], [315, 35, 331, 12], [315, 39, 331, 12, "_jsxDevRuntime"], [315, 53, 331, 12], [315, 54, 331, 12, "jsxDEV"], [315, 60, 331, 12], [315, 62, 331, 13, "_Text"], [315, 67, 331, 13], [315, 68, 331, 13, "default"], [315, 75, 331, 17], [316, 14, 332, 14, "style"], [316, 19, 332, 19], [316, 21, 332, 21], [317, 16, 333, 16, "fontSize"], [317, 24, 333, 24], [317, 26, 333, 26], [317, 28, 333, 28], [318, 16, 334, 16, "fontFamily"], [318, 26, 334, 26], [318, 28, 334, 28], [318, 49, 334, 49], [319, 16, 335, 16, "color"], [319, 21, 335, 21], [319, 23, 335, 23, "colors"], [319, 29, 335, 29], [319, 30, 335, 30, "background"], [320, 14, 336, 14], [320, 15, 336, 16], [321, 14, 336, 16, "children"], [321, 22, 336, 16], [321, 24, 336, 17], [322, 12, 338, 12], [323, 14, 338, 12, "fileName"], [323, 22, 338, 12], [323, 24, 338, 12, "_jsxFileName"], [323, 36, 338, 12], [324, 14, 338, 12, "lineNumber"], [324, 24, 338, 12], [325, 14, 338, 12, "columnNumber"], [325, 26, 338, 12], [326, 12, 338, 12], [326, 19, 338, 18], [327, 10, 338, 19], [328, 12, 338, 19, "fileName"], [328, 20, 338, 19], [328, 22, 338, 19, "_jsxFileName"], [328, 34, 338, 19], [329, 12, 338, 19, "lineNumber"], [329, 22, 338, 19], [330, 12, 338, 19, "columnNumber"], [330, 24, 338, 19], [331, 10, 338, 19], [331, 17, 339, 28], [331, 18, 339, 29], [332, 8, 339, 29], [333, 10, 339, 29, "fileName"], [333, 18, 339, 29], [333, 20, 339, 29, "_jsxFileName"], [333, 32, 339, 29], [334, 10, 339, 29, "lineNumber"], [334, 20, 339, 29], [335, 10, 339, 29, "columnNumber"], [335, 22, 339, 29], [336, 8, 339, 29], [336, 15, 340, 14], [337, 6, 340, 15], [338, 8, 340, 15, "fileName"], [338, 16, 340, 15], [338, 18, 340, 15, "_jsxFileName"], [338, 30, 340, 15], [339, 8, 340, 15, "lineNumber"], [339, 18, 340, 15], [340, 8, 340, 15, "columnNumber"], [340, 20, 340, 15], [341, 6, 340, 15], [341, 13, 341, 12], [341, 14, 341, 13], [342, 4, 343, 2], [344, 4, 345, 2], [345, 4, 346, 2], [345, 24, 347, 4], [345, 28, 347, 4, "_jsxDevRuntime"], [345, 42, 347, 4], [345, 43, 347, 4, "jsxDEV"], [345, 49, 347, 4], [345, 51, 347, 5, "_View"], [345, 56, 347, 5], [345, 57, 347, 5, "default"], [345, 64, 347, 9], [346, 6, 347, 10, "style"], [346, 11, 347, 15], [346, 13, 347, 17], [347, 8, 347, 19, "flex"], [347, 12, 347, 23], [347, 14, 347, 25], [347, 15, 347, 26], [348, 8, 347, 28, "backgroundColor"], [348, 23, 347, 43], [348, 25, 347, 45, "colors"], [348, 31, 347, 51], [348, 32, 347, 52, "background"], [349, 6, 347, 63], [349, 7, 347, 65], [350, 6, 347, 65, "children"], [350, 14, 347, 65], [350, 30, 348, 6], [350, 34, 348, 6, "_jsxDevRuntime"], [350, 48, 348, 6], [350, 49, 348, 6, "jsxDEV"], [350, 55, 348, 6], [350, 57, 348, 7, "_Header"], [350, 64, 348, 7], [350, 65, 348, 7, "default"], [350, 72, 348, 13], [351, 8, 349, 8, "voiceMode"], [351, 17, 349, 17], [351, 19, 349, 19, "voiceMode"], [351, 28, 349, 29], [352, 8, 350, 8, "onToggleVoiceMode"], [352, 25, 350, 25], [352, 27, 350, 27, "toggleVoiceMode"], [352, 42, 350, 43], [353, 8, 351, 8, "onDone"], [353, 14, 351, 14], [353, 16, 351, 16, "onDone"], [353, 17, 351, 16], [353, 22, 351, 22, "<PERSON><PERSON>"], [353, 36, 351, 27], [353, 37, 351, 28, "alert"], [353, 42, 351, 33], [353, 43, 351, 34], [353, 57, 351, 48], [354, 6, 351, 50], [355, 8, 351, 50, "fileName"], [355, 16, 351, 50], [355, 18, 351, 50, "_jsxFileName"], [355, 30, 351, 50], [356, 8, 351, 50, "lineNumber"], [356, 18, 351, 50], [357, 8, 351, 50, "columnNumber"], [357, 20, 351, 50], [358, 6, 351, 50], [358, 13, 352, 7], [358, 14, 352, 8], [358, 16, 353, 7, "voiceMode"], [358, 25, 353, 16], [358, 41, 354, 8], [358, 45, 354, 8, "_jsxDevRuntime"], [358, 59, 354, 8], [358, 60, 354, 8, "jsxDEV"], [358, 66, 354, 8], [358, 68, 354, 9, "_VoiceMode"], [358, 78, 354, 9], [358, 79, 354, 9, "default"], [358, 86, 354, 18], [359, 8, 355, 10, "isRecording"], [359, 19, 355, 21], [359, 21, 355, 23, "recorderState"], [359, 34, 355, 36], [359, 35, 355, 37, "isRecording"], [359, 46, 355, 49], [360, 8, 356, 10, "onRecord"], [360, 16, 356, 18], [360, 18, 356, 20, "handleVoiceRecord"], [360, 35, 356, 38], [361, 8, 357, 10, "hasPermission"], [361, 21, 357, 23], [361, 23, 357, 25, "hasPermission"], [361, 36, 357, 39], [362, 8, 358, 10, "isLoading"], [362, 17, 358, 19], [362, 19, 358, 21, "isLoading"], [362, 28, 358, 31], [363, 8, 359, 10, "transcript"], [363, 18, 359, 20], [363, 20, 359, 22, "transcript"], [363, 30, 359, 33], [364, 8, 360, 10, "isMuted"], [364, 15, 360, 17], [364, 17, 360, 19, "isMuted"], [364, 24, 360, 27], [365, 8, 361, 10, "onMute"], [365, 14, 361, 16], [365, 16, 361, 18, "handleMute"], [366, 6, 361, 29], [367, 8, 361, 29, "fileName"], [367, 16, 361, 29], [367, 18, 361, 29, "_jsxFileName"], [367, 30, 361, 29], [368, 8, 361, 29, "lineNumber"], [368, 18, 361, 29], [369, 8, 361, 29, "columnNumber"], [369, 20, 361, 29], [370, 6, 361, 29], [370, 13, 362, 9], [370, 14, 362, 10], [370, 30, 364, 8], [370, 34, 364, 8, "_jsxDevRuntime"], [370, 48, 364, 8], [370, 49, 364, 8, "jsxDEV"], [370, 55, 364, 8], [370, 57, 364, 9, "_KeyboardAvoidingAnimatedView"], [370, 86, 364, 9], [370, 87, 364, 9, "default"], [370, 94, 364, 37], [371, 8, 364, 38, "style"], [371, 13, 364, 43], [371, 15, 364, 45], [372, 10, 364, 47, "flex"], [372, 14, 364, 51], [372, 16, 364, 53], [373, 8, 364, 55], [373, 9, 364, 57], [374, 8, 364, 57, "children"], [374, 16, 364, 57], [374, 32, 366, 10], [374, 36, 366, 10, "_jsxDevRuntime"], [374, 50, 366, 10], [374, 51, 366, 10, "jsxDEV"], [374, 57, 366, 10], [374, 59, 366, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [374, 70, 366, 11], [374, 71, 366, 11, "default"], [374, 78, 366, 21], [375, 10, 367, 12, "ref"], [375, 13, 367, 15], [375, 15, 367, 17, "scrollViewRef"], [375, 28, 367, 31], [376, 10, 368, 12, "style"], [376, 15, 368, 17], [376, 17, 368, 19], [377, 12, 368, 21, "flex"], [377, 16, 368, 25], [377, 18, 368, 27], [378, 10, 368, 29], [378, 11, 368, 31], [379, 10, 369, 12, "contentContainerStyle"], [379, 31, 369, 33], [379, 33, 369, 35], [380, 12, 370, 14, "paddingHorizontal"], [380, 29, 370, 31], [380, 31, 370, 33], [380, 33, 370, 35], [381, 12, 371, 14, "paddingVertical"], [381, 27, 371, 29], [381, 29, 371, 31], [381, 31, 371, 33], [382, 12, 372, 14, "paddingBottom"], [382, 25, 372, 27], [382, 27, 372, 29], [382, 30, 372, 32], [382, 31, 372, 34], [383, 10, 373, 12], [383, 11, 373, 14], [384, 10, 374, 12, "showsVerticalScrollIndicator"], [384, 38, 374, 40], [384, 40, 374, 42], [384, 45, 374, 48], [385, 10, 374, 48, "children"], [385, 18, 374, 48], [385, 21, 375, 13, "messages"], [385, 29, 375, 21], [385, 30, 375, 22, "map"], [385, 33, 375, 25], [385, 34, 375, 26], [385, 35, 375, 27, "message"], [385, 42, 375, 34], [385, 44, 375, 36, "index"], [385, 49, 375, 41], [385, 67, 376, 14], [385, 71, 376, 14, "_jsxDevRuntime"], [385, 85, 376, 14], [385, 86, 376, 14, "jsxDEV"], [385, 92, 376, 14], [385, 94, 376, 15, "_MessageBubble"], [385, 108, 376, 15], [385, 109, 376, 15, "default"], [385, 116, 376, 28], [386, 12, 376, 41, "message"], [386, 19, 376, 48], [386, 21, 376, 50, "message"], [386, 28, 376, 58], [387, 12, 376, 59, "onLongPress"], [387, 23, 376, 70], [387, 25, 376, 72, "handleLongPress"], [388, 10, 376, 88], [388, 13, 376, 34, "index"], [388, 18, 376, 39], [389, 12, 376, 39, "fileName"], [389, 20, 376, 39], [389, 22, 376, 39, "_jsxFileName"], [389, 34, 376, 39], [390, 12, 376, 39, "lineNumber"], [390, 22, 376, 39], [391, 12, 376, 39, "columnNumber"], [391, 24, 376, 39], [392, 10, 376, 39], [392, 17, 376, 90], [392, 18, 377, 13], [392, 19, 377, 14], [392, 21, 380, 13, "isLoading"], [392, 30, 380, 22], [392, 47, 381, 14], [392, 51, 381, 14, "_jsxDevRuntime"], [392, 65, 381, 14], [392, 66, 381, 14, "jsxDEV"], [392, 72, 381, 14], [392, 74, 381, 15, "_View"], [392, 79, 381, 15], [392, 80, 381, 15, "default"], [392, 87, 381, 19], [393, 12, 382, 16, "style"], [393, 17, 382, 21], [393, 19, 382, 23], [394, 14, 383, 18, "marginBottom"], [394, 26, 383, 30], [394, 28, 383, 32], [394, 30, 383, 34], [395, 14, 384, 18, "alignSelf"], [395, 23, 384, 27], [395, 25, 384, 29], [395, 37, 384, 41], [396, 14, 385, 18, "max<PERSON><PERSON><PERSON>"], [396, 22, 385, 26], [396, 24, 385, 28], [397, 12, 386, 16], [397, 13, 386, 18], [398, 12, 386, 18, "children"], [398, 20, 386, 18], [398, 35, 387, 16], [398, 39, 387, 16, "_jsxDevRuntime"], [398, 53, 387, 16], [398, 54, 387, 16, "jsxDEV"], [398, 60, 387, 16], [398, 62, 387, 17, "_View"], [398, 67, 387, 17], [398, 68, 387, 17, "default"], [398, 75, 387, 21], [399, 14, 388, 18, "style"], [399, 19, 388, 23], [399, 21, 388, 25], [400, 16, 389, 20, "backgroundColor"], [400, 31, 389, 35], [400, 33, 389, 37, "colors"], [400, 39, 389, 43], [400, 40, 389, 44, "cardBackground"], [400, 54, 389, 58], [401, 16, 390, 20, "borderRadius"], [401, 28, 390, 32], [401, 30, 390, 34], [401, 32, 390, 36], [402, 16, 391, 20, "paddingHorizontal"], [402, 33, 391, 37], [402, 35, 391, 39], [402, 37, 391, 41], [403, 16, 392, 20, "paddingVertical"], [403, 31, 392, 35], [403, 33, 392, 37], [403, 35, 392, 39], [404, 16, 393, 20, "borderWidth"], [404, 27, 393, 31], [404, 29, 393, 33], [404, 30, 393, 34], [405, 16, 394, 20, "borderColor"], [405, 27, 394, 31], [405, 29, 394, 33, "colors"], [405, 35, 394, 39], [405, 36, 394, 40, "outline"], [406, 14, 395, 18], [406, 15, 395, 20], [407, 14, 395, 20, "children"], [407, 22, 395, 20], [407, 37, 396, 18], [407, 41, 396, 18, "_jsxDevRuntime"], [407, 55, 396, 18], [407, 56, 396, 18, "jsxDEV"], [407, 62, 396, 18], [407, 64, 396, 19, "_Text"], [407, 69, 396, 19], [407, 70, 396, 19, "default"], [407, 77, 396, 23], [408, 16, 397, 20, "style"], [408, 21, 397, 25], [408, 23, 397, 27], [409, 18, 398, 22, "fontSize"], [409, 26, 398, 30], [409, 28, 398, 32], [409, 30, 398, 34], [410, 18, 399, 22, "fontFamily"], [410, 28, 399, 32], [410, 30, 399, 34], [410, 50, 399, 54], [411, 18, 400, 22, "color"], [411, 23, 400, 27], [411, 25, 400, 29, "colors"], [411, 31, 400, 35], [411, 32, 400, 36, "textSecondary"], [411, 45, 400, 49], [412, 18, 401, 22, "lineHeight"], [412, 28, 401, 32], [412, 30, 401, 34], [413, 16, 402, 20], [413, 17, 402, 22], [414, 16, 402, 22, "children"], [414, 24, 402, 22], [414, 26, 402, 23], [415, 14, 404, 18], [416, 16, 404, 18, "fileName"], [416, 24, 404, 18], [416, 26, 404, 18, "_jsxFileName"], [416, 38, 404, 18], [417, 16, 404, 18, "lineNumber"], [417, 26, 404, 18], [418, 16, 404, 18, "columnNumber"], [418, 28, 404, 18], [419, 14, 404, 18], [419, 21, 404, 24], [420, 12, 404, 25], [421, 14, 404, 25, "fileName"], [421, 22, 404, 25], [421, 24, 404, 25, "_jsxFileName"], [421, 36, 404, 25], [422, 14, 404, 25, "lineNumber"], [422, 24, 404, 25], [423, 14, 404, 25, "columnNumber"], [423, 26, 404, 25], [424, 12, 404, 25], [424, 19, 405, 22], [425, 10, 405, 23], [426, 12, 405, 23, "fileName"], [426, 20, 405, 23], [426, 22, 405, 23, "_jsxFileName"], [426, 34, 405, 23], [427, 12, 405, 23, "lineNumber"], [427, 22, 405, 23], [428, 12, 405, 23, "columnNumber"], [428, 24, 405, 23], [429, 10, 405, 23], [429, 17, 406, 20], [429, 18, 407, 13], [430, 8, 407, 13], [431, 10, 407, 13, "fileName"], [431, 18, 407, 13], [431, 20, 407, 13, "_jsxFileName"], [431, 32, 407, 13], [432, 10, 407, 13, "lineNumber"], [432, 20, 407, 13], [433, 10, 407, 13, "columnNumber"], [433, 22, 407, 13], [434, 8, 407, 13], [434, 15, 408, 22], [434, 16, 408, 23], [434, 18, 411, 11, "quickActions"], [434, 30, 411, 23], [434, 31, 411, 24, "length"], [434, 37, 411, 30], [434, 40, 411, 33], [434, 41, 411, 34], [434, 58, 412, 12], [434, 62, 412, 12, "_jsxDevRuntime"], [434, 76, 412, 12], [434, 77, 412, 12, "jsxDEV"], [434, 83, 412, 12], [434, 85, 412, 13, "_View"], [434, 90, 412, 13], [434, 91, 412, 13, "default"], [434, 98, 412, 17], [435, 10, 413, 14, "style"], [435, 15, 413, 19], [435, 17, 413, 21], [436, 12, 414, 16, "position"], [436, 20, 414, 24], [436, 22, 414, 26], [436, 32, 414, 36], [437, 12, 415, 16, "bottom"], [437, 18, 415, 22], [437, 20, 415, 24, "insets"], [437, 26, 415, 30], [437, 27, 415, 31, "bottom"], [437, 33, 415, 37], [437, 36, 415, 40], [437, 39, 415, 43], [438, 12, 416, 16, "left"], [438, 16, 416, 20], [438, 18, 416, 22], [438, 20, 416, 24], [439, 12, 417, 16, "right"], [439, 17, 417, 21], [439, 19, 417, 23], [440, 10, 418, 14], [440, 11, 418, 16], [441, 10, 418, 16, "children"], [441, 18, 418, 16], [441, 33, 419, 14], [441, 37, 419, 14, "_jsxDevRuntime"], [441, 51, 419, 14], [441, 52, 419, 14, "jsxDEV"], [441, 58, 419, 14], [441, 60, 419, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [441, 71, 419, 15], [441, 72, 419, 15, "default"], [441, 79, 419, 25], [442, 12, 420, 16, "horizontal"], [442, 22, 420, 26], [443, 12, 421, 16, "showsHorizontalScrollIndicator"], [443, 42, 421, 46], [443, 44, 421, 48], [443, 49, 421, 54], [444, 12, 422, 16, "contentContainerStyle"], [444, 33, 422, 37], [444, 35, 422, 39], [445, 14, 422, 41, "gap"], [445, 17, 422, 44], [445, 19, 422, 46], [446, 12, 422, 48], [446, 13, 422, 50], [447, 12, 422, 50, "children"], [447, 20, 422, 50], [447, 22, 423, 17, "quickActions"], [447, 34, 423, 29], [447, 35, 423, 30, "map"], [447, 38, 423, 33], [447, 39, 423, 34], [447, 40, 423, 35, "action"], [447, 46, 423, 41], [447, 48, 423, 43, "index"], [447, 53, 423, 48], [447, 71, 424, 18], [447, 75, 424, 18, "_jsxDevRuntime"], [447, 89, 424, 18], [447, 90, 424, 18, "jsxDEV"], [447, 96, 424, 18], [447, 98, 424, 19, "_TouchableOpacity"], [447, 115, 424, 19], [447, 116, 424, 19, "default"], [447, 123, 424, 35], [448, 14, 426, 20, "style"], [448, 19, 426, 25], [448, 21, 426, 27], [449, 16, 427, 22, "backgroundColor"], [449, 31, 427, 37], [449, 33, 427, 39, "colors"], [449, 39, 427, 45], [449, 40, 427, 46, "primaryUltraLight"], [449, 57, 427, 63], [450, 16, 428, 22, "borderRadius"], [450, 28, 428, 34], [450, 30, 428, 36], [450, 32, 428, 38], [451, 16, 429, 22, "paddingHorizontal"], [451, 33, 429, 39], [451, 35, 429, 41], [451, 37, 429, 43], [452, 16, 430, 22, "paddingVertical"], [452, 31, 430, 37], [452, 33, 430, 39], [452, 34, 430, 40], [453, 16, 431, 22, "borderWidth"], [453, 27, 431, 33], [453, 29, 431, 35], [453, 30, 431, 36], [454, 16, 432, 22, "borderColor"], [454, 27, 432, 33], [454, 29, 432, 35, "colors"], [454, 35, 432, 41], [454, 36, 432, 42, "primary"], [455, 14, 433, 20], [455, 15, 433, 22], [456, 14, 434, 20, "onPress"], [456, 21, 434, 27], [456, 23, 434, 29, "onPress"], [456, 24, 434, 29], [456, 29, 434, 35, "handleQuickAction"], [456, 46, 434, 52], [456, 47, 434, 53, "action"], [456, 53, 434, 59], [456, 54, 434, 61], [457, 14, 434, 61, "children"], [457, 22, 434, 61], [457, 37, 435, 20], [457, 41, 435, 20, "_jsxDevRuntime"], [457, 55, 435, 20], [457, 56, 435, 20, "jsxDEV"], [457, 62, 435, 20], [457, 64, 435, 21, "_Text"], [457, 69, 435, 21], [457, 70, 435, 21, "default"], [457, 77, 435, 25], [458, 16, 436, 22, "style"], [458, 21, 436, 27], [458, 23, 436, 29], [459, 18, 437, 24, "fontSize"], [459, 26, 437, 32], [459, 28, 437, 34], [459, 30, 437, 36], [460, 18, 438, 24, "fontFamily"], [460, 28, 438, 34], [460, 30, 438, 36], [460, 49, 438, 55], [461, 18, 439, 24, "color"], [461, 23, 439, 29], [461, 25, 439, 31, "colors"], [461, 31, 439, 37], [461, 32, 439, 38, "primary"], [462, 16, 440, 22], [462, 17, 440, 24], [463, 16, 440, 24, "children"], [463, 24, 440, 24], [463, 26, 441, 23, "action"], [464, 14, 441, 29], [465, 16, 441, 29, "fileName"], [465, 24, 441, 29], [465, 26, 441, 29, "_jsxFileName"], [465, 38, 441, 29], [466, 16, 441, 29, "lineNumber"], [466, 26, 441, 29], [467, 16, 441, 29, "columnNumber"], [467, 28, 441, 29], [468, 14, 441, 29], [468, 21, 442, 26], [469, 12, 442, 27], [469, 15, 425, 25, "index"], [469, 20, 425, 30], [470, 14, 425, 30, "fileName"], [470, 22, 425, 30], [470, 24, 425, 30, "_jsxFileName"], [470, 36, 425, 30], [471, 14, 425, 30, "lineNumber"], [471, 24, 425, 30], [472, 14, 425, 30, "columnNumber"], [472, 26, 425, 30], [473, 12, 425, 30], [473, 19, 443, 36], [473, 20, 444, 17], [474, 10, 444, 18], [475, 12, 444, 18, "fileName"], [475, 20, 444, 18], [475, 22, 444, 18, "_jsxFileName"], [475, 34, 444, 18], [476, 12, 444, 18, "lineNumber"], [476, 22, 444, 18], [477, 12, 444, 18, "columnNumber"], [477, 24, 444, 18], [478, 10, 444, 18], [478, 17, 445, 26], [479, 8, 445, 27], [480, 10, 445, 27, "fileName"], [480, 18, 445, 27], [480, 20, 445, 27, "_jsxFileName"], [480, 32, 445, 27], [481, 10, 445, 27, "lineNumber"], [481, 20, 445, 27], [482, 10, 445, 27, "columnNumber"], [482, 22, 445, 27], [483, 8, 445, 27], [483, 15, 446, 18], [483, 16, 447, 11], [483, 31, 450, 10], [483, 35, 450, 10, "_jsxDevRuntime"], [483, 49, 450, 10], [483, 50, 450, 10, "jsxDEV"], [483, 56, 450, 10], [483, 58, 450, 11, "_View"], [483, 63, 450, 11], [483, 64, 450, 11, "default"], [483, 71, 450, 15], [484, 10, 451, 12, "style"], [484, 15, 451, 17], [484, 17, 451, 19], [485, 12, 452, 14, "position"], [485, 20, 452, 22], [485, 22, 452, 24], [485, 32, 452, 34], [486, 12, 453, 14, "bottom"], [486, 18, 453, 20], [486, 20, 453, 22], [486, 21, 453, 23], [487, 12, 454, 14, "left"], [487, 16, 454, 18], [487, 18, 454, 20], [487, 19, 454, 21], [488, 12, 455, 14, "right"], [488, 17, 455, 19], [488, 19, 455, 21], [489, 10, 456, 12], [489, 11, 456, 14], [490, 10, 456, 14, "children"], [490, 18, 456, 14], [490, 33, 457, 12], [490, 37, 457, 12, "_jsxDevRuntime"], [490, 51, 457, 12], [490, 52, 457, 12, "jsxDEV"], [490, 58, 457, 12], [490, 60, 457, 13, "_TextMode"], [490, 69, 457, 13], [490, 70, 457, 13, "default"], [490, 77, 457, 21], [491, 12, 458, 14, "inputText"], [491, 21, 458, 23], [491, 23, 458, 25, "inputText"], [491, 32, 458, 35], [492, 12, 459, 14, "onInputChange"], [492, 25, 459, 27], [492, 27, 459, 29, "setInputText"], [492, 39, 459, 42], [493, 12, 460, 14, "onSendMessage"], [493, 25, 460, 27], [493, 27, 460, 29, "onSendMessage"], [493, 28, 460, 29], [493, 33, 460, 35, "handleSendMessage"], [493, 50, 460, 52], [493, 51, 460, 53], [493, 52, 460, 55], [494, 12, 461, 14, "onStartDictation"], [494, 28, 461, 30], [494, 30, 461, 32, "handleDictation"], [495, 10, 461, 48], [496, 12, 461, 48, "fileName"], [496, 20, 461, 48], [496, 22, 461, 48, "_jsxFileName"], [496, 34, 461, 48], [497, 12, 461, 48, "lineNumber"], [497, 22, 461, 48], [498, 12, 461, 48, "columnNumber"], [498, 24, 461, 48], [499, 10, 461, 48], [499, 17, 462, 13], [500, 8, 462, 14], [501, 10, 462, 14, "fileName"], [501, 18, 462, 14], [501, 20, 462, 14, "_jsxFileName"], [501, 32, 462, 14], [502, 10, 462, 14, "lineNumber"], [502, 20, 462, 14], [503, 10, 462, 14, "columnNumber"], [503, 22, 462, 14], [504, 8, 462, 14], [504, 15, 463, 16], [504, 16, 463, 17], [505, 6, 463, 17], [506, 8, 463, 17, "fileName"], [506, 16, 463, 17], [506, 18, 463, 17, "_jsxFileName"], [506, 30, 463, 17], [507, 8, 463, 17, "lineNumber"], [507, 18, 463, 17], [508, 8, 463, 17, "columnNumber"], [508, 20, 463, 17], [509, 6, 463, 17], [509, 13, 464, 38], [509, 14, 465, 7], [509, 29, 468, 6], [509, 33, 468, 6, "_jsxDevRuntime"], [509, 47, 468, 6], [509, 48, 468, 6, "jsxDEV"], [509, 54, 468, 6], [509, 56, 468, 7, "_Modal"], [509, 62, 468, 7], [509, 63, 468, 7, "default"], [509, 70, 468, 12], [510, 8, 469, 8, "transparent"], [510, 19, 469, 19], [511, 8, 470, 8, "visible"], [511, 15, 470, 15], [511, 17, 470, 17, "isContextMenuVisible"], [511, 37, 470, 38], [512, 8, 471, 8, "onRequestClose"], [512, 22, 471, 22], [512, 24, 471, 24, "onRequestClose"], [512, 25, 471, 24], [512, 30, 471, 30, "setIsContextMenuVisible"], [512, 53, 471, 53], [512, 54, 471, 54], [512, 59, 471, 59], [512, 60, 471, 61], [513, 8, 471, 61, "children"], [513, 16, 471, 61], [513, 31, 473, 8], [513, 35, 473, 8, "_jsxDevRuntime"], [513, 49, 473, 8], [513, 50, 473, 8, "jsxDEV"], [513, 56, 473, 8], [513, 58, 473, 9, "_TouchableOpacity"], [513, 75, 473, 9], [513, 76, 473, 9, "default"], [513, 83, 473, 25], [514, 10, 474, 10, "style"], [514, 15, 474, 15], [514, 17, 474, 17], [515, 12, 474, 19, "flex"], [515, 16, 474, 23], [515, 18, 474, 25], [515, 19, 474, 26], [516, 12, 474, 28, "backgroundColor"], [516, 27, 474, 43], [516, 29, 474, 45], [516, 46, 474, 62], [517, 12, 474, 64, "justifyContent"], [517, 26, 474, 78], [517, 28, 474, 80], [517, 36, 474, 88], [518, 12, 474, 90, "alignItems"], [518, 22, 474, 100], [518, 24, 474, 102], [519, 10, 474, 111], [519, 11, 474, 113], [520, 10, 475, 10, "activeOpacity"], [520, 23, 475, 23], [520, 25, 475, 25], [520, 26, 475, 27], [521, 10, 476, 10, "onPressOut"], [521, 20, 476, 20], [521, 22, 476, 22, "onPressOut"], [521, 23, 476, 22], [521, 28, 476, 28, "setIsContextMenuVisible"], [521, 51, 476, 51], [521, 52, 476, 52], [521, 57, 476, 57], [521, 58, 476, 59], [522, 10, 476, 59, "children"], [522, 18, 476, 59], [522, 33, 478, 10], [522, 37, 478, 10, "_jsxDevRuntime"], [522, 51, 478, 10], [522, 52, 478, 10, "jsxDEV"], [522, 58, 478, 10], [522, 60, 478, 11, "_View"], [522, 65, 478, 11], [522, 66, 478, 11, "default"], [522, 73, 478, 15], [523, 12, 478, 16, "style"], [523, 17, 478, 21], [523, 19, 478, 23], [524, 14, 478, 25, "backgroundColor"], [524, 29, 478, 40], [524, 31, 478, 42, "colors"], [524, 37, 478, 48], [524, 38, 478, 49, "background"], [524, 48, 478, 59], [525, 14, 478, 61, "borderRadius"], [525, 26, 478, 73], [525, 28, 478, 75], [525, 30, 478, 77], [526, 14, 478, 79, "padding"], [526, 21, 478, 86], [526, 23, 478, 88], [526, 25, 478, 90], [527, 14, 478, 92, "width"], [527, 19, 478, 97], [527, 21, 478, 99], [528, 12, 478, 105], [528, 13, 478, 107], [529, 12, 478, 107, "children"], [529, 20, 478, 107], [529, 36, 479, 12], [529, 40, 479, 12, "_jsxDevRuntime"], [529, 54, 479, 12], [529, 55, 479, 12, "jsxDEV"], [529, 61, 479, 12], [529, 63, 479, 13, "_TouchableOpacity"], [529, 80, 479, 13], [529, 81, 479, 13, "default"], [529, 88, 479, 29], [530, 14, 479, 30, "onPress"], [530, 21, 479, 37], [530, 23, 479, 39, "handleCopyMessage"], [530, 40, 479, 57], [531, 14, 479, 58, "style"], [531, 19, 479, 63], [531, 21, 479, 65], [532, 16, 479, 67, "paddingVertical"], [532, 31, 479, 82], [532, 33, 479, 84], [533, 14, 479, 87], [533, 15, 479, 89], [534, 14, 479, 89, "children"], [534, 22, 479, 89], [534, 37, 480, 14], [534, 41, 480, 14, "_jsxDevRuntime"], [534, 55, 480, 14], [534, 56, 480, 14, "jsxDEV"], [534, 62, 480, 14], [534, 64, 480, 15, "_Text"], [534, 69, 480, 15], [534, 70, 480, 15, "default"], [534, 77, 480, 19], [535, 16, 480, 20, "style"], [535, 21, 480, 25], [535, 23, 480, 27], [536, 18, 480, 29, "fontSize"], [536, 26, 480, 37], [536, 28, 480, 39], [536, 30, 480, 41], [537, 18, 480, 43, "fontFamily"], [537, 28, 480, 53], [537, 30, 480, 55], [537, 49, 480, 74], [538, 18, 480, 76, "color"], [538, 23, 480, 81], [538, 25, 480, 83, "colors"], [538, 31, 480, 89], [538, 32, 480, 90, "text"], [539, 16, 480, 95], [539, 17, 480, 97], [540, 16, 480, 97, "children"], [540, 24, 480, 97], [540, 26, 480, 98], [541, 14, 480, 110], [542, 16, 480, 110, "fileName"], [542, 24, 480, 110], [542, 26, 480, 110, "_jsxFileName"], [542, 38, 480, 110], [543, 16, 480, 110, "lineNumber"], [543, 26, 480, 110], [544, 16, 480, 110, "columnNumber"], [544, 28, 480, 110], [545, 14, 480, 110], [545, 21, 480, 116], [546, 12, 480, 117], [547, 14, 480, 117, "fileName"], [547, 22, 480, 117], [547, 24, 480, 117, "_jsxFileName"], [547, 36, 480, 117], [548, 14, 480, 117, "lineNumber"], [548, 24, 480, 117], [549, 14, 480, 117, "columnNumber"], [549, 26, 480, 117], [550, 12, 480, 117], [550, 19, 481, 30], [550, 20, 481, 31], [550, 22, 482, 13, "longPressedMessage"], [550, 40, 482, 31], [550, 42, 482, 33, "role"], [550, 46, 482, 37], [550, 51, 482, 42], [550, 62, 482, 53], [550, 79, 483, 14], [550, 83, 483, 14, "_jsxDevRuntime"], [550, 97, 483, 14], [550, 98, 483, 14, "jsxDEV"], [550, 104, 483, 14], [550, 106, 483, 15, "_TouchableOpacity"], [550, 123, 483, 15], [550, 124, 483, 15, "default"], [550, 131, 483, 31], [551, 14, 483, 32, "onPress"], [551, 21, 483, 39], [551, 23, 483, 41, "handleListenToMessage"], [551, 44, 483, 63], [552, 14, 483, 64, "style"], [552, 19, 483, 69], [552, 21, 483, 71], [553, 16, 483, 73, "paddingVertical"], [553, 31, 483, 88], [553, 33, 483, 90], [554, 14, 483, 93], [554, 15, 483, 95], [555, 14, 483, 95, "children"], [555, 22, 483, 95], [555, 37, 484, 16], [555, 41, 484, 16, "_jsxDevRuntime"], [555, 55, 484, 16], [555, 56, 484, 16, "jsxDEV"], [555, 62, 484, 16], [555, 64, 484, 17, "_Text"], [555, 69, 484, 17], [555, 70, 484, 17, "default"], [555, 77, 484, 21], [556, 16, 484, 22, "style"], [556, 21, 484, 27], [556, 23, 484, 29], [557, 18, 484, 31, "fontSize"], [557, 26, 484, 39], [557, 28, 484, 41], [557, 30, 484, 43], [558, 18, 484, 45, "fontFamily"], [558, 28, 484, 55], [558, 30, 484, 57], [558, 49, 484, 76], [559, 18, 484, 78, "color"], [559, 23, 484, 83], [559, 25, 484, 85, "colors"], [559, 31, 484, 91], [559, 32, 484, 92, "text"], [560, 16, 484, 97], [560, 17, 484, 99], [561, 16, 484, 99, "children"], [561, 24, 484, 99], [561, 26, 484, 100], [562, 14, 484, 117], [563, 16, 484, 117, "fileName"], [563, 24, 484, 117], [563, 26, 484, 117, "_jsxFileName"], [563, 38, 484, 117], [564, 16, 484, 117, "lineNumber"], [564, 26, 484, 117], [565, 16, 484, 117, "columnNumber"], [565, 28, 484, 117], [566, 14, 484, 117], [566, 21, 484, 123], [567, 12, 484, 124], [568, 14, 484, 124, "fileName"], [568, 22, 484, 124], [568, 24, 484, 124, "_jsxFileName"], [568, 36, 484, 124], [569, 14, 484, 124, "lineNumber"], [569, 24, 484, 124], [570, 14, 484, 124, "columnNumber"], [570, 26, 484, 124], [571, 12, 484, 124], [571, 19, 485, 32], [571, 20, 486, 13], [572, 10, 486, 13], [573, 12, 486, 13, "fileName"], [573, 20, 486, 13], [573, 22, 486, 13, "_jsxFileName"], [573, 34, 486, 13], [574, 12, 486, 13, "lineNumber"], [574, 22, 486, 13], [575, 12, 486, 13, "columnNumber"], [575, 24, 486, 13], [576, 10, 486, 13], [576, 17, 487, 16], [577, 8, 487, 17], [578, 10, 487, 17, "fileName"], [578, 18, 487, 17], [578, 20, 487, 17, "_jsxFileName"], [578, 32, 487, 17], [579, 10, 487, 17, "lineNumber"], [579, 20, 487, 17], [580, 10, 487, 17, "columnNumber"], [580, 22, 487, 17], [581, 8, 487, 17], [581, 15, 488, 26], [582, 6, 488, 27], [583, 8, 488, 27, "fileName"], [583, 16, 488, 27], [583, 18, 488, 27, "_jsxFileName"], [583, 30, 488, 27], [584, 8, 488, 27, "lineNumber"], [584, 18, 488, 27], [585, 8, 488, 27, "columnNumber"], [585, 20, 488, 27], [586, 6, 488, 27], [586, 13, 489, 13], [586, 14, 489, 14], [587, 4, 489, 14], [588, 6, 489, 14, "fileName"], [588, 14, 489, 14], [588, 16, 489, 14, "_jsxFileName"], [588, 28, 489, 14], [589, 6, 489, 14, "lineNumber"], [589, 16, 489, 14], [590, 6, 489, 14, "columnNumber"], [590, 18, 489, 14], [591, 4, 489, 14], [591, 11, 490, 10], [591, 12, 490, 11], [592, 2, 492, 0], [593, 2, 492, 1, "_s"], [593, 4, 492, 1], [593, 5, 34, 24, "BrainstormScreen"], [593, 21, 34, 40], [594, 4, 34, 40], [594, 12, 35, 17, "useSafeAreaInsets"], [594, 57, 35, 34], [594, 59, 36, 17, "useColors"], [594, 79, 36, 26], [594, 81, 37, 24, "useFonts"], [594, 98, 37, 32], [594, 100, 43, 19, "useAudioRecorder"], [594, 127, 43, 35], [594, 129, 44, 24, "useAudioRecorderState"], [594, 161, 44, 45], [595, 2, 44, 45], [596, 2, 44, 45, "_c"], [596, 4, 44, 45], [596, 7, 34, 24, "BrainstormScreen"], [596, 23, 34, 40], [597, 2, 34, 40], [597, 6, 34, 40, "_c"], [597, 8, 34, 40], [598, 2, 34, 40, "$RefreshReg$"], [598, 14, 34, 40], [598, 15, 34, 40, "_c"], [598, 17, 34, 40], [599, 0, 34, 40], [599, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "handleVoiceRecord", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCiC;YC4B;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJK;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVwB;0BWE;GX0B;0BYE;GZG;4BaE;GbM;gCcE;GdO;qBeE;GfuB;YCI;GDI;gBgB0F,iChB;0BiBwB;ajBE;kCkB8C;6BCW,+BD;iBlBU;6BoBgB,yBpB;wBqBW,oCrB;sBsBK,oCtB"}}, "type": "js/module"}]}