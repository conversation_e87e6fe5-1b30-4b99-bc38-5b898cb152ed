{"dependencies": [{"name": "./CommonActions.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "PtqAa1NphRFgpMNqtsHOtuJIKuM=", "exportNames": ["*"]}}, {"name": "./BaseRouter.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 94}, "end": {"line": 5, "column": 45, "index": 139}}], "key": "dJts27xOtIr7LbP/D7m0z2vzxHI=", "exportNames": ["*"]}}, {"name": "./DrawerRouter.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 140}, "end": {"line": 6, "column": 64, "index": 204}}], "key": "4gTrrbHRvavUnTU42cP/PMqKsB0=", "exportNames": ["*"]}}, {"name": "./StackRouter.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 205}, "end": {"line": 7, "column": 61, "index": 266}}], "key": "wzhmXYqJrFHNGabWcnHmzt476nE=", "exportNames": ["*"]}}, {"name": "./TabRouter.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 267}, "end": {"line": 8, "column": 55, "index": 322}}], "key": "jC/Bg2g+cpTuZwE6Bq9LoiYVdNc=", "exportNames": ["*"]}}, {"name": "./types.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 323}, "end": {"line": 9, "column": 27, "index": 350}}], "key": "yJvqu7zVoaSgx/LOxsKU/6eppkQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    CommonActions: true,\n    BaseRouter: true,\n    DrawerActions: true,\n    DrawerRouter: true,\n    StackActions: true,\n    StackRouter: true,\n    TabActions: true,\n    TabRouter: true\n  };\n  Object.defineProperty(exports, \"BaseRouter\", {\n    enumerable: true,\n    get: function () {\n      return _BaseRouter.BaseRouter;\n    }\n  });\n  exports.CommonActions = void 0;\n  Object.defineProperty(exports, \"DrawerActions\", {\n    enumerable: true,\n    get: function () {\n      return _DrawerRouter.DrawerActions;\n    }\n  });\n  Object.defineProperty(exports, \"DrawerRouter\", {\n    enumerable: true,\n    get: function () {\n      return _DrawerRouter.DrawerRouter;\n    }\n  });\n  Object.defineProperty(exports, \"StackActions\", {\n    enumerable: true,\n    get: function () {\n      return _StackRouter.StackActions;\n    }\n  });\n  Object.defineProperty(exports, \"StackRouter\", {\n    enumerable: true,\n    get: function () {\n      return _StackRouter.StackRouter;\n    }\n  });\n  Object.defineProperty(exports, \"TabActions\", {\n    enumerable: true,\n    get: function () {\n      return _TabRouter.TabActions;\n    }\n  });\n  Object.defineProperty(exports, \"TabRouter\", {\n    enumerable: true,\n    get: function () {\n      return _TabRouter.TabRouter;\n    }\n  });\n  var CommonActions = _interopRequireWildcard(require(_dependencyMap[0], \"./CommonActions.js\"));\n  exports.CommonActions = CommonActions;\n  var _BaseRouter = require(_dependencyMap[1], \"./BaseRouter.js\");\n  var _DrawerRouter = require(_dependencyMap[2], \"./DrawerRouter.js\");\n  var _StackRouter = require(_dependencyMap[3], \"./StackRouter.js\");\n  var _TabRouter = require(_dependencyMap[4], \"./TabRouter.js\");\n  var _types = require(_dependencyMap[5], \"./types.js\");\n  Object.keys(_types).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _types[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _types[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n});", "lineCount": 79, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13], [7, 6, 1, 13, "_exportNames"], [7, 18, 1, 13], [8, 4, 1, 13, "CommonActions"], [8, 17, 1, 13], [9, 4, 1, 13, "BaseRouter"], [9, 14, 1, 13], [10, 4, 1, 13, "DrawerActions"], [10, 17, 1, 13], [11, 4, 1, 13, "DrawerRouter"], [11, 16, 1, 13], [12, 4, 1, 13, "StackActions"], [12, 16, 1, 13], [13, 4, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [13, 15, 1, 13], [14, 4, 1, 13, "TabActions"], [14, 14, 1, 13], [15, 4, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 13, 1, 13], [16, 2, 1, 13], [17, 2, 1, 13, "Object"], [17, 8, 1, 13], [17, 9, 1, 13, "defineProperty"], [17, 23, 1, 13], [17, 24, 1, 13, "exports"], [17, 31, 1, 13], [18, 4, 1, 13, "enumerable"], [18, 14, 1, 13], [19, 4, 1, 13, "get"], [19, 7, 1, 13], [19, 18, 1, 13, "get"], [19, 19, 1, 13], [20, 6, 1, 13], [20, 13, 1, 13, "_BaseRouter"], [20, 24, 1, 13], [20, 25, 1, 13, "BaseRouter"], [20, 35, 1, 13], [21, 4, 1, 13], [22, 2, 1, 13], [23, 2, 1, 13, "exports"], [23, 9, 1, 13], [23, 10, 1, 13, "CommonActions"], [23, 23, 1, 13], [24, 2, 1, 13, "Object"], [24, 8, 1, 13], [24, 9, 1, 13, "defineProperty"], [24, 23, 1, 13], [24, 24, 1, 13, "exports"], [24, 31, 1, 13], [25, 4, 1, 13, "enumerable"], [25, 14, 1, 13], [26, 4, 1, 13, "get"], [26, 7, 1, 13], [26, 18, 1, 13, "get"], [26, 19, 1, 13], [27, 6, 1, 13], [27, 13, 1, 13, "_DrawerRouter"], [27, 26, 1, 13], [27, 27, 1, 13, "DrawerActions"], [27, 40, 1, 13], [28, 4, 1, 13], [29, 2, 1, 13], [30, 2, 1, 13, "Object"], [30, 8, 1, 13], [30, 9, 1, 13, "defineProperty"], [30, 23, 1, 13], [30, 24, 1, 13, "exports"], [30, 31, 1, 13], [31, 4, 1, 13, "enumerable"], [31, 14, 1, 13], [32, 4, 1, 13, "get"], [32, 7, 1, 13], [32, 18, 1, 13, "get"], [32, 19, 1, 13], [33, 6, 1, 13], [33, 13, 1, 13, "_DrawerRouter"], [33, 26, 1, 13], [33, 27, 1, 13, "DrawerRouter"], [33, 39, 1, 13], [34, 4, 1, 13], [35, 2, 1, 13], [36, 2, 1, 13, "Object"], [36, 8, 1, 13], [36, 9, 1, 13, "defineProperty"], [36, 23, 1, 13], [36, 24, 1, 13, "exports"], [36, 31, 1, 13], [37, 4, 1, 13, "enumerable"], [37, 14, 1, 13], [38, 4, 1, 13, "get"], [38, 7, 1, 13], [38, 18, 1, 13, "get"], [38, 19, 1, 13], [39, 6, 1, 13], [39, 13, 1, 13, "_<PERSON>ack<PERSON><PERSON>er"], [39, 25, 1, 13], [39, 26, 1, 13, "StackActions"], [39, 38, 1, 13], [40, 4, 1, 13], [41, 2, 1, 13], [42, 2, 1, 13, "Object"], [42, 8, 1, 13], [42, 9, 1, 13, "defineProperty"], [42, 23, 1, 13], [42, 24, 1, 13, "exports"], [42, 31, 1, 13], [43, 4, 1, 13, "enumerable"], [43, 14, 1, 13], [44, 4, 1, 13, "get"], [44, 7, 1, 13], [44, 18, 1, 13, "get"], [44, 19, 1, 13], [45, 6, 1, 13], [45, 13, 1, 13, "_<PERSON>ack<PERSON><PERSON>er"], [45, 25, 1, 13], [45, 26, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [45, 37, 1, 13], [46, 4, 1, 13], [47, 2, 1, 13], [48, 2, 1, 13, "Object"], [48, 8, 1, 13], [48, 9, 1, 13, "defineProperty"], [48, 23, 1, 13], [48, 24, 1, 13, "exports"], [48, 31, 1, 13], [49, 4, 1, 13, "enumerable"], [49, 14, 1, 13], [50, 4, 1, 13, "get"], [50, 7, 1, 13], [50, 18, 1, 13, "get"], [50, 19, 1, 13], [51, 6, 1, 13], [51, 13, 1, 13, "_Tab<PERSON><PERSON>er"], [51, 23, 1, 13], [51, 24, 1, 13, "TabActions"], [51, 34, 1, 13], [52, 4, 1, 13], [53, 2, 1, 13], [54, 2, 1, 13, "Object"], [54, 8, 1, 13], [54, 9, 1, 13, "defineProperty"], [54, 23, 1, 13], [54, 24, 1, 13, "exports"], [54, 31, 1, 13], [55, 4, 1, 13, "enumerable"], [55, 14, 1, 13], [56, 4, 1, 13, "get"], [56, 7, 1, 13], [56, 18, 1, 13, "get"], [56, 19, 1, 13], [57, 6, 1, 13], [57, 13, 1, 13, "_Tab<PERSON><PERSON>er"], [57, 23, 1, 13], [57, 24, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [57, 33, 1, 13], [58, 4, 1, 13], [59, 2, 1, 13], [60, 2, 3, 0], [60, 6, 3, 0, "CommonActions"], [60, 19, 3, 0], [60, 22, 3, 0, "_interopRequireWildcard"], [60, 45, 3, 0], [60, 46, 3, 0, "require"], [60, 53, 3, 0], [60, 54, 3, 0, "_dependencyMap"], [60, 68, 3, 0], [61, 2, 3, 52, "exports"], [61, 9, 3, 52], [61, 10, 3, 52, "CommonActions"], [61, 23, 3, 52], [61, 26, 3, 52, "CommonActions"], [61, 39, 3, 52], [62, 2, 5, 0], [62, 6, 5, 0, "_BaseRouter"], [62, 17, 5, 0], [62, 20, 5, 0, "require"], [62, 27, 5, 0], [62, 28, 5, 0, "_dependencyMap"], [62, 42, 5, 0], [63, 2, 6, 0], [63, 6, 6, 0, "_DrawerRouter"], [63, 19, 6, 0], [63, 22, 6, 0, "require"], [63, 29, 6, 0], [63, 30, 6, 0, "_dependencyMap"], [63, 44, 6, 0], [64, 2, 7, 0], [64, 6, 7, 0, "_<PERSON>ack<PERSON><PERSON>er"], [64, 18, 7, 0], [64, 21, 7, 0, "require"], [64, 28, 7, 0], [64, 29, 7, 0, "_dependencyMap"], [64, 43, 7, 0], [65, 2, 8, 0], [65, 6, 8, 0, "_Tab<PERSON><PERSON>er"], [65, 16, 8, 0], [65, 19, 8, 0, "require"], [65, 26, 8, 0], [65, 27, 8, 0, "_dependencyMap"], [65, 41, 8, 0], [66, 2, 9, 0], [66, 6, 9, 0, "_types"], [66, 12, 9, 0], [66, 15, 9, 0, "require"], [66, 22, 9, 0], [66, 23, 9, 0, "_dependencyMap"], [66, 37, 9, 0], [67, 2, 9, 0, "Object"], [67, 8, 9, 0], [67, 9, 9, 0, "keys"], [67, 13, 9, 0], [67, 14, 9, 0, "_types"], [67, 20, 9, 0], [67, 22, 9, 0, "for<PERSON>ach"], [67, 29, 9, 0], [67, 40, 9, 0, "key"], [67, 43, 9, 0], [68, 4, 9, 0], [68, 8, 9, 0, "key"], [68, 11, 9, 0], [68, 29, 9, 0, "key"], [68, 32, 9, 0], [69, 4, 9, 0], [69, 8, 9, 0, "Object"], [69, 14, 9, 0], [69, 15, 9, 0, "prototype"], [69, 24, 9, 0], [69, 25, 9, 0, "hasOwnProperty"], [69, 39, 9, 0], [69, 40, 9, 0, "call"], [69, 44, 9, 0], [69, 45, 9, 0, "_exportNames"], [69, 57, 9, 0], [69, 59, 9, 0, "key"], [69, 62, 9, 0], [70, 4, 9, 0], [70, 8, 9, 0, "key"], [70, 11, 9, 0], [70, 15, 9, 0, "exports"], [70, 22, 9, 0], [70, 26, 9, 0, "exports"], [70, 33, 9, 0], [70, 34, 9, 0, "key"], [70, 37, 9, 0], [70, 43, 9, 0, "_types"], [70, 49, 9, 0], [70, 50, 9, 0, "key"], [70, 53, 9, 0], [71, 4, 9, 0, "Object"], [71, 10, 9, 0], [71, 11, 9, 0, "defineProperty"], [71, 25, 9, 0], [71, 26, 9, 0, "exports"], [71, 33, 9, 0], [71, 35, 9, 0, "key"], [71, 38, 9, 0], [72, 6, 9, 0, "enumerable"], [72, 16, 9, 0], [73, 6, 9, 0, "get"], [73, 9, 9, 0], [73, 20, 9, 0, "get"], [73, 21, 9, 0], [74, 8, 9, 0], [74, 15, 9, 0, "_types"], [74, 21, 9, 0], [74, 22, 9, 0, "key"], [74, 25, 9, 0], [75, 6, 9, 0], [76, 4, 9, 0], [77, 2, 9, 0], [78, 2, 9, 27], [78, 11, 9, 27, "_interopRequireWildcard"], [78, 35, 9, 27, "e"], [78, 36, 9, 27], [78, 38, 9, 27, "t"], [78, 39, 9, 27], [78, 68, 9, 27, "WeakMap"], [78, 75, 9, 27], [78, 81, 9, 27, "r"], [78, 82, 9, 27], [78, 89, 9, 27, "WeakMap"], [78, 96, 9, 27], [78, 100, 9, 27, "n"], [78, 101, 9, 27], [78, 108, 9, 27, "WeakMap"], [78, 115, 9, 27], [78, 127, 9, 27, "_interopRequireWildcard"], [78, 150, 9, 27], [78, 162, 9, 27, "_interopRequireWildcard"], [78, 163, 9, 27, "e"], [78, 164, 9, 27], [78, 166, 9, 27, "t"], [78, 167, 9, 27], [78, 176, 9, 27, "t"], [78, 177, 9, 27], [78, 181, 9, 27, "e"], [78, 182, 9, 27], [78, 186, 9, 27, "e"], [78, 187, 9, 27], [78, 188, 9, 27, "__esModule"], [78, 198, 9, 27], [78, 207, 9, 27, "e"], [78, 208, 9, 27], [78, 214, 9, 27, "o"], [78, 215, 9, 27], [78, 217, 9, 27, "i"], [78, 218, 9, 27], [78, 220, 9, 27, "f"], [78, 221, 9, 27], [78, 226, 9, 27, "__proto__"], [78, 235, 9, 27], [78, 243, 9, 27, "default"], [78, 250, 9, 27], [78, 252, 9, 27, "e"], [78, 253, 9, 27], [78, 270, 9, 27, "e"], [78, 271, 9, 27], [78, 294, 9, 27, "e"], [78, 295, 9, 27], [78, 320, 9, 27, "e"], [78, 321, 9, 27], [78, 330, 9, 27, "f"], [78, 331, 9, 27], [78, 337, 9, 27, "o"], [78, 338, 9, 27], [78, 341, 9, 27, "t"], [78, 342, 9, 27], [78, 345, 9, 27, "n"], [78, 346, 9, 27], [78, 349, 9, 27, "r"], [78, 350, 9, 27], [78, 358, 9, 27, "o"], [78, 359, 9, 27], [78, 360, 9, 27, "has"], [78, 363, 9, 27], [78, 364, 9, 27, "e"], [78, 365, 9, 27], [78, 375, 9, 27, "o"], [78, 376, 9, 27], [78, 377, 9, 27, "get"], [78, 380, 9, 27], [78, 381, 9, 27, "e"], [78, 382, 9, 27], [78, 385, 9, 27, "o"], [78, 386, 9, 27], [78, 387, 9, 27, "set"], [78, 390, 9, 27], [78, 391, 9, 27, "e"], [78, 392, 9, 27], [78, 394, 9, 27, "f"], [78, 395, 9, 27], [78, 409, 9, 27, "_t"], [78, 411, 9, 27], [78, 415, 9, 27, "e"], [78, 416, 9, 27], [78, 432, 9, 27, "_t"], [78, 434, 9, 27], [78, 441, 9, 27, "hasOwnProperty"], [78, 455, 9, 27], [78, 456, 9, 27, "call"], [78, 460, 9, 27], [78, 461, 9, 27, "e"], [78, 462, 9, 27], [78, 464, 9, 27, "_t"], [78, 466, 9, 27], [78, 473, 9, 27, "i"], [78, 474, 9, 27], [78, 478, 9, 27, "o"], [78, 479, 9, 27], [78, 482, 9, 27, "Object"], [78, 488, 9, 27], [78, 489, 9, 27, "defineProperty"], [78, 503, 9, 27], [78, 508, 9, 27, "Object"], [78, 514, 9, 27], [78, 515, 9, 27, "getOwnPropertyDescriptor"], [78, 539, 9, 27], [78, 540, 9, 27, "e"], [78, 541, 9, 27], [78, 543, 9, 27, "_t"], [78, 545, 9, 27], [78, 552, 9, 27, "i"], [78, 553, 9, 27], [78, 554, 9, 27, "get"], [78, 557, 9, 27], [78, 561, 9, 27, "i"], [78, 562, 9, 27], [78, 563, 9, 27, "set"], [78, 566, 9, 27], [78, 570, 9, 27, "o"], [78, 571, 9, 27], [78, 572, 9, 27, "f"], [78, 573, 9, 27], [78, 575, 9, 27, "_t"], [78, 577, 9, 27], [78, 579, 9, 27, "i"], [78, 580, 9, 27], [78, 584, 9, 27, "f"], [78, 585, 9, 27], [78, 586, 9, 27, "_t"], [78, 588, 9, 27], [78, 592, 9, 27, "e"], [78, 593, 9, 27], [78, 594, 9, 27, "_t"], [78, 596, 9, 27], [78, 607, 9, 27, "f"], [78, 608, 9, 27], [78, 613, 9, 27, "e"], [78, 614, 9, 27], [78, 616, 9, 27, "t"], [78, 617, 9, 27], [79, 0, 9, 27], [79, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}