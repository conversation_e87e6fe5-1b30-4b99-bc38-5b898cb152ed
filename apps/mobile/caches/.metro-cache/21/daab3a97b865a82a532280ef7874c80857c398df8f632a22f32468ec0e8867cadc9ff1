{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = processTransformOrigin;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[2], \"invariant\"));\n  var INDEX_X = 0;\n  var INDEX_Y = 1;\n  var INDEX_Z = 2;\n  function processTransformOrigin(transformOrigin) {\n    if (typeof transformOrigin === 'string') {\n      var transformOriginString = transformOrigin;\n      var regex = /(top|bottom|left|right|center|\\d+(?:%|px)|0)/gi;\n      var transformOriginArray = ['50%', '50%', 0];\n      var index = INDEX_X;\n      var matches;\n      outer: while (matches = regex.exec(transformOriginString)) {\n        var nextIndex = index + 1;\n        var value = matches[0];\n        var valueLower = value.toLowerCase();\n        switch (valueLower) {\n          case 'left':\n          case 'right':\n            {\n              (0, _invariant.default)(index === INDEX_X, 'Transform-origin %s can only be used for x-position', value);\n              transformOriginArray[INDEX_X] = valueLower === 'left' ? 0 : '100%';\n              break;\n            }\n          case 'top':\n          case 'bottom':\n            {\n              (0, _invariant.default)(index !== INDEX_Z, 'Transform-origin %s can only be used for y-position', value);\n              transformOriginArray[INDEX_Y] = valueLower === 'top' ? 0 : '100%';\n              if (index === INDEX_X) {\n                var horizontal = regex.exec(transformOriginString);\n                if (horizontal == null) {\n                  break outer;\n                }\n                switch (horizontal[0].toLowerCase()) {\n                  case 'left':\n                    transformOriginArray[INDEX_X] = 0;\n                    break;\n                  case 'right':\n                    transformOriginArray[INDEX_X] = '100%';\n                    break;\n                  case 'center':\n                    transformOriginArray[INDEX_X] = '50%';\n                    break;\n                  default:\n                    (0, _invariant.default)(false, 'Could not parse transform-origin: %s', transformOriginString);\n                }\n                nextIndex = INDEX_Z;\n              }\n              break;\n            }\n          case 'center':\n            {\n              (0, _invariant.default)(index !== INDEX_Z, 'Transform-origin value %s cannot be used for z-position', value);\n              transformOriginArray[index] = '50%';\n              break;\n            }\n          default:\n            {\n              if (value.endsWith('%')) {\n                transformOriginArray[index] = value;\n              } else {\n                transformOriginArray[index] = parseFloat(value);\n              }\n              break;\n            }\n        }\n        index = nextIndex;\n      }\n      transformOrigin = transformOriginArray;\n    }\n    if (__DEV__) {\n      _validateTransformOrigin(transformOrigin);\n    }\n    return transformOrigin;\n  }\n  function _validateTransformOrigin(transformOrigin) {\n    (0, _invariant.default)(transformOrigin.length === 3, 'Transform origin must have exactly 3 values.');\n    var _transformOrigin = (0, _slicedToArray2.default)(transformOrigin, 3),\n      x = _transformOrigin[0],\n      y = _transformOrigin[1],\n      z = _transformOrigin[2];\n    (0, _invariant.default)(typeof x === 'number' || typeof x === 'string' && x.endsWith('%'), 'Transform origin x-position must be a number. Passed value: %s.', x);\n    (0, _invariant.default)(typeof y === 'number' || typeof y === 'string' && y.endsWith('%'), 'Transform origin y-position must be a number. Passed value: %s.', y);\n    (0, _invariant.default)(typeof z === 'number', 'Transform origin z-position must be a number. Passed value: %s.', z);\n  }\n});", "lineCount": 93, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_invariant"], [8, 16, 11, 0], [8, 19, 11, 0, "_interopRequireDefault"], [8, 41, 11, 0], [8, 42, 11, 0, "require"], [8, 49, 11, 0], [8, 50, 11, 0, "_dependencyMap"], [8, 64, 11, 0], [9, 2, 13, 0], [9, 6, 13, 6, "INDEX_X"], [9, 13, 13, 13], [9, 16, 13, 16], [9, 17, 13, 17], [10, 2, 14, 0], [10, 6, 14, 6, "INDEX_Y"], [10, 13, 14, 13], [10, 16, 14, 16], [10, 17, 14, 17], [11, 2, 15, 0], [11, 6, 15, 6, "INDEX_Z"], [11, 13, 15, 13], [11, 16, 15, 16], [11, 17, 15, 17], [12, 2, 18, 15], [12, 11, 18, 24, "processTransformOrigin"], [12, 33, 18, 46, "processTransformOrigin"], [12, 34, 19, 2, "transform<PERSON><PERSON>in"], [12, 49, 19, 50], [12, 51, 20, 26], [13, 4, 21, 2], [13, 8, 21, 6], [13, 15, 21, 13, "transform<PERSON><PERSON>in"], [13, 30, 21, 28], [13, 35, 21, 33], [13, 43, 21, 41], [13, 45, 21, 43], [14, 6, 22, 4], [14, 10, 22, 10, "transformOriginString"], [14, 31, 22, 31], [14, 34, 22, 34, "transform<PERSON><PERSON>in"], [14, 49, 22, 49], [15, 6, 23, 4], [15, 10, 23, 10, "regex"], [15, 15, 23, 15], [15, 18, 23, 18], [15, 66, 23, 66], [16, 6, 24, 4], [16, 10, 24, 10, "transformOriginArray"], [16, 30, 24, 54], [16, 33, 24, 57], [16, 34, 24, 58], [16, 39, 24, 63], [16, 41, 24, 65], [16, 46, 24, 70], [16, 48, 24, 72], [16, 49, 24, 73], [16, 50, 24, 74], [17, 6, 26, 4], [17, 10, 26, 8, "index"], [17, 15, 26, 13], [17, 18, 26, 16, "INDEX_X"], [17, 25, 26, 23], [18, 6, 27, 4], [18, 10, 27, 8, "matches"], [18, 17, 27, 15], [19, 6, 28, 4, "outer"], [19, 11, 28, 9], [19, 13, 28, 11], [19, 20, 28, 19, "matches"], [19, 27, 28, 26], [19, 30, 28, 29, "regex"], [19, 35, 28, 34], [19, 36, 28, 35, "exec"], [19, 40, 28, 39], [19, 41, 28, 40, "transformOriginString"], [19, 62, 28, 61], [19, 63, 28, 62], [19, 65, 28, 65], [20, 8, 29, 6], [20, 12, 29, 10, "nextIndex"], [20, 21, 29, 19], [20, 24, 29, 22, "index"], [20, 29, 29, 27], [20, 32, 29, 30], [20, 33, 29, 31], [21, 8, 31, 6], [21, 12, 31, 12, "value"], [21, 17, 31, 17], [21, 20, 31, 20, "matches"], [21, 27, 31, 27], [21, 28, 31, 28], [21, 29, 31, 29], [21, 30, 31, 30], [22, 8, 32, 6], [22, 12, 32, 12, "valueLower"], [22, 22, 32, 22], [22, 25, 32, 25, "value"], [22, 30, 32, 30], [22, 31, 32, 31, "toLowerCase"], [22, 42, 32, 42], [22, 43, 32, 43], [22, 44, 32, 44], [23, 8, 34, 6], [23, 16, 34, 14, "valueLower"], [23, 26, 34, 24], [24, 10, 35, 8], [24, 15, 35, 13], [24, 21, 35, 19], [25, 10, 36, 8], [25, 15, 36, 13], [25, 22, 36, 20], [26, 12, 36, 22], [27, 14, 37, 10], [27, 18, 37, 10, "invariant"], [27, 36, 37, 19], [27, 38, 38, 12, "index"], [27, 43, 38, 17], [27, 48, 38, 22, "INDEX_X"], [27, 55, 38, 29], [27, 57, 39, 12], [27, 110, 39, 65], [27, 112, 40, 12, "value"], [27, 117, 41, 10], [27, 118, 41, 11], [28, 14, 42, 10, "transformOriginArray"], [28, 34, 42, 30], [28, 35, 42, 31, "INDEX_X"], [28, 42, 42, 38], [28, 43, 42, 39], [28, 46, 42, 42, "valueLower"], [28, 56, 42, 52], [28, 61, 42, 57], [28, 67, 42, 63], [28, 70, 42, 66], [28, 71, 42, 67], [28, 74, 42, 70], [28, 80, 42, 76], [29, 14, 43, 10], [30, 12, 44, 8], [31, 10, 45, 8], [31, 15, 45, 13], [31, 20, 45, 18], [32, 10, 46, 8], [32, 15, 46, 13], [32, 23, 46, 21], [33, 12, 46, 23], [34, 14, 47, 10], [34, 18, 47, 10, "invariant"], [34, 36, 47, 19], [34, 38, 48, 12, "index"], [34, 43, 48, 17], [34, 48, 48, 22, "INDEX_Z"], [34, 55, 48, 29], [34, 57, 49, 12], [34, 110, 49, 65], [34, 112, 50, 12, "value"], [34, 117, 51, 10], [34, 118, 51, 11], [35, 14, 52, 10, "transformOriginArray"], [35, 34, 52, 30], [35, 35, 52, 31, "INDEX_Y"], [35, 42, 52, 38], [35, 43, 52, 39], [35, 46, 52, 42, "valueLower"], [35, 56, 52, 52], [35, 61, 52, 57], [35, 66, 52, 62], [35, 69, 52, 65], [35, 70, 52, 66], [35, 73, 52, 69], [35, 79, 52, 75], [36, 14, 55, 10], [36, 18, 55, 14, "index"], [36, 23, 55, 19], [36, 28, 55, 24, "INDEX_X"], [36, 35, 55, 31], [36, 37, 55, 33], [37, 16, 56, 12], [37, 20, 56, 18, "horizontal"], [37, 30, 56, 28], [37, 33, 56, 31, "regex"], [37, 38, 56, 36], [37, 39, 56, 37, "exec"], [37, 43, 56, 41], [37, 44, 56, 42, "transformOriginString"], [37, 65, 56, 63], [37, 66, 56, 64], [38, 16, 57, 12], [38, 20, 57, 16, "horizontal"], [38, 30, 57, 26], [38, 34, 57, 30], [38, 38, 57, 34], [38, 40, 57, 36], [39, 18, 58, 14], [39, 24, 58, 20, "outer"], [39, 29, 58, 25], [40, 16, 59, 12], [41, 16, 61, 12], [41, 24, 61, 20, "horizontal"], [41, 34, 61, 30], [41, 35, 61, 31], [41, 36, 61, 32], [41, 37, 61, 33], [41, 38, 61, 34, "toLowerCase"], [41, 49, 61, 45], [41, 50, 61, 46], [41, 51, 61, 47], [42, 18, 62, 14], [42, 23, 62, 19], [42, 29, 62, 25], [43, 20, 63, 16, "transformOriginArray"], [43, 40, 63, 36], [43, 41, 63, 37, "INDEX_X"], [43, 48, 63, 44], [43, 49, 63, 45], [43, 52, 63, 48], [43, 53, 63, 49], [44, 20, 64, 16], [45, 18, 65, 14], [45, 23, 65, 19], [45, 30, 65, 26], [46, 20, 66, 16, "transformOriginArray"], [46, 40, 66, 36], [46, 41, 66, 37, "INDEX_X"], [46, 48, 66, 44], [46, 49, 66, 45], [46, 52, 66, 48], [46, 58, 66, 54], [47, 20, 67, 16], [48, 18, 68, 14], [48, 23, 68, 19], [48, 31, 68, 27], [49, 20, 69, 16, "transformOriginArray"], [49, 40, 69, 36], [49, 41, 69, 37, "INDEX_X"], [49, 48, 69, 44], [49, 49, 69, 45], [49, 52, 69, 48], [49, 57, 69, 53], [50, 20, 70, 16], [51, 18, 71, 14], [52, 20, 72, 16], [52, 24, 72, 16, "invariant"], [52, 42, 72, 25], [52, 44, 73, 18], [52, 49, 73, 23], [52, 51, 74, 18], [52, 89, 74, 56], [52, 91, 75, 18, "transformOriginString"], [52, 112, 76, 16], [52, 113, 76, 17], [53, 16, 77, 12], [54, 16, 78, 12, "nextIndex"], [54, 25, 78, 21], [54, 28, 78, 24, "INDEX_Z"], [54, 35, 78, 31], [55, 14, 79, 10], [56, 14, 81, 10], [57, 12, 82, 8], [58, 10, 83, 8], [58, 15, 83, 13], [58, 23, 83, 21], [59, 12, 83, 23], [60, 14, 84, 10], [60, 18, 84, 10, "invariant"], [60, 36, 84, 19], [60, 38, 85, 12, "index"], [60, 43, 85, 17], [60, 48, 85, 22, "INDEX_Z"], [60, 55, 85, 29], [60, 57, 86, 12], [60, 114, 86, 69], [60, 116, 87, 12, "value"], [60, 121, 88, 10], [60, 122, 88, 11], [61, 14, 89, 10, "transformOriginArray"], [61, 34, 89, 30], [61, 35, 89, 31, "index"], [61, 40, 89, 36], [61, 41, 89, 37], [61, 44, 89, 40], [61, 49, 89, 45], [62, 14, 90, 10], [63, 12, 91, 8], [64, 10, 92, 8], [65, 12, 92, 17], [66, 14, 93, 10], [66, 18, 93, 14, "value"], [66, 23, 93, 19], [66, 24, 93, 20, "endsWith"], [66, 32, 93, 28], [66, 33, 93, 29], [66, 36, 93, 32], [66, 37, 93, 33], [66, 39, 93, 35], [67, 16, 94, 12, "transformOriginArray"], [67, 36, 94, 32], [67, 37, 94, 33, "index"], [67, 42, 94, 38], [67, 43, 94, 39], [67, 46, 94, 42, "value"], [67, 51, 94, 47], [68, 14, 95, 10], [68, 15, 95, 11], [68, 21, 95, 17], [69, 16, 96, 12, "transformOriginArray"], [69, 36, 96, 32], [69, 37, 96, 33, "index"], [69, 42, 96, 38], [69, 43, 96, 39], [69, 46, 96, 42, "parseFloat"], [69, 56, 96, 52], [69, 57, 96, 53, "value"], [69, 62, 96, 58], [69, 63, 96, 59], [70, 14, 97, 10], [71, 14, 98, 10], [72, 12, 99, 8], [73, 8, 100, 6], [74, 8, 102, 6, "index"], [74, 13, 102, 11], [74, 16, 102, 14, "nextIndex"], [74, 25, 102, 23], [75, 6, 103, 4], [76, 6, 105, 4, "transform<PERSON><PERSON>in"], [76, 21, 105, 19], [76, 24, 105, 22, "transformOriginArray"], [76, 44, 105, 42], [77, 4, 106, 2], [78, 4, 108, 2], [78, 8, 108, 6, "__DEV__"], [78, 15, 108, 13], [78, 17, 108, 15], [79, 6, 109, 4, "_validateTransformOrigin"], [79, 30, 109, 28], [79, 31, 109, 29, "transform<PERSON><PERSON>in"], [79, 46, 109, 44], [79, 47, 109, 45], [80, 4, 110, 2], [81, 4, 112, 2], [81, 11, 112, 9, "transform<PERSON><PERSON>in"], [81, 26, 112, 24], [82, 2, 113, 0], [83, 2, 115, 0], [83, 11, 115, 9, "_validateTransformOrigin"], [83, 35, 115, 33, "_validateTransformOrigin"], [83, 36, 115, 34, "transform<PERSON><PERSON>in"], [83, 51, 115, 73], [83, 53, 115, 75], [84, 4, 116, 2], [84, 8, 116, 2, "invariant"], [84, 26, 116, 11], [84, 28, 117, 4, "transform<PERSON><PERSON>in"], [84, 43, 117, 19], [84, 44, 117, 20, "length"], [84, 50, 117, 26], [84, 55, 117, 31], [84, 56, 117, 32], [84, 58, 118, 4], [84, 104, 119, 2], [84, 105, 119, 3], [85, 4, 120, 2], [85, 8, 120, 2, "_transform<PERSON><PERSON><PERSON>"], [85, 24, 120, 2], [85, 31, 120, 2, "_slicedToArray2"], [85, 46, 120, 2], [85, 47, 120, 2, "default"], [85, 54, 120, 2], [85, 56, 120, 20, "transform<PERSON><PERSON>in"], [85, 71, 120, 35], [86, 6, 120, 9, "x"], [86, 7, 120, 10], [86, 10, 120, 10, "_transform<PERSON><PERSON><PERSON>"], [86, 26, 120, 10], [87, 6, 120, 12, "y"], [87, 7, 120, 13], [87, 10, 120, 13, "_transform<PERSON><PERSON><PERSON>"], [87, 26, 120, 13], [88, 6, 120, 15, "z"], [88, 7, 120, 16], [88, 10, 120, 16, "_transform<PERSON><PERSON><PERSON>"], [88, 26, 120, 16], [89, 4, 121, 2], [89, 8, 121, 2, "invariant"], [89, 26, 121, 11], [89, 28, 122, 4], [89, 35, 122, 11, "x"], [89, 36, 122, 12], [89, 41, 122, 17], [89, 49, 122, 25], [89, 53, 122, 30], [89, 60, 122, 37, "x"], [89, 61, 122, 38], [89, 66, 122, 43], [89, 74, 122, 51], [89, 78, 122, 55, "x"], [89, 79, 122, 56], [89, 80, 122, 57, "endsWith"], [89, 88, 122, 65], [89, 89, 122, 66], [89, 92, 122, 69], [89, 93, 122, 71], [89, 95, 123, 4], [89, 160, 123, 69], [89, 162, 124, 4, "x"], [89, 163, 125, 2], [89, 164, 125, 3], [90, 4, 126, 2], [90, 8, 126, 2, "invariant"], [90, 26, 126, 11], [90, 28, 127, 4], [90, 35, 127, 11, "y"], [90, 36, 127, 12], [90, 41, 127, 17], [90, 49, 127, 25], [90, 53, 127, 30], [90, 60, 127, 37, "y"], [90, 61, 127, 38], [90, 66, 127, 43], [90, 74, 127, 51], [90, 78, 127, 55, "y"], [90, 79, 127, 56], [90, 80, 127, 57, "endsWith"], [90, 88, 127, 65], [90, 89, 127, 66], [90, 92, 127, 69], [90, 93, 127, 71], [90, 95, 128, 4], [90, 160, 128, 69], [90, 162, 129, 4, "y"], [90, 163, 130, 2], [90, 164, 130, 3], [91, 4, 131, 2], [91, 8, 131, 2, "invariant"], [91, 26, 131, 11], [91, 28, 132, 4], [91, 35, 132, 11, "z"], [91, 36, 132, 12], [91, 41, 132, 17], [91, 49, 132, 25], [91, 51, 133, 4], [91, 116, 133, 69], [91, 118, 134, 4, "z"], [91, 119, 135, 2], [91, 120, 135, 3], [92, 2, 136, 0], [93, 0, 136, 1], [93, 3]], "functionMap": {"names": ["<global>", "processTransformOrigin", "_validateTransformOrigin"], "mappings": "AAA;eCiB;CD+F;AEE"}}, "type": "js/module"}]}