{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "expo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 32, "index": 32}}], "key": "PWvtvXU7MaET6Yd1Gn8oQOXJQ8A=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 33}, "end": {"line": 2, "column": 61, "index": 94}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 95}, "end": {"line": 3, "column": 53, "index": 148}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 149}, "end": {"line": 4, "column": 40, "index": 189}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./AudioModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 190}, "end": {"line": 5, "column": 40, "index": 230}}], "key": "VlSxUi/e3yJzxdTom69eHea8DLs=", "exportNames": ["*"]}}, {"name": "./utils/options", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 231}, "end": {"line": 6, "column": 57, "index": 288}}], "key": "6v352GoeGXFYsqgkwOF9h6BgPT8=", "exportNames": ["*"]}}, {"name": "./utils/resolveSource", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 289}, "end": {"line": 7, "column": 54, "index": 343}}], "key": "9oyT4U3r4jMjpNZXF9x4mw0dgHc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.AUDIO_SAMPLE_UPDATE = void 0;\n  Object.defineProperty(exports, \"AudioModule\", {\n    enumerable: true,\n    get: function () {\n      return _AudioModule.default;\n    }\n  });\n  exports.RECORDING_STATUS_UPDATE = exports.PLAYBACK_STATUS_UPDATE = void 0;\n  exports.createAudioPlayer = createAudioPlayer;\n  exports.getRecordingPermissionsAsync = getRecordingPermissionsAsync;\n  exports.requestRecordingPermissionsAsync = requestRecordingPermissionsAsync;\n  exports.setAudioModeAsync = setAudioModeAsync;\n  exports.setIsAudioActiveAsync = setIsAudioActiveAsync;\n  exports.useAudioPlayer = useAudioPlayer;\n  exports.useAudioPlayerStatus = useAudioPlayerStatus;\n  exports.useAudioRecorder = useAudioRecorder;\n  exports.useAudioRecorderState = useAudioRecorderState;\n  exports.useAudioSampleListener = useAudioSampleListener;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var _expo = require(_dependencyMap[3], \"expo\");\n  var _expoModulesCore = require(_dependencyMap[4], \"expo-modules-core\");\n  var _react = require(_dependencyMap[5], \"react\");\n  var _reactNative = require(_dependencyMap[6], \"react-native\");\n  var _AudioModule = _interopRequireDefault(require(_dependencyMap[7], \"./AudioModule\"));\n  var _options = require(_dependencyMap[8], \"./utils/options\");\n  var _resolveSource = require(_dependencyMap[9], \"./utils/resolveSource\");\n  var PLAYBACK_STATUS_UPDATE = exports.PLAYBACK_STATUS_UPDATE = 'playbackStatusUpdate';\n  var AUDIO_SAMPLE_UPDATE = exports.AUDIO_SAMPLE_UPDATE = 'audioSampleUpdate';\n  var RECORDING_STATUS_UPDATE = exports.RECORDING_STATUS_UPDATE = 'recordingStatusUpdate';\n  // TODO: Temporary solution until we develop a way of overriding prototypes that won't break the lazy loading of the module.\n  var replace = _AudioModule.default.AudioPlayer.prototype.replace;\n  _AudioModule.default.AudioPlayer.prototype.replace = function (source) {\n    return replace.call(this, (0, _resolveSource.resolveSource)(source));\n  };\n  // @docsMissing\n  function useAudioPlayer() {\n    var source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var updateInterval = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 500;\n    var parsedSource = (0, _resolveSource.resolveSource)(source);\n    return (0, _expoModulesCore.useReleasingSharedObject)(() => new _AudioModule.default.AudioPlayer(parsedSource, updateInterval), [JSON.stringify(parsedSource)]);\n  }\n  // @docsMissing\n  function useAudioPlayerStatus(player) {\n    var currentStatus = (0, _react.useMemo)(() => player.currentStatus, [player.id]);\n    return (0, _expo.useEvent)(player, PLAYBACK_STATUS_UPDATE, currentStatus);\n  }\n  // @docsMissing\n  function useAudioSampleListener(player, listener) {\n    (0, _react.useEffect)(() => {\n      if (!player.isAudioSamplingSupported) {\n        return;\n      }\n      player.setAudioSamplingEnabled(true);\n      var subscription = player.addListener(AUDIO_SAMPLE_UPDATE, listener);\n      return () => subscription.remove();\n    }, [player.id]);\n  }\n  // @docsMissing\n  function useAudioRecorder(options, statusListener) {\n    var platformOptions = (0, _options.createRecordingOptions)(options);\n    var recorder = (0, _expoModulesCore.useReleasingSharedObject)(() => {\n      return new _AudioModule.default.AudioRecorder(platformOptions);\n    }, [JSON.stringify(platformOptions)]);\n    (0, _react.useEffect)(() => {\n      var subscription = recorder.addListener(RECORDING_STATUS_UPDATE, status => {\n        statusListener?.(status);\n      });\n      return () => subscription.remove();\n    }, [recorder.id]);\n    return recorder;\n  }\n  // @docsMissing\n  function useAudioRecorderState(recorder) {\n    var interval = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 500;\n    var _useState = (0, _react.useState)(recorder.getStatus()),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n    (0, _react.useEffect)(() => {\n      var int = setInterval(() => {\n        setState(recorder.getStatus());\n      }, interval);\n      return () => clearInterval(int);\n    }, [recorder.id]);\n    return state;\n  }\n  /**\n   * Creates an instance of an `AudioPlayer` that doesn't release automatically.\n   *\n   * > **info** For most use cases you should use the [`useAudioPlayer`](#useaudioplayersource-updateinterval) hook instead.\n   * > See the [Using the `AudioPlayer` directly](#using-the-audioplayer-directly) section for more details.\n   * @param source\n   * @param updateInterval\n   */\n  function createAudioPlayer() {\n    var source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var updateInterval = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 500;\n    var parsedSource = (0, _resolveSource.resolveSource)(source);\n    return new _AudioModule.default.AudioPlayer(parsedSource, updateInterval);\n  }\n  // @docsMissing\n  function setIsAudioActiveAsync(_x) {\n    return _setIsAudioActiveAsync.apply(this, arguments);\n  } // @docsMissing\n  function _setIsAudioActiveAsync() {\n    _setIsAudioActiveAsync = (0, _asyncToGenerator2.default)(function* (active) {\n      return yield _AudioModule.default.setIsAudioActiveAsync(active);\n    });\n    return _setIsAudioActiveAsync.apply(this, arguments);\n  }\n  function setAudioModeAsync(_x2) {\n    return _setAudioModeAsync.apply(this, arguments);\n  } // @docsMissing\n  function _setAudioModeAsync() {\n    _setAudioModeAsync = (0, _asyncToGenerator2.default)(function* (mode) {\n      var audioMode = _reactNative.Platform.OS === 'ios' ? mode : {\n        shouldPlayInBackground: mode.shouldPlayInBackground,\n        shouldRouteThroughEarpiece: mode.shouldRouteThroughEarpiece,\n        interruptionMode: mode.interruptionModeAndroid\n      };\n      return yield _AudioModule.default.setAudioModeAsync(audioMode);\n    });\n    return _setAudioModeAsync.apply(this, arguments);\n  }\n  function requestRecordingPermissionsAsync() {\n    return _requestRecordingPermissionsAsync.apply(this, arguments);\n  } // @docsMissing\n  function _requestRecordingPermissionsAsync() {\n    _requestRecordingPermissionsAsync = (0, _asyncToGenerator2.default)(function* () {\n      return yield _AudioModule.default.requestRecordingPermissionsAsync();\n    });\n    return _requestRecordingPermissionsAsync.apply(this, arguments);\n  }\n  function getRecordingPermissionsAsync() {\n    return _getRecordingPermissionsAsync.apply(this, arguments);\n  }\n  function _getRecordingPermissionsAsync() {\n    _getRecordingPermissionsAsync = (0, _asyncToGenerator2.default)(function* () {\n      return yield _AudioModule.default.getRecordingPermissionsAsync();\n    });\n    return _getRecordingPermissionsAsync.apply(this, arguments);\n  }\n});", "lineCount": 149, "map": [[26, 2, 1, 0], [26, 6, 1, 0, "_expo"], [26, 11, 1, 0], [26, 14, 1, 0, "require"], [26, 21, 1, 0], [26, 22, 1, 0, "_dependencyMap"], [26, 36, 1, 0], [27, 2, 2, 0], [27, 6, 2, 0, "_expoModulesCore"], [27, 22, 2, 0], [27, 25, 2, 0, "require"], [27, 32, 2, 0], [27, 33, 2, 0, "_dependencyMap"], [27, 47, 2, 0], [28, 2, 3, 0], [28, 6, 3, 0, "_react"], [28, 12, 3, 0], [28, 15, 3, 0, "require"], [28, 22, 3, 0], [28, 23, 3, 0, "_dependencyMap"], [28, 37, 3, 0], [29, 2, 4, 0], [29, 6, 4, 0, "_reactNative"], [29, 18, 4, 0], [29, 21, 4, 0, "require"], [29, 28, 4, 0], [29, 29, 4, 0, "_dependencyMap"], [29, 43, 4, 0], [30, 2, 5, 0], [30, 6, 5, 0, "_AudioModule"], [30, 18, 5, 0], [30, 21, 5, 0, "_interopRequireDefault"], [30, 43, 5, 0], [30, 44, 5, 0, "require"], [30, 51, 5, 0], [30, 52, 5, 0, "_dependencyMap"], [30, 66, 5, 0], [31, 2, 6, 0], [31, 6, 6, 0, "_options"], [31, 14, 6, 0], [31, 17, 6, 0, "require"], [31, 24, 6, 0], [31, 25, 6, 0, "_dependencyMap"], [31, 39, 6, 0], [32, 2, 7, 0], [32, 6, 7, 0, "_resolveSource"], [32, 20, 7, 0], [32, 23, 7, 0, "require"], [32, 30, 7, 0], [32, 31, 7, 0, "_dependencyMap"], [32, 45, 7, 0], [33, 2, 8, 7], [33, 6, 8, 13, "PLAYBACK_STATUS_UPDATE"], [33, 28, 8, 35], [33, 31, 8, 35, "exports"], [33, 38, 8, 35], [33, 39, 8, 35, "PLAYBACK_STATUS_UPDATE"], [33, 61, 8, 35], [33, 64, 8, 38], [33, 86, 8, 60], [34, 2, 9, 7], [34, 6, 9, 13, "AUDIO_SAMPLE_UPDATE"], [34, 25, 9, 32], [34, 28, 9, 32, "exports"], [34, 35, 9, 32], [34, 36, 9, 32, "AUDIO_SAMPLE_UPDATE"], [34, 55, 9, 32], [34, 58, 9, 35], [34, 77, 9, 54], [35, 2, 10, 7], [35, 6, 10, 13, "RECORDING_STATUS_UPDATE"], [35, 29, 10, 36], [35, 32, 10, 36, "exports"], [35, 39, 10, 36], [35, 40, 10, 36, "RECORDING_STATUS_UPDATE"], [35, 63, 10, 36], [35, 66, 10, 39], [35, 89, 10, 62], [36, 2, 11, 0], [37, 2, 12, 0], [37, 6, 12, 6, "replace"], [37, 13, 12, 13], [37, 16, 12, 16, "AudioModule"], [37, 36, 12, 27], [37, 37, 12, 28, "AudioPlayer"], [37, 48, 12, 39], [37, 49, 12, 40, "prototype"], [37, 58, 12, 49], [37, 59, 12, 50, "replace"], [37, 66, 12, 57], [38, 2, 13, 0, "AudioModule"], [38, 22, 13, 11], [38, 23, 13, 12, "AudioPlayer"], [38, 34, 13, 23], [38, 35, 13, 24, "prototype"], [38, 44, 13, 33], [38, 45, 13, 34, "replace"], [38, 52, 13, 41], [38, 55, 13, 44], [38, 65, 13, 54, "source"], [38, 71, 13, 60], [38, 73, 13, 62], [39, 4, 14, 4], [39, 11, 14, 11, "replace"], [39, 18, 14, 18], [39, 19, 14, 19, "call"], [39, 23, 14, 23], [39, 24, 14, 24], [39, 28, 14, 28], [39, 30, 14, 30], [39, 34, 14, 30, "resolveSource"], [39, 62, 14, 43], [39, 64, 14, 44, "source"], [39, 70, 14, 50], [39, 71, 14, 51], [39, 72, 14, 52], [40, 2, 15, 0], [40, 3, 15, 1], [41, 2, 16, 0], [42, 2, 17, 7], [42, 11, 17, 16, "useAudioPlayer"], [42, 25, 17, 30, "useAudioPlayer"], [42, 26, 17, 30], [42, 28, 17, 68], [43, 4, 17, 68], [43, 8, 17, 31, "source"], [43, 14, 17, 37], [43, 17, 17, 37, "arguments"], [43, 26, 17, 37], [43, 27, 17, 37, "length"], [43, 33, 17, 37], [43, 41, 17, 37, "arguments"], [43, 50, 17, 37], [43, 58, 17, 37, "undefined"], [43, 67, 17, 37], [43, 70, 17, 37, "arguments"], [43, 79, 17, 37], [43, 85, 17, 40], [43, 89, 17, 44], [44, 4, 17, 44], [44, 8, 17, 46, "updateInterval"], [44, 22, 17, 60], [44, 25, 17, 60, "arguments"], [44, 34, 17, 60], [44, 35, 17, 60, "length"], [44, 41, 17, 60], [44, 49, 17, 60, "arguments"], [44, 58, 17, 60], [44, 66, 17, 60, "undefined"], [44, 75, 17, 60], [44, 78, 17, 60, "arguments"], [44, 87, 17, 60], [44, 93, 17, 63], [44, 96, 17, 66], [45, 4, 18, 4], [45, 8, 18, 10, "parsedSource"], [45, 20, 18, 22], [45, 23, 18, 25], [45, 27, 18, 25, "resolveSource"], [45, 55, 18, 38], [45, 57, 18, 39, "source"], [45, 63, 18, 45], [45, 64, 18, 46], [46, 4, 19, 4], [46, 11, 19, 11], [46, 15, 19, 11, "useReleasingSharedObject"], [46, 56, 19, 35], [46, 58, 19, 36], [46, 64, 19, 42], [46, 68, 19, 46, "AudioModule"], [46, 88, 19, 57], [46, 89, 19, 58, "AudioPlayer"], [46, 100, 19, 69], [46, 101, 19, 70, "parsedSource"], [46, 113, 19, 82], [46, 115, 19, 84, "updateInterval"], [46, 129, 19, 98], [46, 130, 19, 99], [46, 132, 19, 101], [46, 133, 19, 102, "JSON"], [46, 137, 19, 106], [46, 138, 19, 107, "stringify"], [46, 147, 19, 116], [46, 148, 19, 117, "parsedSource"], [46, 160, 19, 129], [46, 161, 19, 130], [46, 162, 19, 131], [46, 163, 19, 132], [47, 2, 20, 0], [48, 2, 21, 0], [49, 2, 22, 7], [49, 11, 22, 16, "useAudioPlayerStatus"], [49, 31, 22, 36, "useAudioPlayerStatus"], [49, 32, 22, 37, "player"], [49, 38, 22, 43], [49, 40, 22, 45], [50, 4, 23, 4], [50, 8, 23, 10, "currentStatus"], [50, 21, 23, 23], [50, 24, 23, 26], [50, 28, 23, 26, "useMemo"], [50, 42, 23, 33], [50, 44, 23, 34], [50, 50, 23, 40, "player"], [50, 56, 23, 46], [50, 57, 23, 47, "currentStatus"], [50, 70, 23, 60], [50, 72, 23, 62], [50, 73, 23, 63, "player"], [50, 79, 23, 69], [50, 80, 23, 70, "id"], [50, 82, 23, 72], [50, 83, 23, 73], [50, 84, 23, 74], [51, 4, 24, 4], [51, 11, 24, 11], [51, 15, 24, 11, "useEvent"], [51, 29, 24, 19], [51, 31, 24, 20, "player"], [51, 37, 24, 26], [51, 39, 24, 28, "PLAYBACK_STATUS_UPDATE"], [51, 61, 24, 50], [51, 63, 24, 52, "currentStatus"], [51, 76, 24, 65], [51, 77, 24, 66], [52, 2, 25, 0], [53, 2, 26, 0], [54, 2, 27, 7], [54, 11, 27, 16, "useAudioSampleListener"], [54, 33, 27, 38, "useAudioSampleListener"], [54, 34, 27, 39, "player"], [54, 40, 27, 45], [54, 42, 27, 47, "listener"], [54, 50, 27, 55], [54, 52, 27, 57], [55, 4, 28, 4], [55, 8, 28, 4, "useEffect"], [55, 24, 28, 13], [55, 26, 28, 14], [55, 32, 28, 20], [56, 6, 29, 8], [56, 10, 29, 12], [56, 11, 29, 13, "player"], [56, 17, 29, 19], [56, 18, 29, 20, "isAudioSamplingSupported"], [56, 42, 29, 44], [56, 44, 29, 46], [57, 8, 30, 12], [58, 6, 31, 8], [59, 6, 32, 8, "player"], [59, 12, 32, 14], [59, 13, 32, 15, "setAudioSamplingEnabled"], [59, 36, 32, 38], [59, 37, 32, 39], [59, 41, 32, 43], [59, 42, 32, 44], [60, 6, 33, 8], [60, 10, 33, 14, "subscription"], [60, 22, 33, 26], [60, 25, 33, 29, "player"], [60, 31, 33, 35], [60, 32, 33, 36, "addListener"], [60, 43, 33, 47], [60, 44, 33, 48, "AUDIO_SAMPLE_UPDATE"], [60, 63, 33, 67], [60, 65, 33, 69, "listener"], [60, 73, 33, 77], [60, 74, 33, 78], [61, 6, 34, 8], [61, 13, 34, 15], [61, 19, 34, 21, "subscription"], [61, 31, 34, 33], [61, 32, 34, 34, "remove"], [61, 38, 34, 40], [61, 39, 34, 41], [61, 40, 34, 42], [62, 4, 35, 4], [62, 5, 35, 5], [62, 7, 35, 7], [62, 8, 35, 8, "player"], [62, 14, 35, 14], [62, 15, 35, 15, "id"], [62, 17, 35, 17], [62, 18, 35, 18], [62, 19, 35, 19], [63, 2, 36, 0], [64, 2, 37, 0], [65, 2, 38, 7], [65, 11, 38, 16, "useAudioRecorder"], [65, 27, 38, 32, "useAudioRecorder"], [65, 28, 38, 33, "options"], [65, 35, 38, 40], [65, 37, 38, 42, "statusListener"], [65, 51, 38, 56], [65, 53, 38, 58], [66, 4, 39, 4], [66, 8, 39, 10, "platformOptions"], [66, 23, 39, 25], [66, 26, 39, 28], [66, 30, 39, 28, "createRecordingOptions"], [66, 61, 39, 50], [66, 63, 39, 51, "options"], [66, 70, 39, 58], [66, 71, 39, 59], [67, 4, 40, 4], [67, 8, 40, 10, "recorder"], [67, 16, 40, 18], [67, 19, 40, 21], [67, 23, 40, 21, "useReleasingSharedObject"], [67, 64, 40, 45], [67, 66, 40, 46], [67, 72, 40, 52], [68, 6, 41, 8], [68, 13, 41, 15], [68, 17, 41, 19, "AudioModule"], [68, 37, 41, 30], [68, 38, 41, 31, "AudioRecorder"], [68, 51, 41, 44], [68, 52, 41, 45, "platformOptions"], [68, 67, 41, 60], [68, 68, 41, 61], [69, 4, 42, 4], [69, 5, 42, 5], [69, 7, 42, 7], [69, 8, 42, 8, "JSON"], [69, 12, 42, 12], [69, 13, 42, 13, "stringify"], [69, 22, 42, 22], [69, 23, 42, 23, "platformOptions"], [69, 38, 42, 38], [69, 39, 42, 39], [69, 40, 42, 40], [69, 41, 42, 41], [70, 4, 43, 4], [70, 8, 43, 4, "useEffect"], [70, 24, 43, 13], [70, 26, 43, 14], [70, 32, 43, 20], [71, 6, 44, 8], [71, 10, 44, 14, "subscription"], [71, 22, 44, 26], [71, 25, 44, 29, "recorder"], [71, 33, 44, 37], [71, 34, 44, 38, "addListener"], [71, 45, 44, 49], [71, 46, 44, 50, "RECORDING_STATUS_UPDATE"], [71, 69, 44, 73], [71, 71, 44, 76, "status"], [71, 77, 44, 82], [71, 81, 44, 87], [72, 8, 45, 12, "statusListener"], [72, 22, 45, 26], [72, 25, 45, 29, "status"], [72, 31, 45, 35], [72, 32, 45, 36], [73, 6, 46, 8], [73, 7, 46, 9], [73, 8, 46, 10], [74, 6, 47, 8], [74, 13, 47, 15], [74, 19, 47, 21, "subscription"], [74, 31, 47, 33], [74, 32, 47, 34, "remove"], [74, 38, 47, 40], [74, 39, 47, 41], [74, 40, 47, 42], [75, 4, 48, 4], [75, 5, 48, 5], [75, 7, 48, 7], [75, 8, 48, 8, "recorder"], [75, 16, 48, 16], [75, 17, 48, 17, "id"], [75, 19, 48, 19], [75, 20, 48, 20], [75, 21, 48, 21], [76, 4, 49, 4], [76, 11, 49, 11, "recorder"], [76, 19, 49, 19], [77, 2, 50, 0], [78, 2, 51, 0], [79, 2, 52, 7], [79, 11, 52, 16, "useAudioRecorderState"], [79, 32, 52, 37, "useAudioRecorderState"], [79, 33, 52, 38, "recorder"], [79, 41, 52, 46], [79, 43, 52, 64], [80, 4, 52, 64], [80, 8, 52, 48, "interval"], [80, 16, 52, 56], [80, 19, 52, 56, "arguments"], [80, 28, 52, 56], [80, 29, 52, 56, "length"], [80, 35, 52, 56], [80, 43, 52, 56, "arguments"], [80, 52, 52, 56], [80, 60, 52, 56, "undefined"], [80, 69, 52, 56], [80, 72, 52, 56, "arguments"], [80, 81, 52, 56], [80, 87, 52, 59], [80, 90, 52, 62], [81, 4, 53, 4], [81, 8, 53, 4, "_useState"], [81, 17, 53, 4], [81, 20, 53, 30], [81, 24, 53, 30, "useState"], [81, 39, 53, 38], [81, 41, 53, 39, "recorder"], [81, 49, 53, 47], [81, 50, 53, 48, "getStatus"], [81, 59, 53, 57], [81, 60, 53, 58], [81, 61, 53, 59], [81, 62, 53, 60], [82, 6, 53, 60, "_useState2"], [82, 16, 53, 60], [82, 23, 53, 60, "_slicedToArray2"], [82, 38, 53, 60], [82, 39, 53, 60, "default"], [82, 46, 53, 60], [82, 48, 53, 60, "_useState"], [82, 57, 53, 60], [83, 6, 53, 11, "state"], [83, 11, 53, 16], [83, 14, 53, 16, "_useState2"], [83, 24, 53, 16], [84, 6, 53, 18, "setState"], [84, 14, 53, 26], [84, 17, 53, 26, "_useState2"], [84, 27, 53, 26], [85, 4, 54, 4], [85, 8, 54, 4, "useEffect"], [85, 24, 54, 13], [85, 26, 54, 14], [85, 32, 54, 20], [86, 6, 55, 8], [86, 10, 55, 14, "int"], [86, 13, 55, 17], [86, 16, 55, 20, "setInterval"], [86, 27, 55, 31], [86, 28, 55, 32], [86, 34, 55, 38], [87, 8, 56, 12, "setState"], [87, 16, 56, 20], [87, 17, 56, 21, "recorder"], [87, 25, 56, 29], [87, 26, 56, 30, "getStatus"], [87, 35, 56, 39], [87, 36, 56, 40], [87, 37, 56, 41], [87, 38, 56, 42], [88, 6, 57, 8], [88, 7, 57, 9], [88, 9, 57, 11, "interval"], [88, 17, 57, 19], [88, 18, 57, 20], [89, 6, 58, 8], [89, 13, 58, 15], [89, 19, 58, 21, "clearInterval"], [89, 32, 58, 34], [89, 33, 58, 35, "int"], [89, 36, 58, 38], [89, 37, 58, 39], [90, 4, 59, 4], [90, 5, 59, 5], [90, 7, 59, 7], [90, 8, 59, 8, "recorder"], [90, 16, 59, 16], [90, 17, 59, 17, "id"], [90, 19, 59, 19], [90, 20, 59, 20], [90, 21, 59, 21], [91, 4, 60, 4], [91, 11, 60, 11, "state"], [91, 16, 60, 16], [92, 2, 61, 0], [93, 2, 62, 0], [94, 0, 63, 0], [95, 0, 64, 0], [96, 0, 65, 0], [97, 0, 66, 0], [98, 0, 67, 0], [99, 0, 68, 0], [100, 0, 69, 0], [101, 2, 70, 7], [101, 11, 70, 16, "createAudioPlayer"], [101, 28, 70, 33, "createAudioPlayer"], [101, 29, 70, 33], [101, 31, 70, 71], [102, 4, 70, 71], [102, 8, 70, 34, "source"], [102, 14, 70, 40], [102, 17, 70, 40, "arguments"], [102, 26, 70, 40], [102, 27, 70, 40, "length"], [102, 33, 70, 40], [102, 41, 70, 40, "arguments"], [102, 50, 70, 40], [102, 58, 70, 40, "undefined"], [102, 67, 70, 40], [102, 70, 70, 40, "arguments"], [102, 79, 70, 40], [102, 85, 70, 43], [102, 89, 70, 47], [103, 4, 70, 47], [103, 8, 70, 49, "updateInterval"], [103, 22, 70, 63], [103, 25, 70, 63, "arguments"], [103, 34, 70, 63], [103, 35, 70, 63, "length"], [103, 41, 70, 63], [103, 49, 70, 63, "arguments"], [103, 58, 70, 63], [103, 66, 70, 63, "undefined"], [103, 75, 70, 63], [103, 78, 70, 63, "arguments"], [103, 87, 70, 63], [103, 93, 70, 66], [103, 96, 70, 69], [104, 4, 71, 4], [104, 8, 71, 10, "parsedSource"], [104, 20, 71, 22], [104, 23, 71, 25], [104, 27, 71, 25, "resolveSource"], [104, 55, 71, 38], [104, 57, 71, 39, "source"], [104, 63, 71, 45], [104, 64, 71, 46], [105, 4, 72, 4], [105, 11, 72, 11], [105, 15, 72, 15, "AudioModule"], [105, 35, 72, 26], [105, 36, 72, 27, "AudioPlayer"], [105, 47, 72, 38], [105, 48, 72, 39, "parsedSource"], [105, 60, 72, 51], [105, 62, 72, 53, "updateInterval"], [105, 76, 72, 67], [105, 77, 72, 68], [106, 2, 73, 0], [107, 2, 74, 0], [108, 2, 74, 0], [108, 11, 75, 22, "setIsAudioActiveAsync"], [108, 32, 75, 43, "setIsAudioActiveAsync"], [108, 33, 75, 43, "_x"], [108, 35, 75, 43], [109, 4, 75, 43], [109, 11, 75, 43, "_setIsAudioActiveAsync"], [109, 33, 75, 43], [109, 34, 75, 43, "apply"], [109, 39, 75, 43], [109, 46, 75, 43, "arguments"], [109, 55, 75, 43], [110, 2, 75, 43], [110, 4, 78, 0], [111, 2, 78, 0], [111, 11, 78, 0, "_setIsAudioActiveAsync"], [111, 34, 78, 0], [112, 4, 78, 0, "_setIsAudioActiveAsync"], [112, 26, 78, 0], [112, 33, 78, 0, "_asyncToGenerator2"], [112, 51, 78, 0], [112, 52, 78, 0, "default"], [112, 59, 78, 0], [112, 61, 75, 7], [112, 72, 75, 44, "active"], [112, 78, 75, 50], [112, 80, 75, 52], [113, 6, 76, 4], [113, 19, 76, 17, "AudioModule"], [113, 39, 76, 28], [113, 40, 76, 29, "setIsAudioActiveAsync"], [113, 61, 76, 50], [113, 62, 76, 51, "active"], [113, 68, 76, 57], [113, 69, 76, 58], [114, 4, 77, 0], [114, 5, 77, 1], [115, 4, 77, 1], [115, 11, 77, 1, "_setIsAudioActiveAsync"], [115, 33, 77, 1], [115, 34, 77, 1, "apply"], [115, 39, 77, 1], [115, 46, 77, 1, "arguments"], [115, 55, 77, 1], [116, 2, 77, 1], [117, 2, 77, 1], [117, 11, 79, 22, "setAudioModeAsync"], [117, 28, 79, 39, "setAudioModeAsync"], [117, 29, 79, 39, "_x2"], [117, 32, 79, 39], [118, 4, 79, 39], [118, 11, 79, 39, "_setAudioModeAsync"], [118, 29, 79, 39], [118, 30, 79, 39, "apply"], [118, 35, 79, 39], [118, 42, 79, 39, "arguments"], [118, 51, 79, 39], [119, 2, 79, 39], [119, 4, 89, 0], [120, 2, 89, 0], [120, 11, 89, 0, "_setAudioModeAsync"], [120, 30, 89, 0], [121, 4, 89, 0, "_setAudioModeAsync"], [121, 22, 89, 0], [121, 29, 89, 0, "_asyncToGenerator2"], [121, 47, 89, 0], [121, 48, 89, 0, "default"], [121, 55, 89, 0], [121, 57, 79, 7], [121, 68, 79, 40, "mode"], [121, 72, 79, 44], [121, 74, 79, 46], [122, 6, 80, 4], [122, 10, 80, 10, "audioMode"], [122, 19, 80, 19], [122, 22, 80, 22, "Platform"], [122, 43, 80, 30], [122, 44, 80, 31, "OS"], [122, 46, 80, 33], [122, 51, 80, 38], [122, 56, 80, 43], [122, 59, 81, 10, "mode"], [122, 63, 81, 14], [122, 66, 82, 10], [123, 8, 83, 12, "shouldPlayInBackground"], [123, 30, 83, 34], [123, 32, 83, 36, "mode"], [123, 36, 83, 40], [123, 37, 83, 41, "shouldPlayInBackground"], [123, 59, 83, 63], [124, 8, 84, 12, "shouldRouteThroughEarpiece"], [124, 34, 84, 38], [124, 36, 84, 40, "mode"], [124, 40, 84, 44], [124, 41, 84, 45, "shouldRouteThroughEarpiece"], [124, 67, 84, 71], [125, 8, 85, 12, "interruptionMode"], [125, 24, 85, 28], [125, 26, 85, 30, "mode"], [125, 30, 85, 34], [125, 31, 85, 35, "interruptionModeAndroid"], [126, 6, 86, 8], [126, 7, 86, 9], [127, 6, 87, 4], [127, 19, 87, 17, "AudioModule"], [127, 39, 87, 28], [127, 40, 87, 29, "setAudioModeAsync"], [127, 57, 87, 46], [127, 58, 87, 47, "audioMode"], [127, 67, 87, 56], [127, 68, 87, 57], [128, 4, 88, 0], [128, 5, 88, 1], [129, 4, 88, 1], [129, 11, 88, 1, "_setAudioModeAsync"], [129, 29, 88, 1], [129, 30, 88, 1, "apply"], [129, 35, 88, 1], [129, 42, 88, 1, "arguments"], [129, 51, 88, 1], [130, 2, 88, 1], [131, 2, 88, 1], [131, 11, 90, 22, "requestRecordingPermissionsAsync"], [131, 43, 90, 54, "requestRecordingPermissionsAsync"], [131, 44, 90, 54], [132, 4, 90, 54], [132, 11, 90, 54, "_requestRecordingPermissionsAsync"], [132, 44, 90, 54], [132, 45, 90, 54, "apply"], [132, 50, 90, 54], [132, 57, 90, 54, "arguments"], [132, 66, 90, 54], [133, 2, 90, 54], [133, 4, 93, 0], [134, 2, 93, 0], [134, 11, 93, 0, "_requestRecordingPermissionsAsync"], [134, 45, 93, 0], [135, 4, 93, 0, "_requestRecordingPermissionsAsync"], [135, 37, 93, 0], [135, 44, 93, 0, "_asyncToGenerator2"], [135, 62, 93, 0], [135, 63, 93, 0, "default"], [135, 70, 93, 0], [135, 72, 90, 7], [135, 85, 90, 57], [136, 6, 91, 4], [136, 19, 91, 17, "AudioModule"], [136, 39, 91, 28], [136, 40, 91, 29, "requestRecordingPermissionsAsync"], [136, 72, 91, 61], [136, 73, 91, 62], [136, 74, 91, 63], [137, 4, 92, 0], [137, 5, 92, 1], [138, 4, 92, 1], [138, 11, 92, 1, "_requestRecordingPermissionsAsync"], [138, 44, 92, 1], [138, 45, 92, 1, "apply"], [138, 50, 92, 1], [138, 57, 92, 1, "arguments"], [138, 66, 92, 1], [139, 2, 92, 1], [140, 2, 92, 1], [140, 11, 94, 22, "getRecordingPermissionsAsync"], [140, 39, 94, 50, "getRecordingPermissionsAsync"], [140, 40, 94, 50], [141, 4, 94, 50], [141, 11, 94, 50, "_getRecordingPermissionsAsync"], [141, 40, 94, 50], [141, 41, 94, 50, "apply"], [141, 46, 94, 50], [141, 53, 94, 50, "arguments"], [141, 62, 94, 50], [142, 2, 94, 50], [143, 2, 94, 50], [143, 11, 94, 50, "_getRecordingPermissionsAsync"], [143, 41, 94, 50], [144, 4, 94, 50, "_getRecordingPermissionsAsync"], [144, 33, 94, 50], [144, 40, 94, 50, "_asyncToGenerator2"], [144, 58, 94, 50], [144, 59, 94, 50, "default"], [144, 66, 94, 50], [144, 68, 94, 7], [144, 81, 94, 53], [145, 6, 95, 4], [145, 19, 95, 17, "AudioModule"], [145, 39, 95, 28], [145, 40, 95, 29, "getRecordingPermissionsAsync"], [145, 68, 95, 57], [145, 69, 95, 58], [145, 70, 95, 59], [146, 4, 96, 0], [146, 5, 96, 1], [147, 4, 96, 1], [147, 11, 96, 1, "_getRecordingPermissionsAsync"], [147, 40, 96, 1], [147, 41, 96, 1, "apply"], [147, 46, 96, 1], [147, 53, 96, 1, "arguments"], [147, 62, 96, 1], [148, 2, 96, 1], [149, 0, 96, 1], [149, 3]], "functionMap": {"names": ["<global>", "AudioModule.AudioPlayer.prototype.replace", "useAudioPlayer", "useReleasingSharedObject$argument_0", "useAudioPlayerStatus", "useMemo$argument_0", "useAudioSampleListener", "useEffect$argument_0", "<anonymous>", "useAudioRecorder", "recorder.addListener$argument_1", "useAudioRecorderState", "setInterval$argument_0", "createAudioPlayer", "setIsAudioActiveAsync", "setAudioModeAsync", "requestRecordingPermissionsAsync", "getRecordingPermissionsAsync"], "mappings": "AAA;4CCY;CDE;OEE;oCCE,+DD;CFC;OIE;kCCC,0BD;CJE;OME;cCC;eCM,2BD;KDC;CNC;OSE;8CNE;KME;cFC;2EGC;SHE;eCC,2BD;KEC;CTE;OWE;cJE;gCKC;SLE;eCC,wBD;KIC;CXE;OaS;CbG;OcE;CdE;OeE;CfS;OgBE;ChBE;OiBE;CjBE"}}, "type": "js/module"}]}