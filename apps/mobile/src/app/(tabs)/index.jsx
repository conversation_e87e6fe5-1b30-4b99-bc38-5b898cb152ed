import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Clipboard,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  useFonts,
  Poppins_400Regular,
  Poppins_500Medium,
  Poppins_600SemiBold,
} from "@expo-google-fonts/poppins";
import { MessageSquare } from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { useColors } from "@/components/useColors";
import KeyboardAvoidingAnimatedView from "@/components/KeyboardAvoidingAnimatedView";
import {
  useAudioRecorder,
  useAudioRecorderState,
  requestRecordingPermissionsAsync,
  RecordingPresets,
} from "expo-audio";
import { fakeMessages, fakeQuickActions } from "@/utils/fakeData";
import Header from "@/components/Header";
import TextMode from "@/components/TextMode";
import VoiceMode from "@/components/VoiceMode";
import MessageBubble from "@/components/MessageBubble";

export default function BrainstormScreen() {
  const insets = useSafeAreaInsets();
  const colors = useColors();
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
  });

  const recorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const recorderState = useAudioRecorderState(recorder);

  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState("");
  const [isFirstTime, setIsFirstTime] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [quickActions, setQuickActions] = useState([]);
  const [voiceMode, setVoiceMode] = useState(true); // Voice mode on by default
  const [hasPermission, setHasPermission] = useState(false);
  const [isDictating, setIsDictating] = useState(false);
  const [longPressedMessage, setLongPressedMessage] = useState(null);
  const [isContextMenuVisible, setIsContextMenuVisible] = useState(false);
  const [transcript, setTranscript] = useState([]);
  const [isMuted, setIsMuted] = useState(false);

  const scrollViewRef = useRef(null);

  // Request recording permissions
  useEffect(() => {
    (async () => {
      const { granted } = await requestRecordingPermissionsAsync();
      setHasPermission(granted);
      if (!granted && voiceMode) {
        Alert.alert(
          "Permission Required",
          "Microphone access is needed for voice recording. You can still use text mode.",
          [
            { text: "Use Text Mode", onPress: () => setVoiceMode(false) },
            { text: "OK" },
          ],
        );
      }
    })();
  }, [voiceMode]);

  const handleStartBrainstorming = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsFirstTime(false);
    setMessages(fakeMessages);
    setQuickActions(fakeQuickActions);
  };

  const handleSendMessage = (message = inputText) => {
    if (!message.trim()) return;

    const newUserMessage = { role: "user", content: message };
    setMessages((prev) => [...prev, newUserMessage]);
    setInputText("");
    setQuickActions([]);
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        role: "assistant",
        content: "That's a fascinating idea! Could you elaborate on the target audience?",
      };
      setMessages((prev) => [...prev, aiResponse]);
      setQuickActions(fakeQuickActions);
      setIsLoading(false);
    }, 1500);
  };

  const handleQuickAction = (action) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    handleSendMessage(action);
  };

  const toggleVoiceMode = () => {
    if (!voiceMode && !hasPermission) {
      Alert.alert(
        "Permission Required",
        "Microphone access is needed for voice recording.",
        [
          { text: "Cancel" },
          {
            text: "Grant Permission",
            onPress: async () => {
              const { granted } = await requestRecordingPermissionsAsync();
              if (granted) {
                setHasPermission(true);
                setVoiceMode(true);
              }
            },
          },
        ],
      );
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setVoiceMode(!voiceMode);
  };

  const handleVoiceRecord = async () => {
    if (!hasPermission) {
      Alert.alert(
        "Permission Required",
        "Microphone access is needed for voice recording.",
      );
      return;
    }

    try {
      if (recorderState.isRecording) {
        await recorder.stop();

        // Here you would typically process the audio file
        // For now, we'll simulate converting speech to text
        const mockTranscript = "This is a voice message"; // In real app, you'd use speech-to-text service
        setTranscript((prev) => [...prev, { speaker: 'user', text: mockTranscript }]);
        handleSendMessage(mockTranscript);
      } else {
        await recorder.prepareToRecordAsync();
        recorder.record();
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error("Error with voice recording:", error);
      Alert.alert("Error", "Failed to record voice message");
    }
  };

  const handleDictation = async () => {
    if (!hasPermission) {
      Alert.alert(
        "Permission Required",
        "Microphone access is needed for voice recording.",
      );
      return;
    }

    try {
      if (isDictating) {
        await recorder.stop();
        const mockTranscript = "This is a dictated message."; // In real app, you'd use speech-to-text service
        setInputText(inputText + mockTranscript);
        setIsDictating(false);
      } else {
        await recorder.prepareToRecordAsync();
        recorder.record();
        setIsDictating(true);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error("Error with dictation:", error);
      Alert.alert("Error", "Failed to dictate message");
      setIsDictating(false);
    }
  };

  const handleLongPress = (message) => {
    setLongPressedMessage(message);
    setIsContextMenuVisible(true);
  };

  const handleCopyMessage = () => {
    if (longPressedMessage) {
      Clipboard.setString(longPressedMessage.content);
      setIsContextMenuVisible(false);
      setLongPressedMessage(null);
    }
  };

  const handleListenToMessage = () => {
    if (longPressedMessage) {
      // Simulate text-to-speech
      Alert.alert("Listening to message", longPressedMessage.content);
      setIsContextMenuVisible(false);
      setLongPressedMessage(null);
    }
  };

  const handleMute = () => {
    setIsMuted(!isMuted);
  };


  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  if (!fontsLoaded) {
    return null;
  }

  // Welcome Screen
  if (isFirstTime) {
    return (
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        <View
          style={{
            flex: 1,
            paddingTop: insets.top + 60,
            paddingHorizontal: 24,
            paddingBottom: insets.bottom + 24,
            alignItems: "center",
            justifyContent: "center",
          }}>
          {/* Icon */}
          <View
            style={{
              width: 120,
              height: 120,
              borderRadius: 60,
              backgroundColor: colors.primaryUltraLight,
              alignItems: "center",
              justifyContent: "center",
              marginBottom: 32,
            }}>
            <MessageSquare size={48} color={colors.primary} />
          </View>

          {/* Title */}
          <Text
            style={{
              fontSize: 28,
              fontFamily: "Poppins_600SemiBold",
              color: colors.text,
              textAlign: "center",
              marginBottom: 16,
            }}>
            AI Brainstorming Assistant
          </Text>

          {/* Description */}
          <Text
            style={{
              fontSize: 16,
              fontFamily: "Poppins_400Regular",
              color: colors.textSecondary,
              textAlign: "center",
              lineHeight: 24,
              marginBottom: 48,
            }}>
            Get expert guidance for app ideas, business planning, creative
            writing, and more. Start your first session now!
          </Text>

          {/* CTA Button */}
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary,
              borderRadius: 16,
              paddingHorizontal: 32,
              paddingVertical: 16,
              minWidth: 200,
              alignItems: "center",
            }}
            onPress={handleStartBrainstorming}>
            <Text
              style={{
                fontSize: 18,
                fontFamily: "Poppins_600SemiBold",
                color: colors.background,
              }}>
              Start Brainstorming
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Conversation Screen
  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Header 
        voiceMode={voiceMode} 
        onToggleVoiceMode={toggleVoiceMode} 
        onDone={() => Alert.alert("Session Done")} 
      />
      {voiceMode ? (
        <VoiceMode
          isRecording={recorderState.isRecording}
          onRecord={handleVoiceRecord}
          hasPermission={hasPermission}
          isLoading={isLoading}
          transcript={transcript}
          isMuted={isMuted}
          onMute={handleMute}
        />
      ) : (
        <KeyboardAvoidingAnimatedView style={{ flex: 1 }}>
          {/* Messages */}
          <ScrollView
            ref={scrollViewRef}
            style={{ flex: 1 }}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingVertical: 16,
              paddingBottom: 120, // Space for input area
            }}
            showsVerticalScrollIndicator={false}>
            {messages.map((message, index) => (
              <MessageBubble key={index} message={message} onLongPress={handleLongPress} />
            ))}

            {/* Loading indicator */}
            {isLoading && (
              <View
                style={{
                  marginBottom: 16,
                  alignSelf: "flex-start",
                  maxWidth: "80%",
                }}>
                <View
                  style={{
                    backgroundColor: colors.cardBackground,
                    borderRadius: 16,
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    borderWidth: 1,
                    borderColor: colors.outline,
                  }}>
                  <Text
                    style={{
                      fontSize: 16,
                      fontFamily: "Poppins_400Regular",
                      color: colors.textSecondary,
                      lineHeight: 22,
                    }}>
                    Thinking...
                  </Text>
                </View>
              </View>
            )}
          </ScrollView>

          {/* Quick Actions */}
          {quickActions.length > 0 && (
            <View
              style={{
                position: "absolute",
                bottom: insets.bottom + 100,
                left: 16,
                right: 16,
              }}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ gap: 8 }}>
                {quickActions.map((action, index) => (
                  <TouchableOpacity
                    key={index}
                    style={{
                      backgroundColor: colors.primaryUltraLight,
                      borderRadius: 20,
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      borderWidth: 1,
                      borderColor: colors.primary,
                    }}
                    onPress={() => handleQuickAction(action)}>
                    <Text
                      style={{
                        fontSize: 14,
                        fontFamily: "Poppins_500Medium",
                        color: colors.primary,
                      }}>
                      {action}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Input Area */}
          <View
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
            }}>
            <TextMode 
              inputText={inputText}
              onInputChange={setInputText}
              onSendMessage={() => handleSendMessage()}
              onStartDictation={handleDictation}
            />
          </View>
        </KeyboardAvoidingAnimatedView>
      )}

      {/* Context Menu Modal */}
      <Modal
        transparent
        visible={isContextMenuVisible}
        onRequestClose={() => setIsContextMenuVisible(false)}
      >
        <TouchableOpacity
          style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' }}
          activeOpacity={1}
          onPressOut={() => setIsContextMenuVisible(false)}
        >
          <View style={{ backgroundColor: colors.background, borderRadius: 16, padding: 16, width: '80%' }}>
            <TouchableOpacity onPress={handleCopyMessage} style={{ paddingVertical: 12 }}>
              <Text style={{ fontSize: 16, fontFamily: 'Poppins_500Medium', color: colors.text }}>Copy Message</Text>
            </TouchableOpacity>
            {longPressedMessage?.role === 'assistant' && (
              <TouchableOpacity onPress={handleListenToMessage} style={{ paddingVertical: 12 }}>
                <Text style={{ fontSize: 16, fontFamily: 'Poppins_500Medium', color: colors.text }}>Listen to Message</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}