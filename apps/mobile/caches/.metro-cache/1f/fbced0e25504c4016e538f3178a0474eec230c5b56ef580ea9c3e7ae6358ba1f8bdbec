{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  'use strict';\n\n  var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n  module.exports = ReactPropTypesSecret;\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [9, 2, 8, 0], [9, 14, 8, 12], [11, 2, 10, 0], [11, 6, 10, 4, "ReactPropTypesSecret"], [11, 26, 10, 24], [11, 29, 10, 27], [11, 75, 10, 73], [12, 2, 12, 0, "module"], [12, 8, 12, 6], [12, 9, 12, 7, "exports"], [12, 16, 12, 14], [12, 19, 12, 17, "ReactPropTypesSecret"], [12, 39, 12, 37], [13, 0, 12, 38], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}