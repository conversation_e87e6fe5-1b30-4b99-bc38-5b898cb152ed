{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 12,\n    \"height\": 21,\n    \"scales\": [1, 2, 3, 4],\n    \"hash\": \"c560d7e01660506cdb3be1e07257053e\",\n    \"name\": \"back-icon\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"7d40544b395c5949f4646f5e150fe020\", \"a132ecc4ba5c1517ff83c0fb321bc7fc\", \"0ea69b5077e7c4696db85dbcba75b0e1\", \"2d0a9133e39524f138be6d4db9f4851f\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 124, 1, 144], [5, 4, 1, 145], [5, 11, 1, 152], [5, 13, 1, 153], [5, 15, 1, 155], [6, 4, 1, 156], [6, 12, 1, 164], [6, 14, 1, 165], [6, 16, 1, 167], [7, 4, 1, 168], [7, 12, 1, 176], [7, 14, 1, 177], [7, 15, 1, 178], [7, 16, 1, 179], [7, 18, 1, 180], [7, 19, 1, 181], [7, 21, 1, 182], [7, 22, 1, 183], [7, 24, 1, 184], [7, 25, 1, 185], [7, 26, 1, 186], [8, 4, 1, 187], [8, 10, 1, 193], [8, 12, 1, 194], [8, 46, 1, 228], [9, 4, 1, 229], [9, 10, 1, 235], [9, 12, 1, 236], [9, 23, 1, 247], [10, 4, 1, 248], [10, 10, 1, 254], [10, 12, 1, 255], [10, 17, 1, 260], [11, 4, 1, 261], [11, 16, 1, 273], [11, 18, 1, 274], [11, 19, 1, 275], [11, 53, 1, 309], [11, 55, 1, 310], [11, 89, 1, 344], [11, 91, 1, 345], [11, 125, 1, 379], [11, 127, 1, 380], [11, 161, 1, 414], [12, 2, 1, 415], [12, 3, 1, 416], [13, 0, 1, 416], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}