{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./AndroidSwipeRefreshLayoutNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 16, "column": 52}}], "key": "ATJPaoAKZiTjNu5hCuRTofEBee8=", "exportNames": ["*"]}}, {"name": "./PullToRefreshViewNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 19, "column": 44}}], "key": "PzqlF7R5SixMQpvefmtXoVzNSkM=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 52}}], "key": "rq8uaKZ6EGoAq6r0NBzCUcqwzHs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _AndroidSwipeRefreshLayoutNativeComponent = _interopRequireWildcard(require(_dependencyMap[7], \"./AndroidSwipeRefreshLayoutNativeComponent\"));\n  var _PullToRefreshViewNativeComponent = _interopRequireWildcard(require(_dependencyMap[8], \"./PullToRefreshViewNativeComponent\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[9], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[10], \"react-native-css-interop/jsx-runtime\");\n  var _excluded = [\"enabled\", \"colors\", \"progressBackgroundColor\", \"size\"],\n    _excluded2 = [\"tintColor\", \"titleColor\", \"title\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/Components/RefreshControl/RefreshControl.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Platform = require(_dependencyMap[11], \"../../Utilities/Platform\").default;\n  var RefreshControl = /*#__PURE__*/function (_React$Component) {\n    function RefreshControl() {\n      var _this;\n      (0, _classCallCheck2.default)(this, RefreshControl);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, RefreshControl, [...args]);\n      _this._lastNativeRefreshing = false;\n      _this._onRefresh = () => {\n        _this._lastNativeRefreshing = true;\n        _this.props.onRefresh && _this.props.onRefresh();\n        _this.forceUpdate();\n      };\n      _this._setNativeRef = ref => {\n        _this._nativeRef = ref;\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(RefreshControl, _React$Component);\n    return (0, _createClass2.default)(RefreshControl, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this._lastNativeRefreshing = this.props.refreshing;\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        if (this.props.refreshing !== prevProps.refreshing) {\n          this._lastNativeRefreshing = this.props.refreshing;\n        } else if (this.props.refreshing !== this._lastNativeRefreshing && this._nativeRef) {\n          if (Platform.OS === 'android') {\n            _AndroidSwipeRefreshLayoutNativeComponent.Commands.setNativeRefreshing(this._nativeRef, this.props.refreshing);\n          } else {\n            _PullToRefreshViewNativeComponent.Commands.setNativeRefreshing(this._nativeRef, this.props.refreshing);\n          }\n          this._lastNativeRefreshing = this.props.refreshing;\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        if (Platform.OS === 'ios') {\n          var _this$props = this.props,\n            enabled = _this$props.enabled,\n            colors = _this$props.colors,\n            progressBackgroundColor = _this$props.progressBackgroundColor,\n            size = _this$props.size,\n            props = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n          return (0, _jsxRuntime.jsx)(_PullToRefreshViewNativeComponent.default, {\n            ...props,\n            ref: this._setNativeRef,\n            onRefresh: this._onRefresh\n          });\n        } else {\n          var _this$props2 = this.props,\n            tintColor = _this$props2.tintColor,\n            titleColor = _this$props2.titleColor,\n            title = _this$props2.title,\n            _props = (0, _objectWithoutProperties2.default)(_this$props2, _excluded2);\n          return (0, _jsxRuntime.jsx)(_AndroidSwipeRefreshLayoutNativeComponent.default, {\n            ..._props,\n            ref: this._setNativeRef,\n            onRefresh: this._onRefresh\n          });\n        }\n      }\n    }]);\n  }(_react.default.Component);\n  var _default = exports.default = RefreshControl;\n});", "lineCount": 94, "map": [[13, 2, 14, 0], [13, 6, 14, 0, "_AndroidSwipeRefreshLayoutNativeComponent"], [13, 47, 14, 0], [13, 50, 14, 0, "_interopRequireWildcard"], [13, 73, 14, 0], [13, 74, 14, 0, "require"], [13, 81, 14, 0], [13, 82, 14, 0, "_dependencyMap"], [13, 96, 14, 0], [14, 2, 17, 0], [14, 6, 17, 0, "_PullToRefreshViewNativeComponent"], [14, 39, 17, 0], [14, 42, 17, 0, "_interopRequireWildcard"], [14, 65, 17, 0], [14, 66, 17, 0, "require"], [14, 73, 17, 0], [14, 74, 17, 0, "_dependencyMap"], [14, 88, 17, 0], [15, 2, 20, 0], [15, 6, 20, 0, "_react"], [15, 12, 20, 0], [15, 15, 20, 0, "_interopRequireDefault"], [15, 37, 20, 0], [15, 38, 20, 0, "require"], [15, 45, 20, 0], [15, 46, 20, 0, "_dependencyMap"], [15, 60, 20, 0], [16, 2, 20, 26], [16, 6, 20, 26, "_jsxRuntime"], [16, 17, 20, 26], [16, 20, 20, 26, "require"], [16, 27, 20, 26], [16, 28, 20, 26, "_dependencyMap"], [16, 42, 20, 26], [17, 2, 20, 26], [17, 6, 20, 26, "_excluded"], [17, 15, 20, 26], [18, 4, 20, 26, "_excluded2"], [18, 14, 20, 26], [19, 2, 20, 26], [19, 6, 20, 26, "_jsxFileName"], [19, 18, 20, 26], [20, 2, 20, 26], [20, 11, 20, 26, "_interopRequireWildcard"], [20, 35, 20, 26, "e"], [20, 36, 20, 26], [20, 38, 20, 26, "t"], [20, 39, 20, 26], [20, 68, 20, 26, "WeakMap"], [20, 75, 20, 26], [20, 81, 20, 26, "r"], [20, 82, 20, 26], [20, 89, 20, 26, "WeakMap"], [20, 96, 20, 26], [20, 100, 20, 26, "n"], [20, 101, 20, 26], [20, 108, 20, 26, "WeakMap"], [20, 115, 20, 26], [20, 127, 20, 26, "_interopRequireWildcard"], [20, 150, 20, 26], [20, 162, 20, 26, "_interopRequireWildcard"], [20, 163, 20, 26, "e"], [20, 164, 20, 26], [20, 166, 20, 26, "t"], [20, 167, 20, 26], [20, 176, 20, 26, "t"], [20, 177, 20, 26], [20, 181, 20, 26, "e"], [20, 182, 20, 26], [20, 186, 20, 26, "e"], [20, 187, 20, 26], [20, 188, 20, 26, "__esModule"], [20, 198, 20, 26], [20, 207, 20, 26, "e"], [20, 208, 20, 26], [20, 214, 20, 26, "o"], [20, 215, 20, 26], [20, 217, 20, 26, "i"], [20, 218, 20, 26], [20, 220, 20, 26, "f"], [20, 221, 20, 26], [20, 226, 20, 26, "__proto__"], [20, 235, 20, 26], [20, 243, 20, 26, "default"], [20, 250, 20, 26], [20, 252, 20, 26, "e"], [20, 253, 20, 26], [20, 270, 20, 26, "e"], [20, 271, 20, 26], [20, 294, 20, 26, "e"], [20, 295, 20, 26], [20, 320, 20, 26, "e"], [20, 321, 20, 26], [20, 330, 20, 26, "f"], [20, 331, 20, 26], [20, 337, 20, 26, "o"], [20, 338, 20, 26], [20, 341, 20, 26, "t"], [20, 342, 20, 26], [20, 345, 20, 26, "n"], [20, 346, 20, 26], [20, 349, 20, 26, "r"], [20, 350, 20, 26], [20, 358, 20, 26, "o"], [20, 359, 20, 26], [20, 360, 20, 26, "has"], [20, 363, 20, 26], [20, 364, 20, 26, "e"], [20, 365, 20, 26], [20, 375, 20, 26, "o"], [20, 376, 20, 26], [20, 377, 20, 26, "get"], [20, 380, 20, 26], [20, 381, 20, 26, "e"], [20, 382, 20, 26], [20, 385, 20, 26, "o"], [20, 386, 20, 26], [20, 387, 20, 26, "set"], [20, 390, 20, 26], [20, 391, 20, 26, "e"], [20, 392, 20, 26], [20, 394, 20, 26, "f"], [20, 395, 20, 26], [20, 409, 20, 26, "_t"], [20, 411, 20, 26], [20, 415, 20, 26, "e"], [20, 416, 20, 26], [20, 432, 20, 26, "_t"], [20, 434, 20, 26], [20, 441, 20, 26, "hasOwnProperty"], [20, 455, 20, 26], [20, 456, 20, 26, "call"], [20, 460, 20, 26], [20, 461, 20, 26, "e"], [20, 462, 20, 26], [20, 464, 20, 26, "_t"], [20, 466, 20, 26], [20, 473, 20, 26, "i"], [20, 474, 20, 26], [20, 478, 20, 26, "o"], [20, 479, 20, 26], [20, 482, 20, 26, "Object"], [20, 488, 20, 26], [20, 489, 20, 26, "defineProperty"], [20, 503, 20, 26], [20, 508, 20, 26, "Object"], [20, 514, 20, 26], [20, 515, 20, 26, "getOwnPropertyDescriptor"], [20, 539, 20, 26], [20, 540, 20, 26, "e"], [20, 541, 20, 26], [20, 543, 20, 26, "_t"], [20, 545, 20, 26], [20, 552, 20, 26, "i"], [20, 553, 20, 26], [20, 554, 20, 26, "get"], [20, 557, 20, 26], [20, 561, 20, 26, "i"], [20, 562, 20, 26], [20, 563, 20, 26, "set"], [20, 566, 20, 26], [20, 570, 20, 26, "o"], [20, 571, 20, 26], [20, 572, 20, 26, "f"], [20, 573, 20, 26], [20, 575, 20, 26, "_t"], [20, 577, 20, 26], [20, 579, 20, 26, "i"], [20, 580, 20, 26], [20, 584, 20, 26, "f"], [20, 585, 20, 26], [20, 586, 20, 26, "_t"], [20, 588, 20, 26], [20, 592, 20, 26, "e"], [20, 593, 20, 26], [20, 594, 20, 26, "_t"], [20, 596, 20, 26], [20, 607, 20, 26, "f"], [20, 608, 20, 26], [20, 613, 20, 26, "e"], [20, 614, 20, 26], [20, 616, 20, 26, "t"], [20, 617, 20, 26], [21, 2, 20, 26], [21, 11, 20, 26, "_callSuper"], [21, 22, 20, 26, "t"], [21, 23, 20, 26], [21, 25, 20, 26, "o"], [21, 26, 20, 26], [21, 28, 20, 26, "e"], [21, 29, 20, 26], [21, 40, 20, 26, "o"], [21, 41, 20, 26], [21, 48, 20, 26, "_getPrototypeOf2"], [21, 64, 20, 26], [21, 65, 20, 26, "default"], [21, 72, 20, 26], [21, 74, 20, 26, "o"], [21, 75, 20, 26], [21, 82, 20, 26, "_possibleConstructorReturn2"], [21, 109, 20, 26], [21, 110, 20, 26, "default"], [21, 117, 20, 26], [21, 119, 20, 26, "t"], [21, 120, 20, 26], [21, 122, 20, 26, "_isNativeReflectConstruct"], [21, 147, 20, 26], [21, 152, 20, 26, "Reflect"], [21, 159, 20, 26], [21, 160, 20, 26, "construct"], [21, 169, 20, 26], [21, 170, 20, 26, "o"], [21, 171, 20, 26], [21, 173, 20, 26, "e"], [21, 174, 20, 26], [21, 186, 20, 26, "_getPrototypeOf2"], [21, 202, 20, 26], [21, 203, 20, 26, "default"], [21, 210, 20, 26], [21, 212, 20, 26, "t"], [21, 213, 20, 26], [21, 215, 20, 26, "constructor"], [21, 226, 20, 26], [21, 230, 20, 26, "o"], [21, 231, 20, 26], [21, 232, 20, 26, "apply"], [21, 237, 20, 26], [21, 238, 20, 26, "t"], [21, 239, 20, 26], [21, 241, 20, 26, "e"], [21, 242, 20, 26], [22, 2, 20, 26], [22, 11, 20, 26, "_isNativeReflectConstruct"], [22, 37, 20, 26], [22, 51, 20, 26, "t"], [22, 52, 20, 26], [22, 56, 20, 26, "Boolean"], [22, 63, 20, 26], [22, 64, 20, 26, "prototype"], [22, 73, 20, 26], [22, 74, 20, 26, "valueOf"], [22, 81, 20, 26], [22, 82, 20, 26, "call"], [22, 86, 20, 26], [22, 87, 20, 26, "Reflect"], [22, 94, 20, 26], [22, 95, 20, 26, "construct"], [22, 104, 20, 26], [22, 105, 20, 26, "Boolean"], [22, 112, 20, 26], [22, 145, 20, 26, "t"], [22, 146, 20, 26], [22, 159, 20, 26, "_isNativeReflectConstruct"], [22, 184, 20, 26], [22, 196, 20, 26, "_isNativeReflectConstruct"], [22, 197, 20, 26], [22, 210, 20, 26, "t"], [22, 211, 20, 26], [23, 2, 22, 0], [23, 6, 22, 6, "Platform"], [23, 14, 22, 14], [23, 17, 22, 17, "require"], [23, 24, 22, 24], [23, 25, 22, 24, "_dependencyMap"], [23, 39, 22, 24], [23, 71, 22, 51], [23, 72, 22, 52], [23, 73, 22, 53, "default"], [23, 80, 22, 60], [24, 2, 22, 61], [24, 6, 127, 6, "RefreshControl"], [24, 20, 127, 20], [24, 46, 127, 20, "_React$Component"], [24, 62, 127, 20], [25, 4, 127, 20], [25, 13, 127, 20, "RefreshControl"], [25, 28, 127, 20], [26, 6, 127, 20], [26, 10, 127, 20, "_this"], [26, 15, 127, 20], [27, 6, 127, 20], [27, 10, 127, 20, "_classCallCheck2"], [27, 26, 127, 20], [27, 27, 127, 20, "default"], [27, 34, 127, 20], [27, 42, 127, 20, "RefreshControl"], [27, 56, 127, 20], [28, 6, 127, 20], [28, 15, 127, 20, "_len"], [28, 19, 127, 20], [28, 22, 127, 20, "arguments"], [28, 31, 127, 20], [28, 32, 127, 20, "length"], [28, 38, 127, 20], [28, 40, 127, 20, "args"], [28, 44, 127, 20], [28, 51, 127, 20, "Array"], [28, 56, 127, 20], [28, 57, 127, 20, "_len"], [28, 61, 127, 20], [28, 64, 127, 20, "_key"], [28, 68, 127, 20], [28, 74, 127, 20, "_key"], [28, 78, 127, 20], [28, 81, 127, 20, "_len"], [28, 85, 127, 20], [28, 87, 127, 20, "_key"], [28, 91, 127, 20], [29, 8, 127, 20, "args"], [29, 12, 127, 20], [29, 13, 127, 20, "_key"], [29, 17, 127, 20], [29, 21, 127, 20, "arguments"], [29, 30, 127, 20], [29, 31, 127, 20, "_key"], [29, 35, 127, 20], [30, 6, 127, 20], [31, 6, 127, 20, "_this"], [31, 11, 127, 20], [31, 14, 127, 20, "_callSuper"], [31, 24, 127, 20], [31, 31, 127, 20, "RefreshControl"], [31, 45, 127, 20], [31, 51, 127, 20, "args"], [31, 55, 127, 20], [32, 6, 127, 20, "_this"], [32, 11, 127, 20], [32, 12, 132, 2, "_lastNativeRefreshing"], [32, 33, 132, 23], [32, 36, 132, 35], [32, 41, 132, 40], [33, 6, 132, 40, "_this"], [33, 11, 132, 40], [33, 12, 186, 2, "_onRefresh"], [33, 22, 186, 12], [33, 25, 186, 15], [33, 31, 186, 21], [34, 8, 187, 4, "_this"], [34, 13, 187, 4], [34, 14, 187, 9, "_lastNativeRefreshing"], [34, 35, 187, 30], [34, 38, 187, 33], [34, 42, 187, 37], [35, 8, 190, 4, "_this"], [35, 13, 190, 4], [35, 14, 190, 9, "props"], [35, 19, 190, 14], [35, 20, 190, 15, "onRefresh"], [35, 29, 190, 24], [35, 33, 190, 28, "_this"], [35, 38, 190, 28], [35, 39, 190, 33, "props"], [35, 44, 190, 38], [35, 45, 190, 39, "onRefresh"], [35, 54, 190, 48], [35, 55, 190, 49], [35, 56, 190, 50], [36, 8, 194, 4, "_this"], [36, 13, 194, 4], [36, 14, 194, 9, "forceUpdate"], [36, 25, 194, 20], [36, 26, 194, 21], [36, 27, 194, 22], [37, 6, 195, 2], [37, 7, 195, 3], [38, 6, 195, 3, "_this"], [38, 11, 195, 3], [38, 12, 197, 2, "_setNativeRef"], [38, 25, 197, 15], [38, 28, 198, 4, "ref"], [38, 31, 201, 5], [38, 35, 202, 7], [39, 8, 203, 4, "_this"], [39, 13, 203, 4], [39, 14, 203, 9, "_nativeRef"], [39, 24, 203, 19], [39, 27, 203, 22, "ref"], [39, 30, 203, 25], [40, 6, 204, 2], [40, 7, 204, 3], [41, 6, 204, 3], [41, 13, 204, 3, "_this"], [41, 18, 204, 3], [42, 4, 204, 3], [43, 4, 204, 3], [43, 8, 204, 3, "_inherits2"], [43, 18, 204, 3], [43, 19, 204, 3, "default"], [43, 26, 204, 3], [43, 28, 204, 3, "RefreshControl"], [43, 42, 204, 3], [43, 44, 204, 3, "_React$Component"], [43, 60, 204, 3], [44, 4, 204, 3], [44, 15, 204, 3, "_createClass2"], [44, 28, 204, 3], [44, 29, 204, 3, "default"], [44, 36, 204, 3], [44, 38, 204, 3, "RefreshControl"], [44, 52, 204, 3], [45, 6, 204, 3, "key"], [45, 9, 204, 3], [46, 6, 204, 3, "value"], [46, 11, 204, 3], [46, 13, 134, 2], [46, 22, 134, 2, "componentDidMount"], [46, 39, 134, 19, "componentDidMount"], [46, 40, 134, 19], [46, 42, 134, 22], [47, 8, 135, 4], [47, 12, 135, 8], [47, 13, 135, 9, "_lastNativeRefreshing"], [47, 34, 135, 30], [47, 37, 135, 33], [47, 41, 135, 37], [47, 42, 135, 38, "props"], [47, 47, 135, 43], [47, 48, 135, 44, "refreshing"], [47, 58, 135, 54], [48, 6, 136, 2], [49, 4, 136, 3], [50, 6, 136, 3, "key"], [50, 9, 136, 3], [51, 6, 136, 3, "value"], [51, 11, 136, 3], [51, 13, 138, 2], [51, 22, 138, 2, "componentDidUpdate"], [51, 40, 138, 20, "componentDidUpdate"], [51, 41, 138, 21, "prevProps"], [51, 50, 138, 51], [51, 52, 138, 53], [52, 8, 142, 4], [52, 12, 142, 8], [52, 16, 142, 12], [52, 17, 142, 13, "props"], [52, 22, 142, 18], [52, 23, 142, 19, "refreshing"], [52, 33, 142, 29], [52, 38, 142, 34, "prevProps"], [52, 47, 142, 43], [52, 48, 142, 44, "refreshing"], [52, 58, 142, 54], [52, 60, 142, 56], [53, 10, 143, 6], [53, 14, 143, 10], [53, 15, 143, 11, "_lastNativeRefreshing"], [53, 36, 143, 32], [53, 39, 143, 35], [53, 43, 143, 39], [53, 44, 143, 40, "props"], [53, 49, 143, 45], [53, 50, 143, 46, "refreshing"], [53, 60, 143, 56], [54, 8, 144, 4], [54, 9, 144, 5], [54, 15, 144, 11], [54, 19, 145, 6], [54, 23, 145, 10], [54, 24, 145, 11, "props"], [54, 29, 145, 16], [54, 30, 145, 17, "refreshing"], [54, 40, 145, 27], [54, 45, 145, 32], [54, 49, 145, 36], [54, 50, 145, 37, "_lastNativeRefreshing"], [54, 71, 145, 58], [54, 75, 146, 6], [54, 79, 146, 10], [54, 80, 146, 11, "_nativeRef"], [54, 90, 146, 21], [54, 92, 147, 6], [55, 10, 148, 6], [55, 14, 148, 10, "Platform"], [55, 22, 148, 18], [55, 23, 148, 19, "OS"], [55, 25, 148, 21], [55, 30, 148, 26], [55, 39, 148, 35], [55, 41, 148, 37], [56, 12, 149, 8, "AndroidSwipeRefreshLayoutCommands"], [56, 62, 149, 41], [56, 63, 149, 42, "setNativeRefreshing"], [56, 82, 149, 61], [56, 83, 150, 10], [56, 87, 150, 14], [56, 88, 150, 15, "_nativeRef"], [56, 98, 150, 25], [56, 100, 151, 10], [56, 104, 151, 14], [56, 105, 151, 15, "props"], [56, 110, 151, 20], [56, 111, 151, 21, "refreshing"], [56, 121, 152, 8], [56, 122, 152, 9], [57, 10, 153, 6], [57, 11, 153, 7], [57, 17, 153, 13], [58, 12, 154, 8, "PullToRefreshCommands"], [58, 54, 154, 29], [58, 55, 154, 30, "setNativeRefreshing"], [58, 74, 154, 49], [58, 75, 155, 10], [58, 79, 155, 14], [58, 80, 155, 15, "_nativeRef"], [58, 90, 155, 25], [58, 92, 156, 10], [58, 96, 156, 14], [58, 97, 156, 15, "props"], [58, 102, 156, 20], [58, 103, 156, 21, "refreshing"], [58, 113, 157, 8], [58, 114, 157, 9], [59, 10, 158, 6], [60, 10, 159, 6], [60, 14, 159, 10], [60, 15, 159, 11, "_lastNativeRefreshing"], [60, 36, 159, 32], [60, 39, 159, 35], [60, 43, 159, 39], [60, 44, 159, 40, "props"], [60, 49, 159, 45], [60, 50, 159, 46, "refreshing"], [60, 60, 159, 56], [61, 8, 160, 4], [62, 6, 161, 2], [63, 4, 161, 3], [64, 6, 161, 3, "key"], [64, 9, 161, 3], [65, 6, 161, 3, "value"], [65, 11, 161, 3], [65, 13, 163, 2], [65, 22, 163, 2, "render"], [65, 28, 163, 8, "render"], [65, 29, 163, 8], [65, 31, 163, 23], [66, 8, 164, 4], [66, 12, 164, 8, "Platform"], [66, 20, 164, 16], [66, 21, 164, 17, "OS"], [66, 23, 164, 19], [66, 28, 164, 24], [66, 33, 164, 29], [66, 35, 164, 31], [67, 10, 165, 6], [67, 14, 165, 6, "_this$props"], [67, 25, 165, 6], [67, 28, 166, 8], [67, 32, 166, 12], [67, 33, 166, 13, "props"], [67, 38, 166, 18], [68, 12, 165, 13, "enabled"], [68, 19, 165, 20], [68, 22, 165, 20, "_this$props"], [68, 33, 165, 20], [68, 34, 165, 13, "enabled"], [68, 41, 165, 20], [69, 12, 165, 22, "colors"], [69, 18, 165, 28], [69, 21, 165, 28, "_this$props"], [69, 32, 165, 28], [69, 33, 165, 22, "colors"], [69, 39, 165, 28], [70, 12, 165, 30, "progressBackgroundColor"], [70, 35, 165, 53], [70, 38, 165, 53, "_this$props"], [70, 49, 165, 53], [70, 50, 165, 30, "progressBackgroundColor"], [70, 73, 165, 53], [71, 12, 165, 55, "size"], [71, 16, 165, 59], [71, 19, 165, 59, "_this$props"], [71, 30, 165, 59], [71, 31, 165, 55, "size"], [71, 35, 165, 59], [72, 12, 165, 64, "props"], [72, 17, 165, 69], [72, 24, 165, 69, "_objectWithoutProperties2"], [72, 49, 165, 69], [72, 50, 165, 69, "default"], [72, 57, 165, 69], [72, 59, 165, 69, "_this$props"], [72, 70, 165, 69], [72, 72, 165, 69, "_excluded"], [72, 81, 165, 69], [73, 10, 167, 6], [73, 17, 168, 8], [73, 21, 168, 8, "_jsxRuntime"], [73, 32, 168, 8], [73, 33, 168, 8, "jsx"], [73, 36, 168, 8], [73, 38, 168, 9, "_PullToRefreshViewNativeComponent"], [73, 71, 168, 9], [73, 72, 168, 9, "default"], [73, 79, 168, 41], [74, 12, 168, 41], [74, 15, 169, 14, "props"], [74, 20, 169, 19], [75, 12, 170, 10, "ref"], [75, 15, 170, 13], [75, 17, 170, 15], [75, 21, 170, 19], [75, 22, 170, 20, "_setNativeRef"], [75, 35, 170, 34], [76, 12, 171, 10, "onRefresh"], [76, 21, 171, 19], [76, 23, 171, 21], [76, 27, 171, 25], [76, 28, 171, 26, "_onRefresh"], [77, 10, 171, 37], [77, 11, 172, 9], [77, 12, 172, 10], [78, 8, 174, 4], [78, 9, 174, 5], [78, 15, 174, 11], [79, 10, 175, 6], [79, 14, 175, 6, "_this$props2"], [79, 26, 175, 6], [79, 29, 175, 55], [79, 33, 175, 59], [79, 34, 175, 60, "props"], [79, 39, 175, 65], [80, 12, 175, 13, "tintColor"], [80, 21, 175, 22], [80, 24, 175, 22, "_this$props2"], [80, 36, 175, 22], [80, 37, 175, 13, "tintColor"], [80, 46, 175, 22], [81, 12, 175, 24, "titleColor"], [81, 22, 175, 34], [81, 25, 175, 34, "_this$props2"], [81, 37, 175, 34], [81, 38, 175, 24, "titleColor"], [81, 48, 175, 34], [82, 12, 175, 36, "title"], [82, 17, 175, 41], [82, 20, 175, 41, "_this$props2"], [82, 32, 175, 41], [82, 33, 175, 36, "title"], [82, 38, 175, 41], [83, 12, 175, 46, "props"], [83, 18, 175, 51], [83, 25, 175, 51, "_objectWithoutProperties2"], [83, 50, 175, 51], [83, 51, 175, 51, "default"], [83, 58, 175, 51], [83, 60, 175, 51, "_this$props2"], [83, 72, 175, 51], [83, 74, 175, 51, "_excluded2"], [83, 84, 175, 51], [84, 10, 176, 6], [84, 17, 177, 8], [84, 21, 177, 8, "_jsxRuntime"], [84, 32, 177, 8], [84, 33, 177, 8, "jsx"], [84, 36, 177, 8], [84, 38, 177, 9, "_AndroidSwipeRefreshLayoutNativeComponent"], [84, 79, 177, 9], [84, 80, 177, 9, "default"], [84, 87, 177, 49], [85, 12, 177, 49], [85, 15, 178, 14, "props"], [85, 21, 178, 19], [86, 12, 179, 10, "ref"], [86, 15, 179, 13], [86, 17, 179, 15], [86, 21, 179, 19], [86, 22, 179, 20, "_setNativeRef"], [86, 35, 179, 34], [87, 12, 180, 10, "onRefresh"], [87, 21, 180, 19], [87, 23, 180, 21], [87, 27, 180, 25], [87, 28, 180, 26, "_onRefresh"], [88, 10, 180, 37], [88, 11, 181, 9], [88, 12, 181, 10], [89, 8, 183, 4], [90, 6, 184, 2], [91, 4, 184, 3], [92, 2, 184, 3], [92, 4, 127, 29, "React"], [92, 18, 127, 34], [92, 19, 127, 35, "Component"], [92, 28, 127, 44], [93, 2, 127, 44], [93, 6, 127, 44, "_default"], [93, 14, 127, 44], [93, 17, 127, 44, "exports"], [93, 24, 127, 44], [93, 25, 127, 44, "default"], [93, 32, 127, 44], [93, 35, 207, 15, "RefreshControl"], [93, 49, 207, 29], [94, 0, 207, 29], [94, 3]], "functionMap": {"names": ["<global>", "RefreshControl", "componentDidMount", "componentDidUpdate", "render", "_onRefresh", "_setNativeRef"], "mappings": "AAA;AC8H;ECO;GDE;EEE;GFuB;EGE;GHqB;eIE;GJS;kBKE;GLO;CDC"}}, "type": "js/module"}]}