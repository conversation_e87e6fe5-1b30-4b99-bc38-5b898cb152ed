{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 48, "index": 63}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isAndroid = isAndroid;\n  exports.isChromeDebugger = isChromeDebugger;\n  exports.isFabric = isFabric;\n  exports.isJest = isJest;\n  exports.isReact19 = isReact19;\n  exports.isWeb = isWeb;\n  exports.isWindowAvailable = isWindowAvailable;\n  exports.shouldBeUseWeb = shouldBeUseWeb;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  // This type is necessary since some libraries tend to do a lib check\n  // and this file causes type errors on `global` access.\n\n  function isJest() {\n    return !!process.env.JEST_WORKER_ID;\n  }\n\n  // `isChromeDebugger` also returns true in Jest environment, so `isJest()` check should always be performed first\n  function isChromeDebugger() {\n    return (!global.nativeCallSyncHook || !!global.__REMOTEDEV__) && !global.RN$Bridgeless;\n  }\n  function isWeb() {\n    return _Platform.default.OS === 'web';\n  }\n  function isAndroid() {\n    return _Platform.default.OS === 'android';\n  }\n  function isWindows() {\n    return _Platform.default.OS === 'windows';\n  }\n  function shouldBeUseWeb() {\n    return isJest() || isChromeDebugger() || isWeb() || isWindows();\n  }\n  function isFabric() {\n    return !!global._IS_FABRIC;\n  }\n  function isReact19() {\n    return _react.version.startsWith('19.');\n  }\n  function isWindowAvailable() {\n    // the window object is unavailable when building the server portion of a site that uses SSG\n    // this function shouldn't be used to conditionally render components\n    // https://www.joshwcomeau.com/react/the-perils-of-rehydration/\n    // @ts-ignore Fallback if `window` is undefined.\n    return typeof window !== 'undefined';\n  }\n});", "lineCount": 54, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "isAndroid"], [8, 19, 1, 13], [8, 22, 1, 13, "isAndroid"], [8, 31, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "isChromeDebugger"], [9, 26, 1, 13], [9, 29, 1, 13, "isChromeDebugger"], [9, 45, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "isF<PERSON><PERSON>"], [10, 18, 1, 13], [10, 21, 1, 13, "isF<PERSON><PERSON>"], [10, 29, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "isJest"], [11, 16, 1, 13], [11, 19, 1, 13, "isJest"], [11, 25, 1, 13], [12, 2, 1, 13, "exports"], [12, 9, 1, 13], [12, 10, 1, 13, "isReact19"], [12, 19, 1, 13], [12, 22, 1, 13, "isReact19"], [12, 31, 1, 13], [13, 2, 1, 13, "exports"], [13, 9, 1, 13], [13, 10, 1, 13, "isWeb"], [13, 15, 1, 13], [13, 18, 1, 13, "isWeb"], [13, 23, 1, 13], [14, 2, 1, 13, "exports"], [14, 9, 1, 13], [14, 10, 1, 13, "isWindowAvailable"], [14, 27, 1, 13], [14, 30, 1, 13, "isWindowAvailable"], [14, 47, 1, 13], [15, 2, 1, 13, "exports"], [15, 9, 1, 13], [15, 10, 1, 13, "shouldBeUseWeb"], [15, 24, 1, 13], [15, 27, 1, 13, "shouldBeUseWeb"], [15, 41, 1, 13], [16, 2, 3, 0], [16, 6, 3, 0, "_react"], [16, 12, 3, 0], [16, 15, 3, 0, "require"], [16, 22, 3, 0], [16, 23, 3, 0, "_dependencyMap"], [16, 37, 3, 0], [17, 2, 3, 48], [17, 6, 3, 48, "_Platform"], [17, 15, 3, 48], [17, 18, 3, 48, "_interopRequireDefault"], [17, 40, 3, 48], [17, 41, 3, 48, "require"], [17, 48, 3, 48], [17, 49, 3, 48, "_dependencyMap"], [17, 63, 3, 48], [18, 2, 6, 0], [19, 2, 7, 0], [21, 2, 9, 7], [21, 11, 9, 16, "isJest"], [21, 17, 9, 22, "isJest"], [21, 18, 9, 22], [21, 20, 9, 25], [22, 4, 10, 2], [22, 11, 10, 9], [22, 12, 10, 10], [22, 13, 10, 11, "process"], [22, 20, 10, 18], [22, 21, 10, 19, "env"], [22, 24, 10, 22], [22, 25, 10, 23, "JEST_WORKER_ID"], [22, 39, 10, 37], [23, 2, 11, 0], [25, 2, 13, 0], [26, 2, 14, 7], [26, 11, 14, 16, "isChromeDebugger"], [26, 27, 14, 32, "isChromeDebugger"], [26, 28, 14, 32], [26, 30, 14, 35], [27, 4, 15, 2], [27, 11, 15, 9], [27, 12, 15, 10], [27, 13, 15, 11, "global"], [27, 19, 15, 17], [27, 20, 15, 18, "nativeCallSyncHook"], [27, 38, 15, 36], [27, 42, 15, 40], [27, 43, 15, 41], [27, 44, 15, 42, "global"], [27, 50, 15, 48], [27, 51, 15, 49, "__REMOTEDEV__"], [27, 64, 15, 62], [27, 69, 15, 67], [27, 70, 15, 68, "global"], [27, 76, 15, 74], [27, 77, 15, 75, "RN$Bridgeless"], [27, 90, 15, 88], [28, 2, 16, 0], [29, 2, 17, 7], [29, 11, 17, 16, "isWeb"], [29, 16, 17, 21, "isWeb"], [29, 17, 17, 21], [29, 19, 17, 24], [30, 4, 18, 2], [30, 11, 18, 9, "Platform"], [30, 28, 18, 17], [30, 29, 18, 18, "OS"], [30, 31, 18, 20], [30, 36, 18, 25], [30, 41, 18, 30], [31, 2, 19, 0], [32, 2, 20, 7], [32, 11, 20, 16, "isAndroid"], [32, 20, 20, 25, "isAndroid"], [32, 21, 20, 25], [32, 23, 20, 28], [33, 4, 21, 2], [33, 11, 21, 9, "Platform"], [33, 28, 21, 17], [33, 29, 21, 18, "OS"], [33, 31, 21, 20], [33, 36, 21, 25], [33, 45, 21, 34], [34, 2, 22, 0], [35, 2, 23, 0], [35, 11, 23, 9, "isWindows"], [35, 20, 23, 18, "isWindows"], [35, 21, 23, 18], [35, 23, 23, 21], [36, 4, 24, 2], [36, 11, 24, 9, "Platform"], [36, 28, 24, 17], [36, 29, 24, 18, "OS"], [36, 31, 24, 20], [36, 36, 24, 25], [36, 45, 24, 34], [37, 2, 25, 0], [38, 2, 26, 7], [38, 11, 26, 16, "shouldBeUseWeb"], [38, 25, 26, 30, "shouldBeUseWeb"], [38, 26, 26, 30], [38, 28, 26, 33], [39, 4, 27, 2], [39, 11, 27, 9, "isJest"], [39, 17, 27, 15], [39, 18, 27, 16], [39, 19, 27, 17], [39, 23, 27, 21, "isChromeDebugger"], [39, 39, 27, 37], [39, 40, 27, 38], [39, 41, 27, 39], [39, 45, 27, 43, "isWeb"], [39, 50, 27, 48], [39, 51, 27, 49], [39, 52, 27, 50], [39, 56, 27, 54, "isWindows"], [39, 65, 27, 63], [39, 66, 27, 64], [39, 67, 27, 65], [40, 2, 28, 0], [41, 2, 29, 7], [41, 11, 29, 16, "isF<PERSON><PERSON>"], [41, 19, 29, 24, "isF<PERSON><PERSON>"], [41, 20, 29, 24], [41, 22, 29, 27], [42, 4, 30, 2], [42, 11, 30, 9], [42, 12, 30, 10], [42, 13, 30, 11, "global"], [42, 19, 30, 17], [42, 20, 30, 18, "_IS_FABRIC"], [42, 30, 30, 28], [43, 2, 31, 0], [44, 2, 32, 7], [44, 11, 32, 16, "isReact19"], [44, 20, 32, 25, "isReact19"], [44, 21, 32, 25], [44, 23, 32, 28], [45, 4, 33, 2], [45, 11, 33, 9, "reactVersion"], [45, 25, 33, 21], [45, 26, 33, 22, "startsWith"], [45, 36, 33, 32], [45, 37, 33, 33], [45, 42, 33, 38], [45, 43, 33, 39], [46, 2, 34, 0], [47, 2, 35, 7], [47, 11, 35, 16, "isWindowAvailable"], [47, 28, 35, 33, "isWindowAvailable"], [47, 29, 35, 33], [47, 31, 35, 36], [48, 4, 36, 2], [49, 4, 37, 2], [50, 4, 38, 2], [51, 4, 39, 2], [52, 4, 40, 2], [52, 11, 40, 9], [52, 18, 40, 16, "window"], [52, 24, 40, 22], [52, 29, 40, 27], [52, 40, 40, 38], [53, 2, 41, 0], [54, 0, 41, 1], [54, 3]], "functionMap": {"names": ["<global>", "isJest", "isChromeDebugger", "isWeb", "isAndroid", "isWindows", "shouldBeUseWeb", "isF<PERSON><PERSON>", "isReact19", "isWindowAvailable"], "mappings": "AAA;OCQ;CDE;OEG;CFE;OGC;CHE;OIC;CJE;AKC;CLE;OMC;CNE;OOC;CPE;OQC;CRE;OSC;CTM"}}, "type": "js/module"}]}