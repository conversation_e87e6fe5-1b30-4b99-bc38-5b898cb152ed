{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2F%40react-navigation%2Felements%2Flib%2Fmodule%2Fassets\",\n    \"width\": 24,\n    \"height\": 24,\n    \"scales\": [1, 2, 3, 4],\n    \"hash\": \"7842eab38ac1ef8af0adf4f8147ed837\",\n    \"name\": \"search-icon\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"dad2fa9f4394a630f0f9a0d6dabd44bc\", \"f3a81967828232c893d547162e922764\", \"d62ddc38b69aff346c20a28774933d6a\", \"f3a81967828232c893d547162e922764\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 124, 1, 144], [5, 4, 1, 145], [5, 11, 1, 152], [5, 13, 1, 153], [5, 15, 1, 155], [6, 4, 1, 156], [6, 12, 1, 164], [6, 14, 1, 165], [6, 16, 1, 167], [7, 4, 1, 168], [7, 12, 1, 176], [7, 14, 1, 177], [7, 15, 1, 178], [7, 16, 1, 179], [7, 18, 1, 180], [7, 19, 1, 181], [7, 21, 1, 182], [7, 22, 1, 183], [7, 24, 1, 184], [7, 25, 1, 185], [7, 26, 1, 186], [8, 4, 1, 187], [8, 10, 1, 193], [8, 12, 1, 194], [8, 46, 1, 228], [9, 4, 1, 229], [9, 10, 1, 235], [9, 12, 1, 236], [9, 25, 1, 249], [10, 4, 1, 250], [10, 10, 1, 256], [10, 12, 1, 257], [10, 17, 1, 262], [11, 4, 1, 263], [11, 16, 1, 275], [11, 18, 1, 276], [11, 19, 1, 277], [11, 53, 1, 311], [11, 55, 1, 312], [11, 89, 1, 346], [11, 91, 1, 347], [11, 125, 1, 381], [11, 127, 1, 382], [11, 161, 1, 416], [12, 2, 1, 417], [12, 3, 1, 418], [13, 0, 1, 418], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}