{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenContentWrapperNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 69}, "end": {"line": 3, "column": 96, "index": 165}}], "key": "ywwcVq1R4ZKAZKjzGhCP16WL4WI=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _ScreenContentWrapperNativeComponent = _interopRequireDefault(require(_dependencyMap[2], \"../fabric/ScreenContentWrapperNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[3], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-screens/src/components/ScreenContentWrapper.tsx\";\n  function ScreenContentWrapper(props) {\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScreenContentWrapperNativeComponent.default, {\n      collapsable: false,\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 10\n    }, this);\n  }\n  var _default = exports.default = ScreenContentWrapper;\n});", "lineCount": 22, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_ScreenContentWrapperNativeComponent"], [8, 42, 3, 0], [8, 45, 3, 0, "_interopRequireDefault"], [8, 67, 3, 0], [8, 68, 3, 0, "require"], [8, 75, 3, 0], [8, 76, 3, 0, "_dependencyMap"], [8, 90, 3, 0], [9, 2, 3, 96], [9, 6, 3, 96, "_jsxDevRuntime"], [9, 20, 3, 96], [9, 23, 3, 96, "require"], [9, 30, 3, 96], [9, 31, 3, 96, "_dependencyMap"], [9, 45, 3, 96], [10, 2, 3, 96], [10, 6, 3, 96, "_jsxFileName"], [10, 18, 3, 96], [11, 2, 5, 0], [11, 11, 5, 9, "ScreenContentWrapper"], [11, 31, 5, 29, "ScreenContentWrapper"], [11, 32, 5, 30, "props"], [11, 37, 5, 46], [11, 39, 5, 48], [12, 4, 6, 2], [12, 24, 6, 9], [12, 28, 6, 9, "_jsxDevRuntime"], [12, 42, 6, 9], [12, 43, 6, 9, "jsxDEV"], [12, 49, 6, 9], [12, 51, 6, 10, "_ScreenContentWrapperNativeComponent"], [12, 87, 6, 10], [12, 88, 6, 10, "default"], [12, 95, 6, 45], [13, 6, 6, 46, "collapsable"], [13, 17, 6, 57], [13, 19, 6, 59], [13, 24, 6, 65], [14, 6, 6, 65], [14, 9, 6, 70, "props"], [15, 4, 6, 75], [16, 6, 6, 75, "fileName"], [16, 14, 6, 75], [16, 16, 6, 75, "_jsxFileName"], [16, 28, 6, 75], [17, 6, 6, 75, "lineNumber"], [17, 16, 6, 75], [18, 6, 6, 75, "columnNumber"], [18, 18, 6, 75], [19, 4, 6, 75], [19, 11, 6, 78], [19, 12, 6, 79], [20, 2, 7, 0], [21, 2, 7, 1], [21, 6, 7, 1, "_default"], [21, 14, 7, 1], [21, 17, 7, 1, "exports"], [21, 24, 7, 1], [21, 25, 7, 1, "default"], [21, 32, 7, 1], [21, 35, 9, 15, "ScreenContentWrapper"], [21, 55, 9, 35], [22, 0, 9, 35], [22, 3]], "functionMap": {"names": ["<global>", "ScreenContentWrapper"], "mappings": "AAA;ACI;CDE"}}, "type": "js/module"}]}