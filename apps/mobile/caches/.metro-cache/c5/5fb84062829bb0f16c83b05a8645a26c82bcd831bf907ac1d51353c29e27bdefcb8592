{"dependencies": [{"name": "../animationParser.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 74, "index": 89}}], "key": "O2GgmGIlz6MOk52iJY+MJ4hFpWQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PinwheelData = exports.Pinwheel = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser.js\");\n  const DEFAULT_PINWHEEL_TIME = 0.3;\n  const PinwheelData = exports.PinwheelData = {\n    PinwheelIn: {\n      name: 'PinwheelIn',\n      style: {\n        0: {\n          transform: [{\n            rotate: '5rad',\n            scale: 0\n          }],\n          opacity: 0\n        },\n        100: {\n          transform: [{\n            rotate: '0deg',\n            scale: 1\n          }],\n          opacity: 1\n        }\n      },\n      duration: DEFAULT_PINWHEEL_TIME\n    },\n    PinwheelOut: {\n      name: 'PinwheelOut',\n      style: {\n        0: {\n          transform: [{\n            rotate: '0rad',\n            scale: 1\n          }],\n          opacity: 1\n        },\n        100: {\n          transform: [{\n            rotate: '5rad',\n            scale: 0\n          }],\n          opacity: 0\n        }\n      },\n      duration: DEFAULT_PINWHEEL_TIME\n    }\n  };\n  const Pinwheel = exports.Pinwheel = {\n    PinwheelIn: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(PinwheelData.PinwheelIn),\n      duration: PinwheelData.PinwheelIn.duration\n    },\n    PinwheelOut: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(PinwheelData.PinwheelOut),\n      duration: PinwheelData.PinwheelOut.duration\n    }\n  };\n});", "lineCount": 62, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "PinwheelData"], [7, 22, 1, 13], [7, 25, 1, 13, "exports"], [7, 32, 1, 13], [7, 33, 1, 13, "Pinwheel"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_animation<PERSON><PERSON>er"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "DEFAULT_PINWHEEL_TIME"], [9, 29, 4, 27], [9, 32, 4, 30], [9, 35, 4, 33], [10, 2, 5, 7], [10, 8, 5, 13, "PinwheelData"], [10, 20, 5, 25], [10, 23, 5, 25, "exports"], [10, 30, 5, 25], [10, 31, 5, 25, "PinwheelData"], [10, 43, 5, 25], [10, 46, 5, 28], [11, 4, 6, 2, "PinwheelIn"], [11, 14, 6, 12], [11, 16, 6, 14], [12, 6, 7, 4, "name"], [12, 10, 7, 8], [12, 12, 7, 10], [12, 24, 7, 22], [13, 6, 8, 4, "style"], [13, 11, 8, 9], [13, 13, 8, 11], [14, 8, 9, 6], [14, 9, 9, 7], [14, 11, 9, 9], [15, 10, 10, 8, "transform"], [15, 19, 10, 17], [15, 21, 10, 19], [15, 22, 10, 20], [16, 12, 11, 10, "rotate"], [16, 18, 11, 16], [16, 20, 11, 18], [16, 26, 11, 24], [17, 12, 12, 10, "scale"], [17, 17, 12, 15], [17, 19, 12, 17], [18, 10, 13, 8], [18, 11, 13, 9], [18, 12, 13, 10], [19, 10, 14, 8, "opacity"], [19, 17, 14, 15], [19, 19, 14, 17], [20, 8, 15, 6], [20, 9, 15, 7], [21, 8, 16, 6], [21, 11, 16, 9], [21, 13, 16, 11], [22, 10, 17, 8, "transform"], [22, 19, 17, 17], [22, 21, 17, 19], [22, 22, 17, 20], [23, 12, 18, 10, "rotate"], [23, 18, 18, 16], [23, 20, 18, 18], [23, 26, 18, 24], [24, 12, 19, 10, "scale"], [24, 17, 19, 15], [24, 19, 19, 17], [25, 10, 20, 8], [25, 11, 20, 9], [25, 12, 20, 10], [26, 10, 21, 8, "opacity"], [26, 17, 21, 15], [26, 19, 21, 17], [27, 8, 22, 6], [28, 6, 23, 4], [28, 7, 23, 5], [29, 6, 24, 4, "duration"], [29, 14, 24, 12], [29, 16, 24, 14, "DEFAULT_PINWHEEL_TIME"], [30, 4, 25, 2], [30, 5, 25, 3], [31, 4, 26, 2, "PinwheelOut"], [31, 15, 26, 13], [31, 17, 26, 15], [32, 6, 27, 4, "name"], [32, 10, 27, 8], [32, 12, 27, 10], [32, 25, 27, 23], [33, 6, 28, 4, "style"], [33, 11, 28, 9], [33, 13, 28, 11], [34, 8, 29, 6], [34, 9, 29, 7], [34, 11, 29, 9], [35, 10, 30, 8, "transform"], [35, 19, 30, 17], [35, 21, 30, 19], [35, 22, 30, 20], [36, 12, 31, 10, "rotate"], [36, 18, 31, 16], [36, 20, 31, 18], [36, 26, 31, 24], [37, 12, 32, 10, "scale"], [37, 17, 32, 15], [37, 19, 32, 17], [38, 10, 33, 8], [38, 11, 33, 9], [38, 12, 33, 10], [39, 10, 34, 8, "opacity"], [39, 17, 34, 15], [39, 19, 34, 17], [40, 8, 35, 6], [40, 9, 35, 7], [41, 8, 36, 6], [41, 11, 36, 9], [41, 13, 36, 11], [42, 10, 37, 8, "transform"], [42, 19, 37, 17], [42, 21, 37, 19], [42, 22, 37, 20], [43, 12, 38, 10, "rotate"], [43, 18, 38, 16], [43, 20, 38, 18], [43, 26, 38, 24], [44, 12, 39, 10, "scale"], [44, 17, 39, 15], [44, 19, 39, 17], [45, 10, 40, 8], [45, 11, 40, 9], [45, 12, 40, 10], [46, 10, 41, 8, "opacity"], [46, 17, 41, 15], [46, 19, 41, 17], [47, 8, 42, 6], [48, 6, 43, 4], [48, 7, 43, 5], [49, 6, 44, 4, "duration"], [49, 14, 44, 12], [49, 16, 44, 14, "DEFAULT_PINWHEEL_TIME"], [50, 4, 45, 2], [51, 2, 46, 0], [51, 3, 46, 1], [52, 2, 47, 7], [52, 8, 47, 13, "Pinwheel"], [52, 16, 47, 21], [52, 19, 47, 21, "exports"], [52, 26, 47, 21], [52, 27, 47, 21, "Pinwheel"], [52, 35, 47, 21], [52, 38, 47, 24], [53, 4, 48, 2, "PinwheelIn"], [53, 14, 48, 12], [53, 16, 48, 14], [54, 6, 49, 4, "style"], [54, 11, 49, 9], [54, 13, 49, 11], [54, 17, 49, 11, "convertAnimationObjectToKeyframes"], [54, 67, 49, 44], [54, 69, 49, 45, "PinwheelData"], [54, 81, 49, 57], [54, 82, 49, 58, "PinwheelIn"], [54, 92, 49, 68], [54, 93, 49, 69], [55, 6, 50, 4, "duration"], [55, 14, 50, 12], [55, 16, 50, 14, "PinwheelData"], [55, 28, 50, 26], [55, 29, 50, 27, "PinwheelIn"], [55, 39, 50, 37], [55, 40, 50, 38, "duration"], [56, 4, 51, 2], [56, 5, 51, 3], [57, 4, 52, 2, "PinwheelOut"], [57, 15, 52, 13], [57, 17, 52, 15], [58, 6, 53, 4, "style"], [58, 11, 53, 9], [58, 13, 53, 11], [58, 17, 53, 11, "convertAnimationObjectToKeyframes"], [58, 67, 53, 44], [58, 69, 53, 45, "PinwheelData"], [58, 81, 53, 57], [58, 82, 53, 58, "PinwheelOut"], [58, 93, 53, 69], [58, 94, 53, 70], [59, 6, 54, 4, "duration"], [59, 14, 54, 12], [59, 16, 54, 14, "PinwheelData"], [59, 28, 54, 26], [59, 29, 54, 27, "PinwheelOut"], [59, 40, 54, 38], [59, 41, 54, 39, "duration"], [60, 4, 55, 2], [61, 2, 56, 0], [61, 3, 56, 1], [62, 0, 56, 2], [62, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}