{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./sweet/setUpErrorManager.fx", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 38, "index": 38}}], "key": "unNfssZPERC/bZ+6KXmNuRu4AHs=", "exportNames": ["*"]}}, {"name": "./web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 39}, "end": {"line": 2, "column": 15, "index": 54}}], "key": "8hbLzyIXFhqWXguD+C6jDKIcDJ4=", "exportNames": ["*"]}}, {"name": "./NativeModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 104}, "end": {"line": 6, "column": 57, "index": 161}}], "key": "bKrTNGrQhrueoqkHcq+VBMxaM6w=", "exportNames": ["*"]}}, {"name": "./SharedObject", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 162}, "end": {"line": 7, "column": 57, "index": 219}}], "key": "sgMIwbEZw2dBxMKPKRqpyF5kl9M=", "exportNames": ["*"]}}, {"name": "./SharedRef", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 220}, "end": {"line": 8, "column": 51, "index": 271}}], "key": "oY1WXsWtE5/1rzSKWjpFxWarDyk=", "exportNames": ["*"]}}, {"name": "./Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 273}, "end": {"line": 10, "column": 49, "index": 322}}], "key": "mJ+B86P8DN4tqVaj4OEiZcgtefg=", "exportNames": ["*"]}}, {"name": "./uuid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 323}, "end": {"line": 11, "column": 41, "index": 364}}], "key": "Q1VgbV74OaNDCDDYB3/dMtZbO1k=", "exportNames": ["*"]}}, {"name": "./EventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 366}, "end": {"line": 13, "column": 81, "index": 447}}], "key": "c7y51sdeNB+ZIpPSVQeGauhYgLk=", "exportNames": ["*"]}}, {"name": "./NativeViewManagerAdapter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 517}, "end": {"line": 15, "column": 70, "index": 587}}], "key": "/jUhtzKiTzd9NP5zUxGrLVqXrdU=", "exportNames": ["*"]}}, {"name": "./requireNativeModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 589}, "end": {"line": 17, "column": 38, "index": 627}}], "key": "6ybEgv0o6wMlGqg8lKStnnMYUaQ=", "exportNames": ["*"]}}, {"name": "./registerWebModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 628}, "end": {"line": 18, "column": 36, "index": 664}}], "key": "oL68Ur+KlpL+vQR4F/rdbtr+Tpk=", "exportNames": ["*"]}}, {"name": "./TypedArrays.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 665}, "end": {"line": 19, "column": 36, "index": 701}}], "key": "ZJTPL2zMQyO3l4FRkyLYlyLUIUA=", "exportNames": ["*"]}}, {"name": "./PermissionsInterface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 703}, "end": {"line": 21, "column": 39, "index": 742}}], "key": "ARXCX5EwdMVwiREzax8RXXOIrOI=", "exportNames": ["*"]}}, {"name": "./PermissionsHook", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 743}, "end": {"line": 22, "column": 34, "index": 777}}], "key": "9zD/6L1F7fyXMtHtbv8b6SGnwyg=", "exportNames": ["*"]}}, {"name": "./Refs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 779}, "end": {"line": 24, "column": 23, "index": 802}}], "key": "CORX0kxlJDJI//a2Q6ULdctzn3s=", "exportNames": ["*"]}}, {"name": "./hooks/useReleasingSharedObject", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 804}, "end": {"line": 26, "column": 49, "index": 853}}], "key": "1plaP6A2E/qwwuBQZbHCVoF7+hg=", "exportNames": ["*"]}}, {"name": "./reload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 854}, "end": {"line": 27, "column": 25, "index": 879}}], "key": "UGhkBeI3XngpEsrcU41ggzXZSkI=", "exportNames": ["*"]}}, {"name": "./errors/CodedError", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 891}, "end": {"line": 30, "column": 49, "index": 940}}], "key": "omv5Egy/Hqu37O5MU0fTaQbHVLQ=", "exportNames": ["*"]}}, {"name": "./errors/UnavailabilityError", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 941}, "end": {"line": 31, "column": 67, "index": 1008}}], "key": "7YGNGxinwyHSgmui8hdWKdjookg=", "exportNames": ["*"]}}, {"name": "./LegacyEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0, "index": 1024}, "end": {"line": 34, "column": 58, "index": 1082}}], "key": "6HygTJHNi9ygdZFNE3AKMK5Z9mQ=", "exportNames": ["*"]}}, {"name": "./NativeModulesProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 35, "column": 0, "index": 1083}, "end": {"line": 35, "column": 69, "index": 1152}}], "key": "W+feuhrG2kjxGmfPOxPmPEHY8Ac=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    NativeModule: true,\n    SharedObject: true,\n    SharedRef: true,\n    Platform: true,\n    uuid: true,\n    EventEmitter: true,\n    requireNativeViewManager: true,\n    CodedError: true,\n    UnavailabilityError: true,\n    LegacyEventEmitter: true,\n    NativeModulesProxy: true\n  };\n  Object.defineProperty(exports, \"CodedError\", {\n    enumerable: true,\n    get: function () {\n      return _CodedError.CodedError;\n    }\n  });\n  Object.defineProperty(exports, \"EventEmitter\", {\n    enumerable: true,\n    get: function () {\n      return _EventEmitter.default;\n    }\n  });\n  Object.defineProperty(exports, \"LegacyEventEmitter\", {\n    enumerable: true,\n    get: function () {\n      return _LegacyEventEmitter.LegacyEventEmitter;\n    }\n  });\n  Object.defineProperty(exports, \"NativeModule\", {\n    enumerable: true,\n    get: function () {\n      return _NativeModule.default;\n    }\n  });\n  Object.defineProperty(exports, \"NativeModulesProxy\", {\n    enumerable: true,\n    get: function () {\n      return _NativeModulesProxy.default;\n    }\n  });\n  Object.defineProperty(exports, \"Platform\", {\n    enumerable: true,\n    get: function () {\n      return _Platform.default;\n    }\n  });\n  Object.defineProperty(exports, \"SharedObject\", {\n    enumerable: true,\n    get: function () {\n      return _SharedObject.default;\n    }\n  });\n  Object.defineProperty(exports, \"SharedRef\", {\n    enumerable: true,\n    get: function () {\n      return _SharedRef.default;\n    }\n  });\n  Object.defineProperty(exports, \"UnavailabilityError\", {\n    enumerable: true,\n    get: function () {\n      return _UnavailabilityError.UnavailabilityError;\n    }\n  });\n  Object.defineProperty(exports, \"requireNativeViewManager\", {\n    enumerable: true,\n    get: function () {\n      return _NativeViewManagerAdapter.requireNativeViewManager;\n    }\n  });\n  Object.defineProperty(exports, \"uuid\", {\n    enumerable: true,\n    get: function () {\n      return _uuid.default;\n    }\n  });\n  require(_dependencyMap[1], \"./sweet/setUpErrorManager.fx\");\n  require(_dependencyMap[2], \"./web\");\n  var _NativeModule = _interopRequireDefault(require(_dependencyMap[3], \"./NativeModule\"));\n  var _SharedObject = _interopRequireDefault(require(_dependencyMap[4], \"./SharedObject\"));\n  var _SharedRef = _interopRequireDefault(require(_dependencyMap[5], \"./SharedRef\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[6], \"./Platform\"));\n  var _uuid = _interopRequireDefault(require(_dependencyMap[7], \"./uuid\"));\n  var _EventEmitter = _interopRequireDefault(require(_dependencyMap[8], \"./EventEmitter\"));\n  var _NativeViewManagerAdapter = require(_dependencyMap[9], \"./NativeViewManagerAdapter\");\n  var _requireNativeModule = require(_dependencyMap[10], \"./requireNativeModule\");\n  Object.keys(_requireNativeModule).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _requireNativeModule[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _requireNativeModule[key];\n      }\n    });\n  });\n  var _registerWebModule = require(_dependencyMap[11], \"./registerWebModule\");\n  Object.keys(_registerWebModule).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _registerWebModule[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _registerWebModule[key];\n      }\n    });\n  });\n  var _TypedArrays = require(_dependencyMap[12], \"./TypedArrays.types\");\n  Object.keys(_TypedArrays).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _TypedArrays[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _TypedArrays[key];\n      }\n    });\n  });\n  var _PermissionsInterface = require(_dependencyMap[13], \"./PermissionsInterface\");\n  Object.keys(_PermissionsInterface).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _PermissionsInterface[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _PermissionsInterface[key];\n      }\n    });\n  });\n  var _PermissionsHook = require(_dependencyMap[14], \"./PermissionsHook\");\n  Object.keys(_PermissionsHook).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _PermissionsHook[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _PermissionsHook[key];\n      }\n    });\n  });\n  var _Refs = require(_dependencyMap[15], \"./Refs\");\n  Object.keys(_Refs).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _Refs[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Refs[key];\n      }\n    });\n  });\n  var _useReleasingSharedObject = require(_dependencyMap[16], \"./hooks/useReleasingSharedObject\");\n  Object.keys(_useReleasingSharedObject).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _useReleasingSharedObject[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _useReleasingSharedObject[key];\n      }\n    });\n  });\n  var _reload = require(_dependencyMap[17], \"./reload\");\n  Object.keys(_reload).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _reload[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _reload[key];\n      }\n    });\n  });\n  var _CodedError = require(_dependencyMap[18], \"./errors/CodedError\");\n  var _UnavailabilityError = require(_dependencyMap[19], \"./errors/UnavailabilityError\");\n  var _LegacyEventEmitter = require(_dependencyMap[20], \"./LegacyEventEmitter\");\n  var _NativeModulesProxy = _interopRequireDefault(require(_dependencyMap[21], \"./NativeModulesProxy\"));\n});", "lineCount": 194, "map": [[85, 2, 1, 0, "require"], [85, 9, 1, 0], [85, 10, 1, 0, "_dependencyMap"], [85, 24, 1, 0], [86, 2, 2, 0, "require"], [86, 9, 2, 0], [86, 10, 2, 0, "_dependencyMap"], [86, 24, 2, 0], [87, 2, 6, 0], [87, 6, 6, 0, "_NativeModule"], [87, 19, 6, 0], [87, 22, 6, 0, "_interopRequireDefault"], [87, 44, 6, 0], [87, 45, 6, 0, "require"], [87, 52, 6, 0], [87, 53, 6, 0, "_dependencyMap"], [87, 67, 6, 0], [88, 2, 7, 0], [88, 6, 7, 0, "_SharedObject"], [88, 19, 7, 0], [88, 22, 7, 0, "_interopRequireDefault"], [88, 44, 7, 0], [88, 45, 7, 0, "require"], [88, 52, 7, 0], [88, 53, 7, 0, "_dependencyMap"], [88, 67, 7, 0], [89, 2, 8, 0], [89, 6, 8, 0, "_SharedRef"], [89, 16, 8, 0], [89, 19, 8, 0, "_interopRequireDefault"], [89, 41, 8, 0], [89, 42, 8, 0, "require"], [89, 49, 8, 0], [89, 50, 8, 0, "_dependencyMap"], [89, 64, 8, 0], [90, 2, 10, 0], [90, 6, 10, 0, "_Platform"], [90, 15, 10, 0], [90, 18, 10, 0, "_interopRequireDefault"], [90, 40, 10, 0], [90, 41, 10, 0, "require"], [90, 48, 10, 0], [90, 49, 10, 0, "_dependencyMap"], [90, 63, 10, 0], [91, 2, 11, 0], [91, 6, 11, 0, "_uuid"], [91, 11, 11, 0], [91, 14, 11, 0, "_interopRequireDefault"], [91, 36, 11, 0], [91, 37, 11, 0, "require"], [91, 44, 11, 0], [91, 45, 11, 0, "_dependencyMap"], [91, 59, 11, 0], [92, 2, 13, 0], [92, 6, 13, 0, "_EventEmitter"], [92, 19, 13, 0], [92, 22, 13, 0, "_interopRequireDefault"], [92, 44, 13, 0], [92, 45, 13, 0, "require"], [92, 52, 13, 0], [92, 53, 13, 0, "_dependencyMap"], [92, 67, 13, 0], [93, 2, 15, 0], [93, 6, 15, 0, "_NativeViewManagerAdapter"], [93, 31, 15, 0], [93, 34, 15, 0, "require"], [93, 41, 15, 0], [93, 42, 15, 0, "_dependencyMap"], [93, 56, 15, 0], [94, 2, 17, 0], [94, 6, 17, 0, "_requireNativeModule"], [94, 26, 17, 0], [94, 29, 17, 0, "require"], [94, 36, 17, 0], [94, 37, 17, 0, "_dependencyMap"], [94, 51, 17, 0], [95, 2, 17, 0, "Object"], [95, 8, 17, 0], [95, 9, 17, 0, "keys"], [95, 13, 17, 0], [95, 14, 17, 0, "_requireNativeModule"], [95, 34, 17, 0], [95, 36, 17, 0, "for<PERSON>ach"], [95, 43, 17, 0], [95, 54, 17, 0, "key"], [95, 57, 17, 0], [96, 4, 17, 0], [96, 8, 17, 0, "key"], [96, 11, 17, 0], [96, 29, 17, 0, "key"], [96, 32, 17, 0], [97, 4, 17, 0], [97, 8, 17, 0, "Object"], [97, 14, 17, 0], [97, 15, 17, 0, "prototype"], [97, 24, 17, 0], [97, 25, 17, 0, "hasOwnProperty"], [97, 39, 17, 0], [97, 40, 17, 0, "call"], [97, 44, 17, 0], [97, 45, 17, 0, "_exportNames"], [97, 57, 17, 0], [97, 59, 17, 0, "key"], [97, 62, 17, 0], [98, 4, 17, 0], [98, 8, 17, 0, "key"], [98, 11, 17, 0], [98, 15, 17, 0, "exports"], [98, 22, 17, 0], [98, 26, 17, 0, "exports"], [98, 33, 17, 0], [98, 34, 17, 0, "key"], [98, 37, 17, 0], [98, 43, 17, 0, "_requireNativeModule"], [98, 63, 17, 0], [98, 64, 17, 0, "key"], [98, 67, 17, 0], [99, 4, 17, 0, "Object"], [99, 10, 17, 0], [99, 11, 17, 0, "defineProperty"], [99, 25, 17, 0], [99, 26, 17, 0, "exports"], [99, 33, 17, 0], [99, 35, 17, 0, "key"], [99, 38, 17, 0], [100, 6, 17, 0, "enumerable"], [100, 16, 17, 0], [101, 6, 17, 0, "get"], [101, 9, 17, 0], [101, 20, 17, 0, "get"], [101, 21, 17, 0], [102, 8, 17, 0], [102, 15, 17, 0, "_requireNativeModule"], [102, 35, 17, 0], [102, 36, 17, 0, "key"], [102, 39, 17, 0], [103, 6, 17, 0], [104, 4, 17, 0], [105, 2, 17, 0], [106, 2, 18, 0], [106, 6, 18, 0, "_registerWebModule"], [106, 24, 18, 0], [106, 27, 18, 0, "require"], [106, 34, 18, 0], [106, 35, 18, 0, "_dependencyMap"], [106, 49, 18, 0], [107, 2, 18, 0, "Object"], [107, 8, 18, 0], [107, 9, 18, 0, "keys"], [107, 13, 18, 0], [107, 14, 18, 0, "_registerWebModule"], [107, 32, 18, 0], [107, 34, 18, 0, "for<PERSON>ach"], [107, 41, 18, 0], [107, 52, 18, 0, "key"], [107, 55, 18, 0], [108, 4, 18, 0], [108, 8, 18, 0, "key"], [108, 11, 18, 0], [108, 29, 18, 0, "key"], [108, 32, 18, 0], [109, 4, 18, 0], [109, 8, 18, 0, "Object"], [109, 14, 18, 0], [109, 15, 18, 0, "prototype"], [109, 24, 18, 0], [109, 25, 18, 0, "hasOwnProperty"], [109, 39, 18, 0], [109, 40, 18, 0, "call"], [109, 44, 18, 0], [109, 45, 18, 0, "_exportNames"], [109, 57, 18, 0], [109, 59, 18, 0, "key"], [109, 62, 18, 0], [110, 4, 18, 0], [110, 8, 18, 0, "key"], [110, 11, 18, 0], [110, 15, 18, 0, "exports"], [110, 22, 18, 0], [110, 26, 18, 0, "exports"], [110, 33, 18, 0], [110, 34, 18, 0, "key"], [110, 37, 18, 0], [110, 43, 18, 0, "_registerWebModule"], [110, 61, 18, 0], [110, 62, 18, 0, "key"], [110, 65, 18, 0], [111, 4, 18, 0, "Object"], [111, 10, 18, 0], [111, 11, 18, 0, "defineProperty"], [111, 25, 18, 0], [111, 26, 18, 0, "exports"], [111, 33, 18, 0], [111, 35, 18, 0, "key"], [111, 38, 18, 0], [112, 6, 18, 0, "enumerable"], [112, 16, 18, 0], [113, 6, 18, 0, "get"], [113, 9, 18, 0], [113, 20, 18, 0, "get"], [113, 21, 18, 0], [114, 8, 18, 0], [114, 15, 18, 0, "_registerWebModule"], [114, 33, 18, 0], [114, 34, 18, 0, "key"], [114, 37, 18, 0], [115, 6, 18, 0], [116, 4, 18, 0], [117, 2, 18, 0], [118, 2, 19, 0], [118, 6, 19, 0, "_TypedArrays"], [118, 18, 19, 0], [118, 21, 19, 0, "require"], [118, 28, 19, 0], [118, 29, 19, 0, "_dependencyMap"], [118, 43, 19, 0], [119, 2, 19, 0, "Object"], [119, 8, 19, 0], [119, 9, 19, 0, "keys"], [119, 13, 19, 0], [119, 14, 19, 0, "_TypedArrays"], [119, 26, 19, 0], [119, 28, 19, 0, "for<PERSON>ach"], [119, 35, 19, 0], [119, 46, 19, 0, "key"], [119, 49, 19, 0], [120, 4, 19, 0], [120, 8, 19, 0, "key"], [120, 11, 19, 0], [120, 29, 19, 0, "key"], [120, 32, 19, 0], [121, 4, 19, 0], [121, 8, 19, 0, "Object"], [121, 14, 19, 0], [121, 15, 19, 0, "prototype"], [121, 24, 19, 0], [121, 25, 19, 0, "hasOwnProperty"], [121, 39, 19, 0], [121, 40, 19, 0, "call"], [121, 44, 19, 0], [121, 45, 19, 0, "_exportNames"], [121, 57, 19, 0], [121, 59, 19, 0, "key"], [121, 62, 19, 0], [122, 4, 19, 0], [122, 8, 19, 0, "key"], [122, 11, 19, 0], [122, 15, 19, 0, "exports"], [122, 22, 19, 0], [122, 26, 19, 0, "exports"], [122, 33, 19, 0], [122, 34, 19, 0, "key"], [122, 37, 19, 0], [122, 43, 19, 0, "_TypedArrays"], [122, 55, 19, 0], [122, 56, 19, 0, "key"], [122, 59, 19, 0], [123, 4, 19, 0, "Object"], [123, 10, 19, 0], [123, 11, 19, 0, "defineProperty"], [123, 25, 19, 0], [123, 26, 19, 0, "exports"], [123, 33, 19, 0], [123, 35, 19, 0, "key"], [123, 38, 19, 0], [124, 6, 19, 0, "enumerable"], [124, 16, 19, 0], [125, 6, 19, 0, "get"], [125, 9, 19, 0], [125, 20, 19, 0, "get"], [125, 21, 19, 0], [126, 8, 19, 0], [126, 15, 19, 0, "_TypedArrays"], [126, 27, 19, 0], [126, 28, 19, 0, "key"], [126, 31, 19, 0], [127, 6, 19, 0], [128, 4, 19, 0], [129, 2, 19, 0], [130, 2, 21, 0], [130, 6, 21, 0, "_PermissionsInterface"], [130, 27, 21, 0], [130, 30, 21, 0, "require"], [130, 37, 21, 0], [130, 38, 21, 0, "_dependencyMap"], [130, 52, 21, 0], [131, 2, 21, 0, "Object"], [131, 8, 21, 0], [131, 9, 21, 0, "keys"], [131, 13, 21, 0], [131, 14, 21, 0, "_PermissionsInterface"], [131, 35, 21, 0], [131, 37, 21, 0, "for<PERSON>ach"], [131, 44, 21, 0], [131, 55, 21, 0, "key"], [131, 58, 21, 0], [132, 4, 21, 0], [132, 8, 21, 0, "key"], [132, 11, 21, 0], [132, 29, 21, 0, "key"], [132, 32, 21, 0], [133, 4, 21, 0], [133, 8, 21, 0, "Object"], [133, 14, 21, 0], [133, 15, 21, 0, "prototype"], [133, 24, 21, 0], [133, 25, 21, 0, "hasOwnProperty"], [133, 39, 21, 0], [133, 40, 21, 0, "call"], [133, 44, 21, 0], [133, 45, 21, 0, "_exportNames"], [133, 57, 21, 0], [133, 59, 21, 0, "key"], [133, 62, 21, 0], [134, 4, 21, 0], [134, 8, 21, 0, "key"], [134, 11, 21, 0], [134, 15, 21, 0, "exports"], [134, 22, 21, 0], [134, 26, 21, 0, "exports"], [134, 33, 21, 0], [134, 34, 21, 0, "key"], [134, 37, 21, 0], [134, 43, 21, 0, "_PermissionsInterface"], [134, 64, 21, 0], [134, 65, 21, 0, "key"], [134, 68, 21, 0], [135, 4, 21, 0, "Object"], [135, 10, 21, 0], [135, 11, 21, 0, "defineProperty"], [135, 25, 21, 0], [135, 26, 21, 0, "exports"], [135, 33, 21, 0], [135, 35, 21, 0, "key"], [135, 38, 21, 0], [136, 6, 21, 0, "enumerable"], [136, 16, 21, 0], [137, 6, 21, 0, "get"], [137, 9, 21, 0], [137, 20, 21, 0, "get"], [137, 21, 21, 0], [138, 8, 21, 0], [138, 15, 21, 0, "_PermissionsInterface"], [138, 36, 21, 0], [138, 37, 21, 0, "key"], [138, 40, 21, 0], [139, 6, 21, 0], [140, 4, 21, 0], [141, 2, 21, 0], [142, 2, 22, 0], [142, 6, 22, 0, "_PermissionsHook"], [142, 22, 22, 0], [142, 25, 22, 0, "require"], [142, 32, 22, 0], [142, 33, 22, 0, "_dependencyMap"], [142, 47, 22, 0], [143, 2, 22, 0, "Object"], [143, 8, 22, 0], [143, 9, 22, 0, "keys"], [143, 13, 22, 0], [143, 14, 22, 0, "_PermissionsHook"], [143, 30, 22, 0], [143, 32, 22, 0, "for<PERSON>ach"], [143, 39, 22, 0], [143, 50, 22, 0, "key"], [143, 53, 22, 0], [144, 4, 22, 0], [144, 8, 22, 0, "key"], [144, 11, 22, 0], [144, 29, 22, 0, "key"], [144, 32, 22, 0], [145, 4, 22, 0], [145, 8, 22, 0, "Object"], [145, 14, 22, 0], [145, 15, 22, 0, "prototype"], [145, 24, 22, 0], [145, 25, 22, 0, "hasOwnProperty"], [145, 39, 22, 0], [145, 40, 22, 0, "call"], [145, 44, 22, 0], [145, 45, 22, 0, "_exportNames"], [145, 57, 22, 0], [145, 59, 22, 0, "key"], [145, 62, 22, 0], [146, 4, 22, 0], [146, 8, 22, 0, "key"], [146, 11, 22, 0], [146, 15, 22, 0, "exports"], [146, 22, 22, 0], [146, 26, 22, 0, "exports"], [146, 33, 22, 0], [146, 34, 22, 0, "key"], [146, 37, 22, 0], [146, 43, 22, 0, "_PermissionsHook"], [146, 59, 22, 0], [146, 60, 22, 0, "key"], [146, 63, 22, 0], [147, 4, 22, 0, "Object"], [147, 10, 22, 0], [147, 11, 22, 0, "defineProperty"], [147, 25, 22, 0], [147, 26, 22, 0, "exports"], [147, 33, 22, 0], [147, 35, 22, 0, "key"], [147, 38, 22, 0], [148, 6, 22, 0, "enumerable"], [148, 16, 22, 0], [149, 6, 22, 0, "get"], [149, 9, 22, 0], [149, 20, 22, 0, "get"], [149, 21, 22, 0], [150, 8, 22, 0], [150, 15, 22, 0, "_PermissionsHook"], [150, 31, 22, 0], [150, 32, 22, 0, "key"], [150, 35, 22, 0], [151, 6, 22, 0], [152, 4, 22, 0], [153, 2, 22, 0], [154, 2, 24, 0], [154, 6, 24, 0, "_Refs"], [154, 11, 24, 0], [154, 14, 24, 0, "require"], [154, 21, 24, 0], [154, 22, 24, 0, "_dependencyMap"], [154, 36, 24, 0], [155, 2, 24, 0, "Object"], [155, 8, 24, 0], [155, 9, 24, 0, "keys"], [155, 13, 24, 0], [155, 14, 24, 0, "_Refs"], [155, 19, 24, 0], [155, 21, 24, 0, "for<PERSON>ach"], [155, 28, 24, 0], [155, 39, 24, 0, "key"], [155, 42, 24, 0], [156, 4, 24, 0], [156, 8, 24, 0, "key"], [156, 11, 24, 0], [156, 29, 24, 0, "key"], [156, 32, 24, 0], [157, 4, 24, 0], [157, 8, 24, 0, "Object"], [157, 14, 24, 0], [157, 15, 24, 0, "prototype"], [157, 24, 24, 0], [157, 25, 24, 0, "hasOwnProperty"], [157, 39, 24, 0], [157, 40, 24, 0, "call"], [157, 44, 24, 0], [157, 45, 24, 0, "_exportNames"], [157, 57, 24, 0], [157, 59, 24, 0, "key"], [157, 62, 24, 0], [158, 4, 24, 0], [158, 8, 24, 0, "key"], [158, 11, 24, 0], [158, 15, 24, 0, "exports"], [158, 22, 24, 0], [158, 26, 24, 0, "exports"], [158, 33, 24, 0], [158, 34, 24, 0, "key"], [158, 37, 24, 0], [158, 43, 24, 0, "_Refs"], [158, 48, 24, 0], [158, 49, 24, 0, "key"], [158, 52, 24, 0], [159, 4, 24, 0, "Object"], [159, 10, 24, 0], [159, 11, 24, 0, "defineProperty"], [159, 25, 24, 0], [159, 26, 24, 0, "exports"], [159, 33, 24, 0], [159, 35, 24, 0, "key"], [159, 38, 24, 0], [160, 6, 24, 0, "enumerable"], [160, 16, 24, 0], [161, 6, 24, 0, "get"], [161, 9, 24, 0], [161, 20, 24, 0, "get"], [161, 21, 24, 0], [162, 8, 24, 0], [162, 15, 24, 0, "_Refs"], [162, 20, 24, 0], [162, 21, 24, 0, "key"], [162, 24, 24, 0], [163, 6, 24, 0], [164, 4, 24, 0], [165, 2, 24, 0], [166, 2, 26, 0], [166, 6, 26, 0, "_useReleasingSharedObject"], [166, 31, 26, 0], [166, 34, 26, 0, "require"], [166, 41, 26, 0], [166, 42, 26, 0, "_dependencyMap"], [166, 56, 26, 0], [167, 2, 26, 0, "Object"], [167, 8, 26, 0], [167, 9, 26, 0, "keys"], [167, 13, 26, 0], [167, 14, 26, 0, "_useReleasingSharedObject"], [167, 39, 26, 0], [167, 41, 26, 0, "for<PERSON>ach"], [167, 48, 26, 0], [167, 59, 26, 0, "key"], [167, 62, 26, 0], [168, 4, 26, 0], [168, 8, 26, 0, "key"], [168, 11, 26, 0], [168, 29, 26, 0, "key"], [168, 32, 26, 0], [169, 4, 26, 0], [169, 8, 26, 0, "Object"], [169, 14, 26, 0], [169, 15, 26, 0, "prototype"], [169, 24, 26, 0], [169, 25, 26, 0, "hasOwnProperty"], [169, 39, 26, 0], [169, 40, 26, 0, "call"], [169, 44, 26, 0], [169, 45, 26, 0, "_exportNames"], [169, 57, 26, 0], [169, 59, 26, 0, "key"], [169, 62, 26, 0], [170, 4, 26, 0], [170, 8, 26, 0, "key"], [170, 11, 26, 0], [170, 15, 26, 0, "exports"], [170, 22, 26, 0], [170, 26, 26, 0, "exports"], [170, 33, 26, 0], [170, 34, 26, 0, "key"], [170, 37, 26, 0], [170, 43, 26, 0, "_useReleasingSharedObject"], [170, 68, 26, 0], [170, 69, 26, 0, "key"], [170, 72, 26, 0], [171, 4, 26, 0, "Object"], [171, 10, 26, 0], [171, 11, 26, 0, "defineProperty"], [171, 25, 26, 0], [171, 26, 26, 0, "exports"], [171, 33, 26, 0], [171, 35, 26, 0, "key"], [171, 38, 26, 0], [172, 6, 26, 0, "enumerable"], [172, 16, 26, 0], [173, 6, 26, 0, "get"], [173, 9, 26, 0], [173, 20, 26, 0, "get"], [173, 21, 26, 0], [174, 8, 26, 0], [174, 15, 26, 0, "_useReleasingSharedObject"], [174, 40, 26, 0], [174, 41, 26, 0, "key"], [174, 44, 26, 0], [175, 6, 26, 0], [176, 4, 26, 0], [177, 2, 26, 0], [178, 2, 27, 0], [178, 6, 27, 0, "_reload"], [178, 13, 27, 0], [178, 16, 27, 0, "require"], [178, 23, 27, 0], [178, 24, 27, 0, "_dependencyMap"], [178, 38, 27, 0], [179, 2, 27, 0, "Object"], [179, 8, 27, 0], [179, 9, 27, 0, "keys"], [179, 13, 27, 0], [179, 14, 27, 0, "_reload"], [179, 21, 27, 0], [179, 23, 27, 0, "for<PERSON>ach"], [179, 30, 27, 0], [179, 41, 27, 0, "key"], [179, 44, 27, 0], [180, 4, 27, 0], [180, 8, 27, 0, "key"], [180, 11, 27, 0], [180, 29, 27, 0, "key"], [180, 32, 27, 0], [181, 4, 27, 0], [181, 8, 27, 0, "Object"], [181, 14, 27, 0], [181, 15, 27, 0, "prototype"], [181, 24, 27, 0], [181, 25, 27, 0, "hasOwnProperty"], [181, 39, 27, 0], [181, 40, 27, 0, "call"], [181, 44, 27, 0], [181, 45, 27, 0, "_exportNames"], [181, 57, 27, 0], [181, 59, 27, 0, "key"], [181, 62, 27, 0], [182, 4, 27, 0], [182, 8, 27, 0, "key"], [182, 11, 27, 0], [182, 15, 27, 0, "exports"], [182, 22, 27, 0], [182, 26, 27, 0, "exports"], [182, 33, 27, 0], [182, 34, 27, 0, "key"], [182, 37, 27, 0], [182, 43, 27, 0, "_reload"], [182, 50, 27, 0], [182, 51, 27, 0, "key"], [182, 54, 27, 0], [183, 4, 27, 0, "Object"], [183, 10, 27, 0], [183, 11, 27, 0, "defineProperty"], [183, 25, 27, 0], [183, 26, 27, 0, "exports"], [183, 33, 27, 0], [183, 35, 27, 0, "key"], [183, 38, 27, 0], [184, 6, 27, 0, "enumerable"], [184, 16, 27, 0], [185, 6, 27, 0, "get"], [185, 9, 27, 0], [185, 20, 27, 0, "get"], [185, 21, 27, 0], [186, 8, 27, 0], [186, 15, 27, 0, "_reload"], [186, 22, 27, 0], [186, 23, 27, 0, "key"], [186, 26, 27, 0], [187, 6, 27, 0], [188, 4, 27, 0], [189, 2, 27, 0], [190, 2, 30, 0], [190, 6, 30, 0, "_CodedError"], [190, 17, 30, 0], [190, 20, 30, 0, "require"], [190, 27, 30, 0], [190, 28, 30, 0, "_dependencyMap"], [190, 42, 30, 0], [191, 2, 31, 0], [191, 6, 31, 0, "_UnavailabilityError"], [191, 26, 31, 0], [191, 29, 31, 0, "require"], [191, 36, 31, 0], [191, 37, 31, 0, "_dependencyMap"], [191, 51, 31, 0], [192, 2, 34, 0], [192, 6, 34, 0, "_LegacyEventEmitter"], [192, 25, 34, 0], [192, 28, 34, 0, "require"], [192, 35, 34, 0], [192, 36, 34, 0, "_dependencyMap"], [192, 50, 34, 0], [193, 2, 35, 0], [193, 6, 35, 0, "_NativeModulesProxy"], [193, 25, 35, 0], [193, 28, 35, 0, "_interopRequireDefault"], [193, 50, 35, 0], [193, 51, 35, 0, "require"], [193, 58, 35, 0], [193, 59, 35, 0, "_dependencyMap"], [193, 73, 35, 0], [194, 0, 35, 69], [194, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}