{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const MousePointerClick = exports.default = (0, _createLucideIcon.default)(\"MousePointerClick\", [[\"path\", {\n    d: \"M14 4.1 12 6\",\n    key: \"ita8i4\"\n  }], [\"path\", {\n    d: \"m5.1 8-2.9-.8\",\n    key: \"1go3kf\"\n  }], [\"path\", {\n    d: \"m6 12-1.9 2\",\n    key: \"mnht97\"\n  }], [\"path\", {\n    d: \"M7.2 2.2 8 5.1\",\n    key: \"1cfko1\"\n  }], [\"path\", {\n    d: \"M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z\",\n    key: \"s0h3yz\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "MousePointerClick"], [15, 25, 10, 23], [15, 28, 10, 23, "exports"], [15, 35, 10, 23], [15, 36, 10, 23, "default"], [15, 43, 10, 23], [15, 46, 10, 26], [15, 50, 10, 26, "createLucideIcon"], [15, 75, 10, 42], [15, 77, 10, 43], [15, 96, 10, 62], [15, 98, 10, 64], [15, 99, 11, 2], [15, 100, 11, 3], [15, 106, 11, 9], [15, 108, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 21, 11, 30], [17, 4, 11, 32, "key"], [17, 7, 11, 35], [17, 9, 11, 37], [18, 2, 11, 46], [18, 3, 11, 47], [18, 4, 11, 48], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 22, 12, 31], [20, 4, 12, 33, "key"], [20, 7, 12, 36], [20, 9, 12, 38], [21, 2, 12, 47], [21, 3, 12, 48], [21, 4, 12, 49], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 20, 13, 29], [23, 4, 13, 31, "key"], [23, 7, 13, 34], [23, 9, 13, 36], [24, 2, 13, 45], [24, 3, 13, 46], [24, 4, 13, 47], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 23, 14, 32], [26, 4, 14, 34, "key"], [26, 7, 14, 37], [26, 9, 14, 39], [27, 2, 14, 48], [27, 3, 14, 49], [27, 4, 14, 50], [27, 6, 15, 2], [27, 7, 16, 4], [27, 13, 16, 10], [27, 15, 17, 4], [28, 4, 18, 6, "d"], [28, 5, 18, 7], [28, 7, 18, 9], [28, 134, 18, 136], [29, 4, 19, 6, "key"], [29, 7, 19, 9], [29, 9, 19, 11], [30, 2, 20, 4], [30, 3, 20, 5], [30, 4, 21, 3], [30, 5, 22, 1], [30, 6, 22, 2], [31, 0, 22, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}