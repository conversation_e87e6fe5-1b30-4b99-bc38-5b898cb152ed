{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 31, "index": 99}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 141}, "end": {"line": 6, "column": 48, "index": 189}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Background = Background;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Animated\"));\n  var _jsxRuntime = require(_dependencyMap[4], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function Background({\n    style,\n    ...rest\n  }) {\n    const {\n      colors\n    } = (0, _native.useTheme)();\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.View, {\n      ...rest,\n      style: [{\n        flex: 1,\n        backgroundColor: colors.background\n      }, style]\n    });\n  }\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Background"], [8, 20, 1, 13], [8, 23, 1, 13, "Background"], [8, 33, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "React"], [10, 11, 4, 0], [10, 14, 4, 0, "_interopRequireWildcard"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 4, 31], [11, 6, 4, 31, "_Animated"], [11, 15, 4, 31], [11, 18, 4, 31, "_interopRequireDefault"], [11, 40, 4, 31], [11, 41, 4, 31, "require"], [11, 48, 4, 31], [11, 49, 4, 31, "_dependencyMap"], [11, 63, 4, 31], [12, 2, 6, 0], [12, 6, 6, 0, "_jsxRuntime"], [12, 17, 6, 0], [12, 20, 6, 0, "require"], [12, 27, 6, 0], [12, 28, 6, 0, "_dependencyMap"], [12, 42, 6, 0], [13, 2, 6, 48], [13, 11, 6, 48, "_interopRequireWildcard"], [13, 35, 6, 48, "e"], [13, 36, 6, 48], [13, 38, 6, 48, "t"], [13, 39, 6, 48], [13, 68, 6, 48, "WeakMap"], [13, 75, 6, 48], [13, 81, 6, 48, "r"], [13, 82, 6, 48], [13, 89, 6, 48, "WeakMap"], [13, 96, 6, 48], [13, 100, 6, 48, "n"], [13, 101, 6, 48], [13, 108, 6, 48, "WeakMap"], [13, 115, 6, 48], [13, 127, 6, 48, "_interopRequireWildcard"], [13, 150, 6, 48], [13, 162, 6, 48, "_interopRequireWildcard"], [13, 163, 6, 48, "e"], [13, 164, 6, 48], [13, 166, 6, 48, "t"], [13, 167, 6, 48], [13, 176, 6, 48, "t"], [13, 177, 6, 48], [13, 181, 6, 48, "e"], [13, 182, 6, 48], [13, 186, 6, 48, "e"], [13, 187, 6, 48], [13, 188, 6, 48, "__esModule"], [13, 198, 6, 48], [13, 207, 6, 48, "e"], [13, 208, 6, 48], [13, 214, 6, 48, "o"], [13, 215, 6, 48], [13, 217, 6, 48, "i"], [13, 218, 6, 48], [13, 220, 6, 48, "f"], [13, 221, 6, 48], [13, 226, 6, 48, "__proto__"], [13, 235, 6, 48], [13, 243, 6, 48, "default"], [13, 250, 6, 48], [13, 252, 6, 48, "e"], [13, 253, 6, 48], [13, 270, 6, 48, "e"], [13, 271, 6, 48], [13, 294, 6, 48, "e"], [13, 295, 6, 48], [13, 320, 6, 48, "e"], [13, 321, 6, 48], [13, 330, 6, 48, "f"], [13, 331, 6, 48], [13, 337, 6, 48, "o"], [13, 338, 6, 48], [13, 341, 6, 48, "t"], [13, 342, 6, 48], [13, 345, 6, 48, "n"], [13, 346, 6, 48], [13, 349, 6, 48, "r"], [13, 350, 6, 48], [13, 358, 6, 48, "o"], [13, 359, 6, 48], [13, 360, 6, 48, "has"], [13, 363, 6, 48], [13, 364, 6, 48, "e"], [13, 365, 6, 48], [13, 375, 6, 48, "o"], [13, 376, 6, 48], [13, 377, 6, 48, "get"], [13, 380, 6, 48], [13, 381, 6, 48, "e"], [13, 382, 6, 48], [13, 385, 6, 48, "o"], [13, 386, 6, 48], [13, 387, 6, 48, "set"], [13, 390, 6, 48], [13, 391, 6, 48, "e"], [13, 392, 6, 48], [13, 394, 6, 48, "f"], [13, 395, 6, 48], [13, 411, 6, 48, "t"], [13, 412, 6, 48], [13, 416, 6, 48, "e"], [13, 417, 6, 48], [13, 433, 6, 48, "t"], [13, 434, 6, 48], [13, 441, 6, 48, "hasOwnProperty"], [13, 455, 6, 48], [13, 456, 6, 48, "call"], [13, 460, 6, 48], [13, 461, 6, 48, "e"], [13, 462, 6, 48], [13, 464, 6, 48, "t"], [13, 465, 6, 48], [13, 472, 6, 48, "i"], [13, 473, 6, 48], [13, 477, 6, 48, "o"], [13, 478, 6, 48], [13, 481, 6, 48, "Object"], [13, 487, 6, 48], [13, 488, 6, 48, "defineProperty"], [13, 502, 6, 48], [13, 507, 6, 48, "Object"], [13, 513, 6, 48], [13, 514, 6, 48, "getOwnPropertyDescriptor"], [13, 538, 6, 48], [13, 539, 6, 48, "e"], [13, 540, 6, 48], [13, 542, 6, 48, "t"], [13, 543, 6, 48], [13, 550, 6, 48, "i"], [13, 551, 6, 48], [13, 552, 6, 48, "get"], [13, 555, 6, 48], [13, 559, 6, 48, "i"], [13, 560, 6, 48], [13, 561, 6, 48, "set"], [13, 564, 6, 48], [13, 568, 6, 48, "o"], [13, 569, 6, 48], [13, 570, 6, 48, "f"], [13, 571, 6, 48], [13, 573, 6, 48, "t"], [13, 574, 6, 48], [13, 576, 6, 48, "i"], [13, 577, 6, 48], [13, 581, 6, 48, "f"], [13, 582, 6, 48], [13, 583, 6, 48, "t"], [13, 584, 6, 48], [13, 588, 6, 48, "e"], [13, 589, 6, 48], [13, 590, 6, 48, "t"], [13, 591, 6, 48], [13, 602, 6, 48, "f"], [13, 603, 6, 48], [13, 608, 6, 48, "e"], [13, 609, 6, 48], [13, 611, 6, 48, "t"], [13, 612, 6, 48], [14, 2, 7, 7], [14, 11, 7, 16, "Background"], [14, 21, 7, 26, "Background"], [14, 22, 7, 27], [15, 4, 8, 2, "style"], [15, 9, 8, 7], [16, 4, 9, 2], [16, 7, 9, 5, "rest"], [17, 2, 10, 0], [17, 3, 10, 1], [17, 5, 10, 3], [18, 4, 11, 2], [18, 10, 11, 8], [19, 6, 12, 4, "colors"], [20, 4, 13, 2], [20, 5, 13, 3], [20, 8, 13, 6], [20, 12, 13, 6, "useTheme"], [20, 28, 13, 14], [20, 30, 13, 15], [20, 31, 13, 16], [21, 4, 14, 2], [21, 11, 14, 9], [21, 24, 14, 22], [21, 28, 14, 22, "_jsx"], [21, 43, 14, 26], [21, 45, 14, 27, "Animated"], [21, 62, 14, 35], [21, 63, 14, 36, "View"], [21, 67, 14, 40], [21, 69, 14, 42], [22, 6, 15, 4], [22, 9, 15, 7, "rest"], [22, 13, 15, 11], [23, 6, 16, 4, "style"], [23, 11, 16, 9], [23, 13, 16, 11], [23, 14, 16, 12], [24, 8, 17, 6, "flex"], [24, 12, 17, 10], [24, 14, 17, 12], [24, 15, 17, 13], [25, 8, 18, 6, "backgroundColor"], [25, 23, 18, 21], [25, 25, 18, 23, "colors"], [25, 31, 18, 29], [25, 32, 18, 30, "background"], [26, 6, 19, 4], [26, 7, 19, 5], [26, 9, 19, 7, "style"], [26, 14, 19, 12], [27, 4, 20, 2], [27, 5, 20, 3], [27, 6, 20, 4], [28, 2, 21, 0], [29, 0, 21, 1], [29, 3]], "functionMap": {"names": ["<global>", "Background"], "mappings": "AAA;OCM;CDc"}}, "type": "js/module"}]}