{"dependencies": [{"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 49, "index": 64}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "../updateProps/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 65}, "end": {"line": 4, "column": 54, "index": 119}}], "key": "qbpfy52wS90DxIiWiB1QiooQv/k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.applyStyleForBelowTopScreen = exports.applyStyle = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../PlatformChecker.js\");\n  var _index = require(_dependencyMap[1], \"../updateProps/index.js\");\n  const IS_FABRIC = (0, _PlatformChecker.isFabric)();\n  const _worklet_14626504491388_init_data = {\n    code: \"function createViewDescriptorPaper_reactNativeReanimated_styleUpdaterJs1(screenId){return{tag:screenId,name:'RCTView'};}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createViewDescriptorPaper_reactNativeReanimated_styleUpdaterJs1\\\",\\\"screenId\\\",\\\"tag\\\",\\\"name\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\\\"],\\\"mappings\\\":\\\"AAKA,SAAAA,+DAA6CA,CAAAC,QAAA,EAG3C,MAAO,CACLC,GAAG,CAAED,QAAQ,CACbE,IAAI,CAAE,SACR,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createViewDescriptorPaper = function () {\n    const _e = [new global.Error(), 1, -27];\n    const createViewDescriptorPaper = function (screenId) {\n      return {\n        tag: screenId,\n        name: 'RCTView'\n      };\n    };\n    createViewDescriptorPaper.__closure = {};\n    createViewDescriptorPaper.__workletHash = 14626504491388;\n    createViewDescriptorPaper.__initData = _worklet_14626504491388_init_data;\n    createViewDescriptorPaper.__stackDetails = _e;\n    return createViewDescriptorPaper;\n  }();\n  const _worklet_13488996496906_init_data = {\n    code: \"function createViewDescriptorFabric_reactNativeReanimated_styleUpdaterJs2(screenId){return{shadowNodeWrapper:screenId};}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createViewDescriptorFabric_reactNativeReanimated_styleUpdaterJs2\\\",\\\"screenId\\\",\\\"shadowNodeWrapper\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\\\"],\\\"mappings\\\":\\\"AAaA,SAAAA,gEAA8CA,CAAAC,QAAA,EAG5C,MAAO,CACLC,iBAAiB,CAAED,QACrB,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createViewDescriptorFabric = function () {\n    const _e = [new global.Error(), 1, -27];\n    const createViewDescriptorFabric = function (screenId) {\n      return {\n        shadowNodeWrapper: screenId\n      };\n    };\n    createViewDescriptorFabric.__closure = {};\n    createViewDescriptorFabric.__workletHash = 13488996496906;\n    createViewDescriptorFabric.__initData = _worklet_13488996496906_init_data;\n    createViewDescriptorFabric.__stackDetails = _e;\n    return createViewDescriptorFabric;\n  }();\n  const createViewDescriptor = IS_FABRIC ? createViewDescriptorFabric : createViewDescriptorPaper;\n  const _worklet_15431420270588_init_data = {\n    code: \"function applyStyleForTopScreen_reactNativeReanimated_styleUpdaterJs3(screenTransitionConfig,event){const{createViewDescriptor,updateProps}=this.__closure;const{screenDimensions:screenDimensions,topScreenId:topScreenId,screenTransition:screenTransition}=screenTransitionConfig;const{topScreenStyle:computeTopScreenStyle}=screenTransition;const topScreenStyle=computeTopScreenStyle(event,screenDimensions);const topScreenDescriptor={value:[createViewDescriptor(topScreenId)]};updateProps(topScreenDescriptor,topScreenStyle,undefined);}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyStyleForTopScreen_reactNativeReanimated_styleUpdaterJs3\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"createViewDescriptor\\\",\\\"updateProps\\\",\\\"__closure\\\",\\\"screenDimensions\\\",\\\"topScreenId\\\",\\\"screenTransition\\\",\\\"topScreenStyle\\\",\\\"computeTopScreenStyle\\\",\\\"topScreenDescriptor\\\",\\\"value\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\\\"],\\\"mappings\\\":\\\"AAqBA,SAAAA,4DAA+DA,CAAAC,sBAAA,CAAAC,KAAA,QAAAC,oBAAA,CAAAC,WAAA,OAAAC,SAAA,CAG7D,KAAM,CACJC,gBAAgB,CAAhBA,gBAAgB,CAChBC,WAAW,CAAXA,WAAW,CACXC,gBAAA,CAAAA,gBACF,CAAC,CAAGP,sBAAsB,CAC1B,KAAM,CACJQ,cAAc,CAAEC,qBAClB,CAAC,CAAGF,gBAAgB,CACpB,KAAM,CAAAC,cAAc,CAAGC,qBAAqB,CAACR,KAAK,CAAEI,gBAAgB,CAAC,CACrE,KAAM,CAAAK,mBAAmB,CAAG,CAC1BC,KAAK,CAAE,CAACT,oBAAoB,CAACI,WAAW,CAAC,CAC3C,CAAC,CACDH,WAAW,CAACO,mBAAmB,CAAEF,cAAc,CAAEI,SAAS,CAAC,CAC7D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const applyStyleForTopScreen = function () {\n    const _e = [new global.Error(), -3, -27];\n    const applyStyleForTopScreen = function (screenTransitionConfig, event) {\n      const {\n        screenDimensions,\n        topScreenId,\n        screenTransition\n      } = screenTransitionConfig;\n      const {\n        topScreenStyle: computeTopScreenStyle\n      } = screenTransition;\n      const topScreenStyle = computeTopScreenStyle(event, screenDimensions);\n      const topScreenDescriptor = {\n        value: [createViewDescriptor(topScreenId)]\n      };\n      (0, _index.updateProps)(topScreenDescriptor, topScreenStyle, undefined);\n    };\n    applyStyleForTopScreen.__closure = {\n      createViewDescriptor,\n      updateProps: _index.updateProps\n    };\n    applyStyleForTopScreen.__workletHash = 15431420270588;\n    applyStyleForTopScreen.__initData = _worklet_15431420270588_init_data;\n    applyStyleForTopScreen.__stackDetails = _e;\n    return applyStyleForTopScreen;\n  }();\n  const _worklet_2747176653608_init_data = {\n    code: \"function applyStyleForBelowTopScreen_reactNativeReanimated_styleUpdaterJs4(screenTransitionConfig,event){const{createViewDescriptor,updateProps}=this.__closure;const{screenDimensions:screenDimensions,belowTopScreenId:belowTopScreenId,screenTransition:screenTransition}=screenTransitionConfig;const{belowTopScreenStyle:computeBelowTopScreenStyle}=screenTransition;const belowTopScreenStyle=computeBelowTopScreenStyle(event,screenDimensions);const belowTopScreenDescriptor={value:[createViewDescriptor(belowTopScreenId)]};updateProps(belowTopScreenDescriptor,belowTopScreenStyle,undefined);}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyStyleForBelowTopScreen_reactNativeReanimated_styleUpdaterJs4\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"createViewDescriptor\\\",\\\"updateProps\\\",\\\"__closure\\\",\\\"screenDimensions\\\",\\\"belowTopScreenId\\\",\\\"screenTransition\\\",\\\"belowTopScreenStyle\\\",\\\"computeBelowTopScreenStyle\\\",\\\"belowTopScreenDescriptor\\\",\\\"value\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\\\"],\\\"mappings\\\":\\\"AAsCO,SAAAA,iEAAoEA,CAAAC,sBAAA,CAAAC,KAAA,QAAAC,oBAAA,CAAAC,WAAA,OAAAC,SAAA,CAGzE,KAAM,CACJC,gBAAgB,CAAhBA,gBAAgB,CAChBC,gBAAgB,CAAhBA,gBAAgB,CAChBC,gBAAA,CAAAA,gBACF,CAAC,CAAGP,sBAAsB,CAC1B,KAAM,CACJQ,mBAAmB,CAAEC,0BACvB,CAAC,CAAGF,gBAAgB,CACpB,KAAM,CAAAC,mBAAmB,CAAGC,0BAA0B,CAACR,KAAK,CAAEI,gBAAgB,CAAC,CAC/E,KAAM,CAAAK,wBAAwB,CAAG,CAC/BC,KAAK,CAAE,CAACT,oBAAoB,CAACI,gBAAgB,CAAC,CAChD,CAAC,CACDH,WAAW,CAACO,wBAAwB,CAAEF,mBAAmB,CAAEI,SAAS,CAAC,CACvE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const applyStyleForBelowTopScreen = exports.applyStyleForBelowTopScreen = function () {\n    const _e = [new global.Error(), -3, -27];\n    const applyStyleForBelowTopScreen = function (screenTransitionConfig, event) {\n      const {\n        screenDimensions,\n        belowTopScreenId,\n        screenTransition\n      } = screenTransitionConfig;\n      const {\n        belowTopScreenStyle: computeBelowTopScreenStyle\n      } = screenTransition;\n      const belowTopScreenStyle = computeBelowTopScreenStyle(event, screenDimensions);\n      const belowTopScreenDescriptor = {\n        value: [createViewDescriptor(belowTopScreenId)]\n      };\n      (0, _index.updateProps)(belowTopScreenDescriptor, belowTopScreenStyle, undefined);\n    };\n    applyStyleForBelowTopScreen.__closure = {\n      createViewDescriptor,\n      updateProps: _index.updateProps\n    };\n    applyStyleForBelowTopScreen.__workletHash = 2747176653608;\n    applyStyleForBelowTopScreen.__initData = _worklet_2747176653608_init_data;\n    applyStyleForBelowTopScreen.__stackDetails = _e;\n    return applyStyleForBelowTopScreen;\n  }();\n  const _worklet_7793038430696_init_data = {\n    code: \"function applyStyle_reactNativeReanimated_styleUpdaterJs5(screenTransitionConfig,event){const{applyStyleForTopScreen,applyStyleForBelowTopScreen}=this.__closure;applyStyleForTopScreen(screenTransitionConfig,event);applyStyleForBelowTopScreen(screenTransitionConfig,event);}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyStyle_reactNativeReanimated_styleUpdaterJs5\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"applyStyleForTopScreen\\\",\\\"applyStyleForBelowTopScreen\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/styleUpdater.js\\\"],\\\"mappings\\\":\\\"AAuDO,SAAAA,gDAAmDA,CAAAC,sBAAA,CAAAC,KAAA,QAAAC,sBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAGxDF,sBAAsB,CAACF,sBAAsB,CAAEC,KAAK,CAAC,CACrDE,2BAA2B,CAACH,sBAAsB,CAAEC,KAAK,CAAC,CAC5D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const applyStyle = exports.applyStyle = function () {\n    const _e = [new global.Error(), -3, -27];\n    const applyStyle = function (screenTransitionConfig, event) {\n      applyStyleForTopScreen(screenTransitionConfig, event);\n      applyStyleForBelowTopScreen(screenTransitionConfig, event);\n    };\n    applyStyle.__closure = {\n      applyStyleForTopScreen,\n      applyStyleForBelowTopScreen\n    };\n    applyStyle.__workletHash = 7793038430696;\n    applyStyle.__initData = _worklet_7793038430696_init_data;\n    applyStyle.__stackDetails = _e;\n    return applyStyle;\n  }();\n});", "lineCount": 136, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "applyStyleForBelowTopScreen"], [7, 37, 1, 13], [7, 40, 1, 13, "exports"], [7, 47, 1, 13], [7, 48, 1, 13, "applyStyle"], [7, 58, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_index"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 5, 0], [10, 8, 5, 6, "IS_FABRIC"], [10, 17, 5, 15], [10, 20, 5, 18], [10, 24, 5, 18, "isF<PERSON><PERSON>"], [10, 49, 5, 26], [10, 51, 5, 27], [10, 52, 5, 28], [11, 2, 5, 29], [11, 8, 5, 29, "_worklet_14626504491388_init_data"], [11, 41, 5, 29], [12, 4, 5, 29, "code"], [12, 8, 5, 29], [13, 4, 5, 29, "location"], [13, 12, 5, 29], [14, 4, 5, 29, "sourceMap"], [14, 13, 5, 29], [15, 4, 5, 29, "version"], [15, 11, 5, 29], [16, 2, 5, 29], [17, 2, 5, 29], [17, 8, 5, 29, "createViewDescriptorPaper"], [17, 33, 5, 29], [17, 36, 6, 0], [18, 4, 6, 0], [18, 10, 6, 0, "_e"], [18, 12, 6, 0], [18, 20, 6, 0, "global"], [18, 26, 6, 0], [18, 27, 6, 0, "Error"], [18, 32, 6, 0], [19, 4, 6, 0], [19, 10, 6, 0, "createViewDescriptorPaper"], [19, 35, 6, 0], [19, 47, 6, 0, "createViewDescriptorPaper"], [19, 48, 6, 35, "screenId"], [19, 56, 6, 43], [19, 58, 6, 45], [20, 6, 9, 2], [20, 13, 9, 9], [21, 8, 10, 4, "tag"], [21, 11, 10, 7], [21, 13, 10, 9, "screenId"], [21, 21, 10, 17], [22, 8, 11, 4, "name"], [22, 12, 11, 8], [22, 14, 11, 10], [23, 6, 12, 2], [23, 7, 12, 3], [24, 4, 13, 0], [24, 5, 13, 1], [25, 4, 13, 1, "createViewDescriptorPaper"], [25, 29, 13, 1], [25, 30, 13, 1, "__closure"], [25, 39, 13, 1], [26, 4, 13, 1, "createViewDescriptorPaper"], [26, 29, 13, 1], [26, 30, 13, 1, "__workletHash"], [26, 43, 13, 1], [27, 4, 13, 1, "createViewDescriptorPaper"], [27, 29, 13, 1], [27, 30, 13, 1, "__initData"], [27, 40, 13, 1], [27, 43, 13, 1, "_worklet_14626504491388_init_data"], [27, 76, 13, 1], [28, 4, 13, 1, "createViewDescriptorPaper"], [28, 29, 13, 1], [28, 30, 13, 1, "__stackDetails"], [28, 44, 13, 1], [28, 47, 13, 1, "_e"], [28, 49, 13, 1], [29, 4, 13, 1], [29, 11, 13, 1, "createViewDescriptorPaper"], [29, 36, 13, 1], [30, 2, 13, 1], [30, 3, 6, 0], [31, 2, 6, 0], [31, 8, 6, 0, "_worklet_13488996496906_init_data"], [31, 41, 6, 0], [32, 4, 6, 0, "code"], [32, 8, 6, 0], [33, 4, 6, 0, "location"], [33, 12, 6, 0], [34, 4, 6, 0, "sourceMap"], [34, 13, 6, 0], [35, 4, 6, 0, "version"], [35, 11, 6, 0], [36, 2, 6, 0], [37, 2, 6, 0], [37, 8, 6, 0, "createViewDescriptorFabric"], [37, 34, 6, 0], [37, 37, 14, 0], [38, 4, 14, 0], [38, 10, 14, 0, "_e"], [38, 12, 14, 0], [38, 20, 14, 0, "global"], [38, 26, 14, 0], [38, 27, 14, 0, "Error"], [38, 32, 14, 0], [39, 4, 14, 0], [39, 10, 14, 0, "createViewDescriptorFabric"], [39, 36, 14, 0], [39, 48, 14, 0, "createViewDescriptorFabric"], [39, 49, 14, 36, "screenId"], [39, 57, 14, 44], [39, 59, 14, 46], [40, 6, 17, 2], [40, 13, 17, 9], [41, 8, 18, 4, "shadowNodeWrapper"], [41, 25, 18, 21], [41, 27, 18, 23, "screenId"], [42, 6, 19, 2], [42, 7, 19, 3], [43, 4, 20, 0], [43, 5, 20, 1], [44, 4, 20, 1, "createViewDescriptorFabric"], [44, 30, 20, 1], [44, 31, 20, 1, "__closure"], [44, 40, 20, 1], [45, 4, 20, 1, "createViewDescriptorFabric"], [45, 30, 20, 1], [45, 31, 20, 1, "__workletHash"], [45, 44, 20, 1], [46, 4, 20, 1, "createViewDescriptorFabric"], [46, 30, 20, 1], [46, 31, 20, 1, "__initData"], [46, 41, 20, 1], [46, 44, 20, 1, "_worklet_13488996496906_init_data"], [46, 77, 20, 1], [47, 4, 20, 1, "createViewDescriptorFabric"], [47, 30, 20, 1], [47, 31, 20, 1, "__stackDetails"], [47, 45, 20, 1], [47, 48, 20, 1, "_e"], [47, 50, 20, 1], [48, 4, 20, 1], [48, 11, 20, 1, "createViewDescriptorFabric"], [48, 37, 20, 1], [49, 2, 20, 1], [49, 3, 14, 0], [50, 2, 21, 0], [50, 8, 21, 6, "createViewDescriptor"], [50, 28, 21, 26], [50, 31, 21, 29, "IS_FABRIC"], [50, 40, 21, 38], [50, 43, 21, 41, "createViewDescriptorFabric"], [50, 69, 21, 67], [50, 72, 21, 70, "createViewDescriptorPaper"], [50, 97, 21, 95], [51, 2, 21, 96], [51, 8, 21, 96, "_worklet_15431420270588_init_data"], [51, 41, 21, 96], [52, 4, 21, 96, "code"], [52, 8, 21, 96], [53, 4, 21, 96, "location"], [53, 12, 21, 96], [54, 4, 21, 96, "sourceMap"], [54, 13, 21, 96], [55, 4, 21, 96, "version"], [55, 11, 21, 96], [56, 2, 21, 96], [57, 2, 21, 96], [57, 8, 21, 96, "applyStyleForTopScreen"], [57, 30, 21, 96], [57, 33, 22, 0], [58, 4, 22, 0], [58, 10, 22, 0, "_e"], [58, 12, 22, 0], [58, 20, 22, 0, "global"], [58, 26, 22, 0], [58, 27, 22, 0, "Error"], [58, 32, 22, 0], [59, 4, 22, 0], [59, 10, 22, 0, "applyStyleForTopScreen"], [59, 32, 22, 0], [59, 44, 22, 0, "applyStyleForTopScreen"], [59, 45, 22, 32, "screenTransitionConfig"], [59, 67, 22, 54], [59, 69, 22, 56, "event"], [59, 74, 22, 61], [59, 76, 22, 63], [60, 6, 25, 2], [60, 12, 25, 8], [61, 8, 26, 4, "screenDimensions"], [61, 24, 26, 20], [62, 8, 27, 4, "topScreenId"], [62, 19, 27, 15], [63, 8, 28, 4, "screenTransition"], [64, 6, 29, 2], [64, 7, 29, 3], [64, 10, 29, 6, "screenTransitionConfig"], [64, 32, 29, 28], [65, 6, 30, 2], [65, 12, 30, 8], [66, 8, 31, 4, "topScreenStyle"], [66, 22, 31, 18], [66, 24, 31, 20, "computeTopScreenStyle"], [67, 6, 32, 2], [67, 7, 32, 3], [67, 10, 32, 6, "screenTransition"], [67, 26, 32, 22], [68, 6, 33, 2], [68, 12, 33, 8, "topScreenStyle"], [68, 26, 33, 22], [68, 29, 33, 25, "computeTopScreenStyle"], [68, 50, 33, 46], [68, 51, 33, 47, "event"], [68, 56, 33, 52], [68, 58, 33, 54, "screenDimensions"], [68, 74, 33, 70], [68, 75, 33, 71], [69, 6, 34, 2], [69, 12, 34, 8, "topScreenDescriptor"], [69, 31, 34, 27], [69, 34, 34, 30], [70, 8, 35, 4, "value"], [70, 13, 35, 9], [70, 15, 35, 11], [70, 16, 35, 12, "createViewDescriptor"], [70, 36, 35, 32], [70, 37, 35, 33, "topScreenId"], [70, 48, 35, 44], [70, 49, 35, 45], [71, 6, 36, 2], [71, 7, 36, 3], [72, 6, 37, 2], [72, 10, 37, 2, "updateProps"], [72, 28, 37, 13], [72, 30, 37, 14, "topScreenDescriptor"], [72, 49, 37, 33], [72, 51, 37, 35, "topScreenStyle"], [72, 65, 37, 49], [72, 67, 37, 51, "undefined"], [72, 76, 37, 60], [72, 77, 37, 61], [73, 4, 38, 0], [73, 5, 38, 1], [74, 4, 38, 1, "applyStyleForTopScreen"], [74, 26, 38, 1], [74, 27, 38, 1, "__closure"], [74, 36, 38, 1], [75, 6, 38, 1, "createViewDescriptor"], [75, 26, 38, 1], [76, 6, 38, 1, "updateProps"], [76, 17, 38, 1], [76, 19, 37, 2, "updateProps"], [77, 4, 37, 13], [78, 4, 37, 13, "applyStyleForTopScreen"], [78, 26, 37, 13], [78, 27, 37, 13, "__workletHash"], [78, 40, 37, 13], [79, 4, 37, 13, "applyStyleForTopScreen"], [79, 26, 37, 13], [79, 27, 37, 13, "__initData"], [79, 37, 37, 13], [79, 40, 37, 13, "_worklet_15431420270588_init_data"], [79, 73, 37, 13], [80, 4, 37, 13, "applyStyleForTopScreen"], [80, 26, 37, 13], [80, 27, 37, 13, "__stackDetails"], [80, 41, 37, 13], [80, 44, 37, 13, "_e"], [80, 46, 37, 13], [81, 4, 37, 13], [81, 11, 37, 13, "applyStyleForTopScreen"], [81, 33, 37, 13], [82, 2, 37, 13], [82, 3, 22, 0], [83, 2, 22, 0], [83, 8, 22, 0, "_worklet_2747176653608_init_data"], [83, 40, 22, 0], [84, 4, 22, 0, "code"], [84, 8, 22, 0], [85, 4, 22, 0, "location"], [85, 12, 22, 0], [86, 4, 22, 0, "sourceMap"], [86, 13, 22, 0], [87, 4, 22, 0, "version"], [87, 11, 22, 0], [88, 2, 22, 0], [89, 2, 22, 0], [89, 8, 22, 0, "applyStyleForBelowTopScreen"], [89, 35, 22, 0], [89, 38, 22, 0, "exports"], [89, 45, 22, 0], [89, 46, 22, 0, "applyStyleForBelowTopScreen"], [89, 73, 22, 0], [89, 76, 39, 7], [90, 4, 39, 7], [90, 10, 39, 7, "_e"], [90, 12, 39, 7], [90, 20, 39, 7, "global"], [90, 26, 39, 7], [90, 27, 39, 7, "Error"], [90, 32, 39, 7], [91, 4, 39, 7], [91, 10, 39, 7, "applyStyleForBelowTopScreen"], [91, 37, 39, 7], [91, 49, 39, 7, "applyStyleForBelowTopScreen"], [91, 50, 39, 44, "screenTransitionConfig"], [91, 72, 39, 66], [91, 74, 39, 68, "event"], [91, 79, 39, 73], [91, 81, 39, 75], [92, 6, 42, 2], [92, 12, 42, 8], [93, 8, 43, 4, "screenDimensions"], [93, 24, 43, 20], [94, 8, 44, 4, "belowTopScreenId"], [94, 24, 44, 20], [95, 8, 45, 4, "screenTransition"], [96, 6, 46, 2], [96, 7, 46, 3], [96, 10, 46, 6, "screenTransitionConfig"], [96, 32, 46, 28], [97, 6, 47, 2], [97, 12, 47, 8], [98, 8, 48, 4, "belowTopScreenStyle"], [98, 27, 48, 23], [98, 29, 48, 25, "computeBelowTopScreenStyle"], [99, 6, 49, 2], [99, 7, 49, 3], [99, 10, 49, 6, "screenTransition"], [99, 26, 49, 22], [100, 6, 50, 2], [100, 12, 50, 8, "belowTopScreenStyle"], [100, 31, 50, 27], [100, 34, 50, 30, "computeBelowTopScreenStyle"], [100, 60, 50, 56], [100, 61, 50, 57, "event"], [100, 66, 50, 62], [100, 68, 50, 64, "screenDimensions"], [100, 84, 50, 80], [100, 85, 50, 81], [101, 6, 51, 2], [101, 12, 51, 8, "belowTopScreenDescriptor"], [101, 36, 51, 32], [101, 39, 51, 35], [102, 8, 52, 4, "value"], [102, 13, 52, 9], [102, 15, 52, 11], [102, 16, 52, 12, "createViewDescriptor"], [102, 36, 52, 32], [102, 37, 52, 33, "belowTopScreenId"], [102, 53, 52, 49], [102, 54, 52, 50], [103, 6, 53, 2], [103, 7, 53, 3], [104, 6, 54, 2], [104, 10, 54, 2, "updateProps"], [104, 28, 54, 13], [104, 30, 54, 14, "belowTopScreenDescriptor"], [104, 54, 54, 38], [104, 56, 54, 40, "belowTopScreenStyle"], [104, 75, 54, 59], [104, 77, 54, 61, "undefined"], [104, 86, 54, 70], [104, 87, 54, 71], [105, 4, 55, 0], [105, 5, 55, 1], [106, 4, 55, 1, "applyStyleForBelowTopScreen"], [106, 31, 55, 1], [106, 32, 55, 1, "__closure"], [106, 41, 55, 1], [107, 6, 55, 1, "createViewDescriptor"], [107, 26, 55, 1], [108, 6, 55, 1, "updateProps"], [108, 17, 55, 1], [108, 19, 54, 2, "updateProps"], [109, 4, 54, 13], [110, 4, 54, 13, "applyStyleForBelowTopScreen"], [110, 31, 54, 13], [110, 32, 54, 13, "__workletHash"], [110, 45, 54, 13], [111, 4, 54, 13, "applyStyleForBelowTopScreen"], [111, 31, 54, 13], [111, 32, 54, 13, "__initData"], [111, 42, 54, 13], [111, 45, 54, 13, "_worklet_2747176653608_init_data"], [111, 77, 54, 13], [112, 4, 54, 13, "applyStyleForBelowTopScreen"], [112, 31, 54, 13], [112, 32, 54, 13, "__stackDetails"], [112, 46, 54, 13], [112, 49, 54, 13, "_e"], [112, 51, 54, 13], [113, 4, 54, 13], [113, 11, 54, 13, "applyStyleForBelowTopScreen"], [113, 38, 54, 13], [114, 2, 54, 13], [114, 3, 39, 7], [115, 2, 39, 7], [115, 8, 39, 7, "_worklet_7793038430696_init_data"], [115, 40, 39, 7], [116, 4, 39, 7, "code"], [116, 8, 39, 7], [117, 4, 39, 7, "location"], [117, 12, 39, 7], [118, 4, 39, 7, "sourceMap"], [118, 13, 39, 7], [119, 4, 39, 7, "version"], [119, 11, 39, 7], [120, 2, 39, 7], [121, 2, 39, 7], [121, 8, 39, 7, "applyStyle"], [121, 18, 39, 7], [121, 21, 39, 7, "exports"], [121, 28, 39, 7], [121, 29, 39, 7, "applyStyle"], [121, 39, 39, 7], [121, 42, 56, 7], [122, 4, 56, 7], [122, 10, 56, 7, "_e"], [122, 12, 56, 7], [122, 20, 56, 7, "global"], [122, 26, 56, 7], [122, 27, 56, 7, "Error"], [122, 32, 56, 7], [123, 4, 56, 7], [123, 10, 56, 7, "applyStyle"], [123, 20, 56, 7], [123, 32, 56, 7, "applyStyle"], [123, 33, 56, 27, "screenTransitionConfig"], [123, 55, 56, 49], [123, 57, 56, 51, "event"], [123, 62, 56, 56], [123, 64, 56, 58], [124, 6, 59, 2, "applyStyleForTopScreen"], [124, 28, 59, 24], [124, 29, 59, 25, "screenTransitionConfig"], [124, 51, 59, 47], [124, 53, 59, 49, "event"], [124, 58, 59, 54], [124, 59, 59, 55], [125, 6, 60, 2, "applyStyleForBelowTopScreen"], [125, 33, 60, 29], [125, 34, 60, 30, "screenTransitionConfig"], [125, 56, 60, 52], [125, 58, 60, 54, "event"], [125, 63, 60, 59], [125, 64, 60, 60], [126, 4, 61, 0], [126, 5, 61, 1], [127, 4, 61, 1, "applyStyle"], [127, 14, 61, 1], [127, 15, 61, 1, "__closure"], [127, 24, 61, 1], [128, 6, 61, 1, "applyStyleForTopScreen"], [128, 28, 61, 1], [129, 6, 61, 1, "applyStyleForBelowTopScreen"], [130, 4, 61, 1], [131, 4, 61, 1, "applyStyle"], [131, 14, 61, 1], [131, 15, 61, 1, "__workletHash"], [131, 28, 61, 1], [132, 4, 61, 1, "applyStyle"], [132, 14, 61, 1], [132, 15, 61, 1, "__initData"], [132, 25, 61, 1], [132, 28, 61, 1, "_worklet_7793038430696_init_data"], [132, 60, 61, 1], [133, 4, 61, 1, "applyStyle"], [133, 14, 61, 1], [133, 15, 61, 1, "__stackDetails"], [133, 29, 61, 1], [133, 32, 61, 1, "_e"], [133, 34, 61, 1], [134, 4, 61, 1], [134, 11, 61, 1, "applyStyle"], [134, 21, 61, 1], [135, 2, 61, 1], [135, 3, 56, 7], [136, 0, 56, 7], [136, 3]], "functionMap": {"names": ["<global>", "createViewDescriptorPaper", "createViewDescriptorFabric", "applyStyleForTopScreen", "applyStyleForBelowTopScreen", "applyStyle"], "mappings": "AAA;ACK;CDO;AEC;CFM;AGE;CHgB;OIC;CJgB;OKC;CLK"}}, "type": "js/module"}]}