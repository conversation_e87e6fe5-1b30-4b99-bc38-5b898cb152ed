{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 60}, "end": {"line": 10, "column": 22, "index": 172}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/slicedToArray\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _reactNativeSafeAreaContext = require(_dependencyMap[5], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[6], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[7], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[8], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[9], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[10], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[11], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[12], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[13], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[14], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[15], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[16], \"@/components/MessageBubble\"));\n  var _jsxDevRuntime = require(_dependencyMap[17], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    var colors = (0, _useColors.useColors)();\n    var _useFonts = (0, _poppins.useFonts)({\n        Poppins_400Regular: _poppins.Poppins_400Regular,\n        Poppins_500Medium: _poppins.Poppins_500Medium,\n        Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n      }),\n      _useFonts2 = (0, _slicedToArray2.default)(_useFonts, 1),\n      fontsLoaded = _useFonts2[0];\n    var recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    var recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    var _useState = (0, _react.useState)([]),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      messages = _useState2[0],\n      setMessages = _useState2[1];\n    var _useState3 = (0, _react.useState)(\"\"),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      inputText = _useState4[0],\n      setInputText = _useState4[1];\n    var _useState5 = (0, _react.useState)(true),\n      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),\n      isFirstTime = _useState6[0],\n      setIsFirstTime = _useState6[1];\n    var _useState7 = (0, _react.useState)(false),\n      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),\n      isLoading = _useState8[0],\n      setIsLoading = _useState8[1];\n    var _useState9 = (0, _react.useState)([]),\n      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),\n      quickActions = _useState0[0],\n      setQuickActions = _useState0[1];\n    var _useState1 = (0, _react.useState)(true),\n      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),\n      voiceMode = _useState10[0],\n      setVoiceMode = _useState10[1]; // Voice mode on by default\n    var _useState11 = (0, _react.useState)(false),\n      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),\n      hasPermission = _useState12[0],\n      setHasPermission = _useState12[1];\n    var _useState13 = (0, _react.useState)(false),\n      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),\n      isDictating = _useState14[0],\n      setIsDictating = _useState14[1];\n    var _useState15 = (0, _react.useState)(null),\n      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),\n      longPressedMessage = _useState16[0],\n      setLongPressedMessage = _useState16[1];\n    var _useState17 = (0, _react.useState)(false),\n      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),\n      isContextMenuVisible = _useState18[0],\n      setIsContextMenuVisible = _useState18[1];\n    var _useState19 = (0, _react.useState)([]),\n      _useState20 = (0, _slicedToArray2.default)(_useState19, 2),\n      transcript = _useState20[0],\n      setTranscript = _useState20[1];\n    var _useState21 = (0, _react.useState)(false),\n      _useState22 = (0, _slicedToArray2.default)(_useState21, 2),\n      isMuted = _useState22[0],\n      setIsMuted = _useState22[1];\n    var scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (0, _asyncToGenerator2.default)(function* () {\n        var _yield$requestRecordi = yield (0, _expoAudio.requestRecordingPermissionsAsync)(),\n          granted = _yield$requestRecordi.granted;\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _reactNative.Alert.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    var handleStartBrainstorming = () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n    };\n    var handleSendMessage = function () {\n      var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : inputText;\n      if (!message.trim()) return;\n      var newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        var aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    var handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    var toggleVoiceMode = () => {\n      if (!voiceMode && !hasPermission) {\n        _reactNative.Alert.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: function () {\n            var _ref2 = (0, _asyncToGenerator2.default)(function* () {\n              var _yield$requestRecordi2 = yield (0, _expoAudio.requestRecordingPermissionsAsync)(),\n                granted = _yield$requestRecordi2.granted;\n              if (granted) {\n                setHasPermission(true);\n                setVoiceMode(true);\n              }\n            });\n            return function onPress() {\n              return _ref2.apply(this, arguments);\n            };\n          }()\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      setVoiceMode(!voiceMode);\n    };\n    var handleVoiceRecord = /*#__PURE__*/function () {\n      var _ref3 = (0, _asyncToGenerator2.default)(function* () {\n        if (!hasPermission) {\n          _reactNative.Alert.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n          return;\n        }\n        try {\n          if (recorderState.isRecording) {\n            yield recorder.stop();\n\n            // Here you would typically process the audio file\n            // For now, we'll simulate converting speech to text\n            var mockTranscript = \"This is a voice message\"; // In real app, you'd use speech-to-text service\n            setTranscript(prev => [...prev, {\n              speaker: 'user',\n              text: mockTranscript\n            }]);\n            handleSendMessage(mockTranscript);\n          } else {\n            yield recorder.prepareToRecordAsync();\n            recorder.record();\n            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n          }\n        } catch (error) {\n          console.error(\"Error with voice recording:\", error);\n          _reactNative.Alert.alert(\"Error\", \"Failed to record voice message\");\n        }\n      });\n      return function handleVoiceRecord() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var handleDictation = /*#__PURE__*/function () {\n      var _ref4 = (0, _asyncToGenerator2.default)(function* () {\n        if (!hasPermission) {\n          _reactNative.Alert.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n          return;\n        }\n        try {\n          if (isDictating) {\n            yield recorder.stop();\n            var mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n            setInputText(inputText + mockTranscript);\n            setIsDictating(false);\n          } else {\n            yield recorder.prepareToRecordAsync();\n            recorder.record();\n            setIsDictating(true);\n            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n          }\n        } catch (error) {\n          console.error(\"Error with dictation:\", error);\n          _reactNative.Alert.alert(\"Error\", \"Failed to dictate message\");\n          setIsDictating(false);\n        }\n      });\n      return function handleDictation() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    var handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _reactNative.Clipboard.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    var handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _reactNative.Alert.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    var handleMute = () => {\n      setIsMuted(!isMuted);\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _reactNative.Alert.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        onRecord: handleVoiceRecord,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.ScrollView, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Modal, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.View, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.TouchableOpacity, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_reactNative.Text, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"9C1WkPoISXmHucsqM0n1HH2DnhY=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 618, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_react"], [9, 12, 1, 0], [9, 15, 1, 0, "_interopRequireWildcard"], [9, 38, 1, 0], [9, 39, 1, 0, "require"], [9, 46, 1, 0], [9, 47, 1, 0, "_dependencyMap"], [9, 61, 1, 0], [10, 2, 2, 0], [10, 6, 2, 0, "_reactNative"], [10, 18, 2, 0], [10, 21, 2, 0, "require"], [10, 28, 2, 0], [10, 29, 2, 0, "_dependencyMap"], [10, 43, 2, 0], [11, 2, 11, 0], [11, 6, 11, 0, "_reactNativeSafeAreaContext"], [11, 33, 11, 0], [11, 36, 11, 0, "require"], [11, 43, 11, 0], [11, 44, 11, 0, "_dependencyMap"], [11, 58, 11, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_poppins"], [12, 14, 12, 0], [12, 17, 12, 0, "require"], [12, 24, 12, 0], [12, 25, 12, 0, "_dependencyMap"], [12, 39, 12, 0], [13, 2, 18, 0], [13, 6, 18, 0, "_lucideReactNative"], [13, 24, 18, 0], [13, 27, 18, 0, "require"], [13, 34, 18, 0], [13, 35, 18, 0, "_dependencyMap"], [13, 49, 18, 0], [14, 2, 19, 0], [14, 6, 19, 0, "Haptics"], [14, 13, 19, 0], [14, 16, 19, 0, "_interopRequireWildcard"], [14, 39, 19, 0], [14, 40, 19, 0, "require"], [14, 47, 19, 0], [14, 48, 19, 0, "_dependencyMap"], [14, 62, 19, 0], [15, 2, 20, 0], [15, 6, 20, 0, "_useColors"], [15, 16, 20, 0], [15, 19, 20, 0, "require"], [15, 26, 20, 0], [15, 27, 20, 0, "_dependencyMap"], [15, 41, 20, 0], [16, 2, 21, 0], [16, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [16, 35, 21, 0], [16, 38, 21, 0, "_interopRequireDefault"], [16, 60, 21, 0], [16, 61, 21, 0, "require"], [16, 68, 21, 0], [16, 69, 21, 0, "_dependencyMap"], [16, 83, 21, 0], [17, 2, 22, 0], [17, 6, 22, 0, "_expoAudio"], [17, 16, 22, 0], [17, 19, 22, 0, "require"], [17, 26, 22, 0], [17, 27, 22, 0, "_dependencyMap"], [17, 41, 22, 0], [18, 2, 28, 0], [18, 6, 28, 0, "_fakeData"], [18, 15, 28, 0], [18, 18, 28, 0, "require"], [18, 25, 28, 0], [18, 26, 28, 0, "_dependencyMap"], [18, 40, 28, 0], [19, 2, 29, 0], [19, 6, 29, 0, "_Header"], [19, 13, 29, 0], [19, 16, 29, 0, "_interopRequireDefault"], [19, 38, 29, 0], [19, 39, 29, 0, "require"], [19, 46, 29, 0], [19, 47, 29, 0, "_dependencyMap"], [19, 61, 29, 0], [20, 2, 30, 0], [20, 6, 30, 0, "_TextMode"], [20, 15, 30, 0], [20, 18, 30, 0, "_interopRequireDefault"], [20, 40, 30, 0], [20, 41, 30, 0, "require"], [20, 48, 30, 0], [20, 49, 30, 0, "_dependencyMap"], [20, 63, 30, 0], [21, 2, 31, 0], [21, 6, 31, 0, "_VoiceMode"], [21, 16, 31, 0], [21, 19, 31, 0, "_interopRequireDefault"], [21, 41, 31, 0], [21, 42, 31, 0, "require"], [21, 49, 31, 0], [21, 50, 31, 0, "_dependencyMap"], [21, 64, 31, 0], [22, 2, 32, 0], [22, 6, 32, 0, "_MessageBubble"], [22, 20, 32, 0], [22, 23, 32, 0, "_interopRequireDefault"], [22, 45, 32, 0], [22, 46, 32, 0, "require"], [22, 53, 32, 0], [22, 54, 32, 0, "_dependencyMap"], [22, 68, 32, 0], [23, 2, 32, 55], [23, 6, 32, 55, "_jsxDevRuntime"], [23, 20, 32, 55], [23, 23, 32, 55, "require"], [23, 30, 32, 55], [23, 31, 32, 55, "_dependencyMap"], [23, 45, 32, 55], [24, 2, 32, 55], [24, 6, 32, 55, "_jsxFileName"], [24, 18, 32, 55], [25, 4, 32, 55, "_s"], [25, 6, 32, 55], [25, 9, 32, 55, "$RefreshSig$"], [25, 21, 32, 55], [26, 2, 32, 55], [26, 11, 32, 55, "_interopRequireWildcard"], [26, 35, 32, 55, "e"], [26, 36, 32, 55], [26, 38, 32, 55, "t"], [26, 39, 32, 55], [26, 68, 32, 55, "WeakMap"], [26, 75, 32, 55], [26, 81, 32, 55, "r"], [26, 82, 32, 55], [26, 89, 32, 55, "WeakMap"], [26, 96, 32, 55], [26, 100, 32, 55, "n"], [26, 101, 32, 55], [26, 108, 32, 55, "WeakMap"], [26, 115, 32, 55], [26, 127, 32, 55, "_interopRequireWildcard"], [26, 150, 32, 55], [26, 162, 32, 55, "_interopRequireWildcard"], [26, 163, 32, 55, "e"], [26, 164, 32, 55], [26, 166, 32, 55, "t"], [26, 167, 32, 55], [26, 176, 32, 55, "t"], [26, 177, 32, 55], [26, 181, 32, 55, "e"], [26, 182, 32, 55], [26, 186, 32, 55, "e"], [26, 187, 32, 55], [26, 188, 32, 55, "__esModule"], [26, 198, 32, 55], [26, 207, 32, 55, "e"], [26, 208, 32, 55], [26, 214, 32, 55, "o"], [26, 215, 32, 55], [26, 217, 32, 55, "i"], [26, 218, 32, 55], [26, 220, 32, 55, "f"], [26, 221, 32, 55], [26, 226, 32, 55, "__proto__"], [26, 235, 32, 55], [26, 243, 32, 55, "default"], [26, 250, 32, 55], [26, 252, 32, 55, "e"], [26, 253, 32, 55], [26, 270, 32, 55, "e"], [26, 271, 32, 55], [26, 294, 32, 55, "e"], [26, 295, 32, 55], [26, 320, 32, 55, "e"], [26, 321, 32, 55], [26, 330, 32, 55, "f"], [26, 331, 32, 55], [26, 337, 32, 55, "o"], [26, 338, 32, 55], [26, 341, 32, 55, "t"], [26, 342, 32, 55], [26, 345, 32, 55, "n"], [26, 346, 32, 55], [26, 349, 32, 55, "r"], [26, 350, 32, 55], [26, 358, 32, 55, "o"], [26, 359, 32, 55], [26, 360, 32, 55, "has"], [26, 363, 32, 55], [26, 364, 32, 55, "e"], [26, 365, 32, 55], [26, 375, 32, 55, "o"], [26, 376, 32, 55], [26, 377, 32, 55, "get"], [26, 380, 32, 55], [26, 381, 32, 55, "e"], [26, 382, 32, 55], [26, 385, 32, 55, "o"], [26, 386, 32, 55], [26, 387, 32, 55, "set"], [26, 390, 32, 55], [26, 391, 32, 55, "e"], [26, 392, 32, 55], [26, 394, 32, 55, "f"], [26, 395, 32, 55], [26, 409, 32, 55, "_t"], [26, 411, 32, 55], [26, 415, 32, 55, "e"], [26, 416, 32, 55], [26, 432, 32, 55, "_t"], [26, 434, 32, 55], [26, 441, 32, 55, "hasOwnProperty"], [26, 455, 32, 55], [26, 456, 32, 55, "call"], [26, 460, 32, 55], [26, 461, 32, 55, "e"], [26, 462, 32, 55], [26, 464, 32, 55, "_t"], [26, 466, 32, 55], [26, 473, 32, 55, "i"], [26, 474, 32, 55], [26, 478, 32, 55, "o"], [26, 479, 32, 55], [26, 482, 32, 55, "Object"], [26, 488, 32, 55], [26, 489, 32, 55, "defineProperty"], [26, 503, 32, 55], [26, 508, 32, 55, "Object"], [26, 514, 32, 55], [26, 515, 32, 55, "getOwnPropertyDescriptor"], [26, 539, 32, 55], [26, 540, 32, 55, "e"], [26, 541, 32, 55], [26, 543, 32, 55, "_t"], [26, 545, 32, 55], [26, 552, 32, 55, "i"], [26, 553, 32, 55], [26, 554, 32, 55, "get"], [26, 557, 32, 55], [26, 561, 32, 55, "i"], [26, 562, 32, 55], [26, 563, 32, 55, "set"], [26, 566, 32, 55], [26, 570, 32, 55, "o"], [26, 571, 32, 55], [26, 572, 32, 55, "f"], [26, 573, 32, 55], [26, 575, 32, 55, "_t"], [26, 577, 32, 55], [26, 579, 32, 55, "i"], [26, 580, 32, 55], [26, 584, 32, 55, "f"], [26, 585, 32, 55], [26, 586, 32, 55, "_t"], [26, 588, 32, 55], [26, 592, 32, 55, "e"], [26, 593, 32, 55], [26, 594, 32, 55, "_t"], [26, 596, 32, 55], [26, 607, 32, 55, "f"], [26, 608, 32, 55], [26, 613, 32, 55, "e"], [26, 614, 32, 55], [26, 616, 32, 55, "t"], [26, 617, 32, 55], [27, 2, 34, 15], [27, 11, 34, 24, "BrainstormScreen"], [27, 27, 34, 40, "BrainstormScreen"], [27, 28, 34, 40], [27, 30, 34, 43], [28, 4, 34, 43, "_s"], [28, 6, 34, 43], [29, 4, 35, 2], [29, 8, 35, 8, "insets"], [29, 14, 35, 14], [29, 17, 35, 17], [29, 21, 35, 17, "useSafeAreaInsets"], [29, 66, 35, 34], [29, 68, 35, 35], [29, 69, 35, 36], [30, 4, 36, 2], [30, 8, 36, 8, "colors"], [30, 14, 36, 14], [30, 17, 36, 17], [30, 21, 36, 17, "useColors"], [30, 41, 36, 26], [30, 43, 36, 27], [30, 44, 36, 28], [31, 4, 37, 2], [31, 8, 37, 2, "_useFonts"], [31, 17, 37, 2], [31, 20, 37, 24], [31, 24, 37, 24, "useFonts"], [31, 41, 37, 32], [31, 43, 37, 33], [32, 8, 38, 4, "Poppins_400Regular"], [32, 26, 38, 22], [32, 28, 38, 4, "Poppins_400Regular"], [32, 55, 38, 22], [33, 8, 39, 4, "Poppins_500Medium"], [33, 25, 39, 21], [33, 27, 39, 4, "Poppins_500Medium"], [33, 53, 39, 21], [34, 8, 40, 4, "Poppins_600SemiBold"], [34, 27, 40, 23], [34, 29, 40, 4, "Poppins_600SemiBold"], [35, 6, 41, 2], [35, 7, 41, 3], [35, 8, 41, 4], [36, 6, 41, 4, "_useFonts2"], [36, 16, 41, 4], [36, 23, 41, 4, "_slicedToArray2"], [36, 38, 41, 4], [36, 39, 41, 4, "default"], [36, 46, 41, 4], [36, 48, 41, 4, "_useFonts"], [36, 57, 41, 4], [37, 6, 37, 9, "fontsLoaded"], [37, 17, 37, 20], [37, 20, 37, 20, "_useFonts2"], [37, 30, 37, 20], [38, 4, 43, 2], [38, 8, 43, 8, "recorder"], [38, 16, 43, 16], [38, 19, 43, 19], [38, 23, 43, 19, "useAudioRecorder"], [38, 50, 43, 35], [38, 52, 43, 36, "RecordingPresets"], [38, 79, 43, 52], [38, 80, 43, 53, "HIGH_QUALITY"], [38, 92, 43, 65], [38, 93, 43, 66], [39, 4, 44, 2], [39, 8, 44, 8, "recorderState"], [39, 21, 44, 21], [39, 24, 44, 24], [39, 28, 44, 24, "useAudioRecorderState"], [39, 60, 44, 45], [39, 62, 44, 46, "recorder"], [39, 70, 44, 54], [39, 71, 44, 55], [40, 4, 46, 2], [40, 8, 46, 2, "_useState"], [40, 17, 46, 2], [40, 20, 46, 34], [40, 24, 46, 34, "useState"], [40, 39, 46, 42], [40, 41, 46, 43], [40, 43, 46, 45], [40, 44, 46, 46], [41, 6, 46, 46, "_useState2"], [41, 16, 46, 46], [41, 23, 46, 46, "_slicedToArray2"], [41, 38, 46, 46], [41, 39, 46, 46, "default"], [41, 46, 46, 46], [41, 48, 46, 46, "_useState"], [41, 57, 46, 46], [42, 6, 46, 9, "messages"], [42, 14, 46, 17], [42, 17, 46, 17, "_useState2"], [42, 27, 46, 17], [43, 6, 46, 19, "setMessages"], [43, 17, 46, 30], [43, 20, 46, 30, "_useState2"], [43, 30, 46, 30], [44, 4, 47, 2], [44, 8, 47, 2, "_useState3"], [44, 18, 47, 2], [44, 21, 47, 36], [44, 25, 47, 36, "useState"], [44, 40, 47, 44], [44, 42, 47, 45], [44, 44, 47, 47], [44, 45, 47, 48], [45, 6, 47, 48, "_useState4"], [45, 16, 47, 48], [45, 23, 47, 48, "_slicedToArray2"], [45, 38, 47, 48], [45, 39, 47, 48, "default"], [45, 46, 47, 48], [45, 48, 47, 48, "_useState3"], [45, 58, 47, 48], [46, 6, 47, 9, "inputText"], [46, 15, 47, 18], [46, 18, 47, 18, "_useState4"], [46, 28, 47, 18], [47, 6, 47, 20, "setInputText"], [47, 18, 47, 32], [47, 21, 47, 32, "_useState4"], [47, 31, 47, 32], [48, 4, 48, 2], [48, 8, 48, 2, "_useState5"], [48, 18, 48, 2], [48, 21, 48, 40], [48, 25, 48, 40, "useState"], [48, 40, 48, 48], [48, 42, 48, 49], [48, 46, 48, 53], [48, 47, 48, 54], [49, 6, 48, 54, "_useState6"], [49, 16, 48, 54], [49, 23, 48, 54, "_slicedToArray2"], [49, 38, 48, 54], [49, 39, 48, 54, "default"], [49, 46, 48, 54], [49, 48, 48, 54, "_useState5"], [49, 58, 48, 54], [50, 6, 48, 9, "isFirstTime"], [50, 17, 48, 20], [50, 20, 48, 20, "_useState6"], [50, 30, 48, 20], [51, 6, 48, 22, "setIsFirstTime"], [51, 20, 48, 36], [51, 23, 48, 36, "_useState6"], [51, 33, 48, 36], [52, 4, 49, 2], [52, 8, 49, 2, "_useState7"], [52, 18, 49, 2], [52, 21, 49, 36], [52, 25, 49, 36, "useState"], [52, 40, 49, 44], [52, 42, 49, 45], [52, 47, 49, 50], [52, 48, 49, 51], [53, 6, 49, 51, "_useState8"], [53, 16, 49, 51], [53, 23, 49, 51, "_slicedToArray2"], [53, 38, 49, 51], [53, 39, 49, 51, "default"], [53, 46, 49, 51], [53, 48, 49, 51, "_useState7"], [53, 58, 49, 51], [54, 6, 49, 9, "isLoading"], [54, 15, 49, 18], [54, 18, 49, 18, "_useState8"], [54, 28, 49, 18], [55, 6, 49, 20, "setIsLoading"], [55, 18, 49, 32], [55, 21, 49, 32, "_useState8"], [55, 31, 49, 32], [56, 4, 50, 2], [56, 8, 50, 2, "_useState9"], [56, 18, 50, 2], [56, 21, 50, 42], [56, 25, 50, 42, "useState"], [56, 40, 50, 50], [56, 42, 50, 51], [56, 44, 50, 53], [56, 45, 50, 54], [57, 6, 50, 54, "_useState0"], [57, 16, 50, 54], [57, 23, 50, 54, "_slicedToArray2"], [57, 38, 50, 54], [57, 39, 50, 54, "default"], [57, 46, 50, 54], [57, 48, 50, 54, "_useState9"], [57, 58, 50, 54], [58, 6, 50, 9, "quickActions"], [58, 18, 50, 21], [58, 21, 50, 21, "_useState0"], [58, 31, 50, 21], [59, 6, 50, 23, "setQuickActions"], [59, 21, 50, 38], [59, 24, 50, 38, "_useState0"], [59, 34, 50, 38], [60, 4, 51, 2], [60, 8, 51, 2, "_useState1"], [60, 18, 51, 2], [60, 21, 51, 36], [60, 25, 51, 36, "useState"], [60, 40, 51, 44], [60, 42, 51, 45], [60, 46, 51, 49], [60, 47, 51, 50], [61, 6, 51, 50, "_useState10"], [61, 17, 51, 50], [61, 24, 51, 50, "_slicedToArray2"], [61, 39, 51, 50], [61, 40, 51, 50, "default"], [61, 47, 51, 50], [61, 49, 51, 50, "_useState1"], [61, 59, 51, 50], [62, 6, 51, 9, "voiceMode"], [62, 15, 51, 18], [62, 18, 51, 18, "_useState10"], [62, 29, 51, 18], [63, 6, 51, 20, "setVoiceMode"], [63, 18, 51, 32], [63, 21, 51, 32, "_useState10"], [63, 32, 51, 32], [63, 36, 51, 51], [63, 37, 51, 52], [64, 4, 52, 2], [64, 8, 52, 2, "_useState11"], [64, 19, 52, 2], [64, 22, 52, 44], [64, 26, 52, 44, "useState"], [64, 41, 52, 52], [64, 43, 52, 53], [64, 48, 52, 58], [64, 49, 52, 59], [65, 6, 52, 59, "_useState12"], [65, 17, 52, 59], [65, 24, 52, 59, "_slicedToArray2"], [65, 39, 52, 59], [65, 40, 52, 59, "default"], [65, 47, 52, 59], [65, 49, 52, 59, "_useState11"], [65, 60, 52, 59], [66, 6, 52, 9, "hasPermission"], [66, 19, 52, 22], [66, 22, 52, 22, "_useState12"], [66, 33, 52, 22], [67, 6, 52, 24, "setHasPermission"], [67, 22, 52, 40], [67, 25, 52, 40, "_useState12"], [67, 36, 52, 40], [68, 4, 53, 2], [68, 8, 53, 2, "_useState13"], [68, 19, 53, 2], [68, 22, 53, 40], [68, 26, 53, 40, "useState"], [68, 41, 53, 48], [68, 43, 53, 49], [68, 48, 53, 54], [68, 49, 53, 55], [69, 6, 53, 55, "_useState14"], [69, 17, 53, 55], [69, 24, 53, 55, "_slicedToArray2"], [69, 39, 53, 55], [69, 40, 53, 55, "default"], [69, 47, 53, 55], [69, 49, 53, 55, "_useState13"], [69, 60, 53, 55], [70, 6, 53, 9, "isDictating"], [70, 17, 53, 20], [70, 20, 53, 20, "_useState14"], [70, 31, 53, 20], [71, 6, 53, 22, "setIsDictating"], [71, 20, 53, 36], [71, 23, 53, 36, "_useState14"], [71, 34, 53, 36], [72, 4, 54, 2], [72, 8, 54, 2, "_useState15"], [72, 19, 54, 2], [72, 22, 54, 54], [72, 26, 54, 54, "useState"], [72, 41, 54, 62], [72, 43, 54, 63], [72, 47, 54, 67], [72, 48, 54, 68], [73, 6, 54, 68, "_useState16"], [73, 17, 54, 68], [73, 24, 54, 68, "_slicedToArray2"], [73, 39, 54, 68], [73, 40, 54, 68, "default"], [73, 47, 54, 68], [73, 49, 54, 68, "_useState15"], [73, 60, 54, 68], [74, 6, 54, 9, "longPressedMessage"], [74, 24, 54, 27], [74, 27, 54, 27, "_useState16"], [74, 38, 54, 27], [75, 6, 54, 29, "setLongPressedMessage"], [75, 27, 54, 50], [75, 30, 54, 50, "_useState16"], [75, 41, 54, 50], [76, 4, 55, 2], [76, 8, 55, 2, "_useState17"], [76, 19, 55, 2], [76, 22, 55, 58], [76, 26, 55, 58, "useState"], [76, 41, 55, 66], [76, 43, 55, 67], [76, 48, 55, 72], [76, 49, 55, 73], [77, 6, 55, 73, "_useState18"], [77, 17, 55, 73], [77, 24, 55, 73, "_slicedToArray2"], [77, 39, 55, 73], [77, 40, 55, 73, "default"], [77, 47, 55, 73], [77, 49, 55, 73, "_useState17"], [77, 60, 55, 73], [78, 6, 55, 9, "isContextMenuVisible"], [78, 26, 55, 29], [78, 29, 55, 29, "_useState18"], [78, 40, 55, 29], [79, 6, 55, 31, "setIsContextMenuVisible"], [79, 29, 55, 54], [79, 32, 55, 54, "_useState18"], [79, 43, 55, 54], [80, 4, 56, 2], [80, 8, 56, 2, "_useState19"], [80, 19, 56, 2], [80, 22, 56, 38], [80, 26, 56, 38, "useState"], [80, 41, 56, 46], [80, 43, 56, 47], [80, 45, 56, 49], [80, 46, 56, 50], [81, 6, 56, 50, "_useState20"], [81, 17, 56, 50], [81, 24, 56, 50, "_slicedToArray2"], [81, 39, 56, 50], [81, 40, 56, 50, "default"], [81, 47, 56, 50], [81, 49, 56, 50, "_useState19"], [81, 60, 56, 50], [82, 6, 56, 9, "transcript"], [82, 16, 56, 19], [82, 19, 56, 19, "_useState20"], [82, 30, 56, 19], [83, 6, 56, 21, "setTranscript"], [83, 19, 56, 34], [83, 22, 56, 34, "_useState20"], [83, 33, 56, 34], [84, 4, 57, 2], [84, 8, 57, 2, "_useState21"], [84, 19, 57, 2], [84, 22, 57, 32], [84, 26, 57, 32, "useState"], [84, 41, 57, 40], [84, 43, 57, 41], [84, 48, 57, 46], [84, 49, 57, 47], [85, 6, 57, 47, "_useState22"], [85, 17, 57, 47], [85, 24, 57, 47, "_slicedToArray2"], [85, 39, 57, 47], [85, 40, 57, 47, "default"], [85, 47, 57, 47], [85, 49, 57, 47, "_useState21"], [85, 60, 57, 47], [86, 6, 57, 9, "isMuted"], [86, 13, 57, 16], [86, 16, 57, 16, "_useState22"], [86, 27, 57, 16], [87, 6, 57, 18, "setIsMuted"], [87, 16, 57, 28], [87, 19, 57, 28, "_useState22"], [87, 30, 57, 28], [88, 4, 59, 2], [88, 8, 59, 8, "scrollViewRef"], [88, 21, 59, 21], [88, 24, 59, 24], [88, 28, 59, 24, "useRef"], [88, 41, 59, 30], [88, 43, 59, 31], [88, 47, 59, 35], [88, 48, 59, 36], [90, 4, 61, 2], [91, 4, 62, 2], [91, 8, 62, 2, "useEffect"], [91, 24, 62, 11], [91, 26, 62, 12], [91, 32, 62, 18], [92, 6, 63, 4], [92, 10, 63, 4, "_asyncToGenerator2"], [92, 28, 63, 4], [92, 29, 63, 4, "default"], [92, 36, 63, 4], [92, 38, 63, 5], [92, 51, 63, 17], [93, 8, 64, 6], [93, 12, 64, 6, "_yield$requestRecordi"], [93, 33, 64, 6], [93, 42, 64, 32], [93, 46, 64, 32, "requestRecordingPermissionsAsync"], [93, 89, 64, 64], [93, 91, 64, 65], [93, 92, 64, 66], [94, 10, 64, 14, "granted"], [94, 17, 64, 21], [94, 20, 64, 21, "_yield$requestRecordi"], [94, 41, 64, 21], [94, 42, 64, 14, "granted"], [94, 49, 64, 21], [95, 8, 65, 6, "setHasPermission"], [95, 24, 65, 22], [95, 25, 65, 23, "granted"], [95, 32, 65, 30], [95, 33, 65, 31], [96, 8, 66, 6], [96, 12, 66, 10], [96, 13, 66, 11, "granted"], [96, 20, 66, 18], [96, 24, 66, 22, "voiceMode"], [96, 33, 66, 31], [96, 35, 66, 33], [97, 10, 67, 8, "<PERSON><PERSON>"], [97, 28, 67, 13], [97, 29, 67, 14, "alert"], [97, 34, 67, 19], [97, 35, 68, 10], [97, 56, 68, 31], [97, 58, 69, 10], [97, 137, 69, 89], [97, 139, 70, 10], [97, 140, 71, 12], [98, 12, 71, 14, "text"], [98, 16, 71, 18], [98, 18, 71, 20], [98, 33, 71, 35], [99, 12, 71, 37, "onPress"], [99, 19, 71, 44], [99, 21, 71, 46, "onPress"], [99, 22, 71, 46], [99, 27, 71, 52, "setVoiceMode"], [99, 39, 71, 64], [99, 40, 71, 65], [99, 45, 71, 70], [100, 10, 71, 72], [100, 11, 71, 73], [100, 13, 72, 12], [101, 12, 72, 14, "text"], [101, 16, 72, 18], [101, 18, 72, 20], [102, 10, 72, 25], [102, 11, 72, 26], [102, 12, 74, 8], [102, 13, 74, 9], [103, 8, 75, 6], [104, 6, 76, 4], [104, 7, 76, 5], [104, 9, 76, 7], [104, 10, 76, 8], [105, 4, 77, 2], [105, 5, 77, 3], [105, 7, 77, 5], [105, 8, 77, 6, "voiceMode"], [105, 17, 77, 15], [105, 18, 77, 16], [105, 19, 77, 17], [106, 4, 79, 2], [106, 8, 79, 8, "handleStartBrainstorming"], [106, 32, 79, 32], [106, 35, 79, 35, "handleStartBrainstorming"], [106, 36, 79, 35], [106, 41, 79, 41], [107, 6, 80, 4, "Haptics"], [107, 13, 80, 11], [107, 14, 80, 12, "impactAsync"], [107, 25, 80, 23], [107, 26, 80, 24, "Haptics"], [107, 33, 80, 31], [107, 34, 80, 32, "ImpactFeedbackStyle"], [107, 53, 80, 51], [107, 54, 80, 52, "Medium"], [107, 60, 80, 58], [107, 61, 80, 59], [108, 6, 81, 4, "setIsFirstTime"], [108, 20, 81, 18], [108, 21, 81, 19], [108, 26, 81, 24], [108, 27, 81, 25], [109, 6, 82, 4, "setMessages"], [109, 17, 82, 15], [109, 18, 82, 16, "fakeMessages"], [109, 40, 82, 28], [109, 41, 82, 29], [110, 6, 83, 4, "setQuickActions"], [110, 21, 83, 19], [110, 22, 83, 20, "fakeQuickActions"], [110, 48, 83, 36], [110, 49, 83, 37], [111, 4, 84, 2], [111, 5, 84, 3], [112, 4, 86, 2], [112, 8, 86, 8, "handleSendMessage"], [112, 25, 86, 25], [112, 28, 86, 28], [112, 37, 86, 28, "handleSendMessage"], [112, 38, 86, 28], [112, 40, 86, 53], [113, 6, 86, 53], [113, 10, 86, 29, "message"], [113, 17, 86, 36], [113, 20, 86, 36, "arguments"], [113, 29, 86, 36], [113, 30, 86, 36, "length"], [113, 36, 86, 36], [113, 44, 86, 36, "arguments"], [113, 53, 86, 36], [113, 61, 86, 36, "undefined"], [113, 70, 86, 36], [113, 73, 86, 36, "arguments"], [113, 82, 86, 36], [113, 88, 86, 39, "inputText"], [113, 97, 86, 48], [114, 6, 87, 4], [114, 10, 87, 8], [114, 11, 87, 9, "message"], [114, 18, 87, 16], [114, 19, 87, 17, "trim"], [114, 23, 87, 21], [114, 24, 87, 22], [114, 25, 87, 23], [114, 27, 87, 25], [115, 6, 89, 4], [115, 10, 89, 10, "newUserMessage"], [115, 24, 89, 24], [115, 27, 89, 27], [116, 8, 89, 29, "role"], [116, 12, 89, 33], [116, 14, 89, 35], [116, 20, 89, 41], [117, 8, 89, 43, "content"], [117, 15, 89, 50], [117, 17, 89, 52, "message"], [118, 6, 89, 60], [118, 7, 89, 61], [119, 6, 90, 4, "setMessages"], [119, 17, 90, 15], [119, 18, 90, 17, "prev"], [119, 22, 90, 21], [119, 26, 90, 26], [119, 27, 90, 27], [119, 30, 90, 30, "prev"], [119, 34, 90, 34], [119, 36, 90, 36, "newUserMessage"], [119, 50, 90, 50], [119, 51, 90, 51], [119, 52, 90, 52], [120, 6, 91, 4, "setInputText"], [120, 18, 91, 16], [120, 19, 91, 17], [120, 21, 91, 19], [120, 22, 91, 20], [121, 6, 92, 4, "setQuickActions"], [121, 21, 92, 19], [121, 22, 92, 20], [121, 24, 92, 22], [121, 25, 92, 23], [122, 6, 93, 4, "setIsLoading"], [122, 18, 93, 16], [122, 19, 93, 17], [122, 23, 93, 21], [122, 24, 93, 22], [124, 6, 95, 4], [125, 6, 96, 4, "setTimeout"], [125, 16, 96, 14], [125, 17, 96, 15], [125, 23, 96, 21], [126, 8, 97, 6], [126, 12, 97, 12, "aiResponse"], [126, 22, 97, 22], [126, 25, 97, 25], [127, 10, 98, 8, "role"], [127, 14, 98, 12], [127, 16, 98, 14], [127, 27, 98, 25], [128, 10, 99, 8, "content"], [128, 17, 99, 15], [128, 19, 99, 17], [129, 8, 100, 6], [129, 9, 100, 7], [130, 8, 101, 6, "setMessages"], [130, 19, 101, 17], [130, 20, 101, 19, "prev"], [130, 24, 101, 23], [130, 28, 101, 28], [130, 29, 101, 29], [130, 32, 101, 32, "prev"], [130, 36, 101, 36], [130, 38, 101, 38, "aiResponse"], [130, 48, 101, 48], [130, 49, 101, 49], [130, 50, 101, 50], [131, 8, 102, 6, "setQuickActions"], [131, 23, 102, 21], [131, 24, 102, 22, "fakeQuickActions"], [131, 50, 102, 38], [131, 51, 102, 39], [132, 8, 103, 6, "setIsLoading"], [132, 20, 103, 18], [132, 21, 103, 19], [132, 26, 103, 24], [132, 27, 103, 25], [133, 6, 104, 4], [133, 7, 104, 5], [133, 9, 104, 7], [133, 13, 104, 11], [133, 14, 104, 12], [134, 4, 105, 2], [134, 5, 105, 3], [135, 4, 107, 2], [135, 8, 107, 8, "handleQuickAction"], [135, 25, 107, 25], [135, 28, 107, 29, "action"], [135, 34, 107, 35], [135, 38, 107, 40], [136, 6, 108, 4, "Haptics"], [136, 13, 108, 11], [136, 14, 108, 12, "impactAsync"], [136, 25, 108, 23], [136, 26, 108, 24, "Haptics"], [136, 33, 108, 31], [136, 34, 108, 32, "ImpactFeedbackStyle"], [136, 53, 108, 51], [136, 54, 108, 52, "Light"], [136, 59, 108, 57], [136, 60, 108, 58], [137, 6, 109, 4, "handleSendMessage"], [137, 23, 109, 21], [137, 24, 109, 22, "action"], [137, 30, 109, 28], [137, 31, 109, 29], [138, 4, 110, 2], [138, 5, 110, 3], [139, 4, 112, 2], [139, 8, 112, 8, "toggleVoiceMode"], [139, 23, 112, 23], [139, 26, 112, 26, "toggleVoiceMode"], [139, 27, 112, 26], [139, 32, 112, 32], [140, 6, 113, 4], [140, 10, 113, 8], [140, 11, 113, 9, "voiceMode"], [140, 20, 113, 18], [140, 24, 113, 22], [140, 25, 113, 23, "hasPermission"], [140, 38, 113, 36], [140, 40, 113, 38], [141, 8, 114, 6, "<PERSON><PERSON>"], [141, 26, 114, 11], [141, 27, 114, 12, "alert"], [141, 32, 114, 17], [141, 33, 115, 8], [141, 54, 115, 29], [141, 56, 116, 8], [141, 106, 116, 58], [141, 108, 117, 8], [141, 109, 118, 10], [142, 10, 118, 12, "text"], [142, 14, 118, 16], [142, 16, 118, 18], [143, 8, 118, 27], [143, 9, 118, 28], [143, 11, 119, 10], [144, 10, 120, 12, "text"], [144, 14, 120, 16], [144, 16, 120, 18], [144, 34, 120, 36], [145, 10, 121, 12, "onPress"], [145, 17, 121, 19], [146, 12, 121, 19], [146, 16, 121, 19, "_ref2"], [146, 21, 121, 19], [146, 28, 121, 19, "_asyncToGenerator2"], [146, 46, 121, 19], [146, 47, 121, 19, "default"], [146, 54, 121, 19], [146, 56, 121, 21], [146, 69, 121, 33], [147, 14, 122, 14], [147, 18, 122, 14, "_yield$requestRecordi2"], [147, 40, 122, 14], [147, 49, 122, 40], [147, 53, 122, 40, "requestRecordingPermissionsAsync"], [147, 96, 122, 72], [147, 98, 122, 73], [147, 99, 122, 74], [148, 16, 122, 22, "granted"], [148, 23, 122, 29], [148, 26, 122, 29, "_yield$requestRecordi2"], [148, 48, 122, 29], [148, 49, 122, 22, "granted"], [148, 56, 122, 29], [149, 14, 123, 14], [149, 18, 123, 18, "granted"], [149, 25, 123, 25], [149, 27, 123, 27], [150, 16, 124, 16, "setHasPermission"], [150, 32, 124, 32], [150, 33, 124, 33], [150, 37, 124, 37], [150, 38, 124, 38], [151, 16, 125, 16, "setVoiceMode"], [151, 28, 125, 28], [151, 29, 125, 29], [151, 33, 125, 33], [151, 34, 125, 34], [152, 14, 126, 14], [153, 12, 127, 12], [153, 13, 127, 13], [154, 12, 127, 13], [154, 28, 121, 12, "onPress"], [154, 35, 121, 19, "onPress"], [154, 36, 121, 19], [155, 14, 121, 19], [155, 21, 121, 19, "_ref2"], [155, 26, 121, 19], [155, 27, 121, 19, "apply"], [155, 32, 121, 19], [155, 39, 121, 19, "arguments"], [155, 48, 121, 19], [156, 12, 121, 19], [157, 10, 121, 19], [158, 8, 128, 10], [158, 9, 128, 11], [158, 10, 130, 6], [158, 11, 130, 7], [159, 8, 131, 6], [160, 6, 132, 4], [161, 6, 134, 4, "Haptics"], [161, 13, 134, 11], [161, 14, 134, 12, "impactAsync"], [161, 25, 134, 23], [161, 26, 134, 24, "Haptics"], [161, 33, 134, 31], [161, 34, 134, 32, "ImpactFeedbackStyle"], [161, 53, 134, 51], [161, 54, 134, 52, "Light"], [161, 59, 134, 57], [161, 60, 134, 58], [162, 6, 135, 4, "setVoiceMode"], [162, 18, 135, 16], [162, 19, 135, 17], [162, 20, 135, 18, "voiceMode"], [162, 29, 135, 27], [162, 30, 135, 28], [163, 4, 136, 2], [163, 5, 136, 3], [164, 4, 138, 2], [164, 8, 138, 8, "handleVoiceRecord"], [164, 25, 138, 25], [165, 6, 138, 25], [165, 10, 138, 25, "_ref3"], [165, 15, 138, 25], [165, 22, 138, 25, "_asyncToGenerator2"], [165, 40, 138, 25], [165, 41, 138, 25, "default"], [165, 48, 138, 25], [165, 50, 138, 28], [165, 63, 138, 40], [166, 8, 139, 4], [166, 12, 139, 8], [166, 13, 139, 9, "hasPermission"], [166, 26, 139, 22], [166, 28, 139, 24], [167, 10, 140, 6, "<PERSON><PERSON>"], [167, 28, 140, 11], [167, 29, 140, 12, "alert"], [167, 34, 140, 17], [167, 35, 141, 8], [167, 56, 141, 29], [167, 58, 142, 8], [167, 108, 143, 6], [167, 109, 143, 7], [168, 10, 144, 6], [169, 8, 145, 4], [170, 8, 147, 4], [170, 12, 147, 8], [171, 10, 148, 6], [171, 14, 148, 10, "recorderState"], [171, 27, 148, 23], [171, 28, 148, 24, "isRecording"], [171, 39, 148, 35], [171, 41, 148, 37], [172, 12, 149, 8], [172, 18, 149, 14, "recorder"], [172, 26, 149, 22], [172, 27, 149, 23, "stop"], [172, 31, 149, 27], [172, 32, 149, 28], [172, 33, 149, 29], [174, 12, 151, 8], [175, 12, 152, 8], [176, 12, 153, 8], [176, 16, 153, 14, "mockTranscript"], [176, 30, 153, 28], [176, 33, 153, 31], [176, 58, 153, 56], [176, 59, 153, 57], [176, 60, 153, 58], [177, 12, 154, 8, "setTranscript"], [177, 25, 154, 21], [177, 26, 154, 23, "prev"], [177, 30, 154, 27], [177, 34, 154, 32], [177, 35, 154, 33], [177, 38, 154, 36, "prev"], [177, 42, 154, 40], [177, 44, 154, 42], [178, 14, 154, 44, "speaker"], [178, 21, 154, 51], [178, 23, 154, 53], [178, 29, 154, 59], [179, 14, 154, 61, "text"], [179, 18, 154, 65], [179, 20, 154, 67, "mockTranscript"], [180, 12, 154, 82], [180, 13, 154, 83], [180, 14, 154, 84], [180, 15, 154, 85], [181, 12, 155, 8, "handleSendMessage"], [181, 29, 155, 25], [181, 30, 155, 26, "mockTranscript"], [181, 44, 155, 40], [181, 45, 155, 41], [182, 10, 156, 6], [182, 11, 156, 7], [182, 17, 156, 13], [183, 12, 157, 8], [183, 18, 157, 14, "recorder"], [183, 26, 157, 22], [183, 27, 157, 23, "prepareToRecordAsync"], [183, 47, 157, 43], [183, 48, 157, 44], [183, 49, 157, 45], [184, 12, 158, 8, "recorder"], [184, 20, 158, 16], [184, 21, 158, 17, "record"], [184, 27, 158, 23], [184, 28, 158, 24], [184, 29, 158, 25], [185, 12, 159, 8, "Haptics"], [185, 19, 159, 15], [185, 20, 159, 16, "impactAsync"], [185, 31, 159, 27], [185, 32, 159, 28, "Haptics"], [185, 39, 159, 35], [185, 40, 159, 36, "ImpactFeedbackStyle"], [185, 59, 159, 55], [185, 60, 159, 56, "Medium"], [185, 66, 159, 62], [185, 67, 159, 63], [186, 10, 160, 6], [187, 8, 161, 4], [187, 9, 161, 5], [187, 10, 161, 6], [187, 17, 161, 13, "error"], [187, 22, 161, 18], [187, 24, 161, 20], [188, 10, 162, 6, "console"], [188, 17, 162, 13], [188, 18, 162, 14, "error"], [188, 23, 162, 19], [188, 24, 162, 20], [188, 53, 162, 49], [188, 55, 162, 51, "error"], [188, 60, 162, 56], [188, 61, 162, 57], [189, 10, 163, 6, "<PERSON><PERSON>"], [189, 28, 163, 11], [189, 29, 163, 12, "alert"], [189, 34, 163, 17], [189, 35, 163, 18], [189, 42, 163, 25], [189, 44, 163, 27], [189, 76, 163, 59], [189, 77, 163, 60], [190, 8, 164, 4], [191, 6, 165, 2], [191, 7, 165, 3], [192, 6, 165, 3], [192, 22, 138, 8, "handleVoiceRecord"], [192, 39, 138, 25, "handleVoiceRecord"], [192, 40, 138, 25], [193, 8, 138, 25], [193, 15, 138, 25, "_ref3"], [193, 20, 138, 25], [193, 21, 138, 25, "apply"], [193, 26, 138, 25], [193, 33, 138, 25, "arguments"], [193, 42, 138, 25], [194, 6, 138, 25], [195, 4, 138, 25], [195, 7, 165, 3], [196, 4, 167, 2], [196, 8, 167, 8, "handleDictation"], [196, 23, 167, 23], [197, 6, 167, 23], [197, 10, 167, 23, "_ref4"], [197, 15, 167, 23], [197, 22, 167, 23, "_asyncToGenerator2"], [197, 40, 167, 23], [197, 41, 167, 23, "default"], [197, 48, 167, 23], [197, 50, 167, 26], [197, 63, 167, 38], [198, 8, 168, 4], [198, 12, 168, 8], [198, 13, 168, 9, "hasPermission"], [198, 26, 168, 22], [198, 28, 168, 24], [199, 10, 169, 6, "<PERSON><PERSON>"], [199, 28, 169, 11], [199, 29, 169, 12, "alert"], [199, 34, 169, 17], [199, 35, 170, 8], [199, 56, 170, 29], [199, 58, 171, 8], [199, 108, 172, 6], [199, 109, 172, 7], [200, 10, 173, 6], [201, 8, 174, 4], [202, 8, 176, 4], [202, 12, 176, 8], [203, 10, 177, 6], [203, 14, 177, 10, "isDictating"], [203, 25, 177, 21], [203, 27, 177, 23], [204, 12, 178, 8], [204, 18, 178, 14, "recorder"], [204, 26, 178, 22], [204, 27, 178, 23, "stop"], [204, 31, 178, 27], [204, 32, 178, 28], [204, 33, 178, 29], [205, 12, 179, 8], [205, 16, 179, 14, "mockTranscript"], [205, 30, 179, 28], [205, 33, 179, 31], [205, 62, 179, 60], [205, 63, 179, 61], [205, 64, 179, 62], [206, 12, 180, 8, "setInputText"], [206, 24, 180, 20], [206, 25, 180, 21, "inputText"], [206, 34, 180, 30], [206, 37, 180, 33, "mockTranscript"], [206, 51, 180, 47], [206, 52, 180, 48], [207, 12, 181, 8, "setIsDictating"], [207, 26, 181, 22], [207, 27, 181, 23], [207, 32, 181, 28], [207, 33, 181, 29], [208, 10, 182, 6], [208, 11, 182, 7], [208, 17, 182, 13], [209, 12, 183, 8], [209, 18, 183, 14, "recorder"], [209, 26, 183, 22], [209, 27, 183, 23, "prepareToRecordAsync"], [209, 47, 183, 43], [209, 48, 183, 44], [209, 49, 183, 45], [210, 12, 184, 8, "recorder"], [210, 20, 184, 16], [210, 21, 184, 17, "record"], [210, 27, 184, 23], [210, 28, 184, 24], [210, 29, 184, 25], [211, 12, 185, 8, "setIsDictating"], [211, 26, 185, 22], [211, 27, 185, 23], [211, 31, 185, 27], [211, 32, 185, 28], [212, 12, 186, 8, "Haptics"], [212, 19, 186, 15], [212, 20, 186, 16, "impactAsync"], [212, 31, 186, 27], [212, 32, 186, 28, "Haptics"], [212, 39, 186, 35], [212, 40, 186, 36, "ImpactFeedbackStyle"], [212, 59, 186, 55], [212, 60, 186, 56, "Medium"], [212, 66, 186, 62], [212, 67, 186, 63], [213, 10, 187, 6], [214, 8, 188, 4], [214, 9, 188, 5], [214, 10, 188, 6], [214, 17, 188, 13, "error"], [214, 22, 188, 18], [214, 24, 188, 20], [215, 10, 189, 6, "console"], [215, 17, 189, 13], [215, 18, 189, 14, "error"], [215, 23, 189, 19], [215, 24, 189, 20], [215, 47, 189, 43], [215, 49, 189, 45, "error"], [215, 54, 189, 50], [215, 55, 189, 51], [216, 10, 190, 6, "<PERSON><PERSON>"], [216, 28, 190, 11], [216, 29, 190, 12, "alert"], [216, 34, 190, 17], [216, 35, 190, 18], [216, 42, 190, 25], [216, 44, 190, 27], [216, 71, 190, 54], [216, 72, 190, 55], [217, 10, 191, 6, "setIsDictating"], [217, 24, 191, 20], [217, 25, 191, 21], [217, 30, 191, 26], [217, 31, 191, 27], [218, 8, 192, 4], [219, 6, 193, 2], [219, 7, 193, 3], [220, 6, 193, 3], [220, 22, 167, 8, "handleDictation"], [220, 37, 167, 23, "handleDictation"], [220, 38, 167, 23], [221, 8, 167, 23], [221, 15, 167, 23, "_ref4"], [221, 20, 167, 23], [221, 21, 167, 23, "apply"], [221, 26, 167, 23], [221, 33, 167, 23, "arguments"], [221, 42, 167, 23], [222, 6, 167, 23], [223, 4, 167, 23], [223, 7, 193, 3], [224, 4, 195, 2], [224, 8, 195, 8, "handleLongPress"], [224, 23, 195, 23], [224, 26, 195, 27, "message"], [224, 33, 195, 34], [224, 37, 195, 39], [225, 6, 196, 4, "setLongPressedMessage"], [225, 27, 196, 25], [225, 28, 196, 26, "message"], [225, 35, 196, 33], [225, 36, 196, 34], [226, 6, 197, 4, "setIsContextMenuVisible"], [226, 29, 197, 27], [226, 30, 197, 28], [226, 34, 197, 32], [226, 35, 197, 33], [227, 4, 198, 2], [227, 5, 198, 3], [228, 4, 200, 2], [228, 8, 200, 8, "handleCopyMessage"], [228, 25, 200, 25], [228, 28, 200, 28, "handleCopyMessage"], [228, 29, 200, 28], [228, 34, 200, 34], [229, 6, 201, 4], [229, 10, 201, 8, "longPressedMessage"], [229, 28, 201, 26], [229, 30, 201, 28], [230, 8, 202, 6, "Clipboard"], [230, 30, 202, 15], [230, 31, 202, 16, "setString"], [230, 40, 202, 25], [230, 41, 202, 26, "longPressedMessage"], [230, 59, 202, 44], [230, 60, 202, 45, "content"], [230, 67, 202, 52], [230, 68, 202, 53], [231, 8, 203, 6, "setIsContextMenuVisible"], [231, 31, 203, 29], [231, 32, 203, 30], [231, 37, 203, 35], [231, 38, 203, 36], [232, 8, 204, 6, "setLongPressedMessage"], [232, 29, 204, 27], [232, 30, 204, 28], [232, 34, 204, 32], [232, 35, 204, 33], [233, 6, 205, 4], [234, 4, 206, 2], [234, 5, 206, 3], [235, 4, 208, 2], [235, 8, 208, 8, "handleListenToMessage"], [235, 29, 208, 29], [235, 32, 208, 32, "handleListenToMessage"], [235, 33, 208, 32], [235, 38, 208, 38], [236, 6, 209, 4], [236, 10, 209, 8, "longPressedMessage"], [236, 28, 209, 26], [236, 30, 209, 28], [237, 8, 210, 6], [238, 8, 211, 6, "<PERSON><PERSON>"], [238, 26, 211, 11], [238, 27, 211, 12, "alert"], [238, 32, 211, 17], [238, 33, 211, 18], [238, 55, 211, 40], [238, 57, 211, 42, "longPressedMessage"], [238, 75, 211, 60], [238, 76, 211, 61, "content"], [238, 83, 211, 68], [238, 84, 211, 69], [239, 8, 212, 6, "setIsContextMenuVisible"], [239, 31, 212, 29], [239, 32, 212, 30], [239, 37, 212, 35], [239, 38, 212, 36], [240, 8, 213, 6, "setLongPressedMessage"], [240, 29, 213, 27], [240, 30, 213, 28], [240, 34, 213, 32], [240, 35, 213, 33], [241, 6, 214, 4], [242, 4, 215, 2], [242, 5, 215, 3], [243, 4, 217, 2], [243, 8, 217, 8, "handleMute"], [243, 18, 217, 18], [243, 21, 217, 21, "handleMute"], [243, 22, 217, 21], [243, 27, 217, 27], [244, 6, 218, 4, "setIsMuted"], [244, 16, 218, 14], [244, 17, 218, 15], [244, 18, 218, 16, "isMuted"], [244, 25, 218, 23], [244, 26, 218, 24], [245, 4, 219, 2], [245, 5, 219, 3], [247, 4, 222, 2], [248, 4, 223, 2], [248, 8, 223, 2, "useEffect"], [248, 24, 223, 11], [248, 26, 223, 12], [248, 32, 223, 18], [249, 6, 224, 4], [249, 10, 224, 8, "scrollViewRef"], [249, 23, 224, 21], [249, 24, 224, 22, "current"], [249, 31, 224, 29], [249, 33, 224, 31], [250, 8, 225, 6, "scrollViewRef"], [250, 21, 225, 19], [250, 22, 225, 20, "current"], [250, 29, 225, 27], [250, 30, 225, 28, "scrollToEnd"], [250, 41, 225, 39], [250, 42, 225, 40], [251, 10, 225, 42, "animated"], [251, 18, 225, 50], [251, 20, 225, 52], [252, 8, 225, 57], [252, 9, 225, 58], [252, 10, 225, 59], [253, 6, 226, 4], [254, 4, 227, 2], [254, 5, 227, 3], [254, 7, 227, 5], [254, 8, 227, 6, "messages"], [254, 16, 227, 14], [254, 17, 227, 15], [254, 18, 227, 16], [255, 4, 229, 2], [255, 8, 229, 6], [255, 9, 229, 7, "fontsLoaded"], [255, 20, 229, 18], [255, 22, 229, 20], [256, 6, 230, 4], [256, 13, 230, 11], [256, 17, 230, 15], [257, 4, 231, 2], [259, 4, 233, 2], [260, 4, 234, 2], [260, 8, 234, 6, "isFirstTime"], [260, 19, 234, 17], [260, 21, 234, 19], [261, 6, 235, 4], [261, 26, 236, 6], [261, 30, 236, 6, "_jsxDevRuntime"], [261, 44, 236, 6], [261, 45, 236, 6, "jsxDEV"], [261, 51, 236, 6], [261, 53, 236, 7, "_reactNative"], [261, 65, 236, 7], [261, 66, 236, 7, "View"], [261, 70, 236, 11], [262, 8, 236, 12, "style"], [262, 13, 236, 17], [262, 15, 236, 19], [263, 10, 236, 21, "flex"], [263, 14, 236, 25], [263, 16, 236, 27], [263, 17, 236, 28], [264, 10, 236, 30, "backgroundColor"], [264, 25, 236, 45], [264, 27, 236, 47, "colors"], [264, 33, 236, 53], [264, 34, 236, 54, "background"], [265, 8, 236, 65], [265, 9, 236, 67], [266, 8, 236, 67, "children"], [266, 16, 236, 67], [266, 31, 237, 8], [266, 35, 237, 8, "_jsxDevRuntime"], [266, 49, 237, 8], [266, 50, 237, 8, "jsxDEV"], [266, 56, 237, 8], [266, 58, 237, 9, "_reactNative"], [266, 70, 237, 9], [266, 71, 237, 9, "View"], [266, 75, 237, 13], [267, 10, 238, 10, "style"], [267, 15, 238, 15], [267, 17, 238, 17], [268, 12, 239, 12, "flex"], [268, 16, 239, 16], [268, 18, 239, 18], [268, 19, 239, 19], [269, 12, 240, 12, "paddingTop"], [269, 22, 240, 22], [269, 24, 240, 24, "insets"], [269, 30, 240, 30], [269, 31, 240, 31, "top"], [269, 34, 240, 34], [269, 37, 240, 37], [269, 39, 240, 39], [270, 12, 241, 12, "paddingHorizontal"], [270, 29, 241, 29], [270, 31, 241, 31], [270, 33, 241, 33], [271, 12, 242, 12, "paddingBottom"], [271, 25, 242, 25], [271, 27, 242, 27, "insets"], [271, 33, 242, 33], [271, 34, 242, 34, "bottom"], [271, 40, 242, 40], [271, 43, 242, 43], [271, 45, 242, 45], [272, 12, 243, 12, "alignItems"], [272, 22, 243, 22], [272, 24, 243, 24], [272, 32, 243, 32], [273, 12, 244, 12, "justifyContent"], [273, 26, 244, 26], [273, 28, 244, 28], [274, 10, 245, 10], [274, 11, 245, 12], [275, 10, 245, 12, "children"], [275, 18, 245, 12], [275, 34, 247, 10], [275, 38, 247, 10, "_jsxDevRuntime"], [275, 52, 247, 10], [275, 53, 247, 10, "jsxDEV"], [275, 59, 247, 10], [275, 61, 247, 11, "_reactNative"], [275, 73, 247, 11], [275, 74, 247, 11, "View"], [275, 78, 247, 15], [276, 12, 248, 12, "style"], [276, 17, 248, 17], [276, 19, 248, 19], [277, 14, 249, 14, "width"], [277, 19, 249, 19], [277, 21, 249, 21], [277, 24, 249, 24], [278, 14, 250, 14, "height"], [278, 20, 250, 20], [278, 22, 250, 22], [278, 25, 250, 25], [279, 14, 251, 14, "borderRadius"], [279, 26, 251, 26], [279, 28, 251, 28], [279, 30, 251, 30], [280, 14, 252, 14, "backgroundColor"], [280, 29, 252, 29], [280, 31, 252, 31, "colors"], [280, 37, 252, 37], [280, 38, 252, 38, "primaryUltraLight"], [280, 55, 252, 55], [281, 14, 253, 14, "alignItems"], [281, 24, 253, 24], [281, 26, 253, 26], [281, 34, 253, 34], [282, 14, 254, 14, "justifyContent"], [282, 28, 254, 28], [282, 30, 254, 30], [282, 38, 254, 38], [283, 14, 255, 14, "marginBottom"], [283, 26, 255, 26], [283, 28, 255, 28], [284, 12, 256, 12], [284, 13, 256, 14], [285, 12, 256, 14, "children"], [285, 20, 256, 14], [285, 35, 257, 12], [285, 39, 257, 12, "_jsxDevRuntime"], [285, 53, 257, 12], [285, 54, 257, 12, "jsxDEV"], [285, 60, 257, 12], [285, 62, 257, 13, "_lucideReactNative"], [285, 80, 257, 13], [285, 81, 257, 13, "MessageSquare"], [285, 94, 257, 26], [286, 14, 257, 27, "size"], [286, 18, 257, 31], [286, 20, 257, 33], [286, 22, 257, 36], [287, 14, 257, 37, "color"], [287, 19, 257, 42], [287, 21, 257, 44, "colors"], [287, 27, 257, 50], [287, 28, 257, 51, "primary"], [288, 12, 257, 59], [289, 14, 257, 59, "fileName"], [289, 22, 257, 59], [289, 24, 257, 59, "_jsxFileName"], [289, 36, 257, 59], [290, 14, 257, 59, "lineNumber"], [290, 24, 257, 59], [291, 14, 257, 59, "columnNumber"], [291, 26, 257, 59], [292, 12, 257, 59], [292, 19, 257, 61], [293, 10, 257, 62], [294, 12, 257, 62, "fileName"], [294, 20, 257, 62], [294, 22, 257, 62, "_jsxFileName"], [294, 34, 257, 62], [295, 12, 257, 62, "lineNumber"], [295, 22, 257, 62], [296, 12, 257, 62, "columnNumber"], [296, 24, 257, 62], [297, 10, 257, 62], [297, 17, 258, 16], [297, 18, 258, 17], [297, 33, 261, 10], [297, 37, 261, 10, "_jsxDevRuntime"], [297, 51, 261, 10], [297, 52, 261, 10, "jsxDEV"], [297, 58, 261, 10], [297, 60, 261, 11, "_reactNative"], [297, 72, 261, 11], [297, 73, 261, 11, "Text"], [297, 77, 261, 15], [298, 12, 262, 12, "style"], [298, 17, 262, 17], [298, 19, 262, 19], [299, 14, 263, 14, "fontSize"], [299, 22, 263, 22], [299, 24, 263, 24], [299, 26, 263, 26], [300, 14, 264, 14, "fontFamily"], [300, 24, 264, 24], [300, 26, 264, 26], [300, 47, 264, 47], [301, 14, 265, 14, "color"], [301, 19, 265, 19], [301, 21, 265, 21, "colors"], [301, 27, 265, 27], [301, 28, 265, 28, "text"], [301, 32, 265, 32], [302, 14, 266, 14, "textAlign"], [302, 23, 266, 23], [302, 25, 266, 25], [302, 33, 266, 33], [303, 14, 267, 14, "marginBottom"], [303, 26, 267, 26], [303, 28, 267, 28], [304, 12, 268, 12], [304, 13, 268, 14], [305, 12, 268, 14, "children"], [305, 20, 268, 14], [305, 22, 268, 15], [306, 10, 270, 10], [307, 12, 270, 10, "fileName"], [307, 20, 270, 10], [307, 22, 270, 10, "_jsxFileName"], [307, 34, 270, 10], [308, 12, 270, 10, "lineNumber"], [308, 22, 270, 10], [309, 12, 270, 10, "columnNumber"], [309, 24, 270, 10], [310, 10, 270, 10], [310, 17, 270, 16], [310, 18, 270, 17], [310, 33, 273, 10], [310, 37, 273, 10, "_jsxDevRuntime"], [310, 51, 273, 10], [310, 52, 273, 10, "jsxDEV"], [310, 58, 273, 10], [310, 60, 273, 11, "_reactNative"], [310, 72, 273, 11], [310, 73, 273, 11, "Text"], [310, 77, 273, 15], [311, 12, 274, 12, "style"], [311, 17, 274, 17], [311, 19, 274, 19], [312, 14, 275, 14, "fontSize"], [312, 22, 275, 22], [312, 24, 275, 24], [312, 26, 275, 26], [313, 14, 276, 14, "fontFamily"], [313, 24, 276, 24], [313, 26, 276, 26], [313, 46, 276, 46], [314, 14, 277, 14, "color"], [314, 19, 277, 19], [314, 21, 277, 21, "colors"], [314, 27, 277, 27], [314, 28, 277, 28, "textSecondary"], [314, 41, 277, 41], [315, 14, 278, 14, "textAlign"], [315, 23, 278, 23], [315, 25, 278, 25], [315, 33, 278, 33], [316, 14, 279, 14, "lineHeight"], [316, 24, 279, 24], [316, 26, 279, 26], [316, 28, 279, 28], [317, 14, 280, 14, "marginBottom"], [317, 26, 280, 26], [317, 28, 280, 28], [318, 12, 281, 12], [318, 13, 281, 14], [319, 12, 281, 14, "children"], [319, 20, 281, 14], [319, 22, 281, 15], [320, 10, 284, 10], [321, 12, 284, 10, "fileName"], [321, 20, 284, 10], [321, 22, 284, 10, "_jsxFileName"], [321, 34, 284, 10], [322, 12, 284, 10, "lineNumber"], [322, 22, 284, 10], [323, 12, 284, 10, "columnNumber"], [323, 24, 284, 10], [324, 10, 284, 10], [324, 17, 284, 16], [324, 18, 284, 17], [324, 33, 287, 10], [324, 37, 287, 10, "_jsxDevRuntime"], [324, 51, 287, 10], [324, 52, 287, 10, "jsxDEV"], [324, 58, 287, 10], [324, 60, 287, 11, "_reactNative"], [324, 72, 287, 11], [324, 73, 287, 11, "TouchableOpacity"], [324, 89, 287, 27], [325, 12, 288, 12, "style"], [325, 17, 288, 17], [325, 19, 288, 19], [326, 14, 289, 14, "backgroundColor"], [326, 29, 289, 29], [326, 31, 289, 31, "colors"], [326, 37, 289, 37], [326, 38, 289, 38, "primary"], [326, 45, 289, 45], [327, 14, 290, 14, "borderRadius"], [327, 26, 290, 26], [327, 28, 290, 28], [327, 30, 290, 30], [328, 14, 291, 14, "paddingHorizontal"], [328, 31, 291, 31], [328, 33, 291, 33], [328, 35, 291, 35], [329, 14, 292, 14, "paddingVertical"], [329, 29, 292, 29], [329, 31, 292, 31], [329, 33, 292, 33], [330, 14, 293, 14, "min<PERSON><PERSON><PERSON>"], [330, 22, 293, 22], [330, 24, 293, 24], [330, 27, 293, 27], [331, 14, 294, 14, "alignItems"], [331, 24, 294, 24], [331, 26, 294, 26], [332, 12, 295, 12], [332, 13, 295, 14], [333, 12, 296, 12, "onPress"], [333, 19, 296, 19], [333, 21, 296, 21, "handleStartBrainstorming"], [333, 45, 296, 46], [334, 12, 296, 46, "children"], [334, 20, 296, 46], [334, 35, 297, 12], [334, 39, 297, 12, "_jsxDevRuntime"], [334, 53, 297, 12], [334, 54, 297, 12, "jsxDEV"], [334, 60, 297, 12], [334, 62, 297, 13, "_reactNative"], [334, 74, 297, 13], [334, 75, 297, 13, "Text"], [334, 79, 297, 17], [335, 14, 298, 14, "style"], [335, 19, 298, 19], [335, 21, 298, 21], [336, 16, 299, 16, "fontSize"], [336, 24, 299, 24], [336, 26, 299, 26], [336, 28, 299, 28], [337, 16, 300, 16, "fontFamily"], [337, 26, 300, 26], [337, 28, 300, 28], [337, 49, 300, 49], [338, 16, 301, 16, "color"], [338, 21, 301, 21], [338, 23, 301, 23, "colors"], [338, 29, 301, 29], [338, 30, 301, 30, "background"], [339, 14, 302, 14], [339, 15, 302, 16], [340, 14, 302, 16, "children"], [340, 22, 302, 16], [340, 24, 302, 17], [341, 12, 304, 12], [342, 14, 304, 12, "fileName"], [342, 22, 304, 12], [342, 24, 304, 12, "_jsxFileName"], [342, 36, 304, 12], [343, 14, 304, 12, "lineNumber"], [343, 24, 304, 12], [344, 14, 304, 12, "columnNumber"], [344, 26, 304, 12], [345, 12, 304, 12], [345, 19, 304, 18], [346, 10, 304, 19], [347, 12, 304, 19, "fileName"], [347, 20, 304, 19], [347, 22, 304, 19, "_jsxFileName"], [347, 34, 304, 19], [348, 12, 304, 19, "lineNumber"], [348, 22, 304, 19], [349, 12, 304, 19, "columnNumber"], [349, 24, 304, 19], [350, 10, 304, 19], [350, 17, 305, 28], [350, 18, 305, 29], [351, 8, 305, 29], [352, 10, 305, 29, "fileName"], [352, 18, 305, 29], [352, 20, 305, 29, "_jsxFileName"], [352, 32, 305, 29], [353, 10, 305, 29, "lineNumber"], [353, 20, 305, 29], [354, 10, 305, 29, "columnNumber"], [354, 22, 305, 29], [355, 8, 305, 29], [355, 15, 306, 14], [356, 6, 306, 15], [357, 8, 306, 15, "fileName"], [357, 16, 306, 15], [357, 18, 306, 15, "_jsxFileName"], [357, 30, 306, 15], [358, 8, 306, 15, "lineNumber"], [358, 18, 306, 15], [359, 8, 306, 15, "columnNumber"], [359, 20, 306, 15], [360, 6, 306, 15], [360, 13, 307, 12], [360, 14, 307, 13], [361, 4, 309, 2], [363, 4, 311, 2], [364, 4, 312, 2], [364, 24, 313, 4], [364, 28, 313, 4, "_jsxDevRuntime"], [364, 42, 313, 4], [364, 43, 313, 4, "jsxDEV"], [364, 49, 313, 4], [364, 51, 313, 5, "_reactNative"], [364, 63, 313, 5], [364, 64, 313, 5, "View"], [364, 68, 313, 9], [365, 6, 313, 10, "style"], [365, 11, 313, 15], [365, 13, 313, 17], [366, 8, 313, 19, "flex"], [366, 12, 313, 23], [366, 14, 313, 25], [366, 15, 313, 26], [367, 8, 313, 28, "backgroundColor"], [367, 23, 313, 43], [367, 25, 313, 45, "colors"], [367, 31, 313, 51], [367, 32, 313, 52, "background"], [368, 6, 313, 63], [368, 7, 313, 65], [369, 6, 313, 65, "children"], [369, 14, 313, 65], [369, 30, 314, 6], [369, 34, 314, 6, "_jsxDevRuntime"], [369, 48, 314, 6], [369, 49, 314, 6, "jsxDEV"], [369, 55, 314, 6], [369, 57, 314, 7, "_Header"], [369, 64, 314, 7], [369, 65, 314, 7, "default"], [369, 72, 314, 13], [370, 8, 315, 8, "voiceMode"], [370, 17, 315, 17], [370, 19, 315, 19, "voiceMode"], [370, 28, 315, 29], [371, 8, 316, 8, "onToggleVoiceMode"], [371, 25, 316, 25], [371, 27, 316, 27, "toggleVoiceMode"], [371, 42, 316, 43], [372, 8, 317, 8, "onDone"], [372, 14, 317, 14], [372, 16, 317, 16, "onDone"], [372, 17, 317, 16], [372, 22, 317, 22, "<PERSON><PERSON>"], [372, 40, 317, 27], [372, 41, 317, 28, "alert"], [372, 46, 317, 33], [372, 47, 317, 34], [372, 61, 317, 48], [373, 6, 317, 50], [374, 8, 317, 50, "fileName"], [374, 16, 317, 50], [374, 18, 317, 50, "_jsxFileName"], [374, 30, 317, 50], [375, 8, 317, 50, "lineNumber"], [375, 18, 317, 50], [376, 8, 317, 50, "columnNumber"], [376, 20, 317, 50], [377, 6, 317, 50], [377, 13, 318, 7], [377, 14, 318, 8], [377, 16, 319, 7, "voiceMode"], [377, 25, 319, 16], [377, 41, 320, 8], [377, 45, 320, 8, "_jsxDevRuntime"], [377, 59, 320, 8], [377, 60, 320, 8, "jsxDEV"], [377, 66, 320, 8], [377, 68, 320, 9, "_VoiceMode"], [377, 78, 320, 9], [377, 79, 320, 9, "default"], [377, 86, 320, 18], [378, 8, 321, 10, "isRecording"], [378, 19, 321, 21], [378, 21, 321, 23, "recorderState"], [378, 34, 321, 36], [378, 35, 321, 37, "isRecording"], [378, 46, 321, 49], [379, 8, 322, 10, "onRecord"], [379, 16, 322, 18], [379, 18, 322, 20, "handleVoiceRecord"], [379, 35, 322, 38], [380, 8, 323, 10, "hasPermission"], [380, 21, 323, 23], [380, 23, 323, 25, "hasPermission"], [380, 36, 323, 39], [381, 8, 324, 10, "isLoading"], [381, 17, 324, 19], [381, 19, 324, 21, "isLoading"], [381, 28, 324, 31], [382, 8, 325, 10, "transcript"], [382, 18, 325, 20], [382, 20, 325, 22, "transcript"], [382, 30, 325, 33], [383, 8, 326, 10, "isMuted"], [383, 15, 326, 17], [383, 17, 326, 19, "isMuted"], [383, 24, 326, 27], [384, 8, 327, 10, "onMute"], [384, 14, 327, 16], [384, 16, 327, 18, "handleMute"], [385, 6, 327, 29], [386, 8, 327, 29, "fileName"], [386, 16, 327, 29], [386, 18, 327, 29, "_jsxFileName"], [386, 30, 327, 29], [387, 8, 327, 29, "lineNumber"], [387, 18, 327, 29], [388, 8, 327, 29, "columnNumber"], [388, 20, 327, 29], [389, 6, 327, 29], [389, 13, 328, 9], [389, 14, 328, 10], [389, 30, 330, 8], [389, 34, 330, 8, "_jsxDevRuntime"], [389, 48, 330, 8], [389, 49, 330, 8, "jsxDEV"], [389, 55, 330, 8], [389, 57, 330, 9, "_KeyboardAvoidingAnimatedView"], [389, 86, 330, 9], [389, 87, 330, 9, "default"], [389, 94, 330, 37], [390, 8, 330, 38, "style"], [390, 13, 330, 43], [390, 15, 330, 45], [391, 10, 330, 47, "flex"], [391, 14, 330, 51], [391, 16, 330, 53], [392, 8, 330, 55], [392, 9, 330, 57], [393, 8, 330, 57, "children"], [393, 16, 330, 57], [393, 32, 332, 10], [393, 36, 332, 10, "_jsxDevRuntime"], [393, 50, 332, 10], [393, 51, 332, 10, "jsxDEV"], [393, 57, 332, 10], [393, 59, 332, 11, "_reactNative"], [393, 71, 332, 11], [393, 72, 332, 11, "ScrollView"], [393, 82, 332, 21], [394, 10, 333, 12, "ref"], [394, 13, 333, 15], [394, 15, 333, 17, "scrollViewRef"], [394, 28, 333, 31], [395, 10, 334, 12, "style"], [395, 15, 334, 17], [395, 17, 334, 19], [396, 12, 334, 21, "flex"], [396, 16, 334, 25], [396, 18, 334, 27], [397, 10, 334, 29], [397, 11, 334, 31], [398, 10, 335, 12, "contentContainerStyle"], [398, 31, 335, 33], [398, 33, 335, 35], [399, 12, 336, 14, "paddingHorizontal"], [399, 29, 336, 31], [399, 31, 336, 33], [399, 33, 336, 35], [400, 12, 337, 14, "paddingVertical"], [400, 27, 337, 29], [400, 29, 337, 31], [400, 31, 337, 33], [401, 12, 338, 14, "paddingBottom"], [401, 25, 338, 27], [401, 27, 338, 29], [401, 30, 338, 32], [401, 31, 338, 34], [402, 10, 339, 12], [402, 11, 339, 14], [403, 10, 340, 12, "showsVerticalScrollIndicator"], [403, 38, 340, 40], [403, 40, 340, 42], [403, 45, 340, 48], [404, 10, 340, 48, "children"], [404, 18, 340, 48], [404, 21, 341, 13, "messages"], [404, 29, 341, 21], [404, 30, 341, 22, "map"], [404, 33, 341, 25], [404, 34, 341, 26], [404, 35, 341, 27, "message"], [404, 42, 341, 34], [404, 44, 341, 36, "index"], [404, 49, 341, 41], [404, 67, 342, 14], [404, 71, 342, 14, "_jsxDevRuntime"], [404, 85, 342, 14], [404, 86, 342, 14, "jsxDEV"], [404, 92, 342, 14], [404, 94, 342, 15, "_MessageBubble"], [404, 108, 342, 15], [404, 109, 342, 15, "default"], [404, 116, 342, 28], [405, 12, 342, 41, "message"], [405, 19, 342, 48], [405, 21, 342, 50, "message"], [405, 28, 342, 58], [406, 12, 342, 59, "onLongPress"], [406, 23, 342, 70], [406, 25, 342, 72, "handleLongPress"], [407, 10, 342, 88], [407, 13, 342, 34, "index"], [407, 18, 342, 39], [408, 12, 342, 39, "fileName"], [408, 20, 342, 39], [408, 22, 342, 39, "_jsxFileName"], [408, 34, 342, 39], [409, 12, 342, 39, "lineNumber"], [409, 22, 342, 39], [410, 12, 342, 39, "columnNumber"], [410, 24, 342, 39], [411, 10, 342, 39], [411, 17, 342, 90], [411, 18, 343, 13], [411, 19, 343, 14], [411, 21, 346, 13, "isLoading"], [411, 30, 346, 22], [411, 47, 347, 14], [411, 51, 347, 14, "_jsxDevRuntime"], [411, 65, 347, 14], [411, 66, 347, 14, "jsxDEV"], [411, 72, 347, 14], [411, 74, 347, 15, "_reactNative"], [411, 86, 347, 15], [411, 87, 347, 15, "View"], [411, 91, 347, 19], [412, 12, 348, 16, "style"], [412, 17, 348, 21], [412, 19, 348, 23], [413, 14, 349, 18, "marginBottom"], [413, 26, 349, 30], [413, 28, 349, 32], [413, 30, 349, 34], [414, 14, 350, 18, "alignSelf"], [414, 23, 350, 27], [414, 25, 350, 29], [414, 37, 350, 41], [415, 14, 351, 18, "max<PERSON><PERSON><PERSON>"], [415, 22, 351, 26], [415, 24, 351, 28], [416, 12, 352, 16], [416, 13, 352, 18], [417, 12, 352, 18, "children"], [417, 20, 352, 18], [417, 35, 353, 16], [417, 39, 353, 16, "_jsxDevRuntime"], [417, 53, 353, 16], [417, 54, 353, 16, "jsxDEV"], [417, 60, 353, 16], [417, 62, 353, 17, "_reactNative"], [417, 74, 353, 17], [417, 75, 353, 17, "View"], [417, 79, 353, 21], [418, 14, 354, 18, "style"], [418, 19, 354, 23], [418, 21, 354, 25], [419, 16, 355, 20, "backgroundColor"], [419, 31, 355, 35], [419, 33, 355, 37, "colors"], [419, 39, 355, 43], [419, 40, 355, 44, "cardBackground"], [419, 54, 355, 58], [420, 16, 356, 20, "borderRadius"], [420, 28, 356, 32], [420, 30, 356, 34], [420, 32, 356, 36], [421, 16, 357, 20, "paddingHorizontal"], [421, 33, 357, 37], [421, 35, 357, 39], [421, 37, 357, 41], [422, 16, 358, 20, "paddingVertical"], [422, 31, 358, 35], [422, 33, 358, 37], [422, 35, 358, 39], [423, 16, 359, 20, "borderWidth"], [423, 27, 359, 31], [423, 29, 359, 33], [423, 30, 359, 34], [424, 16, 360, 20, "borderColor"], [424, 27, 360, 31], [424, 29, 360, 33, "colors"], [424, 35, 360, 39], [424, 36, 360, 40, "outline"], [425, 14, 361, 18], [425, 15, 361, 20], [426, 14, 361, 20, "children"], [426, 22, 361, 20], [426, 37, 362, 18], [426, 41, 362, 18, "_jsxDevRuntime"], [426, 55, 362, 18], [426, 56, 362, 18, "jsxDEV"], [426, 62, 362, 18], [426, 64, 362, 19, "_reactNative"], [426, 76, 362, 19], [426, 77, 362, 19, "Text"], [426, 81, 362, 23], [427, 16, 363, 20, "style"], [427, 21, 363, 25], [427, 23, 363, 27], [428, 18, 364, 22, "fontSize"], [428, 26, 364, 30], [428, 28, 364, 32], [428, 30, 364, 34], [429, 18, 365, 22, "fontFamily"], [429, 28, 365, 32], [429, 30, 365, 34], [429, 50, 365, 54], [430, 18, 366, 22, "color"], [430, 23, 366, 27], [430, 25, 366, 29, "colors"], [430, 31, 366, 35], [430, 32, 366, 36, "textSecondary"], [430, 45, 366, 49], [431, 18, 367, 22, "lineHeight"], [431, 28, 367, 32], [431, 30, 367, 34], [432, 16, 368, 20], [432, 17, 368, 22], [433, 16, 368, 22, "children"], [433, 24, 368, 22], [433, 26, 368, 23], [434, 14, 370, 18], [435, 16, 370, 18, "fileName"], [435, 24, 370, 18], [435, 26, 370, 18, "_jsxFileName"], [435, 38, 370, 18], [436, 16, 370, 18, "lineNumber"], [436, 26, 370, 18], [437, 16, 370, 18, "columnNumber"], [437, 28, 370, 18], [438, 14, 370, 18], [438, 21, 370, 24], [439, 12, 370, 25], [440, 14, 370, 25, "fileName"], [440, 22, 370, 25], [440, 24, 370, 25, "_jsxFileName"], [440, 36, 370, 25], [441, 14, 370, 25, "lineNumber"], [441, 24, 370, 25], [442, 14, 370, 25, "columnNumber"], [442, 26, 370, 25], [443, 12, 370, 25], [443, 19, 371, 22], [444, 10, 371, 23], [445, 12, 371, 23, "fileName"], [445, 20, 371, 23], [445, 22, 371, 23, "_jsxFileName"], [445, 34, 371, 23], [446, 12, 371, 23, "lineNumber"], [446, 22, 371, 23], [447, 12, 371, 23, "columnNumber"], [447, 24, 371, 23], [448, 10, 371, 23], [448, 17, 372, 20], [448, 18, 373, 13], [449, 8, 373, 13], [450, 10, 373, 13, "fileName"], [450, 18, 373, 13], [450, 20, 373, 13, "_jsxFileName"], [450, 32, 373, 13], [451, 10, 373, 13, "lineNumber"], [451, 20, 373, 13], [452, 10, 373, 13, "columnNumber"], [452, 22, 373, 13], [453, 8, 373, 13], [453, 15, 374, 22], [453, 16, 374, 23], [453, 18, 377, 11, "quickActions"], [453, 30, 377, 23], [453, 31, 377, 24, "length"], [453, 37, 377, 30], [453, 40, 377, 33], [453, 41, 377, 34], [453, 58, 378, 12], [453, 62, 378, 12, "_jsxDevRuntime"], [453, 76, 378, 12], [453, 77, 378, 12, "jsxDEV"], [453, 83, 378, 12], [453, 85, 378, 13, "_reactNative"], [453, 97, 378, 13], [453, 98, 378, 13, "View"], [453, 102, 378, 17], [454, 10, 379, 14, "style"], [454, 15, 379, 19], [454, 17, 379, 21], [455, 12, 380, 16, "position"], [455, 20, 380, 24], [455, 22, 380, 26], [455, 32, 380, 36], [456, 12, 381, 16, "bottom"], [456, 18, 381, 22], [456, 20, 381, 24, "insets"], [456, 26, 381, 30], [456, 27, 381, 31, "bottom"], [456, 33, 381, 37], [456, 36, 381, 40], [456, 39, 381, 43], [457, 12, 382, 16, "left"], [457, 16, 382, 20], [457, 18, 382, 22], [457, 20, 382, 24], [458, 12, 383, 16, "right"], [458, 17, 383, 21], [458, 19, 383, 23], [459, 10, 384, 14], [459, 11, 384, 16], [460, 10, 384, 16, "children"], [460, 18, 384, 16], [460, 33, 385, 14], [460, 37, 385, 14, "_jsxDevRuntime"], [460, 51, 385, 14], [460, 52, 385, 14, "jsxDEV"], [460, 58, 385, 14], [460, 60, 385, 15, "_reactNative"], [460, 72, 385, 15], [460, 73, 385, 15, "ScrollView"], [460, 83, 385, 25], [461, 12, 386, 16, "horizontal"], [461, 22, 386, 26], [462, 12, 387, 16, "showsHorizontalScrollIndicator"], [462, 42, 387, 46], [462, 44, 387, 48], [462, 49, 387, 54], [463, 12, 388, 16, "contentContainerStyle"], [463, 33, 388, 37], [463, 35, 388, 39], [464, 14, 388, 41, "gap"], [464, 17, 388, 44], [464, 19, 388, 46], [465, 12, 388, 48], [465, 13, 388, 50], [466, 12, 388, 50, "children"], [466, 20, 388, 50], [466, 22, 389, 17, "quickActions"], [466, 34, 389, 29], [466, 35, 389, 30, "map"], [466, 38, 389, 33], [466, 39, 389, 34], [466, 40, 389, 35, "action"], [466, 46, 389, 41], [466, 48, 389, 43, "index"], [466, 53, 389, 48], [466, 71, 390, 18], [466, 75, 390, 18, "_jsxDevRuntime"], [466, 89, 390, 18], [466, 90, 390, 18, "jsxDEV"], [466, 96, 390, 18], [466, 98, 390, 19, "_reactNative"], [466, 110, 390, 19], [466, 111, 390, 19, "TouchableOpacity"], [466, 127, 390, 35], [467, 14, 392, 20, "style"], [467, 19, 392, 25], [467, 21, 392, 27], [468, 16, 393, 22, "backgroundColor"], [468, 31, 393, 37], [468, 33, 393, 39, "colors"], [468, 39, 393, 45], [468, 40, 393, 46, "primaryUltraLight"], [468, 57, 393, 63], [469, 16, 394, 22, "borderRadius"], [469, 28, 394, 34], [469, 30, 394, 36], [469, 32, 394, 38], [470, 16, 395, 22, "paddingHorizontal"], [470, 33, 395, 39], [470, 35, 395, 41], [470, 37, 395, 43], [471, 16, 396, 22, "paddingVertical"], [471, 31, 396, 37], [471, 33, 396, 39], [471, 34, 396, 40], [472, 16, 397, 22, "borderWidth"], [472, 27, 397, 33], [472, 29, 397, 35], [472, 30, 397, 36], [473, 16, 398, 22, "borderColor"], [473, 27, 398, 33], [473, 29, 398, 35, "colors"], [473, 35, 398, 41], [473, 36, 398, 42, "primary"], [474, 14, 399, 20], [474, 15, 399, 22], [475, 14, 400, 20, "onPress"], [475, 21, 400, 27], [475, 23, 400, 29, "onPress"], [475, 24, 400, 29], [475, 29, 400, 35, "handleQuickAction"], [475, 46, 400, 52], [475, 47, 400, 53, "action"], [475, 53, 400, 59], [475, 54, 400, 61], [476, 14, 400, 61, "children"], [476, 22, 400, 61], [476, 37, 401, 20], [476, 41, 401, 20, "_jsxDevRuntime"], [476, 55, 401, 20], [476, 56, 401, 20, "jsxDEV"], [476, 62, 401, 20], [476, 64, 401, 21, "_reactNative"], [476, 76, 401, 21], [476, 77, 401, 21, "Text"], [476, 81, 401, 25], [477, 16, 402, 22, "style"], [477, 21, 402, 27], [477, 23, 402, 29], [478, 18, 403, 24, "fontSize"], [478, 26, 403, 32], [478, 28, 403, 34], [478, 30, 403, 36], [479, 18, 404, 24, "fontFamily"], [479, 28, 404, 34], [479, 30, 404, 36], [479, 49, 404, 55], [480, 18, 405, 24, "color"], [480, 23, 405, 29], [480, 25, 405, 31, "colors"], [480, 31, 405, 37], [480, 32, 405, 38, "primary"], [481, 16, 406, 22], [481, 17, 406, 24], [482, 16, 406, 24, "children"], [482, 24, 406, 24], [482, 26, 407, 23, "action"], [483, 14, 407, 29], [484, 16, 407, 29, "fileName"], [484, 24, 407, 29], [484, 26, 407, 29, "_jsxFileName"], [484, 38, 407, 29], [485, 16, 407, 29, "lineNumber"], [485, 26, 407, 29], [486, 16, 407, 29, "columnNumber"], [486, 28, 407, 29], [487, 14, 407, 29], [487, 21, 408, 26], [488, 12, 408, 27], [488, 15, 391, 25, "index"], [488, 20, 391, 30], [489, 14, 391, 30, "fileName"], [489, 22, 391, 30], [489, 24, 391, 30, "_jsxFileName"], [489, 36, 391, 30], [490, 14, 391, 30, "lineNumber"], [490, 24, 391, 30], [491, 14, 391, 30, "columnNumber"], [491, 26, 391, 30], [492, 12, 391, 30], [492, 19, 409, 36], [492, 20, 410, 17], [493, 10, 410, 18], [494, 12, 410, 18, "fileName"], [494, 20, 410, 18], [494, 22, 410, 18, "_jsxFileName"], [494, 34, 410, 18], [495, 12, 410, 18, "lineNumber"], [495, 22, 410, 18], [496, 12, 410, 18, "columnNumber"], [496, 24, 410, 18], [497, 10, 410, 18], [497, 17, 411, 26], [498, 8, 411, 27], [499, 10, 411, 27, "fileName"], [499, 18, 411, 27], [499, 20, 411, 27, "_jsxFileName"], [499, 32, 411, 27], [500, 10, 411, 27, "lineNumber"], [500, 20, 411, 27], [501, 10, 411, 27, "columnNumber"], [501, 22, 411, 27], [502, 8, 411, 27], [502, 15, 412, 18], [502, 16, 413, 11], [502, 31, 416, 10], [502, 35, 416, 10, "_jsxDevRuntime"], [502, 49, 416, 10], [502, 50, 416, 10, "jsxDEV"], [502, 56, 416, 10], [502, 58, 416, 11, "_reactNative"], [502, 70, 416, 11], [502, 71, 416, 11, "View"], [502, 75, 416, 15], [503, 10, 417, 12, "style"], [503, 15, 417, 17], [503, 17, 417, 19], [504, 12, 418, 14, "position"], [504, 20, 418, 22], [504, 22, 418, 24], [504, 32, 418, 34], [505, 12, 419, 14, "bottom"], [505, 18, 419, 20], [505, 20, 419, 22], [505, 21, 419, 23], [506, 12, 420, 14, "left"], [506, 16, 420, 18], [506, 18, 420, 20], [506, 19, 420, 21], [507, 12, 421, 14, "right"], [507, 17, 421, 19], [507, 19, 421, 21], [508, 10, 422, 12], [508, 11, 422, 14], [509, 10, 422, 14, "children"], [509, 18, 422, 14], [509, 33, 423, 12], [509, 37, 423, 12, "_jsxDevRuntime"], [509, 51, 423, 12], [509, 52, 423, 12, "jsxDEV"], [509, 58, 423, 12], [509, 60, 423, 13, "_TextMode"], [509, 69, 423, 13], [509, 70, 423, 13, "default"], [509, 77, 423, 21], [510, 12, 424, 14, "inputText"], [510, 21, 424, 23], [510, 23, 424, 25, "inputText"], [510, 32, 424, 35], [511, 12, 425, 14, "onInputChange"], [511, 25, 425, 27], [511, 27, 425, 29, "setInputText"], [511, 39, 425, 42], [512, 12, 426, 14, "onSendMessage"], [512, 25, 426, 27], [512, 27, 426, 29, "onSendMessage"], [512, 28, 426, 29], [512, 33, 426, 35, "handleSendMessage"], [512, 50, 426, 52], [512, 51, 426, 53], [512, 52, 426, 55], [513, 12, 427, 14, "onStartDictation"], [513, 28, 427, 30], [513, 30, 427, 32, "handleDictation"], [514, 10, 427, 48], [515, 12, 427, 48, "fileName"], [515, 20, 427, 48], [515, 22, 427, 48, "_jsxFileName"], [515, 34, 427, 48], [516, 12, 427, 48, "lineNumber"], [516, 22, 427, 48], [517, 12, 427, 48, "columnNumber"], [517, 24, 427, 48], [518, 10, 427, 48], [518, 17, 428, 13], [519, 8, 428, 14], [520, 10, 428, 14, "fileName"], [520, 18, 428, 14], [520, 20, 428, 14, "_jsxFileName"], [520, 32, 428, 14], [521, 10, 428, 14, "lineNumber"], [521, 20, 428, 14], [522, 10, 428, 14, "columnNumber"], [522, 22, 428, 14], [523, 8, 428, 14], [523, 15, 429, 16], [523, 16, 429, 17], [524, 6, 429, 17], [525, 8, 429, 17, "fileName"], [525, 16, 429, 17], [525, 18, 429, 17, "_jsxFileName"], [525, 30, 429, 17], [526, 8, 429, 17, "lineNumber"], [526, 18, 429, 17], [527, 8, 429, 17, "columnNumber"], [527, 20, 429, 17], [528, 6, 429, 17], [528, 13, 430, 38], [528, 14, 431, 7], [528, 29, 434, 6], [528, 33, 434, 6, "_jsxDevRuntime"], [528, 47, 434, 6], [528, 48, 434, 6, "jsxDEV"], [528, 54, 434, 6], [528, 56, 434, 7, "_reactNative"], [528, 68, 434, 7], [528, 69, 434, 7, "Modal"], [528, 74, 434, 12], [529, 8, 435, 8, "transparent"], [529, 19, 435, 19], [530, 8, 436, 8, "visible"], [530, 15, 436, 15], [530, 17, 436, 17, "isContextMenuVisible"], [530, 37, 436, 38], [531, 8, 437, 8, "onRequestClose"], [531, 22, 437, 22], [531, 24, 437, 24, "onRequestClose"], [531, 25, 437, 24], [531, 30, 437, 30, "setIsContextMenuVisible"], [531, 53, 437, 53], [531, 54, 437, 54], [531, 59, 437, 59], [531, 60, 437, 61], [532, 8, 437, 61, "children"], [532, 16, 437, 61], [532, 31, 439, 8], [532, 35, 439, 8, "_jsxDevRuntime"], [532, 49, 439, 8], [532, 50, 439, 8, "jsxDEV"], [532, 56, 439, 8], [532, 58, 439, 9, "_reactNative"], [532, 70, 439, 9], [532, 71, 439, 9, "TouchableOpacity"], [532, 87, 439, 25], [533, 10, 440, 10, "style"], [533, 15, 440, 15], [533, 17, 440, 17], [534, 12, 440, 19, "flex"], [534, 16, 440, 23], [534, 18, 440, 25], [534, 19, 440, 26], [535, 12, 440, 28, "backgroundColor"], [535, 27, 440, 43], [535, 29, 440, 45], [535, 46, 440, 62], [536, 12, 440, 64, "justifyContent"], [536, 26, 440, 78], [536, 28, 440, 80], [536, 36, 440, 88], [537, 12, 440, 90, "alignItems"], [537, 22, 440, 100], [537, 24, 440, 102], [538, 10, 440, 111], [538, 11, 440, 113], [539, 10, 441, 10, "activeOpacity"], [539, 23, 441, 23], [539, 25, 441, 25], [539, 26, 441, 27], [540, 10, 442, 10, "onPressOut"], [540, 20, 442, 20], [540, 22, 442, 22, "onPressOut"], [540, 23, 442, 22], [540, 28, 442, 28, "setIsContextMenuVisible"], [540, 51, 442, 51], [540, 52, 442, 52], [540, 57, 442, 57], [540, 58, 442, 59], [541, 10, 442, 59, "children"], [541, 18, 442, 59], [541, 33, 444, 10], [541, 37, 444, 10, "_jsxDevRuntime"], [541, 51, 444, 10], [541, 52, 444, 10, "jsxDEV"], [541, 58, 444, 10], [541, 60, 444, 11, "_reactNative"], [541, 72, 444, 11], [541, 73, 444, 11, "View"], [541, 77, 444, 15], [542, 12, 444, 16, "style"], [542, 17, 444, 21], [542, 19, 444, 23], [543, 14, 444, 25, "backgroundColor"], [543, 29, 444, 40], [543, 31, 444, 42, "colors"], [543, 37, 444, 48], [543, 38, 444, 49, "background"], [543, 48, 444, 59], [544, 14, 444, 61, "borderRadius"], [544, 26, 444, 73], [544, 28, 444, 75], [544, 30, 444, 77], [545, 14, 444, 79, "padding"], [545, 21, 444, 86], [545, 23, 444, 88], [545, 25, 444, 90], [546, 14, 444, 92, "width"], [546, 19, 444, 97], [546, 21, 444, 99], [547, 12, 444, 105], [547, 13, 444, 107], [548, 12, 444, 107, "children"], [548, 20, 444, 107], [548, 36, 445, 12], [548, 40, 445, 12, "_jsxDevRuntime"], [548, 54, 445, 12], [548, 55, 445, 12, "jsxDEV"], [548, 61, 445, 12], [548, 63, 445, 13, "_reactNative"], [548, 75, 445, 13], [548, 76, 445, 13, "TouchableOpacity"], [548, 92, 445, 29], [549, 14, 445, 30, "onPress"], [549, 21, 445, 37], [549, 23, 445, 39, "handleCopyMessage"], [549, 40, 445, 57], [550, 14, 445, 58, "style"], [550, 19, 445, 63], [550, 21, 445, 65], [551, 16, 445, 67, "paddingVertical"], [551, 31, 445, 82], [551, 33, 445, 84], [552, 14, 445, 87], [552, 15, 445, 89], [553, 14, 445, 89, "children"], [553, 22, 445, 89], [553, 37, 446, 14], [553, 41, 446, 14, "_jsxDevRuntime"], [553, 55, 446, 14], [553, 56, 446, 14, "jsxDEV"], [553, 62, 446, 14], [553, 64, 446, 15, "_reactNative"], [553, 76, 446, 15], [553, 77, 446, 15, "Text"], [553, 81, 446, 19], [554, 16, 446, 20, "style"], [554, 21, 446, 25], [554, 23, 446, 27], [555, 18, 446, 29, "fontSize"], [555, 26, 446, 37], [555, 28, 446, 39], [555, 30, 446, 41], [556, 18, 446, 43, "fontFamily"], [556, 28, 446, 53], [556, 30, 446, 55], [556, 49, 446, 74], [557, 18, 446, 76, "color"], [557, 23, 446, 81], [557, 25, 446, 83, "colors"], [557, 31, 446, 89], [557, 32, 446, 90, "text"], [558, 16, 446, 95], [558, 17, 446, 97], [559, 16, 446, 97, "children"], [559, 24, 446, 97], [559, 26, 446, 98], [560, 14, 446, 110], [561, 16, 446, 110, "fileName"], [561, 24, 446, 110], [561, 26, 446, 110, "_jsxFileName"], [561, 38, 446, 110], [562, 16, 446, 110, "lineNumber"], [562, 26, 446, 110], [563, 16, 446, 110, "columnNumber"], [563, 28, 446, 110], [564, 14, 446, 110], [564, 21, 446, 116], [565, 12, 446, 117], [566, 14, 446, 117, "fileName"], [566, 22, 446, 117], [566, 24, 446, 117, "_jsxFileName"], [566, 36, 446, 117], [567, 14, 446, 117, "lineNumber"], [567, 24, 446, 117], [568, 14, 446, 117, "columnNumber"], [568, 26, 446, 117], [569, 12, 446, 117], [569, 19, 447, 30], [569, 20, 447, 31], [569, 22, 448, 13, "longPressedMessage"], [569, 40, 448, 31], [569, 42, 448, 33, "role"], [569, 46, 448, 37], [569, 51, 448, 42], [569, 62, 448, 53], [569, 79, 449, 14], [569, 83, 449, 14, "_jsxDevRuntime"], [569, 97, 449, 14], [569, 98, 449, 14, "jsxDEV"], [569, 104, 449, 14], [569, 106, 449, 15, "_reactNative"], [569, 118, 449, 15], [569, 119, 449, 15, "TouchableOpacity"], [569, 135, 449, 31], [570, 14, 449, 32, "onPress"], [570, 21, 449, 39], [570, 23, 449, 41, "handleListenToMessage"], [570, 44, 449, 63], [571, 14, 449, 64, "style"], [571, 19, 449, 69], [571, 21, 449, 71], [572, 16, 449, 73, "paddingVertical"], [572, 31, 449, 88], [572, 33, 449, 90], [573, 14, 449, 93], [573, 15, 449, 95], [574, 14, 449, 95, "children"], [574, 22, 449, 95], [574, 37, 450, 16], [574, 41, 450, 16, "_jsxDevRuntime"], [574, 55, 450, 16], [574, 56, 450, 16, "jsxDEV"], [574, 62, 450, 16], [574, 64, 450, 17, "_reactNative"], [574, 76, 450, 17], [574, 77, 450, 17, "Text"], [574, 81, 450, 21], [575, 16, 450, 22, "style"], [575, 21, 450, 27], [575, 23, 450, 29], [576, 18, 450, 31, "fontSize"], [576, 26, 450, 39], [576, 28, 450, 41], [576, 30, 450, 43], [577, 18, 450, 45, "fontFamily"], [577, 28, 450, 55], [577, 30, 450, 57], [577, 49, 450, 76], [578, 18, 450, 78, "color"], [578, 23, 450, 83], [578, 25, 450, 85, "colors"], [578, 31, 450, 91], [578, 32, 450, 92, "text"], [579, 16, 450, 97], [579, 17, 450, 99], [580, 16, 450, 99, "children"], [580, 24, 450, 99], [580, 26, 450, 100], [581, 14, 450, 117], [582, 16, 450, 117, "fileName"], [582, 24, 450, 117], [582, 26, 450, 117, "_jsxFileName"], [582, 38, 450, 117], [583, 16, 450, 117, "lineNumber"], [583, 26, 450, 117], [584, 16, 450, 117, "columnNumber"], [584, 28, 450, 117], [585, 14, 450, 117], [585, 21, 450, 123], [586, 12, 450, 124], [587, 14, 450, 124, "fileName"], [587, 22, 450, 124], [587, 24, 450, 124, "_jsxFileName"], [587, 36, 450, 124], [588, 14, 450, 124, "lineNumber"], [588, 24, 450, 124], [589, 14, 450, 124, "columnNumber"], [589, 26, 450, 124], [590, 12, 450, 124], [590, 19, 451, 32], [590, 20, 452, 13], [591, 10, 452, 13], [592, 12, 452, 13, "fileName"], [592, 20, 452, 13], [592, 22, 452, 13, "_jsxFileName"], [592, 34, 452, 13], [593, 12, 452, 13, "lineNumber"], [593, 22, 452, 13], [594, 12, 452, 13, "columnNumber"], [594, 24, 452, 13], [595, 10, 452, 13], [595, 17, 453, 16], [596, 8, 453, 17], [597, 10, 453, 17, "fileName"], [597, 18, 453, 17], [597, 20, 453, 17, "_jsxFileName"], [597, 32, 453, 17], [598, 10, 453, 17, "lineNumber"], [598, 20, 453, 17], [599, 10, 453, 17, "columnNumber"], [599, 22, 453, 17], [600, 8, 453, 17], [600, 15, 454, 26], [601, 6, 454, 27], [602, 8, 454, 27, "fileName"], [602, 16, 454, 27], [602, 18, 454, 27, "_jsxFileName"], [602, 30, 454, 27], [603, 8, 454, 27, "lineNumber"], [603, 18, 454, 27], [604, 8, 454, 27, "columnNumber"], [604, 20, 454, 27], [605, 6, 454, 27], [605, 13, 455, 13], [605, 14, 455, 14], [606, 4, 455, 14], [607, 6, 455, 14, "fileName"], [607, 14, 455, 14], [607, 16, 455, 14, "_jsxFileName"], [607, 28, 455, 14], [608, 6, 455, 14, "lineNumber"], [608, 16, 455, 14], [609, 6, 455, 14, "columnNumber"], [609, 18, 455, 14], [610, 4, 455, 14], [610, 11, 456, 10], [610, 12, 456, 11], [611, 2, 458, 0], [612, 2, 458, 1, "_s"], [612, 4, 458, 1], [612, 5, 34, 24, "BrainstormScreen"], [612, 21, 34, 40], [613, 4, 34, 40], [613, 12, 35, 17, "useSafeAreaInsets"], [613, 57, 35, 34], [613, 59, 36, 17, "useColors"], [613, 79, 36, 26], [613, 81, 37, 24, "useFonts"], [613, 98, 37, 32], [613, 100, 43, 19, "useAudioRecorder"], [613, 127, 43, 35], [613, 129, 44, 24, "useAudioRecorderState"], [613, 161, 44, 45], [614, 2, 44, 45], [615, 2, 44, 45, "_c"], [615, 4, 44, 45], [615, 7, 34, 24, "BrainstormScreen"], [615, 23, 34, 40], [616, 2, 34, 40], [616, 6, 34, 40, "_c"], [616, 8, 34, 40], [617, 2, 34, 40, "$RefreshReg$"], [617, 14, 34, 40], [617, 15, 34, 40, "_c"], [617, 17, 34, 40], [618, 0, 34, 40], [618, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "handleVoiceRecord", "setTranscript$argument_0", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCiC;YC4B;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJK;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMM;GTS;4BUE;sBCgB,8DD;GVW;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBE;YCI;GDI;gBiB0F,iCjB;0BkBwB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}