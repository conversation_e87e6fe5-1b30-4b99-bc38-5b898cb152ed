{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  // Those are the easings that can be implemented using Bezier curves.\n  // Others should be done as CSS animations\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WebEasings = void 0;\n  exports.getEasingByName = getEasingByName;\n  const WebEasings = exports.WebEasings = {\n    linear: [0, 0, 1, 1],\n    ease: [0.42, 0, 1, 1],\n    quad: [0.11, 0, 0.5, 0],\n    cubic: [0.32, 0, 0.67, 0],\n    sin: [0.12, 0, 0.39, 0],\n    circle: [0.55, 0, 1, 0.45],\n    exp: [0.7, 0, 0.84, 0]\n  };\n  function getEasingByName(easingName) {\n    return `cubic-bezier(${WebEasings[easingName].toString()})`;\n  }\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 4, 0], [6, 2, 4, 0, "Object"], [6, 8, 4, 0], [6, 9, 4, 0, "defineProperty"], [6, 23, 4, 0], [6, 24, 4, 0, "exports"], [6, 31, 4, 0], [7, 4, 4, 0, "value"], [7, 9, 4, 0], [8, 2, 4, 0], [9, 2, 4, 0, "exports"], [9, 9, 4, 0], [9, 10, 4, 0, "WebEasings"], [9, 20, 4, 0], [10, 2, 4, 0, "exports"], [10, 9, 4, 0], [10, 10, 4, 0, "getEasingByName"], [10, 25, 4, 0], [10, 28, 4, 0, "getEasingByName"], [10, 43, 4, 0], [11, 2, 5, 7], [11, 8, 5, 13, "WebEasings"], [11, 18, 5, 23], [11, 21, 5, 23, "exports"], [11, 28, 5, 23], [11, 29, 5, 23, "WebEasings"], [11, 39, 5, 23], [11, 42, 5, 26], [12, 4, 6, 2, "linear"], [12, 10, 6, 8], [12, 12, 6, 10], [12, 13, 6, 11], [12, 14, 6, 12], [12, 16, 6, 14], [12, 17, 6, 15], [12, 19, 6, 17], [12, 20, 6, 18], [12, 22, 6, 20], [12, 23, 6, 21], [12, 24, 6, 22], [13, 4, 7, 2, "ease"], [13, 8, 7, 6], [13, 10, 7, 8], [13, 11, 7, 9], [13, 15, 7, 13], [13, 17, 7, 15], [13, 18, 7, 16], [13, 20, 7, 18], [13, 21, 7, 19], [13, 23, 7, 21], [13, 24, 7, 22], [13, 25, 7, 23], [14, 4, 8, 2, "quad"], [14, 8, 8, 6], [14, 10, 8, 8], [14, 11, 8, 9], [14, 15, 8, 13], [14, 17, 8, 15], [14, 18, 8, 16], [14, 20, 8, 18], [14, 23, 8, 21], [14, 25, 8, 23], [14, 26, 8, 24], [14, 27, 8, 25], [15, 4, 9, 2, "cubic"], [15, 9, 9, 7], [15, 11, 9, 9], [15, 12, 9, 10], [15, 16, 9, 14], [15, 18, 9, 16], [15, 19, 9, 17], [15, 21, 9, 19], [15, 25, 9, 23], [15, 27, 9, 25], [15, 28, 9, 26], [15, 29, 9, 27], [16, 4, 10, 2, "sin"], [16, 7, 10, 5], [16, 9, 10, 7], [16, 10, 10, 8], [16, 14, 10, 12], [16, 16, 10, 14], [16, 17, 10, 15], [16, 19, 10, 17], [16, 23, 10, 21], [16, 25, 10, 23], [16, 26, 10, 24], [16, 27, 10, 25], [17, 4, 11, 2, "circle"], [17, 10, 11, 8], [17, 12, 11, 10], [17, 13, 11, 11], [17, 17, 11, 15], [17, 19, 11, 17], [17, 20, 11, 18], [17, 22, 11, 20], [17, 23, 11, 21], [17, 25, 11, 23], [17, 29, 11, 27], [17, 30, 11, 28], [18, 4, 12, 2, "exp"], [18, 7, 12, 5], [18, 9, 12, 7], [18, 10, 12, 8], [18, 13, 12, 11], [18, 15, 12, 13], [18, 16, 12, 14], [18, 18, 12, 16], [18, 22, 12, 20], [18, 24, 12, 22], [18, 25, 12, 23], [19, 2, 13, 0], [19, 3, 13, 1], [20, 2, 14, 7], [20, 11, 14, 16, "getEasingByName"], [20, 26, 14, 31, "getEasingByName"], [20, 27, 14, 32, "easingName"], [20, 37, 14, 42], [20, 39, 14, 44], [21, 4, 15, 2], [21, 11, 15, 9], [21, 27, 15, 25, "WebEasings"], [21, 37, 15, 35], [21, 38, 15, 36, "easingName"], [21, 48, 15, 46], [21, 49, 15, 47], [21, 50, 15, 48, "toString"], [21, 58, 15, 56], [21, 59, 15, 57], [21, 60, 15, 58], [21, 63, 15, 61], [22, 2, 16, 0], [23, 0, 16, 1], [23, 3]], "functionMap": {"names": ["<global>", "getEasingByName"], "mappings": "AAA;OCa;CDE"}}, "type": "js/module"}]}