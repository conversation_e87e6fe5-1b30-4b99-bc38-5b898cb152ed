{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  function defineLazyObjectProperty(object, name, descriptor) {\n    var get = descriptor.get;\n    var enumerable = descriptor.enumerable !== false;\n    var writable = descriptor.writable !== false;\n    var value;\n    var valueSet = false;\n    function getValue() {\n      if (!valueSet) {\n        valueSet = true;\n        setValue(get());\n      }\n      return value;\n    }\n    function setValue(newValue) {\n      value = newValue;\n      valueSet = true;\n      Object.defineProperty(object, name, {\n        value: newValue,\n        configurable: true,\n        enumerable,\n        writable\n      });\n    }\n    Object.defineProperty(object, name, {\n      get: getValue,\n      set: setValue,\n      configurable: true,\n      enumerable\n    });\n  }\n  var _default = exports.default = defineLazyObjectProperty;\n});", "lineCount": 39, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 16, 0], [8, 11, 16, 9, "defineLazyObjectProperty"], [8, 35, 16, 33, "defineLazyObjectProperty"], [8, 36, 17, 2, "object"], [8, 42, 17, 22], [8, 44, 18, 2, "name"], [8, 48, 18, 14], [8, 50, 19, 2, "descriptor"], [8, 60, 24, 3], [8, 62, 25, 8], [9, 4, 26, 2], [9, 8, 26, 9, "get"], [9, 11, 26, 12], [9, 14, 26, 16, "descriptor"], [9, 24, 26, 26], [9, 25, 26, 9, "get"], [9, 28, 26, 12], [10, 4, 27, 2], [10, 8, 27, 8, "enumerable"], [10, 18, 27, 18], [10, 21, 27, 21, "descriptor"], [10, 31, 27, 31], [10, 32, 27, 32, "enumerable"], [10, 42, 27, 42], [10, 47, 27, 47], [10, 52, 27, 52], [11, 4, 28, 2], [11, 8, 28, 8, "writable"], [11, 16, 28, 16], [11, 19, 28, 19, "descriptor"], [11, 29, 28, 29], [11, 30, 28, 30, "writable"], [11, 38, 28, 38], [11, 43, 28, 43], [11, 48, 28, 48], [12, 4, 30, 2], [12, 8, 30, 6, "value"], [12, 13, 30, 11], [13, 4, 31, 2], [13, 8, 31, 6, "valueSet"], [13, 16, 31, 14], [13, 19, 31, 17], [13, 24, 31, 22], [14, 4, 32, 2], [14, 13, 32, 11, "getValue"], [14, 21, 32, 19, "getValue"], [14, 22, 32, 19], [14, 24, 32, 25], [15, 6, 36, 4], [15, 10, 36, 8], [15, 11, 36, 9, "valueSet"], [15, 19, 36, 17], [15, 21, 36, 19], [16, 8, 42, 6, "valueSet"], [16, 16, 42, 14], [16, 19, 42, 17], [16, 23, 42, 21], [17, 8, 43, 6, "setValue"], [17, 16, 43, 14], [17, 17, 43, 15, "get"], [17, 20, 43, 18], [17, 21, 43, 19], [17, 22, 43, 20], [17, 23, 43, 21], [18, 6, 44, 4], [19, 6, 45, 4], [19, 13, 45, 11, "value"], [19, 18, 45, 16], [20, 4, 46, 2], [21, 4, 47, 2], [21, 13, 47, 11, "setValue"], [21, 21, 47, 19, "setValue"], [21, 22, 47, 20, "newValue"], [21, 30, 47, 31], [21, 32, 47, 39], [22, 6, 48, 4, "value"], [22, 11, 48, 9], [22, 14, 48, 12, "newValue"], [22, 22, 48, 20], [23, 6, 49, 4, "valueSet"], [23, 14, 49, 12], [23, 17, 49, 15], [23, 21, 49, 19], [24, 6, 50, 4, "Object"], [24, 12, 50, 10], [24, 13, 50, 11, "defineProperty"], [24, 27, 50, 25], [24, 28, 50, 26, "object"], [24, 34, 50, 32], [24, 36, 50, 34, "name"], [24, 40, 50, 38], [24, 42, 50, 40], [25, 8, 51, 6, "value"], [25, 13, 51, 11], [25, 15, 51, 13, "newValue"], [25, 23, 51, 21], [26, 8, 52, 6, "configurable"], [26, 20, 52, 18], [26, 22, 52, 20], [26, 26, 52, 24], [27, 8, 53, 6, "enumerable"], [27, 18, 53, 16], [28, 8, 54, 6, "writable"], [29, 6, 55, 4], [29, 7, 55, 5], [29, 8, 55, 6], [30, 4, 56, 2], [31, 4, 58, 2, "Object"], [31, 10, 58, 8], [31, 11, 58, 9, "defineProperty"], [31, 25, 58, 23], [31, 26, 58, 24, "object"], [31, 32, 58, 30], [31, 34, 58, 32, "name"], [31, 38, 58, 36], [31, 40, 58, 38], [32, 6, 59, 4, "get"], [32, 9, 59, 7], [32, 11, 59, 9, "getValue"], [32, 19, 59, 17], [33, 6, 60, 4, "set"], [33, 9, 60, 7], [33, 11, 60, 9, "setValue"], [33, 19, 60, 17], [34, 6, 61, 4, "configurable"], [34, 18, 61, 16], [34, 20, 61, 18], [34, 24, 61, 22], [35, 6, 62, 4, "enumerable"], [36, 4, 63, 2], [36, 5, 63, 3], [36, 6, 63, 4], [37, 2, 64, 0], [38, 2, 64, 1], [38, 6, 64, 1, "_default"], [38, 14, 64, 1], [38, 17, 64, 1, "exports"], [38, 24, 64, 1], [38, 25, 64, 1, "default"], [38, 32, 64, 1], [38, 35, 66, 15, "defineLazyObjectProperty"], [38, 59, 66, 39], [39, 0, 66, 39], [39, 3]], "functionMap": {"names": ["<global>", "defineLazyObjectProperty", "getValue", "setValue"], "mappings": "AAA;ACe;ECgB;GDc;EEC;GFS;CDQ"}}, "type": "js/module"}]}