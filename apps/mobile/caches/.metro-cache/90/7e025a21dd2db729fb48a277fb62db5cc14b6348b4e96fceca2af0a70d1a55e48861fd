{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Nfc = exports.default = (0, _createLucideIcon.default)(\"Nfc\", [[\"path\", {\n    d: \"M6 8.32a7.43 7.43 0 0 1 0 7.36\",\n    key: \"9iaqei\"\n  }], [\"path\", {\n    d: \"M9.46 6.21a11.76 11.76 0 0 1 0 11.58\",\n    key: \"1yha7l\"\n  }], [\"path\", {\n    d: \"M12.91 4.1a15.91 15.91 0 0 1 .01 15.8\",\n    key: \"4iu2gk\"\n  }], [\"path\", {\n    d: \"M16.37 2a20.16 20.16 0 0 1 0 20\",\n    key: \"sap9u2\"\n  }]]);\n});", "lineCount": 28, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Nfc"], [15, 11, 10, 9], [15, 14, 10, 9, "exports"], [15, 21, 10, 9], [15, 22, 10, 9, "default"], [15, 29, 10, 9], [15, 32, 10, 12], [15, 36, 10, 12, "createLucideIcon"], [15, 61, 10, 28], [15, 63, 10, 29], [15, 68, 10, 34], [15, 70, 10, 36], [15, 71, 11, 2], [15, 72, 11, 3], [15, 78, 11, 9], [15, 80, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 39, 11, 48], [17, 4, 11, 50, "key"], [17, 7, 11, 53], [17, 9, 11, 55], [18, 2, 11, 64], [18, 3, 11, 65], [18, 4, 11, 66], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 45, 12, 54], [20, 4, 12, 56, "key"], [20, 7, 12, 59], [20, 9, 12, 61], [21, 2, 12, 70], [21, 3, 12, 71], [21, 4, 12, 72], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 46, 13, 55], [23, 4, 13, 57, "key"], [23, 7, 13, 60], [23, 9, 13, 62], [24, 2, 13, 71], [24, 3, 13, 72], [24, 4, 13, 73], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 40, 14, 49], [26, 4, 14, 51, "key"], [26, 7, 14, 54], [26, 9, 14, 56], [27, 2, 14, 65], [27, 3, 14, 66], [27, 4, 14, 67], [27, 5, 15, 1], [27, 6, 15, 2], [28, 0, 15, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}