{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Monitor = exports.default = (0, _createLucideIcon.default)(\"Monitor\", [[\"rect\", {\n    width: \"20\",\n    height: \"14\",\n    x: \"2\",\n    y: \"3\",\n    rx: \"2\",\n    key: \"48i651\"\n  }], [\"line\", {\n    x1: \"8\",\n    x2: \"16\",\n    y1: \"21\",\n    y2: \"21\",\n    key: \"1svkeh\"\n  }], [\"line\", {\n    x1: \"12\",\n    x2: \"12\",\n    y1: \"17\",\n    y2: \"21\",\n    key: \"vw1qmm\"\n  }]]);\n});", "lineCount": 35, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Monitor"], [15, 15, 10, 13], [15, 18, 10, 13, "exports"], [15, 25, 10, 13], [15, 26, 10, 13, "default"], [15, 33, 10, 13], [15, 36, 10, 16], [15, 40, 10, 16, "createLucideIcon"], [15, 65, 10, 32], [15, 67, 10, 33], [15, 76, 10, 42], [15, 78, 10, 44], [15, 79, 11, 2], [15, 80, 11, 3], [15, 86, 11, 9], [15, 88, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 15, 11, 24], [17, 4, 11, 26, "height"], [17, 10, 11, 32], [17, 12, 11, 34], [17, 16, 11, 38], [18, 4, 11, 40, "x"], [18, 5, 11, 41], [18, 7, 11, 43], [18, 10, 11, 46], [19, 4, 11, 48, "y"], [19, 5, 11, 49], [19, 7, 11, 51], [19, 10, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "key"], [21, 7, 11, 68], [21, 9, 11, 70], [22, 2, 11, 79], [22, 3, 11, 80], [22, 4, 11, 81], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [22, 15, 12, 11], [23, 4, 12, 13, "x1"], [23, 6, 12, 15], [23, 8, 12, 17], [23, 11, 12, 20], [24, 4, 12, 22, "x2"], [24, 6, 12, 24], [24, 8, 12, 26], [24, 12, 12, 30], [25, 4, 12, 32, "y1"], [25, 6, 12, 34], [25, 8, 12, 36], [25, 12, 12, 40], [26, 4, 12, 42, "y2"], [26, 6, 12, 44], [26, 8, 12, 46], [26, 12, 12, 50], [27, 4, 12, 52, "key"], [27, 7, 12, 55], [27, 9, 12, 57], [28, 2, 12, 66], [28, 3, 12, 67], [28, 4, 12, 68], [28, 6, 13, 2], [28, 7, 13, 3], [28, 13, 13, 9], [28, 15, 13, 11], [29, 4, 13, 13, "x1"], [29, 6, 13, 15], [29, 8, 13, 17], [29, 12, 13, 21], [30, 4, 13, 23, "x2"], [30, 6, 13, 25], [30, 8, 13, 27], [30, 12, 13, 31], [31, 4, 13, 33, "y1"], [31, 6, 13, 35], [31, 8, 13, 37], [31, 12, 13, 41], [32, 4, 13, 43, "y2"], [32, 6, 13, 45], [32, 8, 13, 47], [32, 12, 13, 51], [33, 4, 13, 53, "key"], [33, 7, 13, 56], [33, 9, 13, 58], [34, 2, 13, 67], [34, 3, 13, 68], [34, 4, 13, 69], [34, 5, 14, 1], [34, 6, 14, 2], [35, 0, 14, 3], [35, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}