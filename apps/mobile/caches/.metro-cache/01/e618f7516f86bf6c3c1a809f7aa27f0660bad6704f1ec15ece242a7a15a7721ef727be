{"dependencies": [{"name": "./conversions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 20, "index": 20}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "AUDPhSp3SCRpotk+n4dj5MLm8fk=", "exportNames": ["*"]}}, {"name": "./route", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 14, "index": 60}, "end": {"line": 2, "column": 32, "index": 78}}], "key": "CgLF7khkD2MDURiduSWr++dKYzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  const conversions = require(_dependencyMap[0], \"./conversions\");\n  const route = require(_dependencyMap[1], \"./route\");\n  const convert = {};\n  const models = Object.keys(conversions);\n  function wrapRaw(fn) {\n    const wrappedFn = function (...args) {\n      const arg0 = args[0];\n      if (arg0 === undefined || arg0 === null) {\n        return arg0;\n      }\n      if (arg0.length > 1) {\n        args = arg0;\n      }\n      return fn(args);\n    };\n\n    // Preserve .conversion property if there is one\n    if ('conversion' in fn) {\n      wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n  }\n  function wrapRounded(fn) {\n    const wrappedFn = function (...args) {\n      const arg0 = args[0];\n      if (arg0 === undefined || arg0 === null) {\n        return arg0;\n      }\n      if (arg0.length > 1) {\n        args = arg0;\n      }\n      const result = fn(args);\n\n      // We're assuming the result is an array here.\n      // see notice in conversions.js; don't use box types\n      // in conversion functions.\n      if (typeof result === 'object') {\n        for (let len = result.length, i = 0; i < len; i++) {\n          result[i] = Math.round(result[i]);\n        }\n      }\n      return result;\n    };\n\n    // Preserve .conversion property if there is one\n    if ('conversion' in fn) {\n      wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n  }\n  models.forEach(fromModel => {\n    convert[fromModel] = {};\n    Object.defineProperty(convert[fromModel], 'channels', {\n      value: conversions[fromModel].channels\n    });\n    Object.defineProperty(convert[fromModel], 'labels', {\n      value: conversions[fromModel].labels\n    });\n    const routes = route(fromModel);\n    const routeModels = Object.keys(routes);\n    routeModels.forEach(toModel => {\n      const fn = routes[toModel];\n      convert[fromModel][toModel] = wrapRounded(fn);\n      convert[fromModel][toModel].raw = wrapRaw(fn);\n    });\n  });\n  module.exports = convert;\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 8, 1, 6, "conversions"], [2, 19, 1, 17], [2, 22, 1, 20, "require"], [2, 29, 1, 27], [2, 30, 1, 27, "_dependencyMap"], [2, 44, 1, 27], [2, 64, 1, 43], [2, 65, 1, 44], [3, 2, 2, 0], [3, 8, 2, 6, "route"], [3, 13, 2, 11], [3, 16, 2, 14, "require"], [3, 23, 2, 21], [3, 24, 2, 21, "_dependencyMap"], [3, 38, 2, 21], [3, 52, 2, 31], [3, 53, 2, 32], [4, 2, 4, 0], [4, 8, 4, 6, "convert"], [4, 15, 4, 13], [4, 18, 4, 16], [4, 19, 4, 17], [4, 20, 4, 18], [5, 2, 6, 0], [5, 8, 6, 6, "models"], [5, 14, 6, 12], [5, 17, 6, 15, "Object"], [5, 23, 6, 21], [5, 24, 6, 22, "keys"], [5, 28, 6, 26], [5, 29, 6, 27, "conversions"], [5, 40, 6, 38], [5, 41, 6, 39], [6, 2, 8, 0], [6, 11, 8, 9, "wrapRaw"], [6, 18, 8, 16, "wrapRaw"], [6, 19, 8, 17, "fn"], [6, 21, 8, 19], [6, 23, 8, 21], [7, 4, 9, 1], [7, 10, 9, 7, "wrappedFn"], [7, 19, 9, 16], [7, 22, 9, 19], [7, 31, 9, 19, "wrappedFn"], [7, 32, 9, 29], [7, 35, 9, 32, "args"], [7, 39, 9, 36], [7, 41, 9, 38], [8, 6, 10, 2], [8, 12, 10, 8, "arg0"], [8, 16, 10, 12], [8, 19, 10, 15, "args"], [8, 23, 10, 19], [8, 24, 10, 20], [8, 25, 10, 21], [8, 26, 10, 22], [9, 6, 11, 2], [9, 10, 11, 6, "arg0"], [9, 14, 11, 10], [9, 19, 11, 15, "undefined"], [9, 28, 11, 24], [9, 32, 11, 28, "arg0"], [9, 36, 11, 32], [9, 41, 11, 37], [9, 45, 11, 41], [9, 47, 11, 43], [10, 8, 12, 3], [10, 15, 12, 10, "arg0"], [10, 19, 12, 14], [11, 6, 13, 2], [12, 6, 15, 2], [12, 10, 15, 6, "arg0"], [12, 14, 15, 10], [12, 15, 15, 11, "length"], [12, 21, 15, 17], [12, 24, 15, 20], [12, 25, 15, 21], [12, 27, 15, 23], [13, 8, 16, 3, "args"], [13, 12, 16, 7], [13, 15, 16, 10, "arg0"], [13, 19, 16, 14], [14, 6, 17, 2], [15, 6, 19, 2], [15, 13, 19, 9, "fn"], [15, 15, 19, 11], [15, 16, 19, 12, "args"], [15, 20, 19, 16], [15, 21, 19, 17], [16, 4, 20, 1], [16, 5, 20, 2], [18, 4, 22, 1], [19, 4, 23, 1], [19, 8, 23, 5], [19, 20, 23, 17], [19, 24, 23, 21, "fn"], [19, 26, 23, 23], [19, 28, 23, 25], [20, 6, 24, 2, "wrappedFn"], [20, 15, 24, 11], [20, 16, 24, 12, "conversion"], [20, 26, 24, 22], [20, 29, 24, 25, "fn"], [20, 31, 24, 27], [20, 32, 24, 28, "conversion"], [20, 42, 24, 38], [21, 4, 25, 1], [22, 4, 27, 1], [22, 11, 27, 8, "wrappedFn"], [22, 20, 27, 17], [23, 2, 28, 0], [24, 2, 30, 0], [24, 11, 30, 9, "wrapRounded"], [24, 22, 30, 20, "wrapRounded"], [24, 23, 30, 21, "fn"], [24, 25, 30, 23], [24, 27, 30, 25], [25, 4, 31, 1], [25, 10, 31, 7, "wrappedFn"], [25, 19, 31, 16], [25, 22, 31, 19], [25, 31, 31, 19, "wrappedFn"], [25, 32, 31, 29], [25, 35, 31, 32, "args"], [25, 39, 31, 36], [25, 41, 31, 38], [26, 6, 32, 2], [26, 12, 32, 8, "arg0"], [26, 16, 32, 12], [26, 19, 32, 15, "args"], [26, 23, 32, 19], [26, 24, 32, 20], [26, 25, 32, 21], [26, 26, 32, 22], [27, 6, 34, 2], [27, 10, 34, 6, "arg0"], [27, 14, 34, 10], [27, 19, 34, 15, "undefined"], [27, 28, 34, 24], [27, 32, 34, 28, "arg0"], [27, 36, 34, 32], [27, 41, 34, 37], [27, 45, 34, 41], [27, 47, 34, 43], [28, 8, 35, 3], [28, 15, 35, 10, "arg0"], [28, 19, 35, 14], [29, 6, 36, 2], [30, 6, 38, 2], [30, 10, 38, 6, "arg0"], [30, 14, 38, 10], [30, 15, 38, 11, "length"], [30, 21, 38, 17], [30, 24, 38, 20], [30, 25, 38, 21], [30, 27, 38, 23], [31, 8, 39, 3, "args"], [31, 12, 39, 7], [31, 15, 39, 10, "arg0"], [31, 19, 39, 14], [32, 6, 40, 2], [33, 6, 42, 2], [33, 12, 42, 8, "result"], [33, 18, 42, 14], [33, 21, 42, 17, "fn"], [33, 23, 42, 19], [33, 24, 42, 20, "args"], [33, 28, 42, 24], [33, 29, 42, 25], [35, 6, 44, 2], [36, 6, 45, 2], [37, 6, 46, 2], [38, 6, 47, 2], [38, 10, 47, 6], [38, 17, 47, 13, "result"], [38, 23, 47, 19], [38, 28, 47, 24], [38, 36, 47, 32], [38, 38, 47, 34], [39, 8, 48, 3], [39, 13, 48, 8], [39, 17, 48, 12, "len"], [39, 20, 48, 15], [39, 23, 48, 18, "result"], [39, 29, 48, 24], [39, 30, 48, 25, "length"], [39, 36, 48, 31], [39, 38, 48, 33, "i"], [39, 39, 48, 34], [39, 42, 48, 37], [39, 43, 48, 38], [39, 45, 48, 40, "i"], [39, 46, 48, 41], [39, 49, 48, 44, "len"], [39, 52, 48, 47], [39, 54, 48, 49, "i"], [39, 55, 48, 50], [39, 57, 48, 52], [39, 59, 48, 54], [40, 10, 49, 4, "result"], [40, 16, 49, 10], [40, 17, 49, 11, "i"], [40, 18, 49, 12], [40, 19, 49, 13], [40, 22, 49, 16, "Math"], [40, 26, 49, 20], [40, 27, 49, 21, "round"], [40, 32, 49, 26], [40, 33, 49, 27, "result"], [40, 39, 49, 33], [40, 40, 49, 34, "i"], [40, 41, 49, 35], [40, 42, 49, 36], [40, 43, 49, 37], [41, 8, 50, 3], [42, 6, 51, 2], [43, 6, 53, 2], [43, 13, 53, 9, "result"], [43, 19, 53, 15], [44, 4, 54, 1], [44, 5, 54, 2], [46, 4, 56, 1], [47, 4, 57, 1], [47, 8, 57, 5], [47, 20, 57, 17], [47, 24, 57, 21, "fn"], [47, 26, 57, 23], [47, 28, 57, 25], [48, 6, 58, 2, "wrappedFn"], [48, 15, 58, 11], [48, 16, 58, 12, "conversion"], [48, 26, 58, 22], [48, 29, 58, 25, "fn"], [48, 31, 58, 27], [48, 32, 58, 28, "conversion"], [48, 42, 58, 38], [49, 4, 59, 1], [50, 4, 61, 1], [50, 11, 61, 8, "wrappedFn"], [50, 20, 61, 17], [51, 2, 62, 0], [52, 2, 64, 0, "models"], [52, 8, 64, 6], [52, 9, 64, 7, "for<PERSON>ach"], [52, 16, 64, 14], [52, 17, 64, 15, "fromModel"], [52, 26, 64, 24], [52, 30, 64, 28], [53, 4, 65, 1, "convert"], [53, 11, 65, 8], [53, 12, 65, 9, "fromModel"], [53, 21, 65, 18], [53, 22, 65, 19], [53, 25, 65, 22], [53, 26, 65, 23], [53, 27, 65, 24], [54, 4, 67, 1, "Object"], [54, 10, 67, 7], [54, 11, 67, 8, "defineProperty"], [54, 25, 67, 22], [54, 26, 67, 23, "convert"], [54, 33, 67, 30], [54, 34, 67, 31, "fromModel"], [54, 43, 67, 40], [54, 44, 67, 41], [54, 46, 67, 43], [54, 56, 67, 53], [54, 58, 67, 55], [55, 6, 67, 56, "value"], [55, 11, 67, 61], [55, 13, 67, 63, "conversions"], [55, 24, 67, 74], [55, 25, 67, 75, "fromModel"], [55, 34, 67, 84], [55, 35, 67, 85], [55, 36, 67, 86, "channels"], [56, 4, 67, 94], [56, 5, 67, 95], [56, 6, 67, 96], [57, 4, 68, 1, "Object"], [57, 10, 68, 7], [57, 11, 68, 8, "defineProperty"], [57, 25, 68, 22], [57, 26, 68, 23, "convert"], [57, 33, 68, 30], [57, 34, 68, 31, "fromModel"], [57, 43, 68, 40], [57, 44, 68, 41], [57, 46, 68, 43], [57, 54, 68, 51], [57, 56, 68, 53], [58, 6, 68, 54, "value"], [58, 11, 68, 59], [58, 13, 68, 61, "conversions"], [58, 24, 68, 72], [58, 25, 68, 73, "fromModel"], [58, 34, 68, 82], [58, 35, 68, 83], [58, 36, 68, 84, "labels"], [59, 4, 68, 90], [59, 5, 68, 91], [59, 6, 68, 92], [60, 4, 70, 1], [60, 10, 70, 7, "routes"], [60, 16, 70, 13], [60, 19, 70, 16, "route"], [60, 24, 70, 21], [60, 25, 70, 22, "fromModel"], [60, 34, 70, 31], [60, 35, 70, 32], [61, 4, 71, 1], [61, 10, 71, 7, "routeModels"], [61, 21, 71, 18], [61, 24, 71, 21, "Object"], [61, 30, 71, 27], [61, 31, 71, 28, "keys"], [61, 35, 71, 32], [61, 36, 71, 33, "routes"], [61, 42, 71, 39], [61, 43, 71, 40], [62, 4, 73, 1, "routeModels"], [62, 15, 73, 12], [62, 16, 73, 13, "for<PERSON>ach"], [62, 23, 73, 20], [62, 24, 73, 21, "toModel"], [62, 31, 73, 28], [62, 35, 73, 32], [63, 6, 74, 2], [63, 12, 74, 8, "fn"], [63, 14, 74, 10], [63, 17, 74, 13, "routes"], [63, 23, 74, 19], [63, 24, 74, 20, "toModel"], [63, 31, 74, 27], [63, 32, 74, 28], [64, 6, 76, 2, "convert"], [64, 13, 76, 9], [64, 14, 76, 10, "fromModel"], [64, 23, 76, 19], [64, 24, 76, 20], [64, 25, 76, 21, "toModel"], [64, 32, 76, 28], [64, 33, 76, 29], [64, 36, 76, 32, "wrapRounded"], [64, 47, 76, 43], [64, 48, 76, 44, "fn"], [64, 50, 76, 46], [64, 51, 76, 47], [65, 6, 77, 2, "convert"], [65, 13, 77, 9], [65, 14, 77, 10, "fromModel"], [65, 23, 77, 19], [65, 24, 77, 20], [65, 25, 77, 21, "toModel"], [65, 32, 77, 28], [65, 33, 77, 29], [65, 34, 77, 30, "raw"], [65, 37, 77, 33], [65, 40, 77, 36, "wrapRaw"], [65, 47, 77, 43], [65, 48, 77, 44, "fn"], [65, 50, 77, 46], [65, 51, 77, 47], [66, 4, 78, 1], [66, 5, 78, 2], [66, 6, 78, 3], [67, 2, 79, 0], [67, 3, 79, 1], [67, 4, 79, 2], [68, 2, 81, 0, "module"], [68, 8, 81, 6], [68, 9, 81, 7, "exports"], [68, 16, 81, 14], [68, 19, 81, 17, "convert"], [68, 26, 81, 24], [69, 0, 81, 25], [69, 3]], "functionMap": {"names": ["<global>", "wrapRaw", "wrappedFn", "wrapRounded", "models.forEach$argument_0", "routeModels.forEach$argument_0"], "mappings": "AAA;ACO;mBCC;EDW;CDQ;AGE;mBDC;ECuB;CHQ;eIE;qBCS;EDK;CJC"}}, "type": "js/module"}]}