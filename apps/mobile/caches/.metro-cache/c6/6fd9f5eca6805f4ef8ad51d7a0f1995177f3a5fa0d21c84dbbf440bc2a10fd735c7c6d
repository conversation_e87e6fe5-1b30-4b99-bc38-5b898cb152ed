{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const Refrigerator = exports.default = (0, _createLucideIcon.default)(\"Refrigerator\", [[\"path\", {\n    d: \"M5 6a4 4 0 0 1 4-4h6a4 4 0 0 1 4 4v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6Z\",\n    key: \"fpq118\"\n  }], [\"path\", {\n    d: \"M5 10h14\",\n    key: \"elsbfy\"\n  }], [\"path\", {\n    d: \"M15 7v6\",\n    key: \"1nx30x\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "Refrigerator"], [15, 20, 10, 18], [15, 23, 10, 18, "exports"], [15, 30, 10, 18], [15, 31, 10, 18, "default"], [15, 38, 10, 18], [15, 41, 10, 21], [15, 45, 10, 21, "createLucideIcon"], [15, 70, 10, 37], [15, 72, 10, 38], [15, 86, 10, 52], [15, 88, 10, 54], [15, 89, 11, 2], [15, 90, 12, 4], [15, 96, 12, 10], [15, 98, 13, 4], [16, 4, 13, 6, "d"], [16, 5, 13, 7], [16, 7, 13, 9], [16, 79, 13, 81], [17, 4, 13, 83, "key"], [17, 7, 13, 86], [17, 9, 13, 88], [18, 2, 13, 97], [18, 3, 13, 98], [18, 4, 14, 3], [18, 6, 15, 2], [18, 7, 15, 3], [18, 13, 15, 9], [18, 15, 15, 11], [19, 4, 15, 13, "d"], [19, 5, 15, 14], [19, 7, 15, 16], [19, 17, 15, 26], [20, 4, 15, 28, "key"], [20, 7, 15, 31], [20, 9, 15, 33], [21, 2, 15, 42], [21, 3, 15, 43], [21, 4, 15, 44], [21, 6, 16, 2], [21, 7, 16, 3], [21, 13, 16, 9], [21, 15, 16, 11], [22, 4, 16, 13, "d"], [22, 5, 16, 14], [22, 7, 16, 16], [22, 16, 16, 25], [23, 4, 16, 27, "key"], [23, 7, 16, 30], [23, 9, 16, 32], [24, 2, 16, 41], [24, 3, 16, 42], [24, 4, 16, 43], [24, 5, 17, 1], [24, 6, 17, 2], [25, 0, 17, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}