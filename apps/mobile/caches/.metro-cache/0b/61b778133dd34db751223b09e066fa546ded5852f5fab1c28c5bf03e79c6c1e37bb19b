{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Core/InitializeCore", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 89}, "end": {"line": 2, "column": 52, "index": 141}}], "key": "464jGO4ZdUhcG1Xq7Odwj9qOTdk=", "exportNames": ["*"]}}, {"name": "whatwg-fetch", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 259}, "end": {"line": 5, "column": 22, "index": 281}}], "key": "bkTjGN2MZKvQ2THKk7fF742jdKk=", "exportNames": ["*"]}}, {"name": "expo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 335}, "end": {"line": 7, "column": 14, "index": 349}}], "key": "PWvtvXU7MaET6Yd1Gn8oQOXJQ8A=", "exportNames": ["*"]}}, {"name": "expo-constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 464}, "end": {"line": 10, "column": 39, "index": 503}}], "key": "pPv5KzfRT0rL6NCr7G9k0o4d1W8=", "exportNames": ["*"]}}, {"name": "./Location", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 505}, "end": {"line": 12, "column": 54, "index": 559}}], "key": "f704Yb9XZ6TVx9B+7NEY1aCHIlY=", "exportNames": ["*"]}}, {"name": "../getDevServer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 560}, "end": {"line": 13, "column": 43, "index": 603}}], "key": "a6SsyENWfMcaCRMYVoTbrDzHQH0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.wrapFetchWithWindowLocation = wrapFetchWithWindowLocation;\n  require(_dependencyMap[1], \"react-native/Libraries/Core/InitializeCore\");\n  require(_dependencyMap[2], \"whatwg-fetch\");\n  require(_dependencyMap[3], \"expo\");\n  var _expoConstants = _interopRequireDefault(require(_dependencyMap[4], \"expo-constants\"));\n  var _Location = require(_dependencyMap[5], \"./Location\");\n  var _getDevServer = _interopRequireDefault(require(_dependencyMap[6], \"../getDevServer\"));\n  // This MUST be first to ensure that `fetch` is defined in the React Native environment.\n\n  // Ensure fetch is installed before adding our fetch polyfill to ensure Headers and Request are available globally.\n\n  // This MUST be imported to ensure URL is installed.\n\n  // This file configures the runtime environment to increase compatibility with WinterCG.\n  // https://wintercg.org/\n\n  var manifest = _expoConstants.default.expoConfig;\n  function getOrigin() {\n    if (process.env.NODE_ENV !== 'production') {\n      // e.g. http://localhost:8081\n      return (0, _getDevServer.default)().url;\n    }\n    var extra = manifest?.extra;\n    return extra?.router?.origin ??\n    // Written automatically during release builds.\n    extra?.router?.generatedOrigin;\n  }\n\n  // TODO: This would be better if native and tied as close to the JS engine as possible, i.e. it should\n  // reflect the exact location of the JS file that was executed.\n  function getBaseUrl() {\n    // TODO: Make it official by moving out of `extra`\n    var productionBaseUrl = getOrigin();\n    if (!productionBaseUrl) {\n      return null;\n    }\n\n    // Ensure no trailing slash\n    return productionBaseUrl?.replace(/\\/$/, '');\n  }\n  var polyfillSymbol = Symbol.for('expo.polyfillFetchWithWindowLocation');\n  function wrapFetchWithWindowLocation(fetch) {\n    if (fetch[polyfillSymbol]) {\n      return fetch;\n    }\n    var _fetch = function () {\n      for (var _len = arguments.length, props = new Array(_len), _key = 0; _key < _len; _key++) {\n        props[_key] = arguments[_key];\n      }\n      if (props[0] && typeof props[0] === 'string' && props[0].startsWith('/')) {\n        props[0] = new URL(props[0], window.location?.origin).toString();\n      } else if (props[0] && typeof props[0] === 'object') {\n        if (props[0].url && typeof props[0].url === 'string' && props[0].url.startsWith('/')) {\n          props[0].url = new URL(props[0].url, window.location?.origin).toString();\n        }\n      }\n      return fetch(...props);\n    };\n    _fetch[polyfillSymbol] = true;\n    return _fetch;\n  }\n  var extra = manifest?.extra;\n  if (extra?.router?.origin !== false) {\n    // Polyfill window.location in native runtimes.\n    if (typeof window !== 'undefined' && !window.location) {\n      var url = getBaseUrl();\n      if (url) {\n        (0, _Location.setLocationHref)(url);\n        (0, _Location.install)();\n      }\n    }\n\n    // Polyfill native fetch to support relative URLs\n    Object.defineProperty(global, 'fetch', {\n      // value: fetch,\n      value: wrapFetchWithWindowLocation(fetch)\n    });\n  } else {\n    // Polyfill native fetch to support relative URLs\n    Object.defineProperty(global, 'fetch', {\n      value: fetch\n    });\n  }\n});", "lineCount": 89, "map": [[7, 2, 2, 0, "require"], [7, 9, 2, 0], [7, 10, 2, 0, "_dependencyMap"], [7, 24, 2, 0], [8, 2, 5, 0, "require"], [8, 9, 5, 0], [8, 10, 5, 0, "_dependencyMap"], [8, 24, 5, 0], [9, 2, 7, 0, "require"], [9, 9, 7, 0], [9, 10, 7, 0, "_dependencyMap"], [9, 24, 7, 0], [10, 2, 10, 0], [10, 6, 10, 0, "_expoConstants"], [10, 20, 10, 0], [10, 23, 10, 0, "_interopRequireDefault"], [10, 45, 10, 0], [10, 46, 10, 0, "require"], [10, 53, 10, 0], [10, 54, 10, 0, "_dependencyMap"], [10, 68, 10, 0], [11, 2, 12, 0], [11, 6, 12, 0, "_Location"], [11, 15, 12, 0], [11, 18, 12, 0, "require"], [11, 25, 12, 0], [11, 26, 12, 0, "_dependencyMap"], [11, 40, 12, 0], [12, 2, 13, 0], [12, 6, 13, 0, "_getDevServer"], [12, 19, 13, 0], [12, 22, 13, 0, "_interopRequireDefault"], [12, 44, 13, 0], [12, 45, 13, 0, "require"], [12, 52, 13, 0], [12, 53, 13, 0, "_dependencyMap"], [12, 67, 13, 0], [13, 2, 1, 0], [15, 2, 4, 0], [17, 2, 6, 0], [19, 2, 8, 0], [20, 2, 9, 0], [22, 2, 22, 0], [22, 6, 22, 6, "manifest"], [22, 14, 22, 14], [22, 17, 22, 17, "Constants"], [22, 39, 22, 26], [22, 40, 22, 27, "expoConfig"], [22, 50, 22, 37], [23, 2, 24, 0], [23, 11, 24, 9, "<PERSON><PERSON><PERSON><PERSON>"], [23, 20, 24, 18, "<PERSON><PERSON><PERSON><PERSON>"], [23, 21, 24, 18], [23, 23, 24, 21], [24, 4, 25, 2], [24, 8, 25, 6, "process"], [24, 15, 25, 13], [24, 16, 25, 14, "env"], [24, 19, 25, 17], [24, 20, 25, 18, "NODE_ENV"], [24, 28, 25, 26], [24, 33, 25, 31], [24, 45, 25, 43], [24, 47, 25, 45], [25, 6, 26, 4], [26, 6, 27, 4], [26, 13, 27, 11], [26, 17, 27, 11, "getDevServer"], [26, 38, 27, 23], [26, 40, 27, 24], [26, 41, 27, 25], [26, 42, 27, 26, "url"], [26, 45, 27, 29], [27, 4, 28, 2], [28, 4, 29, 2], [28, 8, 29, 8, "extra"], [28, 13, 29, 13], [28, 16, 29, 16, "manifest"], [28, 24, 29, 24], [28, 26, 29, 26, "extra"], [28, 31, 29, 63], [29, 4, 30, 2], [29, 11, 31, 4, "extra"], [29, 16, 31, 9], [29, 18, 31, 11, "router"], [29, 24, 31, 17], [29, 26, 31, 19, "origin"], [29, 32, 31, 25], [30, 4, 32, 4], [31, 4, 33, 4, "extra"], [31, 9, 33, 9], [31, 11, 33, 11, "router"], [31, 17, 33, 17], [31, 19, 33, 19, "generated<PERSON><PERSON>in"], [31, 34, 33, 34], [32, 2, 35, 0], [34, 2, 37, 0], [35, 2, 38, 0], [36, 2, 39, 0], [36, 11, 39, 9, "getBaseUrl"], [36, 21, 39, 19, "getBaseUrl"], [36, 22, 39, 19], [36, 24, 39, 22], [37, 4, 40, 2], [38, 4, 41, 2], [38, 8, 41, 8, "productionBaseUrl"], [38, 25, 41, 25], [38, 28, 41, 28, "<PERSON><PERSON><PERSON><PERSON>"], [38, 37, 41, 37], [38, 38, 41, 38], [38, 39, 41, 39], [39, 4, 43, 2], [39, 8, 43, 6], [39, 9, 43, 7, "productionBaseUrl"], [39, 26, 43, 24], [39, 28, 43, 26], [40, 6, 44, 4], [40, 13, 44, 11], [40, 17, 44, 15], [41, 4, 45, 2], [43, 4, 47, 2], [44, 4, 48, 2], [44, 11, 48, 9, "productionBaseUrl"], [44, 28, 48, 26], [44, 30, 48, 28, "replace"], [44, 37, 48, 35], [44, 38, 48, 36], [44, 43, 48, 41], [44, 45, 48, 43], [44, 47, 48, 45], [44, 48, 48, 46], [45, 2, 49, 0], [46, 2, 51, 0], [46, 6, 51, 6, "polyfillSymbol"], [46, 20, 51, 20], [46, 23, 51, 23, "Symbol"], [46, 29, 51, 29], [46, 30, 51, 30, "for"], [46, 33, 51, 33], [46, 34, 51, 34], [46, 72, 51, 72], [46, 73, 51, 73], [47, 2, 53, 7], [47, 11, 53, 16, "wrapFetchWithWindowLocation"], [47, 38, 53, 43, "wrapFetchWithWindowLocation"], [47, 39, 53, 44, "fetch"], [47, 44, 53, 92], [47, 46, 53, 94], [48, 4, 54, 2], [48, 8, 54, 6, "fetch"], [48, 13, 54, 11], [48, 14, 54, 12, "polyfillSymbol"], [48, 28, 54, 26], [48, 29, 54, 27], [48, 31, 54, 29], [49, 6, 55, 4], [49, 13, 55, 11, "fetch"], [49, 18, 55, 16], [50, 4, 56, 2], [51, 4, 58, 2], [51, 8, 58, 8, "_fetch"], [51, 14, 58, 14], [51, 17, 58, 17], [51, 26, 58, 17, "_fetch"], [51, 27, 58, 17], [51, 29, 58, 38], [52, 6, 58, 38], [52, 15, 58, 38, "_len"], [52, 19, 58, 38], [52, 22, 58, 38, "arguments"], [52, 31, 58, 38], [52, 32, 58, 38, "length"], [52, 38, 58, 38], [52, 40, 58, 21, "props"], [52, 45, 58, 26], [52, 52, 58, 26, "Array"], [52, 57, 58, 26], [52, 58, 58, 26, "_len"], [52, 62, 58, 26], [52, 65, 58, 26, "_key"], [52, 69, 58, 26], [52, 75, 58, 26, "_key"], [52, 79, 58, 26], [52, 82, 58, 26, "_len"], [52, 86, 58, 26], [52, 88, 58, 26, "_key"], [52, 92, 58, 26], [53, 8, 58, 21, "props"], [53, 13, 58, 26], [53, 14, 58, 26, "_key"], [53, 18, 58, 26], [53, 22, 58, 26, "arguments"], [53, 31, 58, 26], [53, 32, 58, 26, "_key"], [53, 36, 58, 26], [54, 6, 58, 26], [55, 6, 59, 4], [55, 10, 59, 8, "props"], [55, 15, 59, 13], [55, 16, 59, 14], [55, 17, 59, 15], [55, 18, 59, 16], [55, 22, 59, 20], [55, 29, 59, 27, "props"], [55, 34, 59, 32], [55, 35, 59, 33], [55, 36, 59, 34], [55, 37, 59, 35], [55, 42, 59, 40], [55, 50, 59, 48], [55, 54, 59, 52, "props"], [55, 59, 59, 57], [55, 60, 59, 58], [55, 61, 59, 59], [55, 62, 59, 60], [55, 63, 59, 61, "startsWith"], [55, 73, 59, 71], [55, 74, 59, 72], [55, 77, 59, 75], [55, 78, 59, 76], [55, 80, 59, 78], [56, 8, 60, 6, "props"], [56, 13, 60, 11], [56, 14, 60, 12], [56, 15, 60, 13], [56, 16, 60, 14], [56, 19, 60, 17], [56, 23, 60, 21, "URL"], [56, 26, 60, 24], [56, 27, 60, 25, "props"], [56, 32, 60, 30], [56, 33, 60, 31], [56, 34, 60, 32], [56, 35, 60, 33], [56, 37, 60, 35, "window"], [56, 43, 60, 41], [56, 44, 60, 42, "location"], [56, 52, 60, 50], [56, 54, 60, 52, "origin"], [56, 60, 60, 58], [56, 61, 60, 59], [56, 62, 60, 60, "toString"], [56, 70, 60, 68], [56, 71, 60, 69], [56, 72, 60, 70], [57, 6, 61, 4], [57, 7, 61, 5], [57, 13, 61, 11], [57, 17, 61, 15, "props"], [57, 22, 61, 20], [57, 23, 61, 21], [57, 24, 61, 22], [57, 25, 61, 23], [57, 29, 61, 27], [57, 36, 61, 34, "props"], [57, 41, 61, 39], [57, 42, 61, 40], [57, 43, 61, 41], [57, 44, 61, 42], [57, 49, 61, 47], [57, 57, 61, 55], [57, 59, 61, 57], [58, 8, 62, 6], [58, 12, 62, 10, "props"], [58, 17, 62, 15], [58, 18, 62, 16], [58, 19, 62, 17], [58, 20, 62, 18], [58, 21, 62, 19, "url"], [58, 24, 62, 22], [58, 28, 62, 26], [58, 35, 62, 33, "props"], [58, 40, 62, 38], [58, 41, 62, 39], [58, 42, 62, 40], [58, 43, 62, 41], [58, 44, 62, 42, "url"], [58, 47, 62, 45], [58, 52, 62, 50], [58, 60, 62, 58], [58, 64, 62, 62, "props"], [58, 69, 62, 67], [58, 70, 62, 68], [58, 71, 62, 69], [58, 72, 62, 70], [58, 73, 62, 71, "url"], [58, 76, 62, 74], [58, 77, 62, 75, "startsWith"], [58, 87, 62, 85], [58, 88, 62, 86], [58, 91, 62, 89], [58, 92, 62, 90], [58, 94, 62, 92], [59, 10, 63, 8, "props"], [59, 15, 63, 13], [59, 16, 63, 14], [59, 17, 63, 15], [59, 18, 63, 16], [59, 19, 63, 17, "url"], [59, 22, 63, 20], [59, 25, 63, 23], [59, 29, 63, 27, "URL"], [59, 32, 63, 30], [59, 33, 63, 31, "props"], [59, 38, 63, 36], [59, 39, 63, 37], [59, 40, 63, 38], [59, 41, 63, 39], [59, 42, 63, 40, "url"], [59, 45, 63, 43], [59, 47, 63, 45, "window"], [59, 53, 63, 51], [59, 54, 63, 52, "location"], [59, 62, 63, 60], [59, 64, 63, 62, "origin"], [59, 70, 63, 68], [59, 71, 63, 69], [59, 72, 63, 70, "toString"], [59, 80, 63, 78], [59, 81, 63, 79], [59, 82, 63, 80], [60, 8, 64, 6], [61, 6, 65, 4], [62, 6, 66, 4], [62, 13, 66, 11, "fetch"], [62, 18, 66, 16], [62, 19, 66, 17], [62, 22, 66, 20, "props"], [62, 27, 66, 25], [62, 28, 66, 26], [63, 4, 67, 2], [63, 5, 67, 3], [64, 4, 69, 2, "_fetch"], [64, 10, 69, 8], [64, 11, 69, 9, "polyfillSymbol"], [64, 25, 69, 23], [64, 26, 69, 24], [64, 29, 69, 27], [64, 33, 69, 31], [65, 4, 71, 2], [65, 11, 71, 9, "_fetch"], [65, 17, 71, 15], [66, 2, 72, 0], [67, 2, 74, 0], [67, 6, 74, 6, "extra"], [67, 11, 74, 11], [67, 14, 74, 14, "manifest"], [67, 22, 74, 22], [67, 24, 74, 24, "extra"], [67, 29, 74, 61], [68, 2, 75, 0], [68, 6, 75, 4, "extra"], [68, 11, 75, 9], [68, 13, 75, 11, "router"], [68, 19, 75, 17], [68, 21, 75, 19, "origin"], [68, 27, 75, 25], [68, 32, 75, 30], [68, 37, 75, 35], [68, 39, 75, 37], [69, 4, 76, 2], [70, 4, 77, 2], [70, 8, 77, 6], [70, 15, 77, 13, "window"], [70, 21, 77, 19], [70, 26, 77, 24], [70, 37, 77, 35], [70, 41, 77, 39], [70, 42, 77, 40, "window"], [70, 48, 77, 46], [70, 49, 77, 47, "location"], [70, 57, 77, 55], [70, 59, 77, 57], [71, 6, 78, 4], [71, 10, 78, 10, "url"], [71, 13, 78, 13], [71, 16, 78, 16, "getBaseUrl"], [71, 26, 78, 26], [71, 27, 78, 27], [71, 28, 78, 28], [72, 6, 79, 4], [72, 10, 79, 8, "url"], [72, 13, 79, 11], [72, 15, 79, 13], [73, 8, 80, 6], [73, 12, 80, 6, "setLocationHref"], [73, 37, 80, 21], [73, 39, 80, 22, "url"], [73, 42, 80, 25], [73, 43, 80, 26], [74, 8, 81, 6], [74, 12, 81, 6, "install"], [74, 29, 81, 13], [74, 31, 81, 14], [74, 32, 81, 15], [75, 6, 82, 4], [76, 4, 83, 2], [78, 4, 85, 2], [79, 4, 86, 2, "Object"], [79, 10, 86, 8], [79, 11, 86, 9, "defineProperty"], [79, 25, 86, 23], [79, 26, 86, 24, "global"], [79, 32, 86, 30], [79, 34, 86, 32], [79, 41, 86, 39], [79, 43, 86, 41], [80, 6, 87, 4], [81, 6, 88, 4, "value"], [81, 11, 88, 9], [81, 13, 88, 11, "wrapFetchWithWindowLocation"], [81, 40, 88, 38], [81, 41, 88, 39, "fetch"], [81, 46, 88, 44], [82, 4, 89, 2], [82, 5, 89, 3], [82, 6, 89, 4], [83, 2, 90, 0], [83, 3, 90, 1], [83, 9, 90, 7], [84, 4, 91, 2], [85, 4, 92, 2, "Object"], [85, 10, 92, 8], [85, 11, 92, 9, "defineProperty"], [85, 25, 92, 23], [85, 26, 92, 24, "global"], [85, 32, 92, 30], [85, 34, 92, 32], [85, 41, 92, 39], [85, 43, 92, 41], [86, 6, 93, 4, "value"], [86, 11, 93, 9], [86, 13, 93, 11, "fetch"], [87, 4, 94, 2], [87, 5, 94, 3], [87, 6, 94, 4], [88, 2, 95, 0], [89, 0, 95, 1], [89, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>", "getBaseUrl", "wrapFetchWithWindowLocation", "_fetch"], "mappings": "AAA;ACuB;CDW;AEI;CFU;OGI;iBCK;GDS;CHK"}}, "type": "js/module"}]}