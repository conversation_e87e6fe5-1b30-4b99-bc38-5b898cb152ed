import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Mic, Square, MicOff } from 'lucide-react-native';
import { useColors } from './useColors';
import AudioVisualizer from './AudioVisualizer';
import RealtimeTranscript from './RealtimeTranscript';

export default function VoiceMode({ isRecording, hasPermission, isLoading, transcript, isMuted, onMute }) {
  const colors = useColors();
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    // Simulate speaking state for the visualizer
    // Only show as speaking if recording and not muted
    if (isRecording && !isMuted) {
      setIsSpeaking(true);
    } else {
      setIsSpeaking(false);
    }
  }, [isRecording, isMuted]);

  return (
    <View style={{ flex: 1 }}>
      <AudioVisualizer isSpeaking={isSpeaking} />
      <RealtimeTranscript transcript={transcript} />

      {/* Status indicator - centered at bottom */}
      <View
        style={{
          position: 'absolute',
          bottom: 32,
          left: 0,
          right: 0,
          alignItems: 'center',
          paddingHorizontal: 16,
          backgroundColor: 'transparent',
        }}
      >
        <View
          style={{
            paddingHorizontal: 20,
            paddingVertical: 12,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: 20,
            alignItems: 'center',
          }}
        >
          <Text
            style={{
              fontSize: 14,
              fontFamily: 'Poppins_500Medium',
              color: 'white',
              textAlign: 'center',
            }}
          >
            {!hasPermission
              ? 'Microphone permission needed'
              : isMuted
                ? 'Voice session active (muted)'
                : 'Voice session active - listening'}
          </Text>
        </View>
      </View>

      {/* Mute/Unmute button - bottom right */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 32,
          right: 24,
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: isMuted ? '#FF3B30' : colors.primary,
          alignItems: 'center',
          justifyContent: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
          elevation: 4,
        }}
        onPress={onMute}
      >
        {isMuted ? (
          <MicOff size={24} color={colors.background} />
        ) : (
          <Mic size={24} color={colors.background} />
        )}
      </TouchableOpacity>
    </View>
  );
}
