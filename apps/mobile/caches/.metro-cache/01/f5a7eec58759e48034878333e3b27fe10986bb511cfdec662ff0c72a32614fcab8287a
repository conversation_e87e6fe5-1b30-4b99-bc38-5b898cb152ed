{"dependencies": [{"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "../../../mountRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 56}, "end": {"line": 2, "column": 55, "index": 111}}], "key": "ZDu7aL2iuT3Od7iyX13y9sY9XZQ=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 112}, "end": {"line": 3, "column": 34, "index": 146}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useMountReactions = useMountReactions;\n  var _utils = require(_dependencyMap[0], \"../../utils\");\n  var _mountRegistry = require(_dependencyMap[1], \"../../../mountRegistry\");\n  var _react = require(_dependencyMap[2], \"react\");\n  function shouldUpdateDetector(relation, gesture) {\n    if (relation === undefined) {\n      return false;\n    }\n    for (const tag of (0, _utils.transformIntoHandlerTags)(relation)) {\n      if (tag === gesture.handlerTag) {\n        return true;\n      }\n    }\n    return false;\n  }\n  function useMountReactions(updateDetector, state) {\n    (0, _react.useEffect)(() => {\n      return _mountRegistry.MountRegistry.addMountListener(gesture => {\n        // At this point the ref in the gesture config should be updated, so we can check if one of the gestures\n        // set in a relation with the gesture got mounted. If so, we need to update the detector to propagate\n        // the changes to the native side.\n        for (const attachedGesture of state.attachedGestures) {\n          const blocksHandlers = attachedGesture.config.blocksHandlers;\n          const requireToFail = attachedGesture.config.requireToFail;\n          const simultaneousWith = attachedGesture.config.simultaneousWith;\n          if (shouldUpdateDetector(blocksHandlers, gesture) || shouldUpdateDetector(requireToFail, gesture) || shouldUpdateDetector(simultaneousWith, gesture)) {\n            updateDetector(); // We can safely return here, if any other gestures should be updated, they will be by the above call\n\n            return;\n          }\n        }\n      });\n    }, [updateDetector, state]);\n  }\n});", "lineCount": 39, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_utils"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_mountRegistry"], [7, 20, 2, 0], [7, 23, 2, 0, "require"], [7, 30, 2, 0], [7, 31, 2, 0, "_dependencyMap"], [7, 45, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 5, 0], [9, 11, 5, 9, "shouldUpdateDetector"], [9, 31, 5, 29, "shouldUpdateDetector"], [9, 32, 5, 30, "relation"], [9, 40, 5, 38], [9, 42, 5, 40, "gesture"], [9, 49, 5, 47], [9, 51, 5, 49], [10, 4, 6, 2], [10, 8, 6, 6, "relation"], [10, 16, 6, 14], [10, 21, 6, 19, "undefined"], [10, 30, 6, 28], [10, 32, 6, 30], [11, 6, 7, 4], [11, 13, 7, 11], [11, 18, 7, 16], [12, 4, 8, 2], [13, 4, 10, 2], [13, 9, 10, 7], [13, 15, 10, 13, "tag"], [13, 18, 10, 16], [13, 22, 10, 20], [13, 26, 10, 20, "transformIntoHandlerTags"], [13, 57, 10, 44], [13, 59, 10, 45, "relation"], [13, 67, 10, 53], [13, 68, 10, 54], [13, 70, 10, 56], [14, 6, 11, 4], [14, 10, 11, 8, "tag"], [14, 13, 11, 11], [14, 18, 11, 16, "gesture"], [14, 25, 11, 23], [14, 26, 11, 24, "handlerTag"], [14, 36, 11, 34], [14, 38, 11, 36], [15, 8, 12, 6], [15, 15, 12, 13], [15, 19, 12, 17], [16, 6, 13, 4], [17, 4, 14, 2], [18, 4, 16, 2], [18, 11, 16, 9], [18, 16, 16, 14], [19, 2, 17, 0], [20, 2, 19, 7], [20, 11, 19, 16, "useMountReactions"], [20, 28, 19, 33, "useMountReactions"], [20, 29, 19, 34, "updateDetector"], [20, 43, 19, 48], [20, 45, 19, 50, "state"], [20, 50, 19, 55], [20, 52, 19, 57], [21, 4, 20, 2], [21, 8, 20, 2, "useEffect"], [21, 24, 20, 11], [21, 26, 20, 12], [21, 32, 20, 18], [22, 6, 21, 4], [22, 13, 21, 11, "MountRegistry"], [22, 41, 21, 24], [22, 42, 21, 25, "addMountListener"], [22, 58, 21, 41], [22, 59, 21, 42, "gesture"], [22, 66, 21, 49], [22, 70, 21, 53], [23, 8, 22, 6], [24, 8, 23, 6], [25, 8, 24, 6], [26, 8, 25, 6], [26, 13, 25, 11], [26, 19, 25, 17, "attachedGesture"], [26, 34, 25, 32], [26, 38, 25, 36, "state"], [26, 43, 25, 41], [26, 44, 25, 42, "attachedGestures"], [26, 60, 25, 58], [26, 62, 25, 60], [27, 10, 26, 8], [27, 16, 26, 14, "blocksHandlers"], [27, 30, 26, 28], [27, 33, 26, 31, "attachedGesture"], [27, 48, 26, 46], [27, 49, 26, 47, "config"], [27, 55, 26, 53], [27, 56, 26, 54, "blocksHandlers"], [27, 70, 26, 68], [28, 10, 27, 8], [28, 16, 27, 14, "requireToFail"], [28, 29, 27, 27], [28, 32, 27, 30, "attachedGesture"], [28, 47, 27, 45], [28, 48, 27, 46, "config"], [28, 54, 27, 52], [28, 55, 27, 53, "requireToFail"], [28, 68, 27, 66], [29, 10, 28, 8], [29, 16, 28, 14, "simultaneousWith"], [29, 32, 28, 30], [29, 35, 28, 33, "attachedGesture"], [29, 50, 28, 48], [29, 51, 28, 49, "config"], [29, 57, 28, 55], [29, 58, 28, 56, "simultaneousWith"], [29, 74, 28, 72], [30, 10, 30, 8], [30, 14, 30, 12, "shouldUpdateDetector"], [30, 34, 30, 32], [30, 35, 30, 33, "blocksHandlers"], [30, 49, 30, 47], [30, 51, 30, 49, "gesture"], [30, 58, 30, 56], [30, 59, 30, 57], [30, 63, 30, 61, "shouldUpdateDetector"], [30, 83, 30, 81], [30, 84, 30, 82, "requireToFail"], [30, 97, 30, 95], [30, 99, 30, 97, "gesture"], [30, 106, 30, 104], [30, 107, 30, 105], [30, 111, 30, 109, "shouldUpdateDetector"], [30, 131, 30, 129], [30, 132, 30, 130, "simultaneousWith"], [30, 148, 30, 146], [30, 150, 30, 148, "gesture"], [30, 157, 30, 155], [30, 158, 30, 156], [30, 160, 30, 158], [31, 12, 31, 10, "updateDetector"], [31, 26, 31, 24], [31, 27, 31, 25], [31, 28, 31, 26], [31, 29, 31, 27], [31, 30, 31, 28], [33, 12, 33, 10], [34, 10, 34, 8], [35, 8, 35, 6], [36, 6, 36, 4], [36, 7, 36, 5], [36, 8, 36, 6], [37, 4, 37, 2], [37, 5, 37, 3], [37, 7, 37, 5], [37, 8, 37, 6, "updateDetector"], [37, 22, 37, 20], [37, 24, 37, 22, "state"], [37, 29, 37, 27], [37, 30, 37, 28], [37, 31, 37, 29], [38, 2, 38, 0], [39, 0, 38, 1], [39, 3]], "functionMap": {"names": ["<global>", "shouldUpdateDetector", "useMountReactions", "useEffect$argument_0", "MountRegistry.addMountListener$argument_0"], "mappings": "AAA;ACI;CDY;OEE;YCC;0CCC;KDe;GDC;CFC"}}, "type": "js/module"}]}