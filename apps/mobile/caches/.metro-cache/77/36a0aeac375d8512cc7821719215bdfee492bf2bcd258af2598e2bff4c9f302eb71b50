{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./VelocityTracker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 48, "index": 259}}], "key": "QbDk2GE3iqwFFASqUISemKLRhp0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _VelocityTracker = _interopRequireDefault(require(_dependencyMap[1], \"./VelocityTracker\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const MAX_POINTERS = 20;\n  class PointerTracker {\n    constructor() {\n      _defineProperty(this, \"velocityTracker\", new _VelocityTracker.default());\n      _defineProperty(this, \"_trackedPointers\", new Map());\n      _defineProperty(this, \"touchEventsIds\", new Map());\n      _defineProperty(this, \"lastMovedPointerId\", void 0);\n      _defineProperty(this, \"cachedAbsoluteAverages\", {\n        x: 0,\n        y: 0\n      });\n      _defineProperty(this, \"cachedRelativeAverages\", {\n        x: 0,\n        y: 0\n      });\n      this.lastMovedPointerId = NaN;\n      for (let i = 0; i < MAX_POINTERS; ++i) {\n        this.touchEventsIds.set(i, NaN);\n      }\n    }\n    addToTracker(event) {\n      if (this.trackedPointers.has(event.pointerId)) {\n        return;\n      }\n      this.lastMovedPointerId = event.pointerId;\n      const newElement = {\n        abosoluteCoords: {\n          x: event.x,\n          y: event.y\n        },\n        relativeCoords: {\n          x: event.offsetX,\n          y: event.offsetY\n        },\n        timestamp: event.time,\n        velocityX: 0,\n        velocityY: 0\n      };\n      this.trackedPointers.set(event.pointerId, newElement);\n      this.mapTouchEventId(event.pointerId);\n      this.cachedAbsoluteAverages = this.getAbsoluteCoordsAverage();\n      this.cachedRelativeAverages = this.getRelativeCoordsAverage();\n    }\n    removeFromTracker(pointerId) {\n      this.trackedPointers.delete(pointerId);\n      this.removeMappedTouchId(pointerId);\n    }\n    track(event) {\n      const element = this.trackedPointers.get(event.pointerId);\n      if (!element) {\n        return;\n      }\n      this.lastMovedPointerId = event.pointerId;\n      this.velocityTracker.add(event);\n      const [velocityX, velocityY] = this.velocityTracker.velocity;\n      element.velocityX = velocityX;\n      element.velocityY = velocityY;\n      element.abosoluteCoords = {\n        x: event.x,\n        y: event.y\n      };\n      element.relativeCoords = {\n        x: event.offsetX,\n        y: event.offsetY\n      };\n      this.trackedPointers.set(event.pointerId, element);\n      this.cachedAbsoluteAverages = this.getAbsoluteCoordsAverage();\n      this.cachedRelativeAverages = this.getRelativeCoordsAverage();\n    } // Mapping TouchEvents ID\n\n    mapTouchEventId(id) {\n      for (const [mappedId, touchId] of this.touchEventsIds) {\n        if (isNaN(touchId)) {\n          this.touchEventsIds.set(mappedId, id);\n          break;\n        }\n      }\n    }\n    removeMappedTouchId(id) {\n      const mappedId = this.getMappedTouchEventId(id);\n      if (!isNaN(mappedId)) {\n        this.touchEventsIds.set(mappedId, NaN);\n      }\n    }\n    getMappedTouchEventId(touchEventId) {\n      for (const [key, value] of this.touchEventsIds.entries()) {\n        if (value === touchEventId) {\n          return key;\n        }\n      }\n      return NaN;\n    }\n    getVelocity(pointerId) {\n      var _this$trackedPointers, _this$trackedPointers2;\n      return {\n        x: (_this$trackedPointers = this.trackedPointers.get(pointerId)) === null || _this$trackedPointers === void 0 ? void 0 : _this$trackedPointers.velocityX,\n        y: (_this$trackedPointers2 = this.trackedPointers.get(pointerId)) === null || _this$trackedPointers2 === void 0 ? void 0 : _this$trackedPointers2.velocityY\n      };\n    }\n    getLastAbsoluteCoords(pointerId) {\n      var _this$trackedPointers3;\n      return (_this$trackedPointers3 = this.trackedPointers.get(pointerId !== null && pointerId !== void 0 ? pointerId : this.lastMovedPointerId)) === null || _this$trackedPointers3 === void 0 ? void 0 : _this$trackedPointers3.abosoluteCoords;\n    }\n    getLastRelativeCoords(pointerId) {\n      var _this$trackedPointers4;\n      return (_this$trackedPointers4 = this.trackedPointers.get(pointerId !== null && pointerId !== void 0 ? pointerId : this.lastMovedPointerId)) === null || _this$trackedPointers4 === void 0 ? void 0 : _this$trackedPointers4.relativeCoords;\n    } // Some handlers use these methods to send average values in native event.\n    // This may happen when pointers have already been removed from tracker (i.e. pointerup event).\n    // In situation when NaN would be sent as a response, we return cached value.\n    // That prevents handlers from crashing\n\n    getAbsoluteCoordsAverage() {\n      const coordsSum = this.getAbsoluteCoordsSum();\n      const avgX = coordsSum.x / this.trackedPointers.size;\n      const avgY = coordsSum.y / this.trackedPointers.size;\n      const averages = {\n        x: isNaN(avgX) ? this.cachedAbsoluteAverages.x : avgX,\n        y: isNaN(avgY) ? this.cachedAbsoluteAverages.y : avgY\n      };\n      return averages;\n    }\n    getRelativeCoordsAverage() {\n      const coordsSum = this.getRelativeCoordsSum();\n      const avgX = coordsSum.x / this.trackedPointers.size;\n      const avgY = coordsSum.y / this.trackedPointers.size;\n      const averages = {\n        x: isNaN(avgX) ? this.cachedRelativeAverages.x : avgX,\n        y: isNaN(avgY) ? this.cachedRelativeAverages.y : avgY\n      };\n      return averages;\n    }\n    getAbsoluteCoordsSum(ignoredPointer) {\n      const sum = {\n        x: 0,\n        y: 0\n      };\n      this.trackedPointers.forEach((value, key) => {\n        if (key !== ignoredPointer) {\n          sum.x += value.abosoluteCoords.x;\n          sum.y += value.abosoluteCoords.y;\n        }\n      });\n      return sum;\n    }\n    getRelativeCoordsSum(ignoredPointer) {\n      const sum = {\n        x: 0,\n        y: 0\n      };\n      this.trackedPointers.forEach((value, key) => {\n        if (key !== ignoredPointer) {\n          sum.x += value.relativeCoords.x;\n          sum.y += value.relativeCoords.y;\n        }\n      });\n      return sum;\n    }\n    resetTracker() {\n      this.velocityTracker.reset();\n      this.trackedPointers.clear();\n      this.lastMovedPointerId = NaN;\n      for (let i = 0; i < MAX_POINTERS; ++i) {\n        this.touchEventsIds.set(i, NaN);\n      }\n    }\n    static shareCommonPointers(stPointers, ndPointers) {\n      return stPointers.some(pointerId => ndPointers.includes(pointerId));\n    }\n    get trackedPointersCount() {\n      return this.trackedPointers.size;\n    }\n    get trackedPointersIDs() {\n      const keys = [];\n      this.trackedPointers.forEach((_value, key) => {\n        keys.push(key);\n      });\n      return keys;\n    }\n    get trackedPointers() {\n      return this._trackedPointers;\n    }\n  }\n  exports.default = PointerTracker;\n});", "lineCount": 204, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_VelocityTracker"], [7, 22, 3, 0], [7, 25, 3, 0, "_interopRequireDefault"], [7, 47, 3, 0], [7, 48, 3, 0, "require"], [7, 55, 3, 0], [7, 56, 3, 0, "_dependencyMap"], [7, 70, 3, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "obj"], [8, 30, 1, 28], [8, 32, 1, 30, "key"], [8, 35, 1, 33], [8, 37, 1, 35, "value"], [8, 42, 1, 40], [8, 44, 1, 42], [9, 4, 1, 44], [9, 8, 1, 48, "key"], [9, 11, 1, 51], [9, 15, 1, 55, "obj"], [9, 18, 1, 58], [9, 20, 1, 60], [10, 6, 1, 62, "Object"], [10, 12, 1, 68], [10, 13, 1, 69, "defineProperty"], [10, 27, 1, 83], [10, 28, 1, 84, "obj"], [10, 31, 1, 87], [10, 33, 1, 89, "key"], [10, 36, 1, 92], [10, 38, 1, 94], [11, 8, 1, 96, "value"], [11, 13, 1, 101], [11, 15, 1, 103, "value"], [11, 20, 1, 108], [12, 8, 1, 110, "enumerable"], [12, 18, 1, 120], [12, 20, 1, 122], [12, 24, 1, 126], [13, 8, 1, 128, "configurable"], [13, 20, 1, 140], [13, 22, 1, 142], [13, 26, 1, 146], [14, 8, 1, 148, "writable"], [14, 16, 1, 156], [14, 18, 1, 158], [15, 6, 1, 163], [15, 7, 1, 164], [15, 8, 1, 165], [16, 4, 1, 167], [16, 5, 1, 168], [16, 11, 1, 174], [17, 6, 1, 176, "obj"], [17, 9, 1, 179], [17, 10, 1, 180, "key"], [17, 13, 1, 183], [17, 14, 1, 184], [17, 17, 1, 187, "value"], [17, 22, 1, 192], [18, 4, 1, 194], [19, 4, 1, 196], [19, 11, 1, 203, "obj"], [19, 14, 1, 206], [20, 2, 1, 208], [21, 2, 4, 0], [21, 8, 4, 6, "MAX_POINTERS"], [21, 20, 4, 18], [21, 23, 4, 21], [21, 25, 4, 23], [22, 2, 5, 15], [22, 8, 5, 21, "PointerTracker"], [22, 22, 5, 35], [22, 23, 5, 36], [23, 4, 6, 2, "constructor"], [23, 15, 6, 13, "constructor"], [23, 16, 6, 13], [23, 18, 6, 16], [24, 6, 7, 4, "_defineProperty"], [24, 21, 7, 19], [24, 22, 7, 20], [24, 26, 7, 24], [24, 28, 7, 26], [24, 45, 7, 43], [24, 47, 7, 45], [24, 51, 7, 49, "VelocityTracker"], [24, 75, 7, 64], [24, 76, 7, 65], [24, 77, 7, 66], [24, 78, 7, 67], [25, 6, 9, 4, "_defineProperty"], [25, 21, 9, 19], [25, 22, 9, 20], [25, 26, 9, 24], [25, 28, 9, 26], [25, 46, 9, 44], [25, 48, 9, 46], [25, 52, 9, 50, "Map"], [25, 55, 9, 53], [25, 56, 9, 54], [25, 57, 9, 55], [25, 58, 9, 56], [26, 6, 11, 4, "_defineProperty"], [26, 21, 11, 19], [26, 22, 11, 20], [26, 26, 11, 24], [26, 28, 11, 26], [26, 44, 11, 42], [26, 46, 11, 44], [26, 50, 11, 48, "Map"], [26, 53, 11, 51], [26, 54, 11, 52], [26, 55, 11, 53], [26, 56, 11, 54], [27, 6, 13, 4, "_defineProperty"], [27, 21, 13, 19], [27, 22, 13, 20], [27, 26, 13, 24], [27, 28, 13, 26], [27, 48, 13, 46], [27, 50, 13, 48], [27, 55, 13, 53], [27, 56, 13, 54], [27, 57, 13, 55], [28, 6, 15, 4, "_defineProperty"], [28, 21, 15, 19], [28, 22, 15, 20], [28, 26, 15, 24], [28, 28, 15, 26], [28, 52, 15, 50], [28, 54, 15, 52], [29, 8, 16, 6, "x"], [29, 9, 16, 7], [29, 11, 16, 9], [29, 12, 16, 10], [30, 8, 17, 6, "y"], [30, 9, 17, 7], [30, 11, 17, 9], [31, 6, 18, 4], [31, 7, 18, 5], [31, 8, 18, 6], [32, 6, 20, 4, "_defineProperty"], [32, 21, 20, 19], [32, 22, 20, 20], [32, 26, 20, 24], [32, 28, 20, 26], [32, 52, 20, 50], [32, 54, 20, 52], [33, 8, 21, 6, "x"], [33, 9, 21, 7], [33, 11, 21, 9], [33, 12, 21, 10], [34, 8, 22, 6, "y"], [34, 9, 22, 7], [34, 11, 22, 9], [35, 6, 23, 4], [35, 7, 23, 5], [35, 8, 23, 6], [36, 6, 25, 4], [36, 10, 25, 8], [36, 11, 25, 9, "lastMovedPointerId"], [36, 29, 25, 27], [36, 32, 25, 30, "NaN"], [36, 35, 25, 33], [37, 6, 27, 4], [37, 11, 27, 9], [37, 15, 27, 13, "i"], [37, 16, 27, 14], [37, 19, 27, 17], [37, 20, 27, 18], [37, 22, 27, 20, "i"], [37, 23, 27, 21], [37, 26, 27, 24, "MAX_POINTERS"], [37, 38, 27, 36], [37, 40, 27, 38], [37, 42, 27, 40, "i"], [37, 43, 27, 41], [37, 45, 27, 43], [38, 8, 28, 6], [38, 12, 28, 10], [38, 13, 28, 11, "touchEventsIds"], [38, 27, 28, 25], [38, 28, 28, 26, "set"], [38, 31, 28, 29], [38, 32, 28, 30, "i"], [38, 33, 28, 31], [38, 35, 28, 33, "NaN"], [38, 38, 28, 36], [38, 39, 28, 37], [39, 6, 29, 4], [40, 4, 30, 2], [41, 4, 32, 2, "addToTracker"], [41, 16, 32, 14, "addToTracker"], [41, 17, 32, 15, "event"], [41, 22, 32, 20], [41, 24, 32, 22], [42, 6, 33, 4], [42, 10, 33, 8], [42, 14, 33, 12], [42, 15, 33, 13, "trackedPointers"], [42, 30, 33, 28], [42, 31, 33, 29, "has"], [42, 34, 33, 32], [42, 35, 33, 33, "event"], [42, 40, 33, 38], [42, 41, 33, 39, "pointerId"], [42, 50, 33, 48], [42, 51, 33, 49], [42, 53, 33, 51], [43, 8, 34, 6], [44, 6, 35, 4], [45, 6, 37, 4], [45, 10, 37, 8], [45, 11, 37, 9, "lastMovedPointerId"], [45, 29, 37, 27], [45, 32, 37, 30, "event"], [45, 37, 37, 35], [45, 38, 37, 36, "pointerId"], [45, 47, 37, 45], [46, 6, 38, 4], [46, 12, 38, 10, "newElement"], [46, 22, 38, 20], [46, 25, 38, 23], [47, 8, 39, 6, "abosoluteCoords"], [47, 23, 39, 21], [47, 25, 39, 23], [48, 10, 40, 8, "x"], [48, 11, 40, 9], [48, 13, 40, 11, "event"], [48, 18, 40, 16], [48, 19, 40, 17, "x"], [48, 20, 40, 18], [49, 10, 41, 8, "y"], [49, 11, 41, 9], [49, 13, 41, 11, "event"], [49, 18, 41, 16], [49, 19, 41, 17, "y"], [50, 8, 42, 6], [50, 9, 42, 7], [51, 8, 43, 6, "relativeCoords"], [51, 22, 43, 20], [51, 24, 43, 22], [52, 10, 44, 8, "x"], [52, 11, 44, 9], [52, 13, 44, 11, "event"], [52, 18, 44, 16], [52, 19, 44, 17, "offsetX"], [52, 26, 44, 24], [53, 10, 45, 8, "y"], [53, 11, 45, 9], [53, 13, 45, 11, "event"], [53, 18, 45, 16], [53, 19, 45, 17, "offsetY"], [54, 8, 46, 6], [54, 9, 46, 7], [55, 8, 47, 6, "timestamp"], [55, 17, 47, 15], [55, 19, 47, 17, "event"], [55, 24, 47, 22], [55, 25, 47, 23, "time"], [55, 29, 47, 27], [56, 8, 48, 6, "velocityX"], [56, 17, 48, 15], [56, 19, 48, 17], [56, 20, 48, 18], [57, 8, 49, 6, "velocityY"], [57, 17, 49, 15], [57, 19, 49, 17], [58, 6, 50, 4], [58, 7, 50, 5], [59, 6, 51, 4], [59, 10, 51, 8], [59, 11, 51, 9, "trackedPointers"], [59, 26, 51, 24], [59, 27, 51, 25, "set"], [59, 30, 51, 28], [59, 31, 51, 29, "event"], [59, 36, 51, 34], [59, 37, 51, 35, "pointerId"], [59, 46, 51, 44], [59, 48, 51, 46, "newElement"], [59, 58, 51, 56], [59, 59, 51, 57], [60, 6, 52, 4], [60, 10, 52, 8], [60, 11, 52, 9, "mapTouchEventId"], [60, 26, 52, 24], [60, 27, 52, 25, "event"], [60, 32, 52, 30], [60, 33, 52, 31, "pointerId"], [60, 42, 52, 40], [60, 43, 52, 41], [61, 6, 53, 4], [61, 10, 53, 8], [61, 11, 53, 9, "cachedAbsoluteAverages"], [61, 33, 53, 31], [61, 36, 53, 34], [61, 40, 53, 38], [61, 41, 53, 39, "getAbsoluteCoordsAverage"], [61, 65, 53, 63], [61, 66, 53, 64], [61, 67, 53, 65], [62, 6, 54, 4], [62, 10, 54, 8], [62, 11, 54, 9, "cachedRelativeAverages"], [62, 33, 54, 31], [62, 36, 54, 34], [62, 40, 54, 38], [62, 41, 54, 39, "getRelativeCoordsAverage"], [62, 65, 54, 63], [62, 66, 54, 64], [62, 67, 54, 65], [63, 4, 55, 2], [64, 4, 57, 2, "removeFromTracker"], [64, 21, 57, 19, "removeFromTracker"], [64, 22, 57, 20, "pointerId"], [64, 31, 57, 29], [64, 33, 57, 31], [65, 6, 58, 4], [65, 10, 58, 8], [65, 11, 58, 9, "trackedPointers"], [65, 26, 58, 24], [65, 27, 58, 25, "delete"], [65, 33, 58, 31], [65, 34, 58, 32, "pointerId"], [65, 43, 58, 41], [65, 44, 58, 42], [66, 6, 59, 4], [66, 10, 59, 8], [66, 11, 59, 9, "removeMappedTouchId"], [66, 30, 59, 28], [66, 31, 59, 29, "pointerId"], [66, 40, 59, 38], [66, 41, 59, 39], [67, 4, 60, 2], [68, 4, 62, 2, "track"], [68, 9, 62, 7, "track"], [68, 10, 62, 8, "event"], [68, 15, 62, 13], [68, 17, 62, 15], [69, 6, 63, 4], [69, 12, 63, 10, "element"], [69, 19, 63, 17], [69, 22, 63, 20], [69, 26, 63, 24], [69, 27, 63, 25, "trackedPointers"], [69, 42, 63, 40], [69, 43, 63, 41, "get"], [69, 46, 63, 44], [69, 47, 63, 45, "event"], [69, 52, 63, 50], [69, 53, 63, 51, "pointerId"], [69, 62, 63, 60], [69, 63, 63, 61], [70, 6, 65, 4], [70, 10, 65, 8], [70, 11, 65, 9, "element"], [70, 18, 65, 16], [70, 20, 65, 18], [71, 8, 66, 6], [72, 6, 67, 4], [73, 6, 69, 4], [73, 10, 69, 8], [73, 11, 69, 9, "lastMovedPointerId"], [73, 29, 69, 27], [73, 32, 69, 30, "event"], [73, 37, 69, 35], [73, 38, 69, 36, "pointerId"], [73, 47, 69, 45], [74, 6, 70, 4], [74, 10, 70, 8], [74, 11, 70, 9, "velocityTracker"], [74, 26, 70, 24], [74, 27, 70, 25, "add"], [74, 30, 70, 28], [74, 31, 70, 29, "event"], [74, 36, 70, 34], [74, 37, 70, 35], [75, 6, 71, 4], [75, 12, 71, 10], [75, 13, 71, 11, "velocityX"], [75, 22, 71, 20], [75, 24, 71, 22, "velocityY"], [75, 33, 71, 31], [75, 34, 71, 32], [75, 37, 71, 35], [75, 41, 71, 39], [75, 42, 71, 40, "velocityTracker"], [75, 57, 71, 55], [75, 58, 71, 56, "velocity"], [75, 66, 71, 64], [76, 6, 72, 4, "element"], [76, 13, 72, 11], [76, 14, 72, 12, "velocityX"], [76, 23, 72, 21], [76, 26, 72, 24, "velocityX"], [76, 35, 72, 33], [77, 6, 73, 4, "element"], [77, 13, 73, 11], [77, 14, 73, 12, "velocityY"], [77, 23, 73, 21], [77, 26, 73, 24, "velocityY"], [77, 35, 73, 33], [78, 6, 74, 4, "element"], [78, 13, 74, 11], [78, 14, 74, 12, "abosoluteCoords"], [78, 29, 74, 27], [78, 32, 74, 30], [79, 8, 75, 6, "x"], [79, 9, 75, 7], [79, 11, 75, 9, "event"], [79, 16, 75, 14], [79, 17, 75, 15, "x"], [79, 18, 75, 16], [80, 8, 76, 6, "y"], [80, 9, 76, 7], [80, 11, 76, 9, "event"], [80, 16, 76, 14], [80, 17, 76, 15, "y"], [81, 6, 77, 4], [81, 7, 77, 5], [82, 6, 78, 4, "element"], [82, 13, 78, 11], [82, 14, 78, 12, "relativeCoords"], [82, 28, 78, 26], [82, 31, 78, 29], [83, 8, 79, 6, "x"], [83, 9, 79, 7], [83, 11, 79, 9, "event"], [83, 16, 79, 14], [83, 17, 79, 15, "offsetX"], [83, 24, 79, 22], [84, 8, 80, 6, "y"], [84, 9, 80, 7], [84, 11, 80, 9, "event"], [84, 16, 80, 14], [84, 17, 80, 15, "offsetY"], [85, 6, 81, 4], [85, 7, 81, 5], [86, 6, 82, 4], [86, 10, 82, 8], [86, 11, 82, 9, "trackedPointers"], [86, 26, 82, 24], [86, 27, 82, 25, "set"], [86, 30, 82, 28], [86, 31, 82, 29, "event"], [86, 36, 82, 34], [86, 37, 82, 35, "pointerId"], [86, 46, 82, 44], [86, 48, 82, 46, "element"], [86, 55, 82, 53], [86, 56, 82, 54], [87, 6, 83, 4], [87, 10, 83, 8], [87, 11, 83, 9, "cachedAbsoluteAverages"], [87, 33, 83, 31], [87, 36, 83, 34], [87, 40, 83, 38], [87, 41, 83, 39, "getAbsoluteCoordsAverage"], [87, 65, 83, 63], [87, 66, 83, 64], [87, 67, 83, 65], [88, 6, 84, 4], [88, 10, 84, 8], [88, 11, 84, 9, "cachedRelativeAverages"], [88, 33, 84, 31], [88, 36, 84, 34], [88, 40, 84, 38], [88, 41, 84, 39, "getRelativeCoordsAverage"], [88, 65, 84, 63], [88, 66, 84, 64], [88, 67, 84, 65], [89, 4, 85, 2], [89, 5, 85, 3], [89, 6, 85, 4], [91, 4, 88, 2, "mapTouchEventId"], [91, 19, 88, 17, "mapTouchEventId"], [91, 20, 88, 18, "id"], [91, 22, 88, 20], [91, 24, 88, 22], [92, 6, 89, 4], [92, 11, 89, 9], [92, 17, 89, 15], [92, 18, 89, 16, "mappedId"], [92, 26, 89, 24], [92, 28, 89, 26, "touchId"], [92, 35, 89, 33], [92, 36, 89, 34], [92, 40, 89, 38], [92, 44, 89, 42], [92, 45, 89, 43, "touchEventsIds"], [92, 59, 89, 57], [92, 61, 89, 59], [93, 8, 90, 6], [93, 12, 90, 10, "isNaN"], [93, 17, 90, 15], [93, 18, 90, 16, "touchId"], [93, 25, 90, 23], [93, 26, 90, 24], [93, 28, 90, 26], [94, 10, 91, 8], [94, 14, 91, 12], [94, 15, 91, 13, "touchEventsIds"], [94, 29, 91, 27], [94, 30, 91, 28, "set"], [94, 33, 91, 31], [94, 34, 91, 32, "mappedId"], [94, 42, 91, 40], [94, 44, 91, 42, "id"], [94, 46, 91, 44], [94, 47, 91, 45], [95, 10, 92, 8], [96, 8, 93, 6], [97, 6, 94, 4], [98, 4, 95, 2], [99, 4, 97, 2, "removeMappedTouchId"], [99, 23, 97, 21, "removeMappedTouchId"], [99, 24, 97, 22, "id"], [99, 26, 97, 24], [99, 28, 97, 26], [100, 6, 98, 4], [100, 12, 98, 10, "mappedId"], [100, 20, 98, 18], [100, 23, 98, 21], [100, 27, 98, 25], [100, 28, 98, 26, "getMappedTouchEventId"], [100, 49, 98, 47], [100, 50, 98, 48, "id"], [100, 52, 98, 50], [100, 53, 98, 51], [101, 6, 100, 4], [101, 10, 100, 8], [101, 11, 100, 9, "isNaN"], [101, 16, 100, 14], [101, 17, 100, 15, "mappedId"], [101, 25, 100, 23], [101, 26, 100, 24], [101, 28, 100, 26], [102, 8, 101, 6], [102, 12, 101, 10], [102, 13, 101, 11, "touchEventsIds"], [102, 27, 101, 25], [102, 28, 101, 26, "set"], [102, 31, 101, 29], [102, 32, 101, 30, "mappedId"], [102, 40, 101, 38], [102, 42, 101, 40, "NaN"], [102, 45, 101, 43], [102, 46, 101, 44], [103, 6, 102, 4], [104, 4, 103, 2], [105, 4, 105, 2, "getMappedTouchEventId"], [105, 25, 105, 23, "getMappedTouchEventId"], [105, 26, 105, 24, "touchEventId"], [105, 38, 105, 36], [105, 40, 105, 38], [106, 6, 106, 4], [106, 11, 106, 9], [106, 17, 106, 15], [106, 18, 106, 16, "key"], [106, 21, 106, 19], [106, 23, 106, 21, "value"], [106, 28, 106, 26], [106, 29, 106, 27], [106, 33, 106, 31], [106, 37, 106, 35], [106, 38, 106, 36, "touchEventsIds"], [106, 52, 106, 50], [106, 53, 106, 51, "entries"], [106, 60, 106, 58], [106, 61, 106, 59], [106, 62, 106, 60], [106, 64, 106, 62], [107, 8, 107, 6], [107, 12, 107, 10, "value"], [107, 17, 107, 15], [107, 22, 107, 20, "touchEventId"], [107, 34, 107, 32], [107, 36, 107, 34], [108, 10, 108, 8], [108, 17, 108, 15, "key"], [108, 20, 108, 18], [109, 8, 109, 6], [110, 6, 110, 4], [111, 6, 112, 4], [111, 13, 112, 11, "NaN"], [111, 16, 112, 14], [112, 4, 113, 2], [113, 4, 115, 2, "getVelocity"], [113, 15, 115, 13, "getVelocity"], [113, 16, 115, 14, "pointerId"], [113, 25, 115, 23], [113, 27, 115, 25], [114, 6, 116, 4], [114, 10, 116, 8, "_this$trackedPointers"], [114, 31, 116, 29], [114, 33, 116, 31, "_this$trackedPointers2"], [114, 55, 116, 53], [115, 6, 118, 4], [115, 13, 118, 11], [116, 8, 119, 6, "x"], [116, 9, 119, 7], [116, 11, 119, 9], [116, 12, 119, 10, "_this$trackedPointers"], [116, 33, 119, 31], [116, 36, 119, 34], [116, 40, 119, 38], [116, 41, 119, 39, "trackedPointers"], [116, 56, 119, 54], [116, 57, 119, 55, "get"], [116, 60, 119, 58], [116, 61, 119, 59, "pointerId"], [116, 70, 119, 68], [116, 71, 119, 69], [116, 77, 119, 75], [116, 81, 119, 79], [116, 85, 119, 83, "_this$trackedPointers"], [116, 106, 119, 104], [116, 111, 119, 109], [116, 116, 119, 114], [116, 117, 119, 115], [116, 120, 119, 118], [116, 125, 119, 123], [116, 126, 119, 124], [116, 129, 119, 127, "_this$trackedPointers"], [116, 150, 119, 148], [116, 151, 119, 149, "velocityX"], [116, 160, 119, 158], [117, 8, 120, 6, "y"], [117, 9, 120, 7], [117, 11, 120, 9], [117, 12, 120, 10, "_this$trackedPointers2"], [117, 34, 120, 32], [117, 37, 120, 35], [117, 41, 120, 39], [117, 42, 120, 40, "trackedPointers"], [117, 57, 120, 55], [117, 58, 120, 56, "get"], [117, 61, 120, 59], [117, 62, 120, 60, "pointerId"], [117, 71, 120, 69], [117, 72, 120, 70], [117, 78, 120, 76], [117, 82, 120, 80], [117, 86, 120, 84, "_this$trackedPointers2"], [117, 108, 120, 106], [117, 113, 120, 111], [117, 118, 120, 116], [117, 119, 120, 117], [117, 122, 120, 120], [117, 127, 120, 125], [117, 128, 120, 126], [117, 131, 120, 129, "_this$trackedPointers2"], [117, 153, 120, 151], [117, 154, 120, 152, "velocityY"], [118, 6, 121, 4], [118, 7, 121, 5], [119, 4, 122, 2], [120, 4, 124, 2, "getLastAbsoluteCoords"], [120, 25, 124, 23, "getLastAbsoluteCoords"], [120, 26, 124, 24, "pointerId"], [120, 35, 124, 33], [120, 37, 124, 35], [121, 6, 125, 4], [121, 10, 125, 8, "_this$trackedPointers3"], [121, 32, 125, 30], [122, 6, 127, 4], [122, 13, 127, 11], [122, 14, 127, 12, "_this$trackedPointers3"], [122, 36, 127, 34], [122, 39, 127, 37], [122, 43, 127, 41], [122, 44, 127, 42, "trackedPointers"], [122, 59, 127, 57], [122, 60, 127, 58, "get"], [122, 63, 127, 61], [122, 64, 127, 62, "pointerId"], [122, 73, 127, 71], [122, 78, 127, 76], [122, 82, 127, 80], [122, 86, 127, 84, "pointerId"], [122, 95, 127, 93], [122, 100, 127, 98], [122, 105, 127, 103], [122, 106, 127, 104], [122, 109, 127, 107, "pointerId"], [122, 118, 127, 116], [122, 121, 127, 119], [122, 125, 127, 123], [122, 126, 127, 124, "lastMovedPointerId"], [122, 144, 127, 142], [122, 145, 127, 143], [122, 151, 127, 149], [122, 155, 127, 153], [122, 159, 127, 157, "_this$trackedPointers3"], [122, 181, 127, 179], [122, 186, 127, 184], [122, 191, 127, 189], [122, 192, 127, 190], [122, 195, 127, 193], [122, 200, 127, 198], [122, 201, 127, 199], [122, 204, 127, 202, "_this$trackedPointers3"], [122, 226, 127, 224], [122, 227, 127, 225, "abosoluteCoords"], [122, 242, 127, 240], [123, 4, 128, 2], [124, 4, 130, 2, "getLastRelativeCoords"], [124, 25, 130, 23, "getLastRelativeCoords"], [124, 26, 130, 24, "pointerId"], [124, 35, 130, 33], [124, 37, 130, 35], [125, 6, 131, 4], [125, 10, 131, 8, "_this$trackedPointers4"], [125, 32, 131, 30], [126, 6, 133, 4], [126, 13, 133, 11], [126, 14, 133, 12, "_this$trackedPointers4"], [126, 36, 133, 34], [126, 39, 133, 37], [126, 43, 133, 41], [126, 44, 133, 42, "trackedPointers"], [126, 59, 133, 57], [126, 60, 133, 58, "get"], [126, 63, 133, 61], [126, 64, 133, 62, "pointerId"], [126, 73, 133, 71], [126, 78, 133, 76], [126, 82, 133, 80], [126, 86, 133, 84, "pointerId"], [126, 95, 133, 93], [126, 100, 133, 98], [126, 105, 133, 103], [126, 106, 133, 104], [126, 109, 133, 107, "pointerId"], [126, 118, 133, 116], [126, 121, 133, 119], [126, 125, 133, 123], [126, 126, 133, 124, "lastMovedPointerId"], [126, 144, 133, 142], [126, 145, 133, 143], [126, 151, 133, 149], [126, 155, 133, 153], [126, 159, 133, 157, "_this$trackedPointers4"], [126, 181, 133, 179], [126, 186, 133, 184], [126, 191, 133, 189], [126, 192, 133, 190], [126, 195, 133, 193], [126, 200, 133, 198], [126, 201, 133, 199], [126, 204, 133, 202, "_this$trackedPointers4"], [126, 226, 133, 224], [126, 227, 133, 225, "relativeCoords"], [126, 241, 133, 239], [127, 4, 134, 2], [127, 5, 134, 3], [127, 6, 134, 4], [128, 4, 135, 2], [129, 4, 136, 2], [130, 4, 137, 2], [132, 4, 140, 2, "getAbsoluteCoordsAverage"], [132, 28, 140, 26, "getAbsoluteCoordsAverage"], [132, 29, 140, 26], [132, 31, 140, 29], [133, 6, 141, 4], [133, 12, 141, 10, "coordsSum"], [133, 21, 141, 19], [133, 24, 141, 22], [133, 28, 141, 26], [133, 29, 141, 27, "getAbsoluteCoordsSum"], [133, 49, 141, 47], [133, 50, 141, 48], [133, 51, 141, 49], [134, 6, 142, 4], [134, 12, 142, 10, "avgX"], [134, 16, 142, 14], [134, 19, 142, 17, "coordsSum"], [134, 28, 142, 26], [134, 29, 142, 27, "x"], [134, 30, 142, 28], [134, 33, 142, 31], [134, 37, 142, 35], [134, 38, 142, 36, "trackedPointers"], [134, 53, 142, 51], [134, 54, 142, 52, "size"], [134, 58, 142, 56], [135, 6, 143, 4], [135, 12, 143, 10, "avgY"], [135, 16, 143, 14], [135, 19, 143, 17, "coordsSum"], [135, 28, 143, 26], [135, 29, 143, 27, "y"], [135, 30, 143, 28], [135, 33, 143, 31], [135, 37, 143, 35], [135, 38, 143, 36, "trackedPointers"], [135, 53, 143, 51], [135, 54, 143, 52, "size"], [135, 58, 143, 56], [136, 6, 144, 4], [136, 12, 144, 10, "averages"], [136, 20, 144, 18], [136, 23, 144, 21], [137, 8, 145, 6, "x"], [137, 9, 145, 7], [137, 11, 145, 9, "isNaN"], [137, 16, 145, 14], [137, 17, 145, 15, "avgX"], [137, 21, 145, 19], [137, 22, 145, 20], [137, 25, 145, 23], [137, 29, 145, 27], [137, 30, 145, 28, "cachedAbsoluteAverages"], [137, 52, 145, 50], [137, 53, 145, 51, "x"], [137, 54, 145, 52], [137, 57, 145, 55, "avgX"], [137, 61, 145, 59], [138, 8, 146, 6, "y"], [138, 9, 146, 7], [138, 11, 146, 9, "isNaN"], [138, 16, 146, 14], [138, 17, 146, 15, "avgY"], [138, 21, 146, 19], [138, 22, 146, 20], [138, 25, 146, 23], [138, 29, 146, 27], [138, 30, 146, 28, "cachedAbsoluteAverages"], [138, 52, 146, 50], [138, 53, 146, 51, "y"], [138, 54, 146, 52], [138, 57, 146, 55, "avgY"], [139, 6, 147, 4], [139, 7, 147, 5], [140, 6, 148, 4], [140, 13, 148, 11, "averages"], [140, 21, 148, 19], [141, 4, 149, 2], [142, 4, 151, 2, "getRelativeCoordsAverage"], [142, 28, 151, 26, "getRelativeCoordsAverage"], [142, 29, 151, 26], [142, 31, 151, 29], [143, 6, 152, 4], [143, 12, 152, 10, "coordsSum"], [143, 21, 152, 19], [143, 24, 152, 22], [143, 28, 152, 26], [143, 29, 152, 27, "getRelativeCoordsSum"], [143, 49, 152, 47], [143, 50, 152, 48], [143, 51, 152, 49], [144, 6, 153, 4], [144, 12, 153, 10, "avgX"], [144, 16, 153, 14], [144, 19, 153, 17, "coordsSum"], [144, 28, 153, 26], [144, 29, 153, 27, "x"], [144, 30, 153, 28], [144, 33, 153, 31], [144, 37, 153, 35], [144, 38, 153, 36, "trackedPointers"], [144, 53, 153, 51], [144, 54, 153, 52, "size"], [144, 58, 153, 56], [145, 6, 154, 4], [145, 12, 154, 10, "avgY"], [145, 16, 154, 14], [145, 19, 154, 17, "coordsSum"], [145, 28, 154, 26], [145, 29, 154, 27, "y"], [145, 30, 154, 28], [145, 33, 154, 31], [145, 37, 154, 35], [145, 38, 154, 36, "trackedPointers"], [145, 53, 154, 51], [145, 54, 154, 52, "size"], [145, 58, 154, 56], [146, 6, 155, 4], [146, 12, 155, 10, "averages"], [146, 20, 155, 18], [146, 23, 155, 21], [147, 8, 156, 6, "x"], [147, 9, 156, 7], [147, 11, 156, 9, "isNaN"], [147, 16, 156, 14], [147, 17, 156, 15, "avgX"], [147, 21, 156, 19], [147, 22, 156, 20], [147, 25, 156, 23], [147, 29, 156, 27], [147, 30, 156, 28, "cachedRelativeAverages"], [147, 52, 156, 50], [147, 53, 156, 51, "x"], [147, 54, 156, 52], [147, 57, 156, 55, "avgX"], [147, 61, 156, 59], [148, 8, 157, 6, "y"], [148, 9, 157, 7], [148, 11, 157, 9, "isNaN"], [148, 16, 157, 14], [148, 17, 157, 15, "avgY"], [148, 21, 157, 19], [148, 22, 157, 20], [148, 25, 157, 23], [148, 29, 157, 27], [148, 30, 157, 28, "cachedRelativeAverages"], [148, 52, 157, 50], [148, 53, 157, 51, "y"], [148, 54, 157, 52], [148, 57, 157, 55, "avgY"], [149, 6, 158, 4], [149, 7, 158, 5], [150, 6, 159, 4], [150, 13, 159, 11, "averages"], [150, 21, 159, 19], [151, 4, 160, 2], [152, 4, 162, 2, "getAbsoluteCoordsSum"], [152, 24, 162, 22, "getAbsoluteCoordsSum"], [152, 25, 162, 23, "ignoredPointer"], [152, 39, 162, 37], [152, 41, 162, 39], [153, 6, 163, 4], [153, 12, 163, 10, "sum"], [153, 15, 163, 13], [153, 18, 163, 16], [154, 8, 164, 6, "x"], [154, 9, 164, 7], [154, 11, 164, 9], [154, 12, 164, 10], [155, 8, 165, 6, "y"], [155, 9, 165, 7], [155, 11, 165, 9], [156, 6, 166, 4], [156, 7, 166, 5], [157, 6, 167, 4], [157, 10, 167, 8], [157, 11, 167, 9, "trackedPointers"], [157, 26, 167, 24], [157, 27, 167, 25, "for<PERSON>ach"], [157, 34, 167, 32], [157, 35, 167, 33], [157, 36, 167, 34, "value"], [157, 41, 167, 39], [157, 43, 167, 41, "key"], [157, 46, 167, 44], [157, 51, 167, 49], [158, 8, 168, 6], [158, 12, 168, 10, "key"], [158, 15, 168, 13], [158, 20, 168, 18, "ignoredPointer"], [158, 34, 168, 32], [158, 36, 168, 34], [159, 10, 169, 8, "sum"], [159, 13, 169, 11], [159, 14, 169, 12, "x"], [159, 15, 169, 13], [159, 19, 169, 17, "value"], [159, 24, 169, 22], [159, 25, 169, 23, "abosoluteCoords"], [159, 40, 169, 38], [159, 41, 169, 39, "x"], [159, 42, 169, 40], [160, 10, 170, 8, "sum"], [160, 13, 170, 11], [160, 14, 170, 12, "y"], [160, 15, 170, 13], [160, 19, 170, 17, "value"], [160, 24, 170, 22], [160, 25, 170, 23, "abosoluteCoords"], [160, 40, 170, 38], [160, 41, 170, 39, "y"], [160, 42, 170, 40], [161, 8, 171, 6], [162, 6, 172, 4], [162, 7, 172, 5], [162, 8, 172, 6], [163, 6, 173, 4], [163, 13, 173, 11, "sum"], [163, 16, 173, 14], [164, 4, 174, 2], [165, 4, 176, 2, "getRelativeCoordsSum"], [165, 24, 176, 22, "getRelativeCoordsSum"], [165, 25, 176, 23, "ignoredPointer"], [165, 39, 176, 37], [165, 41, 176, 39], [166, 6, 177, 4], [166, 12, 177, 10, "sum"], [166, 15, 177, 13], [166, 18, 177, 16], [167, 8, 178, 6, "x"], [167, 9, 178, 7], [167, 11, 178, 9], [167, 12, 178, 10], [168, 8, 179, 6, "y"], [168, 9, 179, 7], [168, 11, 179, 9], [169, 6, 180, 4], [169, 7, 180, 5], [170, 6, 181, 4], [170, 10, 181, 8], [170, 11, 181, 9, "trackedPointers"], [170, 26, 181, 24], [170, 27, 181, 25, "for<PERSON>ach"], [170, 34, 181, 32], [170, 35, 181, 33], [170, 36, 181, 34, "value"], [170, 41, 181, 39], [170, 43, 181, 41, "key"], [170, 46, 181, 44], [170, 51, 181, 49], [171, 8, 182, 6], [171, 12, 182, 10, "key"], [171, 15, 182, 13], [171, 20, 182, 18, "ignoredPointer"], [171, 34, 182, 32], [171, 36, 182, 34], [172, 10, 183, 8, "sum"], [172, 13, 183, 11], [172, 14, 183, 12, "x"], [172, 15, 183, 13], [172, 19, 183, 17, "value"], [172, 24, 183, 22], [172, 25, 183, 23, "relativeCoords"], [172, 39, 183, 37], [172, 40, 183, 38, "x"], [172, 41, 183, 39], [173, 10, 184, 8, "sum"], [173, 13, 184, 11], [173, 14, 184, 12, "y"], [173, 15, 184, 13], [173, 19, 184, 17, "value"], [173, 24, 184, 22], [173, 25, 184, 23, "relativeCoords"], [173, 39, 184, 37], [173, 40, 184, 38, "y"], [173, 41, 184, 39], [174, 8, 185, 6], [175, 6, 186, 4], [175, 7, 186, 5], [175, 8, 186, 6], [176, 6, 187, 4], [176, 13, 187, 11, "sum"], [176, 16, 187, 14], [177, 4, 188, 2], [178, 4, 190, 2, "resetTracker"], [178, 16, 190, 14, "resetTracker"], [178, 17, 190, 14], [178, 19, 190, 17], [179, 6, 191, 4], [179, 10, 191, 8], [179, 11, 191, 9, "velocityTracker"], [179, 26, 191, 24], [179, 27, 191, 25, "reset"], [179, 32, 191, 30], [179, 33, 191, 31], [179, 34, 191, 32], [180, 6, 192, 4], [180, 10, 192, 8], [180, 11, 192, 9, "trackedPointers"], [180, 26, 192, 24], [180, 27, 192, 25, "clear"], [180, 32, 192, 30], [180, 33, 192, 31], [180, 34, 192, 32], [181, 6, 193, 4], [181, 10, 193, 8], [181, 11, 193, 9, "lastMovedPointerId"], [181, 29, 193, 27], [181, 32, 193, 30, "NaN"], [181, 35, 193, 33], [182, 6, 195, 4], [182, 11, 195, 9], [182, 15, 195, 13, "i"], [182, 16, 195, 14], [182, 19, 195, 17], [182, 20, 195, 18], [182, 22, 195, 20, "i"], [182, 23, 195, 21], [182, 26, 195, 24, "MAX_POINTERS"], [182, 38, 195, 36], [182, 40, 195, 38], [182, 42, 195, 40, "i"], [182, 43, 195, 41], [182, 45, 195, 43], [183, 8, 196, 6], [183, 12, 196, 10], [183, 13, 196, 11, "touchEventsIds"], [183, 27, 196, 25], [183, 28, 196, 26, "set"], [183, 31, 196, 29], [183, 32, 196, 30, "i"], [183, 33, 196, 31], [183, 35, 196, 33, "NaN"], [183, 38, 196, 36], [183, 39, 196, 37], [184, 6, 197, 4], [185, 4, 198, 2], [186, 4, 200, 2], [186, 11, 200, 9, "shareCommonPointers"], [186, 30, 200, 28, "shareCommonPointers"], [186, 31, 200, 29, "stPointers"], [186, 41, 200, 39], [186, 43, 200, 41, "ndPointers"], [186, 53, 200, 51], [186, 55, 200, 53], [187, 6, 201, 4], [187, 13, 201, 11, "stPointers"], [187, 23, 201, 21], [187, 24, 201, 22, "some"], [187, 28, 201, 26], [187, 29, 201, 27, "pointerId"], [187, 38, 201, 36], [187, 42, 201, 40, "ndPointers"], [187, 52, 201, 50], [187, 53, 201, 51, "includes"], [187, 61, 201, 59], [187, 62, 201, 60, "pointerId"], [187, 71, 201, 69], [187, 72, 201, 70], [187, 73, 201, 71], [188, 4, 202, 2], [189, 4, 204, 2], [189, 8, 204, 6, "trackedPointersCount"], [189, 28, 204, 26, "trackedPointersCount"], [189, 29, 204, 26], [189, 31, 204, 29], [190, 6, 205, 4], [190, 13, 205, 11], [190, 17, 205, 15], [190, 18, 205, 16, "trackedPointers"], [190, 33, 205, 31], [190, 34, 205, 32, "size"], [190, 38, 205, 36], [191, 4, 206, 2], [192, 4, 208, 2], [192, 8, 208, 6, "trackedPointersIDs"], [192, 26, 208, 24, "trackedPointersIDs"], [192, 27, 208, 24], [192, 29, 208, 27], [193, 6, 209, 4], [193, 12, 209, 10, "keys"], [193, 16, 209, 14], [193, 19, 209, 17], [193, 21, 209, 19], [194, 6, 210, 4], [194, 10, 210, 8], [194, 11, 210, 9, "trackedPointers"], [194, 26, 210, 24], [194, 27, 210, 25, "for<PERSON>ach"], [194, 34, 210, 32], [194, 35, 210, 33], [194, 36, 210, 34, "_value"], [194, 42, 210, 40], [194, 44, 210, 42, "key"], [194, 47, 210, 45], [194, 52, 210, 50], [195, 8, 211, 6, "keys"], [195, 12, 211, 10], [195, 13, 211, 11, "push"], [195, 17, 211, 15], [195, 18, 211, 16, "key"], [195, 21, 211, 19], [195, 22, 211, 20], [196, 6, 212, 4], [196, 7, 212, 5], [196, 8, 212, 6], [197, 6, 213, 4], [197, 13, 213, 11, "keys"], [197, 17, 213, 15], [198, 4, 214, 2], [199, 4, 216, 2], [199, 8, 216, 6, "trackedPointers"], [199, 23, 216, 21, "trackedPointers"], [199, 24, 216, 21], [199, 26, 216, 24], [200, 6, 217, 4], [200, 13, 217, 11], [200, 17, 217, 15], [200, 18, 217, 16, "_trackedPointers"], [200, 34, 217, 32], [201, 4, 218, 2], [202, 2, 220, 0], [203, 2, 220, 1, "exports"], [203, 9, 220, 1], [203, 10, 220, 1, "default"], [203, 17, 220, 1], [203, 20, 220, 1, "PointerTracker"], [203, 34, 220, 1], [204, 0, 220, 1], [204, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "PointerTracker", "constructor", "addToTracker", "removeFromTracker", "track", "mapTouchEventId", "removeMappedTouchId", "getMappedTouchEventId", "getVelocity", "getLastAbsoluteCoords", "getLastRelativeCoords", "getAbsoluteCoordsAverage", "getRelativeCoordsAverage", "getAbsoluteCoordsSum", "trackedPointers.forEach$argument_0", "getRelativeCoordsSum", "resetTracker", "shareCommonPointers", "stPointers.some$argument_0", "get__trackedPointersCount", "get__trackedPointersIDs", "get__trackedPointers"], "mappings": "AAA,iNC;eCI;ECC;GDwB;EEE;GFuB;EGE;GHG;EIE;GJuB;EKG;GLO;EME;GNM;EOE;GPQ;EQE;GRO;ESE;GTI;EUE;GVI;EWM;GXS;EYE;GZS;EaE;iCCK;KDK;GbE;EeE;iCDK;KCK;GfE;EgBE;GhBQ;EiBE;2BCC,2CD;GjBC;EmBE;GnBE;EoBE;iCNE;KME;GpBE;EqBE;GrBE;CDE"}}, "type": "js/module"}]}