{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "./constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 37}, "end": {"line": 2, "column": 38, "index": 75}}], "key": "waDaw5D7vDr2hRFu0z1BqRCTzP4=", "exportNames": ["*"]}}, {"name": "./IndiscreteGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 76}, "end": {"line": 3, "column": 66, "index": 142}}], "key": "d7/3lZQfxBXmNrwL5D+QBRLWiAg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _constants = require(_dependencyMap[2], \"./constants\");\n  var _IndiscreteGestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./IndiscreteGestureHandler\"));\n  class RotationGestureHandler extends _IndiscreteGestureHandler.default {\n    get name() {\n      return 'rotate';\n    }\n    get NativeGestureClass() {\n      return _hammerjs.default.Rotate;\n    }\n    transformNativeEvent({\n      rotation,\n      velocity,\n      center\n    }) {\n      var _this$initialRotation;\n      return {\n        rotation: (rotation - ((_this$initialRotation = this.initialRotation) !== null && _this$initialRotation !== void 0 ? _this$initialRotation : 0)) * _constants.DEG_RAD,\n        anchorX: center.x,\n        anchorY: center.y,\n        velocity\n      };\n    }\n  }\n  var _default = exports.default = RotationGestureHandler;\n});", "lineCount": 32, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_hammerjs"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_constants"], [8, 16, 2, 0], [8, 19, 2, 0, "require"], [8, 26, 2, 0], [8, 27, 2, 0, "_dependencyMap"], [8, 41, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_IndiscreteGestureHandler"], [9, 31, 3, 0], [9, 34, 3, 0, "_interopRequireDefault"], [9, 56, 3, 0], [9, 57, 3, 0, "require"], [9, 64, 3, 0], [9, 65, 3, 0, "_dependencyMap"], [9, 79, 3, 0], [10, 2, 5, 0], [10, 8, 5, 6, "RotationGestureHandler"], [10, 30, 5, 28], [10, 39, 5, 37, "IndiscreteGestureHandler"], [10, 72, 5, 61], [10, 73, 5, 62], [11, 4, 6, 2], [11, 8, 6, 6, "name"], [11, 12, 6, 10, "name"], [11, 13, 6, 10], [11, 15, 6, 13], [12, 6, 7, 4], [12, 13, 7, 11], [12, 21, 7, 19], [13, 4, 8, 2], [14, 4, 10, 2], [14, 8, 10, 6, "NativeGestureClass"], [14, 26, 10, 24, "NativeGestureClass"], [14, 27, 10, 24], [14, 29, 10, 27], [15, 6, 11, 4], [15, 13, 11, 11, "Hammer"], [15, 30, 11, 17], [15, 31, 11, 18, "Rotate"], [15, 37, 11, 24], [16, 4, 12, 2], [17, 4, 14, 2, "transformNativeEvent"], [17, 24, 14, 22, "transformNativeEvent"], [17, 25, 14, 23], [18, 6, 15, 4, "rotation"], [18, 14, 15, 12], [19, 6, 16, 4, "velocity"], [19, 14, 16, 12], [20, 6, 17, 4, "center"], [21, 4, 18, 2], [21, 5, 18, 3], [21, 7, 18, 5], [22, 6, 19, 4], [22, 10, 19, 8, "_this$initialRotation"], [22, 31, 19, 29], [23, 6, 21, 4], [23, 13, 21, 11], [24, 8, 22, 6, "rotation"], [24, 16, 22, 14], [24, 18, 22, 16], [24, 19, 22, 17, "rotation"], [24, 27, 22, 25], [24, 31, 22, 29], [24, 32, 22, 30, "_this$initialRotation"], [24, 53, 22, 51], [24, 56, 22, 54], [24, 60, 22, 58], [24, 61, 22, 59, "initialRotation"], [24, 76, 22, 74], [24, 82, 22, 80], [24, 86, 22, 84], [24, 90, 22, 88, "_this$initialRotation"], [24, 111, 22, 109], [24, 116, 22, 114], [24, 121, 22, 119], [24, 122, 22, 120], [24, 125, 22, 123, "_this$initialRotation"], [24, 146, 22, 144], [24, 149, 22, 147], [24, 150, 22, 148], [24, 151, 22, 149], [24, 155, 22, 153, "DEG_RAD"], [24, 173, 22, 160], [25, 8, 23, 6, "anchorX"], [25, 15, 23, 13], [25, 17, 23, 15, "center"], [25, 23, 23, 21], [25, 24, 23, 22, "x"], [25, 25, 23, 23], [26, 8, 24, 6, "anchorY"], [26, 15, 24, 13], [26, 17, 24, 15, "center"], [26, 23, 24, 21], [26, 24, 24, 22, "y"], [26, 25, 24, 23], [27, 8, 25, 6, "velocity"], [28, 6, 26, 4], [28, 7, 26, 5], [29, 4, 27, 2], [30, 2, 29, 0], [31, 2, 29, 1], [31, 6, 29, 1, "_default"], [31, 14, 29, 1], [31, 17, 29, 1, "exports"], [31, 24, 29, 1], [31, 25, 29, 1, "default"], [31, 32, 29, 1], [31, 35, 31, 15, "RotationGestureHandler"], [31, 57, 31, 37], [32, 0, 31, 37], [32, 3]], "functionMap": {"names": ["<global>", "RotationGestureHandler", "get__name", "get__NativeGestureClass", "transformNativeEvent"], "mappings": "AAA;ACI;ECC;GDE;EEE;GFE;EGE;GHa;CDE"}}, "type": "js/module"}]}