{"dependencies": [{"name": "../threads.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 51, "index": 66}}], "key": "K4CZCGtE2IjiBjBQzdc2uYfV4CM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.prepareUIRegistry = void 0;\n  var _threads = require(_dependencyMap[0], \"../threads.js\");\n  const _worklet_11036889787670_init_data = {\n    code: \"function reactNativeReanimated_FrameCallbackRegistryUIJs1(){const frameCallbackRegistry={frameCallbackRegistry:new Map(),activeFrameCallbacks:new Set(),previousFrameTimestamp:null,nextCallId:0,runCallbacks:function(callId){var _this=this;const loop=function(timestamp){if(callId!==_this.nextCallId){return;}if(_this.previousFrameTimestamp===null){_this.previousFrameTimestamp=timestamp;}const delta=timestamp-_this.previousFrameTimestamp;_this.activeFrameCallbacks.forEach(function(callbackId){const callbackDetails=_this.frameCallbackRegistry.get(callbackId);const{startTime:startTime}=callbackDetails;if(startTime===null){callbackDetails.startTime=timestamp;callbackDetails.callback({timestamp:timestamp,timeSincePreviousFrame:null,timeSinceFirstFrame:0});}else{callbackDetails.callback({timestamp:timestamp,timeSincePreviousFrame:delta,timeSinceFirstFrame:timestamp-startTime});}});if(_this.activeFrameCallbacks.size>0){_this.previousFrameTimestamp=timestamp;requestAnimationFrame(loop);}else{_this.previousFrameTimestamp=null;}};if(this.activeFrameCallbacks.size===1&&callId===this.nextCallId){requestAnimationFrame(loop);}},registerFrameCallback:function(callback,callbackId){this.frameCallbackRegistry.set(callbackId,{callback:callback,startTime:null});},unregisterFrameCallback:function(callbackId){this.manageStateFrameCallback(callbackId,false);this.frameCallbackRegistry.delete(callbackId);},manageStateFrameCallback:function(callbackId,state){if(callbackId===-1){return;}if(state){this.activeFrameCallbacks.add(callbackId);this.runCallbacks(this.nextCallId);}else{const callback=this.frameCallbackRegistry.get(callbackId);callback.startTime=null;this.activeFrameCallbacks.delete(callbackId);if(this.activeFrameCallbacks.size===0){this.nextCallId+=1;}}}};global._frameCallbackRegistry=frameCallbackRegistry;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryUI.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_FrameCallbackRegistryUIJs1\\\",\\\"frameCallbackRegistry\\\",\\\"Map\\\",\\\"activeFrameCallbacks\\\",\\\"Set\\\",\\\"previousFrameTimestamp\\\",\\\"nextCallId\\\",\\\"runCallbacks\\\",\\\"callId\\\",\\\"_this\\\",\\\"loop\\\",\\\"timestamp\\\",\\\"delta\\\",\\\"forEach\\\",\\\"callbackId\\\",\\\"callbackDetails\\\",\\\"get\\\",\\\"startTime\\\",\\\"callback\\\",\\\"timeSincePreviousFrame\\\",\\\"timeSinceFirstFrame\\\",\\\"size\\\",\\\"requestAnimationFrame\\\",\\\"registerFrameCallback\\\",\\\"set\\\",\\\"unregisterFrameCallback\\\",\\\"manageStateFrameCallback\\\",\\\"delete\\\",\\\"state\\\",\\\"add\\\",\\\"global\\\",\\\"_frameCallbackRegistry\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryUI.js\\\"],\\\"mappings\\\":\\\"AAGoD,SAAAA,gDAAMA,CAAA,EAGxD,KAAM,CAAAC,qBAAqB,CAAG,CAC5BA,qBAAqB,CAAE,GAAI,CAAAC,GAAG,CAAC,CAAC,CAChCC,oBAAoB,CAAE,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC/BC,sBAAsB,CAAE,IAAI,CAC5BC,UAAU,CAAE,CAAC,CACbC,YAAY,SAAAA,CAACC,MAAM,CAAE,KAAAC,KAAA,MACnB,KAAM,CAAAC,IAAI,CAAG,QAAAA,CAAAC,SAAS,CAAI,CACxB,GAAIH,MAAM,GAAKC,KAAI,CAACH,UAAU,CAAE,CAC9B,OACF,CACA,GAAIG,KAAI,CAACJ,sBAAsB,GAAK,IAAI,CAAE,CACxCI,KAAI,CAACJ,sBAAsB,CAAGM,SAAS,CACzC,CACA,KAAM,CAAAC,KAAK,CAAGD,SAAS,CAAGF,KAAI,CAACJ,sBAAsB,CACrDI,KAAI,CAACN,oBAAoB,CAACU,OAAO,CAAC,SAAAC,UAAU,CAAI,CAC9C,KAAM,CAAAC,eAAe,CAAGN,KAAI,CAACR,qBAAqB,CAACe,GAAG,CAACF,UAAU,CAAC,CAClE,KAAM,CACJG,SAAA,CAAAA,SACF,CAAC,CAAGF,eAAe,CACnB,GAAIE,SAAS,GAAK,IAAI,CAAE,CAEtBF,eAAe,CAACE,SAAS,CAAGN,SAAS,CACrCI,eAAe,CAACG,QAAQ,CAAC,CACvBP,SAAS,CAATA,SAAS,CACTQ,sBAAsB,CAAE,IAAI,CAC5BC,mBAAmB,CAAE,CACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CAELL,eAAe,CAACG,QAAQ,CAAC,CACvBP,SAAS,CAATA,SAAS,CACTQ,sBAAsB,CAAEP,KAAK,CAC7BQ,mBAAmB,CAAET,SAAS,CAAGM,SACnC,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACF,GAAIR,KAAI,CAACN,oBAAoB,CAACkB,IAAI,CAAG,CAAC,CAAE,CACtCZ,KAAI,CAACJ,sBAAsB,CAAGM,SAAS,CACvCW,qBAAqB,CAACZ,IAAI,CAAC,CAC7B,CAAC,IAAM,CACLD,KAAI,CAACJ,sBAAsB,CAAG,IAAI,CACpC,CACF,CAAC,CAKD,GAAI,IAAI,CAACF,oBAAoB,CAACkB,IAAI,GAAK,CAAC,EAAIb,MAAM,GAAK,IAAI,CAACF,UAAU,CAAE,CACtEgB,qBAAqB,CAACZ,IAAI,CAAC,CAC7B,CACF,CAAC,CACDa,qBAAqB,SAAAA,CAACL,QAAQ,CAAEJ,UAAU,CAAE,CAC1C,IAAI,CAACb,qBAAqB,CAACuB,GAAG,CAACV,UAAU,CAAE,CACzCI,QAAQ,CAARA,QAAQ,CACRD,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,CACDQ,uBAAuB,SAAAA,CAACX,UAAU,CAAE,CAClC,IAAI,CAACY,wBAAwB,CAACZ,UAAU,CAAE,KAAK,CAAC,CAChD,IAAI,CAACb,qBAAqB,CAAC0B,MAAM,CAACb,UAAU,CAAC,CAC/C,CAAC,CACDY,wBAAwB,SAAAA,CAACZ,UAAU,CAAEc,KAAK,CAAE,CAC1C,GAAId,UAAU,GAAK,CAAC,CAAC,CAAE,CACrB,OACF,CACA,GAAIc,KAAK,CAAE,CACT,IAAI,CAACzB,oBAAoB,CAAC0B,GAAG,CAACf,UAAU,CAAC,CACzC,IAAI,CAACP,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC,CACpC,CAAC,IAAM,CACL,KAAM,CAAAY,QAAQ,CAAG,IAAI,CAACjB,qBAAqB,CAACe,GAAG,CAACF,UAAU,CAAC,CAC3DI,QAAQ,CAACD,SAAS,CAAG,IAAI,CACzB,IAAI,CAACd,oBAAoB,CAACwB,MAAM,CAACb,UAAU,CAAC,CAC5C,GAAI,IAAI,CAACX,oBAAoB,CAACkB,IAAI,GAAK,CAAC,CAAE,CACxC,IAAI,CAACf,UAAU,EAAI,CAAC,CACtB,CACF,CACF,CACF,CAAC,CACDwB,MAAM,CAACC,sBAAsB,CAAG9B,qBAAqB,CACvD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const prepareUIRegistry = exports.prepareUIRegistry = (0, _threads.runOnUIImmediately)(function () {\n    const _e = [new global.Error(), 1, -27];\n    const reactNativeReanimated_FrameCallbackRegistryUIJs1 = function () {\n      const frameCallbackRegistry = {\n        frameCallbackRegistry: new Map(),\n        activeFrameCallbacks: new Set(),\n        previousFrameTimestamp: null,\n        nextCallId: 0,\n        runCallbacks(callId) {\n          const loop = timestamp => {\n            if (callId !== this.nextCallId) {\n              return;\n            }\n            if (this.previousFrameTimestamp === null) {\n              this.previousFrameTimestamp = timestamp;\n            }\n            const delta = timestamp - this.previousFrameTimestamp;\n            this.activeFrameCallbacks.forEach(callbackId => {\n              const callbackDetails = this.frameCallbackRegistry.get(callbackId);\n              const {\n                startTime\n              } = callbackDetails;\n              if (startTime === null) {\n                // First frame\n                callbackDetails.startTime = timestamp;\n                callbackDetails.callback({\n                  timestamp,\n                  timeSincePreviousFrame: null,\n                  timeSinceFirstFrame: 0\n                });\n              } else {\n                // Next frame\n                callbackDetails.callback({\n                  timestamp,\n                  timeSincePreviousFrame: delta,\n                  timeSinceFirstFrame: timestamp - startTime\n                });\n              }\n            });\n            if (this.activeFrameCallbacks.size > 0) {\n              this.previousFrameTimestamp = timestamp;\n              requestAnimationFrame(loop);\n            } else {\n              this.previousFrameTimestamp = null;\n            }\n          };\n\n          // runCallback() should only be called after registering a callback,\n          // so if there is only one active callback, then it means that there were\n          // zero previously and the loop isn't running yet.\n          if (this.activeFrameCallbacks.size === 1 && callId === this.nextCallId) {\n            requestAnimationFrame(loop);\n          }\n        },\n        registerFrameCallback(callback, callbackId) {\n          this.frameCallbackRegistry.set(callbackId, {\n            callback,\n            startTime: null\n          });\n        },\n        unregisterFrameCallback(callbackId) {\n          this.manageStateFrameCallback(callbackId, false);\n          this.frameCallbackRegistry.delete(callbackId);\n        },\n        manageStateFrameCallback(callbackId, state) {\n          if (callbackId === -1) {\n            return;\n          }\n          if (state) {\n            this.activeFrameCallbacks.add(callbackId);\n            this.runCallbacks(this.nextCallId);\n          } else {\n            const callback = this.frameCallbackRegistry.get(callbackId);\n            callback.startTime = null;\n            this.activeFrameCallbacks.delete(callbackId);\n            if (this.activeFrameCallbacks.size === 0) {\n              this.nextCallId += 1;\n            }\n          }\n        }\n      };\n      global._frameCallbackRegistry = frameCallbackRegistry;\n    };\n    reactNativeReanimated_FrameCallbackRegistryUIJs1.__closure = {};\n    reactNativeReanimated_FrameCallbackRegistryUIJs1.__workletHash = 11036889787670;\n    reactNativeReanimated_FrameCallbackRegistryUIJs1.__initData = _worklet_11036889787670_init_data;\n    reactNativeReanimated_FrameCallbackRegistryUIJs1.__stackDetails = _e;\n    return reactNativeReanimated_FrameCallbackRegistryUIJs1;\n  }());\n});", "lineCount": 104, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "prepareUIRegistry"], [7, 27, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_threads"], [8, 14, 3, 0], [8, 17, 3, 0, "require"], [8, 24, 3, 0], [8, 25, 3, 0, "_dependencyMap"], [8, 39, 3, 0], [9, 2, 3, 51], [9, 8, 3, 51, "_worklet_11036889787670_init_data"], [9, 41, 3, 51], [10, 4, 3, 51, "code"], [10, 8, 3, 51], [11, 4, 3, 51, "location"], [11, 12, 3, 51], [12, 4, 3, 51, "sourceMap"], [12, 13, 3, 51], [13, 4, 3, 51, "version"], [13, 11, 3, 51], [14, 2, 3, 51], [15, 2, 4, 7], [15, 8, 4, 13, "prepareUIRegistry"], [15, 25, 4, 30], [15, 28, 4, 30, "exports"], [15, 35, 4, 30], [15, 36, 4, 30, "prepareUIRegistry"], [15, 53, 4, 30], [15, 56, 4, 33], [15, 60, 4, 33, "runOnUIImmediately"], [15, 87, 4, 51], [15, 89, 4, 52], [16, 4, 4, 52], [16, 10, 4, 52, "_e"], [16, 12, 4, 52], [16, 20, 4, 52, "global"], [16, 26, 4, 52], [16, 27, 4, 52, "Error"], [16, 32, 4, 52], [17, 4, 4, 52], [17, 10, 4, 52, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [17, 58, 4, 52], [17, 70, 4, 52, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [17, 71, 4, 52], [17, 73, 4, 58], [18, 6, 7, 2], [18, 12, 7, 8, "frameCallbackRegistry"], [18, 33, 7, 29], [18, 36, 7, 32], [19, 8, 8, 4, "frameCallbackRegistry"], [19, 29, 8, 25], [19, 31, 8, 27], [19, 35, 8, 31, "Map"], [19, 38, 8, 34], [19, 39, 8, 35], [19, 40, 8, 36], [20, 8, 9, 4, "activeFrameCallbacks"], [20, 28, 9, 24], [20, 30, 9, 26], [20, 34, 9, 30, "Set"], [20, 37, 9, 33], [20, 38, 9, 34], [20, 39, 9, 35], [21, 8, 10, 4, "previousFrameTimestamp"], [21, 30, 10, 26], [21, 32, 10, 28], [21, 36, 10, 32], [22, 8, 11, 4, "nextCallId"], [22, 18, 11, 14], [22, 20, 11, 16], [22, 21, 11, 17], [23, 8, 12, 4, "runCallbacks"], [23, 20, 12, 16, "runCallbacks"], [23, 21, 12, 17, "callId"], [23, 27, 12, 23], [23, 29, 12, 25], [24, 10, 13, 6], [24, 16, 13, 12, "loop"], [24, 20, 13, 16], [24, 23, 13, 19, "timestamp"], [24, 32, 13, 28], [24, 36, 13, 32], [25, 12, 14, 8], [25, 16, 14, 12, "callId"], [25, 22, 14, 18], [25, 27, 14, 23], [25, 31, 14, 27], [25, 32, 14, 28, "nextCallId"], [25, 42, 14, 38], [25, 44, 14, 40], [26, 14, 15, 10], [27, 12, 16, 8], [28, 12, 17, 8], [28, 16, 17, 12], [28, 20, 17, 16], [28, 21, 17, 17, "previousFrameTimestamp"], [28, 43, 17, 39], [28, 48, 17, 44], [28, 52, 17, 48], [28, 54, 17, 50], [29, 14, 18, 10], [29, 18, 18, 14], [29, 19, 18, 15, "previousFrameTimestamp"], [29, 41, 18, 37], [29, 44, 18, 40, "timestamp"], [29, 53, 18, 49], [30, 12, 19, 8], [31, 12, 20, 8], [31, 18, 20, 14, "delta"], [31, 23, 20, 19], [31, 26, 20, 22, "timestamp"], [31, 35, 20, 31], [31, 38, 20, 34], [31, 42, 20, 38], [31, 43, 20, 39, "previousFrameTimestamp"], [31, 65, 20, 61], [32, 12, 21, 8], [32, 16, 21, 12], [32, 17, 21, 13, "activeFrameCallbacks"], [32, 37, 21, 33], [32, 38, 21, 34, "for<PERSON>ach"], [32, 45, 21, 41], [32, 46, 21, 42, "callbackId"], [32, 56, 21, 52], [32, 60, 21, 56], [33, 14, 22, 10], [33, 20, 22, 16, "callbackDetails"], [33, 35, 22, 31], [33, 38, 22, 34], [33, 42, 22, 38], [33, 43, 22, 39, "frameCallbackRegistry"], [33, 64, 22, 60], [33, 65, 22, 61, "get"], [33, 68, 22, 64], [33, 69, 22, 65, "callbackId"], [33, 79, 22, 75], [33, 80, 22, 76], [34, 14, 23, 10], [34, 20, 23, 16], [35, 16, 24, 12, "startTime"], [36, 14, 25, 10], [36, 15, 25, 11], [36, 18, 25, 14, "callbackDetails"], [36, 33, 25, 29], [37, 14, 26, 10], [37, 18, 26, 14, "startTime"], [37, 27, 26, 23], [37, 32, 26, 28], [37, 36, 26, 32], [37, 38, 26, 34], [38, 16, 27, 12], [39, 16, 28, 12, "callbackDetails"], [39, 31, 28, 27], [39, 32, 28, 28, "startTime"], [39, 41, 28, 37], [39, 44, 28, 40, "timestamp"], [39, 53, 28, 49], [40, 16, 29, 12, "callbackDetails"], [40, 31, 29, 27], [40, 32, 29, 28, "callback"], [40, 40, 29, 36], [40, 41, 29, 37], [41, 18, 30, 14, "timestamp"], [41, 27, 30, 23], [42, 18, 31, 14, "timeSincePreviousFrame"], [42, 40, 31, 36], [42, 42, 31, 38], [42, 46, 31, 42], [43, 18, 32, 14, "timeSinceFirstFrame"], [43, 37, 32, 33], [43, 39, 32, 35], [44, 16, 33, 12], [44, 17, 33, 13], [44, 18, 33, 14], [45, 14, 34, 10], [45, 15, 34, 11], [45, 21, 34, 17], [46, 16, 35, 12], [47, 16, 36, 12, "callbackDetails"], [47, 31, 36, 27], [47, 32, 36, 28, "callback"], [47, 40, 36, 36], [47, 41, 36, 37], [48, 18, 37, 14, "timestamp"], [48, 27, 37, 23], [49, 18, 38, 14, "timeSincePreviousFrame"], [49, 40, 38, 36], [49, 42, 38, 38, "delta"], [49, 47, 38, 43], [50, 18, 39, 14, "timeSinceFirstFrame"], [50, 37, 39, 33], [50, 39, 39, 35, "timestamp"], [50, 48, 39, 44], [50, 51, 39, 47, "startTime"], [51, 16, 40, 12], [51, 17, 40, 13], [51, 18, 40, 14], [52, 14, 41, 10], [53, 12, 42, 8], [53, 13, 42, 9], [53, 14, 42, 10], [54, 12, 43, 8], [54, 16, 43, 12], [54, 20, 43, 16], [54, 21, 43, 17, "activeFrameCallbacks"], [54, 41, 43, 37], [54, 42, 43, 38, "size"], [54, 46, 43, 42], [54, 49, 43, 45], [54, 50, 43, 46], [54, 52, 43, 48], [55, 14, 44, 10], [55, 18, 44, 14], [55, 19, 44, 15, "previousFrameTimestamp"], [55, 41, 44, 37], [55, 44, 44, 40, "timestamp"], [55, 53, 44, 49], [56, 14, 45, 10, "requestAnimationFrame"], [56, 35, 45, 31], [56, 36, 45, 32, "loop"], [56, 40, 45, 36], [56, 41, 45, 37], [57, 12, 46, 8], [57, 13, 46, 9], [57, 19, 46, 15], [58, 14, 47, 10], [58, 18, 47, 14], [58, 19, 47, 15, "previousFrameTimestamp"], [58, 41, 47, 37], [58, 44, 47, 40], [58, 48, 47, 44], [59, 12, 48, 8], [60, 10, 49, 6], [60, 11, 49, 7], [62, 10, 51, 6], [63, 10, 52, 6], [64, 10, 53, 6], [65, 10, 54, 6], [65, 14, 54, 10], [65, 18, 54, 14], [65, 19, 54, 15, "activeFrameCallbacks"], [65, 39, 54, 35], [65, 40, 54, 36, "size"], [65, 44, 54, 40], [65, 49, 54, 45], [65, 50, 54, 46], [65, 54, 54, 50, "callId"], [65, 60, 54, 56], [65, 65, 54, 61], [65, 69, 54, 65], [65, 70, 54, 66, "nextCallId"], [65, 80, 54, 76], [65, 82, 54, 78], [66, 12, 55, 8, "requestAnimationFrame"], [66, 33, 55, 29], [66, 34, 55, 30, "loop"], [66, 38, 55, 34], [66, 39, 55, 35], [67, 10, 56, 6], [68, 8, 57, 4], [68, 9, 57, 5], [69, 8, 58, 4, "registerFrameCallback"], [69, 29, 58, 25, "registerFrameCallback"], [69, 30, 58, 26, "callback"], [69, 38, 58, 34], [69, 40, 58, 36, "callbackId"], [69, 50, 58, 46], [69, 52, 58, 48], [70, 10, 59, 6], [70, 14, 59, 10], [70, 15, 59, 11, "frameCallbackRegistry"], [70, 36, 59, 32], [70, 37, 59, 33, "set"], [70, 40, 59, 36], [70, 41, 59, 37, "callbackId"], [70, 51, 59, 47], [70, 53, 59, 49], [71, 12, 60, 8, "callback"], [71, 20, 60, 16], [72, 12, 61, 8, "startTime"], [72, 21, 61, 17], [72, 23, 61, 19], [73, 10, 62, 6], [73, 11, 62, 7], [73, 12, 62, 8], [74, 8, 63, 4], [74, 9, 63, 5], [75, 8, 64, 4, "unregisterFrameCallback"], [75, 31, 64, 27, "unregisterFrameCallback"], [75, 32, 64, 28, "callbackId"], [75, 42, 64, 38], [75, 44, 64, 40], [76, 10, 65, 6], [76, 14, 65, 10], [76, 15, 65, 11, "manageStateFrameCallback"], [76, 39, 65, 35], [76, 40, 65, 36, "callbackId"], [76, 50, 65, 46], [76, 52, 65, 48], [76, 57, 65, 53], [76, 58, 65, 54], [77, 10, 66, 6], [77, 14, 66, 10], [77, 15, 66, 11, "frameCallbackRegistry"], [77, 36, 66, 32], [77, 37, 66, 33, "delete"], [77, 43, 66, 39], [77, 44, 66, 40, "callbackId"], [77, 54, 66, 50], [77, 55, 66, 51], [78, 8, 67, 4], [78, 9, 67, 5], [79, 8, 68, 4, "manageStateFrameCallback"], [79, 32, 68, 28, "manageStateFrameCallback"], [79, 33, 68, 29, "callbackId"], [79, 43, 68, 39], [79, 45, 68, 41, "state"], [79, 50, 68, 46], [79, 52, 68, 48], [80, 10, 69, 6], [80, 14, 69, 10, "callbackId"], [80, 24, 69, 20], [80, 29, 69, 25], [80, 30, 69, 26], [80, 31, 69, 27], [80, 33, 69, 29], [81, 12, 70, 8], [82, 10, 71, 6], [83, 10, 72, 6], [83, 14, 72, 10, "state"], [83, 19, 72, 15], [83, 21, 72, 17], [84, 12, 73, 8], [84, 16, 73, 12], [84, 17, 73, 13, "activeFrameCallbacks"], [84, 37, 73, 33], [84, 38, 73, 34, "add"], [84, 41, 73, 37], [84, 42, 73, 38, "callbackId"], [84, 52, 73, 48], [84, 53, 73, 49], [85, 12, 74, 8], [85, 16, 74, 12], [85, 17, 74, 13, "runCallbacks"], [85, 29, 74, 25], [85, 30, 74, 26], [85, 34, 74, 30], [85, 35, 74, 31, "nextCallId"], [85, 45, 74, 41], [85, 46, 74, 42], [86, 10, 75, 6], [86, 11, 75, 7], [86, 17, 75, 13], [87, 12, 76, 8], [87, 18, 76, 14, "callback"], [87, 26, 76, 22], [87, 29, 76, 25], [87, 33, 76, 29], [87, 34, 76, 30, "frameCallbackRegistry"], [87, 55, 76, 51], [87, 56, 76, 52, "get"], [87, 59, 76, 55], [87, 60, 76, 56, "callbackId"], [87, 70, 76, 66], [87, 71, 76, 67], [88, 12, 77, 8, "callback"], [88, 20, 77, 16], [88, 21, 77, 17, "startTime"], [88, 30, 77, 26], [88, 33, 77, 29], [88, 37, 77, 33], [89, 12, 78, 8], [89, 16, 78, 12], [89, 17, 78, 13, "activeFrameCallbacks"], [89, 37, 78, 33], [89, 38, 78, 34, "delete"], [89, 44, 78, 40], [89, 45, 78, 41, "callbackId"], [89, 55, 78, 51], [89, 56, 78, 52], [90, 12, 79, 8], [90, 16, 79, 12], [90, 20, 79, 16], [90, 21, 79, 17, "activeFrameCallbacks"], [90, 41, 79, 37], [90, 42, 79, 38, "size"], [90, 46, 79, 42], [90, 51, 79, 47], [90, 52, 79, 48], [90, 54, 79, 50], [91, 14, 80, 10], [91, 18, 80, 14], [91, 19, 80, 15, "nextCallId"], [91, 29, 80, 25], [91, 33, 80, 29], [91, 34, 80, 30], [92, 12, 81, 8], [93, 10, 82, 6], [94, 8, 83, 4], [95, 6, 84, 2], [95, 7, 84, 3], [96, 6, 85, 2, "global"], [96, 12, 85, 8], [96, 13, 85, 9, "_frameCallbackRegistry"], [96, 35, 85, 31], [96, 38, 85, 34, "frameCallbackRegistry"], [96, 59, 85, 55], [97, 4, 86, 0], [97, 5, 86, 1], [98, 4, 86, 1, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [98, 52, 86, 1], [98, 53, 86, 1, "__closure"], [98, 62, 86, 1], [99, 4, 86, 1, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [99, 52, 86, 1], [99, 53, 86, 1, "__workletHash"], [99, 66, 86, 1], [100, 4, 86, 1, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [100, 52, 86, 1], [100, 53, 86, 1, "__initData"], [100, 63, 86, 1], [100, 66, 86, 1, "_worklet_11036889787670_init_data"], [100, 99, 86, 1], [101, 4, 86, 1, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [101, 52, 86, 1], [101, 53, 86, 1, "__stackDetails"], [101, 67, 86, 1], [101, 70, 86, 1, "_e"], [101, 72, 86, 1], [102, 4, 86, 1], [102, 11, 86, 1, "reactNativeReanimated_FrameCallbackRegistryUIJs1"], [102, 59, 86, 1], [103, 2, 86, 1], [103, 3, 4, 52], [103, 5, 86, 1], [103, 6, 86, 2], [104, 0, 86, 3], [104, 3]], "functionMap": {"names": ["<global>", "runOnUIImmediately$argument_0", "frameCallbackRegistry.runCallbacks", "loop", "activeFrameCallbacks.forEach$argument_0", "frameCallbackRegistry.registerFrameCallback", "frameCallbackRegistry.unregisterFrameCallback", "frameCallbackRegistry.manageStateFrameCallback"], "mappings": "AAA;oDCG;ICQ;mBCC;0CCQ;SDqB;ODO;KDQ;IIC;KJK;IKC;KLG;IMC;KNe;CDG"}}, "type": "js/module"}]}