{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.blobToArrayBufferAsync = blobToArrayBufferAsync;\n  exports.legacyBlobToArrayBufferAsync = legacyBlobToArrayBufferAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  /**\n   * Converts a Blob to an ArrayBuffer.\n   */\n  function blobToArrayBufferAsync(blob) {\n    if (typeof blob.arrayBuffer === 'function') {\n      return blob.arrayBuffer();\n    }\n    return legacyBlobToArrayBufferAsync(blob);\n  }\n\n  /**\n   * Converts a Blob to an ArrayBuffer using the FileReader API.\n   */\n  function legacyBlobToArrayBufferAsync(_x) {\n    return _legacyBlobToArrayBufferAsync.apply(this, arguments);\n  }\n  function _legacyBlobToArrayBufferAsync() {\n    _legacyBlobToArrayBufferAsync = (0, _asyncToGenerator2.default)(function* (blob) {\n      return new Promise((resolve, reject) => {\n        var reader = new FileReader();\n        reader.onload = () => {\n          resolve(reader.result);\n        };\n        reader.onerror = reject;\n        reader.readAsArrayBuffer(blob);\n      });\n    });\n    return _legacyBlobToArrayBufferAsync.apply(this, arguments);\n  }\n});", "lineCount": 38, "map": [[9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 2, 4, 7], [12, 11, 4, 16, "blobToArrayBufferAsync"], [12, 33, 4, 38, "blobToArrayBufferAsync"], [12, 34, 4, 39, "blob"], [12, 38, 4, 49], [12, 40, 4, 73], [13, 4, 5, 2], [13, 8, 5, 6], [13, 15, 5, 13, "blob"], [13, 19, 5, 17], [13, 20, 5, 18, "arrayBuffer"], [13, 31, 5, 29], [13, 36, 5, 34], [13, 46, 5, 44], [13, 48, 5, 46], [14, 6, 6, 4], [14, 13, 6, 11, "blob"], [14, 17, 6, 15], [14, 18, 6, 16, "arrayBuffer"], [14, 29, 6, 27], [14, 30, 6, 28], [14, 31, 6, 29], [15, 4, 7, 2], [16, 4, 8, 2], [16, 11, 8, 9, "legacyBlobToArrayBufferAsync"], [16, 39, 8, 37], [16, 40, 8, 38, "blob"], [16, 44, 8, 42], [16, 45, 8, 43], [17, 2, 9, 0], [19, 2, 11, 0], [20, 0, 12, 0], [21, 0, 13, 0], [22, 2, 11, 0], [22, 11, 14, 22, "legacyBlobToArrayBufferAsync"], [22, 39, 14, 50, "legacyBlobToArrayBufferAsync"], [22, 40, 14, 50, "_x"], [22, 42, 14, 50], [23, 4, 14, 50], [23, 11, 14, 50, "_legacyBlobToArrayBufferAsync"], [23, 40, 14, 50], [23, 41, 14, 50, "apply"], [23, 46, 14, 50], [23, 53, 14, 50, "arguments"], [23, 62, 14, 50], [24, 2, 14, 50], [25, 2, 14, 50], [25, 11, 14, 50, "_legacyBlobToArrayBufferAsync"], [25, 41, 14, 50], [26, 4, 14, 50, "_legacyBlobToArrayBufferAsync"], [26, 33, 14, 50], [26, 40, 14, 50, "_asyncToGenerator2"], [26, 58, 14, 50], [26, 59, 14, 50, "default"], [26, 66, 14, 50], [26, 68, 14, 7], [26, 79, 14, 51, "blob"], [26, 83, 14, 61], [26, 85, 14, 85], [27, 6, 15, 2], [27, 13, 15, 9], [27, 17, 15, 13, "Promise"], [27, 24, 15, 20], [27, 25, 15, 21], [27, 26, 15, 22, "resolve"], [27, 33, 15, 29], [27, 35, 15, 31, "reject"], [27, 41, 15, 37], [27, 46, 15, 42], [28, 8, 16, 4], [28, 12, 16, 10, "reader"], [28, 18, 16, 16], [28, 21, 16, 19], [28, 25, 16, 23, "FileReader"], [28, 35, 16, 33], [28, 36, 16, 34], [28, 37, 16, 35], [29, 8, 17, 4, "reader"], [29, 14, 17, 10], [29, 15, 17, 11, "onload"], [29, 21, 17, 17], [29, 24, 17, 20], [29, 30, 17, 26], [30, 10, 18, 6, "resolve"], [30, 17, 18, 13], [30, 18, 18, 14, "reader"], [30, 24, 18, 20], [30, 25, 18, 21, "result"], [30, 31, 18, 42], [30, 32, 18, 43], [31, 8, 19, 4], [31, 9, 19, 5], [32, 8, 20, 4, "reader"], [32, 14, 20, 10], [32, 15, 20, 11, "onerror"], [32, 22, 20, 18], [32, 25, 20, 21, "reject"], [32, 31, 20, 27], [33, 8, 21, 4, "reader"], [33, 14, 21, 10], [33, 15, 21, 11, "readAsA<PERSON>y<PERSON><PERSON>er"], [33, 32, 21, 28], [33, 33, 21, 29, "blob"], [33, 37, 21, 33], [33, 38, 21, 34], [34, 6, 22, 2], [34, 7, 22, 3], [34, 8, 22, 4], [35, 4, 23, 0], [35, 5, 23, 1], [36, 4, 23, 1], [36, 11, 23, 1, "_legacyBlobToArrayBufferAsync"], [36, 40, 23, 1], [36, 41, 23, 1, "apply"], [36, 46, 23, 1], [36, 53, 23, 1, "arguments"], [36, 62, 23, 1], [37, 2, 23, 1], [38, 0, 23, 1], [38, 3]], "functionMap": {"names": ["<global>", "blobToArrayBufferAsync", "legacyBlobToArrayBufferAsync", "Promise$argument_0", "reader.onload"], "mappings": "AAA;OCG;CDK;OEK;qBCC;oBCE;KDE;GDG;CFC"}}, "type": "js/module"}]}