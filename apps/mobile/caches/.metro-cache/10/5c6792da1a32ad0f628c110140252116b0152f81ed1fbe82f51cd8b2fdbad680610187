{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.flattenArray = flattenArray;\n  exports.has = void 0;\n  function flattenArray(array) {\n    if (!Array.isArray(array)) {\n      return [array];\n    }\n    const resultArr = [];\n    const _flattenArray = arr => {\n      arr.forEach(item => {\n        if (Array.isArray(item)) {\n          _flattenArray(item);\n        } else {\n          resultArr.push(item);\n        }\n      });\n    };\n    _flattenArray(array);\n    return resultArr;\n  }\n  const has = (key, x) => {\n    if (typeof x === 'function' || typeof x === 'object') {\n      if (x === null || x === undefined) {\n        return false;\n      } else {\n        return key in x;\n      }\n    }\n    return false;\n  };\n  exports.has = has;\n});", "lineCount": 37, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "flattenArray"], [7, 22, 1, 13], [7, 25, 1, 13, "flattenArray"], [7, 37, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "has"], [8, 13, 1, 13], [9, 2, 3, 7], [9, 11, 3, 16, "flattenArray"], [9, 23, 3, 28, "flattenArray"], [9, 24, 3, 29, "array"], [9, 29, 3, 34], [9, 31, 3, 36], [10, 4, 4, 2], [10, 8, 4, 6], [10, 9, 4, 7, "Array"], [10, 14, 4, 12], [10, 15, 4, 13, "isArray"], [10, 22, 4, 20], [10, 23, 4, 21, "array"], [10, 28, 4, 26], [10, 29, 4, 27], [10, 31, 4, 29], [11, 6, 5, 4], [11, 13, 5, 11], [11, 14, 5, 12, "array"], [11, 19, 5, 17], [11, 20, 5, 18], [12, 4, 6, 2], [13, 4, 7, 2], [13, 10, 7, 8, "resultArr"], [13, 19, 7, 17], [13, 22, 7, 20], [13, 24, 7, 22], [14, 4, 8, 2], [14, 10, 8, 8, "_flattenArray"], [14, 23, 8, 21], [14, 26, 8, 24, "arr"], [14, 29, 8, 27], [14, 33, 8, 31], [15, 6, 9, 4, "arr"], [15, 9, 9, 7], [15, 10, 9, 8, "for<PERSON>ach"], [15, 17, 9, 15], [15, 18, 9, 16, "item"], [15, 22, 9, 20], [15, 26, 9, 24], [16, 8, 10, 6], [16, 12, 10, 10, "Array"], [16, 17, 10, 15], [16, 18, 10, 16, "isArray"], [16, 25, 10, 23], [16, 26, 10, 24, "item"], [16, 30, 10, 28], [16, 31, 10, 29], [16, 33, 10, 31], [17, 10, 11, 8, "_flattenArray"], [17, 23, 11, 21], [17, 24, 11, 22, "item"], [17, 28, 11, 26], [17, 29, 11, 27], [18, 8, 12, 6], [18, 9, 12, 7], [18, 15, 12, 13], [19, 10, 13, 8, "resultArr"], [19, 19, 13, 17], [19, 20, 13, 18, "push"], [19, 24, 13, 22], [19, 25, 13, 23, "item"], [19, 29, 13, 27], [19, 30, 13, 28], [20, 8, 14, 6], [21, 6, 15, 4], [21, 7, 15, 5], [21, 8, 15, 6], [22, 4, 16, 2], [22, 5, 16, 3], [23, 4, 17, 2, "_flattenArray"], [23, 17, 17, 15], [23, 18, 17, 16, "array"], [23, 23, 17, 21], [23, 24, 17, 22], [24, 4, 18, 2], [24, 11, 18, 9, "resultArr"], [24, 20, 18, 18], [25, 2, 19, 0], [26, 2, 20, 7], [26, 8, 20, 13, "has"], [26, 11, 20, 16], [26, 14, 20, 19, "has"], [26, 15, 20, 20, "key"], [26, 18, 20, 23], [26, 20, 20, 25, "x"], [26, 21, 20, 26], [26, 26, 20, 31], [27, 4, 21, 2], [27, 8, 21, 6], [27, 15, 21, 13, "x"], [27, 16, 21, 14], [27, 21, 21, 19], [27, 31, 21, 29], [27, 35, 21, 33], [27, 42, 21, 40, "x"], [27, 43, 21, 41], [27, 48, 21, 46], [27, 56, 21, 54], [27, 58, 21, 56], [28, 6, 22, 4], [28, 10, 22, 8, "x"], [28, 11, 22, 9], [28, 16, 22, 14], [28, 20, 22, 18], [28, 24, 22, 22, "x"], [28, 25, 22, 23], [28, 30, 22, 28, "undefined"], [28, 39, 22, 37], [28, 41, 22, 39], [29, 8, 23, 6], [29, 15, 23, 13], [29, 20, 23, 18], [30, 6, 24, 4], [30, 7, 24, 5], [30, 13, 24, 11], [31, 8, 25, 6], [31, 15, 25, 13, "key"], [31, 18, 25, 16], [31, 22, 25, 20, "x"], [31, 23, 25, 21], [32, 6, 26, 4], [33, 4, 27, 2], [34, 4, 28, 2], [34, 11, 28, 9], [34, 16, 28, 14], [35, 2, 29, 0], [35, 3, 29, 1], [36, 2, 29, 2, "exports"], [36, 9, 29, 2], [36, 10, 29, 2, "has"], [36, 13, 29, 2], [36, 16, 29, 2, "has"], [36, 19, 29, 2], [37, 0, 29, 2], [37, 3]], "functionMap": {"names": ["<global>", "flattenArray", "_flattenArray", "arr.forEach$argument_0", "has"], "mappings": "AAA;OCE;wBCK;gBCC;KDM;GDC;CDG;mBIC;CJS"}}, "type": "js/module"}]}