{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Compare two arrays with primitive values as the content.\n   * We need to make sure that both values and order match.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isArrayEqual = isArrayEqual;\n  function isArrayEqual(a, b) {\n    if (a === b) {\n      return true;\n    }\n    if (a.length !== b.length) {\n      return false;\n    }\n    return a.every((it, index) => it === b[index]);\n  }\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 2, 3, 0, "Object"], [8, 8, 3, 0], [8, 9, 3, 0, "defineProperty"], [8, 23, 3, 0], [8, 24, 3, 0, "exports"], [8, 31, 3, 0], [9, 4, 3, 0, "value"], [9, 9, 3, 0], [10, 2, 3, 0], [11, 2, 3, 0, "exports"], [11, 9, 3, 0], [11, 10, 3, 0, "isArrayEqual"], [11, 22, 3, 0], [11, 25, 3, 0, "isArrayEqual"], [11, 37, 3, 0], [12, 2, 7, 7], [12, 11, 7, 16, "isArrayEqual"], [12, 23, 7, 28, "isArrayEqual"], [12, 24, 7, 29, "a"], [12, 25, 7, 30], [12, 27, 7, 32, "b"], [12, 28, 7, 33], [12, 30, 7, 35], [13, 4, 8, 2], [13, 8, 8, 6, "a"], [13, 9, 8, 7], [13, 14, 8, 12, "b"], [13, 15, 8, 13], [13, 17, 8, 15], [14, 6, 9, 4], [14, 13, 9, 11], [14, 17, 9, 15], [15, 4, 10, 2], [16, 4, 11, 2], [16, 8, 11, 6, "a"], [16, 9, 11, 7], [16, 10, 11, 8, "length"], [16, 16, 11, 14], [16, 21, 11, 19, "b"], [16, 22, 11, 20], [16, 23, 11, 21, "length"], [16, 29, 11, 27], [16, 31, 11, 29], [17, 6, 12, 4], [17, 13, 12, 11], [17, 18, 12, 16], [18, 4, 13, 2], [19, 4, 14, 2], [19, 11, 14, 9, "a"], [19, 12, 14, 10], [19, 13, 14, 11, "every"], [19, 18, 14, 16], [19, 19, 14, 17], [19, 20, 14, 18, "it"], [19, 22, 14, 20], [19, 24, 14, 22, "index"], [19, 29, 14, 27], [19, 34, 14, 32, "it"], [19, 36, 14, 34], [19, 41, 14, 39, "b"], [19, 42, 14, 40], [19, 43, 14, 41, "index"], [19, 48, 14, 46], [19, 49, 14, 47], [19, 50, 14, 48], [20, 2, 15, 0], [21, 0, 15, 1], [21, 3]], "functionMap": {"names": ["<global>", "isArrayEqual", "a.every$argument_0"], "mappings": "AAA;OCM;iBCO,8BD;CDC"}}, "type": "js/module"}]}