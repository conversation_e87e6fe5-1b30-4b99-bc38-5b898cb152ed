{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 42, "index": 56}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 113}, "end": {"line": 5, "column": 51, "index": 164}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../shareables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 165}, "end": {"line": 6, "column": 46, "index": 211}}], "key": "FdbDFSDZ4WXqE0/hGYXRyz7sZ44=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 282}, "end": {"line": 8, "column": 66, "index": 348}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useHandler = useHandler;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _PlatformChecker = require(_dependencyMap[1], \"../PlatformChecker\");\n  var _shareables = require(_dependencyMap[2], \"../shareables\");\n  var _utils = require(_dependencyMap[3], \"./utils\");\n  /**\n   * Lets you find out whether the event handler dependencies have changed.\n   *\n   * @param handlers - An object of event handlers.\n   * @param dependencies - An optional array of dependencies.\n   * @returns An object containing a boolean indicating whether the dependencies\n   *   have changed, and a boolean indicating whether the code is running on the\n   *   web.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useHandler\n   */\n  // @ts-expect-error This overload is required by our API.\n\n  function useHandler(handlers, dependencies) {\n    var initRef = (0, _react.useRef)(null);\n    if (initRef.current === null) {\n      var _context = (0, _shareables.makeShareable)({});\n      initRef.current = {\n        context: _context,\n        savedDependencies: []\n      };\n    }\n    (0, _react.useEffect)(() => {\n      return () => {\n        initRef.current = null;\n      };\n    }, []);\n    var _initRef$current = initRef.current,\n      context = _initRef$current.context,\n      savedDependencies = _initRef$current.savedDependencies;\n    dependencies = (0, _utils.buildDependencies)(dependencies, handlers);\n    var doDependenciesDiffer = !(0, _utils.areDependenciesEqual)(dependencies, savedDependencies);\n    initRef.current.savedDependencies = dependencies;\n    var useWeb = (0, _PlatformChecker.isWeb)() || (0, _PlatformChecker.isJest)();\n    return {\n      context,\n      doDependenciesDiffer,\n      useWeb\n    };\n  }\n});", "lineCount": 51, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useHandler"], [7, 20, 1, 13], [7, 23, 1, 13, "useHandler"], [7, 33, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_PlatformChecker"], [9, 22, 5, 0], [9, 25, 5, 0, "require"], [9, 32, 5, 0], [9, 33, 5, 0, "_dependencyMap"], [9, 47, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_shareables"], [10, 17, 6, 0], [10, 20, 6, 0, "require"], [10, 27, 6, 0], [10, 28, 6, 0, "_dependencyMap"], [10, 42, 6, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_utils"], [11, 12, 8, 0], [11, 15, 8, 0, "require"], [11, 22, 8, 0], [11, 23, 8, 0, "_dependencyMap"], [11, 37, 8, 0], [12, 2, 43, 0], [13, 0, 44, 0], [14, 0, 45, 0], [15, 0, 46, 0], [16, 0, 47, 0], [17, 0, 48, 0], [18, 0, 49, 0], [19, 0, 50, 0], [20, 0, 51, 0], [21, 0, 52, 0], [22, 2, 53, 0], [24, 2, 62, 7], [24, 11, 62, 16, "useHandler"], [24, 21, 62, 26, "useHandler"], [24, 22, 66, 2, "handlers"], [24, 30, 66, 50], [24, 32, 67, 2, "dependencies"], [24, 44, 67, 31], [24, 46, 68, 30], [25, 4, 69, 2], [25, 8, 69, 8, "initRef"], [25, 15, 69, 15], [25, 18, 69, 18], [25, 22, 69, 18, "useRef"], [25, 35, 69, 24], [25, 37, 69, 66], [25, 41, 69, 70], [25, 42, 69, 71], [26, 4, 70, 2], [26, 8, 70, 6, "initRef"], [26, 15, 70, 13], [26, 16, 70, 14, "current"], [26, 23, 70, 21], [26, 28, 70, 26], [26, 32, 70, 30], [26, 34, 70, 32], [27, 6, 71, 4], [27, 10, 71, 10, "context"], [27, 18, 71, 17], [27, 21, 71, 20], [27, 25, 71, 20, "makeShareable"], [27, 50, 71, 33], [27, 52, 71, 34], [27, 53, 71, 35], [27, 54, 71, 47], [27, 55, 71, 48], [28, 6, 72, 4, "initRef"], [28, 13, 72, 11], [28, 14, 72, 12, "current"], [28, 21, 72, 19], [28, 24, 72, 22], [29, 8, 73, 6, "context"], [29, 15, 73, 13], [29, 17, 73, 6, "context"], [29, 25, 73, 13], [30, 8, 74, 6, "savedDependencies"], [30, 25, 74, 23], [30, 27, 74, 25], [31, 6, 75, 4], [31, 7, 75, 5], [32, 4, 76, 2], [33, 4, 78, 2], [33, 8, 78, 2, "useEffect"], [33, 24, 78, 11], [33, 26, 78, 12], [33, 32, 78, 18], [34, 6, 79, 4], [34, 13, 79, 11], [34, 19, 79, 17], [35, 8, 80, 6, "initRef"], [35, 15, 80, 13], [35, 16, 80, 14, "current"], [35, 23, 80, 21], [35, 26, 80, 24], [35, 30, 80, 28], [36, 6, 81, 4], [36, 7, 81, 5], [37, 4, 82, 2], [37, 5, 82, 3], [37, 7, 82, 5], [37, 9, 82, 7], [37, 10, 82, 8], [38, 4, 84, 2], [38, 8, 84, 2, "_initRef$current"], [38, 24, 84, 2], [38, 27, 84, 41, "initRef"], [38, 34, 84, 48], [38, 35, 84, 49, "current"], [38, 42, 84, 56], [39, 6, 84, 10, "context"], [39, 13, 84, 17], [39, 16, 84, 17, "_initRef$current"], [39, 32, 84, 17], [39, 33, 84, 10, "context"], [39, 40, 84, 17], [40, 6, 84, 19, "savedDependencies"], [40, 23, 84, 36], [40, 26, 84, 36, "_initRef$current"], [40, 42, 84, 36], [40, 43, 84, 19, "savedDependencies"], [40, 60, 84, 36], [41, 4, 86, 2, "dependencies"], [41, 16, 86, 14], [41, 19, 86, 17], [41, 23, 86, 17, "buildDependencies"], [41, 47, 86, 34], [41, 49, 87, 4, "dependencies"], [41, 61, 87, 16], [41, 63, 88, 4, "handlers"], [41, 71, 89, 2], [41, 72, 89, 3], [42, 4, 91, 2], [42, 8, 91, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [42, 28, 91, 28], [42, 31, 91, 31], [42, 32, 91, 32], [42, 36, 91, 32, "areDependenciesEqual"], [42, 63, 91, 52], [42, 65, 92, 4, "dependencies"], [42, 77, 92, 16], [42, 79, 93, 4, "savedDependencies"], [42, 96, 94, 2], [42, 97, 94, 3], [43, 4, 95, 2, "initRef"], [43, 11, 95, 9], [43, 12, 95, 10, "current"], [43, 19, 95, 17], [43, 20, 95, 18, "savedDependencies"], [43, 37, 95, 35], [43, 40, 95, 38, "dependencies"], [43, 52, 95, 50], [44, 4, 96, 2], [44, 8, 96, 8, "useWeb"], [44, 14, 96, 14], [44, 17, 96, 17], [44, 21, 96, 17, "isWeb"], [44, 43, 96, 22], [44, 45, 96, 23], [44, 46, 96, 24], [44, 50, 96, 28], [44, 54, 96, 28, "isJest"], [44, 77, 96, 34], [44, 79, 96, 35], [44, 80, 96, 36], [45, 4, 98, 2], [45, 11, 98, 9], [46, 6, 98, 11, "context"], [46, 13, 98, 18], [47, 6, 98, 20, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [47, 26, 98, 40], [48, 6, 98, 42, "useWeb"], [49, 4, 98, 49], [49, 5, 98, 50], [50, 2, 99, 0], [51, 0, 99, 1], [51, 3]], "functionMap": {"names": ["<global>", "useHandler", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OC6D;YCgB;WCC;KDE;GDC;CDiB"}}, "type": "js/module"}]}