{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 269}, "end": {"line": 4, "column": 36, "index": 305}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 306}, "end": {"line": 5, "column": 59, "index": 365}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}, {"name": "../tools/GestureHandlerOrchestrator", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 366}, "end": {"line": 6, "column": 77, "index": 443}}], "key": "nkMyuZ+jFvH1SEyjdUxCz0RRbms=", "exportNames": ["*"]}}, {"name": "../tools/InteractionManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 444}, "end": {"line": 7, "column": 61, "index": 505}}], "key": "Ic7vMO13iMYXacgQUhqNaXYohkw=", "exportNames": ["*"]}}, {"name": "../tools/PointerTracker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 506}, "end": {"line": 8, "column": 53, "index": 559}}], "key": "ZCIRZYxghjWTdgqLbuODgLDsZiU=", "exportNames": ["*"]}}, {"name": "../../handlers/gestureHandlerCommon", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 560}, "end": {"line": 9, "column": 66, "index": 626}}], "key": "xaaqCODkGxAwJpzGKT+4pXLUAXk=", "exportNames": ["*"]}}, {"name": "../../PointerType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 627}, "end": {"line": 10, "column": 48, "index": 675}}], "key": "PNpP2j+zRZwLQ3w6ZmXPMJNakiU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _interfaces = require(_dependencyMap[2], \"../interfaces\");\n  var _GestureHandlerOrchestrator = _interopRequireDefault(require(_dependencyMap[3], \"../tools/GestureHandlerOrchestrator\"));\n  var _InteractionManager = _interopRequireDefault(require(_dependencyMap[4], \"../tools/InteractionManager\"));\n  var _PointerTracker = _interopRequireDefault(require(_dependencyMap[5], \"../tools/PointerTracker\"));\n  var _gestureHandlerCommon = require(_dependencyMap[6], \"../../handlers/gestureHandlerCommon\");\n  var _PointerType = require(_dependencyMap[7], \"../../PointerType\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  /* eslint-disable @typescript-eslint/no-empty-function */\n\n  class GestureHandler {\n    // Orchestrator properties\n    constructor(delegate) {\n      _defineProperty(this, \"lastSentState\", null);\n      _defineProperty(this, \"_state\", _State.State.UNDETERMINED);\n      _defineProperty(this, \"_shouldCancelWhenOutside\", false);\n      _defineProperty(this, \"hasCustomActivationCriteria\", false);\n      _defineProperty(this, \"_enabled\", false);\n      _defineProperty(this, \"viewRef\", void 0);\n      _defineProperty(this, \"propsRef\", void 0);\n      _defineProperty(this, \"_handlerTag\", void 0);\n      _defineProperty(this, \"_config\", {\n        enabled: false\n      });\n      _defineProperty(this, \"_tracker\", new _PointerTracker.default());\n      _defineProperty(this, \"_activationIndex\", 0);\n      _defineProperty(this, \"_awaiting\", false);\n      _defineProperty(this, \"_active\", false);\n      _defineProperty(this, \"_shouldResetProgress\", false);\n      _defineProperty(this, \"_pointerType\", _PointerType.PointerType.MOUSE);\n      _defineProperty(this, \"_delegate\", void 0);\n      _defineProperty(this, \"sendEvent\", (newState, oldState) => {\n        const {\n          onGestureHandlerEvent,\n          onGestureHandlerStateChange\n        } = this.propsRef.current;\n        const resultEvent = this.transformEventData(newState, oldState); // In the new API oldState field has to be undefined, unless we send event state changed\n        // Here the order is flipped to avoid workarounds such as making backup of the state and setting it to undefined first, then changing it back\n        // Flipping order with setting oldState to undefined solves issue, when events were being sent twice instead of once\n        // However, this may cause trouble in the future (but for now we don't know that)\n\n        if (this.lastSentState !== newState) {\n          this.lastSentState = newState;\n          invokeNullableMethod(onGestureHandlerStateChange, resultEvent);\n        }\n        if (this.state === _State.State.ACTIVE) {\n          resultEvent.nativeEvent.oldState = undefined;\n          invokeNullableMethod(onGestureHandlerEvent, resultEvent);\n        }\n      });\n      this._delegate = delegate;\n    } //\n    // Initializing handler\n    //\n\n    init(viewRef, propsRef) {\n      this.propsRef = propsRef;\n      this.viewRef = viewRef;\n      this.state = _State.State.UNDETERMINED;\n      this.delegate.init(viewRef, this);\n    }\n    attachEventManager(manager) {\n      manager.setOnPointerDown(this.onPointerDown.bind(this));\n      manager.setOnPointerAdd(this.onPointerAdd.bind(this));\n      manager.setOnPointerUp(this.onPointerUp.bind(this));\n      manager.setOnPointerRemove(this.onPointerRemove.bind(this));\n      manager.setOnPointerMove(this.onPointerMove.bind(this));\n      manager.setOnPointerEnter(this.onPointerEnter.bind(this));\n      manager.setOnPointerLeave(this.onPointerLeave.bind(this));\n      manager.setOnPointerCancel(this.onPointerCancel.bind(this));\n      manager.setOnPointerOutOfBounds(this.onPointerOutOfBounds.bind(this));\n      manager.setOnPointerMoveOver(this.onPointerMoveOver.bind(this));\n      manager.setOnPointerMoveOut(this.onPointerMoveOut.bind(this));\n      manager.setOnWheel(this.onWheel.bind(this));\n      manager.registerListeners();\n    } //\n    // Resetting handler\n    //\n\n    onCancel() {}\n    onReset() {}\n    resetProgress() {}\n    reset() {\n      this.tracker.resetTracker();\n      this.onReset();\n      this.resetProgress();\n      this.delegate.reset();\n      this.state = _State.State.UNDETERMINED;\n    } //\n    // State logic\n    //\n\n    moveToState(newState, sendIfDisabled) {\n      if (this.state === newState) {\n        return;\n      }\n      const oldState = this.state;\n      this.state = newState;\n      if (this.tracker.trackedPointersCount > 0 && this.config.needsPointerData && this.isFinished()) {\n        this.cancelTouches();\n      }\n      _GestureHandlerOrchestrator.default.instance.onHandlerStateChange(this, newState, oldState, sendIfDisabled);\n      this.onStateChange(newState, oldState);\n      if (!this.enabled && this.isFinished()) {\n        this.state = _State.State.UNDETERMINED;\n      }\n    }\n    onStateChange(_newState, _oldState) {}\n    begin() {\n      if (!this.checkHitSlop()) {\n        return;\n      }\n      if (this.state === _State.State.UNDETERMINED) {\n        this.moveToState(_State.State.BEGAN);\n      }\n    }\n    /**\n     * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send fail event\n     */\n\n    fail(sendIfDisabled) {\n      if (this.state === _State.State.ACTIVE || this.state === _State.State.BEGAN) {\n        // Here the order of calling the delegate and moveToState is important.\n        // At this point we can use currentState as previuos state, because immediately after changing cursor we call moveToState method.\n        this.delegate.onFail();\n        this.moveToState(_State.State.FAILED, sendIfDisabled);\n      }\n      this.resetProgress();\n    }\n    /**\n     * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send cancel event\n     */\n\n    cancel(sendIfDisabled) {\n      if (this.state === _State.State.ACTIVE || this.state === _State.State.UNDETERMINED || this.state === _State.State.BEGAN) {\n        this.onCancel(); // Same as above - order matters\n\n        this.delegate.onCancel();\n        this.moveToState(_State.State.CANCELLED, sendIfDisabled);\n      }\n    }\n    activate(force = false) {\n      if ((this.config.manualActivation !== true || force) && (this.state === _State.State.UNDETERMINED || this.state === _State.State.BEGAN)) {\n        this.delegate.onActivate();\n        this.moveToState(_State.State.ACTIVE);\n      }\n    }\n    end() {\n      if (this.state === _State.State.BEGAN || this.state === _State.State.ACTIVE) {\n        // Same as above - order matters\n        this.delegate.onEnd();\n        this.moveToState(_State.State.END);\n      }\n      this.resetProgress();\n    } //\n    // Methods for orchestrator\n    //\n\n    getShouldResetProgress() {\n      return this.shouldResetProgress;\n    }\n    setShouldResetProgress(value) {\n      this.shouldResetProgress = value;\n    }\n    shouldWaitForHandlerFailure(handler) {\n      if (handler === this) {\n        return false;\n      }\n      return _InteractionManager.default.instance.shouldWaitForHandlerFailure(this, handler);\n    }\n    shouldRequireToWaitForFailure(handler) {\n      if (handler === this) {\n        return false;\n      }\n      return _InteractionManager.default.instance.shouldRequireHandlerToWaitForFailure(this, handler);\n    }\n    shouldRecognizeSimultaneously(handler) {\n      if (handler === this) {\n        return true;\n      }\n      return _InteractionManager.default.instance.shouldRecognizeSimultaneously(this, handler);\n    }\n    shouldBeCancelledByOther(handler) {\n      if (handler === this) {\n        return false;\n      }\n      return _InteractionManager.default.instance.shouldHandlerBeCancelledBy(this, handler);\n    } //\n    // Event actions\n    //\n\n    onPointerDown(event) {\n      _GestureHandlerOrchestrator.default.instance.recordHandlerIfNotPresent(this);\n      this.pointerType = event.pointerType;\n      if (this.pointerType === _PointerType.PointerType.TOUCH) {\n        _GestureHandlerOrchestrator.default.instance.cancelMouseAndPenGestures(this);\n      } // TODO: Bring back touch events along with introducing `handleDown` method that will handle handler specific stuff\n    } // Adding another pointer to existing ones\n\n    onPointerAdd(event) {\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerUp(event) {\n      this.tryToSendTouchEvent(event);\n    } // Removing pointer, when there is more than one pointers\n\n    onPointerRemove(event) {\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerMove(event) {\n      this.tryToSendMoveEvent(false, event);\n    }\n    onPointerLeave(event) {\n      if (this.shouldCancelWhenOutside) {\n        switch (this.state) {\n          case _State.State.ACTIVE:\n            this.cancel();\n            break;\n          case _State.State.BEGAN:\n            this.fail();\n            break;\n        }\n        return;\n      }\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerEnter(event) {\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerCancel(event) {\n      this.tryToSendTouchEvent(event);\n      this.cancel();\n      this.reset();\n    }\n    onPointerOutOfBounds(event) {\n      this.tryToSendMoveEvent(true, event);\n    }\n    onPointerMoveOver(_event) {// Used only by hover gesture handler atm\n    }\n    onPointerMoveOut(_event) {// Used only by hover gesture handler atm\n    }\n    onWheel(_event) {// Used only by pan gesture handler\n    }\n    tryToSendMoveEvent(out, event) {\n      if (out && this.shouldCancelWhenOutside || !this.enabled) {\n        return;\n      }\n      if (this.active) {\n        this.sendEvent(this.state, this.state);\n      }\n      this.tryToSendTouchEvent(event);\n    }\n    tryToSendTouchEvent(event) {\n      if (this.config.needsPointerData) {\n        this.sendTouchEvent(event);\n      }\n    }\n    sendTouchEvent(event) {\n      if (!this.enabled) {\n        return;\n      }\n      const {\n        onGestureHandlerEvent\n      } = this.propsRef.current;\n      const touchEvent = this.transformTouchEvent(event);\n      if (touchEvent) {\n        invokeNullableMethod(onGestureHandlerEvent, touchEvent);\n      }\n    } //\n    // Events Sending\n    //\n\n    transformEventData(newState, oldState) {\n      return {\n        nativeEvent: {\n          numberOfPointers: this.tracker.trackedPointersCount,\n          state: newState,\n          pointerInside: this.delegate.isPointerInBounds(this.tracker.getAbsoluteCoordsAverage()),\n          ...this.transformNativeEvent(),\n          handlerTag: this.handlerTag,\n          target: this.viewRef,\n          oldState: newState !== oldState ? oldState : undefined,\n          pointerType: this.pointerType\n        },\n        timeStamp: Date.now()\n      };\n    }\n    transformTouchEvent(event) {\n      const rect = this.delegate.measureView();\n      const all = [];\n      const changed = [];\n      const trackerData = this.tracker.trackedPointers; // This if handles edge case where all pointers have been cancelled\n      // When pointercancel is triggered, reset method is called. This means that tracker will be reset after first pointer being cancelled\n      // The problem is, that handler will receive another pointercancel event from the rest of the pointers\n      // To avoid crashing, we don't send event if tracker tracks no pointers, i.e. has been reset\n\n      if (trackerData.size === 0 || !trackerData.has(event.pointerId)) {\n        return;\n      }\n      trackerData.forEach((element, key) => {\n        const id = this.tracker.getMappedTouchEventId(key);\n        all.push({\n          id: id,\n          x: element.abosoluteCoords.x - rect.pageX,\n          y: element.abosoluteCoords.y - rect.pageY,\n          absoluteX: element.abosoluteCoords.x,\n          absoluteY: element.abosoluteCoords.y\n        });\n      }); // Each pointer sends its own event, so we want changed touches to contain only the pointer that has changed.\n      // However, if the event is cancel, we want to cancel all pointers to avoid crashes\n\n      if (event.eventType !== _interfaces.EventTypes.CANCEL) {\n        changed.push({\n          id: this.tracker.getMappedTouchEventId(event.pointerId),\n          x: event.x - rect.pageX,\n          y: event.y - rect.pageY,\n          absoluteX: event.x,\n          absoluteY: event.y\n        });\n      } else {\n        trackerData.forEach((element, key) => {\n          const id = this.tracker.getMappedTouchEventId(key);\n          changed.push({\n            id: id,\n            x: element.abosoluteCoords.x - rect.pageX,\n            y: element.abosoluteCoords.y - rect.pageY,\n            absoluteX: element.abosoluteCoords.x,\n            absoluteY: element.abosoluteCoords.y\n          });\n        });\n      }\n      let eventType = _interfaces.TouchEventType.UNDETERMINED;\n      switch (event.eventType) {\n        case _interfaces.EventTypes.DOWN:\n        case _interfaces.EventTypes.ADDITIONAL_POINTER_DOWN:\n          eventType = _interfaces.TouchEventType.DOWN;\n          break;\n        case _interfaces.EventTypes.UP:\n        case _interfaces.EventTypes.ADDITIONAL_POINTER_UP:\n          eventType = _interfaces.TouchEventType.UP;\n          break;\n        case _interfaces.EventTypes.MOVE:\n          eventType = _interfaces.TouchEventType.MOVE;\n          break;\n        case _interfaces.EventTypes.CANCEL:\n          eventType = _interfaces.TouchEventType.CANCELLED;\n          break;\n      } // Here, when we receive up event, we want to decrease number of touches\n      // That's because we want handler to send information that there's one pointer less\n      // However, we still want this pointer to be present in allTouches array, so that its data can be accessed\n\n      let numberOfTouches = all.length;\n      if (event.eventType === _interfaces.EventTypes.UP || event.eventType === _interfaces.EventTypes.ADDITIONAL_POINTER_UP) {\n        --numberOfTouches;\n      }\n      return {\n        nativeEvent: {\n          handlerTag: this.handlerTag,\n          state: this.state,\n          eventType: eventType,\n          changedTouches: changed,\n          allTouches: all,\n          numberOfTouches: numberOfTouches,\n          pointerType: this.pointerType\n        },\n        timeStamp: Date.now()\n      };\n    }\n    cancelTouches() {\n      const rect = this.delegate.measureView();\n      const all = [];\n      const changed = [];\n      const trackerData = this.tracker.trackedPointers;\n      if (trackerData.size === 0) {\n        return;\n      }\n      trackerData.forEach((element, key) => {\n        const id = this.tracker.getMappedTouchEventId(key);\n        all.push({\n          id: id,\n          x: element.abosoluteCoords.x - rect.pageX,\n          y: element.abosoluteCoords.y - rect.pageY,\n          absoluteX: element.abosoluteCoords.x,\n          absoluteY: element.abosoluteCoords.y\n        });\n        changed.push({\n          id: id,\n          x: element.abosoluteCoords.x - rect.pageX,\n          y: element.abosoluteCoords.y - rect.pageY,\n          absoluteX: element.abosoluteCoords.x,\n          absoluteY: element.abosoluteCoords.y\n        });\n      });\n      const cancelEvent = {\n        nativeEvent: {\n          handlerTag: this.handlerTag,\n          state: this.state,\n          eventType: _interfaces.TouchEventType.CANCELLED,\n          changedTouches: changed,\n          allTouches: all,\n          numberOfTouches: all.length,\n          pointerType: this.pointerType\n        },\n        timeStamp: Date.now()\n      };\n      const {\n        onGestureHandlerEvent\n      } = this.propsRef.current;\n      invokeNullableMethod(onGestureHandlerEvent, cancelEvent);\n    }\n    transformNativeEvent() {\n      // Those properties are shared by most handlers and if not this method will be overriden\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      const lastRelativeCoords = this.tracker.getRelativeCoordsAverage();\n      return {\n        x: lastRelativeCoords.x,\n        y: lastRelativeCoords.y,\n        absoluteX: lastCoords.x,\n        absoluteY: lastCoords.y\n      };\n    } //\n    // Handling config\n    //\n\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      this._config = {\n        enabled: enabled,\n        ...props\n      };\n      this.enabled = enabled;\n      this.delegate.onEnabledChange(enabled);\n      if (this.config.shouldCancelWhenOutside !== undefined) {\n        this.shouldCancelWhenOutside = this.config.shouldCancelWhenOutside;\n      }\n      this.validateHitSlops();\n      if (this.enabled) {\n        return;\n      }\n      switch (this.state) {\n        case _State.State.ACTIVE:\n          this.fail(true);\n          break;\n        case _State.State.UNDETERMINED:\n          _GestureHandlerOrchestrator.default.instance.removeHandlerFromOrchestrator(this);\n          break;\n        default:\n          this.cancel(true);\n          break;\n      }\n    }\n    checkCustomActivationCriteria(criterias) {\n      for (const key in this.config) {\n        if (criterias.indexOf(key) >= 0) {\n          this.hasCustomActivationCriteria = true;\n        }\n      }\n    }\n    validateHitSlops() {\n      if (!this.config.hitSlop) {\n        return;\n      }\n      if (this.config.hitSlop.left !== undefined && this.config.hitSlop.right !== undefined && this.config.hitSlop.width !== undefined) {\n        throw new Error('HitSlop Error: Cannot define left, right and width at the same time');\n      }\n      if (this.config.hitSlop.width !== undefined && this.config.hitSlop.left === undefined && this.config.hitSlop.right === undefined) {\n        throw new Error('HitSlop Error: When width is defined, either left or right has to be defined');\n      }\n      if (this.config.hitSlop.height !== undefined && this.config.hitSlop.top !== undefined && this.config.hitSlop.bottom !== undefined) {\n        throw new Error('HitSlop Error: Cannot define top, bottom and height at the same time');\n      }\n      if (this.config.hitSlop.height !== undefined && this.config.hitSlop.top === undefined && this.config.hitSlop.bottom === undefined) {\n        throw new Error('HitSlop Error: When height is defined, either top or bottom has to be defined');\n      }\n    }\n    checkHitSlop() {\n      if (!this.config.hitSlop) {\n        return true;\n      }\n      const {\n        width,\n        height\n      } = this.delegate.measureView();\n      let left = 0;\n      let top = 0;\n      let right = width;\n      let bottom = height;\n      if (this.config.hitSlop.horizontal !== undefined) {\n        left -= this.config.hitSlop.horizontal;\n        right += this.config.hitSlop.horizontal;\n      }\n      if (this.config.hitSlop.vertical !== undefined) {\n        top -= this.config.hitSlop.vertical;\n        bottom += this.config.hitSlop.vertical;\n      }\n      if (this.config.hitSlop.left !== undefined) {\n        left = -this.config.hitSlop.left;\n      }\n      if (this.config.hitSlop.right !== undefined) {\n        right = width + this.config.hitSlop.right;\n      }\n      if (this.config.hitSlop.top !== undefined) {\n        top = -this.config.hitSlop.top;\n      }\n      if (this.config.hitSlop.bottom !== undefined) {\n        bottom = width + this.config.hitSlop.bottom;\n      }\n      if (this.config.hitSlop.width !== undefined) {\n        if (this.config.hitSlop.left !== undefined) {\n          right = left + this.config.hitSlop.width;\n        } else if (this.config.hitSlop.right !== undefined) {\n          left = right - this.config.hitSlop.width;\n        }\n      }\n      if (this.config.hitSlop.height !== undefined) {\n        if (this.config.hitSlop.top !== undefined) {\n          bottom = top + this.config.hitSlop.height;\n        } else if (this.config.hitSlop.bottom !== undefined) {\n          top = bottom - this.config.hitSlop.height;\n        }\n      }\n      const rect = this.delegate.measureView();\n      const {\n        x,\n        y\n      } = this.tracker.getLastAbsoluteCoords();\n      const offsetX = x - rect.pageX;\n      const offsetY = y - rect.pageY;\n      return offsetX >= left && offsetX <= right && offsetY >= top && offsetY <= bottom;\n    }\n    isButtonInConfig(mouseButton) {\n      return !mouseButton || !this.config.mouseButton && mouseButton === _gestureHandlerCommon.MouseButton.LEFT || this.config.mouseButton && mouseButton & this.config.mouseButton;\n    }\n    resetConfig() {}\n    onDestroy() {\n      this.delegate.destroy(this.config);\n    } //\n    // Getters and setters\n    //\n\n    get handlerTag() {\n      return this._handlerTag;\n    }\n    set handlerTag(value) {\n      this._handlerTag = value;\n    }\n    get config() {\n      return this._config;\n    }\n    get delegate() {\n      return this._delegate;\n    }\n    get tracker() {\n      return this._tracker;\n    }\n    get state() {\n      return this._state;\n    }\n    set state(value) {\n      this._state = value;\n    }\n    get shouldCancelWhenOutside() {\n      return this._shouldCancelWhenOutside;\n    }\n    set shouldCancelWhenOutside(value) {\n      this._shouldCancelWhenOutside = value;\n    }\n    get enabled() {\n      return this._enabled;\n    }\n    set enabled(value) {\n      this._enabled = value;\n    }\n    get pointerType() {\n      return this._pointerType;\n    }\n    set pointerType(value) {\n      this._pointerType = value;\n    }\n    get active() {\n      return this._active;\n    }\n    set active(value) {\n      this._active = value;\n    }\n    get awaiting() {\n      return this._awaiting;\n    }\n    set awaiting(value) {\n      this._awaiting = value;\n    }\n    get activationIndex() {\n      return this._activationIndex;\n    }\n    set activationIndex(value) {\n      this._activationIndex = value;\n    }\n    get shouldResetProgress() {\n      return this._shouldResetProgress;\n    }\n    set shouldResetProgress(value) {\n      this._shouldResetProgress = value;\n    }\n    getTrackedPointersID() {\n      return this.tracker.trackedPointersIDs;\n    }\n    isFinished() {\n      return this.state === _State.State.END || this.state === _State.State.FAILED || this.state === _State.State.CANCELLED;\n    }\n  }\n  exports.default = GestureHandler;\n  function invokeNullableMethod(method, event) {\n    if (!method) {\n      return;\n    }\n    if (typeof method === 'function') {\n      method(event);\n      return;\n    }\n    if ('__getHandler' in method && typeof method.__getHandler === 'function') {\n      const handler = method.__getHandler();\n      invokeNullableMethod(handler, event);\n      return;\n    }\n    if (!('__nodeConfig' in method)) {\n      return;\n    }\n    const {\n      argMapping\n    } = method.__nodeConfig;\n    if (!Array.isArray(argMapping)) {\n      return;\n    }\n    for (const [index, [key, value]] of argMapping.entries()) {\n      if (!(key in event.nativeEvent)) {\n        continue;\n      } // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n\n      const nativeValue = event.nativeEvent[key]; // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n\n      if (value !== null && value !== void 0 && value.setValue) {\n        // Reanimated API\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call\n        value.setValue(nativeValue);\n      } else {\n        // RN Animated API\n        method.__nodeConfig.argMapping[index] = [key, nativeValue];\n      }\n    }\n    return;\n  }\n});", "lineCount": 673, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_State"], [7, 12, 4, 0], [7, 15, 4, 0, "require"], [7, 22, 4, 0], [7, 23, 4, 0, "_dependencyMap"], [7, 37, 4, 0], [8, 2, 5, 0], [8, 6, 5, 0, "_interfaces"], [8, 17, 5, 0], [8, 20, 5, 0, "require"], [8, 27, 5, 0], [8, 28, 5, 0, "_dependencyMap"], [8, 42, 5, 0], [9, 2, 6, 0], [9, 6, 6, 0, "_GestureHandlerOrchestrator"], [9, 33, 6, 0], [9, 36, 6, 0, "_interopRequireDefault"], [9, 58, 6, 0], [9, 59, 6, 0, "require"], [9, 66, 6, 0], [9, 67, 6, 0, "_dependencyMap"], [9, 81, 6, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_InteractionManager"], [10, 25, 7, 0], [10, 28, 7, 0, "_interopRequireDefault"], [10, 50, 7, 0], [10, 51, 7, 0, "require"], [10, 58, 7, 0], [10, 59, 7, 0, "_dependencyMap"], [10, 73, 7, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_PointerTracker"], [11, 21, 8, 0], [11, 24, 8, 0, "_interopRequireDefault"], [11, 46, 8, 0], [11, 47, 8, 0, "require"], [11, 54, 8, 0], [11, 55, 8, 0, "_dependencyMap"], [11, 69, 8, 0], [12, 2, 9, 0], [12, 6, 9, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [12, 27, 9, 0], [12, 30, 9, 0, "require"], [12, 37, 9, 0], [12, 38, 9, 0, "_dependencyMap"], [12, 52, 9, 0], [13, 2, 10, 0], [13, 6, 10, 0, "_PointerType"], [13, 18, 10, 0], [13, 21, 10, 0, "require"], [13, 28, 10, 0], [13, 29, 10, 0, "_dependencyMap"], [13, 43, 10, 0], [14, 2, 1, 0], [14, 11, 1, 9, "_defineProperty"], [14, 26, 1, 24, "_defineProperty"], [14, 27, 1, 25, "obj"], [14, 30, 1, 28], [14, 32, 1, 30, "key"], [14, 35, 1, 33], [14, 37, 1, 35, "value"], [14, 42, 1, 40], [14, 44, 1, 42], [15, 4, 1, 44], [15, 8, 1, 48, "key"], [15, 11, 1, 51], [15, 15, 1, 55, "obj"], [15, 18, 1, 58], [15, 20, 1, 60], [16, 6, 1, 62, "Object"], [16, 12, 1, 68], [16, 13, 1, 69, "defineProperty"], [16, 27, 1, 83], [16, 28, 1, 84, "obj"], [16, 31, 1, 87], [16, 33, 1, 89, "key"], [16, 36, 1, 92], [16, 38, 1, 94], [17, 8, 1, 96, "value"], [17, 13, 1, 101], [17, 15, 1, 103, "value"], [17, 20, 1, 108], [18, 8, 1, 110, "enumerable"], [18, 18, 1, 120], [18, 20, 1, 122], [18, 24, 1, 126], [19, 8, 1, 128, "configurable"], [19, 20, 1, 140], [19, 22, 1, 142], [19, 26, 1, 146], [20, 8, 1, 148, "writable"], [20, 16, 1, 156], [20, 18, 1, 158], [21, 6, 1, 163], [21, 7, 1, 164], [21, 8, 1, 165], [22, 4, 1, 167], [22, 5, 1, 168], [22, 11, 1, 174], [23, 6, 1, 176, "obj"], [23, 9, 1, 179], [23, 10, 1, 180, "key"], [23, 13, 1, 183], [23, 14, 1, 184], [23, 17, 1, 187, "value"], [23, 22, 1, 192], [24, 4, 1, 194], [25, 4, 1, 196], [25, 11, 1, 203, "obj"], [25, 14, 1, 206], [26, 2, 1, 208], [28, 2, 3, 0], [30, 2, 11, 15], [30, 8, 11, 21, "Gesture<PERSON>andler"], [30, 22, 11, 35], [30, 23, 11, 36], [31, 4, 12, 2], [32, 4, 13, 2, "constructor"], [32, 15, 13, 13, "constructor"], [32, 16, 13, 14, "delegate"], [32, 24, 13, 22], [32, 26, 13, 24], [33, 6, 14, 4, "_defineProperty"], [33, 21, 14, 19], [33, 22, 14, 20], [33, 26, 14, 24], [33, 28, 14, 26], [33, 43, 14, 41], [33, 45, 14, 43], [33, 49, 14, 47], [33, 50, 14, 48], [34, 6, 16, 4, "_defineProperty"], [34, 21, 16, 19], [34, 22, 16, 20], [34, 26, 16, 24], [34, 28, 16, 26], [34, 36, 16, 34], [34, 38, 16, 36, "State"], [34, 50, 16, 41], [34, 51, 16, 42, "UNDETERMINED"], [34, 63, 16, 54], [34, 64, 16, 55], [35, 6, 18, 4, "_defineProperty"], [35, 21, 18, 19], [35, 22, 18, 20], [35, 26, 18, 24], [35, 28, 18, 26], [35, 54, 18, 52], [35, 56, 18, 54], [35, 61, 18, 59], [35, 62, 18, 60], [36, 6, 20, 4, "_defineProperty"], [36, 21, 20, 19], [36, 22, 20, 20], [36, 26, 20, 24], [36, 28, 20, 26], [36, 57, 20, 55], [36, 59, 20, 57], [36, 64, 20, 62], [36, 65, 20, 63], [37, 6, 22, 4, "_defineProperty"], [37, 21, 22, 19], [37, 22, 22, 20], [37, 26, 22, 24], [37, 28, 22, 26], [37, 38, 22, 36], [37, 40, 22, 38], [37, 45, 22, 43], [37, 46, 22, 44], [38, 6, 24, 4, "_defineProperty"], [38, 21, 24, 19], [38, 22, 24, 20], [38, 26, 24, 24], [38, 28, 24, 26], [38, 37, 24, 35], [38, 39, 24, 37], [38, 44, 24, 42], [38, 45, 24, 43], [38, 46, 24, 44], [39, 6, 26, 4, "_defineProperty"], [39, 21, 26, 19], [39, 22, 26, 20], [39, 26, 26, 24], [39, 28, 26, 26], [39, 38, 26, 36], [39, 40, 26, 38], [39, 45, 26, 43], [39, 46, 26, 44], [39, 47, 26, 45], [40, 6, 28, 4, "_defineProperty"], [40, 21, 28, 19], [40, 22, 28, 20], [40, 26, 28, 24], [40, 28, 28, 26], [40, 41, 28, 39], [40, 43, 28, 41], [40, 48, 28, 46], [40, 49, 28, 47], [40, 50, 28, 48], [41, 6, 30, 4, "_defineProperty"], [41, 21, 30, 19], [41, 22, 30, 20], [41, 26, 30, 24], [41, 28, 30, 26], [41, 37, 30, 35], [41, 39, 30, 37], [42, 8, 31, 6, "enabled"], [42, 15, 31, 13], [42, 17, 31, 15], [43, 6, 32, 4], [43, 7, 32, 5], [43, 8, 32, 6], [44, 6, 34, 4, "_defineProperty"], [44, 21, 34, 19], [44, 22, 34, 20], [44, 26, 34, 24], [44, 28, 34, 26], [44, 38, 34, 36], [44, 40, 34, 38], [44, 44, 34, 42, "PointerTracker"], [44, 67, 34, 56], [44, 68, 34, 57], [44, 69, 34, 58], [44, 70, 34, 59], [45, 6, 36, 4, "_defineProperty"], [45, 21, 36, 19], [45, 22, 36, 20], [45, 26, 36, 24], [45, 28, 36, 26], [45, 46, 36, 44], [45, 48, 36, 46], [45, 49, 36, 47], [45, 50, 36, 48], [46, 6, 38, 4, "_defineProperty"], [46, 21, 38, 19], [46, 22, 38, 20], [46, 26, 38, 24], [46, 28, 38, 26], [46, 39, 38, 37], [46, 41, 38, 39], [46, 46, 38, 44], [46, 47, 38, 45], [47, 6, 40, 4, "_defineProperty"], [47, 21, 40, 19], [47, 22, 40, 20], [47, 26, 40, 24], [47, 28, 40, 26], [47, 37, 40, 35], [47, 39, 40, 37], [47, 44, 40, 42], [47, 45, 40, 43], [48, 6, 42, 4, "_defineProperty"], [48, 21, 42, 19], [48, 22, 42, 20], [48, 26, 42, 24], [48, 28, 42, 26], [48, 50, 42, 48], [48, 52, 42, 50], [48, 57, 42, 55], [48, 58, 42, 56], [49, 6, 44, 4, "_defineProperty"], [49, 21, 44, 19], [49, 22, 44, 20], [49, 26, 44, 24], [49, 28, 44, 26], [49, 42, 44, 40], [49, 44, 44, 42, "PointerType"], [49, 68, 44, 53], [49, 69, 44, 54, "MOUSE"], [49, 74, 44, 59], [49, 75, 44, 60], [50, 6, 46, 4, "_defineProperty"], [50, 21, 46, 19], [50, 22, 46, 20], [50, 26, 46, 24], [50, 28, 46, 26], [50, 39, 46, 37], [50, 41, 46, 39], [50, 46, 46, 44], [50, 47, 46, 45], [50, 48, 46, 46], [51, 6, 48, 4, "_defineProperty"], [51, 21, 48, 19], [51, 22, 48, 20], [51, 26, 48, 24], [51, 28, 48, 26], [51, 39, 48, 37], [51, 41, 48, 39], [51, 42, 48, 40, "newState"], [51, 50, 48, 48], [51, 52, 48, 50, "oldState"], [51, 60, 48, 58], [51, 65, 48, 63], [52, 8, 49, 6], [52, 14, 49, 12], [53, 10, 50, 8, "onGestureHandlerEvent"], [53, 31, 50, 29], [54, 10, 51, 8, "onGestureHandlerStateChange"], [55, 8, 52, 6], [55, 9, 52, 7], [55, 12, 52, 10], [55, 16, 52, 14], [55, 17, 52, 15, "propsRef"], [55, 25, 52, 23], [55, 26, 52, 24, "current"], [55, 33, 52, 31], [56, 8, 53, 6], [56, 14, 53, 12, "resultEvent"], [56, 25, 53, 23], [56, 28, 53, 26], [56, 32, 53, 30], [56, 33, 53, 31, "transformEventData"], [56, 51, 53, 49], [56, 52, 53, 50, "newState"], [56, 60, 53, 58], [56, 62, 53, 60, "oldState"], [56, 70, 53, 68], [56, 71, 53, 69], [56, 72, 53, 70], [56, 73, 53, 71], [57, 8, 54, 6], [58, 8, 55, 6], [59, 8, 56, 6], [61, 8, 58, 6], [61, 12, 58, 10], [61, 16, 58, 14], [61, 17, 58, 15, "lastSentState"], [61, 30, 58, 28], [61, 35, 58, 33, "newState"], [61, 43, 58, 41], [61, 45, 58, 43], [62, 10, 59, 8], [62, 14, 59, 12], [62, 15, 59, 13, "lastSentState"], [62, 28, 59, 26], [62, 31, 59, 29, "newState"], [62, 39, 59, 37], [63, 10, 60, 8, "invokeNullableMethod"], [63, 30, 60, 28], [63, 31, 60, 29, "onGestureHandlerStateChange"], [63, 58, 60, 56], [63, 60, 60, 58, "resultEvent"], [63, 71, 60, 69], [63, 72, 60, 70], [64, 8, 61, 6], [65, 8, 63, 6], [65, 12, 63, 10], [65, 16, 63, 14], [65, 17, 63, 15, "state"], [65, 22, 63, 20], [65, 27, 63, 25, "State"], [65, 39, 63, 30], [65, 40, 63, 31, "ACTIVE"], [65, 46, 63, 37], [65, 48, 63, 39], [66, 10, 64, 8, "resultEvent"], [66, 21, 64, 19], [66, 22, 64, 20, "nativeEvent"], [66, 33, 64, 31], [66, 34, 64, 32, "oldState"], [66, 42, 64, 40], [66, 45, 64, 43, "undefined"], [66, 54, 64, 52], [67, 10, 65, 8, "invokeNullableMethod"], [67, 30, 65, 28], [67, 31, 65, 29, "onGestureHandlerEvent"], [67, 52, 65, 50], [67, 54, 65, 52, "resultEvent"], [67, 65, 65, 63], [67, 66, 65, 64], [68, 8, 66, 6], [69, 6, 67, 4], [69, 7, 67, 5], [69, 8, 67, 6], [70, 6, 69, 4], [70, 10, 69, 8], [70, 11, 69, 9, "_delegate"], [70, 20, 69, 18], [70, 23, 69, 21, "delegate"], [70, 31, 69, 29], [71, 4, 70, 2], [71, 5, 70, 3], [71, 6, 70, 4], [72, 4, 71, 2], [73, 4, 72, 2], [75, 4, 75, 2, "init"], [75, 8, 75, 6, "init"], [75, 9, 75, 7, "viewRef"], [75, 16, 75, 14], [75, 18, 75, 16, "propsRef"], [75, 26, 75, 24], [75, 28, 75, 26], [76, 6, 76, 4], [76, 10, 76, 8], [76, 11, 76, 9, "propsRef"], [76, 19, 76, 17], [76, 22, 76, 20, "propsRef"], [76, 30, 76, 28], [77, 6, 77, 4], [77, 10, 77, 8], [77, 11, 77, 9, "viewRef"], [77, 18, 77, 16], [77, 21, 77, 19, "viewRef"], [77, 28, 77, 26], [78, 6, 78, 4], [78, 10, 78, 8], [78, 11, 78, 9, "state"], [78, 16, 78, 14], [78, 19, 78, 17, "State"], [78, 31, 78, 22], [78, 32, 78, 23, "UNDETERMINED"], [78, 44, 78, 35], [79, 6, 79, 4], [79, 10, 79, 8], [79, 11, 79, 9, "delegate"], [79, 19, 79, 17], [79, 20, 79, 18, "init"], [79, 24, 79, 22], [79, 25, 79, 23, "viewRef"], [79, 32, 79, 30], [79, 34, 79, 32], [79, 38, 79, 36], [79, 39, 79, 37], [80, 4, 80, 2], [81, 4, 82, 2, "attachEventManager"], [81, 22, 82, 20, "attachEventManager"], [81, 23, 82, 21, "manager"], [81, 30, 82, 28], [81, 32, 82, 30], [82, 6, 83, 4, "manager"], [82, 13, 83, 11], [82, 14, 83, 12, "setOnPointerDown"], [82, 30, 83, 28], [82, 31, 83, 29], [82, 35, 83, 33], [82, 36, 83, 34, "onPointerDown"], [82, 49, 83, 47], [82, 50, 83, 48, "bind"], [82, 54, 83, 52], [82, 55, 83, 53], [82, 59, 83, 57], [82, 60, 83, 58], [82, 61, 83, 59], [83, 6, 84, 4, "manager"], [83, 13, 84, 11], [83, 14, 84, 12, "setOnPointerAdd"], [83, 29, 84, 27], [83, 30, 84, 28], [83, 34, 84, 32], [83, 35, 84, 33, "onPointerAdd"], [83, 47, 84, 45], [83, 48, 84, 46, "bind"], [83, 52, 84, 50], [83, 53, 84, 51], [83, 57, 84, 55], [83, 58, 84, 56], [83, 59, 84, 57], [84, 6, 85, 4, "manager"], [84, 13, 85, 11], [84, 14, 85, 12, "setOnPointerUp"], [84, 28, 85, 26], [84, 29, 85, 27], [84, 33, 85, 31], [84, 34, 85, 32, "onPointerUp"], [84, 45, 85, 43], [84, 46, 85, 44, "bind"], [84, 50, 85, 48], [84, 51, 85, 49], [84, 55, 85, 53], [84, 56, 85, 54], [84, 57, 85, 55], [85, 6, 86, 4, "manager"], [85, 13, 86, 11], [85, 14, 86, 12, "setOnPointerRemove"], [85, 32, 86, 30], [85, 33, 86, 31], [85, 37, 86, 35], [85, 38, 86, 36, "onPointerRemove"], [85, 53, 86, 51], [85, 54, 86, 52, "bind"], [85, 58, 86, 56], [85, 59, 86, 57], [85, 63, 86, 61], [85, 64, 86, 62], [85, 65, 86, 63], [86, 6, 87, 4, "manager"], [86, 13, 87, 11], [86, 14, 87, 12, "setOnPointerMove"], [86, 30, 87, 28], [86, 31, 87, 29], [86, 35, 87, 33], [86, 36, 87, 34, "onPointerMove"], [86, 49, 87, 47], [86, 50, 87, 48, "bind"], [86, 54, 87, 52], [86, 55, 87, 53], [86, 59, 87, 57], [86, 60, 87, 58], [86, 61, 87, 59], [87, 6, 88, 4, "manager"], [87, 13, 88, 11], [87, 14, 88, 12, "setOnPointerEnter"], [87, 31, 88, 29], [87, 32, 88, 30], [87, 36, 88, 34], [87, 37, 88, 35, "onPointerEnter"], [87, 51, 88, 49], [87, 52, 88, 50, "bind"], [87, 56, 88, 54], [87, 57, 88, 55], [87, 61, 88, 59], [87, 62, 88, 60], [87, 63, 88, 61], [88, 6, 89, 4, "manager"], [88, 13, 89, 11], [88, 14, 89, 12, "setOnPointerLeave"], [88, 31, 89, 29], [88, 32, 89, 30], [88, 36, 89, 34], [88, 37, 89, 35, "onPointerLeave"], [88, 51, 89, 49], [88, 52, 89, 50, "bind"], [88, 56, 89, 54], [88, 57, 89, 55], [88, 61, 89, 59], [88, 62, 89, 60], [88, 63, 89, 61], [89, 6, 90, 4, "manager"], [89, 13, 90, 11], [89, 14, 90, 12, "setOnPointerCancel"], [89, 32, 90, 30], [89, 33, 90, 31], [89, 37, 90, 35], [89, 38, 90, 36, "onPointerCancel"], [89, 53, 90, 51], [89, 54, 90, 52, "bind"], [89, 58, 90, 56], [89, 59, 90, 57], [89, 63, 90, 61], [89, 64, 90, 62], [89, 65, 90, 63], [90, 6, 91, 4, "manager"], [90, 13, 91, 11], [90, 14, 91, 12, "setOnPointerOutOfBounds"], [90, 37, 91, 35], [90, 38, 91, 36], [90, 42, 91, 40], [90, 43, 91, 41, "onPointerOutOfBounds"], [90, 63, 91, 61], [90, 64, 91, 62, "bind"], [90, 68, 91, 66], [90, 69, 91, 67], [90, 73, 91, 71], [90, 74, 91, 72], [90, 75, 91, 73], [91, 6, 92, 4, "manager"], [91, 13, 92, 11], [91, 14, 92, 12, "setOnPointerMoveOver"], [91, 34, 92, 32], [91, 35, 92, 33], [91, 39, 92, 37], [91, 40, 92, 38, "onPointerMoveOver"], [91, 57, 92, 55], [91, 58, 92, 56, "bind"], [91, 62, 92, 60], [91, 63, 92, 61], [91, 67, 92, 65], [91, 68, 92, 66], [91, 69, 92, 67], [92, 6, 93, 4, "manager"], [92, 13, 93, 11], [92, 14, 93, 12, "setOnPointerMoveOut"], [92, 33, 93, 31], [92, 34, 93, 32], [92, 38, 93, 36], [92, 39, 93, 37, "onPointerMoveOut"], [92, 55, 93, 53], [92, 56, 93, 54, "bind"], [92, 60, 93, 58], [92, 61, 93, 59], [92, 65, 93, 63], [92, 66, 93, 64], [92, 67, 93, 65], [93, 6, 94, 4, "manager"], [93, 13, 94, 11], [93, 14, 94, 12, "setOnWheel"], [93, 24, 94, 22], [93, 25, 94, 23], [93, 29, 94, 27], [93, 30, 94, 28, "onWheel"], [93, 37, 94, 35], [93, 38, 94, 36, "bind"], [93, 42, 94, 40], [93, 43, 94, 41], [93, 47, 94, 45], [93, 48, 94, 46], [93, 49, 94, 47], [94, 6, 95, 4, "manager"], [94, 13, 95, 11], [94, 14, 95, 12, "registerListeners"], [94, 31, 95, 29], [94, 32, 95, 30], [94, 33, 95, 31], [95, 4, 96, 2], [95, 5, 96, 3], [95, 6, 96, 4], [96, 4, 97, 2], [97, 4, 98, 2], [99, 4, 101, 2, "onCancel"], [99, 12, 101, 10, "onCancel"], [99, 13, 101, 10], [99, 15, 101, 13], [99, 16, 101, 14], [100, 4, 103, 2, "onReset"], [100, 11, 103, 9, "onReset"], [100, 12, 103, 9], [100, 14, 103, 12], [100, 15, 103, 13], [101, 4, 105, 2, "resetProgress"], [101, 17, 105, 15, "resetProgress"], [101, 18, 105, 15], [101, 20, 105, 18], [101, 21, 105, 19], [102, 4, 107, 2, "reset"], [102, 9, 107, 7, "reset"], [102, 10, 107, 7], [102, 12, 107, 10], [103, 6, 108, 4], [103, 10, 108, 8], [103, 11, 108, 9, "tracker"], [103, 18, 108, 16], [103, 19, 108, 17, "resetTracker"], [103, 31, 108, 29], [103, 32, 108, 30], [103, 33, 108, 31], [104, 6, 109, 4], [104, 10, 109, 8], [104, 11, 109, 9, "onReset"], [104, 18, 109, 16], [104, 19, 109, 17], [104, 20, 109, 18], [105, 6, 110, 4], [105, 10, 110, 8], [105, 11, 110, 9, "resetProgress"], [105, 24, 110, 22], [105, 25, 110, 23], [105, 26, 110, 24], [106, 6, 111, 4], [106, 10, 111, 8], [106, 11, 111, 9, "delegate"], [106, 19, 111, 17], [106, 20, 111, 18, "reset"], [106, 25, 111, 23], [106, 26, 111, 24], [106, 27, 111, 25], [107, 6, 112, 4], [107, 10, 112, 8], [107, 11, 112, 9, "state"], [107, 16, 112, 14], [107, 19, 112, 17, "State"], [107, 31, 112, 22], [107, 32, 112, 23, "UNDETERMINED"], [107, 44, 112, 35], [108, 4, 113, 2], [108, 5, 113, 3], [108, 6, 113, 4], [109, 4, 114, 2], [110, 4, 115, 2], [112, 4, 118, 2, "moveToState"], [112, 15, 118, 13, "moveToState"], [112, 16, 118, 14, "newState"], [112, 24, 118, 22], [112, 26, 118, 24, "sendIfDisabled"], [112, 40, 118, 38], [112, 42, 118, 40], [113, 6, 119, 4], [113, 10, 119, 8], [113, 14, 119, 12], [113, 15, 119, 13, "state"], [113, 20, 119, 18], [113, 25, 119, 23, "newState"], [113, 33, 119, 31], [113, 35, 119, 33], [114, 8, 120, 6], [115, 6, 121, 4], [116, 6, 123, 4], [116, 12, 123, 10, "oldState"], [116, 20, 123, 18], [116, 23, 123, 21], [116, 27, 123, 25], [116, 28, 123, 26, "state"], [116, 33, 123, 31], [117, 6, 124, 4], [117, 10, 124, 8], [117, 11, 124, 9, "state"], [117, 16, 124, 14], [117, 19, 124, 17, "newState"], [117, 27, 124, 25], [118, 6, 126, 4], [118, 10, 126, 8], [118, 14, 126, 12], [118, 15, 126, 13, "tracker"], [118, 22, 126, 20], [118, 23, 126, 21, "trackedPointersCount"], [118, 43, 126, 41], [118, 46, 126, 44], [118, 47, 126, 45], [118, 51, 126, 49], [118, 55, 126, 53], [118, 56, 126, 54, "config"], [118, 62, 126, 60], [118, 63, 126, 61, "needsPointerData"], [118, 79, 126, 77], [118, 83, 126, 81], [118, 87, 126, 85], [118, 88, 126, 86, "isFinished"], [118, 98, 126, 96], [118, 99, 126, 97], [118, 100, 126, 98], [118, 102, 126, 100], [119, 8, 127, 6], [119, 12, 127, 10], [119, 13, 127, 11, "cancelTouches"], [119, 26, 127, 24], [119, 27, 127, 25], [119, 28, 127, 26], [120, 6, 128, 4], [121, 6, 130, 4, "GestureHandlerOrchestrator"], [121, 41, 130, 30], [121, 42, 130, 31, "instance"], [121, 50, 130, 39], [121, 51, 130, 40, "onHandlerStateChange"], [121, 71, 130, 60], [121, 72, 130, 61], [121, 76, 130, 65], [121, 78, 130, 67, "newState"], [121, 86, 130, 75], [121, 88, 130, 77, "oldState"], [121, 96, 130, 85], [121, 98, 130, 87, "sendIfDisabled"], [121, 112, 130, 101], [121, 113, 130, 102], [122, 6, 131, 4], [122, 10, 131, 8], [122, 11, 131, 9, "onStateChange"], [122, 24, 131, 22], [122, 25, 131, 23, "newState"], [122, 33, 131, 31], [122, 35, 131, 33, "oldState"], [122, 43, 131, 41], [122, 44, 131, 42], [123, 6, 133, 4], [123, 10, 133, 8], [123, 11, 133, 9], [123, 15, 133, 13], [123, 16, 133, 14, "enabled"], [123, 23, 133, 21], [123, 27, 133, 25], [123, 31, 133, 29], [123, 32, 133, 30, "isFinished"], [123, 42, 133, 40], [123, 43, 133, 41], [123, 44, 133, 42], [123, 46, 133, 44], [124, 8, 134, 6], [124, 12, 134, 10], [124, 13, 134, 11, "state"], [124, 18, 134, 16], [124, 21, 134, 19, "State"], [124, 33, 134, 24], [124, 34, 134, 25, "UNDETERMINED"], [124, 46, 134, 37], [125, 6, 135, 4], [126, 4, 136, 2], [127, 4, 138, 2, "onStateChange"], [127, 17, 138, 15, "onStateChange"], [127, 18, 138, 16, "_newState"], [127, 27, 138, 25], [127, 29, 138, 27, "_oldState"], [127, 38, 138, 36], [127, 40, 138, 38], [127, 41, 138, 39], [128, 4, 140, 2, "begin"], [128, 9, 140, 7, "begin"], [128, 10, 140, 7], [128, 12, 140, 10], [129, 6, 141, 4], [129, 10, 141, 8], [129, 11, 141, 9], [129, 15, 141, 13], [129, 16, 141, 14, "checkHitSlop"], [129, 28, 141, 26], [129, 29, 141, 27], [129, 30, 141, 28], [129, 32, 141, 30], [130, 8, 142, 6], [131, 6, 143, 4], [132, 6, 145, 4], [132, 10, 145, 8], [132, 14, 145, 12], [132, 15, 145, 13, "state"], [132, 20, 145, 18], [132, 25, 145, 23, "State"], [132, 37, 145, 28], [132, 38, 145, 29, "UNDETERMINED"], [132, 50, 145, 41], [132, 52, 145, 43], [133, 8, 146, 6], [133, 12, 146, 10], [133, 13, 146, 11, "moveToState"], [133, 24, 146, 22], [133, 25, 146, 23, "State"], [133, 37, 146, 28], [133, 38, 146, 29, "BEGAN"], [133, 43, 146, 34], [133, 44, 146, 35], [134, 6, 147, 4], [135, 4, 148, 2], [136, 4, 149, 2], [137, 0, 150, 0], [138, 0, 151, 0], [140, 4, 154, 2, "fail"], [140, 8, 154, 6, "fail"], [140, 9, 154, 7, "sendIfDisabled"], [140, 23, 154, 21], [140, 25, 154, 23], [141, 6, 155, 4], [141, 10, 155, 8], [141, 14, 155, 12], [141, 15, 155, 13, "state"], [141, 20, 155, 18], [141, 25, 155, 23, "State"], [141, 37, 155, 28], [141, 38, 155, 29, "ACTIVE"], [141, 44, 155, 35], [141, 48, 155, 39], [141, 52, 155, 43], [141, 53, 155, 44, "state"], [141, 58, 155, 49], [141, 63, 155, 54, "State"], [141, 75, 155, 59], [141, 76, 155, 60, "BEGAN"], [141, 81, 155, 65], [141, 83, 155, 67], [142, 8, 156, 6], [143, 8, 157, 6], [144, 8, 158, 6], [144, 12, 158, 10], [144, 13, 158, 11, "delegate"], [144, 21, 158, 19], [144, 22, 158, 20, "onFail"], [144, 28, 158, 26], [144, 29, 158, 27], [144, 30, 158, 28], [145, 8, 159, 6], [145, 12, 159, 10], [145, 13, 159, 11, "moveToState"], [145, 24, 159, 22], [145, 25, 159, 23, "State"], [145, 37, 159, 28], [145, 38, 159, 29, "FAILED"], [145, 44, 159, 35], [145, 46, 159, 37, "sendIfDisabled"], [145, 60, 159, 51], [145, 61, 159, 52], [146, 6, 160, 4], [147, 6, 162, 4], [147, 10, 162, 8], [147, 11, 162, 9, "resetProgress"], [147, 24, 162, 22], [147, 25, 162, 23], [147, 26, 162, 24], [148, 4, 163, 2], [149, 4, 164, 2], [150, 0, 165, 0], [151, 0, 166, 0], [153, 4, 169, 2, "cancel"], [153, 10, 169, 8, "cancel"], [153, 11, 169, 9, "sendIfDisabled"], [153, 25, 169, 23], [153, 27, 169, 25], [154, 6, 170, 4], [154, 10, 170, 8], [154, 14, 170, 12], [154, 15, 170, 13, "state"], [154, 20, 170, 18], [154, 25, 170, 23, "State"], [154, 37, 170, 28], [154, 38, 170, 29, "ACTIVE"], [154, 44, 170, 35], [154, 48, 170, 39], [154, 52, 170, 43], [154, 53, 170, 44, "state"], [154, 58, 170, 49], [154, 63, 170, 54, "State"], [154, 75, 170, 59], [154, 76, 170, 60, "UNDETERMINED"], [154, 88, 170, 72], [154, 92, 170, 76], [154, 96, 170, 80], [154, 97, 170, 81, "state"], [154, 102, 170, 86], [154, 107, 170, 91, "State"], [154, 119, 170, 96], [154, 120, 170, 97, "BEGAN"], [154, 125, 170, 102], [154, 127, 170, 104], [155, 8, 171, 6], [155, 12, 171, 10], [155, 13, 171, 11, "onCancel"], [155, 21, 171, 19], [155, 22, 171, 20], [155, 23, 171, 21], [155, 24, 171, 22], [155, 25, 171, 23], [157, 8, 173, 6], [157, 12, 173, 10], [157, 13, 173, 11, "delegate"], [157, 21, 173, 19], [157, 22, 173, 20, "onCancel"], [157, 30, 173, 28], [157, 31, 173, 29], [157, 32, 173, 30], [158, 8, 174, 6], [158, 12, 174, 10], [158, 13, 174, 11, "moveToState"], [158, 24, 174, 22], [158, 25, 174, 23, "State"], [158, 37, 174, 28], [158, 38, 174, 29, "CANCELLED"], [158, 47, 174, 38], [158, 49, 174, 40, "sendIfDisabled"], [158, 63, 174, 54], [158, 64, 174, 55], [159, 6, 175, 4], [160, 4, 176, 2], [161, 4, 178, 2, "activate"], [161, 12, 178, 10, "activate"], [161, 13, 178, 11, "force"], [161, 18, 178, 16], [161, 21, 178, 19], [161, 26, 178, 24], [161, 28, 178, 26], [162, 6, 179, 4], [162, 10, 179, 8], [162, 11, 179, 9], [162, 15, 179, 13], [162, 16, 179, 14, "config"], [162, 22, 179, 20], [162, 23, 179, 21, "manualActivation"], [162, 39, 179, 37], [162, 44, 179, 42], [162, 48, 179, 46], [162, 52, 179, 50, "force"], [162, 57, 179, 55], [162, 63, 179, 61], [162, 67, 179, 65], [162, 68, 179, 66, "state"], [162, 73, 179, 71], [162, 78, 179, 76, "State"], [162, 90, 179, 81], [162, 91, 179, 82, "UNDETERMINED"], [162, 103, 179, 94], [162, 107, 179, 98], [162, 111, 179, 102], [162, 112, 179, 103, "state"], [162, 117, 179, 108], [162, 122, 179, 113, "State"], [162, 134, 179, 118], [162, 135, 179, 119, "BEGAN"], [162, 140, 179, 124], [162, 141, 179, 125], [162, 143, 179, 127], [163, 8, 180, 6], [163, 12, 180, 10], [163, 13, 180, 11, "delegate"], [163, 21, 180, 19], [163, 22, 180, 20, "onActivate"], [163, 32, 180, 30], [163, 33, 180, 31], [163, 34, 180, 32], [164, 8, 181, 6], [164, 12, 181, 10], [164, 13, 181, 11, "moveToState"], [164, 24, 181, 22], [164, 25, 181, 23, "State"], [164, 37, 181, 28], [164, 38, 181, 29, "ACTIVE"], [164, 44, 181, 35], [164, 45, 181, 36], [165, 6, 182, 4], [166, 4, 183, 2], [167, 4, 185, 2, "end"], [167, 7, 185, 5, "end"], [167, 8, 185, 5], [167, 10, 185, 8], [168, 6, 186, 4], [168, 10, 186, 8], [168, 14, 186, 12], [168, 15, 186, 13, "state"], [168, 20, 186, 18], [168, 25, 186, 23, "State"], [168, 37, 186, 28], [168, 38, 186, 29, "BEGAN"], [168, 43, 186, 34], [168, 47, 186, 38], [168, 51, 186, 42], [168, 52, 186, 43, "state"], [168, 57, 186, 48], [168, 62, 186, 53, "State"], [168, 74, 186, 58], [168, 75, 186, 59, "ACTIVE"], [168, 81, 186, 65], [168, 83, 186, 67], [169, 8, 187, 6], [170, 8, 188, 6], [170, 12, 188, 10], [170, 13, 188, 11, "delegate"], [170, 21, 188, 19], [170, 22, 188, 20, "onEnd"], [170, 27, 188, 25], [170, 28, 188, 26], [170, 29, 188, 27], [171, 8, 189, 6], [171, 12, 189, 10], [171, 13, 189, 11, "moveToState"], [171, 24, 189, 22], [171, 25, 189, 23, "State"], [171, 37, 189, 28], [171, 38, 189, 29, "END"], [171, 41, 189, 32], [171, 42, 189, 33], [172, 6, 190, 4], [173, 6, 192, 4], [173, 10, 192, 8], [173, 11, 192, 9, "resetProgress"], [173, 24, 192, 22], [173, 25, 192, 23], [173, 26, 192, 24], [174, 4, 193, 2], [174, 5, 193, 3], [174, 6, 193, 4], [175, 4, 194, 2], [176, 4, 195, 2], [178, 4, 198, 2, "getShouldResetProgress"], [178, 26, 198, 24, "getShouldResetProgress"], [178, 27, 198, 24], [178, 29, 198, 27], [179, 6, 199, 4], [179, 13, 199, 11], [179, 17, 199, 15], [179, 18, 199, 16, "shouldResetProgress"], [179, 37, 199, 35], [180, 4, 200, 2], [181, 4, 202, 2, "setShouldResetProgress"], [181, 26, 202, 24, "setShouldResetProgress"], [181, 27, 202, 25, "value"], [181, 32, 202, 30], [181, 34, 202, 32], [182, 6, 203, 4], [182, 10, 203, 8], [182, 11, 203, 9, "shouldResetProgress"], [182, 30, 203, 28], [182, 33, 203, 31, "value"], [182, 38, 203, 36], [183, 4, 204, 2], [184, 4, 206, 2, "shouldWaitForHandlerFailure"], [184, 31, 206, 29, "shouldWaitForHandlerFailure"], [184, 32, 206, 30, "handler"], [184, 39, 206, 37], [184, 41, 206, 39], [185, 6, 207, 4], [185, 10, 207, 8, "handler"], [185, 17, 207, 15], [185, 22, 207, 20], [185, 26, 207, 24], [185, 28, 207, 26], [186, 8, 208, 6], [186, 15, 208, 13], [186, 20, 208, 18], [187, 6, 209, 4], [188, 6, 211, 4], [188, 13, 211, 11, "InteractionManager"], [188, 40, 211, 29], [188, 41, 211, 30, "instance"], [188, 49, 211, 38], [188, 50, 211, 39, "shouldWaitForHandlerFailure"], [188, 77, 211, 66], [188, 78, 211, 67], [188, 82, 211, 71], [188, 84, 211, 73, "handler"], [188, 91, 211, 80], [188, 92, 211, 81], [189, 4, 212, 2], [190, 4, 214, 2, "shouldRequireToWaitForFailure"], [190, 33, 214, 31, "shouldRequireToWaitForFailure"], [190, 34, 214, 32, "handler"], [190, 41, 214, 39], [190, 43, 214, 41], [191, 6, 215, 4], [191, 10, 215, 8, "handler"], [191, 17, 215, 15], [191, 22, 215, 20], [191, 26, 215, 24], [191, 28, 215, 26], [192, 8, 216, 6], [192, 15, 216, 13], [192, 20, 216, 18], [193, 6, 217, 4], [194, 6, 219, 4], [194, 13, 219, 11, "InteractionManager"], [194, 40, 219, 29], [194, 41, 219, 30, "instance"], [194, 49, 219, 38], [194, 50, 219, 39, "shouldRequireHandlerToWaitForFailure"], [194, 86, 219, 75], [194, 87, 219, 76], [194, 91, 219, 80], [194, 93, 219, 82, "handler"], [194, 100, 219, 89], [194, 101, 219, 90], [195, 4, 220, 2], [196, 4, 222, 2, "shouldRecognizeSimultaneously"], [196, 33, 222, 31, "shouldRecognizeSimultaneously"], [196, 34, 222, 32, "handler"], [196, 41, 222, 39], [196, 43, 222, 41], [197, 6, 223, 4], [197, 10, 223, 8, "handler"], [197, 17, 223, 15], [197, 22, 223, 20], [197, 26, 223, 24], [197, 28, 223, 26], [198, 8, 224, 6], [198, 15, 224, 13], [198, 19, 224, 17], [199, 6, 225, 4], [200, 6, 227, 4], [200, 13, 227, 11, "InteractionManager"], [200, 40, 227, 29], [200, 41, 227, 30, "instance"], [200, 49, 227, 38], [200, 50, 227, 39, "shouldRecognizeSimultaneously"], [200, 79, 227, 68], [200, 80, 227, 69], [200, 84, 227, 73], [200, 86, 227, 75, "handler"], [200, 93, 227, 82], [200, 94, 227, 83], [201, 4, 228, 2], [202, 4, 230, 2, "shouldBeCancelledByOther"], [202, 28, 230, 26, "shouldBeCancelledByOther"], [202, 29, 230, 27, "handler"], [202, 36, 230, 34], [202, 38, 230, 36], [203, 6, 231, 4], [203, 10, 231, 8, "handler"], [203, 17, 231, 15], [203, 22, 231, 20], [203, 26, 231, 24], [203, 28, 231, 26], [204, 8, 232, 6], [204, 15, 232, 13], [204, 20, 232, 18], [205, 6, 233, 4], [206, 6, 235, 4], [206, 13, 235, 11, "InteractionManager"], [206, 40, 235, 29], [206, 41, 235, 30, "instance"], [206, 49, 235, 38], [206, 50, 235, 39, "shouldHandlerBeCancelledBy"], [206, 76, 235, 65], [206, 77, 235, 66], [206, 81, 235, 70], [206, 83, 235, 72, "handler"], [206, 90, 235, 79], [206, 91, 235, 80], [207, 4, 236, 2], [207, 5, 236, 3], [207, 6, 236, 4], [208, 4, 237, 2], [209, 4, 238, 2], [211, 4, 241, 2, "onPointerDown"], [211, 17, 241, 15, "onPointerDown"], [211, 18, 241, 16, "event"], [211, 23, 241, 21], [211, 25, 241, 23], [212, 6, 242, 4, "GestureHandlerOrchestrator"], [212, 41, 242, 30], [212, 42, 242, 31, "instance"], [212, 50, 242, 39], [212, 51, 242, 40, "recordHandlerIfNotPresent"], [212, 76, 242, 65], [212, 77, 242, 66], [212, 81, 242, 70], [212, 82, 242, 71], [213, 6, 243, 4], [213, 10, 243, 8], [213, 11, 243, 9, "pointerType"], [213, 22, 243, 20], [213, 25, 243, 23, "event"], [213, 30, 243, 28], [213, 31, 243, 29, "pointerType"], [213, 42, 243, 40], [214, 6, 245, 4], [214, 10, 245, 8], [214, 14, 245, 12], [214, 15, 245, 13, "pointerType"], [214, 26, 245, 24], [214, 31, 245, 29, "PointerType"], [214, 55, 245, 40], [214, 56, 245, 41, "TOUCH"], [214, 61, 245, 46], [214, 63, 245, 48], [215, 8, 246, 6, "GestureHandlerOrchestrator"], [215, 43, 246, 32], [215, 44, 246, 33, "instance"], [215, 52, 246, 41], [215, 53, 246, 42, "cancelMouseAndPenGestures"], [215, 78, 246, 67], [215, 79, 246, 68], [215, 83, 246, 72], [215, 84, 246, 73], [216, 6, 247, 4], [216, 7, 247, 5], [216, 8, 247, 6], [217, 4, 249, 2], [217, 5, 249, 3], [217, 6, 249, 4], [219, 4, 252, 2, "onPointerAdd"], [219, 16, 252, 14, "onPointerAdd"], [219, 17, 252, 15, "event"], [219, 22, 252, 20], [219, 24, 252, 22], [220, 6, 253, 4], [220, 10, 253, 8], [220, 11, 253, 9, "tryToSendTouchEvent"], [220, 30, 253, 28], [220, 31, 253, 29, "event"], [220, 36, 253, 34], [220, 37, 253, 35], [221, 4, 254, 2], [222, 4, 256, 2, "onPointerUp"], [222, 15, 256, 13, "onPointerUp"], [222, 16, 256, 14, "event"], [222, 21, 256, 19], [222, 23, 256, 21], [223, 6, 257, 4], [223, 10, 257, 8], [223, 11, 257, 9, "tryToSendTouchEvent"], [223, 30, 257, 28], [223, 31, 257, 29, "event"], [223, 36, 257, 34], [223, 37, 257, 35], [224, 4, 258, 2], [224, 5, 258, 3], [224, 6, 258, 4], [226, 4, 261, 2, "onPointerRemove"], [226, 19, 261, 17, "onPointerRemove"], [226, 20, 261, 18, "event"], [226, 25, 261, 23], [226, 27, 261, 25], [227, 6, 262, 4], [227, 10, 262, 8], [227, 11, 262, 9, "tryToSendTouchEvent"], [227, 30, 262, 28], [227, 31, 262, 29, "event"], [227, 36, 262, 34], [227, 37, 262, 35], [228, 4, 263, 2], [229, 4, 265, 2, "onPointerMove"], [229, 17, 265, 15, "onPointerMove"], [229, 18, 265, 16, "event"], [229, 23, 265, 21], [229, 25, 265, 23], [230, 6, 266, 4], [230, 10, 266, 8], [230, 11, 266, 9, "tryToSendMoveEvent"], [230, 29, 266, 27], [230, 30, 266, 28], [230, 35, 266, 33], [230, 37, 266, 35, "event"], [230, 42, 266, 40], [230, 43, 266, 41], [231, 4, 267, 2], [232, 4, 269, 2, "onPointerLeave"], [232, 18, 269, 16, "onPointerLeave"], [232, 19, 269, 17, "event"], [232, 24, 269, 22], [232, 26, 269, 24], [233, 6, 270, 4], [233, 10, 270, 8], [233, 14, 270, 12], [233, 15, 270, 13, "shouldCancelWhenOutside"], [233, 38, 270, 36], [233, 40, 270, 38], [234, 8, 271, 6], [234, 16, 271, 14], [234, 20, 271, 18], [234, 21, 271, 19, "state"], [234, 26, 271, 24], [235, 10, 272, 8], [235, 15, 272, 13, "State"], [235, 27, 272, 18], [235, 28, 272, 19, "ACTIVE"], [235, 34, 272, 25], [236, 12, 273, 10], [236, 16, 273, 14], [236, 17, 273, 15, "cancel"], [236, 23, 273, 21], [236, 24, 273, 22], [236, 25, 273, 23], [237, 12, 274, 10], [238, 10, 276, 8], [238, 15, 276, 13, "State"], [238, 27, 276, 18], [238, 28, 276, 19, "BEGAN"], [238, 33, 276, 24], [239, 12, 277, 10], [239, 16, 277, 14], [239, 17, 277, 15, "fail"], [239, 21, 277, 19], [239, 22, 277, 20], [239, 23, 277, 21], [240, 12, 278, 10], [241, 8, 279, 6], [242, 8, 281, 6], [243, 6, 282, 4], [244, 6, 284, 4], [244, 10, 284, 8], [244, 11, 284, 9, "tryToSendTouchEvent"], [244, 30, 284, 28], [244, 31, 284, 29, "event"], [244, 36, 284, 34], [244, 37, 284, 35], [245, 4, 285, 2], [246, 4, 287, 2, "onPointerEnter"], [246, 18, 287, 16, "onPointerEnter"], [246, 19, 287, 17, "event"], [246, 24, 287, 22], [246, 26, 287, 24], [247, 6, 288, 4], [247, 10, 288, 8], [247, 11, 288, 9, "tryToSendTouchEvent"], [247, 30, 288, 28], [247, 31, 288, 29, "event"], [247, 36, 288, 34], [247, 37, 288, 35], [248, 4, 289, 2], [249, 4, 291, 2, "onPointerCancel"], [249, 19, 291, 17, "onPointerCancel"], [249, 20, 291, 18, "event"], [249, 25, 291, 23], [249, 27, 291, 25], [250, 6, 292, 4], [250, 10, 292, 8], [250, 11, 292, 9, "tryToSendTouchEvent"], [250, 30, 292, 28], [250, 31, 292, 29, "event"], [250, 36, 292, 34], [250, 37, 292, 35], [251, 6, 293, 4], [251, 10, 293, 8], [251, 11, 293, 9, "cancel"], [251, 17, 293, 15], [251, 18, 293, 16], [251, 19, 293, 17], [252, 6, 294, 4], [252, 10, 294, 8], [252, 11, 294, 9, "reset"], [252, 16, 294, 14], [252, 17, 294, 15], [252, 18, 294, 16], [253, 4, 295, 2], [254, 4, 297, 2, "onPointerOutOfBounds"], [254, 24, 297, 22, "onPointerOutOfBounds"], [254, 25, 297, 23, "event"], [254, 30, 297, 28], [254, 32, 297, 30], [255, 6, 298, 4], [255, 10, 298, 8], [255, 11, 298, 9, "tryToSendMoveEvent"], [255, 29, 298, 27], [255, 30, 298, 28], [255, 34, 298, 32], [255, 36, 298, 34, "event"], [255, 41, 298, 39], [255, 42, 298, 40], [256, 4, 299, 2], [257, 4, 301, 2, "onPointerMoveOver"], [257, 21, 301, 19, "onPointerMoveOver"], [257, 22, 301, 20, "_event"], [257, 28, 301, 26], [257, 30, 301, 28], [257, 31, 301, 29], [258, 4, 301, 29], [259, 4, 304, 2, "onPointerMoveOut"], [259, 20, 304, 18, "onPointerMoveOut"], [259, 21, 304, 19, "_event"], [259, 27, 304, 25], [259, 29, 304, 27], [259, 30, 304, 28], [260, 4, 304, 28], [261, 4, 307, 2, "onWheel"], [261, 11, 307, 9, "onWheel"], [261, 12, 307, 10, "_event"], [261, 18, 307, 16], [261, 20, 307, 18], [261, 21, 307, 19], [262, 4, 307, 19], [263, 4, 310, 2, "tryToSendMoveEvent"], [263, 22, 310, 20, "tryToSendMoveEvent"], [263, 23, 310, 21, "out"], [263, 26, 310, 24], [263, 28, 310, 26, "event"], [263, 33, 310, 31], [263, 35, 310, 33], [264, 6, 311, 4], [264, 10, 311, 8, "out"], [264, 13, 311, 11], [264, 17, 311, 15], [264, 21, 311, 19], [264, 22, 311, 20, "shouldCancelWhenOutside"], [264, 45, 311, 43], [264, 49, 311, 47], [264, 50, 311, 48], [264, 54, 311, 52], [264, 55, 311, 53, "enabled"], [264, 62, 311, 60], [264, 64, 311, 62], [265, 8, 312, 6], [266, 6, 313, 4], [267, 6, 315, 4], [267, 10, 315, 8], [267, 14, 315, 12], [267, 15, 315, 13, "active"], [267, 21, 315, 19], [267, 23, 315, 21], [268, 8, 316, 6], [268, 12, 316, 10], [268, 13, 316, 11, "sendEvent"], [268, 22, 316, 20], [268, 23, 316, 21], [268, 27, 316, 25], [268, 28, 316, 26, "state"], [268, 33, 316, 31], [268, 35, 316, 33], [268, 39, 316, 37], [268, 40, 316, 38, "state"], [268, 45, 316, 43], [268, 46, 316, 44], [269, 6, 317, 4], [270, 6, 319, 4], [270, 10, 319, 8], [270, 11, 319, 9, "tryToSendTouchEvent"], [270, 30, 319, 28], [270, 31, 319, 29, "event"], [270, 36, 319, 34], [270, 37, 319, 35], [271, 4, 320, 2], [272, 4, 322, 2, "tryToSendTouchEvent"], [272, 23, 322, 21, "tryToSendTouchEvent"], [272, 24, 322, 22, "event"], [272, 29, 322, 27], [272, 31, 322, 29], [273, 6, 323, 4], [273, 10, 323, 8], [273, 14, 323, 12], [273, 15, 323, 13, "config"], [273, 21, 323, 19], [273, 22, 323, 20, "needsPointerData"], [273, 38, 323, 36], [273, 40, 323, 38], [274, 8, 324, 6], [274, 12, 324, 10], [274, 13, 324, 11, "sendTouchEvent"], [274, 27, 324, 25], [274, 28, 324, 26, "event"], [274, 33, 324, 31], [274, 34, 324, 32], [275, 6, 325, 4], [276, 4, 326, 2], [277, 4, 328, 2, "sendTouchEvent"], [277, 18, 328, 16, "sendTouchEvent"], [277, 19, 328, 17, "event"], [277, 24, 328, 22], [277, 26, 328, 24], [278, 6, 329, 4], [278, 10, 329, 8], [278, 11, 329, 9], [278, 15, 329, 13], [278, 16, 329, 14, "enabled"], [278, 23, 329, 21], [278, 25, 329, 23], [279, 8, 330, 6], [280, 6, 331, 4], [281, 6, 333, 4], [281, 12, 333, 10], [282, 8, 334, 6, "onGestureHandlerEvent"], [283, 6, 335, 4], [283, 7, 335, 5], [283, 10, 335, 8], [283, 14, 335, 12], [283, 15, 335, 13, "propsRef"], [283, 23, 335, 21], [283, 24, 335, 22, "current"], [283, 31, 335, 29], [284, 6, 336, 4], [284, 12, 336, 10, "touchEvent"], [284, 22, 336, 20], [284, 25, 336, 23], [284, 29, 336, 27], [284, 30, 336, 28, "transformTouchEvent"], [284, 49, 336, 47], [284, 50, 336, 48, "event"], [284, 55, 336, 53], [284, 56, 336, 54], [285, 6, 338, 4], [285, 10, 338, 8, "touchEvent"], [285, 20, 338, 18], [285, 22, 338, 20], [286, 8, 339, 6, "invokeNullableMethod"], [286, 28, 339, 26], [286, 29, 339, 27, "onGestureHandlerEvent"], [286, 50, 339, 48], [286, 52, 339, 50, "touchEvent"], [286, 62, 339, 60], [286, 63, 339, 61], [287, 6, 340, 4], [288, 4, 341, 2], [288, 5, 341, 3], [288, 6, 341, 4], [289, 4, 342, 2], [290, 4, 343, 2], [292, 4, 346, 2, "transformEventData"], [292, 22, 346, 20, "transformEventData"], [292, 23, 346, 21, "newState"], [292, 31, 346, 29], [292, 33, 346, 31, "oldState"], [292, 41, 346, 39], [292, 43, 346, 41], [293, 6, 347, 4], [293, 13, 347, 11], [294, 8, 348, 6, "nativeEvent"], [294, 19, 348, 17], [294, 21, 348, 19], [295, 10, 349, 8, "numberOfPointers"], [295, 26, 349, 24], [295, 28, 349, 26], [295, 32, 349, 30], [295, 33, 349, 31, "tracker"], [295, 40, 349, 38], [295, 41, 349, 39, "trackedPointersCount"], [295, 61, 349, 59], [296, 10, 350, 8, "state"], [296, 15, 350, 13], [296, 17, 350, 15, "newState"], [296, 25, 350, 23], [297, 10, 351, 8, "pointerInside"], [297, 23, 351, 21], [297, 25, 351, 23], [297, 29, 351, 27], [297, 30, 351, 28, "delegate"], [297, 38, 351, 36], [297, 39, 351, 37, "isPointerInBounds"], [297, 56, 351, 54], [297, 57, 351, 55], [297, 61, 351, 59], [297, 62, 351, 60, "tracker"], [297, 69, 351, 67], [297, 70, 351, 68, "getAbsoluteCoordsAverage"], [297, 94, 351, 92], [297, 95, 351, 93], [297, 96, 351, 94], [297, 97, 351, 95], [298, 10, 352, 8], [298, 13, 352, 11], [298, 17, 352, 15], [298, 18, 352, 16, "transformNativeEvent"], [298, 38, 352, 36], [298, 39, 352, 37], [298, 40, 352, 38], [299, 10, 353, 8, "handlerTag"], [299, 20, 353, 18], [299, 22, 353, 20], [299, 26, 353, 24], [299, 27, 353, 25, "handlerTag"], [299, 37, 353, 35], [300, 10, 354, 8, "target"], [300, 16, 354, 14], [300, 18, 354, 16], [300, 22, 354, 20], [300, 23, 354, 21, "viewRef"], [300, 30, 354, 28], [301, 10, 355, 8, "oldState"], [301, 18, 355, 16], [301, 20, 355, 18, "newState"], [301, 28, 355, 26], [301, 33, 355, 31, "oldState"], [301, 41, 355, 39], [301, 44, 355, 42, "oldState"], [301, 52, 355, 50], [301, 55, 355, 53, "undefined"], [301, 64, 355, 62], [302, 10, 356, 8, "pointerType"], [302, 21, 356, 19], [302, 23, 356, 21], [302, 27, 356, 25], [302, 28, 356, 26, "pointerType"], [303, 8, 357, 6], [303, 9, 357, 7], [304, 8, 358, 6, "timeStamp"], [304, 17, 358, 15], [304, 19, 358, 17, "Date"], [304, 23, 358, 21], [304, 24, 358, 22, "now"], [304, 27, 358, 25], [304, 28, 358, 26], [305, 6, 359, 4], [305, 7, 359, 5], [306, 4, 360, 2], [307, 4, 362, 2, "transformTouchEvent"], [307, 23, 362, 21, "transformTouchEvent"], [307, 24, 362, 22, "event"], [307, 29, 362, 27], [307, 31, 362, 29], [308, 6, 363, 4], [308, 12, 363, 10, "rect"], [308, 16, 363, 14], [308, 19, 363, 17], [308, 23, 363, 21], [308, 24, 363, 22, "delegate"], [308, 32, 363, 30], [308, 33, 363, 31, "measure<PERSON>iew"], [308, 44, 363, 42], [308, 45, 363, 43], [308, 46, 363, 44], [309, 6, 364, 4], [309, 12, 364, 10, "all"], [309, 15, 364, 13], [309, 18, 364, 16], [309, 20, 364, 18], [310, 6, 365, 4], [310, 12, 365, 10, "changed"], [310, 19, 365, 17], [310, 22, 365, 20], [310, 24, 365, 22], [311, 6, 366, 4], [311, 12, 366, 10, "trackerData"], [311, 23, 366, 21], [311, 26, 366, 24], [311, 30, 366, 28], [311, 31, 366, 29, "tracker"], [311, 38, 366, 36], [311, 39, 366, 37, "trackedPointers"], [311, 54, 366, 52], [311, 55, 366, 53], [311, 56, 366, 54], [312, 6, 367, 4], [313, 6, 368, 4], [314, 6, 369, 4], [316, 6, 371, 4], [316, 10, 371, 8, "trackerData"], [316, 21, 371, 19], [316, 22, 371, 20, "size"], [316, 26, 371, 24], [316, 31, 371, 29], [316, 32, 371, 30], [316, 36, 371, 34], [316, 37, 371, 35, "trackerData"], [316, 48, 371, 46], [316, 49, 371, 47, "has"], [316, 52, 371, 50], [316, 53, 371, 51, "event"], [316, 58, 371, 56], [316, 59, 371, 57, "pointerId"], [316, 68, 371, 66], [316, 69, 371, 67], [316, 71, 371, 69], [317, 8, 372, 6], [318, 6, 373, 4], [319, 6, 375, 4, "trackerData"], [319, 17, 375, 15], [319, 18, 375, 16, "for<PERSON>ach"], [319, 25, 375, 23], [319, 26, 375, 24], [319, 27, 375, 25, "element"], [319, 34, 375, 32], [319, 36, 375, 34, "key"], [319, 39, 375, 37], [319, 44, 375, 42], [320, 8, 376, 6], [320, 14, 376, 12, "id"], [320, 16, 376, 14], [320, 19, 376, 17], [320, 23, 376, 21], [320, 24, 376, 22, "tracker"], [320, 31, 376, 29], [320, 32, 376, 30, "getMappedTouchEventId"], [320, 53, 376, 51], [320, 54, 376, 52, "key"], [320, 57, 376, 55], [320, 58, 376, 56], [321, 8, 377, 6, "all"], [321, 11, 377, 9], [321, 12, 377, 10, "push"], [321, 16, 377, 14], [321, 17, 377, 15], [322, 10, 378, 8, "id"], [322, 12, 378, 10], [322, 14, 378, 12, "id"], [322, 16, 378, 14], [323, 10, 379, 8, "x"], [323, 11, 379, 9], [323, 13, 379, 11, "element"], [323, 20, 379, 18], [323, 21, 379, 19, "abosoluteCoords"], [323, 36, 379, 34], [323, 37, 379, 35, "x"], [323, 38, 379, 36], [323, 41, 379, 39, "rect"], [323, 45, 379, 43], [323, 46, 379, 44, "pageX"], [323, 51, 379, 49], [324, 10, 380, 8, "y"], [324, 11, 380, 9], [324, 13, 380, 11, "element"], [324, 20, 380, 18], [324, 21, 380, 19, "abosoluteCoords"], [324, 36, 380, 34], [324, 37, 380, 35, "y"], [324, 38, 380, 36], [324, 41, 380, 39, "rect"], [324, 45, 380, 43], [324, 46, 380, 44, "pageY"], [324, 51, 380, 49], [325, 10, 381, 8, "absoluteX"], [325, 19, 381, 17], [325, 21, 381, 19, "element"], [325, 28, 381, 26], [325, 29, 381, 27, "abosoluteCoords"], [325, 44, 381, 42], [325, 45, 381, 43, "x"], [325, 46, 381, 44], [326, 10, 382, 8, "absoluteY"], [326, 19, 382, 17], [326, 21, 382, 19, "element"], [326, 28, 382, 26], [326, 29, 382, 27, "abosoluteCoords"], [326, 44, 382, 42], [326, 45, 382, 43, "y"], [327, 8, 383, 6], [327, 9, 383, 7], [327, 10, 383, 8], [328, 6, 384, 4], [328, 7, 384, 5], [328, 8, 384, 6], [328, 9, 384, 7], [328, 10, 384, 8], [329, 6, 385, 4], [331, 6, 387, 4], [331, 10, 387, 8, "event"], [331, 15, 387, 13], [331, 16, 387, 14, "eventType"], [331, 25, 387, 23], [331, 30, 387, 28, "EventTypes"], [331, 52, 387, 38], [331, 53, 387, 39, "CANCEL"], [331, 59, 387, 45], [331, 61, 387, 47], [332, 8, 388, 6, "changed"], [332, 15, 388, 13], [332, 16, 388, 14, "push"], [332, 20, 388, 18], [332, 21, 388, 19], [333, 10, 389, 8, "id"], [333, 12, 389, 10], [333, 14, 389, 12], [333, 18, 389, 16], [333, 19, 389, 17, "tracker"], [333, 26, 389, 24], [333, 27, 389, 25, "getMappedTouchEventId"], [333, 48, 389, 46], [333, 49, 389, 47, "event"], [333, 54, 389, 52], [333, 55, 389, 53, "pointerId"], [333, 64, 389, 62], [333, 65, 389, 63], [334, 10, 390, 8, "x"], [334, 11, 390, 9], [334, 13, 390, 11, "event"], [334, 18, 390, 16], [334, 19, 390, 17, "x"], [334, 20, 390, 18], [334, 23, 390, 21, "rect"], [334, 27, 390, 25], [334, 28, 390, 26, "pageX"], [334, 33, 390, 31], [335, 10, 391, 8, "y"], [335, 11, 391, 9], [335, 13, 391, 11, "event"], [335, 18, 391, 16], [335, 19, 391, 17, "y"], [335, 20, 391, 18], [335, 23, 391, 21, "rect"], [335, 27, 391, 25], [335, 28, 391, 26, "pageY"], [335, 33, 391, 31], [336, 10, 392, 8, "absoluteX"], [336, 19, 392, 17], [336, 21, 392, 19, "event"], [336, 26, 392, 24], [336, 27, 392, 25, "x"], [336, 28, 392, 26], [337, 10, 393, 8, "absoluteY"], [337, 19, 393, 17], [337, 21, 393, 19, "event"], [337, 26, 393, 24], [337, 27, 393, 25, "y"], [338, 8, 394, 6], [338, 9, 394, 7], [338, 10, 394, 8], [339, 6, 395, 4], [339, 7, 395, 5], [339, 13, 395, 11], [340, 8, 396, 6, "trackerData"], [340, 19, 396, 17], [340, 20, 396, 18, "for<PERSON>ach"], [340, 27, 396, 25], [340, 28, 396, 26], [340, 29, 396, 27, "element"], [340, 36, 396, 34], [340, 38, 396, 36, "key"], [340, 41, 396, 39], [340, 46, 396, 44], [341, 10, 397, 8], [341, 16, 397, 14, "id"], [341, 18, 397, 16], [341, 21, 397, 19], [341, 25, 397, 23], [341, 26, 397, 24, "tracker"], [341, 33, 397, 31], [341, 34, 397, 32, "getMappedTouchEventId"], [341, 55, 397, 53], [341, 56, 397, 54, "key"], [341, 59, 397, 57], [341, 60, 397, 58], [342, 10, 398, 8, "changed"], [342, 17, 398, 15], [342, 18, 398, 16, "push"], [342, 22, 398, 20], [342, 23, 398, 21], [343, 12, 399, 10, "id"], [343, 14, 399, 12], [343, 16, 399, 14, "id"], [343, 18, 399, 16], [344, 12, 400, 10, "x"], [344, 13, 400, 11], [344, 15, 400, 13, "element"], [344, 22, 400, 20], [344, 23, 400, 21, "abosoluteCoords"], [344, 38, 400, 36], [344, 39, 400, 37, "x"], [344, 40, 400, 38], [344, 43, 400, 41, "rect"], [344, 47, 400, 45], [344, 48, 400, 46, "pageX"], [344, 53, 400, 51], [345, 12, 401, 10, "y"], [345, 13, 401, 11], [345, 15, 401, 13, "element"], [345, 22, 401, 20], [345, 23, 401, 21, "abosoluteCoords"], [345, 38, 401, 36], [345, 39, 401, 37, "y"], [345, 40, 401, 38], [345, 43, 401, 41, "rect"], [345, 47, 401, 45], [345, 48, 401, 46, "pageY"], [345, 53, 401, 51], [346, 12, 402, 10, "absoluteX"], [346, 21, 402, 19], [346, 23, 402, 21, "element"], [346, 30, 402, 28], [346, 31, 402, 29, "abosoluteCoords"], [346, 46, 402, 44], [346, 47, 402, 45, "x"], [346, 48, 402, 46], [347, 12, 403, 10, "absoluteY"], [347, 21, 403, 19], [347, 23, 403, 21, "element"], [347, 30, 403, 28], [347, 31, 403, 29, "abosoluteCoords"], [347, 46, 403, 44], [347, 47, 403, 45, "y"], [348, 10, 404, 8], [348, 11, 404, 9], [348, 12, 404, 10], [349, 8, 405, 6], [349, 9, 405, 7], [349, 10, 405, 8], [350, 6, 406, 4], [351, 6, 408, 4], [351, 10, 408, 8, "eventType"], [351, 19, 408, 17], [351, 22, 408, 20, "TouchEventType"], [351, 48, 408, 34], [351, 49, 408, 35, "UNDETERMINED"], [351, 61, 408, 47], [352, 6, 410, 4], [352, 14, 410, 12, "event"], [352, 19, 410, 17], [352, 20, 410, 18, "eventType"], [352, 29, 410, 27], [353, 8, 411, 6], [353, 13, 411, 11, "EventTypes"], [353, 35, 411, 21], [353, 36, 411, 22, "DOWN"], [353, 40, 411, 26], [354, 8, 412, 6], [354, 13, 412, 11, "EventTypes"], [354, 35, 412, 21], [354, 36, 412, 22, "ADDITIONAL_POINTER_DOWN"], [354, 59, 412, 45], [355, 10, 413, 8, "eventType"], [355, 19, 413, 17], [355, 22, 413, 20, "TouchEventType"], [355, 48, 413, 34], [355, 49, 413, 35, "DOWN"], [355, 53, 413, 39], [356, 10, 414, 8], [357, 8, 416, 6], [357, 13, 416, 11, "EventTypes"], [357, 35, 416, 21], [357, 36, 416, 22, "UP"], [357, 38, 416, 24], [358, 8, 417, 6], [358, 13, 417, 11, "EventTypes"], [358, 35, 417, 21], [358, 36, 417, 22, "ADDITIONAL_POINTER_UP"], [358, 57, 417, 43], [359, 10, 418, 8, "eventType"], [359, 19, 418, 17], [359, 22, 418, 20, "TouchEventType"], [359, 48, 418, 34], [359, 49, 418, 35, "UP"], [359, 51, 418, 37], [360, 10, 419, 8], [361, 8, 421, 6], [361, 13, 421, 11, "EventTypes"], [361, 35, 421, 21], [361, 36, 421, 22, "MOVE"], [361, 40, 421, 26], [362, 10, 422, 8, "eventType"], [362, 19, 422, 17], [362, 22, 422, 20, "TouchEventType"], [362, 48, 422, 34], [362, 49, 422, 35, "MOVE"], [362, 53, 422, 39], [363, 10, 423, 8], [364, 8, 425, 6], [364, 13, 425, 11, "EventTypes"], [364, 35, 425, 21], [364, 36, 425, 22, "CANCEL"], [364, 42, 425, 28], [365, 10, 426, 8, "eventType"], [365, 19, 426, 17], [365, 22, 426, 20, "TouchEventType"], [365, 48, 426, 34], [365, 49, 426, 35, "CANCELLED"], [365, 58, 426, 44], [366, 10, 427, 8], [367, 6, 428, 4], [367, 7, 428, 5], [367, 8, 428, 6], [368, 6, 429, 4], [369, 6, 430, 4], [371, 6, 433, 4], [371, 10, 433, 8, "numberOfTouches"], [371, 25, 433, 23], [371, 28, 433, 26, "all"], [371, 31, 433, 29], [371, 32, 433, 30, "length"], [371, 38, 433, 36], [372, 6, 435, 4], [372, 10, 435, 8, "event"], [372, 15, 435, 13], [372, 16, 435, 14, "eventType"], [372, 25, 435, 23], [372, 30, 435, 28, "EventTypes"], [372, 52, 435, 38], [372, 53, 435, 39, "UP"], [372, 55, 435, 41], [372, 59, 435, 45, "event"], [372, 64, 435, 50], [372, 65, 435, 51, "eventType"], [372, 74, 435, 60], [372, 79, 435, 65, "EventTypes"], [372, 101, 435, 75], [372, 102, 435, 76, "ADDITIONAL_POINTER_UP"], [372, 123, 435, 97], [372, 125, 435, 99], [373, 8, 436, 6], [373, 10, 436, 8, "numberOfTouches"], [373, 25, 436, 23], [374, 6, 437, 4], [375, 6, 439, 4], [375, 13, 439, 11], [376, 8, 440, 6, "nativeEvent"], [376, 19, 440, 17], [376, 21, 440, 19], [377, 10, 441, 8, "handlerTag"], [377, 20, 441, 18], [377, 22, 441, 20], [377, 26, 441, 24], [377, 27, 441, 25, "handlerTag"], [377, 37, 441, 35], [378, 10, 442, 8, "state"], [378, 15, 442, 13], [378, 17, 442, 15], [378, 21, 442, 19], [378, 22, 442, 20, "state"], [378, 27, 442, 25], [379, 10, 443, 8, "eventType"], [379, 19, 443, 17], [379, 21, 443, 19, "eventType"], [379, 30, 443, 28], [380, 10, 444, 8, "changedTouches"], [380, 24, 444, 22], [380, 26, 444, 24, "changed"], [380, 33, 444, 31], [381, 10, 445, 8, "allTouches"], [381, 20, 445, 18], [381, 22, 445, 20, "all"], [381, 25, 445, 23], [382, 10, 446, 8, "numberOfTouches"], [382, 25, 446, 23], [382, 27, 446, 25, "numberOfTouches"], [382, 42, 446, 40], [383, 10, 447, 8, "pointerType"], [383, 21, 447, 19], [383, 23, 447, 21], [383, 27, 447, 25], [383, 28, 447, 26, "pointerType"], [384, 8, 448, 6], [384, 9, 448, 7], [385, 8, 449, 6, "timeStamp"], [385, 17, 449, 15], [385, 19, 449, 17, "Date"], [385, 23, 449, 21], [385, 24, 449, 22, "now"], [385, 27, 449, 25], [385, 28, 449, 26], [386, 6, 450, 4], [386, 7, 450, 5], [387, 4, 451, 2], [388, 4, 453, 2, "cancelTouches"], [388, 17, 453, 15, "cancelTouches"], [388, 18, 453, 15], [388, 20, 453, 18], [389, 6, 454, 4], [389, 12, 454, 10, "rect"], [389, 16, 454, 14], [389, 19, 454, 17], [389, 23, 454, 21], [389, 24, 454, 22, "delegate"], [389, 32, 454, 30], [389, 33, 454, 31, "measure<PERSON>iew"], [389, 44, 454, 42], [389, 45, 454, 43], [389, 46, 454, 44], [390, 6, 455, 4], [390, 12, 455, 10, "all"], [390, 15, 455, 13], [390, 18, 455, 16], [390, 20, 455, 18], [391, 6, 456, 4], [391, 12, 456, 10, "changed"], [391, 19, 456, 17], [391, 22, 456, 20], [391, 24, 456, 22], [392, 6, 457, 4], [392, 12, 457, 10, "trackerData"], [392, 23, 457, 21], [392, 26, 457, 24], [392, 30, 457, 28], [392, 31, 457, 29, "tracker"], [392, 38, 457, 36], [392, 39, 457, 37, "trackedPointers"], [392, 54, 457, 52], [393, 6, 459, 4], [393, 10, 459, 8, "trackerData"], [393, 21, 459, 19], [393, 22, 459, 20, "size"], [393, 26, 459, 24], [393, 31, 459, 29], [393, 32, 459, 30], [393, 34, 459, 32], [394, 8, 460, 6], [395, 6, 461, 4], [396, 6, 463, 4, "trackerData"], [396, 17, 463, 15], [396, 18, 463, 16, "for<PERSON>ach"], [396, 25, 463, 23], [396, 26, 463, 24], [396, 27, 463, 25, "element"], [396, 34, 463, 32], [396, 36, 463, 34, "key"], [396, 39, 463, 37], [396, 44, 463, 42], [397, 8, 464, 6], [397, 14, 464, 12, "id"], [397, 16, 464, 14], [397, 19, 464, 17], [397, 23, 464, 21], [397, 24, 464, 22, "tracker"], [397, 31, 464, 29], [397, 32, 464, 30, "getMappedTouchEventId"], [397, 53, 464, 51], [397, 54, 464, 52, "key"], [397, 57, 464, 55], [397, 58, 464, 56], [398, 8, 465, 6, "all"], [398, 11, 465, 9], [398, 12, 465, 10, "push"], [398, 16, 465, 14], [398, 17, 465, 15], [399, 10, 466, 8, "id"], [399, 12, 466, 10], [399, 14, 466, 12, "id"], [399, 16, 466, 14], [400, 10, 467, 8, "x"], [400, 11, 467, 9], [400, 13, 467, 11, "element"], [400, 20, 467, 18], [400, 21, 467, 19, "abosoluteCoords"], [400, 36, 467, 34], [400, 37, 467, 35, "x"], [400, 38, 467, 36], [400, 41, 467, 39, "rect"], [400, 45, 467, 43], [400, 46, 467, 44, "pageX"], [400, 51, 467, 49], [401, 10, 468, 8, "y"], [401, 11, 468, 9], [401, 13, 468, 11, "element"], [401, 20, 468, 18], [401, 21, 468, 19, "abosoluteCoords"], [401, 36, 468, 34], [401, 37, 468, 35, "y"], [401, 38, 468, 36], [401, 41, 468, 39, "rect"], [401, 45, 468, 43], [401, 46, 468, 44, "pageY"], [401, 51, 468, 49], [402, 10, 469, 8, "absoluteX"], [402, 19, 469, 17], [402, 21, 469, 19, "element"], [402, 28, 469, 26], [402, 29, 469, 27, "abosoluteCoords"], [402, 44, 469, 42], [402, 45, 469, 43, "x"], [402, 46, 469, 44], [403, 10, 470, 8, "absoluteY"], [403, 19, 470, 17], [403, 21, 470, 19, "element"], [403, 28, 470, 26], [403, 29, 470, 27, "abosoluteCoords"], [403, 44, 470, 42], [403, 45, 470, 43, "y"], [404, 8, 471, 6], [404, 9, 471, 7], [404, 10, 471, 8], [405, 8, 472, 6, "changed"], [405, 15, 472, 13], [405, 16, 472, 14, "push"], [405, 20, 472, 18], [405, 21, 472, 19], [406, 10, 473, 8, "id"], [406, 12, 473, 10], [406, 14, 473, 12, "id"], [406, 16, 473, 14], [407, 10, 474, 8, "x"], [407, 11, 474, 9], [407, 13, 474, 11, "element"], [407, 20, 474, 18], [407, 21, 474, 19, "abosoluteCoords"], [407, 36, 474, 34], [407, 37, 474, 35, "x"], [407, 38, 474, 36], [407, 41, 474, 39, "rect"], [407, 45, 474, 43], [407, 46, 474, 44, "pageX"], [407, 51, 474, 49], [408, 10, 475, 8, "y"], [408, 11, 475, 9], [408, 13, 475, 11, "element"], [408, 20, 475, 18], [408, 21, 475, 19, "abosoluteCoords"], [408, 36, 475, 34], [408, 37, 475, 35, "y"], [408, 38, 475, 36], [408, 41, 475, 39, "rect"], [408, 45, 475, 43], [408, 46, 475, 44, "pageY"], [408, 51, 475, 49], [409, 10, 476, 8, "absoluteX"], [409, 19, 476, 17], [409, 21, 476, 19, "element"], [409, 28, 476, 26], [409, 29, 476, 27, "abosoluteCoords"], [409, 44, 476, 42], [409, 45, 476, 43, "x"], [409, 46, 476, 44], [410, 10, 477, 8, "absoluteY"], [410, 19, 477, 17], [410, 21, 477, 19, "element"], [410, 28, 477, 26], [410, 29, 477, 27, "abosoluteCoords"], [410, 44, 477, 42], [410, 45, 477, 43, "y"], [411, 8, 478, 6], [411, 9, 478, 7], [411, 10, 478, 8], [412, 6, 479, 4], [412, 7, 479, 5], [412, 8, 479, 6], [413, 6, 480, 4], [413, 12, 480, 10, "cancelEvent"], [413, 23, 480, 21], [413, 26, 480, 24], [414, 8, 481, 6, "nativeEvent"], [414, 19, 481, 17], [414, 21, 481, 19], [415, 10, 482, 8, "handlerTag"], [415, 20, 482, 18], [415, 22, 482, 20], [415, 26, 482, 24], [415, 27, 482, 25, "handlerTag"], [415, 37, 482, 35], [416, 10, 483, 8, "state"], [416, 15, 483, 13], [416, 17, 483, 15], [416, 21, 483, 19], [416, 22, 483, 20, "state"], [416, 27, 483, 25], [417, 10, 484, 8, "eventType"], [417, 19, 484, 17], [417, 21, 484, 19, "TouchEventType"], [417, 47, 484, 33], [417, 48, 484, 34, "CANCELLED"], [417, 57, 484, 43], [418, 10, 485, 8, "changedTouches"], [418, 24, 485, 22], [418, 26, 485, 24, "changed"], [418, 33, 485, 31], [419, 10, 486, 8, "allTouches"], [419, 20, 486, 18], [419, 22, 486, 20, "all"], [419, 25, 486, 23], [420, 10, 487, 8, "numberOfTouches"], [420, 25, 487, 23], [420, 27, 487, 25, "all"], [420, 30, 487, 28], [420, 31, 487, 29, "length"], [420, 37, 487, 35], [421, 10, 488, 8, "pointerType"], [421, 21, 488, 19], [421, 23, 488, 21], [421, 27, 488, 25], [421, 28, 488, 26, "pointerType"], [422, 8, 489, 6], [422, 9, 489, 7], [423, 8, 490, 6, "timeStamp"], [423, 17, 490, 15], [423, 19, 490, 17, "Date"], [423, 23, 490, 21], [423, 24, 490, 22, "now"], [423, 27, 490, 25], [423, 28, 490, 26], [424, 6, 491, 4], [424, 7, 491, 5], [425, 6, 492, 4], [425, 12, 492, 10], [426, 8, 493, 6, "onGestureHandlerEvent"], [427, 6, 494, 4], [427, 7, 494, 5], [427, 10, 494, 8], [427, 14, 494, 12], [427, 15, 494, 13, "propsRef"], [427, 23, 494, 21], [427, 24, 494, 22, "current"], [427, 31, 494, 29], [428, 6, 495, 4, "invokeNullableMethod"], [428, 26, 495, 24], [428, 27, 495, 25, "onGestureHandlerEvent"], [428, 48, 495, 46], [428, 50, 495, 48, "cancelEvent"], [428, 61, 495, 59], [428, 62, 495, 60], [429, 4, 496, 2], [430, 4, 498, 2, "transformNativeEvent"], [430, 24, 498, 22, "transformNativeEvent"], [430, 25, 498, 22], [430, 27, 498, 25], [431, 6, 499, 4], [432, 6, 500, 4], [432, 12, 500, 10, "lastCoords"], [432, 22, 500, 20], [432, 25, 500, 23], [432, 29, 500, 27], [432, 30, 500, 28, "tracker"], [432, 37, 500, 35], [432, 38, 500, 36, "getAbsoluteCoordsAverage"], [432, 62, 500, 60], [432, 63, 500, 61], [432, 64, 500, 62], [433, 6, 501, 4], [433, 12, 501, 10, "lastRelativeCoords"], [433, 30, 501, 28], [433, 33, 501, 31], [433, 37, 501, 35], [433, 38, 501, 36, "tracker"], [433, 45, 501, 43], [433, 46, 501, 44, "getRelativeCoordsAverage"], [433, 70, 501, 68], [433, 71, 501, 69], [433, 72, 501, 70], [434, 6, 502, 4], [434, 13, 502, 11], [435, 8, 503, 6, "x"], [435, 9, 503, 7], [435, 11, 503, 9, "lastRelativeCoords"], [435, 29, 503, 27], [435, 30, 503, 28, "x"], [435, 31, 503, 29], [436, 8, 504, 6, "y"], [436, 9, 504, 7], [436, 11, 504, 9, "lastRelativeCoords"], [436, 29, 504, 27], [436, 30, 504, 28, "y"], [436, 31, 504, 29], [437, 8, 505, 6, "absoluteX"], [437, 17, 505, 15], [437, 19, 505, 17, "lastCoords"], [437, 29, 505, 27], [437, 30, 505, 28, "x"], [437, 31, 505, 29], [438, 8, 506, 6, "absoluteY"], [438, 17, 506, 15], [438, 19, 506, 17, "lastCoords"], [438, 29, 506, 27], [438, 30, 506, 28, "y"], [439, 6, 507, 4], [439, 7, 507, 5], [440, 4, 508, 2], [440, 5, 508, 3], [440, 6, 508, 4], [441, 4, 509, 2], [442, 4, 510, 2], [444, 4, 513, 2, "updateGestureConfig"], [444, 23, 513, 21, "updateGestureConfig"], [444, 24, 513, 22], [445, 6, 514, 4, "enabled"], [445, 13, 514, 11], [445, 16, 514, 14], [445, 20, 514, 18], [446, 6, 515, 4], [446, 9, 515, 7, "props"], [447, 4, 516, 2], [447, 5, 516, 3], [447, 7, 516, 5], [448, 6, 517, 4], [448, 10, 517, 8], [448, 11, 517, 9, "_config"], [448, 18, 517, 16], [448, 21, 517, 19], [449, 8, 518, 6, "enabled"], [449, 15, 518, 13], [449, 17, 518, 15, "enabled"], [449, 24, 518, 22], [450, 8, 519, 6], [450, 11, 519, 9, "props"], [451, 6, 520, 4], [451, 7, 520, 5], [452, 6, 521, 4], [452, 10, 521, 8], [452, 11, 521, 9, "enabled"], [452, 18, 521, 16], [452, 21, 521, 19, "enabled"], [452, 28, 521, 26], [453, 6, 522, 4], [453, 10, 522, 8], [453, 11, 522, 9, "delegate"], [453, 19, 522, 17], [453, 20, 522, 18, "onEnabledChange"], [453, 35, 522, 33], [453, 36, 522, 34, "enabled"], [453, 43, 522, 41], [453, 44, 522, 42], [454, 6, 524, 4], [454, 10, 524, 8], [454, 14, 524, 12], [454, 15, 524, 13, "config"], [454, 21, 524, 19], [454, 22, 524, 20, "shouldCancelWhenOutside"], [454, 45, 524, 43], [454, 50, 524, 48, "undefined"], [454, 59, 524, 57], [454, 61, 524, 59], [455, 8, 525, 6], [455, 12, 525, 10], [455, 13, 525, 11, "shouldCancelWhenOutside"], [455, 36, 525, 34], [455, 39, 525, 37], [455, 43, 525, 41], [455, 44, 525, 42, "config"], [455, 50, 525, 48], [455, 51, 525, 49, "shouldCancelWhenOutside"], [455, 74, 525, 72], [456, 6, 526, 4], [457, 6, 528, 4], [457, 10, 528, 8], [457, 11, 528, 9, "validateHitSlops"], [457, 27, 528, 25], [457, 28, 528, 26], [457, 29, 528, 27], [458, 6, 530, 4], [458, 10, 530, 8], [458, 14, 530, 12], [458, 15, 530, 13, "enabled"], [458, 22, 530, 20], [458, 24, 530, 22], [459, 8, 531, 6], [460, 6, 532, 4], [461, 6, 534, 4], [461, 14, 534, 12], [461, 18, 534, 16], [461, 19, 534, 17, "state"], [461, 24, 534, 22], [462, 8, 535, 6], [462, 13, 535, 11, "State"], [462, 25, 535, 16], [462, 26, 535, 17, "ACTIVE"], [462, 32, 535, 23], [463, 10, 536, 8], [463, 14, 536, 12], [463, 15, 536, 13, "fail"], [463, 19, 536, 17], [463, 20, 536, 18], [463, 24, 536, 22], [463, 25, 536, 23], [464, 10, 537, 8], [465, 8, 539, 6], [465, 13, 539, 11, "State"], [465, 25, 539, 16], [465, 26, 539, 17, "UNDETERMINED"], [465, 38, 539, 29], [466, 10, 540, 8, "GestureHandlerOrchestrator"], [466, 45, 540, 34], [466, 46, 540, 35, "instance"], [466, 54, 540, 43], [466, 55, 540, 44, "removeHandlerFromOrchestrator"], [466, 84, 540, 73], [466, 85, 540, 74], [466, 89, 540, 78], [466, 90, 540, 79], [467, 10, 541, 8], [468, 8, 543, 6], [469, 10, 544, 8], [469, 14, 544, 12], [469, 15, 544, 13, "cancel"], [469, 21, 544, 19], [469, 22, 544, 20], [469, 26, 544, 24], [469, 27, 544, 25], [470, 10, 545, 8], [471, 6, 546, 4], [472, 4, 547, 2], [473, 4, 549, 2, "checkCustomActivationCriteria"], [473, 33, 549, 31, "checkCustomActivationCriteria"], [473, 34, 549, 32, "criterias"], [473, 43, 549, 41], [473, 45, 549, 43], [474, 6, 550, 4], [474, 11, 550, 9], [474, 17, 550, 15, "key"], [474, 20, 550, 18], [474, 24, 550, 22], [474, 28, 550, 26], [474, 29, 550, 27, "config"], [474, 35, 550, 33], [474, 37, 550, 35], [475, 8, 551, 6], [475, 12, 551, 10, "criterias"], [475, 21, 551, 19], [475, 22, 551, 20, "indexOf"], [475, 29, 551, 27], [475, 30, 551, 28, "key"], [475, 33, 551, 31], [475, 34, 551, 32], [475, 38, 551, 36], [475, 39, 551, 37], [475, 41, 551, 39], [476, 10, 552, 8], [476, 14, 552, 12], [476, 15, 552, 13, "hasCustomActivationCriteria"], [476, 42, 552, 40], [476, 45, 552, 43], [476, 49, 552, 47], [477, 8, 553, 6], [478, 6, 554, 4], [479, 4, 555, 2], [480, 4, 557, 2, "validateHitSlops"], [480, 20, 557, 18, "validateHitSlops"], [480, 21, 557, 18], [480, 23, 557, 21], [481, 6, 558, 4], [481, 10, 558, 8], [481, 11, 558, 9], [481, 15, 558, 13], [481, 16, 558, 14, "config"], [481, 22, 558, 20], [481, 23, 558, 21, "hitSlop"], [481, 30, 558, 28], [481, 32, 558, 30], [482, 8, 559, 6], [483, 6, 560, 4], [484, 6, 562, 4], [484, 10, 562, 8], [484, 14, 562, 12], [484, 15, 562, 13, "config"], [484, 21, 562, 19], [484, 22, 562, 20, "hitSlop"], [484, 29, 562, 27], [484, 30, 562, 28, "left"], [484, 34, 562, 32], [484, 39, 562, 37, "undefined"], [484, 48, 562, 46], [484, 52, 562, 50], [484, 56, 562, 54], [484, 57, 562, 55, "config"], [484, 63, 562, 61], [484, 64, 562, 62, "hitSlop"], [484, 71, 562, 69], [484, 72, 562, 70, "right"], [484, 77, 562, 75], [484, 82, 562, 80, "undefined"], [484, 91, 562, 89], [484, 95, 562, 93], [484, 99, 562, 97], [484, 100, 562, 98, "config"], [484, 106, 562, 104], [484, 107, 562, 105, "hitSlop"], [484, 114, 562, 112], [484, 115, 562, 113, "width"], [484, 120, 562, 118], [484, 125, 562, 123, "undefined"], [484, 134, 562, 132], [484, 136, 562, 134], [485, 8, 563, 6], [485, 14, 563, 12], [485, 18, 563, 16, "Error"], [485, 23, 563, 21], [485, 24, 563, 22], [485, 93, 563, 91], [485, 94, 563, 92], [486, 6, 564, 4], [487, 6, 566, 4], [487, 10, 566, 8], [487, 14, 566, 12], [487, 15, 566, 13, "config"], [487, 21, 566, 19], [487, 22, 566, 20, "hitSlop"], [487, 29, 566, 27], [487, 30, 566, 28, "width"], [487, 35, 566, 33], [487, 40, 566, 38, "undefined"], [487, 49, 566, 47], [487, 53, 566, 51], [487, 57, 566, 55], [487, 58, 566, 56, "config"], [487, 64, 566, 62], [487, 65, 566, 63, "hitSlop"], [487, 72, 566, 70], [487, 73, 566, 71, "left"], [487, 77, 566, 75], [487, 82, 566, 80, "undefined"], [487, 91, 566, 89], [487, 95, 566, 93], [487, 99, 566, 97], [487, 100, 566, 98, "config"], [487, 106, 566, 104], [487, 107, 566, 105, "hitSlop"], [487, 114, 566, 112], [487, 115, 566, 113, "right"], [487, 120, 566, 118], [487, 125, 566, 123, "undefined"], [487, 134, 566, 132], [487, 136, 566, 134], [488, 8, 567, 6], [488, 14, 567, 12], [488, 18, 567, 16, "Error"], [488, 23, 567, 21], [488, 24, 567, 22], [488, 102, 567, 100], [488, 103, 567, 101], [489, 6, 568, 4], [490, 6, 570, 4], [490, 10, 570, 8], [490, 14, 570, 12], [490, 15, 570, 13, "config"], [490, 21, 570, 19], [490, 22, 570, 20, "hitSlop"], [490, 29, 570, 27], [490, 30, 570, 28, "height"], [490, 36, 570, 34], [490, 41, 570, 39, "undefined"], [490, 50, 570, 48], [490, 54, 570, 52], [490, 58, 570, 56], [490, 59, 570, 57, "config"], [490, 65, 570, 63], [490, 66, 570, 64, "hitSlop"], [490, 73, 570, 71], [490, 74, 570, 72, "top"], [490, 77, 570, 75], [490, 82, 570, 80, "undefined"], [490, 91, 570, 89], [490, 95, 570, 93], [490, 99, 570, 97], [490, 100, 570, 98, "config"], [490, 106, 570, 104], [490, 107, 570, 105, "hitSlop"], [490, 114, 570, 112], [490, 115, 570, 113, "bottom"], [490, 121, 570, 119], [490, 126, 570, 124, "undefined"], [490, 135, 570, 133], [490, 137, 570, 135], [491, 8, 571, 6], [491, 14, 571, 12], [491, 18, 571, 16, "Error"], [491, 23, 571, 21], [491, 24, 571, 22], [491, 94, 571, 92], [491, 95, 571, 93], [492, 6, 572, 4], [493, 6, 574, 4], [493, 10, 574, 8], [493, 14, 574, 12], [493, 15, 574, 13, "config"], [493, 21, 574, 19], [493, 22, 574, 20, "hitSlop"], [493, 29, 574, 27], [493, 30, 574, 28, "height"], [493, 36, 574, 34], [493, 41, 574, 39, "undefined"], [493, 50, 574, 48], [493, 54, 574, 52], [493, 58, 574, 56], [493, 59, 574, 57, "config"], [493, 65, 574, 63], [493, 66, 574, 64, "hitSlop"], [493, 73, 574, 71], [493, 74, 574, 72, "top"], [493, 77, 574, 75], [493, 82, 574, 80, "undefined"], [493, 91, 574, 89], [493, 95, 574, 93], [493, 99, 574, 97], [493, 100, 574, 98, "config"], [493, 106, 574, 104], [493, 107, 574, 105, "hitSlop"], [493, 114, 574, 112], [493, 115, 574, 113, "bottom"], [493, 121, 574, 119], [493, 126, 574, 124, "undefined"], [493, 135, 574, 133], [493, 137, 574, 135], [494, 8, 575, 6], [494, 14, 575, 12], [494, 18, 575, 16, "Error"], [494, 23, 575, 21], [494, 24, 575, 22], [494, 103, 575, 101], [494, 104, 575, 102], [495, 6, 576, 4], [496, 4, 577, 2], [497, 4, 579, 2, "checkHitSlop"], [497, 16, 579, 14, "checkHitSlop"], [497, 17, 579, 14], [497, 19, 579, 17], [498, 6, 580, 4], [498, 10, 580, 8], [498, 11, 580, 9], [498, 15, 580, 13], [498, 16, 580, 14, "config"], [498, 22, 580, 20], [498, 23, 580, 21, "hitSlop"], [498, 30, 580, 28], [498, 32, 580, 30], [499, 8, 581, 6], [499, 15, 581, 13], [499, 19, 581, 17], [500, 6, 582, 4], [501, 6, 584, 4], [501, 12, 584, 10], [502, 8, 585, 6, "width"], [502, 13, 585, 11], [503, 8, 586, 6, "height"], [504, 6, 587, 4], [504, 7, 587, 5], [504, 10, 587, 8], [504, 14, 587, 12], [504, 15, 587, 13, "delegate"], [504, 23, 587, 21], [504, 24, 587, 22, "measure<PERSON>iew"], [504, 35, 587, 33], [504, 36, 587, 34], [504, 37, 587, 35], [505, 6, 588, 4], [505, 10, 588, 8, "left"], [505, 14, 588, 12], [505, 17, 588, 15], [505, 18, 588, 16], [506, 6, 589, 4], [506, 10, 589, 8, "top"], [506, 13, 589, 11], [506, 16, 589, 14], [506, 17, 589, 15], [507, 6, 590, 4], [507, 10, 590, 8, "right"], [507, 15, 590, 13], [507, 18, 590, 16, "width"], [507, 23, 590, 21], [508, 6, 591, 4], [508, 10, 591, 8, "bottom"], [508, 16, 591, 14], [508, 19, 591, 17, "height"], [508, 25, 591, 23], [509, 6, 593, 4], [509, 10, 593, 8], [509, 14, 593, 12], [509, 15, 593, 13, "config"], [509, 21, 593, 19], [509, 22, 593, 20, "hitSlop"], [509, 29, 593, 27], [509, 30, 593, 28, "horizontal"], [509, 40, 593, 38], [509, 45, 593, 43, "undefined"], [509, 54, 593, 52], [509, 56, 593, 54], [510, 8, 594, 6, "left"], [510, 12, 594, 10], [510, 16, 594, 14], [510, 20, 594, 18], [510, 21, 594, 19, "config"], [510, 27, 594, 25], [510, 28, 594, 26, "hitSlop"], [510, 35, 594, 33], [510, 36, 594, 34, "horizontal"], [510, 46, 594, 44], [511, 8, 595, 6, "right"], [511, 13, 595, 11], [511, 17, 595, 15], [511, 21, 595, 19], [511, 22, 595, 20, "config"], [511, 28, 595, 26], [511, 29, 595, 27, "hitSlop"], [511, 36, 595, 34], [511, 37, 595, 35, "horizontal"], [511, 47, 595, 45], [512, 6, 596, 4], [513, 6, 598, 4], [513, 10, 598, 8], [513, 14, 598, 12], [513, 15, 598, 13, "config"], [513, 21, 598, 19], [513, 22, 598, 20, "hitSlop"], [513, 29, 598, 27], [513, 30, 598, 28, "vertical"], [513, 38, 598, 36], [513, 43, 598, 41, "undefined"], [513, 52, 598, 50], [513, 54, 598, 52], [514, 8, 599, 6, "top"], [514, 11, 599, 9], [514, 15, 599, 13], [514, 19, 599, 17], [514, 20, 599, 18, "config"], [514, 26, 599, 24], [514, 27, 599, 25, "hitSlop"], [514, 34, 599, 32], [514, 35, 599, 33, "vertical"], [514, 43, 599, 41], [515, 8, 600, 6, "bottom"], [515, 14, 600, 12], [515, 18, 600, 16], [515, 22, 600, 20], [515, 23, 600, 21, "config"], [515, 29, 600, 27], [515, 30, 600, 28, "hitSlop"], [515, 37, 600, 35], [515, 38, 600, 36, "vertical"], [515, 46, 600, 44], [516, 6, 601, 4], [517, 6, 603, 4], [517, 10, 603, 8], [517, 14, 603, 12], [517, 15, 603, 13, "config"], [517, 21, 603, 19], [517, 22, 603, 20, "hitSlop"], [517, 29, 603, 27], [517, 30, 603, 28, "left"], [517, 34, 603, 32], [517, 39, 603, 37, "undefined"], [517, 48, 603, 46], [517, 50, 603, 48], [518, 8, 604, 6, "left"], [518, 12, 604, 10], [518, 15, 604, 13], [518, 16, 604, 14], [518, 20, 604, 18], [518, 21, 604, 19, "config"], [518, 27, 604, 25], [518, 28, 604, 26, "hitSlop"], [518, 35, 604, 33], [518, 36, 604, 34, "left"], [518, 40, 604, 38], [519, 6, 605, 4], [520, 6, 607, 4], [520, 10, 607, 8], [520, 14, 607, 12], [520, 15, 607, 13, "config"], [520, 21, 607, 19], [520, 22, 607, 20, "hitSlop"], [520, 29, 607, 27], [520, 30, 607, 28, "right"], [520, 35, 607, 33], [520, 40, 607, 38, "undefined"], [520, 49, 607, 47], [520, 51, 607, 49], [521, 8, 608, 6, "right"], [521, 13, 608, 11], [521, 16, 608, 14, "width"], [521, 21, 608, 19], [521, 24, 608, 22], [521, 28, 608, 26], [521, 29, 608, 27, "config"], [521, 35, 608, 33], [521, 36, 608, 34, "hitSlop"], [521, 43, 608, 41], [521, 44, 608, 42, "right"], [521, 49, 608, 47], [522, 6, 609, 4], [523, 6, 611, 4], [523, 10, 611, 8], [523, 14, 611, 12], [523, 15, 611, 13, "config"], [523, 21, 611, 19], [523, 22, 611, 20, "hitSlop"], [523, 29, 611, 27], [523, 30, 611, 28, "top"], [523, 33, 611, 31], [523, 38, 611, 36, "undefined"], [523, 47, 611, 45], [523, 49, 611, 47], [524, 8, 612, 6, "top"], [524, 11, 612, 9], [524, 14, 612, 12], [524, 15, 612, 13], [524, 19, 612, 17], [524, 20, 612, 18, "config"], [524, 26, 612, 24], [524, 27, 612, 25, "hitSlop"], [524, 34, 612, 32], [524, 35, 612, 33, "top"], [524, 38, 612, 36], [525, 6, 613, 4], [526, 6, 615, 4], [526, 10, 615, 8], [526, 14, 615, 12], [526, 15, 615, 13, "config"], [526, 21, 615, 19], [526, 22, 615, 20, "hitSlop"], [526, 29, 615, 27], [526, 30, 615, 28, "bottom"], [526, 36, 615, 34], [526, 41, 615, 39, "undefined"], [526, 50, 615, 48], [526, 52, 615, 50], [527, 8, 616, 6, "bottom"], [527, 14, 616, 12], [527, 17, 616, 15, "width"], [527, 22, 616, 20], [527, 25, 616, 23], [527, 29, 616, 27], [527, 30, 616, 28, "config"], [527, 36, 616, 34], [527, 37, 616, 35, "hitSlop"], [527, 44, 616, 42], [527, 45, 616, 43, "bottom"], [527, 51, 616, 49], [528, 6, 617, 4], [529, 6, 619, 4], [529, 10, 619, 8], [529, 14, 619, 12], [529, 15, 619, 13, "config"], [529, 21, 619, 19], [529, 22, 619, 20, "hitSlop"], [529, 29, 619, 27], [529, 30, 619, 28, "width"], [529, 35, 619, 33], [529, 40, 619, 38, "undefined"], [529, 49, 619, 47], [529, 51, 619, 49], [530, 8, 620, 6], [530, 12, 620, 10], [530, 16, 620, 14], [530, 17, 620, 15, "config"], [530, 23, 620, 21], [530, 24, 620, 22, "hitSlop"], [530, 31, 620, 29], [530, 32, 620, 30, "left"], [530, 36, 620, 34], [530, 41, 620, 39, "undefined"], [530, 50, 620, 48], [530, 52, 620, 50], [531, 10, 621, 8, "right"], [531, 15, 621, 13], [531, 18, 621, 16, "left"], [531, 22, 621, 20], [531, 25, 621, 23], [531, 29, 621, 27], [531, 30, 621, 28, "config"], [531, 36, 621, 34], [531, 37, 621, 35, "hitSlop"], [531, 44, 621, 42], [531, 45, 621, 43, "width"], [531, 50, 621, 48], [532, 8, 622, 6], [532, 9, 622, 7], [532, 15, 622, 13], [532, 19, 622, 17], [532, 23, 622, 21], [532, 24, 622, 22, "config"], [532, 30, 622, 28], [532, 31, 622, 29, "hitSlop"], [532, 38, 622, 36], [532, 39, 622, 37, "right"], [532, 44, 622, 42], [532, 49, 622, 47, "undefined"], [532, 58, 622, 56], [532, 60, 622, 58], [533, 10, 623, 8, "left"], [533, 14, 623, 12], [533, 17, 623, 15, "right"], [533, 22, 623, 20], [533, 25, 623, 23], [533, 29, 623, 27], [533, 30, 623, 28, "config"], [533, 36, 623, 34], [533, 37, 623, 35, "hitSlop"], [533, 44, 623, 42], [533, 45, 623, 43, "width"], [533, 50, 623, 48], [534, 8, 624, 6], [535, 6, 625, 4], [536, 6, 627, 4], [536, 10, 627, 8], [536, 14, 627, 12], [536, 15, 627, 13, "config"], [536, 21, 627, 19], [536, 22, 627, 20, "hitSlop"], [536, 29, 627, 27], [536, 30, 627, 28, "height"], [536, 36, 627, 34], [536, 41, 627, 39, "undefined"], [536, 50, 627, 48], [536, 52, 627, 50], [537, 8, 628, 6], [537, 12, 628, 10], [537, 16, 628, 14], [537, 17, 628, 15, "config"], [537, 23, 628, 21], [537, 24, 628, 22, "hitSlop"], [537, 31, 628, 29], [537, 32, 628, 30, "top"], [537, 35, 628, 33], [537, 40, 628, 38, "undefined"], [537, 49, 628, 47], [537, 51, 628, 49], [538, 10, 629, 8, "bottom"], [538, 16, 629, 14], [538, 19, 629, 17, "top"], [538, 22, 629, 20], [538, 25, 629, 23], [538, 29, 629, 27], [538, 30, 629, 28, "config"], [538, 36, 629, 34], [538, 37, 629, 35, "hitSlop"], [538, 44, 629, 42], [538, 45, 629, 43, "height"], [538, 51, 629, 49], [539, 8, 630, 6], [539, 9, 630, 7], [539, 15, 630, 13], [539, 19, 630, 17], [539, 23, 630, 21], [539, 24, 630, 22, "config"], [539, 30, 630, 28], [539, 31, 630, 29, "hitSlop"], [539, 38, 630, 36], [539, 39, 630, 37, "bottom"], [539, 45, 630, 43], [539, 50, 630, 48, "undefined"], [539, 59, 630, 57], [539, 61, 630, 59], [540, 10, 631, 8, "top"], [540, 13, 631, 11], [540, 16, 631, 14, "bottom"], [540, 22, 631, 20], [540, 25, 631, 23], [540, 29, 631, 27], [540, 30, 631, 28, "config"], [540, 36, 631, 34], [540, 37, 631, 35, "hitSlop"], [540, 44, 631, 42], [540, 45, 631, 43, "height"], [540, 51, 631, 49], [541, 8, 632, 6], [542, 6, 633, 4], [543, 6, 635, 4], [543, 12, 635, 10, "rect"], [543, 16, 635, 14], [543, 19, 635, 17], [543, 23, 635, 21], [543, 24, 635, 22, "delegate"], [543, 32, 635, 30], [543, 33, 635, 31, "measure<PERSON>iew"], [543, 44, 635, 42], [543, 45, 635, 43], [543, 46, 635, 44], [544, 6, 636, 4], [544, 12, 636, 10], [545, 8, 637, 6, "x"], [545, 9, 637, 7], [546, 8, 638, 6, "y"], [547, 6, 639, 4], [547, 7, 639, 5], [547, 10, 639, 8], [547, 14, 639, 12], [547, 15, 639, 13, "tracker"], [547, 22, 639, 20], [547, 23, 639, 21, "getLastAbsoluteCoords"], [547, 44, 639, 42], [547, 45, 639, 43], [547, 46, 639, 44], [548, 6, 640, 4], [548, 12, 640, 10, "offsetX"], [548, 19, 640, 17], [548, 22, 640, 20, "x"], [548, 23, 640, 21], [548, 26, 640, 24, "rect"], [548, 30, 640, 28], [548, 31, 640, 29, "pageX"], [548, 36, 640, 34], [549, 6, 641, 4], [549, 12, 641, 10, "offsetY"], [549, 19, 641, 17], [549, 22, 641, 20, "y"], [549, 23, 641, 21], [549, 26, 641, 24, "rect"], [549, 30, 641, 28], [549, 31, 641, 29, "pageY"], [549, 36, 641, 34], [550, 6, 642, 4], [550, 13, 642, 11, "offsetX"], [550, 20, 642, 18], [550, 24, 642, 22, "left"], [550, 28, 642, 26], [550, 32, 642, 30, "offsetX"], [550, 39, 642, 37], [550, 43, 642, 41, "right"], [550, 48, 642, 46], [550, 52, 642, 50, "offsetY"], [550, 59, 642, 57], [550, 63, 642, 61, "top"], [550, 66, 642, 64], [550, 70, 642, 68, "offsetY"], [550, 77, 642, 75], [550, 81, 642, 79, "bottom"], [550, 87, 642, 85], [551, 4, 643, 2], [552, 4, 645, 2, "isButtonInConfig"], [552, 20, 645, 18, "isButtonInConfig"], [552, 21, 645, 19, "mouseButton"], [552, 32, 645, 30], [552, 34, 645, 32], [553, 6, 646, 4], [553, 13, 646, 11], [553, 14, 646, 12, "mouseButton"], [553, 25, 646, 23], [553, 29, 646, 27], [553, 30, 646, 28], [553, 34, 646, 32], [553, 35, 646, 33, "config"], [553, 41, 646, 39], [553, 42, 646, 40, "mouseButton"], [553, 53, 646, 51], [553, 57, 646, 55, "mouseButton"], [553, 68, 646, 66], [553, 73, 646, 71, "MouseB<PERSON>on"], [553, 106, 646, 82], [553, 107, 646, 83, "LEFT"], [553, 111, 646, 87], [553, 115, 646, 91], [553, 119, 646, 95], [553, 120, 646, 96, "config"], [553, 126, 646, 102], [553, 127, 646, 103, "mouseButton"], [553, 138, 646, 114], [553, 142, 646, 118, "mouseButton"], [553, 153, 646, 129], [553, 156, 646, 132], [553, 160, 646, 136], [553, 161, 646, 137, "config"], [553, 167, 646, 143], [553, 168, 646, 144, "mouseButton"], [553, 179, 646, 155], [554, 4, 647, 2], [555, 4, 649, 2, "resetConfig"], [555, 15, 649, 13, "resetConfig"], [555, 16, 649, 13], [555, 18, 649, 16], [555, 19, 649, 17], [556, 4, 651, 2, "onDestroy"], [556, 13, 651, 11, "onDestroy"], [556, 14, 651, 11], [556, 16, 651, 14], [557, 6, 652, 4], [557, 10, 652, 8], [557, 11, 652, 9, "delegate"], [557, 19, 652, 17], [557, 20, 652, 18, "destroy"], [557, 27, 652, 25], [557, 28, 652, 26], [557, 32, 652, 30], [557, 33, 652, 31, "config"], [557, 39, 652, 37], [557, 40, 652, 38], [558, 4, 653, 2], [558, 5, 653, 3], [558, 6, 653, 4], [559, 4, 654, 2], [560, 4, 655, 2], [562, 4, 658, 2], [562, 8, 658, 6, "handlerTag"], [562, 18, 658, 16, "handlerTag"], [562, 19, 658, 16], [562, 21, 658, 19], [563, 6, 659, 4], [563, 13, 659, 11], [563, 17, 659, 15], [563, 18, 659, 16, "_handlerTag"], [563, 29, 659, 27], [564, 4, 660, 2], [565, 4, 662, 2], [565, 8, 662, 6, "handlerTag"], [565, 18, 662, 16, "handlerTag"], [565, 19, 662, 17, "value"], [565, 24, 662, 22], [565, 26, 662, 24], [566, 6, 663, 4], [566, 10, 663, 8], [566, 11, 663, 9, "_handlerTag"], [566, 22, 663, 20], [566, 25, 663, 23, "value"], [566, 30, 663, 28], [567, 4, 664, 2], [568, 4, 666, 2], [568, 8, 666, 6, "config"], [568, 14, 666, 12, "config"], [568, 15, 666, 12], [568, 17, 666, 15], [569, 6, 667, 4], [569, 13, 667, 11], [569, 17, 667, 15], [569, 18, 667, 16, "_config"], [569, 25, 667, 23], [570, 4, 668, 2], [571, 4, 670, 2], [571, 8, 670, 6, "delegate"], [571, 16, 670, 14, "delegate"], [571, 17, 670, 14], [571, 19, 670, 17], [572, 6, 671, 4], [572, 13, 671, 11], [572, 17, 671, 15], [572, 18, 671, 16, "_delegate"], [572, 27, 671, 25], [573, 4, 672, 2], [574, 4, 674, 2], [574, 8, 674, 6, "tracker"], [574, 15, 674, 13, "tracker"], [574, 16, 674, 13], [574, 18, 674, 16], [575, 6, 675, 4], [575, 13, 675, 11], [575, 17, 675, 15], [575, 18, 675, 16, "_tracker"], [575, 26, 675, 24], [576, 4, 676, 2], [577, 4, 678, 2], [577, 8, 678, 6, "state"], [577, 13, 678, 11, "state"], [577, 14, 678, 11], [577, 16, 678, 14], [578, 6, 679, 4], [578, 13, 679, 11], [578, 17, 679, 15], [578, 18, 679, 16, "_state"], [578, 24, 679, 22], [579, 4, 680, 2], [580, 4, 682, 2], [580, 8, 682, 6, "state"], [580, 13, 682, 11, "state"], [580, 14, 682, 12, "value"], [580, 19, 682, 17], [580, 21, 682, 19], [581, 6, 683, 4], [581, 10, 683, 8], [581, 11, 683, 9, "_state"], [581, 17, 683, 15], [581, 20, 683, 18, "value"], [581, 25, 683, 23], [582, 4, 684, 2], [583, 4, 686, 2], [583, 8, 686, 6, "shouldCancelWhenOutside"], [583, 31, 686, 29, "shouldCancelWhenOutside"], [583, 32, 686, 29], [583, 34, 686, 32], [584, 6, 687, 4], [584, 13, 687, 11], [584, 17, 687, 15], [584, 18, 687, 16, "_shouldCancelWhenOutside"], [584, 42, 687, 40], [585, 4, 688, 2], [586, 4, 690, 2], [586, 8, 690, 6, "shouldCancelWhenOutside"], [586, 31, 690, 29, "shouldCancelWhenOutside"], [586, 32, 690, 30, "value"], [586, 37, 690, 35], [586, 39, 690, 37], [587, 6, 691, 4], [587, 10, 691, 8], [587, 11, 691, 9, "_shouldCancelWhenOutside"], [587, 35, 691, 33], [587, 38, 691, 36, "value"], [587, 43, 691, 41], [588, 4, 692, 2], [589, 4, 694, 2], [589, 8, 694, 6, "enabled"], [589, 15, 694, 13, "enabled"], [589, 16, 694, 13], [589, 18, 694, 16], [590, 6, 695, 4], [590, 13, 695, 11], [590, 17, 695, 15], [590, 18, 695, 16, "_enabled"], [590, 26, 695, 24], [591, 4, 696, 2], [592, 4, 698, 2], [592, 8, 698, 6, "enabled"], [592, 15, 698, 13, "enabled"], [592, 16, 698, 14, "value"], [592, 21, 698, 19], [592, 23, 698, 21], [593, 6, 699, 4], [593, 10, 699, 8], [593, 11, 699, 9, "_enabled"], [593, 19, 699, 17], [593, 22, 699, 20, "value"], [593, 27, 699, 25], [594, 4, 700, 2], [595, 4, 702, 2], [595, 8, 702, 6, "pointerType"], [595, 19, 702, 17, "pointerType"], [595, 20, 702, 17], [595, 22, 702, 20], [596, 6, 703, 4], [596, 13, 703, 11], [596, 17, 703, 15], [596, 18, 703, 16, "_pointerType"], [596, 30, 703, 28], [597, 4, 704, 2], [598, 4, 706, 2], [598, 8, 706, 6, "pointerType"], [598, 19, 706, 17, "pointerType"], [598, 20, 706, 18, "value"], [598, 25, 706, 23], [598, 27, 706, 25], [599, 6, 707, 4], [599, 10, 707, 8], [599, 11, 707, 9, "_pointerType"], [599, 23, 707, 21], [599, 26, 707, 24, "value"], [599, 31, 707, 29], [600, 4, 708, 2], [601, 4, 710, 2], [601, 8, 710, 6, "active"], [601, 14, 710, 12, "active"], [601, 15, 710, 12], [601, 17, 710, 15], [602, 6, 711, 4], [602, 13, 711, 11], [602, 17, 711, 15], [602, 18, 711, 16, "_active"], [602, 25, 711, 23], [603, 4, 712, 2], [604, 4, 714, 2], [604, 8, 714, 6, "active"], [604, 14, 714, 12, "active"], [604, 15, 714, 13, "value"], [604, 20, 714, 18], [604, 22, 714, 20], [605, 6, 715, 4], [605, 10, 715, 8], [605, 11, 715, 9, "_active"], [605, 18, 715, 16], [605, 21, 715, 19, "value"], [605, 26, 715, 24], [606, 4, 716, 2], [607, 4, 718, 2], [607, 8, 718, 6, "awaiting"], [607, 16, 718, 14, "awaiting"], [607, 17, 718, 14], [607, 19, 718, 17], [608, 6, 719, 4], [608, 13, 719, 11], [608, 17, 719, 15], [608, 18, 719, 16, "_awaiting"], [608, 27, 719, 25], [609, 4, 720, 2], [610, 4, 722, 2], [610, 8, 722, 6, "awaiting"], [610, 16, 722, 14, "awaiting"], [610, 17, 722, 15, "value"], [610, 22, 722, 20], [610, 24, 722, 22], [611, 6, 723, 4], [611, 10, 723, 8], [611, 11, 723, 9, "_awaiting"], [611, 20, 723, 18], [611, 23, 723, 21, "value"], [611, 28, 723, 26], [612, 4, 724, 2], [613, 4, 726, 2], [613, 8, 726, 6, "activationIndex"], [613, 23, 726, 21, "activationIndex"], [613, 24, 726, 21], [613, 26, 726, 24], [614, 6, 727, 4], [614, 13, 727, 11], [614, 17, 727, 15], [614, 18, 727, 16, "_activationIndex"], [614, 34, 727, 32], [615, 4, 728, 2], [616, 4, 730, 2], [616, 8, 730, 6, "activationIndex"], [616, 23, 730, 21, "activationIndex"], [616, 24, 730, 22, "value"], [616, 29, 730, 27], [616, 31, 730, 29], [617, 6, 731, 4], [617, 10, 731, 8], [617, 11, 731, 9, "_activationIndex"], [617, 27, 731, 25], [617, 30, 731, 28, "value"], [617, 35, 731, 33], [618, 4, 732, 2], [619, 4, 734, 2], [619, 8, 734, 6, "shouldResetProgress"], [619, 27, 734, 25, "shouldResetProgress"], [619, 28, 734, 25], [619, 30, 734, 28], [620, 6, 735, 4], [620, 13, 735, 11], [620, 17, 735, 15], [620, 18, 735, 16, "_shouldResetProgress"], [620, 38, 735, 36], [621, 4, 736, 2], [622, 4, 738, 2], [622, 8, 738, 6, "shouldResetProgress"], [622, 27, 738, 25, "shouldResetProgress"], [622, 28, 738, 26, "value"], [622, 33, 738, 31], [622, 35, 738, 33], [623, 6, 739, 4], [623, 10, 739, 8], [623, 11, 739, 9, "_shouldResetProgress"], [623, 31, 739, 29], [623, 34, 739, 32, "value"], [623, 39, 739, 37], [624, 4, 740, 2], [625, 4, 742, 2, "getTrackedPointersID"], [625, 24, 742, 22, "getTrackedPointersID"], [625, 25, 742, 22], [625, 27, 742, 25], [626, 6, 743, 4], [626, 13, 743, 11], [626, 17, 743, 15], [626, 18, 743, 16, "tracker"], [626, 25, 743, 23], [626, 26, 743, 24, "trackedPointersIDs"], [626, 44, 743, 42], [627, 4, 744, 2], [628, 4, 746, 2, "isFinished"], [628, 14, 746, 12, "isFinished"], [628, 15, 746, 12], [628, 17, 746, 15], [629, 6, 747, 4], [629, 13, 747, 11], [629, 17, 747, 15], [629, 18, 747, 16, "state"], [629, 23, 747, 21], [629, 28, 747, 26, "State"], [629, 40, 747, 31], [629, 41, 747, 32, "END"], [629, 44, 747, 35], [629, 48, 747, 39], [629, 52, 747, 43], [629, 53, 747, 44, "state"], [629, 58, 747, 49], [629, 63, 747, 54, "State"], [629, 75, 747, 59], [629, 76, 747, 60, "FAILED"], [629, 82, 747, 66], [629, 86, 747, 70], [629, 90, 747, 74], [629, 91, 747, 75, "state"], [629, 96, 747, 80], [629, 101, 747, 85, "State"], [629, 113, 747, 90], [629, 114, 747, 91, "CANCELLED"], [629, 123, 747, 100], [630, 4, 748, 2], [631, 2, 750, 0], [632, 2, 750, 1, "exports"], [632, 9, 750, 1], [632, 10, 750, 1, "default"], [632, 17, 750, 1], [632, 20, 750, 1, "Gesture<PERSON>andler"], [632, 34, 750, 1], [633, 2, 752, 0], [633, 11, 752, 9, "invokeNullableMethod"], [633, 31, 752, 29, "invokeNullableMethod"], [633, 32, 752, 30, "method"], [633, 38, 752, 36], [633, 40, 752, 38, "event"], [633, 45, 752, 43], [633, 47, 752, 45], [634, 4, 753, 2], [634, 8, 753, 6], [634, 9, 753, 7, "method"], [634, 15, 753, 13], [634, 17, 753, 15], [635, 6, 754, 4], [636, 4, 755, 2], [637, 4, 757, 2], [637, 8, 757, 6], [637, 15, 757, 13, "method"], [637, 21, 757, 19], [637, 26, 757, 24], [637, 36, 757, 34], [637, 38, 757, 36], [638, 6, 758, 4, "method"], [638, 12, 758, 10], [638, 13, 758, 11, "event"], [638, 18, 758, 16], [638, 19, 758, 17], [639, 6, 759, 4], [640, 4, 760, 2], [641, 4, 762, 2], [641, 8, 762, 6], [641, 22, 762, 20], [641, 26, 762, 24, "method"], [641, 32, 762, 30], [641, 36, 762, 34], [641, 43, 762, 41, "method"], [641, 49, 762, 47], [641, 50, 762, 48, "__<PERSON><PERSON><PERSON><PERSON>"], [641, 62, 762, 60], [641, 67, 762, 65], [641, 77, 762, 75], [641, 79, 762, 77], [642, 6, 763, 4], [642, 12, 763, 10, "handler"], [642, 19, 763, 17], [642, 22, 763, 20, "method"], [642, 28, 763, 26], [642, 29, 763, 27, "__<PERSON><PERSON><PERSON><PERSON>"], [642, 41, 763, 39], [642, 42, 763, 40], [642, 43, 763, 41], [643, 6, 765, 4, "invokeNullableMethod"], [643, 26, 765, 24], [643, 27, 765, 25, "handler"], [643, 34, 765, 32], [643, 36, 765, 34, "event"], [643, 41, 765, 39], [643, 42, 765, 40], [644, 6, 766, 4], [645, 4, 767, 2], [646, 4, 769, 2], [646, 8, 769, 6], [646, 10, 769, 8], [646, 24, 769, 22], [646, 28, 769, 26, "method"], [646, 34, 769, 32], [646, 35, 769, 33], [646, 37, 769, 35], [647, 6, 770, 4], [648, 4, 771, 2], [649, 4, 773, 2], [649, 10, 773, 8], [650, 6, 774, 4, "arg<PERSON><PERSON><PERSON>"], [651, 4, 775, 2], [651, 5, 775, 3], [651, 8, 775, 6, "method"], [651, 14, 775, 12], [651, 15, 775, 13, "__nodeConfig"], [651, 27, 775, 25], [652, 4, 777, 2], [652, 8, 777, 6], [652, 9, 777, 7, "Array"], [652, 14, 777, 12], [652, 15, 777, 13, "isArray"], [652, 22, 777, 20], [652, 23, 777, 21, "arg<PERSON><PERSON><PERSON>"], [652, 33, 777, 31], [652, 34, 777, 32], [652, 36, 777, 34], [653, 6, 778, 4], [654, 4, 779, 2], [655, 4, 781, 2], [655, 9, 781, 7], [655, 15, 781, 13], [655, 16, 781, 14, "index"], [655, 21, 781, 19], [655, 23, 781, 21], [655, 24, 781, 22, "key"], [655, 27, 781, 25], [655, 29, 781, 27, "value"], [655, 34, 781, 32], [655, 35, 781, 33], [655, 36, 781, 34], [655, 40, 781, 38, "arg<PERSON><PERSON><PERSON>"], [655, 50, 781, 48], [655, 51, 781, 49, "entries"], [655, 58, 781, 56], [655, 59, 781, 57], [655, 60, 781, 58], [655, 62, 781, 60], [656, 6, 782, 4], [656, 10, 782, 8], [656, 12, 782, 10, "key"], [656, 15, 782, 13], [656, 19, 782, 17, "event"], [656, 24, 782, 22], [656, 25, 782, 23, "nativeEvent"], [656, 36, 782, 34], [656, 37, 782, 35], [656, 39, 782, 37], [657, 8, 783, 6], [658, 6, 784, 4], [658, 7, 784, 5], [658, 8, 784, 6], [660, 6, 787, 4], [660, 12, 787, 10, "nativeValue"], [660, 23, 787, 21], [660, 26, 787, 24, "event"], [660, 31, 787, 29], [660, 32, 787, 30, "nativeEvent"], [660, 43, 787, 41], [660, 44, 787, 42, "key"], [660, 47, 787, 45], [660, 48, 787, 46], [660, 49, 787, 47], [660, 50, 787, 48], [662, 6, 789, 4], [662, 10, 789, 8, "value"], [662, 15, 789, 13], [662, 20, 789, 18], [662, 24, 789, 22], [662, 28, 789, 26, "value"], [662, 33, 789, 31], [662, 38, 789, 36], [662, 43, 789, 41], [662, 44, 789, 42], [662, 48, 789, 46, "value"], [662, 53, 789, 51], [662, 54, 789, 52, "setValue"], [662, 62, 789, 60], [662, 64, 789, 62], [663, 8, 790, 6], [664, 8, 791, 6], [665, 8, 792, 6, "value"], [665, 13, 792, 11], [665, 14, 792, 12, "setValue"], [665, 22, 792, 20], [665, 23, 792, 21, "nativeValue"], [665, 34, 792, 32], [665, 35, 792, 33], [666, 6, 793, 4], [666, 7, 793, 5], [666, 13, 793, 11], [667, 8, 794, 6], [668, 8, 795, 6, "method"], [668, 14, 795, 12], [668, 15, 795, 13, "__nodeConfig"], [668, 27, 795, 25], [668, 28, 795, 26, "arg<PERSON><PERSON><PERSON>"], [668, 38, 795, 36], [668, 39, 795, 37, "index"], [668, 44, 795, 42], [668, 45, 795, 43], [668, 48, 795, 46], [668, 49, 795, 47, "key"], [668, 52, 795, 50], [668, 54, 795, 52, "nativeValue"], [668, 65, 795, 63], [668, 66, 795, 64], [669, 6, 796, 4], [670, 4, 797, 2], [671, 4, 799, 2], [672, 2, 800, 0], [673, 0, 800, 1], [673, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "Gesture<PERSON>andler", "constructor", "_defineProperty$argument_2", "init", "attachEventManager", "onCancel", "onReset", "resetProgress", "reset", "moveToState", "onStateChange", "begin", "fail", "cancel", "activate", "end", "getShouldResetProgress", "setShouldResetProgress", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "onPointerDown", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerLeave", "onPointerEnter", "onPointerCancel", "onPointerOutOfBounds", "onPointerMoveOver", "onPointerMoveOut", "onWheel", "tryToSendMoveEvent", "tryToSendTouchEvent", "sendTouchEvent", "transformEventData", "transformTouchEvent", "trackerData.forEach$argument_0", "cancelTouches", "transformNativeEvent", "updateGestureConfig", "checkCustomActivationCriteria", "validateHitSlops", "checkHitSlop", "isButtonInConfig", "resetConfig", "onDestroy", "get__handlerTag", "set__handlerTag", "get__config", "get__delegate", "get__tracker", "get__state", "set__state", "get__shouldCancelWhenOutside", "set__shouldCancelWhenOutside", "get__enabled", "set__enabled", "get__pointerType", "set__pointerType", "get__active", "set__active", "get__awaiting", "set__awaiting", "get__activationIndex", "set__activationIndex", "get__shouldResetProgress", "set__shouldResetProgress", "getTrackedPointersID", "isFinished", "invokeNullableMethod"], "mappings": "AAA,iNC;eCU;ECE;uCCmC;KDmB;GDG;EGK;GHK;EIE;GJc;EKK,aL;EME,YN;EOE,kBP;EQE;GRM;ESK;GTkB;EUE,sCV;EWE;GXQ;EYM;GZS;EaM;GbO;EcE;GdK;EeE;GfQ;EgBK;GhBE;EiBE;GjBE;EkBE;GlBM;EmBE;GnBM;EoBE;GpBM;EqBE;GrBM;EsBK;GtBQ;EuBG;GvBE;EwBE;GxBE;EyBG;GzBE;E0BE;G1BE;E2BE;G3BgB;E4BE;G5BE;E6BE;G7BI;E8BE;G9BE;E+BE;G/BC;EgCE;GhCC;EiCE;GjCC;EkCE;GlCU;EmCE;GnCI;EoCE;GpCa;EqCK;GrCc;EsCE;wBCa;KDS;0BCY;ODS;GtC8C;EwCE;wBDU;KCgB;GxCiB;EyCE;GzCU;E0CK;G1CkC;E2CE;G3CM;E4CE;G5CoB;E6CE;G7CgE;E8CE;G9CE;E+CE,gB/C;EgDE;GhDE;EiDK;GjDE;EkDE;GlDE;EmDE;GnDE;EoDE;GpDE;EqDE;GrDE;EsDE;GtDE;EuDE;GvDE;EwDE;GxDE;EyDE;GzDE;E0DE;G1DE;E2DE;G3DE;E4DE;G5DE;E6DE;G7DE;E8DE;G9DE;E+DE;G/DE;EgEE;GhEE;EiEE;GjEE;EkEE;GlEE;EmEE;GnEE;EoEE;GpEE;EqEE;GrEE;EsEE;GtEE;EuEE;GvEE;CDE;AyEE;CzEgD"}}, "type": "js/module"}]}