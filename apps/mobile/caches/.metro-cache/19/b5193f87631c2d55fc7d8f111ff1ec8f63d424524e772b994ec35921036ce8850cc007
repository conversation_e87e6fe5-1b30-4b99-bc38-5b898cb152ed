{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 56, "index": 128}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 202}, "end": {"line": 5, "column": 40, "index": 242}}], "key": "80gGlYetrHmVdqDZvFU6/ojATpA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 243}, "end": {"line": 6, "column": 28, "index": 271}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/UseNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 272}, "end": {"line": 7, "column": 52, "index": 324}}], "key": "y+BEsDCJDgF/1HxgH+zR+gqP/Xw=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _extractProps = require(_dependencyMap[7], \"../lib/extract/extractProps\");\n  var _util = require(_dependencyMap[8], \"../lib/util\");\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[9], \"./Shape\"));\n  var _UseNativeComponent = _interopRequireDefault(require(_dependencyMap[10], \"../fabric/UseNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[11], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-svg/src/elements/Use.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Use = exports.default = /*#__PURE__*/function (_Shape) {\n    function Use() {\n      (0, _classCallCheck2.default)(this, Use);\n      return _callSuper(this, Use, arguments);\n    }\n    (0, _inherits2.default)(Use, _Shape);\n    return (0, _createClass2.default)(Use, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var children = props.children,\n          x = props.x,\n          y = props.y,\n          width = props.width,\n          height = props.height,\n          xlinkHref = props.xlinkHref,\n          _props$href = props.href,\n          href = _props$href === void 0 ? xlinkHref : _props$href;\n        var matched = href && href.match(_util.idPattern);\n        var match = matched && matched[1];\n        if (!match) {\n          console.warn('Invalid `href` prop for `Use` element, expected a href like \"#id\", but got: \"' + href + '\"');\n        }\n        var useProps = {\n          href: match ?? undefined,\n          x,\n          y,\n          width,\n          height\n        };\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_UseNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...(0, _extractProps.withoutXY)(this, props),\n          ...useProps,\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_Shape2.default);\n  Use.displayName = 'Use';\n  Use.defaultProps = {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  };\n});", "lineCount": 72, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractProps"], [13, 19, 3, 0], [13, 22, 3, 0, "require"], [13, 29, 3, 0], [13, 30, 3, 0, "_dependencyMap"], [13, 44, 3, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_util"], [14, 11, 5, 0], [14, 14, 5, 0, "require"], [14, 21, 5, 0], [14, 22, 5, 0, "_dependencyMap"], [14, 36, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_Shape2"], [15, 13, 6, 0], [15, 16, 6, 0, "_interopRequireDefault"], [15, 38, 6, 0], [15, 39, 6, 0, "require"], [15, 46, 6, 0], [15, 47, 6, 0, "_dependencyMap"], [15, 61, 6, 0], [16, 2, 7, 0], [16, 6, 7, 0, "_UseNativeComponent"], [16, 25, 7, 0], [16, 28, 7, 0, "_interopRequireDefault"], [16, 50, 7, 0], [16, 51, 7, 0, "require"], [16, 58, 7, 0], [16, 59, 7, 0, "_dependencyMap"], [16, 73, 7, 0], [17, 2, 7, 52], [17, 6, 7, 52, "_jsxDevRuntime"], [17, 20, 7, 52], [17, 23, 7, 52, "require"], [17, 30, 7, 52], [17, 31, 7, 52, "_dependencyMap"], [17, 45, 7, 52], [18, 2, 7, 52], [18, 6, 7, 52, "_jsxFileName"], [18, 18, 7, 52], [19, 2, 7, 52], [19, 11, 7, 52, "_interopRequireWildcard"], [19, 35, 7, 52, "e"], [19, 36, 7, 52], [19, 38, 7, 52, "t"], [19, 39, 7, 52], [19, 68, 7, 52, "WeakMap"], [19, 75, 7, 52], [19, 81, 7, 52, "r"], [19, 82, 7, 52], [19, 89, 7, 52, "WeakMap"], [19, 96, 7, 52], [19, 100, 7, 52, "n"], [19, 101, 7, 52], [19, 108, 7, 52, "WeakMap"], [19, 115, 7, 52], [19, 127, 7, 52, "_interopRequireWildcard"], [19, 150, 7, 52], [19, 162, 7, 52, "_interopRequireWildcard"], [19, 163, 7, 52, "e"], [19, 164, 7, 52], [19, 166, 7, 52, "t"], [19, 167, 7, 52], [19, 176, 7, 52, "t"], [19, 177, 7, 52], [19, 181, 7, 52, "e"], [19, 182, 7, 52], [19, 186, 7, 52, "e"], [19, 187, 7, 52], [19, 188, 7, 52, "__esModule"], [19, 198, 7, 52], [19, 207, 7, 52, "e"], [19, 208, 7, 52], [19, 214, 7, 52, "o"], [19, 215, 7, 52], [19, 217, 7, 52, "i"], [19, 218, 7, 52], [19, 220, 7, 52, "f"], [19, 221, 7, 52], [19, 226, 7, 52, "__proto__"], [19, 235, 7, 52], [19, 243, 7, 52, "default"], [19, 250, 7, 52], [19, 252, 7, 52, "e"], [19, 253, 7, 52], [19, 270, 7, 52, "e"], [19, 271, 7, 52], [19, 294, 7, 52, "e"], [19, 295, 7, 52], [19, 320, 7, 52, "e"], [19, 321, 7, 52], [19, 330, 7, 52, "f"], [19, 331, 7, 52], [19, 337, 7, 52, "o"], [19, 338, 7, 52], [19, 341, 7, 52, "t"], [19, 342, 7, 52], [19, 345, 7, 52, "n"], [19, 346, 7, 52], [19, 349, 7, 52, "r"], [19, 350, 7, 52], [19, 358, 7, 52, "o"], [19, 359, 7, 52], [19, 360, 7, 52, "has"], [19, 363, 7, 52], [19, 364, 7, 52, "e"], [19, 365, 7, 52], [19, 375, 7, 52, "o"], [19, 376, 7, 52], [19, 377, 7, 52, "get"], [19, 380, 7, 52], [19, 381, 7, 52, "e"], [19, 382, 7, 52], [19, 385, 7, 52, "o"], [19, 386, 7, 52], [19, 387, 7, 52, "set"], [19, 390, 7, 52], [19, 391, 7, 52, "e"], [19, 392, 7, 52], [19, 394, 7, 52, "f"], [19, 395, 7, 52], [19, 409, 7, 52, "_t"], [19, 411, 7, 52], [19, 415, 7, 52, "e"], [19, 416, 7, 52], [19, 432, 7, 52, "_t"], [19, 434, 7, 52], [19, 441, 7, 52, "hasOwnProperty"], [19, 455, 7, 52], [19, 456, 7, 52, "call"], [19, 460, 7, 52], [19, 461, 7, 52, "e"], [19, 462, 7, 52], [19, 464, 7, 52, "_t"], [19, 466, 7, 52], [19, 473, 7, 52, "i"], [19, 474, 7, 52], [19, 478, 7, 52, "o"], [19, 479, 7, 52], [19, 482, 7, 52, "Object"], [19, 488, 7, 52], [19, 489, 7, 52, "defineProperty"], [19, 503, 7, 52], [19, 508, 7, 52, "Object"], [19, 514, 7, 52], [19, 515, 7, 52, "getOwnPropertyDescriptor"], [19, 539, 7, 52], [19, 540, 7, 52, "e"], [19, 541, 7, 52], [19, 543, 7, 52, "_t"], [19, 545, 7, 52], [19, 552, 7, 52, "i"], [19, 553, 7, 52], [19, 554, 7, 52, "get"], [19, 557, 7, 52], [19, 561, 7, 52, "i"], [19, 562, 7, 52], [19, 563, 7, 52, "set"], [19, 566, 7, 52], [19, 570, 7, 52, "o"], [19, 571, 7, 52], [19, 572, 7, 52, "f"], [19, 573, 7, 52], [19, 575, 7, 52, "_t"], [19, 577, 7, 52], [19, 579, 7, 52, "i"], [19, 580, 7, 52], [19, 584, 7, 52, "f"], [19, 585, 7, 52], [19, 586, 7, 52, "_t"], [19, 588, 7, 52], [19, 592, 7, 52, "e"], [19, 593, 7, 52], [19, 594, 7, 52, "_t"], [19, 596, 7, 52], [19, 607, 7, 52, "f"], [19, 608, 7, 52], [19, 613, 7, 52, "e"], [19, 614, 7, 52], [19, 616, 7, 52, "t"], [19, 617, 7, 52], [20, 2, 7, 52], [20, 11, 7, 52, "_callSuper"], [20, 22, 7, 52, "t"], [20, 23, 7, 52], [20, 25, 7, 52, "o"], [20, 26, 7, 52], [20, 28, 7, 52, "e"], [20, 29, 7, 52], [20, 40, 7, 52, "o"], [20, 41, 7, 52], [20, 48, 7, 52, "_getPrototypeOf2"], [20, 64, 7, 52], [20, 65, 7, 52, "default"], [20, 72, 7, 52], [20, 74, 7, 52, "o"], [20, 75, 7, 52], [20, 82, 7, 52, "_possibleConstructorReturn2"], [20, 109, 7, 52], [20, 110, 7, 52, "default"], [20, 117, 7, 52], [20, 119, 7, 52, "t"], [20, 120, 7, 52], [20, 122, 7, 52, "_isNativeReflectConstruct"], [20, 147, 7, 52], [20, 152, 7, 52, "Reflect"], [20, 159, 7, 52], [20, 160, 7, 52, "construct"], [20, 169, 7, 52], [20, 170, 7, 52, "o"], [20, 171, 7, 52], [20, 173, 7, 52, "e"], [20, 174, 7, 52], [20, 186, 7, 52, "_getPrototypeOf2"], [20, 202, 7, 52], [20, 203, 7, 52, "default"], [20, 210, 7, 52], [20, 212, 7, 52, "t"], [20, 213, 7, 52], [20, 215, 7, 52, "constructor"], [20, 226, 7, 52], [20, 230, 7, 52, "o"], [20, 231, 7, 52], [20, 232, 7, 52, "apply"], [20, 237, 7, 52], [20, 238, 7, 52, "t"], [20, 239, 7, 52], [20, 241, 7, 52, "e"], [20, 242, 7, 52], [21, 2, 7, 52], [21, 11, 7, 52, "_isNativeReflectConstruct"], [21, 37, 7, 52], [21, 51, 7, 52, "t"], [21, 52, 7, 52], [21, 56, 7, 52, "Boolean"], [21, 63, 7, 52], [21, 64, 7, 52, "prototype"], [21, 73, 7, 52], [21, 74, 7, 52, "valueOf"], [21, 81, 7, 52], [21, 82, 7, 52, "call"], [21, 86, 7, 52], [21, 87, 7, 52, "Reflect"], [21, 94, 7, 52], [21, 95, 7, 52, "construct"], [21, 104, 7, 52], [21, 105, 7, 52, "Boolean"], [21, 112, 7, 52], [21, 145, 7, 52, "t"], [21, 146, 7, 52], [21, 159, 7, 52, "_isNativeReflectConstruct"], [21, 184, 7, 52], [21, 196, 7, 52, "_isNativeReflectConstruct"], [21, 197, 7, 52], [21, 210, 7, 52, "t"], [21, 211, 7, 52], [22, 2, 7, 52], [22, 6, 21, 21, "Use"], [22, 9, 21, 24], [22, 12, 21, 24, "exports"], [22, 19, 21, 24], [22, 20, 21, 24, "default"], [22, 27, 21, 24], [22, 53, 21, 24, "_Shape"], [22, 59, 21, 24], [23, 4, 21, 24], [23, 13, 21, 24, "Use"], [23, 17, 21, 24], [24, 6, 21, 24], [24, 10, 21, 24, "_classCallCheck2"], [24, 26, 21, 24], [24, 27, 21, 24, "default"], [24, 34, 21, 24], [24, 42, 21, 24, "Use"], [24, 45, 21, 24], [25, 6, 21, 24], [25, 13, 21, 24, "_callSuper"], [25, 23, 21, 24], [25, 30, 21, 24, "Use"], [25, 33, 21, 24], [25, 35, 21, 24, "arguments"], [25, 44, 21, 24], [26, 4, 21, 24], [27, 4, 21, 24], [27, 8, 21, 24, "_inherits2"], [27, 18, 21, 24], [27, 19, 21, 24, "default"], [27, 26, 21, 24], [27, 28, 21, 24, "Use"], [27, 31, 21, 24], [27, 33, 21, 24, "_Shape"], [27, 39, 21, 24], [28, 4, 21, 24], [28, 15, 21, 24, "_createClass2"], [28, 28, 21, 24], [28, 29, 21, 24, "default"], [28, 36, 21, 24], [28, 38, 21, 24, "Use"], [28, 41, 21, 24], [29, 6, 21, 24, "key"], [29, 9, 21, 24], [30, 6, 21, 24, "value"], [30, 11, 21, 24], [30, 13, 31, 2], [30, 22, 31, 2, "render"], [30, 28, 31, 8, "render"], [30, 29, 31, 8], [30, 31, 31, 11], [31, 8, 32, 4], [31, 12, 32, 12, "props"], [31, 17, 32, 17], [31, 20, 32, 22], [31, 24, 32, 26], [31, 25, 32, 12, "props"], [31, 30, 32, 17], [32, 8, 33, 4], [32, 12, 34, 6, "children"], [32, 20, 34, 14], [32, 23, 41, 8, "props"], [32, 28, 41, 13], [32, 29, 34, 6, "children"], [32, 37, 34, 14], [33, 10, 35, 6, "x"], [33, 11, 35, 7], [33, 14, 41, 8, "props"], [33, 19, 41, 13], [33, 20, 35, 6, "x"], [33, 21, 35, 7], [34, 10, 36, 6, "y"], [34, 11, 36, 7], [34, 14, 41, 8, "props"], [34, 19, 41, 13], [34, 20, 36, 6, "y"], [34, 21, 36, 7], [35, 10, 37, 6, "width"], [35, 15, 37, 11], [35, 18, 41, 8, "props"], [35, 23, 41, 13], [35, 24, 37, 6, "width"], [35, 29, 37, 11], [36, 10, 38, 6, "height"], [36, 16, 38, 12], [36, 19, 41, 8, "props"], [36, 24, 41, 13], [36, 25, 38, 6, "height"], [36, 31, 38, 12], [37, 10, 39, 6, "xlinkHref"], [37, 19, 39, 15], [37, 22, 41, 8, "props"], [37, 27, 41, 13], [37, 28, 39, 6, "xlinkHref"], [37, 37, 39, 15], [38, 10, 39, 15, "_props$href"], [38, 21, 39, 15], [38, 24, 41, 8, "props"], [38, 29, 41, 13], [38, 30, 40, 6, "href"], [38, 34, 40, 10], [39, 10, 40, 6, "href"], [39, 14, 40, 10], [39, 17, 40, 10, "_props$href"], [39, 28, 40, 10], [39, 42, 40, 13, "xlinkHref"], [39, 51, 40, 22], [39, 54, 40, 22, "_props$href"], [39, 65, 40, 22], [40, 8, 43, 4], [40, 12, 43, 10, "matched"], [40, 19, 43, 17], [40, 22, 43, 20, "href"], [40, 26, 43, 24], [40, 30, 43, 28, "href"], [40, 34, 43, 32], [40, 35, 43, 33, "match"], [40, 40, 43, 38], [40, 41, 43, 39, "idPattern"], [40, 56, 43, 48], [40, 57, 43, 49], [41, 8, 44, 4], [41, 12, 44, 10, "match"], [41, 17, 44, 15], [41, 20, 44, 18, "matched"], [41, 27, 44, 25], [41, 31, 44, 29, "matched"], [41, 38, 44, 36], [41, 39, 44, 37], [41, 40, 44, 38], [41, 41, 44, 39], [42, 8, 46, 4], [42, 12, 46, 8], [42, 13, 46, 9, "match"], [42, 18, 46, 14], [42, 20, 46, 16], [43, 10, 47, 6, "console"], [43, 17, 47, 13], [43, 18, 47, 14, "warn"], [43, 22, 47, 18], [43, 23, 48, 8], [43, 102, 48, 87], [43, 105, 49, 10, "href"], [43, 109, 49, 14], [43, 112, 50, 10], [43, 115, 51, 6], [43, 116, 51, 7], [44, 8, 52, 4], [45, 8, 53, 4], [45, 12, 53, 10, "useProps"], [45, 20, 53, 18], [45, 23, 53, 21], [46, 10, 54, 6, "href"], [46, 14, 54, 10], [46, 16, 54, 12, "match"], [46, 21, 54, 17], [46, 25, 54, 21, "undefined"], [46, 34, 54, 30], [47, 10, 55, 6, "x"], [47, 11, 55, 7], [48, 10, 56, 6, "y"], [48, 11, 56, 7], [49, 10, 57, 6, "width"], [49, 15, 57, 11], [50, 10, 58, 6, "height"], [51, 8, 59, 4], [51, 9, 59, 5], [52, 8, 60, 4], [52, 28, 61, 6], [52, 32, 61, 6, "_jsxDevRuntime"], [52, 46, 61, 6], [52, 47, 61, 6, "jsxDEV"], [52, 53, 61, 6], [52, 55, 61, 7, "_UseNativeComponent"], [52, 74, 61, 7], [52, 75, 61, 7, "default"], [52, 82, 61, 15], [53, 10, 62, 8, "ref"], [53, 13, 62, 11], [53, 15, 62, 14, "ref"], [53, 18, 62, 17], [53, 22, 62, 22], [53, 26, 62, 26], [53, 27, 62, 27, "refMethod"], [53, 36, 62, 36], [53, 37, 62, 37, "ref"], [53, 40, 62, 72], [53, 41, 62, 74], [54, 10, 62, 74], [54, 13, 63, 12], [54, 17, 63, 12, "withoutXY"], [54, 40, 63, 21], [54, 42, 63, 22], [54, 46, 63, 26], [54, 48, 63, 28, "props"], [54, 53, 63, 33], [54, 54, 63, 34], [55, 10, 63, 34], [55, 13, 64, 12, "useProps"], [55, 21, 64, 20], [56, 10, 64, 20, "children"], [56, 18, 64, 20], [56, 20, 65, 9, "children"], [57, 8, 65, 17], [58, 10, 65, 17, "fileName"], [58, 18, 65, 17], [58, 20, 65, 17, "_jsxFileName"], [58, 32, 65, 17], [59, 10, 65, 17, "lineNumber"], [59, 20, 65, 17], [60, 10, 65, 17, "columnNumber"], [60, 22, 65, 17], [61, 8, 65, 17], [61, 15, 66, 16], [61, 16, 66, 17], [62, 6, 68, 2], [63, 4, 68, 3], [64, 2, 68, 3], [64, 4, 21, 33, "<PERSON><PERSON><PERSON>"], [64, 19, 21, 38], [65, 2, 21, 21, "Use"], [65, 5, 21, 24], [65, 6, 22, 9, "displayName"], [65, 17, 22, 20], [65, 20, 22, 23], [65, 25, 22, 28], [66, 2, 21, 21, "Use"], [66, 5, 21, 24], [66, 6, 24, 9, "defaultProps"], [66, 18, 24, 21], [66, 21, 24, 24], [67, 4, 25, 4, "x"], [67, 5, 25, 5], [67, 7, 25, 7], [67, 8, 25, 8], [68, 4, 26, 4, "y"], [68, 5, 26, 5], [68, 7, 26, 7], [68, 8, 26, 8], [69, 4, 27, 4, "width"], [69, 9, 27, 9], [69, 11, 27, 11], [69, 12, 27, 12], [70, 4, 28, 4, "height"], [70, 10, 28, 10], [70, 12, 28, 12], [71, 2, 29, 2], [71, 3, 29, 3], [72, 0, 29, 3], [72, 3]], "functionMap": {"names": ["<global>", "Use", "render", "RNSVGUse.props.ref"], "mappings": "AAA;eCoB;ECU;aC+B,4DD;GDM;CDC"}}, "type": "js/module"}]}