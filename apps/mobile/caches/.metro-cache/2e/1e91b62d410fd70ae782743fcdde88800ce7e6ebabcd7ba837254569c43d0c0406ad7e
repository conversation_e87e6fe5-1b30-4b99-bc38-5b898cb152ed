{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.findHostInstance = findHostInstance;\n  function findHostInstance(_component) {}\n});", "lineCount": 9, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "findHostInstance"], [7, 26, 1, 13], [7, 29, 1, 13, "findHostInstance"], [7, 45, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "findHostInstance"], [8, 27, 3, 32, "findHostInstance"], [8, 28, 3, 33, "_component"], [8, 38, 3, 43], [8, 40, 3, 45], [8, 41, 3, 46], [9, 0, 3, 47], [9, 3]], "functionMap": {"names": ["<global>", "findHostInstance"], "mappings": "AAA;OCE,wCD"}}, "type": "js/module"}]}