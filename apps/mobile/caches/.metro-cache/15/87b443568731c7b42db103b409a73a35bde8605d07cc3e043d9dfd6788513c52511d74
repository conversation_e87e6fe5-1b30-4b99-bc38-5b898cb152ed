{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 33, "index": 281}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}, {"name": "./constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 282}, "end": {"line": 5, "column": 89, "index": 371}}], "key": "waDaw5D7vDr2hRFu0z1BqRCTzP4=", "exportNames": ["*"]}}, {"name": "./DiscreteGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 372}, "end": {"line": 6, "column": 62, "index": 434}}], "key": "dpOMqAtzFjjUQychI3TYNsYmXQE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 435}, "end": {"line": 7, "column": 66, "index": 501}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _State = require(_dependencyMap[2], \"../State\");\n  var _constants = require(_dependencyMap[3], \"./constants\");\n  var _DiscreteGestureHandler = _interopRequireDefault(require(_dependencyMap[4], \"./DiscreteGestureHandler\"));\n  var _utils = require(_dependencyMap[5], \"./utils\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class PressGestureHandler extends _DiscreteGestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"visualFeedbackTimer\", void 0);\n      _defineProperty(this, \"initialEvent\", null);\n      _defineProperty(this, \"shouldDelayTouches\", true);\n    }\n    get name() {\n      return 'press';\n    }\n    get minDurationMs() {\n      // @ts-ignore FIXME(TS)\n      return (0, _utils.isnan)(this.config.minDurationMs) ? 5 : this.config.minDurationMs;\n    }\n    get maxDist() {\n      return (0, _utils.isnan)(this.config.maxDist) ? 9 : this.config.maxDist;\n    }\n    get NativeGestureClass() {\n      return _hammerjs.default.Press;\n    }\n    simulateCancelEvent(inputData) {\n      // Long press never starts so we can't rely on the running event boolean.\n      this.hasGestureFailed = true;\n      this.cancelEvent(inputData);\n    }\n    updateHasCustomActivationCriteria({\n      shouldCancelWhenOutside,\n      maxDistSq\n    }) {\n      return shouldCancelWhenOutside || !(0, _utils.isValidNumber)(maxDistSq);\n    }\n    getState(type) {\n      return {\n        [_hammerjs.default.INPUT_START]: _State.State.BEGAN,\n        [_hammerjs.default.INPUT_MOVE]: _State.State.ACTIVE,\n        [_hammerjs.default.INPUT_END]: _State.State.END,\n        [_hammerjs.default.INPUT_CANCEL]: _State.State.CANCELLED\n      }[type];\n    }\n    getConfig() {\n      if (!this.hasCustomActivationCriteria) {\n        // Default config\n        // If no params have been defined then this config should emulate the native gesture as closely as possible.\n        return {\n          shouldCancelWhenOutside: true,\n          maxDistSq: 10\n        };\n      }\n      return this.config;\n    }\n    getHammerConfig() {\n      return {\n        ...super.getHammerConfig(),\n        // threshold: this.maxDist,\n        time: this.minDurationMs\n      };\n    }\n    onGestureActivated(ev) {\n      this.onGestureStart(ev);\n    }\n    shouldDelayTouchForEvent({\n      pointerType\n    }) {\n      // Don't disable event for mouse input\n      return this.shouldDelayTouches && pointerType === 'touch';\n    }\n    onGestureStart(ev) {\n      this.isGestureRunning = true;\n      clearTimeout(this.visualFeedbackTimer);\n      this.initialEvent = ev;\n      this.visualFeedbackTimer = (0, _utils.fireAfterInterval)(() => {\n        this.sendGestureStartedEvent(this.initialEvent);\n        this.initialEvent = null;\n      }, this.shouldDelayTouchForEvent(ev) && _constants.CONTENT_TOUCHES_DELAY);\n    }\n    sendGestureStartedEvent(ev) {\n      clearTimeout(this.visualFeedbackTimer);\n      this.visualFeedbackTimer = null;\n      this.sendEvent({\n        ...ev,\n        eventType: _hammerjs.default.INPUT_MOVE,\n        isFirst: true\n      });\n    }\n    forceInvalidate(event) {\n      super.forceInvalidate(event);\n      clearTimeout(this.visualFeedbackTimer);\n      this.visualFeedbackTimer = null;\n      this.initialEvent = null;\n    }\n    onRawEvent(ev) {\n      super.onRawEvent(ev);\n      if (this.isGestureRunning) {\n        if (ev.isFinal) {\n          let timeout;\n          if (this.visualFeedbackTimer) {\n            // Aesthetic timing for a quick tap.\n            // We haven't activated the tap right away to emulate iOS `delaysContentTouches`\n            // Now we must send the initial activation event and wait a set amount of time before firing the end event.\n            timeout = _constants.CONTENT_TOUCHES_QUICK_TAP_END_DELAY;\n            this.sendGestureStartedEvent(this.initialEvent);\n            this.initialEvent = null;\n          }\n          (0, _utils.fireAfterInterval)(() => {\n            this.sendEvent({\n              ...ev,\n              eventType: _hammerjs.default.INPUT_END,\n              isFinal: true\n            }); // @ts-ignore -- this should explicitly support undefined\n\n            this.onGestureEnded();\n          }, timeout);\n        } else {\n          this.sendEvent({\n            ...ev,\n            eventType: _hammerjs.default.INPUT_MOVE,\n            isFinal: false\n          });\n        }\n      }\n    }\n    updateGestureConfig({\n      shouldActivateOnStart = false,\n      disallowInterruption = false,\n      shouldCancelWhenOutside = true,\n      minDurationMs = Number.NaN,\n      maxDist = Number.NaN,\n      minPointers = 1,\n      maxPointers = 1,\n      ...props\n    }) {\n      return super.updateGestureConfig({\n        shouldActivateOnStart,\n        disallowInterruption,\n        shouldCancelWhenOutside,\n        minDurationMs,\n        maxDist,\n        minPointers,\n        maxPointers,\n        ...props\n      });\n    }\n  }\n  var _default = exports.default = PressGestureHandler;\n});", "lineCount": 169, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_hammerjs"], [7, 15, 3, 0], [7, 18, 3, 0, "_interopRequireDefault"], [7, 40, 3, 0], [7, 41, 3, 0, "require"], [7, 48, 3, 0], [7, 49, 3, 0, "_dependencyMap"], [7, 63, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_State"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_constants"], [9, 16, 5, 0], [9, 19, 5, 0, "require"], [9, 26, 5, 0], [9, 27, 5, 0, "_dependencyMap"], [9, 41, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_DiscreteGestureHandler"], [10, 29, 6, 0], [10, 32, 6, 0, "_interopRequireDefault"], [10, 54, 6, 0], [10, 55, 6, 0, "require"], [10, 62, 6, 0], [10, 63, 6, 0, "_dependencyMap"], [10, 77, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_utils"], [11, 12, 7, 0], [11, 15, 7, 0, "require"], [11, 22, 7, 0], [11, 23, 7, 0, "_dependencyMap"], [11, 37, 7, 0], [12, 2, 1, 0], [12, 11, 1, 9, "_defineProperty"], [12, 26, 1, 24, "_defineProperty"], [12, 27, 1, 25, "obj"], [12, 30, 1, 28], [12, 32, 1, 30, "key"], [12, 35, 1, 33], [12, 37, 1, 35, "value"], [12, 42, 1, 40], [12, 44, 1, 42], [13, 4, 1, 44], [13, 8, 1, 48, "key"], [13, 11, 1, 51], [13, 15, 1, 55, "obj"], [13, 18, 1, 58], [13, 20, 1, 60], [14, 6, 1, 62, "Object"], [14, 12, 1, 68], [14, 13, 1, 69, "defineProperty"], [14, 27, 1, 83], [14, 28, 1, 84, "obj"], [14, 31, 1, 87], [14, 33, 1, 89, "key"], [14, 36, 1, 92], [14, 38, 1, 94], [15, 8, 1, 96, "value"], [15, 13, 1, 101], [15, 15, 1, 103, "value"], [15, 20, 1, 108], [16, 8, 1, 110, "enumerable"], [16, 18, 1, 120], [16, 20, 1, 122], [16, 24, 1, 126], [17, 8, 1, 128, "configurable"], [17, 20, 1, 140], [17, 22, 1, 142], [17, 26, 1, 146], [18, 8, 1, 148, "writable"], [18, 16, 1, 156], [18, 18, 1, 158], [19, 6, 1, 163], [19, 7, 1, 164], [19, 8, 1, 165], [20, 4, 1, 167], [20, 5, 1, 168], [20, 11, 1, 174], [21, 6, 1, 176, "obj"], [21, 9, 1, 179], [21, 10, 1, 180, "key"], [21, 13, 1, 183], [21, 14, 1, 184], [21, 17, 1, 187, "value"], [21, 22, 1, 192], [22, 4, 1, 194], [23, 4, 1, 196], [23, 11, 1, 203, "obj"], [23, 14, 1, 206], [24, 2, 1, 208], [25, 2, 9, 0], [25, 8, 9, 6, "PressGestureHandler"], [25, 27, 9, 25], [25, 36, 9, 34, "DiscreteGestureHandler"], [25, 67, 9, 56], [25, 68, 9, 57], [26, 4, 10, 2, "constructor"], [26, 15, 10, 13, "constructor"], [26, 16, 10, 14], [26, 19, 10, 17, "args"], [26, 23, 10, 21], [26, 25, 10, 23], [27, 6, 11, 4], [27, 11, 11, 9], [27, 12, 11, 10], [27, 15, 11, 13, "args"], [27, 19, 11, 17], [27, 20, 11, 18], [28, 6, 13, 4, "_defineProperty"], [28, 21, 13, 19], [28, 22, 13, 20], [28, 26, 13, 24], [28, 28, 13, 26], [28, 49, 13, 47], [28, 51, 13, 49], [28, 56, 13, 54], [28, 57, 13, 55], [28, 58, 13, 56], [29, 6, 15, 4, "_defineProperty"], [29, 21, 15, 19], [29, 22, 15, 20], [29, 26, 15, 24], [29, 28, 15, 26], [29, 42, 15, 40], [29, 44, 15, 42], [29, 48, 15, 46], [29, 49, 15, 47], [30, 6, 17, 4, "_defineProperty"], [30, 21, 17, 19], [30, 22, 17, 20], [30, 26, 17, 24], [30, 28, 17, 26], [30, 48, 17, 46], [30, 50, 17, 48], [30, 54, 17, 52], [30, 55, 17, 53], [31, 4, 18, 2], [32, 4, 20, 2], [32, 8, 20, 6, "name"], [32, 12, 20, 10, "name"], [32, 13, 20, 10], [32, 15, 20, 13], [33, 6, 21, 4], [33, 13, 21, 11], [33, 20, 21, 18], [34, 4, 22, 2], [35, 4, 24, 2], [35, 8, 24, 6, "minDurationMs"], [35, 21, 24, 19, "minDurationMs"], [35, 22, 24, 19], [35, 24, 24, 22], [36, 6, 25, 4], [37, 6, 26, 4], [37, 13, 26, 11], [37, 17, 26, 11, "isnan"], [37, 29, 26, 16], [37, 31, 26, 17], [37, 35, 26, 21], [37, 36, 26, 22, "config"], [37, 42, 26, 28], [37, 43, 26, 29, "minDurationMs"], [37, 56, 26, 42], [37, 57, 26, 43], [37, 60, 26, 46], [37, 61, 26, 47], [37, 64, 26, 50], [37, 68, 26, 54], [37, 69, 26, 55, "config"], [37, 75, 26, 61], [37, 76, 26, 62, "minDurationMs"], [37, 89, 26, 75], [38, 4, 27, 2], [39, 4, 29, 2], [39, 8, 29, 6, "maxDist"], [39, 15, 29, 13, "maxDist"], [39, 16, 29, 13], [39, 18, 29, 16], [40, 6, 30, 4], [40, 13, 30, 11], [40, 17, 30, 11, "isnan"], [40, 29, 30, 16], [40, 31, 30, 17], [40, 35, 30, 21], [40, 36, 30, 22, "config"], [40, 42, 30, 28], [40, 43, 30, 29, "maxDist"], [40, 50, 30, 36], [40, 51, 30, 37], [40, 54, 30, 40], [40, 55, 30, 41], [40, 58, 30, 44], [40, 62, 30, 48], [40, 63, 30, 49, "config"], [40, 69, 30, 55], [40, 70, 30, 56, "maxDist"], [40, 77, 30, 63], [41, 4, 31, 2], [42, 4, 33, 2], [42, 8, 33, 6, "NativeGestureClass"], [42, 26, 33, 24, "NativeGestureClass"], [42, 27, 33, 24], [42, 29, 33, 27], [43, 6, 34, 4], [43, 13, 34, 11, "Hammer"], [43, 30, 34, 17], [43, 31, 34, 18, "Press"], [43, 36, 34, 23], [44, 4, 35, 2], [45, 4, 37, 2, "simulateCancelEvent"], [45, 23, 37, 21, "simulateCancelEvent"], [45, 24, 37, 22, "inputData"], [45, 33, 37, 31], [45, 35, 37, 33], [46, 6, 38, 4], [47, 6, 39, 4], [47, 10, 39, 8], [47, 11, 39, 9, "hasGestureFailed"], [47, 27, 39, 25], [47, 30, 39, 28], [47, 34, 39, 32], [48, 6, 40, 4], [48, 10, 40, 8], [48, 11, 40, 9, "cancelEvent"], [48, 22, 40, 20], [48, 23, 40, 21, "inputData"], [48, 32, 40, 30], [48, 33, 40, 31], [49, 4, 41, 2], [50, 4, 43, 2, "updateHasCustomActivationCriteria"], [50, 37, 43, 35, "updateHasCustomActivationCriteria"], [50, 38, 43, 36], [51, 6, 44, 4, "shouldCancelWhenOutside"], [51, 29, 44, 27], [52, 6, 45, 4, "maxDistSq"], [53, 4, 46, 2], [53, 5, 46, 3], [53, 7, 46, 5], [54, 6, 47, 4], [54, 13, 47, 11, "shouldCancelWhenOutside"], [54, 36, 47, 34], [54, 40, 47, 38], [54, 41, 47, 39], [54, 45, 47, 39, "isValidNumber"], [54, 65, 47, 52], [54, 67, 47, 53, "maxDistSq"], [54, 76, 47, 62], [54, 77, 47, 63], [55, 4, 48, 2], [56, 4, 50, 2, "getState"], [56, 12, 50, 10, "getState"], [56, 13, 50, 11, "type"], [56, 17, 50, 15], [56, 19, 50, 17], [57, 6, 51, 4], [57, 13, 51, 11], [58, 8, 52, 6], [58, 9, 52, 7, "Hammer"], [58, 26, 52, 13], [58, 27, 52, 14, "INPUT_START"], [58, 38, 52, 25], [58, 41, 52, 28, "State"], [58, 53, 52, 33], [58, 54, 52, 34, "BEGAN"], [58, 59, 52, 39], [59, 8, 53, 6], [59, 9, 53, 7, "Hammer"], [59, 26, 53, 13], [59, 27, 53, 14, "INPUT_MOVE"], [59, 37, 53, 24], [59, 40, 53, 27, "State"], [59, 52, 53, 32], [59, 53, 53, 33, "ACTIVE"], [59, 59, 53, 39], [60, 8, 54, 6], [60, 9, 54, 7, "Hammer"], [60, 26, 54, 13], [60, 27, 54, 14, "INPUT_END"], [60, 36, 54, 23], [60, 39, 54, 26, "State"], [60, 51, 54, 31], [60, 52, 54, 32, "END"], [60, 55, 54, 35], [61, 8, 55, 6], [61, 9, 55, 7, "Hammer"], [61, 26, 55, 13], [61, 27, 55, 14, "INPUT_CANCEL"], [61, 39, 55, 26], [61, 42, 55, 29, "State"], [61, 54, 55, 34], [61, 55, 55, 35, "CANCELLED"], [62, 6, 56, 4], [62, 7, 56, 5], [62, 8, 56, 6, "type"], [62, 12, 56, 10], [62, 13, 56, 11], [63, 4, 57, 2], [64, 4, 59, 2, "getConfig"], [64, 13, 59, 11, "getConfig"], [64, 14, 59, 11], [64, 16, 59, 14], [65, 6, 60, 4], [65, 10, 60, 8], [65, 11, 60, 9], [65, 15, 60, 13], [65, 16, 60, 14, "hasCustomActivationCriteria"], [65, 43, 60, 41], [65, 45, 60, 43], [66, 8, 61, 6], [67, 8, 62, 6], [68, 8, 63, 6], [68, 15, 63, 13], [69, 10, 64, 8, "shouldCancelWhenOutside"], [69, 33, 64, 31], [69, 35, 64, 33], [69, 39, 64, 37], [70, 10, 65, 8, "maxDistSq"], [70, 19, 65, 17], [70, 21, 65, 19], [71, 8, 66, 6], [71, 9, 66, 7], [72, 6, 67, 4], [73, 6, 69, 4], [73, 13, 69, 11], [73, 17, 69, 15], [73, 18, 69, 16, "config"], [73, 24, 69, 22], [74, 4, 70, 2], [75, 4, 72, 2, "getHammerConfig"], [75, 19, 72, 17, "getHammerConfig"], [75, 20, 72, 17], [75, 22, 72, 20], [76, 6, 73, 4], [76, 13, 73, 11], [77, 8, 73, 13], [77, 11, 73, 16], [77, 16, 73, 21], [77, 17, 73, 22, "getHammerConfig"], [77, 32, 73, 37], [77, 33, 73, 38], [77, 34, 73, 39], [78, 8, 74, 6], [79, 8, 75, 6, "time"], [79, 12, 75, 10], [79, 14, 75, 12], [79, 18, 75, 16], [79, 19, 75, 17, "minDurationMs"], [80, 6, 76, 4], [80, 7, 76, 5], [81, 4, 77, 2], [82, 4, 79, 2, "onGestureActivated"], [82, 22, 79, 20, "onGestureActivated"], [82, 23, 79, 21, "ev"], [82, 25, 79, 23], [82, 27, 79, 25], [83, 6, 80, 4], [83, 10, 80, 8], [83, 11, 80, 9, "onGestureStart"], [83, 25, 80, 23], [83, 26, 80, 24, "ev"], [83, 28, 80, 26], [83, 29, 80, 27], [84, 4, 81, 2], [85, 4, 83, 2, "shouldDelayTouchForEvent"], [85, 28, 83, 26, "shouldDelayTouchForEvent"], [85, 29, 83, 27], [86, 6, 84, 4, "pointerType"], [87, 4, 85, 2], [87, 5, 85, 3], [87, 7, 85, 5], [88, 6, 86, 4], [89, 6, 87, 4], [89, 13, 87, 11], [89, 17, 87, 15], [89, 18, 87, 16, "shouldDelayTouches"], [89, 36, 87, 34], [89, 40, 87, 38, "pointerType"], [89, 51, 87, 49], [89, 56, 87, 54], [89, 63, 87, 61], [90, 4, 88, 2], [91, 4, 90, 2, "onGestureStart"], [91, 18, 90, 16, "onGestureStart"], [91, 19, 90, 17, "ev"], [91, 21, 90, 19], [91, 23, 90, 21], [92, 6, 91, 4], [92, 10, 91, 8], [92, 11, 91, 9, "isGestureRunning"], [92, 27, 91, 25], [92, 30, 91, 28], [92, 34, 91, 32], [93, 6, 92, 4, "clearTimeout"], [93, 18, 92, 16], [93, 19, 92, 17], [93, 23, 92, 21], [93, 24, 92, 22, "visualFeedbackTimer"], [93, 43, 92, 41], [93, 44, 92, 42], [94, 6, 93, 4], [94, 10, 93, 8], [94, 11, 93, 9, "initialEvent"], [94, 23, 93, 21], [94, 26, 93, 24, "ev"], [94, 28, 93, 26], [95, 6, 94, 4], [95, 10, 94, 8], [95, 11, 94, 9, "visualFeedbackTimer"], [95, 30, 94, 28], [95, 33, 94, 31], [95, 37, 94, 31, "fireAfterInterval"], [95, 61, 94, 48], [95, 63, 94, 49], [95, 69, 94, 55], [96, 8, 95, 6], [96, 12, 95, 10], [96, 13, 95, 11, "sendGestureStartedEvent"], [96, 36, 95, 34], [96, 37, 95, 35], [96, 41, 95, 39], [96, 42, 95, 40, "initialEvent"], [96, 54, 95, 52], [96, 55, 95, 53], [97, 8, 96, 6], [97, 12, 96, 10], [97, 13, 96, 11, "initialEvent"], [97, 25, 96, 23], [97, 28, 96, 26], [97, 32, 96, 30], [98, 6, 97, 4], [98, 7, 97, 5], [98, 9, 97, 7], [98, 13, 97, 11], [98, 14, 97, 12, "shouldDelayTouchForEvent"], [98, 38, 97, 36], [98, 39, 97, 37, "ev"], [98, 41, 97, 39], [98, 42, 97, 40], [98, 46, 97, 44, "CONTENT_TOUCHES_DELAY"], [98, 78, 97, 65], [98, 79, 97, 66], [99, 4, 98, 2], [100, 4, 100, 2, "sendGestureStartedEvent"], [100, 27, 100, 25, "sendGestureStartedEvent"], [100, 28, 100, 26, "ev"], [100, 30, 100, 28], [100, 32, 100, 30], [101, 6, 101, 4, "clearTimeout"], [101, 18, 101, 16], [101, 19, 101, 17], [101, 23, 101, 21], [101, 24, 101, 22, "visualFeedbackTimer"], [101, 43, 101, 41], [101, 44, 101, 42], [102, 6, 102, 4], [102, 10, 102, 8], [102, 11, 102, 9, "visualFeedbackTimer"], [102, 30, 102, 28], [102, 33, 102, 31], [102, 37, 102, 35], [103, 6, 103, 4], [103, 10, 103, 8], [103, 11, 103, 9, "sendEvent"], [103, 20, 103, 18], [103, 21, 103, 19], [104, 8, 103, 21], [104, 11, 103, 24, "ev"], [104, 13, 103, 26], [105, 8, 104, 6, "eventType"], [105, 17, 104, 15], [105, 19, 104, 17, "Hammer"], [105, 36, 104, 23], [105, 37, 104, 24, "INPUT_MOVE"], [105, 47, 104, 34], [106, 8, 105, 6, "<PERSON><PERSON><PERSON><PERSON>"], [106, 15, 105, 13], [106, 17, 105, 15], [107, 6, 106, 4], [107, 7, 106, 5], [107, 8, 106, 6], [108, 4, 107, 2], [109, 4, 109, 2, "forceInvalidate"], [109, 19, 109, 17, "forceInvalidate"], [109, 20, 109, 18, "event"], [109, 25, 109, 23], [109, 27, 109, 25], [110, 6, 110, 4], [110, 11, 110, 9], [110, 12, 110, 10, "forceInvalidate"], [110, 27, 110, 25], [110, 28, 110, 26, "event"], [110, 33, 110, 31], [110, 34, 110, 32], [111, 6, 111, 4, "clearTimeout"], [111, 18, 111, 16], [111, 19, 111, 17], [111, 23, 111, 21], [111, 24, 111, 22, "visualFeedbackTimer"], [111, 43, 111, 41], [111, 44, 111, 42], [112, 6, 112, 4], [112, 10, 112, 8], [112, 11, 112, 9, "visualFeedbackTimer"], [112, 30, 112, 28], [112, 33, 112, 31], [112, 37, 112, 35], [113, 6, 113, 4], [113, 10, 113, 8], [113, 11, 113, 9, "initialEvent"], [113, 23, 113, 21], [113, 26, 113, 24], [113, 30, 113, 28], [114, 4, 114, 2], [115, 4, 116, 2, "onRawEvent"], [115, 14, 116, 12, "onRawEvent"], [115, 15, 116, 13, "ev"], [115, 17, 116, 15], [115, 19, 116, 17], [116, 6, 117, 4], [116, 11, 117, 9], [116, 12, 117, 10, "onRawEvent"], [116, 22, 117, 20], [116, 23, 117, 21, "ev"], [116, 25, 117, 23], [116, 26, 117, 24], [117, 6, 119, 4], [117, 10, 119, 8], [117, 14, 119, 12], [117, 15, 119, 13, "isGestureRunning"], [117, 31, 119, 29], [117, 33, 119, 31], [118, 8, 120, 6], [118, 12, 120, 10, "ev"], [118, 14, 120, 12], [118, 15, 120, 13, "isFinal"], [118, 22, 120, 20], [118, 24, 120, 22], [119, 10, 121, 8], [119, 14, 121, 12, "timeout"], [119, 21, 121, 19], [120, 10, 123, 8], [120, 14, 123, 12], [120, 18, 123, 16], [120, 19, 123, 17, "visualFeedbackTimer"], [120, 38, 123, 36], [120, 40, 123, 38], [121, 12, 124, 10], [122, 12, 125, 10], [123, 12, 126, 10], [124, 12, 127, 10, "timeout"], [124, 19, 127, 17], [124, 22, 127, 20, "CONTENT_TOUCHES_QUICK_TAP_END_DELAY"], [124, 68, 127, 55], [125, 12, 128, 10], [125, 16, 128, 14], [125, 17, 128, 15, "sendGestureStartedEvent"], [125, 40, 128, 38], [125, 41, 128, 39], [125, 45, 128, 43], [125, 46, 128, 44, "initialEvent"], [125, 58, 128, 56], [125, 59, 128, 57], [126, 12, 129, 10], [126, 16, 129, 14], [126, 17, 129, 15, "initialEvent"], [126, 29, 129, 27], [126, 32, 129, 30], [126, 36, 129, 34], [127, 10, 130, 8], [128, 10, 132, 8], [128, 14, 132, 8, "fireAfterInterval"], [128, 38, 132, 25], [128, 40, 132, 26], [128, 46, 132, 32], [129, 12, 133, 10], [129, 16, 133, 14], [129, 17, 133, 15, "sendEvent"], [129, 26, 133, 24], [129, 27, 133, 25], [130, 14, 133, 27], [130, 17, 133, 30, "ev"], [130, 19, 133, 32], [131, 14, 134, 12, "eventType"], [131, 23, 134, 21], [131, 25, 134, 23, "Hammer"], [131, 42, 134, 29], [131, 43, 134, 30, "INPUT_END"], [131, 52, 134, 39], [132, 14, 135, 12, "isFinal"], [132, 21, 135, 19], [132, 23, 135, 21], [133, 12, 136, 10], [133, 13, 136, 11], [133, 14, 136, 12], [133, 15, 136, 13], [133, 16, 136, 14], [135, 12, 138, 10], [135, 16, 138, 14], [135, 17, 138, 15, "onGestureEnded"], [135, 31, 138, 29], [135, 32, 138, 30], [135, 33, 138, 31], [136, 10, 139, 8], [136, 11, 139, 9], [136, 13, 139, 11, "timeout"], [136, 20, 139, 18], [136, 21, 139, 19], [137, 8, 140, 6], [137, 9, 140, 7], [137, 15, 140, 13], [138, 10, 141, 8], [138, 14, 141, 12], [138, 15, 141, 13, "sendEvent"], [138, 24, 141, 22], [138, 25, 141, 23], [139, 12, 141, 25], [139, 15, 141, 28, "ev"], [139, 17, 141, 30], [140, 12, 142, 10, "eventType"], [140, 21, 142, 19], [140, 23, 142, 21, "Hammer"], [140, 40, 142, 27], [140, 41, 142, 28, "INPUT_MOVE"], [140, 51, 142, 38], [141, 12, 143, 10, "isFinal"], [141, 19, 143, 17], [141, 21, 143, 19], [142, 10, 144, 8], [142, 11, 144, 9], [142, 12, 144, 10], [143, 8, 145, 6], [144, 6, 146, 4], [145, 4, 147, 2], [146, 4, 149, 2, "updateGestureConfig"], [146, 23, 149, 21, "updateGestureConfig"], [146, 24, 149, 22], [147, 6, 150, 4, "shouldActivateOnStart"], [147, 27, 150, 25], [147, 30, 150, 28], [147, 35, 150, 33], [148, 6, 151, 4, "disallowInterruption"], [148, 26, 151, 24], [148, 29, 151, 27], [148, 34, 151, 32], [149, 6, 152, 4, "shouldCancelWhenOutside"], [149, 29, 152, 27], [149, 32, 152, 30], [149, 36, 152, 34], [150, 6, 153, 4, "minDurationMs"], [150, 19, 153, 17], [150, 22, 153, 20, "Number"], [150, 28, 153, 26], [150, 29, 153, 27, "NaN"], [150, 32, 153, 30], [151, 6, 154, 4, "maxDist"], [151, 13, 154, 11], [151, 16, 154, 14, "Number"], [151, 22, 154, 20], [151, 23, 154, 21, "NaN"], [151, 26, 154, 24], [152, 6, 155, 4, "minPointers"], [152, 17, 155, 15], [152, 20, 155, 18], [152, 21, 155, 19], [153, 6, 156, 4, "maxPointers"], [153, 17, 156, 15], [153, 20, 156, 18], [153, 21, 156, 19], [154, 6, 157, 4], [154, 9, 157, 7, "props"], [155, 4, 158, 2], [155, 5, 158, 3], [155, 7, 158, 5], [156, 6, 159, 4], [156, 13, 159, 11], [156, 18, 159, 16], [156, 19, 159, 17, "updateGestureConfig"], [156, 38, 159, 36], [156, 39, 159, 37], [157, 8, 160, 6, "shouldActivateOnStart"], [157, 29, 160, 27], [158, 8, 161, 6, "disallowInterruption"], [158, 28, 161, 26], [159, 8, 162, 6, "shouldCancelWhenOutside"], [159, 31, 162, 29], [160, 8, 163, 6, "minDurationMs"], [160, 21, 163, 19], [161, 8, 164, 6, "maxDist"], [161, 15, 164, 13], [162, 8, 165, 6, "minPointers"], [162, 19, 165, 17], [163, 8, 166, 6, "maxPointers"], [163, 19, 166, 17], [164, 8, 167, 6], [164, 11, 167, 9, "props"], [165, 6, 168, 4], [165, 7, 168, 5], [165, 8, 168, 6], [166, 4, 169, 2], [167, 2, 171, 0], [168, 2, 171, 1], [168, 6, 171, 1, "_default"], [168, 14, 171, 1], [168, 17, 171, 1, "exports"], [168, 24, 171, 1], [168, 25, 171, 1, "default"], [168, 32, 171, 1], [168, 35, 173, 15, "PressGestureHandler"], [168, 54, 173, 34], [169, 0, 173, 34], [169, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "PressGestureHandler", "constructor", "get__name", "get__minDurationMs", "get__maxDist", "get__NativeGestureClass", "simulateCancelEvent", "updateHasCustomActivationCriteria", "getState", "getConfig", "getHammerConfig", "onGestureActivated", "shouldDelayTouchForEvent", "onGestureStart", "fireAfterInterval$argument_0", "sendGestureStartedEvent", "forceInvalidate", "onRawEvent", "updateGestureConfig"], "mappings": "AAA,iNC;ACQ;ECC;GDQ;EEE;GFE;EGE;GHG;EIE;GJE;EKE;GLE;EME;GNI;EOE;GPK;EQE;GRO;ESE;GTW;EUE;GVK;EWE;GXE;EYE;GZK;EaE;iDCI;KDG;GbC;EeE;GfO;EgBE;GhBK;EiBE;0BHgB;SGO;GjBQ;EkBE;GlBoB;CDE"}}, "type": "js/module"}]}