{"dependencies": [{"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 65, "index": 97}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.hasNextPage = hasNextPage;\n  exports.hasPreviousPage = hasPreviousPage;\n  exports.infiniteQueryBehavior = infiniteQueryBehavior;\n  var _utils = require(_dependencyMap[0], \"./utils.js\");\n  // src/infiniteQueryBehavior.ts\n\n  function infiniteQueryBehavior(pages) {\n    return {\n      onFetch: (context, query) => {\n        const options = context.options;\n        const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n        const oldPages = context.state.data?.pages || [];\n        const oldPageParams = context.state.data?.pageParams || [];\n        let result = {\n          pages: [],\n          pageParams: []\n        };\n        let currentPage = 0;\n        const fetchFn = async () => {\n          let cancelled = false;\n          const addSignalProperty = object => {\n            Object.defineProperty(object, \"signal\", {\n              enumerable: true,\n              get: () => {\n                if (context.signal.aborted) {\n                  cancelled = true;\n                } else {\n                  context.signal.addEventListener(\"abort\", () => {\n                    cancelled = true;\n                  });\n                }\n                return context.signal;\n              }\n            });\n          };\n          const queryFn = (0, _utils.ensureQueryFn)(context.options, context.fetchOptions);\n          const fetchPage = async (data, param, previous) => {\n            if (cancelled) {\n              return Promise.reject();\n            }\n            if (param == null && data.pages.length) {\n              return Promise.resolve(data);\n            }\n            const createQueryFnContext = () => {\n              const queryFnContext2 = {\n                client: context.client,\n                queryKey: context.queryKey,\n                pageParam: param,\n                direction: previous ? \"backward\" : \"forward\",\n                meta: context.options.meta\n              };\n              addSignalProperty(queryFnContext2);\n              return queryFnContext2;\n            };\n            const queryFnContext = createQueryFnContext();\n            const page = await queryFn(queryFnContext);\n            const {\n              maxPages\n            } = context.options;\n            const addTo = previous ? _utils.addToStart : _utils.addToEnd;\n            return {\n              pages: addTo(data.pages, page, maxPages),\n              pageParams: addTo(data.pageParams, param, maxPages)\n            };\n          };\n          if (direction && oldPages.length) {\n            const previous = direction === \"backward\";\n            const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n            const oldData = {\n              pages: oldPages,\n              pageParams: oldPageParams\n            };\n            const param = pageParamFn(options, oldData);\n            result = await fetchPage(oldData, param, previous);\n          } else {\n            const remainingPages = pages ?? oldPages.length;\n            do {\n              const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n              if (currentPage > 0 && param == null) {\n                break;\n              }\n              result = await fetchPage(result, param);\n              currentPage++;\n            } while (currentPage < remainingPages);\n          }\n          return result;\n        };\n        if (context.options.persister) {\n          context.fetchFn = () => {\n            return context.options.persister?.(fetchFn, {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            }, query);\n          };\n        } else {\n          context.fetchFn = fetchFn;\n        }\n      }\n    };\n  }\n  function getNextPageParam(options, {\n    pages,\n    pageParams\n  }) {\n    const lastIndex = pages.length - 1;\n    return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n  }\n  function getPreviousPageParam(options, {\n    pages,\n    pageParams\n  }) {\n    return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n  }\n  function hasNextPage(options, data) {\n    if (!data) return false;\n    return getNextPageParam(options, data) != null;\n  }\n  function hasPreviousPage(options, data) {\n    if (!data || !options.getPreviousPageParam) return false;\n    return getPreviousPageParam(options, data) != null;\n  }\n});", "lineCount": 128, "map": [[8, 2, 2, 0], [8, 6, 2, 0, "_utils"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 1, 0], [11, 2, 3, 0], [11, 11, 3, 9, "infiniteQueryBehavior"], [11, 32, 3, 30, "infiniteQueryBehavior"], [11, 33, 3, 31, "pages"], [11, 38, 3, 36], [11, 40, 3, 38], [12, 4, 4, 2], [12, 11, 4, 9], [13, 6, 5, 4, "onFetch"], [13, 13, 5, 11], [13, 15, 5, 13, "onFetch"], [13, 16, 5, 14, "context"], [13, 23, 5, 21], [13, 25, 5, 23, "query"], [13, 30, 5, 28], [13, 35, 5, 33], [14, 8, 6, 6], [14, 14, 6, 12, "options"], [14, 21, 6, 19], [14, 24, 6, 22, "context"], [14, 31, 6, 29], [14, 32, 6, 30, "options"], [14, 39, 6, 37], [15, 8, 7, 6], [15, 14, 7, 12, "direction"], [15, 23, 7, 21], [15, 26, 7, 24, "context"], [15, 33, 7, 31], [15, 34, 7, 32, "fetchOptions"], [15, 46, 7, 44], [15, 48, 7, 46, "meta"], [15, 52, 7, 50], [15, 54, 7, 52, "fetchMore"], [15, 63, 7, 61], [15, 65, 7, 63, "direction"], [15, 74, 7, 72], [16, 8, 8, 6], [16, 14, 8, 12, "oldPages"], [16, 22, 8, 20], [16, 25, 8, 23, "context"], [16, 32, 8, 30], [16, 33, 8, 31, "state"], [16, 38, 8, 36], [16, 39, 8, 37, "data"], [16, 43, 8, 41], [16, 45, 8, 43, "pages"], [16, 50, 8, 48], [16, 54, 8, 52], [16, 56, 8, 54], [17, 8, 9, 6], [17, 14, 9, 12, "oldPageParams"], [17, 27, 9, 25], [17, 30, 9, 28, "context"], [17, 37, 9, 35], [17, 38, 9, 36, "state"], [17, 43, 9, 41], [17, 44, 9, 42, "data"], [17, 48, 9, 46], [17, 50, 9, 48, "pageParams"], [17, 60, 9, 58], [17, 64, 9, 62], [17, 66, 9, 64], [18, 8, 10, 6], [18, 12, 10, 10, "result"], [18, 18, 10, 16], [18, 21, 10, 19], [19, 10, 10, 21, "pages"], [19, 15, 10, 26], [19, 17, 10, 28], [19, 19, 10, 30], [20, 10, 10, 32, "pageParams"], [20, 20, 10, 42], [20, 22, 10, 44], [21, 8, 10, 47], [21, 9, 10, 48], [22, 8, 11, 6], [22, 12, 11, 10, "currentPage"], [22, 23, 11, 21], [22, 26, 11, 24], [22, 27, 11, 25], [23, 8, 12, 6], [23, 14, 12, 12, "fetchFn"], [23, 21, 12, 19], [23, 24, 12, 22], [23, 30, 12, 22, "fetchFn"], [23, 31, 12, 22], [23, 36, 12, 34], [24, 10, 13, 8], [24, 14, 13, 12, "cancelled"], [24, 23, 13, 21], [24, 26, 13, 24], [24, 31, 13, 29], [25, 10, 14, 8], [25, 16, 14, 14, "addSignalProperty"], [25, 33, 14, 31], [25, 36, 14, 35, "object"], [25, 42, 14, 41], [25, 46, 14, 46], [26, 12, 15, 10, "Object"], [26, 18, 15, 16], [26, 19, 15, 17, "defineProperty"], [26, 33, 15, 31], [26, 34, 15, 32, "object"], [26, 40, 15, 38], [26, 42, 15, 40], [26, 50, 15, 48], [26, 52, 15, 50], [27, 14, 16, 12, "enumerable"], [27, 24, 16, 22], [27, 26, 16, 24], [27, 30, 16, 28], [28, 14, 17, 12, "get"], [28, 17, 17, 15], [28, 19, 17, 17, "get"], [28, 20, 17, 17], [28, 25, 17, 23], [29, 16, 18, 14], [29, 20, 18, 18, "context"], [29, 27, 18, 25], [29, 28, 18, 26, "signal"], [29, 34, 18, 32], [29, 35, 18, 33, "aborted"], [29, 42, 18, 40], [29, 44, 18, 42], [30, 18, 19, 16, "cancelled"], [30, 27, 19, 25], [30, 30, 19, 28], [30, 34, 19, 32], [31, 16, 20, 14], [31, 17, 20, 15], [31, 23, 20, 21], [32, 18, 21, 16, "context"], [32, 25, 21, 23], [32, 26, 21, 24, "signal"], [32, 32, 21, 30], [32, 33, 21, 31, "addEventListener"], [32, 49, 21, 47], [32, 50, 21, 48], [32, 57, 21, 55], [32, 59, 21, 57], [32, 65, 21, 63], [33, 20, 22, 18, "cancelled"], [33, 29, 22, 27], [33, 32, 22, 30], [33, 36, 22, 34], [34, 18, 23, 16], [34, 19, 23, 17], [34, 20, 23, 18], [35, 16, 24, 14], [36, 16, 25, 14], [36, 23, 25, 21, "context"], [36, 30, 25, 28], [36, 31, 25, 29, "signal"], [36, 37, 25, 35], [37, 14, 26, 12], [38, 12, 27, 10], [38, 13, 27, 11], [38, 14, 27, 12], [39, 10, 28, 8], [39, 11, 28, 9], [40, 10, 29, 8], [40, 16, 29, 14, "queryFn"], [40, 23, 29, 21], [40, 26, 29, 24], [40, 30, 29, 24, "ensureQueryFn"], [40, 50, 29, 37], [40, 52, 29, 38, "context"], [40, 59, 29, 45], [40, 60, 29, 46, "options"], [40, 67, 29, 53], [40, 69, 29, 55, "context"], [40, 76, 29, 62], [40, 77, 29, 63, "fetchOptions"], [40, 89, 29, 75], [40, 90, 29, 76], [41, 10, 30, 8], [41, 16, 30, 14, "fetchPage"], [41, 25, 30, 23], [41, 28, 30, 26], [41, 34, 30, 26, "fetchPage"], [41, 35, 30, 33, "data"], [41, 39, 30, 37], [41, 41, 30, 39, "param"], [41, 46, 30, 44], [41, 48, 30, 46, "previous"], [41, 56, 30, 54], [41, 61, 30, 59], [42, 12, 31, 10], [42, 16, 31, 14, "cancelled"], [42, 25, 31, 23], [42, 27, 31, 25], [43, 14, 32, 12], [43, 21, 32, 19, "Promise"], [43, 28, 32, 26], [43, 29, 32, 27, "reject"], [43, 35, 32, 33], [43, 36, 32, 34], [43, 37, 32, 35], [44, 12, 33, 10], [45, 12, 34, 10], [45, 16, 34, 14, "param"], [45, 21, 34, 19], [45, 25, 34, 23], [45, 29, 34, 27], [45, 33, 34, 31, "data"], [45, 37, 34, 35], [45, 38, 34, 36, "pages"], [45, 43, 34, 41], [45, 44, 34, 42, "length"], [45, 50, 34, 48], [45, 52, 34, 50], [46, 14, 35, 12], [46, 21, 35, 19, "Promise"], [46, 28, 35, 26], [46, 29, 35, 27, "resolve"], [46, 36, 35, 34], [46, 37, 35, 35, "data"], [46, 41, 35, 39], [46, 42, 35, 40], [47, 12, 36, 10], [48, 12, 37, 10], [48, 18, 37, 16, "createQueryFnContext"], [48, 38, 37, 36], [48, 41, 37, 39, "createQueryFnContext"], [48, 42, 37, 39], [48, 47, 37, 45], [49, 14, 38, 12], [49, 20, 38, 18, "queryFnContext2"], [49, 35, 38, 33], [49, 38, 38, 36], [50, 16, 39, 14, "client"], [50, 22, 39, 20], [50, 24, 39, 22, "context"], [50, 31, 39, 29], [50, 32, 39, 30, "client"], [50, 38, 39, 36], [51, 16, 40, 14, "query<PERSON><PERSON>"], [51, 24, 40, 22], [51, 26, 40, 24, "context"], [51, 33, 40, 31], [51, 34, 40, 32, "query<PERSON><PERSON>"], [51, 42, 40, 40], [52, 16, 41, 14, "pageParam"], [52, 25, 41, 23], [52, 27, 41, 25, "param"], [52, 32, 41, 30], [53, 16, 42, 14, "direction"], [53, 25, 42, 23], [53, 27, 42, 25, "previous"], [53, 35, 42, 33], [53, 38, 42, 36], [53, 48, 42, 46], [53, 51, 42, 49], [53, 60, 42, 58], [54, 16, 43, 14, "meta"], [54, 20, 43, 18], [54, 22, 43, 20, "context"], [54, 29, 43, 27], [54, 30, 43, 28, "options"], [54, 37, 43, 35], [54, 38, 43, 36, "meta"], [55, 14, 44, 12], [55, 15, 44, 13], [56, 14, 45, 12, "addSignalProperty"], [56, 31, 45, 29], [56, 32, 45, 30, "queryFnContext2"], [56, 47, 45, 45], [56, 48, 45, 46], [57, 14, 46, 12], [57, 21, 46, 19, "queryFnContext2"], [57, 36, 46, 34], [58, 12, 47, 10], [58, 13, 47, 11], [59, 12, 48, 10], [59, 18, 48, 16, "queryFnContext"], [59, 32, 48, 30], [59, 35, 48, 33, "createQueryFnContext"], [59, 55, 48, 53], [59, 56, 48, 54], [59, 57, 48, 55], [60, 12, 49, 10], [60, 18, 49, 16, "page"], [60, 22, 49, 20], [60, 25, 49, 23], [60, 31, 49, 29, "queryFn"], [60, 38, 49, 36], [60, 39, 49, 37, "queryFnContext"], [60, 53, 49, 51], [60, 54, 49, 52], [61, 12, 50, 10], [61, 18, 50, 16], [62, 14, 50, 18, "maxPages"], [63, 12, 50, 27], [63, 13, 50, 28], [63, 16, 50, 31, "context"], [63, 23, 50, 38], [63, 24, 50, 39, "options"], [63, 31, 50, 46], [64, 12, 51, 10], [64, 18, 51, 16, "addTo"], [64, 23, 51, 21], [64, 26, 51, 24, "previous"], [64, 34, 51, 32], [64, 37, 51, 35, "addToStart"], [64, 54, 51, 45], [64, 57, 51, 48, "addToEnd"], [64, 72, 51, 56], [65, 12, 52, 10], [65, 19, 52, 17], [66, 14, 53, 12, "pages"], [66, 19, 53, 17], [66, 21, 53, 19, "addTo"], [66, 26, 53, 24], [66, 27, 53, 25, "data"], [66, 31, 53, 29], [66, 32, 53, 30, "pages"], [66, 37, 53, 35], [66, 39, 53, 37, "page"], [66, 43, 53, 41], [66, 45, 53, 43, "maxPages"], [66, 53, 53, 51], [66, 54, 53, 52], [67, 14, 54, 12, "pageParams"], [67, 24, 54, 22], [67, 26, 54, 24, "addTo"], [67, 31, 54, 29], [67, 32, 54, 30, "data"], [67, 36, 54, 34], [67, 37, 54, 35, "pageParams"], [67, 47, 54, 45], [67, 49, 54, 47, "param"], [67, 54, 54, 52], [67, 56, 54, 54, "maxPages"], [67, 64, 54, 62], [68, 12, 55, 10], [68, 13, 55, 11], [69, 10, 56, 8], [69, 11, 56, 9], [70, 10, 57, 8], [70, 14, 57, 12, "direction"], [70, 23, 57, 21], [70, 27, 57, 25, "oldPages"], [70, 35, 57, 33], [70, 36, 57, 34, "length"], [70, 42, 57, 40], [70, 44, 57, 42], [71, 12, 58, 10], [71, 18, 58, 16, "previous"], [71, 26, 58, 24], [71, 29, 58, 27, "direction"], [71, 38, 58, 36], [71, 43, 58, 41], [71, 53, 58, 51], [72, 12, 59, 10], [72, 18, 59, 16, "pageParamFn"], [72, 29, 59, 27], [72, 32, 59, 30, "previous"], [72, 40, 59, 38], [72, 43, 59, 41, "getPreviousPageParam"], [72, 63, 59, 61], [72, 66, 59, 64, "getNextPageParam"], [72, 82, 59, 80], [73, 12, 60, 10], [73, 18, 60, 16, "oldData"], [73, 25, 60, 23], [73, 28, 60, 26], [74, 14, 61, 12, "pages"], [74, 19, 61, 17], [74, 21, 61, 19, "oldPages"], [74, 29, 61, 27], [75, 14, 62, 12, "pageParams"], [75, 24, 62, 22], [75, 26, 62, 24, "oldPageParams"], [76, 12, 63, 10], [76, 13, 63, 11], [77, 12, 64, 10], [77, 18, 64, 16, "param"], [77, 23, 64, 21], [77, 26, 64, 24, "pageParamFn"], [77, 37, 64, 35], [77, 38, 64, 36, "options"], [77, 45, 64, 43], [77, 47, 64, 45, "oldData"], [77, 54, 64, 52], [77, 55, 64, 53], [78, 12, 65, 10, "result"], [78, 18, 65, 16], [78, 21, 65, 19], [78, 27, 65, 25, "fetchPage"], [78, 36, 65, 34], [78, 37, 65, 35, "oldData"], [78, 44, 65, 42], [78, 46, 65, 44, "param"], [78, 51, 65, 49], [78, 53, 65, 51, "previous"], [78, 61, 65, 59], [78, 62, 65, 60], [79, 10, 66, 8], [79, 11, 66, 9], [79, 17, 66, 15], [80, 12, 67, 10], [80, 18, 67, 16, "remainingPages"], [80, 32, 67, 30], [80, 35, 67, 33, "pages"], [80, 40, 67, 38], [80, 44, 67, 42, "oldPages"], [80, 52, 67, 50], [80, 53, 67, 51, "length"], [80, 59, 67, 57], [81, 12, 68, 10], [81, 15, 68, 13], [82, 14, 69, 12], [82, 20, 69, 18, "param"], [82, 25, 69, 23], [82, 28, 69, 26, "currentPage"], [82, 39, 69, 37], [82, 44, 69, 42], [82, 45, 69, 43], [82, 48, 69, 46, "oldPageParams"], [82, 61, 69, 59], [82, 62, 69, 60], [82, 63, 69, 61], [82, 64, 69, 62], [82, 68, 69, 66, "options"], [82, 75, 69, 73], [82, 76, 69, 74, "initialPageParam"], [82, 92, 69, 90], [82, 95, 69, 93, "getNextPageParam"], [82, 111, 69, 109], [82, 112, 69, 110, "options"], [82, 119, 69, 117], [82, 121, 69, 119, "result"], [82, 127, 69, 125], [82, 128, 69, 126], [83, 14, 70, 12], [83, 18, 70, 16, "currentPage"], [83, 29, 70, 27], [83, 32, 70, 30], [83, 33, 70, 31], [83, 37, 70, 35, "param"], [83, 42, 70, 40], [83, 46, 70, 44], [83, 50, 70, 48], [83, 52, 70, 50], [84, 16, 71, 14], [85, 14, 72, 12], [86, 14, 73, 12, "result"], [86, 20, 73, 18], [86, 23, 73, 21], [86, 29, 73, 27, "fetchPage"], [86, 38, 73, 36], [86, 39, 73, 37, "result"], [86, 45, 73, 43], [86, 47, 73, 45, "param"], [86, 52, 73, 50], [86, 53, 73, 51], [87, 14, 74, 12, "currentPage"], [87, 25, 74, 23], [87, 27, 74, 25], [88, 12, 75, 10], [88, 13, 75, 11], [88, 21, 75, 19, "currentPage"], [88, 32, 75, 30], [88, 35, 75, 33, "remainingPages"], [88, 49, 75, 47], [89, 10, 76, 8], [90, 10, 77, 8], [90, 17, 77, 15, "result"], [90, 23, 77, 21], [91, 8, 78, 6], [91, 9, 78, 7], [92, 8, 79, 6], [92, 12, 79, 10, "context"], [92, 19, 79, 17], [92, 20, 79, 18, "options"], [92, 27, 79, 25], [92, 28, 79, 26, "persister"], [92, 37, 79, 35], [92, 39, 79, 37], [93, 10, 80, 8, "context"], [93, 17, 80, 15], [93, 18, 80, 16, "fetchFn"], [93, 25, 80, 23], [93, 28, 80, 26], [93, 34, 80, 32], [94, 12, 81, 10], [94, 19, 81, 17, "context"], [94, 26, 81, 24], [94, 27, 81, 25, "options"], [94, 34, 81, 32], [94, 35, 81, 33, "persister"], [94, 44, 81, 42], [94, 47, 82, 12, "fetchFn"], [94, 54, 82, 19], [94, 56, 83, 12], [95, 14, 84, 14, "client"], [95, 20, 84, 20], [95, 22, 84, 22, "context"], [95, 29, 84, 29], [95, 30, 84, 30, "client"], [95, 36, 84, 36], [96, 14, 85, 14, "query<PERSON><PERSON>"], [96, 22, 85, 22], [96, 24, 85, 24, "context"], [96, 31, 85, 31], [96, 32, 85, 32, "query<PERSON><PERSON>"], [96, 40, 85, 40], [97, 14, 86, 14, "meta"], [97, 18, 86, 18], [97, 20, 86, 20, "context"], [97, 27, 86, 27], [97, 28, 86, 28, "options"], [97, 35, 86, 35], [97, 36, 86, 36, "meta"], [97, 40, 86, 40], [98, 14, 87, 14, "signal"], [98, 20, 87, 20], [98, 22, 87, 22, "context"], [98, 29, 87, 29], [98, 30, 87, 30, "signal"], [99, 12, 88, 12], [99, 13, 88, 13], [99, 15, 89, 12, "query"], [99, 20, 90, 10], [99, 21, 90, 11], [100, 10, 91, 8], [100, 11, 91, 9], [101, 8, 92, 6], [101, 9, 92, 7], [101, 15, 92, 13], [102, 10, 93, 8, "context"], [102, 17, 93, 15], [102, 18, 93, 16, "fetchFn"], [102, 25, 93, 23], [102, 28, 93, 26, "fetchFn"], [102, 35, 93, 33], [103, 8, 94, 6], [104, 6, 95, 4], [105, 4, 96, 2], [105, 5, 96, 3], [106, 2, 97, 0], [107, 2, 98, 0], [107, 11, 98, 9, "getNextPageParam"], [107, 27, 98, 25, "getNextPageParam"], [107, 28, 98, 26, "options"], [107, 35, 98, 33], [107, 37, 98, 35], [108, 4, 98, 37, "pages"], [108, 9, 98, 42], [109, 4, 98, 44, "pageParams"], [110, 2, 98, 55], [110, 3, 98, 56], [110, 5, 98, 58], [111, 4, 99, 2], [111, 10, 99, 8, "lastIndex"], [111, 19, 99, 17], [111, 22, 99, 20, "pages"], [111, 27, 99, 25], [111, 28, 99, 26, "length"], [111, 34, 99, 32], [111, 37, 99, 35], [111, 38, 99, 36], [112, 4, 100, 2], [112, 11, 100, 9, "pages"], [112, 16, 100, 14], [112, 17, 100, 15, "length"], [112, 23, 100, 21], [112, 26, 100, 24], [112, 27, 100, 25], [112, 30, 100, 28, "options"], [112, 37, 100, 35], [112, 38, 100, 36, "getNextPageParam"], [112, 54, 100, 52], [112, 55, 101, 4, "pages"], [112, 60, 101, 9], [112, 61, 101, 10, "lastIndex"], [112, 70, 101, 19], [112, 71, 101, 20], [112, 73, 102, 4, "pages"], [112, 78, 102, 9], [112, 80, 103, 4, "pageParams"], [112, 90, 103, 14], [112, 91, 103, 15, "lastIndex"], [112, 100, 103, 24], [112, 101, 103, 25], [112, 103, 104, 4, "pageParams"], [112, 113, 105, 2], [112, 114, 105, 3], [112, 117, 105, 6], [112, 122, 105, 11], [112, 123, 105, 12], [113, 2, 106, 0], [114, 2, 107, 0], [114, 11, 107, 9, "getPreviousPageParam"], [114, 31, 107, 29, "getPreviousPageParam"], [114, 32, 107, 30, "options"], [114, 39, 107, 37], [114, 41, 107, 39], [115, 4, 107, 41, "pages"], [115, 9, 107, 46], [116, 4, 107, 48, "pageParams"], [117, 2, 107, 59], [117, 3, 107, 60], [117, 5, 107, 62], [118, 4, 108, 2], [118, 11, 108, 9, "pages"], [118, 16, 108, 14], [118, 17, 108, 15, "length"], [118, 23, 108, 21], [118, 26, 108, 24], [118, 27, 108, 25], [118, 30, 108, 28, "options"], [118, 37, 108, 35], [118, 38, 108, 36, "getPreviousPageParam"], [118, 58, 108, 56], [118, 61, 108, 59, "pages"], [118, 66, 108, 64], [118, 67, 108, 65], [118, 68, 108, 66], [118, 69, 108, 67], [118, 71, 108, 69, "pages"], [118, 76, 108, 74], [118, 78, 108, 76, "pageParams"], [118, 88, 108, 86], [118, 89, 108, 87], [118, 90, 108, 88], [118, 91, 108, 89], [118, 93, 108, 91, "pageParams"], [118, 103, 108, 101], [118, 104, 108, 102], [118, 107, 108, 105], [118, 112, 108, 110], [118, 113, 108, 111], [119, 2, 109, 0], [120, 2, 110, 0], [120, 11, 110, 9, "hasNextPage"], [120, 22, 110, 20, "hasNextPage"], [120, 23, 110, 21, "options"], [120, 30, 110, 28], [120, 32, 110, 30, "data"], [120, 36, 110, 34], [120, 38, 110, 36], [121, 4, 111, 2], [121, 8, 111, 6], [121, 9, 111, 7, "data"], [121, 13, 111, 11], [121, 15, 111, 13], [121, 22, 111, 20], [121, 27, 111, 25], [122, 4, 112, 2], [122, 11, 112, 9, "getNextPageParam"], [122, 27, 112, 25], [122, 28, 112, 26, "options"], [122, 35, 112, 33], [122, 37, 112, 35, "data"], [122, 41, 112, 39], [122, 42, 112, 40], [122, 46, 112, 44], [122, 50, 112, 48], [123, 2, 113, 0], [124, 2, 114, 0], [124, 11, 114, 9, "hasPreviousPage"], [124, 26, 114, 24, "hasPreviousPage"], [124, 27, 114, 25, "options"], [124, 34, 114, 32], [124, 36, 114, 34, "data"], [124, 40, 114, 38], [124, 42, 114, 40], [125, 4, 115, 2], [125, 8, 115, 6], [125, 9, 115, 7, "data"], [125, 13, 115, 11], [125, 17, 115, 15], [125, 18, 115, 16, "options"], [125, 25, 115, 23], [125, 26, 115, 24, "getPreviousPageParam"], [125, 46, 115, 44], [125, 48, 115, 46], [125, 55, 115, 53], [125, 60, 115, 58], [126, 4, 116, 2], [126, 11, 116, 9, "getPreviousPageParam"], [126, 31, 116, 29], [126, 32, 116, 30, "options"], [126, 39, 116, 37], [126, 41, 116, 39, "data"], [126, 45, 116, 43], [126, 46, 116, 44], [126, 50, 116, 48], [126, 54, 116, 52], [127, 2, 117, 0], [128, 0, 117, 1], [128, 3]], "functionMap": {"names": ["<global>", "infiniteQueryBehavior", "onFetch", "fetchFn", "addSignalProperty", "Object.defineProperty$argument_2.get", "context.signal.addEventListener$argument_1", "fetchPage", "createQueryFnContext", "context.fetchFn", "getNextPageParam", "getPreviousPageParam", "hasNextPage", "hasPreviousPage"], "mappings": "AAA;ACE;aCE;sBCO;kCCE;iBCG;yDCI;iBDE;aDG;SDE;0BIE;uCCO;WDU;SJS;ODsB;0BOE;SPW;KDI;CDE;AUC;CVQ;AWC;CXE;AYC;CZG;AaC;CbG"}}, "type": "js/module"}]}