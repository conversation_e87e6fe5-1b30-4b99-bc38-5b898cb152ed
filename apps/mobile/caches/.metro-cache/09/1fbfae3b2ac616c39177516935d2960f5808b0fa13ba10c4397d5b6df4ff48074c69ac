{"dependencies": [{"name": "../../../src/private/specs_DEPRECATED/modules/NativeSoundManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 81}}], "key": "O55Xe+oUnW+gNFUISvwWKvAPG14=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _NativeSoundManager = _interopRequireWildcard(require(_dependencyMap[0], \"../../../src/private/specs_DEPRECATED/modules/NativeSoundManager\"));\n  Object.keys(_NativeSoundManager).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _NativeSoundManager[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _NativeSoundManager[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _NativeSoundManager.default;\n});", "lineCount": 21, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeSoundManager"], [7, 25, 11, 0], [7, 28, 11, 0, "_interopRequireWildcard"], [7, 51, 11, 0], [7, 52, 11, 0, "require"], [7, 59, 11, 0], [7, 60, 11, 0, "_dependencyMap"], [7, 74, 11, 0], [8, 2, 11, 0, "Object"], [8, 8, 11, 0], [8, 9, 11, 0, "keys"], [8, 13, 11, 0], [8, 14, 11, 0, "_NativeSoundManager"], [8, 33, 11, 0], [8, 35, 11, 0, "for<PERSON>ach"], [8, 42, 11, 0], [8, 53, 11, 0, "key"], [8, 56, 11, 0], [9, 4, 11, 0], [9, 8, 11, 0, "key"], [9, 11, 11, 0], [9, 29, 11, 0, "key"], [9, 32, 11, 0], [10, 4, 11, 0], [10, 8, 11, 0, "Object"], [10, 14, 11, 0], [10, 15, 11, 0, "prototype"], [10, 24, 11, 0], [10, 25, 11, 0, "hasOwnProperty"], [10, 39, 11, 0], [10, 40, 11, 0, "call"], [10, 44, 11, 0], [10, 45, 11, 0, "_exportNames"], [10, 57, 11, 0], [10, 59, 11, 0, "key"], [10, 62, 11, 0], [11, 4, 11, 0], [11, 8, 11, 0, "key"], [11, 11, 11, 0], [11, 15, 11, 0, "exports"], [11, 22, 11, 0], [11, 26, 11, 0, "exports"], [11, 33, 11, 0], [11, 34, 11, 0, "key"], [11, 37, 11, 0], [11, 43, 11, 0, "_NativeSoundManager"], [11, 62, 11, 0], [11, 63, 11, 0, "key"], [11, 66, 11, 0], [12, 4, 11, 0, "Object"], [12, 10, 11, 0], [12, 11, 11, 0, "defineProperty"], [12, 25, 11, 0], [12, 26, 11, 0, "exports"], [12, 33, 11, 0], [12, 35, 11, 0, "key"], [12, 38, 11, 0], [13, 6, 11, 0, "enumerable"], [13, 16, 11, 0], [14, 6, 11, 0, "get"], [14, 9, 11, 0], [14, 20, 11, 0, "get"], [14, 21, 11, 0], [15, 8, 11, 0], [15, 15, 11, 0, "_NativeSoundManager"], [15, 34, 11, 0], [15, 35, 11, 0, "key"], [15, 38, 11, 0], [16, 6, 11, 0], [17, 4, 11, 0], [18, 2, 11, 0], [19, 2, 11, 81], [19, 11, 11, 81, "_interopRequireWildcard"], [19, 35, 11, 81, "e"], [19, 36, 11, 81], [19, 38, 11, 81, "t"], [19, 39, 11, 81], [19, 68, 11, 81, "WeakMap"], [19, 75, 11, 81], [19, 81, 11, 81, "r"], [19, 82, 11, 81], [19, 89, 11, 81, "WeakMap"], [19, 96, 11, 81], [19, 100, 11, 81, "n"], [19, 101, 11, 81], [19, 108, 11, 81, "WeakMap"], [19, 115, 11, 81], [19, 127, 11, 81, "_interopRequireWildcard"], [19, 150, 11, 81], [19, 162, 11, 81, "_interopRequireWildcard"], [19, 163, 11, 81, "e"], [19, 164, 11, 81], [19, 166, 11, 81, "t"], [19, 167, 11, 81], [19, 176, 11, 81, "t"], [19, 177, 11, 81], [19, 181, 11, 81, "e"], [19, 182, 11, 81], [19, 186, 11, 81, "e"], [19, 187, 11, 81], [19, 188, 11, 81, "__esModule"], [19, 198, 11, 81], [19, 207, 11, 81, "e"], [19, 208, 11, 81], [19, 214, 11, 81, "o"], [19, 215, 11, 81], [19, 217, 11, 81, "i"], [19, 218, 11, 81], [19, 220, 11, 81, "f"], [19, 221, 11, 81], [19, 226, 11, 81, "__proto__"], [19, 235, 11, 81], [19, 243, 11, 81, "default"], [19, 250, 11, 81], [19, 252, 11, 81, "e"], [19, 253, 11, 81], [19, 270, 11, 81, "e"], [19, 271, 11, 81], [19, 294, 11, 81, "e"], [19, 295, 11, 81], [19, 320, 11, 81, "e"], [19, 321, 11, 81], [19, 330, 11, 81, "f"], [19, 331, 11, 81], [19, 337, 11, 81, "o"], [19, 338, 11, 81], [19, 341, 11, 81, "t"], [19, 342, 11, 81], [19, 345, 11, 81, "n"], [19, 346, 11, 81], [19, 349, 11, 81, "r"], [19, 350, 11, 81], [19, 358, 11, 81, "o"], [19, 359, 11, 81], [19, 360, 11, 81, "has"], [19, 363, 11, 81], [19, 364, 11, 81, "e"], [19, 365, 11, 81], [19, 375, 11, 81, "o"], [19, 376, 11, 81], [19, 377, 11, 81, "get"], [19, 380, 11, 81], [19, 381, 11, 81, "e"], [19, 382, 11, 81], [19, 385, 11, 81, "o"], [19, 386, 11, 81], [19, 387, 11, 81, "set"], [19, 390, 11, 81], [19, 391, 11, 81, "e"], [19, 392, 11, 81], [19, 394, 11, 81, "f"], [19, 395, 11, 81], [19, 409, 11, 81, "_t"], [19, 411, 11, 81], [19, 415, 11, 81, "e"], [19, 416, 11, 81], [19, 432, 11, 81, "_t"], [19, 434, 11, 81], [19, 441, 11, 81, "hasOwnProperty"], [19, 455, 11, 81], [19, 456, 11, 81, "call"], [19, 460, 11, 81], [19, 461, 11, 81, "e"], [19, 462, 11, 81], [19, 464, 11, 81, "_t"], [19, 466, 11, 81], [19, 473, 11, 81, "i"], [19, 474, 11, 81], [19, 478, 11, 81, "o"], [19, 479, 11, 81], [19, 482, 11, 81, "Object"], [19, 488, 11, 81], [19, 489, 11, 81, "defineProperty"], [19, 503, 11, 81], [19, 508, 11, 81, "Object"], [19, 514, 11, 81], [19, 515, 11, 81, "getOwnPropertyDescriptor"], [19, 539, 11, 81], [19, 540, 11, 81, "e"], [19, 541, 11, 81], [19, 543, 11, 81, "_t"], [19, 545, 11, 81], [19, 552, 11, 81, "i"], [19, 553, 11, 81], [19, 554, 11, 81, "get"], [19, 557, 11, 81], [19, 561, 11, 81, "i"], [19, 562, 11, 81], [19, 563, 11, 81, "set"], [19, 566, 11, 81], [19, 570, 11, 81, "o"], [19, 571, 11, 81], [19, 572, 11, 81, "f"], [19, 573, 11, 81], [19, 575, 11, 81, "_t"], [19, 577, 11, 81], [19, 579, 11, 81, "i"], [19, 580, 11, 81], [19, 584, 11, 81, "f"], [19, 585, 11, 81], [19, 586, 11, 81, "_t"], [19, 588, 11, 81], [19, 592, 11, 81, "e"], [19, 593, 11, 81], [19, 594, 11, 81, "_t"], [19, 596, 11, 81], [19, 607, 11, 81, "f"], [19, 608, 11, 81], [19, 613, 11, 81, "e"], [19, 614, 11, 81], [19, 616, 11, 81, "t"], [19, 617, 11, 81], [20, 2, 11, 81], [20, 6, 11, 81, "_default"], [20, 14, 11, 81], [20, 17, 11, 81, "exports"], [20, 24, 11, 81], [20, 25, 11, 81, "default"], [20, 32, 11, 81], [20, 35, 13, 15, "NativeSoundManager"], [20, 62, 13, 33], [21, 0, 13, 33], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}