{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var SatelliteDish = exports.default = (0, _createLucideIcon.default)(\"SatelliteDish\", [[\"path\", {\n    d: \"M4 10a7.31 7.31 0 0 0 10 10Z\",\n    key: \"1fzpp3\"\n  }], [\"path\", {\n    d: \"m9 15 3-3\",\n    key: \"88sc13\"\n  }], [\"path\", {\n    d: \"M17 13a6 6 0 0 0-6-6\",\n    key: \"15cc6u\"\n  }], [\"path\", {\n    d: \"M21 13A10 10 0 0 0 11 3\",\n    key: \"11nf8s\"\n  }]]);\n});", "lineCount": 28, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "SatelliteDish"], [15, 19, 10, 19], [15, 22, 10, 19, "exports"], [15, 29, 10, 19], [15, 30, 10, 19, "default"], [15, 37, 10, 19], [15, 40, 10, 22], [15, 44, 10, 22, "createLucideIcon"], [15, 69, 10, 38], [15, 71, 10, 39], [15, 86, 10, 54], [15, 88, 10, 56], [15, 89, 11, 2], [15, 90, 11, 3], [15, 96, 11, 9], [15, 98, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 37, 11, 46], [17, 4, 11, 48, "key"], [17, 7, 11, 51], [17, 9, 11, 53], [18, 2, 11, 62], [18, 3, 11, 63], [18, 4, 11, 64], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 18, 12, 27], [20, 4, 12, 29, "key"], [20, 7, 12, 32], [20, 9, 12, 34], [21, 2, 12, 43], [21, 3, 12, 44], [21, 4, 12, 45], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 29, 13, 38], [23, 4, 13, 40, "key"], [23, 7, 13, 43], [23, 9, 13, 45], [24, 2, 13, 54], [24, 3, 13, 55], [24, 4, 13, 56], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 32, 14, 41], [26, 4, 14, 43, "key"], [26, 7, 14, 46], [26, 9, 14, 48], [27, 2, 14, 57], [27, 3, 14, 58], [27, 4, 14, 59], [27, 5, 15, 1], [27, 6, 15, 2], [28, 0, 15, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}