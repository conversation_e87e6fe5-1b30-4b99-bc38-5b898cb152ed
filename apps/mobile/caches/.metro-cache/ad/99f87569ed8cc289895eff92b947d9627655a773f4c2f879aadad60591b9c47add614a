{"dependencies": [{"name": "../getNamedContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 56, "index": 71}}], "key": "KRgSZGDSx7/6c0jPDTEf7wsaS4k=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderHeightContext = void 0;\n  var _getNamedContext = require(_dependencyMap[0], \"../getNamedContext.js\");\n  const HeaderHeightContext = exports.HeaderHeightContext = (0, _getNamedContext.getNamedContext)('HeaderHeightContext', undefined);\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "HeaderHeightContext"], [7, 29, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_getNamedContext"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "HeaderHeightContext"], [9, 27, 4, 32], [9, 30, 4, 32, "exports"], [9, 37, 4, 32], [9, 38, 4, 32, "HeaderHeightContext"], [9, 57, 4, 32], [9, 60, 4, 35], [9, 64, 4, 35, "getNamedContext"], [9, 96, 4, 50], [9, 98, 4, 51], [9, 119, 4, 72], [9, 121, 4, 74, "undefined"], [9, 130, 4, 83], [9, 131, 4, 84], [10, 0, 4, 85], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}