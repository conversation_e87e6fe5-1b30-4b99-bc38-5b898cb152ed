{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getNextHandlerTag = getNextHandlerTag;\n  let handlerTag = 1;\n  function getNextHandlerTag() {\n    return handlerTag++;\n  }\n});", "lineCount": 10, "map": [[6, 2, 1, 0], [6, 6, 1, 4, "handlerTag"], [6, 16, 1, 14], [6, 19, 1, 17], [6, 20, 1, 18], [7, 2, 2, 7], [7, 11, 2, 16, "getNextHandlerTag"], [7, 28, 2, 33, "getNextHandlerTag"], [7, 29, 2, 33], [7, 31, 2, 36], [8, 4, 3, 2], [8, 11, 3, 9, "handlerTag"], [8, 21, 3, 19], [8, 23, 3, 21], [9, 2, 4, 0], [10, 0, 4, 1], [10, 3]], "functionMap": {"names": ["<global>", "getNextHandlerTag"], "mappings": "AAA;OCC;CDE"}}, "type": "js/module"}]}