{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const SplinePointer = exports.default = (0, _createLucideIcon.default)(\"SplinePointer\", [[\"path\", {\n    d: \"M12.034 12.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.943l-3.444 1.068a1 1 0 0 0-.66.66l-1.067 3.443a.5.5 0 0 1-.943.033z\",\n    key: \"xwnzip\"\n  }], [\"path\", {\n    d: \"M5 17A12 12 0 0 1 17 5\",\n    key: \"1okkup\"\n  }], [\"circle\", {\n    cx: \"19\",\n    cy: \"5\",\n    r: \"2\",\n    key: \"mhkx31\"\n  }], [\"circle\", {\n    cx: \"5\",\n    cy: \"19\",\n    r: \"2\",\n    key: \"v8kfzx\"\n  }]]);\n});", "lineCount": 32, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 21, 10, 19], [15, 24, 10, 19, "exports"], [15, 31, 10, 19], [15, 32, 10, 19, "default"], [15, 39, 10, 19], [15, 42, 10, 22], [15, 46, 10, 22, "createLucideIcon"], [15, 71, 10, 38], [15, 73, 10, 39], [15, 88, 10, 54], [15, 90, 10, 56], [15, 91, 11, 2], [15, 92, 12, 4], [15, 98, 12, 10], [15, 100, 13, 4], [16, 4, 14, 6, "d"], [16, 5, 14, 7], [16, 7, 14, 9], [16, 138, 14, 140], [17, 4, 15, 6, "key"], [17, 7, 15, 9], [17, 9, 15, 11], [18, 2, 16, 4], [18, 3, 16, 5], [18, 4, 17, 3], [18, 6, 18, 2], [18, 7, 18, 3], [18, 13, 18, 9], [18, 15, 18, 11], [19, 4, 18, 13, "d"], [19, 5, 18, 14], [19, 7, 18, 16], [19, 31, 18, 40], [20, 4, 18, 42, "key"], [20, 7, 18, 45], [20, 9, 18, 47], [21, 2, 18, 56], [21, 3, 18, 57], [21, 4, 18, 58], [21, 6, 19, 2], [21, 7, 19, 3], [21, 15, 19, 11], [21, 17, 19, 13], [22, 4, 19, 15, "cx"], [22, 6, 19, 17], [22, 8, 19, 19], [22, 12, 19, 23], [23, 4, 19, 25, "cy"], [23, 6, 19, 27], [23, 8, 19, 29], [23, 11, 19, 32], [24, 4, 19, 34, "r"], [24, 5, 19, 35], [24, 7, 19, 37], [24, 10, 19, 40], [25, 4, 19, 42, "key"], [25, 7, 19, 45], [25, 9, 19, 47], [26, 2, 19, 56], [26, 3, 19, 57], [26, 4, 19, 58], [26, 6, 20, 2], [26, 7, 20, 3], [26, 15, 20, 11], [26, 17, 20, 13], [27, 4, 20, 15, "cx"], [27, 6, 20, 17], [27, 8, 20, 19], [27, 11, 20, 22], [28, 4, 20, 24, "cy"], [28, 6, 20, 26], [28, 8, 20, 28], [28, 12, 20, 32], [29, 4, 20, 34, "r"], [29, 5, 20, 35], [29, 7, 20, 37], [29, 10, 20, 40], [30, 4, 20, 42, "key"], [30, 7, 20, 45], [30, 9, 20, 47], [31, 2, 20, 56], [31, 3, 20, 57], [31, 4, 20, 58], [31, 5, 21, 1], [31, 6, 21, 2], [32, 0, 21, 3], [32, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}