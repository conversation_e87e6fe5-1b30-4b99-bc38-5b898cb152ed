{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./Easing.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 42, "index": 110}}], "key": "1vYH+qXuBgCne+SeMCUQz05xXM8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.convertAnimationObjectToKeyframes = convertAnimationObjectToKeyframes;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _Easing = require(_dependencyMap[2], \"./Easing.web\");\n  function convertAnimationObjectToKeyframes(animationObject) {\n    var keyframe = `@keyframes ${animationObject.name} { `;\n    for (var _ref of Object.entries(animationObject.style)) {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n      var timestamp = _ref2[0];\n      var style = _ref2[1];\n      var step = timestamp === 'from' ? 0 : timestamp === 'to' ? 100 : timestamp;\n      keyframe += `${step}% { `;\n      for (var _ref3 of Object.entries(style)) {\n        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);\n        var property = _ref4[0];\n        var values = _ref4[1];\n        if (property === 'easing') {\n          var easingName = 'linear';\n          if (values in _Easing.WebEasings) {\n            easingName = values;\n          } else if (values.name in _Easing.WebEasings) {\n            easingName = values.name;\n          }\n          keyframe += `animation-timing-function: cubic-bezier(${_Easing.WebEasings[easingName].toString()});`;\n          continue;\n        }\n        if (property === 'originX') {\n          keyframe += `left: ${values}px; `;\n          continue;\n        }\n        if (property === 'originY') {\n          keyframe += `top: ${values}px; `;\n          continue;\n        }\n        if (property !== 'transform') {\n          keyframe += `${property}: ${values}; `;\n          continue;\n        }\n        keyframe += `transform:`;\n        values.forEach(value => {\n          for (var _ref5 of Object.entries(value)) {\n            var _ref6 = (0, _slicedToArray2.default)(_ref5, 2);\n            var transformProperty = _ref6[0];\n            var transformPropertyValue = _ref6[1];\n            keyframe += ` ${transformProperty}(${transformPropertyValue})`;\n          }\n        });\n        keyframe += `; `; // Property end\n      }\n      keyframe += `} `; // Timestamp end\n    }\n    keyframe += `} `; // Keyframe end\n\n    return keyframe;\n  }\n});", "lineCount": 62, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "convertAnimationObjectToKeyframes"], [8, 43, 1, 13], [8, 46, 1, 13, "convertAnimationObjectToKeyframes"], [8, 79, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 4, 0], [10, 6, 4, 0, "_Easing"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 43, 7], [11, 11, 43, 16, "convertAnimationObjectToKeyframes"], [11, 44, 43, 49, "convertAnimationObjectToKeyframes"], [11, 45, 44, 2, "animationObject"], [11, 60, 44, 32], [11, 62, 45, 2], [12, 4, 46, 2], [12, 8, 46, 6, "keyframe"], [12, 16, 46, 14], [12, 19, 46, 17], [12, 33, 46, 31, "animationObject"], [12, 48, 46, 46], [12, 49, 46, 47, "name"], [12, 53, 46, 51], [12, 58, 46, 56], [13, 4, 48, 2], [13, 13, 48, 2, "_ref"], [13, 17, 48, 2], [13, 21, 48, 35, "Object"], [13, 27, 48, 41], [13, 28, 48, 42, "entries"], [13, 35, 48, 49], [13, 36, 48, 50, "animationObject"], [13, 51, 48, 65], [13, 52, 48, 66, "style"], [13, 57, 48, 71], [13, 58, 48, 72], [13, 60, 48, 74], [14, 6, 48, 74], [14, 10, 48, 74, "_ref2"], [14, 15, 48, 74], [14, 22, 48, 74, "_slicedToArray2"], [14, 37, 48, 74], [14, 38, 48, 74, "default"], [14, 45, 48, 74], [14, 47, 48, 74, "_ref"], [14, 51, 48, 74], [15, 6, 48, 74], [15, 10, 48, 14, "timestamp"], [15, 19, 48, 23], [15, 22, 48, 23, "_ref2"], [15, 27, 48, 23], [16, 6, 48, 23], [16, 10, 48, 25, "style"], [16, 15, 48, 30], [16, 18, 48, 30, "_ref2"], [16, 23, 48, 30], [17, 6, 49, 4], [17, 10, 49, 10, "step"], [17, 14, 49, 14], [17, 17, 50, 6, "timestamp"], [17, 26, 50, 15], [17, 31, 50, 20], [17, 37, 50, 26], [17, 40, 50, 29], [17, 41, 50, 30], [17, 44, 50, 33, "timestamp"], [17, 53, 50, 42], [17, 58, 50, 47], [17, 62, 50, 51], [17, 65, 50, 54], [17, 68, 50, 57], [17, 71, 50, 60, "timestamp"], [17, 80, 50, 69], [18, 6, 52, 4, "keyframe"], [18, 14, 52, 12], [18, 18, 52, 16], [18, 21, 52, 19, "step"], [18, 25, 52, 23], [18, 31, 52, 29], [19, 6, 54, 4], [19, 15, 54, 4, "_ref3"], [19, 20, 54, 4], [19, 24, 54, 37, "Object"], [19, 30, 54, 43], [19, 31, 54, 44, "entries"], [19, 38, 54, 51], [19, 39, 54, 52, "style"], [19, 44, 54, 57], [19, 45, 54, 58], [19, 47, 54, 60], [20, 8, 54, 60], [20, 12, 54, 60, "_ref4"], [20, 17, 54, 60], [20, 24, 54, 60, "_slicedToArray2"], [20, 39, 54, 60], [20, 40, 54, 60, "default"], [20, 47, 54, 60], [20, 49, 54, 60, "_ref3"], [20, 54, 54, 60], [21, 8, 54, 60], [21, 12, 54, 16, "property"], [21, 20, 54, 24], [21, 23, 54, 24, "_ref4"], [21, 28, 54, 24], [22, 8, 54, 24], [22, 12, 54, 26, "values"], [22, 18, 54, 32], [22, 21, 54, 32, "_ref4"], [22, 26, 54, 32], [23, 8, 55, 6], [23, 12, 55, 10, "property"], [23, 20, 55, 18], [23, 25, 55, 23], [23, 33, 55, 31], [23, 35, 55, 33], [24, 10, 56, 8], [24, 14, 56, 12, "easingName"], [24, 24, 56, 39], [24, 27, 56, 42], [24, 35, 56, 50], [25, 10, 58, 8], [25, 14, 58, 12, "values"], [25, 20, 58, 18], [25, 24, 58, 22, "WebEasings"], [25, 42, 58, 32], [25, 44, 58, 34], [26, 12, 59, 10, "easingName"], [26, 22, 59, 20], [26, 25, 59, 23, "values"], [26, 31, 59, 29], [27, 10, 60, 8], [27, 11, 60, 9], [27, 17, 60, 15], [27, 21, 60, 19, "values"], [27, 27, 60, 25], [27, 28, 60, 26, "name"], [27, 32, 60, 30], [27, 36, 60, 34, "WebEasings"], [27, 54, 60, 44], [27, 56, 60, 46], [28, 12, 61, 10, "easingName"], [28, 22, 61, 20], [28, 25, 61, 23, "values"], [28, 31, 61, 29], [28, 32, 61, 30, "name"], [28, 36, 61, 34], [29, 10, 62, 8], [30, 10, 64, 8, "keyframe"], [30, 18, 64, 16], [30, 22, 64, 20], [30, 65, 64, 63, "WebEasings"], [30, 83, 64, 73], [30, 84, 65, 10, "easingName"], [30, 94, 65, 20], [30, 95, 66, 9], [30, 96, 66, 10, "toString"], [30, 104, 66, 18], [30, 105, 66, 19], [30, 106, 66, 20], [30, 110, 66, 24], [31, 10, 68, 8], [32, 8, 69, 6], [33, 8, 71, 6], [33, 12, 71, 10, "property"], [33, 20, 71, 18], [33, 25, 71, 23], [33, 34, 71, 32], [33, 36, 71, 34], [34, 10, 72, 8, "keyframe"], [34, 18, 72, 16], [34, 22, 72, 20], [34, 31, 72, 29, "values"], [34, 37, 72, 35], [34, 43, 72, 41], [35, 10, 73, 8], [36, 8, 74, 6], [37, 8, 76, 6], [37, 12, 76, 10, "property"], [37, 20, 76, 18], [37, 25, 76, 23], [37, 34, 76, 32], [37, 36, 76, 34], [38, 10, 77, 8, "keyframe"], [38, 18, 77, 16], [38, 22, 77, 20], [38, 30, 77, 28, "values"], [38, 36, 77, 34], [38, 42, 77, 40], [39, 10, 78, 8], [40, 8, 79, 6], [41, 8, 81, 6], [41, 12, 81, 10, "property"], [41, 20, 81, 18], [41, 25, 81, 23], [41, 36, 81, 34], [41, 38, 81, 36], [42, 10, 82, 8, "keyframe"], [42, 18, 82, 16], [42, 22, 82, 20], [42, 25, 82, 23, "property"], [42, 33, 82, 31], [42, 38, 82, 36, "values"], [42, 44, 82, 42], [42, 48, 82, 46], [43, 10, 83, 8], [44, 8, 84, 6], [45, 8, 86, 6, "keyframe"], [45, 16, 86, 14], [45, 20, 86, 18], [45, 32, 86, 30], [46, 8, 88, 6, "values"], [46, 14, 88, 12], [46, 15, 88, 13, "for<PERSON>ach"], [46, 22, 88, 20], [46, 23, 88, 22, "value"], [46, 28, 88, 61], [46, 32, 88, 66], [47, 10, 89, 8], [47, 19, 89, 8, "_ref5"], [47, 24, 89, 8], [47, 28, 92, 13, "Object"], [47, 34, 92, 19], [47, 35, 92, 20, "entries"], [47, 42, 92, 27], [47, 43, 92, 28, "value"], [47, 48, 92, 33], [47, 49, 92, 34], [47, 51, 92, 36], [48, 12, 92, 36], [48, 16, 92, 36, "_ref6"], [48, 21, 92, 36], [48, 28, 92, 36, "_slicedToArray2"], [48, 43, 92, 36], [48, 44, 92, 36, "default"], [48, 51, 92, 36], [48, 53, 92, 36, "_ref5"], [48, 58, 92, 36], [49, 12, 92, 36], [49, 16, 90, 10, "transformProperty"], [49, 33, 90, 27], [49, 36, 90, 27, "_ref6"], [49, 41, 90, 27], [50, 12, 90, 27], [50, 16, 91, 10, "transformPropertyValue"], [50, 38, 91, 32], [50, 41, 91, 32, "_ref6"], [50, 46, 91, 32], [51, 12, 93, 10, "keyframe"], [51, 20, 93, 18], [51, 24, 93, 22], [51, 28, 93, 26, "transformProperty"], [51, 45, 93, 43], [51, 49, 93, 47, "transformPropertyValue"], [51, 71, 93, 69], [51, 74, 93, 72], [52, 10, 94, 8], [53, 8, 95, 6], [53, 9, 95, 7], [53, 10, 95, 8], [54, 8, 96, 6, "keyframe"], [54, 16, 96, 14], [54, 20, 96, 18], [54, 24, 96, 22], [54, 25, 96, 23], [54, 26, 96, 24], [55, 6, 97, 4], [56, 6, 98, 4, "keyframe"], [56, 14, 98, 12], [56, 18, 98, 16], [56, 22, 98, 20], [56, 23, 98, 21], [56, 24, 98, 22], [57, 4, 99, 2], [58, 4, 100, 2, "keyframe"], [58, 12, 100, 10], [58, 16, 100, 14], [58, 20, 100, 18], [58, 21, 100, 19], [58, 22, 100, 20], [60, 4, 102, 2], [60, 11, 102, 9, "keyframe"], [60, 19, 102, 17], [61, 2, 103, 0], [62, 0, 103, 1], [62, 3]], "functionMap": {"names": ["<global>", "convertAnimationObjectToKeyframes", "values.forEach$argument_0"], "mappings": "AAA;OC0C;qBC6C;ODO;CDQ"}}, "type": "js/module"}]}