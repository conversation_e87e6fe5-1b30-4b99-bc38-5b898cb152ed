{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../../Directions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 66, "index": 314}}], "key": "05BxNcpXaYAeyUvpG1n5gignxMc=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 315}, "end": {"line": 5, "column": 46, "index": 361}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}, {"name": "../tools/Vector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 362}, "end": {"line": 6, "column": 37, "index": 399}}], "key": "4h752T1Mc+t0TArQHF8ZPfbqdCc=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 400}, "end": {"line": 7, "column": 43, "index": 443}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _Directions = require(_dependencyMap[2], \"../../Directions\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./GestureHandler\"));\n  var _Vector = _interopRequireDefault(require(_dependencyMap[4], \"../tools/Vector\"));\n  var _utils = require(_dependencyMap[5], \"../utils\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const DEFAULT_MAX_DURATION_MS = 800;\n  const DEFAULT_MIN_VELOCITY = 700;\n  const DEFAULT_ALIGNMENT_CONE = 30;\n  const DEFAULT_DIRECTION = _Directions.Directions.RIGHT;\n  const DEFAULT_NUMBER_OF_TOUCHES_REQUIRED = 1;\n  const AXIAL_DEVIATION_COSINE = (0, _utils.coneToDeviation)(DEFAULT_ALIGNMENT_CONE);\n  const DIAGONAL_DEVIATION_COSINE = (0, _utils.coneToDeviation)(90 - DEFAULT_ALIGNMENT_CONE);\n  class FlingGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"numberOfPointersRequired\", DEFAULT_NUMBER_OF_TOUCHES_REQUIRED);\n      _defineProperty(this, \"direction\", DEFAULT_DIRECTION);\n      _defineProperty(this, \"maxDurationMs\", DEFAULT_MAX_DURATION_MS);\n      _defineProperty(this, \"minVelocity\", DEFAULT_MIN_VELOCITY);\n      _defineProperty(this, \"delayTimeout\", void 0);\n      _defineProperty(this, \"maxNumberOfPointersSimultaneously\", 0);\n      _defineProperty(this, \"keyPointer\", NaN);\n    }\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      super.updateGestureConfig({\n        enabled: enabled,\n        ...props\n      });\n      if (this.config.direction) {\n        this.direction = this.config.direction;\n      }\n      if (this.config.numberOfPointers) {\n        this.numberOfPointersRequired = this.config.numberOfPointers;\n      }\n    }\n    startFling() {\n      this.begin();\n      this.maxNumberOfPointersSimultaneously = 1;\n      this.delayTimeout = setTimeout(() => this.fail(), this.maxDurationMs);\n    }\n    tryEndFling() {\n      const velocityVector = _Vector.default.fromVelocity(this.tracker, this.keyPointer);\n      const getAlignment = (direction, minimalAlignmentCosine) => {\n        return (direction & this.direction) === direction && velocityVector.isSimilar(_Vector.default.fromDirection(direction), minimalAlignmentCosine);\n      };\n      const axialDirectionsList = Object.values(_Directions.Directions);\n      const diagonalDirectionsList = Object.values(_Directions.DiagonalDirections); // List of alignments to all activated directions\n\n      const axialAlignmentList = axialDirectionsList.map(direction => getAlignment(direction, AXIAL_DEVIATION_COSINE));\n      const diagonalAlignmentList = diagonalDirectionsList.map(direction => getAlignment(direction, DIAGONAL_DEVIATION_COSINE));\n      const isAligned = axialAlignmentList.some(Boolean) || diagonalAlignmentList.some(Boolean);\n      const isFast = velocityVector.magnitude > this.minVelocity;\n      if (this.maxNumberOfPointersSimultaneously === this.numberOfPointersRequired && isAligned && isFast) {\n        clearTimeout(this.delayTimeout);\n        this.activate();\n        return true;\n      }\n      return false;\n    }\n    endFling() {\n      if (!this.tryEndFling()) {\n        this.fail();\n      }\n    }\n    onPointerDown(event) {\n      if (!this.isButtonInConfig(event.button)) {\n        return;\n      }\n      this.tracker.addToTracker(event);\n      this.keyPointer = event.pointerId;\n      super.onPointerDown(event);\n      this.newPointerAction();\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerAdd(event);\n      this.newPointerAction();\n    }\n    newPointerAction() {\n      if (this.state === _State.State.UNDETERMINED) {\n        this.startFling();\n      }\n      if (this.state !== _State.State.BEGAN) {\n        return;\n      }\n      this.tryEndFling();\n      if (this.tracker.trackedPointersCount > this.maxNumberOfPointersSimultaneously) {\n        this.maxNumberOfPointersSimultaneously = this.tracker.trackedPointersCount;\n      }\n    }\n    pointerMoveAction(event) {\n      this.tracker.track(event);\n      if (this.state !== _State.State.BEGAN) {\n        return;\n      }\n      this.tryEndFling();\n    }\n    onPointerMove(event) {\n      this.pointerMoveAction(event);\n      super.onPointerMove(event);\n    }\n    onPointerOutOfBounds(event) {\n      this.pointerMoveAction(event);\n      super.onPointerOutOfBounds(event);\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      this.onUp(event);\n      this.keyPointer = NaN;\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.onUp(event);\n    }\n    onUp(event) {\n      if (this.state === _State.State.BEGAN) {\n        this.endFling();\n      }\n      this.tracker.removeFromTracker(event.pointerId);\n    }\n    activate(force) {\n      super.activate(force);\n      this.end();\n    }\n    resetConfig() {\n      super.resetConfig();\n      this.numberOfPointersRequired = DEFAULT_NUMBER_OF_TOUCHES_REQUIRED;\n      this.direction = DEFAULT_DIRECTION;\n    }\n  }\n  exports.default = FlingGestureHandler;\n});", "lineCount": 155, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_Directions"], [8, 17, 4, 0], [8, 20, 4, 0, "require"], [8, 27, 4, 0], [8, 28, 4, 0, "_dependencyMap"], [8, 42, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_Gesture<PERSON><PERSON>ler"], [9, 21, 5, 0], [9, 24, 5, 0, "_interopRequireDefault"], [9, 46, 5, 0], [9, 47, 5, 0, "require"], [9, 54, 5, 0], [9, 55, 5, 0, "_dependencyMap"], [9, 69, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_Vector"], [10, 13, 6, 0], [10, 16, 6, 0, "_interopRequireDefault"], [10, 38, 6, 0], [10, 39, 6, 0, "require"], [10, 46, 6, 0], [10, 47, 6, 0, "_dependencyMap"], [10, 61, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_utils"], [11, 12, 7, 0], [11, 15, 7, 0, "require"], [11, 22, 7, 0], [11, 23, 7, 0, "_dependencyMap"], [11, 37, 7, 0], [12, 2, 1, 0], [12, 11, 1, 9, "_defineProperty"], [12, 26, 1, 24, "_defineProperty"], [12, 27, 1, 25, "obj"], [12, 30, 1, 28], [12, 32, 1, 30, "key"], [12, 35, 1, 33], [12, 37, 1, 35, "value"], [12, 42, 1, 40], [12, 44, 1, 42], [13, 4, 1, 44], [13, 8, 1, 48, "key"], [13, 11, 1, 51], [13, 15, 1, 55, "obj"], [13, 18, 1, 58], [13, 20, 1, 60], [14, 6, 1, 62, "Object"], [14, 12, 1, 68], [14, 13, 1, 69, "defineProperty"], [14, 27, 1, 83], [14, 28, 1, 84, "obj"], [14, 31, 1, 87], [14, 33, 1, 89, "key"], [14, 36, 1, 92], [14, 38, 1, 94], [15, 8, 1, 96, "value"], [15, 13, 1, 101], [15, 15, 1, 103, "value"], [15, 20, 1, 108], [16, 8, 1, 110, "enumerable"], [16, 18, 1, 120], [16, 20, 1, 122], [16, 24, 1, 126], [17, 8, 1, 128, "configurable"], [17, 20, 1, 140], [17, 22, 1, 142], [17, 26, 1, 146], [18, 8, 1, 148, "writable"], [18, 16, 1, 156], [18, 18, 1, 158], [19, 6, 1, 163], [19, 7, 1, 164], [19, 8, 1, 165], [20, 4, 1, 167], [20, 5, 1, 168], [20, 11, 1, 174], [21, 6, 1, 176, "obj"], [21, 9, 1, 179], [21, 10, 1, 180, "key"], [21, 13, 1, 183], [21, 14, 1, 184], [21, 17, 1, 187, "value"], [21, 22, 1, 192], [22, 4, 1, 194], [23, 4, 1, 196], [23, 11, 1, 203, "obj"], [23, 14, 1, 206], [24, 2, 1, 208], [25, 2, 8, 0], [25, 8, 8, 6, "DEFAULT_MAX_DURATION_MS"], [25, 31, 8, 29], [25, 34, 8, 32], [25, 37, 8, 35], [26, 2, 9, 0], [26, 8, 9, 6, "DEFAULT_MIN_VELOCITY"], [26, 28, 9, 26], [26, 31, 9, 29], [26, 34, 9, 32], [27, 2, 10, 0], [27, 8, 10, 6, "DEFAULT_ALIGNMENT_CONE"], [27, 30, 10, 28], [27, 33, 10, 31], [27, 35, 10, 33], [28, 2, 11, 0], [28, 8, 11, 6, "DEFAULT_DIRECTION"], [28, 25, 11, 23], [28, 28, 11, 26, "Directions"], [28, 50, 11, 36], [28, 51, 11, 37, "RIGHT"], [28, 56, 11, 42], [29, 2, 12, 0], [29, 8, 12, 6, "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED"], [29, 42, 12, 40], [29, 45, 12, 43], [29, 46, 12, 44], [30, 2, 13, 0], [30, 8, 13, 6, "AXIAL_DEVIATION_COSINE"], [30, 30, 13, 28], [30, 33, 13, 31], [30, 37, 13, 31, "coneToDeviation"], [30, 59, 13, 46], [30, 61, 13, 47, "DEFAULT_ALIGNMENT_CONE"], [30, 83, 13, 69], [30, 84, 13, 70], [31, 2, 14, 0], [31, 8, 14, 6, "DIAGONAL_DEVIATION_COSINE"], [31, 33, 14, 31], [31, 36, 14, 34], [31, 40, 14, 34, "coneToDeviation"], [31, 62, 14, 49], [31, 64, 14, 50], [31, 66, 14, 52], [31, 69, 14, 55, "DEFAULT_ALIGNMENT_CONE"], [31, 91, 14, 77], [31, 92, 14, 78], [32, 2, 15, 15], [32, 8, 15, 21, "FlingGestureHandler"], [32, 27, 15, 40], [32, 36, 15, 49, "Gesture<PERSON>andler"], [32, 59, 15, 63], [32, 60, 15, 64], [33, 4, 16, 2, "constructor"], [33, 15, 16, 13, "constructor"], [33, 16, 16, 14], [33, 19, 16, 17, "args"], [33, 23, 16, 21], [33, 25, 16, 23], [34, 6, 17, 4], [34, 11, 17, 9], [34, 12, 17, 10], [34, 15, 17, 13, "args"], [34, 19, 17, 17], [34, 20, 17, 18], [35, 6, 19, 4, "_defineProperty"], [35, 21, 19, 19], [35, 22, 19, 20], [35, 26, 19, 24], [35, 28, 19, 26], [35, 54, 19, 52], [35, 56, 19, 54, "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED"], [35, 90, 19, 88], [35, 91, 19, 89], [36, 6, 21, 4, "_defineProperty"], [36, 21, 21, 19], [36, 22, 21, 20], [36, 26, 21, 24], [36, 28, 21, 26], [36, 39, 21, 37], [36, 41, 21, 39, "DEFAULT_DIRECTION"], [36, 58, 21, 56], [36, 59, 21, 57], [37, 6, 23, 4, "_defineProperty"], [37, 21, 23, 19], [37, 22, 23, 20], [37, 26, 23, 24], [37, 28, 23, 26], [37, 43, 23, 41], [37, 45, 23, 43, "DEFAULT_MAX_DURATION_MS"], [37, 68, 23, 66], [37, 69, 23, 67], [38, 6, 25, 4, "_defineProperty"], [38, 21, 25, 19], [38, 22, 25, 20], [38, 26, 25, 24], [38, 28, 25, 26], [38, 41, 25, 39], [38, 43, 25, 41, "DEFAULT_MIN_VELOCITY"], [38, 63, 25, 61], [38, 64, 25, 62], [39, 6, 27, 4, "_defineProperty"], [39, 21, 27, 19], [39, 22, 27, 20], [39, 26, 27, 24], [39, 28, 27, 26], [39, 42, 27, 40], [39, 44, 27, 42], [39, 49, 27, 47], [39, 50, 27, 48], [39, 51, 27, 49], [40, 6, 29, 4, "_defineProperty"], [40, 21, 29, 19], [40, 22, 29, 20], [40, 26, 29, 24], [40, 28, 29, 26], [40, 63, 29, 61], [40, 65, 29, 63], [40, 66, 29, 64], [40, 67, 29, 65], [41, 6, 31, 4, "_defineProperty"], [41, 21, 31, 19], [41, 22, 31, 20], [41, 26, 31, 24], [41, 28, 31, 26], [41, 40, 31, 38], [41, 42, 31, 40, "NaN"], [41, 45, 31, 43], [41, 46, 31, 44], [42, 4, 32, 2], [43, 4, 34, 2, "updateGestureConfig"], [43, 23, 34, 21, "updateGestureConfig"], [43, 24, 34, 22], [44, 6, 35, 4, "enabled"], [44, 13, 35, 11], [44, 16, 35, 14], [44, 20, 35, 18], [45, 6, 36, 4], [45, 9, 36, 7, "props"], [46, 4, 37, 2], [46, 5, 37, 3], [46, 7, 37, 5], [47, 6, 38, 4], [47, 11, 38, 9], [47, 12, 38, 10, "updateGestureConfig"], [47, 31, 38, 29], [47, 32, 38, 30], [48, 8, 39, 6, "enabled"], [48, 15, 39, 13], [48, 17, 39, 15, "enabled"], [48, 24, 39, 22], [49, 8, 40, 6], [49, 11, 40, 9, "props"], [50, 6, 41, 4], [50, 7, 41, 5], [50, 8, 41, 6], [51, 6, 43, 4], [51, 10, 43, 8], [51, 14, 43, 12], [51, 15, 43, 13, "config"], [51, 21, 43, 19], [51, 22, 43, 20, "direction"], [51, 31, 43, 29], [51, 33, 43, 31], [52, 8, 44, 6], [52, 12, 44, 10], [52, 13, 44, 11, "direction"], [52, 22, 44, 20], [52, 25, 44, 23], [52, 29, 44, 27], [52, 30, 44, 28, "config"], [52, 36, 44, 34], [52, 37, 44, 35, "direction"], [52, 46, 44, 44], [53, 6, 45, 4], [54, 6, 47, 4], [54, 10, 47, 8], [54, 14, 47, 12], [54, 15, 47, 13, "config"], [54, 21, 47, 19], [54, 22, 47, 20, "numberOfPointers"], [54, 38, 47, 36], [54, 40, 47, 38], [55, 8, 48, 6], [55, 12, 48, 10], [55, 13, 48, 11, "numberOfPointersRequired"], [55, 37, 48, 35], [55, 40, 48, 38], [55, 44, 48, 42], [55, 45, 48, 43, "config"], [55, 51, 48, 49], [55, 52, 48, 50, "numberOfPointers"], [55, 68, 48, 66], [56, 6, 49, 4], [57, 4, 50, 2], [58, 4, 52, 2, "startFling"], [58, 14, 52, 12, "startFling"], [58, 15, 52, 12], [58, 17, 52, 15], [59, 6, 53, 4], [59, 10, 53, 8], [59, 11, 53, 9, "begin"], [59, 16, 53, 14], [59, 17, 53, 15], [59, 18, 53, 16], [60, 6, 54, 4], [60, 10, 54, 8], [60, 11, 54, 9, "maxNumberOfPointersSimultaneously"], [60, 44, 54, 42], [60, 47, 54, 45], [60, 48, 54, 46], [61, 6, 55, 4], [61, 10, 55, 8], [61, 11, 55, 9, "delayTimeout"], [61, 23, 55, 21], [61, 26, 55, 24, "setTimeout"], [61, 36, 55, 34], [61, 37, 55, 35], [61, 43, 55, 41], [61, 47, 55, 45], [61, 48, 55, 46, "fail"], [61, 52, 55, 50], [61, 53, 55, 51], [61, 54, 55, 52], [61, 56, 55, 54], [61, 60, 55, 58], [61, 61, 55, 59, "maxDurationMs"], [61, 74, 55, 72], [61, 75, 55, 73], [62, 4, 56, 2], [63, 4, 58, 2, "tryEndFling"], [63, 15, 58, 13, "tryEndFling"], [63, 16, 58, 13], [63, 18, 58, 16], [64, 6, 59, 4], [64, 12, 59, 10, "velocityVector"], [64, 26, 59, 24], [64, 29, 59, 27, "Vector"], [64, 44, 59, 33], [64, 45, 59, 34, "fromVelocity"], [64, 57, 59, 46], [64, 58, 59, 47], [64, 62, 59, 51], [64, 63, 59, 52, "tracker"], [64, 70, 59, 59], [64, 72, 59, 61], [64, 76, 59, 65], [64, 77, 59, 66, "keyPointer"], [64, 87, 59, 76], [64, 88, 59, 77], [65, 6, 61, 4], [65, 12, 61, 10, "getAlignment"], [65, 24, 61, 22], [65, 27, 61, 25, "getAlignment"], [65, 28, 61, 26, "direction"], [65, 37, 61, 35], [65, 39, 61, 37, "minimalAlignmentCosine"], [65, 61, 61, 59], [65, 66, 61, 64], [66, 8, 62, 6], [66, 15, 62, 13], [66, 16, 62, 14, "direction"], [66, 25, 62, 23], [66, 28, 62, 26], [66, 32, 62, 30], [66, 33, 62, 31, "direction"], [66, 42, 62, 40], [66, 48, 62, 46, "direction"], [66, 57, 62, 55], [66, 61, 62, 59, "velocityVector"], [66, 75, 62, 73], [66, 76, 62, 74, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [66, 85, 62, 83], [66, 86, 62, 84, "Vector"], [66, 101, 62, 90], [66, 102, 62, 91, "fromDirection"], [66, 115, 62, 104], [66, 116, 62, 105, "direction"], [66, 125, 62, 114], [66, 126, 62, 115], [66, 128, 62, 117, "minimalAlignmentCosine"], [66, 150, 62, 139], [66, 151, 62, 140], [67, 6, 63, 4], [67, 7, 63, 5], [68, 6, 65, 4], [68, 12, 65, 10, "axialDirectionsList"], [68, 31, 65, 29], [68, 34, 65, 32, "Object"], [68, 40, 65, 38], [68, 41, 65, 39, "values"], [68, 47, 65, 45], [68, 48, 65, 46, "Directions"], [68, 70, 65, 56], [68, 71, 65, 57], [69, 6, 66, 4], [69, 12, 66, 10, "diagonalDirectionsList"], [69, 34, 66, 32], [69, 37, 66, 35, "Object"], [69, 43, 66, 41], [69, 44, 66, 42, "values"], [69, 50, 66, 48], [69, 51, 66, 49, "DiagonalDirections"], [69, 81, 66, 67], [69, 82, 66, 68], [69, 83, 66, 69], [69, 84, 66, 70], [71, 6, 68, 4], [71, 12, 68, 10, "axialAlignmentList"], [71, 30, 68, 28], [71, 33, 68, 31, "axialDirectionsList"], [71, 52, 68, 50], [71, 53, 68, 51, "map"], [71, 56, 68, 54], [71, 57, 68, 55, "direction"], [71, 66, 68, 64], [71, 70, 68, 68, "getAlignment"], [71, 82, 68, 80], [71, 83, 68, 81, "direction"], [71, 92, 68, 90], [71, 94, 68, 92, "AXIAL_DEVIATION_COSINE"], [71, 116, 68, 114], [71, 117, 68, 115], [71, 118, 68, 116], [72, 6, 69, 4], [72, 12, 69, 10, "diagonalAlignmentList"], [72, 33, 69, 31], [72, 36, 69, 34, "diagonalDirectionsList"], [72, 58, 69, 56], [72, 59, 69, 57, "map"], [72, 62, 69, 60], [72, 63, 69, 61, "direction"], [72, 72, 69, 70], [72, 76, 69, 74, "getAlignment"], [72, 88, 69, 86], [72, 89, 69, 87, "direction"], [72, 98, 69, 96], [72, 100, 69, 98, "DIAGONAL_DEVIATION_COSINE"], [72, 125, 69, 123], [72, 126, 69, 124], [72, 127, 69, 125], [73, 6, 70, 4], [73, 12, 70, 10, "isAligned"], [73, 21, 70, 19], [73, 24, 70, 22, "axialAlignmentList"], [73, 42, 70, 40], [73, 43, 70, 41, "some"], [73, 47, 70, 45], [73, 48, 70, 46, "Boolean"], [73, 55, 70, 53], [73, 56, 70, 54], [73, 60, 70, 58, "diagonalAlignmentList"], [73, 81, 70, 79], [73, 82, 70, 80, "some"], [73, 86, 70, 84], [73, 87, 70, 85, "Boolean"], [73, 94, 70, 92], [73, 95, 70, 93], [74, 6, 71, 4], [74, 12, 71, 10, "isFast"], [74, 18, 71, 16], [74, 21, 71, 19, "velocityVector"], [74, 35, 71, 33], [74, 36, 71, 34, "magnitude"], [74, 45, 71, 43], [74, 48, 71, 46], [74, 52, 71, 50], [74, 53, 71, 51, "minVelocity"], [74, 64, 71, 62], [75, 6, 73, 4], [75, 10, 73, 8], [75, 14, 73, 12], [75, 15, 73, 13, "maxNumberOfPointersSimultaneously"], [75, 48, 73, 46], [75, 53, 73, 51], [75, 57, 73, 55], [75, 58, 73, 56, "numberOfPointersRequired"], [75, 82, 73, 80], [75, 86, 73, 84, "isAligned"], [75, 95, 73, 93], [75, 99, 73, 97, "isFast"], [75, 105, 73, 103], [75, 107, 73, 105], [76, 8, 74, 6, "clearTimeout"], [76, 20, 74, 18], [76, 21, 74, 19], [76, 25, 74, 23], [76, 26, 74, 24, "delayTimeout"], [76, 38, 74, 36], [76, 39, 74, 37], [77, 8, 75, 6], [77, 12, 75, 10], [77, 13, 75, 11, "activate"], [77, 21, 75, 19], [77, 22, 75, 20], [77, 23, 75, 21], [78, 8, 76, 6], [78, 15, 76, 13], [78, 19, 76, 17], [79, 6, 77, 4], [80, 6, 79, 4], [80, 13, 79, 11], [80, 18, 79, 16], [81, 4, 80, 2], [82, 4, 82, 2, "endFling"], [82, 12, 82, 10, "endFling"], [82, 13, 82, 10], [82, 15, 82, 13], [83, 6, 83, 4], [83, 10, 83, 8], [83, 11, 83, 9], [83, 15, 83, 13], [83, 16, 83, 14, "tryEndFling"], [83, 27, 83, 25], [83, 28, 83, 26], [83, 29, 83, 27], [83, 31, 83, 29], [84, 8, 84, 6], [84, 12, 84, 10], [84, 13, 84, 11, "fail"], [84, 17, 84, 15], [84, 18, 84, 16], [84, 19, 84, 17], [85, 6, 85, 4], [86, 4, 86, 2], [87, 4, 88, 2, "onPointerDown"], [87, 17, 88, 15, "onPointerDown"], [87, 18, 88, 16, "event"], [87, 23, 88, 21], [87, 25, 88, 23], [88, 6, 89, 4], [88, 10, 89, 8], [88, 11, 89, 9], [88, 15, 89, 13], [88, 16, 89, 14, "isButtonInConfig"], [88, 32, 89, 30], [88, 33, 89, 31, "event"], [88, 38, 89, 36], [88, 39, 89, 37, "button"], [88, 45, 89, 43], [88, 46, 89, 44], [88, 48, 89, 46], [89, 8, 90, 6], [90, 6, 91, 4], [91, 6, 93, 4], [91, 10, 93, 8], [91, 11, 93, 9, "tracker"], [91, 18, 93, 16], [91, 19, 93, 17, "addToTracker"], [91, 31, 93, 29], [91, 32, 93, 30, "event"], [91, 37, 93, 35], [91, 38, 93, 36], [92, 6, 94, 4], [92, 10, 94, 8], [92, 11, 94, 9, "keyPointer"], [92, 21, 94, 19], [92, 24, 94, 22, "event"], [92, 29, 94, 27], [92, 30, 94, 28, "pointerId"], [92, 39, 94, 37], [93, 6, 95, 4], [93, 11, 95, 9], [93, 12, 95, 10, "onPointerDown"], [93, 25, 95, 23], [93, 26, 95, 24, "event"], [93, 31, 95, 29], [93, 32, 95, 30], [94, 6, 96, 4], [94, 10, 96, 8], [94, 11, 96, 9, "newPointerAction"], [94, 27, 96, 25], [94, 28, 96, 26], [94, 29, 96, 27], [95, 6, 97, 4], [95, 10, 97, 8], [95, 11, 97, 9, "tryToSendTouchEvent"], [95, 30, 97, 28], [95, 31, 97, 29, "event"], [95, 36, 97, 34], [95, 37, 97, 35], [96, 4, 98, 2], [97, 4, 100, 2, "onPointerAdd"], [97, 16, 100, 14, "onPointerAdd"], [97, 17, 100, 15, "event"], [97, 22, 100, 20], [97, 24, 100, 22], [98, 6, 101, 4], [98, 10, 101, 8], [98, 11, 101, 9, "tracker"], [98, 18, 101, 16], [98, 19, 101, 17, "addToTracker"], [98, 31, 101, 29], [98, 32, 101, 30, "event"], [98, 37, 101, 35], [98, 38, 101, 36], [99, 6, 102, 4], [99, 11, 102, 9], [99, 12, 102, 10, "onPointerAdd"], [99, 24, 102, 22], [99, 25, 102, 23, "event"], [99, 30, 102, 28], [99, 31, 102, 29], [100, 6, 103, 4], [100, 10, 103, 8], [100, 11, 103, 9, "newPointerAction"], [100, 27, 103, 25], [100, 28, 103, 26], [100, 29, 103, 27], [101, 4, 104, 2], [102, 4, 106, 2, "newPointerAction"], [102, 20, 106, 18, "newPointerAction"], [102, 21, 106, 18], [102, 23, 106, 21], [103, 6, 107, 4], [103, 10, 107, 8], [103, 14, 107, 12], [103, 15, 107, 13, "state"], [103, 20, 107, 18], [103, 25, 107, 23, "State"], [103, 37, 107, 28], [103, 38, 107, 29, "UNDETERMINED"], [103, 50, 107, 41], [103, 52, 107, 43], [104, 8, 108, 6], [104, 12, 108, 10], [104, 13, 108, 11, "startFling"], [104, 23, 108, 21], [104, 24, 108, 22], [104, 25, 108, 23], [105, 6, 109, 4], [106, 6, 111, 4], [106, 10, 111, 8], [106, 14, 111, 12], [106, 15, 111, 13, "state"], [106, 20, 111, 18], [106, 25, 111, 23, "State"], [106, 37, 111, 28], [106, 38, 111, 29, "BEGAN"], [106, 43, 111, 34], [106, 45, 111, 36], [107, 8, 112, 6], [108, 6, 113, 4], [109, 6, 115, 4], [109, 10, 115, 8], [109, 11, 115, 9, "tryEndFling"], [109, 22, 115, 20], [109, 23, 115, 21], [109, 24, 115, 22], [110, 6, 117, 4], [110, 10, 117, 8], [110, 14, 117, 12], [110, 15, 117, 13, "tracker"], [110, 22, 117, 20], [110, 23, 117, 21, "trackedPointersCount"], [110, 43, 117, 41], [110, 46, 117, 44], [110, 50, 117, 48], [110, 51, 117, 49, "maxNumberOfPointersSimultaneously"], [110, 84, 117, 82], [110, 86, 117, 84], [111, 8, 118, 6], [111, 12, 118, 10], [111, 13, 118, 11, "maxNumberOfPointersSimultaneously"], [111, 46, 118, 44], [111, 49, 118, 47], [111, 53, 118, 51], [111, 54, 118, 52, "tracker"], [111, 61, 118, 59], [111, 62, 118, 60, "trackedPointersCount"], [111, 82, 118, 80], [112, 6, 119, 4], [113, 4, 120, 2], [114, 4, 122, 2, "pointerMoveAction"], [114, 21, 122, 19, "pointerMoveAction"], [114, 22, 122, 20, "event"], [114, 27, 122, 25], [114, 29, 122, 27], [115, 6, 123, 4], [115, 10, 123, 8], [115, 11, 123, 9, "tracker"], [115, 18, 123, 16], [115, 19, 123, 17, "track"], [115, 24, 123, 22], [115, 25, 123, 23, "event"], [115, 30, 123, 28], [115, 31, 123, 29], [116, 6, 125, 4], [116, 10, 125, 8], [116, 14, 125, 12], [116, 15, 125, 13, "state"], [116, 20, 125, 18], [116, 25, 125, 23, "State"], [116, 37, 125, 28], [116, 38, 125, 29, "BEGAN"], [116, 43, 125, 34], [116, 45, 125, 36], [117, 8, 126, 6], [118, 6, 127, 4], [119, 6, 129, 4], [119, 10, 129, 8], [119, 11, 129, 9, "tryEndFling"], [119, 22, 129, 20], [119, 23, 129, 21], [119, 24, 129, 22], [120, 4, 130, 2], [121, 4, 132, 2, "onPointerMove"], [121, 17, 132, 15, "onPointerMove"], [121, 18, 132, 16, "event"], [121, 23, 132, 21], [121, 25, 132, 23], [122, 6, 133, 4], [122, 10, 133, 8], [122, 11, 133, 9, "pointerMoveAction"], [122, 28, 133, 26], [122, 29, 133, 27, "event"], [122, 34, 133, 32], [122, 35, 133, 33], [123, 6, 134, 4], [123, 11, 134, 9], [123, 12, 134, 10, "onPointerMove"], [123, 25, 134, 23], [123, 26, 134, 24, "event"], [123, 31, 134, 29], [123, 32, 134, 30], [124, 4, 135, 2], [125, 4, 137, 2, "onPointerOutOfBounds"], [125, 24, 137, 22, "onPointerOutOfBounds"], [125, 25, 137, 23, "event"], [125, 30, 137, 28], [125, 32, 137, 30], [126, 6, 138, 4], [126, 10, 138, 8], [126, 11, 138, 9, "pointerMoveAction"], [126, 28, 138, 26], [126, 29, 138, 27, "event"], [126, 34, 138, 32], [126, 35, 138, 33], [127, 6, 139, 4], [127, 11, 139, 9], [127, 12, 139, 10, "onPointerOutOfBounds"], [127, 32, 139, 30], [127, 33, 139, 31, "event"], [127, 38, 139, 36], [127, 39, 139, 37], [128, 4, 140, 2], [129, 4, 142, 2, "onPointerUp"], [129, 15, 142, 13, "onPointerUp"], [129, 16, 142, 14, "event"], [129, 21, 142, 19], [129, 23, 142, 21], [130, 6, 143, 4], [130, 11, 143, 9], [130, 12, 143, 10, "onPointerUp"], [130, 23, 143, 21], [130, 24, 143, 22, "event"], [130, 29, 143, 27], [130, 30, 143, 28], [131, 6, 144, 4], [131, 10, 144, 8], [131, 11, 144, 9, "onUp"], [131, 15, 144, 13], [131, 16, 144, 14, "event"], [131, 21, 144, 19], [131, 22, 144, 20], [132, 6, 145, 4], [132, 10, 145, 8], [132, 11, 145, 9, "keyPointer"], [132, 21, 145, 19], [132, 24, 145, 22, "NaN"], [132, 27, 145, 25], [133, 4, 146, 2], [134, 4, 148, 2, "onPointerRemove"], [134, 19, 148, 17, "onPointerRemove"], [134, 20, 148, 18, "event"], [134, 25, 148, 23], [134, 27, 148, 25], [135, 6, 149, 4], [135, 11, 149, 9], [135, 12, 149, 10, "onPointerRemove"], [135, 27, 149, 25], [135, 28, 149, 26, "event"], [135, 33, 149, 31], [135, 34, 149, 32], [136, 6, 150, 4], [136, 10, 150, 8], [136, 11, 150, 9, "onUp"], [136, 15, 150, 13], [136, 16, 150, 14, "event"], [136, 21, 150, 19], [136, 22, 150, 20], [137, 4, 151, 2], [138, 4, 153, 2, "onUp"], [138, 8, 153, 6, "onUp"], [138, 9, 153, 7, "event"], [138, 14, 153, 12], [138, 16, 153, 14], [139, 6, 154, 4], [139, 10, 154, 8], [139, 14, 154, 12], [139, 15, 154, 13, "state"], [139, 20, 154, 18], [139, 25, 154, 23, "State"], [139, 37, 154, 28], [139, 38, 154, 29, "BEGAN"], [139, 43, 154, 34], [139, 45, 154, 36], [140, 8, 155, 6], [140, 12, 155, 10], [140, 13, 155, 11, "endFling"], [140, 21, 155, 19], [140, 22, 155, 20], [140, 23, 155, 21], [141, 6, 156, 4], [142, 6, 158, 4], [142, 10, 158, 8], [142, 11, 158, 9, "tracker"], [142, 18, 158, 16], [142, 19, 158, 17, "removeFromTracker"], [142, 36, 158, 34], [142, 37, 158, 35, "event"], [142, 42, 158, 40], [142, 43, 158, 41, "pointerId"], [142, 52, 158, 50], [142, 53, 158, 51], [143, 4, 159, 2], [144, 4, 161, 2, "activate"], [144, 12, 161, 10, "activate"], [144, 13, 161, 11, "force"], [144, 18, 161, 16], [144, 20, 161, 18], [145, 6, 162, 4], [145, 11, 162, 9], [145, 12, 162, 10, "activate"], [145, 20, 162, 18], [145, 21, 162, 19, "force"], [145, 26, 162, 24], [145, 27, 162, 25], [146, 6, 163, 4], [146, 10, 163, 8], [146, 11, 163, 9, "end"], [146, 14, 163, 12], [146, 15, 163, 13], [146, 16, 163, 14], [147, 4, 164, 2], [148, 4, 166, 2, "resetConfig"], [148, 15, 166, 13, "resetConfig"], [148, 16, 166, 13], [148, 18, 166, 16], [149, 6, 167, 4], [149, 11, 167, 9], [149, 12, 167, 10, "resetConfig"], [149, 23, 167, 21], [149, 24, 167, 22], [149, 25, 167, 23], [150, 6, 168, 4], [150, 10, 168, 8], [150, 11, 168, 9, "numberOfPointersRequired"], [150, 35, 168, 33], [150, 38, 168, 36, "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED"], [150, 72, 168, 70], [151, 6, 169, 4], [151, 10, 169, 8], [151, 11, 169, 9, "direction"], [151, 20, 169, 18], [151, 23, 169, 21, "DEFAULT_DIRECTION"], [151, 40, 169, 38], [152, 4, 170, 2], [153, 2, 172, 0], [154, 2, 172, 1, "exports"], [154, 9, 172, 1], [154, 10, 172, 1, "default"], [154, 17, 172, 1], [154, 20, 172, 1, "FlingGestureHandler"], [154, 39, 172, 1], [155, 0, 172, 1], [155, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "FlingGestureHandler", "constructor", "updateGestureConfig", "startFling", "setTimeout$argument_0", "tryEndFling", "getAlignment", "axialDirectionsList.map$argument_0", "diagonalDirectionsList.map$argument_0", "endFling", "onPointerDown", "onPointerAdd", "newPointerAction", "pointerMoveAction", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onPointerRemove", "onUp", "activate", "resetConfig"], "mappings": "AAA,iNC;eCc;ECC;GDgB;EEE;GFgB;EGE;mCCG,iBD;GHC;EKE;yBCG;KDE;uDEK,4DF;6DGC,+DH;GLW;ESE;GTI;EUE;GVU;EWE;GXI;EYE;GZc;EaE;GbQ;EcE;GdG;EeE;GfG;EgBE;GhBI;EiBE;GjBG;EkBE;GlBM;EmBE;GnBG;EoBE;GpBI;CDE"}}, "type": "js/module"}]}