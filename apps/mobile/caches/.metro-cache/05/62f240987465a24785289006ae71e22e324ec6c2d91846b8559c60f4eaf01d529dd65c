{"dependencies": [], "output": [{"data": {"code": "__d(function(global, require, _importDefaultUnused, _importAllUnused, module, exports, _dependencyMapUnused) {\n  module.exports = {\n  \"name\": \"react-native-url-polyfill\",\n  \"version\": \"2.0.0\",\n  \"description\": \"A lightweight and trustworthy URL polyfill for React Native\",\n  \"keywords\": [\n    \"URL\",\n    \"URLSearchParams\",\n    \"polyfill\",\n    \"react native\",\n    \"whatwg-url\"\n  ],\n  \"bugs\": {\n    \"url\": \"https://github.com/charpeni/react-native-url-polyfill/issues\"\n  },\n  \"homepage\": \"https://github.com/charpeni/react-native-url-polyfill\",\n  \"readme\": \"https://github.com/charpeni/react-native-url-polyfill#readme\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/charpeni/react-native-url-polyfill.git\"\n  },\n  \"main\": \"index.js\",\n  \"types\": \"index.d.ts\",\n  \"scripts\": {\n    \"test\": \"jest\",\n    \"lint\": \"eslint .\",\n    \"prepare\": \"husky install\",\n    \"bundle-size\": \"node scripts/bundle-size\"\n  },\n  \"author\": \"<PERSON>entier <<EMAIL>>\",\n  \"license\": \"MIT\",\n  \"dependencies\": {\n    \"whatwg-url-without-unicode\": \"8.0.0-3\"\n  },\n  \"devDependencies\": {\n    \"@react-native-community/eslint-config\": \"3.2.0\",\n    \"detox\": \"20.9.1\",\n    \"eslint\": \"8.44.0\",\n    \"eslint-plugin-prettier\": \"4.2.1\",\n    \"husky\": \"8.0.3\",\n    \"jest\": \"29.5.0\",\n    \"lint-staged\": \"13.2.3\",\n    \"metro-react-native-babel-preset\": \"0.76.7\",\n    \"nanoid\": \"3.3.6\",\n    \"prettier\": \"2.8.8\",\n    \"react\": \"18.2.0\",\n    \"react-native\": \"0.72.1\",\n    \"react-native-bundle-scale\": \"1.1.0\",\n    \"typescript\": \"5.1.6\"\n  },\n  \"peerDependencies\": {\n    \"react-native\": \"*\"\n  },\n  \"jest\": {\n    \"preset\": \"react-native\",\n    \"testPathIgnorePatterns\": [\n      \"/node_modules/\",\n      \"./platforms/\"\n    ]\n  },\n  \"lint-staged\": {\n    \"*.js\": [\n      \"eslint --fix\"\n    ]\n  }\n}\n;\n});", "lineCount": 68, "map": [[68, 3]], "functionMap": null}, "type": "js/module"}]}