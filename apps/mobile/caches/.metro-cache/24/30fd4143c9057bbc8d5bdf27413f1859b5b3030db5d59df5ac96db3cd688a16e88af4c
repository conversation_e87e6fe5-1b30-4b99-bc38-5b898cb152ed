{"dependencies": [{"name": "../../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 18}, "end": {"line": 3, "column": 41, "index": 59}}], "key": "ByXat9lt9duIJLDmSeH0V+tRq1s=", "exportNames": ["*"]}}, {"name": "react-native-reanimated", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 15, "index": 98}, "end": {"line": 7, "column": 49, "index": 132}}], "key": "+aUP6OdvebG47hiJSteO076+5ZE=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Reanimated = void 0;\n  var _utils = require(_dependencyMap[0], \"../../utils\");\n  var _Reanimated;\n  let Reanimated = exports.Reanimated = void 0;\n  try {\n    exports.Reanimated = Reanimated = require(_dependencyMap[1], \"react-native-reanimated\");\n  } catch (e) {\n    // When 'react-native-reanimated' is not available we want to quietly continue\n    // @ts-ignore TS demands the variable to be initialized\n    exports.Reanimated = Reanimated = undefined;\n  }\n  if (!((_Reanimated = Reanimated) !== null && _Reanimated !== void 0 && _Reanimated.useSharedValue)) {\n    // @ts-ignore Make sure the loaded module is actually Reanimated, if it's not\n    // reset the module to undefined so we can fallback to the default implementation\n    exports.Reanimated = Reanimated = undefined;\n  }\n  const _worklet_7298969188432_init_data = {\n    code: \"function reactNativeGestureHandler_reanimatedWrapperJs1(){const{tagMessage}=this.__closure;console.warn(tagMessage('Please use newer version of react-native-reanimated in order to control state of the gestures.'));}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/reanimatedWrapper.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeGestureHandler_reanimatedWrapperJs1\\\",\\\"tagMessage\\\",\\\"__closure\\\",\\\"console\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/reanimatedWrapper.js\\\"],\\\"mappings\\\":\\\"AAqB+B,SAAAA,8CAAMA,CAAA,QAAAC,UAAA,OAAAC,SAAA,CAGjCC,OAAO,CAACC,IAAI,CAACH,UAAU,CAAC,gGAAgG,CAAC,CAAC,CAC5H\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  if (Reanimated !== undefined && !Reanimated.setGestureState) {\n    // The loaded module is Reanimated but it doesn't have the setGestureState defined\n    Reanimated.setGestureState = function () {\n      const _e = [new global.Error(), -2, -27];\n      const reactNativeGestureHandler_reanimatedWrapperJs1 = function () {\n        console.warn((0, _utils.tagMessage)('Please use newer version of react-native-reanimated in order to control state of the gestures.'));\n      };\n      reactNativeGestureHandler_reanimatedWrapperJs1.__closure = {\n        tagMessage: _utils.tagMessage\n      };\n      reactNativeGestureHandler_reanimatedWrapperJs1.__workletHash = 7298969188432;\n      reactNativeGestureHandler_reanimatedWrapperJs1.__initData = _worklet_7298969188432_init_data;\n      reactNativeGestureHandler_reanimatedWrapperJs1.__stackDetails = _e;\n      return reactNativeGestureHandler_reanimatedWrapperJs1;\n    }();\n  }\n});", "lineCount": 43, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_utils"], [6, 12, 3, 0], [6, 15, 3, 0, "require"], [6, 22, 3, 0], [6, 23, 3, 0, "_dependencyMap"], [6, 37, 3, 0], [7, 2, 1, 0], [7, 6, 1, 4, "_Reanimated"], [7, 17, 1, 15], [8, 2, 4, 0], [8, 6, 4, 4, "Reanimated"], [8, 16, 4, 14], [8, 19, 4, 14, "exports"], [8, 26, 4, 14], [8, 27, 4, 14, "Reanimated"], [8, 37, 4, 14], [9, 2, 6, 0], [9, 6, 6, 4], [10, 4, 7, 2, "exports"], [10, 11, 7, 2], [10, 12, 7, 2, "Reanimated"], [10, 22, 7, 2], [10, 25, 7, 2, "Reanimated"], [10, 35, 7, 12], [10, 38, 7, 15, "require"], [10, 45, 7, 22], [10, 46, 7, 22, "_dependencyMap"], [10, 60, 7, 22], [10, 90, 7, 48], [10, 91, 7, 49], [11, 2, 8, 0], [11, 3, 8, 1], [11, 4, 8, 2], [11, 11, 8, 9, "e"], [11, 12, 8, 10], [11, 14, 8, 12], [12, 4, 9, 2], [13, 4, 10, 2], [14, 4, 11, 2, "exports"], [14, 11, 11, 2], [14, 12, 11, 2, "Reanimated"], [14, 22, 11, 2], [14, 25, 11, 2, "Reanimated"], [14, 35, 11, 12], [14, 38, 11, 15, "undefined"], [14, 47, 11, 24], [15, 2, 12, 0], [16, 2, 14, 0], [16, 6, 14, 4], [16, 8, 14, 6], [16, 9, 14, 7, "_Reanimated"], [16, 20, 14, 18], [16, 23, 14, 21, "Reanimated"], [16, 33, 14, 31], [16, 39, 14, 37], [16, 43, 14, 41], [16, 47, 14, 45, "_Reanimated"], [16, 58, 14, 56], [16, 63, 14, 61], [16, 68, 14, 66], [16, 69, 14, 67], [16, 73, 14, 71, "_Reanimated"], [16, 84, 14, 82], [16, 85, 14, 83, "useSharedValue"], [16, 99, 14, 97], [16, 100, 14, 98], [16, 102, 14, 100], [17, 4, 15, 2], [18, 4, 16, 2], [19, 4, 17, 2, "exports"], [19, 11, 17, 2], [19, 12, 17, 2, "Reanimated"], [19, 22, 17, 2], [19, 25, 17, 2, "Reanimated"], [19, 35, 17, 12], [19, 38, 17, 15, "undefined"], [19, 47, 17, 24], [20, 2, 18, 0], [21, 2, 18, 1], [21, 8, 18, 1, "_worklet_7298969188432_init_data"], [21, 40, 18, 1], [22, 4, 18, 1, "code"], [22, 8, 18, 1], [23, 4, 18, 1, "location"], [23, 12, 18, 1], [24, 4, 18, 1, "sourceMap"], [24, 13, 18, 1], [25, 4, 18, 1, "version"], [25, 11, 18, 1], [26, 2, 18, 1], [27, 2, 20, 0], [27, 6, 20, 4, "Reanimated"], [27, 16, 20, 14], [27, 21, 20, 19, "undefined"], [27, 30, 20, 28], [27, 34, 20, 32], [27, 35, 20, 33, "Reanimated"], [27, 45, 20, 43], [27, 46, 20, 44, "setGestureState"], [27, 61, 20, 59], [27, 63, 20, 61], [28, 4, 21, 2], [29, 4, 22, 2, "Reanimated"], [29, 14, 22, 12], [29, 15, 22, 13, "setGestureState"], [29, 30, 22, 28], [29, 33, 22, 31], [30, 6, 22, 31], [30, 12, 22, 31, "_e"], [30, 14, 22, 31], [30, 22, 22, 31, "global"], [30, 28, 22, 31], [30, 29, 22, 31, "Error"], [30, 34, 22, 31], [31, 6, 22, 31], [31, 12, 22, 31, "reactNativeGestureHandler_reanimatedWrapperJs1"], [31, 58, 22, 31], [31, 70, 22, 31, "reactNativeGestureHandler_reanimatedWrapperJs1"], [31, 71, 22, 31], [31, 73, 22, 37], [32, 8, 25, 4, "console"], [32, 15, 25, 11], [32, 16, 25, 12, "warn"], [32, 20, 25, 16], [32, 21, 25, 17], [32, 25, 25, 17, "tagMessage"], [32, 42, 25, 27], [32, 44, 25, 28], [32, 140, 25, 124], [32, 141, 25, 125], [32, 142, 25, 126], [33, 6, 26, 2], [33, 7, 26, 3], [34, 6, 26, 3, "reactNativeGestureHandler_reanimatedWrapperJs1"], [34, 52, 26, 3], [34, 53, 26, 3, "__closure"], [34, 62, 26, 3], [35, 8, 26, 3, "tagMessage"], [35, 18, 26, 3], [35, 20, 25, 17, "tagMessage"], [36, 6, 25, 27], [37, 6, 25, 27, "reactNativeGestureHandler_reanimatedWrapperJs1"], [37, 52, 25, 27], [37, 53, 25, 27, "__workletHash"], [37, 66, 25, 27], [38, 6, 25, 27, "reactNativeGestureHandler_reanimatedWrapperJs1"], [38, 52, 25, 27], [38, 53, 25, 27, "__initData"], [38, 63, 25, 27], [38, 66, 25, 27, "_worklet_7298969188432_init_data"], [38, 98, 25, 27], [39, 6, 25, 27, "reactNativeGestureHandler_reanimatedWrapperJs1"], [39, 52, 25, 27], [39, 53, 25, 27, "__stackDetails"], [39, 67, 25, 27], [39, 70, 25, 27, "_e"], [39, 72, 25, 27], [40, 6, 25, 27], [40, 13, 25, 27, "reactNativeGestureHandler_reanimatedWrapperJs1"], [40, 59, 25, 27], [41, 4, 25, 27], [41, 5, 22, 31], [41, 7, 26, 3], [42, 2, 27, 0], [43, 0, 27, 1], [43, 3]], "functionMap": {"names": ["<global>", "Reanimated.setGestureState"], "mappings": "AAA;+BCqB;GDI"}}, "type": "js/module"}]}