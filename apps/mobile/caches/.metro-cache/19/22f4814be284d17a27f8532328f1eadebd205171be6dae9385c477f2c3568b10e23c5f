{"dependencies": [{"name": "./ReactNativeFeatureFlagsBase", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 27, "column": 39}}], "key": "EuxqjeUPgW72VOw56G9CoEF4ee8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useTurboModules = exports.useTurboModuleInterop = exports.useShadowNodeStateOnClone = exports.useRefsForTextInputState = exports.useRawPropsJsiValue = exports.useOptimizedEventBatchingOnAndroid = exports.useNativeViewConfigsInBridgelessMode = exports.useFabricInterop = exports.useEditTextStockAndroidFocusBehavior = exports.useAlwaysAvailableJSErrorHandling = exports.updateRuntimeShadowNodeReferencesOnCommit = exports.traceTurboModulePromiseRejectionsOnAndroid = exports.throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS = exports.shouldUseSetNativePropsInFabric = exports.shouldUseRemoveClippedSubviewsAsDefaultOnIOS = exports.shouldUseAnimatedObjectForTransform = exports.scheduleAnimatedCleanupInMicrotask = exports.removeTurboModuleManagerDelegateMutex = exports.override = exports.lazyAnimationCallbacks = exports.jsOnlyTestFlag = exports.isLayoutAnimationEnabled = exports.fuseboxNetworkInspectionEnabled = exports.fuseboxEnabledRelease = exports.fixVirtualizeListCollapseWindowSize = exports.fixMountingCoordinatorReportedPendingTransactionsOnAndroid = exports.fixMappingOfEventPrioritiesBetweenFabricAndReact = exports.fixDifferentiatorEmittingUpdatesWithWrongParentTag = exports.excludeYogaFromRawProps = exports.enableViewRecyclingForView = exports.enableViewRecyclingForText = exports.enableViewRecycling = exports.enableViewCulling = exports.enableUIConsistency = exports.enableSynchronousStateUpdates = exports.enableReportEventPaintTime = exports.enablePropsUpdateReconciliationAndroid = exports.enablePreciseSchedulingForPremountItemsOnAndroid = exports.enableNewBackgroundAndBorderDrawables = exports.enableNativeCSSParsing = exports.enableLongTaskAPI = exports.enableLayoutAnimationsOnIOS = exports.enableLayoutAnimationsOnAndroid = exports.enableJSRuntimeGCOnMemoryPressureOnIOS = exports.enableImagePrefetchingAndroid = exports.enableIOSViewClipToPaddingBox = exports.enableFabricRenderer = exports.enableFabricLogs = exports.enableEagerRootViewAttachment = exports.enableDOMDocumentAPI = exports.enableCppPropsIteratorSetter = exports.enableBridgelessArchitecture = exports.enableAnimatedClearImmediateFix = exports.enableAccumulatedUpdatesInRawPropsAndroid = exports.enableAccessToHostTreeInFabric = exports.disableMountItemReorderingAndroid = exports.disableInteractionManager = exports.commonTestFlagWithoutNativeImplementation = exports.commonTestFlag = exports.avoidStateUpdateInAnimatedPropsMemo = exports.animatedShouldUseSingleOp = exports.animatedShouldDebounceQueueFlush = void 0;\n  var _ReactNativeFeatureFlagsBase = require(_dependencyMap[0], \"./ReactNativeFeatureFlagsBase\");\n  var jsOnlyTestFlag = exports.jsOnlyTestFlag = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('jsOnlyTestFlag', false);\n  var animatedShouldDebounceQueueFlush = exports.animatedShouldDebounceQueueFlush = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('animatedShouldDebounceQueueFlush', false);\n  var animatedShouldUseSingleOp = exports.animatedShouldUseSingleOp = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('animatedShouldUseSingleOp', false);\n  var avoidStateUpdateInAnimatedPropsMemo = exports.avoidStateUpdateInAnimatedPropsMemo = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('avoidStateUpdateInAnimatedPropsMemo', false);\n  var disableInteractionManager = exports.disableInteractionManager = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('disableInteractionManager', false);\n  var enableAccessToHostTreeInFabric = exports.enableAccessToHostTreeInFabric = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('enableAccessToHostTreeInFabric', false);\n  var enableAnimatedClearImmediateFix = exports.enableAnimatedClearImmediateFix = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('enableAnimatedClearImmediateFix', true);\n  var enableDOMDocumentAPI = exports.enableDOMDocumentAPI = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('enableDOMDocumentAPI', false);\n  var fixVirtualizeListCollapseWindowSize = exports.fixVirtualizeListCollapseWindowSize = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('fixVirtualizeListCollapseWindowSize', false);\n  var isLayoutAnimationEnabled = exports.isLayoutAnimationEnabled = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('isLayoutAnimationEnabled', true);\n  var scheduleAnimatedCleanupInMicrotask = exports.scheduleAnimatedCleanupInMicrotask = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('scheduleAnimatedCleanupInMicrotask', false);\n  var shouldUseAnimatedObjectForTransform = exports.shouldUseAnimatedObjectForTransform = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('shouldUseAnimatedObjectForTransform', false);\n  var shouldUseRemoveClippedSubviewsAsDefaultOnIOS = exports.shouldUseRemoveClippedSubviewsAsDefaultOnIOS = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('shouldUseRemoveClippedSubviewsAsDefaultOnIOS', false);\n  var shouldUseSetNativePropsInFabric = exports.shouldUseSetNativePropsInFabric = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('shouldUseSetNativePropsInFabric', true);\n  var useRefsForTextInputState = exports.useRefsForTextInputState = (0, _ReactNativeFeatureFlagsBase.createJavaScriptFlagGetter)('useRefsForTextInputState', false);\n  var commonTestFlag = exports.commonTestFlag = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('commonTestFlag', false);\n  var commonTestFlagWithoutNativeImplementation = exports.commonTestFlagWithoutNativeImplementation = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('commonTestFlagWithoutNativeImplementation', false);\n  var disableMountItemReorderingAndroid = exports.disableMountItemReorderingAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('disableMountItemReorderingAndroid', false);\n  var enableAccumulatedUpdatesInRawPropsAndroid = exports.enableAccumulatedUpdatesInRawPropsAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableAccumulatedUpdatesInRawPropsAndroid', false);\n  var enableBridgelessArchitecture = exports.enableBridgelessArchitecture = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableBridgelessArchitecture', false);\n  var enableCppPropsIteratorSetter = exports.enableCppPropsIteratorSetter = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableCppPropsIteratorSetter', false);\n  var enableEagerRootViewAttachment = exports.enableEagerRootViewAttachment = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableEagerRootViewAttachment', false);\n  var enableFabricLogs = exports.enableFabricLogs = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableFabricLogs', false);\n  var enableFabricRenderer = exports.enableFabricRenderer = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableFabricRenderer', false);\n  var enableIOSViewClipToPaddingBox = exports.enableIOSViewClipToPaddingBox = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableIOSViewClipToPaddingBox', false);\n  var enableImagePrefetchingAndroid = exports.enableImagePrefetchingAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableImagePrefetchingAndroid', false);\n  var enableJSRuntimeGCOnMemoryPressureOnIOS = exports.enableJSRuntimeGCOnMemoryPressureOnIOS = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableJSRuntimeGCOnMemoryPressureOnIOS', false);\n  var enableLayoutAnimationsOnAndroid = exports.enableLayoutAnimationsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableLayoutAnimationsOnAndroid', false);\n  var enableLayoutAnimationsOnIOS = exports.enableLayoutAnimationsOnIOS = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableLayoutAnimationsOnIOS', true);\n  var enableLongTaskAPI = exports.enableLongTaskAPI = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableLongTaskAPI', false);\n  var enableNativeCSSParsing = exports.enableNativeCSSParsing = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableNativeCSSParsing', false);\n  var enableNewBackgroundAndBorderDrawables = exports.enableNewBackgroundAndBorderDrawables = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableNewBackgroundAndBorderDrawables', false);\n  var enablePreciseSchedulingForPremountItemsOnAndroid = exports.enablePreciseSchedulingForPremountItemsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enablePreciseSchedulingForPremountItemsOnAndroid', false);\n  var enablePropsUpdateReconciliationAndroid = exports.enablePropsUpdateReconciliationAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enablePropsUpdateReconciliationAndroid', false);\n  var enableReportEventPaintTime = exports.enableReportEventPaintTime = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableReportEventPaintTime', false);\n  var enableSynchronousStateUpdates = exports.enableSynchronousStateUpdates = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableSynchronousStateUpdates', false);\n  var enableUIConsistency = exports.enableUIConsistency = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableUIConsistency', false);\n  var enableViewCulling = exports.enableViewCulling = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewCulling', false);\n  var enableViewRecycling = exports.enableViewRecycling = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewRecycling', false);\n  var enableViewRecyclingForText = exports.enableViewRecyclingForText = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewRecyclingForText', true);\n  var enableViewRecyclingForView = exports.enableViewRecyclingForView = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('enableViewRecyclingForView', true);\n  var excludeYogaFromRawProps = exports.excludeYogaFromRawProps = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('excludeYogaFromRawProps', false);\n  var fixDifferentiatorEmittingUpdatesWithWrongParentTag = exports.fixDifferentiatorEmittingUpdatesWithWrongParentTag = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fixDifferentiatorEmittingUpdatesWithWrongParentTag', true);\n  var fixMappingOfEventPrioritiesBetweenFabricAndReact = exports.fixMappingOfEventPrioritiesBetweenFabricAndReact = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fixMappingOfEventPrioritiesBetweenFabricAndReact', false);\n  var fixMountingCoordinatorReportedPendingTransactionsOnAndroid = exports.fixMountingCoordinatorReportedPendingTransactionsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fixMountingCoordinatorReportedPendingTransactionsOnAndroid', false);\n  var fuseboxEnabledRelease = exports.fuseboxEnabledRelease = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fuseboxEnabledRelease', false);\n  var fuseboxNetworkInspectionEnabled = exports.fuseboxNetworkInspectionEnabled = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('fuseboxNetworkInspectionEnabled', false);\n  var lazyAnimationCallbacks = exports.lazyAnimationCallbacks = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('lazyAnimationCallbacks', false);\n  var removeTurboModuleManagerDelegateMutex = exports.removeTurboModuleManagerDelegateMutex = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('removeTurboModuleManagerDelegateMutex', false);\n  var throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS = exports.throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS', false);\n  var traceTurboModulePromiseRejectionsOnAndroid = exports.traceTurboModulePromiseRejectionsOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('traceTurboModulePromiseRejectionsOnAndroid', false);\n  var updateRuntimeShadowNodeReferencesOnCommit = exports.updateRuntimeShadowNodeReferencesOnCommit = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('updateRuntimeShadowNodeReferencesOnCommit', false);\n  var useAlwaysAvailableJSErrorHandling = exports.useAlwaysAvailableJSErrorHandling = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useAlwaysAvailableJSErrorHandling', false);\n  var useEditTextStockAndroidFocusBehavior = exports.useEditTextStockAndroidFocusBehavior = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useEditTextStockAndroidFocusBehavior', true);\n  var useFabricInterop = exports.useFabricInterop = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useFabricInterop', false);\n  var useNativeViewConfigsInBridgelessMode = exports.useNativeViewConfigsInBridgelessMode = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useNativeViewConfigsInBridgelessMode', false);\n  var useOptimizedEventBatchingOnAndroid = exports.useOptimizedEventBatchingOnAndroid = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useOptimizedEventBatchingOnAndroid', false);\n  var useRawPropsJsiValue = exports.useRawPropsJsiValue = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useRawPropsJsiValue', false);\n  var useShadowNodeStateOnClone = exports.useShadowNodeStateOnClone = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useShadowNodeStateOnClone', false);\n  var useTurboModuleInterop = exports.useTurboModuleInterop = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useTurboModuleInterop', false);\n  var useTurboModules = exports.useTurboModules = (0, _ReactNativeFeatureFlagsBase.createNativeFlagGetter)('useTurboModules', false);\n  var override = exports.override = _ReactNativeFeatureFlagsBase.setOverrides;\n});", "lineCount": 69, "map": [[6, 2, 21, 0], [6, 6, 21, 0, "_ReactNativeFeatureFlagsBase"], [6, 34, 21, 0], [6, 37, 21, 0, "require"], [6, 44, 21, 0], [6, 45, 21, 0, "_dependencyMap"], [6, 59, 21, 0], [7, 2, 102, 7], [7, 6, 102, 13, "jsOnlyTestFlag"], [7, 20, 102, 44], [7, 23, 102, 44, "exports"], [7, 30, 102, 44], [7, 31, 102, 44, "jsOnlyTestFlag"], [7, 45, 102, 44], [7, 48, 102, 47], [7, 52, 102, 47, "createJavaScriptFlagGetter"], [7, 107, 102, 73], [7, 109, 102, 74], [7, 125, 102, 90], [7, 127, 102, 92], [7, 132, 102, 97], [7, 133, 102, 98], [8, 2, 107, 7], [8, 6, 107, 13, "animatedShouldDebounceQueueFlush"], [8, 38, 107, 62], [8, 41, 107, 62, "exports"], [8, 48, 107, 62], [8, 49, 107, 62, "animatedShouldDebounceQueueFlush"], [8, 81, 107, 62], [8, 84, 107, 65], [8, 88, 107, 65, "createJavaScriptFlagGetter"], [8, 143, 107, 91], [8, 145, 107, 92], [8, 179, 107, 126], [8, 181, 107, 128], [8, 186, 107, 133], [8, 187, 107, 134], [9, 2, 112, 7], [9, 6, 112, 13, "animatedShouldUseSingleOp"], [9, 31, 112, 55], [9, 34, 112, 55, "exports"], [9, 41, 112, 55], [9, 42, 112, 55, "animatedShouldUseSingleOp"], [9, 67, 112, 55], [9, 70, 112, 58], [9, 74, 112, 58, "createJavaScriptFlagGetter"], [9, 129, 112, 84], [9, 131, 112, 85], [9, 158, 112, 112], [9, 160, 112, 114], [9, 165, 112, 119], [9, 166, 112, 120], [10, 2, 117, 7], [10, 6, 117, 13, "avoidStateUpdateInAnimatedPropsMemo"], [10, 41, 117, 65], [10, 44, 117, 65, "exports"], [10, 51, 117, 65], [10, 52, 117, 65, "avoidStateUpdateInAnimatedPropsMemo"], [10, 87, 117, 65], [10, 90, 117, 68], [10, 94, 117, 68, "createJavaScriptFlagGetter"], [10, 149, 117, 94], [10, 151, 117, 95], [10, 188, 117, 132], [10, 190, 117, 134], [10, 195, 117, 139], [10, 196, 117, 140], [11, 2, 122, 7], [11, 6, 122, 13, "disableInteractionManager"], [11, 31, 122, 55], [11, 34, 122, 55, "exports"], [11, 41, 122, 55], [11, 42, 122, 55, "disableInteractionManager"], [11, 67, 122, 55], [11, 70, 122, 58], [11, 74, 122, 58, "createJavaScriptFlagGetter"], [11, 129, 122, 84], [11, 131, 122, 85], [11, 158, 122, 112], [11, 160, 122, 114], [11, 165, 122, 119], [11, 166, 122, 120], [12, 2, 127, 7], [12, 6, 127, 13, "enableAccessToHostTreeInFabric"], [12, 36, 127, 60], [12, 39, 127, 60, "exports"], [12, 46, 127, 60], [12, 47, 127, 60, "enableAccessToHostTreeInFabric"], [12, 77, 127, 60], [12, 80, 127, 63], [12, 84, 127, 63, "createJavaScriptFlagGetter"], [12, 139, 127, 89], [12, 141, 127, 90], [12, 173, 127, 122], [12, 175, 127, 124], [12, 180, 127, 129], [12, 181, 127, 130], [13, 2, 132, 7], [13, 6, 132, 13, "enableAnimatedClearImmediateFix"], [13, 37, 132, 61], [13, 40, 132, 61, "exports"], [13, 47, 132, 61], [13, 48, 132, 61, "enableAnimatedClearImmediateFix"], [13, 79, 132, 61], [13, 82, 132, 64], [13, 86, 132, 64, "createJavaScriptFlagGetter"], [13, 141, 132, 90], [13, 143, 132, 91], [13, 176, 132, 124], [13, 178, 132, 126], [13, 182, 132, 130], [13, 183, 132, 131], [14, 2, 137, 7], [14, 6, 137, 13, "enableDOMDocumentAPI"], [14, 26, 137, 50], [14, 29, 137, 50, "exports"], [14, 36, 137, 50], [14, 37, 137, 50, "enableDOMDocumentAPI"], [14, 57, 137, 50], [14, 60, 137, 53], [14, 64, 137, 53, "createJavaScriptFlagGetter"], [14, 119, 137, 79], [14, 121, 137, 80], [14, 143, 137, 102], [14, 145, 137, 104], [14, 150, 137, 109], [14, 151, 137, 110], [15, 2, 142, 7], [15, 6, 142, 13, "fixVirtualizeListCollapseWindowSize"], [15, 41, 142, 65], [15, 44, 142, 65, "exports"], [15, 51, 142, 65], [15, 52, 142, 65, "fixVirtualizeListCollapseWindowSize"], [15, 87, 142, 65], [15, 90, 142, 68], [15, 94, 142, 68, "createJavaScriptFlagGetter"], [15, 149, 142, 94], [15, 151, 142, 95], [15, 188, 142, 132], [15, 190, 142, 134], [15, 195, 142, 139], [15, 196, 142, 140], [16, 2, 147, 7], [16, 6, 147, 13, "isLayoutAnimationEnabled"], [16, 30, 147, 54], [16, 33, 147, 54, "exports"], [16, 40, 147, 54], [16, 41, 147, 54, "isLayoutAnimationEnabled"], [16, 65, 147, 54], [16, 68, 147, 57], [16, 72, 147, 57, "createJavaScriptFlagGetter"], [16, 127, 147, 83], [16, 129, 147, 84], [16, 155, 147, 110], [16, 157, 147, 112], [16, 161, 147, 116], [16, 162, 147, 117], [17, 2, 152, 7], [17, 6, 152, 13, "scheduleAnimatedCleanupInMicrotask"], [17, 40, 152, 64], [17, 43, 152, 64, "exports"], [17, 50, 152, 64], [17, 51, 152, 64, "scheduleAnimatedCleanupInMicrotask"], [17, 85, 152, 64], [17, 88, 152, 67], [17, 92, 152, 67, "createJavaScriptFlagGetter"], [17, 147, 152, 93], [17, 149, 152, 94], [17, 185, 152, 130], [17, 187, 152, 132], [17, 192, 152, 137], [17, 193, 152, 138], [18, 2, 157, 7], [18, 6, 157, 13, "shouldUseAnimatedObjectForTransform"], [18, 41, 157, 65], [18, 44, 157, 65, "exports"], [18, 51, 157, 65], [18, 52, 157, 65, "shouldUseAnimatedObjectForTransform"], [18, 87, 157, 65], [18, 90, 157, 68], [18, 94, 157, 68, "createJavaScriptFlagGetter"], [18, 149, 157, 94], [18, 151, 157, 95], [18, 188, 157, 132], [18, 190, 157, 134], [18, 195, 157, 139], [18, 196, 157, 140], [19, 2, 162, 7], [19, 6, 162, 13, "shouldUseRemoveClippedSubviewsAsDefaultOnIOS"], [19, 50, 162, 74], [19, 53, 162, 74, "exports"], [19, 60, 162, 74], [19, 61, 162, 74, "shouldUseRemoveClippedSubviewsAsDefaultOnIOS"], [19, 105, 162, 74], [19, 108, 162, 77], [19, 112, 162, 77, "createJavaScriptFlagGetter"], [19, 167, 162, 103], [19, 169, 162, 104], [19, 215, 162, 150], [19, 217, 162, 152], [19, 222, 162, 157], [19, 223, 162, 158], [20, 2, 167, 7], [20, 6, 167, 13, "shouldUseSetNativePropsInFabric"], [20, 37, 167, 61], [20, 40, 167, 61, "exports"], [20, 47, 167, 61], [20, 48, 167, 61, "shouldUseSetNativePropsInFabric"], [20, 79, 167, 61], [20, 82, 167, 64], [20, 86, 167, 64, "createJavaScriptFlagGetter"], [20, 141, 167, 90], [20, 143, 167, 91], [20, 176, 167, 124], [20, 178, 167, 126], [20, 182, 167, 130], [20, 183, 167, 131], [21, 2, 172, 7], [21, 6, 172, 13, "useRefsForTextInputState"], [21, 30, 172, 54], [21, 33, 172, 54, "exports"], [21, 40, 172, 54], [21, 41, 172, 54, "useRefsForTextInputState"], [21, 65, 172, 54], [21, 68, 172, 57], [21, 72, 172, 57, "createJavaScriptFlagGetter"], [21, 127, 172, 83], [21, 129, 172, 84], [21, 155, 172, 110], [21, 157, 172, 112], [21, 162, 172, 117], [21, 163, 172, 118], [22, 2, 177, 7], [22, 6, 177, 13, "commonTestFlag"], [22, 20, 177, 44], [22, 23, 177, 44, "exports"], [22, 30, 177, 44], [22, 31, 177, 44, "commonTestFlag"], [22, 45, 177, 44], [22, 48, 177, 47], [22, 52, 177, 47, "createNativeFlagGetter"], [22, 103, 177, 69], [22, 105, 177, 70], [22, 121, 177, 86], [22, 123, 177, 88], [22, 128, 177, 93], [22, 129, 177, 94], [23, 2, 181, 7], [23, 6, 181, 13, "commonTestFlagWithoutNativeImplementation"], [23, 47, 181, 71], [23, 50, 181, 71, "exports"], [23, 57, 181, 71], [23, 58, 181, 71, "commonTestFlagWithoutNativeImplementation"], [23, 99, 181, 71], [23, 102, 181, 74], [23, 106, 181, 74, "createNativeFlagGetter"], [23, 157, 181, 96], [23, 159, 181, 97], [23, 202, 181, 140], [23, 204, 181, 142], [23, 209, 181, 147], [23, 210, 181, 148], [24, 2, 185, 7], [24, 6, 185, 13, "disableMountItemReorderingAndroid"], [24, 39, 185, 63], [24, 42, 185, 63, "exports"], [24, 49, 185, 63], [24, 50, 185, 63, "disableMountItemReorderingAndroid"], [24, 83, 185, 63], [24, 86, 185, 66], [24, 90, 185, 66, "createNativeFlagGetter"], [24, 141, 185, 88], [24, 143, 185, 89], [24, 178, 185, 124], [24, 180, 185, 126], [24, 185, 185, 131], [24, 186, 185, 132], [25, 2, 189, 7], [25, 6, 189, 13, "enableAccumulatedUpdatesInRawPropsAndroid"], [25, 47, 189, 71], [25, 50, 189, 71, "exports"], [25, 57, 189, 71], [25, 58, 189, 71, "enableAccumulatedUpdatesInRawPropsAndroid"], [25, 99, 189, 71], [25, 102, 189, 74], [25, 106, 189, 74, "createNativeFlagGetter"], [25, 157, 189, 96], [25, 159, 189, 97], [25, 202, 189, 140], [25, 204, 189, 142], [25, 209, 189, 147], [25, 210, 189, 148], [26, 2, 193, 7], [26, 6, 193, 13, "enableBridgelessArchitecture"], [26, 34, 193, 58], [26, 37, 193, 58, "exports"], [26, 44, 193, 58], [26, 45, 193, 58, "enableBridgelessArchitecture"], [26, 73, 193, 58], [26, 76, 193, 61], [26, 80, 193, 61, "createNativeFlagGetter"], [26, 131, 193, 83], [26, 133, 193, 84], [26, 163, 193, 114], [26, 165, 193, 116], [26, 170, 193, 121], [26, 171, 193, 122], [27, 2, 197, 7], [27, 6, 197, 13, "enableCppPropsIteratorSetter"], [27, 34, 197, 58], [27, 37, 197, 58, "exports"], [27, 44, 197, 58], [27, 45, 197, 58, "enableCppPropsIteratorSetter"], [27, 73, 197, 58], [27, 76, 197, 61], [27, 80, 197, 61, "createNativeFlagGetter"], [27, 131, 197, 83], [27, 133, 197, 84], [27, 163, 197, 114], [27, 165, 197, 116], [27, 170, 197, 121], [27, 171, 197, 122], [28, 2, 201, 7], [28, 6, 201, 13, "enableEagerRootViewAttachment"], [28, 35, 201, 59], [28, 38, 201, 59, "exports"], [28, 45, 201, 59], [28, 46, 201, 59, "enableEagerRootViewAttachment"], [28, 75, 201, 59], [28, 78, 201, 62], [28, 82, 201, 62, "createNativeFlagGetter"], [28, 133, 201, 84], [28, 135, 201, 85], [28, 166, 201, 116], [28, 168, 201, 118], [28, 173, 201, 123], [28, 174, 201, 124], [29, 2, 205, 7], [29, 6, 205, 13, "enableFabricLogs"], [29, 22, 205, 46], [29, 25, 205, 46, "exports"], [29, 32, 205, 46], [29, 33, 205, 46, "enableFabricLogs"], [29, 49, 205, 46], [29, 52, 205, 49], [29, 56, 205, 49, "createNativeFlagGetter"], [29, 107, 205, 71], [29, 109, 205, 72], [29, 127, 205, 90], [29, 129, 205, 92], [29, 134, 205, 97], [29, 135, 205, 98], [30, 2, 209, 7], [30, 6, 209, 13, "enableFab<PERSON><PERSON><PERSON><PERSON>"], [30, 26, 209, 50], [30, 29, 209, 50, "exports"], [30, 36, 209, 50], [30, 37, 209, 50, "enableFab<PERSON><PERSON><PERSON><PERSON>"], [30, 57, 209, 50], [30, 60, 209, 53], [30, 64, 209, 53, "createNativeFlagGetter"], [30, 115, 209, 75], [30, 117, 209, 76], [30, 139, 209, 98], [30, 141, 209, 100], [30, 146, 209, 105], [30, 147, 209, 106], [31, 2, 213, 7], [31, 6, 213, 13, "enableIOSViewClipToPaddingBox"], [31, 35, 213, 59], [31, 38, 213, 59, "exports"], [31, 45, 213, 59], [31, 46, 213, 59, "enableIOSViewClipToPaddingBox"], [31, 75, 213, 59], [31, 78, 213, 62], [31, 82, 213, 62, "createNativeFlagGetter"], [31, 133, 213, 84], [31, 135, 213, 85], [31, 166, 213, 116], [31, 168, 213, 118], [31, 173, 213, 123], [31, 174, 213, 124], [32, 2, 217, 7], [32, 6, 217, 13, "enableImagePrefetchingAndroid"], [32, 35, 217, 59], [32, 38, 217, 59, "exports"], [32, 45, 217, 59], [32, 46, 217, 59, "enableImagePrefetchingAndroid"], [32, 75, 217, 59], [32, 78, 217, 62], [32, 82, 217, 62, "createNativeFlagGetter"], [32, 133, 217, 84], [32, 135, 217, 85], [32, 166, 217, 116], [32, 168, 217, 118], [32, 173, 217, 123], [32, 174, 217, 124], [33, 2, 221, 7], [33, 6, 221, 13, "enableJSRuntimeGCOnMemoryPressureOnIOS"], [33, 44, 221, 68], [33, 47, 221, 68, "exports"], [33, 54, 221, 68], [33, 55, 221, 68, "enableJSRuntimeGCOnMemoryPressureOnIOS"], [33, 93, 221, 68], [33, 96, 221, 71], [33, 100, 221, 71, "createNativeFlagGetter"], [33, 151, 221, 93], [33, 153, 221, 94], [33, 193, 221, 134], [33, 195, 221, 136], [33, 200, 221, 141], [33, 201, 221, 142], [34, 2, 225, 7], [34, 6, 225, 13, "enableLayoutAnimationsOnAndroid"], [34, 37, 225, 61], [34, 40, 225, 61, "exports"], [34, 47, 225, 61], [34, 48, 225, 61, "enableLayoutAnimationsOnAndroid"], [34, 79, 225, 61], [34, 82, 225, 64], [34, 86, 225, 64, "createNativeFlagGetter"], [34, 137, 225, 86], [34, 139, 225, 87], [34, 172, 225, 120], [34, 174, 225, 122], [34, 179, 225, 127], [34, 180, 225, 128], [35, 2, 229, 7], [35, 6, 229, 13, "enableLayoutAnimationsOnIOS"], [35, 33, 229, 57], [35, 36, 229, 57, "exports"], [35, 43, 229, 57], [35, 44, 229, 57, "enableLayoutAnimationsOnIOS"], [35, 71, 229, 57], [35, 74, 229, 60], [35, 78, 229, 60, "createNativeFlagGetter"], [35, 129, 229, 82], [35, 131, 229, 83], [35, 160, 229, 112], [35, 162, 229, 114], [35, 166, 229, 118], [35, 167, 229, 119], [36, 2, 233, 7], [36, 6, 233, 13, "enableLongTaskAPI"], [36, 23, 233, 47], [36, 26, 233, 47, "exports"], [36, 33, 233, 47], [36, 34, 233, 47, "enableLongTaskAPI"], [36, 51, 233, 47], [36, 54, 233, 50], [36, 58, 233, 50, "createNativeFlagGetter"], [36, 109, 233, 72], [36, 111, 233, 73], [36, 130, 233, 92], [36, 132, 233, 94], [36, 137, 233, 99], [36, 138, 233, 100], [37, 2, 237, 7], [37, 6, 237, 13, "enableNativeCSSParsing"], [37, 28, 237, 52], [37, 31, 237, 52, "exports"], [37, 38, 237, 52], [37, 39, 237, 52, "enableNativeCSSParsing"], [37, 61, 237, 52], [37, 64, 237, 55], [37, 68, 237, 55, "createNativeFlagGetter"], [37, 119, 237, 77], [37, 121, 237, 78], [37, 145, 237, 102], [37, 147, 237, 104], [37, 152, 237, 109], [37, 153, 237, 110], [38, 2, 241, 7], [38, 6, 241, 13, "enableNewBackgroundAndBorderDrawables"], [38, 43, 241, 67], [38, 46, 241, 67, "exports"], [38, 53, 241, 67], [38, 54, 241, 67, "enableNewBackgroundAndBorderDrawables"], [38, 91, 241, 67], [38, 94, 241, 70], [38, 98, 241, 70, "createNativeFlagGetter"], [38, 149, 241, 92], [38, 151, 241, 93], [38, 190, 241, 132], [38, 192, 241, 134], [38, 197, 241, 139], [38, 198, 241, 140], [39, 2, 245, 7], [39, 6, 245, 13, "enablePreciseSchedulingForPremountItemsOnAndroid"], [39, 54, 245, 78], [39, 57, 245, 78, "exports"], [39, 64, 245, 78], [39, 65, 245, 78, "enablePreciseSchedulingForPremountItemsOnAndroid"], [39, 113, 245, 78], [39, 116, 245, 81], [39, 120, 245, 81, "createNativeFlagGetter"], [39, 171, 245, 103], [39, 173, 245, 104], [39, 223, 245, 154], [39, 225, 245, 156], [39, 230, 245, 161], [39, 231, 245, 162], [40, 2, 249, 7], [40, 6, 249, 13, "enablePropsUpdateReconciliationAndroid"], [40, 44, 249, 68], [40, 47, 249, 68, "exports"], [40, 54, 249, 68], [40, 55, 249, 68, "enablePropsUpdateReconciliationAndroid"], [40, 93, 249, 68], [40, 96, 249, 71], [40, 100, 249, 71, "createNativeFlagGetter"], [40, 151, 249, 93], [40, 153, 249, 94], [40, 193, 249, 134], [40, 195, 249, 136], [40, 200, 249, 141], [40, 201, 249, 142], [41, 2, 253, 7], [41, 6, 253, 13, "enableReportEventPaintTime"], [41, 32, 253, 56], [41, 35, 253, 56, "exports"], [41, 42, 253, 56], [41, 43, 253, 56, "enableReportEventPaintTime"], [41, 69, 253, 56], [41, 72, 253, 59], [41, 76, 253, 59, "createNativeFlagGetter"], [41, 127, 253, 81], [41, 129, 253, 82], [41, 157, 253, 110], [41, 159, 253, 112], [41, 164, 253, 117], [41, 165, 253, 118], [42, 2, 257, 7], [42, 6, 257, 13, "enableSynchronousStateUpdates"], [42, 35, 257, 59], [42, 38, 257, 59, "exports"], [42, 45, 257, 59], [42, 46, 257, 59, "enableSynchronousStateUpdates"], [42, 75, 257, 59], [42, 78, 257, 62], [42, 82, 257, 62, "createNativeFlagGetter"], [42, 133, 257, 84], [42, 135, 257, 85], [42, 166, 257, 116], [42, 168, 257, 118], [42, 173, 257, 123], [42, 174, 257, 124], [43, 2, 261, 7], [43, 6, 261, 13, "enableUIConsistency"], [43, 25, 261, 49], [43, 28, 261, 49, "exports"], [43, 35, 261, 49], [43, 36, 261, 49, "enableUIConsistency"], [43, 55, 261, 49], [43, 58, 261, 52], [43, 62, 261, 52, "createNativeFlagGetter"], [43, 113, 261, 74], [43, 115, 261, 75], [43, 136, 261, 96], [43, 138, 261, 98], [43, 143, 261, 103], [43, 144, 261, 104], [44, 2, 265, 7], [44, 6, 265, 13, "enableViewCulling"], [44, 23, 265, 47], [44, 26, 265, 47, "exports"], [44, 33, 265, 47], [44, 34, 265, 47, "enableViewCulling"], [44, 51, 265, 47], [44, 54, 265, 50], [44, 58, 265, 50, "createNativeFlagGetter"], [44, 109, 265, 72], [44, 111, 265, 73], [44, 130, 265, 92], [44, 132, 265, 94], [44, 137, 265, 99], [44, 138, 265, 100], [45, 2, 269, 7], [45, 6, 269, 13, "enableViewRecycling"], [45, 25, 269, 49], [45, 28, 269, 49, "exports"], [45, 35, 269, 49], [45, 36, 269, 49, "enableViewRecycling"], [45, 55, 269, 49], [45, 58, 269, 52], [45, 62, 269, 52, "createNativeFlagGetter"], [45, 113, 269, 74], [45, 115, 269, 75], [45, 136, 269, 96], [45, 138, 269, 98], [45, 143, 269, 103], [45, 144, 269, 104], [46, 2, 273, 7], [46, 6, 273, 13, "enableViewRecyclingForText"], [46, 32, 273, 56], [46, 35, 273, 56, "exports"], [46, 42, 273, 56], [46, 43, 273, 56, "enableViewRecyclingForText"], [46, 69, 273, 56], [46, 72, 273, 59], [46, 76, 273, 59, "createNativeFlagGetter"], [46, 127, 273, 81], [46, 129, 273, 82], [46, 157, 273, 110], [46, 159, 273, 112], [46, 163, 273, 116], [46, 164, 273, 117], [47, 2, 277, 7], [47, 6, 277, 13, "enableViewRecyclingForView"], [47, 32, 277, 56], [47, 35, 277, 56, "exports"], [47, 42, 277, 56], [47, 43, 277, 56, "enableViewRecyclingForView"], [47, 69, 277, 56], [47, 72, 277, 59], [47, 76, 277, 59, "createNativeFlagGetter"], [47, 127, 277, 81], [47, 129, 277, 82], [47, 157, 277, 110], [47, 159, 277, 112], [47, 163, 277, 116], [47, 164, 277, 117], [48, 2, 281, 7], [48, 6, 281, 13, "excludeYogaFromRawProps"], [48, 29, 281, 53], [48, 32, 281, 53, "exports"], [48, 39, 281, 53], [48, 40, 281, 53, "excludeYogaFromRawProps"], [48, 63, 281, 53], [48, 66, 281, 56], [48, 70, 281, 56, "createNativeFlagGetter"], [48, 121, 281, 78], [48, 123, 281, 79], [48, 148, 281, 104], [48, 150, 281, 106], [48, 155, 281, 111], [48, 156, 281, 112], [49, 2, 285, 7], [49, 6, 285, 13, "fixDifferentiatorEmittingUpdatesWithWrongParentTag"], [49, 56, 285, 80], [49, 59, 285, 80, "exports"], [49, 66, 285, 80], [49, 67, 285, 80, "fixDifferentiatorEmittingUpdatesWithWrongParentTag"], [49, 117, 285, 80], [49, 120, 285, 83], [49, 124, 285, 83, "createNativeFlagGetter"], [49, 175, 285, 105], [49, 177, 285, 106], [49, 229, 285, 158], [49, 231, 285, 160], [49, 235, 285, 164], [49, 236, 285, 165], [50, 2, 289, 7], [50, 6, 289, 13, "fixMappingOfEventPrioritiesBetweenFabricAndReact"], [50, 54, 289, 78], [50, 57, 289, 78, "exports"], [50, 64, 289, 78], [50, 65, 289, 78, "fixMappingOfEventPrioritiesBetweenFabricAndReact"], [50, 113, 289, 78], [50, 116, 289, 81], [50, 120, 289, 81, "createNativeFlagGetter"], [50, 171, 289, 103], [50, 173, 289, 104], [50, 223, 289, 154], [50, 225, 289, 156], [50, 230, 289, 161], [50, 231, 289, 162], [51, 2, 293, 7], [51, 6, 293, 13, "fixMountingCoordinatorReportedPendingTransactionsOnAndroid"], [51, 64, 293, 88], [51, 67, 293, 88, "exports"], [51, 74, 293, 88], [51, 75, 293, 88, "fixMountingCoordinatorReportedPendingTransactionsOnAndroid"], [51, 133, 293, 88], [51, 136, 293, 91], [51, 140, 293, 91, "createNativeFlagGetter"], [51, 191, 293, 113], [51, 193, 293, 114], [51, 253, 293, 174], [51, 255, 293, 176], [51, 260, 293, 181], [51, 261, 293, 182], [52, 2, 297, 7], [52, 6, 297, 13, "fuseboxEnabledRelease"], [52, 27, 297, 51], [52, 30, 297, 51, "exports"], [52, 37, 297, 51], [52, 38, 297, 51, "fuseboxEnabledRelease"], [52, 59, 297, 51], [52, 62, 297, 54], [52, 66, 297, 54, "createNativeFlagGetter"], [52, 117, 297, 76], [52, 119, 297, 77], [52, 142, 297, 100], [52, 144, 297, 102], [52, 149, 297, 107], [52, 150, 297, 108], [53, 2, 301, 7], [53, 6, 301, 13, "fuseboxNetworkInspectionEnabled"], [53, 37, 301, 61], [53, 40, 301, 61, "exports"], [53, 47, 301, 61], [53, 48, 301, 61, "fuseboxNetworkInspectionEnabled"], [53, 79, 301, 61], [53, 82, 301, 64], [53, 86, 301, 64, "createNativeFlagGetter"], [53, 137, 301, 86], [53, 139, 301, 87], [53, 172, 301, 120], [53, 174, 301, 122], [53, 179, 301, 127], [53, 180, 301, 128], [54, 2, 305, 7], [54, 6, 305, 13, "lazyAnimationCallbacks"], [54, 28, 305, 52], [54, 31, 305, 52, "exports"], [54, 38, 305, 52], [54, 39, 305, 52, "lazyAnimationCallbacks"], [54, 61, 305, 52], [54, 64, 305, 55], [54, 68, 305, 55, "createNativeFlagGetter"], [54, 119, 305, 77], [54, 121, 305, 78], [54, 145, 305, 102], [54, 147, 305, 104], [54, 152, 305, 109], [54, 153, 305, 110], [55, 2, 309, 7], [55, 6, 309, 13, "removeTurboModuleManagerDelegateMutex"], [55, 43, 309, 67], [55, 46, 309, 67, "exports"], [55, 53, 309, 67], [55, 54, 309, 67, "removeTurboModuleManagerDelegateMutex"], [55, 91, 309, 67], [55, 94, 309, 70], [55, 98, 309, 70, "createNativeFlagGetter"], [55, 149, 309, 92], [55, 151, 309, 93], [55, 190, 309, 132], [55, 192, 309, 134], [55, 197, 309, 139], [55, 198, 309, 140], [56, 2, 313, 7], [56, 6, 313, 13, "throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS"], [56, 74, 313, 98], [56, 77, 313, 98, "exports"], [56, 84, 313, 98], [56, 85, 313, 98, "throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS"], [56, 153, 313, 98], [56, 156, 313, 101], [56, 160, 313, 101, "createNativeFlagGetter"], [56, 211, 313, 123], [56, 213, 313, 124], [56, 283, 313, 194], [56, 285, 313, 196], [56, 290, 313, 201], [56, 291, 313, 202], [57, 2, 317, 7], [57, 6, 317, 13, "traceTurboModulePromiseRejectionsOnAndroid"], [57, 48, 317, 72], [57, 51, 317, 72, "exports"], [57, 58, 317, 72], [57, 59, 317, 72, "traceTurboModulePromiseRejectionsOnAndroid"], [57, 101, 317, 72], [57, 104, 317, 75], [57, 108, 317, 75, "createNativeFlagGetter"], [57, 159, 317, 97], [57, 161, 317, 98], [57, 205, 317, 142], [57, 207, 317, 144], [57, 212, 317, 149], [57, 213, 317, 150], [58, 2, 321, 7], [58, 6, 321, 13, "updateRuntimeShadowNodeReferencesOnCommit"], [58, 47, 321, 71], [58, 50, 321, 71, "exports"], [58, 57, 321, 71], [58, 58, 321, 71, "updateRuntimeShadowNodeReferencesOnCommit"], [58, 99, 321, 71], [58, 102, 321, 74], [58, 106, 321, 74, "createNativeFlagGetter"], [58, 157, 321, 96], [58, 159, 321, 97], [58, 202, 321, 140], [58, 204, 321, 142], [58, 209, 321, 147], [58, 210, 321, 148], [59, 2, 325, 7], [59, 6, 325, 13, "useAlwaysAvailableJSErrorHandling"], [59, 39, 325, 63], [59, 42, 325, 63, "exports"], [59, 49, 325, 63], [59, 50, 325, 63, "useAlwaysAvailableJSErrorHandling"], [59, 83, 325, 63], [59, 86, 325, 66], [59, 90, 325, 66, "createNativeFlagGetter"], [59, 141, 325, 88], [59, 143, 325, 89], [59, 178, 325, 124], [59, 180, 325, 126], [59, 185, 325, 131], [59, 186, 325, 132], [60, 2, 329, 7], [60, 6, 329, 13, "useEditTextStockAndroidFocusBehavior"], [60, 42, 329, 66], [60, 45, 329, 66, "exports"], [60, 52, 329, 66], [60, 53, 329, 66, "useEditTextStockAndroidFocusBehavior"], [60, 89, 329, 66], [60, 92, 329, 69], [60, 96, 329, 69, "createNativeFlagGetter"], [60, 147, 329, 91], [60, 149, 329, 92], [60, 187, 329, 130], [60, 189, 329, 132], [60, 193, 329, 136], [60, 194, 329, 137], [61, 2, 333, 7], [61, 6, 333, 13, "useFabricInterop"], [61, 22, 333, 46], [61, 25, 333, 46, "exports"], [61, 32, 333, 46], [61, 33, 333, 46, "useFabricInterop"], [61, 49, 333, 46], [61, 52, 333, 49], [61, 56, 333, 49, "createNativeFlagGetter"], [61, 107, 333, 71], [61, 109, 333, 72], [61, 127, 333, 90], [61, 129, 333, 92], [61, 134, 333, 97], [61, 135, 333, 98], [62, 2, 337, 7], [62, 6, 337, 13, "useNativeViewConfigsInBridgelessMode"], [62, 42, 337, 66], [62, 45, 337, 66, "exports"], [62, 52, 337, 66], [62, 53, 337, 66, "useNativeViewConfigsInBridgelessMode"], [62, 89, 337, 66], [62, 92, 337, 69], [62, 96, 337, 69, "createNativeFlagGetter"], [62, 147, 337, 91], [62, 149, 337, 92], [62, 187, 337, 130], [62, 189, 337, 132], [62, 194, 337, 137], [62, 195, 337, 138], [63, 2, 341, 7], [63, 6, 341, 13, "useOptimizedEventBatchingOnAndroid"], [63, 40, 341, 64], [63, 43, 341, 64, "exports"], [63, 50, 341, 64], [63, 51, 341, 64, "useOptimizedEventBatchingOnAndroid"], [63, 85, 341, 64], [63, 88, 341, 67], [63, 92, 341, 67, "createNativeFlagGetter"], [63, 143, 341, 89], [63, 145, 341, 90], [63, 181, 341, 126], [63, 183, 341, 128], [63, 188, 341, 133], [63, 189, 341, 134], [64, 2, 345, 7], [64, 6, 345, 13, "useRawPropsJsiValue"], [64, 25, 345, 49], [64, 28, 345, 49, "exports"], [64, 35, 345, 49], [64, 36, 345, 49, "useRawPropsJsiValue"], [64, 55, 345, 49], [64, 58, 345, 52], [64, 62, 345, 52, "createNativeFlagGetter"], [64, 113, 345, 74], [64, 115, 345, 75], [64, 136, 345, 96], [64, 138, 345, 98], [64, 143, 345, 103], [64, 144, 345, 104], [65, 2, 349, 7], [65, 6, 349, 13, "useShadowNodeStateOnClone"], [65, 31, 349, 55], [65, 34, 349, 55, "exports"], [65, 41, 349, 55], [65, 42, 349, 55, "useShadowNodeStateOnClone"], [65, 67, 349, 55], [65, 70, 349, 58], [65, 74, 349, 58, "createNativeFlagGetter"], [65, 125, 349, 80], [65, 127, 349, 81], [65, 154, 349, 108], [65, 156, 349, 110], [65, 161, 349, 115], [65, 162, 349, 116], [66, 2, 353, 7], [66, 6, 353, 13, "useTurboModuleInterop"], [66, 27, 353, 51], [66, 30, 353, 51, "exports"], [66, 37, 353, 51], [66, 38, 353, 51, "useTurboModuleInterop"], [66, 59, 353, 51], [66, 62, 353, 54], [66, 66, 353, 54, "createNativeFlagGetter"], [66, 117, 353, 76], [66, 119, 353, 77], [66, 142, 353, 100], [66, 144, 353, 102], [66, 149, 353, 107], [66, 150, 353, 108], [67, 2, 357, 7], [67, 6, 357, 13, "useTurboModules"], [67, 21, 357, 45], [67, 24, 357, 45, "exports"], [67, 31, 357, 45], [67, 32, 357, 45, "useTurboModules"], [67, 47, 357, 45], [67, 50, 357, 48], [67, 54, 357, 48, "createNativeFlagGetter"], [67, 105, 357, 70], [67, 107, 357, 71], [67, 124, 357, 88], [67, 126, 357, 90], [67, 131, 357, 95], [67, 132, 357, 96], [68, 2, 363, 7], [68, 6, 363, 13, "override"], [68, 14, 363, 21], [68, 17, 363, 21, "exports"], [68, 24, 363, 21], [68, 25, 363, 21, "override"], [68, 33, 363, 21], [68, 36, 363, 24, "setOverrides"], [68, 77, 363, 36], [69, 0, 363, 37], [69, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}