{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 75, "index": 75}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.WebView = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _jsxRuntime = require(_dependencyMap[1], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/polyfills/webview.web.tsx\",\n    _s = $RefreshSig$();\n  /**\n   * Web-based implementation of React Native WebView using iframe\n   */\n  const WebView = exports.WebView = /*#__PURE__*/_s(/*#__PURE__*/(0, _react.forwardRef)(_c = _s((props, ref) => {\n    _s();\n    const iframeRef = (0, _react.useRef)(null);\n    const {\n      source,\n      style,\n      injectedJavaScript,\n      onMessage,\n      onLoadStart,\n      onLoad,\n      onLoadEnd,\n      onNavigationStateChange\n    } = props;\n    (0, _react.useEffect)(() => {\n      const handleMessage = event => {\n        onMessage?.({\n          nativeEvent: {\n            data: event.data\n          }\n        });\n      };\n      window.addEventListener('message', handleMessage);\n      return () => window.removeEventListener('message', handleMessage);\n    }, [onMessage]);\n\n    // Imperative handle to match RN WebView API\n    (0, _react.useImperativeHandle)(ref, () => ({\n      injectJavaScript: js => {\n        iframeRef.current?.contentWindow?.postMessage(js, '*');\n      },\n      goBack: () => {\n        iframeRef.current?.contentWindow?.history.back();\n      },\n      goForward: () => {\n        iframeRef.current?.contentWindow?.history.forward();\n      },\n      reload: () => {\n        iframeRef.current?.contentWindow?.location.reload();\n      },\n      stopLoading: () => {\n        // Not directly possible with iframe\n      }\n    }));\n    const src = source.html ? `data:text/html;charset=utf-8,${encodeURIComponent(source.html)}` : source.uri;\n    return (0, _jsxRuntime.jsx)(\"iframe\", {\n      ref: iframeRef,\n      src: src,\n      style: {\n        border: 'none',\n        width: '100%',\n        height: '100%',\n        overflow: props.scrollEnabled === false ? 'hidden' : 'auto',\n        ...style\n      },\n      allow: \"third-party-cookies\",\n      onLoad: e => {\n        onLoadStart?.();\n        onLoad?.();\n        onLoadEnd?.();\n        if (injectedJavaScript) {\n          iframeRef.current?.contentWindow?.postMessage(injectedJavaScript, '*');\n        }\n        const win = e.currentTarget.contentWindow;\n        if (win) {\n          onNavigationStateChange?.({\n            url: win.location.href,\n            loading: false,\n            canGoBack: win.history.length > 1,\n            canGoForward: win.history.length > 1\n          });\n        }\n      }\n    });\n  }, \"PKGRdAQGoIM5eiCmgqr2M6iY/ek=\")), \"PKGRdAQGoIM5eiCmgqr2M6iY/ek=\");\n  _c2 = WebView;\n  var _default = exports.default = WebView;\n  var _c, _c2;\n  $RefreshReg$(_c, \"WebView$forwardRef\");\n  $RefreshReg$(_c2, \"WebView\");\n});", "lineCount": 92, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 1, 75], [7, 6, 1, 75, "_jsxRuntime"], [7, 17, 1, 75], [7, 20, 1, 75, "require"], [7, 27, 1, 75], [7, 28, 1, 75, "_dependencyMap"], [7, 42, 1, 75], [8, 2, 1, 75], [8, 6, 1, 75, "_jsxFileName"], [8, 18, 1, 75], [9, 4, 1, 75, "_s"], [9, 6, 1, 75], [9, 9, 1, 75, "$RefreshSig$"], [9, 21, 1, 75], [10, 2, 26, 0], [11, 0, 27, 0], [12, 0, 28, 0], [13, 2, 29, 7], [13, 8, 29, 13, "WebView"], [13, 15, 29, 20], [13, 18, 29, 20, "exports"], [13, 25, 29, 20], [13, 26, 29, 20, "WebView"], [13, 33, 29, 20], [13, 49, 29, 20, "_s"], [13, 51, 29, 20], [13, 65, 29, 23], [13, 69, 29, 23, "forwardRef"], [13, 86, 29, 33], [13, 88, 29, 33, "_c"], [13, 90, 29, 33], [13, 93, 29, 33, "_s"], [13, 95, 29, 33], [13, 96, 29, 34], [13, 97, 29, 35, "props"], [13, 102, 29, 47], [13, 104, 29, 49, "ref"], [13, 107, 29, 52], [13, 112, 29, 57], [14, 4, 29, 57, "_s"], [14, 6, 29, 57], [15, 4, 30, 1], [15, 10, 30, 7, "iframeRef"], [15, 19, 30, 16], [15, 22, 30, 19], [15, 26, 30, 19, "useRef"], [15, 39, 30, 25], [15, 41, 30, 45], [15, 45, 30, 49], [15, 46, 30, 50], [16, 4, 31, 1], [16, 10, 31, 7], [17, 6, 32, 2, "source"], [17, 12, 32, 8], [18, 6, 33, 2, "style"], [18, 11, 33, 7], [19, 6, 34, 2, "injectedJavaScript"], [19, 24, 34, 20], [20, 6, 35, 2, "onMessage"], [20, 15, 35, 11], [21, 6, 36, 2, "onLoadStart"], [21, 17, 36, 13], [22, 6, 37, 2, "onLoad"], [22, 12, 37, 8], [23, 6, 38, 2, "onLoadEnd"], [23, 15, 38, 11], [24, 6, 39, 2, "onNavigationStateChange"], [25, 4, 40, 1], [25, 5, 40, 2], [25, 8, 40, 5, "props"], [25, 13, 40, 10], [26, 4, 42, 1], [26, 8, 42, 1, "useEffect"], [26, 24, 42, 10], [26, 26, 42, 11], [26, 32, 42, 17], [27, 6, 43, 2], [27, 12, 43, 8, "handleMessage"], [27, 25, 43, 21], [27, 28, 43, 25, "event"], [27, 33, 43, 44], [27, 37, 43, 49], [28, 8, 44, 3, "onMessage"], [28, 17, 44, 12], [28, 20, 44, 15], [29, 10, 44, 17, "nativeEvent"], [29, 21, 44, 28], [29, 23, 44, 30], [30, 12, 44, 32, "data"], [30, 16, 44, 36], [30, 18, 44, 38, "event"], [30, 23, 44, 43], [30, 24, 44, 44, "data"], [31, 10, 44, 49], [32, 8, 44, 51], [32, 9, 44, 52], [32, 10, 44, 53], [33, 6, 45, 2], [33, 7, 45, 3], [34, 6, 46, 2, "window"], [34, 12, 46, 8], [34, 13, 46, 9, "addEventListener"], [34, 29, 46, 25], [34, 30, 46, 26], [34, 39, 46, 35], [34, 41, 46, 37, "handleMessage"], [34, 54, 46, 50], [34, 55, 46, 51], [35, 6, 47, 2], [35, 13, 47, 9], [35, 19, 47, 15, "window"], [35, 25, 47, 21], [35, 26, 47, 22, "removeEventListener"], [35, 45, 47, 41], [35, 46, 47, 42], [35, 55, 47, 51], [35, 57, 47, 53, "handleMessage"], [35, 70, 47, 66], [35, 71, 47, 67], [36, 4, 48, 1], [36, 5, 48, 2], [36, 7, 48, 4], [36, 8, 48, 5, "onMessage"], [36, 17, 48, 14], [36, 18, 48, 15], [36, 19, 48, 16], [38, 4, 50, 1], [39, 4, 51, 1], [39, 8, 51, 1, "useImperativeHandle"], [39, 34, 51, 20], [39, 36, 51, 21, "ref"], [39, 39, 51, 24], [39, 41, 51, 26], [39, 48, 51, 33], [40, 6, 52, 2, "injectJavaScript"], [40, 22, 52, 18], [40, 24, 52, 21, "js"], [40, 26, 52, 31], [40, 30, 52, 36], [41, 8, 53, 3, "iframeRef"], [41, 17, 53, 12], [41, 18, 53, 13, "current"], [41, 25, 53, 20], [41, 27, 53, 22, "contentWindow"], [41, 40, 53, 35], [41, 42, 53, 37, "postMessage"], [41, 53, 53, 48], [41, 54, 53, 49, "js"], [41, 56, 53, 51], [41, 58, 53, 53], [41, 61, 53, 56], [41, 62, 53, 57], [42, 6, 54, 2], [42, 7, 54, 3], [43, 6, 55, 2, "goBack"], [43, 12, 55, 8], [43, 14, 55, 10, "goBack"], [43, 15, 55, 10], [43, 20, 55, 16], [44, 8, 56, 3, "iframeRef"], [44, 17, 56, 12], [44, 18, 56, 13, "current"], [44, 25, 56, 20], [44, 27, 56, 22, "contentWindow"], [44, 40, 56, 35], [44, 42, 56, 37, "history"], [44, 49, 56, 44], [44, 50, 56, 45, "back"], [44, 54, 56, 49], [44, 55, 56, 50], [44, 56, 56, 51], [45, 6, 57, 2], [45, 7, 57, 3], [46, 6, 58, 2, "goForward"], [46, 15, 58, 11], [46, 17, 58, 13, "goForward"], [46, 18, 58, 13], [46, 23, 58, 19], [47, 8, 59, 3, "iframeRef"], [47, 17, 59, 12], [47, 18, 59, 13, "current"], [47, 25, 59, 20], [47, 27, 59, 22, "contentWindow"], [47, 40, 59, 35], [47, 42, 59, 37, "history"], [47, 49, 59, 44], [47, 50, 59, 45, "forward"], [47, 57, 59, 52], [47, 58, 59, 53], [47, 59, 59, 54], [48, 6, 60, 2], [48, 7, 60, 3], [49, 6, 61, 2, "reload"], [49, 12, 61, 8], [49, 14, 61, 10, "reload"], [49, 15, 61, 10], [49, 20, 61, 16], [50, 8, 62, 3, "iframeRef"], [50, 17, 62, 12], [50, 18, 62, 13, "current"], [50, 25, 62, 20], [50, 27, 62, 22, "contentWindow"], [50, 40, 62, 35], [50, 42, 62, 37, "location"], [50, 50, 62, 45], [50, 51, 62, 46, "reload"], [50, 57, 62, 52], [50, 58, 62, 53], [50, 59, 62, 54], [51, 6, 63, 2], [51, 7, 63, 3], [52, 6, 64, 2, "stopLoading"], [52, 17, 64, 13], [52, 19, 64, 15, "stopLoading"], [52, 20, 64, 15], [52, 25, 64, 21], [53, 8, 65, 3], [54, 6, 65, 3], [55, 4, 67, 1], [55, 5, 67, 2], [55, 6, 67, 3], [55, 7, 67, 4], [56, 4, 69, 1], [56, 10, 69, 7, "src"], [56, 13, 69, 10], [56, 16, 69, 13, "source"], [56, 22, 69, 19], [56, 23, 69, 20, "html"], [56, 27, 69, 24], [56, 30, 70, 4], [56, 62, 70, 36, "encodeURIComponent"], [56, 80, 70, 54], [56, 81, 70, 55, "source"], [56, 87, 70, 61], [56, 88, 70, 62, "html"], [56, 92, 70, 66], [56, 93, 70, 67], [56, 95, 70, 69], [56, 98, 71, 4, "source"], [56, 104, 71, 10], [56, 105, 71, 11, "uri"], [56, 108, 71, 14], [57, 4, 73, 1], [57, 11, 74, 2], [57, 15, 74, 2, "_jsxRuntime"], [57, 26, 74, 2], [57, 27, 74, 2, "jsx"], [57, 30, 74, 2], [58, 6, 75, 3, "ref"], [58, 9, 75, 6], [58, 11, 75, 8, "iframeRef"], [58, 20, 75, 18], [59, 6, 76, 3, "src"], [59, 9, 76, 6], [59, 11, 76, 8, "src"], [59, 14, 76, 12], [60, 6, 77, 3, "style"], [60, 11, 77, 8], [60, 13, 77, 10], [61, 8, 78, 4, "border"], [61, 14, 78, 10], [61, 16, 78, 12], [61, 22, 78, 18], [62, 8, 79, 4, "width"], [62, 13, 79, 9], [62, 15, 79, 11], [62, 21, 79, 17], [63, 8, 80, 4, "height"], [63, 14, 80, 10], [63, 16, 80, 12], [63, 22, 80, 18], [64, 8, 81, 4, "overflow"], [64, 16, 81, 12], [64, 18, 81, 14, "props"], [64, 23, 81, 19], [64, 24, 81, 20, "scrollEnabled"], [64, 37, 81, 33], [64, 42, 81, 38], [64, 47, 81, 43], [64, 50, 81, 46], [64, 58, 81, 54], [64, 61, 81, 57], [64, 67, 81, 63], [65, 8, 82, 4], [65, 11, 82, 8, "style"], [66, 6, 83, 3], [66, 7, 83, 5], [67, 6, 84, 3, "allow"], [67, 11, 84, 8], [67, 13, 84, 9], [67, 34, 84, 30], [68, 6, 85, 3, "onLoad"], [68, 12, 85, 9], [68, 14, 85, 12, "e"], [68, 15, 85, 13], [68, 19, 85, 18], [69, 8, 86, 4, "onLoadStart"], [69, 19, 86, 15], [69, 22, 86, 18], [69, 23, 86, 19], [70, 8, 87, 4, "onLoad"], [70, 14, 87, 10], [70, 17, 87, 13], [70, 18, 87, 14], [71, 8, 88, 4, "onLoadEnd"], [71, 17, 88, 13], [71, 20, 88, 16], [71, 21, 88, 17], [72, 8, 89, 4], [72, 12, 89, 8, "injectedJavaScript"], [72, 30, 89, 26], [72, 32, 89, 28], [73, 10, 90, 5, "iframeRef"], [73, 19, 90, 14], [73, 20, 90, 15, "current"], [73, 27, 90, 22], [73, 29, 90, 24, "contentWindow"], [73, 42, 90, 37], [73, 44, 90, 39, "postMessage"], [73, 55, 90, 50], [73, 56, 91, 6, "injectedJavaScript"], [73, 74, 91, 24], [73, 76, 92, 6], [73, 79, 93, 5], [73, 80, 93, 6], [74, 8, 94, 4], [75, 8, 95, 4], [75, 14, 95, 10, "win"], [75, 17, 95, 13], [75, 20, 95, 16, "e"], [75, 21, 95, 17], [75, 22, 95, 18, "currentTarget"], [75, 35, 95, 31], [75, 36, 95, 32, "contentWindow"], [75, 49, 95, 45], [76, 8, 96, 4], [76, 12, 96, 8, "win"], [76, 15, 96, 11], [76, 17, 96, 13], [77, 10, 97, 5, "onNavigationStateChange"], [77, 33, 97, 28], [77, 36, 97, 31], [78, 12, 98, 6, "url"], [78, 15, 98, 9], [78, 17, 98, 11, "win"], [78, 20, 98, 14], [78, 21, 98, 15, "location"], [78, 29, 98, 23], [78, 30, 98, 24, "href"], [78, 34, 98, 28], [79, 12, 99, 6, "loading"], [79, 19, 99, 13], [79, 21, 99, 15], [79, 26, 99, 20], [80, 12, 100, 6, "canGoBack"], [80, 21, 100, 15], [80, 23, 100, 17, "win"], [80, 26, 100, 20], [80, 27, 100, 21, "history"], [80, 34, 100, 28], [80, 35, 100, 29, "length"], [80, 41, 100, 35], [80, 44, 100, 38], [80, 45, 100, 39], [81, 12, 101, 6, "canGoForward"], [81, 24, 101, 18], [81, 26, 101, 20, "win"], [81, 29, 101, 23], [81, 30, 101, 24, "history"], [81, 37, 101, 31], [81, 38, 101, 32, "length"], [81, 44, 101, 38], [81, 47, 101, 41], [82, 10, 102, 5], [82, 11, 102, 6], [82, 12, 102, 7], [83, 8, 103, 4], [84, 6, 104, 3], [85, 4, 104, 5], [85, 5, 105, 3], [85, 6, 105, 4], [86, 2, 107, 0], [86, 3, 107, 1], [86, 37, 107, 2], [87, 2, 107, 3, "_c2"], [87, 5, 107, 3], [87, 8, 29, 13, "WebView"], [87, 15, 29, 20], [88, 2, 29, 20], [88, 6, 29, 20, "_default"], [88, 14, 29, 20], [88, 17, 29, 20, "exports"], [88, 24, 29, 20], [88, 25, 29, 20, "default"], [88, 32, 29, 20], [88, 35, 109, 15, "WebView"], [88, 42, 109, 22], [89, 2, 109, 22], [89, 6, 109, 22, "_c"], [89, 8, 109, 22], [89, 10, 109, 22, "_c2"], [89, 13, 109, 22], [90, 2, 109, 22, "$RefreshReg$"], [90, 14, 109, 22], [90, 15, 109, 22, "_c"], [90, 17, 109, 22], [91, 2, 109, 22, "$RefreshReg$"], [91, 14, 109, 22], [91, 15, 109, 22, "_c2"], [91, 18, 109, 22], [92, 0, 109, 22], [92, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0", "useEffect$argument_0", "handleMessage", "<anonymous>", "useImperativeHandle$argument_1", "injectJavaScript", "goBack", "goForward", "reload", "stopLoading", "iframe.props.onLoad"], "mappings": "AAA;kCC4B;WCa;wBCC;GDE;SEE,0DF;EDC;0BIG;oBCC;GDE;UEC;GFE;aGC;GHE;UIC;GJE;eKC;GLE;GJC;WUkB;IVmB;CDG"}}, "type": "js/module"}]}