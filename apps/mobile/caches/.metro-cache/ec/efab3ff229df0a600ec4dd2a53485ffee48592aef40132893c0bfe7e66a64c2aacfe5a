{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MINIMAL_RECOGNIZABLE_MAGNITUDE = exports.DEFAULT_TOUCH_SLOP = void 0;\n  const DEFAULT_TOUCH_SLOP = exports.DEFAULT_TOUCH_SLOP = 15;\n  const MINIMAL_RECOGNIZABLE_MAGNITUDE = exports.MINIMAL_RECOGNIZABLE_MAGNITUDE = 0.1;\n});", "lineCount": 8, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "DEFAULT_TOUCH_SLOP"], [6, 26, 1, 31], [6, 29, 1, 31, "exports"], [6, 36, 1, 31], [6, 37, 1, 31, "DEFAULT_TOUCH_SLOP"], [6, 55, 1, 31], [6, 58, 1, 34], [6, 60, 1, 36], [7, 2, 2, 7], [7, 8, 2, 13, "MINIMAL_RECOGNIZABLE_MAGNITUDE"], [7, 38, 2, 43], [7, 41, 2, 43, "exports"], [7, 48, 2, 43], [7, 49, 2, 43, "MINIMAL_RECOGNIZABLE_MAGNITUDE"], [7, 79, 2, 43], [7, 82, 2, 46], [7, 85, 2, 49], [8, 0, 2, 50], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}