{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Clipboard", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "EoqFylXpkoRN0tc8bG15zEhaXMg=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 173}, "end": {"line": 11, "column": 67, "index": 240}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "@expo-google-fonts/poppins", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 241}, "end": {"line": 17, "column": 36, "index": 364}}], "key": "93KjGKc6jvVjlVN1YecD1DDS1H8=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 365}, "end": {"line": 18, "column": 52, "index": 417}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-haptics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 418}, "end": {"line": 19, "column": 40, "index": 458}}], "key": "XxdHVlyuI491+26LeQ/AtfIvhac=", "exportNames": ["*"]}}, {"name": "@/components/useColors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 459}, "end": {"line": 20, "column": 51, "index": 510}}], "key": "3ab4QU2peLb6saUoQy2j7r8cv3g=", "exportNames": ["*"]}}, {"name": "@/components/KeyboardAvoidingAnimatedView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 511}, "end": {"line": 21, "column": 85, "index": 596}}], "key": "vTs57pHNFfIlJpzL3XLoFNq597M=", "exportNames": ["*"]}}, {"name": "expo-audio", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 597}, "end": {"line": 27, "column": 20, "index": 727}}], "key": "GHpNh+2in6lWH3tZa8Mq2h9os0E=", "exportNames": ["*"]}}, {"name": "@/utils/fakeData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 728}, "end": {"line": 28, "column": 66, "index": 794}}], "key": "Elsy/Ao9Ieroakc4uLbVzk5yElE=", "exportNames": ["*"]}}, {"name": "@/components/Header", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 29, "column": 0, "index": 795}, "end": {"line": 29, "column": 41, "index": 836}}], "key": "2061LTT5YnFC4VuXbbTSL9Gikq0=", "exportNames": ["*"]}}, {"name": "@/components/TextMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0, "index": 837}, "end": {"line": 30, "column": 45, "index": 882}}], "key": "j9qYQMmTtvPKE8yIYFdu7QhECCQ=", "exportNames": ["*"]}}, {"name": "@/components/VoiceMode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 31, "column": 0, "index": 883}, "end": {"line": 31, "column": 47, "index": 930}}], "key": "xdqZHtdPKFRql2lF9EtUJ/lPm+Y=", "exportNames": ["*"]}}, {"name": "@/components/MessageBubble", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 32, "column": 0, "index": 931}, "end": {"line": 32, "column": 55, "index": 986}}], "key": "Rmi0QyIYQtkXTDPR3flVI2XzJus=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BrainstormScreen;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/ScrollView\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Alert\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Modal\"));\n  var _Clipboard = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Clipboard\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[9], \"react-native-safe-area-context\");\n  var _poppins = require(_dependencyMap[10], \"@expo-google-fonts/poppins\");\n  var _lucideReactNative = require(_dependencyMap[11], \"lucide-react-native\");\n  var Haptics = _interopRequireWildcard(require(_dependencyMap[12], \"expo-haptics\"));\n  var _useColors = require(_dependencyMap[13], \"@/components/useColors\");\n  var _KeyboardAvoidingAnimatedView = _interopRequireDefault(require(_dependencyMap[14], \"@/components/KeyboardAvoidingAnimatedView\"));\n  var _expoAudio = require(_dependencyMap[15], \"expo-audio\");\n  var _fakeData = require(_dependencyMap[16], \"@/utils/fakeData\");\n  var _Header = _interopRequireDefault(require(_dependencyMap[17], \"@/components/Header\"));\n  var _TextMode = _interopRequireDefault(require(_dependencyMap[18], \"@/components/TextMode\"));\n  var _VoiceMode = _interopRequireDefault(require(_dependencyMap[19], \"@/components/VoiceMode\"));\n  var _MessageBubble = _interopRequireDefault(require(_dependencyMap[20], \"@/components/MessageBubble\"));\n  var _jsxDevRuntime = require(_dependencyMap[21], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/src/app/(tabs)/index.jsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function BrainstormScreen() {\n    _s();\n    const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();\n    const colors = (0, _useColors.useColors)();\n    const [fontsLoaded] = (0, _poppins.useFonts)({\n      Poppins_400Regular: _poppins.Poppins_400Regular,\n      Poppins_500Medium: _poppins.Poppins_500Medium,\n      Poppins_600SemiBold: _poppins.Poppins_600SemiBold\n    });\n    const recorder = (0, _expoAudio.useAudioRecorder)(_expoAudio.RecordingPresets.HIGH_QUALITY);\n    const recorderState = (0, _expoAudio.useAudioRecorderState)(recorder);\n    const [messages, setMessages] = (0, _react.useState)([]);\n    const [inputText, setInputText] = (0, _react.useState)(\"\");\n    const [isFirstTime, setIsFirstTime] = (0, _react.useState)(true);\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const [quickActions, setQuickActions] = (0, _react.useState)([]);\n    const [voiceMode, setVoiceMode] = (0, _react.useState)(true); // Voice mode on by default\n    const [hasPermission, setHasPermission] = (0, _react.useState)(false);\n    const [isDictating, setIsDictating] = (0, _react.useState)(false);\n    const [longPressedMessage, setLongPressedMessage] = (0, _react.useState)(null);\n    const [isContextMenuVisible, setIsContextMenuVisible] = (0, _react.useState)(false);\n    const [transcript, setTranscript] = (0, _react.useState)([]);\n    const [isMuted, setIsMuted] = (0, _react.useState)(false);\n    const scrollViewRef = (0, _react.useRef)(null);\n\n    // Request recording permissions\n    (0, _react.useEffect)(() => {\n      (async () => {\n        const {\n          granted\n        } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n        setHasPermission(granted);\n        if (!granted && voiceMode) {\n          _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording. You can still use text mode.\", [{\n            text: \"Use Text Mode\",\n            onPress: () => setVoiceMode(false)\n          }, {\n            text: \"OK\"\n          }]);\n        }\n      })();\n    }, [voiceMode]);\n    const handleStartBrainstorming = () => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      setIsFirstTime(false);\n      setMessages(_fakeData.fakeMessages);\n      setQuickActions(_fakeData.fakeQuickActions);\n    };\n    const handleSendMessage = (message = inputText) => {\n      if (!message.trim()) return;\n      const newUserMessage = {\n        role: \"user\",\n        content: message\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      setInputText(\"\");\n      setQuickActions([]);\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          role: \"assistant\",\n          content: \"That's a fascinating idea! Could you elaborate on the target audience?\"\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setQuickActions(_fakeData.fakeQuickActions);\n        setIsLoading(false);\n      }, 1500);\n    };\n    const handleQuickAction = action => {\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      handleSendMessage(action);\n    };\n    const toggleVoiceMode = async () => {\n      if (!voiceMode && !hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\", [{\n          text: \"Cancel\"\n        }, {\n          text: \"Grant Permission\",\n          onPress: async () => {\n            const {\n              granted\n            } = await (0, _expoAudio.requestRecordingPermissionsAsync)();\n            if (granted) {\n              setHasPermission(true);\n              setVoiceMode(true);\n              // Auto-start voice session when switching to voice mode\n              await startVoiceSession();\n            }\n          }\n        }]);\n        return;\n      }\n      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n      if (!voiceMode) {\n        // Switching to voice mode - auto-start session\n        setVoiceMode(true);\n        if (hasPermission) {\n          await startVoiceSession();\n        }\n      } else {\n        // Switching to text mode - stop any active session\n        setVoiceMode(false);\n        if (recorderState.isRecording) {\n          await recorder.stop();\n        }\n        setIsMuted(false); // Reset mute state\n      }\n    };\n    const startVoiceSession = async () => {\n      if (!hasPermission) return;\n      try {\n        await recorder.prepareToRecordAsync();\n        recorder.record();\n        setIsMuted(false); // Ensure we start unmuted\n        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n      } catch (error) {\n        console.error(\"Error starting voice session:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to start voice session\");\n      }\n    };\n    const stopVoiceSession = async () => {\n      try {\n        if (recorderState.isRecording) {\n          await recorder.stop();\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error stopping voice session:\", error);\n      }\n    };\n    const handleDictation = async () => {\n      if (!hasPermission) {\n        _Alert.default.alert(\"Permission Required\", \"Microphone access is needed for voice recording.\");\n        return;\n      }\n      try {\n        if (isDictating) {\n          await recorder.stop();\n          const mockTranscript = \"This is a dictated message.\"; // In real app, you'd use speech-to-text service\n          setInputText(inputText + mockTranscript);\n          setIsDictating(false);\n        } else {\n          await recorder.prepareToRecordAsync();\n          recorder.record();\n          setIsDictating(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);\n        }\n      } catch (error) {\n        console.error(\"Error with dictation:\", error);\n        _Alert.default.alert(\"Error\", \"Failed to dictate message\");\n        setIsDictating(false);\n      }\n    };\n    const handleLongPress = message => {\n      setLongPressedMessage(message);\n      setIsContextMenuVisible(true);\n    };\n    const handleCopyMessage = () => {\n      if (longPressedMessage) {\n        _Clipboard.default.setString(longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleListenToMessage = () => {\n      if (longPressedMessage) {\n        // Simulate text-to-speech\n        _Alert.default.alert(\"Listening to message\", longPressedMessage.content);\n        setIsContextMenuVisible(false);\n        setLongPressedMessage(null);\n      }\n    };\n    const handleMute = async () => {\n      if (!hasPermission) return;\n      try {\n        if (isMuted) {\n          // Unmute - start listening again\n          if (!recorderState.isRecording) {\n            await recorder.prepareToRecordAsync();\n            recorder.record();\n          }\n          setIsMuted(false);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        } else {\n          // Mute - stop listening but keep session active\n          if (recorderState.isRecording) {\n            await recorder.stop();\n          }\n          setIsMuted(true);\n          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n        }\n      } catch (error) {\n        console.error(\"Error with mute/unmute:\", error);\n      }\n    };\n\n    // Auto-scroll to bottom when new messages arrive\n    (0, _react.useEffect)(() => {\n      if (scrollViewRef.current) {\n        scrollViewRef.current.scrollToEnd({\n          animated: true\n        });\n      }\n    }, [messages]);\n    if (!fontsLoaded) {\n      return null;\n    }\n\n    // Welcome Screen\n    if (isFirstTime) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: {\n          flex: 1,\n          backgroundColor: colors.background\n        },\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            flex: 1,\n            paddingTop: insets.top + 60,\n            paddingHorizontal: 24,\n            paddingBottom: insets.bottom + 24,\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              width: 120,\n              height: 120,\n              borderRadius: 60,\n              backgroundColor: colors.primaryUltraLight,\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginBottom: 32\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.MessageSquare, {\n              size: 48,\n              color: colors.primary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 28,\n              fontFamily: \"Poppins_600SemiBold\",\n              color: colors.text,\n              textAlign: \"center\",\n              marginBottom: 16\n            },\n            children: \"AI Brainstorming Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: {\n              fontSize: 16,\n              fontFamily: \"Poppins_400Regular\",\n              color: colors.textSecondary,\n              textAlign: \"center\",\n              lineHeight: 24,\n              marginBottom: 48\n            },\n            children: \"Get expert guidance for app ideas, business planning, creative writing, and more. Start your first session now!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            style: {\n              backgroundColor: colors.primary,\n              borderRadius: 16,\n              paddingHorizontal: 32,\n              paddingVertical: 16,\n              minWidth: 200,\n              alignItems: \"center\"\n            },\n            onPress: handleStartBrainstorming,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                fontSize: 18,\n                fontFamily: \"Poppins_600SemiBold\",\n                color: colors.background\n              },\n              children: \"Start Brainstorming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 7\n      }, this);\n    }\n\n    // Conversation Screen\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: {\n        flex: 1,\n        backgroundColor: colors.background\n      },\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Header.default, {\n        voiceMode: voiceMode,\n        onToggleVoiceMode: toggleVoiceMode,\n        onDone: () => _Alert.default.alert(\"Session Done\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 7\n      }, this), voiceMode ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_VoiceMode.default, {\n        isRecording: recorderState.isRecording,\n        onRecord: handleVoiceRecord,\n        hasPermission: hasPermission,\n        isLoading: isLoading,\n        transcript: transcript,\n        isMuted: isMuted,\n        onMute: handleMute\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_KeyboardAvoidingAnimatedView.default, {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n          ref: scrollViewRef,\n          style: {\n            flex: 1\n          },\n          contentContainerStyle: {\n            paddingHorizontal: 16,\n            paddingVertical: 16,\n            paddingBottom: 120 // Space for input area\n          },\n          showsVerticalScrollIndicator: false,\n          children: [messages.map((message, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_MessageBubble.default, {\n            message: message,\n            onLongPress: handleLongPress\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              marginBottom: 16,\n              alignSelf: \"flex-start\",\n              maxWidth: \"80%\"\n            },\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: {\n                backgroundColor: colors.cardBackground,\n                borderRadius: 16,\n                paddingHorizontal: 16,\n                paddingVertical: 12,\n                borderWidth: 1,\n                borderColor: colors.outline\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: \"Poppins_400Regular\",\n                  color: colors.textSecondary,\n                  lineHeight: 22\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), quickActions.length > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: insets.bottom + 100,\n            left: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ScrollView.default, {\n            horizontal: true,\n            showsHorizontalScrollIndicator: false,\n            contentContainerStyle: {\n              gap: 8\n            },\n            children: quickActions.map((action, index) => /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              style: {\n                backgroundColor: colors.primaryUltraLight,\n                borderRadius: 20,\n                paddingHorizontal: 16,\n                paddingVertical: 8,\n                borderWidth: 1,\n                borderColor: colors.primary\n              },\n              onPress: () => handleQuickAction(action),\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 14,\n                  fontFamily: \"Poppins_500Medium\",\n                  color: colors.primary\n                },\n                children: action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: {\n            position: \"absolute\",\n            bottom: 0,\n            left: 0,\n            right: 0\n          },\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextMode.default, {\n            inputText: inputText,\n            onInputChange: setInputText,\n            onSendMessage: () => handleSendMessage(),\n            onStartDictation: handleDictation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        transparent: true,\n        visible: isContextMenuVisible,\n        onRequestClose: () => setIsContextMenuVisible(false),\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n          style: {\n            flex: 1,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            justifyContent: 'center',\n            alignItems: 'center'\n          },\n          activeOpacity: 1,\n          onPressOut: () => setIsContextMenuVisible(false),\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: colors.background,\n              borderRadius: 16,\n              padding: 16,\n              width: '80%'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleCopyMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Copy Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 13\n            }, this), longPressedMessage?.role === 'assistant' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: handleListenToMessage,\n              style: {\n                paddingVertical: 12\n              },\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: {\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium',\n                  color: colors.text\n                },\n                children: \"Listen to Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 5\n    }, this);\n  }\n  _s(BrainstormScreen, \"9C1WkPoISXmHucsqM0n1HH2DnhY=\", false, function () {\n    return [_reactNativeSafeAreaContext.useSafeAreaInsets, _useColors.useColors, _poppins.useFonts, _expoAudio.useAudioRecorder, _expoAudio.useAudioRecorderState];\n  });\n  _c = BrainstormScreen;\n  var _c;\n  $RefreshReg$(_c, \"BrainstormScreen\");\n});", "lineCount": 600, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireWildcard"], [7, 38, 1, 0], [7, 39, 1, 0, "require"], [7, 46, 1, 0], [7, 47, 1, 0, "_dependencyMap"], [7, 61, 1, 0], [8, 2, 1, 59], [8, 6, 1, 59, "_View"], [8, 11, 1, 59], [8, 14, 1, 59, "_interopRequireDefault"], [8, 36, 1, 59], [8, 37, 1, 59, "require"], [8, 44, 1, 59], [8, 45, 1, 59, "_dependencyMap"], [8, 59, 1, 59], [9, 2, 1, 59], [9, 6, 1, 59, "_Text"], [9, 11, 1, 59], [9, 14, 1, 59, "_interopRequireDefault"], [9, 36, 1, 59], [9, 37, 1, 59, "require"], [9, 44, 1, 59], [9, 45, 1, 59, "_dependencyMap"], [9, 59, 1, 59], [10, 2, 1, 59], [10, 6, 1, 59, "_TouchableOpacity"], [10, 23, 1, 59], [10, 26, 1, 59, "_interopRequireDefault"], [10, 48, 1, 59], [10, 49, 1, 59, "require"], [10, 56, 1, 59], [10, 57, 1, 59, "_dependencyMap"], [10, 71, 1, 59], [11, 2, 1, 59], [11, 6, 1, 59, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 17, 1, 59], [11, 20, 1, 59, "_interopRequireDefault"], [11, 42, 1, 59], [11, 43, 1, 59, "require"], [11, 50, 1, 59], [11, 51, 1, 59, "_dependencyMap"], [11, 65, 1, 59], [12, 2, 1, 59], [12, 6, 1, 59, "_<PERSON><PERSON>"], [12, 12, 1, 59], [12, 15, 1, 59, "_interopRequireDefault"], [12, 37, 1, 59], [12, 38, 1, 59, "require"], [12, 45, 1, 59], [12, 46, 1, 59, "_dependencyMap"], [12, 60, 1, 59], [13, 2, 1, 59], [13, 6, 1, 59, "_Modal"], [13, 12, 1, 59], [13, 15, 1, 59, "_interopRequireDefault"], [13, 37, 1, 59], [13, 38, 1, 59, "require"], [13, 45, 1, 59], [13, 46, 1, 59, "_dependencyMap"], [13, 60, 1, 59], [14, 2, 1, 59], [14, 6, 1, 59, "_Clipboard"], [14, 16, 1, 59], [14, 19, 1, 59, "_interopRequireDefault"], [14, 41, 1, 59], [14, 42, 1, 59, "require"], [14, 49, 1, 59], [14, 50, 1, 59, "_dependencyMap"], [14, 64, 1, 59], [15, 2, 11, 0], [15, 6, 11, 0, "_reactNativeSafeAreaContext"], [15, 33, 11, 0], [15, 36, 11, 0, "require"], [15, 43, 11, 0], [15, 44, 11, 0, "_dependencyMap"], [15, 58, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_poppins"], [16, 14, 12, 0], [16, 17, 12, 0, "require"], [16, 24, 12, 0], [16, 25, 12, 0, "_dependencyMap"], [16, 39, 12, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_lucideReactNative"], [17, 24, 18, 0], [17, 27, 18, 0, "require"], [17, 34, 18, 0], [17, 35, 18, 0, "_dependencyMap"], [17, 49, 18, 0], [18, 2, 19, 0], [18, 6, 19, 0, "Haptics"], [18, 13, 19, 0], [18, 16, 19, 0, "_interopRequireWildcard"], [18, 39, 19, 0], [18, 40, 19, 0, "require"], [18, 47, 19, 0], [18, 48, 19, 0, "_dependencyMap"], [18, 62, 19, 0], [19, 2, 20, 0], [19, 6, 20, 0, "_useColors"], [19, 16, 20, 0], [19, 19, 20, 0, "require"], [19, 26, 20, 0], [19, 27, 20, 0, "_dependencyMap"], [19, 41, 20, 0], [20, 2, 21, 0], [20, 6, 21, 0, "_KeyboardAvoidingAnimatedView"], [20, 35, 21, 0], [20, 38, 21, 0, "_interopRequireDefault"], [20, 60, 21, 0], [20, 61, 21, 0, "require"], [20, 68, 21, 0], [20, 69, 21, 0, "_dependencyMap"], [20, 83, 21, 0], [21, 2, 22, 0], [21, 6, 22, 0, "_expoAudio"], [21, 16, 22, 0], [21, 19, 22, 0, "require"], [21, 26, 22, 0], [21, 27, 22, 0, "_dependencyMap"], [21, 41, 22, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_fakeData"], [22, 15, 28, 0], [22, 18, 28, 0, "require"], [22, 25, 28, 0], [22, 26, 28, 0, "_dependencyMap"], [22, 40, 28, 0], [23, 2, 29, 0], [23, 6, 29, 0, "_Header"], [23, 13, 29, 0], [23, 16, 29, 0, "_interopRequireDefault"], [23, 38, 29, 0], [23, 39, 29, 0, "require"], [23, 46, 29, 0], [23, 47, 29, 0, "_dependencyMap"], [23, 61, 29, 0], [24, 2, 30, 0], [24, 6, 30, 0, "_TextMode"], [24, 15, 30, 0], [24, 18, 30, 0, "_interopRequireDefault"], [24, 40, 30, 0], [24, 41, 30, 0, "require"], [24, 48, 30, 0], [24, 49, 30, 0, "_dependencyMap"], [24, 63, 30, 0], [25, 2, 31, 0], [25, 6, 31, 0, "_VoiceMode"], [25, 16, 31, 0], [25, 19, 31, 0, "_interopRequireDefault"], [25, 41, 31, 0], [25, 42, 31, 0, "require"], [25, 49, 31, 0], [25, 50, 31, 0, "_dependencyMap"], [25, 64, 31, 0], [26, 2, 32, 0], [26, 6, 32, 0, "_MessageBubble"], [26, 20, 32, 0], [26, 23, 32, 0, "_interopRequireDefault"], [26, 45, 32, 0], [26, 46, 32, 0, "require"], [26, 53, 32, 0], [26, 54, 32, 0, "_dependencyMap"], [26, 68, 32, 0], [27, 2, 32, 55], [27, 6, 32, 55, "_jsxDevRuntime"], [27, 20, 32, 55], [27, 23, 32, 55, "require"], [27, 30, 32, 55], [27, 31, 32, 55, "_dependencyMap"], [27, 45, 32, 55], [28, 2, 32, 55], [28, 6, 32, 55, "_jsxFileName"], [28, 18, 32, 55], [29, 4, 32, 55, "_s"], [29, 6, 32, 55], [29, 9, 32, 55, "$RefreshSig$"], [29, 21, 32, 55], [30, 2, 32, 55], [30, 11, 32, 55, "_interopRequireWildcard"], [30, 35, 32, 55, "e"], [30, 36, 32, 55], [30, 38, 32, 55, "t"], [30, 39, 32, 55], [30, 68, 32, 55, "WeakMap"], [30, 75, 32, 55], [30, 81, 32, 55, "r"], [30, 82, 32, 55], [30, 89, 32, 55, "WeakMap"], [30, 96, 32, 55], [30, 100, 32, 55, "n"], [30, 101, 32, 55], [30, 108, 32, 55, "WeakMap"], [30, 115, 32, 55], [30, 127, 32, 55, "_interopRequireWildcard"], [30, 150, 32, 55], [30, 162, 32, 55, "_interopRequireWildcard"], [30, 163, 32, 55, "e"], [30, 164, 32, 55], [30, 166, 32, 55, "t"], [30, 167, 32, 55], [30, 176, 32, 55, "t"], [30, 177, 32, 55], [30, 181, 32, 55, "e"], [30, 182, 32, 55], [30, 186, 32, 55, "e"], [30, 187, 32, 55], [30, 188, 32, 55, "__esModule"], [30, 198, 32, 55], [30, 207, 32, 55, "e"], [30, 208, 32, 55], [30, 214, 32, 55, "o"], [30, 215, 32, 55], [30, 217, 32, 55, "i"], [30, 218, 32, 55], [30, 220, 32, 55, "f"], [30, 221, 32, 55], [30, 226, 32, 55, "__proto__"], [30, 235, 32, 55], [30, 243, 32, 55, "default"], [30, 250, 32, 55], [30, 252, 32, 55, "e"], [30, 253, 32, 55], [30, 270, 32, 55, "e"], [30, 271, 32, 55], [30, 294, 32, 55, "e"], [30, 295, 32, 55], [30, 320, 32, 55, "e"], [30, 321, 32, 55], [30, 330, 32, 55, "f"], [30, 331, 32, 55], [30, 337, 32, 55, "o"], [30, 338, 32, 55], [30, 341, 32, 55, "t"], [30, 342, 32, 55], [30, 345, 32, 55, "n"], [30, 346, 32, 55], [30, 349, 32, 55, "r"], [30, 350, 32, 55], [30, 358, 32, 55, "o"], [30, 359, 32, 55], [30, 360, 32, 55, "has"], [30, 363, 32, 55], [30, 364, 32, 55, "e"], [30, 365, 32, 55], [30, 375, 32, 55, "o"], [30, 376, 32, 55], [30, 377, 32, 55, "get"], [30, 380, 32, 55], [30, 381, 32, 55, "e"], [30, 382, 32, 55], [30, 385, 32, 55, "o"], [30, 386, 32, 55], [30, 387, 32, 55, "set"], [30, 390, 32, 55], [30, 391, 32, 55, "e"], [30, 392, 32, 55], [30, 394, 32, 55, "f"], [30, 395, 32, 55], [30, 411, 32, 55, "t"], [30, 412, 32, 55], [30, 416, 32, 55, "e"], [30, 417, 32, 55], [30, 433, 32, 55, "t"], [30, 434, 32, 55], [30, 441, 32, 55, "hasOwnProperty"], [30, 455, 32, 55], [30, 456, 32, 55, "call"], [30, 460, 32, 55], [30, 461, 32, 55, "e"], [30, 462, 32, 55], [30, 464, 32, 55, "t"], [30, 465, 32, 55], [30, 472, 32, 55, "i"], [30, 473, 32, 55], [30, 477, 32, 55, "o"], [30, 478, 32, 55], [30, 481, 32, 55, "Object"], [30, 487, 32, 55], [30, 488, 32, 55, "defineProperty"], [30, 502, 32, 55], [30, 507, 32, 55, "Object"], [30, 513, 32, 55], [30, 514, 32, 55, "getOwnPropertyDescriptor"], [30, 538, 32, 55], [30, 539, 32, 55, "e"], [30, 540, 32, 55], [30, 542, 32, 55, "t"], [30, 543, 32, 55], [30, 550, 32, 55, "i"], [30, 551, 32, 55], [30, 552, 32, 55, "get"], [30, 555, 32, 55], [30, 559, 32, 55, "i"], [30, 560, 32, 55], [30, 561, 32, 55, "set"], [30, 564, 32, 55], [30, 568, 32, 55, "o"], [30, 569, 32, 55], [30, 570, 32, 55, "f"], [30, 571, 32, 55], [30, 573, 32, 55, "t"], [30, 574, 32, 55], [30, 576, 32, 55, "i"], [30, 577, 32, 55], [30, 581, 32, 55, "f"], [30, 582, 32, 55], [30, 583, 32, 55, "t"], [30, 584, 32, 55], [30, 588, 32, 55, "e"], [30, 589, 32, 55], [30, 590, 32, 55, "t"], [30, 591, 32, 55], [30, 602, 32, 55, "f"], [30, 603, 32, 55], [30, 608, 32, 55, "e"], [30, 609, 32, 55], [30, 611, 32, 55, "t"], [30, 612, 32, 55], [31, 2, 34, 15], [31, 11, 34, 24, "BrainstormScreen"], [31, 27, 34, 40, "BrainstormScreen"], [31, 28, 34, 40], [31, 30, 34, 43], [32, 4, 34, 43, "_s"], [32, 6, 34, 43], [33, 4, 35, 2], [33, 10, 35, 8, "insets"], [33, 16, 35, 14], [33, 19, 35, 17], [33, 23, 35, 17, "useSafeAreaInsets"], [33, 68, 35, 34], [33, 70, 35, 35], [33, 71, 35, 36], [34, 4, 36, 2], [34, 10, 36, 8, "colors"], [34, 16, 36, 14], [34, 19, 36, 17], [34, 23, 36, 17, "useColors"], [34, 43, 36, 26], [34, 45, 36, 27], [34, 46, 36, 28], [35, 4, 37, 2], [35, 10, 37, 8], [35, 11, 37, 9, "fontsLoaded"], [35, 22, 37, 20], [35, 23, 37, 21], [35, 26, 37, 24], [35, 30, 37, 24, "useFonts"], [35, 47, 37, 32], [35, 49, 37, 33], [36, 6, 38, 4, "Poppins_400Regular"], [36, 24, 38, 22], [36, 26, 38, 4, "Poppins_400Regular"], [36, 53, 38, 22], [37, 6, 39, 4, "Poppins_500Medium"], [37, 23, 39, 21], [37, 25, 39, 4, "Poppins_500Medium"], [37, 51, 39, 21], [38, 6, 40, 4, "Poppins_600SemiBold"], [38, 25, 40, 23], [38, 27, 40, 4, "Poppins_600SemiBold"], [39, 4, 41, 2], [39, 5, 41, 3], [39, 6, 41, 4], [40, 4, 43, 2], [40, 10, 43, 8, "recorder"], [40, 18, 43, 16], [40, 21, 43, 19], [40, 25, 43, 19, "useAudioRecorder"], [40, 52, 43, 35], [40, 54, 43, 36, "RecordingPresets"], [40, 81, 43, 52], [40, 82, 43, 53, "HIGH_QUALITY"], [40, 94, 43, 65], [40, 95, 43, 66], [41, 4, 44, 2], [41, 10, 44, 8, "recorderState"], [41, 23, 44, 21], [41, 26, 44, 24], [41, 30, 44, 24, "useAudioRecorderState"], [41, 62, 44, 45], [41, 64, 44, 46, "recorder"], [41, 72, 44, 54], [41, 73, 44, 55], [42, 4, 46, 2], [42, 10, 46, 8], [42, 11, 46, 9, "messages"], [42, 19, 46, 17], [42, 21, 46, 19, "setMessages"], [42, 32, 46, 30], [42, 33, 46, 31], [42, 36, 46, 34], [42, 40, 46, 34, "useState"], [42, 55, 46, 42], [42, 57, 46, 43], [42, 59, 46, 45], [42, 60, 46, 46], [43, 4, 47, 2], [43, 10, 47, 8], [43, 11, 47, 9, "inputText"], [43, 20, 47, 18], [43, 22, 47, 20, "setInputText"], [43, 34, 47, 32], [43, 35, 47, 33], [43, 38, 47, 36], [43, 42, 47, 36, "useState"], [43, 57, 47, 44], [43, 59, 47, 45], [43, 61, 47, 47], [43, 62, 47, 48], [44, 4, 48, 2], [44, 10, 48, 8], [44, 11, 48, 9, "isFirstTime"], [44, 22, 48, 20], [44, 24, 48, 22, "setIsFirstTime"], [44, 38, 48, 36], [44, 39, 48, 37], [44, 42, 48, 40], [44, 46, 48, 40, "useState"], [44, 61, 48, 48], [44, 63, 48, 49], [44, 67, 48, 53], [44, 68, 48, 54], [45, 4, 49, 2], [45, 10, 49, 8], [45, 11, 49, 9, "isLoading"], [45, 20, 49, 18], [45, 22, 49, 20, "setIsLoading"], [45, 34, 49, 32], [45, 35, 49, 33], [45, 38, 49, 36], [45, 42, 49, 36, "useState"], [45, 57, 49, 44], [45, 59, 49, 45], [45, 64, 49, 50], [45, 65, 49, 51], [46, 4, 50, 2], [46, 10, 50, 8], [46, 11, 50, 9, "quickActions"], [46, 23, 50, 21], [46, 25, 50, 23, "setQuickActions"], [46, 40, 50, 38], [46, 41, 50, 39], [46, 44, 50, 42], [46, 48, 50, 42, "useState"], [46, 63, 50, 50], [46, 65, 50, 51], [46, 67, 50, 53], [46, 68, 50, 54], [47, 4, 51, 2], [47, 10, 51, 8], [47, 11, 51, 9, "voiceMode"], [47, 20, 51, 18], [47, 22, 51, 20, "setVoiceMode"], [47, 34, 51, 32], [47, 35, 51, 33], [47, 38, 51, 36], [47, 42, 51, 36, "useState"], [47, 57, 51, 44], [47, 59, 51, 45], [47, 63, 51, 49], [47, 64, 51, 50], [47, 65, 51, 51], [47, 66, 51, 52], [48, 4, 52, 2], [48, 10, 52, 8], [48, 11, 52, 9, "hasPermission"], [48, 24, 52, 22], [48, 26, 52, 24, "setHasPermission"], [48, 42, 52, 40], [48, 43, 52, 41], [48, 46, 52, 44], [48, 50, 52, 44, "useState"], [48, 65, 52, 52], [48, 67, 52, 53], [48, 72, 52, 58], [48, 73, 52, 59], [49, 4, 53, 2], [49, 10, 53, 8], [49, 11, 53, 9, "isDictating"], [49, 22, 53, 20], [49, 24, 53, 22, "setIsDictating"], [49, 38, 53, 36], [49, 39, 53, 37], [49, 42, 53, 40], [49, 46, 53, 40, "useState"], [49, 61, 53, 48], [49, 63, 53, 49], [49, 68, 53, 54], [49, 69, 53, 55], [50, 4, 54, 2], [50, 10, 54, 8], [50, 11, 54, 9, "longPressedMessage"], [50, 29, 54, 27], [50, 31, 54, 29, "setLongPressedMessage"], [50, 52, 54, 50], [50, 53, 54, 51], [50, 56, 54, 54], [50, 60, 54, 54, "useState"], [50, 75, 54, 62], [50, 77, 54, 63], [50, 81, 54, 67], [50, 82, 54, 68], [51, 4, 55, 2], [51, 10, 55, 8], [51, 11, 55, 9, "isContextMenuVisible"], [51, 31, 55, 29], [51, 33, 55, 31, "setIsContextMenuVisible"], [51, 56, 55, 54], [51, 57, 55, 55], [51, 60, 55, 58], [51, 64, 55, 58, "useState"], [51, 79, 55, 66], [51, 81, 55, 67], [51, 86, 55, 72], [51, 87, 55, 73], [52, 4, 56, 2], [52, 10, 56, 8], [52, 11, 56, 9, "transcript"], [52, 21, 56, 19], [52, 23, 56, 21, "setTranscript"], [52, 36, 56, 34], [52, 37, 56, 35], [52, 40, 56, 38], [52, 44, 56, 38, "useState"], [52, 59, 56, 46], [52, 61, 56, 47], [52, 63, 56, 49], [52, 64, 56, 50], [53, 4, 57, 2], [53, 10, 57, 8], [53, 11, 57, 9, "isMuted"], [53, 18, 57, 16], [53, 20, 57, 18, "setIsMuted"], [53, 30, 57, 28], [53, 31, 57, 29], [53, 34, 57, 32], [53, 38, 57, 32, "useState"], [53, 53, 57, 40], [53, 55, 57, 41], [53, 60, 57, 46], [53, 61, 57, 47], [54, 4, 59, 2], [54, 10, 59, 8, "scrollViewRef"], [54, 23, 59, 21], [54, 26, 59, 24], [54, 30, 59, 24, "useRef"], [54, 43, 59, 30], [54, 45, 59, 31], [54, 49, 59, 35], [54, 50, 59, 36], [56, 4, 61, 2], [57, 4, 62, 2], [57, 8, 62, 2, "useEffect"], [57, 24, 62, 11], [57, 26, 62, 12], [57, 32, 62, 18], [58, 6, 63, 4], [58, 7, 63, 5], [58, 19, 63, 17], [59, 8, 64, 6], [59, 14, 64, 12], [60, 10, 64, 14, "granted"], [61, 8, 64, 22], [61, 9, 64, 23], [61, 12, 64, 26], [61, 18, 64, 32], [61, 22, 64, 32, "requestRecordingPermissionsAsync"], [61, 65, 64, 64], [61, 67, 64, 65], [61, 68, 64, 66], [62, 8, 65, 6, "setHasPermission"], [62, 24, 65, 22], [62, 25, 65, 23, "granted"], [62, 32, 65, 30], [62, 33, 65, 31], [63, 8, 66, 6], [63, 12, 66, 10], [63, 13, 66, 11, "granted"], [63, 20, 66, 18], [63, 24, 66, 22, "voiceMode"], [63, 33, 66, 31], [63, 35, 66, 33], [64, 10, 67, 8, "<PERSON><PERSON>"], [64, 24, 67, 13], [64, 25, 67, 14, "alert"], [64, 30, 67, 19], [64, 31, 68, 10], [64, 52, 68, 31], [64, 54, 69, 10], [64, 133, 69, 89], [64, 135, 70, 10], [64, 136, 71, 12], [65, 12, 71, 14, "text"], [65, 16, 71, 18], [65, 18, 71, 20], [65, 33, 71, 35], [66, 12, 71, 37, "onPress"], [66, 19, 71, 44], [66, 21, 71, 46, "onPress"], [66, 22, 71, 46], [66, 27, 71, 52, "setVoiceMode"], [66, 39, 71, 64], [66, 40, 71, 65], [66, 45, 71, 70], [67, 10, 71, 72], [67, 11, 71, 73], [67, 13, 72, 12], [68, 12, 72, 14, "text"], [68, 16, 72, 18], [68, 18, 72, 20], [69, 10, 72, 25], [69, 11, 72, 26], [69, 12, 74, 8], [69, 13, 74, 9], [70, 8, 75, 6], [71, 6, 76, 4], [71, 7, 76, 5], [71, 9, 76, 7], [71, 10, 76, 8], [72, 4, 77, 2], [72, 5, 77, 3], [72, 7, 77, 5], [72, 8, 77, 6, "voiceMode"], [72, 17, 77, 15], [72, 18, 77, 16], [72, 19, 77, 17], [73, 4, 79, 2], [73, 10, 79, 8, "handleStartBrainstorming"], [73, 34, 79, 32], [73, 37, 79, 35, "handleStartBrainstorming"], [73, 38, 79, 35], [73, 43, 79, 41], [74, 6, 80, 4, "Haptics"], [74, 13, 80, 11], [74, 14, 80, 12, "impactAsync"], [74, 25, 80, 23], [74, 26, 80, 24, "Haptics"], [74, 33, 80, 31], [74, 34, 80, 32, "ImpactFeedbackStyle"], [74, 53, 80, 51], [74, 54, 80, 52, "Medium"], [74, 60, 80, 58], [74, 61, 80, 59], [75, 6, 81, 4, "setIsFirstTime"], [75, 20, 81, 18], [75, 21, 81, 19], [75, 26, 81, 24], [75, 27, 81, 25], [76, 6, 82, 4, "setMessages"], [76, 17, 82, 15], [76, 18, 82, 16, "fakeMessages"], [76, 40, 82, 28], [76, 41, 82, 29], [77, 6, 83, 4, "setQuickActions"], [77, 21, 83, 19], [77, 22, 83, 20, "fakeQuickActions"], [77, 48, 83, 36], [77, 49, 83, 37], [78, 4, 84, 2], [78, 5, 84, 3], [79, 4, 86, 2], [79, 10, 86, 8, "handleSendMessage"], [79, 27, 86, 25], [79, 30, 86, 28, "handleSendMessage"], [79, 31, 86, 29, "message"], [79, 38, 86, 36], [79, 41, 86, 39, "inputText"], [79, 50, 86, 48], [79, 55, 86, 53], [80, 6, 87, 4], [80, 10, 87, 8], [80, 11, 87, 9, "message"], [80, 18, 87, 16], [80, 19, 87, 17, "trim"], [80, 23, 87, 21], [80, 24, 87, 22], [80, 25, 87, 23], [80, 27, 87, 25], [81, 6, 89, 4], [81, 12, 89, 10, "newUserMessage"], [81, 26, 89, 24], [81, 29, 89, 27], [82, 8, 89, 29, "role"], [82, 12, 89, 33], [82, 14, 89, 35], [82, 20, 89, 41], [83, 8, 89, 43, "content"], [83, 15, 89, 50], [83, 17, 89, 52, "message"], [84, 6, 89, 60], [84, 7, 89, 61], [85, 6, 90, 4, "setMessages"], [85, 17, 90, 15], [85, 18, 90, 17, "prev"], [85, 22, 90, 21], [85, 26, 90, 26], [85, 27, 90, 27], [85, 30, 90, 30, "prev"], [85, 34, 90, 34], [85, 36, 90, 36, "newUserMessage"], [85, 50, 90, 50], [85, 51, 90, 51], [85, 52, 90, 52], [86, 6, 91, 4, "setInputText"], [86, 18, 91, 16], [86, 19, 91, 17], [86, 21, 91, 19], [86, 22, 91, 20], [87, 6, 92, 4, "setQuickActions"], [87, 21, 92, 19], [87, 22, 92, 20], [87, 24, 92, 22], [87, 25, 92, 23], [88, 6, 93, 4, "setIsLoading"], [88, 18, 93, 16], [88, 19, 93, 17], [88, 23, 93, 21], [88, 24, 93, 22], [90, 6, 95, 4], [91, 6, 96, 4, "setTimeout"], [91, 16, 96, 14], [91, 17, 96, 15], [91, 23, 96, 21], [92, 8, 97, 6], [92, 14, 97, 12, "aiResponse"], [92, 24, 97, 22], [92, 27, 97, 25], [93, 10, 98, 8, "role"], [93, 14, 98, 12], [93, 16, 98, 14], [93, 27, 98, 25], [94, 10, 99, 8, "content"], [94, 17, 99, 15], [94, 19, 99, 17], [95, 8, 100, 6], [95, 9, 100, 7], [96, 8, 101, 6, "setMessages"], [96, 19, 101, 17], [96, 20, 101, 19, "prev"], [96, 24, 101, 23], [96, 28, 101, 28], [96, 29, 101, 29], [96, 32, 101, 32, "prev"], [96, 36, 101, 36], [96, 38, 101, 38, "aiResponse"], [96, 48, 101, 48], [96, 49, 101, 49], [96, 50, 101, 50], [97, 8, 102, 6, "setQuickActions"], [97, 23, 102, 21], [97, 24, 102, 22, "fakeQuickActions"], [97, 50, 102, 38], [97, 51, 102, 39], [98, 8, 103, 6, "setIsLoading"], [98, 20, 103, 18], [98, 21, 103, 19], [98, 26, 103, 24], [98, 27, 103, 25], [99, 6, 104, 4], [99, 7, 104, 5], [99, 9, 104, 7], [99, 13, 104, 11], [99, 14, 104, 12], [100, 4, 105, 2], [100, 5, 105, 3], [101, 4, 107, 2], [101, 10, 107, 8, "handleQuickAction"], [101, 27, 107, 25], [101, 30, 107, 29, "action"], [101, 36, 107, 35], [101, 40, 107, 40], [102, 6, 108, 4, "Haptics"], [102, 13, 108, 11], [102, 14, 108, 12, "impactAsync"], [102, 25, 108, 23], [102, 26, 108, 24, "Haptics"], [102, 33, 108, 31], [102, 34, 108, 32, "ImpactFeedbackStyle"], [102, 53, 108, 51], [102, 54, 108, 52, "Light"], [102, 59, 108, 57], [102, 60, 108, 58], [103, 6, 109, 4, "handleSendMessage"], [103, 23, 109, 21], [103, 24, 109, 22, "action"], [103, 30, 109, 28], [103, 31, 109, 29], [104, 4, 110, 2], [104, 5, 110, 3], [105, 4, 112, 2], [105, 10, 112, 8, "toggleVoiceMode"], [105, 25, 112, 23], [105, 28, 112, 26], [105, 34, 112, 26, "toggleVoiceMode"], [105, 35, 112, 26], [105, 40, 112, 38], [106, 6, 113, 4], [106, 10, 113, 8], [106, 11, 113, 9, "voiceMode"], [106, 20, 113, 18], [106, 24, 113, 22], [106, 25, 113, 23, "hasPermission"], [106, 38, 113, 36], [106, 40, 113, 38], [107, 8, 114, 6, "<PERSON><PERSON>"], [107, 22, 114, 11], [107, 23, 114, 12, "alert"], [107, 28, 114, 17], [107, 29, 115, 8], [107, 50, 115, 29], [107, 52, 116, 8], [107, 102, 116, 58], [107, 104, 117, 8], [107, 105, 118, 10], [108, 10, 118, 12, "text"], [108, 14, 118, 16], [108, 16, 118, 18], [109, 8, 118, 27], [109, 9, 118, 28], [109, 11, 119, 10], [110, 10, 120, 12, "text"], [110, 14, 120, 16], [110, 16, 120, 18], [110, 34, 120, 36], [111, 10, 121, 12, "onPress"], [111, 17, 121, 19], [111, 19, 121, 21], [111, 25, 121, 21, "onPress"], [111, 26, 121, 21], [111, 31, 121, 33], [112, 12, 122, 14], [112, 18, 122, 20], [113, 14, 122, 22, "granted"], [114, 12, 122, 30], [114, 13, 122, 31], [114, 16, 122, 34], [114, 22, 122, 40], [114, 26, 122, 40, "requestRecordingPermissionsAsync"], [114, 69, 122, 72], [114, 71, 122, 73], [114, 72, 122, 74], [115, 12, 123, 14], [115, 16, 123, 18, "granted"], [115, 23, 123, 25], [115, 25, 123, 27], [116, 14, 124, 16, "setHasPermission"], [116, 30, 124, 32], [116, 31, 124, 33], [116, 35, 124, 37], [116, 36, 124, 38], [117, 14, 125, 16, "setVoiceMode"], [117, 26, 125, 28], [117, 27, 125, 29], [117, 31, 125, 33], [117, 32, 125, 34], [118, 14, 126, 16], [119, 14, 127, 16], [119, 20, 127, 22, "startVoiceSession"], [119, 37, 127, 39], [119, 38, 127, 40], [119, 39, 127, 41], [120, 12, 128, 14], [121, 10, 129, 12], [122, 8, 130, 10], [122, 9, 130, 11], [122, 10, 132, 6], [122, 11, 132, 7], [123, 8, 133, 6], [124, 6, 134, 4], [125, 6, 136, 4, "Haptics"], [125, 13, 136, 11], [125, 14, 136, 12, "impactAsync"], [125, 25, 136, 23], [125, 26, 136, 24, "Haptics"], [125, 33, 136, 31], [125, 34, 136, 32, "ImpactFeedbackStyle"], [125, 53, 136, 51], [125, 54, 136, 52, "Light"], [125, 59, 136, 57], [125, 60, 136, 58], [126, 6, 138, 4], [126, 10, 138, 8], [126, 11, 138, 9, "voiceMode"], [126, 20, 138, 18], [126, 22, 138, 20], [127, 8, 139, 6], [128, 8, 140, 6, "setVoiceMode"], [128, 20, 140, 18], [128, 21, 140, 19], [128, 25, 140, 23], [128, 26, 140, 24], [129, 8, 141, 6], [129, 12, 141, 10, "hasPermission"], [129, 25, 141, 23], [129, 27, 141, 25], [130, 10, 142, 8], [130, 16, 142, 14, "startVoiceSession"], [130, 33, 142, 31], [130, 34, 142, 32], [130, 35, 142, 33], [131, 8, 143, 6], [132, 6, 144, 4], [132, 7, 144, 5], [132, 13, 144, 11], [133, 8, 145, 6], [134, 8, 146, 6, "setVoiceMode"], [134, 20, 146, 18], [134, 21, 146, 19], [134, 26, 146, 24], [134, 27, 146, 25], [135, 8, 147, 6], [135, 12, 147, 10, "recorderState"], [135, 25, 147, 23], [135, 26, 147, 24, "isRecording"], [135, 37, 147, 35], [135, 39, 147, 37], [136, 10, 148, 8], [136, 16, 148, 14, "recorder"], [136, 24, 148, 22], [136, 25, 148, 23, "stop"], [136, 29, 148, 27], [136, 30, 148, 28], [136, 31, 148, 29], [137, 8, 149, 6], [138, 8, 150, 6, "setIsMuted"], [138, 18, 150, 16], [138, 19, 150, 17], [138, 24, 150, 22], [138, 25, 150, 23], [138, 26, 150, 24], [138, 27, 150, 25], [139, 6, 151, 4], [140, 4, 152, 2], [140, 5, 152, 3], [141, 4, 154, 2], [141, 10, 154, 8, "startVoiceSession"], [141, 27, 154, 25], [141, 30, 154, 28], [141, 36, 154, 28, "startVoiceSession"], [141, 37, 154, 28], [141, 42, 154, 40], [142, 6, 155, 4], [142, 10, 155, 8], [142, 11, 155, 9, "hasPermission"], [142, 24, 155, 22], [142, 26, 155, 24], [143, 6, 157, 4], [143, 10, 157, 8], [144, 8, 158, 6], [144, 14, 158, 12, "recorder"], [144, 22, 158, 20], [144, 23, 158, 21, "prepareToRecordAsync"], [144, 43, 158, 41], [144, 44, 158, 42], [144, 45, 158, 43], [145, 8, 159, 6, "recorder"], [145, 16, 159, 14], [145, 17, 159, 15, "record"], [145, 23, 159, 21], [145, 24, 159, 22], [145, 25, 159, 23], [146, 8, 160, 6, "setIsMuted"], [146, 18, 160, 16], [146, 19, 160, 17], [146, 24, 160, 22], [146, 25, 160, 23], [146, 26, 160, 24], [146, 27, 160, 25], [147, 8, 161, 6, "Haptics"], [147, 15, 161, 13], [147, 16, 161, 14, "impactAsync"], [147, 27, 161, 25], [147, 28, 161, 26, "Haptics"], [147, 35, 161, 33], [147, 36, 161, 34, "ImpactFeedbackStyle"], [147, 55, 161, 53], [147, 56, 161, 54, "Medium"], [147, 62, 161, 60], [147, 63, 161, 61], [148, 6, 162, 4], [148, 7, 162, 5], [148, 8, 162, 6], [148, 15, 162, 13, "error"], [148, 20, 162, 18], [148, 22, 162, 20], [149, 8, 163, 6, "console"], [149, 15, 163, 13], [149, 16, 163, 14, "error"], [149, 21, 163, 19], [149, 22, 163, 20], [149, 53, 163, 51], [149, 55, 163, 53, "error"], [149, 60, 163, 58], [149, 61, 163, 59], [150, 8, 164, 6, "<PERSON><PERSON>"], [150, 22, 164, 11], [150, 23, 164, 12, "alert"], [150, 28, 164, 17], [150, 29, 164, 18], [150, 36, 164, 25], [150, 38, 164, 27], [150, 69, 164, 58], [150, 70, 164, 59], [151, 6, 165, 4], [152, 4, 166, 2], [152, 5, 166, 3], [153, 4, 168, 2], [153, 10, 168, 8, "stopVoiceSession"], [153, 26, 168, 24], [153, 29, 168, 27], [153, 35, 168, 27, "stopVoiceSession"], [153, 36, 168, 27], [153, 41, 168, 39], [154, 6, 169, 4], [154, 10, 169, 8], [155, 8, 170, 6], [155, 12, 170, 10, "recorderState"], [155, 25, 170, 23], [155, 26, 170, 24, "isRecording"], [155, 37, 170, 35], [155, 39, 170, 37], [156, 10, 171, 8], [156, 16, 171, 14, "recorder"], [156, 24, 171, 22], [156, 25, 171, 23, "stop"], [156, 29, 171, 27], [156, 30, 171, 28], [156, 31, 171, 29], [157, 10, 172, 8, "Haptics"], [157, 17, 172, 15], [157, 18, 172, 16, "impactAsync"], [157, 29, 172, 27], [157, 30, 172, 28, "Haptics"], [157, 37, 172, 35], [157, 38, 172, 36, "ImpactFeedbackStyle"], [157, 57, 172, 55], [157, 58, 172, 56, "Medium"], [157, 64, 172, 62], [157, 65, 172, 63], [158, 8, 173, 6], [159, 6, 174, 4], [159, 7, 174, 5], [159, 8, 174, 6], [159, 15, 174, 13, "error"], [159, 20, 174, 18], [159, 22, 174, 20], [160, 8, 175, 6, "console"], [160, 15, 175, 13], [160, 16, 175, 14, "error"], [160, 21, 175, 19], [160, 22, 175, 20], [160, 53, 175, 51], [160, 55, 175, 53, "error"], [160, 60, 175, 58], [160, 61, 175, 59], [161, 6, 176, 4], [162, 4, 177, 2], [162, 5, 177, 3], [163, 4, 179, 2], [163, 10, 179, 8, "handleDictation"], [163, 25, 179, 23], [163, 28, 179, 26], [163, 34, 179, 26, "handleDictation"], [163, 35, 179, 26], [163, 40, 179, 38], [164, 6, 180, 4], [164, 10, 180, 8], [164, 11, 180, 9, "hasPermission"], [164, 24, 180, 22], [164, 26, 180, 24], [165, 8, 181, 6, "<PERSON><PERSON>"], [165, 22, 181, 11], [165, 23, 181, 12, "alert"], [165, 28, 181, 17], [165, 29, 182, 8], [165, 50, 182, 29], [165, 52, 183, 8], [165, 102, 184, 6], [165, 103, 184, 7], [166, 8, 185, 6], [167, 6, 186, 4], [168, 6, 188, 4], [168, 10, 188, 8], [169, 8, 189, 6], [169, 12, 189, 10, "isDictating"], [169, 23, 189, 21], [169, 25, 189, 23], [170, 10, 190, 8], [170, 16, 190, 14, "recorder"], [170, 24, 190, 22], [170, 25, 190, 23, "stop"], [170, 29, 190, 27], [170, 30, 190, 28], [170, 31, 190, 29], [171, 10, 191, 8], [171, 16, 191, 14, "mockTranscript"], [171, 30, 191, 28], [171, 33, 191, 31], [171, 62, 191, 60], [171, 63, 191, 61], [171, 64, 191, 62], [172, 10, 192, 8, "setInputText"], [172, 22, 192, 20], [172, 23, 192, 21, "inputText"], [172, 32, 192, 30], [172, 35, 192, 33, "mockTranscript"], [172, 49, 192, 47], [172, 50, 192, 48], [173, 10, 193, 8, "setIsDictating"], [173, 24, 193, 22], [173, 25, 193, 23], [173, 30, 193, 28], [173, 31, 193, 29], [174, 8, 194, 6], [174, 9, 194, 7], [174, 15, 194, 13], [175, 10, 195, 8], [175, 16, 195, 14, "recorder"], [175, 24, 195, 22], [175, 25, 195, 23, "prepareToRecordAsync"], [175, 45, 195, 43], [175, 46, 195, 44], [175, 47, 195, 45], [176, 10, 196, 8, "recorder"], [176, 18, 196, 16], [176, 19, 196, 17, "record"], [176, 25, 196, 23], [176, 26, 196, 24], [176, 27, 196, 25], [177, 10, 197, 8, "setIsDictating"], [177, 24, 197, 22], [177, 25, 197, 23], [177, 29, 197, 27], [177, 30, 197, 28], [178, 10, 198, 8, "Haptics"], [178, 17, 198, 15], [178, 18, 198, 16, "impactAsync"], [178, 29, 198, 27], [178, 30, 198, 28, "Haptics"], [178, 37, 198, 35], [178, 38, 198, 36, "ImpactFeedbackStyle"], [178, 57, 198, 55], [178, 58, 198, 56, "Medium"], [178, 64, 198, 62], [178, 65, 198, 63], [179, 8, 199, 6], [180, 6, 200, 4], [180, 7, 200, 5], [180, 8, 200, 6], [180, 15, 200, 13, "error"], [180, 20, 200, 18], [180, 22, 200, 20], [181, 8, 201, 6, "console"], [181, 15, 201, 13], [181, 16, 201, 14, "error"], [181, 21, 201, 19], [181, 22, 201, 20], [181, 45, 201, 43], [181, 47, 201, 45, "error"], [181, 52, 201, 50], [181, 53, 201, 51], [182, 8, 202, 6, "<PERSON><PERSON>"], [182, 22, 202, 11], [182, 23, 202, 12, "alert"], [182, 28, 202, 17], [182, 29, 202, 18], [182, 36, 202, 25], [182, 38, 202, 27], [182, 65, 202, 54], [182, 66, 202, 55], [183, 8, 203, 6, "setIsDictating"], [183, 22, 203, 20], [183, 23, 203, 21], [183, 28, 203, 26], [183, 29, 203, 27], [184, 6, 204, 4], [185, 4, 205, 2], [185, 5, 205, 3], [186, 4, 207, 2], [186, 10, 207, 8, "handleLongPress"], [186, 25, 207, 23], [186, 28, 207, 27, "message"], [186, 35, 207, 34], [186, 39, 207, 39], [187, 6, 208, 4, "setLongPressedMessage"], [187, 27, 208, 25], [187, 28, 208, 26, "message"], [187, 35, 208, 33], [187, 36, 208, 34], [188, 6, 209, 4, "setIsContextMenuVisible"], [188, 29, 209, 27], [188, 30, 209, 28], [188, 34, 209, 32], [188, 35, 209, 33], [189, 4, 210, 2], [189, 5, 210, 3], [190, 4, 212, 2], [190, 10, 212, 8, "handleCopyMessage"], [190, 27, 212, 25], [190, 30, 212, 28, "handleCopyMessage"], [190, 31, 212, 28], [190, 36, 212, 34], [191, 6, 213, 4], [191, 10, 213, 8, "longPressedMessage"], [191, 28, 213, 26], [191, 30, 213, 28], [192, 8, 214, 6, "Clipboard"], [192, 26, 214, 15], [192, 27, 214, 16, "setString"], [192, 36, 214, 25], [192, 37, 214, 26, "longPressedMessage"], [192, 55, 214, 44], [192, 56, 214, 45, "content"], [192, 63, 214, 52], [192, 64, 214, 53], [193, 8, 215, 6, "setIsContextMenuVisible"], [193, 31, 215, 29], [193, 32, 215, 30], [193, 37, 215, 35], [193, 38, 215, 36], [194, 8, 216, 6, "setLongPressedMessage"], [194, 29, 216, 27], [194, 30, 216, 28], [194, 34, 216, 32], [194, 35, 216, 33], [195, 6, 217, 4], [196, 4, 218, 2], [196, 5, 218, 3], [197, 4, 220, 2], [197, 10, 220, 8, "handleListenToMessage"], [197, 31, 220, 29], [197, 34, 220, 32, "handleListenToMessage"], [197, 35, 220, 32], [197, 40, 220, 38], [198, 6, 221, 4], [198, 10, 221, 8, "longPressedMessage"], [198, 28, 221, 26], [198, 30, 221, 28], [199, 8, 222, 6], [200, 8, 223, 6, "<PERSON><PERSON>"], [200, 22, 223, 11], [200, 23, 223, 12, "alert"], [200, 28, 223, 17], [200, 29, 223, 18], [200, 51, 223, 40], [200, 53, 223, 42, "longPressedMessage"], [200, 71, 223, 60], [200, 72, 223, 61, "content"], [200, 79, 223, 68], [200, 80, 223, 69], [201, 8, 224, 6, "setIsContextMenuVisible"], [201, 31, 224, 29], [201, 32, 224, 30], [201, 37, 224, 35], [201, 38, 224, 36], [202, 8, 225, 6, "setLongPressedMessage"], [202, 29, 225, 27], [202, 30, 225, 28], [202, 34, 225, 32], [202, 35, 225, 33], [203, 6, 226, 4], [204, 4, 227, 2], [204, 5, 227, 3], [205, 4, 229, 2], [205, 10, 229, 8, "handleMute"], [205, 20, 229, 18], [205, 23, 229, 21], [205, 29, 229, 21, "handleMute"], [205, 30, 229, 21], [205, 35, 229, 33], [206, 6, 230, 4], [206, 10, 230, 8], [206, 11, 230, 9, "hasPermission"], [206, 24, 230, 22], [206, 26, 230, 24], [207, 6, 232, 4], [207, 10, 232, 8], [208, 8, 233, 6], [208, 12, 233, 10, "isMuted"], [208, 19, 233, 17], [208, 21, 233, 19], [209, 10, 234, 8], [210, 10, 235, 8], [210, 14, 235, 12], [210, 15, 235, 13, "recorderState"], [210, 28, 235, 26], [210, 29, 235, 27, "isRecording"], [210, 40, 235, 38], [210, 42, 235, 40], [211, 12, 236, 10], [211, 18, 236, 16, "recorder"], [211, 26, 236, 24], [211, 27, 236, 25, "prepareToRecordAsync"], [211, 47, 236, 45], [211, 48, 236, 46], [211, 49, 236, 47], [212, 12, 237, 10, "recorder"], [212, 20, 237, 18], [212, 21, 237, 19, "record"], [212, 27, 237, 25], [212, 28, 237, 26], [212, 29, 237, 27], [213, 10, 238, 8], [214, 10, 239, 8, "setIsMuted"], [214, 20, 239, 18], [214, 21, 239, 19], [214, 26, 239, 24], [214, 27, 239, 25], [215, 10, 240, 8, "Haptics"], [215, 17, 240, 15], [215, 18, 240, 16, "impactAsync"], [215, 29, 240, 27], [215, 30, 240, 28, "Haptics"], [215, 37, 240, 35], [215, 38, 240, 36, "ImpactFeedbackStyle"], [215, 57, 240, 55], [215, 58, 240, 56, "Light"], [215, 63, 240, 61], [215, 64, 240, 62], [216, 8, 241, 6], [216, 9, 241, 7], [216, 15, 241, 13], [217, 10, 242, 8], [218, 10, 243, 8], [218, 14, 243, 12, "recorderState"], [218, 27, 243, 25], [218, 28, 243, 26, "isRecording"], [218, 39, 243, 37], [218, 41, 243, 39], [219, 12, 244, 10], [219, 18, 244, 16, "recorder"], [219, 26, 244, 24], [219, 27, 244, 25, "stop"], [219, 31, 244, 29], [219, 32, 244, 30], [219, 33, 244, 31], [220, 10, 245, 8], [221, 10, 246, 8, "setIsMuted"], [221, 20, 246, 18], [221, 21, 246, 19], [221, 25, 246, 23], [221, 26, 246, 24], [222, 10, 247, 8, "Haptics"], [222, 17, 247, 15], [222, 18, 247, 16, "impactAsync"], [222, 29, 247, 27], [222, 30, 247, 28, "Haptics"], [222, 37, 247, 35], [222, 38, 247, 36, "ImpactFeedbackStyle"], [222, 57, 247, 55], [222, 58, 247, 56, "Light"], [222, 63, 247, 61], [222, 64, 247, 62], [223, 8, 248, 6], [224, 6, 249, 4], [224, 7, 249, 5], [224, 8, 249, 6], [224, 15, 249, 13, "error"], [224, 20, 249, 18], [224, 22, 249, 20], [225, 8, 250, 6, "console"], [225, 15, 250, 13], [225, 16, 250, 14, "error"], [225, 21, 250, 19], [225, 22, 250, 20], [225, 47, 250, 45], [225, 49, 250, 47, "error"], [225, 54, 250, 52], [225, 55, 250, 53], [226, 6, 251, 4], [227, 4, 252, 2], [227, 5, 252, 3], [229, 4, 255, 2], [230, 4, 256, 2], [230, 8, 256, 2, "useEffect"], [230, 24, 256, 11], [230, 26, 256, 12], [230, 32, 256, 18], [231, 6, 257, 4], [231, 10, 257, 8, "scrollViewRef"], [231, 23, 257, 21], [231, 24, 257, 22, "current"], [231, 31, 257, 29], [231, 33, 257, 31], [232, 8, 258, 6, "scrollViewRef"], [232, 21, 258, 19], [232, 22, 258, 20, "current"], [232, 29, 258, 27], [232, 30, 258, 28, "scrollToEnd"], [232, 41, 258, 39], [232, 42, 258, 40], [233, 10, 258, 42, "animated"], [233, 18, 258, 50], [233, 20, 258, 52], [234, 8, 258, 57], [234, 9, 258, 58], [234, 10, 258, 59], [235, 6, 259, 4], [236, 4, 260, 2], [236, 5, 260, 3], [236, 7, 260, 5], [236, 8, 260, 6, "messages"], [236, 16, 260, 14], [236, 17, 260, 15], [236, 18, 260, 16], [237, 4, 262, 2], [237, 8, 262, 6], [237, 9, 262, 7, "fontsLoaded"], [237, 20, 262, 18], [237, 22, 262, 20], [238, 6, 263, 4], [238, 13, 263, 11], [238, 17, 263, 15], [239, 4, 264, 2], [241, 4, 266, 2], [242, 4, 267, 2], [242, 8, 267, 6, "isFirstTime"], [242, 19, 267, 17], [242, 21, 267, 19], [243, 6, 268, 4], [243, 26, 269, 6], [243, 30, 269, 6, "_jsxDevRuntime"], [243, 44, 269, 6], [243, 45, 269, 6, "jsxDEV"], [243, 51, 269, 6], [243, 53, 269, 7, "_View"], [243, 58, 269, 7], [243, 59, 269, 7, "default"], [243, 66, 269, 11], [244, 8, 269, 12, "style"], [244, 13, 269, 17], [244, 15, 269, 19], [245, 10, 269, 21, "flex"], [245, 14, 269, 25], [245, 16, 269, 27], [245, 17, 269, 28], [246, 10, 269, 30, "backgroundColor"], [246, 25, 269, 45], [246, 27, 269, 47, "colors"], [246, 33, 269, 53], [246, 34, 269, 54, "background"], [247, 8, 269, 65], [247, 9, 269, 67], [248, 8, 269, 67, "children"], [248, 16, 269, 67], [248, 31, 270, 8], [248, 35, 270, 8, "_jsxDevRuntime"], [248, 49, 270, 8], [248, 50, 270, 8, "jsxDEV"], [248, 56, 270, 8], [248, 58, 270, 9, "_View"], [248, 63, 270, 9], [248, 64, 270, 9, "default"], [248, 71, 270, 13], [249, 10, 271, 10, "style"], [249, 15, 271, 15], [249, 17, 271, 17], [250, 12, 272, 12, "flex"], [250, 16, 272, 16], [250, 18, 272, 18], [250, 19, 272, 19], [251, 12, 273, 12, "paddingTop"], [251, 22, 273, 22], [251, 24, 273, 24, "insets"], [251, 30, 273, 30], [251, 31, 273, 31, "top"], [251, 34, 273, 34], [251, 37, 273, 37], [251, 39, 273, 39], [252, 12, 274, 12, "paddingHorizontal"], [252, 29, 274, 29], [252, 31, 274, 31], [252, 33, 274, 33], [253, 12, 275, 12, "paddingBottom"], [253, 25, 275, 25], [253, 27, 275, 27, "insets"], [253, 33, 275, 33], [253, 34, 275, 34, "bottom"], [253, 40, 275, 40], [253, 43, 275, 43], [253, 45, 275, 45], [254, 12, 276, 12, "alignItems"], [254, 22, 276, 22], [254, 24, 276, 24], [254, 32, 276, 32], [255, 12, 277, 12, "justifyContent"], [255, 26, 277, 26], [255, 28, 277, 28], [256, 10, 278, 10], [256, 11, 278, 12], [257, 10, 278, 12, "children"], [257, 18, 278, 12], [257, 34, 280, 10], [257, 38, 280, 10, "_jsxDevRuntime"], [257, 52, 280, 10], [257, 53, 280, 10, "jsxDEV"], [257, 59, 280, 10], [257, 61, 280, 11, "_View"], [257, 66, 280, 11], [257, 67, 280, 11, "default"], [257, 74, 280, 15], [258, 12, 281, 12, "style"], [258, 17, 281, 17], [258, 19, 281, 19], [259, 14, 282, 14, "width"], [259, 19, 282, 19], [259, 21, 282, 21], [259, 24, 282, 24], [260, 14, 283, 14, "height"], [260, 20, 283, 20], [260, 22, 283, 22], [260, 25, 283, 25], [261, 14, 284, 14, "borderRadius"], [261, 26, 284, 26], [261, 28, 284, 28], [261, 30, 284, 30], [262, 14, 285, 14, "backgroundColor"], [262, 29, 285, 29], [262, 31, 285, 31, "colors"], [262, 37, 285, 37], [262, 38, 285, 38, "primaryUltraLight"], [262, 55, 285, 55], [263, 14, 286, 14, "alignItems"], [263, 24, 286, 24], [263, 26, 286, 26], [263, 34, 286, 34], [264, 14, 287, 14, "justifyContent"], [264, 28, 287, 28], [264, 30, 287, 30], [264, 38, 287, 38], [265, 14, 288, 14, "marginBottom"], [265, 26, 288, 26], [265, 28, 288, 28], [266, 12, 289, 12], [266, 13, 289, 14], [267, 12, 289, 14, "children"], [267, 20, 289, 14], [267, 35, 290, 12], [267, 39, 290, 12, "_jsxDevRuntime"], [267, 53, 290, 12], [267, 54, 290, 12, "jsxDEV"], [267, 60, 290, 12], [267, 62, 290, 13, "_lucideReactNative"], [267, 80, 290, 13], [267, 81, 290, 13, "MessageSquare"], [267, 94, 290, 26], [268, 14, 290, 27, "size"], [268, 18, 290, 31], [268, 20, 290, 33], [268, 22, 290, 36], [269, 14, 290, 37, "color"], [269, 19, 290, 42], [269, 21, 290, 44, "colors"], [269, 27, 290, 50], [269, 28, 290, 51, "primary"], [270, 12, 290, 59], [271, 14, 290, 59, "fileName"], [271, 22, 290, 59], [271, 24, 290, 59, "_jsxFileName"], [271, 36, 290, 59], [272, 14, 290, 59, "lineNumber"], [272, 24, 290, 59], [273, 14, 290, 59, "columnNumber"], [273, 26, 290, 59], [274, 12, 290, 59], [274, 19, 290, 61], [275, 10, 290, 62], [276, 12, 290, 62, "fileName"], [276, 20, 290, 62], [276, 22, 290, 62, "_jsxFileName"], [276, 34, 290, 62], [277, 12, 290, 62, "lineNumber"], [277, 22, 290, 62], [278, 12, 290, 62, "columnNumber"], [278, 24, 290, 62], [279, 10, 290, 62], [279, 17, 291, 16], [279, 18, 291, 17], [279, 33, 294, 10], [279, 37, 294, 10, "_jsxDevRuntime"], [279, 51, 294, 10], [279, 52, 294, 10, "jsxDEV"], [279, 58, 294, 10], [279, 60, 294, 11, "_Text"], [279, 65, 294, 11], [279, 66, 294, 11, "default"], [279, 73, 294, 15], [280, 12, 295, 12, "style"], [280, 17, 295, 17], [280, 19, 295, 19], [281, 14, 296, 14, "fontSize"], [281, 22, 296, 22], [281, 24, 296, 24], [281, 26, 296, 26], [282, 14, 297, 14, "fontFamily"], [282, 24, 297, 24], [282, 26, 297, 26], [282, 47, 297, 47], [283, 14, 298, 14, "color"], [283, 19, 298, 19], [283, 21, 298, 21, "colors"], [283, 27, 298, 27], [283, 28, 298, 28, "text"], [283, 32, 298, 32], [284, 14, 299, 14, "textAlign"], [284, 23, 299, 23], [284, 25, 299, 25], [284, 33, 299, 33], [285, 14, 300, 14, "marginBottom"], [285, 26, 300, 26], [285, 28, 300, 28], [286, 12, 301, 12], [286, 13, 301, 14], [287, 12, 301, 14, "children"], [287, 20, 301, 14], [287, 22, 301, 15], [288, 10, 303, 10], [289, 12, 303, 10, "fileName"], [289, 20, 303, 10], [289, 22, 303, 10, "_jsxFileName"], [289, 34, 303, 10], [290, 12, 303, 10, "lineNumber"], [290, 22, 303, 10], [291, 12, 303, 10, "columnNumber"], [291, 24, 303, 10], [292, 10, 303, 10], [292, 17, 303, 16], [292, 18, 303, 17], [292, 33, 306, 10], [292, 37, 306, 10, "_jsxDevRuntime"], [292, 51, 306, 10], [292, 52, 306, 10, "jsxDEV"], [292, 58, 306, 10], [292, 60, 306, 11, "_Text"], [292, 65, 306, 11], [292, 66, 306, 11, "default"], [292, 73, 306, 15], [293, 12, 307, 12, "style"], [293, 17, 307, 17], [293, 19, 307, 19], [294, 14, 308, 14, "fontSize"], [294, 22, 308, 22], [294, 24, 308, 24], [294, 26, 308, 26], [295, 14, 309, 14, "fontFamily"], [295, 24, 309, 24], [295, 26, 309, 26], [295, 46, 309, 46], [296, 14, 310, 14, "color"], [296, 19, 310, 19], [296, 21, 310, 21, "colors"], [296, 27, 310, 27], [296, 28, 310, 28, "textSecondary"], [296, 41, 310, 41], [297, 14, 311, 14, "textAlign"], [297, 23, 311, 23], [297, 25, 311, 25], [297, 33, 311, 33], [298, 14, 312, 14, "lineHeight"], [298, 24, 312, 24], [298, 26, 312, 26], [298, 28, 312, 28], [299, 14, 313, 14, "marginBottom"], [299, 26, 313, 26], [299, 28, 313, 28], [300, 12, 314, 12], [300, 13, 314, 14], [301, 12, 314, 14, "children"], [301, 20, 314, 14], [301, 22, 314, 15], [302, 10, 317, 10], [303, 12, 317, 10, "fileName"], [303, 20, 317, 10], [303, 22, 317, 10, "_jsxFileName"], [303, 34, 317, 10], [304, 12, 317, 10, "lineNumber"], [304, 22, 317, 10], [305, 12, 317, 10, "columnNumber"], [305, 24, 317, 10], [306, 10, 317, 10], [306, 17, 317, 16], [306, 18, 317, 17], [306, 33, 320, 10], [306, 37, 320, 10, "_jsxDevRuntime"], [306, 51, 320, 10], [306, 52, 320, 10, "jsxDEV"], [306, 58, 320, 10], [306, 60, 320, 11, "_TouchableOpacity"], [306, 77, 320, 11], [306, 78, 320, 11, "default"], [306, 85, 320, 27], [307, 12, 321, 12, "style"], [307, 17, 321, 17], [307, 19, 321, 19], [308, 14, 322, 14, "backgroundColor"], [308, 29, 322, 29], [308, 31, 322, 31, "colors"], [308, 37, 322, 37], [308, 38, 322, 38, "primary"], [308, 45, 322, 45], [309, 14, 323, 14, "borderRadius"], [309, 26, 323, 26], [309, 28, 323, 28], [309, 30, 323, 30], [310, 14, 324, 14, "paddingHorizontal"], [310, 31, 324, 31], [310, 33, 324, 33], [310, 35, 324, 35], [311, 14, 325, 14, "paddingVertical"], [311, 29, 325, 29], [311, 31, 325, 31], [311, 33, 325, 33], [312, 14, 326, 14, "min<PERSON><PERSON><PERSON>"], [312, 22, 326, 22], [312, 24, 326, 24], [312, 27, 326, 27], [313, 14, 327, 14, "alignItems"], [313, 24, 327, 24], [313, 26, 327, 26], [314, 12, 328, 12], [314, 13, 328, 14], [315, 12, 329, 12, "onPress"], [315, 19, 329, 19], [315, 21, 329, 21, "handleStartBrainstorming"], [315, 45, 329, 46], [316, 12, 329, 46, "children"], [316, 20, 329, 46], [316, 35, 330, 12], [316, 39, 330, 12, "_jsxDevRuntime"], [316, 53, 330, 12], [316, 54, 330, 12, "jsxDEV"], [316, 60, 330, 12], [316, 62, 330, 13, "_Text"], [316, 67, 330, 13], [316, 68, 330, 13, "default"], [316, 75, 330, 17], [317, 14, 331, 14, "style"], [317, 19, 331, 19], [317, 21, 331, 21], [318, 16, 332, 16, "fontSize"], [318, 24, 332, 24], [318, 26, 332, 26], [318, 28, 332, 28], [319, 16, 333, 16, "fontFamily"], [319, 26, 333, 26], [319, 28, 333, 28], [319, 49, 333, 49], [320, 16, 334, 16, "color"], [320, 21, 334, 21], [320, 23, 334, 23, "colors"], [320, 29, 334, 29], [320, 30, 334, 30, "background"], [321, 14, 335, 14], [321, 15, 335, 16], [322, 14, 335, 16, "children"], [322, 22, 335, 16], [322, 24, 335, 17], [323, 12, 337, 12], [324, 14, 337, 12, "fileName"], [324, 22, 337, 12], [324, 24, 337, 12, "_jsxFileName"], [324, 36, 337, 12], [325, 14, 337, 12, "lineNumber"], [325, 24, 337, 12], [326, 14, 337, 12, "columnNumber"], [326, 26, 337, 12], [327, 12, 337, 12], [327, 19, 337, 18], [328, 10, 337, 19], [329, 12, 337, 19, "fileName"], [329, 20, 337, 19], [329, 22, 337, 19, "_jsxFileName"], [329, 34, 337, 19], [330, 12, 337, 19, "lineNumber"], [330, 22, 337, 19], [331, 12, 337, 19, "columnNumber"], [331, 24, 337, 19], [332, 10, 337, 19], [332, 17, 338, 28], [332, 18, 338, 29], [333, 8, 338, 29], [334, 10, 338, 29, "fileName"], [334, 18, 338, 29], [334, 20, 338, 29, "_jsxFileName"], [334, 32, 338, 29], [335, 10, 338, 29, "lineNumber"], [335, 20, 338, 29], [336, 10, 338, 29, "columnNumber"], [336, 22, 338, 29], [337, 8, 338, 29], [337, 15, 339, 14], [338, 6, 339, 15], [339, 8, 339, 15, "fileName"], [339, 16, 339, 15], [339, 18, 339, 15, "_jsxFileName"], [339, 30, 339, 15], [340, 8, 339, 15, "lineNumber"], [340, 18, 339, 15], [341, 8, 339, 15, "columnNumber"], [341, 20, 339, 15], [342, 6, 339, 15], [342, 13, 340, 12], [342, 14, 340, 13], [343, 4, 342, 2], [345, 4, 344, 2], [346, 4, 345, 2], [346, 24, 346, 4], [346, 28, 346, 4, "_jsxDevRuntime"], [346, 42, 346, 4], [346, 43, 346, 4, "jsxDEV"], [346, 49, 346, 4], [346, 51, 346, 5, "_View"], [346, 56, 346, 5], [346, 57, 346, 5, "default"], [346, 64, 346, 9], [347, 6, 346, 10, "style"], [347, 11, 346, 15], [347, 13, 346, 17], [348, 8, 346, 19, "flex"], [348, 12, 346, 23], [348, 14, 346, 25], [348, 15, 346, 26], [349, 8, 346, 28, "backgroundColor"], [349, 23, 346, 43], [349, 25, 346, 45, "colors"], [349, 31, 346, 51], [349, 32, 346, 52, "background"], [350, 6, 346, 63], [350, 7, 346, 65], [351, 6, 346, 65, "children"], [351, 14, 346, 65], [351, 30, 347, 6], [351, 34, 347, 6, "_jsxDevRuntime"], [351, 48, 347, 6], [351, 49, 347, 6, "jsxDEV"], [351, 55, 347, 6], [351, 57, 347, 7, "_Header"], [351, 64, 347, 7], [351, 65, 347, 7, "default"], [351, 72, 347, 13], [352, 8, 348, 8, "voiceMode"], [352, 17, 348, 17], [352, 19, 348, 19, "voiceMode"], [352, 28, 348, 29], [353, 8, 349, 8, "onToggleVoiceMode"], [353, 25, 349, 25], [353, 27, 349, 27, "toggleVoiceMode"], [353, 42, 349, 43], [354, 8, 350, 8, "onDone"], [354, 14, 350, 14], [354, 16, 350, 16, "onDone"], [354, 17, 350, 16], [354, 22, 350, 22, "<PERSON><PERSON>"], [354, 36, 350, 27], [354, 37, 350, 28, "alert"], [354, 42, 350, 33], [354, 43, 350, 34], [354, 57, 350, 48], [355, 6, 350, 50], [356, 8, 350, 50, "fileName"], [356, 16, 350, 50], [356, 18, 350, 50, "_jsxFileName"], [356, 30, 350, 50], [357, 8, 350, 50, "lineNumber"], [357, 18, 350, 50], [358, 8, 350, 50, "columnNumber"], [358, 20, 350, 50], [359, 6, 350, 50], [359, 13, 351, 7], [359, 14, 351, 8], [359, 16, 352, 7, "voiceMode"], [359, 25, 352, 16], [359, 41, 353, 8], [359, 45, 353, 8, "_jsxDevRuntime"], [359, 59, 353, 8], [359, 60, 353, 8, "jsxDEV"], [359, 66, 353, 8], [359, 68, 353, 9, "_VoiceMode"], [359, 78, 353, 9], [359, 79, 353, 9, "default"], [359, 86, 353, 18], [360, 8, 354, 10, "isRecording"], [360, 19, 354, 21], [360, 21, 354, 23, "recorderState"], [360, 34, 354, 36], [360, 35, 354, 37, "isRecording"], [360, 46, 354, 49], [361, 8, 355, 10, "onRecord"], [361, 16, 355, 18], [361, 18, 355, 20, "handleVoiceRecord"], [361, 35, 355, 38], [362, 8, 356, 10, "hasPermission"], [362, 21, 356, 23], [362, 23, 356, 25, "hasPermission"], [362, 36, 356, 39], [363, 8, 357, 10, "isLoading"], [363, 17, 357, 19], [363, 19, 357, 21, "isLoading"], [363, 28, 357, 31], [364, 8, 358, 10, "transcript"], [364, 18, 358, 20], [364, 20, 358, 22, "transcript"], [364, 30, 358, 33], [365, 8, 359, 10, "isMuted"], [365, 15, 359, 17], [365, 17, 359, 19, "isMuted"], [365, 24, 359, 27], [366, 8, 360, 10, "onMute"], [366, 14, 360, 16], [366, 16, 360, 18, "handleMute"], [367, 6, 360, 29], [368, 8, 360, 29, "fileName"], [368, 16, 360, 29], [368, 18, 360, 29, "_jsxFileName"], [368, 30, 360, 29], [369, 8, 360, 29, "lineNumber"], [369, 18, 360, 29], [370, 8, 360, 29, "columnNumber"], [370, 20, 360, 29], [371, 6, 360, 29], [371, 13, 361, 9], [371, 14, 361, 10], [371, 30, 363, 8], [371, 34, 363, 8, "_jsxDevRuntime"], [371, 48, 363, 8], [371, 49, 363, 8, "jsxDEV"], [371, 55, 363, 8], [371, 57, 363, 9, "_KeyboardAvoidingAnimatedView"], [371, 86, 363, 9], [371, 87, 363, 9, "default"], [371, 94, 363, 37], [372, 8, 363, 38, "style"], [372, 13, 363, 43], [372, 15, 363, 45], [373, 10, 363, 47, "flex"], [373, 14, 363, 51], [373, 16, 363, 53], [374, 8, 363, 55], [374, 9, 363, 57], [375, 8, 363, 57, "children"], [375, 16, 363, 57], [375, 32, 365, 10], [375, 36, 365, 10, "_jsxDevRuntime"], [375, 50, 365, 10], [375, 51, 365, 10, "jsxDEV"], [375, 57, 365, 10], [375, 59, 365, 11, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [375, 70, 365, 11], [375, 71, 365, 11, "default"], [375, 78, 365, 21], [376, 10, 366, 12, "ref"], [376, 13, 366, 15], [376, 15, 366, 17, "scrollViewRef"], [376, 28, 366, 31], [377, 10, 367, 12, "style"], [377, 15, 367, 17], [377, 17, 367, 19], [378, 12, 367, 21, "flex"], [378, 16, 367, 25], [378, 18, 367, 27], [379, 10, 367, 29], [379, 11, 367, 31], [380, 10, 368, 12, "contentContainerStyle"], [380, 31, 368, 33], [380, 33, 368, 35], [381, 12, 369, 14, "paddingHorizontal"], [381, 29, 369, 31], [381, 31, 369, 33], [381, 33, 369, 35], [382, 12, 370, 14, "paddingVertical"], [382, 27, 370, 29], [382, 29, 370, 31], [382, 31, 370, 33], [383, 12, 371, 14, "paddingBottom"], [383, 25, 371, 27], [383, 27, 371, 29], [383, 30, 371, 32], [383, 31, 371, 34], [384, 10, 372, 12], [384, 11, 372, 14], [385, 10, 373, 12, "showsVerticalScrollIndicator"], [385, 38, 373, 40], [385, 40, 373, 42], [385, 45, 373, 48], [386, 10, 373, 48, "children"], [386, 18, 373, 48], [386, 21, 374, 13, "messages"], [386, 29, 374, 21], [386, 30, 374, 22, "map"], [386, 33, 374, 25], [386, 34, 374, 26], [386, 35, 374, 27, "message"], [386, 42, 374, 34], [386, 44, 374, 36, "index"], [386, 49, 374, 41], [386, 67, 375, 14], [386, 71, 375, 14, "_jsxDevRuntime"], [386, 85, 375, 14], [386, 86, 375, 14, "jsxDEV"], [386, 92, 375, 14], [386, 94, 375, 15, "_MessageBubble"], [386, 108, 375, 15], [386, 109, 375, 15, "default"], [386, 116, 375, 28], [387, 12, 375, 41, "message"], [387, 19, 375, 48], [387, 21, 375, 50, "message"], [387, 28, 375, 58], [388, 12, 375, 59, "onLongPress"], [388, 23, 375, 70], [388, 25, 375, 72, "handleLongPress"], [389, 10, 375, 88], [389, 13, 375, 34, "index"], [389, 18, 375, 39], [390, 12, 375, 39, "fileName"], [390, 20, 375, 39], [390, 22, 375, 39, "_jsxFileName"], [390, 34, 375, 39], [391, 12, 375, 39, "lineNumber"], [391, 22, 375, 39], [392, 12, 375, 39, "columnNumber"], [392, 24, 375, 39], [393, 10, 375, 39], [393, 17, 375, 90], [393, 18, 376, 13], [393, 19, 376, 14], [393, 21, 379, 13, "isLoading"], [393, 30, 379, 22], [393, 47, 380, 14], [393, 51, 380, 14, "_jsxDevRuntime"], [393, 65, 380, 14], [393, 66, 380, 14, "jsxDEV"], [393, 72, 380, 14], [393, 74, 380, 15, "_View"], [393, 79, 380, 15], [393, 80, 380, 15, "default"], [393, 87, 380, 19], [394, 12, 381, 16, "style"], [394, 17, 381, 21], [394, 19, 381, 23], [395, 14, 382, 18, "marginBottom"], [395, 26, 382, 30], [395, 28, 382, 32], [395, 30, 382, 34], [396, 14, 383, 18, "alignSelf"], [396, 23, 383, 27], [396, 25, 383, 29], [396, 37, 383, 41], [397, 14, 384, 18, "max<PERSON><PERSON><PERSON>"], [397, 22, 384, 26], [397, 24, 384, 28], [398, 12, 385, 16], [398, 13, 385, 18], [399, 12, 385, 18, "children"], [399, 20, 385, 18], [399, 35, 386, 16], [399, 39, 386, 16, "_jsxDevRuntime"], [399, 53, 386, 16], [399, 54, 386, 16, "jsxDEV"], [399, 60, 386, 16], [399, 62, 386, 17, "_View"], [399, 67, 386, 17], [399, 68, 386, 17, "default"], [399, 75, 386, 21], [400, 14, 387, 18, "style"], [400, 19, 387, 23], [400, 21, 387, 25], [401, 16, 388, 20, "backgroundColor"], [401, 31, 388, 35], [401, 33, 388, 37, "colors"], [401, 39, 388, 43], [401, 40, 388, 44, "cardBackground"], [401, 54, 388, 58], [402, 16, 389, 20, "borderRadius"], [402, 28, 389, 32], [402, 30, 389, 34], [402, 32, 389, 36], [403, 16, 390, 20, "paddingHorizontal"], [403, 33, 390, 37], [403, 35, 390, 39], [403, 37, 390, 41], [404, 16, 391, 20, "paddingVertical"], [404, 31, 391, 35], [404, 33, 391, 37], [404, 35, 391, 39], [405, 16, 392, 20, "borderWidth"], [405, 27, 392, 31], [405, 29, 392, 33], [405, 30, 392, 34], [406, 16, 393, 20, "borderColor"], [406, 27, 393, 31], [406, 29, 393, 33, "colors"], [406, 35, 393, 39], [406, 36, 393, 40, "outline"], [407, 14, 394, 18], [407, 15, 394, 20], [408, 14, 394, 20, "children"], [408, 22, 394, 20], [408, 37, 395, 18], [408, 41, 395, 18, "_jsxDevRuntime"], [408, 55, 395, 18], [408, 56, 395, 18, "jsxDEV"], [408, 62, 395, 18], [408, 64, 395, 19, "_Text"], [408, 69, 395, 19], [408, 70, 395, 19, "default"], [408, 77, 395, 23], [409, 16, 396, 20, "style"], [409, 21, 396, 25], [409, 23, 396, 27], [410, 18, 397, 22, "fontSize"], [410, 26, 397, 30], [410, 28, 397, 32], [410, 30, 397, 34], [411, 18, 398, 22, "fontFamily"], [411, 28, 398, 32], [411, 30, 398, 34], [411, 50, 398, 54], [412, 18, 399, 22, "color"], [412, 23, 399, 27], [412, 25, 399, 29, "colors"], [412, 31, 399, 35], [412, 32, 399, 36, "textSecondary"], [412, 45, 399, 49], [413, 18, 400, 22, "lineHeight"], [413, 28, 400, 32], [413, 30, 400, 34], [414, 16, 401, 20], [414, 17, 401, 22], [415, 16, 401, 22, "children"], [415, 24, 401, 22], [415, 26, 401, 23], [416, 14, 403, 18], [417, 16, 403, 18, "fileName"], [417, 24, 403, 18], [417, 26, 403, 18, "_jsxFileName"], [417, 38, 403, 18], [418, 16, 403, 18, "lineNumber"], [418, 26, 403, 18], [419, 16, 403, 18, "columnNumber"], [419, 28, 403, 18], [420, 14, 403, 18], [420, 21, 403, 24], [421, 12, 403, 25], [422, 14, 403, 25, "fileName"], [422, 22, 403, 25], [422, 24, 403, 25, "_jsxFileName"], [422, 36, 403, 25], [423, 14, 403, 25, "lineNumber"], [423, 24, 403, 25], [424, 14, 403, 25, "columnNumber"], [424, 26, 403, 25], [425, 12, 403, 25], [425, 19, 404, 22], [426, 10, 404, 23], [427, 12, 404, 23, "fileName"], [427, 20, 404, 23], [427, 22, 404, 23, "_jsxFileName"], [427, 34, 404, 23], [428, 12, 404, 23, "lineNumber"], [428, 22, 404, 23], [429, 12, 404, 23, "columnNumber"], [429, 24, 404, 23], [430, 10, 404, 23], [430, 17, 405, 20], [430, 18, 406, 13], [431, 8, 406, 13], [432, 10, 406, 13, "fileName"], [432, 18, 406, 13], [432, 20, 406, 13, "_jsxFileName"], [432, 32, 406, 13], [433, 10, 406, 13, "lineNumber"], [433, 20, 406, 13], [434, 10, 406, 13, "columnNumber"], [434, 22, 406, 13], [435, 8, 406, 13], [435, 15, 407, 22], [435, 16, 407, 23], [435, 18, 410, 11, "quickActions"], [435, 30, 410, 23], [435, 31, 410, 24, "length"], [435, 37, 410, 30], [435, 40, 410, 33], [435, 41, 410, 34], [435, 58, 411, 12], [435, 62, 411, 12, "_jsxDevRuntime"], [435, 76, 411, 12], [435, 77, 411, 12, "jsxDEV"], [435, 83, 411, 12], [435, 85, 411, 13, "_View"], [435, 90, 411, 13], [435, 91, 411, 13, "default"], [435, 98, 411, 17], [436, 10, 412, 14, "style"], [436, 15, 412, 19], [436, 17, 412, 21], [437, 12, 413, 16, "position"], [437, 20, 413, 24], [437, 22, 413, 26], [437, 32, 413, 36], [438, 12, 414, 16, "bottom"], [438, 18, 414, 22], [438, 20, 414, 24, "insets"], [438, 26, 414, 30], [438, 27, 414, 31, "bottom"], [438, 33, 414, 37], [438, 36, 414, 40], [438, 39, 414, 43], [439, 12, 415, 16, "left"], [439, 16, 415, 20], [439, 18, 415, 22], [439, 20, 415, 24], [440, 12, 416, 16, "right"], [440, 17, 416, 21], [440, 19, 416, 23], [441, 10, 417, 14], [441, 11, 417, 16], [442, 10, 417, 16, "children"], [442, 18, 417, 16], [442, 33, 418, 14], [442, 37, 418, 14, "_jsxDevRuntime"], [442, 51, 418, 14], [442, 52, 418, 14, "jsxDEV"], [442, 58, 418, 14], [442, 60, 418, 15, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [442, 71, 418, 15], [442, 72, 418, 15, "default"], [442, 79, 418, 25], [443, 12, 419, 16, "horizontal"], [443, 22, 419, 26], [444, 12, 420, 16, "showsHorizontalScrollIndicator"], [444, 42, 420, 46], [444, 44, 420, 48], [444, 49, 420, 54], [445, 12, 421, 16, "contentContainerStyle"], [445, 33, 421, 37], [445, 35, 421, 39], [446, 14, 421, 41, "gap"], [446, 17, 421, 44], [446, 19, 421, 46], [447, 12, 421, 48], [447, 13, 421, 50], [448, 12, 421, 50, "children"], [448, 20, 421, 50], [448, 22, 422, 17, "quickActions"], [448, 34, 422, 29], [448, 35, 422, 30, "map"], [448, 38, 422, 33], [448, 39, 422, 34], [448, 40, 422, 35, "action"], [448, 46, 422, 41], [448, 48, 422, 43, "index"], [448, 53, 422, 48], [448, 71, 423, 18], [448, 75, 423, 18, "_jsxDevRuntime"], [448, 89, 423, 18], [448, 90, 423, 18, "jsxDEV"], [448, 96, 423, 18], [448, 98, 423, 19, "_TouchableOpacity"], [448, 115, 423, 19], [448, 116, 423, 19, "default"], [448, 123, 423, 35], [449, 14, 425, 20, "style"], [449, 19, 425, 25], [449, 21, 425, 27], [450, 16, 426, 22, "backgroundColor"], [450, 31, 426, 37], [450, 33, 426, 39, "colors"], [450, 39, 426, 45], [450, 40, 426, 46, "primaryUltraLight"], [450, 57, 426, 63], [451, 16, 427, 22, "borderRadius"], [451, 28, 427, 34], [451, 30, 427, 36], [451, 32, 427, 38], [452, 16, 428, 22, "paddingHorizontal"], [452, 33, 428, 39], [452, 35, 428, 41], [452, 37, 428, 43], [453, 16, 429, 22, "paddingVertical"], [453, 31, 429, 37], [453, 33, 429, 39], [453, 34, 429, 40], [454, 16, 430, 22, "borderWidth"], [454, 27, 430, 33], [454, 29, 430, 35], [454, 30, 430, 36], [455, 16, 431, 22, "borderColor"], [455, 27, 431, 33], [455, 29, 431, 35, "colors"], [455, 35, 431, 41], [455, 36, 431, 42, "primary"], [456, 14, 432, 20], [456, 15, 432, 22], [457, 14, 433, 20, "onPress"], [457, 21, 433, 27], [457, 23, 433, 29, "onPress"], [457, 24, 433, 29], [457, 29, 433, 35, "handleQuickAction"], [457, 46, 433, 52], [457, 47, 433, 53, "action"], [457, 53, 433, 59], [457, 54, 433, 61], [458, 14, 433, 61, "children"], [458, 22, 433, 61], [458, 37, 434, 20], [458, 41, 434, 20, "_jsxDevRuntime"], [458, 55, 434, 20], [458, 56, 434, 20, "jsxDEV"], [458, 62, 434, 20], [458, 64, 434, 21, "_Text"], [458, 69, 434, 21], [458, 70, 434, 21, "default"], [458, 77, 434, 25], [459, 16, 435, 22, "style"], [459, 21, 435, 27], [459, 23, 435, 29], [460, 18, 436, 24, "fontSize"], [460, 26, 436, 32], [460, 28, 436, 34], [460, 30, 436, 36], [461, 18, 437, 24, "fontFamily"], [461, 28, 437, 34], [461, 30, 437, 36], [461, 49, 437, 55], [462, 18, 438, 24, "color"], [462, 23, 438, 29], [462, 25, 438, 31, "colors"], [462, 31, 438, 37], [462, 32, 438, 38, "primary"], [463, 16, 439, 22], [463, 17, 439, 24], [464, 16, 439, 24, "children"], [464, 24, 439, 24], [464, 26, 440, 23, "action"], [465, 14, 440, 29], [466, 16, 440, 29, "fileName"], [466, 24, 440, 29], [466, 26, 440, 29, "_jsxFileName"], [466, 38, 440, 29], [467, 16, 440, 29, "lineNumber"], [467, 26, 440, 29], [468, 16, 440, 29, "columnNumber"], [468, 28, 440, 29], [469, 14, 440, 29], [469, 21, 441, 26], [470, 12, 441, 27], [470, 15, 424, 25, "index"], [470, 20, 424, 30], [471, 14, 424, 30, "fileName"], [471, 22, 424, 30], [471, 24, 424, 30, "_jsxFileName"], [471, 36, 424, 30], [472, 14, 424, 30, "lineNumber"], [472, 24, 424, 30], [473, 14, 424, 30, "columnNumber"], [473, 26, 424, 30], [474, 12, 424, 30], [474, 19, 442, 36], [474, 20, 443, 17], [475, 10, 443, 18], [476, 12, 443, 18, "fileName"], [476, 20, 443, 18], [476, 22, 443, 18, "_jsxFileName"], [476, 34, 443, 18], [477, 12, 443, 18, "lineNumber"], [477, 22, 443, 18], [478, 12, 443, 18, "columnNumber"], [478, 24, 443, 18], [479, 10, 443, 18], [479, 17, 444, 26], [480, 8, 444, 27], [481, 10, 444, 27, "fileName"], [481, 18, 444, 27], [481, 20, 444, 27, "_jsxFileName"], [481, 32, 444, 27], [482, 10, 444, 27, "lineNumber"], [482, 20, 444, 27], [483, 10, 444, 27, "columnNumber"], [483, 22, 444, 27], [484, 8, 444, 27], [484, 15, 445, 18], [484, 16, 446, 11], [484, 31, 449, 10], [484, 35, 449, 10, "_jsxDevRuntime"], [484, 49, 449, 10], [484, 50, 449, 10, "jsxDEV"], [484, 56, 449, 10], [484, 58, 449, 11, "_View"], [484, 63, 449, 11], [484, 64, 449, 11, "default"], [484, 71, 449, 15], [485, 10, 450, 12, "style"], [485, 15, 450, 17], [485, 17, 450, 19], [486, 12, 451, 14, "position"], [486, 20, 451, 22], [486, 22, 451, 24], [486, 32, 451, 34], [487, 12, 452, 14, "bottom"], [487, 18, 452, 20], [487, 20, 452, 22], [487, 21, 452, 23], [488, 12, 453, 14, "left"], [488, 16, 453, 18], [488, 18, 453, 20], [488, 19, 453, 21], [489, 12, 454, 14, "right"], [489, 17, 454, 19], [489, 19, 454, 21], [490, 10, 455, 12], [490, 11, 455, 14], [491, 10, 455, 14, "children"], [491, 18, 455, 14], [491, 33, 456, 12], [491, 37, 456, 12, "_jsxDevRuntime"], [491, 51, 456, 12], [491, 52, 456, 12, "jsxDEV"], [491, 58, 456, 12], [491, 60, 456, 13, "_TextMode"], [491, 69, 456, 13], [491, 70, 456, 13, "default"], [491, 77, 456, 21], [492, 12, 457, 14, "inputText"], [492, 21, 457, 23], [492, 23, 457, 25, "inputText"], [492, 32, 457, 35], [493, 12, 458, 14, "onInputChange"], [493, 25, 458, 27], [493, 27, 458, 29, "setInputText"], [493, 39, 458, 42], [494, 12, 459, 14, "onSendMessage"], [494, 25, 459, 27], [494, 27, 459, 29, "onSendMessage"], [494, 28, 459, 29], [494, 33, 459, 35, "handleSendMessage"], [494, 50, 459, 52], [494, 51, 459, 53], [494, 52, 459, 55], [495, 12, 460, 14, "onStartDictation"], [495, 28, 460, 30], [495, 30, 460, 32, "handleDictation"], [496, 10, 460, 48], [497, 12, 460, 48, "fileName"], [497, 20, 460, 48], [497, 22, 460, 48, "_jsxFileName"], [497, 34, 460, 48], [498, 12, 460, 48, "lineNumber"], [498, 22, 460, 48], [499, 12, 460, 48, "columnNumber"], [499, 24, 460, 48], [500, 10, 460, 48], [500, 17, 461, 13], [501, 8, 461, 14], [502, 10, 461, 14, "fileName"], [502, 18, 461, 14], [502, 20, 461, 14, "_jsxFileName"], [502, 32, 461, 14], [503, 10, 461, 14, "lineNumber"], [503, 20, 461, 14], [504, 10, 461, 14, "columnNumber"], [504, 22, 461, 14], [505, 8, 461, 14], [505, 15, 462, 16], [505, 16, 462, 17], [506, 6, 462, 17], [507, 8, 462, 17, "fileName"], [507, 16, 462, 17], [507, 18, 462, 17, "_jsxFileName"], [507, 30, 462, 17], [508, 8, 462, 17, "lineNumber"], [508, 18, 462, 17], [509, 8, 462, 17, "columnNumber"], [509, 20, 462, 17], [510, 6, 462, 17], [510, 13, 463, 38], [510, 14, 464, 7], [510, 29, 467, 6], [510, 33, 467, 6, "_jsxDevRuntime"], [510, 47, 467, 6], [510, 48, 467, 6, "jsxDEV"], [510, 54, 467, 6], [510, 56, 467, 7, "_Modal"], [510, 62, 467, 7], [510, 63, 467, 7, "default"], [510, 70, 467, 12], [511, 8, 468, 8, "transparent"], [511, 19, 468, 19], [512, 8, 469, 8, "visible"], [512, 15, 469, 15], [512, 17, 469, 17, "isContextMenuVisible"], [512, 37, 469, 38], [513, 8, 470, 8, "onRequestClose"], [513, 22, 470, 22], [513, 24, 470, 24, "onRequestClose"], [513, 25, 470, 24], [513, 30, 470, 30, "setIsContextMenuVisible"], [513, 53, 470, 53], [513, 54, 470, 54], [513, 59, 470, 59], [513, 60, 470, 61], [514, 8, 470, 61, "children"], [514, 16, 470, 61], [514, 31, 472, 8], [514, 35, 472, 8, "_jsxDevRuntime"], [514, 49, 472, 8], [514, 50, 472, 8, "jsxDEV"], [514, 56, 472, 8], [514, 58, 472, 9, "_TouchableOpacity"], [514, 75, 472, 9], [514, 76, 472, 9, "default"], [514, 83, 472, 25], [515, 10, 473, 10, "style"], [515, 15, 473, 15], [515, 17, 473, 17], [516, 12, 473, 19, "flex"], [516, 16, 473, 23], [516, 18, 473, 25], [516, 19, 473, 26], [517, 12, 473, 28, "backgroundColor"], [517, 27, 473, 43], [517, 29, 473, 45], [517, 46, 473, 62], [518, 12, 473, 64, "justifyContent"], [518, 26, 473, 78], [518, 28, 473, 80], [518, 36, 473, 88], [519, 12, 473, 90, "alignItems"], [519, 22, 473, 100], [519, 24, 473, 102], [520, 10, 473, 111], [520, 11, 473, 113], [521, 10, 474, 10, "activeOpacity"], [521, 23, 474, 23], [521, 25, 474, 25], [521, 26, 474, 27], [522, 10, 475, 10, "onPressOut"], [522, 20, 475, 20], [522, 22, 475, 22, "onPressOut"], [522, 23, 475, 22], [522, 28, 475, 28, "setIsContextMenuVisible"], [522, 51, 475, 51], [522, 52, 475, 52], [522, 57, 475, 57], [522, 58, 475, 59], [523, 10, 475, 59, "children"], [523, 18, 475, 59], [523, 33, 477, 10], [523, 37, 477, 10, "_jsxDevRuntime"], [523, 51, 477, 10], [523, 52, 477, 10, "jsxDEV"], [523, 58, 477, 10], [523, 60, 477, 11, "_View"], [523, 65, 477, 11], [523, 66, 477, 11, "default"], [523, 73, 477, 15], [524, 12, 477, 16, "style"], [524, 17, 477, 21], [524, 19, 477, 23], [525, 14, 477, 25, "backgroundColor"], [525, 29, 477, 40], [525, 31, 477, 42, "colors"], [525, 37, 477, 48], [525, 38, 477, 49, "background"], [525, 48, 477, 59], [526, 14, 477, 61, "borderRadius"], [526, 26, 477, 73], [526, 28, 477, 75], [526, 30, 477, 77], [527, 14, 477, 79, "padding"], [527, 21, 477, 86], [527, 23, 477, 88], [527, 25, 477, 90], [528, 14, 477, 92, "width"], [528, 19, 477, 97], [528, 21, 477, 99], [529, 12, 477, 105], [529, 13, 477, 107], [530, 12, 477, 107, "children"], [530, 20, 477, 107], [530, 36, 478, 12], [530, 40, 478, 12, "_jsxDevRuntime"], [530, 54, 478, 12], [530, 55, 478, 12, "jsxDEV"], [530, 61, 478, 12], [530, 63, 478, 13, "_TouchableOpacity"], [530, 80, 478, 13], [530, 81, 478, 13, "default"], [530, 88, 478, 29], [531, 14, 478, 30, "onPress"], [531, 21, 478, 37], [531, 23, 478, 39, "handleCopyMessage"], [531, 40, 478, 57], [532, 14, 478, 58, "style"], [532, 19, 478, 63], [532, 21, 478, 65], [533, 16, 478, 67, "paddingVertical"], [533, 31, 478, 82], [533, 33, 478, 84], [534, 14, 478, 87], [534, 15, 478, 89], [535, 14, 478, 89, "children"], [535, 22, 478, 89], [535, 37, 479, 14], [535, 41, 479, 14, "_jsxDevRuntime"], [535, 55, 479, 14], [535, 56, 479, 14, "jsxDEV"], [535, 62, 479, 14], [535, 64, 479, 15, "_Text"], [535, 69, 479, 15], [535, 70, 479, 15, "default"], [535, 77, 479, 19], [536, 16, 479, 20, "style"], [536, 21, 479, 25], [536, 23, 479, 27], [537, 18, 479, 29, "fontSize"], [537, 26, 479, 37], [537, 28, 479, 39], [537, 30, 479, 41], [538, 18, 479, 43, "fontFamily"], [538, 28, 479, 53], [538, 30, 479, 55], [538, 49, 479, 74], [539, 18, 479, 76, "color"], [539, 23, 479, 81], [539, 25, 479, 83, "colors"], [539, 31, 479, 89], [539, 32, 479, 90, "text"], [540, 16, 479, 95], [540, 17, 479, 97], [541, 16, 479, 97, "children"], [541, 24, 479, 97], [541, 26, 479, 98], [542, 14, 479, 110], [543, 16, 479, 110, "fileName"], [543, 24, 479, 110], [543, 26, 479, 110, "_jsxFileName"], [543, 38, 479, 110], [544, 16, 479, 110, "lineNumber"], [544, 26, 479, 110], [545, 16, 479, 110, "columnNumber"], [545, 28, 479, 110], [546, 14, 479, 110], [546, 21, 479, 116], [547, 12, 479, 117], [548, 14, 479, 117, "fileName"], [548, 22, 479, 117], [548, 24, 479, 117, "_jsxFileName"], [548, 36, 479, 117], [549, 14, 479, 117, "lineNumber"], [549, 24, 479, 117], [550, 14, 479, 117, "columnNumber"], [550, 26, 479, 117], [551, 12, 479, 117], [551, 19, 480, 30], [551, 20, 480, 31], [551, 22, 481, 13, "longPressedMessage"], [551, 40, 481, 31], [551, 42, 481, 33, "role"], [551, 46, 481, 37], [551, 51, 481, 42], [551, 62, 481, 53], [551, 79, 482, 14], [551, 83, 482, 14, "_jsxDevRuntime"], [551, 97, 482, 14], [551, 98, 482, 14, "jsxDEV"], [551, 104, 482, 14], [551, 106, 482, 15, "_TouchableOpacity"], [551, 123, 482, 15], [551, 124, 482, 15, "default"], [551, 131, 482, 31], [552, 14, 482, 32, "onPress"], [552, 21, 482, 39], [552, 23, 482, 41, "handleListenToMessage"], [552, 44, 482, 63], [553, 14, 482, 64, "style"], [553, 19, 482, 69], [553, 21, 482, 71], [554, 16, 482, 73, "paddingVertical"], [554, 31, 482, 88], [554, 33, 482, 90], [555, 14, 482, 93], [555, 15, 482, 95], [556, 14, 482, 95, "children"], [556, 22, 482, 95], [556, 37, 483, 16], [556, 41, 483, 16, "_jsxDevRuntime"], [556, 55, 483, 16], [556, 56, 483, 16, "jsxDEV"], [556, 62, 483, 16], [556, 64, 483, 17, "_Text"], [556, 69, 483, 17], [556, 70, 483, 17, "default"], [556, 77, 483, 21], [557, 16, 483, 22, "style"], [557, 21, 483, 27], [557, 23, 483, 29], [558, 18, 483, 31, "fontSize"], [558, 26, 483, 39], [558, 28, 483, 41], [558, 30, 483, 43], [559, 18, 483, 45, "fontFamily"], [559, 28, 483, 55], [559, 30, 483, 57], [559, 49, 483, 76], [560, 18, 483, 78, "color"], [560, 23, 483, 83], [560, 25, 483, 85, "colors"], [560, 31, 483, 91], [560, 32, 483, 92, "text"], [561, 16, 483, 97], [561, 17, 483, 99], [562, 16, 483, 99, "children"], [562, 24, 483, 99], [562, 26, 483, 100], [563, 14, 483, 117], [564, 16, 483, 117, "fileName"], [564, 24, 483, 117], [564, 26, 483, 117, "_jsxFileName"], [564, 38, 483, 117], [565, 16, 483, 117, "lineNumber"], [565, 26, 483, 117], [566, 16, 483, 117, "columnNumber"], [566, 28, 483, 117], [567, 14, 483, 117], [567, 21, 483, 123], [568, 12, 483, 124], [569, 14, 483, 124, "fileName"], [569, 22, 483, 124], [569, 24, 483, 124, "_jsxFileName"], [569, 36, 483, 124], [570, 14, 483, 124, "lineNumber"], [570, 24, 483, 124], [571, 14, 483, 124, "columnNumber"], [571, 26, 483, 124], [572, 12, 483, 124], [572, 19, 484, 32], [572, 20, 485, 13], [573, 10, 485, 13], [574, 12, 485, 13, "fileName"], [574, 20, 485, 13], [574, 22, 485, 13, "_jsxFileName"], [574, 34, 485, 13], [575, 12, 485, 13, "lineNumber"], [575, 22, 485, 13], [576, 12, 485, 13, "columnNumber"], [576, 24, 485, 13], [577, 10, 485, 13], [577, 17, 486, 16], [578, 8, 486, 17], [579, 10, 486, 17, "fileName"], [579, 18, 486, 17], [579, 20, 486, 17, "_jsxFileName"], [579, 32, 486, 17], [580, 10, 486, 17, "lineNumber"], [580, 20, 486, 17], [581, 10, 486, 17, "columnNumber"], [581, 22, 486, 17], [582, 8, 486, 17], [582, 15, 487, 26], [583, 6, 487, 27], [584, 8, 487, 27, "fileName"], [584, 16, 487, 27], [584, 18, 487, 27, "_jsxFileName"], [584, 30, 487, 27], [585, 8, 487, 27, "lineNumber"], [585, 18, 487, 27], [586, 8, 487, 27, "columnNumber"], [586, 20, 487, 27], [587, 6, 487, 27], [587, 13, 488, 13], [587, 14, 488, 14], [588, 4, 488, 14], [589, 6, 488, 14, "fileName"], [589, 14, 488, 14], [589, 16, 488, 14, "_jsxFileName"], [589, 28, 488, 14], [590, 6, 488, 14, "lineNumber"], [590, 16, 488, 14], [591, 6, 488, 14, "columnNumber"], [591, 18, 488, 14], [592, 4, 488, 14], [592, 11, 489, 10], [592, 12, 489, 11], [593, 2, 491, 0], [594, 2, 491, 1, "_s"], [594, 4, 491, 1], [594, 5, 34, 24, "BrainstormScreen"], [594, 21, 34, 40], [595, 4, 34, 40], [595, 12, 35, 17, "useSafeAreaInsets"], [595, 57, 35, 34], [595, 59, 36, 17, "useColors"], [595, 79, 36, 26], [595, 81, 37, 24, "useFonts"], [595, 98, 37, 32], [595, 100, 43, 19, "useAudioRecorder"], [595, 127, 43, 35], [595, 129, 44, 24, "useAudioRecorderState"], [595, 161, 44, 45], [596, 2, 44, 45], [597, 2, 44, 45, "_c"], [597, 4, 44, 45], [597, 7, 34, 24, "BrainstormScreen"], [597, 23, 34, 40], [598, 2, 34, 40], [598, 6, 34, 40, "_c"], [598, 8, 34, 40], [599, 2, 34, 40, "$RefreshReg$"], [599, 14, 34, 40], [599, 15, 34, 40, "_c"], [599, 17, 34, 40], [600, 0, 34, 40], [600, 3]], "functionMap": {"names": ["<global>", "BrainstormScreen", "useEffect$argument_0", "<anonymous>", "onPress", "handleStartBrainstorming", "handleSendMessage", "setMessages$argument_0", "setTimeout$argument_0", "handleQuickAction", "toggleVoiceMode", "startVoiceSession", "stopVoiceSession", "handleDictation", "handleLongPress", "handleCopyMessage", "handleListenToMessage", "handleMute", "Header.props.onDone", "messages.map$argument_0", "quickActions.map$argument_0", "TouchableOpacity.props.onPress", "TextMode.props.onSendMessage", "Modal.props.onRequestClose", "TouchableOpacity.props.onPressOut"], "mappings": "AAA;eCiC;YC4B;KCC;8CCQ,yBD;KDK;GDC;mCIE;GJK;4BKE;gBCI,mCD;eEM;kBDK,+BC;KFG;GLC;4BQE;GRG;0BSE;qBNS;aMQ;GTuB;4BUE;GVY;2BWE;GXS;0BYE;GZ0B;0BaE;GbG;4BcE;GdM;gCeE;GfO;qBgBE;GhBuB;YCI;GDI;gBiB0F,iCjB;0BkBwB;alBE;kCmB8C;6BCW,+BD;iBnBU;6BqBgB,yBrB;wBsBW,oCtB;sBuBK,oCvB"}}, "type": "js/module"}]}