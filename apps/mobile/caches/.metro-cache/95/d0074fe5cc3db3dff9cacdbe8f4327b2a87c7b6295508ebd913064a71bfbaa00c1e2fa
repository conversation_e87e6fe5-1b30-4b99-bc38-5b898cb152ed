{"dependencies": [{"name": "./JSWorklets.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 57, "index": 72}}], "key": "xyPXELNuOiyNfEyr6w/rWXUKLtg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WorkletsModule = void 0;\n  var _JSWorklets = require(_dependencyMap[0], \"./JSWorklets.js\");\n  const WorkletsModule = exports.WorkletsModule = (0, _JSWorklets.createJSWorkletsModule)();\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "WorkletsModule"], [7, 24, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_JSWorklets"], [8, 17, 3, 0], [8, 20, 3, 0, "require"], [8, 27, 3, 0], [8, 28, 3, 0, "_dependencyMap"], [8, 42, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "WorkletsModule"], [9, 22, 4, 27], [9, 25, 4, 27, "exports"], [9, 32, 4, 27], [9, 33, 4, 27, "WorkletsModule"], [9, 47, 4, 27], [9, 50, 4, 30], [9, 54, 4, 30, "createJSWorkletsModule"], [9, 88, 4, 52], [9, 90, 4, 53], [9, 91, 4, 54], [10, 0, 4, 55], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}