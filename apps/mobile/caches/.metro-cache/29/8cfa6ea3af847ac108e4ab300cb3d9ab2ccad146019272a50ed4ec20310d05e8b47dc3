{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./handlers/PanGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 20}, "end": {"line": 2, "column": 61, "index": 81}}], "key": "kgBPL7dQ8ClLuJs0S/T6s3wIxro=", "exportNames": ["*"]}}, {"name": "./handlers/TapGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 82}, "end": {"line": 3, "column": 61, "index": 143}}], "key": "/dqGVX4V7AAIHK9hdGiB9P03wSs=", "exportNames": ["*"]}}, {"name": "./handlers/LongPressGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 144}, "end": {"line": 4, "column": 73, "index": 217}}], "key": "A14QOc0jRnBuWCwBa2r1ZSV30yc=", "exportNames": ["*"]}}, {"name": "./handlers/PinchGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 218}, "end": {"line": 5, "column": 65, "index": 283}}], "key": "vrAtK53QGnCXpAK86ojl/eG6M4o=", "exportNames": ["*"]}}, {"name": "./handlers/RotationGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 284}, "end": {"line": 6, "column": 71, "index": 355}}], "key": "Brsl2+JKpFwQuCjDOdJwAYq8IUY=", "exportNames": ["*"]}}, {"name": "./handlers/FlingGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 356}, "end": {"line": 7, "column": 65, "index": 421}}], "key": "D93/+Awrxwe4eiC+/8GdoP7zvyw=", "exportNames": ["*"]}}, {"name": "./handlers/NativeViewGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 422}, "end": {"line": 8, "column": 75, "index": 497}}], "key": "QMKcmEBmNuli7j9h2CPci69yXRs=", "exportNames": ["*"]}}, {"name": "./handlers/ManualGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 498}, "end": {"line": 9, "column": 67, "index": 565}}], "key": "bJFJM9sH+ZyGDT9d9R2dhwnQIS8=", "exportNames": ["*"]}}, {"name": "./handlers/HoverGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 566}, "end": {"line": 10, "column": 65, "index": 631}}], "key": "cAUGIjp1A6EgQ5fTHiztXpVC++U=", "exportNames": ["*"]}}, {"name": "../web_hammer/NativeViewGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 652}, "end": {"line": 12, "column": 84, "index": 736}}], "key": "G1ufZEtspPLqW8Ew6xTidFL+ozQ=", "exportNames": ["*"]}}, {"name": "../web_hammer/PanGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 737}, "end": {"line": 13, "column": 70, "index": 807}}], "key": "DIaDeY/Yp1ukJrD7bltLc1o6Zd8=", "exportNames": ["*"]}}, {"name": "../web_hammer/TapGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 808}, "end": {"line": 14, "column": 70, "index": 878}}], "key": "INWfi3e1wW5i7hk8BW+Hrs2wlJ8=", "exportNames": ["*"]}}, {"name": "../web_hammer/LongPressGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 879}, "end": {"line": 15, "column": 82, "index": 961}}], "key": "qyqsw1AR+RdLlDuQDVs/XrH3Az4=", "exportNames": ["*"]}}, {"name": "../web_hammer/PinchGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 962}, "end": {"line": 16, "column": 74, "index": 1036}}], "key": "AOV1y6TwfMnnClMWy4XQDYUDQtE=", "exportNames": ["*"]}}, {"name": "../web_hammer/RotationGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 1037}, "end": {"line": 17, "column": 80, "index": 1117}}], "key": "anQCIHA5P5Wd9fxEgh2dC6UzgSA=", "exportNames": ["*"]}}, {"name": "../web_hammer/FlingGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 1118}, "end": {"line": 18, "column": 74, "index": 1192}}], "key": "SpElfB5Bq6e8Ebg5zqjcNd+EE4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HammerGestures = exports.Gestures = void 0;\n  var _PanGestureHandler = _interopRequireDefault(require(_dependencyMap[1], \"./handlers/PanGestureHandler\"));\n  var _TapGestureHandler = _interopRequireDefault(require(_dependencyMap[2], \"./handlers/TapGestureHandler\"));\n  var _LongPressGestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./handlers/LongPressGestureHandler\"));\n  var _PinchGestureHandler = _interopRequireDefault(require(_dependencyMap[4], \"./handlers/PinchGestureHandler\"));\n  var _RotationGestureHandler = _interopRequireDefault(require(_dependencyMap[5], \"./handlers/RotationGestureHandler\"));\n  var _FlingGestureHandler = _interopRequireDefault(require(_dependencyMap[6], \"./handlers/FlingGestureHandler\"));\n  var _NativeViewGestureHandler = _interopRequireDefault(require(_dependencyMap[7], \"./handlers/NativeViewGestureHandler\"));\n  var _ManualGestureHandler = _interopRequireDefault(require(_dependencyMap[8], \"./handlers/ManualGestureHandler\"));\n  var _HoverGestureHandler = _interopRequireDefault(require(_dependencyMap[9], \"./handlers/HoverGestureHandler\"));\n  var _NativeViewGestureHandler2 = _interopRequireDefault(require(_dependencyMap[10], \"../web_hammer/NativeViewGestureHandler\"));\n  var _PanGestureHandler2 = _interopRequireDefault(require(_dependencyMap[11], \"../web_hammer/PanGestureHandler\"));\n  var _TapGestureHandler2 = _interopRequireDefault(require(_dependencyMap[12], \"../web_hammer/TapGestureHandler\"));\n  var _LongPressGestureHandler2 = _interopRequireDefault(require(_dependencyMap[13], \"../web_hammer/LongPressGestureHandler\"));\n  var _PinchGestureHandler2 = _interopRequireDefault(require(_dependencyMap[14], \"../web_hammer/PinchGestureHandler\"));\n  var _RotationGestureHandler2 = _interopRequireDefault(require(_dependencyMap[15], \"../web_hammer/RotationGestureHandler\"));\n  var _FlingGestureHandler2 = _interopRequireDefault(require(_dependencyMap[16], \"../web_hammer/FlingGestureHandler\"));\n  // Gesture Handlers\n\n  // Hammer Handlers\n\n  const Gestures = exports.Gestures = {\n    NativeViewGestureHandler: _NativeViewGestureHandler.default,\n    PanGestureHandler: _PanGestureHandler.default,\n    TapGestureHandler: _TapGestureHandler.default,\n    LongPressGestureHandler: _LongPressGestureHandler.default,\n    PinchGestureHandler: _PinchGestureHandler.default,\n    RotationGestureHandler: _RotationGestureHandler.default,\n    FlingGestureHandler: _FlingGestureHandler.default,\n    ManualGestureHandler: _ManualGestureHandler.default,\n    HoverGestureHandler: _HoverGestureHandler.default\n  };\n  const HammerGestures = exports.HammerGestures = {\n    NativeViewGestureHandler: _NativeViewGestureHandler2.default,\n    PanGestureHandler: _PanGestureHandler2.default,\n    TapGestureHandler: _TapGestureHandler2.default,\n    LongPressGestureHandler: _LongPressGestureHandler2.default,\n    PinchGestureHandler: _PinchGestureHandler2.default,\n    RotationGestureHandler: _RotationGestureHandler2.default,\n    FlingGestureHandler: _FlingGestureHandler2.default\n  };\n});", "lineCount": 47, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_PanGestureHandler"], [7, 24, 2, 0], [7, 27, 2, 0, "_interopRequireDefault"], [7, 49, 2, 0], [7, 50, 2, 0, "require"], [7, 57, 2, 0], [7, 58, 2, 0, "_dependencyMap"], [7, 72, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_TapGestureHandler"], [8, 24, 3, 0], [8, 27, 3, 0, "_interopRequireDefault"], [8, 49, 3, 0], [8, 50, 3, 0, "require"], [8, 57, 3, 0], [8, 58, 3, 0, "_dependencyMap"], [8, 72, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_LongPressGestureHandler"], [9, 30, 4, 0], [9, 33, 4, 0, "_interopRequireDefault"], [9, 55, 4, 0], [9, 56, 4, 0, "require"], [9, 63, 4, 0], [9, 64, 4, 0, "_dependencyMap"], [9, 78, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_PinchGestureHandler"], [10, 26, 5, 0], [10, 29, 5, 0, "_interopRequireDefault"], [10, 51, 5, 0], [10, 52, 5, 0, "require"], [10, 59, 5, 0], [10, 60, 5, 0, "_dependencyMap"], [10, 74, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_RotationGestureHandler"], [11, 29, 6, 0], [11, 32, 6, 0, "_interopRequireDefault"], [11, 54, 6, 0], [11, 55, 6, 0, "require"], [11, 62, 6, 0], [11, 63, 6, 0, "_dependencyMap"], [11, 77, 6, 0], [12, 2, 7, 0], [12, 6, 7, 0, "_FlingGestureHandler"], [12, 26, 7, 0], [12, 29, 7, 0, "_interopRequireDefault"], [12, 51, 7, 0], [12, 52, 7, 0, "require"], [12, 59, 7, 0], [12, 60, 7, 0, "_dependencyMap"], [12, 74, 7, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_NativeViewGestureHandler"], [13, 31, 8, 0], [13, 34, 8, 0, "_interopRequireDefault"], [13, 56, 8, 0], [13, 57, 8, 0, "require"], [13, 64, 8, 0], [13, 65, 8, 0, "_dependencyMap"], [13, 79, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_ManualGestureHandler"], [14, 27, 9, 0], [14, 30, 9, 0, "_interopRequireDefault"], [14, 52, 9, 0], [14, 53, 9, 0, "require"], [14, 60, 9, 0], [14, 61, 9, 0, "_dependencyMap"], [14, 75, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_HoverGestureHandler"], [15, 26, 10, 0], [15, 29, 10, 0, "_interopRequireDefault"], [15, 51, 10, 0], [15, 52, 10, 0, "require"], [15, 59, 10, 0], [15, 60, 10, 0, "_dependencyMap"], [15, 74, 10, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_NativeViewGestureHandler2"], [16, 32, 12, 0], [16, 35, 12, 0, "_interopRequireDefault"], [16, 57, 12, 0], [16, 58, 12, 0, "require"], [16, 65, 12, 0], [16, 66, 12, 0, "_dependencyMap"], [16, 80, 12, 0], [17, 2, 13, 0], [17, 6, 13, 0, "_PanGestureHandler2"], [17, 25, 13, 0], [17, 28, 13, 0, "_interopRequireDefault"], [17, 50, 13, 0], [17, 51, 13, 0, "require"], [17, 58, 13, 0], [17, 59, 13, 0, "_dependencyMap"], [17, 73, 13, 0], [18, 2, 14, 0], [18, 6, 14, 0, "_TapGestureHandler2"], [18, 25, 14, 0], [18, 28, 14, 0, "_interopRequireDefault"], [18, 50, 14, 0], [18, 51, 14, 0, "require"], [18, 58, 14, 0], [18, 59, 14, 0, "_dependencyMap"], [18, 73, 14, 0], [19, 2, 15, 0], [19, 6, 15, 0, "_LongPressGestureHandler2"], [19, 31, 15, 0], [19, 34, 15, 0, "_interopRequireDefault"], [19, 56, 15, 0], [19, 57, 15, 0, "require"], [19, 64, 15, 0], [19, 65, 15, 0, "_dependencyMap"], [19, 79, 15, 0], [20, 2, 16, 0], [20, 6, 16, 0, "_PinchGestureHandler2"], [20, 27, 16, 0], [20, 30, 16, 0, "_interopRequireDefault"], [20, 52, 16, 0], [20, 53, 16, 0, "require"], [20, 60, 16, 0], [20, 61, 16, 0, "_dependencyMap"], [20, 75, 16, 0], [21, 2, 17, 0], [21, 6, 17, 0, "_RotationGestureHandler2"], [21, 30, 17, 0], [21, 33, 17, 0, "_interopRequireDefault"], [21, 55, 17, 0], [21, 56, 17, 0, "require"], [21, 63, 17, 0], [21, 64, 17, 0, "_dependencyMap"], [21, 78, 17, 0], [22, 2, 18, 0], [22, 6, 18, 0, "_FlingGestureHandler2"], [22, 27, 18, 0], [22, 30, 18, 0, "_interopRequireDefault"], [22, 52, 18, 0], [22, 53, 18, 0, "require"], [22, 60, 18, 0], [22, 61, 18, 0, "_dependencyMap"], [22, 75, 18, 0], [23, 2, 1, 0], [25, 2, 10, 66], [27, 2, 19, 7], [27, 8, 19, 13, "Gestures"], [27, 16, 19, 21], [27, 19, 19, 21, "exports"], [27, 26, 19, 21], [27, 27, 19, 21, "Gestures"], [27, 35, 19, 21], [27, 38, 19, 24], [28, 4, 20, 2, "NativeViewGestureHandler"], [28, 28, 20, 26], [28, 30, 20, 2, "NativeViewGestureHandler"], [28, 63, 20, 26], [29, 4, 21, 2, "PanGestureHandler"], [29, 21, 21, 19], [29, 23, 21, 2, "PanGestureHandler"], [29, 49, 21, 19], [30, 4, 22, 2, "TapGestureHandler"], [30, 21, 22, 19], [30, 23, 22, 2, "TapGestureHandler"], [30, 49, 22, 19], [31, 4, 23, 2, "LongPressGestureHandler"], [31, 27, 23, 25], [31, 29, 23, 2, "LongPressGestureHandler"], [31, 61, 23, 25], [32, 4, 24, 2, "PinchGestureHandler"], [32, 23, 24, 21], [32, 25, 24, 2, "PinchGestureHandler"], [32, 53, 24, 21], [33, 4, 25, 2, "RotationGestureHandler"], [33, 26, 25, 24], [33, 28, 25, 2, "RotationGestureHandler"], [33, 59, 25, 24], [34, 4, 26, 2, "FlingGestureHandler"], [34, 23, 26, 21], [34, 25, 26, 2, "FlingGestureHandler"], [34, 53, 26, 21], [35, 4, 27, 2, "ManualGestureHandler"], [35, 24, 27, 22], [35, 26, 27, 2, "ManualGestureHandler"], [35, 55, 27, 22], [36, 4, 28, 2, "HoverGestureHandler"], [36, 23, 28, 21], [36, 25, 28, 2, "HoverGestureHandler"], [37, 2, 29, 0], [37, 3, 29, 1], [38, 2, 30, 7], [38, 8, 30, 13, "HammerGestures"], [38, 22, 30, 27], [38, 25, 30, 27, "exports"], [38, 32, 30, 27], [38, 33, 30, 27, "HammerGestures"], [38, 47, 30, 27], [38, 50, 30, 30], [39, 4, 31, 2, "NativeViewGestureHandler"], [39, 28, 31, 26], [39, 30, 31, 28, "HammerNativeViewGestureHandler"], [39, 64, 31, 58], [40, 4, 32, 2, "PanGestureHandler"], [40, 21, 32, 19], [40, 23, 32, 21, "HammerPanGestureHandler"], [40, 50, 32, 44], [41, 4, 33, 2, "TapGestureHandler"], [41, 21, 33, 19], [41, 23, 33, 21, "HammerTapGestureHandler"], [41, 50, 33, 44], [42, 4, 34, 2, "LongPressGestureHandler"], [42, 27, 34, 25], [42, 29, 34, 27, "HammerLongPressGestureHandler"], [42, 62, 34, 56], [43, 4, 35, 2, "PinchGestureHandler"], [43, 23, 35, 21], [43, 25, 35, 23, "HammerPinchGestureHandler"], [43, 54, 35, 48], [44, 4, 36, 2, "RotationGestureHandler"], [44, 26, 36, 24], [44, 28, 36, 26, "HammerRotationGestureHandler"], [44, 60, 36, 54], [45, 4, 37, 2, "FlingGestureHandler"], [45, 23, 37, 21], [45, 25, 37, 23, "HammerFlingGestureHandler"], [46, 2, 38, 0], [46, 3, 38, 1], [47, 0, 38, 2], [47, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}