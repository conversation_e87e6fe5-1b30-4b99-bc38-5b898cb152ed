{"dependencies": [{"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 126}, "end": {"line": 4, "column": 46, "index": 172}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../updateProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 173}, "end": {"line": 5, "column": 45, "index": 218}}], "key": "9+ynmbkEG/f9MdXTtZSVB+/M8dQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.applyStyleForBelowTopScreen = exports.applyStyle = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../PlatformChecker\");\n  var _updateProps = require(_dependencyMap[1], \"../updateProps\");\n  var IS_FABRIC = (0, _PlatformChecker.isFabric)();\n  var _worklet_13427768743970_init_data = {\n    code: \"function createViewDescriptorPaper_reactNativeReanimated_styleUpdaterTs1(screenId){return{tag:screenId,name:'RCTView'};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createViewDescriptorPaper_reactNativeReanimated_styleUpdaterTs1\\\",\\\"screenId\\\",\\\"tag\\\",\\\"name\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\\\"],\\\"mappings\\\":\\\"AAYA,SAAAA,+DAAyEA,CAAAC,QAAA,EAEvE,MAAO,CAAEC,GAAG,CAAED,QAAQ,CAAEE,IAAI,CAAE,SAAU,CAAC,CAC3C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var createViewDescriptorPaper = function () {\n    var _e = [new global.Error(), 1, -27];\n    var createViewDescriptorPaper = function (screenId) {\n      return {\n        tag: screenId,\n        name: 'RCTView'\n      };\n    };\n    createViewDescriptorPaper.__closure = {};\n    createViewDescriptorPaper.__workletHash = 13427768743970;\n    createViewDescriptorPaper.__initData = _worklet_13427768743970_init_data;\n    createViewDescriptorPaper.__stackDetails = _e;\n    return createViewDescriptorPaper;\n  }();\n  var _worklet_926744547348_init_data = {\n    code: \"function createViewDescriptorFabric_reactNativeReanimated_styleUpdaterTs2(screenId){return{shadowNodeWrapper:screenId};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createViewDescriptorFabric_reactNativeReanimated_styleUpdaterTs2\\\",\\\"screenId\\\",\\\"shadowNodeWrapper\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\\\"],\\\"mappings\\\":\\\"AAgBA,SAAAA,gEAA0EA,CAAAC,QAAA,EAExE,MAAO,CAAEC,iBAAiB,CAAED,QAAS,CAAC,CACxC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var createViewDescriptorFabric = function () {\n    var _e = [new global.Error(), 1, -27];\n    var createViewDescriptorFabric = function (screenId) {\n      return {\n        shadowNodeWrapper: screenId\n      };\n    };\n    createViewDescriptorFabric.__closure = {};\n    createViewDescriptorFabric.__workletHash = 926744547348;\n    createViewDescriptorFabric.__initData = _worklet_926744547348_init_data;\n    createViewDescriptorFabric.__stackDetails = _e;\n    return createViewDescriptorFabric;\n  }();\n  var createViewDescriptor = IS_FABRIC ? createViewDescriptorFabric : createViewDescriptorPaper;\n  var _worklet_10105259623138_init_data = {\n    code: \"function applyStyleForTopScreen_reactNativeReanimated_styleUpdaterTs3(screenTransitionConfig,event){const{createViewDescriptor,updateProps}=this.__closure;const{screenDimensions:screenDimensions,topScreenId:topScreenId,screenTransition:screenTransition}=screenTransitionConfig;const{topScreenStyle:computeTopScreenStyle}=screenTransition;const topScreenStyle=computeTopScreenStyle(event,screenDimensions);const topScreenDescriptor={value:[createViewDescriptor(topScreenId)]};updateProps(topScreenDescriptor,topScreenStyle,undefined);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyStyleForTopScreen_reactNativeReanimated_styleUpdaterTs3\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"createViewDescriptor\\\",\\\"updateProps\\\",\\\"__closure\\\",\\\"screenDimensions\\\",\\\"topScreenId\\\",\\\"screenTransition\\\",\\\"topScreenStyle\\\",\\\"computeTopScreenStyle\\\",\\\"topScreenDescriptor\\\",\\\"value\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\\\"],\\\"mappings\\\":\\\"AAwBA,SAAAA,4DAGEA,CAAAC,sBAAA,CAAAC,KAAA,QAAAC,oBAAA,CAAAC,WAAA,OAAAC,SAAA,CAEA,KAAM,CAAEC,gBAAgB,CAAhBA,gBAAgB,CAAEC,WAAW,CAAXA,WAAW,CAAEC,gBAAA,CAAAA,gBAAiB,CAAC,CACvDP,sBAAsB,CACxB,KAAM,CAAEQ,cAAc,CAAEC,qBAAsB,CAAC,CAAGF,gBAAgB,CAClE,KAAM,CAAAC,cAAc,CAAGC,qBAAqB,CAACR,KAAK,CAAEI,gBAAgB,CAAC,CACrE,KAAM,CAAAK,mBAAmB,CAAG,CAC1BC,KAAK,CAAE,CAACT,oBAAoB,CAACI,WAAW,CAAC,CAC3C,CAAC,CACDH,WAAW,CAACO,mBAAmB,CAAEF,cAAc,CAAEI,SAAS,CAAC,CAC7D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var applyStyleForTopScreen = function () {\n    var _e = [new global.Error(), -3, -27];\n    var applyStyleForTopScreen = function (screenTransitionConfig, event) {\n      var screenDimensions = screenTransitionConfig.screenDimensions,\n        topScreenId = screenTransitionConfig.topScreenId,\n        screenTransition = screenTransitionConfig.screenTransition;\n      var computeTopScreenStyle = screenTransition.topScreenStyle;\n      var topScreenStyle = computeTopScreenStyle(event, screenDimensions);\n      var topScreenDescriptor = {\n        value: [createViewDescriptor(topScreenId)]\n      };\n      (0, _updateProps.updateProps)(topScreenDescriptor, topScreenStyle, undefined);\n    };\n    applyStyleForTopScreen.__closure = {\n      createViewDescriptor,\n      updateProps: _updateProps.updateProps\n    };\n    applyStyleForTopScreen.__workletHash = 10105259623138;\n    applyStyleForTopScreen.__initData = _worklet_10105259623138_init_data;\n    applyStyleForTopScreen.__stackDetails = _e;\n    return applyStyleForTopScreen;\n  }();\n  var _worklet_10509666519158_init_data = {\n    code: \"function applyStyleForBelowTopScreen_reactNativeReanimated_styleUpdaterTs4(screenTransitionConfig,event){const{createViewDescriptor,updateProps}=this.__closure;const{screenDimensions:screenDimensions,belowTopScreenId:belowTopScreenId,screenTransition:screenTransition}=screenTransitionConfig;const{belowTopScreenStyle:computeBelowTopScreenStyle}=screenTransition;const belowTopScreenStyle=computeBelowTopScreenStyle(event,screenDimensions);const belowTopScreenDescriptor={value:[createViewDescriptor(belowTopScreenId)]};updateProps(belowTopScreenDescriptor,belowTopScreenStyle,undefined);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyStyleForBelowTopScreen_reactNativeReanimated_styleUpdaterTs4\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"createViewDescriptor\\\",\\\"updateProps\\\",\\\"__closure\\\",\\\"screenDimensions\\\",\\\"belowTopScreenId\\\",\\\"screenTransition\\\",\\\"belowTopScreenStyle\\\",\\\"computeBelowTopScreenStyle\\\",\\\"belowTopScreenDescriptor\\\",\\\"value\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\\\"],\\\"mappings\\\":\\\"AAuCO,SAAAA,iEAGLA,CAAAC,sBAAA,CAAAC,KAAA,QAAAC,oBAAA,CAAAC,WAAA,OAAAC,SAAA,CAEA,KAAM,CAAEC,gBAAgB,CAAhBA,gBAAgB,CAAEC,gBAAgB,CAAhBA,gBAAgB,CAAEC,gBAAA,CAAAA,gBAAiB,CAAC,CAC5DP,sBAAsB,CACxB,KAAM,CAAEQ,mBAAmB,CAAEC,0BAA2B,CAAC,CAAGF,gBAAgB,CAC5E,KAAM,CAAAC,mBAAmB,CAAGC,0BAA0B,CACpDR,KAAK,CACLI,gBACF,CAAC,CACD,KAAM,CAAAK,wBAAwB,CAAG,CAC/BC,KAAK,CAAE,CAACT,oBAAoB,CAACI,gBAAgB,CAAC,CAChD,CAAC,CACDH,WAAW,CAACO,wBAAwB,CAAEF,mBAAmB,CAAEI,SAAS,CAAC,CACvE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var applyStyleForBelowTopScreen = exports.applyStyleForBelowTopScreen = function () {\n    var _e = [new global.Error(), -3, -27];\n    var applyStyleForBelowTopScreen = function (screenTransitionConfig, event) {\n      var screenDimensions = screenTransitionConfig.screenDimensions,\n        belowTopScreenId = screenTransitionConfig.belowTopScreenId,\n        screenTransition = screenTransitionConfig.screenTransition;\n      var computeBelowTopScreenStyle = screenTransition.belowTopScreenStyle;\n      var belowTopScreenStyle = computeBelowTopScreenStyle(event, screenDimensions);\n      var belowTopScreenDescriptor = {\n        value: [createViewDescriptor(belowTopScreenId)]\n      };\n      (0, _updateProps.updateProps)(belowTopScreenDescriptor, belowTopScreenStyle, undefined);\n    };\n    applyStyleForBelowTopScreen.__closure = {\n      createViewDescriptor,\n      updateProps: _updateProps.updateProps\n    };\n    applyStyleForBelowTopScreen.__workletHash = 10509666519158;\n    applyStyleForBelowTopScreen.__initData = _worklet_10509666519158_init_data;\n    applyStyleForBelowTopScreen.__stackDetails = _e;\n    return applyStyleForBelowTopScreen;\n  }();\n  var _worklet_933859614838_init_data = {\n    code: \"function applyStyle_reactNativeReanimated_styleUpdaterTs5(screenTransitionConfig,event){const{applyStyleForTopScreen,applyStyleForBelowTopScreen}=this.__closure;applyStyleForTopScreen(screenTransitionConfig,event);applyStyleForBelowTopScreen(screenTransitionConfig,event);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"applyStyle_reactNativeReanimated_styleUpdaterTs5\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"applyStyleForTopScreen\\\",\\\"applyStyleForBelowTopScreen\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/styleUpdater.ts\\\"],\\\"mappings\\\":\\\"AAyDO,SAAAA,gDAGLA,CAAAC,sBAAA,CAAAC,KAAA,QAAAC,sBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAEAF,sBAAsB,CAACF,sBAAsB,CAAEC,KAAK,CAAC,CACrDE,2BAA2B,CAACH,sBAAsB,CAAEC,KAAK,CAAC,CAC5D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var applyStyle = exports.applyStyle = function () {\n    var _e = [new global.Error(), -3, -27];\n    var applyStyle = function (screenTransitionConfig, event) {\n      applyStyleForTopScreen(screenTransitionConfig, event);\n      applyStyleForBelowTopScreen(screenTransitionConfig, event);\n    };\n    applyStyle.__closure = {\n      applyStyleForTopScreen,\n      applyStyleForBelowTopScreen\n    };\n    applyStyle.__workletHash = 933859614838;\n    applyStyle.__initData = _worklet_933859614838_init_data;\n    applyStyle.__stackDetails = _e;\n    return applyStyle;\n  }();\n});", "lineCount": 128, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "applyStyleForBelowTopScreen"], [7, 37, 1, 13], [7, 40, 1, 13, "exports"], [7, 47, 1, 13], [7, 48, 1, 13, "applyStyle"], [7, 58, 1, 13], [8, 2, 4, 0], [8, 6, 4, 0, "_PlatformChecker"], [8, 22, 4, 0], [8, 25, 4, 0, "require"], [8, 32, 4, 0], [8, 33, 4, 0, "_dependencyMap"], [8, 47, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_updateProps"], [9, 18, 5, 0], [9, 21, 5, 0, "require"], [9, 28, 5, 0], [9, 29, 5, 0, "_dependencyMap"], [9, 43, 5, 0], [10, 2, 11, 0], [10, 6, 11, 6, "IS_FABRIC"], [10, 15, 11, 15], [10, 18, 11, 18], [10, 22, 11, 18, "isF<PERSON><PERSON>"], [10, 47, 11, 26], [10, 49, 11, 27], [10, 50, 11, 28], [11, 2, 11, 29], [11, 6, 11, 29, "_worklet_13427768743970_init_data"], [11, 39, 11, 29], [12, 4, 11, 29, "code"], [12, 8, 11, 29], [13, 4, 11, 29, "location"], [13, 12, 11, 29], [14, 4, 11, 29, "sourceMap"], [14, 13, 11, 29], [15, 4, 11, 29, "version"], [15, 11, 11, 29], [16, 2, 11, 29], [17, 2, 11, 29], [17, 6, 11, 29, "createViewDescriptorPaper"], [17, 31, 11, 29], [17, 34, 13, 0], [18, 4, 13, 0], [18, 8, 13, 0, "_e"], [18, 10, 13, 0], [18, 18, 13, 0, "global"], [18, 24, 13, 0], [18, 25, 13, 0, "Error"], [18, 30, 13, 0], [19, 4, 13, 0], [19, 8, 13, 0, "createViewDescriptorPaper"], [19, 33, 13, 0], [19, 45, 13, 0, "createViewDescriptorPaper"], [19, 46, 13, 35, "screenId"], [19, 54, 13, 71], [19, 56, 13, 73], [20, 6, 15, 2], [20, 13, 15, 9], [21, 8, 15, 11, "tag"], [21, 11, 15, 14], [21, 13, 15, 16, "screenId"], [21, 21, 15, 24], [22, 8, 15, 26, "name"], [22, 12, 15, 30], [22, 14, 15, 32], [23, 6, 15, 42], [23, 7, 15, 43], [24, 4, 16, 0], [24, 5, 16, 1], [25, 4, 16, 1, "createViewDescriptorPaper"], [25, 29, 16, 1], [25, 30, 16, 1, "__closure"], [25, 39, 16, 1], [26, 4, 16, 1, "createViewDescriptorPaper"], [26, 29, 16, 1], [26, 30, 16, 1, "__workletHash"], [26, 43, 16, 1], [27, 4, 16, 1, "createViewDescriptorPaper"], [27, 29, 16, 1], [27, 30, 16, 1, "__initData"], [27, 40, 16, 1], [27, 43, 16, 1, "_worklet_13427768743970_init_data"], [27, 76, 16, 1], [28, 4, 16, 1, "createViewDescriptorPaper"], [28, 29, 16, 1], [28, 30, 16, 1, "__stackDetails"], [28, 44, 16, 1], [28, 47, 16, 1, "_e"], [28, 49, 16, 1], [29, 4, 16, 1], [29, 11, 16, 1, "createViewDescriptorPaper"], [29, 36, 16, 1], [30, 2, 16, 1], [30, 3, 13, 0], [31, 2, 13, 0], [31, 6, 13, 0, "_worklet_926744547348_init_data"], [31, 37, 13, 0], [32, 4, 13, 0, "code"], [32, 8, 13, 0], [33, 4, 13, 0, "location"], [33, 12, 13, 0], [34, 4, 13, 0, "sourceMap"], [34, 13, 13, 0], [35, 4, 13, 0, "version"], [35, 11, 13, 0], [36, 2, 13, 0], [37, 2, 13, 0], [37, 6, 13, 0, "createViewDescriptorFabric"], [37, 32, 13, 0], [37, 35, 17, 0], [38, 4, 17, 0], [38, 8, 17, 0, "_e"], [38, 10, 17, 0], [38, 18, 17, 0, "global"], [38, 24, 17, 0], [38, 25, 17, 0, "Error"], [38, 30, 17, 0], [39, 4, 17, 0], [39, 8, 17, 0, "createViewDescriptorFabric"], [39, 34, 17, 0], [39, 46, 17, 0, "createViewDescriptorFabric"], [39, 47, 17, 36, "screenId"], [39, 55, 17, 72], [39, 57, 17, 74], [40, 6, 19, 2], [40, 13, 19, 9], [41, 8, 19, 11, "shadowNodeWrapper"], [41, 25, 19, 28], [41, 27, 19, 30, "screenId"], [42, 6, 19, 39], [42, 7, 19, 40], [43, 4, 20, 0], [43, 5, 20, 1], [44, 4, 20, 1, "createViewDescriptorFabric"], [44, 30, 20, 1], [44, 31, 20, 1, "__closure"], [44, 40, 20, 1], [45, 4, 20, 1, "createViewDescriptorFabric"], [45, 30, 20, 1], [45, 31, 20, 1, "__workletHash"], [45, 44, 20, 1], [46, 4, 20, 1, "createViewDescriptorFabric"], [46, 30, 20, 1], [46, 31, 20, 1, "__initData"], [46, 41, 20, 1], [46, 44, 20, 1, "_worklet_926744547348_init_data"], [46, 75, 20, 1], [47, 4, 20, 1, "createViewDescriptorFabric"], [47, 30, 20, 1], [47, 31, 20, 1, "__stackDetails"], [47, 45, 20, 1], [47, 48, 20, 1, "_e"], [47, 50, 20, 1], [48, 4, 20, 1], [48, 11, 20, 1, "createViewDescriptorFabric"], [48, 37, 20, 1], [49, 2, 20, 1], [49, 3, 17, 0], [50, 2, 21, 0], [50, 6, 21, 6, "createViewDescriptor"], [50, 26, 21, 26], [50, 29, 21, 29, "IS_FABRIC"], [50, 38, 21, 38], [50, 41, 22, 4, "createViewDescriptorFabric"], [50, 67, 22, 30], [50, 70, 23, 4, "createViewDescriptorPaper"], [50, 95, 23, 29], [51, 2, 23, 30], [51, 6, 23, 30, "_worklet_10105259623138_init_data"], [51, 39, 23, 30], [52, 4, 23, 30, "code"], [52, 8, 23, 30], [53, 4, 23, 30, "location"], [53, 12, 23, 30], [54, 4, 23, 30, "sourceMap"], [54, 13, 23, 30], [55, 4, 23, 30, "version"], [55, 11, 23, 30], [56, 2, 23, 30], [57, 2, 23, 30], [57, 6, 23, 30, "applyStyleForTopScreen"], [57, 28, 23, 30], [57, 31, 25, 0], [58, 4, 25, 0], [58, 8, 25, 0, "_e"], [58, 10, 25, 0], [58, 18, 25, 0, "global"], [58, 24, 25, 0], [58, 25, 25, 0, "Error"], [58, 30, 25, 0], [59, 4, 25, 0], [59, 8, 25, 0, "applyStyleForTopScreen"], [59, 30, 25, 0], [59, 42, 25, 0, "applyStyleForTopScreen"], [59, 43, 26, 2, "screenTransitionConfig"], [59, 65, 26, 48], [59, 67, 27, 2, "event"], [59, 72, 27, 38], [59, 74, 28, 2], [60, 6, 30, 2], [60, 10, 30, 10, "screenDimensions"], [60, 26, 30, 26], [60, 29, 31, 4, "screenTransitionConfig"], [60, 51, 31, 26], [60, 52, 30, 10, "screenDimensions"], [60, 68, 30, 26], [61, 8, 30, 28, "topScreenId"], [61, 19, 30, 39], [61, 22, 31, 4, "screenTransitionConfig"], [61, 44, 31, 26], [61, 45, 30, 28, "topScreenId"], [61, 56, 30, 39], [62, 8, 30, 41, "screenTransition"], [62, 24, 30, 57], [62, 27, 31, 4, "screenTransitionConfig"], [62, 49, 31, 26], [62, 50, 30, 41, "screenTransition"], [62, 66, 30, 57], [63, 6, 32, 2], [63, 10, 32, 26, "computeTopScreenStyle"], [63, 31, 32, 47], [63, 34, 32, 52, "screenTransition"], [63, 50, 32, 68], [63, 51, 32, 10, "topScreenStyle"], [63, 65, 32, 24], [64, 6, 33, 2], [64, 10, 33, 8, "topScreenStyle"], [64, 24, 33, 22], [64, 27, 33, 25, "computeTopScreenStyle"], [64, 48, 33, 46], [64, 49, 33, 47, "event"], [64, 54, 33, 52], [64, 56, 33, 54, "screenDimensions"], [64, 72, 33, 70], [64, 73, 33, 71], [65, 6, 34, 2], [65, 10, 34, 8, "topScreenDescriptor"], [65, 29, 34, 27], [65, 32, 34, 30], [66, 8, 35, 4, "value"], [66, 13, 35, 9], [66, 15, 35, 11], [66, 16, 35, 12, "createViewDescriptor"], [66, 36, 35, 32], [66, 37, 35, 33, "topScreenId"], [66, 48, 35, 44], [66, 49, 35, 45], [67, 6, 36, 2], [67, 7, 36, 3], [68, 6, 37, 2], [68, 10, 37, 2, "updateProps"], [68, 34, 37, 13], [68, 36, 37, 14, "topScreenDescriptor"], [68, 55, 37, 33], [68, 57, 37, 35, "topScreenStyle"], [68, 71, 37, 49], [68, 73, 37, 51, "undefined"], [68, 82, 37, 60], [68, 83, 37, 61], [69, 4, 38, 0], [69, 5, 38, 1], [70, 4, 38, 1, "applyStyleForTopScreen"], [70, 26, 38, 1], [70, 27, 38, 1, "__closure"], [70, 36, 38, 1], [71, 6, 38, 1, "createViewDescriptor"], [71, 26, 38, 1], [72, 6, 38, 1, "updateProps"], [72, 17, 38, 1], [72, 19, 37, 2, "updateProps"], [73, 4, 37, 13], [74, 4, 37, 13, "applyStyleForTopScreen"], [74, 26, 37, 13], [74, 27, 37, 13, "__workletHash"], [74, 40, 37, 13], [75, 4, 37, 13, "applyStyleForTopScreen"], [75, 26, 37, 13], [75, 27, 37, 13, "__initData"], [75, 37, 37, 13], [75, 40, 37, 13, "_worklet_10105259623138_init_data"], [75, 73, 37, 13], [76, 4, 37, 13, "applyStyleForTopScreen"], [76, 26, 37, 13], [76, 27, 37, 13, "__stackDetails"], [76, 41, 37, 13], [76, 44, 37, 13, "_e"], [76, 46, 37, 13], [77, 4, 37, 13], [77, 11, 37, 13, "applyStyleForTopScreen"], [77, 33, 37, 13], [78, 2, 37, 13], [78, 3, 25, 0], [79, 2, 25, 0], [79, 6, 25, 0, "_worklet_10509666519158_init_data"], [79, 39, 25, 0], [80, 4, 25, 0, "code"], [80, 8, 25, 0], [81, 4, 25, 0, "location"], [81, 12, 25, 0], [82, 4, 25, 0, "sourceMap"], [82, 13, 25, 0], [83, 4, 25, 0, "version"], [83, 11, 25, 0], [84, 2, 25, 0], [85, 2, 25, 0], [85, 6, 25, 0, "applyStyleForBelowTopScreen"], [85, 33, 25, 0], [85, 36, 25, 0, "exports"], [85, 43, 25, 0], [85, 44, 25, 0, "applyStyleForBelowTopScreen"], [85, 71, 25, 0], [85, 74, 40, 7], [86, 4, 40, 7], [86, 8, 40, 7, "_e"], [86, 10, 40, 7], [86, 18, 40, 7, "global"], [86, 24, 40, 7], [86, 25, 40, 7, "Error"], [86, 30, 40, 7], [87, 4, 40, 7], [87, 8, 40, 7, "applyStyleForBelowTopScreen"], [87, 35, 40, 7], [87, 47, 40, 7, "applyStyleForBelowTopScreen"], [87, 48, 41, 2, "screenTransitionConfig"], [87, 70, 41, 48], [87, 72, 42, 2, "event"], [87, 77, 42, 38], [87, 79, 43, 2], [88, 6, 45, 2], [88, 10, 45, 10, "screenDimensions"], [88, 26, 45, 26], [88, 29, 46, 4, "screenTransitionConfig"], [88, 51, 46, 26], [88, 52, 45, 10, "screenDimensions"], [88, 68, 45, 26], [89, 8, 45, 28, "belowTopScreenId"], [89, 24, 45, 44], [89, 27, 46, 4, "screenTransitionConfig"], [89, 49, 46, 26], [89, 50, 45, 28, "belowTopScreenId"], [89, 66, 45, 44], [90, 8, 45, 46, "screenTransition"], [90, 24, 45, 62], [90, 27, 46, 4, "screenTransitionConfig"], [90, 49, 46, 26], [90, 50, 45, 46, "screenTransition"], [90, 66, 45, 62], [91, 6, 47, 2], [91, 10, 47, 31, "computeBelowTopScreenStyle"], [91, 36, 47, 57], [91, 39, 47, 62, "screenTransition"], [91, 55, 47, 78], [91, 56, 47, 10, "belowTopScreenStyle"], [91, 75, 47, 29], [92, 6, 48, 2], [92, 10, 48, 8, "belowTopScreenStyle"], [92, 29, 48, 27], [92, 32, 48, 30, "computeBelowTopScreenStyle"], [92, 58, 48, 56], [92, 59, 49, 4, "event"], [92, 64, 49, 9], [92, 66, 50, 4, "screenDimensions"], [92, 82, 51, 2], [92, 83, 51, 3], [93, 6, 52, 2], [93, 10, 52, 8, "belowTopScreenDescriptor"], [93, 34, 52, 32], [93, 37, 52, 35], [94, 8, 53, 4, "value"], [94, 13, 53, 9], [94, 15, 53, 11], [94, 16, 53, 12, "createViewDescriptor"], [94, 36, 53, 32], [94, 37, 53, 33, "belowTopScreenId"], [94, 53, 53, 49], [94, 54, 53, 50], [95, 6, 54, 2], [95, 7, 54, 3], [96, 6, 55, 2], [96, 10, 55, 2, "updateProps"], [96, 34, 55, 13], [96, 36, 55, 14, "belowTopScreenDescriptor"], [96, 60, 55, 38], [96, 62, 55, 40, "belowTopScreenStyle"], [96, 81, 55, 59], [96, 83, 55, 61, "undefined"], [96, 92, 55, 70], [96, 93, 55, 71], [97, 4, 56, 0], [97, 5, 56, 1], [98, 4, 56, 1, "applyStyleForBelowTopScreen"], [98, 31, 56, 1], [98, 32, 56, 1, "__closure"], [98, 41, 56, 1], [99, 6, 56, 1, "createViewDescriptor"], [99, 26, 56, 1], [100, 6, 56, 1, "updateProps"], [100, 17, 56, 1], [100, 19, 55, 2, "updateProps"], [101, 4, 55, 13], [102, 4, 55, 13, "applyStyleForBelowTopScreen"], [102, 31, 55, 13], [102, 32, 55, 13, "__workletHash"], [102, 45, 55, 13], [103, 4, 55, 13, "applyStyleForBelowTopScreen"], [103, 31, 55, 13], [103, 32, 55, 13, "__initData"], [103, 42, 55, 13], [103, 45, 55, 13, "_worklet_10509666519158_init_data"], [103, 78, 55, 13], [104, 4, 55, 13, "applyStyleForBelowTopScreen"], [104, 31, 55, 13], [104, 32, 55, 13, "__stackDetails"], [104, 46, 55, 13], [104, 49, 55, 13, "_e"], [104, 51, 55, 13], [105, 4, 55, 13], [105, 11, 55, 13, "applyStyleForBelowTopScreen"], [105, 38, 55, 13], [106, 2, 55, 13], [106, 3, 40, 7], [107, 2, 40, 7], [107, 6, 40, 7, "_worklet_933859614838_init_data"], [107, 37, 40, 7], [108, 4, 40, 7, "code"], [108, 8, 40, 7], [109, 4, 40, 7, "location"], [109, 12, 40, 7], [110, 4, 40, 7, "sourceMap"], [110, 13, 40, 7], [111, 4, 40, 7, "version"], [111, 11, 40, 7], [112, 2, 40, 7], [113, 2, 40, 7], [113, 6, 40, 7, "applyStyle"], [113, 16, 40, 7], [113, 19, 40, 7, "exports"], [113, 26, 40, 7], [113, 27, 40, 7, "applyStyle"], [113, 37, 40, 7], [113, 40, 58, 7], [114, 4, 58, 7], [114, 8, 58, 7, "_e"], [114, 10, 58, 7], [114, 18, 58, 7, "global"], [114, 24, 58, 7], [114, 25, 58, 7, "Error"], [114, 30, 58, 7], [115, 4, 58, 7], [115, 8, 58, 7, "applyStyle"], [115, 18, 58, 7], [115, 30, 58, 7, "applyStyle"], [115, 31, 59, 2, "screenTransitionConfig"], [115, 53, 59, 48], [115, 55, 60, 2, "event"], [115, 60, 60, 38], [115, 62, 61, 2], [116, 6, 63, 2, "applyStyleForTopScreen"], [116, 28, 63, 24], [116, 29, 63, 25, "screenTransitionConfig"], [116, 51, 63, 47], [116, 53, 63, 49, "event"], [116, 58, 63, 54], [116, 59, 63, 55], [117, 6, 64, 2, "applyStyleForBelowTopScreen"], [117, 33, 64, 29], [117, 34, 64, 30, "screenTransitionConfig"], [117, 56, 64, 52], [117, 58, 64, 54, "event"], [117, 63, 64, 59], [117, 64, 64, 60], [118, 4, 65, 0], [118, 5, 65, 1], [119, 4, 65, 1, "applyStyle"], [119, 14, 65, 1], [119, 15, 65, 1, "__closure"], [119, 24, 65, 1], [120, 6, 65, 1, "applyStyleForTopScreen"], [120, 28, 65, 1], [121, 6, 65, 1, "applyStyleForBelowTopScreen"], [122, 4, 65, 1], [123, 4, 65, 1, "applyStyle"], [123, 14, 65, 1], [123, 15, 65, 1, "__workletHash"], [123, 28, 65, 1], [124, 4, 65, 1, "applyStyle"], [124, 14, 65, 1], [124, 15, 65, 1, "__initData"], [124, 25, 65, 1], [124, 28, 65, 1, "_worklet_933859614838_init_data"], [124, 59, 65, 1], [125, 4, 65, 1, "applyStyle"], [125, 14, 65, 1], [125, 15, 65, 1, "__stackDetails"], [125, 29, 65, 1], [125, 32, 65, 1, "_e"], [125, 34, 65, 1], [126, 4, 65, 1], [126, 11, 65, 1, "applyStyle"], [126, 21, 65, 1], [127, 2, 65, 1], [127, 3, 58, 7], [128, 0, 58, 7], [128, 3]], "functionMap": {"names": ["<global>", "createViewDescriptorPaper", "createViewDescriptorFabric", "applyStyleForTopScreen", "applyStyleForBelowTopScreen", "applyStyle"], "mappings": "AAA;ACY;CDG;AEC;CFG;AGK;CHa;OIE;CJgB;OKE;CLO"}}, "type": "js/module"}]}