{"dependencies": [{"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 37, "index": 37}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.findHandler = findHandler;\n  exports.findHandlerByTestID = findHandlerByTestID;\n  exports.findOldGestureHandler = findOldGestureHandler;\n  exports.handlerIDToTag = void 0;\n  exports.registerHandler = registerHandler;\n  exports.registerOldGestureHandler = registerOldGestureHandler;\n  exports.unregisterHandler = unregisterHandler;\n  exports.unregisterOldGestureHandler = unregisterOldGestureHandler;\n  var _utils = require(_dependencyMap[0], \"../utils\");\n  const handlerIDToTag = exports.handlerIDToTag = {};\n  const gestures = new Map();\n  const oldHandlers = new Map();\n  const testIDs = new Map();\n  function registerHandler(handlerTag, handler, testID) {\n    gestures.set(handlerTag, handler);\n    if ((0, _utils.isTestEnv)() && testID) {\n      testIDs.set(testID, handlerTag);\n    }\n  }\n  function registerOldGestureHandler(handlerTag, handler) {\n    oldHandlers.set(handlerTag, handler);\n  }\n  function unregisterOldGestureHandler(handlerTag) {\n    oldHandlers.delete(handlerTag);\n  }\n  function unregisterHandler(handlerTag, testID) {\n    gestures.delete(handlerTag);\n    if ((0, _utils.isTestEnv)() && testID) {\n      testIDs.delete(testID);\n    }\n  }\n  function findHandler(handlerTag) {\n    return gestures.get(handlerTag);\n  }\n  function findOldGestureHandler(handlerTag) {\n    return oldHandlers.get(handlerTag);\n  }\n  function findHandlerByTestID(testID) {\n    const handlerTag = testIDs.get(testID);\n    if (handlerTag !== undefined) {\n      var _findHandler;\n      return (_findHandler = findHandler(handlerTag)) !== null && _findHandler !== void 0 ? _findHandler : null;\n    }\n    return null;\n  }\n});", "lineCount": 50, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_utils"], [13, 12, 1, 0], [13, 15, 1, 0, "require"], [13, 22, 1, 0], [13, 23, 1, 0, "_dependencyMap"], [13, 37, 1, 0], [14, 2, 2, 7], [14, 8, 2, 13, "handlerIDToTag"], [14, 22, 2, 27], [14, 25, 2, 27, "exports"], [14, 32, 2, 27], [14, 33, 2, 27, "handlerIDToTag"], [14, 47, 2, 27], [14, 50, 2, 30], [14, 51, 2, 31], [14, 52, 2, 32], [15, 2, 3, 0], [15, 8, 3, 6, "gestures"], [15, 16, 3, 14], [15, 19, 3, 17], [15, 23, 3, 21, "Map"], [15, 26, 3, 24], [15, 27, 3, 25], [15, 28, 3, 26], [16, 2, 4, 0], [16, 8, 4, 6, "oldHandlers"], [16, 19, 4, 17], [16, 22, 4, 20], [16, 26, 4, 24, "Map"], [16, 29, 4, 27], [16, 30, 4, 28], [16, 31, 4, 29], [17, 2, 5, 0], [17, 8, 5, 6, "testIDs"], [17, 15, 5, 13], [17, 18, 5, 16], [17, 22, 5, 20, "Map"], [17, 25, 5, 23], [17, 26, 5, 24], [17, 27, 5, 25], [18, 2, 6, 7], [18, 11, 6, 16, "registerHandler"], [18, 26, 6, 31, "registerHandler"], [18, 27, 6, 32, "handlerTag"], [18, 37, 6, 42], [18, 39, 6, 44, "handler"], [18, 46, 6, 51], [18, 48, 6, 53, "testID"], [18, 54, 6, 59], [18, 56, 6, 61], [19, 4, 7, 2, "gestures"], [19, 12, 7, 10], [19, 13, 7, 11, "set"], [19, 16, 7, 14], [19, 17, 7, 15, "handlerTag"], [19, 27, 7, 25], [19, 29, 7, 27, "handler"], [19, 36, 7, 34], [19, 37, 7, 35], [20, 4, 9, 2], [20, 8, 9, 6], [20, 12, 9, 6, "isTestEnv"], [20, 28, 9, 15], [20, 30, 9, 16], [20, 31, 9, 17], [20, 35, 9, 21, "testID"], [20, 41, 9, 27], [20, 43, 9, 29], [21, 6, 10, 4, "testIDs"], [21, 13, 10, 11], [21, 14, 10, 12, "set"], [21, 17, 10, 15], [21, 18, 10, 16, "testID"], [21, 24, 10, 22], [21, 26, 10, 24, "handlerTag"], [21, 36, 10, 34], [21, 37, 10, 35], [22, 4, 11, 2], [23, 2, 12, 0], [24, 2, 13, 7], [24, 11, 13, 16, "registerOldGestureHandler"], [24, 36, 13, 41, "registerOldGestureHandler"], [24, 37, 13, 42, "handlerTag"], [24, 47, 13, 52], [24, 49, 13, 54, "handler"], [24, 56, 13, 61], [24, 58, 13, 63], [25, 4, 14, 2, "oldHandlers"], [25, 15, 14, 13], [25, 16, 14, 14, "set"], [25, 19, 14, 17], [25, 20, 14, 18, "handlerTag"], [25, 30, 14, 28], [25, 32, 14, 30, "handler"], [25, 39, 14, 37], [25, 40, 14, 38], [26, 2, 15, 0], [27, 2, 16, 7], [27, 11, 16, 16, "unregisterOldGestureHandler"], [27, 38, 16, 43, "unregisterOldGestureHandler"], [27, 39, 16, 44, "handlerTag"], [27, 49, 16, 54], [27, 51, 16, 56], [28, 4, 17, 2, "oldHandlers"], [28, 15, 17, 13], [28, 16, 17, 14, "delete"], [28, 22, 17, 20], [28, 23, 17, 21, "handlerTag"], [28, 33, 17, 31], [28, 34, 17, 32], [29, 2, 18, 0], [30, 2, 19, 7], [30, 11, 19, 16, "unregister<PERSON><PERSON><PERSON>"], [30, 28, 19, 33, "unregister<PERSON><PERSON><PERSON>"], [30, 29, 19, 34, "handlerTag"], [30, 39, 19, 44], [30, 41, 19, 46, "testID"], [30, 47, 19, 52], [30, 49, 19, 54], [31, 4, 20, 2, "gestures"], [31, 12, 20, 10], [31, 13, 20, 11, "delete"], [31, 19, 20, 17], [31, 20, 20, 18, "handlerTag"], [31, 30, 20, 28], [31, 31, 20, 29], [32, 4, 22, 2], [32, 8, 22, 6], [32, 12, 22, 6, "isTestEnv"], [32, 28, 22, 15], [32, 30, 22, 16], [32, 31, 22, 17], [32, 35, 22, 21, "testID"], [32, 41, 22, 27], [32, 43, 22, 29], [33, 6, 23, 4, "testIDs"], [33, 13, 23, 11], [33, 14, 23, 12, "delete"], [33, 20, 23, 18], [33, 21, 23, 19, "testID"], [33, 27, 23, 25], [33, 28, 23, 26], [34, 4, 24, 2], [35, 2, 25, 0], [36, 2, 26, 7], [36, 11, 26, 16, "<PERSON><PERSON><PERSON><PERSON>"], [36, 22, 26, 27, "<PERSON><PERSON><PERSON><PERSON>"], [36, 23, 26, 28, "handlerTag"], [36, 33, 26, 38], [36, 35, 26, 40], [37, 4, 27, 2], [37, 11, 27, 9, "gestures"], [37, 19, 27, 17], [37, 20, 27, 18, "get"], [37, 23, 27, 21], [37, 24, 27, 22, "handlerTag"], [37, 34, 27, 32], [37, 35, 27, 33], [38, 2, 28, 0], [39, 2, 29, 7], [39, 11, 29, 16, "findOldGestureHandler"], [39, 32, 29, 37, "findOldGestureHandler"], [39, 33, 29, 38, "handlerTag"], [39, 43, 29, 48], [39, 45, 29, 50], [40, 4, 30, 2], [40, 11, 30, 9, "oldHandlers"], [40, 22, 30, 20], [40, 23, 30, 21, "get"], [40, 26, 30, 24], [40, 27, 30, 25, "handlerTag"], [40, 37, 30, 35], [40, 38, 30, 36], [41, 2, 31, 0], [42, 2, 32, 7], [42, 11, 32, 16, "findHandlerByTestID"], [42, 30, 32, 35, "findHandlerByTestID"], [42, 31, 32, 36, "testID"], [42, 37, 32, 42], [42, 39, 32, 44], [43, 4, 33, 2], [43, 10, 33, 8, "handlerTag"], [43, 20, 33, 18], [43, 23, 33, 21, "testIDs"], [43, 30, 33, 28], [43, 31, 33, 29, "get"], [43, 34, 33, 32], [43, 35, 33, 33, "testID"], [43, 41, 33, 39], [43, 42, 33, 40], [44, 4, 35, 2], [44, 8, 35, 6, "handlerTag"], [44, 18, 35, 16], [44, 23, 35, 21, "undefined"], [44, 32, 35, 30], [44, 34, 35, 32], [45, 6, 36, 4], [45, 10, 36, 8, "_find<PERSON><PERSON><PERSON>"], [45, 22, 36, 20], [46, 6, 38, 4], [46, 13, 38, 11], [46, 14, 38, 12, "_find<PERSON><PERSON><PERSON>"], [46, 26, 38, 24], [46, 29, 38, 27, "<PERSON><PERSON><PERSON><PERSON>"], [46, 40, 38, 38], [46, 41, 38, 39, "handlerTag"], [46, 51, 38, 49], [46, 52, 38, 50], [46, 58, 38, 56], [46, 62, 38, 60], [46, 66, 38, 64, "_find<PERSON><PERSON><PERSON>"], [46, 78, 38, 76], [46, 83, 38, 81], [46, 88, 38, 86], [46, 89, 38, 87], [46, 92, 38, 90, "_find<PERSON><PERSON><PERSON>"], [46, 104, 38, 102], [46, 107, 38, 105], [46, 111, 38, 109], [47, 4, 39, 2], [48, 4, 41, 2], [48, 11, 41, 9], [48, 15, 41, 13], [49, 2, 42, 0], [50, 0, 42, 1], [50, 3]], "functionMap": {"names": ["<global>", "registerHandler", "registerOldGestureHandler", "unregisterOldGestureHandler", "unregister<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "findOldGestureHandler", "findHandlerByTestID"], "mappings": "AAA;OCK;CDM;OEC;CFE;OGC;CHE;OIC;CJM;OKC;CLE;OMC;CNE;OOC;CPU"}}, "type": "js/module"}]}