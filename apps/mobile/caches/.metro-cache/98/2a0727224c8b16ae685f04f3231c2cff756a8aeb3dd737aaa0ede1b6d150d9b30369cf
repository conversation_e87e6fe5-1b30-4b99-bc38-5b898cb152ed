{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 80}, "end": {"line": 4, "column": 46, "index": 126}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/PixelRatio", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5rdRioKC4qvLVlVTyxLOiQm3IeU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[1], \"./GestureHandler\"));\n  var _PixelRatio = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/PixelRatio\"));\n  /* eslint-disable eslint-comments/no-unlimited-disable */\n\n  /* eslint-disable */\n\n  class DraggingGestureHandler extends _GestureHandler.default {\n    get shouldEnableGestureOnSetup() {\n      return true;\n    }\n    transformNativeEvent({\n      deltaX,\n      deltaY,\n      velocityX,\n      velocityY,\n      center: {\n        x,\n        y\n      }\n    }) {\n      // @ts-ignore FIXME(TS)\n      const rect = this.view.getBoundingClientRect();\n      const ratio = _PixelRatio.default.get();\n      return {\n        translationX: deltaX - (this.__initialX || 0),\n        translationY: deltaY - (this.__initialY || 0),\n        absoluteX: x,\n        absoluteY: y,\n        velocityX: velocityX * ratio,\n        velocityY: velocityY * ratio,\n        x: x - rect.left,\n        y: y - rect.top\n      };\n    }\n  }\n  var _default = exports.default = DraggingGestureHandler;\n});", "lineCount": 43, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_Gesture<PERSON><PERSON>ler"], [7, 21, 4, 0], [7, 24, 4, 0, "_interopRequireDefault"], [7, 46, 4, 0], [7, 47, 4, 0, "require"], [7, 54, 4, 0], [7, 55, 4, 0, "_dependencyMap"], [7, 69, 4, 0], [8, 2, 4, 46], [8, 6, 4, 46, "_PixelRatio"], [8, 17, 4, 46], [8, 20, 4, 46, "_interopRequireDefault"], [8, 42, 4, 46], [8, 43, 4, 46, "require"], [8, 50, 4, 46], [8, 51, 4, 46, "_dependencyMap"], [8, 65, 4, 46], [9, 2, 1, 0], [11, 2, 3, 0], [13, 2, 7, 0], [13, 8, 7, 6, "DraggingGestureHandler"], [13, 30, 7, 28], [13, 39, 7, 37, "Gesture<PERSON>andler"], [13, 62, 7, 51], [13, 63, 7, 52], [14, 4, 8, 2], [14, 8, 8, 6, "shouldEnableGestureOnSetup"], [14, 34, 8, 32, "shouldEnableGestureOnSetup"], [14, 35, 8, 32], [14, 37, 8, 35], [15, 6, 9, 4], [15, 13, 9, 11], [15, 17, 9, 15], [16, 4, 10, 2], [17, 4, 12, 2, "transformNativeEvent"], [17, 24, 12, 22, "transformNativeEvent"], [17, 25, 12, 23], [18, 6, 13, 4, "deltaX"], [18, 12, 13, 10], [19, 6, 14, 4, "deltaY"], [19, 12, 14, 10], [20, 6, 15, 4, "velocityX"], [20, 15, 15, 13], [21, 6, 16, 4, "velocityY"], [21, 15, 16, 13], [22, 6, 17, 4, "center"], [22, 12, 17, 10], [22, 14, 17, 12], [23, 8, 18, 6, "x"], [23, 9, 18, 7], [24, 8, 19, 6, "y"], [25, 6, 20, 4], [26, 4, 21, 2], [26, 5, 21, 3], [26, 7, 21, 5], [27, 6, 22, 4], [28, 6, 23, 4], [28, 12, 23, 10, "rect"], [28, 16, 23, 14], [28, 19, 23, 17], [28, 23, 23, 21], [28, 24, 23, 22, "view"], [28, 28, 23, 26], [28, 29, 23, 27, "getBoundingClientRect"], [28, 50, 23, 48], [28, 51, 23, 49], [28, 52, 23, 50], [29, 6, 24, 4], [29, 12, 24, 10, "ratio"], [29, 17, 24, 15], [29, 20, 24, 18, "PixelRatio"], [29, 39, 24, 28], [29, 40, 24, 29, "get"], [29, 43, 24, 32], [29, 44, 24, 33], [29, 45, 24, 34], [30, 6, 25, 4], [30, 13, 25, 11], [31, 8, 26, 6, "translationX"], [31, 20, 26, 18], [31, 22, 26, 20, "deltaX"], [31, 28, 26, 26], [31, 32, 26, 30], [31, 36, 26, 34], [31, 37, 26, 35, "__initialX"], [31, 47, 26, 45], [31, 51, 26, 49], [31, 52, 26, 50], [31, 53, 26, 51], [32, 8, 27, 6, "translationY"], [32, 20, 27, 18], [32, 22, 27, 20, "deltaY"], [32, 28, 27, 26], [32, 32, 27, 30], [32, 36, 27, 34], [32, 37, 27, 35, "__initialY"], [32, 47, 27, 45], [32, 51, 27, 49], [32, 52, 27, 50], [32, 53, 27, 51], [33, 8, 28, 6, "absoluteX"], [33, 17, 28, 15], [33, 19, 28, 17, "x"], [33, 20, 28, 18], [34, 8, 29, 6, "absoluteY"], [34, 17, 29, 15], [34, 19, 29, 17, "y"], [34, 20, 29, 18], [35, 8, 30, 6, "velocityX"], [35, 17, 30, 15], [35, 19, 30, 17, "velocityX"], [35, 28, 30, 26], [35, 31, 30, 29, "ratio"], [35, 36, 30, 34], [36, 8, 31, 6, "velocityY"], [36, 17, 31, 15], [36, 19, 31, 17, "velocityY"], [36, 28, 31, 26], [36, 31, 31, 29, "ratio"], [36, 36, 31, 34], [37, 8, 32, 6, "x"], [37, 9, 32, 7], [37, 11, 32, 9, "x"], [37, 12, 32, 10], [37, 15, 32, 13, "rect"], [37, 19, 32, 17], [37, 20, 32, 18, "left"], [37, 24, 32, 22], [38, 8, 33, 6, "y"], [38, 9, 33, 7], [38, 11, 33, 9, "y"], [38, 12, 33, 10], [38, 15, 33, 13, "rect"], [38, 19, 33, 17], [38, 20, 33, 18, "top"], [39, 6, 34, 4], [39, 7, 34, 5], [40, 4, 35, 2], [41, 2, 37, 0], [42, 2, 37, 1], [42, 6, 37, 1, "_default"], [42, 14, 37, 1], [42, 17, 37, 1, "exports"], [42, 24, 37, 1], [42, 25, 37, 1, "default"], [42, 32, 37, 1], [42, 35, 39, 15, "DraggingGestureHandler"], [42, 57, 39, 37], [43, 0, 39, 37], [43, 3]], "functionMap": {"names": ["<global>", "DraggingGestureHandler", "get__shouldEnableGestureOnSetup", "transformNativeEvent"], "mappings": "AAA;ACM;ECC;GDE;EEE;GFuB;CDE"}}, "type": "js/module"}]}