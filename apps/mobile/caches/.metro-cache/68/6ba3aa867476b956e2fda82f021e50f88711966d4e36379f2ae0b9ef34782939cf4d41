{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _classPrivateFieldBase(e, t) {\n    if (!{}.hasOwnProperty.call(e, t)) throw new TypeError(\"attempted to use private field on non-instance\");\n    return e;\n  }\n  module.exports = _classPrivateFieldBase, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 7, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_classPrivateFieldBase"], [2, 33, 1, 31, "_classPrivateFieldBase"], [2, 34, 1, 32, "e"], [2, 35, 1, 33], [2, 37, 1, 35, "t"], [2, 38, 1, 36], [2, 40, 1, 38], [3, 4, 2, 2], [3, 8, 2, 6], [3, 9, 2, 7], [3, 10, 2, 8], [3, 11, 2, 9], [3, 12, 2, 10, "hasOwnProperty"], [3, 26, 2, 24], [3, 27, 2, 25, "call"], [3, 31, 2, 29], [3, 32, 2, 30, "e"], [3, 33, 2, 31], [3, 35, 2, 33, "t"], [3, 36, 2, 34], [3, 37, 2, 35], [3, 39, 2, 37], [3, 45, 2, 43], [3, 49, 2, 47, "TypeError"], [3, 58, 2, 56], [3, 59, 2, 57], [3, 107, 2, 105], [3, 108, 2, 106], [4, 4, 3, 2], [4, 11, 3, 9, "e"], [4, 12, 3, 10], [5, 2, 4, 0], [6, 2, 5, 0, "module"], [6, 8, 5, 6], [6, 9, 5, 7, "exports"], [6, 16, 5, 14], [6, 19, 5, 17, "_classPrivateFieldBase"], [6, 41, 5, 39], [6, 43, 5, 41, "module"], [6, 49, 5, 47], [6, 50, 5, 48, "exports"], [6, 57, 5, 55], [6, 58, 5, 56, "__esModule"], [6, 68, 5, 66], [6, 71, 5, 69], [6, 75, 5, 73], [6, 77, 5, 75, "module"], [6, 83, 5, 81], [6, 84, 5, 82, "exports"], [6, 91, 5, 89], [6, 92, 5, 90], [6, 101, 5, 99], [6, 102, 5, 100], [6, 105, 5, 103, "module"], [6, 111, 5, 109], [6, 112, 5, 110, "exports"], [6, 119, 5, 117], [7, 0, 5, 118], [7, 3]], "functionMap": {"names": ["_classPrivateFieldBase", "<global>"], "mappings": "AAA;CCG"}}, "type": "js/module"}]}