{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 64, "index": 96}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "hoist-non-react-statics", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 97}, "end": {"line": 3, "column": 59, "index": 156}}], "key": "MoIuZFdUef7yl/jvHg7drnKZFmo=", "exportNames": ["*"]}}, {"name": "./GestureHandlerRootView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 157}, "end": {"line": 4, "column": 62, "index": 219}}], "key": "RWLneAcG75BjfgIQnRuzKrHXe1k=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = gestureHandlerRootHOC;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _hoistNonReactStatics = _interopRequireDefault(require(_dependencyMap[3], \"hoist-non-react-statics\"));\n  var _GestureHandlerRootView = _interopRequireDefault(require(_dependencyMap[4], \"./GestureHandlerRootView\"));\n  var _jsxDevRuntime = require(_dependencyMap[5], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-gesture-handler/src/components/gestureHandlerRootHOC.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function gestureHandlerRootHOC(Component, containerStyles) {\n    function Wrapper(props) {\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_GestureHandlerRootView.default, {\n        style: [styles.container, containerStyles],\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(Component, {\n          ...props\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 7\n      }, this);\n    }\n    Wrapper.displayName = `gestureHandlerRootHOC(${Component.displayName || Component.name})`;\n\n    // @ts-ignore - hoistNonReactStatics uses old version of @types/react\n    (0, _hoistNonReactStatics.default)(Wrapper, Component);\n    return Wrapper;\n  }\n  var styles = _reactNative.StyleSheet.create({\n    container: {\n      flex: 1\n    }\n  });\n});", "lineCount": 42, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "React"], [7, 11, 1, 0], [7, 14, 1, 0, "_interopRequireWildcard"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_hoistNonReactStatics"], [9, 27, 3, 0], [9, 30, 3, 0, "_interopRequireDefault"], [9, 52, 3, 0], [9, 53, 3, 0, "require"], [9, 60, 3, 0], [9, 61, 3, 0, "_dependencyMap"], [9, 75, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_GestureHandlerRootView"], [10, 29, 4, 0], [10, 32, 4, 0, "_interopRequireDefault"], [10, 54, 4, 0], [10, 55, 4, 0, "require"], [10, 62, 4, 0], [10, 63, 4, 0, "_dependencyMap"], [10, 77, 4, 0], [11, 2, 4, 62], [11, 6, 4, 62, "_jsxDevRuntime"], [11, 20, 4, 62], [11, 23, 4, 62, "require"], [11, 30, 4, 62], [11, 31, 4, 62, "_dependencyMap"], [11, 45, 4, 62], [12, 2, 4, 62], [12, 6, 4, 62, "_jsxFileName"], [12, 18, 4, 62], [13, 2, 4, 62], [13, 11, 4, 62, "_interopRequireWildcard"], [13, 35, 4, 62, "e"], [13, 36, 4, 62], [13, 38, 4, 62, "t"], [13, 39, 4, 62], [13, 68, 4, 62, "WeakMap"], [13, 75, 4, 62], [13, 81, 4, 62, "r"], [13, 82, 4, 62], [13, 89, 4, 62, "WeakMap"], [13, 96, 4, 62], [13, 100, 4, 62, "n"], [13, 101, 4, 62], [13, 108, 4, 62, "WeakMap"], [13, 115, 4, 62], [13, 127, 4, 62, "_interopRequireWildcard"], [13, 150, 4, 62], [13, 162, 4, 62, "_interopRequireWildcard"], [13, 163, 4, 62, "e"], [13, 164, 4, 62], [13, 166, 4, 62, "t"], [13, 167, 4, 62], [13, 176, 4, 62, "t"], [13, 177, 4, 62], [13, 181, 4, 62, "e"], [13, 182, 4, 62], [13, 186, 4, 62, "e"], [13, 187, 4, 62], [13, 188, 4, 62, "__esModule"], [13, 198, 4, 62], [13, 207, 4, 62, "e"], [13, 208, 4, 62], [13, 214, 4, 62, "o"], [13, 215, 4, 62], [13, 217, 4, 62, "i"], [13, 218, 4, 62], [13, 220, 4, 62, "f"], [13, 221, 4, 62], [13, 226, 4, 62, "__proto__"], [13, 235, 4, 62], [13, 243, 4, 62, "default"], [13, 250, 4, 62], [13, 252, 4, 62, "e"], [13, 253, 4, 62], [13, 270, 4, 62, "e"], [13, 271, 4, 62], [13, 294, 4, 62, "e"], [13, 295, 4, 62], [13, 320, 4, 62, "e"], [13, 321, 4, 62], [13, 330, 4, 62, "f"], [13, 331, 4, 62], [13, 337, 4, 62, "o"], [13, 338, 4, 62], [13, 341, 4, 62, "t"], [13, 342, 4, 62], [13, 345, 4, 62, "n"], [13, 346, 4, 62], [13, 349, 4, 62, "r"], [13, 350, 4, 62], [13, 358, 4, 62, "o"], [13, 359, 4, 62], [13, 360, 4, 62, "has"], [13, 363, 4, 62], [13, 364, 4, 62, "e"], [13, 365, 4, 62], [13, 375, 4, 62, "o"], [13, 376, 4, 62], [13, 377, 4, 62, "get"], [13, 380, 4, 62], [13, 381, 4, 62, "e"], [13, 382, 4, 62], [13, 385, 4, 62, "o"], [13, 386, 4, 62], [13, 387, 4, 62, "set"], [13, 390, 4, 62], [13, 391, 4, 62, "e"], [13, 392, 4, 62], [13, 394, 4, 62, "f"], [13, 395, 4, 62], [13, 409, 4, 62, "_t"], [13, 411, 4, 62], [13, 415, 4, 62, "e"], [13, 416, 4, 62], [13, 432, 4, 62, "_t"], [13, 434, 4, 62], [13, 441, 4, 62, "hasOwnProperty"], [13, 455, 4, 62], [13, 456, 4, 62, "call"], [13, 460, 4, 62], [13, 461, 4, 62, "e"], [13, 462, 4, 62], [13, 464, 4, 62, "_t"], [13, 466, 4, 62], [13, 473, 4, 62, "i"], [13, 474, 4, 62], [13, 478, 4, 62, "o"], [13, 479, 4, 62], [13, 482, 4, 62, "Object"], [13, 488, 4, 62], [13, 489, 4, 62, "defineProperty"], [13, 503, 4, 62], [13, 508, 4, 62, "Object"], [13, 514, 4, 62], [13, 515, 4, 62, "getOwnPropertyDescriptor"], [13, 539, 4, 62], [13, 540, 4, 62, "e"], [13, 541, 4, 62], [13, 543, 4, 62, "_t"], [13, 545, 4, 62], [13, 552, 4, 62, "i"], [13, 553, 4, 62], [13, 554, 4, 62, "get"], [13, 557, 4, 62], [13, 561, 4, 62, "i"], [13, 562, 4, 62], [13, 563, 4, 62, "set"], [13, 566, 4, 62], [13, 570, 4, 62, "o"], [13, 571, 4, 62], [13, 572, 4, 62, "f"], [13, 573, 4, 62], [13, 575, 4, 62, "_t"], [13, 577, 4, 62], [13, 579, 4, 62, "i"], [13, 580, 4, 62], [13, 584, 4, 62, "f"], [13, 585, 4, 62], [13, 586, 4, 62, "_t"], [13, 588, 4, 62], [13, 592, 4, 62, "e"], [13, 593, 4, 62], [13, 594, 4, 62, "_t"], [13, 596, 4, 62], [13, 607, 4, 62, "f"], [13, 608, 4, 62], [13, 613, 4, 62, "e"], [13, 614, 4, 62], [13, 616, 4, 62, "t"], [13, 617, 4, 62], [14, 2, 6, 15], [14, 11, 6, 24, "gestureHandlerRootHOC"], [14, 32, 6, 45, "gestureHandlerRootHOC"], [14, 33, 7, 2, "Component"], [14, 42, 7, 35], [14, 44, 8, 2, "containerStyles"], [14, 59, 8, 40], [14, 61, 9, 26], [15, 4, 10, 2], [15, 13, 10, 11, "Wrapper"], [15, 20, 10, 18, "Wrapper"], [15, 21, 10, 19, "props"], [15, 26, 10, 27], [15, 28, 10, 29], [16, 6, 11, 4], [16, 26, 12, 6], [16, 30, 12, 6, "_jsxDevRuntime"], [16, 44, 12, 6], [16, 45, 12, 6, "jsxDEV"], [16, 51, 12, 6], [16, 53, 12, 7, "_GestureHandlerRootView"], [16, 76, 12, 7], [16, 77, 12, 7, "default"], [16, 84, 12, 29], [17, 8, 12, 30, "style"], [17, 13, 12, 35], [17, 15, 12, 37], [17, 16, 12, 38, "styles"], [17, 22, 12, 44], [17, 23, 12, 45, "container"], [17, 32, 12, 54], [17, 34, 12, 56, "containerStyles"], [17, 49, 12, 71], [17, 50, 12, 73], [18, 8, 12, 73, "children"], [18, 16, 12, 73], [18, 31, 13, 8], [18, 35, 13, 8, "_jsxDevRuntime"], [18, 49, 13, 8], [18, 50, 13, 8, "jsxDEV"], [18, 56, 13, 8], [18, 58, 13, 9, "Component"], [18, 67, 13, 18], [19, 10, 13, 18], [19, 13, 13, 23, "props"], [20, 8, 13, 28], [21, 10, 13, 28, "fileName"], [21, 18, 13, 28], [21, 20, 13, 28, "_jsxFileName"], [21, 32, 13, 28], [22, 10, 13, 28, "lineNumber"], [22, 20, 13, 28], [23, 10, 13, 28, "columnNumber"], [23, 22, 13, 28], [24, 8, 13, 28], [24, 15, 13, 31], [25, 6, 13, 32], [26, 8, 13, 32, "fileName"], [26, 16, 13, 32], [26, 18, 13, 32, "_jsxFileName"], [26, 30, 13, 32], [27, 8, 13, 32, "lineNumber"], [27, 18, 13, 32], [28, 8, 13, 32, "columnNumber"], [28, 20, 13, 32], [29, 6, 13, 32], [29, 13, 14, 30], [29, 14, 14, 31], [30, 4, 16, 2], [31, 4, 18, 2, "Wrapper"], [31, 11, 18, 9], [31, 12, 18, 10, "displayName"], [31, 23, 18, 21], [31, 26, 18, 24], [31, 51, 19, 4, "Component"], [31, 60, 19, 13], [31, 61, 19, 14, "displayName"], [31, 72, 19, 25], [31, 76, 19, 29, "Component"], [31, 85, 19, 38], [31, 86, 19, 39, "name"], [31, 90, 19, 43], [31, 93, 20, 5], [33, 4, 22, 2], [34, 4, 23, 2], [34, 8, 23, 2, "hoistNonReactStatics"], [34, 37, 23, 22], [34, 39, 23, 23, "Wrapper"], [34, 46, 23, 30], [34, 48, 23, 32, "Component"], [34, 57, 23, 41], [34, 58, 23, 42], [35, 4, 25, 2], [35, 11, 25, 9, "Wrapper"], [35, 18, 25, 16], [36, 2, 26, 0], [37, 2, 28, 0], [37, 6, 28, 6, "styles"], [37, 12, 28, 12], [37, 15, 28, 15, "StyleSheet"], [37, 38, 28, 25], [37, 39, 28, 26, "create"], [37, 45, 28, 32], [37, 46, 28, 33], [38, 4, 29, 2, "container"], [38, 13, 29, 11], [38, 15, 29, 13], [39, 6, 29, 15, "flex"], [39, 10, 29, 19], [39, 12, 29, 21], [40, 4, 29, 23], [41, 2, 30, 0], [41, 3, 30, 1], [41, 4, 30, 2], [42, 0, 30, 3], [42, 3]], "functionMap": {"names": ["<global>", "gestureHandlerRootHOC", "Wrapper"], "mappings": "AAA;eCK;ECI;GDM;CDU"}}, "type": "js/module"}]}