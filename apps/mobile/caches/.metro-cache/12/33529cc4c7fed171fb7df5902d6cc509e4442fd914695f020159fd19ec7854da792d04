{"dependencies": [{"name": "./bezier", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 217, "column": 20}, "end": {"line": 217, "column": 39}}], "key": "WdDb91kFRAGyP8FNeqQNQW4CqhM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var ease;\n  var Easing = {\n    step0(n) {\n      return n > 0 ? 1 : 0;\n    },\n    step1(n) {\n      return n >= 1 ? 1 : 0;\n    },\n    linear(t) {\n      return t;\n    },\n    ease(t) {\n      if (!ease) {\n        ease = Easing.bezier(0.42, 0, 1, 1);\n      }\n      return ease(t);\n    },\n    quad(t) {\n      return t * t;\n    },\n    cubic(t) {\n      return t * t * t;\n    },\n    poly(n) {\n      return t => Math.pow(t, n);\n    },\n    sin(t) {\n      return 1 - Math.cos(t * Math.PI / 2);\n    },\n    circle(t) {\n      return 1 - Math.sqrt(1 - t * t);\n    },\n    exp(t) {\n      return Math.pow(2, 10 * (t - 1));\n    },\n    elastic() {\n      var bounciness = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;\n      var p = bounciness * Math.PI;\n      return t => 1 - Math.pow(Math.cos(t * Math.PI / 2), 3) * Math.cos(t * p);\n    },\n    back() {\n      var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1.70158;\n      return t => t * t * ((s + 1) * t - s);\n    },\n    bounce(t) {\n      if (t < 1 / 2.75) {\n        return 7.5625 * t * t;\n      }\n      if (t < 2 / 2.75) {\n        var _t = t - 1.5 / 2.75;\n        return 7.5625 * _t * _t + 0.75;\n      }\n      if (t < 2.5 / 2.75) {\n        var _t2 = t - 2.25 / 2.75;\n        return 7.5625 * _t2 * _t2 + 0.9375;\n      }\n      var t2 = t - 2.625 / 2.75;\n      return 7.5625 * t2 * t2 + 0.984375;\n    },\n    bezier(x1, y1, x2, y2) {\n      var _bezier = require(_dependencyMap[0], \"./bezier\").default;\n      return _bezier(x1, y1, x2, y2);\n    },\n    in(easing) {\n      return easing;\n    },\n    out(easing) {\n      return t => 1 - easing(1 - t);\n    },\n    inOut(easing) {\n      return t => {\n        if (t < 0.5) {\n          return easing(t * 2) / 2;\n        }\n        return 1 - easing((1 - t) * 2) / 2;\n      };\n    }\n  };\n  var _default = exports.default = Easing;\n});", "lineCount": 87, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13, "Object"], [4, 8, 11, 13], [4, 9, 11, 13, "defineProperty"], [4, 23, 11, 13], [4, 24, 11, 13, "exports"], [4, 31, 11, 13], [5, 4, 11, 13, "value"], [5, 9, 11, 13], [6, 2, 11, 13], [7, 2, 11, 13, "exports"], [7, 9, 11, 13], [7, 10, 11, 13, "default"], [7, 17, 11, 13], [8, 2, 13, 0], [8, 6, 13, 4, "ease"], [8, 10, 13, 8], [9, 2, 60, 0], [9, 6, 60, 6, "Easing"], [9, 12, 60, 12], [9, 15, 60, 15], [10, 4, 64, 2, "step0"], [10, 9, 64, 7, "step0"], [10, 10, 64, 8, "n"], [10, 11, 64, 17], [10, 13, 64, 27], [11, 6, 65, 4], [11, 13, 65, 11, "n"], [11, 14, 65, 12], [11, 17, 65, 15], [11, 18, 65, 16], [11, 21, 65, 19], [11, 22, 65, 20], [11, 25, 65, 23], [11, 26, 65, 24], [12, 4, 66, 2], [12, 5, 66, 3], [13, 4, 71, 2, "step1"], [13, 9, 71, 7, "step1"], [13, 10, 71, 8, "n"], [13, 11, 71, 17], [13, 13, 71, 27], [14, 6, 72, 4], [14, 13, 72, 11, "n"], [14, 14, 72, 12], [14, 18, 72, 16], [14, 19, 72, 17], [14, 22, 72, 20], [14, 23, 72, 21], [14, 26, 72, 24], [14, 27, 72, 25], [15, 4, 73, 2], [15, 5, 73, 3], [16, 4, 81, 2, "linear"], [16, 10, 81, 8, "linear"], [16, 11, 81, 9, "t"], [16, 12, 81, 18], [16, 14, 81, 28], [17, 6, 82, 4], [17, 13, 82, 11, "t"], [17, 14, 82, 12], [18, 4, 83, 2], [18, 5, 83, 3], [19, 4, 91, 2, "ease"], [19, 8, 91, 6, "ease"], [19, 9, 91, 7, "t"], [19, 10, 91, 16], [19, 12, 91, 26], [20, 6, 92, 4], [20, 10, 92, 8], [20, 11, 92, 9, "ease"], [20, 15, 92, 13], [20, 17, 92, 15], [21, 8, 93, 6, "ease"], [21, 12, 93, 10], [21, 15, 93, 13, "Easing"], [21, 21, 93, 19], [21, 22, 93, 20, "bezier"], [21, 28, 93, 26], [21, 29, 93, 27], [21, 33, 93, 31], [21, 35, 93, 33], [21, 36, 93, 34], [21, 38, 93, 36], [21, 39, 93, 37], [21, 41, 93, 39], [21, 42, 93, 40], [21, 43, 93, 41], [22, 6, 94, 4], [23, 6, 95, 4], [23, 13, 95, 11, "ease"], [23, 17, 95, 15], [23, 18, 95, 16, "t"], [23, 19, 95, 17], [23, 20, 95, 18], [24, 4, 96, 2], [24, 5, 96, 3], [25, 4, 104, 2, "quad"], [25, 8, 104, 6, "quad"], [25, 9, 104, 7, "t"], [25, 10, 104, 16], [25, 12, 104, 26], [26, 6, 105, 4], [26, 13, 105, 11, "t"], [26, 14, 105, 12], [26, 17, 105, 15, "t"], [26, 18, 105, 16], [27, 4, 106, 2], [27, 5, 106, 3], [28, 4, 114, 2, "cubic"], [28, 9, 114, 7, "cubic"], [28, 10, 114, 8, "t"], [28, 11, 114, 17], [28, 13, 114, 27], [29, 6, 115, 4], [29, 13, 115, 11, "t"], [29, 14, 115, 12], [29, 17, 115, 15, "t"], [29, 18, 115, 16], [29, 21, 115, 19, "t"], [29, 22, 115, 20], [30, 4, 116, 2], [30, 5, 116, 3], [31, 4, 124, 2, "poly"], [31, 8, 124, 6, "poly"], [31, 9, 124, 7, "n"], [31, 10, 124, 16], [31, 12, 124, 41], [32, 6, 125, 4], [32, 13, 125, 12, "t"], [32, 14, 125, 21], [32, 18, 125, 26, "Math"], [32, 22, 125, 30], [32, 23, 125, 31, "pow"], [32, 26, 125, 34], [32, 27, 125, 35, "t"], [32, 28, 125, 36], [32, 30, 125, 38, "n"], [32, 31, 125, 39], [32, 32, 125, 40], [33, 4, 126, 2], [33, 5, 126, 3], [34, 4, 133, 2, "sin"], [34, 7, 133, 5, "sin"], [34, 8, 133, 6, "t"], [34, 9, 133, 15], [34, 11, 133, 25], [35, 6, 134, 4], [35, 13, 134, 11], [35, 14, 134, 12], [35, 17, 134, 15, "Math"], [35, 21, 134, 19], [35, 22, 134, 20, "cos"], [35, 25, 134, 23], [35, 26, 134, 25, "t"], [35, 27, 134, 26], [35, 30, 134, 29, "Math"], [35, 34, 134, 33], [35, 35, 134, 34, "PI"], [35, 37, 134, 36], [35, 40, 134, 40], [35, 41, 134, 41], [35, 42, 134, 42], [36, 4, 135, 2], [36, 5, 135, 3], [37, 4, 142, 2, "circle"], [37, 10, 142, 8, "circle"], [37, 11, 142, 9, "t"], [37, 12, 142, 18], [37, 14, 142, 28], [38, 6, 143, 4], [38, 13, 143, 11], [38, 14, 143, 12], [38, 17, 143, 15, "Math"], [38, 21, 143, 19], [38, 22, 143, 20, "sqrt"], [38, 26, 143, 24], [38, 27, 143, 25], [38, 28, 143, 26], [38, 31, 143, 29, "t"], [38, 32, 143, 30], [38, 35, 143, 33, "t"], [38, 36, 143, 34], [38, 37, 143, 35], [39, 4, 144, 2], [39, 5, 144, 3], [40, 4, 151, 2, "exp"], [40, 7, 151, 5, "exp"], [40, 8, 151, 6, "t"], [40, 9, 151, 15], [40, 11, 151, 25], [41, 6, 152, 4], [41, 13, 152, 11, "Math"], [41, 17, 152, 15], [41, 18, 152, 16, "pow"], [41, 21, 152, 19], [41, 22, 152, 20], [41, 23, 152, 21], [41, 25, 152, 23], [41, 27, 152, 25], [41, 31, 152, 29, "t"], [41, 32, 152, 30], [41, 35, 152, 33], [41, 36, 152, 34], [41, 37, 152, 35], [41, 38, 152, 36], [42, 4, 153, 2], [42, 5, 153, 3], [43, 4, 165, 2, "elastic"], [43, 11, 165, 9, "elastic"], [43, 12, 165, 9], [43, 14, 165, 57], [44, 6, 165, 57], [44, 10, 165, 10, "bounciness"], [44, 20, 165, 28], [44, 23, 165, 28, "arguments"], [44, 32, 165, 28], [44, 33, 165, 28, "length"], [44, 39, 165, 28], [44, 47, 165, 28, "arguments"], [44, 56, 165, 28], [44, 64, 165, 28, "undefined"], [44, 73, 165, 28], [44, 76, 165, 28, "arguments"], [44, 85, 165, 28], [44, 91, 165, 31], [44, 92, 165, 32], [45, 6, 166, 4], [45, 10, 166, 10, "p"], [45, 11, 166, 11], [45, 14, 166, 14, "bounciness"], [45, 24, 166, 24], [45, 27, 166, 27, "Math"], [45, 31, 166, 31], [45, 32, 166, 32, "PI"], [45, 34, 166, 34], [46, 6, 167, 4], [46, 13, 167, 11, "t"], [46, 14, 167, 12], [46, 18, 167, 16], [46, 19, 167, 17], [46, 22, 167, 20, "Math"], [46, 26, 167, 24], [46, 27, 167, 25, "pow"], [46, 30, 167, 28], [46, 31, 167, 29, "Math"], [46, 35, 167, 33], [46, 36, 167, 34, "cos"], [46, 39, 167, 37], [46, 40, 167, 39, "t"], [46, 41, 167, 40], [46, 44, 167, 43, "Math"], [46, 48, 167, 47], [46, 49, 167, 48, "PI"], [46, 51, 167, 50], [46, 54, 167, 54], [46, 55, 167, 55], [46, 56, 167, 56], [46, 58, 167, 58], [46, 59, 167, 59], [46, 60, 167, 60], [46, 63, 167, 63, "Math"], [46, 67, 167, 67], [46, 68, 167, 68, "cos"], [46, 71, 167, 71], [46, 72, 167, 72, "t"], [46, 73, 167, 73], [46, 76, 167, 76, "p"], [46, 77, 167, 77], [46, 78, 167, 78], [47, 4, 168, 2], [47, 5, 168, 3], [48, 4, 176, 2, "back"], [48, 8, 176, 6, "back"], [48, 9, 176, 6], [48, 11, 176, 51], [49, 6, 176, 51], [49, 10, 176, 7, "s"], [49, 11, 176, 16], [49, 14, 176, 16, "arguments"], [49, 23, 176, 16], [49, 24, 176, 16, "length"], [49, 30, 176, 16], [49, 38, 176, 16, "arguments"], [49, 47, 176, 16], [49, 55, 176, 16, "undefined"], [49, 64, 176, 16], [49, 67, 176, 16, "arguments"], [49, 76, 176, 16], [49, 82, 176, 19], [49, 89, 176, 26], [50, 6, 177, 4], [50, 13, 177, 11, "t"], [50, 14, 177, 12], [50, 18, 177, 16, "t"], [50, 19, 177, 17], [50, 22, 177, 20, "t"], [50, 23, 177, 21], [50, 27, 177, 25], [50, 28, 177, 26, "s"], [50, 29, 177, 27], [50, 32, 177, 30], [50, 33, 177, 31], [50, 37, 177, 35, "t"], [50, 38, 177, 36], [50, 41, 177, 39, "s"], [50, 42, 177, 40], [50, 43, 177, 41], [51, 4, 178, 2], [51, 5, 178, 3], [52, 4, 185, 2, "bounce"], [52, 10, 185, 8, "bounce"], [52, 11, 185, 9, "t"], [52, 12, 185, 18], [52, 14, 185, 28], [53, 6, 186, 4], [53, 10, 186, 8, "t"], [53, 11, 186, 9], [53, 14, 186, 12], [53, 15, 186, 13], [53, 18, 186, 16], [53, 22, 186, 20], [53, 24, 186, 22], [54, 8, 187, 6], [54, 15, 187, 13], [54, 21, 187, 19], [54, 24, 187, 22, "t"], [54, 25, 187, 23], [54, 28, 187, 26, "t"], [54, 29, 187, 27], [55, 6, 188, 4], [56, 6, 190, 4], [56, 10, 190, 8, "t"], [56, 11, 190, 9], [56, 14, 190, 12], [56, 15, 190, 13], [56, 18, 190, 16], [56, 22, 190, 20], [56, 24, 190, 22], [57, 8, 191, 6], [57, 12, 191, 12, "t2"], [57, 14, 191, 14], [57, 17, 191, 17, "t"], [57, 18, 191, 18], [57, 21, 191, 21], [57, 24, 191, 24], [57, 27, 191, 27], [57, 31, 191, 31], [58, 8, 192, 6], [58, 15, 192, 13], [58, 21, 192, 19], [58, 24, 192, 22, "t2"], [58, 26, 192, 24], [58, 29, 192, 27, "t2"], [58, 31, 192, 29], [58, 34, 192, 32], [58, 38, 192, 36], [59, 6, 193, 4], [60, 6, 195, 4], [60, 10, 195, 8, "t"], [60, 11, 195, 9], [60, 14, 195, 12], [60, 17, 195, 15], [60, 20, 195, 18], [60, 24, 195, 22], [60, 26, 195, 24], [61, 8, 196, 6], [61, 12, 196, 12, "t2"], [61, 15, 196, 14], [61, 18, 196, 17, "t"], [61, 19, 196, 18], [61, 22, 196, 21], [61, 26, 196, 25], [61, 29, 196, 28], [61, 33, 196, 32], [62, 8, 197, 6], [62, 15, 197, 13], [62, 21, 197, 19], [62, 24, 197, 22, "t2"], [62, 27, 197, 24], [62, 30, 197, 27, "t2"], [62, 33, 197, 29], [62, 36, 197, 32], [62, 42, 197, 38], [63, 6, 198, 4], [64, 6, 200, 4], [64, 10, 200, 10, "t2"], [64, 12, 200, 12], [64, 15, 200, 15, "t"], [64, 16, 200, 16], [64, 19, 200, 19], [64, 24, 200, 24], [64, 27, 200, 27], [64, 31, 200, 31], [65, 6, 201, 4], [65, 13, 201, 11], [65, 19, 201, 17], [65, 22, 201, 20, "t2"], [65, 24, 201, 22], [65, 27, 201, 25, "t2"], [65, 29, 201, 27], [65, 32, 201, 30], [65, 40, 201, 38], [66, 4, 202, 2], [66, 5, 202, 3], [67, 4, 211, 2, "bezier"], [67, 10, 211, 8, "bezier"], [67, 11, 212, 4, "x1"], [67, 13, 212, 14], [67, 15, 213, 4, "y1"], [67, 17, 213, 14], [67, 19, 214, 4, "x2"], [67, 21, 214, 14], [67, 23, 215, 4, "y2"], [67, 25, 215, 14], [67, 27, 216, 27], [68, 6, 217, 4], [68, 10, 217, 10, "_bezier"], [68, 17, 217, 17], [68, 20, 217, 20, "require"], [68, 27, 217, 27], [68, 28, 217, 27, "_dependencyMap"], [68, 42, 217, 27], [68, 57, 217, 38], [68, 58, 217, 39], [68, 59, 217, 40, "default"], [68, 66, 217, 47], [69, 6, 218, 4], [69, 13, 218, 11, "_bezier"], [69, 20, 218, 18], [69, 21, 218, 19, "x1"], [69, 23, 218, 21], [69, 25, 218, 23, "y1"], [69, 27, 218, 25], [69, 29, 218, 27, "x2"], [69, 31, 218, 29], [69, 33, 218, 31, "y2"], [69, 35, 218, 33], [69, 36, 218, 34], [70, 4, 219, 2], [70, 5, 219, 3], [71, 4, 224, 2, "in"], [71, 6, 224, 4, "in"], [71, 7, 224, 5, "easing"], [71, 13, 224, 34], [71, 15, 224, 59], [72, 6, 225, 4], [72, 13, 225, 11, "easing"], [72, 19, 225, 17], [73, 4, 226, 2], [73, 5, 226, 3], [74, 4, 231, 2, "out"], [74, 7, 231, 5, "out"], [74, 8, 231, 6, "easing"], [74, 14, 231, 35], [74, 16, 231, 60], [75, 6, 232, 4], [75, 13, 232, 11, "t"], [75, 14, 232, 12], [75, 18, 232, 16], [75, 19, 232, 17], [75, 22, 232, 20, "easing"], [75, 28, 232, 26], [75, 29, 232, 27], [75, 30, 232, 28], [75, 33, 232, 31, "t"], [75, 34, 232, 32], [75, 35, 232, 33], [76, 4, 233, 2], [76, 5, 233, 3], [77, 4, 240, 2, "inOut"], [77, 9, 240, 7, "inOut"], [77, 10, 240, 8, "easing"], [77, 16, 240, 37], [77, 18, 240, 62], [78, 6, 241, 4], [78, 13, 241, 11, "t"], [78, 14, 241, 12], [78, 18, 241, 16], [79, 8, 242, 6], [79, 12, 242, 10, "t"], [79, 13, 242, 11], [79, 16, 242, 14], [79, 19, 242, 17], [79, 21, 242, 19], [80, 10, 243, 8], [80, 17, 243, 15, "easing"], [80, 23, 243, 21], [80, 24, 243, 22, "t"], [80, 25, 243, 23], [80, 28, 243, 26], [80, 29, 243, 27], [80, 30, 243, 28], [80, 33, 243, 31], [80, 34, 243, 32], [81, 8, 244, 6], [82, 8, 245, 6], [82, 15, 245, 13], [82, 16, 245, 14], [82, 19, 245, 17, "easing"], [82, 25, 245, 23], [82, 26, 245, 24], [82, 27, 245, 25], [82, 28, 245, 26], [82, 31, 245, 29, "t"], [82, 32, 245, 30], [82, 36, 245, 34], [82, 37, 245, 35], [82, 38, 245, 36], [82, 41, 245, 39], [82, 42, 245, 40], [83, 6, 246, 4], [83, 7, 246, 5], [84, 4, 247, 2], [85, 2, 248, 0], [85, 3, 248, 1], [86, 2, 248, 2], [86, 6, 248, 2, "_default"], [86, 14, 248, 2], [86, 17, 248, 2, "exports"], [86, 24, 248, 2], [86, 25, 248, 2, "default"], [86, 32, 248, 2], [86, 35, 250, 15, "Easing"], [86, 41, 250, 21], [87, 0, 250, 21], [87, 3]], "functionMap": {"names": ["<global>", "step0", "step1", "linear", "ease", "quad", "cubic", "poly", "<anonymous>", "sin", "circle", "exp", "elastic", "back", "bounce", "bezier", "_in", "out", "inOut"], "mappings": "AAA;EC+D;GDE;EEK;GFE;EGQ;GHE;EIQ;GJK;EKQ;GLE;EMQ;GNE;EOQ;WCC,6BD;GPC;ESO;GTE;EUO;GVE;EWO;GXE;EYY;WJE,mEI;GZC;EaQ;WLC,8BK;GbC;EcO;GdiB;EeS;GfQ;EgBK;GhBE;EiBK;WTC,sBS;GjBC;EkBO;WVC;KUK;GlBC"}}, "type": "js/module"}]}