{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./lib/src/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 46}, "end": {"line": 2, "column": 51, "index": 97}}], "key": "LtAM4hRsOiPg7okd6WdhwoyTEpc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _index = _interopRequireDefault(require(_dependencyMap[1], \"./lib/src/index.js\"));\n  // eslint-disable-next-line import/extensions\n  var _default = exports.default = _index.default;\n});", "lineCount": 10, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_index"], [7, 12, 2, 0], [7, 15, 2, 0, "_interopRequireDefault"], [7, 37, 2, 0], [7, 38, 2, 0, "require"], [7, 45, 2, 0], [7, 46, 2, 0, "_dependencyMap"], [7, 60, 2, 0], [8, 2, 1, 0], [9, 2, 1, 0], [9, 6, 1, 0, "_default"], [9, 14, 1, 0], [9, 17, 1, 0, "exports"], [9, 24, 1, 0], [9, 25, 1, 0, "default"], [9, 32, 1, 0], [9, 35, 4, 15, "useLatestCallback"], [9, 49, 4, 32], [10, 0, 4, 32], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}