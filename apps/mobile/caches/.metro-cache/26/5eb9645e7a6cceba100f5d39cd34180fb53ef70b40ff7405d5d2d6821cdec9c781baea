{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 252}, "end": {"line": 4, "column": 36, "index": 288}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "../constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 289}, "end": {"line": 5, "column": 50, "index": 339}}], "key": "eTOOXVNPpMK2U8dOAmBWjbEJ4yE=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 340}, "end": {"line": 6, "column": 46, "index": 386}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/Platform\"));\n  var _State = require(_dependencyMap[2], \"../../State\");\n  var _constants = require(_dependencyMap[3], \"../constants\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[4], \"./GestureHandler\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class NativeViewGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"buttonRole\", void 0);\n      _defineProperty(this, \"shouldActivateOnStart\", false);\n      _defineProperty(this, \"disallowInterruption\", false);\n      _defineProperty(this, \"startX\", 0);\n      _defineProperty(this, \"startY\", 0);\n      _defineProperty(this, \"minDistSq\", _constants.DEFAULT_TOUCH_SLOP * _constants.DEFAULT_TOUCH_SLOP);\n    }\n    init(ref, propsRef) {\n      super.init(ref, propsRef);\n      this.shouldCancelWhenOutside = true;\n      if (_Platform.default.OS !== 'web') {\n        return;\n      }\n      const view = this.delegate.view;\n      this.restoreViewStyles(view);\n      this.buttonRole = view.getAttribute('role') === 'button';\n    }\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      super.updateGestureConfig({\n        enabled: enabled,\n        ...props\n      });\n      if (this.config.shouldActivateOnStart !== undefined) {\n        this.shouldActivateOnStart = this.config.shouldActivateOnStart;\n      }\n      if (this.config.disallowInterruption !== undefined) {\n        this.disallowInterruption = this.config.disallowInterruption;\n      }\n      const view = this.delegate.view;\n      this.restoreViewStyles(view);\n    }\n    restoreViewStyles(view) {\n      if (!view) {\n        return;\n      }\n      view.style['touchAction'] = 'auto'; // @ts-ignore Turns on defualt touch behavior on Safari\n\n      view.style['WebkitTouchCallout'] = 'auto';\n    }\n    onPointerDown(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerDown(event);\n      this.newPointerAction();\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      this.tracker.addToTracker(event);\n      super.onPointerAdd(event);\n      this.newPointerAction();\n    }\n    newPointerAction() {\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      this.startX = lastCoords.x;\n      this.startY = lastCoords.y;\n      if (this.state !== _State.State.UNDETERMINED) {\n        return;\n      }\n      this.begin();\n      const view = this.delegate.view;\n      const isRNGHText = view.hasAttribute('rnghtext');\n      if (this.buttonRole || isRNGHText) {\n        this.activate();\n      }\n    }\n    onPointerMove(event) {\n      this.tracker.track(event);\n      const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n      const dx = this.startX - lastCoords.x;\n      const dy = this.startY - lastCoords.y;\n      const distSq = dx * dx + dy * dy;\n      if (distSq >= this.minDistSq) {\n        if (this.buttonRole && this.state === _State.State.ACTIVE) {\n          this.cancel();\n        } else if (!this.buttonRole && this.state === _State.State.BEGAN) {\n          this.activate();\n        }\n      }\n    }\n    onPointerLeave() {\n      if (this.state === _State.State.BEGAN || this.state === _State.State.ACTIVE) {\n        this.cancel();\n      }\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      this.onUp(event);\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.onUp(event);\n    }\n    onUp(event) {\n      this.tracker.removeFromTracker(event.pointerId);\n      if (this.tracker.trackedPointersCount === 0) {\n        if (this.state === _State.State.ACTIVE) {\n          this.end();\n        } else {\n          this.fail();\n        }\n      }\n    }\n    shouldRecognizeSimultaneously(handler) {\n      if (super.shouldRecognizeSimultaneously(handler)) {\n        return true;\n      }\n      if (handler instanceof NativeViewGestureHandler && handler.state === _State.State.ACTIVE && handler.disallowsInterruption()) {\n        return false;\n      }\n      const canBeInterrupted = !this.disallowInterruption;\n      if (this.state === _State.State.ACTIVE && handler.state === _State.State.ACTIVE && canBeInterrupted) {\n        return false;\n      }\n      return this.state === _State.State.ACTIVE && canBeInterrupted && handler.handlerTag > 0;\n    }\n    shouldBeCancelledByOther(_handler) {\n      return !this.disallowInterruption;\n    }\n    disallowsInterruption() {\n      return this.disallowInterruption;\n    }\n    isButton() {\n      return this.buttonRole;\n    }\n  }\n  exports.default = NativeViewGestureHandler;\n});", "lineCount": 155, "map": [[8, 2, 4, 0], [8, 6, 4, 0, "_State"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_constants"], [9, 16, 5, 0], [9, 19, 5, 0, "require"], [9, 26, 5, 0], [9, 27, 5, 0, "_dependencyMap"], [9, 41, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_Gesture<PERSON><PERSON>ler"], [10, 21, 6, 0], [10, 24, 6, 0, "_interopRequireDefault"], [10, 46, 6, 0], [10, 47, 6, 0, "require"], [10, 54, 6, 0], [10, 55, 6, 0, "_dependencyMap"], [10, 69, 6, 0], [11, 2, 1, 0], [11, 11, 1, 9, "_defineProperty"], [11, 26, 1, 24, "_defineProperty"], [11, 27, 1, 25, "obj"], [11, 30, 1, 28], [11, 32, 1, 30, "key"], [11, 35, 1, 33], [11, 37, 1, 35, "value"], [11, 42, 1, 40], [11, 44, 1, 42], [12, 4, 1, 44], [12, 8, 1, 48, "key"], [12, 11, 1, 51], [12, 15, 1, 55, "obj"], [12, 18, 1, 58], [12, 20, 1, 60], [13, 6, 1, 62, "Object"], [13, 12, 1, 68], [13, 13, 1, 69, "defineProperty"], [13, 27, 1, 83], [13, 28, 1, 84, "obj"], [13, 31, 1, 87], [13, 33, 1, 89, "key"], [13, 36, 1, 92], [13, 38, 1, 94], [14, 8, 1, 96, "value"], [14, 13, 1, 101], [14, 15, 1, 103, "value"], [14, 20, 1, 108], [15, 8, 1, 110, "enumerable"], [15, 18, 1, 120], [15, 20, 1, 122], [15, 24, 1, 126], [16, 8, 1, 128, "configurable"], [16, 20, 1, 140], [16, 22, 1, 142], [16, 26, 1, 146], [17, 8, 1, 148, "writable"], [17, 16, 1, 156], [17, 18, 1, 158], [18, 6, 1, 163], [18, 7, 1, 164], [18, 8, 1, 165], [19, 4, 1, 167], [19, 5, 1, 168], [19, 11, 1, 174], [20, 6, 1, 176, "obj"], [20, 9, 1, 179], [20, 10, 1, 180, "key"], [20, 13, 1, 183], [20, 14, 1, 184], [20, 17, 1, 187, "value"], [20, 22, 1, 192], [21, 4, 1, 194], [22, 4, 1, 196], [22, 11, 1, 203, "obj"], [22, 14, 1, 206], [23, 2, 1, 208], [24, 2, 7, 15], [24, 8, 7, 21, "NativeViewGestureHandler"], [24, 32, 7, 45], [24, 41, 7, 54, "Gesture<PERSON>andler"], [24, 64, 7, 68], [24, 65, 7, 69], [25, 4, 8, 2, "constructor"], [25, 15, 8, 13, "constructor"], [25, 16, 8, 14], [25, 19, 8, 17, "args"], [25, 23, 8, 21], [25, 25, 8, 23], [26, 6, 9, 4], [26, 11, 9, 9], [26, 12, 9, 10], [26, 15, 9, 13, "args"], [26, 19, 9, 17], [26, 20, 9, 18], [27, 6, 11, 4, "_defineProperty"], [27, 21, 11, 19], [27, 22, 11, 20], [27, 26, 11, 24], [27, 28, 11, 26], [27, 40, 11, 38], [27, 42, 11, 40], [27, 47, 11, 45], [27, 48, 11, 46], [27, 49, 11, 47], [28, 6, 13, 4, "_defineProperty"], [28, 21, 13, 19], [28, 22, 13, 20], [28, 26, 13, 24], [28, 28, 13, 26], [28, 51, 13, 49], [28, 53, 13, 51], [28, 58, 13, 56], [28, 59, 13, 57], [29, 6, 15, 4, "_defineProperty"], [29, 21, 15, 19], [29, 22, 15, 20], [29, 26, 15, 24], [29, 28, 15, 26], [29, 50, 15, 48], [29, 52, 15, 50], [29, 57, 15, 55], [29, 58, 15, 56], [30, 6, 17, 4, "_defineProperty"], [30, 21, 17, 19], [30, 22, 17, 20], [30, 26, 17, 24], [30, 28, 17, 26], [30, 36, 17, 34], [30, 38, 17, 36], [30, 39, 17, 37], [30, 40, 17, 38], [31, 6, 19, 4, "_defineProperty"], [31, 21, 19, 19], [31, 22, 19, 20], [31, 26, 19, 24], [31, 28, 19, 26], [31, 36, 19, 34], [31, 38, 19, 36], [31, 39, 19, 37], [31, 40, 19, 38], [32, 6, 21, 4, "_defineProperty"], [32, 21, 21, 19], [32, 22, 21, 20], [32, 26, 21, 24], [32, 28, 21, 26], [32, 39, 21, 37], [32, 41, 21, 39, "DEFAULT_TOUCH_SLOP"], [32, 70, 21, 57], [32, 73, 21, 60, "DEFAULT_TOUCH_SLOP"], [32, 102, 21, 78], [32, 103, 21, 79], [33, 4, 22, 2], [34, 4, 24, 2, "init"], [34, 8, 24, 6, "init"], [34, 9, 24, 7, "ref"], [34, 12, 24, 10], [34, 14, 24, 12, "propsRef"], [34, 22, 24, 20], [34, 24, 24, 22], [35, 6, 25, 4], [35, 11, 25, 9], [35, 12, 25, 10, "init"], [35, 16, 25, 14], [35, 17, 25, 15, "ref"], [35, 20, 25, 18], [35, 22, 25, 20, "propsRef"], [35, 30, 25, 28], [35, 31, 25, 29], [36, 6, 26, 4], [36, 10, 26, 8], [36, 11, 26, 9, "shouldCancelWhenOutside"], [36, 34, 26, 32], [36, 37, 26, 35], [36, 41, 26, 39], [37, 6, 28, 4], [37, 10, 28, 8, "Platform"], [37, 27, 28, 16], [37, 28, 28, 17, "OS"], [37, 30, 28, 19], [37, 35, 28, 24], [37, 40, 28, 29], [37, 42, 28, 31], [38, 8, 29, 6], [39, 6, 30, 4], [40, 6, 32, 4], [40, 12, 32, 10, "view"], [40, 16, 32, 14], [40, 19, 32, 17], [40, 23, 32, 21], [40, 24, 32, 22, "delegate"], [40, 32, 32, 30], [40, 33, 32, 31, "view"], [40, 37, 32, 35], [41, 6, 33, 4], [41, 10, 33, 8], [41, 11, 33, 9, "restoreViewStyles"], [41, 28, 33, 26], [41, 29, 33, 27, "view"], [41, 33, 33, 31], [41, 34, 33, 32], [42, 6, 34, 4], [42, 10, 34, 8], [42, 11, 34, 9, "buttonRole"], [42, 21, 34, 19], [42, 24, 34, 22, "view"], [42, 28, 34, 26], [42, 29, 34, 27, "getAttribute"], [42, 41, 34, 39], [42, 42, 34, 40], [42, 48, 34, 46], [42, 49, 34, 47], [42, 54, 34, 52], [42, 62, 34, 60], [43, 4, 35, 2], [44, 4, 37, 2, "updateGestureConfig"], [44, 23, 37, 21, "updateGestureConfig"], [44, 24, 37, 22], [45, 6, 38, 4, "enabled"], [45, 13, 38, 11], [45, 16, 38, 14], [45, 20, 38, 18], [46, 6, 39, 4], [46, 9, 39, 7, "props"], [47, 4, 40, 2], [47, 5, 40, 3], [47, 7, 40, 5], [48, 6, 41, 4], [48, 11, 41, 9], [48, 12, 41, 10, "updateGestureConfig"], [48, 31, 41, 29], [48, 32, 41, 30], [49, 8, 42, 6, "enabled"], [49, 15, 42, 13], [49, 17, 42, 15, "enabled"], [49, 24, 42, 22], [50, 8, 43, 6], [50, 11, 43, 9, "props"], [51, 6, 44, 4], [51, 7, 44, 5], [51, 8, 44, 6], [52, 6, 46, 4], [52, 10, 46, 8], [52, 14, 46, 12], [52, 15, 46, 13, "config"], [52, 21, 46, 19], [52, 22, 46, 20, "shouldActivateOnStart"], [52, 43, 46, 41], [52, 48, 46, 46, "undefined"], [52, 57, 46, 55], [52, 59, 46, 57], [53, 8, 47, 6], [53, 12, 47, 10], [53, 13, 47, 11, "shouldActivateOnStart"], [53, 34, 47, 32], [53, 37, 47, 35], [53, 41, 47, 39], [53, 42, 47, 40, "config"], [53, 48, 47, 46], [53, 49, 47, 47, "shouldActivateOnStart"], [53, 70, 47, 68], [54, 6, 48, 4], [55, 6, 50, 4], [55, 10, 50, 8], [55, 14, 50, 12], [55, 15, 50, 13, "config"], [55, 21, 50, 19], [55, 22, 50, 20, "disallowInterruption"], [55, 42, 50, 40], [55, 47, 50, 45, "undefined"], [55, 56, 50, 54], [55, 58, 50, 56], [56, 8, 51, 6], [56, 12, 51, 10], [56, 13, 51, 11, "disallowInterruption"], [56, 33, 51, 31], [56, 36, 51, 34], [56, 40, 51, 38], [56, 41, 51, 39, "config"], [56, 47, 51, 45], [56, 48, 51, 46, "disallowInterruption"], [56, 68, 51, 66], [57, 6, 52, 4], [58, 6, 54, 4], [58, 12, 54, 10, "view"], [58, 16, 54, 14], [58, 19, 54, 17], [58, 23, 54, 21], [58, 24, 54, 22, "delegate"], [58, 32, 54, 30], [58, 33, 54, 31, "view"], [58, 37, 54, 35], [59, 6, 55, 4], [59, 10, 55, 8], [59, 11, 55, 9, "restoreViewStyles"], [59, 28, 55, 26], [59, 29, 55, 27, "view"], [59, 33, 55, 31], [59, 34, 55, 32], [60, 4, 56, 2], [61, 4, 58, 2, "restoreViewStyles"], [61, 21, 58, 19, "restoreViewStyles"], [61, 22, 58, 20, "view"], [61, 26, 58, 24], [61, 28, 58, 26], [62, 6, 59, 4], [62, 10, 59, 8], [62, 11, 59, 9, "view"], [62, 15, 59, 13], [62, 17, 59, 15], [63, 8, 60, 6], [64, 6, 61, 4], [65, 6, 63, 4, "view"], [65, 10, 63, 8], [65, 11, 63, 9, "style"], [65, 16, 63, 14], [65, 17, 63, 15], [65, 30, 63, 28], [65, 31, 63, 29], [65, 34, 63, 32], [65, 40, 63, 38], [65, 41, 63, 39], [65, 42, 63, 40], [67, 6, 65, 4, "view"], [67, 10, 65, 8], [67, 11, 65, 9, "style"], [67, 16, 65, 14], [67, 17, 65, 15], [67, 37, 65, 35], [67, 38, 65, 36], [67, 41, 65, 39], [67, 47, 65, 45], [68, 4, 66, 2], [69, 4, 68, 2, "onPointerDown"], [69, 17, 68, 15, "onPointerDown"], [69, 18, 68, 16, "event"], [69, 23, 68, 21], [69, 25, 68, 23], [70, 6, 69, 4], [70, 10, 69, 8], [70, 11, 69, 9, "tracker"], [70, 18, 69, 16], [70, 19, 69, 17, "addToTracker"], [70, 31, 69, 29], [70, 32, 69, 30, "event"], [70, 37, 69, 35], [70, 38, 69, 36], [71, 6, 70, 4], [71, 11, 70, 9], [71, 12, 70, 10, "onPointerDown"], [71, 25, 70, 23], [71, 26, 70, 24, "event"], [71, 31, 70, 29], [71, 32, 70, 30], [72, 6, 71, 4], [72, 10, 71, 8], [72, 11, 71, 9, "newPointerAction"], [72, 27, 71, 25], [72, 28, 71, 26], [72, 29, 71, 27], [73, 6, 72, 4], [73, 10, 72, 8], [73, 11, 72, 9, "tryToSendTouchEvent"], [73, 30, 72, 28], [73, 31, 72, 29, "event"], [73, 36, 72, 34], [73, 37, 72, 35], [74, 4, 73, 2], [75, 4, 75, 2, "onPointerAdd"], [75, 16, 75, 14, "onPointerAdd"], [75, 17, 75, 15, "event"], [75, 22, 75, 20], [75, 24, 75, 22], [76, 6, 76, 4], [76, 10, 76, 8], [76, 11, 76, 9, "tracker"], [76, 18, 76, 16], [76, 19, 76, 17, "addToTracker"], [76, 31, 76, 29], [76, 32, 76, 30, "event"], [76, 37, 76, 35], [76, 38, 76, 36], [77, 6, 77, 4], [77, 11, 77, 9], [77, 12, 77, 10, "onPointerAdd"], [77, 24, 77, 22], [77, 25, 77, 23, "event"], [77, 30, 77, 28], [77, 31, 77, 29], [78, 6, 78, 4], [78, 10, 78, 8], [78, 11, 78, 9, "newPointerAction"], [78, 27, 78, 25], [78, 28, 78, 26], [78, 29, 78, 27], [79, 4, 79, 2], [80, 4, 81, 2, "newPointerAction"], [80, 20, 81, 18, "newPointerAction"], [80, 21, 81, 18], [80, 23, 81, 21], [81, 6, 82, 4], [81, 12, 82, 10, "lastCoords"], [81, 22, 82, 20], [81, 25, 82, 23], [81, 29, 82, 27], [81, 30, 82, 28, "tracker"], [81, 37, 82, 35], [81, 38, 82, 36, "getAbsoluteCoordsAverage"], [81, 62, 82, 60], [81, 63, 82, 61], [81, 64, 82, 62], [82, 6, 83, 4], [82, 10, 83, 8], [82, 11, 83, 9, "startX"], [82, 17, 83, 15], [82, 20, 83, 18, "lastCoords"], [82, 30, 83, 28], [82, 31, 83, 29, "x"], [82, 32, 83, 30], [83, 6, 84, 4], [83, 10, 84, 8], [83, 11, 84, 9, "startY"], [83, 17, 84, 15], [83, 20, 84, 18, "lastCoords"], [83, 30, 84, 28], [83, 31, 84, 29, "y"], [83, 32, 84, 30], [84, 6, 86, 4], [84, 10, 86, 8], [84, 14, 86, 12], [84, 15, 86, 13, "state"], [84, 20, 86, 18], [84, 25, 86, 23, "State"], [84, 37, 86, 28], [84, 38, 86, 29, "UNDETERMINED"], [84, 50, 86, 41], [84, 52, 86, 43], [85, 8, 87, 6], [86, 6, 88, 4], [87, 6, 90, 4], [87, 10, 90, 8], [87, 11, 90, 9, "begin"], [87, 16, 90, 14], [87, 17, 90, 15], [87, 18, 90, 16], [88, 6, 91, 4], [88, 12, 91, 10, "view"], [88, 16, 91, 14], [88, 19, 91, 17], [88, 23, 91, 21], [88, 24, 91, 22, "delegate"], [88, 32, 91, 30], [88, 33, 91, 31, "view"], [88, 37, 91, 35], [89, 6, 92, 4], [89, 12, 92, 10, "isRNGHText"], [89, 22, 92, 20], [89, 25, 92, 23, "view"], [89, 29, 92, 27], [89, 30, 92, 28, "hasAttribute"], [89, 42, 92, 40], [89, 43, 92, 41], [89, 53, 92, 51], [89, 54, 92, 52], [90, 6, 94, 4], [90, 10, 94, 8], [90, 14, 94, 12], [90, 15, 94, 13, "buttonRole"], [90, 25, 94, 23], [90, 29, 94, 27, "isRNGHText"], [90, 39, 94, 37], [90, 41, 94, 39], [91, 8, 95, 6], [91, 12, 95, 10], [91, 13, 95, 11, "activate"], [91, 21, 95, 19], [91, 22, 95, 20], [91, 23, 95, 21], [92, 6, 96, 4], [93, 4, 97, 2], [94, 4, 99, 2, "onPointerMove"], [94, 17, 99, 15, "onPointerMove"], [94, 18, 99, 16, "event"], [94, 23, 99, 21], [94, 25, 99, 23], [95, 6, 100, 4], [95, 10, 100, 8], [95, 11, 100, 9, "tracker"], [95, 18, 100, 16], [95, 19, 100, 17, "track"], [95, 24, 100, 22], [95, 25, 100, 23, "event"], [95, 30, 100, 28], [95, 31, 100, 29], [96, 6, 101, 4], [96, 12, 101, 10, "lastCoords"], [96, 22, 101, 20], [96, 25, 101, 23], [96, 29, 101, 27], [96, 30, 101, 28, "tracker"], [96, 37, 101, 35], [96, 38, 101, 36, "getAbsoluteCoordsAverage"], [96, 62, 101, 60], [96, 63, 101, 61], [96, 64, 101, 62], [97, 6, 102, 4], [97, 12, 102, 10, "dx"], [97, 14, 102, 12], [97, 17, 102, 15], [97, 21, 102, 19], [97, 22, 102, 20, "startX"], [97, 28, 102, 26], [97, 31, 102, 29, "lastCoords"], [97, 41, 102, 39], [97, 42, 102, 40, "x"], [97, 43, 102, 41], [98, 6, 103, 4], [98, 12, 103, 10, "dy"], [98, 14, 103, 12], [98, 17, 103, 15], [98, 21, 103, 19], [98, 22, 103, 20, "startY"], [98, 28, 103, 26], [98, 31, 103, 29, "lastCoords"], [98, 41, 103, 39], [98, 42, 103, 40, "y"], [98, 43, 103, 41], [99, 6, 104, 4], [99, 12, 104, 10, "distSq"], [99, 18, 104, 16], [99, 21, 104, 19, "dx"], [99, 23, 104, 21], [99, 26, 104, 24, "dx"], [99, 28, 104, 26], [99, 31, 104, 29, "dy"], [99, 33, 104, 31], [99, 36, 104, 34, "dy"], [99, 38, 104, 36], [100, 6, 106, 4], [100, 10, 106, 8, "distSq"], [100, 16, 106, 14], [100, 20, 106, 18], [100, 24, 106, 22], [100, 25, 106, 23, "minDistSq"], [100, 34, 106, 32], [100, 36, 106, 34], [101, 8, 107, 6], [101, 12, 107, 10], [101, 16, 107, 14], [101, 17, 107, 15, "buttonRole"], [101, 27, 107, 25], [101, 31, 107, 29], [101, 35, 107, 33], [101, 36, 107, 34, "state"], [101, 41, 107, 39], [101, 46, 107, 44, "State"], [101, 58, 107, 49], [101, 59, 107, 50, "ACTIVE"], [101, 65, 107, 56], [101, 67, 107, 58], [102, 10, 108, 8], [102, 14, 108, 12], [102, 15, 108, 13, "cancel"], [102, 21, 108, 19], [102, 22, 108, 20], [102, 23, 108, 21], [103, 8, 109, 6], [103, 9, 109, 7], [103, 15, 109, 13], [103, 19, 109, 17], [103, 20, 109, 18], [103, 24, 109, 22], [103, 25, 109, 23, "buttonRole"], [103, 35, 109, 33], [103, 39, 109, 37], [103, 43, 109, 41], [103, 44, 109, 42, "state"], [103, 49, 109, 47], [103, 54, 109, 52, "State"], [103, 66, 109, 57], [103, 67, 109, 58, "BEGAN"], [103, 72, 109, 63], [103, 74, 109, 65], [104, 10, 110, 8], [104, 14, 110, 12], [104, 15, 110, 13, "activate"], [104, 23, 110, 21], [104, 24, 110, 22], [104, 25, 110, 23], [105, 8, 111, 6], [106, 6, 112, 4], [107, 4, 113, 2], [108, 4, 115, 2, "onPointerLeave"], [108, 18, 115, 16, "onPointerLeave"], [108, 19, 115, 16], [108, 21, 115, 19], [109, 6, 116, 4], [109, 10, 116, 8], [109, 14, 116, 12], [109, 15, 116, 13, "state"], [109, 20, 116, 18], [109, 25, 116, 23, "State"], [109, 37, 116, 28], [109, 38, 116, 29, "BEGAN"], [109, 43, 116, 34], [109, 47, 116, 38], [109, 51, 116, 42], [109, 52, 116, 43, "state"], [109, 57, 116, 48], [109, 62, 116, 53, "State"], [109, 74, 116, 58], [109, 75, 116, 59, "ACTIVE"], [109, 81, 116, 65], [109, 83, 116, 67], [110, 8, 117, 6], [110, 12, 117, 10], [110, 13, 117, 11, "cancel"], [110, 19, 117, 17], [110, 20, 117, 18], [110, 21, 117, 19], [111, 6, 118, 4], [112, 4, 119, 2], [113, 4, 121, 2, "onPointerUp"], [113, 15, 121, 13, "onPointerUp"], [113, 16, 121, 14, "event"], [113, 21, 121, 19], [113, 23, 121, 21], [114, 6, 122, 4], [114, 11, 122, 9], [114, 12, 122, 10, "onPointerUp"], [114, 23, 122, 21], [114, 24, 122, 22, "event"], [114, 29, 122, 27], [114, 30, 122, 28], [115, 6, 123, 4], [115, 10, 123, 8], [115, 11, 123, 9, "onUp"], [115, 15, 123, 13], [115, 16, 123, 14, "event"], [115, 21, 123, 19], [115, 22, 123, 20], [116, 4, 124, 2], [117, 4, 126, 2, "onPointerRemove"], [117, 19, 126, 17, "onPointerRemove"], [117, 20, 126, 18, "event"], [117, 25, 126, 23], [117, 27, 126, 25], [118, 6, 127, 4], [118, 11, 127, 9], [118, 12, 127, 10, "onPointerRemove"], [118, 27, 127, 25], [118, 28, 127, 26, "event"], [118, 33, 127, 31], [118, 34, 127, 32], [119, 6, 128, 4], [119, 10, 128, 8], [119, 11, 128, 9, "onUp"], [119, 15, 128, 13], [119, 16, 128, 14, "event"], [119, 21, 128, 19], [119, 22, 128, 20], [120, 4, 129, 2], [121, 4, 131, 2, "onUp"], [121, 8, 131, 6, "onUp"], [121, 9, 131, 7, "event"], [121, 14, 131, 12], [121, 16, 131, 14], [122, 6, 132, 4], [122, 10, 132, 8], [122, 11, 132, 9, "tracker"], [122, 18, 132, 16], [122, 19, 132, 17, "removeFromTracker"], [122, 36, 132, 34], [122, 37, 132, 35, "event"], [122, 42, 132, 40], [122, 43, 132, 41, "pointerId"], [122, 52, 132, 50], [122, 53, 132, 51], [123, 6, 134, 4], [123, 10, 134, 8], [123, 14, 134, 12], [123, 15, 134, 13, "tracker"], [123, 22, 134, 20], [123, 23, 134, 21, "trackedPointersCount"], [123, 43, 134, 41], [123, 48, 134, 46], [123, 49, 134, 47], [123, 51, 134, 49], [124, 8, 135, 6], [124, 12, 135, 10], [124, 16, 135, 14], [124, 17, 135, 15, "state"], [124, 22, 135, 20], [124, 27, 135, 25, "State"], [124, 39, 135, 30], [124, 40, 135, 31, "ACTIVE"], [124, 46, 135, 37], [124, 48, 135, 39], [125, 10, 136, 8], [125, 14, 136, 12], [125, 15, 136, 13, "end"], [125, 18, 136, 16], [125, 19, 136, 17], [125, 20, 136, 18], [126, 8, 137, 6], [126, 9, 137, 7], [126, 15, 137, 13], [127, 10, 138, 8], [127, 14, 138, 12], [127, 15, 138, 13, "fail"], [127, 19, 138, 17], [127, 20, 138, 18], [127, 21, 138, 19], [128, 8, 139, 6], [129, 6, 140, 4], [130, 4, 141, 2], [131, 4, 143, 2, "shouldRecognizeSimultaneously"], [131, 33, 143, 31, "shouldRecognizeSimultaneously"], [131, 34, 143, 32, "handler"], [131, 41, 143, 39], [131, 43, 143, 41], [132, 6, 144, 4], [132, 10, 144, 8], [132, 15, 144, 13], [132, 16, 144, 14, "shouldRecognizeSimultaneously"], [132, 45, 144, 43], [132, 46, 144, 44, "handler"], [132, 53, 144, 51], [132, 54, 144, 52], [132, 56, 144, 54], [133, 8, 145, 6], [133, 15, 145, 13], [133, 19, 145, 17], [134, 6, 146, 4], [135, 6, 148, 4], [135, 10, 148, 8, "handler"], [135, 17, 148, 15], [135, 29, 148, 27, "NativeViewGestureHandler"], [135, 53, 148, 51], [135, 57, 148, 55, "handler"], [135, 64, 148, 62], [135, 65, 148, 63, "state"], [135, 70, 148, 68], [135, 75, 148, 73, "State"], [135, 87, 148, 78], [135, 88, 148, 79, "ACTIVE"], [135, 94, 148, 85], [135, 98, 148, 89, "handler"], [135, 105, 148, 96], [135, 106, 148, 97, "disallowsInterruption"], [135, 127, 148, 118], [135, 128, 148, 119], [135, 129, 148, 120], [135, 131, 148, 122], [136, 8, 149, 6], [136, 15, 149, 13], [136, 20, 149, 18], [137, 6, 150, 4], [138, 6, 152, 4], [138, 12, 152, 10, "canBeInterrupted"], [138, 28, 152, 26], [138, 31, 152, 29], [138, 32, 152, 30], [138, 36, 152, 34], [138, 37, 152, 35, "disallowInterruption"], [138, 57, 152, 55], [139, 6, 154, 4], [139, 10, 154, 8], [139, 14, 154, 12], [139, 15, 154, 13, "state"], [139, 20, 154, 18], [139, 25, 154, 23, "State"], [139, 37, 154, 28], [139, 38, 154, 29, "ACTIVE"], [139, 44, 154, 35], [139, 48, 154, 39, "handler"], [139, 55, 154, 46], [139, 56, 154, 47, "state"], [139, 61, 154, 52], [139, 66, 154, 57, "State"], [139, 78, 154, 62], [139, 79, 154, 63, "ACTIVE"], [139, 85, 154, 69], [139, 89, 154, 73, "canBeInterrupted"], [139, 105, 154, 89], [139, 107, 154, 91], [140, 8, 155, 6], [140, 15, 155, 13], [140, 20, 155, 18], [141, 6, 156, 4], [142, 6, 158, 4], [142, 13, 158, 11], [142, 17, 158, 15], [142, 18, 158, 16, "state"], [142, 23, 158, 21], [142, 28, 158, 26, "State"], [142, 40, 158, 31], [142, 41, 158, 32, "ACTIVE"], [142, 47, 158, 38], [142, 51, 158, 42, "canBeInterrupted"], [142, 67, 158, 58], [142, 71, 158, 62, "handler"], [142, 78, 158, 69], [142, 79, 158, 70, "handlerTag"], [142, 89, 158, 80], [142, 92, 158, 83], [142, 93, 158, 84], [143, 4, 159, 2], [144, 4, 161, 2, "shouldBeCancelledByOther"], [144, 28, 161, 26, "shouldBeCancelledByOther"], [144, 29, 161, 27, "_handler"], [144, 37, 161, 35], [144, 39, 161, 37], [145, 6, 162, 4], [145, 13, 162, 11], [145, 14, 162, 12], [145, 18, 162, 16], [145, 19, 162, 17, "disallowInterruption"], [145, 39, 162, 37], [146, 4, 163, 2], [147, 4, 165, 2, "disallowsInterruption"], [147, 25, 165, 23, "disallowsInterruption"], [147, 26, 165, 23], [147, 28, 165, 26], [148, 6, 166, 4], [148, 13, 166, 11], [148, 17, 166, 15], [148, 18, 166, 16, "disallowInterruption"], [148, 38, 166, 36], [149, 4, 167, 2], [150, 4, 169, 2, "isButton"], [150, 12, 169, 10, "isButton"], [150, 13, 169, 10], [150, 15, 169, 13], [151, 6, 170, 4], [151, 13, 170, 11], [151, 17, 170, 15], [151, 18, 170, 16, "buttonRole"], [151, 28, 170, 26], [152, 4, 171, 2], [153, 2, 173, 0], [154, 2, 173, 1, "exports"], [154, 9, 173, 1], [154, 10, 173, 1, "default"], [154, 17, 173, 1], [154, 20, 173, 1, "NativeViewGestureHandler"], [154, 44, 173, 1], [155, 0, 173, 1], [155, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "NativeViewGestureHandler", "constructor", "init", "updateGestureConfig", "restoreViewStyles", "onPointerDown", "onPointerAdd", "newPointerAction", "onPointerMove", "onPointerLeave", "onPointerUp", "onPointerRemove", "onUp", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "disallowsInterruption", "isButton"], "mappings": "AAA,iNC;eCM;ECC;GDc;EEE;GFW;EGE;GHmB;EIE;GJQ;EKE;GLK;EME;GNI;EOE;GPgB;EQE;GRc;ESE;GTI;EUE;GVG;EWE;GXG;EYE;GZU;EaE;GbgB;EcE;GdE;EeE;GfE;EgBE;GhBE;CDE"}}, "type": "js/module"}]}