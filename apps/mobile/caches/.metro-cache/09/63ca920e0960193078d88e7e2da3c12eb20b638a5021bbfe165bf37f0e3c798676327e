{"dependencies": [], "output": [{"data": {"code": "__d(function(global, require, _importDefaultUnused, _importAllUnused, module, exports, _dependencyMapUnused) {\n  module.exports = {\n  \"accessibility\": 61982,\n  \"alert\": 61983,\n  \"apps\": 61984,\n  \"archive\": 61707,\n  \"arrow-both\": 61985,\n  \"arrow-down\": 61708,\n  \"arrow-left\": 61712,\n  \"arrow-right\": 61714,\n  \"arrow-switch\": 61986,\n  \"arrow-up\": 61716,\n  \"beaker\": 61987,\n  \"bell\": 61726,\n  \"bell-fill\": 61988,\n  \"bell-slash\": 61989,\n  \"blocked\": 61990,\n  \"bold\": 61729,\n  \"book\": 61730,\n  \"bookmark\": 61732,\n  \"bookmark-slash\": 61991,\n  \"briefcase\": 61734,\n  \"broadcast\": 61992,\n  \"browser\": 61993,\n  \"bug\": 61994,\n  \"calendar\": 61735,\n  \"check\": 61739,\n  \"check-circle\": 61740,\n  \"check-circle-fill\": 61995,\n  \"checklist\": 61996,\n  \"chevron-down\": 61742,\n  \"chevron-left\": 61743,\n  \"chevron-right\": 61744,\n  \"chevron-up\": 61745,\n  \"circle\": 61751,\n  \"circle-slash\": 61997,\n  \"clock\": 61753,\n  \"code\": 61760,\n  \"code-of-conduct\": 61998,\n  \"code-review\": 61999,\n  \"code-square\": 62000,\n  \"codescan\": 62001,\n  \"codescan-checkmark\": 62002,\n  \"codespaces\": 62003,\n  \"columns\": 61764,\n  \"comment\": 62004,\n  \"comment-discussion\": 62005,\n  \"container\": 62006,\n  \"copy\": 61767,\n  \"cpu\": 61776,\n  \"credit-card\": 61777,\n  \"cross-reference\": 62007,\n  \"dash\": 62008,\n  \"database\": 61780,\n  \"dependabot\": 62009,\n  \"desktop-download\": 62010,\n  \"device-camera\": 62011,\n  \"device-camera-video\": 62012,\n  \"device-desktop\": 62013,\n  \"device-mobile\": 62014,\n  \"diamond\": 62015,\n  \"diff\": 62016,\n  \"diff-added\": 62017,\n  \"diff-ignored\": 62018,\n  \"diff-modified\": 62019,\n  \"diff-removed\": 62020,\n  \"diff-renamed\": 62021,\n  \"dot\": 62022,\n  \"dot-fill\": 62023,\n  \"download\": 61787,\n  \"duplicate\": 62024,\n  \"ellipsis\": 62025,\n  \"eye\": 61795,\n  \"eye-closed\": 62026,\n  \"feed-discussion\": 62027,\n  \"feed-heart\": 62028,\n  \"feed-person\": 62029,\n  \"feed-repo\": 62030,\n  \"feed-rocket\": 62031,\n  \"feed-star\": 62032,\n  \"feed-tag\": 62033,\n  \"file\": 61801,\n  \"file-badge\": 62034,\n  \"file-binary\": 62035,\n  \"file-code\": 62036,\n  \"file-diff\": 62037,\n  \"file-directory\": 62038,\n  \"file-submodule\": 62039,\n  \"file-symlink-file\": 62040,\n  \"file-zip\": 62041,\n  \"filter\": 61806,\n  \"flame\": 62042,\n  \"fold\": 62043,\n  \"fold-down\": 62044,\n  \"fold-up\": 62045,\n  \"gear\": 62046,\n  \"gift\": 61813,\n  \"git-branch\": 61814,\n  \"git-commit\": 61815,\n  \"git-compare\": 62047,\n  \"git-merge\": 61816,\n  \"git-pull-request\": 61817,\n  \"git-pull-request-closed\": 62048,\n  \"git-pull-request-draft\": 62049,\n  \"globe\": 61820,\n  \"grabber\": 62050,\n  \"graph\": 62051,\n  \"hash\": 61823,\n  \"heading\": 62052,\n  \"heart\": 61825,\n  \"heart-fill\": 62053,\n  \"history\": 62054,\n  \"home\": 61828,\n  \"horizontal-rule\": 62055,\n  \"hourglass\": 62056,\n  \"hubot\": 62057,\n  \"id-badge\": 62058,\n  \"image\": 61829,\n  \"inbox\": 61830,\n  \"infinity\": 62059,\n  \"info\": 61831,\n  \"issue-closed\": 62060,\n  \"issue-draft\": 62061,\n  \"issue-opened\": 62062,\n  \"issue-reopened\": 62063,\n  \"italic\": 61833,\n  \"iterations\": 62064,\n  \"kebab-horizontal\": 62065,\n  \"key\": 61834,\n  \"key-asterisk\": 62066,\n  \"law\": 62067,\n  \"light-bulb\": 62068,\n  \"link\": 61838,\n  \"link-external\": 62069,\n  \"list-ordered\": 62070,\n  \"list-unordered\": 62071,\n  \"location\": 62072,\n  \"lock\": 61843,\n  \"log\": 62073,\n  \"logo-gist\": 62074,\n  \"logo-github\": 62075,\n  \"mail\": 61846,\n  \"mark-github\": 62076,\n  \"markdown\": 62077,\n  \"megaphone\": 62078,\n  \"mention\": 62079,\n  \"meter\": 62080,\n  \"milestone\": 62081,\n  \"mirror\": 62082,\n  \"moon\": 61863,\n  \"mortar-board\": 62083,\n  \"multi-select\": 62084,\n  \"mute\": 62085,\n  \"no-entry\": 62086,\n  \"north-star\": 62087,\n  \"note\": 62088,\n  \"number\": 62089,\n  \"organization\": 62090,\n  \"package\": 61872,\n  \"package-dependencies\": 62091,\n  \"package-dependents\": 62092,\n  \"paintbrush\": 62093,\n  \"paper-airplane\": 62094,\n  \"paste\": 62095,\n  \"pencil\": 62096,\n  \"people\": 62097,\n  \"person\": 62098,\n  \"person-add\": 62099,\n  \"person-fill\": 62100,\n  \"pin\": 62101,\n  \"play\": 61886,\n  \"plug\": 62102,\n  \"plus\": 61888,\n  \"plus-circle\": 61889,\n  \"project\": 62103,\n  \"pulse\": 62104,\n  \"question\": 62105,\n  \"quote\": 62106,\n  \"reply\": 62107,\n  \"repo\": 62108,\n  \"repo-clone\": 62109,\n  \"repo-deleted\": 62110,\n  \"repo-forked\": 62111,\n  \"repo-pull\": 62112,\n  \"repo-push\": 62113,\n  \"repo-template\": 62114,\n  \"report\": 62115,\n  \"rocket\": 62116,\n  \"rows\": 62117,\n  \"rss\": 61901,\n  \"ruby\": 62118,\n  \"screen-full\": 62119,\n  \"screen-normal\": 62120,\n  \"search\": 61904,\n  \"server\": 61906,\n  \"share\": 61908,\n  \"share-android\": 62121,\n  \"shield\": 61910,\n  \"shield-check\": 62122,\n  \"shield-lock\": 62123,\n  \"shield-x\": 62124,\n  \"sidebar-collapse\": 62125,\n  \"sidebar-expand\": 62126,\n  \"sign-in\": 62127,\n  \"sign-out\": 62128,\n  \"single-select\": 62129,\n  \"skip\": 62130,\n  \"smiley\": 62131,\n  \"sort-asc\": 62132,\n  \"sort-desc\": 62133,\n  \"square\": 61924,\n  \"square-fill\": 62134,\n  \"squirrel\": 62135,\n  \"stack\": 62136,\n  \"star\": 61925,\n  \"star-fill\": 62137,\n  \"stop\": 62138,\n  \"stopwatch\": 62139,\n  \"strikethrough\": 62140,\n  \"sun\": 61927,\n  \"sync\": 62141,\n  \"tab-external\": 62142,\n  \"table\": 62143,\n  \"tag\": 61931,\n  \"tasklist\": 62144,\n  \"telescope\": 62145,\n  \"telescope-fill\": 62146,\n  \"terminal\": 61933,\n  \"three-bars\": 62147,\n  \"thumbsdown\": 62148,\n  \"thumbsup\": 62149,\n  \"tools\": 62150,\n  \"trash\": 61940,\n  \"triangle-down\": 62151,\n  \"triangle-left\": 62152,\n  \"triangle-right\": 62153,\n  \"triangle-up\": 62154,\n  \"typography\": 62155,\n  \"unfold\": 62156,\n  \"unlock\": 61953,\n  \"unmute\": 62157,\n  \"unverified\": 62158,\n  \"upload\": 61954,\n  \"verified\": 62159,\n  \"versions\": 62160,\n  \"video\": 61962,\n  \"webhook\": 62161,\n  \"workflow\": 62162,\n  \"x\": 61973,\n  \"x-circle\": 61974,\n  \"x-circle-fill\": 62163,\n  \"zap\": 61978\n};\n});", "lineCount": 254, "map": [[254, 3]], "functionMap": null}, "type": "js/module"}]}