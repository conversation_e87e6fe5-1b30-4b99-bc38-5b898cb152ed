{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./Dimensions", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 38}}], "key": "F1jtBuD0CCf9JbYtsxs9+fDiN68=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 42}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useWindowDimensions;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[2], \"./Dimensions\"));\n  var _react = require(_dependencyMap[3], \"react\");\n  function useWindowDimensions() {\n    var _useState = (0, _react.useState)(() => _Dimensions.default.get('window')),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      dimensions = _useState2[0],\n      setDimensions = _useState2[1];\n    (0, _react.useEffect)(() => {\n      function handleChange(_ref) {\n        var window = _ref.window;\n        if (dimensions.width !== window.width || dimensions.height !== window.height || dimensions.scale !== window.scale || dimensions.fontScale !== window.fontScale) {\n          setDimensions(window);\n        }\n      }\n      var subscription = _Dimensions.default.addEventListener('change', handleChange);\n      handleChange({\n        window: _Dimensions.default.get('window')\n      });\n      return () => {\n        subscription.remove();\n      };\n    }, [dimensions]);\n    return dimensions;\n  }\n});", "lineCount": 32, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_Dimensions"], [8, 17, 11, 0], [8, 20, 11, 0, "_interopRequireDefault"], [8, 42, 11, 0], [8, 43, 11, 0, "require"], [8, 50, 11, 0], [8, 51, 11, 0, "_dependencyMap"], [8, 65, 11, 0], [9, 2, 16, 0], [9, 6, 16, 0, "_react"], [9, 12, 16, 0], [9, 15, 16, 0, "require"], [9, 22, 16, 0], [9, 23, 16, 0, "_dependencyMap"], [9, 37, 16, 0], [10, 2, 18, 15], [10, 11, 18, 24, "useWindowDimensions"], [10, 30, 18, 43, "useWindowDimensions"], [10, 31, 18, 43], [10, 33, 20, 26], [11, 4, 21, 2], [11, 8, 21, 2, "_useState"], [11, 17, 21, 2], [11, 20, 21, 38], [11, 24, 21, 38, "useState"], [11, 39, 21, 46], [11, 41, 21, 47], [11, 47, 21, 53, "Dimensions"], [11, 66, 21, 63], [11, 67, 21, 64, "get"], [11, 70, 21, 67], [11, 71, 21, 68], [11, 79, 21, 76], [11, 80, 21, 77], [11, 81, 21, 78], [12, 6, 21, 78, "_useState2"], [12, 16, 21, 78], [12, 23, 21, 78, "_slicedToArray2"], [12, 38, 21, 78], [12, 39, 21, 78, "default"], [12, 46, 21, 78], [12, 48, 21, 78, "_useState"], [12, 57, 21, 78], [13, 6, 21, 9, "dimensions"], [13, 16, 21, 19], [13, 19, 21, 19, "_useState2"], [13, 29, 21, 19], [14, 6, 21, 21, "setDimensions"], [14, 19, 21, 34], [14, 22, 21, 34, "_useState2"], [14, 32, 21, 34], [15, 4, 22, 2], [15, 8, 22, 2, "useEffect"], [15, 24, 22, 11], [15, 26, 22, 12], [15, 32, 22, 18], [16, 6, 23, 4], [16, 15, 23, 13, "handleChange"], [16, 27, 23, 25, "handleChange"], [16, 28, 23, 25, "_ref"], [16, 32, 23, 25], [16, 34, 27, 7], [17, 8, 27, 7], [17, 12, 24, 6, "window"], [17, 18, 24, 12], [17, 21, 24, 12, "_ref"], [17, 25, 24, 12], [17, 26, 24, 6, "window"], [17, 32, 24, 12], [18, 8, 28, 6], [18, 12, 29, 8, "dimensions"], [18, 22, 29, 18], [18, 23, 29, 19, "width"], [18, 28, 29, 24], [18, 33, 29, 29, "window"], [18, 39, 29, 35], [18, 40, 29, 36, "width"], [18, 45, 29, 41], [18, 49, 30, 8, "dimensions"], [18, 59, 30, 18], [18, 60, 30, 19, "height"], [18, 66, 30, 25], [18, 71, 30, 30, "window"], [18, 77, 30, 36], [18, 78, 30, 37, "height"], [18, 84, 30, 43], [18, 88, 31, 8, "dimensions"], [18, 98, 31, 18], [18, 99, 31, 19, "scale"], [18, 104, 31, 24], [18, 109, 31, 29, "window"], [18, 115, 31, 35], [18, 116, 31, 36, "scale"], [18, 121, 31, 41], [18, 125, 32, 8, "dimensions"], [18, 135, 32, 18], [18, 136, 32, 19, "fontScale"], [18, 145, 32, 28], [18, 150, 32, 33, "window"], [18, 156, 32, 39], [18, 157, 32, 40, "fontScale"], [18, 166, 32, 49], [18, 168, 33, 8], [19, 10, 34, 8, "setDimensions"], [19, 23, 34, 21], [19, 24, 34, 22, "window"], [19, 30, 34, 28], [19, 31, 34, 29], [20, 8, 35, 6], [21, 6, 36, 4], [22, 6, 37, 4], [22, 10, 37, 10, "subscription"], [22, 22, 37, 22], [22, 25, 37, 25, "Dimensions"], [22, 44, 37, 35], [22, 45, 37, 36, "addEventListener"], [22, 61, 37, 52], [22, 62, 37, 53], [22, 70, 37, 61], [22, 72, 37, 63, "handleChange"], [22, 84, 37, 75], [22, 85, 37, 76], [23, 6, 41, 4, "handleChange"], [23, 18, 41, 16], [23, 19, 41, 17], [24, 8, 41, 18, "window"], [24, 14, 41, 24], [24, 16, 41, 26, "Dimensions"], [24, 35, 41, 36], [24, 36, 41, 37, "get"], [24, 39, 41, 40], [24, 40, 41, 41], [24, 48, 41, 49], [25, 6, 41, 50], [25, 7, 41, 51], [25, 8, 41, 52], [26, 6, 42, 4], [26, 13, 42, 11], [26, 19, 42, 17], [27, 8, 43, 6, "subscription"], [27, 20, 43, 18], [27, 21, 43, 19, "remove"], [27, 27, 43, 25], [27, 28, 43, 26], [27, 29, 43, 27], [28, 6, 44, 4], [28, 7, 44, 5], [29, 4, 45, 2], [29, 5, 45, 3], [29, 7, 45, 5], [29, 8, 45, 6, "dimensions"], [29, 18, 45, 16], [29, 19, 45, 17], [29, 20, 45, 18], [30, 4, 46, 2], [30, 11, 46, 9, "dimensions"], [30, 21, 46, 19], [31, 2, 47, 0], [32, 0, 47, 1], [32, 3]], "functionMap": {"names": ["<global>", "useWindowDimensions", "useState$argument_0", "useEffect$argument_0", "handleChange", "<anonymous>"], "mappings": "AAA;eCiB;+CCG,8BD;YEC;ICC;KDa;WEM;KFE;GFC"}}, "type": "js/module"}]}