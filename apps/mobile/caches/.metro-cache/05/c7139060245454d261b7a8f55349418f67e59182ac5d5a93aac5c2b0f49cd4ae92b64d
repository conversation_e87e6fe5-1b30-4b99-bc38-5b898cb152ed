{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.usePrevious = usePrevious;\n  var _react = require(_dependencyMap[0], \"react\");\n  function usePrevious(state) {\n    var ref = (0, _react.useRef)();\n    (0, _react.useEffect)(() => {\n      ref.current = state;\n    });\n    return ref.current;\n  }\n});", "lineCount": 14, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 3, 7], [7, 11, 3, 16, "usePrevious"], [7, 22, 3, 27, "usePrevious"], [7, 23, 3, 31, "state"], [7, 28, 3, 39], [7, 30, 3, 56], [8, 4, 4, 2], [8, 8, 4, 8, "ref"], [8, 11, 4, 11], [8, 14, 4, 14], [8, 18, 4, 14, "useRef"], [8, 31, 4, 20], [8, 33, 4, 24], [8, 34, 4, 25], [9, 4, 6, 2], [9, 8, 6, 2, "useEffect"], [9, 24, 6, 11], [9, 26, 6, 12], [9, 32, 6, 18], [10, 6, 7, 4, "ref"], [10, 9, 7, 7], [10, 10, 7, 8, "current"], [10, 17, 7, 15], [10, 20, 7, 18, "state"], [10, 25, 7, 23], [11, 4, 8, 2], [11, 5, 8, 3], [11, 6, 8, 4], [12, 4, 10, 2], [12, 11, 10, 9, "ref"], [12, 14, 10, 12], [12, 15, 10, 13, "current"], [12, 22, 10, 20], [13, 2, 11, 0], [14, 0, 11, 1], [14, 3]], "functionMap": {"names": ["<global>", "usePrevious", "useEffect$argument_0"], "mappings": "AAA;OCE;YCG;GDE;CDG"}}, "type": "js/module"}]}