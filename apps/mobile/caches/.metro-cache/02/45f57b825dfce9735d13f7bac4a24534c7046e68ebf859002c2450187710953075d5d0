{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var TouchHistoryMath = {\n    centroidDimension: function (touchHistory, touchesChangedAfter, isXAxis, ofCurrent) {\n      var touchBank = touchHistory.touchBank;\n      var total = 0;\n      var count = 0;\n      var oneTouchData = touchHistory.numberActiveTouches === 1 ? touchHistory.touchBank[touchHistory.indexOfSingleActiveTouch] : null;\n      if (oneTouchData !== null) {\n        if (oneTouchData.touchActive && oneTouchData.currentTimeStamp > touchesChangedAfter) {\n          total += ofCurrent && isXAxis ? oneTouchData.currentPageX : ofCurrent && !isXAxis ? oneTouchData.currentPageY : !ofCurrent && isXAxis ? oneTouchData.previousPageX : oneTouchData.previousPageY;\n          count = 1;\n        }\n      } else {\n        for (var i = 0; i < touchBank.length; i++) {\n          var touchTrack = touchBank[i];\n          if (touchTrack !== null && touchTrack !== undefined && touchTrack.touchActive && touchTrack.currentTimeStamp >= touchesChangedAfter) {\n            var toAdd = void 0;\n            if (ofCurrent && isXAxis) {\n              toAdd = touchTrack.currentPageX;\n            } else if (ofCurrent && !isXAxis) {\n              toAdd = touchTrack.currentPageY;\n            } else if (!ofCurrent && isXAxis) {\n              toAdd = touchTrack.previousPageX;\n            } else {\n              toAdd = touchTrack.previousPageY;\n            }\n            total += toAdd;\n            count++;\n          }\n        }\n      }\n      return count > 0 ? total / count : TouchHistoryMath.noCentroid;\n    },\n    currentCentroidXOfTouchesChangedAfter: function (touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true, true);\n    },\n    currentCentroidYOfTouchesChangedAfter: function (touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false, true);\n    },\n    previousCentroidXOfTouchesChangedAfter: function (touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true, false);\n    },\n    previousCentroidYOfTouchesChangedAfter: function (touchHistory, touchesChangedAfter) {\n      return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false, false);\n    },\n    currentCentroidX: function (touchHistory) {\n      return TouchHistoryMath.centroidDimension(touchHistory, 0, true, true);\n    },\n    currentCentroidY: function (touchHistory) {\n      return TouchHistoryMath.centroidDimension(touchHistory, 0, false, true);\n    },\n    noCentroid: -1\n  };\n  var _default = exports.default = TouchHistoryMath;\n});", "lineCount": 59, "map": [[6, 2, 13, 0], [6, 6, 13, 6, "TouchHistoryMath"], [6, 22, 13, 22], [6, 25, 13, 25], [7, 4, 30, 2, "centroidDimension"], [7, 21, 30, 19], [7, 23, 30, 21], [7, 32, 30, 21, "centroidDimension"], [7, 33, 31, 4, "touchHistory"], [7, 45, 31, 34], [7, 47, 32, 4, "touchesChangedAfter"], [7, 66, 32, 31], [7, 68, 33, 4, "isXAxis"], [7, 75, 33, 20], [7, 77, 34, 4, "ofCurrent"], [7, 86, 34, 22], [7, 88, 35, 12], [8, 6, 36, 4], [8, 10, 36, 10, "touchBank"], [8, 19, 36, 19], [8, 22, 36, 22, "touchHistory"], [8, 34, 36, 34], [8, 35, 36, 35, "touchBank"], [8, 44, 36, 44], [9, 6, 37, 4], [9, 10, 37, 8, "total"], [9, 15, 37, 13], [9, 18, 37, 16], [9, 19, 37, 17], [10, 6, 38, 4], [10, 10, 38, 8, "count"], [10, 15, 38, 13], [10, 18, 38, 16], [10, 19, 38, 17], [11, 6, 40, 4], [11, 10, 40, 10, "oneTouchData"], [11, 22, 40, 22], [11, 25, 41, 6, "touchHistory"], [11, 37, 41, 18], [11, 38, 41, 19, "numberActiveTouches"], [11, 57, 41, 38], [11, 62, 41, 43], [11, 63, 41, 44], [11, 66, 42, 10, "touchHistory"], [11, 78, 42, 22], [11, 79, 42, 23, "touchBank"], [11, 88, 42, 32], [11, 89, 42, 33, "touchHistory"], [11, 101, 42, 45], [11, 102, 42, 46, "indexOfSingleActiveTouch"], [11, 126, 42, 70], [11, 127, 42, 71], [11, 130, 43, 10], [11, 134, 43, 14], [12, 6, 45, 4], [12, 10, 45, 8, "oneTouchData"], [12, 22, 45, 20], [12, 27, 45, 25], [12, 31, 45, 29], [12, 33, 45, 31], [13, 8, 46, 6], [13, 12, 47, 8, "oneTouchData"], [13, 24, 47, 20], [13, 25, 47, 21, "touchActive"], [13, 36, 47, 32], [13, 40, 48, 8, "oneTouchData"], [13, 52, 48, 20], [13, 53, 48, 21, "currentTimeStamp"], [13, 69, 48, 37], [13, 72, 48, 40, "touchesChangedAfter"], [13, 91, 48, 59], [13, 93, 49, 8], [14, 10, 50, 8, "total"], [14, 15, 50, 13], [14, 19, 51, 10, "ofCurrent"], [14, 28, 51, 19], [14, 32, 51, 23, "isXAxis"], [14, 39, 51, 30], [14, 42, 52, 14, "oneTouchData"], [14, 54, 52, 26], [14, 55, 52, 27, "currentPageX"], [14, 67, 52, 39], [14, 70, 53, 14, "ofCurrent"], [14, 79, 53, 23], [14, 83, 53, 27], [14, 84, 53, 28, "isXAxis"], [14, 91, 53, 35], [14, 94, 54, 16, "oneTouchData"], [14, 106, 54, 28], [14, 107, 54, 29, "currentPageY"], [14, 119, 54, 41], [14, 122, 55, 16], [14, 123, 55, 17, "ofCurrent"], [14, 132, 55, 26], [14, 136, 55, 30, "isXAxis"], [14, 143, 55, 37], [14, 146, 56, 18, "oneTouchData"], [14, 158, 56, 30], [14, 159, 56, 31, "previousPageX"], [14, 172, 56, 44], [14, 175, 57, 18, "oneTouchData"], [14, 187, 57, 30], [14, 188, 57, 31, "previousPageY"], [14, 201, 57, 44], [15, 10, 58, 8, "count"], [15, 15, 58, 13], [15, 18, 58, 16], [15, 19, 58, 17], [16, 8, 59, 6], [17, 6, 60, 4], [17, 7, 60, 5], [17, 13, 60, 11], [18, 8, 61, 6], [18, 13, 61, 11], [18, 17, 61, 15, "i"], [18, 18, 61, 16], [18, 21, 61, 19], [18, 22, 61, 20], [18, 24, 61, 22, "i"], [18, 25, 61, 23], [18, 28, 61, 26, "touchBank"], [18, 37, 61, 35], [18, 38, 61, 36, "length"], [18, 44, 61, 42], [18, 46, 61, 44, "i"], [18, 47, 61, 45], [18, 49, 61, 47], [18, 51, 61, 49], [19, 10, 62, 8], [19, 14, 62, 14, "touchTrack"], [19, 24, 62, 24], [19, 27, 62, 27, "touchBank"], [19, 36, 62, 36], [19, 37, 62, 37, "i"], [19, 38, 62, 38], [19, 39, 62, 39], [20, 10, 63, 8], [20, 14, 64, 10, "touchTrack"], [20, 24, 64, 20], [20, 29, 64, 25], [20, 33, 64, 29], [20, 37, 65, 10, "touchTrack"], [20, 47, 65, 20], [20, 52, 65, 25, "undefined"], [20, 61, 65, 34], [20, 65, 66, 10, "touchTrack"], [20, 75, 66, 20], [20, 76, 66, 21, "touchActive"], [20, 87, 66, 32], [20, 91, 67, 10, "touchTrack"], [20, 101, 67, 20], [20, 102, 67, 21, "currentTimeStamp"], [20, 118, 67, 37], [20, 122, 67, 41, "touchesChangedAfter"], [20, 141, 67, 60], [20, 143, 68, 10], [21, 12, 69, 10], [21, 16, 69, 14, "toAdd"], [21, 21, 69, 19], [22, 12, 70, 10], [22, 16, 70, 14, "ofCurrent"], [22, 25, 70, 23], [22, 29, 70, 27, "isXAxis"], [22, 36, 70, 34], [22, 38, 70, 36], [23, 14, 71, 12, "toAdd"], [23, 19, 71, 17], [23, 22, 71, 20, "touchTrack"], [23, 32, 71, 30], [23, 33, 71, 31, "currentPageX"], [23, 45, 71, 43], [24, 12, 72, 10], [24, 13, 72, 11], [24, 19, 72, 17], [24, 23, 72, 21, "ofCurrent"], [24, 32, 72, 30], [24, 36, 72, 34], [24, 37, 72, 35, "isXAxis"], [24, 44, 72, 42], [24, 46, 72, 44], [25, 14, 73, 12, "toAdd"], [25, 19, 73, 17], [25, 22, 73, 20, "touchTrack"], [25, 32, 73, 30], [25, 33, 73, 31, "currentPageY"], [25, 45, 73, 43], [26, 12, 74, 10], [26, 13, 74, 11], [26, 19, 74, 17], [26, 23, 74, 21], [26, 24, 74, 22, "ofCurrent"], [26, 33, 74, 31], [26, 37, 74, 35, "isXAxis"], [26, 44, 74, 42], [26, 46, 74, 44], [27, 14, 75, 12, "toAdd"], [27, 19, 75, 17], [27, 22, 75, 20, "touchTrack"], [27, 32, 75, 30], [27, 33, 75, 31, "previousPageX"], [27, 46, 75, 44], [28, 12, 76, 10], [28, 13, 76, 11], [28, 19, 76, 17], [29, 14, 77, 12, "toAdd"], [29, 19, 77, 17], [29, 22, 77, 20, "touchTrack"], [29, 32, 77, 30], [29, 33, 77, 31, "previousPageY"], [29, 46, 77, 44], [30, 12, 78, 10], [31, 12, 79, 10, "total"], [31, 17, 79, 15], [31, 21, 79, 19, "toAdd"], [31, 26, 79, 24], [32, 12, 80, 10, "count"], [32, 17, 80, 15], [32, 19, 80, 17], [33, 10, 81, 8], [34, 8, 82, 6], [35, 6, 83, 4], [36, 6, 84, 4], [36, 13, 84, 11, "count"], [36, 18, 84, 16], [36, 21, 84, 19], [36, 22, 84, 20], [36, 25, 84, 23, "total"], [36, 30, 84, 28], [36, 33, 84, 31, "count"], [36, 38, 84, 36], [36, 41, 84, 39, "TouchHistoryMath"], [36, 57, 84, 55], [36, 58, 84, 56, "noCentroid"], [36, 68, 84, 66], [37, 4, 85, 2], [37, 5, 85, 3], [38, 4, 87, 2, "currentCentroidXOfTouchesChangedAfter"], [38, 41, 87, 39], [38, 43, 87, 41], [38, 52, 87, 41, "currentCentroidXOfTouchesChangedAfter"], [38, 53, 88, 4, "touchHistory"], [38, 65, 88, 34], [38, 67, 89, 4, "touchesChangedAfter"], [38, 86, 89, 31], [38, 88, 90, 12], [39, 6, 91, 4], [39, 13, 91, 11, "TouchHistoryMath"], [39, 29, 91, 27], [39, 30, 91, 28, "centroidDimension"], [39, 47, 91, 45], [39, 48, 92, 6, "touchHistory"], [39, 60, 92, 18], [39, 62, 93, 6, "touchesChangedAfter"], [39, 81, 93, 25], [39, 83, 94, 6], [39, 87, 94, 10], [39, 89, 95, 6], [39, 93, 96, 4], [39, 94, 96, 5], [40, 4, 97, 2], [40, 5, 97, 3], [41, 4, 99, 2, "currentCentroidYOfTouchesChangedAfter"], [41, 41, 99, 39], [41, 43, 99, 41], [41, 52, 99, 41, "currentCentroidYOfTouchesChangedAfter"], [41, 53, 100, 4, "touchHistory"], [41, 65, 100, 34], [41, 67, 101, 4, "touchesChangedAfter"], [41, 86, 101, 31], [41, 88, 102, 12], [42, 6, 103, 4], [42, 13, 103, 11, "TouchHistoryMath"], [42, 29, 103, 27], [42, 30, 103, 28, "centroidDimension"], [42, 47, 103, 45], [42, 48, 104, 6, "touchHistory"], [42, 60, 104, 18], [42, 62, 105, 6, "touchesChangedAfter"], [42, 81, 105, 25], [42, 83, 106, 6], [42, 88, 106, 11], [42, 90, 107, 6], [42, 94, 108, 4], [42, 95, 108, 5], [43, 4, 109, 2], [43, 5, 109, 3], [44, 4, 111, 2, "previousCentroidXOfTouchesChangedAfter"], [44, 42, 111, 40], [44, 44, 111, 42], [44, 53, 111, 42, "previousCentroidXOfTouchesChangedAfter"], [44, 54, 112, 4, "touchHistory"], [44, 66, 112, 34], [44, 68, 113, 4, "touchesChangedAfter"], [44, 87, 113, 31], [44, 89, 114, 12], [45, 6, 115, 4], [45, 13, 115, 11, "TouchHistoryMath"], [45, 29, 115, 27], [45, 30, 115, 28, "centroidDimension"], [45, 47, 115, 45], [45, 48, 116, 6, "touchHistory"], [45, 60, 116, 18], [45, 62, 117, 6, "touchesChangedAfter"], [45, 81, 117, 25], [45, 83, 118, 6], [45, 87, 118, 10], [45, 89, 119, 6], [45, 94, 120, 4], [45, 95, 120, 5], [46, 4, 121, 2], [46, 5, 121, 3], [47, 4, 123, 2, "previousCentroidYOfTouchesChangedAfter"], [47, 42, 123, 40], [47, 44, 123, 42], [47, 53, 123, 42, "previousCentroidYOfTouchesChangedAfter"], [47, 54, 124, 4, "touchHistory"], [47, 66, 124, 34], [47, 68, 125, 4, "touchesChangedAfter"], [47, 87, 125, 31], [47, 89, 126, 12], [48, 6, 127, 4], [48, 13, 127, 11, "TouchHistoryMath"], [48, 29, 127, 27], [48, 30, 127, 28, "centroidDimension"], [48, 47, 127, 45], [48, 48, 128, 6, "touchHistory"], [48, 60, 128, 18], [48, 62, 129, 6, "touchesChangedAfter"], [48, 81, 129, 25], [48, 83, 130, 6], [48, 88, 130, 11], [48, 90, 131, 6], [48, 95, 132, 4], [48, 96, 132, 5], [49, 4, 133, 2], [49, 5, 133, 3], [50, 4, 135, 2, "currentCentroidX"], [50, 20, 135, 18], [50, 22, 135, 20], [50, 31, 135, 20, "currentCentroidX"], [50, 32, 135, 30, "touchHistory"], [50, 44, 135, 60], [50, 46, 135, 70], [51, 6, 136, 4], [51, 13, 136, 11, "TouchHistoryMath"], [51, 29, 136, 27], [51, 30, 136, 28, "centroidDimension"], [51, 47, 136, 45], [51, 48, 137, 6, "touchHistory"], [51, 60, 137, 18], [51, 62, 138, 6], [51, 63, 138, 7], [51, 65, 139, 6], [51, 69, 139, 10], [51, 71, 140, 6], [51, 75, 141, 4], [51, 76, 141, 5], [52, 4, 142, 2], [52, 5, 142, 3], [53, 4, 144, 2, "currentCentroidY"], [53, 20, 144, 18], [53, 22, 144, 20], [53, 31, 144, 20, "currentCentroidY"], [53, 32, 144, 30, "touchHistory"], [53, 44, 144, 60], [53, 46, 144, 70], [54, 6, 145, 4], [54, 13, 145, 11, "TouchHistoryMath"], [54, 29, 145, 27], [54, 30, 145, 28, "centroidDimension"], [54, 47, 145, 45], [54, 48, 146, 6, "touchHistory"], [54, 60, 146, 18], [54, 62, 147, 6], [54, 63, 147, 7], [54, 65, 148, 6], [54, 70, 148, 11], [54, 72, 149, 6], [54, 76, 150, 4], [54, 77, 150, 5], [55, 4, 151, 2], [55, 5, 151, 3], [56, 4, 153, 2, "noCentroid"], [56, 14, 153, 12], [56, 16, 153, 14], [56, 17, 153, 15], [57, 2, 154, 0], [57, 3, 180, 1], [58, 2, 180, 2], [58, 6, 180, 2, "_default"], [58, 14, 180, 2], [58, 17, 180, 2, "exports"], [58, 24, 180, 2], [58, 25, 180, 2, "default"], [58, 32, 180, 2], [58, 35, 182, 15, "TouchHistoryMath"], [58, 51, 182, 31], [59, 0, 182, 31], [59, 3]], "functionMap": {"names": ["<global>", "centroidDimension", "currentCentroidXOfTouchesChangedAfter", "currentCentroidYOfTouchesChangedAfter", "previousCentroidXOfTouchesChangedAfter", "previousCentroidYOfTouchesChangedAfter", "currentCentroidX", "currentCentroidY"], "mappings": "AAA;qBC6B;GDuD;yCEE;GFU;yCGE;GHU;0CIE;GJU;0CKE;GLU;oBME;GNO;oBOE;GPO"}}, "type": "js/module"}]}