{"dependencies": [{"name": "./RNScreensTurboModule.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 65, "index": 80}}], "key": "Hb2KltlZZNteDLgJxBM/w3DMSTQ=", "exportNames": ["*"]}}, {"name": "./styleUpdater.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 81}, "end": {"line": 4, "column": 76, "index": 157}}], "key": "gE9MWcLES+XgUfY8ydNusVgaUwc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getSwipeSimulator = void 0;\n  var _RNScreensTurboModule = require(_dependencyMap[0], \"./RNScreensTurboModule.js\");\n  var _styleUpdater = require(_dependencyMap[1], \"./styleUpdater.js\");\n  const BASE_VELOCITY = 400;\n  const ADDITIONAL_VELOCITY_FACTOR_X = 400;\n  const ADDITIONAL_VELOCITY_FACTOR_Y = 500;\n  const ADDITIONAL_VELOCITY_FACTOR_XY = 600;\n  const _worklet_14551241534927_init_data = {\n    code: \"function computeEasingProgress_reactNativeReanimated_swipeSimulatorJs1(startingTimestamp,distance,velocity){if(Math.abs(distance)<1){return 1;}const elapsedTime=(_getAnimationTimestamp()-startingTimestamp)/1000;const currentPosition=velocity*elapsedTime;const progress=currentPosition/distance;return progress;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"computeEasingProgress_reactNativeReanimated_swipeSimulatorJs1\\\",\\\"startingTimestamp\\\",\\\"distance\\\",\\\"velocity\\\",\\\"Math\\\",\\\"abs\\\",\\\"elapsedTime\\\",\\\"_getAnimationTimestamp\\\",\\\"currentPosition\\\",\\\"progress\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\\\"],\\\"mappings\\\":\\\"AAQA,SAAAA,6DAAoEA,CAAEC,iBAAA,CAAAC,QAAA,CAAAC,QAAA,EAGpE,GAAIC,IAAI,CAACC,GAAG,CAACH,QAAQ,CAAC,CAAG,CAAC,CAAE,CAC1B,MAAO,EAAC,CACV,CACA,KAAM,CAAAI,WAAW,CAAG,CAACC,sBAAsB,CAAC,CAAC,CAAGN,iBAAiB,EAAI,IAAI,CACzE,KAAM,CAAAO,eAAe,CAAGL,QAAQ,CAAGG,WAAW,CAC9C,KAAM,CAAAG,QAAQ,CAAGD,eAAe,CAAGN,QAAQ,CAC3C,MAAO,CAAAO,QAAQ,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const computeEasingProgress = function () {\n    const _e = [new global.Error(), 1, -27];\n    const computeEasingProgress = function (startingTimestamp, distance, velocity) {\n      if (Math.abs(distance) < 1) {\n        return 1;\n      }\n      const elapsedTime = (_getAnimationTimestamp() - startingTimestamp) / 1000;\n      const currentPosition = velocity * elapsedTime;\n      const progress = currentPosition / distance;\n      return progress;\n    };\n    computeEasingProgress.__closure = {};\n    computeEasingProgress.__workletHash = 14551241534927;\n    computeEasingProgress.__initData = _worklet_14551241534927_init_data;\n    computeEasingProgress.__stackDetails = _e;\n    return computeEasingProgress;\n  }();\n  const _worklet_10786758827735_init_data = {\n    code: \"function easing_reactNativeReanimated_swipeSimulatorJs2(x){return 1-Math.pow(1-x,5);}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"easing_reactNativeReanimated_swipeSimulatorJs2\\\",\\\"x\\\",\\\"Math\\\",\\\"pow\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\\\"],\\\"mappings\\\":\\\"AAmBA,SAAAA,8CAAmBA,CAAAC,CAAA,EAIjB,MAAO,EAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGF,CAAC,CAAE,CAAC,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const easing = function () {\n    const _e = [new global.Error(), 1, -27];\n    const easing = function (x) {\n      // based on https://easings.net/#easeOutQuart\n      return 1 - Math.pow(1 - x, 5);\n    };\n    easing.__closure = {};\n    easing.__workletHash = 10786758827735;\n    easing.__initData = _worklet_10786758827735_init_data;\n    easing.__stackDetails = _e;\n    return easing;\n  }();\n  const _worklet_1991703960680_init_data = {\n    code: \"function computeProgress_reactNativeReanimated_swipeSimulatorJs3(screenTransitionConfig,event,isTransitionCanceled){const screenDimensions=screenTransitionConfig.screenDimensions;const progressX=Math.abs(event.translationX/screenDimensions.width);const progressY=Math.abs(event.translationY/screenDimensions.height);const maxProgress=Math.max(progressX,progressY);const progress=isTransitionCanceled?maxProgress/2:maxProgress;return progress;}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"computeProgress_reactNativeReanimated_swipeSimulatorJs3\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"isTransitionCanceled\\\",\\\"screenDimensions\\\",\\\"progressX\\\",\\\"Math\\\",\\\"abs\\\",\\\"translationX\\\",\\\"width\\\",\\\"progressY\\\",\\\"translationY\\\",\\\"height\\\",\\\"maxProgress\\\",\\\"max\\\",\\\"progress\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\\\"],\\\"mappings\\\":\\\"AAyBA,SAAAA,uDAAwDA,CAAAC,sBAAsB,CAAAC,KAAA,CAAAC,oBAAA,EAG5E,KAAM,CAAAC,gBAAgB,CAAGH,sBAAsB,CAACG,gBAAgB,CAChE,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAACM,YAAY,CAAGJ,gBAAgB,CAACK,KAAK,CAAC,CACvE,KAAM,CAAAC,SAAS,CAAGJ,IAAI,CAACC,GAAG,CAACL,KAAK,CAACS,YAAY,CAAGP,gBAAgB,CAACQ,MAAM,CAAC,CACxE,KAAM,CAAAC,WAAW,CAAGP,IAAI,CAACQ,GAAG,CAACT,SAAS,CAAEK,SAAS,CAAC,CAClD,KAAM,CAAAK,QAAQ,CAAGZ,oBAAoB,CAAGU,WAAW,CAAG,CAAC,CAAGA,WAAW,CACrE,MAAO,CAAAE,QAAQ,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const computeProgress = function () {\n    const _e = [new global.Error(), 1, -27];\n    const computeProgress = function (screenTransitionConfig, event, isTransitionCanceled) {\n      const screenDimensions = screenTransitionConfig.screenDimensions;\n      const progressX = Math.abs(event.translationX / screenDimensions.width);\n      const progressY = Math.abs(event.translationY / screenDimensions.height);\n      const maxProgress = Math.max(progressX, progressY);\n      const progress = isTransitionCanceled ? maxProgress / 2 : maxProgress;\n      return progress;\n    };\n    computeProgress.__closure = {};\n    computeProgress.__workletHash = 1991703960680;\n    computeProgress.__initData = _worklet_1991703960680_init_data;\n    computeProgress.__stackDetails = _e;\n    return computeProgress;\n  }();\n  const _worklet_5191610802729_init_data = {\n    code: \"function maybeScheduleNextFrame_reactNativeReanimated_swipeSimulatorJs4(step,didScreenReachDestination,screenTransitionConfig,event,isTransitionCanceled){const{computeProgress,RNScreensTurboModule}=this.__closure;if(!didScreenReachDestination){const stackTag=screenTransitionConfig.stackTag;const progress=computeProgress(screenTransitionConfig,event,isTransitionCanceled);RNScreensTurboModule.updateTransition(stackTag,progress);requestAnimationFrame(step);}else{var _screenTransitionConf;(_screenTransitionConf=screenTransitionConfig.onFinishAnimation)===null||_screenTransitionConf===void 0||_screenTransitionConf.call(screenTransitionConfig);}}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"maybeScheduleNextFrame_reactNativeReanimated_swipeSimulatorJs4\\\",\\\"step\\\",\\\"didScreenReachDestination\\\",\\\"screenTransitionConfig\\\",\\\"event\\\",\\\"isTransitionCanceled\\\",\\\"computeProgress\\\",\\\"RNScreensTurboModule\\\",\\\"__closure\\\",\\\"stackTag\\\",\\\"progress\\\",\\\"updateTransition\\\",\\\"requestAnimationFrame\\\",\\\"_screenTransitionConf\\\",\\\"onFinishAnimation\\\",\\\"call\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\\\"],\\\"mappings\\\":\\\"AAmCA,SAAAA,8DAAiEA,CAAAC,IAAA,CAAAC,yBAA+B,CAAAC,sBAAsB,CAAAC,KAAA,CAAAC,oBAAA,QAAAC,eAAA,CAAAC,oBAAA,OAAAC,SAAA,CAGpH,GAAI,CAACN,yBAAyB,CAAE,CAC9B,KAAM,CAAAO,QAAQ,CAAGN,sBAAsB,CAACM,QAAQ,CAChD,KAAM,CAAAC,QAAQ,CAAGJ,eAAe,CAACH,sBAAsB,CAAEC,KAAK,CAAEC,oBAAoB,CAAC,CACrFE,oBAAoB,CAACI,gBAAgB,CAACF,QAAQ,CAAEC,QAAQ,CAAC,CACzDE,qBAAqB,CAACX,IAAI,CAAC,CAC7B,CAAC,IAAM,KAAAY,qBAAA,CACL,CAAAA,qBAAA,CAAAV,sBAAsB,CAACW,iBAAiB,UAAAD,qBAAA,WAAxCA,qBAAA,CAAAE,IAAA,CAAAZ,sBAA2C,CAAC,CAC9C,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const maybeScheduleNextFrame = function () {\n    const _e = [new global.Error(), -3, -27];\n    const maybeScheduleNextFrame = function (step, didScreenReachDestination, screenTransitionConfig, event, isTransitionCanceled) {\n      if (!didScreenReachDestination) {\n        const stackTag = screenTransitionConfig.stackTag;\n        const progress = computeProgress(screenTransitionConfig, event, isTransitionCanceled);\n        _RNScreensTurboModule.RNScreensTurboModule.updateTransition(stackTag, progress);\n        requestAnimationFrame(step);\n      } else {\n        screenTransitionConfig.onFinishAnimation?.();\n      }\n    };\n    maybeScheduleNextFrame.__closure = {\n      computeProgress,\n      RNScreensTurboModule: _RNScreensTurboModule.RNScreensTurboModule\n    };\n    maybeScheduleNextFrame.__workletHash = 5191610802729;\n    maybeScheduleNextFrame.__initData = _worklet_5191610802729_init_data;\n    maybeScheduleNextFrame.__stackDetails = _e;\n    return maybeScheduleNextFrame;\n  }();\n  const _worklet_15110927952447_init_data = {\n    code: \"function getSwipeSimulator_reactNativeReanimated_swipeSimulatorJs5(event,screenTransitionConfig,lockAxis){const{BASE_VELOCITY,ADDITIONAL_VELOCITY_FACTOR_X,ADDITIONAL_VELOCITY_FACTOR_Y,ADDITIONAL_VELOCITY_FACTOR_XY,applyStyleForBelowTopScreen,computeEasingProgress,easing,applyStyle,maybeScheduleNextFrame}=this.__closure;const screenDimensions=screenTransitionConfig.screenDimensions;const startTimestamp=_getAnimationTimestamp();const{isTransitionCanceled:isTransitionCanceled}=screenTransitionConfig;const startingPosition={x:event.translationX,y:event.translationY};const direction={x:Math.sign(event.translationX),y:Math.sign(event.translationY)};const finalPosition=isTransitionCanceled?{x:0,y:0}:{x:direction.x*screenDimensions.width,y:direction.y*screenDimensions.height};const distance={x:Math.abs(finalPosition.x-startingPosition.x),y:Math.abs(finalPosition.y-startingPosition.y)};const didScreenReachDestination={x:false,y:false};const velocity={x:BASE_VELOCITY,y:BASE_VELOCITY};if(lockAxis==='x'){velocity.y=0;velocity.x+=ADDITIONAL_VELOCITY_FACTOR_X*distance.x/screenDimensions.width;}else if(lockAxis==='y'){velocity.x=0;velocity.y+=ADDITIONAL_VELOCITY_FACTOR_Y*distance.y/screenDimensions.height;}else{const euclideanDistance=Math.sqrt(distance.x**2+distance.y**2);const screenDiagonal=Math.sqrt(screenDimensions.width**2+screenDimensions.height**2);const velocityVectorLength=BASE_VELOCITY+ADDITIONAL_VELOCITY_FACTOR_XY*euclideanDistance/screenDiagonal;if(Math.abs(startingPosition.x)>Math.abs(startingPosition.y)){velocity.x=velocityVectorLength;velocity.y=velocityVectorLength*Math.abs(startingPosition.y/startingPosition.x);}else{velocity.x=velocityVectorLength*Math.abs(startingPosition.x/startingPosition.y);velocity.y=velocityVectorLength;}}if(isTransitionCanceled){function didScreenReachDestinationCheck(){if(lockAxis==='x'){return didScreenReachDestination.x;}else if(lockAxis==='y'){return didScreenReachDestination.y;}else{return didScreenReachDestination.x&&didScreenReachDestination.y;}}function restoreOriginalStyleForBelowTopScreen(){event.translationX=direction.x*screenDimensions.width;event.translationY=direction.y*screenDimensions.height;applyStyleForBelowTopScreen(screenTransitionConfig,event);}const computeFrame=function(){const progress={x:computeEasingProgress(startTimestamp,distance.x,velocity.x),y:computeEasingProgress(startTimestamp,distance.y,velocity.y)};event.translationX=startingPosition.x-direction.x*distance.x*easing(progress.x);event.translationY=startingPosition.y-direction.y*distance.y*easing(progress.y);if(direction.x>0){if(event.translationX<=0){didScreenReachDestination.x=true;event.translationX=0;}}else{if(event.translationX>=0){didScreenReachDestination.x=true;event.translationX=0;}}if(direction.y>0){if(event.translationY<=0){didScreenReachDestination.y=true;event.translationY=0;}}else{if(event.translationY>=0){didScreenReachDestination.y=true;event.translationY=0;}}applyStyle(screenTransitionConfig,event);const finished=didScreenReachDestinationCheck();if(finished){restoreOriginalStyleForBelowTopScreen();}maybeScheduleNextFrame(computeFrame,finished,screenTransitionConfig,event,isTransitionCanceled);};return computeFrame;}else{const computeFrame=function(){const progress={x:computeEasingProgress(startTimestamp,distance.x,velocity.x),y:computeEasingProgress(startTimestamp,distance.y,velocity.y)};event.translationX=startingPosition.x+direction.x*distance.x*easing(progress.x);event.translationY=startingPosition.y+direction.y*distance.y*easing(progress.y);if(direction.x>0){if(event.translationX>=screenDimensions.width){didScreenReachDestination.x=true;event.translationX=screenDimensions.width;}}else{if(event.translationX<=-screenDimensions.width){didScreenReachDestination.x=true;event.translationX=-screenDimensions.width;}}if(direction.y>0){if(event.translationY>=screenDimensions.height){didScreenReachDestination.y=true;event.translationY=screenDimensions.height;}}else{if(event.translationY<=-screenDimensions.height){didScreenReachDestination.y=true;event.translationY=-screenDimensions.height;}}applyStyle(screenTransitionConfig,event);maybeScheduleNextFrame(computeFrame,didScreenReachDestination.x||didScreenReachDestination.y,screenTransitionConfig,event,isTransitionCanceled);};return computeFrame;}}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"getSwipeSimulator_reactNativeReanimated_swipeSimulatorJs5\\\",\\\"event\\\",\\\"screenTransitionConfig\\\",\\\"lockAxis\\\",\\\"BASE_VELOCITY\\\",\\\"ADDITIONAL_VELOCITY_FACTOR_X\\\",\\\"ADDITIONAL_VELOCITY_FACTOR_Y\\\",\\\"ADDITIONAL_VELOCITY_FACTOR_XY\\\",\\\"applyStyleForBelowTopScreen\\\",\\\"computeEasingProgress\\\",\\\"easing\\\",\\\"applyStyle\\\",\\\"maybeScheduleNextFrame\\\",\\\"__closure\\\",\\\"screenDimensions\\\",\\\"startTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"isTransitionCanceled\\\",\\\"startingPosition\\\",\\\"x\\\",\\\"translationX\\\",\\\"y\\\",\\\"translationY\\\",\\\"direction\\\",\\\"Math\\\",\\\"sign\\\",\\\"finalPosition\\\",\\\"width\\\",\\\"height\\\",\\\"distance\\\",\\\"abs\\\",\\\"didScreenReachDestination\\\",\\\"velocity\\\",\\\"euclideanDistance\\\",\\\"sqrt\\\",\\\"screenDiagonal\\\",\\\"velocityVectorLength\\\",\\\"didScreenReachDestinationCheck\\\",\\\"restoreOriginalStyleForBelowTopScreen\\\",\\\"computeFrame\\\",\\\"progress\\\",\\\"finished\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/swipeSimulator.js\\\"],\\\"mappings\\\":\\\"AA+CO,SAAAA,yDAA0DA,CAAQC,KAAE,CAAAC,sBAAA,CAAAC,QAAA,QAAAC,aAAA,CAAAC,4BAAA,CAAAC,4BAAA,CAAAC,6BAAA,CAAAC,2BAAA,CAAAC,qBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,sBAAA,OAAAC,SAAA,CAGzE,KAAM,CAAAC,gBAAgB,CAAGZ,sBAAsB,CAACY,gBAAgB,CAChE,KAAM,CAAAC,cAAc,CAAGC,sBAAsB,CAAC,CAAC,CAC/C,KAAM,CACJC,oBAAA,CAAAA,oBACF,CAAC,CAAGf,sBAAsB,CAC1B,KAAM,CAAAgB,gBAAgB,CAAG,CACvBC,CAAC,CAAElB,KAAK,CAACmB,YAAY,CACrBC,CAAC,CAAEpB,KAAK,CAACqB,YACX,CAAC,CACD,KAAM,CAAAC,SAAS,CAAG,CAChBJ,CAAC,CAAEK,IAAI,CAACC,IAAI,CAACxB,KAAK,CAACmB,YAAY,CAAC,CAChCC,CAAC,CAAEG,IAAI,CAACC,IAAI,CAACxB,KAAK,CAACqB,YAAY,CACjC,CAAC,CACD,KAAM,CAAAI,aAAa,CAAGT,oBAAoB,CAAG,CAC3CE,CAAC,CAAE,CAAC,CACJE,CAAC,CAAE,CACL,CAAC,CAAG,CACFF,CAAC,CAAEI,SAAS,CAACJ,CAAC,CAAGL,gBAAgB,CAACa,KAAK,CACvCN,CAAC,CAAEE,SAAS,CAACF,CAAC,CAAGP,gBAAgB,CAACc,MACpC,CAAC,CACD,KAAM,CAAAC,QAAQ,CAAG,CACfV,CAAC,CAAEK,IAAI,CAACM,GAAG,CAACJ,aAAa,CAACP,CAAC,CAAGD,gBAAgB,CAACC,CAAC,CAAC,CACjDE,CAAC,CAAEG,IAAI,CAACM,GAAG,CAACJ,aAAa,CAACL,CAAC,CAAGH,gBAAgB,CAACG,CAAC,CAClD,CAAC,CACD,KAAM,CAAAU,yBAAyB,CAAG,CAChCZ,CAAC,CAAE,KAAK,CACRE,CAAC,CAAE,KACL,CAAC,CACD,KAAM,CAAAW,QAAQ,CAAG,CACfb,CAAC,CAAEf,aAAa,CAChBiB,CAAC,CAAEjB,aACL,CAAC,CACD,GAAID,QAAQ,GAAK,GAAG,CAAE,CACpB6B,QAAQ,CAACX,CAAC,CAAG,CAAC,CACdW,QAAQ,CAACb,CAAC,EAAId,4BAA4B,CAAGwB,QAAQ,CAACV,CAAC,CAAGL,gBAAgB,CAACa,KAAK,CAClF,CAAC,IAAM,IAAIxB,QAAQ,GAAK,GAAG,CAAE,CAC3B6B,QAAQ,CAACb,CAAC,CAAG,CAAC,CACda,QAAQ,CAACX,CAAC,EAAIf,4BAA4B,CAAGuB,QAAQ,CAACR,CAAC,CAAGP,gBAAgB,CAACc,MAAM,CACnF,CAAC,IAAM,CACL,KAAM,CAAAK,iBAAiB,CAAGT,IAAI,CAACU,IAAI,CAACL,QAAQ,CAACV,CAAC,EAAI,CAAC,CAAGU,QAAQ,CAACR,CAAC,EAAI,CAAC,CAAC,CACtE,KAAM,CAAAc,cAAc,CAAGX,IAAI,CAACU,IAAI,CAACpB,gBAAgB,CAACa,KAAK,EAAI,CAAC,CAAGb,gBAAgB,CAACc,MAAM,EAAI,CAAC,CAAC,CAC5F,KAAM,CAAAQ,oBAAoB,CAAGhC,aAAa,CAAGG,6BAA6B,CAAG0B,iBAAiB,CAAGE,cAAc,CAC/G,GAAIX,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACC,CAAC,CAAC,CAAGK,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACG,CAAC,CAAC,CAAE,CAC/DW,QAAQ,CAACb,CAAC,CAAGiB,oBAAoB,CACjCJ,QAAQ,CAACX,CAAC,CAAGe,oBAAoB,CAAGZ,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACG,CAAC,CAAGH,gBAAgB,CAACC,CAAC,CAAC,CACvF,CAAC,IAAM,CACLa,QAAQ,CAACb,CAAC,CAAGiB,oBAAoB,CAAGZ,IAAI,CAACM,GAAG,CAACZ,gBAAgB,CAACC,CAAC,CAAGD,gBAAgB,CAACG,CAAC,CAAC,CACrFW,QAAQ,CAACX,CAAC,CAAGe,oBAAoB,CACnC,CACF,CACA,GAAInB,oBAAoB,CAAE,CACxB,QAAS,CAAAoB,8BAA8BA,CAAA,CAAG,CACxC,GAAIlC,QAAQ,GAAK,GAAG,CAAE,CACpB,MAAO,CAAA4B,yBAAyB,CAACZ,CAAC,CACpC,CAAC,IAAM,IAAIhB,QAAQ,GAAK,GAAG,CAAE,CAC3B,MAAO,CAAA4B,yBAAyB,CAACV,CAAC,CACpC,CAAC,IAAM,CACL,MAAO,CAAAU,yBAAyB,CAACZ,CAAC,EAAIY,yBAAyB,CAACV,CAAC,CACnE,CACF,CACA,QAAS,CAAAiB,qCAAqCA,CAAA,CAAG,CAC/CrC,KAAK,CAACmB,YAAY,CAAGG,SAAS,CAACJ,CAAC,CAAGL,gBAAgB,CAACa,KAAK,CACzD1B,KAAK,CAACqB,YAAY,CAAGC,SAAS,CAACF,CAAC,CAAGP,gBAAgB,CAACc,MAAM,CAC1DpB,2BAA2B,CAACN,sBAAsB,CAAED,KAAK,CAAC,CAC5D,CACA,KAAM,CAAAsC,YAAY,CAAG,QAAAA,CAAA,CAAM,CACzB,KAAM,CAAAC,QAAQ,CAAG,CACfrB,CAAC,CAAEV,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACV,CAAC,CAAEa,QAAQ,CAACb,CAAC,CAAC,CAChEE,CAAC,CAAEZ,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACR,CAAC,CAAEW,QAAQ,CAACX,CAAC,CACjE,CAAC,CACDpB,KAAK,CAACmB,YAAY,CAAGF,gBAAgB,CAACC,CAAC,CAAGI,SAAS,CAACJ,CAAC,CAAGU,QAAQ,CAACV,CAAC,CAAGT,MAAM,CAAC8B,QAAQ,CAACrB,CAAC,CAAC,CACvFlB,KAAK,CAACqB,YAAY,CAAGJ,gBAAgB,CAACG,CAAC,CAAGE,SAAS,CAACF,CAAC,CAAGQ,QAAQ,CAACR,CAAC,CAAGX,MAAM,CAAC8B,QAAQ,CAACnB,CAAC,CAAC,CACvF,GAAIE,SAAS,CAACJ,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIlB,KAAK,CAACmB,YAAY,EAAI,CAAC,CAAE,CAC3BW,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAG,CAAC,CACxB,CACF,CAAC,IAAM,CACL,GAAInB,KAAK,CAACmB,YAAY,EAAI,CAAC,CAAE,CAC3BW,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAG,CAAC,CACxB,CACF,CACA,GAAIG,SAAS,CAACF,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIpB,KAAK,CAACqB,YAAY,EAAI,CAAC,CAAE,CAC3BS,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAG,CAAC,CACxB,CACF,CAAC,IAAM,CACL,GAAIrB,KAAK,CAACqB,YAAY,EAAI,CAAC,CAAE,CAC3BS,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAG,CAAC,CACxB,CACF,CACAX,UAAU,CAACT,sBAAsB,CAAED,KAAK,CAAC,CACzC,KAAM,CAAAwC,QAAQ,CAAGJ,8BAA8B,CAAC,CAAC,CACjD,GAAII,QAAQ,CAAE,CACZH,qCAAqC,CAAC,CAAC,CACzC,CACA1B,sBAAsB,CAAC2B,YAAY,CAAEE,QAAQ,CAAEvC,sBAAsB,CAAED,KAAK,CAAEgB,oBAAoB,CAAC,CACrG,CAAC,CACD,MAAO,CAAAsB,YAAY,CACrB,CAAC,IAAM,CACL,KAAM,CAAAA,YAAY,CAAG,QAAAA,CAAA,CAAM,CACzB,KAAM,CAAAC,QAAQ,CAAG,CACfrB,CAAC,CAAEV,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACV,CAAC,CAAEa,QAAQ,CAACb,CAAC,CAAC,CAChEE,CAAC,CAAEZ,qBAAqB,CAACM,cAAc,CAAEc,QAAQ,CAACR,CAAC,CAAEW,QAAQ,CAACX,CAAC,CACjE,CAAC,CACDpB,KAAK,CAACmB,YAAY,CAAGF,gBAAgB,CAACC,CAAC,CAAGI,SAAS,CAACJ,CAAC,CAAGU,QAAQ,CAACV,CAAC,CAAGT,MAAM,CAAC8B,QAAQ,CAACrB,CAAC,CAAC,CACvFlB,KAAK,CAACqB,YAAY,CAAGJ,gBAAgB,CAACG,CAAC,CAAGE,SAAS,CAACF,CAAC,CAAGQ,QAAQ,CAACR,CAAC,CAAGX,MAAM,CAAC8B,QAAQ,CAACnB,CAAC,CAAC,CACvF,GAAIE,SAAS,CAACJ,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIlB,KAAK,CAACmB,YAAY,EAAIN,gBAAgB,CAACa,KAAK,CAAE,CAChDI,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAGN,gBAAgB,CAACa,KAAK,CAC7C,CACF,CAAC,IAAM,CACL,GAAI1B,KAAK,CAACmB,YAAY,EAAI,CAACN,gBAAgB,CAACa,KAAK,CAAE,CACjDI,yBAAyB,CAACZ,CAAC,CAAG,IAAI,CAClClB,KAAK,CAACmB,YAAY,CAAG,CAACN,gBAAgB,CAACa,KAAK,CAC9C,CACF,CACA,GAAIJ,SAAS,CAACF,CAAC,CAAG,CAAC,CAAE,CACnB,GAAIpB,KAAK,CAACqB,YAAY,EAAIR,gBAAgB,CAACc,MAAM,CAAE,CACjDG,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAGR,gBAAgB,CAACc,MAAM,CAC9C,CACF,CAAC,IAAM,CACL,GAAI3B,KAAK,CAACqB,YAAY,EAAI,CAACR,gBAAgB,CAACc,MAAM,CAAE,CAClDG,yBAAyB,CAACV,CAAC,CAAG,IAAI,CAClCpB,KAAK,CAACqB,YAAY,CAAG,CAACR,gBAAgB,CAACc,MAAM,CAC/C,CACF,CACAjB,UAAU,CAACT,sBAAsB,CAAED,KAAK,CAAC,CACzCW,sBAAsB,CAAC2B,YAAY,CAAER,yBAAyB,CAACZ,CAAC,EAAIY,yBAAyB,CAACV,CAAC,CAAEnB,sBAAsB,CAAED,KAAK,CAAEgB,oBAAoB,CAAC,CACvJ,CAAC,CACD,MAAO,CAAAsB,YAAY,CACrB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const getSwipeSimulator = exports.getSwipeSimulator = function () {\n    const _e = [new global.Error(), -10, -27];\n    const getSwipeSimulator = function (event, screenTransitionConfig, lockAxis) {\n      const screenDimensions = screenTransitionConfig.screenDimensions;\n      const startTimestamp = _getAnimationTimestamp();\n      const {\n        isTransitionCanceled\n      } = screenTransitionConfig;\n      const startingPosition = {\n        x: event.translationX,\n        y: event.translationY\n      };\n      const direction = {\n        x: Math.sign(event.translationX),\n        y: Math.sign(event.translationY)\n      };\n      const finalPosition = isTransitionCanceled ? {\n        x: 0,\n        y: 0\n      } : {\n        x: direction.x * screenDimensions.width,\n        y: direction.y * screenDimensions.height\n      };\n      const distance = {\n        x: Math.abs(finalPosition.x - startingPosition.x),\n        y: Math.abs(finalPosition.y - startingPosition.y)\n      };\n      const didScreenReachDestination = {\n        x: false,\n        y: false\n      };\n      const velocity = {\n        x: BASE_VELOCITY,\n        y: BASE_VELOCITY\n      };\n      if (lockAxis === 'x') {\n        velocity.y = 0;\n        velocity.x += ADDITIONAL_VELOCITY_FACTOR_X * distance.x / screenDimensions.width;\n      } else if (lockAxis === 'y') {\n        velocity.x = 0;\n        velocity.y += ADDITIONAL_VELOCITY_FACTOR_Y * distance.y / screenDimensions.height;\n      } else {\n        const euclideanDistance = Math.sqrt(distance.x ** 2 + distance.y ** 2);\n        const screenDiagonal = Math.sqrt(screenDimensions.width ** 2 + screenDimensions.height ** 2);\n        const velocityVectorLength = BASE_VELOCITY + ADDITIONAL_VELOCITY_FACTOR_XY * euclideanDistance / screenDiagonal;\n        if (Math.abs(startingPosition.x) > Math.abs(startingPosition.y)) {\n          velocity.x = velocityVectorLength;\n          velocity.y = velocityVectorLength * Math.abs(startingPosition.y / startingPosition.x);\n        } else {\n          velocity.x = velocityVectorLength * Math.abs(startingPosition.x / startingPosition.y);\n          velocity.y = velocityVectorLength;\n        }\n      }\n      if (isTransitionCanceled) {\n        function didScreenReachDestinationCheck() {\n          if (lockAxis === 'x') {\n            return didScreenReachDestination.x;\n          } else if (lockAxis === 'y') {\n            return didScreenReachDestination.y;\n          } else {\n            return didScreenReachDestination.x && didScreenReachDestination.y;\n          }\n        }\n        function restoreOriginalStyleForBelowTopScreen() {\n          event.translationX = direction.x * screenDimensions.width;\n          event.translationY = direction.y * screenDimensions.height;\n          (0, _styleUpdater.applyStyleForBelowTopScreen)(screenTransitionConfig, event);\n        }\n        const computeFrame = () => {\n          const progress = {\n            x: computeEasingProgress(startTimestamp, distance.x, velocity.x),\n            y: computeEasingProgress(startTimestamp, distance.y, velocity.y)\n          };\n          event.translationX = startingPosition.x - direction.x * distance.x * easing(progress.x);\n          event.translationY = startingPosition.y - direction.y * distance.y * easing(progress.y);\n          if (direction.x > 0) {\n            if (event.translationX <= 0) {\n              didScreenReachDestination.x = true;\n              event.translationX = 0;\n            }\n          } else {\n            if (event.translationX >= 0) {\n              didScreenReachDestination.x = true;\n              event.translationX = 0;\n            }\n          }\n          if (direction.y > 0) {\n            if (event.translationY <= 0) {\n              didScreenReachDestination.y = true;\n              event.translationY = 0;\n            }\n          } else {\n            if (event.translationY >= 0) {\n              didScreenReachDestination.y = true;\n              event.translationY = 0;\n            }\n          }\n          (0, _styleUpdater.applyStyle)(screenTransitionConfig, event);\n          const finished = didScreenReachDestinationCheck();\n          if (finished) {\n            restoreOriginalStyleForBelowTopScreen();\n          }\n          maybeScheduleNextFrame(computeFrame, finished, screenTransitionConfig, event, isTransitionCanceled);\n        };\n        return computeFrame;\n      } else {\n        const computeFrame = () => {\n          const progress = {\n            x: computeEasingProgress(startTimestamp, distance.x, velocity.x),\n            y: computeEasingProgress(startTimestamp, distance.y, velocity.y)\n          };\n          event.translationX = startingPosition.x + direction.x * distance.x * easing(progress.x);\n          event.translationY = startingPosition.y + direction.y * distance.y * easing(progress.y);\n          if (direction.x > 0) {\n            if (event.translationX >= screenDimensions.width) {\n              didScreenReachDestination.x = true;\n              event.translationX = screenDimensions.width;\n            }\n          } else {\n            if (event.translationX <= -screenDimensions.width) {\n              didScreenReachDestination.x = true;\n              event.translationX = -screenDimensions.width;\n            }\n          }\n          if (direction.y > 0) {\n            if (event.translationY >= screenDimensions.height) {\n              didScreenReachDestination.y = true;\n              event.translationY = screenDimensions.height;\n            }\n          } else {\n            if (event.translationY <= -screenDimensions.height) {\n              didScreenReachDestination.y = true;\n              event.translationY = -screenDimensions.height;\n            }\n          }\n          (0, _styleUpdater.applyStyle)(screenTransitionConfig, event);\n          maybeScheduleNextFrame(computeFrame, didScreenReachDestination.x || didScreenReachDestination.y, screenTransitionConfig, event, isTransitionCanceled);\n        };\n        return computeFrame;\n      }\n    };\n    getSwipeSimulator.__closure = {\n      BASE_VELOCITY,\n      ADDITIONAL_VELOCITY_FACTOR_X,\n      ADDITIONAL_VELOCITY_FACTOR_Y,\n      ADDITIONAL_VELOCITY_FACTOR_XY,\n      applyStyleForBelowTopScreen: _styleUpdater.applyStyleForBelowTopScreen,\n      computeEasingProgress,\n      easing,\n      applyStyle: _styleUpdater.applyStyle,\n      maybeScheduleNextFrame\n    };\n    getSwipeSimulator.__workletHash = 15110927952447;\n    getSwipeSimulator.__initData = _worklet_15110927952447_init_data;\n    getSwipeSimulator.__stackDetails = _e;\n    return getSwipeSimulator;\n  }();\n});", "lineCount": 267, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "getSwipeSimulator"], [7, 27, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_RNScreensTurboModule"], [8, 27, 3, 0], [8, 30, 3, 0, "require"], [8, 37, 3, 0], [8, 38, 3, 0, "_dependencyMap"], [8, 52, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_styleUpdater"], [9, 19, 4, 0], [9, 22, 4, 0, "require"], [9, 29, 4, 0], [9, 30, 4, 0, "_dependencyMap"], [9, 44, 4, 0], [10, 2, 5, 0], [10, 8, 5, 6, "BASE_VELOCITY"], [10, 21, 5, 19], [10, 24, 5, 22], [10, 27, 5, 25], [11, 2, 6, 0], [11, 8, 6, 6, "ADDITIONAL_VELOCITY_FACTOR_X"], [11, 36, 6, 34], [11, 39, 6, 37], [11, 42, 6, 40], [12, 2, 7, 0], [12, 8, 7, 6, "ADDITIONAL_VELOCITY_FACTOR_Y"], [12, 36, 7, 34], [12, 39, 7, 37], [12, 42, 7, 40], [13, 2, 8, 0], [13, 8, 8, 6, "ADDITIONAL_VELOCITY_FACTOR_XY"], [13, 37, 8, 35], [13, 40, 8, 38], [13, 43, 8, 41], [14, 2, 8, 42], [14, 8, 8, 42, "_worklet_14551241534927_init_data"], [14, 41, 8, 42], [15, 4, 8, 42, "code"], [15, 8, 8, 42], [16, 4, 8, 42, "location"], [16, 12, 8, 42], [17, 4, 8, 42, "sourceMap"], [17, 13, 8, 42], [18, 4, 8, 42, "version"], [18, 11, 8, 42], [19, 2, 8, 42], [20, 2, 8, 42], [20, 8, 8, 42, "computeEasingProgress"], [20, 29, 8, 42], [20, 32, 9, 0], [21, 4, 9, 0], [21, 10, 9, 0, "_e"], [21, 12, 9, 0], [21, 20, 9, 0, "global"], [21, 26, 9, 0], [21, 27, 9, 0, "Error"], [21, 32, 9, 0], [22, 4, 9, 0], [22, 10, 9, 0, "computeEasingProgress"], [22, 31, 9, 0], [22, 43, 9, 0, "computeEasingProgress"], [22, 44, 9, 31, "startingTimestamp"], [22, 61, 9, 48], [22, 63, 9, 50, "distance"], [22, 71, 9, 58], [22, 73, 9, 60, "velocity"], [22, 81, 9, 68], [22, 83, 9, 70], [23, 6, 12, 2], [23, 10, 12, 6, "Math"], [23, 14, 12, 10], [23, 15, 12, 11, "abs"], [23, 18, 12, 14], [23, 19, 12, 15, "distance"], [23, 27, 12, 23], [23, 28, 12, 24], [23, 31, 12, 27], [23, 32, 12, 28], [23, 34, 12, 30], [24, 8, 13, 4], [24, 15, 13, 11], [24, 16, 13, 12], [25, 6, 14, 2], [26, 6, 15, 2], [26, 12, 15, 8, "elapsedTime"], [26, 23, 15, 19], [26, 26, 15, 22], [26, 27, 15, 23, "_getAnimationTimestamp"], [26, 49, 15, 45], [26, 50, 15, 46], [26, 51, 15, 47], [26, 54, 15, 50, "startingTimestamp"], [26, 71, 15, 67], [26, 75, 15, 71], [26, 79, 15, 75], [27, 6, 16, 2], [27, 12, 16, 8, "currentPosition"], [27, 27, 16, 23], [27, 30, 16, 26, "velocity"], [27, 38, 16, 34], [27, 41, 16, 37, "elapsedTime"], [27, 52, 16, 48], [28, 6, 17, 2], [28, 12, 17, 8, "progress"], [28, 20, 17, 16], [28, 23, 17, 19, "currentPosition"], [28, 38, 17, 34], [28, 41, 17, 37, "distance"], [28, 49, 17, 45], [29, 6, 18, 2], [29, 13, 18, 9, "progress"], [29, 21, 18, 17], [30, 4, 19, 0], [30, 5, 19, 1], [31, 4, 19, 1, "computeEasingProgress"], [31, 25, 19, 1], [31, 26, 19, 1, "__closure"], [31, 35, 19, 1], [32, 4, 19, 1, "computeEasingProgress"], [32, 25, 19, 1], [32, 26, 19, 1, "__workletHash"], [32, 39, 19, 1], [33, 4, 19, 1, "computeEasingProgress"], [33, 25, 19, 1], [33, 26, 19, 1, "__initData"], [33, 36, 19, 1], [33, 39, 19, 1, "_worklet_14551241534927_init_data"], [33, 72, 19, 1], [34, 4, 19, 1, "computeEasingProgress"], [34, 25, 19, 1], [34, 26, 19, 1, "__stackDetails"], [34, 40, 19, 1], [34, 43, 19, 1, "_e"], [34, 45, 19, 1], [35, 4, 19, 1], [35, 11, 19, 1, "computeEasingProgress"], [35, 32, 19, 1], [36, 2, 19, 1], [36, 3, 9, 0], [37, 2, 9, 0], [37, 8, 9, 0, "_worklet_10786758827735_init_data"], [37, 41, 9, 0], [38, 4, 9, 0, "code"], [38, 8, 9, 0], [39, 4, 9, 0, "location"], [39, 12, 9, 0], [40, 4, 9, 0, "sourceMap"], [40, 13, 9, 0], [41, 4, 9, 0, "version"], [41, 11, 9, 0], [42, 2, 9, 0], [43, 2, 9, 0], [43, 8, 9, 0, "easing"], [43, 14, 9, 0], [43, 17, 20, 0], [44, 4, 20, 0], [44, 10, 20, 0, "_e"], [44, 12, 20, 0], [44, 20, 20, 0, "global"], [44, 26, 20, 0], [44, 27, 20, 0, "Error"], [44, 32, 20, 0], [45, 4, 20, 0], [45, 10, 20, 0, "easing"], [45, 16, 20, 0], [45, 28, 20, 0, "easing"], [45, 29, 20, 16, "x"], [45, 30, 20, 17], [45, 32, 20, 19], [46, 6, 23, 2], [47, 6, 24, 2], [47, 13, 24, 9], [47, 14, 24, 10], [47, 17, 24, 13, "Math"], [47, 21, 24, 17], [47, 22, 24, 18, "pow"], [47, 25, 24, 21], [47, 26, 24, 22], [47, 27, 24, 23], [47, 30, 24, 26, "x"], [47, 31, 24, 27], [47, 33, 24, 29], [47, 34, 24, 30], [47, 35, 24, 31], [48, 4, 25, 0], [48, 5, 25, 1], [49, 4, 25, 1, "easing"], [49, 10, 25, 1], [49, 11, 25, 1, "__closure"], [49, 20, 25, 1], [50, 4, 25, 1, "easing"], [50, 10, 25, 1], [50, 11, 25, 1, "__workletHash"], [50, 24, 25, 1], [51, 4, 25, 1, "easing"], [51, 10, 25, 1], [51, 11, 25, 1, "__initData"], [51, 21, 25, 1], [51, 24, 25, 1, "_worklet_10786758827735_init_data"], [51, 57, 25, 1], [52, 4, 25, 1, "easing"], [52, 10, 25, 1], [52, 11, 25, 1, "__stackDetails"], [52, 25, 25, 1], [52, 28, 25, 1, "_e"], [52, 30, 25, 1], [53, 4, 25, 1], [53, 11, 25, 1, "easing"], [53, 17, 25, 1], [54, 2, 25, 1], [54, 3, 20, 0], [55, 2, 20, 0], [55, 8, 20, 0, "_worklet_1991703960680_init_data"], [55, 40, 20, 0], [56, 4, 20, 0, "code"], [56, 8, 20, 0], [57, 4, 20, 0, "location"], [57, 12, 20, 0], [58, 4, 20, 0, "sourceMap"], [58, 13, 20, 0], [59, 4, 20, 0, "version"], [59, 11, 20, 0], [60, 2, 20, 0], [61, 2, 20, 0], [61, 8, 20, 0, "computeProgress"], [61, 23, 20, 0], [61, 26, 26, 0], [62, 4, 26, 0], [62, 10, 26, 0, "_e"], [62, 12, 26, 0], [62, 20, 26, 0, "global"], [62, 26, 26, 0], [62, 27, 26, 0, "Error"], [62, 32, 26, 0], [63, 4, 26, 0], [63, 10, 26, 0, "computeProgress"], [63, 25, 26, 0], [63, 37, 26, 0, "computeProgress"], [63, 38, 26, 25, "screenTransitionConfig"], [63, 60, 26, 47], [63, 62, 26, 49, "event"], [63, 67, 26, 54], [63, 69, 26, 56, "isTransitionCanceled"], [63, 89, 26, 76], [63, 91, 26, 78], [64, 6, 29, 2], [64, 12, 29, 8, "screenDimensions"], [64, 28, 29, 24], [64, 31, 29, 27, "screenTransitionConfig"], [64, 53, 29, 49], [64, 54, 29, 50, "screenDimensions"], [64, 70, 29, 66], [65, 6, 30, 2], [65, 12, 30, 8, "progressX"], [65, 21, 30, 17], [65, 24, 30, 20, "Math"], [65, 28, 30, 24], [65, 29, 30, 25, "abs"], [65, 32, 30, 28], [65, 33, 30, 29, "event"], [65, 38, 30, 34], [65, 39, 30, 35, "translationX"], [65, 51, 30, 47], [65, 54, 30, 50, "screenDimensions"], [65, 70, 30, 66], [65, 71, 30, 67, "width"], [65, 76, 30, 72], [65, 77, 30, 73], [66, 6, 31, 2], [66, 12, 31, 8, "progressY"], [66, 21, 31, 17], [66, 24, 31, 20, "Math"], [66, 28, 31, 24], [66, 29, 31, 25, "abs"], [66, 32, 31, 28], [66, 33, 31, 29, "event"], [66, 38, 31, 34], [66, 39, 31, 35, "translationY"], [66, 51, 31, 47], [66, 54, 31, 50, "screenDimensions"], [66, 70, 31, 66], [66, 71, 31, 67, "height"], [66, 77, 31, 73], [66, 78, 31, 74], [67, 6, 32, 2], [67, 12, 32, 8, "max<PERSON>rogress"], [67, 23, 32, 19], [67, 26, 32, 22, "Math"], [67, 30, 32, 26], [67, 31, 32, 27, "max"], [67, 34, 32, 30], [67, 35, 32, 31, "progressX"], [67, 44, 32, 40], [67, 46, 32, 42, "progressY"], [67, 55, 32, 51], [67, 56, 32, 52], [68, 6, 33, 2], [68, 12, 33, 8, "progress"], [68, 20, 33, 16], [68, 23, 33, 19, "isTransitionCanceled"], [68, 43, 33, 39], [68, 46, 33, 42, "max<PERSON>rogress"], [68, 57, 33, 53], [68, 60, 33, 56], [68, 61, 33, 57], [68, 64, 33, 60, "max<PERSON>rogress"], [68, 75, 33, 71], [69, 6, 34, 2], [69, 13, 34, 9, "progress"], [69, 21, 34, 17], [70, 4, 35, 0], [70, 5, 35, 1], [71, 4, 35, 1, "computeProgress"], [71, 19, 35, 1], [71, 20, 35, 1, "__closure"], [71, 29, 35, 1], [72, 4, 35, 1, "computeProgress"], [72, 19, 35, 1], [72, 20, 35, 1, "__workletHash"], [72, 33, 35, 1], [73, 4, 35, 1, "computeProgress"], [73, 19, 35, 1], [73, 20, 35, 1, "__initData"], [73, 30, 35, 1], [73, 33, 35, 1, "_worklet_1991703960680_init_data"], [73, 65, 35, 1], [74, 4, 35, 1, "computeProgress"], [74, 19, 35, 1], [74, 20, 35, 1, "__stackDetails"], [74, 34, 35, 1], [74, 37, 35, 1, "_e"], [74, 39, 35, 1], [75, 4, 35, 1], [75, 11, 35, 1, "computeProgress"], [75, 26, 35, 1], [76, 2, 35, 1], [76, 3, 26, 0], [77, 2, 26, 0], [77, 8, 26, 0, "_worklet_5191610802729_init_data"], [77, 40, 26, 0], [78, 4, 26, 0, "code"], [78, 8, 26, 0], [79, 4, 26, 0, "location"], [79, 12, 26, 0], [80, 4, 26, 0, "sourceMap"], [80, 13, 26, 0], [81, 4, 26, 0, "version"], [81, 11, 26, 0], [82, 2, 26, 0], [83, 2, 26, 0], [83, 8, 26, 0, "maybeScheduleNextFrame"], [83, 30, 26, 0], [83, 33, 36, 0], [84, 4, 36, 0], [84, 10, 36, 0, "_e"], [84, 12, 36, 0], [84, 20, 36, 0, "global"], [84, 26, 36, 0], [84, 27, 36, 0, "Error"], [84, 32, 36, 0], [85, 4, 36, 0], [85, 10, 36, 0, "maybeScheduleNextFrame"], [85, 32, 36, 0], [85, 44, 36, 0, "maybeScheduleNextFrame"], [85, 45, 36, 32, "step"], [85, 49, 36, 36], [85, 51, 36, 38, "didScreenReachDestination"], [85, 76, 36, 63], [85, 78, 36, 65, "screenTransitionConfig"], [85, 100, 36, 87], [85, 102, 36, 89, "event"], [85, 107, 36, 94], [85, 109, 36, 96, "isTransitionCanceled"], [85, 129, 36, 116], [85, 131, 36, 118], [86, 6, 39, 2], [86, 10, 39, 6], [86, 11, 39, 7, "didScreenReachDestination"], [86, 36, 39, 32], [86, 38, 39, 34], [87, 8, 40, 4], [87, 14, 40, 10, "stackTag"], [87, 22, 40, 18], [87, 25, 40, 21, "screenTransitionConfig"], [87, 47, 40, 43], [87, 48, 40, 44, "stackTag"], [87, 56, 40, 52], [88, 8, 41, 4], [88, 14, 41, 10, "progress"], [88, 22, 41, 18], [88, 25, 41, 21, "computeProgress"], [88, 40, 41, 36], [88, 41, 41, 37, "screenTransitionConfig"], [88, 63, 41, 59], [88, 65, 41, 61, "event"], [88, 70, 41, 66], [88, 72, 41, 68, "isTransitionCanceled"], [88, 92, 41, 88], [88, 93, 41, 89], [89, 8, 42, 4, "RNScreensTurboModule"], [89, 50, 42, 24], [89, 51, 42, 25, "updateTransition"], [89, 67, 42, 41], [89, 68, 42, 42, "stackTag"], [89, 76, 42, 50], [89, 78, 42, 52, "progress"], [89, 86, 42, 60], [89, 87, 42, 61], [90, 8, 43, 4, "requestAnimationFrame"], [90, 29, 43, 25], [90, 30, 43, 26, "step"], [90, 34, 43, 30], [90, 35, 43, 31], [91, 6, 44, 2], [91, 7, 44, 3], [91, 13, 44, 9], [92, 8, 45, 4, "screenTransitionConfig"], [92, 30, 45, 26], [92, 31, 45, 27, "onFinishAnimation"], [92, 48, 45, 44], [92, 51, 45, 47], [92, 52, 45, 48], [93, 6, 46, 2], [94, 4, 47, 0], [94, 5, 47, 1], [95, 4, 47, 1, "maybeScheduleNextFrame"], [95, 26, 47, 1], [95, 27, 47, 1, "__closure"], [95, 36, 47, 1], [96, 6, 47, 1, "computeProgress"], [96, 21, 47, 1], [97, 6, 47, 1, "RNScreensTurboModule"], [97, 26, 47, 1], [97, 28, 42, 4, "RNScreensTurboModule"], [98, 4, 42, 24], [99, 4, 42, 24, "maybeScheduleNextFrame"], [99, 26, 42, 24], [99, 27, 42, 24, "__workletHash"], [99, 40, 42, 24], [100, 4, 42, 24, "maybeScheduleNextFrame"], [100, 26, 42, 24], [100, 27, 42, 24, "__initData"], [100, 37, 42, 24], [100, 40, 42, 24, "_worklet_5191610802729_init_data"], [100, 72, 42, 24], [101, 4, 42, 24, "maybeScheduleNextFrame"], [101, 26, 42, 24], [101, 27, 42, 24, "__stackDetails"], [101, 41, 42, 24], [101, 44, 42, 24, "_e"], [101, 46, 42, 24], [102, 4, 42, 24], [102, 11, 42, 24, "maybeScheduleNextFrame"], [102, 33, 42, 24], [103, 2, 42, 24], [103, 3, 36, 0], [104, 2, 36, 0], [104, 8, 36, 0, "_worklet_15110927952447_init_data"], [104, 41, 36, 0], [105, 4, 36, 0, "code"], [105, 8, 36, 0], [106, 4, 36, 0, "location"], [106, 12, 36, 0], [107, 4, 36, 0, "sourceMap"], [107, 13, 36, 0], [108, 4, 36, 0, "version"], [108, 11, 36, 0], [109, 2, 36, 0], [110, 2, 36, 0], [110, 8, 36, 0, "getSwipeSimulator"], [110, 25, 36, 0], [110, 28, 36, 0, "exports"], [110, 35, 36, 0], [110, 36, 36, 0, "getSwipeSimulator"], [110, 53, 36, 0], [110, 56, 48, 7], [111, 4, 48, 7], [111, 10, 48, 7, "_e"], [111, 12, 48, 7], [111, 20, 48, 7, "global"], [111, 26, 48, 7], [111, 27, 48, 7, "Error"], [111, 32, 48, 7], [112, 4, 48, 7], [112, 10, 48, 7, "getSwipeSimulator"], [112, 27, 48, 7], [112, 39, 48, 7, "getSwipeSimulator"], [112, 40, 48, 34, "event"], [112, 45, 48, 39], [112, 47, 48, 41, "screenTransitionConfig"], [112, 69, 48, 63], [112, 71, 48, 65, "lockAxis"], [112, 79, 48, 73], [112, 81, 48, 75], [113, 6, 51, 2], [113, 12, 51, 8, "screenDimensions"], [113, 28, 51, 24], [113, 31, 51, 27, "screenTransitionConfig"], [113, 53, 51, 49], [113, 54, 51, 50, "screenDimensions"], [113, 70, 51, 66], [114, 6, 52, 2], [114, 12, 52, 8, "startTimestamp"], [114, 26, 52, 22], [114, 29, 52, 25, "_getAnimationTimestamp"], [114, 51, 52, 47], [114, 52, 52, 48], [114, 53, 52, 49], [115, 6, 53, 2], [115, 12, 53, 8], [116, 8, 54, 4, "isTransitionCanceled"], [117, 6, 55, 2], [117, 7, 55, 3], [117, 10, 55, 6, "screenTransitionConfig"], [117, 32, 55, 28], [118, 6, 56, 2], [118, 12, 56, 8, "startingPosition"], [118, 28, 56, 24], [118, 31, 56, 27], [119, 8, 57, 4, "x"], [119, 9, 57, 5], [119, 11, 57, 7, "event"], [119, 16, 57, 12], [119, 17, 57, 13, "translationX"], [119, 29, 57, 25], [120, 8, 58, 4, "y"], [120, 9, 58, 5], [120, 11, 58, 7, "event"], [120, 16, 58, 12], [120, 17, 58, 13, "translationY"], [121, 6, 59, 2], [121, 7, 59, 3], [122, 6, 60, 2], [122, 12, 60, 8, "direction"], [122, 21, 60, 17], [122, 24, 60, 20], [123, 8, 61, 4, "x"], [123, 9, 61, 5], [123, 11, 61, 7, "Math"], [123, 15, 61, 11], [123, 16, 61, 12, "sign"], [123, 20, 61, 16], [123, 21, 61, 17, "event"], [123, 26, 61, 22], [123, 27, 61, 23, "translationX"], [123, 39, 61, 35], [123, 40, 61, 36], [124, 8, 62, 4, "y"], [124, 9, 62, 5], [124, 11, 62, 7, "Math"], [124, 15, 62, 11], [124, 16, 62, 12, "sign"], [124, 20, 62, 16], [124, 21, 62, 17, "event"], [124, 26, 62, 22], [124, 27, 62, 23, "translationY"], [124, 39, 62, 35], [125, 6, 63, 2], [125, 7, 63, 3], [126, 6, 64, 2], [126, 12, 64, 8, "finalPosition"], [126, 25, 64, 21], [126, 28, 64, 24, "isTransitionCanceled"], [126, 48, 64, 44], [126, 51, 64, 47], [127, 8, 65, 4, "x"], [127, 9, 65, 5], [127, 11, 65, 7], [127, 12, 65, 8], [128, 8, 66, 4, "y"], [128, 9, 66, 5], [128, 11, 66, 7], [129, 6, 67, 2], [129, 7, 67, 3], [129, 10, 67, 6], [130, 8, 68, 4, "x"], [130, 9, 68, 5], [130, 11, 68, 7, "direction"], [130, 20, 68, 16], [130, 21, 68, 17, "x"], [130, 22, 68, 18], [130, 25, 68, 21, "screenDimensions"], [130, 41, 68, 37], [130, 42, 68, 38, "width"], [130, 47, 68, 43], [131, 8, 69, 4, "y"], [131, 9, 69, 5], [131, 11, 69, 7, "direction"], [131, 20, 69, 16], [131, 21, 69, 17, "y"], [131, 22, 69, 18], [131, 25, 69, 21, "screenDimensions"], [131, 41, 69, 37], [131, 42, 69, 38, "height"], [132, 6, 70, 2], [132, 7, 70, 3], [133, 6, 71, 2], [133, 12, 71, 8, "distance"], [133, 20, 71, 16], [133, 23, 71, 19], [134, 8, 72, 4, "x"], [134, 9, 72, 5], [134, 11, 72, 7, "Math"], [134, 15, 72, 11], [134, 16, 72, 12, "abs"], [134, 19, 72, 15], [134, 20, 72, 16, "finalPosition"], [134, 33, 72, 29], [134, 34, 72, 30, "x"], [134, 35, 72, 31], [134, 38, 72, 34, "startingPosition"], [134, 54, 72, 50], [134, 55, 72, 51, "x"], [134, 56, 72, 52], [134, 57, 72, 53], [135, 8, 73, 4, "y"], [135, 9, 73, 5], [135, 11, 73, 7, "Math"], [135, 15, 73, 11], [135, 16, 73, 12, "abs"], [135, 19, 73, 15], [135, 20, 73, 16, "finalPosition"], [135, 33, 73, 29], [135, 34, 73, 30, "y"], [135, 35, 73, 31], [135, 38, 73, 34, "startingPosition"], [135, 54, 73, 50], [135, 55, 73, 51, "y"], [135, 56, 73, 52], [136, 6, 74, 2], [136, 7, 74, 3], [137, 6, 75, 2], [137, 12, 75, 8, "didScreenReachDestination"], [137, 37, 75, 33], [137, 40, 75, 36], [138, 8, 76, 4, "x"], [138, 9, 76, 5], [138, 11, 76, 7], [138, 16, 76, 12], [139, 8, 77, 4, "y"], [139, 9, 77, 5], [139, 11, 77, 7], [140, 6, 78, 2], [140, 7, 78, 3], [141, 6, 79, 2], [141, 12, 79, 8, "velocity"], [141, 20, 79, 16], [141, 23, 79, 19], [142, 8, 80, 4, "x"], [142, 9, 80, 5], [142, 11, 80, 7, "BASE_VELOCITY"], [142, 24, 80, 20], [143, 8, 81, 4, "y"], [143, 9, 81, 5], [143, 11, 81, 7, "BASE_VELOCITY"], [144, 6, 82, 2], [144, 7, 82, 3], [145, 6, 83, 2], [145, 10, 83, 6, "lockAxis"], [145, 18, 83, 14], [145, 23, 83, 19], [145, 26, 83, 22], [145, 28, 83, 24], [146, 8, 84, 4, "velocity"], [146, 16, 84, 12], [146, 17, 84, 13, "y"], [146, 18, 84, 14], [146, 21, 84, 17], [146, 22, 84, 18], [147, 8, 85, 4, "velocity"], [147, 16, 85, 12], [147, 17, 85, 13, "x"], [147, 18, 85, 14], [147, 22, 85, 18, "ADDITIONAL_VELOCITY_FACTOR_X"], [147, 50, 85, 46], [147, 53, 85, 49, "distance"], [147, 61, 85, 57], [147, 62, 85, 58, "x"], [147, 63, 85, 59], [147, 66, 85, 62, "screenDimensions"], [147, 82, 85, 78], [147, 83, 85, 79, "width"], [147, 88, 85, 84], [148, 6, 86, 2], [148, 7, 86, 3], [148, 13, 86, 9], [148, 17, 86, 13, "lockAxis"], [148, 25, 86, 21], [148, 30, 86, 26], [148, 33, 86, 29], [148, 35, 86, 31], [149, 8, 87, 4, "velocity"], [149, 16, 87, 12], [149, 17, 87, 13, "x"], [149, 18, 87, 14], [149, 21, 87, 17], [149, 22, 87, 18], [150, 8, 88, 4, "velocity"], [150, 16, 88, 12], [150, 17, 88, 13, "y"], [150, 18, 88, 14], [150, 22, 88, 18, "ADDITIONAL_VELOCITY_FACTOR_Y"], [150, 50, 88, 46], [150, 53, 88, 49, "distance"], [150, 61, 88, 57], [150, 62, 88, 58, "y"], [150, 63, 88, 59], [150, 66, 88, 62, "screenDimensions"], [150, 82, 88, 78], [150, 83, 88, 79, "height"], [150, 89, 88, 85], [151, 6, 89, 2], [151, 7, 89, 3], [151, 13, 89, 9], [152, 8, 90, 4], [152, 14, 90, 10, "euclideanDistance"], [152, 31, 90, 27], [152, 34, 90, 30, "Math"], [152, 38, 90, 34], [152, 39, 90, 35, "sqrt"], [152, 43, 90, 39], [152, 44, 90, 40, "distance"], [152, 52, 90, 48], [152, 53, 90, 49, "x"], [152, 54, 90, 50], [152, 58, 90, 54], [152, 59, 90, 55], [152, 62, 90, 58, "distance"], [152, 70, 90, 66], [152, 71, 90, 67, "y"], [152, 72, 90, 68], [152, 76, 90, 72], [152, 77, 90, 73], [152, 78, 90, 74], [153, 8, 91, 4], [153, 14, 91, 10, "screenDiagonal"], [153, 28, 91, 24], [153, 31, 91, 27, "Math"], [153, 35, 91, 31], [153, 36, 91, 32, "sqrt"], [153, 40, 91, 36], [153, 41, 91, 37, "screenDimensions"], [153, 57, 91, 53], [153, 58, 91, 54, "width"], [153, 63, 91, 59], [153, 67, 91, 63], [153, 68, 91, 64], [153, 71, 91, 67, "screenDimensions"], [153, 87, 91, 83], [153, 88, 91, 84, "height"], [153, 94, 91, 90], [153, 98, 91, 94], [153, 99, 91, 95], [153, 100, 91, 96], [154, 8, 92, 4], [154, 14, 92, 10, "velocityVectorLength"], [154, 34, 92, 30], [154, 37, 92, 33, "BASE_VELOCITY"], [154, 50, 92, 46], [154, 53, 92, 49, "ADDITIONAL_VELOCITY_FACTOR_XY"], [154, 82, 92, 78], [154, 85, 92, 81, "euclideanDistance"], [154, 102, 92, 98], [154, 105, 92, 101, "screenDiagonal"], [154, 119, 92, 115], [155, 8, 93, 4], [155, 12, 93, 8, "Math"], [155, 16, 93, 12], [155, 17, 93, 13, "abs"], [155, 20, 93, 16], [155, 21, 93, 17, "startingPosition"], [155, 37, 93, 33], [155, 38, 93, 34, "x"], [155, 39, 93, 35], [155, 40, 93, 36], [155, 43, 93, 39, "Math"], [155, 47, 93, 43], [155, 48, 93, 44, "abs"], [155, 51, 93, 47], [155, 52, 93, 48, "startingPosition"], [155, 68, 93, 64], [155, 69, 93, 65, "y"], [155, 70, 93, 66], [155, 71, 93, 67], [155, 73, 93, 69], [156, 10, 94, 6, "velocity"], [156, 18, 94, 14], [156, 19, 94, 15, "x"], [156, 20, 94, 16], [156, 23, 94, 19, "velocityVectorLength"], [156, 43, 94, 39], [157, 10, 95, 6, "velocity"], [157, 18, 95, 14], [157, 19, 95, 15, "y"], [157, 20, 95, 16], [157, 23, 95, 19, "velocityVectorLength"], [157, 43, 95, 39], [157, 46, 95, 42, "Math"], [157, 50, 95, 46], [157, 51, 95, 47, "abs"], [157, 54, 95, 50], [157, 55, 95, 51, "startingPosition"], [157, 71, 95, 67], [157, 72, 95, 68, "y"], [157, 73, 95, 69], [157, 76, 95, 72, "startingPosition"], [157, 92, 95, 88], [157, 93, 95, 89, "x"], [157, 94, 95, 90], [157, 95, 95, 91], [158, 8, 96, 4], [158, 9, 96, 5], [158, 15, 96, 11], [159, 10, 97, 6, "velocity"], [159, 18, 97, 14], [159, 19, 97, 15, "x"], [159, 20, 97, 16], [159, 23, 97, 19, "velocityVectorLength"], [159, 43, 97, 39], [159, 46, 97, 42, "Math"], [159, 50, 97, 46], [159, 51, 97, 47, "abs"], [159, 54, 97, 50], [159, 55, 97, 51, "startingPosition"], [159, 71, 97, 67], [159, 72, 97, 68, "x"], [159, 73, 97, 69], [159, 76, 97, 72, "startingPosition"], [159, 92, 97, 88], [159, 93, 97, 89, "y"], [159, 94, 97, 90], [159, 95, 97, 91], [160, 10, 98, 6, "velocity"], [160, 18, 98, 14], [160, 19, 98, 15, "y"], [160, 20, 98, 16], [160, 23, 98, 19, "velocityVectorLength"], [160, 43, 98, 39], [161, 8, 99, 4], [162, 6, 100, 2], [163, 6, 101, 2], [163, 10, 101, 6, "isTransitionCanceled"], [163, 30, 101, 26], [163, 32, 101, 28], [164, 8, 102, 4], [164, 17, 102, 13, "didScreenReachDestinationCheck"], [164, 47, 102, 43, "didScreenReachDestinationCheck"], [164, 48, 102, 43], [164, 50, 102, 46], [165, 10, 103, 6], [165, 14, 103, 10, "lockAxis"], [165, 22, 103, 18], [165, 27, 103, 23], [165, 30, 103, 26], [165, 32, 103, 28], [166, 12, 104, 8], [166, 19, 104, 15, "didScreenReachDestination"], [166, 44, 104, 40], [166, 45, 104, 41, "x"], [166, 46, 104, 42], [167, 10, 105, 6], [167, 11, 105, 7], [167, 17, 105, 13], [167, 21, 105, 17, "lockAxis"], [167, 29, 105, 25], [167, 34, 105, 30], [167, 37, 105, 33], [167, 39, 105, 35], [168, 12, 106, 8], [168, 19, 106, 15, "didScreenReachDestination"], [168, 44, 106, 40], [168, 45, 106, 41, "y"], [168, 46, 106, 42], [169, 10, 107, 6], [169, 11, 107, 7], [169, 17, 107, 13], [170, 12, 108, 8], [170, 19, 108, 15, "didScreenReachDestination"], [170, 44, 108, 40], [170, 45, 108, 41, "x"], [170, 46, 108, 42], [170, 50, 108, 46, "didScreenReachDestination"], [170, 75, 108, 71], [170, 76, 108, 72, "y"], [170, 77, 108, 73], [171, 10, 109, 6], [172, 8, 110, 4], [173, 8, 111, 4], [173, 17, 111, 13, "restoreOriginalStyleForBelowTopScreen"], [173, 54, 111, 50, "restoreOriginalStyleForBelowTopScreen"], [173, 55, 111, 50], [173, 57, 111, 53], [174, 10, 112, 6, "event"], [174, 15, 112, 11], [174, 16, 112, 12, "translationX"], [174, 28, 112, 24], [174, 31, 112, 27, "direction"], [174, 40, 112, 36], [174, 41, 112, 37, "x"], [174, 42, 112, 38], [174, 45, 112, 41, "screenDimensions"], [174, 61, 112, 57], [174, 62, 112, 58, "width"], [174, 67, 112, 63], [175, 10, 113, 6, "event"], [175, 15, 113, 11], [175, 16, 113, 12, "translationY"], [175, 28, 113, 24], [175, 31, 113, 27, "direction"], [175, 40, 113, 36], [175, 41, 113, 37, "y"], [175, 42, 113, 38], [175, 45, 113, 41, "screenDimensions"], [175, 61, 113, 57], [175, 62, 113, 58, "height"], [175, 68, 113, 64], [176, 10, 114, 6], [176, 14, 114, 6, "applyStyleForBelowTopScreen"], [176, 55, 114, 33], [176, 57, 114, 34, "screenTransitionConfig"], [176, 79, 114, 56], [176, 81, 114, 58, "event"], [176, 86, 114, 63], [176, 87, 114, 64], [177, 8, 115, 4], [178, 8, 116, 4], [178, 14, 116, 10, "computeFrame"], [178, 26, 116, 22], [178, 29, 116, 25, "computeFrame"], [178, 30, 116, 25], [178, 35, 116, 31], [179, 10, 117, 6], [179, 16, 117, 12, "progress"], [179, 24, 117, 20], [179, 27, 117, 23], [180, 12, 118, 8, "x"], [180, 13, 118, 9], [180, 15, 118, 11, "computeEasingProgress"], [180, 36, 118, 32], [180, 37, 118, 33, "startTimestamp"], [180, 51, 118, 47], [180, 53, 118, 49, "distance"], [180, 61, 118, 57], [180, 62, 118, 58, "x"], [180, 63, 118, 59], [180, 65, 118, 61, "velocity"], [180, 73, 118, 69], [180, 74, 118, 70, "x"], [180, 75, 118, 71], [180, 76, 118, 72], [181, 12, 119, 8, "y"], [181, 13, 119, 9], [181, 15, 119, 11, "computeEasingProgress"], [181, 36, 119, 32], [181, 37, 119, 33, "startTimestamp"], [181, 51, 119, 47], [181, 53, 119, 49, "distance"], [181, 61, 119, 57], [181, 62, 119, 58, "y"], [181, 63, 119, 59], [181, 65, 119, 61, "velocity"], [181, 73, 119, 69], [181, 74, 119, 70, "y"], [181, 75, 119, 71], [182, 10, 120, 6], [182, 11, 120, 7], [183, 10, 121, 6, "event"], [183, 15, 121, 11], [183, 16, 121, 12, "translationX"], [183, 28, 121, 24], [183, 31, 121, 27, "startingPosition"], [183, 47, 121, 43], [183, 48, 121, 44, "x"], [183, 49, 121, 45], [183, 52, 121, 48, "direction"], [183, 61, 121, 57], [183, 62, 121, 58, "x"], [183, 63, 121, 59], [183, 66, 121, 62, "distance"], [183, 74, 121, 70], [183, 75, 121, 71, "x"], [183, 76, 121, 72], [183, 79, 121, 75, "easing"], [183, 85, 121, 81], [183, 86, 121, 82, "progress"], [183, 94, 121, 90], [183, 95, 121, 91, "x"], [183, 96, 121, 92], [183, 97, 121, 93], [184, 10, 122, 6, "event"], [184, 15, 122, 11], [184, 16, 122, 12, "translationY"], [184, 28, 122, 24], [184, 31, 122, 27, "startingPosition"], [184, 47, 122, 43], [184, 48, 122, 44, "y"], [184, 49, 122, 45], [184, 52, 122, 48, "direction"], [184, 61, 122, 57], [184, 62, 122, 58, "y"], [184, 63, 122, 59], [184, 66, 122, 62, "distance"], [184, 74, 122, 70], [184, 75, 122, 71, "y"], [184, 76, 122, 72], [184, 79, 122, 75, "easing"], [184, 85, 122, 81], [184, 86, 122, 82, "progress"], [184, 94, 122, 90], [184, 95, 122, 91, "y"], [184, 96, 122, 92], [184, 97, 122, 93], [185, 10, 123, 6], [185, 14, 123, 10, "direction"], [185, 23, 123, 19], [185, 24, 123, 20, "x"], [185, 25, 123, 21], [185, 28, 123, 24], [185, 29, 123, 25], [185, 31, 123, 27], [186, 12, 124, 8], [186, 16, 124, 12, "event"], [186, 21, 124, 17], [186, 22, 124, 18, "translationX"], [186, 34, 124, 30], [186, 38, 124, 34], [186, 39, 124, 35], [186, 41, 124, 37], [187, 14, 125, 10, "didScreenReachDestination"], [187, 39, 125, 35], [187, 40, 125, 36, "x"], [187, 41, 125, 37], [187, 44, 125, 40], [187, 48, 125, 44], [188, 14, 126, 10, "event"], [188, 19, 126, 15], [188, 20, 126, 16, "translationX"], [188, 32, 126, 28], [188, 35, 126, 31], [188, 36, 126, 32], [189, 12, 127, 8], [190, 10, 128, 6], [190, 11, 128, 7], [190, 17, 128, 13], [191, 12, 129, 8], [191, 16, 129, 12, "event"], [191, 21, 129, 17], [191, 22, 129, 18, "translationX"], [191, 34, 129, 30], [191, 38, 129, 34], [191, 39, 129, 35], [191, 41, 129, 37], [192, 14, 130, 10, "didScreenReachDestination"], [192, 39, 130, 35], [192, 40, 130, 36, "x"], [192, 41, 130, 37], [192, 44, 130, 40], [192, 48, 130, 44], [193, 14, 131, 10, "event"], [193, 19, 131, 15], [193, 20, 131, 16, "translationX"], [193, 32, 131, 28], [193, 35, 131, 31], [193, 36, 131, 32], [194, 12, 132, 8], [195, 10, 133, 6], [196, 10, 134, 6], [196, 14, 134, 10, "direction"], [196, 23, 134, 19], [196, 24, 134, 20, "y"], [196, 25, 134, 21], [196, 28, 134, 24], [196, 29, 134, 25], [196, 31, 134, 27], [197, 12, 135, 8], [197, 16, 135, 12, "event"], [197, 21, 135, 17], [197, 22, 135, 18, "translationY"], [197, 34, 135, 30], [197, 38, 135, 34], [197, 39, 135, 35], [197, 41, 135, 37], [198, 14, 136, 10, "didScreenReachDestination"], [198, 39, 136, 35], [198, 40, 136, 36, "y"], [198, 41, 136, 37], [198, 44, 136, 40], [198, 48, 136, 44], [199, 14, 137, 10, "event"], [199, 19, 137, 15], [199, 20, 137, 16, "translationY"], [199, 32, 137, 28], [199, 35, 137, 31], [199, 36, 137, 32], [200, 12, 138, 8], [201, 10, 139, 6], [201, 11, 139, 7], [201, 17, 139, 13], [202, 12, 140, 8], [202, 16, 140, 12, "event"], [202, 21, 140, 17], [202, 22, 140, 18, "translationY"], [202, 34, 140, 30], [202, 38, 140, 34], [202, 39, 140, 35], [202, 41, 140, 37], [203, 14, 141, 10, "didScreenReachDestination"], [203, 39, 141, 35], [203, 40, 141, 36, "y"], [203, 41, 141, 37], [203, 44, 141, 40], [203, 48, 141, 44], [204, 14, 142, 10, "event"], [204, 19, 142, 15], [204, 20, 142, 16, "translationY"], [204, 32, 142, 28], [204, 35, 142, 31], [204, 36, 142, 32], [205, 12, 143, 8], [206, 10, 144, 6], [207, 10, 145, 6], [207, 14, 145, 6, "applyStyle"], [207, 38, 145, 16], [207, 40, 145, 17, "screenTransitionConfig"], [207, 62, 145, 39], [207, 64, 145, 41, "event"], [207, 69, 145, 46], [207, 70, 145, 47], [208, 10, 146, 6], [208, 16, 146, 12, "finished"], [208, 24, 146, 20], [208, 27, 146, 23, "didScreenReachDestinationCheck"], [208, 57, 146, 53], [208, 58, 146, 54], [208, 59, 146, 55], [209, 10, 147, 6], [209, 14, 147, 10, "finished"], [209, 22, 147, 18], [209, 24, 147, 20], [210, 12, 148, 8, "restoreOriginalStyleForBelowTopScreen"], [210, 49, 148, 45], [210, 50, 148, 46], [210, 51, 148, 47], [211, 10, 149, 6], [212, 10, 150, 6, "maybeScheduleNextFrame"], [212, 32, 150, 28], [212, 33, 150, 29, "computeFrame"], [212, 45, 150, 41], [212, 47, 150, 43, "finished"], [212, 55, 150, 51], [212, 57, 150, 53, "screenTransitionConfig"], [212, 79, 150, 75], [212, 81, 150, 77, "event"], [212, 86, 150, 82], [212, 88, 150, 84, "isTransitionCanceled"], [212, 108, 150, 104], [212, 109, 150, 105], [213, 8, 151, 4], [213, 9, 151, 5], [214, 8, 152, 4], [214, 15, 152, 11, "computeFrame"], [214, 27, 152, 23], [215, 6, 153, 2], [215, 7, 153, 3], [215, 13, 153, 9], [216, 8, 154, 4], [216, 14, 154, 10, "computeFrame"], [216, 26, 154, 22], [216, 29, 154, 25, "computeFrame"], [216, 30, 154, 25], [216, 35, 154, 31], [217, 10, 155, 6], [217, 16, 155, 12, "progress"], [217, 24, 155, 20], [217, 27, 155, 23], [218, 12, 156, 8, "x"], [218, 13, 156, 9], [218, 15, 156, 11, "computeEasingProgress"], [218, 36, 156, 32], [218, 37, 156, 33, "startTimestamp"], [218, 51, 156, 47], [218, 53, 156, 49, "distance"], [218, 61, 156, 57], [218, 62, 156, 58, "x"], [218, 63, 156, 59], [218, 65, 156, 61, "velocity"], [218, 73, 156, 69], [218, 74, 156, 70, "x"], [218, 75, 156, 71], [218, 76, 156, 72], [219, 12, 157, 8, "y"], [219, 13, 157, 9], [219, 15, 157, 11, "computeEasingProgress"], [219, 36, 157, 32], [219, 37, 157, 33, "startTimestamp"], [219, 51, 157, 47], [219, 53, 157, 49, "distance"], [219, 61, 157, 57], [219, 62, 157, 58, "y"], [219, 63, 157, 59], [219, 65, 157, 61, "velocity"], [219, 73, 157, 69], [219, 74, 157, 70, "y"], [219, 75, 157, 71], [220, 10, 158, 6], [220, 11, 158, 7], [221, 10, 159, 6, "event"], [221, 15, 159, 11], [221, 16, 159, 12, "translationX"], [221, 28, 159, 24], [221, 31, 159, 27, "startingPosition"], [221, 47, 159, 43], [221, 48, 159, 44, "x"], [221, 49, 159, 45], [221, 52, 159, 48, "direction"], [221, 61, 159, 57], [221, 62, 159, 58, "x"], [221, 63, 159, 59], [221, 66, 159, 62, "distance"], [221, 74, 159, 70], [221, 75, 159, 71, "x"], [221, 76, 159, 72], [221, 79, 159, 75, "easing"], [221, 85, 159, 81], [221, 86, 159, 82, "progress"], [221, 94, 159, 90], [221, 95, 159, 91, "x"], [221, 96, 159, 92], [221, 97, 159, 93], [222, 10, 160, 6, "event"], [222, 15, 160, 11], [222, 16, 160, 12, "translationY"], [222, 28, 160, 24], [222, 31, 160, 27, "startingPosition"], [222, 47, 160, 43], [222, 48, 160, 44, "y"], [222, 49, 160, 45], [222, 52, 160, 48, "direction"], [222, 61, 160, 57], [222, 62, 160, 58, "y"], [222, 63, 160, 59], [222, 66, 160, 62, "distance"], [222, 74, 160, 70], [222, 75, 160, 71, "y"], [222, 76, 160, 72], [222, 79, 160, 75, "easing"], [222, 85, 160, 81], [222, 86, 160, 82, "progress"], [222, 94, 160, 90], [222, 95, 160, 91, "y"], [222, 96, 160, 92], [222, 97, 160, 93], [223, 10, 161, 6], [223, 14, 161, 10, "direction"], [223, 23, 161, 19], [223, 24, 161, 20, "x"], [223, 25, 161, 21], [223, 28, 161, 24], [223, 29, 161, 25], [223, 31, 161, 27], [224, 12, 162, 8], [224, 16, 162, 12, "event"], [224, 21, 162, 17], [224, 22, 162, 18, "translationX"], [224, 34, 162, 30], [224, 38, 162, 34, "screenDimensions"], [224, 54, 162, 50], [224, 55, 162, 51, "width"], [224, 60, 162, 56], [224, 62, 162, 58], [225, 14, 163, 10, "didScreenReachDestination"], [225, 39, 163, 35], [225, 40, 163, 36, "x"], [225, 41, 163, 37], [225, 44, 163, 40], [225, 48, 163, 44], [226, 14, 164, 10, "event"], [226, 19, 164, 15], [226, 20, 164, 16, "translationX"], [226, 32, 164, 28], [226, 35, 164, 31, "screenDimensions"], [226, 51, 164, 47], [226, 52, 164, 48, "width"], [226, 57, 164, 53], [227, 12, 165, 8], [228, 10, 166, 6], [228, 11, 166, 7], [228, 17, 166, 13], [229, 12, 167, 8], [229, 16, 167, 12, "event"], [229, 21, 167, 17], [229, 22, 167, 18, "translationX"], [229, 34, 167, 30], [229, 38, 167, 34], [229, 39, 167, 35, "screenDimensions"], [229, 55, 167, 51], [229, 56, 167, 52, "width"], [229, 61, 167, 57], [229, 63, 167, 59], [230, 14, 168, 10, "didScreenReachDestination"], [230, 39, 168, 35], [230, 40, 168, 36, "x"], [230, 41, 168, 37], [230, 44, 168, 40], [230, 48, 168, 44], [231, 14, 169, 10, "event"], [231, 19, 169, 15], [231, 20, 169, 16, "translationX"], [231, 32, 169, 28], [231, 35, 169, 31], [231, 36, 169, 32, "screenDimensions"], [231, 52, 169, 48], [231, 53, 169, 49, "width"], [231, 58, 169, 54], [232, 12, 170, 8], [233, 10, 171, 6], [234, 10, 172, 6], [234, 14, 172, 10, "direction"], [234, 23, 172, 19], [234, 24, 172, 20, "y"], [234, 25, 172, 21], [234, 28, 172, 24], [234, 29, 172, 25], [234, 31, 172, 27], [235, 12, 173, 8], [235, 16, 173, 12, "event"], [235, 21, 173, 17], [235, 22, 173, 18, "translationY"], [235, 34, 173, 30], [235, 38, 173, 34, "screenDimensions"], [235, 54, 173, 50], [235, 55, 173, 51, "height"], [235, 61, 173, 57], [235, 63, 173, 59], [236, 14, 174, 10, "didScreenReachDestination"], [236, 39, 174, 35], [236, 40, 174, 36, "y"], [236, 41, 174, 37], [236, 44, 174, 40], [236, 48, 174, 44], [237, 14, 175, 10, "event"], [237, 19, 175, 15], [237, 20, 175, 16, "translationY"], [237, 32, 175, 28], [237, 35, 175, 31, "screenDimensions"], [237, 51, 175, 47], [237, 52, 175, 48, "height"], [237, 58, 175, 54], [238, 12, 176, 8], [239, 10, 177, 6], [239, 11, 177, 7], [239, 17, 177, 13], [240, 12, 178, 8], [240, 16, 178, 12, "event"], [240, 21, 178, 17], [240, 22, 178, 18, "translationY"], [240, 34, 178, 30], [240, 38, 178, 34], [240, 39, 178, 35, "screenDimensions"], [240, 55, 178, 51], [240, 56, 178, 52, "height"], [240, 62, 178, 58], [240, 64, 178, 60], [241, 14, 179, 10, "didScreenReachDestination"], [241, 39, 179, 35], [241, 40, 179, 36, "y"], [241, 41, 179, 37], [241, 44, 179, 40], [241, 48, 179, 44], [242, 14, 180, 10, "event"], [242, 19, 180, 15], [242, 20, 180, 16, "translationY"], [242, 32, 180, 28], [242, 35, 180, 31], [242, 36, 180, 32, "screenDimensions"], [242, 52, 180, 48], [242, 53, 180, 49, "height"], [242, 59, 180, 55], [243, 12, 181, 8], [244, 10, 182, 6], [245, 10, 183, 6], [245, 14, 183, 6, "applyStyle"], [245, 38, 183, 16], [245, 40, 183, 17, "screenTransitionConfig"], [245, 62, 183, 39], [245, 64, 183, 41, "event"], [245, 69, 183, 46], [245, 70, 183, 47], [246, 10, 184, 6, "maybeScheduleNextFrame"], [246, 32, 184, 28], [246, 33, 184, 29, "computeFrame"], [246, 45, 184, 41], [246, 47, 184, 43, "didScreenReachDestination"], [246, 72, 184, 68], [246, 73, 184, 69, "x"], [246, 74, 184, 70], [246, 78, 184, 74, "didScreenReachDestination"], [246, 103, 184, 99], [246, 104, 184, 100, "y"], [246, 105, 184, 101], [246, 107, 184, 103, "screenTransitionConfig"], [246, 129, 184, 125], [246, 131, 184, 127, "event"], [246, 136, 184, 132], [246, 138, 184, 134, "isTransitionCanceled"], [246, 158, 184, 154], [246, 159, 184, 155], [247, 8, 185, 4], [247, 9, 185, 5], [248, 8, 186, 4], [248, 15, 186, 11, "computeFrame"], [248, 27, 186, 23], [249, 6, 187, 2], [250, 4, 188, 0], [250, 5, 188, 1], [251, 4, 188, 1, "getSwipeSimulator"], [251, 21, 188, 1], [251, 22, 188, 1, "__closure"], [251, 31, 188, 1], [252, 6, 188, 1, "BASE_VELOCITY"], [252, 19, 188, 1], [253, 6, 188, 1, "ADDITIONAL_VELOCITY_FACTOR_X"], [253, 34, 188, 1], [254, 6, 188, 1, "ADDITIONAL_VELOCITY_FACTOR_Y"], [254, 34, 188, 1], [255, 6, 188, 1, "ADDITIONAL_VELOCITY_FACTOR_XY"], [255, 35, 188, 1], [256, 6, 188, 1, "applyStyleForBelowTopScreen"], [256, 33, 188, 1], [256, 35, 114, 6, "applyStyleForBelowTopScreen"], [256, 76, 114, 33], [257, 6, 114, 33, "computeEasingProgress"], [257, 27, 114, 33], [258, 6, 114, 33, "easing"], [258, 12, 114, 33], [259, 6, 114, 33, "applyStyle"], [259, 16, 114, 33], [259, 18, 145, 6, "applyStyle"], [259, 42, 145, 16], [260, 6, 145, 16, "maybeScheduleNextFrame"], [261, 4, 145, 16], [262, 4, 145, 16, "getSwipeSimulator"], [262, 21, 145, 16], [262, 22, 145, 16, "__workletHash"], [262, 35, 145, 16], [263, 4, 145, 16, "getSwipeSimulator"], [263, 21, 145, 16], [263, 22, 145, 16, "__initData"], [263, 32, 145, 16], [263, 35, 145, 16, "_worklet_15110927952447_init_data"], [263, 68, 145, 16], [264, 4, 145, 16, "getSwipeSimulator"], [264, 21, 145, 16], [264, 22, 145, 16, "__stackDetails"], [264, 36, 145, 16], [264, 39, 145, 16, "_e"], [264, 41, 145, 16], [265, 4, 145, 16], [265, 11, 145, 16, "getSwipeSimulator"], [265, 28, 145, 16], [266, 2, 145, 16], [266, 3, 48, 7], [267, 0, 48, 7], [267, 3]], "functionMap": {"names": ["<global>", "computeEasingProgress", "easing", "computeProgress", "maybeScheduleNextFrame", "getSwipeSimulator", "didScreenReachDestinationCheck", "restoreOriginalStyleForBelowTopScreen", "computeFrame"], "mappings": "AAA;ACQ;CDU;AEC;CFK;AGC;CHS;AIC;CJW;OKC;ICsD;KDQ;IEC;KFI;yBGC;KHmC;yBGG;KH+B;CLG"}}, "type": "js/module"}]}