{"dependencies": [{"name": "../../../Easing.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "Lmht69cIRX45nwUis8cYTXOn1Ws=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JumpingTransition = JumpingTransition;\n  var _Easing = require(_dependencyMap[0], \"../../../Easing.js\");\n  function JumpingTransition(name, transitionData) {\n    const {\n      translateX,\n      translateY,\n      scaleX,\n      scaleY\n    } = transitionData;\n    const d = Math.max(Math.abs(translateX), Math.abs(translateY)) / 2;\n    const peakTranslateY = translateY <= 0 ? translateY - d : -translateY + d;\n    const jumpingTransition = {\n      name,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }],\n          easing: _Easing.Easing.exp\n        },\n        50: {\n          transform: [{\n            translateX: `${translateX / 2}px`,\n            translateY: `${peakTranslateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: '1,1'\n          }]\n        }\n      },\n      duration: 300\n    };\n    return jumpingTransition;\n  }\n});", "lineCount": 48, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "JumpingTransition"], [7, 27, 1, 13], [7, 30, 1, 13, "JumpingTransition"], [7, 47, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_Easing"], [8, 13, 3, 0], [8, 16, 3, 0, "require"], [8, 23, 3, 0], [8, 24, 3, 0, "_dependencyMap"], [8, 38, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "JumpingTransition"], [9, 28, 4, 33, "JumpingTransition"], [9, 29, 4, 34, "name"], [9, 33, 4, 38], [9, 35, 4, 40, "transitionData"], [9, 49, 4, 54], [9, 51, 4, 56], [10, 4, 5, 2], [10, 10, 5, 8], [11, 6, 6, 4, "translateX"], [11, 16, 6, 14], [12, 6, 7, 4, "translateY"], [12, 16, 7, 14], [13, 6, 8, 4, "scaleX"], [13, 12, 8, 10], [14, 6, 9, 4, "scaleY"], [15, 4, 10, 2], [15, 5, 10, 3], [15, 8, 10, 6, "transitionData"], [15, 22, 10, 20], [16, 4, 11, 2], [16, 10, 11, 8, "d"], [16, 11, 11, 9], [16, 14, 11, 12, "Math"], [16, 18, 11, 16], [16, 19, 11, 17, "max"], [16, 22, 11, 20], [16, 23, 11, 21, "Math"], [16, 27, 11, 25], [16, 28, 11, 26, "abs"], [16, 31, 11, 29], [16, 32, 11, 30, "translateX"], [16, 42, 11, 40], [16, 43, 11, 41], [16, 45, 11, 43, "Math"], [16, 49, 11, 47], [16, 50, 11, 48, "abs"], [16, 53, 11, 51], [16, 54, 11, 52, "translateY"], [16, 64, 11, 62], [16, 65, 11, 63], [16, 66, 11, 64], [16, 69, 11, 67], [16, 70, 11, 68], [17, 4, 12, 2], [17, 10, 12, 8, "peakTranslateY"], [17, 24, 12, 22], [17, 27, 12, 25, "translateY"], [17, 37, 12, 35], [17, 41, 12, 39], [17, 42, 12, 40], [17, 45, 12, 43, "translateY"], [17, 55, 12, 53], [17, 58, 12, 56, "d"], [17, 59, 12, 57], [17, 62, 12, 60], [17, 63, 12, 61, "translateY"], [17, 73, 12, 71], [17, 76, 12, 74, "d"], [17, 77, 12, 75], [18, 4, 13, 2], [18, 10, 13, 8, "jumpingTransition"], [18, 27, 13, 25], [18, 30, 13, 28], [19, 6, 14, 4, "name"], [19, 10, 14, 8], [20, 6, 15, 4, "style"], [20, 11, 15, 9], [20, 13, 15, 11], [21, 8, 16, 6], [21, 9, 16, 7], [21, 11, 16, 9], [22, 10, 17, 8, "transform"], [22, 19, 17, 17], [22, 21, 17, 19], [22, 22, 17, 20], [23, 12, 18, 10, "translateX"], [23, 22, 18, 20], [23, 24, 18, 22], [23, 27, 18, 25, "translateX"], [23, 37, 18, 35], [23, 41, 18, 39], [24, 12, 19, 10, "translateY"], [24, 22, 19, 20], [24, 24, 19, 22], [24, 27, 19, 25, "translateY"], [24, 37, 19, 35], [24, 41, 19, 39], [25, 12, 20, 10, "scale"], [25, 17, 20, 15], [25, 19, 20, 17], [25, 22, 20, 20, "scaleX"], [25, 28, 20, 26], [25, 32, 20, 30, "scaleY"], [25, 38, 20, 36], [26, 10, 21, 8], [26, 11, 21, 9], [26, 12, 21, 10], [27, 10, 22, 8, "easing"], [27, 16, 22, 14], [27, 18, 22, 16, "Easing"], [27, 32, 22, 22], [27, 33, 22, 23, "exp"], [28, 8, 23, 6], [28, 9, 23, 7], [29, 8, 24, 6], [29, 10, 24, 8], [29, 12, 24, 10], [30, 10, 25, 8, "transform"], [30, 19, 25, 17], [30, 21, 25, 19], [30, 22, 25, 20], [31, 12, 26, 10, "translateX"], [31, 22, 26, 20], [31, 24, 26, 22], [31, 27, 26, 25, "translateX"], [31, 37, 26, 35], [31, 40, 26, 38], [31, 41, 26, 39], [31, 45, 26, 43], [32, 12, 27, 10, "translateY"], [32, 22, 27, 20], [32, 24, 27, 22], [32, 27, 27, 25, "peakTranslateY"], [32, 41, 27, 39], [32, 45, 27, 43], [33, 12, 28, 10, "scale"], [33, 17, 28, 15], [33, 19, 28, 17], [33, 22, 28, 20, "scaleX"], [33, 28, 28, 26], [33, 32, 28, 30, "scaleY"], [33, 38, 28, 36], [34, 10, 29, 8], [34, 11, 29, 9], [35, 8, 30, 6], [35, 9, 30, 7], [36, 8, 31, 6], [36, 11, 31, 9], [36, 13, 31, 11], [37, 10, 32, 8, "transform"], [37, 19, 32, 17], [37, 21, 32, 19], [37, 22, 32, 20], [38, 12, 33, 10, "translateX"], [38, 22, 33, 20], [38, 24, 33, 22], [38, 29, 33, 27], [39, 12, 34, 10, "translateY"], [39, 22, 34, 20], [39, 24, 34, 22], [39, 29, 34, 27], [40, 12, 35, 10, "scale"], [40, 17, 35, 15], [40, 19, 35, 17], [41, 10, 36, 8], [41, 11, 36, 9], [42, 8, 37, 6], [43, 6, 38, 4], [43, 7, 38, 5], [44, 6, 39, 4, "duration"], [44, 14, 39, 12], [44, 16, 39, 14], [45, 4, 40, 2], [45, 5, 40, 3], [46, 4, 41, 2], [46, 11, 41, 9, "jumpingTransition"], [46, 28, 41, 26], [47, 2, 42, 0], [48, 0, 42, 1], [48, 3]], "functionMap": {"names": ["<global>", "JumpingTransition"], "mappings": "AAA;OCG;CDsC"}}, "type": "js/module"}]}