{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./DebuggingOverlayRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 66}}], "key": "NXtNgCUZItz/rmdoy2zQwmKXEhM=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 32}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _DebuggingOverlayRegistry = _interopRequireDefault(require(_dependencyMap[1], \"./DebuggingOverlayRegistry\"));\n  var _react = require(_dependencyMap[2], \"react\");\n  var useSubscribeToDebuggingOverlayRegistry = (rootViewRef, debuggingOverlayRef) => {\n    (0, _react.useEffect)(() => {\n      var subscriber = {\n        rootViewRef,\n        debuggingOverlayRef\n      };\n      _DebuggingOverlayRegistry.default.subscribe(subscriber);\n      return () => _DebuggingOverlayRegistry.default.unsubscribe(subscriber);\n    }, [rootViewRef, debuggingOverlayRef]);\n  };\n  var _default = exports.default = useSubscribeToDebuggingOverlayRegistry;\n});", "lineCount": 20, "map": [[7, 2, 17, 0], [7, 6, 17, 0, "_DebuggingOverlayRegistry"], [7, 31, 17, 0], [7, 34, 17, 0, "_interopRequireDefault"], [7, 56, 17, 0], [7, 57, 17, 0, "require"], [7, 64, 17, 0], [7, 65, 17, 0, "_dependencyMap"], [7, 79, 17, 0], [8, 2, 18, 0], [8, 6, 18, 0, "_react"], [8, 12, 18, 0], [8, 15, 18, 0, "require"], [8, 22, 18, 0], [8, 23, 18, 0, "_dependencyMap"], [8, 37, 18, 0], [9, 2, 20, 0], [9, 6, 20, 6, "useSubscribeToDebuggingOverlayRegistry"], [9, 44, 20, 44], [9, 47, 20, 47, "useSubscribeToDebuggingOverlayRegistry"], [9, 48, 21, 2, "rootViewRef"], [9, 59, 21, 38], [9, 61, 22, 2, "debuggingOverlayRef"], [9, 80, 22, 42], [9, 85, 23, 5], [10, 4, 24, 2], [10, 8, 24, 2, "useEffect"], [10, 24, 24, 11], [10, 26, 24, 12], [10, 32, 24, 18], [11, 6, 25, 4], [11, 10, 25, 10, "subscriber"], [11, 20, 25, 20], [11, 23, 25, 23], [12, 8, 25, 24, "rootViewRef"], [12, 19, 25, 35], [13, 8, 25, 37, "debuggingOverlayRef"], [14, 6, 25, 56], [14, 7, 25, 57], [15, 6, 27, 4, "DebuggingOverlayRegistry"], [15, 39, 27, 28], [15, 40, 27, 29, "subscribe"], [15, 49, 27, 38], [15, 50, 27, 39, "subscriber"], [15, 60, 27, 49], [15, 61, 27, 50], [16, 6, 28, 4], [16, 13, 28, 11], [16, 19, 28, 17, "DebuggingOverlayRegistry"], [16, 52, 28, 41], [16, 53, 28, 42, "unsubscribe"], [16, 64, 28, 53], [16, 65, 28, 54, "subscriber"], [16, 75, 28, 64], [16, 76, 28, 65], [17, 4, 29, 2], [17, 5, 29, 3], [17, 7, 29, 5], [17, 8, 29, 6, "rootViewRef"], [17, 19, 29, 17], [17, 21, 29, 19, "debuggingOverlayRef"], [17, 40, 29, 38], [17, 41, 29, 39], [17, 42, 29, 40], [18, 2, 30, 0], [18, 3, 30, 1], [19, 2, 30, 2], [19, 6, 30, 2, "_default"], [19, 14, 30, 2], [19, 17, 30, 2, "exports"], [19, 24, 30, 2], [19, 25, 30, 2, "default"], [19, 32, 30, 2], [19, 35, 32, 15, "useSubscribeToDebuggingOverlayRegistry"], [19, 73, 32, 53], [20, 0, 32, 53], [20, 3]], "functionMap": {"names": ["<global>", "useSubscribeToDebuggingOverlayRegistry", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;+CCmB;YCI;WCI,sDD;GDC;CDC"}}, "type": "js/module"}]}