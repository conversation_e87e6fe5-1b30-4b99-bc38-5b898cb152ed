{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 67, "index": 82}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 83}, "end": {"line": 4, "column": 26, "index": 109}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 110}, "end": {"line": 5, "column": 31, "index": 141}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TextInput", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "DmXc1F5dPYWntVgqRwh73w0VngA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "../assets/clear-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 229}, "end": {"line": 7, "column": 49, "index": 278}}], "key": "yZi8+EqRlrVGamo6rgj0NZqC10c=", "exportNames": ["*"]}}, {"name": "../assets/close-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 279}, "end": {"line": 8, "column": 49, "index": 328}}], "key": "VF4ux2XhnVTQZVaj8eIVFMp8bNU=", "exportNames": ["*"]}}, {"name": "../assets/search-icon.png", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 329}, "end": {"line": 9, "column": 51, "index": 380}}], "key": "ai40rVaAzolPoKDrCo7kH+CIoHU=", "exportNames": ["*"]}}, {"name": "../PlatformPressable.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 381}, "end": {"line": 10, "column": 60, "index": 441}}], "key": "auJZ4k92W56l1sd57k0rcJrkXw0=", "exportNames": ["*"]}}, {"name": "../Text.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 442}, "end": {"line": 11, "column": 34, "index": 476}}], "key": "UfNR+WZdGHHR+kk13ETrBegm38s=", "exportNames": ["*"]}}, {"name": "./HeaderButton.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 477}, "end": {"line": 12, "column": 49, "index": 526}}], "key": "5Mfp2bWqztZ2HFy80uJBbvbN6HA=", "exportNames": ["*"]}}, {"name": "./HeaderIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 527}, "end": {"line": 13, "column": 45, "index": 572}}], "key": "0JPASIZzwd0DulPaj/kDrorllj8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 573}, "end": {"line": 14, "column": 63, "index": 636}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderSearchBar = void 0;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[2], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Animated\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Image\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TextInput = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/TextInput\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/View\"));\n  var _clearIcon = _interopRequireDefault(require(_dependencyMap[10], \"../assets/clear-icon.png\"));\n  var _closeIcon = _interopRequireDefault(require(_dependencyMap[11], \"../assets/close-icon.png\"));\n  var _searchIcon = _interopRequireDefault(require(_dependencyMap[12], \"../assets/search-icon.png\"));\n  var _PlatformPressable = require(_dependencyMap[13], \"../PlatformPressable.js\");\n  var _Text = require(_dependencyMap[14], \"../Text.js\");\n  var _HeaderButton = require(_dependencyMap[15], \"./HeaderButton.js\");\n  var _HeaderIcon = require(_dependencyMap[16], \"./HeaderIcon.js\");\n  var _jsxRuntime = require(_dependencyMap[17], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const INPUT_TYPE_TO_MODE = {\n    text: 'text',\n    number: 'numeric',\n    phone: 'tel',\n    email: 'email'\n  };\n  const useNativeDriver = _Platform.default.OS !== 'web';\n  function HeaderSearchBarInternal({\n    visible,\n    inputType,\n    autoFocus = true,\n    placeholder = 'Search',\n    cancelButtonText = 'Cancel',\n    enterKeyHint = 'search',\n    onChangeText,\n    onClose,\n    tintColor,\n    style,\n    ...rest\n  }, ref) {\n    const navigation = (0, _native.useNavigation)();\n    const {\n      dark,\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    const [value, setValue] = React.useState('');\n    const [rendered, setRendered] = React.useState(visible);\n    const [visibleAnim] = React.useState(() => new _Animated.default.Value(visible ? 1 : 0));\n    const [clearVisibleAnim] = React.useState(() => new _Animated.default.Value(0));\n    const visibleValueRef = React.useRef(visible);\n    const clearVisibleValueRef = React.useRef(false);\n    const inputRef = React.useRef(null);\n    React.useEffect(() => {\n      // Avoid act warning in tests just by rendering header\n      if (visible === visibleValueRef.current) {\n        return;\n      }\n      _Animated.default.timing(visibleAnim, {\n        toValue: visible ? 1 : 0,\n        duration: 100,\n        useNativeDriver\n      }).start(({\n        finished\n      }) => {\n        if (finished) {\n          setRendered(visible);\n          visibleValueRef.current = visible;\n        }\n      });\n      return () => {\n        visibleAnim.stopAnimation();\n      };\n    }, [visible, visibleAnim]);\n    const hasText = value !== '';\n    React.useEffect(() => {\n      if (clearVisibleValueRef.current === hasText) {\n        return;\n      }\n      _Animated.default.timing(clearVisibleAnim, {\n        toValue: hasText ? 1 : 0,\n        duration: 100,\n        useNativeDriver\n      }).start(({\n        finished\n      }) => {\n        if (finished) {\n          clearVisibleValueRef.current = hasText;\n        }\n      });\n    }, [clearVisibleAnim, hasText]);\n    const clearText = React.useCallback(() => {\n      inputRef.current?.clear();\n      inputRef.current?.focus();\n      setValue('');\n    }, []);\n    const onClear = React.useCallback(() => {\n      clearText();\n      // FIXME: figure out how to create a SyntheticEvent\n      // @ts-expect-error: we don't have the native event here\n      onChangeText?.({\n        nativeEvent: {\n          text: ''\n        }\n      });\n    }, [clearText, onChangeText]);\n    const cancelSearch = React.useCallback(() => {\n      onClear();\n      onClose();\n    }, [onClear, onClose]);\n    React.useEffect(() => navigation?.addListener('blur', cancelSearch), [cancelSearch, navigation]);\n    React.useImperativeHandle(ref, () => ({\n      focus: () => {\n        inputRef.current?.focus();\n      },\n      blur: () => {\n        inputRef.current?.blur();\n      },\n      setText: text => {\n        inputRef.current?.setNativeProps({\n          text\n        });\n        setValue(text);\n      },\n      clearText,\n      cancelSearch\n    }), [cancelSearch, clearText]);\n    if (!visible && !rendered) {\n      return null;\n    }\n    const textColor = tintColor ?? colors.text;\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_Animated.default.View, {\n      pointerEvents: visible ? 'auto' : 'none',\n      \"aria-live\": \"polite\",\n      \"aria-hidden\": !visible,\n      style: [styles.container, {\n        opacity: visibleAnim\n      }, style],\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_View.default, {\n        style: styles.searchbarContainer,\n        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {\n          source: _searchIcon.default,\n          tintColor: textColor,\n          style: styles.inputSearchIcon\n        }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_TextInput.default, {\n          ...rest,\n          ref: inputRef,\n          onChange: onChangeText,\n          onChangeText: setValue,\n          autoFocus: autoFocus,\n          inputMode: INPUT_TYPE_TO_MODE[inputType ?? 'text'],\n          enterKeyHint: enterKeyHint,\n          placeholder: placeholder,\n          placeholderTextColor: (0, _color.default)(textColor).alpha(0.5).string(),\n          cursorColor: colors.primary,\n          selectionHandleColor: colors.primary,\n          selectionColor: (0, _color.default)(colors.primary).alpha(0.3).string(),\n          style: [fonts.regular, styles.searchbar, {\n            backgroundColor: _Platform.default.select({\n              ios: dark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',\n              default: 'transparent'\n            }),\n            color: textColor,\n            borderBottomColor: (0, _color.default)(textColor).alpha(0.2).string()\n          }]\n        }), _Platform.default.OS === 'ios' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_PlatformPressable.PlatformPressable, {\n          onPress: onClear,\n          style: [{\n            opacity: clearVisibleAnim,\n            transform: [{\n              scale: clearVisibleAnim\n            }]\n          }, styles.clearButton],\n          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Image.default, {\n            source: _clearIcon.default,\n            resizeMode: \"contain\",\n            tintColor: textColor,\n            style: styles.clearIcon\n          })\n        }) : null]\n      }), _Platform.default.OS !== 'ios' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderButton.HeaderButton, {\n        onPress: () => {\n          if (value) {\n            onClear();\n          } else {\n            onClose();\n          }\n        },\n        style: styles.closeButton,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {\n          source: _closeIcon.default,\n          tintColor: textColor\n        })\n      }) : null, _Platform.default.OS === 'ios' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_PlatformPressable.PlatformPressable, {\n        onPress: cancelSearch,\n        style: styles.cancelButton,\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.Text, {\n          style: [fonts.regular, {\n            color: tintColor ?? colors.primary\n          }, styles.cancelText],\n          children: cancelButtonText\n        })\n      }) : null]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      flexDirection: 'row',\n      alignItems: 'stretch'\n    },\n    inputSearchIcon: {\n      position: 'absolute',\n      opacity: 0.5,\n      left: _Platform.default.select({\n        ios: 16,\n        default: 4\n      }),\n      top: _Platform.default.select({\n        ios: -1,\n        default: 17\n      }),\n      ..._Platform.default.select({\n        ios: {\n          height: 18,\n          width: 18\n        },\n        default: {}\n      })\n    },\n    closeButton: {\n      position: 'absolute',\n      opacity: 0.5,\n      right: _Platform.default.select({\n        ios: 0,\n        default: 8\n      }),\n      top: _Platform.default.select({\n        ios: -2,\n        default: 17\n      })\n    },\n    clearButton: {\n      position: 'absolute',\n      right: 0,\n      top: -7,\n      bottom: 0,\n      justifyContent: 'center',\n      padding: 8\n    },\n    clearIcon: {\n      height: 16,\n      width: 16,\n      opacity: 0.5\n    },\n    cancelButton: {\n      alignSelf: 'center',\n      top: -4\n    },\n    cancelText: {\n      fontSize: 17,\n      marginHorizontal: 12\n    },\n    searchbarContainer: {\n      flex: 1\n    },\n    searchbar: _Platform.default.select({\n      ios: {\n        flex: 1,\n        fontSize: 17,\n        paddingHorizontal: 32,\n        marginLeft: 16,\n        marginTop: -1,\n        marginBottom: 4,\n        borderRadius: 8\n      },\n      default: {\n        flex: 1,\n        fontSize: 18,\n        paddingHorizontal: 36,\n        marginRight: 8,\n        marginTop: 8,\n        marginBottom: 8,\n        borderBottomWidth: 1\n      }\n    })\n  });\n  const HeaderSearchBar = exports.HeaderSearchBar = /*#__PURE__*/React.forwardRef(HeaderSearchBarInternal);\n});", "lineCount": 295, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 25, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_color"], [10, 12, 4, 0], [10, 15, 4, 0, "_interopRequireDefault"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "React"], [11, 11, 5, 0], [11, 14, 5, 0, "_interopRequireWildcard"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 5, 31], [12, 6, 5, 31, "_Animated"], [12, 15, 5, 31], [12, 18, 5, 31, "_interopRequireDefault"], [12, 40, 5, 31], [12, 41, 5, 31, "require"], [12, 48, 5, 31], [12, 49, 5, 31, "_dependencyMap"], [12, 63, 5, 31], [13, 2, 5, 31], [13, 6, 5, 31, "_Image"], [13, 12, 5, 31], [13, 15, 5, 31, "_interopRequireDefault"], [13, 37, 5, 31], [13, 38, 5, 31, "require"], [13, 45, 5, 31], [13, 46, 5, 31, "_dependencyMap"], [13, 60, 5, 31], [14, 2, 5, 31], [14, 6, 5, 31, "_Platform"], [14, 15, 5, 31], [14, 18, 5, 31, "_interopRequireDefault"], [14, 40, 5, 31], [14, 41, 5, 31, "require"], [14, 48, 5, 31], [14, 49, 5, 31, "_dependencyMap"], [14, 63, 5, 31], [15, 2, 5, 31], [15, 6, 5, 31, "_StyleSheet"], [15, 17, 5, 31], [15, 20, 5, 31, "_interopRequireDefault"], [15, 42, 5, 31], [15, 43, 5, 31, "require"], [15, 50, 5, 31], [15, 51, 5, 31, "_dependencyMap"], [15, 65, 5, 31], [16, 2, 5, 31], [16, 6, 5, 31, "_TextInput"], [16, 16, 5, 31], [16, 19, 5, 31, "_interopRequireDefault"], [16, 41, 5, 31], [16, 42, 5, 31, "require"], [16, 49, 5, 31], [16, 50, 5, 31, "_dependencyMap"], [16, 64, 5, 31], [17, 2, 5, 31], [17, 6, 5, 31, "_View"], [17, 11, 5, 31], [17, 14, 5, 31, "_interopRequireDefault"], [17, 36, 5, 31], [17, 37, 5, 31, "require"], [17, 44, 5, 31], [17, 45, 5, 31, "_dependencyMap"], [17, 59, 5, 31], [18, 2, 7, 0], [18, 6, 7, 0, "_clearIcon"], [18, 16, 7, 0], [18, 19, 7, 0, "_interopRequireDefault"], [18, 41, 7, 0], [18, 42, 7, 0, "require"], [18, 49, 7, 0], [18, 50, 7, 0, "_dependencyMap"], [18, 64, 7, 0], [19, 2, 8, 0], [19, 6, 8, 0, "_closeIcon"], [19, 16, 8, 0], [19, 19, 8, 0, "_interopRequireDefault"], [19, 41, 8, 0], [19, 42, 8, 0, "require"], [19, 49, 8, 0], [19, 50, 8, 0, "_dependencyMap"], [19, 64, 8, 0], [20, 2, 9, 0], [20, 6, 9, 0, "_searchIcon"], [20, 17, 9, 0], [20, 20, 9, 0, "_interopRequireDefault"], [20, 42, 9, 0], [20, 43, 9, 0, "require"], [20, 50, 9, 0], [20, 51, 9, 0, "_dependencyMap"], [20, 65, 9, 0], [21, 2, 10, 0], [21, 6, 10, 0, "_PlatformPressable"], [21, 24, 10, 0], [21, 27, 10, 0, "require"], [21, 34, 10, 0], [21, 35, 10, 0, "_dependencyMap"], [21, 49, 10, 0], [22, 2, 11, 0], [22, 6, 11, 0, "_Text"], [22, 11, 11, 0], [22, 14, 11, 0, "require"], [22, 21, 11, 0], [22, 22, 11, 0, "_dependencyMap"], [22, 36, 11, 0], [23, 2, 12, 0], [23, 6, 12, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 19, 12, 0], [23, 22, 12, 0, "require"], [23, 29, 12, 0], [23, 30, 12, 0, "_dependencyMap"], [23, 44, 12, 0], [24, 2, 13, 0], [24, 6, 13, 0, "_HeaderIcon"], [24, 17, 13, 0], [24, 20, 13, 0, "require"], [24, 27, 13, 0], [24, 28, 13, 0, "_dependencyMap"], [24, 42, 13, 0], [25, 2, 14, 0], [25, 6, 14, 0, "_jsxRuntime"], [25, 17, 14, 0], [25, 20, 14, 0, "require"], [25, 27, 14, 0], [25, 28, 14, 0, "_dependencyMap"], [25, 42, 14, 0], [26, 2, 14, 63], [26, 11, 14, 63, "_interopRequireWildcard"], [26, 35, 14, 63, "e"], [26, 36, 14, 63], [26, 38, 14, 63, "t"], [26, 39, 14, 63], [26, 68, 14, 63, "WeakMap"], [26, 75, 14, 63], [26, 81, 14, 63, "r"], [26, 82, 14, 63], [26, 89, 14, 63, "WeakMap"], [26, 96, 14, 63], [26, 100, 14, 63, "n"], [26, 101, 14, 63], [26, 108, 14, 63, "WeakMap"], [26, 115, 14, 63], [26, 127, 14, 63, "_interopRequireWildcard"], [26, 150, 14, 63], [26, 162, 14, 63, "_interopRequireWildcard"], [26, 163, 14, 63, "e"], [26, 164, 14, 63], [26, 166, 14, 63, "t"], [26, 167, 14, 63], [26, 176, 14, 63, "t"], [26, 177, 14, 63], [26, 181, 14, 63, "e"], [26, 182, 14, 63], [26, 186, 14, 63, "e"], [26, 187, 14, 63], [26, 188, 14, 63, "__esModule"], [26, 198, 14, 63], [26, 207, 14, 63, "e"], [26, 208, 14, 63], [26, 214, 14, 63, "o"], [26, 215, 14, 63], [26, 217, 14, 63, "i"], [26, 218, 14, 63], [26, 220, 14, 63, "f"], [26, 221, 14, 63], [26, 226, 14, 63, "__proto__"], [26, 235, 14, 63], [26, 243, 14, 63, "default"], [26, 250, 14, 63], [26, 252, 14, 63, "e"], [26, 253, 14, 63], [26, 270, 14, 63, "e"], [26, 271, 14, 63], [26, 294, 14, 63, "e"], [26, 295, 14, 63], [26, 320, 14, 63, "e"], [26, 321, 14, 63], [26, 330, 14, 63, "f"], [26, 331, 14, 63], [26, 337, 14, 63, "o"], [26, 338, 14, 63], [26, 341, 14, 63, "t"], [26, 342, 14, 63], [26, 345, 14, 63, "n"], [26, 346, 14, 63], [26, 349, 14, 63, "r"], [26, 350, 14, 63], [26, 358, 14, 63, "o"], [26, 359, 14, 63], [26, 360, 14, 63, "has"], [26, 363, 14, 63], [26, 364, 14, 63, "e"], [26, 365, 14, 63], [26, 375, 14, 63, "o"], [26, 376, 14, 63], [26, 377, 14, 63, "get"], [26, 380, 14, 63], [26, 381, 14, 63, "e"], [26, 382, 14, 63], [26, 385, 14, 63, "o"], [26, 386, 14, 63], [26, 387, 14, 63, "set"], [26, 390, 14, 63], [26, 391, 14, 63, "e"], [26, 392, 14, 63], [26, 394, 14, 63, "f"], [26, 395, 14, 63], [26, 411, 14, 63, "t"], [26, 412, 14, 63], [26, 416, 14, 63, "e"], [26, 417, 14, 63], [26, 433, 14, 63, "t"], [26, 434, 14, 63], [26, 441, 14, 63, "hasOwnProperty"], [26, 455, 14, 63], [26, 456, 14, 63, "call"], [26, 460, 14, 63], [26, 461, 14, 63, "e"], [26, 462, 14, 63], [26, 464, 14, 63, "t"], [26, 465, 14, 63], [26, 472, 14, 63, "i"], [26, 473, 14, 63], [26, 477, 14, 63, "o"], [26, 478, 14, 63], [26, 481, 14, 63, "Object"], [26, 487, 14, 63], [26, 488, 14, 63, "defineProperty"], [26, 502, 14, 63], [26, 507, 14, 63, "Object"], [26, 513, 14, 63], [26, 514, 14, 63, "getOwnPropertyDescriptor"], [26, 538, 14, 63], [26, 539, 14, 63, "e"], [26, 540, 14, 63], [26, 542, 14, 63, "t"], [26, 543, 14, 63], [26, 550, 14, 63, "i"], [26, 551, 14, 63], [26, 552, 14, 63, "get"], [26, 555, 14, 63], [26, 559, 14, 63, "i"], [26, 560, 14, 63], [26, 561, 14, 63, "set"], [26, 564, 14, 63], [26, 568, 14, 63, "o"], [26, 569, 14, 63], [26, 570, 14, 63, "f"], [26, 571, 14, 63], [26, 573, 14, 63, "t"], [26, 574, 14, 63], [26, 576, 14, 63, "i"], [26, 577, 14, 63], [26, 581, 14, 63, "f"], [26, 582, 14, 63], [26, 583, 14, 63, "t"], [26, 584, 14, 63], [26, 588, 14, 63, "e"], [26, 589, 14, 63], [26, 590, 14, 63, "t"], [26, 591, 14, 63], [26, 602, 14, 63, "f"], [26, 603, 14, 63], [26, 608, 14, 63, "e"], [26, 609, 14, 63], [26, 611, 14, 63, "t"], [26, 612, 14, 63], [27, 2, 15, 0], [27, 8, 15, 6, "INPUT_TYPE_TO_MODE"], [27, 26, 15, 24], [27, 29, 15, 27], [28, 4, 16, 2, "text"], [28, 8, 16, 6], [28, 10, 16, 8], [28, 16, 16, 14], [29, 4, 17, 2, "number"], [29, 10, 17, 8], [29, 12, 17, 10], [29, 21, 17, 19], [30, 4, 18, 2, "phone"], [30, 9, 18, 7], [30, 11, 18, 9], [30, 16, 18, 14], [31, 4, 19, 2, "email"], [31, 9, 19, 7], [31, 11, 19, 9], [32, 2, 20, 0], [32, 3, 20, 1], [33, 2, 21, 0], [33, 8, 21, 6, "useNativeDriver"], [33, 23, 21, 21], [33, 26, 21, 24, "Platform"], [33, 43, 21, 32], [33, 44, 21, 33, "OS"], [33, 46, 21, 35], [33, 51, 21, 40], [33, 56, 21, 45], [34, 2, 22, 0], [34, 11, 22, 9, "HeaderSearchBarInternal"], [34, 34, 22, 32, "HeaderSearchBarInternal"], [34, 35, 22, 33], [35, 4, 23, 2, "visible"], [35, 11, 23, 9], [36, 4, 24, 2, "inputType"], [36, 13, 24, 11], [37, 4, 25, 2, "autoFocus"], [37, 13, 25, 11], [37, 16, 25, 14], [37, 20, 25, 18], [38, 4, 26, 2, "placeholder"], [38, 15, 26, 13], [38, 18, 26, 16], [38, 26, 26, 24], [39, 4, 27, 2, "cancelButtonText"], [39, 20, 27, 18], [39, 23, 27, 21], [39, 31, 27, 29], [40, 4, 28, 2, "enterKeyHint"], [40, 16, 28, 14], [40, 19, 28, 17], [40, 27, 28, 25], [41, 4, 29, 2, "onChangeText"], [41, 16, 29, 14], [42, 4, 30, 2, "onClose"], [42, 11, 30, 9], [43, 4, 31, 2, "tintColor"], [43, 13, 31, 11], [44, 4, 32, 2, "style"], [44, 9, 32, 7], [45, 4, 33, 2], [45, 7, 33, 5, "rest"], [46, 2, 34, 0], [46, 3, 34, 1], [46, 5, 34, 3, "ref"], [46, 8, 34, 6], [46, 10, 34, 8], [47, 4, 35, 2], [47, 10, 35, 8, "navigation"], [47, 20, 35, 18], [47, 23, 35, 21], [47, 27, 35, 21, "useNavigation"], [47, 48, 35, 34], [47, 50, 35, 35], [47, 51, 35, 36], [48, 4, 36, 2], [48, 10, 36, 8], [49, 6, 37, 4, "dark"], [49, 10, 37, 8], [50, 6, 38, 4, "colors"], [50, 12, 38, 10], [51, 6, 39, 4, "fonts"], [52, 4, 40, 2], [52, 5, 40, 3], [52, 8, 40, 6], [52, 12, 40, 6, "useTheme"], [52, 28, 40, 14], [52, 30, 40, 15], [52, 31, 40, 16], [53, 4, 41, 2], [53, 10, 41, 8], [53, 11, 41, 9, "value"], [53, 16, 41, 14], [53, 18, 41, 16, "setValue"], [53, 26, 41, 24], [53, 27, 41, 25], [53, 30, 41, 28, "React"], [53, 35, 41, 33], [53, 36, 41, 34, "useState"], [53, 44, 41, 42], [53, 45, 41, 43], [53, 47, 41, 45], [53, 48, 41, 46], [54, 4, 42, 2], [54, 10, 42, 8], [54, 11, 42, 9, "rendered"], [54, 19, 42, 17], [54, 21, 42, 19, "setRendered"], [54, 32, 42, 30], [54, 33, 42, 31], [54, 36, 42, 34, "React"], [54, 41, 42, 39], [54, 42, 42, 40, "useState"], [54, 50, 42, 48], [54, 51, 42, 49, "visible"], [54, 58, 42, 56], [54, 59, 42, 57], [55, 4, 43, 2], [55, 10, 43, 8], [55, 11, 43, 9, "visibleAnim"], [55, 22, 43, 20], [55, 23, 43, 21], [55, 26, 43, 24, "React"], [55, 31, 43, 29], [55, 32, 43, 30, "useState"], [55, 40, 43, 38], [55, 41, 43, 39], [55, 47, 43, 45], [55, 51, 43, 49, "Animated"], [55, 68, 43, 57], [55, 69, 43, 58, "Value"], [55, 74, 43, 63], [55, 75, 43, 64, "visible"], [55, 82, 43, 71], [55, 85, 43, 74], [55, 86, 43, 75], [55, 89, 43, 78], [55, 90, 43, 79], [55, 91, 43, 80], [55, 92, 43, 81], [56, 4, 44, 2], [56, 10, 44, 8], [56, 11, 44, 9, "clearVisibleAnim"], [56, 27, 44, 25], [56, 28, 44, 26], [56, 31, 44, 29, "React"], [56, 36, 44, 34], [56, 37, 44, 35, "useState"], [56, 45, 44, 43], [56, 46, 44, 44], [56, 52, 44, 50], [56, 56, 44, 54, "Animated"], [56, 73, 44, 62], [56, 74, 44, 63, "Value"], [56, 79, 44, 68], [56, 80, 44, 69], [56, 81, 44, 70], [56, 82, 44, 71], [56, 83, 44, 72], [57, 4, 45, 2], [57, 10, 45, 8, "visibleValueRef"], [57, 25, 45, 23], [57, 28, 45, 26, "React"], [57, 33, 45, 31], [57, 34, 45, 32, "useRef"], [57, 40, 45, 38], [57, 41, 45, 39, "visible"], [57, 48, 45, 46], [57, 49, 45, 47], [58, 4, 46, 2], [58, 10, 46, 8, "clearVisibleValueRef"], [58, 30, 46, 28], [58, 33, 46, 31, "React"], [58, 38, 46, 36], [58, 39, 46, 37, "useRef"], [58, 45, 46, 43], [58, 46, 46, 44], [58, 51, 46, 49], [58, 52, 46, 50], [59, 4, 47, 2], [59, 10, 47, 8, "inputRef"], [59, 18, 47, 16], [59, 21, 47, 19, "React"], [59, 26, 47, 24], [59, 27, 47, 25, "useRef"], [59, 33, 47, 31], [59, 34, 47, 32], [59, 38, 47, 36], [59, 39, 47, 37], [60, 4, 48, 2, "React"], [60, 9, 48, 7], [60, 10, 48, 8, "useEffect"], [60, 19, 48, 17], [60, 20, 48, 18], [60, 26, 48, 24], [61, 6, 49, 4], [62, 6, 50, 4], [62, 10, 50, 8, "visible"], [62, 17, 50, 15], [62, 22, 50, 20, "visibleValueRef"], [62, 37, 50, 35], [62, 38, 50, 36, "current"], [62, 45, 50, 43], [62, 47, 50, 45], [63, 8, 51, 6], [64, 6, 52, 4], [65, 6, 53, 4, "Animated"], [65, 23, 53, 12], [65, 24, 53, 13, "timing"], [65, 30, 53, 19], [65, 31, 53, 20, "visibleAnim"], [65, 42, 53, 31], [65, 44, 53, 33], [66, 8, 54, 6, "toValue"], [66, 15, 54, 13], [66, 17, 54, 15, "visible"], [66, 24, 54, 22], [66, 27, 54, 25], [66, 28, 54, 26], [66, 31, 54, 29], [66, 32, 54, 30], [67, 8, 55, 6, "duration"], [67, 16, 55, 14], [67, 18, 55, 16], [67, 21, 55, 19], [68, 8, 56, 6, "useNativeDriver"], [69, 6, 57, 4], [69, 7, 57, 5], [69, 8, 57, 6], [69, 9, 57, 7, "start"], [69, 14, 57, 12], [69, 15, 57, 13], [69, 16, 57, 14], [70, 8, 58, 6, "finished"], [71, 6, 59, 4], [71, 7, 59, 5], [71, 12, 59, 10], [72, 8, 60, 6], [72, 12, 60, 10, "finished"], [72, 20, 60, 18], [72, 22, 60, 20], [73, 10, 61, 8, "setRendered"], [73, 21, 61, 19], [73, 22, 61, 20, "visible"], [73, 29, 61, 27], [73, 30, 61, 28], [74, 10, 62, 8, "visibleValueRef"], [74, 25, 62, 23], [74, 26, 62, 24, "current"], [74, 33, 62, 31], [74, 36, 62, 34, "visible"], [74, 43, 62, 41], [75, 8, 63, 6], [76, 6, 64, 4], [76, 7, 64, 5], [76, 8, 64, 6], [77, 6, 65, 4], [77, 13, 65, 11], [77, 19, 65, 17], [78, 8, 66, 6, "visibleAnim"], [78, 19, 66, 17], [78, 20, 66, 18, "stopAnimation"], [78, 33, 66, 31], [78, 34, 66, 32], [78, 35, 66, 33], [79, 6, 67, 4], [79, 7, 67, 5], [80, 4, 68, 2], [80, 5, 68, 3], [80, 7, 68, 5], [80, 8, 68, 6, "visible"], [80, 15, 68, 13], [80, 17, 68, 15, "visibleAnim"], [80, 28, 68, 26], [80, 29, 68, 27], [80, 30, 68, 28], [81, 4, 69, 2], [81, 10, 69, 8, "hasText"], [81, 17, 69, 15], [81, 20, 69, 18, "value"], [81, 25, 69, 23], [81, 30, 69, 28], [81, 32, 69, 30], [82, 4, 70, 2, "React"], [82, 9, 70, 7], [82, 10, 70, 8, "useEffect"], [82, 19, 70, 17], [82, 20, 70, 18], [82, 26, 70, 24], [83, 6, 71, 4], [83, 10, 71, 8, "clearVisibleValueRef"], [83, 30, 71, 28], [83, 31, 71, 29, "current"], [83, 38, 71, 36], [83, 43, 71, 41, "hasText"], [83, 50, 71, 48], [83, 52, 71, 50], [84, 8, 72, 6], [85, 6, 73, 4], [86, 6, 74, 4, "Animated"], [86, 23, 74, 12], [86, 24, 74, 13, "timing"], [86, 30, 74, 19], [86, 31, 74, 20, "clearVisibleAnim"], [86, 47, 74, 36], [86, 49, 74, 38], [87, 8, 75, 6, "toValue"], [87, 15, 75, 13], [87, 17, 75, 15, "hasText"], [87, 24, 75, 22], [87, 27, 75, 25], [87, 28, 75, 26], [87, 31, 75, 29], [87, 32, 75, 30], [88, 8, 76, 6, "duration"], [88, 16, 76, 14], [88, 18, 76, 16], [88, 21, 76, 19], [89, 8, 77, 6, "useNativeDriver"], [90, 6, 78, 4], [90, 7, 78, 5], [90, 8, 78, 6], [90, 9, 78, 7, "start"], [90, 14, 78, 12], [90, 15, 78, 13], [90, 16, 78, 14], [91, 8, 79, 6, "finished"], [92, 6, 80, 4], [92, 7, 80, 5], [92, 12, 80, 10], [93, 8, 81, 6], [93, 12, 81, 10, "finished"], [93, 20, 81, 18], [93, 22, 81, 20], [94, 10, 82, 8, "clearVisibleValueRef"], [94, 30, 82, 28], [94, 31, 82, 29, "current"], [94, 38, 82, 36], [94, 41, 82, 39, "hasText"], [94, 48, 82, 46], [95, 8, 83, 6], [96, 6, 84, 4], [96, 7, 84, 5], [96, 8, 84, 6], [97, 4, 85, 2], [97, 5, 85, 3], [97, 7, 85, 5], [97, 8, 85, 6, "clearVisibleAnim"], [97, 24, 85, 22], [97, 26, 85, 24, "hasText"], [97, 33, 85, 31], [97, 34, 85, 32], [97, 35, 85, 33], [98, 4, 86, 2], [98, 10, 86, 8, "clearText"], [98, 19, 86, 17], [98, 22, 86, 20, "React"], [98, 27, 86, 25], [98, 28, 86, 26, "useCallback"], [98, 39, 86, 37], [98, 40, 86, 38], [98, 46, 86, 44], [99, 6, 87, 4, "inputRef"], [99, 14, 87, 12], [99, 15, 87, 13, "current"], [99, 22, 87, 20], [99, 24, 87, 22, "clear"], [99, 29, 87, 27], [99, 30, 87, 28], [99, 31, 87, 29], [100, 6, 88, 4, "inputRef"], [100, 14, 88, 12], [100, 15, 88, 13, "current"], [100, 22, 88, 20], [100, 24, 88, 22, "focus"], [100, 29, 88, 27], [100, 30, 88, 28], [100, 31, 88, 29], [101, 6, 89, 4, "setValue"], [101, 14, 89, 12], [101, 15, 89, 13], [101, 17, 89, 15], [101, 18, 89, 16], [102, 4, 90, 2], [102, 5, 90, 3], [102, 7, 90, 5], [102, 9, 90, 7], [102, 10, 90, 8], [103, 4, 91, 2], [103, 10, 91, 8, "onClear"], [103, 17, 91, 15], [103, 20, 91, 18, "React"], [103, 25, 91, 23], [103, 26, 91, 24, "useCallback"], [103, 37, 91, 35], [103, 38, 91, 36], [103, 44, 91, 42], [104, 6, 92, 4, "clearText"], [104, 15, 92, 13], [104, 16, 92, 14], [104, 17, 92, 15], [105, 6, 93, 4], [106, 6, 94, 4], [107, 6, 95, 4, "onChangeText"], [107, 18, 95, 16], [107, 21, 95, 19], [108, 8, 96, 6, "nativeEvent"], [108, 19, 96, 17], [108, 21, 96, 19], [109, 10, 97, 8, "text"], [109, 14, 97, 12], [109, 16, 97, 14], [110, 8, 98, 6], [111, 6, 99, 4], [111, 7, 99, 5], [111, 8, 99, 6], [112, 4, 100, 2], [112, 5, 100, 3], [112, 7, 100, 5], [112, 8, 100, 6, "clearText"], [112, 17, 100, 15], [112, 19, 100, 17, "onChangeText"], [112, 31, 100, 29], [112, 32, 100, 30], [112, 33, 100, 31], [113, 4, 101, 2], [113, 10, 101, 8, "cancelSearch"], [113, 22, 101, 20], [113, 25, 101, 23, "React"], [113, 30, 101, 28], [113, 31, 101, 29, "useCallback"], [113, 42, 101, 40], [113, 43, 101, 41], [113, 49, 101, 47], [114, 6, 102, 4, "onClear"], [114, 13, 102, 11], [114, 14, 102, 12], [114, 15, 102, 13], [115, 6, 103, 4, "onClose"], [115, 13, 103, 11], [115, 14, 103, 12], [115, 15, 103, 13], [116, 4, 104, 2], [116, 5, 104, 3], [116, 7, 104, 5], [116, 8, 104, 6, "onClear"], [116, 15, 104, 13], [116, 17, 104, 15, "onClose"], [116, 24, 104, 22], [116, 25, 104, 23], [116, 26, 104, 24], [117, 4, 105, 2, "React"], [117, 9, 105, 7], [117, 10, 105, 8, "useEffect"], [117, 19, 105, 17], [117, 20, 105, 18], [117, 26, 105, 24, "navigation"], [117, 36, 105, 34], [117, 38, 105, 36, "addListener"], [117, 49, 105, 47], [117, 50, 105, 48], [117, 56, 105, 54], [117, 58, 105, 56, "cancelSearch"], [117, 70, 105, 68], [117, 71, 105, 69], [117, 73, 105, 71], [117, 74, 105, 72, "cancelSearch"], [117, 86, 105, 84], [117, 88, 105, 86, "navigation"], [117, 98, 105, 96], [117, 99, 105, 97], [117, 100, 105, 98], [118, 4, 106, 2, "React"], [118, 9, 106, 7], [118, 10, 106, 8, "useImperativeHandle"], [118, 29, 106, 27], [118, 30, 106, 28, "ref"], [118, 33, 106, 31], [118, 35, 106, 33], [118, 42, 106, 40], [119, 6, 107, 4, "focus"], [119, 11, 107, 9], [119, 13, 107, 11, "focus"], [119, 14, 107, 11], [119, 19, 107, 17], [120, 8, 108, 6, "inputRef"], [120, 16, 108, 14], [120, 17, 108, 15, "current"], [120, 24, 108, 22], [120, 26, 108, 24, "focus"], [120, 31, 108, 29], [120, 32, 108, 30], [120, 33, 108, 31], [121, 6, 109, 4], [121, 7, 109, 5], [122, 6, 110, 4, "blur"], [122, 10, 110, 8], [122, 12, 110, 10, "blur"], [122, 13, 110, 10], [122, 18, 110, 16], [123, 8, 111, 6, "inputRef"], [123, 16, 111, 14], [123, 17, 111, 15, "current"], [123, 24, 111, 22], [123, 26, 111, 24, "blur"], [123, 30, 111, 28], [123, 31, 111, 29], [123, 32, 111, 30], [124, 6, 112, 4], [124, 7, 112, 5], [125, 6, 113, 4, "setText"], [125, 13, 113, 11], [125, 15, 113, 13, "text"], [125, 19, 113, 17], [125, 23, 113, 21], [126, 8, 114, 6, "inputRef"], [126, 16, 114, 14], [126, 17, 114, 15, "current"], [126, 24, 114, 22], [126, 26, 114, 24, "setNativeProps"], [126, 40, 114, 38], [126, 41, 114, 39], [127, 10, 115, 8, "text"], [128, 8, 116, 6], [128, 9, 116, 7], [128, 10, 116, 8], [129, 8, 117, 6, "setValue"], [129, 16, 117, 14], [129, 17, 117, 15, "text"], [129, 21, 117, 19], [129, 22, 117, 20], [130, 6, 118, 4], [130, 7, 118, 5], [131, 6, 119, 4, "clearText"], [131, 15, 119, 13], [132, 6, 120, 4, "cancelSearch"], [133, 4, 121, 2], [133, 5, 121, 3], [133, 6, 121, 4], [133, 8, 121, 6], [133, 9, 121, 7, "cancelSearch"], [133, 21, 121, 19], [133, 23, 121, 21, "clearText"], [133, 32, 121, 30], [133, 33, 121, 31], [133, 34, 121, 32], [134, 4, 122, 2], [134, 8, 122, 6], [134, 9, 122, 7, "visible"], [134, 16, 122, 14], [134, 20, 122, 18], [134, 21, 122, 19, "rendered"], [134, 29, 122, 27], [134, 31, 122, 29], [135, 6, 123, 4], [135, 13, 123, 11], [135, 17, 123, 15], [136, 4, 124, 2], [137, 4, 125, 2], [137, 10, 125, 8, "textColor"], [137, 19, 125, 17], [137, 22, 125, 20, "tintColor"], [137, 31, 125, 29], [137, 35, 125, 33, "colors"], [137, 41, 125, 39], [137, 42, 125, 40, "text"], [137, 46, 125, 44], [138, 4, 126, 2], [138, 11, 126, 9], [138, 24, 126, 22], [138, 28, 126, 22, "_jsxs"], [138, 44, 126, 27], [138, 46, 126, 28, "Animated"], [138, 63, 126, 36], [138, 64, 126, 37, "View"], [138, 68, 126, 41], [138, 70, 126, 43], [139, 6, 127, 4, "pointerEvents"], [139, 19, 127, 17], [139, 21, 127, 19, "visible"], [139, 28, 127, 26], [139, 31, 127, 29], [139, 37, 127, 35], [139, 40, 127, 38], [139, 46, 127, 44], [140, 6, 128, 4], [140, 17, 128, 15], [140, 19, 128, 17], [140, 27, 128, 25], [141, 6, 129, 4], [141, 19, 129, 17], [141, 21, 129, 19], [141, 22, 129, 20, "visible"], [141, 29, 129, 27], [142, 6, 130, 4, "style"], [142, 11, 130, 9], [142, 13, 130, 11], [142, 14, 130, 12, "styles"], [142, 20, 130, 18], [142, 21, 130, 19, "container"], [142, 30, 130, 28], [142, 32, 130, 30], [143, 8, 131, 6, "opacity"], [143, 15, 131, 13], [143, 17, 131, 15, "visibleAnim"], [144, 6, 132, 4], [144, 7, 132, 5], [144, 9, 132, 7, "style"], [144, 14, 132, 12], [144, 15, 132, 13], [145, 6, 133, 4, "children"], [145, 14, 133, 12], [145, 16, 133, 14], [145, 17, 133, 15], [145, 30, 133, 28], [145, 34, 133, 28, "_jsxs"], [145, 50, 133, 33], [145, 52, 133, 34, "View"], [145, 65, 133, 38], [145, 67, 133, 40], [146, 8, 134, 6, "style"], [146, 13, 134, 11], [146, 15, 134, 13, "styles"], [146, 21, 134, 19], [146, 22, 134, 20, "searchbarContainer"], [146, 40, 134, 38], [147, 8, 135, 6, "children"], [147, 16, 135, 14], [147, 18, 135, 16], [147, 19, 135, 17], [147, 32, 135, 30], [147, 36, 135, 30, "_jsx"], [147, 51, 135, 34], [147, 53, 135, 35, "HeaderIcon"], [147, 75, 135, 45], [147, 77, 135, 47], [148, 10, 136, 8, "source"], [148, 16, 136, 14], [148, 18, 136, 16, "searchIcon"], [148, 37, 136, 26], [149, 10, 137, 8, "tintColor"], [149, 19, 137, 17], [149, 21, 137, 19, "textColor"], [149, 30, 137, 28], [150, 10, 138, 8, "style"], [150, 15, 138, 13], [150, 17, 138, 15, "styles"], [150, 23, 138, 21], [150, 24, 138, 22, "inputSearchIcon"], [151, 8, 139, 6], [151, 9, 139, 7], [151, 10, 139, 8], [151, 12, 139, 10], [151, 25, 139, 23], [151, 29, 139, 23, "_jsx"], [151, 44, 139, 27], [151, 46, 139, 28, "TextInput"], [151, 64, 139, 37], [151, 66, 139, 39], [152, 10, 140, 8], [152, 13, 140, 11, "rest"], [152, 17, 140, 15], [153, 10, 141, 8, "ref"], [153, 13, 141, 11], [153, 15, 141, 13, "inputRef"], [153, 23, 141, 21], [154, 10, 142, 8, "onChange"], [154, 18, 142, 16], [154, 20, 142, 18, "onChangeText"], [154, 32, 142, 30], [155, 10, 143, 8, "onChangeText"], [155, 22, 143, 20], [155, 24, 143, 22, "setValue"], [155, 32, 143, 30], [156, 10, 144, 8, "autoFocus"], [156, 19, 144, 17], [156, 21, 144, 19, "autoFocus"], [156, 30, 144, 28], [157, 10, 145, 8, "inputMode"], [157, 19, 145, 17], [157, 21, 145, 19, "INPUT_TYPE_TO_MODE"], [157, 39, 145, 37], [157, 40, 145, 38, "inputType"], [157, 49, 145, 47], [157, 53, 145, 51], [157, 59, 145, 57], [157, 60, 145, 58], [158, 10, 146, 8, "enterKeyHint"], [158, 22, 146, 20], [158, 24, 146, 22, "enterKeyHint"], [158, 36, 146, 34], [159, 10, 147, 8, "placeholder"], [159, 21, 147, 19], [159, 23, 147, 21, "placeholder"], [159, 34, 147, 32], [160, 10, 148, 8, "placeholderTextColor"], [160, 30, 148, 28], [160, 32, 148, 30], [160, 36, 148, 30, "Color"], [160, 50, 148, 35], [160, 52, 148, 36, "textColor"], [160, 61, 148, 45], [160, 62, 148, 46], [160, 63, 148, 47, "alpha"], [160, 68, 148, 52], [160, 69, 148, 53], [160, 72, 148, 56], [160, 73, 148, 57], [160, 74, 148, 58, "string"], [160, 80, 148, 64], [160, 81, 148, 65], [160, 82, 148, 66], [161, 10, 149, 8, "cursorColor"], [161, 21, 149, 19], [161, 23, 149, 21, "colors"], [161, 29, 149, 27], [161, 30, 149, 28, "primary"], [161, 37, 149, 35], [162, 10, 150, 8, "selectionHandleColor"], [162, 30, 150, 28], [162, 32, 150, 30, "colors"], [162, 38, 150, 36], [162, 39, 150, 37, "primary"], [162, 46, 150, 44], [163, 10, 151, 8, "selectionColor"], [163, 24, 151, 22], [163, 26, 151, 24], [163, 30, 151, 24, "Color"], [163, 44, 151, 29], [163, 46, 151, 30, "colors"], [163, 52, 151, 36], [163, 53, 151, 37, "primary"], [163, 60, 151, 44], [163, 61, 151, 45], [163, 62, 151, 46, "alpha"], [163, 67, 151, 51], [163, 68, 151, 52], [163, 71, 151, 55], [163, 72, 151, 56], [163, 73, 151, 57, "string"], [163, 79, 151, 63], [163, 80, 151, 64], [163, 81, 151, 65], [164, 10, 152, 8, "style"], [164, 15, 152, 13], [164, 17, 152, 15], [164, 18, 152, 16, "fonts"], [164, 23, 152, 21], [164, 24, 152, 22, "regular"], [164, 31, 152, 29], [164, 33, 152, 31, "styles"], [164, 39, 152, 37], [164, 40, 152, 38, "searchbar"], [164, 49, 152, 47], [164, 51, 152, 49], [165, 12, 153, 10, "backgroundColor"], [165, 27, 153, 25], [165, 29, 153, 27, "Platform"], [165, 46, 153, 35], [165, 47, 153, 36, "select"], [165, 53, 153, 42], [165, 54, 153, 43], [166, 14, 154, 12, "ios"], [166, 17, 154, 15], [166, 19, 154, 17, "dark"], [166, 23, 154, 21], [166, 26, 154, 24], [166, 52, 154, 50], [166, 55, 154, 53], [166, 75, 154, 73], [167, 14, 155, 12, "default"], [167, 21, 155, 19], [167, 23, 155, 21], [168, 12, 156, 10], [168, 13, 156, 11], [168, 14, 156, 12], [169, 12, 157, 10, "color"], [169, 17, 157, 15], [169, 19, 157, 17, "textColor"], [169, 28, 157, 26], [170, 12, 158, 10, "borderBottomColor"], [170, 29, 158, 27], [170, 31, 158, 29], [170, 35, 158, 29, "Color"], [170, 49, 158, 34], [170, 51, 158, 35, "textColor"], [170, 60, 158, 44], [170, 61, 158, 45], [170, 62, 158, 46, "alpha"], [170, 67, 158, 51], [170, 68, 158, 52], [170, 71, 158, 55], [170, 72, 158, 56], [170, 73, 158, 57, "string"], [170, 79, 158, 63], [170, 80, 158, 64], [171, 10, 159, 8], [171, 11, 159, 9], [172, 8, 160, 6], [172, 9, 160, 7], [172, 10, 160, 8], [172, 12, 160, 10, "Platform"], [172, 29, 160, 18], [172, 30, 160, 19, "OS"], [172, 32, 160, 21], [172, 37, 160, 26], [172, 42, 160, 31], [172, 45, 160, 34], [172, 58, 160, 47], [172, 62, 160, 47, "_jsx"], [172, 77, 160, 51], [172, 79, 160, 52, "PlatformPressable"], [172, 115, 160, 69], [172, 117, 160, 71], [173, 10, 161, 8, "onPress"], [173, 17, 161, 15], [173, 19, 161, 17, "onClear"], [173, 26, 161, 24], [174, 10, 162, 8, "style"], [174, 15, 162, 13], [174, 17, 162, 15], [174, 18, 162, 16], [175, 12, 163, 10, "opacity"], [175, 19, 163, 17], [175, 21, 163, 19, "clearVisibleAnim"], [175, 37, 163, 35], [176, 12, 164, 10, "transform"], [176, 21, 164, 19], [176, 23, 164, 21], [176, 24, 164, 22], [177, 14, 165, 12, "scale"], [177, 19, 165, 17], [177, 21, 165, 19, "clearVisibleAnim"], [178, 12, 166, 10], [178, 13, 166, 11], [179, 10, 167, 8], [179, 11, 167, 9], [179, 13, 167, 11, "styles"], [179, 19, 167, 17], [179, 20, 167, 18, "clearButton"], [179, 31, 167, 29], [179, 32, 167, 30], [180, 10, 168, 8, "children"], [180, 18, 168, 16], [180, 20, 168, 18], [180, 33, 168, 31], [180, 37, 168, 31, "_jsx"], [180, 52, 168, 35], [180, 54, 168, 36, "Image"], [180, 68, 168, 41], [180, 70, 168, 43], [181, 12, 169, 10, "source"], [181, 18, 169, 16], [181, 20, 169, 18, "clearIcon"], [181, 38, 169, 27], [182, 12, 170, 10, "resizeMode"], [182, 22, 170, 20], [182, 24, 170, 22], [182, 33, 170, 31], [183, 12, 171, 10, "tintColor"], [183, 21, 171, 19], [183, 23, 171, 21, "textColor"], [183, 32, 171, 30], [184, 12, 172, 10, "style"], [184, 17, 172, 15], [184, 19, 172, 17, "styles"], [184, 25, 172, 23], [184, 26, 172, 24, "clearIcon"], [185, 10, 173, 8], [185, 11, 173, 9], [186, 8, 174, 6], [186, 9, 174, 7], [186, 10, 174, 8], [186, 13, 174, 11], [186, 17, 174, 15], [187, 6, 175, 4], [187, 7, 175, 5], [187, 8, 175, 6], [187, 10, 175, 8, "Platform"], [187, 27, 175, 16], [187, 28, 175, 17, "OS"], [187, 30, 175, 19], [187, 35, 175, 24], [187, 40, 175, 29], [187, 43, 175, 32], [187, 56, 175, 45], [187, 60, 175, 45, "_jsx"], [187, 75, 175, 49], [187, 77, 175, 50, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [187, 103, 175, 62], [187, 105, 175, 64], [188, 8, 176, 6, "onPress"], [188, 15, 176, 13], [188, 17, 176, 15, "onPress"], [188, 18, 176, 15], [188, 23, 176, 21], [189, 10, 177, 8], [189, 14, 177, 12, "value"], [189, 19, 177, 17], [189, 21, 177, 19], [190, 12, 178, 10, "onClear"], [190, 19, 178, 17], [190, 20, 178, 18], [190, 21, 178, 19], [191, 10, 179, 8], [191, 11, 179, 9], [191, 17, 179, 15], [192, 12, 180, 10, "onClose"], [192, 19, 180, 17], [192, 20, 180, 18], [192, 21, 180, 19], [193, 10, 181, 8], [194, 8, 182, 6], [194, 9, 182, 7], [195, 8, 183, 6, "style"], [195, 13, 183, 11], [195, 15, 183, 13, "styles"], [195, 21, 183, 19], [195, 22, 183, 20, "closeButton"], [195, 33, 183, 31], [196, 8, 184, 6, "children"], [196, 16, 184, 14], [196, 18, 184, 16], [196, 31, 184, 29], [196, 35, 184, 29, "_jsx"], [196, 50, 184, 33], [196, 52, 184, 34, "HeaderIcon"], [196, 74, 184, 44], [196, 76, 184, 46], [197, 10, 185, 8, "source"], [197, 16, 185, 14], [197, 18, 185, 16, "closeIcon"], [197, 36, 185, 25], [198, 10, 186, 8, "tintColor"], [198, 19, 186, 17], [198, 21, 186, 19, "textColor"], [199, 8, 187, 6], [199, 9, 187, 7], [200, 6, 188, 4], [200, 7, 188, 5], [200, 8, 188, 6], [200, 11, 188, 9], [200, 15, 188, 13], [200, 17, 188, 15, "Platform"], [200, 34, 188, 23], [200, 35, 188, 24, "OS"], [200, 37, 188, 26], [200, 42, 188, 31], [200, 47, 188, 36], [200, 50, 188, 39], [200, 63, 188, 52], [200, 67, 188, 52, "_jsx"], [200, 82, 188, 56], [200, 84, 188, 57, "PlatformPressable"], [200, 120, 188, 74], [200, 122, 188, 76], [201, 8, 189, 6, "onPress"], [201, 15, 189, 13], [201, 17, 189, 15, "cancelSearch"], [201, 29, 189, 27], [202, 8, 190, 6, "style"], [202, 13, 190, 11], [202, 15, 190, 13, "styles"], [202, 21, 190, 19], [202, 22, 190, 20, "cancelButton"], [202, 34, 190, 32], [203, 8, 191, 6, "children"], [203, 16, 191, 14], [203, 18, 191, 16], [203, 31, 191, 29], [203, 35, 191, 29, "_jsx"], [203, 50, 191, 33], [203, 52, 191, 34, "Text"], [203, 62, 191, 38], [203, 64, 191, 40], [204, 10, 192, 8, "style"], [204, 15, 192, 13], [204, 17, 192, 15], [204, 18, 192, 16, "fonts"], [204, 23, 192, 21], [204, 24, 192, 22, "regular"], [204, 31, 192, 29], [204, 33, 192, 31], [205, 12, 193, 10, "color"], [205, 17, 193, 15], [205, 19, 193, 17, "tintColor"], [205, 28, 193, 26], [205, 32, 193, 30, "colors"], [205, 38, 193, 36], [205, 39, 193, 37, "primary"], [206, 10, 194, 8], [206, 11, 194, 9], [206, 13, 194, 11, "styles"], [206, 19, 194, 17], [206, 20, 194, 18, "cancelText"], [206, 30, 194, 28], [206, 31, 194, 29], [207, 10, 195, 8, "children"], [207, 18, 195, 16], [207, 20, 195, 18, "cancelButtonText"], [208, 8, 196, 6], [208, 9, 196, 7], [209, 6, 197, 4], [209, 7, 197, 5], [209, 8, 197, 6], [209, 11, 197, 9], [209, 15, 197, 13], [210, 4, 198, 2], [210, 5, 198, 3], [210, 6, 198, 4], [211, 2, 199, 0], [212, 2, 200, 0], [212, 8, 200, 6, "styles"], [212, 14, 200, 12], [212, 17, 200, 15, "StyleSheet"], [212, 36, 200, 25], [212, 37, 200, 26, "create"], [212, 43, 200, 32], [212, 44, 200, 33], [213, 4, 201, 2, "container"], [213, 13, 201, 11], [213, 15, 201, 13], [214, 6, 202, 4, "flex"], [214, 10, 202, 8], [214, 12, 202, 10], [214, 13, 202, 11], [215, 6, 203, 4, "flexDirection"], [215, 19, 203, 17], [215, 21, 203, 19], [215, 26, 203, 24], [216, 6, 204, 4, "alignItems"], [216, 16, 204, 14], [216, 18, 204, 16], [217, 4, 205, 2], [217, 5, 205, 3], [218, 4, 206, 2, "inputSearchIcon"], [218, 19, 206, 17], [218, 21, 206, 19], [219, 6, 207, 4, "position"], [219, 14, 207, 12], [219, 16, 207, 14], [219, 26, 207, 24], [220, 6, 208, 4, "opacity"], [220, 13, 208, 11], [220, 15, 208, 13], [220, 18, 208, 16], [221, 6, 209, 4, "left"], [221, 10, 209, 8], [221, 12, 209, 10, "Platform"], [221, 29, 209, 18], [221, 30, 209, 19, "select"], [221, 36, 209, 25], [221, 37, 209, 26], [222, 8, 210, 6, "ios"], [222, 11, 210, 9], [222, 13, 210, 11], [222, 15, 210, 13], [223, 8, 211, 6, "default"], [223, 15, 211, 13], [223, 17, 211, 15], [224, 6, 212, 4], [224, 7, 212, 5], [224, 8, 212, 6], [225, 6, 213, 4, "top"], [225, 9, 213, 7], [225, 11, 213, 9, "Platform"], [225, 28, 213, 17], [225, 29, 213, 18, "select"], [225, 35, 213, 24], [225, 36, 213, 25], [226, 8, 214, 6, "ios"], [226, 11, 214, 9], [226, 13, 214, 11], [226, 14, 214, 12], [226, 15, 214, 13], [227, 8, 215, 6, "default"], [227, 15, 215, 13], [227, 17, 215, 15], [228, 6, 216, 4], [228, 7, 216, 5], [228, 8, 216, 6], [229, 6, 217, 4], [229, 9, 217, 7, "Platform"], [229, 26, 217, 15], [229, 27, 217, 16, "select"], [229, 33, 217, 22], [229, 34, 217, 23], [230, 8, 218, 6, "ios"], [230, 11, 218, 9], [230, 13, 218, 11], [231, 10, 219, 8, "height"], [231, 16, 219, 14], [231, 18, 219, 16], [231, 20, 219, 18], [232, 10, 220, 8, "width"], [232, 15, 220, 13], [232, 17, 220, 15], [233, 8, 221, 6], [233, 9, 221, 7], [234, 8, 222, 6, "default"], [234, 15, 222, 13], [234, 17, 222, 15], [234, 18, 222, 16], [235, 6, 223, 4], [235, 7, 223, 5], [236, 4, 224, 2], [236, 5, 224, 3], [237, 4, 225, 2, "closeButton"], [237, 15, 225, 13], [237, 17, 225, 15], [238, 6, 226, 4, "position"], [238, 14, 226, 12], [238, 16, 226, 14], [238, 26, 226, 24], [239, 6, 227, 4, "opacity"], [239, 13, 227, 11], [239, 15, 227, 13], [239, 18, 227, 16], [240, 6, 228, 4, "right"], [240, 11, 228, 9], [240, 13, 228, 11, "Platform"], [240, 30, 228, 19], [240, 31, 228, 20, "select"], [240, 37, 228, 26], [240, 38, 228, 27], [241, 8, 229, 6, "ios"], [241, 11, 229, 9], [241, 13, 229, 11], [241, 14, 229, 12], [242, 8, 230, 6, "default"], [242, 15, 230, 13], [242, 17, 230, 15], [243, 6, 231, 4], [243, 7, 231, 5], [243, 8, 231, 6], [244, 6, 232, 4, "top"], [244, 9, 232, 7], [244, 11, 232, 9, "Platform"], [244, 28, 232, 17], [244, 29, 232, 18, "select"], [244, 35, 232, 24], [244, 36, 232, 25], [245, 8, 233, 6, "ios"], [245, 11, 233, 9], [245, 13, 233, 11], [245, 14, 233, 12], [245, 15, 233, 13], [246, 8, 234, 6, "default"], [246, 15, 234, 13], [246, 17, 234, 15], [247, 6, 235, 4], [247, 7, 235, 5], [248, 4, 236, 2], [248, 5, 236, 3], [249, 4, 237, 2, "clearButton"], [249, 15, 237, 13], [249, 17, 237, 15], [250, 6, 238, 4, "position"], [250, 14, 238, 12], [250, 16, 238, 14], [250, 26, 238, 24], [251, 6, 239, 4, "right"], [251, 11, 239, 9], [251, 13, 239, 11], [251, 14, 239, 12], [252, 6, 240, 4, "top"], [252, 9, 240, 7], [252, 11, 240, 9], [252, 12, 240, 10], [252, 13, 240, 11], [253, 6, 241, 4, "bottom"], [253, 12, 241, 10], [253, 14, 241, 12], [253, 15, 241, 13], [254, 6, 242, 4, "justifyContent"], [254, 20, 242, 18], [254, 22, 242, 20], [254, 30, 242, 28], [255, 6, 243, 4, "padding"], [255, 13, 243, 11], [255, 15, 243, 13], [256, 4, 244, 2], [256, 5, 244, 3], [257, 4, 245, 2, "clearIcon"], [257, 13, 245, 11], [257, 15, 245, 13], [258, 6, 246, 4, "height"], [258, 12, 246, 10], [258, 14, 246, 12], [258, 16, 246, 14], [259, 6, 247, 4, "width"], [259, 11, 247, 9], [259, 13, 247, 11], [259, 15, 247, 13], [260, 6, 248, 4, "opacity"], [260, 13, 248, 11], [260, 15, 248, 13], [261, 4, 249, 2], [261, 5, 249, 3], [262, 4, 250, 2, "cancelButton"], [262, 16, 250, 14], [262, 18, 250, 16], [263, 6, 251, 4, "alignSelf"], [263, 15, 251, 13], [263, 17, 251, 15], [263, 25, 251, 23], [264, 6, 252, 4, "top"], [264, 9, 252, 7], [264, 11, 252, 9], [264, 12, 252, 10], [265, 4, 253, 2], [265, 5, 253, 3], [266, 4, 254, 2, "cancelText"], [266, 14, 254, 12], [266, 16, 254, 14], [267, 6, 255, 4, "fontSize"], [267, 14, 255, 12], [267, 16, 255, 14], [267, 18, 255, 16], [268, 6, 256, 4, "marginHorizontal"], [268, 22, 256, 20], [268, 24, 256, 22], [269, 4, 257, 2], [269, 5, 257, 3], [270, 4, 258, 2, "searchbarContainer"], [270, 22, 258, 20], [270, 24, 258, 22], [271, 6, 259, 4, "flex"], [271, 10, 259, 8], [271, 12, 259, 10], [272, 4, 260, 2], [272, 5, 260, 3], [273, 4, 261, 2, "searchbar"], [273, 13, 261, 11], [273, 15, 261, 13, "Platform"], [273, 32, 261, 21], [273, 33, 261, 22, "select"], [273, 39, 261, 28], [273, 40, 261, 29], [274, 6, 262, 4, "ios"], [274, 9, 262, 7], [274, 11, 262, 9], [275, 8, 263, 6, "flex"], [275, 12, 263, 10], [275, 14, 263, 12], [275, 15, 263, 13], [276, 8, 264, 6, "fontSize"], [276, 16, 264, 14], [276, 18, 264, 16], [276, 20, 264, 18], [277, 8, 265, 6, "paddingHorizontal"], [277, 25, 265, 23], [277, 27, 265, 25], [277, 29, 265, 27], [278, 8, 266, 6, "marginLeft"], [278, 18, 266, 16], [278, 20, 266, 18], [278, 22, 266, 20], [279, 8, 267, 6, "marginTop"], [279, 17, 267, 15], [279, 19, 267, 17], [279, 20, 267, 18], [279, 21, 267, 19], [280, 8, 268, 6, "marginBottom"], [280, 20, 268, 18], [280, 22, 268, 20], [280, 23, 268, 21], [281, 8, 269, 6, "borderRadius"], [281, 20, 269, 18], [281, 22, 269, 20], [282, 6, 270, 4], [282, 7, 270, 5], [283, 6, 271, 4, "default"], [283, 13, 271, 11], [283, 15, 271, 13], [284, 8, 272, 6, "flex"], [284, 12, 272, 10], [284, 14, 272, 12], [284, 15, 272, 13], [285, 8, 273, 6, "fontSize"], [285, 16, 273, 14], [285, 18, 273, 16], [285, 20, 273, 18], [286, 8, 274, 6, "paddingHorizontal"], [286, 25, 274, 23], [286, 27, 274, 25], [286, 29, 274, 27], [287, 8, 275, 6, "marginRight"], [287, 19, 275, 17], [287, 21, 275, 19], [287, 22, 275, 20], [288, 8, 276, 6, "marginTop"], [288, 17, 276, 15], [288, 19, 276, 17], [288, 20, 276, 18], [289, 8, 277, 6, "marginBottom"], [289, 20, 277, 18], [289, 22, 277, 20], [289, 23, 277, 21], [290, 8, 278, 6, "borderBottomWidth"], [290, 25, 278, 23], [290, 27, 278, 25], [291, 6, 279, 4], [292, 4, 280, 2], [292, 5, 280, 3], [293, 2, 281, 0], [293, 3, 281, 1], [293, 4, 281, 2], [294, 2, 282, 7], [294, 8, 282, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [294, 23, 282, 28], [294, 26, 282, 28, "exports"], [294, 33, 282, 28], [294, 34, 282, 28, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [294, 49, 282, 28], [294, 52, 282, 31], [294, 65, 282, 44, "React"], [294, 70, 282, 49], [294, 71, 282, 50, "forwardRef"], [294, 81, 282, 60], [294, 82, 282, 61, "HeaderSearchBarInternal"], [294, 105, 282, 84], [294, 106, 282, 85], [295, 0, 282, 86], [295, 3]], "functionMap": {"names": ["<global>", "HeaderSearchBarInternal", "React.useState$argument_0", "React.useEffect$argument_0", "Animated.timing.start$argument_0", "<anonymous>", "clearText", "onClear", "cancelSearch", "React.useImperativeHandle$argument_1", "focus", "blur", "setText", "_jsx$argument_1.onPress"], "mappings": "AAA;ACqB;uCCqB,yCD;4CCC,2BD;kBEI;aCS;KDO;WEC;KFE;GFC;kBEE;aCQ;KDM;GFC;sCKC;GLI;oCMC;GNS;yCOC;GPG;kBEC,mDF;iCQC;WCC;KDE;UEC;KFE;aGC;KHK;IRG;eYuD;OZM;CDiB"}}, "type": "js/module"}]}