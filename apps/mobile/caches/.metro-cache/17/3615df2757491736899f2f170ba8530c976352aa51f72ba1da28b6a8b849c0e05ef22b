{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  module.exports = _nonIterableRest, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 6, "map": [[2, 2, 1, 0], [2, 11, 1, 9, "_nonIterableRest"], [2, 27, 1, 25, "_nonIterableRest"], [2, 28, 1, 25], [2, 30, 1, 28], [3, 4, 2, 2], [3, 10, 2, 8], [3, 14, 2, 12, "TypeError"], [3, 23, 2, 21], [3, 24, 2, 22], [3, 163, 2, 161], [3, 164, 2, 162], [4, 2, 3, 0], [5, 2, 4, 0, "module"], [5, 8, 4, 6], [5, 9, 4, 7, "exports"], [5, 16, 4, 14], [5, 19, 4, 17, "_nonIterableRest"], [5, 35, 4, 33], [5, 37, 4, 35, "module"], [5, 43, 4, 41], [5, 44, 4, 42, "exports"], [5, 51, 4, 49], [5, 52, 4, 50, "__esModule"], [5, 62, 4, 60], [5, 65, 4, 63], [5, 69, 4, 67], [5, 71, 4, 69, "module"], [5, 77, 4, 75], [5, 78, 4, 76, "exports"], [5, 85, 4, 83], [5, 86, 4, 84], [5, 95, 4, 93], [5, 96, 4, 94], [5, 99, 4, 97, "module"], [5, 105, 4, 103], [5, 106, 4, 104, "exports"], [5, 113, 4, 111], [6, 0, 4, 112], [6, 3]], "functionMap": {"names": ["_nonIterableRest", "<global>"], "mappings": "AAA;CCE"}}, "type": "js/module"}]}