{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var MessageSquareDiff = exports.default = (0, _createLucideIcon.default)(\"MessageSquareDiff\", [[\"path\", {\n    d: \"m5 19-2 2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2\",\n    key: \"1xuzuj\"\n  }], [\"path\", {\n    d: \"M9 10h6\",\n    key: \"9gxzsh\"\n  }], [\"path\", {\n    d: \"M12 7v6\",\n    key: \"lw1j43\"\n  }], [\"path\", {\n    d: \"M9 17h6\",\n    key: \"r8uit2\"\n  }]]);\n});", "lineCount": 28, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "MessageSquareDiff"], [15, 23, 10, 23], [15, 26, 10, 23, "exports"], [15, 33, 10, 23], [15, 34, 10, 23, "default"], [15, 41, 10, 23], [15, 44, 10, 26], [15, 48, 10, 26, "createLucideIcon"], [15, 73, 10, 42], [15, 75, 10, 43], [15, 94, 10, 62], [15, 96, 10, 64], [15, 97, 11, 2], [15, 98, 11, 3], [15, 104, 11, 9], [15, 106, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 68, 11, 77], [17, 4, 11, 79, "key"], [17, 7, 11, 82], [17, 9, 11, 84], [18, 2, 11, 93], [18, 3, 11, 94], [18, 4, 11, 95], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 16, 12, 25], [20, 4, 12, 27, "key"], [20, 7, 12, 30], [20, 9, 12, 32], [21, 2, 12, 41], [21, 3, 12, 42], [21, 4, 12, 43], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 16, 13, 25], [23, 4, 13, 27, "key"], [23, 7, 13, 30], [23, 9, 13, 32], [24, 2, 13, 41], [24, 3, 13, 42], [24, 4, 13, 43], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 16, 14, 25], [26, 4, 14, 27, "key"], [26, 7, 14, 30], [26, 9, 14, 32], [27, 2, 14, 41], [27, 3, 14, 42], [27, 4, 14, 43], [27, 5, 15, 1], [27, 6, 15, 2], [28, 0, 15, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}