{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./DiscreteGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 62, "index": 62}}], "key": "dpOMqAtzFjjUQychI3TYNsYmXQE=", "exportNames": ["*"]}}, {"name": "./NodeManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 63}, "end": {"line": 2, "column": 45, "index": 108}}], "key": "krMbHBNX+RO/bw1gHRLBNgemf/A=", "exportNames": ["*"]}}, {"name": "./PressGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 109}, "end": {"line": 3, "column": 56, "index": 165}}], "key": "K7JCCzt/0tQ/4D2w8oVjath5+4I=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 166}, "end": {"line": 4, "column": 58, "index": 224}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _DiscreteGestureHandler = _interopRequireDefault(require(_dependencyMap[1], \"./DiscreteGestureHandler\"));\n  var NodeManager = _interopRequireWildcard(require(_dependencyMap[2], \"./NodeManager\"));\n  var _PressGestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./PressGestureHandler\"));\n  var _utils = require(_dependencyMap[4], \"./utils\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  class NativeViewGestureHandler extends _PressGestureHandler.default {\n    get isNative() {\n      return true;\n    }\n    onRawEvent(ev) {\n      super.onRawEvent(ev);\n      if (!ev.isFinal) {\n        // if (this.ref instanceof ScrollView) {\n        if ((0, _utils.TEST_MIN_IF_NOT_NAN)((0, _utils.VEC_LEN_SQ)({\n          x: ev.deltaX,\n          y: ev.deltaY\n        }), 10)) {\n          // @ts-ignore FIXME(TS) config type\n          if (this.config.disallowInterruption) {\n            const gestures = Object.values(NodeManager.getNodes()).filter(gesture => {\n              const {\n                handlerTag,\n                view,\n                isGestureRunning\n              } = gesture;\n              return (\n                // Check if this gesture isn't self\n                handlerTag !== this.handlerTag &&\n                // Ensure the gesture needs to be cancelled\n                isGestureRunning &&\n                // ScrollView can cancel discrete gestures like taps and presses\n                gesture instanceof _DiscreteGestureHandler.default &&\n                // Ensure a view exists and is a child of the current view\n                view &&\n                // @ts-ignore FIXME(TS) view type\n                this.view.contains(view)\n              );\n            }); // Cancel all of the gestures that passed the filter\n\n            for (const gesture of gestures) {\n              // TODO: Bacon: Send some cached event.\n              gesture.forceInvalidate(ev);\n            }\n          }\n        }\n      }\n    }\n  }\n  var _default = exports.default = NativeViewGestureHandler;\n});", "lineCount": 56, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_DiscreteGestureHandler"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "NodeManager"], [8, 17, 2, 0], [8, 20, 2, 0, "_interopRequireWildcard"], [8, 43, 2, 0], [8, 44, 2, 0, "require"], [8, 51, 2, 0], [8, 52, 2, 0, "_dependencyMap"], [8, 66, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_PressGestureHandler"], [9, 26, 3, 0], [9, 29, 3, 0, "_interopRequireDefault"], [9, 51, 3, 0], [9, 52, 3, 0, "require"], [9, 59, 3, 0], [9, 60, 3, 0, "_dependencyMap"], [9, 74, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_utils"], [10, 12, 4, 0], [10, 15, 4, 0, "require"], [10, 22, 4, 0], [10, 23, 4, 0, "_dependencyMap"], [10, 37, 4, 0], [11, 2, 4, 58], [11, 11, 4, 58, "_interopRequireWildcard"], [11, 35, 4, 58, "e"], [11, 36, 4, 58], [11, 38, 4, 58, "t"], [11, 39, 4, 58], [11, 68, 4, 58, "WeakMap"], [11, 75, 4, 58], [11, 81, 4, 58, "r"], [11, 82, 4, 58], [11, 89, 4, 58, "WeakMap"], [11, 96, 4, 58], [11, 100, 4, 58, "n"], [11, 101, 4, 58], [11, 108, 4, 58, "WeakMap"], [11, 115, 4, 58], [11, 127, 4, 58, "_interopRequireWildcard"], [11, 150, 4, 58], [11, 162, 4, 58, "_interopRequireWildcard"], [11, 163, 4, 58, "e"], [11, 164, 4, 58], [11, 166, 4, 58, "t"], [11, 167, 4, 58], [11, 176, 4, 58, "t"], [11, 177, 4, 58], [11, 181, 4, 58, "e"], [11, 182, 4, 58], [11, 186, 4, 58, "e"], [11, 187, 4, 58], [11, 188, 4, 58, "__esModule"], [11, 198, 4, 58], [11, 207, 4, 58, "e"], [11, 208, 4, 58], [11, 214, 4, 58, "o"], [11, 215, 4, 58], [11, 217, 4, 58, "i"], [11, 218, 4, 58], [11, 220, 4, 58, "f"], [11, 221, 4, 58], [11, 226, 4, 58, "__proto__"], [11, 235, 4, 58], [11, 243, 4, 58, "default"], [11, 250, 4, 58], [11, 252, 4, 58, "e"], [11, 253, 4, 58], [11, 270, 4, 58, "e"], [11, 271, 4, 58], [11, 294, 4, 58, "e"], [11, 295, 4, 58], [11, 320, 4, 58, "e"], [11, 321, 4, 58], [11, 330, 4, 58, "f"], [11, 331, 4, 58], [11, 337, 4, 58, "o"], [11, 338, 4, 58], [11, 341, 4, 58, "t"], [11, 342, 4, 58], [11, 345, 4, 58, "n"], [11, 346, 4, 58], [11, 349, 4, 58, "r"], [11, 350, 4, 58], [11, 358, 4, 58, "o"], [11, 359, 4, 58], [11, 360, 4, 58, "has"], [11, 363, 4, 58], [11, 364, 4, 58, "e"], [11, 365, 4, 58], [11, 375, 4, 58, "o"], [11, 376, 4, 58], [11, 377, 4, 58, "get"], [11, 380, 4, 58], [11, 381, 4, 58, "e"], [11, 382, 4, 58], [11, 385, 4, 58, "o"], [11, 386, 4, 58], [11, 387, 4, 58, "set"], [11, 390, 4, 58], [11, 391, 4, 58, "e"], [11, 392, 4, 58], [11, 394, 4, 58, "f"], [11, 395, 4, 58], [11, 411, 4, 58, "t"], [11, 412, 4, 58], [11, 416, 4, 58, "e"], [11, 417, 4, 58], [11, 433, 4, 58, "t"], [11, 434, 4, 58], [11, 441, 4, 58, "hasOwnProperty"], [11, 455, 4, 58], [11, 456, 4, 58, "call"], [11, 460, 4, 58], [11, 461, 4, 58, "e"], [11, 462, 4, 58], [11, 464, 4, 58, "t"], [11, 465, 4, 58], [11, 472, 4, 58, "i"], [11, 473, 4, 58], [11, 477, 4, 58, "o"], [11, 478, 4, 58], [11, 481, 4, 58, "Object"], [11, 487, 4, 58], [11, 488, 4, 58, "defineProperty"], [11, 502, 4, 58], [11, 507, 4, 58, "Object"], [11, 513, 4, 58], [11, 514, 4, 58, "getOwnPropertyDescriptor"], [11, 538, 4, 58], [11, 539, 4, 58, "e"], [11, 540, 4, 58], [11, 542, 4, 58, "t"], [11, 543, 4, 58], [11, 550, 4, 58, "i"], [11, 551, 4, 58], [11, 552, 4, 58, "get"], [11, 555, 4, 58], [11, 559, 4, 58, "i"], [11, 560, 4, 58], [11, 561, 4, 58, "set"], [11, 564, 4, 58], [11, 568, 4, 58, "o"], [11, 569, 4, 58], [11, 570, 4, 58, "f"], [11, 571, 4, 58], [11, 573, 4, 58, "t"], [11, 574, 4, 58], [11, 576, 4, 58, "i"], [11, 577, 4, 58], [11, 581, 4, 58, "f"], [11, 582, 4, 58], [11, 583, 4, 58, "t"], [11, 584, 4, 58], [11, 588, 4, 58, "e"], [11, 589, 4, 58], [11, 590, 4, 58, "t"], [11, 591, 4, 58], [11, 602, 4, 58, "f"], [11, 603, 4, 58], [11, 608, 4, 58, "e"], [11, 609, 4, 58], [11, 611, 4, 58, "t"], [11, 612, 4, 58], [12, 2, 6, 0], [12, 8, 6, 6, "NativeViewGestureHandler"], [12, 32, 6, 30], [12, 41, 6, 39, "PressGestureHandler"], [12, 69, 6, 58], [12, 70, 6, 59], [13, 4, 7, 2], [13, 8, 7, 6, "isNative"], [13, 16, 7, 14, "isNative"], [13, 17, 7, 14], [13, 19, 7, 17], [14, 6, 8, 4], [14, 13, 8, 11], [14, 17, 8, 15], [15, 4, 9, 2], [16, 4, 11, 2, "onRawEvent"], [16, 14, 11, 12, "onRawEvent"], [16, 15, 11, 13, "ev"], [16, 17, 11, 15], [16, 19, 11, 17], [17, 6, 12, 4], [17, 11, 12, 9], [17, 12, 12, 10, "onRawEvent"], [17, 22, 12, 20], [17, 23, 12, 21, "ev"], [17, 25, 12, 23], [17, 26, 12, 24], [18, 6, 14, 4], [18, 10, 14, 8], [18, 11, 14, 9, "ev"], [18, 13, 14, 11], [18, 14, 14, 12, "isFinal"], [18, 21, 14, 19], [18, 23, 14, 21], [19, 8, 15, 6], [20, 8, 16, 6], [20, 12, 16, 10], [20, 16, 16, 10, "TEST_MIN_IF_NOT_NAN"], [20, 42, 16, 29], [20, 44, 16, 30], [20, 48, 16, 30, "VEC_LEN_SQ"], [20, 65, 16, 40], [20, 67, 16, 41], [21, 10, 17, 8, "x"], [21, 11, 17, 9], [21, 13, 17, 11, "ev"], [21, 15, 17, 13], [21, 16, 17, 14, "deltaX"], [21, 22, 17, 20], [22, 10, 18, 8, "y"], [22, 11, 18, 9], [22, 13, 18, 11, "ev"], [22, 15, 18, 13], [22, 16, 18, 14, "deltaY"], [23, 8, 19, 6], [23, 9, 19, 7], [23, 10, 19, 8], [23, 12, 19, 10], [23, 14, 19, 12], [23, 15, 19, 13], [23, 17, 19, 15], [24, 10, 20, 8], [25, 10, 21, 8], [25, 14, 21, 12], [25, 18, 21, 16], [25, 19, 21, 17, "config"], [25, 25, 21, 23], [25, 26, 21, 24, "disallowInterruption"], [25, 46, 21, 44], [25, 48, 21, 46], [26, 12, 22, 10], [26, 18, 22, 16, "gestures"], [26, 26, 22, 24], [26, 29, 22, 27, "Object"], [26, 35, 22, 33], [26, 36, 22, 34, "values"], [26, 42, 22, 40], [26, 43, 22, 41, "NodeManager"], [26, 54, 22, 52], [26, 55, 22, 53, "getNodes"], [26, 63, 22, 61], [26, 64, 22, 62], [26, 65, 22, 63], [26, 66, 22, 64], [26, 67, 22, 65, "filter"], [26, 73, 22, 71], [26, 74, 22, 72, "gesture"], [26, 81, 22, 79], [26, 85, 22, 83], [27, 14, 23, 12], [27, 20, 23, 18], [28, 16, 24, 14, "handlerTag"], [28, 26, 24, 24], [29, 16, 25, 14, "view"], [29, 20, 25, 18], [30, 16, 26, 14, "isGestureRunning"], [31, 14, 27, 12], [31, 15, 27, 13], [31, 18, 27, 16, "gesture"], [31, 25, 27, 23], [32, 14, 28, 12], [33, 16, 28, 20], [34, 16, 29, 14, "handlerTag"], [34, 26, 29, 24], [34, 31, 29, 29], [34, 35, 29, 33], [34, 36, 29, 34, "handlerTag"], [34, 46, 29, 44], [35, 16, 29, 48], [36, 16, 30, 14, "isGestureRunning"], [36, 32, 30, 30], [37, 16, 30, 34], [38, 16, 31, 14, "gesture"], [38, 23, 31, 21], [38, 35, 31, 33, "DiscreteGestureHandler"], [38, 66, 31, 55], [39, 16, 31, 59], [40, 16, 32, 14, "view"], [40, 20, 32, 18], [41, 16, 32, 22], [42, 16, 33, 14], [42, 20, 33, 18], [42, 21, 33, 19, "view"], [42, 25, 33, 23], [42, 26, 33, 24, "contains"], [42, 34, 33, 32], [42, 35, 33, 33, "view"], [42, 39, 33, 37], [43, 14, 33, 38], [44, 12, 35, 10], [44, 13, 35, 11], [44, 14, 35, 12], [44, 15, 35, 13], [44, 16, 35, 14], [46, 12, 37, 10], [46, 17, 37, 15], [46, 23, 37, 21, "gesture"], [46, 30, 37, 28], [46, 34, 37, 32, "gestures"], [46, 42, 37, 40], [46, 44, 37, 42], [47, 14, 38, 12], [48, 14, 39, 12, "gesture"], [48, 21, 39, 19], [48, 22, 39, 20, "forceInvalidate"], [48, 37, 39, 35], [48, 38, 39, 36, "ev"], [48, 40, 39, 38], [48, 41, 39, 39], [49, 12, 40, 10], [50, 10, 41, 8], [51, 8, 42, 6], [52, 6, 43, 4], [53, 4, 44, 2], [54, 2, 46, 0], [55, 2, 46, 1], [55, 6, 46, 1, "_default"], [55, 14, 46, 1], [55, 17, 46, 1, "exports"], [55, 24, 46, 1], [55, 25, 46, 1, "default"], [55, 32, 46, 1], [55, 35, 48, 15, "NativeViewGestureHandler"], [55, 59, 48, 39], [56, 0, 48, 39], [56, 3]], "functionMap": {"names": ["<global>", "NativeViewGestureHandler", "get__isNative", "onRawEvent", "Object.values.filter$argument_0"], "mappings": "AAA;ACK;ECC;GDE;EEE;wECW;WDa;GFS;CDE"}}, "type": "js/module"}]}