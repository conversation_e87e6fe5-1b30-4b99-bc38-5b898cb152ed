{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../src/private/specs_DEPRECATED/modules/NativeAppearance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 91}}], "key": "B/NZI1g/erGIIAxS0DJjlb86/s4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeAppearance = _interopRequireDefault(require(_dependencyMap[1], \"../../src/private/specs_DEPRECATED/modules/NativeAppearance\"));\n  var _default = exports.default = _NativeAppearance.default;\n});", "lineCount": 9, "map": [[7, 2, 12, 0], [7, 6, 12, 0, "_NativeAppearance"], [7, 23, 12, 0], [7, 26, 12, 0, "_interopRequireDefault"], [7, 48, 12, 0], [7, 49, 12, 0, "require"], [7, 56, 12, 0], [7, 57, 12, 0, "_dependencyMap"], [7, 71, 12, 0], [8, 2, 12, 91], [8, 6, 12, 91, "_default"], [8, 14, 12, 91], [8, 17, 12, 91, "exports"], [8, 24, 12, 91], [8, 25, 12, 91, "default"], [8, 32, 12, 91], [8, 35, 13, 15, "NativeAppearance"], [8, 60, 13, 31], [9, 0, 13, 31], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}