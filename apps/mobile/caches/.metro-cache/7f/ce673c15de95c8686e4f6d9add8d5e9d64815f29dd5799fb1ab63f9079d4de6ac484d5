{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const MessageCircleReply = exports.default = (0, _createLucideIcon.default)(\"MessageCircleReply\", [[\"path\", {\n    d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\",\n    key: \"vv11sd\"\n  }], [\"path\", {\n    d: \"m10 15-3-3 3-3\",\n    key: \"1pgupc\"\n  }], [\"path\", {\n    d: \"M7 12h7a2 2 0 0 1 2 2v1\",\n    key: \"1gheu4\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "MessageCircleReply"], [15, 26, 10, 24], [15, 29, 10, 24, "exports"], [15, 36, 10, 24], [15, 37, 10, 24, "default"], [15, 44, 10, 24], [15, 47, 10, 27], [15, 51, 10, 27, "createLucideIcon"], [15, 76, 10, 43], [15, 78, 10, 44], [15, 98, 10, 64], [15, 100, 10, 66], [15, 101, 11, 2], [15, 102, 11, 3], [15, 108, 11, 9], [15, 110, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 39, 11, 48], [17, 4, 11, 50, "key"], [17, 7, 11, 53], [17, 9, 11, 55], [18, 2, 11, 64], [18, 3, 11, 65], [18, 4, 11, 66], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 23, 12, 32], [20, 4, 12, 34, "key"], [20, 7, 12, 37], [20, 9, 12, 39], [21, 2, 12, 48], [21, 3, 12, 49], [21, 4, 12, 50], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 32, 13, 41], [23, 4, 13, 43, "key"], [23, 7, 13, 46], [23, 9, 13, 48], [24, 2, 13, 57], [24, 3, 13, 58], [24, 4, 13, 59], [24, 5, 14, 1], [24, 6, 14, 2], [25, 0, 14, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}