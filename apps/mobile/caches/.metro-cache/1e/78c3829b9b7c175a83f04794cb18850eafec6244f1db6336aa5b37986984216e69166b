{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 63, "index": 135}}], "key": "uyrN8K1sxp8o5ztOrm1s7TT4qf8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 136}, "end": {"line": 4, "column": 56, "index": 192}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 438}, "end": {"line": 15, "column": 53, "index": 491}}], "key": "5viveWF5O/AXsjQDU5X7yyaGrUk=", "exportNames": ["*"]}}, {"name": "../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 492}, "end": {"line": 16, "column": 52, "index": 544}}], "key": "80gGlYetrHmVdqDZvFU6/ojATpA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 545}, "end": {"line": 17, "column": 28, "index": 573}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "./TSpan", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 574}, "end": {"line": 18, "column": 28, "index": 602}}], "key": "M8orbKyGJ5/skPPvrLCJlDjfIGQ=", "exportNames": ["*"]}}, {"name": "../fabric/TextPathNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 603}, "end": {"line": 19, "column": 62, "index": 665}}], "key": "aJAsIJnbeiSA42+O01f/U+b+gJE=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[8], \"../lib/extract/extractTransform\"));\n  var _extractProps = require(_dependencyMap[9], \"../lib/extract/extractProps\");\n  var _extractText = _interopRequireDefault(require(_dependencyMap[10], \"../lib/extract/extractText\"));\n  var _util = require(_dependencyMap[11], \"../lib/util\");\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[12], \"./Shape\"));\n  var _TSpan = _interopRequireDefault(require(_dependencyMap[13], \"./TSpan\"));\n  var _TextPathNativeComponent = _interopRequireDefault(require(_dependencyMap[14], \"../fabric/TextPathNativeComponent\"));\n  var _jsxDevRuntime = require(_dependencyMap[15], \"react/jsx-dev-runtime\");\n  var _excluded = [\"children\", \"xlinkHref\", \"href\", \"startOffset\", \"method\", \"spacing\", \"side\", \"alignmentBaseline\", \"midLine\"];\n  var _jsxFileName = \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-svg/src/elements/TextPath.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var TextPath = exports.default = /*#__PURE__*/function (_Shape) {\n    function TextPath() {\n      var _this;\n      (0, _classCallCheck2.default)(this, TextPath);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, TextPath, [...args]);\n      _this.setNativeProps = props => {\n        var matrix = !props.matrix && (0, _extractTransform.default)(props);\n        if (matrix) {\n          props.matrix = matrix;\n        }\n        Object.assign(props, (0, _util.pickNotNil)((0, _extractText.default)(props, true)));\n        _this.root && _this.root.setNativeProps(props);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(TextPath, _Shape);\n    return (0, _createClass2.default)(TextPath, [{\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          children = _this$props.children,\n          xlinkHref = _this$props.xlinkHref,\n          _this$props$href = _this$props.href,\n          href = _this$props$href === void 0 ? xlinkHref : _this$props$href,\n          _this$props$startOffs = _this$props.startOffset,\n          startOffset = _this$props$startOffs === void 0 ? 0 : _this$props$startOffs,\n          method = _this$props.method,\n          spacing = _this$props.spacing,\n          side = _this$props.side,\n          alignmentBaseline = _this$props.alignmentBaseline,\n          midLine = _this$props.midLine,\n          prop = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n        var matched = href && href.match(_util.idPattern);\n        var match = matched && matched[1];\n        if (match) {\n          var props = (0, _extractProps.withoutXY)(this, prop);\n          Object.assign(props, (0, _extractText.default)({\n            children\n          }, true), {\n            href: match,\n            startOffset,\n            method,\n            spacing,\n            side,\n            alignmentBaseline,\n            midLine\n          });\n          props.ref = this.refMethod;\n          return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TextPathNativeComponent.default, {\n            ...props\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 14\n          }, this);\n        }\n        console.warn('Invalid `href` prop for `TextPath` element, expected a href like \"#id\", but got: \"' + href + '\"');\n        return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TSpan.default, {\n          ref: this.refMethod,\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 7\n        }, this);\n      }\n    }]);\n  }(_Shape2.default);\n  TextPath.displayName = 'TextPath';\n});", "lineCount": 99, "map": [[13, 2, 2, 0], [13, 6, 2, 0, "React"], [13, 11, 2, 0], [13, 14, 2, 0, "_interopRequireWildcard"], [13, 37, 2, 0], [13, 38, 2, 0, "require"], [13, 45, 2, 0], [13, 46, 2, 0, "_dependencyMap"], [13, 60, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_extractTransform"], [14, 23, 3, 0], [14, 26, 3, 0, "_interopRequireDefault"], [14, 48, 3, 0], [14, 49, 3, 0, "require"], [14, 56, 3, 0], [14, 57, 3, 0, "_dependencyMap"], [14, 71, 3, 0], [15, 2, 4, 0], [15, 6, 4, 0, "_extractProps"], [15, 19, 4, 0], [15, 22, 4, 0, "require"], [15, 29, 4, 0], [15, 30, 4, 0, "_dependencyMap"], [15, 44, 4, 0], [16, 2, 15, 0], [16, 6, 15, 0, "_extractText"], [16, 18, 15, 0], [16, 21, 15, 0, "_interopRequireDefault"], [16, 43, 15, 0], [16, 44, 15, 0, "require"], [16, 51, 15, 0], [16, 52, 15, 0, "_dependencyMap"], [16, 66, 15, 0], [17, 2, 16, 0], [17, 6, 16, 0, "_util"], [17, 11, 16, 0], [17, 14, 16, 0, "require"], [17, 21, 16, 0], [17, 22, 16, 0, "_dependencyMap"], [17, 36, 16, 0], [18, 2, 17, 0], [18, 6, 17, 0, "_Shape2"], [18, 13, 17, 0], [18, 16, 17, 0, "_interopRequireDefault"], [18, 38, 17, 0], [18, 39, 17, 0, "require"], [18, 46, 17, 0], [18, 47, 17, 0, "_dependencyMap"], [18, 61, 17, 0], [19, 2, 18, 0], [19, 6, 18, 0, "_TSpan"], [19, 12, 18, 0], [19, 15, 18, 0, "_interopRequireDefault"], [19, 37, 18, 0], [19, 38, 18, 0, "require"], [19, 45, 18, 0], [19, 46, 18, 0, "_dependencyMap"], [19, 60, 18, 0], [20, 2, 19, 0], [20, 6, 19, 0, "_TextPathNativeComponent"], [20, 30, 19, 0], [20, 33, 19, 0, "_interopRequireDefault"], [20, 55, 19, 0], [20, 56, 19, 0, "require"], [20, 63, 19, 0], [20, 64, 19, 0, "_dependencyMap"], [20, 78, 19, 0], [21, 2, 19, 62], [21, 6, 19, 62, "_jsxDevRuntime"], [21, 20, 19, 62], [21, 23, 19, 62, "require"], [21, 30, 19, 62], [21, 31, 19, 62, "_dependencyMap"], [21, 45, 19, 62], [22, 2, 19, 62], [22, 6, 19, 62, "_excluded"], [22, 15, 19, 62], [23, 2, 19, 62], [23, 6, 19, 62, "_jsxFileName"], [23, 18, 19, 62], [24, 2, 19, 62], [24, 11, 19, 62, "_interopRequireWildcard"], [24, 35, 19, 62, "e"], [24, 36, 19, 62], [24, 38, 19, 62, "t"], [24, 39, 19, 62], [24, 68, 19, 62, "WeakMap"], [24, 75, 19, 62], [24, 81, 19, 62, "r"], [24, 82, 19, 62], [24, 89, 19, 62, "WeakMap"], [24, 96, 19, 62], [24, 100, 19, 62, "n"], [24, 101, 19, 62], [24, 108, 19, 62, "WeakMap"], [24, 115, 19, 62], [24, 127, 19, 62, "_interopRequireWildcard"], [24, 150, 19, 62], [24, 162, 19, 62, "_interopRequireWildcard"], [24, 163, 19, 62, "e"], [24, 164, 19, 62], [24, 166, 19, 62, "t"], [24, 167, 19, 62], [24, 176, 19, 62, "t"], [24, 177, 19, 62], [24, 181, 19, 62, "e"], [24, 182, 19, 62], [24, 186, 19, 62, "e"], [24, 187, 19, 62], [24, 188, 19, 62, "__esModule"], [24, 198, 19, 62], [24, 207, 19, 62, "e"], [24, 208, 19, 62], [24, 214, 19, 62, "o"], [24, 215, 19, 62], [24, 217, 19, 62, "i"], [24, 218, 19, 62], [24, 220, 19, 62, "f"], [24, 221, 19, 62], [24, 226, 19, 62, "__proto__"], [24, 235, 19, 62], [24, 243, 19, 62, "default"], [24, 250, 19, 62], [24, 252, 19, 62, "e"], [24, 253, 19, 62], [24, 270, 19, 62, "e"], [24, 271, 19, 62], [24, 294, 19, 62, "e"], [24, 295, 19, 62], [24, 320, 19, 62, "e"], [24, 321, 19, 62], [24, 330, 19, 62, "f"], [24, 331, 19, 62], [24, 337, 19, 62, "o"], [24, 338, 19, 62], [24, 341, 19, 62, "t"], [24, 342, 19, 62], [24, 345, 19, 62, "n"], [24, 346, 19, 62], [24, 349, 19, 62, "r"], [24, 350, 19, 62], [24, 358, 19, 62, "o"], [24, 359, 19, 62], [24, 360, 19, 62, "has"], [24, 363, 19, 62], [24, 364, 19, 62, "e"], [24, 365, 19, 62], [24, 375, 19, 62, "o"], [24, 376, 19, 62], [24, 377, 19, 62, "get"], [24, 380, 19, 62], [24, 381, 19, 62, "e"], [24, 382, 19, 62], [24, 385, 19, 62, "o"], [24, 386, 19, 62], [24, 387, 19, 62, "set"], [24, 390, 19, 62], [24, 391, 19, 62, "e"], [24, 392, 19, 62], [24, 394, 19, 62, "f"], [24, 395, 19, 62], [24, 409, 19, 62, "_t"], [24, 411, 19, 62], [24, 415, 19, 62, "e"], [24, 416, 19, 62], [24, 432, 19, 62, "_t"], [24, 434, 19, 62], [24, 441, 19, 62, "hasOwnProperty"], [24, 455, 19, 62], [24, 456, 19, 62, "call"], [24, 460, 19, 62], [24, 461, 19, 62, "e"], [24, 462, 19, 62], [24, 464, 19, 62, "_t"], [24, 466, 19, 62], [24, 473, 19, 62, "i"], [24, 474, 19, 62], [24, 478, 19, 62, "o"], [24, 479, 19, 62], [24, 482, 19, 62, "Object"], [24, 488, 19, 62], [24, 489, 19, 62, "defineProperty"], [24, 503, 19, 62], [24, 508, 19, 62, "Object"], [24, 514, 19, 62], [24, 515, 19, 62, "getOwnPropertyDescriptor"], [24, 539, 19, 62], [24, 540, 19, 62, "e"], [24, 541, 19, 62], [24, 543, 19, 62, "_t"], [24, 545, 19, 62], [24, 552, 19, 62, "i"], [24, 553, 19, 62], [24, 554, 19, 62, "get"], [24, 557, 19, 62], [24, 561, 19, 62, "i"], [24, 562, 19, 62], [24, 563, 19, 62, "set"], [24, 566, 19, 62], [24, 570, 19, 62, "o"], [24, 571, 19, 62], [24, 572, 19, 62, "f"], [24, 573, 19, 62], [24, 575, 19, 62, "_t"], [24, 577, 19, 62], [24, 579, 19, 62, "i"], [24, 580, 19, 62], [24, 584, 19, 62, "f"], [24, 585, 19, 62], [24, 586, 19, 62, "_t"], [24, 588, 19, 62], [24, 592, 19, 62, "e"], [24, 593, 19, 62], [24, 594, 19, 62, "_t"], [24, 596, 19, 62], [24, 607, 19, 62, "f"], [24, 608, 19, 62], [24, 613, 19, 62, "e"], [24, 614, 19, 62], [24, 616, 19, 62, "t"], [24, 617, 19, 62], [25, 2, 19, 62], [25, 11, 19, 62, "_callSuper"], [25, 22, 19, 62, "t"], [25, 23, 19, 62], [25, 25, 19, 62, "o"], [25, 26, 19, 62], [25, 28, 19, 62, "e"], [25, 29, 19, 62], [25, 40, 19, 62, "o"], [25, 41, 19, 62], [25, 48, 19, 62, "_getPrototypeOf2"], [25, 64, 19, 62], [25, 65, 19, 62, "default"], [25, 72, 19, 62], [25, 74, 19, 62, "o"], [25, 75, 19, 62], [25, 82, 19, 62, "_possibleConstructorReturn2"], [25, 109, 19, 62], [25, 110, 19, 62, "default"], [25, 117, 19, 62], [25, 119, 19, 62, "t"], [25, 120, 19, 62], [25, 122, 19, 62, "_isNativeReflectConstruct"], [25, 147, 19, 62], [25, 152, 19, 62, "Reflect"], [25, 159, 19, 62], [25, 160, 19, 62, "construct"], [25, 169, 19, 62], [25, 170, 19, 62, "o"], [25, 171, 19, 62], [25, 173, 19, 62, "e"], [25, 174, 19, 62], [25, 186, 19, 62, "_getPrototypeOf2"], [25, 202, 19, 62], [25, 203, 19, 62, "default"], [25, 210, 19, 62], [25, 212, 19, 62, "t"], [25, 213, 19, 62], [25, 215, 19, 62, "constructor"], [25, 226, 19, 62], [25, 230, 19, 62, "o"], [25, 231, 19, 62], [25, 232, 19, 62, "apply"], [25, 237, 19, 62], [25, 238, 19, 62, "t"], [25, 239, 19, 62], [25, 241, 19, 62, "e"], [25, 242, 19, 62], [26, 2, 19, 62], [26, 11, 19, 62, "_isNativeReflectConstruct"], [26, 37, 19, 62], [26, 51, 19, 62, "t"], [26, 52, 19, 62], [26, 56, 19, 62, "Boolean"], [26, 63, 19, 62], [26, 64, 19, 62, "prototype"], [26, 73, 19, 62], [26, 74, 19, 62, "valueOf"], [26, 81, 19, 62], [26, 82, 19, 62, "call"], [26, 86, 19, 62], [26, 87, 19, 62, "Reflect"], [26, 94, 19, 62], [26, 95, 19, 62, "construct"], [26, 104, 19, 62], [26, 105, 19, 62, "Boolean"], [26, 112, 19, 62], [26, 145, 19, 62, "t"], [26, 146, 19, 62], [26, 159, 19, 62, "_isNativeReflectConstruct"], [26, 184, 19, 62], [26, 196, 19, 62, "_isNativeReflectConstruct"], [26, 197, 19, 62], [26, 210, 19, 62, "t"], [26, 211, 19, 62], [27, 2, 19, 62], [27, 6, 32, 21, "TextPath"], [27, 14, 32, 29], [27, 17, 32, 29, "exports"], [27, 24, 32, 29], [27, 25, 32, 29, "default"], [27, 32, 32, 29], [27, 58, 32, 29, "_Shape"], [27, 64, 32, 29], [28, 4, 32, 29], [28, 13, 32, 29, "TextPath"], [28, 22, 32, 29], [29, 6, 32, 29], [29, 10, 32, 29, "_this"], [29, 15, 32, 29], [30, 6, 32, 29], [30, 10, 32, 29, "_classCallCheck2"], [30, 26, 32, 29], [30, 27, 32, 29, "default"], [30, 34, 32, 29], [30, 42, 32, 29, "TextPath"], [30, 50, 32, 29], [31, 6, 32, 29], [31, 15, 32, 29, "_len"], [31, 19, 32, 29], [31, 22, 32, 29, "arguments"], [31, 31, 32, 29], [31, 32, 32, 29, "length"], [31, 38, 32, 29], [31, 40, 32, 29, "args"], [31, 44, 32, 29], [31, 51, 32, 29, "Array"], [31, 56, 32, 29], [31, 57, 32, 29, "_len"], [31, 61, 32, 29], [31, 64, 32, 29, "_key"], [31, 68, 32, 29], [31, 74, 32, 29, "_key"], [31, 78, 32, 29], [31, 81, 32, 29, "_len"], [31, 85, 32, 29], [31, 87, 32, 29, "_key"], [31, 91, 32, 29], [32, 8, 32, 29, "args"], [32, 12, 32, 29], [32, 13, 32, 29, "_key"], [32, 17, 32, 29], [32, 21, 32, 29, "arguments"], [32, 30, 32, 29], [32, 31, 32, 29, "_key"], [32, 35, 32, 29], [33, 6, 32, 29], [34, 6, 32, 29, "_this"], [34, 11, 32, 29], [34, 14, 32, 29, "_callSuper"], [34, 24, 32, 29], [34, 31, 32, 29, "TextPath"], [34, 39, 32, 29], [34, 45, 32, 29, "args"], [34, 49, 32, 29], [35, 6, 32, 29, "_this"], [35, 11, 32, 29], [35, 12, 35, 2, "setNativeProps"], [35, 26, 35, 16], [35, 29, 36, 4, "props"], [35, 34, 39, 22], [35, 38, 40, 7], [36, 8, 41, 4], [36, 12, 41, 10, "matrix"], [36, 18, 41, 16], [36, 21, 41, 19], [36, 22, 41, 20, "props"], [36, 27, 41, 25], [36, 28, 41, 26, "matrix"], [36, 34, 41, 32], [36, 38, 41, 36], [36, 42, 41, 36, "extractTransform"], [36, 67, 41, 52], [36, 69, 41, 53, "props"], [36, 74, 41, 58], [36, 75, 41, 59], [37, 8, 42, 4], [37, 12, 42, 8, "matrix"], [37, 18, 42, 14], [37, 20, 42, 16], [38, 10, 43, 6, "props"], [38, 15, 43, 11], [38, 16, 43, 12, "matrix"], [38, 22, 43, 18], [38, 25, 43, 21, "matrix"], [38, 31, 43, 27], [39, 8, 44, 4], [40, 8, 45, 4, "Object"], [40, 14, 45, 10], [40, 15, 45, 11, "assign"], [40, 21, 45, 17], [40, 22, 45, 18, "props"], [40, 27, 45, 23], [40, 29, 45, 25], [40, 33, 45, 25, "pickNotNil"], [40, 49, 45, 35], [40, 51, 45, 36], [40, 55, 45, 36, "extractText"], [40, 75, 45, 47], [40, 77, 45, 48, "props"], [40, 82, 45, 53], [40, 84, 45, 55], [40, 88, 45, 59], [40, 89, 45, 60], [40, 90, 45, 61], [40, 91, 45, 62], [41, 8, 46, 4, "_this"], [41, 13, 46, 4], [41, 14, 46, 9, "root"], [41, 18, 46, 13], [41, 22, 46, 17, "_this"], [41, 27, 46, 17], [41, 28, 46, 22, "root"], [41, 32, 46, 26], [41, 33, 46, 27, "setNativeProps"], [41, 47, 46, 41], [41, 48, 46, 42, "props"], [41, 53, 46, 47], [41, 54, 46, 48], [42, 6, 47, 2], [42, 7, 47, 3], [43, 6, 47, 3], [43, 13, 47, 3, "_this"], [43, 18, 47, 3], [44, 4, 47, 3], [45, 4, 47, 3], [45, 8, 47, 3, "_inherits2"], [45, 18, 47, 3], [45, 19, 47, 3, "default"], [45, 26, 47, 3], [45, 28, 47, 3, "TextPath"], [45, 36, 47, 3], [45, 38, 47, 3, "_Shape"], [45, 44, 47, 3], [46, 4, 47, 3], [46, 15, 47, 3, "_createClass2"], [46, 28, 47, 3], [46, 29, 47, 3, "default"], [46, 36, 47, 3], [46, 38, 47, 3, "TextPath"], [46, 46, 47, 3], [47, 6, 47, 3, "key"], [47, 9, 47, 3], [48, 6, 47, 3, "value"], [48, 11, 47, 3], [48, 13, 49, 2], [48, 22, 49, 2, "render"], [48, 28, 49, 8, "render"], [48, 29, 49, 8], [48, 31, 49, 11], [49, 8, 50, 4], [49, 12, 50, 4, "_this$props"], [49, 23, 50, 4], [49, 26, 61, 8], [49, 30, 61, 12], [49, 31, 61, 13, "props"], [49, 36, 61, 18], [50, 10, 51, 6, "children"], [50, 18, 51, 14], [50, 21, 51, 14, "_this$props"], [50, 32, 51, 14], [50, 33, 51, 6, "children"], [50, 41, 51, 14], [51, 10, 52, 6, "xlinkHref"], [51, 19, 52, 15], [51, 22, 52, 15, "_this$props"], [51, 33, 52, 15], [51, 34, 52, 6, "xlinkHref"], [51, 43, 52, 15], [52, 10, 52, 15, "_this$props$href"], [52, 26, 52, 15], [52, 29, 52, 15, "_this$props"], [52, 40, 52, 15], [52, 41, 53, 6, "href"], [52, 45, 53, 10], [53, 10, 53, 6, "href"], [53, 14, 53, 10], [53, 17, 53, 10, "_this$props$href"], [53, 33, 53, 10], [53, 47, 53, 13, "xlinkHref"], [53, 56, 53, 22], [53, 59, 53, 22, "_this$props$href"], [53, 75, 53, 22], [54, 10, 53, 22, "_this$props$startOffs"], [54, 31, 53, 22], [54, 34, 53, 22, "_this$props"], [54, 45, 53, 22], [54, 46, 54, 6, "startOffset"], [54, 57, 54, 17], [55, 10, 54, 6, "startOffset"], [55, 21, 54, 17], [55, 24, 54, 17, "_this$props$startOffs"], [55, 45, 54, 17], [55, 59, 54, 20], [55, 60, 54, 21], [55, 63, 54, 21, "_this$props$startOffs"], [55, 84, 54, 21], [56, 10, 55, 6, "method"], [56, 16, 55, 12], [56, 19, 55, 12, "_this$props"], [56, 30, 55, 12], [56, 31, 55, 6, "method"], [56, 37, 55, 12], [57, 10, 56, 6, "spacing"], [57, 17, 56, 13], [57, 20, 56, 13, "_this$props"], [57, 31, 56, 13], [57, 32, 56, 6, "spacing"], [57, 39, 56, 13], [58, 10, 57, 6, "side"], [58, 14, 57, 10], [58, 17, 57, 10, "_this$props"], [58, 28, 57, 10], [58, 29, 57, 6, "side"], [58, 33, 57, 10], [59, 10, 58, 6, "alignmentBaseline"], [59, 27, 58, 23], [59, 30, 58, 23, "_this$props"], [59, 41, 58, 23], [59, 42, 58, 6, "alignmentBaseline"], [59, 59, 58, 23], [60, 10, 59, 6, "midLine"], [60, 17, 59, 13], [60, 20, 59, 13, "_this$props"], [60, 31, 59, 13], [60, 32, 59, 6, "midLine"], [60, 39, 59, 13], [61, 10, 60, 9, "prop"], [61, 14, 60, 13], [61, 21, 60, 13, "_objectWithoutProperties2"], [61, 46, 60, 13], [61, 47, 60, 13, "default"], [61, 54, 60, 13], [61, 56, 60, 13, "_this$props"], [61, 67, 60, 13], [61, 69, 60, 13, "_excluded"], [61, 78, 60, 13], [62, 8, 62, 4], [62, 12, 62, 10, "matched"], [62, 19, 62, 17], [62, 22, 62, 20, "href"], [62, 26, 62, 24], [62, 30, 62, 28, "href"], [62, 34, 62, 32], [62, 35, 62, 33, "match"], [62, 40, 62, 38], [62, 41, 62, 39, "idPattern"], [62, 56, 62, 48], [62, 57, 62, 49], [63, 8, 63, 4], [63, 12, 63, 10, "match"], [63, 17, 63, 15], [63, 20, 63, 18, "matched"], [63, 27, 63, 25], [63, 31, 63, 29, "matched"], [63, 38, 63, 36], [63, 39, 63, 37], [63, 40, 63, 38], [63, 41, 63, 39], [64, 8, 64, 4], [64, 12, 64, 8, "match"], [64, 17, 64, 13], [64, 19, 64, 15], [65, 10, 65, 6], [65, 14, 65, 12, "props"], [65, 19, 65, 17], [65, 22, 65, 20], [65, 26, 65, 20, "withoutXY"], [65, 49, 65, 29], [65, 51, 65, 30], [65, 55, 65, 34], [65, 57, 65, 36, "prop"], [65, 61, 65, 40], [65, 62, 65, 41], [66, 10, 66, 6, "Object"], [66, 16, 66, 12], [66, 17, 66, 13, "assign"], [66, 23, 66, 19], [66, 24, 67, 8, "props"], [66, 29, 67, 13], [66, 31, 68, 8], [66, 35, 68, 8, "extractText"], [66, 55, 68, 19], [66, 57, 69, 10], [67, 12, 70, 12, "children"], [68, 10, 71, 10], [68, 11, 71, 11], [68, 13, 72, 10], [68, 17, 73, 8], [68, 18, 73, 9], [68, 20, 74, 8], [69, 12, 75, 10, "href"], [69, 16, 75, 14], [69, 18, 75, 16, "match"], [69, 23, 75, 21], [70, 12, 76, 10, "startOffset"], [70, 23, 76, 21], [71, 12, 77, 10, "method"], [71, 18, 77, 16], [72, 12, 78, 10, "spacing"], [72, 19, 78, 17], [73, 12, 79, 10, "side"], [73, 16, 79, 14], [74, 12, 80, 10, "alignmentBaseline"], [74, 29, 80, 27], [75, 12, 81, 10, "midLine"], [76, 10, 82, 8], [76, 11, 83, 6], [76, 12, 83, 7], [77, 10, 84, 6, "props"], [77, 15, 84, 11], [77, 16, 84, 12, "ref"], [77, 19, 84, 15], [77, 22, 84, 18], [77, 26, 84, 22], [77, 27, 84, 23, "refMethod"], [77, 36, 84, 72], [78, 10, 85, 6], [78, 30, 85, 13], [78, 34, 85, 13, "_jsxDevRuntime"], [78, 48, 85, 13], [78, 49, 85, 13, "jsxDEV"], [78, 55, 85, 13], [78, 57, 85, 14, "_TextPathNativeComponent"], [78, 81, 85, 14], [78, 82, 85, 14, "default"], [78, 89, 85, 27], [79, 12, 85, 27], [79, 15, 85, 32, "props"], [80, 10, 85, 37], [81, 12, 85, 37, "fileName"], [81, 20, 85, 37], [81, 22, 85, 37, "_jsxFileName"], [81, 34, 85, 37], [82, 12, 85, 37, "lineNumber"], [82, 22, 85, 37], [83, 12, 85, 37, "columnNumber"], [83, 24, 85, 37], [84, 10, 85, 37], [84, 17, 85, 40], [84, 18, 85, 41], [85, 8, 86, 4], [86, 8, 88, 4, "console"], [86, 15, 88, 11], [86, 16, 88, 12, "warn"], [86, 20, 88, 16], [86, 21, 89, 6], [86, 105, 89, 90], [86, 108, 90, 8, "href"], [86, 112, 90, 12], [86, 115, 91, 8], [86, 118, 92, 4], [86, 119, 92, 5], [87, 8, 93, 4], [87, 28, 94, 6], [87, 32, 94, 6, "_jsxDevRuntime"], [87, 46, 94, 6], [87, 47, 94, 6, "jsxDEV"], [87, 53, 94, 6], [87, 55, 94, 7, "_TSpan"], [87, 61, 94, 7], [87, 62, 94, 7, "default"], [87, 69, 94, 12], [88, 10, 94, 13, "ref"], [88, 13, 94, 16], [88, 15, 94, 18], [88, 19, 94, 22], [88, 20, 94, 23, "refMethod"], [88, 29, 94, 73], [89, 10, 94, 73, "children"], [89, 18, 94, 73], [89, 20, 95, 9, "children"], [90, 8, 95, 17], [91, 10, 95, 17, "fileName"], [91, 18, 95, 17], [91, 20, 95, 17, "_jsxFileName"], [91, 32, 95, 17], [92, 10, 95, 17, "lineNumber"], [92, 20, 95, 17], [93, 10, 95, 17, "columnNumber"], [93, 22, 95, 17], [94, 8, 95, 17], [94, 15, 96, 13], [94, 16, 96, 14], [95, 6, 98, 2], [96, 4, 98, 3], [97, 2, 98, 3], [97, 4, 32, 38, "<PERSON><PERSON><PERSON>"], [97, 19, 32, 43], [98, 2, 32, 21, "TextPath"], [98, 10, 32, 29], [98, 11, 33, 9, "displayName"], [98, 22, 33, 20], [98, 25, 33, 23], [98, 35, 33, 33], [99, 0, 33, 33], [99, 3]], "functionMap": {"names": ["<global>", "TextPath", "setNativeProps", "render"], "mappings": "AAA;eC+B;mBCG;GDY;EEE;GFiD;CDC"}}, "type": "js/module"}]}