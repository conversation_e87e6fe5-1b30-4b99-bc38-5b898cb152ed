{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const PanelBottom = exports.default = (0, _createLucideIcon.default)(\"PanelBottom\", [[\"rect\", {\n    width: \"18\",\n    height: \"18\",\n    x: \"3\",\n    y: \"3\",\n    rx: \"2\",\n    key: \"afitv7\"\n  }], [\"path\", {\n    d: \"M3 15h18\",\n    key: \"5xshup\"\n  }]]);\n});", "lineCount": 26, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 8, 10, 6, "PanelBottom"], [15, 19, 10, 17], [15, 22, 10, 17, "exports"], [15, 29, 10, 17], [15, 30, 10, 17, "default"], [15, 37, 10, 17], [15, 40, 10, 20], [15, 44, 10, 20, "createLucideIcon"], [15, 69, 10, 36], [15, 71, 10, 37], [15, 84, 10, 50], [15, 86, 10, 52], [15, 87, 11, 2], [15, 88, 11, 3], [15, 94, 11, 9], [15, 96, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 15, 11, 24], [17, 4, 11, 26, "height"], [17, 10, 11, 32], [17, 12, 11, 34], [17, 16, 11, 38], [18, 4, 11, 40, "x"], [18, 5, 11, 41], [18, 7, 11, 43], [18, 10, 11, 46], [19, 4, 11, 48, "y"], [19, 5, 11, 49], [19, 7, 11, 51], [19, 10, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "key"], [21, 7, 11, 68], [21, 9, 11, 70], [22, 2, 11, 79], [22, 3, 11, 80], [22, 4, 11, 81], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [22, 15, 12, 11], [23, 4, 12, 13, "d"], [23, 5, 12, 14], [23, 7, 12, 16], [23, 17, 12, 26], [24, 4, 12, 28, "key"], [24, 7, 12, 31], [24, 9, 12, 33], [25, 2, 12, 42], [25, 3, 12, 43], [25, 4, 12, 44], [25, 5, 13, 1], [25, 6, 13, 2], [26, 0, 13, 3], [26, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}