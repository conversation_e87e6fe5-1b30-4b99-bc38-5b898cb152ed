{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 12, "index": 294}, "end": {"line": 12, "column": 28, "index": 310}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * use-sync-external-store-with-selector.production.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  var React = require(_dependencyMap[0], \"react\");\n  function is(x, y) {\n    return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n  }\n  var objectIs = \"function\" === typeof Object.is ? Object.is : is,\n    useSyncExternalStore = React.useSyncExternalStore,\n    useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue;\n  exports.useSyncExternalStoreWithSelector = function (subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n    var instRef = useRef(null);\n    if (null === instRef.current) {\n      var inst = {\n        hasValue: !1,\n        value: null\n      };\n      instRef.current = inst;\n    } else inst = instRef.current;\n    instRef = useMemo(function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n          }\n          return memoizedSelection = nextSnapshot;\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return memoizedSelection = nextSelection;\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [function () {\n        return memoizedSelector(getSnapshot());\n      }, null === maybeGetServerSnapshot ? void 0 : function () {\n        return memoizedSelector(maybeGetServerSnapshot());\n      }];\n    }, [getSnapshot, getServerSnapshot, selector, isEqual]);\n    var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n    useEffect(function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    }, [value]);\n    useDebugValue(value);\n    return value;\n  };\n});", "lineCount": 70, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 6, 12, 4, "React"], [14, 11, 12, 9], [14, 14, 12, 12, "require"], [14, 21, 12, 19], [14, 22, 12, 19, "_dependencyMap"], [14, 36, 12, 19], [14, 48, 12, 27], [14, 49, 12, 28], [15, 2, 13, 0], [15, 11, 13, 9, "is"], [15, 13, 13, 11, "is"], [15, 14, 13, 12, "x"], [15, 15, 13, 13], [15, 17, 13, 15, "y"], [15, 18, 13, 16], [15, 20, 13, 18], [16, 4, 14, 2], [16, 11, 14, 10, "x"], [16, 12, 14, 11], [16, 17, 14, 16, "y"], [16, 18, 14, 17], [16, 23, 14, 22], [16, 24, 14, 23], [16, 29, 14, 28, "x"], [16, 30, 14, 29], [16, 34, 14, 33], [16, 35, 14, 34], [16, 38, 14, 37, "x"], [16, 39, 14, 38], [16, 44, 14, 43], [16, 45, 14, 44], [16, 48, 14, 47, "y"], [16, 49, 14, 48], [16, 50, 14, 49], [16, 54, 14, 55, "x"], [16, 55, 14, 56], [16, 60, 14, 61, "x"], [16, 61, 14, 62], [16, 65, 14, 66, "y"], [16, 66, 14, 67], [16, 71, 14, 72, "y"], [16, 72, 14, 74], [17, 2, 15, 0], [18, 2, 16, 0], [18, 6, 16, 4, "objectIs"], [18, 14, 16, 12], [18, 17, 16, 15], [18, 27, 16, 25], [18, 32, 16, 30], [18, 39, 16, 37, "Object"], [18, 45, 16, 43], [18, 46, 16, 44, "is"], [18, 48, 16, 46], [18, 51, 16, 49, "Object"], [18, 57, 16, 55], [18, 58, 16, 56, "is"], [18, 60, 16, 58], [18, 63, 16, 61, "is"], [18, 65, 16, 63], [19, 4, 17, 2, "useSyncExternalStore"], [19, 24, 17, 22], [19, 27, 17, 25, "React"], [19, 32, 17, 30], [19, 33, 17, 31, "useSyncExternalStore"], [19, 53, 17, 51], [20, 4, 18, 2, "useRef"], [20, 10, 18, 8], [20, 13, 18, 11, "React"], [20, 18, 18, 16], [20, 19, 18, 17, "useRef"], [20, 25, 18, 23], [21, 4, 19, 2, "useEffect"], [21, 13, 19, 11], [21, 16, 19, 14, "React"], [21, 21, 19, 19], [21, 22, 19, 20, "useEffect"], [21, 31, 19, 29], [22, 4, 20, 2, "useMemo"], [22, 11, 20, 9], [22, 14, 20, 12, "React"], [22, 19, 20, 17], [22, 20, 20, 18, "useMemo"], [22, 27, 20, 25], [23, 4, 21, 2, "useDebugValue"], [23, 17, 21, 15], [23, 20, 21, 18, "React"], [23, 25, 21, 23], [23, 26, 21, 24, "useDebugValue"], [23, 39, 21, 37], [24, 2, 22, 0, "exports"], [24, 9, 22, 7], [24, 10, 22, 8, "useSyncExternalStoreWithSelector"], [24, 42, 22, 40], [24, 45, 22, 43], [24, 55, 23, 2, "subscribe"], [24, 64, 23, 11], [24, 66, 24, 2, "getSnapshot"], [24, 77, 24, 13], [24, 79, 25, 2, "getServerSnapshot"], [24, 96, 25, 19], [24, 98, 26, 2, "selector"], [24, 106, 26, 10], [24, 108, 27, 2, "isEqual"], [24, 115, 27, 9], [24, 117, 28, 2], [25, 4, 29, 2], [25, 8, 29, 6, "instRef"], [25, 15, 29, 13], [25, 18, 29, 16, "useRef"], [25, 24, 29, 22], [25, 25, 29, 23], [25, 29, 29, 27], [25, 30, 29, 28], [26, 4, 30, 2], [26, 8, 30, 6], [26, 12, 30, 10], [26, 17, 30, 15, "instRef"], [26, 24, 30, 22], [26, 25, 30, 23, "current"], [26, 32, 30, 30], [26, 34, 30, 32], [27, 6, 31, 4], [27, 10, 31, 8, "inst"], [27, 14, 31, 12], [27, 17, 31, 15], [28, 8, 31, 17, "hasValue"], [28, 16, 31, 25], [28, 18, 31, 27], [28, 19, 31, 28], [28, 20, 31, 29], [29, 8, 31, 31, "value"], [29, 13, 31, 36], [29, 15, 31, 38], [30, 6, 31, 43], [30, 7, 31, 44], [31, 6, 32, 4, "instRef"], [31, 13, 32, 11], [31, 14, 32, 12, "current"], [31, 21, 32, 19], [31, 24, 32, 22, "inst"], [31, 28, 32, 26], [32, 4, 33, 2], [32, 5, 33, 3], [32, 11, 33, 9, "inst"], [32, 15, 33, 13], [32, 18, 33, 16, "instRef"], [32, 25, 33, 23], [32, 26, 33, 24, "current"], [32, 33, 33, 31], [33, 4, 34, 2, "instRef"], [33, 11, 34, 9], [33, 14, 34, 12, "useMemo"], [33, 21, 34, 19], [33, 22, 35, 4], [33, 34, 35, 16], [34, 6, 36, 6], [34, 15, 36, 15, "memoizedSelector"], [34, 31, 36, 31, "memoizedSelector"], [34, 32, 36, 32, "nextSnapshot"], [34, 44, 36, 44], [34, 46, 36, 46], [35, 8, 37, 8], [35, 12, 37, 12], [35, 13, 37, 13, "hasMemo"], [35, 20, 37, 20], [35, 22, 37, 22], [36, 10, 38, 10, "hasMemo"], [36, 17, 38, 17], [36, 20, 38, 20], [36, 21, 38, 21], [36, 22, 38, 22], [37, 10, 39, 10, "memoizedSnapshot"], [37, 26, 39, 26], [37, 29, 39, 29, "nextSnapshot"], [37, 41, 39, 41], [38, 10, 40, 10, "nextSnapshot"], [38, 22, 40, 22], [38, 25, 40, 25, "selector"], [38, 33, 40, 33], [38, 34, 40, 34, "nextSnapshot"], [38, 46, 40, 46], [38, 47, 40, 47], [39, 10, 41, 10], [39, 14, 41, 14], [39, 19, 41, 19], [39, 20, 41, 20], [39, 25, 41, 25, "isEqual"], [39, 32, 41, 32], [39, 36, 41, 36, "inst"], [39, 40, 41, 40], [39, 41, 41, 41, "hasValue"], [39, 49, 41, 49], [39, 51, 41, 51], [40, 12, 42, 12], [40, 16, 42, 16, "currentSelection"], [40, 32, 42, 32], [40, 35, 42, 35, "inst"], [40, 39, 42, 39], [40, 40, 42, 40, "value"], [40, 45, 42, 45], [41, 12, 43, 12], [41, 16, 43, 16, "isEqual"], [41, 23, 43, 23], [41, 24, 43, 24, "currentSelection"], [41, 40, 43, 40], [41, 42, 43, 42, "nextSnapshot"], [41, 54, 43, 54], [41, 55, 43, 55], [41, 57, 44, 14], [41, 64, 44, 22, "memoizedSelection"], [41, 81, 44, 39], [41, 84, 44, 42, "currentSelection"], [41, 100, 44, 58], [42, 10, 45, 10], [43, 10, 46, 10], [43, 17, 46, 18, "memoizedSelection"], [43, 34, 46, 35], [43, 37, 46, 38, "nextSnapshot"], [43, 49, 46, 50], [44, 8, 47, 8], [45, 8, 48, 8, "currentSelection"], [45, 24, 48, 24], [45, 27, 48, 27, "memoizedSelection"], [45, 44, 48, 44], [46, 8, 49, 8], [46, 12, 49, 12, "objectIs"], [46, 20, 49, 20], [46, 21, 49, 21, "memoizedSnapshot"], [46, 37, 49, 37], [46, 39, 49, 39, "nextSnapshot"], [46, 51, 49, 51], [46, 52, 49, 52], [46, 54, 49, 54], [46, 61, 49, 61, "currentSelection"], [46, 77, 49, 77], [47, 8, 50, 8], [47, 12, 50, 12, "nextSelection"], [47, 25, 50, 25], [47, 28, 50, 28, "selector"], [47, 36, 50, 36], [47, 37, 50, 37, "nextSnapshot"], [47, 49, 50, 49], [47, 50, 50, 50], [48, 8, 51, 8], [48, 12, 51, 12], [48, 17, 51, 17], [48, 18, 51, 18], [48, 23, 51, 23, "isEqual"], [48, 30, 51, 30], [48, 34, 51, 34, "isEqual"], [48, 41, 51, 41], [48, 42, 51, 42, "currentSelection"], [48, 58, 51, 58], [48, 60, 51, 60, "nextSelection"], [48, 73, 51, 73], [48, 74, 51, 74], [48, 76, 52, 10], [48, 83, 52, 18, "memoizedSnapshot"], [48, 99, 52, 34], [48, 102, 52, 37, "nextSnapshot"], [48, 114, 52, 49], [48, 116, 52, 52, "currentSelection"], [48, 132, 52, 68], [49, 8, 53, 8, "memoizedSnapshot"], [49, 24, 53, 24], [49, 27, 53, 27, "nextSnapshot"], [49, 39, 53, 39], [50, 8, 54, 8], [50, 15, 54, 16, "memoizedSelection"], [50, 32, 54, 33], [50, 35, 54, 36, "nextSelection"], [50, 48, 54, 49], [51, 6, 55, 6], [52, 6, 56, 6], [52, 10, 56, 10, "hasMemo"], [52, 17, 56, 17], [52, 20, 56, 20], [52, 21, 56, 21], [52, 22, 56, 22], [53, 8, 57, 8, "memoizedSnapshot"], [53, 24, 57, 24], [54, 8, 58, 8, "memoizedSelection"], [54, 25, 58, 25], [55, 8, 59, 8, "maybeGetServerSnapshot"], [55, 30, 59, 30], [55, 33, 60, 10], [55, 38, 60, 15], [55, 39, 60, 16], [55, 44, 60, 21, "getServerSnapshot"], [55, 61, 60, 38], [55, 64, 60, 41], [55, 68, 60, 45], [55, 71, 60, 48, "getServerSnapshot"], [55, 88, 60, 65], [56, 6, 61, 6], [56, 13, 61, 13], [56, 14, 62, 8], [56, 26, 62, 20], [57, 8, 63, 10], [57, 15, 63, 17, "memoizedSelector"], [57, 31, 63, 33], [57, 32, 63, 34, "getSnapshot"], [57, 43, 63, 45], [57, 44, 63, 46], [57, 45, 63, 47], [57, 46, 63, 48], [58, 6, 64, 8], [58, 7, 64, 9], [58, 9, 65, 8], [58, 13, 65, 12], [58, 18, 65, 17, "maybeGetServerSnapshot"], [58, 40, 65, 39], [58, 43, 66, 12], [58, 48, 66, 17], [58, 49, 66, 18], [58, 52, 67, 12], [58, 64, 67, 24], [59, 8, 68, 14], [59, 15, 68, 21, "memoizedSelector"], [59, 31, 68, 37], [59, 32, 68, 38, "maybeGetServerSnapshot"], [59, 54, 68, 60], [59, 55, 68, 61], [59, 56, 68, 62], [59, 57, 68, 63], [60, 6, 69, 12], [60, 7, 69, 13], [60, 8, 70, 7], [61, 4, 71, 4], [61, 5, 71, 5], [61, 7, 72, 4], [61, 8, 72, 5, "getSnapshot"], [61, 19, 72, 16], [61, 21, 72, 18, "getServerSnapshot"], [61, 38, 72, 35], [61, 40, 72, 37, "selector"], [61, 48, 72, 45], [61, 50, 72, 47, "isEqual"], [61, 57, 72, 54], [61, 58, 73, 2], [61, 59, 73, 3], [62, 4, 74, 2], [62, 8, 74, 6, "value"], [62, 13, 74, 11], [62, 16, 74, 14, "useSyncExternalStore"], [62, 36, 74, 34], [62, 37, 74, 35, "subscribe"], [62, 46, 74, 44], [62, 48, 74, 46, "instRef"], [62, 55, 74, 53], [62, 56, 74, 54], [62, 57, 74, 55], [62, 58, 74, 56], [62, 60, 74, 58, "instRef"], [62, 67, 74, 65], [62, 68, 74, 66], [62, 69, 74, 67], [62, 70, 74, 68], [62, 71, 74, 69], [63, 4, 75, 2, "useEffect"], [63, 13, 75, 11], [63, 14, 76, 4], [63, 26, 76, 16], [64, 6, 77, 6, "inst"], [64, 10, 77, 10], [64, 11, 77, 11, "hasValue"], [64, 19, 77, 19], [64, 22, 77, 22], [64, 23, 77, 23], [64, 24, 77, 24], [65, 6, 78, 6, "inst"], [65, 10, 78, 10], [65, 11, 78, 11, "value"], [65, 16, 78, 16], [65, 19, 78, 19, "value"], [65, 24, 78, 24], [66, 4, 79, 4], [66, 5, 79, 5], [66, 7, 80, 4], [66, 8, 80, 5, "value"], [66, 13, 80, 10], [66, 14, 81, 2], [66, 15, 81, 3], [67, 4, 82, 2, "useDebugValue"], [67, 17, 82, 15], [67, 18, 82, 16, "value"], [67, 23, 82, 21], [67, 24, 82, 22], [68, 4, 83, 2], [68, 11, 83, 9, "value"], [68, 16, 83, 14], [69, 2, 84, 0], [69, 3, 84, 1], [70, 0, 84, 2], [70, 3]], "functionMap": {"names": ["<global>", "is", "exports.useSyncExternalStoreWithSelector", "useMemo$argument_0", "memoizedSelector", "<anonymous>", "useEffect$argument_0"], "mappings": "AAA;ACY;CDE;2CEO;ICa;MCC;ODmB;QEO;SFE;YEG;aFE;KDE;IIK;KJG;CFK"}}, "type": "js/module"}]}