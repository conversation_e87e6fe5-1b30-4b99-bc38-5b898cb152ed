{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./ExpoUpdates", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 46, "index": 46}}], "key": "xvfiwKXsFVvB05bziLrxffeS3bo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.resetLatestContext = exports.latestContext = exports.emitTestStateChangeEvent = exports.addUpdatesStateChangeListener = void 0;\n  var _ExpoUpdates = _interopRequireDefault(require(_dependencyMap[1], \"./ExpoUpdates\"));\n  var latestContext = exports.latestContext = transformNativeStateMachineContext(_ExpoUpdates.default.initialContext);\n  _ExpoUpdates.default.addListener('Expo.nativeUpdatesStateChangeEvent', _handleNativeStateChangeEvent);\n  var _updatesStateChangeListeners = new Set();\n  // Reemits native state change events\n  function _handleNativeStateChangeEvent(params) {\n    var newParams = typeof params === 'string' ? JSON.parse(params) : {\n      ...params\n    };\n    var transformedContext = transformNativeStateMachineContext(newParams.context);\n    // only process state change events if they are in order\n    if (transformedContext.sequenceNumber <= latestContext.sequenceNumber) {\n      return;\n    }\n    newParams.context = transformedContext;\n    exports.latestContext = latestContext = transformedContext;\n    _updatesStateChangeListeners.forEach(listener => listener(newParams));\n  }\n  /**\n   * Add listener for state change events\n   * @hidden\n   */\n  var addUpdatesStateChangeListener = listener => {\n    _updatesStateChangeListeners.add(listener);\n    return {\n      remove() {\n        _updatesStateChangeListeners.delete(listener);\n      }\n    };\n  };\n  /**\n   * Allows JS test to emit a simulated native state change event (used in unit testing)\n   * @hidden\n   */\n  exports.addUpdatesStateChangeListener = addUpdatesStateChangeListener;\n  var emitTestStateChangeEvent = event => {\n    _handleNativeStateChangeEvent(event);\n  };\n  /**\n   * Allows JS test to reset latest context (and sequence number)\n   * @hidden\n   */\n  exports.emitTestStateChangeEvent = emitTestStateChangeEvent;\n  var resetLatestContext = () => {\n    exports.latestContext = latestContext = transformNativeStateMachineContext(_ExpoUpdates.default.initialContext);\n  };\n  exports.resetLatestContext = resetLatestContext;\n  function transformNativeStateMachineContext(originalNativeContext) {\n    var nativeContext = {\n      ...originalNativeContext\n    };\n    if (nativeContext.latestManifestString) {\n      nativeContext.latestManifest = JSON.parse(nativeContext.latestManifestString);\n      delete nativeContext.latestManifestString;\n    }\n    if (nativeContext.downloadedManifestString) {\n      nativeContext.downloadedManifest = JSON.parse(nativeContext.downloadedManifestString);\n      delete nativeContext.downloadedManifestString;\n    }\n    if (nativeContext.lastCheckForUpdateTimeString) {\n      nativeContext.lastCheckForUpdateTime = new Date(nativeContext.lastCheckForUpdateTimeString);\n      delete nativeContext.lastCheckForUpdateTimeString;\n    }\n    if (nativeContext.rollbackString) {\n      nativeContext.rollback = JSON.parse(nativeContext.rollbackString);\n      delete nativeContext.rollbackString;\n    }\n    return nativeContext;\n  }\n});", "lineCount": 76, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_ExpoUpdates"], [7, 18, 1, 0], [7, 21, 1, 0, "_interopRequireDefault"], [7, 43, 1, 0], [7, 44, 1, 0, "require"], [7, 51, 1, 0], [7, 52, 1, 0, "_dependencyMap"], [7, 66, 1, 0], [8, 2, 2, 7], [8, 6, 2, 11, "latestContext"], [8, 19, 2, 24], [8, 22, 2, 24, "exports"], [8, 29, 2, 24], [8, 30, 2, 24, "latestContext"], [8, 43, 2, 24], [8, 46, 2, 27, "transformNativeStateMachineContext"], [8, 80, 2, 61], [8, 81, 2, 62, "ExpoUpdatesModule"], [8, 101, 2, 79], [8, 102, 2, 80, "initialContext"], [8, 116, 2, 94], [8, 117, 2, 95], [9, 2, 3, 0, "ExpoUpdatesModule"], [9, 22, 3, 17], [9, 23, 3, 18, "addListener"], [9, 34, 3, 29], [9, 35, 3, 30], [9, 71, 3, 66], [9, 73, 3, 68, "_handleNativeStateChangeEvent"], [9, 102, 3, 97], [9, 103, 3, 98], [10, 2, 4, 0], [10, 6, 4, 6, "_updatesStateChangeListeners"], [10, 34, 4, 34], [10, 37, 4, 37], [10, 41, 4, 41, "Set"], [10, 44, 4, 44], [10, 45, 4, 45], [10, 46, 4, 46], [11, 2, 5, 0], [12, 2, 6, 0], [12, 11, 6, 9, "_handleNativeStateChangeEvent"], [12, 40, 6, 38, "_handleNativeStateChangeEvent"], [12, 41, 6, 39, "params"], [12, 47, 6, 45], [12, 49, 6, 47], [13, 4, 7, 4], [13, 8, 7, 10, "newParams"], [13, 17, 7, 19], [13, 20, 7, 22], [13, 27, 7, 29, "params"], [13, 33, 7, 35], [13, 38, 7, 40], [13, 46, 7, 48], [13, 49, 7, 51, "JSON"], [13, 53, 7, 55], [13, 54, 7, 56, "parse"], [13, 59, 7, 61], [13, 60, 7, 62, "params"], [13, 66, 7, 68], [13, 67, 7, 69], [13, 70, 7, 72], [14, 6, 7, 74], [14, 9, 7, 77, "params"], [15, 4, 7, 84], [15, 5, 7, 85], [16, 4, 8, 4], [16, 8, 8, 10, "transformedContext"], [16, 26, 8, 28], [16, 29, 8, 31, "transformNativeStateMachineContext"], [16, 63, 8, 65], [16, 64, 8, 66, "newParams"], [16, 73, 8, 75], [16, 74, 8, 76, "context"], [16, 81, 8, 83], [16, 82, 8, 84], [17, 4, 9, 4], [18, 4, 10, 4], [18, 8, 10, 8, "transformedContext"], [18, 26, 10, 26], [18, 27, 10, 27, "sequenceNumber"], [18, 41, 10, 41], [18, 45, 10, 45, "latestContext"], [18, 58, 10, 58], [18, 59, 10, 59, "sequenceNumber"], [18, 73, 10, 73], [18, 75, 10, 75], [19, 6, 11, 8], [20, 4, 12, 4], [21, 4, 13, 4, "newParams"], [21, 13, 13, 13], [21, 14, 13, 14, "context"], [21, 21, 13, 21], [21, 24, 13, 24, "transformedContext"], [21, 42, 13, 42], [22, 4, 14, 4, "exports"], [22, 11, 14, 4], [22, 12, 14, 4, "latestContext"], [22, 25, 14, 4], [22, 28, 14, 4, "latestContext"], [22, 41, 14, 17], [22, 44, 14, 20, "transformedContext"], [22, 62, 14, 38], [23, 4, 15, 4, "_updatesStateChangeListeners"], [23, 32, 15, 32], [23, 33, 15, 33, "for<PERSON>ach"], [23, 40, 15, 40], [23, 41, 15, 42, "listener"], [23, 49, 15, 50], [23, 53, 15, 55, "listener"], [23, 61, 15, 63], [23, 62, 15, 64, "newParams"], [23, 71, 15, 73], [23, 72, 15, 74], [23, 73, 15, 75], [24, 2, 16, 0], [25, 2, 17, 0], [26, 0, 18, 0], [27, 0, 19, 0], [28, 0, 20, 0], [29, 2, 21, 7], [29, 6, 21, 13, "addUpdatesStateChangeListener"], [29, 35, 21, 42], [29, 38, 21, 46, "listener"], [29, 46, 21, 54], [29, 50, 21, 59], [30, 4, 22, 4, "_updatesStateChangeListeners"], [30, 32, 22, 32], [30, 33, 22, 33, "add"], [30, 36, 22, 36], [30, 37, 22, 37, "listener"], [30, 45, 22, 45], [30, 46, 22, 46], [31, 4, 23, 4], [31, 11, 23, 11], [32, 6, 24, 8, "remove"], [32, 12, 24, 14, "remove"], [32, 13, 24, 14], [32, 15, 24, 17], [33, 8, 25, 12, "_updatesStateChangeListeners"], [33, 36, 25, 40], [33, 37, 25, 41, "delete"], [33, 43, 25, 47], [33, 44, 25, 48, "listener"], [33, 52, 25, 56], [33, 53, 25, 57], [34, 6, 26, 8], [35, 4, 27, 4], [35, 5, 27, 5], [36, 2, 28, 0], [36, 3, 28, 1], [37, 2, 29, 0], [38, 0, 30, 0], [39, 0, 31, 0], [40, 0, 32, 0], [41, 2, 29, 0, "exports"], [41, 9, 29, 0], [41, 10, 29, 0, "addUpdatesStateChangeListener"], [41, 39, 29, 0], [41, 42, 29, 0, "addUpdatesStateChangeListener"], [41, 71, 29, 0], [42, 2, 33, 7], [42, 6, 33, 13, "emitTestStateChangeEvent"], [42, 30, 33, 37], [42, 33, 33, 41, "event"], [42, 38, 33, 46], [42, 42, 33, 51], [43, 4, 34, 4, "_handleNativeStateChangeEvent"], [43, 33, 34, 33], [43, 34, 34, 34, "event"], [43, 39, 34, 39], [43, 40, 34, 40], [44, 2, 35, 0], [44, 3, 35, 1], [45, 2, 36, 0], [46, 0, 37, 0], [47, 0, 38, 0], [48, 0, 39, 0], [49, 2, 36, 0, "exports"], [49, 9, 36, 0], [49, 10, 36, 0, "emitTestStateChangeEvent"], [49, 34, 36, 0], [49, 37, 36, 0, "emitTestStateChangeEvent"], [49, 61, 36, 0], [50, 2, 40, 7], [50, 6, 40, 13, "resetLatestContext"], [50, 24, 40, 31], [50, 27, 40, 34, "resetLatestContext"], [50, 28, 40, 34], [50, 33, 40, 40], [51, 4, 41, 4, "exports"], [51, 11, 41, 4], [51, 12, 41, 4, "latestContext"], [51, 25, 41, 4], [51, 28, 41, 4, "latestContext"], [51, 41, 41, 17], [51, 44, 41, 20, "transformNativeStateMachineContext"], [51, 78, 41, 54], [51, 79, 41, 55, "ExpoUpdatesModule"], [51, 99, 41, 72], [51, 100, 41, 73, "initialContext"], [51, 114, 41, 87], [51, 115, 41, 88], [52, 2, 42, 0], [52, 3, 42, 1], [53, 2, 42, 2, "exports"], [53, 9, 42, 2], [53, 10, 42, 2, "resetLatestContext"], [53, 28, 42, 2], [53, 31, 42, 2, "resetLatestContext"], [53, 49, 42, 2], [54, 2, 43, 0], [54, 11, 43, 9, "transformNativeStateMachineContext"], [54, 45, 43, 43, "transformNativeStateMachineContext"], [54, 46, 43, 44, "originalNativeContext"], [54, 67, 43, 65], [54, 69, 43, 67], [55, 4, 44, 4], [55, 8, 44, 10, "nativeContext"], [55, 21, 44, 23], [55, 24, 44, 26], [56, 6, 44, 28], [56, 9, 44, 31, "originalNativeContext"], [57, 4, 44, 53], [57, 5, 44, 54], [58, 4, 45, 4], [58, 8, 45, 8, "nativeContext"], [58, 21, 45, 21], [58, 22, 45, 22, "latestManifestString"], [58, 42, 45, 42], [58, 44, 45, 44], [59, 6, 46, 8, "nativeContext"], [59, 19, 46, 21], [59, 20, 46, 22, "latestManifest"], [59, 34, 46, 36], [59, 37, 46, 39, "JSON"], [59, 41, 46, 43], [59, 42, 46, 44, "parse"], [59, 47, 46, 49], [59, 48, 46, 50, "nativeContext"], [59, 61, 46, 63], [59, 62, 46, 64, "latestManifestString"], [59, 82, 46, 84], [59, 83, 46, 85], [60, 6, 47, 8], [60, 13, 47, 15, "nativeContext"], [60, 26, 47, 28], [60, 27, 47, 29, "latestManifestString"], [60, 47, 47, 49], [61, 4, 48, 4], [62, 4, 49, 4], [62, 8, 49, 8, "nativeContext"], [62, 21, 49, 21], [62, 22, 49, 22, "downloadedManifestString"], [62, 46, 49, 46], [62, 48, 49, 48], [63, 6, 50, 8, "nativeContext"], [63, 19, 50, 21], [63, 20, 50, 22, "downloadedManifest"], [63, 38, 50, 40], [63, 41, 50, 43, "JSON"], [63, 45, 50, 47], [63, 46, 50, 48, "parse"], [63, 51, 50, 53], [63, 52, 50, 54, "nativeContext"], [63, 65, 50, 67], [63, 66, 50, 68, "downloadedManifestString"], [63, 90, 50, 92], [63, 91, 50, 93], [64, 6, 51, 8], [64, 13, 51, 15, "nativeContext"], [64, 26, 51, 28], [64, 27, 51, 29, "downloadedManifestString"], [64, 51, 51, 53], [65, 4, 52, 4], [66, 4, 53, 4], [66, 8, 53, 8, "nativeContext"], [66, 21, 53, 21], [66, 22, 53, 22, "lastCheckForUpdateTimeString"], [66, 50, 53, 50], [66, 52, 53, 52], [67, 6, 54, 8, "nativeContext"], [67, 19, 54, 21], [67, 20, 54, 22, "lastCheckForUpdateTime"], [67, 42, 54, 44], [67, 45, 54, 47], [67, 49, 54, 51, "Date"], [67, 53, 54, 55], [67, 54, 54, 56, "nativeContext"], [67, 67, 54, 69], [67, 68, 54, 70, "lastCheckForUpdateTimeString"], [67, 96, 54, 98], [67, 97, 54, 99], [68, 6, 55, 8], [68, 13, 55, 15, "nativeContext"], [68, 26, 55, 28], [68, 27, 55, 29, "lastCheckForUpdateTimeString"], [68, 55, 55, 57], [69, 4, 56, 4], [70, 4, 57, 4], [70, 8, 57, 8, "nativeContext"], [70, 21, 57, 21], [70, 22, 57, 22, "rollbackString"], [70, 36, 57, 36], [70, 38, 57, 38], [71, 6, 58, 8, "nativeContext"], [71, 19, 58, 21], [71, 20, 58, 22, "rollback"], [71, 28, 58, 30], [71, 31, 58, 33, "JSON"], [71, 35, 58, 37], [71, 36, 58, 38, "parse"], [71, 41, 58, 43], [71, 42, 58, 44, "nativeContext"], [71, 55, 58, 57], [71, 56, 58, 58, "rollbackString"], [71, 70, 58, 72], [71, 71, 58, 73], [72, 6, 59, 8], [72, 13, 59, 15, "nativeContext"], [72, 26, 59, 28], [72, 27, 59, 29, "rollbackString"], [72, 41, 59, 43], [73, 4, 60, 4], [74, 4, 61, 4], [74, 11, 61, 11, "nativeContext"], [74, 24, 61, 24], [75, 2, 62, 0], [76, 0, 62, 1], [76, 3]], "functionMap": {"names": ["<global>", "_handleNativeStateChangeEvent", "_updatesStateChangeListeners.forEach$argument_0", "addUpdatesStateChangeListener", "remove", "emitTestStateChangeEvent", "resetLatestContext", "transformNativeStateMachineContext"], "mappings": "AAA;ACK;yCCS,iCD;CDC;6CGK;QCG;SDE;CHE;wCKK;CLE;kCMK;CNE;AOC;CPmB"}}, "type": "js/module"}]}