{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 63, "column": 0, "index": 1713}, "end": {"line": 65, "column": 3, "index": 1804}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 63, "column": 0, "index": 1713}, "end": {"line": 65, "column": 3, "index": 1804}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNSVGUse';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGUse\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      href: true,\n      x: true,\n      y: true,\n      height: true,\n      width: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 50, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 63, 0], [8, 6, 63, 0, "NativeComponentRegistry"], [8, 29, 65, 3], [8, 32, 63, 0, "require"], [8, 39, 65, 3], [8, 40, 65, 3, "_dependencyMap"], [8, 54, 65, 3], [8, 123, 65, 2], [8, 124, 65, 3], [9, 2, 63, 0], [9, 6, 63, 0, "nativeComponentName"], [9, 25, 65, 3], [9, 28, 63, 0], [9, 38, 65, 3], [10, 2, 63, 0], [10, 6, 63, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 65, 3], [10, 31, 65, 3, "exports"], [10, 38, 65, 3], [10, 39, 65, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 65, 3], [10, 64, 63, 0], [11, 4, 63, 0, "uiViewClassName"], [11, 19, 65, 3], [11, 21, 63, 0], [11, 31, 65, 3], [12, 4, 63, 0, "validAttributes"], [12, 19, 65, 3], [12, 21, 63, 0], [13, 6, 63, 0, "name"], [13, 10, 65, 3], [13, 12, 63, 0], [13, 16, 65, 3], [14, 6, 63, 0, "opacity"], [14, 13, 65, 3], [14, 15, 63, 0], [14, 19, 65, 3], [15, 6, 63, 0, "matrix"], [15, 12, 65, 3], [15, 14, 63, 0], [15, 18, 65, 3], [16, 6, 63, 0, "mask"], [16, 10, 65, 3], [16, 12, 63, 0], [16, 16, 65, 3], [17, 6, 63, 0, "markerStart"], [17, 17, 65, 3], [17, 19, 63, 0], [17, 23, 65, 3], [18, 6, 63, 0, "markerMid"], [18, 15, 65, 3], [18, 17, 63, 0], [18, 21, 65, 3], [19, 6, 63, 0, "markerEnd"], [19, 15, 65, 3], [19, 17, 63, 0], [19, 21, 65, 3], [20, 6, 63, 0, "clipPath"], [20, 14, 65, 3], [20, 16, 63, 0], [20, 20, 65, 3], [21, 6, 63, 0, "clipRule"], [21, 14, 65, 3], [21, 16, 63, 0], [21, 20, 65, 3], [22, 6, 63, 0, "responsible"], [22, 17, 65, 3], [22, 19, 63, 0], [22, 23, 65, 3], [23, 6, 63, 0, "display"], [23, 13, 65, 3], [23, 15, 63, 0], [23, 19, 65, 3], [24, 6, 63, 0, "pointerEvents"], [24, 19, 65, 3], [24, 21, 63, 0], [24, 25, 65, 3], [25, 6, 63, 0, "color"], [25, 11, 65, 3], [25, 13, 63, 0], [26, 8, 63, 0, "process"], [26, 15, 65, 3], [26, 17, 63, 0, "require"], [26, 24, 65, 3], [26, 25, 65, 3, "_dependencyMap"], [26, 39, 65, 3], [26, 92, 65, 2], [26, 93, 65, 3], [26, 94, 63, 0, "default"], [27, 6, 65, 2], [27, 7, 65, 3], [28, 6, 63, 0, "fill"], [28, 10, 65, 3], [28, 12, 63, 0], [28, 16, 65, 3], [29, 6, 63, 0, "fillOpacity"], [29, 17, 65, 3], [29, 19, 63, 0], [29, 23, 65, 3], [30, 6, 63, 0, "fillRule"], [30, 14, 65, 3], [30, 16, 63, 0], [30, 20, 65, 3], [31, 6, 63, 0, "stroke"], [31, 12, 65, 3], [31, 14, 63, 0], [31, 18, 65, 3], [32, 6, 63, 0, "strokeOpacity"], [32, 19, 65, 3], [32, 21, 63, 0], [32, 25, 65, 3], [33, 6, 63, 0, "strokeWidth"], [33, 17, 65, 3], [33, 19, 63, 0], [33, 23, 65, 3], [34, 6, 63, 0, "strokeLinecap"], [34, 19, 65, 3], [34, 21, 63, 0], [34, 25, 65, 3], [35, 6, 63, 0, "strokeLinejoin"], [35, 20, 65, 3], [35, 22, 63, 0], [35, 26, 65, 3], [36, 6, 63, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 65, 3], [36, 23, 63, 0], [36, 27, 65, 3], [37, 6, 63, 0, "strokeDashoffset"], [37, 22, 65, 3], [37, 24, 63, 0], [37, 28, 65, 3], [38, 6, 63, 0, "strokeMiterlimit"], [38, 22, 65, 3], [38, 24, 63, 0], [38, 28, 65, 3], [39, 6, 63, 0, "vectorEffect"], [39, 18, 65, 3], [39, 20, 63, 0], [39, 24, 65, 3], [40, 6, 63, 0, "propList"], [40, 14, 65, 3], [40, 16, 63, 0], [40, 20, 65, 3], [41, 6, 63, 0, "filter"], [41, 12, 65, 3], [41, 14, 63, 0], [41, 18, 65, 3], [42, 6, 63, 0, "href"], [42, 10, 65, 3], [42, 12, 63, 0], [42, 16, 65, 3], [43, 6, 63, 0, "x"], [43, 7, 65, 3], [43, 9, 63, 0], [43, 13, 65, 3], [44, 6, 63, 0, "y"], [44, 7, 65, 3], [44, 9, 63, 0], [44, 13, 65, 3], [45, 6, 63, 0, "height"], [45, 12, 65, 3], [45, 14, 63, 0], [45, 18, 65, 3], [46, 6, 63, 0, "width"], [46, 11, 65, 3], [46, 13, 63, 0], [47, 4, 65, 2], [48, 2, 65, 2], [48, 3, 65, 3], [49, 2, 65, 3], [49, 6, 65, 3, "_default"], [49, 14, 65, 3], [49, 17, 65, 3, "exports"], [49, 24, 65, 3], [49, 25, 65, 3, "default"], [49, 32, 65, 3], [49, 35, 63, 0, "NativeComponentRegistry"], [49, 58, 65, 3], [49, 59, 63, 0, "get"], [49, 62, 65, 3], [49, 63, 63, 0, "nativeComponentName"], [49, 82, 65, 3], [49, 84, 63, 0], [49, 90, 63, 0, "__INTERNAL_VIEW_CONFIG"], [49, 112, 65, 2], [49, 113, 65, 3], [50, 0, 65, 3], [50, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}