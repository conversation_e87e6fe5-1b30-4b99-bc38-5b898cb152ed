{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 63, "index": 78}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 139}, "end": {"line": 5, "column": 48, "index": 187}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderIcon = HeaderIcon;\n  exports.ICON_SIZE = exports.ICON_MARGIN = void 0;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _Image = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Image\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/StyleSheet\"));\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  function HeaderIcon({\n    source,\n    style,\n    ...rest\n  }) {\n    const {\n      colors\n    } = (0, _native.useTheme)();\n    const {\n      direction\n    } = (0, _native.useLocale)();\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Image.default, {\n      source: source,\n      resizeMode: \"contain\",\n      fadeDuration: 0,\n      tintColor: colors.text,\n      style: [styles.icon, direction === 'rtl' && styles.flip, style],\n      ...rest\n    });\n  }\n  const ICON_SIZE = exports.ICON_SIZE = _Platform.default.OS === 'ios' ? 21 : 24;\n  const ICON_MARGIN = exports.ICON_MARGIN = _Platform.default.OS === 'ios' ? 8 : 3;\n  const styles = _StyleSheet.default.create({\n    icon: {\n      width: ICON_SIZE,\n      height: ICON_SIZE,\n      margin: ICON_MARGIN\n    },\n    flip: {\n      transform: 'scaleX(-1)'\n    }\n  });\n});", "lineCount": 47, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "HeaderIcon"], [8, 20, 1, 13], [8, 23, 1, 13, "HeaderIcon"], [8, 33, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "ICON_SIZE"], [9, 19, 1, 13], [9, 22, 1, 13, "exports"], [9, 29, 1, 13], [9, 30, 1, 13, "ICON_MARGIN"], [9, 41, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_native"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 3, 63], [11, 6, 3, 63, "_Image"], [11, 12, 3, 63], [11, 15, 3, 63, "_interopRequireDefault"], [11, 37, 3, 63], [11, 38, 3, 63, "require"], [11, 45, 3, 63], [11, 46, 3, 63, "_dependencyMap"], [11, 60, 3, 63], [12, 2, 3, 63], [12, 6, 3, 63, "_Platform"], [12, 15, 3, 63], [12, 18, 3, 63, "_interopRequireDefault"], [12, 40, 3, 63], [12, 41, 3, 63, "require"], [12, 48, 3, 63], [12, 49, 3, 63, "_dependencyMap"], [12, 63, 3, 63], [13, 2, 3, 63], [13, 6, 3, 63, "_StyleSheet"], [13, 17, 3, 63], [13, 20, 3, 63, "_interopRequireDefault"], [13, 42, 3, 63], [13, 43, 3, 63, "require"], [13, 50, 3, 63], [13, 51, 3, 63, "_dependencyMap"], [13, 65, 3, 63], [14, 2, 5, 0], [14, 6, 5, 0, "_jsxRuntime"], [14, 17, 5, 0], [14, 20, 5, 0, "require"], [14, 27, 5, 0], [14, 28, 5, 0, "_dependencyMap"], [14, 42, 5, 0], [15, 2, 6, 7], [15, 11, 6, 16, "HeaderIcon"], [15, 21, 6, 26, "HeaderIcon"], [15, 22, 6, 27], [16, 4, 7, 2, "source"], [16, 10, 7, 8], [17, 4, 8, 2, "style"], [17, 9, 8, 7], [18, 4, 9, 2], [18, 7, 9, 5, "rest"], [19, 2, 10, 0], [19, 3, 10, 1], [19, 5, 10, 3], [20, 4, 11, 2], [20, 10, 11, 8], [21, 6, 12, 4, "colors"], [22, 4, 13, 2], [22, 5, 13, 3], [22, 8, 13, 6], [22, 12, 13, 6, "useTheme"], [22, 28, 13, 14], [22, 30, 13, 15], [22, 31, 13, 16], [23, 4, 14, 2], [23, 10, 14, 8], [24, 6, 15, 4, "direction"], [25, 4, 16, 2], [25, 5, 16, 3], [25, 8, 16, 6], [25, 12, 16, 6, "useLocale"], [25, 29, 16, 15], [25, 31, 16, 16], [25, 32, 16, 17], [26, 4, 17, 2], [26, 11, 17, 9], [26, 24, 17, 22], [26, 28, 17, 22, "_jsx"], [26, 43, 17, 26], [26, 45, 17, 27, "Image"], [26, 59, 17, 32], [26, 61, 17, 34], [27, 6, 18, 4, "source"], [27, 12, 18, 10], [27, 14, 18, 12, "source"], [27, 20, 18, 18], [28, 6, 19, 4, "resizeMode"], [28, 16, 19, 14], [28, 18, 19, 16], [28, 27, 19, 25], [29, 6, 20, 4, "fadeDuration"], [29, 18, 20, 16], [29, 20, 20, 18], [29, 21, 20, 19], [30, 6, 21, 4, "tintColor"], [30, 15, 21, 13], [30, 17, 21, 15, "colors"], [30, 23, 21, 21], [30, 24, 21, 22, "text"], [30, 28, 21, 26], [31, 6, 22, 4, "style"], [31, 11, 22, 9], [31, 13, 22, 11], [31, 14, 22, 12, "styles"], [31, 20, 22, 18], [31, 21, 22, 19, "icon"], [31, 25, 22, 23], [31, 27, 22, 25, "direction"], [31, 36, 22, 34], [31, 41, 22, 39], [31, 46, 22, 44], [31, 50, 22, 48, "styles"], [31, 56, 22, 54], [31, 57, 22, 55, "flip"], [31, 61, 22, 59], [31, 63, 22, 61, "style"], [31, 68, 22, 66], [31, 69, 22, 67], [32, 6, 23, 4], [32, 9, 23, 7, "rest"], [33, 4, 24, 2], [33, 5, 24, 3], [33, 6, 24, 4], [34, 2, 25, 0], [35, 2, 26, 7], [35, 8, 26, 13, "ICON_SIZE"], [35, 17, 26, 22], [35, 20, 26, 22, "exports"], [35, 27, 26, 22], [35, 28, 26, 22, "ICON_SIZE"], [35, 37, 26, 22], [35, 40, 26, 25, "Platform"], [35, 57, 26, 33], [35, 58, 26, 34, "OS"], [35, 60, 26, 36], [35, 65, 26, 41], [35, 70, 26, 46], [35, 73, 26, 49], [35, 75, 26, 51], [35, 78, 26, 54], [35, 80, 26, 56], [36, 2, 27, 7], [36, 8, 27, 13, "ICON_MARGIN"], [36, 19, 27, 24], [36, 22, 27, 24, "exports"], [36, 29, 27, 24], [36, 30, 27, 24, "ICON_MARGIN"], [36, 41, 27, 24], [36, 44, 27, 27, "Platform"], [36, 61, 27, 35], [36, 62, 27, 36, "OS"], [36, 64, 27, 38], [36, 69, 27, 43], [36, 74, 27, 48], [36, 77, 27, 51], [36, 78, 27, 52], [36, 81, 27, 55], [36, 82, 27, 56], [37, 2, 28, 0], [37, 8, 28, 6, "styles"], [37, 14, 28, 12], [37, 17, 28, 15, "StyleSheet"], [37, 36, 28, 25], [37, 37, 28, 26, "create"], [37, 43, 28, 32], [37, 44, 28, 33], [38, 4, 29, 2, "icon"], [38, 8, 29, 6], [38, 10, 29, 8], [39, 6, 30, 4, "width"], [39, 11, 30, 9], [39, 13, 30, 11, "ICON_SIZE"], [39, 22, 30, 20], [40, 6, 31, 4, "height"], [40, 12, 31, 10], [40, 14, 31, 12, "ICON_SIZE"], [40, 23, 31, 21], [41, 6, 32, 4, "margin"], [41, 12, 32, 10], [41, 14, 32, 12, "ICON_MARGIN"], [42, 4, 33, 2], [42, 5, 33, 3], [43, 4, 34, 2, "flip"], [43, 8, 34, 6], [43, 10, 34, 8], [44, 6, 35, 4, "transform"], [44, 15, 35, 13], [44, 17, 35, 15], [45, 4, 36, 2], [46, 2, 37, 0], [46, 3, 37, 1], [46, 4, 37, 2], [47, 0, 37, 3], [47, 3]], "functionMap": {"names": ["<global>", "HeaderIcon"], "mappings": "AAA;OCK;CDmB"}}, "type": "js/module"}]}