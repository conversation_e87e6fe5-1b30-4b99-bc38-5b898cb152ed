{"dependencies": [{"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 18}, "end": {"line": 14, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var invariant = require(_dependencyMap[0], \"invariant\");\n  var reactDevToolsHook = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n  invariant(<PERSON><PERSON>an(reactDevToolsHook), 'getInspectorDataForViewAtPoint should not be used if React DevTools hook is not injected');\n  var renderers = Array.from(window.__REACT_DEVTOOLS_GLOBAL_HOOK__.renderers.values());\n  var appendRenderer = _ref => {\n    var renderer = _ref.renderer;\n    return renderers.push(renderer);\n  };\n  reactDevToolsHook.on('renderer', appendRenderer);\n  function validateRenderers() {\n    invariant(renderers.length > 0, 'Expected to find at least one React Native renderer on DevTools hook.');\n  }\n  function getInspectorDataForViewAtPoint(inspectedView, locationX, locationY, callback) {\n    validateRenderers();\n    var shouldBreak = false;\n    for (var renderer of renderers) {\n      if (shouldBreak) {\n        break;\n      }\n      if (renderer?.rendererConfig?.getInspectorDataForViewAtPoint != null) {\n        renderer.rendererConfig.getInspectorDataForViewAtPoint(inspectedView, locationX, locationY, viewData => {\n          if (viewData && viewData.hierarchy.length > 0) {\n            shouldBreak = callback(viewData);\n          }\n        });\n      }\n    }\n  }\n  var _default = exports.default = getInspectorDataForViewAtPoint;\n});", "lineCount": 35, "map": [[6, 2, 14, 0], [6, 6, 14, 6, "invariant"], [6, 15, 14, 15], [6, 18, 14, 18, "require"], [6, 25, 14, 25], [6, 26, 14, 25, "_dependencyMap"], [6, 40, 14, 25], [6, 56, 14, 37], [6, 57, 14, 38], [7, 2, 29, 0], [7, 6, 29, 6, "reactDevToolsHook"], [7, 23, 29, 23], [7, 26, 29, 26, "window"], [7, 32, 29, 32], [7, 33, 29, 33, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [7, 63, 29, 63], [8, 2, 30, 0, "invariant"], [8, 11, 30, 9], [8, 12, 31, 2, "Boolean"], [8, 19, 31, 9], [8, 20, 31, 10, "reactDevToolsHook"], [8, 37, 31, 27], [8, 38, 31, 28], [8, 40, 32, 2], [8, 130, 33, 0], [8, 131, 33, 1], [9, 2, 35, 0], [9, 6, 35, 6, "renderers"], [9, 15, 35, 37], [9, 18, 35, 40, "Array"], [9, 23, 35, 45], [9, 24, 35, 46, "from"], [9, 28, 35, 50], [9, 29, 36, 2, "window"], [9, 35, 36, 8], [9, 36, 36, 9, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [9, 66, 36, 39], [9, 67, 36, 40, "renderers"], [9, 76, 36, 49], [9, 77, 36, 50, "values"], [9, 83, 36, 56], [9, 84, 36, 57], [9, 85, 37, 0], [9, 86, 37, 1], [10, 2, 39, 0], [10, 6, 39, 6, "append<PERSON><PERSON><PERSON>"], [10, 20, 39, 20], [10, 23, 39, 23, "_ref"], [10, 27, 39, 23], [11, 4, 39, 23], [11, 8, 39, 25, "renderer"], [11, 16, 39, 33], [11, 19, 39, 33, "_ref"], [11, 23, 39, 33], [11, 24, 39, 25, "renderer"], [11, 32, 39, 33], [12, 4, 39, 33], [12, 11, 40, 2, "renderers"], [12, 20, 40, 11], [12, 21, 40, 12, "push"], [12, 25, 40, 16], [12, 26, 40, 17, "renderer"], [12, 34, 40, 25], [12, 35, 40, 26], [13, 2, 40, 26], [14, 2, 41, 0, "reactDevToolsHook"], [14, 19, 41, 17], [14, 20, 41, 18, "on"], [14, 22, 41, 20], [14, 23, 41, 21], [14, 33, 41, 31], [14, 35, 41, 33, "append<PERSON><PERSON><PERSON>"], [14, 49, 41, 47], [14, 50, 41, 48], [15, 2, 43, 0], [15, 11, 43, 9, "validateRenderers"], [15, 28, 43, 26, "validateRenderers"], [15, 29, 43, 26], [15, 31, 43, 35], [16, 4, 44, 2, "invariant"], [16, 13, 44, 11], [16, 14, 45, 4, "renderers"], [16, 23, 45, 13], [16, 24, 45, 14, "length"], [16, 30, 45, 20], [16, 33, 45, 23], [16, 34, 45, 24], [16, 36, 46, 4], [16, 107, 47, 2], [16, 108, 47, 3], [17, 2, 48, 0], [18, 2, 50, 0], [18, 11, 50, 9, "getInspectorDataForViewAtPoint"], [18, 41, 50, 39, "getInspectorDataForViewAtPoint"], [18, 42, 51, 2, "inspectedView"], [18, 55, 51, 30], [18, 57, 52, 2, "locationX"], [18, 66, 52, 19], [18, 68, 53, 2, "locationY"], [18, 77, 53, 19], [18, 79, 54, 2, "callback"], [18, 87, 54, 57], [18, 89, 55, 2], [19, 4, 56, 2, "validateRenderers"], [19, 21, 56, 19], [19, 22, 56, 20], [19, 23, 56, 21], [20, 4, 58, 2], [20, 8, 58, 6, "shouldBreak"], [20, 19, 58, 17], [20, 22, 58, 20], [20, 27, 58, 25], [21, 4, 60, 2], [21, 9, 60, 7], [21, 13, 60, 13, "renderer"], [21, 21, 60, 21], [21, 25, 60, 25, "renderers"], [21, 34, 60, 34], [21, 36, 60, 36], [22, 6, 61, 4], [22, 10, 61, 8, "shouldBreak"], [22, 21, 61, 19], [22, 23, 61, 21], [23, 8, 62, 6], [24, 6, 63, 4], [25, 6, 65, 4], [25, 10, 65, 8, "renderer"], [25, 18, 65, 16], [25, 20, 65, 18, "rendererConfig"], [25, 34, 65, 32], [25, 36, 65, 34, "getInspectorDataForViewAtPoint"], [25, 66, 65, 64], [25, 70, 65, 68], [25, 74, 65, 72], [25, 76, 65, 74], [26, 8, 66, 6, "renderer"], [26, 16, 66, 14], [26, 17, 66, 15, "rendererConfig"], [26, 31, 66, 29], [26, 32, 66, 30, "getInspectorDataForViewAtPoint"], [26, 62, 66, 60], [26, 63, 67, 8, "inspectedView"], [26, 76, 67, 21], [26, 78, 68, 8, "locationX"], [26, 87, 68, 17], [26, 89, 69, 8, "locationY"], [26, 98, 69, 17], [26, 100, 70, 8, "viewData"], [26, 108, 70, 16], [26, 112, 70, 20], [27, 10, 72, 10], [27, 14, 72, 14, "viewData"], [27, 22, 72, 22], [27, 26, 72, 26, "viewData"], [27, 34, 72, 34], [27, 35, 72, 35, "hierarchy"], [27, 44, 72, 44], [27, 45, 72, 45, "length"], [27, 51, 72, 51], [27, 54, 72, 54], [27, 55, 72, 55], [27, 57, 72, 57], [28, 12, 73, 12, "shouldBreak"], [28, 23, 73, 23], [28, 26, 73, 26, "callback"], [28, 34, 73, 34], [28, 35, 73, 35, "viewData"], [28, 43, 73, 43], [28, 44, 73, 44], [29, 10, 74, 10], [30, 8, 75, 8], [30, 9, 76, 6], [30, 10, 76, 7], [31, 6, 77, 4], [32, 4, 78, 2], [33, 2, 79, 0], [34, 2, 79, 1], [34, 6, 79, 1, "_default"], [34, 14, 79, 1], [34, 17, 79, 1, "exports"], [34, 24, 79, 1], [34, 25, 79, 1, "default"], [34, 32, 79, 1], [34, 35, 81, 15, "getInspectorDataForViewAtPoint"], [34, 65, 81, 45], [35, 0, 81, 45], [35, 3]], "functionMap": {"names": ["<global>", "append<PERSON><PERSON><PERSON>", "validateRenderers", "getInspectorDataForViewAtPoint", "renderer.rendererConfig.getInspectorDataForViewAtPoint$argument_3"], "mappings": "AAA;uBCsC;0BDC;AEG;CFK;AGE;QCoB;SDK;CHI"}}, "type": "js/module"}]}