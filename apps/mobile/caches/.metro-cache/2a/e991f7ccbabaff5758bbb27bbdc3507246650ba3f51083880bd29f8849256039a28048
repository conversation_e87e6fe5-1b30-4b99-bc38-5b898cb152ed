{"dependencies": [{"name": "../animation/styleAnimation.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 68, "index": 83}}], "key": "qOi2WxRW79aYVtfYlPwQR1HJTqY=", "exportNames": ["*"]}}, {"name": "../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 84}, "end": {"line": 4, "column": 56, "index": 140}}], "key": "9j6OaBzi0V5srVAX3iTMRrWOBnc=", "exportNames": ["*"]}}, {"name": "../mutables.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 141}, "end": {"line": 5, "column": 47, "index": 188}}], "key": "9pi6vw2h6bU/rhYqomiHRqMyoiQ=", "exportNames": ["*"]}}, {"name": "../threads.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 189}, "end": {"line": 6, "column": 51, "index": 240}}], "key": "K4CZCGtE2IjiBjBQzdc2uYfV4CM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _styleAnimation = require(_dependencyMap[0], \"../animation/styleAnimation.js\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes.js\");\n  var _mutables = require(_dependencyMap[2], \"../mutables.js\");\n  var _threads = require(_dependencyMap[3], \"../threads.js\");\n  const TAG_OFFSET = 1e9;\n  const _worklet_12903378677985_init_data = {\n    code: \"function startObservingProgress_reactNativeReanimated_animationsManagerJs1(tag,sharedValue,animationType){const{LayoutAnimationType,TAG_OFFSET}=this.__closure;const isSharedTransition=animationType===LayoutAnimationType.SHARED_ELEMENT_TRANSITION;sharedValue.addListener(tag+TAG_OFFSET,function(){global._notifyAboutProgress(tag,sharedValue.value,isSharedTransition);});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"startObservingProgress_reactNativeReanimated_animationsManagerJs1\\\",\\\"tag\\\",\\\"sharedValue\\\",\\\"animationType\\\",\\\"LayoutAnimationType\\\",\\\"TAG_OFFSET\\\",\\\"__closure\\\",\\\"isSharedTransition\\\",\\\"SHARED_ELEMENT_TRANSITION\\\",\\\"addListener\\\",\\\"global\\\",\\\"_notifyAboutProgress\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\\\"],\\\"mappings\\\":\\\"AAOA,SAAAA,iEAAiEA,CAAAC,GAAA,CAAAC,WAAA,CAAAC,aAAA,QAAAC,mBAAA,CAAAC,UAAA,OAAAC,SAAA,CAG/D,KAAM,CAAAC,kBAAkB,CAAGJ,aAAa,GAAKC,mBAAmB,CAACI,yBAAyB,CAC1FN,WAAW,CAACO,WAAW,CAACR,GAAG,CAAGI,UAAU,CAAE,UAAM,CAC9CK,MAAM,CAACC,oBAAoB,CAACV,GAAG,CAAEC,WAAW,CAACU,KAAK,CAAEL,kBAAkB,CAAC,CACzE,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const startObservingProgress = function () {\n    const _e = [new global.Error(), -3, -27];\n    const startObservingProgress = function (tag, sharedValue, animationType) {\n      const isSharedTransition = animationType === _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION;\n      sharedValue.addListener(tag + TAG_OFFSET, () => {\n        global._notifyAboutProgress(tag, sharedValue.value, isSharedTransition);\n      });\n    };\n    startObservingProgress.__closure = {\n      LayoutAnimationType: _commonTypes.LayoutAnimationType,\n      TAG_OFFSET\n    };\n    startObservingProgress.__workletHash = 12903378677985;\n    startObservingProgress.__initData = _worklet_12903378677985_init_data;\n    startObservingProgress.__stackDetails = _e;\n    return startObservingProgress;\n  }();\n  const _worklet_7600341837897_init_data = {\n    code: \"function stopObservingProgress_reactNativeReanimated_animationsManagerJs2(tag,sharedValue,removeView=false){const{TAG_OFFSET}=this.__closure;sharedValue.removeListener(tag+TAG_OFFSET);global._notifyAboutEnd(tag,removeView);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"stopObservingProgress_reactNativeReanimated_animationsManagerJs2\\\",\\\"tag\\\",\\\"sharedValue\\\",\\\"removeView\\\",\\\"TAG_OFFSET\\\",\\\"__closure\\\",\\\"removeListener\\\",\\\"global\\\",\\\"_notifyAboutEnd\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\\\"],\\\"mappings\\\":\\\"AAeA,SAAAA,gEAAqEA,CAAAC,GAAA,CAAAC,WAAA,CAAAC,UAAA,cAAAC,UAAA,OAAAC,SAAA,CAGnEH,WAAW,CAACI,cAAc,CAACL,GAAG,CAAGG,UAAU,CAAC,CAC5CG,MAAM,CAACC,eAAe,CAACP,GAAG,CAAEE,UAAU,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const stopObservingProgress = function () {\n    const _e = [new global.Error(), -2, -27];\n    const stopObservingProgress = function (tag, sharedValue, removeView = false) {\n      sharedValue.removeListener(tag + TAG_OFFSET);\n      global._notifyAboutEnd(tag, removeView);\n    };\n    stopObservingProgress.__closure = {\n      TAG_OFFSET\n    };\n    stopObservingProgress.__workletHash = 7600341837897;\n    stopObservingProgress.__initData = _worklet_7600341837897_init_data;\n    stopObservingProgress.__stackDetails = _e;\n    return stopObservingProgress;\n  }();\n  const _worklet_16265842309113_init_data = {\n    code: \"function createLayoutAnimationManager_reactNativeReanimated_animationsManagerJs3(){const{LayoutAnimationType,makeMutableUI,stopObservingProgress,withStyleAnimation,startObservingProgress}=this.__closure;const currentAnimationForTag=new Map();const mutableValuesForTag=new Map();return{start:function(tag,type,yogaValues,config){if(type===LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS){global.ProgressTransitionRegister.onTransitionStart(tag,yogaValues);return;}const style=config(yogaValues);let currentAnimation=style.animations;const previousAnimation=currentAnimationForTag.get(tag);if(previousAnimation){currentAnimation={...previousAnimation,...style.animations};}currentAnimationForTag.set(tag,currentAnimation);let value=mutableValuesForTag.get(tag);if(value===undefined){value=makeMutableUI(style.initialValues);mutableValuesForTag.set(tag,value);}else{stopObservingProgress(tag,value);value._value=style.initialValues;}const animation=withStyleAnimation(currentAnimation);animation.callback=function(finished){if(finished){currentAnimationForTag.delete(tag);mutableValuesForTag.delete(tag);const shouldRemoveView=type===LayoutAnimationType.EXITING;stopObservingProgress(tag,value,shouldRemoveView);}style.callback&&style.callback(finished===undefined?false:finished);};startObservingProgress(tag,value,type);value.value=animation;},stop:function(tag){const value=mutableValuesForTag.get(tag);if(!value){return;}stopObservingProgress(tag,value);}};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createLayoutAnimationManager_reactNativeReanimated_animationsManagerJs3\\\",\\\"LayoutAnimationType\\\",\\\"makeMutableUI\\\",\\\"stopObservingProgress\\\",\\\"withStyleAnimation\\\",\\\"startObservingProgress\\\",\\\"__closure\\\",\\\"currentAnimationForTag\\\",\\\"Map\\\",\\\"mutableValuesForTag\\\",\\\"start\\\",\\\"tag\\\",\\\"type\\\",\\\"yogaValues\\\",\\\"config\\\",\\\"SHARED_ELEMENT_TRANSITION_PROGRESS\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"onTransitionStart\\\",\\\"style\\\",\\\"currentAnimation\\\",\\\"animations\\\",\\\"previousAnimation\\\",\\\"get\\\",\\\"set\\\",\\\"value\\\",\\\"undefined\\\",\\\"initialValues\\\",\\\"_value\\\",\\\"animation\\\",\\\"callback\\\",\\\"finished\\\",\\\"delete\\\",\\\"shouldRemoveView\\\",\\\"EXITING\\\",\\\"stop\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\\\"],\\\"mappings\\\":\\\"AAqBA,SAAAA,uEAAwCA,CAAA,QAAAC,mBAAA,CAAAC,aAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,OAAAC,SAAA,CAGtC,KAAM,CAAAC,sBAAsB,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACxC,KAAM,CAAAC,mBAAmB,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CACrC,MAAO,CACLE,KAAK,SAAAA,CAACC,GAAG,CAAEC,IAAI,CAKfC,UAAU,CAAEC,MAAM,CAAE,CAClB,GAAIF,IAAI,GAAKX,mBAAmB,CAACc,kCAAkC,CAAE,CACnEC,MAAM,CAACC,0BAA0B,CAACC,iBAAiB,CAACP,GAAG,CAAEE,UAAU,CAAC,CACpE,OACF,CACA,KAAM,CAAAM,KAAK,CAAGL,MAAM,CAACD,UAAU,CAAC,CAChC,GAAI,CAAAO,gBAAgB,CAAGD,KAAK,CAACE,UAAU,CAIvC,KAAM,CAAAC,iBAAiB,CAAGf,sBAAsB,CAACgB,GAAG,CAACZ,GAAG,CAAC,CACzD,GAAIW,iBAAiB,CAAE,CACrBF,gBAAgB,CAAG,CACjB,GAAGE,iBAAiB,CACpB,GAAGH,KAAK,CAACE,UACX,CAAC,CACH,CACAd,sBAAsB,CAACiB,GAAG,CAACb,GAAG,CAAES,gBAAgB,CAAC,CACjD,GAAI,CAAAK,KAAK,CAAGhB,mBAAmB,CAACc,GAAG,CAACZ,GAAG,CAAC,CACxC,GAAIc,KAAK,GAAKC,SAAS,CAAE,CACvBD,KAAK,CAAGvB,aAAa,CAACiB,KAAK,CAACQ,aAAa,CAAC,CAC1ClB,mBAAmB,CAACe,GAAG,CAACb,GAAG,CAAEc,KAAK,CAAC,CACrC,CAAC,IAAM,CACLtB,qBAAqB,CAACQ,GAAG,CAAEc,KAAK,CAAC,CACjCA,KAAK,CAACG,MAAM,CAAGT,KAAK,CAACQ,aAAa,CACpC,CAGA,KAAM,CAAAE,SAAS,CAAGzB,kBAAkB,CAACgB,gBAAgB,CAAC,CACtDS,SAAS,CAACC,QAAQ,CAAG,SAAAC,QAAQ,CAAI,CAC/B,GAAIA,QAAQ,CAAE,CACZxB,sBAAsB,CAACyB,MAAM,CAACrB,GAAG,CAAC,CAClCF,mBAAmB,CAACuB,MAAM,CAACrB,GAAG,CAAC,CAC/B,KAAM,CAAAsB,gBAAgB,CAAGrB,IAAI,GAAKX,mBAAmB,CAACiC,OAAO,CAC7D/B,qBAAqB,CAACQ,GAAG,CAAEc,KAAK,CAAEQ,gBAAgB,CAAC,CACrD,CACAd,KAAK,CAACW,QAAQ,EAAIX,KAAK,CAACW,QAAQ,CAACC,QAAQ,GAAKL,SAAS,CAAG,KAAK,CAAGK,QAAQ,CAAC,CAC7E,CAAC,CACD1B,sBAAsB,CAACM,GAAG,CAAEc,KAAK,CAAEb,IAAI,CAAC,CACxCa,KAAK,CAACA,KAAK,CAAGI,SAAS,CACzB,CAAC,CACDM,IAAI,SAAAA,CAACxB,GAAG,CAAE,CACR,KAAM,CAAAc,KAAK,CAAGhB,mBAAmB,CAACc,GAAG,CAACZ,GAAG,CAAC,CAC1C,GAAI,CAACc,KAAK,CAAE,CACV,OACF,CACAtB,qBAAqB,CAACQ,GAAG,CAAEc,KAAK,CAAC,CACnC,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createLayoutAnimationManager = function () {\n    const _e = [new global.Error(), -6, -27];\n    const createLayoutAnimationManager = function () {\n      const currentAnimationForTag = new Map();\n      const mutableValuesForTag = new Map();\n      return {\n        start(tag, type,\n        /**\n         * CreateLayoutAnimationManager creates an animation manager for both\n         * Layout animations and Shared Transition Elements animations.\n         */\n        yogaValues, config) {\n          if (type === _commonTypes.LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS) {\n            global.ProgressTransitionRegister.onTransitionStart(tag, yogaValues);\n            return;\n          }\n          const style = config(yogaValues);\n          let currentAnimation = style.animations;\n\n          // When layout animation is requested, but a previous one is still running, we merge\n          // new layout animation targets into the ongoing animation\n          const previousAnimation = currentAnimationForTag.get(tag);\n          if (previousAnimation) {\n            currentAnimation = {\n              ...previousAnimation,\n              ...style.animations\n            };\n          }\n          currentAnimationForTag.set(tag, currentAnimation);\n          let value = mutableValuesForTag.get(tag);\n          if (value === undefined) {\n            value = (0, _mutables.makeMutableUI)(style.initialValues);\n            mutableValuesForTag.set(tag, value);\n          } else {\n            stopObservingProgress(tag, value);\n            value._value = style.initialValues;\n          }\n\n          // @ts-ignore The line below started failing because I added types to the method – don't have time to fix it right now\n          const animation = (0, _styleAnimation.withStyleAnimation)(currentAnimation);\n          animation.callback = finished => {\n            if (finished) {\n              currentAnimationForTag.delete(tag);\n              mutableValuesForTag.delete(tag);\n              const shouldRemoveView = type === _commonTypes.LayoutAnimationType.EXITING;\n              stopObservingProgress(tag, value, shouldRemoveView);\n            }\n            style.callback && style.callback(finished === undefined ? false : finished);\n          };\n          startObservingProgress(tag, value, type);\n          value.value = animation;\n        },\n        stop(tag) {\n          const value = mutableValuesForTag.get(tag);\n          if (!value) {\n            return;\n          }\n          stopObservingProgress(tag, value);\n        }\n      };\n    };\n    createLayoutAnimationManager.__closure = {\n      LayoutAnimationType: _commonTypes.LayoutAnimationType,\n      makeMutableUI: _mutables.makeMutableUI,\n      stopObservingProgress,\n      withStyleAnimation: _styleAnimation.withStyleAnimation,\n      startObservingProgress\n    };\n    createLayoutAnimationManager.__workletHash = 16265842309113;\n    createLayoutAnimationManager.__initData = _worklet_16265842309113_init_data;\n    createLayoutAnimationManager.__stackDetails = _e;\n    return createLayoutAnimationManager;\n  }();\n  const _worklet_12746540537052_init_data = {\n    code: \"function reactNativeReanimated_animationsManagerJs4(){const{createLayoutAnimationManager}=this.__closure;global.LayoutAnimationsManager=createLayoutAnimationManager();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_animationsManagerJs4\\\",\\\"createLayoutAnimationManager\\\",\\\"__closure\\\",\\\"global\\\",\\\"LayoutAnimationsManager\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/layoutReanimation/animationsManager.js\\\"],\\\"mappings\\\":\\\"AAkFmB,SAAAA,0CAAMA,CAAA,QAAAC,4BAAA,OAAAC,SAAA,CAGvBC,MAAM,CAACC,uBAAuB,CAAGH,4BAA4B,CAAC,CAAC,CACjE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  (0, _threads.runOnUIImmediately)(function () {\n    const _e = [new global.Error(), -2, -27];\n    const reactNativeReanimated_animationsManagerJs4 = function () {\n      global.LayoutAnimationsManager = createLayoutAnimationManager();\n    };\n    reactNativeReanimated_animationsManagerJs4.__closure = {\n      createLayoutAnimationManager\n    };\n    reactNativeReanimated_animationsManagerJs4.__workletHash = 12746540537052;\n    reactNativeReanimated_animationsManagerJs4.__initData = _worklet_12746540537052_init_data;\n    reactNativeReanimated_animationsManagerJs4.__stackDetails = _e;\n    return reactNativeReanimated_animationsManagerJs4;\n  }())();\n});", "lineCount": 150, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [4, 6, 3, 0, "_styleAnimation"], [4, 21, 3, 0], [4, 24, 3, 0, "require"], [4, 31, 3, 0], [4, 32, 3, 0, "_dependencyMap"], [4, 46, 3, 0], [5, 2, 4, 0], [5, 6, 4, 0, "_commonTypes"], [5, 18, 4, 0], [5, 21, 4, 0, "require"], [5, 28, 4, 0], [5, 29, 4, 0, "_dependencyMap"], [5, 43, 4, 0], [6, 2, 5, 0], [6, 6, 5, 0, "_mutables"], [6, 15, 5, 0], [6, 18, 5, 0, "require"], [6, 25, 5, 0], [6, 26, 5, 0, "_dependencyMap"], [6, 40, 5, 0], [7, 2, 6, 0], [7, 6, 6, 0, "_threads"], [7, 14, 6, 0], [7, 17, 6, 0, "require"], [7, 24, 6, 0], [7, 25, 6, 0, "_dependencyMap"], [7, 39, 6, 0], [8, 2, 7, 0], [8, 8, 7, 6, "TAG_OFFSET"], [8, 18, 7, 16], [8, 21, 7, 19], [8, 24, 7, 22], [9, 2, 7, 23], [9, 8, 7, 23, "_worklet_12903378677985_init_data"], [9, 41, 7, 23], [10, 4, 7, 23, "code"], [10, 8, 7, 23], [11, 4, 7, 23, "location"], [11, 12, 7, 23], [12, 4, 7, 23, "sourceMap"], [12, 13, 7, 23], [13, 4, 7, 23, "version"], [13, 11, 7, 23], [14, 2, 7, 23], [15, 2, 7, 23], [15, 8, 7, 23, "startObservingProgress"], [15, 30, 7, 23], [15, 33, 8, 0], [16, 4, 8, 0], [16, 10, 8, 0, "_e"], [16, 12, 8, 0], [16, 20, 8, 0, "global"], [16, 26, 8, 0], [16, 27, 8, 0, "Error"], [16, 32, 8, 0], [17, 4, 8, 0], [17, 10, 8, 0, "startObservingProgress"], [17, 32, 8, 0], [17, 44, 8, 0, "startObservingProgress"], [17, 45, 8, 32, "tag"], [17, 48, 8, 35], [17, 50, 8, 37, "sharedValue"], [17, 61, 8, 48], [17, 63, 8, 50, "animationType"], [17, 76, 8, 63], [17, 78, 8, 65], [18, 6, 11, 2], [18, 12, 11, 8, "isSharedTransition"], [18, 30, 11, 26], [18, 33, 11, 29, "animationType"], [18, 46, 11, 42], [18, 51, 11, 47, "LayoutAnimationType"], [18, 83, 11, 66], [18, 84, 11, 67, "SHARED_ELEMENT_TRANSITION"], [18, 109, 11, 92], [19, 6, 12, 2, "sharedValue"], [19, 17, 12, 13], [19, 18, 12, 14, "addListener"], [19, 29, 12, 25], [19, 30, 12, 26, "tag"], [19, 33, 12, 29], [19, 36, 12, 32, "TAG_OFFSET"], [19, 46, 12, 42], [19, 48, 12, 44], [19, 54, 12, 50], [20, 8, 13, 4, "global"], [20, 14, 13, 10], [20, 15, 13, 11, "_notifyAboutProgress"], [20, 35, 13, 31], [20, 36, 13, 32, "tag"], [20, 39, 13, 35], [20, 41, 13, 37, "sharedValue"], [20, 52, 13, 48], [20, 53, 13, 49, "value"], [20, 58, 13, 54], [20, 60, 13, 56, "isSharedTransition"], [20, 78, 13, 74], [20, 79, 13, 75], [21, 6, 14, 2], [21, 7, 14, 3], [21, 8, 14, 4], [22, 4, 15, 0], [22, 5, 15, 1], [23, 4, 15, 1, "startObservingProgress"], [23, 26, 15, 1], [23, 27, 15, 1, "__closure"], [23, 36, 15, 1], [24, 6, 15, 1, "LayoutAnimationType"], [24, 25, 15, 1], [24, 27, 11, 47, "LayoutAnimationType"], [24, 59, 11, 66], [25, 6, 11, 66, "TAG_OFFSET"], [26, 4, 11, 66], [27, 4, 11, 66, "startObservingProgress"], [27, 26, 11, 66], [27, 27, 11, 66, "__workletHash"], [27, 40, 11, 66], [28, 4, 11, 66, "startObservingProgress"], [28, 26, 11, 66], [28, 27, 11, 66, "__initData"], [28, 37, 11, 66], [28, 40, 11, 66, "_worklet_12903378677985_init_data"], [28, 73, 11, 66], [29, 4, 11, 66, "startObservingProgress"], [29, 26, 11, 66], [29, 27, 11, 66, "__stackDetails"], [29, 41, 11, 66], [29, 44, 11, 66, "_e"], [29, 46, 11, 66], [30, 4, 11, 66], [30, 11, 11, 66, "startObservingProgress"], [30, 33, 11, 66], [31, 2, 11, 66], [31, 3, 8, 0], [32, 2, 8, 0], [32, 8, 8, 0, "_worklet_7600341837897_init_data"], [32, 40, 8, 0], [33, 4, 8, 0, "code"], [33, 8, 8, 0], [34, 4, 8, 0, "location"], [34, 12, 8, 0], [35, 4, 8, 0, "sourceMap"], [35, 13, 8, 0], [36, 4, 8, 0, "version"], [36, 11, 8, 0], [37, 2, 8, 0], [38, 2, 8, 0], [38, 8, 8, 0, "stopObservingProgress"], [38, 29, 8, 0], [38, 32, 16, 0], [39, 4, 16, 0], [39, 10, 16, 0, "_e"], [39, 12, 16, 0], [39, 20, 16, 0, "global"], [39, 26, 16, 0], [39, 27, 16, 0, "Error"], [39, 32, 16, 0], [40, 4, 16, 0], [40, 10, 16, 0, "stopObservingProgress"], [40, 31, 16, 0], [40, 43, 16, 0, "stopObservingProgress"], [40, 44, 16, 31, "tag"], [40, 47, 16, 34], [40, 49, 16, 36, "sharedValue"], [40, 60, 16, 47], [40, 62, 16, 49, "<PERSON><PERSON><PERSON><PERSON>"], [40, 72, 16, 59], [40, 75, 16, 62], [40, 80, 16, 67], [40, 82, 16, 69], [41, 6, 19, 2, "sharedValue"], [41, 17, 19, 13], [41, 18, 19, 14, "removeListener"], [41, 32, 19, 28], [41, 33, 19, 29, "tag"], [41, 36, 19, 32], [41, 39, 19, 35, "TAG_OFFSET"], [41, 49, 19, 45], [41, 50, 19, 46], [42, 6, 20, 2, "global"], [42, 12, 20, 8], [42, 13, 20, 9, "_notifyAboutEnd"], [42, 28, 20, 24], [42, 29, 20, 25, "tag"], [42, 32, 20, 28], [42, 34, 20, 30, "<PERSON><PERSON><PERSON><PERSON>"], [42, 44, 20, 40], [42, 45, 20, 41], [43, 4, 21, 0], [43, 5, 21, 1], [44, 4, 21, 1, "stopObservingProgress"], [44, 25, 21, 1], [44, 26, 21, 1, "__closure"], [44, 35, 21, 1], [45, 6, 21, 1, "TAG_OFFSET"], [46, 4, 21, 1], [47, 4, 21, 1, "stopObservingProgress"], [47, 25, 21, 1], [47, 26, 21, 1, "__workletHash"], [47, 39, 21, 1], [48, 4, 21, 1, "stopObservingProgress"], [48, 25, 21, 1], [48, 26, 21, 1, "__initData"], [48, 36, 21, 1], [48, 39, 21, 1, "_worklet_7600341837897_init_data"], [48, 71, 21, 1], [49, 4, 21, 1, "stopObservingProgress"], [49, 25, 21, 1], [49, 26, 21, 1, "__stackDetails"], [49, 40, 21, 1], [49, 43, 21, 1, "_e"], [49, 45, 21, 1], [50, 4, 21, 1], [50, 11, 21, 1, "stopObservingProgress"], [50, 32, 21, 1], [51, 2, 21, 1], [51, 3, 16, 0], [52, 2, 16, 0], [52, 8, 16, 0, "_worklet_16265842309113_init_data"], [52, 41, 16, 0], [53, 4, 16, 0, "code"], [53, 8, 16, 0], [54, 4, 16, 0, "location"], [54, 12, 16, 0], [55, 4, 16, 0, "sourceMap"], [55, 13, 16, 0], [56, 4, 16, 0, "version"], [56, 11, 16, 0], [57, 2, 16, 0], [58, 2, 16, 0], [58, 8, 16, 0, "createLayoutAnimationManager"], [58, 36, 16, 0], [58, 39, 22, 0], [59, 4, 22, 0], [59, 10, 22, 0, "_e"], [59, 12, 22, 0], [59, 20, 22, 0, "global"], [59, 26, 22, 0], [59, 27, 22, 0, "Error"], [59, 32, 22, 0], [60, 4, 22, 0], [60, 10, 22, 0, "createLayoutAnimationManager"], [60, 38, 22, 0], [60, 50, 22, 0, "createLayoutAnimationManager"], [60, 51, 22, 0], [60, 53, 22, 40], [61, 6, 25, 2], [61, 12, 25, 8, "currentAnimationForTag"], [61, 34, 25, 30], [61, 37, 25, 33], [61, 41, 25, 37, "Map"], [61, 44, 25, 40], [61, 45, 25, 41], [61, 46, 25, 42], [62, 6, 26, 2], [62, 12, 26, 8, "mutableValuesForTag"], [62, 31, 26, 27], [62, 34, 26, 30], [62, 38, 26, 34, "Map"], [62, 41, 26, 37], [62, 42, 26, 38], [62, 43, 26, 39], [63, 6, 27, 2], [63, 13, 27, 9], [64, 8, 28, 4, "start"], [64, 13, 28, 9, "start"], [64, 14, 28, 10, "tag"], [64, 17, 28, 13], [64, 19, 28, 15, "type"], [64, 23, 28, 19], [65, 8, 29, 4], [66, 0, 30, 0], [67, 0, 31, 0], [68, 0, 32, 0], [69, 8, 33, 4, "yoga<PERSON><PERSON><PERSON>"], [69, 18, 33, 14], [69, 20, 33, 16, "config"], [69, 26, 33, 22], [69, 28, 33, 24], [70, 10, 34, 6], [70, 14, 34, 10, "type"], [70, 18, 34, 14], [70, 23, 34, 19, "LayoutAnimationType"], [70, 55, 34, 38], [70, 56, 34, 39, "SHARED_ELEMENT_TRANSITION_PROGRESS"], [70, 90, 34, 73], [70, 92, 34, 75], [71, 12, 35, 8, "global"], [71, 18, 35, 14], [71, 19, 35, 15, "ProgressTransitionRegister"], [71, 45, 35, 41], [71, 46, 35, 42, "onTransitionStart"], [71, 63, 35, 59], [71, 64, 35, 60, "tag"], [71, 67, 35, 63], [71, 69, 35, 65, "yoga<PERSON><PERSON><PERSON>"], [71, 79, 35, 75], [71, 80, 35, 76], [72, 12, 36, 8], [73, 10, 37, 6], [74, 10, 38, 6], [74, 16, 38, 12, "style"], [74, 21, 38, 17], [74, 24, 38, 20, "config"], [74, 30, 38, 26], [74, 31, 38, 27, "yoga<PERSON><PERSON><PERSON>"], [74, 41, 38, 37], [74, 42, 38, 38], [75, 10, 39, 6], [75, 14, 39, 10, "currentAnimation"], [75, 30, 39, 26], [75, 33, 39, 29, "style"], [75, 38, 39, 34], [75, 39, 39, 35, "animations"], [75, 49, 39, 45], [77, 10, 41, 6], [78, 10, 42, 6], [79, 10, 43, 6], [79, 16, 43, 12, "previousAnimation"], [79, 33, 43, 29], [79, 36, 43, 32, "currentAnimationForTag"], [79, 58, 43, 54], [79, 59, 43, 55, "get"], [79, 62, 43, 58], [79, 63, 43, 59, "tag"], [79, 66, 43, 62], [79, 67, 43, 63], [80, 10, 44, 6], [80, 14, 44, 10, "previousAnimation"], [80, 31, 44, 27], [80, 33, 44, 29], [81, 12, 45, 8, "currentAnimation"], [81, 28, 45, 24], [81, 31, 45, 27], [82, 14, 46, 10], [82, 17, 46, 13, "previousAnimation"], [82, 34, 46, 30], [83, 14, 47, 10], [83, 17, 47, 13, "style"], [83, 22, 47, 18], [83, 23, 47, 19, "animations"], [84, 12, 48, 8], [84, 13, 48, 9], [85, 10, 49, 6], [86, 10, 50, 6, "currentAnimationForTag"], [86, 32, 50, 28], [86, 33, 50, 29, "set"], [86, 36, 50, 32], [86, 37, 50, 33, "tag"], [86, 40, 50, 36], [86, 42, 50, 38, "currentAnimation"], [86, 58, 50, 54], [86, 59, 50, 55], [87, 10, 51, 6], [87, 14, 51, 10, "value"], [87, 19, 51, 15], [87, 22, 51, 18, "mutableValuesForTag"], [87, 41, 51, 37], [87, 42, 51, 38, "get"], [87, 45, 51, 41], [87, 46, 51, 42, "tag"], [87, 49, 51, 45], [87, 50, 51, 46], [88, 10, 52, 6], [88, 14, 52, 10, "value"], [88, 19, 52, 15], [88, 24, 52, 20, "undefined"], [88, 33, 52, 29], [88, 35, 52, 31], [89, 12, 53, 8, "value"], [89, 17, 53, 13], [89, 20, 53, 16], [89, 24, 53, 16, "makeMutableUI"], [89, 47, 53, 29], [89, 49, 53, 30, "style"], [89, 54, 53, 35], [89, 55, 53, 36, "initialValues"], [89, 68, 53, 49], [89, 69, 53, 50], [90, 12, 54, 8, "mutableValuesForTag"], [90, 31, 54, 27], [90, 32, 54, 28, "set"], [90, 35, 54, 31], [90, 36, 54, 32, "tag"], [90, 39, 54, 35], [90, 41, 54, 37, "value"], [90, 46, 54, 42], [90, 47, 54, 43], [91, 10, 55, 6], [91, 11, 55, 7], [91, 17, 55, 13], [92, 12, 56, 8, "stopObservingProgress"], [92, 33, 56, 29], [92, 34, 56, 30, "tag"], [92, 37, 56, 33], [92, 39, 56, 35, "value"], [92, 44, 56, 40], [92, 45, 56, 41], [93, 12, 57, 8, "value"], [93, 17, 57, 13], [93, 18, 57, 14, "_value"], [93, 24, 57, 20], [93, 27, 57, 23, "style"], [93, 32, 57, 28], [93, 33, 57, 29, "initialValues"], [93, 46, 57, 42], [94, 10, 58, 6], [96, 10, 60, 6], [97, 10, 61, 6], [97, 16, 61, 12, "animation"], [97, 25, 61, 21], [97, 28, 61, 24], [97, 32, 61, 24, "withStyleAnimation"], [97, 66, 61, 42], [97, 68, 61, 43, "currentAnimation"], [97, 84, 61, 59], [97, 85, 61, 60], [98, 10, 62, 6, "animation"], [98, 19, 62, 15], [98, 20, 62, 16, "callback"], [98, 28, 62, 24], [98, 31, 62, 27, "finished"], [98, 39, 62, 35], [98, 43, 62, 39], [99, 12, 63, 8], [99, 16, 63, 12, "finished"], [99, 24, 63, 20], [99, 26, 63, 22], [100, 14, 64, 10, "currentAnimationForTag"], [100, 36, 64, 32], [100, 37, 64, 33, "delete"], [100, 43, 64, 39], [100, 44, 64, 40, "tag"], [100, 47, 64, 43], [100, 48, 64, 44], [101, 14, 65, 10, "mutableValuesForTag"], [101, 33, 65, 29], [101, 34, 65, 30, "delete"], [101, 40, 65, 36], [101, 41, 65, 37, "tag"], [101, 44, 65, 40], [101, 45, 65, 41], [102, 14, 66, 10], [102, 20, 66, 16, "shouldRemoveView"], [102, 36, 66, 32], [102, 39, 66, 35, "type"], [102, 43, 66, 39], [102, 48, 66, 44, "LayoutAnimationType"], [102, 80, 66, 63], [102, 81, 66, 64, "EXITING"], [102, 88, 66, 71], [103, 14, 67, 10, "stopObservingProgress"], [103, 35, 67, 31], [103, 36, 67, 32, "tag"], [103, 39, 67, 35], [103, 41, 67, 37, "value"], [103, 46, 67, 42], [103, 48, 67, 44, "shouldRemoveView"], [103, 64, 67, 60], [103, 65, 67, 61], [104, 12, 68, 8], [105, 12, 69, 8, "style"], [105, 17, 69, 13], [105, 18, 69, 14, "callback"], [105, 26, 69, 22], [105, 30, 69, 26, "style"], [105, 35, 69, 31], [105, 36, 69, 32, "callback"], [105, 44, 69, 40], [105, 45, 69, 41, "finished"], [105, 53, 69, 49], [105, 58, 69, 54, "undefined"], [105, 67, 69, 63], [105, 70, 69, 66], [105, 75, 69, 71], [105, 78, 69, 74, "finished"], [105, 86, 69, 82], [105, 87, 69, 83], [106, 10, 70, 6], [106, 11, 70, 7], [107, 10, 71, 6, "startObservingProgress"], [107, 32, 71, 28], [107, 33, 71, 29, "tag"], [107, 36, 71, 32], [107, 38, 71, 34, "value"], [107, 43, 71, 39], [107, 45, 71, 41, "type"], [107, 49, 71, 45], [107, 50, 71, 46], [108, 10, 72, 6, "value"], [108, 15, 72, 11], [108, 16, 72, 12, "value"], [108, 21, 72, 17], [108, 24, 72, 20, "animation"], [108, 33, 72, 29], [109, 8, 73, 4], [109, 9, 73, 5], [110, 8, 74, 4, "stop"], [110, 12, 74, 8, "stop"], [110, 13, 74, 9, "tag"], [110, 16, 74, 12], [110, 18, 74, 14], [111, 10, 75, 6], [111, 16, 75, 12, "value"], [111, 21, 75, 17], [111, 24, 75, 20, "mutableValuesForTag"], [111, 43, 75, 39], [111, 44, 75, 40, "get"], [111, 47, 75, 43], [111, 48, 75, 44, "tag"], [111, 51, 75, 47], [111, 52, 75, 48], [112, 10, 76, 6], [112, 14, 76, 10], [112, 15, 76, 11, "value"], [112, 20, 76, 16], [112, 22, 76, 18], [113, 12, 77, 8], [114, 10, 78, 6], [115, 10, 79, 6, "stopObservingProgress"], [115, 31, 79, 27], [115, 32, 79, 28, "tag"], [115, 35, 79, 31], [115, 37, 79, 33, "value"], [115, 42, 79, 38], [115, 43, 79, 39], [116, 8, 80, 4], [117, 6, 81, 2], [117, 7, 81, 3], [118, 4, 82, 0], [118, 5, 82, 1], [119, 4, 82, 1, "createLayoutAnimationManager"], [119, 32, 82, 1], [119, 33, 82, 1, "__closure"], [119, 42, 82, 1], [120, 6, 82, 1, "LayoutAnimationType"], [120, 25, 82, 1], [120, 27, 34, 19, "LayoutAnimationType"], [120, 59, 34, 38], [121, 6, 34, 38, "makeMutableUI"], [121, 19, 34, 38], [121, 21, 53, 16, "makeMutableUI"], [121, 44, 53, 29], [122, 6, 53, 29, "stopObservingProgress"], [122, 27, 53, 29], [123, 6, 53, 29, "withStyleAnimation"], [123, 24, 53, 29], [123, 26, 61, 24, "withStyleAnimation"], [123, 60, 61, 42], [124, 6, 61, 42, "startObservingProgress"], [125, 4, 61, 42], [126, 4, 61, 42, "createLayoutAnimationManager"], [126, 32, 61, 42], [126, 33, 61, 42, "__workletHash"], [126, 46, 61, 42], [127, 4, 61, 42, "createLayoutAnimationManager"], [127, 32, 61, 42], [127, 33, 61, 42, "__initData"], [127, 43, 61, 42], [127, 46, 61, 42, "_worklet_16265842309113_init_data"], [127, 79, 61, 42], [128, 4, 61, 42, "createLayoutAnimationManager"], [128, 32, 61, 42], [128, 33, 61, 42, "__stackDetails"], [128, 47, 61, 42], [128, 50, 61, 42, "_e"], [128, 52, 61, 42], [129, 4, 61, 42], [129, 11, 61, 42, "createLayoutAnimationManager"], [129, 39, 61, 42], [130, 2, 61, 42], [130, 3, 22, 0], [131, 2, 22, 0], [131, 8, 22, 0, "_worklet_12746540537052_init_data"], [131, 41, 22, 0], [132, 4, 22, 0, "code"], [132, 8, 22, 0], [133, 4, 22, 0, "location"], [133, 12, 22, 0], [134, 4, 22, 0, "sourceMap"], [134, 13, 22, 0], [135, 4, 22, 0, "version"], [135, 11, 22, 0], [136, 2, 22, 0], [137, 2, 83, 0], [137, 6, 83, 0, "runOnUIImmediately"], [137, 33, 83, 18], [137, 35, 83, 19], [138, 4, 83, 19], [138, 10, 83, 19, "_e"], [138, 12, 83, 19], [138, 20, 83, 19, "global"], [138, 26, 83, 19], [138, 27, 83, 19, "Error"], [138, 32, 83, 19], [139, 4, 83, 19], [139, 10, 83, 19, "reactNativeReanimated_animationsManagerJs4"], [139, 52, 83, 19], [139, 64, 83, 19, "reactNativeReanimated_animationsManagerJs4"], [139, 65, 83, 19], [139, 67, 83, 25], [140, 6, 86, 2, "global"], [140, 12, 86, 8], [140, 13, 86, 9, "LayoutAnimationsManager"], [140, 36, 86, 32], [140, 39, 86, 35, "createLayoutAnimationManager"], [140, 67, 86, 63], [140, 68, 86, 64], [140, 69, 86, 65], [141, 4, 87, 0], [141, 5, 87, 1], [142, 4, 87, 1, "reactNativeReanimated_animationsManagerJs4"], [142, 46, 87, 1], [142, 47, 87, 1, "__closure"], [142, 56, 87, 1], [143, 6, 87, 1, "createLayoutAnimationManager"], [144, 4, 87, 1], [145, 4, 87, 1, "reactNativeReanimated_animationsManagerJs4"], [145, 46, 87, 1], [145, 47, 87, 1, "__workletHash"], [145, 60, 87, 1], [146, 4, 87, 1, "reactNativeReanimated_animationsManagerJs4"], [146, 46, 87, 1], [146, 47, 87, 1, "__initData"], [146, 57, 87, 1], [146, 60, 87, 1, "_worklet_12746540537052_init_data"], [146, 93, 87, 1], [147, 4, 87, 1, "reactNativeReanimated_animationsManagerJs4"], [147, 46, 87, 1], [147, 47, 87, 1, "__stackDetails"], [147, 61, 87, 1], [147, 64, 87, 1, "_e"], [147, 66, 87, 1], [148, 4, 87, 1], [148, 11, 87, 1, "reactNativeReanimated_animationsManagerJs4"], [148, 53, 87, 1], [149, 2, 87, 1], [149, 3, 83, 19], [149, 5, 87, 1], [149, 6, 87, 2], [149, 7, 87, 3], [149, 8, 87, 4], [150, 0, 87, 5], [150, 3]], "functionMap": {"names": ["<global>", "startObservingProgress", "sharedValue.addListener$argument_1", "stopObservingProgress", "createLayoutAnimationManager", "start", "animation.callback", "stop", "runOnUIImmediately$argument_0"], "mappings": "AAA;ACO;4CCI;GDE;CDC;AGC;CHK;AIC;ICM;2BCkC;ODQ;KDG;IGC;KHM;CJE;mBQC;CRI"}}, "type": "js/module"}]}