{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Settings/Settings", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 60}}], "key": "z+3T1pfbjMHxm/+fjy23BoBinp8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _Settings = _interopRequireDefault(require(_dependencyMap[1], \"../../../Libraries/Settings/Settings\"));\n  var GLOBAL_HOOK_SETTINGS = 'ReactDevTools::HookSettings';\n  var ReactDevToolsSettingsManager = {\n    setGlobalHookSettings(settings) {\n      _Settings.default.set({\n        [GLOBAL_HOOK_SETTINGS]: settings\n      });\n    },\n    getGlobalHookSettings() {\n      var value = _Settings.default.get(GLOBAL_HOOK_SETTINGS);\n      if (typeof value === 'string') {\n        return value;\n      }\n      return null;\n    }\n  };\n  module.exports = ReactDevToolsSettingsManager;\n});", "lineCount": 20, "map": [[3, 2, 11, 0], [3, 6, 11, 0, "_Settings"], [3, 15, 11, 0], [3, 18, 11, 0, "_interopRequireDefault"], [3, 40, 11, 0], [3, 41, 11, 0, "require"], [3, 48, 11, 0], [3, 49, 11, 0, "_dependencyMap"], [3, 63, 11, 0], [4, 2, 13, 0], [4, 6, 13, 6, "GLOBAL_HOOK_SETTINGS"], [4, 26, 13, 26], [4, 29, 13, 29], [4, 58, 13, 58], [5, 2, 15, 0], [5, 6, 15, 6, "ReactDevToolsSettingsManager"], [5, 34, 15, 34], [5, 37, 15, 37], [6, 4, 16, 2, "setGlobalHookSettings"], [6, 25, 16, 23, "setGlobalHookSettings"], [6, 26, 16, 24, "settings"], [6, 34, 16, 40], [6, 36, 16, 48], [7, 6, 17, 4, "Settings"], [7, 23, 17, 12], [7, 24, 17, 13, "set"], [7, 27, 17, 16], [7, 28, 17, 17], [8, 8, 18, 6], [8, 9, 18, 7, "GLOBAL_HOOK_SETTINGS"], [8, 29, 18, 27], [8, 32, 18, 30, "settings"], [9, 6, 19, 4], [9, 7, 19, 5], [9, 8, 19, 6], [10, 4, 20, 2], [10, 5, 20, 3], [11, 4, 21, 2, "getGlobalHookSettings"], [11, 25, 21, 23, "getGlobalHookSettings"], [11, 26, 21, 23], [11, 28, 21, 35], [12, 6, 22, 4], [12, 10, 22, 10, "value"], [12, 15, 22, 15], [12, 18, 22, 18, "Settings"], [12, 35, 22, 26], [12, 36, 22, 27, "get"], [12, 39, 22, 30], [12, 40, 22, 31, "GLOBAL_HOOK_SETTINGS"], [12, 60, 22, 51], [12, 61, 22, 52], [13, 6, 23, 4], [13, 10, 23, 8], [13, 17, 23, 15, "value"], [13, 22, 23, 20], [13, 27, 23, 25], [13, 35, 23, 33], [13, 37, 23, 35], [14, 8, 24, 6], [14, 15, 24, 13, "value"], [14, 20, 24, 18], [15, 6, 25, 4], [16, 6, 26, 4], [16, 13, 26, 11], [16, 17, 26, 15], [17, 4, 27, 2], [18, 2, 28, 0], [18, 3, 28, 1], [19, 2, 30, 0, "module"], [19, 8, 30, 6], [19, 9, 30, 7, "exports"], [19, 16, 30, 14], [19, 19, 30, 17, "ReactDevToolsSettingsManager"], [19, 47, 30, 45], [20, 0, 30, 46], [20, 3]], "functionMap": {"names": ["<global>", "setGlobalHookSettings", "getGlobalHookSettings"], "mappings": "AAA;ECe;GDI;EEC;GFM"}}, "type": "js/module"}]}