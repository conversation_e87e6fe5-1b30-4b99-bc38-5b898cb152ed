{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Tent = exports.default = (0, _createLucideIcon.default)(\"Tent\", [[\"path\", {\n    d: \"M3.5 21 14 3\",\n    key: \"1szst5\"\n  }], [\"path\", {\n    d: \"M20.5 21 10 3\",\n    key: \"1310c3\"\n  }], [\"path\", {\n    d: \"M15.5 21 12 15l-3.5 6\",\n    key: \"1ddtfw\"\n  }], [\"path\", {\n    d: \"M2 21h20\",\n    key: \"1nyx9w\"\n  }]]);\n});", "lineCount": 28, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Tent"], [15, 10, 10, 10], [15, 13, 10, 10, "exports"], [15, 20, 10, 10], [15, 21, 10, 10, "default"], [15, 28, 10, 10], [15, 31, 10, 13], [15, 35, 10, 13, "createLucideIcon"], [15, 60, 10, 29], [15, 62, 10, 30], [15, 68, 10, 36], [15, 70, 10, 38], [15, 71, 11, 2], [15, 72, 11, 3], [15, 78, 11, 9], [15, 80, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 21, 11, 30], [17, 4, 11, 32, "key"], [17, 7, 11, 35], [17, 9, 11, 37], [18, 2, 11, 46], [18, 3, 11, 47], [18, 4, 11, 48], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 22, 12, 31], [20, 4, 12, 33, "key"], [20, 7, 12, 36], [20, 9, 12, 38], [21, 2, 12, 47], [21, 3, 12, 48], [21, 4, 12, 49], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 30, 13, 39], [23, 4, 13, 41, "key"], [23, 7, 13, 44], [23, 9, 13, 46], [24, 2, 13, 55], [24, 3, 13, 56], [24, 4, 13, 57], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 17, 14, 26], [26, 4, 14, 28, "key"], [26, 7, 14, 31], [26, 9, 14, 33], [27, 2, 14, 42], [27, 3, 14, 43], [27, 4, 14, 44], [27, 5, 15, 1], [27, 6, 15, 2], [28, 0, 15, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}