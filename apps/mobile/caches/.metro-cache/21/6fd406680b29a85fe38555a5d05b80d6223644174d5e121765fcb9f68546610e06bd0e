{"dependencies": [{"name": "../../../Easing", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 41, "index": 55}}], "key": "8UcGBXFzMyir2f1D5kEA6v4hNhA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JumpingTransition = JumpingTransition;\n  var _Easing = require(_dependencyMap[0], \"../../../Easing\");\n  function JumpingTransition(name, transitionData) {\n    var translateX = transitionData.translateX,\n      translateY = transitionData.translateY,\n      scaleX = transitionData.scaleX,\n      scaleY = transitionData.scaleY;\n    var d = Math.max(Math.abs(translateX), Math.abs(translateY)) / 2;\n    var peakTranslateY = translateY <= 0 ? translateY - d : -translateY + d;\n    var jumpingTransition = {\n      name,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }],\n          easing: _Easing.Easing.exp\n        },\n        50: {\n          transform: [{\n            translateX: `${translateX / 2}px`,\n            translateY: `${peakTranslateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: '1,1'\n          }]\n        }\n      },\n      duration: 300\n    };\n    return jumpingTransition;\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "JumpingTransition"], [7, 27, 1, 13], [7, 30, 1, 13, "JumpingTransition"], [7, 47, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_Easing"], [8, 13, 2, 0], [8, 16, 2, 0, "require"], [8, 23, 2, 0], [8, 24, 2, 0, "_dependencyMap"], [8, 38, 2, 0], [9, 2, 5, 7], [9, 11, 5, 16, "JumpingTransition"], [9, 28, 5, 33, "JumpingTransition"], [9, 29, 6, 2, "name"], [9, 33, 6, 14], [9, 35, 7, 2, "transitionData"], [9, 49, 7, 32], [9, 51, 8, 2], [10, 4, 9, 2], [10, 8, 9, 10, "translateX"], [10, 18, 9, 20], [10, 21, 9, 53, "transitionData"], [10, 35, 9, 67], [10, 36, 9, 10, "translateX"], [10, 46, 9, 20], [11, 6, 9, 22, "translateY"], [11, 16, 9, 32], [11, 19, 9, 53, "transitionData"], [11, 33, 9, 67], [11, 34, 9, 22, "translateY"], [11, 44, 9, 32], [12, 6, 9, 34, "scaleX"], [12, 12, 9, 40], [12, 15, 9, 53, "transitionData"], [12, 29, 9, 67], [12, 30, 9, 34, "scaleX"], [12, 36, 9, 40], [13, 6, 9, 42, "scaleY"], [13, 12, 9, 48], [13, 15, 9, 53, "transitionData"], [13, 29, 9, 67], [13, 30, 9, 42, "scaleY"], [13, 36, 9, 48], [14, 4, 11, 2], [14, 8, 11, 8, "d"], [14, 9, 11, 9], [14, 12, 11, 12, "Math"], [14, 16, 11, 16], [14, 17, 11, 17, "max"], [14, 20, 11, 20], [14, 21, 11, 21, "Math"], [14, 25, 11, 25], [14, 26, 11, 26, "abs"], [14, 29, 11, 29], [14, 30, 11, 30, "translateX"], [14, 40, 11, 40], [14, 41, 11, 41], [14, 43, 11, 43, "Math"], [14, 47, 11, 47], [14, 48, 11, 48, "abs"], [14, 51, 11, 51], [14, 52, 11, 52, "translateY"], [14, 62, 11, 62], [14, 63, 11, 63], [14, 64, 11, 64], [14, 67, 11, 67], [14, 68, 11, 68], [15, 4, 12, 2], [15, 8, 12, 8, "peakTranslateY"], [15, 22, 12, 22], [15, 25, 12, 25, "translateY"], [15, 35, 12, 35], [15, 39, 12, 39], [15, 40, 12, 40], [15, 43, 12, 43, "translateY"], [15, 53, 12, 53], [15, 56, 12, 56, "d"], [15, 57, 12, 57], [15, 60, 12, 60], [15, 61, 12, 61, "translateY"], [15, 71, 12, 71], [15, 74, 12, 74, "d"], [15, 75, 12, 75], [16, 4, 14, 2], [16, 8, 14, 8, "jumpingTransition"], [16, 25, 14, 25], [16, 28, 14, 28], [17, 6, 15, 4, "name"], [17, 10, 15, 8], [18, 6, 16, 4, "style"], [18, 11, 16, 9], [18, 13, 16, 11], [19, 8, 17, 6], [19, 9, 17, 7], [19, 11, 17, 9], [20, 10, 18, 8, "transform"], [20, 19, 18, 17], [20, 21, 18, 19], [20, 22, 19, 10], [21, 12, 20, 12, "translateX"], [21, 22, 20, 22], [21, 24, 20, 24], [21, 27, 20, 27, "translateX"], [21, 37, 20, 37], [21, 41, 20, 41], [22, 12, 21, 12, "translateY"], [22, 22, 21, 22], [22, 24, 21, 24], [22, 27, 21, 27, "translateY"], [22, 37, 21, 37], [22, 41, 21, 41], [23, 12, 22, 12, "scale"], [23, 17, 22, 17], [23, 19, 22, 19], [23, 22, 22, 22, "scaleX"], [23, 28, 22, 28], [23, 32, 22, 32, "scaleY"], [23, 38, 22, 38], [24, 10, 23, 10], [24, 11, 23, 11], [24, 12, 24, 9], [25, 10, 25, 8, "easing"], [25, 16, 25, 14], [25, 18, 25, 16, "Easing"], [25, 32, 25, 22], [25, 33, 25, 23, "exp"], [26, 8, 26, 6], [26, 9, 26, 7], [27, 8, 27, 6], [27, 10, 27, 8], [27, 12, 27, 10], [28, 10, 28, 8, "transform"], [28, 19, 28, 17], [28, 21, 28, 19], [28, 22, 29, 10], [29, 12, 30, 12, "translateX"], [29, 22, 30, 22], [29, 24, 30, 24], [29, 27, 30, 27, "translateX"], [29, 37, 30, 37], [29, 40, 30, 40], [29, 41, 30, 41], [29, 45, 30, 45], [30, 12, 31, 12, "translateY"], [30, 22, 31, 22], [30, 24, 31, 24], [30, 27, 31, 27, "peakTranslateY"], [30, 41, 31, 41], [30, 45, 31, 45], [31, 12, 32, 12, "scale"], [31, 17, 32, 17], [31, 19, 32, 19], [31, 22, 32, 22, "scaleX"], [31, 28, 32, 28], [31, 32, 32, 32, "scaleY"], [31, 38, 32, 38], [32, 10, 33, 10], [32, 11, 33, 11], [33, 8, 35, 6], [33, 9, 35, 7], [34, 8, 36, 6], [34, 11, 36, 9], [34, 13, 36, 11], [35, 10, 37, 8, "transform"], [35, 19, 37, 17], [35, 21, 37, 19], [35, 22, 37, 20], [36, 12, 37, 22, "translateX"], [36, 22, 37, 32], [36, 24, 37, 34], [36, 29, 37, 39], [37, 12, 37, 41, "translateY"], [37, 22, 37, 51], [37, 24, 37, 53], [37, 29, 37, 58], [38, 12, 37, 60, "scale"], [38, 17, 37, 65], [38, 19, 37, 67], [39, 10, 37, 73], [39, 11, 37, 74], [40, 8, 38, 6], [41, 6, 39, 4], [41, 7, 39, 5], [42, 6, 40, 4, "duration"], [42, 14, 40, 12], [42, 16, 40, 14], [43, 4, 41, 2], [43, 5, 41, 3], [44, 4, 43, 2], [44, 11, 43, 9, "jumpingTransition"], [44, 28, 43, 26], [45, 2, 44, 0], [46, 0, 44, 1], [46, 3]], "functionMap": {"names": ["<global>", "JumpingTransition"], "mappings": "AAA;OCI;CDuC"}}, "type": "js/module"}]}