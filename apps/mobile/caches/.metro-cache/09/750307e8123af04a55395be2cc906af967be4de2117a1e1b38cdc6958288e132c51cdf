{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 36}, "end": {"line": 4, "column": 31, "index": 67}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "@tanstack/query-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 68}, "end": {"line": 10, "column": 30, "index": 167}}], "key": "GAsr4MDTe9ve1mRxgvML4iY2BZg=", "exportNames": ["*"]}}, {"name": "./QueryClientProvider.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 168}, "end": {"line": 11, "column": 58, "index": 226}}], "key": "R2Cn87b3k6V9pZdY+hWd8Ft8C58=", "exportNames": ["*"]}}, {"name": "./IsRestoringProvider.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 227}, "end": {"line": 12, "column": 58, "index": 285}}], "key": "LsIgHmr91y5USktZ56HoA6qLNhc=", "exportNames": ["*"]}}, {"name": "./QueryErrorResetBoundary.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 286}, "end": {"line": 13, "column": 74, "index": 360}}], "key": "v0npNLmYOKsdEK0iJ4RVpe647XQ=", "exportNames": ["*"]}}, {"name": "./errorBoundaryUtils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 361}, "end": {"line": 18, "column": 33, "index": 482}}], "key": "vPVIYzX/U31yYyVDl4UaADFDRRQ=", "exportNames": ["*"]}}, {"name": "./suspense.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 483}, "end": {"line": 24, "column": 23, "index": 587}}], "key": "0Q+BFfol1+EurGmLVTwxCgLGwZ8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use client\";\n\n  // src/useQueries.ts\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useQueries = useQueries;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _queryCore = require(_dependencyMap[4], \"@tanstack/query-core\");\n  var _QueryClientProvider = require(_dependencyMap[5], \"./QueryClientProvider.js\");\n  var _IsRestoringProvider = require(_dependencyMap[6], \"./IsRestoringProvider.js\");\n  var _QueryErrorResetBoundary = require(_dependencyMap[7], \"./QueryErrorResetBoundary.js\");\n  var _errorBoundaryUtils = require(_dependencyMap[8], \"./errorBoundaryUtils.js\");\n  var _suspense = require(_dependencyMap[9], \"./suspense.js\");\n  var _excluded = [\"queries\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useQueries(_ref, queryClient) {\n    var queries = _ref.queries,\n      options = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var client = (0, _QueryClientProvider.useQueryClient)(queryClient);\n    var isRestoring = (0, _IsRestoringProvider.useIsRestoring)();\n    var errorResetBoundary = (0, _QueryErrorResetBoundary.useQueryErrorResetBoundary)();\n    var defaultedQueries = React.useMemo(() => queries.map(opts => {\n      var defaultedOptions = client.defaultQueryOptions(opts);\n      defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n      return defaultedOptions;\n    }), [queries, client, isRestoring]);\n    defaultedQueries.forEach(query => {\n      (0, _suspense.ensureSuspenseTimers)(query);\n      (0, _errorBoundaryUtils.ensurePreventErrorBoundaryRetry)(query, errorResetBoundary);\n    });\n    (0, _errorBoundaryUtils.useClearResetErrorBoundary)(errorResetBoundary);\n    var _React$useState = React.useState(() => new _queryCore.QueriesObserver(client, defaultedQueries, options)),\n      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 1),\n      observer = _React$useState2[0];\n    var _observer$getOptimist = observer.getOptimisticResult(defaultedQueries, options.combine),\n      _observer$getOptimist2 = (0, _slicedToArray2.default)(_observer$getOptimist, 3),\n      optimisticResult = _observer$getOptimist2[0],\n      getCombinedResult = _observer$getOptimist2[1],\n      trackResult = _observer$getOptimist2[2];\n    var shouldSubscribe = !isRestoring && options.subscribed !== false;\n    React.useSyncExternalStore(React.useCallback(onStoreChange => shouldSubscribe ? observer.subscribe(_queryCore.notifyManager.batchCalls(onStoreChange)) : _queryCore.noop, [observer, shouldSubscribe]), () => observer.getCurrentResult(), () => observer.getCurrentResult());\n    React.useEffect(() => {\n      observer.setQueries(defaultedQueries, options);\n    }, [defaultedQueries, options, observer]);\n    var shouldAtLeastOneSuspend = optimisticResult.some((result, index) => (0, _suspense.shouldSuspend)(defaultedQueries[index], result));\n    var suspensePromises = shouldAtLeastOneSuspend ? optimisticResult.flatMap((result, index) => {\n      var opts = defaultedQueries[index];\n      if (opts) {\n        var queryObserver = new _queryCore.QueryObserver(client, opts);\n        if ((0, _suspense.shouldSuspend)(opts, result)) {\n          return (0, _suspense.fetchOptimistic)(opts, queryObserver, errorResetBoundary);\n        } else if ((0, _suspense.willFetch)(result, isRestoring)) {\n          void (0, _suspense.fetchOptimistic)(opts, queryObserver, errorResetBoundary);\n        }\n      }\n      return [];\n    }) : [];\n    if (suspensePromises.length > 0) {\n      throw Promise.all(suspensePromises);\n    }\n    var firstSingleResultWhichShouldThrow = optimisticResult.find((result, index) => {\n      var query = defaultedQueries[index];\n      return query && (0, _errorBoundaryUtils.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: query.throwOnError,\n        query: client.getQueryCache().get(query.queryHash),\n        suspense: query.suspense\n      });\n    });\n    if (firstSingleResultWhichShouldThrow?.error) {\n      throw firstSingleResultWhichShouldThrow.error;\n    }\n    return getCombinedResult(trackResult());\n  }\n});", "lineCount": 81, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0], [5, 6, 3, 0, "_interopRequireDefault"], [5, 28, 3, 0], [5, 31, 3, 0, "require"], [5, 38, 3, 0], [5, 39, 3, 0, "_dependencyMap"], [5, 53, 3, 0], [6, 2, 3, 0, "Object"], [6, 8, 3, 0], [6, 9, 3, 0, "defineProperty"], [6, 23, 3, 0], [6, 24, 3, 0, "exports"], [6, 31, 3, 0], [7, 4, 3, 0, "value"], [7, 9, 3, 0], [8, 2, 3, 0], [9, 2, 3, 0, "exports"], [9, 9, 3, 0], [9, 10, 3, 0, "useQueries"], [9, 20, 3, 0], [9, 23, 3, 0, "useQueries"], [9, 33, 3, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_slicedToArray2"], [10, 21, 3, 0], [10, 24, 3, 0, "_interopRequireDefault"], [10, 46, 3, 0], [10, 47, 3, 0, "require"], [10, 54, 3, 0], [10, 55, 3, 0, "_dependencyMap"], [10, 69, 3, 0], [11, 2, 3, 0], [11, 6, 3, 0, "_objectWithoutProperties2"], [11, 31, 3, 0], [11, 34, 3, 0, "_interopRequireDefault"], [11, 56, 3, 0], [11, 57, 3, 0, "require"], [11, 64, 3, 0], [11, 65, 3, 0, "_dependencyMap"], [11, 79, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "React"], [12, 11, 4, 0], [12, 14, 4, 0, "_interopRequireWildcard"], [12, 37, 4, 0], [12, 38, 4, 0, "require"], [12, 45, 4, 0], [12, 46, 4, 0, "_dependencyMap"], [12, 60, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_queryCore"], [13, 16, 5, 0], [13, 19, 5, 0, "require"], [13, 26, 5, 0], [13, 27, 5, 0, "_dependencyMap"], [13, 41, 5, 0], [14, 2, 11, 0], [14, 6, 11, 0, "_QueryClientProvider"], [14, 26, 11, 0], [14, 29, 11, 0, "require"], [14, 36, 11, 0], [14, 37, 11, 0, "_dependencyMap"], [14, 51, 11, 0], [15, 2, 12, 0], [15, 6, 12, 0, "_IsRestoringProvider"], [15, 26, 12, 0], [15, 29, 12, 0, "require"], [15, 36, 12, 0], [15, 37, 12, 0, "_dependencyMap"], [15, 51, 12, 0], [16, 2, 13, 0], [16, 6, 13, 0, "_QueryErrorResetBoundary"], [16, 30, 13, 0], [16, 33, 13, 0, "require"], [16, 40, 13, 0], [16, 41, 13, 0, "_dependencyMap"], [16, 55, 13, 0], [17, 2, 14, 0], [17, 6, 14, 0, "_errorBoundaryUtils"], [17, 25, 14, 0], [17, 28, 14, 0, "require"], [17, 35, 14, 0], [17, 36, 14, 0, "_dependencyMap"], [17, 50, 14, 0], [18, 2, 19, 0], [18, 6, 19, 0, "_suspense"], [18, 15, 19, 0], [18, 18, 19, 0, "require"], [18, 25, 19, 0], [18, 26, 19, 0, "_dependencyMap"], [18, 40, 19, 0], [19, 2, 24, 23], [19, 6, 24, 23, "_excluded"], [19, 15, 24, 23], [20, 2, 24, 23], [20, 11, 24, 23, "_interopRequireWildcard"], [20, 35, 24, 23, "e"], [20, 36, 24, 23], [20, 38, 24, 23, "t"], [20, 39, 24, 23], [20, 68, 24, 23, "WeakMap"], [20, 75, 24, 23], [20, 81, 24, 23, "r"], [20, 82, 24, 23], [20, 89, 24, 23, "WeakMap"], [20, 96, 24, 23], [20, 100, 24, 23, "n"], [20, 101, 24, 23], [20, 108, 24, 23, "WeakMap"], [20, 115, 24, 23], [20, 127, 24, 23, "_interopRequireWildcard"], [20, 150, 24, 23], [20, 162, 24, 23, "_interopRequireWildcard"], [20, 163, 24, 23, "e"], [20, 164, 24, 23], [20, 166, 24, 23, "t"], [20, 167, 24, 23], [20, 176, 24, 23, "t"], [20, 177, 24, 23], [20, 181, 24, 23, "e"], [20, 182, 24, 23], [20, 186, 24, 23, "e"], [20, 187, 24, 23], [20, 188, 24, 23, "__esModule"], [20, 198, 24, 23], [20, 207, 24, 23, "e"], [20, 208, 24, 23], [20, 214, 24, 23, "o"], [20, 215, 24, 23], [20, 217, 24, 23, "i"], [20, 218, 24, 23], [20, 220, 24, 23, "f"], [20, 221, 24, 23], [20, 226, 24, 23, "__proto__"], [20, 235, 24, 23], [20, 243, 24, 23, "default"], [20, 250, 24, 23], [20, 252, 24, 23, "e"], [20, 253, 24, 23], [20, 270, 24, 23, "e"], [20, 271, 24, 23], [20, 294, 24, 23, "e"], [20, 295, 24, 23], [20, 320, 24, 23, "e"], [20, 321, 24, 23], [20, 330, 24, 23, "f"], [20, 331, 24, 23], [20, 337, 24, 23, "o"], [20, 338, 24, 23], [20, 341, 24, 23, "t"], [20, 342, 24, 23], [20, 345, 24, 23, "n"], [20, 346, 24, 23], [20, 349, 24, 23, "r"], [20, 350, 24, 23], [20, 358, 24, 23, "o"], [20, 359, 24, 23], [20, 360, 24, 23, "has"], [20, 363, 24, 23], [20, 364, 24, 23, "e"], [20, 365, 24, 23], [20, 375, 24, 23, "o"], [20, 376, 24, 23], [20, 377, 24, 23, "get"], [20, 380, 24, 23], [20, 381, 24, 23, "e"], [20, 382, 24, 23], [20, 385, 24, 23, "o"], [20, 386, 24, 23], [20, 387, 24, 23, "set"], [20, 390, 24, 23], [20, 391, 24, 23, "e"], [20, 392, 24, 23], [20, 394, 24, 23, "f"], [20, 395, 24, 23], [20, 409, 24, 23, "_t"], [20, 411, 24, 23], [20, 415, 24, 23, "e"], [20, 416, 24, 23], [20, 432, 24, 23, "_t"], [20, 434, 24, 23], [20, 441, 24, 23, "hasOwnProperty"], [20, 455, 24, 23], [20, 456, 24, 23, "call"], [20, 460, 24, 23], [20, 461, 24, 23, "e"], [20, 462, 24, 23], [20, 464, 24, 23, "_t"], [20, 466, 24, 23], [20, 473, 24, 23, "i"], [20, 474, 24, 23], [20, 478, 24, 23, "o"], [20, 479, 24, 23], [20, 482, 24, 23, "Object"], [20, 488, 24, 23], [20, 489, 24, 23, "defineProperty"], [20, 503, 24, 23], [20, 508, 24, 23, "Object"], [20, 514, 24, 23], [20, 515, 24, 23, "getOwnPropertyDescriptor"], [20, 539, 24, 23], [20, 540, 24, 23, "e"], [20, 541, 24, 23], [20, 543, 24, 23, "_t"], [20, 545, 24, 23], [20, 552, 24, 23, "i"], [20, 553, 24, 23], [20, 554, 24, 23, "get"], [20, 557, 24, 23], [20, 561, 24, 23, "i"], [20, 562, 24, 23], [20, 563, 24, 23, "set"], [20, 566, 24, 23], [20, 570, 24, 23, "o"], [20, 571, 24, 23], [20, 572, 24, 23, "f"], [20, 573, 24, 23], [20, 575, 24, 23, "_t"], [20, 577, 24, 23], [20, 579, 24, 23, "i"], [20, 580, 24, 23], [20, 584, 24, 23, "f"], [20, 585, 24, 23], [20, 586, 24, 23, "_t"], [20, 588, 24, 23], [20, 592, 24, 23, "e"], [20, 593, 24, 23], [20, 594, 24, 23, "_t"], [20, 596, 24, 23], [20, 607, 24, 23, "f"], [20, 608, 24, 23], [20, 613, 24, 23, "e"], [20, 614, 24, 23], [20, 616, 24, 23, "t"], [20, 617, 24, 23], [21, 2, 25, 0], [21, 11, 25, 9, "useQueries"], [21, 21, 25, 19, "useQueries"], [21, 22, 25, 19, "_ref"], [21, 26, 25, 19], [21, 28, 28, 3, "queryClient"], [21, 39, 28, 14], [21, 41, 28, 16], [22, 4, 28, 16], [22, 8, 26, 2, "queries"], [22, 15, 26, 9], [22, 18, 26, 9, "_ref"], [22, 22, 26, 9], [22, 23, 26, 2, "queries"], [22, 30, 26, 9], [23, 6, 27, 5, "options"], [23, 13, 27, 12], [23, 20, 27, 12, "_objectWithoutProperties2"], [23, 45, 27, 12], [23, 46, 27, 12, "default"], [23, 53, 27, 12], [23, 55, 27, 12, "_ref"], [23, 59, 27, 12], [23, 61, 27, 12, "_excluded"], [23, 70, 27, 12], [24, 4, 29, 2], [24, 8, 29, 8, "client"], [24, 14, 29, 14], [24, 17, 29, 17], [24, 21, 29, 17, "useQueryClient"], [24, 56, 29, 31], [24, 58, 29, 32, "queryClient"], [24, 69, 29, 43], [24, 70, 29, 44], [25, 4, 30, 2], [25, 8, 30, 8, "isRestoring"], [25, 19, 30, 19], [25, 22, 30, 22], [25, 26, 30, 22, "useIsRestoring"], [25, 61, 30, 36], [25, 63, 30, 37], [25, 64, 30, 38], [26, 4, 31, 2], [26, 8, 31, 8, "errorResetBoundary"], [26, 26, 31, 26], [26, 29, 31, 29], [26, 33, 31, 29, "useQueryErrorResetBoundary"], [26, 84, 31, 55], [26, 86, 31, 56], [26, 87, 31, 57], [27, 4, 32, 2], [27, 8, 32, 8, "defaultedQueries"], [27, 24, 32, 24], [27, 27, 32, 27, "React"], [27, 32, 32, 32], [27, 33, 32, 33, "useMemo"], [27, 40, 32, 40], [27, 41, 33, 4], [27, 47, 33, 10, "queries"], [27, 54, 33, 17], [27, 55, 33, 18, "map"], [27, 58, 33, 21], [27, 59, 33, 23, "opts"], [27, 63, 33, 27], [27, 67, 33, 32], [28, 6, 34, 6], [28, 10, 34, 12, "defaultedOptions"], [28, 26, 34, 28], [28, 29, 34, 31, "client"], [28, 35, 34, 37], [28, 36, 34, 38, "defaultQueryOptions"], [28, 55, 34, 57], [28, 56, 35, 8, "opts"], [28, 60, 36, 6], [28, 61, 36, 7], [29, 6, 37, 6, "defaultedOptions"], [29, 22, 37, 22], [29, 23, 37, 23, "_optimisticResults"], [29, 41, 37, 41], [29, 44, 37, 44, "isRestoring"], [29, 55, 37, 55], [29, 58, 37, 58], [29, 71, 37, 71], [29, 74, 37, 74], [29, 86, 37, 86], [30, 6, 38, 6], [30, 13, 38, 13, "defaultedOptions"], [30, 29, 38, 29], [31, 4, 39, 4], [31, 5, 39, 5], [31, 6, 39, 6], [31, 8, 40, 4], [31, 9, 40, 5, "queries"], [31, 16, 40, 12], [31, 18, 40, 14, "client"], [31, 24, 40, 20], [31, 26, 40, 22, "isRestoring"], [31, 37, 40, 33], [31, 38, 41, 2], [31, 39, 41, 3], [32, 4, 42, 2, "defaultedQueries"], [32, 20, 42, 18], [32, 21, 42, 19, "for<PERSON>ach"], [32, 28, 42, 26], [32, 29, 42, 28, "query"], [32, 34, 42, 33], [32, 38, 42, 38], [33, 6, 43, 4], [33, 10, 43, 4, "ensureSuspenseTimers"], [33, 40, 43, 24], [33, 42, 43, 25, "query"], [33, 47, 43, 30], [33, 48, 43, 31], [34, 6, 44, 4], [34, 10, 44, 4, "ensurePreventErrorBoundaryRetry"], [34, 61, 44, 35], [34, 63, 44, 36, "query"], [34, 68, 44, 41], [34, 70, 44, 43, "errorResetBoundary"], [34, 88, 44, 61], [34, 89, 44, 62], [35, 4, 45, 2], [35, 5, 45, 3], [35, 6, 45, 4], [36, 4, 46, 2], [36, 8, 46, 2, "useClearResetErrorBoundary"], [36, 54, 46, 28], [36, 56, 46, 29, "errorResetBoundary"], [36, 74, 46, 47], [36, 75, 46, 48], [37, 4, 47, 2], [37, 8, 47, 2, "_React$useState"], [37, 23, 47, 2], [37, 26, 47, 21, "React"], [37, 31, 47, 26], [37, 32, 47, 27, "useState"], [37, 40, 47, 35], [37, 41, 48, 4], [37, 47, 48, 10], [37, 51, 48, 14, "QueriesObserver"], [37, 77, 48, 29], [37, 78, 49, 6, "client"], [37, 84, 49, 12], [37, 86, 50, 6, "defaultedQueries"], [37, 102, 50, 22], [37, 104, 51, 6, "options"], [37, 111, 52, 4], [37, 112, 53, 2], [37, 113, 53, 3], [38, 6, 53, 3, "_React$useState2"], [38, 22, 53, 3], [38, 29, 53, 3, "_slicedToArray2"], [38, 44, 53, 3], [38, 45, 53, 3, "default"], [38, 52, 53, 3], [38, 54, 53, 3, "_React$useState"], [38, 69, 53, 3], [39, 6, 47, 9, "observer"], [39, 14, 47, 17], [39, 17, 47, 17, "_React$useState2"], [39, 33, 47, 17], [40, 4, 54, 2], [40, 8, 54, 2, "_observer$getOptimist"], [40, 29, 54, 2], [40, 32, 54, 61, "observer"], [40, 40, 54, 69], [40, 41, 54, 70, "getOptimisticResult"], [40, 60, 54, 89], [40, 61, 55, 4, "defaultedQueries"], [40, 77, 55, 20], [40, 79, 56, 4, "options"], [40, 86, 56, 11], [40, 87, 56, 12, "combine"], [40, 94, 57, 2], [40, 95, 57, 3], [41, 6, 57, 3, "_observer$getOptimist2"], [41, 28, 57, 3], [41, 35, 57, 3, "_slicedToArray2"], [41, 50, 57, 3], [41, 51, 57, 3, "default"], [41, 58, 57, 3], [41, 60, 57, 3, "_observer$getOptimist"], [41, 81, 57, 3], [42, 6, 54, 9, "optimisticResult"], [42, 22, 54, 25], [42, 25, 54, 25, "_observer$getOptimist2"], [42, 47, 54, 25], [43, 6, 54, 27, "getCombinedResult"], [43, 23, 54, 44], [43, 26, 54, 44, "_observer$getOptimist2"], [43, 48, 54, 44], [44, 6, 54, 46, "trackResult"], [44, 17, 54, 57], [44, 20, 54, 57, "_observer$getOptimist2"], [44, 42, 54, 57], [45, 4, 58, 2], [45, 8, 58, 8, "shouldSubscribe"], [45, 23, 58, 23], [45, 26, 58, 26], [45, 27, 58, 27, "isRestoring"], [45, 38, 58, 38], [45, 42, 58, 42, "options"], [45, 49, 58, 49], [45, 50, 58, 50, "subscribed"], [45, 60, 58, 60], [45, 65, 58, 65], [45, 70, 58, 70], [46, 4, 59, 2, "React"], [46, 9, 59, 7], [46, 10, 59, 8, "useSyncExternalStore"], [46, 30, 59, 28], [46, 31, 60, 4, "React"], [46, 36, 60, 9], [46, 37, 60, 10, "useCallback"], [46, 48, 60, 21], [46, 49, 61, 7, "onStoreChange"], [46, 62, 61, 20], [46, 66, 61, 25, "shouldSubscribe"], [46, 81, 61, 40], [46, 84, 61, 43, "observer"], [46, 92, 61, 51], [46, 93, 61, 52, "subscribe"], [46, 102, 61, 61], [46, 103, 61, 62, "notify<PERSON><PERSON>ger"], [46, 127, 61, 75], [46, 128, 61, 76, "batchCalls"], [46, 138, 61, 86], [46, 139, 61, 87, "onStoreChange"], [46, 152, 61, 100], [46, 153, 61, 101], [46, 154, 61, 102], [46, 157, 61, 105, "noop"], [46, 172, 61, 109], [46, 174, 62, 6], [46, 175, 62, 7, "observer"], [46, 183, 62, 15], [46, 185, 62, 17, "shouldSubscribe"], [46, 200, 62, 32], [46, 201, 63, 4], [46, 202, 63, 5], [46, 204, 64, 4], [46, 210, 64, 10, "observer"], [46, 218, 64, 18], [46, 219, 64, 19, "getCurrentResult"], [46, 235, 64, 35], [46, 236, 64, 36], [46, 237, 64, 37], [46, 239, 65, 4], [46, 245, 65, 10, "observer"], [46, 253, 65, 18], [46, 254, 65, 19, "getCurrentResult"], [46, 270, 65, 35], [46, 271, 65, 36], [46, 272, 66, 2], [46, 273, 66, 3], [47, 4, 67, 2, "React"], [47, 9, 67, 7], [47, 10, 67, 8, "useEffect"], [47, 19, 67, 17], [47, 20, 67, 18], [47, 26, 67, 24], [48, 6, 68, 4, "observer"], [48, 14, 68, 12], [48, 15, 68, 13, "setQueries"], [48, 25, 68, 23], [48, 26, 69, 6, "defaultedQueries"], [48, 42, 69, 22], [48, 44, 70, 6, "options"], [48, 51, 71, 4], [48, 52, 71, 5], [49, 4, 72, 2], [49, 5, 72, 3], [49, 7, 72, 5], [49, 8, 72, 6, "defaultedQueries"], [49, 24, 72, 22], [49, 26, 72, 24, "options"], [49, 33, 72, 31], [49, 35, 72, 33, "observer"], [49, 43, 72, 41], [49, 44, 72, 42], [49, 45, 72, 43], [50, 4, 73, 2], [50, 8, 73, 8, "shouldAtLeastOneSuspend"], [50, 31, 73, 31], [50, 34, 73, 34, "optimisticResult"], [50, 50, 73, 50], [50, 51, 73, 51, "some"], [50, 55, 73, 55], [50, 56, 74, 4], [50, 57, 74, 5, "result"], [50, 63, 74, 11], [50, 65, 74, 13, "index"], [50, 70, 74, 18], [50, 75, 74, 23], [50, 79, 74, 23, "shouldSuspend"], [50, 102, 74, 36], [50, 104, 74, 37, "defaultedQueries"], [50, 120, 74, 53], [50, 121, 74, 54, "index"], [50, 126, 74, 59], [50, 127, 74, 60], [50, 129, 74, 62, "result"], [50, 135, 74, 68], [50, 136, 75, 2], [50, 137, 75, 3], [51, 4, 76, 2], [51, 8, 76, 8, "suspensePromises"], [51, 24, 76, 24], [51, 27, 76, 27, "shouldAtLeastOneSuspend"], [51, 50, 76, 50], [51, 53, 76, 53, "optimisticResult"], [51, 69, 76, 69], [51, 70, 76, 70, "flatMap"], [51, 77, 76, 77], [51, 78, 76, 78], [51, 79, 76, 79, "result"], [51, 85, 76, 85], [51, 87, 76, 87, "index"], [51, 92, 76, 92], [51, 97, 76, 97], [52, 6, 77, 4], [52, 10, 77, 10, "opts"], [52, 14, 77, 14], [52, 17, 77, 17, "defaultedQueries"], [52, 33, 77, 33], [52, 34, 77, 34, "index"], [52, 39, 77, 39], [52, 40, 77, 40], [53, 6, 78, 4], [53, 10, 78, 8, "opts"], [53, 14, 78, 12], [53, 16, 78, 14], [54, 8, 79, 6], [54, 12, 79, 12, "queryObserver"], [54, 25, 79, 25], [54, 28, 79, 28], [54, 32, 79, 32, "QueryObserver"], [54, 56, 79, 45], [54, 57, 79, 46, "client"], [54, 63, 79, 52], [54, 65, 79, 54, "opts"], [54, 69, 79, 58], [54, 70, 79, 59], [55, 8, 80, 6], [55, 12, 80, 10], [55, 16, 80, 10, "shouldSuspend"], [55, 39, 80, 23], [55, 41, 80, 24, "opts"], [55, 45, 80, 28], [55, 47, 80, 30, "result"], [55, 53, 80, 36], [55, 54, 80, 37], [55, 56, 80, 39], [56, 10, 81, 8], [56, 17, 81, 15], [56, 21, 81, 15, "fetchOptimistic"], [56, 46, 81, 30], [56, 48, 81, 31, "opts"], [56, 52, 81, 35], [56, 54, 81, 37, "queryObserver"], [56, 67, 81, 50], [56, 69, 81, 52, "errorResetBoundary"], [56, 87, 81, 70], [56, 88, 81, 71], [57, 8, 82, 6], [57, 9, 82, 7], [57, 15, 82, 13], [57, 19, 82, 17], [57, 23, 82, 17, "<PERSON><PERSON><PERSON><PERSON>"], [57, 42, 82, 26], [57, 44, 82, 27, "result"], [57, 50, 82, 33], [57, 52, 82, 35, "isRestoring"], [57, 63, 82, 46], [57, 64, 82, 47], [57, 66, 82, 49], [58, 10, 83, 8], [58, 15, 83, 13], [58, 19, 83, 13, "fetchOptimistic"], [58, 44, 83, 28], [58, 46, 83, 29, "opts"], [58, 50, 83, 33], [58, 52, 83, 35, "queryObserver"], [58, 65, 83, 48], [58, 67, 83, 50, "errorResetBoundary"], [58, 85, 83, 68], [58, 86, 83, 69], [59, 8, 84, 6], [60, 6, 85, 4], [61, 6, 86, 4], [61, 13, 86, 11], [61, 15, 86, 13], [62, 4, 87, 2], [62, 5, 87, 3], [62, 6, 87, 4], [62, 9, 87, 7], [62, 11, 87, 9], [63, 4, 88, 2], [63, 8, 88, 6, "suspensePromises"], [63, 24, 88, 22], [63, 25, 88, 23, "length"], [63, 31, 88, 29], [63, 34, 88, 32], [63, 35, 88, 33], [63, 37, 88, 35], [64, 6, 89, 4], [64, 12, 89, 10, "Promise"], [64, 19, 89, 17], [64, 20, 89, 18, "all"], [64, 23, 89, 21], [64, 24, 89, 22, "suspensePromises"], [64, 40, 89, 38], [64, 41, 89, 39], [65, 4, 90, 2], [66, 4, 91, 2], [66, 8, 91, 8, "firstSingleResultWhichShouldThrow"], [66, 41, 91, 41], [66, 44, 91, 44, "optimisticResult"], [66, 60, 91, 60], [66, 61, 91, 61, "find"], [66, 65, 91, 65], [66, 66, 92, 4], [66, 67, 92, 5, "result"], [66, 73, 92, 11], [66, 75, 92, 13, "index"], [66, 80, 92, 18], [66, 85, 92, 23], [67, 6, 93, 6], [67, 10, 93, 12, "query"], [67, 15, 93, 17], [67, 18, 93, 20, "defaultedQueries"], [67, 34, 93, 36], [67, 35, 93, 37, "index"], [67, 40, 93, 42], [67, 41, 93, 43], [68, 6, 94, 6], [68, 13, 94, 13, "query"], [68, 18, 94, 18], [68, 22, 94, 22], [68, 26, 94, 22, "getHasError"], [68, 57, 94, 33], [68, 59, 94, 34], [69, 8, 95, 8, "result"], [69, 14, 95, 14], [70, 8, 96, 8, "errorResetBoundary"], [70, 26, 96, 26], [71, 8, 97, 8, "throwOnError"], [71, 20, 97, 20], [71, 22, 97, 22, "query"], [71, 27, 97, 27], [71, 28, 97, 28, "throwOnError"], [71, 40, 97, 40], [72, 8, 98, 8, "query"], [72, 13, 98, 13], [72, 15, 98, 15, "client"], [72, 21, 98, 21], [72, 22, 98, 22, "get<PERSON><PERSON><PERSON><PERSON>ache"], [72, 35, 98, 35], [72, 36, 98, 36], [72, 37, 98, 37], [72, 38, 98, 38, "get"], [72, 41, 98, 41], [72, 42, 98, 42, "query"], [72, 47, 98, 47], [72, 48, 98, 48, "queryHash"], [72, 57, 98, 57], [72, 58, 98, 58], [73, 8, 99, 8, "suspense"], [73, 16, 99, 16], [73, 18, 99, 18, "query"], [73, 23, 99, 23], [73, 24, 99, 24, "suspense"], [74, 6, 100, 6], [74, 7, 100, 7], [74, 8, 100, 8], [75, 4, 101, 4], [75, 5, 102, 2], [75, 6, 102, 3], [76, 4, 103, 2], [76, 8, 103, 6, "firstSingleResultWhichShouldThrow"], [76, 41, 103, 39], [76, 43, 103, 41, "error"], [76, 48, 103, 46], [76, 50, 103, 48], [77, 6, 104, 4], [77, 12, 104, 10, "firstSingleResultWhichShouldThrow"], [77, 45, 104, 43], [77, 46, 104, 44, "error"], [77, 51, 104, 49], [78, 4, 105, 2], [79, 4, 106, 2], [79, 11, 106, 9, "getCombinedResult"], [79, 28, 106, 26], [79, 29, 106, 27, "trackResult"], [79, 40, 106, 38], [79, 41, 106, 39], [79, 42, 106, 40], [79, 43, 106, 41], [80, 2, 107, 0], [81, 0, 107, 1], [81, 3]], "functionMap": {"names": ["<global>", "useQueries", "React.useMemo$argument_0", "queries.map$argument_0", "defaultedQueries.forEach$argument_0", "React.useState$argument_0", "React.useSyncExternalStore$argument_0", "React.useSyncExternalStore$argument_1", "React.useSyncExternalStore$argument_2", "React.useEffect$argument_0", "optimisticResult.some$argument_0", "optimisticResult.flatMap$argument_0", "optimisticResult.find$argument_0"], "mappings": "AAA;ACwB;ICQ,kBC;KDM,CD;2BGG;GHG;IIG;KJI;MKS,uGL;IMG,iCN;IOC,iCP;kBQE;GRK;ISE,iET;8EUE;GVW;IWK;KXS;CDM"}}, "type": "js/module"}]}