{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./useNavigation.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 51, "index": 98}}], "key": "QYUBGacr5qSJ4R+u3laZK0wRG3s=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFocusEffect = useFocusEffect;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _useNavigation = require(_dependencyMap[1], \"./useNavigation.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to run an effect in a focused screen, similar to `React.useEffect`.\n   * This can be used to perform side-effects such as fetching data or subscribing to events.\n   * The passed callback should be wrapped in `React.useCallback` to avoid running the effect too often.\n   *\n   * @param callback Memoized callback containing the effect, should optionally return a cleanup function.\n   */\n  function useFocusEffect(effect) {\n    var navigation = (0, _useNavigation.useNavigation)();\n\n    // eslint-disable-next-line prefer-rest-params\n    if (arguments[1] !== undefined) {\n      var message = \"You passed a second argument to 'useFocusEffect', but it only accepts one argument. \" + \"If you want to pass a dependency array, you can use 'React.useCallback':\\n\\n\" + 'useFocusEffect(\\n' + '  React.useCallback(() => {\\n' + '    // Your code here\\n' + '  }, [depA, depB])\\n' + ');\\n\\n' + 'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n      console.error(message);\n    }\n    React.useEffect(() => {\n      var isFocused = false;\n      var cleanup;\n      var callback = () => {\n        var destroy = effect();\n        if (destroy === undefined || typeof destroy === 'function') {\n          return destroy;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          var _message = 'An effect function must not return anything besides a function, which is used for clean-up.';\n          if (destroy === null) {\n            _message += \" You returned 'null'. If your effect does not require clean-up, return 'undefined' (or nothing).\";\n          } else if (typeof destroy.then === 'function') {\n            _message += \"\\n\\nIt looks like you wrote 'useFocusEffect(async () => ...)' or returned a Promise. \" + 'Instead, write the async function inside your effect ' + 'and call it immediately:\\n\\n' + 'useFocusEffect(\\n' + '  React.useCallback(() => {\\n' + '    async function fetchData() {\\n' + '      // You can await here\\n' + '      const response = await MyAPI.getData(someId);\\n' + '      // ...\\n' + '    }\\n\\n' + '    fetchData();\\n' + '  }, [someId])\\n' + ');\\n\\n' + 'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n          } else {\n            _message += ` You returned '${JSON.stringify(destroy)}'.`;\n          }\n          console.error(_message);\n        }\n      };\n\n      // We need to run the effect on initial render/dep changes if the screen is focused\n      if (navigation.isFocused()) {\n        cleanup = callback();\n        isFocused = true;\n      }\n      var unsubscribeFocus = navigation.addListener('focus', () => {\n        // If callback was already called for focus, avoid calling it again\n        // The focus event may also fire on initial render, so we guard against running the effect twice\n        if (isFocused) {\n          return;\n        }\n        if (cleanup !== undefined) {\n          cleanup();\n        }\n        cleanup = callback();\n        isFocused = true;\n      });\n      var unsubscribeBlur = navigation.addListener('blur', () => {\n        if (cleanup !== undefined) {\n          cleanup();\n        }\n        cleanup = undefined;\n        isFocused = false;\n      });\n      return () => {\n        if (cleanup !== undefined) {\n          cleanup();\n        }\n        unsubscribeFocus();\n        unsubscribeBlur();\n      };\n    }, [effect, navigation]);\n  }\n});", "lineCount": 80, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useFocusEffect"], [7, 24, 1, 13], [7, 27, 1, 13, "useFocusEffect"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_useNavigation"], [9, 20, 4, 0], [9, 23, 4, 0, "require"], [9, 30, 4, 0], [9, 31, 4, 0, "_dependencyMap"], [9, 45, 4, 0], [10, 2, 4, 51], [10, 11, 4, 51, "_interopRequireWildcard"], [10, 35, 4, 51, "e"], [10, 36, 4, 51], [10, 38, 4, 51, "t"], [10, 39, 4, 51], [10, 68, 4, 51, "WeakMap"], [10, 75, 4, 51], [10, 81, 4, 51, "r"], [10, 82, 4, 51], [10, 89, 4, 51, "WeakMap"], [10, 96, 4, 51], [10, 100, 4, 51, "n"], [10, 101, 4, 51], [10, 108, 4, 51, "WeakMap"], [10, 115, 4, 51], [10, 127, 4, 51, "_interopRequireWildcard"], [10, 150, 4, 51], [10, 162, 4, 51, "_interopRequireWildcard"], [10, 163, 4, 51, "e"], [10, 164, 4, 51], [10, 166, 4, 51, "t"], [10, 167, 4, 51], [10, 176, 4, 51, "t"], [10, 177, 4, 51], [10, 181, 4, 51, "e"], [10, 182, 4, 51], [10, 186, 4, 51, "e"], [10, 187, 4, 51], [10, 188, 4, 51, "__esModule"], [10, 198, 4, 51], [10, 207, 4, 51, "e"], [10, 208, 4, 51], [10, 214, 4, 51, "o"], [10, 215, 4, 51], [10, 217, 4, 51, "i"], [10, 218, 4, 51], [10, 220, 4, 51, "f"], [10, 221, 4, 51], [10, 226, 4, 51, "__proto__"], [10, 235, 4, 51], [10, 243, 4, 51, "default"], [10, 250, 4, 51], [10, 252, 4, 51, "e"], [10, 253, 4, 51], [10, 270, 4, 51, "e"], [10, 271, 4, 51], [10, 294, 4, 51, "e"], [10, 295, 4, 51], [10, 320, 4, 51, "e"], [10, 321, 4, 51], [10, 330, 4, 51, "f"], [10, 331, 4, 51], [10, 337, 4, 51, "o"], [10, 338, 4, 51], [10, 341, 4, 51, "t"], [10, 342, 4, 51], [10, 345, 4, 51, "n"], [10, 346, 4, 51], [10, 349, 4, 51, "r"], [10, 350, 4, 51], [10, 358, 4, 51, "o"], [10, 359, 4, 51], [10, 360, 4, 51, "has"], [10, 363, 4, 51], [10, 364, 4, 51, "e"], [10, 365, 4, 51], [10, 375, 4, 51, "o"], [10, 376, 4, 51], [10, 377, 4, 51, "get"], [10, 380, 4, 51], [10, 381, 4, 51, "e"], [10, 382, 4, 51], [10, 385, 4, 51, "o"], [10, 386, 4, 51], [10, 387, 4, 51, "set"], [10, 390, 4, 51], [10, 391, 4, 51, "e"], [10, 392, 4, 51], [10, 394, 4, 51, "f"], [10, 395, 4, 51], [10, 409, 4, 51, "_t"], [10, 411, 4, 51], [10, 415, 4, 51, "e"], [10, 416, 4, 51], [10, 432, 4, 51, "_t"], [10, 434, 4, 51], [10, 441, 4, 51, "hasOwnProperty"], [10, 455, 4, 51], [10, 456, 4, 51, "call"], [10, 460, 4, 51], [10, 461, 4, 51, "e"], [10, 462, 4, 51], [10, 464, 4, 51, "_t"], [10, 466, 4, 51], [10, 473, 4, 51, "i"], [10, 474, 4, 51], [10, 478, 4, 51, "o"], [10, 479, 4, 51], [10, 482, 4, 51, "Object"], [10, 488, 4, 51], [10, 489, 4, 51, "defineProperty"], [10, 503, 4, 51], [10, 508, 4, 51, "Object"], [10, 514, 4, 51], [10, 515, 4, 51, "getOwnPropertyDescriptor"], [10, 539, 4, 51], [10, 540, 4, 51, "e"], [10, 541, 4, 51], [10, 543, 4, 51, "_t"], [10, 545, 4, 51], [10, 552, 4, 51, "i"], [10, 553, 4, 51], [10, 554, 4, 51, "get"], [10, 557, 4, 51], [10, 561, 4, 51, "i"], [10, 562, 4, 51], [10, 563, 4, 51, "set"], [10, 566, 4, 51], [10, 570, 4, 51, "o"], [10, 571, 4, 51], [10, 572, 4, 51, "f"], [10, 573, 4, 51], [10, 575, 4, 51, "_t"], [10, 577, 4, 51], [10, 579, 4, 51, "i"], [10, 580, 4, 51], [10, 584, 4, 51, "f"], [10, 585, 4, 51], [10, 586, 4, 51, "_t"], [10, 588, 4, 51], [10, 592, 4, 51, "e"], [10, 593, 4, 51], [10, 594, 4, 51, "_t"], [10, 596, 4, 51], [10, 607, 4, 51, "f"], [10, 608, 4, 51], [10, 613, 4, 51, "e"], [10, 614, 4, 51], [10, 616, 4, 51, "t"], [10, 617, 4, 51], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 2, 12, 7], [18, 11, 12, 16, "useFocusEffect"], [18, 25, 12, 30, "useFocusEffect"], [18, 26, 12, 31, "effect"], [18, 32, 12, 37], [18, 34, 12, 39], [19, 4, 13, 2], [19, 8, 13, 8, "navigation"], [19, 18, 13, 18], [19, 21, 13, 21], [19, 25, 13, 21, "useNavigation"], [19, 53, 13, 34], [19, 55, 13, 35], [19, 56, 13, 36], [21, 4, 15, 2], [22, 4, 16, 2], [22, 8, 16, 6, "arguments"], [22, 17, 16, 15], [22, 18, 16, 16], [22, 19, 16, 17], [22, 20, 16, 18], [22, 25, 16, 23, "undefined"], [22, 34, 16, 32], [22, 36, 16, 34], [23, 6, 17, 4], [23, 10, 17, 10, "message"], [23, 17, 17, 17], [23, 20, 17, 20], [23, 106, 17, 106], [23, 109, 17, 109], [23, 187, 17, 187], [23, 190, 17, 190], [23, 209, 17, 209], [23, 212, 17, 212], [23, 243, 17, 243], [23, 246, 17, 246], [23, 271, 17, 271], [23, 274, 17, 274], [23, 296, 17, 296], [23, 299, 17, 299], [23, 307, 17, 307], [23, 310, 17, 310], [23, 378, 17, 378], [24, 6, 18, 4, "console"], [24, 13, 18, 11], [24, 14, 18, 12, "error"], [24, 19, 18, 17], [24, 20, 18, 18, "message"], [24, 27, 18, 25], [24, 28, 18, 26], [25, 4, 19, 2], [26, 4, 20, 2, "React"], [26, 9, 20, 7], [26, 10, 20, 8, "useEffect"], [26, 19, 20, 17], [26, 20, 20, 18], [26, 26, 20, 24], [27, 6, 21, 4], [27, 10, 21, 8, "isFocused"], [27, 19, 21, 17], [27, 22, 21, 20], [27, 27, 21, 25], [28, 6, 22, 4], [28, 10, 22, 8, "cleanup"], [28, 17, 22, 15], [29, 6, 23, 4], [29, 10, 23, 10, "callback"], [29, 18, 23, 18], [29, 21, 23, 21, "callback"], [29, 22, 23, 21], [29, 27, 23, 27], [30, 8, 24, 6], [30, 12, 24, 12, "destroy"], [30, 19, 24, 19], [30, 22, 24, 22, "effect"], [30, 28, 24, 28], [30, 29, 24, 29], [30, 30, 24, 30], [31, 8, 25, 6], [31, 12, 25, 10, "destroy"], [31, 19, 25, 17], [31, 24, 25, 22, "undefined"], [31, 33, 25, 31], [31, 37, 25, 35], [31, 44, 25, 42, "destroy"], [31, 51, 25, 49], [31, 56, 25, 54], [31, 66, 25, 64], [31, 68, 25, 66], [32, 10, 26, 8], [32, 17, 26, 15, "destroy"], [32, 24, 26, 22], [33, 8, 27, 6], [34, 8, 28, 6], [34, 12, 28, 10, "process"], [34, 19, 28, 17], [34, 20, 28, 18, "env"], [34, 23, 28, 21], [34, 24, 28, 22, "NODE_ENV"], [34, 32, 28, 30], [34, 37, 28, 35], [34, 49, 28, 47], [34, 51, 28, 49], [35, 10, 29, 8], [35, 14, 29, 12, "message"], [35, 22, 29, 19], [35, 25, 29, 22], [35, 118, 29, 115], [36, 10, 30, 8], [36, 14, 30, 12, "destroy"], [36, 21, 30, 19], [36, 26, 30, 24], [36, 30, 30, 28], [36, 32, 30, 30], [37, 12, 31, 10, "message"], [37, 20, 31, 17], [37, 24, 31, 21], [37, 122, 31, 119], [38, 10, 32, 8], [38, 11, 32, 9], [38, 17, 32, 15], [38, 21, 32, 19], [38, 28, 32, 26, "destroy"], [38, 35, 32, 33], [38, 36, 32, 34, "then"], [38, 40, 32, 38], [38, 45, 32, 43], [38, 55, 32, 53], [38, 57, 32, 55], [39, 12, 33, 10, "message"], [39, 20, 33, 17], [39, 24, 33, 21], [39, 111, 33, 108], [39, 114, 33, 111], [39, 169, 33, 166], [39, 172, 33, 169], [39, 202, 33, 199], [39, 205, 33, 202], [39, 224, 33, 221], [39, 227, 33, 224], [39, 258, 33, 255], [39, 261, 33, 258], [39, 297, 33, 294], [39, 300, 33, 297], [39, 331, 33, 328], [39, 334, 33, 331], [39, 389, 33, 386], [39, 392, 33, 389], [39, 408, 33, 405], [39, 411, 33, 408], [39, 422, 33, 419], [39, 425, 33, 422], [39, 445, 33, 442], [39, 448, 33, 445], [39, 466, 33, 463], [39, 469, 33, 466], [39, 477, 33, 474], [39, 480, 33, 477], [39, 548, 33, 545], [40, 10, 34, 8], [40, 11, 34, 9], [40, 17, 34, 15], [41, 12, 35, 10, "message"], [41, 20, 35, 17], [41, 24, 35, 21], [41, 42, 35, 39, "JSON"], [41, 46, 35, 43], [41, 47, 35, 44, "stringify"], [41, 56, 35, 53], [41, 57, 35, 54, "destroy"], [41, 64, 35, 61], [41, 65, 35, 62], [41, 69, 35, 66], [42, 10, 36, 8], [43, 10, 37, 8, "console"], [43, 17, 37, 15], [43, 18, 37, 16, "error"], [43, 23, 37, 21], [43, 24, 37, 22, "message"], [43, 32, 37, 29], [43, 33, 37, 30], [44, 8, 38, 6], [45, 6, 39, 4], [45, 7, 39, 5], [47, 6, 41, 4], [48, 6, 42, 4], [48, 10, 42, 8, "navigation"], [48, 20, 42, 18], [48, 21, 42, 19, "isFocused"], [48, 30, 42, 28], [48, 31, 42, 29], [48, 32, 42, 30], [48, 34, 42, 32], [49, 8, 43, 6, "cleanup"], [49, 15, 43, 13], [49, 18, 43, 16, "callback"], [49, 26, 43, 24], [49, 27, 43, 25], [49, 28, 43, 26], [50, 8, 44, 6, "isFocused"], [50, 17, 44, 15], [50, 20, 44, 18], [50, 24, 44, 22], [51, 6, 45, 4], [52, 6, 46, 4], [52, 10, 46, 10, "unsubscribeFocus"], [52, 26, 46, 26], [52, 29, 46, 29, "navigation"], [52, 39, 46, 39], [52, 40, 46, 40, "addListener"], [52, 51, 46, 51], [52, 52, 46, 52], [52, 59, 46, 59], [52, 61, 46, 61], [52, 67, 46, 67], [53, 8, 47, 6], [54, 8, 48, 6], [55, 8, 49, 6], [55, 12, 49, 10, "isFocused"], [55, 21, 49, 19], [55, 23, 49, 21], [56, 10, 50, 8], [57, 8, 51, 6], [58, 8, 52, 6], [58, 12, 52, 10, "cleanup"], [58, 19, 52, 17], [58, 24, 52, 22, "undefined"], [58, 33, 52, 31], [58, 35, 52, 33], [59, 10, 53, 8, "cleanup"], [59, 17, 53, 15], [59, 18, 53, 16], [59, 19, 53, 17], [60, 8, 54, 6], [61, 8, 55, 6, "cleanup"], [61, 15, 55, 13], [61, 18, 55, 16, "callback"], [61, 26, 55, 24], [61, 27, 55, 25], [61, 28, 55, 26], [62, 8, 56, 6, "isFocused"], [62, 17, 56, 15], [62, 20, 56, 18], [62, 24, 56, 22], [63, 6, 57, 4], [63, 7, 57, 5], [63, 8, 57, 6], [64, 6, 58, 4], [64, 10, 58, 10, "unsubscribeBlur"], [64, 25, 58, 25], [64, 28, 58, 28, "navigation"], [64, 38, 58, 38], [64, 39, 58, 39, "addListener"], [64, 50, 58, 50], [64, 51, 58, 51], [64, 57, 58, 57], [64, 59, 58, 59], [64, 65, 58, 65], [65, 8, 59, 6], [65, 12, 59, 10, "cleanup"], [65, 19, 59, 17], [65, 24, 59, 22, "undefined"], [65, 33, 59, 31], [65, 35, 59, 33], [66, 10, 60, 8, "cleanup"], [66, 17, 60, 15], [66, 18, 60, 16], [66, 19, 60, 17], [67, 8, 61, 6], [68, 8, 62, 6, "cleanup"], [68, 15, 62, 13], [68, 18, 62, 16, "undefined"], [68, 27, 62, 25], [69, 8, 63, 6, "isFocused"], [69, 17, 63, 15], [69, 20, 63, 18], [69, 25, 63, 23], [70, 6, 64, 4], [70, 7, 64, 5], [70, 8, 64, 6], [71, 6, 65, 4], [71, 13, 65, 11], [71, 19, 65, 17], [72, 8, 66, 6], [72, 12, 66, 10, "cleanup"], [72, 19, 66, 17], [72, 24, 66, 22, "undefined"], [72, 33, 66, 31], [72, 35, 66, 33], [73, 10, 67, 8, "cleanup"], [73, 17, 67, 15], [73, 18, 67, 16], [73, 19, 67, 17], [74, 8, 68, 6], [75, 8, 69, 6, "unsubscribeFocus"], [75, 24, 69, 22], [75, 25, 69, 23], [75, 26, 69, 24], [76, 8, 70, 6, "unsubscribeBlur"], [76, 23, 70, 21], [76, 24, 70, 22], [76, 25, 70, 23], [77, 6, 71, 4], [77, 7, 71, 5], [78, 4, 72, 2], [78, 5, 72, 3], [78, 7, 72, 5], [78, 8, 72, 6, "effect"], [78, 14, 72, 12], [78, 16, 72, 14, "navigation"], [78, 26, 72, 24], [78, 27, 72, 25], [78, 28, 72, 26], [79, 2, 73, 0], [80, 0, 73, 1], [80, 3]], "functionMap": {"names": ["<global>", "useFocusEffect", "React.useEffect$argument_0", "callback", "navigation.addListener$argument_1", "<anonymous>"], "mappings": "AAA;OCW;kBCQ;qBCG;KDgB;6DEO;KFW;2DEC;KFM;WGC;KHM;GDC;CDC"}}, "type": "js/module"}]}