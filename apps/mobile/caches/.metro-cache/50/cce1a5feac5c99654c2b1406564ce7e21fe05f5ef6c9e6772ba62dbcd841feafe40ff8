{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ghQueueMicrotask = void 0;\n  // We check for typeof requestAnimationFrame because of SSR\n  // Functions are bound to null to avoid issues with scope when using Metro inline requires.\n  const ghQueueMicrotask = exports.ghQueueMicrotask = typeof setImmediate === 'function' ? setImmediate.bind(null) : typeof requestAnimationFrame === 'function' ? requestAnimationFrame.bind(null) : queueMicrotask.bind(null);\n});", "lineCount": 9, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "ghQueueMicrotask"], [8, 24, 3, 29], [8, 27, 3, 29, "exports"], [8, 34, 3, 29], [8, 35, 3, 29, "ghQueueMicrotask"], [8, 51, 3, 29], [8, 54, 3, 32], [8, 61, 3, 39, "setImmediate"], [8, 73, 3, 51], [8, 78, 3, 56], [8, 88, 3, 66], [8, 91, 3, 69, "setImmediate"], [8, 103, 3, 81], [8, 104, 3, 82, "bind"], [8, 108, 3, 86], [8, 109, 3, 87], [8, 113, 3, 91], [8, 114, 3, 92], [8, 117, 3, 95], [8, 124, 3, 102, "requestAnimationFrame"], [8, 145, 3, 123], [8, 150, 3, 128], [8, 160, 3, 138], [8, 163, 3, 141, "requestAnimationFrame"], [8, 184, 3, 162], [8, 185, 3, 163, "bind"], [8, 189, 3, 167], [8, 190, 3, 168], [8, 194, 3, 172], [8, 195, 3, 173], [8, 198, 3, 176, "queueMicrotask"], [8, 212, 3, 190], [8, 213, 3, 191, "bind"], [8, 217, 3, 195], [8, 218, 3, 196], [8, 222, 3, 200], [8, 223, 3, 201], [9, 0, 3, 202], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}