{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 31, "index": 74}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 109}, "end": {"line": 4, "column": 44, "index": 153}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./extractOpacity", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 155}, "end": {"line": 6, "column": 46, "index": 201}}], "key": "jjgYCrYDewRsVLpQaklu9NyKLaY=", "exportNames": ["*"]}}, {"name": "./extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 202}, "end": {"line": 7, "column": 50, "index": 252}}], "key": "/eFg4kip4DvH5s9LoYzzDzsUESE=", "exportNames": ["*"]}}, {"name": "../units", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 300}, "end": {"line": 9, "column": 29, "index": 329}}], "key": "w7B29dYAP+shhz/m1xxZkm//QSw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = extractGradient;\n  var _react = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var React = _react;\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _extractOpacity = _interopRequireDefault(require(_dependencyMap[3], \"./extractOpacity\"));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[4], \"./extractTransform\"));\n  var _units = _interopRequireDefault(require(_dependencyMap[5], \"../units\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var percentReg = /^([+-]?\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)(%?)$/;\n  function percentToFloat(percent) {\n    if (typeof percent === 'number') {\n      return percent;\n    }\n    if (typeof percent === 'object' && typeof percent.__getAnimatedValue === 'function') {\n      return percent.__getAnimatedValue();\n    }\n    var matched = typeof percent === 'string' && percent.match(percentReg);\n    if (!matched) {\n      console.warn(`\"${percent}\" is not a valid number or percentage string.`);\n      return 0;\n    }\n    return matched[2] ? +matched[1] / 100 : +matched[1];\n  }\n  var offsetComparator = (object, other) => object[0] - other[0];\n  function extractGradient(props, parent) {\n    var id = props.id,\n      children = props.children,\n      gradientTransform = props.gradientTransform,\n      transform = props.transform,\n      gradientUnits = props.gradientUnits;\n    if (!id) {\n      return null;\n    }\n    var stops = [];\n    var childArray = children ? _react.Children.map(children, child => /*#__PURE__*/React.cloneElement(child, {\n      parent\n    })) : [];\n    var l = childArray.length;\n    for (var i = 0; i < l; i++) {\n      var _childArray$i$props = childArray[i].props,\n        style = _childArray$i$props.style,\n        _childArray$i$props$o = _childArray$i$props.offset,\n        offset = _childArray$i$props$o === void 0 ? style && style.offset : _childArray$i$props$o,\n        _childArray$i$props$s = _childArray$i$props.stopColor,\n        stopColor = _childArray$i$props$s === void 0 ? style && style.stopColor || '#000' : _childArray$i$props$s,\n        _childArray$i$props$s2 = _childArray$i$props.stopOpacity,\n        stopOpacity = _childArray$i$props$s2 === void 0 ? style && style.stopOpacity : _childArray$i$props$s2;\n      var offsetNumber = percentToFloat(offset || 0);\n      var color = stopColor && (0, _reactNative.processColor)(stopColor);\n      if (typeof color !== 'number' || isNaN(offsetNumber)) {\n        console.warn(`\"${stopColor}\" is not a valid color or \"${offset}\" is not a valid offset`);\n        continue;\n      }\n      var alpha = Math.round((0, _extractOpacity.default)(stopOpacity) * 255);\n      stops.push([offsetNumber, color & 0x00ffffff | alpha << 24]);\n    }\n    stops.sort(offsetComparator);\n    var gradient = [];\n    var k = stops.length;\n    for (var j = 0; j < k; j++) {\n      var s = stops[j];\n      gradient.push(s[0], s[1]);\n    }\n    return {\n      name: id,\n      gradient,\n      children: childArray,\n      gradientUnits: gradientUnits && _units.default[gradientUnits] || 0,\n      gradientTransform: (0, _extractTransform.default)(gradientTransform || transform || props)\n    };\n  }\n});", "lineCount": 77, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_react"], [7, 12, 2, 0], [7, 15, 2, 0, "_interopRequireWildcard"], [7, 38, 2, 0], [7, 39, 2, 0, "require"], [7, 46, 2, 0], [7, 47, 2, 0, "_dependencyMap"], [7, 61, 2, 0], [8, 2, 2, 31], [8, 6, 2, 31, "React"], [8, 11, 2, 31], [8, 14, 2, 31, "_react"], [8, 20, 2, 31], [9, 2, 4, 0], [9, 6, 4, 0, "_reactNative"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_extractOpacity"], [10, 21, 6, 0], [10, 24, 6, 0, "_interopRequireDefault"], [10, 46, 6, 0], [10, 47, 6, 0, "require"], [10, 54, 6, 0], [10, 55, 6, 0, "_dependencyMap"], [10, 69, 6, 0], [11, 2, 7, 0], [11, 6, 7, 0, "_extractTransform"], [11, 23, 7, 0], [11, 26, 7, 0, "_interopRequireDefault"], [11, 48, 7, 0], [11, 49, 7, 0, "require"], [11, 56, 7, 0], [11, 57, 7, 0, "_dependencyMap"], [11, 71, 7, 0], [12, 2, 9, 0], [12, 6, 9, 0, "_units"], [12, 12, 9, 0], [12, 15, 9, 0, "_interopRequireDefault"], [12, 37, 9, 0], [12, 38, 9, 0, "require"], [12, 45, 9, 0], [12, 46, 9, 0, "_dependencyMap"], [12, 60, 9, 0], [13, 2, 9, 29], [13, 11, 9, 29, "_interopRequireWildcard"], [13, 35, 9, 29, "e"], [13, 36, 9, 29], [13, 38, 9, 29, "t"], [13, 39, 9, 29], [13, 68, 9, 29, "WeakMap"], [13, 75, 9, 29], [13, 81, 9, 29, "r"], [13, 82, 9, 29], [13, 89, 9, 29, "WeakMap"], [13, 96, 9, 29], [13, 100, 9, 29, "n"], [13, 101, 9, 29], [13, 108, 9, 29, "WeakMap"], [13, 115, 9, 29], [13, 127, 9, 29, "_interopRequireWildcard"], [13, 150, 9, 29], [13, 162, 9, 29, "_interopRequireWildcard"], [13, 163, 9, 29, "e"], [13, 164, 9, 29], [13, 166, 9, 29, "t"], [13, 167, 9, 29], [13, 176, 9, 29, "t"], [13, 177, 9, 29], [13, 181, 9, 29, "e"], [13, 182, 9, 29], [13, 186, 9, 29, "e"], [13, 187, 9, 29], [13, 188, 9, 29, "__esModule"], [13, 198, 9, 29], [13, 207, 9, 29, "e"], [13, 208, 9, 29], [13, 214, 9, 29, "o"], [13, 215, 9, 29], [13, 217, 9, 29, "i"], [13, 218, 9, 29], [13, 220, 9, 29, "f"], [13, 221, 9, 29], [13, 226, 9, 29, "__proto__"], [13, 235, 9, 29], [13, 243, 9, 29, "default"], [13, 250, 9, 29], [13, 252, 9, 29, "e"], [13, 253, 9, 29], [13, 270, 9, 29, "e"], [13, 271, 9, 29], [13, 294, 9, 29, "e"], [13, 295, 9, 29], [13, 320, 9, 29, "e"], [13, 321, 9, 29], [13, 330, 9, 29, "f"], [13, 331, 9, 29], [13, 337, 9, 29, "o"], [13, 338, 9, 29], [13, 341, 9, 29, "t"], [13, 342, 9, 29], [13, 345, 9, 29, "n"], [13, 346, 9, 29], [13, 349, 9, 29, "r"], [13, 350, 9, 29], [13, 358, 9, 29, "o"], [13, 359, 9, 29], [13, 360, 9, 29, "has"], [13, 363, 9, 29], [13, 364, 9, 29, "e"], [13, 365, 9, 29], [13, 375, 9, 29, "o"], [13, 376, 9, 29], [13, 377, 9, 29, "get"], [13, 380, 9, 29], [13, 381, 9, 29, "e"], [13, 382, 9, 29], [13, 385, 9, 29, "o"], [13, 386, 9, 29], [13, 387, 9, 29, "set"], [13, 390, 9, 29], [13, 391, 9, 29, "e"], [13, 392, 9, 29], [13, 394, 9, 29, "f"], [13, 395, 9, 29], [13, 409, 9, 29, "_t"], [13, 411, 9, 29], [13, 415, 9, 29, "e"], [13, 416, 9, 29], [13, 432, 9, 29, "_t"], [13, 434, 9, 29], [13, 441, 9, 29, "hasOwnProperty"], [13, 455, 9, 29], [13, 456, 9, 29, "call"], [13, 460, 9, 29], [13, 461, 9, 29, "e"], [13, 462, 9, 29], [13, 464, 9, 29, "_t"], [13, 466, 9, 29], [13, 473, 9, 29, "i"], [13, 474, 9, 29], [13, 478, 9, 29, "o"], [13, 479, 9, 29], [13, 482, 9, 29, "Object"], [13, 488, 9, 29], [13, 489, 9, 29, "defineProperty"], [13, 503, 9, 29], [13, 508, 9, 29, "Object"], [13, 514, 9, 29], [13, 515, 9, 29, "getOwnPropertyDescriptor"], [13, 539, 9, 29], [13, 540, 9, 29, "e"], [13, 541, 9, 29], [13, 543, 9, 29, "_t"], [13, 545, 9, 29], [13, 552, 9, 29, "i"], [13, 553, 9, 29], [13, 554, 9, 29, "get"], [13, 557, 9, 29], [13, 561, 9, 29, "i"], [13, 562, 9, 29], [13, 563, 9, 29, "set"], [13, 566, 9, 29], [13, 570, 9, 29, "o"], [13, 571, 9, 29], [13, 572, 9, 29, "f"], [13, 573, 9, 29], [13, 575, 9, 29, "_t"], [13, 577, 9, 29], [13, 579, 9, 29, "i"], [13, 580, 9, 29], [13, 584, 9, 29, "f"], [13, 585, 9, 29], [13, 586, 9, 29, "_t"], [13, 588, 9, 29], [13, 592, 9, 29, "e"], [13, 593, 9, 29], [13, 594, 9, 29, "_t"], [13, 596, 9, 29], [13, 607, 9, 29, "f"], [13, 608, 9, 29], [13, 613, 9, 29, "e"], [13, 614, 9, 29], [13, 616, 9, 29, "t"], [13, 617, 9, 29], [14, 2, 11, 0], [14, 6, 11, 6, "percentReg"], [14, 16, 11, 16], [14, 19, 11, 19], [14, 64, 11, 64], [15, 2, 13, 0], [15, 11, 13, 9, "percentToFloat"], [15, 25, 13, 23, "percentToFloat"], [15, 26, 14, 2, "percent"], [15, 33, 19, 7], [15, 35, 20, 10], [16, 4, 21, 2], [16, 8, 21, 6], [16, 15, 21, 13, "percent"], [16, 22, 21, 20], [16, 27, 21, 25], [16, 35, 21, 33], [16, 37, 21, 35], [17, 6, 22, 4], [17, 13, 22, 11, "percent"], [17, 20, 22, 18], [18, 4, 23, 2], [19, 4, 24, 2], [19, 8, 25, 4], [19, 15, 25, 11, "percent"], [19, 22, 25, 18], [19, 27, 25, 23], [19, 35, 25, 31], [19, 39, 26, 4], [19, 46, 26, 11, "percent"], [19, 53, 26, 18], [19, 54, 26, 19, "__getAnimatedValue"], [19, 72, 26, 37], [19, 77, 26, 42], [19, 87, 26, 52], [19, 89, 27, 4], [20, 6, 28, 4], [20, 13, 28, 11, "percent"], [20, 20, 28, 18], [20, 21, 28, 19, "__getAnimatedValue"], [20, 39, 28, 37], [20, 40, 28, 38], [20, 41, 28, 39], [21, 4, 29, 2], [22, 4, 30, 2], [22, 8, 30, 8, "matched"], [22, 15, 30, 15], [22, 18, 30, 18], [22, 25, 30, 25, "percent"], [22, 32, 30, 32], [22, 37, 30, 37], [22, 45, 30, 45], [22, 49, 30, 49, "percent"], [22, 56, 30, 56], [22, 57, 30, 57, "match"], [22, 62, 30, 62], [22, 63, 30, 63, "percentReg"], [22, 73, 30, 73], [22, 74, 30, 74], [23, 4, 31, 2], [23, 8, 31, 6], [23, 9, 31, 7, "matched"], [23, 16, 31, 14], [23, 18, 31, 16], [24, 6, 32, 4, "console"], [24, 13, 32, 11], [24, 14, 32, 12, "warn"], [24, 18, 32, 16], [24, 19, 32, 17], [24, 23, 32, 21, "percent"], [24, 30, 32, 28], [24, 77, 32, 75], [24, 78, 32, 76], [25, 6, 33, 4], [25, 13, 33, 11], [25, 14, 33, 12], [26, 4, 34, 2], [27, 4, 36, 2], [27, 11, 36, 9, "matched"], [27, 18, 36, 16], [27, 19, 36, 17], [27, 20, 36, 18], [27, 21, 36, 19], [27, 24, 36, 22], [27, 25, 36, 23, "matched"], [27, 32, 36, 30], [27, 33, 36, 31], [27, 34, 36, 32], [27, 35, 36, 33], [27, 38, 36, 36], [27, 41, 36, 39], [27, 44, 36, 42], [27, 45, 36, 43, "matched"], [27, 52, 36, 50], [27, 53, 36, 51], [27, 54, 36, 52], [27, 55, 36, 53], [28, 2, 37, 0], [29, 2, 39, 0], [29, 6, 39, 6, "offsetComparator"], [29, 22, 39, 22], [29, 25, 39, 25, "offsetComparator"], [29, 26, 39, 26, "object"], [29, 32, 39, 42], [29, 34, 39, 44, "other"], [29, 39, 39, 59], [29, 44, 40, 2, "object"], [29, 50, 40, 8], [29, 51, 40, 9], [29, 52, 40, 10], [29, 53, 40, 11], [29, 56, 40, 14, "other"], [29, 61, 40, 19], [29, 62, 40, 20], [29, 63, 40, 21], [29, 64, 40, 22], [30, 2, 42, 15], [30, 11, 42, 24, "extractGradient"], [30, 26, 42, 39, "extractGradient"], [30, 27, 43, 2, "props"], [30, 32, 49, 20], [30, 34, 50, 2, "parent"], [30, 40, 50, 17], [30, 42, 51, 2], [31, 4, 52, 2], [31, 8, 52, 10, "id"], [31, 10, 52, 12], [31, 13, 52, 72, "props"], [31, 18, 52, 77], [31, 19, 52, 10, "id"], [31, 21, 52, 12], [32, 6, 52, 14, "children"], [32, 14, 52, 22], [32, 17, 52, 72, "props"], [32, 22, 52, 77], [32, 23, 52, 14, "children"], [32, 31, 52, 22], [33, 6, 52, 24, "gradientTransform"], [33, 23, 52, 41], [33, 26, 52, 72, "props"], [33, 31, 52, 77], [33, 32, 52, 24, "gradientTransform"], [33, 49, 52, 41], [34, 6, 52, 43, "transform"], [34, 15, 52, 52], [34, 18, 52, 72, "props"], [34, 23, 52, 77], [34, 24, 52, 43, "transform"], [34, 33, 52, 52], [35, 6, 52, 54, "gradientUnits"], [35, 19, 52, 67], [35, 22, 52, 72, "props"], [35, 27, 52, 77], [35, 28, 52, 54, "gradientUnits"], [35, 41, 52, 67], [36, 4, 53, 2], [36, 8, 53, 6], [36, 9, 53, 7, "id"], [36, 11, 53, 9], [36, 13, 53, 11], [37, 6, 54, 4], [37, 13, 54, 11], [37, 17, 54, 15], [38, 4, 55, 2], [39, 4, 57, 2], [39, 8, 57, 8, "stops"], [39, 13, 57, 13], [39, 16, 57, 16], [39, 18, 57, 18], [40, 4, 58, 2], [40, 8, 58, 8, "<PERSON><PERSON><PERSON><PERSON>"], [40, 18, 58, 18], [40, 21, 58, 21, "children"], [40, 29, 58, 29], [40, 32, 59, 6, "Children"], [40, 47, 59, 14], [40, 48, 59, 15, "map"], [40, 51, 59, 18], [40, 52, 59, 19, "children"], [40, 60, 59, 27], [40, 62, 59, 30, "child"], [40, 67, 59, 35], [40, 84, 60, 8, "React"], [40, 89, 60, 13], [40, 90, 60, 14, "cloneElement"], [40, 102, 60, 26], [40, 103, 60, 27, "child"], [40, 108, 60, 32], [40, 110, 60, 34], [41, 6, 61, 10, "parent"], [42, 4, 62, 8], [42, 5, 62, 9], [42, 6, 63, 6], [42, 7, 63, 7], [42, 10, 64, 6], [42, 12, 64, 8], [43, 4, 65, 2], [43, 8, 65, 8, "l"], [43, 9, 65, 9], [43, 12, 65, 12, "<PERSON><PERSON><PERSON><PERSON>"], [43, 22, 65, 22], [43, 23, 65, 23, "length"], [43, 29, 65, 29], [44, 4, 66, 2], [44, 9, 66, 7], [44, 13, 66, 11, "i"], [44, 14, 66, 12], [44, 17, 66, 15], [44, 18, 66, 16], [44, 20, 66, 18, "i"], [44, 21, 66, 19], [44, 24, 66, 22, "l"], [44, 25, 66, 23], [44, 27, 66, 25, "i"], [44, 28, 66, 26], [44, 30, 66, 28], [44, 32, 66, 30], [45, 6, 67, 4], [45, 10, 67, 4, "_childArray$i$props"], [45, 29, 67, 4], [45, 32, 74, 8, "<PERSON><PERSON><PERSON><PERSON>"], [45, 42, 74, 18], [45, 43, 74, 19, "i"], [45, 44, 74, 20], [45, 45, 74, 21], [45, 46, 68, 6, "props"], [45, 51, 68, 11], [46, 8, 69, 8, "style"], [46, 13, 69, 13], [46, 16, 69, 13, "_childArray$i$props"], [46, 35, 69, 13], [46, 36, 69, 8, "style"], [46, 41, 69, 13], [47, 8, 69, 13, "_childArray$i$props$o"], [47, 29, 69, 13], [47, 32, 69, 13, "_childArray$i$props"], [47, 51, 69, 13], [47, 52, 70, 8, "offset"], [47, 58, 70, 14], [48, 8, 70, 8, "offset"], [48, 14, 70, 14], [48, 17, 70, 14, "_childArray$i$props$o"], [48, 38, 70, 14], [48, 52, 70, 17, "style"], [48, 57, 70, 22], [48, 61, 70, 26, "style"], [48, 66, 70, 31], [48, 67, 70, 32, "offset"], [48, 73, 70, 38], [48, 76, 70, 38, "_childArray$i$props$o"], [48, 97, 70, 38], [49, 8, 70, 38, "_childArray$i$props$s"], [49, 29, 70, 38], [49, 32, 70, 38, "_childArray$i$props"], [49, 51, 70, 38], [49, 52, 71, 8, "stopColor"], [49, 61, 71, 17], [50, 8, 71, 8, "stopColor"], [50, 17, 71, 17], [50, 20, 71, 17, "_childArray$i$props$s"], [50, 41, 71, 17], [50, 55, 71, 21, "style"], [50, 60, 71, 26], [50, 64, 71, 30, "style"], [50, 69, 71, 35], [50, 70, 71, 36, "stopColor"], [50, 79, 71, 45], [50, 83, 71, 50], [50, 89, 71, 56], [50, 92, 71, 56, "_childArray$i$props$s"], [50, 113, 71, 56], [51, 8, 71, 56, "_childArray$i$props$s2"], [51, 30, 71, 56], [51, 33, 71, 56, "_childArray$i$props"], [51, 52, 71, 56], [51, 53, 72, 8, "stopOpacity"], [51, 64, 72, 19], [52, 8, 72, 8, "stopOpacity"], [52, 19, 72, 19], [52, 22, 72, 19, "_childArray$i$props$s2"], [52, 44, 72, 19], [52, 58, 72, 22, "style"], [52, 63, 72, 27], [52, 67, 72, 31, "style"], [52, 72, 72, 36], [52, 73, 72, 37, "stopOpacity"], [52, 84, 72, 48], [52, 87, 72, 48, "_childArray$i$props$s2"], [52, 109, 72, 48], [53, 6, 75, 4], [53, 10, 75, 10, "offsetNumber"], [53, 22, 75, 22], [53, 25, 75, 25, "percentToFloat"], [53, 39, 75, 39], [53, 40, 75, 40, "offset"], [53, 46, 75, 46], [53, 50, 75, 50], [53, 51, 75, 51], [53, 52, 75, 52], [54, 6, 76, 4], [54, 10, 76, 10, "color"], [54, 15, 76, 15], [54, 18, 76, 18, "stopColor"], [54, 27, 76, 27], [54, 31, 76, 31], [54, 35, 76, 31, "processColor"], [54, 60, 76, 43], [54, 62, 76, 44, "stopColor"], [54, 71, 76, 53], [54, 72, 76, 54], [55, 6, 77, 4], [55, 10, 77, 8], [55, 17, 77, 15, "color"], [55, 22, 77, 20], [55, 27, 77, 25], [55, 35, 77, 33], [55, 39, 77, 37, "isNaN"], [55, 44, 77, 42], [55, 45, 77, 43, "offsetNumber"], [55, 57, 77, 55], [55, 58, 77, 56], [55, 60, 77, 58], [56, 8, 78, 6, "console"], [56, 15, 78, 13], [56, 16, 78, 14, "warn"], [56, 20, 78, 18], [56, 21, 79, 8], [56, 25, 79, 12, "stopColor"], [56, 34, 79, 21], [56, 64, 79, 51, "offset"], [56, 70, 79, 57], [56, 95, 80, 6], [56, 96, 80, 7], [57, 8, 81, 6], [58, 6, 82, 4], [59, 6, 83, 4], [59, 10, 83, 10, "alpha"], [59, 15, 83, 15], [59, 18, 83, 18, "Math"], [59, 22, 83, 22], [59, 23, 83, 23, "round"], [59, 28, 83, 28], [59, 29, 83, 29], [59, 33, 83, 29, "extractOpacity"], [59, 56, 83, 43], [59, 58, 83, 44, "stopOpacity"], [59, 69, 83, 55], [59, 70, 83, 56], [59, 73, 83, 59], [59, 76, 83, 62], [59, 77, 83, 63], [60, 6, 84, 4, "stops"], [60, 11, 84, 9], [60, 12, 84, 10, "push"], [60, 16, 84, 14], [60, 17, 84, 15], [60, 18, 84, 16, "offsetNumber"], [60, 30, 84, 28], [60, 32, 84, 31, "color"], [60, 37, 84, 36], [60, 40, 84, 39], [60, 50, 84, 49], [60, 53, 84, 54, "alpha"], [60, 58, 84, 59], [60, 62, 84, 63], [60, 64, 84, 66], [60, 65, 84, 67], [60, 66, 84, 68], [61, 4, 85, 2], [62, 4, 86, 2, "stops"], [62, 9, 86, 7], [62, 10, 86, 8, "sort"], [62, 14, 86, 12], [62, 15, 86, 13, "offsetComparator"], [62, 31, 86, 29], [62, 32, 86, 30], [63, 4, 88, 2], [63, 8, 88, 8, "gradient"], [63, 16, 88, 16], [63, 19, 88, 19], [63, 21, 88, 21], [64, 4, 89, 2], [64, 8, 89, 8, "k"], [64, 9, 89, 9], [64, 12, 89, 12, "stops"], [64, 17, 89, 17], [64, 18, 89, 18, "length"], [64, 24, 89, 24], [65, 4, 90, 2], [65, 9, 90, 7], [65, 13, 90, 11, "j"], [65, 14, 90, 12], [65, 17, 90, 15], [65, 18, 90, 16], [65, 20, 90, 18, "j"], [65, 21, 90, 19], [65, 24, 90, 22, "k"], [65, 25, 90, 23], [65, 27, 90, 25, "j"], [65, 28, 90, 26], [65, 30, 90, 28], [65, 32, 90, 30], [66, 6, 91, 4], [66, 10, 91, 10, "s"], [66, 11, 91, 11], [66, 14, 91, 14, "stops"], [66, 19, 91, 19], [66, 20, 91, 20, "j"], [66, 21, 91, 21], [66, 22, 91, 22], [67, 6, 92, 4, "gradient"], [67, 14, 92, 12], [67, 15, 92, 13, "push"], [67, 19, 92, 17], [67, 20, 92, 18, "s"], [67, 21, 92, 19], [67, 22, 92, 20], [67, 23, 92, 21], [67, 24, 92, 22], [67, 26, 92, 24, "s"], [67, 27, 92, 25], [67, 28, 92, 26], [67, 29, 92, 27], [67, 30, 92, 28], [67, 31, 92, 29], [68, 4, 93, 2], [69, 4, 95, 2], [69, 11, 95, 9], [70, 6, 96, 4, "name"], [70, 10, 96, 8], [70, 12, 96, 10, "id"], [70, 14, 96, 12], [71, 6, 97, 4, "gradient"], [71, 14, 97, 12], [72, 6, 98, 4, "children"], [72, 14, 98, 12], [72, 16, 98, 14, "<PERSON><PERSON><PERSON><PERSON>"], [72, 26, 98, 24], [73, 6, 99, 4, "gradientUnits"], [73, 19, 99, 17], [73, 21, 99, 20, "gradientUnits"], [73, 34, 99, 33], [73, 38, 99, 37, "units"], [73, 52, 99, 42], [73, 53, 99, 43, "gradientUnits"], [73, 66, 99, 56], [73, 67, 99, 57], [73, 71, 99, 62], [73, 72, 99, 63], [74, 6, 100, 4, "gradientTransform"], [74, 23, 100, 21], [74, 25, 100, 23], [74, 29, 100, 23, "extractTransform"], [74, 54, 100, 39], [74, 56, 101, 6, "gradientTransform"], [74, 73, 101, 23], [74, 77, 101, 27, "transform"], [74, 86, 101, 36], [74, 90, 101, 40, "props"], [74, 95, 102, 4], [75, 4, 103, 2], [75, 5, 103, 3], [76, 2, 104, 0], [77, 0, 104, 1], [77, 3]], "functionMap": {"names": ["<global>", "percentToFloat", "offsetComparator", "extractGradient", "Children.map$argument_1"], "mappings": "AAA;ACY;CDwB;yBEE;sBFC;eGE;6BCiB;UDG;CH0C"}}, "type": "js/module"}]}