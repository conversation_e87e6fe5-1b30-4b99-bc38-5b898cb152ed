{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./resolveAssetSource", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "qiXdfzfF08Pne6HL41q9H4FDwsA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.resolveSource = resolveSource;\n  var _resolveAssetSource = _interopRequireDefault(require(_dependencyMap[1], \"./resolveAssetSource\"));\n  function resolveSource(source) {\n    if (typeof source === 'string') {\n      return {\n        uri: source\n      };\n    }\n    if (typeof source === 'number') {\n      return (0, _resolveAssetSource.default)(source);\n    }\n    return source ?? null;\n  }\n});", "lineCount": 19, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_resolveAssetSource"], [7, 25, 1, 0], [7, 28, 1, 0, "_interopRequireDefault"], [7, 50, 1, 0], [7, 51, 1, 0, "require"], [7, 58, 1, 0], [7, 59, 1, 0, "_dependencyMap"], [7, 73, 1, 0], [8, 2, 2, 7], [8, 11, 2, 16, "resolveSource"], [8, 24, 2, 29, "resolveSource"], [8, 25, 2, 30, "source"], [8, 31, 2, 36], [8, 33, 2, 38], [9, 4, 3, 4], [9, 8, 3, 8], [9, 15, 3, 15, "source"], [9, 21, 3, 21], [9, 26, 3, 26], [9, 34, 3, 34], [9, 36, 3, 36], [10, 6, 4, 8], [10, 13, 4, 15], [11, 8, 4, 17, "uri"], [11, 11, 4, 20], [11, 13, 4, 22, "source"], [12, 6, 4, 29], [12, 7, 4, 30], [13, 4, 5, 4], [14, 4, 6, 4], [14, 8, 6, 8], [14, 15, 6, 15, "source"], [14, 21, 6, 21], [14, 26, 6, 26], [14, 34, 6, 34], [14, 36, 6, 36], [15, 6, 7, 8], [15, 13, 7, 15], [15, 17, 7, 15, "resolveAssetSource"], [15, 44, 7, 33], [15, 46, 7, 34, "source"], [15, 52, 7, 40], [15, 53, 7, 41], [16, 4, 8, 4], [17, 4, 9, 4], [17, 11, 9, 11, "source"], [17, 17, 9, 17], [17, 21, 9, 21], [17, 25, 9, 25], [18, 2, 10, 0], [19, 0, 10, 1], [19, 3]], "functionMap": {"names": ["<global>", "resolveSource"], "mappings": "AAA;OCC;CDQ"}}, "type": "js/module"}]}