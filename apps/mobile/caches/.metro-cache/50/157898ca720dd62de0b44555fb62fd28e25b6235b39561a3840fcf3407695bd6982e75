{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./Font", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 45, "index": 90}}], "key": "uhjJvb2CC+i2amHkDH4+UF8lIHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFonts = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _Font = require(_dependencyMap[1], \"./Font\");\n  function isMapLoaded(map) {\n    if (typeof map === 'string') {\n      return (0, _Font.isLoaded)(map);\n    } else {\n      return Object.keys(map).every(fontFamily => (0, _Font.isLoaded)(fontFamily));\n    }\n  }\n  function useRuntimeFonts(map) {\n    const [loaded, setLoaded] = (0, _react.useState)(\n    // For web rehydration, we need to check if the fonts are already loaded during the static render.\n    // Native will also benefit from this optimization.\n    isMapLoaded(map));\n    const [error, setError] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      let isMounted = true;\n      (0, _Font.loadAsync)(map).then(() => {\n        if (isMounted) {\n          setLoaded(true);\n        }\n      }).catch(error => {\n        if (isMounted) {\n          setError(error);\n        }\n      });\n      return () => {\n        isMounted = false;\n      };\n    }, []);\n    return [loaded, error];\n  }\n  function useStaticFonts(map) {\n    (0, _Font.loadAsync)(map);\n    return [true, null];\n  }\n  // @needsAudit\n  /**\n   * Load a map of fonts with [`loadAsync`](#loadasyncfontfamilyorfontmap-source). This returns a `boolean` if the fonts are\n   * loaded and ready to use. It also returns an error if something went wrong, to use in development.\n   *\n   * > Note, the fonts are not \"reloaded\" when you dynamically change the font map.\n   *\n   * @param map A map of `fontFamily`s to [`FontSource`](#fontsource)s. After loading the font you can\n   * use the key in the `fontFamily` style prop of a `Text` element.\n   *\n   * @return\n   * - __loaded__ (`boolean`) - A boolean to detect if the font for `fontFamily` has finished\n   * loading.\n   * - __error__ (`Error | null`) - An error encountered when loading the fonts.\n   *\n   * @example\n   * ```tsx\n   * const [loaded, error] = useFonts({ ... });\n   * ```\n   */\n  const useFonts = exports.useFonts = typeof window === 'undefined' ? useStaticFonts : useRuntimeFonts;\n});", "lineCount": 63, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Font"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 11, 3, 9, "isMapLoaded"], [8, 22, 3, 20, "isMapLoaded"], [8, 23, 3, 21, "map"], [8, 26, 3, 24], [8, 28, 3, 26], [9, 4, 4, 4], [9, 8, 4, 8], [9, 15, 4, 15, "map"], [9, 18, 4, 18], [9, 23, 4, 23], [9, 31, 4, 31], [9, 33, 4, 33], [10, 6, 5, 8], [10, 13, 5, 15], [10, 17, 5, 15, "isLoaded"], [10, 31, 5, 23], [10, 33, 5, 24, "map"], [10, 36, 5, 27], [10, 37, 5, 28], [11, 4, 6, 4], [11, 5, 6, 5], [11, 11, 7, 9], [12, 6, 8, 8], [12, 13, 8, 15, "Object"], [12, 19, 8, 21], [12, 20, 8, 22, "keys"], [12, 24, 8, 26], [12, 25, 8, 27, "map"], [12, 28, 8, 30], [12, 29, 8, 31], [12, 30, 8, 32, "every"], [12, 35, 8, 37], [12, 36, 8, 39, "fontFamily"], [12, 46, 8, 49], [12, 50, 8, 54], [12, 54, 8, 54, "isLoaded"], [12, 68, 8, 62], [12, 70, 8, 63, "fontFamily"], [12, 80, 8, 73], [12, 81, 8, 74], [12, 82, 8, 75], [13, 4, 9, 4], [14, 2, 10, 0], [15, 2, 11, 0], [15, 11, 11, 9, "useRuntimeFonts"], [15, 26, 11, 24, "useRuntimeFonts"], [15, 27, 11, 25, "map"], [15, 30, 11, 28], [15, 32, 11, 30], [16, 4, 12, 4], [16, 10, 12, 10], [16, 11, 12, 11, "loaded"], [16, 17, 12, 17], [16, 19, 12, 19, "setLoaded"], [16, 28, 12, 28], [16, 29, 12, 29], [16, 32, 12, 32], [16, 36, 12, 32, "useState"], [16, 51, 12, 40], [17, 4, 13, 4], [18, 4, 14, 4], [19, 4, 15, 4, "isMapLoaded"], [19, 15, 15, 15], [19, 16, 15, 16, "map"], [19, 19, 15, 19], [19, 20, 15, 20], [19, 21, 15, 21], [20, 4, 16, 4], [20, 10, 16, 10], [20, 11, 16, 11, "error"], [20, 16, 16, 16], [20, 18, 16, 18, "setError"], [20, 26, 16, 26], [20, 27, 16, 27], [20, 30, 16, 30], [20, 34, 16, 30, "useState"], [20, 49, 16, 38], [20, 51, 16, 39], [20, 55, 16, 43], [20, 56, 16, 44], [21, 4, 17, 4], [21, 8, 17, 4, "useEffect"], [21, 24, 17, 13], [21, 26, 17, 14], [21, 32, 17, 20], [22, 6, 18, 8], [22, 10, 18, 12, "isMounted"], [22, 19, 18, 21], [22, 22, 18, 24], [22, 26, 18, 28], [23, 6, 19, 8], [23, 10, 19, 8, "loadAsync"], [23, 25, 19, 17], [23, 27, 19, 18, "map"], [23, 30, 19, 21], [23, 31, 19, 22], [23, 32, 20, 13, "then"], [23, 36, 20, 17], [23, 37, 20, 18], [23, 43, 20, 24], [24, 8, 21, 12], [24, 12, 21, 16, "isMounted"], [24, 21, 21, 25], [24, 23, 21, 27], [25, 10, 22, 16, "setLoaded"], [25, 19, 22, 25], [25, 20, 22, 26], [25, 24, 22, 30], [25, 25, 22, 31], [26, 8, 23, 12], [27, 6, 24, 8], [27, 7, 24, 9], [27, 8, 24, 10], [27, 9, 25, 13, "catch"], [27, 14, 25, 18], [27, 15, 25, 20, "error"], [27, 20, 25, 25], [27, 24, 25, 30], [28, 8, 26, 12], [28, 12, 26, 16, "isMounted"], [28, 21, 26, 25], [28, 23, 26, 27], [29, 10, 27, 16, "setError"], [29, 18, 27, 24], [29, 19, 27, 25, "error"], [29, 24, 27, 30], [29, 25, 27, 31], [30, 8, 28, 12], [31, 6, 29, 8], [31, 7, 29, 9], [31, 8, 29, 10], [32, 6, 30, 8], [32, 13, 30, 15], [32, 19, 30, 21], [33, 8, 31, 12, "isMounted"], [33, 17, 31, 21], [33, 20, 31, 24], [33, 25, 31, 29], [34, 6, 32, 8], [34, 7, 32, 9], [35, 4, 33, 4], [35, 5, 33, 5], [35, 7, 33, 7], [35, 9, 33, 9], [35, 10, 33, 10], [36, 4, 34, 4], [36, 11, 34, 11], [36, 12, 34, 12, "loaded"], [36, 18, 34, 18], [36, 20, 34, 20, "error"], [36, 25, 34, 25], [36, 26, 34, 26], [37, 2, 35, 0], [38, 2, 36, 0], [38, 11, 36, 9, "useStaticFonts"], [38, 25, 36, 23, "useStaticFonts"], [38, 26, 36, 24, "map"], [38, 29, 36, 27], [38, 31, 36, 29], [39, 4, 37, 4], [39, 8, 37, 4, "loadAsync"], [39, 23, 37, 13], [39, 25, 37, 14, "map"], [39, 28, 37, 17], [39, 29, 37, 18], [40, 4, 38, 4], [40, 11, 38, 11], [40, 12, 38, 12], [40, 16, 38, 16], [40, 18, 38, 18], [40, 22, 38, 22], [40, 23, 38, 23], [41, 2, 39, 0], [42, 2, 40, 0], [43, 2, 41, 0], [44, 0, 42, 0], [45, 0, 43, 0], [46, 0, 44, 0], [47, 0, 45, 0], [48, 0, 46, 0], [49, 0, 47, 0], [50, 0, 48, 0], [51, 0, 49, 0], [52, 0, 50, 0], [53, 0, 51, 0], [54, 0, 52, 0], [55, 0, 53, 0], [56, 0, 54, 0], [57, 0, 55, 0], [58, 0, 56, 0], [59, 0, 57, 0], [60, 0, 58, 0], [61, 0, 59, 0], [62, 2, 60, 7], [62, 8, 60, 13, "useFonts"], [62, 16, 60, 21], [62, 19, 60, 21, "exports"], [62, 26, 60, 21], [62, 27, 60, 21, "useFonts"], [62, 35, 60, 21], [62, 38, 60, 24], [62, 45, 60, 31, "window"], [62, 51, 60, 37], [62, 56, 60, 42], [62, 67, 60, 53], [62, 70, 60, 56, "useStaticFonts"], [62, 84, 60, 70], [62, 87, 60, 73, "useRuntimeFonts"], [62, 102, 60, 88], [63, 0, 60, 89], [63, 3]], "functionMap": {"names": ["<global>", "isMapLoaded", "Object.keys.every$argument_0", "useRuntimeFonts", "useEffect$argument_0", "loadAsync.then$argument_0", "loadAsync.then._catch$argument_0", "<anonymous>", "useStaticFonts"], "mappings": "AAA;ACE;sCCK,oCD;CDE;AGC;cCM;kBCG;SDI;mBEC;SFI;eGC;SHE;KDC;CHE;AQC;CRG"}}, "type": "js/module"}]}