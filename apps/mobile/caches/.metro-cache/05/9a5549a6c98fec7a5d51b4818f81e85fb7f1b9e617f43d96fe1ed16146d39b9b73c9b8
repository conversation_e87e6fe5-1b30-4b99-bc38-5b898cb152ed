{"dependencies": [{"name": "expo-constants", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 41, "index": 304}, "end": {"line": 7, "column": 66, "index": 329}}], "key": "MLjvisfgn5XkSYgDpD4nfivY4nE=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 23, "index": 355}, "end": {"line": 8, "column": 46, "index": 378}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "react-native-is-edge-to-edge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 39, "index": 419}, "end": {"line": 9, "column": 78, "index": 458}}], "key": "/DF8pvIK7hVUN1ny60pi3/9Ia+A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.canOverrideStatusBarBehavior = void 0;\n  var expo_constants_1 = __importDefault(require(_dependencyMap[0], \"expo-constants\"));\n  var react_native_1 = require(_dependencyMap[1], \"react-native\");\n  var react_native_is_edge_to_edge_1 = require(_dependencyMap[2], \"react-native-is-edge-to-edge\");\n  var hasViewControllerBasedStatusBarAppearance = react_native_1.Platform.OS === 'ios' && !!expo_constants_1.default.expoConfig?.ios?.infoPlist?.UIViewControllerBasedStatusBarAppearance;\n  exports.canOverrideStatusBarBehavior = !(0, react_native_is_edge_to_edge_1.isEdgeToEdge)() && !hasViewControllerBasedStatusBarAppearance;\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__importDefault"], [4, 21, 2, 19], [4, 24, 2, 23], [4, 28, 2, 27], [4, 32, 2, 31], [4, 36, 2, 35], [4, 37, 2, 36, "__importDefault"], [4, 52, 2, 51], [4, 56, 2, 56], [4, 66, 2, 66, "mod"], [4, 69, 2, 69], [4, 71, 2, 71], [5, 4, 3, 4], [5, 11, 3, 12, "mod"], [5, 14, 3, 15], [5, 18, 3, 19, "mod"], [5, 21, 3, 22], [5, 22, 3, 23, "__esModule"], [5, 32, 3, 33], [5, 35, 3, 37, "mod"], [5, 38, 3, 40], [5, 41, 3, 43], [6, 6, 3, 45], [6, 15, 3, 54], [6, 17, 3, 56, "mod"], [7, 4, 3, 60], [7, 5, 3, 61], [8, 2, 4, 0], [8, 3, 4, 1], [9, 2, 5, 0, "Object"], [9, 8, 5, 6], [9, 9, 5, 7, "defineProperty"], [9, 23, 5, 21], [9, 24, 5, 22, "exports"], [9, 31, 5, 29], [9, 33, 5, 31], [9, 45, 5, 43], [9, 47, 5, 45], [10, 4, 5, 47, "value"], [10, 9, 5, 52], [10, 11, 5, 54], [11, 2, 5, 59], [11, 3, 5, 60], [11, 4, 5, 61], [12, 2, 6, 0, "exports"], [12, 9, 6, 7], [12, 10, 6, 8, "canOverrideStatusBarBehavior"], [12, 38, 6, 36], [12, 41, 6, 39], [12, 46, 6, 44], [12, 47, 6, 45], [13, 2, 7, 0], [13, 6, 7, 6, "expo_constants_1"], [13, 22, 7, 22], [13, 25, 7, 25, "__importDefault"], [13, 40, 7, 40], [13, 41, 7, 41, "require"], [13, 48, 7, 48], [13, 49, 7, 48, "_dependencyMap"], [13, 63, 7, 48], [13, 84, 7, 65], [13, 85, 7, 66], [13, 86, 7, 67], [14, 2, 8, 0], [14, 6, 8, 6, "react_native_1"], [14, 20, 8, 20], [14, 23, 8, 23, "require"], [14, 30, 8, 30], [14, 31, 8, 30, "_dependencyMap"], [14, 45, 8, 30], [14, 64, 8, 45], [14, 65, 8, 46], [15, 2, 9, 0], [15, 6, 9, 6, "react_native_is_edge_to_edge_1"], [15, 36, 9, 36], [15, 39, 9, 39, "require"], [15, 46, 9, 46], [15, 47, 9, 46, "_dependencyMap"], [15, 61, 9, 46], [15, 96, 9, 77], [15, 97, 9, 78], [16, 2, 10, 0], [16, 6, 10, 6, "hasViewControllerBasedStatusBarAppearance"], [16, 47, 10, 47], [16, 50, 10, 50, "react_native_1"], [16, 64, 10, 64], [16, 65, 10, 65, "Platform"], [16, 73, 10, 73], [16, 74, 10, 74, "OS"], [16, 76, 10, 76], [16, 81, 10, 81], [16, 86, 10, 86], [16, 90, 11, 4], [16, 91, 11, 5], [16, 92, 11, 6, "expo_constants_1"], [16, 108, 11, 22], [16, 109, 11, 23, "default"], [16, 116, 11, 30], [16, 117, 11, 31, "expoConfig"], [16, 127, 11, 41], [16, 129, 11, 43, "ios"], [16, 132, 11, 46], [16, 134, 11, 48, "infoPlist"], [16, 143, 11, 57], [16, 145, 11, 59, "UIViewControllerBasedStatusBarAppearance"], [16, 185, 11, 99], [17, 2, 12, 0, "exports"], [17, 9, 12, 7], [17, 10, 12, 8, "canOverrideStatusBarBehavior"], [17, 38, 12, 36], [17, 41, 12, 39], [17, 42, 12, 40], [17, 43, 12, 41], [17, 44, 12, 42], [17, 46, 12, 44, "react_native_is_edge_to_edge_1"], [17, 76, 12, 74], [17, 77, 12, 75, "isEdgeToEdge"], [17, 89, 12, 87], [17, 91, 12, 89], [17, 92, 12, 90], [17, 96, 12, 94], [17, 97, 12, 95, "hasViewControllerBasedStatusBarAppearance"], [17, 138, 12, 136], [18, 0, 12, 137], [18, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;wDCC;CDE"}}, "type": "js/module"}]}