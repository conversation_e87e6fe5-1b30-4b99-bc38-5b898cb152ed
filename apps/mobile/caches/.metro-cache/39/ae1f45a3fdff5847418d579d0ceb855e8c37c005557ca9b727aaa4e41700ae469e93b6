{"dependencies": [{"name": "./elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 526, "index": 526}}], "key": "Jzo/Yd6BZZ7INdCboGTp5X+67rE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.tags = void 0;\n  var _elements = require(_dependencyMap[0], \"./elements\");\n  const tags = exports.tags = {\n    circle: _elements.Circle,\n    clipPath: _elements.ClipPath,\n    defs: _elements.Defs,\n    ellipse: _elements.Ellipse,\n    filter: _elements.Filter,\n    feBlend: _elements.FeBlend,\n    feColorMatrix: _elements.FeColorMatrix,\n    feComponentTransfer: _elements.FeComponentTransfer,\n    feComposite: _elements.FeComposite,\n    feConvolveMatrix: _elements.FeConvolveMatrix,\n    feDiffuseLighting: _elements.FeDiffuseLighting,\n    feDisplacementMap: _elements.FeDisplacementMap,\n    feDistantLight: _elements.FeDistantLight,\n    feDropShadow: _elements.FeDropShadow,\n    feFlood: _elements.FeFlood,\n    feGaussianBlur: _elements.FeGaussianBlur,\n    feImage: _elements.FeImage,\n    feMerge: _elements.FeMerge,\n    feMergeNode: _elements.FeMergeNode,\n    feMorphology: _elements.FeMorphology,\n    feOffset: _elements.FeOffset,\n    fePointLight: _elements.FePointLight,\n    feSpecularLighting: _elements.FeSpecularLighting,\n    feSpotLight: _elements.FeSpotLight,\n    feTile: _elements.FeTile,\n    feTurbulence: _elements.FeTurbulence,\n    foreignObject: _elements.ForeignObject,\n    g: _elements.G,\n    image: _elements.Image,\n    line: _elements.Line,\n    linearGradient: _elements.LinearGradient,\n    marker: _elements.Marker,\n    mask: _elements.Mask,\n    path: _elements.Path,\n    pattern: _elements.Pattern,\n    polygon: _elements.Polygon,\n    polyline: _elements.Polyline,\n    radialGradient: _elements.RadialGradient,\n    rect: _elements.Rect,\n    stop: _elements.Stop,\n    svg: _elements.Svg,\n    symbol: _elements.Symbol,\n    text: _elements.Text,\n    textPath: _elements.TextPath,\n    tspan: _elements.TSpan,\n    use: _elements.Use\n  };\n});", "lineCount": 55, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_elements"], [6, 15, 1, 0], [6, 18, 1, 0, "require"], [6, 25, 1, 0], [6, 26, 1, 0, "_dependencyMap"], [6, 40, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "tags"], [7, 12, 2, 17], [7, 15, 2, 17, "exports"], [7, 22, 2, 17], [7, 23, 2, 17, "tags"], [7, 27, 2, 17], [7, 30, 2, 20], [8, 4, 3, 2, "circle"], [8, 10, 3, 8], [8, 12, 3, 10, "Circle"], [8, 28, 3, 16], [9, 4, 4, 2, "clipPath"], [9, 12, 4, 10], [9, 14, 4, 12, "<PERSON><PERSON><PERSON><PERSON>"], [9, 32, 4, 20], [10, 4, 5, 2, "defs"], [10, 8, 5, 6], [10, 10, 5, 8, "Defs"], [10, 24, 5, 12], [11, 4, 6, 2, "ellipse"], [11, 11, 6, 9], [11, 13, 6, 11, "Ellipse"], [11, 30, 6, 18], [12, 4, 7, 2, "filter"], [12, 10, 7, 8], [12, 12, 7, 10, "Filter"], [12, 28, 7, 16], [13, 4, 8, 2, "feBlend"], [13, 11, 8, 9], [13, 13, 8, 11, "FeBlend"], [13, 30, 8, 18], [14, 4, 9, 2, "feColorMatrix"], [14, 17, 9, 15], [14, 19, 9, 17, "FeColorMatrix"], [14, 42, 9, 30], [15, 4, 10, 2, "feComponentTransfer"], [15, 23, 10, 21], [15, 25, 10, 23, "FeComponentTransfer"], [15, 54, 10, 42], [16, 4, 11, 2, "feComposite"], [16, 15, 11, 13], [16, 17, 11, 15, "FeComposite"], [16, 38, 11, 26], [17, 4, 12, 2, "feConvolveMatrix"], [17, 20, 12, 18], [17, 22, 12, 20, "FeConvolveMatrix"], [17, 48, 12, 36], [18, 4, 13, 2, "feDiffuseLighting"], [18, 21, 13, 19], [18, 23, 13, 21, "FeDiffuseLighting"], [18, 50, 13, 38], [19, 4, 14, 2, "feDisplacementMap"], [19, 21, 14, 19], [19, 23, 14, 21, "FeDisplacementMap"], [19, 50, 14, 38], [20, 4, 15, 2, "feDistantLight"], [20, 18, 15, 16], [20, 20, 15, 18, "FeDistantLight"], [20, 44, 15, 32], [21, 4, 16, 2, "feDropShadow"], [21, 16, 16, 14], [21, 18, 16, 16, "FeDropShadow"], [21, 40, 16, 28], [22, 4, 17, 2, "feFlood"], [22, 11, 17, 9], [22, 13, 17, 11, "FeFlood"], [22, 30, 17, 18], [23, 4, 18, 2, "feG<PERSON><PERSON><PERSON>lur"], [23, 18, 18, 16], [23, 20, 18, 18, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 44, 18, 32], [24, 4, 19, 2, "feImage"], [24, 11, 19, 9], [24, 13, 19, 11, "FeImage"], [24, 30, 19, 18], [25, 4, 20, 2, "feMerge"], [25, 11, 20, 9], [25, 13, 20, 11, "FeMerge"], [25, 30, 20, 18], [26, 4, 21, 2, "feMergeNode"], [26, 15, 21, 13], [26, 17, 21, 15, "FeMergeNode"], [26, 38, 21, 26], [27, 4, 22, 2, "feMorphology"], [27, 16, 22, 14], [27, 18, 22, 16, "FeMorphology"], [27, 40, 22, 28], [28, 4, 23, 2, "feOffset"], [28, 12, 23, 10], [28, 14, 23, 12, "FeOffset"], [28, 32, 23, 20], [29, 4, 24, 2, "fePointLight"], [29, 16, 24, 14], [29, 18, 24, 16, "FePointLight"], [29, 40, 24, 28], [30, 4, 25, 2, "feSpecularLighting"], [30, 22, 25, 20], [30, 24, 25, 22, "FeSpecularLighting"], [30, 52, 25, 40], [31, 4, 26, 2, "feSpotLight"], [31, 15, 26, 13], [31, 17, 26, 15, "FeSpotLight"], [31, 38, 26, 26], [32, 4, 27, 2, "feTile"], [32, 10, 27, 8], [32, 12, 27, 10, "FeTile"], [32, 28, 27, 16], [33, 4, 28, 2, "feTurbulence"], [33, 16, 28, 14], [33, 18, 28, 16, "FeTurbulence"], [33, 40, 28, 28], [34, 4, 29, 2, "foreignObject"], [34, 17, 29, 15], [34, 19, 29, 17, "ForeignObject"], [34, 42, 29, 30], [35, 4, 30, 2, "g"], [35, 5, 30, 3], [35, 7, 30, 5, "G"], [35, 18, 30, 6], [36, 4, 31, 2, "image"], [36, 9, 31, 7], [36, 11, 31, 9, "Image"], [36, 26, 31, 14], [37, 4, 32, 2, "line"], [37, 8, 32, 6], [37, 10, 32, 8, "Line"], [37, 24, 32, 12], [38, 4, 33, 2, "linearGradient"], [38, 18, 33, 16], [38, 20, 33, 18, "LinearGradient"], [38, 44, 33, 32], [39, 4, 34, 2, "marker"], [39, 10, 34, 8], [39, 12, 34, 10, "<PERSON><PERSON>"], [39, 28, 34, 16], [40, 4, 35, 2, "mask"], [40, 8, 35, 6], [40, 10, 35, 8, "Mask"], [40, 24, 35, 12], [41, 4, 36, 2, "path"], [41, 8, 36, 6], [41, 10, 36, 8, "Path"], [41, 24, 36, 12], [42, 4, 37, 2, "pattern"], [42, 11, 37, 9], [42, 13, 37, 11, "Pattern"], [42, 30, 37, 18], [43, 4, 38, 2, "polygon"], [43, 11, 38, 9], [43, 13, 38, 11, "Polygon"], [43, 30, 38, 18], [44, 4, 39, 2, "polyline"], [44, 12, 39, 10], [44, 14, 39, 12, "Polyline"], [44, 32, 39, 20], [45, 4, 40, 2, "radialGradient"], [45, 18, 40, 16], [45, 20, 40, 18, "RadialGrad<PERSON>"], [45, 44, 40, 32], [46, 4, 41, 2, "rect"], [46, 8, 41, 6], [46, 10, 41, 8, "Rect"], [46, 24, 41, 12], [47, 4, 42, 2, "stop"], [47, 8, 42, 6], [47, 10, 42, 8, "Stop"], [47, 24, 42, 12], [48, 4, 43, 2, "svg"], [48, 7, 43, 5], [48, 9, 43, 7, "Svg"], [48, 22, 43, 10], [49, 4, 44, 2, "symbol"], [49, 10, 44, 8], [49, 12, 44, 10, "Symbol"], [49, 28, 44, 16], [50, 4, 45, 2, "text"], [50, 8, 45, 6], [50, 10, 45, 8, "Text"], [50, 24, 45, 12], [51, 4, 46, 2, "textPath"], [51, 12, 46, 10], [51, 14, 46, 12, "TextPath"], [51, 32, 46, 20], [52, 4, 47, 2, "tspan"], [52, 9, 47, 7], [52, 11, 47, 9, "TSpan"], [52, 26, 47, 14], [53, 4, 48, 2, "use"], [53, 7, 48, 5], [53, 9, 48, 7, "Use"], [54, 2, 49, 0], [54, 3, 49, 1], [55, 0, 49, 2], [55, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}