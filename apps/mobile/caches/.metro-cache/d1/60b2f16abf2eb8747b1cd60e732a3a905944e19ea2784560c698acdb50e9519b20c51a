{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Use a stub for MaskedView on all Platforms that don't support it.\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MaskedView = MaskedView;\n  function MaskedView({\n    children\n  }) {\n    return children;\n  }\n});", "lineCount": 16, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 2, 3, 0, "Object"], [7, 8, 3, 0], [7, 9, 3, 0, "defineProperty"], [7, 23, 3, 0], [7, 24, 3, 0, "exports"], [7, 31, 3, 0], [8, 4, 3, 0, "value"], [8, 9, 3, 0], [9, 2, 3, 0], [10, 2, 3, 0, "exports"], [10, 9, 3, 0], [10, 10, 3, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 20, 3, 0], [10, 23, 3, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 33, 3, 0], [11, 2, 7, 7], [11, 11, 7, 16, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 21, 7, 26, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 22, 7, 27], [12, 4, 8, 2, "children"], [13, 2, 9, 0], [13, 3, 9, 1], [13, 5, 9, 3], [14, 4, 10, 2], [14, 11, 10, 9, "children"], [14, 19, 10, 17], [15, 2, 11, 0], [16, 0, 11, 1], [16, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCM;CDI"}}, "type": "js/module"}]}