{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeModules/specs/NativeSourceCode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 127}, "end": {"line": 5, "column": 85, "index": 212}}], "key": "6sjsdQtHpMGffql6qBUZwd7gk9A=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getBundleUrl = getBundleUrl;\n  var _NativeSourceCode = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/NativeModules/specs/NativeSourceCode\"));\n  // Copyright 2015-present 650 Industries. All rights reserved.\n\n  /// <reference path=\"../ts-declarations/react-native.d.ts\" />\n\n  function getBundleUrl() {\n    var scriptURL = _NativeSourceCode.default.getConstants().scriptURL;\n    if (scriptURL == null) {\n      return null;\n    }\n    if (scriptURL.startsWith('/')) {\n      scriptURL = `file://${scriptURL}`;\n    }\n    var url = new URL(scriptURL);\n    return url.toString();\n  }\n});", "lineCount": 23, "map": [[7, 2, 5, 0], [7, 6, 5, 0, "_NativeSourceCode"], [7, 23, 5, 0], [7, 26, 5, 0, "_interopRequireDefault"], [7, 48, 5, 0], [7, 49, 5, 0, "require"], [7, 56, 5, 0], [7, 57, 5, 0, "_dependencyMap"], [7, 71, 5, 0], [8, 2, 1, 0], [10, 2, 3, 0], [12, 2, 7, 7], [12, 11, 7, 16, "getBundleUrl"], [12, 23, 7, 28, "getBundleUrl"], [12, 24, 7, 28], [12, 26, 7, 46], [13, 4, 8, 2], [13, 8, 8, 6, "scriptURL"], [13, 17, 8, 15], [13, 20, 8, 18, "SourceCode"], [13, 45, 8, 28], [13, 46, 8, 29, "getConstants"], [13, 58, 8, 41], [13, 59, 8, 42], [13, 60, 8, 43], [13, 61, 8, 44, "scriptURL"], [13, 70, 8, 53], [14, 4, 9, 2], [14, 8, 9, 6, "scriptURL"], [14, 17, 9, 15], [14, 21, 9, 19], [14, 25, 9, 23], [14, 27, 9, 25], [15, 6, 10, 4], [15, 13, 10, 11], [15, 17, 10, 15], [16, 4, 11, 2], [17, 4, 12, 2], [17, 8, 12, 6, "scriptURL"], [17, 17, 12, 15], [17, 18, 12, 16, "startsWith"], [17, 28, 12, 26], [17, 29, 12, 27], [17, 32, 12, 30], [17, 33, 12, 31], [17, 35, 12, 33], [18, 6, 13, 4, "scriptURL"], [18, 15, 13, 13], [18, 18, 13, 16], [18, 28, 13, 26, "scriptURL"], [18, 37, 13, 35], [18, 39, 13, 37], [19, 4, 14, 2], [20, 4, 15, 2], [20, 8, 15, 8, "url"], [20, 11, 15, 11], [20, 14, 15, 14], [20, 18, 15, 18, "URL"], [20, 21, 15, 21], [20, 22, 15, 22, "scriptURL"], [20, 31, 15, 31], [20, 32, 15, 32], [21, 4, 16, 2], [21, 11, 16, 9, "url"], [21, 14, 16, 12], [21, 15, 16, 13, "toString"], [21, 23, 16, 21], [21, 24, 16, 22], [21, 25, 16, 23], [22, 2, 17, 0], [23, 0, 17, 1], [23, 3]], "functionMap": {"names": ["<global>", "getBundleUrl"], "mappings": "AAA;OCM;CDU"}}, "type": "js/module"}]}