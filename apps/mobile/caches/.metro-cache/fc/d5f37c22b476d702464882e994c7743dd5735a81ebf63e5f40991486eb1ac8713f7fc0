{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 78, "index": 78}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./ExpoFontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 79}, "end": {"line": 2, "column": 46, "index": 125}}], "key": "7dk3JQGwGYesJt8OOG3pkBz+dtE=", "exportNames": ["*"]}}, {"name": "./Font.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 126}, "end": {"line": 3, "column": 43, "index": 169}}], "key": "iwvcxaVgfIXdww6iPrKSgtcaZy8=", "exportNames": ["*"]}}, {"name": "./FontLoader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 170}, "end": {"line": 4, "column": 70, "index": 240}}], "key": "ubgLNxOkixzH8pVapAwap9wQ8XU=", "exportNames": ["*"]}}, {"name": "./memory", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 241}, "end": {"line": 5, "column": 124, "index": 365}}], "key": "wlrMBDc1MVhnZOig0xhYu83J328=", "exportNames": ["*"]}}, {"name": "./server", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 366}, "end": {"line": 6, "column": 46, "index": 412}}], "key": "QinwpQMs5c8GY+myVdgXEzx55Tw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"FontDisplay\", {\n    enumerable: true,\n    get: function () {\n      return _Font.FontDisplay;\n    }\n  });\n  exports.getLoadedFonts = getLoadedFonts;\n  exports.isLoaded = isLoaded;\n  exports.isLoading = isLoading;\n  exports.loadAsync = loadAsync;\n  exports.unloadAllAsync = unloadAllAsync;\n  exports.unloadAsync = unloadAsync;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _ExpoFontLoader = _interopRequireDefault(require(_dependencyMap[2], \"./ExpoFontLoader\"));\n  var _Font = require(_dependencyMap[3], \"./Font.types\");\n  var _FontLoader = require(_dependencyMap[4], \"./FontLoader\");\n  var _memory = require(_dependencyMap[5], \"./memory\");\n  var _server = require(_dependencyMap[6], \"./server\");\n  // @needsAudit\n  /**\n   * Synchronously detect if the font for `fontFamily` has finished loading.\n   *\n   * @param fontFamily The name used to load the `FontResource`.\n   * @return Returns `true` if the font has fully loaded.\n   */\n  function isLoaded(fontFamily) {\n    if (_expoModulesCore.Platform.OS === 'web') {\n      return (0, _memory.isLoadedInCache)(fontFamily) || !!_ExpoFontLoader.default.isLoaded(fontFamily);\n    }\n    return (0, _memory.isLoadedNative)(fontFamily);\n  }\n  /**\n   * Synchronously get all the fonts that have been loaded.\n   * This includes fonts that were bundled at build time using the config plugin, as well as those loaded at runtime using `loadAsync`.\n   *\n   * @returns Returns array of strings which you can use as `fontFamily` [style prop](https://reactnative.dev/docs/text#style).\n   */\n  function getLoadedFonts() {\n    return _ExpoFontLoader.default.getLoadedFonts();\n  }\n  // @needsAudit\n  /**\n   * Synchronously detect if the font for `fontFamily` is still being loaded.\n   *\n   * @param fontFamily The name used to load the `FontResource`.\n   * @returns Returns `true` if the font is still loading.\n   */\n  function isLoading(fontFamily) {\n    return fontFamily in _memory.loadPromises;\n  }\n  // @needsAudit\n  /**\n   * An efficient method for loading fonts from static or remote resources which can then be used\n   * with the platform's native text elements. In the browser, this generates a `@font-face` block in\n   * a shared style sheet for fonts. No CSS is needed to use this method.\n   *\n   * > **Note**: We recommend using the [config plugin](#configuration-in-appjsonappconfigjs) instead whenever possible.\n   *\n   * @param fontFamilyOrFontMap String or map of values that can be used as the `fontFamily` [style prop](https://reactnative.dev/docs/text#style)\n   * with React Native `Text` elements.\n   * @param source The font asset that should be loaded into the `fontFamily` namespace.\n   *\n   * @return Returns a promise that fulfils when the font has loaded. Often you may want to wrap the\n   * method in a `try/catch/finally` to ensure the app continues if the font fails to load.\n   */\n  function loadAsync(fontFamilyOrFontMap, source) {\n    // NOTE(EvanBacon): Static render pass on web must be synchronous to collect all fonts.\n    // Because of this, `loadAsync` doesn't use the `async` keyword and deviates from the\n    // standard Expo SDK style guide.\n    const isServer = _expoModulesCore.Platform.OS === 'web' && typeof window === 'undefined';\n    if (typeof fontFamilyOrFontMap === 'object') {\n      if (source) {\n        return Promise.reject(new _expoModulesCore.CodedError(`ERR_FONT_API`, `No fontFamily can be used for the provided source: ${source}. The second argument of \\`loadAsync()\\` can only be used with a \\`string\\` value as the first argument.`));\n      }\n      const fontMap = fontFamilyOrFontMap;\n      const names = Object.keys(fontMap);\n      if (isServer) {\n        names.map(name => (0, _server.registerStaticFont)(name, fontMap[name]));\n        return Promise.resolve();\n      }\n      return Promise.all(names.map(name => loadFontInNamespaceAsync(name, fontMap[name]))).then(() => {});\n    }\n    if (isServer) {\n      (0, _server.registerStaticFont)(fontFamilyOrFontMap, source);\n      return Promise.resolve();\n    }\n    return loadFontInNamespaceAsync(fontFamilyOrFontMap, source);\n  }\n  async function loadFontInNamespaceAsync(fontFamily, source) {\n    if (!source) {\n      throw new _expoModulesCore.CodedError(`ERR_FONT_SOURCE`, `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`);\n    }\n    // we consult the native module to see if the font is already loaded\n    // this is slower than checking the cache but can help avoid loading the same font n times\n    if (isLoaded(fontFamily)) {\n      return;\n    }\n    if (_memory.loadPromises.hasOwnProperty(fontFamily)) {\n      return _memory.loadPromises[fontFamily];\n    }\n    // Important: we want all callers that concurrently try to load the same font to await the same\n    // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n    // promise in the program, we need to create the promise synchronously without yielding the event\n    // loop from this point.\n    const asset = (0, _FontLoader.getAssetForSource)(source);\n    _memory.loadPromises[fontFamily] = (async () => {\n      try {\n        await (0, _FontLoader.loadSingleFontAsync)(fontFamily, asset);\n        (0, _memory.markLoaded)(fontFamily);\n      } finally {\n        delete _memory.loadPromises[fontFamily];\n      }\n    })();\n    await _memory.loadPromises[fontFamily];\n  }\n  // @needsAudit\n  /**\n   * Unloads all the custom fonts. This is used for testing.\n   * @hidden\n   */\n  async function unloadAllAsync() {\n    if (!_ExpoFontLoader.default.unloadAllAsync) {\n      throw new _expoModulesCore.UnavailabilityError('expo-font', 'unloadAllAsync');\n    }\n    if (Object.keys(_memory.loadPromises).length) {\n      throw new _expoModulesCore.CodedError(`ERR_UNLOAD`, `Cannot unload fonts while they're still loading: ${Object.keys(_memory.loadPromises).join(', ')}`);\n    }\n    (0, _memory.purgeCache)();\n    await _ExpoFontLoader.default.unloadAllAsync();\n  }\n  // @needsAudit\n  /**\n   * Unload custom fonts matching the `fontFamily`s and display values provided.\n   * This is used for testing.\n   *\n   * @param fontFamilyOrFontMap The name or names of the custom fonts that will be unloaded.\n   * @param options When `fontFamilyOrFontMap` is a string, this should be the font source used to load\n   * the custom font originally.\n   * @hidden\n   */\n  async function unloadAsync(fontFamilyOrFontMap, options) {\n    if (!_ExpoFontLoader.default.unloadAsync) {\n      throw new _expoModulesCore.UnavailabilityError('expo-font', 'unloadAsync');\n    }\n    if (typeof fontFamilyOrFontMap === 'object') {\n      if (options) {\n        throw new _expoModulesCore.CodedError(`ERR_FONT_API`, `No fontFamily can be used for the provided options: ${options}. The second argument of \\`unloadAsync()\\` can only be used with a \\`string\\` value as the first argument.`);\n      }\n      const fontMap = fontFamilyOrFontMap;\n      const names = Object.keys(fontMap);\n      await Promise.all(names.map(name => unloadFontInNamespaceAsync(name, fontMap[name])));\n      return;\n    }\n    return await unloadFontInNamespaceAsync(fontFamilyOrFontMap, options);\n  }\n  async function unloadFontInNamespaceAsync(fontFamily, options) {\n    if (!isLoaded(fontFamily)) {\n      return;\n    } else {\n      (0, _memory.purgeFontFamilyFromCache)(fontFamily);\n    }\n    // Important: we want all callers that concurrently try to load the same font to await the same\n    // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n    // promise in the program, we need to create the promise synchronously without yielding the event\n    // loop from this point.\n    if (!fontFamily) {\n      throw new _expoModulesCore.CodedError(`ERR_FONT_FAMILY`, `Cannot unload an empty name`);\n    }\n    await _ExpoFontLoader.default.unloadAsync(fontFamily, options);\n  }\n});", "lineCount": 176, "map": [[18, 2, 1, 0], [18, 6, 1, 0, "_expoModulesCore"], [18, 22, 1, 0], [18, 25, 1, 0, "require"], [18, 32, 1, 0], [18, 33, 1, 0, "_dependencyMap"], [18, 47, 1, 0], [19, 2, 2, 0], [19, 6, 2, 0, "_ExpoFontLoader"], [19, 21, 2, 0], [19, 24, 2, 0, "_interopRequireDefault"], [19, 46, 2, 0], [19, 47, 2, 0, "require"], [19, 54, 2, 0], [19, 55, 2, 0, "_dependencyMap"], [19, 69, 2, 0], [20, 2, 3, 0], [20, 6, 3, 0, "_Font"], [20, 11, 3, 0], [20, 14, 3, 0, "require"], [20, 21, 3, 0], [20, 22, 3, 0, "_dependencyMap"], [20, 36, 3, 0], [21, 2, 4, 0], [21, 6, 4, 0, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 17, 4, 0], [21, 20, 4, 0, "require"], [21, 27, 4, 0], [21, 28, 4, 0, "_dependencyMap"], [21, 42, 4, 0], [22, 2, 5, 0], [22, 6, 5, 0, "_memory"], [22, 13, 5, 0], [22, 16, 5, 0, "require"], [22, 23, 5, 0], [22, 24, 5, 0, "_dependencyMap"], [22, 38, 5, 0], [23, 2, 6, 0], [23, 6, 6, 0, "_server"], [23, 13, 6, 0], [23, 16, 6, 0, "require"], [23, 23, 6, 0], [23, 24, 6, 0, "_dependencyMap"], [23, 38, 6, 0], [24, 2, 7, 0], [25, 2, 8, 0], [26, 0, 9, 0], [27, 0, 10, 0], [28, 0, 11, 0], [29, 0, 12, 0], [30, 0, 13, 0], [31, 2, 14, 7], [31, 11, 14, 16, "isLoaded"], [31, 19, 14, 24, "isLoaded"], [31, 20, 14, 25, "fontFamily"], [31, 30, 14, 35], [31, 32, 14, 37], [32, 4, 15, 4], [32, 8, 15, 8, "Platform"], [32, 33, 15, 16], [32, 34, 15, 17, "OS"], [32, 36, 15, 19], [32, 41, 15, 24], [32, 46, 15, 29], [32, 48, 15, 31], [33, 6, 16, 8], [33, 13, 16, 15], [33, 17, 16, 15, "isLoadedInCache"], [33, 40, 16, 30], [33, 42, 16, 31, "fontFamily"], [33, 52, 16, 41], [33, 53, 16, 42], [33, 57, 16, 46], [33, 58, 16, 47], [33, 59, 16, 48, "ExpoFontLoader"], [33, 82, 16, 62], [33, 83, 16, 63, "isLoaded"], [33, 91, 16, 71], [33, 92, 16, 72, "fontFamily"], [33, 102, 16, 82], [33, 103, 16, 83], [34, 4, 17, 4], [35, 4, 18, 4], [35, 11, 18, 11], [35, 15, 18, 11, "isLoadedNative"], [35, 37, 18, 25], [35, 39, 18, 26, "fontFamily"], [35, 49, 18, 36], [35, 50, 18, 37], [36, 2, 19, 0], [37, 2, 20, 0], [38, 0, 21, 0], [39, 0, 22, 0], [40, 0, 23, 0], [41, 0, 24, 0], [42, 0, 25, 0], [43, 2, 26, 7], [43, 11, 26, 16, "getLoadedFonts"], [43, 25, 26, 30, "getLoadedFonts"], [43, 26, 26, 30], [43, 28, 26, 33], [44, 4, 27, 4], [44, 11, 27, 11, "ExpoFontLoader"], [44, 34, 27, 25], [44, 35, 27, 26, "getLoadedFonts"], [44, 49, 27, 40], [44, 50, 27, 41], [44, 51, 27, 42], [45, 2, 28, 0], [46, 2, 29, 0], [47, 2, 30, 0], [48, 0, 31, 0], [49, 0, 32, 0], [50, 0, 33, 0], [51, 0, 34, 0], [52, 0, 35, 0], [53, 2, 36, 7], [53, 11, 36, 16, "isLoading"], [53, 20, 36, 25, "isLoading"], [53, 21, 36, 26, "fontFamily"], [53, 31, 36, 36], [53, 33, 36, 38], [54, 4, 37, 4], [54, 11, 37, 11, "fontFamily"], [54, 21, 37, 21], [54, 25, 37, 25, "loadPromises"], [54, 45, 37, 37], [55, 2, 38, 0], [56, 2, 39, 0], [57, 2, 40, 0], [58, 0, 41, 0], [59, 0, 42, 0], [60, 0, 43, 0], [61, 0, 44, 0], [62, 0, 45, 0], [63, 0, 46, 0], [64, 0, 47, 0], [65, 0, 48, 0], [66, 0, 49, 0], [67, 0, 50, 0], [68, 0, 51, 0], [69, 0, 52, 0], [70, 0, 53, 0], [71, 2, 54, 7], [71, 11, 54, 16, "loadAsync"], [71, 20, 54, 25, "loadAsync"], [71, 21, 54, 26, "fontFamilyOrFontMap"], [71, 40, 54, 45], [71, 42, 54, 47, "source"], [71, 48, 54, 53], [71, 50, 54, 55], [72, 4, 55, 4], [73, 4, 56, 4], [74, 4, 57, 4], [75, 4, 58, 4], [75, 10, 58, 10, "isServer"], [75, 18, 58, 18], [75, 21, 58, 21, "Platform"], [75, 46, 58, 29], [75, 47, 58, 30, "OS"], [75, 49, 58, 32], [75, 54, 58, 37], [75, 59, 58, 42], [75, 63, 58, 46], [75, 70, 58, 53, "window"], [75, 76, 58, 59], [75, 81, 58, 64], [75, 92, 58, 75], [76, 4, 59, 4], [76, 8, 59, 8], [76, 15, 59, 15, "fontFamilyOrFontMap"], [76, 34, 59, 34], [76, 39, 59, 39], [76, 47, 59, 47], [76, 49, 59, 49], [77, 6, 60, 8], [77, 10, 60, 12, "source"], [77, 16, 60, 18], [77, 18, 60, 20], [78, 8, 61, 12], [78, 15, 61, 19, "Promise"], [78, 22, 61, 26], [78, 23, 61, 27, "reject"], [78, 29, 61, 33], [78, 30, 61, 34], [78, 34, 61, 38, "CodedError"], [78, 61, 61, 48], [78, 62, 61, 49], [78, 76, 61, 63], [78, 78, 61, 65], [78, 132, 61, 119, "source"], [78, 138, 61, 125], [78, 244, 61, 231], [78, 245, 61, 232], [78, 246, 61, 233], [79, 6, 62, 8], [80, 6, 63, 8], [80, 12, 63, 14, "fontMap"], [80, 19, 63, 21], [80, 22, 63, 24, "fontFamilyOrFontMap"], [80, 41, 63, 43], [81, 6, 64, 8], [81, 12, 64, 14, "names"], [81, 17, 64, 19], [81, 20, 64, 22, "Object"], [81, 26, 64, 28], [81, 27, 64, 29, "keys"], [81, 31, 64, 33], [81, 32, 64, 34, "fontMap"], [81, 39, 64, 41], [81, 40, 64, 42], [82, 6, 65, 8], [82, 10, 65, 12, "isServer"], [82, 18, 65, 20], [82, 20, 65, 22], [83, 8, 66, 12, "names"], [83, 13, 66, 17], [83, 14, 66, 18, "map"], [83, 17, 66, 21], [83, 18, 66, 23, "name"], [83, 22, 66, 27], [83, 26, 66, 32], [83, 30, 66, 32, "registerStaticFont"], [83, 56, 66, 50], [83, 58, 66, 51, "name"], [83, 62, 66, 55], [83, 64, 66, 57, "fontMap"], [83, 71, 66, 64], [83, 72, 66, 65, "name"], [83, 76, 66, 69], [83, 77, 66, 70], [83, 78, 66, 71], [83, 79, 66, 72], [84, 8, 67, 12], [84, 15, 67, 19, "Promise"], [84, 22, 67, 26], [84, 23, 67, 27, "resolve"], [84, 30, 67, 34], [84, 31, 67, 35], [84, 32, 67, 36], [85, 6, 68, 8], [86, 6, 69, 8], [86, 13, 69, 15, "Promise"], [86, 20, 69, 22], [86, 21, 69, 23, "all"], [86, 24, 69, 26], [86, 25, 69, 27, "names"], [86, 30, 69, 32], [86, 31, 69, 33, "map"], [86, 34, 69, 36], [86, 35, 69, 38, "name"], [86, 39, 69, 42], [86, 43, 69, 47, "loadFontInNamespaceAsync"], [86, 67, 69, 71], [86, 68, 69, 72, "name"], [86, 72, 69, 76], [86, 74, 69, 78, "fontMap"], [86, 81, 69, 85], [86, 82, 69, 86, "name"], [86, 86, 69, 90], [86, 87, 69, 91], [86, 88, 69, 92], [86, 89, 69, 93], [86, 90, 69, 94], [86, 91, 69, 95, "then"], [86, 95, 69, 99], [86, 96, 69, 100], [86, 102, 69, 106], [86, 103, 69, 108], [86, 104, 69, 109], [86, 105, 69, 110], [87, 4, 70, 4], [88, 4, 71, 4], [88, 8, 71, 8, "isServer"], [88, 16, 71, 16], [88, 18, 71, 18], [89, 6, 72, 8], [89, 10, 72, 8, "registerStaticFont"], [89, 36, 72, 26], [89, 38, 72, 27, "fontFamilyOrFontMap"], [89, 57, 72, 46], [89, 59, 72, 48, "source"], [89, 65, 72, 54], [89, 66, 72, 55], [90, 6, 73, 8], [90, 13, 73, 15, "Promise"], [90, 20, 73, 22], [90, 21, 73, 23, "resolve"], [90, 28, 73, 30], [90, 29, 73, 31], [90, 30, 73, 32], [91, 4, 74, 4], [92, 4, 75, 4], [92, 11, 75, 11, "loadFontInNamespaceAsync"], [92, 35, 75, 35], [92, 36, 75, 36, "fontFamilyOrFontMap"], [92, 55, 75, 55], [92, 57, 75, 57, "source"], [92, 63, 75, 63], [92, 64, 75, 64], [93, 2, 76, 0], [94, 2, 77, 0], [94, 17, 77, 15, "loadFontInNamespaceAsync"], [94, 41, 77, 39, "loadFontInNamespaceAsync"], [94, 42, 77, 40, "fontFamily"], [94, 52, 77, 50], [94, 54, 77, 52, "source"], [94, 60, 77, 58], [94, 62, 77, 60], [95, 4, 78, 4], [95, 8, 78, 8], [95, 9, 78, 9, "source"], [95, 15, 78, 15], [95, 17, 78, 17], [96, 6, 79, 8], [96, 12, 79, 14], [96, 16, 79, 18, "CodedError"], [96, 43, 79, 28], [96, 44, 79, 29], [96, 61, 79, 46], [96, 63, 79, 48], [96, 112, 79, 97, "fontFamily"], [96, 122, 79, 107], [96, 128, 79, 113, "source"], [96, 134, 79, 119], [96, 204, 79, 189, "fontFamily"], [96, 214, 79, 199], [96, 217, 79, 202], [96, 218, 79, 203], [97, 4, 80, 4], [98, 4, 81, 4], [99, 4, 82, 4], [100, 4, 83, 4], [100, 8, 83, 8, "isLoaded"], [100, 16, 83, 16], [100, 17, 83, 17, "fontFamily"], [100, 27, 83, 27], [100, 28, 83, 28], [100, 30, 83, 30], [101, 6, 84, 8], [102, 4, 85, 4], [103, 4, 86, 4], [103, 8, 86, 8, "loadPromises"], [103, 28, 86, 20], [103, 29, 86, 21, "hasOwnProperty"], [103, 43, 86, 35], [103, 44, 86, 36, "fontFamily"], [103, 54, 86, 46], [103, 55, 86, 47], [103, 57, 86, 49], [104, 6, 87, 8], [104, 13, 87, 15, "loadPromises"], [104, 33, 87, 27], [104, 34, 87, 28, "fontFamily"], [104, 44, 87, 38], [104, 45, 87, 39], [105, 4, 88, 4], [106, 4, 89, 4], [107, 4, 90, 4], [108, 4, 91, 4], [109, 4, 92, 4], [110, 4, 93, 4], [110, 10, 93, 10, "asset"], [110, 15, 93, 15], [110, 18, 93, 18], [110, 22, 93, 18, "getAssetForSource"], [110, 51, 93, 35], [110, 53, 93, 36, "source"], [110, 59, 93, 42], [110, 60, 93, 43], [111, 4, 94, 4, "loadPromises"], [111, 24, 94, 16], [111, 25, 94, 17, "fontFamily"], [111, 35, 94, 27], [111, 36, 94, 28], [111, 39, 94, 31], [111, 40, 94, 32], [111, 52, 94, 44], [112, 6, 95, 8], [112, 10, 95, 12], [113, 8, 96, 12], [113, 14, 96, 18], [113, 18, 96, 18, "loadSingleFontAsync"], [113, 49, 96, 37], [113, 51, 96, 38, "fontFamily"], [113, 61, 96, 48], [113, 63, 96, 50, "asset"], [113, 68, 96, 55], [113, 69, 96, 56], [114, 8, 97, 12], [114, 12, 97, 12, "markLoaded"], [114, 30, 97, 22], [114, 32, 97, 23, "fontFamily"], [114, 42, 97, 33], [114, 43, 97, 34], [115, 6, 98, 8], [115, 7, 98, 9], [115, 16, 99, 16], [116, 8, 100, 12], [116, 15, 100, 19, "loadPromises"], [116, 35, 100, 31], [116, 36, 100, 32, "fontFamily"], [116, 46, 100, 42], [116, 47, 100, 43], [117, 6, 101, 8], [118, 4, 102, 4], [118, 5, 102, 5], [118, 7, 102, 7], [118, 8, 102, 8], [119, 4, 103, 4], [119, 10, 103, 10, "loadPromises"], [119, 30, 103, 22], [119, 31, 103, 23, "fontFamily"], [119, 41, 103, 33], [119, 42, 103, 34], [120, 2, 104, 0], [121, 2, 105, 0], [122, 2, 106, 0], [123, 0, 107, 0], [124, 0, 108, 0], [125, 0, 109, 0], [126, 2, 110, 7], [126, 17, 110, 22, "unloadAllAsync"], [126, 31, 110, 36, "unloadAllAsync"], [126, 32, 110, 36], [126, 34, 110, 39], [127, 4, 111, 4], [127, 8, 111, 8], [127, 9, 111, 9, "ExpoFontLoader"], [127, 32, 111, 23], [127, 33, 111, 24, "unloadAllAsync"], [127, 47, 111, 38], [127, 49, 111, 40], [128, 6, 112, 8], [128, 12, 112, 14], [128, 16, 112, 18, "UnavailabilityError"], [128, 52, 112, 37], [128, 53, 112, 38], [128, 64, 112, 49], [128, 66, 112, 51], [128, 82, 112, 67], [128, 83, 112, 68], [129, 4, 113, 4], [130, 4, 114, 4], [130, 8, 114, 8, "Object"], [130, 14, 114, 14], [130, 15, 114, 15, "keys"], [130, 19, 114, 19], [130, 20, 114, 20, "loadPromises"], [130, 40, 114, 32], [130, 41, 114, 33], [130, 42, 114, 34, "length"], [130, 48, 114, 40], [130, 50, 114, 42], [131, 6, 115, 8], [131, 12, 115, 14], [131, 16, 115, 18, "CodedError"], [131, 43, 115, 28], [131, 44, 115, 29], [131, 56, 115, 41], [131, 58, 115, 43], [131, 110, 115, 95, "Object"], [131, 116, 115, 101], [131, 117, 115, 102, "keys"], [131, 121, 115, 106], [131, 122, 115, 107, "loadPromises"], [131, 142, 115, 119], [131, 143, 115, 120], [131, 144, 115, 121, "join"], [131, 148, 115, 125], [131, 149, 115, 126], [131, 153, 115, 130], [131, 154, 115, 131], [131, 156, 115, 133], [131, 157, 115, 134], [132, 4, 116, 4], [133, 4, 117, 4], [133, 8, 117, 4, "purge<PERSON>ache"], [133, 26, 117, 14], [133, 28, 117, 15], [133, 29, 117, 16], [134, 4, 118, 4], [134, 10, 118, 10, "ExpoFontLoader"], [134, 33, 118, 24], [134, 34, 118, 25, "unloadAllAsync"], [134, 48, 118, 39], [134, 49, 118, 40], [134, 50, 118, 41], [135, 2, 119, 0], [136, 2, 120, 0], [137, 2, 121, 0], [138, 0, 122, 0], [139, 0, 123, 0], [140, 0, 124, 0], [141, 0, 125, 0], [142, 0, 126, 0], [143, 0, 127, 0], [144, 0, 128, 0], [145, 0, 129, 0], [146, 2, 130, 7], [146, 17, 130, 22, "unloadAsync"], [146, 28, 130, 33, "unloadAsync"], [146, 29, 130, 34, "fontFamilyOrFontMap"], [146, 48, 130, 53], [146, 50, 130, 55, "options"], [146, 57, 130, 62], [146, 59, 130, 64], [147, 4, 131, 4], [147, 8, 131, 8], [147, 9, 131, 9, "ExpoFontLoader"], [147, 32, 131, 23], [147, 33, 131, 24, "unloadAsync"], [147, 44, 131, 35], [147, 46, 131, 37], [148, 6, 132, 8], [148, 12, 132, 14], [148, 16, 132, 18, "UnavailabilityError"], [148, 52, 132, 37], [148, 53, 132, 38], [148, 64, 132, 49], [148, 66, 132, 51], [148, 79, 132, 64], [148, 80, 132, 65], [149, 4, 133, 4], [150, 4, 134, 4], [150, 8, 134, 8], [150, 15, 134, 15, "fontFamilyOrFontMap"], [150, 34, 134, 34], [150, 39, 134, 39], [150, 47, 134, 47], [150, 49, 134, 49], [151, 6, 135, 8], [151, 10, 135, 12, "options"], [151, 17, 135, 19], [151, 19, 135, 21], [152, 8, 136, 12], [152, 14, 136, 18], [152, 18, 136, 22, "CodedError"], [152, 45, 136, 32], [152, 46, 136, 33], [152, 60, 136, 47], [152, 62, 136, 49], [152, 117, 136, 104, "options"], [152, 124, 136, 111], [152, 232, 136, 219], [152, 233, 136, 220], [153, 6, 137, 8], [154, 6, 138, 8], [154, 12, 138, 14, "fontMap"], [154, 19, 138, 21], [154, 22, 138, 24, "fontFamilyOrFontMap"], [154, 41, 138, 43], [155, 6, 139, 8], [155, 12, 139, 14, "names"], [155, 17, 139, 19], [155, 20, 139, 22, "Object"], [155, 26, 139, 28], [155, 27, 139, 29, "keys"], [155, 31, 139, 33], [155, 32, 139, 34, "fontMap"], [155, 39, 139, 41], [155, 40, 139, 42], [156, 6, 140, 8], [156, 12, 140, 14, "Promise"], [156, 19, 140, 21], [156, 20, 140, 22, "all"], [156, 23, 140, 25], [156, 24, 140, 26, "names"], [156, 29, 140, 31], [156, 30, 140, 32, "map"], [156, 33, 140, 35], [156, 34, 140, 37, "name"], [156, 38, 140, 41], [156, 42, 140, 46, "unloadFontInNamespaceAsync"], [156, 68, 140, 72], [156, 69, 140, 73, "name"], [156, 73, 140, 77], [156, 75, 140, 79, "fontMap"], [156, 82, 140, 86], [156, 83, 140, 87, "name"], [156, 87, 140, 91], [156, 88, 140, 92], [156, 89, 140, 93], [156, 90, 140, 94], [156, 91, 140, 95], [157, 6, 141, 8], [158, 4, 142, 4], [159, 4, 143, 4], [159, 11, 143, 11], [159, 17, 143, 17, "unloadFontInNamespaceAsync"], [159, 43, 143, 43], [159, 44, 143, 44, "fontFamilyOrFontMap"], [159, 63, 143, 63], [159, 65, 143, 65, "options"], [159, 72, 143, 72], [159, 73, 143, 73], [160, 2, 144, 0], [161, 2, 145, 0], [161, 17, 145, 15, "unloadFontInNamespaceAsync"], [161, 43, 145, 41, "unloadFontInNamespaceAsync"], [161, 44, 145, 42, "fontFamily"], [161, 54, 145, 52], [161, 56, 145, 54, "options"], [161, 63, 145, 61], [161, 65, 145, 63], [162, 4, 146, 4], [162, 8, 146, 8], [162, 9, 146, 9, "isLoaded"], [162, 17, 146, 17], [162, 18, 146, 18, "fontFamily"], [162, 28, 146, 28], [162, 29, 146, 29], [162, 31, 146, 31], [163, 6, 147, 8], [164, 4, 148, 4], [164, 5, 148, 5], [164, 11, 149, 9], [165, 6, 150, 8], [165, 10, 150, 8, "purgeFontFamilyFromCache"], [165, 42, 150, 32], [165, 44, 150, 33, "fontFamily"], [165, 54, 150, 43], [165, 55, 150, 44], [166, 4, 151, 4], [167, 4, 152, 4], [168, 4, 153, 4], [169, 4, 154, 4], [170, 4, 155, 4], [171, 4, 156, 4], [171, 8, 156, 8], [171, 9, 156, 9, "fontFamily"], [171, 19, 156, 19], [171, 21, 156, 21], [172, 6, 157, 8], [172, 12, 157, 14], [172, 16, 157, 18, "CodedError"], [172, 43, 157, 28], [172, 44, 157, 29], [172, 61, 157, 46], [172, 63, 157, 48], [172, 92, 157, 77], [172, 93, 157, 78], [173, 4, 158, 4], [174, 4, 159, 4], [174, 10, 159, 10, "ExpoFontLoader"], [174, 33, 159, 24], [174, 34, 159, 25, "unloadAsync"], [174, 45, 159, 36], [174, 46, 159, 37, "fontFamily"], [174, 56, 159, 47], [174, 58, 159, 49, "options"], [174, 65, 159, 56], [174, 66, 159, 57], [175, 2, 160, 0], [176, 0, 160, 1], [176, 3]], "functionMap": {"names": ["<global>", "isLoaded", "getLoadedFonts", "isLoading", "loadAsync", "names.map$argument_0", "Promise.all.then$argument_0", "loadFontInNamespaceAsync", "<anonymous>", "unloadAllAsync", "unloadAsync", "unloadFontInNamespaceAsync"], "mappings": "AAA;OCa;CDK;OEO;CFE;OGQ;CHE;OIgB;sBCY,iDD;qCCG,uDD,QE,SF;CJO;AOC;gCCiB;KDQ;CPE;OSM;CTS;OUW;oCLU,yDK;CVI;AWC;CXe"}}, "type": "js/module"}]}