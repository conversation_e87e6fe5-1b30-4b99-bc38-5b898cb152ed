{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.GesturePropError = void 0;\n  class GesturePropError extends Error {\n    constructor(name, value, expectedType) {\n      super(`Invalid property \\`${name}: ${value}\\` expected \\`${expectedType}\\``);\n    }\n  }\n  exports.GesturePropError = GesturePropError;\n});", "lineCount": 12, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "GesturePropError"], [6, 24, 1, 29], [6, 33, 1, 38, "Error"], [6, 38, 1, 43], [6, 39, 1, 44], [7, 4, 2, 2, "constructor"], [7, 15, 2, 13, "constructor"], [7, 16, 2, 14, "name"], [7, 20, 2, 18], [7, 22, 2, 20, "value"], [7, 27, 2, 25], [7, 29, 2, 27, "expectedType"], [7, 41, 2, 39], [7, 43, 2, 41], [8, 6, 3, 4], [8, 11, 3, 9], [8, 12, 3, 10], [8, 34, 3, 32, "name"], [8, 38, 3, 36], [8, 43, 3, 41, "value"], [8, 48, 3, 46], [8, 65, 3, 63, "expectedType"], [8, 77, 3, 75], [8, 81, 3, 79], [8, 82, 3, 80], [9, 4, 4, 2], [10, 2, 6, 0], [11, 2, 6, 1, "exports"], [11, 9, 6, 1], [11, 10, 6, 1, "GesturePropError"], [11, 26, 6, 1], [11, 29, 6, 1, "GesturePropError"], [11, 45, 6, 1], [12, 0, 6, 1], [12, 3]], "functionMap": {"names": ["<global>", "GesturePropError", "GesturePropError#constructor"], "mappings": "AAA,OC;ECC;GDE;CDE"}}, "type": "js/module"}]}