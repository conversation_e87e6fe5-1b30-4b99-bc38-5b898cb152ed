{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.goBack = goBack;\n  exports.navigate = navigate;\n  exports.navigateDeprecated = navigateDeprecated;\n  exports.preload = preload;\n  exports.replaceParams = replaceParams;\n  exports.reset = reset;\n  exports.setParams = setParams;\n  function goBack() {\n    return {\n      type: 'GO_BACK'\n    };\n  }\n  function navigate() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (typeof args[0] === 'string') {\n      var name = args[0],\n        params = args[1],\n        options = args[2];\n      if (typeof options === 'boolean') {\n        console.warn(`Passing a boolean as the third argument to 'navigate' is deprecated. Pass '{ merge: true }' instead.`);\n      }\n      return {\n        type: 'NAVIGATE',\n        payload: {\n          name,\n          params,\n          merge: typeof options === 'boolean' ? options : options?.merge,\n          pop: options?.pop\n        }\n      };\n    } else {\n      var payload = args[0] || {};\n      if (!('name' in payload)) {\n        throw new Error('You need to specify a name when calling navigate with an object as the argument. See https://reactnavigation.org/docs/navigation-actions#navigate for usage.');\n      }\n      return {\n        type: 'NAVIGATE',\n        payload\n      };\n    }\n  }\n  function navigateDeprecated() {\n    if (typeof (arguments.length <= 0 ? undefined : arguments[0]) === 'string') {\n      return {\n        type: 'NAVIGATE_DEPRECATED',\n        payload: {\n          name: arguments.length <= 0 ? undefined : arguments[0],\n          params: arguments.length <= 1 ? undefined : arguments[1]\n        }\n      };\n    } else {\n      var payload = (arguments.length <= 0 ? undefined : arguments[0]) || {};\n      if (!('name' in payload)) {\n        throw new Error('You need to specify a name when calling navigateDeprecated with an object as the argument. See https://reactnavigation.org/docs/navigation-actions#navigatelegacy for usage.');\n      }\n      return {\n        type: 'NAVIGATE_DEPRECATED',\n        payload\n      };\n    }\n  }\n  function reset(state) {\n    return {\n      type: 'RESET',\n      payload: state\n    };\n  }\n  function setParams(params) {\n    return {\n      type: 'SET_PARAMS',\n      payload: {\n        params\n      }\n    };\n  }\n  function replaceParams(params) {\n    return {\n      type: 'REPLACE_PARAMS',\n      payload: {\n        params\n      }\n    };\n  }\n  function preload(name, params) {\n    return {\n      type: 'PRELOAD',\n      payload: {\n        name,\n        params\n      }\n    };\n  }\n});", "lineCount": 101, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "goBack"], [7, 16, 1, 13], [7, 19, 1, 13, "goBack"], [7, 25, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "navigate"], [8, 18, 1, 13], [8, 21, 1, 13, "navigate"], [8, 29, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "navigateDeprecated"], [9, 28, 1, 13], [9, 31, 1, 13, "navigateDeprecated"], [9, 49, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "preload"], [10, 17, 1, 13], [10, 20, 1, 13, "preload"], [10, 27, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "replaceParams"], [11, 23, 1, 13], [11, 26, 1, 13, "replaceParams"], [11, 39, 1, 13], [12, 2, 1, 13, "exports"], [12, 9, 1, 13], [12, 10, 1, 13, "reset"], [12, 15, 1, 13], [12, 18, 1, 13, "reset"], [12, 23, 1, 13], [13, 2, 1, 13, "exports"], [13, 9, 1, 13], [13, 10, 1, 13, "setParams"], [13, 19, 1, 13], [13, 22, 1, 13, "setParams"], [13, 31, 1, 13], [14, 2, 3, 7], [14, 11, 3, 16, "goBack"], [14, 17, 3, 22, "goBack"], [14, 18, 3, 22], [14, 20, 3, 25], [15, 4, 4, 2], [15, 11, 4, 9], [16, 6, 5, 4, "type"], [16, 10, 5, 8], [16, 12, 5, 10], [17, 4, 6, 2], [17, 5, 6, 3], [18, 2, 7, 0], [19, 2, 8, 7], [19, 11, 8, 16, "navigate"], [19, 19, 8, 24, "navigate"], [19, 20, 8, 24], [19, 22, 8, 34], [20, 4, 8, 34], [20, 13, 8, 34, "_len"], [20, 17, 8, 34], [20, 20, 8, 34, "arguments"], [20, 29, 8, 34], [20, 30, 8, 34, "length"], [20, 36, 8, 34], [20, 38, 8, 28, "args"], [20, 42, 8, 32], [20, 49, 8, 32, "Array"], [20, 54, 8, 32], [20, 55, 8, 32, "_len"], [20, 59, 8, 32], [20, 62, 8, 32, "_key"], [20, 66, 8, 32], [20, 72, 8, 32, "_key"], [20, 76, 8, 32], [20, 79, 8, 32, "_len"], [20, 83, 8, 32], [20, 85, 8, 32, "_key"], [20, 89, 8, 32], [21, 6, 8, 28, "args"], [21, 10, 8, 32], [21, 11, 8, 32, "_key"], [21, 15, 8, 32], [21, 19, 8, 32, "arguments"], [21, 28, 8, 32], [21, 29, 8, 32, "_key"], [21, 33, 8, 32], [22, 4, 8, 32], [23, 4, 9, 2], [23, 8, 9, 6], [23, 15, 9, 13, "args"], [23, 19, 9, 17], [23, 20, 9, 18], [23, 21, 9, 19], [23, 22, 9, 20], [23, 27, 9, 25], [23, 35, 9, 33], [23, 37, 9, 35], [24, 6, 10, 4], [24, 10, 10, 11, "name"], [24, 14, 10, 15], [24, 17, 10, 36, "args"], [24, 21, 10, 40], [25, 8, 10, 17, "params"], [25, 14, 10, 23], [25, 17, 10, 36, "args"], [25, 21, 10, 40], [26, 8, 10, 25, "options"], [26, 15, 10, 32], [26, 18, 10, 36, "args"], [26, 22, 10, 40], [27, 6, 11, 4], [27, 10, 11, 8], [27, 17, 11, 15, "options"], [27, 24, 11, 22], [27, 29, 11, 27], [27, 38, 11, 36], [27, 40, 11, 38], [28, 8, 12, 6, "console"], [28, 15, 12, 13], [28, 16, 12, 14, "warn"], [28, 20, 12, 18], [28, 21, 12, 19], [28, 123, 12, 121], [28, 124, 12, 122], [29, 6, 13, 4], [30, 6, 14, 4], [30, 13, 14, 11], [31, 8, 15, 6, "type"], [31, 12, 15, 10], [31, 14, 15, 12], [31, 24, 15, 22], [32, 8, 16, 6, "payload"], [32, 15, 16, 13], [32, 17, 16, 15], [33, 10, 17, 8, "name"], [33, 14, 17, 12], [34, 10, 18, 8, "params"], [34, 16, 18, 14], [35, 10, 19, 8, "merge"], [35, 15, 19, 13], [35, 17, 19, 15], [35, 24, 19, 22, "options"], [35, 31, 19, 29], [35, 36, 19, 34], [35, 45, 19, 43], [35, 48, 19, 46, "options"], [35, 55, 19, 53], [35, 58, 19, 56, "options"], [35, 65, 19, 63], [35, 67, 19, 65, "merge"], [35, 72, 19, 70], [36, 10, 20, 8, "pop"], [36, 13, 20, 11], [36, 15, 20, 13, "options"], [36, 22, 20, 20], [36, 24, 20, 22, "pop"], [37, 8, 21, 6], [38, 6, 22, 4], [38, 7, 22, 5], [39, 4, 23, 2], [39, 5, 23, 3], [39, 11, 23, 9], [40, 6, 24, 4], [40, 10, 24, 10, "payload"], [40, 17, 24, 17], [40, 20, 24, 20, "args"], [40, 24, 24, 24], [40, 25, 24, 25], [40, 26, 24, 26], [40, 27, 24, 27], [40, 31, 24, 31], [40, 32, 24, 32], [40, 33, 24, 33], [41, 6, 25, 4], [41, 10, 25, 8], [41, 12, 25, 10], [41, 18, 25, 16], [41, 22, 25, 20, "payload"], [41, 29, 25, 27], [41, 30, 25, 28], [41, 32, 25, 30], [42, 8, 26, 6], [42, 14, 26, 12], [42, 18, 26, 16, "Error"], [42, 23, 26, 21], [42, 24, 26, 22], [42, 182, 26, 180], [42, 183, 26, 181], [43, 6, 27, 4], [44, 6, 28, 4], [44, 13, 28, 11], [45, 8, 29, 6, "type"], [45, 12, 29, 10], [45, 14, 29, 12], [45, 24, 29, 22], [46, 8, 30, 6, "payload"], [47, 6, 31, 4], [47, 7, 31, 5], [48, 4, 32, 2], [49, 2, 33, 0], [50, 2, 34, 7], [50, 11, 34, 16, "navigateDeprecated"], [50, 29, 34, 34, "navigateDeprecated"], [50, 30, 34, 34], [50, 32, 34, 44], [51, 4, 35, 2], [51, 8, 35, 6], [51, 16, 35, 6, "arguments"], [51, 25, 35, 6], [51, 26, 35, 6, "length"], [51, 32, 35, 6], [51, 40, 35, 6, "undefined"], [51, 49, 35, 6], [51, 52, 35, 6, "arguments"], [51, 61, 35, 6], [51, 65, 35, 20], [51, 70, 35, 25], [51, 78, 35, 33], [51, 80, 35, 35], [52, 6, 36, 4], [52, 13, 36, 11], [53, 8, 37, 6, "type"], [53, 12, 37, 10], [53, 14, 37, 12], [53, 35, 37, 33], [54, 8, 38, 6, "payload"], [54, 15, 38, 13], [54, 17, 38, 15], [55, 10, 39, 8, "name"], [55, 14, 39, 12], [55, 16, 39, 12, "arguments"], [55, 25, 39, 12], [55, 26, 39, 12, "length"], [55, 32, 39, 12], [55, 40, 39, 12, "undefined"], [55, 49, 39, 12], [55, 52, 39, 12, "arguments"], [55, 61, 39, 12], [55, 64, 39, 21], [56, 10, 40, 8, "params"], [56, 16, 40, 14], [56, 18, 40, 14, "arguments"], [56, 27, 40, 14], [56, 28, 40, 14, "length"], [56, 34, 40, 14], [56, 42, 40, 14, "undefined"], [56, 51, 40, 14], [56, 54, 40, 14, "arguments"], [56, 63, 40, 14], [57, 8, 41, 6], [58, 6, 42, 4], [58, 7, 42, 5], [59, 4, 43, 2], [59, 5, 43, 3], [59, 11, 43, 9], [60, 6, 44, 4], [60, 10, 44, 10, "payload"], [60, 17, 44, 17], [60, 20, 44, 20], [60, 21, 44, 20, "arguments"], [60, 30, 44, 20], [60, 31, 44, 20, "length"], [60, 37, 44, 20], [60, 45, 44, 20, "undefined"], [60, 54, 44, 20], [60, 57, 44, 20, "arguments"], [60, 66, 44, 20], [60, 74, 44, 31], [60, 75, 44, 32], [60, 76, 44, 33], [61, 6, 45, 4], [61, 10, 45, 8], [61, 12, 45, 10], [61, 18, 45, 16], [61, 22, 45, 20, "payload"], [61, 29, 45, 27], [61, 30, 45, 28], [61, 32, 45, 30], [62, 8, 46, 6], [62, 14, 46, 12], [62, 18, 46, 16, "Error"], [62, 23, 46, 21], [62, 24, 46, 22], [62, 198, 46, 196], [62, 199, 46, 197], [63, 6, 47, 4], [64, 6, 48, 4], [64, 13, 48, 11], [65, 8, 49, 6, "type"], [65, 12, 49, 10], [65, 14, 49, 12], [65, 35, 49, 33], [66, 8, 50, 6, "payload"], [67, 6, 51, 4], [67, 7, 51, 5], [68, 4, 52, 2], [69, 2, 53, 0], [70, 2, 54, 7], [70, 11, 54, 16, "reset"], [70, 16, 54, 21, "reset"], [70, 17, 54, 22, "state"], [70, 22, 54, 27], [70, 24, 54, 29], [71, 4, 55, 2], [71, 11, 55, 9], [72, 6, 56, 4, "type"], [72, 10, 56, 8], [72, 12, 56, 10], [72, 19, 56, 17], [73, 6, 57, 4, "payload"], [73, 13, 57, 11], [73, 15, 57, 13, "state"], [74, 4, 58, 2], [74, 5, 58, 3], [75, 2, 59, 0], [76, 2, 60, 7], [76, 11, 60, 16, "setParams"], [76, 20, 60, 25, "setParams"], [76, 21, 60, 26, "params"], [76, 27, 60, 32], [76, 29, 60, 34], [77, 4, 61, 2], [77, 11, 61, 9], [78, 6, 62, 4, "type"], [78, 10, 62, 8], [78, 12, 62, 10], [78, 24, 62, 22], [79, 6, 63, 4, "payload"], [79, 13, 63, 11], [79, 15, 63, 13], [80, 8, 64, 6, "params"], [81, 6, 65, 4], [82, 4, 66, 2], [82, 5, 66, 3], [83, 2, 67, 0], [84, 2, 68, 7], [84, 11, 68, 16, "replaceParams"], [84, 24, 68, 29, "replaceParams"], [84, 25, 68, 30, "params"], [84, 31, 68, 36], [84, 33, 68, 38], [85, 4, 69, 2], [85, 11, 69, 9], [86, 6, 70, 4, "type"], [86, 10, 70, 8], [86, 12, 70, 10], [86, 28, 70, 26], [87, 6, 71, 4, "payload"], [87, 13, 71, 11], [87, 15, 71, 13], [88, 8, 72, 6, "params"], [89, 6, 73, 4], [90, 4, 74, 2], [90, 5, 74, 3], [91, 2, 75, 0], [92, 2, 76, 7], [92, 11, 76, 16, "preload"], [92, 18, 76, 23, "preload"], [92, 19, 76, 24, "name"], [92, 23, 76, 28], [92, 25, 76, 30, "params"], [92, 31, 76, 36], [92, 33, 76, 38], [93, 4, 77, 2], [93, 11, 77, 9], [94, 6, 78, 4, "type"], [94, 10, 78, 8], [94, 12, 78, 10], [94, 21, 78, 19], [95, 6, 79, 4, "payload"], [95, 13, 79, 11], [95, 15, 79, 13], [96, 8, 80, 6, "name"], [96, 12, 80, 10], [97, 8, 81, 6, "params"], [98, 6, 82, 4], [99, 4, 83, 2], [99, 5, 83, 3], [100, 2, 84, 0], [101, 0, 84, 1], [101, 3]], "functionMap": {"names": ["<global>", "goBack", "navigate", "navigateDeprecated", "reset", "setParams", "replaceParams", "preload"], "mappings": "AAA;OCE;CDI;OEC;CFyB;OGC;CHmB;OIC;CJK;OKC;CLO;OMC;CNO;OOC;CPQ"}}, "type": "js/module"}]}