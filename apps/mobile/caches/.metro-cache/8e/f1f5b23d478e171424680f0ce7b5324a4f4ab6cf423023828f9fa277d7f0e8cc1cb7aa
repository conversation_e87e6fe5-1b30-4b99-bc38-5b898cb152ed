{"dependencies": [{"name": "../animationParser.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 74, "index": 89}}], "key": "O2GgmGIlz6MOk52iJY+MJ4hFpWQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RollOutData = exports.RollOut = exports.RollInData = exports.RollIn = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser.js\");\n  const DEFAULT_ROLL_TIME = 0.3;\n  const RollInData = exports.RollInData = {\n    RollInLeft: {\n      name: 'RollInLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '-100vw',\n            rotate: '-180deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    },\n    RollInRight: {\n      name: 'RollInRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '100vw',\n            rotate: '180deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    }\n  };\n  const RollOutData = exports.RollOutData = {\n    RollOutLeft: {\n      name: 'RollOutLeft',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '-100vw',\n            rotate: '-180deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    },\n    RollOutRight: {\n      name: 'RollOutRight',\n      style: {\n        0: {\n          transform: [{\n            translateX: '0vw',\n            rotate: '0deg'\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '100vw',\n            rotate: '180deg'\n          }]\n        }\n      },\n      duration: DEFAULT_ROLL_TIME\n    }\n  };\n  const RollIn = exports.RollIn = {\n    RollInLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollInData.RollInLeft),\n      duration: RollInData.RollInLeft.duration\n    },\n    RollInRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollInData.RollInRight),\n      duration: RollInData.RollInRight.duration\n    }\n  };\n  const RollOut = exports.RollOut = {\n    RollOutLeft: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollOutData.RollOutLeft),\n      duration: RollOutData.RollOutLeft.duration\n    },\n    RollOutRight: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(RollOutData.RollOutRight),\n      duration: RollOutData.RollOutRight.duration\n    }\n  };\n});", "lineCount": 106, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "RollOutData"], [7, 21, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [7, 32, 1, 13, "RollOut"], [7, 39, 1, 13], [7, 42, 1, 13, "exports"], [7, 49, 1, 13], [7, 50, 1, 13, "RollInData"], [7, 60, 1, 13], [7, 63, 1, 13, "exports"], [7, 70, 1, 13], [7, 71, 1, 13, "RollIn"], [7, 77, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_animation<PERSON><PERSON>er"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "DEFAULT_ROLL_TIME"], [9, 25, 4, 23], [9, 28, 4, 26], [9, 31, 4, 29], [10, 2, 5, 7], [10, 8, 5, 13, "RollInData"], [10, 18, 5, 23], [10, 21, 5, 23, "exports"], [10, 28, 5, 23], [10, 29, 5, 23, "RollInData"], [10, 39, 5, 23], [10, 42, 5, 26], [11, 4, 6, 2, "RollInLeft"], [11, 14, 6, 12], [11, 16, 6, 14], [12, 6, 7, 4, "name"], [12, 10, 7, 8], [12, 12, 7, 10], [12, 24, 7, 22], [13, 6, 8, 4, "style"], [13, 11, 8, 9], [13, 13, 8, 11], [14, 8, 9, 6], [14, 9, 9, 7], [14, 11, 9, 9], [15, 10, 10, 8, "transform"], [15, 19, 10, 17], [15, 21, 10, 19], [15, 22, 10, 20], [16, 12, 11, 10, "translateX"], [16, 22, 11, 20], [16, 24, 11, 22], [16, 32, 11, 30], [17, 12, 12, 10, "rotate"], [17, 18, 12, 16], [17, 20, 12, 18], [18, 10, 13, 8], [18, 11, 13, 9], [19, 8, 14, 6], [19, 9, 14, 7], [20, 8, 15, 6], [20, 11, 15, 9], [20, 13, 15, 11], [21, 10, 16, 8, "transform"], [21, 19, 16, 17], [21, 21, 16, 19], [21, 22, 16, 20], [22, 12, 17, 10, "translateX"], [22, 22, 17, 20], [22, 24, 17, 22], [22, 29, 17, 27], [23, 12, 18, 10, "rotate"], [23, 18, 18, 16], [23, 20, 18, 18], [24, 10, 19, 8], [24, 11, 19, 9], [25, 8, 20, 6], [26, 6, 21, 4], [26, 7, 21, 5], [27, 6, 22, 4, "duration"], [27, 14, 22, 12], [27, 16, 22, 14, "DEFAULT_ROLL_TIME"], [28, 4, 23, 2], [28, 5, 23, 3], [29, 4, 24, 2, "RollInRight"], [29, 15, 24, 13], [29, 17, 24, 15], [30, 6, 25, 4, "name"], [30, 10, 25, 8], [30, 12, 25, 10], [30, 25, 25, 23], [31, 6, 26, 4, "style"], [31, 11, 26, 9], [31, 13, 26, 11], [32, 8, 27, 6], [32, 9, 27, 7], [32, 11, 27, 9], [33, 10, 28, 8, "transform"], [33, 19, 28, 17], [33, 21, 28, 19], [33, 22, 28, 20], [34, 12, 29, 10, "translateX"], [34, 22, 29, 20], [34, 24, 29, 22], [34, 31, 29, 29], [35, 12, 30, 10, "rotate"], [35, 18, 30, 16], [35, 20, 30, 18], [36, 10, 31, 8], [36, 11, 31, 9], [37, 8, 32, 6], [37, 9, 32, 7], [38, 8, 33, 6], [38, 11, 33, 9], [38, 13, 33, 11], [39, 10, 34, 8, "transform"], [39, 19, 34, 17], [39, 21, 34, 19], [39, 22, 34, 20], [40, 12, 35, 10, "translateX"], [40, 22, 35, 20], [40, 24, 35, 22], [40, 29, 35, 27], [41, 12, 36, 10, "rotate"], [41, 18, 36, 16], [41, 20, 36, 18], [42, 10, 37, 8], [42, 11, 37, 9], [43, 8, 38, 6], [44, 6, 39, 4], [44, 7, 39, 5], [45, 6, 40, 4, "duration"], [45, 14, 40, 12], [45, 16, 40, 14, "DEFAULT_ROLL_TIME"], [46, 4, 41, 2], [47, 2, 42, 0], [47, 3, 42, 1], [48, 2, 43, 7], [48, 8, 43, 13, "RollOutData"], [48, 19, 43, 24], [48, 22, 43, 24, "exports"], [48, 29, 43, 24], [48, 30, 43, 24, "RollOutData"], [48, 41, 43, 24], [48, 44, 43, 27], [49, 4, 44, 2, "RollOutLeft"], [49, 15, 44, 13], [49, 17, 44, 15], [50, 6, 45, 4, "name"], [50, 10, 45, 8], [50, 12, 45, 10], [50, 25, 45, 23], [51, 6, 46, 4, "style"], [51, 11, 46, 9], [51, 13, 46, 11], [52, 8, 47, 6], [52, 9, 47, 7], [52, 11, 47, 9], [53, 10, 48, 8, "transform"], [53, 19, 48, 17], [53, 21, 48, 19], [53, 22, 48, 20], [54, 12, 49, 10, "translateX"], [54, 22, 49, 20], [54, 24, 49, 22], [54, 29, 49, 27], [55, 12, 50, 10, "rotate"], [55, 18, 50, 16], [55, 20, 50, 18], [56, 10, 51, 8], [56, 11, 51, 9], [57, 8, 52, 6], [57, 9, 52, 7], [58, 8, 53, 6], [58, 11, 53, 9], [58, 13, 53, 11], [59, 10, 54, 8, "transform"], [59, 19, 54, 17], [59, 21, 54, 19], [59, 22, 54, 20], [60, 12, 55, 10, "translateX"], [60, 22, 55, 20], [60, 24, 55, 22], [60, 32, 55, 30], [61, 12, 56, 10, "rotate"], [61, 18, 56, 16], [61, 20, 56, 18], [62, 10, 57, 8], [62, 11, 57, 9], [63, 8, 58, 6], [64, 6, 59, 4], [64, 7, 59, 5], [65, 6, 60, 4, "duration"], [65, 14, 60, 12], [65, 16, 60, 14, "DEFAULT_ROLL_TIME"], [66, 4, 61, 2], [66, 5, 61, 3], [67, 4, 62, 2, "RollOutRight"], [67, 16, 62, 14], [67, 18, 62, 16], [68, 6, 63, 4, "name"], [68, 10, 63, 8], [68, 12, 63, 10], [68, 26, 63, 24], [69, 6, 64, 4, "style"], [69, 11, 64, 9], [69, 13, 64, 11], [70, 8, 65, 6], [70, 9, 65, 7], [70, 11, 65, 9], [71, 10, 66, 8, "transform"], [71, 19, 66, 17], [71, 21, 66, 19], [71, 22, 66, 20], [72, 12, 67, 10, "translateX"], [72, 22, 67, 20], [72, 24, 67, 22], [72, 29, 67, 27], [73, 12, 68, 10, "rotate"], [73, 18, 68, 16], [73, 20, 68, 18], [74, 10, 69, 8], [74, 11, 69, 9], [75, 8, 70, 6], [75, 9, 70, 7], [76, 8, 71, 6], [76, 11, 71, 9], [76, 13, 71, 11], [77, 10, 72, 8, "transform"], [77, 19, 72, 17], [77, 21, 72, 19], [77, 22, 72, 20], [78, 12, 73, 10, "translateX"], [78, 22, 73, 20], [78, 24, 73, 22], [78, 31, 73, 29], [79, 12, 74, 10, "rotate"], [79, 18, 74, 16], [79, 20, 74, 18], [80, 10, 75, 8], [80, 11, 75, 9], [81, 8, 76, 6], [82, 6, 77, 4], [82, 7, 77, 5], [83, 6, 78, 4, "duration"], [83, 14, 78, 12], [83, 16, 78, 14, "DEFAULT_ROLL_TIME"], [84, 4, 79, 2], [85, 2, 80, 0], [85, 3, 80, 1], [86, 2, 81, 7], [86, 8, 81, 13, "RollIn"], [86, 14, 81, 19], [86, 17, 81, 19, "exports"], [86, 24, 81, 19], [86, 25, 81, 19, "RollIn"], [86, 31, 81, 19], [86, 34, 81, 22], [87, 4, 82, 2, "RollInLeft"], [87, 14, 82, 12], [87, 16, 82, 14], [88, 6, 83, 4, "style"], [88, 11, 83, 9], [88, 13, 83, 11], [88, 17, 83, 11, "convertAnimationObjectToKeyframes"], [88, 67, 83, 44], [88, 69, 83, 45, "RollInData"], [88, 79, 83, 55], [88, 80, 83, 56, "RollInLeft"], [88, 90, 83, 66], [88, 91, 83, 67], [89, 6, 84, 4, "duration"], [89, 14, 84, 12], [89, 16, 84, 14, "RollInData"], [89, 26, 84, 24], [89, 27, 84, 25, "RollInLeft"], [89, 37, 84, 35], [89, 38, 84, 36, "duration"], [90, 4, 85, 2], [90, 5, 85, 3], [91, 4, 86, 2, "RollInRight"], [91, 15, 86, 13], [91, 17, 86, 15], [92, 6, 87, 4, "style"], [92, 11, 87, 9], [92, 13, 87, 11], [92, 17, 87, 11, "convertAnimationObjectToKeyframes"], [92, 67, 87, 44], [92, 69, 87, 45, "RollInData"], [92, 79, 87, 55], [92, 80, 87, 56, "RollInRight"], [92, 91, 87, 67], [92, 92, 87, 68], [93, 6, 88, 4, "duration"], [93, 14, 88, 12], [93, 16, 88, 14, "RollInData"], [93, 26, 88, 24], [93, 27, 88, 25, "RollInRight"], [93, 38, 88, 36], [93, 39, 88, 37, "duration"], [94, 4, 89, 2], [95, 2, 90, 0], [95, 3, 90, 1], [96, 2, 91, 7], [96, 8, 91, 13, "RollOut"], [96, 15, 91, 20], [96, 18, 91, 20, "exports"], [96, 25, 91, 20], [96, 26, 91, 20, "RollOut"], [96, 33, 91, 20], [96, 36, 91, 23], [97, 4, 92, 2, "RollOutLeft"], [97, 15, 92, 13], [97, 17, 92, 15], [98, 6, 93, 4, "style"], [98, 11, 93, 9], [98, 13, 93, 11], [98, 17, 93, 11, "convertAnimationObjectToKeyframes"], [98, 67, 93, 44], [98, 69, 93, 45, "RollOutData"], [98, 80, 93, 56], [98, 81, 93, 57, "RollOutLeft"], [98, 92, 93, 68], [98, 93, 93, 69], [99, 6, 94, 4, "duration"], [99, 14, 94, 12], [99, 16, 94, 14, "RollOutData"], [99, 27, 94, 25], [99, 28, 94, 26, "RollOutLeft"], [99, 39, 94, 37], [99, 40, 94, 38, "duration"], [100, 4, 95, 2], [100, 5, 95, 3], [101, 4, 96, 2, "RollOutRight"], [101, 16, 96, 14], [101, 18, 96, 16], [102, 6, 97, 4, "style"], [102, 11, 97, 9], [102, 13, 97, 11], [102, 17, 97, 11, "convertAnimationObjectToKeyframes"], [102, 67, 97, 44], [102, 69, 97, 45, "RollOutData"], [102, 80, 97, 56], [102, 81, 97, 57, "RollOutRight"], [102, 93, 97, 69], [102, 94, 97, 70], [103, 6, 98, 4, "duration"], [103, 14, 98, 12], [103, 16, 98, 14, "RollOutData"], [103, 27, 98, 25], [103, 28, 98, 26, "RollOutRight"], [103, 40, 98, 38], [103, 41, 98, 39, "duration"], [104, 4, 99, 2], [105, 2, 100, 0], [105, 3, 100, 1], [106, 0, 100, 2], [106, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}