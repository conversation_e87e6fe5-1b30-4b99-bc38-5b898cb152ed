{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 0, "index": 201}, "end": {"line": 8, "column": 77, "index": 278}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var nativeComponentName = 'RNSScreenContainer';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSScreenContainer\",\n    validAttributes: {}\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 17, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "__INTERNAL_VIEW_CONFIG"], [8, 50, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_codegenNativeComponent"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 8, 0], [10, 6, 8, 0, "NativeComponentRegistry"], [10, 29, 8, 77], [10, 32, 8, 0, "require"], [10, 39, 8, 77], [10, 40, 8, 77, "_dependencyMap"], [10, 54, 8, 77], [10, 123, 8, 76], [10, 124, 8, 77], [11, 2, 8, 0], [11, 6, 8, 0, "nativeComponentName"], [11, 25, 8, 77], [11, 28, 8, 0], [11, 48, 8, 77], [12, 2, 8, 0], [12, 6, 8, 0, "__INTERNAL_VIEW_CONFIG"], [12, 28, 8, 77], [12, 31, 8, 77, "exports"], [12, 38, 8, 77], [12, 39, 8, 77, "__INTERNAL_VIEW_CONFIG"], [12, 61, 8, 77], [12, 64, 8, 0], [13, 4, 8, 0, "uiViewClassName"], [13, 19, 8, 77], [13, 21, 8, 0], [13, 41, 8, 77], [14, 4, 8, 0, "validAttributes"], [14, 19, 8, 77], [14, 21, 8, 0], [14, 22, 8, 76], [15, 2, 8, 76], [15, 3, 8, 77], [16, 2, 8, 77], [16, 6, 8, 77, "_default"], [16, 14, 8, 77], [16, 17, 8, 77, "exports"], [16, 24, 8, 77], [16, 25, 8, 77, "default"], [16, 32, 8, 77], [16, 35, 8, 0, "NativeComponentRegistry"], [16, 58, 8, 77], [16, 59, 8, 0, "get"], [16, 62, 8, 77], [16, 63, 8, 0, "nativeComponentName"], [16, 82, 8, 77], [16, 84, 8, 0], [16, 90, 8, 0, "__INTERNAL_VIEW_CONFIG"], [16, 112, 8, 76], [16, 113, 8, 77], [17, 0, 8, 77], [17, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}