{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.baseGestureHandlerWithDetectorProps = exports.baseGestureHandlerProps = exports.MouseButton = void 0;\n  // Previous types exported gesture handlers as classes which creates an interface and variable, both named the same as class.\n  // Without those types, we'd introduce breaking change, forcing users to prefix every handler type specification with typeof\n  // e.g. React.createRef<TapGestureHandler> -> React.createRef<typeof TapGestureHandler>.\n  // See https://www.typescriptlang.org/docs/handbook/classes.html#constructor-functions for reference.\n  const commonProps = ['id', 'enabled', 'shouldCancelWhenOutside', 'hitSlop', 'cancelsTouchesInView', 'userSelect', 'activeCursor', 'mouseButton', 'enableContextMenu', 'touchAction'];\n  const componentInteractionProps = ['waitFor', 'simultaneousHandlers', 'blocksHandlers'];\n  const baseGestureHandlerProps = exports.baseGestureHandlerProps = [...commonProps, ...componentInteractionProps, 'onBegan', 'onFailed', 'onCancelled', 'onActivated', 'onEnded', 'onGestureEvent', 'onHandlerStateChange'];\n  const baseGestureHandlerWithDetectorProps = exports.baseGestureHandlerWithDetectorProps = [...commonProps, 'needsPointerData', 'manualActivation'];\n  let MouseButton = exports.MouseButton = void 0;\n  (function (MouseButton) {\n    MouseButton[MouseButton[\"LEFT\"] = 1] = \"LEFT\";\n    MouseButton[MouseButton[\"RIGHT\"] = 2] = \"RIGHT\";\n    MouseButton[MouseButton[\"MIDDLE\"] = 4] = \"MIDDLE\";\n    MouseButton[MouseButton[\"BUTTON_4\"] = 8] = \"BUTTON_4\";\n    MouseButton[MouseButton[\"BUTTON_5\"] = 16] = \"BUTTON_5\";\n    MouseButton[MouseButton[\"ALL\"] = 31] = \"ALL\";\n  })(MouseButton || (exports.MouseButton = MouseButton = {}));\n});", "lineCount": 23, "map": [[6, 2, 1, 0], [7, 2, 2, 0], [8, 2, 3, 0], [9, 2, 4, 0], [10, 2, 5, 0], [10, 8, 5, 6, "commonProps"], [10, 19, 5, 17], [10, 22, 5, 20], [10, 23, 5, 21], [10, 27, 5, 25], [10, 29, 5, 27], [10, 38, 5, 36], [10, 40, 5, 38], [10, 65, 5, 63], [10, 67, 5, 65], [10, 76, 5, 74], [10, 78, 5, 76], [10, 100, 5, 98], [10, 102, 5, 100], [10, 114, 5, 112], [10, 116, 5, 114], [10, 130, 5, 128], [10, 132, 5, 130], [10, 145, 5, 143], [10, 147, 5, 145], [10, 166, 5, 164], [10, 168, 5, 166], [10, 181, 5, 179], [10, 182, 5, 180], [11, 2, 6, 0], [11, 8, 6, 6, "componentInteractionProps"], [11, 33, 6, 31], [11, 36, 6, 34], [11, 37, 6, 35], [11, 46, 6, 44], [11, 48, 6, 46], [11, 70, 6, 68], [11, 72, 6, 70], [11, 88, 6, 86], [11, 89, 6, 87], [12, 2, 7, 7], [12, 8, 7, 13, "baseGestureHandlerProps"], [12, 31, 7, 36], [12, 34, 7, 36, "exports"], [12, 41, 7, 36], [12, 42, 7, 36, "baseGestureHandlerProps"], [12, 65, 7, 36], [12, 68, 7, 39], [12, 69, 7, 40], [12, 72, 7, 43, "commonProps"], [12, 83, 7, 54], [12, 85, 7, 56], [12, 88, 7, 59, "componentInteractionProps"], [12, 113, 7, 84], [12, 115, 7, 86], [12, 124, 7, 95], [12, 126, 7, 97], [12, 136, 7, 107], [12, 138, 7, 109], [12, 151, 7, 122], [12, 153, 7, 124], [12, 166, 7, 137], [12, 168, 7, 139], [12, 177, 7, 148], [12, 179, 7, 150], [12, 195, 7, 166], [12, 197, 7, 168], [12, 219, 7, 190], [12, 220, 7, 191], [13, 2, 8, 7], [13, 8, 8, 13, "baseGestureHandlerWithDetectorProps"], [13, 43, 8, 48], [13, 46, 8, 48, "exports"], [13, 53, 8, 48], [13, 54, 8, 48, "baseGestureHandlerWithDetectorProps"], [13, 89, 8, 48], [13, 92, 8, 51], [13, 93, 8, 52], [13, 96, 8, 55, "commonProps"], [13, 107, 8, 66], [13, 109, 8, 68], [13, 127, 8, 86], [13, 129, 8, 88], [13, 147, 8, 106], [13, 148, 8, 107], [14, 2, 9, 7], [14, 6, 9, 11, "MouseB<PERSON>on"], [14, 17, 9, 22], [14, 20, 9, 22, "exports"], [14, 27, 9, 22], [14, 28, 9, 22, "MouseB<PERSON>on"], [14, 39, 9, 22], [15, 2, 11, 0], [15, 3, 11, 1], [15, 13, 11, 11, "MouseB<PERSON>on"], [15, 24, 11, 22], [15, 26, 11, 24], [16, 4, 12, 2, "MouseB<PERSON>on"], [16, 15, 12, 13], [16, 16, 12, 14, "MouseB<PERSON>on"], [16, 27, 12, 25], [16, 28, 12, 26], [16, 34, 12, 32], [16, 35, 12, 33], [16, 38, 12, 36], [16, 39, 12, 37], [16, 40, 12, 38], [16, 43, 12, 41], [16, 49, 12, 47], [17, 4, 13, 2, "MouseB<PERSON>on"], [17, 15, 13, 13], [17, 16, 13, 14, "MouseB<PERSON>on"], [17, 27, 13, 25], [17, 28, 13, 26], [17, 35, 13, 33], [17, 36, 13, 34], [17, 39, 13, 37], [17, 40, 13, 38], [17, 41, 13, 39], [17, 44, 13, 42], [17, 51, 13, 49], [18, 4, 14, 2, "MouseB<PERSON>on"], [18, 15, 14, 13], [18, 16, 14, 14, "MouseB<PERSON>on"], [18, 27, 14, 25], [18, 28, 14, 26], [18, 36, 14, 34], [18, 37, 14, 35], [18, 40, 14, 38], [18, 41, 14, 39], [18, 42, 14, 40], [18, 45, 14, 43], [18, 53, 14, 51], [19, 4, 15, 2, "MouseB<PERSON>on"], [19, 15, 15, 13], [19, 16, 15, 14, "MouseB<PERSON>on"], [19, 27, 15, 25], [19, 28, 15, 26], [19, 38, 15, 36], [19, 39, 15, 37], [19, 42, 15, 40], [19, 43, 15, 41], [19, 44, 15, 42], [19, 47, 15, 45], [19, 57, 15, 55], [20, 4, 16, 2, "MouseB<PERSON>on"], [20, 15, 16, 13], [20, 16, 16, 14, "MouseB<PERSON>on"], [20, 27, 16, 25], [20, 28, 16, 26], [20, 38, 16, 36], [20, 39, 16, 37], [20, 42, 16, 40], [20, 44, 16, 42], [20, 45, 16, 43], [20, 48, 16, 46], [20, 58, 16, 56], [21, 4, 17, 2, "MouseB<PERSON>on"], [21, 15, 17, 13], [21, 16, 17, 14, "MouseB<PERSON>on"], [21, 27, 17, 25], [21, 28, 17, 26], [21, 33, 17, 31], [21, 34, 17, 32], [21, 37, 17, 35], [21, 39, 17, 37], [21, 40, 17, 38], [21, 43, 17, 41], [21, 48, 17, 46], [22, 2, 18, 0], [22, 3, 18, 1], [22, 5, 18, 3, "MouseB<PERSON>on"], [22, 16, 18, 14], [22, 21, 18, 14, "exports"], [22, 28, 18, 14], [22, 29, 18, 14, "MouseB<PERSON>on"], [22, 40, 18, 14], [22, 43, 18, 19, "MouseB<PERSON>on"], [22, 54, 18, 30], [22, 57, 18, 33], [22, 58, 18, 34], [22, 59, 18, 35], [22, 60, 18, 36], [22, 61, 18, 37], [23, 0, 18, 38], [23, 3]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA;CCU;CDO"}}, "type": "js/module"}]}