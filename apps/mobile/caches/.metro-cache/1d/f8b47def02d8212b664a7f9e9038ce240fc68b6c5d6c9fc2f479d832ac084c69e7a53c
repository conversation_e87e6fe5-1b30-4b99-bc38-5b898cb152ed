{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-native/virtualized-lists", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 63}}], "key": "NiuZqJDnRmxYKpdtVk+l6fDKu0g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _virtualizedLists = _interopRequireDefault(require(_dependencyMap[1], \"@react-native/virtualized-lists\"));\n  var VirtualizedList = _virtualizedLists.default.VirtualizedList;\n  var _default = exports.default = VirtualizedList;\n});", "lineCount": 12, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 13, 0], [9, 6, 13, 0, "_virtualizedLists"], [9, 23, 13, 0], [9, 26, 13, 0, "_interopRequireDefault"], [9, 48, 13, 0], [9, 49, 13, 0, "require"], [9, 56, 13, 0], [9, 57, 13, 0, "_dependencyMap"], [9, 71, 13, 0], [10, 2, 16, 0], [10, 6, 16, 6, "VirtualizedList"], [10, 21, 16, 42], [10, 24, 16, 45, "VirtualizedLists"], [10, 49, 16, 61], [10, 50, 16, 62, "VirtualizedList"], [10, 65, 16, 77], [11, 2, 16, 78], [11, 6, 16, 78, "_default"], [11, 14, 16, 78], [11, 17, 16, 78, "exports"], [11, 24, 16, 78], [11, 25, 16, 78, "default"], [11, 32, 16, 78], [11, 35, 23, 15, "VirtualizedList"], [11, 50, 23, 30], [12, 0, 23, 30], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}