{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@egjs/hammerjs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "I5Lt2ouU6D9a2C2V4SJv4GWe8Fg=", "exportNames": ["*"]}}, {"name": "./constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 37}, "end": {"line": 2, "column": 108, "index": 145}}], "key": "waDaw5D7vDr2hRFu0z1BqRCTzP4=", "exportNames": ["*"]}}, {"name": "./DraggingGestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 146}, "end": {"line": 3, "column": 62, "index": 208}}], "key": "veIIVtZ+Znwl7KqPaA+sdGzF8LE=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 209}, "end": {"line": 4, "column": 80, "index": 289}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}, {"name": "../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 290}, "end": {"line": 5, "column": 33, "index": 323}}], "key": "ISRoyBmrsYyTcSqLDCBIFNoRZWE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _hammerjs = _interopRequireDefault(require(_dependencyMap[1], \"@egjs/hammerjs\"));\n  var _constants = require(_dependencyMap[2], \"./constants\");\n  var _DraggingGestureHandler = _interopRequireDefault(require(_dependencyMap[3], \"./DraggingGestureHandler\"));\n  var _utils = require(_dependencyMap[4], \"./utils\");\n  var _State = require(_dependencyMap[5], \"../State\");\n  class PanGestureHandler extends _DraggingGestureHandler.default {\n    get name() {\n      return 'pan';\n    }\n    get NativeGestureClass() {\n      return _hammerjs.default.Pan;\n    }\n    getHammerConfig() {\n      return {\n        ...super.getHammerConfig(),\n        direction: this.getDirection()\n      };\n    }\n    getState(type) {\n      const nextState = super.getState(type); // Ensure that the first state sent is `BEGAN` and not `ACTIVE`\n\n      if (this.previousState === _State.State.UNDETERMINED && nextState === _State.State.ACTIVE) {\n        return _State.State.BEGAN;\n      }\n      return nextState;\n    }\n    getDirection() {\n      const config = this.getConfig();\n      const {\n        activeOffsetXStart,\n        activeOffsetXEnd,\n        activeOffsetYStart,\n        activeOffsetYEnd,\n        minDist\n      } = config;\n      let directions = [];\n      let horizontalDirections = [];\n      if (!(0, _utils.isnan)(minDist)) {\n        return _hammerjs.default.DIRECTION_ALL;\n      }\n      if (!(0, _utils.isnan)(activeOffsetXStart)) {\n        horizontalDirections.push(_hammerjs.default.DIRECTION_LEFT);\n      }\n      if (!(0, _utils.isnan)(activeOffsetXEnd)) {\n        horizontalDirections.push(_hammerjs.default.DIRECTION_RIGHT);\n      }\n      if (horizontalDirections.length === 2) {\n        horizontalDirections = [_hammerjs.default.DIRECTION_HORIZONTAL];\n      }\n      directions = directions.concat(horizontalDirections);\n      let verticalDirections = [];\n      if (!(0, _utils.isnan)(activeOffsetYStart)) {\n        verticalDirections.push(_hammerjs.default.DIRECTION_UP);\n      }\n      if (!(0, _utils.isnan)(activeOffsetYEnd)) {\n        verticalDirections.push(_hammerjs.default.DIRECTION_DOWN);\n      }\n      if (verticalDirections.length === 2) {\n        verticalDirections = [_hammerjs.default.DIRECTION_VERTICAL];\n      }\n      directions = directions.concat(verticalDirections);\n      if (!directions.length) {\n        return _hammerjs.default.DIRECTION_NONE;\n      }\n      if (directions[0] === _hammerjs.default.DIRECTION_HORIZONTAL && directions[1] === _hammerjs.default.DIRECTION_VERTICAL) {\n        return _hammerjs.default.DIRECTION_ALL;\n      }\n      if (horizontalDirections.length && verticalDirections.length) {\n        return _hammerjs.default.DIRECTION_ALL;\n      }\n      return directions[0];\n    }\n    getConfig() {\n      if (!this.hasCustomActivationCriteria) {\n        // Default config\n        // If no params have been defined then this config should emulate the native gesture as closely as possible.\n        return {\n          minDistSq: 10\n        };\n      }\n      return this.config;\n    }\n    shouldFailUnderCustomCriteria({\n      deltaX,\n      deltaY\n    }, criteria) {\n      return !(0, _utils.isnan)(criteria.failOffsetXStart) && deltaX < criteria.failOffsetXStart || !(0, _utils.isnan)(criteria.failOffsetXEnd) && deltaX > criteria.failOffsetXEnd || !(0, _utils.isnan)(criteria.failOffsetYStart) && deltaY < criteria.failOffsetYStart || !(0, _utils.isnan)(criteria.failOffsetYEnd) && deltaY > criteria.failOffsetYEnd;\n    }\n    shouldActivateUnderCustomCriteria({\n      deltaX,\n      deltaY,\n      velocity\n    }, criteria) {\n      return !(0, _utils.isnan)(criteria.activeOffsetXStart) && deltaX < criteria.activeOffsetXStart || !(0, _utils.isnan)(criteria.activeOffsetXEnd) && deltaX > criteria.activeOffsetXEnd || !(0, _utils.isnan)(criteria.activeOffsetYStart) && deltaY < criteria.activeOffsetYStart || !(0, _utils.isnan)(criteria.activeOffsetYEnd) && deltaY > criteria.activeOffsetYEnd || (0, _utils.TEST_MIN_IF_NOT_NAN)((0, _utils.VEC_LEN_SQ)({\n        x: deltaX,\n        y: deltaY\n      }), criteria.minDistSq) || (0, _utils.TEST_MIN_IF_NOT_NAN)(velocity.x, criteria.minVelocityX) || (0, _utils.TEST_MIN_IF_NOT_NAN)(velocity.y, criteria.minVelocityY) || (0, _utils.TEST_MIN_IF_NOT_NAN)((0, _utils.VEC_LEN_SQ)(velocity), criteria.minVelocitySq);\n    }\n    shouldMultiFingerPanFail({\n      pointerLength,\n      scale,\n      deltaRotation\n    }) {\n      if (pointerLength <= 1) {\n        return false;\n      } // Test if the pan had too much pinching or rotating.\n\n      const deltaScale = Math.abs(scale - 1);\n      const absDeltaRotation = Math.abs(deltaRotation);\n      if (deltaScale > _constants.MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD) {\n        // > If the threshold doesn't seem right.\n        // You can log the value which it failed at here:\n        return true;\n      }\n      if (absDeltaRotation > _constants.MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD) {\n        // > If the threshold doesn't seem right.\n        // You can log the value which it failed at here:\n        return true;\n      }\n      return false;\n    }\n    updateHasCustomActivationCriteria(criteria) {\n      return (0, _utils.isValidNumber)(criteria.minDistSq) || (0, _utils.isValidNumber)(criteria.minVelocityX) || (0, _utils.isValidNumber)(criteria.minVelocityY) || (0, _utils.isValidNumber)(criteria.minVelocitySq) || (0, _utils.isValidNumber)(criteria.activeOffsetXStart) || (0, _utils.isValidNumber)(criteria.activeOffsetXEnd) || (0, _utils.isValidNumber)(criteria.activeOffsetYStart) || (0, _utils.isValidNumber)(criteria.activeOffsetYEnd);\n    }\n    isGestureEnabledForEvent(props, _recognizer, inputData) {\n      if (this.shouldFailUnderCustomCriteria(inputData, props)) {\n        return {\n          failed: true\n        };\n      }\n      const velocity = {\n        x: inputData.velocityX,\n        y: inputData.velocityY\n      };\n      if (this.hasCustomActivationCriteria && this.shouldActivateUnderCustomCriteria({\n        deltaX: inputData.deltaX,\n        deltaY: inputData.deltaY,\n        velocity\n      }, props)) {\n        if (this.shouldMultiFingerPanFail({\n          pointerLength: inputData.maxPointers,\n          scale: inputData.scale,\n          deltaRotation: inputData.deltaRotation\n        })) {\n          return {\n            failed: true\n          };\n        }\n        return {\n          success: true\n        };\n      }\n      return {\n        success: false\n      };\n    }\n  }\n  var _default = exports.default = PanGestureHandler;\n});", "lineCount": 165, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_hammerjs"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_constants"], [8, 16, 2, 0], [8, 19, 2, 0, "require"], [8, 26, 2, 0], [8, 27, 2, 0, "_dependencyMap"], [8, 41, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_DraggingGestureHandler"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_utils"], [10, 12, 4, 0], [10, 15, 4, 0, "require"], [10, 22, 4, 0], [10, 23, 4, 0, "_dependencyMap"], [10, 37, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_State"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 7, 0], [12, 8, 7, 6, "PanGestureHandler"], [12, 25, 7, 23], [12, 34, 7, 32, "DraggingGestureHandler"], [12, 65, 7, 54], [12, 66, 7, 55], [13, 4, 8, 2], [13, 8, 8, 6, "name"], [13, 12, 8, 10, "name"], [13, 13, 8, 10], [13, 15, 8, 13], [14, 6, 9, 4], [14, 13, 9, 11], [14, 18, 9, 16], [15, 4, 10, 2], [16, 4, 12, 2], [16, 8, 12, 6, "NativeGestureClass"], [16, 26, 12, 24, "NativeGestureClass"], [16, 27, 12, 24], [16, 29, 12, 27], [17, 6, 13, 4], [17, 13, 13, 11, "Hammer"], [17, 30, 13, 17], [17, 31, 13, 18, "Pan"], [17, 34, 13, 21], [18, 4, 14, 2], [19, 4, 16, 2, "getHammerConfig"], [19, 19, 16, 17, "getHammerConfig"], [19, 20, 16, 17], [19, 22, 16, 20], [20, 6, 17, 4], [20, 13, 17, 11], [21, 8, 17, 13], [21, 11, 17, 16], [21, 16, 17, 21], [21, 17, 17, 22, "getHammerConfig"], [21, 32, 17, 37], [21, 33, 17, 38], [21, 34, 17, 39], [22, 8, 18, 6, "direction"], [22, 17, 18, 15], [22, 19, 18, 17], [22, 23, 18, 21], [22, 24, 18, 22, "getDirection"], [22, 36, 18, 34], [22, 37, 18, 35], [23, 6, 19, 4], [23, 7, 19, 5], [24, 4, 20, 2], [25, 4, 22, 2, "getState"], [25, 12, 22, 10, "getState"], [25, 13, 22, 11, "type"], [25, 17, 22, 15], [25, 19, 22, 17], [26, 6, 23, 4], [26, 12, 23, 10, "nextState"], [26, 21, 23, 19], [26, 24, 23, 22], [26, 29, 23, 27], [26, 30, 23, 28, "getState"], [26, 38, 23, 36], [26, 39, 23, 37, "type"], [26, 43, 23, 41], [26, 44, 23, 42], [26, 45, 23, 43], [26, 46, 23, 44], [28, 6, 25, 4], [28, 10, 25, 8], [28, 14, 25, 12], [28, 15, 25, 13, "previousState"], [28, 28, 25, 26], [28, 33, 25, 31, "State"], [28, 45, 25, 36], [28, 46, 25, 37, "UNDETERMINED"], [28, 58, 25, 49], [28, 62, 25, 53, "nextState"], [28, 71, 25, 62], [28, 76, 25, 67, "State"], [28, 88, 25, 72], [28, 89, 25, 73, "ACTIVE"], [28, 95, 25, 79], [28, 97, 25, 81], [29, 8, 26, 6], [29, 15, 26, 13, "State"], [29, 27, 26, 18], [29, 28, 26, 19, "BEGAN"], [29, 33, 26, 24], [30, 6, 27, 4], [31, 6, 29, 4], [31, 13, 29, 11, "nextState"], [31, 22, 29, 20], [32, 4, 30, 2], [33, 4, 32, 2, "getDirection"], [33, 16, 32, 14, "getDirection"], [33, 17, 32, 14], [33, 19, 32, 17], [34, 6, 33, 4], [34, 12, 33, 10, "config"], [34, 18, 33, 16], [34, 21, 33, 19], [34, 25, 33, 23], [34, 26, 33, 24, "getConfig"], [34, 35, 33, 33], [34, 36, 33, 34], [34, 37, 33, 35], [35, 6, 34, 4], [35, 12, 34, 10], [36, 8, 35, 6, "activeOffsetXStart"], [36, 26, 35, 24], [37, 8, 36, 6, "activeOffsetXEnd"], [37, 24, 36, 22], [38, 8, 37, 6, "activeOffsetYStart"], [38, 26, 37, 24], [39, 8, 38, 6, "activeOffsetYEnd"], [39, 24, 38, 22], [40, 8, 39, 6, "minDist"], [41, 6, 40, 4], [41, 7, 40, 5], [41, 10, 40, 8, "config"], [41, 16, 40, 14], [42, 6, 41, 4], [42, 10, 41, 8, "directions"], [42, 20, 41, 18], [42, 23, 41, 21], [42, 25, 41, 23], [43, 6, 42, 4], [43, 10, 42, 8, "horizontalDirections"], [43, 30, 42, 28], [43, 33, 42, 31], [43, 35, 42, 33], [44, 6, 44, 4], [44, 10, 44, 8], [44, 11, 44, 9], [44, 15, 44, 9, "isnan"], [44, 27, 44, 14], [44, 29, 44, 15, "minDist"], [44, 36, 44, 22], [44, 37, 44, 23], [44, 39, 44, 25], [45, 8, 45, 6], [45, 15, 45, 13, "Hammer"], [45, 32, 45, 19], [45, 33, 45, 20, "DIRECTION_ALL"], [45, 46, 45, 33], [46, 6, 46, 4], [47, 6, 48, 4], [47, 10, 48, 8], [47, 11, 48, 9], [47, 15, 48, 9, "isnan"], [47, 27, 48, 14], [47, 29, 48, 15, "activeOffsetXStart"], [47, 47, 48, 33], [47, 48, 48, 34], [47, 50, 48, 36], [48, 8, 49, 6, "horizontalDirections"], [48, 28, 49, 26], [48, 29, 49, 27, "push"], [48, 33, 49, 31], [48, 34, 49, 32, "Hammer"], [48, 51, 49, 38], [48, 52, 49, 39, "DIRECTION_LEFT"], [48, 66, 49, 53], [48, 67, 49, 54], [49, 6, 50, 4], [50, 6, 52, 4], [50, 10, 52, 8], [50, 11, 52, 9], [50, 15, 52, 9, "isnan"], [50, 27, 52, 14], [50, 29, 52, 15, "activeOffsetXEnd"], [50, 45, 52, 31], [50, 46, 52, 32], [50, 48, 52, 34], [51, 8, 53, 6, "horizontalDirections"], [51, 28, 53, 26], [51, 29, 53, 27, "push"], [51, 33, 53, 31], [51, 34, 53, 32, "Hammer"], [51, 51, 53, 38], [51, 52, 53, 39, "DIRECTION_RIGHT"], [51, 67, 53, 54], [51, 68, 53, 55], [52, 6, 54, 4], [53, 6, 56, 4], [53, 10, 56, 8, "horizontalDirections"], [53, 30, 56, 28], [53, 31, 56, 29, "length"], [53, 37, 56, 35], [53, 42, 56, 40], [53, 43, 56, 41], [53, 45, 56, 43], [54, 8, 57, 6, "horizontalDirections"], [54, 28, 57, 26], [54, 31, 57, 29], [54, 32, 57, 30, "Hammer"], [54, 49, 57, 36], [54, 50, 57, 37, "DIRECTION_HORIZONTAL"], [54, 70, 57, 57], [54, 71, 57, 58], [55, 6, 58, 4], [56, 6, 60, 4, "directions"], [56, 16, 60, 14], [56, 19, 60, 17, "directions"], [56, 29, 60, 27], [56, 30, 60, 28, "concat"], [56, 36, 60, 34], [56, 37, 60, 35, "horizontalDirections"], [56, 57, 60, 55], [56, 58, 60, 56], [57, 6, 61, 4], [57, 10, 61, 8, "verticalDirections"], [57, 28, 61, 26], [57, 31, 61, 29], [57, 33, 61, 31], [58, 6, 63, 4], [58, 10, 63, 8], [58, 11, 63, 9], [58, 15, 63, 9, "isnan"], [58, 27, 63, 14], [58, 29, 63, 15, "activeOffsetYStart"], [58, 47, 63, 33], [58, 48, 63, 34], [58, 50, 63, 36], [59, 8, 64, 6, "verticalDirections"], [59, 26, 64, 24], [59, 27, 64, 25, "push"], [59, 31, 64, 29], [59, 32, 64, 30, "Hammer"], [59, 49, 64, 36], [59, 50, 64, 37, "DIRECTION_UP"], [59, 62, 64, 49], [59, 63, 64, 50], [60, 6, 65, 4], [61, 6, 67, 4], [61, 10, 67, 8], [61, 11, 67, 9], [61, 15, 67, 9, "isnan"], [61, 27, 67, 14], [61, 29, 67, 15, "activeOffsetYEnd"], [61, 45, 67, 31], [61, 46, 67, 32], [61, 48, 67, 34], [62, 8, 68, 6, "verticalDirections"], [62, 26, 68, 24], [62, 27, 68, 25, "push"], [62, 31, 68, 29], [62, 32, 68, 30, "Hammer"], [62, 49, 68, 36], [62, 50, 68, 37, "DIRECTION_DOWN"], [62, 64, 68, 51], [62, 65, 68, 52], [63, 6, 69, 4], [64, 6, 71, 4], [64, 10, 71, 8, "verticalDirections"], [64, 28, 71, 26], [64, 29, 71, 27, "length"], [64, 35, 71, 33], [64, 40, 71, 38], [64, 41, 71, 39], [64, 43, 71, 41], [65, 8, 72, 6, "verticalDirections"], [65, 26, 72, 24], [65, 29, 72, 27], [65, 30, 72, 28, "Hammer"], [65, 47, 72, 34], [65, 48, 72, 35, "DIRECTION_VERTICAL"], [65, 66, 72, 53], [65, 67, 72, 54], [66, 6, 73, 4], [67, 6, 75, 4, "directions"], [67, 16, 75, 14], [67, 19, 75, 17, "directions"], [67, 29, 75, 27], [67, 30, 75, 28, "concat"], [67, 36, 75, 34], [67, 37, 75, 35, "verticalDirections"], [67, 55, 75, 53], [67, 56, 75, 54], [68, 6, 77, 4], [68, 10, 77, 8], [68, 11, 77, 9, "directions"], [68, 21, 77, 19], [68, 22, 77, 20, "length"], [68, 28, 77, 26], [68, 30, 77, 28], [69, 8, 78, 6], [69, 15, 78, 13, "Hammer"], [69, 32, 78, 19], [69, 33, 78, 20, "DIRECTION_NONE"], [69, 47, 78, 34], [70, 6, 79, 4], [71, 6, 81, 4], [71, 10, 81, 8, "directions"], [71, 20, 81, 18], [71, 21, 81, 19], [71, 22, 81, 20], [71, 23, 81, 21], [71, 28, 81, 26, "Hammer"], [71, 45, 81, 32], [71, 46, 81, 33, "DIRECTION_HORIZONTAL"], [71, 66, 81, 53], [71, 70, 81, 57, "directions"], [71, 80, 81, 67], [71, 81, 81, 68], [71, 82, 81, 69], [71, 83, 81, 70], [71, 88, 81, 75, "Hammer"], [71, 105, 81, 81], [71, 106, 81, 82, "DIRECTION_VERTICAL"], [71, 124, 81, 100], [71, 126, 81, 102], [72, 8, 82, 6], [72, 15, 82, 13, "Hammer"], [72, 32, 82, 19], [72, 33, 82, 20, "DIRECTION_ALL"], [72, 46, 82, 33], [73, 6, 83, 4], [74, 6, 85, 4], [74, 10, 85, 8, "horizontalDirections"], [74, 30, 85, 28], [74, 31, 85, 29, "length"], [74, 37, 85, 35], [74, 41, 85, 39, "verticalDirections"], [74, 59, 85, 57], [74, 60, 85, 58, "length"], [74, 66, 85, 64], [74, 68, 85, 66], [75, 8, 86, 6], [75, 15, 86, 13, "Hammer"], [75, 32, 86, 19], [75, 33, 86, 20, "DIRECTION_ALL"], [75, 46, 86, 33], [76, 6, 87, 4], [77, 6, 89, 4], [77, 13, 89, 11, "directions"], [77, 23, 89, 21], [77, 24, 89, 22], [77, 25, 89, 23], [77, 26, 89, 24], [78, 4, 90, 2], [79, 4, 92, 2, "getConfig"], [79, 13, 92, 11, "getConfig"], [79, 14, 92, 11], [79, 16, 92, 14], [80, 6, 93, 4], [80, 10, 93, 8], [80, 11, 93, 9], [80, 15, 93, 13], [80, 16, 93, 14, "hasCustomActivationCriteria"], [80, 43, 93, 41], [80, 45, 93, 43], [81, 8, 94, 6], [82, 8, 95, 6], [83, 8, 96, 6], [83, 15, 96, 13], [84, 10, 97, 8, "minDistSq"], [84, 19, 97, 17], [84, 21, 97, 19], [85, 8, 98, 6], [85, 9, 98, 7], [86, 6, 99, 4], [87, 6, 101, 4], [87, 13, 101, 11], [87, 17, 101, 15], [87, 18, 101, 16, "config"], [87, 24, 101, 22], [88, 4, 102, 2], [89, 4, 104, 2, "shouldFailUnderCustomCriteria"], [89, 33, 104, 31, "shouldFailUnderCustomCriteria"], [89, 34, 104, 32], [90, 6, 105, 4, "deltaX"], [90, 12, 105, 10], [91, 6, 106, 4, "deltaY"], [92, 4, 107, 2], [92, 5, 107, 3], [92, 7, 107, 5, "criteria"], [92, 15, 107, 13], [92, 17, 107, 15], [93, 6, 108, 4], [93, 13, 108, 11], [93, 14, 108, 12], [93, 18, 108, 12, "isnan"], [93, 30, 108, 17], [93, 32, 108, 18, "criteria"], [93, 40, 108, 26], [93, 41, 108, 27, "failOffsetXStart"], [93, 57, 108, 43], [93, 58, 108, 44], [93, 62, 108, 48, "deltaX"], [93, 68, 108, 54], [93, 71, 108, 57, "criteria"], [93, 79, 108, 65], [93, 80, 108, 66, "failOffsetXStart"], [93, 96, 108, 82], [93, 100, 108, 86], [93, 101, 108, 87], [93, 105, 108, 87, "isnan"], [93, 117, 108, 92], [93, 119, 108, 93, "criteria"], [93, 127, 108, 101], [93, 128, 108, 102, "failOffsetXEnd"], [93, 142, 108, 116], [93, 143, 108, 117], [93, 147, 108, 121, "deltaX"], [93, 153, 108, 127], [93, 156, 108, 130, "criteria"], [93, 164, 108, 138], [93, 165, 108, 139, "failOffsetXEnd"], [93, 179, 108, 153], [93, 183, 108, 157], [93, 184, 108, 158], [93, 188, 108, 158, "isnan"], [93, 200, 108, 163], [93, 202, 108, 164, "criteria"], [93, 210, 108, 172], [93, 211, 108, 173, "failOffsetYStart"], [93, 227, 108, 189], [93, 228, 108, 190], [93, 232, 108, 194, "deltaY"], [93, 238, 108, 200], [93, 241, 108, 203, "criteria"], [93, 249, 108, 211], [93, 250, 108, 212, "failOffsetYStart"], [93, 266, 108, 228], [93, 270, 108, 232], [93, 271, 108, 233], [93, 275, 108, 233, "isnan"], [93, 287, 108, 238], [93, 289, 108, 239, "criteria"], [93, 297, 108, 247], [93, 298, 108, 248, "failOffsetYEnd"], [93, 312, 108, 262], [93, 313, 108, 263], [93, 317, 108, 267, "deltaY"], [93, 323, 108, 273], [93, 326, 108, 276, "criteria"], [93, 334, 108, 284], [93, 335, 108, 285, "failOffsetYEnd"], [93, 349, 108, 299], [94, 4, 109, 2], [95, 4, 111, 2, "shouldActivateUnderCustomCriteria"], [95, 37, 111, 35, "shouldActivateUnderCustomCriteria"], [95, 38, 111, 36], [96, 6, 112, 4, "deltaX"], [96, 12, 112, 10], [97, 6, 113, 4, "deltaY"], [97, 12, 113, 10], [98, 6, 114, 4, "velocity"], [99, 4, 115, 2], [99, 5, 115, 3], [99, 7, 115, 5, "criteria"], [99, 15, 115, 13], [99, 17, 115, 15], [100, 6, 116, 4], [100, 13, 116, 11], [100, 14, 116, 12], [100, 18, 116, 12, "isnan"], [100, 30, 116, 17], [100, 32, 116, 18, "criteria"], [100, 40, 116, 26], [100, 41, 116, 27, "activeOffsetXStart"], [100, 59, 116, 45], [100, 60, 116, 46], [100, 64, 116, 50, "deltaX"], [100, 70, 116, 56], [100, 73, 116, 59, "criteria"], [100, 81, 116, 67], [100, 82, 116, 68, "activeOffsetXStart"], [100, 100, 116, 86], [100, 104, 116, 90], [100, 105, 116, 91], [100, 109, 116, 91, "isnan"], [100, 121, 116, 96], [100, 123, 116, 97, "criteria"], [100, 131, 116, 105], [100, 132, 116, 106, "activeOffsetXEnd"], [100, 148, 116, 122], [100, 149, 116, 123], [100, 153, 116, 127, "deltaX"], [100, 159, 116, 133], [100, 162, 116, 136, "criteria"], [100, 170, 116, 144], [100, 171, 116, 145, "activeOffsetXEnd"], [100, 187, 116, 161], [100, 191, 116, 165], [100, 192, 116, 166], [100, 196, 116, 166, "isnan"], [100, 208, 116, 171], [100, 210, 116, 172, "criteria"], [100, 218, 116, 180], [100, 219, 116, 181, "activeOffsetYStart"], [100, 237, 116, 199], [100, 238, 116, 200], [100, 242, 116, 204, "deltaY"], [100, 248, 116, 210], [100, 251, 116, 213, "criteria"], [100, 259, 116, 221], [100, 260, 116, 222, "activeOffsetYStart"], [100, 278, 116, 240], [100, 282, 116, 244], [100, 283, 116, 245], [100, 287, 116, 245, "isnan"], [100, 299, 116, 250], [100, 301, 116, 251, "criteria"], [100, 309, 116, 259], [100, 310, 116, 260, "activeOffsetYEnd"], [100, 326, 116, 276], [100, 327, 116, 277], [100, 331, 116, 281, "deltaY"], [100, 337, 116, 287], [100, 340, 116, 290, "criteria"], [100, 348, 116, 298], [100, 349, 116, 299, "activeOffsetYEnd"], [100, 365, 116, 315], [100, 369, 116, 319], [100, 373, 116, 319, "TEST_MIN_IF_NOT_NAN"], [100, 399, 116, 338], [100, 401, 116, 339], [100, 405, 116, 339, "VEC_LEN_SQ"], [100, 422, 116, 349], [100, 424, 116, 350], [101, 8, 117, 6, "x"], [101, 9, 117, 7], [101, 11, 117, 9, "deltaX"], [101, 17, 117, 15], [102, 8, 118, 6, "y"], [102, 9, 118, 7], [102, 11, 118, 9, "deltaY"], [103, 6, 119, 4], [103, 7, 119, 5], [103, 8, 119, 6], [103, 10, 119, 8, "criteria"], [103, 18, 119, 16], [103, 19, 119, 17, "minDistSq"], [103, 28, 119, 26], [103, 29, 119, 27], [103, 33, 119, 31], [103, 37, 119, 31, "TEST_MIN_IF_NOT_NAN"], [103, 63, 119, 50], [103, 65, 119, 51, "velocity"], [103, 73, 119, 59], [103, 74, 119, 60, "x"], [103, 75, 119, 61], [103, 77, 119, 63, "criteria"], [103, 85, 119, 71], [103, 86, 119, 72, "minVelocityX"], [103, 98, 119, 84], [103, 99, 119, 85], [103, 103, 119, 89], [103, 107, 119, 89, "TEST_MIN_IF_NOT_NAN"], [103, 133, 119, 108], [103, 135, 119, 109, "velocity"], [103, 143, 119, 117], [103, 144, 119, 118, "y"], [103, 145, 119, 119], [103, 147, 119, 121, "criteria"], [103, 155, 119, 129], [103, 156, 119, 130, "minVelocityY"], [103, 168, 119, 142], [103, 169, 119, 143], [103, 173, 119, 147], [103, 177, 119, 147, "TEST_MIN_IF_NOT_NAN"], [103, 203, 119, 166], [103, 205, 119, 167], [103, 209, 119, 167, "VEC_LEN_SQ"], [103, 226, 119, 177], [103, 228, 119, 178, "velocity"], [103, 236, 119, 186], [103, 237, 119, 187], [103, 239, 119, 189, "criteria"], [103, 247, 119, 197], [103, 248, 119, 198, "minVelocitySq"], [103, 261, 119, 211], [103, 262, 119, 212], [104, 4, 120, 2], [105, 4, 122, 2, "shouldMultiFingerPanFail"], [105, 28, 122, 26, "shouldMultiFingerPanFail"], [105, 29, 122, 27], [106, 6, 123, 4, "pointer<PERSON><PERSON><PERSON>"], [106, 19, 123, 17], [107, 6, 124, 4, "scale"], [107, 11, 124, 9], [108, 6, 125, 4, "deltaRotation"], [109, 4, 126, 2], [109, 5, 126, 3], [109, 7, 126, 5], [110, 6, 127, 4], [110, 10, 127, 8, "pointer<PERSON><PERSON><PERSON>"], [110, 23, 127, 21], [110, 27, 127, 25], [110, 28, 127, 26], [110, 30, 127, 28], [111, 8, 128, 6], [111, 15, 128, 13], [111, 20, 128, 18], [112, 6, 129, 4], [112, 7, 129, 5], [112, 8, 129, 6], [114, 6, 132, 4], [114, 12, 132, 10, "deltaScale"], [114, 22, 132, 20], [114, 25, 132, 23, "Math"], [114, 29, 132, 27], [114, 30, 132, 28, "abs"], [114, 33, 132, 31], [114, 34, 132, 32, "scale"], [114, 39, 132, 37], [114, 42, 132, 40], [114, 43, 132, 41], [114, 44, 132, 42], [115, 6, 133, 4], [115, 12, 133, 10, "absDeltaRotation"], [115, 28, 133, 26], [115, 31, 133, 29, "Math"], [115, 35, 133, 33], [115, 36, 133, 34, "abs"], [115, 39, 133, 37], [115, 40, 133, 38, "deltaRotation"], [115, 53, 133, 51], [115, 54, 133, 52], [116, 6, 135, 4], [116, 10, 135, 8, "deltaScale"], [116, 20, 135, 18], [116, 23, 135, 21, "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD"], [116, 70, 135, 57], [116, 72, 135, 59], [117, 8, 136, 6], [118, 8, 137, 6], [119, 8, 138, 6], [119, 15, 138, 13], [119, 19, 138, 17], [120, 6, 139, 4], [121, 6, 141, 4], [121, 10, 141, 8, "absDeltaRotation"], [121, 26, 141, 24], [121, 29, 141, 27, "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD"], [121, 79, 141, 66], [121, 81, 141, 68], [122, 8, 142, 6], [123, 8, 143, 6], [124, 8, 144, 6], [124, 15, 144, 13], [124, 19, 144, 17], [125, 6, 145, 4], [126, 6, 147, 4], [126, 13, 147, 11], [126, 18, 147, 16], [127, 4, 148, 2], [128, 4, 150, 2, "updateHasCustomActivationCriteria"], [128, 37, 150, 35, "updateHasCustomActivationCriteria"], [128, 38, 150, 36, "criteria"], [128, 46, 150, 44], [128, 48, 150, 46], [129, 6, 151, 4], [129, 13, 151, 11], [129, 17, 151, 11, "isValidNumber"], [129, 37, 151, 24], [129, 39, 151, 25, "criteria"], [129, 47, 151, 33], [129, 48, 151, 34, "minDistSq"], [129, 57, 151, 43], [129, 58, 151, 44], [129, 62, 151, 48], [129, 66, 151, 48, "isValidNumber"], [129, 86, 151, 61], [129, 88, 151, 62, "criteria"], [129, 96, 151, 70], [129, 97, 151, 71, "minVelocityX"], [129, 109, 151, 83], [129, 110, 151, 84], [129, 114, 151, 88], [129, 118, 151, 88, "isValidNumber"], [129, 138, 151, 101], [129, 140, 151, 102, "criteria"], [129, 148, 151, 110], [129, 149, 151, 111, "minVelocityY"], [129, 161, 151, 123], [129, 162, 151, 124], [129, 166, 151, 128], [129, 170, 151, 128, "isValidNumber"], [129, 190, 151, 141], [129, 192, 151, 142, "criteria"], [129, 200, 151, 150], [129, 201, 151, 151, "minVelocitySq"], [129, 214, 151, 164], [129, 215, 151, 165], [129, 219, 151, 169], [129, 223, 151, 169, "isValidNumber"], [129, 243, 151, 182], [129, 245, 151, 183, "criteria"], [129, 253, 151, 191], [129, 254, 151, 192, "activeOffsetXStart"], [129, 272, 151, 210], [129, 273, 151, 211], [129, 277, 151, 215], [129, 281, 151, 215, "isValidNumber"], [129, 301, 151, 228], [129, 303, 151, 229, "criteria"], [129, 311, 151, 237], [129, 312, 151, 238, "activeOffsetXEnd"], [129, 328, 151, 254], [129, 329, 151, 255], [129, 333, 151, 259], [129, 337, 151, 259, "isValidNumber"], [129, 357, 151, 272], [129, 359, 151, 273, "criteria"], [129, 367, 151, 281], [129, 368, 151, 282, "activeOffsetYStart"], [129, 386, 151, 300], [129, 387, 151, 301], [129, 391, 151, 305], [129, 395, 151, 305, "isValidNumber"], [129, 415, 151, 318], [129, 417, 151, 319, "criteria"], [129, 425, 151, 327], [129, 426, 151, 328, "activeOffsetYEnd"], [129, 442, 151, 344], [129, 443, 151, 345], [130, 4, 152, 2], [131, 4, 154, 2, "isGestureEnabledForEvent"], [131, 28, 154, 26, "isGestureEnabledForEvent"], [131, 29, 154, 27, "props"], [131, 34, 154, 32], [131, 36, 154, 34, "_recognizer"], [131, 47, 154, 45], [131, 49, 154, 47, "inputData"], [131, 58, 154, 56], [131, 60, 154, 58], [132, 6, 155, 4], [132, 10, 155, 8], [132, 14, 155, 12], [132, 15, 155, 13, "shouldFailUnderCustomCriteria"], [132, 44, 155, 42], [132, 45, 155, 43, "inputData"], [132, 54, 155, 52], [132, 56, 155, 54, "props"], [132, 61, 155, 59], [132, 62, 155, 60], [132, 64, 155, 62], [133, 8, 156, 6], [133, 15, 156, 13], [134, 10, 157, 8, "failed"], [134, 16, 157, 14], [134, 18, 157, 16], [135, 8, 158, 6], [135, 9, 158, 7], [136, 6, 159, 4], [137, 6, 161, 4], [137, 12, 161, 10, "velocity"], [137, 20, 161, 18], [137, 23, 161, 21], [138, 8, 162, 6, "x"], [138, 9, 162, 7], [138, 11, 162, 9, "inputData"], [138, 20, 162, 18], [138, 21, 162, 19, "velocityX"], [138, 30, 162, 28], [139, 8, 163, 6, "y"], [139, 9, 163, 7], [139, 11, 163, 9, "inputData"], [139, 20, 163, 18], [139, 21, 163, 19, "velocityY"], [140, 6, 164, 4], [140, 7, 164, 5], [141, 6, 166, 4], [141, 10, 166, 8], [141, 14, 166, 12], [141, 15, 166, 13, "hasCustomActivationCriteria"], [141, 42, 166, 40], [141, 46, 166, 44], [141, 50, 166, 48], [141, 51, 166, 49, "shouldActivateUnderCustomCriteria"], [141, 84, 166, 82], [141, 85, 166, 83], [142, 8, 167, 6, "deltaX"], [142, 14, 167, 12], [142, 16, 167, 14, "inputData"], [142, 25, 167, 23], [142, 26, 167, 24, "deltaX"], [142, 32, 167, 30], [143, 8, 168, 6, "deltaY"], [143, 14, 168, 12], [143, 16, 168, 14, "inputData"], [143, 25, 168, 23], [143, 26, 168, 24, "deltaY"], [143, 32, 168, 30], [144, 8, 169, 6, "velocity"], [145, 6, 170, 4], [145, 7, 170, 5], [145, 9, 170, 7, "props"], [145, 14, 170, 12], [145, 15, 170, 13], [145, 17, 170, 15], [146, 8, 171, 6], [146, 12, 171, 10], [146, 16, 171, 14], [146, 17, 171, 15, "shouldMultiFingerPanFail"], [146, 41, 171, 39], [146, 42, 171, 40], [147, 10, 172, 8, "pointer<PERSON><PERSON><PERSON>"], [147, 23, 172, 21], [147, 25, 172, 23, "inputData"], [147, 34, 172, 32], [147, 35, 172, 33, "maxPointers"], [147, 46, 172, 44], [148, 10, 173, 8, "scale"], [148, 15, 173, 13], [148, 17, 173, 15, "inputData"], [148, 26, 173, 24], [148, 27, 173, 25, "scale"], [148, 32, 173, 30], [149, 10, 174, 8, "deltaRotation"], [149, 23, 174, 21], [149, 25, 174, 23, "inputData"], [149, 34, 174, 32], [149, 35, 174, 33, "deltaRotation"], [150, 8, 175, 6], [150, 9, 175, 7], [150, 10, 175, 8], [150, 12, 175, 10], [151, 10, 176, 8], [151, 17, 176, 15], [152, 12, 177, 10, "failed"], [152, 18, 177, 16], [152, 20, 177, 18], [153, 10, 178, 8], [153, 11, 178, 9], [154, 8, 179, 6], [155, 8, 181, 6], [155, 15, 181, 13], [156, 10, 182, 8, "success"], [156, 17, 182, 15], [156, 19, 182, 17], [157, 8, 183, 6], [157, 9, 183, 7], [158, 6, 184, 4], [159, 6, 186, 4], [159, 13, 186, 11], [160, 8, 187, 6, "success"], [160, 15, 187, 13], [160, 17, 187, 15], [161, 6, 188, 4], [161, 7, 188, 5], [162, 4, 189, 2], [163, 2, 191, 0], [164, 2, 191, 1], [164, 6, 191, 1, "_default"], [164, 14, 191, 1], [164, 17, 191, 1, "exports"], [164, 24, 191, 1], [164, 25, 191, 1, "default"], [164, 32, 191, 1], [164, 35, 193, 15, "PanGestureHandler"], [164, 52, 193, 32], [165, 0, 193, 32], [165, 3]], "functionMap": {"names": ["<global>", "PanGestureHandler", "get__name", "get__NativeGestureClass", "getHammerConfig", "getState", "getDirection", "getConfig", "shouldFailUnderCustomCriteria", "shouldActivateUnderCustomCriteria", "shouldMultiFingerPanFail", "updateHasCustomActivationCriteria", "isGestureEnabledForEvent"], "mappings": "AAA;ACM;ECC;GDE;EEE;GFE;EGE;GHI;EIE;GJQ;EKE;GL0D;EME;GNU;EOE;GPK;EQE;GRS;ESE;GT0B;EUE;GVE;EWE;GXmC;CDE"}}, "type": "js/module"}]}