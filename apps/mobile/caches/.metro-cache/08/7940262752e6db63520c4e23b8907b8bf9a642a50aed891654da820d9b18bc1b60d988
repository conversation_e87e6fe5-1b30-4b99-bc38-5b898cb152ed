{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 48, "index": 62}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 63}, "end": {"line": 3, "column": 40, "index": 103}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isAndroid = isAndroid;\n  exports.isChromeDebugger = isChromeDebugger;\n  exports.isFabric = isFabric;\n  exports.isJest = isJest;\n  exports.isReact19 = isReact19;\n  exports.isWeb = isWeb;\n  exports.isWindowAvailable = isWindowAvailable;\n  exports.shouldBeUseWeb = shouldBeUseWeb;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  // This type is necessary since some libraries tend to do a lib check\n  // and this file causes type errors on `global` access.\n\n  function isJest() {\n    return !!process.env.JEST_WORKER_ID;\n  }\n\n  // `isChromeDebugger` also returns true in Jest environment, so `isJest()` check should always be performed first\n  function isChromeDebugger() {\n    return (!global.nativeCallSyncHook || !!global.__REMOTEDEV__) && !global.RN$Bridgeless;\n  }\n  function isWeb() {\n    return _reactNative.Platform.OS === 'web';\n  }\n  function isAndroid() {\n    return _reactNative.Platform.OS === 'android';\n  }\n  function isWindows() {\n    return _reactNative.Platform.OS === 'windows';\n  }\n  function shouldBeUseWeb() {\n    return isJest() || isChromeDebugger() || isWeb() || isWindows();\n  }\n  function isFabric() {\n    return !!global._IS_FABRIC;\n  }\n  function isReact19() {\n    return _react.version.startsWith('19.');\n  }\n  function isWindowAvailable() {\n    // the window object is unavailable when building the server portion of a site that uses SSG\n    // this function shouldn't be used to conditionally render components\n    // https://www.joshwcomeau.com/react/the-perils-of-rehydration/\n    // @ts-ignore Fallback if `window` is undefined.\n    return typeof window !== 'undefined';\n  }\n});", "lineCount": 53, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "isAndroid"], [7, 19, 1, 13], [7, 22, 1, 13, "isAndroid"], [7, 31, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "isChromeDebugger"], [8, 26, 1, 13], [8, 29, 1, 13, "isChromeDebugger"], [8, 45, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "isF<PERSON><PERSON>"], [9, 18, 1, 13], [9, 21, 1, 13, "isF<PERSON><PERSON>"], [9, 29, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "isJest"], [10, 16, 1, 13], [10, 19, 1, 13, "isJest"], [10, 25, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "isReact19"], [11, 19, 1, 13], [11, 22, 1, 13, "isReact19"], [11, 31, 1, 13], [12, 2, 1, 13, "exports"], [12, 9, 1, 13], [12, 10, 1, 13, "isWeb"], [12, 15, 1, 13], [12, 18, 1, 13, "isWeb"], [12, 23, 1, 13], [13, 2, 1, 13, "exports"], [13, 9, 1, 13], [13, 10, 1, 13, "isWindowAvailable"], [13, 27, 1, 13], [13, 30, 1, 13, "isWindowAvailable"], [13, 47, 1, 13], [14, 2, 1, 13, "exports"], [14, 9, 1, 13], [14, 10, 1, 13, "shouldBeUseWeb"], [14, 24, 1, 13], [14, 27, 1, 13, "shouldBeUseWeb"], [14, 41, 1, 13], [15, 2, 2, 0], [15, 6, 2, 0, "_react"], [15, 12, 2, 0], [15, 15, 2, 0, "require"], [15, 22, 2, 0], [15, 23, 2, 0, "_dependencyMap"], [15, 37, 2, 0], [16, 2, 3, 0], [16, 6, 3, 0, "_reactNative"], [16, 18, 3, 0], [16, 21, 3, 0, "require"], [16, 28, 3, 0], [16, 29, 3, 0, "_dependencyMap"], [16, 43, 3, 0], [17, 2, 5, 0], [18, 2, 6, 0], [20, 2, 9, 7], [20, 11, 9, 16, "isJest"], [20, 17, 9, 22, "isJest"], [20, 18, 9, 22], [20, 20, 9, 34], [21, 4, 10, 2], [21, 11, 10, 9], [21, 12, 10, 10], [21, 13, 10, 11, "process"], [21, 20, 10, 18], [21, 21, 10, 19, "env"], [21, 24, 10, 22], [21, 25, 10, 23, "JEST_WORKER_ID"], [21, 39, 10, 37], [22, 2, 11, 0], [24, 2, 13, 0], [25, 2, 14, 7], [25, 11, 14, 16, "isChromeDebugger"], [25, 27, 14, 32, "isChromeDebugger"], [25, 28, 14, 32], [25, 30, 14, 44], [26, 4, 15, 2], [26, 11, 16, 4], [26, 12, 16, 5], [26, 13, 16, 7, "global"], [26, 19, 16, 13], [26, 20, 16, 30, "nativeCallSyncHook"], [26, 38, 16, 48], [26, 42, 17, 6], [26, 43, 17, 7], [26, 44, 17, 9, "global"], [26, 50, 17, 15], [26, 51, 17, 32, "__REMOTEDEV__"], [26, 64, 17, 45], [26, 69, 18, 4], [26, 70, 18, 6, "global"], [26, 76, 18, 12], [26, 77, 18, 29, "RN$Bridgeless"], [26, 90, 18, 42], [27, 2, 20, 0], [28, 2, 22, 7], [28, 11, 22, 16, "isWeb"], [28, 16, 22, 21, "isWeb"], [28, 17, 22, 21], [28, 19, 22, 33], [29, 4, 23, 2], [29, 11, 23, 9, "Platform"], [29, 32, 23, 17], [29, 33, 23, 18, "OS"], [29, 35, 23, 20], [29, 40, 23, 25], [29, 45, 23, 30], [30, 2, 24, 0], [31, 2, 26, 7], [31, 11, 26, 16, "isAndroid"], [31, 20, 26, 25, "isAndroid"], [31, 21, 26, 25], [31, 23, 26, 37], [32, 4, 27, 2], [32, 11, 27, 9, "Platform"], [32, 32, 27, 17], [32, 33, 27, 18, "OS"], [32, 35, 27, 20], [32, 40, 27, 25], [32, 49, 27, 34], [33, 2, 28, 0], [34, 2, 30, 0], [34, 11, 30, 9, "isWindows"], [34, 20, 30, 18, "isWindows"], [34, 21, 30, 18], [34, 23, 30, 30], [35, 4, 31, 2], [35, 11, 31, 9, "Platform"], [35, 32, 31, 17], [35, 33, 31, 18, "OS"], [35, 35, 31, 20], [35, 40, 31, 25], [35, 49, 31, 34], [36, 2, 32, 0], [37, 2, 34, 7], [37, 11, 34, 16, "shouldBeUseWeb"], [37, 25, 34, 30, "shouldBeUseWeb"], [37, 26, 34, 30], [37, 28, 34, 33], [38, 4, 35, 2], [38, 11, 35, 9, "isJest"], [38, 17, 35, 15], [38, 18, 35, 16], [38, 19, 35, 17], [38, 23, 35, 21, "isChromeDebugger"], [38, 39, 35, 37], [38, 40, 35, 38], [38, 41, 35, 39], [38, 45, 35, 43, "isWeb"], [38, 50, 35, 48], [38, 51, 35, 49], [38, 52, 35, 50], [38, 56, 35, 54, "isWindows"], [38, 65, 35, 63], [38, 66, 35, 64], [38, 67, 35, 65], [39, 2, 36, 0], [40, 2, 38, 7], [40, 11, 38, 16, "isF<PERSON><PERSON>"], [40, 19, 38, 24, "isF<PERSON><PERSON>"], [40, 20, 38, 24], [40, 22, 38, 27], [41, 4, 39, 2], [41, 11, 39, 9], [41, 12, 39, 10], [41, 13, 39, 12, "global"], [41, 19, 39, 18], [41, 20, 39, 35, "_IS_FABRIC"], [41, 30, 39, 45], [42, 2, 40, 0], [43, 2, 42, 7], [43, 11, 42, 16, "isReact19"], [43, 20, 42, 25, "isReact19"], [43, 21, 42, 25], [43, 23, 42, 28], [44, 4, 43, 2], [44, 11, 43, 9, "reactVersion"], [44, 25, 43, 21], [44, 26, 43, 22, "startsWith"], [44, 36, 43, 32], [44, 37, 43, 33], [44, 42, 43, 38], [44, 43, 43, 39], [45, 2, 44, 0], [46, 2, 46, 7], [46, 11, 46, 16, "isWindowAvailable"], [46, 28, 46, 33, "isWindowAvailable"], [46, 29, 46, 33], [46, 31, 46, 36], [47, 4, 47, 2], [48, 4, 48, 2], [49, 4, 49, 2], [50, 4, 50, 2], [51, 4, 51, 2], [51, 11, 51, 9], [51, 18, 51, 16, "window"], [51, 24, 51, 22], [51, 29, 51, 27], [51, 40, 51, 38], [52, 2, 52, 0], [53, 0, 52, 1], [53, 3]], "functionMap": {"names": ["<global>", "isJest", "isChromeDebugger", "isWeb", "isAndroid", "isWindows", "shouldBeUseWeb", "isF<PERSON><PERSON>", "isReact19", "isWindowAvailable"], "mappings": "AAA;OCQ;CDE;OEG;CFM;OGE;CHE;OIE;CJE;AKE;CLE;OME;CNE;OOE;CPE;OQE;CRE;OSE;CTM"}}, "type": "js/module"}]}