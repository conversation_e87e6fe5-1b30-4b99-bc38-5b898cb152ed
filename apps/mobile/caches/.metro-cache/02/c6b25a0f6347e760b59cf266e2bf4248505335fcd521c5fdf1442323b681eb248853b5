{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1], \"../createLucideIcon.js\"));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Chrome = exports.default = (0, _createLucideIcon.default)(\"Chrome\", [[\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\",\n    key: \"1mglay\"\n  }], [\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"4\",\n    key: \"4exip2\"\n  }], [\"line\", {\n    x1: \"21.17\",\n    x2: \"12\",\n    y1: \"8\",\n    y2: \"8\",\n    key: \"a0cw5f\"\n  }], [\"line\", {\n    x1: \"3.95\",\n    x2: \"8.54\",\n    y1: \"6.06\",\n    y2: \"14\",\n    key: \"1kftof\"\n  }], [\"line\", {\n    x1: \"10.88\",\n    x2: \"15.46\",\n    y1: \"21.94\",\n    y2: \"14\",\n    key: \"1ymyh8\"\n  }]]);\n});", "lineCount": 44, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Chrome"], [15, 12, 10, 12], [15, 15, 10, 12, "exports"], [15, 22, 10, 12], [15, 23, 10, 12, "default"], [15, 30, 10, 12], [15, 33, 10, 15], [15, 37, 10, 15, "createLucideIcon"], [15, 62, 10, 31], [15, 64, 10, 32], [15, 72, 10, 40], [15, 74, 10, 42], [15, 75, 11, 2], [15, 76, 11, 3], [15, 84, 11, 11], [15, 86, 11, 13], [16, 4, 11, 15, "cx"], [16, 6, 11, 17], [16, 8, 11, 19], [16, 12, 11, 23], [17, 4, 11, 25, "cy"], [17, 6, 11, 27], [17, 8, 11, 29], [17, 12, 11, 33], [18, 4, 11, 35, "r"], [18, 5, 11, 36], [18, 7, 11, 38], [18, 11, 11, 42], [19, 4, 11, 44, "key"], [19, 7, 11, 47], [19, 9, 11, 49], [20, 2, 11, 58], [20, 3, 11, 59], [20, 4, 11, 60], [20, 6, 12, 2], [20, 7, 12, 3], [20, 15, 12, 11], [20, 17, 12, 13], [21, 4, 12, 15, "cx"], [21, 6, 12, 17], [21, 8, 12, 19], [21, 12, 12, 23], [22, 4, 12, 25, "cy"], [22, 6, 12, 27], [22, 8, 12, 29], [22, 12, 12, 33], [23, 4, 12, 35, "r"], [23, 5, 12, 36], [23, 7, 12, 38], [23, 10, 12, 41], [24, 4, 12, 43, "key"], [24, 7, 12, 46], [24, 9, 12, 48], [25, 2, 12, 57], [25, 3, 12, 58], [25, 4, 12, 59], [25, 6, 13, 2], [25, 7, 13, 3], [25, 13, 13, 9], [25, 15, 13, 11], [26, 4, 13, 13, "x1"], [26, 6, 13, 15], [26, 8, 13, 17], [26, 15, 13, 24], [27, 4, 13, 26, "x2"], [27, 6, 13, 28], [27, 8, 13, 30], [27, 12, 13, 34], [28, 4, 13, 36, "y1"], [28, 6, 13, 38], [28, 8, 13, 40], [28, 11, 13, 43], [29, 4, 13, 45, "y2"], [29, 6, 13, 47], [29, 8, 13, 49], [29, 11, 13, 52], [30, 4, 13, 54, "key"], [30, 7, 13, 57], [30, 9, 13, 59], [31, 2, 13, 68], [31, 3, 13, 69], [31, 4, 13, 70], [31, 6, 14, 2], [31, 7, 14, 3], [31, 13, 14, 9], [31, 15, 14, 11], [32, 4, 14, 13, "x1"], [32, 6, 14, 15], [32, 8, 14, 17], [32, 14, 14, 23], [33, 4, 14, 25, "x2"], [33, 6, 14, 27], [33, 8, 14, 29], [33, 14, 14, 35], [34, 4, 14, 37, "y1"], [34, 6, 14, 39], [34, 8, 14, 41], [34, 14, 14, 47], [35, 4, 14, 49, "y2"], [35, 6, 14, 51], [35, 8, 14, 53], [35, 12, 14, 57], [36, 4, 14, 59, "key"], [36, 7, 14, 62], [36, 9, 14, 64], [37, 2, 14, 73], [37, 3, 14, 74], [37, 4, 14, 75], [37, 6, 15, 2], [37, 7, 15, 3], [37, 13, 15, 9], [37, 15, 15, 11], [38, 4, 15, 13, "x1"], [38, 6, 15, 15], [38, 8, 15, 17], [38, 15, 15, 24], [39, 4, 15, 26, "x2"], [39, 6, 15, 28], [39, 8, 15, 30], [39, 15, 15, 37], [40, 4, 15, 39, "y1"], [40, 6, 15, 41], [40, 8, 15, 43], [40, 15, 15, 50], [41, 4, 15, 52, "y2"], [41, 6, 15, 54], [41, 8, 15, 56], [41, 12, 15, 60], [42, 4, 15, 62, "key"], [42, 7, 15, 65], [42, 9, 15, 67], [43, 2, 15, 76], [43, 3, 15, 77], [43, 4, 15, 78], [43, 5, 16, 1], [43, 6, 16, 2], [44, 0, 16, 3], [44, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}