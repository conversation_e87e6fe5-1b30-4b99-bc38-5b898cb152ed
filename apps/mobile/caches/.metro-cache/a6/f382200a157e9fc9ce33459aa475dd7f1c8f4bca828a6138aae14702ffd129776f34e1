{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/findNodeHandle", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "2RP7Dp//wHy/IL2D3/pkDbKjgUI=", "exportNames": ["*"]}}, {"name": "./handlersRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 77}, "end": {"line": 2, "column": 52, "index": 129}}], "key": "icHMSVIKxbHLSdF6K64ideInyBg=", "exportNames": ["*"]}}, {"name": "../utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 130}, "end": {"line": 3, "column": 35, "index": 165}}], "key": "mL7nJyZhzUYx+zMcIt1cBzVuRps=", "exportNames": ["*"]}}, {"name": "../RNGestureHandlerModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 166}, "end": {"line": 4, "column": 63, "index": 229}}], "key": "bY7FGgfi8WGOEKHKyXsenNEOYXM=", "exportNames": ["*"]}}, {"name": "../ghQueueMicrotask", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 230}, "end": {"line": 5, "column": 55, "index": 285}}], "key": "Ty3ERJQ4RajY8XDWg1+a8wq7RdE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.filterConfig = filterConfig;\n  exports.findNodeHandle = findNodeHandle;\n  exports.scheduleFlushOperations = scheduleFlushOperations;\n  exports.transformIntoHandlerTags = transformIntoHandlerTags;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/Platform\"));\n  var _findNodeHandle = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/findNodeHandle\"));\n  var _handlersRegistry = require(_dependencyMap[3], \"./handlersRegistry\");\n  var _utils = require(_dependencyMap[4], \"../utils\");\n  var _RNGestureHandlerModule = _interopRequireDefault(require(_dependencyMap[5], \"../RNGestureHandlerModule\"));\n  var _ghQueueMicrotask = require(_dependencyMap[6], \"../ghQueueMicrotask\");\n  function isConfigParam(param, name) {\n    // param !== Object(param) returns false if `param` is a function\n    // or an object and returns true if `param` is null\n    return param !== undefined && (param !== Object(param) || !('__isNative' in param)) && name !== 'onHandlerStateChange' && name !== 'onGestureEvent';\n  }\n  function filterConfig(props, validProps, defaults = {}) {\n    const filteredConfig = {\n      ...defaults\n    };\n    for (const key of validProps) {\n      let value = props[key];\n      if (isConfigParam(value, key)) {\n        if (key === 'simultaneousHandlers' || key === 'waitFor') {\n          value = transformIntoHandlerTags(props[key]);\n        } else if (key === 'hitSlop' && typeof value !== 'object') {\n          value = {\n            top: value,\n            left: value,\n            bottom: value,\n            right: value\n          };\n        }\n        filteredConfig[key] = value;\n      }\n    }\n    return filteredConfig;\n  }\n  function transformIntoHandlerTags(handlerIDs) {\n    handlerIDs = (0, _utils.toArray)(handlerIDs);\n    if (_Platform.default.OS === 'web') {\n      return handlerIDs.map(({\n        current\n      }) => current).filter(handle => handle);\n    } // converts handler string IDs into their numeric tags\n\n    return handlerIDs.map(handlerID => {\n      var _handlerID$current;\n      return _handlersRegistry.handlerIDToTag[handlerID] || ((_handlerID$current = handlerID.current) === null || _handlerID$current === void 0 ? void 0 : _handlerID$current.handlerTag) || -1;\n    }).filter(handlerTag => handlerTag > 0);\n  }\n  function findNodeHandle(node) {\n    if (_Platform.default.OS === 'web') {\n      return node;\n    }\n    return (0, _findNodeHandle.default)(node);\n  }\n  let flushOperationsScheduled = false;\n  function scheduleFlushOperations() {\n    if (!flushOperationsScheduled) {\n      flushOperationsScheduled = true;\n      (0, _ghQueueMicrotask.ghQueueMicrotask)(() => {\n        _RNGestureHandlerModule.default.flushOperations();\n        flushOperationsScheduled = false;\n      });\n    }\n  }\n});", "lineCount": 72, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "_handlersRegistry"], [12, 23, 2, 0], [12, 26, 2, 0, "require"], [12, 33, 2, 0], [12, 34, 2, 0, "_dependencyMap"], [12, 48, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_utils"], [13, 12, 3, 0], [13, 15, 3, 0, "require"], [13, 22, 3, 0], [13, 23, 3, 0, "_dependencyMap"], [13, 37, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_RNGestureHandlerModule"], [14, 29, 4, 0], [14, 32, 4, 0, "_interopRequireDefault"], [14, 54, 4, 0], [14, 55, 4, 0, "require"], [14, 62, 4, 0], [14, 63, 4, 0, "_dependencyMap"], [14, 77, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_ghQueueMicrotask"], [15, 23, 5, 0], [15, 26, 5, 0, "require"], [15, 33, 5, 0], [15, 34, 5, 0, "_dependencyMap"], [15, 48, 5, 0], [16, 2, 7, 0], [16, 11, 7, 9, "isConfigParam"], [16, 24, 7, 22, "isConfigParam"], [16, 25, 7, 23, "param"], [16, 30, 7, 28], [16, 32, 7, 30, "name"], [16, 36, 7, 34], [16, 38, 7, 36], [17, 4, 8, 2], [18, 4, 9, 2], [19, 4, 10, 2], [19, 11, 10, 9, "param"], [19, 16, 10, 14], [19, 21, 10, 19, "undefined"], [19, 30, 10, 28], [19, 35, 10, 33, "param"], [19, 40, 10, 38], [19, 45, 10, 43, "Object"], [19, 51, 10, 49], [19, 52, 10, 50, "param"], [19, 57, 10, 55], [19, 58, 10, 56], [19, 62, 10, 60], [19, 64, 10, 62], [19, 76, 10, 74], [19, 80, 10, 78, "param"], [19, 85, 10, 83], [19, 86, 10, 84], [19, 87, 10, 85], [19, 91, 10, 89, "name"], [19, 95, 10, 93], [19, 100, 10, 98], [19, 122, 10, 120], [19, 126, 10, 124, "name"], [19, 130, 10, 128], [19, 135, 10, 133], [19, 151, 10, 149], [20, 2, 11, 0], [21, 2, 13, 7], [21, 11, 13, 16, "filterConfig"], [21, 23, 13, 28, "filterConfig"], [21, 24, 13, 29, "props"], [21, 29, 13, 34], [21, 31, 13, 36, "validProps"], [21, 41, 13, 46], [21, 43, 13, 48, "defaults"], [21, 51, 13, 56], [21, 54, 13, 59], [21, 55, 13, 60], [21, 56, 13, 61], [21, 58, 13, 63], [22, 4, 14, 2], [22, 10, 14, 8, "filteredConfig"], [22, 24, 14, 22], [22, 27, 14, 25], [23, 6, 14, 27], [23, 9, 14, 30, "defaults"], [24, 4, 15, 2], [24, 5, 15, 3], [25, 4, 17, 2], [25, 9, 17, 7], [25, 15, 17, 13, "key"], [25, 18, 17, 16], [25, 22, 17, 20, "validProps"], [25, 32, 17, 30], [25, 34, 17, 32], [26, 6, 18, 4], [26, 10, 18, 8, "value"], [26, 15, 18, 13], [26, 18, 18, 16, "props"], [26, 23, 18, 21], [26, 24, 18, 22, "key"], [26, 27, 18, 25], [26, 28, 18, 26], [27, 6, 20, 4], [27, 10, 20, 8, "isConfigParam"], [27, 23, 20, 21], [27, 24, 20, 22, "value"], [27, 29, 20, 27], [27, 31, 20, 29, "key"], [27, 34, 20, 32], [27, 35, 20, 33], [27, 37, 20, 35], [28, 8, 21, 6], [28, 12, 21, 10, "key"], [28, 15, 21, 13], [28, 20, 21, 18], [28, 42, 21, 40], [28, 46, 21, 44, "key"], [28, 49, 21, 47], [28, 54, 21, 52], [28, 63, 21, 61], [28, 65, 21, 63], [29, 10, 22, 8, "value"], [29, 15, 22, 13], [29, 18, 22, 16, "transformIntoHandlerTags"], [29, 42, 22, 40], [29, 43, 22, 41, "props"], [29, 48, 22, 46], [29, 49, 22, 47, "key"], [29, 52, 22, 50], [29, 53, 22, 51], [29, 54, 22, 52], [30, 8, 23, 6], [30, 9, 23, 7], [30, 15, 23, 13], [30, 19, 23, 17, "key"], [30, 22, 23, 20], [30, 27, 23, 25], [30, 36, 23, 34], [30, 40, 23, 38], [30, 47, 23, 45, "value"], [30, 52, 23, 50], [30, 57, 23, 55], [30, 65, 23, 63], [30, 67, 23, 65], [31, 10, 24, 8, "value"], [31, 15, 24, 13], [31, 18, 24, 16], [32, 12, 25, 10, "top"], [32, 15, 25, 13], [32, 17, 25, 15, "value"], [32, 22, 25, 20], [33, 12, 26, 10, "left"], [33, 16, 26, 14], [33, 18, 26, 16, "value"], [33, 23, 26, 21], [34, 12, 27, 10, "bottom"], [34, 18, 27, 16], [34, 20, 27, 18, "value"], [34, 25, 27, 23], [35, 12, 28, 10, "right"], [35, 17, 28, 15], [35, 19, 28, 17, "value"], [36, 10, 29, 8], [36, 11, 29, 9], [37, 8, 30, 6], [38, 8, 32, 6, "filteredConfig"], [38, 22, 32, 20], [38, 23, 32, 21, "key"], [38, 26, 32, 24], [38, 27, 32, 25], [38, 30, 32, 28, "value"], [38, 35, 32, 33], [39, 6, 33, 4], [40, 4, 34, 2], [41, 4, 36, 2], [41, 11, 36, 9, "filteredConfig"], [41, 25, 36, 23], [42, 2, 37, 0], [43, 2, 38, 7], [43, 11, 38, 16, "transformIntoHandlerTags"], [43, 35, 38, 40, "transformIntoHandlerTags"], [43, 36, 38, 41, "handlerIDs"], [43, 46, 38, 51], [43, 48, 38, 53], [44, 4, 39, 2, "handlerIDs"], [44, 14, 39, 12], [44, 17, 39, 15], [44, 21, 39, 15, "toArray"], [44, 35, 39, 22], [44, 37, 39, 23, "handlerIDs"], [44, 47, 39, 33], [44, 48, 39, 34], [45, 4, 41, 2], [45, 8, 41, 6, "Platform"], [45, 25, 41, 14], [45, 26, 41, 15, "OS"], [45, 28, 41, 17], [45, 33, 41, 22], [45, 38, 41, 27], [45, 40, 41, 29], [46, 6, 42, 4], [46, 13, 42, 11, "handlerIDs"], [46, 23, 42, 21], [46, 24, 42, 22, "map"], [46, 27, 42, 25], [46, 28, 42, 26], [46, 29, 42, 27], [47, 8, 43, 6, "current"], [48, 6, 44, 4], [48, 7, 44, 5], [48, 12, 44, 10, "current"], [48, 19, 44, 17], [48, 20, 44, 18], [48, 21, 44, 19, "filter"], [48, 27, 44, 25], [48, 28, 44, 26, "handle"], [48, 34, 44, 32], [48, 38, 44, 36, "handle"], [48, 44, 44, 42], [48, 45, 44, 43], [49, 4, 45, 2], [49, 5, 45, 3], [49, 6, 45, 4], [51, 4, 48, 2], [51, 11, 48, 9, "handlerIDs"], [51, 21, 48, 19], [51, 22, 48, 20, "map"], [51, 25, 48, 23], [51, 26, 48, 24, "handlerID"], [51, 35, 48, 33], [51, 39, 48, 37], [52, 6, 49, 4], [52, 10, 49, 8, "_handlerID$current"], [52, 28, 49, 26], [53, 6, 51, 4], [53, 13, 51, 11, "handlerIDToTag"], [53, 45, 51, 25], [53, 46, 51, 26, "handlerID"], [53, 55, 51, 35], [53, 56, 51, 36], [53, 61, 51, 41], [53, 62, 51, 42, "_handlerID$current"], [53, 80, 51, 60], [53, 83, 51, 63, "handlerID"], [53, 92, 51, 72], [53, 93, 51, 73, "current"], [53, 100, 51, 80], [53, 106, 51, 86], [53, 110, 51, 90], [53, 114, 51, 94, "_handlerID$current"], [53, 132, 51, 112], [53, 137, 51, 117], [53, 142, 51, 122], [53, 143, 51, 123], [53, 146, 51, 126], [53, 151, 51, 131], [53, 152, 51, 132], [53, 155, 51, 135, "_handlerID$current"], [53, 173, 51, 153], [53, 174, 51, 154, "handlerTag"], [53, 184, 51, 164], [53, 185, 51, 165], [53, 189, 51, 169], [53, 190, 51, 170], [53, 191, 51, 171], [54, 4, 52, 2], [54, 5, 52, 3], [54, 6, 52, 4], [54, 7, 52, 5, "filter"], [54, 13, 52, 11], [54, 14, 52, 12, "handlerTag"], [54, 24, 52, 22], [54, 28, 52, 26, "handlerTag"], [54, 38, 52, 36], [54, 41, 52, 39], [54, 42, 52, 40], [54, 43, 52, 41], [55, 2, 53, 0], [56, 2, 54, 7], [56, 11, 54, 16, "findNodeHandle"], [56, 25, 54, 30, "findNodeHandle"], [56, 26, 54, 31, "node"], [56, 30, 54, 35], [56, 32, 54, 37], [57, 4, 55, 2], [57, 8, 55, 6, "Platform"], [57, 25, 55, 14], [57, 26, 55, 15, "OS"], [57, 28, 55, 17], [57, 33, 55, 22], [57, 38, 55, 27], [57, 40, 55, 29], [58, 6, 56, 4], [58, 13, 56, 11, "node"], [58, 17, 56, 15], [59, 4, 57, 2], [60, 4, 59, 2], [60, 11, 59, 9], [60, 15, 59, 9, "findNodeHandleRN"], [60, 38, 59, 25], [60, 40, 59, 26, "node"], [60, 44, 59, 30], [60, 45, 59, 31], [61, 2, 60, 0], [62, 2, 61, 0], [62, 6, 61, 4, "flushOperationsScheduled"], [62, 30, 61, 28], [62, 33, 61, 31], [62, 38, 61, 36], [63, 2, 62, 7], [63, 11, 62, 16, "scheduleFlushOperations"], [63, 34, 62, 39, "scheduleFlushOperations"], [63, 35, 62, 39], [63, 37, 62, 42], [64, 4, 63, 2], [64, 8, 63, 6], [64, 9, 63, 7, "flushOperationsScheduled"], [64, 33, 63, 31], [64, 35, 63, 33], [65, 6, 64, 4, "flushOperationsScheduled"], [65, 30, 64, 28], [65, 33, 64, 31], [65, 37, 64, 35], [66, 6, 65, 4], [66, 10, 65, 4, "ghQueueMicrotask"], [66, 44, 65, 20], [66, 46, 65, 21], [66, 52, 65, 27], [67, 8, 66, 6, "RNGestureHandlerModule"], [67, 39, 66, 28], [67, 40, 66, 29, "flushOperations"], [67, 55, 66, 44], [67, 56, 66, 45], [67, 57, 66, 46], [68, 8, 67, 6, "flushOperationsScheduled"], [68, 32, 67, 30], [68, 35, 67, 33], [68, 40, 67, 38], [69, 6, 68, 4], [69, 7, 68, 5], [69, 8, 68, 6], [70, 4, 69, 2], [71, 2, 70, 0], [72, 0, 70, 1], [72, 3]], "functionMap": {"names": ["<global>", "isConfigParam", "filterConfig", "transformIntoHandlerTags", "handlerIDs.map$argument_0", "handlerIDs.map.filter$argument_0", "findNodeHandle", "scheduleFlushOperations", "ghQueueMicrotask$argument_0"], "mappings": "AAA;ACM;CDI;OEE;CFwB;OGC;0BCI;iBDE,SE,gBF;wBCI;GDI,SE,4BF;CHC;OMC;CNM;OOE;qBCG;KDG;CPE"}}, "type": "js/module"}]}