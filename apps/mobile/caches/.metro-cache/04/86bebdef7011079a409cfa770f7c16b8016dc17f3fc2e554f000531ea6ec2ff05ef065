{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-native/normalize-colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 61}}], "key": "r6xNj+hfHNSiyr0OqQ2Fc9JYEeE=", "exportNames": ["*"]}}, {"name": "./PlatformColorValueTypes", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 22, "column": 35}, "end": {"line": 22, "column": 71}}], "key": "g0XBHjargLFmPwBr3IU8GyS7eiQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _normalizeColors = _interopRequireDefault(require(_dependencyMap[1], \"@react-native/normalize-colors\"));\n  function normalizeColor(color) {\n    if (typeof color === 'object' && color != null) {\n      var _require = require(_dependencyMap[2], \"./PlatformColorValueTypes\"),\n        normalizeColorObject = _require.normalizeColorObject;\n      var normalizedColor = normalizeColorObject(color);\n      if (normalizedColor != null) {\n        return normalizedColor;\n      }\n    }\n    if (typeof color === 'string' || typeof color === 'number') {\n      return (0, _normalizeColors.default)(color);\n    }\n  }\n  var _default = exports.default = normalizeColor;\n});", "lineCount": 22, "map": [[7, 2, 16, 0], [7, 6, 16, 0, "_normalizeColors"], [7, 22, 16, 0], [7, 25, 16, 0, "_interopRequireDefault"], [7, 47, 16, 0], [7, 48, 16, 0, "require"], [7, 55, 16, 0], [7, 56, 16, 0, "_dependencyMap"], [7, 70, 16, 0], [8, 2, 18, 0], [8, 11, 18, 9, "normalizeColor"], [8, 25, 18, 23, "normalizeColor"], [8, 26, 19, 2, "color"], [8, 31, 19, 44], [8, 33, 20, 24], [9, 4, 21, 2], [9, 8, 21, 6], [9, 15, 21, 13, "color"], [9, 20, 21, 18], [9, 25, 21, 23], [9, 33, 21, 31], [9, 37, 21, 35, "color"], [9, 42, 21, 40], [9, 46, 21, 44], [9, 50, 21, 48], [9, 52, 21, 50], [10, 6, 22, 4], [10, 10, 22, 4, "_require"], [10, 18, 22, 4], [10, 21, 22, 35, "require"], [10, 28, 22, 42], [10, 29, 22, 42, "_dependencyMap"], [10, 43, 22, 42], [10, 75, 22, 70], [10, 76, 22, 71], [11, 8, 22, 11, "normalizeColorObject"], [11, 28, 22, 31], [11, 31, 22, 31, "_require"], [11, 39, 22, 31], [11, 40, 22, 11, "normalizeColorObject"], [11, 60, 22, 31], [12, 6, 23, 4], [12, 10, 23, 10, "normalizedColor"], [12, 25, 23, 25], [12, 28, 23, 28, "normalizeColorObject"], [12, 48, 23, 48], [12, 49, 23, 49, "color"], [12, 54, 23, 54], [12, 55, 23, 55], [13, 6, 24, 4], [13, 10, 24, 8, "normalizedColor"], [13, 25, 24, 23], [13, 29, 24, 27], [13, 33, 24, 31], [13, 35, 24, 33], [14, 8, 25, 6], [14, 15, 25, 13, "normalizedColor"], [14, 30, 25, 28], [15, 6, 26, 4], [16, 4, 27, 2], [17, 4, 29, 2], [17, 8, 29, 6], [17, 15, 29, 13, "color"], [17, 20, 29, 18], [17, 25, 29, 23], [17, 33, 29, 31], [17, 37, 29, 35], [17, 44, 29, 42, "color"], [17, 49, 29, 47], [17, 54, 29, 52], [17, 62, 29, 60], [17, 64, 29, 62], [18, 6, 30, 4], [18, 13, 30, 11], [18, 17, 30, 11, "_normalizeColor"], [18, 41, 30, 26], [18, 43, 30, 27, "color"], [18, 48, 30, 32], [18, 49, 30, 33], [19, 4, 31, 2], [20, 2, 32, 0], [21, 2, 32, 1], [21, 6, 32, 1, "_default"], [21, 14, 32, 1], [21, 17, 32, 1, "exports"], [21, 24, 32, 1], [21, 25, 32, 1, "default"], [21, 32, 32, 1], [21, 35, 34, 15, "normalizeColor"], [21, 49, 34, 29], [22, 0, 34, 29], [22, 3]], "functionMap": {"names": ["<global>", "normalizeColor"], "mappings": "AAA;ACiB;CDc"}}, "type": "js/module"}]}