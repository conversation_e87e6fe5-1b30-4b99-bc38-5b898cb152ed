{"dependencies": [{"name": "./utils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 19}, "end": {"line": 2, "column": 34, "index": 53}}], "key": "NIaSEHO1E48gsZc7jH9Ex1xTHgE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.pendingThenable = pendingThenable;\n  exports.tryResolveSync = tryResolveSync;\n  var _utils = require(_dependencyMap[0], \"./utils.js\");\n  // src/thenable.ts\n\n  function pendingThenable() {\n    let resolve;\n    let reject;\n    const thenable = new Promise((_resolve, _reject) => {\n      resolve = _resolve;\n      reject = _reject;\n    });\n    thenable.status = \"pending\";\n    thenable.catch(() => {});\n    function finalize(data) {\n      Object.assign(thenable, data);\n      delete thenable.resolve;\n      delete thenable.reject;\n    }\n    thenable.resolve = value => {\n      finalize({\n        status: \"fulfilled\",\n        value\n      });\n      resolve(value);\n    };\n    thenable.reject = reason => {\n      finalize({\n        status: \"rejected\",\n        reason\n      });\n      reject(reason);\n    };\n    return thenable;\n  }\n  function tryResolveSync(promise) {\n    let data;\n    promise.then(result => {\n      data = result;\n      return result;\n    }, _utils.noop)?.catch(_utils.noop);\n    if (data !== void 0) {\n      return {\n        data\n      };\n    }\n    return void 0;\n  }\n});", "lineCount": 53, "map": [[7, 2, 2, 0], [7, 6, 2, 0, "_utils"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 1, 0], [10, 2, 3, 0], [10, 11, 3, 9, "pendingThenable"], [10, 26, 3, 24, "pendingThenable"], [10, 27, 3, 24], [10, 29, 3, 27], [11, 4, 4, 2], [11, 8, 4, 6, "resolve"], [11, 15, 4, 13], [12, 4, 5, 2], [12, 8, 5, 6, "reject"], [12, 14, 5, 12], [13, 4, 6, 2], [13, 10, 6, 8, "thenable"], [13, 18, 6, 16], [13, 21, 6, 19], [13, 25, 6, 23, "Promise"], [13, 32, 6, 30], [13, 33, 6, 31], [13, 34, 6, 32, "_resolve"], [13, 42, 6, 40], [13, 44, 6, 42, "_reject"], [13, 51, 6, 49], [13, 56, 6, 54], [14, 6, 7, 4, "resolve"], [14, 13, 7, 11], [14, 16, 7, 14, "_resolve"], [14, 24, 7, 22], [15, 6, 8, 4, "reject"], [15, 12, 8, 10], [15, 15, 8, 13, "_reject"], [15, 22, 8, 20], [16, 4, 9, 2], [16, 5, 9, 3], [16, 6, 9, 4], [17, 4, 10, 2, "thenable"], [17, 12, 10, 10], [17, 13, 10, 11, "status"], [17, 19, 10, 17], [17, 22, 10, 20], [17, 31, 10, 29], [18, 4, 11, 2, "thenable"], [18, 12, 11, 10], [18, 13, 11, 11, "catch"], [18, 18, 11, 16], [18, 19, 11, 17], [18, 25, 11, 23], [18, 26, 12, 2], [18, 27, 12, 3], [18, 28, 12, 4], [19, 4, 13, 2], [19, 13, 13, 11, "finalize"], [19, 21, 13, 19, "finalize"], [19, 22, 13, 20, "data"], [19, 26, 13, 24], [19, 28, 13, 26], [20, 6, 14, 4, "Object"], [20, 12, 14, 10], [20, 13, 14, 11, "assign"], [20, 19, 14, 17], [20, 20, 14, 18, "thenable"], [20, 28, 14, 26], [20, 30, 14, 28, "data"], [20, 34, 14, 32], [20, 35, 14, 33], [21, 6, 15, 4], [21, 13, 15, 11, "thenable"], [21, 21, 15, 19], [21, 22, 15, 20, "resolve"], [21, 29, 15, 27], [22, 6, 16, 4], [22, 13, 16, 11, "thenable"], [22, 21, 16, 19], [22, 22, 16, 20, "reject"], [22, 28, 16, 26], [23, 4, 17, 2], [24, 4, 18, 2, "thenable"], [24, 12, 18, 10], [24, 13, 18, 11, "resolve"], [24, 20, 18, 18], [24, 23, 18, 22, "value"], [24, 28, 18, 27], [24, 32, 18, 32], [25, 6, 19, 4, "finalize"], [25, 14, 19, 12], [25, 15, 19, 13], [26, 8, 20, 6, "status"], [26, 14, 20, 12], [26, 16, 20, 14], [26, 27, 20, 25], [27, 8, 21, 6, "value"], [28, 6, 22, 4], [28, 7, 22, 5], [28, 8, 22, 6], [29, 6, 23, 4, "resolve"], [29, 13, 23, 11], [29, 14, 23, 12, "value"], [29, 19, 23, 17], [29, 20, 23, 18], [30, 4, 24, 2], [30, 5, 24, 3], [31, 4, 25, 2, "thenable"], [31, 12, 25, 10], [31, 13, 25, 11, "reject"], [31, 19, 25, 17], [31, 22, 25, 21, "reason"], [31, 28, 25, 27], [31, 32, 25, 32], [32, 6, 26, 4, "finalize"], [32, 14, 26, 12], [32, 15, 26, 13], [33, 8, 27, 6, "status"], [33, 14, 27, 12], [33, 16, 27, 14], [33, 26, 27, 24], [34, 8, 28, 6, "reason"], [35, 6, 29, 4], [35, 7, 29, 5], [35, 8, 29, 6], [36, 6, 30, 4, "reject"], [36, 12, 30, 10], [36, 13, 30, 11, "reason"], [36, 19, 30, 17], [36, 20, 30, 18], [37, 4, 31, 2], [37, 5, 31, 3], [38, 4, 32, 2], [38, 11, 32, 9, "thenable"], [38, 19, 32, 17], [39, 2, 33, 0], [40, 2, 34, 0], [40, 11, 34, 9, "tryResolveSync"], [40, 25, 34, 23, "tryResolveSync"], [40, 26, 34, 24, "promise"], [40, 33, 34, 31], [40, 35, 34, 33], [41, 4, 35, 2], [41, 8, 35, 6, "data"], [41, 12, 35, 10], [42, 4, 36, 2, "promise"], [42, 11, 36, 9], [42, 12, 36, 10, "then"], [42, 16, 36, 14], [42, 17, 36, 16, "result"], [42, 23, 36, 22], [42, 27, 36, 27], [43, 6, 37, 4, "data"], [43, 10, 37, 8], [43, 13, 37, 11, "result"], [43, 19, 37, 17], [44, 6, 38, 4], [44, 13, 38, 11, "result"], [44, 19, 38, 17], [45, 4, 39, 2], [45, 5, 39, 3], [45, 7, 39, 5, "noop"], [45, 18, 39, 9], [45, 19, 39, 10], [45, 21, 39, 12, "catch"], [45, 26, 39, 17], [45, 27, 39, 18, "noop"], [45, 38, 39, 22], [45, 39, 39, 23], [46, 4, 40, 2], [46, 8, 40, 6, "data"], [46, 12, 40, 10], [46, 17, 40, 15], [46, 22, 40, 20], [46, 23, 40, 21], [46, 25, 40, 23], [47, 6, 41, 4], [47, 13, 41, 11], [48, 8, 41, 13, "data"], [49, 6, 41, 18], [49, 7, 41, 19], [50, 4, 42, 2], [51, 4, 43, 2], [51, 11, 43, 9], [51, 16, 43, 14], [51, 17, 43, 15], [52, 2, 44, 0], [53, 0, 44, 1], [53, 3]], "functionMap": {"names": ["<global>", "pendingThenable", "Promise$argument_0", "_catch$argument_0", "finalize", "resolve", "reject", "tryResolveSync", "promise.then$argument_0"], "mappings": "AAA;ACE;+BCG;GDG;iBEE;GFC;EGC;GHI;qBIC;GJM;oBKC;GLM;CDE;AOC;eCE;GDG;CPK"}}, "type": "js/module"}]}