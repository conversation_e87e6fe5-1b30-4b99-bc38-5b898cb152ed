{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./EventManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 42, "index": 253}}], "key": "+tTRfUGWY5rcrw6ZVLEgvlFTMQc=", "exportNames": ["*"]}}, {"name": "../interfaces", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 254}, "end": {"line": 4, "column": 43, "index": 297}}], "key": "GMKh3a5g5xNaAog15vl07v6pG2U=", "exportNames": ["*"]}}, {"name": "../../PointerType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 298}, "end": {"line": 5, "column": 48, "index": 346}}], "key": "PNpP2j+zRZwLQ3w6ZmXPMJNakiU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _EventManager = _interopRequireDefault(require(_dependencyMap[1], \"./EventManager\"));\n  var _interfaces = require(_dependencyMap[2], \"../interfaces\");\n  var _PointerType = require(_dependencyMap[3], \"../../PointerType\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  class WheelEventManager extends _EventManager.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"wheelDelta\", {\n        x: 0,\n        y: 0\n      });\n      _defineProperty(this, \"resetDelta\", _event => {\n        this.wheelDelta = {\n          x: 0,\n          y: 0\n        };\n      });\n      _defineProperty(this, \"wheelCallback\", event => {\n        this.wheelDelta.x += event.deltaX;\n        this.wheelDelta.y += event.deltaY;\n        const adaptedEvent = this.mapEvent(event);\n        this.onWheel(adaptedEvent);\n      });\n    }\n    registerListeners() {\n      this.view.addEventListener('pointermove', this.resetDelta);\n      this.view.addEventListener('wheel', this.wheelCallback);\n    }\n    unregisterListeners() {\n      this.view.removeEventListener('pointermove', this.resetDelta);\n      this.view.removeEventListener('wheel', this.wheelCallback);\n    }\n    mapEvent(event) {\n      return {\n        x: event.clientX + this.wheelDelta.x,\n        y: event.clientY + this.wheelDelta.y,\n        offsetX: event.offsetX - event.deltaX,\n        offsetY: event.offsetY - event.deltaY,\n        pointerId: -1,\n        eventType: _interfaces.EventTypes.MOVE,\n        pointerType: _PointerType.PointerType.OTHER,\n        time: event.timeStamp,\n        // @ts-ignore It does exist, but it's deprecated\n        wheelDeltaY: event.wheelDeltaY\n      };\n    }\n    resetManager() {\n      super.resetManager();\n    }\n  }\n  exports.default = WheelEventManager;\n});", "lineCount": 70, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_EventManager"], [7, 19, 3, 0], [7, 22, 3, 0, "_interopRequireDefault"], [7, 44, 3, 0], [7, 45, 3, 0, "require"], [7, 52, 3, 0], [7, 53, 3, 0, "_dependencyMap"], [7, 67, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_interfaces"], [8, 17, 4, 0], [8, 20, 4, 0, "require"], [8, 27, 4, 0], [8, 28, 4, 0, "_dependencyMap"], [8, 42, 4, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_PointerType"], [9, 18, 5, 0], [9, 21, 5, 0, "require"], [9, 28, 5, 0], [9, 29, 5, 0, "_dependencyMap"], [9, 43, 5, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "obj"], [10, 30, 1, 28], [10, 32, 1, 30, "key"], [10, 35, 1, 33], [10, 37, 1, 35, "value"], [10, 42, 1, 40], [10, 44, 1, 42], [11, 4, 1, 44], [11, 8, 1, 48, "key"], [11, 11, 1, 51], [11, 15, 1, 55, "obj"], [11, 18, 1, 58], [11, 20, 1, 60], [12, 6, 1, 62, "Object"], [12, 12, 1, 68], [12, 13, 1, 69, "defineProperty"], [12, 27, 1, 83], [12, 28, 1, 84, "obj"], [12, 31, 1, 87], [12, 33, 1, 89, "key"], [12, 36, 1, 92], [12, 38, 1, 94], [13, 8, 1, 96, "value"], [13, 13, 1, 101], [13, 15, 1, 103, "value"], [13, 20, 1, 108], [14, 8, 1, 110, "enumerable"], [14, 18, 1, 120], [14, 20, 1, 122], [14, 24, 1, 126], [15, 8, 1, 128, "configurable"], [15, 20, 1, 140], [15, 22, 1, 142], [15, 26, 1, 146], [16, 8, 1, 148, "writable"], [16, 16, 1, 156], [16, 18, 1, 158], [17, 6, 1, 163], [17, 7, 1, 164], [17, 8, 1, 165], [18, 4, 1, 167], [18, 5, 1, 168], [18, 11, 1, 174], [19, 6, 1, 176, "obj"], [19, 9, 1, 179], [19, 10, 1, 180, "key"], [19, 13, 1, 183], [19, 14, 1, 184], [19, 17, 1, 187, "value"], [19, 22, 1, 192], [20, 4, 1, 194], [21, 4, 1, 196], [21, 11, 1, 203, "obj"], [21, 14, 1, 206], [22, 2, 1, 208], [23, 2, 6, 15], [23, 8, 6, 21, "WheelEventManager"], [23, 25, 6, 38], [23, 34, 6, 47, "EventManager"], [23, 55, 6, 59], [23, 56, 6, 60], [24, 4, 7, 2, "constructor"], [24, 15, 7, 13, "constructor"], [24, 16, 7, 14], [24, 19, 7, 17, "args"], [24, 23, 7, 21], [24, 25, 7, 23], [25, 6, 8, 4], [25, 11, 8, 9], [25, 12, 8, 10], [25, 15, 8, 13, "args"], [25, 19, 8, 17], [25, 20, 8, 18], [26, 6, 10, 4, "_defineProperty"], [26, 21, 10, 19], [26, 22, 10, 20], [26, 26, 10, 24], [26, 28, 10, 26], [26, 40, 10, 38], [26, 42, 10, 40], [27, 8, 11, 6, "x"], [27, 9, 11, 7], [27, 11, 11, 9], [27, 12, 11, 10], [28, 8, 12, 6, "y"], [28, 9, 12, 7], [28, 11, 12, 9], [29, 6, 13, 4], [29, 7, 13, 5], [29, 8, 13, 6], [30, 6, 15, 4, "_defineProperty"], [30, 21, 15, 19], [30, 22, 15, 20], [30, 26, 15, 24], [30, 28, 15, 26], [30, 40, 15, 38], [30, 42, 15, 40, "_event"], [30, 48, 15, 46], [30, 52, 15, 50], [31, 8, 16, 6], [31, 12, 16, 10], [31, 13, 16, 11, "wheelDelta"], [31, 23, 16, 21], [31, 26, 16, 24], [32, 10, 17, 8, "x"], [32, 11, 17, 9], [32, 13, 17, 11], [32, 14, 17, 12], [33, 10, 18, 8, "y"], [33, 11, 18, 9], [33, 13, 18, 11], [34, 8, 19, 6], [34, 9, 19, 7], [35, 6, 20, 4], [35, 7, 20, 5], [35, 8, 20, 6], [36, 6, 22, 4, "_defineProperty"], [36, 21, 22, 19], [36, 22, 22, 20], [36, 26, 22, 24], [36, 28, 22, 26], [36, 43, 22, 41], [36, 45, 22, 43, "event"], [36, 50, 22, 48], [36, 54, 22, 52], [37, 8, 23, 6], [37, 12, 23, 10], [37, 13, 23, 11, "wheelDelta"], [37, 23, 23, 21], [37, 24, 23, 22, "x"], [37, 25, 23, 23], [37, 29, 23, 27, "event"], [37, 34, 23, 32], [37, 35, 23, 33, "deltaX"], [37, 41, 23, 39], [38, 8, 24, 6], [38, 12, 24, 10], [38, 13, 24, 11, "wheelDelta"], [38, 23, 24, 21], [38, 24, 24, 22, "y"], [38, 25, 24, 23], [38, 29, 24, 27, "event"], [38, 34, 24, 32], [38, 35, 24, 33, "deltaY"], [38, 41, 24, 39], [39, 8, 25, 6], [39, 14, 25, 12, "adaptedEvent"], [39, 26, 25, 24], [39, 29, 25, 27], [39, 33, 25, 31], [39, 34, 25, 32, "mapEvent"], [39, 42, 25, 40], [39, 43, 25, 41, "event"], [39, 48, 25, 46], [39, 49, 25, 47], [40, 8, 26, 6], [40, 12, 26, 10], [40, 13, 26, 11, "onWheel"], [40, 20, 26, 18], [40, 21, 26, 19, "adaptedEvent"], [40, 33, 26, 31], [40, 34, 26, 32], [41, 6, 27, 4], [41, 7, 27, 5], [41, 8, 27, 6], [42, 4, 28, 2], [43, 4, 30, 2, "registerListeners"], [43, 21, 30, 19, "registerListeners"], [43, 22, 30, 19], [43, 24, 30, 22], [44, 6, 31, 4], [44, 10, 31, 8], [44, 11, 31, 9, "view"], [44, 15, 31, 13], [44, 16, 31, 14, "addEventListener"], [44, 32, 31, 30], [44, 33, 31, 31], [44, 46, 31, 44], [44, 48, 31, 46], [44, 52, 31, 50], [44, 53, 31, 51, "reset<PERSON><PERSON><PERSON>"], [44, 63, 31, 61], [44, 64, 31, 62], [45, 6, 32, 4], [45, 10, 32, 8], [45, 11, 32, 9, "view"], [45, 15, 32, 13], [45, 16, 32, 14, "addEventListener"], [45, 32, 32, 30], [45, 33, 32, 31], [45, 40, 32, 38], [45, 42, 32, 40], [45, 46, 32, 44], [45, 47, 32, 45, "wheelCallback"], [45, 60, 32, 58], [45, 61, 32, 59], [46, 4, 33, 2], [47, 4, 35, 2, "unregisterListeners"], [47, 23, 35, 21, "unregisterListeners"], [47, 24, 35, 21], [47, 26, 35, 24], [48, 6, 36, 4], [48, 10, 36, 8], [48, 11, 36, 9, "view"], [48, 15, 36, 13], [48, 16, 36, 14, "removeEventListener"], [48, 35, 36, 33], [48, 36, 36, 34], [48, 49, 36, 47], [48, 51, 36, 49], [48, 55, 36, 53], [48, 56, 36, 54, "reset<PERSON><PERSON><PERSON>"], [48, 66, 36, 64], [48, 67, 36, 65], [49, 6, 37, 4], [49, 10, 37, 8], [49, 11, 37, 9, "view"], [49, 15, 37, 13], [49, 16, 37, 14, "removeEventListener"], [49, 35, 37, 33], [49, 36, 37, 34], [49, 43, 37, 41], [49, 45, 37, 43], [49, 49, 37, 47], [49, 50, 37, 48, "wheelCallback"], [49, 63, 37, 61], [49, 64, 37, 62], [50, 4, 38, 2], [51, 4, 40, 2, "mapEvent"], [51, 12, 40, 10, "mapEvent"], [51, 13, 40, 11, "event"], [51, 18, 40, 16], [51, 20, 40, 18], [52, 6, 41, 4], [52, 13, 41, 11], [53, 8, 42, 6, "x"], [53, 9, 42, 7], [53, 11, 42, 9, "event"], [53, 16, 42, 14], [53, 17, 42, 15, "clientX"], [53, 24, 42, 22], [53, 27, 42, 25], [53, 31, 42, 29], [53, 32, 42, 30, "wheelDelta"], [53, 42, 42, 40], [53, 43, 42, 41, "x"], [53, 44, 42, 42], [54, 8, 43, 6, "y"], [54, 9, 43, 7], [54, 11, 43, 9, "event"], [54, 16, 43, 14], [54, 17, 43, 15, "clientY"], [54, 24, 43, 22], [54, 27, 43, 25], [54, 31, 43, 29], [54, 32, 43, 30, "wheelDelta"], [54, 42, 43, 40], [54, 43, 43, 41, "y"], [54, 44, 43, 42], [55, 8, 44, 6, "offsetX"], [55, 15, 44, 13], [55, 17, 44, 15, "event"], [55, 22, 44, 20], [55, 23, 44, 21, "offsetX"], [55, 30, 44, 28], [55, 33, 44, 31, "event"], [55, 38, 44, 36], [55, 39, 44, 37, "deltaX"], [55, 45, 44, 43], [56, 8, 45, 6, "offsetY"], [56, 15, 45, 13], [56, 17, 45, 15, "event"], [56, 22, 45, 20], [56, 23, 45, 21, "offsetY"], [56, 30, 45, 28], [56, 33, 45, 31, "event"], [56, 38, 45, 36], [56, 39, 45, 37, "deltaY"], [56, 45, 45, 43], [57, 8, 46, 6, "pointerId"], [57, 17, 46, 15], [57, 19, 46, 17], [57, 20, 46, 18], [57, 21, 46, 19], [58, 8, 47, 6, "eventType"], [58, 17, 47, 15], [58, 19, 47, 17, "EventTypes"], [58, 41, 47, 27], [58, 42, 47, 28, "MOVE"], [58, 46, 47, 32], [59, 8, 48, 6, "pointerType"], [59, 19, 48, 17], [59, 21, 48, 19, "PointerType"], [59, 45, 48, 30], [59, 46, 48, 31, "OTHER"], [59, 51, 48, 36], [60, 8, 49, 6, "time"], [60, 12, 49, 10], [60, 14, 49, 12, "event"], [60, 19, 49, 17], [60, 20, 49, 18, "timeStamp"], [60, 29, 49, 27], [61, 8, 50, 6], [62, 8, 51, 6, "wheelDeltaY"], [62, 19, 51, 17], [62, 21, 51, 19, "event"], [62, 26, 51, 24], [62, 27, 51, 25, "wheelDeltaY"], [63, 6, 52, 4], [63, 7, 52, 5], [64, 4, 53, 2], [65, 4, 55, 2, "resetManager"], [65, 16, 55, 14, "resetManager"], [65, 17, 55, 14], [65, 19, 55, 17], [66, 6, 56, 4], [66, 11, 56, 9], [66, 12, 56, 10, "resetManager"], [66, 24, 56, 22], [66, 25, 56, 23], [66, 26, 56, 24], [67, 4, 57, 2], [68, 2, 59, 0], [69, 2, 59, 1, "exports"], [69, 9, 59, 1], [69, 10, 59, 1, "default"], [69, 17, 59, 1], [69, 20, 59, 1, "WheelEventManager"], [69, 37, 59, 1], [70, 0, 59, 1], [70, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "WheelEventManager", "constructor", "_defineProperty$argument_2", "registerListeners", "unregisterListeners", "mapEvent", "resetManager"], "mappings": "AAA,iNC;eCK;ECC;wCCQ;KDK;2CCE;KDK;GDC;EGE;GHG;EIE;GJG;EKE;GLa;EME;GNE;CDE"}}, "type": "js/module"}]}