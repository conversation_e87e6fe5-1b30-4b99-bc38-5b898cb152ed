{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 68, "index": 68}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ForceTouchGesture = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _get2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/get\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _gesture = require(_dependencyMap[7], \"./gesture\");\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  var _worklet_15288835999946_init_data = {\n    code: \"function changeEventCalculator_reactNativeGestureHandler_forceTouchGestureTs1(current,previous){let changePayload;if(previous===undefined){changePayload={forceChange:current.force};}else{changePayload={forceChange:current.force-previous.force};}return{...current,...changePayload};}\",\n    location: \"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-gesture-handler/src/handlers/gestures/forceTouchGesture.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_reactNativeGestureHandler_forceTouchGestureTs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"forceChange\\\",\\\"force\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Documents/GitHub/ai-brainstormer/apps/mobile/node_modules/react-native-gesture-handler/src/handlers/gestures/forceTouchGesture.ts\\\"],\\\"mappings\\\":\\\"AASA,SAAAA,oEAGEA,CAAAC,OAAA,CAAAC,QAAA,EAEA,GAAI,CAAAC,aAAkD,CACtD,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,WAAW,CAAEJ,OAAO,CAACK,KACvB,CAAC,CACH,CAAC,IAAM,CACLH,aAAa,CAAG,CACdE,WAAW,CAAEJ,OAAO,CAACK,KAAK,CAAGJ,QAAQ,CAACI,KACxC,CAAC,CACH,CAEA,MAAO,CAAE,GAAGL,OAAO,CAAE,GAAGE,aAAc,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var changeEventCalculator = function () {\n    var _e = [new global.Error(), 1, -27];\n    var changeEventCalculator = function (current, previous) {\n      var changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          forceChange: current.force\n        };\n      } else {\n        changePayload = {\n          forceChange: current.force - previous.force\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 15288835999946;\n    changeEventCalculator.__initData = _worklet_15288835999946_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  var ForceTouchGesture = exports.ForceTouchGesture = /*#__PURE__*/function (_ContinousBaseGesture) {\n    function ForceTouchGesture() {\n      var _this;\n      (0, _classCallCheck2.default)(this, ForceTouchGesture);\n      _this = _callSuper(this, ForceTouchGesture);\n      _this.config = {};\n      _this.handlerName = 'ForceTouchGestureHandler';\n      return _this;\n    }\n\n    /**\n     * A minimal pressure that is required before gesture can activate.\n     * Should be a value from range [0.0, 1.0]. Default is 0.2.\n     * @param force\n     */\n    (0, _inherits2.default)(ForceTouchGesture, _ContinousBaseGesture);\n    return (0, _createClass2.default)(ForceTouchGesture, [{\n      key: \"minForce\",\n      value: function minForce(force) {\n        this.config.minForce = force;\n        return this;\n      }\n\n      /**\n       * A maximal pressure that could be applied for gesture.\n       * If the pressure is greater, gesture fails. Should be a value from range [0.0, 1.0].\n       * @param force\n       */\n    }, {\n      key: \"maxForce\",\n      value: function maxForce(force) {\n        this.config.maxForce = force;\n        return this;\n      }\n\n      /**\n       * Value defining if haptic feedback has to be performed on activation.\n       * @param value\n       */\n    }, {\n      key: \"feedbackOnActivation\",\n      value: function feedbackOnActivation(value) {\n        this.config.feedbackOnActivation = value;\n        return this;\n      }\n    }, {\n      key: \"onChange\",\n      value: function onChange(callback) {\n        // @ts-ignore TS being overprotective, ForceTouchGestureHandlerEventPayload is Record\n        this.handlers.changeEventCalculator = changeEventCalculator;\n        return _superPropGet(ForceTouchGesture, \"onChange\", this, 3)([callback]);\n      }\n    }]);\n  }(_gesture.ContinousBaseGesture);\n});", "lineCount": 101, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_gesture"], [13, 14, 1, 0], [13, 17, 1, 0, "require"], [13, 24, 1, 0], [13, 25, 1, 0, "_dependencyMap"], [13, 39, 1, 0], [14, 2, 1, 68], [14, 11, 1, 68, "_callSuper"], [14, 22, 1, 68, "t"], [14, 23, 1, 68], [14, 25, 1, 68, "o"], [14, 26, 1, 68], [14, 28, 1, 68, "e"], [14, 29, 1, 68], [14, 40, 1, 68, "o"], [14, 41, 1, 68], [14, 48, 1, 68, "_getPrototypeOf2"], [14, 64, 1, 68], [14, 65, 1, 68, "default"], [14, 72, 1, 68], [14, 74, 1, 68, "o"], [14, 75, 1, 68], [14, 82, 1, 68, "_possibleConstructorReturn2"], [14, 109, 1, 68], [14, 110, 1, 68, "default"], [14, 117, 1, 68], [14, 119, 1, 68, "t"], [14, 120, 1, 68], [14, 122, 1, 68, "_isNativeReflectConstruct"], [14, 147, 1, 68], [14, 152, 1, 68, "Reflect"], [14, 159, 1, 68], [14, 160, 1, 68, "construct"], [14, 169, 1, 68], [14, 170, 1, 68, "o"], [14, 171, 1, 68], [14, 173, 1, 68, "e"], [14, 174, 1, 68], [14, 186, 1, 68, "_getPrototypeOf2"], [14, 202, 1, 68], [14, 203, 1, 68, "default"], [14, 210, 1, 68], [14, 212, 1, 68, "t"], [14, 213, 1, 68], [14, 215, 1, 68, "constructor"], [14, 226, 1, 68], [14, 230, 1, 68, "o"], [14, 231, 1, 68], [14, 232, 1, 68, "apply"], [14, 237, 1, 68], [14, 238, 1, 68, "t"], [14, 239, 1, 68], [14, 241, 1, 68, "e"], [14, 242, 1, 68], [15, 2, 1, 68], [15, 11, 1, 68, "_isNativeReflectConstruct"], [15, 37, 1, 68], [15, 51, 1, 68, "t"], [15, 52, 1, 68], [15, 56, 1, 68, "Boolean"], [15, 63, 1, 68], [15, 64, 1, 68, "prototype"], [15, 73, 1, 68], [15, 74, 1, 68, "valueOf"], [15, 81, 1, 68], [15, 82, 1, 68, "call"], [15, 86, 1, 68], [15, 87, 1, 68, "Reflect"], [15, 94, 1, 68], [15, 95, 1, 68, "construct"], [15, 104, 1, 68], [15, 105, 1, 68, "Boolean"], [15, 112, 1, 68], [15, 145, 1, 68, "t"], [15, 146, 1, 68], [15, 159, 1, 68, "_isNativeReflectConstruct"], [15, 184, 1, 68], [15, 196, 1, 68, "_isNativeReflectConstruct"], [15, 197, 1, 68], [15, 210, 1, 68, "t"], [15, 211, 1, 68], [16, 2, 1, 68], [16, 11, 1, 68, "_superPropGet"], [16, 25, 1, 68, "t"], [16, 26, 1, 68], [16, 28, 1, 68, "o"], [16, 29, 1, 68], [16, 31, 1, 68, "e"], [16, 32, 1, 68], [16, 34, 1, 68, "r"], [16, 35, 1, 68], [16, 43, 1, 68, "p"], [16, 44, 1, 68], [16, 51, 1, 68, "_get2"], [16, 56, 1, 68], [16, 57, 1, 68, "default"], [16, 64, 1, 68], [16, 70, 1, 68, "_getPrototypeOf2"], [16, 86, 1, 68], [16, 87, 1, 68, "default"], [16, 94, 1, 68], [16, 100, 1, 68, "r"], [16, 101, 1, 68], [16, 104, 1, 68, "t"], [16, 105, 1, 68], [16, 106, 1, 68, "prototype"], [16, 115, 1, 68], [16, 118, 1, 68, "t"], [16, 119, 1, 68], [16, 122, 1, 68, "o"], [16, 123, 1, 68], [16, 125, 1, 68, "e"], [16, 126, 1, 68], [16, 140, 1, 68, "r"], [16, 141, 1, 68], [16, 166, 1, 68, "p"], [16, 167, 1, 68], [16, 180, 1, 68, "t"], [16, 181, 1, 68], [16, 192, 1, 68, "p"], [16, 193, 1, 68], [16, 194, 1, 68, "apply"], [16, 199, 1, 68], [16, 200, 1, 68, "e"], [16, 201, 1, 68], [16, 203, 1, 68, "t"], [16, 204, 1, 68], [16, 211, 1, 68, "p"], [16, 212, 1, 68], [17, 2, 1, 68], [17, 6, 1, 68, "_worklet_15288835999946_init_data"], [17, 39, 1, 68], [18, 4, 1, 68, "code"], [18, 8, 1, 68], [19, 4, 1, 68, "location"], [19, 12, 1, 68], [20, 4, 1, 68, "sourceMap"], [20, 13, 1, 68], [21, 4, 1, 68, "version"], [21, 11, 1, 68], [22, 2, 1, 68], [23, 2, 1, 68], [23, 6, 1, 68, "changeEventCalculator"], [23, 27, 1, 68], [23, 30, 10, 0], [24, 4, 10, 0], [24, 8, 10, 0, "_e"], [24, 10, 10, 0], [24, 18, 10, 0, "global"], [24, 24, 10, 0], [24, 25, 10, 0, "Error"], [24, 30, 10, 0], [25, 4, 10, 0], [25, 8, 10, 0, "changeEventCalculator"], [25, 29, 10, 0], [25, 41, 10, 0, "changeEventCalculator"], [25, 42, 11, 2, "current"], [25, 49, 11, 67], [25, 51, 12, 2, "previous"], [25, 59, 12, 69], [25, 61, 13, 2], [26, 6, 15, 2], [26, 10, 15, 6, "changePayload"], [26, 23, 15, 56], [27, 6, 16, 2], [27, 10, 16, 6, "previous"], [27, 18, 16, 14], [27, 23, 16, 19, "undefined"], [27, 32, 16, 28], [27, 34, 16, 30], [28, 8, 17, 4, "changePayload"], [28, 21, 17, 17], [28, 24, 17, 20], [29, 10, 18, 6, "forceChange"], [29, 21, 18, 17], [29, 23, 18, 19, "current"], [29, 30, 18, 26], [29, 31, 18, 27, "force"], [30, 8, 19, 4], [30, 9, 19, 5], [31, 6, 20, 2], [31, 7, 20, 3], [31, 13, 20, 9], [32, 8, 21, 4, "changePayload"], [32, 21, 21, 17], [32, 24, 21, 20], [33, 10, 22, 6, "forceChange"], [33, 21, 22, 17], [33, 23, 22, 19, "current"], [33, 30, 22, 26], [33, 31, 22, 27, "force"], [33, 36, 22, 32], [33, 39, 22, 35, "previous"], [33, 47, 22, 43], [33, 48, 22, 44, "force"], [34, 8, 23, 4], [34, 9, 23, 5], [35, 6, 24, 2], [36, 6, 26, 2], [36, 13, 26, 9], [37, 8, 26, 11], [37, 11, 26, 14, "current"], [37, 18, 26, 21], [38, 8, 26, 23], [38, 11, 26, 26, "changePayload"], [39, 6, 26, 40], [39, 7, 26, 41], [40, 4, 27, 0], [40, 5, 27, 1], [41, 4, 27, 1, "changeEventCalculator"], [41, 25, 27, 1], [41, 26, 27, 1, "__closure"], [41, 35, 27, 1], [42, 4, 27, 1, "changeEventCalculator"], [42, 25, 27, 1], [42, 26, 27, 1, "__workletHash"], [42, 39, 27, 1], [43, 4, 27, 1, "changeEventCalculator"], [43, 25, 27, 1], [43, 26, 27, 1, "__initData"], [43, 36, 27, 1], [43, 39, 27, 1, "_worklet_15288835999946_init_data"], [43, 72, 27, 1], [44, 4, 27, 1, "changeEventCalculator"], [44, 25, 27, 1], [44, 26, 27, 1, "__stackDetails"], [44, 40, 27, 1], [44, 43, 27, 1, "_e"], [44, 45, 27, 1], [45, 4, 27, 1], [45, 11, 27, 1, "changeEventCalculator"], [45, 32, 27, 1], [46, 2, 27, 1], [46, 3, 10, 0], [47, 2, 10, 0], [47, 6, 29, 13, "ForceTouchGesture"], [47, 23, 29, 30], [47, 26, 29, 30, "exports"], [47, 33, 29, 30], [47, 34, 29, 30, "ForceTouchGesture"], [47, 51, 29, 30], [47, 77, 29, 30, "_ContinousBaseGesture"], [47, 98, 29, 30], [48, 4, 35, 2], [48, 13, 35, 2, "ForceTouchGesture"], [48, 31, 35, 2], [48, 33, 35, 16], [49, 6, 35, 16], [49, 10, 35, 16, "_this"], [49, 15, 35, 16], [50, 6, 35, 16], [50, 10, 35, 16, "_classCallCheck2"], [50, 26, 35, 16], [50, 27, 35, 16, "default"], [50, 34, 35, 16], [50, 42, 35, 16, "ForceTouchGesture"], [50, 59, 35, 16], [51, 6, 36, 4, "_this"], [51, 11, 36, 4], [51, 14, 36, 4, "_callSuper"], [51, 24, 36, 4], [51, 31, 36, 4, "ForceTouchGesture"], [51, 48, 36, 4], [52, 6, 36, 12, "_this"], [52, 11, 36, 12], [52, 12, 33, 9, "config"], [52, 18, 33, 15], [52, 21, 33, 63], [52, 22, 33, 64], [52, 23, 33, 65], [53, 6, 38, 4, "_this"], [53, 11, 38, 4], [53, 12, 38, 9, "handler<PERSON>ame"], [53, 23, 38, 20], [53, 26, 38, 23], [53, 52, 38, 49], [54, 6, 38, 50], [54, 13, 38, 50, "_this"], [54, 18, 38, 50], [55, 4, 39, 2], [57, 4, 41, 2], [58, 0, 42, 0], [59, 0, 43, 0], [60, 0, 44, 0], [61, 0, 45, 0], [62, 4, 41, 2], [62, 8, 41, 2, "_inherits2"], [62, 18, 41, 2], [62, 19, 41, 2, "default"], [62, 26, 41, 2], [62, 28, 41, 2, "ForceTouchGesture"], [62, 45, 41, 2], [62, 47, 41, 2, "_ContinousBaseGesture"], [62, 68, 41, 2], [63, 4, 41, 2], [63, 15, 41, 2, "_createClass2"], [63, 28, 41, 2], [63, 29, 41, 2, "default"], [63, 36, 41, 2], [63, 38, 41, 2, "ForceTouchGesture"], [63, 55, 41, 2], [64, 6, 41, 2, "key"], [64, 9, 41, 2], [65, 6, 41, 2, "value"], [65, 11, 41, 2], [65, 13, 46, 2], [65, 22, 46, 2, "minForce"], [65, 30, 46, 10, "minForce"], [65, 31, 46, 11, "force"], [65, 36, 46, 24], [65, 38, 46, 26], [66, 8, 47, 4], [66, 12, 47, 8], [66, 13, 47, 9, "config"], [66, 19, 47, 15], [66, 20, 47, 16, "minForce"], [66, 28, 47, 24], [66, 31, 47, 27, "force"], [66, 36, 47, 32], [67, 8, 48, 4], [67, 15, 48, 11], [67, 19, 48, 15], [68, 6, 49, 2], [70, 6, 51, 2], [71, 0, 52, 0], [72, 0, 53, 0], [73, 0, 54, 0], [74, 0, 55, 0], [75, 4, 51, 2], [76, 6, 51, 2, "key"], [76, 9, 51, 2], [77, 6, 51, 2, "value"], [77, 11, 51, 2], [77, 13, 56, 2], [77, 22, 56, 2, "max<PERSON><PERSON>ce"], [77, 30, 56, 10, "max<PERSON><PERSON>ce"], [77, 31, 56, 11, "force"], [77, 36, 56, 24], [77, 38, 56, 26], [78, 8, 57, 4], [78, 12, 57, 8], [78, 13, 57, 9, "config"], [78, 19, 57, 15], [78, 20, 57, 16, "max<PERSON><PERSON>ce"], [78, 28, 57, 24], [78, 31, 57, 27, "force"], [78, 36, 57, 32], [79, 8, 58, 4], [79, 15, 58, 11], [79, 19, 58, 15], [80, 6, 59, 2], [82, 6, 61, 2], [83, 0, 62, 0], [84, 0, 63, 0], [85, 0, 64, 0], [86, 4, 61, 2], [87, 6, 61, 2, "key"], [87, 9, 61, 2], [88, 6, 61, 2, "value"], [88, 11, 61, 2], [88, 13, 65, 2], [88, 22, 65, 2, "feedbackOnActivation"], [88, 42, 65, 22, "feedbackOnActivation"], [88, 43, 65, 23, "value"], [88, 48, 65, 37], [88, 50, 65, 39], [89, 8, 66, 4], [89, 12, 66, 8], [89, 13, 66, 9, "config"], [89, 19, 66, 15], [89, 20, 66, 16, "feedbackOnActivation"], [89, 40, 66, 36], [89, 43, 66, 39, "value"], [89, 48, 66, 44], [90, 8, 67, 4], [90, 15, 67, 11], [90, 19, 67, 15], [91, 6, 68, 2], [92, 4, 68, 3], [93, 6, 68, 3, "key"], [93, 9, 68, 3], [94, 6, 68, 3, "value"], [94, 11, 68, 3], [94, 13, 70, 2], [94, 22, 70, 2, "onChange"], [94, 30, 70, 10, "onChange"], [94, 31, 71, 4, "callback"], [94, 39, 78, 13], [94, 41, 79, 4], [95, 8, 80, 4], [96, 8, 81, 4], [96, 12, 81, 8], [96, 13, 81, 9, "handlers"], [96, 21, 81, 17], [96, 22, 81, 18, "changeEventCalculator"], [96, 43, 81, 39], [96, 46, 81, 42, "changeEventCalculator"], [96, 67, 81, 63], [97, 8, 82, 4], [97, 15, 82, 4, "_superPropGet"], [97, 28, 82, 4], [97, 29, 82, 4, "ForceTouchGesture"], [97, 46, 82, 4], [97, 70, 82, 26, "callback"], [97, 78, 82, 34], [98, 6, 83, 2], [99, 4, 83, 3], [100, 2, 83, 3], [100, 4, 29, 39, "ContinousBaseGesture"], [100, 33, 29, 59], [101, 0, 29, 59], [101, 3]], "functionMap": {"names": ["<global>", "changeEventCalculator", "ForceTouchGesture", "ForceTouchGesture#constructor", "ForceTouchGesture#minForce", "ForceTouchGesture#maxForce", "ForceTouchGesture#feedbackOnActivation", "ForceTouchGesture#onChange"], "mappings": "AAA;ACS;CDiB;OEE;ECM;GDI;EEO;GFG;EGO;GHG;EIM;GJG;EKE;GLa;CFC"}}, "type": "js/module"}]}