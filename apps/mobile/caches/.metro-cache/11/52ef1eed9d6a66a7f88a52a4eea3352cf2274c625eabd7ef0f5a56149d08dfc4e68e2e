{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 68}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../vendor/emitter/EventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 58}}], "key": "FSJ8dBSGMPKDnoV5nqp600Oqgzc=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "./NativeAppearance", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 27}, "end": {"line": 43, "column": 56}}], "key": "F0su56DNf9LFgn4XpDKr1ghSXpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.addChangeListener = addChangeListener;\n  exports.getColorScheme = getColorScheme;\n  exports.setColorScheme = setColorScheme;\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"../EventEmitter/NativeEventEmitter\"));\n  var _EventEmitter = _interopRequireDefault(require(_dependencyMap[2], \"../vendor/emitter/EventEmitter\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[3], \"invariant\"));\n  var lazyState;\n  function getState() {\n    if (lazyState != null) {\n      return lazyState;\n    }\n    var eventEmitter = new _EventEmitter.default();\n    var NativeAppearance = require(_dependencyMap[4], \"./NativeAppearance\").default;\n    if (NativeAppearance == null) {\n      lazyState = {\n        NativeAppearance: null,\n        appearance: null,\n        eventEmitter\n      };\n    } else {\n      var state = {\n        NativeAppearance,\n        appearance: null,\n        eventEmitter\n      };\n      new _NativeEventEmitter.default(NativeAppearance).addListener('appearanceChanged', newAppearance => {\n        state.appearance = {\n          colorScheme: toColorScheme(newAppearance.colorScheme)\n        };\n        eventEmitter.emit('change', state.appearance);\n      });\n      lazyState = state;\n    }\n    return lazyState;\n  }\n  function getColorScheme() {\n    var colorScheme = null;\n    var state = getState();\n    var NativeAppearance = state.NativeAppearance;\n    if (NativeAppearance != null) {\n      if (state.appearance == null) {\n        state.appearance = {\n          colorScheme: toColorScheme(NativeAppearance.getColorScheme())\n        };\n      }\n      colorScheme = state.appearance.colorScheme;\n    }\n    return colorScheme;\n  }\n  function setColorScheme(colorScheme) {\n    var state = getState();\n    var NativeAppearance = state.NativeAppearance;\n    if (NativeAppearance != null) {\n      NativeAppearance.setColorScheme(colorScheme ?? 'unspecified');\n      state.appearance = {\n        colorScheme: toColorScheme(NativeAppearance.getColorScheme())\n      };\n    }\n  }\n  function addChangeListener(listener) {\n    var _getState = getState(),\n      eventEmitter = _getState.eventEmitter;\n    return eventEmitter.addListener('change', listener);\n  }\n  function toColorScheme(colorScheme) {\n    (0, _invariant.default)(colorScheme === 'dark' || colorScheme === 'light' || colorScheme == null, \"Unrecognized color scheme. Did you mean 'dark', 'light' or null?\");\n    return colorScheme;\n  }\n});", "lineCount": 74, "map": [[9, 2, 15, 0], [9, 6, 15, 0, "_NativeEventEmitter"], [9, 25, 15, 0], [9, 28, 15, 0, "_interopRequireDefault"], [9, 50, 15, 0], [9, 51, 15, 0, "require"], [9, 58, 15, 0], [9, 59, 15, 0, "_dependencyMap"], [9, 73, 15, 0], [10, 2, 16, 0], [10, 6, 16, 0, "_EventEmitter"], [10, 19, 16, 0], [10, 22, 16, 0, "_interopRequireDefault"], [10, 44, 16, 0], [10, 45, 16, 0, "require"], [10, 52, 16, 0], [10, 53, 16, 0, "_dependencyMap"], [10, 67, 16, 0], [11, 2, 17, 0], [11, 6, 17, 0, "_invariant"], [11, 16, 17, 0], [11, 19, 17, 0, "_interopRequireDefault"], [11, 41, 17, 0], [11, 42, 17, 0, "require"], [11, 49, 17, 0], [11, 50, 17, 0, "_dependencyMap"], [11, 64, 17, 0], [12, 2, 23, 0], [12, 6, 23, 4, "lazyState"], [12, 15, 32, 1], [13, 2, 37, 0], [13, 11, 37, 9, "getState"], [13, 19, 37, 17, "getState"], [13, 20, 37, 17], [13, 22, 37, 53], [14, 4, 38, 2], [14, 8, 38, 6, "lazyState"], [14, 17, 38, 15], [14, 21, 38, 19], [14, 25, 38, 23], [14, 27, 38, 25], [15, 6, 39, 4], [15, 13, 39, 11, "lazyState"], [15, 22, 39, 20], [16, 4, 40, 2], [17, 4, 41, 2], [17, 8, 41, 8, "eventEmitter"], [17, 20, 41, 20], [17, 23, 41, 23], [17, 27, 41, 27, "EventEmitter"], [17, 48, 41, 39], [17, 49, 41, 64], [17, 50, 41, 65], [18, 4, 43, 2], [18, 8, 43, 8, "NativeAppearance"], [18, 24, 43, 24], [18, 27, 43, 27, "require"], [18, 34, 43, 34], [18, 35, 43, 34, "_dependencyMap"], [18, 49, 43, 34], [18, 74, 43, 55], [18, 75, 43, 56], [18, 76, 43, 57, "default"], [18, 83, 43, 64], [19, 4, 44, 2], [19, 8, 44, 6, "NativeAppearance"], [19, 24, 44, 22], [19, 28, 44, 26], [19, 32, 44, 30], [19, 34, 44, 32], [20, 6, 46, 4, "lazyState"], [20, 15, 46, 13], [20, 18, 46, 16], [21, 8, 47, 6, "NativeAppearance"], [21, 24, 47, 22], [21, 26, 47, 24], [21, 30, 47, 28], [22, 8, 48, 6, "appearance"], [22, 18, 48, 16], [22, 20, 48, 18], [22, 24, 48, 22], [23, 8, 49, 6, "eventEmitter"], [24, 6, 50, 4], [24, 7, 50, 5], [25, 4, 51, 2], [25, 5, 51, 3], [25, 11, 51, 9], [26, 6, 52, 4], [26, 10, 52, 10, "state"], [26, 15, 52, 48], [26, 18, 52, 51], [27, 8, 53, 6, "NativeAppearance"], [27, 24, 53, 22], [28, 8, 54, 6, "appearance"], [28, 18, 54, 16], [28, 20, 54, 18], [28, 24, 54, 22], [29, 8, 55, 6, "eventEmitter"], [30, 6, 56, 4], [30, 7, 56, 5], [31, 6, 57, 4], [31, 10, 57, 8, "NativeEventEmitter"], [31, 37, 57, 26], [31, 38, 59, 7, "NativeAppearance"], [31, 54, 59, 23], [31, 55, 59, 24], [31, 56, 59, 25, "addListener"], [31, 67, 59, 36], [31, 68, 59, 37], [31, 87, 59, 56], [31, 89, 59, 58, "newAppearance"], [31, 102, 59, 71], [31, 106, 59, 75], [32, 8, 60, 6, "state"], [32, 13, 60, 11], [32, 14, 60, 12, "appearance"], [32, 24, 60, 22], [32, 27, 60, 25], [33, 10, 61, 8, "colorScheme"], [33, 21, 61, 19], [33, 23, 61, 21, "toColorScheme"], [33, 36, 61, 34], [33, 37, 61, 35, "newAppearance"], [33, 50, 61, 48], [33, 51, 61, 49, "colorScheme"], [33, 62, 61, 60], [34, 8, 62, 6], [34, 9, 62, 7], [35, 8, 63, 6, "eventEmitter"], [35, 20, 63, 18], [35, 21, 63, 19, "emit"], [35, 25, 63, 23], [35, 26, 63, 24], [35, 34, 63, 32], [35, 36, 63, 34, "state"], [35, 41, 63, 39], [35, 42, 63, 40, "appearance"], [35, 52, 63, 50], [35, 53, 63, 51], [36, 6, 64, 4], [36, 7, 64, 5], [36, 8, 64, 6], [37, 6, 65, 4, "lazyState"], [37, 15, 65, 13], [37, 18, 65, 16, "state"], [37, 23, 65, 21], [38, 4, 66, 2], [39, 4, 67, 2], [39, 11, 67, 9, "lazyState"], [39, 20, 67, 18], [40, 2, 68, 0], [41, 2, 75, 7], [41, 11, 75, 16, "getColorScheme"], [41, 25, 75, 30, "getColorScheme"], [41, 26, 75, 30], [41, 28, 75, 51], [42, 4, 76, 2], [42, 8, 76, 6, "colorScheme"], [42, 19, 76, 17], [42, 22, 76, 20], [42, 26, 76, 24], [43, 4, 77, 2], [43, 8, 77, 8, "state"], [43, 13, 77, 13], [43, 16, 77, 16, "getState"], [43, 24, 77, 24], [43, 25, 77, 25], [43, 26, 77, 26], [44, 4, 78, 2], [44, 8, 78, 9, "NativeAppearance"], [44, 24, 78, 25], [44, 27, 78, 29, "state"], [44, 32, 78, 34], [44, 33, 78, 9, "NativeAppearance"], [44, 49, 78, 25], [45, 4, 79, 2], [45, 8, 79, 6, "NativeAppearance"], [45, 24, 79, 22], [45, 28, 79, 26], [45, 32, 79, 30], [45, 34, 79, 32], [46, 6, 80, 4], [46, 10, 80, 8, "state"], [46, 15, 80, 13], [46, 16, 80, 14, "appearance"], [46, 26, 80, 24], [46, 30, 80, 28], [46, 34, 80, 32], [46, 36, 80, 34], [47, 8, 83, 6, "state"], [47, 13, 83, 11], [47, 14, 83, 12, "appearance"], [47, 24, 83, 22], [47, 27, 83, 25], [48, 10, 84, 8, "colorScheme"], [48, 21, 84, 19], [48, 23, 84, 21, "toColorScheme"], [48, 36, 84, 34], [48, 37, 84, 35, "NativeAppearance"], [48, 53, 84, 51], [48, 54, 84, 52, "getColorScheme"], [48, 68, 84, 66], [48, 69, 84, 67], [48, 70, 84, 68], [49, 8, 85, 6], [49, 9, 85, 7], [50, 6, 86, 4], [51, 6, 87, 4, "colorScheme"], [51, 17, 87, 15], [51, 20, 87, 18, "state"], [51, 25, 87, 23], [51, 26, 87, 24, "appearance"], [51, 36, 87, 34], [51, 37, 87, 35, "colorScheme"], [51, 48, 87, 46], [52, 4, 88, 2], [53, 4, 89, 2], [53, 11, 89, 9, "colorScheme"], [53, 22, 89, 20], [54, 2, 90, 0], [55, 2, 95, 7], [55, 11, 95, 16, "setColorScheme"], [55, 25, 95, 30, "setColorScheme"], [55, 26, 95, 31, "colorScheme"], [55, 37, 95, 60], [55, 39, 95, 68], [56, 4, 96, 2], [56, 8, 96, 8, "state"], [56, 13, 96, 13], [56, 16, 96, 16, "getState"], [56, 24, 96, 24], [56, 25, 96, 25], [56, 26, 96, 26], [57, 4, 97, 2], [57, 8, 97, 9, "NativeAppearance"], [57, 24, 97, 25], [57, 27, 97, 29, "state"], [57, 32, 97, 34], [57, 33, 97, 9, "NativeAppearance"], [57, 49, 97, 25], [58, 4, 98, 2], [58, 8, 98, 6, "NativeAppearance"], [58, 24, 98, 22], [58, 28, 98, 26], [58, 32, 98, 30], [58, 34, 98, 32], [59, 6, 99, 4, "NativeAppearance"], [59, 22, 99, 20], [59, 23, 99, 21, "setColorScheme"], [59, 37, 99, 35], [59, 38, 99, 36, "colorScheme"], [59, 49, 99, 47], [59, 53, 99, 51], [59, 66, 99, 64], [59, 67, 99, 65], [60, 6, 100, 4, "state"], [60, 11, 100, 9], [60, 12, 100, 10, "appearance"], [60, 22, 100, 20], [60, 25, 100, 23], [61, 8, 101, 6, "colorScheme"], [61, 19, 101, 17], [61, 21, 101, 19, "toColorScheme"], [61, 34, 101, 32], [61, 35, 101, 33, "NativeAppearance"], [61, 51, 101, 49], [61, 52, 101, 50, "getColorScheme"], [61, 66, 101, 64], [61, 67, 101, 65], [61, 68, 101, 66], [62, 6, 102, 4], [62, 7, 102, 5], [63, 4, 103, 2], [64, 2, 104, 0], [65, 2, 109, 7], [65, 11, 109, 16, "addChangeListener"], [65, 28, 109, 33, "addChangeListener"], [65, 29, 110, 2, "listener"], [65, 37, 110, 53], [65, 39, 111, 21], [66, 4, 112, 2], [66, 8, 112, 2, "_getState"], [66, 17, 112, 2], [66, 20, 112, 25, "getState"], [66, 28, 112, 33], [66, 29, 112, 34], [66, 30, 112, 35], [67, 6, 112, 9, "eventEmitter"], [67, 18, 112, 21], [67, 21, 112, 21, "_getState"], [67, 30, 112, 21], [67, 31, 112, 9, "eventEmitter"], [67, 43, 112, 21], [68, 4, 113, 2], [68, 11, 113, 9, "eventEmitter"], [68, 23, 113, 21], [68, 24, 113, 22, "addListener"], [68, 35, 113, 33], [68, 36, 113, 34], [68, 44, 113, 42], [68, 46, 113, 44, "listener"], [68, 54, 113, 52], [68, 55, 113, 53], [69, 2, 114, 0], [70, 2, 119, 0], [70, 11, 119, 9, "toColorScheme"], [70, 24, 119, 22, "toColorScheme"], [70, 25, 119, 23, "colorScheme"], [70, 36, 119, 43], [70, 38, 119, 63], [71, 4, 120, 2], [71, 8, 120, 2, "invariant"], [71, 26, 120, 11], [71, 28, 121, 4, "colorScheme"], [71, 39, 121, 15], [71, 44, 121, 20], [71, 50, 121, 26], [71, 54, 121, 30, "colorScheme"], [71, 65, 121, 41], [71, 70, 121, 46], [71, 77, 121, 53], [71, 81, 121, 57, "colorScheme"], [71, 92, 121, 68], [71, 96, 121, 72], [71, 100, 121, 76], [71, 102, 122, 4], [71, 168, 123, 2], [71, 169, 123, 3], [72, 4, 124, 2], [72, 11, 124, 9, "colorScheme"], [72, 22, 124, 20], [73, 2, 125, 0], [74, 0, 125, 1], [74, 3]], "functionMap": {"names": ["<global>", "getState", "NativeEventEmitter.addListener$argument_1", "getColorScheme", "setColorScheme", "addChangeListener", "toColorScheme"], "mappings": "AAA;ACoC;0DCsB;KDK;CDI;OGO;CHe;OIK;CJS;OKK;CLK;AMK"}}, "type": "js/module"}]}