{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createIconSourceCache;\n  const TYPE_VALUE = 'value';\n  const TYPE_ERROR = 'error';\n  function createIconSourceCache() {\n    const cache = new Map();\n    const setValue = (key, value) => cache.set(key, {\n      type: TYPE_VALUE,\n      data: value\n    });\n    const setError = (key, error) => cache.set(key, {\n      type: TYPE_ERROR,\n      data: error\n    });\n    const has = key => cache.has(key);\n    const get = key => {\n      if (!cache.has(key)) {\n        return undefined;\n      }\n      const {\n        type,\n        data\n      } = cache.get(key);\n      if (type === TYPE_ERROR) {\n        throw data;\n      }\n      return data;\n    };\n    return {\n      setValue,\n      setError,\n      has,\n      get\n    };\n  }\n});", "lineCount": 39, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "TYPE_VALUE"], [6, 18, 1, 16], [6, 21, 1, 19], [6, 28, 1, 26], [7, 2, 2, 0], [7, 8, 2, 6, "TYPE_ERROR"], [7, 18, 2, 16], [7, 21, 2, 19], [7, 28, 2, 26], [8, 2, 4, 15], [8, 11, 4, 24, "createIconSourceCache"], [8, 32, 4, 45, "createIconSourceCache"], [8, 33, 4, 45], [8, 35, 4, 48], [9, 4, 5, 2], [9, 10, 5, 8, "cache"], [9, 15, 5, 13], [9, 18, 5, 16], [9, 22, 5, 20, "Map"], [9, 25, 5, 23], [9, 26, 5, 24], [9, 27, 5, 25], [10, 4, 7, 2], [10, 10, 7, 8, "setValue"], [10, 18, 7, 16], [10, 21, 7, 19, "setValue"], [10, 22, 7, 20, "key"], [10, 25, 7, 23], [10, 27, 7, 25, "value"], [10, 32, 7, 30], [10, 37, 8, 4, "cache"], [10, 42, 8, 9], [10, 43, 8, 10, "set"], [10, 46, 8, 13], [10, 47, 8, 14, "key"], [10, 50, 8, 17], [10, 52, 8, 19], [11, 6, 8, 21, "type"], [11, 10, 8, 25], [11, 12, 8, 27, "TYPE_VALUE"], [11, 22, 8, 37], [12, 6, 8, 39, "data"], [12, 10, 8, 43], [12, 12, 8, 45, "value"], [13, 4, 8, 51], [13, 5, 8, 52], [13, 6, 8, 53], [14, 4, 10, 2], [14, 10, 10, 8, "setError"], [14, 18, 10, 16], [14, 21, 10, 19, "setError"], [14, 22, 10, 20, "key"], [14, 25, 10, 23], [14, 27, 10, 25, "error"], [14, 32, 10, 30], [14, 37, 11, 4, "cache"], [14, 42, 11, 9], [14, 43, 11, 10, "set"], [14, 46, 11, 13], [14, 47, 11, 14, "key"], [14, 50, 11, 17], [14, 52, 11, 19], [15, 6, 11, 21, "type"], [15, 10, 11, 25], [15, 12, 11, 27, "TYPE_ERROR"], [15, 22, 11, 37], [16, 6, 11, 39, "data"], [16, 10, 11, 43], [16, 12, 11, 45, "error"], [17, 4, 11, 51], [17, 5, 11, 52], [17, 6, 11, 53], [18, 4, 13, 2], [18, 10, 13, 8, "has"], [18, 13, 13, 11], [18, 16, 13, 14, "key"], [18, 19, 13, 17], [18, 23, 13, 21, "cache"], [18, 28, 13, 26], [18, 29, 13, 27, "has"], [18, 32, 13, 30], [18, 33, 13, 31, "key"], [18, 36, 13, 34], [18, 37, 13, 35], [19, 4, 15, 2], [19, 10, 15, 8, "get"], [19, 13, 15, 11], [19, 16, 15, 14, "key"], [19, 19, 15, 17], [19, 23, 15, 21], [20, 6, 16, 4], [20, 10, 16, 8], [20, 11, 16, 9, "cache"], [20, 16, 16, 14], [20, 17, 16, 15, "has"], [20, 20, 16, 18], [20, 21, 16, 19, "key"], [20, 24, 16, 22], [20, 25, 16, 23], [20, 27, 16, 25], [21, 8, 17, 6], [21, 15, 17, 13, "undefined"], [21, 24, 17, 22], [22, 6, 18, 4], [23, 6, 19, 4], [23, 12, 19, 10], [24, 8, 19, 12, "type"], [24, 12, 19, 16], [25, 8, 19, 18, "data"], [26, 6, 19, 23], [26, 7, 19, 24], [26, 10, 19, 27, "cache"], [26, 15, 19, 32], [26, 16, 19, 33, "get"], [26, 19, 19, 36], [26, 20, 19, 37, "key"], [26, 23, 19, 40], [26, 24, 19, 41], [27, 6, 20, 4], [27, 10, 20, 8, "type"], [27, 14, 20, 12], [27, 19, 20, 17, "TYPE_ERROR"], [27, 29, 20, 27], [27, 31, 20, 29], [28, 8, 21, 6], [28, 14, 21, 12, "data"], [28, 18, 21, 16], [29, 6, 22, 4], [30, 6, 23, 4], [30, 13, 23, 11, "data"], [30, 17, 23, 15], [31, 4, 24, 2], [31, 5, 24, 3], [32, 4, 26, 2], [32, 11, 26, 9], [33, 6, 26, 11, "setValue"], [33, 14, 26, 19], [34, 6, 26, 21, "setError"], [34, 14, 26, 29], [35, 6, 26, 31, "has"], [35, 9, 26, 34], [36, 6, 26, 36, "get"], [37, 4, 26, 40], [37, 5, 26, 41], [38, 2, 27, 0], [39, 0, 27, 1], [39, 3]], "functionMap": {"names": ["<global>", "createIconSourceCache", "setValue", "setError", "has", "get"], "mappings": "AAA;eCG;mBCG;qDDC;mBEE;qDFC;cGE,qBH;cIE;GJS;CDG"}}, "type": "js/module"}]}