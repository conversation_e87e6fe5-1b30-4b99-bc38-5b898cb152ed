{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "color", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 26, "index": 94}}], "key": "WMoKxUKO/GMHeED0pzSR/dc1v7c=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 95}, "end": {"line": 5, "column": 31, "index": 126}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 190}, "end": {"line": 7, "column": 48, "index": 238}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Badge = Badge;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _color = _interopRequireDefault(require(_dependencyMap[2], \"color\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Animated\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/StyleSheet\"));\n  var _jsxRuntime = require(_dependencyMap[7], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const useNativeDriver = _Platform.default.OS !== 'web';\n  function Badge({\n    children,\n    style,\n    visible = true,\n    size = 18,\n    ...rest\n  }) {\n    const [opacity] = React.useState(() => new _Animated.default.Value(visible ? 1 : 0));\n    const [rendered, setRendered] = React.useState(visible);\n    const {\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    React.useEffect(() => {\n      if (!rendered) {\n        return;\n      }\n      _Animated.default.timing(opacity, {\n        toValue: visible ? 1 : 0,\n        duration: 150,\n        useNativeDriver\n      }).start(({\n        finished\n      }) => {\n        if (finished && !visible) {\n          setRendered(false);\n        }\n      });\n      return () => opacity.stopAnimation();\n    }, [opacity, rendered, visible]);\n    if (!rendered) {\n      if (visible) {\n        setRendered(true);\n      } else {\n        return null;\n      }\n    }\n\n    // @ts-expect-error: backgroundColor definitely exists\n    const {\n      backgroundColor = colors.notification,\n      ...restStyle\n    } = _StyleSheet.default.flatten(style) || {};\n    const textColor = (0, _color.default)(backgroundColor).isLight() ? 'black' : 'white';\n    const borderRadius = size / 2;\n    const fontSize = Math.floor(size * 3 / 4);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.Text, {\n      numberOfLines: 1,\n      style: [{\n        transform: [{\n          scale: opacity.interpolate({\n            inputRange: [0, 1],\n            outputRange: [0.5, 1]\n          })\n        }],\n        color: textColor,\n        lineHeight: size - 1,\n        height: size,\n        minWidth: size,\n        opacity,\n        backgroundColor,\n        fontSize,\n        borderRadius\n      }, fonts.regular, styles.container, restStyle],\n      ...rest,\n      children: children\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    container: {\n      alignSelf: 'flex-end',\n      textAlign: 'center',\n      paddingHorizontal: 4,\n      overflow: 'hidden'\n    }\n  });\n});", "lineCount": 94, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Badge"], [8, 15, 1, 13], [8, 18, 1, 13, "Badge"], [8, 23, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_color"], [10, 12, 4, 0], [10, 15, 4, 0, "_interopRequireDefault"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "React"], [11, 11, 5, 0], [11, 14, 5, 0, "_interopRequireWildcard"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 5, 31], [12, 6, 5, 31, "_Animated"], [12, 15, 5, 31], [12, 18, 5, 31, "_interopRequireDefault"], [12, 40, 5, 31], [12, 41, 5, 31, "require"], [12, 48, 5, 31], [12, 49, 5, 31, "_dependencyMap"], [12, 63, 5, 31], [13, 2, 5, 31], [13, 6, 5, 31, "_Platform"], [13, 15, 5, 31], [13, 18, 5, 31, "_interopRequireDefault"], [13, 40, 5, 31], [13, 41, 5, 31, "require"], [13, 48, 5, 31], [13, 49, 5, 31, "_dependencyMap"], [13, 63, 5, 31], [14, 2, 5, 31], [14, 6, 5, 31, "_StyleSheet"], [14, 17, 5, 31], [14, 20, 5, 31, "_interopRequireDefault"], [14, 42, 5, 31], [14, 43, 5, 31, "require"], [14, 50, 5, 31], [14, 51, 5, 31, "_dependencyMap"], [14, 65, 5, 31], [15, 2, 7, 0], [15, 6, 7, 0, "_jsxRuntime"], [15, 17, 7, 0], [15, 20, 7, 0, "require"], [15, 27, 7, 0], [15, 28, 7, 0, "_dependencyMap"], [15, 42, 7, 0], [16, 2, 7, 48], [16, 11, 7, 48, "_interopRequireWildcard"], [16, 35, 7, 48, "e"], [16, 36, 7, 48], [16, 38, 7, 48, "t"], [16, 39, 7, 48], [16, 68, 7, 48, "WeakMap"], [16, 75, 7, 48], [16, 81, 7, 48, "r"], [16, 82, 7, 48], [16, 89, 7, 48, "WeakMap"], [16, 96, 7, 48], [16, 100, 7, 48, "n"], [16, 101, 7, 48], [16, 108, 7, 48, "WeakMap"], [16, 115, 7, 48], [16, 127, 7, 48, "_interopRequireWildcard"], [16, 150, 7, 48], [16, 162, 7, 48, "_interopRequireWildcard"], [16, 163, 7, 48, "e"], [16, 164, 7, 48], [16, 166, 7, 48, "t"], [16, 167, 7, 48], [16, 176, 7, 48, "t"], [16, 177, 7, 48], [16, 181, 7, 48, "e"], [16, 182, 7, 48], [16, 186, 7, 48, "e"], [16, 187, 7, 48], [16, 188, 7, 48, "__esModule"], [16, 198, 7, 48], [16, 207, 7, 48, "e"], [16, 208, 7, 48], [16, 214, 7, 48, "o"], [16, 215, 7, 48], [16, 217, 7, 48, "i"], [16, 218, 7, 48], [16, 220, 7, 48, "f"], [16, 221, 7, 48], [16, 226, 7, 48, "__proto__"], [16, 235, 7, 48], [16, 243, 7, 48, "default"], [16, 250, 7, 48], [16, 252, 7, 48, "e"], [16, 253, 7, 48], [16, 270, 7, 48, "e"], [16, 271, 7, 48], [16, 294, 7, 48, "e"], [16, 295, 7, 48], [16, 320, 7, 48, "e"], [16, 321, 7, 48], [16, 330, 7, 48, "f"], [16, 331, 7, 48], [16, 337, 7, 48, "o"], [16, 338, 7, 48], [16, 341, 7, 48, "t"], [16, 342, 7, 48], [16, 345, 7, 48, "n"], [16, 346, 7, 48], [16, 349, 7, 48, "r"], [16, 350, 7, 48], [16, 358, 7, 48, "o"], [16, 359, 7, 48], [16, 360, 7, 48, "has"], [16, 363, 7, 48], [16, 364, 7, 48, "e"], [16, 365, 7, 48], [16, 375, 7, 48, "o"], [16, 376, 7, 48], [16, 377, 7, 48, "get"], [16, 380, 7, 48], [16, 381, 7, 48, "e"], [16, 382, 7, 48], [16, 385, 7, 48, "o"], [16, 386, 7, 48], [16, 387, 7, 48, "set"], [16, 390, 7, 48], [16, 391, 7, 48, "e"], [16, 392, 7, 48], [16, 394, 7, 48, "f"], [16, 395, 7, 48], [16, 411, 7, 48, "t"], [16, 412, 7, 48], [16, 416, 7, 48, "e"], [16, 417, 7, 48], [16, 433, 7, 48, "t"], [16, 434, 7, 48], [16, 441, 7, 48, "hasOwnProperty"], [16, 455, 7, 48], [16, 456, 7, 48, "call"], [16, 460, 7, 48], [16, 461, 7, 48, "e"], [16, 462, 7, 48], [16, 464, 7, 48, "t"], [16, 465, 7, 48], [16, 472, 7, 48, "i"], [16, 473, 7, 48], [16, 477, 7, 48, "o"], [16, 478, 7, 48], [16, 481, 7, 48, "Object"], [16, 487, 7, 48], [16, 488, 7, 48, "defineProperty"], [16, 502, 7, 48], [16, 507, 7, 48, "Object"], [16, 513, 7, 48], [16, 514, 7, 48, "getOwnPropertyDescriptor"], [16, 538, 7, 48], [16, 539, 7, 48, "e"], [16, 540, 7, 48], [16, 542, 7, 48, "t"], [16, 543, 7, 48], [16, 550, 7, 48, "i"], [16, 551, 7, 48], [16, 552, 7, 48, "get"], [16, 555, 7, 48], [16, 559, 7, 48, "i"], [16, 560, 7, 48], [16, 561, 7, 48, "set"], [16, 564, 7, 48], [16, 568, 7, 48, "o"], [16, 569, 7, 48], [16, 570, 7, 48, "f"], [16, 571, 7, 48], [16, 573, 7, 48, "t"], [16, 574, 7, 48], [16, 576, 7, 48, "i"], [16, 577, 7, 48], [16, 581, 7, 48, "f"], [16, 582, 7, 48], [16, 583, 7, 48, "t"], [16, 584, 7, 48], [16, 588, 7, 48, "e"], [16, 589, 7, 48], [16, 590, 7, 48, "t"], [16, 591, 7, 48], [16, 602, 7, 48, "f"], [16, 603, 7, 48], [16, 608, 7, 48, "e"], [16, 609, 7, 48], [16, 611, 7, 48, "t"], [16, 612, 7, 48], [17, 2, 8, 0], [17, 8, 8, 6, "useNativeDriver"], [17, 23, 8, 21], [17, 26, 8, 24, "Platform"], [17, 43, 8, 32], [17, 44, 8, 33, "OS"], [17, 46, 8, 35], [17, 51, 8, 40], [17, 56, 8, 45], [18, 2, 9, 7], [18, 11, 9, 16, "Badge"], [18, 16, 9, 21, "Badge"], [18, 17, 9, 22], [19, 4, 10, 2, "children"], [19, 12, 10, 10], [20, 4, 11, 2, "style"], [20, 9, 11, 7], [21, 4, 12, 2, "visible"], [21, 11, 12, 9], [21, 14, 12, 12], [21, 18, 12, 16], [22, 4, 13, 2, "size"], [22, 8, 13, 6], [22, 11, 13, 9], [22, 13, 13, 11], [23, 4, 14, 2], [23, 7, 14, 5, "rest"], [24, 2, 15, 0], [24, 3, 15, 1], [24, 5, 15, 3], [25, 4, 16, 2], [25, 10, 16, 8], [25, 11, 16, 9, "opacity"], [25, 18, 16, 16], [25, 19, 16, 17], [25, 22, 16, 20, "React"], [25, 27, 16, 25], [25, 28, 16, 26, "useState"], [25, 36, 16, 34], [25, 37, 16, 35], [25, 43, 16, 41], [25, 47, 16, 45, "Animated"], [25, 64, 16, 53], [25, 65, 16, 54, "Value"], [25, 70, 16, 59], [25, 71, 16, 60, "visible"], [25, 78, 16, 67], [25, 81, 16, 70], [25, 82, 16, 71], [25, 85, 16, 74], [25, 86, 16, 75], [25, 87, 16, 76], [25, 88, 16, 77], [26, 4, 17, 2], [26, 10, 17, 8], [26, 11, 17, 9, "rendered"], [26, 19, 17, 17], [26, 21, 17, 19, "setRendered"], [26, 32, 17, 30], [26, 33, 17, 31], [26, 36, 17, 34, "React"], [26, 41, 17, 39], [26, 42, 17, 40, "useState"], [26, 50, 17, 48], [26, 51, 17, 49, "visible"], [26, 58, 17, 56], [26, 59, 17, 57], [27, 4, 18, 2], [27, 10, 18, 8], [28, 6, 19, 4, "colors"], [28, 12, 19, 10], [29, 6, 20, 4, "fonts"], [30, 4, 21, 2], [30, 5, 21, 3], [30, 8, 21, 6], [30, 12, 21, 6, "useTheme"], [30, 28, 21, 14], [30, 30, 21, 15], [30, 31, 21, 16], [31, 4, 22, 2, "React"], [31, 9, 22, 7], [31, 10, 22, 8, "useEffect"], [31, 19, 22, 17], [31, 20, 22, 18], [31, 26, 22, 24], [32, 6, 23, 4], [32, 10, 23, 8], [32, 11, 23, 9, "rendered"], [32, 19, 23, 17], [32, 21, 23, 19], [33, 8, 24, 6], [34, 6, 25, 4], [35, 6, 26, 4, "Animated"], [35, 23, 26, 12], [35, 24, 26, 13, "timing"], [35, 30, 26, 19], [35, 31, 26, 20, "opacity"], [35, 38, 26, 27], [35, 40, 26, 29], [36, 8, 27, 6, "toValue"], [36, 15, 27, 13], [36, 17, 27, 15, "visible"], [36, 24, 27, 22], [36, 27, 27, 25], [36, 28, 27, 26], [36, 31, 27, 29], [36, 32, 27, 30], [37, 8, 28, 6, "duration"], [37, 16, 28, 14], [37, 18, 28, 16], [37, 21, 28, 19], [38, 8, 29, 6, "useNativeDriver"], [39, 6, 30, 4], [39, 7, 30, 5], [39, 8, 30, 6], [39, 9, 30, 7, "start"], [39, 14, 30, 12], [39, 15, 30, 13], [39, 16, 30, 14], [40, 8, 31, 6, "finished"], [41, 6, 32, 4], [41, 7, 32, 5], [41, 12, 32, 10], [42, 8, 33, 6], [42, 12, 33, 10, "finished"], [42, 20, 33, 18], [42, 24, 33, 22], [42, 25, 33, 23, "visible"], [42, 32, 33, 30], [42, 34, 33, 32], [43, 10, 34, 8, "setRendered"], [43, 21, 34, 19], [43, 22, 34, 20], [43, 27, 34, 25], [43, 28, 34, 26], [44, 8, 35, 6], [45, 6, 36, 4], [45, 7, 36, 5], [45, 8, 36, 6], [46, 6, 37, 4], [46, 13, 37, 11], [46, 19, 37, 17, "opacity"], [46, 26, 37, 24], [46, 27, 37, 25, "stopAnimation"], [46, 40, 37, 38], [46, 41, 37, 39], [46, 42, 37, 40], [47, 4, 38, 2], [47, 5, 38, 3], [47, 7, 38, 5], [47, 8, 38, 6, "opacity"], [47, 15, 38, 13], [47, 17, 38, 15, "rendered"], [47, 25, 38, 23], [47, 27, 38, 25, "visible"], [47, 34, 38, 32], [47, 35, 38, 33], [47, 36, 38, 34], [48, 4, 39, 2], [48, 8, 39, 6], [48, 9, 39, 7, "rendered"], [48, 17, 39, 15], [48, 19, 39, 17], [49, 6, 40, 4], [49, 10, 40, 8, "visible"], [49, 17, 40, 15], [49, 19, 40, 17], [50, 8, 41, 6, "setRendered"], [50, 19, 41, 17], [50, 20, 41, 18], [50, 24, 41, 22], [50, 25, 41, 23], [51, 6, 42, 4], [51, 7, 42, 5], [51, 13, 42, 11], [52, 8, 43, 6], [52, 15, 43, 13], [52, 19, 43, 17], [53, 6, 44, 4], [54, 4, 45, 2], [56, 4, 47, 2], [57, 4, 48, 2], [57, 10, 48, 8], [58, 6, 49, 4, "backgroundColor"], [58, 21, 49, 19], [58, 24, 49, 22, "colors"], [58, 30, 49, 28], [58, 31, 49, 29, "notification"], [58, 43, 49, 41], [59, 6, 50, 4], [59, 9, 50, 7, "restStyle"], [60, 4, 51, 2], [60, 5, 51, 3], [60, 8, 51, 6, "StyleSheet"], [60, 27, 51, 16], [60, 28, 51, 17, "flatten"], [60, 35, 51, 24], [60, 36, 51, 25, "style"], [60, 41, 51, 30], [60, 42, 51, 31], [60, 46, 51, 35], [60, 47, 51, 36], [60, 48, 51, 37], [61, 4, 52, 2], [61, 10, 52, 8, "textColor"], [61, 19, 52, 17], [61, 22, 52, 20], [61, 26, 52, 20, "color"], [61, 40, 52, 25], [61, 42, 52, 26, "backgroundColor"], [61, 57, 52, 41], [61, 58, 52, 42], [61, 59, 52, 43, "isLight"], [61, 66, 52, 50], [61, 67, 52, 51], [61, 68, 52, 52], [61, 71, 52, 55], [61, 78, 52, 62], [61, 81, 52, 65], [61, 88, 52, 72], [62, 4, 53, 2], [62, 10, 53, 8, "borderRadius"], [62, 22, 53, 20], [62, 25, 53, 23, "size"], [62, 29, 53, 27], [62, 32, 53, 30], [62, 33, 53, 31], [63, 4, 54, 2], [63, 10, 54, 8, "fontSize"], [63, 18, 54, 16], [63, 21, 54, 19, "Math"], [63, 25, 54, 23], [63, 26, 54, 24, "floor"], [63, 31, 54, 29], [63, 32, 54, 30, "size"], [63, 36, 54, 34], [63, 39, 54, 37], [63, 40, 54, 38], [63, 43, 54, 41], [63, 44, 54, 42], [63, 45, 54, 43], [64, 4, 55, 2], [64, 11, 55, 9], [64, 24, 55, 22], [64, 28, 55, 22, "_jsx"], [64, 43, 55, 26], [64, 45, 55, 27, "Animated"], [64, 62, 55, 35], [64, 63, 55, 36, "Text"], [64, 67, 55, 40], [64, 69, 55, 42], [65, 6, 56, 4, "numberOfLines"], [65, 19, 56, 17], [65, 21, 56, 19], [65, 22, 56, 20], [66, 6, 57, 4, "style"], [66, 11, 57, 9], [66, 13, 57, 11], [66, 14, 57, 12], [67, 8, 58, 6, "transform"], [67, 17, 58, 15], [67, 19, 58, 17], [67, 20, 58, 18], [68, 10, 59, 8, "scale"], [68, 15, 59, 13], [68, 17, 59, 15, "opacity"], [68, 24, 59, 22], [68, 25, 59, 23, "interpolate"], [68, 36, 59, 34], [68, 37, 59, 35], [69, 12, 60, 10, "inputRange"], [69, 22, 60, 20], [69, 24, 60, 22], [69, 25, 60, 23], [69, 26, 60, 24], [69, 28, 60, 26], [69, 29, 60, 27], [69, 30, 60, 28], [70, 12, 61, 10, "outputRange"], [70, 23, 61, 21], [70, 25, 61, 23], [70, 26, 61, 24], [70, 29, 61, 27], [70, 31, 61, 29], [70, 32, 61, 30], [71, 10, 62, 8], [71, 11, 62, 9], [72, 8, 63, 6], [72, 9, 63, 7], [72, 10, 63, 8], [73, 8, 64, 6, "color"], [73, 13, 64, 11], [73, 15, 64, 13, "textColor"], [73, 24, 64, 22], [74, 8, 65, 6, "lineHeight"], [74, 18, 65, 16], [74, 20, 65, 18, "size"], [74, 24, 65, 22], [74, 27, 65, 25], [74, 28, 65, 26], [75, 8, 66, 6, "height"], [75, 14, 66, 12], [75, 16, 66, 14, "size"], [75, 20, 66, 18], [76, 8, 67, 6, "min<PERSON><PERSON><PERSON>"], [76, 16, 67, 14], [76, 18, 67, 16, "size"], [76, 22, 67, 20], [77, 8, 68, 6, "opacity"], [77, 15, 68, 13], [78, 8, 69, 6, "backgroundColor"], [78, 23, 69, 21], [79, 8, 70, 6, "fontSize"], [79, 16, 70, 14], [80, 8, 71, 6, "borderRadius"], [81, 6, 72, 4], [81, 7, 72, 5], [81, 9, 72, 7, "fonts"], [81, 14, 72, 12], [81, 15, 72, 13, "regular"], [81, 22, 72, 20], [81, 24, 72, 22, "styles"], [81, 30, 72, 28], [81, 31, 72, 29, "container"], [81, 40, 72, 38], [81, 42, 72, 40, "restStyle"], [81, 51, 72, 49], [81, 52, 72, 50], [82, 6, 73, 4], [82, 9, 73, 7, "rest"], [82, 13, 73, 11], [83, 6, 74, 4, "children"], [83, 14, 74, 12], [83, 16, 74, 14, "children"], [84, 4, 75, 2], [84, 5, 75, 3], [84, 6, 75, 4], [85, 2, 76, 0], [86, 2, 77, 0], [86, 8, 77, 6, "styles"], [86, 14, 77, 12], [86, 17, 77, 15, "StyleSheet"], [86, 36, 77, 25], [86, 37, 77, 26, "create"], [86, 43, 77, 32], [86, 44, 77, 33], [87, 4, 78, 2, "container"], [87, 13, 78, 11], [87, 15, 78, 13], [88, 6, 79, 4, "alignSelf"], [88, 15, 79, 13], [88, 17, 79, 15], [88, 27, 79, 25], [89, 6, 80, 4, "textAlign"], [89, 15, 80, 13], [89, 17, 80, 15], [89, 25, 80, 23], [90, 6, 81, 4, "paddingHorizontal"], [90, 23, 81, 21], [90, 25, 81, 23], [90, 26, 81, 24], [91, 6, 82, 4, "overflow"], [91, 14, 82, 12], [91, 16, 82, 14], [92, 4, 83, 2], [93, 2, 84, 0], [93, 3, 84, 1], [93, 4, 84, 2], [94, 0, 84, 3], [94, 3]], "functionMap": {"names": ["<global>", "Badge", "React.useState$argument_0", "React.useEffect$argument_0", "Animated.timing.start$argument_0", "<anonymous>"], "mappings": "AAA;OCQ;mCCO,yCD;kBEM;aCQ;KDM;WEC,6BF;GFC;CDsC"}}, "type": "js/module"}]}