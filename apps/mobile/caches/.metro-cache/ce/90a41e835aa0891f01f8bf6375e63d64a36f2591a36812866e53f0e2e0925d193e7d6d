{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createGestureHandler = createGestureHandler;\n  exports.dropGestureHandler = dropGestureHandler;\n  exports.gestures = void 0;\n  exports.getHandler = getHandler;\n  exports.getNodes = getNodes;\n  const gestures = exports.gestures = {};\n  function getHandler(tag) {\n    if (tag in gestures) {\n      return gestures[tag];\n    }\n    throw new Error(`No handler for tag ${tag}`);\n  }\n  function createGestureHandler(handlerTag, handler) {\n    if (handlerTag in gestures) {\n      throw new Error(`Handler with tag ${handlerTag} already exists`);\n    }\n    gestures[handlerTag] = handler; // @ts-ignore no types for web handlers yet\n\n    gestures[handlerTag].handlerTag = handlerTag;\n  }\n  function dropGestureHandler(handlerTag) {\n    // Since React 18, there are cases where componentWillUnmount gets called twice in a row\n    // so skip this if the tag was already removed.\n    if (!(handlerTag in gestures)) {\n      return;\n    }\n    getHandler(handlerTag).destroy(); // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n\n    delete gestures[handlerTag];\n  }\n  function getNodes() {\n    return {\n      ...gestures\n    };\n  }\n});", "lineCount": 40, "map": [[10, 2, 1, 7], [10, 8, 1, 13, "gestures"], [10, 16, 1, 21], [10, 19, 1, 21, "exports"], [10, 26, 1, 21], [10, 27, 1, 21, "gestures"], [10, 35, 1, 21], [10, 38, 1, 24], [10, 39, 1, 25], [10, 40, 1, 26], [11, 2, 2, 7], [11, 11, 2, 16, "<PERSON><PERSON><PERSON><PERSON>"], [11, 21, 2, 26, "<PERSON><PERSON><PERSON><PERSON>"], [11, 22, 2, 27, "tag"], [11, 25, 2, 30], [11, 27, 2, 32], [12, 4, 3, 2], [12, 8, 3, 6, "tag"], [12, 11, 3, 9], [12, 15, 3, 13, "gestures"], [12, 23, 3, 21], [12, 25, 3, 23], [13, 6, 4, 4], [13, 13, 4, 11, "gestures"], [13, 21, 4, 19], [13, 22, 4, 20, "tag"], [13, 25, 4, 23], [13, 26, 4, 24], [14, 4, 5, 2], [15, 4, 7, 2], [15, 10, 7, 8], [15, 14, 7, 12, "Error"], [15, 19, 7, 17], [15, 20, 7, 18], [15, 42, 7, 40, "tag"], [15, 45, 7, 43], [15, 47, 7, 45], [15, 48, 7, 46], [16, 2, 8, 0], [17, 2, 9, 7], [17, 11, 9, 16, "createGestureHandler"], [17, 31, 9, 36, "createGestureHandler"], [17, 32, 9, 37, "handlerTag"], [17, 42, 9, 47], [17, 44, 9, 49, "handler"], [17, 51, 9, 56], [17, 53, 9, 58], [18, 4, 10, 2], [18, 8, 10, 6, "handlerTag"], [18, 18, 10, 16], [18, 22, 10, 20, "gestures"], [18, 30, 10, 28], [18, 32, 10, 30], [19, 6, 11, 4], [19, 12, 11, 10], [19, 16, 11, 14, "Error"], [19, 21, 11, 19], [19, 22, 11, 20], [19, 42, 11, 40, "handlerTag"], [19, 52, 11, 50], [19, 69, 11, 67], [19, 70, 11, 68], [20, 4, 12, 2], [21, 4, 14, 2, "gestures"], [21, 12, 14, 10], [21, 13, 14, 11, "handlerTag"], [21, 23, 14, 21], [21, 24, 14, 22], [21, 27, 14, 25, "handler"], [21, 34, 14, 32], [21, 35, 14, 33], [21, 36, 14, 34], [23, 4, 16, 2, "gestures"], [23, 12, 16, 10], [23, 13, 16, 11, "handlerTag"], [23, 23, 16, 21], [23, 24, 16, 22], [23, 25, 16, 23, "handlerTag"], [23, 35, 16, 33], [23, 38, 16, 36, "handlerTag"], [23, 48, 16, 46], [24, 2, 17, 0], [25, 2, 18, 7], [25, 11, 18, 16, "dropGestureHandler"], [25, 29, 18, 34, "dropGestureHandler"], [25, 30, 18, 35, "handlerTag"], [25, 40, 18, 45], [25, 42, 18, 47], [26, 4, 19, 2], [27, 4, 20, 2], [28, 4, 21, 2], [28, 8, 21, 6], [28, 10, 21, 8, "handlerTag"], [28, 20, 21, 18], [28, 24, 21, 22, "gestures"], [28, 32, 21, 30], [28, 33, 21, 31], [28, 35, 21, 33], [29, 6, 22, 4], [30, 4, 23, 2], [31, 4, 25, 2, "<PERSON><PERSON><PERSON><PERSON>"], [31, 14, 25, 12], [31, 15, 25, 13, "handlerTag"], [31, 25, 25, 23], [31, 26, 25, 24], [31, 27, 25, 25, "destroy"], [31, 34, 25, 32], [31, 35, 25, 33], [31, 36, 25, 34], [31, 37, 25, 35], [31, 38, 25, 36], [33, 4, 27, 2], [33, 11, 27, 9, "gestures"], [33, 19, 27, 17], [33, 20, 27, 18, "handlerTag"], [33, 30, 27, 28], [33, 31, 27, 29], [34, 2, 28, 0], [35, 2, 29, 7], [35, 11, 29, 16, "getNodes"], [35, 19, 29, 24, "getNodes"], [35, 20, 29, 24], [35, 22, 29, 27], [36, 4, 30, 2], [36, 11, 30, 9], [37, 6, 30, 11], [37, 9, 30, 14, "gestures"], [38, 4, 31, 2], [38, 5, 31, 3], [39, 2, 32, 0], [40, 0, 32, 1], [40, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>", "createGestureHandler", "dropGestureHandler", "getNodes"], "mappings": "AAA;OCC;CDM;OEC;CFQ;OGC;CHU;OIC;CJG"}}, "type": "js/module"}]}