{"dependencies": [{"name": "./fetchThenEvalJs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 55, "index": 55}}], "key": "I5OZV5Dy4CP9Z+F+VzJneycQlSc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"fetchThenEvalAsync\", {\n    enumerable: true,\n    get: function () {\n      return _fetchThenEvalJs.fetchThenEvalAsync;\n    }\n  });\n  var _fetchThenEvalJs = require(_dependencyMap[0], \"./fetchThenEvalJs\");\n});", "lineCount": 12, "map": [[11, 2, 1, 0], [11, 6, 1, 0, "_fetchThenEvalJs"], [11, 22, 1, 0], [11, 25, 1, 0, "require"], [11, 32, 1, 0], [11, 33, 1, 0, "_dependencyMap"], [11, 47, 1, 0], [12, 0, 1, 55], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}