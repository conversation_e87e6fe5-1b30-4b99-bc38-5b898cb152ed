{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "8DdbuUtV90Z/t9ffltCQA3iNnjQ=", "exportNames": ["*"]}}, {"name": "./ExpoFontUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 102}, "end": {"line": 3, "column": 44, "index": 146}}], "key": "mUGTyZe+sujUBJpON3cfchmCB1w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.renderToImageAsync = renderToImageAsync;\n  var _expoModulesCore = require(_dependencyMap[1], \"expo-modules-core\");\n  var _processColor = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/processColor\"));\n  var _ExpoFontUtils = _interopRequireDefault(require(_dependencyMap[3], \"./ExpoFontUtils\"));\n  /**\n   * Creates an image with provided text.\n   * @param glyphs Text to be exported.\n   * @param options RenderToImageOptions.\n   * @return Promise which fulfils with uri to image.\n   * @platform android\n   * @platform ios\n   */\n  async function renderToImageAsync(glyphs, options) {\n    if (!_ExpoFontUtils.default) {\n      throw new _expoModulesCore.UnavailabilityError('expo-font', 'ExpoFontUtils.renderToImageAsync');\n    }\n    return await _ExpoFontUtils.default.renderToImageAsync(glyphs, {\n      ...options,\n      color: options?.color ? (0, _processColor.default)(options.color) : undefined\n    });\n  }\n});", "lineCount": 27, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_expoModulesCore"], [7, 22, 1, 0], [7, 25, 1, 0, "require"], [7, 32, 1, 0], [7, 33, 1, 0, "_dependencyMap"], [7, 47, 1, 0], [8, 2, 1, 56], [8, 6, 1, 56, "_processColor"], [8, 19, 1, 56], [8, 22, 1, 56, "_interopRequireDefault"], [8, 44, 1, 56], [8, 45, 1, 56, "require"], [8, 52, 1, 56], [8, 53, 1, 56, "_dependencyMap"], [8, 67, 1, 56], [9, 2, 3, 0], [9, 6, 3, 0, "_ExpoFontUtils"], [9, 20, 3, 0], [9, 23, 3, 0, "_interopRequireDefault"], [9, 45, 3, 0], [9, 46, 3, 0, "require"], [9, 53, 3, 0], [9, 54, 3, 0, "_dependencyMap"], [9, 68, 3, 0], [10, 2, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 2, 12, 7], [18, 17, 12, 22, "renderToImageAsync"], [18, 35, 12, 40, "renderToImageAsync"], [18, 36, 12, 41, "glyphs"], [18, 42, 12, 47], [18, 44, 12, 49, "options"], [18, 51, 12, 56], [18, 53, 12, 58], [19, 4, 13, 4], [19, 8, 13, 8], [19, 9, 13, 9, "ExpoFontUtils"], [19, 31, 13, 22], [19, 33, 13, 24], [20, 6, 14, 8], [20, 12, 14, 14], [20, 16, 14, 18, "UnavailabilityError"], [20, 52, 14, 37], [20, 53, 14, 38], [20, 64, 14, 49], [20, 66, 14, 51], [20, 100, 14, 85], [20, 101, 14, 86], [21, 4, 15, 4], [22, 4, 16, 4], [22, 11, 16, 11], [22, 17, 16, 17, "ExpoFontUtils"], [22, 39, 16, 30], [22, 40, 16, 31, "renderToImageAsync"], [22, 58, 16, 49], [22, 59, 16, 50, "glyphs"], [22, 65, 16, 56], [22, 67, 16, 58], [23, 6, 17, 8], [23, 9, 17, 11, "options"], [23, 16, 17, 18], [24, 6, 18, 8, "color"], [24, 11, 18, 13], [24, 13, 18, 15, "options"], [24, 20, 18, 22], [24, 22, 18, 24, "color"], [24, 27, 18, 29], [24, 30, 18, 32], [24, 34, 18, 32, "processColor"], [24, 55, 18, 44], [24, 57, 18, 45, "options"], [24, 64, 18, 52], [24, 65, 18, 53, "color"], [24, 70, 18, 58], [24, 71, 18, 59], [24, 74, 18, 62, "undefined"], [25, 4, 19, 4], [25, 5, 19, 5], [25, 6, 19, 6], [26, 2, 20, 0], [27, 0, 20, 1], [27, 3]], "functionMap": {"names": ["<global>", "renderToImageAsync"], "mappings": "AAA;OCW;CDQ"}}, "type": "js/module"}]}