{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 50, "index": 231}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-svg", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 232}, "end": {"line": 9, "column": 46, "index": 278}}], "key": "lCMYlEpYXUxeSuxY/qJGK1buGwU=", "exportNames": ["*"]}}, {"name": "./defaultAttributes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 279}, "end": {"line": 10, "column": 83, "index": 362}}], "key": "jhIicveOKJFsp32zVLJ2qzocBIw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = require(_dependencyMap[0], \"react\");\n  var NativeSvg = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-svg\"));\n  var _defaultAttributes = _interopRequireWildcard(require(_dependencyMap[2], \"./defaultAttributes.js\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  const createLucideIcon = (iconName, iconNode) => {\n    const Component = /*#__PURE__*/(0, _react.forwardRef)(({\n      color = \"currentColor\",\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      children,\n      \"data-testid\": dataTestId,\n      ...rest\n    }, ref) => {\n      const customAttrs = {\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        ...rest\n      };\n      return /*#__PURE__*/(0, _react.createElement)(NativeSvg.Svg, {\n        ref,\n        ..._defaultAttributes.default,\n        width: size,\n        height: size,\n        \"data-testid\": dataTestId,\n        ...customAttrs\n      }, [...iconNode.map(([tag, attrs]) => {\n        const upperCasedTag = tag.charAt(0).toUpperCase() + tag.slice(1);\n        return /*#__PURE__*/(0, _react.createElement)(NativeSvg[upperCasedTag], {\n          ..._defaultAttributes.childDefaultAttributes,\n          ...customAttrs,\n          ...attrs\n        });\n      }), ...((Array.isArray(children) ? children : [children]) || [])]);\n    });\n    Component.displayName = `${iconName}`;\n    return Component;\n  };\n  exports.default = createLucideIcon;\n});", "lineCount": 52, "map": [[6, 2, 8, 0], [6, 6, 8, 0, "_react"], [6, 12, 8, 0], [6, 15, 8, 0, "require"], [6, 22, 8, 0], [6, 23, 8, 0, "_dependencyMap"], [6, 37, 8, 0], [7, 2, 9, 0], [7, 6, 9, 0, "NativeSvg"], [7, 15, 9, 0], [7, 18, 9, 0, "_interopRequireWildcard"], [7, 41, 9, 0], [7, 42, 9, 0, "require"], [7, 49, 9, 0], [7, 50, 9, 0, "_dependencyMap"], [7, 64, 9, 0], [8, 2, 10, 0], [8, 6, 10, 0, "_defaultAttributes"], [8, 24, 10, 0], [8, 27, 10, 0, "_interopRequireWildcard"], [8, 50, 10, 0], [8, 51, 10, 0, "require"], [8, 58, 10, 0], [8, 59, 10, 0, "_dependencyMap"], [8, 73, 10, 0], [9, 2, 10, 83], [9, 11, 10, 83, "_interopRequireWildcard"], [9, 35, 10, 83, "e"], [9, 36, 10, 83], [9, 38, 10, 83, "t"], [9, 39, 10, 83], [9, 68, 10, 83, "WeakMap"], [9, 75, 10, 83], [9, 81, 10, 83, "r"], [9, 82, 10, 83], [9, 89, 10, 83, "WeakMap"], [9, 96, 10, 83], [9, 100, 10, 83, "n"], [9, 101, 10, 83], [9, 108, 10, 83, "WeakMap"], [9, 115, 10, 83], [9, 127, 10, 83, "_interopRequireWildcard"], [9, 150, 10, 83], [9, 162, 10, 83, "_interopRequireWildcard"], [9, 163, 10, 83, "e"], [9, 164, 10, 83], [9, 166, 10, 83, "t"], [9, 167, 10, 83], [9, 176, 10, 83, "t"], [9, 177, 10, 83], [9, 181, 10, 83, "e"], [9, 182, 10, 83], [9, 186, 10, 83, "e"], [9, 187, 10, 83], [9, 188, 10, 83, "__esModule"], [9, 198, 10, 83], [9, 207, 10, 83, "e"], [9, 208, 10, 83], [9, 214, 10, 83, "o"], [9, 215, 10, 83], [9, 217, 10, 83, "i"], [9, 218, 10, 83], [9, 220, 10, 83, "f"], [9, 221, 10, 83], [9, 226, 10, 83, "__proto__"], [9, 235, 10, 83], [9, 243, 10, 83, "default"], [9, 250, 10, 83], [9, 252, 10, 83, "e"], [9, 253, 10, 83], [9, 270, 10, 83, "e"], [9, 271, 10, 83], [9, 294, 10, 83, "e"], [9, 295, 10, 83], [9, 320, 10, 83, "e"], [9, 321, 10, 83], [9, 330, 10, 83, "f"], [9, 331, 10, 83], [9, 337, 10, 83, "o"], [9, 338, 10, 83], [9, 341, 10, 83, "t"], [9, 342, 10, 83], [9, 345, 10, 83, "n"], [9, 346, 10, 83], [9, 349, 10, 83, "r"], [9, 350, 10, 83], [9, 358, 10, 83, "o"], [9, 359, 10, 83], [9, 360, 10, 83, "has"], [9, 363, 10, 83], [9, 364, 10, 83, "e"], [9, 365, 10, 83], [9, 375, 10, 83, "o"], [9, 376, 10, 83], [9, 377, 10, 83, "get"], [9, 380, 10, 83], [9, 381, 10, 83, "e"], [9, 382, 10, 83], [9, 385, 10, 83, "o"], [9, 386, 10, 83], [9, 387, 10, 83, "set"], [9, 390, 10, 83], [9, 391, 10, 83, "e"], [9, 392, 10, 83], [9, 394, 10, 83, "f"], [9, 395, 10, 83], [9, 411, 10, 83, "t"], [9, 412, 10, 83], [9, 416, 10, 83, "e"], [9, 417, 10, 83], [9, 433, 10, 83, "t"], [9, 434, 10, 83], [9, 441, 10, 83, "hasOwnProperty"], [9, 455, 10, 83], [9, 456, 10, 83, "call"], [9, 460, 10, 83], [9, 461, 10, 83, "e"], [9, 462, 10, 83], [9, 464, 10, 83, "t"], [9, 465, 10, 83], [9, 472, 10, 83, "i"], [9, 473, 10, 83], [9, 477, 10, 83, "o"], [9, 478, 10, 83], [9, 481, 10, 83, "Object"], [9, 487, 10, 83], [9, 488, 10, 83, "defineProperty"], [9, 502, 10, 83], [9, 507, 10, 83, "Object"], [9, 513, 10, 83], [9, 514, 10, 83, "getOwnPropertyDescriptor"], [9, 538, 10, 83], [9, 539, 10, 83, "e"], [9, 540, 10, 83], [9, 542, 10, 83, "t"], [9, 543, 10, 83], [9, 550, 10, 83, "i"], [9, 551, 10, 83], [9, 552, 10, 83, "get"], [9, 555, 10, 83], [9, 559, 10, 83, "i"], [9, 560, 10, 83], [9, 561, 10, 83, "set"], [9, 564, 10, 83], [9, 568, 10, 83, "o"], [9, 569, 10, 83], [9, 570, 10, 83, "f"], [9, 571, 10, 83], [9, 573, 10, 83, "t"], [9, 574, 10, 83], [9, 576, 10, 83, "i"], [9, 577, 10, 83], [9, 581, 10, 83, "f"], [9, 582, 10, 83], [9, 583, 10, 83, "t"], [9, 584, 10, 83], [9, 588, 10, 83, "e"], [9, 589, 10, 83], [9, 590, 10, 83, "t"], [9, 591, 10, 83], [9, 602, 10, 83, "f"], [9, 603, 10, 83], [9, 608, 10, 83, "e"], [9, 609, 10, 83], [9, 611, 10, 83, "t"], [9, 612, 10, 83], [10, 2, 1, 0], [11, 0, 2, 0], [12, 0, 3, 0], [13, 0, 4, 0], [14, 0, 5, 0], [15, 0, 6, 0], [17, 2, 12, 0], [17, 8, 12, 6, "createLucideIcon"], [17, 24, 12, 22], [17, 27, 12, 25, "createLucideIcon"], [17, 28, 12, 26, "iconName"], [17, 36, 12, 34], [17, 38, 12, 36, "iconNode"], [17, 46, 12, 44], [17, 51, 12, 49], [18, 4, 13, 2], [18, 10, 13, 8, "Component"], [18, 19, 13, 17], [18, 35, 13, 20], [18, 39, 13, 20, "forwardRef"], [18, 56, 13, 30], [18, 58, 14, 4], [18, 59, 14, 5], [19, 6, 15, 6, "color"], [19, 11, 15, 11], [19, 14, 15, 14], [19, 28, 15, 28], [20, 6, 16, 6, "size"], [20, 10, 16, 10], [20, 13, 16, 13], [20, 15, 16, 15], [21, 6, 17, 6, "strokeWidth"], [21, 17, 17, 17], [21, 20, 17, 20], [21, 21, 17, 21], [22, 6, 18, 6, "absoluteStrokeWidth"], [22, 25, 18, 25], [23, 6, 19, 6, "children"], [23, 14, 19, 14], [24, 6, 20, 6], [24, 19, 20, 19], [24, 21, 20, 21, "dataTestId"], [24, 31, 20, 31], [25, 6, 21, 6], [25, 9, 21, 9, "rest"], [26, 4, 22, 4], [26, 5, 22, 5], [26, 7, 22, 7, "ref"], [26, 10, 22, 10], [26, 15, 22, 15], [27, 6, 23, 6], [27, 12, 23, 12, "customAttrs"], [27, 23, 23, 23], [27, 26, 23, 26], [28, 8, 24, 8, "stroke"], [28, 14, 24, 14], [28, 16, 24, 16, "color"], [28, 21, 24, 21], [29, 8, 25, 8, "strokeWidth"], [29, 19, 25, 19], [29, 21, 25, 21, "absoluteStrokeWidth"], [29, 40, 25, 40], [29, 43, 25, 43, "Number"], [29, 49, 25, 49], [29, 50, 25, 50, "strokeWidth"], [29, 61, 25, 61], [29, 62, 25, 62], [29, 65, 25, 65], [29, 67, 25, 67], [29, 70, 25, 70, "Number"], [29, 76, 25, 76], [29, 77, 25, 77, "size"], [29, 81, 25, 81], [29, 82, 25, 82], [29, 85, 25, 85, "strokeWidth"], [29, 96, 25, 96], [30, 8, 26, 8], [30, 11, 26, 11, "rest"], [31, 6, 27, 6], [31, 7, 27, 7], [32, 6, 28, 6], [32, 26, 28, 13], [32, 30, 28, 13, "createElement"], [32, 50, 28, 26], [32, 52, 29, 8, "NativeSvg"], [32, 61, 29, 17], [32, 62, 29, 18, "Svg"], [32, 65, 29, 21], [32, 67, 30, 8], [33, 8, 31, 10, "ref"], [33, 11, 31, 13], [34, 8, 32, 10], [34, 11, 32, 13, "defaultAttributes"], [34, 37, 32, 30], [35, 8, 33, 10, "width"], [35, 13, 33, 15], [35, 15, 33, 17, "size"], [35, 19, 33, 21], [36, 8, 34, 10, "height"], [36, 14, 34, 16], [36, 16, 34, 18, "size"], [36, 20, 34, 22], [37, 8, 35, 10], [37, 21, 35, 23], [37, 23, 35, 25, "dataTestId"], [37, 33, 35, 35], [38, 8, 36, 10], [38, 11, 36, 13, "customAttrs"], [39, 6, 37, 8], [39, 7, 37, 9], [39, 9, 38, 8], [39, 10, 39, 10], [39, 13, 39, 13, "iconNode"], [39, 21, 39, 21], [39, 22, 39, 22, "map"], [39, 25, 39, 25], [39, 26, 39, 26], [39, 27, 39, 27], [39, 28, 39, 28, "tag"], [39, 31, 39, 31], [39, 33, 39, 33, "attrs"], [39, 38, 39, 38], [39, 39, 39, 39], [39, 44, 39, 44], [40, 8, 40, 12], [40, 14, 40, 18, "upperCasedTag"], [40, 27, 40, 31], [40, 30, 40, 34, "tag"], [40, 33, 40, 37], [40, 34, 40, 38, "char<PERSON>t"], [40, 40, 40, 44], [40, 41, 40, 45], [40, 42, 40, 46], [40, 43, 40, 47], [40, 44, 40, 48, "toUpperCase"], [40, 55, 40, 59], [40, 56, 40, 60], [40, 57, 40, 61], [40, 60, 40, 64, "tag"], [40, 63, 40, 67], [40, 64, 40, 68, "slice"], [40, 69, 40, 73], [40, 70, 40, 74], [40, 71, 40, 75], [40, 72, 40, 76], [41, 8, 41, 12], [41, 28, 41, 19], [41, 32, 41, 19, "createElement"], [41, 52, 41, 32], [41, 54, 42, 14, "NativeSvg"], [41, 63, 42, 23], [41, 64, 42, 24, "upperCasedTag"], [41, 77, 42, 37], [41, 78, 42, 38], [41, 80, 43, 14], [42, 10, 43, 16], [42, 13, 43, 19, "childDefaultAttributes"], [42, 54, 43, 41], [43, 10, 43, 43], [43, 13, 43, 46, "customAttrs"], [43, 24, 43, 57], [44, 10, 43, 59], [44, 13, 43, 62, "attrs"], [45, 8, 43, 68], [45, 9, 44, 12], [45, 10, 44, 13], [46, 6, 45, 10], [46, 7, 45, 11], [46, 8, 45, 12], [46, 10, 46, 10], [46, 14, 46, 13], [46, 15, 46, 14, "Array"], [46, 20, 46, 19], [46, 21, 46, 20, "isArray"], [46, 28, 46, 27], [46, 29, 46, 28, "children"], [46, 37, 46, 36], [46, 38, 46, 37], [46, 41, 46, 40, "children"], [46, 49, 46, 48], [46, 52, 46, 51], [46, 53, 46, 52, "children"], [46, 61, 46, 60], [46, 62, 46, 61], [46, 67, 46, 66], [46, 69, 46, 68], [46, 71, 48, 6], [46, 72, 48, 7], [47, 4, 49, 4], [47, 5, 50, 2], [47, 6, 50, 3], [48, 4, 51, 2, "Component"], [48, 13, 51, 11], [48, 14, 51, 12, "displayName"], [48, 25, 51, 23], [48, 28, 51, 26], [48, 31, 51, 29, "iconName"], [48, 39, 51, 37], [48, 41, 51, 39], [49, 4, 52, 2], [49, 11, 52, 9, "Component"], [49, 20, 52, 18], [50, 2, 53, 0], [50, 3, 53, 1], [51, 2, 53, 2, "exports"], [51, 9, 53, 2], [51, 10, 53, 2, "default"], [51, 17, 53, 2], [51, 20, 53, 2, "createLucideIcon"], [51, 36, 53, 2], [52, 0, 53, 2], [52, 3]], "functionMap": {"names": ["<global>", "createLucideIcon", "forwardRef$argument_0", "iconNode.map$argument_0"], "mappings": "AAA;yBCW;ICE;0BCyB;WDM;KDI;CDI"}}, "type": "js/module"}]}