{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/get", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7RhWyTq5i/X0UNOgMT1VkjxHPX0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "./PerformanceEntry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 52}}], "key": "brFNAt3Zh5rA+ZZUGgMallCwpmE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.TaskAttributionTiming = exports.PerformanceLongTaskTiming = void 0;\n  var _get2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/get\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classCallCheck\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/inherits\"));\n  var _PerformanceEntry3 = require(_dependencyMap[7], \"./PerformanceEntry\");\n  function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var TaskAttributionTiming = exports.TaskAttributionTiming = /*#__PURE__*/function (_PerformanceEntry) {\n    function TaskAttributionTiming() {\n      (0, _classCallCheck2.default)(this, TaskAttributionTiming);\n      return _callSuper(this, TaskAttributionTiming, arguments);\n    }\n    (0, _inherits2.default)(TaskAttributionTiming, _PerformanceEntry);\n    return (0, _createClass2.default)(TaskAttributionTiming);\n  }(_PerformanceEntry3.PerformanceEntry);\n  var EMPTY_ATTRIBUTION = Object.preventExtensions([]);\n  var PerformanceLongTaskTiming = exports.PerformanceLongTaskTiming = /*#__PURE__*/function (_PerformanceEntry2) {\n    function PerformanceLongTaskTiming() {\n      (0, _classCallCheck2.default)(this, PerformanceLongTaskTiming);\n      return _callSuper(this, PerformanceLongTaskTiming, arguments);\n    }\n    (0, _inherits2.default)(PerformanceLongTaskTiming, _PerformanceEntry2);\n    return (0, _createClass2.default)(PerformanceLongTaskTiming, [{\n      key: \"attribution\",\n      get: function () {\n        return EMPTY_ATTRIBUTION;\n      }\n    }, {\n      key: \"toJSON\",\n      value: function toJSON() {\n        return {\n          ..._superPropGet(PerformanceLongTaskTiming, \"toJSON\", this, 3)([]),\n          attribution: this.attribution\n        };\n      }\n    }]);\n  }(_PerformanceEntry3.PerformanceEntry);\n});", "lineCount": 47, "map": [[13, 2, 15, 0], [13, 6, 15, 0, "_PerformanceEntry3"], [13, 24, 15, 0], [13, 27, 15, 0, "require"], [13, 34, 15, 0], [13, 35, 15, 0, "_dependencyMap"], [13, 49, 15, 0], [14, 2, 15, 52], [14, 11, 15, 52, "_superPropGet"], [14, 25, 15, 52, "t"], [14, 26, 15, 52], [14, 28, 15, 52, "o"], [14, 29, 15, 52], [14, 31, 15, 52, "e"], [14, 32, 15, 52], [14, 34, 15, 52, "r"], [14, 35, 15, 52], [14, 43, 15, 52, "p"], [14, 44, 15, 52], [14, 51, 15, 52, "_get2"], [14, 56, 15, 52], [14, 57, 15, 52, "default"], [14, 64, 15, 52], [14, 70, 15, 52, "_getPrototypeOf2"], [14, 86, 15, 52], [14, 87, 15, 52, "default"], [14, 94, 15, 52], [14, 100, 15, 52, "r"], [14, 101, 15, 52], [14, 104, 15, 52, "t"], [14, 105, 15, 52], [14, 106, 15, 52, "prototype"], [14, 115, 15, 52], [14, 118, 15, 52, "t"], [14, 119, 15, 52], [14, 122, 15, 52, "o"], [14, 123, 15, 52], [14, 125, 15, 52, "e"], [14, 126, 15, 52], [14, 140, 15, 52, "r"], [14, 141, 15, 52], [14, 166, 15, 52, "p"], [14, 167, 15, 52], [14, 180, 15, 52, "t"], [14, 181, 15, 52], [14, 192, 15, 52, "p"], [14, 193, 15, 52], [14, 194, 15, 52, "apply"], [14, 199, 15, 52], [14, 200, 15, 52, "e"], [14, 201, 15, 52], [14, 203, 15, 52, "t"], [14, 204, 15, 52], [14, 211, 15, 52, "p"], [14, 212, 15, 52], [15, 2, 15, 52], [15, 11, 15, 52, "_callSuper"], [15, 22, 15, 52, "t"], [15, 23, 15, 52], [15, 25, 15, 52, "o"], [15, 26, 15, 52], [15, 28, 15, 52, "e"], [15, 29, 15, 52], [15, 40, 15, 52, "o"], [15, 41, 15, 52], [15, 48, 15, 52, "_getPrototypeOf2"], [15, 64, 15, 52], [15, 65, 15, 52, "default"], [15, 72, 15, 52], [15, 74, 15, 52, "o"], [15, 75, 15, 52], [15, 82, 15, 52, "_possibleConstructorReturn2"], [15, 109, 15, 52], [15, 110, 15, 52, "default"], [15, 117, 15, 52], [15, 119, 15, 52, "t"], [15, 120, 15, 52], [15, 122, 15, 52, "_isNativeReflectConstruct"], [15, 147, 15, 52], [15, 152, 15, 52, "Reflect"], [15, 159, 15, 52], [15, 160, 15, 52, "construct"], [15, 169, 15, 52], [15, 170, 15, 52, "o"], [15, 171, 15, 52], [15, 173, 15, 52, "e"], [15, 174, 15, 52], [15, 186, 15, 52, "_getPrototypeOf2"], [15, 202, 15, 52], [15, 203, 15, 52, "default"], [15, 210, 15, 52], [15, 212, 15, 52, "t"], [15, 213, 15, 52], [15, 215, 15, 52, "constructor"], [15, 226, 15, 52], [15, 230, 15, 52, "o"], [15, 231, 15, 52], [15, 232, 15, 52, "apply"], [15, 237, 15, 52], [15, 238, 15, 52, "t"], [15, 239, 15, 52], [15, 241, 15, 52, "e"], [15, 242, 15, 52], [16, 2, 15, 52], [16, 11, 15, 52, "_isNativeReflectConstruct"], [16, 37, 15, 52], [16, 51, 15, 52, "t"], [16, 52, 15, 52], [16, 56, 15, 52, "Boolean"], [16, 63, 15, 52], [16, 64, 15, 52, "prototype"], [16, 73, 15, 52], [16, 74, 15, 52, "valueOf"], [16, 81, 15, 52], [16, 82, 15, 52, "call"], [16, 86, 15, 52], [16, 87, 15, 52, "Reflect"], [16, 94, 15, 52], [16, 95, 15, 52, "construct"], [16, 104, 15, 52], [16, 105, 15, 52, "Boolean"], [16, 112, 15, 52], [16, 145, 15, 52, "t"], [16, 146, 15, 52], [16, 159, 15, 52, "_isNativeReflectConstruct"], [16, 184, 15, 52], [16, 196, 15, 52, "_isNativeReflectConstruct"], [16, 197, 15, 52], [16, 210, 15, 52, "t"], [16, 211, 15, 52], [17, 2, 15, 52], [17, 6, 23, 13, "TaskAttributionTiming"], [17, 27, 23, 34], [17, 30, 23, 34, "exports"], [17, 37, 23, 34], [17, 38, 23, 34, "TaskAttributionTiming"], [17, 59, 23, 34], [17, 85, 23, 34, "_PerformanceEntry"], [17, 102, 23, 34], [18, 4, 23, 34], [18, 13, 23, 34, "TaskAttributionTiming"], [18, 35, 23, 34], [19, 6, 23, 34], [19, 10, 23, 34, "_classCallCheck2"], [19, 26, 23, 34], [19, 27, 23, 34, "default"], [19, 34, 23, 34], [19, 42, 23, 34, "TaskAttributionTiming"], [19, 63, 23, 34], [20, 6, 23, 34], [20, 13, 23, 34, "_callSuper"], [20, 23, 23, 34], [20, 30, 23, 34, "TaskAttributionTiming"], [20, 51, 23, 34], [20, 53, 23, 34, "arguments"], [20, 62, 23, 34], [21, 4, 23, 34], [22, 4, 23, 34], [22, 8, 23, 34, "_inherits2"], [22, 18, 23, 34], [22, 19, 23, 34, "default"], [22, 26, 23, 34], [22, 28, 23, 34, "TaskAttributionTiming"], [22, 49, 23, 34], [22, 51, 23, 34, "_PerformanceEntry"], [22, 68, 23, 34], [23, 4, 23, 34], [23, 15, 23, 34, "_createClass2"], [23, 28, 23, 34], [23, 29, 23, 34, "default"], [23, 36, 23, 34], [23, 38, 23, 34, "TaskAttributionTiming"], [23, 59, 23, 34], [24, 2, 23, 34], [24, 4, 23, 43, "PerformanceEntry"], [24, 39, 23, 59], [25, 2, 25, 0], [25, 6, 25, 6, "EMPTY_ATTRIBUTION"], [25, 23, 25, 62], [25, 26, 26, 2, "Object"], [25, 32, 26, 8], [25, 33, 26, 9, "preventExtensions"], [25, 50, 26, 26], [25, 51, 26, 27], [25, 53, 26, 29], [25, 54, 26, 30], [26, 2, 26, 31], [26, 6, 28, 13, "PerformanceLongTaskTiming"], [26, 31, 28, 38], [26, 34, 28, 38, "exports"], [26, 41, 28, 38], [26, 42, 28, 38, "PerformanceLongTaskTiming"], [26, 67, 28, 38], [26, 93, 28, 38, "_PerformanceEntry2"], [26, 111, 28, 38], [27, 4, 28, 38], [27, 13, 28, 38, "PerformanceLongTaskTiming"], [27, 39, 28, 38], [28, 6, 28, 38], [28, 10, 28, 38, "_classCallCheck2"], [28, 26, 28, 38], [28, 27, 28, 38, "default"], [28, 34, 28, 38], [28, 42, 28, 38, "PerformanceLongTaskTiming"], [28, 67, 28, 38], [29, 6, 28, 38], [29, 13, 28, 38, "_callSuper"], [29, 23, 28, 38], [29, 30, 28, 38, "PerformanceLongTaskTiming"], [29, 55, 28, 38], [29, 57, 28, 38, "arguments"], [29, 66, 28, 38], [30, 4, 28, 38], [31, 4, 28, 38], [31, 8, 28, 38, "_inherits2"], [31, 18, 28, 38], [31, 19, 28, 38, "default"], [31, 26, 28, 38], [31, 28, 28, 38, "PerformanceLongTaskTiming"], [31, 53, 28, 38], [31, 55, 28, 38, "_PerformanceEntry2"], [31, 73, 28, 38], [32, 4, 28, 38], [32, 15, 28, 38, "_createClass2"], [32, 28, 28, 38], [32, 29, 28, 38, "default"], [32, 36, 28, 38], [32, 38, 28, 38, "PerformanceLongTaskTiming"], [32, 63, 28, 38], [33, 6, 28, 38, "key"], [33, 9, 28, 38], [34, 6, 28, 38, "get"], [34, 9, 28, 38], [34, 11, 29, 2], [34, 20, 29, 2, "get"], [34, 21, 29, 2], [34, 23, 29, 59], [35, 8, 30, 4], [35, 15, 30, 11, "EMPTY_ATTRIBUTION"], [35, 32, 30, 28], [36, 6, 31, 2], [37, 4, 31, 3], [38, 6, 31, 3, "key"], [38, 9, 31, 3], [39, 6, 31, 3, "value"], [39, 11, 31, 3], [39, 13, 33, 2], [39, 22, 33, 2, "toJSON"], [39, 28, 33, 8, "toJSON"], [39, 29, 33, 8], [39, 31, 33, 42], [40, 8, 34, 4], [40, 15, 34, 11], [41, 10, 35, 6], [41, 13, 35, 6, "_superPropGet"], [41, 26, 35, 6], [41, 27, 35, 6, "PerformanceLongTaskTiming"], [41, 52, 35, 6], [41, 76, 35, 23], [42, 10, 36, 6, "attribution"], [42, 21, 36, 17], [42, 23, 36, 19], [42, 27, 36, 23], [42, 28, 36, 24, "attribution"], [43, 8, 37, 4], [43, 9, 37, 5], [44, 6, 38, 2], [45, 4, 38, 3], [46, 2, 38, 3], [46, 4, 28, 47, "PerformanceEntry"], [46, 39, 28, 63], [47, 0, 28, 63], [47, 3]], "functionMap": {"names": ["<global>", "TaskAttributionTiming", "PerformanceLongTaskTiming", "PerformanceLongTaskTiming#get__attribution", "PerformanceLongTaskTiming#toJSON"], "mappings": "AAA;OCsB,uDD;OEK;ECC;GDE;EEE;GFK"}}, "type": "js/module"}]}